{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { InstagramStoriesComponent } from '../instagram-stories/instagram-stories.component';\nimport * as i0 from \"@angular/core\";\nexport class StoriesComponent {\n  constructor() {}\n  ngOnInit() {\n    // Simple wrapper component for Instagram stories\n  }\n  static {\n    this.ɵfac = function StoriesComponent_Factory(t) {\n      return new (t || StoriesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesComponent,\n      selectors: [[\"app-stories\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      template: function StoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-instagram-stories\");\n        }\n      },\n      dependencies: [CommonModule, InstagramStoriesComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9jb21wb25lbnRzL3N0b3JpZXMvc3Rvcmllcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNFLGNBQUE7RUFDQSxXQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTaW1wbGUgd3JhcHBlciBmb3IgSW5zdGFncmFtIFN0b3JpZXNcbjpob3N0IHtcbiAgZGlzcGxheTogYmxvY2s7XG4gIHdpZHRoOiAxMDAlO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "InstagramStoriesComponent", "StoriesComponent", "constructor", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "StoriesComponent_Template", "rf", "ctx", "ɵɵelement", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { InstagramStoriesComponent } from '../instagram-stories/instagram-stories.component';\n\n@Component({\n  selector: 'app-stories',\n  standalone: true,\n  imports: [CommonModule, InstagramStoriesComponent],\n  templateUrl: './stories.component.html',\n  styleUrls: ['./stories.component.scss']\n})\nexport class StoriesComponent implements OnInit {\n\n  constructor() {}\n\n  ngOnInit() {\n    // Simple wrapper component for Instagram stories\n  }\n}\n", "<!-- Instagram-style Stories -->\n<app-instagram-stories></app-instagram-stories>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,yBAAyB,QAAQ,kDAAkD;;AAS5F,OAAM,MAAOC,gBAAgB;EAE3BC,YAAA,GAAe;EAEfC,QAAQA,CAAA;IACN;EAAA;;;uBALSF,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX7BN,EAAA,CAAAQ,SAAA,4BAA+C;;;qBDOnChB,YAAY,EAAEC,yBAAyB;MAAAgB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}