import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, of, forkJoin } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface Brand {
  _id: string;
  name: string;
  logo: string;
  description: string;
  slug: string;
  isVerified: boolean;
  productCount: number;
  rating: number;
  establishedYear?: number;
  website?: string;
  categories: string[];
  featured: boolean;
  trending: boolean;
}

export interface TrendingProduct {
  _id: string;
  name: string;
  description: string;
  images: { url: string; alt: string; isPrimary: boolean }[];
  pricing: {
    mrp: number;
    sellingPrice: number;
    discountPercentage: number;
  };
  brand: string;
  category: string;
  subcategory: string;
  rating: { average: number; count: number };
  analytics: { views: number; likes: number; shares: number; purchases: number };
  isTrending: boolean;
  trendingScore: number;
  availability: { status: string; totalStock: number };
}

export interface NewArrival {
  _id: string;
  name: string;
  description: string;
  images: { url: string; alt: string; isPrimary: boolean }[];
  pricing: {
    mrp: number;
    sellingPrice: number;
    discountPercentage: number;
  };
  brand: string;
  category: string;
  subcategory: string;
  rating: { average: number; count: number };
  createdAt: string;
  isNew: boolean;
  availability: { status: string; totalStock: number };
}

export interface QuickLink {
  id: string;
  title: string;
  description: string;
  icon: string;
  route: string;
  color: string;
  category?: string;
  subcategory?: string;
  featured: boolean;
  order: number;
}

export interface ShopStats {
  totalProducts: number;
  totalBrands: number;
  totalCategories: number;
  totalUsers: number;
  newArrivalsCount: number;
  trendingCount: number;
}

@Injectable({
  providedIn: 'root'
})
export class ShopDataService {
  private readonly API_URL = environment.apiUrl;
  
  // Cache subjects for performance
  private featuredBrandsSubject = new BehaviorSubject<Brand[]>([]);
  private trendingProductsSubject = new BehaviorSubject<TrendingProduct[]>([]);
  private newArrivalsSubject = new BehaviorSubject<NewArrival[]>([]);
  private quickLinksSubject = new BehaviorSubject<QuickLink[]>([]);
  private shopStatsSubject = new BehaviorSubject<ShopStats | null>(null);
  
  // Public observables
  public featuredBrands$ = this.featuredBrandsSubject.asObservable();
  public trendingProducts$ = this.trendingProductsSubject.asObservable();
  public newArrivals$ = this.newArrivalsSubject.asObservable();
  public quickLinks$ = this.quickLinksSubject.asObservable();
  public shopStats$ = this.shopStatsSubject.asObservable();

  constructor(private http: HttpClient) {
    this.initializeQuickLinks();
  }

  /**
   * Load all shop data in parallel
   */
  loadAllShopData(): Observable<any> {
    return forkJoin({
      brands: this.loadFeaturedBrands(),
      trending: this.loadTrendingProducts(),
      newArrivals: this.loadNewArrivals(),
      stats: this.loadShopStats()
    });
  }

  /**
   * Load featured brands
   */
  loadFeaturedBrands(): Observable<Brand[]> {
    return this.http.get<any>(`${this.API_URL}/brands/featured`).pipe(
      map(response => {
        const brands = response.success ? response.data : this.getMockFeaturedBrands();
        this.featuredBrandsSubject.next(brands);
        return brands;
      }),
      catchError(() => {
        const mockBrands = this.getMockFeaturedBrands();
        this.featuredBrandsSubject.next(mockBrands);
        return of(mockBrands);
      })
    );
  }

  /**
   * Load trending products
   */
  loadTrendingProducts(limit: number = 12): Observable<TrendingProduct[]> {
    const params = new HttpParams().set('limit', limit.toString());

    return this.http.get<any>(`${this.API_URL}/products/trending`, { params }).pipe(
      map(response => {
        const products = response.success ? response.data : this.getMockTrendingProducts();
        this.trendingProductsSubject.next(products);
        return products;
      }),
      catchError(() => {
        const mockProducts = this.getMockTrendingProducts();
        this.trendingProductsSubject.next(mockProducts);
        return of(mockProducts);
      })
    );
  }

  /**
   * Load new arrivals
   */
  loadNewArrivals(limit: number = 12): Observable<NewArrival[]> {
    const params = new HttpParams()
      .set('limit', limit.toString())
      .set('sortBy', 'newest');

    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(
      map(response => {
        const products = response.success ? response.data : this.getMockNewArrivals();
        this.newArrivalsSubject.next(products);
        return products;
      }),
      catchError(() => {
        const mockProducts = this.getMockNewArrivals();
        this.newArrivalsSubject.next(mockProducts);
        return of(mockProducts);
      })
    );
  }

  /**
   * Load shop statistics
   */
  loadShopStats(): Observable<ShopStats> {
    return this.http.get<any>(`${this.API_URL}/shop/stats`).pipe(
      map(response => {
        const stats = response.success ? response.stats : this.getMockShopStats();
        this.shopStatsSubject.next(stats);
        return stats;
      }),
      catchError(() => {
        const mockStats = this.getMockShopStats();
        this.shopStatsSubject.next(mockStats);
        return of(mockStats);
      })
    );
  }

  /**
   * Get products by brand
   */
  getProductsByBrand(brandSlug: string, limit: number = 20): Observable<any[]> {
    const params = new HttpParams().set('brand', brandSlug).set('limit', limit.toString());
    
    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(
      map(response => response.success ? response.products : []),
      catchError(() => of([]))
    );
  }

  /**
   * Get products by category
   */
  getProductsByCategory(category: string, subcategory?: string, limit: number = 20): Observable<any[]> {
    let params = new HttpParams().set('category', category).set('limit', limit.toString());
    if (subcategory) {
      params = params.set('subcategory', subcategory);
    }
    
    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(
      map(response => response.success ? response.products : []),
      catchError(() => of([]))
    );
  }

  /**
   * Search products
   */
  searchProducts(query: string, filters?: any): Observable<any> {
    let params = new HttpParams().set('q', query);
    
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          params = params.set(key, filters[key]);
        }
      });
    }
    
    return this.http.get<any>(`${this.API_URL}/search/products`, { params }).pipe(
      map(response => response.success ? response : { products: [], totalCount: 0 }),
      catchError(() => of({ products: [], totalCount: 0 }))
    );
  }

  /**
   * Initialize quick links
   */
  private initializeQuickLinks(): void {
    const quickLinks: QuickLink[] = [
      {
        id: 'men-clothing',
        title: 'Men\'s Clothing',
        description: 'Shirts, T-shirts, Jeans & More',
        icon: 'fas fa-tshirt',
        route: '/shop/category/men',
        color: '#3498db',
        category: 'men',
        featured: true,
        order: 1
      },
      {
        id: 'women-clothing',
        title: 'Women\'s Clothing',
        description: 'Dresses, Tops, Kurtis & More',
        icon: 'fas fa-female',
        route: '/shop/category/women',
        color: '#e91e63',
        category: 'women',
        featured: true,
        order: 2
      },
      {
        id: 'footwear',
        title: 'Footwear',
        description: 'Shoes, Sandals, Sneakers',
        icon: 'fas fa-shoe-prints',
        route: '/shop/category/footwear',
        color: '#ff9800',
        category: 'footwear',
        featured: true,
        order: 3
      },
      {
        id: 'accessories',
        title: 'Accessories',
        description: 'Bags, Watches, Jewelry',
        icon: 'fas fa-gem',
        route: '/shop/category/accessories',
        color: '#9c27b0',
        category: 'accessories',
        featured: true,
        order: 4
      },
      {
        id: 'ethnic-wear',
        title: 'Ethnic Wear',
        description: 'Kurtas, Sarees, Traditional',
        icon: 'fas fa-star-and-crescent',
        route: '/shop/category/ethnic',
        color: '#ff5722',
        category: 'ethnic',
        featured: true,
        order: 5
      },
      {
        id: 'sports-fitness',
        title: 'Sports & Fitness',
        description: 'Activewear, Gym Gear',
        icon: 'fas fa-dumbbell',
        route: '/shop/category/sports',
        color: '#4caf50',
        category: 'sports',
        featured: true,
        order: 6
      },
      {
        id: 'sale',
        title: 'Sale & Offers',
        description: 'Up to 70% Off',
        icon: 'fas fa-tags',
        route: '/shop/sale',
        color: '#f44336',
        featured: true,
        order: 7
      },
      {
        id: 'new-arrivals',
        title: 'New Arrivals',
        description: 'Latest Fashion Trends',
        icon: 'fas fa-sparkles',
        route: '/shop/new-arrivals',
        color: '#00bcd4',
        featured: true,
        order: 8
      }
    ];
    
    this.quickLinksSubject.next(quickLinks);
  }

  /**
   * Get current cached data
   */
  getCurrentFeaturedBrands(): Brand[] {
    return this.featuredBrandsSubject.value;
  }

  getCurrentTrendingProducts(): TrendingProduct[] {
    return this.trendingProductsSubject.value;
  }

  getCurrentNewArrivals(): NewArrival[] {
    return this.newArrivalsSubject.value;
  }

  getCurrentQuickLinks(): QuickLink[] {
    return this.quickLinksSubject.value;
  }

  getCurrentShopStats(): ShopStats | null {
    return this.shopStatsSubject.value;
  }

  /**
   * Mock data methods for fallback
   */
  private getMockFeaturedBrands(): Brand[] {
    return [
      {
        _id: '1',
        name: 'Nike',
        logo: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200',
        description: 'Just Do It',
        slug: 'nike',
        isVerified: true,
        productCount: 245,
        rating: 4.8,
        establishedYear: 1964,
        website: 'https://nike.com',
        categories: ['footwear', 'sportswear'],
        featured: true,
        trending: true
      },
      {
        _id: '2',
        name: 'Adidas',
        logo: 'https://images.unsplash.com/photo-1556906781-9a412961c28c?w=200',
        description: 'Impossible is Nothing',
        slug: 'adidas',
        isVerified: true,
        productCount: 189,
        rating: 4.7,
        establishedYear: 1949,
        categories: ['footwear', 'sportswear'],
        featured: true,
        trending: true
      },
      {
        _id: '3',
        name: 'Zara',
        logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200',
        description: 'Fashion Forward',
        slug: 'zara',
        isVerified: true,
        productCount: 567,
        rating: 4.6,
        categories: ['men', 'women', 'accessories'],
        featured: true,
        trending: false
      }
    ];
  }

  private getMockTrendingProducts(): TrendingProduct[] {
    return [
      {
        _id: '1',
        name: 'Premium Cotton T-Shirt',
        description: 'Comfortable cotton t-shirt perfect for daily wear',
        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', alt: 'Cotton T-Shirt', isPrimary: true }],
        pricing: { mrp: 999, sellingPrice: 699, discountPercentage: 30 },
        brand: 'Nike',
        category: 'men',
        subcategory: 'tshirts',
        rating: { average: 4.5, count: 128 },
        analytics: { views: 1250, likes: 89, shares: 23, purchases: 67 },
        isTrending: true,
        trendingScore: 95,
        availability: { status: 'in-stock', totalStock: 45 }
      }
    ];
  }

  private getMockNewArrivals(): NewArrival[] {
    return [
      {
        _id: '1',
        name: 'Designer Kurta Set',
        description: 'Elegant kurta set for special occasions',
        images: [{ url: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=400', alt: 'Kurta Set', isPrimary: true }],
        pricing: { mrp: 2999, sellingPrice: 2299, discountPercentage: 23 },
        brand: 'Ethnic Wear Co',
        category: 'men',
        subcategory: 'kurtas',
        rating: { average: 4.7, count: 45 },
        createdAt: new Date().toISOString(),
        isNew: true,
        availability: { status: 'in-stock', totalStock: 23 }
      }
    ];
  }

  private getMockShopStats(): ShopStats {
    return {
      totalProducts: 12547,
      totalBrands: 456,
      totalCategories: 28,
      totalUsers: 89234,
      newArrivalsCount: 234,
      trendingCount: 156
    };
  }
}
