<nav class="mobile-bottom-nav" *ngIf="shouldShowBottomNav()">
  <div class="nav-container">
    <button 
      *ngFor="let item of getVisibleNavItems(); trackBy: trackByRoute"
      class="nav-item"
      [class.active]="isActive(item.route)"
      (click)="navigateTo(item.route)"
      [attr.aria-label]="item.label"
    >
      <!-- Icon -->
      <div class="nav-icon">
        <i [class]="isActive(item.route) ? item.activeIcon : item.icon"></i>
        
        <!-- Badge for notifications/cart count -->
        <span class="nav-badge" *ngIf="item.badge && item.badge > 0">
          {{ item.badge > 99 ? '99+' : item.badge }}
        </span>
        
        <!-- Special handling for profile with user avatar -->
        <img 
          *ngIf="item.route === '/profile' && isAuthenticated && currentUser?.avatar"
          [src]="getUserAvatar()"
          [alt]="currentUser?.fullName || 'Profile'"
          class="profile-avatar"
          [class.active]="isActive(item.route)"
        >
      </div>
      
      <!-- Label -->
      <span class="nav-label" [class.active]="isActive(item.route)">
        {{ item.label }}
      </span>
      
      <!-- Active indicator -->
      <div class="active-indicator" *ngIf="isActive(item.route)"></div>
    </button>
  </div>
  
  <!-- Floating Action Button for Create Content -->
  <button 
    class="fab-create"
    (click)="showCreateMenu()"
    [class.expanded]="showCreateOptions"
    *ngIf="isAuthenticated"
    aria-label="Create content"
  >
    <i class="fas fa-plus" [class.rotated]="showCreateOptions"></i>
  </button>
  
  <!-- Create Options Menu -->
  <div class="create-menu" [class.visible]="showCreateOptions" *ngIf="isAuthenticated">
    <div class="create-backdrop" (click)="hideCreateMenu()"></div>
    <div class="create-options">
      <button class="create-option" (click)="createStory()">
        <i class="fas fa-camera"></i>
        <span>Story</span>
      </button>
      <button class="create-option" (click)="createPost()">
        <i class="fas fa-image"></i>
        <span>Post</span>
      </button>
      <button class="create-option" (click)="createReel()">
        <i class="fas fa-video"></i>
        <span>Reel</span>
      </button>
      <button class="create-option" (click)="addProduct()">
        <i class="fas fa-tag"></i>
        <span>Product</span>
      </button>
    </div>
  </div>
</nav>
