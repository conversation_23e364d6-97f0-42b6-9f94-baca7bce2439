{"ast": null, "code": "import { StoryViewerComponent } from './story-viewer/story-viewer.component';\nexport const storyRoutes = [{\n  path: 'story/:userId/:storyIndex',\n  component: StoryViewerComponent,\n  title: 'Story Viewer'\n}];", "map": {"version": 3, "names": ["StoryViewerComponent", "storyRoutes", "path", "component", "title"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\story\\story.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { StoryViewerComponent } from './story-viewer/story-viewer.component';\n\nexport const storyRoutes: Routes = [\n  {\n    path: 'story/:userId/:storyIndex',\n    component: StoryViewerComponent,\n    title: 'Story Viewer'\n  }\n];\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,uCAAuC;AAE5E,OAAO,MAAMC,WAAW,GAAW,CACjC;EACEC,IAAI,EAAE,2BAA2B;EACjCC,SAAS,EAAEH,oBAAoB;EAC/BI,KAAK,EAAE;CACR,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}