.profile-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  background: #f8f9fa;
  min-height: 100vh;

  // Profile Navigation
  .profile-navigation {
    background: white;
    border-radius: 16px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 2rem;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
      }

      .nav-tabs {
        display: flex;
        gap: 0.5rem;
        overflow-x: auto;
        padding-bottom: 0.5rem;

        &::-webkit-scrollbar {
          height: 4px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 2px;
        }

        .nav-tab {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          border: none;
          background: #f8f9fa;
          color: #6c757d;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          white-space: nowrap;
          font-weight: 500;

          &:hover {
            background: #e9ecef;
            color: #495057;
          }

          &.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }

          i {
            font-size: 0.9rem;
          }

          @media (max-width: 768px) {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
          }
        }
      }

      .role-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border-radius: 20px;
        color: white;
        font-weight: 600;
        font-size: 0.85rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        i {
          font-size: 0.9rem;
        }

        @media (max-width: 768px) {
          padding: 0.5rem 0.75rem;
          font-size: 0.8rem;
        }
      }
    }
  }

  // Profile Content
  .profile-content {
    position: relative;

    .tab-content {
      display: none;

      &.active {
        display: block;
      }
    }
  }

  // Team Management Styles
  .team-management {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    .team-header {
      margin-bottom: 2rem;
      text-align: center;

      h2 {
        margin: 0 0 0.5rem 0;
        color: #262626;
        font-size: 2rem;
        font-weight: 700;
      }

      p {
        margin: 0;
        color: #8e8e8e;
        font-size: 1rem;
      }
    }

    .team-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      .stat-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .stat-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.2rem;
        }

        .stat-content {
          .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #262626;
            line-height: 1;
          }

          .stat-label {
            color: #8e8e8e;
            font-size: 0.85rem;
            margin-top: 0.25rem;
          }
        }
      }
    }

    .team-actions {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 2rem;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;

        &.btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
          }
        }

        &.btn-secondary {
          background: #f8f9fa;
          color: #6c757d;
          border: 1px solid #dee2e6;

          &:hover {
            background: #e9ecef;
            color: #495057;
          }
        }

        i {
          font-size: 0.9rem;
        }
      }
    }
  }

  // Analytics Dashboard Styles
  .analytics-dashboard {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    .analytics-header {
      margin-bottom: 2rem;
      text-align: center;

      h2 {
        margin: 0 0 0.5rem 0;
        color: #262626;
        font-size: 2rem;
        font-weight: 700;
      }

      p {
        margin: 0;
        color: #8e8e8e;
        font-size: 1rem;
      }
    }

    .analytics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;

      .analytics-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;

        h3 {
          margin: 0 0 1rem 0;
          color: #262626;
          font-size: 1.1rem;
          font-weight: 600;
        }

        .chart-placeholder {
          height: 200px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: white;
          border-radius: 8px;
          color: #6c757d;

          i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
          }

          p {
            margin: 0;
            font-size: 0.9rem;
          }
        }

        .metrics-list {
          .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;

            &:last-child {
              border-bottom: none;
            }

            .metric-label {
              color: #6c757d;
              font-size: 0.9rem;
            }

            .metric-value {
              font-weight: 600;
              font-size: 1rem;
            }
          }
        }

        .activities-list {
          .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;

            &:last-child {
              border-bottom: none;
            }

            .activity-icon {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 0.9rem;
            }

            .activity-content {
              flex: 1;

              .activity-title {
                font-weight: 600;
                color: #262626;
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
              }

              .activity-time {
                color: #8e8e8e;
                font-size: 0.8rem;
              }
            }
          }
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .profile-page {
    background: #121212;

    .profile-navigation,
    .team-management,
    .analytics-dashboard {
      background: #1e1e1e;
      color: #ffffff;
    }

    .team-stats .stat-card,
    .analytics-grid .analytics-card {
      background: #2a2a2a;
    }

    .team-header h2,
    .analytics-header h2,
    .analytics-card h3,
    .stat-content .stat-value,
    .activity-content .activity-title {
      color: #ffffff;
    }

    .team-header p,
    .analytics-header p,
    .stat-content .stat-label,
    .metric-item .metric-label,
    .activity-content .activity-time {
      color: #b3b3b3;
    }

    .chart-placeholder {
      background: #333 !important;
      color: #b3b3b3;
    }

    .metric-item,
    .activity-item {
      border-bottom-color: #333;
    }
  }
}
