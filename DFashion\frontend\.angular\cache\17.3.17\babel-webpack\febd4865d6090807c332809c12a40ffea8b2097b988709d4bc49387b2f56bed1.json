{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction CategoryComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"label\");\n    i0.ɵɵtext(2, \"Size:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryComponent_div_44_Template_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedSize, $event) || (ctx_r1.selectedSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CategoryComponent_div_44_Template_select_change_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelementStart(4, \"option\", 16);\n    i0.ɵɵtext(5, \"All Sizes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 27);\n    i0.ɵɵtext(7, \"XS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 28);\n    i0.ɵɵtext(9, \"S\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 29);\n    i0.ɵɵtext(11, \"M\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 30);\n    i0.ɵɵtext(13, \"L\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 31);\n    i0.ɵɵtext(15, \"XL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 32);\n    i0.ɵɵtext(17, \"XXL\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedSize);\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r4), \"% OFF \");\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_14_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 59);\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_14_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 60);\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtemplate(2, CategoryComponent_div_47_div_1_div_14_i_2_Template, 1, 0, \"i\", 56)(3, CategoryComponent_div_47_div_1_div_14_i_3_Template, 1, 0, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 58);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStars(product_r4.rating.average));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getEmptyStars(product_r4.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r4.rating.count, \")\");\n  }\n}\nfunction CategoryComponent_div_47_div_1_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r4.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction CategoryComponent_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_div_click_0_listener() {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelement(2, \"img\", 37);\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_button_click_4_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r4, $event));\n    });\n    i0.ɵɵelement(5, \"i\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_button_click_6_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.quickView(product_r4, $event));\n    });\n    i0.ɵɵelement(7, \"i\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, CategoryComponent_div_47_div_1_div_8_Template, 2, 1, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 44)(10, \"h3\", 45);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 46);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CategoryComponent_div_47_div_1_div_14_Template, 6, 3, \"div\", 47);\n    i0.ɵɵelementStart(15, \"div\", 48)(16, \"span\", 49);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CategoryComponent_div_47_div_1_span_19_Template, 3, 4, \"span\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_button_click_20_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r4, $event));\n    });\n    i0.ɵɵelement(21, \"i\", 52);\n    i0.ɵɵtext(22, \" Add to Cart \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r4), i0.ɵɵsanitizeUrl)(\"alt\", product_r4.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice && product_r4.originalPrice > product_r4.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(18, 8, product_r4.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice && product_r4.originalPrice > product_r4.price);\n  }\n}\nfunction CategoryComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, CategoryComponent_div_47_div_1_Template, 23, 11, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredProducts);\n  }\n}\nfunction CategoryComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"No products found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your filters or browse other categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_48_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFilters());\n    });\n    i0.ɵɵtext(8, \"Clear Filters\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CategoryComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵelement(1, \"div\", 67);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading products...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CategoryComponent {\n  constructor(route, router) {\n    this.route = route;\n    this.router = router;\n    this.category = '';\n    this.products = [];\n    this.filteredProducts = [];\n    this.loading = true;\n    // Filters\n    this.sortBy = 'featured';\n    this.priceRange = '';\n    this.selectedSize = '';\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      this.category = params['category'];\n      this.loadProducts();\n    });\n  }\n  loadProducts() {\n    this.loading = true;\n    // Load from real API\n    this.products = [];\n    this.filteredProducts = [];\n    this.loading = false;\n  }\n  getCategoryDisplayName() {\n    const categoryNames = {\n      'women': 'Women\\'s Fashion',\n      'men': 'Men\\'s Fashion',\n      'kids': 'Kids\\' Fashion',\n      'ethnic': 'Ethnic Wear',\n      'all': 'All Products'\n    };\n    return categoryNames[this.category] || this.category.charAt(0).toUpperCase() + this.category.slice(1);\n  }\n  getCategoryDescription() {\n    const descriptions = {\n      'women': 'Discover the latest trends in women\\'s fashion',\n      'men': 'Explore stylish and comfortable men\\'s clothing',\n      'kids': 'Fun and comfortable clothing for children',\n      'ethnic': 'Traditional and ethnic wear for special occasions',\n      'all': 'Browse our complete collection of fashion items'\n    };\n    return descriptions[this.category] || 'Explore our collection';\n  }\n  onSortChange() {\n    this.applyFilters();\n  }\n  onFilterChange() {\n    this.applyFilters();\n  }\n  applyFilters() {\n    let filtered = [...this.products];\n    // Apply price filter\n    if (this.priceRange) {\n      if (this.priceRange === '10000+') {\n        filtered = filtered.filter(p => p.price >= 10000);\n      } else {\n        const [min, max] = this.priceRange.split('-').map(Number);\n        filtered = filtered.filter(p => p.price >= min && p.price <= max);\n      }\n    }\n    // Apply size filter (mock implementation)\n    if (this.selectedSize) {\n      // In real implementation, check product.sizes array\n      filtered = filtered.filter(p => true); // Mock: all products have all sizes\n    }\n    // Apply sorting\n    switch (this.sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => (b.rating?.average || 0) - (a.rating?.average || 0));\n        break;\n      case 'newest':\n        // Mock: reverse order for newest\n        filtered.reverse();\n        break;\n      default:\n        // Featured - keep original order\n        break;\n    }\n    this.filteredProducts = filtered;\n  }\n  clearFilters() {\n    this.sortBy = 'featured';\n    this.priceRange = '';\n    this.selectedSize = '';\n    this.filteredProducts = [...this.products];\n  }\n  getProductImage(product) {\n    return product.images[0]?.url || '/assets/images/placeholder.jpg';\n  }\n  getDiscountPercentage(product) {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n  }\n  getStars(rating) {\n    return Array(Math.floor(rating)).fill(0);\n  }\n  getEmptyStars(rating) {\n    return Array(5 - Math.floor(rating)).fill(0);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  addToWishlist(product, event) {\n    event.stopPropagation();\n    // TODO: Implement wishlist functionality\n    console.log('Add to wishlist:', product);\n  }\n  quickView(product, event) {\n    event.stopPropagation();\n    // TODO: Implement quick view modal\n    console.log('Quick view:', product);\n  }\n  addToCart(product, event) {\n    event.stopPropagation();\n    // TODO: Implement add to cart functionality\n    console.log('Add to cart:', product);\n  }\n  goHome() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function CategoryComponent_Factory(t) {\n      return new (t || CategoryComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CategoryComponent,\n      selectors: [[\"app-category\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 50,\n      vars: 10,\n      consts: [[1, \"category-page\"], [1, \"category-header\"], [1, \"breadcrumb\"], [3, \"click\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"current\"], [1, \"category-description\"], [1, \"filters-section\"], [1, \"filter-row\"], [1, \"filter-group\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"featured\"], [\"value\", \"price-low\"], [\"value\", \"price-high\"], [\"value\", \"rating\"], [\"value\", \"newest\"], [\"value\", \"\"], [\"value\", \"0-1000\"], [\"value\", \"1000-3000\"], [\"value\", \"3000-5000\"], [\"value\", \"5000-10000\"], [\"value\", \"10000+\"], [\"class\", \"filter-group\", 4, \"ngIf\"], [1, \"results-count\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"value\", \"XS\"], [\"value\", \"S\"], [\"value\", \"M\"], [\"value\", \"L\"], [\"value\", \"XL\"], [\"value\", \"XXL\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"btn-quick-view\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"btn-add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"discount-badge\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"far fa-star\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"fas\", \"fa-star\"], [1, \"far\", \"fa-star\"], [1, \"original-price\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"fas\", \"fa-search\"], [1, \"btn-primary\", 3, \"click\"], [1, \"loading-state\"], [1, \"loading-spinner\"]],\n      template: function CategoryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n          i0.ɵɵlistener(\"click\", function CategoryComponent_Template_span_click_3_listener() {\n            return ctx.goHome();\n          });\n          i0.ɵɵtext(4, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"i\", 4);\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"h1\");\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 6);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 8)(14, \"div\", 9)(15, \"label\");\n          i0.ɵɵtext(16, \"Sort by:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"select\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryComponent_Template_select_ngModelChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sortBy, $event) || (ctx.sortBy = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function CategoryComponent_Template_select_change_17_listener() {\n            return ctx.onSortChange();\n          });\n          i0.ɵɵelementStart(18, \"option\", 11);\n          i0.ɵɵtext(19, \"Featured\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"option\", 12);\n          i0.ɵɵtext(21, \"Price: Low to High\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"option\", 13);\n          i0.ɵɵtext(23, \"Price: High to Low\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"option\", 14);\n          i0.ɵɵtext(25, \"Customer Rating\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"option\", 15);\n          i0.ɵɵtext(27, \"Newest First\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 9)(29, \"label\");\n          i0.ɵɵtext(30, \"Price Range:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"select\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryComponent_Template_select_ngModelChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.priceRange, $event) || (ctx.priceRange = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function CategoryComponent_Template_select_change_31_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(32, \"option\", 16);\n          i0.ɵɵtext(33, \"All Prices\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"option\", 17);\n          i0.ɵɵtext(35, \"Under \\u20B91,000\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"option\", 18);\n          i0.ɵɵtext(37, \"\\u20B91,000 - \\u20B93,000\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"option\", 19);\n          i0.ɵɵtext(39, \"\\u20B93,000 - \\u20B95,000\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"option\", 20);\n          i0.ɵɵtext(41, \"\\u20B95,000 - \\u20B910,000\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"option\", 21);\n          i0.ɵɵtext(43, \"Above \\u20B910,000\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(44, CategoryComponent_div_44_Template, 18, 1, \"div\", 22);\n          i0.ɵɵelementStart(45, \"div\", 23);\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(47, CategoryComponent_div_47_Template, 2, 1, \"div\", 24)(48, CategoryComponent_div_48_Template, 9, 0, \"div\", 25)(49, CategoryComponent_div_49_Template, 4, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.getCategoryDisplayName());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getCategoryDisplayName());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getCategoryDescription());\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.sortBy);\n          i0.ɵɵadvance(14);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.priceRange);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.category === \"women\" || ctx.category === \"men\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.filteredProducts.length, \" products found \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredProducts.length === 0 && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".category-page[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.category-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  color: #007bff;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%]   .current[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.category-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n  color: #333;\\n}\\n\\n.category-description[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  margin-bottom: 30px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filter-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n  flex-wrap: wrap;\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  white-space: nowrap;\\n}\\n\\n.filter-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n  min-width: 150px;\\n}\\n\\n.results-count[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-weight: 500;\\n  color: #666;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 24px;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 300px;\\n  overflow: hidden;\\n}\\n\\n.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%], .btn-quick-view[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%]:hover, .btn-quick-view[_ngcontent-%COMP%]:hover {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: #ff4757;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n  color: #333;\\n  line-height: 1.3;\\n}\\n\\n.product-brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 8px;\\n}\\n\\n.product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n\\n.stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n\\n.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #ffc107;\\n}\\n\\n.rating-count[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #999;\\n  text-decoration: line-through;\\n  margin-left: 8px;\\n}\\n\\n.btn-add-cart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n}\\n\\n.btn-add-cart[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 80px 20px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-bottom: 10px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 30px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n\\n.loading-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 80px 20px;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #007bff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 20px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .category-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .filter-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 16px;\\n  }\\n  .filter-group[_ngcontent-%COMP%] {\\n    justify-content: space-between;\\n  }\\n  .results-count[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    text-align: center;\\n  }\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "CategoryComponent_div_44_Template_select_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "selectedSize", "ɵɵresetView", "ɵɵlistener", "CategoryComponent_div_44_Template_select_change_3_listener", "onFilterChange", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r4", "ɵɵelement", "ɵɵtemplate", "CategoryComponent_div_47_div_1_div_14_i_2_Template", "CategoryComponent_div_47_div_1_div_14_i_3_Template", "ɵɵproperty", "getStars", "rating", "average", "getEmptyStars", "count", "ɵɵpipeBind2", "originalPrice", "CategoryComponent_div_47_div_1_Template_div_click_0_listener", "_r3", "$implicit", "viewProduct", "CategoryComponent_div_47_div_1_Template_button_click_4_listener", "addToWishlist", "CategoryComponent_div_47_div_1_Template_button_click_6_listener", "quickView", "CategoryComponent_div_47_div_1_div_8_Template", "CategoryComponent_div_47_div_1_div_14_Template", "CategoryComponent_div_47_div_1_span_19_Template", "CategoryComponent_div_47_div_1_Template_button_click_20_listener", "addToCart", "getProductImage", "ɵɵsanitizeUrl", "name", "price", "ɵɵtextInterpolate", "brand", "CategoryComponent_div_47_div_1_Template", "filteredProducts", "CategoryComponent_div_48_Template_button_click_7_listener", "_r5", "clearFilters", "CategoryComponent", "constructor", "route", "router", "category", "products", "loading", "sortBy", "priceRange", "ngOnInit", "params", "subscribe", "loadProducts", "getCategoryDisplayName", "categoryNames", "char<PERSON>t", "toUpperCase", "slice", "getCategoryDescription", "descriptions", "onSortChange", "applyFilters", "filtered", "filter", "p", "min", "max", "split", "map", "Number", "sort", "a", "b", "reverse", "product", "images", "url", "Math", "round", "Array", "floor", "fill", "navigate", "_id", "event", "stopPropagation", "console", "log", "goHome", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "CategoryComponent_Template", "rf", "ctx", "CategoryComponent_Template_span_click_3_listener", "CategoryComponent_Template_select_ngModelChange_17_listener", "CategoryComponent_Template_select_change_17_listener", "CategoryComponent_Template_select_ngModelChange_31_listener", "CategoryComponent_Template_select_change_31_listener", "CategoryComponent_div_44_Template", "CategoryComponent_div_47_Template", "CategoryComponent_div_48_Template", "CategoryComponent_div_49_Template", "length", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "NgSelectOption", "ɵNgSelectMultipleOption", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\category\\category.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Product {\n  _id: string;\n  name: string;\n  price: number;\n  originalPrice?: number;\n  images: { url: string; alt: string }[];\n  brand: string;\n  rating: { average: number; count: number };\n  category: string;\n  subcategory: string;\n  tags: string[];\n}\n\n@Component({\n  selector: 'app-category',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"category-page\">\n      <!-- Header -->\n      <div class=\"category-header\">\n        <div class=\"breadcrumb\">\n          <span (click)=\"goHome()\">Home</span>\n          <i class=\"fas fa-chevron-right\"></i>\n          <span class=\"current\">{{ getCategoryDisplayName() }}</span>\n        </div>\n        <h1>{{ getCategoryDisplayName() }}</h1>\n        <p class=\"category-description\">{{ getCategoryDescription() }}</p>\n      </div>\n\n      <!-- Filters & Sort -->\n      <div class=\"filters-section\">\n        <div class=\"filter-row\">\n          <div class=\"filter-group\">\n            <label>Sort by:</label>\n            <select [(ngModel)]=\"sortBy\" (change)=\"onSortChange()\">\n              <option value=\"featured\">Featured</option>\n              <option value=\"price-low\">Price: Low to High</option>\n              <option value=\"price-high\">Price: High to Low</option>\n              <option value=\"rating\">Customer Rating</option>\n              <option value=\"newest\">Newest First</option>\n            </select>\n          </div>\n\n          <div class=\"filter-group\">\n            <label>Price Range:</label>\n            <select [(ngModel)]=\"priceRange\" (change)=\"onFilterChange()\">\n              <option value=\"\">All Prices</option>\n              <option value=\"0-1000\">Under ₹1,000</option>\n              <option value=\"1000-3000\">₹1,000 - ₹3,000</option>\n              <option value=\"3000-5000\">₹3,000 - ₹5,000</option>\n              <option value=\"5000-10000\">₹5,000 - ₹10,000</option>\n              <option value=\"10000+\">Above ₹10,000</option>\n            </select>\n          </div>\n\n          <div class=\"filter-group\" *ngIf=\"category === 'women' || category === 'men'\">\n            <label>Size:</label>\n            <select [(ngModel)]=\"selectedSize\" (change)=\"onFilterChange()\">\n              <option value=\"\">All Sizes</option>\n              <option value=\"XS\">XS</option>\n              <option value=\"S\">S</option>\n              <option value=\"M\">M</option>\n              <option value=\"L\">L</option>\n              <option value=\"XL\">XL</option>\n              <option value=\"XXL\">XXL</option>\n            </select>\n          </div>\n\n          <div class=\"results-count\">\n            {{ filteredProducts.length }} products found\n          </div>\n        </div>\n      </div>\n\n      <!-- Products Grid -->\n      <div class=\"products-grid\" *ngIf=\"filteredProducts.length > 0\">\n        <div class=\"product-card\" *ngFor=\"let product of filteredProducts\" (click)=\"viewProduct(product)\">\n          <div class=\"product-image\">\n            <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" loading=\"lazy\">\n            <div class=\"product-actions\">\n              <button class=\"btn-wishlist\" (click)=\"addToWishlist(product, $event)\">\n                <i class=\"far fa-heart\"></i>\n              </button>\n              <button class=\"btn-quick-view\" (click)=\"quickView(product, $event)\">\n                <i class=\"fas fa-eye\"></i>\n              </button>\n            </div>\n            <div class=\"discount-badge\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n              {{ getDiscountPercentage(product) }}% OFF\n            </div>\n          </div>\n          \n          <div class=\"product-info\">\n            <h3 class=\"product-name\">{{ product.name }}</h3>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            \n            <div class=\"product-rating\" *ngIf=\"product.rating\">\n              <div class=\"stars\">\n                <i class=\"fas fa-star\" *ngFor=\"let star of getStars(product.rating.average)\"></i>\n                <i class=\"far fa-star\" *ngFor=\"let star of getEmptyStars(product.rating.average)\"></i>\n              </div>\n              <span class=\"rating-count\">({{ product.rating.count }})</span>\n            </div>\n            \n            <div class=\"product-price\">\n              <span class=\"current-price\">₹{{ product.price | number:'1.0-0' }}</span>\n              <span class=\"original-price\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                ₹{{ product.originalPrice | number:'1.0-0' }}\n              </span>\n            </div>\n            \n            <button class=\"btn-add-cart\" (click)=\"addToCart(product, $event)\">\n              <i class=\"fas fa-shopping-cart\"></i>\n              Add to Cart\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Empty State -->\n      <div class=\"empty-state\" *ngIf=\"filteredProducts.length === 0 && !loading\">\n        <div class=\"empty-content\">\n          <i class=\"fas fa-search\"></i>\n          <h2>No products found</h2>\n          <p>Try adjusting your filters or browse other categories</p>\n          <button class=\"btn-primary\" (click)=\"clearFilters()\">Clear Filters</button>\n        </div>\n      </div>\n\n      <!-- Loading State -->\n      <div class=\"loading-state\" *ngIf=\"loading\">\n        <div class=\"loading-spinner\"></div>\n        <p>Loading products...</p>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .category-page {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n    }\n\n    .category-header {\n      margin-bottom: 30px;\n    }\n\n    .breadcrumb {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-bottom: 16px;\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    .breadcrumb span {\n      cursor: pointer;\n    }\n\n    .breadcrumb span:hover {\n      color: #007bff;\n    }\n\n    .breadcrumb .current {\n      color: #333;\n      font-weight: 500;\n    }\n\n    .category-header h1 {\n      font-size: 2.5rem;\n      font-weight: 700;\n      margin-bottom: 8px;\n      color: #333;\n    }\n\n    .category-description {\n      font-size: 1.1rem;\n      color: #666;\n      margin: 0;\n    }\n\n    .filters-section {\n      background: white;\n      border-radius: 12px;\n      padding: 20px;\n      margin-bottom: 30px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n    }\n\n    .filter-row {\n      display: flex;\n      align-items: center;\n      gap: 24px;\n      flex-wrap: wrap;\n    }\n\n    .filter-group {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .filter-group label {\n      font-weight: 500;\n      color: #333;\n      white-space: nowrap;\n    }\n\n    .filter-group select {\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 6px;\n      font-size: 0.9rem;\n      min-width: 150px;\n    }\n\n    .results-count {\n      margin-left: auto;\n      font-weight: 500;\n      color: #666;\n    }\n\n    .products-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n      gap: 24px;\n    }\n\n    .product-card {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .product-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0,0,0,0.15);\n    }\n\n    .product-image {\n      position: relative;\n      height: 300px;\n      overflow: hidden;\n    }\n\n    .product-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .product-card:hover .product-image img {\n      transform: scale(1.05);\n    }\n\n    .product-actions {\n      position: absolute;\n      top: 12px;\n      right: 12px;\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-card:hover .product-actions {\n      opacity: 1;\n    }\n\n    .btn-wishlist, .btn-quick-view {\n      width: 40px;\n      height: 40px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.9);\n      color: #333;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: all 0.2s ease;\n    }\n\n    .btn-wishlist:hover, .btn-quick-view:hover {\n      background: #007bff;\n      color: white;\n    }\n\n    .discount-badge {\n      position: absolute;\n      top: 12px;\n      left: 12px;\n      background: #ff4757;\n      color: white;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.75rem;\n      font-weight: 600;\n    }\n\n    .product-info {\n      padding: 20px;\n    }\n\n    .product-name {\n      font-size: 1.1rem;\n      font-weight: 600;\n      margin-bottom: 4px;\n      color: #333;\n      line-height: 1.3;\n    }\n\n    .product-brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .product-rating {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-bottom: 12px;\n    }\n\n    .stars {\n      display: flex;\n      gap: 2px;\n    }\n\n    .stars i {\n      font-size: 0.8rem;\n      color: #ffc107;\n    }\n\n    .rating-count {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .product-price {\n      margin-bottom: 16px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n      margin-left: 8px;\n    }\n\n    .btn-add-cart {\n      width: 100%;\n      padding: 12px;\n      background: #007bff;\n      color: white;\n      border: none;\n      border-radius: 6px;\n      font-weight: 500;\n      cursor: pointer;\n      transition: background 0.2s ease;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n    }\n\n    .btn-add-cart:hover {\n      background: #0056b3;\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 80px 20px;\n    }\n\n    .empty-content i {\n      font-size: 4rem;\n      color: #ddd;\n      margin-bottom: 20px;\n    }\n\n    .empty-content h2 {\n      font-size: 1.5rem;\n      margin-bottom: 10px;\n    }\n\n    .empty-content p {\n      color: #666;\n      margin-bottom: 30px;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: white;\n      padding: 12px 24px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 500;\n      cursor: pointer;\n    }\n\n    .loading-state {\n      text-align: center;\n      padding: 80px 20px;\n    }\n\n    .loading-spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid #f3f3f3;\n      border-top: 4px solid #007bff;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto 20px;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    @media (max-width: 768px) {\n      .category-header h1 {\n        font-size: 2rem;\n      }\n\n      .filter-row {\n        flex-direction: column;\n        align-items: stretch;\n        gap: 16px;\n      }\n\n      .filter-group {\n        justify-content: space-between;\n      }\n\n      .results-count {\n        margin-left: 0;\n        text-align: center;\n      }\n\n      .products-grid {\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n        gap: 16px;\n      }\n    }\n  `]\n})\nexport class CategoryComponent implements OnInit {\n  category: string = '';\n  products: Product[] = [];\n  filteredProducts: Product[] = [];\n  loading = true;\n  \n  // Filters\n  sortBy = 'featured';\n  priceRange = '';\n  selectedSize = '';\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      this.category = params['category'];\n      this.loadProducts();\n    });\n  }\n\n  loadProducts() {\n    this.loading = true;\n\n    // Load from real API\n    this.products = [];\n    this.filteredProducts = [];\n    this.loading = false;\n  }\n\n\n\n  getCategoryDisplayName(): string {\n    const categoryNames: { [key: string]: string } = {\n      'women': 'Women\\'s Fashion',\n      'men': 'Men\\'s Fashion',\n      'kids': 'Kids\\' Fashion',\n      'ethnic': 'Ethnic Wear',\n      'all': 'All Products'\n    };\n    return categoryNames[this.category] || this.category.charAt(0).toUpperCase() + this.category.slice(1);\n  }\n\n  getCategoryDescription(): string {\n    const descriptions: { [key: string]: string } = {\n      'women': 'Discover the latest trends in women\\'s fashion',\n      'men': 'Explore stylish and comfortable men\\'s clothing',\n      'kids': 'Fun and comfortable clothing for children',\n      'ethnic': 'Traditional and ethnic wear for special occasions',\n      'all': 'Browse our complete collection of fashion items'\n    };\n    return descriptions[this.category] || 'Explore our collection';\n  }\n\n  onSortChange() {\n    this.applyFilters();\n  }\n\n  onFilterChange() {\n    this.applyFilters();\n  }\n\n  applyFilters() {\n    let filtered = [...this.products];\n\n    // Apply price filter\n    if (this.priceRange) {\n      if (this.priceRange === '10000+') {\n        filtered = filtered.filter(p => p.price >= 10000);\n      } else {\n        const [min, max] = this.priceRange.split('-').map(Number);\n        filtered = filtered.filter(p => p.price >= min && p.price <= max);\n      }\n    }\n\n    // Apply size filter (mock implementation)\n    if (this.selectedSize) {\n      // In real implementation, check product.sizes array\n      filtered = filtered.filter(p => true); // Mock: all products have all sizes\n    }\n\n    // Apply sorting\n    switch (this.sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => (b.rating?.average || 0) - (a.rating?.average || 0));\n        break;\n      case 'newest':\n        // Mock: reverse order for newest\n        filtered.reverse();\n        break;\n      default:\n        // Featured - keep original order\n        break;\n    }\n\n    this.filteredProducts = filtered;\n  }\n\n  clearFilters() {\n    this.sortBy = 'featured';\n    this.priceRange = '';\n    this.selectedSize = '';\n    this.filteredProducts = [...this.products];\n  }\n\n  getProductImage(product: Product): string {\n    return product.images[0]?.url || '/assets/images/placeholder.jpg';\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n  }\n\n  getStars(rating: number): number[] {\n    return Array(Math.floor(rating)).fill(0);\n  }\n\n  getEmptyStars(rating: number): number[] {\n    return Array(5 - Math.floor(rating)).fill(0);\n  }\n\n  viewProduct(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  addToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement wishlist functionality\n    console.log('Add to wishlist:', product);\n  }\n\n  quickView(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement quick view modal\n    console.log('Quick view:', product);\n  }\n\n  addToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement add to cart functionality\n    console.log('Add to cart:', product);\n  }\n\n  goHome() {\n    this.router.navigate(['/home']);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IA2DhCC,EADF,CAAAC,cAAA,aAA6E,YACpE;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpBH,EAAA,CAAAC,cAAA,iBAA+D;IAAvDD,EAAA,CAAAI,gBAAA,2BAAAC,kEAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,YAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,YAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAA0B;IAACN,EAAA,CAAAc,UAAA,oBAAAC,2DAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAUJ,MAAA,CAAAO,cAAA,EAAgB;IAAA,EAAC;IAC5DhB,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnCH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9BH,EAAA,CAAAC,cAAA,iBAAkB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5BH,EAAA,CAAAC,cAAA,kBAAkB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5BH,EAAA,CAAAC,cAAA,kBAAkB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5BH,EAAA,CAAAC,cAAA,kBAAmB;IAAAD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9BH,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAE3BF,EAF2B,CAAAG,YAAA,EAAS,EACzB,EACL;;;;IATIH,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAkB,gBAAA,YAAAT,MAAA,CAAAG,YAAA,CAA0B;;;;;IA8BlCZ,EAAA,CAAAC,cAAA,cAAmG;IACjGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAmB,kBAAA,MAAAV,MAAA,CAAAW,qBAAA,CAAAC,UAAA,YACF;;;;;IASIrB,EAAA,CAAAsB,SAAA,YAAiF;;;;;IACjFtB,EAAA,CAAAsB,SAAA,YAAsF;;;;;IAFxFtB,EADF,CAAAC,cAAA,cAAmD,cAC9B;IAEjBD,EADA,CAAAuB,UAAA,IAAAC,kDAAA,gBAA6E,IAAAC,kDAAA,gBACK;IACpFzB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;;;;;IAJsCH,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAA0B,UAAA,YAAAjB,MAAA,CAAAkB,QAAA,CAAAN,UAAA,CAAAO,MAAA,CAAAC,OAAA,EAAmC;IACnC7B,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAA0B,UAAA,YAAAjB,MAAA,CAAAqB,aAAA,CAAAT,UAAA,CAAAO,MAAA,CAAAC,OAAA,EAAwC;IAEvD7B,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAmB,kBAAA,MAAAE,UAAA,CAAAO,MAAA,CAAAG,KAAA,MAA4B;;;;;IAKvD/B,EAAA,CAAAC,cAAA,eAAoG;IAClGD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAmB,kBAAA,YAAAnB,EAAA,CAAAgC,WAAA,OAAAX,UAAA,CAAAY,aAAA,gBACF;;;;;;IAhCNjC,EAAA,CAAAC,cAAA,cAAkG;IAA/BD,EAAA,CAAAc,UAAA,mBAAAoB,6DAAA;MAAA,MAAAb,UAAA,GAAArB,EAAA,CAAAO,aAAA,CAAA4B,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA4B,WAAA,CAAAhB,UAAA,CAAoB;IAAA,EAAC;IAC/FrB,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAsB,SAAA,cAA0E;IAExEtB,EADF,CAAAC,cAAA,cAA6B,iBAC2C;IAAzCD,EAAA,CAAAc,UAAA,mBAAAwB,gEAAAhC,MAAA;MAAA,MAAAe,UAAA,GAAArB,EAAA,CAAAO,aAAA,CAAA4B,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA8B,aAAA,CAAAlB,UAAA,EAAAf,MAAA,CAA8B;IAAA,EAAC;IACnEN,EAAA,CAAAsB,SAAA,YAA4B;IAC9BtB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAoE;IAArCD,EAAA,CAAAc,UAAA,mBAAA0B,gEAAAlC,MAAA;MAAA,MAAAe,UAAA,GAAArB,EAAA,CAAAO,aAAA,CAAA4B,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgC,SAAA,CAAApB,UAAA,EAAAf,MAAA,CAA0B;IAAA,EAAC;IACjEN,EAAA,CAAAsB,SAAA,YAA0B;IAE9BtB,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAuB,UAAA,IAAAmB,6CAAA,kBAAmG;IAGrG1C,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA0B,cACC;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EAAA,CAAAuB,UAAA,KAAAoB,8CAAA,kBAAmD;IASjD3C,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAuB,UAAA,KAAAqB,+CAAA,mBAAoG;IAGtG5C,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAAkE;IAArCD,EAAA,CAAAc,UAAA,mBAAA+B,iEAAAvC,MAAA;MAAA,MAAAe,UAAA,GAAArB,EAAA,CAAAO,aAAA,CAAA4B,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAqC,SAAA,CAAAzB,UAAA,EAAAf,MAAA,CAA0B;IAAA,EAAC;IAC/DN,EAAA,CAAAsB,SAAA,aAAoC;IACpCtB,EAAA,CAAAE,MAAA,qBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAtCGH,EAAA,CAAAiB,SAAA,GAAgC;IAACjB,EAAjC,CAAA0B,UAAA,QAAAjB,MAAA,CAAAsC,eAAA,CAAA1B,UAAA,GAAArB,EAAA,CAAAgD,aAAA,CAAgC,QAAA3B,UAAA,CAAA4B,IAAA,CAAqB;IAS7BjD,EAAA,CAAAiB,SAAA,GAAoE;IAApEjB,EAAA,CAAA0B,UAAA,SAAAL,UAAA,CAAAY,aAAA,IAAAZ,UAAA,CAAAY,aAAA,GAAAZ,UAAA,CAAA6B,KAAA,CAAoE;IAMxElD,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAmD,iBAAA,CAAA9B,UAAA,CAAA4B,IAAA,CAAkB;IAClBjD,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAmD,iBAAA,CAAA9B,UAAA,CAAA+B,KAAA,CAAmB;IAEfpD,EAAA,CAAAiB,SAAA,EAAoB;IAApBjB,EAAA,CAAA0B,UAAA,SAAAL,UAAA,CAAAO,MAAA,CAAoB;IASnB5B,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAmB,kBAAA,WAAAnB,EAAA,CAAAgC,WAAA,QAAAX,UAAA,CAAA6B,KAAA,eAAqC;IACnClD,EAAA,CAAAiB,SAAA,GAAoE;IAApEjB,EAAA,CAAA0B,UAAA,SAAAL,UAAA,CAAAY,aAAA,IAAAZ,UAAA,CAAAY,aAAA,GAAAZ,UAAA,CAAA6B,KAAA,CAAoE;;;;;IA/B1GlD,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAuB,UAAA,IAAA8B,uCAAA,oBAAkG;IAyCpGrD,EAAA,CAAAG,YAAA,EAAM;;;;IAzC0CH,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAA0B,UAAA,YAAAjB,MAAA,CAAA6C,gBAAA,CAAmB;;;;;;IA6CjEtD,EADF,CAAAC,cAAA,cAA2E,cAC9C;IACzBD,EAAA,CAAAsB,SAAA,YAA6B;IAC7BtB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,4DAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,iBAAqD;IAAzBD,EAAA,CAAAc,UAAA,mBAAAyC,0DAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgD,YAAA,EAAc;IAAA,EAAC;IAACzD,EAAA,CAAAE,MAAA,oBAAa;IAEtEF,EAFsE,CAAAG,YAAA,EAAS,EACvE,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAsB,SAAA,cAAmC;IACnCtB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IACxBF,EADwB,CAAAG,YAAA,EAAI,EACtB;;;AAwUZ,OAAM,MAAOuD,iBAAiB;EAW5BC,YACUC,KAAqB,EACrBC,MAAc;IADd,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAZhB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAT,gBAAgB,GAAc,EAAE;IAChC,KAAAU,OAAO,GAAG,IAAI;IAEd;IACA,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAtD,YAAY,GAAG,EAAE;EAKd;EAEHuD,QAAQA,CAAA;IACN,IAAI,CAACP,KAAK,CAACQ,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAI,CAACN,QAAQ,GAAGM,MAAM,CAAC,UAAU,CAAC;MAClC,IAAI,CAACE,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACN,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACT,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACU,OAAO,GAAG,KAAK;EACtB;EAIAO,sBAAsBA,CAAA;IACpB,MAAMC,aAAa,GAA8B;MAC/C,OAAO,EAAE,kBAAkB;MAC3B,KAAK,EAAE,gBAAgB;MACvB,MAAM,EAAE,gBAAgB;MACxB,QAAQ,EAAE,aAAa;MACvB,KAAK,EAAE;KACR;IACD,OAAOA,aAAa,CAAC,IAAI,CAACV,QAAQ,CAAC,IAAI,IAAI,CAACA,QAAQ,CAACW,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG,IAAI,CAACZ,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC;EACvG;EAEAC,sBAAsBA,CAAA;IACpB,MAAMC,YAAY,GAA8B;MAC9C,OAAO,EAAE,gDAAgD;MACzD,KAAK,EAAE,iDAAiD;MACxD,MAAM,EAAE,2CAA2C;MACnD,QAAQ,EAAE,mDAAmD;MAC7D,KAAK,EAAE;KACR;IACD,OAAOA,YAAY,CAAC,IAAI,CAACf,QAAQ,CAAC,IAAI,wBAAwB;EAChE;EAEAgB,YAAYA,CAAA;IACV,IAAI,CAACC,YAAY,EAAE;EACrB;EAEA/D,cAAcA,CAAA;IACZ,IAAI,CAAC+D,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAIC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACjB,QAAQ,CAAC;IAEjC;IACA,IAAI,IAAI,CAACG,UAAU,EAAE;MACnB,IAAI,IAAI,CAACA,UAAU,KAAK,QAAQ,EAAE;QAChCc,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI,KAAK,CAAC;OAClD,MAAM;QACL,MAAM,CAACiC,GAAG,EAAEC,GAAG,CAAC,GAAG,IAAI,CAAClB,UAAU,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;QACzDP,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAIiC,GAAG,IAAID,CAAC,CAAChC,KAAK,IAAIkC,GAAG,CAAC;;;IAIrE;IACA,IAAI,IAAI,CAACxE,YAAY,EAAE;MACrB;MACAoE,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;;IAGzC;IACA,QAAQ,IAAI,CAACjB,MAAM;MACjB,KAAK,WAAW;QACde,QAAQ,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvC,KAAK,GAAGwC,CAAC,CAACxC,KAAK,CAAC;QAC1C;MACF,KAAK,YAAY;QACf8B,QAAQ,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACxC,KAAK,GAAGuC,CAAC,CAACvC,KAAK,CAAC;QAC1C;MACF,KAAK,QAAQ;QACX8B,QAAQ,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC9D,MAAM,EAAEC,OAAO,IAAI,CAAC,KAAK4D,CAAC,CAAC7D,MAAM,EAAEC,OAAO,IAAI,CAAC,CAAC,CAAC;QAC5E;MACF,KAAK,QAAQ;QACX;QACAmD,QAAQ,CAACW,OAAO,EAAE;QAClB;MACF;QACE;QACA;;IAGJ,IAAI,CAACrC,gBAAgB,GAAG0B,QAAQ;EAClC;EAEAvB,YAAYA,CAAA;IACV,IAAI,CAACQ,MAAM,GAAG,UAAU;IACxB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACtD,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC0C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACS,QAAQ,CAAC;EAC5C;EAEAhB,eAAeA,CAAC6C,OAAgB;IAC9B,OAAOA,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,GAAG,IAAI,gCAAgC;EACnE;EAEA1E,qBAAqBA,CAACwE,OAAgB;IACpC,IAAI,CAACA,OAAO,CAAC3D,aAAa,IAAI2D,OAAO,CAAC3D,aAAa,IAAI2D,OAAO,CAAC1C,KAAK,EAAE,OAAO,CAAC;IAC9E,OAAO6C,IAAI,CAACC,KAAK,CAAE,CAACJ,OAAO,CAAC3D,aAAa,GAAG2D,OAAO,CAAC1C,KAAK,IAAI0C,OAAO,CAAC3D,aAAa,GAAI,GAAG,CAAC;EAC5F;EAEAN,QAAQA,CAACC,MAAc;IACrB,OAAOqE,KAAK,CAACF,IAAI,CAACG,KAAK,CAACtE,MAAM,CAAC,CAAC,CAACuE,IAAI,CAAC,CAAC,CAAC;EAC1C;EAEArE,aAAaA,CAACF,MAAc;IAC1B,OAAOqE,KAAK,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAACtE,MAAM,CAAC,CAAC,CAACuE,IAAI,CAAC,CAAC,CAAC;EAC9C;EAEA9D,WAAWA,CAACuD,OAAgB;IAC1B,IAAI,CAAC/B,MAAM,CAACuC,QAAQ,CAAC,CAAC,UAAU,EAAER,OAAO,CAACS,GAAG,CAAC,CAAC;EACjD;EAEA9D,aAAaA,CAACqD,OAAgB,EAAEU,KAAY;IAC1CA,KAAK,CAACC,eAAe,EAAE;IACvB;IACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEb,OAAO,CAAC;EAC1C;EAEAnD,SAASA,CAACmD,OAAgB,EAAEU,KAAY;IACtCA,KAAK,CAACC,eAAe,EAAE;IACvB;IACAC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEb,OAAO,CAAC;EACrC;EAEA9C,SAASA,CAAC8C,OAAgB,EAAEU,KAAY;IACtCA,KAAK,CAACC,eAAe,EAAE;IACvB;IACAC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEb,OAAO,CAAC;EACtC;EAEAc,MAAMA,CAAA;IACJ,IAAI,CAAC7C,MAAM,CAACuC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBA1JW1C,iBAAiB,EAAA1D,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAAjBpD,iBAAiB;MAAAqD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjH,EAAA,CAAAkH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxbpBxH,EAJN,CAAAC,cAAA,aAA2B,aAEI,aACH,cACG;UAAnBD,EAAA,CAAAc,UAAA,mBAAA4G,iDAAA;YAAA,OAASD,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UAAC1G,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpCH,EAAA,CAAAsB,SAAA,WAAoC;UACpCtB,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,GAA8B;UACtDF,EADsD,CAAAG,YAAA,EAAO,EACvD;UACNH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,GAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,YAAgC;UAAAD,EAAA,CAAAE,MAAA,IAA8B;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC9D;UAMAH,EAHN,CAAAC,cAAA,cAA6B,cACH,cACI,aACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAAC,cAAA,kBAAuD;UAA/CD,EAAA,CAAAI,gBAAA,2BAAAuH,4DAAArH,MAAA;YAAAN,EAAA,CAAAW,kBAAA,CAAA8G,GAAA,CAAAxD,MAAA,EAAA3D,MAAA,MAAAmH,GAAA,CAAAxD,MAAA,GAAA3D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAACN,EAAA,CAAAc,UAAA,oBAAA8G,qDAAA;YAAA,OAAUH,GAAA,CAAA3C,YAAA,EAAc;UAAA,EAAC;UACpD9E,EAAA,CAAAC,cAAA,kBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1CH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrDH,EAAA,CAAAC,cAAA,kBAA2B;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtDH,EAAA,CAAAC,cAAA,kBAAuB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/CH,EAAA,CAAAC,cAAA,kBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAEvCF,EAFuC,CAAAG,YAAA,EAAS,EACrC,EACL;UAGJH,EADF,CAAAC,cAAA,cAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3BH,EAAA,CAAAC,cAAA,kBAA6D;UAArDD,EAAA,CAAAI,gBAAA,2BAAAyH,4DAAAvH,MAAA;YAAAN,EAAA,CAAAW,kBAAA,CAAA8G,GAAA,CAAAvD,UAAA,EAAA5D,MAAA,MAAAmH,GAAA,CAAAvD,UAAA,GAAA5D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACN,EAAA,CAAAc,UAAA,oBAAAgH,qDAAA;YAAA,OAAUL,GAAA,CAAAzG,cAAA,EAAgB;UAAA,EAAC;UAC1DhB,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,kBAAuB;UAAAD,EAAA,CAAAE,MAAA,yBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAE,MAAA,iCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAE,MAAA,iCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAC,cAAA,kBAA2B;UAAAD,EAAA,CAAAE,MAAA,kCAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpDH,EAAA,CAAAC,cAAA,kBAAuB;UAAAD,EAAA,CAAAE,MAAA,0BAAa;UAExCF,EAFwC,CAAAG,YAAA,EAAS,EACtC,EACL;UAENH,EAAA,CAAAuB,UAAA,KAAAwG,iCAAA,mBAA6E;UAa7E/H,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAE,MAAA,IACF;UAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UA0DNH,EAvDA,CAAAuB,UAAA,KAAAyG,iCAAA,kBAA+D,KAAAC,iCAAA,kBA6CY,KAAAC,iCAAA,kBAUhC;UAI7ClI,EAAA,CAAAG,YAAA,EAAM;;;UA/GsBH,EAAA,CAAAiB,SAAA,GAA8B;UAA9BjB,EAAA,CAAAmD,iBAAA,CAAAsE,GAAA,CAAAlD,sBAAA,GAA8B;UAElDvE,EAAA,CAAAiB,SAAA,GAA8B;UAA9BjB,EAAA,CAAAmD,iBAAA,CAAAsE,GAAA,CAAAlD,sBAAA,GAA8B;UACFvE,EAAA,CAAAiB,SAAA,GAA8B;UAA9BjB,EAAA,CAAAmD,iBAAA,CAAAsE,GAAA,CAAA7C,sBAAA,GAA8B;UAQlD5E,EAAA,CAAAiB,SAAA,GAAoB;UAApBjB,EAAA,CAAAkB,gBAAA,YAAAuG,GAAA,CAAAxD,MAAA,CAAoB;UAWpBjE,EAAA,CAAAiB,SAAA,IAAwB;UAAxBjB,EAAA,CAAAkB,gBAAA,YAAAuG,GAAA,CAAAvD,UAAA,CAAwB;UAUPlE,EAAA,CAAAiB,SAAA,IAAgD;UAAhDjB,EAAA,CAAA0B,UAAA,SAAA+F,GAAA,CAAA3D,QAAA,gBAAA2D,GAAA,CAAA3D,QAAA,WAAgD;UAczE9D,EAAA,CAAAiB,SAAA,GACF;UADEjB,EAAA,CAAAmB,kBAAA,MAAAsG,GAAA,CAAAnE,gBAAA,CAAA6E,MAAA,qBACF;UAKwBnI,EAAA,CAAAiB,SAAA,EAAiC;UAAjCjB,EAAA,CAAA0B,UAAA,SAAA+F,GAAA,CAAAnE,gBAAA,CAAA6E,MAAA,KAAiC;UA6CnCnI,EAAA,CAAAiB,SAAA,EAA+C;UAA/CjB,EAAA,CAAA0B,UAAA,SAAA+F,GAAA,CAAAnE,gBAAA,CAAA6E,MAAA,WAAAV,GAAA,CAAAzD,OAAA,CAA+C;UAU7ChE,EAAA,CAAAiB,SAAA,EAAa;UAAbjB,EAAA,CAAA0B,UAAA,SAAA+F,GAAA,CAAAzD,OAAA,CAAa;;;qBAnHnClE,YAAY,EAAAsI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAExI,WAAW,EAAAyI,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,0BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}