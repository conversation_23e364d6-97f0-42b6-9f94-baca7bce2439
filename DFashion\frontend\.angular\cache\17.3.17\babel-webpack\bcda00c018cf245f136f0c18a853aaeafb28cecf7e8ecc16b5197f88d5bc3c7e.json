{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/recommendation.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nfunction TrendingProductsComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewAllTrending());\n    });\n    i0.ɵɵtext(1, \" View All \");\n    i0.ɵɵelement(2, \"i\", 12);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingProductsComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"div\", 18);\n    i0.ɵɵelement(3, \"div\", 19)(4, \"div\", 20)(5, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_9_div_2_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingProductsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Unable to load trending products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_10_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadTrendingProducts());\n    });\n    i0.ɵɵelement(8, \"i\", 26);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductsComponent_div_11_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r5), \"% OFF \");\n  }\n}\nfunction TrendingProductsComponent_div_11_div_2_i_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 64);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6);\n  }\n}\nfunction TrendingProductsComponent_div_11_div_2_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.originalPrice));\n  }\n}\nfunction TrendingProductsComponent_div_11_div_2_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tag_r7);\n  }\n}\nfunction TrendingProductsComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_11_div_2_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, TrendingProductsComponent_div_11_div_2_div_7_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementStart(8, \"div\", 35);\n    i0.ɵɵelement(9, \"i\", 36);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 37)(13, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_11_div_2_Template_button_click_13_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r5, $event));\n    });\n    i0.ɵɵelement(14, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_11_div_2_Template_button_click_15_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r5, $event));\n    });\n    i0.ɵɵelement(16, \"i\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 42)(18, \"div\", 43);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"h3\", 44);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 45)(23, \"div\", 46);\n    i0.ɵɵtemplate(24, TrendingProductsComponent_div_11_div_2_i_24_Template, 1, 2, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 48);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 49)(28, \"span\", 50);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, TrendingProductsComponent_div_11_div_2_span_30_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 52)(32, \"div\", 53);\n    i0.ɵɵelement(33, \"i\", 54);\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 53);\n    i0.ɵɵelement(37, \"i\", 55);\n    i0.ɵɵelementStart(38, \"span\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 53);\n    i0.ɵɵelement(41, \"i\", 56);\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 57)(45, \"div\", 58);\n    i0.ɵɵelement(46, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\", 60);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"div\", 61);\n    i0.ɵɵtemplate(50, TrendingProductsComponent_div_11_div_2_span_50_Template, 2, 1, \"span\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r5.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r5.images[0].alt || product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r1.getTrendingIcon(product_r5.trendingReason));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.trendingReason);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r5) > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 21, product_r5.trendingScore * 100, \"1.0-0\"), \"% \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStarArray(product_r5.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", product_r5.rating.average, \" (\", product_r5.rating.count, \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.originalPrice && product_r5.originalPrice > product_r5.price);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(product_r5.viewCount), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(product_r5.purchaseCount), \" sold\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(product_r5.shareCount), \" shares\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", product_r5.engagementRate, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", product_r5.engagementRate, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", product_r5.tags.slice(0, 3));\n  }\n}\nfunction TrendingProductsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_11_div_2_Template, 51, 24, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction TrendingProductsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵelement(2, \"i\", 69);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No trending products available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Check back later for the latest trending items.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 70);\n    i0.ɵɵelement(8, \"i\", 71);\n    i0.ɵɵtext(9, \" Browse Categories \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class TrendingProductsComponent {\n  constructor(recommendationService, authService, cartService, wishlistService, router) {\n    this.recommendationService = recommendationService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.title = 'Trending Now';\n    this.subtitle = 'What\\'s hot in fashion right now';\n    this.limit = 6;\n    this.showViewAll = true;\n    this.trendingProducts = [];\n    this.isLoading = true;\n    this.error = null;\n    this.currentUser = null;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.subscriptions.push(this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.loadTrendingProducts();\n    }));\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadTrendingProducts() {\n    this.isLoading = true;\n    this.error = null;\n    this.subscriptions.push(this.recommendationService.getTrendingProducts(this.category, this.limit).subscribe({\n      next: products => {\n        this.trendingProducts = products;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading trending products:', error);\n        this.error = 'Failed to load trending products';\n        this.isLoading = false;\n        // Load fallback products\n        this.loadFallbackProducts();\n      }\n    }));\n  }\n  loadFallbackProducts() {\n    // Fallback trending products when API fails\n    this.trendingProducts = [{\n      _id: 'trending-1',\n      name: 'Viral Summer Dress',\n      description: 'This dress is trending across social media platforms',\n      price: 2499,\n      originalPrice: 3499,\n      discount: 29,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        alt: 'Summer Dress',\n        isPrimary: true\n      }],\n      category: 'women',\n      subcategory: 'dresses',\n      brand: 'StyleHub',\n      rating: {\n        average: 4.5,\n        count: 89\n      },\n      tags: ['summer', 'trending', 'viral'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.92,\n      trendingReason: 'Viral on social media',\n      viewCount: 15420,\n      purchaseCount: 342,\n      shareCount: 1250,\n      engagementRate: 8.7\n    }, {\n      _id: 'trending-2',\n      name: 'Streetwear Hoodie',\n      description: 'Urban hoodie that\\'s taking the streets by storm',\n      price: 1899,\n      originalPrice: 2499,\n      discount: 24,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400',\n        alt: 'Streetwear Hoodie',\n        isPrimary: true\n      }],\n      category: 'men',\n      subcategory: 'hoodies',\n      brand: 'UrbanEdge',\n      rating: {\n        average: 4.3,\n        count: 156\n      },\n      tags: ['streetwear', 'urban', 'trending'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.88,\n      trendingReason: 'Celebrity endorsed',\n      viewCount: 12300,\n      purchaseCount: 287,\n      shareCount: 890,\n      engagementRate: 7.9\n    }, {\n      _id: 'trending-3',\n      name: 'Sustainable Sneakers',\n      description: 'Eco-friendly sneakers made from recycled materials',\n      price: 3999,\n      originalPrice: 4999,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n        alt: 'Sustainable Sneakers',\n        isPrimary: true\n      }],\n      category: 'footwear',\n      subcategory: 'sneakers',\n      brand: 'EcoStep',\n      rating: {\n        average: 4.6,\n        count: 203\n      },\n      tags: ['sustainable', 'eco-friendly', 'trending'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.85,\n      trendingReason: 'Sustainability trend',\n      viewCount: 9800,\n      purchaseCount: 198,\n      shareCount: 567,\n      engagementRate: 9.2\n    }, {\n      _id: 'trending-4',\n      name: 'Minimalist Watch',\n      description: 'Clean, minimalist design that\\'s perfect for any occasion',\n      price: 5999,\n      originalPrice: 7999,\n      discount: 25,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400',\n        alt: 'Minimalist Watch',\n        isPrimary: true\n      }],\n      category: 'accessories',\n      subcategory: 'watches',\n      brand: 'MinimalTime',\n      rating: {\n        average: 4.7,\n        count: 124\n      },\n      tags: ['minimalist', 'elegant', 'trending'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.83,\n      trendingReason: 'Minimalist trend',\n      viewCount: 8900,\n      purchaseCount: 167,\n      shareCount: 445,\n      engagementRate: 8.1\n    }].slice(0, this.limit);\n  }\n  onProductClick(product) {\n    // Track product view\n    if (this.currentUser) {\n      this.recommendationService.trackProductView(product._id, product.category, 0).subscribe();\n    }\n    // Navigate to product detail\n    this.router.navigate(['/product', product._id]);\n  }\n  addToCart(product, event) {\n    event.stopPropagation();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    // Simplified cart addition for now\n    console.log('Added to cart:', product._id);\n    // Show success message\n  }\n  addToWishlist(product, event) {\n    event.stopPropagation();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: response => {\n        console.log('Added to wishlist:', response);\n        // Show success message\n      },\n      error: error => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n  viewAllTrending() {\n    this.router.navigate(['/recommendations/trending']);\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return product.discount || 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  getStarArray(rating) {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    for (let i = 0; i < 5; i++) {\n      if (i < fullStars) {\n        stars.push(true);\n      } else if (i === fullStars && hasHalfStar) {\n        stars.push(true); // For simplicity, treating half stars as full\n      } else {\n        stars.push(false);\n      }\n    }\n    return stars;\n  }\n  getTrendingIcon(reason) {\n    const reasonLower = reason.toLowerCase();\n    if (reasonLower.includes('viral') || reasonLower.includes('social')) {\n      return 'fas fa-fire';\n    } else if (reasonLower.includes('celebrity')) {\n      return 'fas fa-star';\n    } else if (reasonLower.includes('sustainable') || reasonLower.includes('eco')) {\n      return 'fas fa-leaf';\n    } else if (reasonLower.includes('minimalist')) {\n      return 'fas fa-circle';\n    }\n    return 'fas fa-trending-up';\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  static {\n    this.ɵfac = function TrendingProductsComponent_Factory(t) {\n      return new (t || TrendingProductsComponent)(i0.ɵɵdirectiveInject(i1.RecommendationService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingProductsComponent,\n      selectors: [[\"app-trending-products\"]],\n      inputs: {\n        title: \"title\",\n        subtitle: \"subtitle\",\n        limit: \"limit\",\n        category: \"category\",\n        showViewAll: \"showViewAll\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 7,\n      consts: [[1, \"trending-products-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-fire\", \"trending-icon\"], [1, \"section-subtitle\"], [\"class\", \"view-all-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"product-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-skeleton\"], [1, \"skeleton-image\"], [1, \"skeleton-content\"], [1, \"skeleton-title\"], [1, \"skeleton-price\"], [1, \"skeleton-stats\"], [1, \"error-container\"], [1, \"error-content\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"products-container\"], [1, \"products-grid\"], [\"class\", \"trending-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"trending-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"trending-score\"], [1, \"fas\", \"fa-chart-line\"], [1, \"quick-actions\"], [\"title\", \"Add to Wishlist\", 1, \"quick-action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [\"title\", \"Add to Cart\", 1, \"quick-action-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"trending-stats\"], [1, \"stat-item\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"fas\", \"fa-share\"], [1, \"engagement-rate\"], [1, \"engagement-bar\"], [1, \"engagement-fill\"], [1, \"engagement-text\"], [1, \"product-tags\"], [\"class\", \"tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"discount-badge\"], [1, \"fas\", \"fa-star\"], [1, \"original-price\"], [1, \"tag\"], [1, \"empty-container\"], [1, \"empty-content\"], [1, \"fas\", \"fa-fire\"], [\"routerLink\", \"/categories\", 1, \"browse-btn\"], [1, \"fas\", \"fa-th-large\"]],\n      template: function TrendingProductsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, TrendingProductsComponent_button_8_Template, 3, 0, \"button\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, TrendingProductsComponent_div_9_Template, 3, 2, \"div\", 7)(10, TrendingProductsComponent_div_10_Template, 10, 1, \"div\", 8)(11, TrendingProductsComponent_div_11_Template, 3, 2, \"div\", 9)(12, TrendingProductsComponent_div_12_Template, 10, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.title, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.subtitle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showViewAll && ctx.trendingProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, i6.DecimalPipe, RouterModule, i5.RouterLink],\n      styles: [\".trending-products-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #f0f0f0;\\n  margin-bottom: 32px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-end;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 2px solid #f8f9fa;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.trending-icon[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);\\n}\\n.view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(4px);\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\n.loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n\\n.product-skeleton[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_pulse 1.5s ease-in-out infinite;\\n}\\n\\n.skeleton-image[_ngcontent-%COMP%] {\\n  height: 220px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.skeleton-title[_ngcontent-%COMP%], .skeleton-price[_ngcontent-%COMP%], .skeleton-stats[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n  border-radius: 4px;\\n  margin-bottom: 12px;\\n}\\n\\n.skeleton-title[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n.skeleton-price[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n\\n.skeleton-stats[_ngcontent-%COMP%] {\\n  width: 90%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  color: #666;\\n}\\n\\n.error-content[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n  margin: 0 auto;\\n}\\n\\n.error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n\\n.error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #333;\\n  margin: 0 0 12px 0;\\n  font-weight: 600;\\n}\\n\\n.error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0 0 24px 0;\\n  line-height: 1.5;\\n}\\n\\n.retry-btn[_ngcontent-%COMP%], .browse-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.retry-btn[_ngcontent-%COMP%]:hover, .browse-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);\\n}\\n\\n.products-container[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 24px;\\n}\\n\\n.trending-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #f0f0f0;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.trending-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);\\n  border-color: #ff6b6b;\\n}\\n.trending-card[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n.trending-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 240px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  color: white;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  font-size: 11px;\\n  font-weight: 700;\\n  z-index: 2;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  max-width: 140px;\\n}\\n.trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.trending-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 700;\\n  z-index: 2;\\n}\\n\\n.trending-score[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  left: 12px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 15px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  z-index: 2;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.trending-score[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  display: flex;\\n  gap: 8px;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n\\n.quick-action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 16px;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.quick-action-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 107, 107, 0.9);\\n  color: white;\\n}\\n.quick-action-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: #ff6b6b;\\n  transform: scale(1.1);\\n}\\n.quick-action-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: rgba(84, 160, 255, 0.9);\\n  color: white;\\n}\\n.quick-action-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  background: #54a0ff;\\n  transform: scale(1.1);\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #999;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 8px;\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 12px 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n\\n.product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n\\n.stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n\\n.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ddd;\\n}\\n.stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n.rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.product-pricing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 12px;\\n  padding: 12px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 11px;\\n  color: #666;\\n}\\n.stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ff6b6b;\\n}\\n.stat-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.engagement-rate[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.engagement-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  background: #e0e0e0;\\n  border-radius: 3px;\\n  overflow: hidden;\\n  margin-bottom: 6px;\\n}\\n\\n.engagement-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  border-radius: 3px;\\n  transition: width 0.3s ease;\\n}\\n\\n.engagement-text[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 600;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 6px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background: #f0f0f0;\\n  color: #666;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 1024px) {\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n    gap: 20px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .trending-products-section[_ngcontent-%COMP%] {\\n    padding: 20px 16px;\\n    margin-bottom: 24px;\\n  }\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 16px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .product-image-container[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n  .product-info[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .trending-stats[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .stat-item[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .trending-card[_ngcontent-%COMP%] {\\n    border-radius: 12px;\\n  }\\n  .product-image-container[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .product-info[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .current-price[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .trending-badge[_ngcontent-%COMP%] {\\n    max-width: 100px;\\n    font-size: 10px;\\n    padding: 6px 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TrendingProductsComponent_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "viewAllTrending", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "TrendingProductsComponent_div_9_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "TrendingProductsComponent_div_10_Template_button_click_7_listener", "_r3", "loadTrendingProducts", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r5", "ɵɵclassProp", "star_r6", "formatPrice", "originalPrice", "tag_r7", "TrendingProductsComponent_div_11_div_2_Template_div_click_0_listener", "_r4", "$implicit", "onProductClick", "TrendingProductsComponent_div_11_div_2_div_7_Template", "TrendingProductsComponent_div_11_div_2_Template_button_click_13_listener", "$event", "addToWishlist", "TrendingProductsComponent_div_11_div_2_Template_button_click_15_listener", "addToCart", "TrendingProductsComponent_div_11_div_2_i_24_Template", "TrendingProductsComponent_div_11_div_2_span_30_Template", "TrendingProductsComponent_div_11_div_2_span_50_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "ɵɵclassMap", "getTrendingIcon", "trendingReason", "ɵɵpipeBind2", "trendingScore", "brand", "getStarArray", "rating", "average", "ɵɵtextInterpolate2", "count", "price", "formatNumber", "viewCount", "purchaseCount", "shareCount", "ɵɵstyleProp", "engagementRate", "tags", "slice", "TrendingProductsComponent_div_11_div_2_Template", "trendingProducts", "trackByProductId", "TrendingProductsComponent", "constructor", "recommendationService", "authService", "cartService", "wishlistService", "router", "title", "subtitle", "limit", "showViewAll", "isLoading", "currentUser", "subscriptions", "ngOnInit", "push", "currentUser$", "subscribe", "user", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "getTrendingProducts", "category", "next", "products", "console", "loadFallbackProducts", "_id", "description", "discount", "isPrimary", "subcategory", "isActive", "isFeatured", "product", "trackProductView", "navigate", "event", "stopPropagation", "log", "response", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "num", "toFixed", "toString", "stars", "fullStars", "floor", "hasHalfStar", "i", "reason", "reason<PERSON><PERSON><PERSON>", "toLowerCase", "includes", "index", "ɵɵdirectiveInject", "i1", "RecommendationService", "i2", "AuthService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingProductsComponent_Template", "rf", "ctx", "TrendingProductsComponent_button_8_Template", "TrendingProductsComponent_div_9_Template", "TrendingProductsComponent_div_10_Template", "TrendingProductsComponent_div_11_Template", "TrendingProductsComponent_div_12_Template", "length", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "RouterLink", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\nimport { RecommendationService, TrendingProduct } from '../../../../core/services/recommendation.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-trending-products',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './trending-products.component.html',\n  styleUrls: ['./trending-products.component.scss']\n})\nexport class TrendingProductsComponent implements OnInit, OnDestroy {\n  @Input() title: string = 'Trending Now';\n  @Input() subtitle: string = 'What\\'s hot in fashion right now';\n  @Input() limit: number = 6;\n  @Input() category?: string;\n  @Input() showViewAll: boolean = true;\n\n  trendingProducts: TrendingProduct[] = [];\n  isLoading = true;\n  error: string | null = null;\n  currentUser: any = null;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private recommendationService: RecommendationService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to current user\n    this.subscriptions.push(\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        this.loadTrendingProducts();\n      })\n    );\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  loadTrendingProducts() {\n    this.isLoading = true;\n    this.error = null;\n\n    this.subscriptions.push(\n      this.recommendationService.getTrendingProducts(this.category, this.limit).subscribe({\n        next: (products) => {\n          this.trendingProducts = products;\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading trending products:', error);\n          this.error = 'Failed to load trending products';\n          this.isLoading = false;\n          // Load fallback products\n          this.loadFallbackProducts();\n        }\n      })\n    );\n  }\n\n  loadFallbackProducts() {\n    // Fallback trending products when API fails\n    this.trendingProducts = [\n      {\n        _id: 'trending-1',\n        name: 'Viral Summer Dress',\n        description: 'This dress is trending across social media platforms',\n        price: 2499,\n        originalPrice: 3499,\n        discount: 29,\n        images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400', alt: 'Summer Dress', isPrimary: true }],\n        category: 'women',\n        subcategory: 'dresses',\n        brand: 'StyleHub',\n        rating: { average: 4.5, count: 89 },\n        tags: ['summer', 'trending', 'viral'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.92,\n        trendingReason: 'Viral on social media',\n        viewCount: 15420,\n        purchaseCount: 342,\n        shareCount: 1250,\n        engagementRate: 8.7\n      },\n      {\n        _id: 'trending-2',\n        name: 'Streetwear Hoodie',\n        description: 'Urban hoodie that\\'s taking the streets by storm',\n        price: 1899,\n        originalPrice: 2499,\n        discount: 24,\n        images: [{ url: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400', alt: 'Streetwear Hoodie', isPrimary: true }],\n        category: 'men',\n        subcategory: 'hoodies',\n        brand: 'UrbanEdge',\n        rating: { average: 4.3, count: 156 },\n        tags: ['streetwear', 'urban', 'trending'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.88,\n        trendingReason: 'Celebrity endorsed',\n        viewCount: 12300,\n        purchaseCount: 287,\n        shareCount: 890,\n        engagementRate: 7.9\n      },\n      {\n        _id: 'trending-3',\n        name: 'Sustainable Sneakers',\n        description: 'Eco-friendly sneakers made from recycled materials',\n        price: 3999,\n        originalPrice: 4999,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400', alt: 'Sustainable Sneakers', isPrimary: true }],\n        category: 'footwear',\n        subcategory: 'sneakers',\n        brand: 'EcoStep',\n        rating: { average: 4.6, count: 203 },\n        tags: ['sustainable', 'eco-friendly', 'trending'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.85,\n        trendingReason: 'Sustainability trend',\n        viewCount: 9800,\n        purchaseCount: 198,\n        shareCount: 567,\n        engagementRate: 9.2\n      },\n      {\n        _id: 'trending-4',\n        name: 'Minimalist Watch',\n        description: 'Clean, minimalist design that\\'s perfect for any occasion',\n        price: 5999,\n        originalPrice: 7999,\n        discount: 25,\n        images: [{ url: 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400', alt: 'Minimalist Watch', isPrimary: true }],\n        category: 'accessories',\n        subcategory: 'watches',\n        brand: 'MinimalTime',\n        rating: { average: 4.7, count: 124 },\n        tags: ['minimalist', 'elegant', 'trending'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.83,\n        trendingReason: 'Minimalist trend',\n        viewCount: 8900,\n        purchaseCount: 167,\n        shareCount: 445,\n        engagementRate: 8.1\n      }\n    ].slice(0, this.limit);\n  }\n\n  onProductClick(product: TrendingProduct) {\n    // Track product view\n    if (this.currentUser) {\n      this.recommendationService.trackProductView(product._id, product.category, 0).subscribe();\n    }\n    \n    // Navigate to product detail\n    this.router.navigate(['/product', product._id]);\n  }\n\n  addToCart(product: TrendingProduct, event: Event) {\n    event.stopPropagation();\n    \n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    // Simplified cart addition for now\n    console.log('Added to cart:', product._id);\n    // Show success message\n  }\n\n  addToWishlist(product: TrendingProduct, event: Event) {\n    event.stopPropagation();\n    \n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: (response) => {\n        console.log('Added to wishlist:', response);\n        // Show success message\n      },\n      error: (error) => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n\n  viewAllTrending() {\n    this.router.navigate(['/recommendations/trending']);\n  }\n\n  getDiscountPercentage(product: TrendingProduct): number {\n    if (product.originalPrice && product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return product.discount || 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  getStarArray(rating: number): boolean[] {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    \n    for (let i = 0; i < 5; i++) {\n      if (i < fullStars) {\n        stars.push(true);\n      } else if (i === fullStars && hasHalfStar) {\n        stars.push(true); // For simplicity, treating half stars as full\n      } else {\n        stars.push(false);\n      }\n    }\n    return stars;\n  }\n\n  getTrendingIcon(reason: string): string {\n    const reasonLower = reason.toLowerCase();\n    if (reasonLower.includes('viral') || reasonLower.includes('social')) {\n      return 'fas fa-fire';\n    } else if (reasonLower.includes('celebrity')) {\n      return 'fas fa-star';\n    } else if (reasonLower.includes('sustainable') || reasonLower.includes('eco')) {\n      return 'fas fa-leaf';\n    } else if (reasonLower.includes('minimalist')) {\n      return 'fas fa-circle';\n    }\n    return 'fas fa-trending-up';\n  }\n\n  trackByProductId(index: number, product: TrendingProduct): string {\n    return product._id;\n  }\n}\n", "<div class=\"trending-products-section\">\n  <!-- Section Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <i class=\"fas fa-fire trending-icon\"></i>\n        {{ title }}\n      </h2>\n      <p class=\"section-subtitle\">{{ subtitle }}</p>\n    </div>\n    <button \n      *ngIf=\"showViewAll && trendingProducts.length > 0\" \n      class=\"view-all-btn\"\n      (click)=\"viewAllTrending()\">\n      View All\n      <i class=\"fas fa-arrow-right\"></i>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"product-skeleton\">\n        <div class=\"skeleton-image\"></div>\n        <div class=\"skeleton-content\">\n          <div class=\"skeleton-title\"></div>\n          <div class=\"skeleton-price\"></div>\n          <div class=\"skeleton-stats\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <div class=\"error-content\">\n      <i class=\"fas fa-exclamation-triangle\"></i>\n      <h3>Unable to load trending products</h3>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"loadTrendingProducts()\">\n        <i class=\"fas fa-redo\"></i>\n        Try Again\n      </button>\n    </div>\n  </div>\n\n  <!-- Products Grid -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length > 0\" class=\"products-container\">\n    <div class=\"products-grid\">\n      <div \n        *ngFor=\"let product of trendingProducts; trackBy: trackByProductId\" \n        class=\"trending-card\"\n        (click)=\"onProductClick(product)\">\n        \n        <!-- Product Image -->\n        <div class=\"product-image-container\">\n          <img\n            [src]=\"product.images[0].url\"\n            [alt]=\"product.images[0].alt || product.name\"\n            class=\"product-image\"\n            loading=\"lazy\">\n          \n          <!-- Trending Badge -->\n          <div class=\"trending-badge\">\n            <i [class]=\"getTrendingIcon(product.trendingReason)\"></i>\n            <span>{{ product.trendingReason }}</span>\n          </div>\n\n          <!-- Discount Badge -->\n          <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n            {{ getDiscountPercentage(product) }}% OFF\n          </div>\n\n          <!-- Trending Score -->\n          <div class=\"trending-score\">\n            <i class=\"fas fa-chart-line\"></i>\n            {{ (product.trendingScore * 100) | number:'1.0-0' }}%\n          </div>\n\n          <!-- Quick Actions -->\n          <div class=\"quick-actions\">\n            <button \n              class=\"quick-action-btn wishlist-btn\"\n              (click)=\"addToWishlist(product, $event)\"\n              title=\"Add to Wishlist\">\n              <i class=\"fas fa-heart\"></i>\n            </button>\n            <button \n              class=\"quick-action-btn cart-btn\"\n              (click)=\"addToCart(product, $event)\"\n              title=\"Add to Cart\">\n              <i class=\"fas fa-shopping-cart\"></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Product Info -->\n        <div class=\"product-info\">\n          <!-- Brand -->\n          <div class=\"product-brand\">{{ product.brand }}</div>\n          \n          <!-- Name -->\n          <h3 class=\"product-name\">{{ product.name }}</h3>\n          \n          <!-- Rating -->\n          <div class=\"product-rating\">\n            <div class=\"stars\">\n              <i \n                *ngFor=\"let star of getStarArray(product.rating.average)\" \n                class=\"fas fa-star\"\n                [class.filled]=\"star\">\n              </i>\n            </div>\n            <span class=\"rating-text\">{{ product.rating.average }} ({{ product.rating.count }})</span>\n          </div>\n\n          <!-- Price -->\n          <div class=\"product-pricing\">\n            <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n            <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                  class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n          </div>\n\n          <!-- Trending Stats -->\n          <div class=\"trending-stats\">\n            <div class=\"stat-item\">\n              <i class=\"fas fa-eye\"></i>\n              <span>{{ formatNumber(product.viewCount) }} views</span>\n            </div>\n            <div class=\"stat-item\">\n              <i class=\"fas fa-shopping-bag\"></i>\n              <span>{{ formatNumber(product.purchaseCount) }} sold</span>\n            </div>\n            <div class=\"stat-item\">\n              <i class=\"fas fa-share\"></i>\n              <span>{{ formatNumber(product.shareCount) }} shares</span>\n            </div>\n          </div>\n\n          <!-- Engagement Rate -->\n          <div class=\"engagement-rate\">\n            <div class=\"engagement-bar\">\n              <div class=\"engagement-fill\" [style.width.%]=\"product.engagementRate\"></div>\n            </div>\n            <span class=\"engagement-text\">{{ product.engagementRate }}% engagement</span>\n          </div>\n\n          <!-- Tags -->\n          <div class=\"product-tags\">\n            <span *ngFor=\"let tag of product.tags.slice(0, 3)\" class=\"tag\">{{ tag }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length === 0\" class=\"empty-container\">\n    <div class=\"empty-content\">\n      <i class=\"fas fa-fire\"></i>\n      <h3>No trending products available</h3>\n      <p>Check back later for the latest trending items.</p>\n      <button class=\"browse-btn\" routerLink=\"/categories\">\n        <i class=\"fas fa-th-large\"></i>\n        Browse Categories\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;;;;;;;;;;;;ICQlDC,EAAA,CAAAC,cAAA,iBAG8B;IAA5BD,EAAA,CAAAE,UAAA,mBAAAC,oEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAC3BT,EAAA,CAAAU,MAAA,iBACA;IAAAV,EAAA,CAAAW,SAAA,YAAkC;IACpCX,EAAA,CAAAY,YAAA,EAAS;;;;;IAMPZ,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAW,SAAA,cAAkC;IAClCX,EAAA,CAAAC,cAAA,cAA8B;IAG5BD,EAFA,CAAAW,SAAA,cAAkC,cACA,cACA;IAEtCX,EADE,CAAAY,YAAA,EAAM,EACF;;;;;IARRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAa,UAAA,IAAAC,8CAAA,kBAAiE;IASrEd,EADE,CAAAY,YAAA,EAAM,EACF;;;IAToBZ,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAgB;;;;;;IAaxClB,EADF,CAAAC,cAAA,cAAyD,cAC5B;IACzBD,EAAA,CAAAW,SAAA,YAA2C;IAC3CX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,uCAAgC;IAAAV,EAAA,CAAAY,YAAA,EAAK;IACzCZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAY,YAAA,EAAI;IAClBZ,EAAA,CAAAC,cAAA,iBAA2D;IAAjCD,EAAA,CAAAE,UAAA,mBAAAiB,kEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,oBAAA,EAAsB;IAAA,EAAC;IACxDrB,EAAA,CAAAW,SAAA,YAA2B;IAC3BX,EAAA,CAAAU,MAAA,kBACF;IAEJV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;;;;IANCZ,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,KAAA,CAAW;;;;;IA+BVvB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IADJZ,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAmB,qBAAA,CAAAC,UAAA,YACF;;;;;IAoCI1B,EAAA,CAAAW,SAAA,YAII;;;;IADFX,EAAA,CAAA2B,WAAA,WAAAC,OAAA,CAAqB;;;;;IASzB5B,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAU,MAAA,GAAwC;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;;IAA/CZ,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAuB,WAAA,CAAAH,UAAA,CAAAI,aAAA,EAAwC;;;;;IA6BrE9B,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAU,MAAA,GAAS;IAAAV,EAAA,CAAAY,YAAA,EAAO;;;;IAAhBZ,EAAA,CAAAe,SAAA,EAAS;IAATf,EAAA,CAAAsB,iBAAA,CAAAS,MAAA,CAAS;;;;;;IApG9E/B,EAAA,CAAAC,cAAA,cAGoC;IAAlCD,EAAA,CAAAE,UAAA,mBAAA8B,qEAAA;MAAA,MAAAN,UAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAC,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6B,cAAA,CAAAT,UAAA,CAAuB;IAAA,EAAC;IAGjC1B,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAW,SAAA,cAIiB;IAGjBX,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAW,SAAA,QAAyD;IACzDX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAA4B;IACpCV,EADoC,CAAAY,YAAA,EAAO,EACrC;IAGNZ,EAAA,CAAAa,UAAA,IAAAuB,qDAAA,kBAAuE;IAKvEpC,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAW,SAAA,YAAiC;IACjCX,EAAA,CAAAU,MAAA,IACF;;IAAAV,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAA2B,kBAIC;IADxBD,EAAA,CAAAE,UAAA,mBAAAmC,yEAAAC,MAAA;MAAA,MAAAZ,UAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAC,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiC,aAAA,CAAAb,UAAA,EAAAY,MAAA,CAA8B;IAAA,EAAC;IAExCtC,EAAA,CAAAW,SAAA,aAA4B;IAC9BX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAGsB;IADpBD,EAAA,CAAAE,UAAA,mBAAAsC,yEAAAF,MAAA;MAAA,MAAAZ,UAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAC,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmC,SAAA,CAAAf,UAAA,EAAAY,MAAA,CAA0B;IAAA,EAAC;IAEpCtC,EAAA,CAAAW,SAAA,aAAoC;IAG1CX,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAKJZ,EAFF,CAAAC,cAAA,eAA0B,eAEG;IAAAD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAY,YAAA,EAAM;IAGpDZ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAI9CZ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAAa,UAAA,KAAA6B,oDAAA,gBAGwB;IAE1B1C,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAyD;IACrFV,EADqF,CAAAY,YAAA,EAAO,EACtF;IAIJZ,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAY,YAAA,EAAO;IACnEZ,EAAA,CAAAa,UAAA,KAAA8B,uDAAA,mBAC6B;IAC/B3C,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAA4B,eACH;IACrBD,EAAA,CAAAW,SAAA,aAA0B;IAC1BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA2C;IACnDV,EADmD,CAAAY,YAAA,EAAO,EACpD;IACNZ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAW,SAAA,aAAmC;IACnCX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA8C;IACtDV,EADsD,CAAAY,YAAA,EAAO,EACvD;IACNZ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAW,SAAA,aAA4B;IAC5BX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA6C;IAEvDV,EAFuD,CAAAY,YAAA,EAAO,EACtD,EACF;IAIJZ,EADF,CAAAC,cAAA,eAA6B,eACC;IAC1BD,EAAA,CAAAW,SAAA,eAA4E;IAC9EX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAU,MAAA,IAAwC;IACxEV,EADwE,CAAAY,YAAA,EAAO,EACzE;IAGNZ,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAa,UAAA,KAAA+B,uDAAA,mBAA+D;IAGrE5C,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;;IA/FAZ,EAAA,CAAAe,SAAA,GAA6B;IAC7Bf,EADA,CAAAgB,UAAA,QAAAU,UAAA,CAAAmB,MAAA,IAAAC,GAAA,EAAA9C,EAAA,CAAA+C,aAAA,CAA6B,QAAArB,UAAA,CAAAmB,MAAA,IAAAG,GAAA,IAAAtB,UAAA,CAAAuB,IAAA,CACgB;IAM1CjD,EAAA,CAAAe,SAAA,GAAiD;IAAjDf,EAAA,CAAAkD,UAAA,CAAA5C,MAAA,CAAA6C,eAAA,CAAAzB,UAAA,CAAA0B,cAAA,EAAiD;IAC9CpD,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAsB,iBAAA,CAAAI,UAAA,CAAA0B,cAAA,CAA4B;IAI9BpD,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAmB,qBAAA,CAAAC,UAAA,MAAwC;IAO5C1B,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAxB,EAAA,CAAAqD,WAAA,SAAA3B,UAAA,CAAA4B,aAAA,uBACF;IAsB2BtD,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAsB,iBAAA,CAAAI,UAAA,CAAA6B,KAAA,CAAmB;IAGrBvD,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAsB,iBAAA,CAAAI,UAAA,CAAAuB,IAAA,CAAkB;IAMpBjD,EAAA,CAAAe,SAAA,GAAuC;IAAvCf,EAAA,CAAAgB,UAAA,YAAAV,MAAA,CAAAkD,YAAA,CAAA9B,UAAA,CAAA+B,MAAA,CAAAC,OAAA,EAAuC;IAKlC1D,EAAA,CAAAe,SAAA,GAAyD;IAAzDf,EAAA,CAAA2D,kBAAA,KAAAjC,UAAA,CAAA+B,MAAA,CAAAC,OAAA,QAAAhC,UAAA,CAAA+B,MAAA,CAAAG,KAAA,MAAyD;IAKvD5D,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAuB,WAAA,CAAAH,UAAA,CAAAmC,KAAA,EAAgC;IACrD7D,EAAA,CAAAe,SAAA,EAAoE;IAApEf,EAAA,CAAAgB,UAAA,SAAAU,UAAA,CAAAI,aAAA,IAAAJ,UAAA,CAAAI,aAAA,GAAAJ,UAAA,CAAAmC,KAAA,CAAoE;IAQnE7D,EAAA,CAAAe,SAAA,GAA2C;IAA3Cf,EAAA,CAAAwB,kBAAA,KAAAlB,MAAA,CAAAwD,YAAA,CAAApC,UAAA,CAAAqC,SAAA,YAA2C;IAI3C/D,EAAA,CAAAe,SAAA,GAA8C;IAA9Cf,EAAA,CAAAwB,kBAAA,KAAAlB,MAAA,CAAAwD,YAAA,CAAApC,UAAA,CAAAsC,aAAA,WAA8C;IAI9ChE,EAAA,CAAAe,SAAA,GAA6C;IAA7Cf,EAAA,CAAAwB,kBAAA,KAAAlB,MAAA,CAAAwD,YAAA,CAAApC,UAAA,CAAAuC,UAAA,aAA6C;IAOtBjE,EAAA,CAAAe,SAAA,GAAwC;IAAxCf,EAAA,CAAAkE,WAAA,UAAAxC,UAAA,CAAAyC,cAAA,MAAwC;IAEzCnE,EAAA,CAAAe,SAAA,GAAwC;IAAxCf,EAAA,CAAAwB,kBAAA,KAAAE,UAAA,CAAAyC,cAAA,iBAAwC;IAKhDnE,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,YAAAU,UAAA,CAAA0C,IAAA,CAAAC,KAAA,OAA2B;;;;;IArGzDrE,EADF,CAAAC,cAAA,cAA4F,cAC/D;IACzBD,EAAA,CAAAa,UAAA,IAAAyD,+CAAA,oBAGoC;IAsGxCtE,EADE,CAAAY,YAAA,EAAM,EACF;;;;IAxGoBZ,EAAA,CAAAe,SAAA,GAAqB;IAAAf,EAArB,CAAAgB,UAAA,YAAAV,MAAA,CAAAiE,gBAAA,CAAqB,iBAAAjE,MAAA,CAAAkE,gBAAA,CAAyB;;;;;IA4GtExE,EADF,CAAAC,cAAA,cAA2F,cAC9D;IACzBD,EAAA,CAAAW,SAAA,YAA2B;IAC3BX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,qCAA8B;IAAAV,EAAA,CAAAY,YAAA,EAAK;IACvCZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,sDAA+C;IAAAV,EAAA,CAAAY,YAAA,EAAI;IACtDZ,EAAA,CAAAC,cAAA,iBAAoD;IAClDD,EAAA,CAAAW,SAAA,YAA+B;IAC/BX,EAAA,CAAAU,MAAA,0BACF;IAEJV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;;;ADtJR,OAAM,MAAO6D,yBAAyB;EAcpCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAlBP,KAAAC,KAAK,GAAW,cAAc;IAC9B,KAAAC,QAAQ,GAAW,kCAAkC;IACrD,KAAAC,KAAK,GAAW,CAAC;IAEjB,KAAAC,WAAW,GAAY,IAAI;IAEpC,KAAAZ,gBAAgB,GAAsB,EAAE;IACxC,KAAAa,SAAS,GAAG,IAAI;IAChB,KAAA7D,KAAK,GAAkB,IAAI;IAC3B,KAAA8D,WAAW,GAAQ,IAAI;IAEf,KAAAC,aAAa,GAAmB,EAAE;EAQvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,aAAa,CAACE,IAAI,CACrB,IAAI,CAACZ,WAAW,CAACa,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACN,WAAW,GAAGM,IAAI;MACvB,IAAI,CAACtE,oBAAoB,EAAE;IAC7B,CAAC,CAAC,CACH;EACH;EAEAuE,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA1E,oBAAoBA,CAAA;IAClB,IAAI,CAAC+D,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC7D,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC+D,aAAa,CAACE,IAAI,CACrB,IAAI,CAACb,qBAAqB,CAACqB,mBAAmB,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACf,KAAK,CAAC,CAACQ,SAAS,CAAC;MAClFQ,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC5B,gBAAgB,GAAG4B,QAAQ;QAChC,IAAI,CAACf,SAAS,GAAG,KAAK;MACxB,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACf6E,OAAO,CAAC7E,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACA,KAAK,GAAG,kCAAkC;QAC/C,IAAI,CAAC6D,SAAS,GAAG,KAAK;QACtB;QACA,IAAI,CAACiB,oBAAoB,EAAE;MAC7B;KACD,CAAC,CACH;EACH;EAEAA,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAAC9B,gBAAgB,GAAG,CACtB;MACE+B,GAAG,EAAE,YAAY;MACjBrD,IAAI,EAAE,oBAAoB;MAC1BsD,WAAW,EAAE,sDAAsD;MACnE1C,KAAK,EAAE,IAAI;MACX/B,aAAa,EAAE,IAAI;MACnB0E,QAAQ,EAAE,EAAE;MACZ3D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEE,GAAG,EAAE,cAAc;QAAEyD,SAAS,EAAE;MAAI,CAAE,CAAC;MAC7HR,QAAQ,EAAE,OAAO;MACjBS,WAAW,EAAE,SAAS;MACtBnD,KAAK,EAAE,UAAU;MACjBE,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEE,KAAK,EAAE;MAAE,CAAE;MACnCQ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;MACrCuC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBtD,aAAa,EAAE,IAAI;MACnBF,cAAc,EAAE,uBAAuB;MACvCW,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,IAAI;MAChBE,cAAc,EAAE;KACjB,EACD;MACEmC,GAAG,EAAE,YAAY;MACjBrD,IAAI,EAAE,mBAAmB;MACzBsD,WAAW,EAAE,kDAAkD;MAC/D1C,KAAK,EAAE,IAAI;MACX/B,aAAa,EAAE,IAAI;MACnB0E,QAAQ,EAAE,EAAE;MACZ3D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,iEAAiE;QAAEE,GAAG,EAAE,mBAAmB;QAAEyD,SAAS,EAAE;MAAI,CAAE,CAAC;MAC/HR,QAAQ,EAAE,KAAK;MACfS,WAAW,EAAE,SAAS;MACtBnD,KAAK,EAAE,WAAW;MAClBE,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEE,KAAK,EAAE;MAAG,CAAE;MACpCQ,IAAI,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,CAAC;MACzCuC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBtD,aAAa,EAAE,IAAI;MACnBF,cAAc,EAAE,oBAAoB;MACpCW,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,GAAG;MACfE,cAAc,EAAE;KACjB,EACD;MACEmC,GAAG,EAAE,YAAY;MACjBrD,IAAI,EAAE,sBAAsB;MAC5BsD,WAAW,EAAE,oDAAoD;MACjE1C,KAAK,EAAE,IAAI;MACX/B,aAAa,EAAE,IAAI;MACnB0E,QAAQ,EAAE,EAAE;MACZ3D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,iEAAiE;QAAEE,GAAG,EAAE,sBAAsB;QAAEyD,SAAS,EAAE;MAAI,CAAE,CAAC;MAClIR,QAAQ,EAAE,UAAU;MACpBS,WAAW,EAAE,UAAU;MACvBnD,KAAK,EAAE,SAAS;MAChBE,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEE,KAAK,EAAE;MAAG,CAAE;MACpCQ,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,UAAU,CAAC;MACjDuC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBtD,aAAa,EAAE,IAAI;MACnBF,cAAc,EAAE,sBAAsB;MACtCW,SAAS,EAAE,IAAI;MACfC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,GAAG;MACfE,cAAc,EAAE;KACjB,EACD;MACEmC,GAAG,EAAE,YAAY;MACjBrD,IAAI,EAAE,kBAAkB;MACxBsD,WAAW,EAAE,2DAA2D;MACxE1C,KAAK,EAAE,IAAI;MACX/B,aAAa,EAAE,IAAI;MACnB0E,QAAQ,EAAE,EAAE;MACZ3D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEE,GAAG,EAAE,kBAAkB;QAAEyD,SAAS,EAAE;MAAI,CAAE,CAAC;MACjIR,QAAQ,EAAE,aAAa;MACvBS,WAAW,EAAE,SAAS;MACtBnD,KAAK,EAAE,aAAa;MACpBE,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEE,KAAK,EAAE;MAAG,CAAE;MACpCQ,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,UAAU,CAAC;MAC3CuC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBtD,aAAa,EAAE,IAAI;MACnBF,cAAc,EAAE,kBAAkB;MAClCW,SAAS,EAAE,IAAI;MACfC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,GAAG;MACfE,cAAc,EAAE;KACjB,CACF,CAACE,KAAK,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC;EACxB;EAEA/C,cAAcA,CAAC0E,OAAwB;IACrC;IACA,IAAI,IAAI,CAACxB,WAAW,EAAE;MACpB,IAAI,CAACV,qBAAqB,CAACmC,gBAAgB,CAACD,OAAO,CAACP,GAAG,EAAEO,OAAO,CAACZ,QAAQ,EAAE,CAAC,CAAC,CAACP,SAAS,EAAE;;IAG3F;IACA,IAAI,CAACX,MAAM,CAACgC,QAAQ,CAAC,CAAC,UAAU,EAAEF,OAAO,CAACP,GAAG,CAAC,CAAC;EACjD;EAEA7D,SAASA,CAACoE,OAAwB,EAAEG,KAAY;IAC9CA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,CAAC,IAAI,CAAC5B,WAAW,EAAE;MACrB,IAAI,CAACN,MAAM,CAACgC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACAX,OAAO,CAACc,GAAG,CAAC,gBAAgB,EAAEL,OAAO,CAACP,GAAG,CAAC;IAC1C;EACF;EAEA/D,aAAaA,CAACsE,OAAwB,EAAEG,KAAY;IAClDA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,CAAC,IAAI,CAAC5B,WAAW,EAAE;MACrB,IAAI,CAACN,MAAM,CAACgC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF,IAAI,CAACjC,eAAe,CAACvC,aAAa,CAACsE,OAAO,CAACP,GAAG,CAAC,CAACZ,SAAS,CAAC;MACxDQ,IAAI,EAAGiB,QAAQ,IAAI;QACjBf,OAAO,CAACc,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAAC;QAC3C;MACF,CAAC;MACD5F,KAAK,EAAGA,KAAK,IAAI;QACf6E,OAAO,CAAC7E,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEAd,eAAeA,CAAA;IACb,IAAI,CAACsE,MAAM,CAACgC,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACrD;EAEAtF,qBAAqBA,CAACoF,OAAwB;IAC5C,IAAIA,OAAO,CAAC/E,aAAa,IAAI+E,OAAO,CAAChD,KAAK,EAAE;MAC1C,OAAOuD,IAAI,CAACC,KAAK,CAAE,CAACR,OAAO,CAAC/E,aAAa,GAAG+E,OAAO,CAAChD,KAAK,IAAIgD,OAAO,CAAC/E,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO+E,OAAO,CAACL,QAAQ,IAAI,CAAC;EAC9B;EAEA3E,WAAWA,CAACgC,KAAa;IACvB,OAAO,IAAIyD,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAC;EAClB;EAEAC,YAAYA,CAAC8D,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAtE,YAAYA,CAACC,MAAc;IACzB,MAAMsE,KAAK,GAAG,EAAE;IAChB,MAAMC,SAAS,GAAGZ,IAAI,CAACa,KAAK,CAACxE,MAAM,CAAC;IACpC,MAAMyE,WAAW,GAAGzE,MAAM,GAAG,CAAC,KAAK,CAAC;IAEpC,KAAK,IAAI0E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIA,CAAC,GAAGH,SAAS,EAAE;QACjBD,KAAK,CAACvC,IAAI,CAAC,IAAI,CAAC;OACjB,MAAM,IAAI2C,CAAC,KAAKH,SAAS,IAAIE,WAAW,EAAE;QACzCH,KAAK,CAACvC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;OACnB,MAAM;QACLuC,KAAK,CAACvC,IAAI,CAAC,KAAK,CAAC;;;IAGrB,OAAOuC,KAAK;EACd;EAEA5E,eAAeA,CAACiF,MAAc;IAC5B,MAAMC,WAAW,GAAGD,MAAM,CAACE,WAAW,EAAE;IACxC,IAAID,WAAW,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,WAAW,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACnE,OAAO,aAAa;KACrB,MAAM,IAAIF,WAAW,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC5C,OAAO,aAAa;KACrB,MAAM,IAAIF,WAAW,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAIF,WAAW,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7E,OAAO,aAAa;KACrB,MAAM,IAAIF,WAAW,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;MAC7C,OAAO,eAAe;;IAExB,OAAO,oBAAoB;EAC7B;EAEA/D,gBAAgBA,CAACgE,KAAa,EAAE3B,OAAwB;IACtD,OAAOA,OAAO,CAACP,GAAG;EACpB;;;uBA9PW7B,yBAAyB,EAAAzE,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/I,EAAA,CAAAyI,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAjJ,EAAA,CAAAyI,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzB1E,yBAAyB;MAAA2E,SAAA;MAAAC,MAAA;QAAArE,KAAA;QAAAC,QAAA;QAAAC,KAAA;QAAAe,QAAA;QAAAd,WAAA;MAAA;MAAAmE,UAAA;MAAAC,QAAA,GAAAvJ,EAAA,CAAAwJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbhC9J,EAJN,CAAAC,cAAA,aAAuC,aAET,aACE,YACA;UACxBD,EAAA,CAAAW,SAAA,WAAyC;UACzCX,EAAA,CAAAU,MAAA,GACF;UAAAV,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,GAAc;UAC5CV,EAD4C,CAAAY,YAAA,EAAI,EAC1C;UACNZ,EAAA,CAAAa,UAAA,IAAAmJ,2CAAA,oBAG8B;UAIhChK,EAAA,CAAAY,YAAA,EAAM;UA4INZ,EAzIA,CAAAa,UAAA,IAAAoJ,wCAAA,iBAAiD,KAAAC,yCAAA,kBAcQ,KAAAC,yCAAA,iBAamC,KAAAC,yCAAA,mBA8GD;UAW7FpK,EAAA,CAAAY,YAAA,EAAM;;;UAlKEZ,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAwB,kBAAA,MAAAuI,GAAA,CAAA/E,KAAA,MACF;UAC4BhF,EAAA,CAAAe,SAAA,GAAc;UAAdf,EAAA,CAAAsB,iBAAA,CAAAyI,GAAA,CAAA9E,QAAA,CAAc;UAGzCjF,EAAA,CAAAe,SAAA,EAAgD;UAAhDf,EAAA,CAAAgB,UAAA,SAAA+I,GAAA,CAAA5E,WAAA,IAAA4E,GAAA,CAAAxF,gBAAA,CAAA8F,MAAA,KAAgD;UAS/CrK,EAAA,CAAAe,SAAA,EAAe;UAAff,EAAA,CAAAgB,UAAA,SAAA+I,GAAA,CAAA3E,SAAA,CAAe;UAcfpF,EAAA,CAAAe,SAAA,EAAyB;UAAzBf,EAAA,CAAAgB,UAAA,SAAA+I,GAAA,CAAAxI,KAAA,KAAAwI,GAAA,CAAA3E,SAAA,CAAyB;UAazBpF,EAAA,CAAAe,SAAA,EAAyD;UAAzDf,EAAA,CAAAgB,UAAA,UAAA+I,GAAA,CAAA3E,SAAA,KAAA2E,GAAA,CAAAxI,KAAA,IAAAwI,GAAA,CAAAxF,gBAAA,CAAA8F,MAAA,KAAyD;UA8GzDrK,EAAA,CAAAe,SAAA,EAA2D;UAA3Df,EAAA,CAAAgB,UAAA,UAAA+I,GAAA,CAAA3E,SAAA,KAAA2E,GAAA,CAAAxI,KAAA,IAAAwI,GAAA,CAAAxF,gBAAA,CAAA8F,MAAA,OAA2D;;;qBDhJvDvK,YAAY,EAAAwK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE1K,YAAY,EAAAmJ,EAAA,CAAAwB,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}