import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';

declare var Razorpay: any;

export interface PaymentOrder {
  id: string;
  amount: number;
  currency: string;
  receipt: string;
  status: string;
}

export interface PaymentOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  order_id: string;
  handler: (response: any) => void;
  prefill: {
    name: string;
    email: string;
    contact: string;
  };
  theme: {
    color: string;
  };
  modal: {
    ondismiss: () => void;
  };
}

export interface PaymentVerification {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
}

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private apiUrl = environment.apiUrl;
  private paymentStatusSubject = new BehaviorSubject<string>('idle');
  public paymentStatus$ = this.paymentStatusSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadRazorpayScript();
  }

  private loadRazorpayScript(): Promise<boolean> {
    return new Promise((resolve) => {
      if (typeof Razorpay !== 'undefined') {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.head.appendChild(script);
    });
  }

  createOrder(amount: number, currency: string = 'INR'): Observable<PaymentOrder> {
    const orderData = {
      amount: amount * 100, // Convert to paise
      currency: currency
    };
    return this.http.post<PaymentOrder>(`${this.apiUrl}/payment/create-order`, orderData);
  }

  verifyPayment(paymentData: PaymentVerification): Observable<any> {
    return this.http.post(`${this.apiUrl}/payment/verify`, paymentData);
  }

  async initiatePayment(orderData: any, userDetails: any): Promise<void> {
    try {
      this.paymentStatusSubject.next('processing');
      
      // Create order on backend
      const order = await this.createOrder(orderData.amount).toPromise();
      
      if (!order) {
        throw new Error('Failed to create order');
      }

      // Prepare Razorpay options
      const options: PaymentOptions = {
        key: environment.razorpayKeyId,
        amount: order.amount,
        currency: order.currency,
        name: 'DFashion',
        description: 'Fashion Purchase',
        order_id: order.id,
        handler: (response: any) => {
          this.handlePaymentSuccess(response, orderData);
        },
        prefill: {
          name: userDetails.name,
          email: userDetails.email,
          contact: userDetails.phone
        },
        theme: {
          color: '#667eea'
        },
        modal: {
          ondismiss: () => {
            this.paymentStatusSubject.next('cancelled');
          }
        }
      };

      // Open Razorpay checkout
      const rzp = new Razorpay(options);
      rzp.open();

    } catch (error) {
      console.error('Payment initiation failed:', error);
      this.paymentStatusSubject.next('failed');
      throw error;
    }
  }

  private async handlePaymentSuccess(response: any, orderData: any): Promise<void> {
    try {
      // Verify payment on backend
      const verificationData: PaymentVerification = {
        razorpay_order_id: response.razorpay_order_id,
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_signature: response.razorpay_signature
      };

      const verification = await this.verifyPayment(verificationData).toPromise();
      
      if (verification) {
        this.paymentStatusSubject.next('success');
        
        // Generate and send bill
        await this.generateAndSendBill({
          ...orderData,
          paymentId: response.razorpay_payment_id,
          orderId: response.razorpay_order_id
        });
      } else {
        this.paymentStatusSubject.next('failed');
      }
    } catch (error) {
      console.error('Payment verification failed:', error);
      this.paymentStatusSubject.next('failed');
    }
  }

  private async generateAndSendBill(orderData: any): Promise<void> {
    try {
      // This will be implemented with the bill service
      console.log('Generating bill for order:', orderData);
    } catch (error) {
      console.error('Bill generation failed:', error);
    }
  }

  // Payment method selection
  getPaymentMethods(): any[] {
    return [
      {
        id: 'razorpay',
        name: 'Credit/Debit Card',
        description: 'Pay securely with your card',
        icon: 'fas fa-credit-card',
        enabled: true
      },
      {
        id: 'upi',
        name: 'UPI',
        description: 'Pay with UPI apps',
        icon: 'fas fa-mobile-alt',
        enabled: true
      },
      {
        id: 'netbanking',
        name: 'Net Banking',
        description: 'Pay with your bank account',
        icon: 'fas fa-university',
        enabled: true
      },
      {
        id: 'wallet',
        name: 'Wallet',
        description: 'Pay with digital wallets',
        icon: 'fas fa-wallet',
        enabled: true
      },
      {
        id: 'cod',
        name: 'Cash on Delivery',
        description: 'Pay when you receive',
        icon: 'fas fa-money-bill-wave',
        enabled: true
      }
    ];
  }

  // Reset payment status
  resetPaymentStatus(): void {
    this.paymentStatusSubject.next('idle');
  }

  // Get current payment status
  getCurrentPaymentStatus(): string {
    return this.paymentStatusSubject.value;
  }
}
