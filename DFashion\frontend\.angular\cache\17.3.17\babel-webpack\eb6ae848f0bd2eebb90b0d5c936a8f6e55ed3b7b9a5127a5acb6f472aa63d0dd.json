{"ast": null, "code": "import { argsOrArgArray } from '../util/argsOrArgArray';\nimport { onErrorResumeNext as oERNCreate } from '../observable/onErrorResumeNext';\nexport function onErrorResumeNextWith(...sources) {\n  const nextSources = argsOrArgArray(sources);\n  return source => oERNCreate(source, ...nextSources);\n}\nexport const onErrorResumeNext = onErrorResumeNextWith;\n//# sourceMappingURL=onErrorResumeNextWith.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}