{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, of, forkJoin } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ShopDataService = /*#__PURE__*/(() => {\n  class ShopDataService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = environment.apiUrl;\n      // Cache subjects for performance\n      this.featuredBrandsSubject = new BehaviorSubject([]);\n      this.trendingProductsSubject = new BehaviorSubject([]);\n      this.newArrivalsSubject = new BehaviorSubject([]);\n      this.quickLinksSubject = new BehaviorSubject([]);\n      this.shopStatsSubject = new BehaviorSubject(null);\n      // Public observables\n      this.featuredBrands$ = this.featuredBrandsSubject.asObservable();\n      this.trendingProducts$ = this.trendingProductsSubject.asObservable();\n      this.newArrivals$ = this.newArrivalsSubject.asObservable();\n      this.quickLinks$ = this.quickLinksSubject.asObservable();\n      this.shopStats$ = this.shopStatsSubject.asObservable();\n      this.initializeQuickLinks();\n    }\n    /**\n     * Load all shop data in parallel\n     */\n    loadAllShopData() {\n      return forkJoin({\n        brands: this.loadFeaturedBrands(),\n        trending: this.loadTrendingProducts(),\n        newArrivals: this.loadNewArrivals(),\n        stats: this.loadShopStats()\n      });\n    }\n    /**\n     * Load featured brands\n     */\n    loadFeaturedBrands() {\n      return this.http.get(`${this.API_URL}/brands/featured`).pipe(map(response => {\n        const brands = response.success ? response.data : this.getMockFeaturedBrands();\n        this.featuredBrandsSubject.next(brands);\n        return brands;\n      }), catchError(() => {\n        const mockBrands = this.getMockFeaturedBrands();\n        this.featuredBrandsSubject.next(mockBrands);\n        return of(mockBrands);\n      }));\n    }\n    /**\n     * Load trending products\n     */\n    loadTrendingProducts(limit = 12) {\n      const params = new HttpParams().set('limit', limit.toString());\n      return this.http.get(`${this.API_URL}/products/trending`, {\n        params\n      }).pipe(map(response => {\n        const products = response.success ? response.data : this.getMockTrendingProducts();\n        this.trendingProductsSubject.next(products);\n        return products;\n      }), catchError(() => {\n        const mockProducts = this.getMockTrendingProducts();\n        this.trendingProductsSubject.next(mockProducts);\n        return of(mockProducts);\n      }));\n    }\n    /**\n     * Load new arrivals\n     */\n    loadNewArrivals(limit = 12) {\n      const params = new HttpParams().set('limit', limit.toString()).set('sortBy', 'newest');\n      return this.http.get(`${this.API_URL}/products`, {\n        params\n      }).pipe(map(response => {\n        const products = response.success ? response.data : this.getMockNewArrivals();\n        this.newArrivalsSubject.next(products);\n        return products;\n      }), catchError(() => {\n        const mockProducts = this.getMockNewArrivals();\n        this.newArrivalsSubject.next(mockProducts);\n        return of(mockProducts);\n      }));\n    }\n    /**\n     * Load shop statistics\n     */\n    loadShopStats() {\n      return this.http.get(`${this.API_URL}/shop/stats`).pipe(map(response => {\n        const stats = response.success ? response.stats : this.getMockShopStats();\n        this.shopStatsSubject.next(stats);\n        return stats;\n      }), catchError(() => {\n        const mockStats = this.getMockShopStats();\n        this.shopStatsSubject.next(mockStats);\n        return of(mockStats);\n      }));\n    }\n    /**\n     * Get products by brand\n     */\n    getProductsByBrand(brandSlug, limit = 20) {\n      const params = new HttpParams().set('brand', brandSlug).set('limit', limit.toString());\n      return this.http.get(`${this.API_URL}/products`, {\n        params\n      }).pipe(map(response => response.success ? response.products : []), catchError(() => of([])));\n    }\n    /**\n     * Get products by category\n     */\n    getProductsByCategory(category, subcategory, limit = 20) {\n      let params = new HttpParams().set('category', category).set('limit', limit.toString());\n      if (subcategory) {\n        params = params.set('subcategory', subcategory);\n      }\n      return this.http.get(`${this.API_URL}/products`, {\n        params\n      }).pipe(map(response => response.success ? response.products : []), catchError(() => of([])));\n    }\n    /**\n     * Search products\n     */\n    searchProducts(query, filters) {\n      let params = new HttpParams().set('q', query);\n      if (filters) {\n        Object.keys(filters).forEach(key => {\n          if (filters[key]) {\n            params = params.set(key, filters[key]);\n          }\n        });\n      }\n      return this.http.get(`${this.API_URL}/search/products`, {\n        params\n      }).pipe(map(response => response.success ? response : {\n        products: [],\n        totalCount: 0\n      }), catchError(() => of({\n        products: [],\n        totalCount: 0\n      })));\n    }\n    /**\n     * Initialize quick links\n     */\n    initializeQuickLinks() {\n      const quickLinks = [{\n        id: 'men-clothing',\n        title: 'Men\\'s Clothing',\n        description: 'Shirts, T-shirts, Jeans & More',\n        icon: 'fas fa-tshirt',\n        route: '/shop/category/men',\n        color: '#3498db',\n        category: 'men',\n        featured: true,\n        order: 1\n      }, {\n        id: 'women-clothing',\n        title: 'Women\\'s Clothing',\n        description: 'Dresses, Tops, Kurtis & More',\n        icon: 'fas fa-female',\n        route: '/shop/category/women',\n        color: '#e91e63',\n        category: 'women',\n        featured: true,\n        order: 2\n      }, {\n        id: 'footwear',\n        title: 'Footwear',\n        description: 'Shoes, Sandals, Sneakers',\n        icon: 'fas fa-shoe-prints',\n        route: '/shop/category/footwear',\n        color: '#ff9800',\n        category: 'footwear',\n        featured: true,\n        order: 3\n      }, {\n        id: 'accessories',\n        title: 'Accessories',\n        description: 'Bags, Watches, Jewelry',\n        icon: 'fas fa-gem',\n        route: '/shop/category/accessories',\n        color: '#9c27b0',\n        category: 'accessories',\n        featured: true,\n        order: 4\n      }, {\n        id: 'ethnic-wear',\n        title: 'Ethnic Wear',\n        description: 'Kurtas, Sarees, Traditional',\n        icon: 'fas fa-star-and-crescent',\n        route: '/shop/category/ethnic',\n        color: '#ff5722',\n        category: 'ethnic',\n        featured: true,\n        order: 5\n      }, {\n        id: 'sports-fitness',\n        title: 'Sports & Fitness',\n        description: 'Activewear, Gym Gear',\n        icon: 'fas fa-dumbbell',\n        route: '/shop/category/sports',\n        color: '#4caf50',\n        category: 'sports',\n        featured: true,\n        order: 6\n      }, {\n        id: 'sale',\n        title: 'Sale & Offers',\n        description: 'Up to 70% Off',\n        icon: 'fas fa-tags',\n        route: '/shop/sale',\n        color: '#f44336',\n        featured: true,\n        order: 7\n      }, {\n        id: 'new-arrivals',\n        title: 'New Arrivals',\n        description: 'Latest Fashion Trends',\n        icon: 'fas fa-sparkles',\n        route: '/shop/new-arrivals',\n        color: '#00bcd4',\n        featured: true,\n        order: 8\n      }];\n      this.quickLinksSubject.next(quickLinks);\n    }\n    /**\n     * Get current cached data\n     */\n    getCurrentFeaturedBrands() {\n      return this.featuredBrandsSubject.value;\n    }\n    getCurrentTrendingProducts() {\n      return this.trendingProductsSubject.value;\n    }\n    getCurrentNewArrivals() {\n      return this.newArrivalsSubject.value;\n    }\n    getCurrentQuickLinks() {\n      return this.quickLinksSubject.value;\n    }\n    getCurrentShopStats() {\n      return this.shopStatsSubject.value;\n    }\n    /**\n     * Mock data methods for fallback\n     */\n    getMockFeaturedBrands() {\n      return [{\n        _id: '1',\n        name: 'Nike',\n        logo: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200',\n        description: 'Just Do It',\n        slug: 'nike',\n        isVerified: true,\n        productCount: 245,\n        rating: 4.8,\n        establishedYear: 1964,\n        website: 'https://nike.com',\n        categories: ['footwear', 'sportswear'],\n        featured: true,\n        trending: true\n      }, {\n        _id: '2',\n        name: 'Adidas',\n        logo: 'https://images.unsplash.com/photo-1556906781-9a412961c28c?w=200',\n        description: 'Impossible is Nothing',\n        slug: 'adidas',\n        isVerified: true,\n        productCount: 189,\n        rating: 4.7,\n        establishedYear: 1949,\n        categories: ['footwear', 'sportswear'],\n        featured: true,\n        trending: true\n      }, {\n        _id: '3',\n        name: 'Zara',\n        logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200',\n        description: 'Fashion Forward',\n        slug: 'zara',\n        isVerified: true,\n        productCount: 567,\n        rating: 4.6,\n        categories: ['men', 'women', 'accessories'],\n        featured: true,\n        trending: false\n      }];\n    }\n    getMockTrendingProducts() {\n      return [{\n        _id: '1',\n        name: 'Premium Cotton T-Shirt',\n        description: 'Comfortable cotton t-shirt perfect for daily wear',\n        images: [{\n          url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n          alt: 'Cotton T-Shirt',\n          isPrimary: true\n        }],\n        pricing: {\n          mrp: 999,\n          sellingPrice: 699,\n          discountPercentage: 30\n        },\n        brand: 'Nike',\n        category: 'men',\n        subcategory: 'tshirts',\n        rating: {\n          average: 4.5,\n          count: 128\n        },\n        analytics: {\n          views: 1250,\n          likes: 89,\n          shares: 23,\n          purchases: 67\n        },\n        isTrending: true,\n        trendingScore: 95,\n        availability: {\n          status: 'in-stock',\n          totalStock: 45\n        }\n      }];\n    }\n    getMockNewArrivals() {\n      return [{\n        _id: '1',\n        name: 'Designer Kurta Set',\n        description: 'Elegant kurta set for special occasions',\n        images: [{\n          url: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=400',\n          alt: 'Kurta Set',\n          isPrimary: true\n        }],\n        pricing: {\n          mrp: 2999,\n          sellingPrice: 2299,\n          discountPercentage: 23\n        },\n        brand: 'Ethnic Wear Co',\n        category: 'men',\n        subcategory: 'kurtas',\n        rating: {\n          average: 4.7,\n          count: 45\n        },\n        createdAt: new Date().toISOString(),\n        isNew: true,\n        availability: {\n          status: 'in-stock',\n          totalStock: 23\n        }\n      }];\n    }\n    getMockShopStats() {\n      return {\n        totalProducts: 12547,\n        totalBrands: 456,\n        totalCategories: 28,\n        totalUsers: 89234,\n        newArrivalsCount: 234,\n        trendingCount: 156\n      };\n    }\n    static {\n      this.ɵfac = function ShopDataService_Factory(t) {\n        return new (t || ShopDataService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ShopDataService,\n        factory: ShopDataService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ShopDataService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}