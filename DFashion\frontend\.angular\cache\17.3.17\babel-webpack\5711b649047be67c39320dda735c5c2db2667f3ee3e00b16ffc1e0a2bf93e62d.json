{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nlet ViewAddStoriesComponent = class ViewAddStoriesComponent {\n  constructor(router, http, authService, cartService, wishlistService, socialMediaService, realtimeService, cdr, buttonActionsService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.socialMediaService = socialMediaService;\n    this.realtimeService = realtimeService;\n    this.cdr = cdr;\n    this.buttonActionsService = buttonActionsService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    // Story viewer state\n    this.isOpen = false;\n    this.currentStoryIndex = 0;\n    this.currentUserIndex = 0;\n    this.currentUserStories = [];\n    this.showProductTags = false;\n    this.isLiked = false;\n    this.storyProgress = 0;\n    // Navigation state\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.showNavArrows = true;\n    // Custom slider properties\n    this.translateX = 0;\n    this.itemWidth = 80; // Width of each story item including margin\n    this.visibleItems = 6; // Number of visible items\n    this.currentSlideIndex = 0;\n    this.storyDuration = 15000; // 15 seconds default\n    this.progressStartTime = 0;\n    this.currentProgress = 0; // Stable progress value for template binding\n    // Touch handling\n    this.touchStartTime = 0;\n    // Carousel options\n    this.customOptions = {\n      loop: false,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      responsive: {\n        0: {\n          items: 3,\n          nav: false\n        },\n        600: {\n          items: 4,\n          nav: false\n        },\n        900: {\n          items: 6,\n          nav: true\n        }\n      },\n      nav: true\n    };\n    this.subscriptions = [];\n    console.log('🏗️ ViewAddStoriesComponent constructor called');\n  }\n  ngOnInit() {\n    console.log('🚀 ViewAddStoriesComponent ngOnInit called');\n    this.loadStories();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      console.log('👤 Current user updated:', user);\n    });\n    // Check if we should show navigation arrows based on screen size\n    this.updateNavArrowsVisibility();\n  }\n  ngAfterViewInit() {\n    console.log('ngAfterViewInit called');\n    setTimeout(() => {\n      console.log('Initializing slider after view init');\n      this.calculateSliderDimensions();\n      this.updateScrollButtons();\n    }, 500);\n    // Also try immediate initialization\n    setTimeout(() => {\n      if (this.stories.length > 0) {\n        console.log('Re-initializing slider with stories');\n        this.calculateSliderDimensions();\n        this.updateScrollButtons();\n      }\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n  onResize() {\n    this.updateNavArrowsVisibility();\n    this.calculateSliderDimensions();\n    this.updateScrollButtons();\n  }\n  updateNavArrowsVisibility() {\n    this.showNavArrows = window.innerWidth > 768;\n  }\n  calculateSliderDimensions() {\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\n      const screenWidth = window.innerWidth;\n      console.log('Calculating slider dimensions:', {\n        containerWidth,\n        screenWidth\n      });\n      // Responsive visible items\n      if (screenWidth <= 600) {\n        this.visibleItems = 3;\n        this.itemWidth = containerWidth / 3;\n      } else if (screenWidth <= 900) {\n        this.visibleItems = 4;\n        this.itemWidth = containerWidth / 4;\n      } else {\n        this.visibleItems = 6;\n        this.itemWidth = containerWidth / 6;\n      }\n      console.log('Slider dimensions calculated:', {\n        visibleItems: this.visibleItems,\n        itemWidth: this.itemWidth\n      });\n      // Reset slider position if needed\n      this.currentSlideIndex = 0;\n      this.translateX = 0;\n    } else {\n      console.warn('Stories slider element not found, using default dimensions');\n      // Fallback dimensions\n      this.itemWidth = 100;\n      this.visibleItems = 6;\n    }\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        console.log('Stories API response:', response);\n        if (response.success && response.stories && response.stories.length > 0) {\n          // Filter only active stories and map the data structure\n          const allStories = response.stories.filter(story => story.isActive).map(story => ({\n            ...story,\n            mediaUrl: story.media?.url || story.mediaUrl,\n            mediaType: story.media?.type || story.mediaType\n          }));\n          // Group stories by user (Instagram style)\n          this.stories = this.groupStoriesByUser(allStories);\n          console.log('Loaded and grouped stories from API:', this.stories);\n          console.log('Total user story groups:', this.stories.length);\n        } else {\n          console.log('No stories from API, loading fallback stories');\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        console.log('Loading fallback stories due to error');\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      }\n    }));\n  }\n  // Group stories by user like Instagram\n  groupStoriesByUser(allStories) {\n    const userStoriesMap = new Map();\n    allStories.forEach(story => {\n      const userId = story.user._id;\n      if (!userStoriesMap.has(userId)) {\n        userStoriesMap.set(userId, {\n          user: story.user,\n          stories: [],\n          hasProducts: false,\n          totalProducts: 0,\n          latestStoryTime: story.createdAt\n        });\n      }\n      const userGroup = userStoriesMap.get(userId);\n      userGroup.stories.push(story);\n      // Check if any story has products\n      if (story.products && story.products.length > 0) {\n        userGroup.hasProducts = true;\n        userGroup.totalProducts += story.products.length;\n      }\n      // Keep track of latest story time for sorting\n      if (new Date(story.createdAt) > new Date(userGroup.latestStoryTime)) {\n        userGroup.latestStoryTime = story.createdAt;\n      }\n    });\n    // Convert map to array and sort by latest story time\n    return Array.from(userStoriesMap.values()).sort((a, b) => new Date(b.latestStoryTime).getTime() - new Date(a.latestStoryTime).getTime());\n  }\n  loadFallbackStories() {\n    console.log('❌ No stories available from API');\n    this.stories = [];\n  }\n  // Navigation methods\n  scrollLeft() {\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\n    if (this.currentSlideIndex > 0) {\n      this.currentSlideIndex--;\n      this.updateSliderPosition();\n      console.log('Scrolled left to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll left, already at start');\n    }\n  }\n  scrollRight() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    console.log('Scroll right clicked:', {\n      currentIndex: this.currentSlideIndex,\n      totalItems,\n      maxSlideIndex,\n      visibleItems: this.visibleItems\n    });\n    if (this.currentSlideIndex < maxSlideIndex) {\n      this.currentSlideIndex++;\n      this.updateSliderPosition();\n      console.log('Scrolled right to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll right, already at end');\n    }\n  }\n  updateSliderPosition() {\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\n    console.log('Updating slider position:', {\n      currentIndex: this.currentSlideIndex,\n      itemWidth: this.itemWidth,\n      newTranslateX\n    });\n    this.translateX = newTranslateX;\n    this.updateScrollButtons();\n  }\n  updateScrollButtons() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    this.canScrollLeft = this.currentSlideIndex > 0;\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\n    console.log('Updated scroll buttons:', {\n      canScrollLeft: this.canScrollLeft,\n      canScrollRight: this.canScrollRight,\n      totalItems,\n      maxSlideIndex,\n      currentIndex: this.currentSlideIndex\n    });\n  }\n  // Story viewer methods\n  openUserStories(userGroup, userIndex) {\n    this.currentUserIndex = userIndex;\n    this.currentStoryIndex = 0; // Start with first story of this user\n    this.currentUserStories = userGroup.stories;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n    console.log('Opening stories for user:', userGroup.user.username, 'Stories count:', userGroup.stories.length);\n  }\n  openStory(_story, index) {\n    // Legacy method - keeping for compatibility\n    this.currentStoryIndex = index;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.clearStoryTimer();\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n  }\n  nextStory() {\n    // First check if there are more stories for current user\n    if (this.currentStoryIndex < this.currentUserStories.length - 1) {\n      this.currentStoryIndex++;\n      this.showProductTags = false;\n      this.startStoryTimer();\n    } else {\n      // Move to next user's stories\n      if (this.currentUserIndex < this.stories.length - 1) {\n        this.currentUserIndex++;\n        this.currentStoryIndex = 0;\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\n        this.showProductTags = false;\n        this.startStoryTimer();\n      } else {\n        this.closeStories();\n      }\n    }\n  }\n  previousStory() {\n    // First check if there are previous stories for current user\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.showProductTags = false;\n      this.startStoryTimer();\n    } else {\n      // Move to previous user's stories (last story)\n      if (this.currentUserIndex > 0) {\n        this.currentUserIndex--;\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\n        this.currentStoryIndex = this.currentUserStories.length - 1;\n        this.showProductTags = false;\n        this.startStoryTimer();\n      } else {\n        this.closeStories();\n      }\n    }\n  }\n  getCurrentStory() {\n    if (this.currentUserStories && this.currentUserStories.length > 0) {\n      return this.currentUserStories[this.currentStoryIndex] || this.currentUserStories[0];\n    }\n    // Fallback for legacy usage - get first story from first user group\n    if (this.stories && this.stories.length > 0 && this.stories[0].stories.length > 0) {\n      return this.stories[0].stories[0];\n    }\n    return null;\n  }\n  getCurrentUser() {\n    if (this.stories && this.stories.length > 0) {\n      return this.stories[this.currentUserIndex]?.user;\n    }\n    return null;\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getProductCount() {\n    const story = this.getCurrentStory();\n    return story?.products?.length || 0;\n  }\n  // Progress tracking\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) return 100;\n    if (index > this.currentStoryIndex) return 0;\n    // Return the stable progress value for the current story\n    return this.currentProgress;\n  }\n  startStoryTimer() {\n    this.clearStoryTimer();\n    this.progressStartTime = Date.now();\n    this.currentProgress = 0;\n    // Update progress every 100ms for smooth animation\n    this.progressUpdateTimer = setInterval(() => {\n      if (this.progressStartTime) {\n        const elapsed = Date.now() - this.progressStartTime;\n        this.currentProgress = Math.min(elapsed / this.storyDuration * 100, 100);\n        this.cdr.detectChanges(); // Trigger change detection manually\n      }\n    }, 100);\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n  clearStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n      this.progressTimer = null;\n    }\n    if (this.progressUpdateTimer) {\n      clearInterval(this.progressUpdateTimer);\n      this.progressUpdateTimer = null;\n    }\n    this.progressStartTime = 0;\n    this.currentProgress = 0;\n  }\n  // Story interaction methods\n  onStoryClick(event) {\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else if (clickX > windowWidth * 2 / 3) {\n      this.nextStory();\n    }\n  }\n  // Touch handling\n  onTouchStart(_event) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.clearStoryTimer(); // Pause story progress on long press\n    }, 500);\n  }\n  onTouchMove(_event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onTouchEnd(event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n    const touchDuration = Date.now() - this.touchStartTime;\n    if (touchDuration < 500) {\n      // Short tap - treat as click\n      const touch = event.changedTouches[0];\n      this.onStoryClick({\n        clientX: touch.clientX\n      });\n    } else {\n      // Long press ended - resume story progress\n      this.startStoryTimer();\n    }\n  }\n  // Product interaction methods\n  toggleProductTags() {\n    this.showProductTags = !this.showProductTags;\n  }\n  openProductDetails(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  // Add product to cart with real-time functionality\n  addToCart(product) {\n    console.log('Adding product to cart:', product);\n    this.buttonActionsService.addToCart({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      quantity: 1,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to cart successfully:', result);\n          // Show success message or toast\n        } else {\n          console.error('Failed to add product to cart:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding product to cart:', error);\n      }\n    });\n  }\n  // Add product to wishlist with real-time functionality\n  addToWishlist(product) {\n    console.log('Adding product to wishlist:', product);\n    this.buttonActionsService.addToWishlist({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully:', result);\n          // Show success message or toast\n        } else {\n          console.error('Failed to add product to wishlist:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding product to wishlist:', error);\n      }\n    });\n  }\n  // Buy now functionality\n  buyNow(product) {\n    console.log('Buy now clicked for product:', product);\n    this.buttonActionsService.buyNow({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      quantity: 1,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Buy now successful:', result);\n          // Navigation to checkout will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error in buy now:', error);\n      }\n    });\n  }\n  // Story actions with real-time functionality\n  toggleLike() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    this.isLiked = !this.isLiked;\n    // Call API to like/unlike story\n    const endpoint = this.isLiked ? 'like' : 'unlike';\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\n      next: response => {\n        console.log(`Story ${endpoint}d successfully:`, response);\n        // Emit real-time event (using socket directly)\n        if (this.realtimeService.isConnected()) {\n          // For now, we'll track this locally until we add story-specific emit methods\n          console.log('Story liked event:', {\n            storyId: currentStory._id,\n            userId: this.authService.currentUserValue?._id,\n            liked: this.isLiked\n          });\n        }\n      },\n      error: error => {\n        console.error(`Error ${endpoint}ing story:`, error);\n        // Revert the like state on error\n        this.isLiked = !this.isLiked;\n      }\n    });\n  }\n  shareStory() {\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    if (navigator.share) {\n      // Use native sharing if available\n      navigator.share({\n        title: `Story by ${currentStory.user.username}`,\n        text: currentStory.caption,\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\n      }).then(() => {\n        console.log('Story shared successfully');\n        // Track share event\n        this.trackStoryShare(currentStory._id);\n      }).catch(error => {\n        console.error('Error sharing story:', error);\n      });\n    } else {\n      // Fallback: copy link to clipboard\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\n      navigator.clipboard.writeText(shareUrl).then(() => {\n        console.log('Story link copied to clipboard');\n        this.trackStoryShare(currentStory._id);\n        // Show toast notification\n        this.showToast('Story link copied to clipboard!');\n      }).catch(error => {\n        console.error('Error copying to clipboard:', error);\n      });\n    }\n  }\n  saveStory() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    // Call API to save story\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\n      next: response => {\n        console.log('Story saved successfully:', response);\n        this.showToast('Story saved to your collection!');\n        // Emit real-time event (using socket directly)\n        if (this.realtimeService.isConnected()) {\n          // For now, we'll track this locally until we add story-specific emit methods\n          console.log('Story saved event:', {\n            storyId: currentStory._id,\n            userId: this.authService.currentUserValue?._id\n          });\n        }\n      },\n      error: error => {\n        console.error('Error saving story:', error);\n        this.showToast('Error saving story. Please try again.');\n      }\n    });\n  }\n  trackStoryShare(storyId) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\n        next: response => {\n          console.log('Story share tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking story share:', error);\n        }\n      });\n    }\n  }\n  // Handle middle area click for product/category navigation\n  handleMiddleAreaClick(event) {\n    event.stopPropagation();\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return;\n    }\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        if (currentStory.linkedContent.productId) {\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\n        } else if (currentStory.products && currentStory.products.length > 0) {\n          // Fallback to first product if no specific productId\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\n        }\n        break;\n      case 'category':\n        if (currentStory.linkedContent.categoryId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              category: currentStory.linkedContent.categoryId\n            }\n          });\n        }\n        break;\n      case 'brand':\n        if (currentStory.linkedContent.brandId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              brand: currentStory.linkedContent.brandId\n            }\n          });\n        }\n        break;\n      case 'collection':\n        if (currentStory.linkedContent.collectionId) {\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\n        }\n        break;\n      default:\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\n    }\n    // Track click event\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\n  }\n  // Get text for linked content indicator\n  getLinkedContentText() {\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return '';\n    }\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        return 'View Product';\n      case 'category':\n        return 'Browse Category';\n      case 'brand':\n        return 'View Brand';\n      case 'collection':\n        return 'View Collection';\n      default:\n        return 'View Details';\n    }\n  }\n  // Track linked content click for analytics\n  trackLinkedContentClick(storyId, linkedContent) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\n        contentType: linkedContent.type,\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\n      }).subscribe({\n        next: response => {\n          console.log('Linked content click tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking linked content click:', error);\n        }\n      });\n    }\n  }\n  showToast(message) {\n    // Simple toast implementation - you can replace with your preferred toast library\n    const toast = document.createElement('div');\n    toast.textContent = message;\n    toast.style.cssText = `\n      position: fixed;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-size: 14px;\n    `;\n    document.body.appendChild(toast);\n    setTimeout(() => {\n      document.body.removeChild(toast);\n    }, 3000);\n  }\n  // Add story functionality\n  onAdd() {\n    this.router.navigate(['/stories/create']);\n  }\n  // Utility methods\n  getTimeAgo(dateString) {\n    if (!dateString) return 'Unknown';\n    const now = new Date();\n    let date;\n    if (typeof dateString === 'string') {\n      date = new Date(dateString);\n    } else {\n      date = dateString;\n    }\n    // Check if date is valid\n    if (isNaN(date.getTime())) {\n      return 'Unknown';\n    }\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n};\n__decorate([ViewChild('storiesSlider', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"storiesSlider\", void 0);\n__decorate([ViewChild('storiesTrack', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"storiesTrack\", void 0);\n__decorate([ViewChild('storyVideo', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"storyVideo\", void 0);\n__decorate([HostListener('window:resize', ['$event'])], ViewAddStoriesComponent.prototype, \"onResize\", null);\n__decorate([HostListener('document:keydown', ['$event'])], ViewAddStoriesComponent.prototype, \"handleKeydown\", null);\nViewAddStoriesComponent = __decorate([Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})], ViewAddStoriesComponent);\nexport { ViewAddStoriesComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "HostListener", "CommonModule", "FormsModule", "environment", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "cartService", "wishlistService", "socialMediaService", "realtimeService", "cdr", "buttonActionsService", "currentUser", "stories", "isLoadingStories", "isOpen", "currentStoryIndex", "currentUserIndex", "currentUserStories", "showProductTags", "isLiked", "storyProgress", "canScrollLeft", "canScrollRight", "showNavArrows", "translateX", "itemWidth", "visibleItems", "currentSlideIndex", "storyDuration", "progressStartTime", "currentProgress", "touchStartTime", "customOptions", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "responsive", "items", "nav", "subscriptions", "console", "log", "ngOnInit", "loadStories", "currentUser$", "subscribe", "user", "updateNavArrowsVisibility", "ngAfterViewInit", "setTimeout", "calculateSliderDimensions", "updateScrollButtons", "length", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "clearStoryTimer", "onResize", "window", "innerWidth", "storiesSlider", "nativeElement", "containerWidth", "offsetWidth", "screenWidth", "warn", "push", "get", "apiUrl", "next", "response", "success", "allStories", "filter", "story", "isActive", "map", "mediaUrl", "media", "url", "mediaType", "type", "groupStoriesByUser", "loadFallbackStories", "error", "userStoriesMap", "Map", "userId", "_id", "has", "set", "hasProducts", "totalProducts", "latestStoryTime", "createdAt", "userGroup", "products", "Date", "Array", "from", "values", "sort", "a", "b", "getTime", "scrollLeft", "updateSliderPosition", "scrollRight", "totalItems", "maxSlideIndex", "Math", "max", "currentIndex", "newTranslateX", "openUserStories", "userIndex", "startStoryTimer", "document", "body", "style", "overflow", "username", "openStory", "_story", "index", "closeStories", "pauseAllVideos", "nextStory", "previousStory", "getCurrentStory", "getCurrentUser", "getProductCount", "getProgressWidth", "now", "progressUpdateTimer", "setInterval", "elapsed", "min", "detectChanges", "progressTimer", "clearTimeout", "clearInterval", "onStoryClick", "event", "clickX", "clientX", "windowWidth", "onTouchStart", "_event", "longPressTimer", "onTouchMove", "onTouchEnd", "touchDuration", "touch", "changedTouches", "toggleProductTags", "openProductDetails", "product", "navigate", "addToCart", "productId", "size", "color", "quantity", "addedFrom", "result", "message", "addToWishlist", "buyNow", "toggleLike", "isAuthenticated", "currentStory", "endpoint", "post", "isConnected", "storyId", "currentUserValue", "liked", "shareStory", "navigator", "share", "title", "text", "caption", "frontendUrl", "then", "trackStoryShare", "catch", "shareUrl", "clipboard", "writeText", "showToast", "saveStory", "handleMiddleAreaClick", "stopPropagation", "linkedContent", "categoryId", "queryParams", "category", "brandId", "brand", "collectionId", "trackLinkedContentClick", "getLinkedContentText", "contentType", "contentId", "toast", "createElement", "textContent", "cssText", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onAdd", "getTimeAgo", "dateString", "date", "isNaN", "diffInMinutes", "floor", "diffInHours", "diffInDays", "videos", "querySelectorAll", "video", "pause", "handleKeydown", "key", "__decorate", "static", "selector", "standalone", "imports", "CarouselModule", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON>ef, ViewChild, HostListener, AfterViewInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Subscription } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CartService } from 'src/app/core/services/cart.service';\r\nimport { WishlistService } from 'src/app/core/services/wishlist.service';\r\nimport { SocialMediaService } from 'src/app/core/services/social-media.service';\r\nimport { RealtimeService } from 'src/app/core/services/realtime.service';\r\nimport { ButtonActionsService } from 'src/app/core/services/button-actions.service';\r\nimport { register } from 'swiper/element/bundle';\r\n\r\ninterface Story {\r\n  _id: string;\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  media: {\r\n    type: 'image' | 'video';\r\n    url: string;\r\n    thumbnail?: string;\r\n    duration?: number;\r\n  };\r\n  mediaUrl: string; // For backward compatibility\r\n  mediaType: 'image' | 'video'; // For backward compatibility\r\n  caption?: string;\r\n  createdAt: string;\r\n  expiresAt: string;\r\n  views: number;\r\n  isActive: boolean;\r\n  linkedContent?: {\r\n    type: 'product' | 'category' | 'brand' | 'collection';\r\n    productId?: string;\r\n    categoryId?: string;\r\n    brandId?: string;\r\n    collectionId?: string;\r\n  };\r\n  products?: Array<{\r\n    _id: string;\r\n    product: {\r\n      _id: string;\r\n      name: string;\r\n      price: number;\r\n      images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;\r\n    };\r\n    position?: {\r\n      x: number;\r\n      y: number;\r\n    };\r\n    size?: string;\r\n    color?: string;\r\n  }>;\r\n}\r\n\r\ninterface UserStoryGroup {\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  stories: Story[];\r\n  hasProducts: boolean;\r\n  totalProducts: number;\r\n  latestStoryTime: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-view-add-stories',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, CarouselModule],\r\n  templateUrl: './view-add-stories.component.html',\r\n  styleUrls: ['./view-add-stories.component.scss']\r\n})\r\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storiesTrack', { static: false }) storiesTrack!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storyVideo', { static: false }) storyVideo!: ElementRef<HTMLVideoElement>;\r\n\r\n  currentUser: any = null;\r\n  stories: UserStoryGroup[] = [];\r\n  isLoadingStories = true;\r\n\r\n  // Story viewer state\r\n  isOpen = false;\r\n  currentStoryIndex = 0;\r\n  currentUserIndex = 0;\r\n  currentUserStories: Story[] = [];\r\n  showProductTags = false;\r\n  isLiked = false;\r\n  storyTimer: any;\r\n  storyProgress = 0;\r\n\r\n  // Navigation state\r\n  canScrollLeft = false;\r\n  canScrollRight = false;\r\n  showNavArrows = true;\r\n\r\n  // Custom slider properties\r\n  translateX = 0;\r\n  itemWidth = 80; // Width of each story item including margin\r\n  visibleItems = 6; // Number of visible items\r\n  currentSlideIndex = 0;\r\n\r\n  // Progress tracking\r\n  private progressTimer: any;\r\n  private progressUpdateTimer: any;\r\n  private storyDuration = 15000; // 15 seconds default\r\n  private progressStartTime = 0;\r\n  currentProgress = 0; // Stable progress value for template binding\r\n\r\n  // Touch handling\r\n  private touchStartTime = 0;\r\n  private longPressTimer: any;\r\n\r\n  // Carousel options\r\n  customOptions: OwlOptions = {\r\n    loop: false,\r\n    mouseDrag: true,\r\n    touchDrag: true,\r\n    pullDrag: false,\r\n    dots: false,\r\n    navSpeed: 700,\r\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\r\n    responsive: {\r\n      0: {\r\n        items: 3,\r\n        nav: false\r\n      },\r\n      600: {\r\n        items: 4,\r\n        nav: false\r\n      },\r\n      900: {\r\n        items: 6,\r\n        nav: true\r\n      }\r\n    },\r\n    nav: true\r\n  };\r\n\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private authService: AuthService,\r\n    private cartService: CartService,\r\n    private wishlistService: WishlistService,\r\n    private socialMediaService: SocialMediaService,\r\n    private realtimeService: RealtimeService,\r\n    private cdr: ChangeDetectorRef,\r\n    private buttonActionsService: ButtonActionsService\r\n  ) {\r\n    console.log('🏗️ ViewAddStoriesComponent constructor called');\r\n  }\r\n\r\n  ngOnInit() {\r\n    console.log('🚀 ViewAddStoriesComponent ngOnInit called');\r\n    this.loadStories();\r\n    this.authService.currentUser$.subscribe(user => {\r\n      this.currentUser = user;\r\n      console.log('👤 Current user updated:', user);\r\n    });\r\n\r\n    // Check if we should show navigation arrows based on screen size\r\n    this.updateNavArrowsVisibility();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    console.log('ngAfterViewInit called');\r\n    setTimeout(() => {\r\n      console.log('Initializing slider after view init');\r\n      this.calculateSliderDimensions();\r\n      this.updateScrollButtons();\r\n    }, 500);\r\n\r\n    // Also try immediate initialization\r\n    setTimeout(() => {\r\n      if (this.stories.length > 0) {\r\n        console.log('Re-initializing slider with stories');\r\n        this.calculateSliderDimensions();\r\n        this.updateScrollButtons();\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n    this.clearStoryTimer();\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize() {\r\n    this.updateNavArrowsVisibility();\r\n    this.calculateSliderDimensions();\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  private updateNavArrowsVisibility() {\r\n    this.showNavArrows = window.innerWidth > 768;\r\n  }\r\n\r\n  private calculateSliderDimensions() {\r\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\r\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\r\n      const screenWidth = window.innerWidth;\r\n\r\n      console.log('Calculating slider dimensions:', { containerWidth, screenWidth });\r\n\r\n      // Responsive visible items\r\n      if (screenWidth <= 600) {\r\n        this.visibleItems = 3;\r\n        this.itemWidth = containerWidth / 3;\r\n      } else if (screenWidth <= 900) {\r\n        this.visibleItems = 4;\r\n        this.itemWidth = containerWidth / 4;\r\n      } else {\r\n        this.visibleItems = 6;\r\n        this.itemWidth = containerWidth / 6;\r\n      }\r\n\r\n      console.log('Slider dimensions calculated:', {\r\n        visibleItems: this.visibleItems,\r\n        itemWidth: this.itemWidth\r\n      });\r\n\r\n      // Reset slider position if needed\r\n      this.currentSlideIndex = 0;\r\n      this.translateX = 0;\r\n    } else {\r\n      console.warn('Stories slider element not found, using default dimensions');\r\n      // Fallback dimensions\r\n      this.itemWidth = 100;\r\n      this.visibleItems = 6;\r\n    }\r\n  }\r\n\r\n  loadStories() {\r\n    this.isLoadingStories = true;\r\n    this.subscriptions.push(\r\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\r\n        next: (response) => {\r\n          console.log('Stories API response:', response);\r\n          if (response.success && response.stories && response.stories.length > 0) {\r\n            // Filter only active stories and map the data structure\r\n            const allStories = response.stories\r\n              .filter((story: any) => story.isActive)\r\n              .map((story: any) => ({\r\n                ...story,\r\n                mediaUrl: story.media?.url || story.mediaUrl,\r\n                mediaType: story.media?.type || story.mediaType\r\n              }));\r\n\r\n            // Group stories by user (Instagram style)\r\n            this.stories = this.groupStoriesByUser(allStories);\r\n            console.log('Loaded and grouped stories from API:', this.stories);\r\n            console.log('Total user story groups:', this.stories.length);\r\n          } else {\r\n            console.log('No stories from API, loading fallback stories');\r\n            this.loadFallbackStories();\r\n          }\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading stories:', error);\r\n          console.log('Loading fallback stories due to error');\r\n          this.loadFallbackStories();\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  // Group stories by user like Instagram\r\n  private groupStoriesByUser(allStories: Story[]): UserStoryGroup[] {\r\n    const userStoriesMap = new Map();\r\n\r\n    allStories.forEach(story => {\r\n      const userId = story.user._id;\r\n      if (!userStoriesMap.has(userId)) {\r\n        userStoriesMap.set(userId, {\r\n          user: story.user,\r\n          stories: [],\r\n          hasProducts: false,\r\n          totalProducts: 0,\r\n          latestStoryTime: story.createdAt\r\n        });\r\n      }\r\n\r\n      const userGroup = userStoriesMap.get(userId);\r\n      userGroup.stories.push(story);\r\n\r\n      // Check if any story has products\r\n      if (story.products && story.products.length > 0) {\r\n        userGroup.hasProducts = true;\r\n        userGroup.totalProducts += story.products.length;\r\n      }\r\n\r\n      // Keep track of latest story time for sorting\r\n      if (new Date(story.createdAt) > new Date(userGroup.latestStoryTime)) {\r\n        userGroup.latestStoryTime = story.createdAt;\r\n      }\r\n    });\r\n\r\n    // Convert map to array and sort by latest story time\r\n    return Array.from(userStoriesMap.values())\r\n      .sort((a, b) => new Date(b.latestStoryTime).getTime() - new Date(a.latestStoryTime).getTime());\r\n  }\r\n\r\n  loadFallbackStories() {\r\n    console.log('❌ No stories available from API');\r\n    this.stories = [];\r\n  }\r\n\r\n  // Navigation methods\r\n  scrollLeft() {\r\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\r\n    if (this.currentSlideIndex > 0) {\r\n      this.currentSlideIndex--;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled left to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll left, already at start');\r\n    }\r\n  }\r\n\r\n  scrollRight() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    console.log('Scroll right clicked:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      visibleItems: this.visibleItems\r\n    });\r\n\r\n    if (this.currentSlideIndex < maxSlideIndex) {\r\n      this.currentSlideIndex++;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled right to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll right, already at end');\r\n    }\r\n  }\r\n\r\n  private updateSliderPosition() {\r\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\r\n    console.log('Updating slider position:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      itemWidth: this.itemWidth,\r\n      newTranslateX\r\n    });\r\n\r\n    this.translateX = newTranslateX;\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  updateScrollButtons() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    this.canScrollLeft = this.currentSlideIndex > 0;\r\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\r\n\r\n    console.log('Updated scroll buttons:', {\r\n      canScrollLeft: this.canScrollLeft,\r\n      canScrollRight: this.canScrollRight,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      currentIndex: this.currentSlideIndex\r\n    });\r\n  }\r\n\r\n  // Story viewer methods\r\n  openUserStories(userGroup: UserStoryGroup, userIndex: number) {\r\n    this.currentUserIndex = userIndex;\r\n    this.currentStoryIndex = 0; // Start with first story of this user\r\n    this.currentUserStories = userGroup.stories;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n    console.log('Opening stories for user:', userGroup.user.username, 'Stories count:', userGroup.stories.length);\r\n  }\r\n\r\n  openStory(_story: Story, index: number) {\r\n    // Legacy method - keeping for compatibility\r\n    this.currentStoryIndex = index;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n  }\r\n\r\n  closeStories() {\r\n    this.isOpen = false;\r\n    this.clearStoryTimer();\r\n    this.pauseAllVideos();\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  nextStory() {\r\n    // First check if there are more stories for current user\r\n    if (this.currentStoryIndex < this.currentUserStories.length - 1) {\r\n      this.currentStoryIndex++;\r\n      this.showProductTags = false;\r\n      this.startStoryTimer();\r\n    } else {\r\n      // Move to next user's stories\r\n      if (this.currentUserIndex < this.stories.length - 1) {\r\n        this.currentUserIndex++;\r\n        this.currentStoryIndex = 0;\r\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\r\n        this.showProductTags = false;\r\n        this.startStoryTimer();\r\n      } else {\r\n        this.closeStories();\r\n      }\r\n    }\r\n  }\r\n\r\n  previousStory() {\r\n    // First check if there are previous stories for current user\r\n    if (this.currentStoryIndex > 0) {\r\n      this.currentStoryIndex--;\r\n      this.showProductTags = false;\r\n      this.startStoryTimer();\r\n    } else {\r\n      // Move to previous user's stories (last story)\r\n      if (this.currentUserIndex > 0) {\r\n        this.currentUserIndex--;\r\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\r\n        this.currentStoryIndex = this.currentUserStories.length - 1;\r\n        this.showProductTags = false;\r\n        this.startStoryTimer();\r\n      } else {\r\n        this.closeStories();\r\n      }\r\n    }\r\n  }\r\n\r\n  getCurrentStory(): Story | null {\r\n    if (this.currentUserStories && this.currentUserStories.length > 0) {\r\n      return this.currentUserStories[this.currentStoryIndex] || this.currentUserStories[0];\r\n    }\r\n    // Fallback for legacy usage - get first story from first user group\r\n    if (this.stories && this.stories.length > 0 && this.stories[0].stories.length > 0) {\r\n      return this.stories[0].stories[0];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  getCurrentUser(): any {\r\n    if (this.stories && this.stories.length > 0) {\r\n      return this.stories[this.currentUserIndex]?.user;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  hasProducts(): boolean {\r\n    const story = this.getCurrentStory();\r\n    return !!(story && story.products && story.products.length > 0);\r\n  }\r\n\r\n  getProductCount(): number {\r\n    const story = this.getCurrentStory();\r\n    return story?.products?.length || 0;\r\n  }\r\n\r\n  // Progress tracking\r\n  getProgressWidth(index: number): number {\r\n    if (index < this.currentStoryIndex) return 100;\r\n    if (index > this.currentStoryIndex) return 0;\r\n\r\n    // Return the stable progress value for the current story\r\n    return this.currentProgress;\r\n  }\r\n\r\n  private startStoryTimer() {\r\n    this.clearStoryTimer();\r\n    this.progressStartTime = Date.now();\r\n    this.currentProgress = 0;\r\n\r\n    // Update progress every 100ms for smooth animation\r\n    this.progressUpdateTimer = setInterval(() => {\r\n      if (this.progressStartTime) {\r\n        const elapsed = Date.now() - this.progressStartTime;\r\n        this.currentProgress = Math.min((elapsed / this.storyDuration) * 100, 100);\r\n        this.cdr.detectChanges(); // Trigger change detection manually\r\n      }\r\n    }, 100);\r\n\r\n    this.progressTimer = setTimeout(() => {\r\n      this.nextStory();\r\n    }, this.storyDuration);\r\n  }\r\n\r\n  private clearStoryTimer() {\r\n    if (this.progressTimer) {\r\n      clearTimeout(this.progressTimer);\r\n      this.progressTimer = null;\r\n    }\r\n    if (this.progressUpdateTimer) {\r\n      clearInterval(this.progressUpdateTimer);\r\n      this.progressUpdateTimer = null;\r\n    }\r\n    this.progressStartTime = 0;\r\n    this.currentProgress = 0;\r\n  }\r\n\r\n  // Story interaction methods\r\n  onStoryClick(event: MouseEvent) {\r\n    const clickX = event.clientX;\r\n    const windowWidth = window.innerWidth;\r\n\r\n    if (clickX < windowWidth / 3) {\r\n      this.previousStory();\r\n    } else if (clickX > (windowWidth * 2) / 3) {\r\n      this.nextStory();\r\n    }\r\n  }\r\n\r\n  // Touch handling\r\n  onTouchStart(_event: TouchEvent) {\r\n    this.touchStartTime = Date.now();\r\n    this.longPressTimer = setTimeout(() => {\r\n      this.clearStoryTimer(); // Pause story progress on long press\r\n    }, 500);\r\n  }\r\n\r\n  onTouchMove(_event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n  }\r\n\r\n  onTouchEnd(event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n\r\n    const touchDuration = Date.now() - this.touchStartTime;\r\n    if (touchDuration < 500) {\r\n      // Short tap - treat as click\r\n      const touch = event.changedTouches[0];\r\n      this.onStoryClick({ clientX: touch.clientX } as MouseEvent);\r\n    } else {\r\n      // Long press ended - resume story progress\r\n      this.startStoryTimer();\r\n    }\r\n  }\r\n\r\n  // Product interaction methods\r\n  toggleProductTags() {\r\n    this.showProductTags = !this.showProductTags;\r\n  }\r\n\r\n  openProductDetails(product: any) {\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  // Add product to cart with real-time functionality\r\n  addToCart(product: any) {\r\n    console.log('Adding product to cart:', product);\r\n\r\n    this.buttonActionsService.addToCart({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      quantity: 1,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Product added to cart successfully:', result);\r\n          // Show success message or toast\r\n        } else {\r\n          console.error('Failed to add product to cart:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to cart:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Add product to wishlist with real-time functionality\r\n  addToWishlist(product: any) {\r\n    console.log('Adding product to wishlist:', product);\r\n\r\n    this.buttonActionsService.addToWishlist({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Product added to wishlist successfully:', result);\r\n          // Show success message or toast\r\n        } else {\r\n          console.error('Failed to add product to wishlist:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to wishlist:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Buy now functionality\r\n  buyNow(product: any) {\r\n    console.log('Buy now clicked for product:', product);\r\n\r\n    this.buttonActionsService.buyNow({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      quantity: 1,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Buy now successful:', result);\r\n          // Navigation to checkout will be handled by the service\r\n        } else {\r\n          console.error('Failed to buy now:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error in buy now:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Story actions with real-time functionality\r\n  toggleLike() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    this.isLiked = !this.isLiked;\r\n\r\n    // Call API to like/unlike story\r\n    const endpoint = this.isLiked ? 'like' : 'unlike';\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log(`Story ${endpoint}d successfully:`, response);\r\n        // Emit real-time event (using socket directly)\r\n        if (this.realtimeService.isConnected()) {\r\n          // For now, we'll track this locally until we add story-specific emit methods\r\n          console.log('Story liked event:', {\r\n            storyId: currentStory._id,\r\n            userId: this.authService.currentUserValue?._id,\r\n            liked: this.isLiked\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error(`Error ${endpoint}ing story:`, error);\r\n        // Revert the like state on error\r\n        this.isLiked = !this.isLiked;\r\n      }\r\n    });\r\n  }\r\n\r\n  shareStory() {\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    if (navigator.share) {\r\n      // Use native sharing if available\r\n      navigator.share({\r\n        title: `Story by ${currentStory.user.username}`,\r\n        text: currentStory.caption,\r\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\r\n      }).then(() => {\r\n        console.log('Story shared successfully');\r\n        // Track share event\r\n        this.trackStoryShare(currentStory._id);\r\n      }).catch((error) => {\r\n        console.error('Error sharing story:', error);\r\n      });\r\n    } else {\r\n      // Fallback: copy link to clipboard\r\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\r\n      navigator.clipboard.writeText(shareUrl).then(() => {\r\n        console.log('Story link copied to clipboard');\r\n        this.trackStoryShare(currentStory._id);\r\n        // Show toast notification\r\n        this.showToast('Story link copied to clipboard!');\r\n      }).catch((error) => {\r\n        console.error('Error copying to clipboard:', error);\r\n      });\r\n    }\r\n  }\r\n\r\n  saveStory() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    // Call API to save story\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log('Story saved successfully:', response);\r\n        this.showToast('Story saved to your collection!');\r\n        // Emit real-time event (using socket directly)\r\n        if (this.realtimeService.isConnected()) {\r\n          // For now, we'll track this locally until we add story-specific emit methods\r\n          console.log('Story saved event:', {\r\n            storyId: currentStory._id,\r\n            userId: this.authService.currentUserValue?._id\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error saving story:', error);\r\n        this.showToast('Error saving story. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private trackStoryShare(storyId: string) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\r\n        next: (response) => {\r\n          console.log('Story share tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking story share:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Handle middle area click for product/category navigation\r\n  handleMiddleAreaClick(event: Event) {\r\n    event.stopPropagation();\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return;\r\n    }\r\n\r\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        if (currentStory.linkedContent.productId) {\r\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\r\n        } else if (currentStory.products && currentStory.products.length > 0) {\r\n          // Fallback to first product if no specific productId\r\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\r\n        }\r\n        break;\r\n\r\n      case 'category':\r\n        if (currentStory.linkedContent.categoryId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { category: currentStory.linkedContent.categoryId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'brand':\r\n        if (currentStory.linkedContent.brandId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { brand: currentStory.linkedContent.brandId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'collection':\r\n        if (currentStory.linkedContent.collectionId) {\r\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\r\n        }\r\n        break;\r\n\r\n      default:\r\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\r\n    }\r\n\r\n    // Track click event\r\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\r\n  }\r\n\r\n  // Get text for linked content indicator\r\n  getLinkedContentText(): string {\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return '';\r\n    }\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        return 'View Product';\r\n      case 'category':\r\n        return 'Browse Category';\r\n      case 'brand':\r\n        return 'View Brand';\r\n      case 'collection':\r\n        return 'View Collection';\r\n      default:\r\n        return 'View Details';\r\n    }\r\n  }\r\n\r\n  // Track linked content click for analytics\r\n  private trackLinkedContentClick(storyId: string, linkedContent: any) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\r\n        contentType: linkedContent.type,\r\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\r\n      }).subscribe({\r\n        next: (response) => {\r\n          console.log('Linked content click tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking linked content click:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private showToast(message: string) {\r\n    // Simple toast implementation - you can replace with your preferred toast library\r\n    const toast = document.createElement('div');\r\n    toast.textContent = message;\r\n    toast.style.cssText = `\r\n      position: fixed;\r\n      bottom: 20px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: white;\r\n      padding: 12px 24px;\r\n      border-radius: 8px;\r\n      z-index: 10000;\r\n      font-size: 14px;\r\n    `;\r\n    document.body.appendChild(toast);\r\n\r\n    setTimeout(() => {\r\n      document.body.removeChild(toast);\r\n    }, 3000);\r\n  }\r\n\r\n  // Add story functionality\r\n  onAdd() {\r\n    this.router.navigate(['/stories/create']);\r\n  }\r\n\r\n  // Utility methods\r\n  getTimeAgo(dateString: string | Date | undefined): string {\r\n    if (!dateString) return 'Unknown';\r\n\r\n    const now = new Date();\r\n    let date: Date;\r\n\r\n    if (typeof dateString === 'string') {\r\n      date = new Date(dateString);\r\n    } else {\r\n      date = dateString;\r\n    }\r\n\r\n    // Check if date is valid\r\n    if (isNaN(date.getTime())) {\r\n      return 'Unknown';\r\n    }\r\n\r\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\r\n\r\n    if (diffInMinutes < 1) return 'now';\r\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\r\n\r\n    const diffInHours = Math.floor(diffInMinutes / 60);\r\n    if (diffInHours < 24) return `${diffInHours}h`;\r\n\r\n    const diffInDays = Math.floor(diffInHours / 24);\r\n    return `${diffInDays}d`;\r\n  }\r\n\r\n  private pauseAllVideos() {\r\n    const videos = document.querySelectorAll('video');\r\n    videos.forEach(video => {\r\n      if (video.pause) {\r\n        video.pause();\r\n      }\r\n    });\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeydown(event: KeyboardEvent) {\r\n    if (!this.isOpen) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        this.previousStory();\r\n        break;\r\n      case 'ArrowRight':\r\n        this.nextStory();\r\n        break;\r\n      case 'Escape':\r\n        this.closeStories();\r\n        break;\r\n    }\r\n  }\r\n}"], "mappings": ";AAAA,SAASA,SAAS,EAAiCC,SAAS,EAAEC,YAAY,QAAkE,eAAe;AAC3J,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AA0EnD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAqElCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,kBAAsC,EACtCC,eAAgC,EAChCC,GAAsB,EACtBC,oBAA0C;IAR1C,KAAAR,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,oBAAoB,GAApBA,oBAAoB;IAzE9B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,OAAO,GAAqB,EAAE;IAC9B,KAAAC,gBAAgB,GAAG,IAAI;IAEvB;IACA,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,kBAAkB,GAAY,EAAE;IAChC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAG,KAAK;IAEf,KAAAC,aAAa,GAAG,CAAC;IAEjB;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,SAAS,GAAG,EAAE,CAAC,CAAC;IAChB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IAKb,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,iBAAiB,GAAG,CAAC;IAC7B,KAAAC,eAAe,GAAG,CAAC,CAAC,CAAC;IAErB;IACQ,KAAAC,cAAc,GAAG,CAAC;IAG1B;IACA,KAAAC,aAAa,GAAe;MAC1BC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;;OAER;MACDA,GAAG,EAAE;KACN;IAEO,KAAAC,aAAa,GAAmB,EAAE;IAaxCC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAC/D;EAEAC,QAAQA,CAAA;IACNF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD,IAAI,CAACE,WAAW,EAAE;IAClB,IAAI,CAAC3C,WAAW,CAAC4C,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACvC,WAAW,GAAGuC,IAAI;MACvBN,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,IAAI,CAAC;IAC/C,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,yBAAyB,EAAE;EAClC;EAEAC,eAAeA,CAAA;IACbR,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCQ,UAAU,CAAC,MAAK;MACdT,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAACS,yBAAyB,EAAE;MAChC,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;IAEP;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACzC,OAAO,CAAC4C,MAAM,GAAG,CAAC,EAAE;QAC3BZ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD,IAAI,CAACS,yBAAyB,EAAE;QAChC,IAAI,CAACC,mBAAmB,EAAE;;IAE9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACd,aAAa,CAACe,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACX,yBAAyB,EAAE;IAChC,IAAI,CAACG,yBAAyB,EAAE;IAChC,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQJ,yBAAyBA,CAAA;IAC/B,IAAI,CAAC5B,aAAa,GAAGwC,MAAM,CAACC,UAAU,GAAG,GAAG;EAC9C;EAEQV,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAACW,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,aAAa,EAAE;MAC1D,MAAMC,cAAc,GAAG,IAAI,CAACF,aAAa,CAACC,aAAa,CAACE,WAAW;MACnE,MAAMC,WAAW,GAAGN,MAAM,CAACC,UAAU;MAErCpB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAAEsB,cAAc;QAAEE;MAAW,CAAE,CAAC;MAE9E;MACA,IAAIA,WAAW,IAAI,GAAG,EAAE;QACtB,IAAI,CAAC3C,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAG0C,cAAc,GAAG,CAAC;OACpC,MAAM,IAAIE,WAAW,IAAI,GAAG,EAAE;QAC7B,IAAI,CAAC3C,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAG0C,cAAc,GAAG,CAAC;OACpC,MAAM;QACL,IAAI,CAACzC,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAG0C,cAAc,GAAG,CAAC;;MAGrCvB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CnB,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BD,SAAS,EAAE,IAAI,CAACA;OACjB,CAAC;MAEF;MACA,IAAI,CAACE,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACH,UAAU,GAAG,CAAC;KACpB,MAAM;MACLoB,OAAO,CAAC0B,IAAI,CAAC,4DAA4D,CAAC;MAC1E;MACA,IAAI,CAAC7C,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;EAEzB;EAEAqB,WAAWA,CAAA;IACT,IAAI,CAAClC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC8B,aAAa,CAAC4B,IAAI,CACrB,IAAI,CAACpE,IAAI,CAACqE,GAAG,CAAM,GAAGzE,WAAW,CAAC0E,MAAM,UAAU,CAAC,CAACxB,SAAS,CAAC;MAC5DyB,IAAI,EAAGC,QAAQ,IAAI;QACjB/B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE8B,QAAQ,CAAC;QAC9C,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAC/D,OAAO,IAAI+D,QAAQ,CAAC/D,OAAO,CAAC4C,MAAM,GAAG,CAAC,EAAE;UACvE;UACA,MAAMqB,UAAU,GAAGF,QAAQ,CAAC/D,OAAO,CAChCkE,MAAM,CAAEC,KAAU,IAAKA,KAAK,CAACC,QAAQ,CAAC,CACtCC,GAAG,CAAEF,KAAU,KAAM;YACpB,GAAGA,KAAK;YACRG,QAAQ,EAAEH,KAAK,CAACI,KAAK,EAAEC,GAAG,IAAIL,KAAK,CAACG,QAAQ;YAC5CG,SAAS,EAAEN,KAAK,CAACI,KAAK,EAAEG,IAAI,IAAIP,KAAK,CAACM;WACvC,CAAC,CAAC;UAEL;UACA,IAAI,CAACzE,OAAO,GAAG,IAAI,CAAC2E,kBAAkB,CAACV,UAAU,CAAC;UAClDjC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACjC,OAAO,CAAC;UACjEgC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACjC,OAAO,CAAC4C,MAAM,CAAC;SAC7D,MAAM;UACLZ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D,IAAI,CAAC2C,mBAAmB,EAAE;;QAE5B,IAAI,CAAC3E,gBAAgB,GAAG,KAAK;QAC7BwC,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDkC,KAAK,EAAGA,KAAK,IAAI;QACf7C,OAAO,CAAC6C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C7C,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAAC2C,mBAAmB,EAAE;QAC1B,IAAI,CAAC3E,gBAAgB,GAAG,KAAK;QAC7BwC,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC,CACH;EACH;EAEA;EACQgC,kBAAkBA,CAACV,UAAmB;IAC5C,MAAMa,cAAc,GAAG,IAAIC,GAAG,EAAE;IAEhCd,UAAU,CAACnB,OAAO,CAACqB,KAAK,IAAG;MACzB,MAAMa,MAAM,GAAGb,KAAK,CAAC7B,IAAI,CAAC2C,GAAG;MAC7B,IAAI,CAACH,cAAc,CAACI,GAAG,CAACF,MAAM,CAAC,EAAE;QAC/BF,cAAc,CAACK,GAAG,CAACH,MAAM,EAAE;UACzB1C,IAAI,EAAE6B,KAAK,CAAC7B,IAAI;UAChBtC,OAAO,EAAE,EAAE;UACXoF,WAAW,EAAE,KAAK;UAClBC,aAAa,EAAE,CAAC;UAChBC,eAAe,EAAEnB,KAAK,CAACoB;SACxB,CAAC;;MAGJ,MAAMC,SAAS,GAAGV,cAAc,CAAClB,GAAG,CAACoB,MAAM,CAAC;MAC5CQ,SAAS,CAACxF,OAAO,CAAC2D,IAAI,CAACQ,KAAK,CAAC;MAE7B;MACA,IAAIA,KAAK,CAACsB,QAAQ,IAAItB,KAAK,CAACsB,QAAQ,CAAC7C,MAAM,GAAG,CAAC,EAAE;QAC/C4C,SAAS,CAACJ,WAAW,GAAG,IAAI;QAC5BI,SAAS,CAACH,aAAa,IAAIlB,KAAK,CAACsB,QAAQ,CAAC7C,MAAM;;MAGlD;MACA,IAAI,IAAI8C,IAAI,CAACvB,KAAK,CAACoB,SAAS,CAAC,GAAG,IAAIG,IAAI,CAACF,SAAS,CAACF,eAAe,CAAC,EAAE;QACnEE,SAAS,CAACF,eAAe,GAAGnB,KAAK,CAACoB,SAAS;;IAE/C,CAAC,CAAC;IAEF;IACA,OAAOI,KAAK,CAACC,IAAI,CAACd,cAAc,CAACe,MAAM,EAAE,CAAC,CACvCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIN,IAAI,CAACM,CAAC,CAACV,eAAe,CAAC,CAACW,OAAO,EAAE,GAAG,IAAIP,IAAI,CAACK,CAAC,CAACT,eAAe,CAAC,CAACW,OAAO,EAAE,CAAC;EAClG;EAEArB,mBAAmBA,CAAA;IACjB5C,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACjC,OAAO,GAAG,EAAE;EACnB;EAEA;EACAkG,UAAUA,CAAA;IACRlE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAClB,iBAAiB,CAAC;IAC1E,IAAI,IAAI,CAACA,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACoF,oBAAoB,EAAE;MAC3BnE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAClB,iBAAiB,CAAC;KAC/D,MAAM;MACLiB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;EAEvD;EAEAmE,WAAWA,CAAA;IACT,MAAMC,UAAU,GAAG,IAAI,CAACrG,OAAO,CAAC4C,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM0D,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAACvF,YAAY,CAAC;IAEjEkB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCwE,YAAY,EAAE,IAAI,CAAC1F,iBAAiB;MACpCsF,UAAU;MACVC,aAAa;MACbxF,YAAY,EAAE,IAAI,CAACA;KACpB,CAAC;IAEF,IAAI,IAAI,CAACC,iBAAiB,GAAGuF,aAAa,EAAE;MAC1C,IAAI,CAACvF,iBAAiB,EAAE;MACxB,IAAI,CAACoF,oBAAoB,EAAE;MAC3BnE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAClB,iBAAiB,CAAC;KAChE,MAAM;MACLiB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;EAEtD;EAEQkE,oBAAoBA,CAAA;IAC1B,MAAMO,aAAa,GAAG,CAAC,IAAI,CAAC3F,iBAAiB,GAAG,IAAI,CAACF,SAAS;IAC9DmB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCwE,YAAY,EAAE,IAAI,CAAC1F,iBAAiB;MACpCF,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB6F;KACD,CAAC;IAEF,IAAI,CAAC9F,UAAU,GAAG8F,aAAa;IAC/B,IAAI,CAAC/D,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAM0D,UAAU,GAAG,IAAI,CAACrG,OAAO,CAAC4C,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM0D,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAACvF,YAAY,CAAC;IAEjE,IAAI,CAACL,aAAa,GAAG,IAAI,CAACM,iBAAiB,GAAG,CAAC;IAC/C,IAAI,CAACL,cAAc,GAAG,IAAI,CAACK,iBAAiB,GAAGuF,aAAa;IAE5DtE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;MACrCxB,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnC2F,UAAU;MACVC,aAAa;MACbG,YAAY,EAAE,IAAI,CAAC1F;KACpB,CAAC;EACJ;EAEA;EACA4F,eAAeA,CAACnB,SAAyB,EAAEoB,SAAiB;IAC1D,IAAI,CAACxG,gBAAgB,GAAGwG,SAAS;IACjC,IAAI,CAACzG,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACE,kBAAkB,GAAGmF,SAAS,CAACxF,OAAO;IAC3C,IAAI,CAACE,MAAM,GAAG,IAAI;IAClB,IAAI,CAACI,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACuG,eAAe,EAAE;IACtBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvCjF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuD,SAAS,CAAClD,IAAI,CAAC4E,QAAQ,EAAE,gBAAgB,EAAE1B,SAAS,CAACxF,OAAO,CAAC4C,MAAM,CAAC;EAC/G;EAEAuE,SAASA,CAACC,MAAa,EAAEC,KAAa;IACpC;IACA,IAAI,CAAClH,iBAAiB,GAAGkH,KAAK;IAC9B,IAAI,CAACnH,MAAM,GAAG,IAAI;IAClB,IAAI,CAACI,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACuG,eAAe,EAAE;IACtBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEAK,YAAYA,CAAA;IACV,IAAI,CAACpH,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC+C,eAAe,EAAE;IACtB,IAAI,CAACsE,cAAc,EAAE;IACrBT,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAO,SAASA,CAAA;IACP;IACA,IAAI,IAAI,CAACrH,iBAAiB,GAAG,IAAI,CAACE,kBAAkB,CAACuC,MAAM,GAAG,CAAC,EAAE;MAC/D,IAAI,CAACzC,iBAAiB,EAAE;MACxB,IAAI,CAACG,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACuG,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,IAAI,CAACzG,gBAAgB,GAAG,IAAI,CAACJ,OAAO,CAAC4C,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACxC,gBAAgB,EAAE;QACvB,IAAI,CAACD,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACL,OAAO,CAAC,IAAI,CAACI,gBAAgB,CAAC,CAACJ,OAAO;QACrE,IAAI,CAACM,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACuG,eAAe,EAAE;OACvB,MAAM;QACL,IAAI,CAACS,YAAY,EAAE;;;EAGzB;EAEAG,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAACtH,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACG,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACuG,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,IAAI,CAACzG,gBAAgB,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACA,gBAAgB,EAAE;QACvB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACL,OAAO,CAAC,IAAI,CAACI,gBAAgB,CAAC,CAACJ,OAAO;QACrE,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAACE,kBAAkB,CAACuC,MAAM,GAAG,CAAC;QAC3D,IAAI,CAACtC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACuG,eAAe,EAAE;OACvB,MAAM;QACL,IAAI,CAACS,YAAY,EAAE;;;EAGzB;EAEAI,eAAeA,CAAA;IACb,IAAI,IAAI,CAACrH,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACuC,MAAM,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAACvC,kBAAkB,CAAC,IAAI,CAACF,iBAAiB,CAAC,IAAI,IAAI,CAACE,kBAAkB,CAAC,CAAC,CAAC;;IAEtF;IACA,IAAI,IAAI,CAACL,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC4C,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC5C,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC4C,MAAM,GAAG,CAAC,EAAE;MACjF,OAAO,IAAI,CAAC5C,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC;;IAEnC,OAAO,IAAI;EACb;EAEA2H,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC3H,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC4C,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO,IAAI,CAAC5C,OAAO,CAAC,IAAI,CAACI,gBAAgB,CAAC,EAAEkC,IAAI;;IAElD,OAAO,IAAI;EACb;EAEA8C,WAAWA,CAAA;IACT,MAAMjB,KAAK,GAAG,IAAI,CAACuD,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEvD,KAAK,IAAIA,KAAK,CAACsB,QAAQ,IAAItB,KAAK,CAACsB,QAAQ,CAAC7C,MAAM,GAAG,CAAC,CAAC;EACjE;EAEAgF,eAAeA,CAAA;IACb,MAAMzD,KAAK,GAAG,IAAI,CAACuD,eAAe,EAAE;IACpC,OAAOvD,KAAK,EAAEsB,QAAQ,EAAE7C,MAAM,IAAI,CAAC;EACrC;EAEA;EACAiF,gBAAgBA,CAACR,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAAClH,iBAAiB,EAAE,OAAO,GAAG;IAC9C,IAAIkH,KAAK,GAAG,IAAI,CAAClH,iBAAiB,EAAE,OAAO,CAAC;IAE5C;IACA,OAAO,IAAI,CAACe,eAAe;EAC7B;EAEQ2F,eAAeA,CAAA;IACrB,IAAI,CAAC5D,eAAe,EAAE;IACtB,IAAI,CAAChC,iBAAiB,GAAGyE,IAAI,CAACoC,GAAG,EAAE;IACnC,IAAI,CAAC5G,eAAe,GAAG,CAAC;IAExB;IACA,IAAI,CAAC6G,mBAAmB,GAAGC,WAAW,CAAC,MAAK;MAC1C,IAAI,IAAI,CAAC/G,iBAAiB,EAAE;QAC1B,MAAMgH,OAAO,GAAGvC,IAAI,CAACoC,GAAG,EAAE,GAAG,IAAI,CAAC7G,iBAAiB;QACnD,IAAI,CAACC,eAAe,GAAGqF,IAAI,CAAC2B,GAAG,CAAED,OAAO,GAAG,IAAI,CAACjH,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;QAC1E,IAAI,CAACnB,GAAG,CAACsI,aAAa,EAAE,CAAC,CAAC;;IAE9B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACC,aAAa,GAAG3F,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC+E,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAACxG,aAAa,CAAC;EACxB;EAEQiC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACmF,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;;IAE3B,IAAI,IAAI,CAACL,mBAAmB,EAAE;MAC5BO,aAAa,CAAC,IAAI,CAACP,mBAAmB,CAAC;MACvC,IAAI,CAACA,mBAAmB,GAAG,IAAI;;IAEjC,IAAI,CAAC9G,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,CAAC;EAC1B;EAEA;EACAqH,YAAYA,CAACC,KAAiB;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACE,OAAO;IAC5B,MAAMC,WAAW,GAAGxF,MAAM,CAACC,UAAU;IAErC,IAAIqF,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAAClB,aAAa,EAAE;KACrB,MAAM,IAAIgB,MAAM,GAAIE,WAAW,GAAG,CAAC,GAAI,CAAC,EAAE;MACzC,IAAI,CAACnB,SAAS,EAAE;;EAEpB;EAEA;EACAoB,YAAYA,CAACC,MAAkB;IAC7B,IAAI,CAAC1H,cAAc,GAAGuE,IAAI,CAACoC,GAAG,EAAE;IAChC,IAAI,CAACgB,cAAc,GAAGrG,UAAU,CAAC,MAAK;MACpC,IAAI,CAACQ,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA8F,WAAWA,CAACF,MAAkB;IAC5B,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBT,YAAY,CAAC,IAAI,CAACS,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAE,UAAUA,CAACR,KAAiB;IAC1B,IAAI,IAAI,CAACM,cAAc,EAAE;MACvBT,YAAY,CAAC,IAAI,CAACS,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,MAAMG,aAAa,GAAGvD,IAAI,CAACoC,GAAG,EAAE,GAAG,IAAI,CAAC3G,cAAc;IACtD,IAAI8H,aAAa,GAAG,GAAG,EAAE;MACvB;MACA,MAAMC,KAAK,GAAGV,KAAK,CAACW,cAAc,CAAC,CAAC,CAAC;MACrC,IAAI,CAACZ,YAAY,CAAC;QAAEG,OAAO,EAAEQ,KAAK,CAACR;MAAO,CAAgB,CAAC;KAC5D,MAAM;MACL;MACA,IAAI,CAAC7B,eAAe,EAAE;;EAE1B;EAEA;EACAuC,iBAAiBA,CAAA;IACf,IAAI,CAAC9I,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA+I,kBAAkBA,CAACC,OAAY;IAC7B,IAAI,CAAChK,MAAM,CAACiK,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACrE,GAAG,CAAC,CAAC;EACjD;EAEA;EACAuE,SAASA,CAACF,OAAY;IACpBtH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqH,OAAO,CAAC;IAE/C,IAAI,CAACxJ,oBAAoB,CAAC0J,SAAS,CAAC;MAClCC,SAAS,EAAEH,OAAO,CAACrE,GAAG;MACtByE,IAAI,EAAEJ,OAAO,CAACI,IAAI;MAClBC,KAAK,EAAEL,OAAO,CAACK,KAAK;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAACxH,SAAS,CAAC;MACXyB,IAAI,EAAGgG,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC9F,OAAO,EAAE;UAClBhC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE6H,MAAM,CAAC;UAC1D;SACD,MAAM;UACL9H,OAAO,CAAC6C,KAAK,CAAC,gCAAgC,EAAEiF,MAAM,CAACC,OAAO,CAAC;;MAEnE,CAAC;MACDlF,KAAK,EAAGA,KAAK,IAAI;QACf7C,OAAO,CAAC6C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEA;EACAmF,aAAaA,CAACV,OAAY;IACxBtH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEqH,OAAO,CAAC;IAEnD,IAAI,CAACxJ,oBAAoB,CAACkK,aAAa,CAAC;MACtCP,SAAS,EAAEH,OAAO,CAACrE,GAAG;MACtByE,IAAI,EAAEJ,OAAO,CAACI,IAAI;MAClBC,KAAK,EAAEL,OAAO,CAACK,KAAK;MACpBE,SAAS,EAAE;KACZ,CAAC,CAACxH,SAAS,CAAC;MACXyB,IAAI,EAAGgG,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC9F,OAAO,EAAE;UAClBhC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE6H,MAAM,CAAC;UAC9D;SACD,MAAM;UACL9H,OAAO,CAAC6C,KAAK,CAAC,oCAAoC,EAAEiF,MAAM,CAACC,OAAO,CAAC;;MAEvE,CAAC;MACDlF,KAAK,EAAGA,KAAK,IAAI;QACf7C,OAAO,CAAC6C,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;EACAoF,MAAMA,CAACX,OAAY;IACjBtH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqH,OAAO,CAAC;IAEpD,IAAI,CAACxJ,oBAAoB,CAACmK,MAAM,CAAC;MAC/BR,SAAS,EAAEH,OAAO,CAACrE,GAAG;MACtByE,IAAI,EAAEJ,OAAO,CAACI,IAAI;MAClBC,KAAK,EAAEL,OAAO,CAACK,KAAK;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAACxH,SAAS,CAAC;MACXyB,IAAI,EAAGgG,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC9F,OAAO,EAAE;UAClBhC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6H,MAAM,CAAC;UAC1C;SACD,MAAM;UACL9H,OAAO,CAAC6C,KAAK,CAAC,oBAAoB,EAAEiF,MAAM,CAACC,OAAO,CAAC;;MAEvD,CAAC;MACDlF,KAAK,EAAGA,KAAK,IAAI;QACf7C,OAAO,CAAC6C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACAqF,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC1K,WAAW,CAAC2K,eAAe,EAAE;MACrC,IAAI,CAAC7K,MAAM,CAACiK,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMa,YAAY,GAAG,IAAI,CAAC1C,eAAe,EAAE;IAC3C,IAAI,CAAC0C,YAAY,EAAE;IAEnB,IAAI,CAAC7J,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,MAAM8J,QAAQ,GAAG,IAAI,CAAC9J,OAAO,GAAG,MAAM,GAAG,QAAQ;IACjD,IAAI,CAAChB,IAAI,CAAC+K,IAAI,CAAC,GAAGnL,WAAW,CAAC0E,MAAM,YAAYuG,YAAY,CAACnF,GAAG,IAAIoF,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAChI,SAAS,CAAC;MAC5FyB,IAAI,EAAGC,QAAQ,IAAI;QACjB/B,OAAO,CAACC,GAAG,CAAC,SAASoI,QAAQ,iBAAiB,EAAEtG,QAAQ,CAAC;QACzD;QACA,IAAI,IAAI,CAACnE,eAAe,CAAC2K,WAAW,EAAE,EAAE;UACtC;UACAvI,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChCuI,OAAO,EAAEJ,YAAY,CAACnF,GAAG;YACzBD,MAAM,EAAE,IAAI,CAACxF,WAAW,CAACiL,gBAAgB,EAAExF,GAAG;YAC9CyF,KAAK,EAAE,IAAI,CAACnK;WACb,CAAC;;MAEN,CAAC;MACDsE,KAAK,EAAGA,KAAK,IAAI;QACf7C,OAAO,CAAC6C,KAAK,CAAC,SAASwF,QAAQ,YAAY,EAAExF,KAAK,CAAC;QACnD;QACA,IAAI,CAACtE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC9B;KACD,CAAC;EACJ;EAEAoK,UAAUA,CAAA;IACR,MAAMP,YAAY,GAAG,IAAI,CAAC1C,eAAe,EAAE;IAC3C,IAAI,CAAC0C,YAAY,EAAE;IAEnB,IAAIQ,SAAS,CAACC,KAAK,EAAE;MACnB;MACAD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,YAAYV,YAAY,CAAC9H,IAAI,CAAC4E,QAAQ,EAAE;QAC/C6D,IAAI,EAAEX,YAAY,CAACY,OAAO;QAC1BxG,GAAG,EAAE,GAAGrF,WAAW,CAAC8L,WAAW,UAAUb,YAAY,CAACnF,GAAG;OAC1D,CAAC,CAACiG,IAAI,CAAC,MAAK;QACXlJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC;QACA,IAAI,CAACkJ,eAAe,CAACf,YAAY,CAACnF,GAAG,CAAC;MACxC,CAAC,CAAC,CAACmG,KAAK,CAAEvG,KAAK,IAAI;QACjB7C,OAAO,CAAC6C,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;KACH,MAAM;MACL;MACA,MAAMwG,QAAQ,GAAG,GAAGlM,WAAW,CAAC8L,WAAW,UAAUb,YAAY,CAACnF,GAAG,EAAE;MACvE2F,SAAS,CAACU,SAAS,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACH,IAAI,CAAC,MAAK;QAChDlJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACkJ,eAAe,CAACf,YAAY,CAACnF,GAAG,CAAC;QACtC;QACA,IAAI,CAACuG,SAAS,CAAC,iCAAiC,CAAC;MACnD,CAAC,CAAC,CAACJ,KAAK,CAAEvG,KAAK,IAAI;QACjB7C,OAAO,CAAC6C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEA4G,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACjM,WAAW,CAAC2K,eAAe,EAAE;MACrC,IAAI,CAAC7K,MAAM,CAACiK,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMa,YAAY,GAAG,IAAI,CAAC1C,eAAe,EAAE;IAC3C,IAAI,CAAC0C,YAAY,EAAE;IAEnB;IACA,IAAI,CAAC7K,IAAI,CAAC+K,IAAI,CAAC,GAAGnL,WAAW,CAAC0E,MAAM,YAAYuG,YAAY,CAACnF,GAAG,OAAO,EAAE,EAAE,CAAC,CAAC5C,SAAS,CAAC;MACrFyB,IAAI,EAAGC,QAAQ,IAAI;QACjB/B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE8B,QAAQ,CAAC;QAClD,IAAI,CAACyH,SAAS,CAAC,iCAAiC,CAAC;QACjD;QACA,IAAI,IAAI,CAAC5L,eAAe,CAAC2K,WAAW,EAAE,EAAE;UACtC;UACAvI,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChCuI,OAAO,EAAEJ,YAAY,CAACnF,GAAG;YACzBD,MAAM,EAAE,IAAI,CAACxF,WAAW,CAACiL,gBAAgB,EAAExF;WAC5C,CAAC;;MAEN,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACf7C,OAAO,CAAC6C,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAAC2G,SAAS,CAAC,uCAAuC,CAAC;MACzD;KACD,CAAC;EACJ;EAEQL,eAAeA,CAACX,OAAe;IACrC,IAAI,IAAI,CAAChL,WAAW,CAAC2K,eAAe,EAAE;MACpC,IAAI,CAAC5K,IAAI,CAAC+K,IAAI,CAAC,GAAGnL,WAAW,CAAC0E,MAAM,YAAY2G,OAAO,QAAQ,EAAE,EAAE,CAAC,CAACnI,SAAS,CAAC;QAC7EyB,IAAI,EAAGC,QAAQ,IAAI;UACjB/B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8B,QAAQ,CAAC;QAC/C,CAAC;QACDc,KAAK,EAAGA,KAAK,IAAI;UACf7C,OAAO,CAAC6C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEA;EACA6G,qBAAqBA,CAAClD,KAAY;IAChCA,KAAK,CAACmD,eAAe,EAAE;IACvB,MAAMvB,YAAY,GAAG,IAAI,CAAC1C,eAAe,EAAE;IAE3C,IAAI,CAAC0C,YAAY,EAAEwB,aAAa,EAAE;MAChC;;IAGF5J,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEmI,YAAY,CAACwB,aAAa,CAAC;IAE9E,QAAQxB,YAAY,CAACwB,aAAa,CAAClH,IAAI;MACrC,KAAK,SAAS;QACZ,IAAI0F,YAAY,CAACwB,aAAa,CAACnC,SAAS,EAAE;UACxC,IAAI,CAACnK,MAAM,CAACiK,QAAQ,CAAC,CAAC,UAAU,EAAEa,YAAY,CAACwB,aAAa,CAACnC,SAAS,CAAC,CAAC;SACzE,MAAM,IAAIW,YAAY,CAAC3E,QAAQ,IAAI2E,YAAY,CAAC3E,QAAQ,CAAC7C,MAAM,GAAG,CAAC,EAAE;UACpE;UACA,IAAI,CAACtD,MAAM,CAACiK,QAAQ,CAAC,CAAC,UAAU,EAAEa,YAAY,CAAC3E,QAAQ,CAAC,CAAC,CAAC,CAAC6D,OAAO,CAACrE,GAAG,CAAC,CAAC;;QAE1E;MAEF,KAAK,UAAU;QACb,IAAImF,YAAY,CAACwB,aAAa,CAACC,UAAU,EAAE;UACzC,IAAI,CAACvM,MAAM,CAACiK,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9BuC,WAAW,EAAE;cAAEC,QAAQ,EAAE3B,YAAY,CAACwB,aAAa,CAACC;YAAU;WAC/D,CAAC;;QAEJ;MAEF,KAAK,OAAO;QACV,IAAIzB,YAAY,CAACwB,aAAa,CAACI,OAAO,EAAE;UACtC,IAAI,CAAC1M,MAAM,CAACiK,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9BuC,WAAW,EAAE;cAAEG,KAAK,EAAE7B,YAAY,CAACwB,aAAa,CAACI;YAAO;WACzD,CAAC;;QAEJ;MAEF,KAAK,YAAY;QACf,IAAI5B,YAAY,CAACwB,aAAa,CAACM,YAAY,EAAE;UAC3C,IAAI,CAAC5M,MAAM,CAACiK,QAAQ,CAAC,CAAC,aAAa,EAAEa,YAAY,CAACwB,aAAa,CAACM,YAAY,CAAC,CAAC;;QAEhF;MAEF;QACElK,OAAO,CAAC0B,IAAI,CAAC,8BAA8B,EAAE0G,YAAY,CAACwB,aAAa,CAAClH,IAAI,CAAC;;IAGjF;IACA,IAAI,CAACyH,uBAAuB,CAAC/B,YAAY,CAACnF,GAAG,EAAEmF,YAAY,CAACwB,aAAa,CAAC;EAC5E;EAEA;EACAQ,oBAAoBA,CAAA;IAClB,MAAMhC,YAAY,GAAG,IAAI,CAAC1C,eAAe,EAAE;IAE3C,IAAI,CAAC0C,YAAY,EAAEwB,aAAa,EAAE;MAChC,OAAO,EAAE;;IAGX,QAAQxB,YAAY,CAACwB,aAAa,CAAClH,IAAI;MACrC,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,YAAY;QACf,OAAO,iBAAiB;MAC1B;QACE,OAAO,cAAc;;EAE3B;EAEA;EACQyH,uBAAuBA,CAAC3B,OAAe,EAAEoB,aAAkB;IACjE,IAAI,IAAI,CAACpM,WAAW,CAAC2K,eAAe,EAAE;MACpC,IAAI,CAAC5K,IAAI,CAAC+K,IAAI,CAAC,GAAGnL,WAAW,CAAC0E,MAAM,YAAY2G,OAAO,QAAQ,EAAE;QAC/D6B,WAAW,EAAET,aAAa,CAAClH,IAAI;QAC/B4H,SAAS,EAAEV,aAAa,CAACnC,SAAS,IAAImC,aAAa,CAACC,UAAU,IAAID,aAAa,CAACI,OAAO,IAAIJ,aAAa,CAACM;OAC1G,CAAC,CAAC7J,SAAS,CAAC;QACXyB,IAAI,EAAGC,QAAQ,IAAI;UACjB/B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8B,QAAQ,CAAC;QACxD,CAAC;QACDc,KAAK,EAAGA,KAAK,IAAI;UACf7C,OAAO,CAAC6C,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;OACD,CAAC;;EAEN;EAEQ2G,SAASA,CAACzB,OAAe;IAC/B;IACA,MAAMwC,KAAK,GAAGzF,QAAQ,CAAC0F,aAAa,CAAC,KAAK,CAAC;IAC3CD,KAAK,CAACE,WAAW,GAAG1C,OAAO;IAC3BwC,KAAK,CAACvF,KAAK,CAAC0F,OAAO,GAAG;;;;;;;;;;;KAWrB;IACD5F,QAAQ,CAACC,IAAI,CAAC4F,WAAW,CAACJ,KAAK,CAAC;IAEhC9J,UAAU,CAAC,MAAK;MACdqE,QAAQ,CAACC,IAAI,CAAC6F,WAAW,CAACL,KAAK,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAM,KAAKA,CAAA;IACH,IAAI,CAACvN,MAAM,CAACiK,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA;EACAuD,UAAUA,CAACC,UAAqC;IAC9C,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IAEjC,MAAMjF,GAAG,GAAG,IAAIpC,IAAI,EAAE;IACtB,IAAIsH,IAAU;IAEd,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;MAClCC,IAAI,GAAG,IAAItH,IAAI,CAACqH,UAAU,CAAC;KAC5B,MAAM;MACLC,IAAI,GAAGD,UAAU;;IAGnB;IACA,IAAIE,KAAK,CAACD,IAAI,CAAC/G,OAAO,EAAE,CAAC,EAAE;MACzB,OAAO,SAAS;;IAGlB,MAAMiH,aAAa,GAAG3G,IAAI,CAAC4G,KAAK,CAAC,CAACrF,GAAG,CAAC7B,OAAO,EAAE,GAAG+G,IAAI,CAAC/G,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIiH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAME,WAAW,GAAG7G,IAAI,CAAC4G,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIE,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAG9G,IAAI,CAAC4G,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEQ9F,cAAcA,CAAA;IACpB,MAAM+F,MAAM,GAAGxG,QAAQ,CAACyG,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACxK,OAAO,CAAC0K,KAAK,IAAG;MACrB,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAGAC,aAAaA,CAAClF,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACtI,MAAM,EAAE;IAElB,QAAQsI,KAAK,CAACmF,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAAClG,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACD,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACF,YAAY,EAAE;QACnB;;EAEN;CACD;AAt1BgDsG,UAAA,EAA9C7O,SAAS,CAAC,eAAe,EAAE;EAAE8O,MAAM,EAAE;AAAK,CAAE,CAAC,C,6DAA4C;AAC5CD,UAAA,EAA7C7O,SAAS,CAAC,cAAc,EAAE;EAAE8O,MAAM,EAAE;AAAK,CAAE,CAAC,C,4DAA2C;AAC5CD,UAAA,EAA3C7O,SAAS,CAAC,YAAY,EAAE;EAAE8O,MAAM,EAAE;AAAK,CAAE,CAAC,C,0DAA2C;AAoHtFD,UAAA,EADC5O,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,C,sDAKzC;AA6sBD4O,UAAA,EADC5O,YAAY,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,2DAe5C;AAt1BUI,uBAAuB,GAAAwO,UAAA,EAPnC9O,SAAS,CAAC;EACTgP,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC/O,YAAY,EAAEC,WAAW,EAAE+O,cAAc,CAAC;EACpDC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC;CAChD,CAAC,C,EACW/O,uBAAuB,CAu1BnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}