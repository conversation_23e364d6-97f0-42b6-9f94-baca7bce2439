{"ast": null, "code": "import { map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nexport class RoleAccessService {\n  constructor(authService) {\n    this.authService = authService;\n    this.roleHierarchy = {\n      'buyer': {\n        name: 'Buyer',\n        level: 1,\n        permissions: {\n          'profile': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: false\n          },\n          'orders': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: false\n          },\n          'cart': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'wishlist': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'reviews': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'posts': {\n            read: true,\n            write: false,\n            delete: false,\n            admin: false\n          },\n          'stories': {\n            read: true,\n            write: false,\n            delete: false,\n            admin: false\n          },\n          'products': {\n            read: true,\n            write: false,\n            delete: false,\n            admin: false\n          },\n          'analytics': {\n            read: false,\n            write: false,\n            delete: false,\n            admin: false\n          },\n          'users': {\n            read: false,\n            write: false,\n            delete: false,\n            admin: false\n          },\n          'settings': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: false\n          }\n        }\n      },\n      'seller': {\n        name: 'Seller',\n        level: 2,\n        permissions: {\n          'profile': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: false\n          },\n          'orders': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: false\n          },\n          'cart': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'wishlist': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'reviews': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'posts': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'stories': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'products': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: false\n          },\n          'analytics': {\n            read: true,\n            write: false,\n            delete: false,\n            admin: false\n          },\n          'users': {\n            read: false,\n            write: false,\n            delete: false,\n            admin: false\n          },\n          'settings': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: false\n          },\n          'vendor': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: false\n          }\n        }\n      },\n      'admin': {\n        name: 'Admin',\n        level: 3,\n        permissions: {\n          'profile': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'orders': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'cart': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'wishlist': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'reviews': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'posts': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'stories': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'products': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'analytics': {\n            read: true,\n            write: true,\n            delete: false,\n            admin: true\n          },\n          'users': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'settings': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'vendor': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'categories': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'brands': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          }\n        }\n      },\n      'super_admin': {\n        name: 'Super Admin',\n        level: 4,\n        permissions: {\n          'profile': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'orders': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'cart': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'wishlist': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'reviews': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'posts': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'stories': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'products': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'analytics': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'users': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'settings': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'vendor': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'categories': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'brands': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          },\n          'system': {\n            read: true,\n            write: true,\n            delete: true,\n            admin: true\n          }\n        }\n      }\n    };\n  }\n  // Get current user role\n  getCurrentUserRole() {\n    const user = this.authService.currentUserValue;\n    return user?.role || 'buyer';\n  }\n  // Get role information\n  getRoleInfo(role) {\n    const userRole = role || this.getCurrentUserRole();\n    return this.roleHierarchy[userRole] || null;\n  }\n  // Check if user has permission for a specific action\n  hasPermission(module, action) {\n    const roleInfo = this.getRoleInfo();\n    if (!roleInfo) return false;\n    const modulePermissions = roleInfo.permissions[module];\n    if (!modulePermissions) return false;\n    return modulePermissions[action];\n  }\n  // Check if user can read a module\n  canRead(module) {\n    return this.hasPermission(module, 'read');\n  }\n  // Check if user can write to a module\n  canWrite(module) {\n    return this.hasPermission(module, 'write');\n  }\n  // Check if user can delete from a module\n  canDelete(module) {\n    return this.hasPermission(module, 'delete');\n  }\n  // Check if user has admin access to a module\n  hasAdminAccess(module) {\n    return this.hasPermission(module, 'admin');\n  }\n  // Check if user role is at least a certain level\n  hasRoleLevel(minimumLevel) {\n    const roleInfo = this.getRoleInfo();\n    return roleInfo ? roleInfo.level >= minimumLevel : false;\n  }\n  // Check if user is buyer\n  isBuyer() {\n    return this.getCurrentUserRole() === 'buyer';\n  }\n  // Check if user is seller\n  isSeller() {\n    return this.getCurrentUserRole() === 'seller';\n  }\n  // Check if user is admin\n  isAdmin() {\n    return this.getCurrentUserRole() === 'admin';\n  }\n  // Check if user is super admin\n  isSuperAdmin() {\n    return this.getCurrentUserRole() === 'super_admin';\n  }\n  // Check if user has elevated privileges (admin or super admin)\n  hasElevatedPrivileges() {\n    return this.isAdmin() || this.isSuperAdmin();\n  }\n  // Get available modules for current user\n  getAvailableModules() {\n    const roleInfo = this.getRoleInfo();\n    if (!roleInfo) return [];\n    return Object.keys(roleInfo.permissions).filter(module => roleInfo.permissions[module].read);\n  }\n  // Get editable modules for current user\n  getEditableModules() {\n    const roleInfo = this.getRoleInfo();\n    if (!roleInfo) return [];\n    return Object.keys(roleInfo.permissions).filter(module => roleInfo.permissions[module].write);\n  }\n  // Observable methods for reactive programming\n  canRead$(module) {\n    return this.authService.currentUser$.pipe(map(() => this.canRead(module)));\n  }\n  canWrite$(module) {\n    return this.authService.currentUser$.pipe(map(() => this.canWrite(module)));\n  }\n  canDelete$(module) {\n    return this.authService.currentUser$.pipe(map(() => this.canDelete(module)));\n  }\n  hasAdminAccess$(module) {\n    return this.authService.currentUser$.pipe(map(() => this.hasAdminAccess(module)));\n  }\n  // Get filtered navigation items based on role\n  getFilteredNavigation(navigationItems) {\n    return navigationItems.filter(item => {\n      if (item.requiredRole) {\n        return this.hasRoleLevel(this.roleHierarchy[item.requiredRole]?.level || 0);\n      }\n      if (item.requiredPermission) {\n        const [module, action] = item.requiredPermission.split(':');\n        return this.hasPermission(module, action);\n      }\n      return true;\n    });\n  }\n  // Get role-specific settings visibility\n  getSettingsVisibility() {\n    const role = this.getCurrentUserRole();\n    const baseSettings = {\n      'profile': true,\n      'notifications': true,\n      'privacy': true,\n      'security': true\n    };\n    const roleSpecificSettings = {\n      'buyer': {\n        ...baseSettings,\n        'orders': true,\n        'addresses': true,\n        'payment': true\n      },\n      'seller': {\n        ...baseSettings,\n        'vendor': true,\n        'products': true,\n        'analytics': true,\n        'orders': true,\n        'payments': true\n      },\n      'admin': {\n        ...baseSettings,\n        'users': true,\n        'products': true,\n        'orders': true,\n        'analytics': true,\n        'system': true,\n        'vendor': true\n      },\n      'super_admin': {\n        ...baseSettings,\n        'users': true,\n        'products': true,\n        'orders': true,\n        'analytics': true,\n        'system': true,\n        'vendor': true,\n        'database': true,\n        'logs': true\n      }\n    };\n    return roleSpecificSettings[role] || baseSettings;\n  }\n  static {\n    this.ɵfac = function RoleAccessService_Factory(t) {\n      return new (t || RoleAccessService)(i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleAccessService,\n      factory: RoleAccessService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "RoleAccessService", "constructor", "authService", "roleHierarchy", "name", "level", "permissions", "read", "write", "delete", "admin", "getCurrentUserRole", "user", "currentUserValue", "role", "getRoleInfo", "userRole", "hasPermission", "module", "action", "roleInfo", "modulePermissions", "canRead", "canWrite", "canDelete", "hasAdminAccess", "hasRoleLevel", "minimumLevel", "<PERSON><PERSON><PERSON><PERSON>", "isSeller", "isAdmin", "isSuperAdmin", "hasElevatedPrivileges", "getAvailableModules", "Object", "keys", "filter", "getEditableModules", "canRead$", "currentUser$", "pipe", "canWrite$", "canDelete$", "hasAdminAccess$", "getFilteredNavigation", "navigationItems", "item", "requiredRole", "requiredPermission", "split", "getSettingsVisibility", "baseSettings", "roleSpecificSettings", "i0", "ɵɵinject", "i1", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\role-access.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { AuthService } from './auth.service';\nimport { Observable, map } from 'rxjs';\n\nexport interface RolePermissions {\n  [key: string]: {\n    read: boolean;\n    write: boolean;\n    delete: boolean;\n    admin: boolean;\n  };\n}\n\nexport interface UserRole {\n  name: string;\n  level: number;\n  permissions: RolePermissions;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RoleAccessService {\n\n  private roleHierarchy: { [key: string]: UserRole } = {\n    'buyer': {\n      name: 'Buyer',\n      level: 1,\n      permissions: {\n        'profile': { read: true, write: true, delete: false, admin: false },\n        'orders': { read: true, write: true, delete: false, admin: false },\n        'cart': { read: true, write: true, delete: true, admin: false },\n        'wishlist': { read: true, write: true, delete: true, admin: false },\n        'reviews': { read: true, write: true, delete: true, admin: false },\n        'posts': { read: true, write: false, delete: false, admin: false },\n        'stories': { read: true, write: false, delete: false, admin: false },\n        'products': { read: true, write: false, delete: false, admin: false },\n        'analytics': { read: false, write: false, delete: false, admin: false },\n        'users': { read: false, write: false, delete: false, admin: false },\n        'settings': { read: true, write: true, delete: false, admin: false }\n      }\n    },\n    'seller': {\n      name: 'Seller',\n      level: 2,\n      permissions: {\n        'profile': { read: true, write: true, delete: false, admin: false },\n        'orders': { read: true, write: true, delete: false, admin: false },\n        'cart': { read: true, write: true, delete: true, admin: false },\n        'wishlist': { read: true, write: true, delete: true, admin: false },\n        'reviews': { read: true, write: true, delete: true, admin: false },\n        'posts': { read: true, write: true, delete: true, admin: false },\n        'stories': { read: true, write: true, delete: true, admin: false },\n        'products': { read: true, write: true, delete: true, admin: false },\n        'analytics': { read: true, write: false, delete: false, admin: false },\n        'users': { read: false, write: false, delete: false, admin: false },\n        'settings': { read: true, write: true, delete: false, admin: false },\n        'vendor': { read: true, write: true, delete: false, admin: false }\n      }\n    },\n    'admin': {\n      name: 'Admin',\n      level: 3,\n      permissions: {\n        'profile': { read: true, write: true, delete: true, admin: true },\n        'orders': { read: true, write: true, delete: true, admin: true },\n        'cart': { read: true, write: true, delete: true, admin: true },\n        'wishlist': { read: true, write: true, delete: true, admin: true },\n        'reviews': { read: true, write: true, delete: true, admin: true },\n        'posts': { read: true, write: true, delete: true, admin: true },\n        'stories': { read: true, write: true, delete: true, admin: true },\n        'products': { read: true, write: true, delete: true, admin: true },\n        'analytics': { read: true, write: true, delete: false, admin: true },\n        'users': { read: true, write: true, delete: true, admin: true },\n        'settings': { read: true, write: true, delete: true, admin: true },\n        'vendor': { read: true, write: true, delete: true, admin: true },\n        'categories': { read: true, write: true, delete: true, admin: true },\n        'brands': { read: true, write: true, delete: true, admin: true }\n      }\n    },\n    'super_admin': {\n      name: 'Super Admin',\n      level: 4,\n      permissions: {\n        'profile': { read: true, write: true, delete: true, admin: true },\n        'orders': { read: true, write: true, delete: true, admin: true },\n        'cart': { read: true, write: true, delete: true, admin: true },\n        'wishlist': { read: true, write: true, delete: true, admin: true },\n        'reviews': { read: true, write: true, delete: true, admin: true },\n        'posts': { read: true, write: true, delete: true, admin: true },\n        'stories': { read: true, write: true, delete: true, admin: true },\n        'products': { read: true, write: true, delete: true, admin: true },\n        'analytics': { read: true, write: true, delete: true, admin: true },\n        'users': { read: true, write: true, delete: true, admin: true },\n        'settings': { read: true, write: true, delete: true, admin: true },\n        'vendor': { read: true, write: true, delete: true, admin: true },\n        'categories': { read: true, write: true, delete: true, admin: true },\n        'brands': { read: true, write: true, delete: true, admin: true },\n        'system': { read: true, write: true, delete: true, admin: true }\n      }\n    }\n  };\n\n  constructor(private authService: AuthService) {}\n\n  // Get current user role\n  getCurrentUserRole(): string {\n    const user = this.authService.currentUserValue;\n    return user?.role || 'buyer';\n  }\n\n  // Get role information\n  getRoleInfo(role?: string): UserRole | null {\n    const userRole = role || this.getCurrentUserRole();\n    return this.roleHierarchy[userRole] || null;\n  }\n\n  // Check if user has permission for a specific action\n  hasPermission(module: string, action: 'read' | 'write' | 'delete' | 'admin'): boolean {\n    const roleInfo = this.getRoleInfo();\n    if (!roleInfo) return false;\n\n    const modulePermissions = roleInfo.permissions[module];\n    if (!modulePermissions) return false;\n\n    return modulePermissions[action];\n  }\n\n  // Check if user can read a module\n  canRead(module: string): boolean {\n    return this.hasPermission(module, 'read');\n  }\n\n  // Check if user can write to a module\n  canWrite(module: string): boolean {\n    return this.hasPermission(module, 'write');\n  }\n\n  // Check if user can delete from a module\n  canDelete(module: string): boolean {\n    return this.hasPermission(module, 'delete');\n  }\n\n  // Check if user has admin access to a module\n  hasAdminAccess(module: string): boolean {\n    return this.hasPermission(module, 'admin');\n  }\n\n  // Check if user role is at least a certain level\n  hasRoleLevel(minimumLevel: number): boolean {\n    const roleInfo = this.getRoleInfo();\n    return roleInfo ? roleInfo.level >= minimumLevel : false;\n  }\n\n  // Check if user is buyer\n  isBuyer(): boolean {\n    return this.getCurrentUserRole() === 'buyer';\n  }\n\n  // Check if user is seller\n  isSeller(): boolean {\n    return this.getCurrentUserRole() === 'seller';\n  }\n\n  // Check if user is admin\n  isAdmin(): boolean {\n    return this.getCurrentUserRole() === 'admin';\n  }\n\n  // Check if user is super admin\n  isSuperAdmin(): boolean {\n    return this.getCurrentUserRole() === 'super_admin';\n  }\n\n  // Check if user has elevated privileges (admin or super admin)\n  hasElevatedPrivileges(): boolean {\n    return this.isAdmin() || this.isSuperAdmin();\n  }\n\n  // Get available modules for current user\n  getAvailableModules(): string[] {\n    const roleInfo = this.getRoleInfo();\n    if (!roleInfo) return [];\n\n    return Object.keys(roleInfo.permissions).filter(module => \n      roleInfo.permissions[module].read\n    );\n  }\n\n  // Get editable modules for current user\n  getEditableModules(): string[] {\n    const roleInfo = this.getRoleInfo();\n    if (!roleInfo) return [];\n\n    return Object.keys(roleInfo.permissions).filter(module => \n      roleInfo.permissions[module].write\n    );\n  }\n\n  // Observable methods for reactive programming\n  canRead$(module: string): Observable<boolean> {\n    return this.authService.currentUser$.pipe(\n      map(() => this.canRead(module))\n    );\n  }\n\n  canWrite$(module: string): Observable<boolean> {\n    return this.authService.currentUser$.pipe(\n      map(() => this.canWrite(module))\n    );\n  }\n\n  canDelete$(module: string): Observable<boolean> {\n    return this.authService.currentUser$.pipe(\n      map(() => this.canDelete(module))\n    );\n  }\n\n  hasAdminAccess$(module: string): Observable<boolean> {\n    return this.authService.currentUser$.pipe(\n      map(() => this.hasAdminAccess(module))\n    );\n  }\n\n  // Get filtered navigation items based on role\n  getFilteredNavigation(navigationItems: any[]): any[] {\n    return navigationItems.filter(item => {\n      if (item.requiredRole) {\n        return this.hasRoleLevel(this.roleHierarchy[item.requiredRole]?.level || 0);\n      }\n      if (item.requiredPermission) {\n        const [module, action] = item.requiredPermission.split(':');\n        return this.hasPermission(module, action as any);\n      }\n      return true;\n    });\n  }\n\n  // Get role-specific settings visibility\n  getSettingsVisibility(): { [key: string]: boolean } {\n    const role = this.getCurrentUserRole();\n    \n    const baseSettings = {\n      'profile': true,\n      'notifications': true,\n      'privacy': true,\n      'security': true\n    };\n\n    const roleSpecificSettings: { [key: string]: { [key: string]: boolean } } = {\n      'buyer': {\n        ...baseSettings,\n        'orders': true,\n        'addresses': true,\n        'payment': true\n      },\n      'seller': {\n        ...baseSettings,\n        'vendor': true,\n        'products': true,\n        'analytics': true,\n        'orders': true,\n        'payments': true\n      },\n      'admin': {\n        ...baseSettings,\n        'users': true,\n        'products': true,\n        'orders': true,\n        'analytics': true,\n        'system': true,\n        'vendor': true\n      },\n      'super_admin': {\n        ...baseSettings,\n        'users': true,\n        'products': true,\n        'orders': true,\n        'analytics': true,\n        'system': true,\n        'vendor': true,\n        'database': true,\n        'logs': true\n      }\n    };\n\n    return roleSpecificSettings[role] || baseSettings;\n  }\n}\n"], "mappings": "AAEA,SAAqBA,GAAG,QAAQ,MAAM;;;AAoBtC,OAAM,MAAOC,iBAAiB;EAiF5BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA/EvB,KAAAC,aAAa,GAAgC;MACnD,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE;UACX,SAAS,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACnE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UAClE,MAAM,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC/D,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UACnE,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UAClE,OAAO,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UAClE,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACpE,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACrE,WAAW,EAAE;YAAEH,IAAI,EAAE,KAAK;YAAEC,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACvE,OAAO,EAAE;YAAEH,IAAI,EAAE,KAAK;YAAEC,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACnE,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK;;OAErE;MACD,QAAQ,EAAE;QACRN,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE;UACX,SAAS,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACnE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UAClE,MAAM,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC/D,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UACnE,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UAClE,OAAO,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UAChE,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UAClE,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAE;UACnE,WAAW,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACtE,OAAO,EAAE;YAAEH,IAAI,EAAE,KAAK;YAAEC,KAAK,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACnE,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAE;UACpE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK;;OAEnE;MACD,OAAO,EAAE;QACPN,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE;UACX,SAAS,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAChE,MAAM,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC9D,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAClE,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjE,OAAO,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC/D,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjE,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAClE,WAAW,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpE,OAAO,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC/D,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAClE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAChE,YAAY,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI;;OAEjE;MACD,aAAa,EAAE;QACbN,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE;UACX,SAAS,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAChE,MAAM,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC9D,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAClE,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjE,OAAO,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC/D,SAAS,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACjE,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAClE,WAAW,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACnE,OAAO,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC/D,UAAU,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAClE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAChE,YAAY,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,CAAE;UAChE,QAAQ,EAAE;YAAEH,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI;;;KAGnE;EAE8C;EAE/C;EACAC,kBAAkBA,CAAA;IAChB,MAAMC,IAAI,GAAG,IAAI,CAACV,WAAW,CAACW,gBAAgB;IAC9C,OAAOD,IAAI,EAAEE,IAAI,IAAI,OAAO;EAC9B;EAEA;EACAC,WAAWA,CAACD,IAAa;IACvB,MAAME,QAAQ,GAAGF,IAAI,IAAI,IAAI,CAACH,kBAAkB,EAAE;IAClD,OAAO,IAAI,CAACR,aAAa,CAACa,QAAQ,CAAC,IAAI,IAAI;EAC7C;EAEA;EACAC,aAAaA,CAACC,MAAc,EAAEC,MAA6C;IACzE,MAAMC,QAAQ,GAAG,IAAI,CAACL,WAAW,EAAE;IACnC,IAAI,CAACK,QAAQ,EAAE,OAAO,KAAK;IAE3B,MAAMC,iBAAiB,GAAGD,QAAQ,CAACd,WAAW,CAACY,MAAM,CAAC;IACtD,IAAI,CAACG,iBAAiB,EAAE,OAAO,KAAK;IAEpC,OAAOA,iBAAiB,CAACF,MAAM,CAAC;EAClC;EAEA;EACAG,OAAOA,CAACJ,MAAc;IACpB,OAAO,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,MAAM,CAAC;EAC3C;EAEA;EACAK,QAAQA,CAACL,MAAc;IACrB,OAAO,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,OAAO,CAAC;EAC5C;EAEA;EACAM,SAASA,CAACN,MAAc;IACtB,OAAO,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,QAAQ,CAAC;EAC7C;EAEA;EACAO,cAAcA,CAACP,MAAc;IAC3B,OAAO,IAAI,CAACD,aAAa,CAACC,MAAM,EAAE,OAAO,CAAC;EAC5C;EAEA;EACAQ,YAAYA,CAACC,YAAoB;IAC/B,MAAMP,QAAQ,GAAG,IAAI,CAACL,WAAW,EAAE;IACnC,OAAOK,QAAQ,GAAGA,QAAQ,CAACf,KAAK,IAAIsB,YAAY,GAAG,KAAK;EAC1D;EAEA;EACAC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACjB,kBAAkB,EAAE,KAAK,OAAO;EAC9C;EAEA;EACAkB,QAAQA,CAAA;IACN,OAAO,IAAI,CAAClB,kBAAkB,EAAE,KAAK,QAAQ;EAC/C;EAEA;EACAmB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACnB,kBAAkB,EAAE,KAAK,OAAO;EAC9C;EAEA;EACAoB,YAAYA,CAAA;IACV,OAAO,IAAI,CAACpB,kBAAkB,EAAE,KAAK,aAAa;EACpD;EAEA;EACAqB,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACF,OAAO,EAAE,IAAI,IAAI,CAACC,YAAY,EAAE;EAC9C;EAEA;EACAE,mBAAmBA,CAAA;IACjB,MAAMb,QAAQ,GAAG,IAAI,CAACL,WAAW,EAAE;IACnC,IAAI,CAACK,QAAQ,EAAE,OAAO,EAAE;IAExB,OAAOc,MAAM,CAACC,IAAI,CAACf,QAAQ,CAACd,WAAW,CAAC,CAAC8B,MAAM,CAAClB,MAAM,IACpDE,QAAQ,CAACd,WAAW,CAACY,MAAM,CAAC,CAACX,IAAI,CAClC;EACH;EAEA;EACA8B,kBAAkBA,CAAA;IAChB,MAAMjB,QAAQ,GAAG,IAAI,CAACL,WAAW,EAAE;IACnC,IAAI,CAACK,QAAQ,EAAE,OAAO,EAAE;IAExB,OAAOc,MAAM,CAACC,IAAI,CAACf,QAAQ,CAACd,WAAW,CAAC,CAAC8B,MAAM,CAAClB,MAAM,IACpDE,QAAQ,CAACd,WAAW,CAACY,MAAM,CAAC,CAACV,KAAK,CACnC;EACH;EAEA;EACA8B,QAAQA,CAACpB,MAAc;IACrB,OAAO,IAAI,CAAChB,WAAW,CAACqC,YAAY,CAACC,IAAI,CACvCzC,GAAG,CAAC,MAAM,IAAI,CAACuB,OAAO,CAACJ,MAAM,CAAC,CAAC,CAChC;EACH;EAEAuB,SAASA,CAACvB,MAAc;IACtB,OAAO,IAAI,CAAChB,WAAW,CAACqC,YAAY,CAACC,IAAI,CACvCzC,GAAG,CAAC,MAAM,IAAI,CAACwB,QAAQ,CAACL,MAAM,CAAC,CAAC,CACjC;EACH;EAEAwB,UAAUA,CAACxB,MAAc;IACvB,OAAO,IAAI,CAAChB,WAAW,CAACqC,YAAY,CAACC,IAAI,CACvCzC,GAAG,CAAC,MAAM,IAAI,CAACyB,SAAS,CAACN,MAAM,CAAC,CAAC,CAClC;EACH;EAEAyB,eAAeA,CAACzB,MAAc;IAC5B,OAAO,IAAI,CAAChB,WAAW,CAACqC,YAAY,CAACC,IAAI,CACvCzC,GAAG,CAAC,MAAM,IAAI,CAAC0B,cAAc,CAACP,MAAM,CAAC,CAAC,CACvC;EACH;EAEA;EACA0B,qBAAqBA,CAACC,eAAsB;IAC1C,OAAOA,eAAe,CAACT,MAAM,CAACU,IAAI,IAAG;MACnC,IAAIA,IAAI,CAACC,YAAY,EAAE;QACrB,OAAO,IAAI,CAACrB,YAAY,CAAC,IAAI,CAACvB,aAAa,CAAC2C,IAAI,CAACC,YAAY,CAAC,EAAE1C,KAAK,IAAI,CAAC,CAAC;;MAE7E,IAAIyC,IAAI,CAACE,kBAAkB,EAAE;QAC3B,MAAM,CAAC9B,MAAM,EAAEC,MAAM,CAAC,GAAG2B,IAAI,CAACE,kBAAkB,CAACC,KAAK,CAAC,GAAG,CAAC;QAC3D,OAAO,IAAI,CAAChC,aAAa,CAACC,MAAM,EAAEC,MAAa,CAAC;;MAElD,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEA;EACA+B,qBAAqBA,CAAA;IACnB,MAAMpC,IAAI,GAAG,IAAI,CAACH,kBAAkB,EAAE;IAEtC,MAAMwC,YAAY,GAAG;MACnB,SAAS,EAAE,IAAI;MACf,eAAe,EAAE,IAAI;MACrB,SAAS,EAAE,IAAI;MACf,UAAU,EAAE;KACb;IAED,MAAMC,oBAAoB,GAAkD;MAC1E,OAAO,EAAE;QACP,GAAGD,YAAY;QACf,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE;OACZ;MACD,QAAQ,EAAE;QACR,GAAGA,YAAY;QACf,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,IAAI;QACjB,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE;OACb;MACD,OAAO,EAAE;QACP,GAAGA,YAAY;QACf,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,IAAI;QACjB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE;OACX;MACD,aAAa,EAAE;QACb,GAAGA,YAAY;QACf,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,IAAI;QACjB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAI;QAChB,MAAM,EAAE;;KAEX;IAED,OAAOC,oBAAoB,CAACtC,IAAI,CAAC,IAAIqC,YAAY;EACnD;;;uBAzQWnD,iBAAiB,EAAAqD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAjBxD,iBAAiB;MAAAyD,OAAA,EAAjBzD,iBAAiB,CAAA0D,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}