{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject } from 'rxjs';\nlet DynamicProfileComponent = class DynamicProfileComponent {\n  constructor(roleManagementService, router) {\n    this.roleManagementService = roleManagementService;\n    this.router = router;\n    this.userProfile = null;\n    this.roleConfig = null;\n    this.profileLayout = 'specialist';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (this.userProfile?.role) {\n      this.roleConfig = this.roleManagementService.getRoleConfig(this.userProfile.role);\n      this.profileLayout = this.roleConfig.profileLayout;\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  getCoverGradient() {\n    if (!this.roleConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n    const color = this.roleConfig.color;\n    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n  }\n  getProfileActions() {\n    if (!this.userProfile?.role) return [];\n    const baseActions = [{\n      label: 'Edit Profile',\n      icon: 'fas fa-edit',\n      action: 'edit',\n      color: '#4ECDC4'\n    }, {\n      label: 'Settings',\n      icon: 'fas fa-cog',\n      action: 'settings',\n      color: '#45B7D1'\n    }];\n    // Add role-specific actions\n    if (this.roleManagementService.isManager(this.userProfile.role)) {\n      baseActions.push({\n        label: 'Team Management',\n        icon: 'fas fa-users',\n        action: 'team',\n        color: '#96CEB4'\n      }, {\n        label: 'Reports',\n        icon: 'fas fa-chart-bar',\n        action: 'reports',\n        color: '#FFEAA7'\n      });\n    }\n    if (this.userProfile.role === 'super_admin' || this.userProfile.role === 'admin') {\n      baseActions.push({\n        label: 'Admin Panel',\n        icon: 'fas fa-shield-alt',\n        action: 'admin',\n        color: '#FF6B6B'\n      });\n    }\n    return baseActions;\n  }\n  getProfileStats() {\n    // This would be populated based on role and real data\n    // For now, returning mock data based on role\n    if (!this.userProfile?.role) return [];\n    const role = this.userProfile.role;\n    if (role.includes('sales')) {\n      return [{\n        label: 'Sales This Month',\n        value: '₹2.4M',\n        icon: 'fas fa-rupee-sign',\n        color: '#45B7D1',\n        trend: 'up',\n        trendValue: '+12%'\n      }, {\n        label: 'Deals Closed',\n        value: 47,\n        icon: 'fas fa-handshake',\n        color: '#96CEB4',\n        trend: 'up',\n        trendValue: '+8%'\n      }, {\n        label: 'Target Achievement',\n        value: '94%',\n        icon: 'fas fa-target',\n        color: '#FFEAA7',\n        trend: 'stable'\n      }];\n    }\n    if (role.includes('marketing')) {\n      return [{\n        label: 'Campaign Reach',\n        value: '1.2M',\n        icon: 'fas fa-eye',\n        color: '#F38BA8',\n        trend: 'up',\n        trendValue: '+15%'\n      }, {\n        label: 'Engagement Rate',\n        value: '4.8%',\n        icon: 'fas fa-heart',\n        color: '#DDA0DD',\n        trend: 'up',\n        trendValue: '+0.3%'\n      }, {\n        label: 'Conversions',\n        value: 2847,\n        icon: 'fas fa-exchange-alt',\n        color: '#FFB6C1',\n        trend: 'down',\n        trendValue: '-2%'\n      }];\n    }\n    // Default stats for other roles\n    return [{\n      label: 'Tasks Completed',\n      value: 156,\n      icon: 'fas fa-check-circle',\n      color: '#6BCF7F',\n      trend: 'up',\n      trendValue: '+5%'\n    }, {\n      label: 'Projects Active',\n      value: 8,\n      icon: 'fas fa-project-diagram',\n      color: '#4ECDC4',\n      trend: 'stable'\n    }];\n  }\n  handleAction(action) {\n    switch (action) {\n      case 'edit':\n        this.router.navigate(['/profile/edit']);\n        break;\n      case 'settings':\n        this.router.navigate(['/profile/settings']);\n        break;\n      case 'team':\n        this.router.navigate(['/team/management']);\n        break;\n      case 'reports':\n        this.router.navigate(['/reports']);\n        break;\n      case 'admin':\n        this.router.navigate(['/admin']);\n        break;\n    }\n  }\n  getLastActiveText() {\n    if (!this.userProfile?.lastActive) return 'Unknown';\n    const now = new Date();\n    const lastActive = new Date(this.userProfile.lastActive);\n    const diffMs = now.getTime() - lastActive.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    if (diffMins < 1) return 'Just now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;\n    return `${Math.floor(diffMins / 1440)}d ago`;\n  }\n  getTrendIcon(trend) {\n    switch (trend) {\n      case 'up':\n        return 'fas fa-arrow-up';\n      case 'down':\n        return 'fas fa-arrow-down';\n      default:\n        return 'fas fa-minus';\n    }\n  }\n  // Mock data methods - these would be replaced with real data services\n  getTeamSize() {\n    return 12;\n  }\n  getDepartmentCount() {\n    return 3;\n  }\n  getStrategicInitiatives() {\n    return [];\n  }\n  getTeamMembers() {\n    return [];\n  }\n  getDepartmentGoals() {\n    return [];\n  }\n  getPersonalMetrics() {\n    return [];\n  }\n  getRecentAchievements() {\n    return [];\n  }\n  getSystemMetrics() {\n    return [];\n  }\n  getRecentActivities() {\n    return [];\n  }\n  getProgressColor(progress) {\n    return progress > 75 ? '#6BCF7F' : progress > 50 ? '#FFEAA7' : '#FF6B6B';\n  }\n};\n__decorate([Input()], DynamicProfileComponent.prototype, \"userProfile\", void 0);\nDynamicProfileComponent = __decorate([Component({\n  selector: 'app-dynamic-profile',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"dynamic-profile\" [attr.data-layout]=\"profileLayout\" [attr.data-role]=\"userProfile?.role\">\n      <!-- Profile Header -->\n      <div class=\"profile-header\">\n        <div class=\"profile-cover\" [style.background]=\"getCoverGradient()\">\n          <div class=\"profile-actions\">\n            <button \n              *ngFor=\"let action of getProfileActions()\" \n              class=\"action-btn\"\n              [style.background]=\"action.color\"\n              (click)=\"handleAction(action.action)\"\n              [title]=\"action.label\">\n              <i [class]=\"action.icon\"></i>\n            </button>\n          </div>\n        </div>\n        \n        <div class=\"profile-info\">\n          <div class=\"avatar-section\">\n            <div class=\"avatar-container\">\n              <img [src]=\"userProfile?.avatar || '/assets/images/default-avatar.png'\" \n                   [alt]=\"userProfile?.fullName\" \n                   class=\"profile-avatar\">\n              <div class=\"online-indicator\" *ngIf=\"userProfile?.isOnline\"></div>\n              <div class=\"role-badge\" [style.background]=\"roleConfig?.color\">\n                <i [class]=\"roleConfig?.icon\"></i>\n              </div>\n            </div>\n            \n            <div class=\"profile-details\">\n              <div class=\"name-section\">\n                <h1 class=\"profile-name\">{{ userProfile?.fullName }}</h1>\n                <div class=\"verification-badge\" *ngIf=\"userProfile?.isVerified\">\n                  <i class=\"fas fa-check-circle\"></i>\n                </div>\n              </div>\n              \n              <p class=\"profile-username\">@{{ userProfile?.username }}</p>\n              <div class=\"role-info\">\n                <span class=\"role-title\" [style.color]=\"roleConfig?.color\">\n                  {{ roleConfig?.displayName }}\n                </span>\n                <span class=\"department\">{{ roleConfig?.department | titlecase }}</span>\n              </div>\n              \n              <p class=\"profile-bio\" *ngIf=\"userProfile?.bio\">{{ userProfile?.bio }}</p>\n              \n              <div class=\"profile-meta\">\n                <div class=\"meta-item\" *ngIf=\"userProfile?.location\">\n                  <i class=\"fas fa-map-marker-alt\"></i>\n                  <span>{{ userProfile?.location }}</span>\n                </div>\n                <div class=\"meta-item\" *ngIf=\"userProfile?.joinDate\">\n                  <i class=\"fas fa-calendar-alt\"></i>\n                  <span>Joined {{ userProfile?.joinDate | date:'MMM yyyy' }}</span>\n                </div>\n                <div class=\"meta-item\">\n                  <i class=\"fas fa-clock\"></i>\n                  <span>Last active {{ getLastActiveText() }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Profile Stats -->\n      <div class=\"profile-stats\" *ngIf=\"getProfileStats().length > 0\">\n        <div class=\"stats-grid\">\n          <div \n            *ngFor=\"let stat of getProfileStats()\" \n            class=\"stat-card\"\n            [style.border-left-color]=\"stat.color\">\n            <div class=\"stat-icon\" [style.color]=\"stat.color\">\n              <i [class]=\"stat.icon\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-value\">{{ stat.value }}</div>\n              <div class=\"stat-label\">{{ stat.label }}</div>\n              <div class=\"stat-trend\" *ngIf=\"stat.trend\" [class]=\"'trend-' + stat.trend\">\n                <i [class]=\"getTrendIcon(stat.trend)\"></i>\n                <span>{{ stat.trendValue }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Role-Specific Content -->\n      <div class=\"role-content\">\n        <!-- Executive Layout -->\n        <div *ngIf=\"profileLayout === 'executive'\" class=\"executive-layout\">\n          <div class=\"executive-grid\">\n            <div class=\"executive-card\">\n              <h3>Leadership Overview</h3>\n              <div class=\"leadership-metrics\">\n                <div class=\"metric\">\n                  <span class=\"metric-value\">{{ getTeamSize() }}</span>\n                  <span class=\"metric-label\">Team Members</span>\n                </div>\n                <div class=\"metric\">\n                  <span class=\"metric-value\">{{ getDepartmentCount() }}</span>\n                  <span class=\"metric-label\">Departments</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"executive-card\">\n              <h3>Strategic Initiatives</h3>\n              <div class=\"initiatives-list\">\n                <div class=\"initiative-item\" *ngFor=\"let initiative of getStrategicInitiatives()\">\n                  <div class=\"initiative-status\" [class]=\"initiative.status\"></div>\n                  <span>{{ initiative.name }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Manager Layout -->\n        <div *ngIf=\"profileLayout === 'manager'\" class=\"manager-layout\">\n          <div class=\"manager-grid\">\n            <div class=\"manager-card\">\n              <h3>Team Performance</h3>\n              <div class=\"performance-chart\">\n                <!-- Team performance visualization -->\n                <div class=\"performance-item\" *ngFor=\"let member of getTeamMembers()\">\n                  <div class=\"member-info\">\n                    <img [src]=\"member.avatar\" [alt]=\"member.name\" class=\"member-avatar\">\n                    <span>{{ member.name }}</span>\n                  </div>\n                  <div class=\"performance-bar\">\n                    <div class=\"progress\" [style.width.%]=\"member.performance\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"manager-card\">\n              <h3>Department Goals</h3>\n              <div class=\"goals-list\">\n                <div class=\"goal-item\" *ngFor=\"let goal of getDepartmentGoals()\">\n                  <div class=\"goal-progress\">\n                    <div class=\"progress-circle\" [style.background]=\"getProgressColor(goal.progress)\">\n                      <span>{{ goal.progress }}%</span>\n                    </div>\n                  </div>\n                  <div class=\"goal-details\">\n                    <h4>{{ goal.title }}</h4>\n                    <p>{{ goal.description }}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Specialist Layout -->\n        <div *ngIf=\"profileLayout === 'specialist'\" class=\"specialist-layout\">\n          <div class=\"specialist-grid\">\n            <div class=\"specialist-card\">\n              <h3>My Performance</h3>\n              <div class=\"performance-metrics\">\n                <div class=\"metric-circle\" *ngFor=\"let metric of getPersonalMetrics()\">\n                  <div class=\"circle-progress\" [style.background]=\"metric.color\">\n                    <span>{{ metric.value }}</span>\n                  </div>\n                  <label>{{ metric.label }}</label>\n                </div>\n              </div>\n            </div>\n            <div class=\"specialist-card\">\n              <h3>Recent Achievements</h3>\n              <div class=\"achievements-list\">\n                <div class=\"achievement-item\" *ngFor=\"let achievement of getRecentAchievements()\">\n                  <div class=\"achievement-icon\" [style.background]=\"achievement.color\">\n                    <i [class]=\"achievement.icon\"></i>\n                  </div>\n                  <div class=\"achievement-details\">\n                    <h4>{{ achievement.title }}</h4>\n                    <p>{{ achievement.description }}</p>\n                    <span class=\"achievement-date\">{{ achievement.date | date:'short' }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Admin Layout -->\n        <div *ngIf=\"profileLayout === 'admin'\" class=\"admin-layout\">\n          <div class=\"admin-grid\">\n            <div class=\"admin-card\">\n              <h3>System Overview</h3>\n              <div class=\"system-stats\">\n                <div class=\"system-metric\" *ngFor=\"let metric of getSystemMetrics()\">\n                  <div class=\"metric-icon\" [style.color]=\"metric.color\">\n                    <i [class]=\"metric.icon\"></i>\n                  </div>\n                  <div class=\"metric-data\">\n                    <span class=\"metric-value\">{{ metric.value }}</span>\n                    <span class=\"metric-label\">{{ metric.label }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"admin-card\">\n              <h3>Recent Activities</h3>\n              <div class=\"activities-list\">\n                <div class=\"activity-item\" *ngFor=\"let activity of getRecentActivities()\">\n                  <div class=\"activity-time\">{{ activity.time | date:'short' }}</div>\n                  <div class=\"activity-content\">\n                    <span class=\"activity-user\">{{ activity.user }}</span>\n                    <span class=\"activity-action\">{{ activity.action }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./dynamic-profile.component.scss']\n})], DynamicProfileComponent);\nexport { DynamicProfileComponent };", "map": {"version": 3, "names": ["Component", "Input", "CommonModule", "Subject", "DynamicProfileComponent", "constructor", "roleManagementService", "router", "userProfile", "roleConfig", "profileLayout", "destroy$", "ngOnInit", "role", "getRoleConfig", "ngOnDestroy", "next", "complete", "getCoverGradient", "color", "getProfileActions", "baseActions", "label", "icon", "action", "is<PERSON>anager", "push", "getProfileStats", "includes", "value", "trend", "trendValue", "handleAction", "navigate", "getLastActiveText", "lastActive", "now", "Date", "diffMs", "getTime", "diffMins", "Math", "floor", "getTrendIcon", "getTeamSize", "getDepartmentCount", "getStrategicInitiatives", "getTeamMembers", "getDepartmentGoals", "getPersonalMetrics", "getRecentAchievements", "getSystemMetrics", "getRecentActivities", "getProgressColor", "progress", "__decorate", "selector", "standalone", "imports", "template", "styleUrls"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\dynamic-profile\\dynamic-profile.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { RoleManagementService, UserRole, RoleConfig } from '../../../core/services/role-management.service';\n\nexport interface ProfileStats {\n  label: string;\n  value: string | number;\n  icon: string;\n  color: string;\n  trend?: 'up' | 'down' | 'stable';\n  trendValue?: string;\n}\n\nexport interface ProfileAction {\n  label: string;\n  icon: string;\n  action: string;\n  color: string;\n  permission?: string;\n}\n\nexport interface UserProfile {\n  id: string;\n  username: string;\n  fullName: string;\n  email: string;\n  avatar: string;\n  role: UserRole;\n  department: string;\n  joinDate: Date;\n  lastActive: Date;\n  bio?: string;\n  location?: string;\n  phone?: string;\n  isVerified: boolean;\n  isOnline: boolean;\n}\n\n@Component({\n  selector: 'app-dynamic-profile',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"dynamic-profile\" [attr.data-layout]=\"profileLayout\" [attr.data-role]=\"userProfile?.role\">\n      <!-- Profile Header -->\n      <div class=\"profile-header\">\n        <div class=\"profile-cover\" [style.background]=\"getCoverGradient()\">\n          <div class=\"profile-actions\">\n            <button \n              *ngFor=\"let action of getProfileActions()\" \n              class=\"action-btn\"\n              [style.background]=\"action.color\"\n              (click)=\"handleAction(action.action)\"\n              [title]=\"action.label\">\n              <i [class]=\"action.icon\"></i>\n            </button>\n          </div>\n        </div>\n        \n        <div class=\"profile-info\">\n          <div class=\"avatar-section\">\n            <div class=\"avatar-container\">\n              <img [src]=\"userProfile?.avatar || '/assets/images/default-avatar.png'\" \n                   [alt]=\"userProfile?.fullName\" \n                   class=\"profile-avatar\">\n              <div class=\"online-indicator\" *ngIf=\"userProfile?.isOnline\"></div>\n              <div class=\"role-badge\" [style.background]=\"roleConfig?.color\">\n                <i [class]=\"roleConfig?.icon\"></i>\n              </div>\n            </div>\n            \n            <div class=\"profile-details\">\n              <div class=\"name-section\">\n                <h1 class=\"profile-name\">{{ userProfile?.fullName }}</h1>\n                <div class=\"verification-badge\" *ngIf=\"userProfile?.isVerified\">\n                  <i class=\"fas fa-check-circle\"></i>\n                </div>\n              </div>\n              \n              <p class=\"profile-username\">@{{ userProfile?.username }}</p>\n              <div class=\"role-info\">\n                <span class=\"role-title\" [style.color]=\"roleConfig?.color\">\n                  {{ roleConfig?.displayName }}\n                </span>\n                <span class=\"department\">{{ roleConfig?.department | titlecase }}</span>\n              </div>\n              \n              <p class=\"profile-bio\" *ngIf=\"userProfile?.bio\">{{ userProfile?.bio }}</p>\n              \n              <div class=\"profile-meta\">\n                <div class=\"meta-item\" *ngIf=\"userProfile?.location\">\n                  <i class=\"fas fa-map-marker-alt\"></i>\n                  <span>{{ userProfile?.location }}</span>\n                </div>\n                <div class=\"meta-item\" *ngIf=\"userProfile?.joinDate\">\n                  <i class=\"fas fa-calendar-alt\"></i>\n                  <span>Joined {{ userProfile?.joinDate | date:'MMM yyyy' }}</span>\n                </div>\n                <div class=\"meta-item\">\n                  <i class=\"fas fa-clock\"></i>\n                  <span>Last active {{ getLastActiveText() }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Profile Stats -->\n      <div class=\"profile-stats\" *ngIf=\"getProfileStats().length > 0\">\n        <div class=\"stats-grid\">\n          <div \n            *ngFor=\"let stat of getProfileStats()\" \n            class=\"stat-card\"\n            [style.border-left-color]=\"stat.color\">\n            <div class=\"stat-icon\" [style.color]=\"stat.color\">\n              <i [class]=\"stat.icon\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-value\">{{ stat.value }}</div>\n              <div class=\"stat-label\">{{ stat.label }}</div>\n              <div class=\"stat-trend\" *ngIf=\"stat.trend\" [class]=\"'trend-' + stat.trend\">\n                <i [class]=\"getTrendIcon(stat.trend)\"></i>\n                <span>{{ stat.trendValue }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Role-Specific Content -->\n      <div class=\"role-content\">\n        <!-- Executive Layout -->\n        <div *ngIf=\"profileLayout === 'executive'\" class=\"executive-layout\">\n          <div class=\"executive-grid\">\n            <div class=\"executive-card\">\n              <h3>Leadership Overview</h3>\n              <div class=\"leadership-metrics\">\n                <div class=\"metric\">\n                  <span class=\"metric-value\">{{ getTeamSize() }}</span>\n                  <span class=\"metric-label\">Team Members</span>\n                </div>\n                <div class=\"metric\">\n                  <span class=\"metric-value\">{{ getDepartmentCount() }}</span>\n                  <span class=\"metric-label\">Departments</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"executive-card\">\n              <h3>Strategic Initiatives</h3>\n              <div class=\"initiatives-list\">\n                <div class=\"initiative-item\" *ngFor=\"let initiative of getStrategicInitiatives()\">\n                  <div class=\"initiative-status\" [class]=\"initiative.status\"></div>\n                  <span>{{ initiative.name }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Manager Layout -->\n        <div *ngIf=\"profileLayout === 'manager'\" class=\"manager-layout\">\n          <div class=\"manager-grid\">\n            <div class=\"manager-card\">\n              <h3>Team Performance</h3>\n              <div class=\"performance-chart\">\n                <!-- Team performance visualization -->\n                <div class=\"performance-item\" *ngFor=\"let member of getTeamMembers()\">\n                  <div class=\"member-info\">\n                    <img [src]=\"member.avatar\" [alt]=\"member.name\" class=\"member-avatar\">\n                    <span>{{ member.name }}</span>\n                  </div>\n                  <div class=\"performance-bar\">\n                    <div class=\"progress\" [style.width.%]=\"member.performance\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"manager-card\">\n              <h3>Department Goals</h3>\n              <div class=\"goals-list\">\n                <div class=\"goal-item\" *ngFor=\"let goal of getDepartmentGoals()\">\n                  <div class=\"goal-progress\">\n                    <div class=\"progress-circle\" [style.background]=\"getProgressColor(goal.progress)\">\n                      <span>{{ goal.progress }}%</span>\n                    </div>\n                  </div>\n                  <div class=\"goal-details\">\n                    <h4>{{ goal.title }}</h4>\n                    <p>{{ goal.description }}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Specialist Layout -->\n        <div *ngIf=\"profileLayout === 'specialist'\" class=\"specialist-layout\">\n          <div class=\"specialist-grid\">\n            <div class=\"specialist-card\">\n              <h3>My Performance</h3>\n              <div class=\"performance-metrics\">\n                <div class=\"metric-circle\" *ngFor=\"let metric of getPersonalMetrics()\">\n                  <div class=\"circle-progress\" [style.background]=\"metric.color\">\n                    <span>{{ metric.value }}</span>\n                  </div>\n                  <label>{{ metric.label }}</label>\n                </div>\n              </div>\n            </div>\n            <div class=\"specialist-card\">\n              <h3>Recent Achievements</h3>\n              <div class=\"achievements-list\">\n                <div class=\"achievement-item\" *ngFor=\"let achievement of getRecentAchievements()\">\n                  <div class=\"achievement-icon\" [style.background]=\"achievement.color\">\n                    <i [class]=\"achievement.icon\"></i>\n                  </div>\n                  <div class=\"achievement-details\">\n                    <h4>{{ achievement.title }}</h4>\n                    <p>{{ achievement.description }}</p>\n                    <span class=\"achievement-date\">{{ achievement.date | date:'short' }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Admin Layout -->\n        <div *ngIf=\"profileLayout === 'admin'\" class=\"admin-layout\">\n          <div class=\"admin-grid\">\n            <div class=\"admin-card\">\n              <h3>System Overview</h3>\n              <div class=\"system-stats\">\n                <div class=\"system-metric\" *ngFor=\"let metric of getSystemMetrics()\">\n                  <div class=\"metric-icon\" [style.color]=\"metric.color\">\n                    <i [class]=\"metric.icon\"></i>\n                  </div>\n                  <div class=\"metric-data\">\n                    <span class=\"metric-value\">{{ metric.value }}</span>\n                    <span class=\"metric-label\">{{ metric.label }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"admin-card\">\n              <h3>Recent Activities</h3>\n              <div class=\"activities-list\">\n                <div class=\"activity-item\" *ngFor=\"let activity of getRecentActivities()\">\n                  <div class=\"activity-time\">{{ activity.time | date:'short' }}</div>\n                  <div class=\"activity-content\">\n                    <span class=\"activity-user\">{{ activity.user }}</span>\n                    <span class=\"activity-action\">{{ activity.action }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./dynamic-profile.component.scss']\n})\nexport class DynamicProfileComponent implements OnInit, OnDestroy {\n  @Input() userProfile: UserProfile | null = null;\n  \n  roleConfig: RoleConfig | null = null;\n  profileLayout: string = 'specialist';\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private roleManagementService: RoleManagementService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    if (this.userProfile?.role) {\n      this.roleConfig = this.roleManagementService.getRoleConfig(this.userProfile.role);\n      this.profileLayout = this.roleConfig.profileLayout;\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  getCoverGradient(): string {\n    if (!this.roleConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n    \n    const color = this.roleConfig.color;\n    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n  }\n\n  getProfileActions(): ProfileAction[] {\n    if (!this.userProfile?.role) return [];\n\n    const baseActions: ProfileAction[] = [\n      { label: 'Edit Profile', icon: 'fas fa-edit', action: 'edit', color: '#4ECDC4' },\n      { label: 'Settings', icon: 'fas fa-cog', action: 'settings', color: '#45B7D1' }\n    ];\n\n    // Add role-specific actions\n    if (this.roleManagementService.isManager(this.userProfile.role)) {\n      baseActions.push(\n        { label: 'Team Management', icon: 'fas fa-users', action: 'team', color: '#96CEB4' },\n        { label: 'Reports', icon: 'fas fa-chart-bar', action: 'reports', color: '#FFEAA7' }\n      );\n    }\n\n    if (this.userProfile.role === 'super_admin' || this.userProfile.role === 'admin') {\n      baseActions.push(\n        { label: 'Admin Panel', icon: 'fas fa-shield-alt', action: 'admin', color: '#FF6B6B' }\n      );\n    }\n\n    return baseActions;\n  }\n\n  getProfileStats(): ProfileStats[] {\n    // This would be populated based on role and real data\n    // For now, returning mock data based on role\n    if (!this.userProfile?.role) return [];\n\n    const role = this.userProfile.role;\n    \n    if (role.includes('sales')) {\n      return [\n        { label: 'Sales This Month', value: '₹2.4M', icon: 'fas fa-rupee-sign', color: '#45B7D1', trend: 'up', trendValue: '+12%' },\n        { label: 'Deals Closed', value: 47, icon: 'fas fa-handshake', color: '#96CEB4', trend: 'up', trendValue: '+8%' },\n        { label: 'Target Achievement', value: '94%', icon: 'fas fa-target', color: '#FFEAA7', trend: 'stable' }\n      ];\n    }\n\n    if (role.includes('marketing')) {\n      return [\n        { label: 'Campaign Reach', value: '1.2M', icon: 'fas fa-eye', color: '#F38BA8', trend: 'up', trendValue: '+15%' },\n        { label: 'Engagement Rate', value: '4.8%', icon: 'fas fa-heart', color: '#DDA0DD', trend: 'up', trendValue: '+0.3%' },\n        { label: 'Conversions', value: 2847, icon: 'fas fa-exchange-alt', color: '#FFB6C1', trend: 'down', trendValue: '-2%' }\n      ];\n    }\n\n    // Default stats for other roles\n    return [\n      { label: 'Tasks Completed', value: 156, icon: 'fas fa-check-circle', color: '#6BCF7F', trend: 'up', trendValue: '+5%' },\n      { label: 'Projects Active', value: 8, icon: 'fas fa-project-diagram', color: '#4ECDC4', trend: 'stable' }\n    ];\n  }\n\n  handleAction(action: string): void {\n    switch (action) {\n      case 'edit':\n        this.router.navigate(['/profile/edit']);\n        break;\n      case 'settings':\n        this.router.navigate(['/profile/settings']);\n        break;\n      case 'team':\n        this.router.navigate(['/team/management']);\n        break;\n      case 'reports':\n        this.router.navigate(['/reports']);\n        break;\n      case 'admin':\n        this.router.navigate(['/admin']);\n        break;\n    }\n  }\n\n  getLastActiveText(): string {\n    if (!this.userProfile?.lastActive) return 'Unknown';\n    \n    const now = new Date();\n    const lastActive = new Date(this.userProfile.lastActive);\n    const diffMs = now.getTime() - lastActive.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    \n    if (diffMins < 1) return 'Just now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;\n    return `${Math.floor(diffMins / 1440)}d ago`;\n  }\n\n  getTrendIcon(trend: string): string {\n    switch (trend) {\n      case 'up': return 'fas fa-arrow-up';\n      case 'down': return 'fas fa-arrow-down';\n      default: return 'fas fa-minus';\n    }\n  }\n\n  // Mock data methods - these would be replaced with real data services\n  getTeamSize(): number { return 12; }\n  getDepartmentCount(): number { return 3; }\n  getStrategicInitiatives(): any[] { return []; }\n  getTeamMembers(): any[] { return []; }\n  getDepartmentGoals(): any[] { return []; }\n  getPersonalMetrics(): any[] { return []; }\n  getRecentAchievements(): any[] { return []; }\n  getSystemMetrics(): any[] { return []; }\n  getRecentActivities(): any[] { return []; }\n  getProgressColor(progress: number): string { return progress > 75 ? '#6BCF7F' : progress > 50 ? '#FFEAA7' : '#FF6B6B'; }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,QAA2B,eAAe;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,QAAmB,MAAM;AAwQlC,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAQlCC,YACUC,qBAA4C,EAC5CC,MAAc;IADd,KAAAD,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,MAAM,GAANA,MAAM;IATP,KAAAC,WAAW,GAAuB,IAAI;IAE/C,KAAAC,UAAU,GAAsB,IAAI;IACpC,KAAAC,aAAa,GAAW,YAAY;IAE5B,KAAAC,QAAQ,GAAG,IAAIR,OAAO,EAAQ;EAKnC;EAEHS,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,WAAW,EAAEK,IAAI,EAAE;MAC1B,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACH,qBAAqB,CAACQ,aAAa,CAAC,IAAI,CAACN,WAAW,CAACK,IAAI,CAAC;MACjF,IAAI,CAACH,aAAa,GAAG,IAAI,CAACD,UAAU,CAACC,aAAa;;EAEtD;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE,OAAO,mDAAmD;IAEhF,MAAMU,KAAK,GAAG,IAAI,CAACV,UAAU,CAACU,KAAK;IACnC,OAAO,2BAA2BA,KAAK,UAAUA,KAAK,UAAU;EAClE;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACZ,WAAW,EAAEK,IAAI,EAAE,OAAO,EAAE;IAEtC,MAAMQ,WAAW,GAAoB,CACnC;MAAEC,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE,aAAa;MAAEC,MAAM,EAAE,MAAM;MAAEL,KAAK,EAAE;IAAS,CAAE,EAChF;MAAEG,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,YAAY;MAAEC,MAAM,EAAE,UAAU;MAAEL,KAAK,EAAE;IAAS,CAAE,CAChF;IAED;IACA,IAAI,IAAI,CAACb,qBAAqB,CAACmB,SAAS,CAAC,IAAI,CAACjB,WAAW,CAACK,IAAI,CAAC,EAAE;MAC/DQ,WAAW,CAACK,IAAI,CACd;QAAEJ,KAAK,EAAE,iBAAiB;QAAEC,IAAI,EAAE,cAAc;QAAEC,MAAM,EAAE,MAAM;QAAEL,KAAK,EAAE;MAAS,CAAE,EACpF;QAAEG,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,kBAAkB;QAAEC,MAAM,EAAE,SAAS;QAAEL,KAAK,EAAE;MAAS,CAAE,CACpF;;IAGH,IAAI,IAAI,CAACX,WAAW,CAACK,IAAI,KAAK,aAAa,IAAI,IAAI,CAACL,WAAW,CAACK,IAAI,KAAK,OAAO,EAAE;MAChFQ,WAAW,CAACK,IAAI,CACd;QAAEJ,KAAK,EAAE,aAAa;QAAEC,IAAI,EAAE,mBAAmB;QAAEC,MAAM,EAAE,OAAO;QAAEL,KAAK,EAAE;MAAS,CAAE,CACvF;;IAGH,OAAOE,WAAW;EACpB;EAEAM,eAAeA,CAAA;IACb;IACA;IACA,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAEK,IAAI,EAAE,OAAO,EAAE;IAEtC,MAAMA,IAAI,GAAG,IAAI,CAACL,WAAW,CAACK,IAAI;IAElC,IAAIA,IAAI,CAACe,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1B,OAAO,CACL;QAAEN,KAAK,EAAE,kBAAkB;QAAEO,KAAK,EAAE,OAAO;QAAEN,IAAI,EAAE,mBAAmB;QAAEJ,KAAK,EAAE,SAAS;QAAEW,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAM,CAAE,EAC3H;QAAET,KAAK,EAAE,cAAc;QAAEO,KAAK,EAAE,EAAE;QAAEN,IAAI,EAAE,kBAAkB;QAAEJ,KAAK,EAAE,SAAS;QAAEW,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAK,CAAE,EAChH;QAAET,KAAK,EAAE,oBAAoB;QAAEO,KAAK,EAAE,KAAK;QAAEN,IAAI,EAAE,eAAe;QAAEJ,KAAK,EAAE,SAAS;QAAEW,KAAK,EAAE;MAAQ,CAAE,CACxG;;IAGH,IAAIjB,IAAI,CAACe,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC9B,OAAO,CACL;QAAEN,KAAK,EAAE,gBAAgB;QAAEO,KAAK,EAAE,MAAM;QAAEN,IAAI,EAAE,YAAY;QAAEJ,KAAK,EAAE,SAAS;QAAEW,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAM,CAAE,EACjH;QAAET,KAAK,EAAE,iBAAiB;QAAEO,KAAK,EAAE,MAAM;QAAEN,IAAI,EAAE,cAAc;QAAEJ,KAAK,EAAE,SAAS;QAAEW,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAO,CAAE,EACrH;QAAET,KAAK,EAAE,aAAa;QAAEO,KAAK,EAAE,IAAI;QAAEN,IAAI,EAAE,qBAAqB;QAAEJ,KAAK,EAAE,SAAS;QAAEW,KAAK,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAK,CAAE,CACvH;;IAGH;IACA,OAAO,CACL;MAAET,KAAK,EAAE,iBAAiB;MAAEO,KAAK,EAAE,GAAG;MAAEN,IAAI,EAAE,qBAAqB;MAAEJ,KAAK,EAAE,SAAS;MAAEW,KAAK,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAE,EACvH;MAAET,KAAK,EAAE,iBAAiB;MAAEO,KAAK,EAAE,CAAC;MAAEN,IAAI,EAAE,wBAAwB;MAAEJ,KAAK,EAAE,SAAS;MAAEW,KAAK,EAAE;IAAQ,CAAE,CAC1G;EACH;EAEAE,YAAYA,CAACR,MAAc;IACzB,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,IAAI,CAACjB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC;MACF,KAAK,UAAU;QACb,IAAI,CAAC1B,MAAM,CAAC0B,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;QAC3C;MACF,KAAK,MAAM;QACT,IAAI,CAAC1B,MAAM,CAAC0B,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC1C;MACF,KAAK,SAAS;QACZ,IAAI,CAAC1B,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;MACF,KAAK,OAAO;QACV,IAAI,CAAC1B,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAChC;;EAEN;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC1B,WAAW,EAAE2B,UAAU,EAAE,OAAO,SAAS;IAEnD,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMF,UAAU,GAAG,IAAIE,IAAI,CAAC,IAAI,CAAC7B,WAAW,CAAC2B,UAAU,CAAC;IACxD,MAAMG,MAAM,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAGJ,UAAU,CAACI,OAAO,EAAE;IACnD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAG,KAAK,CAAC;IAE3C,IAAIE,QAAQ,GAAG,CAAC,EAAE,OAAO,UAAU;IACnC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGA,QAAQ,OAAO;IAC5C,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,OAAO;IAC/D,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,OAAO;EAC9C;EAEAG,YAAYA,CAACb,KAAa;IACxB,QAAQA,KAAK;MACX,KAAK,IAAI;QAAE,OAAO,iBAAiB;MACnC,KAAK,MAAM;QAAE,OAAO,mBAAmB;MACvC;QAAS,OAAO,cAAc;;EAElC;EAEA;EACAc,WAAWA,CAAA;IAAa,OAAO,EAAE;EAAE;EACnCC,kBAAkBA,CAAA;IAAa,OAAO,CAAC;EAAE;EACzCC,uBAAuBA,CAAA;IAAY,OAAO,EAAE;EAAE;EAC9CC,cAAcA,CAAA;IAAY,OAAO,EAAE;EAAE;EACrCC,kBAAkBA,CAAA;IAAY,OAAO,EAAE;EAAE;EACzCC,kBAAkBA,CAAA;IAAY,OAAO,EAAE;EAAE;EACzCC,qBAAqBA,CAAA;IAAY,OAAO,EAAE;EAAE;EAC5CC,gBAAgBA,CAAA;IAAY,OAAO,EAAE;EAAE;EACvCC,mBAAmBA,CAAA;IAAY,OAAO,EAAE;EAAE;EAC1CC,gBAAgBA,CAACC,QAAgB;IAAY,OAAOA,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAGA,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS;EAAE;CACxH;AA3IUC,UAAA,EAARtD,KAAK,EAAE,C,2DAAwC;AADrCG,uBAAuB,GAAAmD,UAAA,EAnOnCvD,SAAS,CAAC;EACTwD,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxD,YAAY,CAAC;EACvByD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4NT;EACDC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACWxD,uBAAuB,CA4InC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}