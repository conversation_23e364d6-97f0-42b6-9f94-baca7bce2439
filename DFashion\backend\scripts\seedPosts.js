const mongoose = require('mongoose');
const Post = require('../models/Post');
const User = require('../models/User');
const Product = require('../models/Product');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/dfashion', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

const samplePosts = [
  {
    caption: "Just got this amazing summer dress! Perfect for the beach vibes 🌊✨ #SummerFashion #BeachVibes",
    media: [{
      type: 'image',
      url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600'
    }],
    mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',
    mediaType: 'image',
    location: 'Miami Beach',
    visibility: 'public',
    isActive: true,
    analytics: {
      views: 1250,
      likes: 89,
      comments: 12,
      shares: 5
    }
  },
  {
    caption: "Loving this vintage denim jacket! Goes with everything 💙 #VintageStyle #DenimLove",
    media: [{
      type: 'image',
      url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600'
    }],
    mediaUrl: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600',
    mediaType: 'image',
    location: 'New York',
    visibility: 'public',
    isActive: true,
    analytics: {
      views: 890,
      likes: 67,
      comments: 8,
      shares: 3
    }
  },
  {
    caption: "Cozy knit sweater weather is here! 🍂 Perfect for autumn vibes #CozyStyle #AutumnFashion",
    media: [{
      type: 'image',
      url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=600'
    }],
    mediaUrl: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=600',
    mediaType: 'image',
    location: 'San Francisco',
    visibility: 'public',
    isActive: true,
    analytics: {
      views: 1450,
      likes: 102,
      comments: 15,
      shares: 7
    }
  },
  {
    caption: "These high-waisted jeans are everything! 👖 Perfect fit and so comfortable #JeansLove #OOTD",
    media: [{
      type: 'image',
      url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=600'
    }],
    mediaUrl: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=600',
    mediaType: 'image',
    location: 'Los Angeles',
    visibility: 'public',
    isActive: true,
    analytics: {
      views: 2100,
      likes: 156,
      comments: 23,
      shares: 12
    }
  },
  {
    caption: "Elegant silk dress for tonight's dinner 💫 Feeling fabulous! #ElegantStyle #SilkDress",
    media: [{
      type: 'image',
      url: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600'
    }],
    mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600',
    mediaType: 'image',
    location: 'Paris',
    visibility: 'public',
    isActive: true,
    analytics: {
      views: 1780,
      likes: 134,
      comments: 18,
      shares: 9
    }
  }
];

async function seedPosts() {
  try {
    console.log('🌱 Starting to seed posts...');

    // Clear existing posts
    await Post.deleteMany({});
    console.log('🗑️ Cleared existing posts');

    // Get some users to assign posts to
    const users = await User.find({ role: 'buyer' }).limit(5);
    if (users.length === 0) {
      console.log('❌ No users found. Please seed users first.');
      return;
    }

    // Get some products to link to posts
    const products = await Product.find({}).limit(10);
    console.log(`📦 Found ${products.length} products to link`);

    // Create posts
    const posts = [];
    for (let i = 0; i < samplePosts.length; i++) {
      const postData = samplePosts[i];
      const user = users[i % users.length];

      // Add some product tags if products are available
      const postProducts = [];
      let linkedContent = null;

      if (products.length > 0) {
        const numProducts = Math.floor(Math.random() * 3) + 1; // 1-3 products per post
        for (let j = 0; j < numProducts && j < products.length; j++) {
          const product = products[(i + j) % products.length];
          postProducts.push({
            product: product._id,
            position: {
              x: 20 + (j * 30), // Spread products across the image
              y: 30 + (j * 20)
            }
          });
        }

        // Link to the first product
        if (postProducts.length > 0) {
          linkedContent = {
            type: 'product',
            productId: postProducts[0].product
          };
        }
      }

      // If no products available, create a basic linkedContent
      if (!linkedContent && products.length > 0) {
        linkedContent = {
          type: 'product',
          productId: products[0]._id
        };
      }

      const post = new Post({
        ...postData,
        user: user._id,
        products: postProducts,
        linkedContent: linkedContent,
        likes: [],
        comments: [],
        saves: []
      });

      // Add some sample comments
      const numComments = Math.floor(Math.random() * 5) + 1;
      for (let k = 0; k < numComments && k < users.length; k++) {
        const commenter = users[(k + 1) % users.length];
        post.comments.push({
          user: commenter._id,
          text: [
            'Love this look! 😍',
            'Where did you get this?',
            'So stylish! 💕',
            'Perfect outfit!',
            'Amazing style inspiration!',
            'This is gorgeous! 🔥',
            'Need this in my wardrobe!'
          ][k % 7]
        });
      }

      // Add some likes
      const numLikes = Math.floor(Math.random() * users.length);
      for (let l = 0; l < numLikes; l++) {
        const liker = users[l];
        post.likes.push({
          user: liker._id
        });
      }

      posts.push(post);
    }

    // Save all posts
    const savedPosts = await Post.insertMany(posts);
    console.log(`✅ Created ${savedPosts.length} posts successfully!`);

    // Display summary
    console.log('\n📊 Posts Summary:');
    for (const post of savedPosts) {
      console.log(`📱 Post: "${post.caption.substring(0, 50)}..."`);
      console.log(`   👤 User: ${post.user}`);
      console.log(`   🛍️ Products: ${post.products.length}`);
      console.log(`   💬 Comments: ${post.comments.length}`);
      console.log(`   ❤️ Likes: ${post.likes.length}`);
      console.log('');
    }

    console.log('🎉 Posts seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding posts:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the seeding
seedPosts();
