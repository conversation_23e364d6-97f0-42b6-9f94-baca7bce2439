{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StoriesComponent } from '../../components/stories/stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  constructor() {}\n  ngOnInit() {\n    // Home component initialization\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 4,\n      consts: [[1, \"home-container\"], [1, \"content-grid\"], [1, \"main-content\"], [\"title\", \"Suggested for You\", \"subtitle\", \"Personalized recommendations based on your preferences\", 3, \"limit\", \"showViewAll\"], [\"title\", \"Trending Now\", \"subtitle\", \"What's hot in fashion right now\", 3, \"limit\", \"showViewAll\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-stories\")(4, \"app-suggested-products\", 3)(5, \"app-trending-products\", 4)(6, \"app-feed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"app-sidebar\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"limit\", 8)(\"showViewAll\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"limit\", 6)(\"showViewAll\", true);\n        }\n      },\n      dependencies: [CommonModule, StoriesComponent, FeedComponent, SidebarComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  min-height: calc(100vh - 60px);\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 600px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 20px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9wYWdlcy9ob21lL2hvbWUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFBO0VBQ0EsOEJBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxnQ0FBQTtFQUNBLFNBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLDBCQUFBO0lBQ0EsZ0JBQUE7RUFDRjtBQUNGO0FBRUE7RUFDRTtJQUNFLGVBQUE7RUFBRjtFQUdBO0lBQ0UsZUFBQTtJQUNBLFNBQUE7RUFERjtFQUlBO0lBQ0UsU0FBQTtFQUZGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuaG9tZS1jb250YWluZXIge1xuICBwYWRkaW5nOiAyMHB4IDA7XG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA2MHB4KTtcbn1cblxuLmNvbnRlbnQtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDQwMHB4O1xuICBnYXA6IDQwcHg7XG4gIG1heC13aWR0aDogMTAwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbiAgcGFkZGluZzogMCAyMHB4O1xufVxuXG4ubWFpbi1jb250ZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAyNHB4O1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogMTAyNHB4KSB7XG4gIC5jb250ZW50LWdyaWQge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgIG1heC13aWR0aDogNjAwcHg7XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5ob21lLWNvbnRhaW5lciB7XG4gICAgcGFkZGluZzogMTZweCAwO1xuICB9XG5cbiAgLmNvbnRlbnQtZ3JpZCB7XG4gICAgcGFkZGluZzogMCAxNnB4O1xuICAgIGdhcDogMjBweDtcbiAgfVxuXG4gIC5tYWluLWNvbnRlbnQge1xuICAgIGdhcDogMjBweDtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "StoriesComponent", "FeedComponent", "SidebarComponent", "HomeComponent", "constructor", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { StoriesComponent } from '../../components/stories/stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, StoriesComponent, FeedComponent, SidebarComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  constructor() {}\n\n  ngOnInit() {\n    // Home component initialization\n  }\n}\n", "<div class=\"home-container\">\n  <div class=\"content-grid\">\n    <!-- Main Feed -->\n    <div class=\"main-content\">\n      <app-stories></app-stories>\n\n      <!-- Suggested Products Section -->\n      <app-suggested-products\n        title=\"Suggested for You\"\n        subtitle=\"Personalized recommendations based on your preferences\"\n        [limit]=\"8\"\n        [showViewAll]=\"true\">\n      </app-suggested-products>\n\n      <!-- Trending Products Section -->\n      <app-trending-products\n        title=\"Trending Now\"\n        subtitle=\"What's hot in fashion right now\"\n        [limit]=\"6\"\n        [showViewAll]=\"true\">\n      </app-trending-products>\n\n      <app-feed></app-feed>\n    </div>\n\n    <!-- Sidebar -->\n    <app-sidebar></app-sidebar>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;;AAS7E,OAAM,MAAOC,aAAa;EACxBC,YAAA,GAAe;EAEfC,QAAQA,CAAA;IACN;EAAA;;;uBAJSF,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXtBP,EAHJ,CAAAS,cAAA,aAA4B,aACA,aAEE;UAmBxBT,EAlBA,CAAAU,SAAA,kBAA2B,gCAQF,+BAQD,eAEH;UACvBV,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAU,SAAA,kBAA2B;UAE/BV,EADE,CAAAW,YAAA,EAAM,EACF;;;UAlBEX,EAAA,CAAAY,SAAA,GAAW;UACXZ,EADA,CAAAa,UAAA,YAAW,qBACS;UAOpBb,EAAA,CAAAY,SAAA,EAAW;UACXZ,EADA,CAAAa,UAAA,YAAW,qBACS;;;qBDThBvB,YAAY,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,gBAAgB;MAAAqB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}