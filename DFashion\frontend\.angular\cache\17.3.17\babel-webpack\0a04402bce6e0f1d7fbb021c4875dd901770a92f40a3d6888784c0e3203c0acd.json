{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement, d as createEvent } from './index-a1a47f01.js';\nimport { a as attachComponent } from './framework-delegate-ed4ba327.js';\nimport './helpers-be245865.js';\nconst tabCss = \":host(.tab-hidden){display:none !important}\";\nconst IonTabStyle0 = tabCss;\nconst Tab = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.loaded = false;\n    this.active = false;\n    this.delegate = undefined;\n    this.tab = undefined;\n    this.component = undefined;\n  }\n  componentWillLoad() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.active) {\n        yield _this.setActive();\n      }\n    })();\n  }\n  /** Set the active component for the tab */\n  setActive() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield _this2.prepareLazyLoaded();\n      _this2.active = true;\n    })();\n  }\n  changeActive(isActive) {\n    if (isActive) {\n      this.prepareLazyLoaded();\n    }\n  }\n  prepareLazyLoaded() {\n    if (!this.loaded && this.component != null) {\n      this.loaded = true;\n      try {\n        return attachComponent(this.delegate, this.el, this.component, ['ion-page']);\n      } catch (e) {\n        console.error(e);\n      }\n    }\n    return Promise.resolve(undefined);\n  }\n  render() {\n    const {\n      tab,\n      active,\n      component\n    } = this;\n    return h(Host, {\n      key: '4fe50fa809503794be2ef91383e49b72cad6fa82',\n      role: \"tabpanel\",\n      \"aria-hidden\": !active ? 'true' : null,\n      \"aria-labelledby\": `tab-button-${tab}`,\n      class: {\n        'ion-page': component === undefined,\n        'tab-hidden': !active\n      }\n    }, h(\"slot\", {\n      key: '937777a826936d6b399329a926a704008339803e'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"active\": [\"changeActive\"]\n    };\n  }\n};\nTab.style = IonTabStyle0;\nconst tabsCss = \":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}\";\nconst IonTabsStyle0 = tabsCss;\nconst Tabs = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionTabsWillChange = createEvent(this, \"ionTabsWillChange\", 3);\n    this.ionTabsDidChange = createEvent(this, \"ionTabsDidChange\", 3);\n    this.transitioning = false;\n    this.onTabClicked = ev => {\n      const {\n        href,\n        tab\n      } = ev.detail;\n      if (this.useRouter && href !== undefined) {\n        const router = document.querySelector('ion-router');\n        if (router) {\n          router.push(href);\n        }\n      } else {\n        this.select(tab);\n      }\n    };\n    this.selectedTab = undefined;\n    this.useRouter = false;\n  }\n  componentWillLoad() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.useRouter) {\n        _this3.useRouter = !!document.querySelector('ion-router') && !_this3.el.closest('[no-router]');\n      }\n      if (!_this3.useRouter) {\n        const tabs = _this3.tabs;\n        if (tabs.length > 0) {\n          yield _this3.select(tabs[0]);\n        }\n      }\n      _this3.ionNavWillLoad.emit();\n    })();\n  }\n  componentWillRender() {\n    const tabBar = this.el.querySelector('ion-tab-bar');\n    if (tabBar) {\n      const tab = this.selectedTab ? this.selectedTab.tab : undefined;\n      tabBar.selectedTab = tab;\n    }\n  }\n  /**\n   * Select a tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   *\n   * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n   */\n  select(tab) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const selectedTab = getTab(_this4.tabs, tab);\n      if (!_this4.shouldSwitch(selectedTab)) {\n        return false;\n      }\n      yield _this4.setActive(selectedTab);\n      yield _this4.notifyRouter();\n      _this4.tabSwitch();\n      return true;\n    })();\n  }\n  /**\n   * Get a specific tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   *\n   * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n   */\n  getTab(tab) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      return getTab(_this5.tabs, tab);\n    })();\n  }\n  /**\n   * Get the currently selected tab. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   */\n  getSelected() {\n    return Promise.resolve(this.selectedTab ? this.selectedTab.tab : undefined);\n  }\n  /** @internal */\n  setRouteId(id) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const selectedTab = getTab(_this6.tabs, id);\n      if (!_this6.shouldSwitch(selectedTab)) {\n        return {\n          changed: false,\n          element: _this6.selectedTab\n        };\n      }\n      yield _this6.setActive(selectedTab);\n      return {\n        changed: true,\n        element: _this6.selectedTab,\n        markVisible: () => _this6.tabSwitch()\n      };\n    })();\n  }\n  /** @internal */\n  getRouteId() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const tabId = (_a = _this7.selectedTab) === null || _a === void 0 ? void 0 : _a.tab;\n      return tabId !== undefined ? {\n        id: tabId,\n        element: _this7.selectedTab\n      } : undefined;\n    })();\n  }\n  setActive(selectedTab) {\n    if (this.transitioning) {\n      return Promise.reject('transitioning already happening');\n    }\n    this.transitioning = true;\n    this.leavingTab = this.selectedTab;\n    this.selectedTab = selectedTab;\n    this.ionTabsWillChange.emit({\n      tab: selectedTab.tab\n    });\n    selectedTab.active = true;\n    return Promise.resolve();\n  }\n  tabSwitch() {\n    const selectedTab = this.selectedTab;\n    const leavingTab = this.leavingTab;\n    this.leavingTab = undefined;\n    this.transitioning = false;\n    if (!selectedTab) {\n      return;\n    }\n    if (leavingTab !== selectedTab) {\n      if (leavingTab) {\n        leavingTab.active = false;\n      }\n      this.ionTabsDidChange.emit({\n        tab: selectedTab.tab\n      });\n    }\n  }\n  notifyRouter() {\n    if (this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        return router.navChanged('forward');\n      }\n    }\n    return Promise.resolve(false);\n  }\n  shouldSwitch(selectedTab) {\n    const leavingTab = this.selectedTab;\n    return selectedTab !== undefined && selectedTab !== leavingTab && !this.transitioning;\n  }\n  get tabs() {\n    return Array.from(this.el.querySelectorAll('ion-tab'));\n  }\n  render() {\n    return h(Host, {\n      key: '5102fdd8ae80408811312631f0739c356d913840',\n      onIonTabButtonClick: this.onTabClicked\n    }, h(\"slot\", {\n      key: '55f781cd010dcebf9675f2a0b4eab9f4271b780e',\n      name: \"top\"\n    }), h(\"div\", {\n      key: '2da0ee7b8c82e4bfa42f8bc5873e23e50a88c405',\n      class: \"tabs-inner\"\n    }, h(\"slot\", {\n      key: '6335a62a10398c008c91f87f15b4a940a95e175d'\n    })), h(\"slot\", {\n      key: '9b6f08a21c703cc7fdebd48eb746d0fde6a8454d',\n      name: \"bottom\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst getTab = (tabs, tab) => {\n  const tabEl = typeof tab === 'string' ? tabs.find(t => t.tab === tab) : tab;\n  if (!tabEl) {\n    console.error(`tab with id: \"${tabEl}\" does not exist`);\n  }\n  return tabEl;\n};\nTabs.style = IonTabsStyle0;\nexport { Tab as ion_tab, Tabs as ion_tabs };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}