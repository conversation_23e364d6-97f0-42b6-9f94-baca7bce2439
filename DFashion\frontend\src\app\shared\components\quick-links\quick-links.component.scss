.quick-links-section {
  padding: 2rem 0;
  background: white;

  // Section Header
  .section-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 0 1rem;

    .header-content {
      .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #262626;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        i {
          color: #ffc107;
          font-size: 1.8rem;
        }

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .section-subtitle {
        color: #8e8e8e;
        font-size: 1rem;
        margin: 0;
      }
    }
  }

  // Quick Links Grid
  .quick-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    padding: 0 1rem;
    margin-bottom: 2rem;

    &.compact {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.75rem;

      .quick-link-card {
        padding: 1rem;

        .link-icon {
          width: 40px;
          height: 40px;
          font-size: 1.2rem;
        }

        .link-title {
          font-size: 0.9rem;
        }
      }
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 0.75rem;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr 1fr;
      gap: 0.5rem;

      .quick-link-card {
        min-height: auto;
      }
    }
  }

  // Quick Link Card
  .quick-link-card {
    position: relative;
    padding: 1.5rem;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    min-height: 120px;
    display: flex;
    align-items: center;
    gap: 1rem;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);

      .link-arrow {
        transform: translateX(5px);
      }

      .hover-effect {
        opacity: 0.1;
        transform: scale(1.1);
      }

      .link-icon {
        transform: scale(1.1);
      }
    }

    &:focus {
      outline: 3px solid rgba(102, 126, 234, 0.3);
      outline-offset: 2px;
    }

    .link-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .link-content {
      flex: 1;

      .link-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #262626;
        margin: 0 0 0.25rem 0;
        line-height: 1.2;
      }

      .link-description {
        font-size: 0.85rem;
        color: #8e8e8e;
        margin: 0;
        line-height: 1.3;
      }
    }

    .link-arrow {
      color: #8e8e8e;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .hover-effect {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0;
      transition: all 0.3s ease;
      border-radius: 16px;
    }
  }

  // Popular Categories
  .popular-categories {
    margin-bottom: 2rem;
    padding: 0 1rem;

    .categories-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #262626;
      margin: 0 0 1rem 0;
      text-align: center;
    }

    .categories-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      justify-content: center;

      .category-chip {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: white;
        border: 2px solid;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        font-size: 0.85rem;

        &:hover {
          background: currentColor;
          color: white !important;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        i {
          font-size: 0.8rem;
        }

        .product-count {
          font-size: 0.75rem;
          opacity: 0.8;
        }
      }
    }
  }

  // Quick Actions
  .quick-actions {
    margin-bottom: 2rem;
    padding: 0 1rem;

    .actions-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #262626;
      margin: 0 0 1rem 0;
      text-align: center;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;

      @media (max-width: 768px) {
        grid-template-columns: repeat(3, 1fr);
      }

      @media (max-width: 480px) {
        grid-template-columns: repeat(2, 1fr);
      }

      .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        border: none;
        border-radius: 12px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        i {
          font-size: 1.2rem;
        }

        span {
          font-size: 0.85rem;
        }
      }
    }
  }

  // Featured Collections
  .featured-collections {
    padding: 0 1rem;

    .collections-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #262626;
      margin: 0 0 1rem 0;
      text-align: center;
    }

    .collections-scroll {
      display: flex;
      gap: 1rem;
      overflow-x: auto;
      padding-bottom: 1rem;

      &::-webkit-scrollbar {
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a1a1a1;
        }
      }

      .collection-card {
        min-width: 200px;
        height: 150px;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

          .collection-overlay {
            background: rgba(0, 0, 0, 0.6);
          }
        }

        .collection-image {
          width: 100%;
          height: 100%;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .collection-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.4);
            display: flex;
            align-items: flex-end;
            padding: 1rem;
            transition: all 0.3s ease;

            .collection-info {
              color: white;

              h4 {
                margin: 0 0 0.25rem 0;
                font-size: 1rem;
                font-weight: 600;
              }

              p {
                margin: 0;
                font-size: 0.8rem;
                opacity: 0.9;
              }
            }
          }
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .quick-links-section {
    background: #121212;

    .section-title,
    .categories-title,
    .actions-title,
    .collections-title {
      color: #ffffff;
    }

    .quick-link-card {
      background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
      border-color: #333;

      .link-title {
        color: #ffffff;
      }

      .link-icon {
        background: rgba(255, 255, 255, 0.1);
      }
    }

    .category-chip {
      background: #1e1e1e;

      &:hover {
        background: currentColor !important;
      }
    }

    .action-btn {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

      &:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .quick-links-section {
    padding: 1rem 0;

    .section-header {
      margin-bottom: 1rem;

      .section-title {
        font-size: 1.3rem;
      }

      .section-subtitle {
        font-size: 0.9rem;
      }
    }

    .quick-links-grid,
    .popular-categories,
    .quick-actions,
    .featured-collections {
      margin-bottom: 1.5rem;
    }

    .categories-title,
    .actions-title,
    .collections-title {
      font-size: 1.1rem;
    }
  }
}
