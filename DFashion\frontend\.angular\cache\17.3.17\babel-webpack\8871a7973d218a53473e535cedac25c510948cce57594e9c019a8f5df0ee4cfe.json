{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AdvancedSearchService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/search`;\n    // Search state management\n    this.searchHistorySubject = new BehaviorSubject([]);\n    this.recentSearchesSubject = new BehaviorSubject([]);\n    this.trendingSearchesSubject = new BehaviorSubject([]);\n    this.searchHistory$ = this.searchHistorySubject.asObservable();\n    this.recentSearches$ = this.recentSearchesSubject.asObservable();\n    this.trendingSearches$ = this.trendingSearchesSubject.asObservable();\n    // Query patterns for intelligent detection\n    this.queryPatterns = {\n      ambiguous: [/^kurts?$/i, /^shirts?$/i, /^pants?$/i, /^dresses?$/i, /^shoes?$/i, /^bags?$/i, /^watches?$/i, /^jewelry$/i],\n      specific: [/kurtas?\\s+for\\s+(men|boys|male)/i, /kurtas?\\s+for\\s+(women|girls|female)/i, /(men|boys|male)\\s+kurtas?/i, /(women|girls|female)\\s+kurtas?/i, /(men|boys|male)\\s+shirts?/i, /(women|girls|female)\\s+dresses?/i],\n      genderSpecific: [/(men|boys|male|gents)/i, /(women|girls|female|ladies)/i]\n    };\n    this.loadSearchHistory();\n    this.loadTrendingSearches();\n  }\n  /**\n   * Main search method with intelligent query processing\n   */\n  search(searchQuery) {\n    const startTime = Date.now();\n    // Analyze query type\n    const queryAnalysis = this.analyzeQuery(searchQuery.query);\n    // Handle ambiguous queries that need category selection\n    if (queryAnalysis.isAmbiguous && !searchQuery.category) {\n      return this.handleAmbiguousQuery(searchQuery.query, startTime);\n    }\n    // Process specific queries\n    if (queryAnalysis.isSpecific) {\n      searchQuery = this.enhanceSpecificQuery(searchQuery, queryAnalysis);\n    }\n    // Perform the actual search\n    return this.performSearch(searchQuery, startTime);\n  }\n  /**\n   * Get search suggestions as user types\n   */\n  getSuggestions(query) {\n    if (!query || query.length < 2) {\n      return of([]);\n    }\n    const params = new HttpParams().set('q', query).set('type', 'suggestions');\n    return this.http.get(`${this.API_URL}/suggestions`, {\n      params\n    }).pipe(map(response => response.suggestions || []), catchError(() => of(this.getFallbackSuggestions(query))));\n  }\n  /**\n   * Analyze query to determine type and intent\n   */\n  analyzeQuery(query) {\n    const normalizedQuery = query.toLowerCase().trim();\n    const analysis = {\n      isAmbiguous: false,\n      isSpecific: false,\n      detectedGender: null,\n      detectedCategory: null,\n      detectedSubcategory: null,\n      confidence: 0\n    };\n    // Check for ambiguous patterns\n    for (const pattern of this.queryPatterns.ambiguous) {\n      if (pattern.test(normalizedQuery)) {\n        analysis.isAmbiguous = true;\n        analysis.detectedCategory = this.extractCategoryFromPattern(normalizedQuery);\n        break;\n      }\n    }\n    // Check for specific patterns\n    for (const pattern of this.queryPatterns.specific) {\n      const match = pattern.exec(normalizedQuery);\n      if (match) {\n        analysis.isSpecific = true;\n        analysis.detectedGender = this.extractGender(match[1] || match[2]);\n        analysis.detectedCategory = this.extractCategoryFromQuery(normalizedQuery);\n        analysis.confidence = 0.9;\n        break;\n      }\n    }\n    // Check for gender-specific terms\n    for (const pattern of this.queryPatterns.genderSpecific) {\n      const match = pattern.exec(normalizedQuery);\n      if (match) {\n        analysis.detectedGender = this.extractGender(match[1]);\n        break;\n      }\n    }\n    return analysis;\n  }\n  /**\n   * Handle ambiguous queries by providing category options\n   */\n  handleAmbiguousQuery(query, startTime) {\n    const categories = this.getCategoryOptionsForQuery(query);\n    return of({\n      products: [],\n      totalCount: 0,\n      suggestions: [],\n      categoryRedirect: {\n        shouldRedirect: true,\n        categories: categories,\n        originalQuery: query\n      },\n      searchMetadata: {\n        queryType: 'ambiguous',\n        processingTime: Date.now() - startTime,\n        appliedFilters: {}\n      }\n    });\n  }\n  /**\n   * Enhance specific queries with detected parameters\n   */\n  enhanceSpecificQuery(searchQuery, analysis) {\n    const enhanced = {\n      ...searchQuery\n    };\n    if (analysis.detectedGender) {\n      enhanced.targetGender = analysis.detectedGender;\n      enhanced.category = analysis.detectedGender === 'men' ? 'men' : 'women';\n    }\n    if (analysis.detectedCategory) {\n      enhanced.subcategory = analysis.detectedCategory;\n    }\n    return enhanced;\n  }\n  /**\n   * Perform the actual search API call\n   */\n  performSearch(searchQuery, startTime) {\n    let params = new HttpParams();\n    // Add query parameters\n    if (searchQuery.query) params = params.set('q', searchQuery.query);\n    if (searchQuery.category) params = params.set('category', searchQuery.category);\n    if (searchQuery.subcategory) params = params.set('subcategory', searchQuery.subcategory);\n    if (searchQuery.targetGender) params = params.set('targetGender', searchQuery.targetGender);\n    if (searchQuery.brand) params = params.set('brand', searchQuery.brand);\n    if (searchQuery.sortBy) params = params.set('sortBy', searchQuery.sortBy);\n    // Add price range\n    if (searchQuery.priceRange) {\n      params = params.set('minPrice', searchQuery.priceRange.min.toString());\n      params = params.set('maxPrice', searchQuery.priceRange.max.toString());\n    }\n    // Add filters\n    if (searchQuery.filters) {\n      Object.keys(searchQuery.filters).forEach(key => {\n        const values = searchQuery.filters[key];\n        if (values && values.length > 0) {\n          params = params.set(key, values.join(','));\n        }\n      });\n    }\n    return this.http.get(`${this.API_URL}/products`, {\n      params\n    }).pipe(map(response => ({\n      products: response.products || [],\n      totalCount: response.totalCount || 0,\n      suggestions: response.suggestions || [],\n      searchMetadata: {\n        queryType: 'specific',\n        processingTime: Date.now() - startTime,\n        appliedFilters: searchQuery.filters || {}\n      }\n    })), catchError(() => of({\n      products: [],\n      totalCount: 0,\n      suggestions: [],\n      searchMetadata: {\n        queryType: 'specific',\n        processingTime: Date.now() - startTime,\n        appliedFilters: {}\n      }\n    })));\n  }\n  /**\n   * Get category options for ambiguous queries\n   */\n  getCategoryOptionsForQuery(query) {\n    const normalizedQuery = query.toLowerCase();\n    if (normalizedQuery.includes('kurt')) {\n      return [{\n        id: 'men-kurtas',\n        name: 'Men\\'s Kurtas',\n        description: 'Traditional and modern kurtas for men',\n        productCount: 45,\n        targetGender: 'men',\n        image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300'\n      }, {\n        id: 'women-kurtis',\n        name: 'Women\\'s Kurtis',\n        description: 'Stylish kurtis and kurtas for women',\n        productCount: 67,\n        targetGender: 'women',\n        image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300'\n      }];\n    }\n    // Add more category mappings as needed\n    return [];\n  }\n  /**\n   * Extract gender from query text\n   */\n  extractGender(genderText) {\n    const normalized = genderText.toLowerCase();\n    if (['men', 'boys', 'male', 'gents'].includes(normalized)) {\n      return 'men';\n    }\n    if (['women', 'girls', 'female', 'ladies'].includes(normalized)) {\n      return 'women';\n    }\n    return 'unisex';\n  }\n  /**\n   * Extract category from query pattern\n   */\n  extractCategoryFromPattern(query) {\n    if (query.includes('kurt')) return 'kurtas';\n    if (query.includes('shirt')) return 'shirts';\n    if (query.includes('pant')) return 'pants';\n    if (query.includes('dress')) return 'dresses';\n    return '';\n  }\n  /**\n   * Extract category from full query\n   */\n  extractCategoryFromQuery(query) {\n    if (query.includes('kurta')) return 'kurtas';\n    if (query.includes('kurti')) return 'kurtis';\n    if (query.includes('shirt')) return 'shirts';\n    if (query.includes('pant') || query.includes('trouser')) return 'pants';\n    if (query.includes('dress')) return 'dresses';\n    return '';\n  }\n  /**\n   * Get fallback suggestions when API fails\n   */\n  getFallbackSuggestions(query) {\n    const suggestions = [];\n    if (query.toLowerCase().includes('kurt')) {\n      suggestions.push({\n        text: 'kurtas for men',\n        type: 'keyword',\n        count: 45\n      }, {\n        text: 'kurtas for women',\n        type: 'keyword',\n        count: 67\n      }, {\n        text: 'cotton kurtas',\n        type: 'keyword',\n        count: 32\n      }, {\n        text: 'silk kurtas',\n        type: 'keyword',\n        count: 18\n      });\n    }\n    return suggestions;\n  }\n  /**\n   * Save search to history\n   */\n  saveSearchToHistory(query) {\n    const currentHistory = this.searchHistorySubject.value;\n    const updatedHistory = [query, ...currentHistory.filter(q => q !== query)].slice(0, 10);\n    this.searchHistorySubject.next(updatedHistory);\n    localStorage.setItem('dfashion_search_history', JSON.stringify(updatedHistory));\n  }\n  /**\n   * Load search history from storage\n   */\n  loadSearchHistory() {\n    const stored = localStorage.getItem('dfashion_search_history');\n    if (stored) {\n      try {\n        const history = JSON.parse(stored);\n        this.searchHistorySubject.next(history);\n      } catch (e) {\n        console.warn('Failed to load search history');\n      }\n    }\n  }\n  /**\n   * Load trending searches\n   */\n  loadTrendingSearches() {\n    // This would typically come from an API\n    const trending = ['kurtas for men', 'women ethnic wear', 'cotton shirts', 'formal dresses', 'casual wear'];\n    this.trendingSearchesSubject.next(trending);\n  }\n  /**\n   * Clear search history\n   */\n  clearSearchHistory() {\n    this.searchHistorySubject.next([]);\n    localStorage.removeItem('dfashion_search_history');\n  }\n  static {\n    this.ɵfac = function AdvancedSearchService_Factory(t) {\n      return new (t || AdvancedSearchService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdvancedSearchService,\n      factory: AdvancedSearchService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "of", "map", "catchError", "environment", "AdvancedSearchService", "constructor", "http", "API_URL", "apiUrl", "searchHistorySubject", "recentSearchesSubject", "trendingSearchesSubject", "searchHistory$", "asObservable", "recentSearches$", "trendingSearches$", "queryPatterns", "ambiguous", "specific", "genderSpecific", "loadSearchHistory", "loadTrendingSearches", "search", "searchQuery", "startTime", "Date", "now", "queryAnalysis", "analyzeQuery", "query", "isAmbiguous", "category", "handleAmbiguousQuery", "isSpecific", "enhanceSpecificQuery", "performSearch", "getSuggestions", "length", "params", "set", "get", "pipe", "response", "suggestions", "getFallbackSuggestions", "normalizedQuery", "toLowerCase", "trim", "analysis", "<PERSON><PERSON><PERSON>", "detectedCategory", "detectedSubcategory", "confidence", "pattern", "test", "extractCategoryFromPattern", "match", "exec", "extractGender", "extractCategoryFromQuery", "categories", "getCategoryOptionsForQuery", "products", "totalCount", "categoryRedirect", "shouldRedirect", "originalQuery", "searchMetadata", "queryType", "processingTime", "appliedFilters", "enhanced", "targetGender", "subcategory", "brand", "sortBy", "priceRange", "min", "toString", "max", "filters", "Object", "keys", "for<PERSON>ach", "key", "values", "join", "includes", "id", "name", "description", "productCount", "image", "genderText", "normalized", "push", "text", "type", "count", "saveSearchToHistory", "currentHistory", "value", "updatedHistory", "filter", "q", "slice", "next", "localStorage", "setItem", "JSON", "stringify", "stored", "getItem", "history", "parse", "e", "console", "warn", "trending", "clearSearchHistory", "removeItem", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\advanced-search.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject, of } from 'rxjs';\nimport { map, catchError, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\n\nexport interface SearchQuery {\n  query: string;\n  category?: string;\n  subcategory?: string;\n  targetGender?: string;\n  priceRange?: {\n    min: number;\n    max: number;\n  };\n  brand?: string;\n  sortBy?: 'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest';\n  filters?: {\n    availability?: string[];\n    occasion?: string[];\n    material?: string[];\n    size?: string[];\n    color?: string[];\n  };\n}\n\nexport interface SearchResult {\n  products: any[];\n  totalCount: number;\n  suggestions: string[];\n  categoryRedirect?: {\n    shouldRedirect: boolean;\n    categories: CategoryOption[];\n    originalQuery: string;\n  };\n  searchMetadata: {\n    queryType: 'specific' | 'ambiguous' | 'category';\n    processingTime: number;\n    appliedFilters: any;\n  };\n}\n\nexport interface CategoryOption {\n  id: string;\n  name: string;\n  description: string;\n  productCount: number;\n  targetGender: string;\n  image: string;\n  category?: string;\n  subcategory?: string;\n  avgPrice?: number;\n}\n\nexport interface SearchSuggestion {\n  text: string;\n  type: 'product' | 'category' | 'brand' | 'keyword';\n  count?: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdvancedSearchService {\n  private readonly API_URL = `${environment.apiUrl}/search`;\n  \n  // Search state management\n  private searchHistorySubject = new BehaviorSubject<string[]>([]);\n  private recentSearchesSubject = new BehaviorSubject<string[]>([]);\n  private trendingSearchesSubject = new BehaviorSubject<string[]>([]);\n  \n  public searchHistory$ = this.searchHistorySubject.asObservable();\n  public recentSearches$ = this.recentSearchesSubject.asObservable();\n  public trendingSearches$ = this.trendingSearchesSubject.asObservable();\n\n  // Query patterns for intelligent detection\n  private readonly queryPatterns = {\n    ambiguous: [\n      /^kurts?$/i,\n      /^shirts?$/i,\n      /^pants?$/i,\n      /^dresses?$/i,\n      /^shoes?$/i,\n      /^bags?$/i,\n      /^watches?$/i,\n      /^jewelry$/i\n    ],\n    specific: [\n      /kurtas?\\s+for\\s+(men|boys|male)/i,\n      /kurtas?\\s+for\\s+(women|girls|female)/i,\n      /(men|boys|male)\\s+kurtas?/i,\n      /(women|girls|female)\\s+kurtas?/i,\n      /(men|boys|male)\\s+shirts?/i,\n      /(women|girls|female)\\s+dresses?/i\n    ],\n    genderSpecific: [\n      /(men|boys|male|gents)/i,\n      /(women|girls|female|ladies)/i\n    ]\n  };\n\n  constructor(private http: HttpClient) {\n    this.loadSearchHistory();\n    this.loadTrendingSearches();\n  }\n\n  /**\n   * Main search method with intelligent query processing\n   */\n  search(searchQuery: SearchQuery): Observable<SearchResult> {\n    const startTime = Date.now();\n    \n    // Analyze query type\n    const queryAnalysis = this.analyzeQuery(searchQuery.query);\n    \n    // Handle ambiguous queries that need category selection\n    if (queryAnalysis.isAmbiguous && !searchQuery.category) {\n      return this.handleAmbiguousQuery(searchQuery.query, startTime);\n    }\n    \n    // Process specific queries\n    if (queryAnalysis.isSpecific) {\n      searchQuery = this.enhanceSpecificQuery(searchQuery, queryAnalysis);\n    }\n    \n    // Perform the actual search\n    return this.performSearch(searchQuery, startTime);\n  }\n\n  /**\n   * Get search suggestions as user types\n   */\n  getSuggestions(query: string): Observable<SearchSuggestion[]> {\n    if (!query || query.length < 2) {\n      return of([]);\n    }\n\n    const params = new HttpParams().set('q', query).set('type', 'suggestions');\n    \n    return this.http.get<any>(`${this.API_URL}/suggestions`, { params }).pipe(\n      map(response => response.suggestions || []),\n      catchError(() => of(this.getFallbackSuggestions(query)))\n    );\n  }\n\n  /**\n   * Analyze query to determine type and intent\n   */\n  private analyzeQuery(query: string): any {\n    const normalizedQuery = query.toLowerCase().trim();\n    \n    const analysis = {\n      isAmbiguous: false,\n      isSpecific: false,\n      detectedGender: null as string | null,\n      detectedCategory: null as string | null,\n      detectedSubcategory: null as string | null,\n      confidence: 0\n    };\n\n    // Check for ambiguous patterns\n    for (const pattern of this.queryPatterns.ambiguous) {\n      if (pattern.test(normalizedQuery)) {\n        analysis.isAmbiguous = true;\n        analysis.detectedCategory = this.extractCategoryFromPattern(normalizedQuery);\n        break;\n      }\n    }\n\n    // Check for specific patterns\n    for (const pattern of this.queryPatterns.specific) {\n      const match = pattern.exec(normalizedQuery);\n      if (match) {\n        analysis.isSpecific = true;\n        analysis.detectedGender = this.extractGender(match[1] || match[2]);\n        analysis.detectedCategory = this.extractCategoryFromQuery(normalizedQuery);\n        analysis.confidence = 0.9;\n        break;\n      }\n    }\n\n    // Check for gender-specific terms\n    for (const pattern of this.queryPatterns.genderSpecific) {\n      const match = pattern.exec(normalizedQuery);\n      if (match) {\n        analysis.detectedGender = this.extractGender(match[1]);\n        break;\n      }\n    }\n\n    return analysis;\n  }\n\n  /**\n   * Handle ambiguous queries by providing category options\n   */\n  private handleAmbiguousQuery(query: string, startTime: number): Observable<SearchResult> {\n    const categories = this.getCategoryOptionsForQuery(query);\n    \n    return of({\n      products: [],\n      totalCount: 0,\n      suggestions: [],\n      categoryRedirect: {\n        shouldRedirect: true,\n        categories: categories,\n        originalQuery: query\n      },\n      searchMetadata: {\n        queryType: 'ambiguous',\n        processingTime: Date.now() - startTime,\n        appliedFilters: {}\n      }\n    });\n  }\n\n  /**\n   * Enhance specific queries with detected parameters\n   */\n  private enhanceSpecificQuery(searchQuery: SearchQuery, analysis: any): SearchQuery {\n    const enhanced = { ...searchQuery };\n    \n    if (analysis.detectedGender) {\n      enhanced.targetGender = analysis.detectedGender;\n      enhanced.category = analysis.detectedGender === 'men' ? 'men' : 'women';\n    }\n    \n    if (analysis.detectedCategory) {\n      enhanced.subcategory = analysis.detectedCategory;\n    }\n    \n    return enhanced;\n  }\n\n  /**\n   * Perform the actual search API call\n   */\n  private performSearch(searchQuery: SearchQuery, startTime: number): Observable<SearchResult> {\n    let params = new HttpParams();\n    \n    // Add query parameters\n    if (searchQuery.query) params = params.set('q', searchQuery.query);\n    if (searchQuery.category) params = params.set('category', searchQuery.category);\n    if (searchQuery.subcategory) params = params.set('subcategory', searchQuery.subcategory);\n    if (searchQuery.targetGender) params = params.set('targetGender', searchQuery.targetGender);\n    if (searchQuery.brand) params = params.set('brand', searchQuery.brand);\n    if (searchQuery.sortBy) params = params.set('sortBy', searchQuery.sortBy);\n    \n    // Add price range\n    if (searchQuery.priceRange) {\n      params = params.set('minPrice', searchQuery.priceRange.min.toString());\n      params = params.set('maxPrice', searchQuery.priceRange.max.toString());\n    }\n    \n    // Add filters\n    if (searchQuery.filters) {\n      Object.keys(searchQuery.filters).forEach(key => {\n        const values = searchQuery.filters![key as keyof typeof searchQuery.filters];\n        if (values && values.length > 0) {\n          params = params.set(key, values.join(','));\n        }\n      });\n    }\n\n    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(\n      map(response => ({\n        products: response.products || [],\n        totalCount: response.totalCount || 0,\n        suggestions: response.suggestions || [],\n        searchMetadata: {\n          queryType: 'specific' as 'specific',\n          processingTime: Date.now() - startTime,\n          appliedFilters: searchQuery.filters || {}\n        }\n      })),\n      catchError(() => of({\n        products: [],\n        totalCount: 0,\n        suggestions: [],\n        searchMetadata: {\n          queryType: 'specific' as 'specific',\n          processingTime: Date.now() - startTime,\n          appliedFilters: {}\n        }\n      }))\n    );\n  }\n\n  /**\n   * Get category options for ambiguous queries\n   */\n  private getCategoryOptionsForQuery(query: string): CategoryOption[] {\n    const normalizedQuery = query.toLowerCase();\n    \n    if (normalizedQuery.includes('kurt')) {\n      return [\n        {\n          id: 'men-kurtas',\n          name: 'Men\\'s Kurtas',\n          description: 'Traditional and modern kurtas for men',\n          productCount: 45,\n          targetGender: 'men',\n          image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300'\n        },\n        {\n          id: 'women-kurtis',\n          name: 'Women\\'s Kurtis',\n          description: 'Stylish kurtis and kurtas for women',\n          productCount: 67,\n          targetGender: 'women',\n          image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300'\n        }\n      ];\n    }\n    \n    // Add more category mappings as needed\n    return [];\n  }\n\n  /**\n   * Extract gender from query text\n   */\n  private extractGender(genderText: string): string {\n    const normalized = genderText.toLowerCase();\n    if (['men', 'boys', 'male', 'gents'].includes(normalized)) {\n      return 'men';\n    }\n    if (['women', 'girls', 'female', 'ladies'].includes(normalized)) {\n      return 'women';\n    }\n    return 'unisex';\n  }\n\n  /**\n   * Extract category from query pattern\n   */\n  private extractCategoryFromPattern(query: string): string {\n    if (query.includes('kurt')) return 'kurtas';\n    if (query.includes('shirt')) return 'shirts';\n    if (query.includes('pant')) return 'pants';\n    if (query.includes('dress')) return 'dresses';\n    return '';\n  }\n\n  /**\n   * Extract category from full query\n   */\n  private extractCategoryFromQuery(query: string): string {\n    if (query.includes('kurta')) return 'kurtas';\n    if (query.includes('kurti')) return 'kurtis';\n    if (query.includes('shirt')) return 'shirts';\n    if (query.includes('pant') || query.includes('trouser')) return 'pants';\n    if (query.includes('dress')) return 'dresses';\n    return '';\n  }\n\n  /**\n   * Get fallback suggestions when API fails\n   */\n  private getFallbackSuggestions(query: string): SearchSuggestion[] {\n    const suggestions: SearchSuggestion[] = [];\n    \n    if (query.toLowerCase().includes('kurt')) {\n      suggestions.push(\n        { text: 'kurtas for men', type: 'keyword', count: 45 },\n        { text: 'kurtas for women', type: 'keyword', count: 67 },\n        { text: 'cotton kurtas', type: 'keyword', count: 32 },\n        { text: 'silk kurtas', type: 'keyword', count: 18 }\n      );\n    }\n    \n    return suggestions;\n  }\n\n  /**\n   * Save search to history\n   */\n  saveSearchToHistory(query: string): void {\n    const currentHistory = this.searchHistorySubject.value;\n    const updatedHistory = [query, ...currentHistory.filter(q => q !== query)].slice(0, 10);\n    this.searchHistorySubject.next(updatedHistory);\n    localStorage.setItem('dfashion_search_history', JSON.stringify(updatedHistory));\n  }\n\n  /**\n   * Load search history from storage\n   */\n  private loadSearchHistory(): void {\n    const stored = localStorage.getItem('dfashion_search_history');\n    if (stored) {\n      try {\n        const history = JSON.parse(stored);\n        this.searchHistorySubject.next(history);\n      } catch (e) {\n        console.warn('Failed to load search history');\n      }\n    }\n  }\n\n  /**\n   * Load trending searches\n   */\n  private loadTrendingSearches(): void {\n    // This would typically come from an API\n    const trending = [\n      'kurtas for men',\n      'women ethnic wear',\n      'cotton shirts',\n      'formal dresses',\n      'casual wear'\n    ];\n    this.trendingSearchesSubject.next(trending);\n  }\n\n  /**\n   * Clear search history\n   */\n  clearSearchHistory(): void {\n    this.searchHistorySubject.next([]);\n    localStorage.removeItem('dfashion_search_history');\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAAqBC,eAAe,EAAEC,EAAE,QAAQ,MAAM;AACtD,SAASC,GAAG,EAAEC,UAAU,QAA4C,gBAAgB;AACpF,SAASC,WAAW,QAAQ,mCAAmC;;;AA2D/D,OAAM,MAAOC,qBAAqB;EAsChCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IArCP,KAAAC,OAAO,GAAG,GAAGJ,WAAW,CAACK,MAAM,SAAS;IAEzD;IACQ,KAAAC,oBAAoB,GAAG,IAAIV,eAAe,CAAW,EAAE,CAAC;IACxD,KAAAW,qBAAqB,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IACzD,KAAAY,uBAAuB,GAAG,IAAIZ,eAAe,CAAW,EAAE,CAAC;IAE5D,KAAAa,cAAc,GAAG,IAAI,CAACH,oBAAoB,CAACI,YAAY,EAAE;IACzD,KAAAC,eAAe,GAAG,IAAI,CAACJ,qBAAqB,CAACG,YAAY,EAAE;IAC3D,KAAAE,iBAAiB,GAAG,IAAI,CAACJ,uBAAuB,CAACE,YAAY,EAAE;IAEtE;IACiB,KAAAG,aAAa,GAAG;MAC/BC,SAAS,EAAE,CACT,WAAW,EACX,YAAY,EACZ,WAAW,EACX,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,YAAY,CACb;MACDC,QAAQ,EAAE,CACR,kCAAkC,EAClC,uCAAuC,EACvC,4BAA4B,EAC5B,iCAAiC,EACjC,4BAA4B,EAC5B,kCAAkC,CACnC;MACDC,cAAc,EAAE,CACd,wBAAwB,EACxB,8BAA8B;KAEjC;IAGC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;;;EAGAC,MAAMA,CAACC,WAAwB;IAC7B,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;IAE5B;IACA,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAACL,WAAW,CAACM,KAAK,CAAC;IAE1D;IACA,IAAIF,aAAa,CAACG,WAAW,IAAI,CAACP,WAAW,CAACQ,QAAQ,EAAE;MACtD,OAAO,IAAI,CAACC,oBAAoB,CAACT,WAAW,CAACM,KAAK,EAAEL,SAAS,CAAC;;IAGhE;IACA,IAAIG,aAAa,CAACM,UAAU,EAAE;MAC5BV,WAAW,GAAG,IAAI,CAACW,oBAAoB,CAACX,WAAW,EAAEI,aAAa,CAAC;;IAGrE;IACA,OAAO,IAAI,CAACQ,aAAa,CAACZ,WAAW,EAAEC,SAAS,CAAC;EACnD;EAEA;;;EAGAY,cAAcA,CAACP,KAAa;IAC1B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC9B,OAAOrC,EAAE,CAAC,EAAE,CAAC;;IAGf,MAAMsC,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAACyC,GAAG,CAAC,GAAG,EAAEV,KAAK,CAAC,CAACU,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;IAE1E,OAAO,IAAI,CAACjC,IAAI,CAACkC,GAAG,CAAM,GAAG,IAAI,CAACjC,OAAO,cAAc,EAAE;MAAE+B;IAAM,CAAE,CAAC,CAACG,IAAI,CACvExC,GAAG,CAACyC,QAAQ,IAAIA,QAAQ,CAACC,WAAW,IAAI,EAAE,CAAC,EAC3CzC,UAAU,CAAC,MAAMF,EAAE,CAAC,IAAI,CAAC4C,sBAAsB,CAACf,KAAK,CAAC,CAAC,CAAC,CACzD;EACH;EAEA;;;EAGQD,YAAYA,CAACC,KAAa;IAChC,MAAMgB,eAAe,GAAGhB,KAAK,CAACiB,WAAW,EAAE,CAACC,IAAI,EAAE;IAElD,MAAMC,QAAQ,GAAG;MACflB,WAAW,EAAE,KAAK;MAClBG,UAAU,EAAE,KAAK;MACjBgB,cAAc,EAAE,IAAqB;MACrCC,gBAAgB,EAAE,IAAqB;MACvCC,mBAAmB,EAAE,IAAqB;MAC1CC,UAAU,EAAE;KACb;IAED;IACA,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACrC,aAAa,CAACC,SAAS,EAAE;MAClD,IAAIoC,OAAO,CAACC,IAAI,CAACT,eAAe,CAAC,EAAE;QACjCG,QAAQ,CAAClB,WAAW,GAAG,IAAI;QAC3BkB,QAAQ,CAACE,gBAAgB,GAAG,IAAI,CAACK,0BAA0B,CAACV,eAAe,CAAC;QAC5E;;;IAIJ;IACA,KAAK,MAAMQ,OAAO,IAAI,IAAI,CAACrC,aAAa,CAACE,QAAQ,EAAE;MACjD,MAAMsC,KAAK,GAAGH,OAAO,CAACI,IAAI,CAACZ,eAAe,CAAC;MAC3C,IAAIW,KAAK,EAAE;QACTR,QAAQ,CAACf,UAAU,GAAG,IAAI;QAC1Be,QAAQ,CAACC,cAAc,GAAG,IAAI,CAACS,aAAa,CAACF,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAAC;QAClER,QAAQ,CAACE,gBAAgB,GAAG,IAAI,CAACS,wBAAwB,CAACd,eAAe,CAAC;QAC1EG,QAAQ,CAACI,UAAU,GAAG,GAAG;QACzB;;;IAIJ;IACA,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACrC,aAAa,CAACG,cAAc,EAAE;MACvD,MAAMqC,KAAK,GAAGH,OAAO,CAACI,IAAI,CAACZ,eAAe,CAAC;MAC3C,IAAIW,KAAK,EAAE;QACTR,QAAQ,CAACC,cAAc,GAAG,IAAI,CAACS,aAAa,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;QACtD;;;IAIJ,OAAOR,QAAQ;EACjB;EAEA;;;EAGQhB,oBAAoBA,CAACH,KAAa,EAAEL,SAAiB;IAC3D,MAAMoC,UAAU,GAAG,IAAI,CAACC,0BAA0B,CAAChC,KAAK,CAAC;IAEzD,OAAO7B,EAAE,CAAC;MACR8D,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbpB,WAAW,EAAE,EAAE;MACfqB,gBAAgB,EAAE;QAChBC,cAAc,EAAE,IAAI;QACpBL,UAAU,EAAEA,UAAU;QACtBM,aAAa,EAAErC;OAChB;MACDsC,cAAc,EAAE;QACdC,SAAS,EAAE,WAAW;QACtBC,cAAc,EAAE5C,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;QACtC8C,cAAc,EAAE;;KAEnB,CAAC;EACJ;EAEA;;;EAGQpC,oBAAoBA,CAACX,WAAwB,EAAEyB,QAAa;IAClE,MAAMuB,QAAQ,GAAG;MAAE,GAAGhD;IAAW,CAAE;IAEnC,IAAIyB,QAAQ,CAACC,cAAc,EAAE;MAC3BsB,QAAQ,CAACC,YAAY,GAAGxB,QAAQ,CAACC,cAAc;MAC/CsB,QAAQ,CAACxC,QAAQ,GAAGiB,QAAQ,CAACC,cAAc,KAAK,KAAK,GAAG,KAAK,GAAG,OAAO;;IAGzE,IAAID,QAAQ,CAACE,gBAAgB,EAAE;MAC7BqB,QAAQ,CAACE,WAAW,GAAGzB,QAAQ,CAACE,gBAAgB;;IAGlD,OAAOqB,QAAQ;EACjB;EAEA;;;EAGQpC,aAAaA,CAACZ,WAAwB,EAAEC,SAAiB;IAC/D,IAAIc,MAAM,GAAG,IAAIxC,UAAU,EAAE;IAE7B;IACA,IAAIyB,WAAW,CAACM,KAAK,EAAES,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,GAAG,EAAEhB,WAAW,CAACM,KAAK,CAAC;IAClE,IAAIN,WAAW,CAACQ,QAAQ,EAAEO,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,UAAU,EAAEhB,WAAW,CAACQ,QAAQ,CAAC;IAC/E,IAAIR,WAAW,CAACkD,WAAW,EAAEnC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,aAAa,EAAEhB,WAAW,CAACkD,WAAW,CAAC;IACxF,IAAIlD,WAAW,CAACiD,YAAY,EAAElC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,cAAc,EAAEhB,WAAW,CAACiD,YAAY,CAAC;IAC3F,IAAIjD,WAAW,CAACmD,KAAK,EAAEpC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,OAAO,EAAEhB,WAAW,CAACmD,KAAK,CAAC;IACtE,IAAInD,WAAW,CAACoD,MAAM,EAAErC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAEhB,WAAW,CAACoD,MAAM,CAAC;IAEzE;IACA,IAAIpD,WAAW,CAACqD,UAAU,EAAE;MAC1BtC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,UAAU,EAAEhB,WAAW,CAACqD,UAAU,CAACC,GAAG,CAACC,QAAQ,EAAE,CAAC;MACtExC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,UAAU,EAAEhB,WAAW,CAACqD,UAAU,CAACG,GAAG,CAACD,QAAQ,EAAE,CAAC;;IAGxE;IACA,IAAIvD,WAAW,CAACyD,OAAO,EAAE;MACvBC,MAAM,CAACC,IAAI,CAAC3D,WAAW,CAACyD,OAAO,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;QAC7C,MAAMC,MAAM,GAAG9D,WAAW,CAACyD,OAAQ,CAACI,GAAuC,CAAC;QAC5E,IAAIC,MAAM,IAAIA,MAAM,CAAChD,MAAM,GAAG,CAAC,EAAE;UAC/BC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC6C,GAAG,EAAEC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;;MAE9C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAAChF,IAAI,CAACkC,GAAG,CAAM,GAAG,IAAI,CAACjC,OAAO,WAAW,EAAE;MAAE+B;IAAM,CAAE,CAAC,CAACG,IAAI,CACpExC,GAAG,CAACyC,QAAQ,KAAK;MACfoB,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ,IAAI,EAAE;MACjCC,UAAU,EAAErB,QAAQ,CAACqB,UAAU,IAAI,CAAC;MACpCpB,WAAW,EAAED,QAAQ,CAACC,WAAW,IAAI,EAAE;MACvCwB,cAAc,EAAE;QACdC,SAAS,EAAE,UAAwB;QACnCC,cAAc,EAAE5C,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;QACtC8C,cAAc,EAAE/C,WAAW,CAACyD,OAAO,IAAI;;KAE1C,CAAC,CAAC,EACH9E,UAAU,CAAC,MAAMF,EAAE,CAAC;MAClB8D,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbpB,WAAW,EAAE,EAAE;MACfwB,cAAc,EAAE;QACdC,SAAS,EAAE,UAAwB;QACnCC,cAAc,EAAE5C,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;QACtC8C,cAAc,EAAE;;KAEnB,CAAC,CAAC,CACJ;EACH;EAEA;;;EAGQT,0BAA0BA,CAAChC,KAAa;IAC9C,MAAMgB,eAAe,GAAGhB,KAAK,CAACiB,WAAW,EAAE;IAE3C,IAAID,eAAe,CAAC0C,QAAQ,CAAC,MAAM,CAAC,EAAE;MACpC,OAAO,CACL;QACEC,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,eAAe;QACrBC,WAAW,EAAE,uCAAuC;QACpDC,YAAY,EAAE,EAAE;QAChBnB,YAAY,EAAE,KAAK;QACnBoB,KAAK,EAAE;OACR,EACD;QACEJ,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,qCAAqC;QAClDC,YAAY,EAAE,EAAE;QAChBnB,YAAY,EAAE,OAAO;QACrBoB,KAAK,EAAE;OACR,CACF;;IAGH;IACA,OAAO,EAAE;EACX;EAEA;;;EAGQlC,aAAaA,CAACmC,UAAkB;IACtC,MAAMC,UAAU,GAAGD,UAAU,CAAC/C,WAAW,EAAE;IAC3C,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAACyC,QAAQ,CAACO,UAAU,CAAC,EAAE;MACzD,OAAO,KAAK;;IAEd,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACP,QAAQ,CAACO,UAAU,CAAC,EAAE;MAC/D,OAAO,OAAO;;IAEhB,OAAO,QAAQ;EACjB;EAEA;;;EAGQvC,0BAA0BA,CAAC1B,KAAa;IAC9C,IAAIA,KAAK,CAAC0D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,QAAQ;IAC3C,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,QAAQ;IAC5C,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS;IAC7C,OAAO,EAAE;EACX;EAEA;;;EAGQ5B,wBAAwBA,CAAC9B,KAAa;IAC5C,IAAIA,KAAK,CAAC0D,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,QAAQ;IAC5C,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,QAAQ;IAC5C,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,QAAQ;IAC5C,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,MAAM,CAAC,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,OAAO;IACvE,IAAI1D,KAAK,CAAC0D,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS;IAC7C,OAAO,EAAE;EACX;EAEA;;;EAGQ3C,sBAAsBA,CAACf,KAAa;IAC1C,MAAMc,WAAW,GAAuB,EAAE;IAE1C,IAAId,KAAK,CAACiB,WAAW,EAAE,CAACyC,QAAQ,CAAC,MAAM,CAAC,EAAE;MACxC5C,WAAW,CAACoD,IAAI,CACd;QAAEC,IAAI,EAAE,gBAAgB;QAAEC,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAE,CAAE,EACtD;QAAEF,IAAI,EAAE,kBAAkB;QAAEC,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAE,CAAE,EACxD;QAAEF,IAAI,EAAE,eAAe;QAAEC,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAE,CAAE,EACrD;QAAEF,IAAI,EAAE,aAAa;QAAEC,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAE,CAAE,CACpD;;IAGH,OAAOvD,WAAW;EACpB;EAEA;;;EAGAwD,mBAAmBA,CAACtE,KAAa;IAC/B,MAAMuE,cAAc,GAAG,IAAI,CAAC3F,oBAAoB,CAAC4F,KAAK;IACtD,MAAMC,cAAc,GAAG,CAACzE,KAAK,EAAE,GAAGuE,cAAc,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK3E,KAAK,CAAC,CAAC,CAAC4E,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACvF,IAAI,CAAChG,oBAAoB,CAACiG,IAAI,CAACJ,cAAc,CAAC;IAC9CK,YAAY,CAACC,OAAO,CAAC,yBAAyB,EAAEC,IAAI,CAACC,SAAS,CAACR,cAAc,CAAC,CAAC;EACjF;EAEA;;;EAGQlF,iBAAiBA,CAAA;IACvB,MAAM2F,MAAM,GAAGJ,YAAY,CAACK,OAAO,CAAC,yBAAyB,CAAC;IAC9D,IAAID,MAAM,EAAE;MACV,IAAI;QACF,MAAME,OAAO,GAAGJ,IAAI,CAACK,KAAK,CAACH,MAAM,CAAC;QAClC,IAAI,CAACtG,oBAAoB,CAACiG,IAAI,CAACO,OAAO,CAAC;OACxC,CAAC,OAAOE,CAAC,EAAE;QACVC,OAAO,CAACC,IAAI,CAAC,+BAA+B,CAAC;;;EAGnD;EAEA;;;EAGQhG,oBAAoBA,CAAA;IAC1B;IACA,MAAMiG,QAAQ,GAAG,CACf,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,gBAAgB,EAChB,aAAa,CACd;IACD,IAAI,CAAC3G,uBAAuB,CAAC+F,IAAI,CAACY,QAAQ,CAAC;EAC7C;EAEA;;;EAGAC,kBAAkBA,CAAA;IAChB,IAAI,CAAC9G,oBAAoB,CAACiG,IAAI,CAAC,EAAE,CAAC;IAClCC,YAAY,CAACa,UAAU,CAAC,yBAAyB,CAAC;EACpD;;;uBArWWpH,qBAAqB,EAAAqH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArBxH,qBAAqB;MAAAyH,OAAA,EAArBzH,qBAAqB,CAAA0H,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}