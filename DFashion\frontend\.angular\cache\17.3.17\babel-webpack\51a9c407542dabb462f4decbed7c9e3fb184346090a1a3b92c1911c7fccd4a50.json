{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { Subject } from 'rxjs';\nimport { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/order.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/table\";\nimport * as i10 from \"@angular/material/paginator\";\nimport * as i11 from \"@angular/material/sort\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/tooltip\";\nconst _c0 = () => [5, 10, 25, 50];\nfunction OrderManagementComponent_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r1.label, \" \");\n  }\n}\nfunction OrderManagementComponent_mat_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r2.label, \" \");\n  }\n}\nfunction OrderManagementComponent_th_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Order #\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const order_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(order_r3.orderNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatDate(order_r3.orderDate));\n  }\n}\nfunction OrderManagementComponent_th_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Customer\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(order_r5.customer == null ? null : order_r5.customer.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(order_r5.customer == null ? null : order_r5.customer.email);\n  }\n}\nfunction OrderManagementComponent_th_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Items\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (order_r6.items == null ? null : order_r6.items.length) || 0, \" item(s)\");\n  }\n}\nfunction OrderManagementComponent_th_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Amount\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 33)(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", order_r7.totalAmount, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 2, order_r7.paymentMethod));\n  }\n}\nfunction OrderManagementComponent_th_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 36)(2, \"span\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r3.getStatusColor(order_r8.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, order_r8.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r3.getPaymentStatusColor(order_r8.paymentStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 8, order_r8.paymentStatus), \" \");\n  }\n}\nfunction OrderManagementComponent_th_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 39)(2, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function OrderManagementComponent_td_44_Template_button_click_2_listener() {\n      const order_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.viewOrder(order_r10));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function OrderManagementComponent_td_44_Template_button_click_5_listener() {\n      const order_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.updateOrderStatus(order_r10));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function OrderManagementComponent_td_44_Template_button_click_8_listener() {\n      const order_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.printInvoice(order_r10));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"print\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction OrderManagementComponent_tr_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 43);\n  }\n}\nfunction OrderManagementComponent_tr_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 44);\n  }\n}\nfunction OrderManagementComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"shopping_cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No orders found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class OrderManagementComponent {\n  constructor(orderService, dialog, snackBar) {\n    this.orderService = orderService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.destroy$ = new Subject();\n    this.displayedColumns = ['orderNumber', 'customer', 'items', 'amount', 'status', 'actions'];\n    this.dataSource = new MatTableDataSource([]);\n    this.isLoading = false;\n    this.totalOrders = 0;\n    // Filters\n    this.searchControl = new FormControl('');\n    this.statusFilter = new FormControl('');\n    this.paymentStatusFilter = new FormControl('');\n    this.statuses = [{\n      value: '',\n      label: 'All Statuses'\n    }, {\n      value: 'pending',\n      label: 'Pending'\n    }, {\n      value: 'confirmed',\n      label: 'Confirmed'\n    }, {\n      value: 'shipped',\n      label: 'Shipped'\n    }, {\n      value: 'delivered',\n      label: 'Delivered'\n    }, {\n      value: 'cancelled',\n      label: 'Cancelled'\n    }];\n    this.paymentStatuses = [{\n      value: '',\n      label: 'All Payment Statuses'\n    }, {\n      value: 'pending',\n      label: 'Pending'\n    }, {\n      value: 'paid',\n      label: 'Paid'\n    }, {\n      value: 'failed',\n      label: 'Failed'\n    }, {\n      value: 'refunded',\n      label: 'Refunded'\n    }];\n  }\n  ngOnInit() {\n    this.setupFilters();\n    this.loadOrders();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setupFilters() {\n    this.searchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {\n      this.loadOrders();\n    });\n    [this.statusFilter, this.paymentStatusFilter].forEach(control => {\n      control.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.loadOrders();\n      });\n    });\n  }\n  loadOrders() {\n    this.isLoading = true;\n    // Load orders from API\n    this.dataSource.data = [];\n    this.totalOrders = 0;\n    this.isLoading = false;\n  }\n  onPageChange() {\n    this.loadOrders();\n  }\n  viewOrder(order) {\n    this.snackBar.open('Order details view - Coming soon', 'Close', {\n      duration: 3000\n    });\n  }\n  updateOrderStatus(order) {\n    this.snackBar.open('Order status update - Coming soon', 'Close', {\n      duration: 3000\n    });\n  }\n  printInvoice(order) {\n    this.snackBar.open('Invoice printing - Coming soon', 'Close', {\n      duration: 3000\n    });\n  }\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n  getStatusColor(status) {\n    const statusColors = {\n      'pending': '#ff9800',\n      'confirmed': '#2196f3',\n      'shipped': '#9c27b0',\n      'delivered': '#4caf50',\n      'cancelled': '#f44336'\n    };\n    return statusColors[status] || '#666666';\n  }\n  getPaymentStatusColor(status) {\n    const statusColors = {\n      'pending': '#ff9800',\n      'paid': '#4caf50',\n      'failed': '#f44336',\n      'refunded': '#9e9e9e'\n    };\n    return statusColors[status] || '#666666';\n  }\n  static {\n    this.ɵfac = function OrderManagementComponent_Factory(t) {\n      return new (t || OrderManagementComponent)(i0.ɵɵdirectiveInject(i1.OrderService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OrderManagementComponent,\n      selectors: [[\"app-order-management\"]],\n      viewQuery: function OrderManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      decls: 49,\n      vars: 13,\n      consts: [[1, \"order-management\"], [1, \"filters-section\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Search by order number or customer\", 3, \"formControl\"], [\"matSuffix\", \"\"], [3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"orderNumber\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"customer\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"items\"], [\"matColumnDef\", \"amount\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"page\", \"length\", \"pageSize\", \"pageSizeOptions\"], [3, \"value\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"order-number\"], [1, \"order-date\"], [\"mat-header-cell\", \"\"], [1, \"customer-info\"], [1, \"customer-name\"], [1, \"customer-email\"], [1, \"items-info\"], [1, \"items-count\"], [1, \"amount-cell\"], [1, \"total-amount\"], [1, \"payment-method\"], [1, \"status-cell\"], [1, \"status-chip\"], [1, \"payment-status\"], [1, \"actions-cell\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View order details\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Update status\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Print invoice\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"]],\n      template: function OrderManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4, \"Order Management\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n          i0.ɵɵtext(6, \"Track and manage customer orders\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 1)(9, \"mat-form-field\", 2)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Search orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 3);\n          i0.ɵɵelementStart(13, \"mat-icon\", 4);\n          i0.ɵɵtext(14, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-form-field\", 2)(16, \"mat-label\");\n          i0.ɵɵtext(17, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-select\", 5);\n          i0.ɵɵtemplate(19, OrderManagementComponent_mat_option_19_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"mat-form-field\", 2)(21, \"mat-label\");\n          i0.ɵɵtext(22, \"Payment Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"mat-select\", 5);\n          i0.ɵɵtemplate(24, OrderManagementComponent_mat_option_24_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 7)(26, \"table\", 8);\n          i0.ɵɵelementContainerStart(27, 9);\n          i0.ɵɵtemplate(28, OrderManagementComponent_th_28_Template, 2, 0, \"th\", 10)(29, OrderManagementComponent_td_29_Template, 5, 2, \"td\", 11);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(30, 12);\n          i0.ɵɵtemplate(31, OrderManagementComponent_th_31_Template, 2, 0, \"th\", 13)(32, OrderManagementComponent_td_32_Template, 6, 2, \"td\", 11);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(33, 14);\n          i0.ɵɵtemplate(34, OrderManagementComponent_th_34_Template, 2, 0, \"th\", 13)(35, OrderManagementComponent_td_35_Template, 4, 1, \"td\", 11);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(36, 15);\n          i0.ɵɵtemplate(37, OrderManagementComponent_th_37_Template, 2, 0, \"th\", 10)(38, OrderManagementComponent_td_38_Template, 7, 4, \"td\", 11);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(39, 16);\n          i0.ɵɵtemplate(40, OrderManagementComponent_th_40_Template, 2, 0, \"th\", 13)(41, OrderManagementComponent_td_41_Template, 8, 10, \"td\", 11);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(42, 17);\n          i0.ɵɵtemplate(43, OrderManagementComponent_th_43_Template, 2, 0, \"th\", 13)(44, OrderManagementComponent_td_44_Template, 11, 0, \"td\", 11);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(45, OrderManagementComponent_tr_45_Template, 1, 0, \"tr\", 18)(46, OrderManagementComponent_tr_46_Template, 1, 0, \"tr\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, OrderManagementComponent_div_47_Template, 7, 0, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"mat-paginator\", 21);\n          i0.ɵɵlistener(\"page\", function OrderManagementComponent_Template_mat_paginator_page_48_listener() {\n            return ctx.onPageChange();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formControl\", ctx.searchControl);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.statusFilter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.statuses);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formControl\", ctx.paymentStatusFilter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.paymentStatuses);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dataSource.data.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"length\", ctx.totalOrders)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(12, _c0));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlDirective, i6.MatIcon, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatTable, i9.MatHeaderCellDef, i9.MatHeaderRowDef, i9.MatColumnDef, i9.MatCellDef, i9.MatRowDef, i9.MatHeaderCell, i9.MatCell, i9.MatHeaderRow, i9.MatRow, i10.MatPaginator, i11.MatSort, i11.MatSortHeader, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatSuffix, i14.MatSelect, i15.MatOption, i16.MatTooltip, i4.TitleCasePipe],\n      styles: [\".order-management[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n  flex-wrap: wrap;\\n  align-items: center;\\n}\\n.filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  margin-bottom: 1rem;\\n}\\n\\n.order-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1976d2;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.order-date[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n\\n.customer-info[_ngcontent-%COMP%]   .customer-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n}\\n.customer-info[_ngcontent-%COMP%]   .customer-email[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n\\n.items-info[_ngcontent-%COMP%]   .items-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n\\n.amount-cell[_ngcontent-%COMP%]   .total-amount[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2e7d32;\\n  display: block;\\n  margin-bottom: 0.25rem;\\n}\\n.amount-cell[_ngcontent-%COMP%]   .payment-method[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #666;\\n  text-transform: uppercase;\\n}\\n\\n.status-cell[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: white;\\n  margin-bottom: 0.25rem;\\n}\\n.status-cell[_ngcontent-%COMP%]   .payment-status[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.actions-cell[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem;\\n  color: #666;\\n}\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 1rem 0 0.5rem 0;\\n  font-weight: 400;\\n}\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .filters-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n  .customer-info[_ngcontent-%COMP%], .amount-cell[_ngcontent-%COMP%], .status-cell[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MatTableDataSource", "MatPaginator", "MatSort", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "FormControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "status_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "status_r2", "ɵɵtextInterpolate", "order_r3", "orderNumber", "ctx_r3", "formatDate", "orderDate", "order_r5", "customer", "fullName", "email", "order_r6", "items", "length", "order_r7", "totalAmount", "ɵɵpipeBind1", "paymentMethod", "ɵɵstyleProp", "getStatusColor", "order_r8", "status", "getPaymentStatusColor", "paymentStatus", "ɵɵlistener", "OrderManagementComponent_td_44_Template_button_click_2_listener", "order_r10", "ɵɵrestoreView", "_r9", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewOrder", "OrderManagementComponent_td_44_Template_button_click_5_listener", "updateOrderStatus", "OrderManagementComponent_td_44_Template_button_click_8_listener", "printInvoice", "ɵɵelement", "OrderManagementComponent", "constructor", "orderService", "dialog", "snackBar", "destroy$", "displayedColumns", "dataSource", "isLoading", "totalOrders", "searchControl", "statusFilter", "paymentStatusFilter", "statuses", "paymentStatuses", "ngOnInit", "setupFilters", "loadOrders", "ngOnDestroy", "next", "complete", "valueChanges", "pipe", "subscribe", "for<PERSON>ach", "control", "data", "onPageChange", "order", "open", "duration", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "statusColors", "ɵɵdirectiveInject", "i1", "OrderService", "i2", "MatDialog", "i3", "MatSnackBar", "selectors", "viewQuery", "OrderManagementComponent_Query", "rf", "ctx", "ɵɵtemplate", "OrderManagementComponent_mat_option_19_Template", "OrderManagementComponent_mat_option_24_Template", "ɵɵelementContainerStart", "OrderManagementComponent_th_28_Template", "OrderManagementComponent_td_29_Template", "OrderManagementComponent_th_31_Template", "OrderManagementComponent_td_32_Template", "OrderManagementComponent_th_34_Template", "OrderManagementComponent_td_35_Template", "OrderManagementComponent_th_37_Template", "OrderManagementComponent_td_38_Template", "OrderManagementComponent_th_40_Template", "OrderManagementComponent_td_41_Template", "OrderManagementComponent_th_43_Template", "OrderManagementComponent_td_44_Template", "OrderManagementComponent_tr_45_Template", "OrderManagementComponent_tr_46_Template", "OrderManagementComponent_div_47_Template", "OrderManagementComponent_Template_mat_paginator_page_48_listener", "ɵɵpureFunction0", "_c0"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\orders\\order-management.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Subject } from 'rxjs';\nimport { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\nimport { OrderService } from '../services/order.service';\n\nexport interface Order {\n  _id: string;\n  orderNumber: string;\n  customer: {\n    fullName: string;\n    email: string;\n  };\n  items: any[];\n  totalAmount: number;\n  status: string;\n  paymentStatus: string;\n  paymentMethod: string;\n  orderDate: string;\n  expectedDelivery: string;\n  shippingAddress: any;\n}\n\n@Component({\n  selector: 'app-order-management',\n  template: `\n    <div class=\"order-management\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>Order Management</mat-card-title>\n          <mat-card-subtitle>Track and manage customer orders</mat-card-subtitle>\n        </mat-card-header>\n        \n        <mat-card-content>\n          <!-- Filters -->\n          <div class=\"filters-section\">\n            <mat-form-field appearance=\"outline\">\n              <mat-label>Search orders</mat-label>\n              <input matInput [formControl]=\"searchControl\" placeholder=\"Search by order number or customer\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n            \n            <mat-form-field appearance=\"outline\">\n              <mat-label>Status</mat-label>\n              <mat-select [formControl]=\"statusFilter\">\n                <mat-option *ngFor=\"let status of statuses\" [value]=\"status.value\">\n                  {{ status.label }}\n                </mat-option>\n              </mat-select>\n            </mat-form-field>\n            \n            <mat-form-field appearance=\"outline\">\n              <mat-label>Payment Status</mat-label>\n              <mat-select [formControl]=\"paymentStatusFilter\">\n                <mat-option *ngFor=\"let status of paymentStatuses\" [value]=\"status.value\">\n                  {{ status.label }}\n                </mat-option>\n              </mat-select>\n            </mat-form-field>\n          </div>\n          \n          <!-- Orders Table -->\n          <div class=\"table-container\">\n            <table mat-table [dataSource]=\"dataSource\" matSort>\n              <!-- Order Number Column -->\n              <ng-container matColumnDef=\"orderNumber\">\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Order #</th>\n                <td mat-cell *matCellDef=\"let order\">\n                  <div class=\"order-number\">{{ order.orderNumber }}</div>\n                  <div class=\"order-date\">{{ formatDate(order.orderDate) }}</div>\n                </td>\n              </ng-container>\n              \n              <!-- Customer Column -->\n              <ng-container matColumnDef=\"customer\">\n                <th mat-header-cell *matHeaderCellDef>Customer</th>\n                <td mat-cell *matCellDef=\"let order\">\n                  <div class=\"customer-info\">\n                    <div class=\"customer-name\">{{ order.customer?.fullName }}</div>\n                    <div class=\"customer-email\">{{ order.customer?.email }}</div>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <!-- Items Column -->\n              <ng-container matColumnDef=\"items\">\n                <th mat-header-cell *matHeaderCellDef>Items</th>\n                <td mat-cell *matCellDef=\"let order\">\n                  <div class=\"items-info\">\n                    <span class=\"items-count\">{{ order.items?.length || 0 }} item(s)</span>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <!-- Amount Column -->\n              <ng-container matColumnDef=\"amount\">\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>\n                <td mat-cell *matCellDef=\"let order\">\n                  <div class=\"amount-cell\">\n                    <span class=\"total-amount\">₹{{ order.totalAmount }}</span>\n                    <div class=\"payment-method\">{{ order.paymentMethod | titlecase }}</div>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <!-- Status Column -->\n              <ng-container matColumnDef=\"status\">\n                <th mat-header-cell *matHeaderCellDef>Status</th>\n                <td mat-cell *matCellDef=\"let order\">\n                  <div class=\"status-cell\">\n                    <span class=\"status-chip\" [style.background-color]=\"getStatusColor(order.status)\">\n                      {{ order.status | titlecase }}\n                    </span>\n                    <span class=\"payment-status\" [style.color]=\"getPaymentStatusColor(order.paymentStatus)\">\n                      {{ order.paymentStatus | titlecase }}\n                    </span>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <!-- Actions Column -->\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let order\">\n                  <div class=\"actions-cell\">\n                    <button mat-icon-button matTooltip=\"View order details\" (click)=\"viewOrder(order)\">\n                      <mat-icon>visibility</mat-icon>\n                    </button>\n                    <button mat-icon-button matTooltip=\"Update status\" (click)=\"updateOrderStatus(order)\">\n                      <mat-icon>edit</mat-icon>\n                    </button>\n                    <button mat-icon-button matTooltip=\"Print invoice\" (click)=\"printInvoice(order)\">\n                      <mat-icon>print</mat-icon>\n                    </button>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n            </table>\n            \n            <!-- No Data Message -->\n            <div *ngIf=\"dataSource.data.length === 0\" class=\"no-data\">\n              <mat-icon>shopping_cart</mat-icon>\n              <h3>No orders found</h3>\n              <p>Try adjusting your search criteria.</p>\n            </div>\n          </div>\n          \n          <!-- Paginator -->\n          <mat-paginator \n            [length]=\"totalOrders\"\n            [pageSize]=\"10\"\n            [pageSizeOptions]=\"[5, 10, 25, 50]\"\n            (page)=\"onPageChange()\"\n            showFirstLastButtons>\n          </mat-paginator>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styleUrls: ['./order-management.component.scss']\n})\nexport class OrderManagementComponent implements OnInit, OnDestroy {\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  private destroy$ = new Subject<void>();\n  \n  displayedColumns: string[] = ['orderNumber', 'customer', 'items', 'amount', 'status', 'actions'];\n  dataSource = new MatTableDataSource<Order>([]);\n  isLoading = false;\n  totalOrders = 0;\n  \n  // Filters\n  searchControl = new FormControl('');\n  statusFilter = new FormControl('');\n  paymentStatusFilter = new FormControl('');\n  \n  statuses = [\n    { value: '', label: 'All Statuses' },\n    { value: 'pending', label: 'Pending' },\n    { value: 'confirmed', label: 'Confirmed' },\n    { value: 'shipped', label: 'Shipped' },\n    { value: 'delivered', label: 'Delivered' },\n    { value: 'cancelled', label: 'Cancelled' }\n  ];\n  \n  paymentStatuses = [\n    { value: '', label: 'All Payment Statuses' },\n    { value: 'pending', label: 'Pending' },\n    { value: 'paid', label: 'Paid' },\n    { value: 'failed', label: 'Failed' },\n    { value: 'refunded', label: 'Refunded' }\n  ];\n\n  constructor(\n    private orderService: OrderService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.setupFilters();\n    this.loadOrders();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  setupFilters(): void {\n    this.searchControl.valueChanges.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      takeUntil(this.destroy$)\n    ).subscribe(() => {\n      this.loadOrders();\n    });\n\n    [this.statusFilter, this.paymentStatusFilter].forEach(control => {\n      control.valueChanges.pipe(\n        takeUntil(this.destroy$)\n      ).subscribe(() => {\n        this.loadOrders();\n      });\n    });\n  }\n\n  loadOrders(): void {\n    this.isLoading = true;\n\n    // Load orders from API\n    this.dataSource.data = [];\n    this.totalOrders = 0;\n    this.isLoading = false;\n  }\n\n  onPageChange(): void {\n    this.loadOrders();\n  }\n\n  viewOrder(order: Order): void {\n    this.snackBar.open('Order details view - Coming soon', 'Close', { duration: 3000 });\n  }\n\n  updateOrderStatus(order: Order): void {\n    this.snackBar.open('Order status update - Coming soon', 'Close', { duration: 3000 });\n  }\n\n  printInvoice(order: Order): void {\n    this.snackBar.open('Invoice printing - Coming soon', 'Close', { duration: 3000 });\n  }\n\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n\n  getStatusColor(status: string): string {\n    const statusColors: { [key: string]: string } = {\n      'pending': '#ff9800',\n      'confirmed': '#2196f3',\n      'shipped': '#9c27b0',\n      'delivered': '#4caf50',\n      'cancelled': '#f44336'\n    };\n    return statusColors[status] || '#666666';\n  }\n\n  getPaymentStatusColor(status: string): string {\n    const statusColors: { [key: string]: string } = {\n      'pending': '#ff9800',\n      'paid': '#4caf50',\n      'failed': '#f44336',\n      'refunded': '#9e9e9e'\n    };\n    return statusColors[status] || '#666666';\n  }\n}\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAGhD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;AAC9E,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;IA0C5BC,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+BH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IAChEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAOAT,EAAA,CAAAC,cAAA,qBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAJ,KAAA,CAAsB;IACvEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,SAAA,CAAAD,KAAA,MACF;;;;;IAUAT,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhEH,EADF,CAAAC,cAAA,aAAqC,cACT;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAC3DF,EAD2D,CAAAG,YAAA,EAAM,EAC5D;;;;;IAFuBH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAW,iBAAA,CAAAC,QAAA,CAAAC,WAAA,CAAuB;IACzBb,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAW,iBAAA,CAAAG,MAAA,CAAAC,UAAA,CAAAH,QAAA,CAAAI,SAAA,EAAiC;;;;;IAM3DhB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG/CH,EAFJ,CAAAC,cAAA,aAAqC,cACR,cACE;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAE3DF,EAF2D,CAAAG,YAAA,EAAM,EACzD,EACH;;;;IAH0BH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAW,iBAAA,CAAAM,QAAA,CAAAC,QAAA,kBAAAD,QAAA,CAAAC,QAAA,CAAAC,QAAA,CAA8B;IAC7BnB,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAW,iBAAA,CAAAM,QAAA,CAAAC,QAAA,kBAAAD,QAAA,CAAAC,QAAA,CAAAE,KAAA,CAA2B;;;;;IAO3DpB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG5CH,EAFJ,CAAAC,cAAA,aAAqC,cACX,eACI;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAEpEF,EAFoE,CAAAG,YAAA,EAAO,EACnE,EACH;;;;IAFyBH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAQ,kBAAA,MAAAa,QAAA,CAAAC,KAAA,kBAAAD,QAAA,CAAAC,KAAA,CAAAC,MAAA,mBAAsC;;;;;IAOpEvB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7DH,EAFJ,CAAAC,cAAA,aAAqC,cACV,eACI;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAqC;;IAErEF,EAFqE,CAAAG,YAAA,EAAM,EACnE,EACH;;;;IAH0BH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,kBAAA,WAAAgB,QAAA,CAAAC,WAAA,KAAwB;IACvBzB,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAA0B,WAAA,OAAAF,QAAA,CAAAG,aAAA,EAAqC;;;;;IAOrE3B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAFJ,CAAAC,cAAA,aAAqC,cACV,eAC2D;IAChFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACH;;;;;IAPyBH,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAA4B,WAAA,qBAAAd,MAAA,CAAAe,cAAA,CAAAC,QAAA,CAAAC,MAAA,EAAuD;IAC/E/B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA0B,WAAA,OAAAI,QAAA,CAAAC,MAAA,OACF;IAC6B/B,EAAA,CAAAO,SAAA,GAA0D;IAA1DP,EAAA,CAAA4B,WAAA,UAAAd,MAAA,CAAAkB,qBAAA,CAAAF,QAAA,CAAAG,aAAA,EAA0D;IACrFjC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA0B,WAAA,OAAAI,QAAA,CAAAG,aAAA,OACF;;;;;IAOJjC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAG9CH,EAFJ,CAAAC,cAAA,aAAqC,cACT,iBAC2D;IAA3BD,EAAA,CAAAkC,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,SAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAzB,MAAA,GAAAd,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAAS3B,MAAA,CAAA4B,SAAA,CAAAN,SAAA,CAAgB;IAAA,EAAC;IAChFpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,iBAAsF;IAAnCD,EAAA,CAAAkC,UAAA,mBAAAS,gEAAA;MAAA,MAAAP,SAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAzB,MAAA,GAAAd,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAAS3B,MAAA,CAAA8B,iBAAA,CAAAR,SAAA,CAAwB;IAAA,EAAC;IACnFpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAAiF;IAA9BD,EAAA,CAAAkC,UAAA,mBAAAW,gEAAA;MAAA,MAAAT,SAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAzB,MAAA,GAAAd,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAAS3B,MAAA,CAAAgC,YAAA,CAAAV,SAAA,CAAmB;IAAA,EAAC;IAC9EpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAGrBF,EAHqB,CAAAG,YAAA,EAAW,EACnB,EACL,EACH;;;;;IAGPH,EAAA,CAAA+C,SAAA,aAA4D;;;;;IAC5D/C,EAAA,CAAA+C,SAAA,aAAkE;;;;;IAKlE/C,EADF,CAAAC,cAAA,cAA0D,eAC9C;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IACxCF,EADwC,CAAAG,YAAA,EAAI,EACtC;;;AAiBlB,OAAM,MAAO6C,wBAAwB;EAiCnCC,YACUC,YAA0B,EAC1BC,MAAiB,EACjBC,QAAqB;IAFrB,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAhCV,KAAAC,QAAQ,GAAG,IAAI1D,OAAO,EAAQ;IAEtC,KAAA2D,gBAAgB,GAAa,CAAC,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;IAChG,KAAAC,UAAU,GAAG,IAAI/D,kBAAkB,CAAQ,EAAE,CAAC;IAC9C,KAAAgE,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,aAAa,GAAG,IAAI3D,WAAW,CAAC,EAAE,CAAC;IACnC,KAAA4D,YAAY,GAAG,IAAI5D,WAAW,CAAC,EAAE,CAAC;IAClC,KAAA6D,mBAAmB,GAAG,IAAI7D,WAAW,CAAC,EAAE,CAAC;IAEzC,KAAA8D,QAAQ,GAAG,CACT;MAAEvD,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAc,CAAE,EACpC;MAAEH,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEH,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAW,CAAE,CAC3C;IAED,KAAAqD,eAAe,GAAG,CAChB;MAAExD,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAsB,CAAE,EAC5C;MAAEH,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEH,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAU,CAAE,CACzC;EAME;EAEHsD,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,QAAQ,CAACc,IAAI,EAAE;IACpB,IAAI,CAACd,QAAQ,CAACe,QAAQ,EAAE;EAC1B;EAEAJ,YAAYA,CAAA;IACV,IAAI,CAACN,aAAa,CAACW,YAAY,CAACC,IAAI,CAClCzE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAACyD,QAAQ,CAAC,CACzB,CAACkB,SAAS,CAAC,MAAK;MACf,IAAI,CAACN,UAAU,EAAE;IACnB,CAAC,CAAC;IAEF,CAAC,IAAI,CAACN,YAAY,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAACY,OAAO,CAACC,OAAO,IAAG;MAC9DA,OAAO,CAACJ,YAAY,CAACC,IAAI,CACvB1E,SAAS,CAAC,IAAI,CAACyD,QAAQ,CAAC,CACzB,CAACkB,SAAS,CAAC,MAAK;QACf,IAAI,CAACN,UAAU,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAA,UAAUA,CAAA;IACR,IAAI,CAACT,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACD,UAAU,CAACmB,IAAI,GAAG,EAAE;IACzB,IAAI,CAACjB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACD,SAAS,GAAG,KAAK;EACxB;EAEAmB,YAAYA,CAAA;IACV,IAAI,CAACV,UAAU,EAAE;EACnB;EAEAvB,SAASA,CAACkC,KAAY;IACpB,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,kCAAkC,EAAE,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACrF;EAEAlC,iBAAiBA,CAACgC,KAAY;IAC5B,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACtF;EAEAhC,YAAYA,CAAC8B,KAAY;IACvB,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACnF;EAEA/D,UAAUA,CAACgE,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAvD,cAAcA,CAACE,MAAc;IAC3B,MAAMsD,YAAY,GAA8B;MAC9C,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE;KACd;IACD,OAAOA,YAAY,CAACtD,MAAM,CAAC,IAAI,SAAS;EAC1C;EAEAC,qBAAqBA,CAACD,MAAc;IAClC,MAAMsD,YAAY,GAA8B;MAC9C,SAAS,EAAE,SAAS;MACpB,MAAM,EAAE,SAAS;MACjB,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE;KACb;IACD,OAAOA,YAAY,CAACtD,MAAM,CAAC,IAAI,SAAS;EAC1C;;;uBAxHWiB,wBAAwB,EAAAhD,EAAA,CAAAsF,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAxF,EAAA,CAAAsF,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAA1F,EAAA,CAAAsF,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAxB5C,wBAAwB;MAAA6C,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBACxBvG,YAAY;yBACZC,OAAO;;;;;;;;;;;;;UAzIVM,EAHN,CAAAC,cAAA,aAA8B,eAClB,sBACS,qBACC;UAAAD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjDH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,uCAAgC;UACrDF,EADqD,CAAAG,YAAA,EAAoB,EACvD;UAMZH,EAJN,CAAAC,cAAA,uBAAkB,aAEa,wBACU,iBACxB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAA+C,SAAA,gBAA+F;UAC/F/C,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAC,cAAA,qBAAyC;UACvCD,EAAA,CAAAkG,UAAA,KAAAC,+CAAA,wBAAmE;UAIvEnG,EADE,CAAAG,YAAA,EAAa,EACE;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,qBAAgD;UAC9CD,EAAA,CAAAkG,UAAA,KAAAE,+CAAA,wBAA0E;UAKhFpG,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAIJH,EADF,CAAAC,cAAA,cAA6B,gBACwB;UAEjDD,EAAA,CAAAqG,uBAAA,OAAyC;UAEvCrG,EADA,CAAAkG,UAAA,KAAAI,uCAAA,iBAAsD,KAAAC,uCAAA,iBACjB;;UAOvCvG,EAAA,CAAAqG,uBAAA,QAAsC;UAEpCrG,EADA,CAAAkG,UAAA,KAAAM,uCAAA,iBAAsC,KAAAC,uCAAA,iBACD;;UASvCzG,EAAA,CAAAqG,uBAAA,QAAmC;UAEjCrG,EADA,CAAAkG,UAAA,KAAAQ,uCAAA,iBAAsC,KAAAC,uCAAA,iBACD;;UAQvC3G,EAAA,CAAAqG,uBAAA,QAAoC;UAElCrG,EADA,CAAAkG,UAAA,KAAAU,uCAAA,iBAAsD,KAAAC,uCAAA,iBACjB;;UASvC7G,EAAA,CAAAqG,uBAAA,QAAoC;UAElCrG,EADA,CAAAkG,UAAA,KAAAY,uCAAA,iBAAsC,KAAAC,uCAAA,kBACD;;UAavC/G,EAAA,CAAAqG,uBAAA,QAAqC;UAEnCrG,EADA,CAAAkG,UAAA,KAAAc,uCAAA,iBAAsC,KAAAC,uCAAA,kBACD;;UAgBvCjH,EADA,CAAAkG,UAAA,KAAAgB,uCAAA,iBAAuD,KAAAC,uCAAA,iBACM;UAC/DnH,EAAA,CAAAG,YAAA,EAAQ;UAGRH,EAAA,CAAAkG,UAAA,KAAAkB,wCAAA,kBAA0D;UAK5DpH,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,yBAKuB;UADrBD,EAAA,CAAAkC,UAAA,kBAAAmF,iEAAA;YAAA,OAAQpB,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC;UAK/B3E,EAHM,CAAAG,YAAA,EAAgB,EACC,EACV,EACP;;;UA1HoBH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,gBAAA6F,GAAA,CAAAvC,aAAA,CAA6B;UAMjC1D,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAA6F,GAAA,CAAAtC,YAAA,CAA4B;UACP3D,EAAA,CAAAO,SAAA,EAAW;UAAXP,EAAA,CAAAI,UAAA,YAAA6F,GAAA,CAAApC,QAAA,CAAW;UAQhC7D,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAI,UAAA,gBAAA6F,GAAA,CAAArC,mBAAA,CAAmC;UACd5D,EAAA,CAAAO,SAAA,EAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAA6F,GAAA,CAAAnC,eAAA,CAAkB;UASpC9D,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,eAAA6F,GAAA,CAAA1C,UAAA,CAAyB;UA2EpBvD,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAI,UAAA,oBAAA6F,GAAA,CAAA3C,gBAAA,CAAiC;UACpBtD,EAAA,CAAAO,SAAA,EAA0B;UAA1BP,EAAA,CAAAI,UAAA,qBAAA6F,GAAA,CAAA3C,gBAAA,CAA0B;UAIvDtD,EAAA,CAAAO,SAAA,EAAkC;UAAlCP,EAAA,CAAAI,UAAA,SAAA6F,GAAA,CAAA1C,UAAA,CAAAmB,IAAA,CAAAnD,MAAA,OAAkC;UASxCvB,EAAA,CAAAO,SAAA,EAAsB;UAEtBP,EAFA,CAAAI,UAAA,WAAA6F,GAAA,CAAAxC,WAAA,CAAsB,gBACP,oBAAAzD,EAAA,CAAAsH,eAAA,KAAAC,GAAA,EACoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}