{"ast": null, "code": "import { AuthGuard } from '../../core/guards/auth.guard';\nexport const vendorRoutes = [{\n  path: '',\n  loadComponent: () => import('./pages/dashboard/vendor-dashboard.component').then(m => m.VendorDashboardComponent),\n  canActivate: [AuthGuard],\n  title: 'Vendor Dashboard - DFashion'\n}, {\n  path: 'products',\n  loadComponent: () => import('./pages/products/vendor-products.component').then(m => m.VendorProductsComponent),\n  canActivate: [AuthGuard],\n  title: 'My Products - DFashion'\n}, {\n  path: 'products/create',\n  loadComponent: () => import('./pages/products/create-product.component').then(m => m.CreateProductComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Product - DFashion'\n}, {\n  path: 'posts',\n  loadComponent: () => import('./pages/posts/vendor-posts.component').then(m => m.VendorPostsComponent),\n  canActivate: [AuthGuard],\n  title: 'My Posts - DFashion'\n}, {\n  path: 'posts/create',\n  loadComponent: () => import('./pages/posts/create-post.component').then(m => m.CreatePostComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Post - DFashion'\n}, {\n  path: 'stories',\n  loadComponent: () => import('./pages/stories/vendor-stories.component').then(m => m.VendorStoriesComponent),\n  canActivate: [AuthGuard],\n  title: 'My Stories - DFashion'\n}, {\n  path: 'stories/create',\n  loadComponent: () => import('./pages/stories/create-story.component').then(m => m.CreateStoryComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Story - DFashion'\n}, {\n  path: 'orders',\n  loadComponent: () => import('./pages/orders/vendor-orders.component').then(m => m.VendorOrdersComponent),\n  canActivate: [AuthGuard],\n  title: 'Orders - DFashion'\n}, {\n  path: 'analytics',\n  loadComponent: () => import('./pages/analytics/vendor-analytics.component').then(m => m.VendorAnalyticsComponent),\n  canActivate: [AuthGuard],\n  title: 'Analytics - DFashion'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "vendorRoutes", "path", "loadComponent", "then", "m", "VendorDashboardComponent", "canActivate", "title", "VendorProductsComponent", "CreateProductComponent", "VendorPostsComponent", "CreatePostComponent", "VendorStoriesComponent", "CreateStoryComponent", "VendorOrdersComponent", "VendorAnalyticsComponent"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\vendor\\vendor.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard } from '../../core/guards/auth.guard';\n\nexport const vendorRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./pages/dashboard/vendor-dashboard.component').then(m => m.VendorDashboardComponent),\n    canActivate: [AuthGuard],\n    title: 'Vendor Dashboard - DFashion'\n  },\n  {\n    path: 'products',\n    loadComponent: () => import('./pages/products/vendor-products.component').then(m => m.VendorProductsComponent),\n    canActivate: [AuthGuard],\n    title: 'My Products - DFashion'\n  },\n  {\n    path: 'products/create',\n    loadComponent: () => import('./pages/products/create-product.component').then(m => m.CreateProductComponent),\n    canActivate: [AuthGuard],\n    title: 'Create Product - DFashion'\n  },\n  {\n    path: 'posts',\n    loadComponent: () => import('./pages/posts/vendor-posts.component').then(m => m.VendorPostsComponent),\n    canActivate: [AuthGuard],\n    title: 'My Posts - DFashion'\n  },\n  {\n    path: 'posts/create',\n    loadComponent: () => import('./pages/posts/create-post.component').then(m => m.CreatePostComponent),\n    canActivate: [AuthGuard],\n    title: 'Create Post - DFashion'\n  },\n  {\n    path: 'stories',\n    loadComponent: () => import('./pages/stories/vendor-stories.component').then(m => m.VendorStoriesComponent),\n    canActivate: [AuthGuard],\n    title: 'My Stories - DFashion'\n  },\n  {\n    path: 'stories/create',\n    loadComponent: () => import('./pages/stories/create-story.component').then(m => m.CreateStoryComponent),\n    canActivate: [AuthGuard],\n    title: 'Create Story - DFashion'\n  },\n  {\n    path: 'orders',\n    loadComponent: () => import('./pages/orders/vendor-orders.component').then(m => m.VendorOrdersComponent),\n    canActivate: [AuthGuard],\n    title: 'Orders - DFashion'\n  },\n  {\n    path: 'analytics',\n    loadComponent: () => import('./pages/analytics/vendor-analytics.component').then(m => m.VendorAnalyticsComponent),\n    canActivate: [AuthGuard],\n    title: 'Analytics - DFashion'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,8BAA8B;AAExD,OAAO,MAAMC,YAAY,GAAW,CAClC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,wBAAwB,CAAC;EACjHC,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,uBAAuB,CAAC;EAC9GF,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,sBAAsB,CAAC;EAC5GH,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,OAAO;EACbC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,oBAAoB,CAAC;EACrGJ,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,mBAAmB,CAAC;EACnGL,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,SAAS;EACfC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,sBAAsB,CAAC;EAC3GN,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,oBAAoB,CAAC;EACvGP,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,qBAAqB,CAAC;EACxGR,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,WAAW;EACjBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,wBAAwB,CAAC;EACjHT,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}