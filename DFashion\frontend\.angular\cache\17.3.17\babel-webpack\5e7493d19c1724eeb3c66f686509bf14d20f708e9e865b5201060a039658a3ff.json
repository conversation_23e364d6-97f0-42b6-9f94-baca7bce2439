{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst splitPaneIosCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}::slotted(ion-menu.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width);min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side),:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.split-pane-visible) ::slotted(.split-pane-main){-ms-flex:1;flex:1;overflow:hidden}:host(.split-pane-visible) ::slotted(.split-pane-side:not(ion-menu)),:host(.split-pane-visible) ::slotted(ion-menu.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host(.split-pane-visible) ::slotted(.split-pane-side){-ms-flex-order:-1;order:-1}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--side-min-width:270px;--side-max-width:28%}:host(.split-pane-visible) ::slotted(.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}\";\nconst IonSplitPaneIosStyle0 = splitPaneIosCss;\nconst splitPaneMdCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}::slotted(ion-menu.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width);min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side),:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.split-pane-visible) ::slotted(.split-pane-main){-ms-flex:1;flex:1;overflow:hidden}:host(.split-pane-visible) ::slotted(.split-pane-side:not(ion-menu)),:host(.split-pane-visible) ::slotted(ion-menu.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host(.split-pane-visible) ::slotted(.split-pane-side){-ms-flex-order:-1;order:-1}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--side-min-width:270px;--side-max-width:28%}:host(.split-pane-visible) ::slotted(.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}\";\nconst IonSplitPaneMdStyle0 = splitPaneMdCss;\n\n// TODO(FW-2832): types\nconst SPLIT_PANE_MAIN = 'split-pane-main';\nconst SPLIT_PANE_SIDE = 'split-pane-side';\nconst QUERY = {\n  xs: '(min-width: 0px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n  never: ''\n};\nconst SplitPane = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSplitPaneVisible = createEvent(this, \"ionSplitPaneVisible\", 7);\n    this.visible = false;\n    this.contentId = undefined;\n    this.disabled = false;\n    this.when = QUERY['lg'];\n  }\n  visibleChanged(visible) {\n    const detail = {\n      visible,\n      isPane: this.isPane.bind(this)\n    };\n    this.ionSplitPaneVisible.emit(detail);\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // TODO: connectedCallback is fired in CE build\n      // before WC is defined. This needs to be fixed in Stencil.\n      if (typeof customElements !== 'undefined' && customElements != null) {\n        yield customElements.whenDefined('ion-split-pane');\n      }\n      _this.styleChildren();\n      _this.updateState();\n    })();\n  }\n  disconnectedCallback() {\n    if (this.rmL) {\n      this.rmL();\n      this.rmL = undefined;\n    }\n  }\n  updateState() {\n    if (this.rmL) {\n      this.rmL();\n      this.rmL = undefined;\n    }\n    // Check if the split-pane is disabled\n    if (this.disabled) {\n      this.visible = false;\n      return;\n    }\n    // When query is a boolean\n    const query = this.when;\n    if (typeof query === 'boolean') {\n      this.visible = query;\n      return;\n    }\n    // When query is a string, let's find first if it is a shortcut\n    const mediaQuery = QUERY[query] || query;\n    // Media query is empty or null, we hide it\n    if (mediaQuery.length === 0) {\n      this.visible = false;\n      return;\n    }\n    // Listen on media query\n    const callback = q => {\n      this.visible = q.matches;\n    };\n    const mediaList = window.matchMedia(mediaQuery);\n    // TODO FW-5869\n    mediaList.addListener(callback);\n    this.rmL = () => mediaList.removeListener(callback);\n    this.visible = mediaList.matches;\n  }\n  isPane(element) {\n    if (!this.visible) {\n      return false;\n    }\n    return element.parentElement === this.el && element.classList.contains(SPLIT_PANE_SIDE);\n  }\n  styleChildren() {\n    const contentId = this.contentId;\n    const children = this.el.children;\n    const nu = this.el.childElementCount;\n    let foundMain = false;\n    for (let i = 0; i < nu; i++) {\n      const child = children[i];\n      const isMain = contentId !== undefined && child.id === contentId;\n      if (isMain) {\n        if (foundMain) {\n          console.warn('split pane cannot have more than one main node');\n          return;\n        }\n        foundMain = true;\n      }\n      setPaneClass(child, isMain);\n    }\n    if (!foundMain) {\n      console.warn('split pane does not have a specified main node');\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '57ee198506248916e74d8d082ad547a471e6cc73',\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`split-pane-${mode}`]: true,\n        'split-pane-visible': this.visible\n      }\n    }, h(\"slot\", {\n      key: '2cd89fa50cfe8a7a6bdda981bb89d5a24a8eec88'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"visible\": [\"visibleChanged\"],\n      \"disabled\": [\"updateState\"],\n      \"when\": [\"updateState\"]\n    };\n  }\n};\nconst setPaneClass = (el, isMain) => {\n  let toAdd;\n  let toRemove;\n  if (isMain) {\n    toAdd = SPLIT_PANE_MAIN;\n    toRemove = SPLIT_PANE_SIDE;\n  } else {\n    toAdd = SPLIT_PANE_SIDE;\n    toRemove = SPLIT_PANE_MAIN;\n  }\n  const classList = el.classList;\n  classList.add(toAdd);\n  classList.remove(toRemove);\n};\nSplitPane.style = {\n  ios: IonSplitPaneIosStyle0,\n  md: IonSplitPaneMdStyle0\n};\nexport { SplitPane as ion_split_pane };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}