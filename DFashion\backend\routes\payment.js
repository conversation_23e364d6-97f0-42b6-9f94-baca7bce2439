const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const Razorpay = require('razorpay');

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: 'rzp_test_MiheZxloav4xYh',
  key_secret: 'wrGf5uDOQ2Adrxn0qoaAI9oc'
});

// Create Order
router.post('/create-order', async (req, res) => {
  try {
    console.log('Creating order:', req.body);
    
    const options = {
      amount: req.body.amount, // amount in the smallest currency unit (paise)
      currency: req.body.currency || "INR",
      receipt: crypto.randomBytes(10).toString('hex'),
    };
    
    const order = await razorpay.orders.create(options);
    
    if (!order) {
      return res.status(500).json({
        success: false,
        message: "Some error occurred while creating order"
      });
    }
    
    res.status(200).json({
      success: true,
      data: order
    });
    
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
});

// Verify Payment
router.post('/verify', async (req, res) => {
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;
    
    console.log('Verifying payment:', {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature
    });
    
    // Create signature
    const hmac = crypto.createHmac('sha256', razorpay.key_secret);
    hmac.update(`${razorpay_order_id}|${razorpay_payment_id}`);
    const generatedSignature = hmac.digest('hex');
    
    if (generatedSignature === razorpay_signature) {
      // Payment is verified
      console.log('Payment verified successfully');
      
      // Here you can update your database with payment status
      // Save order details, update payment status, etc.
      
      res.status(200).json({
        success: true,
        message: "Payment verified successfully",
        data: {
          orderId: razorpay_order_id,
          paymentId: razorpay_payment_id,
          status: 'verified'
        }
      });
    } else {
      console.log('Payment verification failed');
      res.status(400).json({
        success: false,
        message: "Invalid signature - Payment verification failed"
      });
    }
    
  } catch (error) {
    console.error('Error verifying payment:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
});

// Get Payment Details
router.get('/details/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    const payment = await razorpay.payments.fetch(paymentId);
    
    res.status(200).json({
      success: true,
      data: payment
    });
    
  } catch (error) {
    console.error('Error fetching payment details:', error);
    res.status(500).json({
      success: false,
      message: "Error fetching payment details",
      error: error.message
    });
  }
});

// Refund Payment
router.post('/refund', async (req, res) => {
  try {
    const { paymentId, amount, reason } = req.body;
    
    const refund = await razorpay.payments.refund(paymentId, {
      amount: amount,
      speed: 'normal',
      notes: {
        reason: reason || 'Customer requested refund'
      }
    });
    
    res.status(200).json({
      success: true,
      message: "Refund initiated successfully",
      data: refund
    });
    
  } catch (error) {
    console.error('Error processing refund:', error);
    res.status(500).json({
      success: false,
      message: "Error processing refund",
      error: error.message
    });
  }
});

// Webhook for payment status updates
router.post('/webhook', (req, res) => {
  try {
    const webhookSignature = req.headers['x-razorpay-signature'];
    const webhookSecret = 'your_webhook_secret'; // Set this in your Razorpay dashboard
    
    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(JSON.stringify(req.body))
      .digest('hex');
    
    if (webhookSignature === expectedSignature) {
      const event = req.body.event;
      const paymentEntity = req.body.payload.payment.entity;
      
      console.log('Webhook received:', event, paymentEntity);
      
      // Handle different webhook events
      switch (event) {
        case 'payment.captured':
          console.log('Payment captured:', paymentEntity.id);
          // Update order status in database
          break;
        case 'payment.failed':
          console.log('Payment failed:', paymentEntity.id);
          // Handle failed payment
          break;
        default:
          console.log('Unhandled webhook event:', event);
      }
      
      res.status(200).json({ status: 'ok' });
    } else {
      res.status(400).json({ error: 'Invalid signature' });
    }
    
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

module.exports = router;
