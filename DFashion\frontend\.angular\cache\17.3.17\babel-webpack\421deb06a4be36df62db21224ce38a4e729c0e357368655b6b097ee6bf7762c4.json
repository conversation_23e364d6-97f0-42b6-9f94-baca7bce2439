{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask, r as registerInstance, d as createEvent, e as readTask, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { I as ION_CONTENT_CLASS_SELECTOR, b as ION_CONTENT_ELEMENT_SELECTOR, p as printIonContentErrorMsg, g as getScrollElement } from './index-f3946ac1.js';\nimport { t as transitionEndAsync, c as componentOnReady, l as clamp, g as getElementRoot, r as raf } from './helpers-be245865.js';\nimport { d as hapticImpact, I as ImpactStyle } from './haptic-554688a5.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport { h as caretBackSharp, i as arrowDown } from './index-f7dc70ba.js';\nimport { S as SPINNERS } from './spinner-configs-964f7cf3.js';\nimport './index-9b0d46f4.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst getRefresherAnimationType = contentEl => {\n  const previousSibling = contentEl.previousElementSibling;\n  const hasHeader = previousSibling !== null && previousSibling.tagName === 'ION-HEADER';\n  return hasHeader ? 'translate' : 'scale';\n};\nconst createPullingAnimation = (type, pullingSpinner, refresherEl) => {\n  return type === 'scale' ? createScaleAnimation(pullingSpinner, refresherEl) : createTranslateAnimation(pullingSpinner, refresherEl);\n};\nconst createBaseAnimation = pullingRefresherIcon => {\n  const spinner = pullingRefresherIcon.querySelector('ion-spinner');\n  const circle = spinner.shadowRoot.querySelector('circle');\n  const spinnerArrowContainer = pullingRefresherIcon.querySelector('.spinner-arrow-container');\n  const arrowContainer = pullingRefresherIcon.querySelector('.arrow-container');\n  const arrow = arrowContainer ? arrowContainer.querySelector('ion-icon') : null;\n  const baseAnimation = createAnimation().duration(1000).easing('ease-out');\n  const spinnerArrowContainerAnimation = createAnimation().addElement(spinnerArrowContainer).keyframes([{\n    offset: 0,\n    opacity: '0.3'\n  }, {\n    offset: 0.45,\n    opacity: '0.3'\n  }, {\n    offset: 0.55,\n    opacity: '1'\n  }, {\n    offset: 1,\n    opacity: '1'\n  }]);\n  const circleInnerAnimation = createAnimation().addElement(circle).keyframes([{\n    offset: 0,\n    strokeDasharray: '1px, 200px'\n  }, {\n    offset: 0.2,\n    strokeDasharray: '1px, 200px'\n  }, {\n    offset: 0.55,\n    strokeDasharray: '100px, 200px'\n  }, {\n    offset: 1,\n    strokeDasharray: '100px, 200px'\n  }]);\n  const circleOuterAnimation = createAnimation().addElement(spinner).keyframes([{\n    offset: 0,\n    transform: 'rotate(-90deg)'\n  }, {\n    offset: 1,\n    transform: 'rotate(210deg)'\n  }]);\n  /**\n   * Only add arrow animation if present\n   * this allows users to customize the spinners\n   * without errors being thrown\n   */\n  if (arrowContainer && arrow) {\n    const arrowContainerAnimation = createAnimation().addElement(arrowContainer).keyframes([{\n      offset: 0,\n      transform: 'rotate(0deg)'\n    }, {\n      offset: 0.3,\n      transform: 'rotate(0deg)'\n    }, {\n      offset: 0.55,\n      transform: 'rotate(280deg)'\n    }, {\n      offset: 1,\n      transform: 'rotate(400deg)'\n    }]);\n    const arrowAnimation = createAnimation().addElement(arrow).keyframes([{\n      offset: 0,\n      transform: 'translateX(2px) scale(0)'\n    }, {\n      offset: 0.3,\n      transform: 'translateX(2px) scale(0)'\n    }, {\n      offset: 0.55,\n      transform: 'translateX(-1.5px) scale(1)'\n    }, {\n      offset: 1,\n      transform: 'translateX(-1.5px) scale(1)'\n    }]);\n    baseAnimation.addAnimation([arrowContainerAnimation, arrowAnimation]);\n  }\n  return baseAnimation.addAnimation([spinnerArrowContainerAnimation, circleInnerAnimation, circleOuterAnimation]);\n};\nconst createScaleAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation().addElement(pullingRefresherIcon).keyframes([{\n    offset: 0,\n    transform: `scale(0) translateY(-${height}px)`\n  }, {\n    offset: 1,\n    transform: 'scale(1) translateY(100px)'\n  }]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createTranslateAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation().addElement(pullingRefresherIcon).keyframes([{\n    offset: 0,\n    transform: `translateY(-${height}px)`\n  }, {\n    offset: 1,\n    transform: 'translateY(100px)'\n  }]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createSnapBackAnimation = pullingRefresherIcon => {\n  return createAnimation().duration(125).addElement(pullingRefresherIcon).fromTo('transform', 'translateY(var(--ion-pulling-refresher-translate, 100px))', 'translateY(0px)');\n};\n// iOS Native Refresher\n// -----------------------------\nconst setSpinnerOpacity = (spinner, opacity) => {\n  spinner.style.setProperty('opacity', opacity.toString());\n};\nconst handleScrollWhilePulling = (ticks, numTicks, pullAmount) => {\n  const max = 1;\n  writeTask(() => {\n    ticks.forEach((el, i) => {\n      /**\n       * Compute the opacity of each tick\n       * mark as a percentage of the pullAmount\n       * offset by max / numTicks so\n       * the tick marks are shown staggered.\n       */\n      const min = i * (max / numTicks);\n      const range = max - min;\n      const start = pullAmount - min;\n      const progression = clamp(0, start / range, 1);\n      el.style.setProperty('opacity', progression.toString());\n    });\n  });\n};\nconst handleScrollWhileRefreshing = (spinner, lastVelocityY) => {\n  writeTask(() => {\n    // If user pulls down quickly, the spinner should spin faster\n    spinner.style.setProperty('--refreshing-rotation-duration', lastVelocityY >= 1.0 ? '0.5s' : '2s');\n    spinner.style.setProperty('opacity', '1');\n  });\n};\nconst translateElement = (el, value, duration = 200) => {\n  if (!el) {\n    return Promise.resolve();\n  }\n  const trans = transitionEndAsync(el, duration);\n  writeTask(() => {\n    el.style.setProperty('transition', `${duration}ms all ease-out`);\n    if (value === undefined) {\n      el.style.removeProperty('transform');\n    } else {\n      el.style.setProperty('transform', `translate3d(0px, ${value}, 0px)`);\n    }\n  });\n  return trans;\n};\n// Utils\n// -----------------------------\n/**\n * In order to use the native iOS refresher the device must support rubber band scrolling.\n * As part of this, we need to exclude Desktop Safari because it has a slightly different rubber band effect that is not compatible with the native refresher in Ionic.\n *\n * We also need to be careful not to include devices that spoof their user agent.\n * For example, when using iOS emulation in Chrome the user agent will be spoofed such that\n * navigator.maxTouchPointer > 0. To work around this,\n * we check to see if the apple-pay-logo is supported as a named image which is only\n * true on Apple devices.\n *\n * We previously checked referencEl.style.webkitOverflowScrolling to explicitly check\n * for rubber band support. However, this property was removed on iPadOS and it's possible\n * that this will be removed on iOS in the future too.\n *\n */\nconst supportsRubberBandScrolling = () => {\n  return navigator.maxTouchPoints > 0 && CSS.supports('background: -webkit-named-image(apple-pay-logo-black)');\n};\nconst shouldUseNativeRefresher = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (referenceEl, mode) {\n    const refresherContent = referenceEl.querySelector('ion-refresher-content');\n    if (!refresherContent) {\n      return Promise.resolve(false);\n    }\n    yield new Promise(resolve => componentOnReady(refresherContent, resolve));\n    const pullingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n    const refreshingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n    return pullingSpinner !== null && refreshingSpinner !== null && (mode === 'ios' && supportsRubberBandScrolling() || mode === 'md');\n  });\n  return function shouldUseNativeRefresher(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst refresherIosCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-ios .refresher-pulling-icon,.refresher-ios .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-ios .refresher-pulling-text,.refresher-ios .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-lines-ios line,.refresher-ios .refresher-refreshing .spinner-lines-small-ios line,.refresher-ios .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-bubbles circle,.refresher-ios .refresher-refreshing .spinner-circles circle,.refresher-ios .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}.refresher-native .refresher-refreshing ion-spinner{--refreshing-rotation-duration:2s;display:none;-webkit-animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards;animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards}.refresher-native .refresher-refreshing{display:none;-webkit-animation:250ms linear refresher-pop forwards;animation:250ms linear refresher-pop forwards}.refresher-native ion-spinner{width:32px;height:32px;color:var(--ion-color-step-450, #747577)}.refresher-native.refresher-refreshing .refresher-pulling ion-spinner,.refresher-native.refresher-completing .refresher-pulling ion-spinner{display:none}.refresher-native.refresher-refreshing .refresher-refreshing ion-spinner,.refresher-native.refresher-completing .refresher-refreshing ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-pulling ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-refreshing ion-spinner{display:none}.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0) rotate(180deg);transform:scale(0) rotate(180deg);-webkit-transition:300ms;transition:300ms}@-webkit-keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}\";\nconst IonRefresherIosStyle0 = refresherIosCss;\nconst refresherMdCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-md .refresher-pulling-icon,.refresher-md .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-md .refresher-pulling-text,.refresher-md .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-lines-md line,.refresher-md .refresher-refreshing .spinner-lines-small-md line,.refresher-md .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-bubbles circle,.refresher-md .refresher-refreshing .spinner-circles circle,.refresher-md .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:24px;height:24px;color:var(--ion-color-primary, #3880ff)}ion-refresher.refresher-native .spinner-arrow-container{display:inherit}ion-refresher.refresher-native .arrow-container{display:block;position:absolute;width:24px;height:24px}ion-refresher.refresher-native .arrow-container ion-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;bottom:-4px;position:absolute;color:var(--ion-color-primary, #3880ff);font-size:12px}ion-refresher.refresher-native.refresher-pulling ion-refresher-content .refresher-pulling,ion-refresher.refresher-native.refresher-ready ion-refresher-content .refresher-pulling{display:-ms-flexbox;display:flex}ion-refresher.refresher-native.refresher-refreshing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-cancelling ion-refresher-content .refresher-refreshing{display:-ms-flexbox;display:flex}ion-refresher.refresher-native .refresher-pulling-icon{-webkit-transform:translateY(calc(-100% - 10px));transform:translateY(calc(-100% - 10px))}ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;border-radius:100%;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;display:-ms-flexbox;display:flex;border:1px solid var(--ion-color-step-200, #ececec);background:var(--ion-color-step-250, #ffffff);-webkit-box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1);box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1)}\";\nconst IonRefresherMdStyle0 = refresherMdCss;\nconst Refresher = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRefresh = createEvent(this, \"ionRefresh\", 7);\n    this.ionPull = createEvent(this, \"ionPull\", 7);\n    this.ionStart = createEvent(this, \"ionStart\", 7);\n    this.appliedStyles = false;\n    this.didStart = false;\n    this.progress = 0;\n    this.pointerDown = false;\n    this.needsCompletion = false;\n    this.didRefresh = false;\n    this.lastVelocityY = 0;\n    this.animations = [];\n    this.nativeRefresher = false;\n    this.state = 1 /* RefresherState.Inactive */;\n    this.pullMin = 60;\n    this.pullMax = this.pullMin + 60;\n    this.closeDuration = '280ms';\n    this.snapbackDuration = '280ms';\n    this.pullFactor = 1;\n    this.disabled = false;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  checkNativeRefresher() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const useNativeRefresher = yield shouldUseNativeRefresher(_this.el, getIonMode(_this));\n      if (useNativeRefresher && !_this.nativeRefresher) {\n        const contentEl = _this.el.closest('ion-content');\n        _this.setupNativeRefresher(contentEl);\n      } else if (!useNativeRefresher) {\n        _this.destroyNativeRefresher();\n      }\n    })();\n  }\n  destroyNativeRefresher() {\n    if (this.scrollEl && this.scrollListenerCallback) {\n      this.scrollEl.removeEventListener('scroll', this.scrollListenerCallback);\n      this.scrollListenerCallback = undefined;\n    }\n    this.nativeRefresher = false;\n  }\n  resetNativeRefresher(el, state) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.state = state;\n      if (getIonMode(_this2) === 'ios') {\n        yield translateElement(el, undefined, 300);\n      } else {\n        yield transitionEndAsync(_this2.el.querySelector('.refresher-refreshing-icon'), 200);\n      }\n      _this2.didRefresh = false;\n      _this2.needsCompletion = false;\n      _this2.pointerDown = false;\n      _this2.animations.forEach(ani => ani.destroy());\n      _this2.animations = [];\n      _this2.progress = 0;\n      _this2.state = 1 /* RefresherState.Inactive */;\n    })();\n  }\n  setupiOSNativeRefresher(pullingSpinner, refreshingSpinner) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.elementToTransform = _this3.scrollEl;\n      const ticks = pullingSpinner.shadowRoot.querySelectorAll('svg');\n      let MAX_PULL = _this3.scrollEl.clientHeight * 0.16;\n      const NUM_TICKS = ticks.length;\n      writeTask(() => ticks.forEach(el => el.style.setProperty('animation', 'none')));\n      _this3.scrollListenerCallback = () => {\n        // If pointer is not on screen or refresher is not active, ignore scroll\n        if (!_this3.pointerDown && _this3.state === 1 /* RefresherState.Inactive */) {\n          return;\n        }\n        readTask(() => {\n          // PTR should only be active when overflow scrolling at the top\n          const scrollTop = _this3.scrollEl.scrollTop;\n          const refresherHeight = _this3.el.clientHeight;\n          if (scrollTop > 0) {\n            /**\n             * If refresher is refreshing and user tries to scroll\n             * progressively fade refresher out/in\n             */\n            if (_this3.state === 8 /* RefresherState.Refreshing */) {\n              const ratio = clamp(0, scrollTop / (refresherHeight * 0.5), 1);\n              writeTask(() => setSpinnerOpacity(refreshingSpinner, 1 - ratio));\n              return;\n            }\n            return;\n          }\n          if (_this3.pointerDown) {\n            if (!_this3.didStart) {\n              _this3.didStart = true;\n              _this3.ionStart.emit();\n            }\n            // emit \"pulling\" on every move\n            if (_this3.pointerDown) {\n              _this3.ionPull.emit();\n            }\n          }\n          /**\n           * We want to delay the start of this gesture by ~30px\n           * when initially pulling down so the refresher does not\n           * overlap with the content. But when letting go of the\n           * gesture before the refresher completes, we want the\n           * refresher tick marks to quickly fade out.\n           */\n          const offset = _this3.didStart ? 30 : 0;\n          const pullAmount = _this3.progress = clamp(0, (Math.abs(scrollTop) - offset) / MAX_PULL, 1);\n          const shouldShowRefreshingSpinner = _this3.state === 8 /* RefresherState.Refreshing */ || pullAmount === 1;\n          if (shouldShowRefreshingSpinner) {\n            if (_this3.pointerDown) {\n              handleScrollWhileRefreshing(refreshingSpinner, _this3.lastVelocityY);\n            }\n            if (!_this3.didRefresh) {\n              _this3.beginRefresh();\n              _this3.didRefresh = true;\n              hapticImpact({\n                style: ImpactStyle.Light\n              });\n              /**\n               * Translate the content element otherwise when pointer is removed\n               * from screen the scroll content will bounce back over the refresher\n               */\n              if (!_this3.pointerDown) {\n                translateElement(_this3.elementToTransform, `${refresherHeight}px`);\n              }\n            }\n          } else {\n            _this3.state = 2 /* RefresherState.Pulling */;\n            handleScrollWhilePulling(ticks, NUM_TICKS, pullAmount);\n          }\n        });\n      };\n      _this3.scrollEl.addEventListener('scroll', _this3.scrollListenerCallback);\n      _this3.gesture = (yield import('./index-2cf77112.js')).createGesture({\n        el: _this3.scrollEl,\n        gestureName: 'refresher',\n        gesturePriority: 31,\n        direction: 'y',\n        threshold: 5,\n        onStart: () => {\n          _this3.pointerDown = true;\n          if (!_this3.didRefresh) {\n            translateElement(_this3.elementToTransform, '0px');\n          }\n          /**\n           * If the content had `display: none` when\n           * the refresher was initialized, its clientHeight\n           * will be 0. When the gesture starts, the content\n           * will be visible, so try to get the correct\n           * client height again. This is most common when\n           * using the refresher in an ion-menu.\n           */\n          if (MAX_PULL === 0) {\n            MAX_PULL = _this3.scrollEl.clientHeight * 0.16;\n          }\n        },\n        onMove: ev => {\n          _this3.lastVelocityY = ev.velocityY;\n        },\n        onEnd: () => {\n          _this3.pointerDown = false;\n          _this3.didStart = false;\n          if (_this3.needsCompletion) {\n            _this3.resetNativeRefresher(_this3.elementToTransform, 32 /* RefresherState.Completing */);\n            _this3.needsCompletion = false;\n          } else if (_this3.didRefresh) {\n            readTask(() => translateElement(_this3.elementToTransform, `${_this3.el.clientHeight}px`));\n          }\n        }\n      });\n      _this3.disabledChanged();\n    })();\n  }\n  setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const circle = getElementRoot(pullingSpinner).querySelector('circle');\n      const pullingRefresherIcon = _this4.el.querySelector('ion-refresher-content .refresher-pulling-icon');\n      const refreshingCircle = getElementRoot(refreshingSpinner).querySelector('circle');\n      if (circle !== null && refreshingCircle !== null) {\n        writeTask(() => {\n          circle.style.setProperty('animation', 'none');\n          // This lines up the animation on the refreshing spinner with the pulling spinner\n          refreshingSpinner.style.setProperty('animation-delay', '-655ms');\n          refreshingCircle.style.setProperty('animation-delay', '-655ms');\n        });\n      }\n      _this4.gesture = (yield import('./index-2cf77112.js')).createGesture({\n        el: _this4.scrollEl,\n        gestureName: 'refresher',\n        gesturePriority: 31,\n        direction: 'y',\n        threshold: 5,\n        canStart: () => _this4.state !== 8 /* RefresherState.Refreshing */ && _this4.state !== 32 /* RefresherState.Completing */ && _this4.scrollEl.scrollTop === 0,\n        onStart: ev => {\n          _this4.progress = 0;\n          ev.data = {\n            animation: undefined,\n            didStart: false,\n            cancelled: false\n          };\n        },\n        onMove: ev => {\n          if (ev.velocityY < 0 && _this4.progress === 0 && !ev.data.didStart || ev.data.cancelled) {\n            ev.data.cancelled = true;\n            return;\n          }\n          if (!ev.data.didStart) {\n            ev.data.didStart = true;\n            _this4.state = 2 /* RefresherState.Pulling */;\n            // When ion-refresher is being used with a custom scroll target, the overflow styles need to be applied directly instead of via a css variable\n            const {\n              scrollEl\n            } = _this4;\n            const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n            writeTask(() => scrollEl.style.setProperty(overflowProperty, 'hidden'));\n            const animationType = getRefresherAnimationType(contentEl);\n            const animation = createPullingAnimation(animationType, pullingRefresherIcon, _this4.el);\n            ev.data.animation = animation;\n            animation.progressStart(false, 0);\n            _this4.ionStart.emit();\n            _this4.animations.push(animation);\n            return;\n          }\n          // Since we are using an easing curve, slow the gesture tracking down a bit\n          _this4.progress = clamp(0, ev.deltaY / 180 * 0.5, 1);\n          ev.data.animation.progressStep(_this4.progress);\n          _this4.ionPull.emit();\n        },\n        onEnd: ev => {\n          if (!ev.data.didStart) {\n            return;\n          }\n          _this4.gesture.enable(false);\n          const {\n            scrollEl\n          } = _this4;\n          const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n          writeTask(() => scrollEl.style.removeProperty(overflowProperty));\n          if (_this4.progress <= 0.4) {\n            ev.data.animation.progressEnd(0, _this4.progress, 500).onFinish(() => {\n              _this4.animations.forEach(ani => ani.destroy());\n              _this4.animations = [];\n              _this4.gesture.enable(true);\n              _this4.state = 1 /* RefresherState.Inactive */;\n            });\n            return;\n          }\n          const progress = getTimeGivenProgression([0, 0], [0, 0], [1, 1], [1, 1], _this4.progress)[0];\n          const snapBackAnimation = createSnapBackAnimation(pullingRefresherIcon);\n          _this4.animations.push(snapBackAnimation);\n          writeTask(/*#__PURE__*/_asyncToGenerator(function* () {\n            pullingRefresherIcon.style.setProperty('--ion-pulling-refresher-translate', `${progress * 100}px`);\n            ev.data.animation.progressEnd();\n            yield snapBackAnimation.play();\n            _this4.beginRefresh();\n            ev.data.animation.destroy();\n            _this4.gesture.enable(true);\n          }));\n        }\n      });\n      _this4.disabledChanged();\n    })();\n  }\n  setupNativeRefresher(contentEl) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (_this5.scrollListenerCallback || !contentEl || _this5.nativeRefresher || !_this5.scrollEl) {\n        return;\n      }\n      /**\n       * If using non-native refresher before make sure\n       * we clean up any old CSS. This can happen when\n       * a user manually calls the refresh method in a\n       * component create callback before the native\n       * refresher is setup.\n       */\n      _this5.setCss(0, '', false, '');\n      _this5.nativeRefresher = true;\n      const pullingSpinner = _this5.el.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n      const refreshingSpinner = _this5.el.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n      if (getIonMode(_this5) === 'ios') {\n        _this5.setupiOSNativeRefresher(pullingSpinner, refreshingSpinner);\n      } else {\n        _this5.setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner);\n      }\n    })();\n  }\n  componentDidUpdate() {\n    this.checkNativeRefresher();\n  }\n  connectedCallback() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.el.getAttribute('slot') !== 'fixed') {\n        console.error('Make sure you use: <ion-refresher slot=\"fixed\">');\n        return;\n      }\n      const contentEl = _this6.el.closest(ION_CONTENT_ELEMENT_SELECTOR);\n      if (!contentEl) {\n        printIonContentErrorMsg(_this6.el);\n        return;\n      }\n      /**\n       * Waits for the content to be ready before querying the scroll\n       * or the background content element.\n       */\n      componentOnReady(contentEl, /*#__PURE__*/_asyncToGenerator(function* () {\n        const customScrollTarget = contentEl.querySelector(ION_CONTENT_CLASS_SELECTOR);\n        /**\n         * Query the custom scroll target (if available), first. In refresher implementations,\n         * the ion-refresher element will always be a direct child of ion-content (slot=\"fixed\"). By\n         * querying the custom scroll target first and falling back to the ion-content element,\n         * the correct scroll element will be returned by the implementation.\n         */\n        _this6.scrollEl = yield getScrollElement(customScrollTarget !== null && customScrollTarget !== void 0 ? customScrollTarget : contentEl);\n        /**\n         * Query the background content element from the host ion-content element directly.\n         */\n        _this6.backgroundContentEl = yield contentEl.getBackgroundElement();\n        if (yield shouldUseNativeRefresher(_this6.el, getIonMode(_this6))) {\n          _this6.setupNativeRefresher(contentEl);\n        } else {\n          _this6.gesture = (yield import('./index-2cf77112.js')).createGesture({\n            el: contentEl,\n            gestureName: 'refresher',\n            gesturePriority: 31,\n            direction: 'y',\n            threshold: 20,\n            passive: false,\n            canStart: () => _this6.canStart(),\n            onStart: () => _this6.onStart(),\n            onMove: ev => _this6.onMove(ev),\n            onEnd: () => _this6.onEnd()\n          });\n          _this6.disabledChanged();\n        }\n      }));\n    })();\n  }\n  disconnectedCallback() {\n    this.destroyNativeRefresher();\n    this.scrollEl = undefined;\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  /**\n   * Call `complete()` when your async operation has completed.\n   * For example, the `refreshing` state is while the app is performing\n   * an asynchronous operation, such as receiving more data from an\n   * AJAX request. Once the data has been received, you then call this\n   * method to signify that the refreshing has completed and to close\n   * the refresher. This method also changes the refresher's state from\n   * `refreshing` to `completing`.\n   */\n  complete() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7.nativeRefresher) {\n        _this7.needsCompletion = true;\n        // Do not reset scroll el until user removes pointer from screen\n        if (!_this7.pointerDown) {\n          raf(() => raf(() => _this7.resetNativeRefresher(_this7.elementToTransform, 32 /* RefresherState.Completing */)));\n        }\n      } else {\n        _this7.close(32 /* RefresherState.Completing */, '120ms');\n      }\n    })();\n  }\n  /**\n   * Changes the refresher's state from `refreshing` to `cancelling`.\n   */\n  cancel() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (_this8.nativeRefresher) {\n        // Do not reset scroll el until user removes pointer from screen\n        if (!_this8.pointerDown) {\n          raf(() => raf(() => _this8.resetNativeRefresher(_this8.elementToTransform, 16 /* RefresherState.Cancelling */)));\n        }\n      } else {\n        _this8.close(16 /* RefresherState.Cancelling */, '');\n      }\n    })();\n  }\n  /**\n   * A number representing how far down the user has pulled.\n   * The number `0` represents the user hasn't pulled down at all. The\n   * number `1`, and anything greater than `1`, represents that the user\n   * has pulled far enough down that when they let go then the refresh will\n   * happen. If they let go and the number is less than `1`, then the\n   * refresh will not happen, and the content will return to it's original\n   * position.\n   */\n  getProgress() {\n    return Promise.resolve(this.progress);\n  }\n  canStart() {\n    if (!this.scrollEl) {\n      return false;\n    }\n    if (this.state !== 1 /* RefresherState.Inactive */) {\n      return false;\n    }\n    // if the scrollTop is greater than zero then it's\n    // not possible to pull the content down yet\n    if (this.scrollEl.scrollTop > 0) {\n      return false;\n    }\n    return true;\n  }\n  onStart() {\n    this.progress = 0;\n    this.state = 1 /* RefresherState.Inactive */;\n    this.memoizeOverflowStyle();\n  }\n  onMove(detail) {\n    if (!this.scrollEl) {\n      return;\n    }\n    // this method can get called like a bazillion times per second,\n    // so it's built to be as efficient as possible, and does its\n    // best to do any DOM read/writes only when absolutely necessary\n    // if multi-touch then get out immediately\n    const ev = detail.event;\n    if (ev.touches !== undefined && ev.touches.length > 1) {\n      return;\n    }\n    // do nothing if it's actively refreshing\n    // or it's in the way of closing\n    // or this was never a startY\n    if ((this.state & 56 /* RefresherState._BUSY_ */) !== 0) {\n      return;\n    }\n    const pullFactor = Number.isNaN(this.pullFactor) || this.pullFactor < 0 ? 1 : this.pullFactor;\n    const deltaY = detail.deltaY * pullFactor;\n    // don't bother if they're scrolling up\n    // and have not already started dragging\n    if (deltaY <= 0) {\n      // the current Y is higher than the starting Y\n      // so they scrolled up enough to be ignored\n      this.progress = 0;\n      this.state = 1 /* RefresherState.Inactive */;\n      if (this.appliedStyles) {\n        // reset the styles only if they were applied\n        this.setCss(0, '', false, '');\n        return;\n      }\n      return;\n    }\n    if (this.state === 1 /* RefresherState.Inactive */) {\n      // this refresh is not already actively pulling down\n      // get the content's scrollTop\n      const scrollHostScrollTop = this.scrollEl.scrollTop;\n      // if the scrollTop is greater than zero then it's\n      // not possible to pull the content down yet\n      if (scrollHostScrollTop > 0) {\n        this.progress = 0;\n        return;\n      }\n      // content scrolled all the way to the top, and dragging down\n      this.state = 2 /* RefresherState.Pulling */;\n    }\n    // prevent native scroll events\n    if (ev.cancelable) {\n      ev.preventDefault();\n    }\n    // the refresher is actively pulling at this point\n    // move the scroll element within the content element\n    this.setCss(deltaY, '0ms', true, '');\n    if (deltaY === 0) {\n      // don't continue if there's no delta yet\n      this.progress = 0;\n      return;\n    }\n    const pullMin = this.pullMin;\n    // set pull progress\n    this.progress = deltaY / pullMin;\n    // emit \"start\" if it hasn't started yet\n    if (!this.didStart) {\n      this.didStart = true;\n      this.ionStart.emit();\n    }\n    // emit \"pulling\" on every move\n    this.ionPull.emit();\n    // do nothing if the delta is less than the pull threshold\n    if (deltaY < pullMin) {\n      // ensure it stays in the pulling state, cuz its not ready yet\n      this.state = 2 /* RefresherState.Pulling */;\n      return;\n    }\n    if (deltaY > this.pullMax) {\n      // they pulled farther than the max, so kick off the refresh\n      this.beginRefresh();\n      return;\n    }\n    // pulled farther than the pull min!!\n    // it is now in the `ready` state!!\n    // if they let go then it'll refresh, kerpow!!\n    this.state = 4 /* RefresherState.Ready */;\n    return;\n  }\n  onEnd() {\n    // only run in a zone when absolutely necessary\n    if (this.state === 4 /* RefresherState.Ready */) {\n      // they pulled down far enough, so it's ready to refresh\n      this.beginRefresh();\n    } else if (this.state === 2 /* RefresherState.Pulling */) {\n      // they were pulling down, but didn't pull down far enough\n      // set the content back to it's original location\n      // and close the refresher\n      // set that the refresh is actively cancelling\n      this.cancel();\n    } else if (this.state === 1 /* RefresherState.Inactive */) {\n      /**\n       * The pull to refresh gesture was aborted\n       * so we should immediately restore any overflow styles\n       * that have been modified. Do not call this.cancel\n       * because the styles will only be reset after a timeout.\n       * If the gesture is aborted then scrolling should be\n       * available right away.\n       */\n      this.restoreOverflowStyle();\n    }\n  }\n  beginRefresh() {\n    // assumes we're already back in a zone\n    // they pulled down far enough, so it's ready to refresh\n    this.state = 8 /* RefresherState.Refreshing */;\n    // place the content in a hangout position while it thinks\n    this.setCss(this.pullMin, this.snapbackDuration, true, '');\n    // emit \"refresh\" because it was pulled down far enough\n    // and they let go to begin refreshing\n    this.ionRefresh.emit({\n      complete: this.complete.bind(this)\n    });\n  }\n  close(state, delay) {\n    // create fallback timer incase something goes wrong with transitionEnd event\n    setTimeout(() => {\n      this.state = 1 /* RefresherState.Inactive */;\n      this.progress = 0;\n      this.didStart = false;\n      /**\n       * Reset any overflow styles so the\n       * user can scroll again.\n       */\n      this.setCss(0, '0ms', false, '', true);\n    }, 600);\n    // reset the styles on the scroll element\n    // set that the refresh is actively cancelling/completing\n    this.state = state;\n    this.setCss(0, this.closeDuration, true, delay);\n  }\n  setCss(y, duration, overflowVisible, delay, shouldRestoreOverflowStyle = false) {\n    if (this.nativeRefresher) {\n      return;\n    }\n    this.appliedStyles = y > 0;\n    writeTask(() => {\n      if (this.scrollEl && this.backgroundContentEl) {\n        const scrollStyle = this.scrollEl.style;\n        const backgroundStyle = this.backgroundContentEl.style;\n        scrollStyle.transform = backgroundStyle.transform = y > 0 ? `translateY(${y}px) translateZ(0px)` : '';\n        scrollStyle.transitionDuration = backgroundStyle.transitionDuration = duration;\n        scrollStyle.transitionDelay = backgroundStyle.transitionDelay = delay;\n        scrollStyle.overflow = overflowVisible ? 'hidden' : '';\n      }\n      /**\n       * Reset the overflow styles only once\n       * the pull to refresh effect has been closed.\n       * This ensures that the gesture is done\n       * and the refresh operation has either\n       * been aborted or has completed.\n       */\n      if (shouldRestoreOverflowStyle) {\n        this.restoreOverflowStyle();\n      }\n    });\n  }\n  memoizeOverflowStyle() {\n    if (this.scrollEl) {\n      const {\n        overflow,\n        overflowX,\n        overflowY\n      } = this.scrollEl.style;\n      this.overflowStyles = {\n        overflow: overflow !== null && overflow !== void 0 ? overflow : '',\n        overflowX: overflowX !== null && overflowX !== void 0 ? overflowX : '',\n        overflowY: overflowY !== null && overflowY !== void 0 ? overflowY : ''\n      };\n    }\n  }\n  restoreOverflowStyle() {\n    if (this.overflowStyles !== undefined && this.scrollEl !== undefined) {\n      const {\n        overflow,\n        overflowX,\n        overflowY\n      } = this.overflowStyles;\n      this.scrollEl.style.overflow = overflow;\n      this.scrollEl.style.overflowX = overflowX;\n      this.scrollEl.style.overflowY = overflowY;\n      this.overflowStyles = undefined;\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '96f4f595ebdb92a12755b642398691bcaab9f7c1',\n      slot: \"fixed\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`refresher-${mode}`]: true,\n        'refresher-native': this.nativeRefresher,\n        'refresher-active': this.state !== 1 /* RefresherState.Inactive */,\n        'refresher-pulling': this.state === 2 /* RefresherState.Pulling */,\n        'refresher-ready': this.state === 4 /* RefresherState.Ready */,\n        'refresher-refreshing': this.state === 8 /* RefresherState.Refreshing */,\n        'refresher-cancelling': this.state === 16 /* RefresherState.Cancelling */,\n        'refresher-completing': this.state === 32 /* RefresherState.Completing */\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nRefresher.style = {\n  ios: IonRefresherIosStyle0,\n  md: IonRefresherMdStyle0\n};\nconst RefresherContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.pullingIcon = undefined;\n    this.pullingText = undefined;\n    this.refreshingSpinner = undefined;\n    this.refreshingText = undefined;\n  }\n  componentWillLoad() {\n    if (this.pullingIcon === undefined) {\n      /**\n       * The native iOS refresher uses a spinner instead of\n       * an icon, so we need to see if this device supports\n       * the native iOS refresher.\n       */\n      const hasRubberBandScrolling = supportsRubberBandScrolling();\n      const mode = getIonMode(this);\n      const overflowRefresher = hasRubberBandScrolling ? 'lines' : arrowDown;\n      this.pullingIcon = config.get('refreshingIcon', mode === 'ios' && hasRubberBandScrolling ? config.get('spinner', overflowRefresher) : 'circular');\n    }\n    if (this.refreshingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.refreshingSpinner = config.get('refreshingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'circular'));\n    }\n  }\n  renderPullingText() {\n    const {\n      customHTMLEnabled,\n      pullingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"refresher-pulling-text\",\n        innerHTML: sanitizeDOMString(pullingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"refresher-pulling-text\"\n    }, pullingText);\n  }\n  renderRefreshingText() {\n    const {\n      customHTMLEnabled,\n      refreshingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"refresher-refreshing-text\",\n        innerHTML: sanitizeDOMString(refreshingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"refresher-refreshing-text\"\n    }, refreshingText);\n  }\n  render() {\n    const pullingIcon = this.pullingIcon;\n    const hasSpinner = pullingIcon != null && SPINNERS[pullingIcon] !== undefined;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'cf3caa51c4aba8a95622f6d32cafa90b683b9d6e',\n      class: mode\n    }, h(\"div\", {\n      key: '5ad70801104bbea873d3525206660c52e4447903',\n      class: \"refresher-pulling\"\n    }, this.pullingIcon && hasSpinner && h(\"div\", {\n      key: '0f95df169fd367528bfaa5d9ccf6690a613609c4',\n      class: \"refresher-pulling-icon\"\n    }, h(\"div\", {\n      key: '4b8f0465a19f017751b207807c32e1fe00fda433',\n      class: \"spinner-arrow-container\"\n    }, h(\"ion-spinner\", {\n      key: '77e60179d76f0d17f8f2dc3518f97a2a924418e6',\n      name: this.pullingIcon,\n      paused: true\n    }), mode === 'md' && this.pullingIcon === 'circular' && h(\"div\", {\n      key: 'f78f63f08f071bead1bfe655bae6394f8a219d91',\n      class: \"arrow-container\"\n    }, h(\"ion-icon\", {\n      key: '4d833d134d2b221cae2dfb0611d4029f2d664db5',\n      icon: caretBackSharp,\n      \"aria-hidden\": \"true\"\n    })))), this.pullingIcon && !hasSpinner && h(\"div\", {\n      key: 'e6db19d7fa324363d2a7c3c046510f4c8461f7e6',\n      class: \"refresher-pulling-icon\"\n    }, h(\"ion-icon\", {\n      key: '66c2ef1a53c5809f49891de515da5f55d9bf8dcc',\n      icon: this.pullingIcon,\n      lazy: false,\n      \"aria-hidden\": \"true\"\n    })), this.pullingText !== undefined && this.renderPullingText()), h(\"div\", {\n      key: '80c413e21d362a5bb0419fcd13092453b3445cee',\n      class: \"refresher-refreshing\"\n    }, this.refreshingSpinner && h(\"div\", {\n      key: '0d5511f9644de26332a1a9ed39b160691fab74d9',\n      class: \"refresher-refreshing-icon\"\n    }, h(\"ion-spinner\", {\n      key: '54e4a96b081c7b453a98e00cceea7c086268a450',\n      name: this.refreshingSpinner\n    })), this.refreshingText !== undefined && this.renderRefreshingText()));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nexport { Refresher as ion_refresher, RefresherContent as ion_refresher_content };", "map": {"version": 3, "names": ["w", "writeTask", "r", "registerInstance", "d", "createEvent", "e", "readTask", "h", "f", "getElement", "H", "Host", "g", "getTimeGivenProgression", "I", "ION_CONTENT_CLASS_SELECTOR", "b", "ION_CONTENT_ELEMENT_SELECTOR", "p", "printIonContentErrorMsg", "getScrollElement", "t", "transitionEndAsync", "c", "componentOnReady", "l", "clamp", "getElementRoot", "raf", "hapticImpact", "ImpactStyle", "getIonMode", "config", "createAnimation", "E", "ENABLE_HTML_CONTENT_DEFAULT", "a", "sanitizeDOMString", "caretBackSharp", "i", "arrowDown", "S", "SPINNERS", "getRefresherAnimationType", "contentEl", "previousSibling", "previousElementSibling", "<PERSON><PERSON><PERSON><PERSON>", "tagName", "createPullingAnimation", "type", "pullingSpinner", "refresherEl", "createScaleAnimation", "createTranslateAnimation", "createBaseAnimation", "pullingRefresherIcon", "spinner", "querySelector", "circle", "shadowRoot", "spinnerArrowContainer", "arrowContainer", "arrow", "baseAnimation", "duration", "easing", "spinnerArrowContainerAnimation", "addElement", "keyframes", "offset", "opacity", "circleInnerAnimation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "circleOuterAnimation", "transform", "arrowContainerAnimation", "arrowAnimation", "addAnimation", "height", "clientHeight", "spinnerAnimation", "createSnapBackAnimation", "fromTo", "setSpinnerOpacity", "style", "setProperty", "toString", "handleScrollWhilePulling", "ticks", "numTicks", "pullAmount", "max", "for<PERSON>ach", "el", "min", "range", "start", "progression", "handleScrollWhileRefreshing", "lastVelocityY", "translateElement", "value", "Promise", "resolve", "trans", "undefined", "removeProperty", "supportsRubberBandScrolling", "navigator", "maxTouchPoints", "CSS", "supports", "shouldUseNative<PERSON><PERSON><PERSON>er", "_ref", "_asyncToGenerator", "referenceEl", "mode", "refresher<PERSON>ontent", "refreshingSpinner", "_x", "_x2", "apply", "arguments", "refresherIosCss", "IonRefresherIosStyle0", "refresherMdCss", "IonRefresherMdStyle0", "Refresher", "constructor", "hostRef", "ionRefresh", "ionPull", "ionStart", "appliedStyles", "didStart", "progress", "pointerDown", "needsCompletion", "did<PERSON><PERSON>resh", "animations", "nativeRefresher", "state", "pullMin", "pullMax", "closeDuration", "snapbackDuration", "pullFactor", "disabled", "disabled<PERSON><PERSON>ed", "gesture", "enable", "checkNativeRefresher", "_this", "useNativeRefresher", "closest", "setupNativeRefresher", "destroyNativeRefresher", "scrollEl", "scrollListenerCallback", "removeEventListener", "resetNativeRef<PERSON>er", "_this2", "ani", "destroy", "setupiOSNativeRefresher", "_this3", "elementToTransform", "querySelectorAll", "MAX_PULL", "NUM_TICKS", "length", "scrollTop", "refresherHeight", "ratio", "emit", "Math", "abs", "shouldShowRefreshingSpinner", "beginRefresh", "Light", "addEventListener", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "direction", "threshold", "onStart", "onMove", "ev", "velocityY", "onEnd", "setupMDNativeRefresher", "_this4", "refreshingCircle", "canStart", "data", "animation", "cancelled", "overflowProperty", "matches", "animationType", "progressStart", "push", "deltaY", "progressStep", "progressEnd", "onFinish", "snapBackAnimation", "play", "_this5", "setCss", "componentDidUpdate", "connectedCallback", "_this6", "getAttribute", "console", "error", "customScrollTarget", "backgroundContentEl", "getBackgroundElement", "passive", "disconnectedCallback", "complete", "_this7", "close", "cancel", "_this8", "getProgress", "memoizeOverflowStyle", "detail", "event", "touches", "Number", "isNaN", "scrollHostScrollTop", "cancelable", "preventDefault", "restoreOverflowStyle", "bind", "delay", "setTimeout", "y", "overflowVisible", "shouldRestoreOverflowStyle", "scrollStyle", "backgroundStyle", "transitionDuration", "transitionDelay", "overflow", "overflowX", "overflowY", "overflowStyles", "render", "key", "slot", "class", "watchers", "ios", "md", "Ref<PERSON><PERSON><PERSON><PERSON><PERSON>", "customHTMLEnabled", "get", "pullingIcon", "pullingText", "refreshingText", "componentWillLoad", "hasRubberBandScrolling", "overflowRefresher", "renderPullingText", "innerHTML", "renderRefreshingText", "<PERSON><PERSON><PERSON><PERSON>", "name", "paused", "icon", "lazy", "ion_refresher", "ion_refresher_content"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-refresher_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask, r as registerInstance, d as createEvent, e as readTask, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { I as ION_CONTENT_CLASS_SELECTOR, b as ION_CONTENT_ELEMENT_SELECTOR, p as printIonContentErrorMsg, g as getScrollElement } from './index-f3946ac1.js';\nimport { t as transitionEndAsync, c as componentOnReady, l as clamp, g as getElementRoot, r as raf } from './helpers-be245865.js';\nimport { d as hapticImpact, I as ImpactStyle } from './haptic-554688a5.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport { h as caretBackSharp, i as arrowDown } from './index-f7dc70ba.js';\nimport { S as SPINNERS } from './spinner-configs-964f7cf3.js';\nimport './index-9b0d46f4.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\n\nconst getRefresherAnimationType = (contentEl) => {\n    const previousSibling = contentEl.previousElementSibling;\n    const hasHeader = previousSibling !== null && previousSibling.tagName === 'ION-HEADER';\n    return hasHeader ? 'translate' : 'scale';\n};\nconst createPullingAnimation = (type, pullingSpinner, refresherEl) => {\n    return type === 'scale'\n        ? createScaleAnimation(pullingSpinner, refresherEl)\n        : createTranslateAnimation(pullingSpinner, refresherEl);\n};\nconst createBaseAnimation = (pullingRefresherIcon) => {\n    const spinner = pullingRefresherIcon.querySelector('ion-spinner');\n    const circle = spinner.shadowRoot.querySelector('circle');\n    const spinnerArrowContainer = pullingRefresherIcon.querySelector('.spinner-arrow-container');\n    const arrowContainer = pullingRefresherIcon.querySelector('.arrow-container');\n    const arrow = arrowContainer ? arrowContainer.querySelector('ion-icon') : null;\n    const baseAnimation = createAnimation().duration(1000).easing('ease-out');\n    const spinnerArrowContainerAnimation = createAnimation()\n        .addElement(spinnerArrowContainer)\n        .keyframes([\n        { offset: 0, opacity: '0.3' },\n        { offset: 0.45, opacity: '0.3' },\n        { offset: 0.55, opacity: '1' },\n        { offset: 1, opacity: '1' },\n    ]);\n    const circleInnerAnimation = createAnimation()\n        .addElement(circle)\n        .keyframes([\n        { offset: 0, strokeDasharray: '1px, 200px' },\n        { offset: 0.2, strokeDasharray: '1px, 200px' },\n        { offset: 0.55, strokeDasharray: '100px, 200px' },\n        { offset: 1, strokeDasharray: '100px, 200px' },\n    ]);\n    const circleOuterAnimation = createAnimation()\n        .addElement(spinner)\n        .keyframes([\n        { offset: 0, transform: 'rotate(-90deg)' },\n        { offset: 1, transform: 'rotate(210deg)' },\n    ]);\n    /**\n     * Only add arrow animation if present\n     * this allows users to customize the spinners\n     * without errors being thrown\n     */\n    if (arrowContainer && arrow) {\n        const arrowContainerAnimation = createAnimation()\n            .addElement(arrowContainer)\n            .keyframes([\n            { offset: 0, transform: 'rotate(0deg)' },\n            { offset: 0.3, transform: 'rotate(0deg)' },\n            { offset: 0.55, transform: 'rotate(280deg)' },\n            { offset: 1, transform: 'rotate(400deg)' },\n        ]);\n        const arrowAnimation = createAnimation()\n            .addElement(arrow)\n            .keyframes([\n            { offset: 0, transform: 'translateX(2px) scale(0)' },\n            { offset: 0.3, transform: 'translateX(2px) scale(0)' },\n            { offset: 0.55, transform: 'translateX(-1.5px) scale(1)' },\n            { offset: 1, transform: 'translateX(-1.5px) scale(1)' },\n        ]);\n        baseAnimation.addAnimation([arrowContainerAnimation, arrowAnimation]);\n    }\n    return baseAnimation.addAnimation([spinnerArrowContainerAnimation, circleInnerAnimation, circleOuterAnimation]);\n};\nconst createScaleAnimation = (pullingRefresherIcon, refresherEl) => {\n    /**\n     * Do not take the height of the refresher icon\n     * because at this point the DOM has not updated,\n     * so the refresher icon is still hidden with\n     * display: none.\n     * The `ion-refresher` container height\n     * is roughly the amount we need to offset\n     * the icon by when pulling down.\n     */\n    const height = refresherEl.clientHeight;\n    const spinnerAnimation = createAnimation()\n        .addElement(pullingRefresherIcon)\n        .keyframes([\n        { offset: 0, transform: `scale(0) translateY(-${height}px)` },\n        { offset: 1, transform: 'scale(1) translateY(100px)' },\n    ]);\n    return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createTranslateAnimation = (pullingRefresherIcon, refresherEl) => {\n    /**\n     * Do not take the height of the refresher icon\n     * because at this point the DOM has not updated,\n     * so the refresher icon is still hidden with\n     * display: none.\n     * The `ion-refresher` container height\n     * is roughly the amount we need to offset\n     * the icon by when pulling down.\n     */\n    const height = refresherEl.clientHeight;\n    const spinnerAnimation = createAnimation()\n        .addElement(pullingRefresherIcon)\n        .keyframes([\n        { offset: 0, transform: `translateY(-${height}px)` },\n        { offset: 1, transform: 'translateY(100px)' },\n    ]);\n    return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createSnapBackAnimation = (pullingRefresherIcon) => {\n    return createAnimation()\n        .duration(125)\n        .addElement(pullingRefresherIcon)\n        .fromTo('transform', 'translateY(var(--ion-pulling-refresher-translate, 100px))', 'translateY(0px)');\n};\n// iOS Native Refresher\n// -----------------------------\nconst setSpinnerOpacity = (spinner, opacity) => {\n    spinner.style.setProperty('opacity', opacity.toString());\n};\nconst handleScrollWhilePulling = (ticks, numTicks, pullAmount) => {\n    const max = 1;\n    writeTask(() => {\n        ticks.forEach((el, i) => {\n            /**\n             * Compute the opacity of each tick\n             * mark as a percentage of the pullAmount\n             * offset by max / numTicks so\n             * the tick marks are shown staggered.\n             */\n            const min = i * (max / numTicks);\n            const range = max - min;\n            const start = pullAmount - min;\n            const progression = clamp(0, start / range, 1);\n            el.style.setProperty('opacity', progression.toString());\n        });\n    });\n};\nconst handleScrollWhileRefreshing = (spinner, lastVelocityY) => {\n    writeTask(() => {\n        // If user pulls down quickly, the spinner should spin faster\n        spinner.style.setProperty('--refreshing-rotation-duration', lastVelocityY >= 1.0 ? '0.5s' : '2s');\n        spinner.style.setProperty('opacity', '1');\n    });\n};\nconst translateElement = (el, value, duration = 200) => {\n    if (!el) {\n        return Promise.resolve();\n    }\n    const trans = transitionEndAsync(el, duration);\n    writeTask(() => {\n        el.style.setProperty('transition', `${duration}ms all ease-out`);\n        if (value === undefined) {\n            el.style.removeProperty('transform');\n        }\n        else {\n            el.style.setProperty('transform', `translate3d(0px, ${value}, 0px)`);\n        }\n    });\n    return trans;\n};\n// Utils\n// -----------------------------\n/**\n * In order to use the native iOS refresher the device must support rubber band scrolling.\n * As part of this, we need to exclude Desktop Safari because it has a slightly different rubber band effect that is not compatible with the native refresher in Ionic.\n *\n * We also need to be careful not to include devices that spoof their user agent.\n * For example, when using iOS emulation in Chrome the user agent will be spoofed such that\n * navigator.maxTouchPointer > 0. To work around this,\n * we check to see if the apple-pay-logo is supported as a named image which is only\n * true on Apple devices.\n *\n * We previously checked referencEl.style.webkitOverflowScrolling to explicitly check\n * for rubber band support. However, this property was removed on iPadOS and it's possible\n * that this will be removed on iOS in the future too.\n *\n */\nconst supportsRubberBandScrolling = () => {\n    return navigator.maxTouchPoints > 0 && CSS.supports('background: -webkit-named-image(apple-pay-logo-black)');\n};\nconst shouldUseNativeRefresher = async (referenceEl, mode) => {\n    const refresherContent = referenceEl.querySelector('ion-refresher-content');\n    if (!refresherContent) {\n        return Promise.resolve(false);\n    }\n    await new Promise((resolve) => componentOnReady(refresherContent, resolve));\n    const pullingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n    const refreshingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n    return (pullingSpinner !== null &&\n        refreshingSpinner !== null &&\n        ((mode === 'ios' && supportsRubberBandScrolling()) || mode === 'md'));\n};\n\nconst refresherIosCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-ios .refresher-pulling-icon,.refresher-ios .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-ios .refresher-pulling-text,.refresher-ios .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-lines-ios line,.refresher-ios .refresher-refreshing .spinner-lines-small-ios line,.refresher-ios .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-bubbles circle,.refresher-ios .refresher-refreshing .spinner-circles circle,.refresher-ios .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}.refresher-native .refresher-refreshing ion-spinner{--refreshing-rotation-duration:2s;display:none;-webkit-animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards;animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards}.refresher-native .refresher-refreshing{display:none;-webkit-animation:250ms linear refresher-pop forwards;animation:250ms linear refresher-pop forwards}.refresher-native ion-spinner{width:32px;height:32px;color:var(--ion-color-step-450, #747577)}.refresher-native.refresher-refreshing .refresher-pulling ion-spinner,.refresher-native.refresher-completing .refresher-pulling ion-spinner{display:none}.refresher-native.refresher-refreshing .refresher-refreshing ion-spinner,.refresher-native.refresher-completing .refresher-refreshing ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-pulling ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-refreshing ion-spinner{display:none}.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0) rotate(180deg);transform:scale(0) rotate(180deg);-webkit-transition:300ms;transition:300ms}@-webkit-keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}\";\nconst IonRefresherIosStyle0 = refresherIosCss;\n\nconst refresherMdCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-md .refresher-pulling-icon,.refresher-md .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-md .refresher-pulling-text,.refresher-md .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-lines-md line,.refresher-md .refresher-refreshing .spinner-lines-small-md line,.refresher-md .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-bubbles circle,.refresher-md .refresher-refreshing .spinner-circles circle,.refresher-md .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:24px;height:24px;color:var(--ion-color-primary, #3880ff)}ion-refresher.refresher-native .spinner-arrow-container{display:inherit}ion-refresher.refresher-native .arrow-container{display:block;position:absolute;width:24px;height:24px}ion-refresher.refresher-native .arrow-container ion-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;bottom:-4px;position:absolute;color:var(--ion-color-primary, #3880ff);font-size:12px}ion-refresher.refresher-native.refresher-pulling ion-refresher-content .refresher-pulling,ion-refresher.refresher-native.refresher-ready ion-refresher-content .refresher-pulling{display:-ms-flexbox;display:flex}ion-refresher.refresher-native.refresher-refreshing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-cancelling ion-refresher-content .refresher-refreshing{display:-ms-flexbox;display:flex}ion-refresher.refresher-native .refresher-pulling-icon{-webkit-transform:translateY(calc(-100% - 10px));transform:translateY(calc(-100% - 10px))}ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;border-radius:100%;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;display:-ms-flexbox;display:flex;border:1px solid var(--ion-color-step-200, #ececec);background:var(--ion-color-step-250, #ffffff);-webkit-box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1);box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1)}\";\nconst IonRefresherMdStyle0 = refresherMdCss;\n\nconst Refresher = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRefresh = createEvent(this, \"ionRefresh\", 7);\n        this.ionPull = createEvent(this, \"ionPull\", 7);\n        this.ionStart = createEvent(this, \"ionStart\", 7);\n        this.appliedStyles = false;\n        this.didStart = false;\n        this.progress = 0;\n        this.pointerDown = false;\n        this.needsCompletion = false;\n        this.didRefresh = false;\n        this.lastVelocityY = 0;\n        this.animations = [];\n        this.nativeRefresher = false;\n        this.state = 1 /* RefresherState.Inactive */;\n        this.pullMin = 60;\n        this.pullMax = this.pullMin + 60;\n        this.closeDuration = '280ms';\n        this.snapbackDuration = '280ms';\n        this.pullFactor = 1;\n        this.disabled = false;\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    async checkNativeRefresher() {\n        const useNativeRefresher = await shouldUseNativeRefresher(this.el, getIonMode(this));\n        if (useNativeRefresher && !this.nativeRefresher) {\n            const contentEl = this.el.closest('ion-content');\n            this.setupNativeRefresher(contentEl);\n        }\n        else if (!useNativeRefresher) {\n            this.destroyNativeRefresher();\n        }\n    }\n    destroyNativeRefresher() {\n        if (this.scrollEl && this.scrollListenerCallback) {\n            this.scrollEl.removeEventListener('scroll', this.scrollListenerCallback);\n            this.scrollListenerCallback = undefined;\n        }\n        this.nativeRefresher = false;\n    }\n    async resetNativeRefresher(el, state) {\n        this.state = state;\n        if (getIonMode(this) === 'ios') {\n            await translateElement(el, undefined, 300);\n        }\n        else {\n            await transitionEndAsync(this.el.querySelector('.refresher-refreshing-icon'), 200);\n        }\n        this.didRefresh = false;\n        this.needsCompletion = false;\n        this.pointerDown = false;\n        this.animations.forEach((ani) => ani.destroy());\n        this.animations = [];\n        this.progress = 0;\n        this.state = 1 /* RefresherState.Inactive */;\n    }\n    async setupiOSNativeRefresher(pullingSpinner, refreshingSpinner) {\n        this.elementToTransform = this.scrollEl;\n        const ticks = pullingSpinner.shadowRoot.querySelectorAll('svg');\n        let MAX_PULL = this.scrollEl.clientHeight * 0.16;\n        const NUM_TICKS = ticks.length;\n        writeTask(() => ticks.forEach((el) => el.style.setProperty('animation', 'none')));\n        this.scrollListenerCallback = () => {\n            // If pointer is not on screen or refresher is not active, ignore scroll\n            if (!this.pointerDown && this.state === 1 /* RefresherState.Inactive */) {\n                return;\n            }\n            readTask(() => {\n                // PTR should only be active when overflow scrolling at the top\n                const scrollTop = this.scrollEl.scrollTop;\n                const refresherHeight = this.el.clientHeight;\n                if (scrollTop > 0) {\n                    /**\n                     * If refresher is refreshing and user tries to scroll\n                     * progressively fade refresher out/in\n                     */\n                    if (this.state === 8 /* RefresherState.Refreshing */) {\n                        const ratio = clamp(0, scrollTop / (refresherHeight * 0.5), 1);\n                        writeTask(() => setSpinnerOpacity(refreshingSpinner, 1 - ratio));\n                        return;\n                    }\n                    return;\n                }\n                if (this.pointerDown) {\n                    if (!this.didStart) {\n                        this.didStart = true;\n                        this.ionStart.emit();\n                    }\n                    // emit \"pulling\" on every move\n                    if (this.pointerDown) {\n                        this.ionPull.emit();\n                    }\n                }\n                /**\n                 * We want to delay the start of this gesture by ~30px\n                 * when initially pulling down so the refresher does not\n                 * overlap with the content. But when letting go of the\n                 * gesture before the refresher completes, we want the\n                 * refresher tick marks to quickly fade out.\n                 */\n                const offset = this.didStart ? 30 : 0;\n                const pullAmount = (this.progress = clamp(0, (Math.abs(scrollTop) - offset) / MAX_PULL, 1));\n                const shouldShowRefreshingSpinner = this.state === 8 /* RefresherState.Refreshing */ || pullAmount === 1;\n                if (shouldShowRefreshingSpinner) {\n                    if (this.pointerDown) {\n                        handleScrollWhileRefreshing(refreshingSpinner, this.lastVelocityY);\n                    }\n                    if (!this.didRefresh) {\n                        this.beginRefresh();\n                        this.didRefresh = true;\n                        hapticImpact({ style: ImpactStyle.Light });\n                        /**\n                         * Translate the content element otherwise when pointer is removed\n                         * from screen the scroll content will bounce back over the refresher\n                         */\n                        if (!this.pointerDown) {\n                            translateElement(this.elementToTransform, `${refresherHeight}px`);\n                        }\n                    }\n                }\n                else {\n                    this.state = 2 /* RefresherState.Pulling */;\n                    handleScrollWhilePulling(ticks, NUM_TICKS, pullAmount);\n                }\n            });\n        };\n        this.scrollEl.addEventListener('scroll', this.scrollListenerCallback);\n        this.gesture = (await import('./index-2cf77112.js')).createGesture({\n            el: this.scrollEl,\n            gestureName: 'refresher',\n            gesturePriority: 31,\n            direction: 'y',\n            threshold: 5,\n            onStart: () => {\n                this.pointerDown = true;\n                if (!this.didRefresh) {\n                    translateElement(this.elementToTransform, '0px');\n                }\n                /**\n                 * If the content had `display: none` when\n                 * the refresher was initialized, its clientHeight\n                 * will be 0. When the gesture starts, the content\n                 * will be visible, so try to get the correct\n                 * client height again. This is most common when\n                 * using the refresher in an ion-menu.\n                 */\n                if (MAX_PULL === 0) {\n                    MAX_PULL = this.scrollEl.clientHeight * 0.16;\n                }\n            },\n            onMove: (ev) => {\n                this.lastVelocityY = ev.velocityY;\n            },\n            onEnd: () => {\n                this.pointerDown = false;\n                this.didStart = false;\n                if (this.needsCompletion) {\n                    this.resetNativeRefresher(this.elementToTransform, 32 /* RefresherState.Completing */);\n                    this.needsCompletion = false;\n                }\n                else if (this.didRefresh) {\n                    readTask(() => translateElement(this.elementToTransform, `${this.el.clientHeight}px`));\n                }\n            },\n        });\n        this.disabledChanged();\n    }\n    async setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner) {\n        const circle = getElementRoot(pullingSpinner).querySelector('circle');\n        const pullingRefresherIcon = this.el.querySelector('ion-refresher-content .refresher-pulling-icon');\n        const refreshingCircle = getElementRoot(refreshingSpinner).querySelector('circle');\n        if (circle !== null && refreshingCircle !== null) {\n            writeTask(() => {\n                circle.style.setProperty('animation', 'none');\n                // This lines up the animation on the refreshing spinner with the pulling spinner\n                refreshingSpinner.style.setProperty('animation-delay', '-655ms');\n                refreshingCircle.style.setProperty('animation-delay', '-655ms');\n            });\n        }\n        this.gesture = (await import('./index-2cf77112.js')).createGesture({\n            el: this.scrollEl,\n            gestureName: 'refresher',\n            gesturePriority: 31,\n            direction: 'y',\n            threshold: 5,\n            canStart: () => this.state !== 8 /* RefresherState.Refreshing */ &&\n                this.state !== 32 /* RefresherState.Completing */ &&\n                this.scrollEl.scrollTop === 0,\n            onStart: (ev) => {\n                this.progress = 0;\n                ev.data = { animation: undefined, didStart: false, cancelled: false };\n            },\n            onMove: (ev) => {\n                if ((ev.velocityY < 0 && this.progress === 0 && !ev.data.didStart) || ev.data.cancelled) {\n                    ev.data.cancelled = true;\n                    return;\n                }\n                if (!ev.data.didStart) {\n                    ev.data.didStart = true;\n                    this.state = 2 /* RefresherState.Pulling */;\n                    // When ion-refresher is being used with a custom scroll target, the overflow styles need to be applied directly instead of via a css variable\n                    const { scrollEl } = this;\n                    const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n                    writeTask(() => scrollEl.style.setProperty(overflowProperty, 'hidden'));\n                    const animationType = getRefresherAnimationType(contentEl);\n                    const animation = createPullingAnimation(animationType, pullingRefresherIcon, this.el);\n                    ev.data.animation = animation;\n                    animation.progressStart(false, 0);\n                    this.ionStart.emit();\n                    this.animations.push(animation);\n                    return;\n                }\n                // Since we are using an easing curve, slow the gesture tracking down a bit\n                this.progress = clamp(0, (ev.deltaY / 180) * 0.5, 1);\n                ev.data.animation.progressStep(this.progress);\n                this.ionPull.emit();\n            },\n            onEnd: (ev) => {\n                if (!ev.data.didStart) {\n                    return;\n                }\n                this.gesture.enable(false);\n                const { scrollEl } = this;\n                const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n                writeTask(() => scrollEl.style.removeProperty(overflowProperty));\n                if (this.progress <= 0.4) {\n                    ev.data.animation.progressEnd(0, this.progress, 500).onFinish(() => {\n                        this.animations.forEach((ani) => ani.destroy());\n                        this.animations = [];\n                        this.gesture.enable(true);\n                        this.state = 1 /* RefresherState.Inactive */;\n                    });\n                    return;\n                }\n                const progress = getTimeGivenProgression([0, 0], [0, 0], [1, 1], [1, 1], this.progress)[0];\n                const snapBackAnimation = createSnapBackAnimation(pullingRefresherIcon);\n                this.animations.push(snapBackAnimation);\n                writeTask(async () => {\n                    pullingRefresherIcon.style.setProperty('--ion-pulling-refresher-translate', `${progress * 100}px`);\n                    ev.data.animation.progressEnd();\n                    await snapBackAnimation.play();\n                    this.beginRefresh();\n                    ev.data.animation.destroy();\n                    this.gesture.enable(true);\n                });\n            },\n        });\n        this.disabledChanged();\n    }\n    async setupNativeRefresher(contentEl) {\n        if (this.scrollListenerCallback || !contentEl || this.nativeRefresher || !this.scrollEl) {\n            return;\n        }\n        /**\n         * If using non-native refresher before make sure\n         * we clean up any old CSS. This can happen when\n         * a user manually calls the refresh method in a\n         * component create callback before the native\n         * refresher is setup.\n         */\n        this.setCss(0, '', false, '');\n        this.nativeRefresher = true;\n        const pullingSpinner = this.el.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n        const refreshingSpinner = this.el.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n        if (getIonMode(this) === 'ios') {\n            this.setupiOSNativeRefresher(pullingSpinner, refreshingSpinner);\n        }\n        else {\n            this.setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner);\n        }\n    }\n    componentDidUpdate() {\n        this.checkNativeRefresher();\n    }\n    async connectedCallback() {\n        if (this.el.getAttribute('slot') !== 'fixed') {\n            console.error('Make sure you use: <ion-refresher slot=\"fixed\">');\n            return;\n        }\n        const contentEl = this.el.closest(ION_CONTENT_ELEMENT_SELECTOR);\n        if (!contentEl) {\n            printIonContentErrorMsg(this.el);\n            return;\n        }\n        /**\n         * Waits for the content to be ready before querying the scroll\n         * or the background content element.\n         */\n        componentOnReady(contentEl, async () => {\n            const customScrollTarget = contentEl.querySelector(ION_CONTENT_CLASS_SELECTOR);\n            /**\n             * Query the custom scroll target (if available), first. In refresher implementations,\n             * the ion-refresher element will always be a direct child of ion-content (slot=\"fixed\"). By\n             * querying the custom scroll target first and falling back to the ion-content element,\n             * the correct scroll element will be returned by the implementation.\n             */\n            this.scrollEl = await getScrollElement(customScrollTarget !== null && customScrollTarget !== void 0 ? customScrollTarget : contentEl);\n            /**\n             * Query the background content element from the host ion-content element directly.\n             */\n            this.backgroundContentEl = await contentEl.getBackgroundElement();\n            if (await shouldUseNativeRefresher(this.el, getIonMode(this))) {\n                this.setupNativeRefresher(contentEl);\n            }\n            else {\n                this.gesture = (await import('./index-2cf77112.js')).createGesture({\n                    el: contentEl,\n                    gestureName: 'refresher',\n                    gesturePriority: 31,\n                    direction: 'y',\n                    threshold: 20,\n                    passive: false,\n                    canStart: () => this.canStart(),\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: () => this.onEnd(),\n                });\n                this.disabledChanged();\n            }\n        });\n    }\n    disconnectedCallback() {\n        this.destroyNativeRefresher();\n        this.scrollEl = undefined;\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    /**\n     * Call `complete()` when your async operation has completed.\n     * For example, the `refreshing` state is while the app is performing\n     * an asynchronous operation, such as receiving more data from an\n     * AJAX request. Once the data has been received, you then call this\n     * method to signify that the refreshing has completed and to close\n     * the refresher. This method also changes the refresher's state from\n     * `refreshing` to `completing`.\n     */\n    async complete() {\n        if (this.nativeRefresher) {\n            this.needsCompletion = true;\n            // Do not reset scroll el until user removes pointer from screen\n            if (!this.pointerDown) {\n                raf(() => raf(() => this.resetNativeRefresher(this.elementToTransform, 32 /* RefresherState.Completing */)));\n            }\n        }\n        else {\n            this.close(32 /* RefresherState.Completing */, '120ms');\n        }\n    }\n    /**\n     * Changes the refresher's state from `refreshing` to `cancelling`.\n     */\n    async cancel() {\n        if (this.nativeRefresher) {\n            // Do not reset scroll el until user removes pointer from screen\n            if (!this.pointerDown) {\n                raf(() => raf(() => this.resetNativeRefresher(this.elementToTransform, 16 /* RefresherState.Cancelling */)));\n            }\n        }\n        else {\n            this.close(16 /* RefresherState.Cancelling */, '');\n        }\n    }\n    /**\n     * A number representing how far down the user has pulled.\n     * The number `0` represents the user hasn't pulled down at all. The\n     * number `1`, and anything greater than `1`, represents that the user\n     * has pulled far enough down that when they let go then the refresh will\n     * happen. If they let go and the number is less than `1`, then the\n     * refresh will not happen, and the content will return to it's original\n     * position.\n     */\n    getProgress() {\n        return Promise.resolve(this.progress);\n    }\n    canStart() {\n        if (!this.scrollEl) {\n            return false;\n        }\n        if (this.state !== 1 /* RefresherState.Inactive */) {\n            return false;\n        }\n        // if the scrollTop is greater than zero then it's\n        // not possible to pull the content down yet\n        if (this.scrollEl.scrollTop > 0) {\n            return false;\n        }\n        return true;\n    }\n    onStart() {\n        this.progress = 0;\n        this.state = 1 /* RefresherState.Inactive */;\n        this.memoizeOverflowStyle();\n    }\n    onMove(detail) {\n        if (!this.scrollEl) {\n            return;\n        }\n        // this method can get called like a bazillion times per second,\n        // so it's built to be as efficient as possible, and does its\n        // best to do any DOM read/writes only when absolutely necessary\n        // if multi-touch then get out immediately\n        const ev = detail.event;\n        if (ev.touches !== undefined && ev.touches.length > 1) {\n            return;\n        }\n        // do nothing if it's actively refreshing\n        // or it's in the way of closing\n        // or this was never a startY\n        if ((this.state & 56 /* RefresherState._BUSY_ */) !== 0) {\n            return;\n        }\n        const pullFactor = Number.isNaN(this.pullFactor) || this.pullFactor < 0 ? 1 : this.pullFactor;\n        const deltaY = detail.deltaY * pullFactor;\n        // don't bother if they're scrolling up\n        // and have not already started dragging\n        if (deltaY <= 0) {\n            // the current Y is higher than the starting Y\n            // so they scrolled up enough to be ignored\n            this.progress = 0;\n            this.state = 1 /* RefresherState.Inactive */;\n            if (this.appliedStyles) {\n                // reset the styles only if they were applied\n                this.setCss(0, '', false, '');\n                return;\n            }\n            return;\n        }\n        if (this.state === 1 /* RefresherState.Inactive */) {\n            // this refresh is not already actively pulling down\n            // get the content's scrollTop\n            const scrollHostScrollTop = this.scrollEl.scrollTop;\n            // if the scrollTop is greater than zero then it's\n            // not possible to pull the content down yet\n            if (scrollHostScrollTop > 0) {\n                this.progress = 0;\n                return;\n            }\n            // content scrolled all the way to the top, and dragging down\n            this.state = 2 /* RefresherState.Pulling */;\n        }\n        // prevent native scroll events\n        if (ev.cancelable) {\n            ev.preventDefault();\n        }\n        // the refresher is actively pulling at this point\n        // move the scroll element within the content element\n        this.setCss(deltaY, '0ms', true, '');\n        if (deltaY === 0) {\n            // don't continue if there's no delta yet\n            this.progress = 0;\n            return;\n        }\n        const pullMin = this.pullMin;\n        // set pull progress\n        this.progress = deltaY / pullMin;\n        // emit \"start\" if it hasn't started yet\n        if (!this.didStart) {\n            this.didStart = true;\n            this.ionStart.emit();\n        }\n        // emit \"pulling\" on every move\n        this.ionPull.emit();\n        // do nothing if the delta is less than the pull threshold\n        if (deltaY < pullMin) {\n            // ensure it stays in the pulling state, cuz its not ready yet\n            this.state = 2 /* RefresherState.Pulling */;\n            return;\n        }\n        if (deltaY > this.pullMax) {\n            // they pulled farther than the max, so kick off the refresh\n            this.beginRefresh();\n            return;\n        }\n        // pulled farther than the pull min!!\n        // it is now in the `ready` state!!\n        // if they let go then it'll refresh, kerpow!!\n        this.state = 4 /* RefresherState.Ready */;\n        return;\n    }\n    onEnd() {\n        // only run in a zone when absolutely necessary\n        if (this.state === 4 /* RefresherState.Ready */) {\n            // they pulled down far enough, so it's ready to refresh\n            this.beginRefresh();\n        }\n        else if (this.state === 2 /* RefresherState.Pulling */) {\n            // they were pulling down, but didn't pull down far enough\n            // set the content back to it's original location\n            // and close the refresher\n            // set that the refresh is actively cancelling\n            this.cancel();\n        }\n        else if (this.state === 1 /* RefresherState.Inactive */) {\n            /**\n             * The pull to refresh gesture was aborted\n             * so we should immediately restore any overflow styles\n             * that have been modified. Do not call this.cancel\n             * because the styles will only be reset after a timeout.\n             * If the gesture is aborted then scrolling should be\n             * available right away.\n             */\n            this.restoreOverflowStyle();\n        }\n    }\n    beginRefresh() {\n        // assumes we're already back in a zone\n        // they pulled down far enough, so it's ready to refresh\n        this.state = 8 /* RefresherState.Refreshing */;\n        // place the content in a hangout position while it thinks\n        this.setCss(this.pullMin, this.snapbackDuration, true, '');\n        // emit \"refresh\" because it was pulled down far enough\n        // and they let go to begin refreshing\n        this.ionRefresh.emit({\n            complete: this.complete.bind(this),\n        });\n    }\n    close(state, delay) {\n        // create fallback timer incase something goes wrong with transitionEnd event\n        setTimeout(() => {\n            this.state = 1 /* RefresherState.Inactive */;\n            this.progress = 0;\n            this.didStart = false;\n            /**\n             * Reset any overflow styles so the\n             * user can scroll again.\n             */\n            this.setCss(0, '0ms', false, '', true);\n        }, 600);\n        // reset the styles on the scroll element\n        // set that the refresh is actively cancelling/completing\n        this.state = state;\n        this.setCss(0, this.closeDuration, true, delay);\n    }\n    setCss(y, duration, overflowVisible, delay, shouldRestoreOverflowStyle = false) {\n        if (this.nativeRefresher) {\n            return;\n        }\n        this.appliedStyles = y > 0;\n        writeTask(() => {\n            if (this.scrollEl && this.backgroundContentEl) {\n                const scrollStyle = this.scrollEl.style;\n                const backgroundStyle = this.backgroundContentEl.style;\n                scrollStyle.transform = backgroundStyle.transform = y > 0 ? `translateY(${y}px) translateZ(0px)` : '';\n                scrollStyle.transitionDuration = backgroundStyle.transitionDuration = duration;\n                scrollStyle.transitionDelay = backgroundStyle.transitionDelay = delay;\n                scrollStyle.overflow = overflowVisible ? 'hidden' : '';\n            }\n            /**\n             * Reset the overflow styles only once\n             * the pull to refresh effect has been closed.\n             * This ensures that the gesture is done\n             * and the refresh operation has either\n             * been aborted or has completed.\n             */\n            if (shouldRestoreOverflowStyle) {\n                this.restoreOverflowStyle();\n            }\n        });\n    }\n    memoizeOverflowStyle() {\n        if (this.scrollEl) {\n            const { overflow, overflowX, overflowY } = this.scrollEl.style;\n            this.overflowStyles = {\n                overflow: overflow !== null && overflow !== void 0 ? overflow : '',\n                overflowX: overflowX !== null && overflowX !== void 0 ? overflowX : '',\n                overflowY: overflowY !== null && overflowY !== void 0 ? overflowY : '',\n            };\n        }\n    }\n    restoreOverflowStyle() {\n        if (this.overflowStyles !== undefined && this.scrollEl !== undefined) {\n            const { overflow, overflowX, overflowY } = this.overflowStyles;\n            this.scrollEl.style.overflow = overflow;\n            this.scrollEl.style.overflowX = overflowX;\n            this.scrollEl.style.overflowY = overflowY;\n            this.overflowStyles = undefined;\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '96f4f595ebdb92a12755b642398691bcaab9f7c1', slot: \"fixed\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`refresher-${mode}`]: true,\n                'refresher-native': this.nativeRefresher,\n                'refresher-active': this.state !== 1 /* RefresherState.Inactive */,\n                'refresher-pulling': this.state === 2 /* RefresherState.Pulling */,\n                'refresher-ready': this.state === 4 /* RefresherState.Ready */,\n                'refresher-refreshing': this.state === 8 /* RefresherState.Refreshing */,\n                'refresher-cancelling': this.state === 16 /* RefresherState.Cancelling */,\n                'refresher-completing': this.state === 32 /* RefresherState.Completing */,\n            } }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nRefresher.style = {\n    ios: IonRefresherIosStyle0,\n    md: IonRefresherMdStyle0\n};\n\nconst RefresherContent = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n        this.pullingIcon = undefined;\n        this.pullingText = undefined;\n        this.refreshingSpinner = undefined;\n        this.refreshingText = undefined;\n    }\n    componentWillLoad() {\n        if (this.pullingIcon === undefined) {\n            /**\n             * The native iOS refresher uses a spinner instead of\n             * an icon, so we need to see if this device supports\n             * the native iOS refresher.\n             */\n            const hasRubberBandScrolling = supportsRubberBandScrolling();\n            const mode = getIonMode(this);\n            const overflowRefresher = hasRubberBandScrolling ? 'lines' : arrowDown;\n            this.pullingIcon = config.get('refreshingIcon', mode === 'ios' && hasRubberBandScrolling ? config.get('spinner', overflowRefresher) : 'circular');\n        }\n        if (this.refreshingSpinner === undefined) {\n            const mode = getIonMode(this);\n            this.refreshingSpinner = config.get('refreshingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'circular'));\n        }\n    }\n    renderPullingText() {\n        const { customHTMLEnabled, pullingText } = this;\n        if (customHTMLEnabled) {\n            return h(\"div\", { class: \"refresher-pulling-text\", innerHTML: sanitizeDOMString(pullingText) });\n        }\n        return h(\"div\", { class: \"refresher-pulling-text\" }, pullingText);\n    }\n    renderRefreshingText() {\n        const { customHTMLEnabled, refreshingText } = this;\n        if (customHTMLEnabled) {\n            return h(\"div\", { class: \"refresher-refreshing-text\", innerHTML: sanitizeDOMString(refreshingText) });\n        }\n        return h(\"div\", { class: \"refresher-refreshing-text\" }, refreshingText);\n    }\n    render() {\n        const pullingIcon = this.pullingIcon;\n        const hasSpinner = pullingIcon != null && SPINNERS[pullingIcon] !== undefined;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'cf3caa51c4aba8a95622f6d32cafa90b683b9d6e', class: mode }, h(\"div\", { key: '5ad70801104bbea873d3525206660c52e4447903', class: \"refresher-pulling\" }, this.pullingIcon && hasSpinner && (h(\"div\", { key: '0f95df169fd367528bfaa5d9ccf6690a613609c4', class: \"refresher-pulling-icon\" }, h(\"div\", { key: '4b8f0465a19f017751b207807c32e1fe00fda433', class: \"spinner-arrow-container\" }, h(\"ion-spinner\", { key: '77e60179d76f0d17f8f2dc3518f97a2a924418e6', name: this.pullingIcon, paused: true }), mode === 'md' && this.pullingIcon === 'circular' && (h(\"div\", { key: 'f78f63f08f071bead1bfe655bae6394f8a219d91', class: \"arrow-container\" }, h(\"ion-icon\", { key: '4d833d134d2b221cae2dfb0611d4029f2d664db5', icon: caretBackSharp, \"aria-hidden\": \"true\" })))))), this.pullingIcon && !hasSpinner && (h(\"div\", { key: 'e6db19d7fa324363d2a7c3c046510f4c8461f7e6', class: \"refresher-pulling-icon\" }, h(\"ion-icon\", { key: '66c2ef1a53c5809f49891de515da5f55d9bf8dcc', icon: this.pullingIcon, lazy: false, \"aria-hidden\": \"true\" }))), this.pullingText !== undefined && this.renderPullingText()), h(\"div\", { key: '80c413e21d362a5bb0419fcd13092453b3445cee', class: \"refresher-refreshing\" }, this.refreshingSpinner && (h(\"div\", { key: '0d5511f9644de26332a1a9ed39b160691fab74d9', class: \"refresher-refreshing-icon\" }, h(\"ion-spinner\", { key: '54e4a96b081c7b453a98e00cceea7c086268a450', name: this.refreshingSpinner }))), this.refreshingText !== undefined && this.renderRefreshingText())));\n    }\n    get el() { return getElement(this); }\n};\n\nexport { Refresher as ion_refresher, RefresherContent as ion_refresher_content };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AAC3I,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,4BAA4B;AACzE,SAASC,CAAC,IAAIC,0BAA0B,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,uBAAuB,EAAEP,CAAC,IAAIQ,gBAAgB,QAAQ,qBAAqB;AAC7J,SAASC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,KAAK,EAAEd,CAAC,IAAIe,cAAc,EAAE1B,CAAC,IAAI2B,GAAG,QAAQ,uBAAuB;AACjI,SAASzB,CAAC,IAAI0B,YAAY,EAAEf,CAAC,IAAIgB,WAAW,QAAQ,sBAAsB;AAC1E,SAASd,CAAC,IAAIe,UAAU,EAAER,CAAC,IAAIS,MAAM,QAAQ,4BAA4B;AACzE,SAAST,CAAC,IAAIU,eAAe,QAAQ,yBAAyB;AAC9D,SAASC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,sBAAsB;AAC/F,SAAS9B,CAAC,IAAI+B,cAAc,EAAEC,CAAC,IAAIC,SAAS,QAAQ,qBAAqB;AACzE,SAASC,CAAC,IAAIC,QAAQ,QAAQ,+BAA+B;AAC7D,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,OAAO,qBAAqB;AAE5B,MAAMC,yBAAyB,GAAIC,SAAS,IAAK;EAC7C,MAAMC,eAAe,GAAGD,SAAS,CAACE,sBAAsB;EACxD,MAAMC,SAAS,GAAGF,eAAe,KAAK,IAAI,IAAIA,eAAe,CAACG,OAAO,KAAK,YAAY;EACtF,OAAOD,SAAS,GAAG,WAAW,GAAG,OAAO;AAC5C,CAAC;AACD,MAAME,sBAAsB,GAAGA,CAACC,IAAI,EAAEC,cAAc,EAAEC,WAAW,KAAK;EAClE,OAAOF,IAAI,KAAK,OAAO,GACjBG,oBAAoB,CAACF,cAAc,EAAEC,WAAW,CAAC,GACjDE,wBAAwB,CAACH,cAAc,EAAEC,WAAW,CAAC;AAC/D,CAAC;AACD,MAAMG,mBAAmB,GAAIC,oBAAoB,IAAK;EAClD,MAAMC,OAAO,GAAGD,oBAAoB,CAACE,aAAa,CAAC,aAAa,CAAC;EACjE,MAAMC,MAAM,GAAGF,OAAO,CAACG,UAAU,CAACF,aAAa,CAAC,QAAQ,CAAC;EACzD,MAAMG,qBAAqB,GAAGL,oBAAoB,CAACE,aAAa,CAAC,0BAA0B,CAAC;EAC5F,MAAMI,cAAc,GAAGN,oBAAoB,CAACE,aAAa,CAAC,kBAAkB,CAAC;EAC7E,MAAMK,KAAK,GAAGD,cAAc,GAAGA,cAAc,CAACJ,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI;EAC9E,MAAMM,aAAa,GAAG/B,eAAe,CAAC,CAAC,CAACgC,QAAQ,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,UAAU,CAAC;EACzE,MAAMC,8BAA8B,GAAGlC,eAAe,CAAC,CAAC,CACnDmC,UAAU,CAACP,qBAAqB,CAAC,CACjCQ,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAM,CAAC,EAC7B;IAAED,MAAM,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAM,CAAC,EAChC;IAAED,MAAM,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAI,CAAC,EAC9B;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAI,CAAC,CAC9B,CAAC;EACF,MAAMC,oBAAoB,GAAGvC,eAAe,CAAC,CAAC,CACzCmC,UAAU,CAACT,MAAM,CAAC,CAClBU,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEG,eAAe,EAAE;EAAa,CAAC,EAC5C;IAAEH,MAAM,EAAE,GAAG;IAAEG,eAAe,EAAE;EAAa,CAAC,EAC9C;IAAEH,MAAM,EAAE,IAAI;IAAEG,eAAe,EAAE;EAAe,CAAC,EACjD;IAAEH,MAAM,EAAE,CAAC;IAAEG,eAAe,EAAE;EAAe,CAAC,CACjD,CAAC;EACF,MAAMC,oBAAoB,GAAGzC,eAAe,CAAC,CAAC,CACzCmC,UAAU,CAACX,OAAO,CAAC,CACnBY,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAAiB,CAAC,EAC1C;IAAEL,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAAiB,CAAC,CAC7C,CAAC;EACF;AACJ;AACA;AACA;AACA;EACI,IAAIb,cAAc,IAAIC,KAAK,EAAE;IACzB,MAAMa,uBAAuB,GAAG3C,eAAe,CAAC,CAAC,CAC5CmC,UAAU,CAACN,cAAc,CAAC,CAC1BO,SAAS,CAAC,CACX;MAAEC,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAAe,CAAC,EACxC;MAAEL,MAAM,EAAE,GAAG;MAAEK,SAAS,EAAE;IAAe,CAAC,EAC1C;MAAEL,MAAM,EAAE,IAAI;MAAEK,SAAS,EAAE;IAAiB,CAAC,EAC7C;MAAEL,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAAiB,CAAC,CAC7C,CAAC;IACF,MAAME,cAAc,GAAG5C,eAAe,CAAC,CAAC,CACnCmC,UAAU,CAACL,KAAK,CAAC,CACjBM,SAAS,CAAC,CACX;MAAEC,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAA2B,CAAC,EACpD;MAAEL,MAAM,EAAE,GAAG;MAAEK,SAAS,EAAE;IAA2B,CAAC,EACtD;MAAEL,MAAM,EAAE,IAAI;MAAEK,SAAS,EAAE;IAA8B,CAAC,EAC1D;MAAEL,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAA8B,CAAC,CAC1D,CAAC;IACFX,aAAa,CAACc,YAAY,CAAC,CAACF,uBAAuB,EAAEC,cAAc,CAAC,CAAC;EACzE;EACA,OAAOb,aAAa,CAACc,YAAY,CAAC,CAACX,8BAA8B,EAAEK,oBAAoB,EAAEE,oBAAoB,CAAC,CAAC;AACnH,CAAC;AACD,MAAMrB,oBAAoB,GAAGA,CAACG,oBAAoB,EAAEJ,WAAW,KAAK;EAChE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM2B,MAAM,GAAG3B,WAAW,CAAC4B,YAAY;EACvC,MAAMC,gBAAgB,GAAGhD,eAAe,CAAC,CAAC,CACrCmC,UAAU,CAACZ,oBAAoB,CAAC,CAChCa,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE,wBAAwBI,MAAM;EAAM,CAAC,EAC7D;IAAET,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAA6B,CAAC,CACzD,CAAC;EACF,OAAOpB,mBAAmB,CAACC,oBAAoB,CAAC,CAACsB,YAAY,CAAC,CAACG,gBAAgB,CAAC,CAAC;AACrF,CAAC;AACD,MAAM3B,wBAAwB,GAAGA,CAACE,oBAAoB,EAAEJ,WAAW,KAAK;EACpE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM2B,MAAM,GAAG3B,WAAW,CAAC4B,YAAY;EACvC,MAAMC,gBAAgB,GAAGhD,eAAe,CAAC,CAAC,CACrCmC,UAAU,CAACZ,oBAAoB,CAAC,CAChCa,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE,eAAeI,MAAM;EAAM,CAAC,EACpD;IAAET,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAAoB,CAAC,CAChD,CAAC;EACF,OAAOpB,mBAAmB,CAACC,oBAAoB,CAAC,CAACsB,YAAY,CAAC,CAACG,gBAAgB,CAAC,CAAC;AACrF,CAAC;AACD,MAAMC,uBAAuB,GAAI1B,oBAAoB,IAAK;EACtD,OAAOvB,eAAe,CAAC,CAAC,CACnBgC,QAAQ,CAAC,GAAG,CAAC,CACbG,UAAU,CAACZ,oBAAoB,CAAC,CAChC2B,MAAM,CAAC,WAAW,EAAE,2DAA2D,EAAE,iBAAiB,CAAC;AAC5G,CAAC;AACD;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAAC3B,OAAO,EAAEc,OAAO,KAAK;EAC5Cd,OAAO,CAAC4B,KAAK,CAACC,WAAW,CAAC,SAAS,EAAEf,OAAO,CAACgB,QAAQ,CAAC,CAAC,CAAC;AAC5D,CAAC;AACD,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,KAAK;EAC9D,MAAMC,GAAG,GAAG,CAAC;EACb5F,SAAS,CAAC,MAAM;IACZyF,KAAK,CAACI,OAAO,CAAC,CAACC,EAAE,EAAEvD,CAAC,KAAK;MACrB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMwD,GAAG,GAAGxD,CAAC,IAAIqD,GAAG,GAAGF,QAAQ,CAAC;MAChC,MAAMM,KAAK,GAAGJ,GAAG,GAAGG,GAAG;MACvB,MAAME,KAAK,GAAGN,UAAU,GAAGI,GAAG;MAC9B,MAAMG,WAAW,GAAGxE,KAAK,CAAC,CAAC,EAAEuE,KAAK,GAAGD,KAAK,EAAE,CAAC,CAAC;MAC9CF,EAAE,CAACT,KAAK,CAACC,WAAW,CAAC,SAAS,EAAEY,WAAW,CAACX,QAAQ,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AACD,MAAMY,2BAA2B,GAAGA,CAAC1C,OAAO,EAAE2C,aAAa,KAAK;EAC5DpG,SAAS,CAAC,MAAM;IACZ;IACAyD,OAAO,CAAC4B,KAAK,CAACC,WAAW,CAAC,gCAAgC,EAAEc,aAAa,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;IACjG3C,OAAO,CAAC4B,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC;EAC7C,CAAC,CAAC;AACN,CAAC;AACD,MAAMe,gBAAgB,GAAGA,CAACP,EAAE,EAAEQ,KAAK,EAAErC,QAAQ,GAAG,GAAG,KAAK;EACpD,IAAI,CAAC6B,EAAE,EAAE;IACL,OAAOS,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;EACA,MAAMC,KAAK,GAAGnF,kBAAkB,CAACwE,EAAE,EAAE7B,QAAQ,CAAC;EAC9CjE,SAAS,CAAC,MAAM;IACZ8F,EAAE,CAACT,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,GAAGrB,QAAQ,iBAAiB,CAAC;IAChE,IAAIqC,KAAK,KAAKI,SAAS,EAAE;MACrBZ,EAAE,CAACT,KAAK,CAACsB,cAAc,CAAC,WAAW,CAAC;IACxC,CAAC,MACI;MACDb,EAAE,CAACT,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,oBAAoBgB,KAAK,QAAQ,CAAC;IACxE;EACJ,CAAC,CAAC;EACF,OAAOG,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,2BAA2B,GAAGA,CAAA,KAAM;EACtC,OAAOC,SAAS,CAACC,cAAc,GAAG,CAAC,IAAIC,GAAG,CAACC,QAAQ,CAAC,uDAAuD,CAAC;AAChH,CAAC;AACD,MAAMC,wBAAwB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,WAAW,EAAEC,IAAI,EAAK;IAC1D,MAAMC,gBAAgB,GAAGF,WAAW,CAAC1D,aAAa,CAAC,uBAAuB,CAAC;IAC3E,IAAI,CAAC4D,gBAAgB,EAAE;MACnB,OAAOf,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,MAAM,IAAID,OAAO,CAAEC,OAAO,IAAKhF,gBAAgB,CAAC8F,gBAAgB,EAAEd,OAAO,CAAC,CAAC;IAC3E,MAAMrD,cAAc,GAAGiE,WAAW,CAAC1D,aAAa,CAAC,sDAAsD,CAAC;IACxG,MAAM6D,iBAAiB,GAAGH,WAAW,CAAC1D,aAAa,CAAC,yDAAyD,CAAC;IAC9G,OAAQP,cAAc,KAAK,IAAI,IAC3BoE,iBAAiB,KAAK,IAAI,KACxBF,IAAI,KAAK,KAAK,IAAIT,2BAA2B,CAAC,CAAC,IAAKS,IAAI,KAAK,IAAI,CAAC;EAC5E,CAAC;EAAA,gBAXKJ,wBAAwBA,CAAAO,EAAA,EAAAC,GAAA;IAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAW7B;AAED,MAAMC,eAAe,GAAG,q1KAAq1K;AAC72K,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,cAAc,GAAG,09JAA09J;AACj/J,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,SAAS,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACjBhI,gBAAgB,CAAC,IAAI,EAAEgI,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAG/H,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAACgI,OAAO,GAAGhI,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACiI,QAAQ,GAAGjI,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACvC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACwC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,OAAO,GAAG,EAAE;IAChC,IAAI,CAACE,aAAa,GAAG,OAAO;IAC5B,IAAI,CAACC,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAACH,QAAQ,CAAC;IACvC;EACJ;EACMI,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAtC,iBAAA;MACzB,MAAMuC,kBAAkB,SAASzC,wBAAwB,CAACwC,KAAI,CAAC3D,EAAE,EAAE/D,UAAU,CAAC0H,KAAI,CAAC,CAAC;MACpF,IAAIC,kBAAkB,IAAI,CAACD,KAAI,CAACZ,eAAe,EAAE;QAC7C,MAAMjG,SAAS,GAAG6G,KAAI,CAAC3D,EAAE,CAAC6D,OAAO,CAAC,aAAa,CAAC;QAChDF,KAAI,CAACG,oBAAoB,CAAChH,SAAS,CAAC;MACxC,CAAC,MACI,IAAI,CAAC8G,kBAAkB,EAAE;QAC1BD,KAAI,CAACI,sBAAsB,CAAC,CAAC;MACjC;IAAC;EACL;EACAA,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAC9C,IAAI,CAACD,QAAQ,CAACE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACD,sBAAsB,CAAC;MACxE,IAAI,CAACA,sBAAsB,GAAGrD,SAAS;IAC3C;IACA,IAAI,CAACmC,eAAe,GAAG,KAAK;EAChC;EACMoB,oBAAoBA,CAACnE,EAAE,EAAEgD,KAAK,EAAE;IAAA,IAAAoB,MAAA;IAAA,OAAA/C,iBAAA;MAClC+C,MAAI,CAACpB,KAAK,GAAGA,KAAK;MAClB,IAAI/G,UAAU,CAACmI,MAAI,CAAC,KAAK,KAAK,EAAE;QAC5B,MAAM7D,gBAAgB,CAACP,EAAE,EAAEY,SAAS,EAAE,GAAG,CAAC;MAC9C,CAAC,MACI;QACD,MAAMpF,kBAAkB,CAAC4I,MAAI,CAACpE,EAAE,CAACpC,aAAa,CAAC,4BAA4B,CAAC,EAAE,GAAG,CAAC;MACtF;MACAwG,MAAI,CAACvB,UAAU,GAAG,KAAK;MACvBuB,MAAI,CAACxB,eAAe,GAAG,KAAK;MAC5BwB,MAAI,CAACzB,WAAW,GAAG,KAAK;MACxByB,MAAI,CAACtB,UAAU,CAAC/C,OAAO,CAAEsE,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;MAC/CF,MAAI,CAACtB,UAAU,GAAG,EAAE;MACpBsB,MAAI,CAAC1B,QAAQ,GAAG,CAAC;MACjB0B,MAAI,CAACpB,KAAK,GAAG,CAAC,CAAC;IAA8B;EACjD;EACMuB,uBAAuBA,CAAClH,cAAc,EAAEoE,iBAAiB,EAAE;IAAA,IAAA+C,MAAA;IAAA,OAAAnD,iBAAA;MAC7DmD,MAAI,CAACC,kBAAkB,GAAGD,MAAI,CAACR,QAAQ;MACvC,MAAMrE,KAAK,GAAGtC,cAAc,CAACS,UAAU,CAAC4G,gBAAgB,CAAC,KAAK,CAAC;MAC/D,IAAIC,QAAQ,GAAGH,MAAI,CAACR,QAAQ,CAAC9E,YAAY,GAAG,IAAI;MAChD,MAAM0F,SAAS,GAAGjF,KAAK,CAACkF,MAAM;MAC9B3K,SAAS,CAAC,MAAMyF,KAAK,CAACI,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACT,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;MACjFgF,MAAI,CAACP,sBAAsB,GAAG,MAAM;QAChC;QACA,IAAI,CAACO,MAAI,CAAC7B,WAAW,IAAI6B,MAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,+BAA+B;UACrE;QACJ;QACAxI,QAAQ,CAAC,MAAM;UACX;UACA,MAAMsK,SAAS,GAAGN,MAAI,CAACR,QAAQ,CAACc,SAAS;UACzC,MAAMC,eAAe,GAAGP,MAAI,CAACxE,EAAE,CAACd,YAAY;UAC5C,IAAI4F,SAAS,GAAG,CAAC,EAAE;YACf;AACpB;AACA;AACA;YACoB,IAAIN,MAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,iCAAiC;cAClD,MAAMgC,KAAK,GAAGpJ,KAAK,CAAC,CAAC,EAAEkJ,SAAS,IAAIC,eAAe,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;cAC9D7K,SAAS,CAAC,MAAMoF,iBAAiB,CAACmC,iBAAiB,EAAE,CAAC,GAAGuD,KAAK,CAAC,CAAC;cAChE;YACJ;YACA;UACJ;UACA,IAAIR,MAAI,CAAC7B,WAAW,EAAE;YAClB,IAAI,CAAC6B,MAAI,CAAC/B,QAAQ,EAAE;cAChB+B,MAAI,CAAC/B,QAAQ,GAAG,IAAI;cACpB+B,MAAI,CAACjC,QAAQ,CAAC0C,IAAI,CAAC,CAAC;YACxB;YACA;YACA,IAAIT,MAAI,CAAC7B,WAAW,EAAE;cAClB6B,MAAI,CAAClC,OAAO,CAAC2C,IAAI,CAAC,CAAC;YACvB;UACJ;UACA;AAChB;AACA;AACA;AACA;AACA;AACA;UACgB,MAAMzG,MAAM,GAAGgG,MAAI,CAAC/B,QAAQ,GAAG,EAAE,GAAG,CAAC;UACrC,MAAM5C,UAAU,GAAI2E,MAAI,CAAC9B,QAAQ,GAAG9G,KAAK,CAAC,CAAC,EAAE,CAACsJ,IAAI,CAACC,GAAG,CAACL,SAAS,CAAC,GAAGtG,MAAM,IAAImG,QAAQ,EAAE,CAAC,CAAE;UAC3F,MAAMS,2BAA2B,GAAGZ,MAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,mCAAmCnD,UAAU,KAAK,CAAC;UACxG,IAAIuF,2BAA2B,EAAE;YAC7B,IAAIZ,MAAI,CAAC7B,WAAW,EAAE;cAClBtC,2BAA2B,CAACoB,iBAAiB,EAAE+C,MAAI,CAAClE,aAAa,CAAC;YACtE;YACA,IAAI,CAACkE,MAAI,CAAC3B,UAAU,EAAE;cAClB2B,MAAI,CAACa,YAAY,CAAC,CAAC;cACnBb,MAAI,CAAC3B,UAAU,GAAG,IAAI;cACtB9G,YAAY,CAAC;gBAAEwD,KAAK,EAAEvD,WAAW,CAACsJ;cAAM,CAAC,CAAC;cAC1C;AACxB;AACA;AACA;cACwB,IAAI,CAACd,MAAI,CAAC7B,WAAW,EAAE;gBACnBpC,gBAAgB,CAACiE,MAAI,CAACC,kBAAkB,EAAE,GAAGM,eAAe,IAAI,CAAC;cACrE;YACJ;UACJ,CAAC,MACI;YACDP,MAAI,CAACxB,KAAK,GAAG,CAAC,CAAC;YACftD,wBAAwB,CAACC,KAAK,EAAEiF,SAAS,EAAE/E,UAAU,CAAC;UAC1D;QACJ,CAAC,CAAC;MACN,CAAC;MACD2E,MAAI,CAACR,QAAQ,CAACuB,gBAAgB,CAAC,QAAQ,EAAEf,MAAI,CAACP,sBAAsB,CAAC;MACrEO,MAAI,CAAChB,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEgC,aAAa,CAAC;QAC/DxF,EAAE,EAAEwE,MAAI,CAACR,QAAQ;QACjByB,WAAW,EAAE,WAAW;QACxBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAEA,CAAA,KAAM;UACXrB,MAAI,CAAC7B,WAAW,GAAG,IAAI;UACvB,IAAI,CAAC6B,MAAI,CAAC3B,UAAU,EAAE;YAClBtC,gBAAgB,CAACiE,MAAI,CAACC,kBAAkB,EAAE,KAAK,CAAC;UACpD;UACA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;UACgB,IAAIE,QAAQ,KAAK,CAAC,EAAE;YAChBA,QAAQ,GAAGH,MAAI,CAACR,QAAQ,CAAC9E,YAAY,GAAG,IAAI;UAChD;QACJ,CAAC;QACD4G,MAAM,EAAGC,EAAE,IAAK;UACZvB,MAAI,CAAClE,aAAa,GAAGyF,EAAE,CAACC,SAAS;QACrC,CAAC;QACDC,KAAK,EAAEA,CAAA,KAAM;UACTzB,MAAI,CAAC7B,WAAW,GAAG,KAAK;UACxB6B,MAAI,CAAC/B,QAAQ,GAAG,KAAK;UACrB,IAAI+B,MAAI,CAAC5B,eAAe,EAAE;YACtB4B,MAAI,CAACL,oBAAoB,CAACK,MAAI,CAACC,kBAAkB,EAAE,EAAE,CAAC,+BAA+B,CAAC;YACtFD,MAAI,CAAC5B,eAAe,GAAG,KAAK;UAChC,CAAC,MACI,IAAI4B,MAAI,CAAC3B,UAAU,EAAE;YACtBrI,QAAQ,CAAC,MAAM+F,gBAAgB,CAACiE,MAAI,CAACC,kBAAkB,EAAE,GAAGD,MAAI,CAACxE,EAAE,CAACd,YAAY,IAAI,CAAC,CAAC;UAC1F;QACJ;MACJ,CAAC,CAAC;MACFsF,MAAI,CAACjB,eAAe,CAAC,CAAC;IAAC;EAC3B;EACM2C,sBAAsBA,CAACpJ,SAAS,EAAEO,cAAc,EAAEoE,iBAAiB,EAAE;IAAA,IAAA0E,MAAA;IAAA,OAAA9E,iBAAA;MACvE,MAAMxD,MAAM,GAAGhC,cAAc,CAACwB,cAAc,CAAC,CAACO,aAAa,CAAC,QAAQ,CAAC;MACrE,MAAMF,oBAAoB,GAAGyI,MAAI,CAACnG,EAAE,CAACpC,aAAa,CAAC,+CAA+C,CAAC;MACnG,MAAMwI,gBAAgB,GAAGvK,cAAc,CAAC4F,iBAAiB,CAAC,CAAC7D,aAAa,CAAC,QAAQ,CAAC;MAClF,IAAIC,MAAM,KAAK,IAAI,IAAIuI,gBAAgB,KAAK,IAAI,EAAE;QAC9ClM,SAAS,CAAC,MAAM;UACZ2D,MAAM,CAAC0B,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC;UAC7C;UACAiC,iBAAiB,CAAClC,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,QAAQ,CAAC;UAChE4G,gBAAgB,CAAC7G,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,QAAQ,CAAC;QACnE,CAAC,CAAC;MACN;MACA2G,MAAI,CAAC3C,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEgC,aAAa,CAAC;QAC/DxF,EAAE,EAAEmG,MAAI,CAACnC,QAAQ;QACjByB,WAAW,EAAE,WAAW;QACxBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,CAAC;QACZS,QAAQ,EAAEA,CAAA,KAAMF,MAAI,CAACnD,KAAK,KAAK,CAAC,CAAC,mCAC7BmD,MAAI,CAACnD,KAAK,KAAK,EAAE,CAAC,mCAClBmD,MAAI,CAACnC,QAAQ,CAACc,SAAS,KAAK,CAAC;QACjCe,OAAO,EAAGE,EAAE,IAAK;UACbI,MAAI,CAACzD,QAAQ,GAAG,CAAC;UACjBqD,EAAE,CAACO,IAAI,GAAG;YAAEC,SAAS,EAAE3F,SAAS;YAAE6B,QAAQ,EAAE,KAAK;YAAE+D,SAAS,EAAE;UAAM,CAAC;QACzE,CAAC;QACDV,MAAM,EAAGC,EAAE,IAAK;UACZ,IAAKA,EAAE,CAACC,SAAS,GAAG,CAAC,IAAIG,MAAI,CAACzD,QAAQ,KAAK,CAAC,IAAI,CAACqD,EAAE,CAACO,IAAI,CAAC7D,QAAQ,IAAKsD,EAAE,CAACO,IAAI,CAACE,SAAS,EAAE;YACrFT,EAAE,CAACO,IAAI,CAACE,SAAS,GAAG,IAAI;YACxB;UACJ;UACA,IAAI,CAACT,EAAE,CAACO,IAAI,CAAC7D,QAAQ,EAAE;YACnBsD,EAAE,CAACO,IAAI,CAAC7D,QAAQ,GAAG,IAAI;YACvB0D,MAAI,CAACnD,KAAK,GAAG,CAAC,CAAC;YACf;YACA,MAAM;cAAEgB;YAAS,CAAC,GAAGmC,MAAI;YACzB,MAAMM,gBAAgB,GAAGzC,QAAQ,CAAC0C,OAAO,CAACzL,0BAA0B,CAAC,GAAG,UAAU,GAAG,YAAY;YACjGf,SAAS,CAAC,MAAM8J,QAAQ,CAACzE,KAAK,CAACC,WAAW,CAACiH,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YACvE,MAAME,aAAa,GAAG9J,yBAAyB,CAACC,SAAS,CAAC;YAC1D,MAAMyJ,SAAS,GAAGpJ,sBAAsB,CAACwJ,aAAa,EAAEjJ,oBAAoB,EAAEyI,MAAI,CAACnG,EAAE,CAAC;YACtF+F,EAAE,CAACO,IAAI,CAACC,SAAS,GAAGA,SAAS;YAC7BA,SAAS,CAACK,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;YACjCT,MAAI,CAAC5D,QAAQ,CAAC0C,IAAI,CAAC,CAAC;YACpBkB,MAAI,CAACrD,UAAU,CAAC+D,IAAI,CAACN,SAAS,CAAC;YAC/B;UACJ;UACA;UACAJ,MAAI,CAACzD,QAAQ,GAAG9G,KAAK,CAAC,CAAC,EAAGmK,EAAE,CAACe,MAAM,GAAG,GAAG,GAAI,GAAG,EAAE,CAAC,CAAC;UACpDf,EAAE,CAACO,IAAI,CAACC,SAAS,CAACQ,YAAY,CAACZ,MAAI,CAACzD,QAAQ,CAAC;UAC7CyD,MAAI,CAAC7D,OAAO,CAAC2C,IAAI,CAAC,CAAC;QACvB,CAAC;QACDgB,KAAK,EAAGF,EAAE,IAAK;UACX,IAAI,CAACA,EAAE,CAACO,IAAI,CAAC7D,QAAQ,EAAE;YACnB;UACJ;UACA0D,MAAI,CAAC3C,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;UAC1B,MAAM;YAAEO;UAAS,CAAC,GAAGmC,MAAI;UACzB,MAAMM,gBAAgB,GAAGzC,QAAQ,CAAC0C,OAAO,CAACzL,0BAA0B,CAAC,GAAG,UAAU,GAAG,YAAY;UACjGf,SAAS,CAAC,MAAM8J,QAAQ,CAACzE,KAAK,CAACsB,cAAc,CAAC4F,gBAAgB,CAAC,CAAC;UAChE,IAAIN,MAAI,CAACzD,QAAQ,IAAI,GAAG,EAAE;YACtBqD,EAAE,CAACO,IAAI,CAACC,SAAS,CAACS,WAAW,CAAC,CAAC,EAAEb,MAAI,CAACzD,QAAQ,EAAE,GAAG,CAAC,CAACuE,QAAQ,CAAC,MAAM;cAChEd,MAAI,CAACrD,UAAU,CAAC/C,OAAO,CAAEsE,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;cAC/C6B,MAAI,CAACrD,UAAU,GAAG,EAAE;cACpBqD,MAAI,CAAC3C,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;cACzB0C,MAAI,CAACnD,KAAK,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC;YACF;UACJ;UACA,MAAMN,QAAQ,GAAG3H,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEoL,MAAI,CAACzD,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC1F,MAAMwE,iBAAiB,GAAG9H,uBAAuB,CAAC1B,oBAAoB,CAAC;UACvEyI,MAAI,CAACrD,UAAU,CAAC+D,IAAI,CAACK,iBAAiB,CAAC;UACvChN,SAAS,cAAAmH,iBAAA,CAAC,aAAY;YAClB3D,oBAAoB,CAAC6B,KAAK,CAACC,WAAW,CAAC,mCAAmC,EAAE,GAAGkD,QAAQ,GAAG,GAAG,IAAI,CAAC;YAClGqD,EAAE,CAACO,IAAI,CAACC,SAAS,CAACS,WAAW,CAAC,CAAC;YAC/B,MAAME,iBAAiB,CAACC,IAAI,CAAC,CAAC;YAC9BhB,MAAI,CAACd,YAAY,CAAC,CAAC;YACnBU,EAAE,CAACO,IAAI,CAACC,SAAS,CAACjC,OAAO,CAAC,CAAC;YAC3B6B,MAAI,CAAC3C,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;UAC7B,CAAC,EAAC;QACN;MACJ,CAAC,CAAC;MACF0C,MAAI,CAAC5C,eAAe,CAAC,CAAC;IAAC;EAC3B;EACMO,oBAAoBA,CAAChH,SAAS,EAAE;IAAA,IAAAsK,MAAA;IAAA,OAAA/F,iBAAA;MAClC,IAAI+F,MAAI,CAACnD,sBAAsB,IAAI,CAACnH,SAAS,IAAIsK,MAAI,CAACrE,eAAe,IAAI,CAACqE,MAAI,CAACpD,QAAQ,EAAE;QACrF;MACJ;MACA;AACR;AACA;AACA;AACA;AACA;AACA;MACQoD,MAAI,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;MAC7BD,MAAI,CAACrE,eAAe,GAAG,IAAI;MAC3B,MAAM1F,cAAc,GAAG+J,MAAI,CAACpH,EAAE,CAACpC,aAAa,CAAC,sDAAsD,CAAC;MACpG,MAAM6D,iBAAiB,GAAG2F,MAAI,CAACpH,EAAE,CAACpC,aAAa,CAAC,yDAAyD,CAAC;MAC1G,IAAI3B,UAAU,CAACmL,MAAI,CAAC,KAAK,KAAK,EAAE;QAC5BA,MAAI,CAAC7C,uBAAuB,CAAClH,cAAc,EAAEoE,iBAAiB,CAAC;MACnE,CAAC,MACI;QACD2F,MAAI,CAAClB,sBAAsB,CAACpJ,SAAS,EAAEO,cAAc,EAAEoE,iBAAiB,CAAC;MAC7E;IAAC;EACL;EACA6F,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC5D,oBAAoB,CAAC,CAAC;EAC/B;EACM6D,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnG,iBAAA;MACtB,IAAImG,MAAI,CAACxH,EAAE,CAACyH,YAAY,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE;QAC1CC,OAAO,CAACC,KAAK,CAAC,iDAAiD,CAAC;QAChE;MACJ;MACA,MAAM7K,SAAS,GAAG0K,MAAI,CAACxH,EAAE,CAAC6D,OAAO,CAAC1I,4BAA4B,CAAC;MAC/D,IAAI,CAAC2B,SAAS,EAAE;QACZzB,uBAAuB,CAACmM,MAAI,CAACxH,EAAE,CAAC;QAChC;MACJ;MACA;AACR;AACA;AACA;MACQtE,gBAAgB,CAACoB,SAAS,eAAAuE,iBAAA,CAAE,aAAY;QACpC,MAAMuG,kBAAkB,GAAG9K,SAAS,CAACc,aAAa,CAAC3C,0BAA0B,CAAC;QAC9E;AACZ;AACA;AACA;AACA;AACA;QACYuM,MAAI,CAACxD,QAAQ,SAAS1I,gBAAgB,CAACsM,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG9K,SAAS,CAAC;QACrI;AACZ;AACA;QACY0K,MAAI,CAACK,mBAAmB,SAAS/K,SAAS,CAACgL,oBAAoB,CAAC,CAAC;QACjE,UAAU3G,wBAAwB,CAACqG,MAAI,CAACxH,EAAE,EAAE/D,UAAU,CAACuL,MAAI,CAAC,CAAC,EAAE;UAC3DA,MAAI,CAAC1D,oBAAoB,CAAChH,SAAS,CAAC;QACxC,CAAC,MACI;UACD0K,MAAI,CAAChE,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEgC,aAAa,CAAC;YAC/DxF,EAAE,EAAElD,SAAS;YACb2I,WAAW,EAAE,WAAW;YACxBC,eAAe,EAAE,EAAE;YACnBC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,EAAE;YACbmC,OAAO,EAAE,KAAK;YACd1B,QAAQ,EAAEA,CAAA,KAAMmB,MAAI,CAACnB,QAAQ,CAAC,CAAC;YAC/BR,OAAO,EAAEA,CAAA,KAAM2B,MAAI,CAAC3B,OAAO,CAAC,CAAC;YAC7BC,MAAM,EAAGC,EAAE,IAAKyB,MAAI,CAAC1B,MAAM,CAACC,EAAE,CAAC;YAC/BE,KAAK,EAAEA,CAAA,KAAMuB,MAAI,CAACvB,KAAK,CAAC;UAC5B,CAAC,CAAC;UACFuB,MAAI,CAACjE,eAAe,CAAC,CAAC;QAC1B;MACJ,CAAC,EAAC;IAAC;EACP;EACAyE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACjE,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,QAAQ,GAAGpD,SAAS;IACzB,IAAI,IAAI,CAAC4C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACc,OAAO,CAAC,CAAC;MACtB,IAAI,CAACd,OAAO,GAAG5C,SAAS;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUqH,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7G,iBAAA;MACb,IAAI6G,MAAI,CAACnF,eAAe,EAAE;QACtBmF,MAAI,CAACtF,eAAe,GAAG,IAAI;QAC3B;QACA,IAAI,CAACsF,MAAI,CAACvF,WAAW,EAAE;UACnB7G,GAAG,CAAC,MAAMA,GAAG,CAAC,MAAMoM,MAAI,CAAC/D,oBAAoB,CAAC+D,MAAI,CAACzD,kBAAkB,EAAE,EAAE,CAAC,+BAA+B,CAAC,CAAC,CAAC;QAChH;MACJ,CAAC,MACI;QACDyD,MAAI,CAACC,KAAK,CAAC,EAAE,CAAC,iCAAiC,OAAO,CAAC;MAC3D;IAAC;EACL;EACA;AACJ;AACA;EACUC,MAAMA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhH,iBAAA;MACX,IAAIgH,MAAI,CAACtF,eAAe,EAAE;QACtB;QACA,IAAI,CAACsF,MAAI,CAAC1F,WAAW,EAAE;UACnB7G,GAAG,CAAC,MAAMA,GAAG,CAAC,MAAMuM,MAAI,CAAClE,oBAAoB,CAACkE,MAAI,CAAC5D,kBAAkB,EAAE,EAAE,CAAC,+BAA+B,CAAC,CAAC,CAAC;QAChH;MACJ,CAAC,MACI;QACD4D,MAAI,CAACF,KAAK,CAAC,EAAE,CAAC,iCAAiC,EAAE,CAAC;MACtD;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAWA,CAAA,EAAG;IACV,OAAO7H,OAAO,CAACC,OAAO,CAAC,IAAI,CAACgC,QAAQ,CAAC;EACzC;EACA2D,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACrC,QAAQ,EAAE;MAChB,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAAChB,KAAK,KAAK,CAAC,CAAC,+BAA+B;MAChD,OAAO,KAAK;IAChB;IACA;IACA;IACA,IAAI,IAAI,CAACgB,QAAQ,CAACc,SAAS,GAAG,CAAC,EAAE;MAC7B,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAe,OAAOA,CAAA,EAAG;IACN,IAAI,CAACnD,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACuF,oBAAoB,CAAC,CAAC;EAC/B;EACAzC,MAAMA,CAAC0C,MAAM,EAAE;IACX,IAAI,CAAC,IAAI,CAACxE,QAAQ,EAAE;MAChB;IACJ;IACA;IACA;IACA;IACA;IACA,MAAM+B,EAAE,GAAGyC,MAAM,CAACC,KAAK;IACvB,IAAI1C,EAAE,CAAC2C,OAAO,KAAK9H,SAAS,IAAImF,EAAE,CAAC2C,OAAO,CAAC7D,MAAM,GAAG,CAAC,EAAE;MACnD;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC7B,KAAK,GAAG,EAAE,CAAC,iCAAiC,CAAC,EAAE;MACrD;IACJ;IACA,MAAMK,UAAU,GAAGsF,MAAM,CAACC,KAAK,CAAC,IAAI,CAACvF,UAAU,CAAC,IAAI,IAAI,CAACA,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,UAAU;IAC7F,MAAMyD,MAAM,GAAG0B,MAAM,CAAC1B,MAAM,GAAGzD,UAAU;IACzC;IACA;IACA,IAAIyD,MAAM,IAAI,CAAC,EAAE;MACb;MACA;MACA,IAAI,CAACpE,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,IAAI,CAACR,aAAa,EAAE;QACpB;QACA,IAAI,CAAC6E,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;QAC7B;MACJ;MACA;IACJ;IACA,IAAI,IAAI,CAACrE,KAAK,KAAK,CAAC,CAAC,+BAA+B;MAChD;MACA;MACA,MAAM6F,mBAAmB,GAAG,IAAI,CAAC7E,QAAQ,CAACc,SAAS;MACnD;MACA;MACA,IAAI+D,mBAAmB,GAAG,CAAC,EAAE;QACzB,IAAI,CAACnG,QAAQ,GAAG,CAAC;QACjB;MACJ;MACA;MACA,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC;IACnB;IACA;IACA,IAAI+C,EAAE,CAAC+C,UAAU,EAAE;MACf/C,EAAE,CAACgD,cAAc,CAAC,CAAC;IACvB;IACA;IACA;IACA,IAAI,CAAC1B,MAAM,CAACP,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;IACpC,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd;MACA,IAAI,CAACpE,QAAQ,GAAG,CAAC;MACjB;IACJ;IACA,MAAMO,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B;IACA,IAAI,CAACP,QAAQ,GAAGoE,MAAM,GAAG7D,OAAO;IAChC;IACA,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAChB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACF,QAAQ,CAAC0C,IAAI,CAAC,CAAC;IACxB;IACA;IACA,IAAI,CAAC3C,OAAO,CAAC2C,IAAI,CAAC,CAAC;IACnB;IACA,IAAI6B,MAAM,GAAG7D,OAAO,EAAE;MAClB;MACA,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC;MACf;IACJ;IACA,IAAI8D,MAAM,GAAG,IAAI,CAAC5D,OAAO,EAAE;MACvB;MACA,IAAI,CAACmC,YAAY,CAAC,CAAC;MACnB;IACJ;IACA;IACA;IACA;IACA,IAAI,CAACrC,KAAK,GAAG,CAAC,CAAC;IACf;EACJ;EACAiD,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAACjD,KAAK,KAAK,CAAC,CAAC,4BAA4B;MAC7C;MACA,IAAI,CAACqC,YAAY,CAAC,CAAC;IACvB,CAAC,MACI,IAAI,IAAI,CAACrC,KAAK,KAAK,CAAC,CAAC,8BAA8B;MACpD;MACA;MACA;MACA;MACA,IAAI,CAACoF,MAAM,CAAC,CAAC;IACjB,CAAC,MACI,IAAI,IAAI,CAACpF,KAAK,KAAK,CAAC,CAAC,+BAA+B;MACrD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACgG,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA3D,YAAYA,CAAA,EAAG;IACX;IACA;IACA,IAAI,CAACrC,KAAK,GAAG,CAAC,CAAC;IACf;IACA,IAAI,CAACqE,MAAM,CAAC,IAAI,CAACpE,OAAO,EAAE,IAAI,CAACG,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC;IAC1D;IACA;IACA,IAAI,CAACf,UAAU,CAAC4C,IAAI,CAAC;MACjBgD,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACgB,IAAI,CAAC,IAAI;IACrC,CAAC,CAAC;EACN;EACAd,KAAKA,CAACnF,KAAK,EAAEkG,KAAK,EAAE;IAChB;IACAC,UAAU,CAAC,MAAM;MACb,IAAI,CAACnG,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,CAACN,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACD,QAAQ,GAAG,KAAK;MACrB;AACZ;AACA;AACA;MACY,IAAI,CAAC4E,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IAC1C,CAAC,EAAE,GAAG,CAAC;IACP;IACA;IACA,IAAI,CAACrE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAClE,aAAa,EAAE,IAAI,EAAE+F,KAAK,CAAC;EACnD;EACA7B,MAAMA,CAAC+B,CAAC,EAAEjL,QAAQ,EAAEkL,eAAe,EAAEH,KAAK,EAAEI,0BAA0B,GAAG,KAAK,EAAE;IAC5E,IAAI,IAAI,CAACvG,eAAe,EAAE;MACtB;IACJ;IACA,IAAI,CAACP,aAAa,GAAG4G,CAAC,GAAG,CAAC;IAC1BlP,SAAS,CAAC,MAAM;MACZ,IAAI,IAAI,CAAC8J,QAAQ,IAAI,IAAI,CAAC6D,mBAAmB,EAAE;QAC3C,MAAM0B,WAAW,GAAG,IAAI,CAACvF,QAAQ,CAACzE,KAAK;QACvC,MAAMiK,eAAe,GAAG,IAAI,CAAC3B,mBAAmB,CAACtI,KAAK;QACtDgK,WAAW,CAAC1K,SAAS,GAAG2K,eAAe,CAAC3K,SAAS,GAAGuK,CAAC,GAAG,CAAC,GAAG,cAAcA,CAAC,qBAAqB,GAAG,EAAE;QACrGG,WAAW,CAACE,kBAAkB,GAAGD,eAAe,CAACC,kBAAkB,GAAGtL,QAAQ;QAC9EoL,WAAW,CAACG,eAAe,GAAGF,eAAe,CAACE,eAAe,GAAGR,KAAK;QACrEK,WAAW,CAACI,QAAQ,GAAGN,eAAe,GAAG,QAAQ,GAAG,EAAE;MAC1D;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIC,0BAA0B,EAAE;QAC5B,IAAI,CAACN,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC;EACN;EACAT,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACvE,QAAQ,EAAE;MACf,MAAM;QAAE2F,QAAQ;QAAEC,SAAS;QAAEC;MAAU,CAAC,GAAG,IAAI,CAAC7F,QAAQ,CAACzE,KAAK;MAC9D,IAAI,CAACuK,cAAc,GAAG;QAClBH,QAAQ,EAAEA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,EAAE;QAClEC,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE;QACtEC,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG;MACxE,CAAC;IACL;EACJ;EACAb,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACc,cAAc,KAAKlJ,SAAS,IAAI,IAAI,CAACoD,QAAQ,KAAKpD,SAAS,EAAE;MAClE,MAAM;QAAE+I,QAAQ;QAAEC,SAAS;QAAEC;MAAU,CAAC,GAAG,IAAI,CAACC,cAAc;MAC9D,IAAI,CAAC9F,QAAQ,CAACzE,KAAK,CAACoK,QAAQ,GAAGA,QAAQ;MACvC,IAAI,CAAC3F,QAAQ,CAACzE,KAAK,CAACqK,SAAS,GAAGA,SAAS;MACzC,IAAI,CAAC5F,QAAQ,CAACzE,KAAK,CAACsK,SAAS,GAAGA,SAAS;MACzC,IAAI,CAACC,cAAc,GAAGlJ,SAAS;IACnC;EACJ;EACAmJ,MAAMA,CAAA,EAAG;IACL,MAAMxI,IAAI,GAAGtF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQxB,CAAC,CAACI,IAAI,EAAE;MAAEmP,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE;QACjF,CAAC3I,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,aAAaA,IAAI,EAAE,GAAG,IAAI;QAC3B,kBAAkB,EAAE,IAAI,CAACwB,eAAe;QACxC,kBAAkB,EAAE,IAAI,CAACC,KAAK,KAAK,CAAC,CAAC;QACrC,mBAAmB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACtC,iBAAiB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACpC,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACzC,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,EAAE,CAAC;QAC1C,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,EAAE,CAAC;MAC9C;IAAE,CAAC,CAAC;EACZ;EACA,IAAIhD,EAAEA,CAAA,EAAG;IAAE,OAAOrF,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwP,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDjI,SAAS,CAAC3C,KAAK,GAAG;EACd6K,GAAG,EAAErI,qBAAqB;EAC1BsI,EAAE,EAAEpI;AACR,CAAC;AAED,MAAMqI,gBAAgB,GAAG,MAAM;EAC3BnI,WAAWA,CAACC,OAAO,EAAE;IACjBhI,gBAAgB,CAAC,IAAI,EAAEgI,OAAO,CAAC;IAC/B,IAAI,CAACmI,iBAAiB,GAAGrO,MAAM,CAACsO,GAAG,CAAC,2BAA2B,EAAEnO,2BAA2B,CAAC;IAC7F,IAAI,CAACoO,WAAW,GAAG7J,SAAS;IAC5B,IAAI,CAAC8J,WAAW,GAAG9J,SAAS;IAC5B,IAAI,CAACa,iBAAiB,GAAGb,SAAS;IAClC,IAAI,CAAC+J,cAAc,GAAG/J,SAAS;EACnC;EACAgK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACH,WAAW,KAAK7J,SAAS,EAAE;MAChC;AACZ;AACA;AACA;AACA;MACY,MAAMiK,sBAAsB,GAAG/J,2BAA2B,CAAC,CAAC;MAC5D,MAAMS,IAAI,GAAGtF,UAAU,CAAC,IAAI,CAAC;MAC7B,MAAM6O,iBAAiB,GAAGD,sBAAsB,GAAG,OAAO,GAAGnO,SAAS;MACtE,IAAI,CAAC+N,WAAW,GAAGvO,MAAM,CAACsO,GAAG,CAAC,gBAAgB,EAAEjJ,IAAI,KAAK,KAAK,IAAIsJ,sBAAsB,GAAG3O,MAAM,CAACsO,GAAG,CAAC,SAAS,EAAEM,iBAAiB,CAAC,GAAG,UAAU,CAAC;IACrJ;IACA,IAAI,IAAI,CAACrJ,iBAAiB,KAAKb,SAAS,EAAE;MACtC,MAAMW,IAAI,GAAGtF,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACwF,iBAAiB,GAAGvF,MAAM,CAACsO,GAAG,CAAC,mBAAmB,EAAEtO,MAAM,CAACsO,GAAG,CAAC,SAAS,EAAEjJ,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC;IAC1H;EACJ;EACAwJ,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAER,iBAAiB;MAAEG;IAAY,CAAC,GAAG,IAAI;IAC/C,IAAIH,iBAAiB,EAAE;MACnB,OAAO9P,CAAC,CAAC,KAAK,EAAE;QAAEyP,KAAK,EAAE,wBAAwB;QAAEc,SAAS,EAAEzO,iBAAiB,CAACmO,WAAW;MAAE,CAAC,CAAC;IACnG;IACA,OAAOjQ,CAAC,CAAC,KAAK,EAAE;MAAEyP,KAAK,EAAE;IAAyB,CAAC,EAAEQ,WAAW,CAAC;EACrE;EACAO,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAEV,iBAAiB;MAAEI;IAAe,CAAC,GAAG,IAAI;IAClD,IAAIJ,iBAAiB,EAAE;MACnB,OAAO9P,CAAC,CAAC,KAAK,EAAE;QAAEyP,KAAK,EAAE,2BAA2B;QAAEc,SAAS,EAAEzO,iBAAiB,CAACoO,cAAc;MAAE,CAAC,CAAC;IACzG;IACA,OAAOlQ,CAAC,CAAC,KAAK,EAAE;MAAEyP,KAAK,EAAE;IAA4B,CAAC,EAAES,cAAc,CAAC;EAC3E;EACAZ,MAAMA,CAAA,EAAG;IACL,MAAMU,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMS,UAAU,GAAGT,WAAW,IAAI,IAAI,IAAI7N,QAAQ,CAAC6N,WAAW,CAAC,KAAK7J,SAAS;IAC7E,MAAMW,IAAI,GAAGtF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQxB,CAAC,CAACI,IAAI,EAAE;MAAEmP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE3I;IAAK,CAAC,EAAE9G,CAAC,CAAC,KAAK,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAoB,CAAC,EAAE,IAAI,CAACO,WAAW,IAAIS,UAAU,IAAKzQ,CAAC,CAAC,KAAK,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAyB,CAAC,EAAEzP,CAAC,CAAC,KAAK,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAA0B,CAAC,EAAEzP,CAAC,CAAC,aAAa,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEmB,IAAI,EAAE,IAAI,CAACV,WAAW;MAAEW,MAAM,EAAE;IAAK,CAAC,CAAC,EAAE7J,IAAI,KAAK,IAAI,IAAI,IAAI,CAACkJ,WAAW,KAAK,UAAU,IAAKhQ,CAAC,CAAC,KAAK,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAkB,CAAC,EAAEzP,CAAC,CAAC,UAAU,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEqB,IAAI,EAAE7O,cAAc;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAE,CAAC,CAAE,EAAE,IAAI,CAACiO,WAAW,IAAI,CAACS,UAAU,IAAKzQ,CAAC,CAAC,KAAK,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAyB,CAAC,EAAEzP,CAAC,CAAC,UAAU,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEqB,IAAI,EAAE,IAAI,CAACZ,WAAW;MAAEa,IAAI,EAAE,KAAK;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAACZ,WAAW,KAAK9J,SAAS,IAAI,IAAI,CAACmK,iBAAiB,CAAC,CAAC,CAAC,EAAEtQ,CAAC,CAAC,KAAK,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAuB,CAAC,EAAE,IAAI,CAACzI,iBAAiB,IAAKhH,CAAC,CAAC,KAAK,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAA4B,CAAC,EAAEzP,CAAC,CAAC,aAAa,EAAE;MAAEuP,GAAG,EAAE,0CAA0C;MAAEmB,IAAI,EAAE,IAAI,CAAC1J;IAAkB,CAAC,CAAC,CAAE,EAAE,IAAI,CAACkJ,cAAc,KAAK/J,SAAS,IAAI,IAAI,CAACqK,oBAAoB,CAAC,CAAC,CAAC,CAAC;EACv8C;EACA,IAAIjL,EAAEA,CAAA,EAAG;IAAE,OAAOrF,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AAED,SAASuH,SAAS,IAAIqJ,aAAa,EAAEjB,gBAAgB,IAAIkB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}