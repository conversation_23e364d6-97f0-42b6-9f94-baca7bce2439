{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/auth.service\";\nimport * as i2 from \"../../../core/services/cart.service\";\nimport * as i3 from \"../../../core/services/wishlist-new.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction HeaderComponent_div_23_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.getTotalItemCount());\n  }\n}\nfunction HeaderComponent_div_23_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_23_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"span\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFormattedCartTotal());\n  }\n}\nfunction HeaderComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Total Items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HeaderComponent_div_23_span_4_Template, 2, 1, \"span\", 23)(5, HeaderComponent_div_23_span_5_Template, 2, 0, \"span\", 24)(6, HeaderComponent_div_23_div_6_Template, 3, 1, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getTotalItemCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getTotalItemCount() === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.shouldShowCartTotalPrice());\n  }\n}\nfunction HeaderComponent_div_32_a_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \" Vendor Dashboard \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_32_a_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \" Admin Panel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_32_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 39);\n  }\n}\nfunction HeaderComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_32_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleUserMenu());\n    });\n    i0.ɵɵelement(1, \"img\", 31);\n    i0.ɵɵelementStart(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 33);\n    i0.ɵɵelementStart(5, \"div\", 34)(6, \"a\", 35);\n    i0.ɵɵelement(7, \"i\", 36);\n    i0.ɵɵtext(8, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 37);\n    i0.ɵɵelement(10, \"i\", 38);\n    i0.ɵɵtext(11, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"div\", 39);\n    i0.ɵɵtemplate(13, HeaderComponent_div_32_a_13_Template, 3, 0, \"a\", 40)(14, HeaderComponent_div_32_a_14_Template, 3, 0, \"a\", 41)(15, HeaderComponent_div_32_div_15_Template, 1, 0, \"div\", 42);\n    i0.ɵɵelementStart(16, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_32_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵelement(17, \"i\", 44);\n    i0.ɵɵtext(18, \" Logout \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"show\", ctx_r0.showUserMenu);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"vendor\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"admin\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role !== \"customer\");\n  }\n}\nfunction HeaderComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"a\", 50);\n    i0.ɵɵtext(2, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 51);\n    i0.ɵɵtext(4, \"Sign Up\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HeaderComponent {\n  constructor(authService, cartService, wishlistService, router) {\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.currentUser = null;\n    this.searchQuery = '';\n    this.showUserMenu = false;\n    this.cartItemCount = 0;\n    this.wishlistItemCount = 0;\n    this.totalItemCount = 0;\n    this.cartTotalAmount = 0;\n    this.showCartTotalPrice = false;\n  }\n  ngOnInit() {\n    // Subscribe to user changes and refresh counts on login\n    this.authService.currentUser$.subscribe(user => {\n      const wasLoggedOut = !this.currentUser;\n      this.currentUser = user;\n      // If user just logged in, refresh cart and wishlist\n      if (user && wasLoggedOut) {\n        console.log('🔄 User logged in, refreshing cart and wishlist...');\n        setTimeout(() => {\n          this.cartService.refreshCartOnLogin();\n          this.wishlistService.refreshWishlistOnLogin();\n        }, 100);\n      }\n    });\n    // Subscribe to cart count\n    this.cartService.cartItemCount$.subscribe(count => {\n      this.cartItemCount = count;\n      this.updateTotalCount();\n      console.log('🛒 Header cart count updated:', count);\n    });\n    // Subscribe to wishlist count\n    this.wishlistService.wishlistItemCount$.subscribe(count => {\n      this.wishlistItemCount = count;\n      this.updateTotalCount();\n      console.log('💝 Header wishlist count updated:', count);\n    });\n    // Subscribe to cart total amount\n    this.cartService.cartTotalAmount$.subscribe(amount => {\n      this.cartTotalAmount = amount;\n      console.log('💰 Header cart total amount updated:', amount);\n    });\n    // Subscribe to cart price display flag\n    this.cartService.showCartTotalPrice$.subscribe(showPrice => {\n      this.showCartTotalPrice = showPrice;\n      console.log('💲 Header show cart total price updated:', showPrice);\n    });\n    // Load cart and wishlist on init\n    this.cartService.loadCart();\n    this.wishlistService.loadWishlist();\n    // Close dropdown when clicking outside\n    document.addEventListener('click', event => {\n      const target = event.target;\n      if (!target.closest('.user-menu')) {\n        this.showUserMenu = false;\n      }\n    });\n  }\n  toggleUserMenu() {\n    this.showUserMenu = !this.showUserMenu;\n  }\n  openSearch() {\n    this.router.navigate(['/search']);\n  }\n  // Update total count\n  updateTotalCount() {\n    this.totalItemCount = (this.cartItemCount || 0) + (this.wishlistItemCount || 0);\n  }\n  // Get total count for display\n  getTotalItemCount() {\n    return this.totalItemCount;\n  }\n  // Get formatted cart total amount\n  getFormattedCartTotal() {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.cartTotalAmount || 0);\n  }\n  // Check if cart total price should be displayed\n  shouldShowCartTotalPrice() {\n    return this.currentUser !== null && this.showCartTotalPrice;\n  }\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.searchQuery\n        }\n      });\n      this.searchQuery = ''; // Clear search after navigation\n    } else {\n      this.router.navigate(['/search']);\n    }\n  }\n  logout() {\n    this.authService.logout();\n    this.showUserMenu = false;\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.WishlistNewService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 34,\n      vars: 4,\n      consts: [[1, \"header\"], [1, \"container\"], [1, \"header-content\"], [1, \"logo\"], [\"routerLink\", \"/home\"], [1, \"gradient-text\"], [1, \"search-bar\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"Search for fashion, brands, and more...\", \"readonly\", \"\", 3, \"ngModelChange\", \"keyup.enter\", \"click\", \"ngModel\"], [1, \"nav-menu\"], [\"routerLink\", \"/home\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/shop\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-compass\"], [1, \"fas\", \"fa-shopping-bag\"], [\"class\", \"total-count-item\", 4, \"ngIf\"], [\"routerLink\", \"/wishlist\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-heart\"], [\"routerLink\", \"/cart\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"user-menu\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"auth-buttons\", 4, \"ngIf\"], [1, \"total-count-item\"], [\"class\", \"total-count-badge\", 4, \"ngIf\"], [\"class\", \"total-count-badge zero\", 4, \"ngIf\"], [\"class\", \"cart-total-display\", 4, \"ngIf\"], [1, \"total-count-badge\"], [1, \"total-count-badge\", \"zero\"], [1, \"cart-total-display\"], [1, \"cart-total-text\"], [1, \"user-menu\", 3, \"click\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"username\"], [1, \"fas\", \"fa-chevron-down\"], [1, \"dropdown-menu\"], [\"routerLink\", \"/profile\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-user\"], [\"routerLink\", \"/settings\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-cog\"], [1, \"dropdown-divider\"], [\"routerLink\", \"/vendor/dashboard\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"routerLink\", \"/admin\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"class\", \"dropdown-divider\", 4, \"ngIf\"], [1, \"dropdown-item\", \"logout\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [\"routerLink\", \"/vendor/dashboard\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-store\"], [\"routerLink\", \"/admin\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"auth-buttons\"], [\"routerLink\", \"/auth/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/auth/register\", 1, \"btn\", \"btn-primary\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"h1\", 5);\n          i0.ɵɵtext(6, \"DFashion\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_div_click_7_listener() {\n            return ctx.openSearch();\n          });\n          i0.ɵɵelement(8, \"i\", 7);\n          i0.ɵɵelementStart(9, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HeaderComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function HeaderComponent_Template_input_keyup_enter_9_listener() {\n            return ctx.onSearch();\n          })(\"click\", function HeaderComponent_Template_input_click_9_listener() {\n            return ctx.openSearch();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"nav\", 9)(11, \"a\", 10);\n          i0.ɵɵelement(12, \"i\", 11);\n          i0.ɵɵelementStart(13, \"span\");\n          i0.ɵɵtext(14, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"a\", 12);\n          i0.ɵɵelement(16, \"i\", 13);\n          i0.ɵɵelementStart(17, \"span\");\n          i0.ɵɵtext(18, \"Explore\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"a\", 12);\n          i0.ɵɵelement(20, \"i\", 14);\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, HeaderComponent_div_23_Template, 7, 3, \"div\", 15);\n          i0.ɵɵelementStart(24, \"a\", 16);\n          i0.ɵɵelement(25, \"i\", 17);\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Wishlist\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"a\", 18);\n          i0.ɵɵelement(29, \"i\", 19);\n          i0.ɵɵelementStart(30, \"span\");\n          i0.ɵɵtext(31, \"Cart\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(32, HeaderComponent_div_32_Template, 19, 8, \"div\", 20)(33, HeaderComponent_div_33_Template, 5, 0, \"div\", 21);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentUser);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, RouterModule, i4.RouterLink, i4.RouterLinkActive, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\".header[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-bottom: 1px solid #dbdbdb;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  height: 60px;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 60px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1;\\n  max-width: 400px;\\n  margin: 0 40px;\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 16px 8px 40px;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  background: #fafafa;\\n  font-size: 14px;\\n  outline: none;\\n  transition: all 0.2s;\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  background: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #8e8e8e;\\n}\\n\\n.nav-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-decoration: none;\\n  color: #262626;\\n  font-size: 12px;\\n  transition: color 0.2s;\\n  padding: 8px;\\n  border-radius: 4px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 4px;\\n}\\n\\n.nav-item.active[_ngcontent-%COMP%], .nav-item[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n}\\n\\n.cart-item[_ngcontent-%COMP%], .wishlist-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.cart-badge[_ngcontent-%COMP%], .wishlist-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  background: #ef4444;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.cart-total-display[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: #28a745;\\n  color: white;\\n  font-size: 9px;\\n  font-weight: 600;\\n  padding: 2px 4px;\\n  border-radius: 4px;\\n  white-space: nowrap;\\n  margin-top: 2px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.cart-total-text[_ngcontent-%COMP%] {\\n  font-size: 9px;\\n}\\n\\n.total-count-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background: linear-gradient(135deg, #4834d4, #686de0);\\n  color: white;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  margin-left: 8px;\\n}\\n\\n.total-count-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.total-count-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  min-width: 16px;\\n  text-align: center;\\n}\\n\\n.auth-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.2s;\\n  border: 1px solid transparent;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  border-color: var(--primary-color);\\n  background: transparent;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-dark);\\n}\\n\\n.user-menu[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: background 0.2s;\\n}\\n\\n.user-menu[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n\\n.user-menu[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #64748b;\\n  transition: transform 0.2s;\\n}\\n\\n.user-menu.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: #fff;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  min-width: 200px;\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.2s;\\n  z-index: 1000;\\n}\\n\\n.dropdown-menu.show[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 16px;\\n  text-decoration: none;\\n  color: #262626;\\n  font-size: 14px;\\n  transition: background 0.2s;\\n  border: none;\\n  background: none;\\n  width: 100%;\\n  text-align: left;\\n  cursor: pointer;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n}\\n\\n.dropdown-item.logout[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n\\n.dropdown-item.logout[_ngcontent-%COMP%]:hover {\\n  background: #fef2f2;\\n}\\n\\n.dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: #e2e8f0;\\n  margin: 8px 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .search-bar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-menu[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .username[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .cart-total-display[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    padding: 1px 3px;\\n  }\\n  .cart-total-text[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n  .total-count-display[_ngcontent-%COMP%] {\\n    padding: 3px 6px;\\n    font-size: 10px;\\n    margin-left: 4px;\\n  }\\n  .total-count-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .total-count-text[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    min-width: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getTotalItemCount", "getFormattedCartTotal", "ɵɵelement", "ɵɵtemplate", "HeaderComponent_div_23_span_4_Template", "HeaderComponent_div_23_span_5_Template", "HeaderComponent_div_23_div_6_Template", "ɵɵproperty", "shouldShowCartTotalPrice", "ɵɵlistener", "HeaderComponent_div_32_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "toggleUserMenu", "HeaderComponent_div_32_a_13_Template", "HeaderComponent_div_32_a_14_Template", "HeaderComponent_div_32_div_15_Template", "HeaderComponent_div_32_Template_button_click_16_listener", "logout", "currentUser", "avatar", "ɵɵsanitizeUrl", "fullName", "username", "ɵɵclassProp", "showUserMenu", "role", "HeaderComponent", "constructor", "authService", "cartService", "wishlistService", "router", "searchQuery", "cartItemCount", "wishlistItemCount", "totalItemCount", "cartTotalAmount", "showCartTotalPrice", "ngOnInit", "currentUser$", "subscribe", "user", "wasLoggedOut", "console", "log", "setTimeout", "refreshCartOnLogin", "refreshWishlistOnLogin", "cartItemCount$", "count", "updateTotalCount", "wishlistItemCount$", "cartTotalAmount$", "amount", "showCartTotalPrice$", "showPrice", "loadCart", "loadWishlist", "document", "addEventListener", "event", "target", "closest", "openSearch", "navigate", "Intl", "NumberFormat", "style", "currency", "format", "onSearch", "trim", "queryParams", "q", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "CartService", "i3", "WishlistNewService", "i4", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "HeaderComponent_Template_div_click_7_listener", "ɵɵtwoWayListener", "HeaderComponent_Template_input_ngModelChange_9_listener", "$event", "ɵɵtwoWayBindingSet", "HeaderComponent_Template_input_keyup_enter_9_listener", "HeaderComponent_Template_input_click_9_listener", "HeaderComponent_div_23_Template", "HeaderComponent_div_32_Template", "HeaderComponent_div_33_Template", "ɵɵtwoWayProperty", "i5", "NgIf", "RouterLink", "RouterLinkActive", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\shared\\components\\header\\header.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\nimport { AuthService } from '../../../core/services/auth.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistNewService } from '../../../core/services/wishlist-new.service';\nimport { User } from '../../../core/models/user.model';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule],\n  template: `\n    <header class=\"header\">\n      <div class=\"container\">\n        <div class=\"header-content\">\n          <!-- Logo -->\n          <div class=\"logo\">\n            <a routerLink=\"/home\">\n              <h1 class=\"gradient-text\">DFashion</h1>\n            </a>\n          </div>\n\n          <!-- Search Bar -->\n          <div class=\"search-bar\" (click)=\"openSearch()\">\n            <i class=\"fas fa-search\"></i>\n            <input\n              type=\"text\"\n              placeholder=\"Search for fashion, brands, and more...\"\n              [(ngModel)]=\"searchQuery\"\n              (keyup.enter)=\"onSearch()\"\n              (click)=\"openSearch()\"\n              readonly\n            >\n          </div>\n\n          <!-- Navigation -->\n          <nav class=\"nav-menu\">\n            <a routerLink=\"/home\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-home\"></i>\n              <span>Home</span>\n            </a>\n            <a routerLink=\"/shop\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-compass\"></i>\n              <span>Explore</span>\n            </a>\n            <a routerLink=\"/shop\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-shopping-bag\"></i>\n              <span>Shop</span>\n            </a>\n            <!-- Total Count Display (Cart + Wishlist) -->\n            <div class=\"total-count-item\" *ngIf=\"currentUser\">\n              <i class=\"fas fa-shopping-bag\"></i>\n              <span>Total Items</span>\n              <span class=\"total-count-badge\" *ngIf=\"getTotalItemCount() > 0\">{{ getTotalItemCount() }}</span>\n              <span class=\"total-count-badge zero\" *ngIf=\"getTotalItemCount() === 0\">0</span>\n\n              <!-- Cart total price display when cart has 4+ items -->\n              <div class=\"cart-total-display\" *ngIf=\"shouldShowCartTotalPrice()\">\n                <span class=\"cart-total-text\">{{ getFormattedCartTotal() }}</span>\n              </div>\n            </div>\n\n            <a routerLink=\"/wishlist\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-heart\"></i>\n              <span>Wishlist</span>\n            </a>\n            <a routerLink=\"/cart\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-shopping-cart\"></i>\n              <span>Cart</span>\n            </a>\n\n            <!-- User Menu for logged in users -->\n            <div *ngIf=\"currentUser\" class=\"user-menu\" (click)=\"toggleUserMenu()\">\n              <img [src]=\"currentUser.avatar\" [alt]=\"currentUser.fullName\" class=\"user-avatar\">\n              <span class=\"username\">{{ currentUser.username }}</span>\n              <i class=\"fas fa-chevron-down\"></i>\n              \n              <!-- Dropdown Menu -->\n              <div class=\"dropdown-menu\" [class.show]=\"showUserMenu\">\n                <a routerLink=\"/profile\" class=\"dropdown-item\">\n                  <i class=\"fas fa-user\"></i>\n                  Profile\n                </a>\n                <a routerLink=\"/settings\" class=\"dropdown-item\">\n                  <i class=\"fas fa-cog\"></i>\n                  Settings\n                </a>\n                <div class=\"dropdown-divider\"></div>\n                <a *ngIf=\"currentUser.role === 'vendor'\" routerLink=\"/vendor/dashboard\" class=\"dropdown-item\">\n                  <i class=\"fas fa-store\"></i>\n                  Vendor Dashboard\n                </a>\n                <a *ngIf=\"currentUser.role === 'admin'\" routerLink=\"/admin\" class=\"dropdown-item\">\n                  <i class=\"fas fa-shield-alt\"></i>\n                  Admin Panel\n                </a>\n                <div class=\"dropdown-divider\" *ngIf=\"currentUser.role !== 'customer'\"></div>\n                <button (click)=\"logout()\" class=\"dropdown-item logout\">\n                  <i class=\"fas fa-sign-out-alt\"></i>\n                  Logout\n                </button>\n              </div>\n            </div>\n\n            <!-- Login/Register for guest users -->\n            <div *ngIf=\"!currentUser\" class=\"auth-buttons\">\n              <a routerLink=\"/auth/login\" class=\"btn btn-outline\">Login</a>\n              <a routerLink=\"/auth/register\" class=\"btn btn-primary\">Sign Up</a>\n            </div>\n          </nav>\n        </div>\n      </div>\n    </header>\n  `,\n  styles: [`\n    .header {\n      background: #fff;\n      border-bottom: 1px solid #dbdbdb;\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 1000;\n      height: 60px;\n    }\n\n    .header-content {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      height: 60px;\n    }\n\n    .logo a {\n      text-decoration: none;\n    }\n\n    .logo h1 {\n      font-size: 24px;\n      font-weight: 700;\n      margin: 0;\n    }\n\n    .search-bar {\n      position: relative;\n      flex: 1;\n      max-width: 400px;\n      margin: 0 40px;\n    }\n\n    .search-bar input {\n      width: 100%;\n      padding: 8px 16px 8px 40px;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      background: #fafafa;\n      font-size: 14px;\n      outline: none;\n      transition: all 0.2s;\n    }\n\n    .search-bar input:focus {\n      background: #fff;\n      border-color: var(--primary-color);\n    }\n\n    .search-bar i {\n      position: absolute;\n      left: 12px;\n      top: 50%;\n      transform: translateY(-50%);\n      color: #8e8e8e;\n    }\n\n    .nav-menu {\n      display: flex;\n      align-items: center;\n      gap: 24px;\n    }\n\n    .nav-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-decoration: none;\n      color: #262626;\n      font-size: 12px;\n      transition: color 0.2s;\n      padding: 8px;\n      border-radius: 4px;\n    }\n\n    .nav-item i {\n      font-size: 20px;\n      margin-bottom: 4px;\n    }\n\n    .nav-item.active,\n    .nav-item:hover {\n      color: var(--primary-color);\n    }\n\n    .cart-item,\n    .wishlist-item {\n      position: relative;\n    }\n\n    .cart-badge,\n    .wishlist-badge {\n      position: absolute;\n      top: -2px;\n      right: -2px;\n      background: #ef4444;\n      color: white;\n      font-size: 10px;\n      font-weight: 600;\n      padding: 2px 6px;\n      border-radius: 10px;\n      min-width: 16px;\n      text-align: center;\n      line-height: 1.2;\n    }\n\n    .cart-total-display {\n      position: absolute;\n      top: 100%;\n      right: 0;\n      background: #28a745;\n      color: white;\n      font-size: 9px;\n      font-weight: 600;\n      padding: 2px 4px;\n      border-radius: 4px;\n      white-space: nowrap;\n      margin-top: 2px;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .cart-total-text {\n      font-size: 9px;\n    }\n\n    .total-count-display {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      padding: 4px 8px;\n      background: linear-gradient(135deg, #4834d4, #686de0);\n      color: white;\n      border-radius: 12px;\n      font-size: 12px;\n      font-weight: 600;\n      margin-left: 8px;\n    }\n\n    .total-count-display i {\n      font-size: 12px;\n    }\n\n    .total-count-text {\n      font-size: 12px;\n      min-width: 16px;\n      text-align: center;\n    }\n\n    .auth-buttons {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .btn {\n      padding: 8px 16px;\n      border-radius: 6px;\n      text-decoration: none;\n      font-size: 14px;\n      font-weight: 500;\n      transition: all 0.2s;\n      border: 1px solid transparent;\n    }\n\n    .btn-outline {\n      color: var(--primary-color);\n      border-color: var(--primary-color);\n      background: transparent;\n    }\n\n    .btn-outline:hover {\n      background: var(--primary-color);\n      color: white;\n    }\n\n    .btn-primary {\n      background: var(--primary-color);\n      color: white;\n    }\n\n    .btn-primary:hover {\n      background: var(--primary-dark);\n    }\n\n    .user-menu {\n      position: relative;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      cursor: pointer;\n      padding: 8px 12px;\n      border-radius: 8px;\n      transition: background 0.2s;\n    }\n\n    .user-menu:hover {\n      background: #f1f5f9;\n    }\n\n    .user-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .username {\n      font-weight: 500;\n      font-size: 14px;\n    }\n\n    .user-menu i {\n      font-size: 12px;\n      color: #64748b;\n      transition: transform 0.2s;\n    }\n\n    .user-menu.active i {\n      transform: rotate(180deg);\n    }\n\n    .dropdown-menu {\n      position: absolute;\n      top: 100%;\n      right: 0;\n      background: #fff;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      min-width: 200px;\n      opacity: 0;\n      visibility: hidden;\n      transform: translateY(-10px);\n      transition: all 0.2s;\n      z-index: 1000;\n    }\n\n    .dropdown-menu.show {\n      opacity: 1;\n      visibility: visible;\n      transform: translateY(0);\n    }\n\n    .dropdown-item {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 16px;\n      text-decoration: none;\n      color: #262626;\n      font-size: 14px;\n      transition: background 0.2s;\n      border: none;\n      background: none;\n      width: 100%;\n      text-align: left;\n      cursor: pointer;\n    }\n\n    .dropdown-item:hover {\n      background: #f8fafc;\n    }\n\n    .dropdown-item.logout {\n      color: #ef4444;\n    }\n\n    .dropdown-item.logout:hover {\n      background: #fef2f2;\n    }\n\n    .dropdown-divider {\n      height: 1px;\n      background: #e2e8f0;\n      margin: 8px 0;\n    }\n\n    @media (max-width: 768px) {\n      .search-bar {\n        display: none;\n      }\n\n      .nav-menu {\n        gap: 16px;\n      }\n\n      .nav-item span {\n        display: none;\n      }\n\n      .username {\n        display: none;\n      }\n\n      .cart-total-display {\n        font-size: 8px;\n        padding: 1px 3px;\n      }\n\n      .cart-total-text {\n        font-size: 8px;\n      }\n\n      .total-count-display {\n        padding: 3px 6px;\n        font-size: 10px;\n        margin-left: 4px;\n      }\n\n      .total-count-display i {\n        font-size: 10px;\n      }\n\n      .total-count-text {\n        font-size: 10px;\n        min-width: 12px;\n      }\n    }\n  `]\n})\nexport class HeaderComponent implements OnInit {\n  currentUser: User | null = null;\n  searchQuery = '';\n  showUserMenu = false;\n  cartItemCount = 0;\n  wishlistItemCount = 0;\n  totalItemCount = 0;\n  cartTotalAmount = 0;\n  showCartTotalPrice = false;\n\n  constructor(\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistNewService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to user changes and refresh counts on login\n    this.authService.currentUser$.subscribe(user => {\n      const wasLoggedOut = !this.currentUser;\n      this.currentUser = user;\n\n      // If user just logged in, refresh cart and wishlist\n      if (user && wasLoggedOut) {\n        console.log('🔄 User logged in, refreshing cart and wishlist...');\n        setTimeout(() => {\n          this.cartService.refreshCartOnLogin();\n          this.wishlistService.refreshWishlistOnLogin();\n        }, 100);\n      }\n    });\n\n    // Subscribe to cart count\n    this.cartService.cartItemCount$.subscribe((count: number) => {\n      this.cartItemCount = count;\n      this.updateTotalCount();\n      console.log('🛒 Header cart count updated:', count);\n    });\n\n    // Subscribe to wishlist count\n    this.wishlistService.wishlistItemCount$.subscribe((count: number) => {\n      this.wishlistItemCount = count;\n      this.updateTotalCount();\n      console.log('💝 Header wishlist count updated:', count);\n    });\n\n    // Subscribe to cart total amount\n    this.cartService.cartTotalAmount$.subscribe((amount: number) => {\n      this.cartTotalAmount = amount;\n      console.log('💰 Header cart total amount updated:', amount);\n    });\n\n    // Subscribe to cart price display flag\n    this.cartService.showCartTotalPrice$.subscribe((showPrice: boolean) => {\n      this.showCartTotalPrice = showPrice;\n      console.log('💲 Header show cart total price updated:', showPrice);\n    });\n\n    // Load cart and wishlist on init\n    this.cartService.loadCart();\n    this.wishlistService.loadWishlist();\n\n    // Close dropdown when clicking outside\n    document.addEventListener('click', (event) => {\n      const target = event.target as HTMLElement;\n      if (!target.closest('.user-menu')) {\n        this.showUserMenu = false;\n      }\n    });\n  }\n\n  toggleUserMenu() {\n    this.showUserMenu = !this.showUserMenu;\n  }\n\n  openSearch() {\n    this.router.navigate(['/search']);\n  }\n\n  // Update total count\n  private updateTotalCount() {\n    this.totalItemCount = (this.cartItemCount || 0) + (this.wishlistItemCount || 0);\n  }\n\n  // Get total count for display\n  getTotalItemCount(): number {\n    return this.totalItemCount;\n  }\n\n  // Get formatted cart total amount\n  getFormattedCartTotal(): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.cartTotalAmount || 0);\n  }\n\n  // Check if cart total price should be displayed\n  shouldShowCartTotalPrice(): boolean {\n    return this.currentUser !== null && this.showCartTotalPrice;\n  }\n\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: { q: this.searchQuery }\n      });\n      this.searchQuery = ''; // Clear search after navigation\n    } else {\n      this.router.navigate(['/search']);\n    }\n  }\n\n  logout() {\n    this.authService.logout();\n    this.showUserMenu = false;\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;IAqD9BC,EAAA,CAAAC,cAAA,eAAgE;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,GAAyB;;;;;IACzFP,EAAA,CAAAC,cAAA,eAAuE;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI7EH,EADF,CAAAC,cAAA,cAAmE,eACnC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;;;;IAD0BH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAE,qBAAA,GAA6B;;;;;IAR/DR,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAS,SAAA,YAAmC;IACnCT,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKxBH,EAJA,CAAAU,UAAA,IAAAC,sCAAA,mBAAgE,IAAAC,sCAAA,mBACO,IAAAC,qCAAA,kBAGJ;IAGrEb,EAAA,CAAAG,YAAA,EAAM;;;;IAP6BH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAC,iBAAA,OAA6B;IACxBP,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAC,iBAAA,SAA+B;IAGpCP,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,wBAAA,GAAgC;;;;;IA+B/Df,EAAA,CAAAC,cAAA,YAA8F;IAC5FD,EAAA,CAAAS,SAAA,YAA4B;IAC5BT,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACJH,EAAA,CAAAC,cAAA,YAAkF;IAChFD,EAAA,CAAAS,SAAA,YAAiC;IACjCT,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACJH,EAAA,CAAAS,SAAA,cAA4E;;;;;;IAxBhFT,EAAA,CAAAC,cAAA,cAAsE;IAA3BD,EAAA,CAAAgB,UAAA,mBAAAC,qDAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAgB,cAAA,EAAgB;IAAA,EAAC;IACnEtB,EAAA,CAAAS,SAAA,cAAiF;IACjFT,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAS,SAAA,YAAmC;IAIjCT,EADF,CAAAC,cAAA,cAAuD,YACN;IAC7CD,EAAA,CAAAS,SAAA,YAA2B;IAC3BT,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAAgD;IAC9CD,EAAA,CAAAS,SAAA,aAA0B;IAC1BT,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAS,SAAA,eAAoC;IASpCT,EARA,CAAAU,UAAA,KAAAa,oCAAA,gBAA8F,KAAAC,oCAAA,gBAIZ,KAAAC,sCAAA,kBAIZ;IACtEzB,EAAA,CAAAC,cAAA,kBAAwD;IAAhDD,EAAA,CAAAgB,UAAA,mBAAAU,yDAAA;MAAA1B,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAqB,MAAA,EAAQ;IAAA,EAAC;IACxB3B,EAAA,CAAAS,SAAA,aAAmC;IACnCT,EAAA,CAAAE,MAAA,gBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IA7BCH,EAAA,CAAAI,SAAA,EAA0B;IAACJ,EAA3B,CAAAc,UAAA,QAAAR,MAAA,CAAAsB,WAAA,CAAAC,MAAA,EAAA7B,EAAA,CAAA8B,aAAA,CAA0B,QAAAxB,MAAA,CAAAsB,WAAA,CAAAG,QAAA,CAA6B;IACrC/B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAsB,WAAA,CAAAI,QAAA,CAA0B;IAItBhC,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAiC,WAAA,SAAA3B,MAAA,CAAA4B,YAAA,CAA2B;IAUhDlC,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAsB,WAAA,CAAAO,IAAA,cAAmC;IAInCnC,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAsB,WAAA,CAAAO,IAAA,aAAkC;IAIPnC,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAsB,WAAA,CAAAO,IAAA,gBAAqC;;;;;IAUtEnC,EADF,CAAAC,cAAA,cAA+C,YACO;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7DH,EAAA,CAAAC,cAAA,YAAuD;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAChEF,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;AAyUlB,OAAM,MAAOiC,eAAe;EAU1BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,eAAmC,EACnCC,MAAc;IAHd,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAb,WAAW,GAAgB,IAAI;IAC/B,KAAAc,WAAW,GAAG,EAAE;IAChB,KAAAR,YAAY,GAAG,KAAK;IACpB,KAAAS,aAAa,GAAG,CAAC;IACjB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,eAAe,GAAG,CAAC;IACnB,KAAAC,kBAAkB,GAAG,KAAK;EAOvB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACV,WAAW,CAACW,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACxB,WAAW;MACtC,IAAI,CAACA,WAAW,GAAGuB,IAAI;MAEvB;MACA,IAAIA,IAAI,IAAIC,YAAY,EAAE;QACxBC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjEC,UAAU,CAAC,MAAK;UACd,IAAI,CAAChB,WAAW,CAACiB,kBAAkB,EAAE;UACrC,IAAI,CAAChB,eAAe,CAACiB,sBAAsB,EAAE;QAC/C,CAAC,EAAE,GAAG,CAAC;;IAEX,CAAC,CAAC;IAEF;IACA,IAAI,CAAClB,WAAW,CAACmB,cAAc,CAACR,SAAS,CAAES,KAAa,IAAI;MAC1D,IAAI,CAAChB,aAAa,GAAGgB,KAAK;MAC1B,IAAI,CAACC,gBAAgB,EAAE;MACvBP,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEK,KAAK,CAAC;IACrD,CAAC,CAAC;IAEF;IACA,IAAI,CAACnB,eAAe,CAACqB,kBAAkB,CAACX,SAAS,CAAES,KAAa,IAAI;MAClE,IAAI,CAACf,iBAAiB,GAAGe,KAAK;MAC9B,IAAI,CAACC,gBAAgB,EAAE;MACvBP,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEK,KAAK,CAAC;IACzD,CAAC,CAAC;IAEF;IACA,IAAI,CAACpB,WAAW,CAACuB,gBAAgB,CAACZ,SAAS,CAAEa,MAAc,IAAI;MAC7D,IAAI,CAACjB,eAAe,GAAGiB,MAAM;MAC7BV,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAES,MAAM,CAAC;IAC7D,CAAC,CAAC;IAEF;IACA,IAAI,CAACxB,WAAW,CAACyB,mBAAmB,CAACd,SAAS,CAAEe,SAAkB,IAAI;MACpE,IAAI,CAAClB,kBAAkB,GAAGkB,SAAS;MACnCZ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEW,SAAS,CAAC;IACpE,CAAC,CAAC;IAEF;IACA,IAAI,CAAC1B,WAAW,CAAC2B,QAAQ,EAAE;IAC3B,IAAI,CAAC1B,eAAe,CAAC2B,YAAY,EAAE;IAEnC;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAI;MAC3C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;MAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;QACjC,IAAI,CAACtC,YAAY,GAAG,KAAK;;IAE7B,CAAC,CAAC;EACJ;EAEAZ,cAAcA,CAAA;IACZ,IAAI,CAACY,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAuC,UAAUA,CAAA;IACR,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACQd,gBAAgBA,CAAA;IACtB,IAAI,CAACf,cAAc,GAAG,CAAC,IAAI,CAACF,aAAa,IAAI,CAAC,KAAK,IAAI,CAACC,iBAAiB,IAAI,CAAC,CAAC;EACjF;EAEA;EACArC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACsC,cAAc;EAC5B;EAEA;EACArC,qBAAqBA,CAAA;IACnB,OAAO,IAAImE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC,IAAI,CAACjC,eAAe,IAAI,CAAC,CAAC;EACtC;EAEA;EACA/B,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACa,WAAW,KAAK,IAAI,IAAI,IAAI,CAACmB,kBAAkB;EAC7D;EAEAiC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtC,WAAW,CAACuC,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACxC,MAAM,CAACiC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;QAChCQ,WAAW,EAAE;UAAEC,CAAC,EAAE,IAAI,CAACzC;QAAW;OACnC,CAAC;MACF,IAAI,CAACA,WAAW,GAAG,EAAE,CAAC,CAAC;KACxB,MAAM;MACL,IAAI,CAACD,MAAM,CAACiC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA/C,MAAMA,CAAA;IACJ,IAAI,CAACW,WAAW,CAACX,MAAM,EAAE;IACzB,IAAI,CAACO,YAAY,GAAG,KAAK;EAC3B;;;uBArHWE,eAAe,EAAApC,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxF,EAAA,CAAAoF,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAA1F,EAAA,CAAAoF,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfxD,eAAe;MAAAyD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/F,EAAA,CAAAgG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnadtG,EANV,CAAAC,cAAA,gBAAuB,aACE,aACO,aAER,WACM,YACM;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACrC,EACA;UAGNH,EAAA,CAAAC,cAAA,aAA+C;UAAvBD,EAAA,CAAAgB,UAAA,mBAAAwF,8CAAA;YAAA,OAASD,GAAA,CAAA9B,UAAA,EAAY;UAAA,EAAC;UAC5CzE,EAAA,CAAAS,SAAA,WAA6B;UAC7BT,EAAA,CAAAC,cAAA,eAOC;UAJCD,EAAA,CAAAyG,gBAAA,2BAAAC,wDAAAC,MAAA;YAAA3G,EAAA,CAAA4G,kBAAA,CAAAL,GAAA,CAAA7D,WAAA,EAAAiE,MAAA,MAAAJ,GAAA,CAAA7D,WAAA,GAAAiE,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAEzB3G,EADA,CAAAgB,UAAA,yBAAA6F,sDAAA;YAAA,OAAeN,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC,mBAAA8B,gDAAA;YAAA,OACjBP,GAAA,CAAA9B,UAAA,EAAY;UAAA,EAAC;UAG1BzE,EARE,CAAAG,YAAA,EAOC,EACG;UAIJH,EADF,CAAAC,cAAA,cAAsB,aAC6C;UAC/DD,EAAA,CAAAS,SAAA,aAA2B;UAC3BT,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACZF,EADY,CAAAG,YAAA,EAAO,EACf;UACJH,EAAA,CAAAC,cAAA,aAAiE;UAC/DD,EAAA,CAAAS,SAAA,aAA8B;UAC9BT,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACfF,EADe,CAAAG,YAAA,EAAO,EAClB;UACJH,EAAA,CAAAC,cAAA,aAAiE;UAC/DD,EAAA,CAAAS,SAAA,aAAmC;UACnCT,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACZF,EADY,CAAAG,YAAA,EAAO,EACf;UAEJH,EAAA,CAAAU,UAAA,KAAAqG,+BAAA,kBAAkD;UAYlD/G,EAAA,CAAAC,cAAA,aAAqE;UACnED,EAAA,CAAAS,SAAA,aAA4B;UAC5BT,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAChBF,EADgB,CAAAG,YAAA,EAAO,EACnB;UACJH,EAAA,CAAAC,cAAA,aAAiE;UAC/DD,EAAA,CAAAS,SAAA,aAAoC;UACpCT,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACZF,EADY,CAAAG,YAAA,EAAO,EACf;UAoCJH,EAjCA,CAAAU,UAAA,KAAAsG,+BAAA,mBAAsE,KAAAC,+BAAA,kBAiCvB;UAOvDjH,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACC;;;UApFCH,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAkH,gBAAA,YAAAX,GAAA,CAAA7D,WAAA,CAAyB;UAsBI1C,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAc,UAAA,SAAAyF,GAAA,CAAA3E,WAAA,CAAiB;UAsB1C5B,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAc,UAAA,SAAAyF,GAAA,CAAA3E,WAAA,CAAiB;UAiCjB5B,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAAc,UAAA,UAAAyF,GAAA,CAAA3E,WAAA,CAAkB;;;qBA/FxB/B,YAAY,EAAAsH,EAAA,CAAAC,IAAA,EAAEtH,YAAY,EAAA6F,EAAA,CAAA0B,UAAA,EAAA1B,EAAA,CAAA2B,gBAAA,EAAEvH,WAAW,EAAAwH,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}