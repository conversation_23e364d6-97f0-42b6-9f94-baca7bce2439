.rating-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stars {
  display: flex;
  gap: 0.125rem;

  ion-icon {
    font-size: 1rem;
    
    &.interactive {
      cursor: pointer;
      transition: transform 0.2s ease;
      
      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

.rating-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ion-color-dark);
}

.rating-count {
  color: var(--ion-color-medium);
  font-weight: normal;
}

// Size variants
:host(.small) .stars ion-icon {
  font-size: 0.875rem;
}

:host(.large) .stars ion-icon {
  font-size: 1.25rem;
}
