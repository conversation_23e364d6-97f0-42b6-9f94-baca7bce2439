.dashboard-container {
  min-height: 100%;
  background: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 1rem;

  p {
    color: #666;
    margin: 0;
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .welcome-text {
    h1 {
      margin: 0 0 0.5rem 0;
      font-size: 2rem;
      font-weight: 600;
    }

    p {
      margin: 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }
  }

  .welcome-actions {
    button {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;

  .stat-card {
    border-left: 4px solid;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    mat-card-content {
      padding: 1.5rem;

      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;

          mat-icon {
            font-size: 1.5rem;
          }
        }

        .stat-change {
          font-size: 0.85rem;
          font-weight: 600;
          padding: 0.25rem 0.5rem;
          border-radius: 6px;

          &.positive-change {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
          }

          &.negative-change {
            background: rgba(244, 67, 54, 0.1);
            color: #f44336;
          }
        }
      }

      .stat-content {
        .stat-value {
          font-size: 2rem;
          font-weight: 700;
          color: #333;
          margin-bottom: 0.25rem;
        }

        .stat-title {
          color: #666;
          font-size: 0.95rem;
          font-weight: 500;
        }
      }
    }
  }
}

.charts-section {
  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;

    .chart-card {
      mat-card-header {
        margin-bottom: 1rem;

        mat-card-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: #333;
        }

        mat-card-subtitle {
          color: #666;
          margin-top: 0.25rem;
        }
      }

      .chart-container {
        height: 300px;
        position: relative;

        .chart-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: #f8f9fa;
          border-radius: 8px;
          color: #666;
          text-align: center;
          padding: 2rem;

          mat-icon {
            font-size: 4rem;
            width: 4rem;
            height: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
          }

          h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.125rem;
            font-weight: 500;
          }

          p {
            margin: 0 0 1rem 0;
            font-size: 0.875rem;
          }

          .mock-data {
            small {
              font-size: 0.75rem;
              color: #999;
            }
          }
        }
      }
    }
  }
}

.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;

  .activities-card {
    .activities-list {
      .activity-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .activity-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;

          mat-icon {
            font-size: 1.25rem;
          }
        }

        .activity-content {
          flex: 1;

          .activity-message {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.25rem;
          }

          .activity-time {
            color: #666;
            font-size: 0.85rem;
          }
        }
      }
    }

    .activities-footer {
      text-align: center;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #f0f0f0;
    }
  }

  .actions-card {
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;

      .action-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1.5rem 1rem;
        height: auto;
        background: #f8f9fa;
        color: #333;
        border: 1px solid #e9ecef;

        &:hover {
          background: #e9ecef;
          transform: translateY(-1px);
        }

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }

        span {
          font-size: 0.85rem;
          font-weight: 500;
        }
      }
    }
  }
}

.status-card {
  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;

    .status-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;

        &.online {
          background: #4caf50;
        }

        &.warning {
          background: #ff9800;
        }

        &.error {
          background: #f44336;
        }
      }

      .status-info {
        .status-label {
          font-weight: 500;
          color: #333;
          margin-bottom: 0.25rem;
        }

        .status-value {
          color: #666;
          font-size: 0.9rem;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .charts-section .charts-grid {
    grid-template-columns: 1fr;
  }

  .bottom-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    gap: 1rem;
    text-align: center;

    .welcome-text h1 {
      font-size: 1.5rem;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-section .charts-grid {
    grid-template-columns: 1fr;

    .chart-card .chart-container {
      height: 250px;
    }
  }

  .actions-card .actions-grid {
    grid-template-columns: 1fr;
  }

  .status-card .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .dashboard-content {
    gap: 1rem;
  }

  .welcome-section {
    padding: 1.5rem;
  }

  .status-card .status-grid {
    grid-template-columns: 1fr;
  }
}

// Material Overrides
::ng-deep {
  .mat-card {
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .mat-card-header {
    padding-bottom: 0 !important;
  }

  .mat-spinner circle {
    stroke: #667eea;
  }
}
