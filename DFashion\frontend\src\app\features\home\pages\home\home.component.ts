import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ProductService } from '../../../../core/services/product.service';
import { AuthService } from '../../../../core/services/auth.service';
import { ButtonActionsService } from '../../../../core/services/button-actions.service';
import { PostsService, InstagramPost } from '../../../../core/services/posts.service';
import { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';
import { PaymentModalComponent, PaymentModalData } from '../../../../shared/components/payment-modal/payment-modal.component';
import { SidebarComponent } from '../../components/sidebar/sidebar.component';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, ViewAddStoriesComponent,
    SidebarComponent, PaymentModalComponent],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  featuredProducts: any[] = [];
  trendingProducts: any[] = [];
  newArrivals: any[] = [];
  trendingPosts: any[] = [];
  categories: any[] = [];
  isLoading = true;
  isAuthenticated = false;

  // Instagram-style data
  instagramPosts: any[] = [];
  summerCollection: any[] = [];
  suggestedUsers: any[] = [];
  currentUser: any = null;
  newComment = '';

  // Payment Modal
  showPaymentModal = false;
  paymentModalData: PaymentModalData | null = null;

  constructor(
    private router: Router,
    private productService: ProductService,
    private authService: AuthService,
    private buttonActionsService: ButtonActionsService,
    private postsService: PostsService
  ) {}

  ngOnInit() {
    this.loadHomeData();
    this.checkAuthStatus();
    this.loadInstagramData();
  }

  checkAuthStatus() {
    this.authService.currentUser$.subscribe(user => {
      this.isAuthenticated = !!user;
      this.currentUser = user;
    });
  }

  loadInstagramData() {
    this.loadInstagramPosts();
    this.loadSummerCollection();
    this.loadSuggestedUsers();
  }

  loadInstagramPosts() {
    // Load real Instagram-style posts from API
    this.postsService.getPosts(1, 10).subscribe({
      next: (response) => {
        if (response.success) {
          this.instagramPosts = response.posts;
          console.log('✅ Instagram posts loaded:', this.instagramPosts.length);
        } else {
          this.instagramPosts = [];
          console.log('⚠️ No Instagram posts available');
        }
      },
      error: (error) => {
        console.error('❌ Error loading Instagram posts:', error);
        this.instagramPosts = [];
      }
    });
  }

  loadSummerCollection() {
    // Load summer collection from API using category products
    this.productService.getCategoryProducts('summer').subscribe({
      next: (response: any) => {
        this.summerCollection = response.products || [];
        console.log('✅ Summer collection loaded:', this.summerCollection.length);
      },
      error: (error: any) => {
        console.error('❌ Error loading summer collection:', error);
        this.summerCollection = [];
      }
    });
  }

  loadSuggestedUsers() {
    // Load suggested users from API
    // For now, set empty array until users API is implemented
    this.suggestedUsers = [];
    console.log('✅ Suggested users loaded:', this.suggestedUsers.length);
  }

  async loadHomeData() {
    try {
      this.isLoading = true;

      // Load all data in parallel
      const [featured, trending, arrivals] = await Promise.all([
        this.productService.getFeaturedProducts().toPromise(),
        this.productService.getTrendingProducts().toPromise(),
        this.productService.getNewArrivals().toPromise()
      ]);

      this.featuredProducts = ((featured as any)?.products || (featured as any)?.data || featured || []).slice(0, 8);
      this.trendingProducts = ((trending as any)?.products || (trending as any)?.data || trending || []).slice(0, 8);
      this.newArrivals = ((arrivals as any)?.products || (arrivals as any)?.data || arrivals || []).slice(0, 8);

      // Log loaded data counts
      console.log('✅ Featured products loaded:', this.featuredProducts.length);
      console.log('✅ Trending products loaded:', this.trendingProducts.length);
      console.log('✅ New arrivals loaded:', this.newArrivals.length);

      // Load categories
      this.loadCategories();

      // Load trending posts (mock data for now)
      this.loadTrendingPosts();

    } catch (error) {
      console.error('❌ Error loading home data:', error);
      // Set empty arrays instead of fallback data
      this.featuredProducts = [];
      this.trendingProducts = [];
      this.newArrivals = [];
    } finally {
      this.isLoading = false;
    }
  }

  loadCategories() {
    // Load categories from API
    this.productService.getCategories().subscribe({
      next: (response: any) => {
        this.categories = response.data || [];
        console.log('✅ Categories loaded:', this.categories.length);
      },
      error: (error: any) => {
        console.error('❌ Error loading categories:', error);
        this.categories = [];
      }
    });
  }

  loadTrendingPosts() {
    // Load trending posts from API
    this.postsService.getTrendingPosts(6).subscribe({
      next: (response) => {
        if (response.success) {
          this.trendingPosts = response.posts;
          console.log('✅ Trending posts loaded:', this.trendingPosts.length);
        } else {
          this.trendingPosts = [];
          console.log('⚠️ No trending posts available');
        }
      },
      error: (error) => {
        console.error('❌ Error loading trending posts:', error);
        this.trendingPosts = [];
      }
    });
  }



  // Navigation methods
  onProductClick(product: any) {
    this.router.navigate(['/product', product._id || product.id]);
  }

  onCategoryClick(category: any) {
    this.router.navigate(['/shop'], {
      queryParams: { category: category.name.toLowerCase() }
    });
  }

  viewAllCategories() {
    this.router.navigate(['/shop']);
  }

  // Instagram-style interaction methods
  toggleLike(post: InstagramPost) {
    if (!post._id) return;

    // Optimistic update
    const wasLiked = post.isLiked;
    post.isLiked = !post.isLiked;
    post.analytics.likes += post.isLiked ? 1 : -1;

    this.postsService.toggleLike(post._id).subscribe({
      next: (result) => {
        if (result.success && result.likesCount !== undefined) {
          post.analytics.likes = result.likesCount;
          console.log('✅ Post like toggled successfully');
        } else {
          // Revert on failure
          post.isLiked = wasLiked;
          post.analytics.likes += wasLiked ? 1 : -1;
          console.error('❌ Failed to toggle like:', result.message);
        }
      },
      error: (error) => {
        // Revert on error
        post.isLiked = wasLiked;
        post.analytics.likes += wasLiked ? 1 : -1;
        console.error('❌ Error toggling like:', error);
      }
    });
  }

  toggleSave(post: any) {
    if (!post.id && !post._id) return;

    const postId = post.id || post._id;

    // Optimistic update
    post.isSaved = !post.isSaved;

    this.buttonActionsService.savePost(postId).subscribe({
      next: (result) => {
        if (!result.success) {
          // Revert on failure
          post.isSaved = !post.isSaved;
          console.error('Failed to save post:', result.message);
        }
      },
      error: (error) => {
        // Revert on error
        post.isSaved = !post.isSaved;
        console.error('Error saving post:', error);
      }
    });
  }

  sharePost(post: any) {
    if (!post.id && !post._id) return;

    const postId = post.id || post._id;

    this.buttonActionsService.sharePost(postId).subscribe({
      next: (result) => {
        if (result.success) {
          console.log('Post shared successfully');
        } else {
          console.error('Failed to share post:', result.message);
        }
      },
      error: (error) => {
        console.error('Error sharing post:', error);
      }
    });
  }

  focusCommentInput(post: any) {
    // Focus on comment input for this post
    console.log('Focus comment for post:', post.id);
  }

  addComment(post: InstagramPost) {
    if (!this.newComment || !this.newComment.trim()) return;
    if (!post._id) return;

    const commentText = this.newComment.trim();

    this.postsService.addComment(post._id, commentText).subscribe({
      next: (result) => {
        if (result.success && result.comment) {
          // Add comment to local state
          post.comments.push(result.comment);
          post.analytics.comments += 1;
          this.newComment = '';
          console.log('✅ Comment added successfully');
        } else {
          console.error('❌ Failed to add comment:', result.message);
        }
      },
      error: (error) => {
        console.error('❌ Error adding comment:', error);
      }
    });
  }

  showProductDetails(product: any) {
    console.log('🛍️ Show product details:', product);
    if (product._id) {
      this.router.navigate(['/product', product._id]);
    } else {
      // Toggle product preview for posts
      product.showPreview = !product.showPreview;
    }
  }

  // View full post
  viewPost(post: InstagramPost) {
    console.log('📱 View post:', post._id);
    this.router.navigate(['/post', post._id]);
  }

  // View user profile
  viewUserProfile(user: any) {
    console.log('👤 View user profile:', user.username);
    this.router.navigate(['/profile', user.username]);
  }

  // Enhanced product tags click handler
  handleProductTagsClick(post: InstagramPost) {
    if (!post.products || post.products.length === 0) return;

    // If only one product/item, navigate directly
    if (post.products.length === 1) {
      const productTag = post.products[0];
      this.onProductTagClick(productTag);
      return;
    }

    // If multiple products, toggle visibility to show selection
    this.toggleProductTags(post);
  }

  // Get dynamic title for product tags button
  getProductTagsButtonTitle(post: InstagramPost): string {
    if (!post.products || post.products.length === 0) return 'No products';

    if (post.products.length === 1) {
      const productTag = post.products[0];
      // Since PostProduct only has basic product info, we'll use the product name
      return `View ${productTag.product?.name || 'Product'}`;
    }

    return `View ${post.products.length} Products`;
  }

  // Toggle product tags visibility (kept for multiple products)
  toggleProductTags(post: InstagramPost) {
    post.showProductTags = !post.showProductTags;
    console.log('🏷️ Product tags toggled:', post.showProductTags);
  }

  // E-commerce methods
  buyNow(product: any) {
    console.log('Buy now:', product);

    if (!product.id && !product._id) return;

    const productId = product.id || product._id;

    this.buttonActionsService.buyNow({
      productId: productId,
      quantity: 1,
      addedFrom: 'home_feed'
    }).subscribe({
      next: (result) => {
        if (result.success) {
          console.log('Buy now successful, redirecting to checkout');
          // Navigation will be handled by the service
        } else {
          console.error('Failed to buy now:', result.message);
          // Fallback to payment modal if needed
          this.showPaymentModalFallback(product);
        }
      },
      error: (error) => {
        console.error('Error in buy now:', error);
        // Fallback to payment modal
        this.showPaymentModalFallback(product);
      }
    });
  }

  private showPaymentModalFallback(product: any) {
    if (!this.currentUser) {
      this.router.navigate(['/auth/login'], { queryParams: { returnUrl: '/home' } });
      return;
    }

    // Prepare payment modal data for single product purchase
    this.paymentModalData = {
      amount: product.price,
      orderData: {
        items: [{
          id: product.id,
          name: product.name,
          price: product.price,
          quantity: 1,
          image: product.image
        }],
        subtotal: product.price,
        tax: product.price * 0.18,
        shipping: 0,
        discount: 0,
        total: product.price + (product.price * 0.18)
      },
      userDetails: {
        name: this.currentUser.fullName || this.currentUser.username,
        email: this.currentUser.email,
        phone: this.currentUser.phone || ''
      }
    };

    this.showPaymentModal = true;
  }

  addToWishlist(product: any) {
    console.log('Add to wishlist:', product);

    if (!product.id && !product._id) return;

    const productId = product.id || product._id;

    this.buttonActionsService.addToWishlist({
      productId: productId,
      addedFrom: 'home_feed'
    }).subscribe({
      next: (result) => {
        if (result.success) {
          console.log('Product added to wishlist successfully');
        } else {
          console.error('Failed to add to wishlist:', result.message);
        }
      },
      error: (error) => {
        console.error('Error adding to wishlist:', error);
      }
    });
  }

  addToCart(product: any) {
    console.log('Add to cart:', product);

    if (!product.id && !product._id) return;

    const productId = product.id || product._id;

    this.buttonActionsService.addToCart({
      productId: productId,
      quantity: 1,
      addedFrom: 'home_feed'
    }).subscribe({
      next: (result) => {
        if (result.success) {
          console.log('Product added to cart successfully');
        } else {
          console.error('Failed to add to cart:', result.message);
        }
      },
      error: (error) => {
        console.error('Error adding to cart:', error);
      }
    });
  }

  followUser(user: any) {
    console.log('Follow user:', user.username);
    // Implement follow functionality
  }

  viewSummerCollection() {
    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });
  }

  toggleComments(post: any) {
    console.log('Toggle comments for post:', post.id);
    // Implement comments toggle
  }

  // Utility methods
  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  formatLikesCount(likes: number): string {
    return this.formatNumber(likes);
  }

  getTimeAgo(date: Date | string): string {
    if (!date) return 'Unknown';

    let dateObj: Date;

    // Handle different date formats
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return 'Unknown';
    }

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Unknown';
    }

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    return `${Math.floor(diffInSeconds / 604800)}w`;
  }

  trackByPostId(index: number, post: any): string {
    return post.id;
  }

  // Payment Modal handlers
  onPaymentCompleted(paymentResult: any) {
    this.showPaymentModal = false;

    if (paymentResult.status === 'success') {
      // Navigate to success page
      this.router.navigate(['/payment-success'], {
        queryParams: {
          orderId: paymentResult.orderId || this.generateOrderId(),
          method: paymentResult.method
        }
      });
    } else {
      // Show error message
      alert('Payment failed. Please try again.');
    }
  }

  onPaymentModalClose() {
    this.showPaymentModal = false;
  }

  private generateOrderId(): string {
    return 'ORD' + Date.now().toString();
  }

  // Enhanced product tag methods
  onProductTagClick(productTag: any): void {
    if (!productTag || !productTag.product) return;

    // Since PostProduct only has basic product info, we'll navigate to the product
    this.navigateToProduct(productTag.product);
  }

  private navigateToProduct(product: any): void {
    if (product?._id || product?.id) {
      const productId = product._id || product.id;
      console.log('🛍️ Navigating to product:', productId);
      this.router.navigate(['/product', productId]);
    } else {
      console.warn('⚠️ Product navigation failed: No valid product ID', product);
      this.showToast('Product not found');
    }
  }

  private navigateToCategory(category: any): void {
    if (category?.slug || category?.name || category?._id) {
      const categoryIdentifier = category.slug || category._id || category.name.toLowerCase().replace(/\s+/g, '-');
      console.log('📂 Navigating to category:', categoryIdentifier);
      this.router.navigate(['/shop'], {
        queryParams: { category: categoryIdentifier }
      });
    } else {
      console.warn('⚠️ Category navigation failed: No valid category identifier', category);
      this.showToast('Category not found');
    }
  }

  private navigateToVendor(vendor: any): void {
    if (vendor?._id || vendor?.id || vendor?.username) {
      const vendorId = vendor._id || vendor.id || vendor.username;
      console.log('🏪 Navigating to vendor:', vendorId);
      this.router.navigate(['/vendor', vendorId]);
    } else {
      console.warn('⚠️ Vendor navigation failed: No valid vendor identifier', vendor);
      this.showToast('Vendor not found');
    }
  }

  private navigateToBrand(brand: any): void {
    if (brand?.slug || brand?.name || brand?._id) {
      const brandIdentifier = brand.slug || brand._id || brand.name.toLowerCase().replace(/\s+/g, '-');
      console.log('🏷️ Navigating to brand:', brandIdentifier);
      this.router.navigate(['/shop'], {
        queryParams: { brand: brandIdentifier }
      });
    } else {
      console.warn('⚠️ Brand navigation failed: No valid brand identifier', brand);
      this.showToast('Brand not found');
    }
  }

  getTagTooltip(productTag: any): string {
    const name = this.getTagName(productTag);
    return `View product: ${name}`;
  }

  getTagIcon(navigationType: string): string {
    return 'fas fa-tag'; // Always use product tag icon since we only support products
  }

  getTagName(productTag: any): string {
    return productTag.product?.name || 'Product';
  }

  getTagSubtitle(productTag: any): string {
    return productTag.product?.price ? `$${productTag.product.price}` : '';
  }

  getTagImageUrl(productTag: any): string {
    return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';
  }

  // Simple toast notification method
  private showToast(message: string): void {
    // Create toast element
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      z-index: 10000;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      backdrop-filter: blur(10px);
      animation: slideUp 0.3s ease-out;
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }
    `;
    document.head.appendChild(style);

    // Add to DOM
    document.body.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        document.body.removeChild(toast);
      }
      if (style.parentNode) {
        document.head.removeChild(style);
      }
    }, 3000);
  }
}
