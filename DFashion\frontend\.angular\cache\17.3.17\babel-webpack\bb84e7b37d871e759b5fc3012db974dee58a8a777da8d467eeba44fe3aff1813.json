{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/cart.service\";\nimport * as i5 from \"src/app/core/services/wishlist.service\";\nimport * as i6 from \"src/app/core/services/social-media.service\";\nimport * as i7 from \"src/app/core/services/realtime.service\";\nimport * as i8 from \"src/app/core/services/button-actions.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"ngx-owl-carousel-o\";\nconst _c0 = [\"storiesSlider\"];\nconst _c1 = [\"storiesTrack\"];\nconst _c2 = [\"storyVideo\"];\nfunction ViewAddStoriesComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_ng_template_13_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAdd());\n    });\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 20)(3, \"div\", 21);\n    i0.ɵɵelement(4, \"img\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 23);\n    i0.ɵɵelement(6, \"i\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 25);\n    i0.ɵɵtext(8, \"Your Story\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_14_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 19)(2, \"div\", 27)(3, \"div\", 21);\n    i0.ɵɵelement(4, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewAddStoriesComponent_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ViewAddStoriesComponent_14_ng_template_0_Template, 7, 0, \"ng-template\", 12);\n  }\n}\nfunction ViewAddStoriesComponent_15_ng_template_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_15_ng_template_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const userGroup_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", userGroup_r6.totalProducts, \" item\", userGroup_r6.totalProducts > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_15_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_15_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const userGroup_r6 = ctx_r4.$implicit;\n      const i_r7 = ctx_r4.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openUserStories(userGroup_r6, i_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 30)(3, \"div\", 21);\n    i0.ɵɵelement(4, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ViewAddStoriesComponent_15_ng_template_0_div_5_Template, 2, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ViewAddStoriesComponent_15_ng_template_0_div_10_Template, 2, 2, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const userGroup_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-products\", userGroup_r6.hasProducts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", userGroup_r6.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", userGroup_r6.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", userGroup_r6.hasProducts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(userGroup_r6.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", userGroup_r6.stories.length, \" \", userGroup_r6.stories.length === 1 ? \"story\" : \"stories\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", userGroup_r6.hasProducts);\n  }\n}\nfunction ViewAddStoriesComponent_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ViewAddStoriesComponent_15_ng_template_0_Template, 11, 9, \"ng-template\", 12);\n  }\n}\nfunction ViewAddStoriesComponent_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r9 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r9 === ctx_r2.currentStoryIndex)(\"completed\", i_r9 < ctx_r2.currentStoryIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r2.getProgressWidth(i_r9), \"%\");\n  }\n}\nfunction ViewAddStoriesComponent_div_18_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 66);\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (tmp_4_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_4_0.mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_18_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 67, 2);\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (tmp_5_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_5_0.mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_18_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_div_17_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.handleMiddleAreaClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 69);\n    i0.ɵɵelement(2, \"i\", 70);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \"View linked \", (tmp_4_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_4_0.linkedContent == null ? null : tmp_4_0.linkedContent.type, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.getLinkedContentText());\n  }\n}\nfunction ViewAddStoriesComponent_div_18_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_div_18_div_1_Template_div_click_0_listener($event) {\n      const productTag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      productTag_r12.product && ctx_r2.openProductDetails(productTag_r12.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"div\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵelement(3, \"img\", 76);\n    i0.ɵɵelementStart(4, \"div\", 77)(5, \"span\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 79);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 80)(10, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_div_18_div_1_Template_button_click_10_listener($event) {\n      const productTag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      productTag_r12.product && ctx_r2.addToCart(productTag_r12.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(11, \"i\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_div_18_div_1_Template_button_click_12_listener($event) {\n      const productTag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      productTag_r12.product && ctx_r2.addToWishlist(productTag_r12.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(13, \"i\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_div_18_div_1_Template_button_click_14_listener($event) {\n      const productTag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      productTag_r12.product && ctx_r2.buyNow(productTag_r12.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(15, \"i\", 86);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const productTag_r12 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", (productTag_r12.position == null ? null : productTag_r12.position.x) || 50, \"%\")(\"top\", (productTag_r12.position == null ? null : productTag_r12.position.y) || 50, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", productTag_r12.product.images[0].url || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", productTag_r12.product.name || \"Product\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r12.product.name || \"Product\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", productTag_r12.product.price || 0, \"\");\n  }\n}\nfunction ViewAddStoriesComponent_div_18_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_18_div_18_div_1_Template, 16, 8, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", (tmp_4_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_4_0.products);\n  }\n}\nfunction ViewAddStoriesComponent_div_18_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_button_19_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 88);\n    i0.ɵɵelementStart(2, \"span\", 89);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.showProductTags);\n    i0.ɵɵproperty(\"title\", \"View \" + ctx_r2.getProductCount() + \" product\" + (ctx_r2.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵattribute(\"aria-label\", \"View \" + ctx_r2.getProductCount() + \" product\" + (ctx_r2.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getProductCount());\n  }\n}\nfunction ViewAddStoriesComponent_div_18_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.previousStory());\n    });\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_18_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.nextStory());\n    });\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39, 1)(3, \"div\", 40);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_18_div_4_Template, 2, 6, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵelement(6, \"img\", 43);\n    i0.ɵɵelementStart(7, \"div\", 44)(8, \"span\", 45);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 46);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeStories());\n    });\n    i0.ɵɵelement(13, \"i\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_Template_div_click_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_18_Template_div_touchstart_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_18_Template_div_touchmove_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_18_Template_div_touchend_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTouchEnd($event));\n    });\n    i0.ɵɵtemplate(15, ViewAddStoriesComponent_div_18_img_15_Template, 1, 1, \"img\", 50)(16, ViewAddStoriesComponent_div_18_video_16_Template, 2, 1, \"video\", 51)(17, ViewAddStoriesComponent_div_18_div_17_Template, 5, 3, \"div\", 52)(18, ViewAddStoriesComponent_div_18_div_18_Template, 2, 1, \"div\", 53)(19, ViewAddStoriesComponent_div_18_button_19_Template, 4, 5, \"button\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 55)(21, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleLike());\n    });\n    i0.ɵɵelement(22, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.shareStory());\n    });\n    i0.ɵɵelement(24, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_18_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveStory());\n    });\n    i0.ɵɵelement(26, \"i\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, ViewAddStoriesComponent_div_18_button_27_Template, 2, 0, \"button\", 62)(28, ViewAddStoriesComponent_div_18_button_28_Template, 2, 0, \"button\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.stories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (tmp_4_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_4_0.user == null ? null : tmp_4_0.user.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_5_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_5_0.user == null ? null : tmp_5_0.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getTimeAgo(((tmp_6_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_6_0.createdAt) || \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_7_0.mediaType) === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_8_0.mediaType) === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_9_0.linkedContent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showProductTags && ((tmp_10_0 = ctx_r2.getCurrentStory()) == null ? null : tmp_10_0.products));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasProducts());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r2.isLiked);\n    i0.ɵɵproperty(\"title\", ctx_r2.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStoryIndex > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStoryIndex < ctx_r2.stories.length - 1);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService, cartService, wishlistService, socialMediaService, realtimeService, cdr, buttonActionsService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.socialMediaService = socialMediaService;\n    this.realtimeService = realtimeService;\n    this.cdr = cdr;\n    this.buttonActionsService = buttonActionsService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    // Story viewer state\n    this.isOpen = false;\n    this.currentStoryIndex = 0;\n    this.currentUserIndex = 0;\n    this.currentUserStories = [];\n    this.showProductTags = false;\n    this.isLiked = false;\n    this.storyProgress = 0;\n    // Navigation state\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.showNavArrows = true;\n    // Custom slider properties\n    this.translateX = 0;\n    this.itemWidth = 80; // Width of each story item including margin\n    this.visibleItems = 6; // Number of visible items\n    this.currentSlideIndex = 0;\n    this.storyDuration = 15000; // 15 seconds default\n    this.progressStartTime = 0;\n    this.currentProgress = 0; // Stable progress value for template binding\n    // Touch handling\n    this.touchStartTime = 0;\n    // Carousel options\n    this.customOptions = {\n      loop: false,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      responsive: {\n        0: {\n          items: 3,\n          nav: false\n        },\n        600: {\n          items: 4,\n          nav: false\n        },\n        900: {\n          items: 6,\n          nav: true\n        }\n      },\n      nav: true\n    };\n    this.subscriptions = [];\n    console.log('🏗️ ViewAddStoriesComponent constructor called');\n  }\n  ngOnInit() {\n    console.log('🚀 ViewAddStoriesComponent ngOnInit called');\n    this.loadStories();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      console.log('👤 Current user updated:', user);\n    });\n    // Check if we should show navigation arrows based on screen size\n    this.updateNavArrowsVisibility();\n  }\n  ngAfterViewInit() {\n    console.log('ngAfterViewInit called');\n    setTimeout(() => {\n      console.log('Initializing slider after view init');\n      this.calculateSliderDimensions();\n      this.updateScrollButtons();\n    }, 500);\n    // Also try immediate initialization\n    setTimeout(() => {\n      if (this.stories.length > 0) {\n        console.log('Re-initializing slider with stories');\n        this.calculateSliderDimensions();\n        this.updateScrollButtons();\n      }\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n  onResize() {\n    this.updateNavArrowsVisibility();\n    this.calculateSliderDimensions();\n    this.updateScrollButtons();\n  }\n  updateNavArrowsVisibility() {\n    this.showNavArrows = window.innerWidth > 768;\n  }\n  calculateSliderDimensions() {\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\n      const screenWidth = window.innerWidth;\n      console.log('Calculating slider dimensions:', {\n        containerWidth,\n        screenWidth\n      });\n      // Responsive visible items\n      if (screenWidth <= 600) {\n        this.visibleItems = 3;\n        this.itemWidth = containerWidth / 3;\n      } else if (screenWidth <= 900) {\n        this.visibleItems = 4;\n        this.itemWidth = containerWidth / 4;\n      } else {\n        this.visibleItems = 6;\n        this.itemWidth = containerWidth / 6;\n      }\n      console.log('Slider dimensions calculated:', {\n        visibleItems: this.visibleItems,\n        itemWidth: this.itemWidth\n      });\n      // Reset slider position if needed\n      this.currentSlideIndex = 0;\n      this.translateX = 0;\n    } else {\n      console.warn('Stories slider element not found, using default dimensions');\n      // Fallback dimensions\n      this.itemWidth = 100;\n      this.visibleItems = 6;\n    }\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        console.log('Stories API response:', response);\n        if (response.success && response.stories && response.stories.length > 0) {\n          // Filter only active stories and map the data structure\n          const allStories = response.stories.filter(story => story.isActive).map(story => ({\n            ...story,\n            mediaUrl: story.media?.url || story.mediaUrl,\n            mediaType: story.media?.type || story.mediaType\n          }));\n          // Group stories by user (Instagram style)\n          this.stories = this.groupStoriesByUser(allStories);\n          console.log('Loaded and grouped stories from API:', this.stories);\n          console.log('Total user story groups:', this.stories.length);\n        } else {\n          console.log('No stories from API, loading fallback stories');\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        console.log('Loading fallback stories due to error');\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      }\n    }));\n  }\n  // Group stories by user like Instagram\n  groupStoriesByUser(allStories) {\n    const userStoriesMap = new Map();\n    allStories.forEach(story => {\n      const userId = story.user._id;\n      if (!userStoriesMap.has(userId)) {\n        userStoriesMap.set(userId, {\n          user: story.user,\n          stories: [],\n          hasProducts: false,\n          totalProducts: 0,\n          latestStoryTime: story.createdAt\n        });\n      }\n      const userGroup = userStoriesMap.get(userId);\n      userGroup.stories.push(story);\n      // Check if any story has products\n      if (story.products && story.products.length > 0) {\n        userGroup.hasProducts = true;\n        userGroup.totalProducts += story.products.length;\n      }\n      // Keep track of latest story time for sorting\n      if (new Date(story.createdAt) > new Date(userGroup.latestStoryTime)) {\n        userGroup.latestStoryTime = story.createdAt;\n      }\n    });\n    // Convert map to array and sort by latest story time\n    return Array.from(userStoriesMap.values()).sort((a, b) => new Date(b.latestStoryTime).getTime() - new Date(a.latestStoryTime).getTime());\n  }\n  loadFallbackStories() {\n    console.log('❌ No stories available from API');\n    this.stories = [];\n  }\n  // Navigation methods\n  scrollLeft() {\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\n    if (this.currentSlideIndex > 0) {\n      this.currentSlideIndex--;\n      this.updateSliderPosition();\n      console.log('Scrolled left to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll left, already at start');\n    }\n  }\n  scrollRight() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    console.log('Scroll right clicked:', {\n      currentIndex: this.currentSlideIndex,\n      totalItems,\n      maxSlideIndex,\n      visibleItems: this.visibleItems\n    });\n    if (this.currentSlideIndex < maxSlideIndex) {\n      this.currentSlideIndex++;\n      this.updateSliderPosition();\n      console.log('Scrolled right to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll right, already at end');\n    }\n  }\n  updateSliderPosition() {\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\n    console.log('Updating slider position:', {\n      currentIndex: this.currentSlideIndex,\n      itemWidth: this.itemWidth,\n      newTranslateX\n    });\n    this.translateX = newTranslateX;\n    this.updateScrollButtons();\n  }\n  updateScrollButtons() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    this.canScrollLeft = this.currentSlideIndex > 0;\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\n    console.log('Updated scroll buttons:', {\n      canScrollLeft: this.canScrollLeft,\n      canScrollRight: this.canScrollRight,\n      totalItems,\n      maxSlideIndex,\n      currentIndex: this.currentSlideIndex\n    });\n  }\n  // Story viewer methods\n  openUserStories(userGroup, userIndex) {\n    this.currentUserIndex = userIndex;\n    this.currentStoryIndex = 0; // Start with first story of this user\n    this.currentUserStories = userGroup.stories;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n    console.log('Opening stories for user:', userGroup.user.username, 'Stories count:', userGroup.stories.length);\n  }\n  openStory(_story, index) {\n    // Legacy method - keeping for compatibility\n    this.currentStoryIndex = index;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.clearStoryTimer();\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n  }\n  nextStory() {\n    // First check if there are more stories for current user\n    if (this.currentStoryIndex < this.currentUserStories.length - 1) {\n      this.currentStoryIndex++;\n      this.showProductTags = false;\n      this.startStoryTimer();\n    } else {\n      // Move to next user's stories\n      if (this.currentUserIndex < this.stories.length - 1) {\n        this.currentUserIndex++;\n        this.currentStoryIndex = 0;\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\n        this.showProductTags = false;\n        this.startStoryTimer();\n      } else {\n        this.closeStories();\n      }\n    }\n  }\n  previousStory() {\n    // First check if there are previous stories for current user\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.showProductTags = false;\n      this.startStoryTimer();\n    } else {\n      // Move to previous user's stories (last story)\n      if (this.currentUserIndex > 0) {\n        this.currentUserIndex--;\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\n        this.currentStoryIndex = this.currentUserStories.length - 1;\n        this.showProductTags = false;\n        this.startStoryTimer();\n      } else {\n        this.closeStories();\n      }\n    }\n  }\n  getCurrentStory() {\n    if (this.currentUserStories && this.currentUserStories.length > 0) {\n      return this.currentUserStories[this.currentStoryIndex] || this.currentUserStories[0];\n    }\n    // Fallback for legacy usage - get first story from first user group\n    if (this.stories && this.stories.length > 0 && this.stories[0].stories.length > 0) {\n      return this.stories[0].stories[0];\n    }\n    return null;\n  }\n  getCurrentUser() {\n    if (this.stories && this.stories.length > 0) {\n      return this.stories[this.currentUserIndex]?.user;\n    }\n    return null;\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getProductCount() {\n    const story = this.getCurrentStory();\n    return story?.products?.length || 0;\n  }\n  // Progress tracking\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) return 100;\n    if (index > this.currentStoryIndex) return 0;\n    // Return the stable progress value for the current story\n    return this.currentProgress;\n  }\n  startStoryTimer() {\n    this.clearStoryTimer();\n    this.progressStartTime = Date.now();\n    this.currentProgress = 0;\n    // Update progress every 100ms for smooth animation\n    this.progressUpdateTimer = setInterval(() => {\n      if (this.progressStartTime) {\n        const elapsed = Date.now() - this.progressStartTime;\n        this.currentProgress = Math.min(elapsed / this.storyDuration * 100, 100);\n        this.cdr.detectChanges(); // Trigger change detection manually\n      }\n    }, 100);\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n  clearStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n      this.progressTimer = null;\n    }\n    if (this.progressUpdateTimer) {\n      clearInterval(this.progressUpdateTimer);\n      this.progressUpdateTimer = null;\n    }\n    this.progressStartTime = 0;\n    this.currentProgress = 0;\n  }\n  // Story interaction methods\n  onStoryClick(event) {\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else if (clickX > windowWidth * 2 / 3) {\n      this.nextStory();\n    }\n  }\n  // Touch handling\n  onTouchStart(_event) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.clearStoryTimer(); // Pause story progress on long press\n    }, 500);\n  }\n  onTouchMove(_event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onTouchEnd(event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n    const touchDuration = Date.now() - this.touchStartTime;\n    if (touchDuration < 500) {\n      // Short tap - treat as click\n      const touch = event.changedTouches[0];\n      this.onStoryClick({\n        clientX: touch.clientX\n      });\n    } else {\n      // Long press ended - resume story progress\n      this.startStoryTimer();\n    }\n  }\n  // Product interaction methods\n  toggleProductTags() {\n    this.showProductTags = !this.showProductTags;\n  }\n  openProductDetails(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  // Add product to cart with real-time functionality\n  addToCart(product) {\n    console.log('Adding product to cart:', product);\n    this.buttonActionsService.addToCart({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      quantity: 1,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to cart successfully:', result);\n          // Show success message or toast\n        } else {\n          console.error('Failed to add product to cart:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding product to cart:', error);\n      }\n    });\n  }\n  // Add product to wishlist with real-time functionality\n  addToWishlist(product) {\n    console.log('Adding product to wishlist:', product);\n    this.buttonActionsService.addToWishlist({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully:', result);\n          // Show success message or toast\n        } else {\n          console.error('Failed to add product to wishlist:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding product to wishlist:', error);\n      }\n    });\n  }\n  // Buy now functionality\n  buyNow(product) {\n    console.log('Buy now clicked for product:', product);\n    this.buttonActionsService.buyNow({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      quantity: 1,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Buy now successful:', result);\n          // Navigation to checkout will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error in buy now:', error);\n      }\n    });\n  }\n  // Story actions with real-time functionality\n  toggleLike() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    this.isLiked = !this.isLiked;\n    // Call API to like/unlike story\n    const endpoint = this.isLiked ? 'like' : 'unlike';\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\n      next: response => {\n        console.log(`Story ${endpoint}d successfully:`, response);\n        // Emit real-time event (using socket directly)\n        if (this.realtimeService.isConnected()) {\n          // For now, we'll track this locally until we add story-specific emit methods\n          console.log('Story liked event:', {\n            storyId: currentStory._id,\n            userId: this.authService.currentUserValue?._id,\n            liked: this.isLiked\n          });\n        }\n      },\n      error: error => {\n        console.error(`Error ${endpoint}ing story:`, error);\n        // Revert the like state on error\n        this.isLiked = !this.isLiked;\n      }\n    });\n  }\n  shareStory() {\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    if (navigator.share) {\n      // Use native sharing if available\n      navigator.share({\n        title: `Story by ${currentStory.user.username}`,\n        text: currentStory.caption,\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\n      }).then(() => {\n        console.log('Story shared successfully');\n        // Track share event\n        this.trackStoryShare(currentStory._id);\n      }).catch(error => {\n        console.error('Error sharing story:', error);\n      });\n    } else {\n      // Fallback: copy link to clipboard\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\n      navigator.clipboard.writeText(shareUrl).then(() => {\n        console.log('Story link copied to clipboard');\n        this.trackStoryShare(currentStory._id);\n        // Show toast notification\n        this.showToast('Story link copied to clipboard!');\n      }).catch(error => {\n        console.error('Error copying to clipboard:', error);\n      });\n    }\n  }\n  saveStory() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    // Call API to save story\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\n      next: response => {\n        console.log('Story saved successfully:', response);\n        this.showToast('Story saved to your collection!');\n        // Emit real-time event (using socket directly)\n        if (this.realtimeService.isConnected()) {\n          // For now, we'll track this locally until we add story-specific emit methods\n          console.log('Story saved event:', {\n            storyId: currentStory._id,\n            userId: this.authService.currentUserValue?._id\n          });\n        }\n      },\n      error: error => {\n        console.error('Error saving story:', error);\n        this.showToast('Error saving story. Please try again.');\n      }\n    });\n  }\n  trackStoryShare(storyId) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\n        next: response => {\n          console.log('Story share tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking story share:', error);\n        }\n      });\n    }\n  }\n  // Handle middle area click for product/category navigation\n  handleMiddleAreaClick(event) {\n    event.stopPropagation();\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return;\n    }\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        if (currentStory.linkedContent.productId) {\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\n        } else if (currentStory.products && currentStory.products.length > 0) {\n          // Fallback to first product if no specific productId\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\n        }\n        break;\n      case 'category':\n        if (currentStory.linkedContent.categoryId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              category: currentStory.linkedContent.categoryId\n            }\n          });\n        }\n        break;\n      case 'brand':\n        if (currentStory.linkedContent.brandId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              brand: currentStory.linkedContent.brandId\n            }\n          });\n        }\n        break;\n      case 'collection':\n        if (currentStory.linkedContent.collectionId) {\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\n        }\n        break;\n      default:\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\n    }\n    // Track click event\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\n  }\n  // Get text for linked content indicator\n  getLinkedContentText() {\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return '';\n    }\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        return 'View Product';\n      case 'category':\n        return 'Browse Category';\n      case 'brand':\n        return 'View Brand';\n      case 'collection':\n        return 'View Collection';\n      default:\n        return 'View Details';\n    }\n  }\n  // Track linked content click for analytics\n  trackLinkedContentClick(storyId, linkedContent) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\n        contentType: linkedContent.type,\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\n      }).subscribe({\n        next: response => {\n          console.log('Linked content click tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking linked content click:', error);\n        }\n      });\n    }\n  }\n  showToast(message) {\n    // Simple toast implementation - you can replace with your preferred toast library\n    const toast = document.createElement('div');\n    toast.textContent = message;\n    toast.style.cssText = `\n      position: fixed;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-size: 14px;\n    `;\n    document.body.appendChild(toast);\n    setTimeout(() => {\n      document.body.removeChild(toast);\n    }, 3000);\n  }\n  // Add story functionality\n  onAdd() {\n    this.router.navigate(['/stories/create']);\n  }\n  // Utility methods\n  getTimeAgo(dateString) {\n    if (!dateString) return 'Unknown';\n    const now = new Date();\n    let date;\n    if (typeof dateString === 'string') {\n      date = new Date(dateString);\n    } else {\n      date = dateString;\n    }\n    // Check if date is valid\n    if (isNaN(date.getTime())) {\n      return 'Unknown';\n    }\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CartService), i0.ɵɵdirectiveInject(i5.WishlistService), i0.ɵɵdirectiveInject(i6.SocialMediaService), i0.ɵɵdirectiveInject(i7.RealtimeService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i8.ButtonActionsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesTrack = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow)(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 19,\n      vars: 10,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [\"storyVideo\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [\"type\", \"button\", \"aria-label\", \"Create new story\", \"title\", \"Create a new story\", 1, \"create-story-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-plus\"], [1, \"stories-slider-wrapper\"], [\"type\", \"button\", \"aria-label\", \"Scroll stories left\", \"title\", \"Previous stories\", 1, \"nav-arrow\", \"nav-arrow-left\", 3, \"click\", \"disabled\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-chevron-left\"], [1, \"stories-carousel\", 3, \"options\"], [\"carouselSlide\", \"\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"aria-label\", \"Scroll stories right\", \"title\", \"Next stories\", 1, \"nav-arrow\", \"nav-arrow-right\", 3, \"click\", \"disabled\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-chevron-right\"], [\"class\", \"stories-overlay\", 4, \"ngIf\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus-btn\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [1, \"story-item\", \"loading-story\"], [1, \"story-avatar\", \"loading-avatar\"], [1, \"loading-spinner\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [\"class\", \"shopping-bag-indicator\", \"title\", \"Shoppable content\", 4, \"ngIf\"], [1, \"story-count-badge\"], [\"class\", \"product-count-badge\", 4, \"ngIf\"], [\"title\", \"Shoppable content\", 1, \"shopping-bag-indicator\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-count-badge\"], [1, \"stories-overlay\"], [1, \"stories-content\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [\"alt\", \"Avatar\", 1, \"story-header-avatar\", 3, \"src\"], [1, \"story-header-info\"], [1, \"username\"], [1, \"time\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-media\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [\"class\", \"story-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-video\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-middle-click-area\", 3, \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"shopping-bag-btn\", \"type\", \"button\", 3, \"active\", \"title\", \"click\", 4, \"ngIf\"], [1, \"story-actions\"], [\"type\", \"button\", 1, \"action-btn\", \"like-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"aria-label\", \"Share story\", \"title\", \"Share story\", 1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-share\"], [\"type\", \"button\", \"aria-label\", \"Save story\", \"title\", \"Save story\", 1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-bookmark\"], [\"class\", \"story-nav-btn story-nav-prev\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-nav-btn story-nav-next\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-image\", 3, \"src\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-video\", 3, \"src\"], [1, \"story-middle-click-area\", 3, \"click\", \"title\"], [1, \"middle-click-indicator\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-dot\"], [1, \"product-tag-info\"], [1, \"product-tag-image\", 3, \"src\", \"alt\"], [1, \"product-tag-details\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"product-tag-actions\"], [\"type\", \"button\", \"title\", \"Add to Cart\", 1, \"product-action-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [\"type\", \"button\", \"title\", \"Add to Wishlist\", 1, \"product-action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"title\", \"Buy Now\", 1, \"product-action-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [\"type\", \"button\", 1, \"shopping-bag-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-shopping-bag\"], [\"aria-hidden\", \"true\", 1, \"product-count\"], [1, \"story-nav-btn\", \"story-nav-prev\", 3, \"click\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"story-nav-btn\", \"story-nav-next\", 3, \"click\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3, 0)(2, \"div\", 4)(3, \"h3\", 5);\n          i0.ɵɵtext(4, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelement(6, \"i\", 7);\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"Create\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollLeft());\n          });\n          i0.ɵɵelement(11, \"i\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"owl-carousel-o\", 11);\n          i0.ɵɵtemplate(13, ViewAddStoriesComponent_ng_template_13_Template, 9, 1, \"ng-template\", 12)(14, ViewAddStoriesComponent_14_Template, 1, 0, null, 13)(15, ViewAddStoriesComponent_15_Template, 1, 0, null, 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollRight());\n          });\n          i0.ɵɵelement(17, \"i\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_18_Template, 29, 15, \"div\", 17);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollLeft);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.customOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollRight);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i9.NgForOf, i9.NgIf, FormsModule, CarouselModule, i10.CarouselComponent, i10.CarouselSlideDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  overflow: hidden;\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 8px;\\n    padding: 12px;\\n    border-radius: 12px;\\n  }\\n}\\n\\n.stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  flex: 1;\\n  min-width: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  color: white;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  white-space: nowrap;\\n  min-height: 36px;\\n  flex-shrink: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.5);\\n  outline-offset: 2px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n@media (max-width: 480px) {\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 12px;\\n    min-height: 32px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-header[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n}\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: white;\\n  transform: scale(1.1);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.nav-arrow.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.nav-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n}\\n@media (max-width: 768px) {\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  flex-shrink: 0;\\n  width: calc(16.6666666667% - 16px);\\n  margin: 0 8px;\\n  box-sizing: border-box;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n}\\n@media (max-width: 900px) {\\n  .story-item[_ngcontent-%COMP%] {\\n    width: calc(25% - 12px);\\n    margin: 0 6px;\\n  }\\n}\\n@media (max-width: 600px) {\\n  .story-item[_ngcontent-%COMP%] {\\n    width: calc(33.3333333333% - 12px);\\n    margin: 0 6px;\\n  }\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  padding: 3px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.story-avatar.has-products[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b35, #f7931e, #ff6b35, #f7931e);\\n  background-size: 200% 200%;\\n  animation: _ngcontent-%COMP%_gradientShift 3s ease-in-out infinite;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n}\\n.story-avatar.has-products[_ngcontent-%COMP%]:hover {\\n  animation-duration: 1.5s;\\n  transform: scale(1.05);\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientShift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    width: 54px;\\n    height: 54px;\\n  }\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n\\n\\n.shopping-bag-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -6px;\\n  right: -6px;\\n  background: #ff6b35; \\n\\n  border-radius: 50%;\\n  width: 26px;\\n  height: 26px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 3px solid white;\\n  box-shadow: 0 2px 12px rgba(255, 107, 53, 0.3);\\n  cursor: pointer;\\n  z-index: 2;\\n  transition: all 0.3s ease;\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.4);\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 600;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-indicator[_ngcontent-%COMP%] {\\n    width: 22px;\\n    height: 22px;\\n    bottom: -4px;\\n    right: -4px;\\n    border-width: 2px;\\n  }\\n  .shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .shopping-bag-indicator[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n    bottom: -3px;\\n    right: -3px;\\n  }\\n  .shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: white;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: 80px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n  }\\n}\\n\\n\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #0095f6;\\n  font-weight: 600;\\n  text-shadow: none;\\n}\\n\\n.product-count-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -8px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  white-space: nowrap;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .product-count-badge[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 1px 4px;\\n  }\\n}\\n\\n.story-count-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #8e8e8e;\\n  margin-top: 2px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f9f9f9, #e8e8e8);\\n  border: 2px dashed #c7c7c7;\\n  position: relative;\\n}\\n.add-avatar[_ngcontent-%COMP%]:hover {\\n  border-color: #0095f6;\\n  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);\\n}\\n.add-avatar[_ngcontent-%COMP%]   .story-avatar-img[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n  filter: grayscale(20%);\\n}\\n\\n\\n\\n.add-story-plus-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -6px;\\n  right: -6px;\\n  width: 28px;\\n  height: 28px;\\n  background: #0095f6; \\n\\n  border: 3px solid white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 2;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 12px rgba(0, 149, 246, 0.3);\\n}\\n.add-story-plus-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 16px rgba(0, 149, 246, 0.4);\\n}\\n.add-story-plus-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n@media (max-width: 768px) {\\n  .add-story-plus-btn[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n    bottom: -4px;\\n    right: -4px;\\n    border-width: 2px;\\n  }\\n  .add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .add-story-plus-btn[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    bottom: -3px;\\n    right: -3px;\\n  }\\n  .add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n\\n\\n.stories-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95));\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-overlay[_ngcontent-%COMP%] {\\n    background: #000;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n.stories-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 90%;\\n  max-width: 400px;\\n  height: 80vh;\\n  max-height: 700px;\\n  background: #000;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100vh;\\n    max-height: none;\\n    border-radius: 0;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(50px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n.progress-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 4px;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 3px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n  overflow: hidden;\\n}\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progressFill 5s linear;\\n}\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  border-radius: 2px;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progressFill {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story-header[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);\\n  color: #fff;\\n  z-index: 10;\\n}\\n@media (max-width: 768px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n\\n.story-header-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-header-avatar[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n\\n.story-header-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 2px;\\n}\\n@media (max-width: 768px) {\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-weight: 400;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #fff;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n@media (max-width: 768px) {\\n  .close-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  background: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n\\n.story-middle-click-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 5;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.story-middle-click-area[_ngcontent-%COMP%]:hover {\\n  transform: translate(-50%, -50%) scale(1.1);\\n}\\n\\n.middle-click-indicator[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  animation: _ngcontent-%COMP%_pulseIndicator 2s infinite;\\n}\\n.middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.middle-click-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n@media (max-width: 768px) {\\n  .middle-click-indicator[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    font-size: 12px;\\n  }\\n  .middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulseIndicator {\\n  0% {\\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  }\\n}\\n.story-image[_ngcontent-%COMP%], .story-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.product-tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 3px solid #667eea;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_ripple 2s infinite;\\n}\\n.product-tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #667eea;\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_ripple {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);\\n  }\\n}\\n.product-tag-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  min-width: 200px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.product-tag-image[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.product-tag-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 14px;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #667eea;\\n  font-weight: 700;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.product-tag-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n  justify-content: flex-start;\\n}\\n\\n.product-action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: #333;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.product-action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.product-action-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.product-action-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n}\\n.product-action-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: #f5576c;\\n  color: white;\\n}\\n.product-action-btn.buy-btn[_ngcontent-%COMP%]:hover {\\n  background: #28a745;\\n  color: white;\\n}\\n.product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n@media (max-width: 768px) {\\n  .product-action-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.shopping-bag-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  right: 20px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 56px;\\n  height: 56px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\\n  z-index: 5;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.8);\\n  outline-offset: 2px;\\n}\\n.shopping-bag-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  animation: _ngcontent-%COMP%_bounce 0.6s ease;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: #f5576c;\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  border: 2px solid white;\\n  min-width: 24px;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n    bottom: 16px;\\n    right: 16px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    font-size: 10px;\\n    min-width: 20px;\\n    top: -6px;\\n    right: -6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n    bottom: 12px;\\n    right: 12px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n    font-size: 9px;\\n    min-width: 18px;\\n    top: -5px;\\n    right: -5px;\\n  }\\n}\\n@media (max-height: 600px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    right: 10px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-10px);\\n  }\\n  80% {\\n    transform: translateY(-5px);\\n  }\\n}\\n.story-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  left: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  z-index: 5;\\n}\\n@media (max-width: 768px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 16px;\\n    left: 16px;\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 12px;\\n    left: 12px;\\n    gap: 10px;\\n  }\\n}\\n@media (max-width: 768px) and (max-height: 600px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    left: 10px;\\n    gap: 8px;\\n  }\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n}\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.action-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.action-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.6);\\n  outline-offset: 2px;\\n}\\n.action-btn.liked[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  color: white;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n@media (max-width: 768px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    min-width: 44px;\\n    min-height: 44px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n.story-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n  z-index: 5;\\n}\\n.story-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.story-nav-btn.story-nav-prev[_ngcontent-%COMP%] {\\n  left: 20px;\\n}\\n.story-nav-btn.story-nav-next[_ngcontent-%COMP%] {\\n  right: 20px;\\n}\\n.story-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n@media (max-width: 768px) {\\n  .story-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n    padding: 8px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .stories-content[_ngcontent-%COMP%]   .shopping-bag-btn[_ngcontent-%COMP%]:not(:only-child) {\\n    right: 16px;\\n    bottom: 80px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 4px;\\n    padding: 8px;\\n    border-radius: 8px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n    padding: 6px;\\n    bottom: 20px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n}\\n@media (max-width: 768px) and (orientation: landscape) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    height: 100vh;\\n    max-height: none;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    right: 10px;\\n  }\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    left: 10px;\\n    gap: 8px;\\n  }\\n}\\n.story-item.loading[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .shopping-bag-btn[_ngcontent-%COMP%]:focus, .nav-arrow[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #667eea;\\n  outline-offset: 2px;\\n}\\n\\n@media (prefers-contrast: high) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    border: 2px solid #000;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    border: 2px solid #fff;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "ViewAddStoriesComponent_ng_template_13_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onAdd", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "currentUser", "avatar", "ɵɵsanitizeUrl", "ɵɵtemplate", "ViewAddStoriesComponent_14_ng_template_0_Template", "ɵɵtextInterpolate2", "userGroup_r6", "totalProducts", "ViewAddStoriesComponent_15_ng_template_0_Template_div_click_0_listener", "_r4", "ctx_r4", "$implicit", "i_r7", "index", "openUserStories", "ViewAddStoriesComponent_15_ng_template_0_div_5_Template", "ViewAddStoriesComponent_15_ng_template_0_div_10_Template", "ɵɵclassProp", "hasProducts", "user", "username", "ɵɵtextInterpolate", "stories", "length", "ViewAddStoriesComponent_15_ng_template_0_Template", "i_r9", "currentStoryIndex", "ɵɵstyleProp", "getProgressWidth", "tmp_4_0", "getCurrentStory", "mediaUrl", "tmp_5_0", "ViewAddStoriesComponent_div_18_div_17_Template_div_click_0_listener", "$event", "_r10", "handleMiddleAreaClick", "ɵɵpropertyInterpolate1", "linkedContent", "type", "getLinkedContentText", "ViewAddStoriesComponent_div_18_div_18_div_1_Template_div_click_0_listener", "productTag_r12", "_r11", "product", "openProductDetails", "stopPropagation", "ViewAddStoriesComponent_div_18_div_18_div_1_Template_button_click_10_listener", "addToCart", "ViewAddStoriesComponent_div_18_div_18_div_1_Template_button_click_12_listener", "addToWishlist", "ViewAddStoriesComponent_div_18_div_18_div_1_Template_button_click_14_listener", "buyNow", "position", "x", "y", "images", "url", "name", "ɵɵtextInterpolate1", "price", "ViewAddStoriesComponent_div_18_div_18_div_1_Template", "products", "ViewAddStoriesComponent_div_18_button_19_Template_button_click_0_listener", "_r13", "toggleProductTags", "showProductTags", "getProductCount", "ViewAddStoriesComponent_div_18_button_27_Template_button_click_0_listener", "_r14", "previousStory", "ViewAddStoriesComponent_div_18_button_28_Template_button_click_0_listener", "_r15", "nextStory", "ViewAddStoriesComponent_div_18_div_4_Template", "ViewAddStoriesComponent_div_18_Template_button_click_12_listener", "_r8", "closeStories", "ViewAddStoriesComponent_div_18_Template_div_click_14_listener", "onStoryClick", "ViewAddStoriesComponent_div_18_Template_div_touchstart_14_listener", "onTouchStart", "ViewAddStoriesComponent_div_18_Template_div_touchmove_14_listener", "onTouchMove", "ViewAddStoriesComponent_div_18_Template_div_touchend_14_listener", "onTouchEnd", "ViewAddStoriesComponent_div_18_img_15_Template", "ViewAddStoriesComponent_div_18_video_16_Template", "ViewAddStoriesComponent_div_18_div_17_Template", "ViewAddStoriesComponent_div_18_div_18_Template", "ViewAddStoriesComponent_div_18_button_19_Template", "ViewAddStoriesComponent_div_18_Template_button_click_21_listener", "toggleLike", "ViewAddStoriesComponent_div_18_Template_button_click_23_listener", "shareStory", "ViewAddStoriesComponent_div_18_Template_button_click_25_listener", "saveStory", "ViewAddStoriesComponent_div_18_button_27_Template", "ViewAddStoriesComponent_div_18_button_28_Template", "getTimeAgo", "tmp_6_0", "createdAt", "tmp_7_0", "mediaType", "tmp_8_0", "tmp_9_0", "tmp_10_0", "isLiked", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "cartService", "wishlistService", "socialMediaService", "realtimeService", "cdr", "buttonActionsService", "isLoadingStories", "isOpen", "currentUserIndex", "currentUserStories", "storyProgress", "canScrollLeft", "canScrollRight", "showNavArrows", "translateX", "itemWidth", "visibleItems", "currentSlideIndex", "storyDuration", "progressStartTime", "currentProgress", "touchStartTime", "customOptions", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "responsive", "items", "nav", "subscriptions", "console", "log", "ngOnInit", "loadStories", "currentUser$", "subscribe", "updateNavArrowsVisibility", "ngAfterViewInit", "setTimeout", "calculateSliderDimensions", "updateScrollButtons", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "clearStoryTimer", "onResize", "window", "innerWidth", "storiesSlider", "nativeElement", "containerWidth", "offsetWidth", "screenWidth", "warn", "push", "get", "apiUrl", "next", "response", "success", "allStories", "filter", "story", "isActive", "map", "media", "groupStoriesByUser", "loadFallbackStories", "error", "userStoriesMap", "Map", "userId", "_id", "has", "set", "latestStoryTime", "userGroup", "Date", "Array", "from", "values", "sort", "a", "b", "getTime", "scrollLeft", "updateSliderPosition", "scrollRight", "totalItems", "maxSlideIndex", "Math", "max", "currentIndex", "newTranslateX", "userIndex", "startStoryTimer", "document", "body", "style", "overflow", "openStory", "_story", "pauseAllVideos", "getCurrentUser", "now", "progressUpdateTimer", "setInterval", "elapsed", "min", "detectChanges", "progressTimer", "clearTimeout", "clearInterval", "event", "clickX", "clientX", "windowWidth", "_event", "longPressTimer", "touchDuration", "touch", "changedTouches", "navigate", "productId", "size", "color", "quantity", "addedFrom", "result", "message", "isAuthenticated", "currentStory", "endpoint", "post", "isConnected", "storyId", "currentUserValue", "liked", "navigator", "share", "title", "text", "caption", "frontendUrl", "then", "trackStoryShare", "catch", "shareUrl", "clipboard", "writeText", "showToast", "categoryId", "queryParams", "category", "brandId", "brand", "collectionId", "trackLinkedContentClick", "contentType", "contentId", "toast", "createElement", "textContent", "cssText", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateString", "date", "isNaN", "diffInMinutes", "floor", "diffInHours", "diffInDays", "videos", "querySelectorAll", "video", "pause", "handleKeydown", "key", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "i4", "CartService", "i5", "WishlistService", "i6", "SocialMediaService", "i7", "RealtimeService", "ChangeDetectorRef", "i8", "ButtonActionsService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_Template_button_click_5_listener", "_r1", "ViewAddStoriesComponent_Template_button_click_10_listener", "ViewAddStoriesComponent_ng_template_13_Template", "ViewAddStoriesComponent_14_Template", "ViewAddStoriesComponent_15_Template", "ViewAddStoriesComponent_Template_button_click_16_listener", "ViewAddStoriesComponent_div_18_Template", "i9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i10", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild, HostListener, AfterViewInit, ChangeDetectorRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Subscription } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CartService } from 'src/app/core/services/cart.service';\r\nimport { WishlistService } from 'src/app/core/services/wishlist.service';\r\nimport { SocialMediaService } from 'src/app/core/services/social-media.service';\r\nimport { RealtimeService } from 'src/app/core/services/realtime.service';\r\nimport { ButtonActionsService } from 'src/app/core/services/button-actions.service';\r\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\r\n\r\ninterface Story {\r\n  _id: string;\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  media: {\r\n    type: 'image' | 'video';\r\n    url: string;\r\n    thumbnail?: string;\r\n    duration?: number;\r\n  };\r\n  mediaUrl: string; // For backward compatibility\r\n  mediaType: 'image' | 'video'; // For backward compatibility\r\n  caption?: string;\r\n  createdAt: string;\r\n  expiresAt: string;\r\n  views: number;\r\n  isActive: boolean;\r\n  linkedContent?: {\r\n    type: 'product' | 'category' | 'brand' | 'collection';\r\n    productId?: string;\r\n    categoryId?: string;\r\n    brandId?: string;\r\n    collectionId?: string;\r\n  };\r\n  products?: Array<{\r\n    _id: string;\r\n    product: {\r\n      _id: string;\r\n      name: string;\r\n      price: number;\r\n      images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;\r\n    };\r\n    position?: {\r\n      x: number;\r\n      y: number;\r\n    };\r\n    size?: string;\r\n    color?: string;\r\n  }>;\r\n}\r\n\r\ninterface UserStoryGroup {\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  stories: Story[];\r\n  hasProducts: boolean;\r\n  totalProducts: number;\r\n  latestStoryTime: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-view-add-stories',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, CarouselModule],\r\n  templateUrl: './view-add-stories.component.html',\r\n  styleUrls: ['./view-add-stories.component.scss']\r\n})\r\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storiesTrack', { static: false }) storiesTrack!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storyVideo', { static: false }) storyVideo!: ElementRef<HTMLVideoElement>;\r\n\r\n  currentUser: any = null;\r\n  stories: UserStoryGroup[] = [];\r\n  isLoadingStories = true;\r\n\r\n  // Story viewer state\r\n  isOpen = false;\r\n  currentStoryIndex = 0;\r\n  currentUserIndex = 0;\r\n  currentUserStories: Story[] = [];\r\n  showProductTags = false;\r\n  isLiked = false;\r\n  storyTimer: any;\r\n  storyProgress = 0;\r\n\r\n  // Navigation state\r\n  canScrollLeft = false;\r\n  canScrollRight = false;\r\n  showNavArrows = true;\r\n\r\n  // Custom slider properties\r\n  translateX = 0;\r\n  itemWidth = 80; // Width of each story item including margin\r\n  visibleItems = 6; // Number of visible items\r\n  currentSlideIndex = 0;\r\n\r\n  // Progress tracking\r\n  private progressTimer: any;\r\n  private progressUpdateTimer: any;\r\n  private storyDuration = 15000; // 15 seconds default\r\n  private progressStartTime = 0;\r\n  currentProgress = 0; // Stable progress value for template binding\r\n\r\n  // Touch handling\r\n  private touchStartTime = 0;\r\n  private longPressTimer: any;\r\n\r\n  // Carousel options\r\n  customOptions: OwlOptions = {\r\n    loop: false,\r\n    mouseDrag: true,\r\n    touchDrag: true,\r\n    pullDrag: false,\r\n    dots: false,\r\n    navSpeed: 700,\r\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\r\n    responsive: {\r\n      0: {\r\n        items: 3,\r\n        nav: false\r\n      },\r\n      600: {\r\n        items: 4,\r\n        nav: false\r\n      },\r\n      900: {\r\n        items: 6,\r\n        nav: true\r\n      }\r\n    },\r\n    nav: true\r\n  };\r\n\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private authService: AuthService,\r\n    private cartService: CartService,\r\n    private wishlistService: WishlistService,\r\n    private socialMediaService: SocialMediaService,\r\n    private realtimeService: RealtimeService,\r\n    private cdr: ChangeDetectorRef,\r\n    private buttonActionsService: ButtonActionsService\r\n  ) {\r\n    console.log('🏗️ ViewAddStoriesComponent constructor called');\r\n  }\r\n\r\n  ngOnInit() {\r\n    console.log('🚀 ViewAddStoriesComponent ngOnInit called');\r\n    this.loadStories();\r\n    this.authService.currentUser$.subscribe(user => {\r\n      this.currentUser = user;\r\n      console.log('👤 Current user updated:', user);\r\n    });\r\n\r\n    // Check if we should show navigation arrows based on screen size\r\n    this.updateNavArrowsVisibility();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    console.log('ngAfterViewInit called');\r\n    setTimeout(() => {\r\n      console.log('Initializing slider after view init');\r\n      this.calculateSliderDimensions();\r\n      this.updateScrollButtons();\r\n    }, 500);\r\n\r\n    // Also try immediate initialization\r\n    setTimeout(() => {\r\n      if (this.stories.length > 0) {\r\n        console.log('Re-initializing slider with stories');\r\n        this.calculateSliderDimensions();\r\n        this.updateScrollButtons();\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n    this.clearStoryTimer();\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize() {\r\n    this.updateNavArrowsVisibility();\r\n    this.calculateSliderDimensions();\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  private updateNavArrowsVisibility() {\r\n    this.showNavArrows = window.innerWidth > 768;\r\n  }\r\n\r\n  private calculateSliderDimensions() {\r\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\r\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\r\n      const screenWidth = window.innerWidth;\r\n\r\n      console.log('Calculating slider dimensions:', { containerWidth, screenWidth });\r\n\r\n      // Responsive visible items\r\n      if (screenWidth <= 600) {\r\n        this.visibleItems = 3;\r\n        this.itemWidth = containerWidth / 3;\r\n      } else if (screenWidth <= 900) {\r\n        this.visibleItems = 4;\r\n        this.itemWidth = containerWidth / 4;\r\n      } else {\r\n        this.visibleItems = 6;\r\n        this.itemWidth = containerWidth / 6;\r\n      }\r\n\r\n      console.log('Slider dimensions calculated:', {\r\n        visibleItems: this.visibleItems,\r\n        itemWidth: this.itemWidth\r\n      });\r\n\r\n      // Reset slider position if needed\r\n      this.currentSlideIndex = 0;\r\n      this.translateX = 0;\r\n    } else {\r\n      console.warn('Stories slider element not found, using default dimensions');\r\n      // Fallback dimensions\r\n      this.itemWidth = 100;\r\n      this.visibleItems = 6;\r\n    }\r\n  }\r\n\r\n  loadStories() {\r\n    this.isLoadingStories = true;\r\n    this.subscriptions.push(\r\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\r\n        next: (response) => {\r\n          console.log('Stories API response:', response);\r\n          if (response.success && response.stories && response.stories.length > 0) {\r\n            // Filter only active stories and map the data structure\r\n            const allStories = response.stories\r\n              .filter((story: any) => story.isActive)\r\n              .map((story: any) => ({\r\n                ...story,\r\n                mediaUrl: story.media?.url || story.mediaUrl,\r\n                mediaType: story.media?.type || story.mediaType\r\n              }));\r\n\r\n            // Group stories by user (Instagram style)\r\n            this.stories = this.groupStoriesByUser(allStories);\r\n            console.log('Loaded and grouped stories from API:', this.stories);\r\n            console.log('Total user story groups:', this.stories.length);\r\n          } else {\r\n            console.log('No stories from API, loading fallback stories');\r\n            this.loadFallbackStories();\r\n          }\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading stories:', error);\r\n          console.log('Loading fallback stories due to error');\r\n          this.loadFallbackStories();\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  // Group stories by user like Instagram\r\n  private groupStoriesByUser(allStories: Story[]): UserStoryGroup[] {\r\n    const userStoriesMap = new Map();\r\n\r\n    allStories.forEach(story => {\r\n      const userId = story.user._id;\r\n      if (!userStoriesMap.has(userId)) {\r\n        userStoriesMap.set(userId, {\r\n          user: story.user,\r\n          stories: [],\r\n          hasProducts: false,\r\n          totalProducts: 0,\r\n          latestStoryTime: story.createdAt\r\n        });\r\n      }\r\n\r\n      const userGroup = userStoriesMap.get(userId);\r\n      userGroup.stories.push(story);\r\n\r\n      // Check if any story has products\r\n      if (story.products && story.products.length > 0) {\r\n        userGroup.hasProducts = true;\r\n        userGroup.totalProducts += story.products.length;\r\n      }\r\n\r\n      // Keep track of latest story time for sorting\r\n      if (new Date(story.createdAt) > new Date(userGroup.latestStoryTime)) {\r\n        userGroup.latestStoryTime = story.createdAt;\r\n      }\r\n    });\r\n\r\n    // Convert map to array and sort by latest story time\r\n    return Array.from(userStoriesMap.values())\r\n      .sort((a, b) => new Date(b.latestStoryTime).getTime() - new Date(a.latestStoryTime).getTime());\r\n  }\r\n\r\n  loadFallbackStories() {\r\n    console.log('❌ No stories available from API');\r\n    this.stories = [];\r\n  }\r\n\r\n  // Navigation methods\r\n  scrollLeft() {\r\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\r\n    if (this.currentSlideIndex > 0) {\r\n      this.currentSlideIndex--;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled left to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll left, already at start');\r\n    }\r\n  }\r\n\r\n  scrollRight() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    console.log('Scroll right clicked:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      visibleItems: this.visibleItems\r\n    });\r\n\r\n    if (this.currentSlideIndex < maxSlideIndex) {\r\n      this.currentSlideIndex++;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled right to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll right, already at end');\r\n    }\r\n  }\r\n\r\n  private updateSliderPosition() {\r\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\r\n    console.log('Updating slider position:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      itemWidth: this.itemWidth,\r\n      newTranslateX\r\n    });\r\n\r\n    this.translateX = newTranslateX;\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  updateScrollButtons() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    this.canScrollLeft = this.currentSlideIndex > 0;\r\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\r\n\r\n    console.log('Updated scroll buttons:', {\r\n      canScrollLeft: this.canScrollLeft,\r\n      canScrollRight: this.canScrollRight,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      currentIndex: this.currentSlideIndex\r\n    });\r\n  }\r\n\r\n  // Story viewer methods\r\n  openUserStories(userGroup: UserStoryGroup, userIndex: number) {\r\n    this.currentUserIndex = userIndex;\r\n    this.currentStoryIndex = 0; // Start with first story of this user\r\n    this.currentUserStories = userGroup.stories;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n    console.log('Opening stories for user:', userGroup.user.username, 'Stories count:', userGroup.stories.length);\r\n  }\r\n\r\n  openStory(_story: Story, index: number) {\r\n    // Legacy method - keeping for compatibility\r\n    this.currentStoryIndex = index;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n  }\r\n\r\n  closeStories() {\r\n    this.isOpen = false;\r\n    this.clearStoryTimer();\r\n    this.pauseAllVideos();\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  nextStory() {\r\n    // First check if there are more stories for current user\r\n    if (this.currentStoryIndex < this.currentUserStories.length - 1) {\r\n      this.currentStoryIndex++;\r\n      this.showProductTags = false;\r\n      this.startStoryTimer();\r\n    } else {\r\n      // Move to next user's stories\r\n      if (this.currentUserIndex < this.stories.length - 1) {\r\n        this.currentUserIndex++;\r\n        this.currentStoryIndex = 0;\r\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\r\n        this.showProductTags = false;\r\n        this.startStoryTimer();\r\n      } else {\r\n        this.closeStories();\r\n      }\r\n    }\r\n  }\r\n\r\n  previousStory() {\r\n    // First check if there are previous stories for current user\r\n    if (this.currentStoryIndex > 0) {\r\n      this.currentStoryIndex--;\r\n      this.showProductTags = false;\r\n      this.startStoryTimer();\r\n    } else {\r\n      // Move to previous user's stories (last story)\r\n      if (this.currentUserIndex > 0) {\r\n        this.currentUserIndex--;\r\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\r\n        this.currentStoryIndex = this.currentUserStories.length - 1;\r\n        this.showProductTags = false;\r\n        this.startStoryTimer();\r\n      } else {\r\n        this.closeStories();\r\n      }\r\n    }\r\n  }\r\n\r\n  getCurrentStory(): Story | null {\r\n    if (this.currentUserStories && this.currentUserStories.length > 0) {\r\n      return this.currentUserStories[this.currentStoryIndex] || this.currentUserStories[0];\r\n    }\r\n    // Fallback for legacy usage - get first story from first user group\r\n    if (this.stories && this.stories.length > 0 && this.stories[0].stories.length > 0) {\r\n      return this.stories[0].stories[0];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  getCurrentUser(): any {\r\n    if (this.stories && this.stories.length > 0) {\r\n      return this.stories[this.currentUserIndex]?.user;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  hasProducts(): boolean {\r\n    const story = this.getCurrentStory();\r\n    return !!(story && story.products && story.products.length > 0);\r\n  }\r\n\r\n  getProductCount(): number {\r\n    const story = this.getCurrentStory();\r\n    return story?.products?.length || 0;\r\n  }\r\n\r\n  // Progress tracking\r\n  getProgressWidth(index: number): number {\r\n    if (index < this.currentStoryIndex) return 100;\r\n    if (index > this.currentStoryIndex) return 0;\r\n\r\n    // Return the stable progress value for the current story\r\n    return this.currentProgress;\r\n  }\r\n\r\n  private startStoryTimer() {\r\n    this.clearStoryTimer();\r\n    this.progressStartTime = Date.now();\r\n    this.currentProgress = 0;\r\n\r\n    // Update progress every 100ms for smooth animation\r\n    this.progressUpdateTimer = setInterval(() => {\r\n      if (this.progressStartTime) {\r\n        const elapsed = Date.now() - this.progressStartTime;\r\n        this.currentProgress = Math.min((elapsed / this.storyDuration) * 100, 100);\r\n        this.cdr.detectChanges(); // Trigger change detection manually\r\n      }\r\n    }, 100);\r\n\r\n    this.progressTimer = setTimeout(() => {\r\n      this.nextStory();\r\n    }, this.storyDuration);\r\n  }\r\n\r\n  private clearStoryTimer() {\r\n    if (this.progressTimer) {\r\n      clearTimeout(this.progressTimer);\r\n      this.progressTimer = null;\r\n    }\r\n    if (this.progressUpdateTimer) {\r\n      clearInterval(this.progressUpdateTimer);\r\n      this.progressUpdateTimer = null;\r\n    }\r\n    this.progressStartTime = 0;\r\n    this.currentProgress = 0;\r\n  }\r\n\r\n  // Story interaction methods\r\n  onStoryClick(event: MouseEvent) {\r\n    const clickX = event.clientX;\r\n    const windowWidth = window.innerWidth;\r\n\r\n    if (clickX < windowWidth / 3) {\r\n      this.previousStory();\r\n    } else if (clickX > (windowWidth * 2) / 3) {\r\n      this.nextStory();\r\n    }\r\n  }\r\n\r\n  // Touch handling\r\n  onTouchStart(_event: TouchEvent) {\r\n    this.touchStartTime = Date.now();\r\n    this.longPressTimer = setTimeout(() => {\r\n      this.clearStoryTimer(); // Pause story progress on long press\r\n    }, 500);\r\n  }\r\n\r\n  onTouchMove(_event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n  }\r\n\r\n  onTouchEnd(event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n\r\n    const touchDuration = Date.now() - this.touchStartTime;\r\n    if (touchDuration < 500) {\r\n      // Short tap - treat as click\r\n      const touch = event.changedTouches[0];\r\n      this.onStoryClick({ clientX: touch.clientX } as MouseEvent);\r\n    } else {\r\n      // Long press ended - resume story progress\r\n      this.startStoryTimer();\r\n    }\r\n  }\r\n\r\n  // Product interaction methods\r\n  toggleProductTags() {\r\n    this.showProductTags = !this.showProductTags;\r\n  }\r\n\r\n  openProductDetails(product: any) {\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  // Add product to cart with real-time functionality\r\n  addToCart(product: any) {\r\n    console.log('Adding product to cart:', product);\r\n\r\n    this.buttonActionsService.addToCart({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      quantity: 1,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Product added to cart successfully:', result);\r\n          // Show success message or toast\r\n        } else {\r\n          console.error('Failed to add product to cart:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to cart:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Add product to wishlist with real-time functionality\r\n  addToWishlist(product: any) {\r\n    console.log('Adding product to wishlist:', product);\r\n\r\n    this.buttonActionsService.addToWishlist({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Product added to wishlist successfully:', result);\r\n          // Show success message or toast\r\n        } else {\r\n          console.error('Failed to add product to wishlist:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to wishlist:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Buy now functionality\r\n  buyNow(product: any) {\r\n    console.log('Buy now clicked for product:', product);\r\n\r\n    this.buttonActionsService.buyNow({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      quantity: 1,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Buy now successful:', result);\r\n          // Navigation to checkout will be handled by the service\r\n        } else {\r\n          console.error('Failed to buy now:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error in buy now:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Story actions with real-time functionality\r\n  toggleLike() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    this.isLiked = !this.isLiked;\r\n\r\n    // Call API to like/unlike story\r\n    const endpoint = this.isLiked ? 'like' : 'unlike';\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log(`Story ${endpoint}d successfully:`, response);\r\n        // Emit real-time event (using socket directly)\r\n        if (this.realtimeService.isConnected()) {\r\n          // For now, we'll track this locally until we add story-specific emit methods\r\n          console.log('Story liked event:', {\r\n            storyId: currentStory._id,\r\n            userId: this.authService.currentUserValue?._id,\r\n            liked: this.isLiked\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error(`Error ${endpoint}ing story:`, error);\r\n        // Revert the like state on error\r\n        this.isLiked = !this.isLiked;\r\n      }\r\n    });\r\n  }\r\n\r\n  shareStory() {\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    if (navigator.share) {\r\n      // Use native sharing if available\r\n      navigator.share({\r\n        title: `Story by ${currentStory.user.username}`,\r\n        text: currentStory.caption,\r\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\r\n      }).then(() => {\r\n        console.log('Story shared successfully');\r\n        // Track share event\r\n        this.trackStoryShare(currentStory._id);\r\n      }).catch((error) => {\r\n        console.error('Error sharing story:', error);\r\n      });\r\n    } else {\r\n      // Fallback: copy link to clipboard\r\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\r\n      navigator.clipboard.writeText(shareUrl).then(() => {\r\n        console.log('Story link copied to clipboard');\r\n        this.trackStoryShare(currentStory._id);\r\n        // Show toast notification\r\n        this.showToast('Story link copied to clipboard!');\r\n      }).catch((error) => {\r\n        console.error('Error copying to clipboard:', error);\r\n      });\r\n    }\r\n  }\r\n\r\n  saveStory() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    // Call API to save story\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log('Story saved successfully:', response);\r\n        this.showToast('Story saved to your collection!');\r\n        // Emit real-time event (using socket directly)\r\n        if (this.realtimeService.isConnected()) {\r\n          // For now, we'll track this locally until we add story-specific emit methods\r\n          console.log('Story saved event:', {\r\n            storyId: currentStory._id,\r\n            userId: this.authService.currentUserValue?._id\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error saving story:', error);\r\n        this.showToast('Error saving story. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private trackStoryShare(storyId: string) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\r\n        next: (response) => {\r\n          console.log('Story share tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking story share:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Handle middle area click for product/category navigation\r\n  handleMiddleAreaClick(event: Event) {\r\n    event.stopPropagation();\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return;\r\n    }\r\n\r\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        if (currentStory.linkedContent.productId) {\r\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\r\n        } else if (currentStory.products && currentStory.products.length > 0) {\r\n          // Fallback to first product if no specific productId\r\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\r\n        }\r\n        break;\r\n\r\n      case 'category':\r\n        if (currentStory.linkedContent.categoryId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { category: currentStory.linkedContent.categoryId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'brand':\r\n        if (currentStory.linkedContent.brandId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { brand: currentStory.linkedContent.brandId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'collection':\r\n        if (currentStory.linkedContent.collectionId) {\r\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\r\n        }\r\n        break;\r\n\r\n      default:\r\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\r\n    }\r\n\r\n    // Track click event\r\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\r\n  }\r\n\r\n  // Get text for linked content indicator\r\n  getLinkedContentText(): string {\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return '';\r\n    }\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        return 'View Product';\r\n      case 'category':\r\n        return 'Browse Category';\r\n      case 'brand':\r\n        return 'View Brand';\r\n      case 'collection':\r\n        return 'View Collection';\r\n      default:\r\n        return 'View Details';\r\n    }\r\n  }\r\n\r\n  // Track linked content click for analytics\r\n  private trackLinkedContentClick(storyId: string, linkedContent: any) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\r\n        contentType: linkedContent.type,\r\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\r\n      }).subscribe({\r\n        next: (response) => {\r\n          console.log('Linked content click tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking linked content click:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private showToast(message: string) {\r\n    // Simple toast implementation - you can replace with your preferred toast library\r\n    const toast = document.createElement('div');\r\n    toast.textContent = message;\r\n    toast.style.cssText = `\r\n      position: fixed;\r\n      bottom: 20px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: white;\r\n      padding: 12px 24px;\r\n      border-radius: 8px;\r\n      z-index: 10000;\r\n      font-size: 14px;\r\n    `;\r\n    document.body.appendChild(toast);\r\n\r\n    setTimeout(() => {\r\n      document.body.removeChild(toast);\r\n    }, 3000);\r\n  }\r\n\r\n  // Add story functionality\r\n  onAdd() {\r\n    this.router.navigate(['/stories/create']);\r\n  }\r\n\r\n  // Utility methods\r\n  getTimeAgo(dateString: string | Date | undefined): string {\r\n    if (!dateString) return 'Unknown';\r\n\r\n    const now = new Date();\r\n    let date: Date;\r\n\r\n    if (typeof dateString === 'string') {\r\n      date = new Date(dateString);\r\n    } else {\r\n      date = dateString;\r\n    }\r\n\r\n    // Check if date is valid\r\n    if (isNaN(date.getTime())) {\r\n      return 'Unknown';\r\n    }\r\n\r\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\r\n\r\n    if (diffInMinutes < 1) return 'now';\r\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\r\n\r\n    const diffInHours = Math.floor(diffInMinutes / 60);\r\n    if (diffInHours < 24) return `${diffInHours}h`;\r\n\r\n    const diffInDays = Math.floor(diffInHours / 24);\r\n    return `${diffInDays}d`;\r\n  }\r\n\r\n  private pauseAllVideos() {\r\n    const videos = document.querySelectorAll('video');\r\n    videos.forEach(video => {\r\n      if (video.pause) {\r\n        video.pause();\r\n      }\r\n    });\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeydown(event: KeyboardEvent) {\r\n    if (!this.isOpen) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        this.previousStory();\r\n        break;\r\n      case 'ArrowRight':\r\n        this.nextStory();\r\n        break;\r\n      case 'Escape':\r\n        this.closeStories();\r\n        break;\r\n    }\r\n  }\r\n}", "<div class=\"stories-container\" #storiesContainer>\n  <!-- Debug Info -->\n\n\n  <!-- Stories Header -->\n  <div class=\"stories-header\">\n    <h3 class=\"stories-title\">Stories</h3>\n    <button class=\"create-story-btn\"\n            (click)=\"onAdd()\"\n            type=\"button\"\n            aria-label=\"Create new story\"\n            title=\"Create a new story\">\n      <i class=\"fas fa-plus\" aria-hidden=\"true\"></i>\n      <span>Create</span>\n    </button>\n  </div>\n\n  <!-- Stories Slider with Navigation -->\n  <div class=\"stories-slider-wrapper\">\n    <!-- Navigation Arrow Left -->\n    <button class=\"nav-arrow nav-arrow-left\"\n            (click)=\"scrollLeft()\"\n            [disabled]=\"!canScrollLeft\"\n            [class.hidden]=\"!showNavArrows\"\n            type=\"button\"\n            aria-label=\"Scroll stories left\"\n            title=\"Previous stories\">\n      <i class=\"fas fa-chevron-left\" aria-hidden=\"true\"></i>\n    </button>\n\n    <!-- Custom Stories Slider -->\n    <!-- Stories Carousel using ngx-owl-carousel-o -->\n    <owl-carousel-o [options]=\"customOptions\" class=\"stories-carousel\">\n      <!-- Add Story Button -->\n      <ng-template carouselSlide>\n        <div class=\"story-item add-story-item\" (click)=\"onAdd()\">\n          <div class=\"story-avatar-container\">\n            <div class=\"story-avatar add-avatar\">\n              <div class=\"story-avatar-inner\">\n                <img\n                  class=\"story-avatar-img\"\n                  [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\"\n                  alt=\"Your Story\"\n                />\n              </div>\n              <!-- Plus button positioned outside the circle like Instagram -->\n              <div class=\"add-story-plus-btn\">\n                <i class=\"fas fa-plus\"></i>\n              </div>\n            </div>\n          </div>\n          <div class=\"story-username\">Your Story</div>\n        </div>\n      </ng-template>\n\n      <!-- Loading State -->\n      <ng-template carouselSlide *ngIf=\"isLoadingStories\">\n        <div class=\"story-item loading-story\">\n          <div class=\"story-avatar-container\">\n            <div class=\"story-avatar loading-avatar\">\n              <div class=\"story-avatar-inner\">\n                <div class=\"loading-spinner\"></div>\n              </div>\n            </div>\n          </div>\n          <div class=\"story-username\">Loading...</div>\n        </div>\n      </ng-template>\n\n      <!-- User Story Groups (Instagram style) -->\n      <ng-template carouselSlide *ngFor=\"let userGroup of stories; let i = index\">\n        <div class=\"story-item\" (click)=\"openUserStories(userGroup, i)\">\n          <div class=\"story-avatar-container\">\n            <div class=\"story-avatar\" [class.has-products]=\"userGroup.hasProducts\">\n              <div class=\"story-avatar-inner\">\n                <img\n                  class=\"story-avatar-img\"\n                  [src]=\"userGroup.user.avatar\"\n                  [alt]=\"userGroup.user.username\"\n                />\n              </div>\n              <!-- Shopping bag indicator positioned outside the circle like Instagram -->\n              <div class=\"shopping-bag-indicator\"\n                   *ngIf=\"userGroup.hasProducts\"\n                   title=\"Shoppable content\">\n                <i class=\"fas fa-shopping-bag\"></i>\n              </div>\n            </div>\n          </div>\n          <div class=\"story-username\">{{ userGroup.user.username }}</div>\n          <!-- Story count and product count badge -->\n          <div class=\"story-count-badge\">\n            {{ userGroup.stories.length }} {{ userGroup.stories.length === 1 ? 'story' : 'stories' }}\n          </div>\n          <div class=\"product-count-badge\"\n               *ngIf=\"userGroup.hasProducts\">\n            {{ userGroup.totalProducts }} item{{ userGroup.totalProducts > 1 ? 's' : '' }}\n          </div>\n        </div>\n      </ng-template>\n    </owl-carousel-o>\n\n    <!-- Navigation Arrow Right -->\n    <button class=\"nav-arrow nav-arrow-right\"\n            (click)=\"scrollRight()\"\n            [disabled]=\"!canScrollRight\"\n            [class.hidden]=\"!showNavArrows\"\n            type=\"button\"\n            aria-label=\"Scroll stories right\"\n            title=\"Next stories\">\n      <i class=\"fas fa-chevron-right\" aria-hidden=\"true\"></i>\n    </button>\n  </div>\n</div>\n\n<!-- Fullscreen Stories Viewer (shown when isOpen is true) -->\n<div class=\"stories-overlay\" *ngIf=\"isOpen\">\n  <div class=\"stories-content\" #feedCover>\n    <!-- Progress Bars -->\n    <div class=\"progress-container\">\n      <div class=\"progress-bar\"\n           *ngFor=\"let story of stories; let i = index\"\n           [class.active]=\"i === currentStoryIndex\"\n           [class.completed]=\"i < currentStoryIndex\">\n        <div class=\"progress-fill\"\n             [style.width.%]=\"getProgressWidth(i)\"></div>\n      </div>\n    </div>\n\n    <!-- Story Header -->\n    <div class=\"story-header\">\n      <img class=\"story-header-avatar\" [src]=\"getCurrentStory()?.user?.avatar\" alt=\"Avatar\"/>\n      <div class=\"story-header-info\">\n        <span class=\"username\">{{ getCurrentStory()?.user?.username }}</span>\n        <span class=\"time\">{{ getTimeAgo(getCurrentStory()?.createdAt || '') }}</span>\n      </div>\n      <button (click)=\"closeStories()\" class=\"close-btn\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Story Media -->\n    <div class=\"story-media\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n\n      <!-- Image Story -->\n      <img *ngIf=\"getCurrentStory()?.mediaType === 'image'\"\n           [src]=\"getCurrentStory()?.mediaUrl\"\n           class=\"story-image\"/>\n\n      <!-- Video Story -->\n      <video *ngIf=\"getCurrentStory()?.mediaType === 'video'\"\n             class=\"story-video\"\n             [src]=\"getCurrentStory()?.mediaUrl\"\n             autoplay muted #storyVideo></video>\n\n      <!-- Middle Click Area for Product/Category Navigation -->\n      <div class=\"story-middle-click-area\"\n           (click)=\"handleMiddleAreaClick($event)\"\n           *ngIf=\"getCurrentStory()?.linkedContent\"\n           title=\"View linked {{ getCurrentStory()?.linkedContent?.type }}\">\n        <div class=\"middle-click-indicator\">\n          <i class=\"fas fa-external-link-alt\"></i>\n          <span>{{ getLinkedContentText() }}</span>\n        </div>\n      </div>\n\n      <!-- Product Tags -->\n      <div class=\"product-tags\" *ngIf=\"showProductTags && getCurrentStory()?.products\">\n        <div class=\"product-tag\"\n             *ngFor=\"let productTag of getCurrentStory()?.products\"\n             [style.left.%]=\"productTag.position?.x || 50\"\n             [style.top.%]=\"productTag.position?.y || 50\"\n             (click)=\"productTag.product && openProductDetails(productTag.product); $event.stopPropagation()\">\n          <div class=\"product-tag-dot\"></div>\n          <div class=\"product-tag-info\">\n            <img [src]=\"productTag.product.images[0].url || '/assets/images/product-placeholder.jpg'\"\n                 [alt]=\"productTag.product.name || 'Product'\"\n                 class=\"product-tag-image\">\n            <div class=\"product-tag-details\">\n              <span class=\"product-tag-name\">{{ productTag.product.name || 'Product' }}</span>\n              <span class=\"product-tag-price\">${{ productTag.product.price || 0 }}</span>\n              <div class=\"product-tag-actions\">\n                <button class=\"product-action-btn cart-btn\"\n                        (click)=\"productTag.product && addToCart(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Add to Cart\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n                <button class=\"product-action-btn wishlist-btn\"\n                        (click)=\"productTag.product && addToWishlist(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Add to Wishlist\">\n                  <i class=\"fas fa-heart\"></i>\n                </button>\n                <button class=\"product-action-btn buy-btn\"\n                        (click)=\"productTag.product && buyNow(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Buy Now\">\n                  <i class=\"fas fa-bolt\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Shopping Bag Button -->\n      <button class=\"shopping-bag-btn\"\n              *ngIf=\"hasProducts()\"\n              (click)=\"toggleProductTags(); $event.stopPropagation()\"\n              [class.active]=\"showProductTags\"\n              type=\"button\"\n              [attr.aria-label]=\"'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')\"\n              [title]=\"'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')\">\n        <i class=\"fas fa-shopping-bag\" aria-hidden=\"true\"></i>\n        <span class=\"product-count\" aria-hidden=\"true\">{{ getProductCount() }}</span>\n      </button>\n    </div>\n\n    <!-- Story Actions -->\n    <div class=\"story-actions\">\n      <button class=\"action-btn like-btn\"\n              (click)=\"toggleLike()\"\n              [class.liked]=\"isLiked\"\n              type=\"button\"\n              [attr.aria-label]=\"isLiked ? 'Unlike story' : 'Like story'\"\n              [title]=\"isLiked ? 'Unlike story' : 'Like story'\">\n        <i class=\"fas fa-heart\" aria-hidden=\"true\"></i>\n      </button>\n      <button class=\"action-btn share-btn\"\n              (click)=\"shareStory()\"\n              type=\"button\"\n              aria-label=\"Share story\"\n              title=\"Share story\">\n        <i class=\"fas fa-share\" aria-hidden=\"true\"></i>\n      </button>\n      <button class=\"action-btn save-btn\"\n              (click)=\"saveStory()\"\n              type=\"button\"\n              aria-label=\"Save story\"\n              title=\"Save story\">\n        <i class=\"fas fa-bookmark\" aria-hidden=\"true\"></i>\n      </button>\n    </div>\n\n    <!-- Navigation Arrows -->\n    <button class=\"story-nav-btn story-nav-prev\"\n            (click)=\"previousStory()\"\n            *ngIf=\"currentStoryIndex > 0\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n    <button class=\"story-nav-btn story-nav-next\"\n            (click)=\"nextStory()\"\n            *ngIf=\"currentStoryIndex < stories.length - 1\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAO1D,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;;;;;ICsBvDC,EAAA,CAAAC,cAAA,cAAyD;IAAlBD,EAAA,CAAAE,UAAA,mBAAAC,qEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IAGlDT,EAFJ,CAAAC,cAAA,cAAoC,cACG,cACH;IAC9BD,EAAA,CAAAU,SAAA,cAIE;IACJV,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAU,SAAA,YAA2B;IAGjCV,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;IACNX,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IACxCZ,EADwC,CAAAW,YAAA,EAAM,EACxC;;;;IAXIX,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,WAAA,kBAAAT,MAAA,CAAAS,WAAA,CAAAC,MAAA,kCAAAhB,EAAA,CAAAiB,aAAA,CAA0D;;;;;IAmB9DjB,EAHN,CAAAC,cAAA,cAAsC,cACA,cACO,cACP;IAC9BD,EAAA,CAAAU,SAAA,cAAmC;IAGzCV,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;IACNX,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IACxCZ,EADwC,CAAAW,YAAA,EAAM,EACxC;;;;;IAVRX,EAAA,CAAAkB,UAAA,IAAAC,iDAAA,0BAAoD;;;;;IA0B5CnB,EAAA,CAAAC,cAAA,cAE+B;IAC7BD,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAW,YAAA,EAAM;;;;;IAQVX,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAoB,kBAAA,MAAAC,YAAA,CAAAC,aAAA,WAAAD,YAAA,CAAAC,aAAA,qBACF;;;;;;IA1BFtB,EAAA,CAAAC,cAAA,cAAgE;IAAxCD,EAAA,CAAAE,UAAA,mBAAAqB,uEAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAC,MAAA,GAAAzB,EAAA,CAAAO,aAAA;MAAA,MAAAc,YAAA,GAAAI,MAAA,CAAAC,SAAA;MAAA,MAAAC,IAAA,GAAAF,MAAA,CAAAG,KAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuB,eAAA,CAAAR,YAAA,EAAAM,IAAA,CAA6B;IAAA,EAAC;IAGzD3B,EAFJ,CAAAC,cAAA,cAAoC,cACqC,cACrC;IAC9BD,EAAA,CAAAU,SAAA,cAIE;IACJV,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAkB,UAAA,IAAAY,uDAAA,kBAE+B;IAInC9B,EADE,CAAAW,YAAA,EAAM,EACF;IACNX,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAY,MAAA,GAA6B;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IAE/DX,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAkB,UAAA,KAAAa,wDAAA,kBACmC;IAGrC/B,EAAA,CAAAW,YAAA,EAAM;;;;IAzBwBX,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAgC,WAAA,iBAAAX,YAAA,CAAAY,WAAA,CAA4C;IAIhEjC,EAAA,CAAAa,SAAA,GAA6B;IAC7Bb,EADA,CAAAc,UAAA,QAAAO,YAAA,CAAAa,IAAA,CAAAlB,MAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAA6B,QAAAI,YAAA,CAAAa,IAAA,CAAAC,QAAA,CACE;IAK7BnC,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAc,UAAA,SAAAO,YAAA,CAAAY,WAAA,CAA2B;IAMTjC,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAoC,iBAAA,CAAAf,YAAA,CAAAa,IAAA,CAAAC,QAAA,CAA6B;IAGvDnC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAoB,kBAAA,MAAAC,YAAA,CAAAgB,OAAA,CAAAC,MAAA,OAAAjB,YAAA,CAAAgB,OAAA,CAAAC,MAAA,kCACF;IAEMtC,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAc,UAAA,SAAAO,YAAA,CAAAY,WAAA,CAA2B;;;;;IAzBrCjC,EAAA,CAAAkB,UAAA,IAAAqB,iDAAA,2BAA4E;;;;;IAkD5EvC,EAAA,CAAAC,cAAA,cAG+C;IAC7CD,EAAA,CAAAU,SAAA,cACiD;IACnDV,EAAA,CAAAW,YAAA,EAAM;;;;;IAHDX,EADA,CAAAgC,WAAA,WAAAQ,IAAA,KAAAlC,MAAA,CAAAmC,iBAAA,CAAwC,cAAAD,IAAA,GAAAlC,MAAA,CAAAmC,iBAAA,CACC;IAEvCzC,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAA0C,WAAA,UAAApC,MAAA,CAAAqC,gBAAA,CAAAH,IAAA,OAAqC;;;;;IAwB5CxC,EAAA,CAAAU,SAAA,cAE0B;;;;;IADrBV,EAAA,CAAAc,UAAA,SAAA8B,OAAA,GAAAtC,MAAA,CAAAuC,eAAA,qBAAAD,OAAA,CAAAE,QAAA,EAAA9C,EAAA,CAAAiB,aAAA,CAAmC;;;;;IAIxCjB,EAAA,CAAAU,SAAA,mBAG0C;;;;;IADnCV,EAAA,CAAAc,UAAA,SAAAiC,OAAA,GAAAzC,MAAA,CAAAuC,eAAA,qBAAAE,OAAA,CAAAD,QAAA,EAAA9C,EAAA,CAAAiB,aAAA,CAAmC;;;;;;IAI1CjB,EAAA,CAAAC,cAAA,cAGsE;IAFjED,EAAA,CAAAE,UAAA,mBAAA8C,oEAAAC,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6C,qBAAA,CAAAF,MAAA,CAA6B;IAAA,EAAC;IAG1CjD,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAU,SAAA,YAAwC;IACxCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAA4B;IAEtCZ,EAFsC,CAAAW,YAAA,EAAO,EACrC,EACF;;;;;IALDX,EAAA,CAAAoD,sBAAA,2BAAAR,OAAA,GAAAtC,MAAA,CAAAuC,eAAA,qBAAAD,OAAA,CAAAS,aAAA,kBAAAT,OAAA,CAAAS,aAAA,CAAAC,IAAA,KAAgE;IAG3DtD,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAoC,iBAAA,CAAA9B,MAAA,CAAAiD,oBAAA,GAA4B;;;;;;IAMpCvD,EAAA,CAAAC,cAAA,cAIsG;IAAjGD,EAAA,CAAAE,UAAA,mBAAAsD,0EAAAP,MAAA;MAAA,MAAAQ,cAAA,GAAAzD,EAAA,CAAAI,aAAA,CAAAsD,IAAA,EAAAhC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAkD,cAAA,CAAAE,OAAA,IAA+BrD,MAAA,CAAAsD,kBAAA,CAAAH,cAAA,CAAAE,OAAA,CAAsC;MAAA,OAAA3D,EAAA,CAAAQ,WAAA,CAAEyC,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IACnG7D,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAU,SAAA,cAE+B;IAE7BV,EADF,CAAAC,cAAA,cAAiC,eACA;IAAAD,EAAA,CAAAY,MAAA,GAA0C;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAChFX,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAY,MAAA,GAAoC;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAEzEX,EADF,CAAAC,cAAA,cAAiC,kBAIH;IAFpBD,EAAA,CAAAE,UAAA,mBAAA4D,8EAAAb,MAAA;MAAA,MAAAQ,cAAA,GAAAzD,EAAA,CAAAI,aAAA,CAAAsD,IAAA,EAAAhC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAkD,cAAA,CAAAE,OAAA,IAA+BrD,MAAA,CAAAyD,SAAA,CAAAN,cAAA,CAAAE,OAAA,CAA6B;MAAA,OAAA3D,EAAA,CAAAQ,WAAA,CAAEyC,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAG7F7D,EAAA,CAAAU,SAAA,aAAoC;IACtCV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAGgC;IAFxBD,EAAA,CAAAE,UAAA,mBAAA8D,8EAAAf,MAAA;MAAA,MAAAQ,cAAA,GAAAzD,EAAA,CAAAI,aAAA,CAAAsD,IAAA,EAAAhC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAkD,cAAA,CAAAE,OAAA,IAA+BrD,MAAA,CAAA2D,aAAA,CAAAR,cAAA,CAAAE,OAAA,CAAiC;MAAA,OAAA3D,EAAA,CAAAQ,WAAA,CAAEyC,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAGjG7D,EAAA,CAAAU,SAAA,aAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAGwB;IAFhBD,EAAA,CAAAE,UAAA,mBAAAgE,8EAAAjB,MAAA;MAAA,MAAAQ,cAAA,GAAAzD,EAAA,CAAAI,aAAA,CAAAsD,IAAA,EAAAhC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAkD,cAAA,CAAAE,OAAA,IAA+BrD,MAAA,CAAA6D,MAAA,CAAAV,cAAA,CAAAE,OAAA,CAA0B;MAAA,OAAA3D,EAAA,CAAAQ,WAAA,CAAEyC,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAG1F7D,EAAA,CAAAU,SAAA,aAA2B;IAKrCV,EAJQ,CAAAW,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAhCDX,EADA,CAAA0C,WAAA,UAAAe,cAAA,CAAAW,QAAA,kBAAAX,cAAA,CAAAW,QAAA,CAAAC,CAAA,aAA6C,SAAAZ,cAAA,CAAAW,QAAA,kBAAAX,cAAA,CAAAW,QAAA,CAAAE,CAAA,aACD;IAIxCtE,EAAA,CAAAa,SAAA,GAAoF;IACpFb,EADA,CAAAc,UAAA,QAAA2C,cAAA,CAAAE,OAAA,CAAAY,MAAA,IAAAC,GAAA,8CAAAxE,EAAA,CAAAiB,aAAA,CAAoF,QAAAwC,cAAA,CAAAE,OAAA,CAAAc,IAAA,cACxC;IAGhBzE,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAoC,iBAAA,CAAAqB,cAAA,CAAAE,OAAA,CAAAc,IAAA,cAA0C;IACzCzE,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAA0E,kBAAA,MAAAjB,cAAA,CAAAE,OAAA,CAAAgB,KAAA,UAAoC;;;;;IAb5E3E,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAkB,UAAA,IAAA0D,oDAAA,mBAIsG;IAgCxG5E,EAAA,CAAAW,YAAA,EAAM;;;;;IAnCwBX,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAAc,UAAA,aAAA8B,OAAA,GAAAtC,MAAA,CAAAuC,eAAA,qBAAAD,OAAA,CAAAiC,QAAA,CAA8B;;;;;;IAsC5D7E,EAAA,CAAAC,cAAA,iBAMgG;IAJxFD,EAAA,CAAAE,UAAA,mBAAA4E,0EAAA7B,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA0E,iBAAA,EAAmB;MAAA,OAAAhF,EAAA,CAAAQ,WAAA,CAAEyC,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAK7D7D,EAAA,CAAAU,SAAA,YAAsD;IACtDV,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IACxEZ,EADwE,CAAAW,YAAA,EAAO,EACtE;;;;IANDX,EAAA,CAAAgC,WAAA,WAAA1B,MAAA,CAAA2E,eAAA,CAAgC;IAGhCjF,EAAA,CAAAc,UAAA,oBAAAR,MAAA,CAAA4E,eAAA,mBAAA5E,MAAA,CAAA4E,eAAA,mBAAuF;;IAE9ClF,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAoC,iBAAA,CAAA9B,MAAA,CAAA4E,eAAA,GAAuB;;;;;;IA+B1ElF,EAAA,CAAAC,cAAA,iBAEsC;IAD9BD,EAAA,CAAAE,UAAA,mBAAAiF,0EAAA;MAAAnF,EAAA,CAAAI,aAAA,CAAAgF,IAAA;MAAA,MAAA9E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+E,aAAA,EAAe;IAAA,EAAC;IAE/BrF,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAW,YAAA,EAAS;;;;;;IACTX,EAAA,CAAAC,cAAA,iBAEuD;IAD/CD,EAAA,CAAAE,UAAA,mBAAAoF,0EAAA;MAAAtF,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAjF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkF,SAAA,EAAW;IAAA,EAAC;IAE3BxF,EAAA,CAAAU,SAAA,YAAoC;IACtCV,EAAA,CAAAW,YAAA,EAAS;;;;;;IA5ITX,EAHJ,CAAAC,cAAA,cAA4C,iBACF,cAEN;IAC9BD,EAAA,CAAAkB,UAAA,IAAAuE,6CAAA,kBAG+C;IAIjDzF,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAU,SAAA,cAAuF;IAErFV,EADF,CAAAC,cAAA,cAA+B,eACN;IAAAD,EAAA,CAAAY,MAAA,GAAuC;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACrEX,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAY,MAAA,IAAoD;IACzEZ,EADyE,CAAAW,YAAA,EAAO,EAC1E;IACNX,EAAA,CAAAC,cAAA,kBAAmD;IAA3CD,EAAA,CAAAE,UAAA,mBAAAwF,iEAAA;MAAA1F,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsF,YAAA,EAAc;IAAA,EAAC;IAC9B5F,EAAA,CAAAU,SAAA,aAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;IAGNX,EAAA,CAAAC,cAAA,eAIqC;IAAhCD,EAHA,CAAAE,UAAA,mBAAA2F,8DAAA5C,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwF,YAAA,CAAA7C,MAAA,CAAoB;IAAA,EAAC,wBAAA8C,mEAAA9C,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAChBF,MAAA,CAAA0F,YAAA,CAAA/C,MAAA,CAAoB;IAAA,EAAC,uBAAAgD,kEAAAhD,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACtBF,MAAA,CAAA4F,WAAA,CAAAjD,MAAA,CAAmB;IAAA,EAAC,sBAAAkD,iEAAAlD,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACrBF,MAAA,CAAA8F,UAAA,CAAAnD,MAAA,CAAkB;IAAA,EAAC;IAiElCjD,EA9DA,CAAAkB,UAAA,KAAAmF,8CAAA,kBAE0B,KAAAC,gDAAA,oBAMQ,KAAAC,8CAAA,kBAMoC,KAAAC,8CAAA,kBAQW,KAAAC,iDAAA,qBA8Ce;IAIlGzG,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAA2B,kBAMiC;IAJlDD,EAAA,CAAAE,UAAA,mBAAAwG,iEAAA;MAAA1G,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqG,UAAA,EAAY;IAAA,EAAC;IAK5B3G,EAAA,CAAAU,SAAA,aAA+C;IACjDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAI4B;IAHpBD,EAAA,CAAAE,UAAA,mBAAA0G,iEAAA;MAAA5G,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuG,UAAA,EAAY;IAAA,EAAC;IAI5B7G,EAAA,CAAAU,SAAA,aAA+C;IACjDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAI2B;IAHnBD,EAAA,CAAAE,UAAA,mBAAA4G,iEAAA;MAAA9G,EAAA,CAAAI,aAAA,CAAAuF,GAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,SAAA,EAAW;IAAA,EAAC;IAI3B/G,EAAA,CAAAU,SAAA,aAAkD;IAEtDV,EADE,CAAAW,YAAA,EAAS,EACL;IAQNX,EALA,CAAAkB,UAAA,KAAA8F,iDAAA,qBAEsC,KAAAC,iDAAA,qBAKiB;IAI3DjH,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;;;;;;IA5IuBX,EAAA,CAAAa,SAAA,GAAY;IAAZb,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAA+B,OAAA,CAAY;IAUFrC,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,UAAA,SAAA8B,OAAA,GAAAtC,MAAA,CAAAuC,eAAA,qBAAAD,OAAA,CAAAV,IAAA,kBAAAU,OAAA,CAAAV,IAAA,CAAAlB,MAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAAuC;IAE/CjB,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAoC,iBAAA,EAAAW,OAAA,GAAAzC,MAAA,CAAAuC,eAAA,qBAAAE,OAAA,CAAAb,IAAA,kBAAAa,OAAA,CAAAb,IAAA,CAAAC,QAAA,CAAuC;IAC3CnC,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAAoC,iBAAA,CAAA9B,MAAA,CAAA4G,UAAA,GAAAC,OAAA,GAAA7G,MAAA,CAAAuC,eAAA,qBAAAsE,OAAA,CAAAC,SAAA,SAAoD;IAenEpH,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAc,UAAA,WAAAuG,OAAA,GAAA/G,MAAA,CAAAuC,eAAA,qBAAAwE,OAAA,CAAAC,SAAA,cAA8C;IAK5CtH,EAAA,CAAAa,SAAA,EAA8C;IAA9Cb,EAAA,CAAAc,UAAA,WAAAyG,OAAA,GAAAjH,MAAA,CAAAuC,eAAA,qBAAA0E,OAAA,CAAAD,SAAA,cAA8C;IAQhDtH,EAAA,CAAAa,SAAA,EAAsC;IAAtCb,EAAA,CAAAc,UAAA,UAAA0G,OAAA,GAAAlH,MAAA,CAAAuC,eAAA,qBAAA2E,OAAA,CAAAnE,aAAA,CAAsC;IASjBrD,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA2E,eAAA,MAAAwC,QAAA,GAAAnH,MAAA,CAAAuC,eAAA,qBAAA4E,QAAA,CAAA5C,QAAA,EAAoD;IAyCtE7E,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA2B,WAAA,GAAmB;IAepBjC,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAgC,WAAA,UAAA1B,MAAA,CAAAoH,OAAA,CAAuB;IAGvB1H,EAAA,CAAAc,UAAA,UAAAR,MAAA,CAAAoH,OAAA,iCAAiD;;IAsBlD1H,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAmC,iBAAA,KAA2B;IAK3BzC,EAAA,CAAAa,SAAA,EAA4C;IAA5Cb,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAmC,iBAAA,GAAAnC,MAAA,CAAA+B,OAAA,CAAAC,MAAA,KAA4C;;;ADjLzD,OAAM,MAAOqF,uBAAuB;EAqElCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,kBAAsC,EACtCC,eAAgC,EAChCC,GAAsB,EACtBC,oBAA0C;IAR1C,KAAAR,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,oBAAoB,GAApBA,oBAAoB;IAzE9B,KAAAtH,WAAW,GAAQ,IAAI;IACvB,KAAAsB,OAAO,GAAqB,EAAE;IAC9B,KAAAiG,gBAAgB,GAAG,IAAI;IAEvB;IACA,KAAAC,MAAM,GAAG,KAAK;IACd,KAAA9F,iBAAiB,GAAG,CAAC;IACrB,KAAA+F,gBAAgB,GAAG,CAAC;IACpB,KAAAC,kBAAkB,GAAY,EAAE;IAChC,KAAAxD,eAAe,GAAG,KAAK;IACvB,KAAAyC,OAAO,GAAG,KAAK;IAEf,KAAAgB,aAAa,GAAG,CAAC;IAEjB;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,SAAS,GAAG,EAAE,CAAC,CAAC;IAChB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IAKb,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,iBAAiB,GAAG,CAAC;IAC7B,KAAAC,eAAe,GAAG,CAAC,CAAC,CAAC;IAErB;IACQ,KAAAC,cAAc,GAAG,CAAC;IAG1B;IACA,KAAAC,aAAa,GAAe;MAC1BC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;;OAER;MACDA,GAAG,EAAE;KACN;IAEO,KAAAC,aAAa,GAAmB,EAAE;IAaxCC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAC/D;EAEAC,QAAQA,CAAA;IACNF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD,IAAI,CAACE,WAAW,EAAE;IAClB,IAAI,CAACtC,WAAW,CAACuC,YAAY,CAACC,SAAS,CAACrI,IAAI,IAAG;MAC7C,IAAI,CAACnB,WAAW,GAAGmB,IAAI;MACvBgI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEjI,IAAI,CAAC;IAC/C,CAAC,CAAC;IAEF;IACA,IAAI,CAACsI,yBAAyB,EAAE;EAClC;EAEAC,eAAeA,CAAA;IACbP,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCO,UAAU,CAAC,MAAK;MACdR,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAACQ,yBAAyB,EAAE;MAChC,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;IAEP;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACrI,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3B4H,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD,IAAI,CAACQ,yBAAyB,EAAE;QAChC,IAAI,CAACC,mBAAmB,EAAE;;IAE9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,aAAa,CAACa,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACV,yBAAyB,EAAE;IAChC,IAAI,CAACG,yBAAyB,EAAE;IAChC,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQJ,yBAAyBA,CAAA;IAC/B,IAAI,CAAC3B,aAAa,GAAGsC,MAAM,CAACC,UAAU,GAAG,GAAG;EAC9C;EAEQT,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAACU,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,aAAa,EAAE;MAC1D,MAAMC,cAAc,GAAG,IAAI,CAACF,aAAa,CAACC,aAAa,CAACE,WAAW;MACnE,MAAMC,WAAW,GAAGN,MAAM,CAACC,UAAU;MAErClB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAAEoB,cAAc;QAAEE;MAAW,CAAE,CAAC;MAE9E;MACA,IAAIA,WAAW,IAAI,GAAG,EAAE;QACtB,IAAI,CAACzC,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAGwC,cAAc,GAAG,CAAC;OACpC,MAAM,IAAIE,WAAW,IAAI,GAAG,EAAE;QAC7B,IAAI,CAACzC,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAGwC,cAAc,GAAG,CAAC;OACpC,MAAM;QACL,IAAI,CAACvC,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAGwC,cAAc,GAAG,CAAC;;MAGrCrB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CnB,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BD,SAAS,EAAE,IAAI,CAACA;OACjB,CAAC;MAEF;MACA,IAAI,CAACE,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACH,UAAU,GAAG,CAAC;KACpB,MAAM;MACLoB,OAAO,CAACwB,IAAI,CAAC,4DAA4D,CAAC;MAC1E;MACA,IAAI,CAAC3C,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;EAEzB;EAEAqB,WAAWA,CAAA;IACT,IAAI,CAAC/B,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC2B,aAAa,CAAC0B,IAAI,CACrB,IAAI,CAAC7D,IAAI,CAAC8D,GAAG,CAAM,GAAG9L,WAAW,CAAC+L,MAAM,UAAU,CAAC,CAACtB,SAAS,CAAC;MAC5DuB,IAAI,EAAGC,QAAQ,IAAI;QACjB7B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4B,QAAQ,CAAC;QAC9C,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAC1J,OAAO,IAAI0J,QAAQ,CAAC1J,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACvE;UACA,MAAM2J,UAAU,GAAGF,QAAQ,CAAC1J,OAAO,CAChC6J,MAAM,CAAEC,KAAU,IAAKA,KAAK,CAACC,QAAQ,CAAC,CACtCC,GAAG,CAAEF,KAAU,KAAM;YACpB,GAAGA,KAAK;YACRrJ,QAAQ,EAAEqJ,KAAK,CAACG,KAAK,EAAE9H,GAAG,IAAI2H,KAAK,CAACrJ,QAAQ;YAC5CwE,SAAS,EAAE6E,KAAK,CAACG,KAAK,EAAEhJ,IAAI,IAAI6I,KAAK,CAAC7E;WACvC,CAAC,CAAC;UAEL;UACA,IAAI,CAACjF,OAAO,GAAG,IAAI,CAACkK,kBAAkB,CAACN,UAAU,CAAC;UAClD/B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC9H,OAAO,CAAC;UACjE6H,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC9H,OAAO,CAACC,MAAM,CAAC;SAC7D,MAAM;UACL4H,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D,IAAI,CAACqC,mBAAmB,EAAE;;QAE5B,IAAI,CAAClE,gBAAgB,GAAG,KAAK;QAC7BoC,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CvC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAACqC,mBAAmB,EAAE;QAC1B,IAAI,CAAClE,gBAAgB,GAAG,KAAK;QAC7BoC,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC,CACH;EACH;EAEA;EACQ2B,kBAAkBA,CAACN,UAAmB;IAC5C,MAAMS,cAAc,GAAG,IAAIC,GAAG,EAAE;IAEhCV,UAAU,CAACnB,OAAO,CAACqB,KAAK,IAAG;MACzB,MAAMS,MAAM,GAAGT,KAAK,CAACjK,IAAI,CAAC2K,GAAG;MAC7B,IAAI,CAACH,cAAc,CAACI,GAAG,CAACF,MAAM,CAAC,EAAE;QAC/BF,cAAc,CAACK,GAAG,CAACH,MAAM,EAAE;UACzB1K,IAAI,EAAEiK,KAAK,CAACjK,IAAI;UAChBG,OAAO,EAAE,EAAE;UACXJ,WAAW,EAAE,KAAK;UAClBX,aAAa,EAAE,CAAC;UAChB0L,eAAe,EAAEb,KAAK,CAAC/E;SACxB,CAAC;;MAGJ,MAAM6F,SAAS,GAAGP,cAAc,CAACd,GAAG,CAACgB,MAAM,CAAC;MAC5CK,SAAS,CAAC5K,OAAO,CAACsJ,IAAI,CAACQ,KAAK,CAAC;MAE7B;MACA,IAAIA,KAAK,CAACtH,QAAQ,IAAIsH,KAAK,CAACtH,QAAQ,CAACvC,MAAM,GAAG,CAAC,EAAE;QAC/C2K,SAAS,CAAChL,WAAW,GAAG,IAAI;QAC5BgL,SAAS,CAAC3L,aAAa,IAAI6K,KAAK,CAACtH,QAAQ,CAACvC,MAAM;;MAGlD;MACA,IAAI,IAAI4K,IAAI,CAACf,KAAK,CAAC/E,SAAS,CAAC,GAAG,IAAI8F,IAAI,CAACD,SAAS,CAACD,eAAe,CAAC,EAAE;QACnEC,SAAS,CAACD,eAAe,GAAGb,KAAK,CAAC/E,SAAS;;IAE/C,CAAC,CAAC;IAEF;IACA,OAAO+F,KAAK,CAACC,IAAI,CAACV,cAAc,CAACW,MAAM,EAAE,CAAC,CACvCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIN,IAAI,CAACM,CAAC,CAACR,eAAe,CAAC,CAACS,OAAO,EAAE,GAAG,IAAIP,IAAI,CAACK,CAAC,CAACP,eAAe,CAAC,CAACS,OAAO,EAAE,CAAC;EAClG;EAEAjB,mBAAmBA,CAAA;IACjBtC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAAC9H,OAAO,GAAG,EAAE;EACnB;EAEA;EACAqL,UAAUA,CAAA;IACRxD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAClB,iBAAiB,CAAC;IAC1E,IAAI,IAAI,CAACA,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAAC0E,oBAAoB,EAAE;MAC3BzD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAClB,iBAAiB,CAAC;KAC/D,MAAM;MACLiB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;EAEvD;EAEAyD,WAAWA,CAAA;IACT,MAAMC,UAAU,GAAG,IAAI,CAACxL,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAMwL,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAAC7E,YAAY,CAAC;IAEjEkB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnC8D,YAAY,EAAE,IAAI,CAAChF,iBAAiB;MACpC4E,UAAU;MACVC,aAAa;MACb9E,YAAY,EAAE,IAAI,CAACA;KACpB,CAAC;IAEF,IAAI,IAAI,CAACC,iBAAiB,GAAG6E,aAAa,EAAE;MAC1C,IAAI,CAAC7E,iBAAiB,EAAE;MACxB,IAAI,CAAC0E,oBAAoB,EAAE;MAC3BzD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAClB,iBAAiB,CAAC;KAChE,MAAM;MACLiB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;EAEtD;EAEQwD,oBAAoBA,CAAA;IAC1B,MAAMO,aAAa,GAAG,CAAC,IAAI,CAACjF,iBAAiB,GAAG,IAAI,CAACF,SAAS;IAC9DmB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvC8D,YAAY,EAAE,IAAI,CAAChF,iBAAiB;MACpCF,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBmF;KACD,CAAC;IAEF,IAAI,CAACpF,UAAU,GAAGoF,aAAa;IAC/B,IAAI,CAACtD,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMiD,UAAU,GAAG,IAAI,CAACxL,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAMwL,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAAC7E,YAAY,CAAC;IAEjE,IAAI,CAACL,aAAa,GAAG,IAAI,CAACM,iBAAiB,GAAG,CAAC;IAC/C,IAAI,CAACL,cAAc,GAAG,IAAI,CAACK,iBAAiB,GAAG6E,aAAa;IAE5D5D,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;MACrCxB,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCiF,UAAU;MACVC,aAAa;MACbG,YAAY,EAAE,IAAI,CAAChF;KACpB,CAAC;EACJ;EAEA;EACApH,eAAeA,CAACoL,SAAyB,EAAEkB,SAAiB;IAC1D,IAAI,CAAC3F,gBAAgB,GAAG2F,SAAS;IACjC,IAAI,CAAC1L,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACgG,kBAAkB,GAAGwE,SAAS,CAAC5K,OAAO;IAC3C,IAAI,CAACkG,MAAM,GAAG,IAAI;IAClB,IAAI,CAACtD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACmJ,eAAe,EAAE;IACtBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvCtE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE8C,SAAS,CAAC/K,IAAI,CAACC,QAAQ,EAAE,gBAAgB,EAAE8K,SAAS,CAAC5K,OAAO,CAACC,MAAM,CAAC;EAC/G;EAEAmM,SAASA,CAACC,MAAa,EAAE9M,KAAa;IACpC;IACA,IAAI,CAACa,iBAAiB,GAAGb,KAAK;IAC9B,IAAI,CAAC2G,MAAM,GAAG,IAAI;IAClB,IAAI,CAACtD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACmJ,eAAe,EAAE;IACtBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEA5I,YAAYA,CAAA;IACV,IAAI,CAAC2C,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC0C,eAAe,EAAE;IACtB,IAAI,CAAC0D,cAAc,EAAE;IACrBN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAhJ,SAASA,CAAA;IACP;IACA,IAAI,IAAI,CAAC/C,iBAAiB,GAAG,IAAI,CAACgG,kBAAkB,CAACnG,MAAM,GAAG,CAAC,EAAE;MAC/D,IAAI,CAACG,iBAAiB,EAAE;MACxB,IAAI,CAACwC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACmJ,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,IAAI,CAAC5F,gBAAgB,GAAG,IAAI,CAACnG,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACkG,gBAAgB,EAAE;QACvB,IAAI,CAAC/F,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAACgG,kBAAkB,GAAG,IAAI,CAACpG,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAC,CAACnG,OAAO;QACrE,IAAI,CAAC4C,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACmJ,eAAe,EAAE;OACvB,MAAM;QACL,IAAI,CAACxI,YAAY,EAAE;;;EAGzB;EAEAP,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAAC5C,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACwC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACmJ,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,IAAI,CAAC5F,gBAAgB,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACA,gBAAgB,EAAE;QACvB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACpG,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAC,CAACnG,OAAO;QACrE,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACgG,kBAAkB,CAACnG,MAAM,GAAG,CAAC;QAC3D,IAAI,CAAC2C,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACmJ,eAAe,EAAE;OACvB,MAAM;QACL,IAAI,CAACxI,YAAY,EAAE;;;EAGzB;EAEA/C,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC4F,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACnG,MAAM,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAACmG,kBAAkB,CAAC,IAAI,CAAChG,iBAAiB,CAAC,IAAI,IAAI,CAACgG,kBAAkB,CAAC,CAAC,CAAC;;IAEtF;IACA,IAAI,IAAI,CAACpG,OAAO,IAAI,IAAI,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACjF,OAAO,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC;;IAEnC,OAAO,IAAI;EACb;EAEAuM,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACvM,OAAO,IAAI,IAAI,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO,IAAI,CAACD,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAC,EAAEtG,IAAI;;IAElD,OAAO,IAAI;EACb;EAEAD,WAAWA,CAAA;IACT,MAAMkK,KAAK,GAAG,IAAI,CAACtJ,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEsJ,KAAK,IAAIA,KAAK,CAACtH,QAAQ,IAAIsH,KAAK,CAACtH,QAAQ,CAACvC,MAAM,GAAG,CAAC,CAAC;EACjE;EAEA4C,eAAeA,CAAA;IACb,MAAMiH,KAAK,GAAG,IAAI,CAACtJ,eAAe,EAAE;IACpC,OAAOsJ,KAAK,EAAEtH,QAAQ,EAAEvC,MAAM,IAAI,CAAC;EACrC;EAEA;EACAK,gBAAgBA,CAACf,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACa,iBAAiB,EAAE,OAAO,GAAG;IAC9C,IAAIb,KAAK,GAAG,IAAI,CAACa,iBAAiB,EAAE,OAAO,CAAC;IAE5C;IACA,OAAO,IAAI,CAAC2G,eAAe;EAC7B;EAEQgF,eAAeA,CAAA;IACrB,IAAI,CAACnD,eAAe,EAAE;IACtB,IAAI,CAAC9B,iBAAiB,GAAG+D,IAAI,CAAC2B,GAAG,EAAE;IACnC,IAAI,CAACzF,eAAe,GAAG,CAAC;IAExB;IACA,IAAI,CAAC0F,mBAAmB,GAAGC,WAAW,CAAC,MAAK;MAC1C,IAAI,IAAI,CAAC5F,iBAAiB,EAAE;QAC1B,MAAM6F,OAAO,GAAG9B,IAAI,CAAC2B,GAAG,EAAE,GAAG,IAAI,CAAC1F,iBAAiB;QACnD,IAAI,CAACC,eAAe,GAAG2E,IAAI,CAACkB,GAAG,CAAED,OAAO,GAAG,IAAI,CAAC9F,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;QAC1E,IAAI,CAACd,GAAG,CAAC8G,aAAa,EAAE,CAAC,CAAC;;IAE9B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACC,aAAa,GAAGzE,UAAU,CAAC,MAAK;MACnC,IAAI,CAAClF,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAAC0D,aAAa,CAAC;EACxB;EAEQ+B,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACkE,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;;IAE3B,IAAI,IAAI,CAACL,mBAAmB,EAAE;MAC5BO,aAAa,CAAC,IAAI,CAACP,mBAAmB,CAAC;MACvC,IAAI,CAACA,mBAAmB,GAAG,IAAI;;IAEjC,IAAI,CAAC3F,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,CAAC;EAC1B;EAEA;EACAtD,YAAYA,CAACwJ,KAAiB;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACE,OAAO;IAC5B,MAAMC,WAAW,GAAGtE,MAAM,CAACC,UAAU;IAErC,IAAImE,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACpK,aAAa,EAAE;KACrB,MAAM,IAAIkK,MAAM,GAAIE,WAAW,GAAG,CAAC,GAAI,CAAC,EAAE;MACzC,IAAI,CAACjK,SAAS,EAAE;;EAEpB;EAEA;EACAQ,YAAYA,CAAC0J,MAAkB;IAC7B,IAAI,CAACrG,cAAc,GAAG6D,IAAI,CAAC2B,GAAG,EAAE;IAChC,IAAI,CAACc,cAAc,GAAGjF,UAAU,CAAC,MAAK;MACpC,IAAI,CAACO,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA/E,WAAWA,CAACwJ,MAAkB;IAC5B,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBP,YAAY,CAAC,IAAI,CAACO,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAvJ,UAAUA,CAACkJ,KAAiB;IAC1B,IAAI,IAAI,CAACK,cAAc,EAAE;MACvBP,YAAY,CAAC,IAAI,CAACO,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,MAAMC,aAAa,GAAG1C,IAAI,CAAC2B,GAAG,EAAE,GAAG,IAAI,CAACxF,cAAc;IACtD,IAAIuG,aAAa,GAAG,GAAG,EAAE;MACvB;MACA,MAAMC,KAAK,GAAGP,KAAK,CAACQ,cAAc,CAAC,CAAC,CAAC;MACrC,IAAI,CAAChK,YAAY,CAAC;QAAE0J,OAAO,EAAEK,KAAK,CAACL;MAAO,CAAgB,CAAC;KAC5D,MAAM;MACL;MACA,IAAI,CAACpB,eAAe,EAAE;;EAE1B;EAEA;EACApJ,iBAAiBA,CAAA;IACf,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEArB,kBAAkBA,CAACD,OAAY;IAC7B,IAAI,CAACkE,MAAM,CAACkI,QAAQ,CAAC,CAAC,UAAU,EAAEpM,OAAO,CAACkJ,GAAG,CAAC,CAAC;EACjD;EAEA;EACA9I,SAASA,CAACJ,OAAY;IACpBuG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAExG,OAAO,CAAC;IAE/C,IAAI,CAAC0E,oBAAoB,CAACtE,SAAS,CAAC;MAClCiM,SAAS,EAAErM,OAAO,CAACkJ,GAAG;MACtBoD,IAAI,EAAEtM,OAAO,CAACsM,IAAI;MAClBC,KAAK,EAAEvM,OAAO,CAACuM,KAAK;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAAC7F,SAAS,CAAC;MACXuB,IAAI,EAAGuE,MAAM,IAAI;QACf,IAAIA,MAAM,CAACrE,OAAO,EAAE;UAClB9B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkG,MAAM,CAAC;UAC1D;SACD,MAAM;UACLnG,OAAO,CAACuC,KAAK,CAAC,gCAAgC,EAAE4D,MAAM,CAACC,OAAO,CAAC;;MAEnE,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEA;EACAxI,aAAaA,CAACN,OAAY;IACxBuG,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAExG,OAAO,CAAC;IAEnD,IAAI,CAAC0E,oBAAoB,CAACpE,aAAa,CAAC;MACtC+L,SAAS,EAAErM,OAAO,CAACkJ,GAAG;MACtBoD,IAAI,EAAEtM,OAAO,CAACsM,IAAI;MAClBC,KAAK,EAAEvM,OAAO,CAACuM,KAAK;MACpBE,SAAS,EAAE;KACZ,CAAC,CAAC7F,SAAS,CAAC;MACXuB,IAAI,EAAGuE,MAAM,IAAI;QACf,IAAIA,MAAM,CAACrE,OAAO,EAAE;UAClB9B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEkG,MAAM,CAAC;UAC9D;SACD,MAAM;UACLnG,OAAO,CAACuC,KAAK,CAAC,oCAAoC,EAAE4D,MAAM,CAACC,OAAO,CAAC;;MAEvE,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;EACAtI,MAAMA,CAACR,OAAY;IACjBuG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAExG,OAAO,CAAC;IAEpD,IAAI,CAAC0E,oBAAoB,CAAClE,MAAM,CAAC;MAC/B6L,SAAS,EAAErM,OAAO,CAACkJ,GAAG;MACtBoD,IAAI,EAAEtM,OAAO,CAACsM,IAAI;MAClBC,KAAK,EAAEvM,OAAO,CAACuM,KAAK;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAAC7F,SAAS,CAAC;MACXuB,IAAI,EAAGuE,MAAM,IAAI;QACf,IAAIA,MAAM,CAACrE,OAAO,EAAE;UAClB9B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkG,MAAM,CAAC;UAC1C;SACD,MAAM;UACLnG,OAAO,CAACuC,KAAK,CAAC,oBAAoB,EAAE4D,MAAM,CAACC,OAAO,CAAC;;MAEvD,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACA9F,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACoB,WAAW,CAACwI,eAAe,EAAE;MACrC,IAAI,CAAC1I,MAAM,CAACkI,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMS,YAAY,GAAG,IAAI,CAAC3N,eAAe,EAAE;IAC3C,IAAI,CAAC2N,YAAY,EAAE;IAEnB,IAAI,CAAC9I,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,MAAM+I,QAAQ,GAAG,IAAI,CAAC/I,OAAO,GAAG,MAAM,GAAG,QAAQ;IACjD,IAAI,CAACI,IAAI,CAAC4I,IAAI,CAAC,GAAG5Q,WAAW,CAAC+L,MAAM,YAAY2E,YAAY,CAAC3D,GAAG,IAAI4D,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAClG,SAAS,CAAC;MAC5FuB,IAAI,EAAGC,QAAQ,IAAI;QACjB7B,OAAO,CAACC,GAAG,CAAC,SAASsG,QAAQ,iBAAiB,EAAE1E,QAAQ,CAAC;QACzD;QACA,IAAI,IAAI,CAAC5D,eAAe,CAACwI,WAAW,EAAE,EAAE;UACtC;UACAzG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChCyG,OAAO,EAAEJ,YAAY,CAAC3D,GAAG;YACzBD,MAAM,EAAE,IAAI,CAAC7E,WAAW,CAAC8I,gBAAgB,EAAEhE,GAAG;YAC9CiE,KAAK,EAAE,IAAI,CAACpJ;WACb,CAAC;;MAEN,CAAC;MACD+E,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,SAASgE,QAAQ,YAAY,EAAEhE,KAAK,CAAC;QACnD;QACA,IAAI,CAAC/E,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC9B;KACD,CAAC;EACJ;EAEAb,UAAUA,CAAA;IACR,MAAM2J,YAAY,GAAG,IAAI,CAAC3N,eAAe,EAAE;IAC3C,IAAI,CAAC2N,YAAY,EAAE;IAEnB,IAAIO,SAAS,CAACC,KAAK,EAAE;MACnB;MACAD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,YAAYT,YAAY,CAACtO,IAAI,CAACC,QAAQ,EAAE;QAC/C+O,IAAI,EAAEV,YAAY,CAACW,OAAO;QAC1B3M,GAAG,EAAE,GAAG1E,WAAW,CAACsR,WAAW,UAAUZ,YAAY,CAAC3D,GAAG;OAC1D,CAAC,CAACwE,IAAI,CAAC,MAAK;QACXnH,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC;QACA,IAAI,CAACmH,eAAe,CAACd,YAAY,CAAC3D,GAAG,CAAC;MACxC,CAAC,CAAC,CAAC0E,KAAK,CAAE9E,KAAK,IAAI;QACjBvC,OAAO,CAACuC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;KACH,MAAM;MACL;MACA,MAAM+E,QAAQ,GAAG,GAAG1R,WAAW,CAACsR,WAAW,UAAUZ,YAAY,CAAC3D,GAAG,EAAE;MACvEkE,SAAS,CAACU,SAAS,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACH,IAAI,CAAC,MAAK;QAChDnH,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACmH,eAAe,CAACd,YAAY,CAAC3D,GAAG,CAAC;QACtC;QACA,IAAI,CAAC8E,SAAS,CAAC,iCAAiC,CAAC;MACnD,CAAC,CAAC,CAACJ,KAAK,CAAE9E,KAAK,IAAI;QACjBvC,OAAO,CAACuC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEA1F,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACgB,WAAW,CAACwI,eAAe,EAAE;MACrC,IAAI,CAAC1I,MAAM,CAACkI,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMS,YAAY,GAAG,IAAI,CAAC3N,eAAe,EAAE;IAC3C,IAAI,CAAC2N,YAAY,EAAE;IAEnB;IACA,IAAI,CAAC1I,IAAI,CAAC4I,IAAI,CAAC,GAAG5Q,WAAW,CAAC+L,MAAM,YAAY2E,YAAY,CAAC3D,GAAG,OAAO,EAAE,EAAE,CAAC,CAACtC,SAAS,CAAC;MACrFuB,IAAI,EAAGC,QAAQ,IAAI;QACjB7B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4B,QAAQ,CAAC;QAClD,IAAI,CAAC4F,SAAS,CAAC,iCAAiC,CAAC;QACjD;QACA,IAAI,IAAI,CAACxJ,eAAe,CAACwI,WAAW,EAAE,EAAE;UACtC;UACAzG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChCyG,OAAO,EAAEJ,YAAY,CAAC3D,GAAG;YACzBD,MAAM,EAAE,IAAI,CAAC7E,WAAW,CAAC8I,gBAAgB,EAAEhE;WAC5C,CAAC;;MAEN,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACkF,SAAS,CAAC,uCAAuC,CAAC;MACzD;KACD,CAAC;EACJ;EAEQL,eAAeA,CAACV,OAAe;IACrC,IAAI,IAAI,CAAC7I,WAAW,CAACwI,eAAe,EAAE;MACpC,IAAI,CAACzI,IAAI,CAAC4I,IAAI,CAAC,GAAG5Q,WAAW,CAAC+L,MAAM,YAAY+E,OAAO,QAAQ,EAAE,EAAE,CAAC,CAACrG,SAAS,CAAC;QAC7EuB,IAAI,EAAGC,QAAQ,IAAI;UACjB7B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4B,QAAQ,CAAC;QAC/C,CAAC;QACDU,KAAK,EAAGA,KAAK,IAAI;UACfvC,OAAO,CAACuC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEA;EACAtJ,qBAAqBA,CAACmM,KAAY;IAChCA,KAAK,CAACzL,eAAe,EAAE;IACvB,MAAM2M,YAAY,GAAG,IAAI,CAAC3N,eAAe,EAAE;IAE3C,IAAI,CAAC2N,YAAY,EAAEnN,aAAa,EAAE;MAChC;;IAGF6G,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEqG,YAAY,CAACnN,aAAa,CAAC;IAE9E,QAAQmN,YAAY,CAACnN,aAAa,CAACC,IAAI;MACrC,KAAK,SAAS;QACZ,IAAIkN,YAAY,CAACnN,aAAa,CAAC2M,SAAS,EAAE;UACxC,IAAI,CAACnI,MAAM,CAACkI,QAAQ,CAAC,CAAC,UAAU,EAAES,YAAY,CAACnN,aAAa,CAAC2M,SAAS,CAAC,CAAC;SACzE,MAAM,IAAIQ,YAAY,CAAC3L,QAAQ,IAAI2L,YAAY,CAAC3L,QAAQ,CAACvC,MAAM,GAAG,CAAC,EAAE;UACpE;UACA,IAAI,CAACuF,MAAM,CAACkI,QAAQ,CAAC,CAAC,UAAU,EAAES,YAAY,CAAC3L,QAAQ,CAAC,CAAC,CAAC,CAAClB,OAAO,CAACkJ,GAAG,CAAC,CAAC;;QAE1E;MAEF,KAAK,UAAU;QACb,IAAI2D,YAAY,CAACnN,aAAa,CAACuO,UAAU,EAAE;UACzC,IAAI,CAAC/J,MAAM,CAACkI,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9B8B,WAAW,EAAE;cAAEC,QAAQ,EAAEtB,YAAY,CAACnN,aAAa,CAACuO;YAAU;WAC/D,CAAC;;QAEJ;MAEF,KAAK,OAAO;QACV,IAAIpB,YAAY,CAACnN,aAAa,CAAC0O,OAAO,EAAE;UACtC,IAAI,CAAClK,MAAM,CAACkI,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9B8B,WAAW,EAAE;cAAEG,KAAK,EAAExB,YAAY,CAACnN,aAAa,CAAC0O;YAAO;WACzD,CAAC;;QAEJ;MAEF,KAAK,YAAY;QACf,IAAIvB,YAAY,CAACnN,aAAa,CAAC4O,YAAY,EAAE;UAC3C,IAAI,CAACpK,MAAM,CAACkI,QAAQ,CAAC,CAAC,aAAa,EAAES,YAAY,CAACnN,aAAa,CAAC4O,YAAY,CAAC,CAAC;;QAEhF;MAEF;QACE/H,OAAO,CAACwB,IAAI,CAAC,8BAA8B,EAAE8E,YAAY,CAACnN,aAAa,CAACC,IAAI,CAAC;;IAGjF;IACA,IAAI,CAAC4O,uBAAuB,CAAC1B,YAAY,CAAC3D,GAAG,EAAE2D,YAAY,CAACnN,aAAa,CAAC;EAC5E;EAEA;EACAE,oBAAoBA,CAAA;IAClB,MAAMiN,YAAY,GAAG,IAAI,CAAC3N,eAAe,EAAE;IAE3C,IAAI,CAAC2N,YAAY,EAAEnN,aAAa,EAAE;MAChC,OAAO,EAAE;;IAGX,QAAQmN,YAAY,CAACnN,aAAa,CAACC,IAAI;MACrC,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,YAAY;QACf,OAAO,iBAAiB;MAC1B;QACE,OAAO,cAAc;;EAE3B;EAEA;EACQ4O,uBAAuBA,CAACtB,OAAe,EAAEvN,aAAkB;IACjE,IAAI,IAAI,CAAC0E,WAAW,CAACwI,eAAe,EAAE;MACpC,IAAI,CAACzI,IAAI,CAAC4I,IAAI,CAAC,GAAG5Q,WAAW,CAAC+L,MAAM,YAAY+E,OAAO,QAAQ,EAAE;QAC/DuB,WAAW,EAAE9O,aAAa,CAACC,IAAI;QAC/B8O,SAAS,EAAE/O,aAAa,CAAC2M,SAAS,IAAI3M,aAAa,CAACuO,UAAU,IAAIvO,aAAa,CAAC0O,OAAO,IAAI1O,aAAa,CAAC4O;OAC1G,CAAC,CAAC1H,SAAS,CAAC;QACXuB,IAAI,EAAGC,QAAQ,IAAI;UACjB7B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE4B,QAAQ,CAAC;QACxD,CAAC;QACDU,KAAK,EAAGA,KAAK,IAAI;UACfvC,OAAO,CAACuC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;OACD,CAAC;;EAEN;EAEQkF,SAASA,CAACrB,OAAe;IAC/B;IACA,MAAM+B,KAAK,GAAGhE,QAAQ,CAACiE,aAAa,CAAC,KAAK,CAAC;IAC3CD,KAAK,CAACE,WAAW,GAAGjC,OAAO;IAC3B+B,KAAK,CAAC9D,KAAK,CAACiE,OAAO,GAAG;;;;;;;;;;;KAWrB;IACDnE,QAAQ,CAACC,IAAI,CAACmE,WAAW,CAACJ,KAAK,CAAC;IAEhC3H,UAAU,CAAC,MAAK;MACd2D,QAAQ,CAACC,IAAI,CAACoE,WAAW,CAACL,KAAK,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACA5R,KAAKA,CAAA;IACH,IAAI,CAACoH,MAAM,CAACkI,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA;EACA7I,UAAUA,CAACyL,UAAqC;IAC9C,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IAEjC,MAAM9D,GAAG,GAAG,IAAI3B,IAAI,EAAE;IACtB,IAAI0F,IAAU;IAEd,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;MAClCC,IAAI,GAAG,IAAI1F,IAAI,CAACyF,UAAU,CAAC;KAC5B,MAAM;MACLC,IAAI,GAAGD,UAAU;;IAGnB;IACA,IAAIE,KAAK,CAACD,IAAI,CAACnF,OAAO,EAAE,CAAC,EAAE;MACzB,OAAO,SAAS;;IAGlB,MAAMqF,aAAa,GAAG/E,IAAI,CAACgF,KAAK,CAAC,CAAClE,GAAG,CAACpB,OAAO,EAAE,GAAGmF,IAAI,CAACnF,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIqF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAME,WAAW,GAAGjF,IAAI,CAACgF,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIE,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGlF,IAAI,CAACgF,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEQtE,cAAcA,CAAA;IACpB,MAAMuE,MAAM,GAAG7E,QAAQ,CAAC8E,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACpI,OAAO,CAACsI,KAAK,IAAG;MACrB,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAGAC,aAAaA,CAAChE,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAC/G,MAAM,EAAE;IAElB,QAAQ+G,KAAK,CAACiE,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAAClO,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACG,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACI,YAAY,EAAE;QACnB;;EAEN;;;uBAt1BW+B,uBAAuB,EAAA3H,EAAA,CAAAwT,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA1T,EAAA,CAAAwT,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA5T,EAAA,CAAAwT,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA9T,EAAA,CAAAwT,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAhU,EAAA,CAAAwT,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAlU,EAAA,CAAAwT,iBAAA,CAAAW,EAAA,CAAAC,kBAAA,GAAApU,EAAA,CAAAwT,iBAAA,CAAAa,EAAA,CAAAC,eAAA,GAAAtU,EAAA,CAAAwT,iBAAA,CAAAxT,EAAA,CAAAuU,iBAAA,GAAAvU,EAAA,CAAAwT,iBAAA,CAAAgB,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAvB9M,uBAAuB;MAAA+M,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvB7U,EAAA,CAAAE,UAAA,oBAAA6U,kDAAA9R,MAAA;YAAA,OAAA6R,GAAA,CAAA5J,QAAA,CAAAjI,MAAA,CAAgB;UAAA,UAAAjD,EAAA,CAAAgV,eAAA,CAAO,qBAAAC,mDAAAhS,MAAA;YAAA,OAAvB6R,GAAA,CAAAxB,aAAA,CAAArQ,MAAA,CAAqB;UAAA,UAAAjD,EAAA,CAAAkV,iBAAA,CAAE;;;;;;;;;;;UC1EhClV,EANJ,CAAAC,cAAA,gBAAiD,aAKnB,YACA;UAAAD,EAAA,CAAAY,MAAA,cAAO;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACtCX,EAAA,CAAAC,cAAA,gBAImC;UAH3BD,EAAA,CAAAE,UAAA,mBAAAiV,yDAAA;YAAAnV,EAAA,CAAAI,aAAA,CAAAgV,GAAA;YAAA,OAAApV,EAAA,CAAAQ,WAAA,CAASsU,GAAA,CAAArU,KAAA,EAAO;UAAA,EAAC;UAIvBT,EAAA,CAAAU,SAAA,WAA8C;UAC9CV,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAY,MAAA,aAAM;UAEhBZ,EAFgB,CAAAW,YAAA,EAAO,EACZ,EACL;UAKJX,EAFF,CAAAC,cAAA,aAAoC,iBAQD;UALzBD,EAAA,CAAAE,UAAA,mBAAAmV,0DAAA;YAAArV,EAAA,CAAAI,aAAA,CAAAgV,GAAA;YAAA,OAAApV,EAAA,CAAAQ,WAAA,CAASsU,GAAA,CAAApH,UAAA,EAAY;UAAA,EAAC;UAM5B1N,EAAA,CAAAU,SAAA,aAAsD;UACxDV,EAAA,CAAAW,YAAA,EAAS;UAITX,EAAA,CAAAC,cAAA,0BAAmE;UAsCjED,EApCA,CAAAkB,UAAA,KAAAoU,+CAAA,0BAA2B,KAAAC,mCAAA,iBAsByB,KAAAC,mCAAA,iBAcwB;UA8B9ExV,EAAA,CAAAW,YAAA,EAAiB;UAGjBX,EAAA,CAAAC,cAAA,kBAM6B;UALrBD,EAAA,CAAAE,UAAA,mBAAAuV,0DAAA;YAAAzV,EAAA,CAAAI,aAAA,CAAAgV,GAAA;YAAA,OAAApV,EAAA,CAAAQ,WAAA,CAASsU,GAAA,CAAAlH,WAAA,EAAa;UAAA,EAAC;UAM7B5N,EAAA,CAAAU,SAAA,aAAuD;UAG7DV,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;UAGNX,EAAA,CAAAkB,UAAA,KAAAwU,uCAAA,oBAA4C;;;UA7FhC1V,EAAA,CAAAa,SAAA,IAA+B;UAA/Bb,EAAA,CAAAgC,WAAA,YAAA8S,GAAA,CAAAjM,aAAA,CAA+B;UAD/B7I,EAAA,CAAAc,UAAA,cAAAgU,GAAA,CAAAnM,aAAA,CAA2B;UAUnB3I,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAAc,UAAA,YAAAgU,GAAA,CAAAxL,aAAA,CAAyB;UAwBXtJ,EAAA,CAAAa,SAAA,GAAsB;UAAtBb,EAAA,CAAAc,UAAA,SAAAgU,GAAA,CAAAxM,gBAAA,CAAsB;UAcDtI,EAAA,CAAAa,SAAA,EAAY;UAAZb,EAAA,CAAAc,UAAA,YAAAgU,GAAA,CAAAzS,OAAA,CAAY;UAoCvDrC,EAAA,CAAAa,SAAA,EAA+B;UAA/Bb,EAAA,CAAAgC,WAAA,YAAA8S,GAAA,CAAAjM,aAAA,CAA+B;UAD/B7I,EAAA,CAAAc,UAAA,cAAAgU,GAAA,CAAAlM,cAAA,CAA4B;UAWV5I,EAAA,CAAAa,SAAA,GAAY;UAAZb,EAAA,CAAAc,UAAA,SAAAgU,GAAA,CAAAvM,MAAA,CAAY;;;qBDxC9B3I,YAAY,EAAA+V,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEhW,WAAW,EAAEE,cAAc,EAAA+V,GAAA,CAAAC,iBAAA,EAAAD,GAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}