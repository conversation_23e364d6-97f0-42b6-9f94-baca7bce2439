{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as hapticSelection } from './haptic-554688a5.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { f as checkmarkOutline, r as removeOutline, g as ellipseOutline } from './index-f7dc70ba.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst toggleIosCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;outline:none;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item:not(.legacy-toggle)){width:100%;height:100%}:host([slot=start]:not(.legacy-toggle)),:host([slot=end]:not(.legacy-toggle)){width:auto}:host(.legacy-toggle){contain:content;-ms-touch-action:none;touch-action:none}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}:host(.legacy-toggle) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0;pointer-events:none}@supports (inset-inline-start: 0){:host(.legacy-toggle) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-toggle) label{left:0}:host-context([dir=rtl]):host(.legacy-toggle) label,:host-context([dir=rtl]).legacy-toggle label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-toggle:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-toggle) label::-moz-focus-inner{border:0}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-toggle)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.088);--track-background-checked:var(--ion-color-primary, #3880ff);--border-radius:16px;--handle-background:#ffffff;--handle-background-checked:#ffffff;--handle-border-radius:25.5px;--handle-box-shadow:0 3px 12px rgba(0, 0, 0, 0.16), 0 3px 1px rgba(0, 0, 0, 0.1);--handle-height:calc(32px - (2px * 2));--handle-max-height:calc(100% - var(--handle-spacing) * 2);--handle-width:calc(32px - (2px * 2));--handle-spacing:2px;--handle-transition:transform 300ms, width 120ms ease-in-out 80ms, left 110ms ease-in-out 80ms, right 110ms ease-in-out 80ms}:host(.legacy-toggle){width:51px;height:32px;contain:strict;overflow:hidden}.native-wrapper .toggle-icon{width:51px;height:32px;overflow:hidden}:host(.ion-color.toggle-checked) .toggle-icon{background:var(--ion-color-base)}:host(.toggle-activated) .toggle-switch-icon{opacity:0}.toggle-icon{-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);-webkit-transition:background-color 300ms;transition:background-color 300ms}.toggle-inner{will-change:transform}.toggle-switch-icon{position:absolute;top:50%;width:11px;height:11px;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:opacity 300ms, color 300ms;transition:opacity 300ms, color 300ms}.toggle-switch-icon{position:absolute;color:var(--ion-color-dark)}:host(.toggle-ltr) .toggle-switch-icon{right:6px}:host(.toggle-rtl) .toggle-switch-icon{right:initial;left:6px;}:host(.toggle-checked) .toggle-switch-icon.toggle-switch-icon-checked{color:var(--ion-color-contrast, #fff)}:host(.toggle-checked) .toggle-switch-icon:not(.toggle-switch-icon-checked){opacity:0}.toggle-switch-icon-checked{position:absolute;width:15px;height:15px;-webkit-transform:translateY(-50%) rotate(90deg);transform:translateY(-50%) rotate(90deg)}:host(.toggle-ltr) .toggle-switch-icon-checked{right:initial;left:4px;}:host(.toggle-rtl) .toggle-switch-icon-checked{right:4px}:host(.toggle-activated) .toggle-icon::before,:host(.toggle-checked) .toggle-icon::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated.toggle-checked) .toggle-inner::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated) .toggle-inner{width:calc(var(--handle-width) + 6px)}:host(.toggle-ltr.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0);transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0)}:host(.toggle-rtl.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0);transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0)}:host(.toggle-disabled){opacity:0.3}:host(.in-item.legacy-toggle){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:0;padding-inline-end:0;padding-top:6px;padding-bottom:5px}:host(.in-item.legacy-toggle[slot=start]){-webkit-padding-start:0;padding-inline-start:0;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:6px;padding-bottom:5px}\";\nconst IonToggleIosStyle0 = toggleIosCss;\nconst toggleMdCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;outline:none;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item:not(.legacy-toggle)){width:100%;height:100%}:host([slot=start]:not(.legacy-toggle)),:host([slot=end]:not(.legacy-toggle)){width:auto}:host(.legacy-toggle){contain:content;-ms-touch-action:none;touch-action:none}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}:host(.legacy-toggle) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0;pointer-events:none}@supports (inset-inline-start: 0){:host(.legacy-toggle) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-toggle) label{left:0}:host-context([dir=rtl]):host(.legacy-toggle) label,:host-context([dir=rtl]).legacy-toggle label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-toggle:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-toggle) label::-moz-focus-inner{border:0}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-toggle)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.39);--track-background-checked:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.5);--border-radius:14px;--handle-background:#ffffff;--handle-background-checked:var(--ion-color-primary, #3880ff);--handle-border-radius:50%;--handle-box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--handle-width:20px;--handle-height:20px;--handle-max-height:calc(100% + 6px);--handle-spacing:0;--handle-transition:transform 160ms cubic-bezier(0.4, 0, 0.2, 1), background-color 160ms cubic-bezier(0.4, 0, 0.2, 1)}:host(.legacy-toggle){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;width:36px;height:14px;contain:strict}.native-wrapper .toggle-icon{width:36px;height:14px}:host(.ion-color.toggle-checked) .toggle-icon{background:rgba(var(--ion-color-base-rgb), 0.5)}:host(.ion-color.toggle-checked) .toggle-inner{background:var(--ion-color-base)}:host(.toggle-checked) .toggle-inner{color:var(--ion-color-contrast, #fff)}.toggle-icon{-webkit-transition:background-color 160ms;transition:background-color 160ms}.toggle-inner{will-change:background-color, transform;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;color:#000}.toggle-inner .toggle-switch-icon{-webkit-padding-start:1px;padding-inline-start:1px;-webkit-padding-end:1px;padding-inline-end:1px;padding-top:1px;padding-bottom:1px;width:100%;height:100%}:host(.toggle-disabled){opacity:0.38}:host(.in-item.legacy-toggle){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:0;padding-inline-end:0;padding-top:12px;padding-bottom:12px;cursor:pointer}:host(.in-item.legacy-toggle[slot=start]){-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:18px;padding-inline-end:18px;padding-top:12px;padding-bottom:12px}\";\nconst IonToggleMdStyle0 = toggleMdCss;\nconst Toggle = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-tg-${toggleIds++}`;\n    this.lastDrag = 0;\n    this.inheritedAttributes = {};\n    this.didLoad = false;\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    this.setupGesture = /*#__PURE__*/_asyncToGenerator(function* () {\n      const {\n        toggleTrack\n      } = _this;\n      if (toggleTrack) {\n        _this.gesture = (yield import('./index-2cf77112.js')).createGesture({\n          el: toggleTrack,\n          gestureName: 'toggle',\n          gesturePriority: 100,\n          threshold: 5,\n          passive: false,\n          onStart: () => _this.onStart(),\n          onMove: ev => _this.onMove(ev),\n          onEnd: ev => _this.onEnd(ev)\n        });\n        _this.disabledChanged();\n      }\n    });\n    this.onClick = ev => {\n      if (this.disabled) {\n        return;\n      }\n      ev.preventDefault();\n      if (this.lastDrag + 300 < Date.now()) {\n        this.toggleChecked();\n      }\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.getSwitchLabelIcon = (mode, checked) => {\n      if (mode === 'md') {\n        return checked ? checkmarkOutline : removeOutline;\n      }\n      return checked ? removeOutline : ellipseOutline;\n    };\n    this.activated = false;\n    this.color = undefined;\n    this.name = this.inputId;\n    this.checked = false;\n    this.disabled = false;\n    this.value = 'on';\n    this.enableOnOffLabels = config.get('toggleOnOffLabels');\n    this.labelPlacement = 'start';\n    this.legacy = undefined;\n    this.justify = 'space-between';\n    this.alignment = 'center';\n  }\n  disabledChanged() {\n    this.emitStyle();\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  toggleChecked() {\n    const {\n      checked,\n      value\n    } = this;\n    const isNowChecked = !checked;\n    this.checked = isNowChecked;\n    this.ionChange.emit({\n      checked: isNowChecked,\n      value\n    });\n  }\n  connectedCallback() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.legacyFormController = createLegacyFormController(_this2.el);\n      /**\n       * If we have not yet rendered\n       * ion-toggle, then toggleTrack is not defined.\n       * But if we are moving ion-toggle via appendChild,\n       * then toggleTrack will be defined.\n       */\n      if (_this2.didLoad) {\n        _this2.setupGesture();\n      }\n    })();\n  }\n  componentDidLoad() {\n    this.setupGesture();\n    this.didLoad = true;\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  componentWillLoad() {\n    this.emitStyle();\n    if (!this.legacyFormController.hasLegacyControl()) {\n      this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n  }\n  emitStyle() {\n    if (this.legacyFormController.hasLegacyControl()) {\n      this.ionStyle.emit({\n        'interactive-disabled': this.disabled,\n        // TODO(FW-2990): remove this\n        legacy: !!this.legacy\n      });\n    }\n  }\n  onStart() {\n    this.activated = true;\n    // touch-action does not work in iOS\n    this.setFocus();\n  }\n  onMove(detail) {\n    if (shouldToggle(isRTL(this.el), this.checked, detail.deltaX, -10)) {\n      this.toggleChecked();\n      hapticSelection();\n    }\n  }\n  onEnd(ev) {\n    this.activated = false;\n    this.lastDrag = Date.now();\n    ev.event.preventDefault();\n    ev.event.stopImmediatePropagation();\n  }\n  getValue() {\n    return this.value || '';\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  renderOnOffSwitchLabels(mode, checked) {\n    const icon = this.getSwitchLabelIcon(mode, checked);\n    return h(\"ion-icon\", {\n      class: {\n        'toggle-switch-icon': true,\n        'toggle-switch-icon-checked': checked\n      },\n      icon: icon,\n      \"aria-hidden\": \"true\"\n    });\n  }\n  renderToggleControl() {\n    const mode = getIonMode(this);\n    const {\n      enableOnOffLabels,\n      checked\n    } = this;\n    return h(\"div\", {\n      class: \"toggle-icon\",\n      part: \"track\",\n      ref: el => this.toggleTrack = el\n    }, enableOnOffLabels && mode === 'ios' && [this.renderOnOffSwitchLabels(mode, true), this.renderOnOffSwitchLabels(mode, false)], h(\"div\", {\n      class: \"toggle-icon-wrapper\"\n    }, h(\"div\", {\n      class: \"toggle-inner\",\n      part: \"handle\"\n    }, enableOnOffLabels && mode === 'md' && this.renderOnOffSwitchLabels(mode, checked))));\n  }\n  get hasLabel() {\n    return this.el.textContent !== '';\n  }\n  render() {\n    const {\n      legacyFormController\n    } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacyToggle() : this.renderToggle();\n  }\n  renderToggle() {\n    const {\n      activated,\n      color,\n      checked,\n      disabled,\n      el,\n      justify,\n      labelPlacement,\n      inputId,\n      name,\n      alignment\n    } = this;\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    return h(Host, {\n      onClick: this.onClick,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'toggle-activated': activated,\n        'toggle-checked': checked,\n        'toggle-disabled': disabled,\n        [`toggle-justify-${justify}`]: true,\n        [`toggle-alignment-${alignment}`]: true,\n        [`toggle-label-placement-${labelPlacement}`]: true,\n        [`toggle-${rtl}`]: true\n      })\n    }, h(\"label\", {\n      class: \"toggle-wrapper\"\n    }, h(\"input\", Object.assign({\n      type: \"checkbox\",\n      role: \"switch\",\n      \"aria-checked\": `${checked}`,\n      checked: checked,\n      disabled: disabled,\n      id: inputId,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl\n    }, this.inheritedAttributes)), h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      },\n      part: \"label\"\n    }, h(\"slot\", null)), h(\"div\", {\n      class: \"native-wrapper\"\n    }, this.renderToggleControl())));\n  }\n  renderLegacyToggle() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-toggle now requires providing a label with either the default slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-toggle>Email</ion-toggle>\nExample with aria-label: <ion-toggle aria-label=\"Email\"></ion-toggle>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-toggle is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new toggle syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const {\n      activated,\n      color,\n      checked,\n      disabled,\n      el,\n      inputId,\n      name\n    } = this;\n    const mode = getIonMode(this);\n    const {\n      label,\n      labelId,\n      labelText\n    } = getAriaLabel(el, inputId);\n    const value = this.getValue();\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    return h(Host, {\n      onClick: this.onClick,\n      \"aria-labelledby\": label ? labelId : null,\n      \"aria-checked\": `${checked}`,\n      \"aria-hidden\": disabled ? 'true' : null,\n      role: \"switch\",\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'toggle-activated': activated,\n        'toggle-checked': checked,\n        'toggle-disabled': disabled,\n        'legacy-toggle': true,\n        interactive: true,\n        [`toggle-${rtl}`]: true\n      })\n    }, this.renderToggleControl(), h(\"label\", {\n      htmlFor: inputId\n    }, labelText), h(\"input\", {\n      type: \"checkbox\",\n      role: \"switch\",\n      \"aria-checked\": `${checked}`,\n      disabled: disabled,\n      id: inputId,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nconst shouldToggle = (rtl, checked, deltaX, margin) => {\n  if (checked) {\n    return !rtl && margin > deltaX || rtl && -margin < deltaX;\n  } else {\n    return !rtl && -margin < deltaX || rtl && margin > deltaX;\n  }\n};\nlet toggleIds = 0;\nToggle.style = {\n  ios: IonToggleIosStyle0,\n  md: IonToggleMdStyle0\n};\nexport { Toggle as ion_toggle };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "c", "createLegacyFormController", "i", "inheritAriaAttributes", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "hapticSelection", "isRTL", "createColorClasses", "hostContext", "checkmarkOutline", "removeOutline", "g", "ellipseOutline", "config", "b", "getIonMode", "toggleIosCss", "IonToggleIosStyle0", "toggleMdCss", "IonToggleMdStyle0", "Toggle", "constructor", "hostRef", "_this", "ionChange", "ionFocus", "ionBlur", "ionStyle", "inputId", "toggleIds", "lastDrag", "inheritedAttributes", "didLoad", "hasLoggedDeprecationWarning", "setupGesture", "_asyncToGenerator", "toggleTrack", "gesture", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "passive", "onStart", "onMove", "ev", "onEnd", "disabled<PERSON><PERSON>ed", "onClick", "disabled", "preventDefault", "Date", "now", "toggleChecked", "onFocus", "emit", "onBlur", "getSwitchLabelIcon", "mode", "checked", "activated", "color", "undefined", "name", "value", "enableOnOffLabels", "get", "labelPlacement", "legacy", "justify", "alignment", "emitStyle", "enable", "isNowChecked", "connectedCallback", "_this2", "legacyFormController", "componentDidLoad", "disconnectedCallback", "destroy", "componentWillLoad", "hasLegacyControl", "Object", "assign", "setFocus", "detail", "shouldToggle", "deltaX", "event", "stopImmediatePropagation", "getValue", "focusEl", "focus", "renderOnOffSwitchLabels", "icon", "class", "renderToggleControl", "part", "ref", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "render", "renderLegacyToggle", "renderToggle", "rtl", "type", "role", "id", "label", "labelId", "labelText", "interactive", "htmlFor", "watchers", "margin", "style", "ios", "md", "ion_toggle"], "sources": ["E:/Fahion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as hapticSelection } from './haptic-554688a5.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { f as checkmarkOutline, r as removeOutline, g as ellipseOutline } from './index-f7dc70ba.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\n\nconst toggleIosCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;outline:none;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item:not(.legacy-toggle)){width:100%;height:100%}:host([slot=start]:not(.legacy-toggle)),:host([slot=end]:not(.legacy-toggle)){width:auto}:host(.legacy-toggle){contain:content;-ms-touch-action:none;touch-action:none}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}:host(.legacy-toggle) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0;pointer-events:none}@supports (inset-inline-start: 0){:host(.legacy-toggle) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-toggle) label{left:0}:host-context([dir=rtl]):host(.legacy-toggle) label,:host-context([dir=rtl]).legacy-toggle label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-toggle:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-toggle) label::-moz-focus-inner{border:0}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-toggle)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.088);--track-background-checked:var(--ion-color-primary, #3880ff);--border-radius:16px;--handle-background:#ffffff;--handle-background-checked:#ffffff;--handle-border-radius:25.5px;--handle-box-shadow:0 3px 12px rgba(0, 0, 0, 0.16), 0 3px 1px rgba(0, 0, 0, 0.1);--handle-height:calc(32px - (2px * 2));--handle-max-height:calc(100% - var(--handle-spacing) * 2);--handle-width:calc(32px - (2px * 2));--handle-spacing:2px;--handle-transition:transform 300ms, width 120ms ease-in-out 80ms, left 110ms ease-in-out 80ms, right 110ms ease-in-out 80ms}:host(.legacy-toggle){width:51px;height:32px;contain:strict;overflow:hidden}.native-wrapper .toggle-icon{width:51px;height:32px;overflow:hidden}:host(.ion-color.toggle-checked) .toggle-icon{background:var(--ion-color-base)}:host(.toggle-activated) .toggle-switch-icon{opacity:0}.toggle-icon{-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);-webkit-transition:background-color 300ms;transition:background-color 300ms}.toggle-inner{will-change:transform}.toggle-switch-icon{position:absolute;top:50%;width:11px;height:11px;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:opacity 300ms, color 300ms;transition:opacity 300ms, color 300ms}.toggle-switch-icon{position:absolute;color:var(--ion-color-dark)}:host(.toggle-ltr) .toggle-switch-icon{right:6px}:host(.toggle-rtl) .toggle-switch-icon{right:initial;left:6px;}:host(.toggle-checked) .toggle-switch-icon.toggle-switch-icon-checked{color:var(--ion-color-contrast, #fff)}:host(.toggle-checked) .toggle-switch-icon:not(.toggle-switch-icon-checked){opacity:0}.toggle-switch-icon-checked{position:absolute;width:15px;height:15px;-webkit-transform:translateY(-50%) rotate(90deg);transform:translateY(-50%) rotate(90deg)}:host(.toggle-ltr) .toggle-switch-icon-checked{right:initial;left:4px;}:host(.toggle-rtl) .toggle-switch-icon-checked{right:4px}:host(.toggle-activated) .toggle-icon::before,:host(.toggle-checked) .toggle-icon::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated.toggle-checked) .toggle-inner::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated) .toggle-inner{width:calc(var(--handle-width) + 6px)}:host(.toggle-ltr.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0);transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0)}:host(.toggle-rtl.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0);transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0)}:host(.toggle-disabled){opacity:0.3}:host(.in-item.legacy-toggle){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:0;padding-inline-end:0;padding-top:6px;padding-bottom:5px}:host(.in-item.legacy-toggle[slot=start]){-webkit-padding-start:0;padding-inline-start:0;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:6px;padding-bottom:5px}\";\nconst IonToggleIosStyle0 = toggleIosCss;\n\nconst toggleMdCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;outline:none;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item:not(.legacy-toggle)){width:100%;height:100%}:host([slot=start]:not(.legacy-toggle)),:host([slot=end]:not(.legacy-toggle)){width:auto}:host(.legacy-toggle){contain:content;-ms-touch-action:none;touch-action:none}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}:host(.legacy-toggle) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0;pointer-events:none}@supports (inset-inline-start: 0){:host(.legacy-toggle) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-toggle) label{left:0}:host-context([dir=rtl]):host(.legacy-toggle) label,:host-context([dir=rtl]).legacy-toggle label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-toggle:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-toggle) label::-moz-focus-inner{border:0}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-toggle)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.39);--track-background-checked:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.5);--border-radius:14px;--handle-background:#ffffff;--handle-background-checked:var(--ion-color-primary, #3880ff);--handle-border-radius:50%;--handle-box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--handle-width:20px;--handle-height:20px;--handle-max-height:calc(100% + 6px);--handle-spacing:0;--handle-transition:transform 160ms cubic-bezier(0.4, 0, 0.2, 1), background-color 160ms cubic-bezier(0.4, 0, 0.2, 1)}:host(.legacy-toggle){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;width:36px;height:14px;contain:strict}.native-wrapper .toggle-icon{width:36px;height:14px}:host(.ion-color.toggle-checked) .toggle-icon{background:rgba(var(--ion-color-base-rgb), 0.5)}:host(.ion-color.toggle-checked) .toggle-inner{background:var(--ion-color-base)}:host(.toggle-checked) .toggle-inner{color:var(--ion-color-contrast, #fff)}.toggle-icon{-webkit-transition:background-color 160ms;transition:background-color 160ms}.toggle-inner{will-change:background-color, transform;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;color:#000}.toggle-inner .toggle-switch-icon{-webkit-padding-start:1px;padding-inline-start:1px;-webkit-padding-end:1px;padding-inline-end:1px;padding-top:1px;padding-bottom:1px;width:100%;height:100%}:host(.toggle-disabled){opacity:0.38}:host(.in-item.legacy-toggle){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:0;padding-inline-end:0;padding-top:12px;padding-bottom:12px;cursor:pointer}:host(.in-item.legacy-toggle[slot=start]){-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:18px;padding-inline-end:18px;padding-top:12px;padding-bottom:12px}\";\nconst IonToggleMdStyle0 = toggleMdCss;\n\nconst Toggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-tg-${toggleIds++}`;\n        this.lastDrag = 0;\n        this.inheritedAttributes = {};\n        this.didLoad = false;\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        this.setupGesture = async () => {\n            const { toggleTrack } = this;\n            if (toggleTrack) {\n                this.gesture = (await import('./index-2cf77112.js')).createGesture({\n                    el: toggleTrack,\n                    gestureName: 'toggle',\n                    gesturePriority: 100,\n                    threshold: 5,\n                    passive: false,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.disabledChanged();\n            }\n        };\n        this.onClick = (ev) => {\n            if (this.disabled) {\n                return;\n            }\n            ev.preventDefault();\n            if (this.lastDrag + 300 < Date.now()) {\n                this.toggleChecked();\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.getSwitchLabelIcon = (mode, checked) => {\n            if (mode === 'md') {\n                return checked ? checkmarkOutline : removeOutline;\n            }\n            return checked ? removeOutline : ellipseOutline;\n        };\n        this.activated = false;\n        this.color = undefined;\n        this.name = this.inputId;\n        this.checked = false;\n        this.disabled = false;\n        this.value = 'on';\n        this.enableOnOffLabels = config.get('toggleOnOffLabels');\n        this.labelPlacement = 'start';\n        this.legacy = undefined;\n        this.justify = 'space-between';\n        this.alignment = 'center';\n    }\n    disabledChanged() {\n        this.emitStyle();\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    toggleChecked() {\n        const { checked, value } = this;\n        const isNowChecked = !checked;\n        this.checked = isNowChecked;\n        this.ionChange.emit({\n            checked: isNowChecked,\n            value,\n        });\n    }\n    async connectedCallback() {\n        this.legacyFormController = createLegacyFormController(this.el);\n        /**\n         * If we have not yet rendered\n         * ion-toggle, then toggleTrack is not defined.\n         * But if we are moving ion-toggle via appendChild,\n         * then toggleTrack will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n    }\n    componentDidLoad() {\n        this.setupGesture();\n        this.didLoad = true;\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    componentWillLoad() {\n        this.emitStyle();\n        if (!this.legacyFormController.hasLegacyControl()) {\n            this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n        }\n    }\n    emitStyle() {\n        if (this.legacyFormController.hasLegacyControl()) {\n            this.ionStyle.emit({\n                'interactive-disabled': this.disabled,\n                // TODO(FW-2990): remove this\n                legacy: !!this.legacy,\n            });\n        }\n    }\n    onStart() {\n        this.activated = true;\n        // touch-action does not work in iOS\n        this.setFocus();\n    }\n    onMove(detail) {\n        if (shouldToggle(isRTL(this.el), this.checked, detail.deltaX, -10)) {\n            this.toggleChecked();\n            hapticSelection();\n        }\n    }\n    onEnd(ev) {\n        this.activated = false;\n        this.lastDrag = Date.now();\n        ev.event.preventDefault();\n        ev.event.stopImmediatePropagation();\n    }\n    getValue() {\n        return this.value || '';\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    renderOnOffSwitchLabels(mode, checked) {\n        const icon = this.getSwitchLabelIcon(mode, checked);\n        return (h(\"ion-icon\", { class: {\n                'toggle-switch-icon': true,\n                'toggle-switch-icon-checked': checked,\n            }, icon: icon, \"aria-hidden\": \"true\" }));\n    }\n    renderToggleControl() {\n        const mode = getIonMode(this);\n        const { enableOnOffLabels, checked } = this;\n        return (h(\"div\", { class: \"toggle-icon\", part: \"track\", ref: (el) => (this.toggleTrack = el) }, enableOnOffLabels &&\n            mode === 'ios' && [this.renderOnOffSwitchLabels(mode, true), this.renderOnOffSwitchLabels(mode, false)], h(\"div\", { class: \"toggle-icon-wrapper\" }, h(\"div\", { class: \"toggle-inner\", part: \"handle\" }, enableOnOffLabels && mode === 'md' && this.renderOnOffSwitchLabels(mode, checked)))));\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacyToggle() : this.renderToggle();\n    }\n    renderToggle() {\n        const { activated, color, checked, disabled, el, justify, labelPlacement, inputId, name, alignment } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'toggle-activated': activated,\n                'toggle-checked': checked,\n                'toggle-disabled': disabled,\n                [`toggle-justify-${justify}`]: true,\n                [`toggle-alignment-${alignment}`]: true,\n                [`toggle-label-placement-${labelPlacement}`]: true,\n                [`toggle-${rtl}`]: true,\n            }) }, h(\"label\", { class: \"toggle-wrapper\" }, h(\"input\", Object.assign({ type: \"checkbox\", role: \"switch\", \"aria-checked\": `${checked}`, checked: checked, disabled: disabled, id: inputId, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) }, this.inheritedAttributes)), h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, h(\"slot\", null)), h(\"div\", { class: \"native-wrapper\" }, this.renderToggleControl()))));\n    }\n    renderLegacyToggle() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-toggle now requires providing a label with either the default slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-toggle>Email</ion-toggle>\nExample with aria-label: <ion-toggle aria-label=\"Email\"></ion-toggle>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-toggle is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new toggle syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const { activated, color, checked, disabled, el, inputId, name } = this;\n        const mode = getIonMode(this);\n        const { label, labelId, labelText } = getAriaLabel(el, inputId);\n        const value = this.getValue();\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { onClick: this.onClick, \"aria-labelledby\": label ? labelId : null, \"aria-checked\": `${checked}`, \"aria-hidden\": disabled ? 'true' : null, role: \"switch\", class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'toggle-activated': activated,\n                'toggle-checked': checked,\n                'toggle-disabled': disabled,\n                'legacy-toggle': true,\n                interactive: true,\n                [`toggle-${rtl}`]: true,\n            }) }, this.renderToggleControl(), h(\"label\", { htmlFor: inputId }, labelText), h(\"input\", { type: \"checkbox\", role: \"switch\", \"aria-checked\": `${checked}`, disabled: disabled, id: inputId, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst shouldToggle = (rtl, checked, deltaX, margin) => {\n    if (checked) {\n        return (!rtl && margin > deltaX) || (rtl && -margin < deltaX);\n    }\n    else {\n        return (!rtl && -margin < deltaX) || (rtl && margin > deltaX);\n    }\n};\nlet toggleIds = 0;\nToggle.style = {\n    ios: IonToggleIosStyle0,\n    md: IonToggleMdStyle0\n};\n\nexport { Toggle as ion_toggle };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASC,CAAC,IAAIC,0BAA0B,QAAQ,+BAA+B;AAC/E,SAASC,CAAC,IAAIC,qBAAqB,EAAEV,CAAC,IAAIW,iBAAiB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAC7G,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASR,CAAC,IAAIS,eAAe,QAAQ,sBAAsB;AAC3D,SAASP,CAAC,IAAIQ,KAAK,QAAQ,mBAAmB;AAC9C,SAASV,CAAC,IAAIW,kBAAkB,EAAEhB,CAAC,IAAIiB,WAAW,QAAQ,qBAAqB;AAC/E,SAASd,CAAC,IAAIe,gBAAgB,EAAEtB,CAAC,IAAIuB,aAAa,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AACpG,SAAShB,CAAC,IAAIiB,MAAM,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AACzE,OAAO,yBAAyB;AAChC,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAG,yzTAAyzT;AAC90T,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,2sRAA2sR;AAC/tR,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBnC,gBAAgB,CAAC,IAAI,EAAEkC,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAGlC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACmC,QAAQ,GAAGnC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACoC,OAAO,GAAGpC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACqC,QAAQ,GAAGrC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsC,OAAO,GAAG,UAAUC,SAAS,EAAE,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC,IAAI,CAACC,YAAY,gBAAAC,iBAAA,CAAG,aAAY;MAC5B,MAAM;QAAEC;MAAY,CAAC,GAAGb,KAAI;MAC5B,IAAIa,WAAW,EAAE;QACbb,KAAI,CAACc,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEC,aAAa,CAAC;UAC/DC,EAAE,EAAEH,WAAW;UACfI,WAAW,EAAE,QAAQ;UACrBC,eAAe,EAAE,GAAG;UACpBC,SAAS,EAAE,CAAC;UACZC,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEA,CAAA,KAAMrB,KAAI,CAACqB,OAAO,CAAC,CAAC;UAC7BC,MAAM,EAAGC,EAAE,IAAKvB,KAAI,CAACsB,MAAM,CAACC,EAAE,CAAC;UAC/BC,KAAK,EAAGD,EAAE,IAAKvB,KAAI,CAACwB,KAAK,CAACD,EAAE;QAChC,CAAC,CAAC;QACFvB,KAAI,CAACyB,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAIH,EAAE,IAAK;MACnB,IAAI,IAAI,CAACI,QAAQ,EAAE;QACf;MACJ;MACAJ,EAAE,CAACK,cAAc,CAAC,CAAC;MACnB,IAAI,IAAI,CAACrB,QAAQ,GAAG,GAAG,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAClC,IAAI,CAACC,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC9B,QAAQ,CAAC+B,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC/B,OAAO,CAAC8B,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,kBAAkB,GAAG,CAACC,IAAI,EAAEC,OAAO,KAAK;MACzC,IAAID,IAAI,KAAK,IAAI,EAAE;QACf,OAAOC,OAAO,GAAGnD,gBAAgB,GAAGC,aAAa;MACrD;MACA,OAAOkD,OAAO,GAAGlD,aAAa,GAAGE,cAAc;IACnD,CAAC;IACD,IAAI,CAACiD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACpC,OAAO;IACxB,IAAI,CAACgC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACV,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,iBAAiB,GAAGrD,MAAM,CAACsD,GAAG,CAAC,mBAAmB,CAAC;IACxD,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,MAAM,GAAGN,SAAS;IACvB,IAAI,CAACO,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;EAC7B;EACAvB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACwB,SAAS,CAAC,CAAC;IAChB,IAAI,IAAI,CAACnC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACoC,MAAM,CAAC,CAAC,IAAI,CAACvB,QAAQ,CAAC;IACvC;EACJ;EACAI,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEM,OAAO;MAAEK;IAAM,CAAC,GAAG,IAAI;IAC/B,MAAMS,YAAY,GAAG,CAACd,OAAO;IAC7B,IAAI,CAACA,OAAO,GAAGc,YAAY;IAC3B,IAAI,CAAClD,SAAS,CAACgC,IAAI,CAAC;MAChBI,OAAO,EAAEc,YAAY;MACrBT;IACJ,CAAC,CAAC;EACN;EACMU,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzC,iBAAA;MACtByC,MAAI,CAACC,oBAAoB,GAAGhF,0BAA0B,CAAC+E,MAAI,CAACrC,EAAE,CAAC;MAC/D;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIqC,MAAI,CAAC5C,OAAO,EAAE;QACd4C,MAAI,CAAC1C,YAAY,CAAC,CAAC;MACvB;IAAC;EACL;EACA4C,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC5C,YAAY,CAAC,CAAC;IACnB,IAAI,CAACF,OAAO,GAAG,IAAI;EACvB;EACA+C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC1C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC2C,OAAO,CAAC,CAAC;MACtB,IAAI,CAAC3C,OAAO,GAAG0B,SAAS;IAC5B;EACJ;EACAkB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACT,SAAS,CAAC,CAAC;IAChB,IAAI,CAAC,IAAI,CAACK,oBAAoB,CAACK,gBAAgB,CAAC,CAAC,EAAE;MAC/C,IAAI,CAACnD,mBAAmB,GAAGoD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErF,qBAAqB,CAAC,IAAI,CAACwC,EAAE,CAAC,CAAC;IAChF;EACJ;EACAiC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACK,oBAAoB,CAACK,gBAAgB,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACvD,QAAQ,CAAC6B,IAAI,CAAC;QACf,sBAAsB,EAAE,IAAI,CAACN,QAAQ;QACrC;QACAmB,MAAM,EAAE,CAAC,CAAC,IAAI,CAACA;MACnB,CAAC,CAAC;IACN;EACJ;EACAzB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACiB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACwB,QAAQ,CAAC,CAAC;EACnB;EACAxC,MAAMA,CAACyC,MAAM,EAAE;IACX,IAAIC,YAAY,CAACjF,KAAK,CAAC,IAAI,CAACiC,EAAE,CAAC,EAAE,IAAI,CAACqB,OAAO,EAAE0B,MAAM,CAACE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE;MAChE,IAAI,CAAClC,aAAa,CAAC,CAAC;MACpBjD,eAAe,CAAC,CAAC;IACrB;EACJ;EACA0C,KAAKA,CAACD,EAAE,EAAE;IACN,IAAI,CAACe,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC/B,QAAQ,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1BP,EAAE,CAAC2C,KAAK,CAACtC,cAAc,CAAC,CAAC;IACzBL,EAAE,CAAC2C,KAAK,CAACC,wBAAwB,CAAC,CAAC;EACvC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1B,KAAK,IAAI,EAAE;EAC3B;EACAoB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACO,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACAC,uBAAuBA,CAACnC,IAAI,EAAEC,OAAO,EAAE;IACnC,MAAMmC,IAAI,GAAG,IAAI,CAACrC,kBAAkB,CAACC,IAAI,EAAEC,OAAO,CAAC;IACnD,OAAQrE,CAAC,CAAC,UAAU,EAAE;MAAEyG,KAAK,EAAE;QACvB,oBAAoB,EAAE,IAAI;QAC1B,4BAA4B,EAAEpC;MAClC,CAAC;MAAEmC,IAAI,EAAEA,IAAI;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC;EAC/C;EACAE,mBAAmBA,CAAA,EAAG;IAClB,MAAMtC,IAAI,GAAG5C,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEmD,iBAAiB;MAAEN;IAAQ,CAAC,GAAG,IAAI;IAC3C,OAAQrE,CAAC,CAAC,KAAK,EAAE;MAAEyG,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,OAAO;MAAEC,GAAG,EAAG5D,EAAE,IAAM,IAAI,CAACH,WAAW,GAAGG;IAAI,CAAC,EAAE2B,iBAAiB,IAC7GP,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,CAACmC,uBAAuB,CAACnC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAACmC,uBAAuB,CAACnC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAEpE,CAAC,CAAC,KAAK,EAAE;MAAEyG,KAAK,EAAE;IAAsB,CAAC,EAAEzG,CAAC,CAAC,KAAK,EAAE;MAAEyG,KAAK,EAAE,cAAc;MAAEE,IAAI,EAAE;IAAS,CAAC,EAAEhC,iBAAiB,IAAIP,IAAI,KAAK,IAAI,IAAI,IAAI,CAACmC,uBAAuB,CAACnC,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC;EACpS;EACA,IAAIwC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7D,EAAE,CAAC8D,WAAW,KAAK,EAAE;EACrC;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEzB;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAACK,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACqB,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;EACpG;EACAA,YAAYA,CAAA,EAAG;IACX,MAAM;MAAE3C,SAAS;MAAEC,KAAK;MAAEF,OAAO;MAAEV,QAAQ;MAAEX,EAAE;MAAE+B,OAAO;MAAEF,cAAc;MAAExC,OAAO;MAAEoC,IAAI;MAAEO;IAAU,CAAC,GAAG,IAAI;IAC3G,MAAMZ,IAAI,GAAG5C,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkD,KAAK,GAAG,IAAI,CAAC0B,QAAQ,CAAC,CAAC;IAC7B,MAAMc,GAAG,GAAGnG,KAAK,CAACiC,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrCvC,iBAAiB,CAAC,IAAI,EAAEuC,EAAE,EAAEyB,IAAI,EAAEJ,OAAO,GAAGK,KAAK,GAAG,EAAE,EAAEf,QAAQ,CAAC;IACjE,OAAQ3D,CAAC,CAACE,IAAI,EAAE;MAAEwD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE+C,KAAK,EAAEzF,kBAAkB,CAACuD,KAAK,EAAE;QAClE,CAACH,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEnD,WAAW,CAAC,UAAU,EAAE+B,EAAE,CAAC;QACtC,kBAAkB,EAAEsB,SAAS;QAC7B,gBAAgB,EAAED,OAAO;QACzB,iBAAiB,EAAEV,QAAQ;QAC3B,CAAC,kBAAkBoB,OAAO,EAAE,GAAG,IAAI;QACnC,CAAC,oBAAoBC,SAAS,EAAE,GAAG,IAAI;QACvC,CAAC,0BAA0BH,cAAc,EAAE,GAAG,IAAI;QAClD,CAAC,UAAUqC,GAAG,EAAE,GAAG;MACvB,CAAC;IAAE,CAAC,EAAElH,CAAC,CAAC,OAAO,EAAE;MAAEyG,KAAK,EAAE;IAAiB,CAAC,EAAEzG,CAAC,CAAC,OAAO,EAAE4F,MAAM,CAACC,MAAM,CAAC;MAAEsB,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,QAAQ;MAAE,cAAc,EAAE,GAAG/C,OAAO,EAAE;MAAEA,OAAO,EAAEA,OAAO;MAAEV,QAAQ,EAAEA,QAAQ;MAAE0D,EAAE,EAAEhF,OAAO;MAAE2B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEE,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAE0C,GAAG,EAAGP,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,EAAE,IAAI,CAAC7D,mBAAmB,CAAC,CAAC,EAAExC,CAAC,CAAC,KAAK,EAAE;MAAEyG,KAAK,EAAE;QAChV,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACI;MACvC,CAAC;MAAEF,IAAI,EAAE;IAAQ,CAAC,EAAE3G,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,CAAC,KAAK,EAAE;MAAEyG,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;EACjH;EACAM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACtE,2BAA2B,EAAE;MACnC7B,eAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACmC,EAAE,CAAC;MAC9M,IAAI,IAAI,CAAC8B,MAAM,EAAE;QACbjE,eAAe,CAAC;AAChC;AACA,sHAAsH,EAAE,IAAI,CAACmC,EAAE,CAAC;MACpH;MACA,IAAI,CAACN,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAM;MAAE4B,SAAS;MAAEC,KAAK;MAAEF,OAAO;MAAEV,QAAQ;MAAEX,EAAE;MAAEX,OAAO;MAAEoC;IAAK,CAAC,GAAG,IAAI;IACvE,MAAML,IAAI,GAAG5C,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAE8F,KAAK;MAAEC,OAAO;MAAEC;IAAU,CAAC,GAAG7G,YAAY,CAACqC,EAAE,EAAEX,OAAO,CAAC;IAC/D,MAAMqC,KAAK,GAAG,IAAI,CAAC0B,QAAQ,CAAC,CAAC;IAC7B,MAAMc,GAAG,GAAGnG,KAAK,CAACiC,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrCvC,iBAAiB,CAAC,IAAI,EAAEuC,EAAE,EAAEyB,IAAI,EAAEJ,OAAO,GAAGK,KAAK,GAAG,EAAE,EAAEf,QAAQ,CAAC;IACjE,OAAQ3D,CAAC,CAACE,IAAI,EAAE;MAAEwD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,iBAAiB,EAAE4D,KAAK,GAAGC,OAAO,GAAG,IAAI;MAAE,cAAc,EAAE,GAAGlD,OAAO,EAAE;MAAE,aAAa,EAAEV,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEyD,IAAI,EAAE,QAAQ;MAAEX,KAAK,EAAEzF,kBAAkB,CAACuD,KAAK,EAAE;QACpM,CAACH,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEnD,WAAW,CAAC,UAAU,EAAE+B,EAAE,CAAC;QACtC,kBAAkB,EAAEsB,SAAS;QAC7B,gBAAgB,EAAED,OAAO;QACzB,iBAAiB,EAAEV,QAAQ;QAC3B,eAAe,EAAE,IAAI;QACrB8D,WAAW,EAAE,IAAI;QACjB,CAAC,UAAUP,GAAG,EAAE,GAAG;MACvB,CAAC;IAAE,CAAC,EAAE,IAAI,CAACR,mBAAmB,CAAC,CAAC,EAAE1G,CAAC,CAAC,OAAO,EAAE;MAAE0H,OAAO,EAAErF;IAAQ,CAAC,EAAEmF,SAAS,CAAC,EAAExH,CAAC,CAAC,OAAO,EAAE;MAAEmH,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,QAAQ;MAAE,cAAc,EAAE,GAAG/C,OAAO,EAAE;MAAEV,QAAQ,EAAEA,QAAQ;MAAE0D,EAAE,EAAEhF,OAAO;MAAE2B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEE,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAE0C,GAAG,EAAGP,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC,CAAC;EAC/S;EACA,IAAIrD,EAAEA,CAAA,EAAG;IAAE,OAAO5C,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACD,MAAM3B,YAAY,GAAGA,CAACkB,GAAG,EAAE7C,OAAO,EAAE4B,MAAM,EAAE2B,MAAM,KAAK;EACnD,IAAIvD,OAAO,EAAE;IACT,OAAQ,CAAC6C,GAAG,IAAIU,MAAM,GAAG3B,MAAM,IAAMiB,GAAG,IAAI,CAACU,MAAM,GAAG3B,MAAO;EACjE,CAAC,MACI;IACD,OAAQ,CAACiB,GAAG,IAAI,CAACU,MAAM,GAAG3B,MAAM,IAAMiB,GAAG,IAAIU,MAAM,GAAG3B,MAAO;EACjE;AACJ,CAAC;AACD,IAAI3D,SAAS,GAAG,CAAC;AACjBT,MAAM,CAACgG,KAAK,GAAG;EACXC,GAAG,EAAEpG,kBAAkB;EACvBqG,EAAE,EAAEnG;AACR,CAAC;AAED,SAASC,MAAM,IAAImG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}