{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let BillService = /*#__PURE__*/(() => {\n  class BillService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl;\n    }\n    generateBillPDF(billData) {\n      const doc = new jsPDF();\n      // Company Header\n      doc.setFontSize(24);\n      doc.setTextColor(102, 126, 234); // DFashion brand color\n      doc.text('DFashion', 20, 30);\n      doc.setFontSize(12);\n      doc.setTextColor(0, 0, 0);\n      doc.text('Fashion & Lifestyle Store', 20, 40);\n      doc.text('Email: <EMAIL>', 20, 50);\n      doc.text('Phone: +91 9876543210', 20, 60);\n      // Bill Title\n      doc.setFontSize(18);\n      doc.setTextColor(102, 126, 234);\n      doc.text('INVOICE', 150, 30);\n      // Bill Details\n      doc.setFontSize(10);\n      doc.setTextColor(0, 0, 0);\n      doc.text(`Bill No: ${billData.billNumber}`, 150, 45);\n      doc.text(`Order ID: ${billData.orderId}`, 150, 55);\n      doc.text(`Payment ID: ${billData.paymentId}`, 150, 65);\n      doc.text(`Date: ${new Date(billData.orderDate).toLocaleDateString()}`, 150, 75);\n      // Customer Details\n      doc.setFontSize(14);\n      doc.setTextColor(102, 126, 234);\n      doc.text('Bill To:', 20, 85);\n      doc.setFontSize(10);\n      doc.setTextColor(0, 0, 0);\n      doc.text(billData.customerName, 20, 95);\n      doc.text(billData.customerEmail, 20, 105);\n      doc.text(billData.customerPhone, 20, 115);\n      // Address (split into multiple lines if needed)\n      const addressLines = this.splitText(billData.customerAddress, 50);\n      let addressY = 125;\n      addressLines.forEach(line => {\n        doc.text(line, 20, addressY);\n        addressY += 10;\n      });\n      // Items Table\n      const tableStartY = Math.max(addressY + 10, 140);\n      const tableColumns = ['Item', 'Qty', 'Price', 'Total'];\n      const tableRows = billData.items.map(item => [item.name, item.quantity.toString(), `₹${item.price.toFixed(2)}`, `₹${item.total.toFixed(2)}`]);\n      doc.autoTable({\n        head: [tableColumns],\n        body: tableRows,\n        startY: tableStartY,\n        theme: 'grid',\n        headStyles: {\n          fillColor: [102, 126, 234],\n          textColor: [255, 255, 255],\n          fontSize: 10\n        },\n        bodyStyles: {\n          fontSize: 9\n        },\n        columnStyles: {\n          0: {\n            cellWidth: 80\n          },\n          1: {\n            cellWidth: 20,\n            halign: 'center'\n          },\n          2: {\n            cellWidth: 30,\n            halign: 'right'\n          },\n          3: {\n            cellWidth: 30,\n            halign: 'right'\n          }\n        }\n      });\n      // Calculate totals section Y position\n      const finalY = doc.lastAutoTable.finalY + 20;\n      // Totals Section\n      const totalsX = 130;\n      doc.setFontSize(10);\n      doc.text('Subtotal:', totalsX, finalY);\n      doc.text(`₹${billData.subtotal.toFixed(2)}`, totalsX + 40, finalY);\n      if (billData.discount > 0) {\n        doc.text('Discount:', totalsX, finalY + 10);\n        doc.text(`-₹${billData.discount.toFixed(2)}`, totalsX + 40, finalY + 10);\n      }\n      doc.text('Shipping:', totalsX, finalY + 20);\n      doc.text(`₹${billData.shipping.toFixed(2)}`, totalsX + 40, finalY + 20);\n      doc.text('Tax (GST):', totalsX, finalY + 30);\n      doc.text(`₹${billData.tax.toFixed(2)}`, totalsX + 40, finalY + 30);\n      // Total\n      doc.setFontSize(12);\n      doc.setTextColor(102, 126, 234);\n      doc.text('Total:', totalsX, finalY + 45);\n      doc.text(`₹${billData.total.toFixed(2)}`, totalsX + 40, finalY + 45);\n      // Payment Method\n      doc.setFontSize(10);\n      doc.setTextColor(0, 0, 0);\n      doc.text(`Payment Method: ${billData.paymentMethod}`, 20, finalY + 60);\n      // Footer\n      doc.setFontSize(8);\n      doc.setTextColor(128, 128, 128);\n      doc.text('Thank you for shopping with DFashion!', 20, finalY + 80);\n      doc.text('For any queries, contact <NAME_EMAIL>', 20, finalY + 90);\n      return doc;\n    }\n    splitText(text, maxLength) {\n      const words = text.split(' ');\n      const lines = [];\n      let currentLine = '';\n      words.forEach(word => {\n        if ((currentLine + word).length <= maxLength) {\n          currentLine += (currentLine ? ' ' : '') + word;\n        } else {\n          if (currentLine) lines.push(currentLine);\n          currentLine = word;\n        }\n      });\n      if (currentLine) lines.push(currentLine);\n      return lines;\n    }\n    downloadBill(billData) {\n      const doc = this.generateBillPDF(billData);\n      doc.save(`DFashion_Invoice_${billData.billNumber}.pdf`);\n    }\n    generateBillBlob(billData) {\n      const doc = this.generateBillPDF(billData);\n      return doc.output('blob');\n    }\n    sendBillEmail(billData, message) {\n      const formData = new FormData();\n      // Generate PDF blob\n      const pdfBlob = this.generateBillBlob(billData);\n      formData.append('billpdf', pdfBlob, `DFashion_Invoice_${billData.billNumber}.pdf`);\n      // Add other data\n      formData.append('userId', billData.orderId); // Using orderId as userId for now\n      formData.append('name', billData.customerName);\n      formData.append('email', billData.customerEmail);\n      formData.append('message', message || 'Thank you for your purchase! Please find your invoice attached.');\n      formData.append('paymentType', JSON.stringify({\n        method: billData.paymentMethod,\n        paymentId: billData.paymentId,\n        orderId: billData.orderId\n      }));\n      return this.http.post(`${this.apiUrl}/email/send-bill`, formData);\n    }\n    generateBillNumber() {\n      const date = new Date();\n      const year = date.getFullYear().toString().slice(-2);\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n      return `DF${year}${month}${day}${random}`;\n    }\n    calculateTotals(items, shippingCost = 0, discountAmount = 0, taxRate = 0.18) {\n      const subtotal = items.reduce((sum, item) => sum + item.total, 0);\n      const discountedSubtotal = subtotal - discountAmount;\n      const tax = discountedSubtotal * taxRate;\n      const total = discountedSubtotal + tax + shippingCost;\n      return {\n        subtotal,\n        tax,\n        shipping: shippingCost,\n        discount: discountAmount,\n        total\n      };\n    }\n    static {\n      this.ɵfac = function BillService_Factory(t) {\n        return new (t || BillService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BillService,\n        factory: BillService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BillService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}