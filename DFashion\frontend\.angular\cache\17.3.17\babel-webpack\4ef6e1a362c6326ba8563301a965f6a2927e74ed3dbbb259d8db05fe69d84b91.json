{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class RolePipe {\n  constructor() {\n    this.roleDisplayNames = {\n      'super_admin': 'Super Administrator',\n      'admin': 'Administrator',\n      'sales_manager': 'Sales Manager',\n      'marketing_manager': 'Marketing Manager',\n      'account_manager': 'Account Manager',\n      'support_manager': 'Support Manager',\n      'sales_executive': 'Sales Executive',\n      'marketing_executive': 'Marketing Executive',\n      'account_executive': 'Account Executive',\n      'support_executive': 'Support Executive',\n      'customer': 'Customer',\n      'vendor': 'Vendor'\n    };\n  }\n  transform(role) {\n    return this.roleDisplayNames[role] || role;\n  }\n  static {\n    this.ɵfac = function RolePipe_Factory(t) {\n      return new (t || RolePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"role\",\n      type: RolePipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["RolePipe", "constructor", "roleDisplayNames", "transform", "role", "pure"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\pipes\\role.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n\n@Pipe({\n  name: 'role'\n})\nexport class RolePipe implements PipeTransform {\n\n  private roleDisplayNames: { [key: string]: string } = {\n    'super_admin': 'Super Administrator',\n    'admin': 'Administrator',\n    'sales_manager': 'Sales Manager',\n    'marketing_manager': 'Marketing Manager',\n    'account_manager': 'Account Manager',\n    'support_manager': 'Support Manager',\n    'sales_executive': 'Sales Executive',\n    'marketing_executive': 'Marketing Executive',\n    'account_executive': 'Account Executive',\n    'support_executive': 'Support Executive',\n    'customer': 'Customer',\n    'vendor': 'Vendor'\n  };\n\n  transform(role: string): string {\n    return this.roleDisplayNames[role] || role;\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,QAAQ;EAHrBC,YAAA;IAKU,KAAAC,gBAAgB,GAA8B;MACpD,aAAa,EAAE,qBAAqB;MACpC,OAAO,EAAE,eAAe;MACxB,eAAe,EAAE,eAAe;MAChC,mBAAmB,EAAE,mBAAmB;MACxC,iBAAiB,EAAE,iBAAiB;MACpC,iBAAiB,EAAE,iBAAiB;MACpC,iBAAiB,EAAE,iBAAiB;MACpC,qBAAqB,EAAE,qBAAqB;MAC5C,mBAAmB,EAAE,mBAAmB;MACxC,mBAAmB,EAAE,mBAAmB;MACxC,UAAU,EAAE,UAAU;MACtB,QAAQ,EAAE;KACX;;EAEDC,SAASA,CAACC,IAAY;IACpB,OAAO,IAAI,CAACF,gBAAgB,CAACE,IAAI,CAAC,IAAIA,IAAI;EAC5C;;;uBAnBWJ,QAAQ;IAAA;EAAA;;;;YAARA,QAAQ;MAAAK,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}