{"ast": null, "code": "export const authRoutes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'register',\n  loadComponent: () => import('./pages/register/register.component').then(m => m.RegisterComponent)\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}