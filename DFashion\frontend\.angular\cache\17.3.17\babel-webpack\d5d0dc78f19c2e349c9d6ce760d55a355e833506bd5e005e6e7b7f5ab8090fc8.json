{"ast": null, "code": "import { of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let PostsService = /*#__PURE__*/(() => {\n  class PostsService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = 'http://localhost:5000/api';\n    }\n    // Get all posts (Instagram feed)\n    getPosts(page = 1, limit = 10) {\n      return this.http.get(`${this.API_URL}/posts?page=${page}&limit=${limit}`).pipe(map(response => {\n        // Transform posts to match Instagram format\n        if (response.success && response.posts) {\n          response.posts = response.posts.map(post => this.transformPost(post));\n        }\n        return response;\n      }), catchError(error => {\n        console.error('Error fetching posts:', error);\n        return of({\n          success: false,\n          posts: [],\n          pagination: {\n            current: 1,\n            pages: 0,\n            total: 0\n          }\n        });\n      }));\n    }\n    // Get single post by ID\n    getPost(id) {\n      return this.http.get(`${this.API_URL}/posts/${id}`).pipe(map(response => {\n        if (response.success && response.post) {\n          response.post = this.transformPost(response.post);\n        }\n        return response;\n      }), catchError(error => {\n        console.error('Error fetching post:', error);\n        return of({\n          success: false,\n          post: null\n        });\n      }));\n    }\n    // Create new post\n    createPost(postData) {\n      return this.http.post(`${this.API_URL}/posts`, postData).pipe(map(response => {\n        if (response.post) {\n          response.post = this.transformPost(response.post);\n        }\n        return {\n          success: true,\n          post: response.post,\n          message: response.message\n        };\n      }), catchError(error => {\n        console.error('Error creating post:', error);\n        return of({\n          success: false,\n          message: 'Failed to create post'\n        });\n      }));\n    }\n    // Like/unlike post\n    toggleLike(postId) {\n      return this.http.post(`${this.API_URL}/posts/${postId}/like`, {}).pipe(map(response => ({\n        success: true,\n        message: response.message,\n        likesCount: response.likesCount\n      })), catchError(error => {\n        console.error('Error toggling like:', error);\n        return of({\n          success: false,\n          message: 'Failed to toggle like'\n        });\n      }));\n    }\n    // Add comment to post\n    addComment(postId, text) {\n      return this.http.post(`${this.API_URL}/posts/${postId}/comment`, {\n        text\n      }).pipe(map(response => ({\n        success: true,\n        comment: response.comment,\n        message: response.message\n      })), catchError(error => {\n        console.error('Error adding comment:', error);\n        return of({\n          success: false,\n          message: 'Failed to add comment'\n        });\n      }));\n    }\n    // Transform backend post to Instagram format\n    transformPost(post) {\n      return {\n        _id: post._id,\n        user: {\n          _id: post.user._id,\n          username: post.user.username,\n          fullName: post.user.fullName,\n          avatar: post.user.avatar || '/assets/images/default-avatar.jpg',\n          socialStats: post.user.socialStats\n        },\n        caption: post.caption || '',\n        media: post.media || [{\n          type: post.mediaType || 'image',\n          url: post.mediaUrl\n        }],\n        mediaUrl: post.mediaUrl || (post.media && post.media[0] ? post.media[0].url : ''),\n        mediaType: post.mediaType || 'image',\n        location: post.location,\n        likes: post.likes || [],\n        comments: post.comments || [],\n        saves: post.saves || [],\n        products: this.transformProducts(post.products || []),\n        analytics: {\n          views: post.analytics?.views || 0,\n          likes: post.analytics?.likes || post.likes?.length || 0,\n          comments: post.analytics?.comments || post.comments?.length || 0,\n          shares: post.analytics?.shares || 0\n        },\n        isLiked: post.isLiked || false,\n        isSaved: post.isSaved || false,\n        createdAt: post.createdAt,\n        updatedAt: post.updatedAt\n      };\n    }\n    // Transform products for enhanced navigation\n    transformProducts(products) {\n      return products.map(productTag => ({\n        _id: productTag._id,\n        product: {\n          _id: productTag.product._id,\n          name: productTag.product.name,\n          price: productTag.product.price,\n          images: productTag.product.images || [],\n          brand: productTag.product.brand\n        },\n        position: productTag.position || {\n          x: 50,\n          y: 50\n        }\n      }));\n    }\n    // Get trending posts\n    getTrendingPosts(limit = 10) {\n      return this.http.get(`${this.API_URL}/posts?sortBy=likes&limit=${limit}`).pipe(map(response => {\n        if (response.success && response.posts) {\n          response.posts = response.posts.map(post => this.transformPost(post));\n        }\n        return {\n          success: response.success,\n          posts: response.posts || []\n        };\n      }), catchError(error => {\n        console.error('Error fetching trending posts:', error);\n        return of({\n          success: false,\n          posts: []\n        });\n      }));\n    }\n    // Get user posts\n    getUserPosts(userId, page = 1, limit = 10) {\n      return this.http.get(`${this.API_URL}/posts?userId=${userId}&page=${page}&limit=${limit}`).pipe(map(response => {\n        if (response.success && response.posts) {\n          response.posts = response.posts.map(post => this.transformPost(post));\n        }\n        return response;\n      }), catchError(error => {\n        console.error('Error fetching user posts:', error);\n        return of({\n          success: false,\n          posts: [],\n          pagination: {\n            current: 1,\n            pages: 0,\n            total: 0\n          }\n        });\n      }));\n    }\n    static {\n      this.ɵfac = function PostsService_Factory(t) {\n        return new (t || PostsService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PostsService,\n        factory: PostsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PostsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}