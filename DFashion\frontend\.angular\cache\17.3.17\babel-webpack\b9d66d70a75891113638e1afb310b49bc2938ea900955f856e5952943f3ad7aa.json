{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport { AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\n\n/** @docs-private */\nconst _c0 = [\"mat-sort-header\", \"\"];\nconst _c1 = [\"*\"];\nfunction MatSortHeader_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"@arrowPosition.start\", function MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_start_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = true);\n    })(\"@arrowPosition.done\", function MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_done_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = false);\n    });\n    i0.ɵɵelement(1, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4);\n    i0.ɵɵelement(3, \"div\", 5)(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@arrowOpacity\", ctx_r1._getArrowViewState())(\"@arrowPosition\", ctx_r1._getArrowViewState())(\"@allowChildren\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@indicator\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@leftPointer\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@rightPointer\", ctx_r1._getArrowDirectionState());\n  }\n}\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort {\n  /** The sort direction of the currently active MatSortable. */\n  get direction() {\n    return this._direction;\n  }\n  set direction(direction) {\n    if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortInvalidDirectionError(direction);\n    }\n    this._direction = direction;\n  }\n  constructor(_defaultOptions) {\n    this._defaultOptions = _defaultOptions;\n    this._initializedStream = new ReplaySubject(1);\n    /** Collection of all registered sortables that this directive manages. */\n    this.sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    this._stateChanges = new Subject();\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    this.start = 'asc';\n    this._direction = '';\n    /** Whether the sortable is disabled. */\n    this.disabled = false;\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    this.sortChange = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    this.initialized = this._initializedStream;\n  }\n  /**\n   * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n   * collection of MatSortables.\n   */\n  register(sortable) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!sortable.id) {\n        throw getSortHeaderMissingIdError();\n      }\n      if (this.sortables.has(sortable.id)) {\n        throw getSortDuplicateSortableIdError(sortable.id);\n      }\n    }\n    this.sortables.set(sortable.id, sortable);\n  }\n  /**\n   * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n   * collection of contained MatSortables.\n   */\n  deregister(sortable) {\n    this.sortables.delete(sortable.id);\n  }\n  /** Sets the active sort id and determines the new sort direction. */\n  sort(sortable) {\n    if (this.active != sortable.id) {\n      this.active = sortable.id;\n      this.direction = sortable.start ? sortable.start : this.start;\n    } else {\n      this.direction = this.getNextSortDirection(sortable);\n    }\n    this.sortChange.emit({\n      active: this.active,\n      direction: this.direction\n    });\n  }\n  /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n  getNextSortDirection(sortable) {\n    if (!sortable) {\n      return '';\n    }\n    // Get the sort direction cycle with the potential sortable overrides.\n    const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n    let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n    // Get and return the next direction in the cycle\n    let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n    if (nextDirectionIndex >= sortDirectionCycle.length) {\n      nextDirectionIndex = 0;\n    }\n    return sortDirectionCycle[nextDirectionIndex];\n  }\n  ngOnInit() {\n    this._initializedStream.next();\n  }\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._initializedStream.complete();\n  }\n  static {\n    this.ɵfac = function MatSort_Factory(t) {\n      return new (t || MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSort,\n      selectors: [[\"\", \"matSort\", \"\"]],\n      hostAttrs: [1, \"mat-sort\"],\n      inputs: {\n        active: [i0.ɵɵInputFlags.None, \"matSortActive\", \"active\"],\n        start: [i0.ɵɵInputFlags.None, \"matSortStart\", \"start\"],\n        direction: [i0.ɵɵInputFlags.None, \"matSortDirection\", \"direction\"],\n        disableClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matSortDisableClear\", \"disableClear\", booleanAttribute],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matSortDisabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        sortChange: \"matSortChange\"\n      },\n      exportAs: [\"matSort\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSort, [{\n    type: Directive,\n    args: [{\n      selector: '[matSort]',\n      exportAs: 'matSort',\n      host: {\n        'class': 'mat-sort'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SORT_DEFAULT_OPTIONS]\n    }]\n  }], {\n    active: [{\n      type: Input,\n      args: ['matSortActive']\n    }],\n    start: [{\n      type: Input,\n      args: ['matSortStart']\n    }],\n    direction: [{\n      type: Input,\n      args: ['matSortDirection']\n    }],\n    disableClear: [{\n      type: Input,\n      args: [{\n        alias: 'matSortDisableClear',\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'matSortDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    sortChange: [{\n      type: Output,\n      args: ['matSortChange']\n    }]\n  });\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n  /** Animation that moves the sort indicator. */\n  indicator: trigger('indicator', [state('active-asc, asc', style({\n    transform: 'translateY(0px)'\n  })),\n  // 10px is the height of the sort indicator, minus the width of the pointers\n  state('active-desc, desc', style({\n    transform: 'translateY(10px)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: trigger('leftPointer', [state('active-asc, asc', style({\n    transform: 'rotate(-45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: trigger('rightPointer', [state('active-asc, asc', style({\n    transform: 'rotate(45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(-45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: trigger('arrowOpacity', [state('desc-to-active, asc-to-active, active', style({\n    opacity: 1\n  })), state('desc-to-hint, asc-to-hint, hint', style({\n    opacity: 0.54\n  })), state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', style({\n    opacity: 0\n  })),\n  // Transition between all states except for immediate transitions\n  transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')), transition('* <=> *', animate(SORT_ANIMATION_TRANSITION))]),\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: trigger('arrowPosition', [\n  // Hidden Above => Hint Center\n  transition('* => desc-to-hint, * => desc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(-25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Below\n  transition('* => hint-to-desc, * => active-to-desc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(25%)'\n  })]))),\n  // Hidden Below => Hint Center\n  transition('* => asc-to-hint, * => asc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Above\n  transition('* => hint-to-asc, * => active-to-asc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(-25%)'\n  })]))), state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', style({\n    transform: 'translateY(0)'\n  })), state('hint-to-desc, active-to-desc, desc', style({\n    transform: 'translateY(-25%)'\n  })), state('hint-to-asc, active-to-asc, asc', style({\n    transform: 'translateY(25%)'\n  }))]),\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: trigger('allowChildren', [transition('* <=> *', [query('@*', animateChild(), {\n    optional: true\n  })])])\n};\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n  constructor() {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    this.changes = new Subject();\n  }\n  static {\n    this.ɵfac = function MatSortHeaderIntl_Factory(t) {\n      return new (t || MatSortHeaderIntl)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSortHeaderIntl,\n      factory: MatSortHeaderIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeaderIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader {\n  /**\n   * Description applied to MatSortHeader's button element with aria-describedby. This text should\n   * describe the action that will occur when the user clicks the sort header.\n   */\n  get sortActionDescription() {\n    return this._sortActionDescription;\n  }\n  set sortActionDescription(value) {\n    this._updateSortActionDescription(value);\n  }\n  constructor(\n  /**\n   * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n   * @breaking-change 13.0.0\n   */\n  _intl, _changeDetectorRef,\n  // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n  // tslint:disable-next-line: lightweight-tokens\n  _sort, _columnDef, _focusMonitor, _elementRef, /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n  _ariaDescriber, defaultOptions) {\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._sort = _sort;\n    this._columnDef = _columnDef;\n    this._focusMonitor = _focusMonitor;\n    this._elementRef = _elementRef;\n    this._ariaDescriber = _ariaDescriber;\n    /**\n     * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n     * provide an affordance that the header is sortable by showing on focus and hover.\n     */\n    this._showIndicatorHint = false;\n    /**\n     * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n     * position through the animation. If animations are currently disabled, the fromState is removed\n     * so that there is no animation displayed.\n     */\n    this._viewState = {};\n    /** The direction the arrow should be facing according to the current state. */\n    this._arrowDirection = '';\n    /**\n     * Whether the view state animation should show the transition between the `from` and `to` states.\n     */\n    this._disableViewStateAnimation = false;\n    /** Sets the position of the arrow that displays when sorted. */\n    this.arrowPosition = 'after';\n    /** whether the sort header is disabled. */\n    this.disabled = false;\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    this._sortActionDescription = 'Sort';\n    // Note that we use a string token for the `_columnDef`, because the value is provided both by\n    // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n    // and we want to avoid having the sort header depending on the CDK table because\n    // of this single reference.\n    if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortHeaderNotContainedWithinSortError();\n    }\n    if (defaultOptions?.arrowPosition) {\n      this.arrowPosition = defaultOptions?.arrowPosition;\n    }\n    this._handleStateChanges();\n  }\n  ngOnInit() {\n    if (!this.id && this._columnDef) {\n      this.id = this._columnDef.name;\n    }\n    // Initialize the direction of the arrow and set the view state to be immediately that state.\n    this._updateArrowDirection();\n    this._setAnimationTransitionState({\n      toState: this._isSorted() ? 'active' : this._arrowDirection\n    });\n    this._sort.register(this);\n    this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n    this._updateSortActionDescription(this._sortActionDescription);\n  }\n  ngAfterViewInit() {\n    // We use the focus monitor because we also want to style\n    // things differently based on the focus origin.\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const newState = !!origin;\n      if (newState !== this._showIndicatorHint) {\n        this._setIndicatorHintVisible(newState);\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._sort.deregister(this);\n    this._rerenderSubscription.unsubscribe();\n    if (this._sortButton) {\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n    }\n  }\n  /**\n   * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n   * user showing what the active sort will become. If set to false, the arrow will fade away.\n   */\n  _setIndicatorHintVisible(visible) {\n    // No-op if the sort header is disabled - should not make the hint visible.\n    if (this._isDisabled() && visible) {\n      return;\n    }\n    this._showIndicatorHint = visible;\n    if (!this._isSorted()) {\n      this._updateArrowDirection();\n      if (this._showIndicatorHint) {\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'hint'\n        });\n      } else {\n        this._setAnimationTransitionState({\n          fromState: 'hint',\n          toState: this._arrowDirection\n        });\n      }\n    }\n  }\n  /**\n   * Sets the animation transition view state for the arrow's position and opacity. If the\n   * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n   * no animation appears.\n   */\n  _setAnimationTransitionState(viewState) {\n    this._viewState = viewState || {};\n    // If the animation for arrow position state (opacity/translation) should be disabled,\n    // remove the fromState so that it jumps right to the toState.\n    if (this._disableViewStateAnimation) {\n      this._viewState = {\n        toState: viewState.toState\n      };\n    }\n  }\n  /** Triggers the sort on this sort header and removes the indicator hint. */\n  _toggleOnInteraction() {\n    this._sort.sort(this);\n    // Do not show the animation if the header was already shown in the right position.\n    if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n      this._disableViewStateAnimation = true;\n    }\n  }\n  _handleClick() {\n    if (!this._isDisabled()) {\n      this._sort.sort(this);\n    }\n  }\n  _handleKeydown(event) {\n    if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n      event.preventDefault();\n      this._toggleOnInteraction();\n    }\n  }\n  /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n  _isSorted() {\n    return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n  }\n  /** Returns the animation state for the arrow direction (indicator and pointers). */\n  _getArrowDirectionState() {\n    return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n  }\n  /** Returns the arrow position state (opacity, translation). */\n  _getArrowViewState() {\n    const fromState = this._viewState.fromState;\n    return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n  }\n  /**\n   * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n   * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n   * active sorted direction. The reason this is updated through a function is because the direction\n   * should only be changed at specific times - when deactivated but the hint is displayed and when\n   * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n   * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n   * preserving its direction, even though the next sort direction is actually different and should\n   * only be changed once the arrow displays again (hint or activation).\n   */\n  _updateArrowDirection() {\n    this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n  }\n  _isDisabled() {\n    return this._sort.disabled || this.disabled;\n  }\n  /**\n   * Gets the aria-sort attribute that should be applied to this sort header. If this header\n   * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n   * says that the aria-sort property should only be present on one header at a time, so removing\n   * ensures this is true.\n   */\n  _getAriaSortAttribute() {\n    if (!this._isSorted()) {\n      return 'none';\n    }\n    return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n  }\n  /** Whether the arrow inside the sort header should be rendered. */\n  _renderArrow() {\n    return !this._isDisabled() || this._isSorted();\n  }\n  _updateSortActionDescription(newDescription) {\n    // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n    // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n    // for every *cell* in the table, creating a lot of unnecessary noise.\n    // If _sortButton is undefined, the component hasn't been initialized yet so there's\n    // nothing to update in the DOM.\n    if (this._sortButton) {\n      // removeDescription will no-op if there is no existing message.\n      // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      this._ariaDescriber?.describe(this._sortButton, newDescription);\n    }\n    this._sortActionDescription = newDescription;\n  }\n  /** Handles changes in the sorting state. */\n  _handleStateChanges() {\n    this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n      if (this._isSorted()) {\n        this._updateArrowDirection();\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n          this._disableViewStateAnimation = true;\n        }\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'active'\n        });\n        this._showIndicatorHint = false;\n      }\n      // If this header was recently active and now no longer sorted, animate away the arrow.\n      if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n        this._disableViewStateAnimation = false;\n        this._setAnimationTransitionState({\n          fromState: 'active',\n          toState: this._arrowDirection\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  static {\n    this.ɵfac = function MatSortHeader_Factory(t) {\n      return new (t || MatSortHeader)(i0.ɵɵdirectiveInject(MatSortHeaderIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MatSort, 8), i0.ɵɵdirectiveInject('MAT_SORT_HEADER_COLUMN_DEF', 8), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.AriaDescriber, 8), i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSortHeader,\n      selectors: [[\"\", \"mat-sort-header\", \"\"]],\n      hostAttrs: [1, \"mat-sort-header\"],\n      hostVars: 3,\n      hostBindings: function MatSortHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatSortHeader_click_HostBindingHandler() {\n            return ctx._handleClick();\n          })(\"keydown\", function MatSortHeader_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"mouseenter\", function MatSortHeader_mouseenter_HostBindingHandler() {\n            return ctx._setIndicatorHintVisible(true);\n          })(\"mouseleave\", function MatSortHeader_mouseleave_HostBindingHandler() {\n            return ctx._setIndicatorHintVisible(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n          i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n        }\n      },\n      inputs: {\n        id: [i0.ɵɵInputFlags.None, \"mat-sort-header\", \"id\"],\n        arrowPosition: \"arrowPosition\",\n        start: \"start\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        sortActionDescription: \"sortActionDescription\",\n        disableClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableClear\", \"disableClear\", booleanAttribute]\n      },\n      exportAs: [\"matSortHeader\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 7,\n      consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [1, \"mat-sort-header-arrow\"], [1, \"mat-sort-header-stem\"], [1, \"mat-sort-header-indicator\"], [1, \"mat-sort-header-pointer-left\"], [1, \"mat-sort-header-pointer-right\"], [1, \"mat-sort-header-pointer-middle\"]],\n      template: function MatSortHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, MatSortHeader_Conditional_3_Template, 6, 6, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\");\n          i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(3, ctx._renderArrow() ? 3 : -1);\n        }\n      },\n      styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;color:var(--mat-sort-arrow-color);opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeader, [{\n    type: Component,\n    args: [{\n      selector: '[mat-sort-header]',\n      exportAs: 'matSortHeader',\n      host: {\n        'class': 'mat-sort-header',\n        '(click)': '_handleClick()',\n        '(keydown)': '_handleKeydown($event)',\n        '(mouseenter)': '_setIndicatorHintVisible(true)',\n        '(mouseleave)': '_setIndicatorHintVisible(false)',\n        '[attr.aria-sort]': '_getAriaSortAttribute()',\n        '[class.mat-sort-header-disabled]': '_isDisabled()'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren],\n      standalone: true,\n      template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\"\\n        [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n        [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n        [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n        (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n        (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n      <div class=\\\"mat-sort-header-stem\\\"></div>\\n      <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n        <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n      </div>\\n    </div>\\n  }\\n</div>\\n\",\n      styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;color:var(--mat-sort-arrow-color);opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"]\n    }]\n  }], () => [{\n    type: MatSortHeaderIntl\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: MatSort,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: ['MAT_SORT_HEADER_COLUMN_DEF']\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i3.FocusMonitor\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i3.AriaDescriber,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SORT_DEFAULT_OPTIONS]\n    }]\n  }], {\n    id: [{\n      type: Input,\n      args: ['mat-sort-header']\n    }],\n    arrowPosition: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    sortActionDescription: [{\n      type: Input\n    }],\n    disableClear: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatSortModule {\n  static {\n    this.ɵfac = function MatSortModule_Factory(t) {\n      return new (t || MatSortModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSortModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n      imports: [MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatSort, MatSortHeader],\n      exports: [MatSort, MatSortHeader],\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "booleanAttribute", "Directive", "Optional", "Inject", "Input", "Output", "Injectable", "SkipSelf", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgModule", "i3", "SPACE", "ENTER", "ReplaySubject", "Subject", "merge", "trigger", "state", "style", "transition", "animate", "keyframes", "query", "animate<PERSON><PERSON><PERSON>", "AnimationDurations", "AnimationCurves", "MatCommonModule", "_c0", "_c1", "MatSortHeader_Conditional_3_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_start_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_disableViewStateAnimation", "MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_done_0_listener", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "_getArrowViewState", "_getArrowDirectionState", "ɵɵadvance", "getSortDuplicateSortableIdError", "id", "Error", "getSortHeaderNotContainedWithinSortError", "getSortHeaderMissingIdError", "getSortInvalidDirectionError", "direction", "MAT_SORT_DEFAULT_OPTIONS", "MatSort", "_direction", "ngDevMode", "constructor", "_defaultOptions", "_initializedStream", "sortables", "Map", "_stateChanges", "start", "disabled", "sortChange", "initialized", "register", "sortable", "has", "set", "deregister", "delete", "sort", "active", "getNextSortDirection", "emit", "disableClear", "sortDirectionCycle", "getSortDirectionCycle", "nextDirectionIndex", "indexOf", "length", "ngOnInit", "next", "ngOnChanges", "ngOnDestroy", "complete", "ɵfac", "MatSort_Factory", "t", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "ɵɵInputFlags", "None", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "host", "undefined", "decorators", "alias", "transform", "sortOrder", "reverse", "push", "SORT_ANIMATION_TRANSITION", "ENTERING", "STANDARD_CURVE", "matSortAnimations", "indicator", "leftPointer", "rightPointer", "arrowOpacity", "opacity", "arrowPosition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional", "MatSortHeaderIntl", "changes", "MatSortHeaderIntl_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "MAT_SORT_HEADER_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_SORT_HEADER_INTL_PROVIDER", "provide", "deps", "useFactory", "Mat<PERSON>ort<PERSON><PERSON>er", "sortActionDescription", "_sortActionDescription", "value", "_updateSortActionDescription", "_intl", "_changeDetectorRef", "_sort", "_columnDef", "_focusMonitor", "_elementRef", "_ariaDescriber", "defaultOptions", "_showIndicatorHint", "_viewState", "_arrowDirection", "_handleStateChanges", "name", "_updateArrowDirection", "_setAnimationTransitionState", "toState", "_isSorted", "_sortButton", "nativeElement", "querySelector", "ngAfterViewInit", "monitor", "subscribe", "origin", "newState", "_setIndicatorHintVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stopMonitoring", "_rerenderSubscription", "unsubscribe", "removeDescription", "visible", "_isDisabled", "fromState", "viewState", "_toggleOnInteraction", "_handleClick", "_handleKeydown", "event", "keyCode", "preventDefault", "_getAriaSortAttribute", "_renderArrow", "newDescription", "describe", "MatSortHeader_Factory", "ChangeDetectorRef", "FocusMonitor", "ElementRef", "AriaDescriber", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "MatSortHeader_HostBindings", "MatSortHeader_click_HostBindingHandler", "MatSortHeader_keydown_HostBindingHandler", "$event", "MatSortHeader_mouseenter_HostBindingHandler", "MatSortHeader_mouseleave_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatSortHeader_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵtemplate", "ɵɵconditional", "styles", "encapsulation", "data", "animation", "changeDetection", "OnPush", "animations", "MatSortModule", "MatSortModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports"], "sources": ["E:/Fahion/DFashion/DFashion/frontend/node_modules/@angular/material/fesm2022/sort.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport { AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\n\n/** @docs-private */\nfunction getSortDuplicateSortableIdError(id) {\n    return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n    return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n    return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n    return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort {\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n        return this._direction;\n    }\n    set direction(direction) {\n        if (direction &&\n            direction !== 'asc' &&\n            direction !== 'desc' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortInvalidDirectionError(direction);\n        }\n        this._direction = direction;\n    }\n    constructor(_defaultOptions) {\n        this._defaultOptions = _defaultOptions;\n        this._initializedStream = new ReplaySubject(1);\n        /** Collection of all registered sortables that this directive manages. */\n        this.sortables = new Map();\n        /** Used to notify any child components listening to state changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The direction to set when an MatSortable is initially sorted.\n         * May be overridden by the MatSortable's sort start.\n         */\n        this.start = 'asc';\n        this._direction = '';\n        /** Whether the sortable is disabled. */\n        this.disabled = false;\n        /** Event emitted when the user changes either the active sort or sort direction. */\n        this.sortChange = new EventEmitter();\n        /** Emits when the paginator is initialized. */\n        this.initialized = this._initializedStream;\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!sortable.id) {\n                throw getSortHeaderMissingIdError();\n            }\n            if (this.sortables.has(sortable.id)) {\n                throw getSortDuplicateSortableIdError(sortable.id);\n            }\n        }\n        this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n        this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n        if (this.active != sortable.id) {\n            this.active = sortable.id;\n            this.direction = sortable.start ? sortable.start : this.start;\n        }\n        else {\n            this.direction = this.getNextSortDirection(sortable);\n        }\n        this.sortChange.emit({ active: this.active, direction: this.direction });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n        if (!sortable) {\n            return '';\n        }\n        // Get the sort direction cycle with the potential sortable overrides.\n        const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n        let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n        // Get and return the next direction in the cycle\n        let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n        if (nextDirectionIndex >= sortDirectionCycle.length) {\n            nextDirectionIndex = 0;\n        }\n        return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n        this._initializedStream.next();\n    }\n    ngOnChanges() {\n        this._stateChanges.next();\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n        this._initializedStream.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSort, deps: [{ token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatSort, isStandalone: true, selector: \"[matSort]\", inputs: { active: [\"matSortActive\", \"active\"], start: [\"matSortStart\", \"start\"], direction: [\"matSortDirection\", \"direction\"], disableClear: [\"matSortDisableClear\", \"disableClear\", booleanAttribute], disabled: [\"matSortDisabled\", \"disabled\", booleanAttribute] }, outputs: { sortChange: \"matSortChange\" }, host: { classAttribute: \"mat-sort\" }, exportAs: [\"matSort\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSort, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSort]',\n                    exportAs: 'matSort',\n                    host: {\n                        'class': 'mat-sort',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }], propDecorators: { active: [{\n                type: Input,\n                args: ['matSortActive']\n            }], start: [{\n                type: Input,\n                args: ['matSortStart']\n            }], direction: [{\n                type: Input,\n                args: ['matSortDirection']\n            }], disableClear: [{\n                type: Input,\n                args: [{ alias: 'matSortDisableClear', transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'matSortDisabled', transform: booleanAttribute }]\n            }], sortChange: [{\n                type: Output,\n                args: ['matSortChange']\n            }] } });\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n    let sortOrder = ['asc', 'desc'];\n    if (start == 'desc') {\n        sortOrder.reverse();\n    }\n    if (!disableClear) {\n        sortOrder.push('');\n    }\n    return sortOrder;\n}\n\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n    /** Animation that moves the sort indicator. */\n    indicator: trigger('indicator', [\n        state('active-asc, asc', style({ transform: 'translateY(0px)' })),\n        // 10px is the height of the sort indicator, minus the width of the pointers\n        state('active-desc, desc', style({ transform: 'translateY(10px)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n    leftPointer: trigger('leftPointer', [\n        state('active-asc, asc', style({ transform: 'rotate(-45deg)' })),\n        state('active-desc, desc', style({ transform: 'rotate(45deg)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n    rightPointer: trigger('rightPointer', [\n        state('active-asc, asc', style({ transform: 'rotate(45deg)' })),\n        state('active-desc, desc', style({ transform: 'rotate(-45deg)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that controls the arrow opacity. */\n    arrowOpacity: trigger('arrowOpacity', [\n        state('desc-to-active, asc-to-active, active', style({ opacity: 1 })),\n        state('desc-to-hint, asc-to-hint, hint', style({ opacity: 0.54 })),\n        state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', style({ opacity: 0 })),\n        // Transition between all states except for immediate transitions\n        transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n        transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /**\n     * Animation for the translation of the arrow as a whole. States are separated into two\n     * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n     * peek, and active. The other states define a specific animation (source-to-destination)\n     * and are determined as a function of their prev user-perceived state and what the next state\n     * should be.\n     */\n    arrowPosition: trigger('arrowPosition', [\n        // Hidden Above => Hint Center\n        transition('* => desc-to-hint, * => desc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(-25%)' }), style({ transform: 'translateY(0)' })]))),\n        // Hint Center => Hidden Below\n        transition('* => hint-to-desc, * => active-to-desc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(0)' }), style({ transform: 'translateY(25%)' })]))),\n        // Hidden Below => Hint Center\n        transition('* => asc-to-hint, * => asc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(25%)' }), style({ transform: 'translateY(0)' })]))),\n        // Hint Center => Hidden Above\n        transition('* => hint-to-asc, * => active-to-asc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(0)' }), style({ transform: 'translateY(-25%)' })]))),\n        state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', style({ transform: 'translateY(0)' })),\n        state('hint-to-desc, active-to-desc, desc', style({ transform: 'translateY(-25%)' })),\n        state('hint-to-asc, active-to-asc, asc', style({ transform: 'translateY(25%)' })),\n    ]),\n    /** Necessary trigger that calls animate on children animations. */\n    allowChildren: trigger('allowChildren', [\n        transition('* <=> *', [query('@*', animateChild(), { optional: true })]),\n    ]),\n};\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n    constructor() {\n        /**\n         * Stream that emits whenever the labels here are changed. Use this to notify\n         * components if the labels have changed after initialization.\n         */\n        this.changes = new Subject();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortHeaderIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortHeaderIntl, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortHeaderIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n    // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n    provide: MatSortHeaderIntl,\n    deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n    useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY,\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader {\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n        return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n        this._updateSortActionDescription(value);\n    }\n    constructor(\n    /**\n     * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n     * @breaking-change 13.0.0\n     */\n    _intl, _changeDetectorRef, \n    // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n    // tslint:disable-next-line: lightweight-tokens\n    _sort, _columnDef, _focusMonitor, _elementRef, \n    /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n    _ariaDescriber, defaultOptions) {\n        this._intl = _intl;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._sort = _sort;\n        this._columnDef = _columnDef;\n        this._focusMonitor = _focusMonitor;\n        this._elementRef = _elementRef;\n        this._ariaDescriber = _ariaDescriber;\n        /**\n         * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n         * provide an affordance that the header is sortable by showing on focus and hover.\n         */\n        this._showIndicatorHint = false;\n        /**\n         * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n         * position through the animation. If animations are currently disabled, the fromState is removed\n         * so that there is no animation displayed.\n         */\n        this._viewState = {};\n        /** The direction the arrow should be facing according to the current state. */\n        this._arrowDirection = '';\n        /**\n         * Whether the view state animation should show the transition between the `from` and `to` states.\n         */\n        this._disableViewStateAnimation = false;\n        /** Sets the position of the arrow that displays when sorted. */\n        this.arrowPosition = 'after';\n        /** whether the sort header is disabled. */\n        this.disabled = false;\n        // Default the action description to \"Sort\" because it's better than nothing.\n        // Without a description, the button's label comes from the sort header text content,\n        // which doesn't give any indication that it performs a sorting operation.\n        this._sortActionDescription = 'Sort';\n        // Note that we use a string token for the `_columnDef`, because the value is provided both by\n        // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n        // and we want to avoid having the sort header depending on the CDK table because\n        // of this single reference.\n        if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortHeaderNotContainedWithinSortError();\n        }\n        if (defaultOptions?.arrowPosition) {\n            this.arrowPosition = defaultOptions?.arrowPosition;\n        }\n        this._handleStateChanges();\n    }\n    ngOnInit() {\n        if (!this.id && this._columnDef) {\n            this.id = this._columnDef.name;\n        }\n        // Initialize the direction of the arrow and set the view state to be immediately that state.\n        this._updateArrowDirection();\n        this._setAnimationTransitionState({\n            toState: this._isSorted() ? 'active' : this._arrowDirection,\n        });\n        this._sort.register(this);\n        this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n        this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n        // We use the focus monitor because we also want to style\n        // things differently based on the focus origin.\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n            const newState = !!origin;\n            if (newState !== this._showIndicatorHint) {\n                this._setIndicatorHintVisible(newState);\n                this._changeDetectorRef.markForCheck();\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._sort.deregister(this);\n        this._rerenderSubscription.unsubscribe();\n        if (this._sortButton) {\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n        }\n    }\n    /**\n     * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n     * user showing what the active sort will become. If set to false, the arrow will fade away.\n     */\n    _setIndicatorHintVisible(visible) {\n        // No-op if the sort header is disabled - should not make the hint visible.\n        if (this._isDisabled() && visible) {\n            return;\n        }\n        this._showIndicatorHint = visible;\n        if (!this._isSorted()) {\n            this._updateArrowDirection();\n            if (this._showIndicatorHint) {\n                this._setAnimationTransitionState({ fromState: this._arrowDirection, toState: 'hint' });\n            }\n            else {\n                this._setAnimationTransitionState({ fromState: 'hint', toState: this._arrowDirection });\n            }\n        }\n    }\n    /**\n     * Sets the animation transition view state for the arrow's position and opacity. If the\n     * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n     * no animation appears.\n     */\n    _setAnimationTransitionState(viewState) {\n        this._viewState = viewState || {};\n        // If the animation for arrow position state (opacity/translation) should be disabled,\n        // remove the fromState so that it jumps right to the toState.\n        if (this._disableViewStateAnimation) {\n            this._viewState = { toState: viewState.toState };\n        }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n        this._sort.sort(this);\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n            this._disableViewStateAnimation = true;\n        }\n    }\n    _handleClick() {\n        if (!this._isDisabled()) {\n            this._sort.sort(this);\n        }\n    }\n    _handleKeydown(event) {\n        if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n            event.preventDefault();\n            this._toggleOnInteraction();\n        }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n        return (this._sort.active == this.id &&\n            (this._sort.direction === 'asc' || this._sort.direction === 'desc'));\n    }\n    /** Returns the animation state for the arrow direction (indicator and pointers). */\n    _getArrowDirectionState() {\n        return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n    }\n    /** Returns the arrow position state (opacity, translation). */\n    _getArrowViewState() {\n        const fromState = this._viewState.fromState;\n        return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n    }\n    /**\n     * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n     * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n     * active sorted direction. The reason this is updated through a function is because the direction\n     * should only be changed at specific times - when deactivated but the hint is displayed and when\n     * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n     * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n     * preserving its direction, even though the next sort direction is actually different and should\n     * only be changed once the arrow displays again (hint or activation).\n     */\n    _updateArrowDirection() {\n        this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n    }\n    _isDisabled() {\n        return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n        if (!this._isSorted()) {\n            return 'none';\n        }\n        return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n        return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n        // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n        // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n        // for every *cell* in the table, creating a lot of unnecessary noise.\n        // If _sortButton is undefined, the component hasn't been initialized yet so there's\n        // nothing to update in the DOM.\n        if (this._sortButton) {\n            // removeDescription will no-op if there is no existing message.\n            // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n            this._ariaDescriber?.describe(this._sortButton, newDescription);\n        }\n        this._sortActionDescription = newDescription;\n    }\n    /** Handles changes in the sorting state. */\n    _handleStateChanges() {\n        this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n            if (this._isSorted()) {\n                this._updateArrowDirection();\n                // Do not show the animation if the header was already shown in the right position.\n                if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n                    this._disableViewStateAnimation = true;\n                }\n                this._setAnimationTransitionState({ fromState: this._arrowDirection, toState: 'active' });\n                this._showIndicatorHint = false;\n            }\n            // If this header was recently active and now no longer sorted, animate away the arrow.\n            if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n                this._disableViewStateAnimation = false;\n                this._setAnimationTransitionState({ fromState: 'active', toState: this._arrowDirection });\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortHeader, deps: [{ token: MatSortHeaderIntl }, { token: i0.ChangeDetectorRef }, { token: MatSort, optional: true }, { token: 'MAT_SORT_HEADER_COLUMN_DEF', optional: true }, { token: i3.FocusMonitor }, { token: i0.ElementRef }, { token: i3.AriaDescriber, optional: true }, { token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatSortHeader, isStandalone: true, selector: \"[mat-sort-header]\", inputs: { id: [\"mat-sort-header\", \"id\"], arrowPosition: \"arrowPosition\", start: \"start\", disabled: [\"disabled\", \"disabled\", booleanAttribute], sortActionDescription: \"sortActionDescription\", disableClear: [\"disableClear\", \"disableClear\", booleanAttribute] }, host: { listeners: { \"click\": \"_handleClick()\", \"keydown\": \"_handleKeydown($event)\", \"mouseenter\": \"_setIndicatorHintVisible(true)\", \"mouseleave\": \"_setIndicatorHintVisible(false)\" }, properties: { \"attr.aria-sort\": \"_getAriaSortAttribute()\", \"class.mat-sort-header-disabled\": \"_isDisabled()\" }, classAttribute: \"mat-sort-header\" }, exportAs: [\"matSortHeader\"], ngImport: i0, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\"\\n        [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n        [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n        [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n        (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n        (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n      <div class=\\\"mat-sort-header-stem\\\"></div>\\n      <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n        <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n      </div>\\n    </div>\\n  }\\n</div>\\n\", styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;color:var(--mat-sort-arrow-color);opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"], animations: [\n            matSortAnimations.indicator,\n            matSortAnimations.leftPointer,\n            matSortAnimations.rightPointer,\n            matSortAnimations.arrowOpacity,\n            matSortAnimations.arrowPosition,\n            matSortAnimations.allowChildren,\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortHeader, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-sort-header]', exportAs: 'matSortHeader', host: {\n                        'class': 'mat-sort-header',\n                        '(click)': '_handleClick()',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(mouseenter)': '_setIndicatorHintVisible(true)',\n                        '(mouseleave)': '_setIndicatorHintVisible(false)',\n                        '[attr.aria-sort]': '_getAriaSortAttribute()',\n                        '[class.mat-sort-header-disabled]': '_isDisabled()',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, animations: [\n                        matSortAnimations.indicator,\n                        matSortAnimations.leftPointer,\n                        matSortAnimations.rightPointer,\n                        matSortAnimations.arrowOpacity,\n                        matSortAnimations.arrowPosition,\n                        matSortAnimations.allowChildren,\n                    ], standalone: true, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\"\\n        [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n        [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n        [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n        (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n        (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n      <div class=\\\"mat-sort-header-stem\\\"></div>\\n      <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n        <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n      </div>\\n    </div>\\n  }\\n</div>\\n\", styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;color:var(--mat-sort-arrow-color);opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"] }]\n        }], ctorParameters: () => [{ type: MatSortHeaderIntl }, { type: i0.ChangeDetectorRef }, { type: MatSort, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: ['MAT_SORT_HEADER_COLUMN_DEF']\n                }, {\n                    type: Optional\n                }] }, { type: i3.FocusMonitor }, { type: i0.ElementRef }, { type: i3.AriaDescriber, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }], propDecorators: { id: [{\n                type: Input,\n                args: ['mat-sort-header']\n            }], arrowPosition: [{\n                type: Input\n            }], start: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], sortActionDescription: [{\n                type: Input\n            }], disableClear: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nclass MatSortModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortModule, imports: [MatCommonModule, MatSort, MatSortHeader], exports: [MatSort, MatSortHeader] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortModule, providers: [MAT_SORT_HEADER_INTL_PROVIDER], imports: [MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSortModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatSort, MatSortHeader],\n                    exports: [MatSort, MatSortHeader],\n                    providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,QAAQ,eAAe;AACjN,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpD,SAASC,aAAa,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACpD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,QAAQ,qBAAqB;AAChH,SAASC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;;AAE7F;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAgHoGrC,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAAuC,cAAA,YA4XgiF,CAAC;IA5XniFvC,EAAE,CAAAwC,UAAA,kCAAAC,kFAAA;MAAFzC,EAAE,CAAA0C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CAAAF,MAAA,CAAAG,0BAAA,GA4Xo9E,IAAI;IAAA,CAAC,CAAC,iCAAAC,iFAAA;MA5X59E/C,EAAE,CAAA0C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CAAAF,MAAA,CAAAG,0BAAA,GA4XyhF,KAAK;IAAA,CAAC,CAAC;IA5XliF9C,EAAE,CAAAgD,SAAA,YA4XklF,CAAC;IA5XrlFhD,EAAE,CAAAuC,cAAA,YA4X8qF,CAAC;IA5XjrFvC,EAAE,CAAAgD,SAAA,YA4XuxF,CAAC,YAA0G,CAAC,YAA6D,CAAC;IA5Xn8FhD,EAAE,CAAAiD,YAAA,CA4X88F,CAAC,CAAW,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAQ,MAAA,GA5X79F3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAkD,UAAA,kBAAAP,MAAA,CAAAQ,kBAAA,EA4XwyE,CAAC,mBAAAR,MAAA,CAAAQ,kBAAA,EAAkD,CAAC,mBAAAR,MAAA,CAAAS,uBAAA,EAAuD,CAAC;IA5Xt5EpD,EAAE,CAAAqD,SAAA,EA4X6qF,CAAC;IA5XhrFrD,EAAE,CAAAkD,UAAA,eAAAP,MAAA,CAAAS,uBAAA,EA4X6qF,CAAC;IA5XhrFpD,EAAE,CAAAqD,SAAA,CA4XgxF,CAAC;IA5XnxFrD,EAAE,CAAAkD,UAAA,iBAAAP,MAAA,CAAAS,uBAAA,EA4XgxF,CAAC;IA5XnxFpD,EAAE,CAAAqD,SAAA,CA4X23F,CAAC;IA5X93FrD,EAAE,CAAAkD,UAAA,kBAAAP,MAAA,CAAAS,uBAAA,EA4X23F,CAAC;EAAA;AAAA;AA3el+F,SAASE,+BAA+BA,CAACC,EAAE,EAAE;EACzC,OAAOC,KAAK,CAAC,kDAAkDD,EAAE,IAAI,CAAC;AAC1E;AACA;AACA,SAASE,wCAAwCA,CAAA,EAAG;EAChD,OAAOD,KAAK,CAAC,kFAAkF,CAAC;AACpG;AACA;AACA,SAASE,2BAA2BA,CAAA,EAAG;EACnC,OAAOF,KAAK,CAAC,kDAAkD,CAAC;AACpE;AACA;AACA,SAASG,4BAA4BA,CAACC,SAAS,EAAE;EAC7C,OAAOJ,KAAK,CAAC,GAAGI,SAAS,mDAAmD,CAAC;AACjF;;AAEA;AACA,MAAMC,wBAAwB,GAAG,IAAI5D,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA,MAAM6D,OAAO,CAAC;EACV;EACA,IAAIF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACG,UAAU;EAC1B;EACA,IAAIH,SAASA,CAACA,SAAS,EAAE;IACrB,IAAIA,SAAS,IACTA,SAAS,KAAK,KAAK,IACnBA,SAAS,KAAK,MAAM,KACnB,OAAOI,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAML,4BAA4B,CAACC,SAAS,CAAC;IACjD;IACA,IAAI,CAACG,UAAU,GAAGH,SAAS;EAC/B;EACAK,WAAWA,CAACC,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,kBAAkB,GAAG,IAAIjD,aAAa,CAAC,CAAC,CAAC;IAC9C;IACA,IAAI,CAACkD,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B;IACA,IAAI,CAACC,aAAa,GAAG,IAAInD,OAAO,CAAC,CAAC;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACoD,KAAK,GAAG,KAAK;IAClB,IAAI,CAACR,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACS,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACC,UAAU,GAAG,IAAIvE,YAAY,CAAC,CAAC;IACpC;IACA,IAAI,CAACwE,WAAW,GAAG,IAAI,CAACP,kBAAkB;EAC9C;EACA;AACJ;AACA;AACA;EACIQ,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,OAAOZ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAACY,QAAQ,CAACrB,EAAE,EAAE;QACd,MAAMG,2BAA2B,CAAC,CAAC;MACvC;MACA,IAAI,IAAI,CAACU,SAAS,CAACS,GAAG,CAACD,QAAQ,CAACrB,EAAE,CAAC,EAAE;QACjC,MAAMD,+BAA+B,CAACsB,QAAQ,CAACrB,EAAE,CAAC;MACtD;IACJ;IACA,IAAI,CAACa,SAAS,CAACU,GAAG,CAACF,QAAQ,CAACrB,EAAE,EAAEqB,QAAQ,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIG,UAAUA,CAACH,QAAQ,EAAE;IACjB,IAAI,CAACR,SAAS,CAACY,MAAM,CAACJ,QAAQ,CAACrB,EAAE,CAAC;EACtC;EACA;EACA0B,IAAIA,CAACL,QAAQ,EAAE;IACX,IAAI,IAAI,CAACM,MAAM,IAAIN,QAAQ,CAACrB,EAAE,EAAE;MAC5B,IAAI,CAAC2B,MAAM,GAAGN,QAAQ,CAACrB,EAAE;MACzB,IAAI,CAACK,SAAS,GAAGgB,QAAQ,CAACL,KAAK,GAAGK,QAAQ,CAACL,KAAK,GAAG,IAAI,CAACA,KAAK;IACjE,CAAC,MACI;MACD,IAAI,CAACX,SAAS,GAAG,IAAI,CAACuB,oBAAoB,CAACP,QAAQ,CAAC;IACxD;IACA,IAAI,CAACH,UAAU,CAACW,IAAI,CAAC;MAAEF,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEtB,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,CAAC;EAC5E;EACA;EACAuB,oBAAoBA,CAACP,QAAQ,EAAE;IAC3B,IAAI,CAACA,QAAQ,EAAE;MACX,OAAO,EAAE;IACb;IACA;IACA,MAAMS,YAAY,GAAGT,QAAQ,EAAES,YAAY,IAAI,IAAI,CAACA,YAAY,IAAI,CAAC,CAAC,IAAI,CAACnB,eAAe,EAAEmB,YAAY;IACxG,IAAIC,kBAAkB,GAAGC,qBAAqB,CAACX,QAAQ,CAACL,KAAK,IAAI,IAAI,CAACA,KAAK,EAAEc,YAAY,CAAC;IAC1F;IACA,IAAIG,kBAAkB,GAAGF,kBAAkB,CAACG,OAAO,CAAC,IAAI,CAAC7B,SAAS,CAAC,GAAG,CAAC;IACvE,IAAI4B,kBAAkB,IAAIF,kBAAkB,CAACI,MAAM,EAAE;MACjDF,kBAAkB,GAAG,CAAC;IAC1B;IACA,OAAOF,kBAAkB,CAACE,kBAAkB,CAAC;EACjD;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACxB,kBAAkB,CAACyB,IAAI,CAAC,CAAC;EAClC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvB,aAAa,CAACsB,IAAI,CAAC,CAAC;EAC7B;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxB,aAAa,CAACyB,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC5B,kBAAkB,CAAC4B,QAAQ,CAAC,CAAC;EACtC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFpC,OAAO,EAAjB9D,EAAE,CAAAmG,iBAAA,CAAiCtC,wBAAwB;IAAA,CAA4D;EAAE;EACzN;IAAS,IAAI,CAACuC,IAAI,kBAD8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EACJxC,OAAO;MAAAyC,SAAA;MAAAC,SAAA;MAAAC,MAAA;QAAAvB,MAAA,GADLlF,EAAE,CAAA0G,YAAA,CAAAC,IAAA;QAAApC,KAAA,GAAFvE,EAAE,CAAA0G,YAAA,CAAAC,IAAA;QAAA/C,SAAA,GAAF5D,EAAE,CAAA0G,YAAA,CAAAC,IAAA;QAAAtB,YAAA,GAAFrF,EAAE,CAAA0G,YAAA,CAAAE,0BAAA,yCACqOzG,gBAAgB;QAAAqE,QAAA,GADvPxE,EAAE,CAAA0G,YAAA,CAAAE,0BAAA,iCACkSzG,gBAAgB;MAAA;MAAA0G,OAAA;QAAApC,UAAA;MAAA;MAAAqC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADpThH,EAAE,CAAAiH,wBAAA,EAAFjH,EAAE,CAAAkH,oBAAA;IAAA,EACkc;EAAE;AAC1iB;AACA;EAAA,QAAAlD,SAAA,oBAAAA,SAAA,KAHoGhE,EAAE,CAAAmH,iBAAA,CAGXrD,OAAO,EAAc,CAAC;IACrGwC,IAAI,EAAElG,SAAS;IACfgH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBP,QAAQ,EAAE,SAAS;MACnBQ,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACDP,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAET,IAAI,EAAEiB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/ClB,IAAI,EAAEjG;IACV,CAAC,EAAE;MACCiG,IAAI,EAAEhG,MAAM;MACZ8G,IAAI,EAAE,CAACvD,wBAAwB;IACnC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEqB,MAAM,EAAE,CAAC;MAClCoB,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE7C,KAAK,EAAE,CAAC;MACR+B,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAExD,SAAS,EAAE,CAAC;MACZ0C,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE/B,YAAY,EAAE,CAAC;MACfiB,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC;QAAEK,KAAK,EAAE,qBAAqB;QAAEC,SAAS,EAAEvH;MAAiB,CAAC;IACxE,CAAC,CAAC;IAAEqE,QAAQ,EAAE,CAAC;MACX8B,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC;QAAEK,KAAK,EAAE,iBAAiB;QAAEC,SAAS,EAAEvH;MAAiB,CAAC;IACpE,CAAC,CAAC;IAAEsE,UAAU,EAAE,CAAC;MACb6B,IAAI,EAAE9F,MAAM;MACZ4G,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAAS7B,qBAAqBA,CAAChB,KAAK,EAAEc,YAAY,EAAE;EAChD,IAAIsC,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;EAC/B,IAAIpD,KAAK,IAAI,MAAM,EAAE;IACjBoD,SAAS,CAACC,OAAO,CAAC,CAAC;EACvB;EACA,IAAI,CAACvC,YAAY,EAAE;IACfsC,SAAS,CAACE,IAAI,CAAC,EAAE,CAAC;EACtB;EACA,OAAOF,SAAS;AACpB;AAEA,MAAMG,yBAAyB,GAAGjG,kBAAkB,CAACkG,QAAQ,GAAG,GAAG,GAAGjG,eAAe,CAACkG,cAAc;AACpG;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG;EACtB;EACAC,SAAS,EAAE7G,OAAO,CAAC,WAAW,EAAE,CAC5BC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC;EACjE;EACApG,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,EACpElG,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAACqG,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAK,WAAW,EAAE9G,OAAO,CAAC,aAAa,EAAE,CAChCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EAChEpG,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EACjElG,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAACqG,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAM,YAAY,EAAE/G,OAAO,CAAC,cAAc,EAAE,CAClCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EAC/DpG,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EAClElG,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAACqG,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAO,YAAY,EAAEhH,OAAO,CAAC,cAAc,EAAE,CAClCC,KAAK,CAAC,uCAAuC,EAAEC,KAAK,CAAC;IAAE+G,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EACrEhH,KAAK,CAAC,iCAAiC,EAAEC,KAAK,CAAC;IAAE+G,OAAO,EAAE;EAAK,CAAC,CAAC,CAAC,EAClEhH,KAAK,CAAC,2EAA2E,EAAEC,KAAK,CAAC;IAAE+G,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC;EACzG;EACA9G,UAAU,CAAC,wDAAwD,EAAEC,OAAO,CAAC,KAAK,CAAC,CAAC,EACpFD,UAAU,CAAC,SAAS,EAAEC,OAAO,CAACqG,yBAAyB,CAAC,CAAC,CAC5D,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;EACIS,aAAa,EAAElH,OAAO,CAAC,eAAe,EAAE;EACpC;EACAG,UAAU,CAAC,wCAAwC,EAAEC,OAAO,CAACqG,yBAAyB,EAAEpG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAmB,CAAC,CAAC,EAAEnG,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtL;EACAlG,UAAU,CAAC,wCAAwC,EAAEC,OAAO,CAACqG,yBAAyB,EAAEpG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAgB,CAAC,CAAC,EAAEnG,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrL;EACAlG,UAAU,CAAC,sCAAsC,EAAEC,OAAO,CAACqG,yBAAyB,EAAEpG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAkB,CAAC,CAAC,EAAEnG,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnL;EACAlG,UAAU,CAAC,sCAAsC,EAAEC,OAAO,CAACqG,yBAAyB,EAAEpG,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAgB,CAAC,CAAC,EAAEnG,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpLpG,KAAK,CAAC,wEAAwE,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EACtHpG,KAAK,CAAC,oCAAoC,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,EACrFpG,KAAK,CAAC,iCAAiC,EAAEC,KAAK,CAAC;IAAEmG,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC,CACpF,CAAC;EACF;EACAc,aAAa,EAAEnH,OAAO,CAAC,eAAe,EAAE,CACpCG,UAAU,CAAC,SAAS,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEC,YAAY,CAAC,CAAC,EAAE;IAAE6G,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC,CAAC,CAC3E;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBzE,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAAC0E,OAAO,GAAG,IAAIxH,OAAO,CAAC,CAAC;EAChC;EACA;IAAS,IAAI,CAAC6E,IAAI,YAAA4C,0BAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAwFwC,iBAAiB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACG,KAAK,kBA1H6E7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EA0HYL,iBAAiB;MAAAM,OAAA,EAAjBN,iBAAiB,CAAA1C,IAAA;MAAAiD,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAAjF,SAAA,oBAAAA,SAAA,KA5HoGhE,EAAE,CAAAmH,iBAAA,CA4HXuB,iBAAiB,EAAc,CAAC;IAC/GpC,IAAI,EAAE7F,UAAU;IAChB2G,IAAI,EAAE,CAAC;MAAE6B,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASC,qCAAqCA,CAACC,UAAU,EAAE;EACvD,OAAOA,UAAU,IAAI,IAAIT,iBAAiB,CAAC,CAAC;AAChD;AACA;AACA,MAAMU,6BAA6B,GAAG;EAClC;EACAC,OAAO,EAAEX,iBAAiB;EAC1BY,IAAI,EAAE,CAAC,CAAC,IAAIjJ,QAAQ,CAAC,CAAC,EAAE,IAAIK,QAAQ,CAAC,CAAC,EAAEgI,iBAAiB,CAAC,CAAC;EAC3Da,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,CAAC;EAChB;AACJ;AACA;AACA;EACI,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAACE,KAAK,EAAE;IAC7B,IAAI,CAACC,4BAA4B,CAACD,KAAK,CAAC;EAC5C;EACA1F,WAAWA;EACX;AACJ;AACA;AACA;EACI4F,KAAK,EAAEC,kBAAkB;EACzB;EACA;EACAC,KAAK,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAC7C;EACAC,cAAc,EAAEC,cAAc,EAAE;IAC5B,IAAI,CAACP,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;AACR;AACA;IACQ,IAAI,CAACzH,0BAA0B,GAAG,KAAK;IACvC;IACA,IAAI,CAACyF,aAAa,GAAG,OAAO;IAC5B;IACA,IAAI,CAAC/D,QAAQ,GAAG,KAAK;IACrB;IACA;IACA;IACA,IAAI,CAACkF,sBAAsB,GAAG,MAAM;IACpC;IACA;IACA;IACA;IACA,IAAI,CAACK,KAAK,KAAK,OAAO/F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3D,MAAMP,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI2G,cAAc,EAAE7B,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,GAAG6B,cAAc,EAAE7B,aAAa;IACtD;IACA,IAAI,CAACiC,mBAAmB,CAAC,CAAC;EAC9B;EACA7E,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACpC,EAAE,IAAI,IAAI,CAACyG,UAAU,EAAE;MAC7B,IAAI,CAACzG,EAAE,GAAG,IAAI,CAACyG,UAAU,CAACS,IAAI;IAClC;IACA;IACA,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,4BAA4B,CAAC;MAC9BC,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC,GAAG,QAAQ,GAAG,IAAI,CAACN;IAChD,CAAC,CAAC;IACF,IAAI,CAACR,KAAK,CAACpF,QAAQ,CAAC,IAAI,CAAC;IACzB,IAAI,CAACmG,WAAW,GAAG,IAAI,CAACZ,WAAW,CAACa,aAAa,CAACC,aAAa,CAAC,4BAA4B,CAAC;IAC7F,IAAI,CAACpB,4BAA4B,CAAC,IAAI,CAACF,sBAAsB,CAAC;EAClE;EACAuB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAChB,aAAa,CAACiB,OAAO,CAAC,IAAI,CAAChB,WAAW,EAAE,IAAI,CAAC,CAACiB,SAAS,CAACC,MAAM,IAAI;MACnE,MAAMC,QAAQ,GAAG,CAAC,CAACD,MAAM;MACzB,IAAIC,QAAQ,KAAK,IAAI,CAAChB,kBAAkB,EAAE;QACtC,IAAI,CAACiB,wBAAwB,CAACD,QAAQ,CAAC;QACvC,IAAI,CAACvB,kBAAkB,CAACyB,YAAY,CAAC,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN;EACAzF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmE,aAAa,CAACuB,cAAc,CAAC,IAAI,CAACtB,WAAW,CAAC;IACnD,IAAI,CAACH,KAAK,CAAChF,UAAU,CAAC,IAAI,CAAC;IAC3B,IAAI,CAAC0G,qBAAqB,CAACC,WAAW,CAAC,CAAC;IACxC,IAAI,IAAI,CAACZ,WAAW,EAAE;MAClB,IAAI,CAACX,cAAc,EAAEwB,iBAAiB,CAAC,IAAI,CAACb,WAAW,EAAE,IAAI,CAACpB,sBAAsB,CAAC;IACzF;EACJ;EACA;AACJ;AACA;AACA;EACI4B,wBAAwBA,CAACM,OAAO,EAAE;IAC9B;IACA,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,IAAID,OAAO,EAAE;MAC/B;IACJ;IACA,IAAI,CAACvB,kBAAkB,GAAGuB,OAAO;IACjC,IAAI,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;MACnB,IAAI,CAACH,qBAAqB,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACL,kBAAkB,EAAE;QACzB,IAAI,CAACM,4BAA4B,CAAC;UAAEmB,SAAS,EAAE,IAAI,CAACvB,eAAe;UAAEK,OAAO,EAAE;QAAO,CAAC,CAAC;MAC3F,CAAC,MACI;QACD,IAAI,CAACD,4BAA4B,CAAC;UAAEmB,SAAS,EAAE,MAAM;UAAElB,OAAO,EAAE,IAAI,CAACL;QAAgB,CAAC,CAAC;MAC3F;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACII,4BAA4BA,CAACoB,SAAS,EAAE;IACpC,IAAI,CAACzB,UAAU,GAAGyB,SAAS,IAAI,CAAC,CAAC;IACjC;IACA;IACA,IAAI,IAAI,CAACjJ,0BAA0B,EAAE;MACjC,IAAI,CAACwH,UAAU,GAAG;QAAEM,OAAO,EAAEmB,SAAS,CAACnB;MAAQ,CAAC;IACpD;EACJ;EACA;EACAoB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACjC,KAAK,CAAC9E,IAAI,CAAC,IAAI,CAAC;IACrB;IACA,IAAI,IAAI,CAACqF,UAAU,CAACM,OAAO,KAAK,MAAM,IAAI,IAAI,CAACN,UAAU,CAACM,OAAO,KAAK,QAAQ,EAAE;MAC5E,IAAI,CAAC9H,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACAmJ,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACJ,WAAW,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC9B,KAAK,CAAC9E,IAAI,CAAC,IAAI,CAAC;IACzB;EACJ;EACAiH,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACN,WAAW,CAAC,CAAC,KAAKM,KAAK,CAACC,OAAO,KAAKpL,KAAK,IAAImL,KAAK,CAACC,OAAO,KAAKnL,KAAK,CAAC,EAAE;MAC7EkL,KAAK,CAACE,cAAc,CAAC,CAAC;MACtB,IAAI,CAACL,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACAnB,SAASA,CAAA,EAAG;IACR,OAAQ,IAAI,CAACd,KAAK,CAAC7E,MAAM,IAAI,IAAI,CAAC3B,EAAE,KAC/B,IAAI,CAACwG,KAAK,CAACnG,SAAS,KAAK,KAAK,IAAI,IAAI,CAACmG,KAAK,CAACnG,SAAS,KAAK,MAAM,CAAC;EAC3E;EACA;EACAR,uBAAuBA,CAAA,EAAG;IACtB,OAAO,GAAG,IAAI,CAACyH,SAAS,CAAC,CAAC,GAAG,SAAS,GAAG,EAAE,GAAG,IAAI,CAACN,eAAe,EAAE;EACxE;EACA;EACApH,kBAAkBA,CAAA,EAAG;IACjB,MAAM2I,SAAS,GAAG,IAAI,CAACxB,UAAU,CAACwB,SAAS;IAC3C,OAAO,CAACA,SAAS,GAAG,GAAGA,SAAS,MAAM,GAAG,EAAE,IAAI,IAAI,CAACxB,UAAU,CAACM,OAAO;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACH,eAAe,GAAG,IAAI,CAACM,SAAS,CAAC,CAAC,GAAG,IAAI,CAACd,KAAK,CAACnG,SAAS,GAAG,IAAI,CAACW,KAAK,IAAI,IAAI,CAACwF,KAAK,CAACxF,KAAK;EACnG;EACAsH,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9B,KAAK,CAACvF,QAAQ,IAAI,IAAI,CAACA,QAAQ;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;EACI8H,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACzB,SAAS,CAAC,CAAC,EAAE;MACnB,OAAO,MAAM;IACjB;IACA,OAAO,IAAI,CAACd,KAAK,CAACnG,SAAS,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY;EACrE;EACA;EACA2I,YAAYA,CAAA,EAAG;IACX,OAAO,CAAC,IAAI,CAACV,WAAW,CAAC,CAAC,IAAI,IAAI,CAAChB,SAAS,CAAC,CAAC;EAClD;EACAjB,4BAA4BA,CAAC4C,cAAc,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC1B,WAAW,EAAE;MAClB;MACA;MACA,IAAI,CAACX,cAAc,EAAEwB,iBAAiB,CAAC,IAAI,CAACb,WAAW,EAAE,IAAI,CAACpB,sBAAsB,CAAC;MACrF,IAAI,CAACS,cAAc,EAAEsC,QAAQ,CAAC,IAAI,CAAC3B,WAAW,EAAE0B,cAAc,CAAC;IACnE;IACA,IAAI,CAAC9C,sBAAsB,GAAG8C,cAAc;EAChD;EACA;EACAhC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACiB,qBAAqB,GAAGrK,KAAK,CAAC,IAAI,CAAC2I,KAAK,CAACtF,UAAU,EAAE,IAAI,CAACsF,KAAK,CAACzF,aAAa,EAAE,IAAI,CAACuF,KAAK,CAAClB,OAAO,CAAC,CAACwC,SAAS,CAAC,MAAM;MACpH,IAAI,IAAI,CAACN,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAACH,qBAAqB,CAAC,CAAC;QAC5B;QACA,IAAI,IAAI,CAACJ,UAAU,CAACM,OAAO,KAAK,MAAM,IAAI,IAAI,CAACN,UAAU,CAACM,OAAO,KAAK,QAAQ,EAAE;UAC5E,IAAI,CAAC9H,0BAA0B,GAAG,IAAI;QAC1C;QACA,IAAI,CAAC6H,4BAA4B,CAAC;UAAEmB,SAAS,EAAE,IAAI,CAACvB,eAAe;UAAEK,OAAO,EAAE;QAAS,CAAC,CAAC;QACzF,IAAI,CAACP,kBAAkB,GAAG,KAAK;MACnC;MACA;MACA,IAAI,CAAC,IAAI,CAACQ,SAAS,CAAC,CAAC,IAAI,IAAI,CAACP,UAAU,IAAI,IAAI,CAACA,UAAU,CAACM,OAAO,KAAK,QAAQ,EAAE;QAC9E,IAAI,CAAC9H,0BAA0B,GAAG,KAAK;QACvC,IAAI,CAAC6H,4BAA4B,CAAC;UAAEmB,SAAS,EAAE,QAAQ;UAAElB,OAAO,EAAE,IAAI,CAACL;QAAgB,CAAC,CAAC;MAC7F;MACA,IAAI,CAACT,kBAAkB,CAACyB,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACvF,IAAI,YAAA0G,sBAAAxG,CAAA;MAAA,YAAAA,CAAA,IAAwFsD,aAAa,EA3XvBxJ,EAAE,CAAAmG,iBAAA,CA2XuCuC,iBAAiB,GA3X1D1I,EAAE,CAAAmG,iBAAA,CA2XqEnG,EAAE,CAAC2M,iBAAiB,GA3X3F3M,EAAE,CAAAmG,iBAAA,CA2XsGrC,OAAO,MA3X/G9D,EAAE,CAAAmG,iBAAA,CA2X0I,4BAA4B,MA3XxKnG,EAAE,CAAAmG,iBAAA,CA2XmMpF,EAAE,CAAC6L,YAAY,GA3XpN5M,EAAE,CAAAmG,iBAAA,CA2X+NnG,EAAE,CAAC6M,UAAU,GA3X9O7M,EAAE,CAAAmG,iBAAA,CA2XyPpF,EAAE,CAAC+L,aAAa,MA3X3Q9M,EAAE,CAAAmG,iBAAA,CA2XsStC,wBAAwB;IAAA,CAA4D;EAAE;EAC9d;IAAS,IAAI,CAACkJ,IAAI,kBA5X8E/M,EAAE,CAAAgN,iBAAA;MAAA1G,IAAA,EA4XJkD,aAAa;MAAAjD,SAAA;MAAAC,SAAA;MAAAyG,QAAA;MAAAC,YAAA,WAAAC,2BAAAhL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5XXnC,EAAE,CAAAwC,UAAA,mBAAA4K,uCAAA;YAAA,OA4XJhL,GAAA,CAAA6J,YAAA,CAAa,CAAC;UAAA,CAAF,CAAC,qBAAAoB,yCAAAC,MAAA;YAAA,OAAblL,GAAA,CAAA8J,cAAA,CAAAoB,MAAqB,CAAC;UAAA,CAAV,CAAC,wBAAAC,4CAAA;YAAA,OAAbnL,GAAA,CAAAkJ,wBAAA,CAAyB,IAAI,CAAC;UAAA,CAAlB,CAAC,wBAAAkC,4CAAA;YAAA,OAAbpL,GAAA,CAAAkJ,wBAAA,CAAyB,KAAK,CAAC;UAAA,CAAnB,CAAC;QAAA;QAAA,IAAAnJ,EAAA;UA5XXnC,EAAE,CAAAyN,WAAA,cA4XJrL,GAAA,CAAAkK,qBAAA,CAAsB,CAAC;UA5XrBtM,EAAE,CAAA0N,WAAA,6BA4XJtL,GAAA,CAAAyJ,WAAA,CAAY,EAAC;QAAA;MAAA;MAAApF,MAAA;QAAAlD,EAAA,GA5XXvD,EAAE,CAAA0G,YAAA,CAAAC,IAAA;QAAA4B,aAAA;QAAAhE,KAAA;QAAAC,QAAA,GAAFxE,EAAE,CAAA0G,YAAA,CAAAE,0BAAA,0BA4X0LzG,gBAAgB;QAAAsJ,qBAAA;QAAApE,YAAA,GA5X5MrF,EAAE,CAAA0G,YAAA,CAAAE,0BAAA,kCA4X4SzG,gBAAgB;MAAA;MAAA2G,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA5X9ThH,EAAE,CAAAiH,wBAAA,EAAFjH,EAAE,CAAA2N,mBAAA;MAAAC,KAAA,EAAA5L,GAAA;MAAA6L,kBAAA,EAAA5L,GAAA;MAAA6L,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAA/L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnC,EAAE,CAAAmO,eAAA;UAAFnO,EAAE,CAAAuC,cAAA,YA4X2oD,CAAC,YAAub,CAAC;UA5XtkEvC,EAAE,CAAAoO,YAAA,EA4XkmE,CAAC;UA5XrmEpO,EAAE,CAAAiD,YAAA,CA4X4mE,CAAC;UA5X/mEjD,EAAE,CAAAqO,UAAA,IAAAnM,oCAAA,gBA4X4sE,CAAC;UA5X/sElC,EAAE,CAAAiD,YAAA,CA4Xu+F,CAAC;QAAA;QAAA,IAAAd,EAAA;UA5X1+FnC,EAAE,CAAA0N,WAAA,2BAAAtL,GAAA,CAAAyI,SAAA,EA4Xo9C,CAAC,oCAAAzI,GAAA,CAAAmG,aAAA,aAA4E,CAAC;UA5XpiDvI,EAAE,CAAAyN,WAAA,aAAArL,GAAA,CAAAyJ,WAAA,uBAAAzJ,GAAA,CAAAyJ,WAAA;UAAF7L,EAAE,CAAAqD,SAAA,EA4X+9F,CAAC;UA5Xl+FrD,EAAE,CAAAsO,aAAA,IAAAlM,GAAA,CAAAmK,YAAA,WA4X+9F,CAAC;QAAA;MAAA;MAAAgC,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAAy4D,CACn8JzG,iBAAiB,CAACC,SAAS,EAC3BD,iBAAiB,CAACE,WAAW,EAC7BF,iBAAiB,CAACG,YAAY,EAC9BH,iBAAiB,CAACI,YAAY,EAC9BJ,iBAAiB,CAACM,aAAa,EAC/BN,iBAAiB,CAACO,aAAa;MAClC;MAAAmG,eAAA;IAAA,EAAiG;EAAE;AAC5G;AACA;EAAA,QAAA3K,SAAA,oBAAAA,SAAA,KArYoGhE,EAAE,CAAAmH,iBAAA,CAqYXqC,aAAa,EAAc,CAAC;IAC3GlD,IAAI,EAAE3F,SAAS;IACfyG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEP,QAAQ,EAAE,eAAe;MAAEQ,IAAI,EAAE;QAC7D,OAAO,EAAE,iBAAiB;QAC1B,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE,wBAAwB;QACrC,cAAc,EAAE,gCAAgC;QAChD,cAAc,EAAE,iCAAiC;QACjD,kBAAkB,EAAE,yBAAyB;QAC7C,kCAAkC,EAAE;MACxC,CAAC;MAAEkH,aAAa,EAAE5N,iBAAiB,CAAC+F,IAAI;MAAEgI,eAAe,EAAE9N,uBAAuB,CAAC+N,MAAM;MAAEC,UAAU,EAAE,CACnG5G,iBAAiB,CAACC,SAAS,EAC3BD,iBAAiB,CAACE,WAAW,EAC7BF,iBAAiB,CAACG,YAAY,EAC9BH,iBAAiB,CAACI,YAAY,EAC9BJ,iBAAiB,CAACM,aAAa,EAC/BN,iBAAiB,CAACO,aAAa,CAClC;MAAEzB,UAAU,EAAE,IAAI;MAAEkH,QAAQ,EAAE,wyEAAwyE;MAAEM,MAAM,EAAE,CAAC,o2DAAo2D;IAAE,CAAC;EACrsI,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjI,IAAI,EAAEoC;EAAkB,CAAC,EAAE;IAAEpC,IAAI,EAAEtG,EAAE,CAAC2M;EAAkB,CAAC,EAAE;IAAErG,IAAI,EAAExC,OAAO;IAAE0D,UAAU,EAAE,CAAC;MAC1GlB,IAAI,EAAEjG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEiG,IAAI,EAAEiB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClClB,IAAI,EAAEhG,MAAM;MACZ8G,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,EAAE;MACCd,IAAI,EAAEjG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEiG,IAAI,EAAEvF,EAAE,CAAC6L;EAAa,CAAC,EAAE;IAAEtG,IAAI,EAAEtG,EAAE,CAAC6M;EAAW,CAAC,EAAE;IAAEvG,IAAI,EAAEvF,EAAE,CAAC+L,aAAa;IAAEtF,UAAU,EAAE,CAAC;MAC7FlB,IAAI,EAAEjG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEiG,IAAI,EAAEiB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClClB,IAAI,EAAEjG;IACV,CAAC,EAAE;MACCiG,IAAI,EAAEhG,MAAM;MACZ8G,IAAI,EAAE,CAACvD,wBAAwB;IACnC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEN,EAAE,EAAE,CAAC;MAC9B+C,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEmB,aAAa,EAAE,CAAC;MAChBjC,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEgE,KAAK,EAAE,CAAC;MACR+B,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEiE,QAAQ,EAAE,CAAC;MACX8B,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsJ,qBAAqB,EAAE,CAAC;MACxBnD,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAE8E,YAAY,EAAE,CAAC;MACfiB,IAAI,EAAE/F,KAAK;MACX6G,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvH;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2O,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC9I,IAAI,YAAA+I,sBAAA7I,CAAA;MAAA,YAAAA,CAAA,IAAwF4I,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAxb8EhP,EAAE,CAAAiP,gBAAA;MAAA3I,IAAA,EAwbSwI;IAAa,EAA0F;EAAE;EACpN;IAAS,IAAI,CAACI,IAAI,kBAzb8ElP,EAAE,CAAAmP,gBAAA;MAAAC,SAAA,EAybmC,CAAChG,6BAA6B,CAAC;MAAAiG,OAAA,GAAYtN,eAAe;IAAA,EAAI;EAAE;AACzM;AACA;EAAA,QAAAiC,SAAA,oBAAAA,SAAA,KA3boGhE,EAAE,CAAAmH,iBAAA,CA2bX2H,aAAa,EAAc,CAAC;IAC3GxI,IAAI,EAAExF,QAAQ;IACdsG,IAAI,EAAE,CAAC;MACCiI,OAAO,EAAE,CAACtN,eAAe,EAAE+B,OAAO,EAAE0F,aAAa,CAAC;MAClD8F,OAAO,EAAE,CAACxL,OAAO,EAAE0F,aAAa,CAAC;MACjC4F,SAAS,EAAE,CAAChG,6BAA6B;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASvF,wBAAwB,EAAEuF,6BAA6B,EAAEF,qCAAqC,EAAEpF,OAAO,EAAE0F,aAAa,EAAEd,iBAAiB,EAAEoG,aAAa,EAAE7G,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}