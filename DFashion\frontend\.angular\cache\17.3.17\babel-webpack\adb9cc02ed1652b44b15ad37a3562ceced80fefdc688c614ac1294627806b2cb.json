{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/role-management.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction DynamicProfileComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function DynamicProfileComponent_button_4_Template_button_click_0_listener() {\n      const action_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleAction(action_r2.action));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r2 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background\", action_r2.color);\n    i0.ɵɵproperty(\"title\", action_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r2.icon);\n  }\n}\nfunction DynamicProfileComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 31);\n  }\n}\nfunction DynamicProfileComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DynamicProfileComponent_p_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.userProfile == null ? null : ctx_r2.userProfile.bio);\n  }\n}\nfunction DynamicProfileComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.userProfile == null ? null : ctx_r2.userProfile.location);\n  }\n}\nfunction DynamicProfileComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Joined \", i0.ɵɵpipeBind2(4, 1, ctx_r2.userProfile == null ? null : ctx_r2.userProfile.joinDate, \"MMM yyyy\"), \"\");\n  }\n}\nfunction DynamicProfileComponent_div_33_div_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"trend-\" + stat_r4.trend);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getTrendIcon(stat_r4.trend));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r4.trendValue);\n  }\n}\nfunction DynamicProfileComponent_div_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"div\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 44);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DynamicProfileComponent_div_33_div_2_div_8_Template, 4, 5, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r4 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r4.color);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", stat_r4.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(stat_r4.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r4.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r4.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", stat_r4.trend);\n  }\n}\nfunction DynamicProfileComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵtemplate(2, DynamicProfileComponent_div_33_div_2_Template, 9, 9, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getProfileStats());\n  }\n}\nfunction DynamicProfileComponent_div_35_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"div\", 57);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const initiative_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(initiative_r5.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(initiative_r5.name);\n  }\n}\nfunction DynamicProfileComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48)(2, \"div\", 49)(3, \"h3\");\n    i0.ɵɵtext(4, \"Leadership Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"div\", 51)(7, \"span\", 52);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 53);\n    i0.ɵɵtext(10, \"Team Members\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 51)(12, \"span\", 52);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 53);\n    i0.ɵɵtext(15, \"Departments\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 49)(17, \"h3\");\n    i0.ɵɵtext(18, \"Strategic Initiatives\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 54);\n    i0.ɵɵtemplate(20, DynamicProfileComponent_div_35_div_20_Template, 4, 3, \"div\", 55);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.getTeamSize());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getDepartmentCount());\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStrategicInitiatives());\n  }\n}\nfunction DynamicProfileComponent_div_36_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"img\", 67);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 68);\n    i0.ɵɵelement(6, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const member_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", member_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", member_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(member_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", member_r6.performance, \"%\");\n  }\n}\nfunction DynamicProfileComponent_div_36_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 72)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 73)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const goal_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background\", ctx_r2.getProgressColor(goal_r7.progress));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", goal_r7.progress, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(goal_r7.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r7.description);\n  }\n}\nfunction DynamicProfileComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60)(3, \"h3\");\n    i0.ɵɵtext(4, \"Team Performance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61);\n    i0.ɵɵtemplate(6, DynamicProfileComponent_div_36_div_6_Template, 7, 5, \"div\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 60)(8, \"h3\");\n    i0.ɵɵtext(9, \"Department Goals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 63);\n    i0.ɵɵtemplate(11, DynamicProfileComponent_div_36_div_11_Template, 10, 5, \"div\", 64);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getTeamMembers());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getDepartmentGoals());\n  }\n}\nfunction DynamicProfileComponent_div_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const metric_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", metric_r8.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r8.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r8.label);\n  }\n}\nfunction DynamicProfileComponent_div_37_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 85)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 86);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const achievement_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", achievement_r9.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(achievement_r9.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(achievement_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(achievement_r9.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 7, achievement_r9.date, \"short\"));\n  }\n}\nfunction DynamicProfileComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\", 76)(3, \"h3\");\n    i0.ɵɵtext(4, \"My Performance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 77);\n    i0.ɵɵtemplate(6, DynamicProfileComponent_div_37_div_6_Template, 6, 4, \"div\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 76)(8, \"h3\");\n    i0.ɵɵtext(9, \"Recent Achievements\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 79);\n    i0.ɵɵtemplate(11, DynamicProfileComponent_div_37_div_11_Template, 11, 10, \"div\", 80);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getPersonalMetrics());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getRecentAchievements());\n  }\n}\nfunction DynamicProfileComponent_div_38_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 96)(4, \"span\", 52);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const metric_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", metric_r10.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(metric_r10.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(metric_r10.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r10.label);\n  }\n}\nfunction DynamicProfileComponent_div_38_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 99)(5, \"span\", 100);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 101);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 3, activity_r11.time, \"short\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(activity_r11.user);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r11.action);\n  }\n}\nfunction DynamicProfileComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"div\", 89)(3, \"h3\");\n    i0.ɵɵtext(4, \"System Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 90);\n    i0.ɵɵtemplate(6, DynamicProfileComponent_div_38_div_6_Template, 8, 6, \"div\", 91);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 89)(8, \"h3\");\n    i0.ɵɵtext(9, \"Recent Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 92);\n    i0.ɵɵtemplate(11, DynamicProfileComponent_div_38_div_11_Template, 9, 6, \"div\", 93);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSystemMetrics());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getRecentActivities());\n  }\n}\nexport class DynamicProfileComponent {\n  constructor(roleManagementService, router) {\n    this.roleManagementService = roleManagementService;\n    this.router = router;\n    this.userProfile = null;\n    this.roleConfig = null;\n    this.profileLayout = 'specialist';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (this.userProfile?.role) {\n      this.roleConfig = this.roleManagementService.getRoleConfig(this.userProfile.role);\n      this.profileLayout = this.roleConfig.profileLayout;\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  getCoverGradient() {\n    if (!this.roleConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n    const color = this.roleConfig.color;\n    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n  }\n  getProfileActions() {\n    if (!this.userProfile?.role) return [];\n    const baseActions = [{\n      label: 'Edit Profile',\n      icon: 'fas fa-edit',\n      action: 'edit',\n      color: '#4ECDC4'\n    }, {\n      label: 'Settings',\n      icon: 'fas fa-cog',\n      action: 'settings',\n      color: '#45B7D1'\n    }];\n    // Add role-specific actions\n    if (this.roleManagementService.isManager(this.userProfile.role)) {\n      baseActions.push({\n        label: 'Team Management',\n        icon: 'fas fa-users',\n        action: 'team',\n        color: '#96CEB4'\n      }, {\n        label: 'Reports',\n        icon: 'fas fa-chart-bar',\n        action: 'reports',\n        color: '#FFEAA7'\n      });\n    }\n    if (this.userProfile.role === 'super_admin' || this.userProfile.role === 'admin') {\n      baseActions.push({\n        label: 'Admin Panel',\n        icon: 'fas fa-shield-alt',\n        action: 'admin',\n        color: '#FF6B6B'\n      });\n    }\n    return baseActions;\n  }\n  getProfileStats() {\n    // This would be populated based on role and real data\n    // For now, returning mock data based on role\n    if (!this.userProfile?.role) return [];\n    const role = this.userProfile.role;\n    if (role.includes('sales')) {\n      return [{\n        label: 'Sales This Month',\n        value: '₹2.4M',\n        icon: 'fas fa-rupee-sign',\n        color: '#45B7D1',\n        trend: 'up',\n        trendValue: '+12%'\n      }, {\n        label: 'Deals Closed',\n        value: 47,\n        icon: 'fas fa-handshake',\n        color: '#96CEB4',\n        trend: 'up',\n        trendValue: '+8%'\n      }, {\n        label: 'Target Achievement',\n        value: '94%',\n        icon: 'fas fa-target',\n        color: '#FFEAA7',\n        trend: 'stable'\n      }];\n    }\n    if (role.includes('marketing')) {\n      return [{\n        label: 'Campaign Reach',\n        value: '1.2M',\n        icon: 'fas fa-eye',\n        color: '#F38BA8',\n        trend: 'up',\n        trendValue: '+15%'\n      }, {\n        label: 'Engagement Rate',\n        value: '4.8%',\n        icon: 'fas fa-heart',\n        color: '#DDA0DD',\n        trend: 'up',\n        trendValue: '+0.3%'\n      }, {\n        label: 'Conversions',\n        value: 2847,\n        icon: 'fas fa-exchange-alt',\n        color: '#FFB6C1',\n        trend: 'down',\n        trendValue: '-2%'\n      }];\n    }\n    // Default stats for other roles\n    return [{\n      label: 'Tasks Completed',\n      value: 156,\n      icon: 'fas fa-check-circle',\n      color: '#6BCF7F',\n      trend: 'up',\n      trendValue: '+5%'\n    }, {\n      label: 'Projects Active',\n      value: 8,\n      icon: 'fas fa-project-diagram',\n      color: '#4ECDC4',\n      trend: 'stable'\n    }];\n  }\n  handleAction(action) {\n    switch (action) {\n      case 'edit':\n        this.router.navigate(['/profile/edit']);\n        break;\n      case 'settings':\n        this.router.navigate(['/profile/settings']);\n        break;\n      case 'team':\n        this.router.navigate(['/team/management']);\n        break;\n      case 'reports':\n        this.router.navigate(['/reports']);\n        break;\n      case 'admin':\n        this.router.navigate(['/admin']);\n        break;\n    }\n  }\n  getLastActiveText() {\n    if (!this.userProfile?.lastActive) return 'Unknown';\n    const now = new Date();\n    const lastActive = new Date(this.userProfile.lastActive);\n    const diffMs = now.getTime() - lastActive.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    if (diffMins < 1) return 'Just now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;\n    return `${Math.floor(diffMins / 1440)}d ago`;\n  }\n  getTrendIcon(trend) {\n    switch (trend) {\n      case 'up':\n        return 'fas fa-arrow-up';\n      case 'down':\n        return 'fas fa-arrow-down';\n      default:\n        return 'fas fa-minus';\n    }\n  }\n  // Mock data methods - these would be replaced with real data services\n  getTeamSize() {\n    return 12;\n  }\n  getDepartmentCount() {\n    return 3;\n  }\n  getStrategicInitiatives() {\n    return [];\n  }\n  getTeamMembers() {\n    return [];\n  }\n  getDepartmentGoals() {\n    return [];\n  }\n  getPersonalMetrics() {\n    return [];\n  }\n  getRecentAchievements() {\n    return [];\n  }\n  getSystemMetrics() {\n    return [];\n  }\n  getRecentActivities() {\n    return [];\n  }\n  getProgressColor(progress) {\n    return progress > 75 ? '#6BCF7F' : progress > 50 ? '#FFEAA7' : '#FF6B6B';\n  }\n  static {\n    this.ɵfac = function DynamicProfileComponent_Factory(t) {\n      return new (t || DynamicProfileComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DynamicProfileComponent,\n      selectors: [[\"app-dynamic-profile\"]],\n      inputs: {\n        userProfile: \"userProfile\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 39,\n      vars: 30,\n      consts: [[1, \"dynamic-profile\"], [1, \"profile-header\"], [1, \"profile-cover\"], [1, \"profile-actions\"], [\"class\", \"action-btn\", 3, \"background\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-info\"], [1, \"avatar-section\"], [1, \"avatar-container\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [\"class\", \"online-indicator\", 4, \"ngIf\"], [1, \"role-badge\"], [1, \"profile-details\"], [1, \"name-section\"], [1, \"profile-name\"], [\"class\", \"verification-badge\", 4, \"ngIf\"], [1, \"profile-username\"], [1, \"role-info\"], [1, \"role-title\"], [1, \"department\"], [\"class\", \"profile-bio\", 4, \"ngIf\"], [1, \"profile-meta\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [1, \"meta-item\"], [1, \"fas\", \"fa-clock\"], [\"class\", \"profile-stats\", 4, \"ngIf\"], [1, \"role-content\"], [\"class\", \"executive-layout\", 4, \"ngIf\"], [\"class\", \"manager-layout\", 4, \"ngIf\"], [\"class\", \"specialist-layout\", 4, \"ngIf\"], [\"class\", \"admin-layout\", 4, \"ngIf\"], [1, \"action-btn\", 3, \"click\", \"title\"], [1, \"online-indicator\"], [1, \"verification-badge\"], [1, \"fas\", \"fa-check-circle\"], [1, \"profile-bio\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"fas\", \"fa-calendar-alt\"], [1, \"profile-stats\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-trend\", 3, \"class\", 4, \"ngIf\"], [1, \"stat-trend\"], [1, \"executive-layout\"], [1, \"executive-grid\"], [1, \"executive-card\"], [1, \"leadership-metrics\"], [1, \"metric\"], [1, \"metric-value\"], [1, \"metric-label\"], [1, \"initiatives-list\"], [\"class\", \"initiative-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"initiative-item\"], [1, \"initiative-status\"], [1, \"manager-layout\"], [1, \"manager-grid\"], [1, \"manager-card\"], [1, \"performance-chart\"], [\"class\", \"performance-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"goals-list\"], [\"class\", \"goal-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"performance-item\"], [1, \"member-info\"], [1, \"member-avatar\", 3, \"src\", \"alt\"], [1, \"performance-bar\"], [1, \"progress\"], [1, \"goal-item\"], [1, \"goal-progress\"], [1, \"progress-circle\"], [1, \"goal-details\"], [1, \"specialist-layout\"], [1, \"specialist-grid\"], [1, \"specialist-card\"], [1, \"performance-metrics\"], [\"class\", \"metric-circle\", 4, \"ngFor\", \"ngForOf\"], [1, \"achievements-list\"], [\"class\", \"achievement-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"metric-circle\"], [1, \"circle-progress\"], [1, \"achievement-item\"], [1, \"achievement-icon\"], [1, \"achievement-details\"], [1, \"achievement-date\"], [1, \"admin-layout\"], [1, \"admin-grid\"], [1, \"admin-card\"], [1, \"system-stats\"], [\"class\", \"system-metric\", 4, \"ngFor\", \"ngForOf\"], [1, \"activities-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"system-metric\"], [1, \"metric-icon\"], [1, \"metric-data\"], [1, \"activity-item\"], [1, \"activity-time\"], [1, \"activity-content\"], [1, \"activity-user\"], [1, \"activity-action\"]],\n      template: function DynamicProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, DynamicProfileComponent_button_4_Template, 2, 5, \"button\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelement(8, \"img\", 8);\n          i0.ɵɵtemplate(9, DynamicProfileComponent_div_9_Template, 1, 0, \"div\", 9);\n          i0.ɵɵelementStart(10, \"div\", 10);\n          i0.ɵɵelement(11, \"i\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"h1\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, DynamicProfileComponent_div_16_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 15);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 16)(20, \"span\", 17);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"span\", 18);\n          i0.ɵɵtext(23);\n          i0.ɵɵpipe(24, \"titlecase\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, DynamicProfileComponent_p_25_Template, 2, 1, \"p\", 19);\n          i0.ɵɵelementStart(26, \"div\", 20);\n          i0.ɵɵtemplate(27, DynamicProfileComponent_div_27_Template, 4, 1, \"div\", 21)(28, DynamicProfileComponent_div_28_Template, 5, 4, \"div\", 21);\n          i0.ɵɵelementStart(29, \"div\", 22);\n          i0.ɵɵelement(30, \"i\", 23);\n          i0.ɵɵelementStart(31, \"span\");\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(33, DynamicProfileComponent_div_33_Template, 3, 1, \"div\", 24);\n          i0.ɵɵelementStart(34, \"div\", 25);\n          i0.ɵɵtemplate(35, DynamicProfileComponent_div_35_Template, 21, 3, \"div\", 26)(36, DynamicProfileComponent_div_36_Template, 12, 2, \"div\", 27)(37, DynamicProfileComponent_div_37_Template, 12, 2, \"div\", 28)(38, DynamicProfileComponent_div_38_Template, 12, 2, \"div\", 29);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-layout\", ctx.profileLayout)(\"data-role\", ctx.userProfile == null ? null : ctx.userProfile.role);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background\", ctx.getCoverGradient());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getProfileActions());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", (ctx.userProfile == null ? null : ctx.userProfile.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.userProfile == null ? null : ctx.userProfile.fullName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.isOnline);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"background\", ctx.roleConfig == null ? null : ctx.roleConfig.color);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.roleConfig == null ? null : ctx.roleConfig.icon);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.userProfile == null ? null : ctx.userProfile.fullName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.isVerified);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"@\", (ctx.userProfile == null ? null : ctx.userProfile.username) || \"username\", \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", ctx.roleConfig == null ? null : ctx.roleConfig.color);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.roleConfig == null ? null : ctx.roleConfig.displayName, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 28, ctx.roleConfig == null ? null : ctx.roleConfig.department));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.bio);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.location);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.joinDate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Last active \", ctx.getLastActiveText(), \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getProfileStats().length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"executive\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"manager\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"specialist\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"admin\");\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.TitleCasePipe, i3.DatePipe],\n      styles: [\".dynamic-profile[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0;\\n  background: #fafafa;\\n  min-height: 100vh;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: white;\\n  border-radius: 0 0 24px 24px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  margin-bottom: 2rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-cover[_ngcontent-%COMP%] {\\n  height: 200px;\\n  position: relative;\\n  display: flex;\\n  align-items: flex-end;\\n  justify-content: flex-end;\\n  padding: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .dynamic-profile[_ngcontent-%COMP%]   .profile-cover[_ngcontent-%COMP%] {\\n    height: 150px;\\n  }\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  z-index: 2;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%] {\\n  padding: 0 2rem 2rem;\\n  margin-top: -60px;\\n  position: relative;\\n  z-index: 3;\\n}\\n@media (max-width: 768px) {\\n  .dynamic-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%] {\\n    padding: 0 1rem 1.5rem;\\n    margin-top: -40px;\\n  }\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .avatar-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  align-items: flex-start;\\n}\\n@media (max-width: 768px) {\\n  .dynamic-profile[_ngcontent-%COMP%]   .avatar-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    text-align: center;\\n    gap: 1rem;\\n  }\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex-shrink: 0;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  border-radius: 50%;\\n  border: 4px solid white;\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n  object-fit: cover;\\n}\\n@media (max-width: 768px) {\\n  .dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n  }\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 8px;\\n  width: 20px;\\n  height: 20px;\\n  background: #4CAF50;\\n  border: 3px solid white;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: -5px;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 0.8rem;\\n  border: 2px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.25rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #262626;\\n  margin: 0;\\n  line-height: 1.2;\\n}\\n@media (max-width: 768px) {\\n  .dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%] {\\n  color: #1DA1F2;\\n  font-size: 1.2rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 1rem;\\n  margin: 0 0 0.5rem 0;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .role-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .role-info[_ngcontent-%COMP%]   .role-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.95rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .role-info[_ngcontent-%COMP%]   .department[_ngcontent-%COMP%] {\\n  background: #f0f0f0;\\n  color: #666;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-bio[_ngcontent-%COMP%] {\\n  color: #262626;\\n  line-height: 1.5;\\n  margin: 0 0 1rem 0;\\n  font-size: 0.95rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: #8e8e8e;\\n  font-size: 0.85rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 14px;\\n  text-align: center;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1rem;\\n  padding: 0 1rem;\\n}\\n@media (max-width: 768px) {\\n  .dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  border-left: 4px solid;\\n  transition: transform 0.2s ease;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: rgba(0, 0, 0, 0.05);\\n  border-radius: 12px;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #262626;\\n  line-height: 1;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.85rem;\\n  margin-top: 0.25rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend.trend-up[_ngcontent-%COMP%] {\\n  color: #4CAF50;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend.trend-down[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend.trend-stable[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .role-content[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 1.5rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #262626;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: space-around;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #262626;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #8e8e8e;\\n  margin-top: 0.25rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .member-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  min-width: 120px;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .member-info[_ngcontent-%COMP%]   .member-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .performance-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background: #f0f0f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .performance-bar[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #4CAF50, #8BC34A);\\n  transition: width 0.3s ease;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 0.75rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .achievement-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 0.75rem 0;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%]   .metric-data[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%]   .metric-data[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #8e8e8e;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .dynamic-profile[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .dynamic-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n    color: #ffffff;\\n  }\\n  .dynamic-profile[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .dynamic-profile[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    color: #b3b3b3;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subject", "i0", "ɵɵelementStart", "ɵɵlistener", "DynamicProfileComponent_button_4_Template_button_click_0_listener", "action_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "handleAction", "action", "ɵɵelement", "ɵɵelementEnd", "ɵɵstyleProp", "color", "ɵɵproperty", "label", "ɵɵadvance", "ɵɵclassMap", "icon", "ɵɵtext", "ɵɵtextInterpolate", "userProfile", "bio", "location", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "joinDate", "stat_r4", "trend", "getTrendIcon", "trendValue", "ɵɵtemplate", "DynamicProfileComponent_div_33_div_2_div_8_Template", "value", "DynamicProfileComponent_div_33_div_2_Template", "getProfileStats", "initiative_r5", "status", "name", "DynamicProfileComponent_div_35_div_20_Template", "getTeamSize", "getDepartmentCount", "getStrategicInitiatives", "member_r6", "avatar", "ɵɵsanitizeUrl", "performance", "getProgressColor", "goal_r7", "progress", "title", "description", "DynamicProfileComponent_div_36_div_6_Template", "DynamicProfileComponent_div_36_div_11_Template", "getTeamMembers", "getDepartmentGoals", "metric_r8", "achievement_r9", "date", "DynamicProfileComponent_div_37_div_6_Template", "DynamicProfileComponent_div_37_div_11_Template", "getPersonalMetrics", "getRecentAchievements", "metric_r10", "activity_r11", "time", "user", "DynamicProfileComponent_div_38_div_6_Template", "DynamicProfileComponent_div_38_div_11_Template", "getSystemMetrics", "getRecentActivities", "DynamicProfileComponent", "constructor", "roleManagementService", "router", "roleConfig", "profileLayout", "destroy$", "ngOnInit", "role", "getRoleConfig", "ngOnDestroy", "next", "complete", "getCoverGradient", "getProfileActions", "baseActions", "is<PERSON>anager", "push", "includes", "navigate", "getLastActiveText", "lastActive", "now", "Date", "diffMs", "getTime", "diffMins", "Math", "floor", "ɵɵdirectiveInject", "i1", "RoleManagementService", "i2", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DynamicProfileComponent_Template", "rf", "ctx", "DynamicProfileComponent_button_4_Template", "DynamicProfileComponent_div_9_Template", "DynamicProfileComponent_div_16_Template", "DynamicProfileComponent_p_25_Template", "DynamicProfileComponent_div_27_Template", "DynamicProfileComponent_div_28_Template", "DynamicProfileComponent_div_33_Template", "DynamicProfileComponent_div_35_Template", "DynamicProfileComponent_div_36_Template", "DynamicProfileComponent_div_37_Template", "DynamicProfileComponent_div_38_Template", "fullName", "isOnline", "isVerified", "username", "displayName", "ɵɵpipeBind1", "department", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "TitleCasePipe", "DatePipe", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\dynamic-profile\\dynamic-profile.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { RoleManagementService, UserRole, RoleConfig } from '../../../core/services/role-management.service';\n\nexport interface ProfileStats {\n  label: string;\n  value: string | number;\n  icon: string;\n  color: string;\n  trend?: 'up' | 'down' | 'stable';\n  trendValue?: string;\n}\n\nexport interface ProfileAction {\n  label: string;\n  icon: string;\n  action: string;\n  color: string;\n  permission?: string;\n}\n\nexport interface UserProfile {\n  id: string;\n  username: string;\n  fullName: string;\n  email: string;\n  avatar: string;\n  role: UserRole;\n  department: string;\n  joinDate: Date;\n  lastActive: Date;\n  bio?: string;\n  location?: string;\n  phone?: string;\n  isVerified: boolean;\n  isOnline: boolean;\n}\n\n@Component({\n  selector: 'app-dynamic-profile',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"dynamic-profile\" [attr.data-layout]=\"profileLayout\" [attr.data-role]=\"userProfile?.role\">\n      <!-- Profile Header -->\n      <div class=\"profile-header\">\n        <div class=\"profile-cover\" [style.background]=\"getCoverGradient()\">\n          <div class=\"profile-actions\">\n            <button \n              *ngFor=\"let action of getProfileActions()\" \n              class=\"action-btn\"\n              [style.background]=\"action.color\"\n              (click)=\"handleAction(action.action)\"\n              [title]=\"action.label\">\n              <i [class]=\"action.icon\"></i>\n            </button>\n          </div>\n        </div>\n        \n        <div class=\"profile-info\">\n          <div class=\"avatar-section\">\n            <div class=\"avatar-container\">\n              <img [src]=\"userProfile?.avatar || '/assets/images/default-avatar.png'\" \n                   [alt]=\"userProfile?.fullName\" \n                   class=\"profile-avatar\">\n              <div class=\"online-indicator\" *ngIf=\"userProfile?.isOnline\"></div>\n              <div class=\"role-badge\" [style.background]=\"roleConfig?.color\">\n                <i [class]=\"roleConfig?.icon\"></i>\n              </div>\n            </div>\n            \n            <div class=\"profile-details\">\n              <div class=\"name-section\">\n                <h1 class=\"profile-name\">{{ userProfile?.fullName }}</h1>\n                <div class=\"verification-badge\" *ngIf=\"userProfile?.isVerified\">\n                  <i class=\"fas fa-check-circle\"></i>\n                </div>\n              </div>\n              \n              <p class=\"profile-username\">&#64;{{ userProfile?.username || 'username' }}</p>\n              <div class=\"role-info\">\n                <span class=\"role-title\" [style.color]=\"roleConfig?.color\">\n                  {{ roleConfig?.displayName }}\n                </span>\n                <span class=\"department\">{{ roleConfig?.department | titlecase }}</span>\n              </div>\n              \n              <p class=\"profile-bio\" *ngIf=\"userProfile?.bio\">{{ userProfile?.bio }}</p>\n              \n              <div class=\"profile-meta\">\n                <div class=\"meta-item\" *ngIf=\"userProfile?.location\">\n                  <i class=\"fas fa-map-marker-alt\"></i>\n                  <span>{{ userProfile?.location }}</span>\n                </div>\n                <div class=\"meta-item\" *ngIf=\"userProfile?.joinDate\">\n                  <i class=\"fas fa-calendar-alt\"></i>\n                  <span>Joined {{ userProfile?.joinDate | date:'MMM yyyy' }}</span>\n                </div>\n                <div class=\"meta-item\">\n                  <i class=\"fas fa-clock\"></i>\n                  <span>Last active {{ getLastActiveText() }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Profile Stats -->\n      <div class=\"profile-stats\" *ngIf=\"getProfileStats().length > 0\">\n        <div class=\"stats-grid\">\n          <div \n            *ngFor=\"let stat of getProfileStats()\" \n            class=\"stat-card\"\n            [style.border-left-color]=\"stat.color\">\n            <div class=\"stat-icon\" [style.color]=\"stat.color\">\n              <i [class]=\"stat.icon\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-value\">{{ stat.value }}</div>\n              <div class=\"stat-label\">{{ stat.label }}</div>\n              <div class=\"stat-trend\" *ngIf=\"stat.trend\" [class]=\"'trend-' + stat.trend\">\n                <i [class]=\"getTrendIcon(stat.trend)\"></i>\n                <span>{{ stat.trendValue }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Role-Specific Content -->\n      <div class=\"role-content\">\n        <!-- Executive Layout -->\n        <div *ngIf=\"profileLayout === 'executive'\" class=\"executive-layout\">\n          <div class=\"executive-grid\">\n            <div class=\"executive-card\">\n              <h3>Leadership Overview</h3>\n              <div class=\"leadership-metrics\">\n                <div class=\"metric\">\n                  <span class=\"metric-value\">{{ getTeamSize() }}</span>\n                  <span class=\"metric-label\">Team Members</span>\n                </div>\n                <div class=\"metric\">\n                  <span class=\"metric-value\">{{ getDepartmentCount() }}</span>\n                  <span class=\"metric-label\">Departments</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"executive-card\">\n              <h3>Strategic Initiatives</h3>\n              <div class=\"initiatives-list\">\n                <div class=\"initiative-item\" *ngFor=\"let initiative of getStrategicInitiatives()\">\n                  <div class=\"initiative-status\" [class]=\"initiative.status\"></div>\n                  <span>{{ initiative.name }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Manager Layout -->\n        <div *ngIf=\"profileLayout === 'manager'\" class=\"manager-layout\">\n          <div class=\"manager-grid\">\n            <div class=\"manager-card\">\n              <h3>Team Performance</h3>\n              <div class=\"performance-chart\">\n                <!-- Team performance visualization -->\n                <div class=\"performance-item\" *ngFor=\"let member of getTeamMembers()\">\n                  <div class=\"member-info\">\n                    <img [src]=\"member.avatar\" [alt]=\"member.name\" class=\"member-avatar\">\n                    <span>{{ member.name }}</span>\n                  </div>\n                  <div class=\"performance-bar\">\n                    <div class=\"progress\" [style.width.%]=\"member.performance\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"manager-card\">\n              <h3>Department Goals</h3>\n              <div class=\"goals-list\">\n                <div class=\"goal-item\" *ngFor=\"let goal of getDepartmentGoals()\">\n                  <div class=\"goal-progress\">\n                    <div class=\"progress-circle\" [style.background]=\"getProgressColor(goal.progress)\">\n                      <span>{{ goal.progress }}%</span>\n                    </div>\n                  </div>\n                  <div class=\"goal-details\">\n                    <h4>{{ goal.title }}</h4>\n                    <p>{{ goal.description }}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Specialist Layout -->\n        <div *ngIf=\"profileLayout === 'specialist'\" class=\"specialist-layout\">\n          <div class=\"specialist-grid\">\n            <div class=\"specialist-card\">\n              <h3>My Performance</h3>\n              <div class=\"performance-metrics\">\n                <div class=\"metric-circle\" *ngFor=\"let metric of getPersonalMetrics()\">\n                  <div class=\"circle-progress\" [style.background]=\"metric.color\">\n                    <span>{{ metric.value }}</span>\n                  </div>\n                  <label>{{ metric.label }}</label>\n                </div>\n              </div>\n            </div>\n            <div class=\"specialist-card\">\n              <h3>Recent Achievements</h3>\n              <div class=\"achievements-list\">\n                <div class=\"achievement-item\" *ngFor=\"let achievement of getRecentAchievements()\">\n                  <div class=\"achievement-icon\" [style.background]=\"achievement.color\">\n                    <i [class]=\"achievement.icon\"></i>\n                  </div>\n                  <div class=\"achievement-details\">\n                    <h4>{{ achievement.title }}</h4>\n                    <p>{{ achievement.description }}</p>\n                    <span class=\"achievement-date\">{{ achievement.date | date:'short' }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Admin Layout -->\n        <div *ngIf=\"profileLayout === 'admin'\" class=\"admin-layout\">\n          <div class=\"admin-grid\">\n            <div class=\"admin-card\">\n              <h3>System Overview</h3>\n              <div class=\"system-stats\">\n                <div class=\"system-metric\" *ngFor=\"let metric of getSystemMetrics()\">\n                  <div class=\"metric-icon\" [style.color]=\"metric.color\">\n                    <i [class]=\"metric.icon\"></i>\n                  </div>\n                  <div class=\"metric-data\">\n                    <span class=\"metric-value\">{{ metric.value }}</span>\n                    <span class=\"metric-label\">{{ metric.label }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"admin-card\">\n              <h3>Recent Activities</h3>\n              <div class=\"activities-list\">\n                <div class=\"activity-item\" *ngFor=\"let activity of getRecentActivities()\">\n                  <div class=\"activity-time\">{{ activity.time | date:'short' }}</div>\n                  <div class=\"activity-content\">\n                    <span class=\"activity-user\">{{ activity.user }}</span>\n                    <span class=\"activity-action\">{{ activity.action }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./dynamic-profile.component.scss']\n})\nexport class DynamicProfileComponent implements OnInit, OnDestroy {\n  @Input() userProfile: UserProfile | null = null;\n  \n  roleConfig: RoleConfig | null = null;\n  profileLayout: string = 'specialist';\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private roleManagementService: RoleManagementService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    if (this.userProfile?.role) {\n      this.roleConfig = this.roleManagementService.getRoleConfig(this.userProfile.role);\n      this.profileLayout = this.roleConfig.profileLayout;\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  getCoverGradient(): string {\n    if (!this.roleConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n    \n    const color = this.roleConfig.color;\n    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n  }\n\n  getProfileActions(): ProfileAction[] {\n    if (!this.userProfile?.role) return [];\n\n    const baseActions: ProfileAction[] = [\n      { label: 'Edit Profile', icon: 'fas fa-edit', action: 'edit', color: '#4ECDC4' },\n      { label: 'Settings', icon: 'fas fa-cog', action: 'settings', color: '#45B7D1' }\n    ];\n\n    // Add role-specific actions\n    if (this.roleManagementService.isManager(this.userProfile.role)) {\n      baseActions.push(\n        { label: 'Team Management', icon: 'fas fa-users', action: 'team', color: '#96CEB4' },\n        { label: 'Reports', icon: 'fas fa-chart-bar', action: 'reports', color: '#FFEAA7' }\n      );\n    }\n\n    if (this.userProfile.role === 'super_admin' || this.userProfile.role === 'admin') {\n      baseActions.push(\n        { label: 'Admin Panel', icon: 'fas fa-shield-alt', action: 'admin', color: '#FF6B6B' }\n      );\n    }\n\n    return baseActions;\n  }\n\n  getProfileStats(): ProfileStats[] {\n    // This would be populated based on role and real data\n    // For now, returning mock data based on role\n    if (!this.userProfile?.role) return [];\n\n    const role = this.userProfile.role;\n    \n    if (role.includes('sales')) {\n      return [\n        { label: 'Sales This Month', value: '₹2.4M', icon: 'fas fa-rupee-sign', color: '#45B7D1', trend: 'up', trendValue: '+12%' },\n        { label: 'Deals Closed', value: 47, icon: 'fas fa-handshake', color: '#96CEB4', trend: 'up', trendValue: '+8%' },\n        { label: 'Target Achievement', value: '94%', icon: 'fas fa-target', color: '#FFEAA7', trend: 'stable' }\n      ];\n    }\n\n    if (role.includes('marketing')) {\n      return [\n        { label: 'Campaign Reach', value: '1.2M', icon: 'fas fa-eye', color: '#F38BA8', trend: 'up', trendValue: '+15%' },\n        { label: 'Engagement Rate', value: '4.8%', icon: 'fas fa-heart', color: '#DDA0DD', trend: 'up', trendValue: '+0.3%' },\n        { label: 'Conversions', value: 2847, icon: 'fas fa-exchange-alt', color: '#FFB6C1', trend: 'down', trendValue: '-2%' }\n      ];\n    }\n\n    // Default stats for other roles\n    return [\n      { label: 'Tasks Completed', value: 156, icon: 'fas fa-check-circle', color: '#6BCF7F', trend: 'up', trendValue: '+5%' },\n      { label: 'Projects Active', value: 8, icon: 'fas fa-project-diagram', color: '#4ECDC4', trend: 'stable' }\n    ];\n  }\n\n  handleAction(action: string): void {\n    switch (action) {\n      case 'edit':\n        this.router.navigate(['/profile/edit']);\n        break;\n      case 'settings':\n        this.router.navigate(['/profile/settings']);\n        break;\n      case 'team':\n        this.router.navigate(['/team/management']);\n        break;\n      case 'reports':\n        this.router.navigate(['/reports']);\n        break;\n      case 'admin':\n        this.router.navigate(['/admin']);\n        break;\n    }\n  }\n\n  getLastActiveText(): string {\n    if (!this.userProfile?.lastActive) return 'Unknown';\n    \n    const now = new Date();\n    const lastActive = new Date(this.userProfile.lastActive);\n    const diffMs = now.getTime() - lastActive.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    \n    if (diffMins < 1) return 'Just now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;\n    return `${Math.floor(diffMins / 1440)}d ago`;\n  }\n\n  getTrendIcon(trend: string): string {\n    switch (trend) {\n      case 'up': return 'fas fa-arrow-up';\n      case 'down': return 'fas fa-arrow-down';\n      default: return 'fas fa-minus';\n    }\n  }\n\n  // Mock data methods - these would be replaced with real data services\n  getTeamSize(): number { return 12; }\n  getDepartmentCount(): number { return 3; }\n  getStrategicInitiatives(): any[] { return []; }\n  getTeamMembers(): any[] { return []; }\n  getDepartmentGoals(): any[] { return []; }\n  getPersonalMetrics(): any[] { return []; }\n  getRecentAchievements(): any[] { return []; }\n  getSystemMetrics(): any[] { return []; }\n  getRecentActivities(): any[] { return []; }\n  getProgressColor(progress: number): string { return progress > 75 ? '#6BCF7F' : progress > 50 ? '#FFEAA7' : '#FF6B6B'; }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,QAAmB,MAAM;;;;;;;;IA+C7BC,EAAA,CAAAC,cAAA,iBAKyB;IADvBD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,SAAA,CAAAQ,MAAA,CAA2B;IAAA,EAAC;IAErCZ,EAAA,CAAAa,SAAA,QAA6B;IAC/Bb,EAAA,CAAAc,YAAA,EAAS;;;;IAJPd,EAAA,CAAAe,WAAA,eAAAX,SAAA,CAAAY,KAAA,CAAiC;IAEjChB,EAAA,CAAAiB,UAAA,UAAAb,SAAA,CAAAc,KAAA,CAAsB;IACnBlB,EAAA,CAAAmB,SAAA,EAAqB;IAArBnB,EAAA,CAAAoB,UAAA,CAAAhB,SAAA,CAAAiB,IAAA,CAAqB;;;;;IAWxBrB,EAAA,CAAAa,SAAA,cAAkE;;;;;IAShEb,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAa,SAAA,YAAmC;IACrCb,EAAA,CAAAc,YAAA,EAAM;;;;;IAWRd,EAAA,CAAAC,cAAA,YAAgD;IAAAD,EAAA,CAAAsB,MAAA,GAAsB;IAAAtB,EAAA,CAAAc,YAAA,EAAI;;;;IAA1Bd,EAAA,CAAAmB,SAAA,EAAsB;IAAtBnB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAgB,WAAA,kBAAAhB,MAAA,CAAAgB,WAAA,CAAAC,GAAA,CAAsB;;;;;IAGpEzB,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAa,SAAA,YAAqC;IACrCb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAsB,MAAA,GAA2B;IACnCtB,EADmC,CAAAc,YAAA,EAAO,EACpC;;;;IADEd,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAgB,WAAA,kBAAAhB,MAAA,CAAAgB,WAAA,CAAAE,QAAA,CAA2B;;;;;IAEnC1B,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAa,SAAA,YAAmC;IACnCb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAsB,MAAA,GAAoD;;IAC5DtB,EAD4D,CAAAc,YAAA,EAAO,EAC7D;;;;IADEd,EAAA,CAAAmB,SAAA,GAAoD;IAApDnB,EAAA,CAAA2B,kBAAA,YAAA3B,EAAA,CAAA4B,WAAA,OAAApB,MAAA,CAAAgB,WAAA,kBAAAhB,MAAA,CAAAgB,WAAA,CAAAK,QAAA,kBAAoD;;;;;IAyB9D7B,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAa,SAAA,QAA0C;IAC1Cb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAsB,MAAA,GAAqB;IAC7BtB,EAD6B,CAAAc,YAAA,EAAO,EAC9B;;;;;IAHqCd,EAAA,CAAAoB,UAAA,YAAAU,OAAA,CAAAC,KAAA,CAA+B;IACrE/B,EAAA,CAAAmB,SAAA,EAAkC;IAAlCnB,EAAA,CAAAoB,UAAA,CAAAZ,MAAA,CAAAwB,YAAA,CAAAF,OAAA,CAAAC,KAAA,EAAkC;IAC/B/B,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAuB,iBAAA,CAAAO,OAAA,CAAAG,UAAA,CAAqB;;;;;IAR/BjC,EAJF,CAAAC,cAAA,cAGyC,cACW;IAChDD,EAAA,CAAAa,SAAA,QAA2B;IAC7Bb,EAAA,CAAAc,YAAA,EAAM;IAEJd,EADF,CAAAC,cAAA,cAA0B,cACA;IAAAD,EAAA,CAAAsB,MAAA,GAAgB;IAAAtB,EAAA,CAAAc,YAAA,EAAM;IAC9Cd,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAsB,MAAA,GAAgB;IAAAtB,EAAA,CAAAc,YAAA,EAAM;IAC9Cd,EAAA,CAAAkC,UAAA,IAAAC,mDAAA,kBAA2E;IAK/EnC,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAZJd,EAAA,CAAAe,WAAA,sBAAAe,OAAA,CAAAd,KAAA,CAAsC;IACfhB,EAAA,CAAAmB,SAAA,EAA0B;IAA1BnB,EAAA,CAAAe,WAAA,UAAAe,OAAA,CAAAd,KAAA,CAA0B;IAC5ChB,EAAA,CAAAmB,SAAA,EAAmB;IAAnBnB,EAAA,CAAAoB,UAAA,CAAAU,OAAA,CAAAT,IAAA,CAAmB;IAGErB,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAuB,iBAAA,CAAAO,OAAA,CAAAM,KAAA,CAAgB;IAChBpC,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAuB,iBAAA,CAAAO,OAAA,CAAAZ,KAAA,CAAgB;IACflB,EAAA,CAAAmB,SAAA,EAAgB;IAAhBnB,EAAA,CAAAiB,UAAA,SAAAa,OAAA,CAAAC,KAAA,CAAgB;;;;;IAX/C/B,EADF,CAAAC,cAAA,cAAgE,cACtC;IACtBD,EAAA,CAAAkC,UAAA,IAAAG,6CAAA,kBAGyC;IAc7CrC,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAhBiBd,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAA8B,eAAA,GAAoB;;;;;IAuCjCtC,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAa,SAAA,cAAiE;IACjEb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAsB,MAAA,GAAqB;IAC7BtB,EAD6B,CAAAc,YAAA,EAAO,EAC9B;;;;IAF2Bd,EAAA,CAAAmB,SAAA,EAA2B;IAA3BnB,EAAA,CAAAoB,UAAA,CAAAmB,aAAA,CAAAC,MAAA,CAA2B;IACpDxC,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAuB,iBAAA,CAAAgB,aAAA,CAAAE,IAAA,CAAqB;;;;;IAjB/BzC,EAHN,CAAAC,cAAA,cAAoE,cACtC,cACE,SACtB;IAAAD,EAAA,CAAAsB,MAAA,0BAAmB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IAGxBd,EAFJ,CAAAC,cAAA,cAAgC,cACV,eACS;IAAAD,EAAA,CAAAsB,MAAA,GAAmB;IAAAtB,EAAA,CAAAc,YAAA,EAAO;IACrDd,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAsB,MAAA,oBAAY;IACzCtB,EADyC,CAAAc,YAAA,EAAO,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAoB,gBACS;IAAAD,EAAA,CAAAsB,MAAA,IAA0B;IAAAtB,EAAA,CAAAc,YAAA,EAAO;IAC5Dd,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAsB,MAAA,mBAAW;IAG5CtB,EAH4C,CAAAc,YAAA,EAAO,EACzC,EACF,EACF;IAEJd,EADF,CAAAC,cAAA,eAA4B,UACtB;IAAAD,EAAA,CAAAsB,MAAA,6BAAqB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IAC9Bd,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAkC,UAAA,KAAAQ,8CAAA,kBAAkF;IAO1F1C,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;;;;IAnB+Bd,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAmC,WAAA,GAAmB;IAInB3C,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAoC,kBAAA,GAA0B;IAQH5C,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAqC,uBAAA,GAA4B;;;;;IAiB9E7C,EADF,CAAAC,cAAA,cAAsE,cAC3C;IACvBD,EAAA,CAAAa,SAAA,cAAqE;IACrEb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAsB,MAAA,GAAiB;IACzBtB,EADyB,CAAAc,YAAA,EAAO,EAC1B;IACNd,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAa,SAAA,cAAiE;IAErEb,EADE,CAAAc,YAAA,EAAM,EACF;;;;IANGd,EAAA,CAAAmB,SAAA,GAAqB;IAACnB,EAAtB,CAAAiB,UAAA,QAAA6B,SAAA,CAAAC,MAAA,EAAA/C,EAAA,CAAAgD,aAAA,CAAqB,QAAAF,SAAA,CAAAL,IAAA,CAAoB;IACxCzC,EAAA,CAAAmB,SAAA,GAAiB;IAAjBnB,EAAA,CAAAuB,iBAAA,CAAAuB,SAAA,CAAAL,IAAA,CAAiB;IAGDzC,EAAA,CAAAmB,SAAA,GAAoC;IAApCnB,EAAA,CAAAe,WAAA,UAAA+B,SAAA,CAAAG,WAAA,MAAoC;;;;;IAWxDjD,EAHN,CAAAC,cAAA,cAAiE,cACpC,cACyD,WAC1E;IAAAD,EAAA,CAAAsB,MAAA,GAAoB;IAE9BtB,EAF8B,CAAAc,YAAA,EAAO,EAC7B,EACF;IAEJd,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAsB,MAAA,GAAgB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAsB,MAAA,GAAsB;IAE7BtB,EAF6B,CAAAc,YAAA,EAAI,EACzB,EACF;;;;;IAR2Bd,EAAA,CAAAmB,SAAA,GAAoD;IAApDnB,EAAA,CAAAe,WAAA,eAAAP,MAAA,CAAA0C,gBAAA,CAAAC,OAAA,CAAAC,QAAA,EAAoD;IACzEpD,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAA2B,kBAAA,KAAAwB,OAAA,CAAAC,QAAA,MAAoB;IAIxBpD,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAuB,iBAAA,CAAA4B,OAAA,CAAAE,KAAA,CAAgB;IACjBrD,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAuB,iBAAA,CAAA4B,OAAA,CAAAG,WAAA,CAAsB;;;;;IAzB/BtD,EAHN,CAAAC,cAAA,cAAgE,cACpC,cACE,SACpB;IAAAD,EAAA,CAAAsB,MAAA,uBAAgB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,cAA+B;IAE7BD,EAAA,CAAAkC,UAAA,IAAAqB,6CAAA,kBAAsE;IAU1EvD,EADE,CAAAc,YAAA,EAAM,EACF;IAEJd,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAsB,MAAA,uBAAgB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAkC,UAAA,KAAAsB,8CAAA,mBAAiE;IAczExD,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;;;;IA5BmDd,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAiD,cAAA,GAAmB;IAc5BzD,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAkD,kBAAA,GAAuB;;;;;IAwB3D1D,EAFJ,CAAAC,cAAA,cAAuE,cACN,WACvD;IAAAD,EAAA,CAAAsB,MAAA,GAAkB;IAC1BtB,EAD0B,CAAAc,YAAA,EAAO,EAC3B;IACNd,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAsB,MAAA,GAAkB;IAC3BtB,EAD2B,CAAAc,YAAA,EAAQ,EAC7B;;;;IAJyBd,EAAA,CAAAmB,SAAA,EAAiC;IAAjCnB,EAAA,CAAAe,WAAA,eAAA4C,SAAA,CAAA3C,KAAA,CAAiC;IACtDhB,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAuB,iBAAA,CAAAoC,SAAA,CAAAvB,KAAA,CAAkB;IAEnBpC,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAuB,iBAAA,CAAAoC,SAAA,CAAAzC,KAAA,CAAkB;;;;;IAQzBlB,EADF,CAAAC,cAAA,cAAkF,cACX;IACnED,EAAA,CAAAa,SAAA,QAAkC;IACpCb,EAAA,CAAAc,YAAA,EAAM;IAEJd,EADF,CAAAC,cAAA,cAAiC,SAC3B;IAAAD,EAAA,CAAAsB,MAAA,GAAuB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IAChCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAsB,MAAA,GAA6B;IAAAtB,EAAA,CAAAc,YAAA,EAAI;IACpCd,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAsB,MAAA,GAAqC;;IAExEtB,EAFwE,CAAAc,YAAA,EAAO,EACvE,EACF;;;;IAR0Bd,EAAA,CAAAmB,SAAA,EAAsC;IAAtCnB,EAAA,CAAAe,WAAA,eAAA6C,cAAA,CAAA5C,KAAA,CAAsC;IAC/DhB,EAAA,CAAAmB,SAAA,EAA0B;IAA1BnB,EAAA,CAAAoB,UAAA,CAAAwC,cAAA,CAAAvC,IAAA,CAA0B;IAGzBrB,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAuB,iBAAA,CAAAqC,cAAA,CAAAP,KAAA,CAAuB;IACxBrD,EAAA,CAAAmB,SAAA,GAA6B;IAA7BnB,EAAA,CAAAuB,iBAAA,CAAAqC,cAAA,CAAAN,WAAA,CAA6B;IACDtD,EAAA,CAAAmB,SAAA,GAAqC;IAArCnB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAA4B,WAAA,QAAAgC,cAAA,CAAAC,IAAA,WAAqC;;;;;IApB1E7D,EAHN,CAAAC,cAAA,cAAsE,cACvC,cACE,SACvB;IAAAD,EAAA,CAAAsB,MAAA,qBAAc;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IACvBd,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAkC,UAAA,IAAA4B,6CAAA,kBAAuE;IAO3E9D,EADE,CAAAc,YAAA,EAAM,EACF;IAEJd,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAsB,MAAA,0BAAmB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IAC5Bd,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAkC,UAAA,KAAA6B,8CAAA,oBAAkF;IAa1F/D,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;;;;IAxBgDd,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAwD,kBAAA,GAAuB;IAWfhE,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAyD,qBAAA,GAA0B;;;;;IAsB9EjE,EADF,CAAAC,cAAA,cAAqE,cACb;IACpDD,EAAA,CAAAa,SAAA,QAA6B;IAC/Bb,EAAA,CAAAc,YAAA,EAAM;IAEJd,EADF,CAAAC,cAAA,cAAyB,eACI;IAAAD,EAAA,CAAAsB,MAAA,GAAkB;IAAAtB,EAAA,CAAAc,YAAA,EAAO;IACpDd,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAsB,MAAA,GAAkB;IAEjDtB,EAFiD,CAAAc,YAAA,EAAO,EAChD,EACF;;;;IAPqBd,EAAA,CAAAmB,SAAA,EAA4B;IAA5BnB,EAAA,CAAAe,WAAA,UAAAmD,UAAA,CAAAlD,KAAA,CAA4B;IAChDhB,EAAA,CAAAmB,SAAA,EAAqB;IAArBnB,EAAA,CAAAoB,UAAA,CAAA8C,UAAA,CAAA7C,IAAA,CAAqB;IAGGrB,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAuB,iBAAA,CAAA2C,UAAA,CAAA9B,KAAA,CAAkB;IAClBpC,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAuB,iBAAA,CAAA2C,UAAA,CAAAhD,KAAA,CAAkB;;;;;IAS/ClB,EADF,CAAAC,cAAA,cAA0E,cAC7C;IAAAD,EAAA,CAAAsB,MAAA,GAAkC;;IAAAtB,EAAA,CAAAc,YAAA,EAAM;IAEjEd,EADF,CAAAC,cAAA,cAA8B,gBACA;IAAAD,EAAA,CAAAsB,MAAA,GAAmB;IAAAtB,EAAA,CAAAc,YAAA,EAAO;IACtDd,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAsB,MAAA,GAAqB;IAEvDtB,EAFuD,CAAAc,YAAA,EAAO,EACtD,EACF;;;;IALuBd,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAA4B,WAAA,OAAAuC,YAAA,CAAAC,IAAA,WAAkC;IAE/BpE,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAuB,iBAAA,CAAA4C,YAAA,CAAAE,IAAA,CAAmB;IACjBrE,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAuB,iBAAA,CAAA4C,YAAA,CAAAvD,MAAA,CAAqB;;;;;IApBzDZ,EAHN,CAAAC,cAAA,cAA4D,cAClC,cACE,SAClB;IAAAD,EAAA,CAAAsB,MAAA,sBAAe;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IACxBd,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAkC,UAAA,IAAAoC,6CAAA,kBAAqE;IAUzEtE,EADE,CAAAc,YAAA,EAAM,EACF;IAEJd,EADF,CAAAC,cAAA,cAAwB,SAClB;IAAAD,EAAA,CAAAsB,MAAA,wBAAiB;IAAAtB,EAAA,CAAAc,YAAA,EAAK;IAC1Bd,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAkC,UAAA,KAAAqC,8CAAA,kBAA0E;IAUlFvE,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;;;;IAxBgDd,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAgE,gBAAA,GAAqB;IAcnBxE,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAiE,mBAAA,GAAwB;;;AAgBxF,OAAM,MAAOC,uBAAuB;EAQlCC,YACUC,qBAA4C,EAC5CC,MAAc;IADd,KAAAD,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,MAAM,GAANA,MAAM;IATP,KAAArD,WAAW,GAAuB,IAAI;IAE/C,KAAAsD,UAAU,GAAsB,IAAI;IACpC,KAAAC,aAAa,GAAW,YAAY;IAE5B,KAAAC,QAAQ,GAAG,IAAIjF,OAAO,EAAQ;EAKnC;EAEHkF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzD,WAAW,EAAE0D,IAAI,EAAE;MAC1B,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACF,qBAAqB,CAACO,aAAa,CAAC,IAAI,CAAC3D,WAAW,CAAC0D,IAAI,CAAC;MACjF,IAAI,CAACH,aAAa,GAAG,IAAI,CAACD,UAAU,CAACC,aAAa;;EAEtD;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE,OAAO,mDAAmD;IAEhF,MAAM9D,KAAK,GAAG,IAAI,CAAC8D,UAAU,CAAC9D,KAAK;IACnC,OAAO,2BAA2BA,KAAK,UAAUA,KAAK,UAAU;EAClE;EAEAwE,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAChE,WAAW,EAAE0D,IAAI,EAAE,OAAO,EAAE;IAEtC,MAAMO,WAAW,GAAoB,CACnC;MAAEvE,KAAK,EAAE,cAAc;MAAEG,IAAI,EAAE,aAAa;MAAET,MAAM,EAAE,MAAM;MAAEI,KAAK,EAAE;IAAS,CAAE,EAChF;MAAEE,KAAK,EAAE,UAAU;MAAEG,IAAI,EAAE,YAAY;MAAET,MAAM,EAAE,UAAU;MAAEI,KAAK,EAAE;IAAS,CAAE,CAChF;IAED;IACA,IAAI,IAAI,CAAC4D,qBAAqB,CAACc,SAAS,CAAC,IAAI,CAAClE,WAAW,CAAC0D,IAAI,CAAC,EAAE;MAC/DO,WAAW,CAACE,IAAI,CACd;QAAEzE,KAAK,EAAE,iBAAiB;QAAEG,IAAI,EAAE,cAAc;QAAET,MAAM,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAS,CAAE,EACpF;QAAEE,KAAK,EAAE,SAAS;QAAEG,IAAI,EAAE,kBAAkB;QAAET,MAAM,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAS,CAAE,CACpF;;IAGH,IAAI,IAAI,CAACQ,WAAW,CAAC0D,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC1D,WAAW,CAAC0D,IAAI,KAAK,OAAO,EAAE;MAChFO,WAAW,CAACE,IAAI,CACd;QAAEzE,KAAK,EAAE,aAAa;QAAEG,IAAI,EAAE,mBAAmB;QAAET,MAAM,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAS,CAAE,CACvF;;IAGH,OAAOyE,WAAW;EACpB;EAEAnD,eAAeA,CAAA;IACb;IACA;IACA,IAAI,CAAC,IAAI,CAACd,WAAW,EAAE0D,IAAI,EAAE,OAAO,EAAE;IAEtC,MAAMA,IAAI,GAAG,IAAI,CAAC1D,WAAW,CAAC0D,IAAI;IAElC,IAAIA,IAAI,CAACU,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1B,OAAO,CACL;QAAE1E,KAAK,EAAE,kBAAkB;QAAEkB,KAAK,EAAE,OAAO;QAAEf,IAAI,EAAE,mBAAmB;QAAEL,KAAK,EAAE,SAAS;QAAEe,KAAK,EAAE,IAAI;QAAEE,UAAU,EAAE;MAAM,CAAE,EAC3H;QAAEf,KAAK,EAAE,cAAc;QAAEkB,KAAK,EAAE,EAAE;QAAEf,IAAI,EAAE,kBAAkB;QAAEL,KAAK,EAAE,SAAS;QAAEe,KAAK,EAAE,IAAI;QAAEE,UAAU,EAAE;MAAK,CAAE,EAChH;QAAEf,KAAK,EAAE,oBAAoB;QAAEkB,KAAK,EAAE,KAAK;QAAEf,IAAI,EAAE,eAAe;QAAEL,KAAK,EAAE,SAAS;QAAEe,KAAK,EAAE;MAAQ,CAAE,CACxG;;IAGH,IAAImD,IAAI,CAACU,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC9B,OAAO,CACL;QAAE1E,KAAK,EAAE,gBAAgB;QAAEkB,KAAK,EAAE,MAAM;QAAEf,IAAI,EAAE,YAAY;QAAEL,KAAK,EAAE,SAAS;QAAEe,KAAK,EAAE,IAAI;QAAEE,UAAU,EAAE;MAAM,CAAE,EACjH;QAAEf,KAAK,EAAE,iBAAiB;QAAEkB,KAAK,EAAE,MAAM;QAAEf,IAAI,EAAE,cAAc;QAAEL,KAAK,EAAE,SAAS;QAAEe,KAAK,EAAE,IAAI;QAAEE,UAAU,EAAE;MAAO,CAAE,EACrH;QAAEf,KAAK,EAAE,aAAa;QAAEkB,KAAK,EAAE,IAAI;QAAEf,IAAI,EAAE,qBAAqB;QAAEL,KAAK,EAAE,SAAS;QAAEe,KAAK,EAAE,MAAM;QAAEE,UAAU,EAAE;MAAK,CAAE,CACvH;;IAGH;IACA,OAAO,CACL;MAAEf,KAAK,EAAE,iBAAiB;MAAEkB,KAAK,EAAE,GAAG;MAAEf,IAAI,EAAE,qBAAqB;MAAEL,KAAK,EAAE,SAAS;MAAEe,KAAK,EAAE,IAAI;MAAEE,UAAU,EAAE;IAAK,CAAE,EACvH;MAAEf,KAAK,EAAE,iBAAiB;MAAEkB,KAAK,EAAE,CAAC;MAAEf,IAAI,EAAE,wBAAwB;MAAEL,KAAK,EAAE,SAAS;MAAEe,KAAK,EAAE;IAAQ,CAAE,CAC1G;EACH;EAEApB,YAAYA,CAACC,MAAc;IACzB,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,IAAI,CAACiE,MAAM,CAACgB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC;MACF,KAAK,UAAU;QACb,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;QAC3C;MACF,KAAK,MAAM;QACT,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC1C;MACF,KAAK,SAAS;QACZ,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;MACF,KAAK,OAAO;QACV,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAChC;;EAEN;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACtE,WAAW,EAAEuE,UAAU,EAAE,OAAO,SAAS;IAEnD,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMF,UAAU,GAAG,IAAIE,IAAI,CAAC,IAAI,CAACzE,WAAW,CAACuE,UAAU,CAAC;IACxD,MAAMG,MAAM,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAGJ,UAAU,CAACI,OAAO,EAAE;IACnD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAG,KAAK,CAAC;IAE3C,IAAIE,QAAQ,GAAG,CAAC,EAAE,OAAO,UAAU;IACnC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGA,QAAQ,OAAO;IAC5C,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,OAAO;IAC/D,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC,OAAO;EAC9C;EAEApE,YAAYA,CAACD,KAAa;IACxB,QAAQA,KAAK;MACX,KAAK,IAAI;QAAE,OAAO,iBAAiB;MACnC,KAAK,MAAM;QAAE,OAAO,mBAAmB;MACvC;QAAS,OAAO,cAAc;;EAElC;EAEA;EACAY,WAAWA,CAAA;IAAa,OAAO,EAAE;EAAE;EACnCC,kBAAkBA,CAAA;IAAa,OAAO,CAAC;EAAE;EACzCC,uBAAuBA,CAAA;IAAY,OAAO,EAAE;EAAE;EAC9CY,cAAcA,CAAA;IAAY,OAAO,EAAE;EAAE;EACrCC,kBAAkBA,CAAA;IAAY,OAAO,EAAE;EAAE;EACzCM,kBAAkBA,CAAA;IAAY,OAAO,EAAE;EAAE;EACzCC,qBAAqBA,CAAA;IAAY,OAAO,EAAE;EAAE;EAC5CO,gBAAgBA,CAAA;IAAY,OAAO,EAAE;EAAE;EACvCC,mBAAmBA,CAAA;IAAY,OAAO,EAAE;EAAE;EAC1CvB,gBAAgBA,CAACE,QAAgB;IAAY,OAAOA,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAGA,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS;EAAE;;;uBA3I5GsB,uBAAuB,EAAA1E,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBjC,uBAAuB;MAAAkC,SAAA;MAAAC,MAAA;QAAArF,WAAA;MAAA;MAAAsF,UAAA;MAAAC,QAAA,GAAA/G,EAAA,CAAAgH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1N1BtH,EAJN,CAAAC,cAAA,aAAqG,aAEvE,aACyC,aACpC;UAC3BD,EAAA,CAAAkC,UAAA,IAAAsF,yCAAA,oBAKyB;UAI7BxH,EADE,CAAAc,YAAA,EAAM,EACF;UAIFd,EAFJ,CAAAC,cAAA,aAA0B,aACI,aACI;UAC5BD,EAAA,CAAAa,SAAA,aAE4B;UAC5Bb,EAAA,CAAAkC,UAAA,IAAAuF,sCAAA,iBAA4D;UAC5DzH,EAAA,CAAAC,cAAA,eAA+D;UAC7DD,EAAA,CAAAa,SAAA,SAAkC;UAEtCb,EADE,CAAAc,YAAA,EAAM,EACF;UAIFd,EAFJ,CAAAC,cAAA,eAA6B,eACD,cACC;UAAAD,EAAA,CAAAsB,MAAA,IAA2B;UAAAtB,EAAA,CAAAc,YAAA,EAAK;UACzDd,EAAA,CAAAkC,UAAA,KAAAwF,uCAAA,kBAAgE;UAGlE1H,EAAA,CAAAc,YAAA,EAAM;UAENd,EAAA,CAAAC,cAAA,aAA4B;UAAAD,EAAA,CAAAsB,MAAA,IAA8C;UAAAtB,EAAA,CAAAc,YAAA,EAAI;UAE5Ed,EADF,CAAAC,cAAA,eAAuB,gBACsC;UACzDD,EAAA,CAAAsB,MAAA,IACF;UAAAtB,EAAA,CAAAc,YAAA,EAAO;UACPd,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAsB,MAAA,IAAwC;;UACnEtB,EADmE,CAAAc,YAAA,EAAO,EACpE;UAENd,EAAA,CAAAkC,UAAA,KAAAyF,qCAAA,gBAAgD;UAEhD3H,EAAA,CAAAC,cAAA,eAA0B;UAKxBD,EAJA,CAAAkC,UAAA,KAAA0F,uCAAA,kBAAqD,KAAAC,uCAAA,kBAIA;UAIrD7H,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAa,SAAA,aAA4B;UAC5Bb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAsB,MAAA,IAAqC;UAMvDtB,EANuD,CAAAc,YAAA,EAAO,EAC9C,EACF,EACF,EACF,EACF,EACF;UAGNd,EAAA,CAAAkC,UAAA,KAAA4F,uCAAA,kBAAgE;UAsBhE9H,EAAA,CAAAC,cAAA,eAA0B;UAmGxBD,EAjGA,CAAAkC,UAAA,KAAA6F,uCAAA,mBAAoE,KAAAC,uCAAA,mBA4BJ,KAAAC,uCAAA,mBAqCM,KAAAC,uCAAA,mBAgCV;UA+BhElI,EADE,CAAAc,YAAA,EAAM,EACF;;;;UAvNyBd,EAAA,CAAAmB,SAAA,GAAuC;UAAvCnB,EAAA,CAAAe,WAAA,eAAAwG,GAAA,CAAAhC,gBAAA,GAAuC;UAGzCvF,EAAA,CAAAmB,SAAA,GAAsB;UAAtBnB,EAAA,CAAAiB,UAAA,YAAAsG,GAAA,CAAA/B,iBAAA,GAAsB;UAapCxF,EAAA,CAAAmB,SAAA,GAAkE;UAClEnB,EADA,CAAAiB,UAAA,SAAAsG,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAAuB,MAAA,0CAAA/C,EAAA,CAAAgD,aAAA,CAAkE,QAAAuE,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAA2G,QAAA,CACrC;UAEHnI,EAAA,CAAAmB,SAAA,EAA2B;UAA3BnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAA4G,QAAA,CAA2B;UAClCpI,EAAA,CAAAmB,SAAA,EAAsC;UAAtCnB,EAAA,CAAAe,WAAA,eAAAwG,GAAA,CAAAzC,UAAA,kBAAAyC,GAAA,CAAAzC,UAAA,CAAA9D,KAAA,CAAsC;UACzDhB,EAAA,CAAAmB,SAAA,EAA0B;UAA1BnB,EAAA,CAAAoB,UAAA,CAAAmG,GAAA,CAAAzC,UAAA,kBAAAyC,GAAA,CAAAzC,UAAA,CAAAzD,IAAA,CAA0B;UAMJrB,EAAA,CAAAmB,SAAA,GAA2B;UAA3BnB,EAAA,CAAAuB,iBAAA,CAAAgG,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAA2G,QAAA,CAA2B;UACnBnI,EAAA,CAAAmB,SAAA,EAA6B;UAA7BnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAA6G,UAAA,CAA6B;UAKpCrI,EAAA,CAAAmB,SAAA,GAA8C;UAA9CnB,EAAA,CAAA2B,kBAAA,OAAA4F,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAA8G,QAAA,oBAA8C;UAE/CtI,EAAA,CAAAmB,SAAA,GAAiC;UAAjCnB,EAAA,CAAAe,WAAA,UAAAwG,GAAA,CAAAzC,UAAA,kBAAAyC,GAAA,CAAAzC,UAAA,CAAA9D,KAAA,CAAiC;UACxDhB,EAAA,CAAAmB,SAAA,EACF;UADEnB,EAAA,CAAA2B,kBAAA,MAAA4F,GAAA,CAAAzC,UAAA,kBAAAyC,GAAA,CAAAzC,UAAA,CAAAyD,WAAA,MACF;UACyBvI,EAAA,CAAAmB,SAAA,GAAwC;UAAxCnB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAwI,WAAA,SAAAjB,GAAA,CAAAzC,UAAA,kBAAAyC,GAAA,CAAAzC,UAAA,CAAA2D,UAAA,EAAwC;UAG3CzI,EAAA,CAAAmB,SAAA,GAAsB;UAAtBnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAAC,GAAA,CAAsB;UAGpBzB,EAAA,CAAAmB,SAAA,GAA2B;UAA3BnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAAE,QAAA,CAA2B;UAI3B1B,EAAA,CAAAmB,SAAA,EAA2B;UAA3BnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAA/F,WAAA,kBAAA+F,GAAA,CAAA/F,WAAA,CAAAK,QAAA,CAA2B;UAM3C7B,EAAA,CAAAmB,SAAA,GAAqC;UAArCnB,EAAA,CAAA2B,kBAAA,iBAAA4F,GAAA,CAAAzB,iBAAA,OAAqC;UAS3B9F,EAAA,CAAAmB,SAAA,EAAkC;UAAlCnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAAjF,eAAA,GAAAoG,MAAA,KAAkC;UAwBtD1I,EAAA,CAAAmB,SAAA,GAAmC;UAAnCnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAAxC,aAAA,iBAAmC;UA4BnC/E,EAAA,CAAAmB,SAAA,EAAiC;UAAjCnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAAxC,aAAA,eAAiC;UAqCjC/E,EAAA,CAAAmB,SAAA,EAAoC;UAApCnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAAxC,aAAA,kBAAoC;UAgCpC/E,EAAA,CAAAmB,SAAA,EAA+B;UAA/BnB,EAAA,CAAAiB,UAAA,SAAAsG,GAAA,CAAAxC,aAAA,aAA+B;;;qBA7LjCjF,YAAY,EAAA6I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,QAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}