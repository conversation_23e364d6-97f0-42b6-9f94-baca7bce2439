{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { OVERLAY_BACK_BUTTON_PRIORITY, shouldUseCloseWatcher } from './hardware-back-button-6107a37c.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { C as CoreDelegate } from './framework-delegate-ed4ba327.js';\nimport { c as componentOnReady, f as focusVisibleElement, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nlet lastOverlayIndex = 0;\nlet lastId = 0;\nconst activeAnimations = new WeakMap();\nconst createController = tagName => {\n  return {\n    create(options) {\n      return createOverlay(tagName, options);\n    },\n    dismiss(data, role, id) {\n      return dismissOverlay(document, data, role, tagName, id);\n    },\n    getTop() {\n      return _asyncToGenerator(function* () {\n        return getPresentedOverlay(document, tagName);\n      })();\n    }\n  };\n};\nconst alertController = /*@__PURE__*/createController('ion-alert');\nconst actionSheetController = /*@__PURE__*/createController('ion-action-sheet');\nconst loadingController = /*@__PURE__*/createController('ion-loading');\nconst modalController = /*@__PURE__*/createController('ion-modal');\nconst pickerController = /*@__PURE__*/createController('ion-picker');\nconst popoverController = /*@__PURE__*/createController('ion-popover');\nconst toastController = /*@__PURE__*/createController('ion-toast');\n/**\n * Prepares the overlay element to be presented.\n */\nconst prepareOverlay = el => {\n  if (typeof document !== 'undefined') {\n    /**\n     * Adds a single instance of event listeners for application behaviors:\n     *\n     * - Escape Key behavior to dismiss an overlay\n     * - Trapping focus within an overlay\n     * - Back button behavior to dismiss an overlay\n     *\n     * This only occurs when the first overlay is created.\n     */\n    connectListeners(document);\n  }\n  const overlayIndex = lastOverlayIndex++;\n  /**\n   * overlayIndex is used in the overlay components to set a zIndex.\n   * This ensures that the most recently presented overlay will be\n   * on top.\n   */\n  el.overlayIndex = overlayIndex;\n};\n/**\n * Assigns an incrementing id to an overlay element, that does not\n * already have an id assigned to it.\n *\n * Used to track unique instances of an overlay element.\n */\nconst setOverlayId = el => {\n  if (!el.hasAttribute('id')) {\n    el.id = `ion-overlay-${++lastId}`;\n  }\n  return el.id;\n};\nconst createOverlay = (tagName, opts) => {\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (typeof window !== 'undefined' && typeof window.customElements !== 'undefined') {\n    return window.customElements.whenDefined(tagName).then(() => {\n      const element = document.createElement(tagName);\n      element.classList.add('overlay-hidden');\n      /**\n       * Convert the passed in overlay options into props\n       * that get passed down into the new overlay.\n       */\n      Object.assign(element, Object.assign(Object.assign({}, opts), {\n        hasController: true\n      }));\n      // append the overlay element to the document body\n      getAppRoot(document).appendChild(element);\n      return new Promise(resolve => componentOnReady(element, resolve));\n    });\n  }\n  return Promise.resolve();\n};\n/**\n * This query string selects elements that\n * are eligible to receive focus. We select\n * interactive elements that meet the following\n * criteria:\n * 1. Element does not have a negative tabindex\n * 2. Element does not have `hidden`\n * 3. Element does not have `disabled` for non-Ionic components.\n * 4. Element does not have `disabled` or `disabled=\"true\"` for Ionic components.\n * Note: We need this distinction because `disabled=\"false\"` is\n * valid usage for the disabled property on ion-button.\n */\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^=\"-\"]):not([hidden]):not([disabled]), textarea:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), button:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), select:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable[disabled=\"false\"]:not([tabindex^=\"-\"]):not([hidden])';\nconst isOverlayHidden = overlay => overlay.classList.contains('overlay-hidden');\n/**\n * Focuses the first descendant in an overlay\n * that can receive focus. If none exists,\n * the entire overlay will be focused.\n */\nconst focusFirstDescendant = (ref, overlay) => {\n  const firstInput = ref.querySelector(focusableQueryString);\n  focusElementInOverlay(firstInput, overlay);\n};\n/**\n * Focuses the last descendant in an overlay\n * that can receive focus. If none exists,\n * the entire overlay will be focused.\n */\nconst focusLastDescendant = (ref, overlay) => {\n  const inputs = Array.from(ref.querySelectorAll(focusableQueryString));\n  const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n  focusElementInOverlay(lastInput, overlay);\n};\n/**\n * Focuses a particular element in an overlay. If the element\n * doesn't have anything focusable associated with it then\n * the overlay itself will be focused.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInOverlay = (hostToFocus, overlay) => {\n  let elementToFocus = hostToFocus;\n  const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n  }\n  if (elementToFocus) {\n    focusVisibleElement(elementToFocus);\n  } else {\n    // Focus overlay instead of letting focus escape\n    overlay.focus();\n  }\n};\n/**\n * Traps keyboard focus inside of overlay components.\n * Based on https://w3c.github.io/aria-practices/examples/dialog-modal/alertdialog.html\n * This includes the following components: Action Sheet, Alert, Loading, Modal,\n * Picker, and Popover.\n * Should NOT include: Toast\n */\nconst trapKeyboardFocus = (ev, doc) => {\n  const lastOverlay = getPresentedOverlay(doc, 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover');\n  const target = ev.target;\n  /**\n   * If no active overlay, ignore this event.\n   *\n   * If this component uses the shadow dom,\n   * this global listener is pointless\n   * since it will not catch the focus\n   * traps as they are inside the shadow root.\n   * We need to add a listener to the shadow root\n   * itself to ensure the focus trap works.\n   */\n  if (!lastOverlay || !target) {\n    return;\n  }\n  /**\n   * If the ion-disable-focus-trap class\n   * is present on an overlay, then this component\n   * instance has opted out of focus trapping.\n   * An example of this is when the sheet modal\n   * has a backdrop that is disabled. The content\n   * behind the sheet should be focusable until\n   * the backdrop is enabled.\n   */\n  if (lastOverlay.classList.contains('ion-disable-focus-trap')) {\n    return;\n  }\n  const trapScopedFocus = () => {\n    /**\n     * If we are focusing the overlay, clear\n     * the last focused element so that hitting\n     * tab activates the first focusable element\n     * in the overlay wrapper.\n     */\n    if (lastOverlay === target) {\n      lastOverlay.lastFocus = undefined;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n      /**\n       * Otherwise, we must be focusing an element\n       * inside of the overlay. The two possible options\n       * here are an input/button/etc or the ion-focus-trap\n       * element. The focus trap element is used to prevent\n       * the keyboard focus from leaving the overlay when\n       * using Tab or screen assistants.\n       */\n    } else {\n      /**\n       * We do not want to focus the traps, so get the overlay\n       * wrapper element as the traps live outside of the wrapper.\n       */\n      const overlayRoot = getElementRoot(lastOverlay);\n      if (!overlayRoot.contains(target)) {\n        return;\n      }\n      const overlayWrapper = overlayRoot.querySelector('.ion-overlay-wrapper');\n      if (!overlayWrapper) {\n        return;\n      }\n      /**\n       * If the target is inside the wrapper, let the browser\n       * focus as normal and keep a log of the last focused element.\n       * Additionally, if the backdrop was tapped we should not\n       * move focus back inside the wrapper as that could cause\n       * an interactive elements focus state to activate.\n       */\n      if (overlayWrapper.contains(target) || target === overlayRoot.querySelector('ion-backdrop')) {\n        lastOverlay.lastFocus = target;\n      } else {\n        /**\n         * Otherwise, we must have focused one of the focus traps.\n         * We need to wrap the focus to either the first element\n         * or the last element.\n         */\n        /**\n         * Once we call `focusFirstDescendant` and focus the first\n         * descendant, another focus event will fire which will\n         * cause `lastOverlay.lastFocus` to be updated before\n         * we can run the code after that. We will cache the value\n         * here to avoid that.\n         */\n        const lastFocus = lastOverlay.lastFocus;\n        // Focus the first element in the overlay wrapper\n        focusFirstDescendant(overlayWrapper, lastOverlay);\n        /**\n         * If the cached last focused element is the\n         * same as the active element, then we need\n         * to wrap focus to the last descendant. This happens\n         * when the first descendant is focused, and the user\n         * presses Shift + Tab. The previous line will focus\n         * the same descendant again (the first one), causing\n         * last focus to equal the active element.\n         */\n        if (lastFocus === doc.activeElement) {\n          focusLastDescendant(overlayWrapper, lastOverlay);\n        }\n        lastOverlay.lastFocus = doc.activeElement;\n      }\n    }\n  };\n  const trapShadowFocus = () => {\n    /**\n     * If the target is inside the wrapper, let the browser\n     * focus as normal and keep a log of the last focused element.\n     */\n    if (lastOverlay.contains(target)) {\n      lastOverlay.lastFocus = target;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n    } else {\n      /**\n       * Otherwise, we are about to have focus\n       * go out of the overlay. We need to wrap\n       * the focus to either the first element\n       * or the last element.\n       */\n      /**\n       * Once we call `focusFirstDescendant` and focus the first\n       * descendant, another focus event will fire which will\n       * cause `lastOverlay.lastFocus` to be updated before\n       * we can run the code after that. We will cache the value\n       * here to avoid that.\n       */\n      const lastFocus = lastOverlay.lastFocus;\n      // Focus the first element in the overlay wrapper\n      focusFirstDescendant(lastOverlay, lastOverlay);\n      /**\n       * If the cached last focused element is the\n       * same as the active element, then we need\n       * to wrap focus to the last descendant. This happens\n       * when the first descendant is focused, and the user\n       * presses Shift + Tab. The previous line will focus\n       * the same descendant again (the first one), causing\n       * last focus to equal the active element.\n       */\n      if (lastFocus === doc.activeElement) {\n        focusLastDescendant(lastOverlay, lastOverlay);\n      }\n      lastOverlay.lastFocus = doc.activeElement;\n    }\n  };\n  if (lastOverlay.shadowRoot) {\n    trapShadowFocus();\n  } else {\n    trapScopedFocus();\n  }\n};\nconst connectListeners = doc => {\n  if (lastOverlayIndex === 0) {\n    lastOverlayIndex = 1;\n    doc.addEventListener('focus', ev => {\n      trapKeyboardFocus(ev, doc);\n    }, true);\n    // handle back-button click\n    doc.addEventListener('ionBackButton', ev => {\n      const lastOverlay = getPresentedOverlay(doc);\n      if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n        ev.detail.register(OVERLAY_BACK_BUTTON_PRIORITY, () => {\n          /**\n           * Do not return this promise otherwise\n           * the hardware back button utility will\n           * be blocked until the overlay dismisses.\n           * This is important for a modal with canDismiss.\n           * If the application presents a confirmation alert\n           * in the \"canDismiss\" callback, then it will be impossible\n           * to use the hardware back button to dismiss the alert\n           * dialog because the hardware back button utility\n           * is blocked on waiting for the modal to dismiss.\n           */\n          lastOverlay.dismiss(undefined, BACKDROP);\n        });\n      }\n    });\n    /**\n     * Handle ESC to close overlay.\n     * CloseWatcher also handles pressing the Esc\n     * key, so if a browser supports CloseWatcher then\n     * this behavior will be handled via the ionBackButton\n     * event.\n     */\n    if (!shouldUseCloseWatcher()) {\n      doc.addEventListener('keydown', ev => {\n        if (ev.key === 'Escape') {\n          const lastOverlay = getPresentedOverlay(doc);\n          if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n            lastOverlay.dismiss(undefined, BACKDROP);\n          }\n        }\n      });\n    }\n  }\n};\nconst dismissOverlay = (doc, data, role, overlayTag, id) => {\n  const overlay = getPresentedOverlay(doc, overlayTag, id);\n  if (!overlay) {\n    return Promise.reject('overlay does not exist');\n  }\n  return overlay.dismiss(data, role);\n};\n/**\n * Returns a list of all overlays in the DOM even if they are not presented.\n */\nconst getOverlays = (doc, selector) => {\n  if (selector === undefined) {\n    selector = 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover,ion-toast';\n  }\n  return Array.from(doc.querySelectorAll(selector)).filter(c => c.overlayIndex > 0);\n};\n/**\n * Returns a list of all presented overlays.\n * Inline overlays can exist in the DOM but not be presented,\n * so there are times when we want to exclude those.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n */\nconst getPresentedOverlays = (doc, overlayTag) => {\n  return getOverlays(doc, overlayTag).filter(o => !isOverlayHidden(o));\n};\n/**\n * Returns a presented overlay element.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n * @param id The unique identifier for the overlay instance.\n * @returns The overlay element or `undefined` if no overlay element is found.\n */\nconst getPresentedOverlay = (doc, overlayTag, id) => {\n  const overlays = getPresentedOverlays(doc, overlayTag);\n  return id === undefined ? overlays[overlays.length - 1] : overlays.find(o => o.id === id);\n};\n/**\n * When an overlay is presented, the main\n * focus is the overlay not the page content.\n * We need to remove the page content from the\n * accessibility tree otherwise when\n * users use \"read screen from top\" gestures with\n * TalkBack and VoiceOver, the screen reader will begin\n * to read the content underneath the overlay.\n *\n * We need a container where all page components\n * exist that is separate from where the overlays\n * are added in the DOM. For most apps, this element\n * is the top most ion-router-outlet. In the event\n * that devs are not using a router,\n * they will need to add the \"ion-view-container-root\"\n * id to the element that contains all of their views.\n *\n * TODO: If Framework supports having multiple top\n * level router outlets we would need to update this.\n * Example: One outlet for side menu and one outlet\n * for main content.\n */\nconst setRootAriaHidden = (hidden = false) => {\n  const root = getAppRoot(document);\n  const viewContainer = root.querySelector('ion-router-outlet, ion-nav, #ion-view-container-root');\n  if (!viewContainer) {\n    return;\n  }\n  if (hidden) {\n    viewContainer.setAttribute('aria-hidden', 'true');\n  } else {\n    viewContainer.removeAttribute('aria-hidden');\n  }\n};\nconst present = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (overlay, name, iosEnterAnimation, mdEnterAnimation, opts) {\n    var _a, _b;\n    if (overlay.presented) {\n      return;\n    }\n    setRootAriaHidden(true);\n    hideOverlaysFromScreenReaders(overlay.el);\n    overlay.presented = true;\n    overlay.willPresent.emit();\n    (_a = overlay.willPresentShorthand) === null || _a === void 0 ? void 0 : _a.emit();\n    const mode = getIonMode(overlay);\n    // get the user's animation fn if one was provided\n    const animationBuilder = overlay.enterAnimation ? overlay.enterAnimation : config.get(name, mode === 'ios' ? iosEnterAnimation : mdEnterAnimation);\n    const completed = yield overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n    if (completed) {\n      overlay.didPresent.emit();\n      (_b = overlay.didPresentShorthand) === null || _b === void 0 ? void 0 : _b.emit();\n    }\n    /**\n     * When an overlay that steals focus\n     * is dismissed, focus should be returned\n     * to the element that was focused\n     * prior to the overlay opening. Toast\n     * does not steal focus and is excluded\n     * from returning focus as a result.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n      restoreElementFocus(overlay.el);\n    }\n    /**\n     * If the focused element is already\n     * inside the overlay component then\n     * focus should not be moved from that\n     * to the overlay container.\n     */\n    if (overlay.keyboardClose && (document.activeElement === null || !overlay.el.contains(document.activeElement))) {\n      overlay.el.focus();\n    }\n    /**\n     * If this overlay was previously dismissed without being\n     * the topmost one (such as by manually calling dismiss()),\n     * it would still have aria-hidden on being presented again.\n     * Removing it here ensures the overlay is visible to screen\n     * readers.\n     */\n    overlay.el.removeAttribute('aria-hidden');\n  });\n  return function present(_x, _x2, _x3, _x4, _x5) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * When an overlay component is dismissed,\n * focus should be returned to the element\n * that presented the overlay. Otherwise\n * focus will be set on the body which\n * means that people using screen readers\n * or tabbing will need to re-navigate\n * to where they were before they\n * opened the overlay.\n */\nconst restoreElementFocus = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (overlayEl) {\n    let previousElement = document.activeElement;\n    if (!previousElement) {\n      return;\n    }\n    const shadowRoot = previousElement === null || previousElement === void 0 ? void 0 : previousElement.shadowRoot;\n    if (shadowRoot) {\n      // If there are no inner focusable elements, just focus the host element.\n      previousElement = shadowRoot.querySelector(focusableQueryString) || previousElement;\n    }\n    yield overlayEl.onDidDismiss();\n    /**\n     * After onDidDismiss, the overlay loses focus\n     * because it is removed from the document\n     *\n     * > An element will also lose focus [...]\n     * > if the element is removed from the document)\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Element/blur_event\n     *\n     * Additionally, `document.activeElement` returns:\n     *\n     * > The Element which currently has focus,\n     * > `<body>` or null if there is\n     * > no focused element.\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Document/activeElement#value\n     *\n     * However, if the user has already focused\n     * an element sometime between onWillDismiss\n     * and onDidDismiss (for example, focusing a\n     * text box after tapping a button in an\n     * action sheet) then don't restore focus to\n     * previous element\n     */\n    if (document.activeElement === null || document.activeElement === document.body) {\n      previousElement.focus();\n    }\n  });\n  return function restoreElementFocus(_x6) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nconst dismiss = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (overlay, data, role, name, iosLeaveAnimation, mdLeaveAnimation, opts) {\n    var _a, _b;\n    if (!overlay.presented) {\n      return false;\n    }\n    /**\n     * If this is the last visible overlay then\n     * we want to re-add the root to the accessibility tree.\n     */\n    if (doc !== undefined && getPresentedOverlays(doc).length === 1) {\n      setRootAriaHidden(false);\n    }\n    overlay.presented = false;\n    try {\n      // Overlay contents should not be clickable during dismiss\n      overlay.el.style.setProperty('pointer-events', 'none');\n      overlay.willDismiss.emit({\n        data,\n        role\n      });\n      (_a = overlay.willDismissShorthand) === null || _a === void 0 ? void 0 : _a.emit({\n        data,\n        role\n      });\n      const mode = getIonMode(overlay);\n      const animationBuilder = overlay.leaveAnimation ? overlay.leaveAnimation : config.get(name, mode === 'ios' ? iosLeaveAnimation : mdLeaveAnimation);\n      // If dismissed via gesture, no need to play leaving animation again\n      if (role !== GESTURE) {\n        yield overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n      }\n      overlay.didDismiss.emit({\n        data,\n        role\n      });\n      (_b = overlay.didDismissShorthand) === null || _b === void 0 ? void 0 : _b.emit({\n        data,\n        role\n      });\n      // Get a reference to all animations currently assigned to this overlay\n      // Then tear them down to return the overlay to its initial visual state\n      const animations = activeAnimations.get(overlay) || [];\n      animations.forEach(ani => ani.destroy());\n      activeAnimations.delete(overlay);\n      /**\n       * Make overlay hidden again in case it is being reused.\n       * We can safely remove pointer-events: none as\n       * overlay-hidden will set display: none.\n       */\n      overlay.el.classList.add('overlay-hidden');\n      overlay.el.style.removeProperty('pointer-events');\n      /**\n       * Clear any focus trapping references\n       * when the overlay is dismissed.\n       */\n      if (overlay.el.lastFocus !== undefined) {\n        overlay.el.lastFocus = undefined;\n      }\n    } catch (err) {\n      console.error(err);\n    }\n    overlay.el.remove();\n    revealOverlaysToScreenReaders();\n    return true;\n  });\n  return function dismiss(_x7, _x8, _x9, _x0, _x1, _x10, _x11) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nconst getAppRoot = doc => {\n  return doc.querySelector('ion-app') || doc.body;\n};\nconst overlayAnimation = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(function* (overlay, animationBuilder, baseEl, opts) {\n    // Make overlay visible in case it's hidden\n    baseEl.classList.remove('overlay-hidden');\n    const aniRoot = overlay.el;\n    const animation = animationBuilder(aniRoot, opts);\n    if (!overlay.animated || !config.getBoolean('animated', true)) {\n      animation.duration(0);\n    }\n    if (overlay.keyboardClose) {\n      animation.beforeAddWrite(() => {\n        const activeElement = baseEl.ownerDocument.activeElement;\n        if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.matches('input,ion-input, ion-textarea')) {\n          activeElement.blur();\n        }\n      });\n    }\n    const activeAni = activeAnimations.get(overlay) || [];\n    activeAnimations.set(overlay, [...activeAni, animation]);\n    yield animation.play();\n    return true;\n  });\n  return function overlayAnimation(_x12, _x13, _x14, _x15) {\n    return _ref4.apply(this, arguments);\n  };\n}();\nconst eventMethod = (element, eventName) => {\n  let resolve;\n  const promise = new Promise(r => resolve = r);\n  onceEvent(element, eventName, event => {\n    resolve(event.detail);\n  });\n  return promise;\n};\nconst onceEvent = (element, eventName, callback) => {\n  const handler = ev => {\n    removeEventListener(element, eventName, handler);\n    callback(ev);\n  };\n  addEventListener(element, eventName, handler);\n};\nconst isCancel = role => {\n  return role === 'cancel' || role === BACKDROP;\n};\nconst defaultGate = h => h();\n/**\n * Calls a developer provided method while avoiding\n * Angular Zones. Since the handler is provided by\n * the developer, we should throw any errors\n * received so that developer-provided bug\n * tracking software can log it.\n */\nconst safeCall = (handler, arg) => {\n  if (typeof handler === 'function') {\n    const jmp = config.get('_zoneGate', defaultGate);\n    return jmp(() => {\n      try {\n        return handler(arg);\n      } catch (e) {\n        throw e;\n      }\n    });\n  }\n  return undefined;\n};\nconst BACKDROP = 'backdrop';\nconst GESTURE = 'gesture';\nconst OVERLAY_GESTURE_PRIORITY = 39;\n/**\n * Creates a delegate controller.\n *\n * Requires that the component has the following properties:\n * - `el: HTMLElement`\n * - `hasController: boolean`\n * - `delegate?: FrameworkDelegate`\n *\n * @param ref The component class instance.\n */\nconst createDelegateController = ref => {\n  let inline = false;\n  let workingDelegate;\n  const coreDelegate = CoreDelegate();\n  /**\n   * Determines whether or not an overlay is being used\n   * inline or via a controller/JS and returns the correct delegate.\n   * By default, subsequent calls to getDelegate will use\n   * a cached version of the delegate.\n   * This is useful for calling dismiss after present,\n   * so that the correct delegate is given.\n   * @param force `true` to force the non-cached version of the delegate.\n   * @returns The delegate to use and whether or not the overlay is inline.\n   */\n  const getDelegate = (force = false) => {\n    if (workingDelegate && !force) {\n      return {\n        delegate: workingDelegate,\n        inline\n      };\n    }\n    const {\n      el,\n      hasController,\n      delegate\n    } = ref;\n    /**\n     * If using overlay inline\n     * we potentially need to use the coreDelegate\n     * so that this works in vanilla JS apps.\n     * If a developer has presented this component\n     * via a controller, then we can assume\n     * the component is already in the\n     * correct place.\n     */\n    const parentEl = el.parentNode;\n    inline = parentEl !== null && !hasController;\n    workingDelegate = inline ? delegate || coreDelegate : delegate;\n    return {\n      inline,\n      delegate: workingDelegate\n    };\n  };\n  /**\n   * Attaches a component in the DOM. Teleports the component\n   * to the root of the app.\n   * @param component The component to optionally construct and append to the element.\n   */\n  const attachViewToDom = /*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(function* (component) {\n      const {\n        delegate\n      } = getDelegate(true);\n      if (delegate) {\n        return yield delegate.attachViewToDom(ref.el, component);\n      }\n      const {\n        hasController\n      } = ref;\n      if (hasController && component !== undefined) {\n        throw new Error('framework delegate is missing');\n      }\n      return null;\n    });\n    return function attachViewToDom(_x16) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  /**\n   * Moves a component back to its original location in the DOM.\n   */\n  const removeViewFromDom = () => {\n    const {\n      delegate\n    } = getDelegate();\n    if (delegate && ref.el !== undefined) {\n      delegate.removeViewFromDom(ref.el.parentElement, ref.el);\n    }\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\n/**\n * Constructs a trigger interaction for an overlay.\n * Presents an overlay when the trigger is clicked.\n *\n * Usage:\n * ```ts\n * triggerController = createTriggerController();\n * triggerController.addClickListener(el, trigger);\n * ```\n */\nconst createTriggerController = () => {\n  let destroyTriggerInteraction;\n  /**\n   * Removes the click listener from the trigger element.\n   */\n  const removeClickListener = () => {\n    if (destroyTriggerInteraction) {\n      destroyTriggerInteraction();\n      destroyTriggerInteraction = undefined;\n    }\n  };\n  /**\n   * Adds a click listener to the trigger element.\n   * Presents the overlay when the trigger is clicked.\n   * @param el The overlay element.\n   * @param trigger The ID of the element to add a click listener to.\n   */\n  const addClickListener = (el, trigger) => {\n    removeClickListener();\n    const triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n    if (!triggerEl) {\n      printIonWarning(`A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on an overlay component.`, el);\n      return;\n    }\n    const configureTriggerInteraction = (targetEl, overlayEl) => {\n      const openOverlay = () => {\n        overlayEl.present();\n      };\n      targetEl.addEventListener('click', openOverlay);\n      return () => {\n        targetEl.removeEventListener('click', openOverlay);\n      };\n    };\n    destroyTriggerInteraction = configureTriggerInteraction(triggerEl, el);\n  };\n  return {\n    addClickListener,\n    removeClickListener\n  };\n};\n/**\n * Ensure that underlying overlays have aria-hidden if necessary so that screen readers\n * cannot move focus to these elements. Note that we cannot rely on focus/focusin/focusout\n * events here because those events do not fire when the screen readers moves to a non-focusable\n * element such as text.\n * Without this logic screen readers would be able to move focus outside of the top focus-trapped overlay.\n *\n * @param newTopMostOverlay - The overlay that is being presented. Since the overlay has not been\n * fully presented yet at the time this function is called it will not be included in the getPresentedOverlays result.\n */\nconst hideOverlaysFromScreenReaders = newTopMostOverlay => {\n  var _a;\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const presentedOverlay = overlays[i];\n    const nextPresentedOverlay = (_a = overlays[i + 1]) !== null && _a !== void 0 ? _a : newTopMostOverlay;\n    /**\n     * If next overlay has aria-hidden then all remaining overlays will have it too.\n     * Or, if the next overlay is a Toast that does not have aria-hidden then current overlay\n     * should not have aria-hidden either so focus can remain in the current overlay.\n     */\n    if (nextPresentedOverlay.hasAttribute('aria-hidden') || nextPresentedOverlay.tagName !== 'ION-TOAST') {\n      presentedOverlay.setAttribute('aria-hidden', 'true');\n    }\n  }\n};\n/**\n * When dismissing an overlay we need to reveal the new top-most overlay to screen readers.\n * If the top-most overlay is a Toast we potentially need to reveal more overlays since\n * focus is never automatically moved to the Toast.\n */\nconst revealOverlaysToScreenReaders = () => {\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const currentOverlay = overlays[i];\n    /**\n     * If the current we are looking at is a Toast then we can remove aria-hidden.\n     * However, we potentially need to keep looking at the overlay stack because there\n     * could be more Toasts underneath. Additionally, we need to unhide the closest non-Toast\n     * overlay too so focus can move there since focus is never automatically moved to the Toast.\n     */\n    currentOverlay.removeAttribute('aria-hidden');\n    /**\n     * If we found a non-Toast element then we can just remove aria-hidden and stop searching entirely\n     * since this overlay should always receive focus. As a result, all underlying overlays should still\n     * be hidden from screen readers.\n     */\n    if (currentOverlay.tagName !== 'ION-TOAST') {\n      break;\n    }\n  }\n};\nexport { BACKDROP as B, GESTURE as G, OVERLAY_GESTURE_PRIORITY as O, alertController as a, actionSheetController as b, popoverController as c, createDelegateController as d, createTriggerController as e, present as f, dismiss as g, eventMethod as h, isCancel as i, prepareOverlay as j, setOverlayId as k, loadingController as l, modalController as m, focusFirstDescendant as n, getPresentedOverlay as o, pickerController as p, safeCall as s, toastController as t };", "map": {"version": 3, "names": ["d", "doc", "OVERLAY_BACK_BUTTON_PRIORITY", "shouldUseCloseWatcher", "b", "getIonMode", "c", "config", "C", "CoreDelegate", "componentOnReady", "f", "focusVisibleElement", "a", "addEventListener", "removeEventListener", "g", "getElementRoot", "p", "printIonWarning", "lastOverlayIndex", "lastId", "activeAnimations", "WeakMap", "createController", "tagName", "create", "options", "createOverlay", "dismiss", "data", "role", "id", "dismiss<PERSON><PERSON><PERSON>", "document", "getTop", "_asyncToGenerator", "getPresentedOverlay", "alertController", "actionSheetController", "loadingController", "modalController", "pickerController", "popoverController", "toastController", "prepareOverlay", "el", "connectListeners", "overlayIndex", "setOverlayId", "hasAttribute", "opts", "window", "customElements", "whenDefined", "then", "element", "createElement", "classList", "add", "Object", "assign", "hasController", "getAppRoot", "append<PERSON><PERSON><PERSON>", "Promise", "resolve", "focusableQueryString", "isOverlayHidden", "overlay", "contains", "focusFirstDescendant", "ref", "firstInput", "querySelector", "focusElementInOverlay", "focusLastDescendant", "inputs", "Array", "from", "querySelectorAll", "lastInput", "length", "hostToFocus", "elementToFocus", "shadowRoot", "focus", "trapKeyboardFocus", "ev", "lastOverlay", "target", "trapScopedFocus", "lastFocus", "undefined", "overlayRoot", "overlayWrapper", "activeElement", "trapShadowFocus", "<PERSON><PERSON><PERSON><PERSON>", "detail", "register", "BACKDROP", "key", "overlayTag", "reject", "getOverlays", "selector", "filter", "getPresentedOverlays", "o", "overlays", "find", "setRootAriaHidden", "hidden", "root", "viewContainer", "setAttribute", "removeAttribute", "present", "_ref", "name", "iosEnterAnimation", "mdEnterAnimation", "_a", "_b", "presented", "hideOverlaysFromScreenReaders", "willPresent", "emit", "willPresentShorthand", "mode", "animationBuilder", "enterAnimation", "get", "completed", "overlayAnimation", "didPresent", "didPresentShorthand", "restoreElementFocus", "keyboardClose", "_x", "_x2", "_x3", "_x4", "_x5", "apply", "arguments", "_ref2", "overlayEl", "previousElement", "onDid<PERSON><PERSON><PERSON>", "body", "_x6", "_ref3", "iosLeaveAnimation", "mdLeaveAnimation", "style", "setProperty", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leaveAnimation", "GESTURE", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animations", "for<PERSON>ach", "ani", "destroy", "delete", "removeProperty", "err", "console", "error", "remove", "revealOverlaysToScreenReaders", "_x7", "_x8", "_x9", "_x0", "_x1", "_x10", "_x11", "_ref4", "baseEl", "aniRoot", "animation", "animated", "getBoolean", "duration", "beforeAddWrite", "ownerDocument", "matches", "blur", "activeAni", "set", "play", "_x12", "_x13", "_x14", "_x15", "eventMethod", "eventName", "promise", "r", "onceEvent", "event", "callback", "handler", "isCancel", "defaultGate", "h", "safeCall", "arg", "jmp", "e", "OVERLAY_GESTURE_PRIORITY", "createDelegateController", "inline", "workingDelegate", "coreDelegate", "getDelegate", "force", "delegate", "parentEl", "parentNode", "attachViewToDom", "_ref5", "component", "Error", "_x16", "removeViewFromDom", "parentElement", "createTriggerController", "destroyTriggerInteraction", "removeClickListener", "addClickListener", "trigger", "triggerEl", "getElementById", "configureTriggerInteraction", "targetEl", "openOverlay", "newTopMostOverlay", "i", "presentedOverlay", "nextPresentedOverlay", "currentOverlay", "B", "G", "O", "j", "k", "l", "m", "n", "s", "t"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/overlays-b874c3c3.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { OVERLAY_BACK_BUTTON_PRIORITY, shouldUseCloseWatcher } from './hardware-back-button-6107a37c.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { C as CoreDelegate } from './framework-delegate-ed4ba327.js';\nimport { c as componentOnReady, f as focusVisibleElement, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\n\nlet lastOverlayIndex = 0;\nlet lastId = 0;\nconst activeAnimations = new WeakMap();\nconst createController = (tagName) => {\n    return {\n        create(options) {\n            return createOverlay(tagName, options);\n        },\n        dismiss(data, role, id) {\n            return dismissOverlay(document, data, role, tagName, id);\n        },\n        async getTop() {\n            return getPresentedOverlay(document, tagName);\n        },\n    };\n};\nconst alertController = /*@__PURE__*/ createController('ion-alert');\nconst actionSheetController = /*@__PURE__*/ createController('ion-action-sheet');\nconst loadingController = /*@__PURE__*/ createController('ion-loading');\nconst modalController = /*@__PURE__*/ createController('ion-modal');\nconst pickerController = /*@__PURE__*/ createController('ion-picker');\nconst popoverController = /*@__PURE__*/ createController('ion-popover');\nconst toastController = /*@__PURE__*/ createController('ion-toast');\n/**\n * Prepares the overlay element to be presented.\n */\nconst prepareOverlay = (el) => {\n    if (typeof document !== 'undefined') {\n        /**\n         * Adds a single instance of event listeners for application behaviors:\n         *\n         * - Escape Key behavior to dismiss an overlay\n         * - Trapping focus within an overlay\n         * - Back button behavior to dismiss an overlay\n         *\n         * This only occurs when the first overlay is created.\n         */\n        connectListeners(document);\n    }\n    const overlayIndex = lastOverlayIndex++;\n    /**\n     * overlayIndex is used in the overlay components to set a zIndex.\n     * This ensures that the most recently presented overlay will be\n     * on top.\n     */\n    el.overlayIndex = overlayIndex;\n};\n/**\n * Assigns an incrementing id to an overlay element, that does not\n * already have an id assigned to it.\n *\n * Used to track unique instances of an overlay element.\n */\nconst setOverlayId = (el) => {\n    if (!el.hasAttribute('id')) {\n        el.id = `ion-overlay-${++lastId}`;\n    }\n    return el.id;\n};\nconst createOverlay = (tagName, opts) => {\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (typeof window !== 'undefined' && typeof window.customElements !== 'undefined') {\n        return window.customElements.whenDefined(tagName).then(() => {\n            const element = document.createElement(tagName);\n            element.classList.add('overlay-hidden');\n            /**\n             * Convert the passed in overlay options into props\n             * that get passed down into the new overlay.\n             */\n            Object.assign(element, Object.assign(Object.assign({}, opts), { hasController: true }));\n            // append the overlay element to the document body\n            getAppRoot(document).appendChild(element);\n            return new Promise((resolve) => componentOnReady(element, resolve));\n        });\n    }\n    return Promise.resolve();\n};\n/**\n * This query string selects elements that\n * are eligible to receive focus. We select\n * interactive elements that meet the following\n * criteria:\n * 1. Element does not have a negative tabindex\n * 2. Element does not have `hidden`\n * 3. Element does not have `disabled` for non-Ionic components.\n * 4. Element does not have `disabled` or `disabled=\"true\"` for Ionic components.\n * Note: We need this distinction because `disabled=\"false\"` is\n * valid usage for the disabled property on ion-button.\n */\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^=\"-\"]):not([hidden]):not([disabled]), textarea:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), button:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), select:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable[disabled=\"false\"]:not([tabindex^=\"-\"]):not([hidden])';\nconst isOverlayHidden = (overlay) => overlay.classList.contains('overlay-hidden');\n/**\n * Focuses the first descendant in an overlay\n * that can receive focus. If none exists,\n * the entire overlay will be focused.\n */\nconst focusFirstDescendant = (ref, overlay) => {\n    const firstInput = ref.querySelector(focusableQueryString);\n    focusElementInOverlay(firstInput, overlay);\n};\n/**\n * Focuses the last descendant in an overlay\n * that can receive focus. If none exists,\n * the entire overlay will be focused.\n */\nconst focusLastDescendant = (ref, overlay) => {\n    const inputs = Array.from(ref.querySelectorAll(focusableQueryString));\n    const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n    focusElementInOverlay(lastInput, overlay);\n};\n/**\n * Focuses a particular element in an overlay. If the element\n * doesn't have anything focusable associated with it then\n * the overlay itself will be focused.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInOverlay = (hostToFocus, overlay) => {\n    let elementToFocus = hostToFocus;\n    const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n    }\n    if (elementToFocus) {\n        focusVisibleElement(elementToFocus);\n    }\n    else {\n        // Focus overlay instead of letting focus escape\n        overlay.focus();\n    }\n};\n/**\n * Traps keyboard focus inside of overlay components.\n * Based on https://w3c.github.io/aria-practices/examples/dialog-modal/alertdialog.html\n * This includes the following components: Action Sheet, Alert, Loading, Modal,\n * Picker, and Popover.\n * Should NOT include: Toast\n */\nconst trapKeyboardFocus = (ev, doc) => {\n    const lastOverlay = getPresentedOverlay(doc, 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover');\n    const target = ev.target;\n    /**\n     * If no active overlay, ignore this event.\n     *\n     * If this component uses the shadow dom,\n     * this global listener is pointless\n     * since it will not catch the focus\n     * traps as they are inside the shadow root.\n     * We need to add a listener to the shadow root\n     * itself to ensure the focus trap works.\n     */\n    if (!lastOverlay || !target) {\n        return;\n    }\n    /**\n     * If the ion-disable-focus-trap class\n     * is present on an overlay, then this component\n     * instance has opted out of focus trapping.\n     * An example of this is when the sheet modal\n     * has a backdrop that is disabled. The content\n     * behind the sheet should be focusable until\n     * the backdrop is enabled.\n     */\n    if (lastOverlay.classList.contains('ion-disable-focus-trap')) {\n        return;\n    }\n    const trapScopedFocus = () => {\n        /**\n         * If we are focusing the overlay, clear\n         * the last focused element so that hitting\n         * tab activates the first focusable element\n         * in the overlay wrapper.\n         */\n        if (lastOverlay === target) {\n            lastOverlay.lastFocus = undefined;\n            /**\n             * Toasts can be presented from an overlay.\n             * However, focus should still be returned to\n             * the overlay when clicking a toast. Normally,\n             * focus would be returned to the last focusable\n             * descendant in the overlay which may not always be\n             * the button that the toast was presented from. In this case,\n             * the focus may be returned to an unexpected element.\n             * To account for this, we make sure to return focus to the\n             * last focused element in the overlay if focus is\n             * moved to the toast.\n             */\n        }\n        else if (target.tagName === 'ION-TOAST') {\n            focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n            /**\n             * Otherwise, we must be focusing an element\n             * inside of the overlay. The two possible options\n             * here are an input/button/etc or the ion-focus-trap\n             * element. The focus trap element is used to prevent\n             * the keyboard focus from leaving the overlay when\n             * using Tab or screen assistants.\n             */\n        }\n        else {\n            /**\n             * We do not want to focus the traps, so get the overlay\n             * wrapper element as the traps live outside of the wrapper.\n             */\n            const overlayRoot = getElementRoot(lastOverlay);\n            if (!overlayRoot.contains(target)) {\n                return;\n            }\n            const overlayWrapper = overlayRoot.querySelector('.ion-overlay-wrapper');\n            if (!overlayWrapper) {\n                return;\n            }\n            /**\n             * If the target is inside the wrapper, let the browser\n             * focus as normal and keep a log of the last focused element.\n             * Additionally, if the backdrop was tapped we should not\n             * move focus back inside the wrapper as that could cause\n             * an interactive elements focus state to activate.\n             */\n            if (overlayWrapper.contains(target) || target === overlayRoot.querySelector('ion-backdrop')) {\n                lastOverlay.lastFocus = target;\n            }\n            else {\n                /**\n                 * Otherwise, we must have focused one of the focus traps.\n                 * We need to wrap the focus to either the first element\n                 * or the last element.\n                 */\n                /**\n                 * Once we call `focusFirstDescendant` and focus the first\n                 * descendant, another focus event will fire which will\n                 * cause `lastOverlay.lastFocus` to be updated before\n                 * we can run the code after that. We will cache the value\n                 * here to avoid that.\n                 */\n                const lastFocus = lastOverlay.lastFocus;\n                // Focus the first element in the overlay wrapper\n                focusFirstDescendant(overlayWrapper, lastOverlay);\n                /**\n                 * If the cached last focused element is the\n                 * same as the active element, then we need\n                 * to wrap focus to the last descendant. This happens\n                 * when the first descendant is focused, and the user\n                 * presses Shift + Tab. The previous line will focus\n                 * the same descendant again (the first one), causing\n                 * last focus to equal the active element.\n                 */\n                if (lastFocus === doc.activeElement) {\n                    focusLastDescendant(overlayWrapper, lastOverlay);\n                }\n                lastOverlay.lastFocus = doc.activeElement;\n            }\n        }\n    };\n    const trapShadowFocus = () => {\n        /**\n         * If the target is inside the wrapper, let the browser\n         * focus as normal and keep a log of the last focused element.\n         */\n        if (lastOverlay.contains(target)) {\n            lastOverlay.lastFocus = target;\n            /**\n             * Toasts can be presented from an overlay.\n             * However, focus should still be returned to\n             * the overlay when clicking a toast. Normally,\n             * focus would be returned to the last focusable\n             * descendant in the overlay which may not always be\n             * the button that the toast was presented from. In this case,\n             * the focus may be returned to an unexpected element.\n             * To account for this, we make sure to return focus to the\n             * last focused element in the overlay if focus is\n             * moved to the toast.\n             */\n        }\n        else if (target.tagName === 'ION-TOAST') {\n            focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n        }\n        else {\n            /**\n             * Otherwise, we are about to have focus\n             * go out of the overlay. We need to wrap\n             * the focus to either the first element\n             * or the last element.\n             */\n            /**\n             * Once we call `focusFirstDescendant` and focus the first\n             * descendant, another focus event will fire which will\n             * cause `lastOverlay.lastFocus` to be updated before\n             * we can run the code after that. We will cache the value\n             * here to avoid that.\n             */\n            const lastFocus = lastOverlay.lastFocus;\n            // Focus the first element in the overlay wrapper\n            focusFirstDescendant(lastOverlay, lastOverlay);\n            /**\n             * If the cached last focused element is the\n             * same as the active element, then we need\n             * to wrap focus to the last descendant. This happens\n             * when the first descendant is focused, and the user\n             * presses Shift + Tab. The previous line will focus\n             * the same descendant again (the first one), causing\n             * last focus to equal the active element.\n             */\n            if (lastFocus === doc.activeElement) {\n                focusLastDescendant(lastOverlay, lastOverlay);\n            }\n            lastOverlay.lastFocus = doc.activeElement;\n        }\n    };\n    if (lastOverlay.shadowRoot) {\n        trapShadowFocus();\n    }\n    else {\n        trapScopedFocus();\n    }\n};\nconst connectListeners = (doc) => {\n    if (lastOverlayIndex === 0) {\n        lastOverlayIndex = 1;\n        doc.addEventListener('focus', (ev) => {\n            trapKeyboardFocus(ev, doc);\n        }, true);\n        // handle back-button click\n        doc.addEventListener('ionBackButton', (ev) => {\n            const lastOverlay = getPresentedOverlay(doc);\n            if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n                ev.detail.register(OVERLAY_BACK_BUTTON_PRIORITY, () => {\n                    /**\n                     * Do not return this promise otherwise\n                     * the hardware back button utility will\n                     * be blocked until the overlay dismisses.\n                     * This is important for a modal with canDismiss.\n                     * If the application presents a confirmation alert\n                     * in the \"canDismiss\" callback, then it will be impossible\n                     * to use the hardware back button to dismiss the alert\n                     * dialog because the hardware back button utility\n                     * is blocked on waiting for the modal to dismiss.\n                     */\n                    lastOverlay.dismiss(undefined, BACKDROP);\n                });\n            }\n        });\n        /**\n         * Handle ESC to close overlay.\n         * CloseWatcher also handles pressing the Esc\n         * key, so if a browser supports CloseWatcher then\n         * this behavior will be handled via the ionBackButton\n         * event.\n         */\n        if (!shouldUseCloseWatcher()) {\n            doc.addEventListener('keydown', (ev) => {\n                if (ev.key === 'Escape') {\n                    const lastOverlay = getPresentedOverlay(doc);\n                    if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n                        lastOverlay.dismiss(undefined, BACKDROP);\n                    }\n                }\n            });\n        }\n    }\n};\nconst dismissOverlay = (doc, data, role, overlayTag, id) => {\n    const overlay = getPresentedOverlay(doc, overlayTag, id);\n    if (!overlay) {\n        return Promise.reject('overlay does not exist');\n    }\n    return overlay.dismiss(data, role);\n};\n/**\n * Returns a list of all overlays in the DOM even if they are not presented.\n */\nconst getOverlays = (doc, selector) => {\n    if (selector === undefined) {\n        selector = 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover,ion-toast';\n    }\n    return Array.from(doc.querySelectorAll(selector)).filter((c) => c.overlayIndex > 0);\n};\n/**\n * Returns a list of all presented overlays.\n * Inline overlays can exist in the DOM but not be presented,\n * so there are times when we want to exclude those.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n */\nconst getPresentedOverlays = (doc, overlayTag) => {\n    return getOverlays(doc, overlayTag).filter((o) => !isOverlayHidden(o));\n};\n/**\n * Returns a presented overlay element.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n * @param id The unique identifier for the overlay instance.\n * @returns The overlay element or `undefined` if no overlay element is found.\n */\nconst getPresentedOverlay = (doc, overlayTag, id) => {\n    const overlays = getPresentedOverlays(doc, overlayTag);\n    return id === undefined ? overlays[overlays.length - 1] : overlays.find((o) => o.id === id);\n};\n/**\n * When an overlay is presented, the main\n * focus is the overlay not the page content.\n * We need to remove the page content from the\n * accessibility tree otherwise when\n * users use \"read screen from top\" gestures with\n * TalkBack and VoiceOver, the screen reader will begin\n * to read the content underneath the overlay.\n *\n * We need a container where all page components\n * exist that is separate from where the overlays\n * are added in the DOM. For most apps, this element\n * is the top most ion-router-outlet. In the event\n * that devs are not using a router,\n * they will need to add the \"ion-view-container-root\"\n * id to the element that contains all of their views.\n *\n * TODO: If Framework supports having multiple top\n * level router outlets we would need to update this.\n * Example: One outlet for side menu and one outlet\n * for main content.\n */\nconst setRootAriaHidden = (hidden = false) => {\n    const root = getAppRoot(document);\n    const viewContainer = root.querySelector('ion-router-outlet, ion-nav, #ion-view-container-root');\n    if (!viewContainer) {\n        return;\n    }\n    if (hidden) {\n        viewContainer.setAttribute('aria-hidden', 'true');\n    }\n    else {\n        viewContainer.removeAttribute('aria-hidden');\n    }\n};\nconst present = async (overlay, name, iosEnterAnimation, mdEnterAnimation, opts) => {\n    var _a, _b;\n    if (overlay.presented) {\n        return;\n    }\n    setRootAriaHidden(true);\n    hideOverlaysFromScreenReaders(overlay.el);\n    overlay.presented = true;\n    overlay.willPresent.emit();\n    (_a = overlay.willPresentShorthand) === null || _a === void 0 ? void 0 : _a.emit();\n    const mode = getIonMode(overlay);\n    // get the user's animation fn if one was provided\n    const animationBuilder = overlay.enterAnimation\n        ? overlay.enterAnimation\n        : config.get(name, mode === 'ios' ? iosEnterAnimation : mdEnterAnimation);\n    const completed = await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n    if (completed) {\n        overlay.didPresent.emit();\n        (_b = overlay.didPresentShorthand) === null || _b === void 0 ? void 0 : _b.emit();\n    }\n    /**\n     * When an overlay that steals focus\n     * is dismissed, focus should be returned\n     * to the element that was focused\n     * prior to the overlay opening. Toast\n     * does not steal focus and is excluded\n     * from returning focus as a result.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n        restoreElementFocus(overlay.el);\n    }\n    /**\n     * If the focused element is already\n     * inside the overlay component then\n     * focus should not be moved from that\n     * to the overlay container.\n     */\n    if (overlay.keyboardClose && (document.activeElement === null || !overlay.el.contains(document.activeElement))) {\n        overlay.el.focus();\n    }\n    /**\n     * If this overlay was previously dismissed without being\n     * the topmost one (such as by manually calling dismiss()),\n     * it would still have aria-hidden on being presented again.\n     * Removing it here ensures the overlay is visible to screen\n     * readers.\n     */\n    overlay.el.removeAttribute('aria-hidden');\n};\n/**\n * When an overlay component is dismissed,\n * focus should be returned to the element\n * that presented the overlay. Otherwise\n * focus will be set on the body which\n * means that people using screen readers\n * or tabbing will need to re-navigate\n * to where they were before they\n * opened the overlay.\n */\nconst restoreElementFocus = async (overlayEl) => {\n    let previousElement = document.activeElement;\n    if (!previousElement) {\n        return;\n    }\n    const shadowRoot = previousElement === null || previousElement === void 0 ? void 0 : previousElement.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        previousElement = shadowRoot.querySelector(focusableQueryString) || previousElement;\n    }\n    await overlayEl.onDidDismiss();\n    /**\n     * After onDidDismiss, the overlay loses focus\n     * because it is removed from the document\n     *\n     * > An element will also lose focus [...]\n     * > if the element is removed from the document)\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Element/blur_event\n     *\n     * Additionally, `document.activeElement` returns:\n     *\n     * > The Element which currently has focus,\n     * > `<body>` or null if there is\n     * > no focused element.\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Document/activeElement#value\n     *\n     * However, if the user has already focused\n     * an element sometime between onWillDismiss\n     * and onDidDismiss (for example, focusing a\n     * text box after tapping a button in an\n     * action sheet) then don't restore focus to\n     * previous element\n     */\n    if (document.activeElement === null || document.activeElement === document.body) {\n        previousElement.focus();\n    }\n};\nconst dismiss = async (overlay, data, role, name, iosLeaveAnimation, mdLeaveAnimation, opts) => {\n    var _a, _b;\n    if (!overlay.presented) {\n        return false;\n    }\n    /**\n     * If this is the last visible overlay then\n     * we want to re-add the root to the accessibility tree.\n     */\n    if (doc !== undefined && getPresentedOverlays(doc).length === 1) {\n        setRootAriaHidden(false);\n    }\n    overlay.presented = false;\n    try {\n        // Overlay contents should not be clickable during dismiss\n        overlay.el.style.setProperty('pointer-events', 'none');\n        overlay.willDismiss.emit({ data, role });\n        (_a = overlay.willDismissShorthand) === null || _a === void 0 ? void 0 : _a.emit({ data, role });\n        const mode = getIonMode(overlay);\n        const animationBuilder = overlay.leaveAnimation\n            ? overlay.leaveAnimation\n            : config.get(name, mode === 'ios' ? iosLeaveAnimation : mdLeaveAnimation);\n        // If dismissed via gesture, no need to play leaving animation again\n        if (role !== GESTURE) {\n            await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n        }\n        overlay.didDismiss.emit({ data, role });\n        (_b = overlay.didDismissShorthand) === null || _b === void 0 ? void 0 : _b.emit({ data, role });\n        // Get a reference to all animations currently assigned to this overlay\n        // Then tear them down to return the overlay to its initial visual state\n        const animations = activeAnimations.get(overlay) || [];\n        animations.forEach((ani) => ani.destroy());\n        activeAnimations.delete(overlay);\n        /**\n         * Make overlay hidden again in case it is being reused.\n         * We can safely remove pointer-events: none as\n         * overlay-hidden will set display: none.\n         */\n        overlay.el.classList.add('overlay-hidden');\n        overlay.el.style.removeProperty('pointer-events');\n        /**\n         * Clear any focus trapping references\n         * when the overlay is dismissed.\n         */\n        if (overlay.el.lastFocus !== undefined) {\n            overlay.el.lastFocus = undefined;\n        }\n    }\n    catch (err) {\n        console.error(err);\n    }\n    overlay.el.remove();\n    revealOverlaysToScreenReaders();\n    return true;\n};\nconst getAppRoot = (doc) => {\n    return doc.querySelector('ion-app') || doc.body;\n};\nconst overlayAnimation = async (overlay, animationBuilder, baseEl, opts) => {\n    // Make overlay visible in case it's hidden\n    baseEl.classList.remove('overlay-hidden');\n    const aniRoot = overlay.el;\n    const animation = animationBuilder(aniRoot, opts);\n    if (!overlay.animated || !config.getBoolean('animated', true)) {\n        animation.duration(0);\n    }\n    if (overlay.keyboardClose) {\n        animation.beforeAddWrite(() => {\n            const activeElement = baseEl.ownerDocument.activeElement;\n            if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.matches('input,ion-input, ion-textarea')) {\n                activeElement.blur();\n            }\n        });\n    }\n    const activeAni = activeAnimations.get(overlay) || [];\n    activeAnimations.set(overlay, [...activeAni, animation]);\n    await animation.play();\n    return true;\n};\nconst eventMethod = (element, eventName) => {\n    let resolve;\n    const promise = new Promise((r) => (resolve = r));\n    onceEvent(element, eventName, (event) => {\n        resolve(event.detail);\n    });\n    return promise;\n};\nconst onceEvent = (element, eventName, callback) => {\n    const handler = (ev) => {\n        removeEventListener(element, eventName, handler);\n        callback(ev);\n    };\n    addEventListener(element, eventName, handler);\n};\nconst isCancel = (role) => {\n    return role === 'cancel' || role === BACKDROP;\n};\nconst defaultGate = (h) => h();\n/**\n * Calls a developer provided method while avoiding\n * Angular Zones. Since the handler is provided by\n * the developer, we should throw any errors\n * received so that developer-provided bug\n * tracking software can log it.\n */\nconst safeCall = (handler, arg) => {\n    if (typeof handler === 'function') {\n        const jmp = config.get('_zoneGate', defaultGate);\n        return jmp(() => {\n            try {\n                return handler(arg);\n            }\n            catch (e) {\n                throw e;\n            }\n        });\n    }\n    return undefined;\n};\nconst BACKDROP = 'backdrop';\nconst GESTURE = 'gesture';\nconst OVERLAY_GESTURE_PRIORITY = 39;\n/**\n * Creates a delegate controller.\n *\n * Requires that the component has the following properties:\n * - `el: HTMLElement`\n * - `hasController: boolean`\n * - `delegate?: FrameworkDelegate`\n *\n * @param ref The component class instance.\n */\nconst createDelegateController = (ref) => {\n    let inline = false;\n    let workingDelegate;\n    const coreDelegate = CoreDelegate();\n    /**\n     * Determines whether or not an overlay is being used\n     * inline or via a controller/JS and returns the correct delegate.\n     * By default, subsequent calls to getDelegate will use\n     * a cached version of the delegate.\n     * This is useful for calling dismiss after present,\n     * so that the correct delegate is given.\n     * @param force `true` to force the non-cached version of the delegate.\n     * @returns The delegate to use and whether or not the overlay is inline.\n     */\n    const getDelegate = (force = false) => {\n        if (workingDelegate && !force) {\n            return {\n                delegate: workingDelegate,\n                inline,\n            };\n        }\n        const { el, hasController, delegate } = ref;\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = el.parentNode;\n        inline = parentEl !== null && !hasController;\n        workingDelegate = inline ? delegate || coreDelegate : delegate;\n        return { inline, delegate: workingDelegate };\n    };\n    /**\n     * Attaches a component in the DOM. Teleports the component\n     * to the root of the app.\n     * @param component The component to optionally construct and append to the element.\n     */\n    const attachViewToDom = async (component) => {\n        const { delegate } = getDelegate(true);\n        if (delegate) {\n            return await delegate.attachViewToDom(ref.el, component);\n        }\n        const { hasController } = ref;\n        if (hasController && component !== undefined) {\n            throw new Error('framework delegate is missing');\n        }\n        return null;\n    };\n    /**\n     * Moves a component back to its original location in the DOM.\n     */\n    const removeViewFromDom = () => {\n        const { delegate } = getDelegate();\n        if (delegate && ref.el !== undefined) {\n            delegate.removeViewFromDom(ref.el.parentElement, ref.el);\n        }\n    };\n    return {\n        attachViewToDom,\n        removeViewFromDom,\n    };\n};\n/**\n * Constructs a trigger interaction for an overlay.\n * Presents an overlay when the trigger is clicked.\n *\n * Usage:\n * ```ts\n * triggerController = createTriggerController();\n * triggerController.addClickListener(el, trigger);\n * ```\n */\nconst createTriggerController = () => {\n    let destroyTriggerInteraction;\n    /**\n     * Removes the click listener from the trigger element.\n     */\n    const removeClickListener = () => {\n        if (destroyTriggerInteraction) {\n            destroyTriggerInteraction();\n            destroyTriggerInteraction = undefined;\n        }\n    };\n    /**\n     * Adds a click listener to the trigger element.\n     * Presents the overlay when the trigger is clicked.\n     * @param el The overlay element.\n     * @param trigger The ID of the element to add a click listener to.\n     */\n    const addClickListener = (el, trigger) => {\n        removeClickListener();\n        const triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n        if (!triggerEl) {\n            printIonWarning(`A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on an overlay component.`, el);\n            return;\n        }\n        const configureTriggerInteraction = (targetEl, overlayEl) => {\n            const openOverlay = () => {\n                overlayEl.present();\n            };\n            targetEl.addEventListener('click', openOverlay);\n            return () => {\n                targetEl.removeEventListener('click', openOverlay);\n            };\n        };\n        destroyTriggerInteraction = configureTriggerInteraction(triggerEl, el);\n    };\n    return {\n        addClickListener,\n        removeClickListener,\n    };\n};\n/**\n * Ensure that underlying overlays have aria-hidden if necessary so that screen readers\n * cannot move focus to these elements. Note that we cannot rely on focus/focusin/focusout\n * events here because those events do not fire when the screen readers moves to a non-focusable\n * element such as text.\n * Without this logic screen readers would be able to move focus outside of the top focus-trapped overlay.\n *\n * @param newTopMostOverlay - The overlay that is being presented. Since the overlay has not been\n * fully presented yet at the time this function is called it will not be included in the getPresentedOverlays result.\n */\nconst hideOverlaysFromScreenReaders = (newTopMostOverlay) => {\n    var _a;\n    if (doc === undefined)\n        return;\n    const overlays = getPresentedOverlays(doc);\n    for (let i = overlays.length - 1; i >= 0; i--) {\n        const presentedOverlay = overlays[i];\n        const nextPresentedOverlay = (_a = overlays[i + 1]) !== null && _a !== void 0 ? _a : newTopMostOverlay;\n        /**\n         * If next overlay has aria-hidden then all remaining overlays will have it too.\n         * Or, if the next overlay is a Toast that does not have aria-hidden then current overlay\n         * should not have aria-hidden either so focus can remain in the current overlay.\n         */\n        if (nextPresentedOverlay.hasAttribute('aria-hidden') || nextPresentedOverlay.tagName !== 'ION-TOAST') {\n            presentedOverlay.setAttribute('aria-hidden', 'true');\n        }\n    }\n};\n/**\n * When dismissing an overlay we need to reveal the new top-most overlay to screen readers.\n * If the top-most overlay is a Toast we potentially need to reveal more overlays since\n * focus is never automatically moved to the Toast.\n */\nconst revealOverlaysToScreenReaders = () => {\n    if (doc === undefined)\n        return;\n    const overlays = getPresentedOverlays(doc);\n    for (let i = overlays.length - 1; i >= 0; i--) {\n        const currentOverlay = overlays[i];\n        /**\n         * If the current we are looking at is a Toast then we can remove aria-hidden.\n         * However, we potentially need to keep looking at the overlay stack because there\n         * could be more Toasts underneath. Additionally, we need to unhide the closest non-Toast\n         * overlay too so focus can move there since focus is never automatically moved to the Toast.\n         */\n        currentOverlay.removeAttribute('aria-hidden');\n        /**\n         * If we found a non-Toast element then we can just remove aria-hidden and stop searching entirely\n         * since this overlay should always receive focus. As a result, all underlying overlays should still\n         * be hidden from screen readers.\n         */\n        if (currentOverlay.tagName !== 'ION-TOAST') {\n            break;\n        }\n    }\n};\n\nexport { BACKDROP as B, GESTURE as G, OVERLAY_GESTURE_PRIORITY as O, alertController as a, actionSheetController as b, popoverController as c, createDelegateController as d, createTriggerController as e, present as f, dismiss as g, eventMethod as h, isCancel as i, prepareOverlay as j, setOverlayId as k, loadingController as l, modalController as m, focusFirstDescendant as n, getPresentedOverlay as o, pickerController as p, safeCall as s, toastController as t };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,4BAA4B,EAAEC,qBAAqB,QAAQ,oCAAoC;AACxG,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,MAAM,QAAQ,4BAA4B;AACzE,SAASC,CAAC,IAAIC,YAAY,QAAQ,kCAAkC;AACpE,SAASH,CAAC,IAAII,gBAAgB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEV,CAAC,IAAIW,mBAAmB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,uBAAuB;AAC7J,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAE1D,IAAIC,gBAAgB,GAAG,CAAC;AACxB,IAAIC,MAAM,GAAG,CAAC;AACd,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACtC,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;EAClC,OAAO;IACHC,MAAMA,CAACC,OAAO,EAAE;MACZ,OAAOC,aAAa,CAACH,OAAO,EAAEE,OAAO,CAAC;IAC1C,CAAC;IACDE,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAEC,EAAE,EAAE;MACpB,OAAOC,cAAc,CAACC,QAAQ,EAAEJ,IAAI,EAAEC,IAAI,EAAEN,OAAO,EAAEO,EAAE,CAAC;IAC5D,CAAC;IACKG,MAAMA,CAAA,EAAG;MAAA,OAAAC,iBAAA;QACX,OAAOC,mBAAmB,CAACH,QAAQ,EAAET,OAAO,CAAC;MAAC;IAClD;EACJ,CAAC;AACL,CAAC;AACD,MAAMa,eAAe,GAAG,aAAcd,gBAAgB,CAAC,WAAW,CAAC;AACnE,MAAMe,qBAAqB,GAAG,aAAcf,gBAAgB,CAAC,kBAAkB,CAAC;AAChF,MAAMgB,iBAAiB,GAAG,aAAchB,gBAAgB,CAAC,aAAa,CAAC;AACvE,MAAMiB,eAAe,GAAG,aAAcjB,gBAAgB,CAAC,WAAW,CAAC;AACnE,MAAMkB,gBAAgB,GAAG,aAAclB,gBAAgB,CAAC,YAAY,CAAC;AACrE,MAAMmB,iBAAiB,GAAG,aAAcnB,gBAAgB,CAAC,aAAa,CAAC;AACvE,MAAMoB,eAAe,GAAG,aAAcpB,gBAAgB,CAAC,WAAW,CAAC;AACnE;AACA;AACA;AACA,MAAMqB,cAAc,GAAIC,EAAE,IAAK;EAC3B,IAAI,OAAOZ,QAAQ,KAAK,WAAW,EAAE;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQa,gBAAgB,CAACb,QAAQ,CAAC;EAC9B;EACA,MAAMc,YAAY,GAAG5B,gBAAgB,EAAE;EACvC;AACJ;AACA;AACA;AACA;EACI0B,EAAE,CAACE,YAAY,GAAGA,YAAY;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAIH,EAAE,IAAK;EACzB,IAAI,CAACA,EAAE,CAACI,YAAY,CAAC,IAAI,CAAC,EAAE;IACxBJ,EAAE,CAACd,EAAE,GAAG,eAAe,EAAEX,MAAM,EAAE;EACrC;EACA,OAAOyB,EAAE,CAACd,EAAE;AAChB,CAAC;AACD,MAAMJ,aAAa,GAAGA,CAACH,OAAO,EAAE0B,IAAI,KAAK;EACrC;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,cAAc,KAAK,WAAW,EAAE;IAC/E,OAAOD,MAAM,CAACC,cAAc,CAACC,WAAW,CAAC7B,OAAO,CAAC,CAAC8B,IAAI,CAAC,MAAM;MACzD,MAAMC,OAAO,GAAGtB,QAAQ,CAACuB,aAAa,CAAChC,OAAO,CAAC;MAC/C+B,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;MACvC;AACZ;AACA;AACA;MACYC,MAAM,CAACC,MAAM,CAACL,OAAO,EAAEI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,IAAI,CAAC,EAAE;QAAEW,aAAa,EAAE;MAAK,CAAC,CAAC,CAAC;MACvF;MACAC,UAAU,CAAC7B,QAAQ,CAAC,CAAC8B,WAAW,CAACR,OAAO,CAAC;MACzC,OAAO,IAAIS,OAAO,CAAEC,OAAO,IAAKxD,gBAAgB,CAAC8C,OAAO,EAAEU,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC;EACN;EACA,OAAOD,OAAO,CAACC,OAAO,CAAC,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,ucAAuc;AACpe,MAAMC,eAAe,GAAIC,OAAO,IAAKA,OAAO,CAACX,SAAS,CAACY,QAAQ,CAAC,gBAAgB,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAGA,CAACC,GAAG,EAAEH,OAAO,KAAK;EAC3C,MAAMI,UAAU,GAAGD,GAAG,CAACE,aAAa,CAACP,oBAAoB,CAAC;EAC1DQ,qBAAqB,CAACF,UAAU,EAAEJ,OAAO,CAAC;AAC9C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMO,mBAAmB,GAAGA,CAACJ,GAAG,EAAEH,OAAO,KAAK;EAC1C,MAAMQ,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACP,GAAG,CAACQ,gBAAgB,CAACb,oBAAoB,CAAC,CAAC;EACrE,MAAMc,SAAS,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC,GAAGL,MAAM,CAACA,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EACtEP,qBAAqB,CAACM,SAAS,EAAEZ,OAAO,CAAC;AAC7C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,qBAAqB,GAAGA,CAACQ,WAAW,EAAEd,OAAO,KAAK;EACpD,IAAIe,cAAc,GAAGD,WAAW;EAChC,MAAME,UAAU,GAAGF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,UAAU;EACnG,IAAIA,UAAU,EAAE;IACZ;IACAD,cAAc,GAAGC,UAAU,CAACX,aAAa,CAACP,oBAAoB,CAAC,IAAIgB,WAAW;EAClF;EACA,IAAIC,cAAc,EAAE;IAChBxE,mBAAmB,CAACwE,cAAc,CAAC;EACvC,CAAC,MACI;IACD;IACAf,OAAO,CAACiB,KAAK,CAAC,CAAC;EACnB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAACC,EAAE,EAAEvF,GAAG,KAAK;EACnC,MAAMwF,WAAW,GAAGpD,mBAAmB,CAACpC,GAAG,EAAE,yEAAyE,CAAC;EACvH,MAAMyF,MAAM,GAAGF,EAAE,CAACE,MAAM;EACxB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAACD,WAAW,IAAI,CAACC,MAAM,EAAE;IACzB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAID,WAAW,CAAC/B,SAAS,CAACY,QAAQ,CAAC,wBAAwB,CAAC,EAAE;IAC1D;EACJ;EACA,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC1B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIF,WAAW,KAAKC,MAAM,EAAE;MACxBD,WAAW,CAACG,SAAS,GAAGC,SAAS;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,CAAC,MACI,IAAIH,MAAM,CAACjE,OAAO,KAAK,WAAW,EAAE;MACrCkD,qBAAqB,CAACc,WAAW,CAACG,SAAS,EAAEH,WAAW,CAAC;MACzD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAMK,WAAW,GAAG7E,cAAc,CAACwE,WAAW,CAAC;MAC/C,IAAI,CAACK,WAAW,CAACxB,QAAQ,CAACoB,MAAM,CAAC,EAAE;QAC/B;MACJ;MACA,MAAMK,cAAc,GAAGD,WAAW,CAACpB,aAAa,CAAC,sBAAsB,CAAC;MACxE,IAAI,CAACqB,cAAc,EAAE;QACjB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIA,cAAc,CAACzB,QAAQ,CAACoB,MAAM,CAAC,IAAIA,MAAM,KAAKI,WAAW,CAACpB,aAAa,CAAC,cAAc,CAAC,EAAE;QACzFe,WAAW,CAACG,SAAS,GAAGF,MAAM;MAClC,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;QACgB;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,MAAME,SAAS,GAAGH,WAAW,CAACG,SAAS;QACvC;QACArB,oBAAoB,CAACwB,cAAc,EAAEN,WAAW,CAAC;QACjD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAIG,SAAS,KAAK3F,GAAG,CAAC+F,aAAa,EAAE;UACjCpB,mBAAmB,CAACmB,cAAc,EAAEN,WAAW,CAAC;QACpD;QACAA,WAAW,CAACG,SAAS,GAAG3F,GAAG,CAAC+F,aAAa;MAC7C;IACJ;EACJ,CAAC;EACD,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B;AACR;AACA;AACA;IACQ,IAAIR,WAAW,CAACnB,QAAQ,CAACoB,MAAM,CAAC,EAAE;MAC9BD,WAAW,CAACG,SAAS,GAAGF,MAAM;MAC9B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,CAAC,MACI,IAAIA,MAAM,CAACjE,OAAO,KAAK,WAAW,EAAE;MACrCkD,qBAAqB,CAACc,WAAW,CAACG,SAAS,EAAEH,WAAW,CAAC;IAC7D,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;MACY;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMG,SAAS,GAAGH,WAAW,CAACG,SAAS;MACvC;MACArB,oBAAoB,CAACkB,WAAW,EAAEA,WAAW,CAAC;MAC9C;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIG,SAAS,KAAK3F,GAAG,CAAC+F,aAAa,EAAE;QACjCpB,mBAAmB,CAACa,WAAW,EAAEA,WAAW,CAAC;MACjD;MACAA,WAAW,CAACG,SAAS,GAAG3F,GAAG,CAAC+F,aAAa;IAC7C;EACJ,CAAC;EACD,IAAIP,WAAW,CAACJ,UAAU,EAAE;IACxBY,eAAe,CAAC,CAAC;EACrB,CAAC,MACI;IACDN,eAAe,CAAC,CAAC;EACrB;AACJ,CAAC;AACD,MAAM5C,gBAAgB,GAAI9C,GAAG,IAAK;EAC9B,IAAImB,gBAAgB,KAAK,CAAC,EAAE;IACxBA,gBAAgB,GAAG,CAAC;IACpBnB,GAAG,CAACa,gBAAgB,CAAC,OAAO,EAAG0E,EAAE,IAAK;MAClCD,iBAAiB,CAACC,EAAE,EAAEvF,GAAG,CAAC;IAC9B,CAAC,EAAE,IAAI,CAAC;IACR;IACAA,GAAG,CAACa,gBAAgB,CAAC,eAAe,EAAG0E,EAAE,IAAK;MAC1C,MAAMC,WAAW,GAAGpD,mBAAmB,CAACpC,GAAG,CAAC;MAC5C,IAAIwF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACS,eAAe,EAAE;QACvFV,EAAE,CAACW,MAAM,CAACC,QAAQ,CAAClG,4BAA4B,EAAE,MAAM;UACnD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoBuF,WAAW,CAAC5D,OAAO,CAACgE,SAAS,EAAEQ,QAAQ,CAAC;QAC5C,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAClG,qBAAqB,CAAC,CAAC,EAAE;MAC1BF,GAAG,CAACa,gBAAgB,CAAC,SAAS,EAAG0E,EAAE,IAAK;QACpC,IAAIA,EAAE,CAACc,GAAG,KAAK,QAAQ,EAAE;UACrB,MAAMb,WAAW,GAAGpD,mBAAmB,CAACpC,GAAG,CAAC;UAC5C,IAAIwF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACS,eAAe,EAAE;YACvFT,WAAW,CAAC5D,OAAO,CAACgE,SAAS,EAAEQ,QAAQ,CAAC;UAC5C;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;AACJ,CAAC;AACD,MAAMpE,cAAc,GAAGA,CAAChC,GAAG,EAAE6B,IAAI,EAAEC,IAAI,EAAEwE,UAAU,EAAEvE,EAAE,KAAK;EACxD,MAAMqC,OAAO,GAAGhC,mBAAmB,CAACpC,GAAG,EAAEsG,UAAU,EAAEvE,EAAE,CAAC;EACxD,IAAI,CAACqC,OAAO,EAAE;IACV,OAAOJ,OAAO,CAACuC,MAAM,CAAC,wBAAwB,CAAC;EACnD;EACA,OAAOnC,OAAO,CAACxC,OAAO,CAACC,IAAI,EAAEC,IAAI,CAAC;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAM0E,WAAW,GAAGA,CAACxG,GAAG,EAAEyG,QAAQ,KAAK;EACnC,IAAIA,QAAQ,KAAKb,SAAS,EAAE;IACxBa,QAAQ,GAAG,mFAAmF;EAClG;EACA,OAAO5B,KAAK,CAACC,IAAI,CAAC9E,GAAG,CAAC+E,gBAAgB,CAAC0B,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAErG,CAAC,IAAKA,CAAC,CAAC0C,YAAY,GAAG,CAAC,CAAC;AACvF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4D,oBAAoB,GAAGA,CAAC3G,GAAG,EAAEsG,UAAU,KAAK;EAC9C,OAAOE,WAAW,CAACxG,GAAG,EAAEsG,UAAU,CAAC,CAACI,MAAM,CAAEE,CAAC,IAAK,CAACzC,eAAe,CAACyC,CAAC,CAAC,CAAC;AAC1E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMxE,mBAAmB,GAAGA,CAACpC,GAAG,EAAEsG,UAAU,EAAEvE,EAAE,KAAK;EACjD,MAAM8E,QAAQ,GAAGF,oBAAoB,CAAC3G,GAAG,EAAEsG,UAAU,CAAC;EACtD,OAAOvE,EAAE,KAAK6D,SAAS,GAAGiB,QAAQ,CAACA,QAAQ,CAAC5B,MAAM,GAAG,CAAC,CAAC,GAAG4B,QAAQ,CAACC,IAAI,CAAEF,CAAC,IAAKA,CAAC,CAAC7E,EAAE,KAAKA,EAAE,CAAC;AAC/F,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgF,iBAAiB,GAAGA,CAACC,MAAM,GAAG,KAAK,KAAK;EAC1C,MAAMC,IAAI,GAAGnD,UAAU,CAAC7B,QAAQ,CAAC;EACjC,MAAMiF,aAAa,GAAGD,IAAI,CAACxC,aAAa,CAAC,sDAAsD,CAAC;EAChG,IAAI,CAACyC,aAAa,EAAE;IAChB;EACJ;EACA,IAAIF,MAAM,EAAE;IACRE,aAAa,CAACC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EACrD,CAAC,MACI;IACDD,aAAa,CAACE,eAAe,CAAC,aAAa,CAAC;EAChD;AACJ,CAAC;AACD,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAAnF,iBAAA,CAAG,WAAOiC,OAAO,EAAEmD,IAAI,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEvE,IAAI,EAAK;IAChF,IAAIwE,EAAE,EAAEC,EAAE;IACV,IAAIvD,OAAO,CAACwD,SAAS,EAAE;MACnB;IACJ;IACAb,iBAAiB,CAAC,IAAI,CAAC;IACvBc,6BAA6B,CAACzD,OAAO,CAACvB,EAAE,CAAC;IACzCuB,OAAO,CAACwD,SAAS,GAAG,IAAI;IACxBxD,OAAO,CAAC0D,WAAW,CAACC,IAAI,CAAC,CAAC;IAC1B,CAACL,EAAE,GAAGtD,OAAO,CAAC4D,oBAAoB,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAC,CAAC;IAClF,MAAME,IAAI,GAAG7H,UAAU,CAACgE,OAAO,CAAC;IAChC;IACA,MAAM8D,gBAAgB,GAAG9D,OAAO,CAAC+D,cAAc,GACzC/D,OAAO,CAAC+D,cAAc,GACtB7H,MAAM,CAAC8H,GAAG,CAACb,IAAI,EAAEU,IAAI,KAAK,KAAK,GAAGT,iBAAiB,GAAGC,gBAAgB,CAAC;IAC7E,MAAMY,SAAS,SAASC,gBAAgB,CAAClE,OAAO,EAAE8D,gBAAgB,EAAE9D,OAAO,CAACvB,EAAE,EAAEK,IAAI,CAAC;IACrF,IAAImF,SAAS,EAAE;MACXjE,OAAO,CAACmE,UAAU,CAACR,IAAI,CAAC,CAAC;MACzB,CAACJ,EAAE,GAAGvD,OAAO,CAACoE,mBAAmB,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAAC,CAAC;IACrF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI3D,OAAO,CAACvB,EAAE,CAACrB,OAAO,KAAK,WAAW,EAAE;MACpCiH,mBAAmB,CAACrE,OAAO,CAACvB,EAAE,CAAC;IACnC;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAIuB,OAAO,CAACsE,aAAa,KAAKzG,QAAQ,CAAC8D,aAAa,KAAK,IAAI,IAAI,CAAC3B,OAAO,CAACvB,EAAE,CAACwB,QAAQ,CAACpC,QAAQ,CAAC8D,aAAa,CAAC,CAAC,EAAE;MAC5G3B,OAAO,CAACvB,EAAE,CAACwC,KAAK,CAAC,CAAC;IACtB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACIjB,OAAO,CAACvB,EAAE,CAACuE,eAAe,CAAC,aAAa,CAAC;EAC7C,CAAC;EAAA,gBAhDKC,OAAOA,CAAAsB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAzB,IAAA,CAAA0B,KAAA,OAAAC,SAAA;EAAA;AAAA,GAgDZ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMR,mBAAmB;EAAA,IAAAS,KAAA,GAAA/G,iBAAA,CAAG,WAAOgH,SAAS,EAAK;IAC7C,IAAIC,eAAe,GAAGnH,QAAQ,CAAC8D,aAAa;IAC5C,IAAI,CAACqD,eAAe,EAAE;MAClB;IACJ;IACA,MAAMhE,UAAU,GAAGgE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAChE,UAAU;IAC/G,IAAIA,UAAU,EAAE;MACZ;MACAgE,eAAe,GAAGhE,UAAU,CAACX,aAAa,CAACP,oBAAoB,CAAC,IAAIkF,eAAe;IACvF;IACA,MAAMD,SAAS,CAACE,YAAY,CAAC,CAAC;IAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIpH,QAAQ,CAAC8D,aAAa,KAAK,IAAI,IAAI9D,QAAQ,CAAC8D,aAAa,KAAK9D,QAAQ,CAACqH,IAAI,EAAE;MAC7EF,eAAe,CAAC/D,KAAK,CAAC,CAAC;IAC3B;EACJ,CAAC;EAAA,gBAtCKoD,mBAAmBA,CAAAc,GAAA;IAAA,OAAAL,KAAA,CAAAF,KAAA,OAAAC,SAAA;EAAA;AAAA,GAsCxB;AACD,MAAMrH,OAAO;EAAA,IAAA4H,KAAA,GAAArH,iBAAA,CAAG,WAAOiC,OAAO,EAAEvC,IAAI,EAAEC,IAAI,EAAEyF,IAAI,EAAEkC,iBAAiB,EAAEC,gBAAgB,EAAExG,IAAI,EAAK;IAC5F,IAAIwE,EAAE,EAAEC,EAAE;IACV,IAAI,CAACvD,OAAO,CAACwD,SAAS,EAAE;MACpB,OAAO,KAAK;IAChB;IACA;AACJ;AACA;AACA;IACI,IAAI5H,GAAG,KAAK4F,SAAS,IAAIe,oBAAoB,CAAC3G,GAAG,CAAC,CAACiF,MAAM,KAAK,CAAC,EAAE;MAC7D8B,iBAAiB,CAAC,KAAK,CAAC;IAC5B;IACA3C,OAAO,CAACwD,SAAS,GAAG,KAAK;IACzB,IAAI;MACA;MACAxD,OAAO,CAACvB,EAAE,CAAC8G,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;MACtDxF,OAAO,CAACyF,WAAW,CAAC9B,IAAI,CAAC;QAAElG,IAAI;QAAEC;MAAK,CAAC,CAAC;MACxC,CAAC4F,EAAE,GAAGtD,OAAO,CAAC0F,oBAAoB,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAC;QAAElG,IAAI;QAAEC;MAAK,CAAC,CAAC;MAChG,MAAMmG,IAAI,GAAG7H,UAAU,CAACgE,OAAO,CAAC;MAChC,MAAM8D,gBAAgB,GAAG9D,OAAO,CAAC2F,cAAc,GACzC3F,OAAO,CAAC2F,cAAc,GACtBzJ,MAAM,CAAC8H,GAAG,CAACb,IAAI,EAAEU,IAAI,KAAK,KAAK,GAAGwB,iBAAiB,GAAGC,gBAAgB,CAAC;MAC7E;MACA,IAAI5H,IAAI,KAAKkI,OAAO,EAAE;QAClB,MAAM1B,gBAAgB,CAAClE,OAAO,EAAE8D,gBAAgB,EAAE9D,OAAO,CAACvB,EAAE,EAAEK,IAAI,CAAC;MACvE;MACAkB,OAAO,CAAC6F,UAAU,CAAClC,IAAI,CAAC;QAAElG,IAAI;QAAEC;MAAK,CAAC,CAAC;MACvC,CAAC6F,EAAE,GAAGvD,OAAO,CAAC8F,mBAAmB,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAAC;QAAElG,IAAI;QAAEC;MAAK,CAAC,CAAC;MAC/F;MACA;MACA,MAAMqI,UAAU,GAAG9I,gBAAgB,CAAC+G,GAAG,CAAChE,OAAO,CAAC,IAAI,EAAE;MACtD+F,UAAU,CAACC,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;MAC1CjJ,gBAAgB,CAACkJ,MAAM,CAACnG,OAAO,CAAC;MAChC;AACR;AACA;AACA;AACA;MACQA,OAAO,CAACvB,EAAE,CAACY,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC1CU,OAAO,CAACvB,EAAE,CAAC8G,KAAK,CAACa,cAAc,CAAC,gBAAgB,CAAC;MACjD;AACR;AACA;AACA;MACQ,IAAIpG,OAAO,CAACvB,EAAE,CAAC8C,SAAS,KAAKC,SAAS,EAAE;QACpCxB,OAAO,CAACvB,EAAE,CAAC8C,SAAS,GAAGC,SAAS;MACpC;IACJ,CAAC,CACD,OAAO6E,GAAG,EAAE;MACRC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;IACtB;IACArG,OAAO,CAACvB,EAAE,CAAC+H,MAAM,CAAC,CAAC;IACnBC,6BAA6B,CAAC,CAAC;IAC/B,OAAO,IAAI;EACf,CAAC;EAAA,gBAtDKjJ,OAAOA,CAAAkJ,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA;IAAA,OAAA5B,KAAA,CAAAR,KAAA,OAAAC,SAAA;EAAA;AAAA,GAsDZ;AACD,MAAMnF,UAAU,GAAI9D,GAAG,IAAK;EACxB,OAAOA,GAAG,CAACyE,aAAa,CAAC,SAAS,CAAC,IAAIzE,GAAG,CAACsJ,IAAI;AACnD,CAAC;AACD,MAAMhB,gBAAgB;EAAA,IAAA+C,KAAA,GAAAlJ,iBAAA,CAAG,WAAOiC,OAAO,EAAE8D,gBAAgB,EAAEoD,MAAM,EAAEpI,IAAI,EAAK;IACxE;IACAoI,MAAM,CAAC7H,SAAS,CAACmH,MAAM,CAAC,gBAAgB,CAAC;IACzC,MAAMW,OAAO,GAAGnH,OAAO,CAACvB,EAAE;IAC1B,MAAM2I,SAAS,GAAGtD,gBAAgB,CAACqD,OAAO,EAAErI,IAAI,CAAC;IACjD,IAAI,CAACkB,OAAO,CAACqH,QAAQ,IAAI,CAACnL,MAAM,CAACoL,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE;MAC3DF,SAAS,CAACG,QAAQ,CAAC,CAAC,CAAC;IACzB;IACA,IAAIvH,OAAO,CAACsE,aAAa,EAAE;MACvB8C,SAAS,CAACI,cAAc,CAAC,MAAM;QAC3B,MAAM7F,aAAa,GAAGuF,MAAM,CAACO,aAAa,CAAC9F,aAAa;QACxD,IAAIA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC+F,OAAO,CAAC,+BAA+B,CAAC,EAAE;UACtH/F,aAAa,CAACgG,IAAI,CAAC,CAAC;QACxB;MACJ,CAAC,CAAC;IACN;IACA,MAAMC,SAAS,GAAG3K,gBAAgB,CAAC+G,GAAG,CAAChE,OAAO,CAAC,IAAI,EAAE;IACrD/C,gBAAgB,CAAC4K,GAAG,CAAC7H,OAAO,EAAE,CAAC,GAAG4H,SAAS,EAAER,SAAS,CAAC,CAAC;IACxD,MAAMA,SAAS,CAACU,IAAI,CAAC,CAAC;IACtB,OAAO,IAAI;EACf,CAAC;EAAA,gBApBK5D,gBAAgBA,CAAA6D,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;IAAA,OAAAjB,KAAA,CAAArC,KAAA,OAAAC,SAAA;EAAA;AAAA,GAoBrB;AACD,MAAMsD,WAAW,GAAGA,CAAChJ,OAAO,EAAEiJ,SAAS,KAAK;EACxC,IAAIvI,OAAO;EACX,MAAMwI,OAAO,GAAG,IAAIzI,OAAO,CAAE0I,CAAC,IAAMzI,OAAO,GAAGyI,CAAE,CAAC;EACjDC,SAAS,CAACpJ,OAAO,EAAEiJ,SAAS,EAAGI,KAAK,IAAK;IACrC3I,OAAO,CAAC2I,KAAK,CAAC1G,MAAM,CAAC;EACzB,CAAC,CAAC;EACF,OAAOuG,OAAO;AAClB,CAAC;AACD,MAAME,SAAS,GAAGA,CAACpJ,OAAO,EAAEiJ,SAAS,EAAEK,QAAQ,KAAK;EAChD,MAAMC,OAAO,GAAIvH,EAAE,IAAK;IACpBzE,mBAAmB,CAACyC,OAAO,EAAEiJ,SAAS,EAAEM,OAAO,CAAC;IAChDD,QAAQ,CAACtH,EAAE,CAAC;EAChB,CAAC;EACD1E,gBAAgB,CAAC0C,OAAO,EAAEiJ,SAAS,EAAEM,OAAO,CAAC;AACjD,CAAC;AACD,MAAMC,QAAQ,GAAIjL,IAAI,IAAK;EACvB,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAKsE,QAAQ;AACjD,CAAC;AACD,MAAM4G,WAAW,GAAIC,CAAC,IAAKA,CAAC,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAGA,CAACJ,OAAO,EAAEK,GAAG,KAAK;EAC/B,IAAI,OAAOL,OAAO,KAAK,UAAU,EAAE;IAC/B,MAAMM,GAAG,GAAG9M,MAAM,CAAC8H,GAAG,CAAC,WAAW,EAAE4E,WAAW,CAAC;IAChD,OAAOI,GAAG,CAAC,MAAM;MACb,IAAI;QACA,OAAON,OAAO,CAACK,GAAG,CAAC;MACvB,CAAC,CACD,OAAOE,CAAC,EAAE;QACN,MAAMA,CAAC;MACX;IACJ,CAAC,CAAC;EACN;EACA,OAAOzH,SAAS;AACpB,CAAC;AACD,MAAMQ,QAAQ,GAAG,UAAU;AAC3B,MAAM4D,OAAO,GAAG,SAAS;AACzB,MAAMsD,wBAAwB,GAAG,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAIhJ,GAAG,IAAK;EACtC,IAAIiJ,MAAM,GAAG,KAAK;EAClB,IAAIC,eAAe;EACnB,MAAMC,YAAY,GAAGlN,YAAY,CAAC,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMmN,WAAW,GAAGA,CAACC,KAAK,GAAG,KAAK,KAAK;IACnC,IAAIH,eAAe,IAAI,CAACG,KAAK,EAAE;MAC3B,OAAO;QACHC,QAAQ,EAAEJ,eAAe;QACzBD;MACJ,CAAC;IACL;IACA,MAAM;MAAE3K,EAAE;MAAEgB,aAAa;MAAEgK;IAAS,CAAC,GAAGtJ,GAAG;IAC3C;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMuJ,QAAQ,GAAGjL,EAAE,CAACkL,UAAU;IAC9BP,MAAM,GAAGM,QAAQ,KAAK,IAAI,IAAI,CAACjK,aAAa;IAC5C4J,eAAe,GAAGD,MAAM,GAAGK,QAAQ,IAAIH,YAAY,GAAGG,QAAQ;IAC9D,OAAO;MAAEL,MAAM;MAAEK,QAAQ,EAAEJ;IAAgB,CAAC;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,MAAMO,eAAe;IAAA,IAAAC,KAAA,GAAA9L,iBAAA,CAAG,WAAO+L,SAAS,EAAK;MACzC,MAAM;QAAEL;MAAS,CAAC,GAAGF,WAAW,CAAC,IAAI,CAAC;MACtC,IAAIE,QAAQ,EAAE;QACV,aAAaA,QAAQ,CAACG,eAAe,CAACzJ,GAAG,CAAC1B,EAAE,EAAEqL,SAAS,CAAC;MAC5D;MACA,MAAM;QAAErK;MAAc,CAAC,GAAGU,GAAG;MAC7B,IAAIV,aAAa,IAAIqK,SAAS,KAAKtI,SAAS,EAAE;QAC1C,MAAM,IAAIuI,KAAK,CAAC,+BAA+B,CAAC;MACpD;MACA,OAAO,IAAI;IACf,CAAC;IAAA,gBAVKH,eAAeA,CAAAI,IAAA;MAAA,OAAAH,KAAA,CAAAjF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAUpB;EACD;AACJ;AACA;EACI,MAAMoF,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,MAAM;MAAER;IAAS,CAAC,GAAGF,WAAW,CAAC,CAAC;IAClC,IAAIE,QAAQ,IAAItJ,GAAG,CAAC1B,EAAE,KAAK+C,SAAS,EAAE;MAClCiI,QAAQ,CAACQ,iBAAiB,CAAC9J,GAAG,CAAC1B,EAAE,CAACyL,aAAa,EAAE/J,GAAG,CAAC1B,EAAE,CAAC;IAC5D;EACJ,CAAC;EACD,OAAO;IACHmL,eAAe;IACfK;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;EAClC,IAAIC,yBAAyB;EAC7B;AACJ;AACA;EACI,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAID,yBAAyB,EAAE;MAC3BA,yBAAyB,CAAC,CAAC;MAC3BA,yBAAyB,GAAG5I,SAAS;IACzC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM8I,gBAAgB,GAAGA,CAAC7L,EAAE,EAAE8L,OAAO,KAAK;IACtCF,mBAAmB,CAAC,CAAC;IACrB,MAAMG,SAAS,GAAGD,OAAO,KAAK/I,SAAS,GAAG3D,QAAQ,CAAC4M,cAAc,CAACF,OAAO,CAAC,GAAG,IAAI;IACjF,IAAI,CAACC,SAAS,EAAE;MACZ1N,eAAe,CAAC,kCAAkCyN,OAAO,gIAAgI,EAAE9L,EAAE,CAAC;MAC9L;IACJ;IACA,MAAMiM,2BAA2B,GAAGA,CAACC,QAAQ,EAAE5F,SAAS,KAAK;MACzD,MAAM6F,WAAW,GAAGA,CAAA,KAAM;QACtB7F,SAAS,CAAC9B,OAAO,CAAC,CAAC;MACvB,CAAC;MACD0H,QAAQ,CAAClO,gBAAgB,CAAC,OAAO,EAAEmO,WAAW,CAAC;MAC/C,OAAO,MAAM;QACTD,QAAQ,CAACjO,mBAAmB,CAAC,OAAO,EAAEkO,WAAW,CAAC;MACtD,CAAC;IACL,CAAC;IACDR,yBAAyB,GAAGM,2BAA2B,CAACF,SAAS,EAAE/L,EAAE,CAAC;EAC1E,CAAC;EACD,OAAO;IACH6L,gBAAgB;IAChBD;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM5G,6BAA6B,GAAIoH,iBAAiB,IAAK;EACzD,IAAIvH,EAAE;EACN,IAAI1H,GAAG,KAAK4F,SAAS,EACjB;EACJ,MAAMiB,QAAQ,GAAGF,oBAAoB,CAAC3G,GAAG,CAAC;EAC1C,KAAK,IAAIkP,CAAC,GAAGrI,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAEiK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,MAAMC,gBAAgB,GAAGtI,QAAQ,CAACqI,CAAC,CAAC;IACpC,MAAME,oBAAoB,GAAG,CAAC1H,EAAE,GAAGb,QAAQ,CAACqI,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIxH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGuH,iBAAiB;IACtG;AACR;AACA;AACA;AACA;IACQ,IAAIG,oBAAoB,CAACnM,YAAY,CAAC,aAAa,CAAC,IAAImM,oBAAoB,CAAC5N,OAAO,KAAK,WAAW,EAAE;MAClG2N,gBAAgB,CAAChI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACxD;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM0D,6BAA6B,GAAGA,CAAA,KAAM;EACxC,IAAI7K,GAAG,KAAK4F,SAAS,EACjB;EACJ,MAAMiB,QAAQ,GAAGF,oBAAoB,CAAC3G,GAAG,CAAC;EAC1C,KAAK,IAAIkP,CAAC,GAAGrI,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAEiK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,MAAMG,cAAc,GAAGxI,QAAQ,CAACqI,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;AACA;IACQG,cAAc,CAACjI,eAAe,CAAC,aAAa,CAAC;IAC7C;AACR;AACA;AACA;AACA;IACQ,IAAIiI,cAAc,CAAC7N,OAAO,KAAK,WAAW,EAAE;MACxC;IACJ;EACJ;AACJ,CAAC;AAED,SAAS4E,QAAQ,IAAIkJ,CAAC,EAAEtF,OAAO,IAAIuF,CAAC,EAAEjC,wBAAwB,IAAIkC,CAAC,EAAEnN,eAAe,IAAIzB,CAAC,EAAE0B,qBAAqB,IAAInC,CAAC,EAAEuC,iBAAiB,IAAIrC,CAAC,EAAEkN,wBAAwB,IAAIxN,CAAC,EAAEwO,uBAAuB,IAAIlB,CAAC,EAAEhG,OAAO,IAAI3G,CAAC,EAAEkB,OAAO,IAAIb,CAAC,EAAEwL,WAAW,IAAIU,CAAC,EAAEF,QAAQ,IAAImC,CAAC,EAAEtM,cAAc,IAAI6M,CAAC,EAAEzM,YAAY,IAAI0M,CAAC,EAAEnN,iBAAiB,IAAIoN,CAAC,EAAEnN,eAAe,IAAIoN,CAAC,EAAEtL,oBAAoB,IAAIuL,CAAC,EAAEzN,mBAAmB,IAAIwE,CAAC,EAAEnE,gBAAgB,IAAIxB,CAAC,EAAEiM,QAAQ,IAAI4C,CAAC,EAAEnN,eAAe,IAAIoN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}