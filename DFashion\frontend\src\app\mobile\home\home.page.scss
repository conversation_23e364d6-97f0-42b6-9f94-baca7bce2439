// Header Styles
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .logo {
    height: 28px;
    width: auto;
  }
  
  .app-name {
    font-weight: 600;
    font-size: 18px;
  }
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
  
  ion-spinner {
    --color: var(--ion-color-primary);
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

// Hero Section
.hero-section {
  margin-bottom: 20px;
  
  .hero-slides {
    height: 200px;
    
    .slide-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 20px;
      border-radius: 12px;
      margin: 0 10px;
      
      h2 {
        color: white;
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
      }
      
      p {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        margin-bottom: 16px;
      }
      
      ion-button {
        --border-width: 2px;
        --border-color: white;
        font-weight: 600;
      }
    }
  }
}

// Section Styles
.section {
  margin-bottom: 24px;
  padding: 0 16px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0;
    }
    
    ion-button {
      --color: var(--ion-color-primary);
      font-size: 14px;
    }
  }
}

// Categories Grid
.categories-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 8px;
  
  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:active {
      transform: scale(0.95);
    }
    
    .category-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      
      ion-icon {
        font-size: 28px;
        color: white;
      }
    }
    
    span {
      font-size: 12px;
      font-weight: 500;
      color: var(--ion-color-dark);
    }
  }
}

// Stories Slides
.stories-slides {
  margin: 0 -16px;
  padding: 0 16px;
  
  .story-item {
    cursor: pointer;
    
    .story-image {
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 8px;
      border: 3px solid var(--ion-color-primary);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .story-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.3);
        
        ion-icon {
          color: white;
          font-size: 24px;
        }
      }
    }
    
    .story-title {
      font-size: 12px;
      text-align: center;
      margin: 0;
      color: var(--ion-color-dark);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// Products Slides
.products-slides {
  margin: 0 -16px;
  padding: 0 16px;
  
  .product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:active {
      transform: scale(0.98);
    }
    
    .product-image {
      position: relative;
      height: 160px;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .product-badge {
        position: absolute;
        top: 8px;
        left: 8px;
        background: var(--ion-color-danger);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
      }
    }
    
    .product-info {
      padding: 12px;
      
      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--ion-color-dark);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .product-price {
        margin-bottom: 8px;
        
        .current-price {
          font-size: 16px;
          font-weight: 700;
          color: var(--ion-color-primary);
        }
        
        .original-price {
          font-size: 12px;
          color: var(--ion-color-medium);
          text-decoration: line-through;
          margin-left: 8px;
        }
      }
      
      .product-rating {
        display: flex;
        align-items: center;
        gap: 4px;
        
        ion-icon {
          font-size: 12px;
          color: var(--ion-color-medium);
          
          &.filled {
            color: #ffc409;
          }
        }
        
        span {
          font-size: 12px;
          color: var(--ion-color-medium);
        }
      }
    }
  }
}

// Posts Grid
.posts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  
  .post-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:active {
      transform: scale(0.98);
    }
    
    .post-image {
      position: relative;
      height: 120px;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .post-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.3);
        opacity: 0;
        transition: opacity 0.2s ease;
        
        ion-icon {
          color: white;
          font-size: 32px;
        }
      }
      
      &:hover .post-overlay {
        opacity: 1;
      }
    }
    
    .post-content {
      padding: 12px;
      
      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--ion-color-dark);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      p {
        font-size: 12px;
        color: var(--ion-color-medium);
        margin: 0 0 8px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .post-stats {
        display: flex;
        gap: 12px;
        
        span {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: var(--ion-color-medium);
          
          ion-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Bottom Spacing
.bottom-spacing {
  height: 80px;
}
