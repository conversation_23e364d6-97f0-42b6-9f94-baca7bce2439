import { Injectable } from '@angular/core';
import { LoadingController } from '@ionic/angular';
import { BehaviorSubject, Observable, finalize } from 'rxjs';

export interface LoadingState {
  [key: string]: {
    isLoading: boolean;
    message?: string;
    progress?: number;
    startTime?: Date;
  };
}

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private loadingCount = 0;
  private currentLoading: HTMLIonLoadingElement | null = null;

  // Enhanced loading state management
  private loadingStateSubject = new BehaviorSubject<LoadingState>({});
  public loadingState$ = this.loadingStateSubject.asObservable();

  public isLoading$ = this.loadingSubject.asObservable();

  constructor(private loadingController: LoadingController) {}

  async show(message: string = 'Loading...', duration?: number): Promise<void> {
    this.loadingCount++;
    
    if (this.loadingCount === 1) {
      this.loadingSubject.next(true);
      
      this.currentLoading = await this.loadingController.create({
        message,
        duration,
        spinner: 'crescent',
        cssClass: 'custom-loading'
      });
      
      await this.currentLoading.present();
    }
  }

  async hide(): Promise<void> {
    if (this.loadingCount > 0) {
      this.loadingCount--;
    }
    
    if (this.loadingCount === 0) {
      this.loadingSubject.next(false);
      
      if (this.currentLoading) {
        await this.currentLoading.dismiss();
        this.currentLoading = null;
      }
    }
  }

  async hideAll(): Promise<void> {
    this.loadingCount = 0;
    this.loadingSubject.next(false);
    
    if (this.currentLoading) {
      await this.currentLoading.dismiss();
      this.currentLoading = null;
    }
  }

  isLoading(): boolean {
    return this.loadingSubject.value;
  }

  // Enhanced loading methods
  startLoading(key: string, message?: string): void {
    const currentState = this.loadingStateSubject.value;
    const newState = {
      ...currentState,
      [key]: {
        isLoading: true,
        message,
        progress: 0,
        startTime: new Date()
      }
    };

    this.loadingStateSubject.next(newState);
    this.updateGlobalLoading();
  }

  stopLoading(key: string): void {
    const currentState = this.loadingStateSubject.value;
    const newState = { ...currentState };

    if (newState[key]) {
      delete newState[key];
    }

    this.loadingStateSubject.next(newState);
    this.updateGlobalLoading();
  }

  updateProgress(key: string, progress: number, message?: string): void {
    const currentState = this.loadingStateSubject.value;

    if (currentState[key]) {
      const newState = {
        ...currentState,
        [key]: {
          ...currentState[key],
          progress: Math.max(0, Math.min(100, progress)),
          message: message || currentState[key].message
        }
      };

      this.loadingStateSubject.next(newState);
    }
  }

  isLoadingKey(key: string): Observable<boolean> {
    return new Observable(observer => {
      this.loadingState$.subscribe(state => {
        observer.next(!!state[key]?.isLoading);
      });
    });
  }

  isLoadingKeySync(key: string): boolean {
    const currentState = this.loadingStateSubject.value;
    return !!currentState[key]?.isLoading;
  }

  private updateGlobalLoading(): void {
    const currentState = this.loadingStateSubject.value;
    const hasLoading = Object.values(currentState).some(state => state.isLoading);
    this.loadingSubject.next(hasLoading);
  }

  wrapWithLoading<T>(source: Observable<T>, key: string, message?: string): Observable<T> {
    this.startLoading(key, message);
    return source.pipe(finalize(() => this.stopLoading(key)));
  }

  // Predefined loading keys
  static readonly KEYS = {
    LOGIN: 'auth.login',
    LOGOUT: 'auth.logout',
    REGISTER: 'auth.register',
    PROFILE_LOAD: 'user.profile.load',
    PRODUCTS_LOAD: 'shop.products.load',
    CART_LOAD: 'cart.load',
    SEARCH: 'search.execute',
    BRANDS_LOAD: 'shop.brands.load',
    GLOBAL: 'global'
  } as const;
}
