{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/story.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/media.service\";\nconst _c0 = [\"storiesSlider\"];\nexport class StoriesComponent {\n  constructor(storyService, authService, router, mediaService) {\n    this.storyService = storyService;\n    this.authService = authService;\n    this.router = router;\n    this.mediaService = mediaService;\n    this.storyGroups = [];\n    this.currentUser = null;\n    // Slider properties\n    this.translateX = 0;\n    this.currentSlide = 0;\n    this.isTransitioning = false;\n    // Navigation properties\n    this.canSlideLeft = false;\n    this.canSlideRight = false;\n    this.showArrows = false;\n    this.showDots = false;\n    this.dots = [];\n    // Touch properties\n    this.touchStartX = 0;\n    this.touchCurrentX = 0;\n    this.isDragging = false;\n    // Responsive properties\n    this.slidesPerView = 1;\n    this.slideWidth = 0;\n    this.autoSlideDelay = 3000; // 3 seconds\n    this.isAutoSliding = true;\n    // Auto-loading properties\n    this.isLoadingMore = false;\n    this.hasMoreStories = true;\n    this.currentPage = 1;\n    this.storiesPerPage = 10;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.loadStories();\n  }\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n  loadStories(page = 1, append = false) {\n    if (this.isLoadingMore) return;\n    this.isLoadingMore = true;\n    this.storyService.getStories(page, this.storiesPerPage).subscribe({\n      next: response => {\n        if (response.storyGroups && response.storyGroups.length > 0) {\n          if (append) {\n            this.storyGroups = [...this.storyGroups, ...response.storyGroups];\n          } else {\n            this.storyGroups = response.storyGroups;\n          }\n          // Check if there are more stories\n          this.hasMoreStories = response.storyGroups.length === this.storiesPerPage;\n          // Update slider after stories load\n          setTimeout(() => {\n            this.calculateResponsiveSettings();\n            this.updateSliderState();\n            if (!append) {\n              this.startAutoSlide();\n            }\n          }, 100);\n        } else {\n          console.log('No stories available from database');\n          if (!append) {\n            this.storyGroups = [];\n          }\n          this.hasMoreStories = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        if (!append) {\n          this.storyGroups = [];\n        }\n        this.hasMoreStories = false;\n        this.isLoadingMore = false;\n      }\n    });\n  }\n  loadMoreStories() {\n    if (this.hasMoreStories && !this.isLoadingMore) {\n      this.currentPage++;\n      this.loadStories(this.currentPage, true);\n    }\n  }\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Check if we need to load more stories\n      if (this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      } else {\n        // Reset to beginning\n        this.currentSlide = 0;\n        this.updateSliderPosition();\n      }\n    }\n  }\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n      // Check if we're near the end and need to load more\n      if (this.currentSlide >= maxSlide - 1 && this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      }\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  goToSlide(slideIndex) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n  getMaxSlide() {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n  // Touch gesture methods\n  onTouchStart(event) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n    this.isDragging = false;\n  }\n  // Image handling methods\n  getSafeImageUrl(url) {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n  onImageError(event) {\n    this.mediaService.handleImageError(event, 'user');\n  }\n  openStoryViewer(storyGroup) {\n    // Navigate to story viewer with the selected story group\n    this.router.navigate(['/stories', storyGroup.user._id], {\n      queryParams: {\n        storyId: storyGroup.stories[0]._id,\n        source: 'home'\n      }\n    });\n  }\n  openAddStory() {\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.showStoryCreationModal();\n  }\n  showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n    // Add global function for file handling\n    window.handleFileSelect = input => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n  showFilePreview(file) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ? `<video src=\"${fileURL}\" controls autoplay muted></video>` : `<img src=\"${fileURL}\" alt=\"Story preview\">`}\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n  static {\n    this.ɵfac = function StoriesComponent_Factory(t) {\n      return new (t || StoriesComponent)(i0.ɵɵdirectiveInject(i1.StoryService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesComponent,\n      selectors: [[\"app-stories\"]],\n      viewQuery: function StoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      template: function StoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-instagram-stories\");\n        }\n      },\n      dependencies: [CommonModule],\n      styles: [\".stories-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  width: 100%;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0;\\n  will-change: transform;\\n  padding: 16px 0;\\n}\\n\\n.story-slide[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 0 12px;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 80px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:first-child {\\n  padding-left: 16px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:last-child {\\n  padding-right: 16px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  position: relative;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  padding: 3px;\\n  background: #fafafa;\\n  border: 1px solid #dbdbdb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.avatar-ring.has-story[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  border: none;\\n  animation: _ngcontent-%COMP%_storyPulse 2s infinite;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.avatar-ring.viewed-story[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  border: 1px solid #dbdbdb;\\n  animation: none;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border: 2px dashed #dbdbdb;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  background: rgba(0, 149, 246, 0.05);\\n}\\n\\n.avatar-ring.loading-ring[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 2px solid #e9ecef;\\n  animation: _ngcontent-%COMP%_loadingPulse 1.5s ease-in-out infinite;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 3px solid #e9ecef;\\n  border-top: 3px solid var(--primary-color);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loadingPulse {\\n  0%, 100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 0.7;\\n    transform: scale(1.05);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-slide[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n}\\n.loading-slide[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-style: italic;\\n}\\n\\n.avatar-image[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #fff;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 20px;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyPulse {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  }\\n  50% {\\n    transform: scale(1.02);\\n    box-shadow: 0 4px 16px rgba(240, 148, 51, 0.3);\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  }\\n}\\n\\n\\n.auto-slide-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  z-index: 5;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  animation: _ngcontent-%COMP%_fadeInSlide 0.3s ease;\\n}\\n\\n.slide-progress[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: var(--primary-color, #007bff);\\n  animation: _ngcontent-%COMP%_slideProgress 3s linear infinite;\\n}\\n\\n.slide-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInSlide {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideProgress {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.2);\\n    opacity: 0.7;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.95);\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\\n  color: #262626;\\n  font-size: 16px;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #fff;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n  transform: translateY(-50%);\\n}\\n\\n.nav-arrow.nav-left[_ngcontent-%COMP%] {\\n  left: 120px; \\n\\n}\\n\\n.nav-arrow.nav-right[_ngcontent-%COMP%] {\\n  right: 12px;\\n}\\n\\n\\n\\n.nav-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 12px 0 8px;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #dbdbdb;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.nav-dot.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  transform: scale(1.2);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  opacity: 0.7;\\n}\\n\\n\\n\\n@media (min-width: 1024px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    padding: 0 15px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 72px;\\n    height: 72px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    max-width: 80px;\\n  }\\n  .nav-arrow.nav-left[_ngcontent-%COMP%] {\\n    left: 140px; \\n\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 1023px) and (min-width: 768px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 80px;\\n    padding: 0 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n    font-size: 14px;\\n  }\\n  .nav-arrow.nav-left[_ngcontent-%COMP%] {\\n    left: 120px; \\n\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 767px) and (min-width: 481px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 14px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    padding: 0 10px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n}\\n\\n\\n@media (max-width: 480px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 65px;\\n    padding: 0 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 12px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 52px;\\n    height: 52px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 55px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n    padding: 8px 0 6px;\\n  }\\n  .nav-dot[_ngcontent-%COMP%] {\\n    width: 6px;\\n    height: 6px;\\n  }\\n}\\n\\n\\n@media (max-width: 360px) {\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    padding: 0 6px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 8px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 50px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "StoriesComponent", "constructor", "storyService", "authService", "router", "mediaService", "storyGroups", "currentUser", "translateX", "currentSlide", "isTransitioning", "canSlideLeft", "canSlideRight", "showArrows", "showDots", "dots", "touchStartX", "touchCurrentX", "isDragging", "<PERSON><PERSON><PERSON><PERSON>iew", "slideWidth", "autoSlideDelay", "isAutoSliding", "isLoadingMore", "hasMoreStories", "currentPage", "storiesPerPage", "ngOnInit", "currentUser$", "subscribe", "user", "loadStories", "ngAfterViewInit", "setTimeout", "calculateResponsiveSettings", "updateSliderState", "startAutoSlide", "window", "addEventListener", "ngOnDestroy", "stopAutoSlide", "storiesSlider", "containerWidth", "nativeElement", "clientWidth", "screenWidth", "innerWidth", "Math", "floor", "max", "totalSlides", "length", "totalPages", "ceil", "Array", "fill", "map", "_", "i", "page", "append", "getStories", "next", "response", "console", "log", "error", "loadMoreStories", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "pauseAutoSlide", "resumeAutoSlide", "maxSlide", "getMaxSlide", "slideRight", "updateSliderPosition", "slideLeft", "min", "goToSlide", "slideIndex", "onScroll", "onTouchStart", "event", "touches", "clientX", "onTouchMove", "deltaX", "abs", "preventDefault", "onTouchEnd", "threshold", "getSafeImageUrl", "url", "onImageError", "handleImageError", "openStoryViewer", "storyGroup", "navigate", "_id", "queryParams", "storyId", "stories", "source", "openAddStory", "showStoryCreationModal", "modalOverlay", "document", "createElement", "className", "innerHTML", "styles", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "handleFileSelect", "input", "files", "file", "name", "type", "showFilePreview", "remove", "previewModal", "fileURL", "URL", "createObjectURL", "isVideo", "startsWith", "previewStyles", "onMouseEnter", "onMouseLeave", "i0", "ɵɵdirectiveInject", "i1", "StoryService", "i2", "AuthService", "i3", "Router", "i4", "MediaService", "selectors", "viewQuery", "StoriesComponent_Query", "rf", "ctx", "ɵɵelement"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { MediaService } from '../../../../core/services/media.service';\n\nimport { StoryService } from '../../../../core/services/story.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { StoryGroup, Story } from '../../../../core/models/story.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-stories',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './stories.component.html',\n  styleUrls: ['./stories.component.scss']\n\n\n})\nexport class StoriesComponent implements OnInit, AfterViewInit {\n  @ViewChild('storiesSlider') storiesSlider!: ElementRef<HTMLDivElement>;\n\n  storyGroups: StoryGroup[] = [];\n  currentUser: User | null = null;\n\n  // Slider properties\n  translateX = 0;\n  currentSlide = 0;\n  isTransitioning = false;\n\n  // Navigation properties\n  canSlideLeft = false;\n  canSlideRight = false;\n  showArrows = false;\n  showDots = false;\n  dots: number[] = [];\n\n  // Touch properties\n  touchStartX = 0;\n  touchCurrentX = 0;\n  isDragging = false;\n\n  // Responsive properties\n  slidesPerView = 1;\n  slideWidth = 0;\n\n  // Auto-slide properties\n  autoSlideInterval: any;\n  autoSlideDelay = 3000; // 3 seconds\n  isAutoSliding = true;\n\n  // Auto-loading properties\n  isLoadingMore = false;\n  hasMoreStories = true;\n  currentPage = 1;\n  storiesPerPage = 10;\n\n  constructor(\n    private storyService: StoryService,\n    private authService: AuthService,\n    private router: Router,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    this.loadStories();\n  }\n\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n\n  loadStories(page: number = 1, append: boolean = false) {\n    if (this.isLoadingMore) return;\n\n    this.isLoadingMore = true;\n\n    this.storyService.getStories(page, this.storiesPerPage).subscribe({\n      next: (response) => {\n        if (response.storyGroups && response.storyGroups.length > 0) {\n          if (append) {\n            this.storyGroups = [...this.storyGroups, ...response.storyGroups];\n          } else {\n            this.storyGroups = response.storyGroups;\n          }\n\n          // Check if there are more stories\n          this.hasMoreStories = response.storyGroups.length === this.storiesPerPage;\n\n          // Update slider after stories load\n          setTimeout(() => {\n            this.calculateResponsiveSettings();\n            this.updateSliderState();\n            if (!append) {\n              this.startAutoSlide();\n            }\n          }, 100);\n        } else {\n          console.log('No stories available from database');\n          if (!append) {\n            this.storyGroups = [];\n          }\n          this.hasMoreStories = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        if (!append) {\n          this.storyGroups = [];\n        }\n        this.hasMoreStories = false;\n        this.isLoadingMore = false;\n      }\n    });\n  }\n\n  loadMoreStories() {\n    if (this.hasMoreStories && !this.isLoadingMore) {\n      this.currentPage++;\n      this.loadStories(this.currentPage, true);\n    }\n  }\n\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Check if we need to load more stories\n      if (this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      } else {\n        // Reset to beginning\n        this.currentSlide = 0;\n        this.updateSliderPosition();\n      }\n    }\n  }\n\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n\n      // Check if we're near the end and need to load more\n      if (this.currentSlide >= maxSlide - 1 && this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      }\n\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  goToSlide(slideIndex: number) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n\n  getMaxSlide(): number {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n\n  // Touch gesture methods\n  onTouchStart(event: TouchEvent) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n\n    this.isDragging = false;\n  }\n\n  // Image handling methods\n  getSafeImageUrl(url: string | undefined): string {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n\n  onImageError(event: Event): void {\n    this.mediaService.handleImageError(event, 'user');\n  }\n\n\n\n  openStoryViewer(storyGroup: StoryGroup) {\n    // Navigate to story viewer with the selected story group\n    this.router.navigate(['/stories', storyGroup.user._id], {\n      queryParams: {\n        storyId: storyGroup.stories[0]._id,\n        source: 'home'\n      }\n    });\n  }\n\n  openAddStory() {\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.showStoryCreationModal();\n  }\n\n  private showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n\n    // Add global function for file handling\n    (window as any).handleFileSelect = (input: HTMLInputElement) => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n\n  private showFilePreview(file: File) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ?\n            `<video src=\"${fileURL}\" controls autoplay muted></video>` :\n            `<img src=\"${fileURL}\" alt=\"Story preview\">`\n          }\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n\n\n\n\n\n\n\n\n\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n\n\n}\n", "<!-- Instagram-style Stories -->\n<app-instagram-stories></app-instagram-stories>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;AAkB9C,OAAM,MAAOC,gBAAgB;EAsC3BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAH1B,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAvCtB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,IAAI,GAAa,EAAE;IAEnB;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,CAAC;IAId,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE;EAOhB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACxB,WAAW,CAACyB,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACvB,WAAW,GAAGuB,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;IAEP;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACJ,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAN,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACO,aAAa,EAAE;IAEzB,MAAMC,cAAc,GAAG,IAAI,CAACD,aAAa,CAACE,aAAa,CAACC,WAAW;IACnE,MAAMC,WAAW,GAAGR,MAAM,CAACS,UAAU;IAErC;IACA,IAAID,WAAW,IAAI,IAAI,EAAE;MACvB,IAAI,CAAC1B,aAAa,GAAG4B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACtB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACP,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAI+B,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAAC1B,aAAa,GAAG4B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACtB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACP,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAI+B,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAAC1B,aAAa,GAAG4B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACtB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACP,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM,IAAI+B,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAAC1B,aAAa,GAAG4B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACtB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACP,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM;MACL,IAAI,CAACK,aAAa,GAAG4B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACtB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACP,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;;IAGtB;IACA,IAAI,CAACK,aAAa,GAAG4B,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC9B,aAAa,EAAE,CAAC,CAAC;IAEpD;IACA,IAAI,IAAI,CAACL,QAAQ,EAAE;MACjB,MAAMoC,WAAW,GAAG,IAAI,CAAC5C,WAAW,CAAC6C,MAAM,GAAG,CAAC,CAAC,CAAC;MACjD,MAAMC,UAAU,GAAGL,IAAI,CAACM,IAAI,CAACH,WAAW,GAAG,IAAI,CAAC/B,aAAa,CAAC;MAC9D,IAAI,CAACJ,IAAI,GAAGuC,KAAK,CAACF,UAAU,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;;EAE1D;EAEA3B,WAAWA,CAAC4B,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACnD,IAAI,IAAI,CAACrC,aAAa,EAAE;IAExB,IAAI,CAACA,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACrB,YAAY,CAAC2D,UAAU,CAACF,IAAI,EAAE,IAAI,CAACjC,cAAc,CAAC,CAACG,SAAS,CAAC;MAChEiC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACzD,WAAW,IAAIyD,QAAQ,CAACzD,WAAW,CAAC6C,MAAM,GAAG,CAAC,EAAE;UAC3D,IAAIS,MAAM,EAAE;YACV,IAAI,CAACtD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,EAAE,GAAGyD,QAAQ,CAACzD,WAAW,CAAC;WAClE,MAAM;YACL,IAAI,CAACA,WAAW,GAAGyD,QAAQ,CAACzD,WAAW;;UAGzC;UACA,IAAI,CAACkB,cAAc,GAAGuC,QAAQ,CAACzD,WAAW,CAAC6C,MAAM,KAAK,IAAI,CAACzB,cAAc;UAEzE;UACAO,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,2BAA2B,EAAE;YAClC,IAAI,CAACC,iBAAiB,EAAE;YACxB,IAAI,CAACyB,MAAM,EAAE;cACX,IAAI,CAACxB,cAAc,EAAE;;UAEzB,CAAC,EAAE,GAAG,CAAC;SACR,MAAM;UACL4B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,IAAI,CAACL,MAAM,EAAE;YACX,IAAI,CAACtD,WAAW,GAAG,EAAE;;UAEvB,IAAI,CAACkB,cAAc,GAAG,KAAK;;QAE7B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACN,MAAM,EAAE;UACX,IAAI,CAACtD,WAAW,GAAG,EAAE;;QAEvB,IAAI,CAACkB,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACJ;EAEA4C,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3C,cAAc,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;MAC9C,IAAI,CAACE,WAAW,EAAE;MAClB,IAAI,CAACM,WAAW,CAAC,IAAI,CAACN,WAAW,EAAE,IAAI,CAAC;;EAE5C;EAEA;EACAW,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC9B,WAAW,CAAC6C,MAAM,IAAI,IAAI,CAAChC,aAAa,EAAE,OAAO,CAAC;IAE3D,IAAI,CAACqB,aAAa,EAAE,CAAC,CAAC;IACtB,IAAI,CAAC4B,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAAC/C,aAAa,EAAE;QACtB,IAAI,CAACgD,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACjD,cAAc,CAAC;EACzB;EAEAmB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC4B,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAAClD,aAAa,GAAG,KAAK;EAC5B;EAEAmD,eAAeA,CAAA;IACb,IAAI,CAACnD,aAAa,GAAG,IAAI;EAC3B;EAEAgD,aAAaA,CAAA;IACX,MAAMI,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,IAAI,CAAClE,YAAY,GAAGiE,QAAQ,EAAE;MAChC,IAAI,CAACE,UAAU,EAAE;KAClB,MAAM;MACL;MACA,IAAI,IAAI,CAACpD,cAAc,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;QAC9C,IAAI,CAAC4C,eAAe,EAAE;OACvB,MAAM;QACL;QACA,IAAI,CAAC1D,YAAY,GAAG,CAAC;QACrB,IAAI,CAACoE,oBAAoB,EAAE;;;EAGjC;EAEA;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACnE,YAAY,EAAE;MACrB,IAAI,CAAC6D,cAAc,EAAE;MACrB,IAAI,CAAC/D,YAAY,GAAGsC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxC,YAAY,GAAG,CAAC,CAAC;MACtD,IAAI,CAACoE,oBAAoB,EAAE;MAC3B;MACA5C,UAAU,CAAC,MAAM,IAAI,CAACwC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEAG,UAAUA,CAAA;IACR,IAAI,IAAI,CAAChE,aAAa,EAAE;MACtB,IAAI,CAAC4D,cAAc,EAAE;MACrB,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAAClE,YAAY,GAAGsC,IAAI,CAACgC,GAAG,CAACL,QAAQ,EAAE,IAAI,CAACjE,YAAY,GAAG,CAAC,CAAC;MAC7D,IAAI,CAACoE,oBAAoB,EAAE;MAE3B;MACA,IAAI,IAAI,CAACpE,YAAY,IAAIiE,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAClD,cAAc,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;QACnF,IAAI,CAAC4C,eAAe,EAAE;;MAGxB;MACAlC,UAAU,CAAC,MAAM,IAAI,CAACwC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEAO,SAASA,CAACC,UAAkB;IAC1B,IAAI,CAACT,cAAc,EAAE;IACrB,IAAI,CAAC/D,YAAY,GAAGwE,UAAU;IAC9B,IAAI,CAACJ,oBAAoB,EAAE;IAC3B;IACA5C,UAAU,CAAC,MAAM,IAAI,CAACwC,eAAe,EAAE,EAAE,IAAI,CAAC;EAChD;EAEAI,oBAAoBA,CAAA;IAClB,IAAI,CAACnE,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACF,UAAU,GAAG,CAAC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACW,UAAU,GAAG,IAAI,CAACD,aAAa;IAC3E,IAAI,CAACgB,iBAAiB,EAAE;IAExB;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAACvB,eAAe,GAAG,KAAK;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAyB,iBAAiBA,CAAA;IACf,MAAMuC,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,CAAChE,YAAY,GAAG,IAAI,CAACF,YAAY,GAAG,CAAC;IACzC,IAAI,CAACG,aAAa,GAAG,IAAI,CAACH,YAAY,GAAGiE,QAAQ;EACnD;EAEAC,WAAWA,CAAA;IACT,MAAMzB,WAAW,GAAG,IAAI,CAAC5C,WAAW,CAAC6C,MAAM,GAAG,CAAC,CAAC,CAAC;IACjD,OAAOJ,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACM,IAAI,CAACH,WAAW,GAAG,IAAI,CAAC/B,aAAa,CAAC,GAAG,CAAC,CAAC;EACrE;EAEA+D,QAAQA,CAAA;IACN;IACA,IAAI,CAAC/C,iBAAiB,EAAE;EAC1B;EAEA;EACAgD,YAAYA,CAACC,KAAiB;IAC5B,IAAI,CAACpE,WAAW,GAAGoE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC3C,IAAI,CAACrE,aAAa,GAAG,IAAI,CAACD,WAAW;IACrC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACsD,cAAc,EAAE;EACvB;EAEAe,WAAWA,CAACH,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAClE,UAAU,EAAE;IAEtB,IAAI,CAACD,aAAa,GAAGmE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC7C,MAAME,MAAM,GAAG,IAAI,CAACvE,aAAa,GAAG,IAAI,CAACD,WAAW;IAEpD;IACA,IAAI+B,IAAI,CAAC0C,GAAG,CAACD,MAAM,CAAC,GAAG,EAAE,EAAE;MACzBJ,KAAK,CAACM,cAAc,EAAE;;EAE1B;EAEAC,UAAUA,CAACP,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAClE,UAAU,EAAE;IAEtB,MAAMsE,MAAM,GAAG,IAAI,CAACvE,aAAa,GAAG,IAAI,CAACD,WAAW;IACpD,MAAM4E,SAAS,GAAG,EAAE,CAAC,CAAC;IAEtB,IAAI7C,IAAI,CAAC0C,GAAG,CAACD,MAAM,CAAC,GAAGI,SAAS,EAAE;MAChC,IAAIJ,MAAM,GAAG,CAAC,EAAE;QACd;QACA,IAAI,CAACV,SAAS,EAAE;OACjB,MAAM;QACL;QACA,IAAI,CAACF,UAAU,EAAE;;KAEpB,MAAM;MACL;MACA3C,UAAU,CAAC,MAAM,IAAI,CAACwC,eAAe,EAAE,EAAE,IAAI,CAAC;;IAGhD,IAAI,CAACvD,UAAU,GAAG,KAAK;EACzB;EAEA;EACA2E,eAAeA,CAACC,GAAuB;IACrC,OAAO,IAAI,CAACzF,YAAY,CAACwF,eAAe,CAACC,GAAG,EAAE,MAAM,CAAC;EACvD;EAEAC,YAAYA,CAACX,KAAY;IACvB,IAAI,CAAC/E,YAAY,CAAC2F,gBAAgB,CAACZ,KAAK,EAAE,MAAM,CAAC;EACnD;EAIAa,eAAeA,CAACC,UAAsB;IACpC;IACA,IAAI,CAAC9F,MAAM,CAAC+F,QAAQ,CAAC,CAAC,UAAU,EAAED,UAAU,CAACpE,IAAI,CAACsE,GAAG,CAAC,EAAE;MACtDC,WAAW,EAAE;QACXC,OAAO,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC,CAACH,GAAG;QAClCI,MAAM,EAAE;;KAEX,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAClG,WAAW,EAAE;MACrB,IAAI,CAACH,MAAM,CAAC+F,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAEF,IAAI,CAACO,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,sBAAsB;IAC/CH,YAAY,CAACI,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2DxB;IAED;IACA,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC9CG,MAAM,CAACC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqLpB;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACjCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACCtE,MAAc,CAACgF,gBAAgB,GAAIC,KAAuB,IAAI;MAC7D,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACzB,IAAIA,KAAK,IAAIA,KAAK,CAACpE,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMqE,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;QACrBvD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuD,IAAI,CAACC,IAAI,EAAED,IAAI,CAACE,IAAI,CAAC;QAEnD;QACA,IAAI,CAACC,eAAe,CAACH,IAAI,CAAC;QAC1Bb,YAAY,CAACiB,MAAM,EAAE;;IAEzB,CAAC;EACH;EAEQD,eAAeA,CAACH,IAAU;IAChC,MAAMK,YAAY,GAAGjB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDgB,YAAY,CAACf,SAAS,GAAG,qBAAqB;IAE9C,MAAMgB,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACzC,MAAMS,OAAO,GAAGT,IAAI,CAACE,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC;IAE9CL,YAAY,CAACd,SAAS,GAAG;;;;;;;;;;YAUjBkB,OAAO,GACP,eAAeH,OAAO,oCAAoC,GAC1D,aAAaA,OAAO,wBACtB;;;;;;;;;;;;;;KAcL;IAED;IACA,MAAMK,aAAa,GAAGvB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACrDsB,aAAa,CAAClB,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8E3B;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACgB,aAAa,CAAC;IACxCvB,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACU,YAAY,CAAC;EACzC;EAUA;EACAO,YAAYA,CAAA;IACV,IAAI,CAAC5D,cAAc,EAAE;EACvB;EAEA6D,YAAYA,CAAA;IACV,IAAI,CAAC5D,eAAe,EAAE;EACxB;;;uBA9uBWzE,gBAAgB,EAAAsI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAhB/I,gBAAgB;MAAAgJ,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UClB7Bb,EAAA,CAAAe,SAAA,4BAA+C;;;qBDYnCtJ,YAAY;MAAAiH,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}