import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

// Import existing sidebar components
import { SuggestedProductsComponent } from '../suggested-products/suggested-products.component';
import { TrendingProductsComponent } from '../trending-products/trending-products.component';
import { TopInfluencersComponent } from '../top-influencers/top-influencers.component';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    CommonModule, 
    RouterModule, 
    SuggestedProductsComponent, 
    TrendingProductsComponent, 
    TopInfluencersComponent
  ],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  // Data passed from home component
  @Input() currentUser: any = null;
  @Input() featuredProducts: any[] = [];
  @Input() trendingProducts: any[] = [];
  @Input() newArrivals: any[] = [];
  @Input() summerCollection: any[] = [];
  @Input() categories: any[] = [];

  constructor(private router: Router) {}

  ngOnInit() {
    console.log('✅ Sidebar component initialized with data:', {
      featuredProducts: this.featuredProducts.length,
      trendingProducts: this.trendingProducts.length,
      newArrivals: this.newArrivals.length,
      summerCollection: this.summerCollection.length,
      categories: this.categories.length
    });
  }

  // Navigation methods
  onProductClick(product: any) {
    console.log('🛍️ Navigate to product:', product.name);
    this.router.navigate(['/product', product._id || product.id]);
  }

  onCategoryClick(category: any) {
    console.log('📂 Navigate to category:', category.name);
    this.router.navigate(['/shop'], {
      queryParams: { category: category.slug || category.name.toLowerCase() }
    });
  }

  onUserClick(user: any) {
    console.log('👤 Navigate to user profile:', user.username);
    this.router.navigate(['/profile', user.username]);
  }

  followUser(user: any) {
    console.log('➕ Follow user:', user.username);
    // TODO: Implement follow functionality
  }

  viewAllCategories() {
    this.router.navigate(['/shop']);
  }

  viewSummerCollection() {
    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });
  }

  viewFeaturedProducts() {
    this.router.navigate(['/shop'], { queryParams: { filter: 'featured' } });
  }

  viewNewArrivals() {
    this.router.navigate(['/shop'], { queryParams: { filter: 'new' } });
  }

  // Utility methods
  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  getDiscountPercentage(product: any): number {
    if (product.originalPrice && product.originalPrice > product.price) {
      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    }
    return product.discount || 0;
  }

  // Track by functions for performance
  trackByProductId(index: number, product: any): string {
    return product._id || product.id;
  }

  trackByCategoryId(index: number, category: any): string {
    return category._id || category.id;
  }

  // Get categories to display (real data or fallback)
  getDisplayCategories() {
    if (this.categories && this.categories.length > 0) {
      return this.categories.slice(0, 4);
    }

    // Fallback categories with images
    return [
      {
        _id: 'women',
        name: 'Women',
        slug: 'women',
        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'
      },
      {
        _id: 'men',
        name: 'Men',
        slug: 'men',
        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'
      },
      {
        _id: 'children',
        name: 'Kids',
        slug: 'children',
        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'
      },
      {
        _id: 'ethnic',
        name: 'Ethnic',
        slug: 'ethnic',
        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'
      }
    ];
  }
}
