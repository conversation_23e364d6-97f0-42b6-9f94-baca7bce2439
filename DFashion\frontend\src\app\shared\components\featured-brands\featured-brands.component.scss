.featured-brands-section {
  padding: 2rem 0;
  background: #fafafa;

  // Section Header
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .header-content {
      .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #262626;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        i {
          color: #ffd700;
          font-size: 1.8rem;
        }

        @media (max-width: 768px) {
          font-size: 1.5rem;
          justify-content: center;
        }
      }

      .section-subtitle {
        color: #8e8e8e;
        font-size: 1rem;
        margin: 0;
      }
    }

    .view-all-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 25px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      i {
        transition: transform 0.3s ease;
      }

      &:hover i {
        transform: translateX(3px);
      }
    }
  }

  // Loading State
  .loading-container {
    padding: 0 1rem;

    .loading-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;

      .brand-card-skeleton {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

        .skeleton-logo {
          width: 80px;
          height: 80px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
          border-radius: 12px;
          margin-bottom: 1rem;
        }

        .skeleton-text {
          height: 16px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
          border-radius: 4px;
          margin-bottom: 0.5rem;

          &.short {
            width: 60%;
          }
        }
      }
    }
  }

  // Brands Grid
  .brands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 0 1rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }
  }

  // Brand Card
  .brand-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);

      .brand-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }

    &:focus {
      outline: 3px solid #667eea;
      outline-offset: 2px;
    }

    .brand-logo-container {
      position: relative;
      margin-bottom: 1rem;

      .brand-logo {
        width: 80px;
        height: 80px;
        object-fit: contain;
        border-radius: 12px;
        background: #f8f9fa;
        padding: 0.5rem;
        transition: transform 0.3s ease;
      }

      .verification-badge {
        position: absolute;
        top: -5px;
        right: 50px;
        background: #4caf50;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
      }

      .trending-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: linear-gradient(135deg, #ff6b6b, #ff8e53);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);

        i {
          animation: pulse 2s infinite;
        }
      }
    }

    .brand-info {
      .brand-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #262626;
        margin: 0 0 0.5rem 0;
      }

      .brand-description {
        color: #8e8e8e;
        font-size: 0.9rem;
        margin: 0 0 1rem 0;
        line-height: 1.4;
      }

      .brand-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-bottom: 1rem;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.8rem;
          color: #666;

          i {
            color: #667eea;
            width: 12px;
          }
        }
      }

      .brand-categories {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .category-tag {
          background: #f0f2ff;
          color: #667eea;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: #667eea;
            color: white;
          }
        }

        .more-categories {
          color: #8e8e8e;
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }

    .brand-actions {
      position: absolute;
      bottom: 1rem;
      left: 1rem;
      right: 1rem;
      display: flex;
      gap: 0.5rem;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;

      .action-btn {
        flex: 1;
        padding: 0.75rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-weight: 600;

        &.primary {
          background: #667eea;
          color: white;

          &:hover {
            background: #5a6fd8;
          }
        }

        &.secondary {
          background: #f8f9fa;
          color: #666;
          flex: none;
          width: 40px;

          &:hover {
            background: #e9ecef;
          }

          .fa-heart.favorited {
            color: #e91e63;
          }
        }
      }
    }
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #8e8e8e;

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #ddd;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      color: #666;
    }

    p {
      margin: 0 0 1.5rem 0;
    }

    .retry-btn {
      background: #667eea;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0 auto;
    }
  }

  // Brand Modal
  .brand-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;

    .modal-content {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      max-width: 500px;
      width: 100%;
      position: relative;
      max-height: 90vh;
      overflow-y: auto;

      .close-btn {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        color: #666;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #f0f0f0;
        }
      }

      .modal-header {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;

        .modal-logo {
          width: 80px;
          height: 80px;
          object-fit: contain;
          border-radius: 12px;
          background: #f8f9fa;
          padding: 0.5rem;
        }

        .modal-brand-info {
          flex: 1;

          h3 {
            margin: 0 0 0.5rem 0;
            color: #262626;
          }

          p {
            margin: 0 0 1rem 0;
            color: #8e8e8e;
          }

          .website-link {
            color: #667eea;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .modal-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;

        .stat-card {
          text-align: center;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;

          .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #262626;
          }

          .stat-label {
            font-size: 0.8rem;
            color: #8e8e8e;
            margin-top: 0.25rem;
          }
        }
      }

      .modal-actions {
        .btn {
          width: 100%;
          padding: 1rem;
          background: #667eea;
          color: white;
          border: none;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;

          &:hover {
            background: #5a6fd8;
          }
        }
      }
    }
  }
}

// Animations
@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .featured-brands-section {
    background: #121212;

    .section-title {
      color: #ffffff;
    }

    .brand-card {
      background: #1e1e1e;
      color: #ffffff;

      .brand-name {
        color: #ffffff;
      }

      .brand-logo {
        background: #2a2a2a;
      }

      .action-btn.secondary {
        background: #2a2a2a;
        color: #ffffff;

        &:hover {
          background: #333;
        }
      }
    }

    .modal-content {
      background: #1e1e1e;
      color: #ffffff;

      .modal-brand-info h3 {
        color: #ffffff;
      }

      .modal-logo {
        background: #2a2a2a;
      }

      .stat-card {
        background: #2a2a2a;

        .stat-value {
          color: #ffffff;
        }
      }
    }
  }
}
