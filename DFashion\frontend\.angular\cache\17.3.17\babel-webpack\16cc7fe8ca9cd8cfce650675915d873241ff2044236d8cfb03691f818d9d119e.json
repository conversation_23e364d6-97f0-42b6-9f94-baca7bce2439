{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, e as readTask, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { f as findClosestIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-f3946ac1.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport './helpers-be245865.js';\nimport './index-9b0d46f4.js';\nconst infiniteScrollCss = \"ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}\";\nconst IonInfiniteScrollStyle0 = infiniteScrollCss;\nconst InfiniteScroll = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInfinite = createEvent(this, \"ionInfinite\", 7);\n    this.thrPx = 0;\n    this.thrPc = 0;\n    /**\n     * didFire exists so that ionInfinite\n     * does not fire multiple times if\n     * users continue to scroll after\n     * scrolling into the infinite\n     * scroll threshold.\n     */\n    this.didFire = false;\n    this.isBusy = false;\n    this.onScroll = () => {\n      const scrollEl = this.scrollEl;\n      if (!scrollEl || !this.canStart()) {\n        return 1;\n      }\n      const infiniteHeight = this.el.offsetHeight;\n      if (infiniteHeight === 0) {\n        // if there is no height of this element then do nothing\n        return 2;\n      }\n      const scrollTop = scrollEl.scrollTop;\n      const scrollHeight = scrollEl.scrollHeight;\n      const height = scrollEl.offsetHeight;\n      const threshold = this.thrPc !== 0 ? height * this.thrPc : this.thrPx;\n      const distanceFromInfinite = this.position === 'bottom' ? scrollHeight - infiniteHeight - scrollTop - threshold - height : scrollTop - infiniteHeight - threshold;\n      if (distanceFromInfinite < 0) {\n        if (!this.didFire) {\n          this.isLoading = true;\n          this.didFire = true;\n          this.ionInfinite.emit();\n          return 3;\n        }\n      }\n      return 4;\n    };\n    this.isLoading = false;\n    this.threshold = '15%';\n    this.disabled = false;\n    this.position = 'bottom';\n  }\n  thresholdChanged() {\n    const val = this.threshold;\n    if (val.lastIndexOf('%') > -1) {\n      this.thrPx = 0;\n      this.thrPc = parseFloat(val) / 100;\n    } else {\n      this.thrPx = parseFloat(val);\n      this.thrPc = 0;\n    }\n  }\n  disabledChanged() {\n    const disabled = this.disabled;\n    if (disabled) {\n      this.isLoading = false;\n      this.isBusy = false;\n    }\n    this.enableScrollEvents(!disabled);\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const contentEl = findClosestIonContent(_this.el);\n      if (!contentEl) {\n        printIonContentErrorMsg(_this.el);\n        return;\n      }\n      _this.scrollEl = yield getScrollElement(contentEl);\n      _this.thresholdChanged();\n      _this.disabledChanged();\n      if (_this.position === 'top') {\n        writeTask(() => {\n          if (_this.scrollEl) {\n            _this.scrollEl.scrollTop = _this.scrollEl.scrollHeight - _this.scrollEl.clientHeight;\n          }\n        });\n      }\n    })();\n  }\n  disconnectedCallback() {\n    this.enableScrollEvents(false);\n    this.scrollEl = undefined;\n  }\n  /**\n   * Call `complete()` within the `ionInfinite` output event handler when\n   * your async operation has completed. For example, the `loading`\n   * state is while the app is performing an asynchronous operation,\n   * such as receiving more data from an AJAX request to add more items\n   * to a data list. Once the data has been received and UI updated, you\n   * then call this method to signify that the loading has completed.\n   * This method will change the infinite scroll's state from `loading`\n   * to `enabled`.\n   */\n  complete() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const scrollEl = _this2.scrollEl;\n      if (!_this2.isLoading || !scrollEl) {\n        return;\n      }\n      _this2.isLoading = false;\n      if (_this2.position === 'top') {\n        /**\n         * New content is being added at the top, but the scrollTop position stays the same,\n         * which causes a scroll jump visually. This algorithm makes sure to prevent this.\n         * (Frame 1)\n         *    - complete() is called, but the UI hasn't had time to update yet.\n         *    - Save the current content dimensions.\n         *    - Wait for the next frame using _dom.read, so the UI will be updated.\n         * (Frame 2)\n         *    - Read the new content dimensions.\n         *    - Calculate the height difference and the new scroll position.\n         *    - Delay the scroll position change until other possible dom reads are done using _dom.write to be performant.\n         * (Still frame 2, if I'm correct)\n         *    - Change the scroll position (= visually maintain the scroll position).\n         *    - Change the state to re-enable the InfiniteScroll.\n         *    - This should be after changing the scroll position, or it could\n         *    cause the InfiniteScroll to be triggered again immediately.\n         * (Frame 3)\n         *    Done.\n         */\n        _this2.isBusy = true;\n        // ******** DOM READ ****************\n        // Save the current content dimensions before the UI updates\n        const prev = scrollEl.scrollHeight - scrollEl.scrollTop;\n        // ******** DOM READ ****************\n        requestAnimationFrame(() => {\n          readTask(() => {\n            // UI has updated, save the new content dimensions\n            const scrollHeight = scrollEl.scrollHeight;\n            // New content was added on top, so the scroll position should be changed immediately to prevent it from jumping around\n            const newScrollTop = scrollHeight - prev;\n            // ******** DOM WRITE ****************\n            requestAnimationFrame(() => {\n              writeTask(() => {\n                scrollEl.scrollTop = newScrollTop;\n                _this2.isBusy = false;\n                _this2.didFire = false;\n              });\n            });\n          });\n        });\n      } else {\n        _this2.didFire = false;\n      }\n    })();\n  }\n  canStart() {\n    return !this.disabled && !this.isBusy && !!this.scrollEl && !this.isLoading;\n  }\n  enableScrollEvents(shouldListen) {\n    if (this.scrollEl) {\n      if (shouldListen) {\n        this.scrollEl.addEventListener('scroll', this.onScroll);\n      } else {\n        this.scrollEl.removeEventListener('scroll', this.onScroll);\n      }\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const disabled = this.disabled;\n    return h(Host, {\n      key: 'c2248d06232dd7771dd155693ec75f9258dc969e',\n      class: {\n        [mode]: true,\n        'infinite-scroll-loading': this.isLoading,\n        'infinite-scroll-enabled': !disabled\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"threshold\": [\"thresholdChanged\"],\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nInfiniteScroll.style = IonInfiniteScrollStyle0;\nconst infiniteScrollContentIosCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\nconst IonInfiniteScrollContentIosStyle0 = infiniteScrollContentIosCss;\nconst infiniteScrollContentMdCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\nconst IonInfiniteScrollContentMdStyle0 = infiniteScrollContentMdCss;\nconst InfiniteScrollContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.loadingSpinner = undefined;\n    this.loadingText = undefined;\n  }\n  componentDidLoad() {\n    if (this.loadingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.loadingSpinner = config.get('infiniteLoadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n    }\n  }\n  renderLoadingText() {\n    const {\n      customHTMLEnabled,\n      loadingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"infinite-loading-text\",\n        innerHTML: sanitizeDOMString(loadingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"infinite-loading-text\"\n    }, this.loadingText);\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '2f4afb07bcfe3e12528eb9cee8646a097e0b359f',\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`infinite-scroll-content-${mode}`]: true\n      }\n    }, h(\"div\", {\n      key: 'af038177bf10c88c8970682487a4328689aaa5f2',\n      class: \"infinite-loading\"\n    }, this.loadingSpinner && h(\"div\", {\n      key: '1da5d419bc6a978b6a509fdab47dae347fc8d221',\n      class: \"infinite-loading-spinner\"\n    }, h(\"ion-spinner\", {\n      key: '60cc5c64e0a317ac0005d5afe42c4bb8da58136f',\n      name: this.loadingSpinner\n    })), this.loadingText !== undefined && this.renderLoadingText()));\n  }\n};\nInfiniteScrollContent.style = {\n  ios: IonInfiniteScrollContentIosStyle0,\n  md: IonInfiniteScrollContentMdStyle0\n};\nexport { InfiniteScroll as ion_infinite_scroll, InfiniteScrollContent as ion_infinite_scroll_content };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}