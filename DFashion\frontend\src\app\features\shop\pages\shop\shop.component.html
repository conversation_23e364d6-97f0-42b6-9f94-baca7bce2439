<div class="shop-container">
  <!-- Global Error Display -->
  <app-error-display [showGlobalErrors]="true"></app-error-display>

  <!-- Loading State -->
  <app-loading-spinner
    *ngIf="isLoading"
    [showGlobalLoading]="true"
    message="Loading shop data..."
    webSpinnerType="pulse">
  </app-loading-spinner>

  <!-- Shop Content -->
  <div *ngIf="!isLoading" class="shop-content">
    <!-- Hero Section with Search -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">Discover Fashion That Defines You</h1>
        <p class="hero-subtitle">Explore thousands of products from top brands</p>

        <!-- Enhanced Search Bar -->
        <div class="search-container">
          <div class="search-bar">
            <div class="search-input-wrapper">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                [(ngModel)]="searchQuery"
                placeholder="Search products, brands, categories..."
                (keyup.enter)="search()"
                class="search-input">
              <button *ngIf="searchQuery" (click)="searchQuery = ''; search()" class="clear-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button (click)="search()" class="search-btn">
              <i class="fas fa-search"></i>
              <span>Search</span>
            </button>
          </div>

          <!-- Quick Filters -->
          <div class="quick-filters" *ngIf="!searchQuery">
            <button class="filter-chip" (click)="filterType = 'trending'; onFilterChange()">
              <i class="fas fa-fire"></i>
              Trending
            </button>
            <button class="filter-chip" (click)="filterType = 'new'; onFilterChange()">
              <i class="fas fa-sparkles"></i>
              New Arrivals
            </button>
            <button class="filter-chip" (click)="filterType = 'sale'; onFilterChange()">
              <i class="fas fa-tags"></i>
              On Sale
            </button>
          </div>
        </div>

        <!-- Shop Stats -->
        <div class="shop-stats" *ngIf="shopStats">
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(shopStats.totalProducts) }}+</span>
            <span class="stat-label">Products</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(shopStats.totalBrands) }}+</span>
            <span class="stat-label">Brands</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(shopStats.totalUsers) }}+</span>
            <span class="stat-label">Happy Customers</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Shop Sections (when not filtering) -->
    <div *ngIf="showMainSections" class="main-sections">
      <!-- Quick Links Section -->
      <app-quick-links
        [showHeader]="true"
        [maxLinks]="8"
        [showPopularCategories]="true"
        [showQuickActions]="true"
        [showFeaturedCollections]="true">
      </app-quick-links>

      <!-- Featured Brands Section -->
      <app-featured-brands
        [maxBrands]="6"
        [showHeader]="true">
      </app-featured-brands>

      <!-- Trending Now Section -->
      <app-trending-now
        [maxProducts]="12"
        [showHeader]="true"
        [showScrollButtons]="true">
      </app-trending-now>

      <!-- New Arrivals Section -->
      <app-new-arrivals
        [maxProducts]="8"
        [showHeader]="true"
        [showLoadMore]="true">
      </app-new-arrivals>
    </div>

    <!-- Filtered Results Section (when searching/filtering) -->
    <div *ngIf="!showMainSections" class="filtered-results">
      <div class="results-header">
        <h2 class="results-title">
          <span *ngIf="searchQuery">Search Results for "{{ searchQuery }}"</span>
          <span *ngIf="!searchQuery && selectedCategory">{{ selectedCategory | titlecase }} Products</span>
          <span *ngIf="!searchQuery && !selectedCategory && filterType">{{ filterType | titlecase }} Products</span>
        </h2>
        <div class="results-meta">
          <span class="results-count">{{ filteredProducts.length }} products found</span>
          <button class="clear-filters-btn" (click)="clearFilters()">
            <i class="fas fa-times"></i>
            Clear Filters
          </button>
        </div>
      </div>

      <!-- Filtered Products Grid -->
      <div *ngIf="filteredProducts.length > 0" class="products-grid">
        <div *ngFor="let product of filteredProducts" class="product-card" (click)="navigateToProduct(product._id)">
          <div class="product-image-container">
            <img [src]="getProductImage(product)" [alt]="product.name" class="product-image">
            <div *ngIf="getDiscountPercentage(product) > 0" class="discount-badge">
              {{ getDiscountPercentage(product) }}% OFF
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-brand">{{ product.brand }}</p>
            <div class="product-pricing">
              <span class="current-price">₹{{ product.price }}</span>
              <span *ngIf="product.originalPrice && product.originalPrice > product.price"
                    class="original-price">₹{{ product.originalPrice }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results State -->
      <app-no-data
        *ngIf="filteredProducts.length === 0 && !isLoading"
        title="No Products Found"
        message="Try adjusting your search or filters to find what you're looking for."
        iconClass="fas fa-search"
        containerClass="compact"
        [showActions]="true"
        primaryAction="Clear Filters"
        secondaryAction="Browse All Products"
        [suggestions]="['Men\'s Clothing', 'Women\'s Fashion', 'Footwear', 'Accessories']"
        suggestionsTitle="Popular Categories:">
      </app-no-data>
    </div>
  </div>
</div>
