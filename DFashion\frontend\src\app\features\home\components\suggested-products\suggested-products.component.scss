.suggested-products-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  margin: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    margin: 8px;
    padding: 16px;
    border-radius: 12px;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    margin: 0;

    i {
      color: #f6ad55;
      font-size: 20px;
    }

    @media (max-width: 768px) {
      font-size: 20px;
    }
  }

  .view-all-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    @media (max-width: 768px) {
      padding: 6px 12px;
      font-size: 14px;
    }
  }
}

/* Products Slider Wrapper */
.products-slider-wrapper {
  position: relative;
  width: 100%;
  padding: 0 35px; /* Space for navigation buttons */
  overflow: hidden; /* Prevent content overflow */

  @media (max-width: 768px) {
    padding: 0 16px;
  }

  @media (max-width: 480px) {
    padding: 0 12px;
  }
}

/* Swiper Carousel Styling for Products */
.products-swiper {
  width: 100%;
  position: relative;

  /* Override Swiper CSS Variables for Products */
  --swiper-navigation-size: 24px;
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 6px;
  --swiper-navigation-color: #667eea;
  --swiper-theme-color: #667eea;

  ::ng-deep {
    .swiper-wrapper {
      align-items: stretch; /* Make all slides same height */
    }

    .swiper-slide {
      display: flex;
      height: auto;
      width: 280px !important; /* Fixed width for consistent sizing */

      @media (max-width: 1200px) {
        width: 260px !important;
      }

      @media (max-width: 768px) {
        width: 220px !important;
      }

      @media (max-width: 480px) {
        width: 180px !important;
      }

      @media (max-width: 360px) {
        width: 160px !important;
      }
    }

    /* Custom Swiper Navigation Buttons for Products */
    .swiper-button-next,
    .swiper-button-prev {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)) !important;
      border: 1px solid rgba(102, 126, 234, 0.2) !important;
      border-radius: 50% !important;
      width: 24px !important;
      height: 24px !important;
      margin-top: calc(0px - (24px / 2)) !important;
      cursor: pointer !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      box-shadow:
        0 3px 10px rgba(102, 126, 234, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.08) !important;
      z-index: 12 !important;
      backdrop-filter: blur(6px) !important;

      /* Hide default SVG and use custom icons */
      svg {
        display: none !important;
      }

      &:after {
        content: '' !important;
        display: none !important;
      }

      /* Custom Icon using CSS */
      &:before {
        content: '' !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        background-size: 12px 12px !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8)) !important;
        transition: all 0.3s ease !important;
      }

      &:hover {
        background: linear-gradient(135deg, #ffffff, #f8fafc) !important;
        transform: scale(1.1) translateY(-1px) !important;
        box-shadow:
          0 6px 20px rgba(102, 126, 234, 0.25),
          0 3px 8px rgba(0, 0, 0, 0.12) !important;
        border-color: rgba(102, 126, 234, 0.4) !important;
      }

      &:active {
        transform: scale(1.05) translateY(0) !important;
        transition-duration: 0.1s !important;
      }

      &.swiper-button-disabled {
        opacity: 0.3 !important;
        cursor: not-allowed !important;
        pointer-events: none !important;
        transform: none !important;

        &:hover {
          transform: none !important;
          box-shadow: 0 3px 10px rgba(102, 126, 234, 0.15) !important;
        }
      }
    }

    .swiper-button-prev {
      left: 6px !important;

      &:before {
        /* Modern Left Arrow SVG */
        background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.5 3L5.5 7L8.5 11' stroke='%23667eea' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
      }

      &:hover:before {
        /* Darker purple on hover */
        background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.5 3L5.5 7L8.5 11' stroke='%23764ba2' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
      }
    }

    .swiper-button-next {
      right: 6px !important;

      &:before {
        /* Modern Right Arrow SVG */
        background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.5 3L8.5 7L5.5 11' stroke='%23667eea' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
      }

      &:hover:before {
        /* Darker purple on hover */
        background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.5 3L8.5 7L5.5 11' stroke='%23764ba2' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
      }
    }

    @media (max-width: 768px) {
      .swiper-button-next,
      .swiper-button-prev {
        display: none !important;
      }
    }
  }
}

.product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 100%; /* Full width of slider item */
  height: 100%; /* Full height for consistent card sizes */
  display: flex;
  flex-direction: column;
  min-height: 400px; /* Ensure minimum height for content */

  @media (max-width: 768px) {
    min-height: 350px;
  }

  @media (max-width: 480px) {
    min-height: 320px;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

    .quick-actions {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    border-radius: 12px;

    &:hover {
      transform: translateY(-4px);
    }
  }
}

.product-image-container {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;

  .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover .product-image {
    transform: scale(1.05);
  }

  .discount-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(240, 147, 251, 0.4);
  }

  .wishlist-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: white;
      transform: scale(1.1);
      color: #f5576c;
    }

    i {
      font-size: 16px;
    }
  }

  .quick-actions {
    position: absolute;
    bottom: 12px;
    right: 12px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;

    .quick-action-btn {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: white;
        transform: scale(1.1);
      }

      i {
        font-size: 14px;
      }
    }

    @media (max-width: 768px) {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.product-info {
  padding: 16px;
  flex-grow: 1; /* Fill remaining space in card */
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.vendor-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.8;
  }

  .vendor-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
  }

  .vendor-name {
    font-size: 12px;
    font-weight: 500;
    color: #667eea;
  }

  .verified-icon {
    color: #667eea;
    font-size: 12px;
  }
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

.price-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  .current-price {
    font-size: 18px;
    font-weight: 700;
    color: #2d3748;
  }

  .original-price {
    font-size: 14px;
    color: #a0aec0;
    text-decoration: line-through;
  }

  @media (max-width: 768px) {
    .current-price {
      font-size: 16px;
    }

    .original-price {
      font-size: 12px;
    }
  }
}

.rating-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  .stars {
    display: flex;
    gap: 2px;

    i {
      font-size: 12px;
      color: #e2e8f0;

      &.filled {
        color: #f6ad55;
      }
    }
  }

  .rating-text {
    font-size: 12px;
    color: #718096;
  }
}

.analytics-container {
  display: flex;
  gap: 12px;

  .analytics-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #718096;

    i {
      font-size: 10px;
    }
  }

  @media (max-width: 768px) {
    gap: 8px;

    .analytics-item {
      font-size: 10px;
    }
  }
}

// Loading States
.loading-container {
  .loading-grid {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding: 0 40px;
    scroll-behavior: smooth;

    /* Hide scrollbar */
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }

    @media (max-width: 768px) {
      gap: 12px;
      padding: 0 20px;
    }

    @media (max-width: 480px) {
      padding: 0 16px;
    }
  }

  .product-skeleton {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    animation: pulse 1.5s ease-in-out infinite;
    min-width: 280px; /* Consistent width for skeleton cards */
    flex-shrink: 0;

    @media (max-width: 768px) {
      min-width: 200px;
    }

    @media (max-width: 480px) {
      min-width: 160px;
    }

    .skeleton-image {
      aspect-ratio: 1;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }

    .skeleton-content {
      padding: 16px;

      .skeleton-line {
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 6px;
        margin-bottom: 8px;

        &.short {
          width: 60%;
        }
      }
    }
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

// Load More Button Styling
.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;

  .load-more-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    i.fa-spinner {
      animation: spin 1s linear infinite;
    }

    @media (max-width: 768px) {
      padding: 10px 20px;
      font-size: 13px;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Error and Empty States
.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  .error-content,
  .empty-content {
    text-align: center;
    max-width: 400px;

    i {
      font-size: 48px;
      color: #a0aec0;
      margin-bottom: 16px;
    }

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }

    p {
      color: #718096;
      margin-bottom: 24px;
    }

    .retry-btn,
    .browse-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: none;
      border-radius: 20px;
      padding: 12px 24px;
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// Load More
.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;

  .load-more-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 12px 32px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    i {
      margin-right: 8px;
    }
  }
}
