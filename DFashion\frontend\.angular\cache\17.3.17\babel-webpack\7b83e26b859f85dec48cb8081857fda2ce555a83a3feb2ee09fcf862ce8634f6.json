{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, booleanAttribute, TemplateRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, inject, ElementRef, EventEmitter, ANIMATION_MODULE_TYPE, numberAttribute, Output, ContentChildren, forwardRef, QueryList, Attribute, NgModule } from '@angular/core';\nimport { MatRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule } from '@angular/material/core';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { Subject, fromEvent, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, CdkMonitorFocus } from '@angular/cdk/a11y';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { takeUntil, take, startWith, switchMap, skip, filter, distinctUntilChanged } from 'rxjs/operators';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { DOCUMENT } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst _c0 = [\"*\"];\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c1 = [\"tabListContainer\"];\nconst _c2 = [\"tabList\"];\nconst _c3 = [\"tabListInner\"];\nconst _c4 = [\"nextPaginator\"];\nconst _c5 = [\"previousPaginator\"];\nconst _c6 = a0 => ({\n  animationDuration: a0\n});\nconst _c7 = (a0, a1) => ({\n  value: a0,\n  params: a1\n});\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\nconst _c8 = [\"tabBodyWrapper\"];\nconst _c9 = [\"tabHeader\"];\nfunction MatTabGroup_For_3_Conditional_6_ng_template_0_Template(rf, ctx) {}\nfunction MatTabGroup_For_3_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_For_3_Conditional_6_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\nfunction MatTabGroup_For_3_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\nfunction MatTabGroup_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7, 2);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_For_3_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const tab_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const tabHeader_r7 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r5._handleClick(tab_r4, tabHeader_r7, i_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r2).$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._tabFocusChanged($event, i_r5));\n    });\n    i0.ɵɵelement(2, \"span\", 8)(3, \"div\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10)(5, \"span\", 11);\n    i0.ɵɵtemplate(6, MatTabGroup_For_3_Conditional_6_Template, 1, 1, null, 12)(7, MatTabGroup_For_3_Conditional_7_Template, 1, 1);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const i_r5 = ctx.$index;\n    const tabNode_r8 = i0.ɵɵreference(1);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r4.labelClass);\n    i0.ɵɵclassProp(\"mdc-tab--active\", ctx_r5.selectedIndex === i_r5);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabLabelId(i_r5))(\"disabled\", tab_r4.disabled)(\"fitInkBarToContent\", ctx_r5.fitInkBarToContent);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r5._getTabIndex(i_r5))(\"aria-posinset\", i_r5 + 1)(\"aria-setsize\", ctx_r5._tabs.length)(\"aria-controls\", ctx_r5._getTabContentId(i_r5))(\"aria-selected\", ctx_r5.selectedIndex === i_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matRippleTrigger\", tabNode_r8)(\"matRippleDisabled\", tab_r4.disabled || ctx_r5.disableRipple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(6, tab_r4.templateLabel ? 6 : 7);\n  }\n}\nfunction MatTabGroup_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatTabGroup_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 13);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._setTabBodyWrapperHeight($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const i_r11 = ctx.$index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r10.bodyClass);\n    i0.ɵɵclassProp(\"mat-mdc-tab-body-active\", ctx_r5.selectedIndex === i_r11);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabContentId(i_r11))(\"content\", tab_r10.content)(\"position\", tab_r10.position)(\"origin\", tab_r10.origin)(\"animationDuration\", ctx_r5.animationDuration)(\"preserveContent\", ctx_r5.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r5.contentTabIndex != null && ctx_r5.selectedIndex === i_r11 ? ctx_r5.contentTabIndex : null)(\"aria-labelledby\", ctx_r5._getTabLabelId(i_r11))(\"aria-hidden\", ctx_r5.selectedIndex !== i_r11);\n  }\n}\nconst _c10 = [\"mat-tab-nav-bar\", \"\"];\nconst _c11 = [\"mat-tab-link\", \"\"];\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n  constructor(/** Content for the tab. */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function MatTabContent_Factory(t) {\n      return new (t || MatTabContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabContent,\n      selectors: [[\"\", \"matTabContent\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabContent, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabContent]',\n      providers: [{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n  constructor(templateRef, viewContainerRef, _closestTab) {\n    super(templateRef, viewContainerRef);\n    this._closestTab = _closestTab;\n  }\n  static {\n    this.ɵfac = function MatTabLabel_Factory(t) {\n      return new (t || MatTabLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabLabel,\n      selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-label], [matTabLabel]',\n      providers: [{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_TAB]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  get templateLabel() {\n    return this._templateLabel;\n  }\n  set templateLabel(value) {\n    this._setTemplateLabelInput(value);\n  }\n  /** @docs-private */\n  get content() {\n    return this._contentPortal;\n  }\n  constructor(_viewContainerRef, _closestTabGroup) {\n    this._viewContainerRef = _viewContainerRef;\n    this._closestTabGroup = _closestTabGroup;\n    /** whether the tab is disabled. */\n    this.disabled = false;\n    /**\n     * Template provided in the tab content that will be used if present, used to enable lazy-loading\n     */\n    this._explicitContent = undefined;\n    /** Plain text label for the tab, used when there is no template label. */\n    this.textLabel = '';\n    /** Portal that will be the hosted content of the tab */\n    this._contentPortal = null;\n    /** Emits whenever the internal state of the tab changes. */\n    this._stateChanges = new Subject();\n    /**\n     * The relatively indexed position where 0 represents the center, negative is left, and positive\n     * represents the right.\n     */\n    this.position = null;\n    /**\n     * The initial relatively index origin of the tab if it was created and selected after there\n     * was already a selected tab. Provides context of what position the tab should originate from.\n     */\n    this.origin = null;\n    /**\n     * Whether the tab is currently active.\n     */\n    this.isActive = false;\n  }\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  ngOnInit() {\n    this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setTemplateLabelInput(value) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n  static {\n    this.ɵfac = function MatTab_Factory(t) {\n      return new (t || MatTab)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB_GROUP, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTab,\n      selectors: [[\"mat-tab\"]],\n      contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTabLabel, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatTabContent, 7, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n        }\n      },\n      viewQuery: function MatTab_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n        }\n      },\n      hostAttrs: [\"hidden\", \"\"],\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        textLabel: [i0.ɵɵInputFlags.None, \"label\", \"textLabel\"],\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        labelClass: \"labelClass\",\n        bodyClass: \"bodyClass\"\n      },\n      exportAs: [\"matTab\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatTab_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTab, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matTab',\n      providers: [{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }],\n      standalone: true,\n      host: {\n        // This element will be rendered on the server in order to support hydration.\n        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n        'hidden': ''\n      },\n      template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\"\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_TAB_GROUP]\n    }, {\n      type: Optional\n    }]\n  }], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    templateLabel: [{\n      type: ContentChild,\n      args: [MatTabLabel]\n    }],\n    _explicitContent: [{\n      type: ContentChild,\n      args: [MatTabContent, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _implicitContent: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    textLabel: [{\n      type: Input,\n      args: ['label']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    labelClass: [{\n      type: Input\n    }],\n    bodyClass: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n  constructor(_items) {\n    this._items = _items;\n  }\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n  }\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n    if (correspondingItem === currentItem) {\n      return;\n    }\n    currentItem?.deactivateInkBar();\n    if (correspondingItem) {\n      const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n      // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n      correspondingItem.activateInkBar(domRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\nclass InkBarItem {\n  constructor() {\n    this._elementRef = inject(ElementRef);\n    this._fitToContent = false;\n  }\n  /** Whether the ink bar should fit to the entire tab or just its content. */\n  get fitInkBarToContent() {\n    return this._fitToContent;\n  }\n  set fitInkBarToContent(newValue) {\n    if (this._fitToContent !== newValue) {\n      this._fitToContent = newValue;\n      if (this._inkBarElement) {\n        this._appendInkBarElement();\n      }\n    }\n  }\n  /** Aligns the ink bar to the current item. */\n  activateInkBar(previousIndicatorClientRect) {\n    const element = this._elementRef.nativeElement;\n    // Early exit if no indicator is present to handle cases where an indicator\n    // may be activated without a prior indicator state\n    if (!previousIndicatorClientRect || !element.getBoundingClientRect || !this._inkBarContentElement) {\n      element.classList.add(ACTIVE_CLASS);\n      return;\n    }\n    // This animation uses the FLIP approach. You can read more about it at the link below:\n    // https://aerotwist.com/blog/flip-your-animations/\n    // Calculate the dimensions based on the dimensions of the previous indicator\n    const currentClientRect = element.getBoundingClientRect();\n    const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n    const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n    element.classList.add(NO_TRANSITION_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n    // Force repaint before updating classes and transform to ensure the transform properly takes effect\n    element.getBoundingClientRect();\n    element.classList.remove(NO_TRANSITION_CLASS);\n    element.classList.add(ACTIVE_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', '');\n  }\n  /** Removes the ink bar from the current item. */\n  deactivateInkBar() {\n    this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n  }\n  /** Initializes the foundation. */\n  ngOnInit() {\n    this._createInkBarElement();\n  }\n  /** Destroys the foundation. */\n  ngOnDestroy() {\n    this._inkBarElement?.remove();\n    this._inkBarElement = this._inkBarContentElement = null;\n  }\n  /** Creates and appends the ink bar element. */\n  _createInkBarElement() {\n    const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n    const inkBarElement = this._inkBarElement = documentNode.createElement('span');\n    const inkBarContentElement = this._inkBarContentElement = documentNode.createElement('span');\n    inkBarElement.className = 'mdc-tab-indicator';\n    inkBarContentElement.className = 'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n    inkBarElement.appendChild(this._inkBarContentElement);\n    this._appendInkBarElement();\n  }\n  /**\n   * Appends the ink bar to the tab host element or content, depending on whether\n   * the ink bar should fit to content.\n   */\n  _appendInkBarElement() {\n    if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Ink bar element has not been created and cannot be appended');\n    }\n    const parentElement = this._fitToContent ? this._elementRef.nativeElement.querySelector('.mdc-tab__content') : this._elementRef.nativeElement;\n    if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Missing element to host the ink bar');\n    }\n    parentElement.appendChild(this._inkBarElement);\n  }\n  static {\n    this.ɵfac = function InkBarItem_Factory(t) {\n      return new (t || InkBarItem)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InkBarItem,\n      inputs: {\n        fitInkBarToContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute]\n      },\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InkBarItem, [{\n    type: Directive\n  }], null, {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n  return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n  constructor(elementRef) {\n    super();\n    this.elementRef = elementRef;\n    /** Whether the tab is disabled. */\n    this.disabled = false;\n  }\n  /** Sets focus on the wrapper element */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  getOffsetLeft() {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n  getOffsetWidth() {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n  static {\n    this.ɵfac = function MatTabLabelWrapper_Factory(t) {\n      return new (t || MatTabLabelWrapper)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabLabelWrapper,\n      selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n      hostVars: 3,\n      hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n          i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabelWrapper, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabLabelWrapper]',\n      host: {\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[attr.aria-disabled]': '!!disabled'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(v) {\n    const value = isNaN(v) ? 0 : v;\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._viewportRuler = _viewportRuler;\n    this._dir = _dir;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._animationMode = _animationMode;\n    /** The distance in pixels that the tab labels should be translated to the left. */\n    this._scrollDistance = 0;\n    /** Whether the header should scroll to the selected index after the view has been checked. */\n    this._selectedIndexChanged = false;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Whether the controls for pagination should be displayed */\n    this._showPaginationControls = false;\n    /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n    this._disableScrollAfter = true;\n    /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n    this._disableScrollBefore = true;\n    /** Stream that will stop the automated scrolling. */\n    this._stopScrolling = new Subject();\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    this.disablePagination = false;\n    this._selectedIndex = 0;\n    /** Event emitted when the option is selected. */\n    this.selectFocusedIndex = new EventEmitter();\n    /** Event emitted when a label is focused. */\n    this.indexFocused = new EventEmitter();\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    _ngZone.runOutsideAngular(() => {\n      fromEvent(_elementRef.nativeElement, 'mouseleave').pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._stopInterval();\n      });\n    });\n  }\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('before');\n    });\n    fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('after');\n    });\n  }\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : of('ltr');\n    const resize = this._viewportRuler.change(150);\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n    this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap()\n    // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n    .skipPredicate(() => false);\n    this._keyManager.updateActiveItem(this._selectedIndex);\n    // Defer the first call in order to allow for slower browsers to lay out the elements.\n    // This helps in cases where the user lands directly on a page with paginated tabs.\n    // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n    // can hold up tests that are in a background tab.\n    this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n    // On dir change or window resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      // We need to defer this to give the browser some time to recalculate\n      // the element dimensions. The call has to be wrapped in `NgZone.run`,\n      // because the viewport change handler runs outside of Angular.\n      this._ngZone.run(() => {\n        Promise.resolve().then(() => {\n          // Clamp the scroll distance, because it can change with the number of tabs.\n          this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n          realign();\n        });\n      });\n      this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n    });\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n  /** Sends any changes that could affect the layout of the items. */\n  _itemsResized() {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n    return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n      const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n      tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }))),\n    // Skip the first emit since the resize observer emits when an item\n    // is observed for new items when the tab is already inserted\n    skip(1),\n    // Skip emissions where all the elements are invisible since we don't want\n    // the header to try and re-render with invalid measurements. See #25574.\n    filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n  }\n  ngAfterContentChecked() {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        this._keyManager.onKeydown(event);\n    }\n  }\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n    this._keyManager.setActiveItem(value);\n  }\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index) {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n  /** The layout direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance() {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value) {\n    this._scrollTo(value);\n  }\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex) {\n    if (this.disablePagination) {\n      return;\n    }\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n    if (!selectedLabel) {\n      return;\n    }\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {\n      offsetLeft,\n      offsetWidth\n    } = selectedLabel.elementRef.nativeElement;\n    let labelBeforePos, labelAfterPos;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n    }\n  }\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n      if (isEnabled !== this._showPaginationControls) {\n        this._changeDetectorRef.markForCheck();\n      }\n      this._showPaginationControls = isEnabled;\n    }\n  }\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance() {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab() {\n    const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction, mouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n    // Avoid overlapping timers.\n    this._stopInterval();\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n    // Keep the timer going until something tells it to stop or the component is destroyed.\n    .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n      const {\n        maxScrollDistance,\n        distance\n      } = this._scrollHeader(direction);\n      // Stop the timer if we've reached the start or the end.\n      if (distance === 0 || distance >= maxScrollDistance) {\n        this._stopInterval();\n      }\n    });\n  }\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  _scrollTo(position) {\n    if (this.disablePagination) {\n      return {\n        maxScrollDistance: 0,\n        distance: 0\n      };\n    }\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n    return {\n      maxScrollDistance,\n      distance: this._scrollDistance\n    };\n  }\n  static {\n    this.ɵfac = function MatPaginatedTabHeader_Factory(t) {\n      return new (t || MatPaginatedTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatPaginatedTabHeader,\n      inputs: {\n        disablePagination: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disablePagination\", \"disablePagination\", booleanAttribute],\n        selectedIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectedIndex\", \"selectedIndex\", numberAttribute]\n      },\n      outputs: {\n        selectFocusedIndex: \"selectFocusedIndex\",\n        indexFocused: \"indexFocused\"\n      },\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatedTabHeader, [{\n    type: Directive\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i3.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    selectFocusedIndex: [{\n      type: Output\n    }],\n    indexFocused: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n  constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    /** Whether the ripple effect is disabled or not. */\n    this.disableRipple = false;\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n  _itemSelected(event) {\n    event.preventDefault();\n  }\n  static {\n    this.ɵfac = function MatTabHeader_Factory(t) {\n      return new (t || MatTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabHeader,\n      selectors: [[\"mat-tab-header\"]],\n      contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n        }\n      },\n      viewQuery: function MatTabHeader_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n          i0.ɵɵviewQuery(_c3, 7);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-tab-header\"],\n      hostVars: 4,\n      hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n        }\n      },\n      inputs: {\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 13,\n      vars: 10,\n      consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\", \"disabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-label-container\", 3, \"keydown\"], [\"role\", \"tablist\", 1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-labels\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\", \"disabled\"]],\n      template: function MatTabHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 5, 0);\n          i0.ɵɵlistener(\"click\", function MatTabHeader_Template_button_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n          })(\"mousedown\", function MatTabHeader_Template_button_mousedown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n          })(\"touchend\", function MatTabHeader_Template_button_touchend_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._stopInterval());\n          });\n          i0.ɵɵelement(2, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 7, 1);\n          i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleKeydown($event));\n          });\n          i0.ɵɵelementStart(5, \"div\", 8, 2);\n          i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onContentChanges());\n          });\n          i0.ɵɵelementStart(7, \"div\", 9, 3);\n          i0.ɵɵprojection(9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"button\", 10, 4);\n          i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_button_mousedown_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n          })(\"click\", function MatTabHeader_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n          })(\"touchend\", function MatTabHeader_Template_button_touchend_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._stopInterval());\n          });\n          i0.ɵɵelement(12, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n        }\n      },\n      dependencies: [MatRipple, CdkObserveContent],\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height);border-top-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\"\n      },\n      standalone: true,\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height);border-top-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i3.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    _items: [{\n      type: ContentChildren,\n      args: [MatTabLabelWrapper, {\n        descendants: false\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nconst matTabsAnimations = {\n  /** Animation translates a tab along the X axis. */\n  translateTab: trigger('translateTab', [\n  // Transitions to `none` instead of 0, because some browsers might blur the content.\n  state('center, void, left-origin-center, right-origin-center', style({\n    transform: 'none'\n  })),\n  // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  // in order to ensure that the element has a height before its state changes. This is\n  // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  // not have a static height and is not rendered. See related issue: #9465\n  state('left', style({\n    transform: 'translate3d(-100%, 0, 0)',\n    minHeight: '1px',\n    // Normally this is redundant since we detach the content from the DOM, but if the user\n    // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    visibility: 'hidden'\n  })), state('right', style({\n    transform: 'translate3d(100%, 0, 0)',\n    minHeight: '1px',\n    visibility: 'hidden'\n  })), transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')), transition('void => left-origin-center', [style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')]), transition('void => right-origin-center', [style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')])])\n};\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n  constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n    super(componentFactoryResolver, viewContainerRef, _document);\n    this._host = _host;\n    /** Subscription to events for when the tab body begins centering. */\n    this._centeringSub = Subscription.EMPTY;\n    /** Subscription to events for when the tab body finishes leaving from center position. */\n    this._leavingSub = Subscription.EMPTY;\n  }\n  /** Set initial visibility or set up subscription for changing visibility. */\n  ngOnInit() {\n    super.ngOnInit();\n    this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition(this._host._position))).subscribe(isCentering => {\n      if (isCentering && !this.hasAttached()) {\n        this.attach(this._host._content);\n      }\n    });\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n  /** Clean up centering subscription. */\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MatTabBodyPortal_Factory(t) {\n      return new (t || MatTabBodyPortal)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(forwardRef(() => MatTabBody)), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabBodyPortal,\n      selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBodyPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabBodyHost]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: MatTabBody,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => MatTabBody)]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  set position(position) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n  constructor(_elementRef, _dir, changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    /** Subscription to the directionality change observable. */\n    this._dirChangeSubscription = Subscription.EMPTY;\n    /** Emits when an animation on the tab is complete. */\n    this._translateTabComplete = new Subject();\n    /** Event emitted when the tab begins to animate towards the center as the active tab. */\n    this._onCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    this._beforeCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    this._afterLeavingCenter = new EventEmitter();\n    /** Event emitted when the tab completes its animation towards the center. */\n    this._onCentered = new EventEmitter(true);\n    // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n    // anyway to prevent the animations module from throwing an error if the body is used on its own.\n    /** Duration for the tab's animation. */\n    this.animationDuration = '500ms';\n    /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n    this.preserveContent = false;\n    if (_dir) {\n      this._dirChangeSubscription = _dir.change.subscribe(dir => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n    // Ensure that we get unique animation events, because the `.done` callback can get\n    // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n    this._translateTabComplete.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      // If the transition to the center is complete, emit an event.\n      if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n        this._onCentered.emit();\n      }\n      if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n        this._afterLeavingCenter.emit();\n      }\n    });\n  }\n  /**\n   * After initialized, check if the content is centered and has an origin. If so, set the\n   * special position states that transition the tab from the left or right before centering.\n   */\n  ngOnInit() {\n    if (this._position == 'center' && this.origin != null) {\n      this._position = this._computePositionFromOrigin(this.origin);\n    }\n  }\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n    this._translateTabComplete.complete();\n  }\n  _onTranslateTabStarted(event) {\n    const isCentering = this._isCenterPosition(event.toState);\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n  /** The text direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition(position) {\n    return position == 'center' || position == 'left-origin-center' || position == 'right-origin-center';\n  }\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  _computePositionAnimationState(dir = this._getLayoutDirection()) {\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n  }\n  /**\n   * Computes the position state based on the specified origin position. This is used if the\n   * tab is becoming visible immediately after creation.\n   */\n  _computePositionFromOrigin(origin) {\n    const dir = this._getLayoutDirection();\n    if (dir == 'ltr' && origin <= 0 || dir == 'rtl' && origin > 0) {\n      return 'left-origin-center';\n    }\n    return 'right-origin-center';\n  }\n  static {\n    this.ɵfac = function MatTabBody_Factory(t) {\n      return new (t || MatTabBody)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabBody,\n      selectors: [[\"mat-tab-body\"]],\n      viewQuery: function MatTabBody_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-tab-body\"],\n      inputs: {\n        _content: [i0.ɵɵInputFlags.None, \"content\", \"_content\"],\n        origin: \"origin\",\n        animationDuration: \"animationDuration\",\n        preserveContent: \"preserveContent\",\n        position: \"position\"\n      },\n      outputs: {\n        _onCentering: \"_onCentering\",\n        _beforeCentering: \"_beforeCentering\",\n        _afterLeavingCenter: \"_afterLeavingCenter\",\n        _onCentered: \"_onCentered\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 6,\n      consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-mdc-tab-body-content\"], [\"matTabBodyHost\", \"\"]],\n      template: function MatTabBody_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵlistener(\"@translateTab.start\", function MatTabBody_Template_div_animation_translateTab_start_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onTranslateTabStarted($event));\n          })(\"@translateTab.done\", function MatTabBody_Template_div_animation_translateTab_done_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._translateTabComplete.next($event));\n          });\n          i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"@translateTab\", i0.ɵɵpureFunction2(3, _c7, ctx._position, i0.ɵɵpureFunction1(1, _c6, ctx.animationDuration)));\n        }\n      },\n      dependencies: [MatTabBodyPortal, CdkScrollable],\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matTabsAnimations.translateTab]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBody, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-body',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      animations: [matTabsAnimations.translateTab],\n      host: {\n        'class': 'mat-mdc-tab-body'\n      },\n      standalone: true,\n      imports: [MatTabBodyPortal, CdkScrollable],\n      template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    _onCentering: [{\n      type: Output\n    }],\n    _beforeCentering: [{\n      type: Output\n    }],\n    _afterLeavingCenter: [{\n      type: Output\n    }],\n    _onCentered: [{\n      type: Output\n    }],\n    _portalHost: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet]\n    }],\n    _content: [{\n      type: Input,\n      args: ['content']\n    }],\n    origin: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n/** Boolean constant that determines whether the tab group supports the `backgroundColor` input */\nconst ENABLE_BACKGROUND_INPUT = true;\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    this._indexToSelect = isNaN(value) ? null : value;\n  }\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  get contentTabIndex() {\n    return this._contentTabIndex;\n  }\n  set contentTabIndex(value) {\n    this._contentTabIndex = isNaN(value) ? null : value;\n  }\n  /**\n   * Background color of the tab group.\n   * @deprecated The background color should be customized through Sass theming APIs.\n   * @breaking-change 20.0.0 Remove this input\n   */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    if (!ENABLE_BACKGROUND_INPUT) {\n      throw new Error(`mat-tab-group background color must be set through the Sass theming API`);\n    }\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  constructor(_elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** All of the tabs that belong to the group. */\n    this._tabs = new QueryList();\n    /** The tab index that should be selected after the content has been checked. */\n    this._indexToSelect = 0;\n    /** Index of the tab that was focused last. */\n    this._lastFocusedTabIndex = null;\n    /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n    this._tabBodyWrapperHeight = 0;\n    /** Subscription to tabs being added/removed. */\n    this._tabsSubscription = Subscription.EMPTY;\n    /** Subscription to changes in the tab labels. */\n    this._tabLabelSubscription = Subscription.EMPTY;\n    this._fitInkBarToContent = false;\n    /** Whether tabs should be stretched to fill the header. */\n    this.stretchTabs = true;\n    /** Whether the tab group should grow to the size of the active tab. */\n    this.dynamicHeight = false;\n    this._selectedIndex = null;\n    /** Position of the tab header. */\n    this.headerPosition = 'above';\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    this.disablePagination = false;\n    /** Whether ripples in the tab group are disabled. */\n    this.disableRipple = false;\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n    this.preserveContent = false;\n    /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n    this.selectedIndexChange = new EventEmitter();\n    /** Event emitted when focus has changed within a tab group. */\n    this.focusChange = new EventEmitter();\n    /** Event emitted when the body animation has completed */\n    this.animationDone = new EventEmitter();\n    /** Event emitted when the tab selection has changed. */\n    this.selectedTabChange = new EventEmitter(true);\n    /** Whether the tab group is rendered on the server. */\n    this._isServer = !inject(Platform).isBrowser;\n    this._groupId = nextId++;\n    this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    if (defaultConfig?.contentTabIndex != null) {\n      this.contentTabIndex = defaultConfig.contentTabIndex;\n    }\n    this.preserveContent = !!defaultConfig?.preserveContent;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect);\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab;\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Listens to changes in all of the tabs. */\n  _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n      this._tabs.reset(tabs.filter(tab => {\n        return tab._closestTabGroup === this || !tab._closestTabGroup;\n      }));\n      this._tabs.notifyOnChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n  }\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index) {\n    const header = this._tabHeader;\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n  _focusChanged(index) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n  _createChangeEvent(index) {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  _clampTabIndex(index) {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(i) {\n    return `mat-tab-label-${this._groupId}-${i}`;\n  }\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(i) {\n    return `mat-tab-content-${this._groupId}-${i}`;\n  }\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight) {\n    if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n      return;\n    }\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight() {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this.animationDone.emit();\n  }\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab, tabHeader, index) {\n    tabHeader.focusIndex = index;\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index) {\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin, index) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n  static {\n    this.ɵfac = function MatTabGroup_Factory(t) {\n      return new (t || MatTabGroup)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabGroup,\n      selectors: [[\"mat-tab-group\"]],\n      contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n        }\n      },\n      viewQuery: function MatTabGroup_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c8, 5);\n          i0.ɵɵviewQuery(_c9, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-tab-group\"],\n      hostVars: 10,\n      hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n          i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n          i0.ɵɵclassProp(\"mat-mdc-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-mdc-tab-group-inverted-header\", ctx.headerPosition === \"below\")(\"mat-mdc-tab-group-stretch-tabs\", ctx.stretchTabs);\n        }\n      },\n      inputs: {\n        color: \"color\",\n        fitInkBarToContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n        stretchTabs: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n        dynamicHeight: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"dynamicHeight\", \"dynamicHeight\", booleanAttribute],\n        selectedIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n        headerPosition: \"headerPosition\",\n        animationDuration: \"animationDuration\",\n        contentTabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"contentTabIndex\", \"contentTabIndex\", numberAttribute],\n        disablePagination: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disablePagination\", \"disablePagination\", booleanAttribute],\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        preserveContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"preserveContent\", \"preserveContent\", booleanAttribute],\n        backgroundColor: \"backgroundColor\"\n      },\n      outputs: {\n        selectedIndexChange: \"selectedIndexChange\",\n        focusChange: \"focusChange\",\n        animationDone: \"animationDone\",\n        selectedTabChange: \"selectedTabChange\"\n      },\n      exportAs: [\"matTabGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 9,\n      vars: 6,\n      consts: [[\"tabHeader\", \"\"], [\"tabBodyWrapper\", \"\"], [\"tabNode\", \"\"], [3, \"indexFocused\", \"selectFocusedIndex\", \"selectedIndex\", \"disableRipple\", \"disablePagination\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-mdc-focus-indicator\", 3, \"id\", \"mdc-tab--active\", \"class\", \"disabled\", \"fitInkBarToContent\"], [1, \"mat-mdc-tab-body-wrapper\"], [\"role\", \"tabpanel\", 3, \"id\", \"mat-mdc-tab-body-active\", \"class\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-mdc-focus-indicator\", 3, \"click\", \"cdkFocusChange\", \"id\", \"disabled\", \"fitInkBarToContent\"], [1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"_onCentered\", \"_onCentering\", \"id\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\"]],\n      template: function MatTabGroup_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"mat-tab-header\", 3, 0);\n          i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._focusChanged($event));\n          })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectedIndex = $event);\n          });\n          i0.ɵɵrepeaterCreate(2, MatTabGroup_For_3_Template, 8, 17, \"div\", 4, i0.ɵɵrepeaterTrackByIdentity);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, MatTabGroup_Conditional_4_Template, 1, 0);\n          i0.ɵɵelementStart(5, \"div\", 5, 1);\n          i0.ɵɵrepeaterCreate(7, MatTabGroup_For_8_Template, 1, 13, \"mat-tab-body\", 6, i0.ɵɵrepeaterTrackByIdentity);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination);\n          i0.ɵɵadvance(2);\n          i0.ɵɵrepeater(ctx._tabs);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(4, ctx._isServer ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵrepeater(ctx._tabs);\n        }\n      },\n      dependencies: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-group',\n      exportAs: 'matTabGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }],\n      host: {\n        'class': 'mat-mdc-tab-group',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      standalone: true,\n      imports: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n      template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(i)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n        [attr.aria-posinset]=\\\"i + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId(i)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n                 [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [origin]=\\\"tab.origin\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n    </mat-tab-body>\\n  }\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_TABS_CONFIG]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    _allTabs: [{\n      type: ContentChildren,\n      args: [MatTab, {\n        descendants: true\n      }]\n    }],\n    _tabBodyWrapper: [{\n      type: ViewChild,\n      args: ['tabBodyWrapper']\n    }],\n    _tabHeader: [{\n      type: ViewChild,\n      args: ['tabHeader']\n    }],\n    color: [{\n      type: Input\n    }],\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    dynamicHeight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    contentTabIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    preserveContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    focusChange: [{\n      type: Output\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    selectedTabChange: [{\n      type: Output\n    }]\n  });\n})();\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {}\n\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent.next(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  /** Background color of the tab nav. */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this._fitInkBarToContent = new BehaviorSubject(false);\n    /** Whether tabs should be stretched to fill the header. */\n    this.stretchTabs = true;\n    /** Whether the ripple effect is disabled or not. */\n    this.disableRipple = false;\n    /** Theme color of the nav bar. */\n    this.color = 'primary';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n  _itemSelected() {\n    // noop\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      this.updateActiveLink();\n    });\n    super.ngAfterContentInit();\n  }\n  ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n    const items = this._items.toArray();\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        this._changeDetectorRef.markForCheck();\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n        return;\n      }\n    }\n    // The ink bar should hide itself if no items are active.\n    this.selectedIndex = -1;\n    this._inkBar.hide();\n  }\n  _getRole() {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n  static {\n    this.ɵfac = function MatTabNav_Factory(t) {\n      return new (t || MatTabNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabNav,\n      selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n      contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n        }\n      },\n      viewQuery: function MatTabNav_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n          i0.ɵɵviewQuery(_c3, 7);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-tab-nav-bar\", \"mat-mdc-tab-header\"],\n      hostVars: 17,\n      hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx._getRole());\n          i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-mdc-tab-nav-bar-stretch-tabs\", ctx.stretchTabs)(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        fitInkBarToContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n        stretchTabs: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n        animationDuration: \"animationDuration\",\n        backgroundColor: \"backgroundColor\",\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        color: \"color\",\n        tabPanel: \"tabPanel\"\n      },\n      exportAs: [\"matTabNavBar\", \"matTabNav\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c10,\n      ngContentSelectors: _c0,\n      decls: 13,\n      vars: 8,\n      consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\", \"disabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-link-container\", 3, \"keydown\"], [1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-links\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\", \"disabled\"]],\n      template: function MatTabNav_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 5, 0);\n          i0.ɵɵlistener(\"click\", function MatTabNav_Template_button_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n          })(\"mousedown\", function MatTabNav_Template_button_mousedown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n          })(\"touchend\", function MatTabNav_Template_button_touchend_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._stopInterval());\n          });\n          i0.ɵɵelement(2, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 7, 1);\n          i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleKeydown($event));\n          });\n          i0.ɵɵelementStart(5, \"div\", 8, 2);\n          i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onContentChanges());\n          });\n          i0.ɵɵelementStart(7, \"div\", 9, 3);\n          i0.ɵɵprojection(9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"button\", 10, 4);\n          i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_button_mousedown_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n          })(\"click\", function MatTabNav_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n          })(\"touchend\", function MatTabNav_Template_button_touchend_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._stopInterval());\n          });\n          i0.ɵɵelement(12, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n        }\n      },\n      dependencies: [MatRipple, CdkObserveContent],\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNav, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-nav-bar]',\n      exportAs: 'matTabNavBar, matTabNav',\n      host: {\n        '[attr.role]': '_getRole()',\n        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i3.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_TABS_CONFIG]\n    }]\n  }], {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    _items: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatTabLink), {\n        descendants: true\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    tabPanel: [{\n      type: Input\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n  /** Whether the link is active. */\n  get active() {\n    return this._isActive;\n  }\n  set active(value) {\n    if (value !== this._isActive) {\n      this._isActive = value;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n  }\n  constructor(_tabNavBar, /** @docs-private */elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n    super();\n    this._tabNavBar = _tabNavBar;\n    this.elementRef = elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._destroyed = new Subject();\n    /** Whether the tab link is active or not. */\n    this._isActive = false;\n    /** Whether the tab link is disabled. */\n    this.disabled = false;\n    /** Whether ripples are disabled on the tab link. */\n    this.disableRipple = false;\n    this.tabIndex = 0;\n    /** Unique id for the tab. */\n    this.id = `mat-tab-link-${nextUniqueId++}`;\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = parseInt(tabIndex) || 0;\n    if (animationMode === 'NoopAnimations') {\n      this.rippleConfig.animation = {\n        enterDuration: 0,\n        exitDuration: 0\n      };\n    }\n    _tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n      this.fitInkBarToContent = fitInkBarToContent;\n    });\n  }\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this.disabled) {\n        event.preventDefault();\n      } else if (this._tabNavBar.tabPanel) {\n        // Only prevent the default action on space since it can scroll the page.\n        // Don't prevent enter since it can break link navigation.\n        if (event.keyCode === SPACE) {\n          event.preventDefault();\n        }\n        this.elementRef.nativeElement.click();\n      }\n    }\n  }\n  _getAriaControls() {\n    return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n  _getAriaSelected() {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n  _getAriaCurrent() {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n  _getRole() {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n  _getTabIndex() {\n    if (this._tabNavBar.tabPanel) {\n      return this._isActive && !this.disabled ? 0 : -1;\n    } else {\n      return this.disabled ? -1 : this.tabIndex;\n    }\n  }\n  static {\n    this.ɵfac = function MatTabLink_Factory(t) {\n      return new (t || MatTabLink)(i0.ɵɵdirectiveInject(MatTabNav), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabLink,\n      selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n      hostAttrs: [1, \"mdc-tab\", \"mat-mdc-tab-link\", \"mat-mdc-focus-indicator\"],\n      hostVars: 11,\n      hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n            return ctx._handleFocus();\n          })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._getTabIndex())(\"role\", ctx._getRole());\n          i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled)(\"mdc-tab--active\", ctx.active);\n        }\n      },\n      inputs: {\n        active: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"active\", \"active\", booleanAttribute],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        id: \"id\"\n      },\n      exportAs: [\"matTabLink\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c11,\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 2,\n      consts: [[1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"]],\n      template: function MatTabLink_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelement(0, \"span\", 0)(1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n          i0.ɵɵprojection(4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx.elementRef.nativeElement)(\"matRippleDisabled\", ctx.rippleDisabled);\n        }\n      },\n      dependencies: [MatRipple],\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLink, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-link], [matTabLink]',\n      exportAs: 'matTabLink',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n        '[attr.aria-controls]': '_getAriaControls()',\n        '[attr.aria-current]': '_getAriaCurrent()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.id]': 'id',\n        '[attr.tabIndex]': '_getTabIndex()',\n        '[attr.role]': '_getRole()',\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[class.mdc-tab--active]': 'active',\n        '(focus)': '_handleFocus()',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      standalone: true,\n      imports: [MatRipple],\n      template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\",\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"]\n    }]\n  }], () => [{\n    type: MatTabNav\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: i4.FocusMonitor\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    active: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n  constructor() {\n    /** Unique id for the tab panel. */\n    this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n  }\n  static {\n    this.ɵfac = function MatTabNavPanel_Factory(t) {\n      return new (t || MatTabNavPanel)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabNavPanel,\n      selectors: [[\"mat-tab-nav-panel\"]],\n      hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-mdc-tab-nav-panel\"],\n      hostVars: 2,\n      hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n        }\n      },\n      inputs: {\n        id: \"id\"\n      },\n      exportAs: [\"matTabNavPanel\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatTabNavPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNavPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-nav-panel',\n      exportAs: 'matTabNavPanel',\n      template: '<ng-content></ng-content>',\n      host: {\n        '[attr.aria-labelledby]': '_activeTabId',\n        '[attr.id]': 'id',\n        'class': 'mat-mdc-tab-nav-panel',\n        'role': 'tabpanel'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\nclass MatTabsModule {\n  static {\n    this.ɵfac = function MatTabsModule_Factory(t) {\n      return new (t || MatTabsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTabsModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n      exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "Inject", "Optional", "booleanAttribute", "TemplateRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChild", "ViewChild", "inject", "ElementRef", "EventEmitter", "ANIMATION_MODULE_TYPE", "numberAttribute", "Output", "ContentChildren", "forwardRef", "QueryList", "Attribute", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "CdkPortal", "TemplatePortal", "CdkPortalOutlet", "Subject", "fromEvent", "of", "merge", "EMPTY", "Observable", "timer", "Subscription", "BehaviorSubject", "i1", "CdkScrollable", "i3", "normalizePassiveListenerOptions", "Platform", "i2", "i4", "FocusKeyManager", "CdkMonitorFocus", "hasModifierKey", "SPACE", "ENTER", "takeUntil", "take", "startWith", "switchMap", "skip", "filter", "distinctUntilChanged", "CdkObserveContent", "DOCUMENT", "trigger", "state", "style", "transition", "animate", "_c0", "MatTab_ng_template_0_Template", "rf", "ctx", "ɵɵprojection", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "a0", "animationDuration", "_c7", "a1", "value", "params", "MatTabBody_ng_template_2_Template", "_c8", "_c9", "MatTabGroup_For_3_Conditional_6_ng_template_0_Template", "MatTabGroup_For_3_Conditional_6_Template", "ɵɵtemplate", "tab_r4", "ɵɵnextContext", "$implicit", "ɵɵproperty", "templateLabel", "MatTabGroup_For_3_Conditional_7_Template", "ɵɵtext", "ɵɵtextInterpolate", "textLabel", "MatTabGroup_For_3_Template", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatTabGroup_For_3_Template_div_click_0_listener", "ctx_r2", "ɵɵrestoreView", "i_r5", "$index", "ctx_r5", "tabHeader_r7", "ɵɵreference", "ɵɵresetView", "_handleClick", "MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener", "$event", "_tabFocusChanged", "ɵɵelement", "ɵɵelementEnd", "tabNode_r8", "ɵɵclassMap", "labelClass", "ɵɵclassProp", "selectedIndex", "_getTabLabelId", "disabled", "fitInkBarToContent", "ɵɵattribute", "_getTabIndex", "_tabs", "length", "_getTabContentId", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵadvance", "disable<PERSON><PERSON><PERSON>", "ɵɵconditional", "MatTabGroup_Conditional_4_Template", "MatTabGroup_For_8_Template", "_r9", "MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener", "_removeTabBodyWrapperHeight", "MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener", "_setTabBodyWrapperHeight", "tab_r10", "i_r11", "bodyClass", "content", "position", "origin", "preserve<PERSON><PERSON>nt", "contentTabIndex", "_c10", "_c11", "MAT_TAB_CONTENT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "template", "ɵfac", "MatTabContent_Factory", "t", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "MAT_TAB_LABEL", "MAT_TAB", "MatTab<PERSON><PERSON><PERSON>", "templateRef", "viewContainerRef", "_closestTab", "MatTabLabel_Factory", "ViewContainerRef", "ɵɵInheritDefinitionFeature", "undefined", "decorators", "MAT_TAB_GROUP", "Mat<PERSON><PERSON>", "_templateLabel", "_setTemplateLabelInput", "_contentPortal", "_viewContainerRef", "_closestTabGroup", "_explicitContent", "_stateChanges", "isActive", "ngOnChanges", "changes", "hasOwnProperty", "next", "ngOnDestroy", "complete", "ngOnInit", "_implicitContent", "MatTab_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatTab_Query", "ɵɵviewQuery", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "None", "exportAs", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "MatTab_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "host", "transform", "read", "static", "ACTIVE_CLASS", "NO_TRANSITION_CLASS", "MatInkBar", "_items", "hide", "for<PERSON>ach", "item", "deactivateInkBar", "alignToElement", "element", "correspondingItem", "find", "elementRef", "nativeElement", "currentItem", "_currentItem", "domRect", "getBoundingClientRect", "activateInkBar", "InkBarItem", "_elementRef", "_fitTo<PERSON>ontent", "newValue", "_inkBarElement", "_appendInkBarElement", "previousIndicatorClientRect", "_inkBarContentElement", "classList", "add", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "setProperty", "remove", "_createInkBarElement", "documentNode", "ownerDocument", "document", "inkBarElement", "createElement", "inkBarContentElement", "className", "append<PERSON><PERSON><PERSON>", "Error", "parentElement", "querySelector", "InkBarItem_Factory", "_MAT_INK_BAR_POSITIONER_FACTORY", "method", "offsetLeft", "offsetWidth", "_MAT_INK_BAR_POSITIONER", "providedIn", "factory", "MatTabLabelWrapper", "focus", "getOffsetLeft", "getOffsetWidth", "MatTabLabelWrapper_Factory", "hostVars", "hostBindings", "MatTabLabelWrapper_HostBindings", "passiveEventListenerOptions", "passive", "HEADER_SCROLL_DELAY", "HEADER_SCROLL_INTERVAL", "MatPaginatedTabHeader", "_selectedIndex", "v", "isNaN", "_selectedIndexChanged", "_keyManager", "updateActiveItem", "_changeDetectorRef", "_viewportRuler", "_dir", "_ngZone", "_platform", "_animationMode", "_scrollDistance", "_destroyed", "_showPaginationControls", "_disableScrollAfter", "_disableScrollBefore", "_stopScrolling", "disablePagination", "selectFocusedIndex", "indexFocused", "runOutsideAngular", "pipe", "subscribe", "_stopInterval", "ngAfterViewInit", "_previousPaginator", "_handlePaginatorPress", "_nextPaginator", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "change", "resize", "realign", "updatePagination", "_alignInkBarToSelectedTab", "withHorizontalOrientation", "_getLayoutDirection", "withHomeAndEnd", "withWrap", "skipPredicate", "onStable", "_itemsResized", "run", "Promise", "resolve", "then", "Math", "max", "min", "_getMaxScrollDistance", "newFocusIndex", "emit", "_setTabFocus", "ResizeObserver", "tabItems", "observer", "resizeObserver", "entries", "observe", "disconnect", "some", "e", "contentRect", "height", "ngAfterContentChecked", "_tabLabelCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_scrollToLabel", "_checkScrollingControls", "_scrollDistanceChanged", "_updateTabScrollPosition", "destroy", "_handleKeydown", "event", "keyCode", "focusIndex", "get", "_itemSelected", "onKeydown", "_onContentChanges", "textContent", "_currentTextContent", "_checkPaginationEnabled", "activeItemIndex", "_isValidIndex", "setActiveItem", "index", "toArray", "tabIndex", "containerEl", "_tabListContainer", "dir", "scrollLeft", "scrollWidth", "scrollDistance", "translateX", "_tabList", "round", "TRIDENT", "EDGE", "_scrollTo", "_scrollHeader", "direction", "viewLength", "scrollAmount", "_handlePaginatorClick", "labelIndex", "<PERSON><PERSON><PERSON><PERSON>", "labelBeforePos", "labelAfterPos", "_tabListInner", "beforeVisiblePos", "afterVisiblePos", "isEnabled", "lengthOfTabList", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>W<PERSON><PERSON>", "_inkBar", "mouseEvent", "button", "maxScrollDistance", "distance", "MatPaginatedTabHeader_Factory", "ChangeDetectorRef", "ViewportRuler", "Directionality", "NgZone", "outputs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeDetectorRef", "viewportRuler", "ngZone", "platform", "animationMode", "preventDefault", "MatTabHeader_Factory", "MatTabHeader_ContentQueries", "MatTabHeader_Query", "MatTabHeader_HostBindings", "consts", "MatTabHeader_Template", "_r1", "MatTabHeader_Template_button_click_0_listener", "MatTabHeader_Template_button_mousedown_0_listener", "MatTab<PERSON><PERSON>er_Template_button_touchend_0_listener", "MatTab<PERSON><PERSON><PERSON>_Template_div_keydown_3_listener", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON>_Template_div_cdkObserveContent_5_listener", "MatTab<PERSON>eader_Template_button_mousedown_10_listener", "MatTab<PERSON><PERSON><PERSON>_Template_button_click_10_listener", "Mat<PERSON>ab<PERSON><PERSON><PERSON>_Template_button_touchend_10_listener", "dependencies", "styles", "imports", "descendants", "MAT_TABS_CONFIG", "matTabsAnimations", "translateTab", "minHeight", "visibility", "MatTabBodyPortal", "componentFactoryResolver", "_host", "_document", "_centeringSub", "_leavingSub", "_beforeCentering", "_isCenterPosition", "_position", "isCentering", "has<PERSON>tta<PERSON>", "attach", "_content", "_afterLeavingCenter", "detach", "unsubscribe", "MatTabBodyPortal_Factory", "ComponentFactoryResolver", "MatTabBody", "_positionIndex", "_computePositionAnimationState", "_dirChangeSubscription", "_translateTabComplete", "_onCentering", "_onCentered", "x", "y", "fromState", "toState", "_computePositionFromO<PERSON>in", "_onTranslateTabStarted", "clientHeight", "MatTabBody_Factory", "MatTabBody_Query", "_portalHost", "MatTabBody_Template", "MatTabBody_Template_div_animation_translateTab_start_0_listener", "MatTabBody_Template_div_animation_translateTab_done_0_listener", "ɵɵpureFunction2", "ɵɵpureFunction1", "data", "animation", "animations", "nextId", "ENABLE_BACKGROUND_INPUT", "MatTabGroup", "_fitInkBarToContent", "_indexToSelect", "_animationDuration", "stringValue", "test", "_contentTabIndex", "backgroundColor", "_backgroundColor", "defaultConfig", "_lastFocusedTabIndex", "_tabBodyWrapperHeight", "_tabsSubscription", "_tabLabelSubscription", "stretchTabs", "dynamicHeight", "headerPosition", "selectedIndexChange", "focusChange", "animationDone", "selectedTabChange", "_isServer", "<PERSON><PERSON><PERSON><PERSON>", "_groupId", "indexToSelect", "_clampTabIndex", "isFirstRun", "_createChangeEvent", "wrapper", "_tabBodyWrapper", "tab", "_subscribeToAllTabChanges", "_subscribeToTabLabels", "tabs", "selectedTab", "i", "_allTabs", "reset", "notifyOn<PERSON><PERSON>es", "realignInkBar", "_tabHeader", "focusTab", "header", "_focusChanged", "MatTabChangeEvent", "map", "tabHeight", "offsetHeight", "tabHeader", "targetIndex", "<PERSON><PERSON><PERSON><PERSON>", "MatTabGroup_Factory", "MatTabGroup_ContentQueries", "MatTabGroup_Query", "MatTabGroup_HostBindings", "color", "ɵɵstyleProp", "MatTabGroup_Template", "MatTabGroup_Template_mat_tab_header_indexFocused_0_listener", "MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "alias", "nextUniqueId", "MatTabNav", "updateActiveLink", "tabPanel", "items", "active", "_activeTabId", "id", "_getRole", "getAttribute", "MatTabNav_Factory", "MatTabNav_ContentQueries", "MatTabLink", "MatTabNav_Query", "MatTabNav_HostBindings", "attrs", "MatTabNav_Template", "MatTabNav_Template_button_click_0_listener", "MatTabNav_Template_button_mousedown_0_listener", "MatTabNav_Template_button_touchend_0_listener", "MatTabNav_Template_div_keydown_3_listener", "MatTabNav_Template_div_cdkObserveContent_5_listener", "MatTabNav_Template_button_mousedown_10_listener", "MatTabNav_Template_button_click_10_listener", "MatTabNav_Template_button_touchend_10_listener", "_isActive", "_tabNavBar", "rippleDisabled", "rippleConfig", "globalRippleOptions", "_focusMonitor", "parseInt", "enterDuration", "exitDuration", "monitor", "stopMonitoring", "_handleFocus", "indexOf", "click", "_getAriaControls", "_getAriaSelected", "_getAriaCurrent", "MatTabLink_Factory", "ɵɵinjectAttribute", "FocusMonitor", "MatTabLink_HostBindings", "MatTabLink_focus_HostBindingHandler", "MatTabLink_keydown_HostBindingHandler", "MatTabLink_Template", "OnPush", "MatTabNavPanel", "MatTabNavPanel_Factory", "MatTabNavPanel_HostBindings", "MatTabNavPanel_Template", "MatTabsModule", "MatTabsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@angular/material/fesm2022/tabs.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, booleanAttribute, TemplateRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, inject, ElementRef, EventEmitter, ANIMATION_MODULE_TYPE, numberAttribute, Output, ContentChildren, forwardRef, QueryList, Attribute, NgModule } from '@angular/core';\nimport { MatRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule } from '@angular/material/core';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { Subject, fromEvent, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, CdkMonitorFocus } from '@angular/cdk/a11y';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { takeUntil, take, startWith, switchMap, skip, filter, distinctUntilChanged } from 'rxjs/operators';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { DOCUMENT } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n    constructor(/** Content for the tab. */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabContent, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabContent, isStandalone: true, selector: \"[matTabContent]\", providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabContent]',\n                    providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n    constructor(templateRef, viewContainerRef, _closestTab) {\n        super(templateRef, viewContainerRef);\n        this._closestTab = _closestTab;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabel, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: MAT_TAB, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabLabel, isStandalone: true, selector: \"[mat-tab-label], [matTabLabel]\", providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-label], [matTabLabel]',\n                    providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB]\n                }, {\n                    type: Optional\n                }] }] });\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n        return this._templateLabel;\n    }\n    set templateLabel(value) {\n        this._setTemplateLabelInput(value);\n    }\n    /** @docs-private */\n    get content() {\n        return this._contentPortal;\n    }\n    constructor(_viewContainerRef, _closestTabGroup) {\n        this._viewContainerRef = _viewContainerRef;\n        this._closestTabGroup = _closestTabGroup;\n        /** whether the tab is disabled. */\n        this.disabled = false;\n        /**\n         * Template provided in the tab content that will be used if present, used to enable lazy-loading\n         */\n        this._explicitContent = undefined;\n        /** Plain text label for the tab, used when there is no template label. */\n        this.textLabel = '';\n        /** Portal that will be the hosted content of the tab */\n        this._contentPortal = null;\n        /** Emits whenever the internal state of the tab changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The relatively indexed position where 0 represents the center, negative is left, and positive\n         * represents the right.\n         */\n        this.position = null;\n        /**\n         * The initial relatively index origin of the tab if it was created and selected after there\n         * was already a selected tab. Provides context of what position the tab should originate from.\n         */\n        this.origin = null;\n        /**\n         * Whether the tab is currently active.\n         */\n        this.isActive = false;\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n            this._stateChanges.next();\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    ngOnInit() {\n        this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n        // Only update the label if the query managed to find one. This works around an issue where a\n        // user may have manually set `templateLabel` during creation mode, which would then get\n        // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n        // tab matches the current one so that we don't pick up labels from nested tabs.\n        if (value && value._closestTab === this) {\n            this._templateLabel = value;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTab, deps: [{ token: i0.ViewContainerRef }, { token: MAT_TAB_GROUP, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTab, isStandalone: true, selector: \"mat-tab\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], textLabel: [\"label\", \"textLabel\"], ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], labelClass: \"labelClass\", bodyClass: \"bodyClass\" }, host: { attributes: { \"hidden\": \"\" } }, providers: [{ provide: MAT_TAB, useExisting: MatTab }], queries: [{ propertyName: \"templateLabel\", first: true, predicate: MatTabLabel, descendants: true }, { propertyName: \"_explicitContent\", first: true, predicate: MatTabContent, descendants: true, read: TemplateRef, static: true }], viewQueries: [{ propertyName: \"_implicitContent\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"matTab\"], usesOnChanges: true, ngImport: i0, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\", changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, exportAs: 'matTab', providers: [{ provide: MAT_TAB, useExisting: MatTab }], standalone: true, host: {\n                        // This element will be rendered on the server in order to support hydration.\n                        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n                        'hidden': '',\n                    }, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB_GROUP]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], templateLabel: [{\n                type: ContentChild,\n                args: [MatTabLabel]\n            }], _explicitContent: [{\n                type: ContentChild,\n                args: [MatTabContent, { read: TemplateRef, static: true }]\n            }], _implicitContent: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], textLabel: [{\n                type: Input,\n                args: ['label']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], labelClass: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }] } });\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n    constructor(_items) {\n        this._items = _items;\n    }\n    /** Hides the ink bar. */\n    hide() {\n        this._items.forEach(item => item.deactivateInkBar());\n    }\n    /** Aligns the ink bar to a DOM node. */\n    alignToElement(element) {\n        const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n        const currentItem = this._currentItem;\n        if (correspondingItem === currentItem) {\n            return;\n        }\n        currentItem?.deactivateInkBar();\n        if (correspondingItem) {\n            const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n            // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n            correspondingItem.activateInkBar(domRect);\n            this._currentItem = correspondingItem;\n        }\n    }\n}\nclass InkBarItem {\n    constructor() {\n        this._elementRef = inject(ElementRef);\n        this._fitToContent = false;\n    }\n    /** Whether the ink bar should fit to the entire tab or just its content. */\n    get fitInkBarToContent() {\n        return this._fitToContent;\n    }\n    set fitInkBarToContent(newValue) {\n        if (this._fitToContent !== newValue) {\n            this._fitToContent = newValue;\n            if (this._inkBarElement) {\n                this._appendInkBarElement();\n            }\n        }\n    }\n    /** Aligns the ink bar to the current item. */\n    activateInkBar(previousIndicatorClientRect) {\n        const element = this._elementRef.nativeElement;\n        // Early exit if no indicator is present to handle cases where an indicator\n        // may be activated without a prior indicator state\n        if (!previousIndicatorClientRect ||\n            !element.getBoundingClientRect ||\n            !this._inkBarContentElement) {\n            element.classList.add(ACTIVE_CLASS);\n            return;\n        }\n        // This animation uses the FLIP approach. You can read more about it at the link below:\n        // https://aerotwist.com/blog/flip-your-animations/\n        // Calculate the dimensions based on the dimensions of the previous indicator\n        const currentClientRect = element.getBoundingClientRect();\n        const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n        const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n        element.classList.add(NO_TRANSITION_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n        // Force repaint before updating classes and transform to ensure the transform properly takes effect\n        element.getBoundingClientRect();\n        element.classList.remove(NO_TRANSITION_CLASS);\n        element.classList.add(ACTIVE_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', '');\n    }\n    /** Removes the ink bar from the current item. */\n    deactivateInkBar() {\n        this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n    }\n    /** Initializes the foundation. */\n    ngOnInit() {\n        this._createInkBarElement();\n    }\n    /** Destroys the foundation. */\n    ngOnDestroy() {\n        this._inkBarElement?.remove();\n        this._inkBarElement = this._inkBarContentElement = null;\n    }\n    /** Creates and appends the ink bar element. */\n    _createInkBarElement() {\n        const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n        const inkBarElement = (this._inkBarElement = documentNode.createElement('span'));\n        const inkBarContentElement = (this._inkBarContentElement = documentNode.createElement('span'));\n        inkBarElement.className = 'mdc-tab-indicator';\n        inkBarContentElement.className =\n            'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n        inkBarElement.appendChild(this._inkBarContentElement);\n        this._appendInkBarElement();\n    }\n    /**\n     * Appends the ink bar to the tab host element or content, depending on whether\n     * the ink bar should fit to content.\n     */\n    _appendInkBarElement() {\n        if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Ink bar element has not been created and cannot be appended');\n        }\n        const parentElement = this._fitToContent\n            ? this._elementRef.nativeElement.querySelector('.mdc-tab__content')\n            : this._elementRef.nativeElement;\n        if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Missing element to host the ink bar');\n        }\n        parentElement.appendChild(this._inkBarElement);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InkBarItem, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: InkBarItem, inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InkBarItem, decorators: [{\n            type: Directive\n        }], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n    const method = (element) => ({\n        left: element ? (element.offsetLeft || 0) + 'px' : '0',\n        width: element ? (element.offsetWidth || 0) + 'px' : '0',\n    });\n    return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n    constructor(elementRef) {\n        super();\n        this.elementRef = elementRef;\n        /** Whether the tab is disabled. */\n        this.disabled = false;\n    }\n    /** Sets focus on the wrapper element */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n        return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n        return this.elementRef.nativeElement.offsetWidth;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabelWrapper, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabLabelWrapper, isStandalone: true, selector: \"[matTabLabelWrapper]\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-disabled\": \"disabled\", \"attr.aria-disabled\": \"!!disabled\" } }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabelWrapper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabLabelWrapper]',\n                    host: {\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[attr.aria-disabled]': '!!disabled',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(v) {\n        const value = isNaN(v) ? 0 : v;\n        if (this._selectedIndex != value) {\n            this._selectedIndexChanged = true;\n            this._selectedIndex = value;\n            if (this._keyManager) {\n                this._keyManager.updateActiveItem(value);\n            }\n        }\n    }\n    constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._viewportRuler = _viewportRuler;\n        this._dir = _dir;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._animationMode = _animationMode;\n        /** The distance in pixels that the tab labels should be translated to the left. */\n        this._scrollDistance = 0;\n        /** Whether the header should scroll to the selected index after the view has been checked. */\n        this._selectedIndexChanged = false;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Whether the controls for pagination should be displayed */\n        this._showPaginationControls = false;\n        /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n        this._disableScrollAfter = true;\n        /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n        this._disableScrollBefore = true;\n        /** Stream that will stop the automated scrolling. */\n        this._stopScrolling = new Subject();\n        /**\n         * Whether pagination should be disabled. This can be used to avoid unnecessary\n         * layout recalculations if it's known that pagination won't be required.\n         */\n        this.disablePagination = false;\n        this._selectedIndex = 0;\n        /** Event emitted when the option is selected. */\n        this.selectFocusedIndex = new EventEmitter();\n        /** Event emitted when a label is focused. */\n        this.indexFocused = new EventEmitter();\n        // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n        _ngZone.runOutsideAngular(() => {\n            fromEvent(_elementRef.nativeElement, 'mouseleave')\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => {\n                this._stopInterval();\n            });\n        });\n    }\n    ngAfterViewInit() {\n        // We need to handle these events manually, because we want to bind passive event listeners.\n        fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('before');\n        });\n        fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('after');\n        });\n    }\n    ngAfterContentInit() {\n        const dirChange = this._dir ? this._dir.change : of('ltr');\n        const resize = this._viewportRuler.change(150);\n        const realign = () => {\n            this.updatePagination();\n            this._alignInkBarToSelectedTab();\n        };\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHorizontalOrientation(this._getLayoutDirection())\n            .withHomeAndEnd()\n            .withWrap()\n            // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n            .skipPredicate(() => false);\n        this._keyManager.updateActiveItem(this._selectedIndex);\n        // Defer the first call in order to allow for slower browsers to lay out the elements.\n        // This helps in cases where the user lands directly on a page with paginated tabs.\n        // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n        // can hold up tests that are in a background tab.\n        this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n        // On dir change or window resize, realign the ink bar and update the orientation of\n        // the key manager if the direction has changed.\n        merge(dirChange, resize, this._items.changes, this._itemsResized())\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            // We need to defer this to give the browser some time to recalculate\n            // the element dimensions. The call has to be wrapped in `NgZone.run`,\n            // because the viewport change handler runs outside of Angular.\n            this._ngZone.run(() => {\n                Promise.resolve().then(() => {\n                    // Clamp the scroll distance, because it can change with the number of tabs.\n                    this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n                    realign();\n                });\n            });\n            this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n        });\n        // If there is a change in the focus key manager we need to emit the `indexFocused`\n        // event in order to provide a public event that notifies about focus changes. Also we realign\n        // the tabs container by scrolling the new focused tab into the visible section.\n        this._keyManager.change.subscribe(newFocusIndex => {\n            this.indexFocused.emit(newFocusIndex);\n            this._setTabFocus(newFocusIndex);\n        });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n        if (typeof ResizeObserver !== 'function') {\n            return EMPTY;\n        }\n        return this._items.changes.pipe(startWith(this._items), switchMap((tabItems) => new Observable((observer) => this._ngZone.runOutsideAngular(() => {\n            const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n            tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }))), \n        // Skip the first emit since the resize observer emits when an item\n        // is observed for new items when the tab is already inserted\n        skip(1), \n        // Skip emissions where all the elements are invisible since we don't want\n        // the header to try and re-render with invalid measurements. See #25574.\n        filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n        // If the number of tab labels have changed, check if scrolling should be enabled\n        if (this._tabLabelCount != this._items.length) {\n            this.updatePagination();\n            this._tabLabelCount = this._items.length;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the selected index has changed, scroll to the label and check if the scrolling controls\n        // should be disabled.\n        if (this._selectedIndexChanged) {\n            this._scrollToLabel(this._selectedIndex);\n            this._checkScrollingControls();\n            this._alignInkBarToSelectedTab();\n            this._selectedIndexChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n        // then translate the header to reflect this.\n        if (this._scrollDistanceChanged) {\n            this._updateTabScrollPosition();\n            this._scrollDistanceChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n        // We don't handle any key bindings with a modifier key.\n        if (hasModifierKey(event)) {\n            return;\n        }\n        switch (event.keyCode) {\n            case ENTER:\n            case SPACE:\n                if (this.focusIndex !== this.selectedIndex) {\n                    const item = this._items.get(this.focusIndex);\n                    if (item && !item.disabled) {\n                        this.selectFocusedIndex.emit(this.focusIndex);\n                        this._itemSelected(event);\n                    }\n                }\n                break;\n            default:\n                this._keyManager.onKeydown(event);\n        }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n        const textContent = this._elementRef.nativeElement.textContent;\n        // We need to diff the text content of the header, because the MutationObserver callback\n        // will fire even if the text content didn't change which is inefficient and is prone\n        // to infinite loops if a poorly constructed expression is passed in (see #14249).\n        if (textContent !== this._currentTextContent) {\n            this._currentTextContent = textContent || '';\n            // The content observer runs outside the `NgZone` by default, which\n            // means that we need to bring the callback back in ourselves.\n            this._ngZone.run(() => {\n                this.updatePagination();\n                this._alignInkBarToSelectedTab();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        this._checkPaginationEnabled();\n        this._checkScrollingControls();\n        this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n        if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n            return;\n        }\n        this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n        return this._items ? !!this._items.toArray()[index] : true;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n        if (this._showPaginationControls) {\n            this._scrollToLabel(tabIndex);\n        }\n        if (this._items && this._items.length) {\n            this._items.toArray()[tabIndex].focus();\n            // Do not let the browser manage scrolling to focus the element, this will be handled\n            // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n            // should be the full width minus the offset width.\n            const containerEl = this._tabListContainer.nativeElement;\n            const dir = this._getLayoutDirection();\n            if (dir == 'ltr') {\n                containerEl.scrollLeft = 0;\n            }\n            else {\n                containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n            }\n        }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n        if (this.disablePagination) {\n            return;\n        }\n        const scrollDistance = this.scrollDistance;\n        const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n        // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n        // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n        // and ripples will exceed the boundaries of the visible tab bar.\n        // See: https://github.com/angular/components/issues/10276\n        // We round the `transform` here, because transforms with sub-pixel precision cause some\n        // browsers to blur the content of the element.\n        this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n        // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n        // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n        // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n        // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n        if (this._platform.TRIDENT || this._platform.EDGE) {\n            this._tabListContainer.nativeElement.scrollLeft = 0;\n        }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n        return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n        this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        // Move the scroll distance one-third the length of the tab list's viewport.\n        const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n        return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n        this._stopInterval();\n        this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n        if (this.disablePagination) {\n            return;\n        }\n        const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n        if (!selectedLabel) {\n            return;\n        }\n        // The view length is the visible width of the tab labels.\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        const { offsetLeft, offsetWidth } = selectedLabel.elementRef.nativeElement;\n        let labelBeforePos, labelAfterPos;\n        if (this._getLayoutDirection() == 'ltr') {\n            labelBeforePos = offsetLeft;\n            labelAfterPos = labelBeforePos + offsetWidth;\n        }\n        else {\n            labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n            labelBeforePos = labelAfterPos - offsetWidth;\n        }\n        const beforeVisiblePos = this.scrollDistance;\n        const afterVisiblePos = this.scrollDistance + viewLength;\n        if (labelBeforePos < beforeVisiblePos) {\n            // Scroll header to move label to the before direction\n            this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n        }\n        else if (labelAfterPos > afterVisiblePos) {\n            // Scroll header to move label to the after direction\n            this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n        }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n        if (this.disablePagination) {\n            this._showPaginationControls = false;\n        }\n        else {\n            const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n            if (!isEnabled) {\n                this.scrollDistance = 0;\n            }\n            if (isEnabled !== this._showPaginationControls) {\n                this._changeDetectorRef.markForCheck();\n            }\n            this._showPaginationControls = isEnabled;\n        }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n        if (this.disablePagination) {\n            this._disableScrollAfter = this._disableScrollBefore = true;\n        }\n        else {\n            // Check if the pagination arrows should be activated.\n            this._disableScrollBefore = this.scrollDistance == 0;\n            this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n        const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n        const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n        const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n        if (selectedLabelWrapper) {\n            this._inkBar.alignToElement(selectedLabelWrapper);\n        }\n        else {\n            this._inkBar.hide();\n        }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n        this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n        // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n        // null check the `button`, but we do it so we don't break tests that use fake events.\n        if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n            return;\n        }\n        // Avoid overlapping timers.\n        this._stopInterval();\n        // Start a timer after the delay and keep firing based on the interval.\n        timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n            // Keep the timer going until something tells it to stop or the component is destroyed.\n            .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n            .subscribe(() => {\n            const { maxScrollDistance, distance } = this._scrollHeader(direction);\n            // Stop the timer if we've reached the start or the end.\n            if (distance === 0 || distance >= maxScrollDistance) {\n                this._stopInterval();\n            }\n        });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n        if (this.disablePagination) {\n            return { maxScrollDistance: 0, distance: 0 };\n        }\n        const maxScrollDistance = this._getMaxScrollDistance();\n        this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n        // Mark that the scroll distance has changed so that after the view is checked, the CSS\n        // transformation can move the header.\n        this._scrollDistanceChanged = true;\n        this._checkScrollingControls();\n        return { maxScrollDistance, distance: this._scrollDistance };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatedTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: i2.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatPaginatedTabHeader, inputs: { disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute] }, outputs: { selectFocusedIndex: \"selectFocusedIndex\", indexFocused: \"indexFocused\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatedTabHeader, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selectFocusedIndex: [{\n                type: Output\n            }], indexFocused: [{\n                type: Output\n            }] } });\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        /** Whether the ripple effect is disabled or not. */\n        this.disableRipple = false;\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n    _itemSelected(event) {\n        event.preventDefault();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: i2.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabHeader, isStandalone: true, selector: \"mat-tab-header\", inputs: { disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\" }, classAttribute: \"mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: MatTabLabelWrapper }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height);border-top-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                    }, standalone: true, imports: [MatRipple, CdkObserveContent], template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height);border-top-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatTabLabelWrapper, { descendants: false }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nconst matTabsAnimations = {\n    /** Animation translates a tab along the X axis. */\n    translateTab: trigger('translateTab', [\n        // Transitions to `none` instead of 0, because some browsers might blur the content.\n        state('center, void, left-origin-center, right-origin-center', style({ transform: 'none' })),\n        // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n        // in order to ensure that the element has a height before its state changes. This is\n        // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n        // not have a static height and is not rendered. See related issue: #9465\n        state('left', style({\n            transform: 'translate3d(-100%, 0, 0)',\n            minHeight: '1px',\n            // Normally this is redundant since we detach the content from the DOM, but if the user\n            // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n            visibility: 'hidden',\n        })),\n        state('right', style({\n            transform: 'translate3d(100%, 0, 0)',\n            minHeight: '1px',\n            visibility: 'hidden',\n        })),\n        transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')),\n        transition('void => left-origin-center', [\n            style({ transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n        transition('void => right-origin-center', [\n            style({ transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n    ]),\n};\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n    constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n        super(componentFactoryResolver, viewContainerRef, _document);\n        this._host = _host;\n        /** Subscription to events for when the tab body begins centering. */\n        this._centeringSub = Subscription.EMPTY;\n        /** Subscription to events for when the tab body finishes leaving from center position. */\n        this._leavingSub = Subscription.EMPTY;\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n        super.ngOnInit();\n        this._centeringSub = this._host._beforeCentering\n            .pipe(startWith(this._host._isCenterPosition(this._host._position)))\n            .subscribe((isCentering) => {\n            if (isCentering && !this.hasAttached()) {\n                this.attach(this._host._content);\n            }\n        });\n        this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n            if (!this._host.preserveContent) {\n                this.detach();\n            }\n        });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._centeringSub.unsubscribe();\n        this._leavingSub.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBodyPortal, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: forwardRef(() => MatTabBody) }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabBodyPortal, isStandalone: true, selector: \"[matTabBodyHost]\", usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBodyPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabBodyHost]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: MatTabBody, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatTabBody)]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n        this._positionIndex = position;\n        this._computePositionAnimationState();\n    }\n    constructor(_elementRef, _dir, changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        /** Subscription to the directionality change observable. */\n        this._dirChangeSubscription = Subscription.EMPTY;\n        /** Emits when an animation on the tab is complete. */\n        this._translateTabComplete = new Subject();\n        /** Event emitted when the tab begins to animate towards the center as the active tab. */\n        this._onCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._beforeCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._afterLeavingCenter = new EventEmitter();\n        /** Event emitted when the tab completes its animation towards the center. */\n        this._onCentered = new EventEmitter(true);\n        // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n        // anyway to prevent the animations module from throwing an error if the body is used on its own.\n        /** Duration for the tab's animation. */\n        this.animationDuration = '500ms';\n        /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n        this.preserveContent = false;\n        if (_dir) {\n            this._dirChangeSubscription = _dir.change.subscribe((dir) => {\n                this._computePositionAnimationState(dir);\n                changeDetectorRef.markForCheck();\n            });\n        }\n        // Ensure that we get unique animation events, because the `.done` callback can get\n        // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n        this._translateTabComplete\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            // If the transition to the center is complete, emit an event.\n            if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n                this._onCentered.emit();\n            }\n            if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n                this._afterLeavingCenter.emit();\n            }\n        });\n    }\n    /**\n     * After initialized, check if the content is centered and has an origin. If so, set the\n     * special position states that transition the tab from the left or right before centering.\n     */\n    ngOnInit() {\n        if (this._position == 'center' && this.origin != null) {\n            this._position = this._computePositionFromOrigin(this.origin);\n        }\n    }\n    ngOnDestroy() {\n        this._dirChangeSubscription.unsubscribe();\n        this._translateTabComplete.complete();\n    }\n    _onTranslateTabStarted(event) {\n        const isCentering = this._isCenterPosition(event.toState);\n        this._beforeCentering.emit(isCentering);\n        if (isCentering) {\n            this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n        }\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition(position) {\n        return (position == 'center' || position == 'left-origin-center' || position == 'right-origin-center');\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n        if (this._positionIndex < 0) {\n            this._position = dir == 'ltr' ? 'left' : 'right';\n        }\n        else if (this._positionIndex > 0) {\n            this._position = dir == 'ltr' ? 'right' : 'left';\n        }\n        else {\n            this._position = 'center';\n        }\n    }\n    /**\n     * Computes the position state based on the specified origin position. This is used if the\n     * tab is becoming visible immediately after creation.\n     */\n    _computePositionFromOrigin(origin) {\n        const dir = this._getLayoutDirection();\n        if ((dir == 'ltr' && origin <= 0) || (dir == 'rtl' && origin > 0)) {\n            return 'left-origin-center';\n        }\n        return 'right-origin-center';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBody, deps: [{ token: i0.ElementRef }, { token: i2.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabBody, isStandalone: true, selector: \"mat-tab-body\", inputs: { _content: [\"content\", \"_content\"], origin: \"origin\", animationDuration: \"animationDuration\", preserveContent: \"preserveContent\", position: \"position\" }, outputs: { _onCentering: \"_onCentering\", _beforeCentering: \"_beforeCentering\", _afterLeavingCenter: \"_afterLeavingCenter\", _onCentered: \"_onCentered\" }, host: { classAttribute: \"mat-mdc-tab-body\" }, viewQueries: [{ propertyName: \"_portalHost\", first: true, predicate: CdkPortalOutlet, descendants: true }], ngImport: i0, template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"], dependencies: [{ kind: \"directive\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\" }, { kind: \"directive\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matTabsAnimations.translateTab], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBody, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-body', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, animations: [matTabsAnimations.translateTab], host: {\n                        'class': 'mat-mdc-tab-body',\n                    }, standalone: true, imports: [MatTabBodyPortal, CdkScrollable], template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }], propDecorators: { _onCentering: [{\n                type: Output\n            }], _beforeCentering: [{\n                type: Output\n            }], _afterLeavingCenter: [{\n                type: Output\n            }], _onCentered: [{\n                type: Output\n            }], _portalHost: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet]\n            }], _content: [{\n                type: Input,\n                args: ['content']\n            }], origin: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }] } });\n\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n/** Boolean constant that determines whether the tab group supports the `backgroundColor` input */\nconst ENABLE_BACKGROUND_INPUT = true;\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        this._indexToSelect = isNaN(value) ? null : value;\n    }\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n        return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n        this._contentTabIndex = isNaN(value) ? null : value;\n    }\n    /**\n     * Background color of the tab group.\n     * @deprecated The background color should be customized through Sass theming APIs.\n     * @breaking-change 20.0.0 Remove this input\n     */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        if (!ENABLE_BACKGROUND_INPUT) {\n            throw new Error(`mat-tab-group background color must be set through the Sass theming API`);\n        }\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    constructor(_elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** All of the tabs that belong to the group. */\n        this._tabs = new QueryList();\n        /** The tab index that should be selected after the content has been checked. */\n        this._indexToSelect = 0;\n        /** Index of the tab that was focused last. */\n        this._lastFocusedTabIndex = null;\n        /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n        this._tabBodyWrapperHeight = 0;\n        /** Subscription to tabs being added/removed. */\n        this._tabsSubscription = Subscription.EMPTY;\n        /** Subscription to changes in the tab labels. */\n        this._tabLabelSubscription = Subscription.EMPTY;\n        this._fitInkBarToContent = false;\n        /** Whether tabs should be stretched to fill the header. */\n        this.stretchTabs = true;\n        /** Whether the tab group should grow to the size of the active tab. */\n        this.dynamicHeight = false;\n        this._selectedIndex = null;\n        /** Position of the tab header. */\n        this.headerPosition = 'above';\n        /**\n         * Whether pagination should be disabled. This can be used to avoid unnecessary\n         * layout recalculations if it's known that pagination won't be required.\n         */\n        this.disablePagination = false;\n        /** Whether ripples in the tab group are disabled. */\n        this.disableRipple = false;\n        /**\n         * By default tabs remove their content from the DOM while it's off-screen.\n         * Setting this to `true` will keep it in the DOM which will prevent elements\n         * like iframes and videos from reloading next time it comes back into the view.\n         */\n        this.preserveContent = false;\n        /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n        this.selectedIndexChange = new EventEmitter();\n        /** Event emitted when focus has changed within a tab group. */\n        this.focusChange = new EventEmitter();\n        /** Event emitted when the body animation has completed */\n        this.animationDone = new EventEmitter();\n        /** Event emitted when the tab selection has changed. */\n        this.selectedTabChange = new EventEmitter(true);\n        /** Whether the tab group is rendered on the server. */\n        this._isServer = !inject(Platform).isBrowser;\n        this._groupId = nextId++;\n        this.animationDuration =\n            defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.dynamicHeight =\n            defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n        if (defaultConfig?.contentTabIndex != null) {\n            this.contentTabIndex = defaultConfig.contentTabIndex;\n        }\n        this.preserveContent = !!defaultConfig?.preserveContent;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n        // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n        // the amount of tabs changes before the actual change detection runs.\n        const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n        // If there is a change in selected index, emit a change event. Should not trigger if\n        // the selected index has not yet been initialized.\n        if (this._selectedIndex != indexToSelect) {\n            const isFirstRun = this._selectedIndex == null;\n            if (!isFirstRun) {\n                this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                // Preserve the height so page doesn't scroll up during tab change.\n                // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n                const wrapper = this._tabBodyWrapper.nativeElement;\n                wrapper.style.minHeight = wrapper.clientHeight + 'px';\n            }\n            // Changing these values after change detection has run\n            // since the checked content may contain references to them.\n            Promise.resolve().then(() => {\n                this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n                if (!isFirstRun) {\n                    this.selectedIndexChange.emit(indexToSelect);\n                    // Clear the min-height, this was needed during tab change to avoid\n                    // unnecessary scrolling.\n                    this._tabBodyWrapper.nativeElement.style.minHeight = '';\n                }\n            });\n        }\n        // Setup the position for each tab and optionally setup an origin on the next selected tab.\n        this._tabs.forEach((tab, index) => {\n            tab.position = index - indexToSelect;\n            // If there is already a selected tab, then set up an origin for the next selected tab\n            // if it doesn't have one already.\n            if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n                tab.origin = indexToSelect - this._selectedIndex;\n            }\n        });\n        if (this._selectedIndex !== indexToSelect) {\n            this._selectedIndex = indexToSelect;\n            this._lastFocusedTabIndex = null;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngAfterContentInit() {\n        this._subscribeToAllTabChanges();\n        this._subscribeToTabLabels();\n        // Subscribe to changes in the amount of tabs, in order to be\n        // able to re-render the content as new tabs are added or removed.\n        this._tabsSubscription = this._tabs.changes.subscribe(() => {\n            const indexToSelect = this._clampTabIndex(this._indexToSelect);\n            // Maintain the previously-selected tab if a new tab is added or removed and there is no\n            // explicit change that selects a different tab.\n            if (indexToSelect === this._selectedIndex) {\n                const tabs = this._tabs.toArray();\n                let selectedTab;\n                for (let i = 0; i < tabs.length; i++) {\n                    if (tabs[i].isActive) {\n                        // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n                        // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n                        // adding a tab within the `selectedIndexChange` event.\n                        this._indexToSelect = this._selectedIndex = i;\n                        this._lastFocusedTabIndex = null;\n                        selectedTab = tabs[i];\n                        break;\n                    }\n                }\n                // If we haven't found an active tab and a tab exists at the selected index, it means\n                // that the active tab was swapped out. Since this won't be picked up by the rendering\n                // loop in `ngAfterContentChecked`, we need to sync it up manually.\n                if (!selectedTab && tabs[indexToSelect]) {\n                    Promise.resolve().then(() => {\n                        tabs[indexToSelect].isActive = true;\n                        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                    });\n                }\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n        // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n        // some that are inside of nested tab groups. We filter them out manually by checking that\n        // the closest group to the tab is the current one.\n        this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs) => {\n            this._tabs.reset(tabs.filter(tab => {\n                return tab._closestTabGroup === this || !tab._closestTabGroup;\n            }));\n            this._tabs.notifyOnChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._tabs.destroy();\n        this._tabsSubscription.unsubscribe();\n        this._tabLabelSubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n        if (this._tabHeader) {\n            this._tabHeader._alignInkBarToSelectedTab();\n        }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        if (this._tabHeader) {\n            this._tabHeader.updatePagination();\n        }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n        const header = this._tabHeader;\n        if (header) {\n            header.focusIndex = index;\n        }\n    }\n    _focusChanged(index) {\n        this._lastFocusedTabIndex = index;\n        this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n        const event = new MatTabChangeEvent();\n        event.index = index;\n        if (this._tabs && this._tabs.length) {\n            event.tab = this._tabs.toArray()[index];\n        }\n        return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n        if (this._tabLabelSubscription) {\n            this._tabLabelSubscription.unsubscribe();\n        }\n        this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n        // Note the `|| 0`, which ensures that values like NaN can't get through\n        // and which would otherwise throw the component into an infinite loop\n        // (since Math.max(NaN, 0) === NaN).\n        return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(i) {\n        return `mat-tab-label-${this._groupId}-${i}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(i) {\n        return `mat-tab-content-${this._groupId}-${i}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n        if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n            return;\n        }\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n        // This conditional forces the browser to paint the height so that\n        // the animation to the new height can have an origin.\n        if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n            wrapper.style.height = tabHeight + 'px';\n        }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        this._tabBodyWrapperHeight = wrapper.clientHeight;\n        wrapper.style.height = '';\n        this.animationDone.emit();\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n        tabHeader.focusIndex = index;\n        if (!tab.disabled) {\n            this.selectedIndex = index;\n        }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(index) {\n        const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n        return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n        // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n        // can cause the tab to be moved out from under the pointer, interrupting the\n        // click sequence (see #21898). We don't need to scroll the tab into view for\n        // such cases anyway, because it will be done when the tab becomes selected.\n        if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n            this._tabHeader.focusIndex = index;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabGroup, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatTabGroup, isStandalone: true, selector: \"mat-tab-group\", inputs: { color: \"color\", fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], dynamicHeight: [\"dynamicHeight\", \"dynamicHeight\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute], headerPosition: \"headerPosition\", animationDuration: \"animationDuration\", contentTabIndex: [\"contentTabIndex\", \"contentTabIndex\", numberAttribute], disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], preserveContent: [\"preserveContent\", \"preserveContent\", booleanAttribute], backgroundColor: \"backgroundColor\" }, outputs: { selectedIndexChange: \"selectedIndexChange\", focusChange: \"focusChange\", animationDone: \"animationDone\", selectedTabChange: \"selectedTabChange\" }, host: { properties: { \"class\": \"\\\"mat-\\\" + (color || \\\"primary\\\")\", \"class.mat-mdc-tab-group-dynamic-height\": \"dynamicHeight\", \"class.mat-mdc-tab-group-inverted-header\": \"headerPosition === \\\"below\\\"\", \"class.mat-mdc-tab-group-stretch-tabs\": \"stretchTabs\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-group\" }, providers: [\n            {\n                provide: MAT_TAB_GROUP,\n                useExisting: MatTabGroup,\n            },\n        ], queries: [{ propertyName: \"_allTabs\", predicate: MatTab, descendants: true }], viewQueries: [{ propertyName: \"_tabBodyWrapper\", first: true, predicate: [\"tabBodyWrapper\"], descendants: true }, { propertyName: \"_tabHeader\", first: true, predicate: [\"tabHeader\"], descendants: true }], exportAs: [\"matTabGroup\"], ngImport: i0, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(i)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n        [attr.aria-posinset]=\\\"i + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId(i)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n                 [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [origin]=\\\"tab.origin\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n    </mat-tab-body>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"], dependencies: [{ kind: \"component\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: [\"disableRipple\"] }, { kind: \"directive\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: [\"disabled\"] }, { kind: \"directive\", type: CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: [\"cdkFocusChange\"], exportAs: [\"cdkMonitorFocus\"] }, { kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }, { kind: \"component\", type: MatTabBody, selector: \"mat-tab-body\", inputs: [\"content\", \"origin\", \"animationDuration\", \"preserveContent\", \"position\"], outputs: [\"_onCentering\", \"_beforeCentering\", \"_afterLeavingCenter\", \"_onCentered\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-group', exportAs: 'matTabGroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        {\n                            provide: MAT_TAB_GROUP,\n                            useExisting: MatTabGroup,\n                        },\n                    ], host: {\n                        'class': 'mat-mdc-tab-group',\n                        '[class]': '\"mat-\" + (color || \"primary\")',\n                        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n                        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n                        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, standalone: true, imports: [\n                        MatTabHeader,\n                        MatTabLabelWrapper,\n                        CdkMonitorFocus,\n                        MatRipple,\n                        CdkPortalOutlet,\n                        MatTabBody,\n                    ], template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(i)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n        [attr.aria-posinset]=\\\"i + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId(i)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n                 [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [origin]=\\\"tab.origin\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n    </mat-tab-body>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _allTabs: [{\n                type: ContentChildren,\n                args: [MatTab, { descendants: true }]\n            }], _tabBodyWrapper: [{\n                type: ViewChild,\n                args: ['tabBodyWrapper']\n            }], _tabHeader: [{\n                type: ViewChild,\n                args: ['tabHeader']\n            }], color: [{\n                type: Input\n            }], fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], dynamicHeight: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], contentTabIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], preserveContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], backgroundColor: [{\n                type: Input\n            }], selectedIndexChange: [{\n                type: Output\n            }], focusChange: [{\n                type: Output\n            }], animationDone: [{\n                type: Output\n            }], selectedTabChange: [{\n                type: Output\n            }] } });\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n}\n\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent.value;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent.next(value);\n        this._changeDetectorRef.markForCheck();\n    }\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    /** Background color of the tab nav. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._fitInkBarToContent = new BehaviorSubject(false);\n        /** Whether tabs should be stretched to fill the header. */\n        this.stretchTabs = true;\n        /** Whether the ripple effect is disabled or not. */\n        this.disableRipple = false;\n        /** Theme color of the nav bar. */\n        this.color = 'primary';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    _itemSelected() {\n        // noop\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        // We need this to run before the `changes` subscription in parent to ensure that the\n        // selectedIndex is up-to-date by the time the super class starts looking for it.\n        this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            this.updateActiveLink();\n        });\n        super.ngAfterContentInit();\n    }\n    ngAfterViewInit() {\n        if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n        }\n        super.ngAfterViewInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n        if (!this._items) {\n            return;\n        }\n        const items = this._items.toArray();\n        for (let i = 0; i < items.length; i++) {\n            if (items[i].active) {\n                this.selectedIndex = i;\n                this._changeDetectorRef.markForCheck();\n                if (this.tabPanel) {\n                    this.tabPanel._activeTabId = items[i].id;\n                }\n                return;\n            }\n        }\n        // The ink bar should hide itself if no items are active.\n        this.selectedIndex = -1;\n        this._inkBar.hide();\n    }\n    _getRole() {\n        return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNav, deps: [{ token: i0.ElementRef }, { token: i2.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_TABS_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabNav, isStandalone: true, selector: \"[mat-tab-nav-bar]\", inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], animationDuration: \"animationDuration\", backgroundColor: \"backgroundColor\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], color: \"color\", tabPanel: \"tabPanel\" }, host: { properties: { \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\", \"class.mat-mdc-tab-nav-bar-stretch-tabs\": \"stretchTabs\", \"class.mat-primary\": \"color !== \\\"warn\\\" && color !== \\\"accent\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-nav-bar mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: i0.forwardRef(() => MatTabLink), descendants: true }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], exportAs: [\"matTabNavBar\", \"matTabNav\"], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNav, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-nav-bar]', exportAs: 'matTabNavBar, matTabNav', host: {\n                        '[attr.role]': '_getRole()',\n                        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n                        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, standalone: true, imports: [MatRipple, CdkObserveContent], template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }] }], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], animationDuration: [{\n                type: Input\n            }], _items: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatTabLink), { descendants: true }]\n            }], backgroundColor: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], color: [{\n                type: Input\n            }], tabPanel: [{\n                type: Input\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n    /** Whether the link is active. */\n    get active() {\n        return this._isActive;\n    }\n    set active(value) {\n        if (value !== this._isActive) {\n            this._isActive = value;\n            this._tabNavBar.updateActiveLink();\n        }\n    }\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._tabNavBar.disableRipple ||\n            !!this.rippleConfig.disabled);\n    }\n    constructor(_tabNavBar, \n    /** @docs-private */ elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n        super();\n        this._tabNavBar = _tabNavBar;\n        this.elementRef = elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._destroyed = new Subject();\n        /** Whether the tab link is active or not. */\n        this._isActive = false;\n        /** Whether the tab link is disabled. */\n        this.disabled = false;\n        /** Whether ripples are disabled on the tab link. */\n        this.disableRipple = false;\n        this.tabIndex = 0;\n        /** Unique id for the tab. */\n        this.id = `mat-tab-link-${nextUniqueId++}`;\n        this.rippleConfig = globalRippleOptions || {};\n        this.tabIndex = parseInt(tabIndex) || 0;\n        if (animationMode === 'NoopAnimations') {\n            this.rippleConfig.animation = { enterDuration: 0, exitDuration: 0 };\n        }\n        _tabNavBar._fitInkBarToContent\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(fitInkBarToContent => {\n            this.fitInkBarToContent = fitInkBarToContent;\n        });\n    }\n    /** Focuses the tab link. */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        super.ngOnDestroy();\n        this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n        // Since we allow navigation through tabbing in the nav bar, we\n        // have to update the focused index whenever the link receives focus.\n        this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === SPACE || event.keyCode === ENTER) {\n            if (this.disabled) {\n                event.preventDefault();\n            }\n            else if (this._tabNavBar.tabPanel) {\n                // Only prevent the default action on space since it can scroll the page.\n                // Don't prevent enter since it can break link navigation.\n                if (event.keyCode === SPACE) {\n                    event.preventDefault();\n                }\n                this.elementRef.nativeElement.click();\n            }\n        }\n    }\n    _getAriaControls() {\n        return this._tabNavBar.tabPanel\n            ? this._tabNavBar.tabPanel?.id\n            : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n        if (this._tabNavBar.tabPanel) {\n            return this.active ? 'true' : 'false';\n        }\n        else {\n            return this.elementRef.nativeElement.getAttribute('aria-selected');\n        }\n    }\n    _getAriaCurrent() {\n        return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n        return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    _getTabIndex() {\n        if (this._tabNavBar.tabPanel) {\n            return this._isActive && !this.disabled ? 0 : -1;\n        }\n        else {\n            return this.disabled ? -1 : this.tabIndex;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLink, deps: [{ token: MatTabNav }, { token: i0.ElementRef }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i4.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabLink, isStandalone: true, selector: \"[mat-tab-link], [matTabLink]\", inputs: { active: [\"active\", \"active\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], id: \"id\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-controls\": \"_getAriaControls()\", \"attr.aria-current\": \"_getAriaCurrent()\", \"attr.aria-disabled\": \"disabled\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.id\": \"id\", \"attr.tabIndex\": \"_getTabIndex()\", \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-disabled\": \"disabled\", \"class.mdc-tab--active\": \"active\" }, classAttribute: \"mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator\" }, exportAs: [\"matTabLink\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLink, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-link], [matTabLink]', exportAs: 'matTabLink', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n                        '[attr.aria-controls]': '_getAriaControls()',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.id]': 'id',\n                        '[attr.tabIndex]': '_getTabIndex()',\n                        '[attr.role]': '_getRole()',\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[class.mdc-tab--active]': 'active',\n                        '(focus)': '_handleFocus()',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, standalone: true, imports: [MatRipple], template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"] }]\n        }], ctorParameters: () => [{ type: MatTabNav }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { active: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], id: [{\n                type: Input\n            }] } });\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n    constructor() {\n        /** Unique id for the tab panel. */\n        this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNavPanel, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabNavPanel, isStandalone: true, selector: \"mat-tab-nav-panel\", inputs: { id: \"id\" }, host: { attributes: { \"role\": \"tabpanel\" }, properties: { \"attr.aria-labelledby\": \"_activeTabId\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-tab-nav-panel\" }, exportAs: [\"matTabNavPanel\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNavPanel, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-tab-nav-panel',\n                    exportAs: 'matTabNavPanel',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        '[attr.aria-labelledby]': '_activeTabId',\n                        '[attr.id]': 'id',\n                        'class': 'mat-mdc-tab-nav-panel',\n                        'role': 'tabpanel',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    standalone: true,\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\nclass MatTabsModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink], exports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                    exports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,MAAM,EAAEC,eAAe,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAChV,SAASC,SAAS,EAAEC,yBAAyB,EAAEC,eAAe,QAAQ,wBAAwB;AAC9F,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AAChF,SAASC,OAAO,EAAEC,SAAS,EAAEC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,eAAe,QAAQ,MAAM;AAC7G,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,+BAA+B,EAAEC,QAAQ,QAAQ,uBAAuB;AACjF,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,EAAEC,eAAe,QAAQ,mBAAmB;AACpE,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,oBAAoB,QAAQ,gBAAgB;AAC1G,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;;AAEhF;AACA;AACA;AACA;AACA;AAJA,MAAAC,GAAA;AAAA,SAAAC,8BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAWoGnE,EAAE,CAAAqE,YAAA,EAwHqiC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,iBAAA,EAAAD;AAAA;AAAA,MAAAE,GAAA,GAAAA,CAAAF,EAAA,EAAAG,EAAA;EAAAC,KAAA,EAAAJ,EAAA;EAAAK,MAAA,EAAAF;AAAA;AAAA,SAAAG,kCAAAf,EAAA,EAAAC,GAAA;AAAA,MAAAe,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uDAAAlB,EAAA,EAAAC,GAAA;AAAA,SAAAkB,yCAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxHxiCnE,EAAE,CAAAuF,UAAA,IAAAF,sDAAA,yBAq8C+xE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAqB,MAAA,GAr8ClyExF,EAAE,CAAAyF,aAAA,GAAAC,SAAA;IAAF1F,EAAE,CAAA2F,UAAA,oBAAAH,MAAA,CAAAI,aAq8C8xE,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAr8CjyEnE,EAAE,CAAA8F,MAAA,EAq8Cm1E,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAqB,MAAA,GAr8Ct1ExF,EAAE,CAAAyF,aAAA,GAAAC,SAAA;IAAF1F,EAAE,CAAA+F,iBAAA,CAAAP,MAAA,CAAAQ,SAq8Cm1E,CAAC;EAAA;AAAA;AAAA,SAAAC,2BAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GAr8Ct1ElG,EAAE,CAAAmG,gBAAA;IAAFnG,EAAE,CAAAoG,cAAA,eAq8Cg+C,CAAC;IAr8Cn+CpG,EAAE,CAAAqG,UAAA,mBAAAC,gDAAA;MAAA,MAAAC,MAAA,GAAFvG,EAAE,CAAAwG,aAAA,CAAAN,GAAA;MAAA,MAAAV,MAAA,GAAAe,MAAA,CAAAb,SAAA;MAAA,MAAAe,IAAA,GAAAF,MAAA,CAAAG,MAAA;MAAA,MAAAC,MAAA,GAAF3G,EAAE,CAAAyF,aAAA;MAAA,MAAAmB,YAAA,GAAF5G,EAAE,CAAA6G,WAAA;MAAA,OAAF7G,EAAE,CAAA8G,WAAA,CAq8Cq4CH,MAAA,CAAAI,YAAA,CAAAvB,MAAA,EAAAoB,YAAA,EAAAH,IAA8B,CAAC;IAAA,CAAC,CAAC,4BAAAO,yDAAAC,MAAA;MAAA,MAAAR,IAAA,GAr8Cx6CzG,EAAE,CAAAwG,aAAA,CAAAN,GAAA,EAAAQ,MAAA;MAAA,MAAAC,MAAA,GAAF3G,EAAE,CAAAyF,aAAA;MAAA,OAAFzF,EAAE,CAAA8G,WAAA,CAq8Cm8CH,MAAA,CAAAO,gBAAA,CAAAD,MAAA,EAAAR,IAA0B,CAAC;IAAA,CAAC,CAAC;IAr8Cl+CzG,EAAE,CAAAmH,SAAA,aAq8C+gD,CAAC,YAAmT,CAAC;IAr8Ct0DnH,EAAE,CAAAoG,cAAA,cAq8C82D,CAAC,cAA6C,CAAC;IAr8C/5DpG,EAAE,CAAAuF,UAAA,IAAAD,wCAAA,gBAq8C4tE,CAAC,IAAAO,wCAAA,MAAqG,CAAC;IAr8Cr0E7F,EAAE,CAAAoH,YAAA,CAq8Cq2E,CAAC,CAAc,CAAC,CAAW,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAqB,MAAA,GAAApB,GAAA,CAAAsB,SAAA;IAAA,MAAAe,IAAA,GAAArC,GAAA,CAAAsC,MAAA;IAAA,MAAAW,UAAA,GAr8Cn4ErH,EAAE,CAAA6G,WAAA;IAAA,MAAAF,MAAA,GAAF3G,EAAE,CAAAyF,aAAA;IAAFzF,EAAE,CAAAsH,UAAA,CAAA9B,MAAA,CAAA+B,UAq8CsxC,CAAC;IAr8CzxCvH,EAAE,CAAAwH,WAAA,oBAAAb,MAAA,CAAAc,aAAA,KAAAhB,IAq8CkvC,CAAC;IAr8CrvCzG,EAAE,CAAA2F,UAAA,OAAAgB,MAAA,CAAAe,cAAA,CAAAjB,IAAA,CAq8C8yB,CAAC,aAAAjB,MAAA,CAAAmC,QAA4gB,CAAC,uBAAAhB,MAAA,CAAAiB,kBAAoD,CAAC;IAr8Cn3C5H,EAAE,CAAA6H,WAAA,aAAAlB,MAAA,CAAAmB,YAAA,CAAArB,IAAA,oBAAAA,IAAA,sBAAAE,MAAA,CAAAoB,KAAA,CAAAC,MAAA,mBAAArB,MAAA,CAAAsB,gBAAA,CAAAxB,IAAA,oBAAAE,MAAA,CAAAc,aAAA,KAAAhB,IAAA,gBAAAjB,MAAA,CAAA0C,SAAA,8BAAA1C,MAAA,CAAA0C,SAAA,IAAA1C,MAAA,CAAA2C,cAAA,GAAA3C,MAAA,CAAA2C,cAAA;IAAFnI,EAAE,CAAAoI,SAAA,EAq8C6vD,CAAC;IAr8ChwDpI,EAAE,CAAA2F,UAAA,qBAAA0B,UAq8C6vD,CAAC,sBAAA7B,MAAA,CAAAmC,QAAA,IAAAhB,MAAA,CAAA0B,aAA8D,CAAC;IAr8C/zDrI,EAAE,CAAAoI,SAAA,EAq8Co1E,CAAC;IAr8Cv1EpI,EAAE,CAAAsI,aAAA,IAAA9C,MAAA,CAAAI,aAAA,QAq8Co1E,CAAC;EAAA;AAAA;AAAA,SAAA2C,mCAAApE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAr8Cv1EnE,EAAE,CAAAqE,YAAA,EAq8C8uF,CAAC;EAAA;AAAA;AAAA,SAAAmE,2BAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsE,GAAA,GAr8CjvFzI,EAAE,CAAAmG,gBAAA;IAAFnG,EAAE,CAAAoG,cAAA,sBAq8C2uH,CAAC;IAr8C9uHpG,EAAE,CAAAqG,UAAA,yBAAAqC,+DAAA;MAAF1I,EAAE,CAAAwG,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAF3G,EAAE,CAAAyF,aAAA;MAAA,OAAFzF,EAAE,CAAA8G,WAAA,CAq8CsoHH,MAAA,CAAAgC,2BAAA,CAA4B,CAAC;IAAA,CAAC,CAAC,0BAAAC,gEAAA3B,MAAA;MAr8CvqHjH,EAAE,CAAAwG,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAF3G,EAAE,CAAAyF,aAAA;MAAA,OAAFzF,EAAE,CAAA8G,WAAA,CAq8CysHH,MAAA,CAAAkC,wBAAA,CAAA5B,MAA+B,CAAC;IAAA,CAAC,CAAC;IAr8C7uHjH,EAAE,CAAAoH,YAAA,CAq8CgwH,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAA2E,OAAA,GAAA1E,GAAA,CAAAsB,SAAA;IAAA,MAAAqD,KAAA,GAAA3E,GAAA,CAAAsC,MAAA;IAAA,MAAAC,MAAA,GAr8CnwH3G,EAAE,CAAAyF,aAAA;IAAFzF,EAAE,CAAAsH,UAAA,CAAAwB,OAAA,CAAAE,SAq8Cw2G,CAAC;IAr8C32GhJ,EAAE,CAAAwH,WAAA,4BAAAb,MAAA,CAAAc,aAAA,KAAAsB,KAq8C4zG,CAAC;IAr8C/zG/I,EAAE,CAAA2F,UAAA,OAAAgB,MAAA,CAAAsB,gBAAA,CAAAc,KAAA,CAq8CsgG,CAAC,YAAAD,OAAA,CAAAG,OAA8Y,CAAC,aAAAH,OAAA,CAAAI,QAA8C,CAAC,WAAAJ,OAAA,CAAAK,MAAyC,CAAC,sBAAAxC,MAAA,CAAA9B,iBAA2D,CAAC,oBAAA8B,MAAA,CAAAyC,eAAuD,CAAC;IAr8CrmHpJ,EAAE,CAAA6H,WAAA,aAAAlB,MAAA,CAAA0C,eAAA,YAAA1C,MAAA,CAAAc,aAAA,KAAAsB,KAAA,GAAApC,MAAA,CAAA0C,eAAA,4BAAA1C,MAAA,CAAAe,cAAA,CAAAqB,KAAA,kBAAApC,MAAA,CAAAc,aAAA,KAAAsB,KAAA;EAAA;AAAA;AAAA,MAAAO,IAAA;AAAA,MAAAC,IAAA;AANtG,MAAMC,eAAe,GAAG,IAAIvJ,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAMwJ,aAAa,CAAC;EAChBC,WAAWA,CAAC,2BAA4BC,QAAQ,EAAE;IAC9C,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFL,aAAa,EAAvBzJ,EAAE,CAAA+J,iBAAA,CAAuC/J,EAAE,CAACM,WAAW;IAAA,CAA4C;EAAE;EACrM;IAAS,IAAI,CAAC0J,IAAI,kBAD8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EACJT,aAAa;MAAAU,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADXrK,EAAE,CAAAsK,kBAAA,CACuE,CAAC;QAAEC,OAAO,EAAEf,eAAe;QAAEgB,WAAW,EAAEf;MAAc,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC1P;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAHoGzK,EAAE,CAAA0K,iBAAA,CAGXjB,aAAa,EAAc,CAAC;IAC3GS,IAAI,EAAEhK,SAAS;IACfyK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEf,eAAe;QAAEgB,WAAW,EAAEf;MAAc,CAAC,CAAC;MACrEW,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAElK,EAAE,CAACM;EAAY,CAAC,CAAC;AAAA;;AAE5D;AACA;AACA;AACA;AACA;AACA,MAAMwK,aAAa,GAAG,IAAI7K,cAAc,CAAC,aAAa,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAM8K,OAAO,GAAG,IAAI9K,cAAc,CAAC,SAAS,CAAC;AAC7C;AACA,MAAM+K,WAAW,SAASrJ,SAAS,CAAC;EAChC+H,WAAWA,CAACuB,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,EAAE;IACpD,KAAK,CAACF,WAAW,EAAEC,gBAAgB,CAAC;IACpC,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACA;IAAS,IAAI,CAACvB,IAAI,YAAAwB,oBAAAtB,CAAA;MAAA,YAAAA,CAAA,IAAwFkB,WAAW,EA7BrBhL,EAAE,CAAA+J,iBAAA,CA6BqC/J,EAAE,CAACM,WAAW,GA7BrDN,EAAE,CAAA+J,iBAAA,CA6BgE/J,EAAE,CAACqL,gBAAgB,GA7BrFrL,EAAE,CAAA+J,iBAAA,CA6BgGgB,OAAO;IAAA,CAA4D;EAAE;EACvQ;IAAS,IAAI,CAACf,IAAI,kBA9B8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EA8BJc,WAAW;MAAAb,SAAA;MAAAC,UAAA;MAAAC,QAAA,GA9BTrK,EAAE,CAAAsK,kBAAA,CA8BoF,CAAC;QAAEC,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAEQ;MAAY,CAAC,CAAC,GA9B5IhL,EAAE,CAAAsL,0BAAA;IAAA,EA8BkL;EAAE;AAC1R;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAhCoGzK,EAAE,CAAA0K,iBAAA,CAgCXM,WAAW,EAAc,CAAC;IACzGd,IAAI,EAAEhK,SAAS;IACfyK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAEQ;MAAY,CAAC,CAAC;MACjEZ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAElK,EAAE,CAACM;EAAY,CAAC,EAAE;IAAE4J,IAAI,EAAElK,EAAE,CAACqL;EAAiB,CAAC,EAAE;IAAEnB,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxGtB,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAACI,OAAO;IAClB,CAAC,EAAE;MACCb,IAAI,EAAE9J;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA,MAAMqL,aAAa,GAAG,IAAIxL,cAAc,CAAC,eAAe,CAAC;AACzD,MAAMyL,MAAM,CAAC;EACT;EACA,IAAI9F,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC+F,cAAc;EAC9B;EACA,IAAI/F,aAAaA,CAACZ,KAAK,EAAE;IACrB,IAAI,CAAC4G,sBAAsB,CAAC5G,KAAK,CAAC;EACtC;EACA;EACA,IAAIiE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4C,cAAc;EAC9B;EACAnC,WAAWA,CAACoC,iBAAiB,EAAEC,gBAAgB,EAAE;IAC7C,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAACpE,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACqE,gBAAgB,GAAGT,SAAS;IACjC;IACA,IAAI,CAACvF,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAAC6F,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACI,aAAa,GAAG,IAAInK,OAAO,CAAC,CAAC;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACoH,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB;AACR;AACA;IACQ,IAAI,CAAC+C,QAAQ,GAAG,KAAK;EACzB;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,cAAc,CAAC,WAAW,CAAC,IAAID,OAAO,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;MAC3E,IAAI,CAACJ,aAAa,CAACK,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,aAAa,CAACO,QAAQ,CAAC,CAAC;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACZ,cAAc,GAAG,IAAIjK,cAAc,CAAC,IAAI,CAACoK,gBAAgB,IAAI,IAAI,CAACU,gBAAgB,EAAE,IAAI,CAACZ,iBAAiB,CAAC;EACpH;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,sBAAsBA,CAAC5G,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACmG,WAAW,KAAK,IAAI,EAAE;MACrC,IAAI,CAACQ,cAAc,GAAG3G,KAAK;IAC/B;EACJ;EACA;IAAS,IAAI,CAAC4E,IAAI,YAAA+C,eAAA7C,CAAA;MAAA,YAAAA,CAAA,IAAwF4B,MAAM,EAvHhB1L,EAAE,CAAA+J,iBAAA,CAuHgC/J,EAAE,CAACqL,gBAAgB,GAvHrDrL,EAAE,CAAA+J,iBAAA,CAuHgE0B,aAAa;IAAA,CAA4D;EAAE;EAC7O;IAAS,IAAI,CAACmB,IAAI,kBAxH8E5M,EAAE,CAAA6M,iBAAA;MAAA3C,IAAA,EAwHJwB,MAAM;MAAAvB,SAAA;MAAA2C,cAAA,WAAAC,sBAAA5I,EAAA,EAAAC,GAAA,EAAA4I,QAAA;QAAA,IAAA7I,EAAA;UAxHJnE,EAAE,CAAAiN,cAAA,CAAAD,QAAA,EAwHschC,WAAW;UAxHndhL,EAAE,CAAAiN,cAAA,CAAAD,QAAA,EAwHoiBvD,aAAa,KAA2BnJ,WAAW;QAAA;QAAA,IAAA6D,EAAA;UAAA,IAAA+I,EAAA;UAxHzlBlN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAwB,aAAA,GAAAsH,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA4H,gBAAA,GAAAkB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAC,aAAApJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAwN,WAAA,CAwHorBlN,WAAW;QAAA;QAAA,IAAA6D,EAAA;UAAA,IAAA+I,EAAA;UAxHjsBlN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAsI,gBAAA,GAAAQ,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA,aAwHmU,EAAE;MAAAC,MAAA;QAAA/F,QAAA,GAxHvU3H,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,0BAwH0FvN,gBAAgB;QAAA2F,SAAA,GAxH5GhG,EAAE,CAAA2N,YAAA,CAAAE,IAAA;QAAA3F,SAAA,GAAFlI,EAAE,CAAA2N,YAAA,CAAAE,IAAA;QAAA1F,cAAA,GAAFnI,EAAE,CAAA2N,YAAA,CAAAE,IAAA;QAAAtG,UAAA;QAAAyB,SAAA;MAAA;MAAA8E,QAAA;MAAA1D,UAAA;MAAAC,QAAA,GAAFrK,EAAE,CAAAsK,kBAAA,CAwHsV,CAAC;QAAEC,OAAO,EAAEQ,OAAO;QAAEP,WAAW,EAAEkB;MAAO,CAAC,CAAC,GAxHnY1L,EAAE,CAAA+N,wBAAA,EAAF/N,EAAE,CAAAgO,oBAAA,EAAFhO,EAAE,CAAAiO,mBAAA;MAAAC,kBAAA,EAAAjK,GAAA;MAAAkK,KAAA;MAAAC,IAAA;MAAAzE,QAAA,WAAA0E,gBAAAlK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAuF,UAAA,IAAArB,6BAAA,qBAwH4gC,CAAC;QAAA;MAAA;MAAAqK,aAAA;IAAA,EAA4I;EAAE;AACjwC;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA1HoGzK,EAAE,CAAA0K,iBAAA,CA0HXgB,MAAM,EAAc,CAAC;IACpGxB,IAAI,EAAE3J,SAAS;IACfoK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAE4D,eAAe,EAAEhO,uBAAuB,CAACiO,OAAO;MAAEF,aAAa,EAAE9N,iBAAiB,CAACoN,IAAI;MAAEC,QAAQ,EAAE,QAAQ;MAAEjD,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEQ,OAAO;QAAEP,WAAW,EAAEkB;MAAO,CAAC,CAAC;MAAEtB,UAAU,EAAE,IAAI;MAAEsE,IAAI,EAAE;QAC/M;QACA;QACA,QAAQ,EAAE;MACd,CAAC;MAAE/E,QAAQ,EAAE;IAAgR,CAAC;EAC1S,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEO,IAAI,EAAElK,EAAE,CAACqL;EAAiB,CAAC,EAAE;IAAEnB,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9EtB,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAACc,aAAa;IACxB,CAAC,EAAE;MACCvB,IAAI,EAAE9J;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEuH,QAAQ,EAAE,CAAC;MACpCuC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuF,aAAa,EAAE,CAAC;MAChBsE,IAAI,EAAEvJ,YAAY;MAClBgK,IAAI,EAAE,CAACK,WAAW;IACtB,CAAC,CAAC;IAAEgB,gBAAgB,EAAE,CAAC;MACnB9B,IAAI,EAAEvJ,YAAY;MAClBgK,IAAI,EAAE,CAAClB,aAAa,EAAE;QAAEmF,IAAI,EAAEtO,WAAW;QAAEuO,MAAM,EAAE;MAAK,CAAC;IAC7D,CAAC,CAAC;IAAEnC,gBAAgB,EAAE,CAAC;MACnBxC,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAACrK,WAAW,EAAE;QAAEuO,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAE7I,SAAS,EAAE,CAAC;MACZkE,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEzC,SAAS,EAAE,CAAC;MACZgC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAExC,cAAc,EAAE,CAAC;MACjB+B,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEpD,UAAU,EAAE,CAAC;MACb2C,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEsI,SAAS,EAAE,CAAC;MACZkB,IAAI,EAAExJ;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMoO,YAAY,GAAG,2BAA2B;AAChD;AACA,MAAMC,mBAAmB,GAAG,kCAAkC;AAC9D;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZtF,WAAWA,CAACuF,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,MAAM,CAACE,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;EACxD;EACA;EACAC,cAAcA,CAACC,OAAO,EAAE;IACpB,MAAMC,iBAAiB,GAAG,IAAI,CAACP,MAAM,CAACQ,IAAI,CAACL,IAAI,IAAIA,IAAI,CAACM,UAAU,CAACC,aAAa,KAAKJ,OAAO,CAAC;IAC7F,MAAMK,WAAW,GAAG,IAAI,CAACC,YAAY;IACrC,IAAIL,iBAAiB,KAAKI,WAAW,EAAE;MACnC;IACJ;IACAA,WAAW,EAAEP,gBAAgB,CAAC,CAAC;IAC/B,IAAIG,iBAAiB,EAAE;MACnB,MAAMM,OAAO,GAAGF,WAAW,EAAEF,UAAU,CAACC,aAAa,CAACI,qBAAqB,GAAG,CAAC;MAC/E;MACAP,iBAAiB,CAACQ,cAAc,CAACF,OAAO,CAAC;MACzC,IAAI,CAACD,YAAY,GAAGL,iBAAiB;IACzC;EACJ;AACJ;AACA,MAAMS,UAAU,CAAC;EACbvG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwG,WAAW,GAAGrP,MAAM,CAACC,UAAU,CAAC;IACrC,IAAI,CAACqP,aAAa,GAAG,KAAK;EAC9B;EACA;EACA,IAAIvI,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACuI,aAAa;EAC7B;EACA,IAAIvI,kBAAkBA,CAACwI,QAAQ,EAAE;IAC7B,IAAI,IAAI,CAACD,aAAa,KAAKC,QAAQ,EAAE;MACjC,IAAI,CAACD,aAAa,GAAGC,QAAQ;MAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ;EACJ;EACA;EACAN,cAAcA,CAACO,2BAA2B,EAAE;IACxC,MAAMhB,OAAO,GAAG,IAAI,CAACW,WAAW,CAACP,aAAa;IAC9C;IACA;IACA,IAAI,CAACY,2BAA2B,IAC5B,CAAChB,OAAO,CAACQ,qBAAqB,IAC9B,CAAC,IAAI,CAACS,qBAAqB,EAAE;MAC7BjB,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;MACnC;IACJ;IACA;IACA;IACA;IACA,MAAM6B,iBAAiB,GAAGpB,OAAO,CAACQ,qBAAqB,CAAC,CAAC;IACzD,MAAMa,UAAU,GAAGL,2BAA2B,CAACM,KAAK,GAAGF,iBAAiB,CAACE,KAAK;IAC9E,MAAMC,SAAS,GAAGP,2BAA2B,CAACQ,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;IAC3ExB,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC3B,mBAAmB,CAAC;IAC1C,IAAI,CAACyB,qBAAqB,CAAC1M,KAAK,CAACkN,WAAW,CAAC,WAAW,EAAE,cAAcF,SAAS,cAAcF,UAAU,GAAG,CAAC;IAC7G;IACArB,OAAO,CAACQ,qBAAqB,CAAC,CAAC;IAC/BR,OAAO,CAACkB,SAAS,CAACQ,MAAM,CAAClC,mBAAmB,CAAC;IAC7CQ,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;IACnC,IAAI,CAAC0B,qBAAqB,CAAC1M,KAAK,CAACkN,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;EACjE;EACA;EACA3B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACa,WAAW,CAACP,aAAa,CAACc,SAAS,CAACQ,MAAM,CAACnC,YAAY,CAAC;EACjE;EACA;EACArC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyE,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA3E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8D,cAAc,EAAEY,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACZ,cAAc,GAAG,IAAI,CAACG,qBAAqB,GAAG,IAAI;EAC3D;EACA;EACAU,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,YAAY,GAAG,IAAI,CAACjB,WAAW,CAACP,aAAa,CAACyB,aAAa,IAAIC,QAAQ;IAC7E,MAAMC,aAAa,GAAI,IAAI,CAACjB,cAAc,GAAGc,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAChF,MAAMC,oBAAoB,GAAI,IAAI,CAAChB,qBAAqB,GAAGW,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAC9FD,aAAa,CAACG,SAAS,GAAG,mBAAmB;IAC7CD,oBAAoB,CAACC,SAAS,GAC1B,kEAAkE;IACtEH,aAAa,CAACI,WAAW,CAAC,IAAI,CAAClB,qBAAqB,CAAC;IACrD,IAAI,CAACF,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACD,cAAc,KAAK,OAAO5F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAMkH,KAAK,CAAC,6DAA6D,CAAC;IAC9E;IACA,MAAMC,aAAa,GAAG,IAAI,CAACzB,aAAa,GAClC,IAAI,CAACD,WAAW,CAACP,aAAa,CAACkC,aAAa,CAAC,mBAAmB,CAAC,GACjE,IAAI,CAAC3B,WAAW,CAACP,aAAa;IACpC,IAAI,CAACiC,aAAa,KAAK,OAAOnH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAMkH,KAAK,CAAC,qCAAqC,CAAC;IACtD;IACAC,aAAa,CAACF,WAAW,CAAC,IAAI,CAACrB,cAAc,CAAC;EAClD;EACA;IAAS,IAAI,CAACzG,IAAI,YAAAkI,mBAAAhI,CAAA;MAAA,YAAAA,CAAA,IAAwFmG,UAAU;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACjG,IAAI,kBApR8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EAoRJ+F,UAAU;MAAAvC,MAAA;QAAA9F,kBAAA,GApRR5H,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,8CAoRmFvN,gBAAgB;MAAA;MAAAgK,QAAA,GApRrGrK,EAAE,CAAA+N,wBAAA;IAAA,EAoRuH;EAAE;AAC/N;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAtRoGzK,EAAE,CAAA0K,iBAAA,CAsRXuF,UAAU,EAAc,CAAC;IACxG/F,IAAI,EAAEhK;EACV,CAAC,CAAC,QAAkB;IAAE0H,kBAAkB,EAAE,CAAC;MACnCsC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAAS0R,+BAA+BA,CAAA,EAAG;EACvC,MAAMC,MAAM,GAAIzC,OAAO,KAAM;IACzBwB,IAAI,EAAExB,OAAO,GAAG,CAACA,OAAO,CAAC0C,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;IACtDpB,KAAK,EAAEtB,OAAO,GAAG,CAACA,OAAO,CAAC2C,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG;EACzD,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB;AACA;AACA,MAAMG,uBAAuB,GAAG,IAAIlS,cAAc,CAAC,qBAAqB,EAAE;EACtEmS,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEN;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,SAASrC,UAAU,CAAC;EACxCvG,WAAWA,CAACgG,UAAU,EAAE;IACpB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B;IACA,IAAI,CAAC/H,QAAQ,GAAG,KAAK;EACzB;EACA;EACA4K,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7C,UAAU,CAACC,aAAa,CAAC4C,KAAK,CAAC,CAAC;EACzC;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9C,UAAU,CAACC,aAAa,CAACsC,UAAU;EACnD;EACAQ,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/C,UAAU,CAACC,aAAa,CAACuC,WAAW;EACpD;EACA;IAAS,IAAI,CAACtI,IAAI,YAAA8I,2BAAA5I,CAAA;MAAA,YAAAA,CAAA,IAAwFwI,kBAAkB,EAlU5BtS,EAAE,CAAA+J,iBAAA,CAkU4C/J,EAAE,CAACc,UAAU;IAAA,CAA4C;EAAE;EACzM;IAAS,IAAI,CAACkJ,IAAI,kBAnU8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EAmUJoI,kBAAkB;MAAAnI,SAAA;MAAAwI,QAAA;MAAAC,YAAA,WAAAC,gCAAA1O,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnUhBnE,EAAE,CAAA6H,WAAA,oBAAAzD,GAAA,CAAAuD,QAAA;UAAF3H,EAAE,CAAAwH,WAAA,yBAAApD,GAAA,CAAAuD,QAmUa,CAAC;QAAA;MAAA;MAAA+F,MAAA;QAAA/F,QAAA,GAnUhB3H,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,0BAmUmHvN,gBAAgB;MAAA;MAAA+J,UAAA;MAAAC,QAAA,GAnUrIrK,EAAE,CAAA+N,wBAAA,EAAF/N,EAAE,CAAAsL,0BAAA;IAAA,EAmUsR;EAAE;AAC9X;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KArUoGzK,EAAE,CAAA0K,iBAAA,CAqUX4H,kBAAkB,EAAc,CAAC;IAChHpI,IAAI,EAAEhK,SAAS;IACfyK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChC8D,IAAI,EAAE;QACF,8BAA8B,EAAE,UAAU;QAC1C,sBAAsB,EAAE;MAC5B,CAAC;MACDtE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAElK,EAAE,CAACc;EAAW,CAAC,CAAC,EAAkB;IAAE6G,QAAQ,EAAE,CAAC;MAC1EuC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMyS,2BAA2B,GAAGpQ,+BAA+B,CAAC;EAChEqQ,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB;EACA,IAAIzL,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC0L,cAAc;EAC9B;EACA,IAAI1L,aAAaA,CAAC2L,CAAC,EAAE;IACjB,MAAMpO,KAAK,GAAGqO,KAAK,CAACD,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC;IAC9B,IAAI,IAAI,CAACD,cAAc,IAAInO,KAAK,EAAE;MAC9B,IAAI,CAACsO,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACH,cAAc,GAAGnO,KAAK;MAC3B,IAAI,IAAI,CAACuO,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACC,gBAAgB,CAACxO,KAAK,CAAC;MAC5C;IACJ;EACJ;EACA0E,WAAWA,CAACwG,WAAW,EAAEuD,kBAAkB,EAAEC,cAAc,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,cAAc,EAAE;IACnG,IAAI,CAAC5D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACuD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACT,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAACU,UAAU,GAAG,IAAIlS,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACmS,uBAAuB,GAAG,KAAK;IACpC;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,cAAc,GAAG,IAAItS,OAAO,CAAC,CAAC;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAACuS,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAClB,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACmB,kBAAkB,GAAG,IAAIvT,YAAY,CAAC,CAAC;IAC5C;IACA,IAAI,CAACwT,YAAY,GAAG,IAAIxT,YAAY,CAAC,CAAC;IACtC;IACA6S,OAAO,CAACY,iBAAiB,CAAC,MAAM;MAC5BzS,SAAS,CAACmO,WAAW,CAACP,aAAa,EAAE,YAAY,CAAC,CAC7C8E,IAAI,CAACtR,SAAS,CAAC,IAAI,CAAC6Q,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;QACjB,IAAI,CAACC,aAAa,CAAC,CAAC;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd;IACA7S,SAAS,CAAC,IAAI,CAAC8S,kBAAkB,CAAClF,aAAa,EAAE,YAAY,EAAEmD,2BAA2B,CAAC,CACtF2B,IAAI,CAACtR,SAAS,CAAC,IAAI,CAAC6Q,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;MACjB,IAAI,CAACI,qBAAqB,CAAC,QAAQ,CAAC;IACxC,CAAC,CAAC;IACF/S,SAAS,CAAC,IAAI,CAACgT,cAAc,CAACpF,aAAa,EAAE,YAAY,EAAEmD,2BAA2B,CAAC,CAClF2B,IAAI,CAACtR,SAAS,CAAC,IAAI,CAAC6Q,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;MACjB,IAAI,CAACI,qBAAqB,CAAC,OAAO,CAAC;IACvC,CAAC,CAAC;EACN;EACAE,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAACtB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACuB,MAAM,GAAGlT,EAAE,CAAC,KAAK,CAAC;IAC1D,MAAMmT,MAAM,GAAG,IAAI,CAACzB,cAAc,CAACwB,MAAM,CAAC,GAAG,CAAC;IAC9C,MAAME,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,CAAC/B,WAAW,GAAG,IAAIzQ,eAAe,CAAC,IAAI,CAACmM,MAAM,CAAC,CAC9CsG,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CACrDC,cAAc,CAAC,CAAC,CAChBC,QAAQ,CAAC;IACV;IAAA,CACCC,aAAa,CAAC,MAAM,KAAK,CAAC;IAC/B,IAAI,CAACpC,WAAW,CAACC,gBAAgB,CAAC,IAAI,CAACL,cAAc,CAAC;IACtD;IACA;IACA;IACA;IACA,IAAI,CAACS,OAAO,CAACgC,QAAQ,CAACnB,IAAI,CAACrR,IAAI,CAAC,CAAC,CAAC,CAAC,CAACsR,SAAS,CAACU,OAAO,CAAC;IACtD;IACA;IACAnT,KAAK,CAACgT,SAAS,EAAEE,MAAM,EAAE,IAAI,CAAClG,MAAM,CAAC7C,OAAO,EAAE,IAAI,CAACyJ,aAAa,CAAC,CAAC,CAAC,CAC9DpB,IAAI,CAACtR,SAAS,CAAC,IAAI,CAAC6Q,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACd,OAAO,CAACkC,GAAG,CAAC,MAAM;QACnBC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB;UACA,IAAI,CAAClC,eAAe,GAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACtC,eAAe,CAAC,CAAC;UAChGqB,OAAO,CAAC,CAAC;QACb,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAAC7B,WAAW,CAACgC,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACjC,WAAW,CAAC2B,MAAM,CAACR,SAAS,CAAC4B,aAAa,IAAI;MAC/C,IAAI,CAAC/B,YAAY,CAACgC,IAAI,CAACD,aAAa,CAAC;MACrC,IAAI,CAACE,YAAY,CAACF,aAAa,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACAT,aAAaA,CAAA,EAAG;IACZ,IAAI,OAAOY,cAAc,KAAK,UAAU,EAAE;MACtC,OAAOvU,KAAK;IAChB;IACA,OAAO,IAAI,CAAC+M,MAAM,CAAC7C,OAAO,CAACqI,IAAI,CAACpR,SAAS,CAAC,IAAI,CAAC4L,MAAM,CAAC,EAAE3L,SAAS,CAAEoT,QAAQ,IAAK,IAAIvU,UAAU,CAAEwU,QAAQ,IAAK,IAAI,CAAC/C,OAAO,CAACY,iBAAiB,CAAC,MAAM;MAC9I,MAAMoC,cAAc,GAAG,IAAIH,cAAc,CAACI,OAAO,IAAIF,QAAQ,CAACrK,IAAI,CAACuK,OAAO,CAAC,CAAC;MAC5EH,QAAQ,CAACvH,OAAO,CAACC,IAAI,IAAIwH,cAAc,CAACE,OAAO,CAAC1H,IAAI,CAACM,UAAU,CAACC,aAAa,CAAC,CAAC;MAC/E,OAAO,MAAM;QACTiH,cAAc,CAACG,UAAU,CAAC,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;IACJ;IACA;IACAxT,IAAI,CAAC,CAAC,CAAC;IACP;IACA;IACAC,MAAM,CAACqT,OAAO,IAAIA,OAAO,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAACrG,KAAK,GAAG,CAAC,IAAIoG,CAAC,CAACC,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9F;EACAC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,cAAc,IAAI,IAAI,CAACpI,MAAM,CAACjH,MAAM,EAAE;MAC3C,IAAI,CAACqN,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACgC,cAAc,GAAG,IAAI,CAACpI,MAAM,CAACjH,MAAM;MACxC,IAAI,CAACyL,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAAChE,qBAAqB,EAAE;MAC5B,IAAI,CAACiE,cAAc,CAAC,IAAI,CAACpE,cAAc,CAAC;MACxC,IAAI,CAACqE,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAAClC,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAAChC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACG,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAC7B,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACD,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAAChE,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA/K,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgH,WAAW,EAAEoE,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC3D,UAAU,CAAC1H,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC0H,UAAU,CAACxH,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC4H,cAAc,CAAC5H,QAAQ,CAAC,CAAC;EAClC;EACA;EACAoL,cAAcA,CAACC,KAAK,EAAE;IAClB;IACA,IAAI7U,cAAc,CAAC6U,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,QAAQA,KAAK,CAACC,OAAO;MACjB,KAAK5U,KAAK;MACV,KAAKD,KAAK;QACN,IAAI,IAAI,CAAC8U,UAAU,KAAK,IAAI,CAACtQ,aAAa,EAAE;UACxC,MAAM2H,IAAI,GAAG,IAAI,CAACH,MAAM,CAAC+I,GAAG,CAAC,IAAI,CAACD,UAAU,CAAC;UAC7C,IAAI3I,IAAI,IAAI,CAACA,IAAI,CAACzH,QAAQ,EAAE;YACxB,IAAI,CAAC2M,kBAAkB,CAACiC,IAAI,CAAC,IAAI,CAACwB,UAAU,CAAC;YAC7C,IAAI,CAACE,aAAa,CAACJ,KAAK,CAAC;UAC7B;QACJ;QACA;MACJ;QACI,IAAI,CAACtE,WAAW,CAAC2E,SAAS,CAACL,KAAK,CAAC;IACzC;EACJ;EACA;AACJ;AACA;EACIM,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,WAAW,GAAG,IAAI,CAAClI,WAAW,CAACP,aAAa,CAACyI,WAAW;IAC9D;IACA;IACA;IACA,IAAIA,WAAW,KAAK,IAAI,CAACC,mBAAmB,EAAE;MAC1C,IAAI,CAACA,mBAAmB,GAAGD,WAAW,IAAI,EAAE;MAC5C;MACA;MACA,IAAI,CAACxE,OAAO,CAACkC,GAAG,CAAC,MAAM;QACnB,IAAI,CAACT,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;QAChC,IAAI,CAAC7B,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACiD,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACd,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACE,wBAAwB,CAAC,CAAC;EACnC;EACA;EACA,IAAIK,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACgF,eAAe,GAAG,CAAC;EAClE;EACA;EACA,IAAIR,UAAUA,CAAC/S,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACwT,aAAa,CAACxT,KAAK,CAAC,IAAI,IAAI,CAAC+S,UAAU,KAAK/S,KAAK,IAAI,CAAC,IAAI,CAACuO,WAAW,EAAE;MAC9E;IACJ;IACA,IAAI,CAACA,WAAW,CAACkF,aAAa,CAACzT,KAAK,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACIwT,aAAaA,CAACE,KAAK,EAAE;IACjB,OAAO,IAAI,CAACzJ,MAAM,GAAG,CAAC,CAAC,IAAI,CAACA,MAAM,CAAC0J,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIlC,YAAYA,CAACoC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAAC3E,uBAAuB,EAAE;MAC9B,IAAI,CAACsD,cAAc,CAACqB,QAAQ,CAAC;IACjC;IACA,IAAI,IAAI,CAAC3J,MAAM,IAAI,IAAI,CAACA,MAAM,CAACjH,MAAM,EAAE;MACnC,IAAI,CAACiH,MAAM,CAAC0J,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACrG,KAAK,CAAC,CAAC;MACvC;MACA;MACA;MACA,MAAMsG,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACnJ,aAAa;MACxD,MAAMoJ,GAAG,GAAG,IAAI,CAACvD,mBAAmB,CAAC,CAAC;MACtC,IAAIuD,GAAG,IAAI,KAAK,EAAE;QACdF,WAAW,CAACG,UAAU,GAAG,CAAC;MAC9B,CAAC,MACI;QACDH,WAAW,CAACG,UAAU,GAAGH,WAAW,CAACI,WAAW,GAAGJ,WAAW,CAAC3G,WAAW;MAC9E;IACJ;EACJ;EACA;EACAsD,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7B,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC3O,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACA0S,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACrD,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAM6E,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,MAAMC,UAAU,GAAG,IAAI,CAAC3D,mBAAmB,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC0D,cAAc,GAAGA,cAAc;IAC1F;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,QAAQ,CAACzJ,aAAa,CAAC7L,KAAK,CAAC6K,SAAS,GAAG,cAAcuH,IAAI,CAACmD,KAAK,CAACF,UAAU,CAAC,KAAK;IACvF;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACtF,SAAS,CAACyF,OAAO,IAAI,IAAI,CAACzF,SAAS,CAAC0F,IAAI,EAAE;MAC/C,IAAI,CAACT,iBAAiB,CAACnJ,aAAa,CAACqJ,UAAU,GAAG,CAAC;IACvD;EACJ;EACA;EACA,IAAIE,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACnF,eAAe;EAC/B;EACA,IAAImF,cAAcA,CAAClU,KAAK,EAAE;IACtB,IAAI,CAACwU,SAAS,CAACxU,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIyU,aAAaA,CAACC,SAAS,EAAE;IACrB,MAAMC,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAACnJ,aAAa,CAACuC,WAAW;IACnE;IACA,MAAM0H,YAAY,GAAI,CAACF,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIC,UAAU,GAAI,CAAC;IACxE,OAAO,IAAI,CAACH,SAAS,CAAC,IAAI,CAACzF,eAAe,GAAG6F,YAAY,CAAC;EAC9D;EACA;EACAC,qBAAqBA,CAACH,SAAS,EAAE;IAC7B,IAAI,CAAC/E,aAAa,CAAC,CAAC;IACpB,IAAI,CAAC8E,aAAa,CAACC,SAAS,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACInC,cAAcA,CAACuC,UAAU,EAAE;IACvB,IAAI,IAAI,CAACzF,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAM0F,aAAa,GAAG,IAAI,CAAC9K,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC0J,OAAO,CAAC,CAAC,CAACmB,UAAU,CAAC,GAAG,IAAI;IAC5E,IAAI,CAACC,aAAa,EAAE;MAChB;IACJ;IACA;IACA,MAAMJ,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAACnJ,aAAa,CAACuC,WAAW;IACnE,MAAM;MAAED,UAAU;MAAEC;IAAY,CAAC,GAAG6H,aAAa,CAACrK,UAAU,CAACC,aAAa;IAC1E,IAAIqK,cAAc,EAAEC,aAAa;IACjC,IAAI,IAAI,CAACzE,mBAAmB,CAAC,CAAC,IAAI,KAAK,EAAE;MACrCwE,cAAc,GAAG/H,UAAU;MAC3BgI,aAAa,GAAGD,cAAc,GAAG9H,WAAW;IAChD,CAAC,MACI;MACD+H,aAAa,GAAG,IAAI,CAACC,aAAa,CAACvK,aAAa,CAACuC,WAAW,GAAGD,UAAU;MACzE+H,cAAc,GAAGC,aAAa,GAAG/H,WAAW;IAChD;IACA,MAAMiI,gBAAgB,GAAG,IAAI,CAACjB,cAAc;IAC5C,MAAMkB,eAAe,GAAG,IAAI,CAAClB,cAAc,GAAGS,UAAU;IACxD,IAAIK,cAAc,GAAGG,gBAAgB,EAAE;MACnC;MACA,IAAI,CAACjB,cAAc,IAAIiB,gBAAgB,GAAGH,cAAc;IAC5D,CAAC,MACI,IAAIC,aAAa,GAAGG,eAAe,EAAE;MACtC;MACA,IAAI,CAAClB,cAAc,IAAIhD,IAAI,CAACE,GAAG,CAAC6D,aAAa,GAAGG,eAAe,EAAEJ,cAAc,GAAGG,gBAAgB,CAAC;IACvG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI7B,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACjE,iBAAiB,EAAE;MACxB,IAAI,CAACJ,uBAAuB,GAAG,KAAK;IACxC,CAAC,MACI;MACD,MAAMoG,SAAS,GAAG,IAAI,CAACH,aAAa,CAACvK,aAAa,CAACsJ,WAAW,GAAG,IAAI,CAAC/I,WAAW,CAACP,aAAa,CAACuC,WAAW;MAC3G,IAAI,CAACmI,SAAS,EAAE;QACZ,IAAI,CAACnB,cAAc,GAAG,CAAC;MAC3B;MACA,IAAImB,SAAS,KAAK,IAAI,CAACpG,uBAAuB,EAAE;QAC5C,IAAI,CAACR,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;MAC1C;MACA,IAAI,CAACrD,uBAAuB,GAAGoG,SAAS;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI7C,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACnD,iBAAiB,EAAE;MACxB,IAAI,CAACH,mBAAmB,GAAG,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAC/D,CAAC,MACI;MACD;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAAC+E,cAAc,IAAI,CAAC;MACpD,IAAI,CAAChF,mBAAmB,GAAG,IAAI,CAACgF,cAAc,IAAI,IAAI,CAAC7C,qBAAqB,CAAC,CAAC;MAC9E,IAAI,CAAC5C,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjB,qBAAqBA,CAAA,EAAG;IACpB,MAAMiE,eAAe,GAAG,IAAI,CAACJ,aAAa,CAACvK,aAAa,CAACsJ,WAAW;IACpE,MAAMU,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAACnJ,aAAa,CAACuC,WAAW;IACnE,OAAOoI,eAAe,GAAGX,UAAU,IAAI,CAAC;EAC5C;EACA;EACArE,yBAAyBA,CAAA,EAAG;IACxB,MAAMiF,YAAY,GAAG,IAAI,CAACtL,MAAM,IAAI,IAAI,CAACA,MAAM,CAACjH,MAAM,GAAG,IAAI,CAACiH,MAAM,CAAC0J,OAAO,CAAC,CAAC,CAAC,IAAI,CAAClR,aAAa,CAAC,GAAG,IAAI;IACzG,MAAM+S,oBAAoB,GAAGD,YAAY,GAAGA,YAAY,CAAC7K,UAAU,CAACC,aAAa,GAAG,IAAI;IACxF,IAAI6K,oBAAoB,EAAE;MACtB,IAAI,CAACC,OAAO,CAACnL,cAAc,CAACkL,oBAAoB,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACC,OAAO,CAACvL,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACAyF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACP,cAAc,CAAC9H,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIwI,qBAAqBA,CAAC4E,SAAS,EAAEgB,UAAU,EAAE;IACzC;IACA;IACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,IAAI,IAAI,IAAID,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACpE;IACJ;IACA;IACA,IAAI,CAAChG,aAAa,CAAC,CAAC;IACpB;IACAvS,KAAK,CAAC4Q,mBAAmB,EAAEC,sBAAsB;IAC7C;IAAA,CACCwB,IAAI,CAACtR,SAAS,CAAClB,KAAK,CAAC,IAAI,CAACmS,cAAc,EAAE,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC,CAC5DU,SAAS,CAAC,MAAM;MACjB,MAAM;QAAEkG,iBAAiB;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACpB,aAAa,CAACC,SAAS,CAAC;MACrE;MACA,IAAImB,QAAQ,KAAK,CAAC,IAAIA,QAAQ,IAAID,iBAAiB,EAAE;QACjD,IAAI,CAACjG,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI6E,SAASA,CAACtQ,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACmL,iBAAiB,EAAE;MACxB,OAAO;QAAEuG,iBAAiB,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;IAChD;IACA,MAAMD,iBAAiB,GAAG,IAAI,CAACvE,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACtC,eAAe,GAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACwE,iBAAiB,EAAE1R,QAAQ,CAAC,CAAC;IACzE;IACA;IACA,IAAI,CAACuO,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACD,uBAAuB,CAAC,CAAC;IAC9B,OAAO;MAAEoD,iBAAiB;MAAEC,QAAQ,EAAE,IAAI,CAAC9G;IAAgB,CAAC;EAChE;EACA;IAAS,IAAI,CAACnK,IAAI,YAAAkR,8BAAAhR,CAAA;MAAA,YAAAA,CAAA,IAAwFoJ,qBAAqB,EA9yB/BlT,EAAE,CAAA+J,iBAAA,CA8yB+C/J,EAAE,CAACc,UAAU,GA9yB9Dd,EAAE,CAAA+J,iBAAA,CA8yByE/J,EAAE,CAAC+a,iBAAiB,GA9yB/F/a,EAAE,CAAA+J,iBAAA,CA8yB0GxH,EAAE,CAACyY,aAAa,GA9yB5Hhb,EAAE,CAAA+J,iBAAA,CA8yBuInH,EAAE,CAACqY,cAAc,MA9yB1Jjb,EAAE,CAAA+J,iBAAA,CA8yBqL/J,EAAE,CAACkb,MAAM,GA9yBhMlb,EAAE,CAAA+J,iBAAA,CA8yB2MtH,EAAE,CAACE,QAAQ,GA9yBxN3C,EAAE,CAAA+J,iBAAA,CA8yBmO/I,qBAAqB;IAAA,CAA4D;EAAE;EACxZ;IAAS,IAAI,CAACgJ,IAAI,kBA/yB8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EA+yBJgJ,qBAAqB;MAAAxF,MAAA;QAAA2G,iBAAA,GA/yBnBrU,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,4CA+yB2FvN,gBAAgB;QAAAoH,aAAA,GA/yB7GzH,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,oCA+yBgK3M,eAAe;MAAA;MAAAka,OAAA;QAAA7G,kBAAA;QAAAC,YAAA;MAAA;MAAAlK,QAAA,GA/yBjLrK,EAAE,CAAA+N,wBAAA;IAAA,EA+yBwR;EAAE;AAChY;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAjzBoGzK,EAAE,CAAA0K,iBAAA,CAizBXwI,qBAAqB,EAAc,CAAC;IACnHhJ,IAAI,EAAEhK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEgK,IAAI,EAAElK,EAAE,CAACc;EAAW,CAAC,EAAE;IAAEoJ,IAAI,EAAElK,EAAE,CAAC+a;EAAkB,CAAC,EAAE;IAAE7Q,IAAI,EAAE3H,EAAE,CAACyY;EAAc,CAAC,EAAE;IAAE9Q,IAAI,EAAEtH,EAAE,CAACqY,cAAc;IAAEzP,UAAU,EAAE,CAAC;MAC5ItB,IAAI,EAAE9J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAElK,EAAE,CAACkb;EAAO,CAAC,EAAE;IAAEhR,IAAI,EAAEzH,EAAE,CAACE;EAAS,CAAC,EAAE;IAAEuH,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9EtB,IAAI,EAAE9J;IACV,CAAC,EAAE;MACC8J,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAAC3J,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEqT,iBAAiB,EAAE,CAAC;MAC7CnK,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoH,aAAa,EAAE,CAAC;MAChByC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAE1N;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEqT,kBAAkB,EAAE,CAAC;MACrBpK,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAEqT,YAAY,EAAE,CAAC;MACfrK,IAAI,EAAEhJ;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMka,YAAY,SAASlI,qBAAqB,CAAC;EAC7CxJ,WAAWA,CAACgG,UAAU,EAAE2L,iBAAiB,EAAEC,aAAa,EAAEvC,GAAG,EAAEwC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAC/L,UAAU,EAAE2L,iBAAiB,EAAEC,aAAa,EAAEvC,GAAG,EAAEwC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF;IACA,IAAI,CAACpT,aAAa,GAAG,KAAK;EAC9B;EACA2M,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACyF,OAAO,GAAG,IAAIzL,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAAC+F,kBAAkB,CAAC,CAAC;EAC9B;EACAiD,aAAaA,CAACJ,KAAK,EAAE;IACjBA,KAAK,CAAC6D,cAAc,CAAC,CAAC;EAC1B;EACA;IAAS,IAAI,CAAC9R,IAAI,YAAA+R,qBAAA7R,CAAA;MAAA,YAAAA,CAAA,IAAwFsR,YAAY,EA11BtBpb,EAAE,CAAA+J,iBAAA,CA01BsC/J,EAAE,CAACc,UAAU,GA11BrDd,EAAE,CAAA+J,iBAAA,CA01BgE/J,EAAE,CAAC+a,iBAAiB,GA11BtF/a,EAAE,CAAA+J,iBAAA,CA01BiGxH,EAAE,CAACyY,aAAa,GA11BnHhb,EAAE,CAAA+J,iBAAA,CA01B8HnH,EAAE,CAACqY,cAAc,MA11BjJjb,EAAE,CAAA+J,iBAAA,CA01B4K/J,EAAE,CAACkb,MAAM,GA11BvLlb,EAAE,CAAA+J,iBAAA,CA01BkMtH,EAAE,CAACE,QAAQ,GA11B/M3C,EAAE,CAAA+J,iBAAA,CA01B0N/I,qBAAqB;IAAA,CAA4D;EAAE;EAC/Y;IAAS,IAAI,CAAC4L,IAAI,kBA31B8E5M,EAAE,CAAA6M,iBAAA;MAAA3C,IAAA,EA21BJkR,YAAY;MAAAjR,SAAA;MAAA2C,cAAA,WAAA8O,4BAAAzX,EAAA,EAAAC,GAAA,EAAA4I,QAAA;QAAA,IAAA7I,EAAA;UA31BVnE,EAAE,CAAAiN,cAAA,CAAAD,QAAA,EA21B+YsF,kBAAkB;QAAA;QAAA,IAAAnO,EAAA;UAAA,IAAA+I,EAAA;UA31BnalN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA6K,MAAA,GAAA/B,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAuO,mBAAA1X,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAwN,WAAA,CAAAlJ,GAAA;UAAFtE,EAAE,CAAAwN,WAAA,CAAAjJ,GAAA;UAAFvE,EAAE,CAAAwN,WAAA,CAAAhJ,GAAA;UAAFxE,EAAE,CAAAwN,WAAA,CAAA/I,GAAA;UAAFzE,EAAE,CAAAwN,WAAA,CAAA9I,GAAA;QAAA;QAAA,IAAAP,EAAA;UAAA,IAAA+I,EAAA;UAAFlN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA0U,iBAAA,GAAA5L,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAgV,QAAA,GAAAlM,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA8V,aAAA,GAAAhN,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA2Q,cAAA,GAAA7H,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAyQ,kBAAA,GAAA3H,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAkF,QAAA;MAAAC,YAAA,WAAAkJ,0BAAA3X,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAwH,WAAA,mDAAApD,GAAA,CAAA6P,uBA21BO,CAAC,2BAAZ7P,GAAA,CAAAoR,mBAAA,CAAoB,CAAC,IAAI,KAAd,CAAC;QAAA;MAAA;MAAA9H,MAAA;QAAArF,aAAA,GA31BVrI,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,oCA21BsHvN,gBAAgB;MAAA;MAAA+J,UAAA;MAAAC,QAAA,GA31BxIrK,EAAE,CAAA+N,wBAAA,EAAF/N,EAAE,CAAAsL,0BAAA,EAAFtL,EAAE,CAAAiO,mBAAA;MAAAC,kBAAA,EAAAjK,GAAA;MAAAkK,KAAA;MAAAC,IAAA;MAAA2N,MAAA;MAAApS,QAAA,WAAAqS,sBAAA7X,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA8X,GAAA,GAAFjc,EAAE,CAAAmG,gBAAA;UAAFnG,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAoG,cAAA,kBA21B+mD,CAAC;UA31BlnDpG,EAAE,CAAAqG,UAAA,mBAAA6V,8CAAA;YAAFlc,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21B2+C1C,GAAA,CAAAyV,qBAAA,CAAsB,QAAQ,CAAC;UAAA,CAAC,CAAC,uBAAAsC,kDAAAlV,MAAA;YA31B9gDjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21BiiD1C,GAAA,CAAA0Q,qBAAA,CAAsB,QAAQ,EAAA7N,MAAQ,CAAC;UAAA,CAAC,CAAC,sBAAAmV,iDAAA;YA31B5kDpc,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21B8lD1C,GAAA,CAAAuQ,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA31BjnD3U,EAAE,CAAAmH,SAAA,YA21B8qD,CAAC;UA31BjrDnH,EAAE,CAAAoH,YAAA,CA21ByrD,CAAC;UA31B5rDpH,EAAE,CAAAoG,cAAA,eA21Bm3D,CAAC;UA31Bt3DpG,EAAE,CAAAqG,UAAA,qBAAAgW,6CAAApV,MAAA;YAAFjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21BgxD1C,GAAA,CAAAwT,cAAA,CAAA3Q,MAAqB,CAAC;UAAA,CAAC,CAAC;UA31B1yDjH,EAAE,CAAAoG,cAAA,eA21Bi/D,CAAC;UA31Bp/DpG,EAAE,CAAAqG,UAAA,+BAAAiW,uDAAA;YAAFtc,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21B49D1C,GAAA,CAAA+T,iBAAA,CAAkB,CAAC;UAAA,CAAC,CAAC;UA31Bn/DnY,EAAE,CAAAoG,cAAA,eA21BuiE,CAAC;UA31B1iEpG,EAAE,CAAAqE,YAAA,EA21BwkE,CAAC;UA31B3kErE,EAAE,CAAAoH,YAAA,CA21BolE,CAAC,CAAS,CAAC,CAAO,CAAC;UA31BzmEpH,EAAE,CAAAoG,cAAA,oBA21ButF,CAAC;UA31B1tFpG,EAAE,CAAAqG,UAAA,uBAAAkW,mDAAAtV,MAAA;YAAFjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21BylF1C,GAAA,CAAA0Q,qBAAA,CAAsB,OAAO,EAAA7N,MAAQ,CAAC;UAAA,CAAC,CAAC,mBAAAuV,+CAAA;YA31BnoFxc,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21BkpF1C,GAAA,CAAAyV,qBAAA,CAAsB,OAAO,CAAC;UAAA,CAAC,CAAC,sBAAA4C,kDAAA;YA31BprFzc,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA21BssF1C,GAAA,CAAAuQ,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA31BztF3U,EAAE,CAAAmH,SAAA,aA21BsxF,CAAC;UA31BzxFnH,EAAE,CAAAoH,YAAA,CA21BiyF,CAAC;QAAA;QAAA,IAAAjD,EAAA;UA31BpyFnE,EAAE,CAAAwH,WAAA,2CAAApD,GAAA,CAAA+P,oBA21Bu6C,CAAC;UA31B16CnU,EAAE,CAAA2F,UAAA,sBAAAvB,GAAA,CAAA+P,oBAAA,IAAA/P,GAAA,CAAAiE,aA21By1C,CAAC,aAAAjE,GAAA,CAAA+P,oBAAA,QAA+H,CAAC;UA31B59CnU,EAAE,CAAAoI,SAAA,EA21Bk3D,CAAC;UA31Br3DpI,EAAE,CAAAwH,WAAA,4BAAApD,GAAA,CAAA0P,cAAA,qBA21Bk3D,CAAC;UA31Br3D9T,EAAE,CAAAoI,SAAA,EA21B4/E,CAAC;UA31B//EpI,EAAE,CAAAwH,WAAA,2CAAApD,GAAA,CAAA8P,mBA21B4/E,CAAC;UA31B//ElU,EAAE,CAAA2F,UAAA,sBAAAvB,GAAA,CAAA8P,mBAAA,IAAA9P,GAAA,CAAAiE,aA21B+6E,CAAC,aAAAjE,GAAA,CAAA8P,mBAAA,QAA6H,CAAC;QAAA;MAAA;MAAAwI,YAAA,GAAsiGlb,SAAS,EAAwPkC,iBAAiB;MAAAiZ,MAAA;MAAApO,aAAA;IAAA,EAA0P;EAAE;AACxsM;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA71BoGzK,EAAE,CAAA0K,iBAAA,CA61BX0Q,YAAY,EAAc,CAAC;IAC1GlR,IAAI,EAAE3J,SAAS;IACfoK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAE2D,aAAa,EAAE9N,iBAAiB,CAACoN,IAAI;MAAEW,eAAe,EAAEhO,uBAAuB,CAACiO,OAAO;MAAEC,IAAI,EAAE;QACxH,OAAO,EAAE,oBAAoB;QAC7B,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE;MACtC,CAAC;MAAEtE,UAAU,EAAE,IAAI;MAAEwS,OAAO,EAAE,CAACpb,SAAS,EAAEkC,iBAAiB,CAAC;MAAEiG,QAAQ,EAAE,6yDAA6yD;MAAEgT,MAAM,EAAE,CAAC,uvFAAuvF;IAAE,CAAC;EACtoJ,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzS,IAAI,EAAElK,EAAE,CAACc;EAAW,CAAC,EAAE;IAAEoJ,IAAI,EAAElK,EAAE,CAAC+a;EAAkB,CAAC,EAAE;IAAE7Q,IAAI,EAAE3H,EAAE,CAACyY;EAAc,CAAC,EAAE;IAAE9Q,IAAI,EAAEtH,EAAE,CAACqY,cAAc;IAAEzP,UAAU,EAAE,CAAC;MAC5ItB,IAAI,EAAE9J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAElK,EAAE,CAACkb;EAAO,CAAC,EAAE;IAAEhR,IAAI,EAAEzH,EAAE,CAACE;EAAS,CAAC,EAAE;IAAEuH,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9EtB,IAAI,EAAE9J;IACV,CAAC,EAAE;MACC8J,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAAC3J,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEiO,MAAM,EAAE,CAAC;MAClC/E,IAAI,EAAE/I,eAAe;MACrBwJ,IAAI,EAAE,CAAC2H,kBAAkB,EAAE;QAAEuK,WAAW,EAAE;MAAM,CAAC;IACrD,CAAC,CAAC;IAAE/D,iBAAiB,EAAE,CAAC;MACpB5O,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEkE,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEuK,QAAQ,EAAE,CAAC;MACXlP,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEkE,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEqL,aAAa,EAAE,CAAC;MAChBhQ,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEkE,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEkG,cAAc,EAAE,CAAC;MACjB7K,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEkK,kBAAkB,EAAE,CAAC;MACrB3K,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEtC,aAAa,EAAE,CAAC;MAChB6B,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMyc,eAAe,GAAG,IAAI7c,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA,MAAM8c,iBAAiB,GAAG;EACtB;EACAC,YAAY,EAAEpZ,OAAO,CAAC,cAAc,EAAE;EAClC;EACAC,KAAK,CAAC,uDAAuD,EAAEC,KAAK,CAAC;IAAE6K,SAAS,EAAE;EAAO,CAAC,CAAC,CAAC;EAC5F;EACA;EACA;EACA;EACA9K,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChB6K,SAAS,EAAE,0BAA0B;IACrCsO,SAAS,EAAE,KAAK;IAChB;IACA;IACAC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACHrZ,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;IACjB6K,SAAS,EAAE,yBAAyB;IACpCsO,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACHnZ,UAAU,CAAC,wDAAwD,EAAEC,OAAO,CAAC,sDAAsD,CAAC,CAAC,EACrID,UAAU,CAAC,4BAA4B,EAAE,CACrCD,KAAK,CAAC;IAAE6K,SAAS,EAAE,0BAA0B;IAAEuO,UAAU,EAAE;EAAS,CAAC,CAAC,EACtElZ,OAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,EACFD,UAAU,CAAC,6BAA6B,EAAE,CACtCD,KAAK,CAAC;IAAE6K,SAAS,EAAE,yBAAyB;IAAEuO,UAAU,EAAE;EAAS,CAAC,CAAC,EACrElZ,OAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,CACL;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMmZ,gBAAgB,SAAStb,eAAe,CAAC;EAC3C6H,WAAWA,CAAC0T,wBAAwB,EAAElS,gBAAgB,EAAEmS,KAAK,EAAEC,SAAS,EAAE;IACtE,KAAK,CAACF,wBAAwB,EAAElS,gBAAgB,EAAEoS,SAAS,CAAC;IAC5D,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACE,aAAa,GAAGlb,YAAY,CAACH,KAAK;IACvC;IACA,IAAI,CAACsb,WAAW,GAAGnb,YAAY,CAACH,KAAK;EACzC;EACA;EACAuK,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAAC8Q,aAAa,GAAG,IAAI,CAACF,KAAK,CAACI,gBAAgB,CAC3ChJ,IAAI,CAACpR,SAAS,CAAC,IAAI,CAACga,KAAK,CAACK,iBAAiB,CAAC,IAAI,CAACL,KAAK,CAACM,SAAS,CAAC,CAAC,CAAC,CACnEjJ,SAAS,CAAEkJ,WAAW,IAAK;MAC5B,IAAIA,WAAW,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;QACpC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC;MACpC;IACJ,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,GAAG,IAAI,CAACH,KAAK,CAACW,mBAAmB,CAACtJ,SAAS,CAAC,MAAM;MAC9D,IAAI,CAAC,IAAI,CAAC2I,KAAK,CAACjU,eAAe,EAAE;QAC7B,IAAI,CAAC6U,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACA1R,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACgR,aAAa,CAACW,WAAW,CAAC,CAAC;IAChC,IAAI,CAACV,WAAW,CAACU,WAAW,CAAC,CAAC;EAClC;EACA;IAAS,IAAI,CAACtU,IAAI,YAAAuU,yBAAArU,CAAA;MAAA,YAAAA,CAAA,IAAwFqT,gBAAgB,EA78B1Bnd,EAAE,CAAA+J,iBAAA,CA68B0C/J,EAAE,CAACoe,wBAAwB,GA78BvEpe,EAAE,CAAA+J,iBAAA,CA68BkF/J,EAAE,CAACqL,gBAAgB,GA78BvGrL,EAAE,CAAA+J,iBAAA,CA68BkH3I,UAAU,CAAC,MAAMid,UAAU,CAAC,GA78BhJre,EAAE,CAAA+J,iBAAA,CA68B2JpG,QAAQ;IAAA,CAA4C;EAAE;EACnT;IAAS,IAAI,CAACqG,IAAI,kBA98B8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EA88BJiT,gBAAgB;MAAAhT,SAAA;MAAAC,UAAA;MAAAC,QAAA,GA98BdrK,EAAE,CAAAsL,0BAAA;IAAA,EA88BsG;EAAE;AAC9M;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAh9BoGzK,EAAE,CAAA0K,iBAAA,CAg9BXyS,gBAAgB,EAAc,CAAC;IAC9GjT,IAAI,EAAEhK,SAAS;IACfyK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BR,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAElK,EAAE,CAACoe;EAAyB,CAAC,EAAE;IAAElU,IAAI,EAAElK,EAAE,CAACqL;EAAiB,CAAC,EAAE;IAAEnB,IAAI,EAAEmU,UAAU;IAAE7S,UAAU,EAAE,CAAC;MACtHtB,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAACvJ,UAAU,CAAC,MAAMid,UAAU,CAAC;IACvC,CAAC;EAAE,CAAC,EAAE;IAAEnU,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCtB,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAAChH,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA,MAAM0a,UAAU,CAAC;EACb;EACA,IAAInV,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACoV,cAAc,GAAGpV,QAAQ;IAC9B,IAAI,CAACqV,8BAA8B,CAAC,CAAC;EACzC;EACA7U,WAAWA,CAACwG,WAAW,EAAEyD,IAAI,EAAE0H,iBAAiB,EAAE;IAC9C,IAAI,CAACnL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACyD,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC6K,sBAAsB,GAAGnc,YAAY,CAACH,KAAK;IAChD;IACA,IAAI,CAACuc,qBAAqB,GAAG,IAAI3c,OAAO,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC4c,YAAY,GAAG,IAAI3d,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAAC0c,gBAAgB,GAAG,IAAI1c,YAAY,CAAC,CAAC;IAC1C;IACA,IAAI,CAACid,mBAAmB,GAAG,IAAIjd,YAAY,CAAC,CAAC;IAC7C;IACA,IAAI,CAAC4d,WAAW,GAAG,IAAI5d,YAAY,CAAC,IAAI,CAAC;IACzC;IACA;IACA;IACA,IAAI,CAAC8D,iBAAiB,GAAG,OAAO;IAChC;IACA,IAAI,CAACuE,eAAe,GAAG,KAAK;IAC5B,IAAIuK,IAAI,EAAE;MACN,IAAI,CAAC6K,sBAAsB,GAAG7K,IAAI,CAACuB,MAAM,CAACR,SAAS,CAAEqE,GAAG,IAAK;QACzD,IAAI,CAACwF,8BAA8B,CAACxF,GAAG,CAAC;QACxCsC,iBAAiB,CAAC/D,YAAY,CAAC,CAAC;MACpC,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAACmH,qBAAqB,CACrBhK,IAAI,CAAChR,oBAAoB,CAAC,CAACmb,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACE,SAAS,KAAKD,CAAC,CAACC,SAAS,IAAIF,CAAC,CAACG,OAAO,KAAKF,CAAC,CAACE,OAAO;IACjE,CAAC,CAAC,CAAC,CACErK,SAAS,CAACmD,KAAK,IAAI;MACpB;MACA,IAAI,IAAI,CAAC6F,iBAAiB,CAAC7F,KAAK,CAACkH,OAAO,CAAC,IAAI,IAAI,CAACrB,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACjF,IAAI,CAACgB,WAAW,CAACpI,IAAI,CAAC,CAAC;MAC3B;MACA,IAAI,IAAI,CAACmH,iBAAiB,CAAC7F,KAAK,CAACiH,SAAS,CAAC,IAAI,CAAC,IAAI,CAACpB,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACpF,IAAI,CAACK,mBAAmB,CAACzH,IAAI,CAAC,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI9J,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACkR,SAAS,IAAI,QAAQ,IAAI,IAAI,CAACxU,MAAM,IAAI,IAAI,EAAE;MACnD,IAAI,CAACwU,SAAS,GAAG,IAAI,CAACqB,0BAA0B,CAAC,IAAI,CAAC7V,MAAM,CAAC;IACjE;EACJ;EACAoD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiS,sBAAsB,CAACN,WAAW,CAAC,CAAC;IACzC,IAAI,CAACO,qBAAqB,CAACjS,QAAQ,CAAC,CAAC;EACzC;EACAyS,sBAAsBA,CAACpH,KAAK,EAAE;IAC1B,MAAM+F,WAAW,GAAG,IAAI,CAACF,iBAAiB,CAAC7F,KAAK,CAACkH,OAAO,CAAC;IACzD,IAAI,CAACtB,gBAAgB,CAAClH,IAAI,CAACqH,WAAW,CAAC;IACvC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACc,YAAY,CAACnI,IAAI,CAAC,IAAI,CAACrG,WAAW,CAACP,aAAa,CAACuP,YAAY,CAAC;IACvE;EACJ;EACA;EACA1J,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7B,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC3O,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACA0Y,iBAAiBA,CAACxU,QAAQ,EAAE;IACxB,OAAQA,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,IAAI,oBAAoB,IAAIA,QAAQ,IAAI,qBAAqB;EACzG;EACA;EACAqV,8BAA8BA,CAACxF,GAAG,GAAG,IAAI,CAACvD,mBAAmB,CAAC,CAAC,EAAE;IAC7D,IAAI,IAAI,CAAC8I,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACX,SAAS,GAAG5E,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;IACpD,CAAC,MACI,IAAI,IAAI,CAACuF,cAAc,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACX,SAAS,GAAG5E,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM;IACpD,CAAC,MACI;MACD,IAAI,CAAC4E,SAAS,GAAG,QAAQ;IAC7B;EACJ;EACA;AACJ;AACA;AACA;EACIqB,0BAA0BA,CAAC7V,MAAM,EAAE;IAC/B,MAAM4P,GAAG,GAAG,IAAI,CAACvD,mBAAmB,CAAC,CAAC;IACtC,IAAKuD,GAAG,IAAI,KAAK,IAAI5P,MAAM,IAAI,CAAC,IAAM4P,GAAG,IAAI,KAAK,IAAI5P,MAAM,GAAG,CAAE,EAAE;MAC/D,OAAO,oBAAoB;IAC/B;IACA,OAAO,qBAAqB;EAChC;EACA;IAAS,IAAI,CAACS,IAAI,YAAAuV,mBAAArV,CAAA;MAAA,YAAAA,CAAA,IAAwFuU,UAAU,EArkCpBre,EAAE,CAAA+J,iBAAA,CAqkCoC/J,EAAE,CAACc,UAAU,GArkCnDd,EAAE,CAAA+J,iBAAA,CAqkC8DnH,EAAE,CAACqY,cAAc,MArkCjFjb,EAAE,CAAA+J,iBAAA,CAqkC4G/J,EAAE,CAAC+a,iBAAiB;IAAA,CAA4C;EAAE;EAChR;IAAS,IAAI,CAACnO,IAAI,kBAtkC8E5M,EAAE,CAAA6M,iBAAA;MAAA3C,IAAA,EAskCJmU,UAAU;MAAAlU,SAAA;MAAAmD,SAAA,WAAA8R,iBAAAjb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtkCRnE,EAAE,CAAAwN,WAAA,CAskCqe3L,eAAe;QAAA;QAAA,IAAAsC,EAAA;UAAA,IAAA+I,EAAA;UAtkCtflN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAib,WAAA,GAAAnS,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAC,MAAA;QAAAqQ,QAAA,GAAF/d,EAAE,CAAA2N,YAAA,CAAAE,IAAA;QAAA1E,MAAA;QAAAtE,iBAAA;QAAAuE,eAAA;QAAAF,QAAA;MAAA;MAAAiS,OAAA;QAAAuD,YAAA;QAAAjB,gBAAA;QAAAO,mBAAA;QAAAW,WAAA;MAAA;MAAAvU,UAAA;MAAAC,QAAA,GAAFrK,EAAE,CAAAiO,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA2N,MAAA;MAAApS,QAAA,WAAA2V,oBAAAnb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA8X,GAAA,GAAFjc,EAAE,CAAAmG,gBAAA;UAAFnG,EAAE,CAAAoG,cAAA,eAskCi2B,CAAC;UAtkCp2BpG,EAAE,CAAAqG,UAAA,iCAAAkZ,gEAAAtY,MAAA;YAAFjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CAskC2uB1C,GAAA,CAAA6a,sBAAA,CAAAhY,MAA6B,CAAC;UAAA,CAAC,CAAC,gCAAAuY,+DAAAvY,MAAA;YAtkC7wBjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CAskCyyB1C,GAAA,CAAAqa,qBAAA,CAAAnS,IAAA,CAAArF,MAAiC,CAAC;UAAA,CAAC,CAAC;UAtkC/0BjH,EAAE,CAAAuF,UAAA,IAAAL,iCAAA,wBAskCi4B,CAAC;UAtkCp4BlF,EAAE,CAAAoH,YAAA,CAskCu5B,CAAC;QAAA;QAAA,IAAAjD,EAAA;UAtkC15BnE,EAAE,CAAA2F,UAAA,kBAAF3F,EAAE,CAAAyf,eAAA,IAAA3a,GAAA,EAAAV,GAAA,CAAAuZ,SAAA,EAAF3d,EAAE,CAAA0f,eAAA,IAAA/a,GAAA,EAAAP,GAAA,CAAAS,iBAAA,EAskC2sB,CAAC;QAAA;MAAA;MAAA6X,YAAA,GAA6yBS,gBAAgB,EAA6D3a,aAAa;MAAAma,MAAA;MAAApO,aAAA;MAAAoR,IAAA;QAAAC,SAAA,EAAgE,CAAC7C,iBAAiB,CAACC,YAAY;MAAC;IAAA,EAAkG;EAAE;AAC73D;AACA;EAAA,QAAAvS,SAAA,oBAAAA,SAAA,KAxkCoGzK,EAAE,CAAA0K,iBAAA,CAwkCX2T,UAAU,EAAc,CAAC;IACxGnU,IAAI,EAAE3J,SAAS;IACfoK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAE2D,aAAa,EAAE9N,iBAAiB,CAACoN,IAAI;MAAEW,eAAe,EAAEhO,uBAAuB,CAACiO,OAAO;MAAEoR,UAAU,EAAE,CAAC9C,iBAAiB,CAACC,YAAY,CAAC;MAAEtO,IAAI,EAAE;QACpK,OAAO,EAAE;MACb,CAAC;MAAEtE,UAAU,EAAE,IAAI;MAAEwS,OAAO,EAAE,CAACO,gBAAgB,EAAE3a,aAAa,CAAC;MAAEmH,QAAQ,EAAE,uXAAuX;MAAEgT,MAAM,EAAE,CAAC,siBAAsiB;IAAE,CAAC;EAClgC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzS,IAAI,EAAElK,EAAE,CAACc;EAAW,CAAC,EAAE;IAAEoJ,IAAI,EAAEtH,EAAE,CAACqY,cAAc;IAAEzP,UAAU,EAAE,CAAC;MAChFtB,IAAI,EAAE9J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAElK,EAAE,CAAC+a;EAAkB,CAAC,CAAC,EAAkB;IAAE2D,YAAY,EAAE,CAAC;MACxExU,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAEuc,gBAAgB,EAAE,CAAC;MACnBvT,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAE8c,mBAAmB,EAAE,CAAC;MACtB9T,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAEyd,WAAW,EAAE,CAAC;MACdzU,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAEme,WAAW,EAAE,CAAC;MACdnV,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC9I,eAAe;IAC1B,CAAC,CAAC;IAAEkc,QAAQ,EAAE,CAAC;MACX7T,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAExB,MAAM,EAAE,CAAC;MACTe,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEmE,iBAAiB,EAAE,CAAC;MACpBqF,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAE0I,eAAe,EAAE,CAAC;MAClBc,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEwI,QAAQ,EAAE,CAAC;MACXgB,IAAI,EAAExJ;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAIof,MAAM,GAAG,CAAC;AACd;AACA,MAAMC,uBAAuB,GAAG,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;EACA,IAAIpY,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACqY,mBAAmB;EACnC;EACA,IAAIrY,kBAAkBA,CAAC5C,KAAK,EAAE;IAC1B,IAAI,CAACib,mBAAmB,GAAGjb,KAAK;IAChC,IAAI,CAACyO,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAI7P,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC0L,cAAc;EAC9B;EACA,IAAI1L,aAAaA,CAACzC,KAAK,EAAE;IACrB,IAAI,CAACkb,cAAc,GAAG7M,KAAK,CAACrO,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACrD;EACA;EACA,IAAIH,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACsb,kBAAkB;EAClC;EACA,IAAItb,iBAAiBA,CAACG,KAAK,EAAE;IACzB,MAAMob,WAAW,GAAGpb,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACmb,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAGpb,KAAK,GAAG,IAAI,GAAGob,WAAW;EACpF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI/W,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACiX,gBAAgB;EAChC;EACA,IAAIjX,eAAeA,CAACrE,KAAK,EAAE;IACvB,IAAI,CAACsb,gBAAgB,GAAGjN,KAAK,CAACrO,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACvD;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIub,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACvb,KAAK,EAAE;IACvB,IAAI,CAAC+a,uBAAuB,EAAE;MAC1B,MAAM,IAAIpO,KAAK,CAAC,yEAAyE,CAAC;IAC9F;IACA,MAAMlB,SAAS,GAAG,IAAI,CAACP,WAAW,CAACP,aAAa,CAACc,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAACsP,eAAe,EAAE,CAAC;IACtF,IAAIvb,KAAK,EAAE;MACPyL,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkB1L,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAACwb,gBAAgB,GAAGxb,KAAK;EACjC;EACA0E,WAAWA,CAACwG,WAAW,EAAEuD,kBAAkB,EAAEgN,aAAa,EAAE3M,cAAc,EAAE;IACxE,IAAI,CAAC5D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACuD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACK,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAAC/L,KAAK,GAAG,IAAI1G,SAAS,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC6e,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACQ,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B;IACA,IAAI,CAACC,iBAAiB,GAAGve,YAAY,CAACH,KAAK;IAC3C;IACA,IAAI,CAAC2e,qBAAqB,GAAGxe,YAAY,CAACH,KAAK;IAC/C,IAAI,CAAC+d,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACa,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC5N,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAAC6N,cAAc,GAAG,OAAO;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAAC3M,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAAChM,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACe,eAAe,GAAG,KAAK;IAC5B;IACA,IAAI,CAAC6X,mBAAmB,GAAG,IAAIlgB,YAAY,CAAC,CAAC;IAC7C;IACA,IAAI,CAACmgB,WAAW,GAAG,IAAIngB,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACogB,aAAa,GAAG,IAAIpgB,YAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAACqgB,iBAAiB,GAAG,IAAIrgB,YAAY,CAAC,IAAI,CAAC;IAC/C;IACA,IAAI,CAACsgB,SAAS,GAAG,CAACxgB,MAAM,CAAC8B,QAAQ,CAAC,CAAC2e,SAAS;IAC5C,IAAI,CAACC,QAAQ,GAAGzB,MAAM,EAAE;IACxB,IAAI,CAACjb,iBAAiB,GAClB4b,aAAa,IAAIA,aAAa,CAAC5b,iBAAiB,GAAG4b,aAAa,CAAC5b,iBAAiB,GAAG,OAAO;IAChG,IAAI,CAACwP,iBAAiB,GAClBoM,aAAa,IAAIA,aAAa,CAACpM,iBAAiB,IAAI,IAAI,GAClDoM,aAAa,CAACpM,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAAC0M,aAAa,GACdN,aAAa,IAAIA,aAAa,CAACM,aAAa,IAAI,IAAI,GAAGN,aAAa,CAACM,aAAa,GAAG,KAAK;IAC9F,IAAIN,aAAa,EAAEpX,eAAe,IAAI,IAAI,EAAE;MACxC,IAAI,CAACA,eAAe,GAAGoX,aAAa,CAACpX,eAAe;IACxD;IACA,IAAI,CAACD,eAAe,GAAG,CAAC,CAACqX,aAAa,EAAErX,eAAe;IACvD,IAAI,CAACxB,kBAAkB,GACnB6Y,aAAa,IAAIA,aAAa,CAAC7Y,kBAAkB,IAAI,IAAI,GACnD6Y,aAAa,CAAC7Y,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACkZ,WAAW,GACZL,aAAa,IAAIA,aAAa,CAACK,WAAW,IAAI,IAAI,GAAGL,aAAa,CAACK,WAAW,GAAG,IAAI;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;EACI1J,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,MAAMoK,aAAa,GAAI,IAAI,CAACtB,cAAc,GAAG,IAAI,CAACuB,cAAc,CAAC,IAAI,CAACvB,cAAc,CAAE;IACtF;IACA;IACA,IAAI,IAAI,CAAC/M,cAAc,IAAIqO,aAAa,EAAE;MACtC,MAAME,UAAU,GAAG,IAAI,CAACvO,cAAc,IAAI,IAAI;MAC9C,IAAI,CAACuO,UAAU,EAAE;QACb,IAAI,CAACN,iBAAiB,CAAC7K,IAAI,CAAC,IAAI,CAACoL,kBAAkB,CAACH,aAAa,CAAC,CAAC;QACnE;QACA;QACA,MAAMI,OAAO,GAAG,IAAI,CAACC,eAAe,CAAClS,aAAa;QAClDiS,OAAO,CAAC9d,KAAK,CAACmZ,SAAS,GAAG2E,OAAO,CAAC1C,YAAY,GAAG,IAAI;MACzD;MACA;MACA;MACAnJ,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAClO,KAAK,CAACoH,OAAO,CAAC,CAAC2S,GAAG,EAAEpJ,KAAK,KAAMoJ,GAAG,CAAC5V,QAAQ,GAAGwM,KAAK,KAAK8I,aAAc,CAAC;QAC5E,IAAI,CAACE,UAAU,EAAE;UACb,IAAI,CAACT,mBAAmB,CAAC1K,IAAI,CAACiL,aAAa,CAAC;UAC5C;UACA;UACA,IAAI,CAACK,eAAe,CAAClS,aAAa,CAAC7L,KAAK,CAACmZ,SAAS,GAAG,EAAE;QAC3D;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAClV,KAAK,CAACoH,OAAO,CAAC,CAAC2S,GAAG,EAAEpJ,KAAK,KAAK;MAC/BoJ,GAAG,CAAC5Y,QAAQ,GAAGwP,KAAK,GAAG8I,aAAa;MACpC;MACA;MACA,IAAI,IAAI,CAACrO,cAAc,IAAI,IAAI,IAAI2O,GAAG,CAAC5Y,QAAQ,IAAI,CAAC,IAAI,CAAC4Y,GAAG,CAAC3Y,MAAM,EAAE;QACjE2Y,GAAG,CAAC3Y,MAAM,GAAGqY,aAAa,GAAG,IAAI,CAACrO,cAAc;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACA,cAAc,KAAKqO,aAAa,EAAE;MACvC,IAAI,CAACrO,cAAc,GAAGqO,aAAa;MACnC,IAAI,CAACd,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACjN,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAtC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC+M,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,CAACpB,iBAAiB,GAAG,IAAI,CAAC7Y,KAAK,CAACqE,OAAO,CAACsI,SAAS,CAAC,MAAM;MACxD,MAAM8M,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACvB,cAAc,CAAC;MAC9D;MACA;MACA,IAAIsB,aAAa,KAAK,IAAI,CAACrO,cAAc,EAAE;QACvC,MAAM8O,IAAI,GAAG,IAAI,CAACla,KAAK,CAAC4Q,OAAO,CAAC,CAAC;QACjC,IAAIuJ,WAAW;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACja,MAAM,EAAEma,CAAC,EAAE,EAAE;UAClC,IAAIF,IAAI,CAACE,CAAC,CAAC,CAACjW,QAAQ,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAACgU,cAAc,GAAG,IAAI,CAAC/M,cAAc,GAAGgP,CAAC;YAC7C,IAAI,CAACzB,oBAAoB,GAAG,IAAI;YAChCwB,WAAW,GAAGD,IAAI,CAACE,CAAC,CAAC;YACrB;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI,CAACD,WAAW,IAAID,IAAI,CAACT,aAAa,CAAC,EAAE;UACrCzL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;YACzBgM,IAAI,CAACT,aAAa,CAAC,CAACtV,QAAQ,GAAG,IAAI;YACnC,IAAI,CAACkV,iBAAiB,CAAC7K,IAAI,CAAC,IAAI,CAACoL,kBAAkB,CAACH,aAAa,CAAC,CAAC;UACvE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC/N,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACAyK,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA,IAAI,CAACK,QAAQ,CAAChW,OAAO,CAACqI,IAAI,CAACpR,SAAS,CAAC,IAAI,CAAC+e,QAAQ,CAAC,CAAC,CAAC1N,SAAS,CAAEuN,IAAI,IAAK;MACrE,IAAI,CAACla,KAAK,CAACsa,KAAK,CAACJ,IAAI,CAACze,MAAM,CAACse,GAAG,IAAI;QAChC,OAAOA,GAAG,CAAC/V,gBAAgB,KAAK,IAAI,IAAI,CAAC+V,GAAG,CAAC/V,gBAAgB;MACjE,CAAC,CAAC,CAAC;MACH,IAAI,CAAChE,KAAK,CAACua,eAAe,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACA/V,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxE,KAAK,CAAC4P,OAAO,CAAC,CAAC;IACpB,IAAI,CAACiJ,iBAAiB,CAAC1C,WAAW,CAAC,CAAC;IACpC,IAAI,CAAC2C,qBAAqB,CAAC3C,WAAW,CAAC,CAAC;EAC5C;EACA;EACAqE,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAClN,yBAAyB,CAAC,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACmN,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACnN,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACIoN,QAAQA,CAAC/J,KAAK,EAAE;IACZ,MAAMgK,MAAM,GAAG,IAAI,CAACF,UAAU;IAC9B,IAAIE,MAAM,EAAE;MACRA,MAAM,CAAC3K,UAAU,GAAGW,KAAK;IAC7B;EACJ;EACAiK,aAAaA,CAACjK,KAAK,EAAE;IACjB,IAAI,CAACgI,oBAAoB,GAAGhI,KAAK;IACjC,IAAI,CAACwI,WAAW,CAAC3K,IAAI,CAAC,IAAI,CAACoL,kBAAkB,CAACjJ,KAAK,CAAC,CAAC;EACzD;EACAiJ,kBAAkBA,CAACjJ,KAAK,EAAE;IACtB,MAAMb,KAAK,GAAG,IAAI+K,iBAAiB,CAAC,CAAC;IACrC/K,KAAK,CAACa,KAAK,GAAGA,KAAK;IACnB,IAAI,IAAI,CAAC3Q,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE;MACjC6P,KAAK,CAACiK,GAAG,GAAG,IAAI,CAAC/Z,KAAK,CAAC4Q,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC;IAC3C;IACA,OAAOb,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACImK,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACnB,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC3C,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAAC2C,qBAAqB,GAAG5e,KAAK,CAAC,GAAG,IAAI,CAAC8F,KAAK,CAAC8a,GAAG,CAACf,GAAG,IAAIA,GAAG,CAAC7V,aAAa,CAAC,CAAC,CAACyI,SAAS,CAAC,MAAM,IAAI,CAACjB,kBAAkB,CAAC6D,YAAY,CAAC,CAAC,CAAC;EAC3I;EACA;EACAmK,cAAcA,CAAC/I,KAAK,EAAE;IAClB;IACA;IACA;IACA,OAAOxC,IAAI,CAACE,GAAG,CAAC,IAAI,CAACrO,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEkO,IAAI,CAACC,GAAG,CAACuC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;EACA;EACAhR,cAAcA,CAACya,CAAC,EAAE;IACd,OAAO,iBAAiB,IAAI,CAACZ,QAAQ,IAAIY,CAAC,EAAE;EAChD;EACA;EACAla,gBAAgBA,CAACka,CAAC,EAAE;IAChB,OAAO,mBAAmB,IAAI,CAACZ,QAAQ,IAAIY,CAAC,EAAE;EAClD;EACA;AACJ;AACA;AACA;EACItZ,wBAAwBA,CAACia,SAAS,EAAE;IAChC,IAAI,CAAC,IAAI,CAAC/B,aAAa,IAAI,CAAC,IAAI,CAACJ,qBAAqB,EAAE;MACpD;IACJ;IACA,MAAMiB,OAAO,GAAG,IAAI,CAACC,eAAe,CAAClS,aAAa;IAClDiS,OAAO,CAAC9d,KAAK,CAACqT,MAAM,GAAG,IAAI,CAACwJ,qBAAqB,GAAG,IAAI;IACxD;IACA;IACA,IAAI,IAAI,CAACkB,eAAe,CAAClS,aAAa,CAACoT,YAAY,EAAE;MACjDnB,OAAO,CAAC9d,KAAK,CAACqT,MAAM,GAAG2L,SAAS,GAAG,IAAI;IAC3C;EACJ;EACA;EACAna,2BAA2BA,CAAA,EAAG;IAC1B,MAAMiZ,OAAO,GAAG,IAAI,CAACC,eAAe,CAAClS,aAAa;IAClD,IAAI,CAACgR,qBAAqB,GAAGiB,OAAO,CAAC1C,YAAY;IACjD0C,OAAO,CAAC9d,KAAK,CAACqT,MAAM,GAAG,EAAE;IACzB,IAAI,CAACgK,aAAa,CAAC5K,IAAI,CAAC,CAAC;EAC7B;EACA;EACAxP,YAAYA,CAAC+a,GAAG,EAAEkB,SAAS,EAAEtK,KAAK,EAAE;IAChCsK,SAAS,CAACjL,UAAU,GAAGW,KAAK;IAC5B,IAAI,CAACoJ,GAAG,CAACna,QAAQ,EAAE;MACf,IAAI,CAACF,aAAa,GAAGiR,KAAK;IAC9B;EACJ;EACA;EACA5Q,YAAYA,CAAC4Q,KAAK,EAAE;IAChB,MAAMuK,WAAW,GAAG,IAAI,CAACvC,oBAAoB,IAAI,IAAI,CAACjZ,aAAa;IACnE,OAAOiR,KAAK,KAAKuK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC;EACA;EACA/b,gBAAgBA,CAACgc,WAAW,EAAExK,KAAK,EAAE;IACjC;IACA;IACA;IACA;IACA,IAAIwK,WAAW,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACnE,IAAI,CAACV,UAAU,CAACzK,UAAU,GAAGW,KAAK;IACtC;EACJ;EACA;IAAS,IAAI,CAAC9O,IAAI,YAAAuZ,oBAAArZ,CAAA;MAAA,YAAAA,CAAA,IAAwFkW,WAAW,EA/7CrBhgB,EAAE,CAAA+J,iBAAA,CA+7CqC/J,EAAE,CAACc,UAAU,GA/7CpDd,EAAE,CAAA+J,iBAAA,CA+7C+D/J,EAAE,CAAC+a,iBAAiB,GA/7CrF/a,EAAE,CAAA+J,iBAAA,CA+7CgG+S,eAAe,MA/7CjH9c,EAAE,CAAA+J,iBAAA,CA+7C4I/I,qBAAqB;IAAA,CAA4D;EAAE;EACjU;IAAS,IAAI,CAAC4L,IAAI,kBAh8C8E5M,EAAE,CAAA6M,iBAAA;MAAA3C,IAAA,EAg8CJ8V,WAAW;MAAA7V,SAAA;MAAA2C,cAAA,WAAAsW,2BAAAjf,EAAA,EAAAC,GAAA,EAAA4I,QAAA;QAAA,IAAA7I,EAAA;UAh8CTnE,EAAE,CAAAiN,cAAA,CAAAD,QAAA,EAq8C1CtB,MAAM;QAAA;QAAA,IAAAvH,EAAA;UAAA,IAAA+I,EAAA;UAr8CkClN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAge,QAAA,GAAAlV,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAA+V,kBAAAlf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAwN,WAAA,CAAArI,GAAA;UAAFnF,EAAE,CAAAwN,WAAA,CAAApI,GAAA;QAAA;QAAA,IAAAjB,EAAA;UAAA,IAAA+I,EAAA;UAAFlN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAyd,eAAA,GAAA3U,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAoe,UAAA,GAAAtV,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAkF,QAAA;MAAAC,YAAA,WAAA0Q,yBAAAnf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAsH,UAAA,CAg8CJ,MAAM,IAAAlD,GAAA,CAAAmf,KAAA,IAAa,SAAS,CAAlB,CAAC;UAh8CTvjB,EAAE,CAAAwjB,WAAA,iCAAApf,GAAA,CAAAS,iBAg8CM,CAAC;UAh8CT7E,EAAE,CAAAwH,WAAA,qCAAApD,GAAA,CAAA2c,aAg8CM,CAAC,sCAAA3c,GAAA,CAAA4c,cAAA,KAAQ,OAAT,CAAC,mCAAA5c,GAAA,CAAA0c,WAAD,CAAC;QAAA;MAAA;MAAApT,MAAA;QAAA6V,KAAA;QAAA3b,kBAAA,GAh8CT5H,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,8CAg8CmJvN,gBAAgB;QAAAygB,WAAA,GAh8CrK9gB,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,qCAg8CuNvN,gBAAgB;QAAA0gB,aAAA,GAh8CzO/gB,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,oCAg8C4RvN,gBAAgB;QAAAoH,aAAA,GAh8C9SzH,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,oCAg8CiW3M,eAAe;QAAA+f,cAAA;QAAAnc,iBAAA;QAAAwE,eAAA,GAh8ClXrJ,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,wCAg8Cqf3M,eAAe;QAAAoT,iBAAA,GAh8CtgBrU,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,4CAg8CqkBvN,gBAAgB;QAAAgI,aAAA,GAh8CvlBrI,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,oCAg8C0oBvN,gBAAgB;QAAA+I,eAAA,GAh8C5pBpJ,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,wCAg8CqtBvN,gBAAgB;QAAAkgB,eAAA;MAAA;MAAApF,OAAA;QAAA8F,mBAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,iBAAA;MAAA;MAAAtT,QAAA;MAAA1D,UAAA;MAAAC,QAAA,GAh8CvuBrK,EAAE,CAAAsK,kBAAA,CAg8C2xC,CACr3C;QACIC,OAAO,EAAEkB,aAAa;QACtBjB,WAAW,EAAEwV;MACjB,CAAC,CACJ,GAr8C2FhgB,EAAE,CAAA+N,wBAAA,EAAF/N,EAAE,CAAAiO,mBAAA;MAAAC,kBAAA,EAAAjK,GAAA;MAAAkK,KAAA;MAAAC,IAAA;MAAA2N,MAAA;MAAApS,QAAA,WAAA8Z,qBAAAtf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA8X,GAAA,GAAFjc,EAAE,CAAAmG,gBAAA;UAAFnG,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAoG,cAAA,0BAq8CgjB,CAAC;UAr8CnjBpG,EAAE,CAAAqG,UAAA,0BAAAqd,4DAAAzc,MAAA;YAAFjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CAq8Cwd1C,GAAA,CAAAue,aAAA,CAAA1b,MAAoB,CAAC;UAAA,CAAC,CAAC,gCAAA0c,kEAAA1c,MAAA;YAr8CjfjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CAAA1C,GAAA,CAAAqD,aAAA,GAAAR,MAAA;UAAA,CAq8C+iB,CAAC;UAr8CljBjH,EAAE,CAAA4jB,gBAAA,IAAA3d,0BAAA,mBAAFjG,EAAE,CAAA6jB,yBAq8Cq4E,CAAC;UAr8Cx4E7jB,EAAE,CAAAoH,YAAA,CAq8Cw5E,CAAC;UAr8C35EpH,EAAE,CAAAuF,UAAA,IAAAgD,kCAAA,MAq8C6tF,CAAC;UAr8ChuFvI,EAAE,CAAAoG,cAAA,eAq8C83F,CAAC;UAr8Cj4FpG,EAAE,CAAA4jB,gBAAA,IAAApb,0BAAA,4BAAFxI,EAAE,CAAA6jB,yBAq8CqwH,CAAC;UAr8CxwH7jB,EAAE,CAAAoH,YAAA,CAq8C6wH,CAAC;QAAA;QAAA,IAAAjD,EAAA;UAr8ChxHnE,EAAE,CAAA2F,UAAA,kBAAAvB,GAAA,CAAAqD,aAAA,KAq8CsU,CAAC,kBAAArD,GAAA,CAAAiE,aAAkD,CAAC,sBAAAjE,GAAA,CAAAiQ,iBAA0D,CAAC;UAr8CvbrU,EAAE,CAAAoI,SAAA,EAq8Cq4E,CAAC;UAr8Cx4EpI,EAAE,CAAA8jB,UAAA,CAAA1f,GAAA,CAAA2D,KAq8Cq4E,CAAC;UAr8Cx4E/H,EAAE,CAAAoI,SAAA,EAq8CivF,CAAC;UAr8CpvFpI,EAAE,CAAAsI,aAAA,IAAAlE,GAAA,CAAAid,SAAA,SAq8CivF,CAAC;UAr8CpvFrhB,EAAE,CAAAoI,SAAA,CAq8C02F,CAAC;UAr8C72FpI,EAAE,CAAAwH,WAAA,4BAAApD,GAAA,CAAA0P,cAAA,qBAq8C02F,CAAC;UAr8C72F9T,EAAE,CAAAoI,SAAA,EAq8CqwH,CAAC;UAr8CxwHpI,EAAE,CAAA8jB,UAAA,CAAA1f,GAAA,CAAA2D,KAq8CqwH,CAAC;QAAA;MAAA;MAAA2U,YAAA,GAAs9QtB,YAAY,EAAsF9I,kBAAkB,EAAuFvP,eAAe,EAA2JvB,SAAS,EAAwPK,eAAe,EAAiJwc,UAAU;MAAA1B,MAAA;MAAApO,aAAA;IAAA,EAAuS;EAAE;AAC34a;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KAv8CoGzK,EAAE,CAAA0K,iBAAA,CAu8CXsV,WAAW,EAAc,CAAC;IACzG9V,IAAI,EAAE3J,SAAS;IACfoK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEkD,QAAQ,EAAE,aAAa;MAAES,aAAa,EAAE9N,iBAAiB,CAACoN,IAAI;MAAEW,eAAe,EAAEhO,uBAAuB,CAACiO,OAAO;MAAE5D,SAAS,EAAE,CACrJ;QACIN,OAAO,EAAEkB,aAAa;QACtBjB,WAAW,EAAEwV;MACjB,CAAC,CACJ;MAAEtR,IAAI,EAAE;QACL,OAAO,EAAE,mBAAmB;QAC5B,SAAS,EAAE,+BAA+B;QAC1C,0CAA0C,EAAE,eAAe;QAC3D,2CAA2C,EAAE,4BAA4B;QACzE,wCAAwC,EAAE,aAAa;QACvD,sCAAsC,EAAE;MAC5C,CAAC;MAAEtE,UAAU,EAAE,IAAI;MAAEwS,OAAO,EAAE,CAC1BxB,YAAY,EACZ9I,kBAAkB,EAClBvP,eAAe,EACfvB,SAAS,EACTK,eAAe,EACfwc,UAAU,CACb;MAAE1U,QAAQ,EAAE,6hHAA6hH;MAAEgT,MAAM,EAAE,CAAC,m5QAAm5Q;IAAE,CAAC;EACv9X,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzS,IAAI,EAAElK,EAAE,CAACc;EAAW,CAAC,EAAE;IAAEoJ,IAAI,EAAElK,EAAE,CAAC+a;EAAkB,CAAC,EAAE;IAAE7Q,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxGtB,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAACmS,eAAe;IAC1B,CAAC,EAAE;MACC5S,IAAI,EAAE9J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCtB,IAAI,EAAE9J;IACV,CAAC,EAAE;MACC8J,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAAC3J,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEohB,QAAQ,EAAE,CAAC;MACpClY,IAAI,EAAE/I,eAAe;MACrBwJ,IAAI,EAAE,CAACe,MAAM,EAAE;QAAEmR,WAAW,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEgF,eAAe,EAAE,CAAC;MAClB3X,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE6X,UAAU,EAAE,CAAC;MACbtY,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE4Y,KAAK,EAAE,CAAC;MACRrZ,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEkH,kBAAkB,EAAE,CAAC;MACrBsC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEygB,WAAW,EAAE,CAAC;MACd5W,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEoZ,KAAK,EAAE,kBAAkB;QAAEpV,SAAS,EAAEtO;MAAiB,CAAC;IACrE,CAAC,CAAC;IAAE0gB,aAAa,EAAE,CAAC;MAChB7W,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoH,aAAa,EAAE,CAAC;MAChByC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAE1N;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE+f,cAAc,EAAE,CAAC;MACjB9W,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEmE,iBAAiB,EAAE,CAAC;MACpBqF,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAE2I,eAAe,EAAE,CAAC;MAClBa,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAE1N;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEoT,iBAAiB,EAAE,CAAC;MACpBnK,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgI,aAAa,EAAE,CAAC;MAChB6B,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+I,eAAe,EAAE,CAAC;MAClBc,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkgB,eAAe,EAAE,CAAC;MAClBrW,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEugB,mBAAmB,EAAE,CAAC;MACtB/W,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAEggB,WAAW,EAAE,CAAC;MACdhX,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAEigB,aAAa,EAAE,CAAC;MAChBjX,IAAI,EAAEhJ;IACV,CAAC,CAAC;IAAEkgB,iBAAiB,EAAE,CAAC;MACpBlX,IAAI,EAAEhJ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM0hB,iBAAiB,CAAC;;AAGxB;AACA,IAAIoB,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMC,SAAS,SAAS/Q,qBAAqB,CAAC;EAC1C;EACA,IAAItL,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACqY,mBAAmB,CAACjb,KAAK;EACzC;EACA,IAAI4C,kBAAkBA,CAAC5C,KAAK,EAAE;IAC1B,IAAI,CAACib,mBAAmB,CAAC3T,IAAI,CAACtH,KAAK,CAAC;IACpC,IAAI,CAACyO,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;EAC1C;EACA,IAAIzS,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACsb,kBAAkB;EAClC;EACA,IAAItb,iBAAiBA,CAACG,KAAK,EAAE;IACzB,MAAMob,WAAW,GAAGpb,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACmb,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAGpb,KAAK,GAAG,IAAI,GAAGob,WAAW;EACpF;EACA;EACA,IAAIG,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACvb,KAAK,EAAE;IACvB,MAAMyL,SAAS,GAAG,IAAI,CAACP,WAAW,CAACP,aAAa,CAACc,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAACsP,eAAe,EAAE,CAAC;IACtF,IAAIvb,KAAK,EAAE;MACPyL,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkB1L,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAACwb,gBAAgB,GAAGxb,KAAK;EACjC;EACA0E,WAAWA,CAACgG,UAAU,EAAEqJ,GAAG,EAAEwC,MAAM,EAAEF,iBAAiB,EAAEC,aAAa,EAAEE,QAAQ,EAAEC,aAAa,EAAEgF,aAAa,EAAE;IAC3G,KAAK,CAAC/Q,UAAU,EAAE2L,iBAAiB,EAAEC,aAAa,EAAEvC,GAAG,EAAEwC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACwE,mBAAmB,GAAG,IAAI3d,eAAe,CAAC,KAAK,CAAC;IACrD;IACA,IAAI,CAACwe,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACzY,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACkb,KAAK,GAAG,SAAS;IACtB,IAAI,CAAClP,iBAAiB,GAClBoM,aAAa,IAAIA,aAAa,CAACpM,iBAAiB,IAAI,IAAI,GAClDoM,aAAa,CAACpM,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAACzM,kBAAkB,GACnB6Y,aAAa,IAAIA,aAAa,CAAC7Y,kBAAkB,IAAI,IAAI,GACnD6Y,aAAa,CAAC7Y,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACkZ,WAAW,GACZL,aAAa,IAAIA,aAAa,CAACK,WAAW,IAAI,IAAI,GAAGL,aAAa,CAACK,WAAW,GAAG,IAAI;EAC7F;EACA7I,aAAaA,CAAA,EAAG;IACZ;EAAA;EAEJjD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACyF,OAAO,GAAG,IAAIzL,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC;IACA;IACA,IAAI,CAACA,MAAM,CAAC7C,OAAO,CAACqI,IAAI,CAACpR,SAAS,CAAC,IAAI,CAAC,EAAEF,SAAS,CAAC,IAAI,CAAC6Q,UAAU,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;MAClF,IAAI,CAACwP,gBAAgB,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,KAAK,CAAClP,kBAAkB,CAAC,CAAC;EAC9B;EACAJ,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACuP,QAAQ,KAAK,OAAO1Z,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM,IAAIkH,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA,KAAK,CAACiD,eAAe,CAAC,CAAC;EAC3B;EACA;EACAsP,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACjV,MAAM,EAAE;MACd;IACJ;IACA,MAAMmV,KAAK,GAAG,IAAI,CAACnV,MAAM,CAAC0J,OAAO,CAAC,CAAC;IACnC,KAAK,IAAIwJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,KAAK,CAACpc,MAAM,EAAEma,CAAC,EAAE,EAAE;MACnC,IAAIiC,KAAK,CAACjC,CAAC,CAAC,CAACkC,MAAM,EAAE;QACjB,IAAI,CAAC5c,aAAa,GAAG0a,CAAC;QACtB,IAAI,CAAC1O,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC6M,QAAQ,EAAE;UACf,IAAI,CAACA,QAAQ,CAACG,YAAY,GAAGF,KAAK,CAACjC,CAAC,CAAC,CAACoC,EAAE;QAC5C;QACA;MACJ;IACJ;IACA;IACA,IAAI,CAAC9c,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACgT,OAAO,CAACvL,IAAI,CAAC,CAAC;EACvB;EACAsV,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACL,QAAQ,GAAG,SAAS,GAAG,IAAI,CAACjU,WAAW,CAACP,aAAa,CAAC8U,YAAY,CAAC,MAAM,CAAC;EAC1F;EACA;IAAS,IAAI,CAAC7a,IAAI,YAAA8a,kBAAA5a,CAAA;MAAA,YAAAA,CAAA,IAAwFma,SAAS,EA5nDnBjkB,EAAE,CAAA+J,iBAAA,CA4nDmC/J,EAAE,CAACc,UAAU,GA5nDlDd,EAAE,CAAA+J,iBAAA,CA4nD6DnH,EAAE,CAACqY,cAAc,MA5nDhFjb,EAAE,CAAA+J,iBAAA,CA4nD2G/J,EAAE,CAACkb,MAAM,GA5nDtHlb,EAAE,CAAA+J,iBAAA,CA4nDiI/J,EAAE,CAAC+a,iBAAiB,GA5nDvJ/a,EAAE,CAAA+J,iBAAA,CA4nDkKxH,EAAE,CAACyY,aAAa,GA5nDpLhb,EAAE,CAAA+J,iBAAA,CA4nD+LtH,EAAE,CAACE,QAAQ,GA5nD5M3C,EAAE,CAAA+J,iBAAA,CA4nDuN/I,qBAAqB,MA5nD9OhB,EAAE,CAAA+J,iBAAA,CA4nDyQ+S,eAAe;IAAA,CAA4D;EAAE;EACxb;IAAS,IAAI,CAAClQ,IAAI,kBA7nD8E5M,EAAE,CAAA6M,iBAAA;MAAA3C,IAAA,EA6nDJ+Z,SAAS;MAAA9Z,SAAA;MAAA2C,cAAA,WAAA6X,yBAAAxgB,EAAA,EAAAC,GAAA,EAAA4I,QAAA;QAAA,IAAA7I,EAAA;UA7nDPnE,EAAE,CAAAiN,cAAA,CAAAD,QAAA,EA6nDijC4X,UAAU;QAAA;QAAA,IAAAzgB,EAAA;UAAA,IAAA+I,EAAA;UA7nD7jClN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA6K,MAAA,GAAA/B,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAuX,gBAAA1gB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAwN,WAAA,CAAAlJ,GAAA;UAAFtE,EAAE,CAAAwN,WAAA,CAAAjJ,GAAA;UAAFvE,EAAE,CAAAwN,WAAA,CAAAhJ,GAAA;UAAFxE,EAAE,CAAAwN,WAAA,CAAA/I,GAAA;UAAFzE,EAAE,CAAAwN,WAAA,CAAA9I,GAAA;QAAA;QAAA,IAAAP,EAAA;UAAA,IAAA+I,EAAA;UAAFlN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA0U,iBAAA,GAAA5L,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAgV,QAAA,GAAAlM,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA8V,aAAA,GAAAhN,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAA2Q,cAAA,GAAA7H,EAAA,CAAAG,KAAA;UAAFrN,EAAE,CAAAmN,cAAA,CAAAD,EAAA,GAAFlN,EAAE,CAAAoN,WAAA,QAAAhJ,GAAA,CAAAyQ,kBAAA,GAAA3H,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAkF,QAAA;MAAAC,YAAA,WAAAkS,uBAAA3gB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAA6H,WAAA,SA6nDJzD,GAAA,CAAAogB,QAAA,CAAS,CAAC;UA7nDRxkB,EAAE,CAAAwjB,WAAA,iCAAApf,GAAA,CAAAS,iBA6nDI,CAAC;UA7nDP7E,EAAE,CAAAwH,WAAA,mDAAApD,GAAA,CAAA6P,uBA6nDI,CAAC,2BAAT7P,GAAA,CAAAoR,mBAAA,CAAoB,CAAC,IAAI,KAAjB,CAAC,qCAAApR,GAAA,CAAA0c,WAAD,CAAC,gBAAA1c,GAAA,CAAAmf,KAAA,KAAC,MAAM,IAAAnf,GAAA,CAAAmf,KAAA,KAAc,QAAtB,CAAC,eAAAnf,GAAA,CAAAmf,KAAA,KAAC,QAAF,CAAC,aAAAnf,GAAA,CAAAmf,KAAA,KAAC,MAAF,CAAC,4BAAAnf,GAAA,CAAA0P,cAAA,KAAU,gBAAX,CAAC;QAAA;MAAA;MAAApG,MAAA;QAAA9F,kBAAA,GA7nDP5H,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,8CA6nDqIvN,gBAAgB;QAAAygB,WAAA,GA7nDvJ9gB,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,qCA6nDyMvN,gBAAgB;QAAAwE,iBAAA;QAAA0b,eAAA;QAAAlY,aAAA,GA7nD3NrI,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,oCA6nD0VvN,gBAAgB;QAAAkjB,KAAA;QAAAY,QAAA;MAAA;MAAArW,QAAA;MAAA1D,UAAA;MAAAC,QAAA,GA7nD5WrK,EAAE,CAAA+N,wBAAA,EAAF/N,EAAE,CAAAsL,0BAAA,EAAFtL,EAAE,CAAAiO,mBAAA;MAAA8W,KAAA,EAAAzb,IAAA;MAAA4E,kBAAA,EAAAjK,GAAA;MAAAkK,KAAA;MAAAC,IAAA;MAAA2N,MAAA;MAAApS,QAAA,WAAAqb,mBAAA7gB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA8X,GAAA,GAAFjc,EAAE,CAAAmG,gBAAA;UAAFnG,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAoG,cAAA,kBA6nDs0E,CAAC;UA7nDz0EpG,EAAE,CAAAqG,UAAA,mBAAA4e,2CAAA;YAAFjlB,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nDksE1C,GAAA,CAAAyV,qBAAA,CAAsB,QAAQ,CAAC;UAAA,CAAC,CAAC,uBAAAqL,+CAAAje,MAAA;YA7nDruEjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nDwvE1C,GAAA,CAAA0Q,qBAAA,CAAsB,QAAQ,EAAA7N,MAAQ,CAAC;UAAA,CAAC,CAAC,sBAAAke,8CAAA;YA7nDnyEnlB,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nDqzE1C,GAAA,CAAAuQ,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA7nDx0E3U,EAAE,CAAAmH,SAAA,YA6nDq4E,CAAC;UA7nDx4EnH,EAAE,CAAAoH,YAAA,CA6nDg5E,CAAC;UA7nDn5EpH,EAAE,CAAAoG,cAAA,eA6nDq/E,CAAC;UA7nDx/EpG,EAAE,CAAAqG,UAAA,qBAAA+e,0CAAAne,MAAA;YAAFjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nD69E1C,GAAA,CAAAwT,cAAA,CAAA3Q,MAAqB,CAAC;UAAA,CAAC,CAAC;UA7nDv/EjH,EAAE,CAAAoG,cAAA,eA6nD8kF,CAAC;UA7nDjlFpG,EAAE,CAAAqG,UAAA,+BAAAgf,oDAAA;YAAFrlB,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nDyjF1C,GAAA,CAAA+T,iBAAA,CAAkB,CAAC;UAAA,CAAC,CAAC;UA7nDhlFnY,EAAE,CAAAoG,cAAA,eA6nDmoF,CAAC;UA7nDtoFpG,EAAE,CAAAqE,YAAA,EA6nDoqF,CAAC;UA7nDvqFrE,EAAE,CAAAoH,YAAA,CA6nDgrF,CAAC,CAAS,CAAC,CAAO,CAAC;UA7nDrsFpH,EAAE,CAAAoG,cAAA,oBA6nDmzG,CAAC;UA7nDtzGpG,EAAE,CAAAqG,UAAA,uBAAAif,gDAAAre,MAAA;YAAFjH,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nDqrG1C,GAAA,CAAA0Q,qBAAA,CAAsB,OAAO,EAAA7N,MAAQ,CAAC;UAAA,CAAC,CAAC,mBAAAse,4CAAA;YA7nD/tGvlB,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nD8uG1C,GAAA,CAAAyV,qBAAA,CAAsB,OAAO,CAAC;UAAA,CAAC,CAAC,sBAAA2L,+CAAA;YA7nDhxGxlB,EAAE,CAAAwG,aAAA,CAAAyV,GAAA;YAAA,OAAFjc,EAAE,CAAA8G,WAAA,CA6nDkyG1C,GAAA,CAAAuQ,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA7nDrzG3U,EAAE,CAAAmH,SAAA,aA6nDk3G,CAAC;UA7nDr3GnH,EAAE,CAAAoH,YAAA,CA6nD63G,CAAC;QAAA;QAAA,IAAAjD,EAAA;UA7nDh4GnE,EAAE,CAAAwH,WAAA,2CAAApD,GAAA,CAAA+P,oBA6nD8nE,CAAC;UA7nDjoEnU,EAAE,CAAA2F,UAAA,sBAAAvB,GAAA,CAAA+P,oBAAA,IAAA/P,GAAA,CAAAiE,aA6nDgjE,CAAC,aAAAjE,GAAA,CAAA+P,oBAAA,QAA+H,CAAC;UA7nDnrEnU,EAAE,CAAAoI,SAAA,GA6nDwlG,CAAC;UA7nD3lGpI,EAAE,CAAAwH,WAAA,2CAAApD,GAAA,CAAA8P,mBA6nDwlG,CAAC;UA7nD3lGlU,EAAE,CAAA2F,UAAA,sBAAAvB,GAAA,CAAA8P,mBAAA,IAAA9P,GAAA,CAAAiE,aA6nD2gG,CAAC,aAAAjE,GAAA,CAAA8P,mBAAA,QAA6H,CAAC;QAAA;MAAA;MAAAwI,YAAA,GAAokOlb,SAAS,EAAwPkC,iBAAiB;MAAAiZ,MAAA;MAAApO,aAAA;IAAA,EAA0P;EAAE;AACl0V;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA/nDoGzK,EAAE,CAAA0K,iBAAA,CA+nDXuZ,SAAS,EAAc,CAAC;IACvG/Z,IAAI,EAAE3J,SAAS;IACfoK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEkD,QAAQ,EAAE,yBAAyB;MAAEY,IAAI,EAAE;QACvE,aAAa,EAAE,YAAY;QAC3B,OAAO,EAAE,wCAAwC;QACjD,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE,gCAAgC;QAClE,0CAA0C,EAAE,aAAa;QACzD,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,iCAAiC,EAAE,qCAAqC;QACxE,sCAAsC,EAAE;MAC5C,CAAC;MAAEH,aAAa,EAAE9N,iBAAiB,CAACoN,IAAI;MAAEW,eAAe,EAAEhO,uBAAuB,CAACiO,OAAO;MAAErE,UAAU,EAAE,IAAI;MAAEwS,OAAO,EAAE,CAACpb,SAAS,EAAEkC,iBAAiB,CAAC;MAAEiG,QAAQ,EAAE,krDAAkrD;MAAEgT,MAAM,EAAE,CAAC,qxNAAqxN;IAAE,CAAC;EACloR,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzS,IAAI,EAAElK,EAAE,CAACc;EAAW,CAAC,EAAE;IAAEoJ,IAAI,EAAEtH,EAAE,CAACqY,cAAc;IAAEzP,UAAU,EAAE,CAAC;MAChFtB,IAAI,EAAE9J;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAElK,EAAE,CAACkb;EAAO,CAAC,EAAE;IAAEhR,IAAI,EAAElK,EAAE,CAAC+a;EAAkB,CAAC,EAAE;IAAE7Q,IAAI,EAAE3H,EAAE,CAACyY;EAAc,CAAC,EAAE;IAAE9Q,IAAI,EAAEzH,EAAE,CAACE;EAAS,CAAC,EAAE;IAAEuH,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC1ItB,IAAI,EAAE9J;IACV,CAAC,EAAE;MACC8J,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAAC3J,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEkJ,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCtB,IAAI,EAAE9J;IACV,CAAC,EAAE;MACC8J,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAACmS,eAAe;IAC1B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAElV,kBAAkB,EAAE,CAAC;MAC9CsC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEygB,WAAW,EAAE,CAAC;MACd5W,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEoZ,KAAK,EAAE,kBAAkB;QAAEpV,SAAS,EAAEtO;MAAiB,CAAC;IACrE,CAAC,CAAC;IAAEwE,iBAAiB,EAAE,CAAC;MACpBqF,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEuO,MAAM,EAAE,CAAC;MACT/E,IAAI,EAAE/I,eAAe;MACrBwJ,IAAI,EAAE,CAACvJ,UAAU,CAAC,MAAMwjB,UAAU,CAAC,EAAE;QAAE/H,WAAW,EAAE;MAAK,CAAC;IAC9D,CAAC,CAAC;IAAE0D,eAAe,EAAE,CAAC;MAClBrW,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAE2H,aAAa,EAAE,CAAC;MAChB6B,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkjB,KAAK,EAAE,CAAC;MACRrZ,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEyjB,QAAQ,EAAE,CAAC;MACXja,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEoY,iBAAiB,EAAE,CAAC;MACpB5O,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEkE,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEuK,QAAQ,EAAE,CAAC;MACXlP,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEkE,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEqL,aAAa,EAAE,CAAC;MAChBhQ,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEkE,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEkG,cAAc,EAAE,CAAC;MACjB7K,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEkK,kBAAkB,EAAE,CAAC;MACrB3K,IAAI,EAAEtJ,SAAS;MACf+J,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMia,UAAU,SAAS3U,UAAU,CAAC;EAChC;EACA,IAAIoU,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACoB,SAAS;EACzB;EACA,IAAIpB,MAAMA,CAACrf,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACygB,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGzgB,KAAK;MACtB,IAAI,CAAC0gB,UAAU,CAACxB,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIyB,cAAcA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAAChe,QAAQ,IACjB,IAAI,CAACU,aAAa,IAClB,IAAI,CAACqd,UAAU,CAACrd,aAAa,IAC7B,CAAC,CAAC,IAAI,CAACud,YAAY,CAACje,QAAQ;EACpC;EACA+B,WAAWA,CAACgc,UAAU,EACtB,oBAAqBhW,UAAU,EAAEmW,mBAAmB,EAAEjN,QAAQ,EAAEkN,aAAa,EAAErK,aAAa,EAAE;IAC1F,KAAK,CAAC,CAAC;IACP,IAAI,CAACiK,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAChW,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACoW,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC9R,UAAU,GAAG,IAAIlS,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAAC2jB,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAAC9d,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACU,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACuQ,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAAC2L,EAAE,GAAG,gBAAgBP,YAAY,EAAE,EAAE;IAC1C,IAAI,CAAC4B,YAAY,GAAGC,mBAAmB,IAAI,CAAC,CAAC;IAC7C,IAAI,CAACjN,QAAQ,GAAGmN,QAAQ,CAACnN,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI6C,aAAa,KAAK,gBAAgB,EAAE;MACpC,IAAI,CAACmK,YAAY,CAAChG,SAAS,GAAG;QAAEoG,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;IACvE;IACAP,UAAU,CAACzF,mBAAmB,CACzBxL,IAAI,CAACtR,SAAS,CAAC,IAAI,CAAC6Q,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC9M,kBAAkB,IAAI;MACjC,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAChD,CAAC,CAAC;EACN;EACA;EACA2K,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7C,UAAU,CAACC,aAAa,CAAC4C,KAAK,CAAC,CAAC;EACzC;EACAqC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACkR,aAAa,CAACI,OAAO,CAAC,IAAI,CAACxW,UAAU,CAAC;EAC/C;EACAnD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyH,UAAU,CAAC1H,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC0H,UAAU,CAACxH,QAAQ,CAAC,CAAC;IAC1B,KAAK,CAACD,WAAW,CAAC,CAAC;IACnB,IAAI,CAACuZ,aAAa,CAACK,cAAc,CAAC,IAAI,CAACzW,UAAU,CAAC;EACtD;EACA0W,YAAYA,CAAA,EAAG;IACX;IACA;IACA,IAAI,CAACV,UAAU,CAAC3N,UAAU,GAAG,IAAI,CAAC2N,UAAU,CAACzW,MAAM,CAAC0J,OAAO,CAAC,CAAC,CAAC0N,OAAO,CAAC,IAAI,CAAC;EAC/E;EACAzO,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACC,OAAO,KAAK7U,KAAK,IAAI4U,KAAK,CAACC,OAAO,KAAK5U,KAAK,EAAE;MACpD,IAAI,IAAI,CAACyE,QAAQ,EAAE;QACfkQ,KAAK,CAAC6D,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI,IAAI,IAAI,CAACgK,UAAU,CAACvB,QAAQ,EAAE;QAC/B;QACA;QACA,IAAItM,KAAK,CAACC,OAAO,KAAK7U,KAAK,EAAE;UACzB4U,KAAK,CAAC6D,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,CAAChM,UAAU,CAACC,aAAa,CAAC2W,KAAK,CAAC,CAAC;MACzC;IACJ;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACb,UAAU,CAACvB,QAAQ,GACzB,IAAI,CAACuB,UAAU,CAACvB,QAAQ,EAAEI,EAAE,GAC5B,IAAI,CAAC7U,UAAU,CAACC,aAAa,CAAC8U,YAAY,CAAC,eAAe,CAAC;EACrE;EACA+B,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACd,UAAU,CAACvB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACE,MAAM,GAAG,MAAM,GAAG,OAAO;IACzC,CAAC,MACI;MACD,OAAO,IAAI,CAAC3U,UAAU,CAACC,aAAa,CAAC8U,YAAY,CAAC,eAAe,CAAC;IACtE;EACJ;EACAgC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpC,MAAM,IAAI,CAAC,IAAI,CAACqB,UAAU,CAACvB,QAAQ,GAAG,MAAM,GAAG,IAAI;EACnE;EACAK,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACkB,UAAU,CAACvB,QAAQ,GAAG,KAAK,GAAG,IAAI,CAACzU,UAAU,CAACC,aAAa,CAAC8U,YAAY,CAAC,MAAM,CAAC;EAChG;EACA3c,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC4d,UAAU,CAACvB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACsB,SAAS,IAAI,CAAC,IAAI,CAAC9d,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,MACI;MACD,OAAO,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAACiR,QAAQ;IAC7C;EACJ;EACA;IAAS,IAAI,CAAChP,IAAI,YAAA8c,mBAAA5c,CAAA;MAAA,YAAAA,CAAA,IAAwF8a,UAAU,EA5yDpB5kB,EAAE,CAAA+J,iBAAA,CA4yDoCka,SAAS,GA5yD/CjkB,EAAE,CAAA+J,iBAAA,CA4yD0D/J,EAAE,CAACc,UAAU,GA5yDzEd,EAAE,CAAA+J,iBAAA,CA4yDoFtI,yBAAyB,MA5yD/GzB,EAAE,CAAA2mB,iBAAA,CA4yD0I,UAAU,GA5yDtJ3mB,EAAE,CAAA+J,iBAAA,CA4yDkLlH,EAAE,CAAC+jB,YAAY,GA5yDnM5mB,EAAE,CAAA+J,iBAAA,CA4yD8M/I,qBAAqB;IAAA,CAA4D;EAAE;EACnY;IAAS,IAAI,CAAC4L,IAAI,kBA7yD8E5M,EAAE,CAAA6M,iBAAA;MAAA3C,IAAA,EA6yDJ0a,UAAU;MAAAza,SAAA;MAAAsD,SAAA;MAAAkF,QAAA;MAAAC,YAAA,WAAAiU,wBAAA1iB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7yDRnE,EAAE,CAAAqG,UAAA,mBAAAygB,oCAAA;YAAA,OA6yDJ1iB,GAAA,CAAAgiB,YAAA,CAAa,CAAC;UAAA,CAAL,CAAC,qBAAAW,sCAAA9f,MAAA;YAAA,OAAV7C,GAAA,CAAAwT,cAAA,CAAA3Q,MAAqB,CAAC;UAAA,CAAb,CAAC;QAAA;QAAA,IAAA9C,EAAA;UA7yDRnE,EAAE,CAAA6H,WAAA,kBA6yDJzD,GAAA,CAAAmiB,gBAAA,CAAiB,CAAC,kBAAlBniB,GAAA,CAAAqiB,eAAA,CAAgB,CAAC,mBAAAriB,GAAA,CAAAuD,QAAA,mBAAjBvD,GAAA,CAAAoiB,gBAAA,CAAiB,CAAC,QAAApiB,GAAA,CAAAmgB,EAAA,cAAlBngB,GAAA,CAAA0D,YAAA,CAAa,CAAC,UAAd1D,GAAA,CAAAogB,QAAA,CAAS,CAAC;UA7yDRxkB,EAAE,CAAAwH,WAAA,yBAAApD,GAAA,CAAAuD,QA6yDK,CAAC,oBAAAvD,GAAA,CAAAigB,MAAD,CAAC;QAAA;MAAA;MAAA3W,MAAA;QAAA2W,MAAA,GA7yDRrkB,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,sBA6yD6GvN,gBAAgB;QAAAsH,QAAA,GA7yD/H3H,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,0BA6yDmKvN,gBAAgB;QAAAgI,aAAA,GA7yDrLrI,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,oCA6yDwOvN,gBAAgB;QAAAuY,QAAA,GA7yD1P5Y,EAAE,CAAA2N,YAAA,CAAAC,0BAAA,0BA6yD+R5I,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG/D,eAAe,CAAC+D,KAAK,CAAE;QAAAuf,EAAA;MAAA;MAAAzW,QAAA;MAAA1D,UAAA;MAAAC,QAAA,GA7yDvVrK,EAAE,CAAA+N,wBAAA,EAAF/N,EAAE,CAAAsL,0BAAA,EAAFtL,EAAE,CAAAiO,mBAAA;MAAA8W,KAAA,EAAAxb,IAAA;MAAA2E,kBAAA,EAAAjK,GAAA;MAAAkK,KAAA;MAAAC,IAAA;MAAA2N,MAAA;MAAApS,QAAA,WAAAqd,oBAAA7iB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAmH,SAAA,aA6yDi8B,CAAC,YAAyJ,CAAC;UA7yD9lCnH,EAAE,CAAAoG,cAAA,aA6yDgoC,CAAC,aAAuC,CAAC;UA7yD3qCpG,EAAE,CAAAqE,YAAA,EA6yDusC,CAAC;UA7yD1sCrE,EAAE,CAAAoH,YAAA,CA6yDktC,CAAC,CAAQ,CAAC;QAAA;QAAA,IAAAjD,EAAA;UA7yD9tCnE,EAAE,CAAAoI,SAAA,CA6yD0iC,CAAC;UA7yD7iCpI,EAAE,CAAA2F,UAAA,qBAAAvB,GAAA,CAAAsL,UAAA,CAAAC,aA6yD0iC,CAAC,sBAAAvL,GAAA,CAAAuhB,cAAyC,CAAC;QAAA;MAAA;MAAAjJ,YAAA,GAA4pIlb,SAAS;MAAAmb,MAAA;MAAApO,aAAA;MAAAC,eAAA;IAAA,EAA6T;EAAE;AAC/pL;AACA;EAAA,QAAA/D,SAAA,oBAAAA,SAAA,KA/yDoGzK,EAAE,CAAA0K,iBAAA,CA+yDXka,UAAU,EAAc,CAAC;IACxG1a,IAAI,EAAE3J,SAAS;IACfoK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAEkD,QAAQ,EAAE,YAAY;MAAEU,eAAe,EAAEhO,uBAAuB,CAACymB,MAAM;MAAE1Y,aAAa,EAAE9N,iBAAiB,CAACoN,IAAI;MAAEa,IAAI,EAAE;QAC7J,OAAO,EAAE,kDAAkD;QAC3D,sBAAsB,EAAE,oBAAoB;QAC5C,qBAAqB,EAAE,mBAAmB;QAC1C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,oBAAoB;QAC5C,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,gBAAgB;QACnC,aAAa,EAAE,YAAY;QAC3B,8BAA8B,EAAE,UAAU;QAC1C,yBAAyB,EAAE,QAAQ;QACnC,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE;MACjB,CAAC;MAAEtE,UAAU,EAAE,IAAI;MAAEwS,OAAO,EAAE,CAACpb,SAAS,CAAC;MAAEmI,QAAQ,EAAE,uUAAuU;MAAEgT,MAAM,EAAE,CAAC,w9HAAw9H;IAAE,CAAC;EAC92I,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzS,IAAI,EAAE+Z;EAAU,CAAC,EAAE;IAAE/Z,IAAI,EAAElK,EAAE,CAACc;EAAW,CAAC,EAAE;IAAEoJ,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7FtB,IAAI,EAAE9J;IACV,CAAC,EAAE;MACC8J,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAAClJ,yBAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAEyI,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCtB,IAAI,EAAE5I,SAAS;MACfqJ,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAET,IAAI,EAAErH,EAAE,CAAC+jB;EAAa,CAAC,EAAE;IAAE1c,IAAI,EAAEqB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7DtB,IAAI,EAAE9J;IACV,CAAC,EAAE;MACC8J,IAAI,EAAE/J,MAAM;MACZwK,IAAI,EAAE,CAAC3J,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEqjB,MAAM,EAAE,CAAC;MAClCna,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsH,QAAQ,EAAE,CAAC;MACXuC,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgI,aAAa,EAAE,CAAC;MAChB6B,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QAAEgE,SAAS,EAAEtO;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuY,QAAQ,EAAE,CAAC;MACX1O,IAAI,EAAExJ,KAAK;MACXiK,IAAI,EAAE,CAAC;QACCgE,SAAS,EAAG3J,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG/D,eAAe,CAAC+D,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEuf,EAAE,EAAE,CAAC;MACLra,IAAI,EAAExJ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMwmB,cAAc,CAAC;EACjBxd,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC6a,EAAE,GAAG,qBAAqBP,YAAY,EAAE,EAAE;EACnD;EACA;IAAS,IAAI,CAACpa,IAAI,YAAAud,uBAAArd,CAAA;MAAA,YAAAA,CAAA,IAAwFod,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACta,IAAI,kBAt2D8E5M,EAAE,CAAA6M,iBAAA;MAAA3C,IAAA,EAs2DJgd,cAAc;MAAA/c,SAAA;MAAAsD,SAAA,WAAyG,UAAU;MAAAkF,QAAA;MAAAC,YAAA,WAAAwU,4BAAAjjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAt2D/HnE,EAAE,CAAA6H,WAAA,oBAAAzD,GAAA,CAAAkgB,YAAA,QAAAlgB,GAAA,CAAAmgB,EAAA;QAAA;MAAA;MAAA7W,MAAA;QAAA6W,EAAA;MAAA;MAAAzW,QAAA;MAAA1D,UAAA;MAAAC,QAAA,GAAFrK,EAAE,CAAAiO,mBAAA;MAAAC,kBAAA,EAAAjK,GAAA;MAAAkK,KAAA;MAAAC,IAAA;MAAAzE,QAAA,WAAA0d,wBAAAljB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnE,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAqE,YAAA,EAs2DoU,CAAC;QAAA;MAAA;MAAAkK,aAAA;MAAAC,eAAA;IAAA,EAAkH;EAAE;AAC/hB;AACA;EAAA,QAAA/D,SAAA,oBAAAA,SAAA,KAx2DoGzK,EAAE,CAAA0K,iBAAA,CAw2DXwc,cAAc,EAAc,CAAC;IAC5Ghd,IAAI,EAAE3J,SAAS;IACfoK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BkD,QAAQ,EAAE,gBAAgB;MAC1BnE,QAAQ,EAAE,2BAA2B;MACrC+E,IAAI,EAAE;QACF,wBAAwB,EAAE,cAAc;QACxC,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,uBAAuB;QAChC,MAAM,EAAE;MACZ,CAAC;MACDH,aAAa,EAAE9N,iBAAiB,CAACoN,IAAI;MACrCW,eAAe,EAAEhO,uBAAuB,CAACymB,MAAM;MAC/C7c,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEma,EAAE,EAAE,CAAC;MACnBra,IAAI,EAAExJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4mB,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC1d,IAAI,YAAA2d,sBAAAzd,CAAA;MAAA,YAAAA,CAAA,IAAwFwd,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBA93D8ExnB,EAAE,CAAAynB,gBAAA;MAAAvd,IAAA,EA83DSod;IAAa,EAclG;EAAE;EACxB;IAAS,IAAI,CAACI,IAAI,kBA74D8E1nB,EAAE,CAAA2nB,gBAAA;MAAA/K,OAAA,GA64DkClb,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC9K;AACA;EAAA,QAAA+I,SAAA,oBAAAA,SAAA,KA/4DoGzK,EAAE,CAAA0K,iBAAA,CA+4DX4c,aAAa,EAAc,CAAC;IAC3Gpd,IAAI,EAAE3I,QAAQ;IACdoJ,IAAI,EAAE,CAAC;MACCiS,OAAO,EAAE,CACLlb,eAAe,EACf+H,aAAa,EACbuB,WAAW,EACXU,MAAM,EACNsU,WAAW,EACXiE,SAAS,EACTiD,cAAc,EACdtC,UAAU,CACb;MACDgD,OAAO,EAAE,CACLlmB,eAAe,EACf+H,aAAa,EACbuB,WAAW,EACXU,MAAM,EACNsU,WAAW,EACXiE,SAAS,EACTiD,cAAc,EACdtC,UAAU;IAElB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS7Z,OAAO,EAAE+R,eAAe,EAAEtT,eAAe,EAAEiC,aAAa,EAAEX,aAAa,EAAEkE,SAAS,EAAEkE,qBAAqB,EAAExH,MAAM,EAAE2S,UAAU,EAAElB,gBAAgB,EAAEyF,iBAAiB,EAAEnZ,aAAa,EAAEuW,WAAW,EAAE5E,YAAY,EAAEpQ,WAAW,EAAEsH,kBAAkB,EAAEsS,UAAU,EAAEX,SAAS,EAAEiD,cAAc,EAAEI,aAAa,EAAEnV,uBAAuB,EAAEJ,+BAA+B,EAAEgL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}