{"ast": null, "code": "import { BehaviorSubject, Observable, tap } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = environment.apiUrl;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n  initializeAuth() {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: response => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // If backend is not running, use mock user for demo\n          this.setMockUser();\n        }\n      });\n    } else {\n      // For demo purposes, set a mock user\n      this.setMockUser();\n    }\n  }\n  setMockUser() {\n    const mockUser = {\n      _id: '1',\n      username: 'fashionista_maya',\n      email: '<EMAIL>',\n      fullName: 'Maya Sharma',\n      avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',\n      bio: 'Fashion enthusiast and style blogger',\n      role: 'customer',\n      isVerified: true,\n      isActive: true,\n      followers: ['2', '3'],\n      following: ['2', '4'],\n      socialStats: {\n        postsCount: 12,\n        followersCount: 1250,\n        followingCount: 890\n      },\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n    this.currentUserSubject.next(mockUser);\n    this.isAuthenticatedSubject.next(true);\n    this.setToken('mock-token-for-demo');\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/auth/login`, credentials).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n    }), catchError(error => {\n      console.log('Backend not available, using demo login');\n      // If backend is not available, simulate login for demo\n      return this.simulateLogin(credentials);\n    }));\n  }\n  simulateLogin(credentials) {\n    // Demo accounts\n    const demoAccounts = [{\n      email: '<EMAIL>',\n      password: 'password123',\n      user: {\n        _id: '1',\n        username: 'fashionista_maya',\n        email: '<EMAIL>',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',\n        bio: 'Fashion enthusiast and style blogger',\n        role: 'customer',\n        isVerified: true,\n        isActive: true,\n        followers: ['2', '3'],\n        following: ['2', '4'],\n        socialStats: {\n          postsCount: 12,\n          followersCount: 1250,\n          followingCount: 890\n        },\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n    }, {\n      email: '<EMAIL>',\n      password: 'password123',\n      user: {\n        _id: '2',\n        username: 'style_guru_raj',\n        email: '<EMAIL>',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150',\n        bio: 'Style guru and fashion vendor',\n        role: 'vendor',\n        isVerified: true,\n        isActive: true,\n        followers: ['1'],\n        following: ['3', '4'],\n        socialStats: {\n          postsCount: 45,\n          followersCount: 5600,\n          followingCount: 234\n        },\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n    }, {\n      email: '<EMAIL>',\n      password: 'admin123',\n      user: {\n        _id: '3',\n        username: 'admin',\n        email: '<EMAIL>',\n        fullName: 'DFashion Admin',\n        avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150',\n        bio: 'Platform administrator',\n        role: 'admin',\n        isVerified: true,\n        isActive: true,\n        followers: [],\n        following: [],\n        socialStats: {\n          postsCount: 0,\n          followersCount: 0,\n          followingCount: 0\n        },\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n    }];\n    const account = demoAccounts.find(acc => acc.email === credentials.email && acc.password === credentials.password);\n    if (account) {\n      const response = {\n        message: 'Demo login successful',\n        token: 'demo-token-' + account.user._id,\n        user: account.user\n      };\n      // Simulate network delay\n      return new Observable(observer => {\n        setTimeout(() => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n          observer.next(response);\n          observer.complete();\n        }, 1000);\n      });\n    } else {\n      return throwError(() => ({\n        error: {\n          message: 'Invalid demo credentials. Try: <EMAIL> / password123'\n        }\n      }));\n    }\n  }\n  register(userData) {\n    return this.http.post(`${this.API_URL}/auth/register`, userData).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n    }));\n  }\n  logout() {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/auth/me`);\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  setToken(token) {\n    localStorage.setItem('token', token);\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  get isAuthenticated() {\n    return this.isAuthenticatedSubject.value;\n  }\n  isAdmin() {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n  isVendor() {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n  isCustomer() {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "tap", "environment", "AuthService", "constructor", "http", "router", "API_URL", "apiUrl", "currentUserSubject", "isAuthenticatedSubject", "currentUser$", "asObservable", "isAuthenticated$", "initializeAuth", "token", "getToken", "getCurrentUser", "subscribe", "next", "response", "user", "error", "setMockUser", "mockUser", "_id", "username", "email", "fullName", "avatar", "bio", "role", "isVerified", "isActive", "followers", "following", "socialStats", "postsCount", "followersCount", "followingCount", "createdAt", "Date", "updatedAt", "setToken", "login", "credentials", "post", "pipe", "catchError", "console", "log", "simulateLogin", "demoAccounts", "password", "account", "find", "acc", "message", "observer", "setTimeout", "complete", "throwError", "register", "userData", "logout", "localStorage", "removeItem", "navigate", "get", "getItem", "setItem", "currentUserValue", "value", "isAuthenticated", "isAdmin", "isVendor", "isCustomer", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\nimport { Router } from '@angular/router';\n\nimport { User, LoginRequest, RegisterRequest, AuthResponse } from '../models/user.model';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = environment.apiUrl;\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {}\n\n  initializeAuth(): void {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: (response) => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // If backend is not running, use mock user for demo\n          this.setMockUser();\n        }\n      });\n    } else {\n      // For demo purposes, set a mock user\n      this.setMockUser();\n    }\n  }\n\n  private setMockUser(): void {\n    const mockUser: User = {\n      _id: '1',\n      username: 'fashionista_maya',\n      email: '<EMAIL>',\n      fullName: 'Maya Sharma',\n      avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',\n      bio: 'Fashion enthusiast and style blogger',\n      role: 'customer',\n      isVerified: true,\n      isActive: true,\n      followers: ['2', '3'],\n      following: ['2', '4'],\n      socialStats: {\n        postsCount: 12,\n        followersCount: 1250,\n        followingCount: 890\n      },\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n\n    this.currentUserSubject.next(mockUser);\n    this.isAuthenticatedSubject.next(true);\n    this.setToken('mock-token-for-demo');\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        }),\n        catchError(error => {\n          console.log('Backend not available, using demo login');\n          // If backend is not available, simulate login for demo\n          return this.simulateLogin(credentials);\n        })\n      );\n  }\n\n  private simulateLogin(credentials: LoginRequest): Observable<AuthResponse> {\n    // Demo accounts\n    const demoAccounts = [\n      {\n        email: '<EMAIL>',\n        password: 'password123',\n        user: {\n          _id: '1',\n          username: 'fashionista_maya',\n          email: '<EMAIL>',\n          fullName: 'Maya Sharma',\n          avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',\n          bio: 'Fashion enthusiast and style blogger',\n          role: 'customer' as const,\n          isVerified: true,\n          isActive: true,\n          followers: ['2', '3'],\n          following: ['2', '4'],\n          socialStats: {\n            postsCount: 12,\n            followersCount: 1250,\n            followingCount: 890\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }\n      },\n      {\n        email: '<EMAIL>',\n        password: 'password123',\n        user: {\n          _id: '2',\n          username: 'style_guru_raj',\n          email: '<EMAIL>',\n          fullName: 'Raj Patel',\n          avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150',\n          bio: 'Style guru and fashion vendor',\n          role: 'vendor' as const,\n          isVerified: true,\n          isActive: true,\n          followers: ['1'],\n          following: ['3', '4'],\n          socialStats: {\n            postsCount: 45,\n            followersCount: 5600,\n            followingCount: 234\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }\n      },\n      {\n        email: '<EMAIL>',\n        password: 'admin123',\n        user: {\n          _id: '3',\n          username: 'admin',\n          email: '<EMAIL>',\n          fullName: 'DFashion Admin',\n          avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150',\n          bio: 'Platform administrator',\n          role: 'admin' as const,\n          isVerified: true,\n          isActive: true,\n          followers: [],\n          following: [],\n          socialStats: {\n            postsCount: 0,\n            followersCount: 0,\n            followingCount: 0\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }\n      }\n    ];\n\n    const account = demoAccounts.find(acc =>\n      acc.email === credentials.email && acc.password === credentials.password\n    );\n\n    if (account) {\n      const response: AuthResponse = {\n        message: 'Demo login successful',\n        token: 'demo-token-' + account.user._id,\n        user: account.user\n      };\n\n      // Simulate network delay\n      return new Observable(observer => {\n        setTimeout(() => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n          observer.next(response);\n          observer.complete();\n        }, 1000);\n      });\n    } else {\n      return throwError(() => ({\n        error: { message: 'Invalid demo credentials. Try: <EMAIL> / password123' }\n      }));\n    }\n  }\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/register`, userData)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        })\n      );\n  }\n\n  logout(): void {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n\n  getCurrentUser(): Observable<{ user: User }> {\n    return this.http.get<{ user: User }>(`${this.API_URL}/auth/me`);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  private setToken(token: string): void {\n    localStorage.setItem('token', token);\n  }\n\n  get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  get isAuthenticated(): boolean {\n    return this.isAuthenticatedSubject.value;\n  }\n\n  isAdmin(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n\n  isVendor(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n\n  isCustomer(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;AAIvD,SAASC,WAAW,QAAQ,mCAAmC;;;;AAK/D,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM;IACrC,KAAAC,kBAAkB,GAAG,IAAIV,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAW,sBAAsB,GAAG,IAAIX,eAAe,CAAU,KAAK,CAAC;IAE7D,KAAAY,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACE,YAAY,EAAE;EAKjE;EAEHE,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,cAAc,EAAE,CAACC,SAAS,CAAC;QAC9BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACX,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;UAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QACxC,CAAC;QACDG,KAAK,EAAEA,CAAA,KAAK;UACV;UACA,IAAI,CAACC,WAAW,EAAE;QACpB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEQA,WAAWA,CAAA;IACjB,MAAMC,QAAQ,GAAS;MACrBC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,kBAAkB;MAC5BC,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE,aAAa;MACvBC,MAAM,EAAE,oEAAoE;MAC5EC,GAAG,EAAE,sCAAsC;MAC3CC,IAAI,EAAE,UAAU;MAChBC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACrBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACrBC,WAAW,EAAE;QACXC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,IAAI;QACpBC,cAAc,EAAE;OACjB;MACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,SAAS,EAAE,IAAID,IAAI;KACpB;IAED,IAAI,CAAChC,kBAAkB,CAACU,IAAI,CAACK,QAAQ,CAAC;IACtC,IAAI,CAACd,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACwB,QAAQ,CAAC,qBAAqB,CAAC;EACtC;EAEAC,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACxC,IAAI,CAACyC,IAAI,CAAe,GAAG,IAAI,CAACvC,OAAO,aAAa,EAAEsC,WAAW,CAAC,CAC3EE,IAAI,CACH9C,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAI,CAACuB,QAAQ,CAACvB,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,EACF6B,UAAU,CAAC1B,KAAK,IAAG;MACjB2B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD;MACA,OAAO,IAAI,CAACC,aAAa,CAACN,WAAW,CAAC;IACxC,CAAC,CAAC,CACH;EACL;EAEQM,aAAaA,CAACN,WAAyB;IAC7C;IACA,MAAMO,YAAY,GAAG,CACnB;MACEzB,KAAK,EAAE,kBAAkB;MACzB0B,QAAQ,EAAE,aAAa;MACvBhC,IAAI,EAAE;QACJI,GAAG,EAAE,GAAG;QACRC,QAAQ,EAAE,kBAAkB;QAC5BC,KAAK,EAAE,kBAAkB;QACzBC,QAAQ,EAAE,aAAa;QACvBC,MAAM,EAAE,oEAAoE;QAC5EC,GAAG,EAAE,sCAAsC;QAC3CC,IAAI,EAAE,UAAmB;QACzBC,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACrBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACrBC,WAAW,EAAE;UACXC,UAAU,EAAE,EAAE;UACdC,cAAc,EAAE,IAAI;UACpBC,cAAc,EAAE;SACjB;QACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,IAAID,IAAI;;KAEtB,EACD;MACEd,KAAK,EAAE,iBAAiB;MACxB0B,QAAQ,EAAE,aAAa;MACvBhC,IAAI,EAAE;QACJI,GAAG,EAAE,GAAG;QACRC,QAAQ,EAAE,gBAAgB;QAC1BC,KAAK,EAAE,iBAAiB;QACxBC,QAAQ,EAAE,WAAW;QACrBC,MAAM,EAAE,oEAAoE;QAC5EC,GAAG,EAAE,+BAA+B;QACpCC,IAAI,EAAE,QAAiB;QACvBC,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,CAAC,GAAG,CAAC;QAChBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACrBC,WAAW,EAAE;UACXC,UAAU,EAAE,EAAE;UACdC,cAAc,EAAE,IAAI;UACpBC,cAAc,EAAE;SACjB;QACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,IAAID,IAAI;;KAEtB,EACD;MACEd,KAAK,EAAE,oBAAoB;MAC3B0B,QAAQ,EAAE,UAAU;MACpBhC,IAAI,EAAE;QACJI,GAAG,EAAE,GAAG;QACRC,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,oBAAoB;QAC3BC,QAAQ,EAAE,gBAAgB;QAC1BC,MAAM,EAAE,oEAAoE;QAC5EC,GAAG,EAAE,wBAAwB;QAC7BC,IAAI,EAAE,OAAgB;QACtBC,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE;UACXC,UAAU,EAAE,CAAC;UACbC,cAAc,EAAE,CAAC;UACjBC,cAAc,EAAE;SACjB;QACDC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,IAAID,IAAI;;KAEtB,CACF;IAED,MAAMa,OAAO,GAAGF,YAAY,CAACG,IAAI,CAACC,GAAG,IACnCA,GAAG,CAAC7B,KAAK,KAAKkB,WAAW,CAAClB,KAAK,IAAI6B,GAAG,CAACH,QAAQ,KAAKR,WAAW,CAACQ,QAAQ,CACzE;IAED,IAAIC,OAAO,EAAE;MACX,MAAMlC,QAAQ,GAAiB;QAC7BqC,OAAO,EAAE,uBAAuB;QAChC1C,KAAK,EAAE,aAAa,GAAGuC,OAAO,CAACjC,IAAI,CAACI,GAAG;QACvCJ,IAAI,EAAEiC,OAAO,CAACjC;OACf;MAED;MACA,OAAO,IAAIrB,UAAU,CAAC0D,QAAQ,IAAG;QAC/BC,UAAU,CAAC,MAAK;UACd,IAAI,CAAChB,QAAQ,CAACvB,QAAQ,CAACL,KAAK,CAAC;UAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;UAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;UACtCuC,QAAQ,CAACvC,IAAI,CAACC,QAAQ,CAAC;UACvBsC,QAAQ,CAACE,QAAQ,EAAE;QACrB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;KACH,MAAM;MACL,OAAOC,UAAU,CAAC,OAAO;QACvBvC,KAAK,EAAE;UAAEmC,OAAO,EAAE;QAA+D;OAClF,CAAC,CAAC;;EAEP;EAEAK,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAAC1D,IAAI,CAACyC,IAAI,CAAe,GAAG,IAAI,CAACvC,OAAO,gBAAgB,EAAEwD,QAAQ,CAAC,CAC3EhB,IAAI,CACH9C,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAI,CAACuB,QAAQ,CAACvB,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,CACH;EACL;EAEA6C,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAACzD,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACb,MAAM,CAAC6D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAlD,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACZ,IAAI,CAAC+D,GAAG,CAAiB,GAAG,IAAI,CAAC7D,OAAO,UAAU,CAAC;EACjE;EAEAS,QAAQA,CAAA;IACN,OAAOiD,YAAY,CAACI,OAAO,CAAC,OAAO,CAAC;EACtC;EAEQ1B,QAAQA,CAAC5B,KAAa;IAC5BkD,YAAY,CAACK,OAAO,CAAC,OAAO,EAAEvD,KAAK,CAAC;EACtC;EAEA,IAAIwD,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC9D,kBAAkB,CAAC+D,KAAK;EACtC;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC/D,sBAAsB,CAAC8D,KAAK;EAC1C;EAEAE,OAAOA,CAAA;IACL,MAAMrD,IAAI,GAAG,IAAI,CAACkD,gBAAgB;IAClC,OAAOlD,IAAI,EAAEU,IAAI,KAAK,OAAO;EAC/B;EAEA4C,QAAQA,CAAA;IACN,MAAMtD,IAAI,GAAG,IAAI,CAACkD,gBAAgB;IAClC,OAAOlD,IAAI,EAAEU,IAAI,KAAK,QAAQ;EAChC;EAEA6C,UAAUA,CAAA;IACR,MAAMvD,IAAI,GAAG,IAAI,CAACkD,gBAAgB;IAClC,OAAOlD,IAAI,EAAEU,IAAI,KAAK,UAAU;EAClC;;;uBAvOW5B,WAAW,EAAA0E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX/E,WAAW;MAAAgF,OAAA,EAAXhF,WAAW,CAAAiF,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}