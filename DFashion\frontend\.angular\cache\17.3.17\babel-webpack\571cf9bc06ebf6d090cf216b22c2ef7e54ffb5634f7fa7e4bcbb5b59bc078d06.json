{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction SuggestedProductsComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵelement(3, \"div\", 17)(4, \"div\", 18)(5, \"div\", 17);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SuggestedProductsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵtemplate(2, SuggestedProductsComponent_div_8_div_2_Template, 6, 0, \"div\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SuggestedProductsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"i\", 21);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Unable to load suggested products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_9_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.retry());\n    });\n    i0.ɵɵelement(8, \"i\", 23);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction SuggestedProductsComponent_div_10_swiper_slide_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" -\", ctx_r1.getDiscountPercentage(product_r4), \"% \");\n  }\n}\nfunction SuggestedProductsComponent_div_10_swiper_slide_2_i_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction SuggestedProductsComponent_div_10_swiper_slide_2_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatPrice(product_r4.originalPrice), \" \");\n  }\n}\nfunction SuggestedProductsComponent_div_10_swiper_slide_2_div_24_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 3);\n  }\n  if (rf & 2) {\n    const star_r5 = ctx.$implicit;\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r5 <= product_r4.rating.average);\n  }\n}\nfunction SuggestedProductsComponent_div_10_swiper_slide_2_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵtemplate(2, SuggestedProductsComponent_div_10_swiper_slide_2_div_24_i_2_Template, 1, 2, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 59);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(3, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", product_r4.rating.average, \" (\", ctx_r1.formatNumber(product_r4.rating.count), \") \");\n  }\n}\nfunction SuggestedProductsComponent_div_10_swiper_slide_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"swiper-slide\")(1, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_10_swiper_slide_2_Template_div_click_1_listener() {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r4));\n    });\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelement(3, \"img\", 29);\n    i0.ɵɵtemplate(4, SuggestedProductsComponent_div_10_swiper_slide_2_div_4_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementStart(5, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_10_swiper_slide_2_Template_button_click_5_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleWishlist(product_r4, $event));\n    });\n    i0.ɵɵelement(6, \"i\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 33)(8, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_10_swiper_slide_2_Template_button_click_8_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r4, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_10_swiper_slide_2_Template_button_click_10_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 38)(13, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_10_swiper_slide_2_Template_div_click_13_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.viewVendor(product_r4.vendor);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(14, \"img\", 40);\n    i0.ɵɵelementStart(15, \"span\", 41);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, SuggestedProductsComponent_div_10_swiper_slide_2_i_17_Template, 1, 0, \"i\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h3\", 43);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 44)(21, \"span\", 45);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, SuggestedProductsComponent_div_10_swiper_slide_2_span_23_Template, 2, 1, \"span\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SuggestedProductsComponent_div_10_swiper_slide_2_div_24_Template, 5, 4, \"div\", 47);\n    i0.ɵɵelementStart(25, \"div\", 48)(26, \"div\", 49);\n    i0.ɵɵelement(27, \"i\", 50);\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 49);\n    i0.ɵɵelement(31, \"i\", 51);\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 49);\n    i0.ɵɵelement(35, \"i\", 52);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", product_r4.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r4) > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"src\", product_r4.vendor.avatar, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.vendor.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.vendor.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.vendor.isInfluencer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.rating);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.formatNumber(product_r4.analytics.views));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatNumber(product_r4.analytics.likes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatNumber(product_r4.analytics.purchases));\n  }\n}\nfunction SuggestedProductsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"swiper-container\", 25);\n    i0.ɵɵtemplate(2, SuggestedProductsComponent_div_10_swiper_slide_2_Template, 38, 14, \"swiper-slide\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"space-between\", ctx_r1.swiperConfig.spaceBetween)(\"navigation\", ctx_r1.swiperConfig.navigation)(\"autoplay\", ctx_r1.swiperConfig.autoplay)(\"loop\", ctx_r1.swiperConfig.loop)(\"free-mode\", true)(\"breakpoints\", ctx_r1.swiperConfig.breakpoints);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.products)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction SuggestedProductsComponent_div_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Load More Products\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuggestedProductsComponent_div_11_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵtext(2, \" Loading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuggestedProductsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadMore());\n    });\n    i0.ɵɵtemplate(2, SuggestedProductsComponent_div_11_span_2_Template, 2, 0, \"span\", 62)(3, SuggestedProductsComponent_div_11_span_3_Template, 3, 0, \"span\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n  }\n}\nfunction SuggestedProductsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No suggested products available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Check back later for personalized product recommendations!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.router.navigate([\"/shop\"]));\n    });\n    i0.ɵɵtext(8, \" Browse All Products \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class SuggestedProductsComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.products = [];\n    this.isLoading = true;\n    this.error = null;\n    this.currentPage = 1;\n    this.totalPages = 1;\n    this.hasMore = false;\n    // Swiper configuration for products slider\n    this.swiperConfig = {\n      slidesPerView: 'auto',\n      spaceBetween: 20,\n      navigation: true,\n      autoplay: {\n        delay: 4000,\n        disableOnInteraction: false,\n        pauseOnMouseEnter: true\n      },\n      loop: false,\n      freeMode: true,\n      breakpoints: {\n        0: {\n          slidesPerView: 'auto',\n          spaceBetween: 12,\n          freeMode: true\n        },\n        480: {\n          slidesPerView: 'auto',\n          spaceBetween: 16,\n          freeMode: true\n        },\n        768: {\n          slidesPerView: 'auto',\n          spaceBetween: 18,\n          freeMode: false\n        },\n        1024: {\n          slidesPerView: 'auto',\n          spaceBetween: 20,\n          freeMode: false\n        }\n      }\n    };\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.loadSuggestedProducts();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadSuggestedProducts(page = 1) {\n    this.isLoading = true;\n    this.error = null;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/products/suggested?page=${page}&limit=12`).subscribe({\n      next: response => {\n        if (response.success) {\n          if (page === 1) {\n            this.products = response.products;\n          } else {\n            this.products = [...this.products, ...response.products];\n          }\n          this.currentPage = response.pagination.page;\n          this.totalPages = response.pagination.pages;\n          this.hasMore = this.currentPage < this.totalPages;\n        } else {\n          this.loadFallbackProducts();\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading suggested products:', error);\n        if (page === 1) {\n          this.loadFallbackProducts();\n        }\n        this.error = 'Failed to load suggested products';\n        this.isLoading = false;\n      }\n    }));\n  }\n  loadFallbackProducts() {\n    this.products = [{\n      _id: '1',\n      name: 'Vintage Denim Jacket',\n      price: 89.99,\n      originalPrice: 129.99,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400',\n        isPrimary: true\n      }],\n      vendor: {\n        _id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',\n        isInfluencer: true\n      },\n      analytics: {\n        views: 15420,\n        likes: 892,\n        shares: 156,\n        purchases: 234\n      },\n      rating: {\n        average: 4.2,\n        count: 156\n      }\n    }, {\n      _id: '2',\n      name: 'Silk Slip Dress',\n      price: 159.99,\n      originalPrice: 199.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400',\n        isPrimary: true\n      }],\n      vendor: {\n        _id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Chen',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        isInfluencer: true\n      },\n      analytics: {\n        views: 12890,\n        likes: 1205,\n        shares: 289,\n        purchases: 167\n      },\n      rating: {\n        average: 4.5,\n        count: 89\n      }\n    }, {\n      _id: '3',\n      name: 'Chunky Knit Sweater',\n      price: 79.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400',\n        isPrimary: true\n      }],\n      vendor: {\n        _id: '3',\n        username: 'beauty_by_sarah',\n        fullName: 'Sarah Johnson',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        isInfluencer: true\n      },\n      analytics: {\n        views: 8945,\n        likes: 567,\n        shares: 89,\n        purchases: 123\n      },\n      rating: {\n        average: 4.7,\n        count: 67\n      }\n    }];\n  }\n  loadMore() {\n    if (this.hasMore && !this.isLoading) {\n      this.loadSuggestedProducts(this.currentPage + 1);\n    }\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  viewVendor(vendor) {\n    this.router.navigate(['/vendor', vendor.username]);\n  }\n  addToCart(product, event) {\n    event.stopPropagation();\n    // TODO: Implement add to cart functionality\n    console.log('Add to cart:', product);\n  }\n  toggleWishlist(product, event) {\n    event.stopPropagation();\n    // TODO: Implement wishlist functionality\n    console.log('Toggle wishlist:', product);\n  }\n  shareProduct(product, event) {\n    event.stopPropagation();\n    // TODO: Implement share functionality\n    console.log('Share product:', product);\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return product.discount || 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  retry() {\n    this.loadSuggestedProducts(1);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  static {\n    this.ɵfac = function SuggestedProductsComponent_Factory(t) {\n      return new (t || SuggestedProductsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuggestedProductsComponent,\n      selectors: [[\"app-suggested-products\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 5,\n      consts: [[1, \"suggested-products-container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"fas\", \"fa-star\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-wrapper\", 4, \"ngIf\"], [\"class\", \"load-more-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"product-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-skeleton\"], [1, \"skeleton-image\"], [1, \"skeleton-content\"], [1, \"skeleton-line\"], [1, \"skeleton-line\", \"short\"], [1, \"error-container\"], [1, \"error-content\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"products-slider-wrapper\"], [\"slides-per-view\", \"auto\", 1, \"products-swiper\", 3, \"space-between\", \"navigation\", \"autoplay\", \"loop\", \"free-mode\", \"breakpoints\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [\"title\", \"Add to wishlist\", 1, \"wishlist-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"quick-actions\"], [\"title\", \"Add to cart\", 1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [\"title\", \"Share\", 1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"product-info\"], [1, \"vendor-info\", 3, \"click\"], [1, \"vendor-avatar\", 3, \"src\", \"alt\"], [1, \"vendor-name\"], [\"class\", \"fas fa-check-circle verified-icon\", \"title\", \"Verified Influencer\", 4, \"ngIf\"], [1, \"product-name\"], [1, \"price-container\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"rating-container\", 4, \"ngIf\"], [1, \"analytics-container\"], [1, \"analytics-item\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-heart\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"discount-badge\"], [\"title\", \"Verified Influencer\", 1, \"fas\", \"fa-check-circle\", \"verified-icon\"], [1, \"original-price\"], [1, \"rating-container\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"load-more-container\"], [1, \"load-more-btn\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"empty-container\"], [1, \"empty-content\"], [1, \"browse-btn\", 3, \"click\"]],\n      template: function SuggestedProductsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \" Suggested For You \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SuggestedProductsComponent_Template_button_click_5_listener() {\n            return ctx.router.navigate([\"/shop\"], {\n              queryParams: {\n                filter: \"suggested\"\n              }\n            });\n          });\n          i0.ɵɵtext(6, \" View All \");\n          i0.ɵɵelement(7, \"i\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, SuggestedProductsComponent_div_8_Template, 3, 2, \"div\", 6)(9, SuggestedProductsComponent_div_9_Template, 10, 1, \"div\", 7)(10, SuggestedProductsComponent_div_10_Template, 3, 8, \"div\", 8)(11, SuggestedProductsComponent_div_11_Template, 4, 3, \"div\", 9)(12, SuggestedProductsComponent_div_12_Template, 9, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading && ctx.products.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && ctx.products.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.products.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasMore);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.products.length === 0 && !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n      styles: [\".suggested-products-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  border-radius: 16px;\\n  margin: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .suggested-products-container[_ngcontent-%COMP%] {\\n    margin: 8px;\\n    padding: 16px;\\n    border-radius: 12px;\\n  }\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #2d3748;\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #f6ad55;\\n  font-size: 20px;\\n}\\n@media (max-width: 768px) {\\n  .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  color: white;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\\n}\\n@media (max-width: 768px) {\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 14px;\\n  }\\n}\\n\\n\\n\\n.products-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  padding: 0 35px; \\n\\n  overflow: hidden; \\n\\n}\\n@media (max-width: 768px) {\\n  .products-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .products-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n  }\\n}\\n\\n\\n\\n.products-swiper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: relative;\\n  \\n\\n  --swiper-navigation-size: 24px;\\n  --swiper-navigation-top-offset: 50%;\\n  --swiper-navigation-sides-offset: 6px;\\n  --swiper-navigation-color: #667eea;\\n  --swiper-theme-color: #667eea;\\n}\\n.products-swiper[_ngcontent-%COMP%]     {\\n  \\n\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-wrapper {\\n  align-items: stretch; \\n\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-slide {\\n  display: flex;\\n  height: auto;\\n  width: 320px !important; \\n\\n}\\n@media (max-width: 1200px) {\\n  .products-swiper[_ngcontent-%COMP%]     .swiper-slide {\\n    width: 300px !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-swiper[_ngcontent-%COMP%]     .swiper-slide {\\n    width: 280px !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .products-swiper[_ngcontent-%COMP%]     .swiper-slide {\\n    width: 240px !important;\\n  }\\n}\\n@media (max-width: 360px) {\\n  .products-swiper[_ngcontent-%COMP%]     .swiper-slide {\\n    width: 200px !important;\\n  }\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev {\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)) !important;\\n  border: 1px solid rgba(102, 126, 234, 0.2) !important;\\n  border-radius: 50% !important;\\n  width: 24px !important;\\n  height: 24px !important;\\n  margin-top: -12px !important;\\n  cursor: pointer !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.15), 0 1px 3px rgba(0, 0, 0, 0.08) !important;\\n  z-index: 12 !important;\\n  -webkit-backdrop-filter: blur(6px) !important;\\n          backdrop-filter: blur(6px) !important;\\n  \\n\\n  \\n\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next svg, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev svg {\\n  display: none !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next:after, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev:after {\\n  content: \\\"\\\" !important;\\n  display: none !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next:before, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev:before {\\n  content: \\\"\\\" !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  width: 100% !important;\\n  height: 100% !important;\\n  background-size: 12px 12px !important;\\n  background-repeat: no-repeat !important;\\n  background-position: center !important;\\n  filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8)) !important;\\n  transition: all 0.3s ease !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next:hover, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev:hover {\\n  background: linear-gradient(135deg, #ffffff, #f8fafc) !important;\\n  transform: scale(1.1) translateY(-1px) !important;\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25), 0 3px 8px rgba(0, 0, 0, 0.12) !important;\\n  border-color: rgba(102, 126, 234, 0.4) !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next:active, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev:active {\\n  transform: scale(1.05) translateY(0) !important;\\n  transition-duration: 0.1s !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next.swiper-button-disabled, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev.swiper-button-disabled {\\n  opacity: 0.3 !important;\\n  cursor: not-allowed !important;\\n  pointer-events: none !important;\\n  transform: none !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next.swiper-button-disabled:hover, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev.swiper-button-disabled:hover {\\n  transform: none !important;\\n  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.15) !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-prev {\\n  left: 6px !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-prev:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.5 3L5.5 7L8.5 11' stroke='%23667eea' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-prev:hover:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.5 3L5.5 7L8.5 11' stroke='%23764ba2' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next {\\n  right: 6px !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.5 3L8.5 7L5.5 11' stroke='%23667eea' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n.products-swiper[_ngcontent-%COMP%]     .swiper-button-next:hover:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.5 3L8.5 7L5.5 11' stroke='%23764ba2' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n@media (max-width: 768px) {\\n  .products-swiper[_ngcontent-%COMP%]     .swiper-button-next, .products-swiper[_ngcontent-%COMP%]     .swiper-button-prev {\\n    display: none !important;\\n  }\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);\\n  width: 100%; \\n\\n  height: 100%; \\n\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 480px; \\n\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n}\\n@media (max-width: 768px) {\\n  .product-card[_ngcontent-%COMP%] {\\n    min-height: 420px;\\n    border-radius: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .product-card[_ngcontent-%COMP%] {\\n    min-height: 380px;\\n    border-radius: 14px;\\n  }\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-12px) scale(1.02);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 24px rgba(0, 0, 0, 0.08);\\n  border-color: rgba(102, 126, 234, 0.2);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.08);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .vendor-name[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n@media (max-width: 768px) {\\n  .product-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-6px) scale(1.01);\\n    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.06);\\n  }\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  aspect-ratio: 1;\\n  overflow: hidden;\\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  filter: brightness(1.02) contrast(1.05);\\n}\\n.product-image-container[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.08);\\n  filter: brightness(1.05) contrast(1.08);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  left: 16px;\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 13px;\\n  font-weight: 700;\\n  box-shadow: 0 4px 16px rgba(255, 107, 107, 0.4), 0 2px 8px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  z-index: 2;\\n}\\n@media (max-width: 480px) {\\n  .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n    padding: 4px 8px;\\n    font-size: 11px;\\n    top: 12px;\\n    left: 12px;\\n  }\\n}\\n.product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  background: rgba(255, 255, 255, 0.95);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\\n  z-index: 2;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.15);\\n  box-shadow: 0 6px 24px rgba(229, 62, 62, 0.3);\\n  border-color: rgba(229, 62, 62, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: #e53e3e;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #c53030;\\n  transform: scale(1.1);\\n}\\n@media (max-width: 480px) {\\n  .product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n    top: 12px;\\n    right: 12px;\\n  }\\n  .product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  display: flex;\\n  gap: 8px;\\n  opacity: 0;\\n  transform: translateY(20px);\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n@media (max-width: 768px) {\\n  .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex-grow: 1; \\n\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(248, 250, 252, 0.5) 100%);\\n}\\n@media (max-width: 768px) {\\n  .product-info[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    gap: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .product-info[_ngcontent-%COMP%] {\\n    padding: 14px;\\n    gap: 8px;\\n  }\\n}\\n\\n.vendor-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  padding: 8px 12px;\\n  border-radius: 12px;\\n  background: rgba(102, 126, 234, 0.05);\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n}\\n.vendor-info[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n  border-color: rgba(102, 126, 234, 0.2);\\n  transform: translateY(-1px);\\n}\\n.vendor-info[_ngcontent-%COMP%]   .vendor-avatar[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid rgba(102, 126, 234, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.vendor-info[_ngcontent-%COMP%]   .vendor-name[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #4a5568;\\n  transition: color 0.3s ease;\\n}\\n.vendor-info[_ngcontent-%COMP%]   .verified-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 14px;\\n  filter: drop-shadow(0 1px 2px rgba(102, 126, 234, 0.3));\\n}\\n@media (max-width: 480px) {\\n  .vendor-info[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    gap: 8px;\\n  }\\n  .vendor-info[_ngcontent-%COMP%]   .vendor-avatar[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n  }\\n  .vendor-info[_ngcontent-%COMP%]   .vendor-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .vendor-info[_ngcontent-%COMP%]   .verified-icon[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #1a202c;\\n  margin: 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  letter-spacing: -0.01em;\\n  transition: color 0.3s ease;\\n}\\n.product-name[_ngcontent-%COMP%]:hover {\\n  color: #667eea;\\n}\\n@media (max-width: 768px) {\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    font-weight: 600;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .product-name[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n}\\n\\n.price-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 8px 12px;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(102, 126, 234, 0.04));\\n  border-radius: 12px;\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n}\\n.price-container[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 800;\\n  color: #667eea;\\n  letter-spacing: -0.02em;\\n  text-shadow: 0 1px 2px rgba(102, 126, 234, 0.1);\\n}\\n.price-container[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  color: #a0aec0;\\n  text-decoration: line-through;\\n  font-weight: 500;\\n}\\n@media (max-width: 768px) {\\n  .price-container[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n  }\\n  .price-container[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .price-container[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .price-container[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .price-container[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n}\\n\\n.rating-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n}\\n.rating-container[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.rating-container[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #e2e8f0;\\n}\\n.rating-container[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%] {\\n  color: #f6ad55;\\n}\\n.rating-container[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #718096;\\n}\\n\\n.analytics-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.analytics-container[_ngcontent-%COMP%]   .analytics-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 11px;\\n  color: #718096;\\n}\\n.analytics-container[_ngcontent-%COMP%]   .analytics-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n@media (max-width: 768px) {\\n  .analytics-container[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .analytics-container[_ngcontent-%COMP%]   .analytics-item[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding: 0 40px;\\n  scroll-behavior: smooth;\\n  \\n\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 0 20px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n  }\\n}\\n.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_pulse 1.5s ease-in-out infinite;\\n  min-width: 280px; \\n\\n  flex-shrink: 0;\\n}\\n@media (max-width: 768px) {\\n  .loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n  }\\n}\\n.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%] {\\n  aspect-ratio: 1;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.8;\\n  }\\n}\\n.load-more-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 24px;\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 25px;\\n  padding: 12px 24px;\\n  color: white;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]   i.fa-spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@media (max-width: 768px) {\\n  .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%] {\\n    padding: 10px 20px;\\n    font-size: 13px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 300px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #a0aec0;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  margin-bottom: 8px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  margin-bottom: 24px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 20px;\\n  padding: 12px 24px;\\n  color: white;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\\n}\\n\\n.load-more-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 32px;\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 25px;\\n  padding: 12px 32px;\\n  color: white;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9jb21wb25lbnRzL3N1Z2dlc3RlZC1wcm9kdWN0cy9zdWdnZXN0ZWQtcHJvZHVjdHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSx5Q0FBQTtBQUNGO0FBQ0U7RUFQRjtJQVFJLFdBQUE7SUFDQSxhQUFBO0lBQ0EsbUJBQUE7RUFFRjtBQUNGOztBQUNBO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQUVGO0FBQUU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7QUFFSjtBQUFJO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUFFTjtBQUNJO0VBZEY7SUFlSSxlQUFBO0VBRUo7QUFDRjtBQUNFO0VBQ0UscURBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUFDSjtBQUNJO0VBQ0UsMkJBQUE7RUFDQSwrQ0FBQTtBQUNOO0FBRUk7RUFsQkY7SUFtQkksaUJBQUE7SUFDQSxlQUFBO0VBQ0o7QUFDRjs7QUFHQSw0QkFBQTtBQUNBO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQSxFQUFBLGlDQUFBO0VBQ0EsZ0JBQUEsRUFBQSw2QkFBQTtBQUFGO0FBRUU7RUFORjtJQU9JLGVBQUE7RUFDRjtBQUNGO0FBQ0U7RUFWRjtJQVdJLGVBQUE7RUFFRjtBQUNGOztBQUNBLHlDQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFFQSwrQ0FBQTtFQUNBLDhCQUFBO0VBQ0EsbUNBQUE7RUFDQSxxQ0FBQTtFQUNBLGtDQUFBO0VBQ0EsNkJBQUE7QUFDRjtBQUNFO0VBMkJFLGtEQUFBO0FBekJKO0FBREk7RUFDRSxvQkFBQSxFQUFBLGdDQUFBO0FBR047QUFBSTtFQUNFLGFBQUE7RUFDQSxZQUFBO0VBQ0EsdUJBQUEsRUFBQSwrQ0FBQTtBQUVOO0FBQU07RUFMRjtJQU1JLHVCQUFBO0VBR047QUFDRjtBQURNO0VBVEY7SUFVSSx1QkFBQTtFQUlOO0FBQ0Y7QUFGTTtFQWJGO0lBY0ksdUJBQUE7RUFLTjtBQUNGO0FBSE07RUFqQkY7SUFrQkksdUJBQUE7RUFNTjtBQUNGO0FBRkk7O0VBRUUsbUdBQUE7RUFDQSxxREFBQTtFQUNBLDZCQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtFQUNBLDRCQUFBO0VBQ0EsMEJBQUE7RUFDQSw0REFBQTtFQUNBLDBGQUNFO0VBRUYsc0JBQUE7RUFDQSw2Q0FBQTtVQUFBLHFDQUFBO0VBRUEsMENBQUE7RUFVQSwwQkFBQTtBQVJOO0FBRE07O0VBQ0Usd0JBQUE7QUFJUjtBQURNOztFQUNFLHNCQUFBO0VBQ0Esd0JBQUE7QUFJUjtBQUFNOztFQUNFLHNCQUFBO0VBQ0Esd0JBQUE7RUFDQSw4QkFBQTtFQUNBLGtDQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtFQUNBLHFDQUFBO0VBQ0EsdUNBQUE7RUFDQSxzQ0FBQTtFQUNBLGtFQUFBO0VBQ0Esb0NBQUE7QUFHUjtBQUFNOztFQUNFLGdFQUFBO0VBQ0EsaURBQUE7RUFDQSwwRkFDRTtFQUVGLGlEQUFBO0FBQ1I7QUFFTTs7RUFDRSwrQ0FBQTtFQUNBLG9DQUFBO0FBQ1I7QUFFTTs7RUFDRSx1QkFBQTtFQUNBLDhCQUFBO0VBQ0EsK0JBQUE7RUFDQSwwQkFBQTtBQUNSO0FBQ1E7O0VBQ0UsMEJBQUE7RUFDQSwyREFBQTtBQUVWO0FBR0k7RUFDRSxvQkFBQTtBQUROO0FBR007RUFDRSwwQkFBQTtFQUNBLDRSQUFBO0FBRFI7QUFJTTtFQUNFLDJCQUFBO0VBQ0EsNFJBQUE7QUFGUjtBQU1JO0VBQ0UscUJBQUE7QUFKTjtBQU1NO0VBQ0UsMkJBQUE7RUFDQSw0UkFBQTtBQUpSO0FBT007RUFDRSwyQkFBQTtFQUNBLDRSQUFBO0FBTFI7QUFTSTtFQUNFOztJQUVFLHdCQUFBO0VBUE47QUFDRjs7QUFZQTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxpREFBQTtFQUNBLHlFQUNFO0VBRUYsV0FBQSxFQUFBLDhCQUFBO0VBQ0EsWUFBQSxFQUFBLDBDQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUEsRUFBQSxnREFBQTtFQUNBLHFDQUFBO0FBWEY7QUFhRTtFQWhCRjtJQWlCSSxpQkFBQTtJQUNBLG1CQUFBO0VBVkY7QUFDRjtBQVlFO0VBckJGO0lBc0JJLGlCQUFBO0lBQ0EsbUJBQUE7RUFURjtBQUNGO0FBV0U7RUFDRSx3Q0FBQTtFQUNBLDJFQUNFO0VBRUYsc0NBQUE7QUFYSjtBQWFJO0VBQ0UsVUFBQTtFQUNBLHdCQUFBO0FBWE47QUFjSTtFQUNFLHNCQUFBO0FBWk47QUFlSTtFQUNFLGNBQUE7QUFiTjtBQWlCRTtFQUNFO0lBQ0UsdUNBQUE7SUFDQSwyRUFDRTtFQWhCTjtBQUNGOztBQXFCQTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsNkRBQUE7QUFsQkY7QUFvQkU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0EsaURBQUE7RUFDQSx1Q0FBQTtBQWxCSjtBQXFCRTtFQUNFLHNCQUFBO0VBQ0EsdUNBQUE7QUFuQko7QUFzQkU7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EscURBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLDZFQUNFO0VBRUYsa0NBQUE7VUFBQSwwQkFBQTtFQUNBLDBDQUFBO0VBQ0EsVUFBQTtBQXRCSjtBQXdCSTtFQWpCRjtJQWtCSSxnQkFBQTtJQUNBLGVBQUE7SUFDQSxTQUFBO0lBQ0EsVUFBQTtFQXJCSjtBQUNGO0FBd0JFO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLHFDQUFBO0VBQ0EsMENBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0VBQ0EsaURBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0EseUNBQUE7RUFDQSxVQUFBO0FBdEJKO0FBd0JJO0VBQ0UsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLDZDQUFBO0VBQ0Esb0NBQUE7QUF0Qk47QUF5Qkk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBdkJOO0FBMEJJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBeEJOO0FBMkJJO0VBcENGO0lBcUNJLFdBQUE7SUFDQSxZQUFBO0lBQ0EsU0FBQTtJQUNBLFdBQUE7RUF4Qko7RUEwQkk7SUFDRSxlQUFBO0VBeEJOO0FBQ0Y7QUE0QkU7RUFDRSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0EsYUFBQTtFQUNBLFFBQUE7RUFDQSxVQUFBO0VBQ0EsMkJBQUE7RUFDQSx5QkFBQTtBQTFCSjtBQTRCSTtFQUNFLG9DQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLG1DQUFBO1VBQUEsMkJBQUE7QUExQk47QUE0Qk07RUFDRSxpQkFBQTtFQUNBLHFCQUFBO0FBMUJSO0FBNkJNO0VBQ0UsZUFBQTtBQTNCUjtBQStCSTtFQWpDRjtJQWtDSSxVQUFBO0lBQ0Esd0JBQUE7RUE1Qko7QUFDRjs7QUFnQ0E7RUFDRSxhQUFBO0VBQ0EsWUFBQSxFQUFBLGlDQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtFQUNBLDZGQUFBO0FBN0JGO0FBK0JFO0VBUkY7SUFTSSxhQUFBO0lBQ0EsU0FBQTtFQTVCRjtBQUNGO0FBOEJFO0VBYkY7SUFjSSxhQUFBO0lBQ0EsUUFBQTtFQTNCRjtBQUNGOztBQThCQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EscUNBQUE7RUFDQSwwQ0FBQTtBQTNCRjtBQTZCRTtFQUNFLG9DQUFBO0VBQ0Esc0NBQUE7RUFDQSwyQkFBQTtBQTNCSjtBQThCRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7QUE1Qko7QUErQkU7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsMkJBQUE7QUE3Qko7QUFnQ0U7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLHVEQUFBO0FBOUJKO0FBaUNFO0VBdkNGO0lBd0NJLGlCQUFBO0lBQ0EsUUFBQTtFQTlCRjtFQWdDRTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VBOUJKO0VBaUNFO0lBQ0UsZUFBQTtFQS9CSjtFQWtDRTtJQUNFLGVBQUE7RUFoQ0o7QUFDRjs7QUFvQ0E7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsU0FBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7RUFDQSxxQkFBQTtFQUNBLDRCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0FBakNGO0FBbUNFO0VBQ0UsY0FBQTtBQWpDSjtBQW9DRTtFQWpCRjtJQWtCSSxlQUFBO0lBQ0EsZ0JBQUE7RUFqQ0Y7QUFDRjtBQW1DRTtFQXRCRjtJQXVCSSxlQUFBO0VBaENGO0FBQ0Y7O0FBbUNBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLGlCQUFBO0VBQ0EseUZBQUE7RUFDQSxtQkFBQTtFQUNBLDBDQUFBO0FBaENGO0FBa0NFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLHVCQUFBO0VBQ0EsK0NBQUE7QUFoQ0o7QUFtQ0U7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLDZCQUFBO0VBQ0EsZ0JBQUE7QUFqQ0o7QUFvQ0U7RUF4QkY7SUF5QkksaUJBQUE7RUFqQ0Y7RUFtQ0U7SUFDRSxlQUFBO0VBakNKO0VBb0NFO0lBQ0UsZUFBQTtFQWxDSjtBQUNGO0FBcUNFO0VBQ0U7SUFDRSxlQUFBO0VBbkNKO0VBc0NFO0lBQ0UsZUFBQTtFQXBDSjtBQUNGOztBQXdDQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxrQkFBQTtBQXJDRjtBQXVDRTtFQUNFLGFBQUE7RUFDQSxRQUFBO0FBckNKO0FBdUNJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUFyQ047QUF1Q007RUFDRSxjQUFBO0FBckNSO0FBMENFO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUF4Q0o7O0FBNENBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUF6Q0Y7QUEyQ0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7QUF6Q0o7QUEyQ0k7RUFDRSxlQUFBO0FBekNOO0FBNkNFO0VBaEJGO0lBaUJJLFFBQUE7RUExQ0Y7RUE0Q0U7SUFDRSxlQUFBO0VBMUNKO0FBQ0Y7O0FBZ0RFO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx1QkFBQTtFQUVBLG1CQUFBO0VBQ0EscUJBQUE7RUFDQSx3QkFBQTtBQTlDSjtBQStDSTtFQUNFLGFBQUE7QUE3Q047QUFnREk7RUFkRjtJQWVJLFNBQUE7SUFDQSxlQUFBO0VBN0NKO0FBQ0Y7QUErQ0k7RUFuQkY7SUFvQkksZUFBQTtFQTVDSjtBQUNGO0FBK0NFO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsMENBQUE7RUFDQSxnQkFBQSxFQUFBLHdDQUFBO0VBQ0EsY0FBQTtBQTdDSjtBQStDSTtFQVJGO0lBU0ksZ0JBQUE7RUE1Q0o7QUFDRjtBQThDSTtFQVpGO0lBYUksZ0JBQUE7RUEzQ0o7QUFDRjtBQTZDSTtFQUNFLGVBQUE7RUFDQSx5RUFBQTtFQUNBLDBCQUFBO0VBQ0EsZ0NBQUE7QUEzQ047QUE4Q0k7RUFDRSxhQUFBO0FBNUNOO0FBOENNO0VBQ0UsWUFBQTtFQUNBLHlFQUFBO0VBQ0EsMEJBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUE1Q1I7QUE4Q1E7RUFDRSxVQUFBO0FBNUNWOztBQW1EQTtFQUNFO0lBQUssMkJBQUE7RUEvQ0w7RUFnREE7SUFBTyw0QkFBQTtFQTdDUDtBQUNGO0FBK0NBO0VBQ0U7SUFBVyxVQUFBO0VBNUNYO0VBNkNBO0lBQU0sWUFBQTtFQTFDTjtBQUNGO0FBNkNBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7QUEzQ0Y7QUE2Q0U7RUFDRSxxREFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLGVBQUE7QUEzQ0o7QUE2Q0k7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBM0NOO0FBOENJO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtBQTVDTjtBQStDSTtFQUNFLGtDQUFBO0FBN0NOO0FBZ0RJO0VBN0JGO0lBOEJJLGtCQUFBO0lBQ0EsZUFBQTtFQTdDSjtBQUNGOztBQWlEQTtFQUNFO0lBQU8sdUJBQUE7RUE3Q1A7RUE4Q0E7SUFBSyx5QkFBQTtFQTNDTDtBQUNGO0FBOENBOztFQUVFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7QUE1Q0Y7QUE4Q0U7Ozs7RUFFRSxrQkFBQTtFQUNBLGdCQUFBO0FBMUNKO0FBNENJOzs7O0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQXZDTjtBQTBDSTs7OztFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQXJDTjtBQXdDSTs7OztFQUNFLGNBQUE7RUFDQSxtQkFBQTtBQW5DTjtBQXNDSTs7Ozs7Ozs7RUFFRSxxREFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBOUJOO0FBZ0NNOzs7Ozs7OztFQUNFLDJCQUFBO0VBQ0EsK0NBQUE7QUF2QlI7O0FBOEJBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7QUEzQkY7QUE2QkU7RUFDRSxxREFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBM0JKO0FBNkJJO0VBQ0UsMkJBQUE7RUFDQSwrQ0FBQTtBQTNCTjtBQThCSTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtBQTVCTjtBQStCSTtFQUNFLGlCQUFBO0FBN0JOIiwic291cmNlc0NvbnRlbnQiOlsiLnN1Z2dlc3RlZC1wcm9kdWN0cy1jb250YWluZXIge1xuICBwYWRkaW5nOiAyNHB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpO1xuICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICBtYXJnaW46IDE2cHg7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIG1hcmdpbjogOHB4O1xuICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgfVxufVxuXG4uc2VjdGlvbi1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XG5cbiAgLnNlY3Rpb24tdGl0bGUge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDEycHg7XG4gICAgZm9udC1zaXplOiAyNHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgY29sb3I6ICMyZDM3NDg7XG4gICAgbWFyZ2luOiAwO1xuXG4gICAgaSB7XG4gICAgICBjb2xvcjogI2Y2YWQ1NTtcbiAgICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICB9XG4gIH1cblxuICAudmlldy1hbGwtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgICBwYWRkaW5nOiA4cHggMTZweDtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiA4cHg7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpO1xuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgcGFkZGluZzogNnB4IDEycHg7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgfVxuICB9XG59XG5cbi8qIFByb2R1Y3RzIFNsaWRlciBXcmFwcGVyICovXG4ucHJvZHVjdHMtc2xpZGVyLXdyYXBwZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiAxMDAlO1xuICBwYWRkaW5nOiAwIDM1cHg7IC8qIFNwYWNlIGZvciBuYXZpZ2F0aW9uIGJ1dHRvbnMgKi9cbiAgb3ZlcmZsb3c6IGhpZGRlbjsgLyogUHJldmVudCBjb250ZW50IG92ZXJmbG93ICovXG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgcGFkZGluZzogMCAxNnB4O1xuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gICAgcGFkZGluZzogMCAxMnB4O1xuICB9XG59XG5cbi8qIFN3aXBlciBDYXJvdXNlbCBTdHlsaW5nIGZvciBQcm9kdWN0cyAqL1xuLnByb2R1Y3RzLXN3aXBlciB7XG4gIHdpZHRoOiAxMDAlO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG5cbiAgLyogT3ZlcnJpZGUgU3dpcGVyIENTUyBWYXJpYWJsZXMgZm9yIFByb2R1Y3RzICovXG4gIC0tc3dpcGVyLW5hdmlnYXRpb24tc2l6ZTogMjRweDtcbiAgLS1zd2lwZXItbmF2aWdhdGlvbi10b3Atb2Zmc2V0OiA1MCU7XG4gIC0tc3dpcGVyLW5hdmlnYXRpb24tc2lkZXMtb2Zmc2V0OiA2cHg7XG4gIC0tc3dpcGVyLW5hdmlnYXRpb24tY29sb3I6ICM2NjdlZWE7XG4gIC0tc3dpcGVyLXRoZW1lLWNvbG9yOiAjNjY3ZWVhO1xuXG4gIDo6bmctZGVlcCB7XG4gICAgLnN3aXBlci13cmFwcGVyIHtcbiAgICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoOyAvKiBNYWtlIGFsbCBzbGlkZXMgc2FtZSBoZWlnaHQgKi9cbiAgICB9XG5cbiAgICAuc3dpcGVyLXNsaWRlIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBoZWlnaHQ6IGF1dG87XG4gICAgICB3aWR0aDogMzIwcHggIWltcG9ydGFudDsgLyogSW5jcmVhc2VkIHdpZHRoIGZvciBiZXR0ZXIgY29udGVudCBkaXNwbGF5ICovXG5cbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHtcbiAgICAgICAgd2lkdGg6IDMwMHB4ICFpbXBvcnRhbnQ7XG4gICAgICB9XG5cbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgICB3aWR0aDogMjgwcHggIWltcG9ydGFudDtcbiAgICAgIH1cblxuICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gICAgICAgIHdpZHRoOiAyNDBweCAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICBAbWVkaWEgKG1heC13aWR0aDogMzYwcHgpIHtcbiAgICAgICAgd2lkdGg6IDIwMHB4ICFpbXBvcnRhbnQ7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLyogQ3VzdG9tIFN3aXBlciBOYXZpZ2F0aW9uIEJ1dHRvbnMgZm9yIFByb2R1Y3RzICovXG4gICAgLnN3aXBlci1idXR0b24tbmV4dCxcbiAgICAuc3dpcGVyLWJ1dHRvbi1wcmV2IHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45NSksIHJnYmEoMjQ4LCAyNTAsIDI1MiwgMC45KSkgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4yKSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlICFpbXBvcnRhbnQ7XG4gICAgICB3aWR0aDogMjRweCAhaW1wb3J0YW50O1xuICAgICAgaGVpZ2h0OiAyNHB4ICFpbXBvcnRhbnQ7XG4gICAgICBtYXJnaW4tdG9wOiBjYWxjKDBweCAtICgyNHB4IC8gMikpICFpbXBvcnRhbnQ7XG4gICAgICBjdXJzb3I6IHBvaW50ZXIgIWltcG9ydGFudDtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSkgIWltcG9ydGFudDtcbiAgICAgIGJveC1zaGFkb3c6XG4gICAgICAgIDAgM3B4IDEwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjE1KSxcbiAgICAgICAgMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4wOCkgIWltcG9ydGFudDtcbiAgICAgIHotaW5kZXg6IDEyICFpbXBvcnRhbnQ7XG4gICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNnB4KSAhaW1wb3J0YW50O1xuXG4gICAgICAvKiBIaWRlIGRlZmF1bHQgU1ZHIGFuZCB1c2UgY3VzdG9tIGljb25zICovXG4gICAgICBzdmcge1xuICAgICAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gICAgICB9XG5cbiAgICAgICY6YWZ0ZXIge1xuICAgICAgICBjb250ZW50OiAnJyAhaW1wb3J0YW50O1xuICAgICAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gICAgICB9XG5cbiAgICAgIC8qIEN1c3RvbSBJY29uIHVzaW5nIENTUyAqL1xuICAgICAgJjpiZWZvcmUge1xuICAgICAgICBjb250ZW50OiAnJyAhaW1wb3J0YW50O1xuICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXIgIWltcG9ydGFudDtcbiAgICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcbiAgICAgICAgaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7XG4gICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTJweCAxMnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQgIWltcG9ydGFudDtcbiAgICAgICAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICAgIGZpbHRlcjogZHJvcC1zaGFkb3coMCAxcHggMnB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KSkgIWltcG9ydGFudDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZSAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZmZmZiwgI2Y4ZmFmYykgIWltcG9ydGFudDtcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpIHRyYW5zbGF0ZVkoLTFweCkgIWltcG9ydGFudDtcbiAgICAgICAgYm94LXNoYWRvdzpcbiAgICAgICAgICAwIDZweCAyMHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4yNSksXG4gICAgICAgICAgMCAzcHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xMikgIWltcG9ydGFudDtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuNCkgIWltcG9ydGFudDtcbiAgICAgIH1cblxuICAgICAgJjphY3RpdmUge1xuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpIHRyYW5zbGF0ZVkoMCkgIWltcG9ydGFudDtcbiAgICAgICAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMC4xcyAhaW1wb3J0YW50O1xuICAgICAgfVxuXG4gICAgICAmLnN3aXBlci1idXR0b24tZGlzYWJsZWQge1xuICAgICAgICBvcGFjaXR5OiAwLjMgIWltcG9ydGFudDtcbiAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZCAhaW1wb3J0YW50O1xuICAgICAgICBwb2ludGVyLWV2ZW50czogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgICB0cmFuc2Zvcm06IG5vbmUgIWltcG9ydGFudDtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IG5vbmUgIWltcG9ydGFudDtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDNweCAxMHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xNSkgIWltcG9ydGFudDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5zd2lwZXItYnV0dG9uLXByZXYge1xuICAgICAgbGVmdDogNnB4ICFpbXBvcnRhbnQ7XG5cbiAgICAgICY6YmVmb3JlIHtcbiAgICAgICAgLyogTW9kZXJuIExlZnQgQXJyb3cgU1ZHICovXG4gICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0Nzdmcgd2lkdGg9JzE0JyBoZWlnaHQ9JzE0JyB2aWV3Qm94PScwIDAgMTQgMTQnIGZpbGw9J25vbmUnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyclM0UlM0NwYXRoIGQ9J004LjUgM0w1LjUgN0w4LjUgMTEnIHN0cm9rZT0nJTIzNjY3ZWVhJyBzdHJva2Utd2lkdGg9JzEuNScgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJy8lM0UlM0Mvc3ZnJTNFXCIpICFpbXBvcnRhbnQ7XG4gICAgICB9XG5cbiAgICAgICY6aG92ZXI6YmVmb3JlIHtcbiAgICAgICAgLyogRGFya2VyIHB1cnBsZSBvbiBob3ZlciAqL1xuICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNDc3ZnIHdpZHRoPScxNCcgaGVpZ2h0PScxNCcgdmlld0JveD0nMCAwIDE0IDE0JyBmaWxsPSdub25lJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnJTNFJTNDcGF0aCBkPSdNOC41IDNMNS41IDdMOC41IDExJyBzdHJva2U9JyUyMzc2NGJhMicgc3Ryb2tlLXdpZHRoPScxLjUnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcvJTNFJTNDL3N2ZyUzRVwiKSAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIH1cblxuICAgIC5zd2lwZXItYnV0dG9uLW5leHQge1xuICAgICAgcmlnaHQ6IDZweCAhaW1wb3J0YW50O1xuXG4gICAgICAmOmJlZm9yZSB7XG4gICAgICAgIC8qIE1vZGVybiBSaWdodCBBcnJvdyBTVkcgKi9cbiAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLCUzQ3N2ZyB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIHZpZXdCb3g9JzAgMCAxNCAxNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyUzRSUzQ3BhdGggZD0nTTUuNSAzTDguNSA3TDUuNSAxMScgc3Ryb2tlPSclMjM2NjdlZWEnIHN0cm9rZS13aWR0aD0nMS41JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnLyUzRSUzQy9zdmclM0VcIikgIWltcG9ydGFudDtcbiAgICAgIH1cblxuICAgICAgJjpob3ZlcjpiZWZvcmUge1xuICAgICAgICAvKiBEYXJrZXIgcHVycGxlIG9uIGhvdmVyICovXG4gICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0Nzdmcgd2lkdGg9JzE0JyBoZWlnaHQ9JzE0JyB2aWV3Qm94PScwIDAgMTQgMTQnIGZpbGw9J25vbmUnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyclM0UlM0NwYXRoIGQ9J001LjUgM0w4LjUgN0w1LjUgMTEnIHN0cm9rZT0nJTIzNzY0YmEyJyBzdHJva2Utd2lkdGg9JzEuNScgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJy8lM0UlM0Mvc3ZnJTNFXCIpICFpbXBvcnRhbnQ7XG4gICAgICB9XG4gICAgfVxuXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAuc3dpcGVyLWJ1dHRvbi1uZXh0LFxuICAgICAgLnN3aXBlci1idXR0b24tcHJldiB7XG4gICAgICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLnByb2R1Y3QtY2FyZCB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG4gIGJveC1zaGFkb3c6XG4gICAgMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMDgpLFxuICAgIDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDQpO1xuICB3aWR0aDogMTAwJTsgLyogRnVsbCB3aWR0aCBvZiBzbGlkZXIgaXRlbSAqL1xuICBoZWlnaHQ6IDEwMCU7IC8qIEZ1bGwgaGVpZ2h0IGZvciBjb25zaXN0ZW50IGNhcmQgc2l6ZXMgKi9cbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgbWluLWhlaWdodDogNDgwcHg7IC8qIEluY3JlYXNlZCBoZWlnaHQgZm9yIGJldHRlciBjb250ZW50IGRpc3BsYXkgKi9cbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjA0KTtcblxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBtaW4taGVpZ2h0OiA0MjBweDtcbiAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gICAgbWluLWhlaWdodDogMzgwcHg7XG4gICAgYm9yZGVyLXJhZGl1czogMTRweDtcbiAgfVxuXG4gICY6aG92ZXIge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTJweCkgc2NhbGUoMS4wMik7XG4gICAgYm94LXNoYWRvdzpcbiAgICAgIDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSksXG4gICAgICAwIDhweCAyNHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gICAgYm9yZGVyLWNvbG9yOiByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMik7XG5cbiAgICAucXVpY2stYWN0aW9ucyB7XG4gICAgICBvcGFjaXR5OiAxO1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICAgIH1cblxuICAgIC5wcm9kdWN0LWltYWdlIHtcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wOCk7XG4gICAgfVxuXG4gICAgLnZlbmRvci1uYW1lIHtcbiAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgIH1cbiAgfVxuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICY6aG92ZXIge1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC02cHgpIHNjYWxlKDEuMDEpO1xuICAgICAgYm94LXNoYWRvdzpcbiAgICAgICAgMCAxMnB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEyKSxcbiAgICAgICAgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xuICAgIH1cbiAgfVxufVxuXG4ucHJvZHVjdC1pbWFnZS1jb250YWluZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGFzcGVjdC1yYXRpbzogMTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZmFmYyAwJSwgI2UyZThmMCAxMDAlKTtcblxuICAucHJvZHVjdC1pbWFnZSB7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgaGVpZ2h0OiAxMDAlO1xuICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG4gICAgZmlsdGVyOiBicmlnaHRuZXNzKDEuMDIpIGNvbnRyYXN0KDEuMDUpO1xuICB9XG5cbiAgJjpob3ZlciAucHJvZHVjdC1pbWFnZSB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA4KTtcbiAgICBmaWx0ZXI6IGJyaWdodG5lc3MoMS4wNSkgY29udHJhc3QoMS4wOCk7XG4gIH1cblxuICAuZGlzY291bnQtYmFkZ2Uge1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDE2cHg7XG4gICAgbGVmdDogMTZweDtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY2YjZiLCAjZWU1YTI0KTtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgcGFkZGluZzogNnB4IDEycHg7XG4gICAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgICBmb250LXNpemU6IDEzcHg7XG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICBib3gtc2hhZG93OlxuICAgICAgMCA0cHggMTZweCByZ2JhKDI1NSwgMTA3LCAxMDcsIDAuNCksXG4gICAgICAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig4cHgpO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgICB6LWluZGV4OiAyO1xuXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gICAgICBwYWRkaW5nOiA0cHggOHB4O1xuICAgICAgZm9udC1zaXplOiAxMXB4O1xuICAgICAgdG9wOiAxMnB4O1xuICAgICAgbGVmdDogMTJweDtcbiAgICB9XG4gIH1cblxuICAud2lzaGxpc3QtYnRuIHtcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgdG9wOiAxNnB4O1xuICAgIHJpZ2h0OiAxNnB4O1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45NSk7XG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICB3aWR0aDogNDBweDtcbiAgICBoZWlnaHQ6IDQwcHg7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMnB4KTtcbiAgICBib3gtc2hhZG93OiAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICB6LWluZGV4OiAyO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xNSk7XG4gICAgICBib3gtc2hhZG93OiAwIDZweCAyNHB4IHJnYmEoMjI5LCA2MiwgNjIsIDAuMyk7XG4gICAgICBib3JkZXItY29sb3I6IHJnYmEoMjI5LCA2MiwgNjIsIDAuMik7XG4gICAgfVxuXG4gICAgaSB7XG4gICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICBjb2xvcjogI2U1M2UzZTtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgfVxuXG4gICAgJjpob3ZlciBpIHtcbiAgICAgIGNvbG9yOiAjYzUzMDMwO1xuICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAgICAgd2lkdGg6IDM2cHg7XG4gICAgICBoZWlnaHQ6IDM2cHg7XG4gICAgICB0b3A6IDEycHg7XG4gICAgICByaWdodDogMTJweDtcblxuICAgICAgaSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAucXVpY2stYWN0aW9ucyB7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIGJvdHRvbTogMTJweDtcbiAgICByaWdodDogMTJweDtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGdhcDogOHB4O1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDIwcHgpO1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAucXVpY2stYWN0aW9uLWJ0biB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICB3aWR0aDogMzJweDtcbiAgICAgIGhlaWdodDogMzJweDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbiAgICAgIH1cblxuICAgICAgaSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIG9wYWNpdHk6IDE7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XG4gICAgfVxuICB9XG59XG5cbi5wcm9kdWN0LWluZm8ge1xuICBwYWRkaW5nOiAyMHB4O1xuICBmbGV4LWdyb3c6IDE7IC8qIEZpbGwgcmVtYWluaW5nIHNwYWNlIGluIGNhcmQgKi9cbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAxMnB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDApIDAlLCByZ2JhKDI0OCwgMjUwLCAyNTIsIDAuNSkgMTAwJSk7XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgcGFkZGluZzogMTZweDtcbiAgICBnYXA6IDEwcHg7XG4gIH1cblxuICBAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcbiAgICBwYWRkaW5nOiAxNHB4O1xuICAgIGdhcDogOHB4O1xuICB9XG59XG5cbi52ZW5kb3ItaW5mbyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTBweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBwYWRkaW5nOiA4cHggMTJweDtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgYmFja2dyb3VuZDogcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjA1KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpO1xuXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKTtcbiAgICBib3JkZXItY29sb3I6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4yKTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gIH1cblxuICAudmVuZG9yLWF2YXRhciB7XG4gICAgd2lkdGg6IDI4cHg7XG4gICAgaGVpZ2h0OiAyOHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICBib3JkZXI6IDJweCBzb2xpZCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMik7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgfVxuXG4gIC52ZW5kb3ItbmFtZSB7XG4gICAgZm9udC1zaXplOiAxM3B4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY29sb3I6ICM0YTU1Njg7XG4gICAgdHJhbnNpdGlvbjogY29sb3IgMC4zcyBlYXNlO1xuICB9XG5cbiAgLnZlcmlmaWVkLWljb24ge1xuICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMXB4IDJweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMykpO1xuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gICAgcGFkZGluZzogNnB4IDEwcHg7XG4gICAgZ2FwOiA4cHg7XG5cbiAgICAudmVuZG9yLWF2YXRhciB7XG4gICAgICB3aWR0aDogMjRweDtcbiAgICAgIGhlaWdodDogMjRweDtcbiAgICB9XG5cbiAgICAudmVuZG9yLW5hbWUge1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgIH1cblxuICAgIC52ZXJpZmllZC1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICB9XG4gIH1cbn1cblxuLnByb2R1Y3QtbmFtZSB7XG4gIGZvbnQtc2l6ZTogMThweDtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6ICMxYTIwMmM7XG4gIG1hcmdpbjogMDtcbiAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgZGlzcGxheTogLXdlYmtpdC1ib3g7XG4gIC13ZWJraXQtbGluZS1jbGFtcDogMjtcbiAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgbGV0dGVyLXNwYWNpbmc6IC0wLjAxZW07XG4gIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZTtcblxuICAmOmhvdmVyIHtcbiAgICBjb2xvcjogIzY2N2VlYTtcbiAgfVxuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gICAgZm9udC1zaXplOiAxNXB4O1xuICB9XG59XG5cbi5wcmljZS1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEwcHg7XG4gIHBhZGRpbmc6IDhweCAxMnB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMDgpLCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMDQpKTtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpO1xuXG4gIC5jdXJyZW50LXByaWNlIHtcbiAgICBmb250LXNpemU6IDIwcHg7XG4gICAgZm9udC13ZWlnaHQ6IDgwMDtcbiAgICBjb2xvcjogIzY2N2VlYTtcbiAgICBsZXR0ZXItc3BhY2luZzogLTAuMDJlbTtcbiAgICB0ZXh0LXNoYWRvdzogMCAxcHggMnB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKTtcbiAgfVxuXG4gIC5vcmlnaW5hbC1wcmljZSB7XG4gICAgZm9udC1zaXplOiAxNXB4O1xuICAgIGNvbG9yOiAjYTBhZWMwO1xuICAgIHRleHQtZGVjb3JhdGlvbjogbGluZS10aHJvdWdoO1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIH1cblxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBwYWRkaW5nOiA2cHggMTBweDtcblxuICAgIC5jdXJyZW50LXByaWNlIHtcbiAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICB9XG5cbiAgICAub3JpZ2luYWwtcHJpY2Uge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgIH1cbiAgfVxuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAgIC5jdXJyZW50LXByaWNlIHtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICB9XG5cbiAgICAub3JpZ2luYWwtcHJpY2Uge1xuICAgICAgZm9udC1zaXplOiAxM3B4O1xuICAgIH1cbiAgfVxufVxuXG4ucmF0aW5nLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogOHB4O1xuICBtYXJnaW4tYm90dG9tOiA4cHg7XG5cbiAgLnN0YXJzIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGdhcDogMnB4O1xuXG4gICAgaSB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBjb2xvcjogI2UyZThmMDtcblxuICAgICAgJi5maWxsZWQge1xuICAgICAgICBjb2xvcjogI2Y2YWQ1NTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAucmF0aW5nLXRleHQge1xuICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICBjb2xvcjogIzcxODA5NjtcbiAgfVxufVxuXG4uYW5hbHl0aWNzLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMTJweDtcblxuICAuYW5hbHl0aWNzLWl0ZW0ge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDRweDtcbiAgICBmb250LXNpemU6IDExcHg7XG4gICAgY29sb3I6ICM3MTgwOTY7XG5cbiAgICBpIHtcbiAgICAgIGZvbnQtc2l6ZTogMTBweDtcbiAgICB9XG4gIH1cblxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBnYXA6IDhweDtcblxuICAgIC5hbmFseXRpY3MtaXRlbSB7XG4gICAgICBmb250LXNpemU6IDEwcHg7XG4gICAgfVxuICB9XG59XG5cbi8vIExvYWRpbmcgU3RhdGVzXG4ubG9hZGluZy1jb250YWluZXIge1xuICAubG9hZGluZy1ncmlkIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGdhcDogMjBweDtcbiAgICBvdmVyZmxvdy14OiBhdXRvO1xuICAgIHBhZGRpbmc6IDAgNDBweDtcbiAgICBzY3JvbGwtYmVoYXZpb3I6IHNtb290aDtcblxuICAgIC8qIEhpZGUgc2Nyb2xsYmFyICovXG4gICAgc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xuICAgIC1tcy1vdmVyZmxvdy1zdHlsZTogbm9uZTtcbiAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgICBkaXNwbGF5OiBub25lO1xuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgZ2FwOiAxMnB4O1xuICAgICAgcGFkZGluZzogMCAyMHB4O1xuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAgICAgcGFkZGluZzogMCAxNnB4O1xuICAgIH1cbiAgfVxuXG4gIC5wcm9kdWN0LXNrZWxldG9uIHtcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgYW5pbWF0aW9uOiBwdWxzZSAxLjVzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICAgIG1pbi13aWR0aDogMjgwcHg7IC8qIENvbnNpc3RlbnQgd2lkdGggZm9yIHNrZWxldG9uIGNhcmRzICovXG4gICAgZmxleC1zaHJpbms6IDA7XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIG1pbi13aWR0aDogMjAwcHg7XG4gICAgfVxuXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gICAgICBtaW4td2lkdGg6IDE2MHB4O1xuICAgIH1cblxuICAgIC5za2VsZXRvbi1pbWFnZSB7XG4gICAgICBhc3BlY3QtcmF0aW86IDE7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICNmMGYwZjAgMjUlLCAjZTBlMGUwIDUwJSwgI2YwZjBmMCA3NSUpO1xuICAgICAgYmFja2dyb3VuZC1zaXplOiAyMDAlIDEwMCU7XG4gICAgICBhbmltYXRpb246IGxvYWRpbmcgMS41cyBpbmZpbml0ZTtcbiAgICB9XG5cbiAgICAuc2tlbGV0b24tY29udGVudCB7XG4gICAgICBwYWRkaW5nOiAxNnB4O1xuXG4gICAgICAuc2tlbGV0b24tbGluZSB7XG4gICAgICAgIGhlaWdodDogMTJweDtcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjZjBmMGYwIDI1JSwgI2UwZTBlMCA1MCUsICNmMGYwZjAgNzUlKTtcbiAgICAgICAgYmFja2dyb3VuZC1zaXplOiAyMDAlIDEwMCU7XG4gICAgICAgIGFuaW1hdGlvbjogbG9hZGluZyAxLjVzIGluZmluaXRlO1xuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcblxuICAgICAgICAmLnNob3J0IHtcbiAgICAgICAgICB3aWR0aDogNjAlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbkBrZXlmcmFtZXMgbG9hZGluZyB7XG4gIDAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMjAwJSAwOyB9XG4gIDEwMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAtMjAwJSAwOyB9XG59XG5cbkBrZXlmcmFtZXMgcHVsc2Uge1xuICAwJSwgMTAwJSB7IG9wYWNpdHk6IDE7IH1cbiAgNTAlIHsgb3BhY2l0eTogMC44OyB9XG59XG5cbi8vIExvYWQgTW9yZSBCdXR0b24gU3R5bGluZ1xuLmxvYWQtbW9yZS1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgbWFyZ2luLXRvcDogMjRweDtcblxuICAubG9hZC1tb3JlLWJ0biB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSwgIzc2NGJhMik7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGJvcmRlci1yYWRpdXM6IDI1cHg7XG4gICAgcGFkZGluZzogMTJweCAyNHB4O1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDhweDtcbiAgICBmb250LXNpemU6IDE0cHg7XG5cbiAgICAmOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpO1xuICAgIH1cblxuICAgICY6ZGlzYWJsZWQge1xuICAgICAgb3BhY2l0eTogMC43O1xuICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcbiAgICAgIHRyYW5zZm9ybTogbm9uZTtcbiAgICB9XG5cbiAgICBpLmZhLXNwaW5uZXIge1xuICAgICAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIHBhZGRpbmc6IDEwcHggMjBweDtcbiAgICAgIGZvbnQtc2l6ZTogMTNweDtcbiAgICB9XG4gIH1cbn1cblxuQGtleWZyYW1lcyBzcGluIHtcbiAgZnJvbSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XG4gIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxufVxuXG4vLyBFcnJvciBhbmQgRW1wdHkgU3RhdGVzXG4uZXJyb3ItY29udGFpbmVyLFxuLmVtcHR5LWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtaW4taGVpZ2h0OiAzMDBweDtcblxuICAuZXJyb3ItY29udGVudCxcbiAgLmVtcHR5LWNvbnRlbnQge1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICBtYXgtd2lkdGg6IDQwMHB4O1xuXG4gICAgaSB7XG4gICAgICBmb250LXNpemU6IDQ4cHg7XG4gICAgICBjb2xvcjogI2EwYWVjMDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gICAgfVxuXG4gICAgaDMge1xuICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGNvbG9yOiAjMmQzNzQ4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgIH1cblxuICAgIHAge1xuICAgICAgY29sb3I6ICM3MTgwOTY7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICAgIH1cblxuICAgIC5yZXRyeS1idG4sXG4gICAgLmJyb3dzZS1idG4ge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSwgIzc2NGJhMik7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICAgICAgcGFkZGluZzogMTJweCAyNHB4O1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBMb2FkIE1vcmVcbi5sb2FkLW1vcmUtY29udGFpbmVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIG1hcmdpbi10b3A6IDMycHg7XG5cbiAgLmxvYWQtbW9yZS1idG4ge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBib3JkZXItcmFkaXVzOiAyNXB4O1xuICAgIHBhZGRpbmc6IDEycHggMzJweDtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICY6aG92ZXI6bm90KDpkaXNhYmxlZCkge1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuNCk7XG4gICAgfVxuXG4gICAgJjpkaXNhYmxlZCB7XG4gICAgICBvcGFjaXR5OiAwLjc7XG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgIH1cblxuICAgIGkge1xuICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "SuggestedProductsComponent_div_8_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "SuggestedProductsComponent_div_9_Template_button_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "retry", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r4", "formatPrice", "originalPrice", "ɵɵclassProp", "star_r5", "rating", "average", "SuggestedProductsComponent_div_10_swiper_slide_2_div_24_i_2_Template", "_c1", "ɵɵtextInterpolate2", "formatNumber", "count", "SuggestedProductsComponent_div_10_swiper_slide_2_Template_div_click_1_listener", "_r3", "$implicit", "viewProduct", "SuggestedProductsComponent_div_10_swiper_slide_2_div_4_Template", "SuggestedProductsComponent_div_10_swiper_slide_2_Template_button_click_5_listener", "$event", "toggleWishlist", "SuggestedProductsComponent_div_10_swiper_slide_2_Template_button_click_8_listener", "addToCart", "SuggestedProductsComponent_div_10_swiper_slide_2_Template_button_click_10_listener", "shareProduct", "SuggestedProductsComponent_div_10_swiper_slide_2_Template_div_click_13_listener", "viewVendor", "vendor", "stopPropagation", "SuggestedProductsComponent_div_10_swiper_slide_2_i_17_Template", "SuggestedProductsComponent_div_10_swiper_slide_2_span_23_Template", "SuggestedProductsComponent_div_10_swiper_slide_2_div_24_Template", "images", "url", "ɵɵsanitizeUrl", "name", "avatar", "fullName", "username", "isInfluencer", "price", "analytics", "views", "likes", "purchases", "SuggestedProductsComponent_div_10_swiper_slide_2_Template", "swiperConfig", "spaceBetween", "navigation", "autoplay", "loop", "breakpoints", "products", "trackByProductId", "SuggestedProductsComponent_div_11_Template_button_click_1_listener", "_r6", "loadMore", "SuggestedProductsComponent_div_11_span_2_Template", "SuggestedProductsComponent_div_11_span_3_Template", "isLoading", "SuggestedProductsComponent_div_12_Template_button_click_7_listener", "_r7", "router", "navigate", "SuggestedProductsComponent", "constructor", "http", "currentPage", "totalPages", "hasMore", "<PERSON><PERSON><PERSON><PERSON>iew", "delay", "disableOnInteraction", "pauseOnMouseEnter", "freeMode", "subscriptions", "ngOnInit", "loadSuggestedProducts", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "page", "push", "get", "apiUrl", "subscribe", "next", "response", "success", "pagination", "pages", "loadFallbackProducts", "console", "_id", "discount", "isPrimary", "shares", "product", "event", "log", "Math", "round", "Intl", "NumberFormat", "style", "currency", "format", "num", "toFixed", "toString", "index", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SuggestedProductsComponent_Template", "rf", "ctx", "SuggestedProductsComponent_Template_button_click_5_listener", "queryParams", "filter", "SuggestedProductsComponent_div_8_Template", "SuggestedProductsComponent_div_9_Template", "SuggestedProductsComponent_div_10_Template", "SuggestedProductsComponent_div_11_Template", "SuggestedProductsComponent_div_12_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\suggested-products\\suggested-products.component.ts", "E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\suggested-products\\suggested-products.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\n\ninterface Product {\n  _id: string;\n  name: string;\n  price: number;\n  originalPrice?: number;\n  discount?: number;\n  images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;\n  vendor: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n    isInfluencer: boolean;\n  };\n  createdBy?: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n    isInfluencer: boolean;\n  };\n  analytics: {\n    views: number;\n    likes: number;\n    shares: number;\n    purchases: number;\n  };\n  rating?: {\n    average: number;\n    count: number;\n  };\n}\n\n@Component({\n  selector: 'app-suggested-products',\n  standalone: true,\n  imports: [CommonModule],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n  templateUrl: './suggested-products.component.html',\n  styleUrls: ['./suggested-products.component.scss']\n})\nexport class SuggestedProductsComponent implements OnInit, OnDestroy {\n  products: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  currentPage = 1;\n  totalPages = 1;\n  hasMore = false;\n\n  // Swiper configuration for products slider\n  swiperConfig = {\n    slidesPerView: 'auto', // Let CSS control the width\n    spaceBetween: 20,\n    navigation: true,\n    autoplay: {\n      delay: 4000,\n      disableOnInteraction: false,\n      pauseOnMouseEnter: true\n    },\n    loop: false, // Don't loop for products to show clear start/end\n    freeMode: true, // Allow free scrolling\n    breakpoints: {\n      0: {\n        slidesPerView: 'auto',\n        spaceBetween: 12,\n        freeMode: true\n      },\n      480: {\n        slidesPerView: 'auto',\n        spaceBetween: 16,\n        freeMode: true\n      },\n      768: {\n        slidesPerView: 'auto',\n        spaceBetween: 18,\n        freeMode: false\n      },\n      1024: {\n        slidesPerView: 'auto',\n        spaceBetween: 20,\n        freeMode: false\n      }\n    }\n  };\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    public router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  loadSuggestedProducts(page: number = 1) {\n    this.isLoading = true;\n    this.error = null;\n\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/products/suggested?page=${page}&limit=12`).subscribe({\n        next: (response) => {\n          if (response.success) {\n            if (page === 1) {\n              this.products = response.products;\n            } else {\n              this.products = [...this.products, ...response.products];\n            }\n            \n            this.currentPage = response.pagination.page;\n            this.totalPages = response.pagination.pages;\n            this.hasMore = this.currentPage < this.totalPages;\n          } else {\n            this.loadFallbackProducts();\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading suggested products:', error);\n          if (page === 1) {\n            this.loadFallbackProducts();\n          }\n          this.error = 'Failed to load suggested products';\n          this.isLoading = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackProducts() {\n    this.products = [\n      {\n        _id: '1',\n        name: 'Vintage Denim Jacket',\n        price: 89.99,\n        originalPrice: 129.99,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400', isPrimary: true }],\n        vendor: {\n          _id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',\n          isInfluencer: true\n        },\n        analytics: { views: 15420, likes: 892, shares: 156, purchases: 234 },\n        rating: { average: 4.2, count: 156 }\n      },\n      {\n        _id: '2',\n        name: 'Silk Slip Dress',\n        price: 159.99,\n        originalPrice: 199.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400', isPrimary: true }],\n        vendor: {\n          _id: '2',\n          username: 'style_guru_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n          isInfluencer: true\n        },\n        analytics: { views: 12890, likes: 1205, shares: 289, purchases: 167 },\n        rating: { average: 4.5, count: 89 }\n      },\n      {\n        _id: '3',\n        name: 'Chunky Knit Sweater',\n        price: 79.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400', isPrimary: true }],\n        vendor: {\n          _id: '3',\n          username: 'beauty_by_sarah',\n          fullName: 'Sarah Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n          isInfluencer: true\n        },\n        analytics: { views: 8945, likes: 567, shares: 89, purchases: 123 },\n        rating: { average: 4.7, count: 67 }\n      }\n    ];\n  }\n\n  loadMore() {\n    if (this.hasMore && !this.isLoading) {\n      this.loadSuggestedProducts(this.currentPage + 1);\n    }\n  }\n\n  viewProduct(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  viewVendor(vendor: any) {\n    this.router.navigate(['/vendor', vendor.username]);\n  }\n\n  addToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement add to cart functionality\n    console.log('Add to cart:', product);\n  }\n\n  toggleWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement wishlist functionality\n    console.log('Toggle wishlist:', product);\n  }\n\n  shareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement share functionality\n    console.log('Share product:', product);\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return product.discount || 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  retry() {\n    this.loadSuggestedProducts(1);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n}\n", "<div class=\"suggested-products-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <h2 class=\"section-title\">\n      <i class=\"fas fa-star\"></i>\n      Suggested For You\n    </h2>\n    <button class=\"view-all-btn\" (click)=\"router.navigate(['/shop'], { queryParams: { filter: 'suggested' } })\">\n      View All\n      <i class=\"fas fa-arrow-right\"></i>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div class=\"loading-container\" *ngIf=\"isLoading && products.length === 0\">\n    <div class=\"loading-grid\">\n      <div class=\"product-skeleton\" *ngFor=\"let item of [1,2,3,4,5,6]\">\n        <div class=\"skeleton-image\"></div>\n        <div class=\"skeleton-content\">\n          <div class=\"skeleton-line\"></div>\n          <div class=\"skeleton-line short\"></div>\n          <div class=\"skeleton-line\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div class=\"error-container\" *ngIf=\"error && products.length === 0\">\n    <div class=\"error-content\">\n      <i class=\"fas fa-exclamation-triangle\"></i>\n      <h3>Unable to load suggested products</h3>\n      <p>{{ error }}</p>\n      <button class=\"retry-btn\" (click)=\"retry()\">\n        <i class=\"fas fa-redo\"></i>\n        Try Again\n      </button>\n    </div>\n  </div>\n\n  <!-- Products Slider -->\n  <div class=\"products-slider-wrapper\" *ngIf=\"products.length > 0\">\n    <swiper-container\n      class=\"products-swiper\"\n      slides-per-view=\"auto\"\n      [space-between]=\"swiperConfig.spaceBetween\"\n      [navigation]=\"swiperConfig.navigation\"\n      [autoplay]=\"swiperConfig.autoplay\"\n      [loop]=\"swiperConfig.loop\"\n      [free-mode]=\"true\"\n      [breakpoints]=\"swiperConfig.breakpoints\">\n\n      <swiper-slide *ngFor=\"let product of products; trackBy: trackByProductId\">\n        <div class=\"product-card\" (click)=\"viewProduct(product)\">\n      \n      <!-- Product Image -->\n      <div class=\"product-image-container\">\n        <img [src]=\"product.images[0].url\"\n             [alt]=\"product.name\"\n             class=\"product-image\"\n             loading=\"lazy\">\n        \n        <!-- Discount Badge -->\n        <div class=\"discount-badge\" *ngIf=\"getDiscountPercentage(product) > 0\">\n          -{{ getDiscountPercentage(product) }}%\n        </div>\n\n        <!-- Wishlist Button -->\n        <button class=\"wishlist-btn\" \n                (click)=\"toggleWishlist(product, $event)\"\n                title=\"Add to wishlist\">\n          <i class=\"far fa-heart\"></i>\n        </button>\n\n        <!-- Quick Actions -->\n        <div class=\"quick-actions\">\n          <button class=\"quick-action-btn\" \n                  (click)=\"addToCart(product, $event)\"\n                  title=\"Add to cart\">\n            <i class=\"fas fa-shopping-cart\"></i>\n          </button>\n          <button class=\"quick-action-btn\" \n                  (click)=\"shareProduct(product, $event)\"\n                  title=\"Share\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Product Info -->\n      <div class=\"product-info\">\n        <!-- Vendor Info -->\n        <div class=\"vendor-info\" (click)=\"viewVendor(product.vendor); $event.stopPropagation()\">\n          <img [src]=\"product.vendor.avatar\" \n               [alt]=\"product.vendor.fullName\"\n               class=\"vendor-avatar\">\n          <span class=\"vendor-name\">{{ product.vendor.username }}</span>\n          <i class=\"fas fa-check-circle verified-icon\" \n             *ngIf=\"product.vendor.isInfluencer\"\n             title=\"Verified Influencer\"></i>\n        </div>\n\n        <!-- Product Name -->\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n\n        <!-- Price -->\n        <div class=\"price-container\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span class=\"original-price\" *ngIf=\"product.originalPrice\">\n            {{ formatPrice(product.originalPrice) }}\n          </span>\n        </div>\n\n        <!-- Rating -->\n        <div class=\"rating-container\" *ngIf=\"product.rating\">\n          <div class=\"stars\">\n            <i class=\"fas fa-star\" \n               *ngFor=\"let star of [1,2,3,4,5]\"\n               [class.filled]=\"star <= product.rating.average\"></i>\n          </div>\n          <span class=\"rating-text\">\n            {{ product.rating.average }} ({{ formatNumber(product.rating.count) }})\n          </span>\n        </div>\n\n        <!-- Analytics -->\n        <div class=\"analytics-container\">\n          <div class=\"analytics-item\">\n            <i class=\"fas fa-eye\"></i>\n            <span>{{ formatNumber(product.analytics.views) }}</span>\n          </div>\n          <div class=\"analytics-item\">\n            <i class=\"fas fa-heart\"></i>\n            <span>{{ formatNumber(product.analytics.likes) }}</span>\n          </div>\n          <div class=\"analytics-item\">\n            <i class=\"fas fa-shopping-bag\"></i>\n            <span>{{ formatNumber(product.analytics.purchases) }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </swiper-slide>\n    </swiper-container>\n  </div>\n\n  <!-- Load More Button -->\n  <div class=\"load-more-container\" *ngIf=\"hasMore\">\n    <button class=\"load-more-btn\" \n            (click)=\"loadMore()\"\n            [disabled]=\"isLoading\">\n      <span *ngIf=\"!isLoading\">Load More Products</span>\n      <span *ngIf=\"isLoading\">\n        <i class=\"fas fa-spinner fa-spin\"></i>\n        Loading...\n      </span>\n    </button>\n  </div>\n\n  <!-- Empty State -->\n  <div class=\"empty-container\" *ngIf=\"products.length === 0 && !isLoading && !error\">\n    <div class=\"empty-content\">\n      <i class=\"fas fa-shopping-bag\"></i>\n      <h3>No suggested products available</h3>\n      <p>Check back later for personalized product recommendations!</p>\n      <button class=\"browse-btn\" (click)=\"router.navigate(['/shop'])\">\n        Browse All Products\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAI9C,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;ICWpDC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAAkC;IAClCF,EAAA,CAAAC,cAAA,cAA8B;IAG5BD,EAFA,CAAAE,SAAA,cAAiC,cACM,cACN;IAErCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARRH,EADF,CAAAC,cAAA,cAA0E,cAC9C;IACxBD,EAAA,CAAAI,UAAA,IAAAC,+CAAA,kBAAiE;IASrEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAT6CH,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAgB;;;;;;IAajET,EADF,CAAAC,cAAA,cAAoE,cACvC;IACzBD,EAAA,CAAAE,SAAA,YAA2C;IAC3CF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,wCAAiC;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAA4C;IAAlBD,EAAA,CAAAW,UAAA,mBAAAC,kEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IACzClB,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAU,MAAA,kBACF;IAEJV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IANCH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IA+BZpB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,OAAAN,MAAA,CAAAO,qBAAA,CAAAC,UAAA,QACF;;;;;IAgCEvB,EAAA,CAAAE,SAAA,YAEmC;;;;;IASnCF,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAN,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAAE,aAAA,OACF;;;;;IAMEzB,EAAA,CAAAE,SAAA,WAEuD;;;;;IAApDF,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,CAA+C;;;;;IAHpD7B,EADF,CAAAC,cAAA,cAAqD,cAChC;IACjBD,EAAA,CAAAI,UAAA,IAAA0B,oEAAA,gBAEmD;IACrD9B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IANkBH,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAuB,GAAA,EAAc;IAIlC/B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAgC,kBAAA,MAAAT,UAAA,CAAAK,MAAA,CAAAC,OAAA,QAAAd,MAAA,CAAAkB,YAAA,CAAAV,UAAA,CAAAK,MAAA,CAAAM,KAAA,QACF;;;;;;IArEFlC,EADF,CAAAC,cAAA,mBAA0E,cACf;IAA/BD,EAAA,CAAAW,UAAA,mBAAAwB,+EAAA;MAAA,MAAAZ,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAuB,WAAA,CAAAf,UAAA,CAAoB;IAAA,EAAC;IAG1DvB,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAGoB;IAGpBF,EAAA,CAAAI,UAAA,IAAAmC,+DAAA,kBAAuE;IAKvEvC,EAAA,CAAAC,cAAA,iBAEgC;IADxBD,EAAA,CAAAW,UAAA,mBAAA6B,kFAAAC,MAAA;MAAA,MAAAlB,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA2B,cAAA,CAAAnB,UAAA,EAAAkB,MAAA,CAA+B;IAAA,EAAC;IAE/CzC,EAAA,CAAAE,SAAA,YAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IAIPH,EADF,CAAAC,cAAA,cAA2B,iBAGG;IADpBD,EAAA,CAAAW,UAAA,mBAAAgC,kFAAAF,MAAA;MAAA,MAAAlB,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA6B,SAAA,CAAArB,UAAA,EAAAkB,MAAA,CAA0B;IAAA,EAAC;IAE1CzC,EAAA,CAAAE,SAAA,YAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsB;IADdD,EAAA,CAAAW,UAAA,mBAAAkC,mFAAAJ,MAAA;MAAA,MAAAlB,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA+B,YAAA,CAAAvB,UAAA,EAAAkB,MAAA,CAA6B;IAAA,EAAC;IAE7CzC,EAAA,CAAAE,SAAA,aAA4B;IAGlCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAKJH,EAFF,CAAAC,cAAA,eAA0B,eAEgE;IAA/DD,EAAA,CAAAW,UAAA,mBAAAoC,gFAAAN,MAAA;MAAA,MAAAlB,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAASD,MAAA,CAAAiC,UAAA,CAAAzB,UAAA,CAAA0B,MAAA,CAA0B;MAAA,OAAAjD,EAAA,CAAAiB,WAAA,CAAEwB,MAAA,CAAAS,eAAA,EAAwB;IAAA,EAAC;IACrFlD,EAAA,CAAAE,SAAA,eAE2B;IAC3BF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAA6B;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAI,UAAA,KAAA+C,8DAAA,gBAE+B;IACjCnD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAI9CH,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAgD,iEAAA,mBAA2D;IAG7DpD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,KAAAiD,gEAAA,kBAAqD;IAanDrD,EADF,CAAAC,cAAA,eAAiC,eACH;IAC1BD,EAAA,CAAAE,SAAA,aAA0B;IAC1BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA2C;IACnDV,EADmD,CAAAG,YAAA,EAAO,EACpD;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA2C;IACnDV,EADmD,CAAAG,YAAA,EAAO,EACpD;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA+C;IAK/DV,EAL+D,CAAAG,YAAA,EAAO,EACxD,EACF,EACF,EACF,EACO;;;;;IArFJH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAgB,UAAA,CAAA+B,MAAA,IAAAC,GAAA,EAAAvD,EAAA,CAAAwD,aAAA,CAA6B,QAAAjC,UAAA,CAAAkC,IAAA,CACT;IAKIzD,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAO,qBAAA,CAAAC,UAAA,MAAwC;IA8B9DvB,EAAA,CAAAM,SAAA,IAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAgB,UAAA,CAAA0B,MAAA,CAAAS,MAAA,EAAA1D,EAAA,CAAAwD,aAAA,CAA6B,QAAAjC,UAAA,CAAA0B,MAAA,CAAAU,QAAA,CACE;IAEV3D,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA0B,MAAA,CAAAW,QAAA,CAA6B;IAEnD5D,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,UAAA,SAAAgB,UAAA,CAAA0B,MAAA,CAAAY,YAAA,CAAiC;IAKd7D,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAAkC,IAAA,CAAkB;IAIbzD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAAuC,KAAA,EAAgC;IAC9B9D,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,SAAAgB,UAAA,CAAAE,aAAA,CAA2B;IAM5BzB,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAO,UAAA,SAAAgB,UAAA,CAAAK,MAAA,CAAoB;IAezC5B,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAkB,YAAA,CAAAV,UAAA,CAAAwC,SAAA,CAAAC,KAAA,EAA2C;IAI3ChE,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAkB,YAAA,CAAAV,UAAA,CAAAwC,SAAA,CAAAE,KAAA,EAA2C;IAI3CjE,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAkB,YAAA,CAAAV,UAAA,CAAAwC,SAAA,CAAAG,SAAA,EAA+C;;;;;IA/F7DlE,EADF,CAAAC,cAAA,cAAiE,2BASpB;IAEzCD,EAAA,CAAAI,UAAA,IAAA+D,yDAAA,6BAA0E;IA4F9EnE,EADE,CAAAG,YAAA,EAAmB,EACf;;;;IAnGFH,EAAA,CAAAM,SAAA,EAA2C;IAK3CN,EALA,CAAAO,UAAA,kBAAAQ,MAAA,CAAAqD,YAAA,CAAAC,YAAA,CAA2C,eAAAtD,MAAA,CAAAqD,YAAA,CAAAE,UAAA,CACL,aAAAvD,MAAA,CAAAqD,YAAA,CAAAG,QAAA,CACJ,SAAAxD,MAAA,CAAAqD,YAAA,CAAAI,IAAA,CACR,mBACR,gBAAAzD,MAAA,CAAAqD,YAAA,CAAAK,WAAA,CACsB;IAENzE,EAAA,CAAAM,SAAA,EAAa;IAAAN,EAAb,CAAAO,UAAA,YAAAQ,MAAA,CAAA2D,QAAA,CAAa,iBAAA3D,MAAA,CAAA4D,gBAAA,CAAyB;;;;;IAmGxE3E,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAU,MAAA,yBAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;;IAClDH,EAAA,CAAAC,cAAA,WAAwB;IACtBD,EAAA,CAAAE,SAAA,YAAsC;IACtCF,EAAA,CAAAU,MAAA,mBACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;;;IAPTH,EADF,CAAAC,cAAA,cAAiD,iBAGhB;IADvBD,EAAA,CAAAW,UAAA,mBAAAiE,mEAAA;MAAA5E,EAAA,CAAAa,aAAA,CAAAgE,GAAA;MAAA,MAAA9D,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA+D,QAAA,EAAU;IAAA,EAAC;IAG1B9E,EADA,CAAAI,UAAA,IAAA2E,iDAAA,mBAAyB,IAAAC,iDAAA,mBACD;IAK5BhF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAPIH,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAAkE,SAAA,CAAsB;IACrBjF,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,UAAAQ,MAAA,CAAAkE,SAAA,CAAgB;IAChBjF,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAkE,SAAA,CAAe;;;;;;IASxBjF,EADF,CAAAC,cAAA,cAAmF,cACtD;IACzBD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,sCAA+B;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,iEAA0D;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACjEH,EAAA,CAAAC,cAAA,iBAAgE;IAArCD,EAAA,CAAAW,UAAA,mBAAAuE,mEAAA;MAAAlF,EAAA,CAAAa,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqE,MAAA,CAAAC,QAAA,EAAiB,OAAO,EAAE;IAAA,EAAC;IAC7DrF,EAAA,CAAAU,MAAA,4BACF;IAEJV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;ADzHR,OAAM,MAAOmF,0BAA0B;EA8CrCC,YACSH,MAAc,EACbI,IAAgB;IADjB,KAAAJ,MAAM,GAANA,MAAM;IACL,KAAAI,IAAI,GAAJA,IAAI;IA/Cd,KAAAd,QAAQ,GAAc,EAAE;IACxB,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAA7D,KAAK,GAAkB,IAAI;IAC3B,KAAAqE,WAAW,GAAG,CAAC;IACf,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAvB,YAAY,GAAG;MACbwB,aAAa,EAAE,MAAM;MACrBvB,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACRsB,KAAK,EAAE,IAAI;QACXC,oBAAoB,EAAE,KAAK;QAC3BC,iBAAiB,EAAE;OACpB;MACDvB,IAAI,EAAE,KAAK;MACXwB,QAAQ,EAAE,IAAI;MACdvB,WAAW,EAAE;QACX,CAAC,EAAE;UACDmB,aAAa,EAAE,MAAM;UACrBvB,YAAY,EAAE,EAAE;UAChB2B,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHJ,aAAa,EAAE,MAAM;UACrBvB,YAAY,EAAE,EAAE;UAChB2B,QAAQ,EAAE;SACX;QACD,GAAG,EAAE;UACHJ,aAAa,EAAE,MAAM;UACrBvB,YAAY,EAAE,EAAE;UAChB2B,QAAQ,EAAE;SACX;QACD,IAAI,EAAE;UACJJ,aAAa,EAAE,MAAM;UACrBvB,YAAY,EAAE,EAAE;UAChB2B,QAAQ,EAAE;;;KAGf;IAEO,KAAAC,aAAa,GAAmB,EAAE;EAKvC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,aAAa,CAACI,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEAJ,qBAAqBA,CAACK,IAAA,GAAe,CAAC;IACpC,IAAI,CAACvB,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC7D,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC6E,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACjB,IAAI,CAACkB,GAAG,CAAM,GAAG3G,WAAW,CAAC4G,MAAM,4BAA4BH,IAAI,WAAW,CAAC,CAACI,SAAS,CAAC;MAC7FC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAIP,IAAI,KAAK,CAAC,EAAE;YACd,IAAI,CAAC9B,QAAQ,GAAGoC,QAAQ,CAACpC,QAAQ;WAClC,MAAM;YACL,IAAI,CAACA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,GAAGoC,QAAQ,CAACpC,QAAQ,CAAC;;UAG1D,IAAI,CAACe,WAAW,GAAGqB,QAAQ,CAACE,UAAU,CAACR,IAAI;UAC3C,IAAI,CAACd,UAAU,GAAGoB,QAAQ,CAACE,UAAU,CAACC,KAAK;UAC3C,IAAI,CAACtB,OAAO,GAAG,IAAI,CAACF,WAAW,GAAG,IAAI,CAACC,UAAU;SAClD,MAAM;UACL,IAAI,CAACwB,oBAAoB,EAAE;;QAE7B,IAAI,CAACjC,SAAS,GAAG,KAAK;MACxB,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACf+F,OAAO,CAAC/F,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAIoF,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAACU,oBAAoB,EAAE;;QAE7B,IAAI,CAAC9F,KAAK,GAAG,mCAAmC;QAChD,IAAI,CAAC6D,SAAS,GAAG,KAAK;MACxB;KACD,CAAC,CACH;EACH;EAEAiC,oBAAoBA,CAAA;IAClB,IAAI,CAACxC,QAAQ,GAAG,CACd;MACE0C,GAAG,EAAE,GAAG;MACR3D,IAAI,EAAE,sBAAsB;MAC5BK,KAAK,EAAE,KAAK;MACZrC,aAAa,EAAE,MAAM;MACrB4F,QAAQ,EAAE,EAAE;MACZ/D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,iEAAiE;QAAE+D,SAAS,EAAE;MAAI,CAAE,CAAC;MACrGrE,MAAM,EAAE;QACNmE,GAAG,EAAE,GAAG;QACRxD,QAAQ,EAAE,kBAAkB;QAC5BD,QAAQ,EAAE,gBAAgB;QAC1BD,MAAM,EAAE,oEAAoE;QAC5EG,YAAY,EAAE;OACf;MACDE,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,GAAG;QAAEsD,MAAM,EAAE,GAAG;QAAErD,SAAS,EAAE;MAAG,CAAE;MACpEtC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEK,KAAK,EAAE;MAAG;KACnC,EACD;MACEkF,GAAG,EAAE,GAAG;MACR3D,IAAI,EAAE,iBAAiB;MACvBK,KAAK,EAAE,MAAM;MACbrC,aAAa,EAAE,MAAM;MACrB4F,QAAQ,EAAE,EAAE;MACZ/D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAE+D,SAAS,EAAE;MAAI,CAAE,CAAC;MACxGrE,MAAM,EAAE;QACNmE,GAAG,EAAE,GAAG;QACRxD,QAAQ,EAAE,iBAAiB;QAC3BD,QAAQ,EAAE,WAAW;QACrBD,MAAM,EAAE,oEAAoE;QAC5EG,YAAY,EAAE;OACf;MACDE,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,IAAI;QAAEsD,MAAM,EAAE,GAAG;QAAErD,SAAS,EAAE;MAAG,CAAE;MACrEtC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEK,KAAK,EAAE;MAAE;KAClC,EACD;MACEkF,GAAG,EAAE,GAAG;MACR3D,IAAI,EAAE,qBAAqB;MAC3BK,KAAK,EAAE,KAAK;MACZR,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAE+D,SAAS,EAAE;MAAI,CAAE,CAAC;MACxGrE,MAAM,EAAE;QACNmE,GAAG,EAAE,GAAG;QACRxD,QAAQ,EAAE,iBAAiB;QAC3BD,QAAQ,EAAE,eAAe;QACzBD,MAAM,EAAE,oEAAoE;QAC5EG,YAAY,EAAE;OACf;MACDE,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,GAAG;QAAEsD,MAAM,EAAE,EAAE;QAAErD,SAAS,EAAE;MAAG,CAAE;MAClEtC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEK,KAAK,EAAE;MAAE;KAClC,CACF;EACH;EAEA4C,QAAQA,CAAA;IACN,IAAI,IAAI,CAACa,OAAO,IAAI,CAAC,IAAI,CAACV,SAAS,EAAE;MACnC,IAAI,CAACkB,qBAAqB,CAAC,IAAI,CAACV,WAAW,GAAG,CAAC,CAAC;;EAEpD;EAEAnD,WAAWA,CAACkF,OAAgB;IAC1B,IAAI,CAACpC,MAAM,CAACC,QAAQ,CAAC,CAAC,UAAU,EAAEmC,OAAO,CAACJ,GAAG,CAAC,CAAC;EACjD;EAEApE,UAAUA,CAACC,MAAW;IACpB,IAAI,CAACmC,MAAM,CAACC,QAAQ,CAAC,CAAC,SAAS,EAAEpC,MAAM,CAACW,QAAQ,CAAC,CAAC;EACpD;EAEAhB,SAASA,CAAC4E,OAAgB,EAAEC,KAAY;IACtCA,KAAK,CAACvE,eAAe,EAAE;IACvB;IACAiE,OAAO,CAACO,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;EACtC;EAEA9E,cAAcA,CAAC8E,OAAgB,EAAEC,KAAY;IAC3CA,KAAK,CAACvE,eAAe,EAAE;IACvB;IACAiE,OAAO,CAACO,GAAG,CAAC,kBAAkB,EAAEF,OAAO,CAAC;EAC1C;EAEA1E,YAAYA,CAAC0E,OAAgB,EAAEC,KAAY;IACzCA,KAAK,CAACvE,eAAe,EAAE;IACvB;IACAiE,OAAO,CAACO,GAAG,CAAC,gBAAgB,EAAEF,OAAO,CAAC;EACxC;EAEAlG,qBAAqBA,CAACkG,OAAgB;IACpC,IAAIA,OAAO,CAAC/F,aAAa,IAAI+F,OAAO,CAAC/F,aAAa,GAAG+F,OAAO,CAAC1D,KAAK,EAAE;MAClE,OAAO6D,IAAI,CAACC,KAAK,CAAE,CAACJ,OAAO,CAAC/F,aAAa,GAAG+F,OAAO,CAAC1D,KAAK,IAAI0D,OAAO,CAAC/F,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO+F,OAAO,CAACH,QAAQ,IAAI,CAAC;EAC9B;EAEA7F,WAAWA,CAACsC,KAAa;IACvB,OAAO,IAAI+D,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACnE,KAAK,CAAC;EAClB;EAEA7B,YAAYA,CAACiG,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAlH,KAAKA,CAAA;IACH,IAAI,CAACiF,qBAAqB,CAAC,CAAC,CAAC;EAC/B;EAEAxB,gBAAgBA,CAAC0D,KAAa,EAAEb,OAAgB;IAC9C,OAAOA,OAAO,CAACJ,GAAG;EACpB;;;uBAhNW9B,0BAA0B,EAAAtF,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxI,EAAA,CAAAsI,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAA1BpD,0BAA0B;MAAAqD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7I,EAAA,CAAA8I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7CnCpJ,EAHJ,CAAAC,cAAA,aAA0C,aAEZ,YACA;UACxBD,EAAA,CAAAE,SAAA,WAA2B;UAC3BF,EAAA,CAAAU,MAAA,0BACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,gBAA4G;UAA/ED,EAAA,CAAAW,UAAA,mBAAA2I,4DAAA;YAAA,OAASD,GAAA,CAAAjE,MAAA,CAAAC,QAAA,EAAiB,OAAO,GAAG;cAAAkE,WAAA,EAAe;gBAAAC,MAAA,EAAU;cAAW;YAAE,CAAE,CAAC;UAAA,EAAC;UACzGxJ,EAAA,CAAAU,MAAA,iBACA;UAAAV,EAAA,CAAAE,SAAA,WAAkC;UAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;UAqJNH,EAlJA,CAAAI,UAAA,IAAAqJ,yCAAA,iBAA0E,IAAAC,yCAAA,kBAcN,KAAAC,0CAAA,iBAaH,KAAAC,0CAAA,iBA0GhB,KAAAC,0CAAA,kBAakC;UAUrF7J,EAAA,CAAAG,YAAA,EAAM;;;UA5J4BH,EAAA,CAAAM,SAAA,GAAwC;UAAxCN,EAAA,CAAAO,UAAA,SAAA8I,GAAA,CAAApE,SAAA,IAAAoE,GAAA,CAAA3E,QAAA,CAAAoF,MAAA,OAAwC;UAc1C9J,EAAA,CAAAM,SAAA,EAAoC;UAApCN,EAAA,CAAAO,UAAA,SAAA8I,GAAA,CAAAjI,KAAA,IAAAiI,GAAA,CAAA3E,QAAA,CAAAoF,MAAA,OAAoC;UAa5B9J,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAA8I,GAAA,CAAA3E,QAAA,CAAAoF,MAAA,KAAyB;UA0G7B9J,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAO,UAAA,SAAA8I,GAAA,CAAA1D,OAAA,CAAa;UAajB3F,EAAA,CAAAM,SAAA,EAAmD;UAAnDN,EAAA,CAAAO,UAAA,SAAA8I,GAAA,CAAA3E,QAAA,CAAAoF,MAAA,WAAAT,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAjI,KAAA,CAAmD;;;qBDrHvEtB,YAAY,EAAAiK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}