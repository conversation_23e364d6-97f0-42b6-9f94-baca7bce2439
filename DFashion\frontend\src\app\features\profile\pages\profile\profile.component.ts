import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { AuthService } from '../../../../core/services/auth.service';
import { RoleManagementService, UserRole, Department } from '../../../../core/services/role-management.service';
import { PermissionManagementService } from '../../../../core/services/permission-management.service';
import { DynamicProfileComponent, UserProfile } from '../../../../shared/components/dynamic-profile/dynamic-profile.component';
import { RoleBasedSettingsComponent } from '../../../../shared/components/role-based-settings/role-based-settings.component';
import { DepartmentDashboardComponent } from '../../../../shared/components/department-dashboard/department-dashboard.component';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DynamicProfileComponent,
    RoleBasedSettingsComponent,
    DepartmentDashboardComponent
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {
  currentUser: UserProfile | null = null;
  currentDepartment: Department | null = null;
  roleConfig: any = null;
  activeTab: string = 'profile';

  // Permission flags
  showDashboard: boolean = false;
  canManageTeam: boolean = false;
  canViewAnalytics: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private roleManagementService: RoleManagementService,
    private permissionManagementService: PermissionManagementService
  ) {}

  ngOnInit() {
    this.loadUserProfile();
    this.setupPermissions();

    // Check for tab parameter in URL
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['tab']) {
        this.setActiveTab(params['tab']);
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadUserProfile(): void {
    // Get current user from auth service
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(response => {
      if (response && response.user) {
        const user = response.user;
        this.currentUser = {
          id: user._id,
          username: user.username,
          fullName: user.fullName,
          email: user.email,
          avatar: user.avatar,
          role: (user.role || 'support_agent') as UserRole,
          department: 'support', // Default department since User model doesn't have department
          joinDate: new Date(user.createdAt || Date.now()),
          lastActive: new Date(),
          bio: user.bio || '',
          location: user.address?.city || '',
          phone: user.phone || '',
          isVerified: user.isVerified,
          isOnline: true
        };

        this.roleConfig = this.roleManagementService.getRoleConfig(this.currentUser.role);
        this.currentDepartment = this.roleConfig.department;

        // Set current user in permission service
        this.permissionManagementService.setCurrentUser(user);
      }
    });
  }

  private setupPermissions(): void {
    combineLatest([
      this.permissionManagementService.isFeatureEnabled('team_management'),
      this.permissionManagementService.isFeatureEnabled('advanced_analytics'),
      this.permissionManagementService.canAccessComponent('team_dashboard')
    ]).pipe(takeUntil(this.destroy$)).subscribe(([teamManagement, analytics, dashboard]) => {
      this.canManageTeam = teamManagement;
      this.canViewAnalytics = analytics;
      this.showDashboard = dashboard;
    });
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;

    // Update URL without navigation
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { tab },
      queryParamsHandling: 'merge'
    });
  }

  // Utility methods for role-based functionality
  getRoleDisplayName(): string {
    if (!this.currentUser?.role) return '';
    return this.roleManagementService.getRoleConfig(this.currentUser.role).displayName;
  }

  getPerformanceColor(performance: number): string {
    if (performance >= 90) return '#4CAF50';
    if (performance >= 75) return '#FF9800';
    return '#f44336';
  }

}
