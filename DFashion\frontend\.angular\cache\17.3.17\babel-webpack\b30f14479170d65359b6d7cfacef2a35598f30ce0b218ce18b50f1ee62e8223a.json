{"ast": null, "code": "export const homeRoutes = [{\n  path: '',\n  loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)\n}];", "map": {"version": 3, "names": ["homeRoutes", "path", "loadComponent", "then", "m", "HomeComponent"], "sources": ["E:\\Fahion\\DFashion\\frontend\\src\\app\\features\\home\\home.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const homeRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,UAAU,GAAW,CAChC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;CACrF,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}