{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-slick-carousel\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_ngx_slick_carousel_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 10)(2, \"div\", 18)(3, \"div\", 12);\n    i0.ɵɵelement(4, \"img\", 19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 15);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r3 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", story_r3.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r3.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r3.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_ngx_slick_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-slick-carousel\", 8)(1, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_ngx_slick_carousel_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAdd());\n    });\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12);\n    i0.ɵɵelement(5, \"img\", 13);\n    i0.ɵɵelementStart(6, \"span\", 14);\n    i0.ɵɵtext(7, \"+\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵtext(9, \"Your Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, ViewAddStoriesComponent_ngx_slick_carousel_2_div_10_Template, 7, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"config\", ctx_r1.storySliderConfig);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.canScrollStoriesLeft = false;\n    this.canScrollStoriesRight = false;\n    this.storySliderConfig = {\n      slidesToShow: 6,\n      slidesToScroll: 2,\n      infinite: false,\n      arrows: false,\n      dots: false,\n      variableWidth: false,\n      swipeToSlide: true,\n      responsive: [{\n        breakpoint: 900,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n    // Handler for Add Story button\n    // Modal state\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    // Reel recording state\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {}\n  removeEventListeners() {}\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        const uploadRes = yield _this2.http.post('/api/stories/upload', formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post('/api/stories', reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories();\n      } catch (err) {\n        alert('Failed to upload reel.');\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post('/api/stories/upload', uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post('/api/stories', storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-slider\", 3, \"config\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-slider\", 3, \"config\"], [\"ngxSlickItem\", \"\", 1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-story-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"ngxSlickItem\", \"\", \"class\", \"story-item\", 4, \"ngFor\", \"ngForOf\"], [\"ngxSlickItem\", \"\", 1, \"story-item\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 1)(2, ViewAddStoriesComponent_ngx_slick_carousel_2_Template, 11, 3, \"ngx-slick-carousel\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, SlickCarouselModule, i5.SlickCarouselComponent, i5.SlickItemDirective],\n      styles: [\"@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.stories-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 30px;\\n  background: white;\\n  border-radius: 15px;\\n  padding: 20px;\\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stories-loading[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 24px;\\n  overflow: hidden;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories[_ngcontent-%COMP%] {\\n  display: flex;\\n  overflow-x: auto;\\n  gap: 15px;\\n  scrollbar-width: none;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  flex: 0 0 auto;\\n  width: 78px;\\n  cursor: pointer;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  padding: 2px;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  overflow: hidden;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 5px;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  max-width: 100%;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: #262626;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories-skeleton[_ngcontent-%COMP%]   .story-skeleton[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  width: 80px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories-skeleton[_ngcontent-%COMP%]   .story-skeleton[_ngcontent-%COMP%]   .skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .stories-skeleton[_ngcontent-%COMP%]   .story-skeleton[_ngcontent-%COMP%]   .skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  margin-top: 6px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n.stories-error[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.stories-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n}\\n.stories-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #8e8e8e;\\n  margin-bottom: 20px;\\n}\\n.stories-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #0095f6;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n.stories-error[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #1877f2;\\n}\\n\\n.stories-empty[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.stories-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n}\\n.stories-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin-bottom: 8px;\\n}\\n.stories-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n  margin-bottom: 20px;\\n}\\n.stories-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%] {\\n  background: #0095f6;\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n.stories-empty[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]:hover {\\n  background: #1877f2;\\n}\\n\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: #262626;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%]   .stories-count[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #8e8e8e;\\n  font-size: 14px;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-actions[_ngcontent-%COMP%]   .refresh-btn[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  background: #efefef;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  font-size: 16px;\\n  transition: all 0.2s;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-actions[_ngcontent-%COMP%]   .refresh-btn[_ngcontent-%COMP%]:hover {\\n  background: #dbdbdb;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-actions[_ngcontent-%COMP%]   .refresh-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-actions[_ngcontent-%COMP%]   .loading-more-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-actions[_ngcontent-%COMP%]   .loading-more-indicator[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border: 2px solid #f3f3f3;\\n  border-top: 2px solid #0095f6;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid #dbdbdb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  font-size: 16px;\\n  z-index: 2;\\n  transition: all 0.2s;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .arrow.left[_ngcontent-%COMP%] {\\n  left: -16px;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .arrow.right[_ngcontent-%COMP%] {\\n  right: -16px;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  overflow-x: auto;\\n  scroll-behavior: smooth;\\n  padding: 10px 0;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  width: 80px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  padding: 2px;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-avatar-inner[_ngcontent-%COMP%]   .story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar.add-story-avatar[_ngcontent-%COMP%] {\\n  background: #efefef;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar.add-story-avatar[_ngcontent-%COMP%]   .story-avatar-inner[_ngcontent-%COMP%] {\\n  border-color: #dbdbdb;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar.add-story-avatar[_ngcontent-%COMP%]   .add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  font-weight: bold;\\n  border: 2px solid white;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 6px;\\n  text-align: center;\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  color: #262626;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n}\\n\\n.new-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.9);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-width: 400px;\\n  max-height: 80vh;\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background: white;\\n  border-bottom: 1px solid #efefef;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%]   .story-user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  margin-right: 12px;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%]   .story-user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 50%;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%]   .story-user-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%]   .story-user-info[_ngcontent-%COMP%]   .story-user-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: #262626;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%]   .story-user-info[_ngcontent-%COMP%]   .story-time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%]   .story-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  cursor: pointer;\\n  color: #8e8e8e;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-header[_ngcontent-%COMP%]   .story-close[_ngcontent-%COMP%]:hover {\\n  color: #262626;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-content[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-content[_ngcontent-%COMP%]   .story-media[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-height: 60vh;\\n  object-fit: cover;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-content[_ngcontent-%COMP%]   .story-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-content[_ngcontent-%COMP%]   .story-navigation[_ngcontent-%COMP%]   .story-nav-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  cursor: pointer;\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-content[_ngcontent-%COMP%]   .story-navigation[_ngcontent-%COMP%]   .story-nav-area.prev[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);\\n}\\n.story-viewer-overlay[_ngcontent-%COMP%]   .story-viewer[_ngcontent-%COMP%]   .story-content[_ngcontent-%COMP%]   .story-navigation[_ngcontent-%COMP%]   .story-nav-area.next[_ngcontent-%COMP%] {\\n  background: linear-gradient(to left, rgba(0, 0, 0, 0.1), transparent);\\n}\\n\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n    border-radius: 0;\\n    box-shadow: none;\\n    border-bottom: 1px solid #efefef;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    width: 70px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-slider[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n    width: 65px;\\n  }\\n  .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-container[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n    width: 55px;\\n    height: 55px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "SlickCarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵtext", "story_r3", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "ɵɵlistener", "ViewAddStoriesComponent_ngx_slick_carousel_2_Template_div_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onAdd", "ViewAddStoriesComponent_ngx_slick_carousel_2_div_10_Template", "storySliderConfig", "currentUser", "stories", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "canScrollStoriesLeft", "canScrollStoriesRight", "slidesToShow", "slidesToScroll", "infinite", "arrows", "dots", "variableWidth", "swipeToSlide", "responsive", "breakpoint", "settings", "showAddModal", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "showCameraOrGallery", "permissionDenied", "isRecording", "recordedChunks", "mediaRecorder", "videoStream", "reelPreviewUrl", "isUploadingReel", "newReelCaption", "newStoryFile", "newStoryCaption", "isUploadingStory", "ngOnInit", "loadStories", "setupEventListeners", "currentUser$", "subscribe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "scrollStoriesLeft", "storiesSlider", "nativeElement", "scrollBy", "left", "behavior", "setTimeout", "updateStoriesArrows", "scrollStoriesRight", "el", "scrollLeft", "scrollWidth", "clientWidth", "ngAfterViewInit", "push", "get", "apiUrl", "next", "response", "success", "storyGroups", "loadFallbackStories", "error", "console", "openStories", "index", "showStory", "document", "body", "style", "overflow", "closeStories", "pauseAllVideos", "storiesContainer", "classList", "add", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "onStoryClick", "clickX", "clientX", "windowWidth", "window", "innerWidth", "onTouchStart", "touches", "onTouchMove", "updateDragPosition", "onTouchEnd", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getCurrentStory", "getStoryProgress", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "formatNumber", "num", "toFixed", "toString", "formatPrice", "price", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "viewProduct", "product", "navigate", "_id", "hasProducts", "story", "products", "getStoryProducts", "onAddStory", "onAddReel", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "startRecording", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stopRecording", "stop", "submitNewReel", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "url", "caption", "isReel", "cleanupReelStream", "getTracks", "track", "closeAddReelModal", "handlePermissionResponse", "allow", "openCamera", "input", "setAttribute", "click", "openGallery", "removeAttribute", "onStoryFileSelected", "file", "target", "files", "submitNewStory", "_this3", "uploadForm", "storyPayload", "closeAddStoryModal", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "$event", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_ngx_slick_carousel_2_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "SlickCarouselComponent", "SlickItemDirective", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { Component, On<PERSON>nit, OnD<PERSON>roy, ElementRef, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\n\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  currentUser: any = null;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // For slider arrows\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n  canScrollStoriesLeft = false;\n  canScrollStoriesRight = false;\nstorySliderConfig = {\n  slidesToShow: 6,\n  slidesToScroll: 2,\n  infinite: false,\n  arrows: false,\n  dots: false,\n  variableWidth: false,\n  swipeToSlide: true,\n  responsive: [\n    { breakpoint: 900, settings: { slidesToShow: 4 } },\n    { breakpoint: 600, settings: { slidesToShow: 3 } }\n  ]\n};\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n        next: (response) => {\n          if (response.success && response.storyGroups) {\n            this.stories = response.storyGroups;\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  private update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  private setupEventListeners() {}\n  private removeEventListeners() {}\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  // Handler for Add Story button\n  // Modal state\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  // Reel recording state\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post('/api/stories', reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload reel.');\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post('/api/stories', storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n}\n", "\n\n\n<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Slider with ngx-slick-carousel -->\n  <ngx-slick-carousel class=\"stories-slider\" [config]=\"storySliderConfig\" *ngIf=\"!isLoadingStories\">\n    <!-- Add Story Button -->\n    <div ngxSlickItem class=\"story-item add-story-item\" (click)=\"onAdd()\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar add-story-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img class=\"story-avatar-img\" [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\" alt=\"Your Story\">\n            <span class=\"add-story-plus\">+</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">Your Story</div>\n    </div>\n    <!-- Dynamic Stories -->\n    <div ngxSlickItem class=\"story-item\" *ngFor=\"let story of stories\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img class=\"story-avatar-img\" [src]=\"story.user.avatar\" [alt]=\"story.user.username\">\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">{{ story.user.username }}</div>\n    </div>\n  </ngx-slick-carousel>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,mBAAmB,QAAQ,oBAAoB;;;;;;;;;;;;;ICFpDC,EAAA,CAAAC,cAAA,aAA6D;IAE3DD,EADA,CAAAE,SAAA,aAAmC,aACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;IAwB9BT,EAHN,CAAAC,cAAA,cAAmE,cAC7B,cACR,cACQ;IAC9BD,EAAA,CAAAE,SAAA,cAAoF;IAG1FF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IACvDV,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IALgCH,EAAA,CAAAM,SAAA,GAAyB;IAACN,EAA1B,CAAAO,UAAA,QAAAI,QAAA,CAAAC,IAAA,CAAAC,MAAA,EAAAb,EAAA,CAAAc,aAAA,CAAyB,QAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAA4B;IAI7Df,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAgB,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;;IApBvDf,EAFF,CAAAC,cAAA,4BAAkG,aAE1B;IAAlBD,EAAA,CAAAiB,UAAA,mBAAAC,2EAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IAG/DxB,EAFJ,CAAAC,cAAA,cAAoC,cACS,cACT;IAC9BD,EAAA,CAAAE,SAAA,cAA0G;IAC1GF,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,QAAC;IAGpCV,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IACxCV,EADwC,CAAAG,YAAA,EAAM,EACxC;IAENH,EAAA,CAAAI,UAAA,KAAAqB,4DAAA,kBAAmE;IAUrEzB,EAAA,CAAAG,YAAA,EAAqB;;;;IAxBsBH,EAAA,CAAAO,UAAA,WAAAc,MAAA,CAAAK,iBAAA,CAA4B;IAM/B1B,EAAA,CAAAM,SAAA,GAA0D;IAA1DN,EAAA,CAAAO,UAAA,SAAAc,MAAA,CAAAM,WAAA,kBAAAN,MAAA,CAAAM,WAAA,CAAAd,MAAA,kCAAAb,EAAA,CAAAc,aAAA,CAA0D;IAQzCd,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAc,MAAA,CAAAO,OAAA,CAAU;;;ADarE,OAAM,MAAOC,uBAAuB;EAuClCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAvCrB,KAAAN,WAAW,GAAQ,IAAI;IAEvB,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAM,gBAAgB,GAAG,IAAI;IAEvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAI1C,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,qBAAqB,GAAG,KAAK;IAC/B,KAAAvB,iBAAiB,GAAG;MAClBwB,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CACV;QAAEC,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAET,YAAY,EAAE;QAAC;MAAE,CAAE,EAClD;QAAEQ,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAET,YAAY,EAAE;QAAC;MAAE,CAAE;KAErD;IA4PC;IACA;IACA,KAAAU,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB;IACA,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAG,KAAK;EAzQrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAC7C,WAAW,CAAC8C,YAAY,CAACC,SAAS,CAACpE,IAAI,IAAG;MAC7C,IAAI,CAACe,WAAW,GAAGf,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAqE,WAAWA,CAAA;IACT,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAAC,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC7EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACP,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC5EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,MAAMQ,EAAE,GAAG,IAAI,CAACR,aAAa,CAACC,aAAa;MAC3C,IAAI,CAACxC,oBAAoB,GAAG+C,EAAE,CAACC,UAAU,GAAG,CAAC;MAC7C,IAAI,CAAC/C,qBAAqB,GAAG8C,EAAE,CAACC,UAAU,GAAGD,EAAE,CAACE,WAAW,GAAGF,EAAE,CAACG,WAAW,GAAG,CAAC;;EAEpF;EACAC,eAAeA,CAAA;IACbP,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;EACnD;EAEA;EACAhB,WAAWA,CAAA;IACT,IAAI,CAAC3C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,aAAa,CAACqD,IAAI,CACrB,IAAI,CAACpE,IAAI,CAACqE,GAAG,CAAM,GAAGvG,WAAW,CAACwG,MAAM,UAAU,CAAC,CAACtB,SAAS,CAAC;MAC5DuB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAAC9E,OAAO,GAAG4E,QAAQ,CAACE,WAAW;SACpC,MAAM;UACL,IAAI,CAACC,mBAAmB,EAAE;;QAE5B,IAAI,CAACzE,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACD0E,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAACzE,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEAyE,mBAAmBA,CAAA;IACjB,IAAI,CAAC/E,OAAO,GAAG;MACb;IAAA,CACD;EACH;EAEAkF,WAAWA,CAACC,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC5E,YAAY,GAAG4E,KAAK;IACzB,IAAI,CAAC3E,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC4E,SAAS,CAACD,KAAK,CAAC;IACrBE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EACAC,YAAYA,CAAA;IACV,IAAI,CAACjF,MAAM,GAAG,KAAK;IACnB,IAAI,CAACkF,cAAc,EAAE;IACrBL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAACgC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhE7B,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAC2B,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAACgC,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EACAV,SAASA,CAACD,KAAa;IACrB,IAAI,CAAC5E,YAAY,GAAG4E,KAAK;IACzB,IAAI,CAACxE,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAACgF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAAC2B,KAAK,CAACQ,SAAS,GAAG,mBAAmB;;EAE7E;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACzF,YAAY,GAAG,IAAI,CAACP,OAAO,CAACiG,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACrF,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EACAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC5F,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EAEAW,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAC7F,MAAM,EAAE;IAClB,QAAQ6F,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACP,YAAY,EAAE;QACnB;;EAEN;EACAc,YAAYA,CAACF,KAAiB;IAC5B,IAAI,IAAI,CAAC5F,UAAU,EAAE;IACrB,MAAM+F,MAAM,GAAGH,KAAK,CAACI,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACP,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EACAa,YAAYA,CAACR,KAAiB;IAC5B,IAAI,CAAC3F,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGuF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC1C,IAAI,CAAC1F,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EACAiG,WAAWA,CAACV,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGsF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC5C,IAAI,CAACO,kBAAkB,EAAE;EAC3B;EACAC,UAAUA,CAACZ,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAMwG,SAAS,GAAG,IAAI,CAACnG,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAMqG,SAAS,GAAGR,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC5F,0BAA0B;IACrE,IAAIoG,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACf,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAACpF,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyF,MAAM,EAAE;;EAEjB;EACQc,kBAAkBA,CAAA;IACxB,MAAME,SAAS,GAAG,IAAI,CAACnG,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAIuG,SAAS,GAAGP,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAACjB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAAC2B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAACpF,OAAO,MAAM;;EAErD;EACQuF,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACzF,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAIkG,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC1G,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAMyG,QAAQ,GAAG,IAAI,CAACzG,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACN,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAAC6E,SAAS,CAACkC,QAAQ,CAAC;QACxB,IAAI,CAACzG,eAAe,GAAG,IAAI;;MAE7B;;IAEF,IAAI,IAAI,CAAC8E,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAAC2B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAACpF,OAAO,MAAM;;IAEnD4G,qBAAqB,CAAC,MAAM,IAAI,CAACrB,MAAM,EAAE,CAAC;EAC5C;EACQR,cAAcA,CAAA;IACpB,MAAM8B,MAAM,GAAGnC,QAAQ,CAACoC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAAClE,OAAO,CAAEoE,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EACQzE,mBAAmBA,CAAA,GAAI;EACvBO,oBAAoBA,CAAA,GAAI;EAChCmE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC5H,OAAO,CAAC,IAAI,CAACO,YAAY,CAAC;EACxC;EACAsH,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACtH,YAAY,GAAG,CAAC,IAAI,IAAI,CAACP,OAAO,CAACiG,MAAM,GAAI,GAAG;EAC9D;EACA6B,UAAUA,CAACC,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGlB,IAAI,CAACgB,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGnB,IAAI,CAACgB,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EACAC,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EACAC,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCxD,KAAK,EAAE,UAAU;MACjByD,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EACAM,WAAWA,CAACC,OAAY;IACtB,IAAI,CAACjJ,MAAM,CAACkJ,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EACAC,WAAWA,CAAA;IACT,MAAMC,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE4B,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACxD,MAAM,GAAG,CAAC,CAAC;EACjE;EACAyD,gBAAgBA,CAAA;IACd,MAAMF,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO4B,KAAK,EAAEC,QAAQ,IAAI,EAAE;EAC9B;EAoBA7J,KAAKA,CAAA;IACH,IAAI,CAACoC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EACA+G,UAAUA,CAAA;IACR,IAAI,CAAC3H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EACA8G,SAASA,CAAA;IACP,IAAI,CAAC5H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBoB,UAAU,CAAC,MAAM,IAAI,CAAC6F,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAACrH,WAAW,SAASuH,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAExC,KAAK,EAAE,IAAI;UAAEyC,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAMzC,KAAK,GAAQrC,QAAQ,CAAC+E,cAAc,CAAC,YAAY,CAAC;QACxD,IAAI1C,KAAK,EAAE;UACTA,KAAK,CAAC2C,SAAS,GAAGP,KAAI,CAACrH,WAAW;UAClCiF,KAAK,CAAC4C,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAAC5H,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAuI,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChI,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKmE,MAAc,CAAC+D,aAAa,CAAC,IAAI,CAACjI,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAACmI,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACvI,cAAc,CAACiC,IAAI,CAACoG,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAACrI,aAAa,CAACuI,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAC1I,cAAc,EAAE;QAAE2I,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAACxI,cAAc,GAAGyI,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAACxI,aAAa,CAAC6I,KAAK,EAAE;IAC1B,IAAI,CAAC/I,WAAW,GAAG,IAAI;EACzB;EACAgJ,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC9I,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAAC+I,IAAI,EAAE;MACzB,IAAI,CAACjJ,WAAW,GAAG,KAAK;;EAE5B;EACMkJ,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI,CAAC0B,MAAI,CAAC/I,cAAc,EAAE;MAC1B+I,MAAI,CAAC9I,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAMqI,IAAI,GAAG,IAAIC,IAAI,CAACQ,MAAI,CAAClJ,cAAc,EAAE;UAAE2I,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEZ,IAAI,EAAE,WAAW,CAAC;QAC3C,MAAMa,SAAS,SAAcJ,MAAI,CAACrL,IAAI,CAAC0L,IAAI,CAAC,qBAAqB,EAAEJ,QAAQ,CAAC,CAACK,SAAS,EAAE;QACxF,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEV,MAAI,CAAC7I,cAAc;UAC5BwJ,MAAM,EAAE;SACT;QACD,MAAMX,MAAI,CAACrL,IAAI,CAAC0L,IAAI,CAAC,cAAc,EAAEE,WAAW,CAAC,CAACD,SAAS,EAAE;QAC7DN,MAAI,CAACvJ,gBAAgB,GAAG,KAAK;QAC7BuJ,MAAI,CAACxI,WAAW,EAAE;OACnB,CAAC,OAAOsH,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;OAChC,SAAS;QACRiB,MAAI,CAAC9I,eAAe,GAAG,KAAK;QAC5B8I,MAAI,CAACY,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC5J,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC6J,SAAS,EAAE,CAAChJ,OAAO,CAACiJ,KAAK,IAAIA,KAAK,CAAChB,IAAI,EAAE,CAAC;MAC3D,IAAI,CAAC9I,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EACA8J,iBAAiBA,CAAA;IACf,IAAI,CAACtK,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACmK,iBAAiB,EAAE;EAC1B;EACAI,wBAAwBA,CAACC,KAAc;IACrC,IAAI,CAACvK,mBAAmB,GAAG,KAAK;IAChC,IAAIuK,KAAK,EAAE;MACT,IAAI,CAACtK,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEhC;EACAsK,UAAUA,CAAA;IACR,IAAI,CAACvK,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B+B,UAAU,CAAC,MAAK;MACd,MAAM4I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAC,WAAWA,CAAA;IACT,IAAI,CAAC3K,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B+B,UAAU,CAAC,MAAK;MACd,MAAM4I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,mBAAmBA,CAAC5G,KAAU;IAC5B,MAAM6G,IAAI,GAAG7G,KAAK,CAAC8G,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACrK,YAAY,GAAGqK,IAAI;;EAE5B;EACMG,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MAClB,IAAI,CAACuD,MAAI,CAACzK,YAAY,EAAE;MACxByK,MAAI,CAACvK,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAMwK,UAAU,GAAG,IAAI5B,QAAQ,EAAE;QACjC4B,UAAU,CAAC3B,MAAM,CAAC,OAAO,EAAE0B,MAAI,CAACzK,YAAY,CAAC;QAC7C,MAAMgJ,SAAS,SAAcyB,MAAI,CAAClN,IAAI,CAAC0L,IAAI,CAAC,qBAAqB,EAAEyB,UAAU,CAAC,CAACxB,SAAS,EAAE;QAC1F,MAAMyB,YAAY,GAAG;UACnBvB,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEmB,MAAI,CAACxK;SACf;QACD,MAAMwK,MAAI,CAAClN,IAAI,CAAC0L,IAAI,CAAC,cAAc,EAAE0B,YAAY,CAAC,CAACzB,SAAS,EAAE;QAC9DuB,MAAI,CAACrL,iBAAiB,GAAG,KAAK;QAC9BqL,MAAI,CAACrK,WAAW,EAAE;OACnB,CAAC,OAAOsH,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACR8C,MAAI,CAACvK,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACA0K,kBAAkBA,CAAA;IAChB,IAAI,CAACxL,iBAAiB,GAAG,KAAK;EAChC;;;uBAtdWhC,uBAAuB,EAAA7B,EAAA,CAAAsP,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxP,EAAA,CAAAsP,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA1P,EAAA,CAAAsP,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvB/N,uBAAuB;MAAAgO,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvBhQ,EAAA,CAAAiB,UAAA,qBAAAiP,mDAAAC,MAAA;YAAA,OAAAF,GAAA,CAAAjI,aAAA,CAAAmI,MAAA,CAAqB;UAAA,UAAAnQ,EAAA,CAAAoQ,iBAAA,CAAE;;;;;;;;;;UCrCpCpQ,EAAA,CAAAC,cAAA,aAA+B;UAU7BD,EARA,CAAAI,UAAA,IAAAiQ,sCAAA,iBAAsD,IAAAC,qDAAA,iCAQ4C;UAyBpGtQ,EAAA,CAAAG,YAAA,EAAM;;;UAjCEH,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAA0P,GAAA,CAAA/N,gBAAA,CAAsB;UAQ6ClC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAA0P,GAAA,CAAA/N,gBAAA,CAAuB;;;qBDuBtFtC,YAAY,EAAA2Q,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE5Q,WAAW,EAAEE,mBAAmB,EAAA2Q,EAAA,CAAAC,sBAAA,EAAAD,EAAA,CAAAE,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}