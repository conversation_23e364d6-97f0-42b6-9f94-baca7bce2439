{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { timer } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/services/story.service\";\nimport * as i3 from \"../../../core/services/cart.service\";\nimport * as i4 from \"../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction StoryViewerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressWidth(i_r3), \"%\");\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r1.currentStoryIndex)(\"completed\", i_r3 < ctx_r1.currentStoryIndex);\n  }\n}\nfunction StoryViewerComponent_div_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.pauseStory());\n    });\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.resumeStory());\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_img_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 36);\n    i0.ɵɵlistener(\"load\", function StoryViewerComponent_div_0_img_17_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption);\n  }\n}\nfunction StoryViewerComponent_div_0_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 37, 0);\n    i0.ɵɵlistener(\"loadeddata\", function StoryViewerComponent_div_0_video_18_Template_video_loadeddata_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    })(\"ended\", function StoryViewerComponent_div_0_video_18_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StoryViewerComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.caption);\n  }\n}\nfunction StoryViewerComponent_div_0_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_div_20_div_1_Template_div_click_0_listener() {\n      const productTag_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.showProductDetails(productTag_r9));\n    });\n    i0.ɵɵelement(1, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const productTag_r9 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"top\", productTag_r9.position.y, \"%\")(\"left\", productTag_r9.position.x, \"%\");\n  }\n}\nfunction StoryViewerComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, StoryViewerComponent_div_0_div_20_div_1_Template, 2, 4, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n  }\n}\nfunction StoryViewerComponent_div_0_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendMessage());\n    });\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵtemplate(2, StoryViewerComponent_div_0_div_2_Template, 2, 6, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 7)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"img\", 9);\n    i0.ɵɵelementStart(6, \"div\", 10)(7, \"span\", 11);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 12);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 13);\n    i0.ɵɵtemplate(12, StoryViewerComponent_div_0_button_12_Template, 2, 0, \"button\", 14)(13, StoryViewerComponent_div_0_button_13_Template, 2, 0, \"button\", 14);\n    i0.ɵɵelementStart(14, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStory());\n    });\n    i0.ɵɵelement(15, \"i\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 17);\n    i0.ɵɵtemplate(17, StoryViewerComponent_div_0_img_17_Template, 1, 2, \"img\", 18)(18, StoryViewerComponent_div_0_video_18_Template, 2, 1, \"video\", 19)(19, StoryViewerComponent_div_0_div_19_Template, 3, 1, \"div\", 20)(20, StoryViewerComponent_div_0_div_20_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 24)(24, \"div\", 25)(25, \"input\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoryViewerComponent_div_0_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.messageText, $event) || (ctx_r1.messageText = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function StoryViewerComponent_div_0_Template_input_keyup_enter_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendMessage());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, StoryViewerComponent_div_0_button_26_Template, 2, 0, \"button\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 28)(28, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.likeStory());\n    });\n    i0.ɵɵelement(29, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareStory());\n    });\n    i0.ɵɵelement(31, \"i\", 31);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.userStories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.currentStory.createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isPaused);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPaused);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.messageText);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.messageText.trim());\n  }\n}\nfunction StoryViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵelement(3, \"img\", 48);\n    i0.ɵɵelementStart(4, \"div\", 49)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 51);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 52)(13, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵelement(14, \"i\", 30);\n    i0.ɵɵtext(15, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToCart(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵelement(17, \"i\", 55);\n    i0.ɵɵtext(18, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyNow(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵtext(20, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(9, 5, ctx_r1.selectedProduct.product.price), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.product.brand);\n  }\n}\nfunction StoryViewerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class StoryViewerComponent {\n  constructor(route, router, storyService, cartService, wishlistService) {\n    this.route = route;\n    this.router = router;\n    this.storyService = storyService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.userStories = [];\n    this.currentStoryIndex = 0;\n    this.currentStory = null;\n    this.isLoading = true;\n    this.isPaused = false;\n    this.progress = 0;\n    this.storyDuration = 5000; // 5 seconds for images\n    this.messageText = '';\n    this.selectedProduct = null;\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const userId = params['userId'];\n      const storyIndex = parseInt(params['storyIndex']) || 0;\n      if (userId) {\n        this.loadUserStories(userId, storyIndex);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.stopProgress();\n    if (this.storyTimeout) {\n      clearTimeout(this.storyTimeout);\n    }\n  }\n  handleKeyboardEvent(event) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStory();\n        break;\n    }\n  }\n  loadUserStories(userId, startIndex = 0) {\n    this.isLoading = true;\n    this.storyService.getUserStories(userId).subscribe({\n      next: response => {\n        this.userStories = response.stories;\n        this.currentStoryIndex = Math.min(startIndex, this.userStories.length - 1);\n        this.currentStory = this.userStories[this.currentStoryIndex];\n        this.isLoading = false;\n        this.startStoryProgress();\n      },\n      error: error => {\n        console.error('Failed to load stories:', error);\n        this.isLoading = false;\n        this.closeStory();\n      }\n    });\n  }\n  startStoryProgress() {\n    this.stopProgress();\n    this.progress = 0;\n    if (this.currentStory?.media.type === 'video') {\n      // For videos, let the video control the progress\n      return;\n    }\n    const intervalMs = 50; // Update every 50ms\n    const increment = intervalMs / this.storyDuration * 100;\n    this.progressSubscription = timer(0, intervalMs).subscribe(() => {\n      if (!this.isPaused) {\n        this.progress += increment;\n        if (this.progress >= 100) {\n          this.nextStory();\n        }\n      }\n    });\n  }\n  stopProgress() {\n    if (this.progressSubscription) {\n      this.progressSubscription.unsubscribe();\n      this.progressSubscription = undefined;\n    }\n  }\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) {\n      return 100;\n    } else if (index === this.currentStoryIndex) {\n      return this.progress;\n    } else {\n      return 0;\n    }\n  }\n  nextStory() {\n    if (this.currentStoryIndex < this.userStories.length - 1) {\n      this.currentStoryIndex++;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.startStoryProgress();\n    } else {\n      // Move to next user's stories or close\n      this.closeStory();\n    }\n  }\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.startStoryProgress();\n    }\n  }\n  pauseStory() {\n    this.isPaused = true;\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      videoElement.pause();\n    }\n  }\n  resumeStory() {\n    this.isPaused = false;\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      videoElement.play();\n    }\n  }\n  onStoryClick(event) {\n    const target = event.target;\n    // Don't handle clicks on interactive elements\n    if (target.closest('.story-header') || target.closest('.story-footer') || target.closest('.product-tag') || target.closest('.nav-area')) {\n      return;\n    }\n    // Pause/resume on tap\n    if (this.isPaused) {\n      this.resumeStory();\n    } else {\n      this.pauseStory();\n    }\n  }\n  onMediaLoaded() {\n    // Media is loaded, story can start\n  }\n  showProductDetails(productTag) {\n    this.selectedProduct = productTag;\n    this.pauseStory();\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n    this.resumeStory();\n  }\n  addToWishlist(productId) {\n    if (this.selectedProduct) {\n      this.wishlistService.addToWishlist(productId).subscribe({\n        next: () => {\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        },\n        error: error => {\n          console.error('Wishlist error:', error);\n          // Fallback to offline mode\n          this.wishlistService.addToWishlistOffline(this.selectedProduct.product);\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n  addToCart(productId) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        },\n        error: error => {\n          console.error('Cart error:', error);\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n  buyNow(productId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.selectedProduct) {\n        try {\n          yield _this.cartService.addToCart(_this.selectedProduct.product, 1, _this.selectedProduct.size, _this.selectedProduct.color);\n          _this.showNotification('Redirecting to checkout...');\n          _this.closeProductModal();\n          _this.router.navigate(['/product', productId]);\n        } catch (error) {\n          console.error('Buy now error:', error);\n          _this.showNotification('Redirecting to product page...');\n          _this.closeProductModal();\n          _this.router.navigate(['/product', productId]);\n        }\n      }\n    })();\n  }\n  showNotification(message) {\n    // Create a simple notification\n    const notification = document.createElement('div');\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 20px;\n      z-index: 30000;\n      font-size: 14px;\n      backdrop-filter: blur(10px);\n      animation: slideDown 0.3s ease;\n    `;\n    document.body.appendChild(notification);\n    setTimeout(() => {\n      notification.remove();\n    }, 2000);\n  }\n  sendMessage() {\n    if (this.messageText.trim()) {\n      console.log('Send message:', this.messageText);\n      this.messageText = '';\n    }\n  }\n  likeStory() {\n    console.log('Like story');\n  }\n  shareStory() {\n    console.log('Share story');\n  }\n  closeStory() {\n    this.router.navigate(['/']);\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n  static {\n    this.ɵfac = function StoryViewerComponent_Factory(t) {\n      return new (t || StoryViewerComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.StoryService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoryViewerComponent,\n      selectors: [[\"app-story-viewer\"]],\n      hostBindings: function StoryViewerComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function StoryViewerComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyboardEvent($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"videoElement\", \"\"], [\"class\", \"story-viewer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"story-viewer\", 3, \"click\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [1, \"time-ago\"], [1, \"story-actions\"], [\"class\", \"action-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-content\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", \"load\", 4, \"ngIf\"], [\"class\", \"story-media\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", \"loadeddata\", \"ended\", 4, \"ngIf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [1, \"nav-area\", \"nav-left\", 3, \"click\"], [1, \"nav-area\", \"nav-right\", 3, \"click\"], [1, \"story-footer\"], [1, \"story-input\"], [\"type\", \"text\", \"placeholder\", \"Send message\", 1, \"message-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"class\", \"send-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"story-reactions\"], [1, \"reaction-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"far\", \"fa-share\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"fas\", \"fa-pause\"], [1, \"fas\", \"fa-play\"], [1, \"story-media\", 3, \"load\", \"src\", \"alt\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-media\", 3, \"loadeddata\", \"ended\", \"src\"], [1, \"story-caption\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"top\", \"left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"tag-dot\"], [1, \"send-btn\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"product-modal\", 3, \"click\"], [1, \"product-modal-content\", 3, \"click\"], [1, \"product-header\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-price\"], [1, \"product-brand\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy-now\", 3, \"click\"], [1, \"loading-container\"], [1, \"spinner\"]],\n      template: function StoryViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StoryViewerComponent_div_0_Template, 32, 13, \"div\", 1)(1, StoryViewerComponent_div_1_Template, 21, 7, \"div\", 2)(2, StoryViewerComponent_div_2_Template, 2, 0, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\".story-viewer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 10000;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  padding: 8px 16px;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  transition: width 0.1s linear;\\n  border-radius: 1px;\\n}\\n\\n.progress-fill.completed[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  position: absolute;\\n  top: 20px;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  object-fit: cover;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.time-ago[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5);\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background 0.2s;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n}\\n\\n.story-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n}\\n\\n.story-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.5);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.story-caption[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 2px solid #007bff;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #007bff;\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);\\n  }\\n}\\n.nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 30%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n\\n.nav-right[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.story-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.story-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 20px;\\n  padding: 12px 16px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #007bff;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-reactions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.reaction-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n}\\n\\n.product-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 20000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.product-modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  max-width: 400px;\\n  width: 100%;\\n  animation: _ngcontent-%COMP%_modalSlideUp 0.3s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalSlideUp {\\n  from {\\n    transform: translateY(50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n.product-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: #e91e63;\\n  margin: 0 0 4px 0;\\n}\\n\\n.product-brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%], .btn-cart[_ngcontent-%COMP%], .btn-buy-now[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  font-size: 12px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n  border: 1px solid #ddd;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: white;\\n}\\n\\n.btn-buy-now[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n  color: white;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 10000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  border-top: 3px solid white;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .product-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .nav-area[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "timer", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ctx_r1", "getProgressWidth", "i_r3", "ɵɵclassProp", "currentStoryIndex", "ɵɵlistener", "StoryViewerComponent_div_0_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "pauseStory", "StoryViewerComponent_div_0_button_13_Template_button_click_0_listener", "_r5", "resumeStory", "StoryViewerComponent_div_0_img_17_Template_img_load_0_listener", "_r6", "onMediaLoaded", "ɵɵproperty", "currentStory", "media", "url", "ɵɵsanitizeUrl", "caption", "StoryViewerComponent_div_0_video_18_Template_video_loadeddata_0_listener", "_r7", "StoryViewerComponent_div_0_video_18_Template_video_ended_0_listener", "nextStory", "ɵɵtext", "ɵɵtextInterpolate", "StoryViewerComponent_div_0_div_20_div_1_Template_div_click_0_listener", "productTag_r9", "_r8", "$implicit", "showProductDetails", "position", "y", "x", "ɵɵtemplate", "StoryViewerComponent_div_0_div_20_div_1_Template", "products", "StoryViewerComponent_div_0_button_26_Template_button_click_0_listener", "_r10", "sendMessage", "StoryViewerComponent_div_0_Template_div_click_0_listener", "$event", "_r1", "onStoryClick", "StoryViewerComponent_div_0_div_2_Template", "StoryViewerComponent_div_0_button_12_Template", "StoryViewerComponent_div_0_button_13_Template", "StoryViewerComponent_div_0_Template_button_click_14_listener", "closeStory", "StoryViewerComponent_div_0_img_17_Template", "StoryViewerComponent_div_0_video_18_Template", "StoryViewerComponent_div_0_div_19_Template", "StoryViewerComponent_div_0_div_20_Template", "StoryViewerComponent_div_0_Template_div_click_21_listener", "previousStory", "StoryViewerComponent_div_0_Template_div_click_22_listener", "ɵɵtwoWayListener", "StoryViewerComponent_div_0_Template_input_ngModelChange_25_listener", "ɵɵtwoWayBindingSet", "messageText", "StoryViewerComponent_div_0_Template_input_keyup_enter_25_listener", "StoryViewerComponent_div_0_button_26_Template", "StoryViewerComponent_div_0_Template_button_click_28_listener", "likeStory", "StoryViewerComponent_div_0_Template_button_click_30_listener", "shareStory", "userStories", "user", "avatar", "fullName", "username", "getTimeAgo", "createdAt", "isPaused", "type", "length", "ɵɵtwoWayProperty", "trim", "StoryViewerComponent_div_1_Template_div_click_0_listener", "_r11", "closeProductModal", "StoryViewerComponent_div_1_Template_div_click_1_listener", "stopPropagation", "StoryViewerComponent_div_1_Template_button_click_13_listener", "addToWishlist", "selectedProduct", "product", "_id", "StoryViewerComponent_div_1_Template_button_click_16_listener", "addToCart", "StoryViewerComponent_div_1_Template_button_click_19_listener", "buyNow", "images", "name", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "price", "brand", "StoryViewerComponent", "constructor", "route", "router", "storyService", "cartService", "wishlistService", "isLoading", "progress", "storyDuration", "ngOnInit", "params", "subscribe", "userId", "storyIndex", "parseInt", "loadUserStories", "ngOnDestroy", "stopProgress", "storyTimeout", "clearTimeout", "handleKeyboardEvent", "event", "key", "startIndex", "getUserStories", "next", "response", "stories", "Math", "min", "startStoryProgress", "error", "console", "intervalMs", "increment", "progressSubscription", "unsubscribe", "undefined", "index", "videoElement", "document", "querySelector", "pause", "play", "target", "closest", "productTag", "productId", "showNotification", "addToWishlistOffline", "size", "color", "_this", "_asyncToGenerator", "navigate", "message", "notification", "createElement", "textContent", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "remove", "log", "date", "now", "Date", "diff", "getTime", "hours", "floor", "days", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "StoryService", "i3", "CartService", "i4", "WishlistService", "selectors", "hostBindings", "StoryViewerComponent_HostBindings", "rf", "ctx", "StoryViewerComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "StoryViewerComponent_div_0_Template", "StoryViewerComponent_div_1_Template", "StoryViewerComponent_div_2_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\story\\story-viewer\\story-viewer.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription, interval, timer } from 'rxjs';\n\nimport { Story } from '../../../core/models/story.model';\nimport { StoryService } from '../../../core/services/story.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistService } from '../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-story-viewer',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"story-viewer\" *ngIf=\"currentStory\" (click)=\"onStoryClick($event)\">\n      <!-- Progress Bars -->\n      <div class=\"progress-container\">\n        <div \n          *ngFor=\"let story of userStories; let i = index\" \n          class=\"progress-bar\"\n        >\n          <div \n            class=\"progress-fill\"\n            [style.width.%]=\"getProgressWidth(i)\"\n            [class.active]=\"i === currentStoryIndex\"\n            [class.completed]=\"i < currentStoryIndex\"\n          ></div>\n        </div>\n      </div>\n\n      <!-- Story Header -->\n      <div class=\"story-header\">\n        <div class=\"user-info\">\n          <img [src]=\"currentStory.user.avatar\" [alt]=\"currentStory.user.fullName\" class=\"user-avatar\">\n          <div class=\"user-details\">\n            <span class=\"username\">{{ currentStory.user.username }}</span>\n            <span class=\"time-ago\">{{ getTimeAgo(currentStory.createdAt) }}</span>\n          </div>\n        </div>\n        <div class=\"story-actions\">\n          <button class=\"action-btn\" (click)=\"pauseStory()\" *ngIf=\"!isPaused\">\n            <i class=\"fas fa-pause\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"resumeStory()\" *ngIf=\"isPaused\">\n            <i class=\"fas fa-play\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"closeStory()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\">\n        <!-- Image Story -->\n        <img \n          *ngIf=\"currentStory.media.type === 'image'\" \n          [src]=\"currentStory.media.url\" \n          [alt]=\"currentStory.caption\"\n          class=\"story-media\"\n          (load)=\"onMediaLoaded()\"\n        >\n        \n        <!-- Video Story -->\n        <video \n          *ngIf=\"currentStory.media.type === 'video'\" \n          [src]=\"currentStory.media.url\"\n          class=\"story-media\"\n          autoplay\n          muted\n          (loadeddata)=\"onMediaLoaded()\"\n          (ended)=\"nextStory()\"\n          #videoElement\n        ></video>\n\n        <!-- Story Caption -->\n        <div class=\"story-caption\" *ngIf=\"currentStory.caption\">\n          <p>{{ currentStory.caption }}</p>\n        </div>\n\n        <!-- Product Tags -->\n        <div class=\"product-tags\" *ngIf=\"currentStory.products.length > 0\">\n          <div \n            *ngFor=\"let productTag of currentStory.products\" \n            class=\"product-tag\"\n            [style.top.%]=\"productTag.position.y\"\n            [style.left.%]=\"productTag.position.x\"\n            (click)=\"showProductDetails(productTag)\"\n          >\n            <div class=\"tag-dot\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation Areas -->\n      <div class=\"nav-area nav-left\" (click)=\"previousStory()\"></div>\n      <div class=\"nav-area nav-right\" (click)=\"nextStory()\"></div>\n\n      <!-- Story Footer -->\n      <div class=\"story-footer\">\n        <div class=\"story-input\">\n          <input \n            type=\"text\" \n            placeholder=\"Send message\" \n            [(ngModel)]=\"messageText\"\n            (keyup.enter)=\"sendMessage()\"\n            class=\"message-input\"\n          >\n          <button class=\"send-btn\" (click)=\"sendMessage()\" *ngIf=\"messageText.trim()\">\n            <i class=\"fas fa-paper-plane\"></i>\n          </button>\n        </div>\n        <div class=\"story-reactions\">\n          <button class=\"reaction-btn\" (click)=\"likeStory()\">\n            <i class=\"far fa-heart\"></i>\n          </button>\n          <button class=\"reaction-btn\" (click)=\"shareStory()\">\n            <i class=\"far fa-share\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Product Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"product-modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"product-header\">\n          <img [src]=\"selectedProduct.product.images[0].url\" [alt]=\"selectedProduct.product.name\" class=\"product-image\">\n          <div class=\"product-info\">\n            <h3>{{ selectedProduct.product.name }}</h3>\n            <p class=\"product-price\">₹{{ selectedProduct.product.price | number }}</p>\n            <p class=\"product-brand\">{{ selectedProduct.product.brand }}</p>\n          </div>\n        </div>\n        <div class=\"product-actions\">\n          <button class=\"btn-wishlist\" (click)=\"addToWishlist(selectedProduct.product._id)\">\n            <i class=\"far fa-heart\"></i>\n            Wishlist\n          </button>\n          <button class=\"btn-cart\" (click)=\"addToCart(selectedProduct.product._id)\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            Add to Cart\n          </button>\n          <button class=\"btn-buy-now\" (click)=\"buyNow(selectedProduct.product._id)\">\n            Buy Now\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\n      <div class=\"spinner\"></div>\n    </div>\n  `,\n  styles: [`\n    .story-viewer {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 10000;\n      display: flex;\n      flex-direction: column;\n      user-select: none;\n    }\n\n    .progress-container {\n      display: flex;\n      gap: 2px;\n      padding: 8px 16px;\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .progress-bar {\n      flex: 1;\n      height: 2px;\n      background: rgba(255, 255, 255, 0.3);\n      border-radius: 1px;\n      overflow: hidden;\n    }\n\n    .progress-fill {\n      height: 100%;\n      background: white;\n      transition: width 0.1s linear;\n      border-radius: 1px;\n    }\n\n    .progress-fill.completed {\n      width: 100% !important;\n    }\n\n    .story-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 16px;\n      position: absolute;\n      top: 20px;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      border: 2px solid white;\n      object-fit: cover;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username {\n      color: white;\n      font-weight: 600;\n      font-size: 14px;\n    }\n\n    .time-ago {\n      color: rgba(255, 255, 255, 0.7);\n      font-size: 12px;\n    }\n\n    .story-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .action-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: rgba(0, 0, 0, 0.5);\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: background 0.2s;\n    }\n\n    .action-btn:hover {\n      background: rgba(0, 0, 0, 0.7);\n    }\n\n    .story-content {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .story-media {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n    }\n\n    .story-caption {\n      position: absolute;\n      bottom: 100px;\n      left: 16px;\n      right: 16px;\n      background: rgba(0, 0, 0, 0.5);\n      color: white;\n      padding: 12px 16px;\n      border-radius: 20px;\n      backdrop-filter: blur(10px);\n    }\n\n    .story-caption p {\n      margin: 0;\n      font-size: 14px;\n      line-height: 1.4;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n    }\n\n    .tag-dot {\n      width: 24px;\n      height: 24px;\n      background: white;\n      border-radius: 50%;\n      border: 2px solid #007bff;\n      position: relative;\n      animation: pulse 2s infinite;\n    }\n\n    .tag-dot::after {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      width: 8px;\n      height: 8px;\n      background: #007bff;\n      border-radius: 50%;\n    }\n\n    @keyframes pulse {\n      0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }\n      70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }\n      100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }\n    }\n\n    .nav-area {\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      width: 30%;\n      z-index: 5;\n      cursor: pointer;\n    }\n\n    .nav-left {\n      left: 0;\n    }\n\n    .nav-right {\n      right: 0;\n    }\n\n    .story-footer {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      padding: 16px;\n      background: rgba(0, 0, 0, 0.3);\n      backdrop-filter: blur(10px);\n    }\n\n    .story-input {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .message-input {\n      flex: 1;\n      background: rgba(255, 255, 255, 0.1);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      border-radius: 20px;\n      padding: 12px 16px;\n      color: white;\n      font-size: 14px;\n    }\n\n    .message-input::placeholder {\n      color: rgba(255, 255, 255, 0.7);\n    }\n\n    .message-input:focus {\n      outline: none;\n      border-color: rgba(255, 255, 255, 0.4);\n    }\n\n    .send-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: #007bff;\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .story-reactions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .reaction-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 18px;\n    }\n\n    .product-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0, 0, 0, 0.8);\n      z-index: 20000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .product-modal-content {\n      background: white;\n      border-radius: 16px;\n      padding: 24px;\n      max-width: 400px;\n      width: 100%;\n      animation: modalSlideUp 0.3s ease;\n    }\n\n    @keyframes modalSlideUp {\n      from {\n        transform: translateY(50px);\n        opacity: 0;\n      }\n      to {\n        transform: translateY(0);\n        opacity: 1;\n      }\n    }\n\n    .product-header {\n      display: flex;\n      gap: 16px;\n      margin-bottom: 20px;\n    }\n\n    .product-image {\n      width: 80px;\n      height: 80px;\n      border-radius: 8px;\n      object-fit: cover;\n    }\n\n    .product-info h3 {\n      margin: 0 0 8px 0;\n      font-size: 18px;\n      font-weight: 600;\n    }\n\n    .product-price {\n      font-size: 20px;\n      font-weight: 700;\n      color: #e91e63;\n      margin: 0 0 4px 0;\n    }\n\n    .product-brand {\n      color: #666;\n      margin: 0;\n      font-size: 14px;\n    }\n\n    .product-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .btn-wishlist, .btn-cart, .btn-buy-now {\n      flex: 1;\n      padding: 12px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      font-size: 12px;\n    }\n\n    .btn-wishlist {\n      background: #f8f9fa;\n      color: #666;\n      border: 1px solid #ddd;\n    }\n\n    .btn-cart {\n      background: #2196f3;\n      color: white;\n    }\n\n    .btn-buy-now {\n      background: #ff9800;\n      color: white;\n    }\n\n    .loading-container {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 10000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 3px solid rgba(255, 255, 255, 0.3);\n      border-top: 3px solid white;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    @media (max-width: 768px) {\n      .product-actions {\n        flex-direction: column;\n      }\n      \n      .nav-area {\n        width: 40%;\n      }\n    }\n  `]\n})\nexport class StoryViewerComponent implements OnInit, OnDestroy {\n  userStories: Story[] = [];\n  currentStoryIndex = 0;\n  currentStory: Story | null = null;\n  \n  isLoading = true;\n  isPaused = false;\n  progress = 0;\n  storyDuration = 5000; // 5 seconds for images\n  \n  messageText = '';\n  selectedProduct: any = null;\n  \n  private progressSubscription?: Subscription;\n  private storyTimeout?: any;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private storyService: StoryService,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const userId = params['userId'];\n      const storyIndex = parseInt(params['storyIndex']) || 0;\n      \n      if (userId) {\n        this.loadUserStories(userId, storyIndex);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this.stopProgress();\n    if (this.storyTimeout) {\n      clearTimeout(this.storyTimeout);\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeyboardEvent(event: KeyboardEvent) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStory();\n        break;\n    }\n  }\n\n  loadUserStories(userId: string, startIndex: number = 0) {\n    this.isLoading = true;\n    \n    this.storyService.getUserStories(userId).subscribe({\n      next: (response) => {\n        this.userStories = response.stories;\n        this.currentStoryIndex = Math.min(startIndex, this.userStories.length - 1);\n        this.currentStory = this.userStories[this.currentStoryIndex];\n        this.isLoading = false;\n        this.startStoryProgress();\n      },\n      error: (error) => {\n        console.error('Failed to load stories:', error);\n        this.isLoading = false;\n        this.closeStory();\n      }\n    });\n  }\n\n  startStoryProgress() {\n    this.stopProgress();\n    this.progress = 0;\n    \n    if (this.currentStory?.media.type === 'video') {\n      // For videos, let the video control the progress\n      return;\n    }\n    \n    const intervalMs = 50; // Update every 50ms\n    const increment = (intervalMs / this.storyDuration) * 100;\n\n    this.progressSubscription = timer(0, intervalMs).subscribe(() => {\n      if (!this.isPaused) {\n        this.progress += increment;\n        if (this.progress >= 100) {\n          this.nextStory();\n        }\n      }\n    });\n  }\n\n  stopProgress() {\n    if (this.progressSubscription) {\n      this.progressSubscription.unsubscribe();\n      this.progressSubscription = undefined;\n    }\n  }\n\n  getProgressWidth(index: number): number {\n    if (index < this.currentStoryIndex) {\n      return 100;\n    } else if (index === this.currentStoryIndex) {\n      return this.progress;\n    } else {\n      return 0;\n    }\n  }\n\n  nextStory() {\n    if (this.currentStoryIndex < this.userStories.length - 1) {\n      this.currentStoryIndex++;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.startStoryProgress();\n    } else {\n      // Move to next user's stories or close\n      this.closeStory();\n    }\n  }\n\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.startStoryProgress();\n    }\n  }\n\n  pauseStory() {\n    this.isPaused = true;\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      videoElement.pause();\n    }\n  }\n\n  resumeStory() {\n    this.isPaused = false;\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      videoElement.play();\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    const target = event.target as HTMLElement;\n    \n    // Don't handle clicks on interactive elements\n    if (target.closest('.story-header') || \n        target.closest('.story-footer') || \n        target.closest('.product-tag') ||\n        target.closest('.nav-area')) {\n      return;\n    }\n    \n    // Pause/resume on tap\n    if (this.isPaused) {\n      this.resumeStory();\n    } else {\n      this.pauseStory();\n    }\n  }\n\n  onMediaLoaded() {\n    // Media is loaded, story can start\n  }\n\n  showProductDetails(productTag: any) {\n    this.selectedProduct = productTag;\n    this.pauseStory();\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n    this.resumeStory();\n  }\n\n  addToWishlist(productId: string) {\n    if (this.selectedProduct) {\n      this.wishlistService.addToWishlist(productId).subscribe({\n        next: () => {\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        },\n        error: (error) => {\n          console.error('Wishlist error:', error);\n          // Fallback to offline mode\n          this.wishlistService.addToWishlistOffline(this.selectedProduct.product);\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n\n  addToCart(productId: string) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        },\n        error: (error: any) => {\n          console.error('Cart error:', error);\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n\n  async buyNow(productId: string) {\n    if (this.selectedProduct) {\n      try {\n        await this.cartService.addToCart(this.selectedProduct.product, 1, this.selectedProduct.size, this.selectedProduct.color);\n        this.showNotification('Redirecting to checkout...');\n        this.closeProductModal();\n        this.router.navigate(['/product', productId]);\n      } catch (error: any) {\n        console.error('Buy now error:', error);\n        this.showNotification('Redirecting to product page...');\n        this.closeProductModal();\n        this.router.navigate(['/product', productId]);\n      }\n    }\n  }\n\n  private showNotification(message: string) {\n    // Create a simple notification\n    const notification = document.createElement('div');\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 20px;\n      z-index: 30000;\n      font-size: 14px;\n      backdrop-filter: blur(10px);\n      animation: slideDown 0.3s ease;\n    `;\n\n    document.body.appendChild(notification);\n\n    setTimeout(() => {\n      notification.remove();\n    }, 2000);\n  }\n\n  sendMessage() {\n    if (this.messageText.trim()) {\n      console.log('Send message:', this.messageText);\n      this.messageText = '';\n    }\n  }\n\n  likeStory() {\n    console.log('Like story');\n  }\n\n  shareStory() {\n    console.log('Share story');\n  }\n\n  closeStory() {\n    this.router.navigate(['/']);\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    \n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n}\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAAiCC,KAAK,QAAQ,MAAM;;;;;;;;;;IAe5CC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,cAKO;IACTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJFH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAK,WAAA,UAAAC,MAAA,CAAAC,gBAAA,CAAAC,IAAA,OAAqC;IAErCR,EADA,CAAAS,WAAA,WAAAD,IAAA,KAAAF,MAAA,CAAAI,iBAAA,CAAwC,cAAAF,IAAA,GAAAF,MAAA,CAAAI,iBAAA,CACC;;;;;;IAe3CV,EAAA,CAAAC,cAAA,iBAAoE;IAAzCD,EAAA,CAAAW,UAAA,mBAAAC,sEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IAC/CjB,EAAA,CAAAE,SAAA,YAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAoE;IAAzCD,EAAA,CAAAW,UAAA,mBAAAO,sEAAA;MAAAlB,EAAA,CAAAa,aAAA,CAAAM,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAc,WAAA,EAAa;IAAA,EAAC;IAChDpB,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAUXH,EAAA,CAAAC,cAAA,cAMC;IADCD,EAAA,CAAAW,UAAA,kBAAAU,+DAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAQV,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC;IAL1BvB,EAAA,CAAAG,YAAA,EAMC;;;;IAHCH,EADA,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA8B,QAAAtB,MAAA,CAAAmB,YAAA,CAAAI,OAAA,CACF;;;;;;IAM9B7B,EAAA,CAAAC,cAAA,mBASC;IAFCD,EADA,CAAAW,UAAA,wBAAAmB,yEAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAcV,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC,mBAAAS,oEAAA;MAAAhC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACrBV,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;IAEtBjC,EAAA,CAAAG,YAAA,EAAQ;;;;IAPPH,EAAA,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA8B;;;;;IAW9B5B,EADF,CAAAC,cAAA,cAAwD,QACnD;IAAAD,EAAA,CAAAkC,MAAA,GAA0B;IAC/BlC,EAD+B,CAAAG,YAAA,EAAI,EAC7B;;;;IADDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAmC,iBAAA,CAAA7B,MAAA,CAAAmB,YAAA,CAAAI,OAAA,CAA0B;;;;;;IAK7B7B,EAAA,CAAAC,cAAA,cAMC;IADCD,EAAA,CAAAW,UAAA,mBAAAyB,sEAAA;MAAA,MAAAC,aAAA,GAAArC,EAAA,CAAAa,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAjC,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAkC,kBAAA,CAAAH,aAAA,CAA8B;IAAA,EAAC;IAExCrC,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IAJJH,EADA,CAAAK,WAAA,QAAAgC,aAAA,CAAAI,QAAA,CAAAC,CAAA,MAAqC,SAAAL,aAAA,CAAAI,QAAA,CAAAE,CAAA,MACC;;;;;IAL1C3C,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAA4C,UAAA,IAAAC,gDAAA,kBAMC;IAGH7C,EAAA,CAAAG,YAAA,EAAM;;;;IARqBH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAmB,YAAA,CAAAqB,QAAA,CAAwB;;;;;;IAyBjD9C,EAAA,CAAAC,cAAA,iBAA4E;IAAnDD,EAAA,CAAAW,UAAA,mBAAAoC,sEAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,IAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA2C,WAAA,EAAa;IAAA,EAAC;IAC9CjD,EAAA,CAAAE,SAAA,YAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAhGfH,EAAA,CAAAC,cAAA,aAA8E;IAA/BD,EAAA,CAAAW,UAAA,mBAAAuC,yDAAAC,MAAA;MAAAnD,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA+C,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAE3EnD,EAAA,CAAAC,cAAA,aAAgC;IAC9BD,EAAA,CAAA4C,UAAA,IAAAU,yCAAA,iBAGC;IAQHtD,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,aAA0B,aACD;IACrBD,EAAA,CAAAE,SAAA,aAA6F;IAE3FF,EADF,CAAAC,cAAA,cAA0B,eACD;IAAAD,EAAA,CAAAkC,MAAA,GAAgC;IAAAlC,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAkC,MAAA,IAAwC;IAEnElC,EAFmE,CAAAG,YAAA,EAAO,EAClE,EACF;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAIzBD,EAHA,CAAA4C,UAAA,KAAAW,6CAAA,qBAAoE,KAAAC,6CAAA,qBAGA;IAGpExD,EAAA,CAAAC,cAAA,kBAAkD;IAAvBD,EAAA,CAAAW,UAAA,mBAAA8C,6DAAA;MAAAzD,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAoD,UAAA,EAAY;IAAA,EAAC;IAC/C1D,EAAA,CAAAE,SAAA,aAA4B;IAGlCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IA4BzBD,EA1BA,CAAA4C,UAAA,KAAAe,0CAAA,kBAMC,KAAAC,4CAAA,oBAYA,KAAAC,0CAAA,kBAGuD,KAAAC,0CAAA,kBAKW;IAWrE9D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAA1BD,EAAA,CAAAW,UAAA,mBAAAoD,0DAAA;MAAA/D,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA0D,aAAA,EAAe;IAAA,EAAC;IAAChE,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,eAAsD;IAAtBD,EAAA,CAAAW,UAAA,mBAAAsD,0DAAA;MAAAjE,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;IAACjC,EAAA,CAAAG,YAAA,EAAM;IAKxDH,EAFJ,CAAAC,cAAA,eAA0B,eACC,iBAOtB;IAHCD,EAAA,CAAAkE,gBAAA,2BAAAC,oEAAAhB,MAAA;MAAAnD,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAoE,kBAAA,CAAA9D,MAAA,CAAA+D,WAAA,EAAAlB,MAAA,MAAA7C,MAAA,CAAA+D,WAAA,GAAAlB,MAAA;MAAA,OAAAnD,EAAA,CAAAgB,WAAA,CAAAmC,MAAA;IAAA,EAAyB;IACzBnD,EAAA,CAAAW,UAAA,yBAAA2D,kEAAA;MAAAtE,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAeV,MAAA,CAAA2C,WAAA,EAAa;IAAA,EAAC;IAJ/BjD,EAAA,CAAAG,YAAA,EAMC;IACDH,EAAA,CAAA4C,UAAA,KAAA2B,6CAAA,qBAA4E;IAG9EvE,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA6B,kBACwB;IAAtBD,EAAA,CAAAW,UAAA,mBAAA6D,6DAAA;MAAAxE,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAmE,SAAA,EAAW;IAAA,EAAC;IAChDzE,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAoD;IAAvBD,EAAA,CAAAW,UAAA,mBAAA+D,6DAAA;MAAA1E,EAAA,CAAAa,aAAA,CAAAuC,GAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAqE,UAAA,EAAY;IAAA,EAAC;IACjD3E,EAAA,CAAAE,SAAA,aAA4B;IAIpCF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAvGkBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAsE,WAAA,CAAgB;IAe7B5E,EAAA,CAAAI,SAAA,GAAgC;IAACJ,EAAjC,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAAoD,IAAA,CAAAC,MAAA,EAAA9E,EAAA,CAAA4B,aAAA,CAAgC,QAAAtB,MAAA,CAAAmB,YAAA,CAAAoD,IAAA,CAAAE,QAAA,CAAmC;IAE/C/E,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAmC,iBAAA,CAAA7B,MAAA,CAAAmB,YAAA,CAAAoD,IAAA,CAAAG,QAAA,CAAgC;IAChChF,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAmC,iBAAA,CAAA7B,MAAA,CAAA2E,UAAA,CAAA3E,MAAA,CAAAmB,YAAA,CAAAyD,SAAA,EAAwC;IAIdlF,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAwB,UAAA,UAAAlB,MAAA,CAAA6E,QAAA,CAAe;IAGdnF,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA6E,QAAA,CAAc;IAajEnF,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAA0D,IAAA,aAAyC;IASzCpF,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAA0D,IAAA,aAAyC;IAWhBpF,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAI,OAAA,CAA0B;IAK3B7B,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAqB,QAAA,CAAAuC,MAAA,KAAsC;IAuB7DrF,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAsF,gBAAA,YAAAhF,MAAA,CAAA+D,WAAA,CAAyB;IAIuBrE,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA+D,WAAA,CAAAkB,IAAA,GAAwB;;;;;;IAgBhFvF,EAAA,CAAAC,cAAA,cAAiF;IAA9BD,EAAA,CAAAW,UAAA,mBAAA6E,yDAAA;MAAAxF,EAAA,CAAAa,aAAA,CAAA4E,IAAA;MAAA,MAAAnF,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAoF,iBAAA,EAAmB;IAAA,EAAC;IAC9E1F,EAAA,CAAAC,cAAA,cAAsE;IAAnCD,EAAA,CAAAW,UAAA,mBAAAgF,yDAAAxC,MAAA;MAAAnD,EAAA,CAAAa,aAAA,CAAA4E,IAAA;MAAA,OAAAzF,EAAA,CAAAgB,WAAA,CAASmC,MAAA,CAAAyC,eAAA,EAAwB;IAAA,EAAC;IACnE5F,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,cAA8G;IAE5GF,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAkC,MAAA,GAAkC;IAAAlC,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAkC,MAAA,GAA6C;;IAAAlC,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAkC,MAAA,IAAmC;IAEhElC,EAFgE,CAAAG,YAAA,EAAI,EAC5D,EACF;IAEJH,EADF,CAAAC,cAAA,eAA6B,kBACuD;IAArDD,EAAA,CAAAW,UAAA,mBAAAkF,6DAAA;MAAA7F,EAAA,CAAAa,aAAA,CAAA4E,IAAA;MAAA,MAAAnF,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAwF,aAAA,CAAAxF,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAA0C;IAAA,EAAC;IAC/EjG,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAkC,MAAA,kBACF;IAAAlC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAAjDD,EAAA,CAAAW,UAAA,mBAAAuF,6DAAA;MAAAlG,EAAA,CAAAa,aAAA,CAAA4E,IAAA;MAAA,MAAAnF,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA6F,SAAA,CAAA7F,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAAsC;IAAA,EAAC;IACvEjG,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAkC,MAAA,qBACF;IAAAlC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAA9CD,EAAA,CAAAW,UAAA,mBAAAyF,6DAAA;MAAApG,EAAA,CAAAa,aAAA,CAAA4E,IAAA;MAAA,MAAAnF,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA+F,MAAA,CAAA/F,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAAmC;IAAA,EAAC;IACvEjG,EAAA,CAAAkC,MAAA,iBACF;IAGNlC,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IArBKH,EAAA,CAAAI,SAAA,GAA6C;IAACJ,EAA9C,CAAAwB,UAAA,QAAAlB,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAM,MAAA,IAAA3E,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA6C,QAAAtB,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAO,IAAA,CAAqC;IAEjFvG,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAmC,iBAAA,CAAA7B,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAO,IAAA,CAAkC;IACbvG,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAwG,kBAAA,WAAAxG,EAAA,CAAAyG,WAAA,OAAAnG,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAU,KAAA,MAA6C;IAC7C1G,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmC,iBAAA,CAAA7B,MAAA,CAAAyF,eAAA,CAAAC,OAAA,CAAAW,KAAA,CAAmC;;;;;IAoBpE3G,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;AA0ZV,OAAM,MAAOyG,oBAAoB;EAgB/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,YAA0B,EAC1BC,WAAwB,EACxBC,eAAgC;IAJhC,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IApBzB,KAAAtC,WAAW,GAAY,EAAE;IACzB,KAAAlE,iBAAiB,GAAG,CAAC;IACrB,KAAAe,YAAY,GAAiB,IAAI;IAEjC,KAAA0F,SAAS,GAAG,IAAI;IAChB,KAAAhC,QAAQ,GAAG,KAAK;IAChB,KAAAiC,QAAQ,GAAG,CAAC;IACZ,KAAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IAEtB,KAAAhD,WAAW,GAAG,EAAE;IAChB,KAAA0B,eAAe,GAAQ,IAAI;EAWxB;EAEHuB,QAAQA,CAAA;IACN,IAAI,CAACR,KAAK,CAACS,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,MAAME,MAAM,GAAGF,MAAM,CAAC,QAAQ,CAAC;MAC/B,MAAMG,UAAU,GAAGC,QAAQ,CAACJ,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;MAEtD,IAAIE,MAAM,EAAE;QACV,IAAI,CAACG,eAAe,CAACH,MAAM,EAAEC,UAAU,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC;;EAEnC;EAGAE,mBAAmBA,CAACC,KAAoB;IACtC,QAAQA,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACnE,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;MACjB,KAAK,GAAG;QACN,IAAI,CAAC/B,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACyB,UAAU,EAAE;QACjB;;EAEN;EAEAkE,eAAeA,CAACH,MAAc,EAAEW,UAAA,GAAqB,CAAC;IACpD,IAAI,CAACjB,SAAS,GAAG,IAAI;IAErB,IAAI,CAACH,YAAY,CAACqB,cAAc,CAACZ,MAAM,CAAC,CAACD,SAAS,CAAC;MACjDc,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC3D,WAAW,GAAG2D,QAAQ,CAACC,OAAO;QACnC,IAAI,CAAC9H,iBAAiB,GAAG+H,IAAI,CAACC,GAAG,CAACN,UAAU,EAAE,IAAI,CAACxD,WAAW,CAACS,MAAM,GAAG,CAAC,CAAC;QAC1E,IAAI,CAAC5D,YAAY,GAAG,IAAI,CAACmD,WAAW,CAAC,IAAI,CAAClE,iBAAiB,CAAC;QAC5D,IAAI,CAACyG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACwB,kBAAkB,EAAE;MAC3B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACzB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACzD,UAAU,EAAE;MACnB;KACD,CAAC;EACJ;EAEAiF,kBAAkBA,CAAA;IAChB,IAAI,CAACb,YAAY,EAAE;IACnB,IAAI,CAACV,QAAQ,GAAG,CAAC;IAEjB,IAAI,IAAI,CAAC3F,YAAY,EAAEC,KAAK,CAAC0D,IAAI,KAAK,OAAO,EAAE;MAC7C;MACA;;IAGF,MAAM0D,UAAU,GAAG,EAAE,CAAC,CAAC;IACvB,MAAMC,SAAS,GAAID,UAAU,GAAG,IAAI,CAACzB,aAAa,GAAI,GAAG;IAEzD,IAAI,CAAC2B,oBAAoB,GAAGjJ,KAAK,CAAC,CAAC,EAAE+I,UAAU,CAAC,CAACtB,SAAS,CAAC,MAAK;MAC9D,IAAI,CAAC,IAAI,CAACrC,QAAQ,EAAE;QAClB,IAAI,CAACiC,QAAQ,IAAI2B,SAAS;QAC1B,IAAI,IAAI,CAAC3B,QAAQ,IAAI,GAAG,EAAE;UACxB,IAAI,CAACnF,SAAS,EAAE;;;IAGtB,CAAC,CAAC;EACJ;EAEA6F,YAAYA,CAAA;IACV,IAAI,IAAI,CAACkB,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACC,WAAW,EAAE;MACvC,IAAI,CAACD,oBAAoB,GAAGE,SAAS;;EAEzC;EAEA3I,gBAAgBA,CAAC4I,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACzI,iBAAiB,EAAE;MAClC,OAAO,GAAG;KACX,MAAM,IAAIyI,KAAK,KAAK,IAAI,CAACzI,iBAAiB,EAAE;MAC3C,OAAO,IAAI,CAAC0G,QAAQ;KACrB,MAAM;MACL,OAAO,CAAC;;EAEZ;EAEAnF,SAASA,CAAA;IACP,IAAI,IAAI,CAACvB,iBAAiB,GAAG,IAAI,CAACkE,WAAW,CAACS,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAAC3E,iBAAiB,EAAE;MACxB,IAAI,CAACe,YAAY,GAAG,IAAI,CAACmD,WAAW,CAAC,IAAI,CAAClE,iBAAiB,CAAC;MAC5D,IAAI,CAACiI,kBAAkB,EAAE;KAC1B,MAAM;MACL;MACA,IAAI,CAACjF,UAAU,EAAE;;EAErB;EAEAM,aAAaA,CAAA;IACX,IAAI,IAAI,CAACtD,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACe,YAAY,GAAG,IAAI,CAACmD,WAAW,CAAC,IAAI,CAAClE,iBAAiB,CAAC;MAC5D,IAAI,CAACiI,kBAAkB,EAAE;;EAE7B;EAEA1H,UAAUA,CAAA;IACR,IAAI,CAACkE,QAAQ,GAAG,IAAI;IACpB,MAAMiE,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,EAAE;;EAExB;EAEAnI,WAAWA,CAAA;IACT,IAAI,CAAC+D,QAAQ,GAAG,KAAK;IACrB,MAAMiE,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACI,IAAI,EAAE;;EAEvB;EAEAnG,YAAYA,CAAC6E,KAAiB;IAC5B,MAAMuB,MAAM,GAAGvB,KAAK,CAACuB,MAAqB;IAE1C;IACA,IAAIA,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,IAC/BD,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,IAC/BD,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,IAC9BD,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;MAC/B;;IAGF;IACA,IAAI,IAAI,CAACvE,QAAQ,EAAE;MACjB,IAAI,CAAC/D,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACH,UAAU,EAAE;;EAErB;EAEAM,aAAaA,CAAA;IACX;EAAA;EAGFiB,kBAAkBA,CAACmH,UAAe;IAChC,IAAI,CAAC5D,eAAe,GAAG4D,UAAU;IACjC,IAAI,CAAC1I,UAAU,EAAE;EACnB;EAEAyE,iBAAiBA,CAAA;IACf,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC3E,WAAW,EAAE;EACpB;EAEA0E,aAAaA,CAAC8D,SAAiB;IAC7B,IAAI,IAAI,CAAC7D,eAAe,EAAE;MACxB,IAAI,CAACmB,eAAe,CAACpB,aAAa,CAAC8D,SAAS,CAAC,CAACpC,SAAS,CAAC;QACtDc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACuB,gBAAgB,CAAC,sBAAsB,CAAC;UAC7C,IAAI,CAACnE,iBAAiB,EAAE;QAC1B,CAAC;QACDkD,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;UACvC;UACA,IAAI,CAAC1B,eAAe,CAAC4C,oBAAoB,CAAC,IAAI,CAAC/D,eAAe,CAACC,OAAO,CAAC;UACvE,IAAI,CAAC6D,gBAAgB,CAAC,sBAAsB,CAAC;UAC7C,IAAI,CAACnE,iBAAiB,EAAE;QAC1B;OACD,CAAC;;EAEN;EAEAS,SAASA,CAACyD,SAAiB;IACzB,IAAI,IAAI,CAAC7D,eAAe,EAAE;MACxB,IAAI,CAACkB,WAAW,CAACd,SAAS,CAACyD,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC7D,eAAe,CAACgE,IAAI,EAAE,IAAI,CAAChE,eAAe,CAACiE,KAAK,CAAC,CAACxC,SAAS,CAAC;QACxGc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACuB,gBAAgB,CAAC,kBAAkB,CAAC;UACzC,IAAI,CAACnE,iBAAiB,EAAE;QAC1B,CAAC;QACDkD,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;UACnC,IAAI,CAACiB,gBAAgB,CAAC,kBAAkB,CAAC;UACzC,IAAI,CAACnE,iBAAiB,EAAE;QAC1B;OACD,CAAC;;EAEN;EAEMW,MAAMA,CAACuD,SAAiB;IAAA,IAAAK,KAAA;IAAA,OAAAC,iBAAA;MAC5B,IAAID,KAAI,CAAClE,eAAe,EAAE;QACxB,IAAI;UACF,MAAMkE,KAAI,CAAChD,WAAW,CAACd,SAAS,CAAC8D,KAAI,CAAClE,eAAe,CAACC,OAAO,EAAE,CAAC,EAAEiE,KAAI,CAAClE,eAAe,CAACgE,IAAI,EAAEE,KAAI,CAAClE,eAAe,CAACiE,KAAK,CAAC;UACxHC,KAAI,CAACJ,gBAAgB,CAAC,4BAA4B,CAAC;UACnDI,KAAI,CAACvE,iBAAiB,EAAE;UACxBuE,KAAI,CAAClD,MAAM,CAACoD,QAAQ,CAAC,CAAC,UAAU,EAAEP,SAAS,CAAC,CAAC;SAC9C,CAAC,OAAOhB,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;UACtCqB,KAAI,CAACJ,gBAAgB,CAAC,gCAAgC,CAAC;UACvDI,KAAI,CAACvE,iBAAiB,EAAE;UACxBuE,KAAI,CAAClD,MAAM,CAACoD,QAAQ,CAAC,CAAC,UAAU,EAAEP,SAAS,CAAC,CAAC;;;IAEhD;EACH;EAEQC,gBAAgBA,CAACO,OAAe;IACtC;IACA,MAAMC,YAAY,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,KAAK,CAAC;IAClDD,YAAY,CAACE,WAAW,GAAGH,OAAO;IAClCC,YAAY,CAACG,KAAK,CAACC,OAAO,GAAG;;;;;;;;;;;;;KAa5B;IAEDpB,QAAQ,CAACqB,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;IAEvCO,UAAU,CAAC,MAAK;MACdP,YAAY,CAACQ,MAAM,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA5H,WAAWA,CAAA;IACT,IAAI,IAAI,CAACoB,WAAW,CAACkB,IAAI,EAAE,EAAE;MAC3BsD,OAAO,CAACiC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACzG,WAAW,CAAC;MAC9C,IAAI,CAACA,WAAW,GAAG,EAAE;;EAEzB;EAEAI,SAASA,CAAA;IACPoE,OAAO,CAACiC,GAAG,CAAC,YAAY,CAAC;EAC3B;EAEAnG,UAAUA,CAAA;IACRkE,OAAO,CAACiC,GAAG,CAAC,aAAa,CAAC;EAC5B;EAEApH,UAAUA,CAAA;IACR,IAAI,CAACqD,MAAM,CAACoD,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEAlF,UAAUA,CAAC8F,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACrD,MAAMC,KAAK,GAAG3C,IAAI,CAAC4C,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIE,KAAK,GAAG,CAAC,EAAE,OAAO,KAAK;IAC3B,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,GAAG;IAClC,MAAME,IAAI,GAAG7C,IAAI,CAAC4C,KAAK,CAACD,KAAK,GAAG,EAAE,CAAC;IACnC,OAAO,GAAGE,IAAI,GAAG;EACnB;;;uBAhSW1E,oBAAoB,EAAA5G,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzL,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1L,EAAA,CAAAuL,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAA5L,EAAA,CAAAuL,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA9L,EAAA,CAAAuL,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAApBpF,oBAAoB;MAAAqF,SAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAApBpM,EAAA,CAAAW,UAAA,qBAAA2L,gDAAAnJ,MAAA;YAAA,OAAAkJ,GAAA,CAAApE,mBAAA,CAAA9E,MAAA,CAA2B;UAAA,UAAAnD,EAAA,CAAAuM,iBAAA,CAAP;;;;;;;;;;UA5Z7BvM,EAzIA,CAAA4C,UAAA,IAAA4J,mCAAA,mBAA8E,IAAAC,mCAAA,kBA8GG,IAAAC,mCAAA,iBA2BhC;;;UAzItB1M,EAAA,CAAAwB,UAAA,SAAA6K,GAAA,CAAA5K,YAAA,CAAkB;UA8GjBzB,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAwB,UAAA,SAAA6K,GAAA,CAAAtG,eAAA,CAAqB;UA2BjB/F,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAwB,UAAA,SAAA6K,GAAA,CAAAlF,SAAA,CAAe;;;qBA3IvCtH,YAAY,EAAA8M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEhN,WAAW,EAAAiN,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}