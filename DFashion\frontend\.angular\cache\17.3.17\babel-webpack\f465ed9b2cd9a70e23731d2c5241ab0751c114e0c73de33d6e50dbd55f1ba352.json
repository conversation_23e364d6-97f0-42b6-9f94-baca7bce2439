{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { timer } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/services/story.service\";\nimport * as i3 from \"../../../core/services/cart.service\";\nimport * as i4 from \"../../../core/services/wishlist.service\";\nimport * as i5 from \"../../../core/services/media.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction StoryViewerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressWidth(i_r3), \"%\");\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r1.currentStoryIndex)(\"completed\", i_r3 < ctx_r1.currentStoryIndex);\n  }\n}\nfunction StoryViewerComponent_div_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.pauseStory());\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.resumeStory());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_img_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 41);\n    i0.ɵɵlistener(\"load\", function StoryViewerComponent_div_0_img_18_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    })(\"error\", function StoryViewerComponent_div_0_img_18_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleImageError($event));\n    })(\"click\", function StoryViewerComponent_div_0_img_18_Template_img_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTap($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.url) || \"\", i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.alt) || ctx_r1.currentStory.caption || \"\");\n  }\n}\nfunction StoryViewerComponent_div_0_video_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 42, 0);\n    i0.ɵɵlistener(\"loadeddata\", function StoryViewerComponent_div_0_video_19_Template_video_loadeddata_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    })(\"ended\", function StoryViewerComponent_div_0_video_19_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    })(\"error\", function StoryViewerComponent_div_0_video_19_Template_video_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleVideoError($event));\n    })(\"click\", function StoryViewerComponent_div_0_video_19_Template_video_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStoryTap($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.url) || \"\", i0.ɵɵsanitizeUrl)(\"poster\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.thumbnailUrl) || \"\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StoryViewerComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatDuration((ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.duration) || 0), \" \");\n  }\n}\nfunction StoryViewerComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_div_21_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleStoryVideo());\n    });\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"playing\", ctx_r1.isStoryVideoPlaying);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.isStoryVideoPlaying ? \"fas fa-pause\" : \"fas fa-play\");\n  }\n}\nfunction StoryViewerComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.caption);\n  }\n}\nfunction StoryViewerComponent_div_0_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_div_23_div_1_Template_div_click_0_listener() {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.showProductDetails(productTag_r10));\n    });\n    i0.ɵɵelement(1, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const productTag_r10 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"top\", productTag_r10.position.y, \"%\")(\"left\", productTag_r10.position.x, \"%\");\n  }\n}\nfunction StoryViewerComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, StoryViewerComponent_div_0_div_23_div_1_Template, 2, 4, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n  }\n}\nfunction StoryViewerComponent_div_0_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendMessage());\n    });\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵtemplate(2, StoryViewerComponent_div_0_div_2_Template, 2, 6, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 7)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"img\", 9);\n    i0.ɵɵelementStart(6, \"div\", 10)(7, \"span\", 11);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 12);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 13);\n    i0.ɵɵtemplate(12, StoryViewerComponent_div_0_button_12_Template, 2, 0, \"button\", 14)(13, StoryViewerComponent_div_0_button_13_Template, 2, 0, \"button\", 14);\n    i0.ɵɵelementStart(14, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStory());\n    });\n    i0.ɵɵelement(15, \"i\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 17);\n    i0.ɵɵtemplate(17, StoryViewerComponent_div_0_div_17_Template, 2, 0, \"div\", 18)(18, StoryViewerComponent_div_0_img_18_Template, 1, 2, \"img\", 19)(19, StoryViewerComponent_div_0_video_19_Template, 2, 2, \"video\", 20)(20, StoryViewerComponent_div_0_div_20_Template, 2, 1, \"div\", 21)(21, StoryViewerComponent_div_0_div_21_Template, 3, 4, \"div\", 22)(22, StoryViewerComponent_div_0_div_22_Template, 3, 1, \"div\", 23)(23, StoryViewerComponent_div_0_div_23_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 27)(27, \"div\", 28)(28, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoryViewerComponent_div_0_Template_input_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.messageText, $event) || (ctx_r1.messageText = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function StoryViewerComponent_div_0_Template_input_keyup_enter_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendMessage());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, StoryViewerComponent_div_0_button_29_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 31)(31, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.likeStory());\n    });\n    i0.ɵɵelement(32, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareStory());\n    });\n    i0.ɵɵelement(34, \"i\", 34);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.userStories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.currentStory.createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isPaused);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPaused);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.type) === \"image\" && !ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.type) === \"video\" && !ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.type) === \"video\" && (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.duration));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.type) === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.messageText);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.messageText.trim());\n  }\n}\nfunction StoryViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 55);\n    i0.ɵɵelement(3, \"img\", 56);\n    i0.ɵɵelementStart(4, \"div\", 57)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 58);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 59);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 60)(13, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵelement(14, \"i\", 33);\n    i0.ɵɵtext(15, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToCart(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵelement(17, \"i\", 63);\n    i0.ɵɵtext(18, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyNow(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵtext(20, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(9, 5, ctx_r1.selectedProduct.product.price), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.product.brand);\n  }\n}\nfunction StoryViewerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"div\", 66);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class StoryViewerComponent {\n  constructor(route, router, storyService, cartService, wishlistService, mediaService) {\n    this.route = route;\n    this.router = router;\n    this.storyService = storyService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.mediaService = mediaService;\n    this.userStories = [];\n    this.currentStoryIndex = 0;\n    this.currentStory = null;\n    this.isLoading = true;\n    this.isPaused = false;\n    this.progress = 0;\n    this.storyDuration = 5000; // 5 seconds for images\n    this.messageText = '';\n    this.selectedProduct = null;\n    // Media handling\n    this.currentMediaItem = null;\n    this.isStoryVideoPlaying = false;\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const userId = params['userId'];\n      const storyIndex = parseInt(params['storyIndex']) || 0;\n      if (userId) {\n        this.loadUserStories(userId, storyIndex);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.stopProgress();\n    if (this.storyTimeout) {\n      clearTimeout(this.storyTimeout);\n    }\n  }\n  handleKeyboardEvent(event) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStory();\n        break;\n    }\n  }\n  loadUserStories(userId, startIndex = 0) {\n    this.isLoading = true;\n    this.storyService.getUserStories(userId).subscribe({\n      next: response => {\n        this.userStories = response.stories;\n        // Enhance stories with video content if needed\n        this.userStories = this.enhanceStoriesWithMedia(this.userStories);\n        this.currentStoryIndex = Math.min(startIndex, this.userStories.length - 1);\n        this.currentStory = this.userStories[this.currentStoryIndex];\n        this.updateCurrentMedia();\n        this.isLoading = false;\n        this.startStoryProgress();\n      },\n      error: error => {\n        console.error('Failed to load stories:', error);\n        this.isLoading = false;\n        this.closeStory();\n      }\n    });\n  }\n  enhanceStoriesWithMedia(stories) {\n    return stories.map(story => {\n      // If story doesn't have media or has broken media, add sample video\n      if (!story.media || !story.media.url || this.isBrokenMediaUrl(story.media.url)) {\n        // Get appropriate video based on story content\n        const contentHint = story.caption || story.products?.map(p => p.product.name).join(' ') || '';\n        const sampleVideo = this.getVideoByContentHint(contentHint);\n        story.media = {\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnail: sampleVideo.thumbnail,\n          duration: sampleVideo.duration\n        };\n      }\n      // Fix broken image URLs\n      if (story.media && story.media.type === 'image' && this.isBrokenMediaUrl(story.media.url)) {\n        story.media = {\n          ...story.media,\n          url: this.mediaService.getSafeImageUrl(story.media.url, 'story'),\n          thumbnail: this.mediaService.getSafeImageUrl(story.media.thumbnail, 'story')\n        };\n      }\n      return story;\n    });\n  }\n  isBrokenMediaUrl(url) {\n    return url.includes('/uploads/') || url.includes('sample-videos.com') || url.includes('localhost');\n  }\n  getVideoByContentHint(hint) {\n    const lowerHint = hint.toLowerCase();\n    if (lowerHint.includes('fashion') || lowerHint.includes('style')) {\n      return this.mediaService.getVideoByType('fashion');\n    }\n    if (lowerHint.includes('tutorial') || lowerHint.includes('tips')) {\n      return this.mediaService.getVideoByType('tutorial');\n    }\n    if (lowerHint.includes('showcase') || lowerHint.includes('collection')) {\n      return this.mediaService.getVideoByType('showcase');\n    }\n    if (lowerHint.includes('behind') || lowerHint.includes('process')) {\n      return this.mediaService.getVideoByType('story');\n    }\n    return this.mediaService.getRandomSampleVideo();\n  }\n  updateCurrentMedia() {\n    if (this.currentStory?.media) {\n      this.currentMediaItem = {\n        id: this.currentStory._id,\n        type: this.currentStory.media.type,\n        url: this.mediaService.getSafeImageUrl(this.currentStory.media.url, 'story'),\n        thumbnailUrl: this.currentStory.media.thumbnail,\n        alt: this.currentStory.caption,\n        duration: this.currentStory.media.duration\n      };\n    }\n  }\n  startStoryProgress() {\n    this.stopProgress();\n    this.progress = 0;\n    if (this.currentStory?.media.type === 'video') {\n      // For videos, let the video control the progress\n      return;\n    }\n    const intervalMs = 50; // Update every 50ms\n    const increment = intervalMs / this.storyDuration * 100;\n    this.progressSubscription = timer(0, intervalMs).subscribe(() => {\n      if (!this.isPaused) {\n        this.progress += increment;\n        if (this.progress >= 100) {\n          this.nextStory();\n        }\n      }\n    });\n  }\n  stopProgress() {\n    if (this.progressSubscription) {\n      this.progressSubscription.unsubscribe();\n      this.progressSubscription = undefined;\n    }\n  }\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) {\n      return 100;\n    } else if (index === this.currentStoryIndex) {\n      return this.progress;\n    } else {\n      return 0;\n    }\n  }\n  nextStory() {\n    if (this.currentStoryIndex < this.userStories.length - 1) {\n      this.currentStoryIndex++;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    } else {\n      // Move to next user's stories or close\n      this.closeStory();\n    }\n  }\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    }\n  }\n  pauseStory() {\n    this.isPaused = true;\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      videoElement.pause();\n    }\n  }\n  resumeStory() {\n    this.isPaused = false;\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      videoElement.play();\n    }\n  }\n  onStoryClick(event) {\n    const target = event.target;\n    // Don't handle clicks on interactive elements\n    if (target.closest('.story-header') || target.closest('.story-footer') || target.closest('.product-tag') || target.closest('.nav-area')) {\n      return;\n    }\n    // Pause/resume on tap\n    if (this.isPaused) {\n      this.resumeStory();\n    } else {\n      this.pauseStory();\n    }\n  }\n  onMediaLoaded() {\n    // Media is loaded, story can start\n    if (this.currentMediaItem?.type === 'video') {\n      this.isStoryVideoPlaying = true;\n    }\n  }\n  handleImageError(event) {\n    this.mediaService.handleImageError(event, 'story');\n  }\n  handleVideoError(event) {\n    console.error('Story video error:', event);\n    // Try to replace with a working video\n    if (this.currentStory) {\n      const sampleVideo = this.mediaService.getRandomSampleVideo();\n      this.currentStory.media = {\n        type: 'video',\n        url: sampleVideo.url,\n        thumbnail: sampleVideo.thumbnail,\n        duration: sampleVideo.duration\n      };\n      this.updateCurrentMedia();\n    } else {\n      // Continue to next story on video error\n      this.nextStory();\n    }\n  }\n  toggleStoryVideo() {\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      if (videoElement.paused) {\n        videoElement.play();\n        this.isStoryVideoPlaying = true;\n        this.resumeStory();\n      } else {\n        videoElement.pause();\n        this.isStoryVideoPlaying = false;\n        this.pauseStory();\n      }\n    }\n  }\n  showProductDetails(productTag) {\n    this.selectedProduct = productTag;\n    this.pauseStory();\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n    this.resumeStory();\n  }\n  addToWishlist(productId) {\n    if (this.selectedProduct) {\n      this.wishlistService.addToWishlist(productId).subscribe({\n        next: () => {\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        },\n        error: error => {\n          console.error('Wishlist error:', error);\n          // Fallback to offline mode\n          this.wishlistService.addToWishlistOffline(this.selectedProduct.product);\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n  addToCart(productId) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        },\n        error: error => {\n          console.error('Cart error:', error);\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n  buyNow(productId) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Redirecting to checkout...');\n          this.closeProductModal();\n          this.router.navigate(['/shop/checkout']);\n        },\n        error: error => {\n          console.error('Buy now error:', error);\n          this.showNotification('Redirecting to product page...');\n          this.closeProductModal();\n          this.router.navigate(['/product', productId]);\n        }\n      });\n    }\n  }\n  showNotification(message) {\n    // Create a simple notification\n    const notification = document.createElement('div');\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 20px;\n      z-index: 30000;\n      font-size: 14px;\n      backdrop-filter: blur(10px);\n      animation: slideDown 0.3s ease;\n    `;\n    document.body.appendChild(notification);\n    setTimeout(() => {\n      notification.remove();\n    }, 2000);\n  }\n  sendMessage() {\n    if (this.messageText.trim()) {\n      console.log('Send message:', this.messageText);\n      this.messageText = '';\n    }\n  }\n  likeStory() {\n    console.log('Like story');\n  }\n  shareStory() {\n    console.log('Share story');\n  }\n  closeStory() {\n    this.router.navigate(['/']);\n  }\n  onStoryTap(event) {\n    const rect = event.target.getBoundingClientRect();\n    const tapX = event.clientX - rect.left;\n    const centerX = rect.width / 2;\n    if (tapX < centerX) {\n      // Tapped left side - previous story\n      this.previousStory();\n    } else {\n      // Tapped right side - next story\n      this.nextStory();\n    }\n  }\n  formatDuration(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n  static {\n    this.ɵfac = function StoryViewerComponent_Factory(t) {\n      return new (t || StoryViewerComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.StoryService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoryViewerComponent,\n      selectors: [[\"app-story-viewer\"]],\n      hostBindings: function StoryViewerComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function StoryViewerComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyboardEvent($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"videoElement\", \"\"], [\"class\", \"story-viewer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"story-viewer\", 3, \"click\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [1, \"time-ago\"], [1, \"story-actions\"], [\"class\", \"action-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-content\"], [\"class\", \"story-loading\", 4, \"ngIf\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", \"load\", \"error\", \"click\", 4, \"ngIf\"], [\"class\", \"story-media\", \"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 3, \"src\", \"poster\", \"loadeddata\", \"ended\", \"error\", \"click\", 4, \"ngIf\"], [\"class\", \"video-duration\", 4, \"ngIf\"], [\"class\", \"video-story-controls\", 4, \"ngIf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [1, \"nav-area\", \"nav-left\", 3, \"click\"], [1, \"nav-area\", \"nav-right\", 3, \"click\"], [1, \"story-footer\"], [1, \"story-input\"], [\"type\", \"text\", \"placeholder\", \"Send message\", 1, \"message-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"class\", \"send-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"story-reactions\"], [1, \"reaction-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"far\", \"fa-share\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"fas\", \"fa-pause\"], [1, \"fas\", \"fa-play\"], [1, \"story-loading\"], [1, \"loading-spinner\"], [1, \"story-media\", 3, \"load\", \"error\", \"click\", \"src\", \"alt\"], [\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 1, \"story-media\", 3, \"loadeddata\", \"ended\", \"error\", \"click\", \"src\", \"poster\"], [1, \"video-duration\"], [1, \"video-story-controls\"], [1, \"video-play-btn\", 3, \"click\"], [1, \"story-caption\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"top\", \"left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"tag-dot\"], [1, \"send-btn\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"product-modal\", 3, \"click\"], [1, \"product-modal-content\", 3, \"click\"], [1, \"product-header\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-price\"], [1, \"product-brand\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy-now\", 3, \"click\"], [1, \"loading-container\"], [1, \"spinner\"]],\n      template: function StoryViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StoryViewerComponent_div_0_Template, 35, 16, \"div\", 1)(1, StoryViewerComponent_div_1_Template, 21, 7, \"div\", 2)(2, StoryViewerComponent_div_2_Template, 2, 0, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, i6.DecimalPipe, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n      styles: [\".story-viewer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 10000;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  padding: 8px 16px;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  transition: width 0.1s linear;\\n  border-radius: 1px;\\n}\\n\\n.progress-fill.completed[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  position: absolute;\\n  top: 20px;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  object-fit: cover;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.time-ago[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5);\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background 0.2s;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n}\\n\\n.story-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.story-loading[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  border-top: 3px solid white;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.story-media[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: transform 0.1s ease;\\n}\\n\\n.story-media[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n\\n.video-duration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  font-family: monospace;\\n  z-index: 10;\\n}\\n\\n\\n\\n.video-story-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  right: 20px;\\n  z-index: 1000;\\n}\\n\\n.video-play-btn[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-size: 16px;\\n  color: #333;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.video-play-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n\\n.video-play-btn.playing[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n}\\n\\n.story-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.5);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.story-caption[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 2px solid #007bff;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #007bff;\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);\\n  }\\n}\\n.nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 30%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n\\n.nav-right[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.story-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.story-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 20px;\\n  padding: 12px 16px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #007bff;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-reactions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.reaction-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n}\\n\\n.product-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 20000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.product-modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  max-width: 400px;\\n  width: 100%;\\n  animation: _ngcontent-%COMP%_modalSlideUp 0.3s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalSlideUp {\\n  from {\\n    transform: translateY(50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n.product-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: #e91e63;\\n  margin: 0 0 4px 0;\\n}\\n\\n.product-brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%], .btn-cart[_ngcontent-%COMP%], .btn-buy-now[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  font-size: 12px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n  border: 1px solid #ddd;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: white;\\n}\\n\\n.btn-buy-now[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n  color: white;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 10000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  border-top: 3px solid white;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .story-viewer[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .story-container[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n    height: 100vh;\\n    border-radius: 0;\\n  }\\n  .progress-bars[_ngcontent-%COMP%] {\\n    padding: 1rem 1rem 0.5rem;\\n  }\\n  .progress-bar[_ngcontent-%COMP%] {\\n    height: 2px;\\n  }\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .user-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .header-actions[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .header-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 1rem;\\n  }\\n  .story-content[_ngcontent-%COMP%] {\\n    height: calc(100vh - 140px);\\n  }\\n  .story-footer[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .message-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n    font-size: 0.9rem;\\n  }\\n  .footer-actions[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .footer-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 1.2rem;\\n  }\\n  .nav-area[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n  .product-tag[_ngcontent-%COMP%] {\\n    transform: scale(0.9);\\n  }\\n  .product-info[_ngcontent-%COMP%] {\\n    width: 280px;\\n    padding: 1rem;\\n  }\\n  .product-image[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .product-details[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .product-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .product-btn[_ngcontent-%COMP%] {\\n    padding: 0.6rem 1rem;\\n    font-size: 0.8rem;\\n  }\\n  .video-story-controls[_ngcontent-%COMP%] {\\n    bottom: 120px;\\n    right: 15px;\\n  }\\n  .video-play-btn[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    font-size: 14px;\\n  }\\n  \\n\\n  .product-modal[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    max-width: 90vw;\\n    max-height: 80vh;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .modal-body[_ngcontent-%COMP%] {\\n    padding: 1rem 0;\\n  }\\n  .modal-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n  .modal-footer[_ngcontent-%COMP%]   .product-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n\\n@media (hover: none) and (pointer: coarse) {\\n  .header-btn[_ngcontent-%COMP%], .footer-btn[_ngcontent-%COMP%], .product-btn[_ngcontent-%COMP%], .video-play-btn[_ngcontent-%COMP%] {\\n    transform: none;\\n    transition: background-color 0.2s ease;\\n  }\\n  .header-btn[_ngcontent-%COMP%]:active, .footer-btn[_ngcontent-%COMP%]:active, .product-btn[_ngcontent-%COMP%]:active, .video-play-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n  }\\n  .product-tag[_ngcontent-%COMP%]:active {\\n    transform: scale(0.85);\\n  }\\n  \\n\\n  .video-story-controls[_ngcontent-%COMP%] {\\n    opacity: 0.8;\\n  }\\n  .video-play-btn[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) and (orientation: landscape) {\\n  .story-container[_ngcontent-%COMP%] {\\n    max-width: 100vh;\\n    margin: 0 auto;\\n  }\\n  .story-content[_ngcontent-%COMP%] {\\n    height: calc(100vh - 100px);\\n  }\\n  .story-header[_ngcontent-%COMP%], .story-footer[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n  }\\n  .progress-bars[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem 0.25rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "timer", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ctx_r1", "getProgressWidth", "i_r3", "ɵɵclassProp", "currentStoryIndex", "ɵɵlistener", "StoryViewerComponent_div_0_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "pauseStory", "StoryViewerComponent_div_0_button_13_Template_button_click_0_listener", "_r5", "resumeStory", "StoryViewerComponent_div_0_img_18_Template_img_load_0_listener", "_r6", "onMediaLoaded", "StoryViewerComponent_div_0_img_18_Template_img_error_0_listener", "$event", "handleImageError", "StoryViewerComponent_div_0_img_18_Template_img_click_0_listener", "onStoryTap", "ɵɵproperty", "currentMediaItem", "url", "ɵɵsanitizeUrl", "alt", "currentStory", "caption", "StoryViewerComponent_div_0_video_19_Template_video_loadeddata_0_listener", "_r7", "StoryViewerComponent_div_0_video_19_Template_video_ended_0_listener", "nextStory", "StoryViewerComponent_div_0_video_19_Template_video_error_0_listener", "handleVideoError", "StoryViewerComponent_div_0_video_19_Template_video_click_0_listener", "thumbnailUrl", "ɵɵtext", "ɵɵtextInterpolate1", "formatDuration", "duration", "StoryViewerComponent_div_0_div_21_Template_button_click_1_listener", "_r8", "toggleStoryVideo", "isStoryVideoPlaying", "ɵɵclassMap", "ɵɵtextInterpolate", "StoryViewerComponent_div_0_div_23_div_1_Template_div_click_0_listener", "productTag_r10", "_r9", "$implicit", "showProductDetails", "position", "y", "x", "ɵɵtemplate", "StoryViewerComponent_div_0_div_23_div_1_Template", "products", "StoryViewerComponent_div_0_button_29_Template_button_click_0_listener", "_r11", "sendMessage", "StoryViewerComponent_div_0_Template_div_click_0_listener", "_r1", "onStoryClick", "StoryViewerComponent_div_0_div_2_Template", "StoryViewerComponent_div_0_button_12_Template", "StoryViewerComponent_div_0_button_13_Template", "StoryViewerComponent_div_0_Template_button_click_14_listener", "closeStory", "StoryViewerComponent_div_0_div_17_Template", "StoryViewerComponent_div_0_img_18_Template", "StoryViewerComponent_div_0_video_19_Template", "StoryViewerComponent_div_0_div_20_Template", "StoryViewerComponent_div_0_div_21_Template", "StoryViewerComponent_div_0_div_22_Template", "StoryViewerComponent_div_0_div_23_Template", "StoryViewerComponent_div_0_Template_div_click_24_listener", "previousStory", "StoryViewerComponent_div_0_Template_div_click_25_listener", "ɵɵtwoWayListener", "StoryViewerComponent_div_0_Template_input_ngModelChange_28_listener", "ɵɵtwoWayBindingSet", "messageText", "StoryViewerComponent_div_0_Template_input_keyup_enter_28_listener", "StoryViewerComponent_div_0_button_29_Template", "StoryViewerComponent_div_0_Template_button_click_31_listener", "likeStory", "StoryViewerComponent_div_0_Template_button_click_33_listener", "shareStory", "userStories", "user", "avatar", "fullName", "username", "getTimeAgo", "createdAt", "isPaused", "isLoading", "type", "length", "ɵɵtwoWayProperty", "trim", "StoryViewerComponent_div_1_Template_div_click_0_listener", "_r12", "closeProductModal", "StoryViewerComponent_div_1_Template_div_click_1_listener", "stopPropagation", "StoryViewerComponent_div_1_Template_button_click_13_listener", "addToWishlist", "selectedProduct", "product", "_id", "StoryViewerComponent_div_1_Template_button_click_16_listener", "addToCart", "StoryViewerComponent_div_1_Template_button_click_19_listener", "buyNow", "images", "name", "ɵɵpipeBind1", "price", "brand", "StoryViewerComponent", "constructor", "route", "router", "storyService", "cartService", "wishlistService", "mediaService", "progress", "storyDuration", "ngOnInit", "params", "subscribe", "userId", "storyIndex", "parseInt", "loadUserStories", "ngOnDestroy", "stopProgress", "storyTimeout", "clearTimeout", "handleKeyboardEvent", "event", "key", "startIndex", "getUserStories", "next", "response", "stories", "enhanceStoriesWithMedia", "Math", "min", "updateCurrentMedia", "startStoryProgress", "error", "console", "map", "story", "media", "isBrokenMediaUrl", "contentHint", "p", "join", "sampleVideo", "getVideoByContentHint", "thumbnail", "getSafeImageUrl", "includes", "hint", "lowerHint", "toLowerCase", "getVideoByType", "getRandomSampleVideo", "id", "intervalMs", "increment", "progressSubscription", "unsubscribe", "undefined", "index", "videoElement", "document", "querySelector", "pause", "play", "target", "closest", "paused", "productTag", "productId", "showNotification", "addToWishlistOffline", "size", "color", "navigate", "message", "notification", "createElement", "textContent", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "remove", "log", "rect", "getBoundingClientRect", "tapX", "clientX", "left", "centerX", "width", "seconds", "minutes", "floor", "remainingSeconds", "toString", "padStart", "date", "now", "Date", "diff", "getTime", "hours", "days", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "StoryService", "i3", "CartService", "i4", "WishlistService", "i5", "MediaService", "selectors", "hostBindings", "StoryViewerComponent_HostBindings", "rf", "ctx", "StoryViewerComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "StoryViewerComponent_div_0_Template", "StoryViewerComponent_div_1_Template", "StoryViewerComponent_div_2_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\story\\story-viewer\\story-viewer.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription, interval, timer } from 'rxjs';\n\nimport { Story } from '../../../core/models/story.model';\nimport { StoryService } from '../../../core/services/story.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistService } from '../../../core/services/wishlist.service';\nimport { MediaService, MediaItem } from '../../../core/services/media.service';\n\n@Component({\n  selector: 'app-story-viewer',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"story-viewer\" *ngIf=\"currentStory\" (click)=\"onStoryClick($event)\">\n      <!-- Progress Bars -->\n      <div class=\"progress-container\">\n        <div \n          *ngFor=\"let story of userStories; let i = index\" \n          class=\"progress-bar\"\n        >\n          <div \n            class=\"progress-fill\"\n            [style.width.%]=\"getProgressWidth(i)\"\n            [class.active]=\"i === currentStoryIndex\"\n            [class.completed]=\"i < currentStoryIndex\"\n          ></div>\n        </div>\n      </div>\n\n      <!-- Story Header -->\n      <div class=\"story-header\">\n        <div class=\"user-info\">\n          <img [src]=\"currentStory.user.avatar\" [alt]=\"currentStory.user.fullName\" class=\"user-avatar\">\n          <div class=\"user-details\">\n            <span class=\"username\">{{ currentStory.user.username }}</span>\n            <span class=\"time-ago\">{{ getTimeAgo(currentStory.createdAt) }}</span>\n          </div>\n        </div>\n        <div class=\"story-actions\">\n          <button class=\"action-btn\" (click)=\"pauseStory()\" *ngIf=\"!isPaused\">\n            <i class=\"fas fa-pause\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"resumeStory()\" *ngIf=\"isPaused\">\n            <i class=\"fas fa-play\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"closeStory()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\">\n        <!-- Loading State -->\n        <div *ngIf=\"isLoading\" class=\"story-loading\">\n          <div class=\"loading-spinner\"></div>\n        </div>\n\n        <!-- Image Story -->\n        <img\n          *ngIf=\"currentMediaItem?.type === 'image' && !isLoading\"\n          [src]=\"currentMediaItem?.url || ''\"\n          [alt]=\"currentMediaItem?.alt || currentStory.caption || ''\"\n          class=\"story-media\"\n          (load)=\"onMediaLoaded()\"\n          (error)=\"handleImageError($event)\"\n          (click)=\"onStoryTap($event)\"\n        >\n\n        <!-- Video Story -->\n        <video\n          *ngIf=\"currentMediaItem?.type === 'video' && !isLoading\"\n          [src]=\"currentMediaItem?.url || ''\"\n          [poster]=\"currentMediaItem?.thumbnailUrl || ''\"\n          class=\"story-media\"\n          autoplay\n          muted\n          playsinline\n          (loadeddata)=\"onMediaLoaded()\"\n          (ended)=\"nextStory()\"\n          (error)=\"handleVideoError($event)\"\n          (click)=\"onStoryTap($event)\"\n          #videoElement\n        ></video>\n\n        <!-- Video Duration Indicator -->\n        <div *ngIf=\"currentMediaItem?.type === 'video' && currentMediaItem?.duration\" class=\"video-duration\">\n          {{ formatDuration(currentMediaItem?.duration || 0) }}\n        </div>\n\n        <!-- Video Controls for Stories -->\n        <div *ngIf=\"currentMediaItem?.type === 'video'\" class=\"video-story-controls\">\n          <button class=\"video-play-btn\" (click)=\"toggleStoryVideo()\" [class.playing]=\"isStoryVideoPlaying\">\n            <i [class]=\"isStoryVideoPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\n          </button>\n        </div>\n\n        <!-- Story Caption -->\n        <div class=\"story-caption\" *ngIf=\"currentStory.caption\">\n          <p>{{ currentStory.caption }}</p>\n        </div>\n\n        <!-- Product Tags -->\n        <div class=\"product-tags\" *ngIf=\"currentStory.products.length > 0\">\n          <div \n            *ngFor=\"let productTag of currentStory.products\" \n            class=\"product-tag\"\n            [style.top.%]=\"productTag.position.y\"\n            [style.left.%]=\"productTag.position.x\"\n            (click)=\"showProductDetails(productTag)\"\n          >\n            <div class=\"tag-dot\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation Areas -->\n      <div class=\"nav-area nav-left\" (click)=\"previousStory()\"></div>\n      <div class=\"nav-area nav-right\" (click)=\"nextStory()\"></div>\n\n      <!-- Story Footer -->\n      <div class=\"story-footer\">\n        <div class=\"story-input\">\n          <input \n            type=\"text\" \n            placeholder=\"Send message\" \n            [(ngModel)]=\"messageText\"\n            (keyup.enter)=\"sendMessage()\"\n            class=\"message-input\"\n          >\n          <button class=\"send-btn\" (click)=\"sendMessage()\" *ngIf=\"messageText.trim()\">\n            <i class=\"fas fa-paper-plane\"></i>\n          </button>\n        </div>\n        <div class=\"story-reactions\">\n          <button class=\"reaction-btn\" (click)=\"likeStory()\">\n            <i class=\"far fa-heart\"></i>\n          </button>\n          <button class=\"reaction-btn\" (click)=\"shareStory()\">\n            <i class=\"far fa-share\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Product Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"product-modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"product-header\">\n          <img [src]=\"selectedProduct.product.images[0].url\" [alt]=\"selectedProduct.product.name\" class=\"product-image\">\n          <div class=\"product-info\">\n            <h3>{{ selectedProduct.product.name }}</h3>\n            <p class=\"product-price\">₹{{ selectedProduct.product.price | number }}</p>\n            <p class=\"product-brand\">{{ selectedProduct.product.brand }}</p>\n          </div>\n        </div>\n        <div class=\"product-actions\">\n          <button class=\"btn-wishlist\" (click)=\"addToWishlist(selectedProduct.product._id)\">\n            <i class=\"far fa-heart\"></i>\n            Wishlist\n          </button>\n          <button class=\"btn-cart\" (click)=\"addToCart(selectedProduct.product._id)\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            Add to Cart\n          </button>\n          <button class=\"btn-buy-now\" (click)=\"buyNow(selectedProduct.product._id)\">\n            Buy Now\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\n      <div class=\"spinner\"></div>\n    </div>\n  `,\n  styles: [`\n    .story-viewer {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 10000;\n      display: flex;\n      flex-direction: column;\n      user-select: none;\n    }\n\n    .progress-container {\n      display: flex;\n      gap: 2px;\n      padding: 8px 16px;\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .progress-bar {\n      flex: 1;\n      height: 2px;\n      background: rgba(255, 255, 255, 0.3);\n      border-radius: 1px;\n      overflow: hidden;\n    }\n\n    .progress-fill {\n      height: 100%;\n      background: white;\n      transition: width 0.1s linear;\n      border-radius: 1px;\n    }\n\n    .progress-fill.completed {\n      width: 100% !important;\n    }\n\n    .story-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 16px;\n      position: absolute;\n      top: 20px;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      border: 2px solid white;\n      object-fit: cover;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username {\n      color: white;\n      font-weight: 600;\n      font-size: 14px;\n    }\n\n    .time-ago {\n      color: rgba(255, 255, 255, 0.7);\n      font-size: 12px;\n    }\n\n    .story-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .action-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: rgba(0, 0, 0, 0.5);\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: background 0.2s;\n    }\n\n    .action-btn:hover {\n      background: rgba(0, 0, 0, 0.7);\n    }\n\n    .story-content {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .story-loading {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      z-index: 10;\n    }\n\n    .loading-spinner {\n      width: 40px;\n      height: 40px;\n      border: 3px solid rgba(255, 255, 255, 0.3);\n      border-top: 3px solid white;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .story-media {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n      cursor: pointer;\n      user-select: none;\n      transition: transform 0.1s ease;\n    }\n\n    .story-media:active {\n      transform: scale(0.98);\n    }\n\n    .video-duration {\n      position: absolute;\n      top: 20px;\n      right: 20px;\n      background: rgba(0, 0, 0, 0.7);\n      color: white;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 12px;\n      font-weight: 500;\n      font-family: monospace;\n      z-index: 10;\n    }\n\n    /* Video Story Controls */\n    .video-story-controls {\n      position: absolute;\n      bottom: 100px;\n      right: 20px;\n      z-index: 1000;\n    }\n\n    .video-play-btn {\n      width: 50px;\n      height: 50px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.9);\n      border: none;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      font-size: 16px;\n      color: #333;\n      backdrop-filter: blur(10px);\n    }\n\n    .video-play-btn:hover {\n      background: white;\n      transform: scale(1.1);\n    }\n\n    .video-play-btn.playing {\n      background: rgba(0, 0, 0, 0.7);\n      color: white;\n    }\n\n    .story-caption {\n      position: absolute;\n      bottom: 100px;\n      left: 16px;\n      right: 16px;\n      background: rgba(0, 0, 0, 0.5);\n      color: white;\n      padding: 12px 16px;\n      border-radius: 20px;\n      backdrop-filter: blur(10px);\n    }\n\n    .story-caption p {\n      margin: 0;\n      font-size: 14px;\n      line-height: 1.4;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n    }\n\n    .tag-dot {\n      width: 24px;\n      height: 24px;\n      background: white;\n      border-radius: 50%;\n      border: 2px solid #007bff;\n      position: relative;\n      animation: pulse 2s infinite;\n    }\n\n    .tag-dot::after {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      width: 8px;\n      height: 8px;\n      background: #007bff;\n      border-radius: 50%;\n    }\n\n    @keyframes pulse {\n      0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }\n      70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }\n      100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }\n    }\n\n    .nav-area {\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      width: 30%;\n      z-index: 5;\n      cursor: pointer;\n    }\n\n    .nav-left {\n      left: 0;\n    }\n\n    .nav-right {\n      right: 0;\n    }\n\n    .story-footer {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      padding: 16px;\n      background: rgba(0, 0, 0, 0.3);\n      backdrop-filter: blur(10px);\n    }\n\n    .story-input {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .message-input {\n      flex: 1;\n      background: rgba(255, 255, 255, 0.1);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      border-radius: 20px;\n      padding: 12px 16px;\n      color: white;\n      font-size: 14px;\n    }\n\n    .message-input::placeholder {\n      color: rgba(255, 255, 255, 0.7);\n    }\n\n    .message-input:focus {\n      outline: none;\n      border-color: rgba(255, 255, 255, 0.4);\n    }\n\n    .send-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: #007bff;\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .story-reactions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .reaction-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 18px;\n    }\n\n    .product-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0, 0, 0, 0.8);\n      z-index: 20000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .product-modal-content {\n      background: white;\n      border-radius: 16px;\n      padding: 24px;\n      max-width: 400px;\n      width: 100%;\n      animation: modalSlideUp 0.3s ease;\n    }\n\n    @keyframes modalSlideUp {\n      from {\n        transform: translateY(50px);\n        opacity: 0;\n      }\n      to {\n        transform: translateY(0);\n        opacity: 1;\n      }\n    }\n\n    .product-header {\n      display: flex;\n      gap: 16px;\n      margin-bottom: 20px;\n    }\n\n    .product-image {\n      width: 80px;\n      height: 80px;\n      border-radius: 8px;\n      object-fit: cover;\n    }\n\n    .product-info h3 {\n      margin: 0 0 8px 0;\n      font-size: 18px;\n      font-weight: 600;\n    }\n\n    .product-price {\n      font-size: 20px;\n      font-weight: 700;\n      color: #e91e63;\n      margin: 0 0 4px 0;\n    }\n\n    .product-brand {\n      color: #666;\n      margin: 0;\n      font-size: 14px;\n    }\n\n    .product-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .btn-wishlist, .btn-cart, .btn-buy-now {\n      flex: 1;\n      padding: 12px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      font-size: 12px;\n    }\n\n    .btn-wishlist {\n      background: #f8f9fa;\n      color: #666;\n      border: 1px solid #ddd;\n    }\n\n    .btn-cart {\n      background: #2196f3;\n      color: white;\n    }\n\n    .btn-buy-now {\n      background: #ff9800;\n      color: white;\n    }\n\n    .loading-container {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 10000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 3px solid rgba(255, 255, 255, 0.3);\n      border-top: 3px solid white;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    /* Mobile Responsive Styles */\n    @media (max-width: 768px) {\n      .story-viewer {\n        padding: 0;\n      }\n\n      .story-container {\n        max-width: 100vw;\n        height: 100vh;\n        border-radius: 0;\n      }\n\n      .progress-bars {\n        padding: 1rem 1rem 0.5rem;\n      }\n\n      .progress-bar {\n        height: 2px;\n      }\n\n      .story-header {\n        padding: 0.75rem 1rem;\n      }\n\n      .user-avatar {\n        width: 35px;\n        height: 35px;\n      }\n\n      .user-info h3 {\n        font-size: 0.9rem;\n      }\n\n      .user-info span {\n        font-size: 0.8rem;\n      }\n\n      .header-actions {\n        gap: 1rem;\n      }\n\n      .header-btn {\n        width: 35px;\n        height: 35px;\n        font-size: 1rem;\n      }\n\n      .story-content {\n        height: calc(100vh - 140px);\n      }\n\n      .story-footer {\n        padding: 1rem;\n      }\n\n      .message-input {\n        padding: 0.75rem 1rem;\n        font-size: 0.9rem;\n      }\n\n      .footer-actions {\n        gap: 1rem;\n      }\n\n      .footer-btn {\n        width: 35px;\n        height: 35px;\n        font-size: 1.2rem;\n      }\n\n      .nav-area {\n        width: 40%;\n      }\n\n      .product-tag {\n        transform: scale(0.9);\n      }\n\n      .product-info {\n        width: 280px;\n        padding: 1rem;\n      }\n\n      .product-image {\n        width: 60px;\n        height: 60px;\n      }\n\n      .product-details h5 {\n        font-size: 0.9rem;\n      }\n\n      .product-details p {\n        font-size: 0.8rem;\n      }\n\n      .product-actions {\n        flex-direction: column;\n        gap: 0.5rem;\n      }\n\n      .product-btn {\n        padding: 0.6rem 1rem;\n        font-size: 0.8rem;\n      }\n\n      .video-story-controls {\n        bottom: 120px;\n        right: 15px;\n      }\n\n      .video-play-btn {\n        width: 45px;\n        height: 45px;\n        font-size: 14px;\n      }\n\n      /* Product Modal Mobile */\n      .product-modal {\n        padding: 1rem;\n        max-width: 90vw;\n        max-height: 80vh;\n      }\n\n      .modal-content {\n        padding: 1rem;\n      }\n\n      .modal-header h4 {\n        font-size: 1.1rem;\n      }\n\n      .modal-body {\n        padding: 1rem 0;\n      }\n\n      .modal-footer {\n        flex-direction: column;\n        gap: 0.75rem;\n      }\n\n      .modal-footer .product-btn {\n        width: 100%;\n        justify-content: center;\n      }\n    }\n\n    /* Touch-friendly interactions for mobile */\n    @media (hover: none) and (pointer: coarse) {\n      .header-btn,\n      .footer-btn,\n      .product-btn,\n      .video-play-btn {\n        transform: none;\n        transition: background-color 0.2s ease;\n      }\n\n      .header-btn:active,\n      .footer-btn:active,\n      .product-btn:active,\n      .video-play-btn:active {\n        transform: scale(0.95);\n      }\n\n      .product-tag:active {\n        transform: scale(0.85);\n      }\n\n      /* Always show video controls on touch devices */\n      .video-story-controls {\n        opacity: 0.8;\n      }\n\n      .video-play-btn {\n        background: rgba(255, 255, 255, 0.95);\n      }\n    }\n\n    /* Landscape mobile orientation */\n    @media (max-width: 768px) and (orientation: landscape) {\n      .story-container {\n        max-width: 100vh;\n        margin: 0 auto;\n      }\n\n      .story-content {\n        height: calc(100vh - 100px);\n      }\n\n      .story-header,\n      .story-footer {\n        padding: 0.5rem 1rem;\n      }\n\n      .progress-bars {\n        padding: 0.5rem 1rem 0.25rem;\n      }\n    }\n  `]\n})\nexport class StoryViewerComponent implements OnInit, OnDestroy {\n  userStories: Story[] = [];\n  currentStoryIndex = 0;\n  currentStory: Story | null = null;\n\n  isLoading = true;\n  isPaused = false;\n  progress = 0;\n  storyDuration = 5000; // 5 seconds for images\n\n  messageText = '';\n  selectedProduct: any = null;\n\n  // Media handling\n  currentMediaItem: MediaItem | null = null;\n  isStoryVideoPlaying = false;\n\n  private progressSubscription?: Subscription;\n  private storyTimeout?: any;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private storyService: StoryService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const userId = params['userId'];\n      const storyIndex = parseInt(params['storyIndex']) || 0;\n      \n      if (userId) {\n        this.loadUserStories(userId, storyIndex);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this.stopProgress();\n    if (this.storyTimeout) {\n      clearTimeout(this.storyTimeout);\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeyboardEvent(event: KeyboardEvent) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStory();\n        break;\n    }\n  }\n\n  loadUserStories(userId: string, startIndex: number = 0) {\n    this.isLoading = true;\n\n    this.storyService.getUserStories(userId).subscribe({\n      next: (response) => {\n        this.userStories = response.stories;\n\n        // Enhance stories with video content if needed\n        this.userStories = this.enhanceStoriesWithMedia(this.userStories);\n\n        this.currentStoryIndex = Math.min(startIndex, this.userStories.length - 1);\n        this.currentStory = this.userStories[this.currentStoryIndex];\n        this.updateCurrentMedia();\n        this.isLoading = false;\n        this.startStoryProgress();\n      },\n      error: (error) => {\n        console.error('Failed to load stories:', error);\n        this.isLoading = false;\n        this.closeStory();\n      }\n    });\n  }\n\n  enhanceStoriesWithMedia(stories: Story[]): Story[] {\n    return stories.map((story) => {\n      // If story doesn't have media or has broken media, add sample video\n      if (!story.media || !story.media.url || this.isBrokenMediaUrl(story.media.url)) {\n        // Get appropriate video based on story content\n        const contentHint = story.caption || story.products?.map(p => p.product.name).join(' ') || '';\n        const sampleVideo = this.getVideoByContentHint(contentHint);\n\n        story.media = {\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnail: sampleVideo.thumbnail,\n          duration: sampleVideo.duration\n        };\n      }\n\n      // Fix broken image URLs\n      if (story.media && story.media.type === 'image' && this.isBrokenMediaUrl(story.media.url)) {\n        story.media = {\n          ...story.media,\n          url: this.mediaService.getSafeImageUrl(story.media.url, 'story'),\n          thumbnail: this.mediaService.getSafeImageUrl(story.media.thumbnail, 'story')\n        };\n      }\n\n      return story;\n    });\n  }\n\n  private isBrokenMediaUrl(url: string): boolean {\n    return url.includes('/uploads/') || url.includes('sample-videos.com') || url.includes('localhost');\n  }\n\n  private getVideoByContentHint(hint: string): any {\n    const lowerHint = hint.toLowerCase();\n\n    if (lowerHint.includes('fashion') || lowerHint.includes('style')) {\n      return this.mediaService.getVideoByType('fashion');\n    }\n    if (lowerHint.includes('tutorial') || lowerHint.includes('tips')) {\n      return this.mediaService.getVideoByType('tutorial');\n    }\n    if (lowerHint.includes('showcase') || lowerHint.includes('collection')) {\n      return this.mediaService.getVideoByType('showcase');\n    }\n    if (lowerHint.includes('behind') || lowerHint.includes('process')) {\n      return this.mediaService.getVideoByType('story');\n    }\n\n    return this.mediaService.getRandomSampleVideo();\n  }\n\n  updateCurrentMedia() {\n    if (this.currentStory?.media) {\n      this.currentMediaItem = {\n        id: this.currentStory._id,\n        type: this.currentStory.media.type,\n        url: this.mediaService.getSafeImageUrl(this.currentStory.media.url, 'story'),\n        thumbnailUrl: this.currentStory.media.thumbnail,\n        alt: this.currentStory.caption,\n        duration: this.currentStory.media.duration\n      };\n    }\n  }\n\n  startStoryProgress() {\n    this.stopProgress();\n    this.progress = 0;\n    \n    if (this.currentStory?.media.type === 'video') {\n      // For videos, let the video control the progress\n      return;\n    }\n    \n    const intervalMs = 50; // Update every 50ms\n    const increment = (intervalMs / this.storyDuration) * 100;\n\n    this.progressSubscription = timer(0, intervalMs).subscribe(() => {\n      if (!this.isPaused) {\n        this.progress += increment;\n        if (this.progress >= 100) {\n          this.nextStory();\n        }\n      }\n    });\n  }\n\n  stopProgress() {\n    if (this.progressSubscription) {\n      this.progressSubscription.unsubscribe();\n      this.progressSubscription = undefined;\n    }\n  }\n\n  getProgressWidth(index: number): number {\n    if (index < this.currentStoryIndex) {\n      return 100;\n    } else if (index === this.currentStoryIndex) {\n      return this.progress;\n    } else {\n      return 0;\n    }\n  }\n\n  nextStory() {\n    if (this.currentStoryIndex < this.userStories.length - 1) {\n      this.currentStoryIndex++;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    } else {\n      // Move to next user's stories or close\n      this.closeStory();\n    }\n  }\n\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    }\n  }\n\n  pauseStory() {\n    this.isPaused = true;\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      videoElement.pause();\n    }\n  }\n\n  resumeStory() {\n    this.isPaused = false;\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      videoElement.play();\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    const target = event.target as HTMLElement;\n    \n    // Don't handle clicks on interactive elements\n    if (target.closest('.story-header') || \n        target.closest('.story-footer') || \n        target.closest('.product-tag') ||\n        target.closest('.nav-area')) {\n      return;\n    }\n    \n    // Pause/resume on tap\n    if (this.isPaused) {\n      this.resumeStory();\n    } else {\n      this.pauseStory();\n    }\n  }\n\n  onMediaLoaded() {\n    // Media is loaded, story can start\n    if (this.currentMediaItem?.type === 'video') {\n      this.isStoryVideoPlaying = true;\n    }\n  }\n\n  handleImageError(event: Event): void {\n    this.mediaService.handleImageError(event, 'story');\n  }\n\n  handleVideoError(event: Event): void {\n    console.error('Story video error:', event);\n    // Try to replace with a working video\n    if (this.currentStory) {\n      const sampleVideo = this.mediaService.getRandomSampleVideo();\n      this.currentStory.media = {\n        type: 'video',\n        url: sampleVideo.url,\n        thumbnail: sampleVideo.thumbnail,\n        duration: sampleVideo.duration\n      };\n      this.updateCurrentMedia();\n    } else {\n      // Continue to next story on video error\n      this.nextStory();\n    }\n  }\n\n  toggleStoryVideo(): void {\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      if (videoElement.paused) {\n        videoElement.play();\n        this.isStoryVideoPlaying = true;\n        this.resumeStory();\n      } else {\n        videoElement.pause();\n        this.isStoryVideoPlaying = false;\n        this.pauseStory();\n      }\n    }\n  }\n\n  showProductDetails(productTag: any) {\n    this.selectedProduct = productTag;\n    this.pauseStory();\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n    this.resumeStory();\n  }\n\n  addToWishlist(productId: string) {\n    if (this.selectedProduct) {\n      this.wishlistService.addToWishlist(productId).subscribe({\n        next: () => {\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        },\n        error: (error) => {\n          console.error('Wishlist error:', error);\n          // Fallback to offline mode\n          this.wishlistService.addToWishlistOffline(this.selectedProduct.product);\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n\n  addToCart(productId: string) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        },\n        error: (error: any) => {\n          console.error('Cart error:', error);\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n\n  buyNow(productId: string) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Redirecting to checkout...');\n          this.closeProductModal();\n          this.router.navigate(['/shop/checkout']);\n        },\n        error: (error: any) => {\n          console.error('Buy now error:', error);\n          this.showNotification('Redirecting to product page...');\n          this.closeProductModal();\n          this.router.navigate(['/product', productId]);\n        }\n      });\n    }\n  }\n\n  private showNotification(message: string) {\n    // Create a simple notification\n    const notification = document.createElement('div');\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 20px;\n      z-index: 30000;\n      font-size: 14px;\n      backdrop-filter: blur(10px);\n      animation: slideDown 0.3s ease;\n    `;\n\n    document.body.appendChild(notification);\n\n    setTimeout(() => {\n      notification.remove();\n    }, 2000);\n  }\n\n  sendMessage() {\n    if (this.messageText.trim()) {\n      console.log('Send message:', this.messageText);\n      this.messageText = '';\n    }\n  }\n\n  likeStory() {\n    console.log('Like story');\n  }\n\n  shareStory() {\n    console.log('Share story');\n  }\n\n  closeStory() {\n    this.router.navigate(['/']);\n  }\n\n  onStoryTap(event: MouseEvent): void {\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const tapX = event.clientX - rect.left;\n    const centerX = rect.width / 2;\n\n    if (tapX < centerX) {\n      // Tapped left side - previous story\n      this.previousStory();\n    } else {\n      // Tapped right side - next story\n      this.nextStory();\n    }\n  }\n\n  formatDuration(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    \n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAAiCC,KAAK,QAAQ,MAAM;;;;;;;;;;;IAgB5CC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,cAKO;IACTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJFH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAK,WAAA,UAAAC,MAAA,CAAAC,gBAAA,CAAAC,IAAA,OAAqC;IAErCR,EADA,CAAAS,WAAA,WAAAD,IAAA,KAAAF,MAAA,CAAAI,iBAAA,CAAwC,cAAAF,IAAA,GAAAF,MAAA,CAAAI,iBAAA,CACC;;;;;;IAe3CV,EAAA,CAAAC,cAAA,iBAAoE;IAAzCD,EAAA,CAAAW,UAAA,mBAAAC,sEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IAC/CjB,EAAA,CAAAE,SAAA,YAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAoE;IAAzCD,EAAA,CAAAW,UAAA,mBAAAO,sEAAA;MAAAlB,EAAA,CAAAa,aAAA,CAAAM,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAc,WAAA,EAAa;IAAA,EAAC;IAChDpB,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;;;;;IAUXH,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAQC;IADCD,EAFA,CAAAW,UAAA,kBAAAU,+DAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAQV,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC,mBAAAC,gEAAAC,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACfV,MAAA,CAAAoB,gBAAA,CAAAD,MAAA,CAAwB;IAAA,EAAC,mBAAAE,gEAAAF,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACzBV,MAAA,CAAAsB,UAAA,CAAAH,MAAA,CAAkB;IAAA,EAAC;IAP9BzB,EAAA,CAAAG,YAAA,EAQC;;;;IALCH,EADA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAAC,GAAA,SAAA/B,EAAA,CAAAgC,aAAA,CAAmC,SAAA1B,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAAG,GAAA,KAAA3B,MAAA,CAAA4B,YAAA,CAAAC,OAAA,OACwB;;;;;;IAQ7DnC,EAAA,CAAAC,cAAA,mBAaC;IAFCD,EAHA,CAAAW,UAAA,wBAAAyB,yEAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAwB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAcV,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC,mBAAAe,oEAAA;MAAAtC,EAAA,CAAAa,aAAA,CAAAwB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACrBV,MAAA,CAAAiC,SAAA,EAAW;IAAA,EAAC,mBAAAC,oEAAAf,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAwB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACZV,MAAA,CAAAmC,gBAAA,CAAAhB,MAAA,CAAwB;IAAA,EAAC,mBAAAiB,oEAAAjB,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAwB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACzBV,MAAA,CAAAsB,UAAA,CAAAH,MAAA,CAAkB;IAAA,EAAC;IAE7BzB,EAAA,CAAAG,YAAA,EAAQ;;;;IAVPH,EADA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAAC,GAAA,SAAA/B,EAAA,CAAAgC,aAAA,CAAmC,YAAA1B,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAAa,YAAA,SAAA3C,EAAA,CAAAgC,aAAA,CACY;;;;;IAajDhC,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAA4C,MAAA,GACF;IAAA5C,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA6C,kBAAA,MAAAvC,MAAA,CAAAwC,cAAA,EAAAxC,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAAiB,QAAA,aACF;;;;;;IAIE/C,EADF,CAAAC,cAAA,cAA6E,iBACuB;IAAnED,EAAA,CAAAW,UAAA,mBAAAqC,mEAAA;MAAAhD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA4C,gBAAA,EAAkB;IAAA,EAAC;IACzDlD,EAAA,CAAAE,SAAA,QAAsE;IAE1EF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAHwDH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAS,WAAA,YAAAH,MAAA,CAAA6C,mBAAA,CAAqC;IAC5FnD,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAAoD,UAAA,CAAA9C,MAAA,CAAA6C,mBAAA,kCAA8D;;;;;IAMnEnD,EADF,CAAAC,cAAA,cAAwD,QACnD;IAAAD,EAAA,CAAA4C,MAAA,GAA0B;IAC/B5C,EAD+B,CAAAG,YAAA,EAAI,EAC7B;;;;IADDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAqD,iBAAA,CAAA/C,MAAA,CAAA4B,YAAA,CAAAC,OAAA,CAA0B;;;;;;IAK7BnC,EAAA,CAAAC,cAAA,cAMC;IADCD,EAAA,CAAAW,UAAA,mBAAA2C,sEAAA;MAAA,MAAAC,cAAA,GAAAvD,EAAA,CAAAa,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAAnD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAoD,kBAAA,CAAAH,cAAA,CAA8B;IAAA,EAAC;IAExCvD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IAJJH,EADA,CAAAK,WAAA,QAAAkD,cAAA,CAAAI,QAAA,CAAAC,CAAA,MAAqC,SAAAL,cAAA,CAAAI,QAAA,CAAAE,CAAA,MACC;;;;;IAL1C7D,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAA8D,UAAA,IAAAC,gDAAA,kBAMC;IAGH/D,EAAA,CAAAG,YAAA,EAAM;;;;IARqBH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAA6B,UAAA,YAAAvB,MAAA,CAAA4B,YAAA,CAAA8B,QAAA,CAAwB;;;;;;IAyBjDhE,EAAA,CAAAC,cAAA,iBAA4E;IAAnDD,EAAA,CAAAW,UAAA,mBAAAsD,sEAAA;MAAAjE,EAAA,CAAAa,aAAA,CAAAqD,IAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA6D,WAAA,EAAa;IAAA,EAAC;IAC9CnE,EAAA,CAAAE,SAAA,YAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvHfH,EAAA,CAAAC,cAAA,aAA8E;IAA/BD,EAAA,CAAAW,UAAA,mBAAAyD,yDAAA3C,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAgE,YAAA,CAAA7C,MAAA,CAAoB;IAAA,EAAC;IAE3EzB,EAAA,CAAAC,cAAA,aAAgC;IAC9BD,EAAA,CAAA8D,UAAA,IAAAS,yCAAA,iBAGC;IAQHvE,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,aAA0B,aACD;IACrBD,EAAA,CAAAE,SAAA,aAA6F;IAE3FF,EADF,CAAAC,cAAA,cAA0B,eACD;IAAAD,EAAA,CAAA4C,MAAA,GAAgC;IAAA5C,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAA4C,MAAA,IAAwC;IAEnE5C,EAFmE,CAAAG,YAAA,EAAO,EAClE,EACF;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAIzBD,EAHA,CAAA8D,UAAA,KAAAU,6CAAA,qBAAoE,KAAAC,6CAAA,qBAGA;IAGpEzE,EAAA,CAAAC,cAAA,kBAAkD;IAAvBD,EAAA,CAAAW,UAAA,mBAAA+D,6DAAA;MAAA1E,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAqE,UAAA,EAAY;IAAA,EAAC;IAC/C3E,EAAA,CAAAE,SAAA,aAA4B;IAGlCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IAmDzBD,EAjDA,CAAA8D,UAAA,KAAAc,0CAAA,kBAA6C,KAAAC,0CAAA,kBAa5C,KAAAC,4CAAA,oBAgBA,KAAAC,0CAAA,kBAGoG,KAAAC,0CAAA,kBAKxB,KAAAC,0CAAA,kBAOrB,KAAAC,0CAAA,kBAKW;IAWrElF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAA1BD,EAAA,CAAAW,UAAA,mBAAAwE,0DAAA;MAAAnF,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA8E,aAAA,EAAe;IAAA,EAAC;IAACpF,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,eAAsD;IAAtBD,EAAA,CAAAW,UAAA,mBAAA0E,0DAAA;MAAArF,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAiC,SAAA,EAAW;IAAA,EAAC;IAACvC,EAAA,CAAAG,YAAA,EAAM;IAKxDH,EAFJ,CAAAC,cAAA,eAA0B,eACC,iBAOtB;IAHCD,EAAA,CAAAsF,gBAAA,2BAAAC,oEAAA9D,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAwF,kBAAA,CAAAlF,MAAA,CAAAmF,WAAA,EAAAhE,MAAA,MAAAnB,MAAA,CAAAmF,WAAA,GAAAhE,MAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAAAS,MAAA;IAAA,EAAyB;IACzBzB,EAAA,CAAAW,UAAA,yBAAA+E,kEAAA;MAAA1F,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAeV,MAAA,CAAA6D,WAAA,EAAa;IAAA,EAAC;IAJ/BnE,EAAA,CAAAG,YAAA,EAMC;IACDH,EAAA,CAAA8D,UAAA,KAAA6B,6CAAA,qBAA4E;IAG9E3F,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA6B,kBACwB;IAAtBD,EAAA,CAAAW,UAAA,mBAAAiF,6DAAA;MAAA5F,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAuF,SAAA,EAAW;IAAA,EAAC;IAChD7F,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAoD;IAAvBD,EAAA,CAAAW,UAAA,mBAAAmF,6DAAA;MAAA9F,EAAA,CAAAa,aAAA,CAAAwD,GAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAyF,UAAA,EAAY;IAAA,EAAC;IACjD/F,EAAA,CAAAE,SAAA,aAA4B;IAIpCF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IA9HkBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA6B,UAAA,YAAAvB,MAAA,CAAA0F,WAAA,CAAgB;IAe7BhG,EAAA,CAAAI,SAAA,GAAgC;IAACJ,EAAjC,CAAA6B,UAAA,QAAAvB,MAAA,CAAA4B,YAAA,CAAA+D,IAAA,CAAAC,MAAA,EAAAlG,EAAA,CAAAgC,aAAA,CAAgC,QAAA1B,MAAA,CAAA4B,YAAA,CAAA+D,IAAA,CAAAE,QAAA,CAAmC;IAE/CnG,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAqD,iBAAA,CAAA/C,MAAA,CAAA4B,YAAA,CAAA+D,IAAA,CAAAG,QAAA,CAAgC;IAChCpG,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAqD,iBAAA,CAAA/C,MAAA,CAAA+F,UAAA,CAAA/F,MAAA,CAAA4B,YAAA,CAAAoE,SAAA,EAAwC;IAIdtG,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAA6B,UAAA,UAAAvB,MAAA,CAAAiG,QAAA,CAAe;IAGdvG,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAiG,QAAA,CAAc;IAY9DvG,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAkG,SAAA,CAAe;IAMlBxG,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAA6B,UAAA,UAAAvB,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAA2E,IAAA,kBAAAnG,MAAA,CAAAkG,SAAA,CAAsD;IAWtDxG,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAA6B,UAAA,UAAAvB,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAA2E,IAAA,kBAAAnG,MAAA,CAAAkG,SAAA,CAAsD;IAenDxG,EAAA,CAAAI,SAAA,EAAsE;IAAtEJ,EAAA,CAAA6B,UAAA,UAAAvB,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAA2E,IAAA,kBAAAnG,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAAiB,QAAA,EAAsE;IAKtE/C,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAA6B,UAAA,UAAAvB,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAAwB,gBAAA,CAAA2E,IAAA,cAAwC;IAOlBzG,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAA4B,YAAA,CAAAC,OAAA,CAA0B;IAK3BnC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAA4B,YAAA,CAAA8B,QAAA,CAAA0C,MAAA,KAAsC;IAuB7D1G,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAA2G,gBAAA,YAAArG,MAAA,CAAAmF,WAAA,CAAyB;IAIuBzF,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAA6B,UAAA,SAAAvB,MAAA,CAAAmF,WAAA,CAAAmB,IAAA,GAAwB;;;;;;IAgBhF5G,EAAA,CAAAC,cAAA,cAAiF;IAA9BD,EAAA,CAAAW,UAAA,mBAAAkG,yDAAA;MAAA7G,EAAA,CAAAa,aAAA,CAAAiG,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAyG,iBAAA,EAAmB;IAAA,EAAC;IAC9E/G,EAAA,CAAAC,cAAA,cAAsE;IAAnCD,EAAA,CAAAW,UAAA,mBAAAqG,yDAAAvF,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAiG,IAAA;MAAA,OAAA9G,EAAA,CAAAgB,WAAA,CAASS,MAAA,CAAAwF,eAAA,EAAwB;IAAA,EAAC;IACnEjH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,cAA8G;IAE5GF,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAA4C,MAAA,GAAkC;IAAA5C,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAA4C,MAAA,GAA6C;;IAAA5C,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAA4C,MAAA,IAAmC;IAEhE5C,EAFgE,CAAAG,YAAA,EAAI,EAC5D,EACF;IAEJH,EADF,CAAAC,cAAA,eAA6B,kBACuD;IAArDD,EAAA,CAAAW,UAAA,mBAAAuG,6DAAA;MAAAlH,EAAA,CAAAa,aAAA,CAAAiG,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA6G,aAAA,CAAA7G,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAA0C;IAAA,EAAC;IAC/EtH,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAA4C,MAAA,kBACF;IAAA5C,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAAjDD,EAAA,CAAAW,UAAA,mBAAA4G,6DAAA;MAAAvH,EAAA,CAAAa,aAAA,CAAAiG,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAkH,SAAA,CAAAlH,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAAsC;IAAA,EAAC;IACvEtH,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAA4C,MAAA,qBACF;IAAA5C,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAA9CD,EAAA,CAAAW,UAAA,mBAAA8G,6DAAA;MAAAzH,EAAA,CAAAa,aAAA,CAAAiG,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAoH,MAAA,CAAApH,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAAmC;IAAA,EAAC;IACvEtH,EAAA,CAAA4C,MAAA,iBACF;IAGN5C,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IArBKH,EAAA,CAAAI,SAAA,GAA6C;IAACJ,EAA9C,CAAA6B,UAAA,QAAAvB,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAM,MAAA,IAAA5F,GAAA,EAAA/B,EAAA,CAAAgC,aAAA,CAA6C,QAAA1B,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAO,IAAA,CAAqC;IAEjF5H,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAqD,iBAAA,CAAA/C,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAO,IAAA,CAAkC;IACb5H,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAA6C,kBAAA,WAAA7C,EAAA,CAAA6H,WAAA,OAAAvH,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAS,KAAA,MAA6C;IAC7C9H,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAqD,iBAAA,CAAA/C,MAAA,CAAA8G,eAAA,CAAAC,OAAA,CAAAU,KAAA,CAAmC;;;;;IAoBpE/H,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;AAoqBV,OAAM,MAAO6H,oBAAoB;EAoB/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,YAA0B,EAC1BC,WAAwB,EACxBC,eAAgC,EAChCC,YAA0B;IAL1B,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IAzBtB,KAAAvC,WAAW,GAAY,EAAE;IACzB,KAAAtF,iBAAiB,GAAG,CAAC;IACrB,KAAAwB,YAAY,GAAiB,IAAI;IAEjC,KAAAsE,SAAS,GAAG,IAAI;IAChB,KAAAD,QAAQ,GAAG,KAAK;IAChB,KAAAiC,QAAQ,GAAG,CAAC;IACZ,KAAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IAEtB,KAAAhD,WAAW,GAAG,EAAE;IAChB,KAAA2B,eAAe,GAAQ,IAAI;IAE3B;IACA,KAAAtF,gBAAgB,GAAqB,IAAI;IACzC,KAAAqB,mBAAmB,GAAG,KAAK;EAYxB;EAEHuF,QAAQA,CAAA;IACN,IAAI,CAACR,KAAK,CAACS,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,MAAME,MAAM,GAAGF,MAAM,CAAC,QAAQ,CAAC;MAC/B,MAAMG,UAAU,GAAGC,QAAQ,CAACJ,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;MAEtD,IAAIE,MAAM,EAAE;QACV,IAAI,CAACG,eAAe,CAACH,MAAM,EAAEC,UAAU,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC;;EAEnC;EAGAE,mBAAmBA,CAACC,KAAoB;IACtC,QAAQA,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACnE,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;MACjB,KAAK,GAAG;QACN,IAAI,CAAC7C,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACoC,UAAU,EAAE;QACjB;;EAEN;EAEAqE,eAAeA,CAACH,MAAc,EAAEW,UAAA,GAAqB,CAAC;IACpD,IAAI,CAAChD,SAAS,GAAG,IAAI;IAErB,IAAI,CAAC4B,YAAY,CAACqB,cAAc,CAACZ,MAAM,CAAC,CAACD,SAAS,CAAC;MACjDc,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC3D,WAAW,GAAG2D,QAAQ,CAACC,OAAO;QAEnC;QACA,IAAI,CAAC5D,WAAW,GAAG,IAAI,CAAC6D,uBAAuB,CAAC,IAAI,CAAC7D,WAAW,CAAC;QAEjE,IAAI,CAACtF,iBAAiB,GAAGoJ,IAAI,CAACC,GAAG,CAACP,UAAU,EAAE,IAAI,CAACxD,WAAW,CAACU,MAAM,GAAG,CAAC,CAAC;QAC1E,IAAI,CAACxE,YAAY,GAAG,IAAI,CAAC8D,WAAW,CAAC,IAAI,CAACtF,iBAAiB,CAAC;QAC5D,IAAI,CAACsJ,kBAAkB,EAAE;QACzB,IAAI,CAACxD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACyD,kBAAkB,EAAE;MAC3B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC1D,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC7B,UAAU,EAAE;MACnB;KACD,CAAC;EACJ;EAEAkF,uBAAuBA,CAACD,OAAgB;IACtC,OAAOA,OAAO,CAACQ,GAAG,CAAEC,KAAK,IAAI;MAC3B;MACA,IAAI,CAACA,KAAK,CAACC,KAAK,IAAI,CAACD,KAAK,CAACC,KAAK,CAACvI,GAAG,IAAI,IAAI,CAACwI,gBAAgB,CAACF,KAAK,CAACC,KAAK,CAACvI,GAAG,CAAC,EAAE;QAC9E;QACA,MAAMyI,WAAW,GAAGH,KAAK,CAAClI,OAAO,IAAIkI,KAAK,CAACrG,QAAQ,EAAEoG,GAAG,CAACK,CAAC,IAAIA,CAAC,CAACpD,OAAO,CAACO,IAAI,CAAC,CAAC8C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;QAC7F,MAAMC,WAAW,GAAG,IAAI,CAACC,qBAAqB,CAACJ,WAAW,CAAC;QAE3DH,KAAK,CAACC,KAAK,GAAG;UACZ7D,IAAI,EAAE,OAAO;UACb1E,GAAG,EAAE4I,WAAW,CAAC5I,GAAG;UACpB8I,SAAS,EAAEF,WAAW,CAACE,SAAS;UAChC9H,QAAQ,EAAE4H,WAAW,CAAC5H;SACvB;;MAGH;MACA,IAAIsH,KAAK,CAACC,KAAK,IAAID,KAAK,CAACC,KAAK,CAAC7D,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC8D,gBAAgB,CAACF,KAAK,CAACC,KAAK,CAACvI,GAAG,CAAC,EAAE;QACzFsI,KAAK,CAACC,KAAK,GAAG;UACZ,GAAGD,KAAK,CAACC,KAAK;UACdvI,GAAG,EAAE,IAAI,CAACwG,YAAY,CAACuC,eAAe,CAACT,KAAK,CAACC,KAAK,CAACvI,GAAG,EAAE,OAAO,CAAC;UAChE8I,SAAS,EAAE,IAAI,CAACtC,YAAY,CAACuC,eAAe,CAACT,KAAK,CAACC,KAAK,CAACO,SAAS,EAAE,OAAO;SAC5E;;MAGH,OAAOR,KAAK;IACd,CAAC,CAAC;EACJ;EAEQE,gBAAgBA,CAACxI,GAAW;IAClC,OAAOA,GAAG,CAACgJ,QAAQ,CAAC,WAAW,CAAC,IAAIhJ,GAAG,CAACgJ,QAAQ,CAAC,mBAAmB,CAAC,IAAIhJ,GAAG,CAACgJ,QAAQ,CAAC,WAAW,CAAC;EACpG;EAEQH,qBAAqBA,CAACI,IAAY;IACxC,MAAMC,SAAS,GAAGD,IAAI,CAACE,WAAW,EAAE;IAEpC,IAAID,SAAS,CAACF,QAAQ,CAAC,SAAS,CAAC,IAAIE,SAAS,CAACF,QAAQ,CAAC,OAAO,CAAC,EAAE;MAChE,OAAO,IAAI,CAACxC,YAAY,CAAC4C,cAAc,CAAC,SAAS,CAAC;;IAEpD,IAAIF,SAAS,CAACF,QAAQ,CAAC,UAAU,CAAC,IAAIE,SAAS,CAACF,QAAQ,CAAC,MAAM,CAAC,EAAE;MAChE,OAAO,IAAI,CAACxC,YAAY,CAAC4C,cAAc,CAAC,UAAU,CAAC;;IAErD,IAAIF,SAAS,CAACF,QAAQ,CAAC,UAAU,CAAC,IAAIE,SAAS,CAACF,QAAQ,CAAC,YAAY,CAAC,EAAE;MACtE,OAAO,IAAI,CAACxC,YAAY,CAAC4C,cAAc,CAAC,UAAU,CAAC;;IAErD,IAAIF,SAAS,CAACF,QAAQ,CAAC,QAAQ,CAAC,IAAIE,SAAS,CAACF,QAAQ,CAAC,SAAS,CAAC,EAAE;MACjE,OAAO,IAAI,CAACxC,YAAY,CAAC4C,cAAc,CAAC,OAAO,CAAC;;IAGlD,OAAO,IAAI,CAAC5C,YAAY,CAAC6C,oBAAoB,EAAE;EACjD;EAEApB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC9H,YAAY,EAAEoI,KAAK,EAAE;MAC5B,IAAI,CAACxI,gBAAgB,GAAG;QACtBuJ,EAAE,EAAE,IAAI,CAACnJ,YAAY,CAACoF,GAAG;QACzBb,IAAI,EAAE,IAAI,CAACvE,YAAY,CAACoI,KAAK,CAAC7D,IAAI;QAClC1E,GAAG,EAAE,IAAI,CAACwG,YAAY,CAACuC,eAAe,CAAC,IAAI,CAAC5I,YAAY,CAACoI,KAAK,CAACvI,GAAG,EAAE,OAAO,CAAC;QAC5EY,YAAY,EAAE,IAAI,CAACT,YAAY,CAACoI,KAAK,CAACO,SAAS;QAC/C5I,GAAG,EAAE,IAAI,CAACC,YAAY,CAACC,OAAO;QAC9BY,QAAQ,EAAE,IAAI,CAACb,YAAY,CAACoI,KAAK,CAACvH;OACnC;;EAEL;EAEAkH,kBAAkBA,CAAA;IAChB,IAAI,CAACf,YAAY,EAAE;IACnB,IAAI,CAACV,QAAQ,GAAG,CAAC;IAEjB,IAAI,IAAI,CAACtG,YAAY,EAAEoI,KAAK,CAAC7D,IAAI,KAAK,OAAO,EAAE;MAC7C;MACA;;IAGF,MAAM6E,UAAU,GAAG,EAAE,CAAC,CAAC;IACvB,MAAMC,SAAS,GAAID,UAAU,GAAG,IAAI,CAAC7C,aAAa,GAAI,GAAG;IAEzD,IAAI,CAAC+C,oBAAoB,GAAGzL,KAAK,CAAC,CAAC,EAAEuL,UAAU,CAAC,CAAC1C,SAAS,CAAC,MAAK;MAC9D,IAAI,CAAC,IAAI,CAACrC,QAAQ,EAAE;QAClB,IAAI,CAACiC,QAAQ,IAAI+C,SAAS;QAC1B,IAAI,IAAI,CAAC/C,QAAQ,IAAI,GAAG,EAAE;UACxB,IAAI,CAACjG,SAAS,EAAE;;;IAGtB,CAAC,CAAC;EACJ;EAEA2G,YAAYA,CAAA;IACV,IAAI,IAAI,CAACsC,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACC,WAAW,EAAE;MACvC,IAAI,CAACD,oBAAoB,GAAGE,SAAS;;EAEzC;EAEAnL,gBAAgBA,CAACoL,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACjL,iBAAiB,EAAE;MAClC,OAAO,GAAG;KACX,MAAM,IAAIiL,KAAK,KAAK,IAAI,CAACjL,iBAAiB,EAAE;MAC3C,OAAO,IAAI,CAAC8H,QAAQ;KACrB,MAAM;MACL,OAAO,CAAC;;EAEZ;EAEAjG,SAASA,CAAA;IACP,IAAI,IAAI,CAAC7B,iBAAiB,GAAG,IAAI,CAACsF,WAAW,CAACU,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAAChG,iBAAiB,EAAE;MACxB,IAAI,CAACwB,YAAY,GAAG,IAAI,CAAC8D,WAAW,CAAC,IAAI,CAACtF,iBAAiB,CAAC;MAC5D,IAAI,CAACsJ,kBAAkB,EAAE;MACzB,IAAI,CAACC,kBAAkB,EAAE;KAC1B,MAAM;MACL;MACA,IAAI,CAACtF,UAAU,EAAE;;EAErB;EAEAS,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC1E,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACwB,YAAY,GAAG,IAAI,CAAC8D,WAAW,CAAC,IAAI,CAACtF,iBAAiB,CAAC;MAC5D,IAAI,CAACsJ,kBAAkB,EAAE;MACzB,IAAI,CAACC,kBAAkB,EAAE;;EAE7B;EAEAhJ,UAAUA,CAAA;IACR,IAAI,CAACsF,QAAQ,GAAG,IAAI;IACpB,MAAMqF,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,EAAE;;EAExB;EAEA3K,WAAWA,CAAA;IACT,IAAI,CAACmF,QAAQ,GAAG,KAAK;IACrB,MAAMqF,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACI,IAAI,EAAE;;EAEvB;EAEA1H,YAAYA,CAACgF,KAAiB;IAC5B,MAAM2C,MAAM,GAAG3C,KAAK,CAAC2C,MAAqB;IAE1C;IACA,IAAIA,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,IAC/BD,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,IAC/BD,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,IAC9BD,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;MAC/B;;IAGF;IACA,IAAI,IAAI,CAAC3F,QAAQ,EAAE;MACjB,IAAI,CAACnF,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACH,UAAU,EAAE;;EAErB;EAEAM,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAACO,gBAAgB,EAAE2E,IAAI,KAAK,OAAO,EAAE;MAC3C,IAAI,CAACtD,mBAAmB,GAAG,IAAI;;EAEnC;EAEAzB,gBAAgBA,CAAC4H,KAAY;IAC3B,IAAI,CAACf,YAAY,CAAC7G,gBAAgB,CAAC4H,KAAK,EAAE,OAAO,CAAC;EACpD;EAEA7G,gBAAgBA,CAAC6G,KAAY;IAC3Ba,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEZ,KAAK,CAAC;IAC1C;IACA,IAAI,IAAI,CAACpH,YAAY,EAAE;MACrB,MAAMyI,WAAW,GAAG,IAAI,CAACpC,YAAY,CAAC6C,oBAAoB,EAAE;MAC5D,IAAI,CAAClJ,YAAY,CAACoI,KAAK,GAAG;QACxB7D,IAAI,EAAE,OAAO;QACb1E,GAAG,EAAE4I,WAAW,CAAC5I,GAAG;QACpB8I,SAAS,EAAEF,WAAW,CAACE,SAAS;QAChC9H,QAAQ,EAAE4H,WAAW,CAAC5H;OACvB;MACD,IAAI,CAACiH,kBAAkB,EAAE;KAC1B,MAAM;MACL;MACA,IAAI,CAACzH,SAAS,EAAE;;EAEpB;EAEAW,gBAAgBA,CAAA;IACd,MAAM0I,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChB,IAAIA,YAAY,CAACO,MAAM,EAAE;QACvBP,YAAY,CAACI,IAAI,EAAE;QACnB,IAAI,CAAC7I,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAAC/B,WAAW,EAAE;OACnB,MAAM;QACLwK,YAAY,CAACG,KAAK,EAAE;QACpB,IAAI,CAAC5I,mBAAmB,GAAG,KAAK;QAChC,IAAI,CAAClC,UAAU,EAAE;;;EAGvB;EAEAyC,kBAAkBA,CAAC0I,UAAe;IAChC,IAAI,CAAChF,eAAe,GAAGgF,UAAU;IACjC,IAAI,CAACnL,UAAU,EAAE;EACnB;EAEA8F,iBAAiBA,CAAA;IACf,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAChG,WAAW,EAAE;EACpB;EAEA+F,aAAaA,CAACkF,SAAiB;IAC7B,IAAI,IAAI,CAACjF,eAAe,EAAE;MACxB,IAAI,CAACkB,eAAe,CAACnB,aAAa,CAACkF,SAAS,CAAC,CAACzD,SAAS,CAAC;QACtDc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC4C,gBAAgB,CAAC,sBAAsB,CAAC;UAC7C,IAAI,CAACvF,iBAAiB,EAAE;QAC1B,CAAC;QACDmD,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;UACvC;UACA,IAAI,CAAC5B,eAAe,CAACiE,oBAAoB,CAAC,IAAI,CAACnF,eAAe,CAACC,OAAO,CAAC;UACvE,IAAI,CAACiF,gBAAgB,CAAC,sBAAsB,CAAC;UAC7C,IAAI,CAACvF,iBAAiB,EAAE;QAC1B;OACD,CAAC;;EAEN;EAEAS,SAASA,CAAC6E,SAAiB;IACzB,IAAI,IAAI,CAACjF,eAAe,EAAE;MACxB,IAAI,CAACiB,WAAW,CAACb,SAAS,CAAC6E,SAAS,EAAE,CAAC,EAAE,IAAI,CAACjF,eAAe,CAACoF,IAAI,EAAE,IAAI,CAACpF,eAAe,CAACqF,KAAK,CAAC,CAAC7D,SAAS,CAAC;QACxGc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC4C,gBAAgB,CAAC,kBAAkB,CAAC;UACzC,IAAI,CAACvF,iBAAiB,EAAE;QAC1B,CAAC;QACDmD,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;UACnC,IAAI,CAACoC,gBAAgB,CAAC,kBAAkB,CAAC;UACzC,IAAI,CAACvF,iBAAiB,EAAE;QAC1B;OACD,CAAC;;EAEN;EAEAW,MAAMA,CAAC2E,SAAiB;IACtB,IAAI,IAAI,CAACjF,eAAe,EAAE;MACxB,IAAI,CAACiB,WAAW,CAACb,SAAS,CAAC6E,SAAS,EAAE,CAAC,EAAE,IAAI,CAACjF,eAAe,CAACoF,IAAI,EAAE,IAAI,CAACpF,eAAe,CAACqF,KAAK,CAAC,CAAC7D,SAAS,CAAC;QACxGc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC4C,gBAAgB,CAAC,4BAA4B,CAAC;UACnD,IAAI,CAACvF,iBAAiB,EAAE;UACxB,IAAI,CAACoB,MAAM,CAACuE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDxC,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;UACtC,IAAI,CAACoC,gBAAgB,CAAC,gCAAgC,CAAC;UACvD,IAAI,CAACvF,iBAAiB,EAAE;UACxB,IAAI,CAACoB,MAAM,CAACuE,QAAQ,CAAC,CAAC,UAAU,EAAEL,SAAS,CAAC,CAAC;QAC/C;OACD,CAAC;;EAEN;EAEQC,gBAAgBA,CAACK,OAAe;IACtC;IACA,MAAMC,YAAY,GAAGf,QAAQ,CAACgB,aAAa,CAAC,KAAK,CAAC;IAClDD,YAAY,CAACE,WAAW,GAAGH,OAAO;IAClCC,YAAY,CAACG,KAAK,CAACC,OAAO,GAAG;;;;;;;;;;;;;KAa5B;IAEDnB,QAAQ,CAACoB,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;IAEvCO,UAAU,CAAC,MAAK;MACdP,YAAY,CAACQ,MAAM,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAjJ,WAAWA,CAAA;IACT,IAAI,IAAI,CAACsB,WAAW,CAACmB,IAAI,EAAE,EAAE;MAC3BuD,OAAO,CAACkD,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC5H,WAAW,CAAC;MAC9C,IAAI,CAACA,WAAW,GAAG,EAAE;;EAEzB;EAEAI,SAASA,CAAA;IACPsE,OAAO,CAACkD,GAAG,CAAC,YAAY,CAAC;EAC3B;EAEAtH,UAAUA,CAAA;IACRoE,OAAO,CAACkD,GAAG,CAAC,aAAa,CAAC;EAC5B;EAEA1I,UAAUA,CAAA;IACR,IAAI,CAACwD,MAAM,CAACuE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEA9K,UAAUA,CAAC0H,KAAiB;IAC1B,MAAMgE,IAAI,GAAIhE,KAAK,CAAC2C,MAAsB,CAACsB,qBAAqB,EAAE;IAClE,MAAMC,IAAI,GAAGlE,KAAK,CAACmE,OAAO,GAAGH,IAAI,CAACI,IAAI;IACtC,MAAMC,OAAO,GAAGL,IAAI,CAACM,KAAK,GAAG,CAAC;IAE9B,IAAIJ,IAAI,GAAGG,OAAO,EAAE;MAClB;MACA,IAAI,CAACvI,aAAa,EAAE;KACrB,MAAM;MACL;MACA,IAAI,CAAC7C,SAAS,EAAE;;EAEpB;EAEAO,cAAcA,CAAC+K,OAAe;IAC5B,MAAMC,OAAO,GAAGhE,IAAI,CAACiE,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGH,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA7H,UAAUA,CAAC8H,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACrD,MAAMC,KAAK,GAAG1E,IAAI,CAACiE,KAAK,CAACO,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIE,KAAK,GAAG,CAAC,EAAE,OAAO,KAAK;IAC3B,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,GAAG;IAClC,MAAMC,IAAI,GAAG3E,IAAI,CAACiE,KAAK,CAACS,KAAK,GAAG,EAAE,CAAC;IACnC,OAAO,GAAGC,IAAI,GAAG;EACnB;;;uBA3aWzG,oBAAoB,EAAAhI,EAAA,CAAA0O,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5O,EAAA,CAAA0O,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7O,EAAA,CAAA0O,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAA/O,EAAA,CAAA0O,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAjP,EAAA,CAAA0O,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAAnP,EAAA,CAAA0O,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApBrH,oBAAoB;MAAAsH,SAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAApBzP,EAAA,CAAAW,UAAA,qBAAAgP,gDAAAlO,MAAA;YAAA,OAAAiO,GAAA,CAAArG,mBAAA,CAAA5H,MAAA,CAA2B;UAAA,UAAAzB,EAAA,CAAA4P,iBAAA,CAAP;;;;;;;;;;UAtqB7B5P,EAhKA,CAAA8D,UAAA,IAAA+L,mCAAA,mBAA8E,IAAAC,mCAAA,kBAqIG,IAAAC,mCAAA,iBA2BhC;;;UAhKtB/P,EAAA,CAAA6B,UAAA,SAAA6N,GAAA,CAAAxN,YAAA,CAAkB;UAqIjBlC,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAA6B,UAAA,SAAA6N,GAAA,CAAAtI,eAAA,CAAqB;UA2BjBpH,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAA6B,UAAA,SAAA6N,GAAA,CAAAlJ,SAAA,CAAe;;;qBAlKvC3G,YAAY,EAAAmQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAErQ,WAAW,EAAAsQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}