{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, i as forceUpdate, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { j as debounceEvent, k as inheritAttributes, c as componentOnReady, r as raf } from './helpers-be245865.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { a as arrowBackSharp, b as closeCircle, d as closeSharp, s as searchOutline, e as searchSharp } from './index-f7dc70ba.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nconst searchbarIosCss = \".sc-ion-searchbar-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-ios-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:inherit}.searchbar-search-icon.sc-ion-searchbar-ios{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-ios{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-ios{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-ios::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-ios>div.sc-ion-searchbar-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-ios:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{display:block}.searchbar-disabled.sc-ion-searchbar-ios-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-ios-h{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.07);--border-radius:10px;--box-shadow:none;--cancel-button-color:var(--ion-color-primary, #3880ff);--clear-button-color:var(--ion-color-step-600, #666666);--color:var(--ion-text-color, #000);--icon-color:var(--ion-color-step-600, #666666);-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;min-height:60px;contain:content}.searchbar-input-container.sc-ion-searchbar-ios{min-height:36px}.searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:calc(50% - 60px);margin-inline-start:calc(50% - 60px);top:0;position:absolute;width:1.375rem;height:100%;contain:strict}@supports (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-ios{inset-inline-start:5px}}@supports not (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-ios{left:5px}[dir=rtl].sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,[dir=rtl] .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{left:unset;right:unset;right:5px}[dir=rtl].sc-ion-searchbar-ios .searchbar-search-icon.sc-ion-searchbar-ios{left:unset;right:unset;right:5px}@supports selector(:dir(rtl)){.searchbar-search-icon.sc-ion-searchbar-ios:dir(rtl){left:unset;right:unset;right:5px}}}.searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:6px;padding-bottom:6px;height:100%;font-size:1.0625rem;font-weight:400;contain:strict}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.75rem;padding-inline-start:1.75rem;-webkit-padding-end:1.75rem;padding-inline-end:1.75rem}.searchbar-clear-button.sc-ion-searchbar-ios{top:0;background-position:center;position:absolute;width:1.875rem;height:100%;border:0;background-color:transparent}@supports (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-ios{inset-inline-end:0}}@supports not (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-ios{right:0}[dir=rtl].sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,[dir=rtl] .sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{left:unset;right:unset;left:0}[dir=rtl].sc-ion-searchbar-ios .searchbar-clear-button.sc-ion-searchbar-ios{left:unset;right:unset;left:0}@supports selector(:dir(rtl)){.searchbar-clear-button.sc-ion-searchbar-ios:dir(rtl){left:unset;right:unset;left:0}}}.searchbar-clear-icon.sc-ion-searchbar-ios{width:1.125rem;height:100%}.searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0;background-color:transparent;font-size:16px}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:0;margin-inline-start:0}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.875rem;padding-inline-start:1.875rem}.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{display:block}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-transition:all 300ms ease;transition:all 300ms ease}.searchbar-animated.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{opacity:1;pointer-events:auto}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-margin-end:-100%;margin-inline-end:-100%;-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);-webkit-transition:all 300ms ease;transition:all 300ms ease;opacity:0;pointer-events:none}.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-transition-duration:0ms;transition-duration:0ms}.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{color:var(--ion-color-base)}@media (any-hover: hover){.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios:hover{color:var(--ion-color-tint)}}ion-toolbar.sc-ion-searchbar-ios-h,ion-toolbar .sc-ion-searchbar-ios-h{padding-top:1px;padding-bottom:15px;min-height:52px}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color),ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color){color:inherit}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios{color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios{background:rgba(var(--ion-color-contrast-rgb), 0.07);color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}\";\nconst IonSearchbarIosStyle0 = searchbarIosCss;\nconst searchbarMdCss = \".sc-ion-searchbar-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-md-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{color:inherit}.searchbar-search-icon.sc-ion-searchbar-md{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-md{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-md{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-md::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-md>div.sc-ion-searchbar-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-md:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{display:block}.searchbar-disabled.sc-ion-searchbar-md-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-md-h{--background:var(--ion-background-color, #fff);--border-radius:2px;--box-shadow:0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--cancel-button-color:var(--ion-color-step-900, #1a1a1a);--clear-button-color:initial;--color:var(--ion-color-step-850, #262626);--icon-color:var(--ion-color-step-600, #666666);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;background:inherit}.searchbar-search-icon.sc-ion-searchbar-md{top:11px;width:1.3125rem;height:1.3125rem}@supports (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-md{inset-inline-start:16px}}@supports not (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-md{left:16px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{left:unset;right:unset;right:16px}[dir=rtl].sc-ion-searchbar-md .searchbar-search-icon.sc-ion-searchbar-md{left:unset;right:unset;right:16px}@supports selector(:dir(rtl)){.searchbar-search-icon.sc-ion-searchbar-md:dir(rtl){left:unset;right:unset;right:16px}}}.searchbar-cancel-button.sc-ion-searchbar-md{top:0;background-color:transparent;font-size:1.5em}@supports (inset-inline-start: 0){.searchbar-cancel-button.sc-ion-searchbar-md{inset-inline-start:9px}}@supports not (inset-inline-start: 0){.searchbar-cancel-button.sc-ion-searchbar-md{left:9px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{left:unset;right:unset;right:9px}[dir=rtl].sc-ion-searchbar-md .searchbar-cancel-button.sc-ion-searchbar-md{left:unset;right:unset;right:9px}@supports selector(:dir(rtl)){.searchbar-cancel-button.sc-ion-searchbar-md:dir(rtl){left:unset;right:unset;right:9px}}}.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-cancel-button.sc-ion-searchbar-md{position:absolute}.searchbar-search-icon.ion-activated.sc-ion-searchbar-md,.searchbar-cancel-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-input.sc-ion-searchbar-md{-webkit-padding-start:3.4375rem;padding-inline-start:3.4375rem;-webkit-padding-end:3.4375rem;padding-inline-end:3.4375rem;padding-top:0.375rem;padding-bottom:0.375rem;background-position:left 8px center;height:auto;font-size:1rem;font-weight:400;line-height:30px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}[dir=rtl].sc-ion-searchbar-md .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}@supports selector(:dir(rtl)){.searchbar-input.sc-ion-searchbar-md:dir(rtl){background-position:right 8px center}}.searchbar-clear-button.sc-ion-searchbar-md{top:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;position:absolute;height:100%;border:0;background-color:transparent}@supports (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-md{inset-inline-end:13px}}@supports not (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-md{right:13px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{left:unset;right:unset;left:13px}[dir=rtl].sc-ion-searchbar-md .searchbar-clear-button.sc-ion-searchbar-md{left:unset;right:unset;left:13px}@supports selector(:dir(rtl)){.searchbar-clear-button.sc-ion-searchbar-md:dir(rtl){left:unset;right:unset;left:13px}}}.searchbar-clear-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-clear-icon.sc-ion-searchbar-md{width:1.375rem;height:100%}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md{display:none}ion-toolbar.sc-ion-searchbar-md-h,ion-toolbar .sc-ion-searchbar-md-h{-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:3px;padding-bottom:3px}\";\nconst IonSearchbarMdStyle0 = searchbarMdCss;\nconst Searchbar = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionClear = createEvent(this, \"ionClear\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.isCancelVisible = false;\n    this.shouldAlignLeft = true;\n    this.inputId = `ion-searchbar-${searchbarIds++}`;\n    this.inheritedAttributes = {};\n    /**\n     * Clears the input field and triggers the control change.\n     */\n    this.onClearInput = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (shouldFocus) {\n        _this.ionClear.emit();\n        return new Promise(resolve => {\n          // setTimeout() fixes https://github.com/ionic-team/ionic/issues/7527\n          // wait for 4 frames\n          setTimeout(() => {\n            const value = _this.getValue();\n            if (value !== '') {\n              _this.value = '';\n              _this.emitInputChange();\n              /**\n               * When tapping clear button\n               * ensure input is focused after\n               * clearing input so users\n               * can quickly start typing.\n               */\n              if (shouldFocus && !_this.focused) {\n                _this.setFocus();\n                /**\n                 * The setFocus call above will clear focusedValue,\n                 * but ionChange will never have gotten a chance to\n                 * fire. Manually revert focusedValue so onBlur can\n                 * compare against what was in the box before the clear.\n                 */\n                _this.focusedValue = value;\n              }\n            }\n            resolve();\n          }, 16 * 4);\n        });\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    /**\n     * Clears the input field and tells the input to blur since\n     * the clearInput function doesn't want the input to blur\n     * then calls the custom cancel function if the user passed one in.\n     */\n    this.onCancelSearchbar = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (ev) {\n        if (ev) {\n          ev.preventDefault();\n          ev.stopPropagation();\n        }\n        _this.ionCancel.emit();\n        // get cached values before clearing the input\n        const value = _this.getValue();\n        const focused = _this.focused;\n        yield _this.onClearInput();\n        /**\n         * If there used to be something in the box, and we weren't focused\n         * beforehand (meaning no blur fired that would already handle this),\n         * manually fire ionChange.\n         */\n        if (value && !focused) {\n          _this.emitValueChange(ev);\n        }\n        if (_this.nativeInput) {\n          _this.nativeInput.blur();\n        }\n      });\n      return function (_x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    /**\n     * Update the Searchbar input value when the input changes\n     */\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value;\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    /**\n     * Sets the Searchbar to not focused and checks if it should align left\n     * based on whether there is a value in the searchbar or not.\n     */\n    this.onBlur = ev => {\n      this.focused = false;\n      this.ionBlur.emit();\n      this.positionElements();\n      if (this.focusedValue !== this.value) {\n        this.emitValueChange(ev);\n      }\n      this.focusedValue = undefined;\n    };\n    /**\n     * Sets the Searchbar to focused and active on input focus.\n     */\n    this.onFocus = () => {\n      this.focused = true;\n      this.focusedValue = this.value;\n      this.ionFocus.emit();\n      this.positionElements();\n    };\n    this.focused = false;\n    this.noAnimate = true;\n    this.color = undefined;\n    this.animated = false;\n    this.autocapitalize = 'default';\n    this.autocomplete = 'off';\n    this.autocorrect = 'off';\n    this.cancelButtonIcon = config.get('backButtonIcon', arrowBackSharp);\n    this.cancelButtonText = 'Cancel';\n    this.clearIcon = undefined;\n    this.debounce = undefined;\n    this.disabled = false;\n    this.inputmode = undefined;\n    this.enterkeyhint = undefined;\n    this.maxlength = undefined;\n    this.minlength = undefined;\n    this.name = this.inputId;\n    this.placeholder = 'Search';\n    this.searchIcon = undefined;\n    this.showCancelButton = 'never';\n    this.showClearButton = 'always';\n    this.spellcheck = false;\n    this.type = 'search';\n    this.value = '';\n  }\n  /**\n   * lang and dir are globally enumerated attributes.\n   * As a result, creating these as properties\n   * can have unintended side effects. Instead, we\n   * listen for attribute changes and inherit them\n   * to the inner `<input>` element.\n   */\n  onLangChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      lang: newValue\n    });\n    forceUpdate(this);\n  }\n  onDirChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      dir: newValue\n    });\n    forceUpdate(this);\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  valueChanged() {\n    const inputEl = this.nativeInput;\n    const value = this.getValue();\n    if (inputEl && inputEl.value !== value) {\n      inputEl.value = value;\n    }\n  }\n  showCancelButtonChanged() {\n    requestAnimationFrame(() => {\n      this.positionElements();\n      forceUpdate(this);\n    });\n  }\n  connectedCallback() {\n    this.emitStyle();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['lang', 'dir']));\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    this.positionElements();\n    this.debounceChanged();\n    setTimeout(() => {\n      this.noAnimate = false;\n    }, 300);\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      searchbar: true\n    });\n  }\n  /**\n   * Sets focus on the native `input` in `ion-searchbar`. Use this method instead of the global\n   * `input.focus()`.\n   *\n   * Developers who wish to focus an input when a page enters\n   * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n   *\n   * Developers who wish to focus an input when an overlay is presented\n   * should call `setFocus` after `didPresent` has resolved.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  setFocus() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.nativeInput) {\n        _this2.nativeInput.focus();\n      }\n    })();\n  }\n  /**\n   * Returns the native `<input>` element used under the hood.\n   */\n  getInputElement() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n       * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n       */\n      if (!_this3.nativeInput) {\n        yield new Promise(resolve => componentOnReady(_this3.el, resolve));\n      }\n      return Promise.resolve(_this3.nativeInput);\n    })();\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    this.ionInput.emit({\n      value,\n      event\n    });\n  }\n  /**\n   * Positions the input search icon, placeholder, and the cancel button\n   * based on the input value and if it is focused. (ios only)\n   */\n  positionElements() {\n    const value = this.getValue();\n    const prevAlignLeft = this.shouldAlignLeft;\n    const mode = getIonMode(this);\n    const shouldAlignLeft = !this.animated || value.trim() !== '' || !!this.focused;\n    this.shouldAlignLeft = shouldAlignLeft;\n    if (mode !== 'ios') {\n      return;\n    }\n    if (prevAlignLeft !== shouldAlignLeft) {\n      this.positionPlaceholder();\n    }\n    if (this.animated) {\n      this.positionCancelButton();\n    }\n  }\n  /**\n   * Positions the input placeholder\n   */\n  positionPlaceholder() {\n    const inputEl = this.nativeInput;\n    if (!inputEl) {\n      return;\n    }\n    const rtl = isRTL(this.el);\n    const iconEl = (this.el.shadowRoot || this.el).querySelector('.searchbar-search-icon');\n    if (this.shouldAlignLeft) {\n      inputEl.removeAttribute('style');\n      iconEl.removeAttribute('style');\n    } else {\n      // Create a dummy span to get the placeholder width\n      const doc = document;\n      const tempSpan = doc.createElement('span');\n      tempSpan.innerText = this.placeholder || '';\n      doc.body.appendChild(tempSpan);\n      // Get the width of the span then remove it\n      raf(() => {\n        const textWidth = tempSpan.offsetWidth;\n        tempSpan.remove();\n        // Calculate the input padding\n        const inputLeft = 'calc(50% - ' + textWidth / 2 + 'px)';\n        // Calculate the icon margin\n        /**\n         * We take the icon width to account\n         * for any text scales applied to the icon\n         * such as Dynamic Type on iOS as well as 8px\n         * of padding.\n         */\n        const iconLeft = 'calc(50% - ' + (textWidth / 2 + iconEl.clientWidth + 8) + 'px)';\n        // Set the input padding start and icon margin start\n        if (rtl) {\n          inputEl.style.paddingRight = inputLeft;\n          iconEl.style.marginRight = iconLeft;\n        } else {\n          inputEl.style.paddingLeft = inputLeft;\n          iconEl.style.marginLeft = iconLeft;\n        }\n      });\n    }\n  }\n  /**\n   * Show the iOS Cancel button on focus, hide it offscreen otherwise\n   */\n  positionCancelButton() {\n    const rtl = isRTL(this.el);\n    const cancelButton = (this.el.shadowRoot || this.el).querySelector('.searchbar-cancel-button');\n    const shouldShowCancel = this.shouldShowCancelButton();\n    if (cancelButton !== null && shouldShowCancel !== this.isCancelVisible) {\n      const cancelStyle = cancelButton.style;\n      this.isCancelVisible = shouldShowCancel;\n      if (shouldShowCancel) {\n        if (rtl) {\n          cancelStyle.marginLeft = '0';\n        } else {\n          cancelStyle.marginRight = '0';\n        }\n      } else {\n        const offset = cancelButton.offsetWidth;\n        if (offset > 0) {\n          if (rtl) {\n            cancelStyle.marginLeft = -offset + 'px';\n          } else {\n            cancelStyle.marginRight = -offset + 'px';\n          }\n        }\n      }\n    }\n  }\n  getValue() {\n    return this.value || '';\n  }\n  hasValue() {\n    return this.getValue() !== '';\n  }\n  /**\n   * Determines whether or not the cancel button should be visible onscreen.\n   * Cancel button should be shown if one of two conditions applies:\n   * 1. `showCancelButton` is set to `always`.\n   * 2. `showCancelButton` is set to `focus`, and the searchbar has been focused.\n   */\n  shouldShowCancelButton() {\n    if (this.showCancelButton === 'never' || this.showCancelButton === 'focus' && !this.focused) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Determines whether or not the clear button should be visible onscreen.\n   * Clear button should be shown if one of two conditions applies:\n   * 1. `showClearButton` is set to `always`.\n   * 2. `showClearButton` is set to `focus`, and the searchbar has been focused.\n   */\n  shouldShowClearButton() {\n    if (this.showClearButton === 'never' || this.showClearButton === 'focus' && !this.focused) {\n      return false;\n    }\n    return true;\n  }\n  render() {\n    const {\n      cancelButtonText,\n      autocapitalize\n    } = this;\n    const animated = this.animated && config.getBoolean('animated', true);\n    const mode = getIonMode(this);\n    const clearIcon = this.clearIcon || (mode === 'ios' ? closeCircle : closeSharp);\n    const searchIcon = this.searchIcon || (mode === 'ios' ? searchOutline : searchSharp);\n    const shouldShowCancelButton = this.shouldShowCancelButton();\n    const cancelButton = this.showCancelButton !== 'never' && h(\"button\", {\n      key: '9c7b4d2e86d9bcd12e57c9a96723d3da598a3773',\n      \"aria-label\": cancelButtonText,\n      \"aria-hidden\": shouldShowCancelButton ? undefined : 'true',\n      type: \"button\",\n      tabIndex: mode === 'ios' && !shouldShowCancelButton ? -1 : undefined,\n      onMouseDown: this.onCancelSearchbar,\n      onTouchStart: this.onCancelSearchbar,\n      class: \"searchbar-cancel-button\"\n    }, h(\"div\", {\n      key: '1c25268a776134cccd29eb752898cb8ac0eed30f',\n      \"aria-hidden\": \"true\"\n    }, mode === 'md' ? h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: this.cancelButtonIcon,\n      lazy: false\n    }) : cancelButtonText));\n    return h(Host, {\n      key: 'feef9fc7e405656e134a76dc037aaaa1a4ce36b4',\n      role: \"search\",\n      \"aria-disabled\": this.disabled ? 'true' : null,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'searchbar-animated': animated,\n        'searchbar-disabled': this.disabled,\n        'searchbar-no-animate': animated && this.noAnimate,\n        'searchbar-has-value': this.hasValue(),\n        'searchbar-left-aligned': this.shouldAlignLeft,\n        'searchbar-has-focus': this.focused,\n        'searchbar-should-show-clear': this.shouldShowClearButton(),\n        'searchbar-should-show-cancel': this.shouldShowCancelButton()\n      })\n    }, h(\"div\", {\n      key: '92e3925dc0de468e5665705902153837105dfa57',\n      class: \"searchbar-input-container\"\n    }, h(\"input\", Object.assign({\n      key: 'fb74faf81b347a62338ccdac981525df1c52b322',\n      \"aria-label\": \"search text\",\n      disabled: this.disabled,\n      ref: el => this.nativeInput = el,\n      class: \"searchbar-input\",\n      inputMode: this.inputmode,\n      enterKeyHint: this.enterkeyhint,\n      name: this.name,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      placeholder: this.placeholder,\n      type: this.type,\n      value: this.getValue(),\n      autoCapitalize: autocapitalize === 'default' ? undefined : autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      spellcheck: this.spellcheck\n    }, this.inheritedAttributes)), mode === 'md' && cancelButton, h(\"ion-icon\", {\n      key: 'd58c3636dac1d2e4135989f4c07dc95c51492e60',\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: searchIcon,\n      lazy: false,\n      class: \"searchbar-search-icon\"\n    }), h(\"button\", {\n      key: '1cece7c63ca5ca4b8799e15ee6d2bac100ef0d5e',\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      \"no-blur\": true,\n      class: \"searchbar-clear-button\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: () => this.onClearInput(true)\n    }, h(\"ion-icon\", {\n      key: 'fe3c2b9cac29002f69e95a89b554c7504e2df050',\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: clearIcon,\n      lazy: false,\n      class: \"searchbar-clear-icon\"\n    }))), mode === 'ios' && cancelButton);\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"lang\": [\"onLangChanged\"],\n      \"dir\": [\"onDirChanged\"],\n      \"debounce\": [\"debounceChanged\"],\n      \"value\": [\"valueChanged\"],\n      \"showCancelButton\": [\"showCancelButtonChanged\"]\n    };\n  }\n};\nlet searchbarIds = 0;\nSearchbar.style = {\n  ios: IonSearchbarIosStyle0,\n  md: IonSearchbarMdStyle0\n};\nexport { Searchbar as ion_searchbar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}