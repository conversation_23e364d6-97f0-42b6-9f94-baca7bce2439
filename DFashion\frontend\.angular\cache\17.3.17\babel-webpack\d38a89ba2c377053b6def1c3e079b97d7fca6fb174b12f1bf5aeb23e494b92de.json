{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, of, forkJoin } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ShopDataService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = environment.apiUrl;\n    // Cache subjects for performance\n    this.featuredBrandsSubject = new BehaviorSubject([]);\n    this.trendingProductsSubject = new BehaviorSubject([]);\n    this.newArrivalsSubject = new BehaviorSubject([]);\n    this.quickLinksSubject = new BehaviorSubject([]);\n    this.shopStatsSubject = new BehaviorSubject(null);\n    // Public observables\n    this.featuredBrands$ = this.featuredBrandsSubject.asObservable();\n    this.trendingProducts$ = this.trendingProductsSubject.asObservable();\n    this.newArrivals$ = this.newArrivalsSubject.asObservable();\n    this.quickLinks$ = this.quickLinksSubject.asObservable();\n    this.shopStats$ = this.shopStatsSubject.asObservable();\n    this.initializeQuickLinks();\n  }\n  /**\n   * Load all shop data in parallel\n   */\n  loadAllShopData() {\n    return forkJoin({\n      brands: this.loadFeaturedBrands(),\n      trending: this.loadTrendingProducts(),\n      newArrivals: this.loadNewArrivals(),\n      stats: this.loadShopStats()\n    });\n  }\n  /**\n   * Load featured brands\n   */\n  loadFeaturedBrands() {\n    return this.http.get(`${this.API_URL}/brands/featured`).pipe(map(response => {\n      const brands = response.success ? response.data : this.getMockFeaturedBrands();\n      this.featuredBrandsSubject.next(brands);\n      return brands;\n    }), catchError(() => {\n      const mockBrands = this.getMockFeaturedBrands();\n      this.featuredBrandsSubject.next(mockBrands);\n      return of(mockBrands);\n    }));\n  }\n  /**\n   * Load trending products\n   */\n  loadTrendingProducts(limit = 12) {\n    const params = new HttpParams().set('limit', limit.toString());\n    return this.http.get(`${this.API_URL}/products/trending`, {\n      params\n    }).pipe(map(response => {\n      const products = response.success ? response.data : this.getMockTrendingProducts();\n      this.trendingProductsSubject.next(products);\n      return products;\n    }), catchError(() => {\n      const mockProducts = this.getMockTrendingProducts();\n      this.trendingProductsSubject.next(mockProducts);\n      return of(mockProducts);\n    }));\n  }\n  /**\n   * Load new arrivals\n   */\n  loadNewArrivals(limit = 12) {\n    const params = new HttpParams().set('limit', limit.toString()).set('sortBy', 'newest');\n    return this.http.get(`${this.API_URL}/products`, {\n      params\n    }).pipe(map(response => {\n      const products = response.success ? response.data : this.getMockNewArrivals();\n      this.newArrivalsSubject.next(products);\n      return products;\n    }), catchError(() => {\n      const mockProducts = this.getMockNewArrivals();\n      this.newArrivalsSubject.next(mockProducts);\n      return of(mockProducts);\n    }));\n  }\n  /**\n   * Load shop statistics\n   */\n  loadShopStats() {\n    return this.http.get(`${this.API_URL}/shop/stats`).pipe(map(response => {\n      const stats = response.success ? response.stats : this.getMockShopStats();\n      this.shopStatsSubject.next(stats);\n      return stats;\n    }), catchError(() => {\n      const mockStats = this.getMockShopStats();\n      this.shopStatsSubject.next(mockStats);\n      return of(mockStats);\n    }));\n  }\n  /**\n   * Get products by brand\n   */\n  getProductsByBrand(brandSlug, limit = 20) {\n    const params = new HttpParams().set('brand', brandSlug).set('limit', limit.toString());\n    return this.http.get(`${this.API_URL}/products`, {\n      params\n    }).pipe(map(response => response.success ? response.products : []), catchError(() => of([])));\n  }\n  /**\n   * Get products by category\n   */\n  getProductsByCategory(category, subcategory, limit = 20) {\n    let params = new HttpParams().set('category', category).set('limit', limit.toString());\n    if (subcategory) {\n      params = params.set('subcategory', subcategory);\n    }\n    return this.http.get(`${this.API_URL}/products`, {\n      params\n    }).pipe(map(response => response.success ? response.products : []), catchError(() => of([])));\n  }\n  /**\n   * Search products\n   */\n  searchProducts(query, filters) {\n    let params = new HttpParams().set('q', query);\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        if (filters[key]) {\n          params = params.set(key, filters[key]);\n        }\n      });\n    }\n    return this.http.get(`${this.API_URL}/search/products`, {\n      params\n    }).pipe(map(response => response.success ? response : {\n      products: [],\n      totalCount: 0\n    }), catchError(() => of({\n      products: [],\n      totalCount: 0\n    })));\n  }\n  /**\n   * Initialize quick links\n   */\n  initializeQuickLinks() {\n    const quickLinks = [{\n      id: 'men-clothing',\n      title: 'Men\\'s Clothing',\n      description: 'Shirts, T-shirts, Jeans & More',\n      icon: 'fas fa-tshirt',\n      route: '/shop/category/men',\n      color: '#3498db',\n      category: 'men',\n      featured: true,\n      order: 1\n    }, {\n      id: 'women-clothing',\n      title: 'Women\\'s Clothing',\n      description: 'Dresses, Tops, Kurtis & More',\n      icon: 'fas fa-female',\n      route: '/shop/category/women',\n      color: '#e91e63',\n      category: 'women',\n      featured: true,\n      order: 2\n    }, {\n      id: 'footwear',\n      title: 'Footwear',\n      description: 'Shoes, Sandals, Sneakers',\n      icon: 'fas fa-shoe-prints',\n      route: '/shop/category/footwear',\n      color: '#ff9800',\n      category: 'footwear',\n      featured: true,\n      order: 3\n    }, {\n      id: 'accessories',\n      title: 'Accessories',\n      description: 'Bags, Watches, Jewelry',\n      icon: 'fas fa-gem',\n      route: '/shop/category/accessories',\n      color: '#9c27b0',\n      category: 'accessories',\n      featured: true,\n      order: 4\n    }, {\n      id: 'ethnic-wear',\n      title: 'Ethnic Wear',\n      description: 'Kurtas, Sarees, Traditional',\n      icon: 'fas fa-star-and-crescent',\n      route: '/shop/category/ethnic',\n      color: '#ff5722',\n      category: 'ethnic',\n      featured: true,\n      order: 5\n    }, {\n      id: 'sports-fitness',\n      title: 'Sports & Fitness',\n      description: 'Activewear, Gym Gear',\n      icon: 'fas fa-dumbbell',\n      route: '/shop/category/sports',\n      color: '#4caf50',\n      category: 'sports',\n      featured: true,\n      order: 6\n    }, {\n      id: 'sale',\n      title: 'Sale & Offers',\n      description: 'Up to 70% Off',\n      icon: 'fas fa-tags',\n      route: '/shop/sale',\n      color: '#f44336',\n      featured: true,\n      order: 7\n    }, {\n      id: 'new-arrivals',\n      title: 'New Arrivals',\n      description: 'Latest Fashion Trends',\n      icon: 'fas fa-sparkles',\n      route: '/shop/new-arrivals',\n      color: '#00bcd4',\n      featured: true,\n      order: 8\n    }];\n    this.quickLinksSubject.next(quickLinks);\n  }\n  /**\n   * Get current cached data\n   */\n  getCurrentFeaturedBrands() {\n    return this.featuredBrandsSubject.value;\n  }\n  getCurrentTrendingProducts() {\n    return this.trendingProductsSubject.value;\n  }\n  getCurrentNewArrivals() {\n    return this.newArrivalsSubject.value;\n  }\n  getCurrentQuickLinks() {\n    return this.quickLinksSubject.value;\n  }\n  getCurrentShopStats() {\n    return this.shopStatsSubject.value;\n  }\n  /**\n   * Mock data methods for fallback\n   */\n  getMockFeaturedBrands() {\n    return [{\n      _id: '1',\n      name: 'Nike',\n      logo: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200',\n      description: 'Just Do It',\n      slug: 'nike',\n      isVerified: true,\n      productCount: 245,\n      rating: 4.8,\n      establishedYear: 1964,\n      website: 'https://nike.com',\n      categories: ['footwear', 'sportswear'],\n      featured: true,\n      trending: true\n    }, {\n      _id: '2',\n      name: 'Adidas',\n      logo: 'https://images.unsplash.com/photo-1556906781-9a412961c28c?w=200',\n      description: 'Impossible is Nothing',\n      slug: 'adidas',\n      isVerified: true,\n      productCount: 189,\n      rating: 4.7,\n      establishedYear: 1949,\n      categories: ['footwear', 'sportswear'],\n      featured: true,\n      trending: true\n    }, {\n      _id: '3',\n      name: 'Zara',\n      logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200',\n      description: 'Fashion Forward',\n      slug: 'zara',\n      isVerified: true,\n      productCount: 567,\n      rating: 4.6,\n      categories: ['men', 'women', 'accessories'],\n      featured: true,\n      trending: false\n    }];\n  }\n  getMockTrendingProducts() {\n    return [{\n      _id: '1',\n      name: 'Premium Cotton T-Shirt',\n      description: 'Comfortable cotton t-shirt perfect for daily wear',\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        alt: 'Cotton T-Shirt',\n        isPrimary: true\n      }],\n      pricing: {\n        mrp: 999,\n        sellingPrice: 699,\n        discountPercentage: 30\n      },\n      brand: 'Nike',\n      category: 'men',\n      subcategory: 'tshirts',\n      rating: {\n        average: 4.5,\n        count: 128\n      },\n      analytics: {\n        views: 1250,\n        likes: 89,\n        shares: 23,\n        purchases: 67\n      },\n      isTrending: true,\n      trendingScore: 95,\n      availability: {\n        status: 'in-stock',\n        totalStock: 45\n      }\n    }];\n  }\n  getMockNewArrivals() {\n    return [{\n      _id: '1',\n      name: 'Designer Kurta Set',\n      description: 'Elegant kurta set for special occasions',\n      images: [{\n        url: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=400',\n        alt: 'Kurta Set',\n        isPrimary: true\n      }],\n      pricing: {\n        mrp: 2999,\n        sellingPrice: 2299,\n        discountPercentage: 23\n      },\n      brand: 'Ethnic Wear Co',\n      category: 'men',\n      subcategory: 'kurtas',\n      rating: {\n        average: 4.7,\n        count: 45\n      },\n      createdAt: new Date().toISOString(),\n      isNew: true,\n      availability: {\n        status: 'in-stock',\n        totalStock: 23\n      }\n    }];\n  }\n  getMockShopStats() {\n    return {\n      totalProducts: 12547,\n      totalBrands: 456,\n      totalCategories: 28,\n      totalUsers: 89234,\n      newArrivalsCount: 234,\n      trendingCount: 156\n    };\n  }\n  static {\n    this.ɵfac = function ShopDataService_Factory(t) {\n      return new (t || ShopDataService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ShopDataService,\n      factory: ShopDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "of", "fork<PERSON><PERSON>n", "map", "catchError", "environment", "ShopDataService", "constructor", "http", "API_URL", "apiUrl", "featuredBrandsSubject", "trendingProductsSubject", "newArrivalsSubject", "quickLinksSubject", "shopStatsSubject", "featuredBrands$", "asObservable", "trendingProducts$", "newArrivals$", "quickLinks$", "shopStats$", "initializeQuickLinks", "loadAllShopData", "brands", "loadFeaturedBrands", "trending", "loadTrendingProducts", "newArrivals", "loadNewArrivals", "stats", "loadShopStats", "get", "pipe", "response", "success", "data", "getMockFeaturedBrands", "next", "mockBrands", "limit", "params", "set", "toString", "products", "getMockTrendingProducts", "mockProducts", "getMockNewArrivals", "getMockShopStats", "mockStats", "getProductsByBrand", "brandSlug", "getProductsByCategory", "category", "subcategory", "searchProducts", "query", "filters", "Object", "keys", "for<PERSON>ach", "key", "totalCount", "quickLinks", "id", "title", "description", "icon", "route", "color", "featured", "order", "getCurrentFeaturedBrands", "value", "getCurrentTrendingProducts", "getCurrentNewArrivals", "getCurrentQuickLinks", "getCurrentShopStats", "_id", "name", "logo", "slug", "isVerified", "productCount", "rating", "establishedYear", "website", "categories", "images", "url", "alt", "isPrimary", "pricing", "mrp", "sellingPrice", "discountPercentage", "brand", "average", "count", "analytics", "views", "likes", "shares", "purchases", "isTrending", "trendingScore", "availability", "status", "totalStock", "createdAt", "Date", "toISOString", "isNew", "totalProducts", "totalBrands", "totalCategories", "totalUsers", "newArrivalsCount", "trendingCount", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\shop-data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject, of, forkJoin } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\n\nexport interface Brand {\n  _id: string;\n  name: string;\n  logo: string;\n  description: string;\n  slug: string;\n  isVerified: boolean;\n  productCount: number;\n  rating: number;\n  establishedYear?: number;\n  website?: string;\n  categories: string[];\n  featured: boolean;\n  trending: boolean;\n}\n\nexport interface TrendingProduct {\n  _id: string;\n  name: string;\n  description: string;\n  images: { url: string; alt: string; isPrimary: boolean }[];\n  pricing: {\n    mrp: number;\n    sellingPrice: number;\n    discountPercentage: number;\n  };\n  brand: string;\n  category: string;\n  subcategory: string;\n  rating: { average: number; count: number };\n  analytics: { views: number; likes: number; shares: number; purchases: number };\n  isTrending: boolean;\n  trendingScore: number;\n  availability: { status: string; totalStock: number };\n}\n\nexport interface NewArrival {\n  _id: string;\n  name: string;\n  description: string;\n  images: { url: string; alt: string; isPrimary: boolean }[];\n  pricing: {\n    mrp: number;\n    sellingPrice: number;\n    discountPercentage: number;\n  };\n  brand: string;\n  category: string;\n  subcategory: string;\n  rating: { average: number; count: number };\n  createdAt: string;\n  isNew: boolean;\n  availability: { status: string; totalStock: number };\n}\n\nexport interface QuickLink {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  route: string;\n  color: string;\n  category?: string;\n  subcategory?: string;\n  featured: boolean;\n  order: number;\n}\n\nexport interface ShopStats {\n  totalProducts: number;\n  totalBrands: number;\n  totalCategories: number;\n  totalUsers: number;\n  newArrivalsCount: number;\n  trendingCount: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ShopDataService {\n  private readonly API_URL = environment.apiUrl;\n  \n  // Cache subjects for performance\n  private featuredBrandsSubject = new BehaviorSubject<Brand[]>([]);\n  private trendingProductsSubject = new BehaviorSubject<TrendingProduct[]>([]);\n  private newArrivalsSubject = new BehaviorSubject<NewArrival[]>([]);\n  private quickLinksSubject = new BehaviorSubject<QuickLink[]>([]);\n  private shopStatsSubject = new BehaviorSubject<ShopStats | null>(null);\n  \n  // Public observables\n  public featuredBrands$ = this.featuredBrandsSubject.asObservable();\n  public trendingProducts$ = this.trendingProductsSubject.asObservable();\n  public newArrivals$ = this.newArrivalsSubject.asObservable();\n  public quickLinks$ = this.quickLinksSubject.asObservable();\n  public shopStats$ = this.shopStatsSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    this.initializeQuickLinks();\n  }\n\n  /**\n   * Load all shop data in parallel\n   */\n  loadAllShopData(): Observable<any> {\n    return forkJoin({\n      brands: this.loadFeaturedBrands(),\n      trending: this.loadTrendingProducts(),\n      newArrivals: this.loadNewArrivals(),\n      stats: this.loadShopStats()\n    });\n  }\n\n  /**\n   * Load featured brands\n   */\n  loadFeaturedBrands(): Observable<Brand[]> {\n    return this.http.get<any>(`${this.API_URL}/brands/featured`).pipe(\n      map(response => {\n        const brands = response.success ? response.data : this.getMockFeaturedBrands();\n        this.featuredBrandsSubject.next(brands);\n        return brands;\n      }),\n      catchError(() => {\n        const mockBrands = this.getMockFeaturedBrands();\n        this.featuredBrandsSubject.next(mockBrands);\n        return of(mockBrands);\n      })\n    );\n  }\n\n  /**\n   * Load trending products\n   */\n  loadTrendingProducts(limit: number = 12): Observable<TrendingProduct[]> {\n    const params = new HttpParams().set('limit', limit.toString());\n\n    return this.http.get<any>(`${this.API_URL}/products/trending`, { params }).pipe(\n      map(response => {\n        const products = response.success ? response.data : this.getMockTrendingProducts();\n        this.trendingProductsSubject.next(products);\n        return products;\n      }),\n      catchError(() => {\n        const mockProducts = this.getMockTrendingProducts();\n        this.trendingProductsSubject.next(mockProducts);\n        return of(mockProducts);\n      })\n    );\n  }\n\n  /**\n   * Load new arrivals\n   */\n  loadNewArrivals(limit: number = 12): Observable<NewArrival[]> {\n    const params = new HttpParams()\n      .set('limit', limit.toString())\n      .set('sortBy', 'newest');\n\n    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(\n      map(response => {\n        const products = response.success ? response.data : this.getMockNewArrivals();\n        this.newArrivalsSubject.next(products);\n        return products;\n      }),\n      catchError(() => {\n        const mockProducts = this.getMockNewArrivals();\n        this.newArrivalsSubject.next(mockProducts);\n        return of(mockProducts);\n      })\n    );\n  }\n\n  /**\n   * Load shop statistics\n   */\n  loadShopStats(): Observable<ShopStats> {\n    return this.http.get<any>(`${this.API_URL}/shop/stats`).pipe(\n      map(response => {\n        const stats = response.success ? response.stats : this.getMockShopStats();\n        this.shopStatsSubject.next(stats);\n        return stats;\n      }),\n      catchError(() => {\n        const mockStats = this.getMockShopStats();\n        this.shopStatsSubject.next(mockStats);\n        return of(mockStats);\n      })\n    );\n  }\n\n  /**\n   * Get products by brand\n   */\n  getProductsByBrand(brandSlug: string, limit: number = 20): Observable<any[]> {\n    const params = new HttpParams().set('brand', brandSlug).set('limit', limit.toString());\n    \n    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(\n      map(response => response.success ? response.products : []),\n      catchError(() => of([]))\n    );\n  }\n\n  /**\n   * Get products by category\n   */\n  getProductsByCategory(category: string, subcategory?: string, limit: number = 20): Observable<any[]> {\n    let params = new HttpParams().set('category', category).set('limit', limit.toString());\n    if (subcategory) {\n      params = params.set('subcategory', subcategory);\n    }\n    \n    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(\n      map(response => response.success ? response.products : []),\n      catchError(() => of([]))\n    );\n  }\n\n  /**\n   * Search products\n   */\n  searchProducts(query: string, filters?: any): Observable<any> {\n    let params = new HttpParams().set('q', query);\n    \n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        if (filters[key]) {\n          params = params.set(key, filters[key]);\n        }\n      });\n    }\n    \n    return this.http.get<any>(`${this.API_URL}/search/products`, { params }).pipe(\n      map(response => response.success ? response : { products: [], totalCount: 0 }),\n      catchError(() => of({ products: [], totalCount: 0 }))\n    );\n  }\n\n  /**\n   * Initialize quick links\n   */\n  private initializeQuickLinks(): void {\n    const quickLinks: QuickLink[] = [\n      {\n        id: 'men-clothing',\n        title: 'Men\\'s Clothing',\n        description: 'Shirts, T-shirts, Jeans & More',\n        icon: 'fas fa-tshirt',\n        route: '/shop/category/men',\n        color: '#3498db',\n        category: 'men',\n        featured: true,\n        order: 1\n      },\n      {\n        id: 'women-clothing',\n        title: 'Women\\'s Clothing',\n        description: 'Dresses, Tops, Kurtis & More',\n        icon: 'fas fa-female',\n        route: '/shop/category/women',\n        color: '#e91e63',\n        category: 'women',\n        featured: true,\n        order: 2\n      },\n      {\n        id: 'footwear',\n        title: 'Footwear',\n        description: 'Shoes, Sandals, Sneakers',\n        icon: 'fas fa-shoe-prints',\n        route: '/shop/category/footwear',\n        color: '#ff9800',\n        category: 'footwear',\n        featured: true,\n        order: 3\n      },\n      {\n        id: 'accessories',\n        title: 'Accessories',\n        description: 'Bags, Watches, Jewelry',\n        icon: 'fas fa-gem',\n        route: '/shop/category/accessories',\n        color: '#9c27b0',\n        category: 'accessories',\n        featured: true,\n        order: 4\n      },\n      {\n        id: 'ethnic-wear',\n        title: 'Ethnic Wear',\n        description: 'Kurtas, Sarees, Traditional',\n        icon: 'fas fa-star-and-crescent',\n        route: '/shop/category/ethnic',\n        color: '#ff5722',\n        category: 'ethnic',\n        featured: true,\n        order: 5\n      },\n      {\n        id: 'sports-fitness',\n        title: 'Sports & Fitness',\n        description: 'Activewear, Gym Gear',\n        icon: 'fas fa-dumbbell',\n        route: '/shop/category/sports',\n        color: '#4caf50',\n        category: 'sports',\n        featured: true,\n        order: 6\n      },\n      {\n        id: 'sale',\n        title: 'Sale & Offers',\n        description: 'Up to 70% Off',\n        icon: 'fas fa-tags',\n        route: '/shop/sale',\n        color: '#f44336',\n        featured: true,\n        order: 7\n      },\n      {\n        id: 'new-arrivals',\n        title: 'New Arrivals',\n        description: 'Latest Fashion Trends',\n        icon: 'fas fa-sparkles',\n        route: '/shop/new-arrivals',\n        color: '#00bcd4',\n        featured: true,\n        order: 8\n      }\n    ];\n    \n    this.quickLinksSubject.next(quickLinks);\n  }\n\n  /**\n   * Get current cached data\n   */\n  getCurrentFeaturedBrands(): Brand[] {\n    return this.featuredBrandsSubject.value;\n  }\n\n  getCurrentTrendingProducts(): TrendingProduct[] {\n    return this.trendingProductsSubject.value;\n  }\n\n  getCurrentNewArrivals(): NewArrival[] {\n    return this.newArrivalsSubject.value;\n  }\n\n  getCurrentQuickLinks(): QuickLink[] {\n    return this.quickLinksSubject.value;\n  }\n\n  getCurrentShopStats(): ShopStats | null {\n    return this.shopStatsSubject.value;\n  }\n\n  /**\n   * Mock data methods for fallback\n   */\n  private getMockFeaturedBrands(): Brand[] {\n    return [\n      {\n        _id: '1',\n        name: 'Nike',\n        logo: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200',\n        description: 'Just Do It',\n        slug: 'nike',\n        isVerified: true,\n        productCount: 245,\n        rating: 4.8,\n        establishedYear: 1964,\n        website: 'https://nike.com',\n        categories: ['footwear', 'sportswear'],\n        featured: true,\n        trending: true\n      },\n      {\n        _id: '2',\n        name: 'Adidas',\n        logo: 'https://images.unsplash.com/photo-1556906781-9a412961c28c?w=200',\n        description: 'Impossible is Nothing',\n        slug: 'adidas',\n        isVerified: true,\n        productCount: 189,\n        rating: 4.7,\n        establishedYear: 1949,\n        categories: ['footwear', 'sportswear'],\n        featured: true,\n        trending: true\n      },\n      {\n        _id: '3',\n        name: 'Zara',\n        logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200',\n        description: 'Fashion Forward',\n        slug: 'zara',\n        isVerified: true,\n        productCount: 567,\n        rating: 4.6,\n        categories: ['men', 'women', 'accessories'],\n        featured: true,\n        trending: false\n      }\n    ];\n  }\n\n  private getMockTrendingProducts(): TrendingProduct[] {\n    return [\n      {\n        _id: '1',\n        name: 'Premium Cotton T-Shirt',\n        description: 'Comfortable cotton t-shirt perfect for daily wear',\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', alt: 'Cotton T-Shirt', isPrimary: true }],\n        pricing: { mrp: 999, sellingPrice: 699, discountPercentage: 30 },\n        brand: 'Nike',\n        category: 'men',\n        subcategory: 'tshirts',\n        rating: { average: 4.5, count: 128 },\n        analytics: { views: 1250, likes: 89, shares: 23, purchases: 67 },\n        isTrending: true,\n        trendingScore: 95,\n        availability: { status: 'in-stock', totalStock: 45 }\n      }\n    ];\n  }\n\n  private getMockNewArrivals(): NewArrival[] {\n    return [\n      {\n        _id: '1',\n        name: 'Designer Kurta Set',\n        description: 'Elegant kurta set for special occasions',\n        images: [{ url: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=400', alt: 'Kurta Set', isPrimary: true }],\n        pricing: { mrp: 2999, sellingPrice: 2299, discountPercentage: 23 },\n        brand: 'Ethnic Wear Co',\n        category: 'men',\n        subcategory: 'kurtas',\n        rating: { average: 4.7, count: 45 },\n        createdAt: new Date().toISOString(),\n        isNew: true,\n        availability: { status: 'in-stock', totalStock: 23 }\n      }\n    ];\n  }\n\n  private getMockShopStats(): ShopStats {\n    return {\n      totalProducts: 12547,\n      totalBrands: 456,\n      totalCategories: 28,\n      totalUsers: 89234,\n      newArrivalsCount: 234,\n      trendingCount: 156\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAAqBC,eAAe,EAAEC,EAAE,EAAEC,QAAQ,QAAQ,MAAM;AAChE,SAASC,GAAG,EAAEC,UAAU,QAAa,gBAAgB;AACrD,SAASC,WAAW,QAAQ,mCAAmC;;;AAkF/D,OAAM,MAAOC,eAAe;EAiB1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAhBP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;IAE7C;IACQ,KAAAC,qBAAqB,GAAG,IAAIX,eAAe,CAAU,EAAE,CAAC;IACxD,KAAAY,uBAAuB,GAAG,IAAIZ,eAAe,CAAoB,EAAE,CAAC;IACpE,KAAAa,kBAAkB,GAAG,IAAIb,eAAe,CAAe,EAAE,CAAC;IAC1D,KAAAc,iBAAiB,GAAG,IAAId,eAAe,CAAc,EAAE,CAAC;IACxD,KAAAe,gBAAgB,GAAG,IAAIf,eAAe,CAAmB,IAAI,CAAC;IAEtE;IACO,KAAAgB,eAAe,GAAG,IAAI,CAACL,qBAAqB,CAACM,YAAY,EAAE;IAC3D,KAAAC,iBAAiB,GAAG,IAAI,CAACN,uBAAuB,CAACK,YAAY,EAAE;IAC/D,KAAAE,YAAY,GAAG,IAAI,CAACN,kBAAkB,CAACI,YAAY,EAAE;IACrD,KAAAG,WAAW,GAAG,IAAI,CAACN,iBAAiB,CAACG,YAAY,EAAE;IACnD,KAAAI,UAAU,GAAG,IAAI,CAACN,gBAAgB,CAACE,YAAY,EAAE;IAGtD,IAAI,CAACK,oBAAoB,EAAE;EAC7B;EAEA;;;EAGAC,eAAeA,CAAA;IACb,OAAOrB,QAAQ,CAAC;MACdsB,MAAM,EAAE,IAAI,CAACC,kBAAkB,EAAE;MACjCC,QAAQ,EAAE,IAAI,CAACC,oBAAoB,EAAE;MACrCC,WAAW,EAAE,IAAI,CAACC,eAAe,EAAE;MACnCC,KAAK,EAAE,IAAI,CAACC,aAAa;KAC1B,CAAC;EACJ;EAEA;;;EAGAN,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACjB,IAAI,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,kBAAkB,CAAC,CAACwB,IAAI,CAC/D9B,GAAG,CAAC+B,QAAQ,IAAG;MACb,MAAMV,MAAM,GAAGU,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACC,qBAAqB,EAAE;MAC9E,IAAI,CAAC1B,qBAAqB,CAAC2B,IAAI,CAACd,MAAM,CAAC;MACvC,OAAOA,MAAM;IACf,CAAC,CAAC,EACFpB,UAAU,CAAC,MAAK;MACd,MAAMmC,UAAU,GAAG,IAAI,CAACF,qBAAqB,EAAE;MAC/C,IAAI,CAAC1B,qBAAqB,CAAC2B,IAAI,CAACC,UAAU,CAAC;MAC3C,OAAOtC,EAAE,CAACsC,UAAU,CAAC;IACvB,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAZ,oBAAoBA,CAACa,KAAA,GAAgB,EAAE;IACrC,MAAMC,MAAM,GAAG,IAAI1C,UAAU,EAAE,CAAC2C,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC;IAE9D,OAAO,IAAI,CAACnC,IAAI,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,oBAAoB,EAAE;MAAEgC;IAAM,CAAE,CAAC,CAACR,IAAI,CAC7E9B,GAAG,CAAC+B,QAAQ,IAAG;MACb,MAAMU,QAAQ,GAAGV,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACS,uBAAuB,EAAE;MAClF,IAAI,CAACjC,uBAAuB,CAAC0B,IAAI,CAACM,QAAQ,CAAC;MAC3C,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFxC,UAAU,CAAC,MAAK;MACd,MAAM0C,YAAY,GAAG,IAAI,CAACD,uBAAuB,EAAE;MACnD,IAAI,CAACjC,uBAAuB,CAAC0B,IAAI,CAACQ,YAAY,CAAC;MAC/C,OAAO7C,EAAE,CAAC6C,YAAY,CAAC;IACzB,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAjB,eAAeA,CAACW,KAAA,GAAgB,EAAE;IAChC,MAAMC,MAAM,GAAG,IAAI1C,UAAU,EAAE,CAC5B2C,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC,CAC9BD,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE1B,OAAO,IAAI,CAAClC,IAAI,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,WAAW,EAAE;MAAEgC;IAAM,CAAE,CAAC,CAACR,IAAI,CACpE9B,GAAG,CAAC+B,QAAQ,IAAG;MACb,MAAMU,QAAQ,GAAGV,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACW,kBAAkB,EAAE;MAC7E,IAAI,CAAClC,kBAAkB,CAACyB,IAAI,CAACM,QAAQ,CAAC;MACtC,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFxC,UAAU,CAAC,MAAK;MACd,MAAM0C,YAAY,GAAG,IAAI,CAACC,kBAAkB,EAAE;MAC9C,IAAI,CAAClC,kBAAkB,CAACyB,IAAI,CAACQ,YAAY,CAAC;MAC1C,OAAO7C,EAAE,CAAC6C,YAAY,CAAC;IACzB,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAf,aAAaA,CAAA;IACX,OAAO,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,aAAa,CAAC,CAACwB,IAAI,CAC1D9B,GAAG,CAAC+B,QAAQ,IAAG;MACb,MAAMJ,KAAK,GAAGI,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACJ,KAAK,GAAG,IAAI,CAACkB,gBAAgB,EAAE;MACzE,IAAI,CAACjC,gBAAgB,CAACuB,IAAI,CAACR,KAAK,CAAC;MACjC,OAAOA,KAAK;IACd,CAAC,CAAC,EACF1B,UAAU,CAAC,MAAK;MACd,MAAM6C,SAAS,GAAG,IAAI,CAACD,gBAAgB,EAAE;MACzC,IAAI,CAACjC,gBAAgB,CAACuB,IAAI,CAACW,SAAS,CAAC;MACrC,OAAOhD,EAAE,CAACgD,SAAS,CAAC;IACtB,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAC,kBAAkBA,CAACC,SAAiB,EAAEX,KAAA,GAAgB,EAAE;IACtD,MAAMC,MAAM,GAAG,IAAI1C,UAAU,EAAE,CAAC2C,GAAG,CAAC,OAAO,EAAES,SAAS,CAAC,CAACT,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC;IAEtF,OAAO,IAAI,CAACnC,IAAI,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,WAAW,EAAE;MAAEgC;IAAM,CAAE,CAAC,CAACR,IAAI,CACpE9B,GAAG,CAAC+B,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACU,QAAQ,GAAG,EAAE,CAAC,EAC1DxC,UAAU,CAAC,MAAMH,EAAE,CAAC,EAAE,CAAC,CAAC,CACzB;EACH;EAEA;;;EAGAmD,qBAAqBA,CAACC,QAAgB,EAAEC,WAAoB,EAAEd,KAAA,GAAgB,EAAE;IAC9E,IAAIC,MAAM,GAAG,IAAI1C,UAAU,EAAE,CAAC2C,GAAG,CAAC,UAAU,EAAEW,QAAQ,CAAC,CAACX,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC;IACtF,IAAIW,WAAW,EAAE;MACfb,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,aAAa,EAAEY,WAAW,CAAC;;IAGjD,OAAO,IAAI,CAAC9C,IAAI,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,WAAW,EAAE;MAAEgC;IAAM,CAAE,CAAC,CAACR,IAAI,CACpE9B,GAAG,CAAC+B,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACU,QAAQ,GAAG,EAAE,CAAC,EAC1DxC,UAAU,CAAC,MAAMH,EAAE,CAAC,EAAE,CAAC,CAAC,CACzB;EACH;EAEA;;;EAGAsD,cAAcA,CAACC,KAAa,EAAEC,OAAa;IACzC,IAAIhB,MAAM,GAAG,IAAI1C,UAAU,EAAE,CAAC2C,GAAG,CAAC,GAAG,EAAEc,KAAK,CAAC;IAE7C,IAAIC,OAAO,EAAE;MACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;QACjC,IAAIJ,OAAO,CAACI,GAAG,CAAC,EAAE;UAChBpB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACmB,GAAG,EAAEJ,OAAO,CAACI,GAAG,CAAC,CAAC;;MAE1C,CAAC,CAAC;;IAGJ,OAAO,IAAI,CAACrD,IAAI,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,kBAAkB,EAAE;MAAEgC;IAAM,CAAE,CAAC,CAACR,IAAI,CAC3E9B,GAAG,CAAC+B,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,GAAG;MAAEU,QAAQ,EAAE,EAAE;MAAEkB,UAAU,EAAE;IAAC,CAAE,CAAC,EAC9E1D,UAAU,CAAC,MAAMH,EAAE,CAAC;MAAE2C,QAAQ,EAAE,EAAE;MAAEkB,UAAU,EAAE;IAAC,CAAE,CAAC,CAAC,CACtD;EACH;EAEA;;;EAGQxC,oBAAoBA,CAAA;IAC1B,MAAMyC,UAAU,GAAgB,CAC9B;MACEC,EAAE,EAAE,cAAc;MAClBC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,gCAAgC;MAC7CC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,oBAAoB;MAC3BC,KAAK,EAAE,SAAS;MAChBhB,QAAQ,EAAE,KAAK;MACfiB,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,EACD;MACEP,EAAE,EAAE,gBAAgB;MACpBC,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,8BAA8B;MAC3CC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,sBAAsB;MAC7BC,KAAK,EAAE,SAAS;MAChBhB,QAAQ,EAAE,OAAO;MACjBiB,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,EACD;MACEP,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE,yBAAyB;MAChCC,KAAK,EAAE,SAAS;MAChBhB,QAAQ,EAAE,UAAU;MACpBiB,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,EACD;MACEP,EAAE,EAAE,aAAa;MACjBC,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,4BAA4B;MACnCC,KAAK,EAAE,SAAS;MAChBhB,QAAQ,EAAE,aAAa;MACvBiB,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,EACD;MACEP,EAAE,EAAE,aAAa;MACjBC,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE,6BAA6B;MAC1CC,IAAI,EAAE,0BAA0B;MAChCC,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,SAAS;MAChBhB,QAAQ,EAAE,QAAQ;MAClBiB,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,EACD;MACEP,EAAE,EAAE,gBAAgB;MACpBC,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,sBAAsB;MACnCC,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,SAAS;MAChBhB,QAAQ,EAAE,QAAQ;MAClBiB,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,EACD;MACEP,EAAE,EAAE,MAAM;MACVC,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,eAAe;MAC5BC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,EACD;MACEP,EAAE,EAAE,cAAc;MAClBC,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,uBAAuB;MACpCC,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,oBAAoB;MAC3BC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,CACF;IAED,IAAI,CAACzD,iBAAiB,CAACwB,IAAI,CAACyB,UAAU,CAAC;EACzC;EAEA;;;EAGAS,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC7D,qBAAqB,CAAC8D,KAAK;EACzC;EAEAC,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAC9D,uBAAuB,CAAC6D,KAAK;EAC3C;EAEAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC9D,kBAAkB,CAAC4D,KAAK;EACtC;EAEAG,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC9D,iBAAiB,CAAC2D,KAAK;EACrC;EAEAI,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC9D,gBAAgB,CAAC0D,KAAK;EACpC;EAEA;;;EAGQpC,qBAAqBA,CAAA;IAC3B,OAAO,CACL;MACEyC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,iEAAiE;MACvEd,WAAW,EAAE,YAAY;MACzBe,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,GAAG;MACjBC,MAAM,EAAE,GAAG;MACXC,eAAe,EAAE,IAAI;MACrBC,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;MACtCjB,QAAQ,EAAE,IAAI;MACd5C,QAAQ,EAAE;KACX,EACD;MACEoD,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,iEAAiE;MACvEd,WAAW,EAAE,uBAAuB;MACpCe,IAAI,EAAE,QAAQ;MACdC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,GAAG;MACjBC,MAAM,EAAE,GAAG;MACXC,eAAe,EAAE,IAAI;MACrBE,UAAU,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;MACtCjB,QAAQ,EAAE,IAAI;MACd5C,QAAQ,EAAE;KACX,EACD;MACEoD,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,oEAAoE;MAC1Ed,WAAW,EAAE,iBAAiB;MAC9Be,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,GAAG;MACjBC,MAAM,EAAE,GAAG;MACXG,UAAU,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,aAAa,CAAC;MAC3CjB,QAAQ,EAAE,IAAI;MACd5C,QAAQ,EAAE;KACX,CACF;EACH;EAEQmB,uBAAuBA,CAAA;IAC7B,OAAO,CACL;MACEiC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,wBAAwB;MAC9Bb,WAAW,EAAE,mDAAmD;MAChEsB,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC/HC,OAAO,EAAE;QAAEC,GAAG,EAAE,GAAG;QAAEC,YAAY,EAAE,GAAG;QAAEC,kBAAkB,EAAE;MAAE,CAAE;MAChEC,KAAK,EAAE,MAAM;MACb3C,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,SAAS;MACtB8B,MAAM,EAAE;QAAEa,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAE,CAAE;MAChEC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE;QAAEC,MAAM,EAAE,UAAU;QAAEC,UAAU,EAAE;MAAE;KACnD,CACF;EACH;EAEQ7D,kBAAkBA,CAAA;IACxB,OAAO,CACL;MACE+B,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,oBAAoB;MAC1Bb,WAAW,EAAE,yCAAyC;MACtDsB,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,WAAW;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC1HC,OAAO,EAAE;QAAEC,GAAG,EAAE,IAAI;QAAEC,YAAY,EAAE,IAAI;QAAEC,kBAAkB,EAAE;MAAE,CAAE;MAClEC,KAAK,EAAE,gBAAgB;MACvB3C,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,QAAQ;MACrB8B,MAAM,EAAE;QAAEa,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnCW,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;MACnCC,KAAK,EAAE,IAAI;MACXN,YAAY,EAAE;QAAEC,MAAM,EAAE,UAAU;QAAEC,UAAU,EAAE;MAAE;KACnD,CACF;EACH;EAEQ5D,gBAAgBA,CAAA;IACtB,OAAO;MACLiE,aAAa,EAAE,KAAK;MACpBC,WAAW,EAAE,GAAG;MAChBC,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,KAAK;MACjBC,gBAAgB,EAAE,GAAG;MACrBC,aAAa,EAAE;KAChB;EACH;;;uBAvXWhH,eAAe,EAAAiH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfpH,eAAe;MAAAqH,OAAA,EAAfrH,eAAe,CAAAsH,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}