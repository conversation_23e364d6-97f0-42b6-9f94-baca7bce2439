{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-be245865.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = el => {\n  const controlEl = el;\n  let legacyControl;\n  const hasLegacyControl = () => {\n    if (legacyControl === undefined) {\n      /**\n       * Detect if developers are using the legacy form control syntax\n       * so a deprecation warning is logged. This warning can be disabled\n       * by either using the new `label` property or setting `aria-label`\n       * on the control.\n       * Alternatively, components that use a slot for the label\n       * can check to see if the component has slotted text\n       * in the light DOM.\n       */\n      const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n      const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n      // Shadow DOM form controls cannot use aria-labelledby\n      controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null;\n      const legacyItemLabel = findItemLabel(controlEl);\n      /**\n       * Developers can manually opt-out of the modern form markup\n       * by setting `legacy=\"true\"` on components.\n       */\n      legacyControl = controlEl.legacy === true || !hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null;\n    }\n    return legacyControl;\n  };\n  return {\n    hasLegacyControl\n  };\n};\nconst hasLabelSlot = controlEl => {\n  /**\n   * Components that have a named label slot\n   * also have other slots, so we need to query for\n   * anything that is explicitly passed to slot=\"label\"\n   */\n  if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n    return true;\n  }\n  /**\n   * Components that have an unnamed slot for the label\n   * have no other slots, so we can check the textContent\n   * of the element.\n   */\n  if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n    return true;\n  }\n  return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-INPUT', 'ION-TEXTAREA', 'ION-SELECT', 'ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\nexport { createLegacyFormController as c };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}