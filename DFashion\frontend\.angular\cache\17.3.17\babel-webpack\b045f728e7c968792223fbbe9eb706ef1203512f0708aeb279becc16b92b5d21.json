{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../core/services/wishlist-new.service\";\nimport * as i3 from \"../../core/services/cart-new.service\";\nimport * as i4 from \"../../core/services/auth.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction WishlistComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_12_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.moveAllToCart());\n    });\n    i0.ɵɵelement(3, \"i\", 14);\n    i0.ɵɵtext(4, \" Move All to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_12_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearWishlist());\n    });\n    i0.ɵɵelement(6, \"i\", 16);\n    i0.ɵɵtext(7, \" Clear Wishlist \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 17)(9, \"label\");\n    i0.ɵɵtext(10, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"select\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function WishlistComponent_div_12_Template_select_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.sortBy, $event) || (ctx_r1.sortBy = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function WishlistComponent_div_12_Template_select_change_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortWishlist());\n    });\n    i0.ɵɵelementStart(12, \"option\", 19);\n    i0.ɵɵtext(13, \"Recently Added\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 20);\n    i0.ɵɵtext(15, \"Price: Low to High\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 21);\n    i0.ɵɵtext(17, \"Price: High to Low\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 22);\n    i0.ɵɵtext(19, \"Product Name\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.sortBy);\n  }\n}\nfunction WishlistComponent_div_13_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(item_r4.product), \"% OFF \");\n  }\n}\nfunction WishlistComponent_div_13_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\");\n    i0.ɵɵtext(2, \"Currently Unavailable\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction WishlistComponent_div_13_div_1_div_10_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n}\nfunction WishlistComponent_div_13_div_1_div_10_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 52);\n  }\n}\nfunction WishlistComponent_div_13_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵtemplate(2, WishlistComponent_div_13_div_1_div_10_i_2_Template, 1, 0, \"i\", 48)(3, WishlistComponent_div_13_div_1_div_10_i_3_Template, 1, 0, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 50);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStars(item_r4.product.rating.average));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getEmptyStars(item_r4.product.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", item_r4.product.rating.count, \")\");\n  }\n}\nfunction WishlistComponent_div_13_div_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, item_r4.product.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction WishlistComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_13_div_1_Template_div_click_1_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(item_r4.product));\n    });\n    i0.ɵɵelement(2, \"img\", 27);\n    i0.ɵɵtemplate(3, WishlistComponent_div_13_div_1_div_3_Template, 2, 1, \"div\", 28)(4, WishlistComponent_div_13_div_1_div_4_Template, 3, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30)(6, \"h3\", 31);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_13_div_1_Template_h3_click_6_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(item_r4.product));\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 32);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, WishlistComponent_div_13_div_1_div_10_Template, 6, 3, \"div\", 33);\n    i0.ɵɵelementStart(11, \"div\", 34)(12, \"span\", 35);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, WishlistComponent_div_13_div_1_span_15_Template, 3, 4, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 37)(17, \"span\", 38);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 39);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 40)(22, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_13_div_1_Template_button_click_22_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(item_r4));\n    });\n    i0.ɵɵelement(23, \"i\", 14);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_13_div_1_Template_button_click_25_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeFromWishlist(item_r4));\n    });\n    i0.ɵɵelement(26, \"i\", 43);\n    i0.ɵɵtext(27, \" Remove \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(item_r4.product), i0.ɵɵsanitizeUrl)(\"alt\", item_r4.product.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.product.originalPrice && item_r4.product.originalPrice > item_r4.product.price);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r4.product.isActive);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.product.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.product.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(14, 14, item_r4.product.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r4.product.originalPrice && item_r4.product.originalPrice > item_r4.product.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Added \", ctx_r1.getTimeAgo(item_r4.addedAt), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"from \", item_r4.addedFrom, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !item_r4.product.isActive || ctx_r1.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", !item_r4.product.isActive ? \"Unavailable\" : \"Add to Cart\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n  }\n}\nfunction WishlistComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, WishlistComponent_div_13_div_1_Template, 28, 17, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.sortedWishlistItems);\n  }\n}\nfunction WishlistComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 43);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Your wishlist is empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Save items you love to buy them later\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 56)(8, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_14_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goShopping());\n    });\n    i0.ɵɵelement(9, \"i\", 58);\n    i0.ɵɵtext(10, \" Start Shopping \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_14_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.browsePosts());\n    });\n    i0.ɵɵelement(12, \"i\", 60);\n    i0.ɵɵtext(13, \" Browse Posts \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction WishlistComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"div\", 62);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading your wishlist...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n// WishlistItem interface is now imported from the service\nexport let WishlistComponent = /*#__PURE__*/(() => {\n  class WishlistComponent {\n    constructor(router, wishlistService, cartService, authService) {\n      this.router = router;\n      this.wishlistService = wishlistService;\n      this.cartService = cartService;\n      this.authService = authService;\n      this.wishlistItems = [];\n      this.sortedWishlistItems = [];\n      this.loading = true;\n      this.sortBy = 'recent';\n    }\n    ngOnInit() {\n      this.loadWishlist();\n    }\n    loadWishlist() {\n      this.loading = true;\n      this.wishlistService.loadWishlist().subscribe({\n        next: response => {\n          if (response.success) {\n            this.wishlistItems = response.wishlist.items;\n            this.sortWishlist();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading wishlist:', error);\n          this.loading = false;\n        }\n      });\n    }\n    // Removed mock data - now using real API data from seeder\n    sortWishlist() {\n      let sorted = [...this.wishlistItems];\n      switch (this.sortBy) {\n        case 'recent':\n          sorted.sort((a, b) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime());\n          break;\n        case 'price-low':\n          sorted.sort((a, b) => a.product.price - b.product.price);\n          break;\n        case 'price-high':\n          sorted.sort((a, b) => b.product.price - a.product.price);\n          break;\n        case 'name':\n          sorted.sort((a, b) => a.product.name.localeCompare(b.product.name));\n          break;\n      }\n      this.sortedWishlistItems = sorted;\n    }\n    getProductImage(product) {\n      return product.images[0]?.url || '/assets/images/placeholder.jpg';\n    }\n    getDiscountPercentage(product) {\n      if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    getStars(rating) {\n      return Array(Math.floor(rating)).fill(0);\n    }\n    getEmptyStars(rating) {\n      return Array(5 - Math.floor(rating)).fill(0);\n    }\n    getTimeAgo(date) {\n      const now = new Date();\n      const diffMs = now.getTime() - new Date(date).getTime();\n      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n      if (diffDays === 0) return 'today';\n      if (diffDays === 1) return 'yesterday';\n      if (diffDays < 7) return `${diffDays} days ago`;\n      if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;\n      return `${Math.floor(diffDays / 30)} months ago`;\n    }\n    viewProduct(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    addToCart(item) {\n      this.cartService.addFromWishlist(item.product._id, 1, item.size, item.color).subscribe({\n        next: response => {\n          if (response.success) {\n            // Optionally remove from wishlist after adding to cart\n            // this.removeFromWishlist(item);\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n        }\n      });\n    }\n    removeFromWishlist(item) {\n      this.wishlistService.removeFromWishlist(item._id).subscribe({\n        next: response => {\n          if (response.success) {\n            // Refresh the wishlist to get updated data and count\n            this.loadWishlist();\n            console.log('✅ Item removed from wishlist successfully');\n          }\n        },\n        error: error => {\n          console.error('Error removing from wishlist:', error);\n        }\n      });\n    }\n    moveAllToCart() {\n      const activeItems = this.wishlistItems.filter(item => item.product.isActive);\n      if (activeItems.length === 0) {\n        alert('No available items to move to cart');\n        return;\n      }\n      if (confirm(`Move ${activeItems.length} items to cart?`)) {\n        // Move items one by one\n        activeItems.forEach(item => {\n          this.wishlistService.moveToCart(item._id, 1).subscribe({\n            next: response => {\n              if (response.success) {\n                this.loadWishlist(); // Refresh wishlist\n              }\n            },\n            error: error => {\n              console.error('Error moving to cart:', error);\n            }\n          });\n        });\n      }\n    }\n    clearWishlist() {\n      if (confirm('Are you sure you want to clear your entire wishlist?')) {\n        // Remove all items one by one\n        this.wishlistItems.forEach(item => {\n          this.removeFromWishlist(item);\n        });\n      }\n    }\n    goHome() {\n      this.router.navigate(['/home']);\n    }\n    goShopping() {\n      this.router.navigate(['/shop']);\n    }\n    browsePosts() {\n      this.router.navigate(['/posts']);\n    }\n    static {\n      this.ɵfac = function WishlistComponent_Factory(t) {\n        return new (t || WishlistComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WishlistNewService), i0.ɵɵdirectiveInject(i3.CartNewService), i0.ɵɵdirectiveInject(i4.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: WishlistComponent,\n        selectors: [[\"app-wishlist\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 16,\n        vars: 5,\n        consts: [[1, \"wishlist-container\"], [1, \"wishlist-header\"], [1, \"breadcrumb\"], [3, \"click\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"current\"], [1, \"wishlist-count\"], [\"class\", \"wishlist-actions\", 4, \"ngIf\"], [\"class\", \"wishlist-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [1, \"wishlist-actions\"], [1, \"action-buttons\"], [1, \"btn-move-all\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-clear-all\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-trash\"], [1, \"sort-options\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"recent\"], [\"value\", \"price-low\"], [\"value\", \"price-high\"], [\"value\", \"name\"], [1, \"wishlist-grid\"], [\"class\", \"wishlist-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"wishlist-item\"], [1, \"item-image\", 3, \"click\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [\"class\", \"unavailable-overlay\", 4, \"ngIf\"], [1, \"item-details\"], [1, \"product-name\", 3, \"click\"], [1, \"product-brand\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"added-info\"], [1, \"added-date\"], [1, \"added-from\"], [1, \"item-actions\"], [1, \"btn-add-cart\", 3, \"click\", \"disabled\"], [1, \"btn-remove\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-heart\"], [1, \"discount-badge\"], [1, \"unavailable-overlay\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"far fa-star\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"fas\", \"fa-star\"], [1, \"far\", \"fa-star\"], [1, \"original-price\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"empty-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-images\"], [1, \"loading-state\"], [1, \"loading-spinner\"]],\n        template: function WishlistComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n            i0.ɵɵlistener(\"click\", function WishlistComponent_Template_span_click_3_listener() {\n              return ctx.goHome();\n            });\n            i0.ɵɵtext(4, \"Home\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"i\", 4);\n            i0.ɵɵelementStart(6, \"span\", 5);\n            i0.ɵɵtext(7, \"My Wishlist\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"h1\");\n            i0.ɵɵtext(9, \"My Wishlist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"p\", 6);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(12, WishlistComponent_div_12_Template, 20, 3, \"div\", 7)(13, WishlistComponent_div_13_Template, 2, 1, \"div\", 8)(14, WishlistComponent_div_14_Template, 14, 0, \"div\", 9)(15, WishlistComponent_div_15_Template, 4, 0, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"\", ctx.wishlistItems.length, \" items saved\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistItems.length === 0 && !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, FormsModule, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel],\n        styles: [\".wishlist-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.wishlist-header[_ngcontent-%COMP%]{margin-bottom:30px}.breadcrumb[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-size:.9rem;color:#666}.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{cursor:pointer}.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover{color:#007bff}.breadcrumb[_ngcontent-%COMP%]   .current[_ngcontent-%COMP%]{color:#333;font-weight:500}.wishlist-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:8px;color:#333}.wishlist-count[_ngcontent-%COMP%]{font-size:1.1rem;color:#666;margin:0}.wishlist-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;background:#fff;border-radius:12px;padding:20px;margin-bottom:30px;box-shadow:0 2px 8px #0000001a}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:12px}.btn-move-all[_ngcontent-%COMP%], .btn-clear-all[_ngcontent-%COMP%]{padding:10px 20px;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;gap:8px}.btn-move-all[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-move-all[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.btn-clear-all[_ngcontent-%COMP%]{background:#f8f9fa;color:#dc3545;border:1px solid #dc3545}.btn-clear-all[_ngcontent-%COMP%]:hover:not(:disabled){background:#dc3545;color:#fff}.sort-options[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.sort-options[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;color:#333}.sort-options[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:8px 12px;border:1px solid #ddd;border-radius:6px;font-size:.9rem}.wishlist-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:24px}.wishlist-item[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000001a;transition:all .3s ease}.wishlist-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.item-image[_ngcontent-%COMP%]{position:relative;height:250px;overflow:hidden;cursor:pointer}.item-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.wishlist-item[_ngcontent-%COMP%]:hover   .item-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.05)}.discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:#ff4757;color:#fff;padding:4px 8px;border-radius:12px;font-size:.75rem;font-weight:600}.unavailable-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#000000b3;display:flex;align-items:center;justify-content:center;color:#fff;font-weight:600}.item-details[_ngcontent-%COMP%]{padding:20px}.product-name[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:4px;color:#333;line-height:1.3;cursor:pointer}.product-name[_ngcontent-%COMP%]:hover{color:#007bff}.product-brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:8px}.product-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.stars[_ngcontent-%COMP%]{display:flex;gap:2px}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem;color:#ffc107}.rating-count[_ngcontent-%COMP%]{font-size:.8rem;color:#666}.product-price[_ngcontent-%COMP%]{margin-bottom:12px}.current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#333}.original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#999;text-decoration:line-through;margin-left:8px}.added-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;font-size:.8rem;color:#666;margin-bottom:16px}.added-from[_ngcontent-%COMP%]{text-transform:capitalize}.item-actions[_ngcontent-%COMP%]{display:flex;gap:8px;padding:0 20px 20px}.btn-add-cart[_ngcontent-%COMP%], .btn-remove[_ngcontent-%COMP%]{flex:1;padding:12px;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center;gap:8px}.btn-add-cart[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-add-cart[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.btn-add-cart[_ngcontent-%COMP%]:disabled{background:#6c757d;cursor:not-allowed}.btn-remove[_ngcontent-%COMP%]{background:#f8f9fa;color:#dc3545;border:1px solid #dc3545}.btn-remove[_ngcontent-%COMP%]:hover:not(:disabled){background:#dc3545;color:#fff}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:80px 20px}.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#ff6b9d;margin-bottom:20px}.empty-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.8rem;margin-bottom:10px;color:#333}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:30px;font-size:1.1rem}.empty-actions[_ngcontent-%COMP%]{display:flex;gap:16px;justify-content:center}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%]{padding:12px 24px;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;gap:8px}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{background:#0056b3}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#007bff;border:1px solid #007bff}.btn-secondary[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.loading-state[_ngcontent-%COMP%]{text-align:center;padding:80px 20px}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 20px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.wishlist-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.wishlist-actions[_ngcontent-%COMP%]{flex-direction:column;gap:16px;align-items:stretch}.action-buttons[_ngcontent-%COMP%]{justify-content:center}.sort-options[_ngcontent-%COMP%]{justify-content:space-between}.wishlist-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.empty-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}}\"]\n      });\n    }\n  }\n  return WishlistComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}