{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Observable, finalize } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class LoadingService {\n  constructor(loadingController) {\n    this.loadingController = loadingController;\n    this.loadingSubject = new BehaviorSubject(false);\n    this.loadingCount = 0;\n    this.currentLoading = null;\n    // Enhanced loading state management\n    this.loadingStateSubject = new BehaviorSubject({});\n    this.loadingState$ = this.loadingStateSubject.asObservable();\n    this.isLoading$ = this.loadingSubject.asObservable();\n  }\n  show() {\n    var _this = this;\n    return _asyncToGenerator(function* (message = 'Loading...', duration) {\n      _this.loadingCount++;\n      if (_this.loadingCount === 1) {\n        _this.loadingSubject.next(true);\n        _this.currentLoading = yield _this.loadingController.create({\n          message,\n          duration,\n          spinner: 'crescent',\n          cssClass: 'custom-loading'\n        });\n        yield _this.currentLoading.present();\n      }\n    }).apply(this, arguments);\n  }\n  hide() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.loadingCount > 0) {\n        _this2.loadingCount--;\n      }\n      if (_this2.loadingCount === 0) {\n        _this2.loadingSubject.next(false);\n        if (_this2.currentLoading) {\n          yield _this2.currentLoading.dismiss();\n          _this2.currentLoading = null;\n        }\n      }\n    })();\n  }\n  hideAll() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.loadingCount = 0;\n      _this3.loadingSubject.next(false);\n      if (_this3.currentLoading) {\n        yield _this3.currentLoading.dismiss();\n        _this3.currentLoading = null;\n      }\n    })();\n  }\n  isLoading() {\n    return this.loadingSubject.value;\n  }\n  // Enhanced loading methods\n  startLoading(key, message) {\n    const currentState = this.loadingStateSubject.value;\n    const newState = {\n      ...currentState,\n      [key]: {\n        isLoading: true,\n        message,\n        progress: 0,\n        startTime: new Date()\n      }\n    };\n    this.loadingStateSubject.next(newState);\n    this.updateGlobalLoading();\n  }\n  stopLoading(key) {\n    const currentState = this.loadingStateSubject.value;\n    const newState = {\n      ...currentState\n    };\n    if (newState[key]) {\n      delete newState[key];\n    }\n    this.loadingStateSubject.next(newState);\n    this.updateGlobalLoading();\n  }\n  updateProgress(key, progress, message) {\n    const currentState = this.loadingStateSubject.value;\n    if (currentState[key]) {\n      const newState = {\n        ...currentState,\n        [key]: {\n          ...currentState[key],\n          progress: Math.max(0, Math.min(100, progress)),\n          message: message || currentState[key].message\n        }\n      };\n      this.loadingStateSubject.next(newState);\n    }\n  }\n  isLoadingKey(key) {\n    return new Observable(observer => {\n      this.loadingState$.subscribe(state => {\n        observer.next(!!state[key]?.isLoading);\n      });\n    });\n  }\n  isLoadingKeySync(key) {\n    const currentState = this.loadingStateSubject.value;\n    return !!currentState[key]?.isLoading;\n  }\n  updateGlobalLoading() {\n    const currentState = this.loadingStateSubject.value;\n    const hasLoading = Object.values(currentState).some(state => state.isLoading);\n    this.loadingSubject.next(hasLoading);\n  }\n  wrapWithLoading(source, key, message) {\n    this.startLoading(key, message);\n    return source.pipe(finalize(() => this.stopLoading(key)));\n  }\n  // Predefined loading keys\n  static {\n    this.KEYS = {\n      LOGIN: 'auth.login',\n      LOGOUT: 'auth.logout',\n      REGISTER: 'auth.register',\n      PROFILE_LOAD: 'user.profile.load',\n      PRODUCTS_LOAD: 'shop.products.load',\n      CART_LOAD: 'cart.load',\n      SEARCH: 'search.execute',\n      BRANDS_LOAD: 'shop.brands.load',\n      GLOBAL: 'global'\n    };\n  }\n  static {\n    this.ɵfac = function LoadingService_Factory(t) {\n      return new (t || LoadingService)(i0.ɵɵinject(i1.LoadingController));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoadingService,\n      factory: LoadingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "finalize", "LoadingService", "constructor", "loadingController", "loadingSubject", "loadingCount", "currentLoading", "loadingStateSubject", "loadingState$", "asObservable", "isLoading$", "show", "_this", "_asyncToGenerator", "message", "duration", "next", "create", "spinner", "cssClass", "present", "apply", "arguments", "hide", "_this2", "dismiss", "hide<PERSON>ll", "_this3", "isLoading", "value", "startLoading", "key", "currentState", "newState", "progress", "startTime", "Date", "updateGlobalLoading", "stopLoading", "updateProgress", "Math", "max", "min", "isLoadingKey", "observer", "subscribe", "state", "isLoadingKeySync", "hasLoading", "Object", "values", "some", "wrapWithLoading", "source", "pipe", "KEYS", "LOGIN", "LOGOUT", "REGISTER", "PROFILE_LOAD", "PRODUCTS_LOAD", "CART_LOAD", "SEARCH", "BRANDS_LOAD", "GLOBAL", "i0", "ɵɵinject", "i1", "LoadingController", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\loading.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { LoadingController } from '@ionic/angular';\nimport { BehaviorSubject, Observable, finalize } from 'rxjs';\n\nexport interface LoadingState {\n  [key: string]: {\n    isLoading: boolean;\n    message?: string;\n    progress?: number;\n    startTime?: Date;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class LoadingService {\n  private loadingSubject = new BehaviorSubject<boolean>(false);\n  private loadingCount = 0;\n  private currentLoading: HTMLIonLoadingElement | null = null;\n\n  // Enhanced loading state management\n  private loadingStateSubject = new BehaviorSubject<LoadingState>({});\n  public loadingState$ = this.loadingStateSubject.asObservable();\n\n  public isLoading$ = this.loadingSubject.asObservable();\n\n  constructor(private loadingController: LoadingController) {}\n\n  async show(message: string = 'Loading...', duration?: number): Promise<void> {\n    this.loadingCount++;\n    \n    if (this.loadingCount === 1) {\n      this.loadingSubject.next(true);\n      \n      this.currentLoading = await this.loadingController.create({\n        message,\n        duration,\n        spinner: 'crescent',\n        cssClass: 'custom-loading'\n      });\n      \n      await this.currentLoading.present();\n    }\n  }\n\n  async hide(): Promise<void> {\n    if (this.loadingCount > 0) {\n      this.loadingCount--;\n    }\n    \n    if (this.loadingCount === 0) {\n      this.loadingSubject.next(false);\n      \n      if (this.currentLoading) {\n        await this.currentLoading.dismiss();\n        this.currentLoading = null;\n      }\n    }\n  }\n\n  async hideAll(): Promise<void> {\n    this.loadingCount = 0;\n    this.loadingSubject.next(false);\n    \n    if (this.currentLoading) {\n      await this.currentLoading.dismiss();\n      this.currentLoading = null;\n    }\n  }\n\n  isLoading(): boolean {\n    return this.loadingSubject.value;\n  }\n\n  // Enhanced loading methods\n  startLoading(key: string, message?: string): void {\n    const currentState = this.loadingStateSubject.value;\n    const newState = {\n      ...currentState,\n      [key]: {\n        isLoading: true,\n        message,\n        progress: 0,\n        startTime: new Date()\n      }\n    };\n\n    this.loadingStateSubject.next(newState);\n    this.updateGlobalLoading();\n  }\n\n  stopLoading(key: string): void {\n    const currentState = this.loadingStateSubject.value;\n    const newState = { ...currentState };\n\n    if (newState[key]) {\n      delete newState[key];\n    }\n\n    this.loadingStateSubject.next(newState);\n    this.updateGlobalLoading();\n  }\n\n  updateProgress(key: string, progress: number, message?: string): void {\n    const currentState = this.loadingStateSubject.value;\n\n    if (currentState[key]) {\n      const newState = {\n        ...currentState,\n        [key]: {\n          ...currentState[key],\n          progress: Math.max(0, Math.min(100, progress)),\n          message: message || currentState[key].message\n        }\n      };\n\n      this.loadingStateSubject.next(newState);\n    }\n  }\n\n  isLoadingKey(key: string): Observable<boolean> {\n    return new Observable(observer => {\n      this.loadingState$.subscribe(state => {\n        observer.next(!!state[key]?.isLoading);\n      });\n    });\n  }\n\n  isLoadingKeySync(key: string): boolean {\n    const currentState = this.loadingStateSubject.value;\n    return !!currentState[key]?.isLoading;\n  }\n\n  private updateGlobalLoading(): void {\n    const currentState = this.loadingStateSubject.value;\n    const hasLoading = Object.values(currentState).some(state => state.isLoading);\n    this.loadingSubject.next(hasLoading);\n  }\n\n  wrapWithLoading<T>(source: Observable<T>, key: string, message?: string): Observable<T> {\n    this.startLoading(key, message);\n    return source.pipe(finalize(() => this.stopLoading(key)));\n  }\n\n  // Predefined loading keys\n  static readonly KEYS = {\n    LOGIN: 'auth.login',\n    LOGOUT: 'auth.logout',\n    REGISTER: 'auth.register',\n    PROFILE_LOAD: 'user.profile.load',\n    PRODUCTS_LOAD: 'shop.products.load',\n    CART_LOAD: 'cart.load',\n    SEARCH: 'search.execute',\n    BRANDS_LOAD: 'shop.brands.load',\n    GLOBAL: 'global'\n  } as const;\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,MAAM;;;AAc5D,OAAM,MAAOC,cAAc;EAWzBC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IAV7B,KAAAC,cAAc,GAAG,IAAIN,eAAe,CAAU,KAAK,CAAC;IACpD,KAAAO,YAAY,GAAG,CAAC;IAChB,KAAAC,cAAc,GAAiC,IAAI;IAE3D;IACQ,KAAAC,mBAAmB,GAAG,IAAIT,eAAe,CAAe,EAAE,CAAC;IAC5D,KAAAU,aAAa,GAAG,IAAI,CAACD,mBAAmB,CAACE,YAAY,EAAE;IAEvD,KAAAC,UAAU,GAAG,IAAI,CAACN,cAAc,CAACK,YAAY,EAAE;EAEK;EAErDE,IAAIA,CAAA,EAAkD;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,YAAjDC,OAAA,GAAkB,YAAY,EAAEC,QAAiB;MAC1DH,KAAI,CAACP,YAAY,EAAE;MAEnB,IAAIO,KAAI,CAACP,YAAY,KAAK,CAAC,EAAE;QAC3BO,KAAI,CAACR,cAAc,CAACY,IAAI,CAAC,IAAI,CAAC;QAE9BJ,KAAI,CAACN,cAAc,SAASM,KAAI,CAACT,iBAAiB,CAACc,MAAM,CAAC;UACxDH,OAAO;UACPC,QAAQ;UACRG,OAAO,EAAE,UAAU;UACnBC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMP,KAAI,CAACN,cAAc,CAACc,OAAO,EAAE;;IACpC,GAAAC,KAAA,OAAAC,SAAA;EACH;EAEMC,IAAIA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MACR,IAAIW,MAAI,CAACnB,YAAY,GAAG,CAAC,EAAE;QACzBmB,MAAI,CAACnB,YAAY,EAAE;;MAGrB,IAAImB,MAAI,CAACnB,YAAY,KAAK,CAAC,EAAE;QAC3BmB,MAAI,CAACpB,cAAc,CAACY,IAAI,CAAC,KAAK,CAAC;QAE/B,IAAIQ,MAAI,CAAClB,cAAc,EAAE;UACvB,MAAMkB,MAAI,CAAClB,cAAc,CAACmB,OAAO,EAAE;UACnCD,MAAI,CAAClB,cAAc,GAAG,IAAI;;;IAE7B;EACH;EAEMoB,OAAOA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAd,iBAAA;MACXc,MAAI,CAACtB,YAAY,GAAG,CAAC;MACrBsB,MAAI,CAACvB,cAAc,CAACY,IAAI,CAAC,KAAK,CAAC;MAE/B,IAAIW,MAAI,CAACrB,cAAc,EAAE;QACvB,MAAMqB,MAAI,CAACrB,cAAc,CAACmB,OAAO,EAAE;QACnCE,MAAI,CAACrB,cAAc,GAAG,IAAI;;IAC3B;EACH;EAEAsB,SAASA,CAAA;IACP,OAAO,IAAI,CAACxB,cAAc,CAACyB,KAAK;EAClC;EAEA;EACAC,YAAYA,CAACC,GAAW,EAAEjB,OAAgB;IACxC,MAAMkB,YAAY,GAAG,IAAI,CAACzB,mBAAmB,CAACsB,KAAK;IACnD,MAAMI,QAAQ,GAAG;MACf,GAAGD,YAAY;MACf,CAACD,GAAG,GAAG;QACLH,SAAS,EAAE,IAAI;QACfd,OAAO;QACPoB,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,IAAIC,IAAI;;KAEtB;IAED,IAAI,CAAC7B,mBAAmB,CAACS,IAAI,CAACiB,QAAQ,CAAC;IACvC,IAAI,CAACI,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAACP,GAAW;IACrB,MAAMC,YAAY,GAAG,IAAI,CAACzB,mBAAmB,CAACsB,KAAK;IACnD,MAAMI,QAAQ,GAAG;MAAE,GAAGD;IAAY,CAAE;IAEpC,IAAIC,QAAQ,CAACF,GAAG,CAAC,EAAE;MACjB,OAAOE,QAAQ,CAACF,GAAG,CAAC;;IAGtB,IAAI,CAACxB,mBAAmB,CAACS,IAAI,CAACiB,QAAQ,CAAC;IACvC,IAAI,CAACI,mBAAmB,EAAE;EAC5B;EAEAE,cAAcA,CAACR,GAAW,EAAEG,QAAgB,EAAEpB,OAAgB;IAC5D,MAAMkB,YAAY,GAAG,IAAI,CAACzB,mBAAmB,CAACsB,KAAK;IAEnD,IAAIG,YAAY,CAACD,GAAG,CAAC,EAAE;MACrB,MAAME,QAAQ,GAAG;QACf,GAAGD,YAAY;QACf,CAACD,GAAG,GAAG;UACL,GAAGC,YAAY,CAACD,GAAG,CAAC;UACpBG,QAAQ,EAAEM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAER,QAAQ,CAAC,CAAC;UAC9CpB,OAAO,EAAEA,OAAO,IAAIkB,YAAY,CAACD,GAAG,CAAC,CAACjB;;OAEzC;MAED,IAAI,CAACP,mBAAmB,CAACS,IAAI,CAACiB,QAAQ,CAAC;;EAE3C;EAEAU,YAAYA,CAACZ,GAAW;IACtB,OAAO,IAAIhC,UAAU,CAAC6C,QAAQ,IAAG;MAC/B,IAAI,CAACpC,aAAa,CAACqC,SAAS,CAACC,KAAK,IAAG;QACnCF,QAAQ,CAAC5B,IAAI,CAAC,CAAC,CAAC8B,KAAK,CAACf,GAAG,CAAC,EAAEH,SAAS,CAAC;MACxC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAmB,gBAAgBA,CAAChB,GAAW;IAC1B,MAAMC,YAAY,GAAG,IAAI,CAACzB,mBAAmB,CAACsB,KAAK;IACnD,OAAO,CAAC,CAACG,YAAY,CAACD,GAAG,CAAC,EAAEH,SAAS;EACvC;EAEQS,mBAAmBA,CAAA;IACzB,MAAML,YAAY,GAAG,IAAI,CAACzB,mBAAmB,CAACsB,KAAK;IACnD,MAAMmB,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAClB,YAAY,CAAC,CAACmB,IAAI,CAACL,KAAK,IAAIA,KAAK,CAAClB,SAAS,CAAC;IAC7E,IAAI,CAACxB,cAAc,CAACY,IAAI,CAACgC,UAAU,CAAC;EACtC;EAEAI,eAAeA,CAAIC,MAAqB,EAAEtB,GAAW,EAAEjB,OAAgB;IACrE,IAAI,CAACgB,YAAY,CAACC,GAAG,EAAEjB,OAAO,CAAC;IAC/B,OAAOuC,MAAM,CAACC,IAAI,CAACtD,QAAQ,CAAC,MAAM,IAAI,CAACsC,WAAW,CAACP,GAAG,CAAC,CAAC,CAAC;EAC3D;EAEA;;IACgB,KAAAwB,IAAI,GAAG;MACrBC,KAAK,EAAE,YAAY;MACnBC,MAAM,EAAE,aAAa;MACrBC,QAAQ,EAAE,eAAe;MACzBC,YAAY,EAAE,mBAAmB;MACjCC,aAAa,EAAE,oBAAoB;MACnCC,SAAS,EAAE,WAAW;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,WAAW,EAAE,kBAAkB;MAC/BC,MAAM,EAAE;KACA;EAAC;;;uBA5IA/D,cAAc,EAAAgE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;aAAdnE,cAAc;MAAAoE,OAAA,EAAdpE,cAAc,CAAAqE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}