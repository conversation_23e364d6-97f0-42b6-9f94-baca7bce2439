.user-avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  background: var(--ion-color-medium);
  color: white;
  font-weight: 600;
  flex-shrink: 0;

  &.small {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }

  &.medium {
    width: 48px;
    height: 48px;
    font-size: 1rem;
  }

  &.large {
    width: 64px;
    height: 64px;
    font-size: 1.25rem;
  }

  &.xlarge {
    width: 96px;
    height: 96px;
    font-size: 1.5rem;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;

    &.hidden {
      display: none;
    }
  }

  .initials {
    user-select: none;
  }

  .status-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 25%;
    height: 25%;
    border-radius: 50%;
    border: 2px solid white;

    &.online {
      background: var(--ion-color-success);
    }

    &.offline {
      background: var(--ion-color-medium);
    }
  }
}

// Size-specific status indicators
.user-avatar.small .status-indicator {
  width: 8px;
  height: 8px;
  border-width: 1px;
}

.user-avatar.medium .status-indicator {
  width: 12px;
  height: 12px;
  border-width: 2px;
}

.user-avatar.large .status-indicator {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.user-avatar.xlarge .status-indicator {
  width: 24px;
  height: 24px;
  border-width: 3px;
}
