{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PaymentService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n    this.paymentStatusSubject = new BehaviorSubject('idle');\n    this.paymentStatus$ = this.paymentStatusSubject.asObservable();\n    this.loadRazorpayScript();\n  }\n  loadRazorpayScript() {\n    return new Promise(resolve => {\n      if (typeof Razorpay !== 'undefined') {\n        resolve(true);\n        return;\n      }\n      const script = document.createElement('script');\n      script.src = 'https://checkout.razorpay.com/v1/checkout.js';\n      script.onload = () => resolve(true);\n      script.onerror = () => resolve(false);\n      document.head.appendChild(script);\n    });\n  }\n  createOrder(amount, currency = 'INR') {\n    const orderData = {\n      amount: amount * 100,\n      currency: currency\n    };\n    return this.http.post(`${this.apiUrl}/payment/create-order`, orderData);\n  }\n  verifyPayment(paymentData) {\n    return this.http.post(`${this.apiUrl}/payment/verify`, paymentData);\n  }\n  initiatePayment(orderData, userDetails) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.paymentStatusSubject.next('processing');\n        // Create order on backend\n        const order = yield _this.createOrder(orderData.amount).toPromise();\n        if (!order) {\n          throw new Error('Failed to create order');\n        }\n        // Prepare Razorpay options\n        const options = {\n          key: environment.razorpayKeyId,\n          amount: order.amount,\n          currency: order.currency,\n          name: 'DFashion',\n          description: 'Fashion Purchase',\n          order_id: order.id,\n          handler: response => {\n            _this.handlePaymentSuccess(response, orderData);\n          },\n          prefill: {\n            name: userDetails.name,\n            email: userDetails.email,\n            contact: userDetails.phone\n          },\n          theme: {\n            color: '#667eea'\n          },\n          modal: {\n            ondismiss: () => {\n              _this.paymentStatusSubject.next('cancelled');\n            }\n          }\n        };\n        // Open Razorpay checkout\n        const rzp = new Razorpay(options);\n        rzp.open();\n      } catch (error) {\n        console.error('Payment initiation failed:', error);\n        _this.paymentStatusSubject.next('failed');\n        throw error;\n      }\n    })();\n  }\n  handlePaymentSuccess(response, orderData) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Verify payment on backend\n        const verificationData = {\n          razorpay_order_id: response.razorpay_order_id,\n          razorpay_payment_id: response.razorpay_payment_id,\n          razorpay_signature: response.razorpay_signature\n        };\n        const verification = yield _this2.verifyPayment(verificationData).toPromise();\n        if (verification) {\n          _this2.paymentStatusSubject.next('success');\n          // Generate and send bill\n          yield _this2.generateAndSendBill({\n            ...orderData,\n            paymentId: response.razorpay_payment_id,\n            orderId: response.razorpay_order_id\n          });\n        } else {\n          _this2.paymentStatusSubject.next('failed');\n        }\n      } catch (error) {\n        console.error('Payment verification failed:', error);\n        _this2.paymentStatusSubject.next('failed');\n      }\n    })();\n  }\n  generateAndSendBill(orderData) {\n    return _asyncToGenerator(function* () {\n      try {\n        // This will be implemented with the bill service\n        console.log('Generating bill for order:', orderData);\n      } catch (error) {\n        console.error('Bill generation failed:', error);\n      }\n    })();\n  }\n  // Payment method selection\n  getPaymentMethods() {\n    return [{\n      id: 'razorpay',\n      name: 'Credit/Debit Card',\n      description: 'Pay securely with your card',\n      icon: 'fas fa-credit-card',\n      enabled: true\n    }, {\n      id: 'upi',\n      name: 'UPI',\n      description: 'Pay with UPI apps',\n      icon: 'fas fa-mobile-alt',\n      enabled: true\n    }, {\n      id: 'netbanking',\n      name: 'Net Banking',\n      description: 'Pay with your bank account',\n      icon: 'fas fa-university',\n      enabled: true\n    }, {\n      id: 'wallet',\n      name: 'Wallet',\n      description: 'Pay with digital wallets',\n      icon: 'fas fa-wallet',\n      enabled: true\n    }, {\n      id: 'cod',\n      name: 'Cash on Delivery',\n      description: 'Pay when you receive',\n      icon: 'fas fa-money-bill-wave',\n      enabled: true\n    }];\n  }\n  // Reset payment status\n  resetPaymentStatus() {\n    this.paymentStatusSubject.next('idle');\n  }\n  // Get current payment status\n  getCurrentPaymentStatus() {\n    return this.paymentStatusSubject.value;\n  }\n  static {\n    this.ɵfac = function PaymentService_Factory(t) {\n      return new (t || PaymentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PaymentService,\n      factory: PaymentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "environment", "PaymentService", "constructor", "http", "apiUrl", "paymentStatusSubject", "paymentStatus$", "asObservable", "loadRazorpayScript", "Promise", "resolve", "Razorpay", "script", "document", "createElement", "src", "onload", "onerror", "head", "append<PERSON><PERSON><PERSON>", "createOrder", "amount", "currency", "orderData", "post", "verifyPayment", "paymentData", "initiatePayment", "userDetails", "_this", "_asyncToGenerator", "next", "order", "to<PERSON>romise", "Error", "options", "key", "razorpayKeyId", "name", "description", "order_id", "id", "handler", "response", "handlePaymentSuccess", "prefill", "email", "contact", "phone", "theme", "color", "modal", "ondismiss", "rzp", "open", "error", "console", "_this2", "verificationData", "razorpay_order_id", "razorpay_payment_id", "razorpay_signature", "verification", "generateAndSendBill", "paymentId", "orderId", "log", "getPaymentMethods", "icon", "enabled", "resetPaymentStatus", "getCurrentPaymentStatus", "value", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\payment.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\n\ndeclare var Razorpay: any;\n\nexport interface PaymentOrder {\n  id: string;\n  amount: number;\n  currency: string;\n  receipt: string;\n  status: string;\n}\n\nexport interface PaymentOptions {\n  key: string;\n  amount: number;\n  currency: string;\n  name: string;\n  description: string;\n  order_id: string;\n  handler: (response: any) => void;\n  prefill: {\n    name: string;\n    email: string;\n    contact: string;\n  };\n  theme: {\n    color: string;\n  };\n  modal: {\n    ondismiss: () => void;\n  };\n}\n\nexport interface PaymentVerification {\n  razorpay_order_id: string;\n  razorpay_payment_id: string;\n  razorpay_signature: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PaymentService {\n  private apiUrl = environment.apiUrl;\n  private paymentStatusSubject = new BehaviorSubject<string>('idle');\n  public paymentStatus$ = this.paymentStatusSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    this.loadRazorpayScript();\n  }\n\n  private loadRazorpayScript(): Promise<boolean> {\n    return new Promise((resolve) => {\n      if (typeof Razorpay !== 'undefined') {\n        resolve(true);\n        return;\n      }\n\n      const script = document.createElement('script');\n      script.src = 'https://checkout.razorpay.com/v1/checkout.js';\n      script.onload = () => resolve(true);\n      script.onerror = () => resolve(false);\n      document.head.appendChild(script);\n    });\n  }\n\n  createOrder(amount: number, currency: string = 'INR'): Observable<PaymentOrder> {\n    const orderData = {\n      amount: amount * 100, // Convert to paise\n      currency: currency\n    };\n    return this.http.post<PaymentOrder>(`${this.apiUrl}/payment/create-order`, orderData);\n  }\n\n  verifyPayment(paymentData: PaymentVerification): Observable<any> {\n    return this.http.post(`${this.apiUrl}/payment/verify`, paymentData);\n  }\n\n  async initiatePayment(orderData: any, userDetails: any): Promise<void> {\n    try {\n      this.paymentStatusSubject.next('processing');\n      \n      // Create order on backend\n      const order = await this.createOrder(orderData.amount).toPromise();\n      \n      if (!order) {\n        throw new Error('Failed to create order');\n      }\n\n      // Prepare Razorpay options\n      const options: PaymentOptions = {\n        key: environment.razorpayKeyId,\n        amount: order.amount,\n        currency: order.currency,\n        name: 'DFashion',\n        description: 'Fashion Purchase',\n        order_id: order.id,\n        handler: (response: any) => {\n          this.handlePaymentSuccess(response, orderData);\n        },\n        prefill: {\n          name: userDetails.name,\n          email: userDetails.email,\n          contact: userDetails.phone\n        },\n        theme: {\n          color: '#667eea'\n        },\n        modal: {\n          ondismiss: () => {\n            this.paymentStatusSubject.next('cancelled');\n          }\n        }\n      };\n\n      // Open Razorpay checkout\n      const rzp = new Razorpay(options);\n      rzp.open();\n\n    } catch (error) {\n      console.error('Payment initiation failed:', error);\n      this.paymentStatusSubject.next('failed');\n      throw error;\n    }\n  }\n\n  private async handlePaymentSuccess(response: any, orderData: any): Promise<void> {\n    try {\n      // Verify payment on backend\n      const verificationData: PaymentVerification = {\n        razorpay_order_id: response.razorpay_order_id,\n        razorpay_payment_id: response.razorpay_payment_id,\n        razorpay_signature: response.razorpay_signature\n      };\n\n      const verification = await this.verifyPayment(verificationData).toPromise();\n      \n      if (verification) {\n        this.paymentStatusSubject.next('success');\n        \n        // Generate and send bill\n        await this.generateAndSendBill({\n          ...orderData,\n          paymentId: response.razorpay_payment_id,\n          orderId: response.razorpay_order_id\n        });\n      } else {\n        this.paymentStatusSubject.next('failed');\n      }\n    } catch (error) {\n      console.error('Payment verification failed:', error);\n      this.paymentStatusSubject.next('failed');\n    }\n  }\n\n  private async generateAndSendBill(orderData: any): Promise<void> {\n    try {\n      // This will be implemented with the bill service\n      console.log('Generating bill for order:', orderData);\n    } catch (error) {\n      console.error('Bill generation failed:', error);\n    }\n  }\n\n  // Payment method selection\n  getPaymentMethods(): any[] {\n    return [\n      {\n        id: 'razorpay',\n        name: 'Credit/Debit Card',\n        description: 'Pay securely with your card',\n        icon: 'fas fa-credit-card',\n        enabled: true\n      },\n      {\n        id: 'upi',\n        name: 'UPI',\n        description: 'Pay with UPI apps',\n        icon: 'fas fa-mobile-alt',\n        enabled: true\n      },\n      {\n        id: 'netbanking',\n        name: 'Net Banking',\n        description: 'Pay with your bank account',\n        icon: 'fas fa-university',\n        enabled: true\n      },\n      {\n        id: 'wallet',\n        name: 'Wallet',\n        description: 'Pay with digital wallets',\n        icon: 'fas fa-wallet',\n        enabled: true\n      },\n      {\n        id: 'cod',\n        name: 'Cash on Delivery',\n        description: 'Pay when you receive',\n        icon: 'fas fa-money-bill-wave',\n        enabled: true\n      }\n    ];\n  }\n\n  // Reset payment status\n  resetPaymentStatus(): void {\n    this.paymentStatusSubject.next('idle');\n  }\n\n  // Get current payment status\n  getCurrentPaymentStatus(): string {\n    return this.paymentStatusSubject.value;\n  }\n}\n"], "mappings": ";AAEA,SAAqBA,eAAe,QAAQ,MAAM;AAClD,SAASC,WAAW,QAAQ,mCAAmC;;;AA0C/D,OAAM,MAAOC,cAAc;EAKzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;IAC3B,KAAAC,oBAAoB,GAAG,IAAIN,eAAe,CAAS,MAAM,CAAC;IAC3D,KAAAO,cAAc,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;IAG9D,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;MAC7B,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;QACnCD,OAAO,CAAC,IAAI,CAAC;QACb;;MAGF,MAAME,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,8CAA8C;MAC3DH,MAAM,CAACI,MAAM,GAAG,MAAMN,OAAO,CAAC,IAAI,CAAC;MACnCE,MAAM,CAACK,OAAO,GAAG,MAAMP,OAAO,CAAC,KAAK,CAAC;MACrCG,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAQ,WAAWA,CAACC,MAAc,EAAEC,QAAA,GAAmB,KAAK;IAClD,MAAMC,SAAS,GAAG;MAChBF,MAAM,EAAEA,MAAM,GAAG,GAAG;MACpBC,QAAQ,EAAEA;KACX;IACD,OAAO,IAAI,CAACnB,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACpB,MAAM,uBAAuB,EAAEmB,SAAS,CAAC;EACvF;EAEAE,aAAaA,CAACC,WAAgC;IAC5C,OAAO,IAAI,CAACvB,IAAI,CAACqB,IAAI,CAAC,GAAG,IAAI,CAACpB,MAAM,iBAAiB,EAAEsB,WAAW,CAAC;EACrE;EAEMC,eAAeA,CAACJ,SAAc,EAAEK,WAAgB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpD,IAAI;QACFD,KAAI,CAACxB,oBAAoB,CAAC0B,IAAI,CAAC,YAAY,CAAC;QAE5C;QACA,MAAMC,KAAK,SAASH,KAAI,CAACT,WAAW,CAACG,SAAS,CAACF,MAAM,CAAC,CAACY,SAAS,EAAE;QAElE,IAAI,CAACD,KAAK,EAAE;UACV,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;;QAG3C;QACA,MAAMC,OAAO,GAAmB;UAC9BC,GAAG,EAAEpC,WAAW,CAACqC,aAAa;UAC9BhB,MAAM,EAAEW,KAAK,CAACX,MAAM;UACpBC,QAAQ,EAAEU,KAAK,CAACV,QAAQ;UACxBgB,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,kBAAkB;UAC/BC,QAAQ,EAAER,KAAK,CAACS,EAAE;UAClBC,OAAO,EAAGC,QAAa,IAAI;YACzBd,KAAI,CAACe,oBAAoB,CAACD,QAAQ,EAAEpB,SAAS,CAAC;UAChD,CAAC;UACDsB,OAAO,EAAE;YACPP,IAAI,EAAEV,WAAW,CAACU,IAAI;YACtBQ,KAAK,EAAElB,WAAW,CAACkB,KAAK;YACxBC,OAAO,EAAEnB,WAAW,CAACoB;WACtB;UACDC,KAAK,EAAE;YACLC,KAAK,EAAE;WACR;UACDC,KAAK,EAAE;YACLC,SAAS,EAAEA,CAAA,KAAK;cACdvB,KAAI,CAACxB,oBAAoB,CAAC0B,IAAI,CAAC,WAAW,CAAC;YAC7C;;SAEH;QAED;QACA,MAAMsB,GAAG,GAAG,IAAI1C,QAAQ,CAACwB,OAAO,CAAC;QACjCkB,GAAG,CAACC,IAAI,EAAE;OAEX,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD1B,KAAI,CAACxB,oBAAoB,CAAC0B,IAAI,CAAC,QAAQ,CAAC;QACxC,MAAMwB,KAAK;;IACZ;EACH;EAEcX,oBAAoBA,CAACD,QAAa,EAAEpB,SAAc;IAAA,IAAAkC,MAAA;IAAA,OAAA3B,iBAAA;MAC9D,IAAI;QACF;QACA,MAAM4B,gBAAgB,GAAwB;UAC5CC,iBAAiB,EAAEhB,QAAQ,CAACgB,iBAAiB;UAC7CC,mBAAmB,EAAEjB,QAAQ,CAACiB,mBAAmB;UACjDC,kBAAkB,EAAElB,QAAQ,CAACkB;SAC9B;QAED,MAAMC,YAAY,SAASL,MAAI,CAAChC,aAAa,CAACiC,gBAAgB,CAAC,CAACzB,SAAS,EAAE;QAE3E,IAAI6B,YAAY,EAAE;UAChBL,MAAI,CAACpD,oBAAoB,CAAC0B,IAAI,CAAC,SAAS,CAAC;UAEzC;UACA,MAAM0B,MAAI,CAACM,mBAAmB,CAAC;YAC7B,GAAGxC,SAAS;YACZyC,SAAS,EAAErB,QAAQ,CAACiB,mBAAmB;YACvCK,OAAO,EAAEtB,QAAQ,CAACgB;WACnB,CAAC;SACH,MAAM;UACLF,MAAI,CAACpD,oBAAoB,CAAC0B,IAAI,CAAC,QAAQ,CAAC;;OAE3C,CAAC,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDE,MAAI,CAACpD,oBAAoB,CAAC0B,IAAI,CAAC,QAAQ,CAAC;;IACzC;EACH;EAEcgC,mBAAmBA,CAACxC,SAAc;IAAA,OAAAO,iBAAA;MAC9C,IAAI;QACF;QACA0B,OAAO,CAACU,GAAG,CAAC,4BAA4B,EAAE3C,SAAS,CAAC;OACrD,CAAC,OAAOgC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;IAChD;EACH;EAEA;EACAY,iBAAiBA,CAAA;IACf,OAAO,CACL;MACE1B,EAAE,EAAE,UAAU;MACdH,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,6BAA6B;MAC1C6B,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE;KACV,EACD;MACE5B,EAAE,EAAE,KAAK;MACTH,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,mBAAmB;MAChC6B,IAAI,EAAE,mBAAmB;MACzBC,OAAO,EAAE;KACV,EACD;MACE5B,EAAE,EAAE,YAAY;MAChBH,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE,4BAA4B;MACzC6B,IAAI,EAAE,mBAAmB;MACzBC,OAAO,EAAE;KACV,EACD;MACE5B,EAAE,EAAE,QAAQ;MACZH,IAAI,EAAE,QAAQ;MACdC,WAAW,EAAE,0BAA0B;MACvC6B,IAAI,EAAE,eAAe;MACrBC,OAAO,EAAE;KACV,EACD;MACE5B,EAAE,EAAE,KAAK;MACTH,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,sBAAsB;MACnC6B,IAAI,EAAE,wBAAwB;MAC9BC,OAAO,EAAE;KACV,CACF;EACH;EAEA;EACAC,kBAAkBA,CAAA;IAChB,IAAI,CAACjE,oBAAoB,CAAC0B,IAAI,CAAC,MAAM,CAAC;EACxC;EAEA;EACAwC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAClE,oBAAoB,CAACmE,KAAK;EACxC;;;uBA3KWvE,cAAc,EAAAwE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd3E,cAAc;MAAA4E,OAAA,EAAd5E,cAAc,CAAA6E,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}