{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RoleAccessDirective, RoleClassDirective, RoleDisableDirective } from '../../../../shared/directives/role-access.directive';\nlet ProfileComponent = class ProfileComponent {\n  constructor(authService, roleAccessService) {\n    this.authService = authService;\n    this.roleAccessService = roleAccessService;\n    this.currentUser = null;\n    this.userRole = '';\n    this.settingsVisibility = {};\n    this.availableModules = [];\n    this.editableModules = [];\n    // Profile sections based on role\n    this.profileSections = [];\n  }\n  ngOnInit() {\n    this.loadUserProfile();\n    this.setupRoleBasedAccess();\n  }\n  loadUserProfile() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.userRole = this.roleAccessService.getCurrentUserRole();\n      this.setupRoleBasedAccess();\n    });\n  }\n  setupRoleBasedAccess() {\n    this.settingsVisibility = this.roleAccessService.getSettingsVisibility();\n    this.availableModules = this.roleAccessService.getAvailableModules();\n    this.editableModules = this.roleAccessService.getEditableModules();\n    this.setupProfileSections();\n  }\n  setupProfileSections() {\n    this.profileSections = [{\n      id: 'basic',\n      title: 'Basic Information',\n      icon: 'fas fa-user',\n      visible: true,\n      editable: this.roleAccessService.canWrite('profile')\n    }, {\n      id: 'security',\n      title: 'Security Settings',\n      icon: 'fas fa-shield-alt',\n      visible: this.settingsVisibility['security'],\n      editable: this.roleAccessService.canWrite('profile')\n    }, {\n      id: 'notifications',\n      title: 'Notification Preferences',\n      icon: 'fas fa-bell',\n      visible: this.settingsVisibility['notifications'],\n      editable: this.roleAccessService.canWrite('profile')\n    }, {\n      id: 'orders',\n      title: 'Order History',\n      icon: 'fas fa-shopping-bag',\n      visible: this.settingsVisibility['orders'],\n      editable: false\n    }, {\n      id: 'vendor',\n      title: 'Vendor Dashboard',\n      icon: 'fas fa-store',\n      visible: this.settingsVisibility['vendor'],\n      editable: this.roleAccessService.canWrite('vendor')\n    }, {\n      id: 'analytics',\n      title: 'Analytics & Reports',\n      icon: 'fas fa-chart-bar',\n      visible: this.roleAccessService.canRead('analytics'),\n      editable: this.roleAccessService.canWrite('analytics')\n    }, {\n      id: 'users',\n      title: 'User Management',\n      icon: 'fas fa-users',\n      visible: this.roleAccessService.canRead('users'),\n      editable: this.roleAccessService.canWrite('users')\n    }, {\n      id: 'system',\n      title: 'System Settings',\n      icon: 'fas fa-cogs',\n      visible: this.roleAccessService.canRead('system'),\n      editable: this.roleAccessService.canWrite('system')\n    }].filter(section => section.visible);\n  }\n  // Role-based utility methods\n  canEditProfile() {\n    return this.roleAccessService.canWrite('profile');\n  }\n  canViewAnalytics() {\n    return this.roleAccessService.canRead('analytics');\n  }\n  canManageUsers() {\n    return this.roleAccessService.canRead('users');\n  }\n  canAccessVendorFeatures() {\n    return this.roleAccessService.isSeller() || this.roleAccessService.hasElevatedPrivileges();\n  }\n  canAccessAdminFeatures() {\n    return this.roleAccessService.hasElevatedPrivileges();\n  }\n  getRoleDisplayName() {\n    const roleInfo = this.roleAccessService.getRoleInfo();\n    return roleInfo?.name || this.userRole;\n  }\n  getRoleColor() {\n    const colors = {\n      'buyer': '#28a745',\n      'seller': '#007bff',\n      'admin': '#fd7e14',\n      'super_admin': '#dc3545'\n    };\n    return colors[this.userRole] || '#6c757d';\n  }\n  // Navigation methods\n  navigateToSection(sectionId) {\n    // Implement navigation logic based on section\n    console.log('Navigating to section:', sectionId);\n  }\n  // Profile update methods\n  updateProfile() {\n    if (!this.canEditProfile()) {\n      alert('You do not have permission to edit your profile');\n      return;\n    }\n    // Implement profile update logic\n    console.log('Updating profile...');\n  }\n  changePassword() {\n    if (!this.canEditProfile()) {\n      alert('You do not have permission to change your password');\n      return;\n    }\n    // Implement password change logic\n    console.log('Changing password...');\n  }\n  getSectionDescription(sectionId) {\n    const descriptions = {\n      'basic': 'Manage your personal information and contact details',\n      'security': 'Update password and security settings',\n      'notifications': 'Configure your notification preferences',\n      'orders': 'View your order history and track shipments',\n      'vendor': 'Manage your seller account and business information',\n      'analytics': 'View performance metrics and reports',\n      'users': 'Manage user accounts and permissions',\n      'system': 'Configure system-wide settings and preferences'\n    };\n    return descriptions[sectionId] || 'Manage this section';\n  }\n};\nProfileComponent = __decorate([Component({\n  selector: 'app-profile',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RoleAccessDirective, RoleClassDirective, RoleDisableDirective],\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.scss']\n})], ProfileComponent);\nexport { ProfileComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "RoleAccessDirective", "RoleClassDirective", "RoleDisableDirective", "ProfileComponent", "constructor", "authService", "roleAccessService", "currentUser", "userRole", "settingsVisibility", "availableModules", "editableModules", "profileSections", "ngOnInit", "loadUserProfile", "setupRoleBasedAccess", "currentUser$", "subscribe", "user", "getCurrentUserRole", "getSettingsVisibility", "getAvailableModules", "getEditableModules", "setupProfileSections", "id", "title", "icon", "visible", "editable", "canWrite", "canRead", "filter", "section", "canEditProfile", "canViewAnalytics", "canManageUsers", "canAccessVendorFeatures", "isSeller", "hasElevatedPrivileges", "canAccessAdminFeatures", "getRoleDisplayName", "roleInfo", "getRoleInfo", "name", "getRoleColor", "colors", "navigateToSection", "sectionId", "console", "log", "updateProfile", "alert", "changePassword", "getSectionDescription", "descriptions", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\Fahion\\DFashion\\frontend\\src\\app\\features\\profile\\pages\\profile\\profile.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { RoleAccessService } from '../../../../core/services/role-access.service';\nimport { RoleAccessDirective, RoleClassDirective, RoleDisableDirective } from '../../../../shared/directives/role-access.directive';\n\n@Component({\n  selector: 'app-profile',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RoleAccessDirective, RoleClassDirective, RoleDisableDirective],\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.scss']\n})\nexport class ProfileComponent implements OnInit {\n  currentUser: any = null;\n  userRole: string = '';\n  settingsVisibility: { [key: string]: boolean } = {};\n  availableModules: string[] = [];\n  editableModules: string[] = [];\n\n  // Profile sections based on role\n  profileSections: any[] = [];\n\n  constructor(\n    private authService: AuthService,\n    private roleAccessService: RoleAccessService\n  ) {}\n\n  ngOnInit() {\n    this.loadUserProfile();\n    this.setupRoleBasedAccess();\n  }\n\n  loadUserProfile() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.userRole = this.roleAccessService.getCurrentUserRole();\n      this.setupRoleBasedAccess();\n    });\n  }\n\n  setupRoleBasedAccess() {\n    this.settingsVisibility = this.roleAccessService.getSettingsVisibility();\n    this.availableModules = this.roleAccessService.getAvailableModules();\n    this.editableModules = this.roleAccessService.getEditableModules();\n    this.setupProfileSections();\n  }\n\n  setupProfileSections() {\n    this.profileSections = [\n      {\n        id: 'basic',\n        title: 'Basic Information',\n        icon: 'fas fa-user',\n        visible: true,\n        editable: this.roleAccessService.canWrite('profile')\n      },\n      {\n        id: 'security',\n        title: 'Security Settings',\n        icon: 'fas fa-shield-alt',\n        visible: this.settingsVisibility['security'],\n        editable: this.roleAccessService.canWrite('profile')\n      },\n      {\n        id: 'notifications',\n        title: 'Notification Preferences',\n        icon: 'fas fa-bell',\n        visible: this.settingsVisibility['notifications'],\n        editable: this.roleAccessService.canWrite('profile')\n      },\n      {\n        id: 'orders',\n        title: 'Order History',\n        icon: 'fas fa-shopping-bag',\n        visible: this.settingsVisibility['orders'],\n        editable: false\n      },\n      {\n        id: 'vendor',\n        title: 'Vendor Dashboard',\n        icon: 'fas fa-store',\n        visible: this.settingsVisibility['vendor'],\n        editable: this.roleAccessService.canWrite('vendor')\n      },\n      {\n        id: 'analytics',\n        title: 'Analytics & Reports',\n        icon: 'fas fa-chart-bar',\n        visible: this.roleAccessService.canRead('analytics'),\n        editable: this.roleAccessService.canWrite('analytics')\n      },\n      {\n        id: 'users',\n        title: 'User Management',\n        icon: 'fas fa-users',\n        visible: this.roleAccessService.canRead('users'),\n        editable: this.roleAccessService.canWrite('users')\n      },\n      {\n        id: 'system',\n        title: 'System Settings',\n        icon: 'fas fa-cogs',\n        visible: this.roleAccessService.canRead('system'),\n        editable: this.roleAccessService.canWrite('system')\n      }\n    ].filter(section => section.visible);\n  }\n\n  // Role-based utility methods\n  canEditProfile(): boolean {\n    return this.roleAccessService.canWrite('profile');\n  }\n\n  canViewAnalytics(): boolean {\n    return this.roleAccessService.canRead('analytics');\n  }\n\n  canManageUsers(): boolean {\n    return this.roleAccessService.canRead('users');\n  }\n\n  canAccessVendorFeatures(): boolean {\n    return this.roleAccessService.isSeller() || this.roleAccessService.hasElevatedPrivileges();\n  }\n\n  canAccessAdminFeatures(): boolean {\n    return this.roleAccessService.hasElevatedPrivileges();\n  }\n\n  getRoleDisplayName(): string {\n    const roleInfo = this.roleAccessService.getRoleInfo();\n    return roleInfo?.name || this.userRole;\n  }\n\n  getRoleColor(): string {\n    const colors: { [key: string]: string } = {\n      'buyer': '#28a745',\n      'seller': '#007bff',\n      'admin': '#fd7e14',\n      'super_admin': '#dc3545'\n    };\n    return colors[this.userRole] || '#6c757d';\n  }\n\n  // Navigation methods\n  navigateToSection(sectionId: string) {\n    // Implement navigation logic based on section\n    console.log('Navigating to section:', sectionId);\n  }\n\n  // Profile update methods\n  updateProfile() {\n    if (!this.canEditProfile()) {\n      alert('You do not have permission to edit your profile');\n      return;\n    }\n    // Implement profile update logic\n    console.log('Updating profile...');\n  }\n\n  changePassword() {\n    if (!this.canEditProfile()) {\n      alert('You do not have permission to change your password');\n      return;\n    }\n    // Implement password change logic\n    console.log('Changing password...');\n  }\n\n  getSectionDescription(sectionId: string): string {\n    const descriptions: { [key: string]: string } = {\n      'basic': 'Manage your personal information and contact details',\n      'security': 'Update password and security settings',\n      'notifications': 'Configure your notification preferences',\n      'orders': 'View your order history and track shipments',\n      'vendor': 'Manage your seller account and business information',\n      'analytics': 'View performance metrics and reports',\n      'users': 'Manage user accounts and permissions',\n      'system': 'Configure system-wide settings and preferences'\n    };\n    return descriptions[sectionId] || 'Manage this section';\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,qDAAqD;AAS5H,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAU3BC,YACUC,WAAwB,EACxBC,iBAAoC;IADpC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAX3B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,kBAAkB,GAA+B,EAAE;IACnD,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAC,eAAe,GAAU,EAAE;EAKxB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAD,eAAeA,CAAA;IACb,IAAI,CAACT,WAAW,CAACW,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACX,WAAW,GAAGW,IAAI;MACvB,IAAI,CAACV,QAAQ,GAAG,IAAI,CAACF,iBAAiB,CAACa,kBAAkB,EAAE;MAC3D,IAAI,CAACJ,oBAAoB,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACN,kBAAkB,GAAG,IAAI,CAACH,iBAAiB,CAACc,qBAAqB,EAAE;IACxE,IAAI,CAACV,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB,CAACe,mBAAmB,EAAE;IACpE,IAAI,CAACV,eAAe,GAAG,IAAI,CAACL,iBAAiB,CAACgB,kBAAkB,EAAE;IAClE,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACX,eAAe,GAAG,CACrB;MACEY,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,QAAQ,CAAC,SAAS;KACpD,EACD;MACEL,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,mBAAmB;MACzBC,OAAO,EAAE,IAAI,CAAClB,kBAAkB,CAAC,UAAU,CAAC;MAC5CmB,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,QAAQ,CAAC,SAAS;KACpD,EACD;MACEL,EAAE,EAAE,eAAe;MACnBC,KAAK,EAAE,0BAA0B;MACjCC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,IAAI,CAAClB,kBAAkB,CAAC,eAAe,CAAC;MACjDmB,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,QAAQ,CAAC,SAAS;KACpD,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,qBAAqB;MAC3BC,OAAO,EAAE,IAAI,CAAClB,kBAAkB,CAAC,QAAQ,CAAC;MAC1CmB,QAAQ,EAAE;KACX,EACD;MACEJ,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAE,IAAI,CAAClB,kBAAkB,CAAC,QAAQ,CAAC;MAC1CmB,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,QAAQ,CAAC,QAAQ;KACnD,EACD;MACEL,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,EAAE,kBAAkB;MACxBC,OAAO,EAAE,IAAI,CAACrB,iBAAiB,CAACwB,OAAO,CAAC,WAAW,CAAC;MACpDF,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,QAAQ,CAAC,WAAW;KACtD,EACD;MACEL,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAE,IAAI,CAACrB,iBAAiB,CAACwB,OAAO,CAAC,OAAO,CAAC;MAChDF,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,QAAQ,CAAC,OAAO;KAClD,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,IAAI,CAACrB,iBAAiB,CAACwB,OAAO,CAAC,QAAQ,CAAC;MACjDF,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,QAAQ,CAAC,QAAQ;KACnD,CACF,CAACE,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACL,OAAO,CAAC;EACtC;EAEA;EACAM,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC3B,iBAAiB,CAACuB,QAAQ,CAAC,SAAS,CAAC;EACnD;EAEAK,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC5B,iBAAiB,CAACwB,OAAO,CAAC,WAAW,CAAC;EACpD;EAEAK,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC7B,iBAAiB,CAACwB,OAAO,CAAC,OAAO,CAAC;EAChD;EAEAM,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAC9B,iBAAiB,CAAC+B,QAAQ,EAAE,IAAI,IAAI,CAAC/B,iBAAiB,CAACgC,qBAAqB,EAAE;EAC5F;EAEAC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACjC,iBAAiB,CAACgC,qBAAqB,EAAE;EACvD;EAEAE,kBAAkBA,CAAA;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAACnC,iBAAiB,CAACoC,WAAW,EAAE;IACrD,OAAOD,QAAQ,EAAEE,IAAI,IAAI,IAAI,CAACnC,QAAQ;EACxC;EAEAoC,YAAYA,CAAA;IACV,MAAMC,MAAM,GAA8B;MACxC,OAAO,EAAE,SAAS;MAClB,QAAQ,EAAE,SAAS;MACnB,OAAO,EAAE,SAAS;MAClB,aAAa,EAAE;KAChB;IACD,OAAOA,MAAM,CAAC,IAAI,CAACrC,QAAQ,CAAC,IAAI,SAAS;EAC3C;EAEA;EACAsC,iBAAiBA,CAACC,SAAiB;IACjC;IACAC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,SAAS,CAAC;EAClD;EAEA;EACAG,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACjB,cAAc,EAAE,EAAE;MAC1BkB,KAAK,CAAC,iDAAiD,CAAC;MACxD;;IAEF;IACAH,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACnB,cAAc,EAAE,EAAE;MAC1BkB,KAAK,CAAC,oDAAoD,CAAC;MAC3D;;IAEF;IACAH,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC;EAEAI,qBAAqBA,CAACN,SAAiB;IACrC,MAAMO,YAAY,GAA8B;MAC9C,OAAO,EAAE,sDAAsD;MAC/D,UAAU,EAAE,uCAAuC;MACnD,eAAe,EAAE,yCAAyC;MAC1D,QAAQ,EAAE,6CAA6C;MACvD,QAAQ,EAAE,qDAAqD;MAC/D,WAAW,EAAE,sCAAsC;MACnD,OAAO,EAAE,sCAAsC;MAC/C,QAAQ,EAAE;KACX;IACD,OAAOA,YAAY,CAACP,SAAS,CAAC,IAAI,qBAAqB;EACzD;CACD;AA1KY5C,gBAAgB,GAAAoD,UAAA,EAP5B1D,SAAS,CAAC;EACT2D,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC5D,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,oBAAoB,CAAC;EACnGyD,WAAW,EAAE,0BAA0B;EACvCC,SAAS,EAAE,CAAC,0BAA0B;CACvC,CAAC,C,EACWzD,gBAAgB,CA0K5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}