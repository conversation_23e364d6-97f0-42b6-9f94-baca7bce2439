{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\nconst _c0 = [\"tabsInner\"];\nclass MenuController {\n  constructor(menuController) {\n    this.menuController = menuController;\n  }\n  /**\n   * Programmatically open the Menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully opened\n   */\n  open(menuId) {\n    return this.menuController.open(menuId);\n  }\n  /**\n   * Programmatically close the Menu. If no `menuId` is given as the first\n   * argument then it'll close any menu which is open. If a `menuId`\n   * is given then it'll close that exact menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully closed\n   */\n  close(menuId) {\n    return this.menuController.close(menuId);\n  }\n  /**\n   * Toggle the menu. If it's closed, it will open, and if opened, it\n   * will close.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu has been toggled\n   */\n  toggle(menuId) {\n    return this.menuController.toggle(menuId);\n  }\n  /**\n   * Used to enable or disable a menu. For example, there could be multiple\n   * left menus, but only one of them should be able to be opened at the same\n   * time. If there are multiple menus on the same side, then enabling one menu\n   * will also automatically disable all the others that are on the same side.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  enable(shouldEnable, menuId) {\n    return this.menuController.enable(shouldEnable, menuId);\n  }\n  /**\n   * Used to enable or disable the ability to swipe open the menu.\n   * @param shouldEnable  True if it should be swipe-able, false if not.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  swipeGesture(shouldEnable, menuId) {\n    return this.menuController.swipeGesture(shouldEnable, menuId);\n  }\n  /**\n   * @param [menuId] Optionally get the menu by its id, or side.\n   * @return Returns true if the specified menu is currently open, otherwise false.\n   * If the menuId is not specified, it returns true if ANY menu is currenly open.\n   */\n  isOpen(menuId) {\n    return this.menuController.isOpen(menuId);\n  }\n  /**\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns true if the menu is currently enabled, otherwise false.\n   */\n  isEnabled(menuId) {\n    return this.menuController.isEnabled(menuId);\n  }\n  /**\n   * Used to get a menu instance. If a `menuId` is not provided then it'll\n   * return the first menu found. If a `menuId` is `left` or `right`, then\n   * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n   * provided, then it'll try to find the menu using the menu's `id`\n   * property. If a menu is not found then it'll return `null`.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu if found, otherwise `null`.\n   */\n  get(menuId) {\n    return this.menuController.get(menuId);\n  }\n  /**\n   * @return Returns the instance of the menu already opened, otherwise `null`.\n   */\n  getOpen() {\n    return this.menuController.getOpen();\n  }\n  /**\n   * @return Returns an array of all menu instances.\n   */\n  getMenus() {\n    return this.menuController.getMenus();\n  }\n  registerAnimation(name, animation) {\n    return this.menuController.registerAnimation(name, animation);\n  }\n  isAnimating() {\n    return this.menuController.isAnimating();\n  }\n  _getOpenSync() {\n    return this.menuController._getOpenSync();\n  }\n  _createAnimation(type, menuCmp) {\n    return this.menuController._createAnimation(type, menuCmp);\n  }\n  _register(menu) {\n    return this.menuController._register(menu);\n  }\n  _unregister(menu) {\n    return this.menuController._unregister(menu);\n  }\n  _setOpen(menu, shouldOpen, animated) {\n    return this.menuController._setOpen(menu, shouldOpen, animated);\n  }\n}\nclass DomController {\n  /**\n   * Schedules a task to run during the READ phase of the next frame.\n   * This task should only read the DOM, but never modify it.\n   */\n  read(cb) {\n    getQueue().read(cb);\n  }\n  /**\n   * Schedules a task to run during the WRITE phase of the next frame.\n   * This task should write the DOM, but never READ it.\n   */\n  write(cb) {\n    getQueue().write(cb);\n  }\n}\n/** @nocollapse */\nDomController.ɵfac = function DomController_Factory(t) {\n  return new (t || DomController)();\n};\n/** @nocollapse */\nDomController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomController,\n  factory: DomController.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst getQueue = () => {\n  const win = typeof window !== 'undefined' ? window : null;\n  if (win != null) {\n    const Ionic = win.Ionic;\n    if (Ionic?.queue) {\n      return Ionic.queue;\n    }\n    return {\n      read: cb => win.requestAnimationFrame(cb),\n      write: cb => win.requestAnimationFrame(cb)\n    };\n  }\n  return {\n    read: cb => cb(),\n    write: cb => cb()\n  };\n};\nclass Platform {\n  constructor(doc, zone) {\n    this.doc = doc;\n    /**\n     * @hidden\n     */\n    this.backButton = new Subject();\n    /**\n     * The keyboardDidShow event emits when the\n     * on-screen keyboard is presented.\n     */\n    this.keyboardDidShow = new Subject();\n    /**\n     * The keyboardDidHide event emits when the\n     * on-screen keyboard is hidden.\n     */\n    this.keyboardDidHide = new Subject();\n    /**\n     * The pause event emits when the native platform puts the application\n     * into the background, typically when the user switches to a different\n     * application. This event would emit when a Cordova app is put into\n     * the background, however, it would not fire on a standard web browser.\n     */\n    this.pause = new Subject();\n    /**\n     * The resume event emits when the native platform pulls the application\n     * out from the background. This event would emit when a Cordova app comes\n     * out from the background, however, it would not fire on a standard web browser.\n     */\n    this.resume = new Subject();\n    /**\n     * The resize event emits when the browser window has changed dimensions. This\n     * could be from a browser window being physically resized, or from a device\n     * changing orientation.\n     */\n    this.resize = new Subject();\n    zone.run(() => {\n      this.win = doc.defaultView;\n      this.backButton.subscribeWithPriority = function (priority, callback) {\n        return this.subscribe(ev => {\n          return ev.register(priority, processNextHandler => zone.run(() => callback(processNextHandler)));\n        });\n      };\n      proxyEvent(this.pause, doc, 'pause', zone);\n      proxyEvent(this.resume, doc, 'resume', zone);\n      proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n      proxyEvent(this.resize, this.win, 'resize', zone);\n      proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n      proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n      let readyResolve;\n      this._readyPromise = new Promise(res => {\n        readyResolve = res;\n      });\n      if (this.win?.['cordova']) {\n        doc.addEventListener('deviceready', () => {\n          readyResolve('cordova');\n        }, {\n          once: true\n        });\n      } else {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        readyResolve('dom');\n      }\n    });\n  }\n  /**\n   * @returns returns true/false based on platform.\n   * @description\n   * Depending on the platform the user is on, `is(platformName)` will\n   * return `true` or `false`. Note that the same app can return `true`\n   * for more than one platform name. For example, an app running from\n   * an iPad would return `true` for the platform names: `mobile`,\n   * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n   * from Cordova then `cordova` would be true, and if it was running\n   * from a web browser on the iPad then `mobileweb` would be `true`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     if (this.platform.is('ios')) {\n   *       // This will only print when on iOS\n   *       console.log('I am an iOS device!');\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * | Platform Name   | Description                        |\n   * |-----------------|------------------------------------|\n   * | android         | on a device running Android.       |\n   * | capacitor       | on a device running Capacitor.     |\n   * | cordova         | on a device running Cordova.       |\n   * | ios             | on a device running iOS.           |\n   * | ipad            | on an iPad device.                 |\n   * | iphone          | on an iPhone device.               |\n   * | phablet         | on a phablet device.               |\n   * | tablet          | on a tablet device.                |\n   * | electron        | in Electron on a desktop device.   |\n   * | pwa             | as a PWA app.                      |\n   * | mobile          | on a mobile device.                |\n   * | mobileweb       | on a mobile device in a browser.   |\n   * | desktop         | on a desktop device.               |\n   * | hybrid          | is a cordova or capacitor app.     |\n   *\n   */\n  is(platformName) {\n    return isPlatform(this.win, platformName);\n  }\n  /**\n   * @returns the array of platforms\n   * @description\n   * Depending on what device you are on, `platforms` can return multiple values.\n   * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n   * it would return `mobile`, `ios`, and `iphone`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     // This will print an array of the current platforms\n   *     console.log(this.platform.platforms());\n   *   }\n   * }\n   * ```\n   */\n  platforms() {\n    return getPlatforms(this.win);\n  }\n  /**\n   * Returns a promise when the platform is ready and native functionality\n   * can be called. If the app is running from within a web browser, then\n   * the promise will resolve when the DOM is ready. When the app is running\n   * from an application engine such as Cordova, then the promise will\n   * resolve when Cordova triggers the `deviceready` event.\n   *\n   * The resolved value is the `readySource`, which states which platform\n   * ready was used. For example, when Cordova is ready, the resolved ready\n   * source is `cordova`. The default ready source value will be `dom`. The\n   * `readySource` is useful if different logic should run depending on the\n   * platform the app is running from. For example, only Cordova can execute\n   * the status bar plugin, so the web should not run status bar plugin logic.\n   *\n   * ```\n   * import { Component } from '@angular/core';\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyApp {\n   *   constructor(public platform: Platform) {\n   *     this.platform.ready().then((readySource) => {\n   *       console.log('Platform ready from', readySource);\n   *       // Platform now ready, execute any required native code\n   *     });\n   *   }\n   * }\n   * ```\n   */\n  ready() {\n    return this._readyPromise;\n  }\n  /**\n   * Returns if this app is using right-to-left language direction or not.\n   * We recommend the app's `index.html` file already has the correct `dir`\n   * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n   * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n   */\n  get isRTL() {\n    return this.doc.dir === 'rtl';\n  }\n  /**\n   * Get the query string parameter\n   */\n  getQueryParam(key) {\n    return readQueryParam(this.win.location.href, key);\n  }\n  /**\n   * Returns `true` if the app is in landscape mode.\n   */\n  isLandscape() {\n    return !this.isPortrait();\n  }\n  /**\n   * Returns `true` if the app is in portrait mode.\n   */\n  isPortrait() {\n    return this.win.matchMedia?.('(orientation: portrait)').matches;\n  }\n  testUserAgent(expression) {\n    const nav = this.win.navigator;\n    return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n  }\n  /**\n   * Get the current url.\n   */\n  url() {\n    return this.win.location.href;\n  }\n  /**\n   * Gets the width of the platform's viewport using `window.innerWidth`.\n   */\n  width() {\n    return this.win.innerWidth;\n  }\n  /**\n   * Gets the height of the platform's viewport using `window.innerHeight`.\n   */\n  height() {\n    return this.win.innerHeight;\n  }\n}\n/** @nocollapse */\nPlatform.ɵfac = function Platform_Factory(t) {\n  return new (t || Platform)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone));\n};\n/** @nocollapse */\nPlatform.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Platform,\n  factory: Platform.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nconst readQueryParam = (url, key) => {\n  key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n  const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n  const results = regex.exec(url);\n  return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n  if (el) {\n    el.addEventListener(eventName, ev => {\n      /**\n       * `zone.run` is required to make sure that we are running inside the Angular zone\n       * at all times. This is necessary since an app that has Capacitor will\n       * override the `document.addEventListener` with its own implementation.\n       * The override causes the event to no longer be in the Angular zone.\n       */\n      zone.run(() => {\n        // ?? cordova might emit \"null\" events\n        const value = ev != null ? ev.detail : undefined;\n        emitter.next(value);\n      });\n    });\n  }\n};\nclass NavController {\n  constructor(platform, location, serializer, router) {\n    this.location = location;\n    this.serializer = serializer;\n    this.router = router;\n    this.direction = DEFAULT_DIRECTION;\n    this.animated = DEFAULT_ANIMATED;\n    this.guessDirection = 'forward';\n    this.lastNavId = -1;\n    // Subscribe to router events to detect direction\n    if (router) {\n      router.events.subscribe(ev => {\n        if (ev instanceof NavigationStart) {\n          const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n          this.guessDirection = id < this.lastNavId ? 'back' : 'forward';\n          this.guessAnimation = !ev.restoredState ? this.guessDirection : undefined;\n          this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n        }\n      });\n    }\n    // Subscribe to backButton events\n    platform.backButton.subscribeWithPriority(0, processNextHandler => {\n      this.pop();\n      processNextHandler();\n    });\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n   *\n   * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n   * and that it will show a \"forward\" animation by default.\n   *\n   * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n   * ```\n   */\n  navigateForward(url, options = {}) {\n    this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('back');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n   * and that it will show a \"back\" animation by default.\n   *\n   * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n   * ```\n   */\n  navigateBack(url, options = {}) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('root');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **root** means that all existing pages in the stack will be removed,\n   * and the navigated page will become the single page in the stack.\n   *\n   * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n   * ```\n   */\n  navigateRoot(url, options = {}) {\n    this.setDirection('root', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n   * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n   * by default.\n   */\n  back(options = {\n    animated: true,\n    animationDirection: 'back'\n  }) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.location.back();\n  }\n  /**\n   * This methods goes back in the context of Ionic's stack navigation.\n   *\n   * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n   * This is the recommended way to go back when you are using `ion-router-outlet`.\n   *\n   * Resolves to `true` if it was able to pop.\n   */\n  pop() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let outlet = _this.topOutlet;\n      while (outlet) {\n        if (yield outlet.pop()) {\n          return true;\n        } else {\n          outlet = outlet.parentOutlet;\n        }\n      }\n      return false;\n    })();\n  }\n  /**\n   * This methods specifies the direction of the next navigation performed by the Angular router.\n   *\n   * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n   *\n   * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n   */\n  setDirection(direction, animated, animationDirection, animationBuilder) {\n    this.direction = direction;\n    this.animated = getAnimation(direction, animated, animationDirection);\n    this.animationBuilder = animationBuilder;\n  }\n  /**\n   * @internal\n   */\n  setTopOutlet(outlet) {\n    this.topOutlet = outlet;\n  }\n  /**\n   * @internal\n   */\n  consumeTransition() {\n    let direction = 'root';\n    let animation;\n    const animationBuilder = this.animationBuilder;\n    if (this.direction === 'auto') {\n      direction = this.guessDirection;\n      animation = this.guessAnimation;\n    } else {\n      animation = this.animated;\n      direction = this.direction;\n    }\n    this.direction = DEFAULT_DIRECTION;\n    this.animated = DEFAULT_ANIMATED;\n    this.animationBuilder = undefined;\n    return {\n      direction,\n      animation,\n      animationBuilder\n    };\n  }\n  navigate(url, options) {\n    if (Array.isArray(url)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigate(url, options);\n    } else {\n      /**\n       * navigateByUrl ignores any properties that\n       * would change the url, so things like queryParams\n       * would be ignored unless we create a url tree\n       * More Info: https://github.com/angular/angular/issues/18798\n       */\n      const urlTree = this.serializer.parse(url.toString());\n      if (options.queryParams !== undefined) {\n        urlTree.queryParams = {\n          ...options.queryParams\n        };\n      }\n      if (options.fragment !== undefined) {\n        urlTree.fragment = options.fragment;\n      }\n      /**\n       * `navigateByUrl` will still apply `NavigationExtras` properties\n       * that do not modify the url, such as `replaceUrl` which is why\n       * `options` is passed in here.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigateByUrl(urlTree, options);\n    }\n  }\n}\n/** @nocollapse */\nNavController.ɵfac = function NavController_Factory(t) {\n  return new (t || NavController)(i0.ɵɵinject(Platform), i0.ɵɵinject(i1.Location), i0.ɵɵinject(i3.UrlSerializer), i0.ɵɵinject(i3.Router, 8));\n};\n/** @nocollapse */\nNavController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NavController,\n  factory: NavController.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Platform\n    }, {\n      type: i1.Location\n    }, {\n      type: i3.UrlSerializer\n    }, {\n      type: i3.Router,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nconst getAnimation = (direction, animated, animationDirection) => {\n  if (animated === false) {\n    return undefined;\n  }\n  if (animationDirection !== undefined) {\n    return animationDirection;\n  }\n  if (direction === 'forward' || direction === 'back') {\n    return direction;\n  } else if (direction === 'root' && animated === true) {\n    return 'forward';\n  }\n  return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\nclass Config {\n  get(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.get(key, fallback);\n    }\n    return null;\n  }\n  getBoolean(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getBoolean(key, fallback);\n    }\n    return false;\n  }\n  getNumber(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getNumber(key, fallback);\n    }\n    return 0;\n  }\n}\n/** @nocollapse */\nConfig.ɵfac = function Config_Factory(t) {\n  return new (t || Config)();\n};\n/** @nocollapse */\nConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Config,\n  factory: Config.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Config, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n  if (typeof window !== 'undefined') {\n    const Ionic = window.Ionic;\n    if (Ionic?.config) {\n      return Ionic.config;\n    }\n  }\n  return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n  constructor(data = {}) {\n    this.data = data;\n  }\n  /**\n   * Get the value of a nav-parameter for the current view\n   *\n   * ```ts\n   * import { NavParams } from 'ionic-angular';\n   *\n   * export class MyClass{\n   *  constructor(public navParams: NavParams){\n   *    // userParams is an object we have in our nav-parameters\n   *    this.navParams.get('userParams');\n   *  }\n   * }\n   * ```\n   *\n   * @param param Which param you want to look up\n   */\n  get(param) {\n    return this.data[param];\n  }\n}\n\n// TODO(FW-2827): types\nclass AngularDelegate {\n  constructor() {\n    this.zone = inject(NgZone);\n    this.applicationRef = inject(ApplicationRef);\n  }\n  create(environmentInjector, injector, elementReferenceKey) {\n    return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey);\n  }\n}\n/** @nocollapse */\nAngularDelegate.ɵfac = function AngularDelegate_Factory(t) {\n  return new (t || AngularDelegate)();\n};\n/** @nocollapse */\nAngularDelegate.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AngularDelegate,\n  factory: AngularDelegate.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularDelegate, [{\n    type: Injectable\n  }], null, null);\n})();\nclass AngularFrameworkDelegate {\n  constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey) {\n    this.environmentInjector = environmentInjector;\n    this.injector = injector;\n    this.applicationRef = applicationRef;\n    this.zone = zone;\n    this.elementReferenceKey = elementReferenceKey;\n    this.elRefMap = new WeakMap();\n    this.elEventsMap = new WeakMap();\n  }\n  attachViewToDom(container, component, params, cssClasses) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentProps = {\n          ...params\n        };\n        /**\n         * Ionic Angular passes a reference to a modal\n         * or popover that can be accessed using a\n         * variable in the overlay component. If\n         * elementReferenceKey is defined, then we should\n         * pass a reference to the component using\n         * elementReferenceKey as the key.\n         */\n        if (this.elementReferenceKey !== undefined) {\n          componentProps[this.elementReferenceKey] = container;\n        }\n        const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey);\n        resolve(el);\n      });\n    });\n  }\n  removeViewFromDom(_container, component) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentRef = this.elRefMap.get(component);\n        if (componentRef) {\n          componentRef.destroy();\n          this.elRefMap.delete(component);\n          const unbindEvents = this.elEventsMap.get(component);\n          if (unbindEvents) {\n            unbindEvents();\n            this.elEventsMap.delete(component);\n          }\n        }\n        resolve();\n      });\n    });\n  }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey) => {\n  /**\n   * Wraps the injector with a custom injector that\n   * provides NavParams to the component.\n   *\n   * NavParams is a legacy feature from Ionic v3 that allows\n   * Angular developers to provide data to a component\n   * and access it by providing NavParams as a dependency\n   * in the constructor.\n   *\n   * The modern approach is to access the data directly\n   * from the component's class instance.\n   */\n  const childInjector = Injector.create({\n    providers: getProviders(params),\n    parent: injector\n  });\n  const componentRef = createComponent(component, {\n    environmentInjector,\n    elementInjector: childInjector\n  });\n  const instance = componentRef.instance;\n  const hostElement = componentRef.location.nativeElement;\n  if (params) {\n    /**\n     * For modals and popovers, a reference to the component is\n     * added to `params` during the call to attachViewToDom. If\n     * a reference using this name is already set, this means\n     * the app is trying to use the name as a component prop,\n     * which will cause collisions.\n     */\n    if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n      console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n    }\n    Object.assign(instance, params);\n  }\n  if (cssClasses) {\n    for (const cssClass of cssClasses) {\n      hostElement.classList.add(cssClass);\n    }\n  }\n  const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n  container.appendChild(hostElement);\n  applicationRef.attachView(componentRef.hostView);\n  elRefMap.set(hostElement, componentRef);\n  elEventsMap.set(hostElement, unbindEvents);\n  return hostElement;\n};\nconst LIFECYCLES = [LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD];\nconst bindLifecycleEvents = (zone, instance, element) => {\n  return zone.run(() => {\n    const unregisters = LIFECYCLES.filter(eventName => typeof instance[eventName] === 'function').map(eventName => {\n      const handler = ev => instance[eventName](ev.detail);\n      element.addEventListener(eventName, handler);\n      return () => element.removeEventListener(eventName, handler);\n    });\n    return () => unregisters.forEach(fn => fn());\n  });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = params => {\n  return [{\n    provide: NavParamsToken,\n    useValue: params\n  }, {\n    provide: NavParams,\n    useFactory: provideNavParamsInjectable,\n    deps: [NavParamsToken]\n  }];\n};\nconst provideNavParamsInjectable = params => {\n  return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\nconst proxyInputs = (Cmp, inputs) => {\n  const Prototype = Cmp.prototype;\n  inputs.forEach(item => {\n    Object.defineProperty(Prototype, item, {\n      get() {\n        return this.el[item];\n      },\n      set(val) {\n        this.z.runOutsideAngular(() => this.el[item] = val);\n      }\n    });\n  });\n};\nconst proxyMethods = (Cmp, methods) => {\n  const Prototype = Cmp.prototype;\n  methods.forEach(methodName => {\n    Prototype[methodName] = function () {\n      const args = arguments;\n      return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n    };\n  });\n};\nconst proxyOutputs = (instance, el, events) => {\n  events.forEach(eventName => instance[eventName] = fromEvent(el, eventName));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n  const decorator = function (cls) {\n    const {\n      defineCustomElementFn,\n      inputs,\n      methods\n    } = opts;\n    if (defineCustomElementFn !== undefined) {\n      defineCustomElementFn();\n    }\n    if (inputs) {\n      proxyInputs(cls, inputs);\n    }\n    if (methods) {\n      proxyMethods(cls, methods);\n    }\n    return cls;\n  };\n  return decorator;\n}\nconst POPOVER_INPUTS = ['alignment', 'animated', 'arrow', 'keepContentsMounted', 'backdropDismiss', 'cssClass', 'dismissOnSelect', 'enterAnimation', 'event', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'showBackdrop', 'translucent', 'trigger', 'triggerAction', 'reference', 'size', 'side'];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = class IonPopover {\n  constructor(c, r, z) {\n    this.z = z;\n    this.isCmpOpen = false;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionPopoverDidPresent', 'ionPopoverWillPresent', 'ionPopoverWillDismiss', 'ionPopoverDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n};\n/** @nocollapse */\nIonPopover.ɵfac = function IonPopover_Factory(t) {\n  return new (t || IonPopover)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n/** @nocollapse */\nIonPopover.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: IonPopover,\n  selectors: [[\"ion-popover\"]],\n  contentQueries: function IonPopover_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n    }\n  },\n  inputs: {\n    alignment: \"alignment\",\n    animated: \"animated\",\n    arrow: \"arrow\",\n    keepContentsMounted: \"keepContentsMounted\",\n    backdropDismiss: \"backdropDismiss\",\n    cssClass: \"cssClass\",\n    dismissOnSelect: \"dismissOnSelect\",\n    enterAnimation: \"enterAnimation\",\n    event: \"event\",\n    isOpen: \"isOpen\",\n    keyboardClose: \"keyboardClose\",\n    leaveAnimation: \"leaveAnimation\",\n    mode: \"mode\",\n    showBackdrop: \"showBackdrop\",\n    translucent: \"translucent\",\n    trigger: \"trigger\",\n    triggerAction: \"triggerAction\",\n    reference: \"reference\",\n    size: \"size\",\n    side: \"side\"\n  }\n});\nIonPopover = __decorate([ProxyCmp({\n  inputs: POPOVER_INPUTS,\n  methods: POPOVER_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonPopover);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonPopover, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-popover',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: POPOVER_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst MODAL_INPUTS = ['animated', 'keepContentsMounted', 'backdropBreakpoint', 'backdropDismiss', 'breakpoints', 'canDismiss', 'cssClass', 'enterAnimation', 'event', 'handle', 'handleBehavior', 'initialBreakpoint', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'presentingElement', 'showBackdrop', 'translucent', 'trigger'];\nconst MODAL_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss', 'setCurrentBreakpoint', 'getCurrentBreakpoint'];\nlet IonModal = class IonModal {\n  constructor(c, r, z) {\n    this.z = z;\n    this.isCmpOpen = false;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionModalDidPresent', 'ionModalWillPresent', 'ionModalWillDismiss', 'ionModalDidDismiss', 'ionBreakpointDidChange', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n};\n/** @nocollapse */\nIonModal.ɵfac = function IonModal_Factory(t) {\n  return new (t || IonModal)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n/** @nocollapse */\nIonModal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: IonModal,\n  selectors: [[\"ion-modal\"]],\n  contentQueries: function IonModal_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n    }\n  },\n  inputs: {\n    animated: \"animated\",\n    keepContentsMounted: \"keepContentsMounted\",\n    backdropBreakpoint: \"backdropBreakpoint\",\n    backdropDismiss: \"backdropDismiss\",\n    breakpoints: \"breakpoints\",\n    canDismiss: \"canDismiss\",\n    cssClass: \"cssClass\",\n    enterAnimation: \"enterAnimation\",\n    event: \"event\",\n    handle: \"handle\",\n    handleBehavior: \"handleBehavior\",\n    initialBreakpoint: \"initialBreakpoint\",\n    isOpen: \"isOpen\",\n    keyboardClose: \"keyboardClose\",\n    leaveAnimation: \"leaveAnimation\",\n    mode: \"mode\",\n    presentingElement: \"presentingElement\",\n    showBackdrop: \"showBackdrop\",\n    translucent: \"translucent\",\n    trigger: \"trigger\"\n  }\n});\nIonModal = __decorate([ProxyCmp({\n  inputs: MODAL_INPUTS,\n  methods: MODAL_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonModal);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonModal, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-modal',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: MODAL_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst insertView = (views, view, direction) => {\n  if (direction === 'root') {\n    return setRoot(views, view);\n  } else if (direction === 'forward') {\n    return setForward(views, view);\n  } else {\n    return setBack(views, view);\n  }\n};\nconst setRoot = (views, view) => {\n  views = views.filter(v => v.stackId !== view.stackId);\n  views.push(view);\n  return views;\n};\nconst setForward = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    views = views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    views.push(view);\n  }\n  return views;\n};\nconst setBack = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    return views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    return setRoot(views, view);\n  }\n};\nconst getUrl = (router, activatedRoute) => {\n  const urlTree = router.createUrlTree(['.'], {\n    relativeTo: activatedRoute\n  });\n  return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n  if (!leavingView) {\n    return true;\n  }\n  return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n  if (!prefixUrl) {\n    return undefined;\n  }\n  const segments = toSegments(url);\n  for (let i = 0; i < segments.length; i++) {\n    if (i >= prefixUrl.length) {\n      return segments[i];\n    }\n    if (segments[i] !== prefixUrl[i]) {\n      return undefined;\n    }\n  }\n  return undefined;\n};\nconst toSegments = path => {\n  return path.split('/').map(s => s.trim()).filter(s => s !== '');\n};\nconst destroyView = view => {\n  if (view) {\n    view.ref.destroy();\n    view.unlistenEvents();\n  }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n  constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n    this.containerEl = containerEl;\n    this.router = router;\n    this.navCtrl = navCtrl;\n    this.zone = zone;\n    this.location = location;\n    this.views = [];\n    this.skipTransition = false;\n    this.nextId = 0;\n    this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n  }\n  createView(ref, activatedRoute) {\n    const url = getUrl(this.router, activatedRoute);\n    const element = ref?.location?.nativeElement;\n    const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n    return {\n      id: this.nextId++,\n      stackId: computeStackId(this.tabsPrefix, url),\n      unlistenEvents,\n      element,\n      ref,\n      url\n    };\n  }\n  getExistingView(activatedRoute) {\n    const activatedUrlKey = getUrl(this.router, activatedRoute);\n    const view = this.views.find(vw => vw.url === activatedUrlKey);\n    if (view) {\n      view.ref.changeDetectorRef.reattach();\n    }\n    return view;\n  }\n  setActive(enteringView) {\n    const consumeResult = this.navCtrl.consumeTransition();\n    let {\n      direction,\n      animation,\n      animationBuilder\n    } = consumeResult;\n    const leavingView = this.activeView;\n    const tabSwitch = isTabSwitch(enteringView, leavingView);\n    if (tabSwitch) {\n      direction = 'back';\n      animation = undefined;\n    }\n    const viewsSnapshot = this.views.slice();\n    let currentNavigation;\n    const router = this.router;\n    // Angular >= 7.2.0\n    if (router.getCurrentNavigation) {\n      currentNavigation = router.getCurrentNavigation();\n      // Angular < 7.2.0\n    } else if (router.navigations?.value) {\n      currentNavigation = router.navigations.value;\n    }\n    /**\n     * If the navigation action\n     * sets `replaceUrl: true`\n     * then we need to make sure\n     * we remove the last item\n     * from our views stack\n     */\n    if (currentNavigation?.extras?.replaceUrl) {\n      if (this.views.length > 0) {\n        this.views.splice(-1, 1);\n      }\n    }\n    const reused = this.views.includes(enteringView);\n    const views = this.insertView(enteringView, direction);\n    // Trigger change detection before transition starts\n    // This will call ngOnInit() the first time too, just after the view\n    // was attached to the dom, but BEFORE the transition starts\n    if (!reused) {\n      enteringView.ref.changeDetectorRef.detectChanges();\n    }\n    /**\n     * If we are going back from a page that\n     * was presented using a custom animation\n     * we should default to using that\n     * unless the developer explicitly\n     * provided another animation.\n     */\n    const customAnimation = enteringView.animationBuilder;\n    if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n      animationBuilder = customAnimation;\n    }\n    /**\n     * Save any custom animation so that navigating\n     * back will use this custom animation by default.\n     */\n    if (leavingView) {\n      leavingView.animationBuilder = animationBuilder;\n    }\n    // Wait until previous transitions finish\n    return this.zone.runOutsideAngular(() => {\n      return this.wait(() => {\n        // disconnect leaving page from change detection to\n        // reduce jank during the page transition\n        if (leavingView) {\n          leavingView.ref.changeDetectorRef.detach();\n        }\n        // In case the enteringView is the same as the leavingPage we need to reattach()\n        enteringView.ref.changeDetectorRef.reattach();\n        return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder).then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone)).then(() => ({\n          enteringView,\n          direction,\n          animation,\n          tabSwitch\n        }));\n      });\n    });\n  }\n  canGoBack(deep, stackId = this.getActiveStackId()) {\n    return this.getStack(stackId).length > deep;\n  }\n  pop(deep, stackId = this.getActiveStackId()) {\n    return this.zone.run(() => {\n      const views = this.getStack(stackId);\n      if (views.length <= deep) {\n        return Promise.resolve(false);\n      }\n      const view = views[views.length - deep - 1];\n      let url = view.url;\n      const viewSavedData = view.savedData;\n      if (viewSavedData) {\n        const primaryOutlet = viewSavedData.get('primary');\n        if (primaryOutlet?.route?._routerState?.snapshot.url) {\n          url = primaryOutlet.route._routerState.snapshot.url;\n        }\n      }\n      const {\n        animationBuilder\n      } = this.navCtrl.consumeTransition();\n      return this.navCtrl.navigateBack(url, {\n        ...view.savedExtras,\n        animation: animationBuilder\n      }).then(() => true);\n    });\n  }\n  startBackTransition() {\n    const leavingView = this.activeView;\n    if (leavingView) {\n      const views = this.getStack(leavingView.stackId);\n      const enteringView = views[views.length - 2];\n      const customAnimation = enteringView.animationBuilder;\n      return this.wait(() => {\n        return this.transition(enteringView,\n        // entering view\n        leavingView,\n        // leaving view\n        'back', this.canGoBack(2), true, customAnimation);\n      });\n    }\n    return Promise.resolve();\n  }\n  endBackTransition(shouldComplete) {\n    if (shouldComplete) {\n      this.skipTransition = true;\n      this.pop(1);\n    } else if (this.activeView) {\n      cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n    }\n  }\n  getLastUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[views.length - 1] : undefined;\n  }\n  /**\n   * @internal\n   */\n  getRootUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[0] : undefined;\n  }\n  getActiveStackId() {\n    return this.activeView ? this.activeView.stackId : undefined;\n  }\n  /**\n   * @internal\n   */\n  getActiveView() {\n    return this.activeView;\n  }\n  hasRunningTask() {\n    return this.runningTask !== undefined;\n  }\n  destroy() {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.containerEl = undefined;\n    this.views.forEach(destroyView);\n    this.activeView = undefined;\n    this.views = [];\n  }\n  getStack(stackId) {\n    return this.views.filter(v => v.stackId === stackId);\n  }\n  insertView(enteringView, direction) {\n    this.activeView = enteringView;\n    this.views = insertView(this.views, enteringView, direction);\n    return this.views.slice();\n  }\n  transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n    if (this.skipTransition) {\n      this.skipTransition = false;\n      return Promise.resolve(false);\n    }\n    if (leavingView === enteringView) {\n      return Promise.resolve(false);\n    }\n    const enteringEl = enteringView ? enteringView.element : undefined;\n    const leavingEl = leavingView ? leavingView.element : undefined;\n    const containerEl = this.containerEl;\n    if (enteringEl && enteringEl !== leavingEl) {\n      enteringEl.classList.add('ion-page');\n      enteringEl.classList.add('ion-page-invisible');\n      if (enteringEl.parentElement !== containerEl) {\n        containerEl.appendChild(enteringEl);\n      }\n      if (containerEl.commit) {\n        return containerEl.commit(enteringEl, leavingEl, {\n          duration: direction === undefined ? 0 : undefined,\n          direction,\n          showGoBack,\n          progressAnimation,\n          animationBuilder\n        });\n      }\n    }\n    return Promise.resolve(false);\n  }\n  wait(task) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.runningTask !== undefined) {\n        yield _this2.runningTask;\n        _this2.runningTask = undefined;\n      }\n      const promise = _this2.runningTask = task();\n      promise.finally(() => _this2.runningTask = undefined);\n      return promise;\n    })();\n  }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n  if (typeof requestAnimationFrame === 'function') {\n    return new Promise(resolve => {\n      requestAnimationFrame(() => {\n        cleanup(activeRoute, views, viewsSnapshot, location, zone);\n        resolve();\n      });\n    });\n  }\n  return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n  /**\n   * Re-enter the Angular zone when destroying page components. This will allow\n   * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n   */\n  zone.run(() => viewsSnapshot.filter(view => !views.includes(view)).forEach(destroyView));\n  views.forEach(view => {\n    /**\n     * In the event that a user navigated multiple\n     * times in rapid succession, we want to make sure\n     * we don't pre-emptively detach a view while\n     * it is in mid-transition.\n     *\n     * In this instance we also do not care about query\n     * params or fragments as it will be the same view regardless\n     */\n    const locationWithoutParams = location.path().split('?')[0];\n    const locationWithoutFragment = locationWithoutParams.split('#')[0];\n    if (view !== activeRoute && view.url !== locationWithoutFragment) {\n      const element = view.element;\n      element.setAttribute('aria-hidden', 'true');\n      element.classList.add('ion-page-hidden');\n      view.ref.changeDetectorRef.detach();\n    }\n  });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonRouterOutlet {\n  constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n    this.parentOutlet = parentOutlet;\n    this.activatedView = null;\n    // Maintain map of activated route proxies for each component instance\n    this.proxyMap = new WeakMap();\n    // Keep the latest activated route in a subject for the proxy routes to switch map to\n    this.currentActivatedRoute$ = new BehaviorSubject(null);\n    this.activated = null;\n    this._activatedRoute = null;\n    /**\n     * The name of the outlet\n     */\n    this.name = PRIMARY_OUTLET;\n    /** @internal */\n    this.stackWillChange = new EventEmitter();\n    /** @internal */\n    this.stackDidChange = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    this.activateEvents = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    this.deactivateEvents = new EventEmitter();\n    this.parentContexts = inject(ChildrenOutletContexts);\n    this.location = inject(ViewContainerRef);\n    this.environmentInjector = inject(EnvironmentInjector);\n    this.inputBinder = inject(INPUT_BINDER, {\n      optional: true\n    });\n    /** @nodoc */\n    this.supportsBindingToComponentInputs = true;\n    // Ionic providers\n    this.config = inject(Config);\n    this.navCtrl = inject(NavController);\n    this.nativeEl = elementRef.nativeElement;\n    this.name = name || PRIMARY_OUTLET;\n    this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n    this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n    this.parentContexts.onChildOutletCreated(this.name, this);\n  }\n  /** @internal */\n  get activatedComponentRef() {\n    return this.activated;\n  }\n  set animation(animation) {\n    this.nativeEl.animation = animation;\n  }\n  set animated(animated) {\n    this.nativeEl.animated = animated;\n  }\n  set swipeGesture(swipe) {\n    this._swipeGesture = swipe;\n    this.nativeEl.swipeHandler = swipe ? {\n      canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n      onStart: () => this.stackCtrl.startBackTransition(),\n      onEnd: shouldContinue => this.stackCtrl.endBackTransition(shouldContinue)\n    } : undefined;\n  }\n  ngOnDestroy() {\n    this.stackCtrl.destroy();\n    this.inputBinder?.unsubscribeFromRouteData(this);\n  }\n  getContext() {\n    return this.parentContexts.getContext(this.name);\n  }\n  ngOnInit() {\n    this.initializeOutletWithName();\n  }\n  // Note: Ionic deviates from the Angular Router implementation here\n  initializeOutletWithName() {\n    if (!this.activated) {\n      // If the outlet was not instantiated at the time the route got activated we need to populate\n      // the outlet when it is initialized (ie inside a NgIf)\n      const context = this.getContext();\n      if (context?.route) {\n        this.activateWith(context.route, context.injector);\n      }\n    }\n    new Promise(resolve => componentOnReady(this.nativeEl, resolve)).then(() => {\n      if (this._swipeGesture === undefined) {\n        this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n      }\n    });\n  }\n  get isActivated() {\n    return !!this.activated;\n  }\n  get component() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this.activated.instance;\n  }\n  get activatedRoute() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this._activatedRoute;\n  }\n  get activatedRouteData() {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to detach the subtree\n   */\n  detach() {\n    throw new Error('incompatible reuse strategy');\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  attach(_ref, _activatedRoute) {\n    throw new Error('incompatible reuse strategy');\n  }\n  deactivate() {\n    if (this.activated) {\n      if (this.activatedView) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        this.activatedView.savedData = new Map(context.children['contexts']);\n        /**\n         * Angular v11.2.10 introduced a change\n         * where this route context is cleared out when\n         * a router-outlet is deactivated, However,\n         * we need this route information in order to\n         * return a user back to the correct tab when\n         * leaving and then going back to the tab context.\n         */\n        const primaryOutlet = this.activatedView.savedData.get('primary');\n        if (primaryOutlet && context.route) {\n          primaryOutlet.route = {\n            ...context.route\n          };\n        }\n        /**\n         * Ensure we are saving the NavigationExtras\n         * data otherwise it will be lost\n         */\n        this.activatedView.savedExtras = {};\n        if (context.route) {\n          const contextSnapshot = context.route.snapshot;\n          this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n          this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n        }\n      }\n      const c = this.component;\n      this.activatedView = null;\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n  activateWith(activatedRoute, environmentInjector) {\n    if (this.isActivated) {\n      throw new Error('Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n    let cmpRef;\n    let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n    if (enteringView) {\n      cmpRef = this.activated = enteringView.ref;\n      const saved = enteringView.savedData;\n      if (saved) {\n        // self-restore\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        context.children['contexts'] = saved;\n      }\n      // Updated activated route proxy for this component\n      this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n    } else {\n      const snapshot = activatedRoute._futureSnapshot;\n      /**\n       * Angular 14 introduces a new `loadComponent` property to the route config.\n       * This function will assign a `component` property to the route snapshot.\n       * We check for the presence of this property to determine if the route is\n       * using standalone components.\n       */\n      const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n      // We create an activated route proxy object that will maintain future updates for this component\n      // over its lifecycle in the stack.\n      const component$ = new BehaviorSubject(null);\n      const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n      const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const component = snapshot.routeConfig.component ?? snapshot.component;\n      cmpRef = this.activated = this.location.createComponent(component, {\n        index: this.location.length,\n        injector,\n        environmentInjector: environmentInjector ?? this.environmentInjector\n      });\n      // Once the component is created we can push it to our local subject supplied to the proxy\n      component$.next(cmpRef.instance);\n      // Calling `markForCheck` to make sure we will run the change detection when the\n      // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n      enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n      // Store references to the proxy by component\n      this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n      this.currentActivatedRoute$.next({\n        component: cmpRef.instance,\n        activatedRoute\n      });\n    }\n    this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n    this.activatedView = enteringView;\n    /**\n     * The top outlet is set prior to the entering view's transition completing,\n     * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n     * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n     * activated.\n     *\n     * In this scenario, activeWith is called for both the tabs and the root router outlet.\n     * To avoid a race condition, we assign the top outlet synchronously.\n     */\n    this.navCtrl.setTopOutlet(this);\n    const leavingView = this.stackCtrl.getActiveView();\n    this.stackWillChange.emit({\n      enteringView,\n      tabSwitch: isTabSwitch(enteringView, leavingView)\n    });\n    this.stackCtrl.setActive(enteringView).then(data => {\n      this.activateEvents.emit(cmpRef.instance);\n      this.stackDidChange.emit(data);\n    });\n  }\n  /**\n   * Returns `true` if there are pages in the stack to go back.\n   */\n  canGoBack(deep = 1, stackId) {\n    return this.stackCtrl.canGoBack(deep, stackId);\n  }\n  /**\n   * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n   */\n  pop(deep = 1, stackId) {\n    return this.stackCtrl.pop(deep, stackId);\n  }\n  /**\n   * Returns the URL of the active page of each stack.\n   */\n  getLastUrl(stackId) {\n    const active = this.stackCtrl.getLastUrl(stackId);\n    return active ? active.url : undefined;\n  }\n  /**\n   * Returns the RouteView of the active page of each stack.\n   * @internal\n   */\n  getLastRouteView(stackId) {\n    return this.stackCtrl.getLastUrl(stackId);\n  }\n  /**\n   * Returns the root view in the tab stack.\n   * @internal\n   */\n  getRootView(stackId) {\n    return this.stackCtrl.getRootUrl(stackId);\n  }\n  /**\n   * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n   */\n  getActiveStackId() {\n    return this.stackCtrl.getActiveStackId();\n  }\n  /**\n   * Since the activated route can change over the life time of a component in an ion router outlet, we create\n   * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n   */\n  createActivatedRouteProxy(component$, activatedRoute) {\n    const proxy = new ActivatedRoute();\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n    proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n    proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n    proxy.url = this.proxyObservable(component$, 'url');\n    proxy.params = this.proxyObservable(component$, 'params');\n    proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n    proxy.fragment = this.proxyObservable(component$, 'fragment');\n    proxy.data = this.proxyObservable(component$, 'data');\n    return proxy;\n  }\n  /**\n   * Create a wrapped observable that will switch to the latest activated route matched by the given component\n   */\n  proxyObservable(component$, path) {\n    return component$.pipe(\n    // First wait until the component instance is pushed\n    filter(component => !!component), switchMap(component => this.currentActivatedRoute$.pipe(filter(current => current !== null && current.component === component), switchMap(current => current && current.activatedRoute[path]), distinctUntilChanged())));\n  }\n  /**\n   * Updates the activated route proxy for the given component to the new incoming router state\n   */\n  updateActivatedRouteProxy(component, activatedRoute) {\n    const proxy = this.proxyMap.get(component);\n    if (!proxy) {\n      throw new Error(`Could not find activated route proxy for view`);\n    }\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    this.currentActivatedRoute$.next({\n      component,\n      activatedRoute\n    });\n  }\n}\n/** @nocollapse */\nIonRouterOutlet.ɵfac = function IonRouterOutlet_Factory(t) {\n  return new (t || IonRouterOutlet)(i0.ɵɵinjectAttribute('name'), i0.ɵɵinjectAttribute('tabs'), i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(IonRouterOutlet, 12));\n};\n/** @nocollapse */\nIonRouterOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: IonRouterOutlet,\n  selectors: [[\"ion-router-outlet\"]],\n  inputs: {\n    animated: \"animated\",\n    animation: \"animation\",\n    mode: \"mode\",\n    swipeGesture: \"swipeGesture\",\n    name: \"name\"\n  },\n  outputs: {\n    stackWillChange: \"stackWillChange\",\n    stackDidChange: \"stackDidChange\",\n    activateEvents: \"activate\",\n    deactivateEvents: \"deactivate\"\n  },\n  exportAs: [\"outlet\"]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonRouterOutlet, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-router-outlet',\n      exportAs: 'outlet',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: ['animated', 'animation', 'mode', 'swipeGesture']\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['name']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Attribute,\n        args: ['tabs']\n      }]\n    }, {\n      type: i1.Location\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.ActivatedRoute\n    }, {\n      type: IonRouterOutlet,\n      decorators: [{\n        type: SkipSelf\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    name: [{\n      type: Input\n    }],\n    stackWillChange: [{\n      type: Output\n    }],\n    stackDidChange: [{\n      type: Output\n    }],\n    activateEvents: [{\n      type: Output,\n      args: ['activate']\n    }],\n    deactivateEvents: [{\n      type: Output,\n      args: ['deactivate']\n    }]\n  });\n})();\nclass OutletInjector {\n  constructor(route, childContexts, parent) {\n    this.route = route;\n    this.childContexts = childContexts;\n    this.parent = parent;\n  }\n  get(token, notFoundValue) {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n    return this.parent.get(token, notFoundValue);\n  }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n  constructor() {\n    this.outletDataSubscriptions = new Map();\n  }\n  bindActivatedRouteToOutletComponent(outlet) {\n    this.unsubscribeFromRouteData(outlet);\n    this.subscribeToRouteData(outlet);\n  }\n  unsubscribeFromRouteData(outlet) {\n    this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n    this.outletDataSubscriptions.delete(outlet);\n  }\n  subscribeToRouteData(outlet) {\n    const {\n      activatedRoute\n    } = outlet;\n    const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data]).pipe(switchMap(([queryParams, params, data], index) => {\n      data = {\n        ...queryParams,\n        ...params,\n        ...data\n      };\n      // Get the first result from the data subscription synchronously so it's available to\n      // the component as soon as possible (and doesn't require a second change detection).\n      if (index === 0) {\n        return of(data);\n      }\n      // Promise.resolve is used to avoid synchronously writing the wrong data when\n      // two of the Observables in the `combineLatest` stream emit one after\n      // another.\n      return Promise.resolve(data);\n    })).subscribe(data => {\n      // Outlet may have been deactivated or changed names to be associated with a different\n      // route\n      if (!outlet.isActivated || !outlet.activatedComponentRef || outlet.activatedRoute !== activatedRoute || activatedRoute.component === null) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      const mirror = reflectComponentType(activatedRoute.component);\n      if (!mirror) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      for (const {\n        templateName\n      } of mirror.inputs) {\n        outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n      }\n    });\n    this.outletDataSubscriptions.set(outlet, dataSubscription);\n  }\n}\n/** @nocollapse */\nRoutedComponentInputBinder.ɵfac = function RoutedComponentInputBinder_Factory(t) {\n  return new (t || RoutedComponentInputBinder)();\n};\n/** @nocollapse */\nRoutedComponentInputBinder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: RoutedComponentInputBinder,\n  factory: RoutedComponentInputBinder.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutedComponentInputBinder, [{\n    type: Injectable\n  }], null, null);\n})();\nconst provideComponentInputBinding = () => {\n  return {\n    provide: INPUT_BINDER,\n    useFactory: componentInputBindingFactory,\n    deps: [Router]\n  };\n};\nfunction componentInputBindingFactory(router) {\n  /**\n   * We cast the router to any here, since the componentInputBindingEnabled\n   * property is not available until Angular v16.\n   */\n  if (router?.componentInputBindingEnabled) {\n    return new RoutedComponentInputBinder();\n  }\n  return null;\n}\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = class IonBackButton {\n  constructor(routerOutlet, navCtrl, config, r, z, c) {\n    this.routerOutlet = routerOutlet;\n    this.navCtrl = navCtrl;\n    this.config = config;\n    this.r = r;\n    this.z = z;\n    c.detach();\n    this.el = this.r.nativeElement;\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n    if (this.routerOutlet?.canGoBack()) {\n      this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n      this.routerOutlet.pop();\n      ev.preventDefault();\n    } else if (defaultHref != null) {\n      this.navCtrl.navigateBack(defaultHref, {\n        animation: this.routerAnimation\n      });\n      ev.preventDefault();\n    }\n  }\n};\n/** @nocollapse */\nIonBackButton.ɵfac = function IonBackButton_Factory(t) {\n  return new (t || IonBackButton)(i0.ɵɵdirectiveInject(IonRouterOutlet, 8), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(Config), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n/** @nocollapse */\nIonBackButton.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: IonBackButton,\n  hostBindings: function IonBackButton_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function IonBackButton_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n  },\n  inputs: {\n    color: \"color\",\n    defaultHref: \"defaultHref\",\n    disabled: \"disabled\",\n    icon: \"icon\",\n    mode: \"mode\",\n    routerAnimation: \"routerAnimation\",\n    text: \"text\",\n    type: \"type\"\n  }\n});\nIonBackButton = __decorate([ProxyCmp({\n  inputs: BACK_BUTTON_INPUTS\n})], IonBackButton);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonBackButton, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: BACK_BUTTON_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: IonRouterOutlet,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: NavController\n    }, {\n      type: Config\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    defaultHref: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nclass RouterLinkDelegateDirective {\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n    this.routerDirection = 'forward';\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    /**\n     * This prevents the browser from\n     * performing a page reload when pressing\n     * an Ionic component with routerLink.\n     * The page reload interferes with routing\n     * and causes ion-back-button to disappear\n     * since the local history is wiped on reload.\n     */\n    ev.preventDefault();\n  }\n}\n/** @nocollapse */\nRouterLinkDelegateDirective.ɵfac = function RouterLinkDelegateDirective_Factory(t) {\n  return new (t || RouterLinkDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n};\n/** @nocollapse */\nRouterLinkDelegateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: RouterLinkDelegateDirective,\n  selectors: [[\"\", \"routerLink\", \"\", 5, \"a\", 5, \"area\"]],\n  hostBindings: function RouterLinkDelegateDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function RouterLinkDelegateDirective_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n  },\n  inputs: {\n    routerDirection: \"routerDirection\",\n    routerAnimation: \"routerAnimation\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: ':not(a):not(area)[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass RouterLinkWithHrefDelegateDirective {\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n    this.routerDirection = 'forward';\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick() {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n  }\n}\n/** @nocollapse */\nRouterLinkWithHrefDelegateDirective.ɵfac = function RouterLinkWithHrefDelegateDirective_Factory(t) {\n  return new (t || RouterLinkWithHrefDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n};\n/** @nocollapse */\nRouterLinkWithHrefDelegateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: RouterLinkWithHrefDelegateDirective,\n  selectors: [[\"a\", \"routerLink\", \"\"], [\"area\", \"routerLink\", \"\"]],\n  hostBindings: function RouterLinkWithHrefDelegateDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function RouterLinkWithHrefDelegateDirective_click_HostBindingHandler() {\n        return ctx.onClick();\n      });\n    }\n  },\n  inputs: {\n    routerDirection: \"routerDirection\",\n    routerAnimation: \"routerAnimation\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkWithHrefDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[routerLink],area[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = ['push', 'insert', 'insertPages', 'pop', 'popTo', 'popToRoot', 'removeIndex', 'setRoot', 'setPages', 'getActive', 'getByIndex', 'canGoBack', 'getPrevious'];\nlet IonNav = class IonNav {\n  constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n    this.z = z;\n    c.detach();\n    this.el = ref.nativeElement;\n    ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n    proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n  }\n};\n/** @nocollapse */\nIonNav.ɵfac = function IonNav_Factory(t) {\n  return new (t || IonNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.EnvironmentInjector), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(AngularDelegate), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n/** @nocollapse */\nIonNav.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: IonNav,\n  inputs: {\n    animated: \"animated\",\n    animation: \"animation\",\n    root: \"root\",\n    rootParams: \"rootParams\",\n    swipeGesture: \"swipeGesture\"\n  }\n});\nIonNav = __decorate([ProxyCmp({\n  inputs: NAV_INPUTS,\n  methods: NAV_METHODS\n})], IonNav);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonNav, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: NAV_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.EnvironmentInjector\n    }, {\n      type: i0.Injector\n    }, {\n      type: AngularDelegate\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonTabs {\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n    /**\n     * Emitted before the tab view is changed.\n     */\n    this.ionTabsWillChange = new EventEmitter();\n    /**\n     * Emitted after the tab view is changed.\n     */\n    this.ionTabsDidChange = new EventEmitter();\n    this.tabBarSlot = 'bottom';\n  }\n  ngAfterContentInit() {\n    this.detectSlotChanges();\n  }\n  ngAfterContentChecked() {\n    this.detectSlotChanges();\n  }\n  /**\n   * @internal\n   */\n  onStackWillChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      this.ionTabsWillChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * @internal\n   */\n  onStackDidChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      if (this.tabBar) {\n        this.tabBar.selectedTab = stackId;\n      }\n      this.ionTabsDidChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * When a tab button is clicked, there are several scenarios:\n   * 1. If the selected tab is currently active (the tab button has been clicked\n   *    again), then it should go to the root view for that tab.\n   *\n   *   a. Get the saved root view from the router outlet. If the saved root view\n   *      matches the tabRootUrl, set the route view to this view including the\n   *      navigation extras.\n   *   b. If the saved root view from the router outlet does\n   *      not match, navigate to the tabRootUrl. No navigation extras are\n   *      included.\n   *\n   * 2. If the current tab tab is not currently selected, get the last route\n   *    view from the router outlet.\n   *\n   *   a. If the last route view exists, navigate to that view including any\n   *      navigation extras\n   *   b. If the last route view doesn't exist, then navigate\n   *      to the default tabRootUrl\n   */\n  select(tabOrEvent) {\n    const isTabString = typeof tabOrEvent === 'string';\n    const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n    const alreadySelected = this.outlet.getActiveStackId() === tab;\n    const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n    /**\n     * If this is a nested tab, prevent the event\n     * from bubbling otherwise the outer tabs\n     * will respond to this event too, causing\n     * the app to get directed to the wrong place.\n     */\n    if (!isTabString) {\n      tabOrEvent.stopPropagation();\n    }\n    if (alreadySelected) {\n      const activeStackId = this.outlet.getActiveStackId();\n      const activeView = this.outlet.getLastRouteView(activeStackId);\n      // If on root tab, do not navigate to root tab again\n      if (activeView?.url === tabRootUrl) {\n        return;\n      }\n      const rootView = this.outlet.getRootView(tab);\n      const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n      return this.navCtrl.navigateRoot(tabRootUrl, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    } else {\n      const lastRoute = this.outlet.getLastRouteView(tab);\n      /**\n       * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n       * selected tab\n       */\n      const url = lastRoute?.url || tabRootUrl;\n      const navigationExtras = lastRoute?.savedExtras;\n      return this.navCtrl.navigateRoot(url, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    }\n  }\n  getSelected() {\n    return this.outlet.getActiveStackId();\n  }\n  /**\n   * Detects changes to the slot attribute of the tab bar.\n   *\n   * If the slot attribute has changed, then the tab bar\n   * should be relocated to the new slot position.\n   */\n  detectSlotChanges() {\n    this.tabBars.forEach(tabBar => {\n      // el is a protected attribute from the generated component wrapper\n      const currentSlot = tabBar.el.getAttribute('slot');\n      if (currentSlot !== this.tabBarSlot) {\n        this.tabBarSlot = currentSlot;\n        this.relocateTabBar();\n      }\n    });\n  }\n  /**\n   * Relocates the tab bar to the new slot position.\n   */\n  relocateTabBar() {\n    /**\n     * `el` is a protected attribute from the generated component wrapper.\n     * To avoid having to manually create the wrapper for tab bar, we\n     * cast the tab bar to any and access the protected attribute.\n     */\n    const tabBar = this.tabBar.el;\n    if (this.tabBarSlot === 'top') {\n      /**\n       * A tab bar with a slot of \"top\" should be inserted\n       * at the top of the container.\n       */\n      this.tabsInner.nativeElement.before(tabBar);\n    } else {\n      /**\n       * A tab bar with a slot of \"bottom\" or without a slot\n       * should be inserted at the end of the container.\n       */\n      this.tabsInner.nativeElement.after(tabBar);\n    }\n  }\n}\n/** @nocollapse */\nIonTabs.ɵfac = function IonTabs_Factory(t) {\n  return new (t || IonTabs)(i0.ɵɵdirectiveInject(NavController));\n};\n/** @nocollapse */\nIonTabs.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: IonTabs,\n  selectors: [[\"ion-tabs\"]],\n  viewQuery: function IonTabs_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7, ElementRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabsInner = _t.first);\n    }\n  },\n  hostBindings: function IonTabs_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"ionTabButtonClick\", function IonTabs_ionTabButtonClick_HostBindingHandler($event) {\n        return ctx.select($event);\n      });\n    }\n  },\n  outputs: {\n    ionTabsWillChange: \"ionTabsWillChange\",\n    ionTabsDidChange: \"ionTabsDidChange\"\n  }\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonTabs, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-tabs'\n    }]\n  }], function () {\n    return [{\n      type: NavController\n    }];\n  }, {\n    tabsInner: [{\n      type: ViewChild,\n      args: ['tabsInner', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    ionTabsWillChange: [{\n      type: Output\n    }],\n    ionTabsDidChange: [{\n      type: Output\n    }],\n    select: [{\n      type: HostListener,\n      args: ['ionTabButtonClick', ['$event']]\n    }]\n  });\n})();\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nclass ValueAccessor {\n  constructor(injector, elementRef) {\n    this.injector = injector;\n    this.elementRef = elementRef;\n    this.onChange = () => {\n      /**/\n    };\n    this.onTouched = () => {\n      /**/\n    };\n  }\n  writeValue(value) {\n    this.elementRef.nativeElement.value = this.lastValue = value;\n    setIonicClasses(this.elementRef);\n  }\n  /**\n   * Notifies the ControlValueAccessor of a change in the value of the control.\n   *\n   * This is called by each of the ValueAccessor directives when we want to update\n   * the status and validity of the form control. For example with text components this\n   * is called when the ionInput event is fired. For select components this is called\n   * when the ionChange event is fired.\n   *\n   * This also updates the Ionic form status classes on the element.\n   *\n   * @param el The component element.\n   * @param value The new value of the control.\n   */\n  handleValueChange(el, value) {\n    if (el === this.elementRef.nativeElement) {\n      if (value !== this.lastValue) {\n        this.lastValue = value;\n        this.onChange(value);\n      }\n      setIonicClasses(this.elementRef);\n    }\n  }\n  _handleBlurEvent(el) {\n    if (el === this.elementRef.nativeElement) {\n      this.onTouched();\n      setIonicClasses(this.elementRef);\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.elementRef.nativeElement.disabled = isDisabled;\n  }\n  ngOnDestroy() {\n    if (this.statusChanges) {\n      this.statusChanges.unsubscribe();\n    }\n  }\n  ngAfterViewInit() {\n    let ngControl;\n    try {\n      ngControl = this.injector.get(NgControl);\n    } catch {\n      /* No FormControl or ngModel binding */\n    }\n    if (!ngControl) {\n      return;\n    }\n    // Listen for changes in validity, disabled, or pending states\n    if (ngControl.statusChanges) {\n      this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n    }\n    /**\n     * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n     * whenever it is implemented.\n     */\n    const formControl = ngControl.control;\n    if (formControl) {\n      const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n      methodsToPatch.forEach(method => {\n        if (typeof formControl[method] !== 'undefined') {\n          const oldFn = formControl[method].bind(formControl);\n          formControl[method] = (...params) => {\n            oldFn(...params);\n            setIonicClasses(this.elementRef);\n          };\n        }\n      });\n    }\n  }\n}\n/** @nocollapse */\nValueAccessor.ɵfac = function ValueAccessor_Factory(t) {\n  return new (t || ValueAccessor)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n/** @nocollapse */\nValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ValueAccessor,\n  hostBindings: function ValueAccessor_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"ionBlur\", function ValueAccessor_ionBlur_HostBindingHandler($event) {\n        return ctx._handleBlurEvent($event.target);\n      });\n    }\n  }\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ValueAccessor, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    _handleBlurEvent: [{\n      type: HostListener,\n      args: ['ionBlur', ['$event.target']]\n    }]\n  });\n})();\nconst setIonicClasses = element => {\n  raf(() => {\n    const input = element.nativeElement;\n    const hasValue = input.value != null && input.value.toString().length > 0;\n    const classes = getClasses(input);\n    setClasses(input, classes);\n    const item = input.closest('ion-item');\n    if (item) {\n      if (hasValue) {\n        setClasses(item, [...classes, 'item-has-value']);\n      } else {\n        setClasses(item, classes);\n      }\n    }\n  });\n};\nconst getClasses = element => {\n  const classList = element.classList;\n  const classes = [];\n  for (let i = 0; i < classList.length; i++) {\n    const item = classList.item(i);\n    if (item !== null && startsWith(item, 'ng-')) {\n      classes.push(`ion-${item.substring(3)}`);\n    }\n  }\n  return classes;\n};\nconst setClasses = (element, classes) => {\n  const classList = element.classList;\n  classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n  classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n  return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   */\n  shouldDetach(_route) {\n    return false;\n  }\n  /**\n   * Returns `false`, meaning the route (and its subtree) is never reattached\n   */\n  shouldAttach(_route) {\n    return false;\n  }\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n  store(_route, _detachedTree) {\n    return;\n  }\n  /**\n   * Returns `null` because this strategy does not store routes for later re-use.\n   */\n  retrieve(_route) {\n    return null;\n  }\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and\n   * current route config are identical and all route parameters are identical.\n   */\n  shouldReuseRoute(future, curr) {\n    if (future.routeConfig !== curr.routeConfig) {\n      return false;\n    }\n    // checking router params\n    const futureParams = future.params;\n    const currentParams = curr.params;\n    const keysA = Object.keys(futureParams);\n    const keysB = Object.keys(currentParams);\n    if (keysA.length !== keysB.length) {\n      return false;\n    }\n    // Test for A's keys different from B.\n    for (const key of keysA) {\n      if (currentParams[key] !== futureParams[key]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n  constructor(ctrl) {\n    this.ctrl = ctrl;\n  }\n  /**\n   * Creates a new overlay\n   */\n  create(opts) {\n    return this.ctrl.create(opts || {});\n  }\n  /**\n   * When `id` is not provided, it dismisses the top overlay.\n   */\n  dismiss(data, role, id) {\n    return this.ctrl.dismiss(data, role, id);\n  }\n  /**\n   * Returns the top overlay.\n   */\n  getTop() {\n    return this.ctrl.getTop();\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "Optional", "InjectionToken", "inject", "NgZone", "ApplicationRef", "Injector", "createComponent", "TemplateRef", "Directive", "ContentChild", "EventEmitter", "ViewContainerRef", "EnvironmentInjector", "Attribute", "SkipSelf", "Input", "Output", "reflectComponentType", "HostListener", "ElementRef", "ViewChild", "i3", "NavigationStart", "PRIMARY_OUTLET", "ChildrenOutletContexts", "ActivatedRoute", "Router", "i1", "DOCUMENT", "isPlatform", "getPlatforms", "LIFECYCLE_WILL_ENTER", "LIFECYCLE_DID_ENTER", "LIFECYCLE_WILL_LEAVE", "LIFECYCLE_DID_LEAVE", "LIFECYCLE_WILL_UNLOAD", "componentOnReady", "Subject", "fromEvent", "BehaviorSubject", "combineLatest", "of", "__decorate", "filter", "switchMap", "distinctUntilChanged", "NgControl", "_c0", "MenuController", "constructor", "menuController", "open", "menuId", "close", "toggle", "enable", "shouldEnable", "swipeGesture", "isOpen", "isEnabled", "get", "get<PERSON>pen", "getMenus", "registerAnimation", "name", "animation", "isAnimating", "_getOpenSync", "_createAnimation", "type", "menuCmp", "_register", "menu", "_unregister", "_setOpen", "shouldOpen", "animated", "DomController", "read", "cb", "getQueue", "write", "ɵfac", "DomController_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "win", "window", "<PERSON><PERSON>", "queue", "requestAnimationFrame", "Platform", "doc", "zone", "backButton", "keyboardDidShow", "keyboardDidHide", "pause", "resume", "resize", "run", "defaultView", "subscribeWithPriority", "priority", "callback", "subscribe", "ev", "register", "processNextHandler", "proxyEvent", "readyResolve", "_readyPromise", "Promise", "res", "addEventListener", "once", "is", "platformName", "platforms", "ready", "isRTL", "dir", "getQueryParam", "key", "readQueryParam", "location", "href", "isLandscape", "isPortrait", "matchMedia", "matches", "testUserAgent", "expression", "nav", "navigator", "userAgent", "indexOf", "url", "width", "innerWidth", "height", "innerHeight", "Platform_Factory", "ɵɵinject", "undefined", "decorators", "replace", "regex", "RegExp", "results", "exec", "decodeURIComponent", "emitter", "el", "eventName", "value", "detail", "next", "NavController", "platform", "serializer", "router", "direction", "DEFAULT_DIRECTION", "DEFAULT_ANIMATED", "guessDirection", "lastNavId", "events", "id", "restoredState", "navigationId", "guessAnimation", "pop", "navigateForward", "options", "setDirection", "animationDirection", "navigate", "navigateBack", "navigateRoot", "back", "_this", "_asyncToGenerator", "outlet", "topOutlet", "parentOutlet", "animationBuilder", "getAnimation", "setTopOutlet", "consumeTransition", "Array", "isArray", "urlTree", "parse", "toString", "queryParams", "fragment", "navigateByUrl", "NavController_Factory", "Location", "UrlSerializer", "Config", "fallback", "c", "getConfig", "getBoolean", "getNumber", "Config_Factory", "ConfigToken", "config", "NavParams", "data", "param", "AngularDelegate", "applicationRef", "create", "environmentInjector", "injector", "elementReferenceKey", "AngularFrameworkDelegate", "AngularDelegate_Factory", "elRefMap", "WeakMap", "elEventsMap", "attachViewToDom", "container", "component", "params", "cssClasses", "resolve", "componentProps", "attachView", "removeViewFromDom", "_container", "componentRef", "destroy", "delete", "unbindEvents", "childInjector", "providers", "getProviders", "parent", "elementInjector", "instance", "hostElement", "nativeElement", "console", "error", "tagName", "toLowerCase", "Object", "assign", "cssClass", "classList", "add", "bindLifecycleEvents", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "set", "LIFECYCLES", "element", "unregisters", "map", "handler", "removeEventListener", "for<PERSON>ach", "fn", "NavParamsToken", "provide", "useValue", "useFactory", "provideNavParamsInjectable", "deps", "proxyInputs", "Cmp", "inputs", "Prototype", "prototype", "item", "defineProperty", "val", "z", "runOutsideAngular", "proxyMethods", "methods", "methodName", "arguments", "apply", "proxyOutputs", "ProxyCmp", "opts", "decorator", "cls", "defineCustomElementFn", "POPOVER_INPUTS", "POPOVER_METHODS", "IonPopover", "r", "isCmpOpen", "detectChanges", "IonPopover_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "selectors", "contentQueries", "IonPopover_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "template", "first", "alignment", "arrow", "keepContentsMounted", "<PERSON><PERSON><PERSON><PERSON>", "dismissOnSelect", "enterAnimation", "event", "keyboardClose", "leaveAnimation", "mode", "showBackdrop", "translucent", "trigger", "triggerAction", "reference", "size", "side", "selector", "static", "MODAL_INPUTS", "MODAL_METHODS", "IonModal", "IonModal_Factory", "IonModal_ContentQueries", "backdropBreakpoint", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>", "handle", "handleBehavior", "initialBreakpoint", "presentingElement", "insertView", "views", "view", "setRoot", "setForward", "setBack", "v", "stackId", "push", "index", "getUrl", "activatedRoute", "createUrlTree", "relativeTo", "serializeUrl", "isTabSwitch", "enteringView", "leavingView", "computeStackId", "prefixUrl", "segments", "toSegments", "i", "length", "path", "split", "s", "trim", "destroyView", "ref", "unlistenEvents", "StackController", "tabsPrefix", "containerEl", "navCtrl", "skipTransition", "nextId", "createView", "getExistingView", "activatedUrlKey", "find", "vw", "changeDetectorRef", "reattach", "setActive", "consumeResult", "activeView", "tabSwitch", "viewsSnapshot", "slice", "currentNavigation", "getCurrentNavigation", "navigations", "extras", "replaceUrl", "splice", "reused", "includes", "customAnimation", "wait", "detach", "transition", "canGoBack", "then", "cleanupAsync", "deep", "getActiveStackId", "getStack", "viewSavedData", "savedData", "primaryOutlet", "route", "_routerState", "snapshot", "savedExtras", "startBackTransition", "endBackTransition", "shouldComplete", "cleanup", "getLastUrl", "getRootUrl", "getActiveView", "hasRunningTask", "runningTask", "showGoBack", "progressAnimation", "enteringEl", "leavingEl", "parentElement", "commit", "duration", "task", "_this2", "promise", "finally", "activeRoute", "locationWithoutParams", "locationWithoutFragment", "setAttribute", "IonRouterOutlet", "tabs", "commonLocation", "elementRef", "activatedView", "proxyMap", "currentActivatedRoute$", "activated", "_activatedRoute", "stackWillChange", "stackDidChange", "activateEvents", "deactivateEvents", "parentContexts", "inputBinder", "INPUT_BINDER", "optional", "supportsBindingToComponentInputs", "nativeEl", "stackCtrl", "onChildOutletCreated", "activatedComponentRef", "swipe", "_swipeGesture", "swi<PERSON><PERSON><PERSON><PERSON>", "canStart", "onStart", "onEnd", "shouldC<PERSON><PERSON>ue", "ngOnDestroy", "unsubscribeFromRouteData", "getContext", "ngOnInit", "initializeOutletWithName", "context", "activateWith", "isActivated", "Error", "activatedRouteData", "attach", "_ref", "deactivate", "Map", "children", "contextSnapshot", "emit", "cmpRef", "saved", "updateActivatedRouteProxy", "_futureSnapshot", "childContexts", "getOrCreateContext", "component$", "activatedRouteProxy", "createActivatedRouteProxy", "OutletInjector", "routeConfig", "bindActivatedRouteToOutletComponent", "active", "getLastRouteView", "getRootView", "proxy", "_paramMap", "proxyObservable", "_queryParamMap", "pipe", "current", "IonRouterOutlet_Factory", "ɵɵinjectAttribute", "outputs", "exportAs", "notFoundValue", "RoutedComponentInputBinder", "outletDataSubscriptions", "subscribeToRouteData", "unsubscribe", "dataSubscription", "mirror", "templateName", "setInput", "RoutedComponentInputBinder_Factory", "provideComponentInputBinding", "componentInputBindingFactory", "componentInputBindingEnabled", "BACK_BUTTON_INPUTS", "IonBackButton", "routerOutlet", "onClick", "defaultHref", "routerAnimation", "preventDefault", "IonBackButton_Factory", "hostBindings", "IonBackButton_HostBindings", "ɵɵlistener", "IonBackButton_click_HostBindingHandler", "$event", "color", "disabled", "icon", "text", "RouterLinkDelegateDirective", "locationStrategy", "routerLink", "routerDirection", "updateTargetUrlAndHref", "ngOnChanges", "prepareExternalUrl", "RouterLinkDelegateDirective_Factory", "LocationStrategy", "RouterLink", "RouterLinkDelegateDirective_HostBindings", "RouterLinkDelegateDirective_click_HostBindingHandler", "features", "ɵɵNgOnChangesFeature", "RouterLinkWithHrefDelegateDirective", "RouterLinkWithHrefDelegateDirective_Factory", "RouterLinkWithHrefDelegateDirective_HostBindings", "RouterLinkWithHrefDelegateDirective_click_HostBindingHandler", "NAV_INPUTS", "NAV_METHODS", "IonNav", "angularDelegate", "delegate", "IonNav_Factory", "root", "rootParams", "IonTabs", "ionTabsWillChange", "ionTabsDidChange", "tabBarSlot", "ngAfterContentInit", "detectSlotChanges", "ngAfterContentChecked", "onStackWillChange", "tab", "onStackDidChange", "tabBar", "selectedTab", "select", "tabOrEvent", "isTabString", "alreadySelected", "tabRootUrl", "stopPropagation", "activeStackId", "rootView", "navigationExtras", "lastRoute", "getSelected", "tabBars", "currentSlot", "getAttribute", "relocateTabBar", "tabsInner", "before", "after", "IonTabs_Factory", "viewQuery", "IonTabs_Query", "ɵɵviewQuery", "IonTabs_HostBindings", "IonTabs_ionTabButtonClick_HostBindingHandler", "raf", "h", "__zone_symbol__requestAnimationFrame", "setTimeout", "ValueAccessor", "onChange", "onTouched", "writeValue", "lastValue", "setIonicClasses", "handleValueChange", "_handleBlurEvent", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "statusChanges", "ngAfterViewInit", "ngControl", "formControl", "control", "methodsToPatch", "method", "oldFn", "bind", "ValueAccessor_Factory", "ValueAccessor_HostBindings", "ValueAccessor_ionBlur_HostBindingHandler", "target", "input", "hasValue", "classes", "getClasses", "setClasses", "closest", "startsWith", "substring", "remove", "search", "IonicRouteStrategy", "<PERSON><PERSON><PERSON><PERSON>", "_route", "<PERSON><PERSON><PERSON><PERSON>", "store", "_detachedTree", "retrieve", "shouldReuseRoute", "future", "curr", "futureParams", "currentParams", "keysA", "keys", "keysB", "OverlayBaseController", "ctrl", "dismiss", "role", "getTop"], "sources": ["E:/Fahion/DFashion/frontend/node_modules/@ionic/angular/fesm2020/ionic-angular-common.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\n\nclass MenuController {\n    constructor(menuController) {\n        this.menuController = menuController;\n    }\n    /**\n     * Programmatically open the Menu.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu is fully opened\n     */\n    open(menuId) {\n        return this.menuController.open(menuId);\n    }\n    /**\n     * Programmatically close the Menu. If no `menuId` is given as the first\n     * argument then it'll close any menu which is open. If a `menuId`\n     * is given then it'll close that exact menu.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu is fully closed\n     */\n    close(menuId) {\n        return this.menuController.close(menuId);\n    }\n    /**\n     * Toggle the menu. If it's closed, it will open, and if opened, it\n     * will close.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu has been toggled\n     */\n    toggle(menuId) {\n        return this.menuController.toggle(menuId);\n    }\n    /**\n     * Used to enable or disable a menu. For example, there could be multiple\n     * left menus, but only one of them should be able to be opened at the same\n     * time. If there are multiple menus on the same side, then enabling one menu\n     * will also automatically disable all the others that are on the same side.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu, which is useful for chaining.\n     */\n    enable(shouldEnable, menuId) {\n        return this.menuController.enable(shouldEnable, menuId);\n    }\n    /**\n     * Used to enable or disable the ability to swipe open the menu.\n     * @param shouldEnable  True if it should be swipe-able, false if not.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu, which is useful for chaining.\n     */\n    swipeGesture(shouldEnable, menuId) {\n        return this.menuController.swipeGesture(shouldEnable, menuId);\n    }\n    /**\n     * @param [menuId] Optionally get the menu by its id, or side.\n     * @return Returns true if the specified menu is currently open, otherwise false.\n     * If the menuId is not specified, it returns true if ANY menu is currenly open.\n     */\n    isOpen(menuId) {\n        return this.menuController.isOpen(menuId);\n    }\n    /**\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns true if the menu is currently enabled, otherwise false.\n     */\n    isEnabled(menuId) {\n        return this.menuController.isEnabled(menuId);\n    }\n    /**\n     * Used to get a menu instance. If a `menuId` is not provided then it'll\n     * return the first menu found. If a `menuId` is `left` or `right`, then\n     * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n     * provided, then it'll try to find the menu using the menu's `id`\n     * property. If a menu is not found then it'll return `null`.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu if found, otherwise `null`.\n     */\n    get(menuId) {\n        return this.menuController.get(menuId);\n    }\n    /**\n     * @return Returns the instance of the menu already opened, otherwise `null`.\n     */\n    getOpen() {\n        return this.menuController.getOpen();\n    }\n    /**\n     * @return Returns an array of all menu instances.\n     */\n    getMenus() {\n        return this.menuController.getMenus();\n    }\n    registerAnimation(name, animation) {\n        return this.menuController.registerAnimation(name, animation);\n    }\n    isAnimating() {\n        return this.menuController.isAnimating();\n    }\n    _getOpenSync() {\n        return this.menuController._getOpenSync();\n    }\n    _createAnimation(type, menuCmp) {\n        return this.menuController._createAnimation(type, menuCmp);\n    }\n    _register(menu) {\n        return this.menuController._register(menu);\n    }\n    _unregister(menu) {\n        return this.menuController._unregister(menu);\n    }\n    _setOpen(menu, shouldOpen, animated) {\n        return this.menuController._setOpen(menu, shouldOpen, animated);\n    }\n}\n\nclass DomController {\n    /**\n     * Schedules a task to run during the READ phase of the next frame.\n     * This task should only read the DOM, but never modify it.\n     */\n    read(cb) {\n        getQueue().read(cb);\n    }\n    /**\n     * Schedules a task to run during the WRITE phase of the next frame.\n     * This task should write the DOM, but never READ it.\n     */\n    write(cb) {\n        getQueue().write(cb);\n    }\n}\n/** @nocollapse */ DomController.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: DomController, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ DomController.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: DomController, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: DomController, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\nconst getQueue = () => {\n    const win = typeof window !== 'undefined' ? window : null;\n    if (win != null) {\n        const Ionic = win.Ionic;\n        if (Ionic?.queue) {\n            return Ionic.queue;\n        }\n        return {\n            read: (cb) => win.requestAnimationFrame(cb),\n            write: (cb) => win.requestAnimationFrame(cb),\n        };\n    }\n    return {\n        read: (cb) => cb(),\n        write: (cb) => cb(),\n    };\n};\n\nclass Platform {\n    constructor(doc, zone) {\n        this.doc = doc;\n        /**\n         * @hidden\n         */\n        this.backButton = new Subject();\n        /**\n         * The keyboardDidShow event emits when the\n         * on-screen keyboard is presented.\n         */\n        this.keyboardDidShow = new Subject();\n        /**\n         * The keyboardDidHide event emits when the\n         * on-screen keyboard is hidden.\n         */\n        this.keyboardDidHide = new Subject();\n        /**\n         * The pause event emits when the native platform puts the application\n         * into the background, typically when the user switches to a different\n         * application. This event would emit when a Cordova app is put into\n         * the background, however, it would not fire on a standard web browser.\n         */\n        this.pause = new Subject();\n        /**\n         * The resume event emits when the native platform pulls the application\n         * out from the background. This event would emit when a Cordova app comes\n         * out from the background, however, it would not fire on a standard web browser.\n         */\n        this.resume = new Subject();\n        /**\n         * The resize event emits when the browser window has changed dimensions. This\n         * could be from a browser window being physically resized, or from a device\n         * changing orientation.\n         */\n        this.resize = new Subject();\n        zone.run(() => {\n            this.win = doc.defaultView;\n            this.backButton.subscribeWithPriority = function (priority, callback) {\n                return this.subscribe((ev) => {\n                    return ev.register(priority, (processNextHandler) => zone.run(() => callback(processNextHandler)));\n                });\n            };\n            proxyEvent(this.pause, doc, 'pause', zone);\n            proxyEvent(this.resume, doc, 'resume', zone);\n            proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n            proxyEvent(this.resize, this.win, 'resize', zone);\n            proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n            proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n            let readyResolve;\n            this._readyPromise = new Promise((res) => {\n                readyResolve = res;\n            });\n            if (this.win?.['cordova']) {\n                doc.addEventListener('deviceready', () => {\n                    readyResolve('cordova');\n                }, { once: true });\n            }\n            else {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                readyResolve('dom');\n            }\n        });\n    }\n    /**\n     * @returns returns true/false based on platform.\n     * @description\n     * Depending on the platform the user is on, `is(platformName)` will\n     * return `true` or `false`. Note that the same app can return `true`\n     * for more than one platform name. For example, an app running from\n     * an iPad would return `true` for the platform names: `mobile`,\n     * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n     * from Cordova then `cordova` would be true, and if it was running\n     * from a web browser on the iPad then `mobileweb` would be `true`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     if (this.platform.is('ios')) {\n     *       // This will only print when on iOS\n     *       console.log('I am an iOS device!');\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * | Platform Name   | Description                        |\n     * |-----------------|------------------------------------|\n     * | android         | on a device running Android.       |\n     * | capacitor       | on a device running Capacitor.     |\n     * | cordova         | on a device running Cordova.       |\n     * | ios             | on a device running iOS.           |\n     * | ipad            | on an iPad device.                 |\n     * | iphone          | on an iPhone device.               |\n     * | phablet         | on a phablet device.               |\n     * | tablet          | on a tablet device.                |\n     * | electron        | in Electron on a desktop device.   |\n     * | pwa             | as a PWA app.                      |\n     * | mobile          | on a mobile device.                |\n     * | mobileweb       | on a mobile device in a browser.   |\n     * | desktop         | on a desktop device.               |\n     * | hybrid          | is a cordova or capacitor app.     |\n     *\n     */\n    is(platformName) {\n        return isPlatform(this.win, platformName);\n    }\n    /**\n     * @returns the array of platforms\n     * @description\n     * Depending on what device you are on, `platforms` can return multiple values.\n     * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n     * it would return `mobile`, `ios`, and `iphone`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     // This will print an array of the current platforms\n     *     console.log(this.platform.platforms());\n     *   }\n     * }\n     * ```\n     */\n    platforms() {\n        return getPlatforms(this.win);\n    }\n    /**\n     * Returns a promise when the platform is ready and native functionality\n     * can be called. If the app is running from within a web browser, then\n     * the promise will resolve when the DOM is ready. When the app is running\n     * from an application engine such as Cordova, then the promise will\n     * resolve when Cordova triggers the `deviceready` event.\n     *\n     * The resolved value is the `readySource`, which states which platform\n     * ready was used. For example, when Cordova is ready, the resolved ready\n     * source is `cordova`. The default ready source value will be `dom`. The\n     * `readySource` is useful if different logic should run depending on the\n     * platform the app is running from. For example, only Cordova can execute\n     * the status bar plugin, so the web should not run status bar plugin logic.\n     *\n     * ```\n     * import { Component } from '@angular/core';\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyApp {\n     *   constructor(public platform: Platform) {\n     *     this.platform.ready().then((readySource) => {\n     *       console.log('Platform ready from', readySource);\n     *       // Platform now ready, execute any required native code\n     *     });\n     *   }\n     * }\n     * ```\n     */\n    ready() {\n        return this._readyPromise;\n    }\n    /**\n     * Returns if this app is using right-to-left language direction or not.\n     * We recommend the app's `index.html` file already has the correct `dir`\n     * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n     * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n     */\n    get isRTL() {\n        return this.doc.dir === 'rtl';\n    }\n    /**\n     * Get the query string parameter\n     */\n    getQueryParam(key) {\n        return readQueryParam(this.win.location.href, key);\n    }\n    /**\n     * Returns `true` if the app is in landscape mode.\n     */\n    isLandscape() {\n        return !this.isPortrait();\n    }\n    /**\n     * Returns `true` if the app is in portrait mode.\n     */\n    isPortrait() {\n        return this.win.matchMedia?.('(orientation: portrait)').matches;\n    }\n    testUserAgent(expression) {\n        const nav = this.win.navigator;\n        return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n    }\n    /**\n     * Get the current url.\n     */\n    url() {\n        return this.win.location.href;\n    }\n    /**\n     * Gets the width of the platform's viewport using `window.innerWidth`.\n     */\n    width() {\n        return this.win.innerWidth;\n    }\n    /**\n     * Gets the height of the platform's viewport using `window.innerHeight`.\n     */\n    height() {\n        return this.win.innerHeight;\n    }\n}\n/** @nocollapse */ Platform.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: Platform, deps: [{ token: DOCUMENT }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ Platform.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: Platform, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }]; } });\nconst readQueryParam = (url, key) => {\n    key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n    const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n    const results = regex.exec(url);\n    return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n    if (el) {\n        el.addEventListener(eventName, (ev) => {\n            /**\n             * `zone.run` is required to make sure that we are running inside the Angular zone\n             * at all times. This is necessary since an app that has Capacitor will\n             * override the `document.addEventListener` with its own implementation.\n             * The override causes the event to no longer be in the Angular zone.\n             */\n            zone.run(() => {\n                // ?? cordova might emit \"null\" events\n                const value = ev != null ? ev.detail : undefined;\n                emitter.next(value);\n            });\n        });\n    }\n};\n\nclass NavController {\n    constructor(platform, location, serializer, router) {\n        this.location = location;\n        this.serializer = serializer;\n        this.router = router;\n        this.direction = DEFAULT_DIRECTION;\n        this.animated = DEFAULT_ANIMATED;\n        this.guessDirection = 'forward';\n        this.lastNavId = -1;\n        // Subscribe to router events to detect direction\n        if (router) {\n            router.events.subscribe((ev) => {\n                if (ev instanceof NavigationStart) {\n                    const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n                    this.guessDirection = id < this.lastNavId ? 'back' : 'forward';\n                    this.guessAnimation = !ev.restoredState ? this.guessDirection : undefined;\n                    this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n                }\n            });\n        }\n        // Subscribe to backButton events\n        platform.backButton.subscribeWithPriority(0, (processNextHandler) => {\n            this.pop();\n            processNextHandler();\n        });\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n     *\n     * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n     * and that it will show a \"forward\" animation by default.\n     *\n     * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n     * ```\n     */\n    navigateForward(url, options = {}) {\n        this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('back');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n     * and that it will show a \"back\" animation by default.\n     *\n     * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n     * ```\n     */\n    navigateBack(url, options = {}) {\n        this.setDirection('back', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('root');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **root** means that all existing pages in the stack will be removed,\n     * and the navigated page will become the single page in the stack.\n     *\n     * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n     * ```\n     */\n    navigateRoot(url, options = {}) {\n        this.setDirection('root', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n     * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n     * by default.\n     */\n    back(options = { animated: true, animationDirection: 'back' }) {\n        this.setDirection('back', options.animated, options.animationDirection, options.animation);\n        return this.location.back();\n    }\n    /**\n     * This methods goes back in the context of Ionic's stack navigation.\n     *\n     * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n     * This is the recommended way to go back when you are using `ion-router-outlet`.\n     *\n     * Resolves to `true` if it was able to pop.\n     */\n    async pop() {\n        let outlet = this.topOutlet;\n        while (outlet) {\n            if (await outlet.pop()) {\n                return true;\n            }\n            else {\n                outlet = outlet.parentOutlet;\n            }\n        }\n        return false;\n    }\n    /**\n     * This methods specifies the direction of the next navigation performed by the Angular router.\n     *\n     * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n     *\n     * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n     */\n    setDirection(direction, animated, animationDirection, animationBuilder) {\n        this.direction = direction;\n        this.animated = getAnimation(direction, animated, animationDirection);\n        this.animationBuilder = animationBuilder;\n    }\n    /**\n     * @internal\n     */\n    setTopOutlet(outlet) {\n        this.topOutlet = outlet;\n    }\n    /**\n     * @internal\n     */\n    consumeTransition() {\n        let direction = 'root';\n        let animation;\n        const animationBuilder = this.animationBuilder;\n        if (this.direction === 'auto') {\n            direction = this.guessDirection;\n            animation = this.guessAnimation;\n        }\n        else {\n            animation = this.animated;\n            direction = this.direction;\n        }\n        this.direction = DEFAULT_DIRECTION;\n        this.animated = DEFAULT_ANIMATED;\n        this.animationBuilder = undefined;\n        return {\n            direction,\n            animation,\n            animationBuilder,\n        };\n    }\n    navigate(url, options) {\n        if (Array.isArray(url)) {\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            return this.router.navigate(url, options);\n        }\n        else {\n            /**\n             * navigateByUrl ignores any properties that\n             * would change the url, so things like queryParams\n             * would be ignored unless we create a url tree\n             * More Info: https://github.com/angular/angular/issues/18798\n             */\n            const urlTree = this.serializer.parse(url.toString());\n            if (options.queryParams !== undefined) {\n                urlTree.queryParams = { ...options.queryParams };\n            }\n            if (options.fragment !== undefined) {\n                urlTree.fragment = options.fragment;\n            }\n            /**\n             * `navigateByUrl` will still apply `NavigationExtras` properties\n             * that do not modify the url, such as `replaceUrl` which is why\n             * `options` is passed in here.\n             */\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            return this.router.navigateByUrl(urlTree, options);\n        }\n    }\n}\n/** @nocollapse */ NavController.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: NavController, deps: [{ token: Platform }, { token: i1.Location }, { token: i3.UrlSerializer }, { token: i3.Router, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ NavController.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: NavController, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: NavController, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: Platform }, { type: i1.Location }, { type: i3.UrlSerializer }, { type: i3.Router, decorators: [{\n                    type: Optional\n                }] }]; } });\nconst getAnimation = (direction, animated, animationDirection) => {\n    if (animated === false) {\n        return undefined;\n    }\n    if (animationDirection !== undefined) {\n        return animationDirection;\n    }\n    if (direction === 'forward' || direction === 'back') {\n        return direction;\n    }\n    else if (direction === 'root' && animated === true) {\n        return 'forward';\n    }\n    return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\n\nclass Config {\n    get(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.get(key, fallback);\n        }\n        return null;\n    }\n    getBoolean(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.getBoolean(key, fallback);\n        }\n        return false;\n    }\n    getNumber(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.getNumber(key, fallback);\n        }\n        return 0;\n    }\n}\n/** @nocollapse */ Config.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: Config, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ Config.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: Config, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: Config, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n    if (typeof window !== 'undefined') {\n        const Ionic = window.Ionic;\n        if (Ionic?.config) {\n            return Ionic.config;\n        }\n    }\n    return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n    constructor(data = {}) {\n        this.data = data;\n    }\n    /**\n     * Get the value of a nav-parameter for the current view\n     *\n     * ```ts\n     * import { NavParams } from 'ionic-angular';\n     *\n     * export class MyClass{\n     *  constructor(public navParams: NavParams){\n     *    // userParams is an object we have in our nav-parameters\n     *    this.navParams.get('userParams');\n     *  }\n     * }\n     * ```\n     *\n     * @param param Which param you want to look up\n     */\n    get(param) {\n        return this.data[param];\n    }\n}\n\n// TODO(FW-2827): types\nclass AngularDelegate {\n    constructor() {\n        this.zone = inject(NgZone);\n        this.applicationRef = inject(ApplicationRef);\n    }\n    create(environmentInjector, injector, elementReferenceKey) {\n        return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey);\n    }\n}\n/** @nocollapse */ AngularDelegate.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: AngularDelegate, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ AngularDelegate.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: AngularDelegate });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: AngularDelegate, decorators: [{\n            type: Injectable\n        }] });\nclass AngularFrameworkDelegate {\n    constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey) {\n        this.environmentInjector = environmentInjector;\n        this.injector = injector;\n        this.applicationRef = applicationRef;\n        this.zone = zone;\n        this.elementReferenceKey = elementReferenceKey;\n        this.elRefMap = new WeakMap();\n        this.elEventsMap = new WeakMap();\n    }\n    attachViewToDom(container, component, params, cssClasses) {\n        return this.zone.run(() => {\n            return new Promise((resolve) => {\n                const componentProps = {\n                    ...params,\n                };\n                /**\n                 * Ionic Angular passes a reference to a modal\n                 * or popover that can be accessed using a\n                 * variable in the overlay component. If\n                 * elementReferenceKey is defined, then we should\n                 * pass a reference to the component using\n                 * elementReferenceKey as the key.\n                 */\n                if (this.elementReferenceKey !== undefined) {\n                    componentProps[this.elementReferenceKey] = container;\n                }\n                const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey);\n                resolve(el);\n            });\n        });\n    }\n    removeViewFromDom(_container, component) {\n        return this.zone.run(() => {\n            return new Promise((resolve) => {\n                const componentRef = this.elRefMap.get(component);\n                if (componentRef) {\n                    componentRef.destroy();\n                    this.elRefMap.delete(component);\n                    const unbindEvents = this.elEventsMap.get(component);\n                    if (unbindEvents) {\n                        unbindEvents();\n                        this.elEventsMap.delete(component);\n                    }\n                }\n                resolve();\n            });\n        });\n    }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey) => {\n    /**\n     * Wraps the injector with a custom injector that\n     * provides NavParams to the component.\n     *\n     * NavParams is a legacy feature from Ionic v3 that allows\n     * Angular developers to provide data to a component\n     * and access it by providing NavParams as a dependency\n     * in the constructor.\n     *\n     * The modern approach is to access the data directly\n     * from the component's class instance.\n     */\n    const childInjector = Injector.create({\n        providers: getProviders(params),\n        parent: injector,\n    });\n    const componentRef = createComponent(component, {\n        environmentInjector,\n        elementInjector: childInjector,\n    });\n    const instance = componentRef.instance;\n    const hostElement = componentRef.location.nativeElement;\n    if (params) {\n        /**\n         * For modals and popovers, a reference to the component is\n         * added to `params` during the call to attachViewToDom. If\n         * a reference using this name is already set, this means\n         * the app is trying to use the name as a component prop,\n         * which will cause collisions.\n         */\n        if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n            console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n        }\n        Object.assign(instance, params);\n    }\n    if (cssClasses) {\n        for (const cssClass of cssClasses) {\n            hostElement.classList.add(cssClass);\n        }\n    }\n    const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n    container.appendChild(hostElement);\n    applicationRef.attachView(componentRef.hostView);\n    elRefMap.set(hostElement, componentRef);\n    elEventsMap.set(hostElement, unbindEvents);\n    return hostElement;\n};\nconst LIFECYCLES = [\n    LIFECYCLE_WILL_ENTER,\n    LIFECYCLE_DID_ENTER,\n    LIFECYCLE_WILL_LEAVE,\n    LIFECYCLE_DID_LEAVE,\n    LIFECYCLE_WILL_UNLOAD,\n];\nconst bindLifecycleEvents = (zone, instance, element) => {\n    return zone.run(() => {\n        const unregisters = LIFECYCLES.filter((eventName) => typeof instance[eventName] === 'function').map((eventName) => {\n            const handler = (ev) => instance[eventName](ev.detail);\n            element.addEventListener(eventName, handler);\n            return () => element.removeEventListener(eventName, handler);\n        });\n        return () => unregisters.forEach((fn) => fn());\n    });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = (params) => {\n    return [\n        {\n            provide: NavParamsToken,\n            useValue: params,\n        },\n        {\n            provide: NavParams,\n            useFactory: provideNavParamsInjectable,\n            deps: [NavParamsToken],\n        },\n    ];\n};\nconst provideNavParamsInjectable = (params) => {\n    return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\nconst proxyInputs = (Cmp, inputs) => {\n    const Prototype = Cmp.prototype;\n    inputs.forEach((item) => {\n        Object.defineProperty(Prototype, item, {\n            get() {\n                return this.el[item];\n            },\n            set(val) {\n                this.z.runOutsideAngular(() => (this.el[item] = val));\n            },\n        });\n    });\n};\nconst proxyMethods = (Cmp, methods) => {\n    const Prototype = Cmp.prototype;\n    methods.forEach((methodName) => {\n        Prototype[methodName] = function () {\n            const args = arguments;\n            return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n        };\n    });\n};\nconst proxyOutputs = (instance, el, events) => {\n    events.forEach((eventName) => (instance[eventName] = fromEvent(el, eventName)));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n    const decorator = function (cls) {\n        const { defineCustomElementFn, inputs, methods } = opts;\n        if (defineCustomElementFn !== undefined) {\n            defineCustomElementFn();\n        }\n        if (inputs) {\n            proxyInputs(cls, inputs);\n        }\n        if (methods) {\n            proxyMethods(cls, methods);\n        }\n        return cls;\n    };\n    return decorator;\n}\n\nconst POPOVER_INPUTS = [\n    'alignment',\n    'animated',\n    'arrow',\n    'keepContentsMounted',\n    'backdropDismiss',\n    'cssClass',\n    'dismissOnSelect',\n    'enterAnimation',\n    'event',\n    'isOpen',\n    'keyboardClose',\n    'leaveAnimation',\n    'mode',\n    'showBackdrop',\n    'translucent',\n    'trigger',\n    'triggerAction',\n    'reference',\n    'size',\n    'side',\n];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = class IonPopover {\n    constructor(c, r, z) {\n        this.z = z;\n        this.isCmpOpen = false;\n        this.el = r.nativeElement;\n        this.el.addEventListener('ionMount', () => {\n            this.isCmpOpen = true;\n            c.detectChanges();\n        });\n        this.el.addEventListener('didDismiss', () => {\n            this.isCmpOpen = false;\n            c.detectChanges();\n        });\n        proxyOutputs(this, this.el, [\n            'ionPopoverDidPresent',\n            'ionPopoverWillPresent',\n            'ionPopoverWillDismiss',\n            'ionPopoverDidDismiss',\n            'didPresent',\n            'willPresent',\n            'willDismiss',\n            'didDismiss',\n        ]);\n    }\n};\n/** @nocollapse */ IonPopover.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonPopover, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ IonPopover.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: IonPopover, selector: \"ion-popover\", inputs: { alignment: \"alignment\", animated: \"animated\", arrow: \"arrow\", keepContentsMounted: \"keepContentsMounted\", backdropDismiss: \"backdropDismiss\", cssClass: \"cssClass\", dismissOnSelect: \"dismissOnSelect\", enterAnimation: \"enterAnimation\", event: \"event\", isOpen: \"isOpen\", keyboardClose: \"keyboardClose\", leaveAnimation: \"leaveAnimation\", mode: \"mode\", showBackdrop: \"showBackdrop\", translucent: \"translucent\", trigger: \"trigger\", triggerAction: \"triggerAction\", reference: \"reference\", size: \"size\", side: \"side\" }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\nIonPopover = __decorate([\n    ProxyCmp({\n        inputs: POPOVER_INPUTS,\n        methods: POPOVER_METHODS,\n    })\n    /**\n     * @Component extends from @Directive\n     * so by defining the inputs here we\n     * do not need to re-define them for the\n     * lazy loaded popover.\n     */\n], IonPopover);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonPopover, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-popover',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: POPOVER_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { template: [{\n                type: ContentChild,\n                args: [TemplateRef, { static: false }]\n            }] } });\n\nconst MODAL_INPUTS = [\n    'animated',\n    'keepContentsMounted',\n    'backdropBreakpoint',\n    'backdropDismiss',\n    'breakpoints',\n    'canDismiss',\n    'cssClass',\n    'enterAnimation',\n    'event',\n    'handle',\n    'handleBehavior',\n    'initialBreakpoint',\n    'isOpen',\n    'keyboardClose',\n    'leaveAnimation',\n    'mode',\n    'presentingElement',\n    'showBackdrop',\n    'translucent',\n    'trigger',\n];\nconst MODAL_METHODS = [\n    'present',\n    'dismiss',\n    'onDidDismiss',\n    'onWillDismiss',\n    'setCurrentBreakpoint',\n    'getCurrentBreakpoint',\n];\nlet IonModal = class IonModal {\n    constructor(c, r, z) {\n        this.z = z;\n        this.isCmpOpen = false;\n        this.el = r.nativeElement;\n        this.el.addEventListener('ionMount', () => {\n            this.isCmpOpen = true;\n            c.detectChanges();\n        });\n        this.el.addEventListener('didDismiss', () => {\n            this.isCmpOpen = false;\n            c.detectChanges();\n        });\n        proxyOutputs(this, this.el, [\n            'ionModalDidPresent',\n            'ionModalWillPresent',\n            'ionModalWillDismiss',\n            'ionModalDidDismiss',\n            'ionBreakpointDidChange',\n            'didPresent',\n            'willPresent',\n            'willDismiss',\n            'didDismiss',\n        ]);\n    }\n};\n/** @nocollapse */ IonModal.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonModal, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ IonModal.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: IonModal, selector: \"ion-modal\", inputs: { animated: \"animated\", keepContentsMounted: \"keepContentsMounted\", backdropBreakpoint: \"backdropBreakpoint\", backdropDismiss: \"backdropDismiss\", breakpoints: \"breakpoints\", canDismiss: \"canDismiss\", cssClass: \"cssClass\", enterAnimation: \"enterAnimation\", event: \"event\", handle: \"handle\", handleBehavior: \"handleBehavior\", initialBreakpoint: \"initialBreakpoint\", isOpen: \"isOpen\", keyboardClose: \"keyboardClose\", leaveAnimation: \"leaveAnimation\", mode: \"mode\", presentingElement: \"presentingElement\", showBackdrop: \"showBackdrop\", translucent: \"translucent\", trigger: \"trigger\" }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\nIonModal = __decorate([\n    ProxyCmp({\n        inputs: MODAL_INPUTS,\n        methods: MODAL_METHODS,\n    })\n    /**\n     * @Component extends from @Directive\n     * so by defining the inputs here we\n     * do not need to re-define them for the\n     * lazy loaded popover.\n     */\n], IonModal);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonModal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-modal',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: MODAL_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { template: [{\n                type: ContentChild,\n                args: [TemplateRef, { static: false }]\n            }] } });\n\nconst insertView = (views, view, direction) => {\n    if (direction === 'root') {\n        return setRoot(views, view);\n    }\n    else if (direction === 'forward') {\n        return setForward(views, view);\n    }\n    else {\n        return setBack(views, view);\n    }\n};\nconst setRoot = (views, view) => {\n    views = views.filter((v) => v.stackId !== view.stackId);\n    views.push(view);\n    return views;\n};\nconst setForward = (views, view) => {\n    const index = views.indexOf(view);\n    if (index >= 0) {\n        views = views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n    }\n    else {\n        views.push(view);\n    }\n    return views;\n};\nconst setBack = (views, view) => {\n    const index = views.indexOf(view);\n    if (index >= 0) {\n        return views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n    }\n    else {\n        return setRoot(views, view);\n    }\n};\nconst getUrl = (router, activatedRoute) => {\n    const urlTree = router.createUrlTree(['.'], { relativeTo: activatedRoute });\n    return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n    if (!leavingView) {\n        return true;\n    }\n    return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n    if (!prefixUrl) {\n        return undefined;\n    }\n    const segments = toSegments(url);\n    for (let i = 0; i < segments.length; i++) {\n        if (i >= prefixUrl.length) {\n            return segments[i];\n        }\n        if (segments[i] !== prefixUrl[i]) {\n            return undefined;\n        }\n    }\n    return undefined;\n};\nconst toSegments = (path) => {\n    return path\n        .split('/')\n        .map((s) => s.trim())\n        .filter((s) => s !== '');\n};\nconst destroyView = (view) => {\n    if (view) {\n        view.ref.destroy();\n        view.unlistenEvents();\n    }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n    constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n        this.containerEl = containerEl;\n        this.router = router;\n        this.navCtrl = navCtrl;\n        this.zone = zone;\n        this.location = location;\n        this.views = [];\n        this.skipTransition = false;\n        this.nextId = 0;\n        this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n    }\n    createView(ref, activatedRoute) {\n        const url = getUrl(this.router, activatedRoute);\n        const element = ref?.location?.nativeElement;\n        const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n        return {\n            id: this.nextId++,\n            stackId: computeStackId(this.tabsPrefix, url),\n            unlistenEvents,\n            element,\n            ref,\n            url,\n        };\n    }\n    getExistingView(activatedRoute) {\n        const activatedUrlKey = getUrl(this.router, activatedRoute);\n        const view = this.views.find((vw) => vw.url === activatedUrlKey);\n        if (view) {\n            view.ref.changeDetectorRef.reattach();\n        }\n        return view;\n    }\n    setActive(enteringView) {\n        const consumeResult = this.navCtrl.consumeTransition();\n        let { direction, animation, animationBuilder } = consumeResult;\n        const leavingView = this.activeView;\n        const tabSwitch = isTabSwitch(enteringView, leavingView);\n        if (tabSwitch) {\n            direction = 'back';\n            animation = undefined;\n        }\n        const viewsSnapshot = this.views.slice();\n        let currentNavigation;\n        const router = this.router;\n        // Angular >= 7.2.0\n        if (router.getCurrentNavigation) {\n            currentNavigation = router.getCurrentNavigation();\n            // Angular < 7.2.0\n        }\n        else if (router.navigations?.value) {\n            currentNavigation = router.navigations.value;\n        }\n        /**\n         * If the navigation action\n         * sets `replaceUrl: true`\n         * then we need to make sure\n         * we remove the last item\n         * from our views stack\n         */\n        if (currentNavigation?.extras?.replaceUrl) {\n            if (this.views.length > 0) {\n                this.views.splice(-1, 1);\n            }\n        }\n        const reused = this.views.includes(enteringView);\n        const views = this.insertView(enteringView, direction);\n        // Trigger change detection before transition starts\n        // This will call ngOnInit() the first time too, just after the view\n        // was attached to the dom, but BEFORE the transition starts\n        if (!reused) {\n            enteringView.ref.changeDetectorRef.detectChanges();\n        }\n        /**\n         * If we are going back from a page that\n         * was presented using a custom animation\n         * we should default to using that\n         * unless the developer explicitly\n         * provided another animation.\n         */\n        const customAnimation = enteringView.animationBuilder;\n        if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n            animationBuilder = customAnimation;\n        }\n        /**\n         * Save any custom animation so that navigating\n         * back will use this custom animation by default.\n         */\n        if (leavingView) {\n            leavingView.animationBuilder = animationBuilder;\n        }\n        // Wait until previous transitions finish\n        return this.zone.runOutsideAngular(() => {\n            return this.wait(() => {\n                // disconnect leaving page from change detection to\n                // reduce jank during the page transition\n                if (leavingView) {\n                    leavingView.ref.changeDetectorRef.detach();\n                }\n                // In case the enteringView is the same as the leavingPage we need to reattach()\n                enteringView.ref.changeDetectorRef.reattach();\n                return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder)\n                    .then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone))\n                    .then(() => ({\n                    enteringView,\n                    direction,\n                    animation,\n                    tabSwitch,\n                }));\n            });\n        });\n    }\n    canGoBack(deep, stackId = this.getActiveStackId()) {\n        return this.getStack(stackId).length > deep;\n    }\n    pop(deep, stackId = this.getActiveStackId()) {\n        return this.zone.run(() => {\n            const views = this.getStack(stackId);\n            if (views.length <= deep) {\n                return Promise.resolve(false);\n            }\n            const view = views[views.length - deep - 1];\n            let url = view.url;\n            const viewSavedData = view.savedData;\n            if (viewSavedData) {\n                const primaryOutlet = viewSavedData.get('primary');\n                if (primaryOutlet?.route?._routerState?.snapshot.url) {\n                    url = primaryOutlet.route._routerState.snapshot.url;\n                }\n            }\n            const { animationBuilder } = this.navCtrl.consumeTransition();\n            return this.navCtrl.navigateBack(url, { ...view.savedExtras, animation: animationBuilder }).then(() => true);\n        });\n    }\n    startBackTransition() {\n        const leavingView = this.activeView;\n        if (leavingView) {\n            const views = this.getStack(leavingView.stackId);\n            const enteringView = views[views.length - 2];\n            const customAnimation = enteringView.animationBuilder;\n            return this.wait(() => {\n                return this.transition(enteringView, // entering view\n                leavingView, // leaving view\n                'back', this.canGoBack(2), true, customAnimation);\n            });\n        }\n        return Promise.resolve();\n    }\n    endBackTransition(shouldComplete) {\n        if (shouldComplete) {\n            this.skipTransition = true;\n            this.pop(1);\n        }\n        else if (this.activeView) {\n            cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n        }\n    }\n    getLastUrl(stackId) {\n        const views = this.getStack(stackId);\n        return views.length > 0 ? views[views.length - 1] : undefined;\n    }\n    /**\n     * @internal\n     */\n    getRootUrl(stackId) {\n        const views = this.getStack(stackId);\n        return views.length > 0 ? views[0] : undefined;\n    }\n    getActiveStackId() {\n        return this.activeView ? this.activeView.stackId : undefined;\n    }\n    /**\n     * @internal\n     */\n    getActiveView() {\n        return this.activeView;\n    }\n    hasRunningTask() {\n        return this.runningTask !== undefined;\n    }\n    destroy() {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        this.containerEl = undefined;\n        this.views.forEach(destroyView);\n        this.activeView = undefined;\n        this.views = [];\n    }\n    getStack(stackId) {\n        return this.views.filter((v) => v.stackId === stackId);\n    }\n    insertView(enteringView, direction) {\n        this.activeView = enteringView;\n        this.views = insertView(this.views, enteringView, direction);\n        return this.views.slice();\n    }\n    transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n        if (this.skipTransition) {\n            this.skipTransition = false;\n            return Promise.resolve(false);\n        }\n        if (leavingView === enteringView) {\n            return Promise.resolve(false);\n        }\n        const enteringEl = enteringView ? enteringView.element : undefined;\n        const leavingEl = leavingView ? leavingView.element : undefined;\n        const containerEl = this.containerEl;\n        if (enteringEl && enteringEl !== leavingEl) {\n            enteringEl.classList.add('ion-page');\n            enteringEl.classList.add('ion-page-invisible');\n            if (enteringEl.parentElement !== containerEl) {\n                containerEl.appendChild(enteringEl);\n            }\n            if (containerEl.commit) {\n                return containerEl.commit(enteringEl, leavingEl, {\n                    duration: direction === undefined ? 0 : undefined,\n                    direction,\n                    showGoBack,\n                    progressAnimation,\n                    animationBuilder,\n                });\n            }\n        }\n        return Promise.resolve(false);\n    }\n    async wait(task) {\n        if (this.runningTask !== undefined) {\n            await this.runningTask;\n            this.runningTask = undefined;\n        }\n        const promise = (this.runningTask = task());\n        promise.finally(() => (this.runningTask = undefined));\n        return promise;\n    }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n    if (typeof requestAnimationFrame === 'function') {\n        return new Promise((resolve) => {\n            requestAnimationFrame(() => {\n                cleanup(activeRoute, views, viewsSnapshot, location, zone);\n                resolve();\n            });\n        });\n    }\n    return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n    /**\n     * Re-enter the Angular zone when destroying page components. This will allow\n     * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n     */\n    zone.run(() => viewsSnapshot.filter((view) => !views.includes(view)).forEach(destroyView));\n    views.forEach((view) => {\n        /**\n         * In the event that a user navigated multiple\n         * times in rapid succession, we want to make sure\n         * we don't pre-emptively detach a view while\n         * it is in mid-transition.\n         *\n         * In this instance we also do not care about query\n         * params or fragments as it will be the same view regardless\n         */\n        const locationWithoutParams = location.path().split('?')[0];\n        const locationWithoutFragment = locationWithoutParams.split('#')[0];\n        if (view !== activeRoute && view.url !== locationWithoutFragment) {\n            const element = view.element;\n            element.setAttribute('aria-hidden', 'true');\n            element.classList.add('ion-page-hidden');\n            view.ref.changeDetectorRef.detach();\n        }\n    });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonRouterOutlet {\n    constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n        this.parentOutlet = parentOutlet;\n        this.activatedView = null;\n        // Maintain map of activated route proxies for each component instance\n        this.proxyMap = new WeakMap();\n        // Keep the latest activated route in a subject for the proxy routes to switch map to\n        this.currentActivatedRoute$ = new BehaviorSubject(null);\n        this.activated = null;\n        this._activatedRoute = null;\n        /**\n         * The name of the outlet\n         */\n        this.name = PRIMARY_OUTLET;\n        /** @internal */\n        this.stackWillChange = new EventEmitter();\n        /** @internal */\n        this.stackDidChange = new EventEmitter();\n        // eslint-disable-next-line @angular-eslint/no-output-rename\n        this.activateEvents = new EventEmitter();\n        // eslint-disable-next-line @angular-eslint/no-output-rename\n        this.deactivateEvents = new EventEmitter();\n        this.parentContexts = inject(ChildrenOutletContexts);\n        this.location = inject(ViewContainerRef);\n        this.environmentInjector = inject(EnvironmentInjector);\n        this.inputBinder = inject(INPUT_BINDER, { optional: true });\n        /** @nodoc */\n        this.supportsBindingToComponentInputs = true;\n        // Ionic providers\n        this.config = inject(Config);\n        this.navCtrl = inject(NavController);\n        this.nativeEl = elementRef.nativeElement;\n        this.name = name || PRIMARY_OUTLET;\n        this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n        this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n        this.parentContexts.onChildOutletCreated(this.name, this);\n    }\n    /** @internal */\n    get activatedComponentRef() {\n        return this.activated;\n    }\n    set animation(animation) {\n        this.nativeEl.animation = animation;\n    }\n    set animated(animated) {\n        this.nativeEl.animated = animated;\n    }\n    set swipeGesture(swipe) {\n        this._swipeGesture = swipe;\n        this.nativeEl.swipeHandler = swipe\n            ? {\n                canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n                onStart: () => this.stackCtrl.startBackTransition(),\n                onEnd: (shouldContinue) => this.stackCtrl.endBackTransition(shouldContinue),\n            }\n            : undefined;\n    }\n    ngOnDestroy() {\n        this.stackCtrl.destroy();\n        this.inputBinder?.unsubscribeFromRouteData(this);\n    }\n    getContext() {\n        return this.parentContexts.getContext(this.name);\n    }\n    ngOnInit() {\n        this.initializeOutletWithName();\n    }\n    // Note: Ionic deviates from the Angular Router implementation here\n    initializeOutletWithName() {\n        if (!this.activated) {\n            // If the outlet was not instantiated at the time the route got activated we need to populate\n            // the outlet when it is initialized (ie inside a NgIf)\n            const context = this.getContext();\n            if (context?.route) {\n                this.activateWith(context.route, context.injector);\n            }\n        }\n        new Promise((resolve) => componentOnReady(this.nativeEl, resolve)).then(() => {\n            if (this._swipeGesture === undefined) {\n                this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n            }\n        });\n    }\n    get isActivated() {\n        return !!this.activated;\n    }\n    get component() {\n        if (!this.activated) {\n            throw new Error('Outlet is not activated');\n        }\n        return this.activated.instance;\n    }\n    get activatedRoute() {\n        if (!this.activated) {\n            throw new Error('Outlet is not activated');\n        }\n        return this._activatedRoute;\n    }\n    get activatedRouteData() {\n        if (this._activatedRoute) {\n            return this._activatedRoute.snapshot.data;\n        }\n        return {};\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to detach the subtree\n     */\n    detach() {\n        throw new Error('incompatible reuse strategy');\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    attach(_ref, _activatedRoute) {\n        throw new Error('incompatible reuse strategy');\n    }\n    deactivate() {\n        if (this.activated) {\n            if (this.activatedView) {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const context = this.getContext();\n                this.activatedView.savedData = new Map(context.children['contexts']);\n                /**\n                 * Angular v11.2.10 introduced a change\n                 * where this route context is cleared out when\n                 * a router-outlet is deactivated, However,\n                 * we need this route information in order to\n                 * return a user back to the correct tab when\n                 * leaving and then going back to the tab context.\n                 */\n                const primaryOutlet = this.activatedView.savedData.get('primary');\n                if (primaryOutlet && context.route) {\n                    primaryOutlet.route = { ...context.route };\n                }\n                /**\n                 * Ensure we are saving the NavigationExtras\n                 * data otherwise it will be lost\n                 */\n                this.activatedView.savedExtras = {};\n                if (context.route) {\n                    const contextSnapshot = context.route.snapshot;\n                    this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n                    this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n                }\n            }\n            const c = this.component;\n            this.activatedView = null;\n            this.activated = null;\n            this._activatedRoute = null;\n            this.deactivateEvents.emit(c);\n        }\n    }\n    activateWith(activatedRoute, environmentInjector) {\n        if (this.isActivated) {\n            throw new Error('Cannot activate an already activated outlet');\n        }\n        this._activatedRoute = activatedRoute;\n        let cmpRef;\n        let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n        if (enteringView) {\n            cmpRef = this.activated = enteringView.ref;\n            const saved = enteringView.savedData;\n            if (saved) {\n                // self-restore\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const context = this.getContext();\n                context.children['contexts'] = saved;\n            }\n            // Updated activated route proxy for this component\n            this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n        }\n        else {\n            const snapshot = activatedRoute._futureSnapshot;\n            /**\n             * Angular 14 introduces a new `loadComponent` property to the route config.\n             * This function will assign a `component` property to the route snapshot.\n             * We check for the presence of this property to determine if the route is\n             * using standalone components.\n             */\n            const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n            // We create an activated route proxy object that will maintain future updates for this component\n            // over its lifecycle in the stack.\n            const component$ = new BehaviorSubject(null);\n            const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n            const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const component = snapshot.routeConfig.component ?? snapshot.component;\n            cmpRef = this.activated = this.location.createComponent(component, {\n                index: this.location.length,\n                injector,\n                environmentInjector: environmentInjector ?? this.environmentInjector,\n            });\n            // Once the component is created we can push it to our local subject supplied to the proxy\n            component$.next(cmpRef.instance);\n            // Calling `markForCheck` to make sure we will run the change detection when the\n            // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n            enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n            // Store references to the proxy by component\n            this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n            this.currentActivatedRoute$.next({ component: cmpRef.instance, activatedRoute });\n        }\n        this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n        this.activatedView = enteringView;\n        /**\n         * The top outlet is set prior to the entering view's transition completing,\n         * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n         * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n         * activated.\n         *\n         * In this scenario, activeWith is called for both the tabs and the root router outlet.\n         * To avoid a race condition, we assign the top outlet synchronously.\n         */\n        this.navCtrl.setTopOutlet(this);\n        const leavingView = this.stackCtrl.getActiveView();\n        this.stackWillChange.emit({\n            enteringView,\n            tabSwitch: isTabSwitch(enteringView, leavingView),\n        });\n        this.stackCtrl.setActive(enteringView).then((data) => {\n            this.activateEvents.emit(cmpRef.instance);\n            this.stackDidChange.emit(data);\n        });\n    }\n    /**\n     * Returns `true` if there are pages in the stack to go back.\n     */\n    canGoBack(deep = 1, stackId) {\n        return this.stackCtrl.canGoBack(deep, stackId);\n    }\n    /**\n     * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n     */\n    pop(deep = 1, stackId) {\n        return this.stackCtrl.pop(deep, stackId);\n    }\n    /**\n     * Returns the URL of the active page of each stack.\n     */\n    getLastUrl(stackId) {\n        const active = this.stackCtrl.getLastUrl(stackId);\n        return active ? active.url : undefined;\n    }\n    /**\n     * Returns the RouteView of the active page of each stack.\n     * @internal\n     */\n    getLastRouteView(stackId) {\n        return this.stackCtrl.getLastUrl(stackId);\n    }\n    /**\n     * Returns the root view in the tab stack.\n     * @internal\n     */\n    getRootView(stackId) {\n        return this.stackCtrl.getRootUrl(stackId);\n    }\n    /**\n     * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n     */\n    getActiveStackId() {\n        return this.stackCtrl.getActiveStackId();\n    }\n    /**\n     * Since the activated route can change over the life time of a component in an ion router outlet, we create\n     * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n     */\n    createActivatedRouteProxy(component$, activatedRoute) {\n        const proxy = new ActivatedRoute();\n        proxy._futureSnapshot = activatedRoute._futureSnapshot;\n        proxy._routerState = activatedRoute._routerState;\n        proxy.snapshot = activatedRoute.snapshot;\n        proxy.outlet = activatedRoute.outlet;\n        proxy.component = activatedRoute.component;\n        // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n        proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n        proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n        proxy.url = this.proxyObservable(component$, 'url');\n        proxy.params = this.proxyObservable(component$, 'params');\n        proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n        proxy.fragment = this.proxyObservable(component$, 'fragment');\n        proxy.data = this.proxyObservable(component$, 'data');\n        return proxy;\n    }\n    /**\n     * Create a wrapped observable that will switch to the latest activated route matched by the given component\n     */\n    proxyObservable(component$, path) {\n        return component$.pipe(\n        // First wait until the component instance is pushed\n        filter((component) => !!component), switchMap((component) => this.currentActivatedRoute$.pipe(filter((current) => current !== null && current.component === component), switchMap((current) => current && current.activatedRoute[path]), distinctUntilChanged())));\n    }\n    /**\n     * Updates the activated route proxy for the given component to the new incoming router state\n     */\n    updateActivatedRouteProxy(component, activatedRoute) {\n        const proxy = this.proxyMap.get(component);\n        if (!proxy) {\n            throw new Error(`Could not find activated route proxy for view`);\n        }\n        proxy._futureSnapshot = activatedRoute._futureSnapshot;\n        proxy._routerState = activatedRoute._routerState;\n        proxy.snapshot = activatedRoute.snapshot;\n        proxy.outlet = activatedRoute.outlet;\n        proxy.component = activatedRoute.component;\n        this.currentActivatedRoute$.next({ component, activatedRoute });\n    }\n}\n/** @nocollapse */ IonRouterOutlet.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonRouterOutlet, deps: [{ token: 'name', attribute: true }, { token: 'tabs', attribute: true, optional: true }, { token: i1.Location }, { token: i0.ElementRef }, { token: i3.Router }, { token: i0.NgZone }, { token: i3.ActivatedRoute }, { token: IonRouterOutlet, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ IonRouterOutlet.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: IonRouterOutlet, selector: \"ion-router-outlet\", inputs: { animated: \"animated\", animation: \"animation\", mode: \"mode\", swipeGesture: \"swipeGesture\", name: \"name\" }, outputs: { stackWillChange: \"stackWillChange\", stackDidChange: \"stackDidChange\", activateEvents: \"activate\", deactivateEvents: \"deactivate\" }, exportAs: [\"outlet\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonRouterOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-router-outlet',\n                    exportAs: 'outlet',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: ['animated', 'animation', 'mode', 'swipeGesture'],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['name']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Attribute,\n                    args: ['tabs']\n                }] }, { type: i1.Location }, { type: i0.ElementRef }, { type: i3.Router }, { type: i0.NgZone }, { type: i3.ActivatedRoute }, { type: IonRouterOutlet, decorators: [{\n                    type: SkipSelf\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { name: [{\n                type: Input\n            }], stackWillChange: [{\n                type: Output\n            }], stackDidChange: [{\n                type: Output\n            }], activateEvents: [{\n                type: Output,\n                args: ['activate']\n            }], deactivateEvents: [{\n                type: Output,\n                args: ['deactivate']\n            }] } });\nclass OutletInjector {\n    constructor(route, childContexts, parent) {\n        this.route = route;\n        this.childContexts = childContexts;\n        this.parent = parent;\n    }\n    get(token, notFoundValue) {\n        if (token === ActivatedRoute) {\n            return this.route;\n        }\n        if (token === ChildrenOutletContexts) {\n            return this.childContexts;\n        }\n        return this.parent.get(token, notFoundValue);\n    }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n    constructor() {\n        this.outletDataSubscriptions = new Map();\n    }\n    bindActivatedRouteToOutletComponent(outlet) {\n        this.unsubscribeFromRouteData(outlet);\n        this.subscribeToRouteData(outlet);\n    }\n    unsubscribeFromRouteData(outlet) {\n        this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n        this.outletDataSubscriptions.delete(outlet);\n    }\n    subscribeToRouteData(outlet) {\n        const { activatedRoute } = outlet;\n        const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data])\n            .pipe(switchMap(([queryParams, params, data], index) => {\n            data = { ...queryParams, ...params, ...data };\n            // Get the first result from the data subscription synchronously so it's available to\n            // the component as soon as possible (and doesn't require a second change detection).\n            if (index === 0) {\n                return of(data);\n            }\n            // Promise.resolve is used to avoid synchronously writing the wrong data when\n            // two of the Observables in the `combineLatest` stream emit one after\n            // another.\n            return Promise.resolve(data);\n        }))\n            .subscribe((data) => {\n            // Outlet may have been deactivated or changed names to be associated with a different\n            // route\n            if (!outlet.isActivated ||\n                !outlet.activatedComponentRef ||\n                outlet.activatedRoute !== activatedRoute ||\n                activatedRoute.component === null) {\n                this.unsubscribeFromRouteData(outlet);\n                return;\n            }\n            const mirror = reflectComponentType(activatedRoute.component);\n            if (!mirror) {\n                this.unsubscribeFromRouteData(outlet);\n                return;\n            }\n            for (const { templateName } of mirror.inputs) {\n                outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n            }\n        });\n        this.outletDataSubscriptions.set(outlet, dataSubscription);\n    }\n}\n/** @nocollapse */ RoutedComponentInputBinder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: RoutedComponentInputBinder, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ RoutedComponentInputBinder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: RoutedComponentInputBinder });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: RoutedComponentInputBinder, decorators: [{\n            type: Injectable\n        }] });\nconst provideComponentInputBinding = () => {\n    return {\n        provide: INPUT_BINDER,\n        useFactory: componentInputBindingFactory,\n        deps: [Router],\n    };\n};\nfunction componentInputBindingFactory(router) {\n    /**\n     * We cast the router to any here, since the componentInputBindingEnabled\n     * property is not available until Angular v16.\n     */\n    if (router?.componentInputBindingEnabled) {\n        return new RoutedComponentInputBinder();\n    }\n    return null;\n}\n\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = class IonBackButton {\n    constructor(routerOutlet, navCtrl, config, r, z, c) {\n        this.routerOutlet = routerOutlet;\n        this.navCtrl = navCtrl;\n        this.config = config;\n        this.r = r;\n        this.z = z;\n        c.detach();\n        this.el = this.r.nativeElement;\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n        const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n        if (this.routerOutlet?.canGoBack()) {\n            this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n            this.routerOutlet.pop();\n            ev.preventDefault();\n        }\n        else if (defaultHref != null) {\n            this.navCtrl.navigateBack(defaultHref, { animation: this.routerAnimation });\n            ev.preventDefault();\n        }\n    }\n};\n/** @nocollapse */ IonBackButton.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonBackButton, deps: [{ token: IonRouterOutlet, optional: true }, { token: NavController }, { token: Config }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ IonBackButton.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: IonBackButton, inputs: { color: \"color\", defaultHref: \"defaultHref\", disabled: \"disabled\", icon: \"icon\", mode: \"mode\", routerAnimation: \"routerAnimation\", text: \"text\", type: \"type\" }, host: { listeners: { \"click\": \"onClick($event)\" } }, ngImport: i0 });\nIonBackButton = __decorate([\n    ProxyCmp({\n        inputs: BACK_BUTTON_INPUTS,\n    })\n], IonBackButton);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonBackButton, decorators: [{\n            type: Directive,\n            args: [{\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: BACK_BUTTON_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: IonRouterOutlet, decorators: [{\n                    type: Optional\n                }] }, { type: NavController }, { type: Config }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { defaultHref: [{\n                type: Input\n            }], routerAnimation: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nclass RouterLinkDelegateDirective {\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n        this.locationStrategy = locationStrategy;\n        this.navCtrl = navCtrl;\n        this.elementRef = elementRef;\n        this.router = router;\n        this.routerLink = routerLink;\n        this.routerDirection = 'forward';\n    }\n    ngOnInit() {\n        this.updateTargetUrlAndHref();\n    }\n    ngOnChanges() {\n        this.updateTargetUrlAndHref();\n    }\n    updateTargetUrlAndHref() {\n        if (this.routerLink?.urlTree) {\n            const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n            this.elementRef.nativeElement.href = href;\n        }\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n        this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n        /**\n         * This prevents the browser from\n         * performing a page reload when pressing\n         * an Ionic component with routerLink.\n         * The page reload interferes with routing\n         * and causes ion-back-button to disappear\n         * since the local history is wiped on reload.\n         */\n        ev.preventDefault();\n    }\n}\n/** @nocollapse */ RouterLinkDelegateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: RouterLinkDelegateDirective, deps: [{ token: i1.LocationStrategy }, { token: NavController }, { token: i0.ElementRef }, { token: i3.Router }, { token: i3.RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ RouterLinkDelegateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: RouterLinkDelegateDirective, selector: \":not(a):not(area)[routerLink]\", inputs: { routerDirection: \"routerDirection\", routerAnimation: \"routerAnimation\" }, host: { listeners: { \"click\": \"onClick($event)\" } }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: RouterLinkDelegateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: ':not(a):not(area)[routerLink]',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.LocationStrategy }, { type: NavController }, { type: i0.ElementRef }, { type: i3.Router }, { type: i3.RouterLink, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { routerDirection: [{\n                type: Input\n            }], routerAnimation: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\nclass RouterLinkWithHrefDelegateDirective {\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n        this.locationStrategy = locationStrategy;\n        this.navCtrl = navCtrl;\n        this.elementRef = elementRef;\n        this.router = router;\n        this.routerLink = routerLink;\n        this.routerDirection = 'forward';\n    }\n    ngOnInit() {\n        this.updateTargetUrlAndHref();\n    }\n    ngOnChanges() {\n        this.updateTargetUrlAndHref();\n    }\n    updateTargetUrlAndHref() {\n        if (this.routerLink?.urlTree) {\n            const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n            this.elementRef.nativeElement.href = href;\n        }\n    }\n    /**\n     * @internal\n     */\n    onClick() {\n        this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    }\n}\n/** @nocollapse */ RouterLinkWithHrefDelegateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: RouterLinkWithHrefDelegateDirective, deps: [{ token: i1.LocationStrategy }, { token: NavController }, { token: i0.ElementRef }, { token: i3.Router }, { token: i3.RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ RouterLinkWithHrefDelegateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: RouterLinkWithHrefDelegateDirective, selector: \"a[routerLink],area[routerLink]\", inputs: { routerDirection: \"routerDirection\", routerAnimation: \"routerAnimation\" }, host: { listeners: { \"click\": \"onClick()\" } }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: RouterLinkWithHrefDelegateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'a[routerLink],area[routerLink]',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.LocationStrategy }, { type: NavController }, { type: i0.ElementRef }, { type: i3.Router }, { type: i3.RouterLink, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { routerDirection: [{\n                type: Input\n            }], routerAnimation: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }] } });\n\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = [\n    'push',\n    'insert',\n    'insertPages',\n    'pop',\n    'popTo',\n    'popToRoot',\n    'removeIndex',\n    'setRoot',\n    'setPages',\n    'getActive',\n    'getByIndex',\n    'canGoBack',\n    'getPrevious',\n];\nlet IonNav = class IonNav {\n    constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n        this.z = z;\n        c.detach();\n        this.el = ref.nativeElement;\n        ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n        proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n    }\n};\n/** @nocollapse */ IonNav.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonNav, deps: [{ token: i0.ElementRef }, { token: i0.EnvironmentInjector }, { token: i0.Injector }, { token: AngularDelegate }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ IonNav.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: IonNav, inputs: { animated: \"animated\", animation: \"animation\", root: \"root\", rootParams: \"rootParams\", swipeGesture: \"swipeGesture\" }, ngImport: i0 });\nIonNav = __decorate([\n    ProxyCmp({\n        inputs: NAV_INPUTS,\n        methods: NAV_METHODS,\n    })\n], IonNav);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonNav, decorators: [{\n            type: Directive,\n            args: [{\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: NAV_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.EnvironmentInjector }, { type: i0.Injector }, { type: AngularDelegate }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }]; } });\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonTabs {\n    constructor(navCtrl) {\n        this.navCtrl = navCtrl;\n        /**\n         * Emitted before the tab view is changed.\n         */\n        this.ionTabsWillChange = new EventEmitter();\n        /**\n         * Emitted after the tab view is changed.\n         */\n        this.ionTabsDidChange = new EventEmitter();\n        this.tabBarSlot = 'bottom';\n    }\n    ngAfterContentInit() {\n        this.detectSlotChanges();\n    }\n    ngAfterContentChecked() {\n        this.detectSlotChanges();\n    }\n    /**\n     * @internal\n     */\n    onStackWillChange({ enteringView, tabSwitch }) {\n        const stackId = enteringView.stackId;\n        if (tabSwitch && stackId !== undefined) {\n            this.ionTabsWillChange.emit({ tab: stackId });\n        }\n    }\n    /**\n     * @internal\n     */\n    onStackDidChange({ enteringView, tabSwitch }) {\n        const stackId = enteringView.stackId;\n        if (tabSwitch && stackId !== undefined) {\n            if (this.tabBar) {\n                this.tabBar.selectedTab = stackId;\n            }\n            this.ionTabsDidChange.emit({ tab: stackId });\n        }\n    }\n    /**\n     * When a tab button is clicked, there are several scenarios:\n     * 1. If the selected tab is currently active (the tab button has been clicked\n     *    again), then it should go to the root view for that tab.\n     *\n     *   a. Get the saved root view from the router outlet. If the saved root view\n     *      matches the tabRootUrl, set the route view to this view including the\n     *      navigation extras.\n     *   b. If the saved root view from the router outlet does\n     *      not match, navigate to the tabRootUrl. No navigation extras are\n     *      included.\n     *\n     * 2. If the current tab tab is not currently selected, get the last route\n     *    view from the router outlet.\n     *\n     *   a. If the last route view exists, navigate to that view including any\n     *      navigation extras\n     *   b. If the last route view doesn't exist, then navigate\n     *      to the default tabRootUrl\n     */\n    select(tabOrEvent) {\n        const isTabString = typeof tabOrEvent === 'string';\n        const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n        const alreadySelected = this.outlet.getActiveStackId() === tab;\n        const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n        /**\n         * If this is a nested tab, prevent the event\n         * from bubbling otherwise the outer tabs\n         * will respond to this event too, causing\n         * the app to get directed to the wrong place.\n         */\n        if (!isTabString) {\n            tabOrEvent.stopPropagation();\n        }\n        if (alreadySelected) {\n            const activeStackId = this.outlet.getActiveStackId();\n            const activeView = this.outlet.getLastRouteView(activeStackId);\n            // If on root tab, do not navigate to root tab again\n            if (activeView?.url === tabRootUrl) {\n                return;\n            }\n            const rootView = this.outlet.getRootView(tab);\n            const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n            return this.navCtrl.navigateRoot(tabRootUrl, {\n                ...navigationExtras,\n                animated: true,\n                animationDirection: 'back',\n            });\n        }\n        else {\n            const lastRoute = this.outlet.getLastRouteView(tab);\n            /**\n             * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n             * selected tab\n             */\n            const url = lastRoute?.url || tabRootUrl;\n            const navigationExtras = lastRoute?.savedExtras;\n            return this.navCtrl.navigateRoot(url, {\n                ...navigationExtras,\n                animated: true,\n                animationDirection: 'back',\n            });\n        }\n    }\n    getSelected() {\n        return this.outlet.getActiveStackId();\n    }\n    /**\n     * Detects changes to the slot attribute of the tab bar.\n     *\n     * If the slot attribute has changed, then the tab bar\n     * should be relocated to the new slot position.\n     */\n    detectSlotChanges() {\n        this.tabBars.forEach((tabBar) => {\n            // el is a protected attribute from the generated component wrapper\n            const currentSlot = tabBar.el.getAttribute('slot');\n            if (currentSlot !== this.tabBarSlot) {\n                this.tabBarSlot = currentSlot;\n                this.relocateTabBar();\n            }\n        });\n    }\n    /**\n     * Relocates the tab bar to the new slot position.\n     */\n    relocateTabBar() {\n        /**\n         * `el` is a protected attribute from the generated component wrapper.\n         * To avoid having to manually create the wrapper for tab bar, we\n         * cast the tab bar to any and access the protected attribute.\n         */\n        const tabBar = this.tabBar.el;\n        if (this.tabBarSlot === 'top') {\n            /**\n             * A tab bar with a slot of \"top\" should be inserted\n             * at the top of the container.\n             */\n            this.tabsInner.nativeElement.before(tabBar);\n        }\n        else {\n            /**\n             * A tab bar with a slot of \"bottom\" or without a slot\n             * should be inserted at the end of the container.\n             */\n            this.tabsInner.nativeElement.after(tabBar);\n        }\n    }\n}\n/** @nocollapse */ IonTabs.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonTabs, deps: [{ token: NavController }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ IonTabs.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: IonTabs, selector: \"ion-tabs\", outputs: { ionTabsWillChange: \"ionTabsWillChange\", ionTabsDidChange: \"ionTabsDidChange\" }, host: { listeners: { \"ionTabButtonClick\": \"select($event)\" } }, viewQueries: [{ propertyName: \"tabsInner\", first: true, predicate: [\"tabsInner\"], descendants: true, read: ElementRef, static: true }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: IonTabs, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-tabs',\n                }]\n        }], ctorParameters: function () { return [{ type: NavController }]; }, propDecorators: { tabsInner: [{\n                type: ViewChild,\n                args: ['tabsInner', { read: ElementRef, static: true }]\n            }], ionTabsWillChange: [{\n                type: Output\n            }], ionTabsDidChange: [{\n                type: Output\n            }], select: [{\n                type: HostListener,\n                args: ['ionTabButtonClick', ['$event']]\n            }] } });\n\nconst raf = (h) => {\n    if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n        return __zone_symbol__requestAnimationFrame(h);\n    }\n    if (typeof requestAnimationFrame === 'function') {\n        return requestAnimationFrame(h);\n    }\n    return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nclass ValueAccessor {\n    constructor(injector, elementRef) {\n        this.injector = injector;\n        this.elementRef = elementRef;\n        this.onChange = () => {\n            /**/\n        };\n        this.onTouched = () => {\n            /**/\n        };\n    }\n    writeValue(value) {\n        this.elementRef.nativeElement.value = this.lastValue = value;\n        setIonicClasses(this.elementRef);\n    }\n    /**\n     * Notifies the ControlValueAccessor of a change in the value of the control.\n     *\n     * This is called by each of the ValueAccessor directives when we want to update\n     * the status and validity of the form control. For example with text components this\n     * is called when the ionInput event is fired. For select components this is called\n     * when the ionChange event is fired.\n     *\n     * This also updates the Ionic form status classes on the element.\n     *\n     * @param el The component element.\n     * @param value The new value of the control.\n     */\n    handleValueChange(el, value) {\n        if (el === this.elementRef.nativeElement) {\n            if (value !== this.lastValue) {\n                this.lastValue = value;\n                this.onChange(value);\n            }\n            setIonicClasses(this.elementRef);\n        }\n    }\n    _handleBlurEvent(el) {\n        if (el === this.elementRef.nativeElement) {\n            this.onTouched();\n            setIonicClasses(this.elementRef);\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.elementRef.nativeElement.disabled = isDisabled;\n    }\n    ngOnDestroy() {\n        if (this.statusChanges) {\n            this.statusChanges.unsubscribe();\n        }\n    }\n    ngAfterViewInit() {\n        let ngControl;\n        try {\n            ngControl = this.injector.get(NgControl);\n        }\n        catch {\n            /* No FormControl or ngModel binding */\n        }\n        if (!ngControl) {\n            return;\n        }\n        // Listen for changes in validity, disabled, or pending states\n        if (ngControl.statusChanges) {\n            this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n        }\n        /**\n         * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n         * whenever it is implemented.\n         */\n        const formControl = ngControl.control;\n        if (formControl) {\n            const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n            methodsToPatch.forEach((method) => {\n                if (typeof formControl[method] !== 'undefined') {\n                    const oldFn = formControl[method].bind(formControl);\n                    formControl[method] = (...params) => {\n                        oldFn(...params);\n                        setIonicClasses(this.elementRef);\n                    };\n                }\n            });\n        }\n    }\n}\n/** @nocollapse */ ValueAccessor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: ValueAccessor, deps: [{ token: i0.Injector }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ ValueAccessor.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.12\", type: ValueAccessor, host: { listeners: { \"ionBlur\": \"_handleBlurEvent($event.target)\" } }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.12\", ngImport: i0, type: ValueAccessor, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.Injector }, { type: i0.ElementRef }]; }, propDecorators: { _handleBlurEvent: [{\n                type: HostListener,\n                args: ['ionBlur', ['$event.target']]\n            }] } });\nconst setIonicClasses = (element) => {\n    raf(() => {\n        const input = element.nativeElement;\n        const hasValue = input.value != null && input.value.toString().length > 0;\n        const classes = getClasses(input);\n        setClasses(input, classes);\n        const item = input.closest('ion-item');\n        if (item) {\n            if (hasValue) {\n                setClasses(item, [...classes, 'item-has-value']);\n            }\n            else {\n                setClasses(item, classes);\n            }\n        }\n    });\n};\nconst getClasses = (element) => {\n    const classList = element.classList;\n    const classes = [];\n    for (let i = 0; i < classList.length; i++) {\n        const item = classList.item(i);\n        if (item !== null && startsWith(item, 'ng-')) {\n            classes.push(`ion-${item.substring(3)}`);\n        }\n    }\n    return classes;\n};\nconst setClasses = (element, classes) => {\n    const classList = element.classList;\n    classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n    classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n    return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n    /**\n     * Whether the given route should detach for later reuse.\n     */\n    shouldDetach(_route) {\n        return false;\n    }\n    /**\n     * Returns `false`, meaning the route (and its subtree) is never reattached\n     */\n    shouldAttach(_route) {\n        return false;\n    }\n    /**\n     * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n     */\n    store(_route, _detachedTree) {\n        return;\n    }\n    /**\n     * Returns `null` because this strategy does not store routes for later re-use.\n     */\n    retrieve(_route) {\n        return null;\n    }\n    /**\n     * Determines if a route should be reused.\n     * This strategy returns `true` when the future route config and\n     * current route config are identical and all route parameters are identical.\n     */\n    shouldReuseRoute(future, curr) {\n        if (future.routeConfig !== curr.routeConfig) {\n            return false;\n        }\n        // checking router params\n        const futureParams = future.params;\n        const currentParams = curr.params;\n        const keysA = Object.keys(futureParams);\n        const keysB = Object.keys(currentParams);\n        if (keysA.length !== keysB.length) {\n            return false;\n        }\n        // Test for A's keys different from B.\n        for (const key of keysA) {\n            if (currentParams[key] !== futureParams[key]) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n    constructor(ctrl) {\n        this.ctrl = ctrl;\n    }\n    /**\n     * Creates a new overlay\n     */\n    create(opts) {\n        return this.ctrl.create((opts || {}));\n    }\n    /**\n     * When `id` is not provided, it dismisses the top overlay.\n     */\n    dismiss(data, role, id) {\n        return this.ctrl.dismiss(data, role, id);\n    }\n    /**\n     * Returns the top overlay.\n     */\n    getTop() {\n        return this.ctrl.getTop();\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AACjU,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,eAAe,EAAEC,cAAc,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACjH,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAChM,SAASC,OAAO,EAAEC,SAAS,EAAEC,eAAe,EAAEC,aAAa,EAAEC,EAAE,QAAQ,MAAM;AAC7E,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,gBAAgB;AACxE,SAASC,SAAS,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAE3C,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIC,IAAIA,CAACC,MAAM,EAAE;IACT,OAAO,IAAI,CAACF,cAAc,CAACC,IAAI,CAACC,MAAM,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACD,MAAM,EAAE;IACV,OAAO,IAAI,CAACF,cAAc,CAACG,KAAK,CAACD,MAAM,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAACF,MAAM,EAAE;IACX,OAAO,IAAI,CAACF,cAAc,CAACI,MAAM,CAACF,MAAM,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,MAAMA,CAACC,YAAY,EAAEJ,MAAM,EAAE;IACzB,OAAO,IAAI,CAACF,cAAc,CAACK,MAAM,CAACC,YAAY,EAAEJ,MAAM,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAACD,YAAY,EAAEJ,MAAM,EAAE;IAC/B,OAAO,IAAI,CAACF,cAAc,CAACO,YAAY,CAACD,YAAY,EAAEJ,MAAM,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACIM,MAAMA,CAACN,MAAM,EAAE;IACX,OAAO,IAAI,CAACF,cAAc,CAACQ,MAAM,CAACN,MAAM,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIO,SAASA,CAACP,MAAM,EAAE;IACd,OAAO,IAAI,CAACF,cAAc,CAACS,SAAS,CAACP,MAAM,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,GAAGA,CAACR,MAAM,EAAE;IACR,OAAO,IAAI,CAACF,cAAc,CAACU,GAAG,CAACR,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;EACIS,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACX,cAAc,CAACW,OAAO,CAAC,CAAC;EACxC;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACZ,cAAc,CAACY,QAAQ,CAAC,CAAC;EACzC;EACAC,iBAAiBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC/B,OAAO,IAAI,CAACf,cAAc,CAACa,iBAAiB,CAACC,IAAI,EAAEC,SAAS,CAAC;EACjE;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChB,cAAc,CAACgB,WAAW,CAAC,CAAC;EAC5C;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjB,cAAc,CAACiB,YAAY,CAAC,CAAC;EAC7C;EACAC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC5B,OAAO,IAAI,CAACpB,cAAc,CAACkB,gBAAgB,CAACC,IAAI,EAAEC,OAAO,CAAC;EAC9D;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAO,IAAI,CAACtB,cAAc,CAACqB,SAAS,CAACC,IAAI,CAAC;EAC9C;EACAC,WAAWA,CAACD,IAAI,EAAE;IACd,OAAO,IAAI,CAACtB,cAAc,CAACuB,WAAW,CAACD,IAAI,CAAC;EAChD;EACAE,QAAQA,CAACF,IAAI,EAAEG,UAAU,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAAC1B,cAAc,CAACwB,QAAQ,CAACF,IAAI,EAAEG,UAAU,EAAEC,QAAQ,CAAC;EACnE;AACJ;AAEA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;EACIC,IAAIA,CAACC,EAAE,EAAE;IACLC,QAAQ,CAAC,CAAC,CAACF,IAAI,CAACC,EAAE,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIE,KAAKA,CAACF,EAAE,EAAE;IACNC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACF,EAAE,CAAC;EACxB;AACJ;AACA;AAAmBF,aAAa,CAACK,IAAI,YAAAC,sBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyFP,aAAa;AAAA,CAAoD;AAC/L;AAAmBA,aAAa,CAACQ,KAAK,kBAD8ExF,EAAE,CAAAyF,kBAAA;EAAAC,KAAA,EACYV,aAAa;EAAAW,OAAA,EAAbX,aAAa,CAAAK,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACtK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFoH7F,EAAE,CAAA8F,iBAAA,CAE1Bd,aAAa,EAAc,CAAC;IAC5GR,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMT,QAAQ,GAAGA,CAAA,KAAM;EACnB,MAAMa,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;EACzD,IAAID,GAAG,IAAI,IAAI,EAAE;IACb,MAAME,KAAK,GAAGF,GAAG,CAACE,KAAK;IACvB,IAAIA,KAAK,EAAEC,KAAK,EAAE;MACd,OAAOD,KAAK,CAACC,KAAK;IACtB;IACA,OAAO;MACHlB,IAAI,EAAGC,EAAE,IAAKc,GAAG,CAACI,qBAAqB,CAAClB,EAAE,CAAC;MAC3CE,KAAK,EAAGF,EAAE,IAAKc,GAAG,CAACI,qBAAqB,CAAClB,EAAE;IAC/C,CAAC;EACL;EACA,OAAO;IACHD,IAAI,EAAGC,EAAE,IAAKA,EAAE,CAAC,CAAC;IAClBE,KAAK,EAAGF,EAAE,IAAKA,EAAE,CAAC;EACtB,CAAC;AACL,CAAC;AAED,MAAMmB,QAAQ,CAAC;EACXjD,WAAWA,CAACkD,GAAG,EAAEC,IAAI,EAAE;IACnB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd;AACR;AACA;IACQ,IAAI,CAACE,UAAU,GAAG,IAAIhE,OAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACiE,eAAe,GAAG,IAAIjE,OAAO,CAAC,CAAC;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACkE,eAAe,GAAG,IAAIlE,OAAO,CAAC,CAAC;IACpC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmE,KAAK,GAAG,IAAInE,OAAO,CAAC,CAAC;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACoE,MAAM,GAAG,IAAIpE,OAAO,CAAC,CAAC;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACqE,MAAM,GAAG,IAAIrE,OAAO,CAAC,CAAC;IAC3B+D,IAAI,CAACO,GAAG,CAAC,MAAM;MACX,IAAI,CAACd,GAAG,GAAGM,GAAG,CAACS,WAAW;MAC1B,IAAI,CAACP,UAAU,CAACQ,qBAAqB,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;QAClE,OAAO,IAAI,CAACC,SAAS,CAAEC,EAAE,IAAK;UAC1B,OAAOA,EAAE,CAACC,QAAQ,CAACJ,QAAQ,EAAGK,kBAAkB,IAAKf,IAAI,CAACO,GAAG,CAAC,MAAMI,QAAQ,CAACI,kBAAkB,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,CAAC,IAAI,CAACZ,KAAK,EAAEL,GAAG,EAAE,OAAO,EAAEC,IAAI,CAAC;MAC1CgB,UAAU,CAAC,IAAI,CAACX,MAAM,EAAEN,GAAG,EAAE,QAAQ,EAAEC,IAAI,CAAC;MAC5CgB,UAAU,CAAC,IAAI,CAACf,UAAU,EAAEF,GAAG,EAAE,eAAe,EAAEC,IAAI,CAAC;MACvDgB,UAAU,CAAC,IAAI,CAACV,MAAM,EAAE,IAAI,CAACb,GAAG,EAAE,QAAQ,EAAEO,IAAI,CAAC;MACjDgB,UAAU,CAAC,IAAI,CAACd,eAAe,EAAE,IAAI,CAACT,GAAG,EAAE,oBAAoB,EAAEO,IAAI,CAAC;MACtEgB,UAAU,CAAC,IAAI,CAACb,eAAe,EAAE,IAAI,CAACV,GAAG,EAAE,oBAAoB,EAAEO,IAAI,CAAC;MACtE,IAAIiB,YAAY;MAChB,IAAI,CAACC,aAAa,GAAG,IAAIC,OAAO,CAAEC,GAAG,IAAK;QACtCH,YAAY,GAAGG,GAAG;MACtB,CAAC,CAAC;MACF,IAAI,IAAI,CAAC3B,GAAG,GAAG,SAAS,CAAC,EAAE;QACvBM,GAAG,CAACsB,gBAAgB,CAAC,aAAa,EAAE,MAAM;UACtCJ,YAAY,CAAC,SAAS,CAAC;QAC3B,CAAC,EAAE;UAAEK,IAAI,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,MACI;QACD;QACAL,YAAY,CAAC,KAAK,CAAC;MACvB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,EAAEA,CAACC,YAAY,EAAE;IACb,OAAO/F,UAAU,CAAC,IAAI,CAACgE,GAAG,EAAE+B,YAAY,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAAA,EAAG;IACR,OAAO/F,YAAY,CAAC,IAAI,CAAC+D,GAAG,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACR,aAAa;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIS,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC5B,GAAG,CAAC6B,GAAG,KAAK,KAAK;EACjC;EACA;AACJ;AACA;EACIC,aAAaA,CAACC,GAAG,EAAE;IACf,OAAOC,cAAc,CAAC,IAAI,CAACtC,GAAG,CAACuC,QAAQ,CAACC,IAAI,EAAEH,GAAG,CAAC;EACtD;EACA;AACJ;AACA;EACII,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIA,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1C,GAAG,CAAC2C,UAAU,GAAG,yBAAyB,CAAC,CAACC,OAAO;EACnE;EACAC,aAAaA,CAACC,UAAU,EAAE;IACtB,MAAMC,GAAG,GAAG,IAAI,CAAC/C,GAAG,CAACgD,SAAS;IAC9B,OAAO,CAAC,EAAED,GAAG,EAAEE,SAAS,IAAIF,GAAG,CAACE,SAAS,CAACC,OAAO,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;EACvE;EACA;AACJ;AACA;EACIK,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACnD,GAAG,CAACuC,QAAQ,CAACC,IAAI;EACjC;EACA;AACJ;AACA;EACIY,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACpD,GAAG,CAACqD,UAAU;EAC9B;EACA;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtD,GAAG,CAACuD,WAAW;EAC/B;AACJ;AACA;AAAmBlD,QAAQ,CAAChB,IAAI,YAAAmE,iBAAAjE,CAAA;EAAA,YAAAA,CAAA,IAAyFc,QAAQ,EAhPbrG,EAAE,CAAAyJ,QAAA,CAgP6B1H,QAAQ,GAhPvC/B,EAAE,CAAAyJ,QAAA,CAgPkDzJ,EAAE,CAACM,MAAM;AAAA,CAA6C;AAC9N;AAAmB+F,QAAQ,CAACb,KAAK,kBAjPmFxF,EAAE,CAAAyF,kBAAA;EAAAC,KAAA,EAiPOW,QAAQ;EAAAV,OAAA,EAARU,QAAQ,CAAAhB,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AAC5J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlPoH7F,EAAE,CAAA8F,iBAAA,CAkP1BO,QAAQ,EAAc,CAAC;IACvG7B,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAEkF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DnF,IAAI,EAAEtE,MAAM;QACZ6F,IAAI,EAAE,CAAChE,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEyC,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7C,MAAMgI,cAAc,GAAGA,CAACa,GAAG,EAAEd,GAAG,KAAK;EACjCA,GAAG,GAAGA,GAAG,CAACuB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;EACrC,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,QAAQ,GAAGzB,GAAG,GAAG,WAAW,CAAC;EACtD,MAAM0B,OAAO,GAAGF,KAAK,CAACG,IAAI,CAACb,GAAG,CAAC;EAC/B,OAAOY,OAAO,GAAGE,kBAAkB,CAACF,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI;AAC9E,CAAC;AACD,MAAMrC,UAAU,GAAGA,CAAC2C,OAAO,EAAEC,EAAE,EAAEC,SAAS,EAAE7D,IAAI,KAAK;EACjD,IAAI4D,EAAE,EAAE;IACJA,EAAE,CAACvC,gBAAgB,CAACwC,SAAS,EAAGhD,EAAE,IAAK;MACnC;AACZ;AACA;AACA;AACA;AACA;MACYb,IAAI,CAACO,GAAG,CAAC,MAAM;QACX;QACA,MAAMuD,KAAK,GAAGjD,EAAE,IAAI,IAAI,GAAGA,EAAE,CAACkD,MAAM,GAAGZ,SAAS;QAChDQ,OAAO,CAACK,IAAI,CAACF,KAAK,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ,CAAC;AAED,MAAMG,aAAa,CAAC;EAChBpH,WAAWA,CAACqH,QAAQ,EAAElC,QAAQ,EAAEmC,UAAU,EAAEC,MAAM,EAAE;IAChD,IAAI,CAACpC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACmC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGC,iBAAiB;IAClC,IAAI,CAAC9F,QAAQ,GAAG+F,gBAAgB;IAChC,IAAI,CAACC,cAAc,GAAG,SAAS;IAC/B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB;IACA,IAAIL,MAAM,EAAE;MACRA,MAAM,CAACM,MAAM,CAAC9D,SAAS,CAAEC,EAAE,IAAK;QAC5B,IAAIA,EAAE,YAAY3F,eAAe,EAAE;UAC/B,MAAMyJ,EAAE,GAAG9D,EAAE,CAAC+D,aAAa,GAAG/D,EAAE,CAAC+D,aAAa,CAACC,YAAY,GAAGhE,EAAE,CAAC8D,EAAE;UACnE,IAAI,CAACH,cAAc,GAAGG,EAAE,GAAG,IAAI,CAACF,SAAS,GAAG,MAAM,GAAG,SAAS;UAC9D,IAAI,CAACK,cAAc,GAAG,CAACjE,EAAE,CAAC+D,aAAa,GAAG,IAAI,CAACJ,cAAc,GAAGrB,SAAS;UACzE,IAAI,CAACsB,SAAS,GAAG,IAAI,CAACD,cAAc,KAAK,SAAS,GAAG3D,EAAE,CAAC8D,EAAE,GAAGA,EAAE;QACnE;MACJ,CAAC,CAAC;IACN;IACA;IACAT,QAAQ,CAACjE,UAAU,CAACQ,qBAAqB,CAAC,CAAC,EAAGM,kBAAkB,IAAK;MACjE,IAAI,CAACgE,GAAG,CAAC,CAAC;MACVhE,kBAAkB,CAAC,CAAC;IACxB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiE,eAAeA,CAACpC,GAAG,EAAEqC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAI,CAACC,YAAY,CAAC,SAAS,EAAED,OAAO,CAACzG,QAAQ,EAAEyG,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACpH,SAAS,CAAC;IAC7F,OAAO,IAAI,CAACuH,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,YAAYA,CAACzC,GAAG,EAAEqC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B,IAAI,CAACC,YAAY,CAAC,MAAM,EAAED,OAAO,CAACzG,QAAQ,EAAEyG,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACpH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAACuH,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAAC1C,GAAG,EAAEqC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B,IAAI,CAACC,YAAY,CAAC,MAAM,EAAED,OAAO,CAACzG,QAAQ,EAAEyG,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACpH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAACuH,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIM,IAAIA,CAACN,OAAO,GAAG;IAAEzG,QAAQ,EAAE,IAAI;IAAE2G,kBAAkB,EAAE;EAAO,CAAC,EAAE;IAC3D,IAAI,CAACD,YAAY,CAAC,MAAM,EAAED,OAAO,CAACzG,QAAQ,EAAEyG,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACpH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAACmE,QAAQ,CAACuD,IAAI,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUR,GAAGA,CAAA,EAAG;IAAA,IAAAS,KAAA;IAAA,OAAAC,iBAAA;MACR,IAAIC,MAAM,GAAGF,KAAI,CAACG,SAAS;MAC3B,OAAOD,MAAM,EAAE;QACX,UAAUA,MAAM,CAACX,GAAG,CAAC,CAAC,EAAE;UACpB,OAAO,IAAI;QACf,CAAC,MACI;UACDW,MAAM,GAAGA,MAAM,CAACE,YAAY;QAChC;MACJ;MACA,OAAO,KAAK;IAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIV,YAAYA,CAACb,SAAS,EAAE7F,QAAQ,EAAE2G,kBAAkB,EAAEU,gBAAgB,EAAE;IACpE,IAAI,CAACxB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC7F,QAAQ,GAAGsH,YAAY,CAACzB,SAAS,EAAE7F,QAAQ,EAAE2G,kBAAkB,CAAC;IACrE,IAAI,CAACU,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;EACIE,YAAYA,CAACL,MAAM,EAAE;IACjB,IAAI,CAACC,SAAS,GAAGD,MAAM;EAC3B;EACA;AACJ;AACA;EACIM,iBAAiBA,CAAA,EAAG;IAChB,IAAI3B,SAAS,GAAG,MAAM;IACtB,IAAIxG,SAAS;IACb,MAAMgI,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAI,IAAI,CAACxB,SAAS,KAAK,MAAM,EAAE;MAC3BA,SAAS,GAAG,IAAI,CAACG,cAAc;MAC/B3G,SAAS,GAAG,IAAI,CAACiH,cAAc;IACnC,CAAC,MACI;MACDjH,SAAS,GAAG,IAAI,CAACW,QAAQ;MACzB6F,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B;IACA,IAAI,CAACA,SAAS,GAAGC,iBAAiB;IAClC,IAAI,CAAC9F,QAAQ,GAAG+F,gBAAgB;IAChC,IAAI,CAACsB,gBAAgB,GAAG1C,SAAS;IACjC,OAAO;MACHkB,SAAS;MACTxG,SAAS;MACTgI;IACJ,CAAC;EACL;EACAT,QAAQA,CAACxC,GAAG,EAAEqC,OAAO,EAAE;IACnB,IAAIgB,KAAK,CAACC,OAAO,CAACtD,GAAG,CAAC,EAAE;MACpB;MACA,OAAO,IAAI,CAACwB,MAAM,CAACgB,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;IAC7C,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMkB,OAAO,GAAG,IAAI,CAAChC,UAAU,CAACiC,KAAK,CAACxD,GAAG,CAACyD,QAAQ,CAAC,CAAC,CAAC;MACrD,IAAIpB,OAAO,CAACqB,WAAW,KAAKnD,SAAS,EAAE;QACnCgD,OAAO,CAACG,WAAW,GAAG;UAAE,GAAGrB,OAAO,CAACqB;QAAY,CAAC;MACpD;MACA,IAAIrB,OAAO,CAACsB,QAAQ,KAAKpD,SAAS,EAAE;QAChCgD,OAAO,CAACI,QAAQ,GAAGtB,OAAO,CAACsB,QAAQ;MACvC;MACA;AACZ;AACA;AACA;AACA;MACY;MACA,OAAO,IAAI,CAACnC,MAAM,CAACoC,aAAa,CAACL,OAAO,EAAElB,OAAO,CAAC;IACtD;EACJ;AACJ;AACA;AAAmBhB,aAAa,CAACnF,IAAI,YAAA2H,sBAAAzH,CAAA;EAAA,YAAAA,CAAA,IAAyFiF,aAAa,EA9cvBxK,EAAE,CAAAyJ,QAAA,CA8cuCpD,QAAQ,GA9cjDrG,EAAE,CAAAyJ,QAAA,CA8c4D3H,EAAE,CAACmL,QAAQ,GA9czEjN,EAAE,CAAAyJ,QAAA,CA8coFjI,EAAE,CAAC0L,aAAa,GA9ctGlN,EAAE,CAAAyJ,QAAA,CA8ciHjI,EAAE,CAACK,MAAM;AAAA,CAA6D;AAC7S;AAAmB2I,aAAa,CAAChF,KAAK,kBA/c8ExF,EAAE,CAAAyF,kBAAA;EAAAC,KAAA,EA+cY8E,aAAa;EAAA7E,OAAA,EAAb6E,aAAa,CAAAnF,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACtK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhdoH7F,EAAE,CAAA8F,iBAAA,CAgd1B0E,aAAa,EAAc,CAAC;IAC5GhG,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAE6B;IAAS,CAAC,EAAE;MAAE7B,IAAI,EAAE1C,EAAE,CAACmL;IAAS,CAAC,EAAE;MAAEzI,IAAI,EAAEhD,EAAE,CAAC0L;IAAc,CAAC,EAAE;MAAE1I,IAAI,EAAEhD,EAAE,CAACK,MAAM;MAAE8H,UAAU,EAAE,CAAC;QACrInF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMkM,YAAY,GAAGA,CAACzB,SAAS,EAAE7F,QAAQ,EAAE2G,kBAAkB,KAAK;EAC9D,IAAI3G,QAAQ,KAAK,KAAK,EAAE;IACpB,OAAO2E,SAAS;EACpB;EACA,IAAIgC,kBAAkB,KAAKhC,SAAS,EAAE;IAClC,OAAOgC,kBAAkB;EAC7B;EACA,IAAId,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACjD,OAAOA,SAAS;EACpB,CAAC,MACI,IAAIA,SAAS,KAAK,MAAM,IAAI7F,QAAQ,KAAK,IAAI,EAAE;IAChD,OAAO,SAAS;EACpB;EACA,OAAO2E,SAAS;AACpB,CAAC;AACD,MAAMmB,iBAAiB,GAAG,MAAM;AAChC,MAAMC,gBAAgB,GAAGpB,SAAS;AAElC,MAAMyD,MAAM,CAAC;EACTpJ,GAAGA,CAACsE,GAAG,EAAE+E,QAAQ,EAAE;IACf,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACtJ,GAAG,CAACsE,GAAG,EAAE+E,QAAQ,CAAC;IAC/B;IACA,OAAO,IAAI;EACf;EACAG,UAAUA,CAAClF,GAAG,EAAE+E,QAAQ,EAAE;IACtB,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACE,UAAU,CAAClF,GAAG,EAAE+E,QAAQ,CAAC;IACtC;IACA,OAAO,KAAK;EAChB;EACAI,SAASA,CAACnF,GAAG,EAAE+E,QAAQ,EAAE;IACrB,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACG,SAAS,CAACnF,GAAG,EAAE+E,QAAQ,CAAC;IACrC;IACA,OAAO,CAAC;EACZ;AACJ;AACA;AAAmBD,MAAM,CAAC9H,IAAI,YAAAoI,eAAAlI,CAAA;EAAA,YAAAA,CAAA,IAAyF4H,MAAM;AAAA,CAAoD;AACjL;AAAmBA,MAAM,CAAC3H,KAAK,kBAlgBqFxF,EAAE,CAAAyF,kBAAA;EAAAC,KAAA,EAkgBKyH,MAAM;EAAAxH,OAAA,EAANwH,MAAM,CAAA9H,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACxJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAngBoH7F,EAAE,CAAA8F,iBAAA,CAmgB1BqH,MAAM,EAAc,CAAC;IACrG3I,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAM8H,WAAW,GAAG,IAAItN,cAAc,CAAC,YAAY,CAAC;AACpD,MAAMkN,SAAS,GAAGA,CAAA,KAAM;EACpB,IAAI,OAAOrH,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGD,MAAM,CAACC,KAAK;IAC1B,IAAIA,KAAK,EAAEyH,MAAM,EAAE;MACf,OAAOzH,KAAK,CAACyH,MAAM;IACvB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZxK,WAAWA,CAACyK,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9J,GAAGA,CAAC+J,KAAK,EAAE;IACP,OAAO,IAAI,CAACD,IAAI,CAACC,KAAK,CAAC;EAC3B;AACJ;;AAEA;AACA,MAAMC,eAAe,CAAC;EAClB3K,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmD,IAAI,GAAGlG,MAAM,CAACC,MAAM,CAAC;IAC1B,IAAI,CAAC0N,cAAc,GAAG3N,MAAM,CAACE,cAAc,CAAC;EAChD;EACA0N,MAAMA,CAACC,mBAAmB,EAAEC,QAAQ,EAAEC,mBAAmB,EAAE;IACvD,OAAO,IAAIC,wBAAwB,CAACH,mBAAmB,EAAEC,QAAQ,EAAE,IAAI,CAACH,cAAc,EAAE,IAAI,CAACzH,IAAI,EAAE6H,mBAAmB,CAAC;EAC3H;AACJ;AACA;AAAmBL,eAAe,CAAC1I,IAAI,YAAAiJ,wBAAA/I,CAAA;EAAA,YAAAA,CAAA,IAAyFwI,eAAe;AAAA,CAAoD;AACnM;AAAmBA,eAAe,CAACvI,KAAK,kBA5kB4ExF,EAAE,CAAAyF,kBAAA;EAAAC,KAAA,EA4kBcqI,eAAe;EAAApI,OAAA,EAAfoI,eAAe,CAAA1I;AAAA,EAAG;AACtJ;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA7kBoH7F,EAAE,CAAA8F,iBAAA,CA6kB1BiI,eAAe,EAAc,CAAC;IAC9GvJ,IAAI,EAAEvE;EACV,CAAC,CAAC;AAAA;AACV,MAAMoO,wBAAwB,CAAC;EAC3BjL,WAAWA,CAAC8K,mBAAmB,EAAEC,QAAQ,EAAEH,cAAc,EAAEzH,IAAI,EAAE6H,mBAAmB,EAAE;IAClF,IAAI,CAACF,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACzH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC6H,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACG,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAG,IAAID,OAAO,CAAC,CAAC;EACpC;EACAE,eAAeA,CAACC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACtD,OAAO,IAAI,CAACvI,IAAI,CAACO,GAAG,CAAC,MAAM;MACvB,OAAO,IAAIY,OAAO,CAAEqH,OAAO,IAAK;QAC5B,MAAMC,cAAc,GAAG;UACnB,GAAGH;QACP,CAAC;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACT,mBAAmB,KAAK1E,SAAS,EAAE;UACxCsF,cAAc,CAAC,IAAI,CAACZ,mBAAmB,CAAC,GAAGO,SAAS;QACxD;QACA,MAAMxE,EAAE,GAAG8E,UAAU,CAAC,IAAI,CAAC1I,IAAI,EAAE,IAAI,CAAC2H,mBAAmB,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,cAAc,EAAE,IAAI,CAACO,QAAQ,EAAE,IAAI,CAACE,WAAW,EAAEE,SAAS,EAAEC,SAAS,EAAEI,cAAc,EAAEF,UAAU,EAAE,IAAI,CAACV,mBAAmB,CAAC;QAC3MW,OAAO,CAAC5E,EAAE,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA+E,iBAAiBA,CAACC,UAAU,EAAEP,SAAS,EAAE;IACrC,OAAO,IAAI,CAACrI,IAAI,CAACO,GAAG,CAAC,MAAM;MACvB,OAAO,IAAIY,OAAO,CAAEqH,OAAO,IAAK;QAC5B,MAAMK,YAAY,GAAG,IAAI,CAACb,QAAQ,CAACxK,GAAG,CAAC6K,SAAS,CAAC;QACjD,IAAIQ,YAAY,EAAE;UACdA,YAAY,CAACC,OAAO,CAAC,CAAC;UACtB,IAAI,CAACd,QAAQ,CAACe,MAAM,CAACV,SAAS,CAAC;UAC/B,MAAMW,YAAY,GAAG,IAAI,CAACd,WAAW,CAAC1K,GAAG,CAAC6K,SAAS,CAAC;UACpD,IAAIW,YAAY,EAAE;YACdA,YAAY,CAAC,CAAC;YACd,IAAI,CAACd,WAAW,CAACa,MAAM,CAACV,SAAS,CAAC;UACtC;QACJ;QACAG,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ;AACA,MAAME,UAAU,GAAGA,CAAC1I,IAAI,EAAE2H,mBAAmB,EAAEC,QAAQ,EAAEH,cAAc,EAAEO,QAAQ,EAAEE,WAAW,EAAEE,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEV,mBAAmB,KAAK;EAC9J;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMoB,aAAa,GAAGhP,QAAQ,CAACyN,MAAM,CAAC;IAClCwB,SAAS,EAAEC,YAAY,CAACb,MAAM,CAAC;IAC/Bc,MAAM,EAAExB;EACZ,CAAC,CAAC;EACF,MAAMiB,YAAY,GAAG3O,eAAe,CAACmO,SAAS,EAAE;IAC5CV,mBAAmB;IACnB0B,eAAe,EAAEJ;EACrB,CAAC,CAAC;EACF,MAAMK,QAAQ,GAAGT,YAAY,CAACS,QAAQ;EACtC,MAAMC,WAAW,GAAGV,YAAY,CAAC7G,QAAQ,CAACwH,aAAa;EACvD,IAAIlB,MAAM,EAAE;IACR;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIT,mBAAmB,IAAIyB,QAAQ,CAACzB,mBAAmB,CAAC,KAAK1E,SAAS,EAAE;MACpEsG,OAAO,CAACC,KAAK,CAAC,kBAAkB7B,mBAAmB,sCAAsCO,SAAS,CAACuB,OAAO,CAACC,WAAW,CAAC,CAAC,2BAA2B/B,mBAAmB,mBAAmBQ,SAAS,CAACzK,IAAI,GAAG,CAAC;IAC/M;IACAiM,MAAM,CAACC,MAAM,CAACR,QAAQ,EAAEhB,MAAM,CAAC;EACnC;EACA,IAAIC,UAAU,EAAE;IACZ,KAAK,MAAMwB,QAAQ,IAAIxB,UAAU,EAAE;MAC/BgB,WAAW,CAACS,SAAS,CAACC,GAAG,CAACF,QAAQ,CAAC;IACvC;EACJ;EACA,MAAMf,YAAY,GAAGkB,mBAAmB,CAAClK,IAAI,EAAEsJ,QAAQ,EAAEC,WAAW,CAAC;EACrEnB,SAAS,CAAC+B,WAAW,CAACZ,WAAW,CAAC;EAClC9B,cAAc,CAACiB,UAAU,CAACG,YAAY,CAACuB,QAAQ,CAAC;EAChDpC,QAAQ,CAACqC,GAAG,CAACd,WAAW,EAAEV,YAAY,CAAC;EACvCX,WAAW,CAACmC,GAAG,CAACd,WAAW,EAAEP,YAAY,CAAC;EAC1C,OAAOO,WAAW;AACtB,CAAC;AACD,MAAMe,UAAU,GAAG,CACf3O,oBAAoB,EACpBC,mBAAmB,EACnBC,oBAAoB,EACpBC,mBAAmB,EACnBC,qBAAqB,CACxB;AACD,MAAMmO,mBAAmB,GAAGA,CAAClK,IAAI,EAAEsJ,QAAQ,EAAEiB,OAAO,KAAK;EACrD,OAAOvK,IAAI,CAACO,GAAG,CAAC,MAAM;IAClB,MAAMiK,WAAW,GAAGF,UAAU,CAAC/N,MAAM,CAAEsH,SAAS,IAAK,OAAOyF,QAAQ,CAACzF,SAAS,CAAC,KAAK,UAAU,CAAC,CAAC4G,GAAG,CAAE5G,SAAS,IAAK;MAC/G,MAAM6G,OAAO,GAAI7J,EAAE,IAAKyI,QAAQ,CAACzF,SAAS,CAAC,CAAChD,EAAE,CAACkD,MAAM,CAAC;MACtDwG,OAAO,CAAClJ,gBAAgB,CAACwC,SAAS,EAAE6G,OAAO,CAAC;MAC5C,OAAO,MAAMH,OAAO,CAACI,mBAAmB,CAAC9G,SAAS,EAAE6G,OAAO,CAAC;IAChE,CAAC,CAAC;IACF,OAAO,MAAMF,WAAW,CAACI,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC;AACN,CAAC;AACD,MAAMC,cAAc,GAAG,IAAIjR,cAAc,CAAC,gBAAgB,CAAC;AAC3D,MAAMsP,YAAY,GAAIb,MAAM,IAAK;EAC7B,OAAO,CACH;IACIyC,OAAO,EAAED,cAAc;IACvBE,QAAQ,EAAE1C;EACd,CAAC,EACD;IACIyC,OAAO,EAAE1D,SAAS;IAClB4D,UAAU,EAAEC,0BAA0B;IACtCC,IAAI,EAAE,CAACL,cAAc;EACzB,CAAC,CACJ;AACL,CAAC;AACD,MAAMI,0BAA0B,GAAI5C,MAAM,IAAK;EAC3C,OAAO,IAAIjB,SAAS,CAACiB,MAAM,CAAC;AAChC,CAAC;;AAED;AACA,MAAM8C,WAAW,GAAGA,CAACC,GAAG,EAAEC,MAAM,KAAK;EACjC,MAAMC,SAAS,GAAGF,GAAG,CAACG,SAAS;EAC/BF,MAAM,CAACV,OAAO,CAAEa,IAAI,IAAK;IACrB5B,MAAM,CAAC6B,cAAc,CAACH,SAAS,EAAEE,IAAI,EAAE;MACnCjO,GAAGA,CAAA,EAAG;QACF,OAAO,IAAI,CAACoG,EAAE,CAAC6H,IAAI,CAAC;MACxB,CAAC;MACDpB,GAAGA,CAACsB,GAAG,EAAE;QACL,IAAI,CAACC,CAAC,CAACC,iBAAiB,CAAC,MAAO,IAAI,CAACjI,EAAE,CAAC6H,IAAI,CAAC,GAAGE,GAAI,CAAC;MACzD;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AACD,MAAMG,YAAY,GAAGA,CAACT,GAAG,EAAEU,OAAO,KAAK;EACnC,MAAMR,SAAS,GAAGF,GAAG,CAACG,SAAS;EAC/BO,OAAO,CAACnB,OAAO,CAAEoB,UAAU,IAAK;IAC5BT,SAAS,CAACS,UAAU,CAAC,GAAG,YAAY;MAChC,MAAMxM,IAAI,GAAGyM,SAAS;MACtB,OAAO,IAAI,CAACL,CAAC,CAACC,iBAAiB,CAAC,MAAM,IAAI,CAACjI,EAAE,CAACoI,UAAU,CAAC,CAACE,KAAK,CAAC,IAAI,CAACtI,EAAE,EAAEpE,IAAI,CAAC,CAAC;IACnF,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD,MAAM2M,YAAY,GAAGA,CAAC7C,QAAQ,EAAE1F,EAAE,EAAEc,MAAM,KAAK;EAC3CA,MAAM,CAACkG,OAAO,CAAE/G,SAAS,IAAMyF,QAAQ,CAACzF,SAAS,CAAC,GAAG3H,SAAS,CAAC0H,EAAE,EAAEC,SAAS,CAAE,CAAC;AACnF,CAAC;AACD;AACA,SAASuI,QAAQA,CAACC,IAAI,EAAE;EACpB,MAAMC,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAE;IAC7B,MAAM;MAAEC,qBAAqB;MAAElB,MAAM;MAAES;IAAQ,CAAC,GAAGM,IAAI;IACvD,IAAIG,qBAAqB,KAAKrJ,SAAS,EAAE;MACrCqJ,qBAAqB,CAAC,CAAC;IAC3B;IACA,IAAIlB,MAAM,EAAE;MACRF,WAAW,CAACmB,GAAG,EAAEjB,MAAM,CAAC;IAC5B;IACA,IAAIS,OAAO,EAAE;MACTD,YAAY,CAACS,GAAG,EAAER,OAAO,CAAC;IAC9B;IACA,OAAOQ,GAAG;EACd,CAAC;EACD,OAAOD,SAAS;AACpB;AAEA,MAAMG,cAAc,GAAG,CACnB,WAAW,EACX,UAAU,EACV,OAAO,EACP,qBAAqB,EACrB,iBAAiB,EACjB,UAAU,EACV,iBAAiB,EACjB,gBAAgB,EAChB,OAAO,EACP,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,MAAM,EACN,cAAc,EACd,aAAa,EACb,SAAS,EACT,eAAe,EACf,WAAW,EACX,MAAM,EACN,MAAM,CACT;AACD,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC;AAC/E,IAAIC,UAAU,GAAG,MAAMA,UAAU,CAAC;EAC9B9P,WAAWA,CAACiK,CAAC,EAAE8F,CAAC,EAAEhB,CAAC,EAAE;IACjB,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACiB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACjJ,EAAE,GAAGgJ,CAAC,CAACpD,aAAa;IACzB,IAAI,CAAC5F,EAAE,CAACvC,gBAAgB,CAAC,UAAU,EAAE,MAAM;MACvC,IAAI,CAACwL,SAAS,GAAG,IAAI;MACrB/F,CAAC,CAACgG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAAClJ,EAAE,CAACvC,gBAAgB,CAAC,YAAY,EAAE,MAAM;MACzC,IAAI,CAACwL,SAAS,GAAG,KAAK;MACtB/F,CAAC,CAACgG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACFX,YAAY,CAAC,IAAI,EAAE,IAAI,CAACvI,EAAE,EAAE,CACxB,sBAAsB,EACtB,uBAAuB,EACvB,uBAAuB,EACvB,sBAAsB,EACtB,YAAY,EACZ,aAAa,EACb,aAAa,EACb,YAAY,CACf,CAAC;EACN;AACJ,CAAC;AACD;AAAmB+I,UAAU,CAAC7N,IAAI,YAAAiO,mBAAA/N,CAAA;EAAA,YAAAA,CAAA,IAAyF2N,UAAU,EAjzBjBlT,EAAE,CAAAuT,iBAAA,CAizBiCvT,EAAE,CAACwT,iBAAiB,GAjzBvDxT,EAAE,CAAAuT,iBAAA,CAizBkEvT,EAAE,CAACsB,UAAU,GAjzBjFtB,EAAE,CAAAuT,iBAAA,CAizB4FvT,EAAE,CAACM,MAAM;AAAA,CAA4C;AACvQ;AAAmB4S,UAAU,CAACO,IAAI,kBAlzBkFzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EAkzBP0O,UAAU;EAAAS,SAAA;EAAAC,cAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MAlzBL9T,EAAE,CAAAiU,cAAA,CAAAD,QAAA,EAkzBsmBtT,WAAW;IAAA;IAAA,IAAAoT,EAAA;MAAA,IAAAI,EAAA;MAlzBnnBlU,EAAE,CAAAmU,cAAA,CAAAD,EAAA,GAAFlU,EAAE,CAAAoU,WAAA,QAAAL,GAAA,CAAAM,QAAA,GAAAH,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAzC,MAAA;IAAA0C,SAAA;IAAAxP,QAAA;IAAAyP,KAAA;IAAAC,mBAAA;IAAAC,eAAA;IAAApE,QAAA;IAAAqE,eAAA;IAAAC,cAAA;IAAAC,KAAA;IAAAhR,MAAA;IAAAiR,aAAA;IAAAC,cAAA;IAAAC,IAAA;IAAAC,YAAA;IAAAC,WAAA;IAAAC,OAAA;IAAAC,aAAA;IAAAC,SAAA;IAAAC,IAAA;IAAAC,IAAA;EAAA;AAAA,EAkzBwpB;AAC9wBrC,UAAU,GAAGrQ,UAAU,CAAC,CACpB8P,QAAQ,CAAC;EACLd,MAAM,EAAEmB,cAAc;EACtBV,OAAO,EAAEW;AACb,CAAC;AACD;AACJ;AACA;AACA;AACA;AACA,GALI,CAMH,EAAEC,UAAU,CAAC;AACd;EAAA,QAAArN,SAAA,oBAAAA,SAAA,KA/zBoH7F,EAAE,CAAA8F,iBAAA,CA+zB1BoN,UAAU,EAAc,CAAC;IACzG1O,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE,aAAa;MACvB;MACA3D,MAAM,EAAEmB;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExO,IAAI,EAAExE,EAAE,CAACwT;IAAkB,CAAC,EAAE;MAAEhP,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+T,QAAQ,EAAE,CAAC;MACjJ7P,IAAI,EAAE5D,YAAY;MAClBmF,IAAI,EAAE,CAACrF,WAAW,EAAE;QAAE+U,MAAM,EAAE;MAAM,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,YAAY,GAAG,CACjB,UAAU,EACV,qBAAqB,EACrB,oBAAoB,EACpB,iBAAiB,EACjB,aAAa,EACb,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,QAAQ,EACR,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,MAAM,EACN,mBAAmB,EACnB,cAAc,EACd,aAAa,EACb,SAAS,CACZ;AACD,MAAMC,aAAa,GAAG,CAClB,SAAS,EACT,SAAS,EACT,cAAc,EACd,eAAe,EACf,sBAAsB,EACtB,sBAAsB,CACzB;AACD,IAAIC,QAAQ,GAAG,MAAMA,QAAQ,CAAC;EAC1BxS,WAAWA,CAACiK,CAAC,EAAE8F,CAAC,EAAEhB,CAAC,EAAE;IACjB,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACiB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACjJ,EAAE,GAAGgJ,CAAC,CAACpD,aAAa;IACzB,IAAI,CAAC5F,EAAE,CAACvC,gBAAgB,CAAC,UAAU,EAAE,MAAM;MACvC,IAAI,CAACwL,SAAS,GAAG,IAAI;MACrB/F,CAAC,CAACgG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAAClJ,EAAE,CAACvC,gBAAgB,CAAC,YAAY,EAAE,MAAM;MACzC,IAAI,CAACwL,SAAS,GAAG,KAAK;MACtB/F,CAAC,CAACgG,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACFX,YAAY,CAAC,IAAI,EAAE,IAAI,CAACvI,EAAE,EAAE,CACxB,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,wBAAwB,EACxB,YAAY,EACZ,aAAa,EACb,aAAa,EACb,YAAY,CACf,CAAC;EACN;AACJ,CAAC;AACD;AAAmByL,QAAQ,CAACvQ,IAAI,YAAAwQ,iBAAAtQ,CAAA;EAAA,YAAAA,CAAA,IAAyFqQ,QAAQ,EAn4Bb5V,EAAE,CAAAuT,iBAAA,CAm4B6BvT,EAAE,CAACwT,iBAAiB,GAn4BnDxT,EAAE,CAAAuT,iBAAA,CAm4B8DvT,EAAE,CAACsB,UAAU,GAn4B7EtB,EAAE,CAAAuT,iBAAA,CAm4BwFvT,EAAE,CAACM,MAAM;AAAA,CAA4C;AACnQ;AAAmBsV,QAAQ,CAACnC,IAAI,kBAp4BoFzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EAo4BToR,QAAQ;EAAAjC,SAAA;EAAAC,cAAA,WAAAkC,wBAAAhC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MAp4BD9T,EAAE,CAAAiU,cAAA,CAAAD,QAAA,EAo4BoqBtT,WAAW;IAAA;IAAA,IAAAoT,EAAA;MAAA,IAAAI,EAAA;MAp4BjrBlU,EAAE,CAAAmU,cAAA,CAAAD,EAAA,GAAFlU,EAAE,CAAAoU,WAAA,QAAAL,GAAA,CAAAM,QAAA,GAAAH,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAzC,MAAA;IAAA9M,QAAA;IAAA0P,mBAAA;IAAAsB,kBAAA;IAAArB,eAAA;IAAAsB,WAAA;IAAAC,UAAA;IAAA3F,QAAA;IAAAsE,cAAA;IAAAC,KAAA;IAAAqB,MAAA;IAAAC,cAAA;IAAAC,iBAAA;IAAAvS,MAAA;IAAAiR,aAAA;IAAAC,cAAA;IAAAC,IAAA;IAAAqB,iBAAA;IAAApB,YAAA;IAAAC,WAAA;IAAAC,OAAA;EAAA;AAAA,EAo4BstB;AAC50BS,QAAQ,GAAG/S,UAAU,CAAC,CAClB8P,QAAQ,CAAC;EACLd,MAAM,EAAE6D,YAAY;EACpBpD,OAAO,EAAEqD;AACb,CAAC;AACD;AACJ;AACA;AACA;AACA;AACA,GALI,CAMH,EAAEC,QAAQ,CAAC;AACZ;EAAA,QAAA/P,SAAA,oBAAAA,SAAA,KAj5BoH7F,EAAE,CAAA8F,iBAAA,CAi5B1B8P,QAAQ,EAAc,CAAC;IACvGpR,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE,WAAW;MACrB;MACA3D,MAAM,EAAE6D;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElR,IAAI,EAAExE,EAAE,CAACwT;IAAkB,CAAC,EAAE;MAAEhP,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+T,QAAQ,EAAE,CAAC;MACjJ7P,IAAI,EAAE5D,YAAY;MAClBmF,IAAI,EAAE,CAACrF,WAAW,EAAE;QAAE+U,MAAM,EAAE;MAAM,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMa,UAAU,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAE5L,SAAS,KAAK;EAC3C,IAAIA,SAAS,KAAK,MAAM,EAAE;IACtB,OAAO6L,OAAO,CAACF,KAAK,EAAEC,IAAI,CAAC;EAC/B,CAAC,MACI,IAAI5L,SAAS,KAAK,SAAS,EAAE;IAC9B,OAAO8L,UAAU,CAACH,KAAK,EAAEC,IAAI,CAAC;EAClC,CAAC,MACI;IACD,OAAOG,OAAO,CAACJ,KAAK,EAAEC,IAAI,CAAC;EAC/B;AACJ,CAAC;AACD,MAAMC,OAAO,GAAGA,CAACF,KAAK,EAAEC,IAAI,KAAK;EAC7BD,KAAK,GAAGA,KAAK,CAACzT,MAAM,CAAE8T,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,CAAC;EACvDN,KAAK,CAACO,IAAI,CAACN,IAAI,CAAC;EAChB,OAAOD,KAAK;AAChB,CAAC;AACD,MAAMG,UAAU,GAAGA,CAACH,KAAK,EAAEC,IAAI,KAAK;EAChC,MAAMO,KAAK,GAAGR,KAAK,CAACrN,OAAO,CAACsN,IAAI,CAAC;EACjC,IAAIO,KAAK,IAAI,CAAC,EAAE;IACZR,KAAK,GAAGA,KAAK,CAACzT,MAAM,CAAE8T,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,IAAID,CAAC,CAAC1L,EAAE,IAAIsL,IAAI,CAACtL,EAAE,CAAC;EAC9E,CAAC,MACI;IACDqL,KAAK,CAACO,IAAI,CAACN,IAAI,CAAC;EACpB;EACA,OAAOD,KAAK;AAChB,CAAC;AACD,MAAMI,OAAO,GAAGA,CAACJ,KAAK,EAAEC,IAAI,KAAK;EAC7B,MAAMO,KAAK,GAAGR,KAAK,CAACrN,OAAO,CAACsN,IAAI,CAAC;EACjC,IAAIO,KAAK,IAAI,CAAC,EAAE;IACZ,OAAOR,KAAK,CAACzT,MAAM,CAAE8T,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,IAAID,CAAC,CAAC1L,EAAE,IAAIsL,IAAI,CAACtL,EAAE,CAAC;EAC7E,CAAC,MACI;IACD,OAAOuL,OAAO,CAACF,KAAK,EAAEC,IAAI,CAAC;EAC/B;AACJ,CAAC;AACD,MAAMQ,MAAM,GAAGA,CAACrM,MAAM,EAAEsM,cAAc,KAAK;EACvC,MAAMvK,OAAO,GAAG/B,MAAM,CAACuM,aAAa,CAAC,CAAC,GAAG,CAAC,EAAE;IAAEC,UAAU,EAAEF;EAAe,CAAC,CAAC;EAC3E,OAAOtM,MAAM,CAACyM,YAAY,CAAC1K,OAAO,CAAC;AACvC,CAAC;AACD,MAAM2K,WAAW,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;EAC/C,IAAI,CAACA,WAAW,EAAE;IACd,OAAO,IAAI;EACf;EACA,OAAOD,YAAY,CAACT,OAAO,KAAKU,WAAW,CAACV,OAAO;AACvD,CAAC;AACD,MAAMW,cAAc,GAAGA,CAACC,SAAS,EAAEtO,GAAG,KAAK;EACvC,IAAI,CAACsO,SAAS,EAAE;IACZ,OAAO/N,SAAS;EACpB;EACA,MAAMgO,QAAQ,GAAGC,UAAU,CAACxO,GAAG,CAAC;EAChC,KAAK,IAAIyO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIA,CAAC,IAAIH,SAAS,CAACI,MAAM,EAAE;MACvB,OAAOH,QAAQ,CAACE,CAAC,CAAC;IACtB;IACA,IAAIF,QAAQ,CAACE,CAAC,CAAC,KAAKH,SAAS,CAACG,CAAC,CAAC,EAAE;MAC9B,OAAOlO,SAAS;IACpB;EACJ;EACA,OAAOA,SAAS;AACpB,CAAC;AACD,MAAMiO,UAAU,GAAIG,IAAI,IAAK;EACzB,OAAOA,IAAI,CACNC,KAAK,CAAC,GAAG,CAAC,CACV/G,GAAG,CAAEgH,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CACpBnV,MAAM,CAAEkV,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;AAChC,CAAC;AACD,MAAME,WAAW,GAAI1B,IAAI,IAAK;EAC1B,IAAIA,IAAI,EAAE;IACNA,IAAI,CAAC2B,GAAG,CAAC9I,OAAO,CAAC,CAAC;IAClBmH,IAAI,CAAC4B,cAAc,CAAC,CAAC;EACzB;AACJ,CAAC;;AAED;AACA,MAAMC,eAAe,CAAC;EAClBjV,WAAWA,CAACkV,UAAU,EAAEC,WAAW,EAAE5N,MAAM,EAAE6N,OAAO,EAAEjS,IAAI,EAAEgC,QAAQ,EAAE;IAClE,IAAI,CAACgQ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC5N,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6N,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjS,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACgC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgO,KAAK,GAAG,EAAE;IACf,IAAI,CAACkC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACJ,UAAU,GAAGA,UAAU,KAAK5O,SAAS,GAAGiO,UAAU,CAACW,UAAU,CAAC,GAAG5O,SAAS;EACnF;EACAiP,UAAUA,CAACR,GAAG,EAAElB,cAAc,EAAE;IAC5B,MAAM9N,GAAG,GAAG6N,MAAM,CAAC,IAAI,CAACrM,MAAM,EAAEsM,cAAc,CAAC;IAC/C,MAAMnG,OAAO,GAAGqH,GAAG,EAAE5P,QAAQ,EAAEwH,aAAa;IAC5C,MAAMqI,cAAc,GAAG3H,mBAAmB,CAAC,IAAI,CAAClK,IAAI,EAAE4R,GAAG,CAACtI,QAAQ,EAAEiB,OAAO,CAAC;IAC5E,OAAO;MACH5F,EAAE,EAAE,IAAI,CAACwN,MAAM,EAAE;MACjB7B,OAAO,EAAEW,cAAc,CAAC,IAAI,CAACc,UAAU,EAAEnP,GAAG,CAAC;MAC7CiP,cAAc;MACdtH,OAAO;MACPqH,GAAG;MACHhP;IACJ,CAAC;EACL;EACAyP,eAAeA,CAAC3B,cAAc,EAAE;IAC5B,MAAM4B,eAAe,GAAG7B,MAAM,CAAC,IAAI,CAACrM,MAAM,EAAEsM,cAAc,CAAC;IAC3D,MAAMT,IAAI,GAAG,IAAI,CAACD,KAAK,CAACuC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAAC5P,GAAG,KAAK0P,eAAe,CAAC;IAChE,IAAIrC,IAAI,EAAE;MACNA,IAAI,CAAC2B,GAAG,CAACa,iBAAiB,CAACC,QAAQ,CAAC,CAAC;IACzC;IACA,OAAOzC,IAAI;EACf;EACA0C,SAASA,CAAC5B,YAAY,EAAE;IACpB,MAAM6B,aAAa,GAAG,IAAI,CAACX,OAAO,CAACjM,iBAAiB,CAAC,CAAC;IACtD,IAAI;MAAE3B,SAAS;MAAExG,SAAS;MAAEgI;IAAiB,CAAC,GAAG+M,aAAa;IAC9D,MAAM5B,WAAW,GAAG,IAAI,CAAC6B,UAAU;IACnC,MAAMC,SAAS,GAAGhC,WAAW,CAACC,YAAY,EAAEC,WAAW,CAAC;IACxD,IAAI8B,SAAS,EAAE;MACXzO,SAAS,GAAG,MAAM;MAClBxG,SAAS,GAAGsF,SAAS;IACzB;IACA,MAAM4P,aAAa,GAAG,IAAI,CAAC/C,KAAK,CAACgD,KAAK,CAAC,CAAC;IACxC,IAAIC,iBAAiB;IACrB,MAAM7O,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B;IACA,IAAIA,MAAM,CAAC8O,oBAAoB,EAAE;MAC7BD,iBAAiB,GAAG7O,MAAM,CAAC8O,oBAAoB,CAAC,CAAC;MACjD;IACJ,CAAC,MACI,IAAI9O,MAAM,CAAC+O,WAAW,EAAErP,KAAK,EAAE;MAChCmP,iBAAiB,GAAG7O,MAAM,CAAC+O,WAAW,CAACrP,KAAK;IAChD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAImP,iBAAiB,EAAEG,MAAM,EAAEC,UAAU,EAAE;MACvC,IAAI,IAAI,CAACrD,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACtB,KAAK,CAACsD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B;IACJ;IACA,MAAMC,MAAM,GAAG,IAAI,CAACvD,KAAK,CAACwD,QAAQ,CAACzC,YAAY,CAAC;IAChD,MAAMf,KAAK,GAAG,IAAI,CAACD,UAAU,CAACgB,YAAY,EAAE1M,SAAS,CAAC;IACtD;IACA;IACA;IACA,IAAI,CAACkP,MAAM,EAAE;MACTxC,YAAY,CAACa,GAAG,CAACa,iBAAiB,CAAC3F,aAAa,CAAC,CAAC;IACtD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM2G,eAAe,GAAG1C,YAAY,CAAClL,gBAAgB;IACrD,IAAIA,gBAAgB,KAAK1C,SAAS,IAAIkB,SAAS,KAAK,MAAM,IAAI,CAACyO,SAAS,IAAIW,eAAe,KAAKtQ,SAAS,EAAE;MACvG0C,gBAAgB,GAAG4N,eAAe;IACtC;IACA;AACR;AACA;AACA;IACQ,IAAIzC,WAAW,EAAE;MACbA,WAAW,CAACnL,gBAAgB,GAAGA,gBAAgB;IACnD;IACA;IACA,OAAO,IAAI,CAAC7F,IAAI,CAAC6L,iBAAiB,CAAC,MAAM;MACrC,OAAO,IAAI,CAAC6H,IAAI,CAAC,MAAM;QACnB;QACA;QACA,IAAI1C,WAAW,EAAE;UACbA,WAAW,CAACY,GAAG,CAACa,iBAAiB,CAACkB,MAAM,CAAC,CAAC;QAC9C;QACA;QACA5C,YAAY,CAACa,GAAG,CAACa,iBAAiB,CAACC,QAAQ,CAAC,CAAC;QAC7C,OAAO,IAAI,CAACkB,UAAU,CAAC7C,YAAY,EAAEC,WAAW,EAAEnT,SAAS,EAAE,IAAI,CAACgW,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAEhO,gBAAgB,CAAC,CACnGiO,IAAI,CAAC,MAAMC,YAAY,CAAChD,YAAY,EAAEf,KAAK,EAAE+C,aAAa,EAAE,IAAI,CAAC/Q,QAAQ,EAAE,IAAI,CAAChC,IAAI,CAAC,CAAC,CACtF8T,IAAI,CAAC,OAAO;UACb/C,YAAY;UACZ1M,SAAS;UACTxG,SAAS;UACTiV;QACJ,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAe,SAASA,CAACG,IAAI,EAAE1D,OAAO,GAAG,IAAI,CAAC2D,gBAAgB,CAAC,CAAC,EAAE;IAC/C,OAAO,IAAI,CAACC,QAAQ,CAAC5D,OAAO,CAAC,CAACgB,MAAM,GAAG0C,IAAI;EAC/C;EACAjP,GAAGA,CAACiP,IAAI,EAAE1D,OAAO,GAAG,IAAI,CAAC2D,gBAAgB,CAAC,CAAC,EAAE;IACzC,OAAO,IAAI,CAACjU,IAAI,CAACO,GAAG,CAAC,MAAM;MACvB,MAAMyP,KAAK,GAAG,IAAI,CAACkE,QAAQ,CAAC5D,OAAO,CAAC;MACpC,IAAIN,KAAK,CAACsB,MAAM,IAAI0C,IAAI,EAAE;QACtB,OAAO7S,OAAO,CAACqH,OAAO,CAAC,KAAK,CAAC;MACjC;MACA,MAAMyH,IAAI,GAAGD,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG0C,IAAI,GAAG,CAAC,CAAC;MAC3C,IAAIpR,GAAG,GAAGqN,IAAI,CAACrN,GAAG;MAClB,MAAMuR,aAAa,GAAGlE,IAAI,CAACmE,SAAS;MACpC,IAAID,aAAa,EAAE;QACf,MAAME,aAAa,GAAGF,aAAa,CAAC3W,GAAG,CAAC,SAAS,CAAC;QAClD,IAAI6W,aAAa,EAAEC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,CAAC5R,GAAG,EAAE;UAClDA,GAAG,GAAGyR,aAAa,CAACC,KAAK,CAACC,YAAY,CAACC,QAAQ,CAAC5R,GAAG;QACvD;MACJ;MACA,MAAM;QAAEiD;MAAiB,CAAC,GAAG,IAAI,CAACoM,OAAO,CAACjM,iBAAiB,CAAC,CAAC;MAC7D,OAAO,IAAI,CAACiM,OAAO,CAAC5M,YAAY,CAACzC,GAAG,EAAE;QAAE,GAAGqN,IAAI,CAACwE,WAAW;QAAE5W,SAAS,EAAEgI;MAAiB,CAAC,CAAC,CAACiO,IAAI,CAAC,MAAM,IAAI,CAAC;IAChH,CAAC,CAAC;EACN;EACAY,mBAAmBA,CAAA,EAAG;IAClB,MAAM1D,WAAW,GAAG,IAAI,CAAC6B,UAAU;IACnC,IAAI7B,WAAW,EAAE;MACb,MAAMhB,KAAK,GAAG,IAAI,CAACkE,QAAQ,CAAClD,WAAW,CAACV,OAAO,CAAC;MAChD,MAAMS,YAAY,GAAGf,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG,CAAC,CAAC;MAC5C,MAAMmC,eAAe,GAAG1C,YAAY,CAAClL,gBAAgB;MACrD,OAAO,IAAI,CAAC6N,IAAI,CAAC,MAAM;QACnB,OAAO,IAAI,CAACE,UAAU,CAAC7C,YAAY;QAAE;QACrCC,WAAW;QAAE;QACb,MAAM,EAAE,IAAI,CAAC6C,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEJ,eAAe,CAAC;MACrD,CAAC,CAAC;IACN;IACA,OAAOtS,OAAO,CAACqH,OAAO,CAAC,CAAC;EAC5B;EACAmM,iBAAiBA,CAACC,cAAc,EAAE;IAC9B,IAAIA,cAAc,EAAE;MAChB,IAAI,CAAC1C,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACnN,GAAG,CAAC,CAAC,CAAC;IACf,CAAC,MACI,IAAI,IAAI,CAAC8N,UAAU,EAAE;MACtBgC,OAAO,CAAC,IAAI,CAAChC,UAAU,EAAE,IAAI,CAAC7C,KAAK,EAAE,IAAI,CAACA,KAAK,EAAE,IAAI,CAAChO,QAAQ,EAAE,IAAI,CAAChC,IAAI,CAAC;IAC9E;EACJ;EACA8U,UAAUA,CAACxE,OAAO,EAAE;IAChB,MAAMN,KAAK,GAAG,IAAI,CAACkE,QAAQ,CAAC5D,OAAO,CAAC;IACpC,OAAON,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG,CAAC,CAAC,GAAGnO,SAAS;EACjE;EACA;AACJ;AACA;EACI4R,UAAUA,CAACzE,OAAO,EAAE;IAChB,MAAMN,KAAK,GAAG,IAAI,CAACkE,QAAQ,CAAC5D,OAAO,CAAC;IACpC,OAAON,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,CAAC,CAAC,CAAC,GAAG7M,SAAS;EAClD;EACA8Q,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACpB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACvC,OAAO,GAAGnN,SAAS;EAChE;EACA;AACJ;AACA;EACI6R,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnC,UAAU;EAC1B;EACAoC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,KAAK/R,SAAS;EACzC;EACA2F,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACkJ,WAAW,GAAG7O,SAAS;IAC5B,IAAI,CAAC6M,KAAK,CAACpF,OAAO,CAAC+G,WAAW,CAAC;IAC/B,IAAI,CAACkB,UAAU,GAAG1P,SAAS;IAC3B,IAAI,CAAC6M,KAAK,GAAG,EAAE;EACnB;EACAkE,QAAQA,CAAC5D,OAAO,EAAE;IACd,OAAO,IAAI,CAACN,KAAK,CAACzT,MAAM,CAAE8T,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKA,OAAO,CAAC;EAC1D;EACAP,UAAUA,CAACgB,YAAY,EAAE1M,SAAS,EAAE;IAChC,IAAI,CAACwO,UAAU,GAAG9B,YAAY;IAC9B,IAAI,CAACf,KAAK,GAAGD,UAAU,CAAC,IAAI,CAACC,KAAK,EAAEe,YAAY,EAAE1M,SAAS,CAAC;IAC5D,OAAO,IAAI,CAAC2L,KAAK,CAACgD,KAAK,CAAC,CAAC;EAC7B;EACAY,UAAUA,CAAC7C,YAAY,EAAEC,WAAW,EAAE3M,SAAS,EAAE8Q,UAAU,EAAEC,iBAAiB,EAAEvP,gBAAgB,EAAE;IAC9F,IAAI,IAAI,CAACqM,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,GAAG,KAAK;MAC3B,OAAO/Q,OAAO,CAACqH,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,IAAIwI,WAAW,KAAKD,YAAY,EAAE;MAC9B,OAAO5P,OAAO,CAACqH,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,MAAM6M,UAAU,GAAGtE,YAAY,GAAGA,YAAY,CAACxG,OAAO,GAAGpH,SAAS;IAClE,MAAMmS,SAAS,GAAGtE,WAAW,GAAGA,WAAW,CAACzG,OAAO,GAAGpH,SAAS;IAC/D,MAAM6O,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAIqD,UAAU,IAAIA,UAAU,KAAKC,SAAS,EAAE;MACxCD,UAAU,CAACrL,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACpCoL,UAAU,CAACrL,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAC9C,IAAIoL,UAAU,CAACE,aAAa,KAAKvD,WAAW,EAAE;QAC1CA,WAAW,CAAC7H,WAAW,CAACkL,UAAU,CAAC;MACvC;MACA,IAAIrD,WAAW,CAACwD,MAAM,EAAE;QACpB,OAAOxD,WAAW,CAACwD,MAAM,CAACH,UAAU,EAAEC,SAAS,EAAE;UAC7CG,QAAQ,EAAEpR,SAAS,KAAKlB,SAAS,GAAG,CAAC,GAAGA,SAAS;UACjDkB,SAAS;UACT8Q,UAAU;UACVC,iBAAiB;UACjBvP;QACJ,CAAC,CAAC;MACN;IACJ;IACA,OAAO1E,OAAO,CAACqH,OAAO,CAAC,KAAK,CAAC;EACjC;EACMkL,IAAIA,CAACgC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAlQ,iBAAA;MACb,IAAIkQ,MAAI,CAACT,WAAW,KAAK/R,SAAS,EAAE;QAChC,MAAMwS,MAAI,CAACT,WAAW;QACtBS,MAAI,CAACT,WAAW,GAAG/R,SAAS;MAChC;MACA,MAAMyS,OAAO,GAAID,MAAI,CAACT,WAAW,GAAGQ,IAAI,CAAC,CAAE;MAC3CE,OAAO,CAACC,OAAO,CAAC,MAAOF,MAAI,CAACT,WAAW,GAAG/R,SAAU,CAAC;MACrD,OAAOyS,OAAO;IAAC;EACnB;AACJ;AACA,MAAM7B,YAAY,GAAGA,CAAC+B,WAAW,EAAE9F,KAAK,EAAE+C,aAAa,EAAE/Q,QAAQ,EAAEhC,IAAI,KAAK;EACxE,IAAI,OAAOH,qBAAqB,KAAK,UAAU,EAAE;IAC7C,OAAO,IAAIsB,OAAO,CAAEqH,OAAO,IAAK;MAC5B3I,qBAAqB,CAAC,MAAM;QACxBgV,OAAO,CAACiB,WAAW,EAAE9F,KAAK,EAAE+C,aAAa,EAAE/Q,QAAQ,EAAEhC,IAAI,CAAC;QAC1DwI,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAOrH,OAAO,CAACqH,OAAO,CAAC,CAAC;AAC5B,CAAC;AACD,MAAMqM,OAAO,GAAGA,CAACiB,WAAW,EAAE9F,KAAK,EAAE+C,aAAa,EAAE/Q,QAAQ,EAAEhC,IAAI,KAAK;EACnE;AACJ;AACA;AACA;EACIA,IAAI,CAACO,GAAG,CAAC,MAAMwS,aAAa,CAACxW,MAAM,CAAE0T,IAAI,IAAK,CAACD,KAAK,CAACwD,QAAQ,CAACvD,IAAI,CAAC,CAAC,CAACrF,OAAO,CAAC+G,WAAW,CAAC,CAAC;EAC1F3B,KAAK,CAACpF,OAAO,CAAEqF,IAAI,IAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM8F,qBAAqB,GAAG/T,QAAQ,CAACuP,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D,MAAMwE,uBAAuB,GAAGD,qBAAqB,CAACvE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnE,IAAIvB,IAAI,KAAK6F,WAAW,IAAI7F,IAAI,CAACrN,GAAG,KAAKoT,uBAAuB,EAAE;MAC9D,MAAMzL,OAAO,GAAG0F,IAAI,CAAC1F,OAAO;MAC5BA,OAAO,CAAC0L,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC3C1L,OAAO,CAACP,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxCgG,IAAI,CAAC2B,GAAG,CAACa,iBAAiB,CAACkB,MAAM,CAAC,CAAC;IACvC;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA,MAAMuC,eAAe,CAAC;EAClBrZ,WAAWA,CAACe,IAAI,EAAEuY,IAAI,EAAEC,cAAc,EAAEC,UAAU,EAAEjS,MAAM,EAAEpE,IAAI,EAAE0Q,cAAc,EAAE9K,YAAY,EAAE;IAC5F,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC0Q,aAAa,GAAG,IAAI;IACzB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAItO,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACuO,sBAAsB,GAAG,IAAIra,eAAe,CAAC,IAAI,CAAC;IACvD,IAAI,CAACsa,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;IACQ,IAAI,CAAC9Y,IAAI,GAAGzC,cAAc;IAC1B;IACA,IAAI,CAACwb,eAAe,GAAG,IAAIrc,YAAY,CAAC,CAAC;IACzC;IACA,IAAI,CAACsc,cAAc,GAAG,IAAItc,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAACuc,cAAc,GAAG,IAAIvc,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAACwc,gBAAgB,GAAG,IAAIxc,YAAY,CAAC,CAAC;IAC1C,IAAI,CAACyc,cAAc,GAAGjd,MAAM,CAACsB,sBAAsB,CAAC;IACpD,IAAI,CAAC4G,QAAQ,GAAGlI,MAAM,CAACS,gBAAgB,CAAC;IACxC,IAAI,CAACoN,mBAAmB,GAAG7N,MAAM,CAACU,mBAAmB,CAAC;IACtD,IAAI,CAACwc,WAAW,GAAGld,MAAM,CAACmd,YAAY,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3D;IACA,IAAI,CAACC,gCAAgC,GAAG,IAAI;IAC5C;IACA,IAAI,CAAC/P,MAAM,GAAGtN,MAAM,CAAC8M,MAAM,CAAC;IAC5B,IAAI,CAACqL,OAAO,GAAGnY,MAAM,CAACmK,aAAa,CAAC;IACpC,IAAI,CAACmT,QAAQ,GAAGf,UAAU,CAAC7M,aAAa;IACxC,IAAI,CAAC5L,IAAI,GAAGA,IAAI,IAAIzC,cAAc;IAClC,IAAI,CAAC4W,UAAU,GAAGoE,IAAI,KAAK,MAAM,GAAG1F,MAAM,CAACrM,MAAM,EAAEsM,cAAc,CAAC,GAAGvN,SAAS;IAC9E,IAAI,CAACkU,SAAS,GAAG,IAAIvF,eAAe,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACqF,QAAQ,EAAEhT,MAAM,EAAE,IAAI,CAAC6N,OAAO,EAAEjS,IAAI,EAAEoW,cAAc,CAAC;IAChH,IAAI,CAACW,cAAc,CAACO,oBAAoB,CAAC,IAAI,CAAC1Z,IAAI,EAAE,IAAI,CAAC;EAC7D;EACA;EACA,IAAI2Z,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACd,SAAS;EACzB;EACA,IAAI5Y,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACuZ,QAAQ,CAACvZ,SAAS,GAAGA,SAAS;EACvC;EACA,IAAIW,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAAC4Y,QAAQ,CAAC5Y,QAAQ,GAAGA,QAAQ;EACrC;EACA,IAAInB,YAAYA,CAACma,KAAK,EAAE;IACpB,IAAI,CAACC,aAAa,GAAGD,KAAK;IAC1B,IAAI,CAACJ,QAAQ,CAACM,YAAY,GAAGF,KAAK,GAC5B;MACEG,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACN,SAAS,CAACxD,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACwD,SAAS,CAACpC,cAAc,CAAC,CAAC;MAC/E2C,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACP,SAAS,CAAC3C,mBAAmB,CAAC,CAAC;MACnDmD,KAAK,EAAGC,cAAc,IAAK,IAAI,CAACT,SAAS,CAAC1C,iBAAiB,CAACmD,cAAc;IAC9E,CAAC,GACC3U,SAAS;EACnB;EACA4U,WAAWA,CAAA,EAAG;IACV,IAAI,CAACV,SAAS,CAACvO,OAAO,CAAC,CAAC;IACxB,IAAI,CAACkO,WAAW,EAAEgB,wBAAwB,CAAC,IAAI,CAAC;EACpD;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClB,cAAc,CAACkB,UAAU,CAAC,IAAI,CAACra,IAAI,CAAC;EACpD;EACAsa,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACnC;EACA;EACAA,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAE;MACjB;MACA;MACA,MAAM2B,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;MACjC,IAAIG,OAAO,EAAE9D,KAAK,EAAE;QAChB,IAAI,CAAC+D,YAAY,CAACD,OAAO,CAAC9D,KAAK,EAAE8D,OAAO,CAACxQ,QAAQ,CAAC;MACtD;IACJ;IACA,IAAIzG,OAAO,CAAEqH,OAAO,IAAKxM,gBAAgB,CAAC,IAAI,CAACob,QAAQ,EAAE5O,OAAO,CAAC,CAAC,CAACsL,IAAI,CAAC,MAAM;MAC1E,IAAI,IAAI,CAAC2D,aAAa,KAAKtU,SAAS,EAAE;QAClC,IAAI,CAAC9F,YAAY,GAAG,IAAI,CAAC+J,MAAM,CAACJ,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAACoQ,QAAQ,CAAC3I,IAAI,KAAK,KAAK,CAAC;MAChG;IACJ,CAAC,CAAC;EACN;EACA,IAAI6J,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC,IAAI,CAAC7B,SAAS;EAC3B;EACA,IAAIpO,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACoO,SAAS,EAAE;MACjB,MAAM,IAAI8B,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA,OAAO,IAAI,CAAC9B,SAAS,CAACnN,QAAQ;EAClC;EACA,IAAIoH,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC+F,SAAS,EAAE;MACjB,MAAM,IAAI8B,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA,OAAO,IAAI,CAAC7B,eAAe;EAC/B;EACA,IAAI8B,kBAAkBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC9B,eAAe,EAAE;MACtB,OAAO,IAAI,CAACA,eAAe,CAAClC,QAAQ,CAAClN,IAAI;IAC7C;IACA,OAAO,CAAC,CAAC;EACb;EACA;AACJ;AACA;EACIqM,MAAMA,CAAA,EAAG;IACL,MAAM,IAAI4E,KAAK,CAAC,6BAA6B,CAAC;EAClD;EACA;AACJ;AACA;EACI;EACAE,MAAMA,CAACC,IAAI,EAAEhC,eAAe,EAAE;IAC1B,MAAM,IAAI6B,KAAK,CAAC,6BAA6B,CAAC;EAClD;EACAI,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAClC,SAAS,EAAE;MAChB,IAAI,IAAI,CAACH,aAAa,EAAE;QACpB;QACA,MAAM8B,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC3B,aAAa,CAAClC,SAAS,GAAG,IAAIwE,GAAG,CAACR,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMxE,aAAa,GAAG,IAAI,CAACiC,aAAa,CAAClC,SAAS,CAAC5W,GAAG,CAAC,SAAS,CAAC;QACjE,IAAI6W,aAAa,IAAI+D,OAAO,CAAC9D,KAAK,EAAE;UAChCD,aAAa,CAACC,KAAK,GAAG;YAAE,GAAG8D,OAAO,CAAC9D;UAAM,CAAC;QAC9C;QACA;AAChB;AACA;AACA;QACgB,IAAI,CAACgC,aAAa,CAAC7B,WAAW,GAAG,CAAC,CAAC;QACnC,IAAI2D,OAAO,CAAC9D,KAAK,EAAE;UACf,MAAMwE,eAAe,GAAGV,OAAO,CAAC9D,KAAK,CAACE,QAAQ;UAC9C,IAAI,CAAC8B,aAAa,CAAC7B,WAAW,CAACnO,WAAW,GAAGwS,eAAe,CAACxS,WAAW;UACxE,IAAI,CAACgQ,aAAa,CAAC7B,WAAW,CAAClO,QAAQ,GAAGuS,eAAe,CAACvS,QAAQ;QACtE;MACJ;MACA,MAAMO,CAAC,GAAG,IAAI,CAACuB,SAAS;MACxB,IAAI,CAACiO,aAAa,GAAG,IAAI;MACzB,IAAI,CAACG,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACI,gBAAgB,CAACiC,IAAI,CAACjS,CAAC,CAAC;IACjC;EACJ;EACAuR,YAAYA,CAAC3H,cAAc,EAAE/I,mBAAmB,EAAE;IAC9C,IAAI,IAAI,CAAC2Q,WAAW,EAAE;MAClB,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;IAClE;IACA,IAAI,CAAC7B,eAAe,GAAGhG,cAAc;IACrC,IAAIsI,MAAM;IACV,IAAIjI,YAAY,GAAG,IAAI,CAACsG,SAAS,CAAChF,eAAe,CAAC3B,cAAc,CAAC;IACjE,IAAIK,YAAY,EAAE;MACdiI,MAAM,GAAG,IAAI,CAACvC,SAAS,GAAG1F,YAAY,CAACa,GAAG;MAC1C,MAAMqH,KAAK,GAAGlI,YAAY,CAACqD,SAAS;MACpC,IAAI6E,KAAK,EAAE;QACP;QACA;QACA,MAAMb,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QACjCG,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,GAAGI,KAAK;MACxC;MACA;MACA,IAAI,CAACC,yBAAyB,CAACF,MAAM,CAAC1P,QAAQ,EAAEoH,cAAc,CAAC;IACnE,CAAC,MACI;MACD,MAAM8D,QAAQ,GAAG9D,cAAc,CAACyI,eAAe;MAC/C;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,aAAa,GAAG,IAAI,CAACrC,cAAc,CAACsC,kBAAkB,CAAC,IAAI,CAACzb,IAAI,CAAC,CAACib,QAAQ;MAChF;MACA;MACA,MAAMS,UAAU,GAAG,IAAInd,eAAe,CAAC,IAAI,CAAC;MAC5C,MAAMod,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACF,UAAU,EAAE5I,cAAc,CAAC;MACtF,MAAM9I,QAAQ,GAAG,IAAI6R,cAAc,CAACF,mBAAmB,EAAEH,aAAa,EAAE,IAAI,CAACpX,QAAQ,CAAC4F,QAAQ,CAAC;MAC/F;MACA,MAAMS,SAAS,GAAGmM,QAAQ,CAACkF,WAAW,CAACrR,SAAS,IAAImM,QAAQ,CAACnM,SAAS;MACtE2Q,MAAM,GAAG,IAAI,CAACvC,SAAS,GAAG,IAAI,CAACzU,QAAQ,CAAC9H,eAAe,CAACmO,SAAS,EAAE;QAC/DmI,KAAK,EAAE,IAAI,CAACxO,QAAQ,CAACsP,MAAM;QAC3B1J,QAAQ;QACRD,mBAAmB,EAAEA,mBAAmB,IAAI,IAAI,CAACA;MACrD,CAAC,CAAC;MACF;MACA2R,UAAU,CAACtV,IAAI,CAACgV,MAAM,CAAC1P,QAAQ,CAAC;MAChC;MACA;MACAyH,YAAY,GAAG,IAAI,CAACsG,SAAS,CAACjF,UAAU,CAAC,IAAI,CAACqE,SAAS,EAAE/F,cAAc,CAAC;MACxE;MACA,IAAI,CAAC6F,QAAQ,CAAClM,GAAG,CAAC2O,MAAM,CAAC1P,QAAQ,EAAEiQ,mBAAmB,CAAC;MACvD,IAAI,CAAC/C,sBAAsB,CAACxS,IAAI,CAAC;QAAEqE,SAAS,EAAE2Q,MAAM,CAAC1P,QAAQ;QAAEoH;MAAe,CAAC,CAAC;IACpF;IACA,IAAI,CAACsG,WAAW,EAAE2C,mCAAmC,CAAC,IAAI,CAAC;IAC3D,IAAI,CAACrD,aAAa,GAAGvF,YAAY;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkB,OAAO,CAAClM,YAAY,CAAC,IAAI,CAAC;IAC/B,MAAMiL,WAAW,GAAG,IAAI,CAACqG,SAAS,CAACrC,aAAa,CAAC,CAAC;IAClD,IAAI,CAAC2B,eAAe,CAACoC,IAAI,CAAC;MACtBhI,YAAY;MACZ+B,SAAS,EAAEhC,WAAW,CAACC,YAAY,EAAEC,WAAW;IACpD,CAAC,CAAC;IACF,IAAI,CAACqG,SAAS,CAAC1E,SAAS,CAAC5B,YAAY,CAAC,CAAC+C,IAAI,CAAExM,IAAI,IAAK;MAClD,IAAI,CAACuP,cAAc,CAACkC,IAAI,CAACC,MAAM,CAAC1P,QAAQ,CAAC;MACzC,IAAI,CAACsN,cAAc,CAACmC,IAAI,CAACzR,IAAI,CAAC;IAClC,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIuM,SAASA,CAACG,IAAI,GAAG,CAAC,EAAE1D,OAAO,EAAE;IACzB,OAAO,IAAI,CAAC+G,SAAS,CAACxD,SAAS,CAACG,IAAI,EAAE1D,OAAO,CAAC;EAClD;EACA;AACJ;AACA;EACIvL,GAAGA,CAACiP,IAAI,GAAG,CAAC,EAAE1D,OAAO,EAAE;IACnB,OAAO,IAAI,CAAC+G,SAAS,CAACtS,GAAG,CAACiP,IAAI,EAAE1D,OAAO,CAAC;EAC5C;EACA;AACJ;AACA;EACIwE,UAAUA,CAACxE,OAAO,EAAE;IAChB,MAAMsJ,MAAM,GAAG,IAAI,CAACvC,SAAS,CAACvC,UAAU,CAACxE,OAAO,CAAC;IACjD,OAAOsJ,MAAM,GAAGA,MAAM,CAAChX,GAAG,GAAGO,SAAS;EAC1C;EACA;AACJ;AACA;AACA;EACI0W,gBAAgBA,CAACvJ,OAAO,EAAE;IACtB,OAAO,IAAI,CAAC+G,SAAS,CAACvC,UAAU,CAACxE,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIwJ,WAAWA,CAACxJ,OAAO,EAAE;IACjB,OAAO,IAAI,CAAC+G,SAAS,CAACtC,UAAU,CAACzE,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;EACI2D,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACoD,SAAS,CAACpD,gBAAgB,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIuF,yBAAyBA,CAACF,UAAU,EAAE5I,cAAc,EAAE;IAClD,MAAMqJ,KAAK,GAAG,IAAI1e,cAAc,CAAC,CAAC;IAClC0e,KAAK,CAACZ,eAAe,GAAGzI,cAAc,CAACyI,eAAe;IACtDY,KAAK,CAACxF,YAAY,GAAG7D,cAAc,CAAC6D,YAAY;IAChDwF,KAAK,CAACvF,QAAQ,GAAG9D,cAAc,CAAC8D,QAAQ;IACxCuF,KAAK,CAACrU,MAAM,GAAGgL,cAAc,CAAChL,MAAM;IACpCqU,KAAK,CAAC1R,SAAS,GAAGqI,cAAc,CAACrI,SAAS;IAC1C;IACA0R,KAAK,CAACC,SAAS,GAAG,IAAI,CAACC,eAAe,CAACX,UAAU,EAAE,UAAU,CAAC;IAC9DS,KAAK,CAACG,cAAc,GAAG,IAAI,CAACD,eAAe,CAACX,UAAU,EAAE,eAAe,CAAC;IACxES,KAAK,CAACnX,GAAG,GAAG,IAAI,CAACqX,eAAe,CAACX,UAAU,EAAE,KAAK,CAAC;IACnDS,KAAK,CAACzR,MAAM,GAAG,IAAI,CAAC2R,eAAe,CAACX,UAAU,EAAE,QAAQ,CAAC;IACzDS,KAAK,CAACzT,WAAW,GAAG,IAAI,CAAC2T,eAAe,CAACX,UAAU,EAAE,aAAa,CAAC;IACnES,KAAK,CAACxT,QAAQ,GAAG,IAAI,CAAC0T,eAAe,CAACX,UAAU,EAAE,UAAU,CAAC;IAC7DS,KAAK,CAACzS,IAAI,GAAG,IAAI,CAAC2S,eAAe,CAACX,UAAU,EAAE,MAAM,CAAC;IACrD,OAAOS,KAAK;EAChB;EACA;AACJ;AACA;EACIE,eAAeA,CAACX,UAAU,EAAE/H,IAAI,EAAE;IAC9B,OAAO+H,UAAU,CAACa,IAAI;IACtB;IACA5d,MAAM,CAAE8L,SAAS,IAAK,CAAC,CAACA,SAAS,CAAC,EAAE7L,SAAS,CAAE6L,SAAS,IAAK,IAAI,CAACmO,sBAAsB,CAAC2D,IAAI,CAAC5d,MAAM,CAAE6d,OAAO,IAAKA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAAC/R,SAAS,KAAKA,SAAS,CAAC,EAAE7L,SAAS,CAAE4d,OAAO,IAAKA,OAAO,IAAIA,OAAO,CAAC1J,cAAc,CAACa,IAAI,CAAC,CAAC,EAAE9U,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;EACtQ;EACA;AACJ;AACA;EACIyc,yBAAyBA,CAAC7Q,SAAS,EAAEqI,cAAc,EAAE;IACjD,MAAMqJ,KAAK,GAAG,IAAI,CAACxD,QAAQ,CAAC/Y,GAAG,CAAC6K,SAAS,CAAC;IAC1C,IAAI,CAAC0R,KAAK,EAAE;MACR,MAAM,IAAIxB,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACAwB,KAAK,CAACZ,eAAe,GAAGzI,cAAc,CAACyI,eAAe;IACtDY,KAAK,CAACxF,YAAY,GAAG7D,cAAc,CAAC6D,YAAY;IAChDwF,KAAK,CAACvF,QAAQ,GAAG9D,cAAc,CAAC8D,QAAQ;IACxCuF,KAAK,CAACrU,MAAM,GAAGgL,cAAc,CAAChL,MAAM;IACpCqU,KAAK,CAAC1R,SAAS,GAAGqI,cAAc,CAACrI,SAAS;IAC1C,IAAI,CAACmO,sBAAsB,CAACxS,IAAI,CAAC;MAAEqE,SAAS;MAAEqI;IAAe,CAAC,CAAC;EACnE;AACJ;AACA;AAAmBwF,eAAe,CAACpX,IAAI,YAAAub,wBAAArb,CAAA;EAAA,YAAAA,CAAA,IAAyFkX,eAAe,EA7iD3Bzc,EAAE,CAAA6gB,iBAAA,CA6iD2C,MAAM,GA7iDnD7gB,EAAE,CAAA6gB,iBAAA,CA6iD+E,MAAM,GA7iDvF7gB,EAAE,CAAAuT,iBAAA,CA6iDmIzR,EAAE,CAACmL,QAAQ,GA7iDhJjN,EAAE,CAAAuT,iBAAA,CA6iD2JvT,EAAE,CAACsB,UAAU,GA7iD1KtB,EAAE,CAAAuT,iBAAA,CA6iDqL/R,EAAE,CAACK,MAAM,GA7iDhM7B,EAAE,CAAAuT,iBAAA,CA6iD2MvT,EAAE,CAACM,MAAM,GA7iDtNN,EAAE,CAAAuT,iBAAA,CA6iDiO/R,EAAE,CAACI,cAAc,GA7iDpP5B,EAAE,CAAAuT,iBAAA,CA6iD+PkJ,eAAe;AAAA,CAA4E;AAChd;AAAmBA,eAAe,CAAChJ,IAAI,kBA9iD6EzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EA8iDFiY,eAAe;EAAA9I,SAAA;EAAA9B,MAAA;IAAA9M,QAAA;IAAAX,SAAA;IAAA4Q,IAAA;IAAApR,YAAA;IAAAO,IAAA;EAAA;EAAA2c,OAAA;IAAA5D,eAAA;IAAAC,cAAA;IAAAC,cAAA;IAAAC,gBAAA;EAAA;EAAA0D,QAAA;AAAA,EAAyU;AAC5c;EAAA,QAAAlb,SAAA,oBAAAA,SAAA,KA/iDoH7F,EAAE,CAAA8F,iBAAA,CA+iD1B2W,eAAe,EAAc,CAAC;IAC9GjY,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE,mBAAmB;MAC7BuL,QAAQ,EAAE,QAAQ;MAClB;MACAlP,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc;IAC5D,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErN,IAAI,EAAEkF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DnF,IAAI,EAAExD,SAAS;QACf+E,IAAI,EAAE,CAAC,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEvB,IAAI,EAAEkF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCnF,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAExD,SAAS;QACf+E,IAAI,EAAE,CAAC,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEvB,IAAI,EAAE1C,EAAE,CAACmL;IAAS,CAAC,EAAE;MAAEzI,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAEhD,EAAE,CAACI;IAAe,CAAC,EAAE;MAAE4C,IAAI,EAAEiY,eAAe;MAAE9S,UAAU,EAAE,CAAC;QAC/JnF,IAAI,EAAEvD;MACV,CAAC,EAAE;QACCuD,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEgE,IAAI,EAAE,CAAC;MACnCK,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEgc,eAAe,EAAE,CAAC;MAClB1Y,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEgc,cAAc,EAAE,CAAC;MACjB3Y,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEic,cAAc,EAAE,CAAC;MACjB5Y,IAAI,EAAErD,MAAM;MACZ4E,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEsX,gBAAgB,EAAE,CAAC;MACnB7Y,IAAI,EAAErD,MAAM;MACZ4E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMia,cAAc,CAAC;EACjB5c,WAAWA,CAACyX,KAAK,EAAE8E,aAAa,EAAEhQ,MAAM,EAAE;IACtC,IAAI,CAACkL,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC8E,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAChQ,MAAM,GAAGA,MAAM;EACxB;EACA5L,GAAGA,CAAC2B,KAAK,EAAEsb,aAAa,EAAE;IACtB,IAAItb,KAAK,KAAK9D,cAAc,EAAE;MAC1B,OAAO,IAAI,CAACiZ,KAAK;IACrB;IACA,IAAInV,KAAK,KAAK/D,sBAAsB,EAAE;MAClC,OAAO,IAAI,CAACge,aAAa;IAC7B;IACA,OAAO,IAAI,CAAChQ,MAAM,CAAC5L,GAAG,CAAC2B,KAAK,EAAEsb,aAAa,CAAC;EAChD;AACJ;AACA;AACA,MAAMxD,YAAY,GAAG,IAAIpd,cAAc,CAAC,EAAE,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6gB,0BAA0B,CAAC;EAC7B7d,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8d,uBAAuB,GAAG,IAAI/B,GAAG,CAAC,CAAC;EAC5C;EACAe,mCAAmCA,CAACjU,MAAM,EAAE;IACxC,IAAI,CAACsS,wBAAwB,CAACtS,MAAM,CAAC;IACrC,IAAI,CAACkV,oBAAoB,CAAClV,MAAM,CAAC;EACrC;EACAsS,wBAAwBA,CAACtS,MAAM,EAAE;IAC7B,IAAI,CAACiV,uBAAuB,CAACnd,GAAG,CAACkI,MAAM,CAAC,EAAEmV,WAAW,CAAC,CAAC;IACvD,IAAI,CAACF,uBAAuB,CAAC5R,MAAM,CAACrD,MAAM,CAAC;EAC/C;EACAkV,oBAAoBA,CAAClV,MAAM,EAAE;IACzB,MAAM;MAAEgL;IAAe,CAAC,GAAGhL,MAAM;IACjC,MAAMoV,gBAAgB,GAAG1e,aAAa,CAAC,CAACsU,cAAc,CAACpK,WAAW,EAAEoK,cAAc,CAACpI,MAAM,EAAEoI,cAAc,CAACpJ,IAAI,CAAC,CAAC,CAC3G6S,IAAI,CAAC3d,SAAS,CAAC,CAAC,CAAC8J,WAAW,EAAEgC,MAAM,EAAEhB,IAAI,CAAC,EAAEkJ,KAAK,KAAK;MACxDlJ,IAAI,GAAG;QAAE,GAAGhB,WAAW;QAAE,GAAGgC,MAAM;QAAE,GAAGhB;MAAK,CAAC;MAC7C;MACA;MACA,IAAIkJ,KAAK,KAAK,CAAC,EAAE;QACb,OAAOnU,EAAE,CAACiL,IAAI,CAAC;MACnB;MACA;MACA;MACA;MACA,OAAOnG,OAAO,CAACqH,OAAO,CAAClB,IAAI,CAAC;IAChC,CAAC,CAAC,CAAC,CACE1G,SAAS,CAAE0G,IAAI,IAAK;MACrB;MACA;MACA,IAAI,CAAC5B,MAAM,CAAC4S,WAAW,IACnB,CAAC5S,MAAM,CAAC6R,qBAAqB,IAC7B7R,MAAM,CAACgL,cAAc,KAAKA,cAAc,IACxCA,cAAc,CAACrI,SAAS,KAAK,IAAI,EAAE;QACnC,IAAI,CAAC2P,wBAAwB,CAACtS,MAAM,CAAC;QACrC;MACJ;MACA,MAAMqV,MAAM,GAAGlgB,oBAAoB,CAAC6V,cAAc,CAACrI,SAAS,CAAC;MAC7D,IAAI,CAAC0S,MAAM,EAAE;QACT,IAAI,CAAC/C,wBAAwB,CAACtS,MAAM,CAAC;QACrC;MACJ;MACA,KAAK,MAAM;QAAEsV;MAAa,CAAC,IAAID,MAAM,CAACzP,MAAM,EAAE;QAC1C5F,MAAM,CAAC6R,qBAAqB,CAAC0D,QAAQ,CAACD,YAAY,EAAE1T,IAAI,CAAC0T,YAAY,CAAC,CAAC;MAC3E;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,uBAAuB,CAACtQ,GAAG,CAAC3E,MAAM,EAAEoV,gBAAgB,CAAC;EAC9D;AACJ;AACA;AAAmBJ,0BAA0B,CAAC5b,IAAI,YAAAoc,mCAAAlc,CAAA;EAAA,YAAAA,CAAA,IAAyF0b,0BAA0B;AAAA,CAAoD;AACzN;AAAmBA,0BAA0B,CAACzb,KAAK,kBAlqDiExF,EAAE,CAAAyF,kBAAA;EAAAC,KAAA,EAkqDyBub,0BAA0B;EAAAtb,OAAA,EAA1Bsb,0BAA0B,CAAA5b;AAAA,EAAG;AAC5K;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAnqDoH7F,EAAE,CAAA8F,iBAAA,CAmqD1Bmb,0BAA0B,EAAc,CAAC;IACzHzc,IAAI,EAAEvE;EACV,CAAC,CAAC;AAAA;AACV,MAAMyhB,4BAA4B,GAAGA,CAAA,KAAM;EACvC,OAAO;IACHpQ,OAAO,EAAEkM,YAAY;IACrBhM,UAAU,EAAEmQ,4BAA4B;IACxCjQ,IAAI,EAAE,CAAC7P,MAAM;EACjB,CAAC;AACL,CAAC;AACD,SAAS8f,4BAA4BA,CAAChX,MAAM,EAAE;EAC1C;AACJ;AACA;AACA;EACI,IAAIA,MAAM,EAAEiX,4BAA4B,EAAE;IACtC,OAAO,IAAIX,0BAA0B,CAAC,CAAC;EAC3C;EACA,OAAO,IAAI;AACf;AAEA,MAAMY,kBAAkB,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC;AAClH,IAAIC,aAAa,GAAG,MAAMA,aAAa,CAAC;EACpC1e,WAAWA,CAAC2e,YAAY,EAAEvJ,OAAO,EAAE7K,MAAM,EAAEwF,CAAC,EAAEhB,CAAC,EAAE9E,CAAC,EAAE;IAChD,IAAI,CAAC0U,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACvJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7K,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACwF,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChB,CAAC,GAAGA,CAAC;IACV9E,CAAC,CAAC6M,MAAM,CAAC,CAAC;IACV,IAAI,CAAC/P,EAAE,GAAG,IAAI,CAACgJ,CAAC,CAACpD,aAAa;EAClC;EACA;AACJ;AACA;EACIiS,OAAOA,CAAC5a,EAAE,EAAE;IACR,MAAM6a,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,IAAI,CAACtU,MAAM,CAAC5J,GAAG,CAAC,uBAAuB,CAAC;IAChF,IAAI,IAAI,CAACge,YAAY,EAAE3H,SAAS,CAAC,CAAC,EAAE;MAChC,IAAI,CAAC5B,OAAO,CAAC/M,YAAY,CAAC,MAAM,EAAE/B,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACwY,eAAe,CAAC;MAC7E,IAAI,CAACH,YAAY,CAACzW,GAAG,CAAC,CAAC;MACvBlE,EAAE,CAAC+a,cAAc,CAAC,CAAC;IACvB,CAAC,MACI,IAAIF,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACzJ,OAAO,CAAC5M,YAAY,CAACqW,WAAW,EAAE;QAAE7d,SAAS,EAAE,IAAI,CAAC8d;MAAgB,CAAC,CAAC;MAC3E9a,EAAE,CAAC+a,cAAc,CAAC,CAAC;IACvB;EACJ;AACJ,CAAC;AACD;AAAmBL,aAAa,CAACzc,IAAI,YAAA+c,sBAAA7c,CAAA;EAAA,YAAAA,CAAA,IAAyFuc,aAAa,EAntDvB9hB,EAAE,CAAAuT,iBAAA,CAmtDuCkJ,eAAe,MAntDxDzc,EAAE,CAAAuT,iBAAA,CAmtDmF/I,aAAa,GAntDlGxK,EAAE,CAAAuT,iBAAA,CAmtD6GpG,MAAM,GAntDrHnN,EAAE,CAAAuT,iBAAA,CAmtDgIvT,EAAE,CAACsB,UAAU,GAntD/ItB,EAAE,CAAAuT,iBAAA,CAmtD0JvT,EAAE,CAACM,MAAM,GAntDrKN,EAAE,CAAAuT,iBAAA,CAmtDgLvT,EAAE,CAACwT,iBAAiB;AAAA,CAA4C;AACtW;AAAmBsO,aAAa,CAACrO,IAAI,kBAptD+EzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EAotDJsd,aAAa;EAAAO,YAAA,WAAAC,2BAAAxO,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAptDX9T,EAAE,CAAAuiB,UAAA,mBAAAC,uCAAAC,MAAA;QAAA,OAotDJ1O,GAAA,CAAAiO,OAAA,CAAAS,MAAc,CAAC;MAAA,CAAH,CAAC;IAAA;EAAA;EAAA5Q,MAAA;IAAA6Q,KAAA;IAAAT,WAAA;IAAAU,QAAA;IAAAC,IAAA;IAAA5N,IAAA;IAAAkN,eAAA;IAAAW,IAAA;IAAAre,IAAA;EAAA;AAAA,EAAgP;AAC/Wsd,aAAa,GAAGjf,UAAU,CAAC,CACvB8P,QAAQ,CAAC;EACLd,MAAM,EAAEgQ;AACZ,CAAC,CAAC,CACL,EAAEC,aAAa,CAAC;AACjB;EAAA,QAAAjc,SAAA,oBAAAA,SAAA,KA1tDoH7F,EAAE,CAAA8F,iBAAA,CA0tD1Bgc,aAAa,EAAc,CAAC;IAC5Gtd,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC;MACA8L,MAAM,EAAEgQ;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErd,IAAI,EAAEiY,eAAe;MAAE9S,UAAU,EAAE,CAAC;QACpEnF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,EAAE;MAAEqE,IAAI,EAAEgG;IAAc,CAAC,EAAE;MAAEhG,IAAI,EAAE2I;IAAO,CAAC,EAAE;MAAE3I,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACwT;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyO,WAAW,EAAE,CAAC;MACnKzd,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEghB,eAAe,EAAE,CAAC;MAClB1d,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE8gB,OAAO,EAAE,CAAC;MACVxd,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+c,2BAA2B,CAAC;EAC9B1f,WAAWA,CAAC2f,gBAAgB,EAAEvK,OAAO,EAAEoE,UAAU,EAAEjS,MAAM,EAAEqY,UAAU,EAAE;IACnE,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACvK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACjS,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqY,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,eAAe,GAAG,SAAS;EACpC;EACAxE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyE,sBAAsB,CAAC,CAAC;EACjC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,sBAAsB,CAAC,CAAC;EACjC;EACAA,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACF,UAAU,EAAEtW,OAAO,EAAE;MAC1B,MAAMlE,IAAI,GAAG,IAAI,CAACua,gBAAgB,CAACK,kBAAkB,CAAC,IAAI,CAACzY,MAAM,CAACyM,YAAY,CAAC,IAAI,CAAC4L,UAAU,CAACtW,OAAO,CAAC,CAAC;MACxG,IAAI,CAACkQ,UAAU,CAAC7M,aAAa,CAACvH,IAAI,GAAGA,IAAI;IAC7C;EACJ;EACA;AACJ;AACA;EACIwZ,OAAOA,CAAC5a,EAAE,EAAE;IACR,IAAI,CAACoR,OAAO,CAAC/M,YAAY,CAAC,IAAI,CAACwX,eAAe,EAAEvZ,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACwY,eAAe,CAAC;IAC3F;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ9a,EAAE,CAAC+a,cAAc,CAAC,CAAC;EACvB;AACJ;AACA;AAAmBW,2BAA2B,CAACzd,IAAI,YAAAge,oCAAA9d,CAAA;EAAA,YAAAA,CAAA,IAAyFud,2BAA2B,EAtxDnD9iB,EAAE,CAAAuT,iBAAA,CAsxDmEzR,EAAE,CAACwhB,gBAAgB,GAtxDxFtjB,EAAE,CAAAuT,iBAAA,CAsxDmG/I,aAAa,GAtxDlHxK,EAAE,CAAAuT,iBAAA,CAsxD6HvT,EAAE,CAACsB,UAAU,GAtxD5ItB,EAAE,CAAAuT,iBAAA,CAsxDuJ/R,EAAE,CAACK,MAAM,GAtxDlK7B,EAAE,CAAAuT,iBAAA,CAsxD6K/R,EAAE,CAAC+hB,UAAU;AAAA,CAA4D;AAC5W;AAAmBT,2BAA2B,CAACrP,IAAI,kBAvxDiEzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EAuxDUse,2BAA2B;EAAAnP,SAAA;EAAA0O,YAAA,WAAAmB,yCAAA1P,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAvxDvC9T,EAAE,CAAAuiB,UAAA,mBAAAkB,qDAAAhB,MAAA;QAAA,OAuxDU1O,GAAA,CAAAiO,OAAA,CAAAS,MAAc,CAAC;MAAA,CAAW,CAAC;IAAA;EAAA;EAAA5Q,MAAA;IAAAoR,eAAA;IAAAf,eAAA;EAAA;EAAAwB,QAAA,GAvxDvC1jB,EAAE,CAAA2jB,oBAAA;AAAA,EAuxD+P;AACrX;EAAA,QAAA9d,SAAA,oBAAAA,SAAA,KAxxDoH7F,EAAE,CAAA8F,iBAAA,CAwxD1Bgd,2BAA2B,EAAc,CAAC;IAC1Hte,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhR,IAAI,EAAE1C,EAAE,CAACwhB;IAAiB,CAAC,EAAE;MAAE9e,IAAI,EAAEgG;IAAc,CAAC,EAAE;MAAEhG,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAEhD,EAAE,CAAC+hB,UAAU;MAAE5Z,UAAU,EAAE,CAAC;QACxKnF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE8iB,eAAe,EAAE,CAAC;MAC9Cze,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEghB,eAAe,EAAE,CAAC;MAClB1d,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE8gB,OAAO,EAAE,CAAC;MACVxd,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM6d,mCAAmC,CAAC;EACtCxgB,WAAWA,CAAC2f,gBAAgB,EAAEvK,OAAO,EAAEoE,UAAU,EAAEjS,MAAM,EAAEqY,UAAU,EAAE;IACnE,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACvK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACjS,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqY,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,eAAe,GAAG,SAAS;EACpC;EACAxE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyE,sBAAsB,CAAC,CAAC;EACjC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,sBAAsB,CAAC,CAAC;EACjC;EACAA,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACF,UAAU,EAAEtW,OAAO,EAAE;MAC1B,MAAMlE,IAAI,GAAG,IAAI,CAACua,gBAAgB,CAACK,kBAAkB,CAAC,IAAI,CAACzY,MAAM,CAACyM,YAAY,CAAC,IAAI,CAAC4L,UAAU,CAACtW,OAAO,CAAC,CAAC;MACxG,IAAI,CAACkQ,UAAU,CAAC7M,aAAa,CAACvH,IAAI,GAAGA,IAAI;IAC7C;EACJ;EACA;AACJ;AACA;EACIwZ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACxJ,OAAO,CAAC/M,YAAY,CAAC,IAAI,CAACwX,eAAe,EAAEvZ,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACwY,eAAe,CAAC;EAC/F;AACJ;AACA;AAAmB0B,mCAAmC,CAACve,IAAI,YAAAwe,4CAAAte,CAAA;EAAA,YAAAA,CAAA,IAAyFqe,mCAAmC,EAn0DnE5jB,EAAE,CAAAuT,iBAAA,CAm0DmFzR,EAAE,CAACwhB,gBAAgB,GAn0DxGtjB,EAAE,CAAAuT,iBAAA,CAm0DmH/I,aAAa,GAn0DlIxK,EAAE,CAAAuT,iBAAA,CAm0D6IvT,EAAE,CAACsB,UAAU,GAn0D5JtB,EAAE,CAAAuT,iBAAA,CAm0DuK/R,EAAE,CAACK,MAAM,GAn0DlL7B,EAAE,CAAAuT,iBAAA,CAm0D6L/R,EAAE,CAAC+hB,UAAU;AAAA,CAA4D;AAC5X;AAAmBK,mCAAmC,CAACnQ,IAAI,kBAp0DyDzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EAo0DkBof,mCAAmC;EAAAjQ,SAAA;EAAA0O,YAAA,WAAAyB,iDAAAhQ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAp0DvD9T,EAAE,CAAAuiB,UAAA,mBAAAwB,6DAAA;QAAA,OAo0DkBhQ,GAAA,CAAAiO,OAAA,CAAQ,CAAC;MAAA,CAAyB,CAAC;IAAA;EAAA;EAAAnQ,MAAA;IAAAoR,eAAA;IAAAf,eAAA;EAAA;EAAAwB,QAAA,GAp0DvD1jB,EAAE,CAAA2jB,oBAAA;AAAA,EAo0D0Q;AAChY;EAAA,QAAA9d,SAAA,oBAAAA,SAAA,KAr0DoH7F,EAAE,CAAA8F,iBAAA,CAq0D1B8d,mCAAmC,EAAc,CAAC;IAClIpf,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhR,IAAI,EAAE1C,EAAE,CAACwhB;IAAiB,CAAC,EAAE;MAAE9e,IAAI,EAAEgG;IAAc,CAAC,EAAE;MAAEhG,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAEhD,EAAE,CAAC+hB,UAAU;MAAE5Z,UAAU,EAAE,CAAC;QACxKnF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE8iB,eAAe,EAAE,CAAC;MAC9Cze,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEghB,eAAe,EAAE,CAAC;MAClB1d,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE8gB,OAAO,EAAE,CAAC;MACVxd,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMie,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,CAAC;AAClF,MAAMC,WAAW,GAAG,CAChB,MAAM,EACN,QAAQ,EACR,aAAa,EACb,KAAK,EACL,OAAO,EACP,WAAW,EACX,aAAa,EACb,SAAS,EACT,UAAU,EACV,WAAW,EACX,YAAY,EACZ,WAAW,EACX,aAAa,CAChB;AACD,IAAIC,MAAM,GAAG,MAAMA,MAAM,CAAC;EACtB9gB,WAAWA,CAAC+U,GAAG,EAAEjK,mBAAmB,EAAEC,QAAQ,EAAEgW,eAAe,EAAEhS,CAAC,EAAE9E,CAAC,EAAE;IACnE,IAAI,CAAC8E,CAAC,GAAGA,CAAC;IACV9E,CAAC,CAAC6M,MAAM,CAAC,CAAC;IACV,IAAI,CAAC/P,EAAE,GAAGgO,GAAG,CAACpI,aAAa;IAC3BoI,GAAG,CAACpI,aAAa,CAACqU,QAAQ,GAAGD,eAAe,CAAClW,MAAM,CAACC,mBAAmB,EAAEC,QAAQ,CAAC;IAClFuE,YAAY,CAAC,IAAI,EAAE,IAAI,CAACvI,EAAE,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;EACxE;AACJ,CAAC;AACD;AAAmB+Z,MAAM,CAAC7e,IAAI,YAAAgf,eAAA9e,CAAA;EAAA,YAAAA,CAAA,IAAyF2e,MAAM,EA92DTlkB,EAAE,CAAAuT,iBAAA,CA82DyBvT,EAAE,CAACsB,UAAU,GA92DxCtB,EAAE,CAAAuT,iBAAA,CA82DmDvT,EAAE,CAACe,mBAAmB,GA92D3Ef,EAAE,CAAAuT,iBAAA,CA82DsFvT,EAAE,CAACQ,QAAQ,GA92DnGR,EAAE,CAAAuT,iBAAA,CA82D8GxF,eAAe,GA92D/H/N,EAAE,CAAAuT,iBAAA,CA82D0IvT,EAAE,CAACM,MAAM,GA92DrJN,EAAE,CAAAuT,iBAAA,CA82DgKvT,EAAE,CAACwT,iBAAiB;AAAA,CAA4C;AACtV;AAAmB0Q,MAAM,CAACzQ,IAAI,kBA/2DsFzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EA+2DX0f,MAAM;EAAArS,MAAA;IAAA9M,QAAA;IAAAX,SAAA;IAAAkgB,IAAA;IAAAC,UAAA;IAAA3gB,YAAA;EAAA;AAAA,EAAiJ;AAClQsgB,MAAM,GAAGrhB,UAAU,CAAC,CAChB8P,QAAQ,CAAC;EACLd,MAAM,EAAEmS,UAAU;EAClB1R,OAAO,EAAE2R;AACb,CAAC,CAAC,CACL,EAAEC,MAAM,CAAC;AACV;EAAA,QAAAre,SAAA,oBAAAA,SAAA,KAt3DoH7F,EAAE,CAAA8F,iBAAA,CAs3D1Boe,MAAM,EAAc,CAAC;IACrG1f,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC;MACA8L,MAAM,EAAEmS;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExf,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACe;IAAoB,CAAC,EAAE;MAAEyD,IAAI,EAAExE,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAEgE,IAAI,EAAEuJ;IAAgB,CAAC,EAAE;MAAEvJ,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACwT;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEtN;AACA,MAAMgR,OAAO,CAAC;EACVphB,WAAWA,CAACoV,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB;AACR;AACA;IACQ,IAAI,CAACiM,iBAAiB,GAAG,IAAI5jB,YAAY,CAAC,CAAC;IAC3C;AACR;AACA;IACQ,IAAI,CAAC6jB,gBAAgB,GAAG,IAAI7jB,YAAY,CAAC,CAAC;IAC1C,IAAI,CAAC8jB,UAAU,GAAG,QAAQ;EAC9B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC5B;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACD,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIE,iBAAiBA,CAAC;IAAEzN,YAAY;IAAE+B;EAAU,CAAC,EAAE;IAC3C,MAAMxC,OAAO,GAAGS,YAAY,CAACT,OAAO;IACpC,IAAIwC,SAAS,IAAIxC,OAAO,KAAKnN,SAAS,EAAE;MACpC,IAAI,CAAC+a,iBAAiB,CAACnF,IAAI,CAAC;QAAE0F,GAAG,EAAEnO;MAAQ,CAAC,CAAC;IACjD;EACJ;EACA;AACJ;AACA;EACIoO,gBAAgBA,CAAC;IAAE3N,YAAY;IAAE+B;EAAU,CAAC,EAAE;IAC1C,MAAMxC,OAAO,GAAGS,YAAY,CAACT,OAAO;IACpC,IAAIwC,SAAS,IAAIxC,OAAO,KAAKnN,SAAS,EAAE;MACpC,IAAI,IAAI,CAACwb,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACC,WAAW,GAAGtO,OAAO;MACrC;MACA,IAAI,CAAC6N,gBAAgB,CAACpF,IAAI,CAAC;QAAE0F,GAAG,EAAEnO;MAAQ,CAAC,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuO,MAAMA,CAACC,UAAU,EAAE;IACf,MAAMC,WAAW,GAAG,OAAOD,UAAU,KAAK,QAAQ;IAClD,MAAML,GAAG,GAAGM,WAAW,GAAGD,UAAU,GAAGA,UAAU,CAAC/a,MAAM,CAAC0a,GAAG;IAC5D,MAAMO,eAAe,GAAG,IAAI,CAACtZ,MAAM,CAACuO,gBAAgB,CAAC,CAAC,KAAKwK,GAAG;IAC9D,MAAMQ,UAAU,GAAG,GAAG,IAAI,CAACvZ,MAAM,CAACqM,UAAU,IAAI0M,GAAG,EAAE;IACrD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACM,WAAW,EAAE;MACdD,UAAU,CAACI,eAAe,CAAC,CAAC;IAChC;IACA,IAAIF,eAAe,EAAE;MACjB,MAAMG,aAAa,GAAG,IAAI,CAACzZ,MAAM,CAACuO,gBAAgB,CAAC,CAAC;MACpD,MAAMpB,UAAU,GAAG,IAAI,CAACnN,MAAM,CAACmU,gBAAgB,CAACsF,aAAa,CAAC;MAC9D;MACA,IAAItM,UAAU,EAAEjQ,GAAG,KAAKqc,UAAU,EAAE;QAChC;MACJ;MACA,MAAMG,QAAQ,GAAG,IAAI,CAAC1Z,MAAM,CAACoU,WAAW,CAAC2E,GAAG,CAAC;MAC7C,MAAMY,gBAAgB,GAAGD,QAAQ,IAAIH,UAAU,KAAKG,QAAQ,CAACxc,GAAG,IAAIwc,QAAQ,CAAC3K,WAAW;MACxF,OAAO,IAAI,CAACxC,OAAO,CAAC3M,YAAY,CAAC2Z,UAAU,EAAE;QACzC,GAAGI,gBAAgB;QACnB7gB,QAAQ,EAAE,IAAI;QACd2G,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAMma,SAAS,GAAG,IAAI,CAAC5Z,MAAM,CAACmU,gBAAgB,CAAC4E,GAAG,CAAC;MACnD;AACZ;AACA;AACA;MACY,MAAM7b,GAAG,GAAG0c,SAAS,EAAE1c,GAAG,IAAIqc,UAAU;MACxC,MAAMI,gBAAgB,GAAGC,SAAS,EAAE7K,WAAW;MAC/C,OAAO,IAAI,CAACxC,OAAO,CAAC3M,YAAY,CAAC1C,GAAG,EAAE;QAClC,GAAGyc,gBAAgB;QACnB7gB,QAAQ,EAAE,IAAI;QACd2G,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN;EACJ;EACAoa,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7Z,MAAM,CAACuO,gBAAgB,CAAC,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACkB,OAAO,CAAC5U,OAAO,CAAE+T,MAAM,IAAK;MAC7B;MACA,MAAMc,WAAW,GAAGd,MAAM,CAAC/a,EAAE,CAAC8b,YAAY,CAAC,MAAM,CAAC;MAClD,IAAID,WAAW,KAAK,IAAI,CAACrB,UAAU,EAAE;QACjC,IAAI,CAACA,UAAU,GAAGqB,WAAW;QAC7B,IAAI,CAACE,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIA,cAAcA,CAAA,EAAG;IACb;AACR;AACA;AACA;AACA;IACQ,MAAMhB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC/a,EAAE;IAC7B,IAAI,IAAI,CAACwa,UAAU,KAAK,KAAK,EAAE;MAC3B;AACZ;AACA;AACA;MACY,IAAI,CAACwB,SAAS,CAACpW,aAAa,CAACqW,MAAM,CAAClB,MAAM,CAAC;IAC/C,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,IAAI,CAACiB,SAAS,CAACpW,aAAa,CAACsW,KAAK,CAACnB,MAAM,CAAC;IAC9C;EACJ;AACJ;AACA;AAAmBV,OAAO,CAACnf,IAAI,YAAAihB,gBAAA/gB,CAAA;EAAA,YAAAA,CAAA,IAAyFif,OAAO,EAphEXxkB,EAAE,CAAAuT,iBAAA,CAohE2B/I,aAAa;AAAA,CAA4C;AAC1M;AAAmBga,OAAO,CAAC/Q,IAAI,kBArhEqFzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EAqhEVggB,OAAO;EAAA7Q,SAAA;EAAA4S,SAAA,WAAAC,cAAA1S,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MArhEC9T,EAAE,CAAAymB,WAAA,CAAAvjB,GAAA,KAqhE2R5B,UAAU;IAAA;IAAA,IAAAwS,EAAA;MAAA,IAAAI,EAAA;MArhEvSlU,EAAE,CAAAmU,cAAA,CAAAD,EAAA,GAAFlU,EAAE,CAAAoU,WAAA,QAAAL,GAAA,CAAAoS,SAAA,GAAAjS,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAA+N,YAAA,WAAAqE,qBAAA5S,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF9T,EAAE,CAAAuiB,UAAA,+BAAAoE,6CAAAlE,MAAA;QAAA,OAqhEV1O,GAAA,CAAAqR,MAAA,CAAA3C,MAAa,CAAC;MAAA,CAAR,CAAC;IAAA;EAAA;EAAA3B,OAAA;IAAA2D,iBAAA;IAAAC,gBAAA;EAAA;AAAA,EAA0U;AAC7b;EAAA,QAAA7e,SAAA,oBAAAA,SAAA,KAthEoH7F,EAAE,CAAA8F,iBAAA,CAshE1B0e,OAAO,EAAc,CAAC;IACtGhgB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhR,IAAI,EAAEgG;IAAc,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE2b,SAAS,EAAE,CAAC;MAC7F3hB,IAAI,EAAEjD,SAAS;MACfwE,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEd,IAAI,EAAE3D,UAAU;QAAEmU,MAAM,EAAE;MAAK,CAAC;IAC1D,CAAC,CAAC;IAAEgP,iBAAiB,EAAE,CAAC;MACpBjgB,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEujB,gBAAgB,EAAE,CAAC;MACnBlgB,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEikB,MAAM,EAAE,CAAC;MACT5gB,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6gB,GAAG,GAAIC,CAAC,IAAK;EACf,IAAI,OAAOC,oCAAoC,KAAK,UAAU,EAAE;IAC5D,OAAOA,oCAAoC,CAACD,CAAC,CAAC;EAClD;EACA,IAAI,OAAOzgB,qBAAqB,KAAK,UAAU,EAAE;IAC7C,OAAOA,qBAAqB,CAACygB,CAAC,CAAC;EACnC;EACA,OAAOE,UAAU,CAACF,CAAC,CAAC;AACxB,CAAC;;AAED;AACA,MAAMG,aAAa,CAAC;EAChB5jB,WAAWA,CAAC+K,QAAQ,EAAEyO,UAAU,EAAE;IAC9B,IAAI,CAACzO,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACyO,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACqK,QAAQ,GAAG,MAAM;MAClB;IAAA,CACH;IACD,IAAI,CAACC,SAAS,GAAG,MAAM;MACnB;IAAA,CACH;EACL;EACAC,UAAUA,CAAC9c,KAAK,EAAE;IACd,IAAI,CAACuS,UAAU,CAAC7M,aAAa,CAAC1F,KAAK,GAAG,IAAI,CAAC+c,SAAS,GAAG/c,KAAK;IAC5Dgd,eAAe,CAAC,IAAI,CAACzK,UAAU,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0K,iBAAiBA,CAACnd,EAAE,EAAEE,KAAK,EAAE;IACzB,IAAIF,EAAE,KAAK,IAAI,CAACyS,UAAU,CAAC7M,aAAa,EAAE;MACtC,IAAI1F,KAAK,KAAK,IAAI,CAAC+c,SAAS,EAAE;QAC1B,IAAI,CAACA,SAAS,GAAG/c,KAAK;QACtB,IAAI,CAAC4c,QAAQ,CAAC5c,KAAK,CAAC;MACxB;MACAgd,eAAe,CAAC,IAAI,CAACzK,UAAU,CAAC;IACpC;EACJ;EACA2K,gBAAgBA,CAACpd,EAAE,EAAE;IACjB,IAAIA,EAAE,KAAK,IAAI,CAACyS,UAAU,CAAC7M,aAAa,EAAE;MACtC,IAAI,CAACmX,SAAS,CAAC,CAAC;MAChBG,eAAe,CAAC,IAAI,CAACzK,UAAU,CAAC;IACpC;EACJ;EACA4K,gBAAgBA,CAACpW,EAAE,EAAE;IACjB,IAAI,CAAC6V,QAAQ,GAAG7V,EAAE;EACtB;EACAqW,iBAAiBA,CAACrW,EAAE,EAAE;IAClB,IAAI,CAAC8V,SAAS,GAAG9V,EAAE;EACvB;EACAsW,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC/K,UAAU,CAAC7M,aAAa,CAAC4S,QAAQ,GAAGgF,UAAU;EACvD;EACArJ,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACsJ,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACxG,WAAW,CAAC,CAAC;IACpC;EACJ;EACAyG,eAAeA,CAAA,EAAG;IACd,IAAIC,SAAS;IACb,IAAI;MACAA,SAAS,GAAG,IAAI,CAAC3Z,QAAQ,CAACpK,GAAG,CAACd,SAAS,CAAC;IAC5C,CAAC,CACD,MAAM;MACF;IAAA;IAEJ,IAAI,CAAC6kB,SAAS,EAAE;MACZ;IACJ;IACA;IACA,IAAIA,SAAS,CAACF,aAAa,EAAE;MACzB,IAAI,CAACA,aAAa,GAAGE,SAAS,CAACF,aAAa,CAACzgB,SAAS,CAAC,MAAMkgB,eAAe,CAAC,IAAI,CAACzK,UAAU,CAAC,CAAC;IAClG;IACA;AACR;AACA;AACA;IACQ,MAAMmL,WAAW,GAAGD,SAAS,CAACE,OAAO;IACrC,IAAID,WAAW,EAAE;MACb,MAAME,cAAc,GAAG,CAAC,eAAe,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,CAAC;MAChHA,cAAc,CAAC9W,OAAO,CAAE+W,MAAM,IAAK;QAC/B,IAAI,OAAOH,WAAW,CAACG,MAAM,CAAC,KAAK,WAAW,EAAE;UAC5C,MAAMC,KAAK,GAAGJ,WAAW,CAACG,MAAM,CAAC,CAACE,IAAI,CAACL,WAAW,CAAC;UACnDA,WAAW,CAACG,MAAM,CAAC,GAAG,CAAC,GAAGrZ,MAAM,KAAK;YACjCsZ,KAAK,CAAC,GAAGtZ,MAAM,CAAC;YAChBwY,eAAe,CAAC,IAAI,CAACzK,UAAU,CAAC;UACpC,CAAC;QACL;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AAAmBoK,aAAa,CAAC3hB,IAAI,YAAAgjB,sBAAA9iB,CAAA;EAAA,YAAAA,CAAA,IAAyFyhB,aAAa,EA7oEvBhnB,EAAE,CAAAuT,iBAAA,CA6oEuCvT,EAAE,CAACQ,QAAQ,GA7oEpDR,EAAE,CAAAuT,iBAAA,CA6oE+DvT,EAAE,CAACsB,UAAU;AAAA,CAA4C;AAC9O;AAAmB0lB,aAAa,CAACvT,IAAI,kBA9oE+EzT,EAAE,CAAA0T,iBAAA;EAAAlP,IAAA,EA8oEJwiB,aAAa;EAAA3E,YAAA,WAAAiG,2BAAAxU,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA9oEX9T,EAAE,CAAAuiB,UAAA,qBAAAgG,yCAAA9F,MAAA;QAAA,OA8oEJ1O,GAAA,CAAAwT,gBAAA,CAAA9E,MAAA,CAAA+F,MAA8B,CAAC;MAAA,CAAnB,CAAC;IAAA;EAAA;AAAA,EAAwF;AACvN;EAAA,QAAA3iB,SAAA,oBAAAA,SAAA,KA/oEoH7F,EAAE,CAAA8F,iBAAA,CA+oE1BkhB,aAAa,EAAc,CAAC;IAC5GxiB,IAAI,EAAE7D;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6D,IAAI,EAAExE,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAEgE,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEimB,gBAAgB,EAAE,CAAC;MAC3H/iB,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC;IACvC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMshB,eAAe,GAAIvW,OAAO,IAAK;EACjC8V,GAAG,CAAC,MAAM;IACN,MAAM6B,KAAK,GAAG3X,OAAO,CAACf,aAAa;IACnC,MAAM2Y,QAAQ,GAAGD,KAAK,CAACpe,KAAK,IAAI,IAAI,IAAIoe,KAAK,CAACpe,KAAK,CAACuC,QAAQ,CAAC,CAAC,CAACiL,MAAM,GAAG,CAAC;IACzE,MAAM8Q,OAAO,GAAGC,UAAU,CAACH,KAAK,CAAC;IACjCI,UAAU,CAACJ,KAAK,EAAEE,OAAO,CAAC;IAC1B,MAAM3W,IAAI,GAAGyW,KAAK,CAACK,OAAO,CAAC,UAAU,CAAC;IACtC,IAAI9W,IAAI,EAAE;MACN,IAAI0W,QAAQ,EAAE;QACVG,UAAU,CAAC7W,IAAI,EAAE,CAAC,GAAG2W,OAAO,EAAE,gBAAgB,CAAC,CAAC;MACpD,CAAC,MACI;QACDE,UAAU,CAAC7W,IAAI,EAAE2W,OAAO,CAAC;MAC7B;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAMC,UAAU,GAAI9X,OAAO,IAAK;EAC5B,MAAMP,SAAS,GAAGO,OAAO,CAACP,SAAS;EACnC,MAAMoY,OAAO,GAAG,EAAE;EAClB,KAAK,IAAI/Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrH,SAAS,CAACsH,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAM5F,IAAI,GAAGzB,SAAS,CAACyB,IAAI,CAAC4F,CAAC,CAAC;IAC9B,IAAI5F,IAAI,KAAK,IAAI,IAAI+W,UAAU,CAAC/W,IAAI,EAAE,KAAK,CAAC,EAAE;MAC1C2W,OAAO,CAAC7R,IAAI,CAAC,OAAO9E,IAAI,CAACgX,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C;EACJ;EACA,OAAOL,OAAO;AAClB,CAAC;AACD,MAAME,UAAU,GAAGA,CAAC/X,OAAO,EAAE6X,OAAO,KAAK;EACrC,MAAMpY,SAAS,GAAGO,OAAO,CAACP,SAAS;EACnCA,SAAS,CAAC0Y,MAAM,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,WAAW,EAAE,cAAc,CAAC;EACzG1Y,SAAS,CAACC,GAAG,CAAC,GAAGmY,OAAO,CAAC;AAC7B,CAAC;AACD,MAAMI,UAAU,GAAGA,CAACN,KAAK,EAAES,MAAM,KAAK;EAClC,OAAOT,KAAK,CAACO,SAAS,CAAC,CAAC,EAAEE,MAAM,CAACrR,MAAM,CAAC,KAAKqR,MAAM;AACvD,CAAC;;AAED;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;AACJ;AACA;EACIC,YAAYA,CAACC,MAAM,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACIC,YAAYA,CAACD,MAAM,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACIE,KAAKA,CAACF,MAAM,EAAEG,aAAa,EAAE;IACzB;EACJ;EACA;AACJ;AACA;EACIC,QAAQA,CAACJ,MAAM,EAAE;IACb,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIK,gBAAgBA,CAACC,MAAM,EAAEC,IAAI,EAAE;IAC3B,IAAID,MAAM,CAAC1J,WAAW,KAAK2J,IAAI,CAAC3J,WAAW,EAAE;MACzC,OAAO,KAAK;IAChB;IACA;IACA,MAAM4J,YAAY,GAAGF,MAAM,CAAC9a,MAAM;IAClC,MAAMib,aAAa,GAAGF,IAAI,CAAC/a,MAAM;IACjC,MAAMkb,KAAK,GAAG3Z,MAAM,CAAC4Z,IAAI,CAACH,YAAY,CAAC;IACvC,MAAMI,KAAK,GAAG7Z,MAAM,CAAC4Z,IAAI,CAACF,aAAa,CAAC;IACxC,IAAIC,KAAK,CAAClS,MAAM,KAAKoS,KAAK,CAACpS,MAAM,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA;IACA,KAAK,MAAMxP,GAAG,IAAI0hB,KAAK,EAAE;MACrB,IAAID,aAAa,CAACzhB,GAAG,CAAC,KAAKwhB,YAAY,CAACxhB,GAAG,CAAC,EAAE;QAC1C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;AACJ;;AAEA;AACA,MAAM6hB,qBAAqB,CAAC;EACxB9mB,WAAWA,CAAC+mB,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACIlc,MAAMA,CAAC2E,IAAI,EAAE;IACT,OAAO,IAAI,CAACuX,IAAI,CAAClc,MAAM,CAAE2E,IAAI,IAAI,CAAC,CAAE,CAAC;EACzC;EACA;AACJ;AACA;EACIwX,OAAOA,CAACvc,IAAI,EAAEwc,IAAI,EAAEnf,EAAE,EAAE;IACpB,OAAO,IAAI,CAACif,IAAI,CAACC,OAAO,CAACvc,IAAI,EAAEwc,IAAI,EAAEnf,EAAE,CAAC;EAC5C;EACA;AACJ;AACA;EACIof,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACH,IAAI,CAACG,MAAM,CAAC,CAAC;EAC7B;AACJ;;AAEA;AACA;AACA;;AAEA,SAASvc,eAAe,EAAEZ,MAAM,EAAEO,WAAW,EAAE1I,aAAa,EAAE8c,aAAa,EAAElM,QAAQ,EAAEsO,MAAM,EAAEhR,UAAU,EAAEuJ,eAAe,EAAE+H,OAAO,EAAE2E,kBAAkB,EAAEhmB,cAAc,EAAEqH,aAAa,EAAEoD,SAAS,EAAEsc,qBAAqB,EAAE7jB,QAAQ,EAAEsM,QAAQ,EAAEmQ,2BAA2B,EAAEc,mCAAmC,EAAEoD,aAAa,EAAEvW,mBAAmB,EAAEiR,4BAA4B,EAAEkF,GAAG,EAAES,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}