import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { RoleManagementService, UserRole, RoleConfig } from '../../../core/services/role-management.service';

export interface SettingsSection {
  id: string;
  title: string;
  icon: string;
  description: string;
  requiredPermission?: string;
  settings: SettingsItem[];
}

export interface SettingsItem {
  id: string;
  type: 'toggle' | 'select' | 'input' | 'range' | 'color' | 'file' | 'textarea';
  label: string;
  description?: string;
  value: any;
  options?: { label: string; value: any }[];
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  validation?: {
    required?: boolean;
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
  onChange?: (value: any) => void;
}

@Component({
  selector: 'app-role-based-settings',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="role-based-settings" [attr.data-role]="currentRole">
      <!-- Settings Header -->
      <div class="settings-header">
        <div class="header-content">
          <div class="role-indicator" [style.background]="roleConfig?.color">
            <i [class]="roleConfig?.icon"></i>
          </div>
          <div class="header-text">
            <h1>Settings</h1>
            <p>{{ roleConfig?.displayName }} - {{ roleConfig?.department | titlecase }}</p>
          </div>
        </div>
      </div>

      <!-- Settings Navigation -->
      <div class="settings-navigation">
        <div class="nav-tabs">
          <button 
            *ngFor="let section of getAvailableSections()" 
            class="nav-tab"
            [class.active]="activeSection === section.id"
            (click)="setActiveSection(section.id)">
            <i [class]="section.icon"></i>
            <span>{{ section.title }}</span>
          </button>
        </div>
      </div>

      <!-- Settings Content -->
      <div class="settings-content">
        <div *ngFor="let section of getAvailableSections()" 
             class="settings-section"
             [class.active]="activeSection === section.id">
          
          <div class="section-header">
            <h2>{{ section.title }}</h2>
            <p>{{ section.description }}</p>
          </div>

          <div class="settings-grid">
            <div *ngFor="let setting of section.settings" class="setting-item">
              <div class="setting-info">
                <label [for]="setting.id" class="setting-label">{{ setting.label }}</label>
                <p *ngIf="setting.description" class="setting-description">{{ setting.description }}</p>
              </div>

              <div class="setting-control">
                <!-- Toggle Switch -->
                <div *ngIf="setting.type === 'toggle'" class="toggle-switch">
                  <input 
                    type="checkbox" 
                    [id]="setting.id"
                    [(ngModel)]="setting.value"
                    (change)="onSettingChange(setting, $event)"
                    class="toggle-input">
                  <label [for]="setting.id" class="toggle-label"></label>
                </div>

                <!-- Select Dropdown -->
                <select 
                  *ngIf="setting.type === 'select'"
                  [id]="setting.id"
                  [(ngModel)]="setting.value"
                  (change)="onSettingChange(setting, $event)"
                  class="select-input">
                  <option *ngFor="let option of setting.options" [value]="option.value">
                    {{ option.label }}
                  </option>
                </select>

                <!-- Text Input -->
                <input 
                  *ngIf="setting.type === 'input'"
                  type="text"
                  [id]="setting.id"
                  [(ngModel)]="setting.value"
                  (input)="onSettingChange(setting, $event)"
                  [placeholder]="setting.placeholder"
                  class="text-input">

                <!-- Range Slider -->
                <div *ngIf="setting.type === 'range'" class="range-container">
                  <input 
                    type="range"
                    [id]="setting.id"
                    [(ngModel)]="setting.value"
                    (input)="onSettingChange(setting, $event)"
                    [min]="setting.min"
                    [max]="setting.max"
                    [step]="setting.step"
                    class="range-input">
                  <span class="range-value">{{ setting.value }}</span>
                </div>

                <!-- Color Picker -->
                <input 
                  *ngIf="setting.type === 'color'"
                  type="color"
                  [id]="setting.id"
                  [(ngModel)]="setting.value"
                  (change)="onSettingChange(setting, $event)"
                  class="color-input">

                <!-- File Upload -->
                <div *ngIf="setting.type === 'file'" class="file-upload">
                  <input 
                    type="file"
                    [id]="setting.id"
                    (change)="onFileChange(setting, $event)"
                    class="file-input"
                    hidden>
                  <label [for]="setting.id" class="file-label">
                    <i class="fas fa-upload"></i>
                    <span>Choose File</span>
                  </label>
                  <span *ngIf="setting.value" class="file-name">{{ getFileName(setting.value) }}</span>
                </div>

                <!-- Textarea -->
                <textarea 
                  *ngIf="setting.type === 'textarea'"
                  [id]="setting.id"
                  [(ngModel)]="setting.value"
                  (input)="onSettingChange(setting, $event)"
                  [placeholder]="setting.placeholder"
                  class="textarea-input"
                  rows="4">
                </textarea>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Actions -->
      <div class="settings-actions">
        <button class="btn btn-secondary" (click)="resetToDefaults()">
          <i class="fas fa-undo"></i>
          Reset to Defaults
        </button>
        <button class="btn btn-primary" (click)="saveSettings()">
          <i class="fas fa-save"></i>
          Save Changes
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./role-based-settings.component.scss']
})
export class RoleBasedSettingsComponent implements OnInit, OnDestroy {
  @Input() currentRole: UserRole | null = null;
  
  roleConfig: RoleConfig | null = null;
  activeSection: string = 'profile';
  
  private destroy$ = new Subject<void>();

  constructor(private roleManagementService: RoleManagementService) {}

  ngOnInit() {
    if (this.currentRole) {
      this.roleConfig = this.roleManagementService.getRoleConfig(this.currentRole);
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getAvailableSections(): SettingsSection[] {
    if (!this.currentRole) return [];

    const baseSections: SettingsSection[] = [
      {
        id: 'profile',
        title: 'Profile',
        icon: 'fas fa-user',
        description: 'Manage your personal profile information',
        settings: [
          {
            id: 'displayName',
            type: 'input',
            label: 'Display Name',
            description: 'Your name as it appears to others',
            value: '',
            placeholder: 'Enter your display name'
          },
          {
            id: 'bio',
            type: 'textarea',
            label: 'Bio',
            description: 'Tell others about yourself',
            value: '',
            placeholder: 'Write a short bio...'
          },
          {
            id: 'avatar',
            type: 'file',
            label: 'Profile Picture',
            description: 'Upload a profile picture',
            value: null
          },
          {
            id: 'publicProfile',
            type: 'toggle',
            label: 'Public Profile',
            description: 'Make your profile visible to everyone',
            value: true
          }
        ]
      },
      {
        id: 'notifications',
        title: 'Notifications',
        icon: 'fas fa-bell',
        description: 'Configure your notification preferences',
        settings: [
          {
            id: 'emailNotifications',
            type: 'toggle',
            label: 'Email Notifications',
            description: 'Receive notifications via email',
            value: true
          },
          {
            id: 'pushNotifications',
            type: 'toggle',
            label: 'Push Notifications',
            description: 'Receive push notifications in browser',
            value: true
          },
          {
            id: 'notificationFrequency',
            type: 'select',
            label: 'Notification Frequency',
            description: 'How often to receive notifications',
            value: 'immediate',
            options: [
              { label: 'Immediate', value: 'immediate' },
              { label: 'Hourly', value: 'hourly' },
              { label: 'Daily', value: 'daily' },
              { label: 'Weekly', value: 'weekly' }
            ]
          }
        ]
      },
      {
        id: 'privacy',
        title: 'Privacy',
        icon: 'fas fa-shield-alt',
        description: 'Control your privacy and security settings',
        settings: [
          {
            id: 'profileVisibility',
            type: 'select',
            label: 'Profile Visibility',
            description: 'Who can see your profile',
            value: 'team',
            options: [
              { label: 'Everyone', value: 'public' },
              { label: 'Team Members', value: 'team' },
              { label: 'Department Only', value: 'department' },
              { label: 'Private', value: 'private' }
            ]
          },
          {
            id: 'showOnlineStatus',
            type: 'toggle',
            label: 'Show Online Status',
            description: 'Let others see when you\'re online',
            value: true
          },
          {
            id: 'allowDirectMessages',
            type: 'toggle',
            label: 'Allow Direct Messages',
            description: 'Allow others to send you direct messages',
            value: true
          }
        ]
      }
    ];

    // Add role-specific sections
    const roleSpecificSections = this.getRoleSpecificSections();
    return [...baseSections, ...roleSpecificSections];
  }

  getRoleSpecificSections(): SettingsSection[] {
    if (!this.currentRole) return [];

    const sections: SettingsSection[] = [];

    // Admin-specific settings
    if (this.currentRole === 'super_admin' || this.currentRole === 'admin') {
      sections.push({
        id: 'system',
        title: 'System',
        icon: 'fas fa-cogs',
        description: 'System-wide configuration settings',
        requiredPermission: 'system.manage',
        settings: [
          {
            id: 'maintenanceMode',
            type: 'toggle',
            label: 'Maintenance Mode',
            description: 'Enable maintenance mode for the system',
            value: false
          },
          {
            id: 'userRegistration',
            type: 'toggle',
            label: 'User Registration',
            description: 'Allow new user registrations',
            value: true
          },
          {
            id: 'sessionTimeout',
            type: 'range',
            label: 'Session Timeout (minutes)',
            description: 'Automatic logout after inactivity',
            value: 30,
            min: 5,
            max: 480,
            step: 5
          }
        ]
      });
    }

    // Manager-specific settings
    if (this.roleManagementService.isManager(this.currentRole)) {
      sections.push({
        id: 'team',
        title: 'Team Management',
        icon: 'fas fa-users',
        description: 'Manage your team settings and preferences',
        settings: [
          {
            id: 'teamVisibility',
            type: 'select',
            label: 'Team Visibility',
            description: 'Who can see your team information',
            value: 'department',
            options: [
              { label: 'Public', value: 'public' },
              { label: 'Department', value: 'department' },
              { label: 'Team Only', value: 'team' }
            ]
          },
          {
            id: 'autoAssignTasks',
            type: 'toggle',
            label: 'Auto-assign Tasks',
            description: 'Automatically assign tasks to team members',
            value: false
          },
          {
            id: 'teamReportFrequency',
            type: 'select',
            label: 'Team Report Frequency',
            description: 'How often to generate team reports',
            value: 'weekly',
            options: [
              { label: 'Daily', value: 'daily' },
              { label: 'Weekly', value: 'weekly' },
              { label: 'Monthly', value: 'monthly' }
            ]
          }
        ]
      });
    }

    // Department-specific settings
    const departmentSections = this.getDepartmentSpecificSections();
    sections.push(...departmentSections);

    return sections;
  }

  getDepartmentSpecificSections(): SettingsSection[] {
    if (!this.roleConfig) return [];

    const sections: SettingsSection[] = [];

    switch (this.roleConfig.department) {
      case 'sales':
        sections.push({
          id: 'sales',
          title: 'Sales Settings',
          icon: 'fas fa-chart-line',
          description: 'Configure sales-specific preferences',
          settings: [
            {
              id: 'salesTarget',
              type: 'input',
              label: 'Monthly Sales Target',
              description: 'Your monthly sales target amount',
              value: '',
              placeholder: 'Enter target amount'
            },
            {
              id: 'commissionRate',
              type: 'range',
              label: 'Commission Rate (%)',
              description: 'Your commission rate percentage',
              value: 5,
              min: 0,
              max: 20,
              step: 0.5
            },
            {
              id: 'followUpReminders',
              type: 'toggle',
              label: 'Follow-up Reminders',
              description: 'Get reminders for customer follow-ups',
              value: true
            }
          ]
        });
        break;

      case 'marketing':
        sections.push({
          id: 'marketing',
          title: 'Marketing Settings',
          icon: 'fas fa-bullhorn',
          description: 'Configure marketing-specific preferences',
          settings: [
            {
              id: 'campaignBudget',
              type: 'input',
              label: 'Monthly Campaign Budget',
              description: 'Your monthly campaign budget',
              value: '',
              placeholder: 'Enter budget amount'
            },
            {
              id: 'contentApproval',
              type: 'toggle',
              label: 'Content Auto-approval',
              description: 'Automatically approve content you create',
              value: false
            },
            {
              id: 'socialPlatforms',
              type: 'select',
              label: 'Primary Social Platform',
              description: 'Your primary social media platform',
              value: 'instagram',
              options: [
                { label: 'Instagram', value: 'instagram' },
                { label: 'Facebook', value: 'facebook' },
                { label: 'Twitter', value: 'twitter' },
                { label: 'LinkedIn', value: 'linkedin' }
              ]
            }
          ]
        });
        break;

      case 'support':
        sections.push({
          id: 'support',
          title: 'Support Settings',
          icon: 'fas fa-headset',
          description: 'Configure support-specific preferences',
          settings: [
            {
              id: 'ticketAutoAssign',
              type: 'toggle',
              label: 'Auto-assign Tickets',
              description: 'Automatically assign tickets to you',
              value: true
            },
            {
              id: 'responseTimeTarget',
              type: 'range',
              label: 'Response Time Target (hours)',
              description: 'Your target response time for tickets',
              value: 2,
              min: 1,
              max: 24,
              step: 1
            },
            {
              id: 'escalationThreshold',
              type: 'range',
              label: 'Escalation Threshold (hours)',
              description: 'Auto-escalate tickets after this time',
              value: 8,
              min: 1,
              max: 48,
              step: 1
            }
          ]
        });
        break;
    }

    return sections;
  }

  setActiveSection(sectionId: string): void {
    this.activeSection = sectionId;
  }

  onSettingChange(setting: SettingsItem, event: any): void {
    const value = event.target ? event.target.value : event;
    setting.value = setting.type === 'toggle' ? event.target.checked : value;
    
    if (setting.onChange) {
      setting.onChange(setting.value);
    }
  }

  onFileChange(setting: SettingsItem, event: any): void {
    const file = event.target.files[0];
    if (file) {
      setting.value = file;
      if (setting.onChange) {
        setting.onChange(file);
      }
    }
  }

  getFileName(file: File): string {
    return file ? file.name : '';
  }

  resetToDefaults(): void {
    // Reset all settings to default values
    console.log('Resetting to defaults...');
  }

  saveSettings(): void {
    // Save all settings
    console.log('Saving settings...');

    // Show success message
    this.showSuccessMessage('Settings saved successfully!');
  }

  private showSuccessMessage(message: string): void {
    // Implementation for showing success message
    console.log(message);
  }
}
