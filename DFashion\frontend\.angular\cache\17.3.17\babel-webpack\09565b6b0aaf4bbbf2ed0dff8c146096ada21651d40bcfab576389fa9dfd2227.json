{"ast": null, "code": "/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText(text, x, y, styles, doc) {\n  styles = styles || {};\n  var PHYSICAL_LINE_HEIGHT = 1.15;\n  var k = doc.internal.scaleFactor;\n  var fontSize = doc.internal.getFontSize() / k;\n  var lineHeightFactor = doc.getLineHeightFactor ? doc.getLineHeightFactor() : PHYSICAL_LINE_HEIGHT;\n  var lineHeight = fontSize * lineHeightFactor;\n  var splitRegex = /\\r\\n|\\r|\\n/g;\n  var splitText = '';\n  var lineCount = 1;\n  if (styles.valign === 'middle' || styles.valign === 'bottom' || styles.halign === 'center' || styles.halign === 'right') {\n    splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n    lineCount = splitText.length || 1;\n  }\n  // Align the top\n  y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n  if (styles.valign === 'middle') y -= lineCount / 2 * lineHeight;else if (styles.valign === 'bottom') y -= lineCount * lineHeight;\n  if (styles.halign === 'center' || styles.halign === 'right') {\n    var alignSize = fontSize;\n    if (styles.halign === 'center') alignSize *= 0.5;\n    if (splitText && lineCount >= 1) {\n      for (var iLine = 0; iLine < splitText.length; iLine++) {\n        doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n        y += lineHeight;\n      }\n      return doc;\n    }\n    x -= doc.getStringUnitWidth(text) * alignSize;\n  }\n  if (styles.halign === 'justify') {\n    doc.text(text, x, y, {\n      maxWidth: styles.maxWidth || 100,\n      align: 'justify'\n    });\n  } else {\n    doc.text(text, x, y);\n  }\n  return doc;\n}\nvar globalDefaults = {};\nvar DocHandler = /** @class */function () {\n  function DocHandler(jsPDFDocument) {\n    this.jsPDFDocument = jsPDFDocument;\n    this.userStyles = {\n      // Black for versions of jspdf without getTextColor\n      textColor: jsPDFDocument.getTextColor ? this.jsPDFDocument.getTextColor() : 0,\n      fontSize: jsPDFDocument.internal.getFontSize(),\n      fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n      font: jsPDFDocument.internal.getFont().fontName,\n      // 0 for versions of jspdf without getLineWidth\n      lineWidth: jsPDFDocument.getLineWidth ? this.jsPDFDocument.getLineWidth() : 0,\n      // Black for versions of jspdf without getDrawColor\n      lineColor: jsPDFDocument.getDrawColor ? this.jsPDFDocument.getDrawColor() : 0\n    };\n  }\n  DocHandler.setDefaults = function (defaults, doc) {\n    if (doc === void 0) {\n      doc = null;\n    }\n    if (doc) {\n      doc.__autoTableDocumentDefaults = defaults;\n    } else {\n      globalDefaults = defaults;\n    }\n  };\n  DocHandler.unifyColor = function (c) {\n    if (Array.isArray(c)) {\n      return c;\n    } else if (typeof c === 'number') {\n      return [c, c, c];\n    } else if (typeof c === 'string') {\n      return [c];\n    } else {\n      return null;\n    }\n  };\n  DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n    // Font style needs to be applied before font\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n    var _a, _b, _c;\n    if (fontOnly === void 0) {\n      fontOnly = false;\n    }\n    if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n      this.jsPDFDocument.setFontStyle(styles.fontStyle);\n    }\n    var _d = this.jsPDFDocument.internal.getFont(),\n      fontStyle = _d.fontStyle,\n      fontName = _d.fontName;\n    if (styles.font) fontName = styles.font;\n    if (styles.fontStyle) {\n      fontStyle = styles.fontStyle;\n      var availableFontStyles = this.getFontList()[fontName];\n      if (availableFontStyles && availableFontStyles.indexOf(fontStyle) === -1 && this.jsPDFDocument.setFontStyle) {\n        // Common issue was that the default bold in headers\n        // made custom fonts not work. For example:\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n        this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n        fontStyle = availableFontStyles[0];\n      }\n    }\n    this.jsPDFDocument.setFont(fontName, fontStyle);\n    if (styles.fontSize) this.jsPDFDocument.setFontSize(styles.fontSize);\n    if (fontOnly) {\n      return; // Performance improvement\n    }\n    var color = DocHandler.unifyColor(styles.fillColor);\n    if (color) (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n    color = DocHandler.unifyColor(styles.textColor);\n    if (color) (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n    color = DocHandler.unifyColor(styles.lineColor);\n    if (color) (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n    if (typeof styles.lineWidth === 'number') {\n      this.jsPDFDocument.setLineWidth(styles.lineWidth);\n    }\n  };\n  DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n    return this.jsPDFDocument.splitTextToSize(text, size, opts);\n  };\n  /**\n   * Adds a rectangle to the PDF\n   * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n   * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n   * @param width Width (in units declared at inception of PDF document)\n   * @param height Height (in units declared at inception of PDF document)\n   * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n   */\n  DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n    // null is excluded from fillStyle possible values because it isn't needed\n    // and is prone to bugs as it's used to postpone setting the style\n    // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n    return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n  };\n  DocHandler.prototype.getLastAutoTable = function () {\n    return this.jsPDFDocument.lastAutoTable || null;\n  };\n  DocHandler.prototype.getTextWidth = function (text) {\n    return this.jsPDFDocument.getTextWidth(text);\n  };\n  DocHandler.prototype.getDocument = function () {\n    return this.jsPDFDocument;\n  };\n  DocHandler.prototype.setPage = function (page) {\n    this.jsPDFDocument.setPage(page);\n  };\n  DocHandler.prototype.addPage = function () {\n    return this.jsPDFDocument.addPage();\n  };\n  DocHandler.prototype.getFontList = function () {\n    return this.jsPDFDocument.getFontList();\n  };\n  DocHandler.prototype.getGlobalOptions = function () {\n    return globalDefaults || {};\n  };\n  DocHandler.prototype.getDocumentOptions = function () {\n    return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n  };\n  DocHandler.prototype.pageSize = function () {\n    var pageSize = this.jsPDFDocument.internal.pageSize;\n    // JSPDF 1.4 uses get functions instead of properties on pageSize\n    if (pageSize.width == null) {\n      pageSize = {\n        width: pageSize.getWidth(),\n        height: pageSize.getHeight()\n      };\n    }\n    return pageSize;\n  };\n  DocHandler.prototype.scaleFactor = function () {\n    return this.jsPDFDocument.internal.scaleFactor;\n  };\n  DocHandler.prototype.getLineHeightFactor = function () {\n    var doc = this.jsPDFDocument;\n    return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n  };\n  DocHandler.prototype.getLineHeight = function (fontSize) {\n    return fontSize / this.scaleFactor() * this.getLineHeightFactor();\n  };\n  DocHandler.prototype.pageNumber = function () {\n    var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n    if (!pageInfo) {\n      // Only recent versions of jspdf has pageInfo\n      return this.jsPDFDocument.internal.getNumberOfPages();\n    }\n    return pageInfo.pageNumber;\n  };\n  return DocHandler;\n}();\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nvar HtmlRowInput = /** @class */function (_super) {\n  __extends(HtmlRowInput, _super);\n  function HtmlRowInput(element) {\n    var _this = _super.call(this) || this;\n    _this._element = element;\n    return _this;\n  }\n  return HtmlRowInput;\n}(Array);\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n  return {\n    font: 'helvetica',\n    // helvetica, times, courier\n    fontStyle: 'normal',\n    // normal, bold, italic, bolditalic\n    overflow: 'linebreak',\n    // linebreak, ellipsize, visible or hidden\n    fillColor: false,\n    // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n    textColor: 20,\n    halign: 'left',\n    // left, center, right, justify\n    valign: 'top',\n    // top, middle, bottom\n    fontSize: 10,\n    cellPadding: 5 / scaleFactor,\n    // number or {top,left,right,left,vertical,horizontal}\n    lineColor: 200,\n    lineWidth: 0,\n    cellWidth: 'auto',\n    // 'auto'|'wrap'|number\n    minCellHeight: 0,\n    minCellWidth: 0\n  };\n}\nfunction getTheme(name) {\n  var themes = {\n    striped: {\n      table: {\n        fillColor: 255,\n        textColor: 80,\n        fontStyle: 'normal'\n      },\n      head: {\n        textColor: 255,\n        fillColor: [41, 128, 185],\n        fontStyle: 'bold'\n      },\n      body: {},\n      foot: {\n        textColor: 255,\n        fillColor: [41, 128, 185],\n        fontStyle: 'bold'\n      },\n      alternateRow: {\n        fillColor: 245\n      }\n    },\n    grid: {\n      table: {\n        fillColor: 255,\n        textColor: 80,\n        fontStyle: 'normal',\n        lineWidth: 0.1\n      },\n      head: {\n        textColor: 255,\n        fillColor: [26, 188, 156],\n        fontStyle: 'bold',\n        lineWidth: 0\n      },\n      body: {},\n      foot: {\n        textColor: 255,\n        fillColor: [26, 188, 156],\n        fontStyle: 'bold',\n        lineWidth: 0\n      },\n      alternateRow: {}\n    },\n    plain: {\n      head: {\n        fontStyle: 'bold'\n      },\n      foot: {\n        fontStyle: 'bold'\n      }\n    }\n  };\n  return themes[name];\n}\nfunction getStringWidth(text, styles, doc) {\n  doc.applyStyles(styles, true);\n  var textArr = Array.isArray(text) ? text : [text];\n  var widestLineWidth = textArr.map(function (text) {\n    return doc.getTextWidth(text);\n  }).reduce(function (a, b) {\n    return Math.max(a, b);\n  }, 0);\n  return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n  var lineWidth = table.settings.tableLineWidth;\n  var lineColor = table.settings.tableLineColor;\n  doc.applyStyles({\n    lineWidth: lineWidth,\n    lineColor: lineColor\n  });\n  var fillStyle = getFillStyle(lineWidth, false);\n  if (fillStyle) {\n    doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n  }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n  var drawLine = lineWidth > 0;\n  var drawBackground = fillColor || fillColor === 0;\n  if (drawLine && drawBackground) {\n    return 'DF'; // Fill then stroke\n  } else if (drawLine) {\n    return 'S'; // Only stroke (transparent background)\n  } else if (drawBackground) {\n    return 'F'; // Only fill, no stroke\n  } else {\n    return null;\n  }\n}\nfunction parseSpacing(value, defaultValue) {\n  var _a, _b, _c, _d;\n  value = value || defaultValue;\n  if (Array.isArray(value)) {\n    if (value.length >= 4) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[2],\n        left: value[3]\n      };\n    } else if (value.length === 3) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[2],\n        left: value[1]\n      };\n    } else if (value.length === 2) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[0],\n        left: value[1]\n      };\n    } else if (value.length === 1) {\n      value = value[0];\n    } else {\n      value = defaultValue;\n    }\n  }\n  if (typeof value === 'object') {\n    if (typeof value.vertical === 'number') {\n      value.top = value.vertical;\n      value.bottom = value.vertical;\n    }\n    if (typeof value.horizontal === 'number') {\n      value.right = value.horizontal;\n      value.left = value.horizontal;\n    }\n    return {\n      left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n      top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n      right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n      bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue\n    };\n  }\n  if (typeof value !== 'number') {\n    value = defaultValue;\n  }\n  return {\n    top: value,\n    right: value,\n    bottom: value,\n    left: value\n  };\n}\nfunction getPageAvailableWidth(doc, table) {\n  var margins = parseSpacing(table.settings.margin, 0);\n  return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n  var result = {};\n  var pxScaleFactor = 96 / 72;\n  var backgroundColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)['backgroundColor'];\n  });\n  if (backgroundColor != null) result.fillColor = backgroundColor;\n  var textColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)['color'];\n  });\n  if (textColor != null) result.textColor = textColor;\n  var padding = parsePadding(style, scaleFactor);\n  if (padding) result.cellPadding = padding;\n  var borderColorSide = 'borderTopColor';\n  var finalScaleFactor = pxScaleFactor * scaleFactor;\n  var btw = style.borderTopWidth;\n  if (style.borderBottomWidth === btw && style.borderRightWidth === btw && style.borderLeftWidth === btw) {\n    var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n    if (borderWidth) result.lineWidth = borderWidth;\n  } else {\n    result.lineWidth = {\n      top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n      right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n      bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n      left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor\n    };\n    // Choose border color of first available side\n    // could be improved by supporting object as lineColor\n    if (!result.lineWidth.top) {\n      if (result.lineWidth.right) {\n        borderColorSide = 'borderRightColor';\n      } else if (result.lineWidth.bottom) {\n        borderColorSide = 'borderBottomColor';\n      } else if (result.lineWidth.left) {\n        borderColorSide = 'borderLeftColor';\n      }\n    }\n  }\n  var borderColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)[borderColorSide];\n  });\n  if (borderColor != null) result.lineColor = borderColor;\n  var accepted = ['left', 'right', 'center', 'justify'];\n  if (accepted.indexOf(style.textAlign) !== -1) {\n    result.halign = style.textAlign;\n  }\n  accepted = ['middle', 'bottom', 'top'];\n  if (accepted.indexOf(style.verticalAlign) !== -1) {\n    result.valign = style.verticalAlign;\n  }\n  var res = parseInt(style.fontSize || '');\n  if (!isNaN(res)) result.fontSize = res / pxScaleFactor;\n  var fontStyle = parseFontStyle(style);\n  if (fontStyle) result.fontStyle = fontStyle;\n  var font = (style.fontFamily || '').toLowerCase();\n  if (supportedFonts.indexOf(font) !== -1) {\n    result.font = font;\n  }\n  return result;\n}\nfunction parseFontStyle(style) {\n  var res = '';\n  if (style.fontWeight === 'bold' || style.fontWeight === 'bolder' || parseInt(style.fontWeight) >= 700) {\n    res = 'bold';\n  }\n  if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n    res += 'italic';\n  }\n  return res;\n}\nfunction parseColor(element, styleGetter) {\n  var cssColor = realColor(element, styleGetter);\n  if (!cssColor) return null;\n  var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n  if (!rgba || !Array.isArray(rgba)) {\n    return null;\n  }\n  var color = [parseInt(rgba[1]), parseInt(rgba[2]), parseInt(rgba[3])];\n  var alpha = parseInt(rgba[4]);\n  if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n    return null;\n  }\n  return color;\n}\nfunction realColor(elem, styleGetter) {\n  var bg = styleGetter(elem);\n  if (bg === 'rgba(0, 0, 0, 0)' || bg === 'transparent' || bg === 'initial' || bg === 'inherit') {\n    if (elem.parentElement == null) {\n      return null;\n    }\n    return realColor(elem.parentElement, styleGetter);\n  } else {\n    return bg;\n  }\n}\nfunction parsePadding(style, scaleFactor) {\n  var val = [style.paddingTop, style.paddingRight, style.paddingBottom, style.paddingLeft];\n  var pxScaleFactor = 96 / (72 / scaleFactor);\n  var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n  var inputPadding = val.map(function (n) {\n    return parseInt(n || '0') / pxScaleFactor;\n  });\n  var padding = parseSpacing(inputPadding, 0);\n  if (linePadding > padding.top) {\n    padding.top = linePadding;\n  }\n  if (linePadding > padding.bottom) {\n    padding.bottom = linePadding;\n  }\n  return padding;\n}\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n  var _a, _b;\n  if (includeHiddenHtml === void 0) {\n    includeHiddenHtml = false;\n  }\n  if (useCss === void 0) {\n    useCss = false;\n  }\n  var tableElement;\n  if (typeof input === 'string') {\n    tableElement = window.document.querySelector(input);\n  } else {\n    tableElement = input;\n  }\n  var supportedFonts = Object.keys(doc.getFontList());\n  var scaleFactor = doc.scaleFactor();\n  var head = [],\n    body = [],\n    foot = [];\n  if (!tableElement) {\n    console.error('Html table could not be found with input: ', input);\n    return {\n      head: head,\n      body: body,\n      foot: foot\n    };\n  }\n  for (var i = 0; i < tableElement.rows.length; i++) {\n    var element = tableElement.rows[i];\n    var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n    var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n    if (!row) continue;\n    if (tagName === 'thead') {\n      head.push(row);\n    } else if (tagName === 'tfoot') {\n      foot.push(row);\n    } else {\n      // Add to body both if parent is tbody or table\n      body.push(row);\n    }\n  }\n  return {\n    head: head,\n    body: body,\n    foot: foot\n  };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n  var resultRow = new HtmlRowInput(row);\n  for (var i = 0; i < row.cells.length; i++) {\n    var cell = row.cells[i];\n    var style_1 = window.getComputedStyle(cell);\n    if (includeHidden || style_1.display !== 'none') {\n      var cellStyles = void 0;\n      if (useCss) {\n        cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n      }\n      resultRow.push({\n        rowSpan: cell.rowSpan,\n        colSpan: cell.colSpan,\n        styles: cellStyles,\n        _element: cell,\n        content: parseCellContent(cell)\n      });\n    }\n  }\n  var style = window.getComputedStyle(row);\n  if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n    return resultRow;\n  }\n}\nfunction parseCellContent(orgCell) {\n  // Work on cloned node to make sure no changes are applied to html table\n  var cell = orgCell.cloneNode(true);\n  // Remove extra space and line breaks in markup to make it more similar to\n  // what would be shown in html\n  cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n  // Preserve <br> tags as line breaks in the pdf\n  cell.innerHTML = cell.innerHTML.split(/<br.*?>/) //start with '<br' and ends with '>'.\n  .map(function (part) {\n    return part.trim();\n  }).join('\\n');\n  // innerText for ie\n  return cell.innerText || cell.textContent || '';\n}\nfunction validateInput(global, document, current) {\n  for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n    var options = _a[_i];\n    if (options && typeof options !== 'object') {\n      console.error('The options parameter should be of type object, is: ' + typeof options);\n    }\n    if (options.startY && typeof options.startY !== 'number') {\n      console.error('Invalid value for startY option', options.startY);\n      delete options.startY;\n    }\n  }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n  if (target == null) {\n    throw new TypeError('Cannot convert undefined or null to object');\n  }\n  var to = Object(target);\n  for (var index = 1; index < arguments.length; index++) {\n    // eslint-disable-next-line prefer-rest-params\n    var nextSource = arguments[index];\n    if (nextSource != null) {\n      // Skip over if undefined or null\n      for (var nextKey in nextSource) {\n        // Avoid bugs when hasOwnProperty is shadowed\n        if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n          to[nextKey] = nextSource[nextKey];\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction parseInput(d, current) {\n  var doc = new DocHandler(d);\n  var document = doc.getDocumentOptions();\n  var global = doc.getGlobalOptions();\n  validateInput(global, document, current);\n  var options = assign({}, global, document, current);\n  var win;\n  if (typeof window !== 'undefined') {\n    win = window;\n  }\n  var styles = parseStyles(global, document, current);\n  var hooks = parseHooks(global, document, current);\n  var settings = parseSettings(doc, options);\n  var content = parseContent$1(doc, options, win);\n  return {\n    id: current.tableId,\n    content: content,\n    hooks: hooks,\n    styles: styles,\n    settings: settings\n  };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n  var styleOptions = {\n    styles: {},\n    headStyles: {},\n    bodyStyles: {},\n    footStyles: {},\n    alternateRowStyles: {},\n    columnStyles: {}\n  };\n  var _loop_1 = function (prop) {\n    if (prop === 'columnStyles') {\n      var global_1 = gInput[prop];\n      var document_1 = dInput[prop];\n      var current = cInput[prop];\n      styleOptions.columnStyles = assign({}, global_1, document_1, current);\n    } else {\n      var allOptions = [gInput, dInput, cInput];\n      var styles = allOptions.map(function (opts) {\n        return opts[prop] || {};\n      });\n      styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n    }\n  };\n  for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n    var prop = _a[_i];\n    _loop_1(prop);\n  }\n  return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n  var allOptions = [global, document, current];\n  var result = {\n    didParseCell: [],\n    willDrawCell: [],\n    didDrawCell: [],\n    willDrawPage: [],\n    didDrawPage: []\n  };\n  for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n    var options = allOptions_1[_i];\n    if (options.didParseCell) result.didParseCell.push(options.didParseCell);\n    if (options.willDrawCell) result.willDrawCell.push(options.willDrawCell);\n    if (options.didDrawCell) result.didDrawCell.push(options.didDrawCell);\n    if (options.willDrawPage) result.willDrawPage.push(options.willDrawPage);\n    if (options.didDrawPage) result.didDrawPage.push(options.didDrawPage);\n  }\n  return result;\n}\nfunction parseSettings(doc, options) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n  var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n  var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n  var showFoot;\n  if (options.showFoot === true) {\n    showFoot = 'everyPage';\n  } else if (options.showFoot === false) {\n    showFoot = 'never';\n  } else {\n    showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n  }\n  var showHead;\n  if (options.showHead === true) {\n    showHead = 'everyPage';\n  } else if (options.showHead === false) {\n    showHead = 'never';\n  } else {\n    showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n  }\n  var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n  var theme = options.theme || (useCss ? 'plain' : 'striped');\n  var horizontalPageBreak = !!options.horizontalPageBreak;\n  var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n  return {\n    includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n    useCss: useCss,\n    theme: theme,\n    startY: startY,\n    margin: margin,\n    pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n    rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n    tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n    showHead: showHead,\n    showFoot: showFoot,\n    tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n    tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n    horizontalPageBreak: horizontalPageBreak,\n    horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n    horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows'\n  };\n}\nfunction getStartY(doc, userStartY) {\n  var previous = doc.getLastAutoTable();\n  var sf = doc.scaleFactor();\n  var currentPage = doc.pageNumber();\n  var isSamePageAsPreviousTable = false;\n  if (previous && previous.startPageNumber) {\n    var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n    isSamePageAsPreviousTable = endingPage === currentPage;\n  }\n  if (typeof userStartY === 'number') {\n    return userStartY;\n  } else if (userStartY == null || userStartY === false) {\n    if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n      // Some users had issues with overlapping tables when they used multiple\n      // tables without setting startY so setting it here to a sensible default.\n      return previous.finalY + 20 / sf;\n    }\n  }\n  return null;\n}\nfunction parseContent$1(doc, options, window) {\n  var head = options.head || [];\n  var body = options.body || [];\n  var foot = options.foot || [];\n  if (options.html) {\n    var hidden = options.includeHiddenHtml;\n    if (window) {\n      var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n      head = htmlContent.head || head;\n      body = htmlContent.body || head;\n      foot = htmlContent.foot || head;\n    } else {\n      console.error('Cannot parse html in non browser environment');\n    }\n  }\n  var columns = options.columns || parseColumns(head, body, foot);\n  return {\n    columns: columns,\n    head: head,\n    body: body,\n    foot: foot\n  };\n}\nfunction parseColumns(head, body, foot) {\n  var firstRow = head[0] || body[0] || foot[0] || [];\n  var result = [];\n  Object.keys(firstRow).filter(function (key) {\n    return key !== '_element';\n  }).forEach(function (key) {\n    var colSpan = 1;\n    var input;\n    if (Array.isArray(firstRow)) {\n      input = firstRow[parseInt(key)];\n    } else {\n      input = firstRow[key];\n    }\n    if (typeof input === 'object' && !Array.isArray(input)) {\n      colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n    }\n    for (var i = 0; i < colSpan; i++) {\n      var id = void 0;\n      if (Array.isArray(firstRow)) {\n        id = result.length;\n      } else {\n        id = key + (i > 0 ? \"_\".concat(i) : '');\n      }\n      var rowResult = {\n        dataKey: id\n      };\n      result.push(rowResult);\n    }\n  });\n  return result;\n}\nvar HookData = /** @class */function () {\n  function HookData(doc, table, cursor) {\n    this.table = table;\n    this.pageNumber = table.pageNumber;\n    this.settings = table.settings;\n    this.cursor = cursor;\n    this.doc = doc.getDocument();\n  }\n  return HookData;\n}();\nvar CellHookData = /** @class */function (_super) {\n  __extends(CellHookData, _super);\n  function CellHookData(doc, table, cell, row, column, cursor) {\n    var _this = _super.call(this, doc, table, cursor) || this;\n    _this.cell = cell;\n    _this.row = row;\n    _this.column = column;\n    _this.section = row.section;\n    return _this;\n  }\n  return CellHookData;\n}(HookData);\nvar Table = /** @class */function () {\n  function Table(input, content) {\n    this.pageNumber = 1;\n    this.id = input.id;\n    this.settings = input.settings;\n    this.styles = input.styles;\n    this.hooks = input.hooks;\n    this.columns = content.columns;\n    this.head = content.head;\n    this.body = content.body;\n    this.foot = content.foot;\n  }\n  Table.prototype.getHeadHeight = function (columns) {\n    return this.head.reduce(function (acc, row) {\n      return acc + row.getMaxCellHeight(columns);\n    }, 0);\n  };\n  Table.prototype.getFootHeight = function (columns) {\n    return this.foot.reduce(function (acc, row) {\n      return acc + row.getMaxCellHeight(columns);\n    }, 0);\n  };\n  Table.prototype.allRows = function () {\n    return this.head.concat(this.body).concat(this.foot);\n  };\n  Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n    for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n      var handler = handlers_1[_i];\n      var data = new CellHookData(doc, this, cell, row, column, cursor);\n      var result = handler(data) === false;\n      // Make sure text is always string[] since user can assign string\n      cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n      if (result) {\n        return false;\n      }\n    }\n    return true;\n  };\n  Table.prototype.callEndPageHooks = function (doc, cursor) {\n    doc.applyStyles(doc.userStyles);\n    for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n      var handler = _a[_i];\n      handler(new HookData(doc, this, cursor));\n    }\n  };\n  Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n    for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n      var handler = _a[_i];\n      handler(new HookData(doc, this, cursor));\n    }\n  };\n  Table.prototype.getWidth = function (pageWidth) {\n    if (typeof this.settings.tableWidth === 'number') {\n      return this.settings.tableWidth;\n    } else if (this.settings.tableWidth === 'wrap') {\n      var wrappedWidth = this.columns.reduce(function (total, col) {\n        return total + col.wrappedWidth;\n      }, 0);\n      return wrappedWidth;\n    } else {\n      var margin = this.settings.margin;\n      return pageWidth - margin.left - margin.right;\n    }\n  };\n  return Table;\n}();\nvar Row = /** @class */function () {\n  function Row(raw, index, section, cells, spansMultiplePages) {\n    if (spansMultiplePages === void 0) {\n      spansMultiplePages = false;\n    }\n    this.height = 0;\n    this.raw = raw;\n    if (raw instanceof HtmlRowInput) {\n      this.raw = raw._element;\n      this.element = raw._element;\n    }\n    this.index = index;\n    this.section = section;\n    this.cells = cells;\n    this.spansMultiplePages = spansMultiplePages;\n  }\n  Row.prototype.getMaxCellHeight = function (columns) {\n    var _this = this;\n    return columns.reduce(function (acc, column) {\n      var _a;\n      return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0);\n    }, 0);\n  };\n  Row.prototype.hasRowSpan = function (columns) {\n    var _this = this;\n    return columns.filter(function (column) {\n      var cell = _this.cells[column.index];\n      if (!cell) return false;\n      return cell.rowSpan > 1;\n    }).length > 0;\n  };\n  Row.prototype.canEntireRowFit = function (height, columns) {\n    return this.getMaxCellHeight(columns) <= height;\n  };\n  Row.prototype.getMinimumRowHeight = function (columns, doc) {\n    var _this = this;\n    return columns.reduce(function (acc, column) {\n      var cell = _this.cells[column.index];\n      if (!cell) return 0;\n      var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n      var vPadding = cell.padding('vertical');\n      var oneRowHeight = vPadding + lineHeight;\n      return oneRowHeight > acc ? oneRowHeight : acc;\n    }, 0);\n  };\n  return Row;\n}();\nvar Cell = /** @class */function () {\n  function Cell(raw, styles, section) {\n    var _a;\n    this.contentHeight = 0;\n    this.contentWidth = 0;\n    this.wrappedWidth = 0;\n    this.minReadableWidth = 0;\n    this.minWidth = 0;\n    this.width = 0;\n    this.height = 0;\n    this.x = 0;\n    this.y = 0;\n    this.styles = styles;\n    this.section = section;\n    this.raw = raw;\n    var content = raw;\n    if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n      this.rowSpan = raw.rowSpan || 1;\n      this.colSpan = raw.colSpan || 1;\n      content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n      if (raw._element) {\n        this.raw = raw._element;\n      }\n    } else {\n      this.rowSpan = 1;\n      this.colSpan = 1;\n    }\n    // Stringify 0 and false, but not undefined or null\n    var text = content != null ? '' + content : '';\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    this.text = text.split(splitRegex);\n  }\n  Cell.prototype.getTextPos = function () {\n    var y;\n    if (this.styles.valign === 'top') {\n      y = this.y + this.padding('top');\n    } else if (this.styles.valign === 'bottom') {\n      y = this.y + this.height - this.padding('bottom');\n    } else {\n      var netHeight = this.height - this.padding('vertical');\n      y = this.y + netHeight / 2 + this.padding('top');\n    }\n    var x;\n    if (this.styles.halign === 'right') {\n      x = this.x + this.width - this.padding('right');\n    } else if (this.styles.halign === 'center') {\n      var netWidth = this.width - this.padding('horizontal');\n      x = this.x + netWidth / 2 + this.padding('left');\n    } else {\n      x = this.x + this.padding('left');\n    }\n    return {\n      x: x,\n      y: y\n    };\n  };\n  // TODO (v4): replace parameters with only (lineHeight)\n  Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n    if (lineHeightFactor === void 0) {\n      lineHeightFactor = 1.15;\n    }\n    var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n    var lineHeight = this.styles.fontSize / scaleFactor * lineHeightFactor;\n    var height = lineCount * lineHeight + this.padding('vertical');\n    return Math.max(height, this.styles.minCellHeight);\n  };\n  Cell.prototype.padding = function (name) {\n    var padding = parseSpacing(this.styles.cellPadding, 0);\n    if (name === 'vertical') {\n      return padding.top + padding.bottom;\n    } else if (name === 'horizontal') {\n      return padding.left + padding.right;\n    } else {\n      return padding[name];\n    }\n  };\n  return Cell;\n}();\nvar Column = /** @class */function () {\n  function Column(dataKey, raw, index) {\n    this.wrappedWidth = 0;\n    this.minReadableWidth = 0;\n    this.minWidth = 0;\n    this.width = 0;\n    this.dataKey = dataKey;\n    this.raw = raw;\n    this.index = index;\n  }\n  Column.prototype.getMaxCustomCellWidth = function (table) {\n    var max = 0;\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n      var row = _a[_i];\n      var cell = row.cells[this.index];\n      if (cell && typeof cell.styles.cellWidth === 'number') {\n        max = Math.max(max, cell.styles.cellWidth);\n      }\n    }\n    return max;\n  };\n  return Column;\n}();\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n  calculate(doc, table);\n  var resizableColumns = [];\n  var initialTableWidth = 0;\n  table.columns.forEach(function (column) {\n    var customWidth = column.getMaxCustomCellWidth(table);\n    if (customWidth) {\n      // final column width\n      column.width = customWidth;\n    } else {\n      // initial column width (will be resized)\n      column.width = column.wrappedWidth;\n      resizableColumns.push(column);\n    }\n    initialTableWidth += column.width;\n  });\n  // width difference that needs to be distributed\n  var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n  // first resize attempt: with respect to minReadableWidth and minWidth\n  if (resizeWidth) {\n    resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n      return Math.max(column.minReadableWidth, column.minWidth);\n    });\n  }\n  // second resize attempt: ignore minReadableWidth but respect minWidth\n  if (resizeWidth) {\n    resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n      return column.minWidth;\n    });\n  }\n  resizeWidth = Math.abs(resizeWidth);\n  if (!table.settings.horizontalPageBreak && resizeWidth > 0.1 / doc.scaleFactor()) {\n    // Table can't get smaller due to custom-width or minWidth restrictions\n    // We can't really do much here. Up to user to for example\n    // reduce font size, increase page size or remove custom cell widths\n    // to allow more columns to be reduced in size\n    resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n    console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n  }\n  applyColSpans(table);\n  fitContent(table, doc);\n  applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n  var sf = doc.scaleFactor();\n  var horizontalPageBreak = table.settings.horizontalPageBreak;\n  var availablePageWidth = getPageAvailableWidth(doc, table);\n  table.allRows().forEach(function (row) {\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var cell = row.cells[column.index];\n      if (!cell) continue;\n      var hooks = table.hooks.didParseCell;\n      table.callCellHooks(doc, hooks, cell, row, column, null);\n      var padding = cell.padding('horizontal');\n      cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n      // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n      // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n      // them in the split process to ensure correct word separation and width\n      // calculation.\n      var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n      cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n      if (typeof cell.styles.cellWidth === 'number') {\n        cell.minWidth = cell.styles.cellWidth;\n        cell.wrappedWidth = cell.styles.cellWidth;\n      } else if (cell.styles.cellWidth === 'wrap' || horizontalPageBreak === true) {\n        // cell width should not be more than available page width\n        if (cell.contentWidth > availablePageWidth) {\n          cell.minWidth = availablePageWidth;\n          cell.wrappedWidth = availablePageWidth;\n        } else {\n          cell.minWidth = cell.contentWidth;\n          cell.wrappedWidth = cell.contentWidth;\n        }\n      } else {\n        // auto\n        var defaultMinWidth = 10 / sf;\n        cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n        cell.wrappedWidth = cell.contentWidth;\n        if (cell.minWidth > cell.wrappedWidth) {\n          cell.wrappedWidth = cell.minWidth;\n        }\n      }\n    }\n  });\n  table.allRows().forEach(function (row) {\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var cell = row.cells[column.index];\n      // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n      // Could probably be improved upon however.\n      if (cell && cell.colSpan === 1) {\n        column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n        column.minWidth = Math.max(column.minWidth, cell.minWidth);\n        column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n      } else {\n        // Respect cellWidth set in columnStyles even if there is no cells for this column\n        // or if the column only have colspan cells. Since the width of colspan cells\n        // does not affect the width of columns, setting columnStyles cellWidth enables the\n        // user to at least do it manually.\n        // Note that this is not perfect for now since for example row and table styles are\n        // not accounted for\n        var columnStyles = table.styles.columnStyles[column.dataKey] || table.styles.columnStyles[column.index] || {};\n        var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n        if (cellWidth && typeof cellWidth === 'number') {\n          column.minWidth = cellWidth;\n          column.wrappedWidth = cellWidth;\n        }\n      }\n      if (cell) {\n        // Make sure all columns get at least min width even though width calculations are not based on them\n        if (cell.colSpan > 1 && !column.minWidth) {\n          column.minWidth = cell.minWidth;\n        }\n        if (cell.colSpan > 1 && !column.wrappedWidth) {\n          column.wrappedWidth = cell.minWidth;\n        }\n      }\n    }\n  });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n  var initialResizeWidth = resizeWidth;\n  var sumWrappedWidth = columns.reduce(function (acc, column) {\n    return acc + column.wrappedWidth;\n  }, 0);\n  for (var i = 0; i < columns.length; i++) {\n    var column = columns[i];\n    var ratio = column.wrappedWidth / sumWrappedWidth;\n    var suggestedChange = initialResizeWidth * ratio;\n    var suggestedWidth = column.width + suggestedChange;\n    var minWidth = getMinWidth(column);\n    var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n    resizeWidth -= newWidth - column.width;\n    column.width = newWidth;\n  }\n  resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n  // Run the resizer again if there's remaining width needs\n  // to be distributed and there're columns that can be resized\n  if (resizeWidth) {\n    var resizableColumns = columns.filter(function (column) {\n      return resizeWidth < 0 ? column.width > getMinWidth(column) // check if column can shrink\n      : true; // check if column can grow\n    });\n    if (resizableColumns.length) {\n      resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n    }\n  }\n  return resizeWidth;\n}\nfunction applyRowSpans(table) {\n  var rowSpanCells = {};\n  var colRowSpansLeft = 1;\n  var all = table.allRows();\n  for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n    var row = all[rowIndex];\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var data = rowSpanCells[column.index];\n      if (colRowSpansLeft > 1) {\n        colRowSpansLeft--;\n        delete row.cells[column.index];\n      } else if (data) {\n        data.cell.height += row.height;\n        colRowSpansLeft = data.cell.colSpan;\n        delete row.cells[column.index];\n        data.left--;\n        if (data.left <= 1) {\n          delete rowSpanCells[column.index];\n        }\n      } else {\n        var cell = row.cells[column.index];\n        if (!cell) {\n          continue;\n        }\n        cell.height = row.height;\n        if (cell.rowSpan > 1) {\n          var remaining = all.length - rowIndex;\n          var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n          rowSpanCells[column.index] = {\n            cell: cell,\n            left: left,\n            row: row\n          };\n        }\n      }\n    }\n  }\n}\nfunction applyColSpans(table) {\n  var all = table.allRows();\n  for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n    var row = all[rowIndex];\n    var colSpanCell = null;\n    var combinedColSpanWidth = 0;\n    var colSpansLeft = 0;\n    for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n      var column = table.columns[columnIndex];\n      // Width and colspan\n      colSpansLeft -= 1;\n      if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n        combinedColSpanWidth += column.width;\n        delete row.cells[column.index];\n      } else if (colSpanCell) {\n        var cell = colSpanCell;\n        delete row.cells[column.index];\n        colSpanCell = null;\n        cell.width = column.width + combinedColSpanWidth;\n      } else {\n        var cell = row.cells[column.index];\n        if (!cell) continue;\n        colSpansLeft = cell.colSpan;\n        combinedColSpanWidth = 0;\n        if (cell.colSpan > 1) {\n          colSpanCell = cell;\n          combinedColSpanWidth += column.width;\n          continue;\n        }\n        cell.width = column.width + combinedColSpanWidth;\n      }\n    }\n  }\n}\nfunction fitContent(table, doc) {\n  var rowSpanHeight = {\n    count: 0,\n    height: 0\n  };\n  for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n    var row = _a[_i];\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n      var column = _c[_b];\n      var cell = row.cells[column.index];\n      if (!cell) continue;\n      doc.applyStyles(cell.styles, true);\n      var textSpace = cell.width - cell.padding('horizontal');\n      if (cell.styles.overflow === 'linebreak') {\n        // Add one pt to textSpace to fix rounding error\n        cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), {\n          fontSize: cell.styles.fontSize\n        });\n      } else if (cell.styles.overflow === 'ellipsize') {\n        cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n      } else if (cell.styles.overflow === 'hidden') {\n        cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n      } else if (typeof cell.styles.overflow === 'function') {\n        var result = cell.styles.overflow(cell.text, textSpace);\n        if (typeof result === 'string') {\n          cell.text = [result];\n        } else {\n          cell.text = result;\n        }\n      }\n      cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n      var realContentHeight = cell.contentHeight / cell.rowSpan;\n      if (cell.rowSpan > 1 && rowSpanHeight.count * rowSpanHeight.height < realContentHeight * cell.rowSpan) {\n        rowSpanHeight = {\n          height: realContentHeight,\n          count: cell.rowSpan\n        };\n      } else if (rowSpanHeight && rowSpanHeight.count > 0) {\n        if (rowSpanHeight.height > realContentHeight) {\n          realContentHeight = rowSpanHeight.height;\n        }\n      }\n      if (realContentHeight > row.height) {\n        row.height = realContentHeight;\n      }\n    }\n    rowSpanHeight.count--;\n  }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n  return text.map(function (str) {\n    return ellipsizeStr(str, width, styles, doc, overflow);\n  });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n  var precision = 10000 * doc.scaleFactor();\n  width = Math.ceil(width * precision) / precision;\n  if (width >= getStringWidth(text, styles, doc)) {\n    return text;\n  }\n  while (width < getStringWidth(text + overflow, styles, doc)) {\n    if (text.length <= 1) {\n      break;\n    }\n    text = text.substring(0, text.length - 1);\n  }\n  return text.trim() + overflow;\n}\nfunction createTable(jsPDFDoc, input) {\n  var doc = new DocHandler(jsPDFDoc);\n  var content = parseContent(input, doc.scaleFactor());\n  var table = new Table(input, content);\n  calculateWidths(doc, table);\n  doc.applyStyles(doc.userStyles);\n  return table;\n}\nfunction parseContent(input, sf) {\n  var content = input.content;\n  var columns = createColumns(content.columns);\n  // If no head or foot is set, try generating it with content from columns\n  if (content.head.length === 0) {\n    var sectionRow = generateSectionRow(columns, 'head');\n    if (sectionRow) content.head.push(sectionRow);\n  }\n  if (content.foot.length === 0) {\n    var sectionRow = generateSectionRow(columns, 'foot');\n    if (sectionRow) content.foot.push(sectionRow);\n  }\n  var theme = input.settings.theme;\n  var styles = input.styles;\n  return {\n    columns: columns,\n    head: parseSection('head', content.head, columns, styles, theme, sf),\n    body: parseSection('body', content.body, columns, styles, theme, sf),\n    foot: parseSection('foot', content.foot, columns, styles, theme, sf)\n  };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n  var rowSpansLeftForColumn = {};\n  var result = sectionRows.map(function (rawRow, rowIndex) {\n    var skippedRowForRowSpans = 0;\n    var cells = {};\n    var colSpansAdded = 0;\n    var columnSpansLeft = 0;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n      var column = columns_1[_i];\n      if (rowSpansLeftForColumn[column.index] == null || rowSpansLeftForColumn[column.index].left === 0) {\n        if (columnSpansLeft === 0) {\n          var rawCell = void 0;\n          if (Array.isArray(rawRow)) {\n            rawCell = rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n          } else {\n            rawCell = rawRow[column.dataKey];\n          }\n          var cellInputStyles = {};\n          if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n            cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n          }\n          var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n          var cell = new Cell(rawCell, styles, sectionName);\n          // dataKey is not used internally no more but keep for\n          // backwards compat in hooks\n          cells[column.dataKey] = cell;\n          cells[column.index] = cell;\n          columnSpansLeft = cell.colSpan - 1;\n          rowSpansLeftForColumn[column.index] = {\n            left: cell.rowSpan - 1,\n            times: columnSpansLeft\n          };\n        } else {\n          columnSpansLeft--;\n          colSpansAdded++;\n        }\n      } else {\n        rowSpansLeftForColumn[column.index].left--;\n        columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n        skippedRowForRowSpans++;\n      }\n    }\n    return new Row(rawRow, rowIndex, sectionName, cells);\n  });\n  return result;\n}\nfunction generateSectionRow(columns, section) {\n  var sectionRow = {};\n  columns.forEach(function (col) {\n    if (col.raw != null) {\n      var title = getSectionTitle(section, col.raw);\n      if (title != null) sectionRow[col.dataKey] = title;\n    }\n  });\n  return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n  if (section === 'head') {\n    if (typeof column === 'object') {\n      return column.header || null;\n    } else if (typeof column === 'string' || typeof column === 'number') {\n      return column;\n    }\n  } else if (section === 'foot' && typeof column === 'object') {\n    return column.footer;\n  }\n  return null;\n}\nfunction createColumns(columns) {\n  return columns.map(function (input, index) {\n    var _a;\n    var key;\n    if (typeof input === 'object') {\n      key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n    } else {\n      key = index;\n    }\n    return new Column(key, input, index);\n  });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n  var theme = getTheme(themeName);\n  var sectionStyles;\n  if (sectionName === 'head') {\n    sectionStyles = styles.headStyles;\n  } else if (sectionName === 'body') {\n    sectionStyles = styles.bodyStyles;\n  } else if (sectionName === 'foot') {\n    sectionStyles = styles.footStyles;\n  }\n  var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n  var columnStyles = styles.columnStyles[column.dataKey] || styles.columnStyles[column.index] || {};\n  var colStyles = sectionName === 'body' ? columnStyles : {};\n  var rowStyles = sectionName === 'body' && rowIndex % 2 === 0 ? assign({}, theme.alternateRow, styles.alternateRowStyles) : {};\n  var defaultStyle = defaultStyles(scaleFactor);\n  var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n  return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n  var _a;\n  if (config === void 0) {\n    config = {};\n  }\n  // Get page width\n  var remainingWidth = getPageAvailableWidth(doc, table);\n  // Get column data key to repeat\n  var repeatColumnsMap = new Map();\n  var colIndexes = [];\n  var columns = [];\n  var horizontalPageBreakRepeat = [];\n  if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n    horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n    // It can be a single value of type string or number (even number: 0)\n  } else if (typeof table.settings.horizontalPageBreakRepeat === 'string' || typeof table.settings.horizontalPageBreakRepeat === 'number') {\n    horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n  }\n  // Code to repeat the given column in split pages\n  horizontalPageBreakRepeat.forEach(function (field) {\n    var col = table.columns.find(function (item) {\n      return item.dataKey === field || item.index === field;\n    });\n    if (col && !repeatColumnsMap.has(col.index)) {\n      repeatColumnsMap.set(col.index, true);\n      colIndexes.push(col.index);\n      columns.push(table.columns[col.index]);\n      remainingWidth -= col.wrappedWidth;\n    }\n  });\n  var first = true;\n  var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n  while (i < table.columns.length) {\n    // Prevent duplicates\n    if (repeatColumnsMap.has(i)) {\n      i++;\n      continue;\n    }\n    var colWidth = table.columns[i].wrappedWidth;\n    // Take at least one column even if it doesn't fit\n    if (first || remainingWidth >= colWidth) {\n      first = false;\n      colIndexes.push(i);\n      columns.push(table.columns[i]);\n      remainingWidth -= colWidth;\n    } else {\n      break;\n    }\n    i++;\n  }\n  return {\n    colIndexes: colIndexes,\n    columns: columns,\n    lastIndex: i - 1\n  };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n  var allResults = [];\n  for (var i = 0; i < table.columns.length; i++) {\n    var result = getColumnsCanFitInPage(doc, table, {\n      start: i\n    });\n    if (result.columns.length) {\n      allResults.push(result);\n      i = result.lastIndex;\n    }\n  }\n  return allResults;\n}\nfunction drawTable(jsPDFDoc, table) {\n  var settings = table.settings;\n  var startY = settings.startY;\n  var margin = settings.margin;\n  var cursor = {\n    x: margin.left,\n    y: startY\n  };\n  var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n  var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n  if (settings.pageBreak === 'avoid') {\n    var rows = table.body;\n    var tableHeight = rows.reduce(function (acc, row) {\n      return acc + row.height;\n    }, 0);\n    minTableBottomPos += tableHeight;\n  }\n  var doc = new DocHandler(jsPDFDoc);\n  if (settings.pageBreak === 'always' || settings.startY != null && minTableBottomPos > doc.pageSize().height) {\n    nextPage(doc);\n    cursor.y = margin.top;\n  }\n  table.callWillDrawPageHooks(doc, cursor);\n  var startPos = assign({}, cursor);\n  table.startPageNumber = doc.pageNumber();\n  if (settings.horizontalPageBreak) {\n    // managed flow for split columns\n    printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n  } else {\n    // normal flow\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n      table.head.forEach(function (row) {\n        return printRow(doc, table, row, cursor, table.columns);\n      });\n    }\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n      var isLastRow = index === table.body.length - 1;\n      printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n    });\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n      table.foot.forEach(function (row) {\n        return printRow(doc, table, row, cursor, table.columns);\n      });\n    }\n  }\n  addTableBorder(doc, table, startPos, cursor);\n  table.callEndPageHooks(doc, cursor);\n  table.finalY = cursor.y;\n  jsPDFDoc.lastAutoTable = table;\n  doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n  // calculate width of columns and render only those which can fit into page\n  var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n  var settings = table.settings;\n  if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n    allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n      doc.applyStyles(doc.userStyles);\n      // add page to print next columns in new page\n      if (index > 0) {\n        // When adding a page here, make sure not to print the footers\n        // because they were already printed before on this same loop\n        addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n      } else {\n        // print head for selected columns\n        printHead(doc, table, cursor, colsAndIndexes.columns);\n      }\n      // print body & footer for selected columns\n      printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n      printFoot(doc, table, cursor, colsAndIndexes.columns);\n    });\n  } else {\n    var lastRowIndexOfLastPage_1 = -1;\n    var firstColumnsToFitResult = allColumnsCanFitResult[0];\n    var _loop_1 = function () {\n      // Print the first columns, taking note of the last row printed\n      var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n      if (firstColumnsToFitResult) {\n        doc.applyStyles(doc.userStyles);\n        var firstColumnsToFit = firstColumnsToFitResult.columns;\n        if (lastRowIndexOfLastPage_1 >= 0) {\n          // When adding a page here, make sure not to print the footers\n          // because they were already printed before on this same loop\n          addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n        } else {\n          printHead(doc, table, cursor, firstColumnsToFit);\n        }\n        lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n        printFoot(doc, table, cursor, firstColumnsToFit);\n      }\n      // Check how many rows were printed, so that the next columns would not print more rows than that\n      var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n      // Print the next columns, never exceding maxNumberOfRows\n      allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n        doc.applyStyles(doc.userStyles);\n        // When adding a page here, make sure not to print the footers\n        // because they were already printed before on this same loop\n        addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n        printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n        printFoot(doc, table, cursor, colsAndIndexes.columns);\n      });\n      lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n    };\n    while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n      _loop_1();\n    }\n  }\n}\nfunction printHead(doc, table, cursor, columns) {\n  var settings = table.settings;\n  doc.applyStyles(doc.userStyles);\n  if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n    table.head.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n  doc.applyStyles(doc.userStyles);\n  table.body.forEach(function (row, index) {\n    var isLastRow = index === table.body.length - 1;\n    printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n  });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n  doc.applyStyles(doc.userStyles);\n  maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n  var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n  var lastPrintedRowIndex = -1;\n  table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n    var isLastRow = startRowIndex + index === table.body.length - 1;\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n      printRow(doc, table, row, cursor, columns);\n      lastPrintedRowIndex = startRowIndex + index;\n    }\n  });\n  return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n  var settings = table.settings;\n  doc.applyStyles(doc.userStyles);\n  if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n    table.foot.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n  var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n  var vPadding = cell.padding('vertical');\n  var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n  return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n  var cells = {};\n  row.spansMultiplePages = true;\n  row.height = 0;\n  var rowHeight = 0;\n  for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n    var column = _a[_i];\n    var cell = row.cells[column.index];\n    if (!cell) continue;\n    if (!Array.isArray(cell.text)) {\n      cell.text = [cell.text];\n    }\n    var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n    remainderCell = assign(remainderCell, cell);\n    remainderCell.text = [];\n    var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n    if (cell.text.length > remainingLineCount) {\n      remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n    }\n    var scaleFactor = doc.scaleFactor();\n    var lineHeightFactor = doc.getLineHeightFactor();\n    cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n    if (cell.contentHeight >= remainingPageSpace) {\n      cell.contentHeight = remainingPageSpace;\n      remainderCell.styles.minCellHeight -= remainingPageSpace;\n    }\n    if (cell.contentHeight > row.height) {\n      row.height = cell.contentHeight;\n    }\n    remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n    if (remainderCell.contentHeight > rowHeight) {\n      rowHeight = remainderCell.contentHeight;\n    }\n    cells[column.index] = remainderCell;\n  }\n  var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n  remainderRow.height = rowHeight;\n  for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n    var column = _c[_b];\n    var remainderCell = remainderRow.cells[column.index];\n    if (remainderCell) {\n      remainderCell.height = remainderRow.height;\n    }\n    var cell = row.cells[column.index];\n    if (cell) {\n      cell.height = row.height;\n    }\n  }\n  return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n  var pageHeight = doc.pageSize().height;\n  var margin = table.settings.margin;\n  var marginHeight = margin.top + margin.bottom;\n  var maxRowHeight = pageHeight - marginHeight;\n  if (row.section === 'body') {\n    // Should also take into account that head and foot is not\n    // on every page with some settings\n    maxRowHeight -= table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n  }\n  var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n  var minRowFits = minRowHeight < remainingPageSpace;\n  if (minRowHeight > maxRowHeight) {\n    console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n    return true;\n  }\n  if (!minRowFits) {\n    return false;\n  }\n  var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n  var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n  if (rowHigherThanPage) {\n    if (rowHasRowSpanCell) {\n      console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n    }\n    return true;\n  }\n  if (rowHasRowSpanCell) {\n    // Currently a new page is required whenever a rowspan row don't fit a page.\n    return false;\n  }\n  if (table.settings.rowPageBreak === 'avoid') {\n    return false;\n  }\n  // In all other cases print the row on current page\n  return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n  var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n  if (row.canEntireRowFit(remainingSpace, columns)) {\n    // The row fits in the current page\n    printRow(doc, table, row, cursor, columns);\n  } else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n    // The row gets split in two here, each piece in one page\n    var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n    printRow(doc, table, row, cursor, columns);\n    addPage(doc, table, startPos, cursor, columns);\n    printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n  } else {\n    // The row get printed entirelly on the next page\n    addPage(doc, table, startPos, cursor, columns);\n    printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n  }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n  cursor.x = table.settings.margin.left;\n  for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n    var column = columns_1[_i];\n    var cell = row.cells[column.index];\n    if (!cell) {\n      cursor.x += column.width;\n      continue;\n    }\n    doc.applyStyles(cell.styles);\n    cell.x = cursor.x;\n    cell.y = cursor.y;\n    var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n    if (result === false) {\n      cursor.x += column.width;\n      continue;\n    }\n    drawCellRect(doc, cell, cursor);\n    var textPos = cell.getTextPos();\n    autoTableText(cell.text, textPos.x, textPos.y, {\n      halign: cell.styles.halign,\n      valign: cell.styles.valign,\n      maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right'))\n    }, doc.getDocument());\n    table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n    cursor.x += column.width;\n  }\n  cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n  var cellStyles = cell.styles;\n  // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n  // TODO (v4): better solution?\n  doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n  if (typeof cellStyles.lineWidth === 'number') {\n    // Draw cell background with normal borders\n    var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n    if (fillStyle) {\n      doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n    }\n  } else if (typeof cellStyles.lineWidth === 'object') {\n    // Draw cell background\n    if (cellStyles.fillColor) {\n      doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n    }\n    // Draw cell individual borders\n    drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n  }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n  var x1, y1, x2, y2;\n  if (lineWidth.top) {\n    x1 = cursor.x;\n    y1 = cursor.y;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y;\n    if (lineWidth.right) {\n      x2 += 0.5 * lineWidth.right;\n    }\n    if (lineWidth.left) {\n      x1 -= 0.5 * lineWidth.left;\n    }\n    drawLine(lineWidth.top, x1, y1, x2, y2);\n  }\n  if (lineWidth.bottom) {\n    x1 = cursor.x;\n    y1 = cursor.y + cell.height;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.right) {\n      x2 += 0.5 * lineWidth.right;\n    }\n    if (lineWidth.left) {\n      x1 -= 0.5 * lineWidth.left;\n    }\n    drawLine(lineWidth.bottom, x1, y1, x2, y2);\n  }\n  if (lineWidth.left) {\n    x1 = cursor.x;\n    y1 = cursor.y;\n    x2 = cursor.x;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.top) {\n      y1 -= 0.5 * lineWidth.top;\n    }\n    if (lineWidth.bottom) {\n      y2 += 0.5 * lineWidth.bottom;\n    }\n    drawLine(lineWidth.left, x1, y1, x2, y2);\n  }\n  if (lineWidth.right) {\n    x1 = cursor.x + cell.width;\n    y1 = cursor.y;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.top) {\n      y1 -= 0.5 * lineWidth.top;\n    }\n    if (lineWidth.bottom) {\n      y2 += 0.5 * lineWidth.bottom;\n    }\n    drawLine(lineWidth.right, x1, y1, x2, y2);\n  }\n  function drawLine(width, x1, y1, x2, y2) {\n    doc.getDocument().setLineWidth(width);\n    doc.getDocument().line(x1, y1, x2, y2, 'S');\n  }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n  var bottomContentHeight = table.settings.margin.bottom;\n  var showFoot = table.settings.showFoot;\n  if (showFoot === 'everyPage' || showFoot === 'lastPage' && isLastRow) {\n    bottomContentHeight += table.getFootHeight(table.columns);\n  }\n  return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n  if (columns === void 0) {\n    columns = [];\n  }\n  if (suppressFooter === void 0) {\n    suppressFooter = false;\n  }\n  doc.applyStyles(doc.userStyles);\n  if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n    table.foot.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n  // Add user content just before adding new page ensure it will\n  // be drawn above other things on the page\n  table.callEndPageHooks(doc, cursor);\n  var margin = table.settings.margin;\n  addTableBorder(doc, table, startPos, cursor);\n  nextPage(doc);\n  table.pageNumber++;\n  cursor.x = margin.left;\n  cursor.y = margin.top;\n  startPos.y = margin.top;\n  // call didAddPage hooks before any content is added to the page\n  table.callWillDrawPageHooks(doc, cursor);\n  if (table.settings.showHead === 'everyPage') {\n    table.head.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n    doc.applyStyles(doc.userStyles);\n  }\n}\nfunction nextPage(doc) {\n  var current = doc.pageNumber();\n  doc.setPage(current + 1);\n  var newCurrent = doc.pageNumber();\n  if (newCurrent === current) {\n    doc.addPage();\n    return true;\n  }\n  return false;\n}\nfunction applyPlugin(jsPDF) {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  jsPDF.API.autoTable = function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var options = args[0];\n    var input = parseInput(this, options);\n    var table = createTable(this, input);\n    drawTable(this, table);\n    return this;\n  };\n  // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n  jsPDF.API.lastAutoTable = false;\n  jsPDF.API.autoTableText = function (text, x, y, styles) {\n    autoTableText(text, x, y, styles, this);\n  };\n  jsPDF.API.autoTableSetDefaults = function (defaults) {\n    DocHandler.setDefaults(defaults, this);\n    return this;\n  };\n  jsPDF.autoTableSetDefaults = function (defaults, doc) {\n    DocHandler.setDefaults(defaults, doc);\n  };\n  jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n    var _a;\n    if (includeHiddenElements === void 0) {\n      includeHiddenElements = false;\n    }\n    if (typeof window === 'undefined') {\n      console.error('Cannot run autoTableHtmlToJson in non browser environment');\n      return null;\n    }\n    var doc = new DocHandler(this);\n    var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false),\n      head = _b.head,\n      body = _b.body;\n    var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) {\n      return c.content;\n    })) || [];\n    return {\n      columns: columns,\n      rows: body,\n      data: body\n    };\n  };\n}\nvar _a;\nfunction autoTable(d, options) {\n  var input = parseInput(d, options);\n  var table = createTable(d, input);\n  drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n  var input = parseInput(d, options);\n  return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n  drawTable(d, table);\n}\ntry {\n  if (typeof window !== 'undefined' && window) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    var anyWindow = window;\n    var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\n    if (jsPDF) {\n      applyPlugin(jsPDF);\n    }\n  }\n} catch (error) {\n  console.error('Could not apply autoTable plugin', error);\n}\nexport { Cell, CellHookData, Column, HookData, Row, Table, __createTable, __drawTable, applyPlugin, autoTable, autoTable as default };", "map": {"version": 3, "names": ["autoTableText", "text", "x", "y", "styles", "doc", "PHYSICAL_LINE_HEIGHT", "k", "internal", "scaleFactor", "fontSize", "getFontSize", "lineHeightFactor", "getLineHeightFactor", "lineHeight", "splitRegex", "splitText", "lineCount", "valign", "halign", "split", "length", "alignSize", "iLine", "getStringUnitWidth", "max<PERSON><PERSON><PERSON>", "align", "globalDefaults", "<PERSON><PERSON><PERSON><PERSON>", "jsPDFDocument", "userStyles", "textColor", "getTextColor", "fontStyle", "getFont", "font", "fontName", "lineWidth", "getLineWidth", "lineColor", "getDrawColor", "setDefaults", "defaults", "__autoTableDocumentDefaults", "unifyColor", "c", "Array", "isArray", "prototype", "applyStyles", "fontOnly", "_a", "_b", "_c", "setFontStyle", "_d", "availableFontStyles", "getFontList", "indexOf", "setFont", "setFontSize", "color", "fillColor", "setFillColor", "apply", "setTextColor", "setDrawColor", "setLineWidth", "splitTextToSize", "size", "opts", "rect", "width", "height", "fillStyle", "getLastAutoTable", "lastAutoTable", "getTextWidth", "getDocument", "setPage", "page", "addPage", "getGlobalOptions", "getDocumentOptions", "pageSize", "getWidth", "getHeight", "getLineHeight", "pageNumber", "pageInfo", "getCurrentPageInfo", "getNumberOfPages", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "p", "hasOwnProperty", "call", "__extends", "TypeError", "String", "__", "constructor", "create", "SuppressedError", "error", "suppressed", "message", "e", "Error", "name", "HtmlRowInput", "_super", "element", "_this", "_element", "defaultStyles", "overflow", "cellPadding", "cellWidth", "minCellHeight", "min<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTheme", "themes", "striped", "table", "head", "body", "foot", "alternateRow", "grid", "plain", "getStringWidth", "textArr", "widestLineWidth", "map", "reduce", "a", "Math", "max", "addTableBorder", "startPos", "cursor", "settings", "tableLineWidth", "tableLineColor", "getFillStyle", "drawLine", "drawBackground", "parseSpacing", "value", "defaultValue", "top", "right", "bottom", "left", "vertical", "horizontal", "getPageAvailableWidth", "margins", "margin", "parseCss", "supportedFonts", "style", "window", "result", "pxScaleFactor", "backgroundColor", "parseColor", "elem", "getComputedStyle", "padding", "parsePadding", "borderColorSide", "finalScaleFactor", "btw", "borderTopWidth", "borderBottomWidth", "borderRightWidth", "borderLeftWidth", "borderWidth", "parseFloat", "borderColor", "accepted", "textAlign", "verticalAlign", "res", "parseInt", "isNaN", "parseFontStyle", "fontFamily", "toLowerCase", "fontWeight", "styleGetter", "cssColor", "realColor", "rgba", "match", "alpha", "bg", "parentElement", "val", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "linePadding", "inputPadding", "n", "parseHtml", "input", "includeHiddenHtml", "useCss", "tableElement", "document", "querySelector", "keys", "console", "i", "rows", "tagName", "row", "parse<PERSON>ow<PERSON><PERSON>nt", "push", "includeHidden", "resultRow", "cells", "cell", "style_1", "display", "cellStyles", "rowSpan", "colSpan", "content", "<PERSON>se<PERSON><PERSON><PERSON><PERSON><PERSON>", "orgCell", "cloneNode", "innerHTML", "replace", "part", "trim", "join", "innerText", "textContent", "validateInput", "global", "current", "_i", "options", "startY", "assign", "target", "s", "s1", "s2", "s3", "to", "index", "arguments", "nextSource", "<PERSON><PERSON><PERSON>", "parseInput", "win", "parseStyles", "hooks", "parseHooks", "parseSettings", "parseContent$1", "id", "tableId", "gInput", "dInput", "cInput", "styleOptions", "headStyles", "bodyStyles", "footStyles", "alternateRowStyles", "columnStyles", "_loop_1", "prop", "global_1", "document_1", "allOptions", "didParseCell", "will<PERSON><PERSON><PERSON>ell", "didDrawCell", "willDrawPage", "didDrawPage", "allOptions_1", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "_m", "getStartY", "showFoot", "showHead", "theme", "horizontalPageBreak", "horizontalPageBreakRepeat", "pageBreak", "rowPageBreak", "tableWidth", "horizontalPageBreakBehaviour", "userStartY", "previous", "sf", "currentPage", "isSamePageAsPreviousTable", "startPageNumber", "endingPage", "finalY", "html", "hidden", "htmlContent", "columns", "parseColumns", "firstRow", "filter", "key", "for<PERSON>ach", "concat", "rowResult", "dataKey", "HookData", "CellHookData", "column", "section", "Table", "getHeadHeight", "acc", "getMaxCellHeight", "getFootHeight", "allRows", "callCellHooks", "handlers", "handlers_1", "handler", "data", "callEndPageHooks", "callWillDrawPageHooks", "pageWidth", "<PERSON><PERSON><PERSON><PERSON>", "total", "col", "Row", "raw", "spansMultiplePages", "hasRowSpan", "canEntireRowFit", "getMinimumRowHeight", "vPadding", "oneRowHeight", "Cell", "contentHeight", "contentWidth", "minReadableWidth", "min<PERSON><PERSON><PERSON>", "getTextPos", "netHeight", "netWidth", "getContentHeight", "Column", "getMaxCustomCellWidth", "calculateWidths", "calculate", "resizableColumns", "initialTableWidth", "customWidth", "resizeWidth", "resizeColumns", "abs", "round", "warn", "applyColSpans", "<PERSON><PERSON><PERSON><PERSON>", "applyRowSpans", "availablePageWidth", "longestWordWidth", "defaultMinWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialResizeWidth", "sumWrappedWidth", "ratio", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "newWidth", "rowSpanCells", "colRowSpansLeft", "all", "rowIndex", "remaining", "colSpanCell", "combinedColSpanWidth", "colSpansLeft", "columnIndex", "rowSpanHeight", "count", "textSpace", "ellipsize", "realContentHeight", "str", "ellipsizeStr", "precision", "ceil", "substring", "createTable", "jsPDFDoc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createColumns", "sectionRow", "generateSectionRow", "parseSection", "sectionName", "sectionRows", "styleProps", "rowSpansLeftForColumn", "rawRow", "skippedRowForRowSpans", "colSpansAdded", "columnSpansLeft", "columns_1", "rawCell", "cellInputStyles", "times", "title", "getSectionTitle", "header", "footer", "themeName", "sectionStyles", "otherStyles", "colStyles", "rowStyles", "defaultStyle", "themeStyles", "getColumnsCanFitInPage", "config", "remainingWidth", "repeatColumnsMap", "Map", "colIndexes", "field", "find", "item", "has", "set", "first", "start", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastIndex", "calculateAllColumnsCanFitInPage", "allResults", "drawTable", "sectionsHeight", "minTableBottomPos", "tableHeight", "nextPage", "printTableWithHorizontalPageBreak", "printRow", "isLastRow", "printFullRow", "allColumnsCanFitResult", "colsAndIndexes", "printHead", "printBody", "printFoot", "lastRowIndexOfLastPage_1", "firstColumnsToFitResult", "lastPrintedRowIndex", "firstColumnsToFit", "printBodyWithoutPageBreaks", "maxNumberOfRows", "slice", "startRowIndex", "endRowIndex", "min", "remainingSpace", "getRemainingPageSpace", "getRemainingLineCount", "remainingPageSpace", "remainingLines", "floor", "modifyRowToFit", "rowHeight", "remainderCell", "remainingLineCount", "splice", "remainderRow", "shouldPrintOnCurrentPage", "pageHeight", "marginHeight", "maxRowHeight", "minRowHeight", "minRowFits", "rowHasRowSpanCell", "rowHigherThanPage", "drawCellRect", "textPos", "getFillColor", "drawCellBorders", "x1", "y1", "x2", "y2", "line", "bottomContentHeight", "suppressFooter", "newCurrent", "applyPlugin", "jsPDF", "API", "autoTable", "args", "autoTableSetDefaults", "autoTableHtmlToJson", "tableElem", "includeHiddenElements", "__createTable", "__drawTable", "anyWindow", "jspdf", "default"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs"], "sourcesContent": ["/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText (text, x, y, styles, doc) {\n    styles = styles || {};\n    var PHYSICAL_LINE_HEIGHT = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var lineHeightFactor = doc.getLineHeightFactor\n        ? doc.getLineHeightFactor()\n        : PHYSICAL_LINE_HEIGHT;\n    var lineHeight = fontSize * lineHeightFactor;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * lineHeight;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * lineHeight;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, { maxWidth: styles.maxWidth || 100, align: 'justify' });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\n\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n            this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        }\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1 &&\n                this.jsPDFDocument.setFontStyle) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    /**\n     * Adds a rectangle to the PDF\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n     * @param width Width (in units declared at inception of PDF document)\n     * @param height Height (in units declared at inception of PDF document)\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n     */\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        // null is excluded from fillStyle possible values because it isn't needed\n        // and is prone to bugs as it's used to postpone setting the style\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = { width: pageSize.getWidth(), height: pageSize.getHeight() };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.getLineHeightFactor = function () {\n        var doc = this.jsPDFDocument;\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n    };\n    DocHandler.prototype.getLineHeight = function (fontSize) {\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica', // helvetica, times, courier\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n        textColor: 20,\n        halign: 'left', // left, center, right, justify\n        valign: 'top', // top, middle, bottom\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto', // 'auto'|'wrap'|number\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: { head: { fontStyle: 'bold' }, foot: { fontStyle: 'bold' } },\n    };\n    return themes[name];\n}\n\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nfunction getPageAvailableWidth(doc, table) {\n    var margins = parseSpacing(table.settings.margin, 0);\n    return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    var borderColorSide = 'borderTopColor';\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\n    var btw = style.borderTopWidth;\n    if (style.borderBottomWidth === btw &&\n        style.borderRightWidth === btw &&\n        style.borderLeftWidth === btw) {\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n        if (borderWidth)\n            result.lineWidth = borderWidth;\n    }\n    else {\n        result.lineWidth = {\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\n        };\n        // Choose border color of first available side\n        // could be improved by supporting object as lineColor\n        if (!result.lineWidth.top) {\n            if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n            }\n            else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n            }\n            else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n            }\n        }\n    }\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)[borderColorSide];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = parseSpacing(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\nfunction validateInput(global, document, current) {\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n    }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\n\nfunction parseInput(d, current) {\n    var doc = new DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    validateInput(global, document, current);\n    var options = assign({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent$1(doc, options, win);\n    return { id: current.tableId, content: content, hooks: hooks, styles: styles, settings: settings };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = assign({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        willDrawPage: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.willDrawPage)\n            result.willDrawPage.push(options.willDrawPage);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = !!options.horizontalPageBreak;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent$1(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return { columns: columns, head: head, body: body, foot: foot };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\n\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + lineHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    // TODO (v4): replace parameters with only (lineHeight)\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\n        var height = lineCount * lineHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = parseSpacing(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = getPageAvailableWidth(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n            // them in the split process to ensure correct word separation and width\n            // calculation.\n            var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= getStringWidth(text, styles, doc)) {\n        return text;\n    }\n    while (width < getStringWidth(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\nfunction createTable(jsPDFDoc, input) {\n    var doc = new DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new Table(input, content);\n    calculateWidths(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a;\n        var key;\n        if (typeof input === 'object') {\n            key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n        }\n        else {\n            key = index;\n        }\n        return new Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = getTheme(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? assign({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = defaultStyles(scaleFactor);\n    var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n    var _a;\n    if (config === void 0) { config = {}; }\n    // Get page width\n    var remainingWidth = getPageAvailableWidth(doc, table);\n    // Get column data key to repeat\n    var repeatColumnsMap = new Map();\n    var colIndexes = [];\n    var columns = [];\n    var horizontalPageBreakRepeat = [];\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n        // It can be a single value of type string or number (even number: 0)\n    }\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n    }\n    // Code to repeat the given column in split pages\n    horizontalPageBreakRepeat.forEach(function (field) {\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\n        if (col && !repeatColumnsMap.has(col.index)) {\n            repeatColumnsMap.set(col.index, true);\n            colIndexes.push(col.index);\n            columns.push(table.columns[col.index]);\n            remainingWidth -= col.wrappedWidth;\n        }\n    });\n    var first = true;\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n    while (i < table.columns.length) {\n        // Prevent duplicates\n        if (repeatColumnsMap.has(i)) {\n            i++;\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        // Take at least one column even if it doesn't fit\n        if (first || remainingWidth >= colWidth) {\n            first = false;\n            colIndexes.push(i);\n            columns.push(table.columns[i]);\n            remainingWidth -= colWidth;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n    var allResults = [];\n    for (var i = 0; i < table.columns.length; i++) {\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\n        if (result.columns.length) {\n            allResults.push(result);\n            i = result.lastIndex;\n        }\n    }\n    return allResults;\n}\n\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = { x: margin.left, y: startY };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.body;\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    table.callWillDrawPageHooks(doc, cursor);\n    var startPos = assign({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    addTableBorder(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n    var settings = table.settings;\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n            doc.applyStyles(doc.userStyles);\n            // add page to print next columns in new page\n            if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n            }\n            else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n            }\n            // print body & footer for selected columns\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\n        });\n    }\n    else {\n        var lastRowIndexOfLastPage_1 = -1;\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\n        var _loop_1 = function () {\n            // Print the first columns, taking note of the last row printed\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n            if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                    // When adding a page here, make sure not to print the footers\n                    // because they were already printed before on this same loop\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                }\n                else {\n                    printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n            }\n            // Check how many rows were printed, so that the next columns would not print more rows than that\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n            // Print the next columns, never exceding maxNumberOfRows\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n        };\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n            _loop_1();\n        }\n    }\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n    doc.applyStyles(doc.userStyles);\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n    var lastPrintedRowIndex = -1;\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n        var isLastRow = startRowIndex + index === table.body.length - 1;\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n        if (row.canEntireRowFit(remainingSpace, columns)) {\n            printRow(doc, table, row, cursor, columns);\n            lastPrintedRowIndex = startRowIndex + index;\n        }\n    });\n    return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = assign(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        var lineHeightFactor = doc.getLineHeightFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        // The row fits in the current page\n        printRow(doc, table, row, cursor, columns);\n    }\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n        // The row gets split in two here, each piece in one page\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n        printRow(doc, table, row, cursor, columns);\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n    }\n    else {\n        // The row get printed entirelly on the next page\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellRect(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        autoTableText(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n    // TODO (v4): better solution?\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // Draw cell background with normal borders\n        var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        // Draw cell background\n        if (cellStyles.fillColor) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        }\n        // Draw cell individual borders\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n    }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n    var x1, y1, x2, y2;\n    if (lineWidth.top) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.top, x1, y1, x2, y2);\n    }\n    if (lineWidth.bottom) {\n        x1 = cursor.x;\n        y1 = cursor.y + cell.height;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\n    }\n    if (lineWidth.left) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.left, x1, y1, x2, y2);\n    }\n    if (lineWidth.right) {\n        x1 = cursor.x + cell.width;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.right, x1, y1, x2, y2);\n    }\n    function drawLine(width, x1, y1, x2, y2) {\n        doc.getDocument().setLineWidth(width);\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\n    }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n    if (columns === void 0) { columns = []; }\n    if (suppressFooter === void 0) { suppressFooter = false; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    addTableBorder(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    // call didAddPage hooks before any content is added to the page\n    table.callWillDrawPageHooks(doc, cursor);\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n        doc.applyStyles(doc.userStyles);\n    }\n}\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n        return true;\n    }\n    return false;\n}\n\nfunction applyPlugin(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options = args[0];\n        var input = parseInput(this, options);\n        var table = createTable(this, input);\n        drawTable(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        autoTableText(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        var _a;\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new DocHandler(this);\n        var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\n        return { columns: columns, rows: body, data: body };\n    };\n}\n\nvar _a;\nfunction autoTable(d, options) {\n    var input = parseInput(d, options);\n    var table = createTable(d, input);\n    drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = parseInput(d, options);\n    return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n    drawTable(d, table);\n}\ntry {\n    if (typeof window !== 'undefined' && window) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        var anyWindow = window;\n        var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\n        if (jsPDF) {\n            applyPlugin(jsPDF);\n        }\n    }\n}\ncatch (error) {\n    console.error('Could not apply autoTable plugin', error);\n}\n\nexport { Cell, CellHookData, Column, HookData, Row, Table, __createTable, __drawTable, applyPlugin, autoTable, autoTable as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAaA,CAAEC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAEC,GAAG,EAAE;EAC7CD,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIE,oBAAoB,GAAG,IAAI;EAC/B,IAAIC,CAAC,GAAGF,GAAG,CAACG,QAAQ,CAACC,WAAW;EAChC,IAAIC,QAAQ,GAAGL,GAAG,CAACG,QAAQ,CAACG,WAAW,CAAC,CAAC,GAAGJ,CAAC;EAC7C,IAAIK,gBAAgB,GAAGP,GAAG,CAACQ,mBAAmB,GACxCR,GAAG,CAACQ,mBAAmB,CAAC,CAAC,GACzBP,oBAAoB;EAC1B,IAAIQ,UAAU,GAAGJ,QAAQ,GAAGE,gBAAgB;EAC5C,IAAIG,UAAU,GAAG,aAAa;EAC9B,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIb,MAAM,CAACc,MAAM,KAAK,QAAQ,IAC1Bd,MAAM,CAACc,MAAM,KAAK,QAAQ,IAC1Bd,MAAM,CAACe,MAAM,KAAK,QAAQ,IAC1Bf,MAAM,CAACe,MAAM,KAAK,OAAO,EAAE;IAC3BH,SAAS,GAAG,OAAOf,IAAI,KAAK,QAAQ,GAAGA,IAAI,CAACmB,KAAK,CAACL,UAAU,CAAC,GAAGd,IAAI;IACpEgB,SAAS,GAAGD,SAAS,CAACK,MAAM,IAAI,CAAC;EACrC;EACA;EACAlB,CAAC,IAAIO,QAAQ,IAAI,CAAC,GAAGJ,oBAAoB,CAAC;EAC1C,IAAIF,MAAM,CAACc,MAAM,KAAK,QAAQ,EAC1Bf,CAAC,IAAKc,SAAS,GAAG,CAAC,GAAIH,UAAU,CAAC,KACjC,IAAIV,MAAM,CAACc,MAAM,KAAK,QAAQ,EAC/Bf,CAAC,IAAIc,SAAS,GAAGH,UAAU;EAC/B,IAAIV,MAAM,CAACe,MAAM,KAAK,QAAQ,IAAIf,MAAM,CAACe,MAAM,KAAK,OAAO,EAAE;IACzD,IAAIG,SAAS,GAAGZ,QAAQ;IACxB,IAAIN,MAAM,CAACe,MAAM,KAAK,QAAQ,EAC1BG,SAAS,IAAI,GAAG;IACpB,IAAIN,SAAS,IAAIC,SAAS,IAAI,CAAC,EAAE;MAC7B,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,SAAS,CAACK,MAAM,EAAEE,KAAK,EAAE,EAAE;QACnDlB,GAAG,CAACJ,IAAI,CAACe,SAAS,CAACO,KAAK,CAAC,EAAErB,CAAC,GAAGG,GAAG,CAACmB,kBAAkB,CAACR,SAAS,CAACO,KAAK,CAAC,CAAC,GAAGD,SAAS,EAAEnB,CAAC,CAAC;QACvFA,CAAC,IAAIW,UAAU;MACnB;MACA,OAAOT,GAAG;IACd;IACAH,CAAC,IAAIG,GAAG,CAACmB,kBAAkB,CAACvB,IAAI,CAAC,GAAGqB,SAAS;EACjD;EACA,IAAIlB,MAAM,CAACe,MAAM,KAAK,SAAS,EAAE;IAC7Bd,GAAG,CAACJ,IAAI,CAACA,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAE;MAAEsB,QAAQ,EAAErB,MAAM,CAACqB,QAAQ,IAAI,GAAG;MAAEC,KAAK,EAAE;IAAU,CAAC,CAAC;EAChF,CAAC,MACI;IACDrB,GAAG,CAACJ,IAAI,CAACA,IAAI,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACxB;EACA,OAAOE,GAAG;AACd;AAEA,IAAIsB,cAAc,GAAG,CAAC,CAAC;AACvB,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,aAAa,EAAE;IAC/B,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,UAAU,GAAG;MACd;MACAC,SAAS,EAAEF,aAAa,CAACG,YAAY,GAC/B,IAAI,CAACH,aAAa,CAACG,YAAY,CAAC,CAAC,GACjC,CAAC;MACPtB,QAAQ,EAAEmB,aAAa,CAACrB,QAAQ,CAACG,WAAW,CAAC,CAAC;MAC9CsB,SAAS,EAAEJ,aAAa,CAACrB,QAAQ,CAAC0B,OAAO,CAAC,CAAC,CAACD,SAAS;MACrDE,IAAI,EAAEN,aAAa,CAACrB,QAAQ,CAAC0B,OAAO,CAAC,CAAC,CAACE,QAAQ;MAC/C;MACAC,SAAS,EAAER,aAAa,CAACS,YAAY,GAC/B,IAAI,CAACT,aAAa,CAACS,YAAY,CAAC,CAAC,GACjC,CAAC;MACP;MACAC,SAAS,EAAEV,aAAa,CAACW,YAAY,GAC/B,IAAI,CAACX,aAAa,CAACW,YAAY,CAAC,CAAC,GACjC;IACV,CAAC;EACL;EACAZ,UAAU,CAACa,WAAW,GAAG,UAAUC,QAAQ,EAAErC,GAAG,EAAE;IAC9C,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAG,IAAI;IAAE;IAClC,IAAIA,GAAG,EAAE;MACLA,GAAG,CAACsC,2BAA2B,GAAGD,QAAQ;IAC9C,CAAC,MACI;MACDf,cAAc,GAAGe,QAAQ;IAC7B;EACJ,CAAC;EACDd,UAAU,CAACgB,UAAU,GAAG,UAAUC,CAAC,EAAE;IACjC,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE;MAClB,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MAC5B,OAAO,CAACA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;IACpB,CAAC,MACI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MAC5B,OAAO,CAACA,CAAC,CAAC;IACd,CAAC,MACI;MACD,OAAO,IAAI;IACf;EACJ,CAAC;EACDjB,UAAU,CAACoB,SAAS,CAACC,WAAW,GAAG,UAAU7C,MAAM,EAAE8C,QAAQ,EAAE;IAC3D;IACA;IACA,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,IAAIH,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,KAAK;IAAE;IAC7C,IAAI9C,MAAM,CAAC6B,SAAS,IAAI,IAAI,CAACJ,aAAa,CAACyB,YAAY,EAAE;MACrD,IAAI,CAACzB,aAAa,CAACyB,YAAY,CAAClD,MAAM,CAAC6B,SAAS,CAAC;IACrD;IACA,IAAIsB,EAAE,GAAG,IAAI,CAAC1B,aAAa,CAACrB,QAAQ,CAAC0B,OAAO,CAAC,CAAC;MAAED,SAAS,GAAGsB,EAAE,CAACtB,SAAS;MAAEG,QAAQ,GAAGmB,EAAE,CAACnB,QAAQ;IAChG,IAAIhC,MAAM,CAAC+B,IAAI,EACXC,QAAQ,GAAGhC,MAAM,CAAC+B,IAAI;IAC1B,IAAI/B,MAAM,CAAC6B,SAAS,EAAE;MAClBA,SAAS,GAAG7B,MAAM,CAAC6B,SAAS;MAC5B,IAAIuB,mBAAmB,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACrB,QAAQ,CAAC;MACtD,IAAIoB,mBAAmB,IACnBA,mBAAmB,CAACE,OAAO,CAACzB,SAAS,CAAC,KAAK,CAAC,CAAC,IAC7C,IAAI,CAACJ,aAAa,CAACyB,YAAY,EAAE;QACjC;QACA;QACA;QACA,IAAI,CAACzB,aAAa,CAACyB,YAAY,CAACE,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvDvB,SAAS,GAAGuB,mBAAmB,CAAC,CAAC,CAAC;MACtC;IACJ;IACA,IAAI,CAAC3B,aAAa,CAAC8B,OAAO,CAACvB,QAAQ,EAAEH,SAAS,CAAC;IAC/C,IAAI7B,MAAM,CAACM,QAAQ,EACf,IAAI,CAACmB,aAAa,CAAC+B,WAAW,CAACxD,MAAM,CAACM,QAAQ,CAAC;IACnD,IAAIwC,QAAQ,EAAE;MACV,OAAO,CAAC;IACZ;IACA,IAAIW,KAAK,GAAGjC,UAAU,CAACgB,UAAU,CAACxC,MAAM,CAAC0D,SAAS,CAAC;IACnD,IAAID,KAAK,EACL,CAACV,EAAE,GAAG,IAAI,CAACtB,aAAa,EAAEkC,YAAY,CAACC,KAAK,CAACb,EAAE,EAAEU,KAAK,CAAC;IAC3DA,KAAK,GAAGjC,UAAU,CAACgB,UAAU,CAACxC,MAAM,CAAC2B,SAAS,CAAC;IAC/C,IAAI8B,KAAK,EACL,CAACT,EAAE,GAAG,IAAI,CAACvB,aAAa,EAAEoC,YAAY,CAACD,KAAK,CAACZ,EAAE,EAAES,KAAK,CAAC;IAC3DA,KAAK,GAAGjC,UAAU,CAACgB,UAAU,CAACxC,MAAM,CAACmC,SAAS,CAAC;IAC/C,IAAIsB,KAAK,EACL,CAACR,EAAE,GAAG,IAAI,CAACxB,aAAa,EAAEqC,YAAY,CAACF,KAAK,CAACX,EAAE,EAAEQ,KAAK,CAAC;IAC3D,IAAI,OAAOzD,MAAM,CAACiC,SAAS,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACR,aAAa,CAACsC,YAAY,CAAC/D,MAAM,CAACiC,SAAS,CAAC;IACrD;EACJ,CAAC;EACDT,UAAU,CAACoB,SAAS,CAACoB,eAAe,GAAG,UAAUnE,IAAI,EAAEoE,IAAI,EAAEC,IAAI,EAAE;IAC/D,OAAO,IAAI,CAACzC,aAAa,CAACuC,eAAe,CAACnE,IAAI,EAAEoE,IAAI,EAAEC,IAAI,CAAC;EAC/D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI1C,UAAU,CAACoB,SAAS,CAACuB,IAAI,GAAG,UAAUrE,CAAC,EAAEC,CAAC,EAAEqE,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAE;IAClE;IACA;IACA;IACA,OAAO,IAAI,CAAC7C,aAAa,CAAC0C,IAAI,CAACrE,CAAC,EAAEC,CAAC,EAAEqE,KAAK,EAAEC,MAAM,EAAEC,SAAS,CAAC;EAClE,CAAC;EACD9C,UAAU,CAACoB,SAAS,CAAC2B,gBAAgB,GAAG,YAAY;IAChD,OAAO,IAAI,CAAC9C,aAAa,CAAC+C,aAAa,IAAI,IAAI;EACnD,CAAC;EACDhD,UAAU,CAACoB,SAAS,CAAC6B,YAAY,GAAG,UAAU5E,IAAI,EAAE;IAChD,OAAO,IAAI,CAAC4B,aAAa,CAACgD,YAAY,CAAC5E,IAAI,CAAC;EAChD,CAAC;EACD2B,UAAU,CAACoB,SAAS,CAAC8B,WAAW,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACjD,aAAa;EAC7B,CAAC;EACDD,UAAU,CAACoB,SAAS,CAAC+B,OAAO,GAAG,UAAUC,IAAI,EAAE;IAC3C,IAAI,CAACnD,aAAa,CAACkD,OAAO,CAACC,IAAI,CAAC;EACpC,CAAC;EACDpD,UAAU,CAACoB,SAAS,CAACiC,OAAO,GAAG,YAAY;IACvC,OAAO,IAAI,CAACpD,aAAa,CAACoD,OAAO,CAAC,CAAC;EACvC,CAAC;EACDrD,UAAU,CAACoB,SAAS,CAACS,WAAW,GAAG,YAAY;IAC3C,OAAO,IAAI,CAAC5B,aAAa,CAAC4B,WAAW,CAAC,CAAC;EAC3C,CAAC;EACD7B,UAAU,CAACoB,SAAS,CAACkC,gBAAgB,GAAG,YAAY;IAChD,OAAOvD,cAAc,IAAI,CAAC,CAAC;EAC/B,CAAC;EACDC,UAAU,CAACoB,SAAS,CAACmC,kBAAkB,GAAG,YAAY;IAClD,OAAO,IAAI,CAACtD,aAAa,CAACc,2BAA2B,IAAI,CAAC,CAAC;EAC/D,CAAC;EACDf,UAAU,CAACoB,SAAS,CAACoC,QAAQ,GAAG,YAAY;IACxC,IAAIA,QAAQ,GAAG,IAAI,CAACvD,aAAa,CAACrB,QAAQ,CAAC4E,QAAQ;IACnD;IACA,IAAIA,QAAQ,CAACZ,KAAK,IAAI,IAAI,EAAE;MACxBY,QAAQ,GAAG;QAAEZ,KAAK,EAAEY,QAAQ,CAACC,QAAQ,CAAC,CAAC;QAAEZ,MAAM,EAAEW,QAAQ,CAACE,SAAS,CAAC;MAAE,CAAC;IAC3E;IACA,OAAOF,QAAQ;EACnB,CAAC;EACDxD,UAAU,CAACoB,SAAS,CAACvC,WAAW,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACoB,aAAa,CAACrB,QAAQ,CAACC,WAAW;EAClD,CAAC;EACDmB,UAAU,CAACoB,SAAS,CAACnC,mBAAmB,GAAG,YAAY;IACnD,IAAIR,GAAG,GAAG,IAAI,CAACwB,aAAa;IAC5B,OAAOxB,GAAG,CAACQ,mBAAmB,GAAGR,GAAG,CAACQ,mBAAmB,CAAC,CAAC,GAAG,IAAI;EACrE,CAAC;EACDe,UAAU,CAACoB,SAAS,CAACuC,aAAa,GAAG,UAAU7E,QAAQ,EAAE;IACrD,OAAQA,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC,GAAI,IAAI,CAACI,mBAAmB,CAAC,CAAC;EACvE,CAAC;EACDe,UAAU,CAACoB,SAAS,CAACwC,UAAU,GAAG,YAAY;IAC1C,IAAIC,QAAQ,GAAG,IAAI,CAAC5D,aAAa,CAACrB,QAAQ,CAACkF,kBAAkB,CAAC,CAAC;IAC/D,IAAI,CAACD,QAAQ,EAAE;MACX;MACA,OAAO,IAAI,CAAC5D,aAAa,CAACrB,QAAQ,CAACmF,gBAAgB,CAAC,CAAC;IACzD;IACA,OAAOF,QAAQ,CAACD,UAAU;EAC9B,CAAC;EACD,OAAO5D,UAAU;AACrB,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIgE,aAAa,GAAG,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;EAC/BF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;IAAEC,SAAS,EAAE;EAAG,CAAC,YAAYnD,KAAK,IAAI,UAAU+C,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;EAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;IAAE,KAAK,IAAII,CAAC,IAAIJ,CAAC,EAAE,IAAIC,MAAM,CAAC/C,SAAS,CAACmD,cAAc,CAACC,IAAI,CAACN,CAAC,EAAEI,CAAC,CAAC,EAAEL,CAAC,CAACK,CAAC,CAAC,GAAGJ,CAAC,CAACI,CAAC,CAAC;EAAE,CAAC;EACrG,OAAON,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B,CAAC;AAED,SAASO,SAASA,CAACR,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIQ,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACT,CAAC,CAAC,GAAG,+BAA+B,CAAC;EAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EACnB,SAASU,EAAEA,CAAA,EAAG;IAAE,IAAI,CAACC,WAAW,GAAGZ,CAAC;EAAE;EACtCA,CAAC,CAAC7C,SAAS,GAAG8C,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACW,MAAM,CAACZ,CAAC,CAAC,IAAIU,EAAE,CAACxD,SAAS,GAAG8C,CAAC,CAAC9C,SAAS,EAAE,IAAIwD,EAAE,CAAC,CAAC,CAAC;AACxF;AAEA,OAAOG,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE;EAC5F,IAAIC,CAAC,GAAG,IAAIC,KAAK,CAACF,OAAO,CAAC;EAC1B,OAAOC,CAAC,CAACE,IAAI,GAAG,iBAAiB,EAAEF,CAAC,CAACH,KAAK,GAAGA,KAAK,EAAEG,CAAC,CAACF,UAAU,GAAGA,UAAU,EAAEE,CAAC;AACpF,CAAC;AAED,IAAIG,YAAY,GAAG,aAAe,UAAUC,MAAM,EAAE;EAChDd,SAAS,CAACa,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAACE,OAAO,EAAE;IAC3B,IAAIC,KAAK,GAAGF,MAAM,CAACf,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCiB,KAAK,CAACC,QAAQ,GAAGF,OAAO;IACxB,OAAOC,KAAK;EAChB;EACA,OAAOH,YAAY;AACvB,CAAC,CAACpE,KAAK,CAAE;AACT;AACA,SAASyE,aAAaA,CAAC9G,WAAW,EAAE;EAChC,OAAO;IACH0B,IAAI,EAAE,WAAW;IAAE;IACnBF,SAAS,EAAE,QAAQ;IAAE;IACrBuF,QAAQ,EAAE,WAAW;IAAE;IACvB1D,SAAS,EAAE,KAAK;IAAE;IAClB/B,SAAS,EAAE,EAAE;IACbZ,MAAM,EAAE,MAAM;IAAE;IAChBD,MAAM,EAAE,KAAK;IAAE;IACfR,QAAQ,EAAE,EAAE;IACZ+G,WAAW,EAAE,CAAC,GAAGhH,WAAW;IAAE;IAC9B8B,SAAS,EAAE,GAAG;IACdF,SAAS,EAAE,CAAC;IACZqF,SAAS,EAAE,MAAM;IAAE;IACnBC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE;EAClB,CAAC;AACL;AACA,SAASC,QAAQA,CAACZ,IAAI,EAAE;EACpB,IAAIa,MAAM,GAAG;IACTC,OAAO,EAAE;MACLC,KAAK,EAAE;QAAElE,SAAS,EAAE,GAAG;QAAE/B,SAAS,EAAE,EAAE;QAAEE,SAAS,EAAE;MAAS,CAAC;MAC7DgG,IAAI,EAAE;QAAElG,SAAS,EAAE,GAAG;QAAE+B,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;QAAE7B,SAAS,EAAE;MAAO,CAAC;MACtEiG,IAAI,EAAE,CAAC,CAAC;MACRC,IAAI,EAAE;QAAEpG,SAAS,EAAE,GAAG;QAAE+B,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;QAAE7B,SAAS,EAAE;MAAO,CAAC;MACtEmG,YAAY,EAAE;QAAEtE,SAAS,EAAE;MAAI;IACnC,CAAC;IACDuE,IAAI,EAAE;MACFL,KAAK,EAAE;QACHlE,SAAS,EAAE,GAAG;QACd/B,SAAS,EAAE,EAAE;QACbE,SAAS,EAAE,QAAQ;QACnBI,SAAS,EAAE;MACf,CAAC;MACD4F,IAAI,EAAE;QACFlG,SAAS,EAAE,GAAG;QACd+B,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;QACzB7B,SAAS,EAAE,MAAM;QACjBI,SAAS,EAAE;MACf,CAAC;MACD6F,IAAI,EAAE,CAAC,CAAC;MACRC,IAAI,EAAE;QACFpG,SAAS,EAAE,GAAG;QACd+B,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;QACzB7B,SAAS,EAAE,MAAM;QACjBI,SAAS,EAAE;MACf,CAAC;MACD+F,YAAY,EAAE,CAAC;IACnB,CAAC;IACDE,KAAK,EAAE;MAAEL,IAAI,EAAE;QAAEhG,SAAS,EAAE;MAAO,CAAC;MAAEkG,IAAI,EAAE;QAAElG,SAAS,EAAE;MAAO;IAAE;EACtE,CAAC;EACD,OAAO6F,MAAM,CAACb,IAAI,CAAC;AACvB;AAEA,SAASsB,cAAcA,CAACtI,IAAI,EAAEG,MAAM,EAAEC,GAAG,EAAE;EACvCA,GAAG,CAAC4C,WAAW,CAAC7C,MAAM,EAAE,IAAI,CAAC;EAC7B,IAAIoI,OAAO,GAAG1F,KAAK,CAACC,OAAO,CAAC9C,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EACjD,IAAIwI,eAAe,GAAGD,OAAO,CACxBE,GAAG,CAAC,UAAUzI,IAAI,EAAE;IAAE,OAAOI,GAAG,CAACwE,YAAY,CAAC5E,IAAI,CAAC;EAAE,CAAC,CAAC,CACvD0I,MAAM,CAAC,UAAUC,CAAC,EAAE9C,CAAC,EAAE;IAAE,OAAO+C,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE9C,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,CAAC;EAC1D,OAAO2C,eAAe;AAC1B;AACA,SAASM,cAAcA,CAAC1I,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAE;EAClD,IAAI5G,SAAS,GAAG2F,KAAK,CAACkB,QAAQ,CAACC,cAAc;EAC7C,IAAI5G,SAAS,GAAGyF,KAAK,CAACkB,QAAQ,CAACE,cAAc;EAC7C/I,GAAG,CAAC4C,WAAW,CAAC;IAAEZ,SAAS,EAAEA,SAAS;IAAEE,SAAS,EAAEA;EAAU,CAAC,CAAC;EAC/D,IAAImC,SAAS,GAAG2E,YAAY,CAAChH,SAAS,EAAE,KAAK,CAAC;EAC9C,IAAIqC,SAAS,EAAE;IACXrE,GAAG,CAACkE,IAAI,CAACyE,QAAQ,CAAC9I,CAAC,EAAE8I,QAAQ,CAAC7I,CAAC,EAAE6H,KAAK,CAAC3C,QAAQ,CAAChF,GAAG,CAAC+E,QAAQ,CAAC,CAAC,CAACZ,KAAK,CAAC,EAAEyE,MAAM,CAAC9I,CAAC,GAAG6I,QAAQ,CAAC7I,CAAC,EAAEuE,SAAS,CAAC;EAC5G;AACJ;AACA,SAAS2E,YAAYA,CAAChH,SAAS,EAAEyB,SAAS,EAAE;EACxC,IAAIwF,QAAQ,GAAGjH,SAAS,GAAG,CAAC;EAC5B,IAAIkH,cAAc,GAAGzF,SAAS,IAAIA,SAAS,KAAK,CAAC;EACjD,IAAIwF,QAAQ,IAAIC,cAAc,EAAE;IAC5B,OAAO,IAAI,CAAC,CAAC;EACjB,CAAC,MACI,IAAID,QAAQ,EAAE;IACf,OAAO,GAAG,CAAC,CAAC;EAChB,CAAC,MACI,IAAIC,cAAc,EAAE;IACrB,OAAO,GAAG,CAAC,CAAC;EAChB,CAAC,MACI;IACD,OAAO,IAAI;EACf;AACJ;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACvC,IAAIvG,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,EAAE;EAClBkG,KAAK,GAAGA,KAAK,IAAIC,YAAY;EAC7B,IAAI5G,KAAK,CAACC,OAAO,CAAC0G,KAAK,CAAC,EAAE;IACtB,IAAIA,KAAK,CAACpI,MAAM,IAAI,CAAC,EAAE;MACnB,OAAO;QACHsI,GAAG,EAAEF,KAAK,CAAC,CAAC,CAAC;QACbG,KAAK,EAAEH,KAAK,CAAC,CAAC,CAAC;QACfI,MAAM,EAAEJ,KAAK,CAAC,CAAC,CAAC;QAChBK,IAAI,EAAEL,KAAK,CAAC,CAAC;MACjB,CAAC;IACL,CAAC,MACI,IAAIA,KAAK,CAACpI,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO;QACHsI,GAAG,EAAEF,KAAK,CAAC,CAAC,CAAC;QACbG,KAAK,EAAEH,KAAK,CAAC,CAAC,CAAC;QACfI,MAAM,EAAEJ,KAAK,CAAC,CAAC,CAAC;QAChBK,IAAI,EAAEL,KAAK,CAAC,CAAC;MACjB,CAAC;IACL,CAAC,MACI,IAAIA,KAAK,CAACpI,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO;QACHsI,GAAG,EAAEF,KAAK,CAAC,CAAC,CAAC;QACbG,KAAK,EAAEH,KAAK,CAAC,CAAC,CAAC;QACfI,MAAM,EAAEJ,KAAK,CAAC,CAAC,CAAC;QAChBK,IAAI,EAAEL,KAAK,CAAC,CAAC;MACjB,CAAC;IACL,CAAC,MACI,IAAIA,KAAK,CAACpI,MAAM,KAAK,CAAC,EAAE;MACzBoI,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IACpB,CAAC,MACI;MACDA,KAAK,GAAGC,YAAY;IACxB;EACJ;EACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAI,OAAOA,KAAK,CAACM,QAAQ,KAAK,QAAQ,EAAE;MACpCN,KAAK,CAACE,GAAG,GAAGF,KAAK,CAACM,QAAQ;MAC1BN,KAAK,CAACI,MAAM,GAAGJ,KAAK,CAACM,QAAQ;IACjC;IACA,IAAI,OAAON,KAAK,CAACO,UAAU,KAAK,QAAQ,EAAE;MACtCP,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACO,UAAU;MAC9BP,KAAK,CAACK,IAAI,GAAGL,KAAK,CAACO,UAAU;IACjC;IACA,OAAO;MACHF,IAAI,EAAE,CAAC3G,EAAE,GAAGsG,KAAK,CAACK,IAAI,MAAM,IAAI,IAAI3G,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGuG,YAAY;MACrEC,GAAG,EAAE,CAACvG,EAAE,GAAGqG,KAAK,CAACE,GAAG,MAAM,IAAI,IAAIvG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGsG,YAAY;MACnEE,KAAK,EAAE,CAACvG,EAAE,GAAGoG,KAAK,CAACG,KAAK,MAAM,IAAI,IAAIvG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGqG,YAAY;MACvEG,MAAM,EAAE,CAACtG,EAAE,GAAGkG,KAAK,CAACI,MAAM,MAAM,IAAI,IAAItG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGmG;IACjE,CAAC;EACL;EACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC3BA,KAAK,GAAGC,YAAY;EACxB;EACA,OAAO;IAAEC,GAAG,EAAEF,KAAK;IAAEG,KAAK,EAAEH,KAAK;IAAEI,MAAM,EAAEJ,KAAK;IAAEK,IAAI,EAAEL;EAAM,CAAC;AACnE;AACA,SAASQ,qBAAqBA,CAAC5J,GAAG,EAAE2H,KAAK,EAAE;EACvC,IAAIkC,OAAO,GAAGV,YAAY,CAACxB,KAAK,CAACkB,QAAQ,CAACiB,MAAM,EAAE,CAAC,CAAC;EACpD,OAAO9J,GAAG,CAAC+E,QAAQ,CAAC,CAAC,CAACZ,KAAK,IAAI0F,OAAO,CAACJ,IAAI,GAAGI,OAAO,CAACN,KAAK,CAAC;AAChE;;AAEA;AACA;AACA;AACA,SAASQ,QAAQA,CAACC,cAAc,EAAEjD,OAAO,EAAE3G,WAAW,EAAE6J,KAAK,EAAEC,MAAM,EAAE;EACnE,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,aAAa,GAAG,EAAE,GAAG,EAAE;EAC3B,IAAIC,eAAe,GAAGC,UAAU,CAACvD,OAAO,EAAE,UAAUwD,IAAI,EAAE;IACtD,OAAOL,MAAM,CAACM,gBAAgB,CAACD,IAAI,CAAC,CAAC,iBAAiB,CAAC;EAC3D,CAAC,CAAC;EACF,IAAIF,eAAe,IAAI,IAAI,EACvBF,MAAM,CAAC1G,SAAS,GAAG4G,eAAe;EACtC,IAAI3I,SAAS,GAAG4I,UAAU,CAACvD,OAAO,EAAE,UAAUwD,IAAI,EAAE;IAChD,OAAOL,MAAM,CAACM,gBAAgB,CAACD,IAAI,CAAC,CAAC,OAAO,CAAC;EACjD,CAAC,CAAC;EACF,IAAI7I,SAAS,IAAI,IAAI,EACjByI,MAAM,CAACzI,SAAS,GAAGA,SAAS;EAChC,IAAI+I,OAAO,GAAGC,YAAY,CAACT,KAAK,EAAE7J,WAAW,CAAC;EAC9C,IAAIqK,OAAO,EACPN,MAAM,CAAC/C,WAAW,GAAGqD,OAAO;EAChC,IAAIE,eAAe,GAAG,gBAAgB;EACtC,IAAIC,gBAAgB,GAAGR,aAAa,GAAGhK,WAAW;EAClD,IAAIyK,GAAG,GAAGZ,KAAK,CAACa,cAAc;EAC9B,IAAIb,KAAK,CAACc,iBAAiB,KAAKF,GAAG,IAC/BZ,KAAK,CAACe,gBAAgB,KAAKH,GAAG,IAC9BZ,KAAK,CAACgB,eAAe,KAAKJ,GAAG,EAAE;IAC/B,IAAIK,WAAW,GAAG,CAACC,UAAU,CAACN,GAAG,CAAC,IAAI,CAAC,IAAID,gBAAgB;IAC3D,IAAIM,WAAW,EACXf,MAAM,CAACnI,SAAS,GAAGkJ,WAAW;EACtC,CAAC,MACI;IACDf,MAAM,CAACnI,SAAS,GAAG;MACfsH,GAAG,EAAE,CAAC6B,UAAU,CAAClB,KAAK,CAACa,cAAc,CAAC,IAAI,CAAC,IAAIF,gBAAgB;MAC/DrB,KAAK,EAAE,CAAC4B,UAAU,CAAClB,KAAK,CAACe,gBAAgB,CAAC,IAAI,CAAC,IAAIJ,gBAAgB;MACnEpB,MAAM,EAAE,CAAC2B,UAAU,CAAClB,KAAK,CAACc,iBAAiB,CAAC,IAAI,CAAC,IAAIH,gBAAgB;MACrEnB,IAAI,EAAE,CAAC0B,UAAU,CAAClB,KAAK,CAACgB,eAAe,CAAC,IAAI,CAAC,IAAIL;IACrD,CAAC;IACD;IACA;IACA,IAAI,CAACT,MAAM,CAACnI,SAAS,CAACsH,GAAG,EAAE;MACvB,IAAIa,MAAM,CAACnI,SAAS,CAACuH,KAAK,EAAE;QACxBoB,eAAe,GAAG,kBAAkB;MACxC,CAAC,MACI,IAAIR,MAAM,CAACnI,SAAS,CAACwH,MAAM,EAAE;QAC9BmB,eAAe,GAAG,mBAAmB;MACzC,CAAC,MACI,IAAIR,MAAM,CAACnI,SAAS,CAACyH,IAAI,EAAE;QAC5BkB,eAAe,GAAG,iBAAiB;MACvC;IACJ;EACJ;EACA,IAAIS,WAAW,GAAGd,UAAU,CAACvD,OAAO,EAAE,UAAUwD,IAAI,EAAE;IAClD,OAAOL,MAAM,CAACM,gBAAgB,CAACD,IAAI,CAAC,CAACI,eAAe,CAAC;EACzD,CAAC,CAAC;EACF,IAAIS,WAAW,IAAI,IAAI,EACnBjB,MAAM,CAACjI,SAAS,GAAGkJ,WAAW;EAClC,IAAIC,QAAQ,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;EACrD,IAAIA,QAAQ,CAAChI,OAAO,CAAC4G,KAAK,CAACqB,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;IAC1CnB,MAAM,CAACrJ,MAAM,GAAGmJ,KAAK,CAACqB,SAAS;EACnC;EACAD,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC;EACtC,IAAIA,QAAQ,CAAChI,OAAO,CAAC4G,KAAK,CAACsB,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9CpB,MAAM,CAACtJ,MAAM,GAAGoJ,KAAK,CAACsB,aAAa;EACvC;EACA,IAAIC,GAAG,GAAGC,QAAQ,CAACxB,KAAK,CAAC5J,QAAQ,IAAI,EAAE,CAAC;EACxC,IAAI,CAACqL,KAAK,CAACF,GAAG,CAAC,EACXrB,MAAM,CAAC9J,QAAQ,GAAGmL,GAAG,GAAGpB,aAAa;EACzC,IAAIxI,SAAS,GAAG+J,cAAc,CAAC1B,KAAK,CAAC;EACrC,IAAIrI,SAAS,EACTuI,MAAM,CAACvI,SAAS,GAAGA,SAAS;EAChC,IAAIE,IAAI,GAAG,CAACmI,KAAK,CAAC2B,UAAU,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;EACjD,IAAI7B,cAAc,CAAC3G,OAAO,CAACvB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IACrCqI,MAAM,CAACrI,IAAI,GAAGA,IAAI;EACtB;EACA,OAAOqI,MAAM;AACjB;AACA,SAASwB,cAAcA,CAAC1B,KAAK,EAAE;EAC3B,IAAIuB,GAAG,GAAG,EAAE;EACZ,IAAIvB,KAAK,CAAC6B,UAAU,KAAK,MAAM,IAC3B7B,KAAK,CAAC6B,UAAU,KAAK,QAAQ,IAC7BL,QAAQ,CAACxB,KAAK,CAAC6B,UAAU,CAAC,IAAI,GAAG,EAAE;IACnCN,GAAG,GAAG,MAAM;EAChB;EACA,IAAIvB,KAAK,CAACrI,SAAS,KAAK,QAAQ,IAAIqI,KAAK,CAACrI,SAAS,KAAK,SAAS,EAAE;IAC/D4J,GAAG,IAAI,QAAQ;EACnB;EACA,OAAOA,GAAG;AACd;AACA,SAASlB,UAAUA,CAACvD,OAAO,EAAEgF,WAAW,EAAE;EACtC,IAAIC,QAAQ,GAAGC,SAAS,CAAClF,OAAO,EAAEgF,WAAW,CAAC;EAC9C,IAAI,CAACC,QAAQ,EACT,OAAO,IAAI;EACf,IAAIE,IAAI,GAAGF,QAAQ,CAACG,KAAK,CAAC,wDAAwD,CAAC;EACnF,IAAI,CAACD,IAAI,IAAI,CAACzJ,KAAK,CAACC,OAAO,CAACwJ,IAAI,CAAC,EAAE;IAC/B,OAAO,IAAI;EACf;EACA,IAAI1I,KAAK,GAAG,CACRiI,QAAQ,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,EACjBT,QAAQ,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,EACjBT,QAAQ,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,CACpB;EACD,IAAIE,KAAK,GAAGX,QAAQ,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAIE,KAAK,KAAK,CAAC,IAAIV,KAAK,CAAClI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIkI,KAAK,CAAClI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIkI,KAAK,CAAClI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IACtE,OAAO,IAAI;EACf;EACA,OAAOA,KAAK;AAChB;AACA,SAASyI,SAASA,CAAC1B,IAAI,EAAEwB,WAAW,EAAE;EAClC,IAAIM,EAAE,GAAGN,WAAW,CAACxB,IAAI,CAAC;EAC1B,IAAI8B,EAAE,KAAK,kBAAkB,IACzBA,EAAE,KAAK,aAAa,IACpBA,EAAE,KAAK,SAAS,IAChBA,EAAE,KAAK,SAAS,EAAE;IAClB,IAAI9B,IAAI,CAAC+B,aAAa,IAAI,IAAI,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,OAAOL,SAAS,CAAC1B,IAAI,CAAC+B,aAAa,EAAEP,WAAW,CAAC;EACrD,CAAC,MACI;IACD,OAAOM,EAAE;EACb;AACJ;AACA,SAAS3B,YAAYA,CAACT,KAAK,EAAE7J,WAAW,EAAE;EACtC,IAAImM,GAAG,GAAG,CACNtC,KAAK,CAACuC,UAAU,EAChBvC,KAAK,CAACwC,YAAY,EAClBxC,KAAK,CAACyC,aAAa,EACnBzC,KAAK,CAAC0C,WAAW,CACpB;EACD,IAAIvC,aAAa,GAAG,EAAE,IAAI,EAAE,GAAGhK,WAAW,CAAC;EAC3C,IAAIwM,WAAW,GAAG,CAACnB,QAAQ,CAACxB,KAAK,CAACxJ,UAAU,CAAC,GAAGgL,QAAQ,CAACxB,KAAK,CAAC5J,QAAQ,CAAC,IAAID,WAAW,GAAG,CAAC;EAC3F,IAAIyM,YAAY,GAAGN,GAAG,CAAClE,GAAG,CAAC,UAAUyE,CAAC,EAAE;IACpC,OAAOrB,QAAQ,CAACqB,CAAC,IAAI,GAAG,CAAC,GAAG1C,aAAa;EAC7C,CAAC,CAAC;EACF,IAAIK,OAAO,GAAGtB,YAAY,CAAC0D,YAAY,EAAE,CAAC,CAAC;EAC3C,IAAID,WAAW,GAAGnC,OAAO,CAACnB,GAAG,EAAE;IAC3BmB,OAAO,CAACnB,GAAG,GAAGsD,WAAW;EAC7B;EACA,IAAIA,WAAW,GAAGnC,OAAO,CAACjB,MAAM,EAAE;IAC9BiB,OAAO,CAACjB,MAAM,GAAGoD,WAAW;EAChC;EACA,OAAOnC,OAAO;AAClB;AAEA,SAASsC,SAASA,CAAC/M,GAAG,EAAEgN,KAAK,EAAE9C,MAAM,EAAE+C,iBAAiB,EAAEC,MAAM,EAAE;EAC9D,IAAIpK,EAAE,EAAEC,EAAE;EACV,IAAIkK,iBAAiB,KAAK,KAAK,CAAC,EAAE;IAAEA,iBAAiB,GAAG,KAAK;EAAE;EAC/D,IAAIC,MAAM,KAAK,KAAK,CAAC,EAAE;IAAEA,MAAM,GAAG,KAAK;EAAE;EACzC,IAAIC,YAAY;EAChB,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;IAC3BG,YAAY,GAAGjD,MAAM,CAACkD,QAAQ,CAACC,aAAa,CAACL,KAAK,CAAC;EACvD,CAAC,MACI;IACDG,YAAY,GAAGH,KAAK;EACxB;EACA,IAAIhD,cAAc,GAAGtE,MAAM,CAAC4H,IAAI,CAACtN,GAAG,CAACoD,WAAW,CAAC,CAAC,CAAC;EACnD,IAAIhD,WAAW,GAAGJ,GAAG,CAACI,WAAW,CAAC,CAAC;EACnC,IAAIwH,IAAI,GAAG,EAAE;IAAEC,IAAI,GAAG,EAAE;IAAEC,IAAI,GAAG,EAAE;EACnC,IAAI,CAACqF,YAAY,EAAE;IACfI,OAAO,CAAChH,KAAK,CAAC,4CAA4C,EAAEyG,KAAK,CAAC;IAClE,OAAO;MAAEpF,IAAI,EAAEA,IAAI;MAAEC,IAAI,EAAEA,IAAI;MAAEC,IAAI,EAAEA;IAAK,CAAC;EACjD;EACA,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,YAAY,CAACM,IAAI,CAACzM,MAAM,EAAEwM,CAAC,EAAE,EAAE;IAC/C,IAAIzG,OAAO,GAAGoG,YAAY,CAACM,IAAI,CAACD,CAAC,CAAC;IAClC,IAAIE,OAAO,GAAG,CAAC3K,EAAE,GAAG,CAACD,EAAE,GAAGiE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuF,aAAa,MAAM,IAAI,IAAIxJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4K,OAAO,MAAM,IAAI,IAAI3K,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8I,WAAW,CAAC,CAAC;IACzM,IAAI8B,GAAG,GAAGC,eAAe,CAAC5D,cAAc,EAAE5J,WAAW,EAAE8J,MAAM,EAAEnD,OAAO,EAAEkG,iBAAiB,EAAEC,MAAM,CAAC;IAClG,IAAI,CAACS,GAAG,EACJ;IACJ,IAAID,OAAO,KAAK,OAAO,EAAE;MACrB9F,IAAI,CAACiG,IAAI,CAACF,GAAG,CAAC;IAClB,CAAC,MACI,IAAID,OAAO,KAAK,OAAO,EAAE;MAC1B5F,IAAI,CAAC+F,IAAI,CAACF,GAAG,CAAC;IAClB,CAAC,MACI;MACD;MACA9F,IAAI,CAACgG,IAAI,CAACF,GAAG,CAAC;IAClB;EACJ;EACA,OAAO;IAAE/F,IAAI,EAAEA,IAAI;IAAEC,IAAI,EAAEA,IAAI;IAAEC,IAAI,EAAEA;EAAK,CAAC;AACjD;AACA,SAAS8F,eAAeA,CAAC5D,cAAc,EAAE5J,WAAW,EAAE8J,MAAM,EAAEyD,GAAG,EAAEG,aAAa,EAAEZ,MAAM,EAAE;EACtF,IAAIa,SAAS,GAAG,IAAIlH,YAAY,CAAC8G,GAAG,CAAC;EACrC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,GAAG,CAACK,KAAK,CAAChN,MAAM,EAAEwM,CAAC,EAAE,EAAE;IACvC,IAAIS,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACR,CAAC,CAAC;IACvB,IAAIU,OAAO,GAAGhE,MAAM,CAACM,gBAAgB,CAACyD,IAAI,CAAC;IAC3C,IAAIH,aAAa,IAAII,OAAO,CAACC,OAAO,KAAK,MAAM,EAAE;MAC7C,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIlB,MAAM,EAAE;QACRkB,UAAU,GAAGrE,QAAQ,CAACC,cAAc,EAAEiE,IAAI,EAAE7N,WAAW,EAAE8N,OAAO,EAAEhE,MAAM,CAAC;MAC7E;MACA6D,SAAS,CAACF,IAAI,CAAC;QACXQ,OAAO,EAAEJ,IAAI,CAACI,OAAO;QACrBC,OAAO,EAAEL,IAAI,CAACK,OAAO;QACrBvO,MAAM,EAAEqO,UAAU;QAClBnH,QAAQ,EAAEgH,IAAI;QACdM,OAAO,EAAEC,gBAAgB,CAACP,IAAI;MAClC,CAAC,CAAC;IACN;EACJ;EACA,IAAIhE,KAAK,GAAGC,MAAM,CAACM,gBAAgB,CAACmD,GAAG,CAAC;EACxC,IAAII,SAAS,CAAC/M,MAAM,GAAG,CAAC,KAAK8M,aAAa,IAAI7D,KAAK,CAACkE,OAAO,KAAK,MAAM,CAAC,EAAE;IACrE,OAAOJ,SAAS;EACpB;AACJ;AACA,SAASS,gBAAgBA,CAACC,OAAO,EAAE;EAC/B;EACA,IAAIR,IAAI,GAAGQ,OAAO,CAACC,SAAS,CAAC,IAAI,CAAC;EAClC;EACA;EACAT,IAAI,CAACU,SAAS,GAAGV,IAAI,CAACU,SAAS,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACtE;EACAX,IAAI,CAACU,SAAS,GAAGV,IAAI,CAACU,SAAS,CAC1B5N,KAAK,CAAC,SAAS,CAAC,CAAC;EAAA,CACjBsH,GAAG,CAAC,UAAUwG,IAAI,EAAE;IAAE,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC;EAAE,CAAC,CAAC,CAC5CC,IAAI,CAAC,IAAI,CAAC;EACf;EACA,OAAOd,IAAI,CAACe,SAAS,IAAIf,IAAI,CAACgB,WAAW,IAAI,EAAE;AACnD;AAEA,SAASC,aAAaA,CAACC,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,EAAE;EAC9C,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG,CAACqM,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,CAAC,EAAEC,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;IACrE,IAAIC,OAAO,GAAGxM,EAAE,CAACuM,EAAE,CAAC;IACpB,IAAIC,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MACxC/B,OAAO,CAAChH,KAAK,CAAC,sDAAsD,GAAG,OAAO+I,OAAO,CAAC;IAC1F;IACA,IAAIA,OAAO,CAACC,MAAM,IAAI,OAAOD,OAAO,CAACC,MAAM,KAAK,QAAQ,EAAE;MACtDhC,OAAO,CAAChH,KAAK,CAAC,iCAAiC,EAAE+I,OAAO,CAACC,MAAM,CAAC;MAChE,OAAOD,OAAO,CAACC,MAAM;IACzB;EACJ;AACJ;;AAEA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACnC,IAAIJ,MAAM,IAAI,IAAI,EAAE;IAChB,MAAM,IAAIxJ,SAAS,CAAC,4CAA4C,CAAC;EACrE;EACA,IAAI6J,EAAE,GAAGpK,MAAM,CAAC+J,MAAM,CAAC;EACvB,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGC,SAAS,CAAChP,MAAM,EAAE+O,KAAK,EAAE,EAAE;IACnD;IACA,IAAIE,UAAU,GAAGD,SAAS,CAACD,KAAK,CAAC;IACjC,IAAIE,UAAU,IAAI,IAAI,EAAE;MACpB;MACA,KAAK,IAAIC,OAAO,IAAID,UAAU,EAAE;QAC5B;QACA,IAAIvK,MAAM,CAAC/C,SAAS,CAACmD,cAAc,CAACC,IAAI,CAACkK,UAAU,EAAEC,OAAO,CAAC,EAAE;UAC3DJ,EAAE,CAACI,OAAO,CAAC,GAAGD,UAAU,CAACC,OAAO,CAAC;QACrC;MACJ;IACJ;EACJ;EACA,OAAOJ,EAAE;AACb;AAEA,SAASK,UAAUA,CAAC3K,CAAC,EAAE4J,OAAO,EAAE;EAC5B,IAAIpP,GAAG,GAAG,IAAIuB,UAAU,CAACiE,CAAC,CAAC;EAC3B,IAAI4H,QAAQ,GAAGpN,GAAG,CAAC8E,kBAAkB,CAAC,CAAC;EACvC,IAAIqK,MAAM,GAAGnP,GAAG,CAAC6E,gBAAgB,CAAC,CAAC;EACnCqK,aAAa,CAACC,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,CAAC;EACxC,IAAIE,OAAO,GAAGE,MAAM,CAAC,CAAC,CAAC,EAAEL,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,CAAC;EACnD,IAAIgB,GAAG;EACP,IAAI,OAAOlG,MAAM,KAAK,WAAW,EAAE;IAC/BkG,GAAG,GAAGlG,MAAM;EAChB;EACA,IAAInK,MAAM,GAAGsQ,WAAW,CAAClB,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,CAAC;EACnD,IAAIkB,KAAK,GAAGC,UAAU,CAACpB,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,CAAC;EACjD,IAAIvG,QAAQ,GAAG2H,aAAa,CAACxQ,GAAG,EAAEsP,OAAO,CAAC;EAC1C,IAAIf,OAAO,GAAGkC,cAAc,CAACzQ,GAAG,EAAEsP,OAAO,EAAEc,GAAG,CAAC;EAC/C,OAAO;IAAEM,EAAE,EAAEtB,OAAO,CAACuB,OAAO;IAAEpC,OAAO,EAAEA,OAAO;IAAE+B,KAAK,EAAEA,KAAK;IAAEvQ,MAAM,EAAEA,MAAM;IAAE8I,QAAQ,EAAEA;EAAS,CAAC;AACtG;AACA,SAASwH,WAAWA,CAACO,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EACzC,IAAIC,YAAY,GAAG;IACfhR,MAAM,EAAE,CAAC,CAAC;IACViR,UAAU,EAAE,CAAC,CAAC;IACdC,UAAU,EAAE,CAAC,CAAC;IACdC,UAAU,EAAE,CAAC,CAAC;IACdC,kBAAkB,EAAE,CAAC,CAAC;IACtBC,YAAY,EAAE,CAAC;EACnB,CAAC;EACD,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAK,cAAc,EAAE;MACzB,IAAIC,QAAQ,GAAGX,MAAM,CAACU,IAAI,CAAC;MAC3B,IAAIE,UAAU,GAAGX,MAAM,CAACS,IAAI,CAAC;MAC7B,IAAIlC,OAAO,GAAG0B,MAAM,CAACQ,IAAI,CAAC;MAC1BP,YAAY,CAACK,YAAY,GAAG5B,MAAM,CAAC,CAAC,CAAC,EAAE+B,QAAQ,EAAEC,UAAU,EAAEpC,OAAO,CAAC;IACzE,CAAC,MACI;MACD,IAAIqC,UAAU,GAAG,CAACb,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC;MACzC,IAAI/Q,MAAM,GAAG0R,UAAU,CAACpJ,GAAG,CAAC,UAAUpE,IAAI,EAAE;QAAE,OAAOA,IAAI,CAACqN,IAAI,CAAC,IAAI,CAAC,CAAC;MAAE,CAAC,CAAC;MACzEP,YAAY,CAACO,IAAI,CAAC,GAAG9B,MAAM,CAAC,CAAC,CAAC,EAAEzP,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACpE;EACJ,CAAC;EACD,KAAK,IAAIsP,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG4C,MAAM,CAAC4H,IAAI,CAACyD,YAAY,CAAC,EAAE1B,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;IACnE,IAAIiC,IAAI,GAAGxO,EAAE,CAACuM,EAAE,CAAC;IACjBgC,OAAO,CAACC,IAAI,CAAC;EACjB;EACA,OAAOP,YAAY;AACvB;AACA,SAASR,UAAUA,CAACpB,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,EAAE;EAC3C,IAAIqC,UAAU,GAAG,CAACtC,MAAM,EAAE/B,QAAQ,EAAEgC,OAAO,CAAC;EAC5C,IAAIjF,MAAM,GAAG;IACTuH,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;EACjB,CAAC;EACD,KAAK,IAAIzC,EAAE,GAAG,CAAC,EAAE0C,YAAY,GAAGN,UAAU,EAAEpC,EAAE,GAAG0C,YAAY,CAAC/Q,MAAM,EAAEqO,EAAE,EAAE,EAAE;IACxE,IAAIC,OAAO,GAAGyC,YAAY,CAAC1C,EAAE,CAAC;IAC9B,IAAIC,OAAO,CAACoC,YAAY,EACpBvH,MAAM,CAACuH,YAAY,CAAC7D,IAAI,CAACyB,OAAO,CAACoC,YAAY,CAAC;IAClD,IAAIpC,OAAO,CAACqC,YAAY,EACpBxH,MAAM,CAACwH,YAAY,CAAC9D,IAAI,CAACyB,OAAO,CAACqC,YAAY,CAAC;IAClD,IAAIrC,OAAO,CAACsC,WAAW,EACnBzH,MAAM,CAACyH,WAAW,CAAC/D,IAAI,CAACyB,OAAO,CAACsC,WAAW,CAAC;IAChD,IAAItC,OAAO,CAACuC,YAAY,EACpB1H,MAAM,CAAC0H,YAAY,CAAChE,IAAI,CAACyB,OAAO,CAACuC,YAAY,CAAC;IAClD,IAAIvC,OAAO,CAACwC,WAAW,EACnB3H,MAAM,CAAC2H,WAAW,CAACjE,IAAI,CAACyB,OAAO,CAACwC,WAAW,CAAC;EACpD;EACA,OAAO3H,MAAM;AACjB;AACA,SAASqG,aAAaA,CAACxQ,GAAG,EAAEsP,OAAO,EAAE;EACjC,IAAIxM,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,EAAE,EAAE8O,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClD,IAAIzI,MAAM,GAAGX,YAAY,CAACmG,OAAO,CAACxF,MAAM,EAAE,EAAE,GAAG9J,GAAG,CAACI,WAAW,CAAC,CAAC,CAAC;EACjE,IAAImP,MAAM,GAAG,CAACzM,EAAE,GAAG0P,SAAS,CAACxS,GAAG,EAAEsP,OAAO,CAACC,MAAM,CAAC,MAAM,IAAI,IAAIzM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGgH,MAAM,CAACR,GAAG;EAC9F,IAAImJ,QAAQ;EACZ,IAAInD,OAAO,CAACmD,QAAQ,KAAK,IAAI,EAAE;IAC3BA,QAAQ,GAAG,WAAW;EAC1B,CAAC,MACI,IAAInD,OAAO,CAACmD,QAAQ,KAAK,KAAK,EAAE;IACjCA,QAAQ,GAAG,OAAO;EACtB,CAAC,MACI;IACDA,QAAQ,GAAG,CAAC1P,EAAE,GAAGuM,OAAO,CAACmD,QAAQ,MAAM,IAAI,IAAI1P,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,WAAW;EACnF;EACA,IAAI2P,QAAQ;EACZ,IAAIpD,OAAO,CAACoD,QAAQ,KAAK,IAAI,EAAE;IAC3BA,QAAQ,GAAG,WAAW;EAC1B,CAAC,MACI,IAAIpD,OAAO,CAACoD,QAAQ,KAAK,KAAK,EAAE;IACjCA,QAAQ,GAAG,OAAO;EACtB,CAAC,MACI;IACDA,QAAQ,GAAG,CAAC1P,EAAE,GAAGsM,OAAO,CAACoD,QAAQ,MAAM,IAAI,IAAI1P,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,WAAW;EACnF;EACA,IAAIkK,MAAM,GAAG,CAAChK,EAAE,GAAGoM,OAAO,CAACpC,MAAM,MAAM,IAAI,IAAIhK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;EACzE,IAAIyP,KAAK,GAAGrD,OAAO,CAACqD,KAAK,KAAKzF,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;EAC3D,IAAI0F,mBAAmB,GAAG,CAAC,CAACtD,OAAO,CAACsD,mBAAmB;EACvD,IAAIC,yBAAyB,GAAG,CAACb,EAAE,GAAG1C,OAAO,CAACuD,yBAAyB,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;EAC9G,OAAO;IACH/E,iBAAiB,EAAE,CAACgF,EAAE,GAAG3C,OAAO,CAACrC,iBAAiB,MAAM,IAAI,IAAIgF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;IAC1F/E,MAAM,EAAEA,MAAM;IACdyF,KAAK,EAAEA,KAAK;IACZpD,MAAM,EAAEA,MAAM;IACdzF,MAAM,EAAEA,MAAM;IACdgJ,SAAS,EAAE,CAACZ,EAAE,GAAG5C,OAAO,CAACwD,SAAS,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;IAC3Ea,YAAY,EAAE,CAACZ,EAAE,GAAG7C,OAAO,CAACyD,YAAY,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;IACjFa,UAAU,EAAE,CAACZ,EAAE,GAAG9C,OAAO,CAAC0D,UAAU,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;IAC7EM,QAAQ,EAAEA,QAAQ;IAClBD,QAAQ,EAAEA,QAAQ;IAClB3J,cAAc,EAAE,CAACuJ,EAAE,GAAG/C,OAAO,CAACxG,cAAc,MAAM,IAAI,IAAIuJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAChFtJ,cAAc,EAAE,CAACuJ,EAAE,GAAGhD,OAAO,CAACvG,cAAc,MAAM,IAAI,IAAIuJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;IAClFM,mBAAmB,EAAEA,mBAAmB;IACxCC,yBAAyB,EAAEA,yBAAyB;IACpDI,4BAA4B,EAAE,CAACV,EAAE,GAAGjD,OAAO,CAAC2D,4BAA4B,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;EAC/G,CAAC;AACL;AACA,SAASC,SAASA,CAACxS,GAAG,EAAEkT,UAAU,EAAE;EAChC,IAAIC,QAAQ,GAAGnT,GAAG,CAACsE,gBAAgB,CAAC,CAAC;EACrC,IAAI8O,EAAE,GAAGpT,GAAG,CAACI,WAAW,CAAC,CAAC;EAC1B,IAAIiT,WAAW,GAAGrT,GAAG,CAACmF,UAAU,CAAC,CAAC;EAClC,IAAImO,yBAAyB,GAAG,KAAK;EACrC,IAAIH,QAAQ,IAAIA,QAAQ,CAACI,eAAe,EAAE;IACtC,IAAIC,UAAU,GAAGL,QAAQ,CAACI,eAAe,GAAGJ,QAAQ,CAAChO,UAAU,GAAG,CAAC;IACnEmO,yBAAyB,GAAGE,UAAU,KAAKH,WAAW;EAC1D;EACA,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;IAChC,OAAOA,UAAU;EACrB,CAAC,MACI,IAAIA,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,KAAK,EAAE;IACjD,IAAII,yBAAyB,IAAI,CAACH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACM,MAAM,KAAK,IAAI,EAAE;MAC5G;MACA;MACA,OAAON,QAAQ,CAACM,MAAM,GAAG,EAAE,GAAGL,EAAE;IACpC;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAAS3C,cAAcA,CAACzQ,GAAG,EAAEsP,OAAO,EAAEpF,MAAM,EAAE;EAC1C,IAAItC,IAAI,GAAG0H,OAAO,CAAC1H,IAAI,IAAI,EAAE;EAC7B,IAAIC,IAAI,GAAGyH,OAAO,CAACzH,IAAI,IAAI,EAAE;EAC7B,IAAIC,IAAI,GAAGwH,OAAO,CAACxH,IAAI,IAAI,EAAE;EAC7B,IAAIwH,OAAO,CAACoE,IAAI,EAAE;IACd,IAAIC,MAAM,GAAGrE,OAAO,CAACrC,iBAAiB;IACtC,IAAI/C,MAAM,EAAE;MACR,IAAI0J,WAAW,GAAG7G,SAAS,CAAC/M,GAAG,EAAEsP,OAAO,CAACoE,IAAI,EAAExJ,MAAM,EAAEyJ,MAAM,EAAErE,OAAO,CAACpC,MAAM,CAAC,IAAI,CAAC,CAAC;MACpFtF,IAAI,GAAGgM,WAAW,CAAChM,IAAI,IAAIA,IAAI;MAC/BC,IAAI,GAAG+L,WAAW,CAAC/L,IAAI,IAAID,IAAI;MAC/BE,IAAI,GAAG8L,WAAW,CAAC9L,IAAI,IAAIF,IAAI;IACnC,CAAC,MACI;MACD2F,OAAO,CAAChH,KAAK,CAAC,8CAA8C,CAAC;IACjE;EACJ;EACA,IAAIsN,OAAO,GAAGvE,OAAO,CAACuE,OAAO,IAAIC,YAAY,CAAClM,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAC/D,OAAO;IAAE+L,OAAO,EAAEA,OAAO;IAAEjM,IAAI,EAAEA,IAAI;IAAEC,IAAI,EAAEA,IAAI;IAAEC,IAAI,EAAEA;EAAK,CAAC;AACnE;AACA,SAASgM,YAAYA,CAAClM,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACpC,IAAIiM,QAAQ,GAAGnM,IAAI,CAAC,CAAC,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;EAClD,IAAIqC,MAAM,GAAG,EAAE;EACfzE,MAAM,CAAC4H,IAAI,CAACyG,QAAQ,CAAC,CAChBC,MAAM,CAAC,UAAUC,GAAG,EAAE;IAAE,OAAOA,GAAG,KAAK,UAAU;EAAE,CAAC,CAAC,CACrDC,OAAO,CAAC,UAAUD,GAAG,EAAE;IACxB,IAAI3F,OAAO,GAAG,CAAC;IACf,IAAItB,KAAK;IACT,IAAIvK,KAAK,CAACC,OAAO,CAACqR,QAAQ,CAAC,EAAE;MACzB/G,KAAK,GAAG+G,QAAQ,CAACtI,QAAQ,CAACwI,GAAG,CAAC,CAAC;IACnC,CAAC,MACI;MACDjH,KAAK,GAAG+G,QAAQ,CAACE,GAAG,CAAC;IACzB;IACA,IAAI,OAAOjH,KAAK,KAAK,QAAQ,IAAI,CAACvK,KAAK,CAACC,OAAO,CAACsK,KAAK,CAAC,EAAE;MACpDsB,OAAO,GAAG,CAACtB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACsB,OAAO,KAAK,CAAC;IAChF;IACA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,OAAO,EAAEd,CAAC,EAAE,EAAE;MAC9B,IAAIkD,EAAE,GAAG,KAAK,CAAC;MACf,IAAIjO,KAAK,CAACC,OAAO,CAACqR,QAAQ,CAAC,EAAE;QACzBrD,EAAE,GAAGvG,MAAM,CAACnJ,MAAM;MACtB,CAAC,MACI;QACD0P,EAAE,GAAGuD,GAAG,IAAIzG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC2G,MAAM,CAAC3G,CAAC,CAAC,GAAG,EAAE,CAAC;MAC3C;MACA,IAAI4G,SAAS,GAAG;QAAEC,OAAO,EAAE3D;MAAG,CAAC;MAC/BvG,MAAM,CAAC0D,IAAI,CAACuG,SAAS,CAAC;IAC1B;EACJ,CAAC,CAAC;EACF,OAAOjK,MAAM;AACjB;AAEA,IAAImK,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACtU,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAE;IAClC,IAAI,CAACjB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACxC,UAAU,GAAGwC,KAAK,CAACxC,UAAU;IAClC,IAAI,CAAC0D,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IAC9B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC5I,GAAG,GAAGA,GAAG,CAACyE,WAAW,CAAC,CAAC;EAChC;EACA,OAAO6P,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,IAAIC,YAAY,GAAG,aAAe,UAAUzN,MAAM,EAAE;EAChDd,SAAS,CAACuO,YAAY,EAAEzN,MAAM,CAAC;EAC/B,SAASyN,YAAYA,CAACvU,GAAG,EAAE2H,KAAK,EAAEsG,IAAI,EAAEN,GAAG,EAAE6G,MAAM,EAAE5L,MAAM,EAAE;IACzD,IAAI5B,KAAK,GAAGF,MAAM,CAACf,IAAI,CAAC,IAAI,EAAE/F,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,CAAC,IAAI,IAAI;IACzD5B,KAAK,CAACiH,IAAI,GAAGA,IAAI;IACjBjH,KAAK,CAAC2G,GAAG,GAAGA,GAAG;IACf3G,KAAK,CAACwN,MAAM,GAAGA,MAAM;IACrBxN,KAAK,CAACyN,OAAO,GAAG9G,GAAG,CAAC8G,OAAO;IAC3B,OAAOzN,KAAK;EAChB;EACA,OAAOuN,YAAY;AACvB,CAAC,CAACD,QAAQ,CAAE;AAEZ,IAAII,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAAC1H,KAAK,EAAEuB,OAAO,EAAE;IAC3B,IAAI,CAACpJ,UAAU,GAAG,CAAC;IACnB,IAAI,CAACuL,EAAE,GAAG1D,KAAK,CAAC0D,EAAE;IAClB,IAAI,CAAC7H,QAAQ,GAAGmE,KAAK,CAACnE,QAAQ;IAC9B,IAAI,CAAC9I,MAAM,GAAGiN,KAAK,CAACjN,MAAM;IAC1B,IAAI,CAACuQ,KAAK,GAAGtD,KAAK,CAACsD,KAAK;IACxB,IAAI,CAACuD,OAAO,GAAGtF,OAAO,CAACsF,OAAO;IAC9B,IAAI,CAACjM,IAAI,GAAG2G,OAAO,CAAC3G,IAAI;IACxB,IAAI,CAACC,IAAI,GAAG0G,OAAO,CAAC1G,IAAI;IACxB,IAAI,CAACC,IAAI,GAAGyG,OAAO,CAACzG,IAAI;EAC5B;EACA4M,KAAK,CAAC/R,SAAS,CAACgS,aAAa,GAAG,UAAUd,OAAO,EAAE;IAC/C,OAAO,IAAI,CAACjM,IAAI,CAACU,MAAM,CAAC,UAAUsM,GAAG,EAAEjH,GAAG,EAAE;MAAE,OAAOiH,GAAG,GAAGjH,GAAG,CAACkH,gBAAgB,CAAChB,OAAO,CAAC;IAAE,CAAC,EAAE,CAAC,CAAC;EACnG,CAAC;EACDa,KAAK,CAAC/R,SAAS,CAACmS,aAAa,GAAG,UAAUjB,OAAO,EAAE;IAC/C,OAAO,IAAI,CAAC/L,IAAI,CAACQ,MAAM,CAAC,UAAUsM,GAAG,EAAEjH,GAAG,EAAE;MAAE,OAAOiH,GAAG,GAAGjH,GAAG,CAACkH,gBAAgB,CAAChB,OAAO,CAAC;IAAE,CAAC,EAAE,CAAC,CAAC;EACnG,CAAC;EACDa,KAAK,CAAC/R,SAAS,CAACoS,OAAO,GAAG,YAAY;IAClC,OAAO,IAAI,CAACnN,IAAI,CAACuM,MAAM,CAAC,IAAI,CAACtM,IAAI,CAAC,CAACsM,MAAM,CAAC,IAAI,CAACrM,IAAI,CAAC;EACxD,CAAC;EACD4M,KAAK,CAAC/R,SAAS,CAACqS,aAAa,GAAG,UAAUhV,GAAG,EAAEiV,QAAQ,EAAEhH,IAAI,EAAEN,GAAG,EAAE6G,MAAM,EAAE5L,MAAM,EAAE;IAChF,KAAK,IAAIyG,EAAE,GAAG,CAAC,EAAE6F,UAAU,GAAGD,QAAQ,EAAE5F,EAAE,GAAG6F,UAAU,CAAClU,MAAM,EAAEqO,EAAE,EAAE,EAAE;MAClE,IAAI8F,OAAO,GAAGD,UAAU,CAAC7F,EAAE,CAAC;MAC5B,IAAI+F,IAAI,GAAG,IAAIb,YAAY,CAACvU,GAAG,EAAE,IAAI,EAAEiO,IAAI,EAAEN,GAAG,EAAE6G,MAAM,EAAE5L,MAAM,CAAC;MACjE,IAAIuB,MAAM,GAAGgL,OAAO,CAACC,IAAI,CAAC,KAAK,KAAK;MACpC;MACAnH,IAAI,CAACrO,IAAI,GAAG6C,KAAK,CAACC,OAAO,CAACuL,IAAI,CAACrO,IAAI,CAAC,GAAGqO,IAAI,CAACrO,IAAI,GAAG,CAACqO,IAAI,CAACrO,IAAI,CAAC;MAC9D,IAAIuK,MAAM,EAAE;QACR,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDuK,KAAK,CAAC/R,SAAS,CAAC0S,gBAAgB,GAAG,UAAUrV,GAAG,EAAE4I,MAAM,EAAE;IACtD5I,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;IAC/B,KAAK,IAAI4N,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG,IAAI,CAACwN,KAAK,CAACwB,WAAW,EAAEzC,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;MAChE,IAAI8F,OAAO,GAAGrS,EAAE,CAACuM,EAAE,CAAC;MACpB8F,OAAO,CAAC,IAAIb,QAAQ,CAACtU,GAAG,EAAE,IAAI,EAAE4I,MAAM,CAAC,CAAC;IAC5C;EACJ,CAAC;EACD8L,KAAK,CAAC/R,SAAS,CAAC2S,qBAAqB,GAAG,UAAUtV,GAAG,EAAE4I,MAAM,EAAE;IAC3D,KAAK,IAAIyG,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG,IAAI,CAACwN,KAAK,CAACuB,YAAY,EAAExC,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;MACjE,IAAI8F,OAAO,GAAGrS,EAAE,CAACuM,EAAE,CAAC;MACpB8F,OAAO,CAAC,IAAIb,QAAQ,CAACtU,GAAG,EAAE,IAAI,EAAE4I,MAAM,CAAC,CAAC;IAC5C;EACJ,CAAC;EACD8L,KAAK,CAAC/R,SAAS,CAACqC,QAAQ,GAAG,UAAUuQ,SAAS,EAAE;IAC5C,IAAI,OAAO,IAAI,CAAC1M,QAAQ,CAACmK,UAAU,KAAK,QAAQ,EAAE;MAC9C,OAAO,IAAI,CAACnK,QAAQ,CAACmK,UAAU;IACnC,CAAC,MACI,IAAI,IAAI,CAACnK,QAAQ,CAACmK,UAAU,KAAK,MAAM,EAAE;MAC1C,IAAIwC,YAAY,GAAG,IAAI,CAAC3B,OAAO,CAACvL,MAAM,CAAC,UAAUmN,KAAK,EAAEC,GAAG,EAAE;QAAE,OAAOD,KAAK,GAAGC,GAAG,CAACF,YAAY;MAAE,CAAC,EAAE,CAAC,CAAC;MACrG,OAAOA,YAAY;IACvB,CAAC,MACI;MACD,IAAI1L,MAAM,GAAG,IAAI,CAACjB,QAAQ,CAACiB,MAAM;MACjC,OAAOyL,SAAS,GAAGzL,MAAM,CAACL,IAAI,GAAGK,MAAM,CAACP,KAAK;IACjD;EACJ,CAAC;EACD,OAAOmL,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,IAAIiB,GAAG,GAAG,aAAe,YAAY;EACjC,SAASA,GAAGA,CAACC,GAAG,EAAE7F,KAAK,EAAE0E,OAAO,EAAEzG,KAAK,EAAE6H,kBAAkB,EAAE;IACzD,IAAIA,kBAAkB,KAAK,KAAK,CAAC,EAAE;MAAEA,kBAAkB,GAAG,KAAK;IAAE;IACjE,IAAI,CAACzR,MAAM,GAAG,CAAC;IACf,IAAI,CAACwR,GAAG,GAAGA,GAAG;IACd,IAAIA,GAAG,YAAY/O,YAAY,EAAE;MAC7B,IAAI,CAAC+O,GAAG,GAAGA,GAAG,CAAC3O,QAAQ;MACvB,IAAI,CAACF,OAAO,GAAG6O,GAAG,CAAC3O,QAAQ;IAC/B;IACA,IAAI,CAAC8I,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC0E,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC6H,kBAAkB,GAAGA,kBAAkB;EAChD;EACAF,GAAG,CAAChT,SAAS,CAACkS,gBAAgB,GAAG,UAAUhB,OAAO,EAAE;IAChD,IAAI7M,KAAK,GAAG,IAAI;IAChB,OAAO6M,OAAO,CAACvL,MAAM,CAAC,UAAUsM,GAAG,EAAEJ,MAAM,EAAE;MAAE,IAAI1R,EAAE;MAAE,OAAO0F,IAAI,CAACC,GAAG,CAACmM,GAAG,EAAE,CAAC,CAAC9R,EAAE,GAAGkE,KAAK,CAACgH,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC,MAAM,IAAI,IAAIjN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,MAAM,KAAK,CAAC,CAAC;IAAE,CAAC,EAAE,CAAC,CAAC;EAC9K,CAAC;EACDuR,GAAG,CAAChT,SAAS,CAACmT,UAAU,GAAG,UAAUjC,OAAO,EAAE;IAC1C,IAAI7M,KAAK,GAAG,IAAI;IAChB,OAAQ6M,OAAO,CAACG,MAAM,CAAC,UAAUQ,MAAM,EAAE;MACrC,IAAIvG,IAAI,GAAGjH,KAAK,CAACgH,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;MACpC,IAAI,CAAC9B,IAAI,EACL,OAAO,KAAK;MAChB,OAAOA,IAAI,CAACI,OAAO,GAAG,CAAC;IAC3B,CAAC,CAAC,CAACrN,MAAM,GAAG,CAAC;EACjB,CAAC;EACD2U,GAAG,CAAChT,SAAS,CAACoT,eAAe,GAAG,UAAU3R,MAAM,EAAEyP,OAAO,EAAE;IACvD,OAAO,IAAI,CAACgB,gBAAgB,CAAChB,OAAO,CAAC,IAAIzP,MAAM;EACnD,CAAC;EACDuR,GAAG,CAAChT,SAAS,CAACqT,mBAAmB,GAAG,UAAUnC,OAAO,EAAE7T,GAAG,EAAE;IACxD,IAAIgH,KAAK,GAAG,IAAI;IAChB,OAAO6M,OAAO,CAACvL,MAAM,CAAC,UAAUsM,GAAG,EAAEJ,MAAM,EAAE;MACzC,IAAIvG,IAAI,GAAGjH,KAAK,CAACgH,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;MACpC,IAAI,CAAC9B,IAAI,EACL,OAAO,CAAC;MACZ,IAAIxN,UAAU,GAAGT,GAAG,CAACkF,aAAa,CAAC+I,IAAI,CAAClO,MAAM,CAACM,QAAQ,CAAC;MACxD,IAAI4V,QAAQ,GAAGhI,IAAI,CAACxD,OAAO,CAAC,UAAU,CAAC;MACvC,IAAIyL,YAAY,GAAGD,QAAQ,GAAGxV,UAAU;MACxC,OAAOyV,YAAY,GAAGtB,GAAG,GAAGsB,YAAY,GAAGtB,GAAG;IAClD,CAAC,EAAE,CAAC,CAAC;EACT,CAAC;EACD,OAAOe,GAAG;AACd,CAAC,CAAC,CAAE;AACJ,IAAIQ,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAACP,GAAG,EAAE7V,MAAM,EAAE0U,OAAO,EAAE;IAChC,IAAI3R,EAAE;IACN,IAAI,CAACsT,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACb,YAAY,GAAG,CAAC;IACrB,IAAI,CAACc,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACpS,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACvE,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC0U,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmB,GAAG,GAAGA,GAAG;IACd,IAAIrH,OAAO,GAAGqH,GAAG;IACjB,IAAIA,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACnT,KAAK,CAACC,OAAO,CAACkT,GAAG,CAAC,EAAE;MAC/D,IAAI,CAACvH,OAAO,GAAGuH,GAAG,CAACvH,OAAO,IAAI,CAAC;MAC/B,IAAI,CAACC,OAAO,GAAGsH,GAAG,CAACtH,OAAO,IAAI,CAAC;MAC/BC,OAAO,GAAG,CAACzL,EAAE,GAAG8S,GAAG,CAACrH,OAAO,MAAM,IAAI,IAAIzL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG8S,GAAG;MACjE,IAAIA,GAAG,CAAC3O,QAAQ,EAAE;QACd,IAAI,CAAC2O,GAAG,GAAGA,GAAG,CAAC3O,QAAQ;MAC3B;IACJ,CAAC,MACI;MACD,IAAI,CAACoH,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,OAAO,GAAG,CAAC;IACpB;IACA;IACA,IAAI1O,IAAI,GAAG2O,OAAO,IAAI,IAAI,GAAG,EAAE,GAAGA,OAAO,GAAG,EAAE;IAC9C,IAAI7N,UAAU,GAAG,aAAa;IAC9B,IAAI,CAACd,IAAI,GAAGA,IAAI,CAACmB,KAAK,CAACL,UAAU,CAAC;EACtC;EACAyV,IAAI,CAACxT,SAAS,CAAC6T,UAAU,GAAG,YAAY;IACpC,IAAI1W,CAAC;IACL,IAAI,IAAI,CAACC,MAAM,CAACc,MAAM,KAAK,KAAK,EAAE;MAC9Bf,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAAC2K,OAAO,CAAC,KAAK,CAAC;IACpC,CAAC,MACI,IAAI,IAAI,CAAC1K,MAAM,CAACc,MAAM,KAAK,QAAQ,EAAE;MACtCf,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACsE,MAAM,GAAG,IAAI,CAACqG,OAAO,CAAC,QAAQ,CAAC;IACrD,CAAC,MACI;MACD,IAAIgM,SAAS,GAAG,IAAI,CAACrS,MAAM,GAAG,IAAI,CAACqG,OAAO,CAAC,UAAU,CAAC;MACtD3K,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG2W,SAAS,GAAG,CAAC,GAAG,IAAI,CAAChM,OAAO,CAAC,KAAK,CAAC;IACpD;IACA,IAAI5K,CAAC;IACL,IAAI,IAAI,CAACE,MAAM,CAACe,MAAM,KAAK,OAAO,EAAE;MAChCjB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACsE,KAAK,GAAG,IAAI,CAACsG,OAAO,CAAC,OAAO,CAAC;IACnD,CAAC,MACI,IAAI,IAAI,CAAC1K,MAAM,CAACe,MAAM,KAAK,QAAQ,EAAE;MACtC,IAAI4V,QAAQ,GAAG,IAAI,CAACvS,KAAK,GAAG,IAAI,CAACsG,OAAO,CAAC,YAAY,CAAC;MACtD5K,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG6W,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACjM,OAAO,CAAC,MAAM,CAAC;IACpD,CAAC,MACI;MACD5K,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAAC4K,OAAO,CAAC,MAAM,CAAC;IACrC;IACA,OAAO;MAAE5K,CAAC,EAAEA,CAAC;MAAEC,CAAC,EAAEA;IAAE,CAAC;EACzB,CAAC;EACD;EACAqW,IAAI,CAACxT,SAAS,CAACgU,gBAAgB,GAAG,UAAUvW,WAAW,EAAEG,gBAAgB,EAAE;IACvE,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;MAAEA,gBAAgB,GAAG,IAAI;IAAE;IAC5D,IAAIK,SAAS,GAAG6B,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9C,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,CAACoB,MAAM,GAAG,CAAC;IAC/D,IAAIP,UAAU,GAAI,IAAI,CAACV,MAAM,CAACM,QAAQ,GAAGD,WAAW,GAAIG,gBAAgB;IACxE,IAAI6D,MAAM,GAAGxD,SAAS,GAAGH,UAAU,GAAG,IAAI,CAACgK,OAAO,CAAC,UAAU,CAAC;IAC9D,OAAOjC,IAAI,CAACC,GAAG,CAACrE,MAAM,EAAE,IAAI,CAACrE,MAAM,CAACuH,aAAa,CAAC;EACtD,CAAC;EACD6O,IAAI,CAACxT,SAAS,CAAC8H,OAAO,GAAG,UAAU7D,IAAI,EAAE;IACrC,IAAI6D,OAAO,GAAGtB,YAAY,CAAC,IAAI,CAACpJ,MAAM,CAACqH,WAAW,EAAE,CAAC,CAAC;IACtD,IAAIR,IAAI,KAAK,UAAU,EAAE;MACrB,OAAO6D,OAAO,CAACnB,GAAG,GAAGmB,OAAO,CAACjB,MAAM;IACvC,CAAC,MACI,IAAI5C,IAAI,KAAK,YAAY,EAAE;MAC5B,OAAO6D,OAAO,CAAChB,IAAI,GAAGgB,OAAO,CAAClB,KAAK;IACvC,CAAC,MACI;MACD,OAAOkB,OAAO,CAAC7D,IAAI,CAAC;IACxB;EACJ,CAAC;EACD,OAAOuP,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,IAAIS,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAACvC,OAAO,EAAEuB,GAAG,EAAE7F,KAAK,EAAE;IACjC,IAAI,CAACyF,YAAY,GAAG,CAAC;IACrB,IAAI,CAACc,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACpS,KAAK,GAAG,CAAC;IACd,IAAI,CAACkQ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuB,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC7F,KAAK,GAAGA,KAAK;EACtB;EACA6G,MAAM,CAACjU,SAAS,CAACkU,qBAAqB,GAAG,UAAUlP,KAAK,EAAE;IACtD,IAAIc,GAAG,GAAG,CAAC;IACX,KAAK,IAAI4G,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG6E,KAAK,CAACoN,OAAO,CAAC,CAAC,EAAE1F,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;MACzD,IAAI1B,GAAG,GAAG7K,EAAE,CAACuM,EAAE,CAAC;MAChB,IAAIpB,IAAI,GAAGN,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC+B,KAAK,CAAC;MAChC,IAAI9B,IAAI,IAAI,OAAOA,IAAI,CAAClO,MAAM,CAACsH,SAAS,KAAK,QAAQ,EAAE;QACnDoB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAEwF,IAAI,CAAClO,MAAM,CAACsH,SAAS,CAAC;MAC9C;IACJ;IACA,OAAOoB,GAAG;EACd,CAAC;EACD,OAAOmO,MAAM;AACjB,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA;AACA,SAASE,eAAeA,CAAC9W,GAAG,EAAE2H,KAAK,EAAE;EACjCoP,SAAS,CAAC/W,GAAG,EAAE2H,KAAK,CAAC;EACrB,IAAIqP,gBAAgB,GAAG,EAAE;EACzB,IAAIC,iBAAiB,GAAG,CAAC;EACzBtP,KAAK,CAACkM,OAAO,CAACK,OAAO,CAAC,UAAUM,MAAM,EAAE;IACpC,IAAI0C,WAAW,GAAG1C,MAAM,CAACqC,qBAAqB,CAAClP,KAAK,CAAC;IACrD,IAAIuP,WAAW,EAAE;MACb;MACA1C,MAAM,CAACrQ,KAAK,GAAG+S,WAAW;IAC9B,CAAC,MACI;MACD;MACA1C,MAAM,CAACrQ,KAAK,GAAGqQ,MAAM,CAACgB,YAAY;MAClCwB,gBAAgB,CAACnJ,IAAI,CAAC2G,MAAM,CAAC;IACjC;IACAyC,iBAAiB,IAAIzC,MAAM,CAACrQ,KAAK;EACrC,CAAC,CAAC;EACF;EACA,IAAIgT,WAAW,GAAGxP,KAAK,CAAC3C,QAAQ,CAAChF,GAAG,CAAC+E,QAAQ,CAAC,CAAC,CAACZ,KAAK,CAAC,GAAG8S,iBAAiB;EAC1E;EACA,IAAIE,WAAW,EAAE;IACbA,WAAW,GAAGC,aAAa,CAACJ,gBAAgB,EAAEG,WAAW,EAAE,UAAU3C,MAAM,EAAE;MACzE,OAAOhM,IAAI,CAACC,GAAG,CAAC+L,MAAM,CAAC8B,gBAAgB,EAAE9B,MAAM,CAAC+B,QAAQ,CAAC;IAC7D,CAAC,CAAC;EACN;EACA;EACA,IAAIY,WAAW,EAAE;IACbA,WAAW,GAAGC,aAAa,CAACJ,gBAAgB,EAAEG,WAAW,EAAE,UAAU3C,MAAM,EAAE;MAAE,OAAOA,MAAM,CAAC+B,QAAQ;IAAE,CAAC,CAAC;EAC7G;EACAY,WAAW,GAAG3O,IAAI,CAAC6O,GAAG,CAACF,WAAW,CAAC;EACnC,IAAI,CAACxP,KAAK,CAACkB,QAAQ,CAAC+J,mBAAmB,IACnCuE,WAAW,GAAG,GAAG,GAAGnX,GAAG,CAACI,WAAW,CAAC,CAAC,EAAE;IACvC;IACA;IACA;IACA;IACA+W,WAAW,GAAGA,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG3O,IAAI,CAAC8O,KAAK,CAACH,WAAW,CAAC;IACrE5J,OAAO,CAACgK,IAAI,CAAC,wBAAwB,CAACpD,MAAM,CAACgD,WAAW,EAAE,iCAAiC,CAAC,CAAC;EACjG;EACAK,aAAa,CAAC7P,KAAK,CAAC;EACpB8P,UAAU,CAAC9P,KAAK,EAAE3H,GAAG,CAAC;EACtB0X,aAAa,CAAC/P,KAAK,CAAC;AACxB;AACA,SAASoP,SAASA,CAAC/W,GAAG,EAAE2H,KAAK,EAAE;EAC3B,IAAIyL,EAAE,GAAGpT,GAAG,CAACI,WAAW,CAAC,CAAC;EAC1B,IAAIwS,mBAAmB,GAAGjL,KAAK,CAACkB,QAAQ,CAAC+J,mBAAmB;EAC5D,IAAI+E,kBAAkB,GAAG/N,qBAAqB,CAAC5J,GAAG,EAAE2H,KAAK,CAAC;EAC1DA,KAAK,CAACoN,OAAO,CAAC,CAAC,CAACb,OAAO,CAAC,UAAUvG,GAAG,EAAE;IACnC,KAAK,IAAI0B,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG6E,KAAK,CAACkM,OAAO,EAAExE,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;MACvD,IAAImF,MAAM,GAAG1R,EAAE,CAACuM,EAAE,CAAC;MACnB,IAAIpB,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;MAClC,IAAI,CAAC9B,IAAI,EACL;MACJ,IAAIqC,KAAK,GAAG3I,KAAK,CAAC2I,KAAK,CAACoB,YAAY;MACpC/J,KAAK,CAACqN,aAAa,CAAChV,GAAG,EAAEsQ,KAAK,EAAErC,IAAI,EAAEN,GAAG,EAAE6G,MAAM,EAAE,IAAI,CAAC;MACxD,IAAI/J,OAAO,GAAGwD,IAAI,CAACxD,OAAO,CAAC,YAAY,CAAC;MACxCwD,IAAI,CAACoI,YAAY,GAAGnO,cAAc,CAAC+F,IAAI,CAACrO,IAAI,EAAEqO,IAAI,CAAClO,MAAM,EAAEC,GAAG,CAAC,GAAGyK,OAAO;MACzE;MACA;MACA;MACA;MACA,IAAImN,gBAAgB,GAAG1P,cAAc,CAAC+F,IAAI,CAACrO,IAAI,CAACmP,IAAI,CAAC,GAAG,CAAC,CAAChO,KAAK,CAAC,cAAc,CAAC,EAAEkN,IAAI,CAAClO,MAAM,EAAEC,GAAG,CAAC;MAClGiO,IAAI,CAACqI,gBAAgB,GAAGsB,gBAAgB,GAAG3J,IAAI,CAACxD,OAAO,CAAC,YAAY,CAAC;MACrE,IAAI,OAAOwD,IAAI,CAAClO,MAAM,CAACsH,SAAS,KAAK,QAAQ,EAAE;QAC3C4G,IAAI,CAACsI,QAAQ,GAAGtI,IAAI,CAAClO,MAAM,CAACsH,SAAS;QACrC4G,IAAI,CAACuH,YAAY,GAAGvH,IAAI,CAAClO,MAAM,CAACsH,SAAS;MAC7C,CAAC,MACI,IAAI4G,IAAI,CAAClO,MAAM,CAACsH,SAAS,KAAK,MAAM,IACrCuL,mBAAmB,KAAK,IAAI,EAAE;QAC9B;QACA,IAAI3E,IAAI,CAACoI,YAAY,GAAGsB,kBAAkB,EAAE;UACxC1J,IAAI,CAACsI,QAAQ,GAAGoB,kBAAkB;UAClC1J,IAAI,CAACuH,YAAY,GAAGmC,kBAAkB;QAC1C,CAAC,MACI;UACD1J,IAAI,CAACsI,QAAQ,GAAGtI,IAAI,CAACoI,YAAY;UACjCpI,IAAI,CAACuH,YAAY,GAAGvH,IAAI,CAACoI,YAAY;QACzC;MACJ,CAAC,MACI;QACD;QACA,IAAIwB,eAAe,GAAG,EAAE,GAAGzE,EAAE;QAC7BnF,IAAI,CAACsI,QAAQ,GAAGtI,IAAI,CAAClO,MAAM,CAACwH,YAAY,IAAIsQ,eAAe;QAC3D5J,IAAI,CAACuH,YAAY,GAAGvH,IAAI,CAACoI,YAAY;QACrC,IAAIpI,IAAI,CAACsI,QAAQ,GAAGtI,IAAI,CAACuH,YAAY,EAAE;UACnCvH,IAAI,CAACuH,YAAY,GAAGvH,IAAI,CAACsI,QAAQ;QACrC;MACJ;IACJ;EACJ,CAAC,CAAC;EACF5O,KAAK,CAACoN,OAAO,CAAC,CAAC,CAACb,OAAO,CAAC,UAAUvG,GAAG,EAAE;IACnC,KAAK,IAAI0B,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG6E,KAAK,CAACkM,OAAO,EAAExE,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;MACvD,IAAImF,MAAM,GAAG1R,EAAE,CAACuM,EAAE,CAAC;MACnB,IAAIpB,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;MAClC;MACA;MACA,IAAI9B,IAAI,IAAIA,IAAI,CAACK,OAAO,KAAK,CAAC,EAAE;QAC5BkG,MAAM,CAACgB,YAAY,GAAGhN,IAAI,CAACC,GAAG,CAAC+L,MAAM,CAACgB,YAAY,EAAEvH,IAAI,CAACuH,YAAY,CAAC;QACtEhB,MAAM,CAAC+B,QAAQ,GAAG/N,IAAI,CAACC,GAAG,CAAC+L,MAAM,CAAC+B,QAAQ,EAAEtI,IAAI,CAACsI,QAAQ,CAAC;QAC1D/B,MAAM,CAAC8B,gBAAgB,GAAG9N,IAAI,CAACC,GAAG,CAAC+L,MAAM,CAAC8B,gBAAgB,EAAErI,IAAI,CAACqI,gBAAgB,CAAC;MACtF,CAAC,MACI;QACD;QACA;QACA;QACA;QACA;QACA;QACA,IAAIlF,YAAY,GAAGzJ,KAAK,CAAC5H,MAAM,CAACqR,YAAY,CAACoD,MAAM,CAACH,OAAO,CAAC,IACxD1M,KAAK,CAAC5H,MAAM,CAACqR,YAAY,CAACoD,MAAM,CAACzE,KAAK,CAAC,IACvC,CAAC,CAAC;QACN,IAAI1I,SAAS,GAAG+J,YAAY,CAAC/J,SAAS,IAAI+J,YAAY,CAAC7J,YAAY;QACnE,IAAIF,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;UAC5CmN,MAAM,CAAC+B,QAAQ,GAAGlP,SAAS;UAC3BmN,MAAM,CAACgB,YAAY,GAAGnO,SAAS;QACnC;MACJ;MACA,IAAI4G,IAAI,EAAE;QACN;QACA,IAAIA,IAAI,CAACK,OAAO,GAAG,CAAC,IAAI,CAACkG,MAAM,CAAC+B,QAAQ,EAAE;UACtC/B,MAAM,CAAC+B,QAAQ,GAAGtI,IAAI,CAACsI,QAAQ;QACnC;QACA,IAAItI,IAAI,CAACK,OAAO,GAAG,CAAC,IAAI,CAACkG,MAAM,CAACgB,YAAY,EAAE;UAC1ChB,MAAM,CAACgB,YAAY,GAAGvH,IAAI,CAACsI,QAAQ;QACvC;MACJ;IACJ;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,SAASa,aAAaA,CAACvD,OAAO,EAAEsD,WAAW,EAAEW,WAAW,EAAE;EACtD,IAAIC,kBAAkB,GAAGZ,WAAW;EACpC,IAAIa,eAAe,GAAGnE,OAAO,CAACvL,MAAM,CAAC,UAAUsM,GAAG,EAAEJ,MAAM,EAAE;IAAE,OAAOI,GAAG,GAAGJ,MAAM,CAACgB,YAAY;EAAE,CAAC,EAAE,CAAC,CAAC;EACrG,KAAK,IAAIhI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,OAAO,CAAC7S,MAAM,EAAEwM,CAAC,EAAE,EAAE;IACrC,IAAIgH,MAAM,GAAGX,OAAO,CAACrG,CAAC,CAAC;IACvB,IAAIyK,KAAK,GAAGzD,MAAM,CAACgB,YAAY,GAAGwC,eAAe;IACjD,IAAIE,eAAe,GAAGH,kBAAkB,GAAGE,KAAK;IAChD,IAAIE,cAAc,GAAG3D,MAAM,CAACrQ,KAAK,GAAG+T,eAAe;IACnD,IAAI3B,QAAQ,GAAGuB,WAAW,CAACtD,MAAM,CAAC;IAClC,IAAI4D,QAAQ,GAAGD,cAAc,GAAG5B,QAAQ,GAAGA,QAAQ,GAAG4B,cAAc;IACpEhB,WAAW,IAAIiB,QAAQ,GAAG5D,MAAM,CAACrQ,KAAK;IACtCqQ,MAAM,CAACrQ,KAAK,GAAGiU,QAAQ;EAC3B;EACAjB,WAAW,GAAG3O,IAAI,CAAC8O,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC,GAAG,IAAI;EACnD;EACA;EACA,IAAIA,WAAW,EAAE;IACb,IAAIH,gBAAgB,GAAGnD,OAAO,CAACG,MAAM,CAAC,UAAUQ,MAAM,EAAE;MACpD,OAAO2C,WAAW,GAAG,CAAC,GAChB3C,MAAM,CAACrQ,KAAK,GAAG2T,WAAW,CAACtD,MAAM,CAAC,CAAC;MAAA,EACnC,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAIwC,gBAAgB,CAAChW,MAAM,EAAE;MACzBmW,WAAW,GAAGC,aAAa,CAACJ,gBAAgB,EAAEG,WAAW,EAAEW,WAAW,CAAC;IAC3E;EACJ;EACA,OAAOX,WAAW;AACtB;AACA,SAASO,aAAaA,CAAC/P,KAAK,EAAE;EAC1B,IAAI0Q,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,GAAG,GAAG5Q,KAAK,CAACoN,OAAO,CAAC,CAAC;EACzB,KAAK,IAAIyD,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGD,GAAG,CAACvX,MAAM,EAAEwX,QAAQ,EAAE,EAAE;IACtD,IAAI7K,GAAG,GAAG4K,GAAG,CAACC,QAAQ,CAAC;IACvB,KAAK,IAAInJ,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG6E,KAAK,CAACkM,OAAO,EAAExE,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;MACvD,IAAImF,MAAM,GAAG1R,EAAE,CAACuM,EAAE,CAAC;MACnB,IAAI+F,IAAI,GAAGiD,YAAY,CAAC7D,MAAM,CAACzE,KAAK,CAAC;MACrC,IAAIuI,eAAe,GAAG,CAAC,EAAE;QACrBA,eAAe,EAAE;QACjB,OAAO3K,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;MAClC,CAAC,MACI,IAAIqF,IAAI,EAAE;QACXA,IAAI,CAACnH,IAAI,CAAC7J,MAAM,IAAIuJ,GAAG,CAACvJ,MAAM;QAC9BkU,eAAe,GAAGlD,IAAI,CAACnH,IAAI,CAACK,OAAO;QACnC,OAAOX,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;QAC9BqF,IAAI,CAAC3L,IAAI,EAAE;QACX,IAAI2L,IAAI,CAAC3L,IAAI,IAAI,CAAC,EAAE;UAChB,OAAO4O,YAAY,CAAC7D,MAAM,CAACzE,KAAK,CAAC;QACrC;MACJ,CAAC,MACI;QACD,IAAI9B,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;QAClC,IAAI,CAAC9B,IAAI,EAAE;UACP;QACJ;QACAA,IAAI,CAAC7J,MAAM,GAAGuJ,GAAG,CAACvJ,MAAM;QACxB,IAAI6J,IAAI,CAACI,OAAO,GAAG,CAAC,EAAE;UAClB,IAAIoK,SAAS,GAAGF,GAAG,CAACvX,MAAM,GAAGwX,QAAQ;UACrC,IAAI/O,IAAI,GAAGwE,IAAI,CAACI,OAAO,GAAGoK,SAAS,GAAGA,SAAS,GAAGxK,IAAI,CAACI,OAAO;UAC9DgK,YAAY,CAAC7D,MAAM,CAACzE,KAAK,CAAC,GAAG;YAAE9B,IAAI,EAAEA,IAAI;YAAExE,IAAI,EAAEA,IAAI;YAAEkE,GAAG,EAAEA;UAAI,CAAC;QACrE;MACJ;IACJ;EACJ;AACJ;AACA,SAAS6J,aAAaA,CAAC7P,KAAK,EAAE;EAC1B,IAAI4Q,GAAG,GAAG5Q,KAAK,CAACoN,OAAO,CAAC,CAAC;EACzB,KAAK,IAAIyD,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGD,GAAG,CAACvX,MAAM,EAAEwX,QAAQ,EAAE,EAAE;IACtD,IAAI7K,GAAG,GAAG4K,GAAG,CAACC,QAAQ,CAAC;IACvB,IAAIE,WAAW,GAAG,IAAI;IACtB,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIC,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGlR,KAAK,CAACkM,OAAO,CAAC7S,MAAM,EAAE6X,WAAW,EAAE,EAAE;MACzE,IAAIrE,MAAM,GAAG7M,KAAK,CAACkM,OAAO,CAACgF,WAAW,CAAC;MACvC;MACAD,YAAY,IAAI,CAAC;MACjB,IAAIA,YAAY,GAAG,CAAC,IAAIjR,KAAK,CAACkM,OAAO,CAACgF,WAAW,GAAG,CAAC,CAAC,EAAE;QACpDF,oBAAoB,IAAInE,MAAM,CAACrQ,KAAK;QACpC,OAAOwJ,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;MAClC,CAAC,MACI,IAAI2I,WAAW,EAAE;QAClB,IAAIzK,IAAI,GAAGyK,WAAW;QACtB,OAAO/K,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;QAC9B2I,WAAW,GAAG,IAAI;QAClBzK,IAAI,CAAC9J,KAAK,GAAGqQ,MAAM,CAACrQ,KAAK,GAAGwU,oBAAoB;MACpD,CAAC,MACI;QACD,IAAI1K,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;QAClC,IAAI,CAAC9B,IAAI,EACL;QACJ2K,YAAY,GAAG3K,IAAI,CAACK,OAAO;QAC3BqK,oBAAoB,GAAG,CAAC;QACxB,IAAI1K,IAAI,CAACK,OAAO,GAAG,CAAC,EAAE;UAClBoK,WAAW,GAAGzK,IAAI;UAClB0K,oBAAoB,IAAInE,MAAM,CAACrQ,KAAK;UACpC;QACJ;QACA8J,IAAI,CAAC9J,KAAK,GAAGqQ,MAAM,CAACrQ,KAAK,GAAGwU,oBAAoB;MACpD;IACJ;EACJ;AACJ;AACA,SAASlB,UAAUA,CAAC9P,KAAK,EAAE3H,GAAG,EAAE;EAC5B,IAAI8Y,aAAa,GAAG;IAAEC,KAAK,EAAE,CAAC;IAAE3U,MAAM,EAAE;EAAE,CAAC;EAC3C,KAAK,IAAIiL,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG6E,KAAK,CAACoN,OAAO,CAAC,CAAC,EAAE1F,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;IACzD,IAAI1B,GAAG,GAAG7K,EAAE,CAACuM,EAAE,CAAC;IAChB,KAAK,IAAItM,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG2E,KAAK,CAACkM,OAAO,EAAE9Q,EAAE,GAAGC,EAAE,CAAChC,MAAM,EAAE+B,EAAE,EAAE,EAAE;MACvD,IAAIyR,MAAM,GAAGxR,EAAE,CAACD,EAAE,CAAC;MACnB,IAAIkL,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;MAClC,IAAI,CAAC9B,IAAI,EACL;MACJjO,GAAG,CAAC4C,WAAW,CAACqL,IAAI,CAAClO,MAAM,EAAE,IAAI,CAAC;MAClC,IAAIiZ,SAAS,GAAG/K,IAAI,CAAC9J,KAAK,GAAG8J,IAAI,CAACxD,OAAO,CAAC,YAAY,CAAC;MACvD,IAAIwD,IAAI,CAAClO,MAAM,CAACoH,QAAQ,KAAK,WAAW,EAAE;QACtC;QACA8G,IAAI,CAACrO,IAAI,GAAGI,GAAG,CAAC+D,eAAe,CAACkK,IAAI,CAACrO,IAAI,EAAEoZ,SAAS,GAAG,CAAC,GAAGhZ,GAAG,CAACI,WAAW,CAAC,CAAC,EAAE;UAAEC,QAAQ,EAAE4N,IAAI,CAAClO,MAAM,CAACM;QAAS,CAAC,CAAC;MACrH,CAAC,MACI,IAAI4N,IAAI,CAAClO,MAAM,CAACoH,QAAQ,KAAK,WAAW,EAAE;QAC3C8G,IAAI,CAACrO,IAAI,GAAGqZ,SAAS,CAAChL,IAAI,CAACrO,IAAI,EAAEoZ,SAAS,EAAE/K,IAAI,CAAClO,MAAM,EAAEC,GAAG,EAAE,KAAK,CAAC;MACxE,CAAC,MACI,IAAIiO,IAAI,CAAClO,MAAM,CAACoH,QAAQ,KAAK,QAAQ,EAAE;QACxC8G,IAAI,CAACrO,IAAI,GAAGqZ,SAAS,CAAChL,IAAI,CAACrO,IAAI,EAAEoZ,SAAS,EAAE/K,IAAI,CAAClO,MAAM,EAAEC,GAAG,EAAE,EAAE,CAAC;MACrE,CAAC,MACI,IAAI,OAAOiO,IAAI,CAAClO,MAAM,CAACoH,QAAQ,KAAK,UAAU,EAAE;QACjD,IAAIgD,MAAM,GAAG8D,IAAI,CAAClO,MAAM,CAACoH,QAAQ,CAAC8G,IAAI,CAACrO,IAAI,EAAEoZ,SAAS,CAAC;QACvD,IAAI,OAAO7O,MAAM,KAAK,QAAQ,EAAE;UAC5B8D,IAAI,CAACrO,IAAI,GAAG,CAACuK,MAAM,CAAC;QACxB,CAAC,MACI;UACD8D,IAAI,CAACrO,IAAI,GAAGuK,MAAM;QACtB;MACJ;MACA8D,IAAI,CAACmI,aAAa,GAAGnI,IAAI,CAAC0I,gBAAgB,CAAC3W,GAAG,CAACI,WAAW,CAAC,CAAC,EAAEJ,GAAG,CAACQ,mBAAmB,CAAC,CAAC,CAAC;MACxF,IAAI0Y,iBAAiB,GAAGjL,IAAI,CAACmI,aAAa,GAAGnI,IAAI,CAACI,OAAO;MACzD,IAAIJ,IAAI,CAACI,OAAO,GAAG,CAAC,IAChByK,aAAa,CAACC,KAAK,GAAGD,aAAa,CAAC1U,MAAM,GACtC8U,iBAAiB,GAAGjL,IAAI,CAACI,OAAO,EAAE;QACtCyK,aAAa,GAAG;UAAE1U,MAAM,EAAE8U,iBAAiB;UAAEH,KAAK,EAAE9K,IAAI,CAACI;QAAQ,CAAC;MACtE,CAAC,MACI,IAAIyK,aAAa,IAAIA,aAAa,CAACC,KAAK,GAAG,CAAC,EAAE;QAC/C,IAAID,aAAa,CAAC1U,MAAM,GAAG8U,iBAAiB,EAAE;UAC1CA,iBAAiB,GAAGJ,aAAa,CAAC1U,MAAM;QAC5C;MACJ;MACA,IAAI8U,iBAAiB,GAAGvL,GAAG,CAACvJ,MAAM,EAAE;QAChCuJ,GAAG,CAACvJ,MAAM,GAAG8U,iBAAiB;MAClC;IACJ;IACAJ,aAAa,CAACC,KAAK,EAAE;EACzB;AACJ;AACA,SAASE,SAASA,CAACrZ,IAAI,EAAEuE,KAAK,EAAEpE,MAAM,EAAEC,GAAG,EAAEmH,QAAQ,EAAE;EACnD,OAAOvH,IAAI,CAACyI,GAAG,CAAC,UAAU8Q,GAAG,EAAE;IAAE,OAAOC,YAAY,CAACD,GAAG,EAAEhV,KAAK,EAAEpE,MAAM,EAAEC,GAAG,EAAEmH,QAAQ,CAAC;EAAE,CAAC,CAAC;AAC/F;AACA,SAASiS,YAAYA,CAACxZ,IAAI,EAAEuE,KAAK,EAAEpE,MAAM,EAAEC,GAAG,EAAEmH,QAAQ,EAAE;EACtD,IAAIkS,SAAS,GAAG,KAAK,GAAGrZ,GAAG,CAACI,WAAW,CAAC,CAAC;EACzC+D,KAAK,GAAGqE,IAAI,CAAC8Q,IAAI,CAACnV,KAAK,GAAGkV,SAAS,CAAC,GAAGA,SAAS;EAChD,IAAIlV,KAAK,IAAI+D,cAAc,CAACtI,IAAI,EAAEG,MAAM,EAAEC,GAAG,CAAC,EAAE;IAC5C,OAAOJ,IAAI;EACf;EACA,OAAOuE,KAAK,GAAG+D,cAAc,CAACtI,IAAI,GAAGuH,QAAQ,EAAEpH,MAAM,EAAEC,GAAG,CAAC,EAAE;IACzD,IAAIJ,IAAI,CAACoB,MAAM,IAAI,CAAC,EAAE;MAClB;IACJ;IACApB,IAAI,GAAGA,IAAI,CAAC2Z,SAAS,CAAC,CAAC,EAAE3Z,IAAI,CAACoB,MAAM,GAAG,CAAC,CAAC;EAC7C;EACA,OAAOpB,IAAI,CAACkP,IAAI,CAAC,CAAC,GAAG3H,QAAQ;AACjC;AAEA,SAASqS,WAAWA,CAACC,QAAQ,EAAEzM,KAAK,EAAE;EAClC,IAAIhN,GAAG,GAAG,IAAIuB,UAAU,CAACkY,QAAQ,CAAC;EAClC,IAAIlL,OAAO,GAAGmL,YAAY,CAAC1M,KAAK,EAAEhN,GAAG,CAACI,WAAW,CAAC,CAAC,CAAC;EACpD,IAAIuH,KAAK,GAAG,IAAI+M,KAAK,CAAC1H,KAAK,EAAEuB,OAAO,CAAC;EACrCuI,eAAe,CAAC9W,GAAG,EAAE2H,KAAK,CAAC;EAC3B3H,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;EAC/B,OAAOkG,KAAK;AAChB;AACA,SAAS+R,YAAYA,CAAC1M,KAAK,EAAEoG,EAAE,EAAE;EAC7B,IAAI7E,OAAO,GAAGvB,KAAK,CAACuB,OAAO;EAC3B,IAAIsF,OAAO,GAAG8F,aAAa,CAACpL,OAAO,CAACsF,OAAO,CAAC;EAC5C;EACA,IAAItF,OAAO,CAAC3G,IAAI,CAAC5G,MAAM,KAAK,CAAC,EAAE;IAC3B,IAAI4Y,UAAU,GAAGC,kBAAkB,CAAChG,OAAO,EAAE,MAAM,CAAC;IACpD,IAAI+F,UAAU,EACVrL,OAAO,CAAC3G,IAAI,CAACiG,IAAI,CAAC+L,UAAU,CAAC;EACrC;EACA,IAAIrL,OAAO,CAACzG,IAAI,CAAC9G,MAAM,KAAK,CAAC,EAAE;IAC3B,IAAI4Y,UAAU,GAAGC,kBAAkB,CAAChG,OAAO,EAAE,MAAM,CAAC;IACpD,IAAI+F,UAAU,EACVrL,OAAO,CAACzG,IAAI,CAAC+F,IAAI,CAAC+L,UAAU,CAAC;EACrC;EACA,IAAIjH,KAAK,GAAG3F,KAAK,CAACnE,QAAQ,CAAC8J,KAAK;EAChC,IAAI5S,MAAM,GAAGiN,KAAK,CAACjN,MAAM;EACzB,OAAO;IACH8T,OAAO,EAAEA,OAAO;IAChBjM,IAAI,EAAEkS,YAAY,CAAC,MAAM,EAAEvL,OAAO,CAAC3G,IAAI,EAAEiM,OAAO,EAAE9T,MAAM,EAAE4S,KAAK,EAAES,EAAE,CAAC;IACpEvL,IAAI,EAAEiS,YAAY,CAAC,MAAM,EAAEvL,OAAO,CAAC1G,IAAI,EAAEgM,OAAO,EAAE9T,MAAM,EAAE4S,KAAK,EAAES,EAAE,CAAC;IACpEtL,IAAI,EAAEgS,YAAY,CAAC,MAAM,EAAEvL,OAAO,CAACzG,IAAI,EAAE+L,OAAO,EAAE9T,MAAM,EAAE4S,KAAK,EAAES,EAAE;EACvE,CAAC;AACL;AACA,SAAS0G,YAAYA,CAACC,WAAW,EAAEC,WAAW,EAAEnG,OAAO,EAAEoG,UAAU,EAAEtH,KAAK,EAAEvS,WAAW,EAAE;EACrF,IAAI8Z,qBAAqB,GAAG,CAAC,CAAC;EAC9B,IAAI/P,MAAM,GAAG6P,WAAW,CAAC3R,GAAG,CAAC,UAAU8R,MAAM,EAAE3B,QAAQ,EAAE;IACrD,IAAI4B,qBAAqB,GAAG,CAAC;IAC7B,IAAIpM,KAAK,GAAG,CAAC,CAAC;IACd,IAAIqM,aAAa,GAAG,CAAC;IACrB,IAAIC,eAAe,GAAG,CAAC;IACvB,KAAK,IAAIjL,EAAE,GAAG,CAAC,EAAEkL,SAAS,GAAG1G,OAAO,EAAExE,EAAE,GAAGkL,SAAS,CAACvZ,MAAM,EAAEqO,EAAE,EAAE,EAAE;MAC/D,IAAImF,MAAM,GAAG+F,SAAS,CAAClL,EAAE,CAAC;MAC1B,IAAI6K,qBAAqB,CAAC1F,MAAM,CAACzE,KAAK,CAAC,IAAI,IAAI,IAC3CmK,qBAAqB,CAAC1F,MAAM,CAACzE,KAAK,CAAC,CAACtG,IAAI,KAAK,CAAC,EAAE;QAChD,IAAI6Q,eAAe,KAAK,CAAC,EAAE;UACvB,IAAIE,OAAO,GAAG,KAAK,CAAC;UACpB,IAAI/X,KAAK,CAACC,OAAO,CAACyX,MAAM,CAAC,EAAE;YACvBK,OAAO,GACHL,MAAM,CAAC3F,MAAM,CAACzE,KAAK,GAAGsK,aAAa,GAAGD,qBAAqB,CAAC;UACpE,CAAC,MACI;YACDI,OAAO,GAAGL,MAAM,CAAC3F,MAAM,CAACH,OAAO,CAAC;UACpC;UACA,IAAIoG,eAAe,GAAG,CAAC,CAAC;UACxB,IAAI,OAAOD,OAAO,KAAK,QAAQ,IAAI,CAAC/X,KAAK,CAACC,OAAO,CAAC8X,OAAO,CAAC,EAAE;YACxDC,eAAe,GAAG,CAACD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACza,MAAM,KAAK,CAAC,CAAC;UAC9F;UACA,IAAIA,MAAM,GAAGqO,UAAU,CAAC2L,WAAW,EAAEvF,MAAM,EAAEgE,QAAQ,EAAE7F,KAAK,EAAEsH,UAAU,EAAE7Z,WAAW,EAAEqa,eAAe,CAAC;UACvG,IAAIxM,IAAI,GAAG,IAAIkI,IAAI,CAACqE,OAAO,EAAEza,MAAM,EAAEga,WAAW,CAAC;UACjD;UACA;UACA/L,KAAK,CAACwG,MAAM,CAACH,OAAO,CAAC,GAAGpG,IAAI;UAC5BD,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC,GAAG9B,IAAI;UAC1BqM,eAAe,GAAGrM,IAAI,CAACK,OAAO,GAAG,CAAC;UAClC4L,qBAAqB,CAAC1F,MAAM,CAACzE,KAAK,CAAC,GAAG;YAClCtG,IAAI,EAAEwE,IAAI,CAACI,OAAO,GAAG,CAAC;YACtBqM,KAAK,EAAEJ;UACX,CAAC;QACL,CAAC,MACI;UACDA,eAAe,EAAE;UACjBD,aAAa,EAAE;QACnB;MACJ,CAAC,MACI;QACDH,qBAAqB,CAAC1F,MAAM,CAACzE,KAAK,CAAC,CAACtG,IAAI,EAAE;QAC1C6Q,eAAe,GAAGJ,qBAAqB,CAAC1F,MAAM,CAACzE,KAAK,CAAC,CAAC2K,KAAK;QAC3DN,qBAAqB,EAAE;MAC3B;IACJ;IACA,OAAO,IAAIzE,GAAG,CAACwE,MAAM,EAAE3B,QAAQ,EAAEuB,WAAW,EAAE/L,KAAK,CAAC;EACxD,CAAC,CAAC;EACF,OAAO7D,MAAM;AACjB;AACA,SAAS0P,kBAAkBA,CAAChG,OAAO,EAAEY,OAAO,EAAE;EAC1C,IAAImF,UAAU,GAAG,CAAC,CAAC;EACnB/F,OAAO,CAACK,OAAO,CAAC,UAAUwB,GAAG,EAAE;IAC3B,IAAIA,GAAG,CAACE,GAAG,IAAI,IAAI,EAAE;MACjB,IAAI+E,KAAK,GAAGC,eAAe,CAACnG,OAAO,EAAEiB,GAAG,CAACE,GAAG,CAAC;MAC7C,IAAI+E,KAAK,IAAI,IAAI,EACbf,UAAU,CAAClE,GAAG,CAACrB,OAAO,CAAC,GAAGsG,KAAK;IACvC;EACJ,CAAC,CAAC;EACF,OAAOjV,MAAM,CAAC4H,IAAI,CAACsM,UAAU,CAAC,CAAC5Y,MAAM,GAAG,CAAC,GAAG4Y,UAAU,GAAG,IAAI;AACjE;AACA,SAASgB,eAAeA,CAACnG,OAAO,EAAED,MAAM,EAAE;EACtC,IAAIC,OAAO,KAAK,MAAM,EAAE;IACpB,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC5B,OAAOA,MAAM,CAACqG,MAAM,IAAI,IAAI;IAChC,CAAC,MACI,IAAI,OAAOrG,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC/D,OAAOA,MAAM;IACjB;EACJ,CAAC,MACI,IAAIC,OAAO,KAAK,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IACvD,OAAOA,MAAM,CAACsG,MAAM;EACxB;EACA,OAAO,IAAI;AACf;AACA,SAASnB,aAAaA,CAAC9F,OAAO,EAAE;EAC5B,OAAOA,OAAO,CAACxL,GAAG,CAAC,UAAU2E,KAAK,EAAE+C,KAAK,EAAE;IACvC,IAAIjN,EAAE;IACN,IAAImR,GAAG;IACP,IAAI,OAAOjH,KAAK,KAAK,QAAQ,EAAE;MAC3BiH,GAAG,GAAG,CAACnR,EAAE,GAAGkK,KAAK,CAACqH,OAAO,MAAM,IAAI,IAAIvR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGiN,KAAK;IACrE,CAAC,MACI;MACDkE,GAAG,GAAGlE,KAAK;IACf;IACA,OAAO,IAAI6G,MAAM,CAAC3C,GAAG,EAAEjH,KAAK,EAAE+C,KAAK,CAAC;EACxC,CAAC,CAAC;AACN;AACA,SAAS3B,UAAUA,CAAC2L,WAAW,EAAEvF,MAAM,EAAEgE,QAAQ,EAAEuC,SAAS,EAAEhb,MAAM,EAAEK,WAAW,EAAEqa,eAAe,EAAE;EAChG,IAAI9H,KAAK,GAAGnL,QAAQ,CAACuT,SAAS,CAAC;EAC/B,IAAIC,aAAa;EACjB,IAAIjB,WAAW,KAAK,MAAM,EAAE;IACxBiB,aAAa,GAAGjb,MAAM,CAACiR,UAAU;EACrC,CAAC,MACI,IAAI+I,WAAW,KAAK,MAAM,EAAE;IAC7BiB,aAAa,GAAGjb,MAAM,CAACkR,UAAU;EACrC,CAAC,MACI,IAAI8I,WAAW,KAAK,MAAM,EAAE;IAC7BiB,aAAa,GAAGjb,MAAM,CAACmR,UAAU;EACrC;EACA,IAAI+J,WAAW,GAAGzL,MAAM,CAAC,CAAC,CAAC,EAAEmD,KAAK,CAAChL,KAAK,EAAEgL,KAAK,CAACoH,WAAW,CAAC,EAAEha,MAAM,CAACA,MAAM,EAAEib,aAAa,CAAC;EAC3F,IAAI5J,YAAY,GAAGrR,MAAM,CAACqR,YAAY,CAACoD,MAAM,CAACH,OAAO,CAAC,IAClDtU,MAAM,CAACqR,YAAY,CAACoD,MAAM,CAACzE,KAAK,CAAC,IACjC,CAAC,CAAC;EACN,IAAImL,SAAS,GAAGnB,WAAW,KAAK,MAAM,GAAG3I,YAAY,GAAG,CAAC,CAAC;EAC1D,IAAI+J,SAAS,GAAGpB,WAAW,KAAK,MAAM,IAAIvB,QAAQ,GAAG,CAAC,KAAK,CAAC,GACtDhJ,MAAM,CAAC,CAAC,CAAC,EAAEmD,KAAK,CAAC5K,YAAY,EAAEhI,MAAM,CAACoR,kBAAkB,CAAC,GACzD,CAAC,CAAC;EACR,IAAIiK,YAAY,GAAGlU,aAAa,CAAC9G,WAAW,CAAC;EAC7C,IAAIib,WAAW,GAAG7L,MAAM,CAAC,CAAC,CAAC,EAAE4L,YAAY,EAAEH,WAAW,EAAEE,SAAS,EAAED,SAAS,CAAC;EAC7E,OAAO1L,MAAM,CAAC6L,WAAW,EAAEZ,eAAe,CAAC;AAC/C;;AAEA;AACA,SAASa,sBAAsBA,CAACtb,GAAG,EAAE2H,KAAK,EAAE4T,MAAM,EAAE;EAChD,IAAIzY,EAAE;EACN,IAAIyY,MAAM,KAAK,KAAK,CAAC,EAAE;IAAEA,MAAM,GAAG,CAAC,CAAC;EAAE;EACtC;EACA,IAAIC,cAAc,GAAG5R,qBAAqB,CAAC5J,GAAG,EAAE2H,KAAK,CAAC;EACtD;EACA,IAAI8T,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAI9H,OAAO,GAAG,EAAE;EAChB,IAAIhB,yBAAyB,GAAG,EAAE;EAClC,IAAIpQ,KAAK,CAACC,OAAO,CAACiF,KAAK,CAACkB,QAAQ,CAACgK,yBAAyB,CAAC,EAAE;IACzDA,yBAAyB,GAAGlL,KAAK,CAACkB,QAAQ,CAACgK,yBAAyB;IACpE;EACJ,CAAC,MACI,IAAI,OAAOlL,KAAK,CAACkB,QAAQ,CAACgK,yBAAyB,KAAK,QAAQ,IACjE,OAAOlL,KAAK,CAACkB,QAAQ,CAACgK,yBAAyB,KAAK,QAAQ,EAAE;IAC9DA,yBAAyB,GAAG,CAAClL,KAAK,CAACkB,QAAQ,CAACgK,yBAAyB,CAAC;EAC1E;EACA;EACAA,yBAAyB,CAACqB,OAAO,CAAC,UAAU0H,KAAK,EAAE;IAC/C,IAAIlG,GAAG,GAAG/N,KAAK,CAACkM,OAAO,CAACgI,IAAI,CAAC,UAAUC,IAAI,EAAE;MAAE,OAAOA,IAAI,CAACzH,OAAO,KAAKuH,KAAK,IAAIE,IAAI,CAAC/L,KAAK,KAAK6L,KAAK;IAAE,CAAC,CAAC;IACxG,IAAIlG,GAAG,IAAI,CAAC+F,gBAAgB,CAACM,GAAG,CAACrG,GAAG,CAAC3F,KAAK,CAAC,EAAE;MACzC0L,gBAAgB,CAACO,GAAG,CAACtG,GAAG,CAAC3F,KAAK,EAAE,IAAI,CAAC;MACrC4L,UAAU,CAAC9N,IAAI,CAAC6H,GAAG,CAAC3F,KAAK,CAAC;MAC1B8D,OAAO,CAAChG,IAAI,CAAClG,KAAK,CAACkM,OAAO,CAAC6B,GAAG,CAAC3F,KAAK,CAAC,CAAC;MACtCyL,cAAc,IAAI9F,GAAG,CAACF,YAAY;IACtC;EACJ,CAAC,CAAC;EACF,IAAIyG,KAAK,GAAG,IAAI;EAChB,IAAIzO,CAAC,GAAG,CAAC1K,EAAE,GAAGyY,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACW,KAAK,MAAM,IAAI,IAAIpZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,CAAC;EAChH,OAAO0K,CAAC,GAAG7F,KAAK,CAACkM,OAAO,CAAC7S,MAAM,EAAE;IAC7B;IACA,IAAIya,gBAAgB,CAACM,GAAG,CAACvO,CAAC,CAAC,EAAE;MACzBA,CAAC,EAAE;MACH;IACJ;IACA,IAAI2O,QAAQ,GAAGxU,KAAK,CAACkM,OAAO,CAACrG,CAAC,CAAC,CAACgI,YAAY;IAC5C;IACA,IAAIyG,KAAK,IAAIT,cAAc,IAAIW,QAAQ,EAAE;MACrCF,KAAK,GAAG,KAAK;MACbN,UAAU,CAAC9N,IAAI,CAACL,CAAC,CAAC;MAClBqG,OAAO,CAAChG,IAAI,CAAClG,KAAK,CAACkM,OAAO,CAACrG,CAAC,CAAC,CAAC;MAC9BgO,cAAc,IAAIW,QAAQ;IAC9B,CAAC,MACI;MACD;IACJ;IACA3O,CAAC,EAAE;EACP;EACA,OAAO;IAAEmO,UAAU,EAAEA,UAAU;IAAE9H,OAAO,EAAEA,OAAO;IAAEuI,SAAS,EAAE5O,CAAC,GAAG;EAAE,CAAC;AACzE;AACA,SAAS6O,+BAA+BA,CAACrc,GAAG,EAAE2H,KAAK,EAAE;EACjD,IAAI2U,UAAU,GAAG,EAAE;EACnB,KAAK,IAAI9O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7F,KAAK,CAACkM,OAAO,CAAC7S,MAAM,EAAEwM,CAAC,EAAE,EAAE;IAC3C,IAAIrD,MAAM,GAAGmR,sBAAsB,CAACtb,GAAG,EAAE2H,KAAK,EAAE;MAAEuU,KAAK,EAAE1O;IAAE,CAAC,CAAC;IAC7D,IAAIrD,MAAM,CAAC0J,OAAO,CAAC7S,MAAM,EAAE;MACvBsb,UAAU,CAACzO,IAAI,CAAC1D,MAAM,CAAC;MACvBqD,CAAC,GAAGrD,MAAM,CAACiS,SAAS;IACxB;EACJ;EACA,OAAOE,UAAU;AACrB;AAEA,SAASC,SAASA,CAAC9C,QAAQ,EAAE9R,KAAK,EAAE;EAChC,IAAIkB,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;EAC7B,IAAI0G,MAAM,GAAG1G,QAAQ,CAAC0G,MAAM;EAC5B,IAAIzF,MAAM,GAAGjB,QAAQ,CAACiB,MAAM;EAC5B,IAAIlB,MAAM,GAAG;IAAE/I,CAAC,EAAEiK,MAAM,CAACL,IAAI;IAAE3J,CAAC,EAAEyP;EAAO,CAAC;EAC1C,IAAIiN,cAAc,GAAG7U,KAAK,CAACgN,aAAa,CAAChN,KAAK,CAACkM,OAAO,CAAC,GAAGlM,KAAK,CAACmN,aAAa,CAACnN,KAAK,CAACkM,OAAO,CAAC;EAC5F,IAAI4I,iBAAiB,GAAGlN,MAAM,GAAGzF,MAAM,CAACN,MAAM,GAAGgT,cAAc;EAC/D,IAAI3T,QAAQ,CAACiK,SAAS,KAAK,OAAO,EAAE;IAChC,IAAIrF,IAAI,GAAG9F,KAAK,CAACE,IAAI;IACrB,IAAI6U,WAAW,GAAGjP,IAAI,CAACnF,MAAM,CAAC,UAAUsM,GAAG,EAAEjH,GAAG,EAAE;MAAE,OAAOiH,GAAG,GAAGjH,GAAG,CAACvJ,MAAM;IAAE,CAAC,EAAE,CAAC,CAAC;IAClFqY,iBAAiB,IAAIC,WAAW;EACpC;EACA,IAAI1c,GAAG,GAAG,IAAIuB,UAAU,CAACkY,QAAQ,CAAC;EAClC,IAAI5Q,QAAQ,CAACiK,SAAS,KAAK,QAAQ,IAC9BjK,QAAQ,CAAC0G,MAAM,IAAI,IAAI,IAAIkN,iBAAiB,GAAGzc,GAAG,CAAC+E,QAAQ,CAAC,CAAC,CAACX,MAAO,EAAE;IACxEuY,QAAQ,CAAC3c,GAAG,CAAC;IACb4I,MAAM,CAAC9I,CAAC,GAAGgK,MAAM,CAACR,GAAG;EACzB;EACA3B,KAAK,CAAC2N,qBAAqB,CAACtV,GAAG,EAAE4I,MAAM,CAAC;EACxC,IAAID,QAAQ,GAAG6G,MAAM,CAAC,CAAC,CAAC,EAAE5G,MAAM,CAAC;EACjCjB,KAAK,CAAC4L,eAAe,GAAGvT,GAAG,CAACmF,UAAU,CAAC,CAAC;EACxC,IAAI0D,QAAQ,CAAC+J,mBAAmB,EAAE;IAC9B;IACAgK,iCAAiC,CAAC5c,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,CAAC;EACnE,CAAC,MACI;IACD;IACA5I,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;IAC/B,IAAIoH,QAAQ,CAAC6J,QAAQ,KAAK,WAAW,IACjC7J,QAAQ,CAAC6J,QAAQ,KAAK,WAAW,EAAE;MACnC/K,KAAK,CAACC,IAAI,CAACsM,OAAO,CAAC,UAAUvG,GAAG,EAAE;QAC9B,OAAOkP,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEjB,KAAK,CAACkM,OAAO,CAAC;MAC3D,CAAC,CAAC;IACN;IACA7T,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;IAC/BkG,KAAK,CAACE,IAAI,CAACqM,OAAO,CAAC,UAAUvG,GAAG,EAAEoC,KAAK,EAAE;MACrC,IAAI+M,SAAS,GAAG/M,KAAK,KAAKpI,KAAK,CAACE,IAAI,CAAC7G,MAAM,GAAG,CAAC;MAC/C+b,YAAY,CAAC/c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAEmP,SAAS,EAAEnU,QAAQ,EAAEC,MAAM,EAAEjB,KAAK,CAACkM,OAAO,CAAC;IAC7E,CAAC,CAAC;IACF7T,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;IAC/B,IAAIoH,QAAQ,CAAC4J,QAAQ,KAAK,UAAU,IAAI5J,QAAQ,CAAC4J,QAAQ,KAAK,WAAW,EAAE;MACvE9K,KAAK,CAACG,IAAI,CAACoM,OAAO,CAAC,UAAUvG,GAAG,EAAE;QAC9B,OAAOkP,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEjB,KAAK,CAACkM,OAAO,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;EACAnL,cAAc,CAAC1I,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,CAAC;EAC5CjB,KAAK,CAAC0N,gBAAgB,CAACrV,GAAG,EAAE4I,MAAM,CAAC;EACnCjB,KAAK,CAAC8L,MAAM,GAAG7K,MAAM,CAAC9I,CAAC;EACvB2Z,QAAQ,CAAClV,aAAa,GAAGoD,KAAK;EAC9B3H,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;AACnC;AACA,SAASmb,iCAAiCA,CAAC5c,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAE;EACrE;EACA,IAAIoU,sBAAsB,GAAGX,+BAA+B,CAACrc,GAAG,EAAE2H,KAAK,CAAC;EACxE,IAAIkB,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;EAC7B,IAAIA,QAAQ,CAACoK,4BAA4B,KAAK,cAAc,EAAE;IAC1D+J,sBAAsB,CAAC9I,OAAO,CAAC,UAAU+I,cAAc,EAAElN,KAAK,EAAE;MAC5D/P,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;MAC/B;MACA,IAAIsO,KAAK,GAAG,CAAC,EAAE;QACX;QACA;QACAnL,OAAO,CAAC5E,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAEqU,cAAc,CAACpJ,OAAO,EAAE,IAAI,CAAC;MACvE,CAAC,MACI;QACD;QACAqJ,SAAS,CAACld,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAEqU,cAAc,CAACpJ,OAAO,CAAC;MACzD;MACA;MACAsJ,SAAS,CAACnd,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAEqU,cAAc,CAACpJ,OAAO,CAAC;MAC/DuJ,SAAS,CAACpd,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAEqU,cAAc,CAACpJ,OAAO,CAAC;IACzD,CAAC,CAAC;EACN,CAAC,MACI;IACD,IAAIwJ,wBAAwB,GAAG,CAAC,CAAC;IACjC,IAAIC,uBAAuB,GAAGN,sBAAsB,CAAC,CAAC,CAAC;IACvD,IAAI3L,OAAO,GAAG,SAAAA,CAAA,EAAY;MACtB;MACA,IAAIkM,mBAAmB,GAAGF,wBAAwB;MAClD,IAAIC,uBAAuB,EAAE;QACzBtd,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;QAC/B,IAAI+b,iBAAiB,GAAGF,uBAAuB,CAACzJ,OAAO;QACvD,IAAIwJ,wBAAwB,IAAI,CAAC,EAAE;UAC/B;UACA;UACAzY,OAAO,CAAC5E,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAE4U,iBAAiB,EAAE,IAAI,CAAC;QAClE,CAAC,MACI;UACDN,SAAS,CAACld,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAE4U,iBAAiB,CAAC;QACpD;QACAD,mBAAmB,GAAGE,0BAA0B,CAACzd,GAAG,EAAE2H,KAAK,EAAE0V,wBAAwB,GAAG,CAAC,EAAEzU,MAAM,EAAE4U,iBAAiB,CAAC;QACrHJ,SAAS,CAACpd,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAE4U,iBAAiB,CAAC;MACpD;MACA;MACA,IAAIE,eAAe,GAAGH,mBAAmB,GAAGF,wBAAwB;MACpE;MACAL,sBAAsB,CAACW,KAAK,CAAC,CAAC,CAAC,CAACzJ,OAAO,CAAC,UAAU+I,cAAc,EAAE;QAC9Djd,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;QAC/B;QACA;QACAmD,OAAO,CAAC5E,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAEqU,cAAc,CAACpJ,OAAO,EAAE,IAAI,CAAC;QACnE4J,0BAA0B,CAACzd,GAAG,EAAE2H,KAAK,EAAE0V,wBAAwB,GAAG,CAAC,EAAEzU,MAAM,EAAEqU,cAAc,CAACpJ,OAAO,EAAE6J,eAAe,CAAC;QACrHN,SAAS,CAACpd,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAEqU,cAAc,CAACpJ,OAAO,CAAC;MACzD,CAAC,CAAC;MACFwJ,wBAAwB,GAAGE,mBAAmB;IAClD,CAAC;IACD,OAAOF,wBAAwB,GAAG1V,KAAK,CAACE,IAAI,CAAC7G,MAAM,GAAG,CAAC,EAAE;MACrDqQ,OAAO,CAAC,CAAC;IACb;EACJ;AACJ;AACA,SAAS6L,SAASA,CAACld,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAEiL,OAAO,EAAE;EAC5C,IAAIhL,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;EAC7B7I,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;EAC/B,IAAIoH,QAAQ,CAAC6J,QAAQ,KAAK,WAAW,IAAI7J,QAAQ,CAAC6J,QAAQ,KAAK,WAAW,EAAE;IACxE/K,KAAK,CAACC,IAAI,CAACsM,OAAO,CAAC,UAAUvG,GAAG,EAAE;MAAE,OAAOkP,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,CAAC;IAAE,CAAC,CAAC;EAC7F;AACJ;AACA,SAASsJ,SAASA,CAACnd,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,EAAE;EACtD7T,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;EAC/BkG,KAAK,CAACE,IAAI,CAACqM,OAAO,CAAC,UAAUvG,GAAG,EAAEoC,KAAK,EAAE;IACrC,IAAI+M,SAAS,GAAG/M,KAAK,KAAKpI,KAAK,CAACE,IAAI,CAAC7G,MAAM,GAAG,CAAC;IAC/C+b,YAAY,CAAC/c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAEmP,SAAS,EAAEnU,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,CAAC;EACvE,CAAC,CAAC;AACN;AACA,SAAS4J,0BAA0BA,CAACzd,GAAG,EAAE2H,KAAK,EAAEiW,aAAa,EAAEhV,MAAM,EAAEiL,OAAO,EAAE6J,eAAe,EAAE;EAC7F1d,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;EAC/Bic,eAAe,GAAGA,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG/V,KAAK,CAACE,IAAI,CAAC7G,MAAM;EAC9G,IAAI6c,WAAW,GAAGrV,IAAI,CAACsV,GAAG,CAACF,aAAa,GAAGF,eAAe,EAAE/V,KAAK,CAACE,IAAI,CAAC7G,MAAM,CAAC;EAC9E,IAAIuc,mBAAmB,GAAG,CAAC,CAAC;EAC5B5V,KAAK,CAACE,IAAI,CAAC8V,KAAK,CAACC,aAAa,EAAEC,WAAW,CAAC,CAAC3J,OAAO,CAAC,UAAUvG,GAAG,EAAEoC,KAAK,EAAE;IACvE,IAAI+M,SAAS,GAAGc,aAAa,GAAG7N,KAAK,KAAKpI,KAAK,CAACE,IAAI,CAAC7G,MAAM,GAAG,CAAC;IAC/D,IAAI+c,cAAc,GAAGC,qBAAqB,CAAChe,GAAG,EAAE2H,KAAK,EAAEmV,SAAS,EAAElU,MAAM,CAAC;IACzE,IAAI+E,GAAG,CAACoI,eAAe,CAACgI,cAAc,EAAElK,OAAO,CAAC,EAAE;MAC9CgJ,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,CAAC;MAC1C0J,mBAAmB,GAAGK,aAAa,GAAG7N,KAAK;IAC/C;EACJ,CAAC,CAAC;EACF,OAAOwN,mBAAmB;AAC9B;AACA,SAASH,SAASA,CAACpd,GAAG,EAAE2H,KAAK,EAAEiB,MAAM,EAAEiL,OAAO,EAAE;EAC5C,IAAIhL,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;EAC7B7I,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;EAC/B,IAAIoH,QAAQ,CAAC4J,QAAQ,KAAK,UAAU,IAAI5J,QAAQ,CAAC4J,QAAQ,KAAK,WAAW,EAAE;IACvE9K,KAAK,CAACG,IAAI,CAACoM,OAAO,CAAC,UAAUvG,GAAG,EAAE;MAAE,OAAOkP,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,CAAC;IAAE,CAAC,CAAC;EAC7F;AACJ;AACA,SAASoK,qBAAqBA,CAAChQ,IAAI,EAAEiQ,kBAAkB,EAAEle,GAAG,EAAE;EAC1D,IAAIS,UAAU,GAAGT,GAAG,CAACkF,aAAa,CAAC+I,IAAI,CAAClO,MAAM,CAACM,QAAQ,CAAC;EACxD,IAAI4V,QAAQ,GAAGhI,IAAI,CAACxD,OAAO,CAAC,UAAU,CAAC;EACvC,IAAI0T,cAAc,GAAG3V,IAAI,CAAC4V,KAAK,CAAC,CAACF,kBAAkB,GAAGjI,QAAQ,IAAIxV,UAAU,CAAC;EAC7E,OAAO+H,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE0V,cAAc,CAAC;AACtC;AACA,SAASE,cAAcA,CAAC1Q,GAAG,EAAEuQ,kBAAkB,EAAEvW,KAAK,EAAE3H,GAAG,EAAE;EACzD,IAAIgO,KAAK,GAAG,CAAC,CAAC;EACdL,GAAG,CAACkI,kBAAkB,GAAG,IAAI;EAC7BlI,GAAG,CAACvJ,MAAM,GAAG,CAAC;EACd,IAAIka,SAAS,GAAG,CAAC;EACjB,KAAK,IAAIjP,EAAE,GAAG,CAAC,EAAEvM,EAAE,GAAG6E,KAAK,CAACkM,OAAO,EAAExE,EAAE,GAAGvM,EAAE,CAAC9B,MAAM,EAAEqO,EAAE,EAAE,EAAE;IACvD,IAAImF,MAAM,GAAG1R,EAAE,CAACuM,EAAE,CAAC;IACnB,IAAIpB,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;IAClC,IAAI,CAAC9B,IAAI,EACL;IACJ,IAAI,CAACxL,KAAK,CAACC,OAAO,CAACuL,IAAI,CAACrO,IAAI,CAAC,EAAE;MAC3BqO,IAAI,CAACrO,IAAI,GAAG,CAACqO,IAAI,CAACrO,IAAI,CAAC;IAC3B;IACA,IAAI2e,aAAa,GAAG,IAAIpI,IAAI,CAAClI,IAAI,CAAC2H,GAAG,EAAE3H,IAAI,CAAClO,MAAM,EAAEkO,IAAI,CAACwG,OAAO,CAAC;IACjE8J,aAAa,GAAG/O,MAAM,CAAC+O,aAAa,EAAEtQ,IAAI,CAAC;IAC3CsQ,aAAa,CAAC3e,IAAI,GAAG,EAAE;IACvB,IAAI4e,kBAAkB,GAAGP,qBAAqB,CAAChQ,IAAI,EAAEiQ,kBAAkB,EAAEle,GAAG,CAAC;IAC7E,IAAIiO,IAAI,CAACrO,IAAI,CAACoB,MAAM,GAAGwd,kBAAkB,EAAE;MACvCD,aAAa,CAAC3e,IAAI,GAAGqO,IAAI,CAACrO,IAAI,CAAC6e,MAAM,CAACD,kBAAkB,EAAEvQ,IAAI,CAACrO,IAAI,CAACoB,MAAM,CAAC;IAC/E;IACA,IAAIZ,WAAW,GAAGJ,GAAG,CAACI,WAAW,CAAC,CAAC;IACnC,IAAIG,gBAAgB,GAAGP,GAAG,CAACQ,mBAAmB,CAAC,CAAC;IAChDyN,IAAI,CAACmI,aAAa,GAAGnI,IAAI,CAAC0I,gBAAgB,CAACvW,WAAW,EAAEG,gBAAgB,CAAC;IACzE,IAAI0N,IAAI,CAACmI,aAAa,IAAI8H,kBAAkB,EAAE;MAC1CjQ,IAAI,CAACmI,aAAa,GAAG8H,kBAAkB;MACvCK,aAAa,CAACxe,MAAM,CAACuH,aAAa,IAAI4W,kBAAkB;IAC5D;IACA,IAAIjQ,IAAI,CAACmI,aAAa,GAAGzI,GAAG,CAACvJ,MAAM,EAAE;MACjCuJ,GAAG,CAACvJ,MAAM,GAAG6J,IAAI,CAACmI,aAAa;IACnC;IACAmI,aAAa,CAACnI,aAAa,GAAGmI,aAAa,CAAC5H,gBAAgB,CAACvW,WAAW,EAAEG,gBAAgB,CAAC;IAC3F,IAAIge,aAAa,CAACnI,aAAa,GAAGkI,SAAS,EAAE;MACzCA,SAAS,GAAGC,aAAa,CAACnI,aAAa;IAC3C;IACApI,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC,GAAGwO,aAAa;EACvC;EACA,IAAIG,YAAY,GAAG,IAAI/I,GAAG,CAAChI,GAAG,CAACiI,GAAG,EAAE,CAAC,CAAC,EAAEjI,GAAG,CAAC8G,OAAO,EAAEzG,KAAK,EAAE,IAAI,CAAC;EACjE0Q,YAAY,CAACta,MAAM,GAAGka,SAAS;EAC/B,KAAK,IAAIvb,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG2E,KAAK,CAACkM,OAAO,EAAE9Q,EAAE,GAAGC,EAAE,CAAChC,MAAM,EAAE+B,EAAE,EAAE,EAAE;IACvD,IAAIyR,MAAM,GAAGxR,EAAE,CAACD,EAAE,CAAC;IACnB,IAAIwb,aAAa,GAAGG,YAAY,CAAC1Q,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;IACpD,IAAIwO,aAAa,EAAE;MACfA,aAAa,CAACna,MAAM,GAAGsa,YAAY,CAACta,MAAM;IAC9C;IACA,IAAI6J,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;IAClC,IAAI9B,IAAI,EAAE;MACNA,IAAI,CAAC7J,MAAM,GAAGuJ,GAAG,CAACvJ,MAAM;IAC5B;EACJ;EACA,OAAOsa,YAAY;AACvB;AACA,SAASC,wBAAwBA,CAAC3e,GAAG,EAAE2N,GAAG,EAAEuQ,kBAAkB,EAAEvW,KAAK,EAAE;EACnE,IAAIiX,UAAU,GAAG5e,GAAG,CAAC+E,QAAQ,CAAC,CAAC,CAACX,MAAM;EACtC,IAAI0F,MAAM,GAAGnC,KAAK,CAACkB,QAAQ,CAACiB,MAAM;EAClC,IAAI+U,YAAY,GAAG/U,MAAM,CAACR,GAAG,GAAGQ,MAAM,CAACN,MAAM;EAC7C,IAAIsV,YAAY,GAAGF,UAAU,GAAGC,YAAY;EAC5C,IAAIlR,GAAG,CAAC8G,OAAO,KAAK,MAAM,EAAE;IACxB;IACA;IACAqK,YAAY,IACRnX,KAAK,CAACgN,aAAa,CAAChN,KAAK,CAACkM,OAAO,CAAC,GAAGlM,KAAK,CAACmN,aAAa,CAACnN,KAAK,CAACkM,OAAO,CAAC;EAC/E;EACA,IAAIkL,YAAY,GAAGpR,GAAG,CAACqI,mBAAmB,CAACrO,KAAK,CAACkM,OAAO,EAAE7T,GAAG,CAAC;EAC9D,IAAIgf,UAAU,GAAGD,YAAY,GAAGb,kBAAkB;EAClD,IAAIa,YAAY,GAAGD,YAAY,EAAE;IAC7BvR,OAAO,CAAChH,KAAK,CAAC,gCAAgC,CAAC4N,MAAM,CAACxG,GAAG,CAACoC,KAAK,EAAE,iEAAiE,CAAC,CAAC;IACpI,OAAO,IAAI;EACf;EACA,IAAI,CAACiP,UAAU,EAAE;IACb,OAAO,KAAK;EAChB;EACA,IAAIC,iBAAiB,GAAGtR,GAAG,CAACmI,UAAU,CAACnO,KAAK,CAACkM,OAAO,CAAC;EACrD,IAAIqL,iBAAiB,GAAGvR,GAAG,CAACkH,gBAAgB,CAAClN,KAAK,CAACkM,OAAO,CAAC,GAAGiL,YAAY;EAC1E,IAAII,iBAAiB,EAAE;IACnB,IAAID,iBAAiB,EAAE;MACnB1R,OAAO,CAAChH,KAAK,CAAC,qBAAqB,CAAC4N,MAAM,CAACxG,GAAG,CAACoC,KAAK,EAAE,yIAAyI,CAAC,CAAC;IACrM;IACA,OAAO,IAAI;EACf;EACA,IAAIkP,iBAAiB,EAAE;IACnB;IACA,OAAO,KAAK;EAChB;EACA,IAAItX,KAAK,CAACkB,QAAQ,CAACkK,YAAY,KAAK,OAAO,EAAE;IACzC,OAAO,KAAK;EAChB;EACA;EACA,OAAO,IAAI;AACf;AACA,SAASgK,YAAYA,CAAC/c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAEmP,SAAS,EAAEnU,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,EAAE;EACzE,IAAIkK,cAAc,GAAGC,qBAAqB,CAAChe,GAAG,EAAE2H,KAAK,EAAEmV,SAAS,EAAElU,MAAM,CAAC;EACzE,IAAI+E,GAAG,CAACoI,eAAe,CAACgI,cAAc,EAAElK,OAAO,CAAC,EAAE;IAC9C;IACAgJ,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,CAAC;EAC9C,CAAC,MACI,IAAI8K,wBAAwB,CAAC3e,GAAG,EAAE2N,GAAG,EAAEoQ,cAAc,EAAEpW,KAAK,CAAC,EAAE;IAChE;IACA,IAAI+W,YAAY,GAAGL,cAAc,CAAC1Q,GAAG,EAAEoQ,cAAc,EAAEpW,KAAK,EAAE3H,GAAG,CAAC;IAClE6c,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,CAAC;IAC1CjP,OAAO,CAAC5E,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,CAAC;IAC9CkJ,YAAY,CAAC/c,GAAG,EAAE2H,KAAK,EAAE+W,YAAY,EAAE5B,SAAS,EAAEnU,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,CAAC;EAChF,CAAC,MACI;IACD;IACAjP,OAAO,CAAC5E,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,CAAC;IAC9CkJ,YAAY,CAAC/c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAEmP,SAAS,EAAEnU,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,CAAC;EACvE;AACJ;AACA,SAASgJ,QAAQA,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,EAAE;EAChDjL,MAAM,CAAC/I,CAAC,GAAG8H,KAAK,CAACkB,QAAQ,CAACiB,MAAM,CAACL,IAAI;EACrC,KAAK,IAAI4F,EAAE,GAAG,CAAC,EAAEkL,SAAS,GAAG1G,OAAO,EAAExE,EAAE,GAAGkL,SAAS,CAACvZ,MAAM,EAAEqO,EAAE,EAAE,EAAE;IAC/D,IAAImF,MAAM,GAAG+F,SAAS,CAAClL,EAAE,CAAC;IAC1B,IAAIpB,IAAI,GAAGN,GAAG,CAACK,KAAK,CAACwG,MAAM,CAACzE,KAAK,CAAC;IAClC,IAAI,CAAC9B,IAAI,EAAE;MACPrF,MAAM,CAAC/I,CAAC,IAAI2U,MAAM,CAACrQ,KAAK;MACxB;IACJ;IACAnE,GAAG,CAAC4C,WAAW,CAACqL,IAAI,CAAClO,MAAM,CAAC;IAC5BkO,IAAI,CAACpO,CAAC,GAAG+I,MAAM,CAAC/I,CAAC;IACjBoO,IAAI,CAACnO,CAAC,GAAG8I,MAAM,CAAC9I,CAAC;IACjB,IAAIqK,MAAM,GAAGxC,KAAK,CAACqN,aAAa,CAAChV,GAAG,EAAE2H,KAAK,CAAC2I,KAAK,CAACqB,YAAY,EAAE1D,IAAI,EAAEN,GAAG,EAAE6G,MAAM,EAAE5L,MAAM,CAAC;IAC1F,IAAIuB,MAAM,KAAK,KAAK,EAAE;MAClBvB,MAAM,CAAC/I,CAAC,IAAI2U,MAAM,CAACrQ,KAAK;MACxB;IACJ;IACAgb,YAAY,CAACnf,GAAG,EAAEiO,IAAI,EAAErF,MAAM,CAAC;IAC/B,IAAIwW,OAAO,GAAGnR,IAAI,CAACuI,UAAU,CAAC,CAAC;IAC/B7W,aAAa,CAACsO,IAAI,CAACrO,IAAI,EAAEwf,OAAO,CAACvf,CAAC,EAAEuf,OAAO,CAACtf,CAAC,EAAE;MAC3CgB,MAAM,EAAEmN,IAAI,CAAClO,MAAM,CAACe,MAAM;MAC1BD,MAAM,EAAEoN,IAAI,CAAClO,MAAM,CAACc,MAAM;MAC1BO,QAAQ,EAAEoH,IAAI,CAAC8Q,IAAI,CAACrL,IAAI,CAAC9J,KAAK,GAAG8J,IAAI,CAACxD,OAAO,CAAC,MAAM,CAAC,GAAGwD,IAAI,CAACxD,OAAO,CAAC,OAAO,CAAC;IACjF,CAAC,EAAEzK,GAAG,CAACyE,WAAW,CAAC,CAAC,CAAC;IACrBkD,KAAK,CAACqN,aAAa,CAAChV,GAAG,EAAE2H,KAAK,CAAC2I,KAAK,CAACsB,WAAW,EAAE3D,IAAI,EAAEN,GAAG,EAAE6G,MAAM,EAAE5L,MAAM,CAAC;IAC5EA,MAAM,CAAC/I,CAAC,IAAI2U,MAAM,CAACrQ,KAAK;EAC5B;EACAyE,MAAM,CAAC9I,CAAC,IAAI6N,GAAG,CAACvJ,MAAM;AAC1B;AACA,SAAS+a,YAAYA,CAACnf,GAAG,EAAEiO,IAAI,EAAErF,MAAM,EAAE;EACrC,IAAIwF,UAAU,GAAGH,IAAI,CAAClO,MAAM;EAC5B;EACA;EACAC,GAAG,CAACyE,WAAW,CAAC,CAAC,CAACf,YAAY,CAAC1D,GAAG,CAACyE,WAAW,CAAC,CAAC,CAAC4a,YAAY,CAAC,CAAC,CAAC;EAChE,IAAI,OAAOjR,UAAU,CAACpM,SAAS,KAAK,QAAQ,EAAE;IAC1C;IACA,IAAIqC,SAAS,GAAG2E,YAAY,CAACoF,UAAU,CAACpM,SAAS,EAAEoM,UAAU,CAAC3K,SAAS,CAAC;IACxE,IAAIY,SAAS,EAAE;MACXrE,GAAG,CAACkE,IAAI,CAAC+J,IAAI,CAACpO,CAAC,EAAE+I,MAAM,CAAC9I,CAAC,EAAEmO,IAAI,CAAC9J,KAAK,EAAE8J,IAAI,CAAC7J,MAAM,EAAEC,SAAS,CAAC;IAClE;EACJ,CAAC,MACI,IAAI,OAAO+J,UAAU,CAACpM,SAAS,KAAK,QAAQ,EAAE;IAC/C;IACA,IAAIoM,UAAU,CAAC3K,SAAS,EAAE;MACtBzD,GAAG,CAACkE,IAAI,CAAC+J,IAAI,CAACpO,CAAC,EAAE+I,MAAM,CAAC9I,CAAC,EAAEmO,IAAI,CAAC9J,KAAK,EAAE8J,IAAI,CAAC7J,MAAM,EAAE,GAAG,CAAC;IAC5D;IACA;IACAkb,eAAe,CAACtf,GAAG,EAAEiO,IAAI,EAAErF,MAAM,EAAEwF,UAAU,CAACpM,SAAS,CAAC;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsd,eAAeA,CAACtf,GAAG,EAAEiO,IAAI,EAAErF,MAAM,EAAE5G,SAAS,EAAE;EACnD,IAAIud,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAI1d,SAAS,CAACsH,GAAG,EAAE;IACfiW,EAAE,GAAG3W,MAAM,CAAC/I,CAAC;IACb2f,EAAE,GAAG5W,MAAM,CAAC9I,CAAC;IACb2f,EAAE,GAAG7W,MAAM,CAAC/I,CAAC,GAAGoO,IAAI,CAAC9J,KAAK;IAC1Bub,EAAE,GAAG9W,MAAM,CAAC9I,CAAC;IACb,IAAIkC,SAAS,CAACuH,KAAK,EAAE;MACjBkW,EAAE,IAAI,GAAG,GAAGzd,SAAS,CAACuH,KAAK;IAC/B;IACA,IAAIvH,SAAS,CAACyH,IAAI,EAAE;MAChB8V,EAAE,IAAI,GAAG,GAAGvd,SAAS,CAACyH,IAAI;IAC9B;IACAR,QAAQ,CAACjH,SAAS,CAACsH,GAAG,EAAEiW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC3C;EACA,IAAI1d,SAAS,CAACwH,MAAM,EAAE;IAClB+V,EAAE,GAAG3W,MAAM,CAAC/I,CAAC;IACb2f,EAAE,GAAG5W,MAAM,CAAC9I,CAAC,GAAGmO,IAAI,CAAC7J,MAAM;IAC3Bqb,EAAE,GAAG7W,MAAM,CAAC/I,CAAC,GAAGoO,IAAI,CAAC9J,KAAK;IAC1Bub,EAAE,GAAG9W,MAAM,CAAC9I,CAAC,GAAGmO,IAAI,CAAC7J,MAAM;IAC3B,IAAIpC,SAAS,CAACuH,KAAK,EAAE;MACjBkW,EAAE,IAAI,GAAG,GAAGzd,SAAS,CAACuH,KAAK;IAC/B;IACA,IAAIvH,SAAS,CAACyH,IAAI,EAAE;MAChB8V,EAAE,IAAI,GAAG,GAAGvd,SAAS,CAACyH,IAAI;IAC9B;IACAR,QAAQ,CAACjH,SAAS,CAACwH,MAAM,EAAE+V,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC9C;EACA,IAAI1d,SAAS,CAACyH,IAAI,EAAE;IAChB8V,EAAE,GAAG3W,MAAM,CAAC/I,CAAC;IACb2f,EAAE,GAAG5W,MAAM,CAAC9I,CAAC;IACb2f,EAAE,GAAG7W,MAAM,CAAC/I,CAAC;IACb6f,EAAE,GAAG9W,MAAM,CAAC9I,CAAC,GAAGmO,IAAI,CAAC7J,MAAM;IAC3B,IAAIpC,SAAS,CAACsH,GAAG,EAAE;MACfkW,EAAE,IAAI,GAAG,GAAGxd,SAAS,CAACsH,GAAG;IAC7B;IACA,IAAItH,SAAS,CAACwH,MAAM,EAAE;MAClBkW,EAAE,IAAI,GAAG,GAAG1d,SAAS,CAACwH,MAAM;IAChC;IACAP,QAAQ,CAACjH,SAAS,CAACyH,IAAI,EAAE8V,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC5C;EACA,IAAI1d,SAAS,CAACuH,KAAK,EAAE;IACjBgW,EAAE,GAAG3W,MAAM,CAAC/I,CAAC,GAAGoO,IAAI,CAAC9J,KAAK;IAC1Bqb,EAAE,GAAG5W,MAAM,CAAC9I,CAAC;IACb2f,EAAE,GAAG7W,MAAM,CAAC/I,CAAC,GAAGoO,IAAI,CAAC9J,KAAK;IAC1Bub,EAAE,GAAG9W,MAAM,CAAC9I,CAAC,GAAGmO,IAAI,CAAC7J,MAAM;IAC3B,IAAIpC,SAAS,CAACsH,GAAG,EAAE;MACfkW,EAAE,IAAI,GAAG,GAAGxd,SAAS,CAACsH,GAAG;IAC7B;IACA,IAAItH,SAAS,CAACwH,MAAM,EAAE;MAClBkW,EAAE,IAAI,GAAG,GAAG1d,SAAS,CAACwH,MAAM;IAChC;IACAP,QAAQ,CAACjH,SAAS,CAACuH,KAAK,EAAEgW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC7C;EACA,SAASzW,QAAQA,CAAC9E,KAAK,EAAEob,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACrC1f,GAAG,CAACyE,WAAW,CAAC,CAAC,CAACX,YAAY,CAACK,KAAK,CAAC;IACrCnE,GAAG,CAACyE,WAAW,CAAC,CAAC,CAACkb,IAAI,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE,GAAG,CAAC;EAC/C;AACJ;AACA,SAAS1B,qBAAqBA,CAAChe,GAAG,EAAE2H,KAAK,EAAEmV,SAAS,EAAElU,MAAM,EAAE;EAC1D,IAAIgX,mBAAmB,GAAGjY,KAAK,CAACkB,QAAQ,CAACiB,MAAM,CAACN,MAAM;EACtD,IAAIiJ,QAAQ,GAAG9K,KAAK,CAACkB,QAAQ,CAAC4J,QAAQ;EACtC,IAAIA,QAAQ,KAAK,WAAW,IAAKA,QAAQ,KAAK,UAAU,IAAIqK,SAAU,EAAE;IACpE8C,mBAAmB,IAAIjY,KAAK,CAACmN,aAAa,CAACnN,KAAK,CAACkM,OAAO,CAAC;EAC7D;EACA,OAAO7T,GAAG,CAAC+E,QAAQ,CAAC,CAAC,CAACX,MAAM,GAAGwE,MAAM,CAAC9I,CAAC,GAAG8f,mBAAmB;AACjE;AACA,SAAShb,OAAOA,CAAC5E,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,EAAEiL,OAAO,EAAEgM,cAAc,EAAE;EACpE,IAAIhM,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,EAAE;EAAE;EACxC,IAAIgM,cAAc,KAAK,KAAK,CAAC,EAAE;IAAEA,cAAc,GAAG,KAAK;EAAE;EACzD7f,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;EAC/B,IAAIkG,KAAK,CAACkB,QAAQ,CAAC4J,QAAQ,KAAK,WAAW,IAAI,CAACoN,cAAc,EAAE;IAC5DlY,KAAK,CAACG,IAAI,CAACoM,OAAO,CAAC,UAAUvG,GAAG,EAAE;MAAE,OAAOkP,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,CAAC;IAAE,CAAC,CAAC;EAC7F;EACA;EACA;EACAlM,KAAK,CAAC0N,gBAAgB,CAACrV,GAAG,EAAE4I,MAAM,CAAC;EACnC,IAAIkB,MAAM,GAAGnC,KAAK,CAACkB,QAAQ,CAACiB,MAAM;EAClCpB,cAAc,CAAC1I,GAAG,EAAE2H,KAAK,EAAEgB,QAAQ,EAAEC,MAAM,CAAC;EAC5C+T,QAAQ,CAAC3c,GAAG,CAAC;EACb2H,KAAK,CAACxC,UAAU,EAAE;EAClByD,MAAM,CAAC/I,CAAC,GAAGiK,MAAM,CAACL,IAAI;EACtBb,MAAM,CAAC9I,CAAC,GAAGgK,MAAM,CAACR,GAAG;EACrBX,QAAQ,CAAC7I,CAAC,GAAGgK,MAAM,CAACR,GAAG;EACvB;EACA3B,KAAK,CAAC2N,qBAAqB,CAACtV,GAAG,EAAE4I,MAAM,CAAC;EACxC,IAAIjB,KAAK,CAACkB,QAAQ,CAAC6J,QAAQ,KAAK,WAAW,EAAE;IACzC/K,KAAK,CAACC,IAAI,CAACsM,OAAO,CAAC,UAAUvG,GAAG,EAAE;MAAE,OAAOkP,QAAQ,CAAC7c,GAAG,EAAE2H,KAAK,EAAEgG,GAAG,EAAE/E,MAAM,EAAEiL,OAAO,CAAC;IAAE,CAAC,CAAC;IACzF7T,GAAG,CAAC4C,WAAW,CAAC5C,GAAG,CAACyB,UAAU,CAAC;EACnC;AACJ;AACA,SAASkb,QAAQA,CAAC3c,GAAG,EAAE;EACnB,IAAIoP,OAAO,GAAGpP,GAAG,CAACmF,UAAU,CAAC,CAAC;EAC9BnF,GAAG,CAAC0E,OAAO,CAAC0K,OAAO,GAAG,CAAC,CAAC;EACxB,IAAI0Q,UAAU,GAAG9f,GAAG,CAACmF,UAAU,CAAC,CAAC;EACjC,IAAI2a,UAAU,KAAK1Q,OAAO,EAAE;IACxBpP,GAAG,CAAC4E,OAAO,CAAC,CAAC;IACb,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AAEA,SAASmb,WAAWA,CAACC,KAAK,EAAE;EACxB;EACAA,KAAK,CAACC,GAAG,CAACC,SAAS,GAAG,YAAY;IAC9B,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAI9Q,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGW,SAAS,CAAChP,MAAM,EAAEqO,EAAE,EAAE,EAAE;MAC1C8Q,IAAI,CAAC9Q,EAAE,CAAC,GAAGW,SAAS,CAACX,EAAE,CAAC;IAC5B;IACA,IAAIC,OAAO,GAAG6Q,IAAI,CAAC,CAAC,CAAC;IACrB,IAAInT,KAAK,GAAGmD,UAAU,CAAC,IAAI,EAAEb,OAAO,CAAC;IACrC,IAAI3H,KAAK,GAAG6R,WAAW,CAAC,IAAI,EAAExM,KAAK,CAAC;IACpCuP,SAAS,CAAC,IAAI,EAAE5U,KAAK,CAAC;IACtB,OAAO,IAAI;EACf,CAAC;EACD;EACAqY,KAAK,CAACC,GAAG,CAAC1b,aAAa,GAAG,KAAK;EAC/Byb,KAAK,CAACC,GAAG,CAACtgB,aAAa,GAAG,UAAUC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE;IACpDJ,aAAa,CAACC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE,IAAI,CAAC;EAC3C,CAAC;EACDigB,KAAK,CAACC,GAAG,CAACG,oBAAoB,GAAG,UAAU/d,QAAQ,EAAE;IACjDd,UAAU,CAACa,WAAW,CAACC,QAAQ,EAAE,IAAI,CAAC;IACtC,OAAO,IAAI;EACf,CAAC;EACD2d,KAAK,CAACI,oBAAoB,GAAG,UAAU/d,QAAQ,EAAErC,GAAG,EAAE;IAClDuB,UAAU,CAACa,WAAW,CAACC,QAAQ,EAAErC,GAAG,CAAC;EACzC,CAAC;EACDggB,KAAK,CAACC,GAAG,CAACI,mBAAmB,GAAG,UAAUC,SAAS,EAAEC,qBAAqB,EAAE;IACxE,IAAIzd,EAAE;IACN,IAAIyd,qBAAqB,KAAK,KAAK,CAAC,EAAE;MAAEA,qBAAqB,GAAG,KAAK;IAAE;IACvE,IAAI,OAAOrW,MAAM,KAAK,WAAW,EAAE;MAC/BqD,OAAO,CAAChH,KAAK,CAAC,2DAA2D,CAAC;MAC1E,OAAO,IAAI;IACf;IACA,IAAIvG,GAAG,GAAG,IAAIuB,UAAU,CAAC,IAAI,CAAC;IAC9B,IAAIwB,EAAE,GAAGgK,SAAS,CAAC/M,GAAG,EAAEsgB,SAAS,EAAEpW,MAAM,EAAEqW,qBAAqB,EAAE,KAAK,CAAC;MAAE3Y,IAAI,GAAG7E,EAAE,CAAC6E,IAAI;MAAEC,IAAI,GAAG9E,EAAE,CAAC8E,IAAI;IACxG,IAAIgM,OAAO,GAAG,CAAC,CAAC/Q,EAAE,GAAG8E,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI9E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,GAAG,CAAC,UAAU7F,CAAC,EAAE;MAAE,OAAOA,CAAC,CAAC+L,OAAO;IAAE,CAAC,CAAC,KAAK,EAAE;IACpH,OAAO;MAAEsF,OAAO,EAAEA,OAAO;MAAEpG,IAAI,EAAE5F,IAAI;MAAEuN,IAAI,EAAEvN;IAAK,CAAC;EACvD,CAAC;AACL;AAEA,IAAI/E,EAAE;AACN,SAASod,SAASA,CAAC1a,CAAC,EAAE8J,OAAO,EAAE;EAC3B,IAAItC,KAAK,GAAGmD,UAAU,CAAC3K,CAAC,EAAE8J,OAAO,CAAC;EAClC,IAAI3H,KAAK,GAAG6R,WAAW,CAAChU,CAAC,EAAEwH,KAAK,CAAC;EACjCuP,SAAS,CAAC/W,CAAC,EAAEmC,KAAK,CAAC;AACvB;AACA;AACA,SAAS6Y,aAAaA,CAAChb,CAAC,EAAE8J,OAAO,EAAE;EAC/B,IAAItC,KAAK,GAAGmD,UAAU,CAAC3K,CAAC,EAAE8J,OAAO,CAAC;EAClC,OAAOkK,WAAW,CAAChU,CAAC,EAAEwH,KAAK,CAAC;AAChC;AACA,SAASyT,WAAWA,CAACjb,CAAC,EAAEmC,KAAK,EAAE;EAC3B4U,SAAS,CAAC/W,CAAC,EAAEmC,KAAK,CAAC;AACvB;AACA,IAAI;EACA,IAAI,OAAOuC,MAAM,KAAK,WAAW,IAAIA,MAAM,EAAE;IACzC;IACA,IAAIwW,SAAS,GAAGxW,MAAM;IACtB,IAAI8V,KAAK,GAAGU,SAAS,CAACV,KAAK,KAAK,CAACld,EAAE,GAAG4d,SAAS,CAACC,KAAK,MAAM,IAAI,IAAI7d,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkd,KAAK,CAAC;IACrG,IAAIA,KAAK,EAAE;MACPD,WAAW,CAACC,KAAK,CAAC;IACtB;EACJ;AACJ,CAAC,CACD,OAAOzZ,KAAK,EAAE;EACVgH,OAAO,CAAChH,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;AAC5D;AAEA,SAAS4P,IAAI,EAAE5B,YAAY,EAAEqC,MAAM,EAAEtC,QAAQ,EAAEqB,GAAG,EAAEjB,KAAK,EAAE8L,aAAa,EAAEC,WAAW,EAAEV,WAAW,EAAEG,SAAS,EAAEA,SAAS,IAAIU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}