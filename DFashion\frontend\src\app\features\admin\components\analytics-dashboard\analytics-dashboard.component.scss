.analytics-dashboard {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

// Dashboard Header
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-content {
  flex: 1;
}

.dashboard-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;

  i {
    color: #667eea;
  }
}

.dashboard-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.date-range-select {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.export-dropdown {
  position: relative;

  .export-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
  }

  .export-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 10;

    button {
      display: block;
      width: 100%;
      padding: 8px 16px;
      border: none;
      background: none;
      text-align: left;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background: #f8f9fa;
      }
    }
  }

  &:hover .export-menu {
    display: block;
  }
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Dashboard Tabs
.dashboard-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background: white;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.tab-btn {
  padding: 12px 24px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  &:hover:not(.active) {
    background: #f8f9fa;
    color: #333;
  }
}

// Tab Content
.tab-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

// Overview Tab - Metrics Grid
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-4px);
  }
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;

  &.users-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.revenue-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  }

  &.orders-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  }

  &.conversion-icon {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  }
}

.metric-content {
  flex: 1;

  h3 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin: 0 0 4px 0;
  }

  p {
    font-size: 14px;
    color: #666;
    margin: 0 0 8px 0;
  }
}

.metric-change {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;

  &.positive {
    background: #d4edda;
    color: #155724;
  }

  &.negative {
    background: #f8d7da;
    color: #721c24;
  }
}

// Analytics Sections
.analytics-section {
  margin-bottom: 32px;

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
  }
}

// Categories List
.categories-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;

  .category-info {
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 4px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .revenue-amount {
    font-size: 18px;
    font-weight: 700;
    color: #28a745;
  }
}

// Trends List
.trends-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;

  .trend-info {
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 4px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .trend-indicator {
    font-size: 18px;
  }
}

// Social Media Tab
.social-platforms {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.platform-card {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
}

.sentiment-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
}

.platform-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .metric-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 600;
  }

  .metric-value {
    font-size: 18px;
    font-weight: 700;
    color: #333;
  }
}

.top-posts {
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
  }
}

.post-item {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e9ecef;

  .post-content {
    font-size: 14px;
    color: #333;
    margin: 0 0 8px 0;
    line-height: 1.4;
  }

  .post-stats {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #666;

    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

// Search Engine Tab
.search-overview {
  margin-bottom: 32px;
}

.search-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.search-metric {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;

  h3 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0 0 8px 0;
  }

  p {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.keywords-section {
  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
  }
}

.keywords-table {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px;
  background: #e9ecef;
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
  color: #333;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: white;
  }
}

.keyword-name {
  font-weight: 600;
}

.keyword-position {
  color: #666;
}

.keyword-volume {
  color: #28a745;
  font-weight: 600;
}

.keyword-difficulty {
  color: #dc3545;
  font-weight: 600;
}

.keyword-trend {
  display: flex;
  justify-content: center;
}

// Competitors Tab
.competitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.competitor-card {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
}

.competitor-header {
  margin-bottom: 20px;

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }

  .market-share {
    font-size: 14px;
    color: #666;
    font-weight: 600;
  }
}

.competitor-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.competitor-metric {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .metric-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 600;
  }

  .metric-value {
    font-size: 16px;
    font-weight: 700;
    color: #333;

    &.higher {
      color: #dc3545;
    }

    &.lower {
      color: #28a745;
    }
  }
}

.top-keywords {
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
  }
}

.keywords-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  background: white;
  color: #333;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #e9ecef;
}

// Scraping Tab
.scraping-section {
  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }

  .scraping-description {
    font-size: 16px;
    color: #666;
    margin: 0 0 32px 0;
  }
}

.scraping-tool {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
}

.tool-header {
  margin-bottom: 20px;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      color: #667eea;
    }
  }

  p {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.tool-form {
  display: flex;
  gap: 12px;
  align-items: center;
}

.scraping-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #667eea;
  }
}

.scraping-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.scraping-guidelines {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 12px;
  padding: 20px;
  margin-top: 24px;

  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #0066cc;
    margin: 0 0 12px 0;
  }

  ul {
    margin: 0;
    padding-left: 20px;
    color: #0066cc;

    li {
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 1.4;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .social-platforms,
  .competitors-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .analytics-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    padding: 16px;
  }

  .dashboard-title {
    font-size: 24px;
  }

  .tab-content {
    padding: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .platform-metrics,
  .competitor-metrics {
    grid-template-columns: 1fr;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .tool-form {
    flex-direction: column;
    align-items: stretch;
  }
}
