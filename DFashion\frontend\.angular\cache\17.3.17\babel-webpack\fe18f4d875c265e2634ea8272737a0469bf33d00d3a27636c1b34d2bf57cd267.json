{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { CategorySelectionComponent } from '../../../../shared/components/category-selection/category-selection.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/no-data-config.service\";\nimport * as i4 from \"../../../../core/services/advanced-search.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = () => [1, 2, 3, 4, 5];\nfunction SearchComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SearchComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchComponent_div_9_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_div_11_Template_div_click_0_listener() {\n      const category_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectCategoryForSearch(category_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 24);\n    i0.ɵɵelement(2, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r6.productCount, \" products\");\n  }\n}\nfunction SearchComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.dismissCategorySelection());\n    });\n    i0.ɵɵelement(6, \"i\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"p\");\n    i0.ɵɵtext(9, \"We found multiple categories that match your search. Please select one:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 19);\n    i0.ɵɵtemplate(11, SearchComponent_div_9_div_11_Template, 9, 2, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 21)(13, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchInAllCategories());\n    });\n    i0.ɵɵtext(14, \" Search in All Categories \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Choose Category for \\\"\", ctx_r2.searchQuery, \"\\\"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categoryOptions);\n  }\n}\nfunction SearchComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"app-category-selection\", 29);\n    i0.ɵɵlistener(\"categorySelected\", function SearchComponent_div_10_Template_app_category_selection_categorySelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCategorySelected($event));\n    })(\"searchAllRequested\", function SearchComponent_div_10_Template_app_category_selection_searchAllRequested_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchAllRequested($event));\n    })(\"refineSearchRequested\", function SearchComponent_div_10_Template_app_category_selection_refineSearchRequested_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRefineSearchRequested($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"categories\", ctx_r2.categoryOptions)(\"originalQuery\", ctx_r2.searchQuery);\n  }\n}\nfunction SearchComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"div\", 35);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Searching...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchComponent_div_11_div_3_div_6_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_6_span_3_Template_span_click_0_listener() {\n      const suggestion_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.searchFor(suggestion_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r10, \" \");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2, \"Related searches:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchComponent_div_11_div_3_div_6_span_3_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchSuggestions);\n  }\n}\nfunction SearchComponent_div_11_div_3_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r11);\n  }\n}\nfunction SearchComponent_div_11_div_3_app_no_data_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"title\", ctx_r2.noDataConfig.title)(\"message\", ctx_r2.noDataConfig.message)(\"iconClass\", ctx_r2.noDataConfig.iconClass)(\"containerClass\", ctx_r2.noDataConfig.containerClass)(\"showActions\", ctx_r2.noDataConfig.showActions)(\"primaryAction\", ctx_r2.noDataConfig.primaryAction)(\"secondaryAction\", ctx_r2.noDataConfig.secondaryAction)(\"suggestions\", ctx_r2.noDataConfig.suggestions)(\"suggestionsTitle\", ctx_r2.noDataConfig.suggestionsTitle);\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getDiscountPercentage(product_r13), \"% OFF\");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r13.price, \"1.0-0\"), \"\");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 86);\n  }\n  if (rf & 2) {\n    const i_r14 = ctx.index;\n    const product_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", i_r14 < product_r13.rating.average);\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83);\n    i0.ɵɵtemplate(2, SearchComponent_div_11_div_3_div_46_div_1_div_17_i_2_Template, 1, 2, \"i\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 85);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r13.rating.count, \")\");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_div_click_0_listener() {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r13._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵelement(2, \"img\", 66);\n    i0.ɵɵtemplate(3, SearchComponent_div_11_div_3_div_46_div_1_div_3_Template, 3, 1, \"div\", 67);\n    i0.ɵɵelementStart(4, \"div\", 68)(5, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_5_listener($event) {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.toggleWishlist(product_r13._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(6, \"i\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 70)(8, \"h3\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 71);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 72)(13, \"span\", 73);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SearchComponent_div_11_div_3_div_46_div_1_span_16_Template, 3, 4, \"span\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, SearchComponent_div_11_div_3_div_46_div_1_div_17_Template, 5, 3, \"div\", 75);\n    i0.ɵɵelementStart(18, \"div\", 76)(19, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_19_listener($event) {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.addToCart(product_r13._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 78);\n    i0.ɵɵtext(21, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_22_listener($event) {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.buyNow(product_r13._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(23, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r13.images == null ? null : product_r13.images[0]) || \"/assets/images/placeholder-product.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r13.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r13.discountPrice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r2.isInWishlist(product_r13._id) ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r13.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 10, product_r13.discountPrice || product_r13.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r13.discountPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r13.rating);\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, SearchComponent_div_11_div_3_div_46_div_1_Template, 24, 13, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchResults);\n  }\n}\nfunction SearchComponent_div_11_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"h2\");\n    i0.ɵɵtext(3, \"Search Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SearchComponent_div_11_div_3_div_6_Template, 4, 1, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 41)(9, \"label\");\n    i0.ɵɵtext(10, \"Category:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_11_div_3_Template_select_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedCategory, $event) || (ctx_r2.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_11_div_3_Template_select_change_11_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(12, \"option\", 43);\n    i0.ɵɵtext(13, \"All Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, SearchComponent_div_11_div_3_option_14_Template, 2, 2, \"option\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 41)(16, \"label\");\n    i0.ɵɵtext(17, \"Price Range:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_11_div_3_Template_select_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedPriceRange, $event) || (ctx_r2.selectedPriceRange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_11_div_3_Template_select_change_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(19, \"option\", 43);\n    i0.ɵɵtext(20, \"All Prices\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 45);\n    i0.ɵɵtext(22, \"Under \\u20B91,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 46);\n    i0.ɵɵtext(24, \"\\u20B91,000 - \\u20B92,500\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 47);\n    i0.ɵɵtext(26, \"\\u20B92,500 - \\u20B95,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"option\", 48);\n    i0.ɵɵtext(28, \"\\u20B95,000 - \\u20B910,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"option\", 49);\n    i0.ɵɵtext(30, \"Above \\u20B910,000\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 41)(32, \"label\");\n    i0.ɵɵtext(33, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_11_div_3_Template_select_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.sortBy, $event) || (ctx_r2.sortBy = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_11_div_3_Template_select_change_34_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(35, \"option\", 50);\n    i0.ɵɵtext(36, \"Relevance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"option\", 51);\n    i0.ɵɵtext(38, \"Price: Low to High\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"option\", 52);\n    i0.ɵɵtext(40, \"Price: High to Low\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"option\", 53);\n    i0.ɵɵtext(42, \"Newest First\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"option\", 54);\n    i0.ɵɵtext(44, \"Highest Rated\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(45, SearchComponent_div_11_div_3_app_no_data_45_Template, 1, 9, \"app-no-data\", 55)(46, SearchComponent_div_11_div_3_div_46_Template, 2, 1, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.totalCount || ctx_r2.searchResults.length, \" products found\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchSuggestions.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categories);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedPriceRange);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.sortBy);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.length > 0);\n  }\n}\nfunction SearchComponent_div_11_div_4_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_div_1_span_4_Template_span_click_0_listener() {\n      const search_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.searchFor(search_r16));\n    });\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"i\", 97);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_div_1_span_4_Template_i_click_3_listener($event) {\n      const search_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.removeRecentSearch(search_r16);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const search_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r16, \" \");\n  }\n}\nfunction SearchComponent_div_11_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"h3\");\n    i0.ɵɵtext(2, \"Recent Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 90);\n    i0.ɵɵtemplate(4, SearchComponent_div_11_div_4_div_1_span_4_Template, 4, 1, \"span\", 94);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentSearches);\n  }\n}\nfunction SearchComponent_div_11_div_4_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_span_6_Template_span_click_0_listener() {\n      const search_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.searchFor(search_r18));\n    });\n    i0.ɵɵelement(1, \"i\", 99);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const search_r18 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r18, \" \");\n  }\n}\nfunction SearchComponent_div_11_div_4_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_div_11_Template_div_click_0_listener() {\n      const category_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.browseCategory(category_r20.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 24);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r20 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(category_r20.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r20.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r20.count, \" items\");\n  }\n}\nfunction SearchComponent_div_11_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtemplate(1, SearchComponent_div_11_div_4_div_1_Template, 5, 1, \"div\", 88);\n    i0.ɵɵelementStart(2, \"div\", 89)(3, \"h3\");\n    i0.ɵɵtext(4, \"Popular Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 90);\n    i0.ɵɵtemplate(6, SearchComponent_div_11_div_4_span_6_Template, 3, 1, \"span\", 91);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 89)(8, \"h3\");\n    i0.ɵɵtext(9, \"Browse Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 92);\n    i0.ɵɵtemplate(11, SearchComponent_div_11_div_4_div_11_Template, 7, 4, \"div\", 93);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentSearches.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.popularSearches);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categoryList);\n  }\n}\nfunction SearchComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 3);\n    i0.ɵɵtemplate(2, SearchComponent_div_11_div_2_Template, 4, 0, \"div\", 31)(3, SearchComponent_div_11_div_3_Template, 47, 8, \"div\", 32)(4, SearchComponent_div_11_div_4_Template, 12, 3, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasSearched && !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.hasSearched && !ctx_r2.isLoading);\n  }\n}\nexport class SearchComponent {\n  constructor(route, router, productService, noDataConfigService, advancedSearchService) {\n    this.route = route;\n    this.router = router;\n    this.productService = productService;\n    this.noDataConfigService = noDataConfigService;\n    this.advancedSearchService = advancedSearchService;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.recentSearches = [];\n    this.isLoading = false;\n    this.hasSearched = false;\n    // Enhanced search features\n    this.showCategorySelection = false;\n    this.categoryOptions = [];\n    this.searchResult = null;\n    this.searchSuggestions = [];\n    this.totalCount = 0;\n    // Filters\n    this.selectedCategory = '';\n    this.selectedPriceRange = '';\n    this.sortBy = 'relevance';\n    this.categories = ['Men', 'Women', 'Kids', 'Accessories', 'Footwear', 'Electronics'];\n    // Data\n    this.popularSearches = ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Jackets', 'Accessories', 'Formal Wear', 'Casual Wear'];\n    this.categoryList = [{\n      name: 'Men',\n      icon: 'fas fa-male',\n      count: 1250\n    }, {\n      name: 'Women',\n      icon: 'fas fa-female',\n      count: 1890\n    }, {\n      name: 'Kids',\n      icon: 'fas fa-child',\n      count: 650\n    }, {\n      name: 'Accessories',\n      icon: 'fas fa-gem',\n      count: 890\n    }, {\n      name: 'Footwear',\n      icon: 'fas fa-shoe-prints',\n      count: 750\n    }, {\n      name: 'Electronics',\n      icon: 'fas fa-mobile-alt',\n      count: 450\n    }];\n    // Category mapping for intelligent search\n    this.categoryKeywords = {\n      'Men': ['men', 'mens', 'man', 'male', 'boy', 'boys', 'gentleman', 'kurta', 'kurtas'],\n      'Women': ['women', 'womens', 'woman', 'female', 'girl', 'girls', 'lady', 'ladies', 'saree', 'sarees', 'kurti', 'kurtis'],\n      'Kids': ['kids', 'children', 'child', 'baby', 'toddler', 'infant'],\n      'Accessories': ['accessories', 'jewelry', 'jewellery', 'watch', 'watches', 'belt', 'belts'],\n      'Footwear': ['shoes', 'footwear', 'sandals', 'boots', 'sneakers', 'heels'],\n      'Electronics': ['mobile', 'phone', 'laptop', 'tablet', 'headphones', 'earphones']\n    };\n    this.wishlistItems = [];\n    this.noDataConfig = {};\n    this.searchSubject = new Subject();\n    this.noDataConfig = this.noDataConfigService.getSearchConfig();\n  }\n  ngOnInit() {\n    this.loadRecentSearches();\n    this.loadWishlistItems();\n    // Check for query parameter\n    this.route.queryParams.subscribe(params => {\n      if (params['q']) {\n        this.searchQuery = params['q'];\n        this.performSearch();\n      }\n    });\n    // Setup search with debounce using AdvancedSearchService\n    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), switchMap(query => {\n      if (query.trim().length > 0) {\n        this.isLoading = true;\n        const searchQuery = {\n          query: query.trim(),\n          category: this.selectedCategory || undefined,\n          priceRange: this.getPriceRange().min || this.getPriceRange().max ? {\n            min: this.getPriceRange().min || 0,\n            max: this.getPriceRange().max || 999999\n          } : undefined,\n          sortBy: this.getSortBy(),\n          filters: this.getActiveFilters()\n        };\n        return this.advancedSearchService.search(searchQuery);\n      } else {\n        this.searchResults = [];\n        this.hasSearched = false;\n        this.isLoading = false;\n        this.showCategorySelection = false;\n        return [];\n      }\n    })).subscribe({\n      next: result => {\n        console.log('✅ Advanced search response received:', result);\n        // Handle category redirect for ambiguous queries\n        if (result.categoryRedirect?.shouldRedirect) {\n          this.showCategorySelection = true;\n          this.categoryOptions = result.categoryRedirect.categories;\n          this.searchResults = [];\n          this.hasSearched = false;\n        } else {\n          // Normal search results\n          this.showCategorySelection = false;\n          this.searchResults = result.products || [];\n          this.totalCount = result.totalCount || 0;\n          this.searchSuggestions = result.suggestions || [];\n          this.hasSearched = true;\n          console.log('✅ Search completed:', this.searchResults.length, 'results');\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('❌ Advanced search error:', error);\n        this.isLoading = false;\n        this.hasSearched = true;\n        this.searchResults = [];\n        this.showCategorySelection = false;\n        this.showErrorMessage('Search failed. Please try again.');\n      }\n    });\n  }\n  onSearchInput(event) {\n    this.searchQuery = event.target.value;\n    if (this.searchQuery.trim().length > 0) {\n      this.searchSubject.next(this.searchQuery);\n    } else {\n      this.searchResults = [];\n      this.hasSearched = false;\n      this.showCategorySelection = false;\n      this.categoryOptions = [];\n    }\n  }\n  // Method removed - now handled by AdvancedSearchService\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      this.saveRecentSearch(this.searchQuery.trim());\n      this.performSearch();\n    }\n  }\n  performSearch() {\n    if (this.searchQuery.trim()) {\n      this.searchSubject.next(this.searchQuery);\n    }\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.showCategorySelection = false;\n    this.categoryOptions = [];\n  }\n  // Category selection event handlers\n  onCategorySelected(category) {\n    console.log('Category selected:', category);\n    this.advancedSearchService.saveSearchToHistory(this.searchQuery);\n    // Navigation will be handled by the category selection component\n  }\n  onSearchAllRequested(query) {\n    console.log('Search all categories requested for:', query);\n    this.showCategorySelection = false;\n    this.performDirectSearch();\n  }\n  onRefineSearchRequested(query) {\n    console.log('Refine search requested for:', query);\n    this.showCategorySelection = false;\n    // Focus on search input for refinement\n    setTimeout(() => {\n      const searchInput = document.querySelector('.search-input');\n      if (searchInput) {\n        searchInput.focus();\n      }\n    }, 100);\n  }\n  performDirectSearch() {\n    if (this.searchQuery.trim()) {\n      const searchQuery = {\n        query: this.searchQuery.trim(),\n        category: this.selectedCategory || undefined,\n        sortBy: this.getSortBy(),\n        filters: this.getActiveFilters()\n      };\n      this.isLoading = true;\n      this.advancedSearchService.search(searchQuery).subscribe({\n        next: result => {\n          this.searchResults = result.products || [];\n          this.totalCount = result.totalCount || 0;\n          this.searchSuggestions = result.suggestions || [];\n          this.hasSearched = true;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Direct search error:', error);\n          this.isLoading = false;\n          this.hasSearched = true;\n          this.searchResults = [];\n        }\n      });\n    }\n  }\n  searchFor(term) {\n    this.searchQuery = term;\n    this.saveRecentSearch(term);\n    this.performSearch();\n  }\n  applyFilters() {\n    if (this.searchQuery) {\n      this.performSearch();\n    }\n  }\n  browseCategory(category) {\n    this.selectedCategory = category;\n    this.searchQuery = category;\n    this.performSearch();\n  }\n  selectCategoryForSearch(category) {\n    // Intelligent redirection based on category selection\n    this.selectedCategory = category.name;\n    this.showCategorySelection = false;\n    // Update search query to include category filter\n    const originalQuery = this.searchQuery;\n    this.searchQuery = `${originalQuery} in ${category.name}`;\n    // Navigate to filtered category page\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        q: originalQuery,\n        category: category.name.toLowerCase(),\n        intent: 'category'\n      }\n    });\n  }\n  dismissCategorySelection() {\n    this.showCategorySelection = false;\n    this.categoryOptions = [];\n  }\n  searchInAllCategories() {\n    this.showCategorySelection = false;\n    this.selectedCategory = '';\n    this.performSearch();\n  }\n  viewProduct(productId) {\n    this.router.navigate(['/shop/product', productId]);\n  }\n  toggleWishlist(productId) {\n    if (this.isInWishlist(productId)) {\n      this.wishlistItems = this.wishlistItems.filter(id => id !== productId);\n    } else {\n      this.wishlistItems.push(productId);\n    }\n    localStorage.setItem('wishlist', JSON.stringify(this.wishlistItems));\n  }\n  isInWishlist(productId) {\n    return this.wishlistItems.includes(productId);\n  }\n  addToCart(productId) {\n    // Add to cart logic\n    console.log('Added to cart:', productId);\n    this.showNotification('Added to cart!', 'success');\n  }\n  buyNow(productId) {\n    // Buy now logic\n    console.log('Buy now:', productId);\n    this.showNotification('Redirecting to checkout...', 'info');\n  }\n  getDiscountPercentage(product) {\n    if (product.discountPrice) {\n      return Math.round((product.price - product.discountPrice) / product.price * 100);\n    }\n    return 0;\n  }\n  removeRecentSearch(term) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n  loadRecentSearches() {\n    const saved = localStorage.getItem('recentSearches');\n    this.recentSearches = saved ? JSON.parse(saved) : [];\n  }\n  loadWishlistItems() {\n    const saved = localStorage.getItem('wishlist');\n    this.wishlistItems = saved ? JSON.parse(saved) : [];\n  }\n  saveRecentSearch(term) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    this.recentSearches.unshift(term);\n    this.recentSearches = this.recentSearches.slice(0, 10);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n  showErrorMessage(message) {\n    // Create error notification\n    const notification = document.createElement('div');\n    notification.className = 'search-error-notification';\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #f44336;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      animation: slideIn 0.3s ease;\n    `;\n    document.body.appendChild(notification);\n    setTimeout(() => {\n      notification.remove();\n    }, 3000);\n  }\n  getSortBy() {\n    return this.sortBy;\n  }\n  getActiveFilters() {\n    const filters = {};\n    // Add any active filters here\n    if (this.selectedCategory) {\n      filters.category = [this.selectedCategory];\n    }\n    return filters;\n  }\n  getPriceRange() {\n    if (!this.selectedPriceRange) return {\n      min: undefined,\n      max: undefined\n    };\n    const ranges = {\n      '0-1000': {\n        min: 0,\n        max: 1000\n      },\n      '1000-2500': {\n        min: 1000,\n        max: 2500\n      },\n      '2500-5000': {\n        min: 2500,\n        max: 5000\n      },\n      '5000-10000': {\n        min: 5000,\n        max: 10000\n      },\n      '10000+': {\n        min: 10000\n      }\n    };\n    return ranges[this.selectedPriceRange] || {\n      min: undefined,\n      max: undefined\n    };\n  }\n  getSortField() {\n    const sortMap = {\n      'relevance': 'createdAt',\n      'price-low': 'price',\n      'price-high': 'price',\n      'newest': 'createdAt',\n      'rating': 'rating'\n    };\n    return sortMap[this.sortBy] || 'createdAt';\n  }\n  getSortOrder() {\n    return this.sortBy === 'price-high' ? 'desc' : 'asc';\n  }\n  showNotification(message, type) {\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      right: 20px;\n      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};\n      color: white;\n      padding: 1rem 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 0.9rem;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n    document.body.appendChild(notification);\n    setTimeout(() => notification.remove(), 3000);\n  }\n  static {\n    this.ɵfac = function SearchComponent_Factory(t) {\n      return new (t || SearchComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.NoDataConfigService), i0.ɵɵdirectiveInject(i4.AdvancedSearchService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchComponent,\n      selectors: [[\"app-search\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 5,\n      consts: [[\"searchInput\", \"\"], [1, \"search-page\"], [1, \"search-header\"], [1, \"container\"], [1, \"search-bar-container\"], [1, \"search-input-wrapper\"], [1, \"fas\", \"fa-search\", \"search-icon\"], [\"type\", \"text\", \"placeholder\", \"Search for fashion, brands, and more...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"clear-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"category-selection-overlay\", 4, \"ngIf\"], [\"class\", \"category-selection-section\", 4, \"ngIf\"], [\"class\", \"search-content\", 4, \"ngIf\"], [1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"category-selection-overlay\"], [1, \"category-selection-modal\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"category-options\"], [\"class\", \"category-option\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-actions\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"category-option\", 3, \"click\"], [1, \"category-icon\"], [1, \"fas\", \"fa-tags\"], [1, \"category-details\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"category-selection-section\"], [3, \"categorySelected\", \"searchAllRequested\", \"refineSearchRequested\", \"categories\", \"originalQuery\"], [1, \"search-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"default-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"search-results\"], [1, \"results-header\"], [1, \"results-count\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [1, \"filters-section\"], [1, \"filter-group\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"0-1000\"], [\"value\", \"1000-2500\"], [\"value\", \"2500-5000\"], [\"value\", \"5000-10000\"], [\"value\", \"10000+\"], [\"value\", \"relevance\"], [\"value\", \"price-low\"], [\"value\", \"price-high\"], [\"value\", \"newest\"], [\"value\", \"rating\"], [3, \"title\", \"message\", \"iconClass\", \"containerClass\", \"showActions\", \"primaryAction\", \"secondaryAction\", \"suggestions\", \"suggestionsTitle\", 4, \"ngIf\"], [\"class\", \"results-grid\", 4, \"ngIf\"], [1, \"search-suggestions\"], [\"class\", \"suggestion-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-chip\", 3, \"click\"], [3, \"value\"], [3, \"title\", \"message\", \"iconClass\", \"containerClass\", \"showActions\", \"primaryAction\", \"secondaryAction\", \"suggestions\", \"suggestionsTitle\"], [1, \"results-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [\"class\", \"product-badge\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price-container\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"rating\", 4, \"ngIf\"], [1, \"product-buttons\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy\", 3, \"click\"], [1, \"product-badge\"], [1, \"original-price\"], [1, \"rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"fas\", \"fa-star\"], [1, \"default-content\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"section\"], [1, \"search-chips\"], [\"class\", \"search-chip popular\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"search-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-chip\", 3, \"click\"], [1, \"fas\", \"fa-clock\"], [1, \"fas\", \"fa-times\", 3, \"click\"], [1, \"search-chip\", \"popular\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [1, \"category-card\", 3, \"click\"]],\n      template: function SearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n          i0.ɵɵelement(5, \"i\", 6);\n          i0.ɵɵelementStart(6, \"input\", 7, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_Template_input_ngModelChange_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function SearchComponent_Template_input_input_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchInput($event));\n          })(\"keyup.enter\", function SearchComponent_Template_input_keyup_enter_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchSubmit());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, SearchComponent_button_8_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(9, SearchComponent_div_9_Template, 15, 2, \"div\", 9)(10, SearchComponent_div_10_Template, 2, 2, \"div\", 10)(11, SearchComponent_div_11_Template, 5, 3, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCategorySelection);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCategorySelection && ctx.categoryOptions.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showCategorySelection);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, FormsModule, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel, NoDataComponent, CategorySelectionComponent],\n      styles: [\".search-page[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 60px);\\n  background: #f8f9fa;\\n}\\n\\n.search-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 2rem 0;\\n  position: sticky;\\n  top: 60px;\\n  z-index: 100;\\n}\\n\\n.search-bar-container[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.search-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  color: #6c757d;\\n  z-index: 1;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem 1rem 1rem 3rem;\\n  border: 2px solid #e9ecef;\\n  border-radius: 50px;\\n  font-size: 1rem;\\n  outline: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.search-input[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  color: #6c757d;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n}\\n\\n.category-selection-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.5);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.category-selection-modal[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  max-width: 500px;\\n  width: 90%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.25rem;\\n  color: #6c757d;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  color: #495057;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n  font-size: 0.95rem;\\n}\\n\\n.category-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.category-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  border: 2px solid #e9ecef;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.category-option[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  background: #f8f9ff;\\n}\\n\\n.category-option[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  background: linear-gradient(135deg, #007bff, #0056b3);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.25rem;\\n}\\n\\n.category-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.category-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.category-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.85rem;\\n  color: #6c757d;\\n}\\n\\n.category-option[_ngcontent-%COMP%]   .fa-arrow-right[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e9ecef;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.search-content[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 1rem;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem 0;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #007bff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 1rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.results-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.results-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.results-count[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 0.9rem;\\n  color: #495057;\\n}\\n\\n.filter-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  font-size: 0.9rem;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem 2rem;\\n  background: white;\\n  border-radius: 8px;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #dee2e6;\\n  margin-bottom: 1rem;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.5rem;\\n  color: #495057;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 2rem;\\n}\\n\\n.suggested-searches[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  font-size: 1rem;\\n  color: #495057;\\n}\\n\\n.search-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n  justify-content: center;\\n}\\n\\n.search-tag[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #495057;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  transition: all 0.2s ease;\\n}\\n\\n.search-tag[_ngcontent-%COMP%]:hover {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.results-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n\\n.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.product-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.5rem;\\n  left: 0.5rem;\\n  background: #dc3545;\\n  color: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.5rem;\\n  right: 0.5rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  color: #6c757d;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  color: #dc3545;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #212529;\\n  line-height: 1.3;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.85rem;\\n  margin: 0 0 0.5rem 0;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.price-container[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 700;\\n  color: #28a745;\\n  margin-right: 0.5rem;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n  text-decoration: line-through;\\n}\\n\\n.rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.125rem;\\n}\\n\\n.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #dee2e6;\\n}\\n\\n.stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n.rating-count[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n}\\n\\n.product-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%], .btn-buy[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.5rem;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #495057;\\n  border: 1px solid #dee2e6;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n}\\n\\n.btn-buy[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.btn-buy[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.default-content[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n\\n.section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.search-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n\\n.search-chip[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dee2e6;\\n  color: #495057;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.search-chip[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  color: #007bff;\\n}\\n\\n.search-chip.popular[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #feca57);\\n  color: white;\\n  border: none;\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 2rem 1rem;\\n  border-radius: 12px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.category-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  background: linear-gradient(45deg, #007bff, #0056b3);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 1rem;\\n}\\n\\n.category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: white;\\n}\\n\\n.category-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.category-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .filters-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .results-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 1rem;\\n  }\\n  .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  }\\n  .search-header[_ngcontent-%COMP%] {\\n    padding: 1rem 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "debounceTime", "distinctUntilChanged", "switchMap", "Subject", "NoDataComponent", "CategorySelectionComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "SearchComponent_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "SearchComponent_div_9_div_11_Template_div_click_0_listener", "category_r6", "_r5", "$implicit", "selectCategoryForSearch", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "productCount", "SearchComponent_div_9_Template_button_click_5_listener", "_r4", "dismissCategorySelection", "ɵɵtemplate", "SearchComponent_div_9_div_11_Template", "SearchComponent_div_9_Template_button_click_13_listener", "searchInAllCategories", "searchQuery", "ɵɵproperty", "categoryOptions", "SearchComponent_div_10_Template_app_category_selection_categorySelected_1_listener", "$event", "_r7", "onCategorySelected", "SearchComponent_div_10_Template_app_category_selection_searchAllRequested_1_listener", "onSearchAllRequested", "SearchComponent_div_10_Template_app_category_selection_refineSearchRequested_1_listener", "onRefineSearchRequested", "SearchComponent_div_11_div_3_div_6_span_3_Template_span_click_0_listener", "suggestion_r10", "_r9", "searchFor", "SearchComponent_div_11_div_3_div_6_span_3_Template", "searchSuggestions", "category_r11", "noDataConfig", "title", "message", "iconClass", "containerClass", "showActions", "primaryAction", "secondaryAction", "suggestions", "<PERSON><PERSON><PERSON>le", "getDiscountPercentage", "product_r13", "ɵɵpipeBind2", "price", "ɵɵclassProp", "i_r14", "rating", "average", "SearchComponent_div_11_div_3_div_46_div_1_div_17_i_2_Template", "ɵɵpureFunction0", "_c0", "count", "SearchComponent_div_11_div_3_div_46_div_1_Template_div_click_0_listener", "_r12", "viewProduct", "_id", "SearchComponent_div_11_div_3_div_46_div_1_div_3_Template", "SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_5_listener", "toggleWishlist", "stopPropagation", "SearchComponent_div_11_div_3_div_46_div_1_span_16_Template", "SearchComponent_div_11_div_3_div_46_div_1_div_17_Template", "SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_19_listener", "addToCart", "SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_22_listener", "buyNow", "images", "ɵɵsanitizeUrl", "discountPrice", "ɵɵclassMap", "isInWishlist", "brand", "SearchComponent_div_11_div_3_div_46_div_1_Template", "searchResults", "SearchComponent_div_11_div_3_div_6_Template", "ɵɵtwoWayListener", "SearchComponent_div_11_div_3_Template_select_ngModelChange_11_listener", "_r8", "ɵɵtwoWayBindingSet", "selectedCate<PERSON><PERSON>", "SearchComponent_div_11_div_3_Template_select_change_11_listener", "applyFilters", "SearchComponent_div_11_div_3_option_14_Template", "SearchComponent_div_11_div_3_Template_select_ngModelChange_18_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SearchComponent_div_11_div_3_Template_select_change_18_listener", "SearchComponent_div_11_div_3_Template_select_ngModelChange_34_listener", "sortBy", "SearchComponent_div_11_div_3_Template_select_change_34_listener", "SearchComponent_div_11_div_3_app_no_data_45_Template", "SearchComponent_div_11_div_3_div_46_Template", "totalCount", "length", "ɵɵtwoWayProperty", "categories", "SearchComponent_div_11_div_4_div_1_span_4_Template_span_click_0_listener", "search_r16", "_r15", "SearchComponent_div_11_div_4_div_1_span_4_Template_i_click_3_listener", "removeRecentSearch", "SearchComponent_div_11_div_4_div_1_span_4_Template", "recentSearches", "SearchComponent_div_11_div_4_span_6_Template_span_click_0_listener", "search_r18", "_r17", "SearchComponent_div_11_div_4_div_11_Template_div_click_0_listener", "category_r20", "_r19", "browseCategory", "icon", "SearchComponent_div_11_div_4_div_1_Template", "SearchComponent_div_11_div_4_span_6_Template", "SearchComponent_div_11_div_4_div_11_Template", "popularSearches", "categoryList", "SearchComponent_div_11_div_2_Template", "SearchComponent_div_11_div_3_Template", "SearchComponent_div_11_div_4_Template", "isLoading", "hasSearched", "SearchComponent", "constructor", "route", "router", "productService", "noDataConfigService", "advancedSearchService", "showCategorySelection", "searchResult", "categoryKeywords", "wishlistItems", "searchSubject", "getSearchConfig", "ngOnInit", "loadRecentSearches", "loadWishlistItems", "queryParams", "subscribe", "params", "performSearch", "pipe", "query", "trim", "category", "undefined", "priceRange", "getPriceRange", "min", "max", "getSortBy", "filters", "getActiveFilters", "search", "next", "result", "console", "log", "categoryRedirect", "shouldRedirect", "products", "error", "showErrorMessage", "onSearchInput", "event", "target", "value", "onSearchSubmit", "saveRecentSearch", "saveSearchToHistory", "performDirectSearch", "setTimeout", "searchInput", "document", "querySelector", "focus", "term", "originalQuery", "navigate", "q", "toLowerCase", "intent", "productId", "filter", "id", "push", "localStorage", "setItem", "JSON", "stringify", "includes", "showNotification", "product", "Math", "round", "saved", "getItem", "parse", "unshift", "slice", "notification", "createElement", "className", "textContent", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "remove", "ranges", "getSortField", "sortMap", "getSortOrder", "type", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProductService", "i3", "NoDataConfigService", "i4", "AdvancedSearchService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SearchComponent_Template", "rf", "ctx", "SearchComponent_Template_input_ngModelChange_6_listener", "_r1", "SearchComponent_Template_input_input_6_listener", "SearchComponent_Template_input_keyup_enter_6_listener", "SearchComponent_button_8_Template", "SearchComponent_div_9_Template", "SearchComponent_div_10_Template", "SearchComponent_div_11_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i6", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\search\\pages\\search\\search.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { NoDataConfigService } from '../../../../core/services/no-data-config.service';\nimport { AdvancedSearchService, SearchQuery, SearchResult, CategoryOption } from '../../../../core/services/advanced-search.service';\nimport { CategorySelectionComponent } from '../../../../shared/components/category-selection/category-selection.component';\n\n@Component({\n  selector: 'app-search',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NoDataComponent, CategorySelectionComponent],\n  template: `\n    <div class=\"search-page\">\n      <!-- Search Header -->\n      <div class=\"search-header\">\n        <div class=\"container\">\n          <div class=\"search-bar-container\">\n            <div class=\"search-input-wrapper\">\n              <i class=\"fas fa-search search-icon\"></i>\n              <input \n                type=\"text\" \n                class=\"search-input\"\n                placeholder=\"Search for fashion, brands, and more...\"\n                [(ngModel)]=\"searchQuery\"\n                (input)=\"onSearchInput($event)\"\n                (keyup.enter)=\"onSearchSubmit()\"\n                #searchInput\n              >\n              <button \n                *ngIf=\"searchQuery\" \n                class=\"clear-btn\" \n                (click)=\"clearSearch()\"\n              >\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Intelligent Category Selection -->\n      <div *ngIf=\"showCategorySelection\" class=\"category-selection-overlay\">\n        <div class=\"category-selection-modal\">\n          <div class=\"modal-header\">\n            <h3>Choose Category for \"{{ searchQuery }}\"</h3>\n            <button class=\"close-btn\" (click)=\"dismissCategorySelection()\">\n              <i class=\"fas fa-times\"></i>\n            </button>\n          </div>\n\n          <div class=\"modal-content\">\n            <p>We found multiple categories that match your search. Please select one:</p>\n\n            <div class=\"category-options\">\n              <div\n                *ngFor=\"let category of categoryOptions\"\n                class=\"category-option\"\n                (click)=\"selectCategoryForSearch(category)\"\n              >\n                <div class=\"category-icon\">\n                  <i class=\"fas fa-tags\"></i>\n                </div>\n                <div class=\"category-details\">\n                  <h4>{{ category.name }}</h4>\n                  <p>{{ category.productCount }} products</p>\n                </div>\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n\n            <div class=\"modal-actions\">\n              <button class=\"btn-secondary\" (click)=\"searchInAllCategories()\">\n                Search in All Categories\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Category Selection (for ambiguous queries) -->\n      <div *ngIf=\"showCategorySelection && categoryOptions.length > 0\" class=\"category-selection-section\">\n        <app-category-selection\n          [categories]=\"categoryOptions\"\n          [originalQuery]=\"searchQuery\"\n          (categorySelected)=\"onCategorySelected($event)\"\n          (searchAllRequested)=\"onSearchAllRequested($event)\"\n          (refineSearchRequested)=\"onRefineSearchRequested($event)\">\n        </app-category-selection>\n      </div>\n\n      <!-- Search Content -->\n      <div class=\"search-content\" *ngIf=\"!showCategorySelection\">\n        <div class=\"container\">\n          <!-- Loading -->\n          <div *ngIf=\"isLoading\" class=\"loading-container\">\n            <div class=\"loading-spinner\"></div>\n            <p>Searching...</p>\n          </div>\n\n          <!-- Search Results -->\n          <div *ngIf=\"hasSearched && !isLoading\" class=\"search-results\">\n            <div class=\"results-header\">\n              <h2>Search Results</h2>\n              <span class=\"results-count\">{{ totalCount || searchResults.length }} products found</span>\n              <div *ngIf=\"searchSuggestions.length > 0\" class=\"search-suggestions\">\n                <p>Related searches:</p>\n                <span *ngFor=\"let suggestion of searchSuggestions\"\n                      class=\"suggestion-chip\"\n                      (click)=\"searchFor(suggestion)\">\n                  {{ suggestion }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Filters -->\n            <div class=\"filters-section\">\n              <div class=\"filter-group\">\n                <label>Category:</label>\n                <select [(ngModel)]=\"selectedCategory\" (change)=\"applyFilters()\">\n                  <option value=\"\">All Categories</option>\n                  <option *ngFor=\"let category of categories\" [value]=\"category\">{{ category }}</option>\n                </select>\n              </div>\n              <div class=\"filter-group\">\n                <label>Price Range:</label>\n                <select [(ngModel)]=\"selectedPriceRange\" (change)=\"applyFilters()\">\n                  <option value=\"\">All Prices</option>\n                  <option value=\"0-1000\">Under ₹1,000</option>\n                  <option value=\"1000-2500\">₹1,000 - ₹2,500</option>\n                  <option value=\"2500-5000\">₹2,500 - ₹5,000</option>\n                  <option value=\"5000-10000\">₹5,000 - ₹10,000</option>\n                  <option value=\"10000+\">Above ₹10,000</option>\n                </select>\n              </div>\n              <div class=\"filter-group\">\n                <label>Sort by:</label>\n                <select [(ngModel)]=\"sortBy\" (change)=\"applyFilters()\">\n                  <option value=\"relevance\">Relevance</option>\n                  <option value=\"price-low\">Price: Low to High</option>\n                  <option value=\"price-high\">Price: High to Low</option>\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"rating\">Highest Rated</option>\n                </select>\n              </div>\n            </div>\n\n            <!-- No Results -->\n            <app-no-data\n              *ngIf=\"searchResults.length === 0\"\n              [title]=\"noDataConfig.title\"\n              [message]=\"noDataConfig.message\"\n              [iconClass]=\"noDataConfig.iconClass\"\n              [containerClass]=\"noDataConfig.containerClass\"\n              [showActions]=\"noDataConfig.showActions\"\n              [primaryAction]=\"noDataConfig.primaryAction\"\n              [secondaryAction]=\"noDataConfig.secondaryAction\"\n              [suggestions]=\"noDataConfig.suggestions\"\n              [suggestionsTitle]=\"noDataConfig.suggestionsTitle\">\n            </app-no-data>\n\n            <!-- Results Grid -->\n            <div *ngIf=\"searchResults.length > 0\" class=\"results-grid\">\n              <div *ngFor=\"let product of searchResults\" class=\"product-card\" (click)=\"viewProduct(product._id)\">\n                <div class=\"product-image\">\n                  <img [src]=\"product.images?.[0] || '/assets/images/placeholder-product.jpg'\" [alt]=\"product.name\">\n                  <div class=\"product-badge\" *ngIf=\"product.discountPrice\">\n                    <span>{{ getDiscountPercentage(product) }}% OFF</span>\n                  </div>\n                  <div class=\"product-actions\">\n                    <button class=\"action-btn wishlist-btn\" (click)=\"toggleWishlist(product._id); $event.stopPropagation()\">\n                      <i [class]=\"isInWishlist(product._id) ? 'fas fa-heart' : 'far fa-heart'\"></i>\n                    </button>\n                  </div>\n                </div>\n                <div class=\"product-info\">\n                  <h3>{{ product.name }}</h3>\n                  <p class=\"brand\">{{ product.brand }}</p>\n                  <div class=\"price-container\">\n                    <span class=\"current-price\">₹{{ product.discountPrice || product.price | number:'1.0-0' }}</span>\n                    <span *ngIf=\"product.discountPrice\" class=\"original-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                  </div>\n                  <div class=\"rating\" *ngIf=\"product.rating\">\n                    <div class=\"stars\">\n                      <i *ngFor=\"let star of [1,2,3,4,5]; let i = index\" \n                         class=\"fas fa-star\" \n                         [class.filled]=\"i < product.rating.average\"></i>\n                    </div>\n                    <span class=\"rating-count\">({{ product.rating.count }})</span>\n                  </div>\n                  <div class=\"product-buttons\">\n                    <button class=\"btn-cart\" (click)=\"addToCart(product._id); $event.stopPropagation()\">\n                      <i class=\"fas fa-shopping-cart\"></i>\n                      Add to Cart\n                    </button>\n                    <button class=\"btn-buy\" (click)=\"buyNow(product._id); $event.stopPropagation()\">\n                      Buy Now\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Default Content (when not searching) -->\n          <div *ngIf=\"!hasSearched && !isLoading\" class=\"default-content\">\n            <!-- Recent Searches -->\n            <div *ngIf=\"recentSearches.length > 0\" class=\"section\">\n              <h3>Recent Searches</h3>\n              <div class=\"search-chips\">\n                <span *ngFor=\"let search of recentSearches\" class=\"search-chip\" (click)=\"searchFor(search)\">\n                  <i class=\"fas fa-clock\"></i>\n                  {{ search }}\n                  <i class=\"fas fa-times\" (click)=\"removeRecentSearch(search); $event.stopPropagation()\"></i>\n                </span>\n              </div>\n            </div>\n\n            <!-- Popular Searches -->\n            <div class=\"section\">\n              <h3>Popular Searches</h3>\n              <div class=\"search-chips\">\n                <span *ngFor=\"let search of popularSearches\" class=\"search-chip popular\" (click)=\"searchFor(search)\">\n                  <i class=\"fas fa-fire\"></i>\n                  {{ search }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Categories -->\n            <div class=\"section\">\n              <h3>Browse Categories</h3>\n              <div class=\"categories-grid\">\n                <div *ngFor=\"let category of categoryList\" class=\"category-card\" (click)=\"browseCategory(category.name)\">\n                  <div class=\"category-icon\">\n                    <i [class]=\"category.icon\"></i>\n                  </div>\n                  <h4>{{ category.name }}</h4>\n                  <p>{{ category.count }} items</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .search-page {\n      min-height: calc(100vh - 60px);\n      background: #f8f9fa;\n    }\n\n    .search-header {\n      background: white;\n      border-bottom: 1px solid #e9ecef;\n      padding: 2rem 0;\n      position: sticky;\n      top: 60px;\n      z-index: 100;\n    }\n\n    .search-bar-container {\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .search-input-wrapper {\n      position: relative;\n      display: flex;\n      align-items: center;\n    }\n\n    .search-icon {\n      position: absolute;\n      left: 1rem;\n      color: #6c757d;\n      z-index: 1;\n    }\n\n    .search-input {\n      width: 100%;\n      padding: 1rem 1rem 1rem 3rem;\n      border: 2px solid #e9ecef;\n      border-radius: 50px;\n      font-size: 1rem;\n      outline: none;\n      transition: all 0.3s ease;\n    }\n\n    .search-input:focus {\n      border-color: #007bff;\n      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n    }\n\n    .clear-btn {\n      position: absolute;\n      right: 1rem;\n      background: none;\n      border: none;\n      color: #6c757d;\n      cursor: pointer;\n      padding: 0.5rem;\n    }\n\n    .category-selection-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0, 0, 0, 0.5);\n      z-index: 1000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      animation: fadeIn 0.3s ease;\n    }\n\n    .category-selection-modal {\n      background: white;\n      border-radius: 12px;\n      max-width: 500px;\n      width: 90%;\n      max-height: 80vh;\n      overflow-y: auto;\n      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n      animation: slideUp 0.3s ease;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 1.5rem;\n      border-bottom: 1px solid #e9ecef;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: 1.25rem;\n      color: #6c757d;\n      cursor: pointer;\n      padding: 0.5rem;\n      border-radius: 50%;\n      transition: all 0.2s ease;\n    }\n\n    .close-btn:hover {\n      background: #f8f9fa;\n      color: #495057;\n    }\n\n    .modal-content {\n      padding: 1.5rem;\n    }\n\n    .modal-content p {\n      color: #6c757d;\n      margin-bottom: 1.5rem;\n      font-size: 0.95rem;\n    }\n\n    .category-options {\n      display: flex;\n      flex-direction: column;\n      gap: 0.75rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .category-option {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n      padding: 1rem;\n      border: 2px solid #e9ecef;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .category-option:hover {\n      border-color: #007bff;\n      background: #f8f9ff;\n    }\n\n    .category-option .category-icon {\n      width: 48px;\n      height: 48px;\n      background: linear-gradient(135deg, #007bff, #0056b3);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: 1.25rem;\n    }\n\n    .category-details {\n      flex: 1;\n    }\n\n    .category-details h4 {\n      margin: 0 0 0.25rem 0;\n      font-size: 1rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .category-details p {\n      margin: 0;\n      font-size: 0.85rem;\n      color: #6c757d;\n    }\n\n    .category-option .fa-arrow-right {\n      color: #6c757d;\n      font-size: 0.9rem;\n    }\n\n    .modal-actions {\n      text-align: center;\n      padding-top: 1rem;\n      border-top: 1px solid #e9ecef;\n    }\n\n    .btn-secondary {\n      background: #f8f9fa;\n      color: #6c757d;\n      border: 1px solid #dee2e6;\n      padding: 0.75rem 1.5rem;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 0.9rem;\n      font-weight: 500;\n      transition: all 0.2s ease;\n    }\n\n    .btn-secondary:hover {\n      background: #e9ecef;\n      color: #495057;\n    }\n\n    @keyframes fadeIn {\n      from { opacity: 0; }\n      to { opacity: 1; }\n    }\n\n    @keyframes slideUp {\n      from {\n        opacity: 0;\n        transform: translateY(30px);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0);\n      }\n    }\n\n    .search-content {\n      padding: 2rem 0;\n    }\n\n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 1rem;\n    }\n\n    .loading-container {\n      text-align: center;\n      padding: 4rem 0;\n    }\n\n    .loading-spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid #f3f3f3;\n      border-top: 4px solid #007bff;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto 1rem;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .results-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 2rem;\n    }\n\n    .results-header h2 {\n      margin: 0;\n      font-size: 1.5rem;\n      font-weight: 600;\n    }\n\n    .results-count {\n      color: #6c757d;\n      font-size: 0.9rem;\n    }\n\n    .filters-section {\n      display: flex;\n      gap: 2rem;\n      margin-bottom: 2rem;\n      padding: 1rem;\n      background: white;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .filter-group {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n    }\n\n    .filter-group label {\n      font-weight: 500;\n      font-size: 0.9rem;\n      color: #495057;\n    }\n\n    .filter-group select {\n      padding: 0.5rem;\n      border: 1px solid #ced4da;\n      border-radius: 4px;\n      font-size: 0.9rem;\n    }\n\n    .no-results {\n      text-align: center;\n      padding: 4rem 2rem;\n      background: white;\n      border-radius: 8px;\n    }\n\n    .no-results i {\n      font-size: 4rem;\n      color: #dee2e6;\n      margin-bottom: 1rem;\n    }\n\n    .no-results h3 {\n      margin: 0 0 0.5rem 0;\n      font-size: 1.5rem;\n      color: #495057;\n    }\n\n    .no-results p {\n      color: #6c757d;\n      margin-bottom: 2rem;\n    }\n\n    .suggested-searches h4 {\n      margin: 0 0 1rem 0;\n      font-size: 1rem;\n      color: #495057;\n    }\n\n    .search-tags {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n      justify-content: center;\n    }\n\n    .search-tag {\n      background: #e9ecef;\n      color: #495057;\n      padding: 0.5rem 1rem;\n      border-radius: 20px;\n      cursor: pointer;\n      font-size: 0.9rem;\n      transition: all 0.2s ease;\n    }\n\n    .search-tag:hover {\n      background: #007bff;\n      color: white;\n    }\n\n    .results-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n      gap: 1.5rem;\n    }\n\n    .product-card {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .product-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n\n    .product-image {\n      position: relative;\n      width: 100%;\n      height: 200px;\n      overflow: hidden;\n    }\n\n    .product-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .product-card:hover .product-image img {\n      transform: scale(1.05);\n    }\n\n    .product-badge {\n      position: absolute;\n      top: 0.5rem;\n      left: 0.5rem;\n      background: #dc3545;\n      color: white;\n      padding: 0.25rem 0.5rem;\n      border-radius: 4px;\n      font-size: 0.75rem;\n      font-weight: 600;\n    }\n\n    .product-actions {\n      position: absolute;\n      top: 0.5rem;\n      right: 0.5rem;\n    }\n\n    .action-btn {\n      background: rgba(255, 255, 255, 0.9);\n      border: none;\n      width: 36px;\n      height: 36px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      color: #6c757d;\n    }\n\n    .action-btn:hover {\n      background: white;\n      color: #dc3545;\n    }\n\n    .product-info {\n      padding: 1rem;\n    }\n\n    .product-info h3 {\n      margin: 0 0 0.5rem 0;\n      font-size: 1rem;\n      font-weight: 600;\n      color: #212529;\n      line-height: 1.3;\n    }\n\n    .brand {\n      color: #6c757d;\n      font-size: 0.85rem;\n      margin: 0 0 0.5rem 0;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    .price-container {\n      margin-bottom: 0.5rem;\n    }\n\n    .current-price {\n      font-size: 1.125rem;\n      font-weight: 700;\n      color: #28a745;\n      margin-right: 0.5rem;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #6c757d;\n      text-decoration: line-through;\n    }\n\n    .rating {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      margin-bottom: 1rem;\n    }\n\n    .stars {\n      display: flex;\n      gap: 0.125rem;\n    }\n\n    .stars i {\n      font-size: 0.875rem;\n      color: #dee2e6;\n    }\n\n    .stars i.filled {\n      color: #ffc107;\n    }\n\n    .rating-count {\n      font-size: 0.8rem;\n      color: #6c757d;\n    }\n\n    .product-buttons {\n      display: flex;\n      gap: 0.5rem;\n    }\n\n    .btn-cart,\n    .btn-buy {\n      flex: 1;\n      padding: 0.5rem;\n      border: none;\n      border-radius: 6px;\n      font-size: 0.85rem;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-cart {\n      background: #f8f9fa;\n      color: #495057;\n      border: 1px solid #dee2e6;\n    }\n\n    .btn-cart:hover {\n      background: #e9ecef;\n    }\n\n    .btn-buy {\n      background: #007bff;\n      color: white;\n    }\n\n    .btn-buy:hover {\n      background: #0056b3;\n    }\n\n    .default-content {\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .section {\n      margin-bottom: 3rem;\n    }\n\n    .section h3 {\n      margin: 0 0 1rem 0;\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .search-chips {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n    }\n\n    .search-chip {\n      background: white;\n      border: 1px solid #dee2e6;\n      color: #495057;\n      padding: 0.5rem 1rem;\n      border-radius: 20px;\n      cursor: pointer;\n      font-size: 0.9rem;\n      transition: all 0.2s ease;\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .search-chip:hover {\n      border-color: #007bff;\n      color: #007bff;\n    }\n\n    .search-chip.popular {\n      background: linear-gradient(45deg, #ff6b6b, #feca57);\n      color: white;\n      border: none;\n    }\n\n    .categories-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 1rem;\n    }\n\n    .category-card {\n      background: white;\n      padding: 2rem 1rem;\n      border-radius: 12px;\n      text-align: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    }\n\n    .category-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n\n    .category-icon {\n      width: 60px;\n      height: 60px;\n      background: linear-gradient(45deg, #007bff, #0056b3);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 1rem;\n    }\n\n    .category-icon i {\n      font-size: 1.5rem;\n      color: white;\n    }\n\n    .category-card h4 {\n      margin: 0 0 0.5rem 0;\n      font-size: 1.125rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .category-card p {\n      margin: 0;\n      color: #6c757d;\n      font-size: 0.9rem;\n    }\n\n    @media (max-width: 768px) {\n      .filters-section {\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .results-grid {\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n        gap: 1rem;\n      }\n\n      .categories-grid {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n\n      .search-header {\n        padding: 1rem 0;\n      }\n    }\n  `]\n})\nexport class SearchComponent implements OnInit {\n  searchQuery = '';\n  searchResults: any[] = [];\n  recentSearches: string[] = [];\n  isLoading = false;\n  hasSearched = false;\n\n  // Enhanced search features\n  showCategorySelection = false;\n  categoryOptions: CategoryOption[] = [];\n  searchResult: SearchResult | null = null;\n  searchSuggestions: any[] = [];\n  totalCount = 0;\n\n  // Filters\n  selectedCategory = '';\n  selectedPriceRange = '';\n  sortBy = 'relevance';\n  categories: string[] = ['Men', 'Women', 'Kids', 'Accessories', 'Footwear', 'Electronics'];\n\n  // Data\n  popularSearches = ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Jackets', 'Accessories', 'Formal Wear', 'Casual Wear'];\n  categoryList = [\n    { name: 'Men', icon: 'fas fa-male', count: 1250 },\n    { name: 'Women', icon: 'fas fa-female', count: 1890 },\n    { name: 'Kids', icon: 'fas fa-child', count: 650 },\n    { name: 'Accessories', icon: 'fas fa-gem', count: 890 },\n    { name: 'Footwear', icon: 'fas fa-shoe-prints', count: 750 },\n    { name: 'Electronics', icon: 'fas fa-mobile-alt', count: 450 }\n  ];\n\n  // Category mapping for intelligent search\n  categoryKeywords: { [key: string]: string[] } = {\n    'Men': ['men', 'mens', 'man', 'male', 'boy', 'boys', 'gentleman', 'kurta', 'kurtas'],\n    'Women': ['women', 'womens', 'woman', 'female', 'girl', 'girls', 'lady', 'ladies', 'saree', 'sarees', 'kurti', 'kurtis'],\n    'Kids': ['kids', 'children', 'child', 'baby', 'toddler', 'infant'],\n    'Accessories': ['accessories', 'jewelry', 'jewellery', 'watch', 'watches', 'belt', 'belts'],\n    'Footwear': ['shoes', 'footwear', 'sandals', 'boots', 'sneakers', 'heels'],\n    'Electronics': ['mobile', 'phone', 'laptop', 'tablet', 'headphones', 'earphones']\n  };\n\n  wishlistItems: string[] = [];\n  noDataConfig: any = {};\n  private searchSubject = new Subject<string>();\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private productService: ProductService,\n    private noDataConfigService: NoDataConfigService,\n    private advancedSearchService: AdvancedSearchService\n  ) {\n    this.noDataConfig = this.noDataConfigService.getSearchConfig();\n  }\n\n  ngOnInit() {\n    this.loadRecentSearches();\n    this.loadWishlistItems();\n    \n    // Check for query parameter\n    this.route.queryParams.subscribe(params => {\n      if (params['q']) {\n        this.searchQuery = params['q'];\n        this.performSearch();\n      }\n    });\n\n    // Setup search with debounce using AdvancedSearchService\n    this.searchSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      switchMap(query => {\n        if (query.trim().length > 0) {\n          this.isLoading = true;\n          const searchQuery: SearchQuery = {\n            query: query.trim(),\n            category: this.selectedCategory || undefined,\n            priceRange: this.getPriceRange().min || this.getPriceRange().max ? {\n              min: this.getPriceRange().min || 0,\n              max: this.getPriceRange().max || 999999\n            } : undefined,\n            sortBy: this.getSortBy(),\n            filters: this.getActiveFilters()\n          };\n          return this.advancedSearchService.search(searchQuery);\n        } else {\n          this.searchResults = [];\n          this.hasSearched = false;\n          this.isLoading = false;\n          this.showCategorySelection = false;\n          return [];\n        }\n      })\n    ).subscribe({\n      next: (result: SearchResult) => {\n        console.log('✅ Advanced search response received:', result);\n\n        // Handle category redirect for ambiguous queries\n        if (result.categoryRedirect?.shouldRedirect) {\n          this.showCategorySelection = true;\n          this.categoryOptions = result.categoryRedirect.categories;\n          this.searchResults = [];\n          this.hasSearched = false;\n        } else {\n          // Normal search results\n          this.showCategorySelection = false;\n          this.searchResults = result.products || [];\n          this.totalCount = result.totalCount || 0;\n          this.searchSuggestions = result.suggestions || [];\n          this.hasSearched = true;\n          console.log('✅ Search completed:', this.searchResults.length, 'results');\n        }\n\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('❌ Advanced search error:', error);\n        this.isLoading = false;\n        this.hasSearched = true;\n        this.searchResults = [];\n        this.showCategorySelection = false;\n        this.showErrorMessage('Search failed. Please try again.');\n      }\n    });\n  }\n\n  onSearchInput(event: any) {\n    this.searchQuery = event.target.value;\n    if (this.searchQuery.trim().length > 0) {\n      this.searchSubject.next(this.searchQuery);\n    } else {\n      this.searchResults = [];\n      this.hasSearched = false;\n      this.showCategorySelection = false;\n      this.categoryOptions = [];\n    }\n  }\n\n  // Method removed - now handled by AdvancedSearchService\n\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      this.saveRecentSearch(this.searchQuery.trim());\n      this.performSearch();\n    }\n  }\n\n  performSearch() {\n    if (this.searchQuery.trim()) {\n      this.searchSubject.next(this.searchQuery);\n    }\n  }\n\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.showCategorySelection = false;\n    this.categoryOptions = [];\n  }\n\n  // Category selection event handlers\n  onCategorySelected(category: CategoryOption): void {\n    console.log('Category selected:', category);\n    this.advancedSearchService.saveSearchToHistory(this.searchQuery);\n    // Navigation will be handled by the category selection component\n  }\n\n  onSearchAllRequested(query: string): void {\n    console.log('Search all categories requested for:', query);\n    this.showCategorySelection = false;\n    this.performDirectSearch();\n  }\n\n  onRefineSearchRequested(query: string): void {\n    console.log('Refine search requested for:', query);\n    this.showCategorySelection = false;\n    // Focus on search input for refinement\n    setTimeout(() => {\n      const searchInput = document.querySelector('.search-input') as HTMLInputElement;\n      if (searchInput) {\n        searchInput.focus();\n      }\n    }, 100);\n  }\n\n  private performDirectSearch(): void {\n    if (this.searchQuery.trim()) {\n      const searchQuery: SearchQuery = {\n        query: this.searchQuery.trim(),\n        category: this.selectedCategory || undefined,\n        sortBy: this.getSortBy(),\n        filters: this.getActiveFilters()\n      };\n\n      this.isLoading = true;\n      this.advancedSearchService.search(searchQuery).subscribe({\n        next: (result: SearchResult) => {\n          this.searchResults = result.products || [];\n          this.totalCount = result.totalCount || 0;\n          this.searchSuggestions = result.suggestions || [];\n          this.hasSearched = true;\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Direct search error:', error);\n          this.isLoading = false;\n          this.hasSearched = true;\n          this.searchResults = [];\n        }\n      });\n    }\n  }\n\n  searchFor(term: string) {\n    this.searchQuery = term;\n    this.saveRecentSearch(term);\n    this.performSearch();\n  }\n\n  applyFilters() {\n    if (this.searchQuery) {\n      this.performSearch();\n    }\n  }\n\n  browseCategory(category: string) {\n    this.selectedCategory = category;\n    this.searchQuery = category;\n    this.performSearch();\n  }\n\n  selectCategoryForSearch(category: any) {\n    // Intelligent redirection based on category selection\n    this.selectedCategory = category.name;\n    this.showCategorySelection = false;\n\n    // Update search query to include category filter\n    const originalQuery = this.searchQuery;\n    this.searchQuery = `${originalQuery} in ${category.name}`;\n\n    // Navigate to filtered category page\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        q: originalQuery,\n        category: category.name.toLowerCase(),\n        intent: 'category'\n      }\n    });\n  }\n\n  dismissCategorySelection() {\n    this.showCategorySelection = false;\n    this.categoryOptions = [];\n  }\n\n  searchInAllCategories() {\n    this.showCategorySelection = false;\n    this.selectedCategory = '';\n    this.performSearch();\n  }\n\n  viewProduct(productId: string) {\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  toggleWishlist(productId: string) {\n    if (this.isInWishlist(productId)) {\n      this.wishlistItems = this.wishlistItems.filter(id => id !== productId);\n    } else {\n      this.wishlistItems.push(productId);\n    }\n    localStorage.setItem('wishlist', JSON.stringify(this.wishlistItems));\n  }\n\n  isInWishlist(productId: string): boolean {\n    return this.wishlistItems.includes(productId);\n  }\n\n  addToCart(productId: string) {\n    // Add to cart logic\n    console.log('Added to cart:', productId);\n    this.showNotification('Added to cart!', 'success');\n  }\n\n  buyNow(productId: string) {\n    // Buy now logic\n    console.log('Buy now:', productId);\n    this.showNotification('Redirecting to checkout...', 'info');\n  }\n\n  getDiscountPercentage(product: any): number {\n    if (product.discountPrice) {\n      return Math.round(((product.price - product.discountPrice) / product.price) * 100);\n    }\n    return 0;\n  }\n\n  removeRecentSearch(term: string) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n\n  private loadRecentSearches() {\n    const saved = localStorage.getItem('recentSearches');\n    this.recentSearches = saved ? JSON.parse(saved) : [];\n  }\n\n  private loadWishlistItems() {\n    const saved = localStorage.getItem('wishlist');\n    this.wishlistItems = saved ? JSON.parse(saved) : [];\n  }\n\n  private saveRecentSearch(term: string) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    this.recentSearches.unshift(term);\n    this.recentSearches = this.recentSearches.slice(0, 10);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n\n  private showErrorMessage(message: string) {\n    // Create error notification\n    const notification = document.createElement('div');\n    notification.className = 'search-error-notification';\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #f44336;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      animation: slideIn 0.3s ease;\n    `;\n\n    document.body.appendChild(notification);\n\n    setTimeout(() => {\n      notification.remove();\n    }, 3000);\n  }\n\n  private getSortBy(): 'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest' {\n    return this.sortBy as 'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest';\n  }\n\n  private getActiveFilters(): any {\n    const filters: any = {};\n\n    // Add any active filters here\n    if (this.selectedCategory) {\n      filters.category = [this.selectedCategory];\n    }\n\n    return filters;\n  }\n\n  private getPriceRange() {\n    if (!this.selectedPriceRange) return { min: undefined, max: undefined };\n\n    const ranges: { [key: string]: { min?: number; max?: number } } = {\n      '0-1000': { min: 0, max: 1000 },\n      '1000-2500': { min: 1000, max: 2500 },\n      '2500-5000': { min: 2500, max: 5000 },\n      '5000-10000': { min: 5000, max: 10000 },\n      '10000+': { min: 10000 }\n    };\n\n    return ranges[this.selectedPriceRange] || { min: undefined, max: undefined };\n  }\n\n  private getSortField(): string {\n    const sortMap: { [key: string]: string } = {\n      'relevance': 'createdAt',\n      'price-low': 'price',\n      'price-high': 'price',\n      'newest': 'createdAt',\n      'rating': 'rating'\n    };\n    return sortMap[this.sortBy] || 'createdAt';\n  }\n\n  private getSortOrder(): 'asc' | 'desc' {\n    return this.sortBy === 'price-high' ? 'desc' : 'asc';\n  }\n\n  private showNotification(message: string, type: 'success' | 'info' | 'error') {\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n    \n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      right: 20px;\n      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};\n      color: white;\n      padding: 1rem 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 0.9rem;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n\n    document.body.appendChild(notification);\n    setTimeout(() => notification.remove(), 3000);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,QAAQ,MAAM;AAG9B,SAASC,eAAe,QAAQ,yDAAyD;AAGzF,SAASC,0BAA0B,QAAQ,+EAA+E;;;;;;;;;;;;IAuB5GC,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAEvBT,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAoBTX,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAU,2DAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,uBAAA,CAAAH,WAAA,CAAiC;IAAA,EAAC;IAE3Cb,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAU,SAAA,YAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,cAA8B,SACxB;IAAAD,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC5BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAiB,MAAA,GAAoC;IACzCjB,EADyC,CAAAW,YAAA,EAAI,EACvC;IACNX,EAAA,CAAAU,SAAA,YAAkC;IACpCV,EAAA,CAAAW,YAAA,EAAM;;;;IAJEX,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAmB,iBAAA,CAAAN,WAAA,CAAAO,IAAA,CAAmB;IACpBpB,EAAA,CAAAkB,SAAA,GAAoC;IAApClB,EAAA,CAAAqB,kBAAA,KAAAR,WAAA,CAAAS,YAAA,cAAoC;;;;;;IApB7CtB,EAHN,CAAAC,cAAA,cAAsE,cAC9B,cACV,SACpB;IAAAD,EAAA,CAAAiB,MAAA,GAAuC;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,iBAA+D;IAArCD,EAAA,CAAAE,UAAA,mBAAAqB,uDAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmB,wBAAA,EAA0B;IAAA,EAAC;IAC5DzB,EAAA,CAAAU,SAAA,YAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,cAA2B,QACtB;IAAAD,EAAA,CAAAiB,MAAA,8EAAuE;IAAAjB,EAAA,CAAAW,YAAA,EAAI;IAE9EX,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAA0B,UAAA,KAAAC,qCAAA,kBAIC;IAUH3B,EAAA,CAAAW,YAAA,EAAM;IAGJX,EADF,CAAAC,cAAA,eAA2B,kBACuC;IAAlCD,EAAA,CAAAE,UAAA,mBAAA0B,wDAAA;MAAA5B,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuB,qBAAA,EAAuB;IAAA,EAAC;IAC7D7B,EAAA,CAAAiB,MAAA,kCACF;IAIRjB,EAJQ,CAAAW,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAjCIX,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAAqB,kBAAA,2BAAAf,MAAA,CAAAwB,WAAA,OAAuC;IAWlB9B,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA0B,eAAA,CAAkB;;;;;;IA0B/ChC,EADF,CAAAC,cAAA,cAAoG,iCAMtC;IAA1DD,EAFA,CAAAE,UAAA,8BAAA+B,mFAAAC,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAoBF,MAAA,CAAA8B,kBAAA,CAAAF,MAAA,CAA0B;IAAA,EAAC,gCAAAG,qFAAAH,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACzBF,MAAA,CAAAgC,oBAAA,CAAAJ,MAAA,CAA4B;IAAA,EAAC,mCAAAK,wFAAAL,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAC1BF,MAAA,CAAAkC,uBAAA,CAAAN,MAAA,CAA+B;IAAA,EAAC;IAE7DlC,EADE,CAAAW,YAAA,EAAyB,EACrB;;;;IANFX,EAAA,CAAAkB,SAAA,EAA8B;IAC9BlB,EADA,CAAA+B,UAAA,eAAAzB,MAAA,CAAA0B,eAAA,CAA8B,kBAAA1B,MAAA,CAAAwB,WAAA,CACD;;;;;IAW7B9B,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAiB,MAAA,mBAAY;IACjBjB,EADiB,CAAAW,YAAA,EAAI,EACf;;;;;;IASAX,EAAA,CAAAC,cAAA,eAEsC;IAAhCD,EAAA,CAAAE,UAAA,mBAAAuC,yEAAA;MAAA,MAAAC,cAAA,GAAA1C,EAAA,CAAAI,aAAA,CAAAuC,GAAA,EAAA5B,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsC,SAAA,CAAAF,cAAA,CAAqB;IAAA,EAAC;IACnC1C,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAqB,kBAAA,MAAAqB,cAAA,MACF;;;;;IALA1C,EADF,CAAAC,cAAA,cAAqE,QAChE;IAAAD,EAAA,CAAAiB,MAAA,wBAAiB;IAAAjB,EAAA,CAAAW,YAAA,EAAI;IACxBX,EAAA,CAAA0B,UAAA,IAAAmB,kDAAA,mBAEsC;IAGxC7C,EAAA,CAAAW,YAAA,EAAM;;;;IALyBX,EAAA,CAAAkB,SAAA,GAAoB;IAApBlB,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAwC,iBAAA,CAAoB;;;;;IAc/C9C,EAAA,CAAAC,cAAA,iBAA+D;IAAAD,EAAA,CAAAiB,MAAA,GAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAS;;;;IAA1CX,EAAA,CAAA+B,UAAA,UAAAgB,YAAA,CAAkB;IAAC/C,EAAA,CAAAkB,SAAA,EAAc;IAAdlB,EAAA,CAAAmB,iBAAA,CAAA4B,YAAA,CAAc;;;;;IA2BnF/C,EAAA,CAAAU,SAAA,sBAWc;;;;IADZV,EARA,CAAA+B,UAAA,UAAAzB,MAAA,CAAA0C,YAAA,CAAAC,KAAA,CAA4B,YAAA3C,MAAA,CAAA0C,YAAA,CAAAE,OAAA,CACI,cAAA5C,MAAA,CAAA0C,YAAA,CAAAG,SAAA,CACI,mBAAA7C,MAAA,CAAA0C,YAAA,CAAAI,cAAA,CACU,gBAAA9C,MAAA,CAAA0C,YAAA,CAAAK,WAAA,CACN,kBAAA/C,MAAA,CAAA0C,YAAA,CAAAM,aAAA,CACI,oBAAAhD,MAAA,CAAA0C,YAAA,CAAAO,eAAA,CACI,gBAAAjD,MAAA,CAAA0C,YAAA,CAAAQ,WAAA,CACR,qBAAAlD,MAAA,CAAA0C,YAAA,CAAAS,gBAAA,CACU;;;;;IAS5CzD,EADF,CAAAC,cAAA,cAAyD,WACjD;IAAAD,EAAA,CAAAiB,MAAA,GAAyC;IACjDjB,EADiD,CAAAW,YAAA,EAAO,EAClD;;;;;IADEX,EAAA,CAAAkB,SAAA,GAAyC;IAAzClB,EAAA,CAAAqB,kBAAA,KAAAf,MAAA,CAAAoD,qBAAA,CAAAC,WAAA,WAAyC;;;;;IAa/C3D,EAAA,CAAAC,cAAA,eAA2D;IAAAD,EAAA,CAAAiB,MAAA,GAAqC;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA5CX,EAAA,CAAAkB,SAAA,EAAqC;IAArClB,EAAA,CAAAqB,kBAAA,WAAArB,EAAA,CAAA4D,WAAA,OAAAD,WAAA,CAAAE,KAAA,eAAqC;;;;;IAI9F7D,EAAA,CAAAU,SAAA,YAEmD;;;;;IAAhDV,EAAA,CAAA8D,WAAA,WAAAC,KAAA,GAAAJ,WAAA,CAAAK,MAAA,CAAAC,OAAA,CAA2C;;;;;IAHhDjE,EADF,CAAAC,cAAA,cAA2C,cACtB;IACjBD,EAAA,CAAA0B,UAAA,IAAAwC,6DAAA,gBAE+C;IACjDlE,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAiB,MAAA,GAA4B;IACzDjB,EADyD,CAAAW,YAAA,EAAO,EAC1D;;;;IALkBX,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAmE,eAAA,IAAAC,GAAA,EAAgB;IAIXpE,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAqB,kBAAA,MAAAsC,WAAA,CAAAK,MAAA,CAAAK,KAAA,MAA4B;;;;;;IAzB7DrE,EAAA,CAAAC,cAAA,cAAmG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAoE,wEAAA;MAAA,MAAAX,WAAA,GAAA3D,EAAA,CAAAI,aAAA,CAAAmE,IAAA,EAAAxD,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,WAAA,CAAAb,WAAA,CAAAc,GAAA,CAAwB;IAAA,EAAC;IAChGzE,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAU,SAAA,cAAkG;IAClGV,EAAA,CAAA0B,UAAA,IAAAgD,wDAAA,kBAAyD;IAIvD1E,EADF,CAAAC,cAAA,cAA6B,iBAC6E;IAAhED,EAAA,CAAAE,UAAA,mBAAAyE,2EAAAzC,MAAA;MAAA,MAAAyB,WAAA,GAAA3D,EAAA,CAAAI,aAAA,CAAAmE,IAAA,EAAAxD,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsE,cAAA,CAAAjB,WAAA,CAAAc,GAAA,CAA2B;MAAA,OAAAzE,EAAA,CAAAQ,WAAA,CAAE0B,MAAA,CAAA2C,eAAA,EAAwB;IAAA,EAAC;IACrG7E,EAAA,CAAAU,SAAA,QAA6E;IAGnFV,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;IAEJX,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAiB,MAAA,GAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC3BX,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAiB,MAAA,IAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAI;IAEtCX,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAiB,MAAA,IAA8D;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IACjGX,EAAA,CAAA0B,UAAA,KAAAoD,0DAAA,mBAA2D;IAC7D9E,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA0B,UAAA,KAAAqD,yDAAA,kBAA2C;IASzC/E,EADF,CAAAC,cAAA,eAA6B,kBACyD;IAA3DD,EAAA,CAAAE,UAAA,mBAAA8E,4EAAA9C,MAAA;MAAA,MAAAyB,WAAA,GAAA3D,EAAA,CAAAI,aAAA,CAAAmE,IAAA,EAAAxD,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA2E,SAAA,CAAAtB,WAAA,CAAAc,GAAA,CAAsB;MAAA,OAAAzE,EAAA,CAAAQ,WAAA,CAAE0B,MAAA,CAAA2C,eAAA,EAAwB;IAAA,EAAC;IACjF7E,EAAA,CAAAU,SAAA,aAAoC;IACpCV,EAAA,CAAAiB,MAAA,qBACF;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAgF;IAAxDD,EAAA,CAAAE,UAAA,mBAAAgF,4EAAAhD,MAAA;MAAA,MAAAyB,WAAA,GAAA3D,EAAA,CAAAI,aAAA,CAAAmE,IAAA,EAAAxD,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6E,MAAA,CAAAxB,WAAA,CAAAc,GAAA,CAAmB;MAAA,OAAAzE,EAAA,CAAAQ,WAAA,CAAE0B,MAAA,CAAA2C,eAAA,EAAwB;IAAA,EAAC;IAC7E7E,EAAA,CAAAiB,MAAA,iBACF;IAGNjB,EAHM,CAAAW,YAAA,EAAS,EACL,EACF,EACF;;;;;IAnCGX,EAAA,CAAAkB,SAAA,GAAuE;IAAClB,EAAxE,CAAA+B,UAAA,SAAA4B,WAAA,CAAAyB,MAAA,kBAAAzB,WAAA,CAAAyB,MAAA,kDAAApF,EAAA,CAAAqF,aAAA,CAAuE,QAAA1B,WAAA,CAAAvC,IAAA,CAAqB;IACrEpB,EAAA,CAAAkB,SAAA,EAA2B;IAA3BlB,EAAA,CAAA+B,UAAA,SAAA4B,WAAA,CAAA2B,aAAA,CAA2B;IAKhDtF,EAAA,CAAAkB,SAAA,GAAqE;IAArElB,EAAA,CAAAuF,UAAA,CAAAjF,MAAA,CAAAkF,YAAA,CAAA7B,WAAA,CAAAc,GAAA,oCAAqE;IAKxEzE,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAmB,iBAAA,CAAAwC,WAAA,CAAAvC,IAAA,CAAkB;IACLpB,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAmB,iBAAA,CAAAwC,WAAA,CAAA8B,KAAA,CAAmB;IAENzF,EAAA,CAAAkB,SAAA,GAA8D;IAA9DlB,EAAA,CAAAqB,kBAAA,WAAArB,EAAA,CAAA4D,WAAA,SAAAD,WAAA,CAAA2B,aAAA,IAAA3B,WAAA,CAAAE,KAAA,eAA8D;IACnF7D,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAA+B,UAAA,SAAA4B,WAAA,CAAA2B,aAAA,CAA2B;IAEftF,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAA+B,UAAA,SAAA4B,WAAA,CAAAK,MAAA,CAAoB;;;;;IApB/ChE,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAA0B,UAAA,IAAAgE,kDAAA,oBAAmG;IAsCrG1F,EAAA,CAAAW,YAAA,EAAM;;;;IAtCqBX,EAAA,CAAAkB,SAAA,EAAgB;IAAhBlB,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAqF,aAAA,CAAgB;;;;;;IA5DzC3F,EAFJ,CAAAC,cAAA,cAA8D,cAChC,SACtB;IAAAD,EAAA,CAAAiB,MAAA,qBAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IACvBX,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAiB,MAAA,GAAuD;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAC1FX,EAAA,CAAA0B,UAAA,IAAAkE,2CAAA,kBAAqE;IAQvE5F,EAAA,CAAAW,YAAA,EAAM;IAKFX,EAFJ,CAAAC,cAAA,cAA6B,cACD,YACjB;IAAAD,EAAA,CAAAiB,MAAA,iBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IACxBX,EAAA,CAAAC,cAAA,kBAAiE;IAAzDD,EAAA,CAAA6F,gBAAA,2BAAAC,uEAAA5D,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA2F,GAAA;MAAA,MAAAzF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAgG,kBAAA,CAAA1F,MAAA,CAAA2F,gBAAA,EAAA/D,MAAA,MAAA5B,MAAA,CAAA2F,gBAAA,GAAA/D,MAAA;MAAA,OAAAlC,EAAA,CAAAQ,WAAA,CAAA0B,MAAA;IAAA,EAA8B;IAAClC,EAAA,CAAAE,UAAA,oBAAAgG,gEAAA;MAAAlG,EAAA,CAAAI,aAAA,CAAA2F,GAAA;MAAA,MAAAzF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA6F,YAAA,EAAc;IAAA,EAAC;IAC9DnG,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAiB,MAAA,sBAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACxCX,EAAA,CAAA0B,UAAA,KAAA0E,+CAAA,qBAA+D;IAEnEpG,EADE,CAAAW,YAAA,EAAS,EACL;IAEJX,EADF,CAAAC,cAAA,eAA0B,aACjB;IAAAD,EAAA,CAAAiB,MAAA,oBAAY;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAC3BX,EAAA,CAAAC,cAAA,kBAAmE;IAA3DD,EAAA,CAAA6F,gBAAA,2BAAAQ,uEAAAnE,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA2F,GAAA;MAAA,MAAAzF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAgG,kBAAA,CAAA1F,MAAA,CAAAgG,kBAAA,EAAApE,MAAA,MAAA5B,MAAA,CAAAgG,kBAAA,GAAApE,MAAA;MAAA,OAAAlC,EAAA,CAAAQ,WAAA,CAAA0B,MAAA;IAAA,EAAgC;IAAClC,EAAA,CAAAE,UAAA,oBAAAqG,gEAAA;MAAAvG,EAAA,CAAAI,aAAA,CAAA2F,GAAA;MAAA,MAAAzF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA6F,YAAA,EAAc;IAAA,EAAC;IAChEnG,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAiB,MAAA,kBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACpCX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,yBAAY;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAC5CX,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,iCAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAClDX,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,iCAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAClDX,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAiB,MAAA,kCAAgB;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACpDX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,0BAAa;IAExCjB,EAFwC,CAAAW,YAAA,EAAS,EACtC,EACL;IAEJX,EADF,CAAAC,cAAA,eAA0B,aACjB;IAAAD,EAAA,CAAAiB,MAAA,gBAAQ;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IACvBX,EAAA,CAAAC,cAAA,kBAAuD;IAA/CD,EAAA,CAAA6F,gBAAA,2BAAAW,uEAAAtE,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA2F,GAAA;MAAA,MAAAzF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAgG,kBAAA,CAAA1F,MAAA,CAAAmG,MAAA,EAAAvE,MAAA,MAAA5B,MAAA,CAAAmG,MAAA,GAAAvE,MAAA;MAAA,OAAAlC,EAAA,CAAAQ,WAAA,CAAA0B,MAAA;IAAA,EAAoB;IAAClC,EAAA,CAAAE,UAAA,oBAAAwG,gEAAA;MAAA1G,EAAA,CAAAI,aAAA,CAAA2F,GAAA;MAAA,MAAAzF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA6F,YAAA,EAAc;IAAA,EAAC;IACpDnG,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,iBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAC5CX,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,0BAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACrDX,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAiB,MAAA,0BAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACtDX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,oBAAY;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAC5CX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,qBAAa;IAG1CjB,EAH0C,CAAAW,YAAA,EAAS,EACtC,EACL,EACF;IAiBNX,EAdA,CAAA0B,UAAA,KAAAiF,oDAAA,0BAUqD,KAAAC,4CAAA,kBAIM;IAwC7D5G,EAAA,CAAAW,YAAA,EAAM;;;;IAlG0BX,EAAA,CAAAkB,SAAA,GAAuD;IAAvDlB,EAAA,CAAAqB,kBAAA,KAAAf,MAAA,CAAAuG,UAAA,IAAAvG,MAAA,CAAAqF,aAAA,CAAAmB,MAAA,oBAAuD;IAC7E9G,EAAA,CAAAkB,SAAA,EAAkC;IAAlClB,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAwC,iBAAA,CAAAgE,MAAA,KAAkC;IAc9B9G,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAA+G,gBAAA,YAAAzG,MAAA,CAAA2F,gBAAA,CAA8B;IAEPjG,EAAA,CAAAkB,SAAA,GAAa;IAAblB,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA0G,UAAA,CAAa;IAKpChH,EAAA,CAAAkB,SAAA,GAAgC;IAAhClB,EAAA,CAAA+G,gBAAA,YAAAzG,MAAA,CAAAgG,kBAAA,CAAgC;IAWhCtG,EAAA,CAAAkB,SAAA,IAAoB;IAApBlB,EAAA,CAAA+G,gBAAA,YAAAzG,MAAA,CAAAmG,MAAA,CAAoB;IAY7BzG,EAAA,CAAAkB,SAAA,IAAgC;IAAhClB,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAqF,aAAA,CAAAmB,MAAA,OAAgC;IAa7B9G,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAqF,aAAA,CAAAmB,MAAA,KAA8B;;;;;;IAgDhC9G,EAAA,CAAAC,cAAA,eAA4F;IAA5BD,EAAA,CAAAE,UAAA,mBAAA+G,yEAAA;MAAA,MAAAC,UAAA,GAAAlH,EAAA,CAAAI,aAAA,CAAA+G,IAAA,EAAApG,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsC,SAAA,CAAAsE,UAAA,CAAiB;IAAA,EAAC;IACzFlH,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAiB,MAAA,GACA;IAAAjB,EAAA,CAAAC,cAAA,YAAuF;IAA/DD,EAAA,CAAAE,UAAA,mBAAAkH,sEAAAlF,MAAA;MAAA,MAAAgF,UAAA,GAAAlH,EAAA,CAAAI,aAAA,CAAA+G,IAAA,EAAApG,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+G,kBAAA,CAAAH,UAAA,CAA0B;MAAA,OAAAlH,EAAA,CAAAQ,WAAA,CAAE0B,MAAA,CAAA2C,eAAA,EAAwB;IAAA,EAAC;IACxF7E,EADyF,CAAAW,YAAA,EAAI,EACtF;;;;IAFLX,EAAA,CAAAkB,SAAA,GACA;IADAlB,EAAA,CAAAqB,kBAAA,MAAA6F,UAAA,MACA;;;;;IALJlH,EADF,CAAAC,cAAA,cAAuD,SACjD;IAAAD,EAAA,CAAAiB,MAAA,sBAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IACxBX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA0B,UAAA,IAAA4F,kDAAA,mBAA4F;IAMhGtH,EADE,CAAAW,YAAA,EAAM,EACF;;;;IANuBX,EAAA,CAAAkB,SAAA,GAAiB;IAAjBlB,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAiH,cAAA,CAAiB;;;;;;IAY1CvH,EAAA,CAAAC,cAAA,eAAqG;IAA5BD,EAAA,CAAAE,UAAA,mBAAAsH,mEAAA;MAAA,MAAAC,UAAA,GAAAzH,EAAA,CAAAI,aAAA,CAAAsH,IAAA,EAAA3G,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsC,SAAA,CAAA6E,UAAA,CAAiB;IAAA,EAAC;IAClGzH,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAqB,kBAAA,MAAAoG,UAAA,MACF;;;;;;IAQAzH,EAAA,CAAAC,cAAA,eAAyG;IAAxCD,EAAA,CAAAE,UAAA,mBAAAyH,kEAAA;MAAA,MAAAC,YAAA,GAAA5H,EAAA,CAAAI,aAAA,CAAAyH,IAAA,EAAA9G,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwH,cAAA,CAAAF,YAAA,CAAAxG,IAAA,CAA6B;IAAA,EAAC;IACtGpB,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAU,SAAA,QAA+B;IACjCV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC5BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAiB,MAAA,GAA0B;IAC/BjB,EAD+B,CAAAW,YAAA,EAAI,EAC7B;;;;IAJCX,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAAuF,UAAA,CAAAqC,YAAA,CAAAG,IAAA,CAAuB;IAExB/H,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAmB,iBAAA,CAAAyG,YAAA,CAAAxG,IAAA,CAAmB;IACpBpB,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAqB,kBAAA,KAAAuG,YAAA,CAAAvD,KAAA,WAA0B;;;;;IAjCrCrE,EAAA,CAAAC,cAAA,cAAgE;IAE9DD,EAAA,CAAA0B,UAAA,IAAAsG,2CAAA,kBAAuD;IAarDhI,EADF,CAAAC,cAAA,cAAqB,SACf;IAAAD,EAAA,CAAAiB,MAAA,uBAAgB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IACzBX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA0B,UAAA,IAAAuG,4CAAA,mBAAqG;IAKzGjI,EADE,CAAAW,YAAA,EAAM,EACF;IAIJX,EADF,CAAAC,cAAA,cAAqB,SACf;IAAAD,EAAA,CAAAiB,MAAA,wBAAiB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC1BX,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA0B,UAAA,KAAAwG,4CAAA,kBAAyG;IAS/GlI,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;IAnCEX,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAiH,cAAA,CAAAT,MAAA,KAA+B;IAeR9G,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA6H,eAAA,CAAkB;IAWjBnI,EAAA,CAAAkB,SAAA,GAAe;IAAflB,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA8H,YAAA,CAAe;;;;;IA5IjDpI,EADF,CAAAC,cAAA,cAA2D,aAClC;IAgHrBD,EA9GA,CAAA0B,UAAA,IAAA2G,qCAAA,kBAAiD,IAAAC,qCAAA,mBAMa,IAAAC,qCAAA,mBAwGE;IAuCpEvI,EADE,CAAAW,YAAA,EAAM,EACF;;;;IArJIX,EAAA,CAAAkB,SAAA,GAAe;IAAflB,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAkI,SAAA,CAAe;IAMfxI,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAmI,WAAA,KAAAnI,MAAA,CAAAkI,SAAA,CAA+B;IAwG/BxI,EAAA,CAAAkB,SAAA,EAAgC;IAAhClB,EAAA,CAAA+B,UAAA,UAAAzB,MAAA,CAAAmI,WAAA,KAAAnI,MAAA,CAAAkI,SAAA,CAAgC;;;AA2qBhD,OAAM,MAAOE,eAAe;EA6C1BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,mBAAwC,EACxCC,qBAA4C;IAJ5C,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,qBAAqB,GAArBA,qBAAqB;IAjD/B,KAAAlH,WAAW,GAAG,EAAE;IAChB,KAAA6D,aAAa,GAAU,EAAE;IACzB,KAAA4B,cAAc,GAAa,EAAE;IAC7B,KAAAiB,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAG,KAAK;IAEnB;IACA,KAAAQ,qBAAqB,GAAG,KAAK;IAC7B,KAAAjH,eAAe,GAAqB,EAAE;IACtC,KAAAkH,YAAY,GAAwB,IAAI;IACxC,KAAApG,iBAAiB,GAAU,EAAE;IAC7B,KAAA+D,UAAU,GAAG,CAAC;IAEd;IACA,KAAAZ,gBAAgB,GAAG,EAAE;IACrB,KAAAK,kBAAkB,GAAG,EAAE;IACvB,KAAAG,MAAM,GAAG,WAAW;IACpB,KAAAO,UAAU,GAAa,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,CAAC;IAEzF;IACA,KAAAmB,eAAe,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACtH,KAAAC,YAAY,GAAG,CACb;MAAEhH,IAAI,EAAE,KAAK;MAAE2G,IAAI,EAAE,aAAa;MAAE1D,KAAK,EAAE;IAAI,CAAE,EACjD;MAAEjD,IAAI,EAAE,OAAO;MAAE2G,IAAI,EAAE,eAAe;MAAE1D,KAAK,EAAE;IAAI,CAAE,EACrD;MAAEjD,IAAI,EAAE,MAAM;MAAE2G,IAAI,EAAE,cAAc;MAAE1D,KAAK,EAAE;IAAG,CAAE,EAClD;MAAEjD,IAAI,EAAE,aAAa;MAAE2G,IAAI,EAAE,YAAY;MAAE1D,KAAK,EAAE;IAAG,CAAE,EACvD;MAAEjD,IAAI,EAAE,UAAU;MAAE2G,IAAI,EAAE,oBAAoB;MAAE1D,KAAK,EAAE;IAAG,CAAE,EAC5D;MAAEjD,IAAI,EAAE,aAAa;MAAE2G,IAAI,EAAE,mBAAmB;MAAE1D,KAAK,EAAE;IAAG,CAAE,CAC/D;IAED;IACA,KAAA8E,gBAAgB,GAAgC;MAC9C,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC;MACpF,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;MACxH,MAAM,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;MAClE,aAAa,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;MAC3F,UAAU,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC;MAC1E,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW;KACjF;IAED,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAApG,YAAY,GAAQ,EAAE;IACd,KAAAqG,aAAa,GAAG,IAAIxJ,OAAO,EAAU;IAS3C,IAAI,CAACmD,YAAY,GAAG,IAAI,CAAC+F,mBAAmB,CAACO,eAAe,EAAE;EAChE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACb,KAAK,CAACc,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,GAAG,CAAC,EAAE;QACf,IAAI,CAAC9H,WAAW,GAAG8H,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAACC,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF;IACA,IAAI,CAACR,aAAa,CAACS,IAAI,CACrBpK,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACmK,KAAK,IAAG;MAChB,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAClD,MAAM,GAAG,CAAC,EAAE;QAC3B,IAAI,CAAC0B,SAAS,GAAG,IAAI;QACrB,MAAM1G,WAAW,GAAgB;UAC/BiI,KAAK,EAAEA,KAAK,CAACC,IAAI,EAAE;UACnBC,QAAQ,EAAE,IAAI,CAAChE,gBAAgB,IAAIiE,SAAS;UAC5CC,UAAU,EAAE,IAAI,CAACC,aAAa,EAAE,CAACC,GAAG,IAAI,IAAI,CAACD,aAAa,EAAE,CAACE,GAAG,GAAG;YACjED,GAAG,EAAE,IAAI,CAACD,aAAa,EAAE,CAACC,GAAG,IAAI,CAAC;YAClCC,GAAG,EAAE,IAAI,CAACF,aAAa,EAAE,CAACE,GAAG,IAAI;WAClC,GAAGJ,SAAS;UACbzD,MAAM,EAAE,IAAI,CAAC8D,SAAS,EAAE;UACxBC,OAAO,EAAE,IAAI,CAACC,gBAAgB;SAC/B;QACD,OAAO,IAAI,CAACzB,qBAAqB,CAAC0B,MAAM,CAAC5I,WAAW,CAAC;OACtD,MAAM;QACL,IAAI,CAAC6D,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC8C,WAAW,GAAG,KAAK;QACxB,IAAI,CAACD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACS,qBAAqB,GAAG,KAAK;QAClC,OAAO,EAAE;;IAEb,CAAC,CAAC,CACH,CAACU,SAAS,CAAC;MACVgB,IAAI,EAAGC,MAAoB,IAAI;QAC7BC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEF,MAAM,CAAC;QAE3D;QACA,IAAIA,MAAM,CAACG,gBAAgB,EAAEC,cAAc,EAAE;UAC3C,IAAI,CAAC/B,qBAAqB,GAAG,IAAI;UACjC,IAAI,CAACjH,eAAe,GAAG4I,MAAM,CAACG,gBAAgB,CAAC/D,UAAU;UACzD,IAAI,CAACrB,aAAa,GAAG,EAAE;UACvB,IAAI,CAAC8C,WAAW,GAAG,KAAK;SACzB,MAAM;UACL;UACA,IAAI,CAACQ,qBAAqB,GAAG,KAAK;UAClC,IAAI,CAACtD,aAAa,GAAGiF,MAAM,CAACK,QAAQ,IAAI,EAAE;UAC1C,IAAI,CAACpE,UAAU,GAAG+D,MAAM,CAAC/D,UAAU,IAAI,CAAC;UACxC,IAAI,CAAC/D,iBAAiB,GAAG8H,MAAM,CAACpH,WAAW,IAAI,EAAE;UACjD,IAAI,CAACiF,WAAW,GAAG,IAAI;UACvBoC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACnF,aAAa,CAACmB,MAAM,EAAE,SAAS,CAAC;;QAG1E,IAAI,CAAC0B,SAAS,GAAG,KAAK;MACxB,CAAC;MACD0C,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC1C,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC9C,aAAa,GAAG,EAAE;QACvB,IAAI,CAACsD,qBAAqB,GAAG,KAAK;QAClC,IAAI,CAACkC,gBAAgB,CAAC,kCAAkC,CAAC;MAC3D;KACD,CAAC;EACJ;EAEAC,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACvJ,WAAW,GAAGuJ,KAAK,CAACC,MAAM,CAACC,KAAK;IACrC,IAAI,IAAI,CAACzJ,WAAW,CAACkI,IAAI,EAAE,CAAClD,MAAM,GAAG,CAAC,EAAE;MACtC,IAAI,CAACuC,aAAa,CAACsB,IAAI,CAAC,IAAI,CAAC7I,WAAW,CAAC;KAC1C,MAAM;MACL,IAAI,CAAC6D,aAAa,GAAG,EAAE;MACvB,IAAI,CAAC8C,WAAW,GAAG,KAAK;MACxB,IAAI,CAACQ,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACjH,eAAe,GAAG,EAAE;;EAE7B;EAEA;EAEAwJ,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC1J,WAAW,CAACkI,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACyB,gBAAgB,CAAC,IAAI,CAAC3J,WAAW,CAACkI,IAAI,EAAE,CAAC;MAC9C,IAAI,CAACH,aAAa,EAAE;;EAExB;EAEAA,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC/H,WAAW,CAACkI,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACX,aAAa,CAACsB,IAAI,CAAC,IAAI,CAAC7I,WAAW,CAAC;;EAE7C;EAEArB,WAAWA,CAAA;IACT,IAAI,CAACqB,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC6D,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC8C,WAAW,GAAG,KAAK;IACxB,IAAI,CAACQ,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACjH,eAAe,GAAG,EAAE;EAC3B;EAEA;EACAI,kBAAkBA,CAAC6H,QAAwB;IACzCY,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEb,QAAQ,CAAC;IAC3C,IAAI,CAACjB,qBAAqB,CAAC0C,mBAAmB,CAAC,IAAI,CAAC5J,WAAW,CAAC;IAChE;EACF;EAEAQ,oBAAoBA,CAACyH,KAAa;IAChCc,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEf,KAAK,CAAC;IAC1D,IAAI,CAACd,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAAC0C,mBAAmB,EAAE;EAC5B;EAEAnJ,uBAAuBA,CAACuH,KAAa;IACnCc,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEf,KAAK,CAAC;IAClD,IAAI,CAACd,qBAAqB,GAAG,KAAK;IAClC;IACA2C,UAAU,CAAC,MAAK;MACd,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAAqB;MAC/E,IAAIF,WAAW,EAAE;QACfA,WAAW,CAACG,KAAK,EAAE;;IAEvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEQL,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAAC7J,WAAW,CAACkI,IAAI,EAAE,EAAE;MAC3B,MAAMlI,WAAW,GAAgB;QAC/BiI,KAAK,EAAE,IAAI,CAACjI,WAAW,CAACkI,IAAI,EAAE;QAC9BC,QAAQ,EAAE,IAAI,CAAChE,gBAAgB,IAAIiE,SAAS;QAC5CzD,MAAM,EAAE,IAAI,CAAC8D,SAAS,EAAE;QACxBC,OAAO,EAAE,IAAI,CAACC,gBAAgB;OAC/B;MAED,IAAI,CAACjC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACQ,qBAAqB,CAAC0B,MAAM,CAAC5I,WAAW,CAAC,CAAC6H,SAAS,CAAC;QACvDgB,IAAI,EAAGC,MAAoB,IAAI;UAC7B,IAAI,CAACjF,aAAa,GAAGiF,MAAM,CAACK,QAAQ,IAAI,EAAE;UAC1C,IAAI,CAACpE,UAAU,GAAG+D,MAAM,CAAC/D,UAAU,IAAI,CAAC;UACxC,IAAI,CAAC/D,iBAAiB,GAAG8H,MAAM,CAACpH,WAAW,IAAI,EAAE;UACjD,IAAI,CAACiF,WAAW,GAAG,IAAI;UACvB,IAAI,CAACD,SAAS,GAAG,KAAK;QACxB,CAAC;QACD0C,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC1C,SAAS,GAAG,KAAK;UACtB,IAAI,CAACC,WAAW,GAAG,IAAI;UACvB,IAAI,CAAC9C,aAAa,GAAG,EAAE;QACzB;OACD,CAAC;;EAEN;EAEA/C,SAASA,CAACqJ,IAAY;IACpB,IAAI,CAACnK,WAAW,GAAGmK,IAAI;IACvB,IAAI,CAACR,gBAAgB,CAACQ,IAAI,CAAC;IAC3B,IAAI,CAACpC,aAAa,EAAE;EACtB;EAEA1D,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrE,WAAW,EAAE;MACpB,IAAI,CAAC+H,aAAa,EAAE;;EAExB;EAEA/B,cAAcA,CAACmC,QAAgB;IAC7B,IAAI,CAAChE,gBAAgB,GAAGgE,QAAQ;IAChC,IAAI,CAACnI,WAAW,GAAGmI,QAAQ;IAC3B,IAAI,CAACJ,aAAa,EAAE;EACtB;EAEA7I,uBAAuBA,CAACiJ,QAAa;IACnC;IACA,IAAI,CAAChE,gBAAgB,GAAGgE,QAAQ,CAAC7I,IAAI;IACrC,IAAI,CAAC6H,qBAAqB,GAAG,KAAK;IAElC;IACA,MAAMiD,aAAa,GAAG,IAAI,CAACpK,WAAW;IACtC,IAAI,CAACA,WAAW,GAAG,GAAGoK,aAAa,OAAOjC,QAAQ,CAAC7I,IAAI,EAAE;IAEzD;IACA,IAAI,CAACyH,MAAM,CAACsD,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BzC,WAAW,EAAE;QACX0C,CAAC,EAAEF,aAAa;QAChBjC,QAAQ,EAAEA,QAAQ,CAAC7I,IAAI,CAACiL,WAAW,EAAE;QACrCC,MAAM,EAAE;;KAEX,CAAC;EACJ;EAEA7K,wBAAwBA,CAAA;IACtB,IAAI,CAACwH,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACjH,eAAe,GAAG,EAAE;EAC3B;EAEAH,qBAAqBA,CAAA;IACnB,IAAI,CAACoH,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAAChD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC4D,aAAa,EAAE;EACtB;EAEArF,WAAWA,CAAC+H,SAAiB;IAC3B,IAAI,CAAC1D,MAAM,CAACsD,QAAQ,CAAC,CAAC,eAAe,EAAEI,SAAS,CAAC,CAAC;EACpD;EAEA3H,cAAcA,CAAC2H,SAAiB;IAC9B,IAAI,IAAI,CAAC/G,YAAY,CAAC+G,SAAS,CAAC,EAAE;MAChC,IAAI,CAACnD,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKF,SAAS,CAAC;KACvE,MAAM;MACL,IAAI,CAACnD,aAAa,CAACsD,IAAI,CAACH,SAAS,CAAC;;IAEpCI,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC1D,aAAa,CAAC,CAAC;EACtE;EAEA5D,YAAYA,CAAC+G,SAAiB;IAC5B,OAAO,IAAI,CAACnD,aAAa,CAAC2D,QAAQ,CAACR,SAAS,CAAC;EAC/C;EAEAtH,SAASA,CAACsH,SAAiB;IACzB;IACA1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyB,SAAS,CAAC;IACxC,IAAI,CAACS,gBAAgB,CAAC,gBAAgB,EAAE,SAAS,CAAC;EACpD;EAEA7H,MAAMA,CAACoH,SAAiB;IACtB;IACA1B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyB,SAAS,CAAC;IAClC,IAAI,CAACS,gBAAgB,CAAC,4BAA4B,EAAE,MAAM,CAAC;EAC7D;EAEAtJ,qBAAqBA,CAACuJ,OAAY;IAChC,IAAIA,OAAO,CAAC3H,aAAa,EAAE;MACzB,OAAO4H,IAAI,CAACC,KAAK,CAAE,CAACF,OAAO,CAACpJ,KAAK,GAAGoJ,OAAO,CAAC3H,aAAa,IAAI2H,OAAO,CAACpJ,KAAK,GAAI,GAAG,CAAC;;IAEpF,OAAO,CAAC;EACV;EAEAwD,kBAAkBA,CAAC4E,IAAY;IAC7B,IAAI,CAAC1E,cAAc,GAAG,IAAI,CAACA,cAAc,CAACiF,MAAM,CAAC9B,MAAM,IAAIA,MAAM,KAAKuB,IAAI,CAAC;IAC3EU,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvF,cAAc,CAAC,CAAC;EAC7E;EAEQiC,kBAAkBA,CAAA;IACxB,MAAM4D,KAAK,GAAGT,YAAY,CAACU,OAAO,CAAC,gBAAgB,CAAC;IACpD,IAAI,CAAC9F,cAAc,GAAG6F,KAAK,GAAGP,IAAI,CAACS,KAAK,CAACF,KAAK,CAAC,GAAG,EAAE;EACtD;EAEQ3D,iBAAiBA,CAAA;IACvB,MAAM2D,KAAK,GAAGT,YAAY,CAACU,OAAO,CAAC,UAAU,CAAC;IAC9C,IAAI,CAACjE,aAAa,GAAGgE,KAAK,GAAGP,IAAI,CAACS,KAAK,CAACF,KAAK,CAAC,GAAG,EAAE;EACrD;EAEQ3B,gBAAgBA,CAACQ,IAAY;IACnC,IAAI,CAAC1E,cAAc,GAAG,IAAI,CAACA,cAAc,CAACiF,MAAM,CAAC9B,MAAM,IAAIA,MAAM,KAAKuB,IAAI,CAAC;IAC3E,IAAI,CAAC1E,cAAc,CAACgG,OAAO,CAACtB,IAAI,CAAC;IACjC,IAAI,CAAC1E,cAAc,GAAG,IAAI,CAACA,cAAc,CAACiG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACtDb,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvF,cAAc,CAAC,CAAC;EAC7E;EAEQ4D,gBAAgBA,CAACjI,OAAe;IACtC;IACA,MAAMuK,YAAY,GAAG3B,QAAQ,CAAC4B,aAAa,CAAC,KAAK,CAAC;IAClDD,YAAY,CAACE,SAAS,GAAG,2BAA2B;IACpDF,YAAY,CAACG,WAAW,GAAG1K,OAAO;IAClCuK,YAAY,CAACI,KAAK,CAACC,OAAO,GAAG;;;;;;;;;;;;KAY5B;IAEDhC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACP,YAAY,CAAC;IAEvC7B,UAAU,CAAC,MAAK;MACd6B,YAAY,CAACQ,MAAM,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQ1D,SAASA,CAAA;IACf,OAAO,IAAI,CAAC9D,MAAwE;EACtF;EAEQgE,gBAAgBA,CAAA;IACtB,MAAMD,OAAO,GAAQ,EAAE;IAEvB;IACA,IAAI,IAAI,CAACvE,gBAAgB,EAAE;MACzBuE,OAAO,CAACP,QAAQ,GAAG,CAAC,IAAI,CAAChE,gBAAgB,CAAC;;IAG5C,OAAOuE,OAAO;EAChB;EAEQJ,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9D,kBAAkB,EAAE,OAAO;MAAE+D,GAAG,EAAEH,SAAS;MAAEI,GAAG,EAAEJ;IAAS,CAAE;IAEvE,MAAMgE,MAAM,GAAsD;MAChE,QAAQ,EAAE;QAAE7D,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAI,CAAE;MAC/B,WAAW,EAAE;QAAED,GAAG,EAAE,IAAI;QAAEC,GAAG,EAAE;MAAI,CAAE;MACrC,WAAW,EAAE;QAAED,GAAG,EAAE,IAAI;QAAEC,GAAG,EAAE;MAAI,CAAE;MACrC,YAAY,EAAE;QAAED,GAAG,EAAE,IAAI;QAAEC,GAAG,EAAE;MAAK,CAAE;MACvC,QAAQ,EAAE;QAAED,GAAG,EAAE;MAAK;KACvB;IAED,OAAO6D,MAAM,CAAC,IAAI,CAAC5H,kBAAkB,CAAC,IAAI;MAAE+D,GAAG,EAAEH,SAAS;MAAEI,GAAG,EAAEJ;IAAS,CAAE;EAC9E;EAEQiE,YAAYA,CAAA;IAClB,MAAMC,OAAO,GAA8B;MACzC,WAAW,EAAE,WAAW;MACxB,WAAW,EAAE,OAAO;MACpB,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,WAAW;MACrB,QAAQ,EAAE;KACX;IACD,OAAOA,OAAO,CAAC,IAAI,CAAC3H,MAAM,CAAC,IAAI,WAAW;EAC5C;EAEQ4H,YAAYA,CAAA;IAClB,OAAO,IAAI,CAAC5H,MAAM,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;EACtD;EAEQuG,gBAAgBA,CAAC9J,OAAe,EAAEoL,IAAkC;IAC1E,MAAMb,YAAY,GAAG3B,QAAQ,CAAC4B,aAAa,CAAC,KAAK,CAAC;IAClDD,YAAY,CAACE,SAAS,GAAG,6BAA6BW,IAAI,EAAE;IAC5Db,YAAY,CAACG,WAAW,GAAG1K,OAAO;IAElCuK,YAAY,CAACI,KAAK,CAACC,OAAO,GAAG;;;;oBAIbQ,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGA,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;;;;;;;;;KASxF;IAEDxC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACP,YAAY,CAAC;IACvC7B,UAAU,CAAC,MAAM6B,YAAY,CAACQ,MAAM,EAAE,EAAE,IAAI,CAAC;EAC/C;;;uBA5ZWvF,eAAe,EAAA1I,EAAA,CAAAuO,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzO,EAAA,CAAAuO,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1O,EAAA,CAAAuO,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA5O,EAAA,CAAAuO,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAA9O,EAAA,CAAAuO,iBAAA,CAAAQ,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAftG,eAAe;MAAAuG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnP,EAAA,CAAAoP,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UAt2BhB1P,EALR,CAAAC,cAAA,aAAyB,aAEI,aACF,aACa,aACE;UAChCD,EAAA,CAAAU,SAAA,WAAyC;UACzCV,EAAA,CAAAC,cAAA,kBAQC;UAJCD,EAAA,CAAA6F,gBAAA,2BAAA+J,wDAAA1N,MAAA;YAAAlC,EAAA,CAAAI,aAAA,CAAAyP,GAAA;YAAA7P,EAAA,CAAAgG,kBAAA,CAAA2J,GAAA,CAAA7N,WAAA,EAAAI,MAAA,MAAAyN,GAAA,CAAA7N,WAAA,GAAAI,MAAA;YAAA,OAAAlC,EAAA,CAAAQ,WAAA,CAAA0B,MAAA;UAAA,EAAyB;UAEzBlC,EADA,CAAAE,UAAA,mBAAA4P,gDAAA5N,MAAA;YAAAlC,EAAA,CAAAI,aAAA,CAAAyP,GAAA;YAAA,OAAA7P,EAAA,CAAAQ,WAAA,CAASmP,GAAA,CAAAvE,aAAA,CAAAlJ,MAAA,CAAqB;UAAA,EAAC,yBAAA6N,sDAAA;YAAA/P,EAAA,CAAAI,aAAA,CAAAyP,GAAA;YAAA,OAAA7P,EAAA,CAAAQ,WAAA,CAChBmP,GAAA,CAAAnE,cAAA,EAAgB;UAAA,EAAC;UANlCxL,EAAA,CAAAW,YAAA,EAQC;UACDX,EAAA,CAAA0B,UAAA,IAAAsO,iCAAA,oBAIC;UAMThQ,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;UAqDNX,EAlDA,CAAA0B,UAAA,IAAAuO,8BAAA,kBAAsE,KAAAC,+BAAA,kBAuC8B,KAAAC,+BAAA,kBAWzC;UAyJ7DnQ,EAAA,CAAAW,YAAA,EAAM;;;UA7NMX,EAAA,CAAAkB,SAAA,GAAyB;UAAzBlB,EAAA,CAAA+G,gBAAA,YAAA4I,GAAA,CAAA7N,WAAA,CAAyB;UAMxB9B,EAAA,CAAAkB,SAAA,GAAiB;UAAjBlB,EAAA,CAAA+B,UAAA,SAAA4N,GAAA,CAAA7N,WAAA,CAAiB;UAYtB9B,EAAA,CAAAkB,SAAA,EAA2B;UAA3BlB,EAAA,CAAA+B,UAAA,SAAA4N,GAAA,CAAA1G,qBAAA,CAA2B;UAuC3BjJ,EAAA,CAAAkB,SAAA,EAAyD;UAAzDlB,EAAA,CAAA+B,UAAA,SAAA4N,GAAA,CAAA1G,qBAAA,IAAA0G,GAAA,CAAA3N,eAAA,CAAA8E,MAAA,KAAyD;UAWlC9G,EAAA,CAAAkB,SAAA,EAA4B;UAA5BlB,EAAA,CAAA+B,UAAA,UAAA4N,GAAA,CAAA1G,qBAAA,CAA4B;;;qBAjFnDzJ,YAAY,EAAA4Q,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE9Q,WAAW,EAAA+Q,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,0BAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,OAAA,EAAEhR,eAAe,EAAEC,0BAA0B;MAAAgR,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}