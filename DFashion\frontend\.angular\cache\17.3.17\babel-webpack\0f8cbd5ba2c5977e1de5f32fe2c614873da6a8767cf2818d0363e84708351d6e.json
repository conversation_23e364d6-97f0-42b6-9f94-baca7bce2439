{"ast": null, "code": "/*! @license DOMPurify 3.2.6 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */\n\nconst {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor\n} = Object;\nlet {\n  freeze,\n  seal,\n  create\n} = Object; // eslint-disable-line import/no-mutable-exports\nlet {\n  apply,\n  construct\n} = typeof Reflect !== 'undefined' && Reflect;\nif (!freeze) {\n  freeze = function freeze(x) {\n    return x;\n  };\n}\nif (!seal) {\n  seal = function seal(x) {\n    return x;\n  };\n}\nif (!apply) {\n  apply = function apply(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\nif (!construct) {\n  construct = function construct(Func, args) {\n    return new Func(...args);\n  };\n}\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySplice = unapply(Array.prototype.splice);\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\nconst regExpTest = unapply(RegExp.prototype.test);\nconst typeErrorCreate = unconstruct(TypeError);\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply(func) {\n  return function (thisArg) {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return apply(func, thisArg, args);\n  };\n}\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct(func) {\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return construct(func, args);\n  };\n}\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(set, array) {\n  let transformCaseFunc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n        element = lcElement;\n      }\n    }\n    set[element] = true;\n  }\n  return set;\n}\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray(array) {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n  return array;\n}\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone(object) {\n  const newObject = create(null);\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (value && typeof value === 'object' && value.constructor === Object) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n  return newObject;\n}\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n    object = getPrototypeOf(object);\n  }\n  function fallbackValue() {\n    return null;\n  }\n  return fallbackValue;\n}\nconst html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']);\nconst svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\nconst svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']);\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nconst svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\nconst mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']);\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nconst mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\nconst text = freeze(['#text']);\nconst html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'popover', 'popovertarget', 'popovertargetaction', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'wrap', 'xmlns', 'slot']);\nconst svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'amplitude', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'exponent', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'slope', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'tablevalues', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\nconst mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\nconst xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n// eslint-disable-next-line unicorn/better-regex\nconst MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nconst ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nconst TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nconst DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nconst ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nconst IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nconst IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nconst ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nconst DOCTYPE_NAME = seal(/^html$/i);\nconst CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\nvar EXPRESSIONS = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ARIA_ATTR: ARIA_ATTR,\n  ATTR_WHITESPACE: ATTR_WHITESPACE,\n  CUSTOM_ELEMENT: CUSTOM_ELEMENT,\n  DATA_ATTR: DATA_ATTR,\n  DOCTYPE_NAME: DOCTYPE_NAME,\n  ERB_EXPR: ERB_EXPR,\n  IS_ALLOWED_URI: IS_ALLOWED_URI,\n  IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n  MUSTACHE_EXPR: MUSTACHE_EXPR,\n  TMPLIT_EXPR: TMPLIT_EXPR\n});\n\n/* eslint-disable @typescript-eslint/indent */\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5,\n  // Deprecated\n  entityNode: 6,\n  // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12 // Deprecated\n};\nconst getGlobal = function getGlobal() {\n  return typeof window === 'undefined' ? null : window;\n};\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n  if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n    return null;\n  }\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      }\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n    return null;\n  }\n};\nconst _createHooksMap = function _createHooksMap() {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: []\n  };\n};\nfunction createDOMPurify() {\n  let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n  const DOMPurify = root => createDOMPurify(root);\n  DOMPurify.version = '3.2.6';\n  DOMPurify.removed = [];\n  if (!window || !window.document || window.document.nodeType !== NODE_TYPE.document || !window.Element) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n    return DOMPurify;\n  }\n  let {\n    document\n  } = window;\n  const originalDocument = document;\n  const currentScript = originalDocument.currentScript;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes\n  } = window;\n  const ElementPrototype = Element.prototype;\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName\n  } = document;\n  const {\n    importNode\n  } = originalDocument;\n  let hooks = _createHooksMap();\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT\n  } = EXPRESSIONS;\n  let {\n    IS_ALLOWED_URI: IS_ALLOWED_URI$1\n  } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(create(null, {\n    tagNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    attributeNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    allowCustomizedBuiltInElements: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: false\n    }\n  }));\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc = null;\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n  const formElement = document.createElement('form');\n  const isRegexOrFunction = function isRegexOrFunction(testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function _parseConfig() {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n    PARSER_MEDIA_TYPE =\n    // eslint-disable-next-line unicorn/prefer-includes\n    SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? DEFAULT_PARSER_MEDIA_TYPE : cfg.PARSER_MEDIA_TYPE;\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS') ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR') ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES') ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR') ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR, transformCaseFunc) : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS') ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS, transformCaseFunc) : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS') ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS') ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR') ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES') ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS = cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS = cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, html$1);\n        addToSet(ALLOWED_ATTR, html);\n      }\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, svg$1);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, svgFilters);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, mathMl$1);\n        addToSet(ALLOWED_ATTR, mathMl);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n    }\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n      }\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n      }\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n      }\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n    CONFIG = cfg;\n  };\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [...svg$1, ...svgFilters, ...svgDisallowed]);\n  const ALL_MATHML_TAGS = addToSet({}, [...mathMl$1, ...mathMlDisallowed]);\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function _checkValidNamespace(element) {\n    let parent = getParentNode(element);\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template'\n      };\n    }\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n      }\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n    }\n    // For XHTML and XML documents that support custom namespaces\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return true;\n    }\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function _forceRemove(node) {\n    arrayPush(DOMPurify.removed, {\n      element: node\n    });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function _removeAttribute(name, element) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element\n      });\n    }\n    element.removeAttribute(name);\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function _initDocument(dirty) {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n    }\n    const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n    const body = doc.body || doc.documentElement;\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n    }\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n    }\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function _createNodeIterator(root) {\n    return createNodeIterator.call(root.ownerDocument || root, root,\n    // eslint-disable-next-line no-bitwise\n    NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null);\n  };\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function _isClobbered(element) {\n    return element instanceof HTMLFormElement && (typeof element.nodeName !== 'string' || typeof element.textContent !== 'string' || typeof element.removeChild !== 'function' || !(element.attributes instanceof NamedNodeMap) || typeof element.removeAttribute !== 'function' || typeof element.setAttribute !== 'function' || typeof element.namespaceURI !== 'string' || typeof element.insertBefore !== 'function' || typeof element.hasChildNodes !== 'function');\n  };\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function _isNode(value) {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n  function _executeHooks(hooks, currentNode, data) {\n    arrayForEach(hooks, hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function _sanitizeElements(currentNode) {\n    let content = null;\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS\n    });\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (SAFE_FOR_XML && currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && regExpTest(/<[/\\w!]/g, currentNode.innerHTML) && regExpTest(/<[/\\w!]/g, currentNode.textContent)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any kind of possibly harmful comments */\n    if (SAFE_FOR_XML && currentNode.nodeType === NODE_TYPE.comment && regExpTest(/<[/\\w]/g, currentNode.data)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) {\n          return false;\n        }\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) {\n          return false;\n        }\n      }\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        content = stringReplace(content, expr, ' ');\n      });\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, {\n          element: currentNode.cloneNode()\n        });\n        currentNode.textContent = content;\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n    return false;\n  };\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n      return false;\n    }\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ;else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ;else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n      // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n      // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n      _isBasicCustomElement(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n      // Alternative, second condition checks if it's an `is`-attribute, AND\n      // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ;else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) ;else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ;else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ;else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ;else if (value) {\n      return false;\n    } else ;\n    return true;\n  };\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function _isBasicCustomElement(tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n    const {\n      attributes\n    } = currentNode;\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined\n    };\n    let l = attributes.length;\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const {\n        name,\n        namespaceURI,\n        value: attrValue\n      } = attr;\n      const lcName = transformCaseFunc(name);\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Handle attributes that require Trusted Types */\n      if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n        if (namespaceURI) ;else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML':\n              {\n                value = trustedTypesPolicy.createHTML(value);\n                break;\n              }\n            case 'TrustedScriptURL':\n              {\n                value = trustedTypesPolicy.createScriptURL(value);\n                break;\n              }\n          }\n        }\n      }\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n    while (shadowNode = shadowIterator.nextNode()) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty) {\n    let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === NODE_TYPE.element && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n      // eslint-disable-next-line unicorn/prefer-includes\n      dirty.indexOf('<') === -1) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      }\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n    /* Now start iterating over the created document */\n    while (currentNode = nodeIterator.nextNode()) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n      return returnNode;\n    }\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n    /* Serialize doctype if allowed */\n    if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n      serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n  };\n  DOMPurify.setConfig = function () {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n      return index === -1 ? undefined : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n    return arrayPop(hooks[entryPoint]);\n  };\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n  return DOMPurify;\n}\nvar purify = createDOMPurify();\nexport { purify as default };", "map": {"version": 3, "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayLastIndexOf", "lastIndexOf", "arrayPop", "pop", "arrayPush", "push", "arraySplice", "splice", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "lastIndex", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "undefined", "l", "element", "lcElement", "cleanArray", "index", "isPropertyExist", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "svg", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "EXPRESSIONS", "__proto__", "NODE_TYPE", "attribute", "cdataSection", "entityReference", "entityNode", "progressingInstruction", "comment", "document", "documentType", "documentFragment", "notation", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "console", "warn", "_createHooksMap", "afterSanitizeAttributes", "afterSanitizeElements", "afterSanitizeShadowDOM", "beforeSanitizeAttributes", "beforeSanitizeElements", "beforeSanitizeShadowDOM", "uponSanitizeAttribute", "uponSanitizeElement", "uponSanitizeShadowNode", "createDOMPurify", "DOMPurify", "root", "version", "removed", "nodeType", "Element", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHooks", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "childCount", "i", "child<PERSON>lone", "__removalCount", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "attr", "initValue", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "entryPoint", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks", "purify", "default"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/dompurify/dist/purify.es.mjs"], "sourcesContent": ["/*! @license DOMPurify 3.2.6 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */\n\nconst {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor\n} = Object;\nlet {\n  freeze,\n  seal,\n  create\n} = Object; // eslint-disable-line import/no-mutable-exports\nlet {\n  apply,\n  construct\n} = typeof Reflect !== 'undefined' && Reflect;\nif (!freeze) {\n  freeze = function freeze(x) {\n    return x;\n  };\n}\nif (!seal) {\n  seal = function seal(x) {\n    return x;\n  };\n}\nif (!apply) {\n  apply = function apply(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\nif (!construct) {\n  construct = function construct(Func, args) {\n    return new Func(...args);\n  };\n}\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySplice = unapply(Array.prototype.splice);\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\nconst regExpTest = unapply(RegExp.prototype.test);\nconst typeErrorCreate = unconstruct(TypeError);\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply(func) {\n  return function (thisArg) {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return apply(func, thisArg, args);\n  };\n}\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct(func) {\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return construct(func, args);\n  };\n}\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(set, array) {\n  let transformCaseFunc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n        element = lcElement;\n      }\n    }\n    set[element] = true;\n  }\n  return set;\n}\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray(array) {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n  return array;\n}\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone(object) {\n  const newObject = create(null);\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (value && typeof value === 'object' && value.constructor === Object) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n  return newObject;\n}\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n    object = getPrototypeOf(object);\n  }\n  function fallbackValue() {\n    return null;\n  }\n  return fallbackValue;\n}\n\nconst html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']);\nconst svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\nconst svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']);\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nconst svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\nconst mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']);\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nconst mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\nconst text = freeze(['#text']);\n\nconst html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'popover', 'popovertarget', 'popovertargetaction', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'wrap', 'xmlns', 'slot']);\nconst svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'amplitude', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'exponent', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'slope', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'tablevalues', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\nconst mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\nconst xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n// eslint-disable-next-line unicorn/better-regex\nconst MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nconst ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nconst TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nconst DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nconst ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nconst IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nconst IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nconst ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nconst DOCTYPE_NAME = seal(/^html$/i);\nconst CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n\nvar EXPRESSIONS = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ARIA_ATTR: ARIA_ATTR,\n  ATTR_WHITESPACE: ATTR_WHITESPACE,\n  CUSTOM_ELEMENT: CUSTOM_ELEMENT,\n  DATA_ATTR: DATA_ATTR,\n  DOCTYPE_NAME: DOCTYPE_NAME,\n  ERB_EXPR: ERB_EXPR,\n  IS_ALLOWED_URI: IS_ALLOWED_URI,\n  IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n  MUSTACHE_EXPR: MUSTACHE_EXPR,\n  TMPLIT_EXPR: TMPLIT_EXPR\n});\n\n/* eslint-disable @typescript-eslint/indent */\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5,\n  // Deprecated\n  entityNode: 6,\n  // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12 // Deprecated\n};\nconst getGlobal = function getGlobal() {\n  return typeof window === 'undefined' ? null : window;\n};\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n  if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n    return null;\n  }\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      }\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n    return null;\n  }\n};\nconst _createHooksMap = function _createHooksMap() {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: []\n  };\n};\nfunction createDOMPurify() {\n  let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n  const DOMPurify = root => createDOMPurify(root);\n  DOMPurify.version = '3.2.6';\n  DOMPurify.removed = [];\n  if (!window || !window.document || window.document.nodeType !== NODE_TYPE.document || !window.Element) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n    return DOMPurify;\n  }\n  let {\n    document\n  } = window;\n  const originalDocument = document;\n  const currentScript = originalDocument.currentScript;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes\n  } = window;\n  const ElementPrototype = Element.prototype;\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName\n  } = document;\n  const {\n    importNode\n  } = originalDocument;\n  let hooks = _createHooksMap();\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT\n  } = EXPRESSIONS;\n  let {\n    IS_ALLOWED_URI: IS_ALLOWED_URI$1\n  } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(create(null, {\n    tagNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    attributeNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    allowCustomizedBuiltInElements: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: false\n    }\n  }));\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc = null;\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n  const formElement = document.createElement('form');\n  const isRegexOrFunction = function isRegexOrFunction(testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function _parseConfig() {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n    PARSER_MEDIA_TYPE =\n    // eslint-disable-next-line unicorn/prefer-includes\n    SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? DEFAULT_PARSER_MEDIA_TYPE : cfg.PARSER_MEDIA_TYPE;\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS') ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR') ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES') ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR') ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR, transformCaseFunc) : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS') ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS, transformCaseFunc) : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS') ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS') ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR') ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES') ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS = cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS = cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, html$1);\n        addToSet(ALLOWED_ATTR, html);\n      }\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, svg$1);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, svgFilters);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, mathMl$1);\n        addToSet(ALLOWED_ATTR, mathMl);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n    }\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n      }\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n      }\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n      }\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n    CONFIG = cfg;\n  };\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [...svg$1, ...svgFilters, ...svgDisallowed]);\n  const ALL_MATHML_TAGS = addToSet({}, [...mathMl$1, ...mathMlDisallowed]);\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function _checkValidNamespace(element) {\n    let parent = getParentNode(element);\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template'\n      };\n    }\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n      }\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n    }\n    // For XHTML and XML documents that support custom namespaces\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return true;\n    }\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function _forceRemove(node) {\n    arrayPush(DOMPurify.removed, {\n      element: node\n    });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function _removeAttribute(name, element) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element\n      });\n    }\n    element.removeAttribute(name);\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function _initDocument(dirty) {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n    }\n    const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n    const body = doc.body || doc.documentElement;\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n    }\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n    }\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function _createNodeIterator(root) {\n    return createNodeIterator.call(root.ownerDocument || root, root,\n    // eslint-disable-next-line no-bitwise\n    NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null);\n  };\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function _isClobbered(element) {\n    return element instanceof HTMLFormElement && (typeof element.nodeName !== 'string' || typeof element.textContent !== 'string' || typeof element.removeChild !== 'function' || !(element.attributes instanceof NamedNodeMap) || typeof element.removeAttribute !== 'function' || typeof element.setAttribute !== 'function' || typeof element.namespaceURI !== 'string' || typeof element.insertBefore !== 'function' || typeof element.hasChildNodes !== 'function');\n  };\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function _isNode(value) {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n  function _executeHooks(hooks, currentNode, data) {\n    arrayForEach(hooks, hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function _sanitizeElements(currentNode) {\n    let content = null;\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS\n    });\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (SAFE_FOR_XML && currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && regExpTest(/<[/\\w!]/g, currentNode.innerHTML) && regExpTest(/<[/\\w!]/g, currentNode.textContent)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any kind of possibly harmful comments */\n    if (SAFE_FOR_XML && currentNode.nodeType === NODE_TYPE.comment && regExpTest(/<[/\\w]/g, currentNode.data)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) {\n          return false;\n        }\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) {\n          return false;\n        }\n      }\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        content = stringReplace(content, expr, ' ');\n      });\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, {\n          element: currentNode.cloneNode()\n        });\n        currentNode.textContent = content;\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n    return false;\n  };\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n      return false;\n    }\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ; else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ; else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n      // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n      // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n      _isBasicCustomElement(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n      // Alternative, second condition checks if it's an `is`-attribute, AND\n      // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ; else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) ; else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ; else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if (value) {\n      return false;\n    } else ;\n    return true;\n  };\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function _isBasicCustomElement(tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n    const {\n      attributes\n    } = currentNode;\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined\n    };\n    let l = attributes.length;\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const {\n        name,\n        namespaceURI,\n        value: attrValue\n      } = attr;\n      const lcName = transformCaseFunc(name);\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Handle attributes that require Trusted Types */\n      if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n        if (namespaceURI) ; else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML':\n              {\n                value = trustedTypesPolicy.createHTML(value);\n                break;\n              }\n            case 'TrustedScriptURL':\n              {\n                value = trustedTypesPolicy.createScriptURL(value);\n                break;\n              }\n          }\n        }\n      }\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n    while (shadowNode = shadowIterator.nextNode()) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty) {\n    let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === NODE_TYPE.element && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n      // eslint-disable-next-line unicorn/prefer-includes\n      dirty.indexOf('<') === -1) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      }\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n    /* Now start iterating over the created document */\n    while (currentNode = nodeIterator.nextNode()) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n      return returnNode;\n    }\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n    /* Serialize doctype if allowed */\n    if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n      serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n  };\n  DOMPurify.setConfig = function () {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n      return index === -1 ? undefined : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n    return arrayPop(hooks[entryPoint]);\n  };\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n  return DOMPurify;\n}\nvar purify = createDOMPurify();\n\nexport { purify as default };\n"], "mappings": "AAAA;;AAEA,MAAM;EACJA,OAAO;EACPC,cAAc;EACdC,QAAQ;EACRC,cAAc;EACdC;AACF,CAAC,GAAGC,MAAM;AACV,IAAI;EACFC,MAAM;EACNC,IAAI;EACJC;AACF,CAAC,GAAGH,MAAM,CAAC,CAAC;AACZ,IAAI;EACFI,KAAK;EACLC;AACF,CAAC,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO;AAC7C,IAAI,CAACL,MAAM,EAAE;EACXA,MAAM,GAAG,SAASA,MAAMA,CAACM,CAAC,EAAE;IAC1B,OAAOA,CAAC;EACV,CAAC;AACH;AACA,IAAI,CAACL,IAAI,EAAE;EACTA,IAAI,GAAG,SAASA,IAAIA,CAACK,CAAC,EAAE;IACtB,OAAOA,CAAC;EACV,CAAC;AACH;AACA,IAAI,CAACH,KAAK,EAAE;EACVA,KAAK,GAAG,SAASA,KAAKA,CAACI,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;IAC3C,OAAOF,GAAG,CAACJ,KAAK,CAACK,SAAS,EAAEC,IAAI,CAAC;EACnC,CAAC;AACH;AACA,IAAI,CAACL,SAAS,EAAE;EACdA,SAAS,GAAG,SAASA,SAASA,CAACM,IAAI,EAAED,IAAI,EAAE;IACzC,OAAO,IAAIC,IAAI,CAAC,GAAGD,IAAI,CAAC;EAC1B,CAAC;AACH;AACA,MAAME,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC;AACrD,MAAMC,gBAAgB,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,WAAW,CAAC;AAC7D,MAAMC,QAAQ,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,GAAG,CAAC;AAC7C,MAAMC,SAAS,GAAGR,OAAO,CAACC,KAAK,CAACC,SAAS,CAACO,IAAI,CAAC;AAC/C,MAAMC,WAAW,GAAGV,OAAO,CAACC,KAAK,CAACC,SAAS,CAACS,MAAM,CAAC;AACnD,MAAMC,iBAAiB,GAAGZ,OAAO,CAACa,MAAM,CAACX,SAAS,CAACY,WAAW,CAAC;AAC/D,MAAMC,cAAc,GAAGf,OAAO,CAACa,MAAM,CAACX,SAAS,CAACc,QAAQ,CAAC;AACzD,MAAMC,WAAW,GAAGjB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACgB,KAAK,CAAC;AACnD,MAAMC,aAAa,GAAGnB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACkB,OAAO,CAAC;AACvD,MAAMC,aAAa,GAAGrB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACoB,OAAO,CAAC;AACvD,MAAMC,UAAU,GAAGvB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACsB,IAAI,CAAC;AACjD,MAAMC,oBAAoB,GAAGzB,OAAO,CAACb,MAAM,CAACe,SAAS,CAACwB,cAAc,CAAC;AACrE,MAAMC,UAAU,GAAG3B,OAAO,CAAC4B,MAAM,CAAC1B,SAAS,CAAC2B,IAAI,CAAC;AACjD,MAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,SAAShC,OAAOA,CAACiC,IAAI,EAAE;EACrB,OAAO,UAAUC,OAAO,EAAE;IACxB,IAAIA,OAAO,YAAYN,MAAM,EAAE;MAC7BM,OAAO,CAACC,SAAS,GAAG,CAAC;IACvB;IACA,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEzC,IAAI,GAAG,IAAII,KAAK,CAACmC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MAC1G1C,IAAI,CAAC0C,IAAI,GAAG,CAAC,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;IAClC;IACA,OAAOhD,KAAK,CAAC0C,IAAI,EAAEC,OAAO,EAAErC,IAAI,CAAC;EACnC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,WAAWA,CAACE,IAAI,EAAE;EACzB,OAAO,YAAY;IACjB,KAAK,IAAIO,KAAK,GAAGH,SAAS,CAACC,MAAM,EAAEzC,IAAI,GAAG,IAAII,KAAK,CAACuC,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7F5C,IAAI,CAAC4C,KAAK,CAAC,GAAGJ,SAAS,CAACI,KAAK,CAAC;IAChC;IACA,OAAOjD,SAAS,CAACyC,IAAI,EAAEpC,IAAI,CAAC;EAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6C,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC5B,IAAIC,iBAAiB,GAAGR,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKS,SAAS,GAAGT,SAAS,CAAC,CAAC,CAAC,GAAGzB,iBAAiB;EAC7G,IAAI7B,cAAc,EAAE;IAClB;IACA;IACA;IACAA,cAAc,CAAC4D,GAAG,EAAE,IAAI,CAAC;EAC3B;EACA,IAAII,CAAC,GAAGH,KAAK,CAACN,MAAM;EACpB,OAAOS,CAAC,EAAE,EAAE;IACV,IAAIC,OAAO,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACtB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,SAAS,GAAGJ,iBAAiB,CAACG,OAAO,CAAC;MAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;QACzB;QACA,IAAI,CAAChE,QAAQ,CAAC4D,KAAK,CAAC,EAAE;UACpBA,KAAK,CAACG,CAAC,CAAC,GAAGE,SAAS;QACtB;QACAD,OAAO,GAAGC,SAAS;MACrB;IACF;IACAN,GAAG,CAACK,OAAO,CAAC,GAAG,IAAI;EACrB;EACA,OAAOL,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,UAAUA,CAACN,KAAK,EAAE;EACzB,KAAK,IAAIO,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,KAAK,CAACN,MAAM,EAAEa,KAAK,EAAE,EAAE;IACjD,MAAMC,eAAe,GAAG3B,oBAAoB,CAACmB,KAAK,EAAEO,KAAK,CAAC;IAC1D,IAAI,CAACC,eAAe,EAAE;MACpBR,KAAK,CAACO,KAAK,CAAC,GAAG,IAAI;IACrB;EACF;EACA,OAAOP,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,KAAKA,CAACC,MAAM,EAAE;EACrB,MAAMC,SAAS,GAAGjE,MAAM,CAAC,IAAI,CAAC;EAC9B,KAAK,MAAM,CAACkE,QAAQ,EAAEC,KAAK,CAAC,IAAI3E,OAAO,CAACwE,MAAM,CAAC,EAAE;IAC/C,MAAMF,eAAe,GAAG3B,oBAAoB,CAAC6B,MAAM,EAAEE,QAAQ,CAAC;IAC9D,IAAIJ,eAAe,EAAE;MACnB,IAAInD,KAAK,CAACyD,OAAO,CAACD,KAAK,CAAC,EAAE;QACxBF,SAAS,CAACC,QAAQ,CAAC,GAAGN,UAAU,CAACO,KAAK,CAAC;MACzC,CAAC,MAAM,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,WAAW,KAAKxE,MAAM,EAAE;QAC7EoE,SAAS,CAACC,QAAQ,CAAC,GAAGH,KAAK,CAACI,KAAK,CAAC;MACpC,CAAC,MAAM;QACLF,SAAS,CAACC,QAAQ,CAAC,GAAGC,KAAK;MAC7B;IACF;EACF;EACA,OAAOF,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,YAAYA,CAACN,MAAM,EAAEO,IAAI,EAAE;EAClC,OAAOP,MAAM,KAAK,IAAI,EAAE;IACtB,MAAMQ,IAAI,GAAG5E,wBAAwB,CAACoE,MAAM,EAAEO,IAAI,CAAC;IACnD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACC,GAAG,EAAE;QACZ,OAAO/D,OAAO,CAAC8D,IAAI,CAACC,GAAG,CAAC;MAC1B;MACA,IAAI,OAAOD,IAAI,CAACL,KAAK,KAAK,UAAU,EAAE;QACpC,OAAOzD,OAAO,CAAC8D,IAAI,CAACL,KAAK,CAAC;MAC5B;IACF;IACAH,MAAM,GAAGrE,cAAc,CAACqE,MAAM,CAAC;EACjC;EACA,SAASU,aAAaA,CAAA,EAAG;IACvB,OAAO,IAAI;EACb;EACA,OAAOA,aAAa;AACtB;AAEA,MAAMC,MAAM,GAAG7E,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACh/B,MAAM8E,KAAK,GAAG9E,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC1d,MAAM+E,UAAU,GAAG/E,MAAM,CAAC,CAAC,SAAS,EAAE,eAAe,EAAE,qBAAqB,EAAE,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,oBAAoB,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;AACtZ;AACA;AACA;AACA;AACA,MAAMgF,aAAa,GAAGhF,MAAM,CAAC,CAAC,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AAC7T,MAAMiF,QAAQ,GAAGjF,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;AAC7T;AACA;AACA,MAAMkF,gBAAgB,GAAGlF,MAAM,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;AACzN,MAAMmF,IAAI,GAAGnF,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;AAE9B,MAAMoF,IAAI,GAAGpF,MAAM,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,sBAAsB,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,yBAAyB,EAAE,uBAAuB,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC1uC,MAAMqF,GAAG,GAAGrF,MAAM,CAAC,CAAC,eAAe,EAAE,YAAY,EAAE,UAAU,EAAE,oBAAoB,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,qBAAqB,EAAE,6BAA6B,EAAE,eAAe,EAAE,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,EAAE,kBAAkB,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;AACh1E,MAAMsF,MAAM,GAAGtF,MAAM,CAAC,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,eAAe,EAAE,sBAAsB,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACpqB,MAAMuF,GAAG,GAAGvF,MAAM,CAAC,CAAC,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;;AAEvF;AACA,MAAMwF,aAAa,GAAGvF,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC;AACzD,MAAMwF,QAAQ,GAAGxF,IAAI,CAAC,uBAAuB,CAAC;AAC9C,MAAMyF,WAAW,GAAGzF,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;AAC3C,MAAM0F,SAAS,GAAG1F,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;AACxD,MAAM2F,SAAS,GAAG3F,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC1C,MAAM4F,cAAc,GAAG5F,IAAI,CAAC,kGAAkG,CAAC;AAC/H,CAAC;AACD,MAAM6F,iBAAiB,GAAG7F,IAAI,CAAC,uBAAuB,CAAC;AACvD,MAAM8F,eAAe,GAAG9F,IAAI,CAAC,6DAA6D,CAAC;AAC3F,CAAC;AACD,MAAM+F,YAAY,GAAG/F,IAAI,CAAC,SAAS,CAAC;AACpC,MAAMgG,cAAc,GAAGhG,IAAI,CAAC,0BAA0B,CAAC;AAEvD,IAAIiG,WAAW,GAAG,aAAanG,MAAM,CAACC,MAAM,CAAC;EAC3CmG,SAAS,EAAE,IAAI;EACfP,SAAS,EAAEA,SAAS;EACpBG,eAAe,EAAEA,eAAe;EAChCE,cAAc,EAAEA,cAAc;EAC9BN,SAAS,EAAEA,SAAS;EACpBK,YAAY,EAAEA,YAAY;EAC1BP,QAAQ,EAAEA,QAAQ;EAClBI,cAAc,EAAEA,cAAc;EAC9BC,iBAAiB,EAAEA,iBAAiB;EACpCN,aAAa,EAAEA,aAAa;EAC5BE,WAAW,EAAEA;AACf,CAAC,CAAC;;AAEF;AACA;AACA,MAAMU,SAAS,GAAG;EAChBxC,OAAO,EAAE,CAAC;EACVyC,SAAS,EAAE,CAAC;EACZlB,IAAI,EAAE,CAAC;EACPmB,YAAY,EAAE,CAAC;EACfC,eAAe,EAAE,CAAC;EAClB;EACAC,UAAU,EAAE,CAAC;EACb;EACAC,sBAAsB,EAAE,CAAC;EACzBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,YAAY,EAAE,EAAE;EAChBC,gBAAgB,EAAE,EAAE;EACpBC,QAAQ,EAAE,EAAE,CAAC;AACf,CAAC;AACD,MAAMC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;EACrC,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,YAAY,EAAEC,iBAAiB,EAAE;EACpG,IAAI,OAAOD,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAAE;IACvF,OAAO,IAAI;EACb;EACA;EACA;EACA;EACA,IAAIC,MAAM,GAAG,IAAI;EACjB,MAAMC,SAAS,GAAG,uBAAuB;EACzC,IAAIH,iBAAiB,IAAIA,iBAAiB,CAACI,YAAY,CAACD,SAAS,CAAC,EAAE;IAClED,MAAM,GAAGF,iBAAiB,CAACK,YAAY,CAACF,SAAS,CAAC;EACpD;EACA,MAAMG,UAAU,GAAG,WAAW,IAAIJ,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC;EAC7D,IAAI;IACF,OAAOH,YAAY,CAACE,YAAY,CAACK,UAAU,EAAE;MAC3CC,UAAUA,CAACtC,IAAI,EAAE;QACf,OAAOA,IAAI;MACb,CAAC;MACDuC,eAAeA,CAACC,SAAS,EAAE;QACzB,OAAOA,SAAS;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV;IACA;IACA;IACAC,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAGN,UAAU,GAAG,wBAAwB,CAAC;IAC5E,OAAO,IAAI;EACb;AACF,CAAC;AACD,MAAMO,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;EACjD,OAAO;IACLC,uBAAuB,EAAE,EAAE;IAC3BC,qBAAqB,EAAE,EAAE;IACzBC,sBAAsB,EAAE,EAAE;IAC1BC,wBAAwB,EAAE,EAAE;IAC5BC,sBAAsB,EAAE,EAAE;IAC1BC,uBAAuB,EAAE,EAAE;IAC3BC,qBAAqB,EAAE,EAAE;IACzBC,mBAAmB,EAAE,EAAE;IACvBC,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,SAASC,eAAeA,CAAA,EAAG;EACzB,IAAI1B,MAAM,GAAG/D,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKS,SAAS,GAAGT,SAAS,CAAC,CAAC,CAAC,GAAG8D,SAAS,CAAC,CAAC;EAC5F,MAAM4B,SAAS,GAAGC,IAAI,IAAIF,eAAe,CAACE,IAAI,CAAC;EAC/CD,SAAS,CAACE,OAAO,GAAG,OAAO;EAC3BF,SAAS,CAACG,OAAO,GAAG,EAAE;EACtB,IAAI,CAAC9B,MAAM,IAAI,CAACA,MAAM,CAACL,QAAQ,IAAIK,MAAM,CAACL,QAAQ,CAACoC,QAAQ,KAAK3C,SAAS,CAACO,QAAQ,IAAI,CAACK,MAAM,CAACgC,OAAO,EAAE;IACrG;IACA;IACAL,SAAS,CAACM,WAAW,GAAG,KAAK;IAC7B,OAAON,SAAS;EAClB;EACA,IAAI;IACFhC;EACF,CAAC,GAAGK,MAAM;EACV,MAAMkC,gBAAgB,GAAGvC,QAAQ;EACjC,MAAMwC,aAAa,GAAGD,gBAAgB,CAACC,aAAa;EACpD,MAAM;IACJC,gBAAgB;IAChBC,mBAAmB;IACnBC,IAAI;IACJN,OAAO;IACPO,UAAU;IACVC,YAAY,GAAGxC,MAAM,CAACwC,YAAY,IAAIxC,MAAM,CAACyC,eAAe;IAC5DC,eAAe;IACfC,SAAS;IACTzC;EACF,CAAC,GAAGF,MAAM;EACV,MAAM4C,gBAAgB,GAAGZ,OAAO,CAAClI,SAAS;EAC1C,MAAM+I,SAAS,GAAGrF,YAAY,CAACoF,gBAAgB,EAAE,WAAW,CAAC;EAC7D,MAAME,MAAM,GAAGtF,YAAY,CAACoF,gBAAgB,EAAE,QAAQ,CAAC;EACvD,MAAMG,cAAc,GAAGvF,YAAY,CAACoF,gBAAgB,EAAE,aAAa,CAAC;EACpE,MAAMI,aAAa,GAAGxF,YAAY,CAACoF,gBAAgB,EAAE,YAAY,CAAC;EAClE,MAAMK,aAAa,GAAGzF,YAAY,CAACoF,gBAAgB,EAAE,YAAY,CAAC;EAClE;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOP,mBAAmB,KAAK,UAAU,EAAE;IAC7C,MAAMa,QAAQ,GAAGvD,QAAQ,CAACwD,aAAa,CAAC,UAAU,CAAC;IACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;MACtD1D,QAAQ,GAAGuD,QAAQ,CAACE,OAAO,CAACC,aAAa;IAC3C;EACF;EACA,IAAIC,kBAAkB;EACtB,IAAIC,SAAS,GAAG,EAAE;EAClB,MAAM;IACJC,cAAc;IACdC,kBAAkB;IAClBC,sBAAsB;IACtBC;EACF,CAAC,GAAGhE,QAAQ;EACZ,MAAM;IACJiE;EACF,CAAC,GAAG1B,gBAAgB;EACpB,IAAI2B,KAAK,GAAG7C,eAAe,CAAC,CAAC;EAC7B;AACF;AACA;EACEW,SAAS,CAACM,WAAW,GAAG,OAAOvJ,OAAO,KAAK,UAAU,IAAI,OAAOuK,aAAa,KAAK,UAAU,IAAIO,cAAc,IAAIA,cAAc,CAACM,kBAAkB,KAAKpH,SAAS;EACjK,MAAM;IACJ8B,aAAa;IACbC,QAAQ;IACRC,WAAW;IACXC,SAAS;IACTC,SAAS;IACTE,iBAAiB;IACjBC,eAAe;IACfE;EACF,CAAC,GAAGC,WAAW;EACf,IAAI;IACFL,cAAc,EAAEkF;EAClB,CAAC,GAAG7E,WAAW;EACf;AACF;AACA;AACA;EACE;EACA,IAAI8E,YAAY,GAAG,IAAI;EACvB,MAAMC,oBAAoB,GAAG3H,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGuB,MAAM,EAAE,GAAGC,KAAK,EAAE,GAAGC,UAAU,EAAE,GAAGE,QAAQ,EAAE,GAAGE,IAAI,CAAC,CAAC;EACrG;EACA,IAAI+F,YAAY,GAAG,IAAI;EACvB,MAAMC,oBAAoB,GAAG7H,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG8B,IAAI,EAAE,GAAGC,GAAG,EAAE,GAAGC,MAAM,EAAE,GAAGC,GAAG,CAAC,CAAC;EAC/E;AACF;AACA;AACA;AACA;AACA;EACE,IAAI6F,uBAAuB,GAAGrL,MAAM,CAACE,IAAI,CAACC,MAAM,CAAC,IAAI,EAAE;IACrDmL,YAAY,EAAE;MACZC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBnH,KAAK,EAAE;IACT,CAAC;IACDoH,kBAAkB,EAAE;MAClBH,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBnH,KAAK,EAAE;IACT,CAAC;IACDqH,8BAA8B,EAAE;MAC9BJ,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBnH,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH;EACA,IAAIsH,WAAW,GAAG,IAAI;EACtB;EACA,IAAIC,WAAW,GAAG,IAAI;EACtB;EACA,IAAIC,eAAe,GAAG,IAAI;EAC1B;EACA,IAAIC,eAAe,GAAG,IAAI;EAC1B;EACA,IAAIC,uBAAuB,GAAG,KAAK;EACnC;AACF;EACE,IAAIC,wBAAwB,GAAG,IAAI;EACnC;AACF;AACA;EACE,IAAIC,kBAAkB,GAAG,KAAK;EAC9B;AACF;AACA;EACE,IAAIC,YAAY,GAAG,IAAI;EACvB;EACA,IAAIC,cAAc,GAAG,KAAK;EAC1B;EACA,IAAIC,UAAU,GAAG,KAAK;EACtB;AACF;EACE,IAAIC,UAAU,GAAG,KAAK;EACtB;AACF;AACA;AACA;EACE,IAAIC,UAAU,GAAG,KAAK;EACtB;AACF;EACE,IAAIC,mBAAmB,GAAG,KAAK;EAC/B;AACF;EACE,IAAIC,mBAAmB,GAAG,KAAK;EAC/B;AACF;AACA;EACE,IAAIC,YAAY,GAAG,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,oBAAoB,GAAG,KAAK;EAChC,MAAMC,2BAA2B,GAAG,eAAe;EACnD;EACA,IAAIC,YAAY,GAAG,IAAI;EACvB;AACF;EACE,IAAIC,QAAQ,GAAG,KAAK;EACpB;EACA,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB;EACA,IAAIC,eAAe,GAAG,IAAI;EAC1B,MAAMC,uBAAuB,GAAG1J,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EACjS;EACA,IAAI2J,aAAa,GAAG,IAAI;EACxB,MAAMC,qBAAqB,GAAG5J,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;EACjG;EACA,IAAI6J,mBAAmB,GAAG,IAAI;EAC9B,MAAMC,2BAA2B,GAAG9J,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;EACjL,MAAM+J,gBAAgB,GAAG,oCAAoC;EAC7D,MAAMC,aAAa,GAAG,4BAA4B;EAClD,MAAMC,cAAc,GAAG,8BAA8B;EACrD;EACA,IAAIC,SAAS,GAAGD,cAAc;EAC9B,IAAIE,cAAc,GAAG,KAAK;EAC1B;EACA,IAAIC,kBAAkB,GAAG,IAAI;EAC7B,MAAMC,0BAA0B,GAAGrK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC+J,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,CAAC,EAAE5L,cAAc,CAAC;EAClH,IAAIiM,8BAA8B,GAAGtK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;EACpF,IAAIuK,uBAAuB,GAAGvK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;EAC9D;EACA;EACA;EACA;EACA,MAAMwK,4BAA4B,GAAGxK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;EAC5F;EACA,IAAIyK,iBAAiB,GAAG,IAAI;EAC5B,MAAMC,4BAA4B,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC;EAC3E,MAAMC,yBAAyB,GAAG,WAAW;EAC7C,IAAIxK,iBAAiB,GAAG,IAAI;EAC5B;EACA,IAAIyK,MAAM,GAAG,IAAI;EACjB;EACA;EACA,MAAMC,WAAW,GAAGxH,QAAQ,CAACwD,aAAa,CAAC,MAAM,CAAC;EAClD,MAAMiE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,SAAS,EAAE;IAC9D,OAAOA,SAAS,YAAY7L,MAAM,IAAI6L,SAAS,YAAYC,QAAQ;EACrE,CAAC;EACD;AACF;AACA;AACA;AACA;EACE;EACA,MAAMC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IAC3C,IAAIC,GAAG,GAAGvL,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKS,SAAS,GAAGT,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChF,IAAIiL,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;MAC5B;IACF;IACA;IACA,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACnCA,GAAG,GAAG,CAAC,CAAC;IACV;IACA;IACAA,GAAG,GAAGvK,KAAK,CAACuK,GAAG,CAAC;IAChBT,iBAAiB;IACjB;IACAC,4BAA4B,CAAC9L,OAAO,CAACsM,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAAGE,yBAAyB,GAAGO,GAAG,CAACT,iBAAiB;IACtH;IACAtK,iBAAiB,GAAGsK,iBAAiB,KAAK,uBAAuB,GAAGpM,cAAc,GAAGH,iBAAiB;IACtG;IACAwJ,YAAY,GAAG3I,oBAAoB,CAACmM,GAAG,EAAE,cAAc,CAAC,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,GAAG,CAACxD,YAAY,EAAEvH,iBAAiB,CAAC,GAAGwH,oBAAoB;IACnIC,YAAY,GAAG7I,oBAAoB,CAACmM,GAAG,EAAE,cAAc,CAAC,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,GAAG,CAACtD,YAAY,EAAEzH,iBAAiB,CAAC,GAAG0H,oBAAoB;IACnIuC,kBAAkB,GAAGrL,oBAAoB,CAACmM,GAAG,EAAE,oBAAoB,CAAC,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,GAAG,CAACd,kBAAkB,EAAE/L,cAAc,CAAC,GAAGgM,0BAA0B;IACxJR,mBAAmB,GAAG9K,oBAAoB,CAACmM,GAAG,EAAE,mBAAmB,CAAC,GAAGlL,QAAQ,CAACW,KAAK,CAACmJ,2BAA2B,CAAC,EAAEoB,GAAG,CAACC,iBAAiB,EAAEhL,iBAAiB,CAAC,GAAG2J,2BAA2B;IAC3LH,aAAa,GAAG5K,oBAAoB,CAACmM,GAAG,EAAE,mBAAmB,CAAC,GAAGlL,QAAQ,CAACW,KAAK,CAACiJ,qBAAqB,CAAC,EAAEsB,GAAG,CAACE,iBAAiB,EAAEjL,iBAAiB,CAAC,GAAGyJ,qBAAqB;IACzKH,eAAe,GAAG1K,oBAAoB,CAACmM,GAAG,EAAE,iBAAiB,CAAC,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,GAAG,CAACzB,eAAe,EAAEtJ,iBAAiB,CAAC,GAAGuJ,uBAAuB;IAC/IrB,WAAW,GAAGtJ,oBAAoB,CAACmM,GAAG,EAAE,aAAa,CAAC,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,GAAG,CAAC7C,WAAW,EAAElI,iBAAiB,CAAC,GAAGQ,KAAK,CAAC,CAAC,CAAC,CAAC;IACrH2H,WAAW,GAAGvJ,oBAAoB,CAACmM,GAAG,EAAE,aAAa,CAAC,GAAGlL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,GAAG,CAAC5C,WAAW,EAAEnI,iBAAiB,CAAC,GAAGQ,KAAK,CAAC,CAAC,CAAC,CAAC;IACrH6I,YAAY,GAAGzK,oBAAoB,CAACmM,GAAG,EAAE,cAAc,CAAC,GAAGA,GAAG,CAAC1B,YAAY,GAAG,KAAK;IACnFjB,eAAe,GAAG2C,GAAG,CAAC3C,eAAe,KAAK,KAAK,CAAC,CAAC;IACjDC,eAAe,GAAG0C,GAAG,CAAC1C,eAAe,KAAK,KAAK,CAAC,CAAC;IACjDC,uBAAuB,GAAGyC,GAAG,CAACzC,uBAAuB,IAAI,KAAK,CAAC,CAAC;IAChEC,wBAAwB,GAAGwC,GAAG,CAACxC,wBAAwB,KAAK,KAAK,CAAC,CAAC;IACnEC,kBAAkB,GAAGuC,GAAG,CAACvC,kBAAkB,IAAI,KAAK,CAAC,CAAC;IACtDC,YAAY,GAAGsC,GAAG,CAACtC,YAAY,KAAK,KAAK,CAAC,CAAC;IAC3CC,cAAc,GAAGqC,GAAG,CAACrC,cAAc,IAAI,KAAK,CAAC,CAAC;IAC9CG,UAAU,GAAGkC,GAAG,CAAClC,UAAU,IAAI,KAAK,CAAC,CAAC;IACtCC,mBAAmB,GAAGiC,GAAG,CAACjC,mBAAmB,IAAI,KAAK,CAAC,CAAC;IACxDC,mBAAmB,GAAGgC,GAAG,CAAChC,mBAAmB,IAAI,KAAK,CAAC,CAAC;IACxDH,UAAU,GAAGmC,GAAG,CAACnC,UAAU,IAAI,KAAK,CAAC,CAAC;IACtCI,YAAY,GAAG+B,GAAG,CAAC/B,YAAY,KAAK,KAAK,CAAC,CAAC;IAC3CC,oBAAoB,GAAG8B,GAAG,CAAC9B,oBAAoB,IAAI,KAAK,CAAC,CAAC;IAC1DE,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC,CAAC;IAC3CC,QAAQ,GAAG2B,GAAG,CAAC3B,QAAQ,IAAI,KAAK,CAAC,CAAC;IAClC9B,gBAAgB,GAAGyD,GAAG,CAACG,kBAAkB,IAAI9I,cAAc;IAC3D2H,SAAS,GAAGgB,GAAG,CAAChB,SAAS,IAAID,cAAc;IAC3CK,8BAA8B,GAAGY,GAAG,CAACZ,8BAA8B,IAAIA,8BAA8B;IACrGC,uBAAuB,GAAGW,GAAG,CAACX,uBAAuB,IAAIA,uBAAuB;IAChFzC,uBAAuB,GAAGoD,GAAG,CAACpD,uBAAuB,IAAI,CAAC,CAAC;IAC3D,IAAIoD,GAAG,CAACpD,uBAAuB,IAAIgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAC,EAAE;MAC9FD,uBAAuB,CAACC,YAAY,GAAGmD,GAAG,CAACpD,uBAAuB,CAACC,YAAY;IACjF;IACA,IAAImD,GAAG,CAACpD,uBAAuB,IAAIgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAC,EAAE;MACpGL,uBAAuB,CAACK,kBAAkB,GAAG+C,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB;IAC7F;IACA,IAAI+C,GAAG,CAACpD,uBAAuB,IAAI,OAAOoD,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,KAAK,SAAS,EAAE;MAClHN,uBAAuB,CAACM,8BAA8B,GAAG8C,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B;IACrH;IACA,IAAIO,kBAAkB,EAAE;MACtBH,eAAe,GAAG,KAAK;IACzB;IACA,IAAIS,mBAAmB,EAAE;MACvBD,UAAU,GAAG,IAAI;IACnB;IACA;IACA,IAAIQ,YAAY,EAAE;MAChB9B,YAAY,GAAG1H,QAAQ,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC;MACjC+F,YAAY,GAAG,EAAE;MACjB,IAAI4B,YAAY,CAAC1H,IAAI,KAAK,IAAI,EAAE;QAC9B9B,QAAQ,CAAC0H,YAAY,EAAEnG,MAAM,CAAC;QAC9BvB,QAAQ,CAAC4H,YAAY,EAAE9F,IAAI,CAAC;MAC9B;MACA,IAAI0H,YAAY,CAACzH,GAAG,KAAK,IAAI,EAAE;QAC7B/B,QAAQ,CAAC0H,YAAY,EAAElG,KAAK,CAAC;QAC7BxB,QAAQ,CAAC4H,YAAY,EAAE7F,GAAG,CAAC;QAC3B/B,QAAQ,CAAC4H,YAAY,EAAE3F,GAAG,CAAC;MAC7B;MACA,IAAIuH,YAAY,CAAC/H,UAAU,KAAK,IAAI,EAAE;QACpCzB,QAAQ,CAAC0H,YAAY,EAAEjG,UAAU,CAAC;QAClCzB,QAAQ,CAAC4H,YAAY,EAAE7F,GAAG,CAAC;QAC3B/B,QAAQ,CAAC4H,YAAY,EAAE3F,GAAG,CAAC;MAC7B;MACA,IAAIuH,YAAY,CAACxH,MAAM,KAAK,IAAI,EAAE;QAChChC,QAAQ,CAAC0H,YAAY,EAAE/F,QAAQ,CAAC;QAChC3B,QAAQ,CAAC4H,YAAY,EAAE5F,MAAM,CAAC;QAC9BhC,QAAQ,CAAC4H,YAAY,EAAE3F,GAAG,CAAC;MAC7B;IACF;IACA;IACA,IAAIiJ,GAAG,CAACI,QAAQ,EAAE;MAChB,IAAI5D,YAAY,KAAKC,oBAAoB,EAAE;QACzCD,YAAY,GAAG/G,KAAK,CAAC+G,YAAY,CAAC;MACpC;MACA1H,QAAQ,CAAC0H,YAAY,EAAEwD,GAAG,CAACI,QAAQ,EAAEnL,iBAAiB,CAAC;IACzD;IACA,IAAI+K,GAAG,CAACK,QAAQ,EAAE;MAChB,IAAI3D,YAAY,KAAKC,oBAAoB,EAAE;QACzCD,YAAY,GAAGjH,KAAK,CAACiH,YAAY,CAAC;MACpC;MACA5H,QAAQ,CAAC4H,YAAY,EAAEsD,GAAG,CAACK,QAAQ,EAAEpL,iBAAiB,CAAC;IACzD;IACA,IAAI+K,GAAG,CAACC,iBAAiB,EAAE;MACzBnL,QAAQ,CAAC6J,mBAAmB,EAAEqB,GAAG,CAACC,iBAAiB,EAAEhL,iBAAiB,CAAC;IACzE;IACA,IAAI+K,GAAG,CAACzB,eAAe,EAAE;MACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;QAC/CD,eAAe,GAAG9I,KAAK,CAAC8I,eAAe,CAAC;MAC1C;MACAzJ,QAAQ,CAACyJ,eAAe,EAAEyB,GAAG,CAACzB,eAAe,EAAEtJ,iBAAiB,CAAC;IACnE;IACA;IACA,IAAImJ,YAAY,EAAE;MAChB5B,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI;IAC9B;IACA;IACA,IAAImB,cAAc,EAAE;MAClB7I,QAAQ,CAAC0H,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClD;IACA;IACA,IAAIA,YAAY,CAAC8D,KAAK,EAAE;MACtBxL,QAAQ,CAAC0H,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC;MACjC,OAAOW,WAAW,CAACoD,KAAK;IAC1B;IACA,IAAIP,GAAG,CAACQ,oBAAoB,EAAE;MAC5B,IAAI,OAAOR,GAAG,CAACQ,oBAAoB,CAACtH,UAAU,KAAK,UAAU,EAAE;QAC7D,MAAMhF,eAAe,CAAC,6EAA6E,CAAC;MACtG;MACA,IAAI,OAAO8L,GAAG,CAACQ,oBAAoB,CAACrH,eAAe,KAAK,UAAU,EAAE;QAClE,MAAMjF,eAAe,CAAC,kFAAkF,CAAC;MAC3G;MACA;MACA4H,kBAAkB,GAAGkE,GAAG,CAACQ,oBAAoB;MAC7C;MACAzE,SAAS,GAAGD,kBAAkB,CAAC5C,UAAU,CAAC,EAAE,CAAC;IAC/C,CAAC,MAAM;MACL;MACA,IAAI4C,kBAAkB,KAAK5G,SAAS,EAAE;QACpC4G,kBAAkB,GAAGrD,yBAAyB,CAACC,YAAY,EAAEiC,aAAa,CAAC;MAC7E;MACA;MACA,IAAImB,kBAAkB,KAAK,IAAI,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;QAChEA,SAAS,GAAGD,kBAAkB,CAAC5C,UAAU,CAAC,EAAE,CAAC;MAC/C;IACF;IACA;IACA;IACA,IAAI1H,MAAM,EAAE;MACVA,MAAM,CAACwO,GAAG,CAAC;IACb;IACAN,MAAM,GAAGM,GAAG;EACd,CAAC;EACD;AACF;AACA;EACE,MAAMS,YAAY,GAAG3L,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGwB,KAAK,EAAE,GAAGC,UAAU,EAAE,GAAGC,aAAa,CAAC,CAAC;EAC9E,MAAMkK,eAAe,GAAG5L,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG2B,QAAQ,EAAE,GAAGC,gBAAgB,CAAC,CAAC;EACxE;AACF;AACA;AACA;AACA;AACA;EACE,MAAMiK,oBAAoB,GAAG,SAASA,oBAAoBA,CAACvL,OAAO,EAAE;IAClE,IAAIwL,MAAM,GAAGnF,aAAa,CAACrG,OAAO,CAAC;IACnC;IACA;IACA,IAAI,CAACwL,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;MAC9BD,MAAM,GAAG;QACPE,YAAY,EAAE9B,SAAS;QACvB6B,OAAO,EAAE;MACX,CAAC;IACH;IACA,MAAMA,OAAO,GAAG7N,iBAAiB,CAACoC,OAAO,CAACyL,OAAO,CAAC;IAClD,MAAME,aAAa,GAAG/N,iBAAiB,CAAC4N,MAAM,CAACC,OAAO,CAAC;IACvD,IAAI,CAAC3B,kBAAkB,CAAC9J,OAAO,CAAC0L,YAAY,CAAC,EAAE;MAC7C,OAAO,KAAK;IACd;IACA,IAAI1L,OAAO,CAAC0L,YAAY,KAAKhC,aAAa,EAAE;MAC1C;MACA;MACA;MACA,IAAI8B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;QAC1C,OAAO8B,OAAO,KAAK,KAAK;MAC1B;MACA;MACA;MACA;MACA,IAAID,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,EAAE;QAC5C,OAAOgC,OAAO,KAAK,KAAK,KAAKE,aAAa,KAAK,gBAAgB,IAAI3B,8BAA8B,CAAC2B,aAAa,CAAC,CAAC;MACnH;MACA;MACA;MACA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC;IACvC;IACA,IAAIzL,OAAO,CAAC0L,YAAY,KAAKjC,gBAAgB,EAAE;MAC7C;MACA;MACA;MACA,IAAI+B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;QAC1C,OAAO8B,OAAO,KAAK,MAAM;MAC3B;MACA;MACA;MACA,IAAID,MAAM,CAACE,YAAY,KAAKhC,aAAa,EAAE;QACzC,OAAO+B,OAAO,KAAK,MAAM,IAAIxB,uBAAuB,CAAC0B,aAAa,CAAC;MACrE;MACA;MACA;MACA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC;IAC1C;IACA,IAAIzL,OAAO,CAAC0L,YAAY,KAAK/B,cAAc,EAAE;MAC3C;MACA;MACA;MACA,IAAI6B,MAAM,CAACE,YAAY,KAAKhC,aAAa,IAAI,CAACO,uBAAuB,CAAC0B,aAAa,CAAC,EAAE;QACpF,OAAO,KAAK;MACd;MACA,IAAIH,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,IAAI,CAACO,8BAA8B,CAAC2B,aAAa,CAAC,EAAE;QAC9F,OAAO,KAAK;MACd;MACA;MACA;MACA,OAAO,CAACL,eAAe,CAACG,OAAO,CAAC,KAAKvB,4BAA4B,CAACuB,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC;IACvG;IACA;IACA,IAAItB,iBAAiB,KAAK,uBAAuB,IAAIL,kBAAkB,CAAC9J,OAAO,CAAC0L,YAAY,CAAC,EAAE;MAC7F,OAAO,IAAI;IACb;IACA;IACA;IACA;IACA;IACA,OAAO,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;AACA;EACE,MAAMG,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;IAC/CtO,SAAS,CAACuH,SAAS,CAACG,OAAO,EAAE;MAC3BlF,OAAO,EAAE8L;IACX,CAAC,CAAC;IACF,IAAI;MACF;MACAzF,aAAa,CAACyF,IAAI,CAAC,CAACC,WAAW,CAACD,IAAI,CAAC;IACvC,CAAC,CAAC,OAAO7H,CAAC,EAAE;MACViC,MAAM,CAAC4F,IAAI,CAAC;IACd;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAEjM,OAAO,EAAE;IAChE,IAAI;MACFxC,SAAS,CAACuH,SAAS,CAACG,OAAO,EAAE;QAC3BzC,SAAS,EAAEzC,OAAO,CAACkM,gBAAgB,CAACD,IAAI,CAAC;QACzCE,IAAI,EAAEnM;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOiE,CAAC,EAAE;MACVzG,SAAS,CAACuH,SAAS,CAACG,OAAO,EAAE;QAC3BzC,SAAS,EAAE,IAAI;QACf0J,IAAI,EAAEnM;MACR,CAAC,CAAC;IACJ;IACAA,OAAO,CAACoM,eAAe,CAACH,IAAI,CAAC;IAC7B;IACA,IAAIA,IAAI,KAAK,IAAI,EAAE;MACjB,IAAIvD,UAAU,IAAIC,mBAAmB,EAAE;QACrC,IAAI;UACFkD,YAAY,CAAC7L,OAAO,CAAC;QACvB,CAAC,CAAC,OAAOiE,CAAC,EAAE,CAAC;MACf,CAAC,MAAM;QACL,IAAI;UACFjE,OAAO,CAACqM,YAAY,CAACJ,IAAI,EAAE,EAAE,CAAC;QAChC,CAAC,CAAC,OAAOhI,CAAC,EAAE,CAAC;MACf;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE,MAAMqI,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAClD;IACA,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIC,iBAAiB,GAAG,IAAI;IAC5B,IAAIhE,UAAU,EAAE;MACd8D,KAAK,GAAG,mBAAmB,GAAGA,KAAK;IACrC,CAAC,MAAM;MACL;MACA,MAAMG,OAAO,GAAGzO,WAAW,CAACsO,KAAK,EAAE,aAAa,CAAC;MACjDE,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAC3C;IACA,IAAIvC,iBAAiB,KAAK,uBAAuB,IAAIP,SAAS,KAAKD,cAAc,EAAE;MACjF;MACA4C,KAAK,GAAG,gEAAgE,GAAGA,KAAK,GAAG,gBAAgB;IACrG;IACA,MAAMI,YAAY,GAAGjG,kBAAkB,GAAGA,kBAAkB,CAAC5C,UAAU,CAACyI,KAAK,CAAC,GAAGA,KAAK;IACtF;AACJ;AACA;AACA;IACI,IAAI3C,SAAS,KAAKD,cAAc,EAAE;MAChC,IAAI;QACF6C,GAAG,GAAG,IAAIzG,SAAS,CAAC,CAAC,CAAC6G,eAAe,CAACD,YAAY,EAAExC,iBAAiB,CAAC;MACxE,CAAC,CAAC,OAAOlG,CAAC,EAAE,CAAC;IACf;IACA;IACA,IAAI,CAACuI,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;MAChCL,GAAG,GAAG5F,cAAc,CAACkG,cAAc,CAAClD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;MAChE,IAAI;QACF4C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGlD,cAAc,GAAGlD,SAAS,GAAGgG,YAAY;MAC3E,CAAC,CAAC,OAAO1I,CAAC,EAAE;QACV;MAAA;IAEJ;IACA,MAAM+I,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe;IAC5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;MAC9BO,IAAI,CAACC,YAAY,CAAClK,QAAQ,CAACmK,cAAc,CAACT,iBAAiB,CAAC,EAAEO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAC3F;IACA;IACA,IAAIvD,SAAS,KAAKD,cAAc,EAAE;MAChC,OAAO5C,oBAAoB,CAACqG,IAAI,CAACZ,GAAG,EAAEjE,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5E;IACA,OAAOA,cAAc,GAAGiE,GAAG,CAACK,eAAe,GAAGG,IAAI;EACpD,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE,MAAMK,mBAAmB,GAAG,SAASA,mBAAmBA,CAACrI,IAAI,EAAE;IAC7D,OAAO6B,kBAAkB,CAACuG,IAAI,CAACpI,IAAI,CAACyB,aAAa,IAAIzB,IAAI,EAAEA,IAAI;IAC/D;IACAW,UAAU,CAAC2H,YAAY,GAAG3H,UAAU,CAAC4H,YAAY,GAAG5H,UAAU,CAAC6H,SAAS,GAAG7H,UAAU,CAAC8H,2BAA2B,GAAG9H,UAAU,CAAC+H,kBAAkB,EAAE,IAAI,CAAC;EAC1J,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,YAAY,GAAG,SAASA,YAAYA,CAAC3N,OAAO,EAAE;IAClD,OAAOA,OAAO,YAAY8F,eAAe,KAAK,OAAO9F,OAAO,CAAC4N,QAAQ,KAAK,QAAQ,IAAI,OAAO5N,OAAO,CAAC6N,WAAW,KAAK,QAAQ,IAAI,OAAO7N,OAAO,CAAC+L,WAAW,KAAK,UAAU,IAAI,EAAE/L,OAAO,CAAC8N,UAAU,YAAYlI,YAAY,CAAC,IAAI,OAAO5F,OAAO,CAACoM,eAAe,KAAK,UAAU,IAAI,OAAOpM,OAAO,CAACqM,YAAY,KAAK,UAAU,IAAI,OAAOrM,OAAO,CAAC0L,YAAY,KAAK,QAAQ,IAAI,OAAO1L,OAAO,CAACiN,YAAY,KAAK,UAAU,IAAI,OAAOjN,OAAO,CAAC+N,aAAa,KAAK,UAAU,CAAC;EACtc,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,OAAO,GAAG,SAASA,OAAOA,CAACvN,KAAK,EAAE;IACtC,OAAO,OAAOiF,IAAI,KAAK,UAAU,IAAIjF,KAAK,YAAYiF,IAAI;EAC5D,CAAC;EACD,SAASuI,aAAaA,CAAChH,KAAK,EAAEiH,WAAW,EAAEC,IAAI,EAAE;IAC/CpR,YAAY,CAACkK,KAAK,EAAEmH,IAAI,IAAI;MAC1BA,IAAI,CAAChB,IAAI,CAACrI,SAAS,EAAEmJ,WAAW,EAAEC,IAAI,EAAE7D,MAAM,CAAC;IACjD,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM+D,iBAAiB,GAAG,SAASA,iBAAiBA,CAACH,WAAW,EAAE;IAChE,IAAI1H,OAAO,GAAG,IAAI;IAClB;IACAyH,aAAa,CAAChH,KAAK,CAACxC,sBAAsB,EAAEyJ,WAAW,EAAE,IAAI,CAAC;IAC9D;IACA,IAAIP,YAAY,CAACO,WAAW,CAAC,EAAE;MAC7BrC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IACA;IACA,MAAMzC,OAAO,GAAG5L,iBAAiB,CAACqO,WAAW,CAACN,QAAQ,CAAC;IACvD;IACAK,aAAa,CAAChH,KAAK,CAACrC,mBAAmB,EAAEsJ,WAAW,EAAE;MACpDzC,OAAO;MACP6C,WAAW,EAAElH;IACf,CAAC,CAAC;IACF;IACA,IAAIkB,YAAY,IAAI4F,WAAW,CAACH,aAAa,CAAC,CAAC,IAAI,CAACC,OAAO,CAACE,WAAW,CAACK,iBAAiB,CAAC,IAAI5P,UAAU,CAAC,UAAU,EAAEuP,WAAW,CAACnB,SAAS,CAAC,IAAIpO,UAAU,CAAC,UAAU,EAAEuP,WAAW,CAACL,WAAW,CAAC,EAAE;MAC9LhC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IACA;IACA,IAAIA,WAAW,CAAC/I,QAAQ,KAAK3C,SAAS,CAACK,sBAAsB,EAAE;MAC7DgJ,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IACA;IACA,IAAI5F,YAAY,IAAI4F,WAAW,CAAC/I,QAAQ,KAAK3C,SAAS,CAACM,OAAO,IAAInE,UAAU,CAAC,SAAS,EAAEuP,WAAW,CAACC,IAAI,CAAC,EAAE;MACzGtC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IACA;IACA,IAAI,CAAC9G,YAAY,CAACqE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;MAClD;MACA,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAAC,IAAI+C,qBAAqB,CAAC/C,OAAO,CAAC,EAAE;QAC3D,IAAIjE,uBAAuB,CAACC,YAAY,YAAY7I,MAAM,IAAID,UAAU,CAAC6I,uBAAuB,CAACC,YAAY,EAAEgE,OAAO,CAAC,EAAE;UACvH,OAAO,KAAK;QACd;QACA,IAAIjE,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IAAIlD,uBAAuB,CAACC,YAAY,CAACgE,OAAO,CAAC,EAAE;UAC7G,OAAO,KAAK;QACd;MACF;MACA;MACA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAO,CAAC,EAAE;QAC7C,MAAMgD,UAAU,GAAGpI,aAAa,CAAC6H,WAAW,CAAC,IAAIA,WAAW,CAACO,UAAU;QACvE,MAAMtB,UAAU,GAAG/G,aAAa,CAAC8H,WAAW,CAAC,IAAIA,WAAW,CAACf,UAAU;QACvE,IAAIA,UAAU,IAAIsB,UAAU,EAAE;UAC5B,MAAMC,UAAU,GAAGvB,UAAU,CAAC7N,MAAM;UACpC,KAAK,IAAIqP,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;YACxC,MAAMC,UAAU,GAAG3I,SAAS,CAACkH,UAAU,CAACwB,CAAC,CAAC,EAAE,IAAI,CAAC;YACjDC,UAAU,CAACC,cAAc,GAAG,CAACX,WAAW,CAACW,cAAc,IAAI,CAAC,IAAI,CAAC;YACjEJ,UAAU,CAACxB,YAAY,CAAC2B,UAAU,EAAEzI,cAAc,CAAC+H,WAAW,CAAC,CAAC;UAClE;QACF;MACF;MACArC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IACA;IACA,IAAIA,WAAW,YAAY9I,OAAO,IAAI,CAACmG,oBAAoB,CAAC2C,WAAW,CAAC,EAAE;MACxErC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IACA;IACA,IAAI,CAACzC,OAAO,KAAK,UAAU,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,UAAU,KAAK9M,UAAU,CAAC,6BAA6B,EAAEuP,WAAW,CAACnB,SAAS,CAAC,EAAE;MACnJlB,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IACA;IACA,IAAI7F,kBAAkB,IAAI6F,WAAW,CAAC/I,QAAQ,KAAK3C,SAAS,CAACjB,IAAI,EAAE;MACjE;MACAiF,OAAO,GAAG0H,WAAW,CAACL,WAAW;MACjC9Q,YAAY,CAAC,CAAC6E,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAEgN,IAAI,IAAI;QAC3DtI,OAAO,GAAGrI,aAAa,CAACqI,OAAO,EAAEsI,IAAI,EAAE,GAAG,CAAC;MAC7C,CAAC,CAAC;MACF,IAAIZ,WAAW,CAACL,WAAW,KAAKrH,OAAO,EAAE;QACvChJ,SAAS,CAACuH,SAAS,CAACG,OAAO,EAAE;UAC3BlF,OAAO,EAAEkO,WAAW,CAACjI,SAAS,CAAC;QACjC,CAAC,CAAC;QACFiI,WAAW,CAACL,WAAW,GAAGrH,OAAO;MACnC;IACF;IACA;IACAyH,aAAa,CAAChH,KAAK,CAAC3C,qBAAqB,EAAE4J,WAAW,EAAE,IAAI,CAAC;IAC7D,OAAO,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EACA,MAAMa,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,MAAM,EAAExO,KAAK,EAAE;IACzE;IACA,IAAIoI,YAAY,KAAKoG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,KAAKxO,KAAK,IAAIsC,QAAQ,IAAItC,KAAK,IAAI8J,WAAW,CAAC,EAAE;MACzG,OAAO,KAAK;IACd;IACA;AACJ;AACA;AACA;IACI,IAAIrC,eAAe,IAAI,CAACF,WAAW,CAACiH,MAAM,CAAC,IAAItQ,UAAU,CAACoD,SAAS,EAAEkN,MAAM,CAAC,EAAE,CAAC,KAAM,IAAIhH,eAAe,IAAItJ,UAAU,CAACqD,SAAS,EAAEiN,MAAM,CAAC,EAAE,CAAC,KAAM,IAAI,CAAC3H,YAAY,CAAC2H,MAAM,CAAC,IAAIjH,WAAW,CAACiH,MAAM,CAAC,EAAE;MAClM;MACA;MACA;MACA;MACAT,qBAAqB,CAACQ,KAAK,CAAC,KAAKxH,uBAAuB,CAACC,YAAY,YAAY7I,MAAM,IAAID,UAAU,CAAC6I,uBAAuB,CAACC,YAAY,EAAEuH,KAAK,CAAC,IAAIxH,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IAAIlD,uBAAuB,CAACC,YAAY,CAACuH,KAAK,CAAC,CAAC,KAAKxH,uBAAuB,CAACK,kBAAkB,YAAYjJ,MAAM,IAAID,UAAU,CAAC6I,uBAAuB,CAACK,kBAAkB,EAAEoH,MAAM,CAAC,IAAIzH,uBAAuB,CAACK,kBAAkB,YAAY6C,QAAQ,IAAIlD,uBAAuB,CAACK,kBAAkB,CAACoH,MAAM,CAAC,CAAC;MAC1f;MACA;MACAA,MAAM,KAAK,IAAI,IAAIzH,uBAAuB,CAACM,8BAA8B,KAAKN,uBAAuB,CAACC,YAAY,YAAY7I,MAAM,IAAID,UAAU,CAAC6I,uBAAuB,CAACC,YAAY,EAAEhH,KAAK,CAAC,IAAI+G,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IAAIlD,uBAAuB,CAACC,YAAY,CAAChH,KAAK,CAAC,CAAC,EAAE,CAAC,KAAM;QAClT,OAAO,KAAK;MACd;MACA;IACF,CAAC,MAAM,IAAI8I,mBAAmB,CAAC0F,MAAM,CAAC,EAAE,CAAC,KAAM,IAAItQ,UAAU,CAACwI,gBAAgB,EAAEhJ,aAAa,CAACsC,KAAK,EAAE0B,eAAe,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAM,IAAI,CAAC8M,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KAAKD,KAAK,KAAK,QAAQ,IAAI3Q,aAAa,CAACoC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI4I,aAAa,CAAC2F,KAAK,CAAC,EAAE,CAAC,KAAM,IAAI7G,uBAAuB,IAAI,CAACxJ,UAAU,CAACuD,iBAAiB,EAAE/D,aAAa,CAACsC,KAAK,EAAE0B,eAAe,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAM,IAAI1B,KAAK,EAAE;MACja,OAAO,KAAK;IACd,CAAC,MAAM;IACP,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM+N,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC/C,OAAO,EAAE;IACpE,OAAOA,OAAO,KAAK,gBAAgB,IAAIxN,WAAW,CAACwN,OAAO,EAAEpJ,cAAc,CAAC;EAC7E,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM6M,mBAAmB,GAAG,SAASA,mBAAmBA,CAAChB,WAAW,EAAE;IACpE;IACAD,aAAa,CAAChH,KAAK,CAACzC,wBAAwB,EAAE0J,WAAW,EAAE,IAAI,CAAC;IAChE,MAAM;MACJJ;IACF,CAAC,GAAGI,WAAW;IACf;IACA,IAAI,CAACJ,UAAU,IAAIH,YAAY,CAACO,WAAW,CAAC,EAAE;MAC5C;IACF;IACA,MAAMiB,SAAS,GAAG;MAChBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAEjI,YAAY;MAC/BkI,aAAa,EAAE1P;IACjB,CAAC;IACD,IAAIC,CAAC,GAAG+N,UAAU,CAACxO,MAAM;IACzB;IACA,OAAOS,CAAC,EAAE,EAAE;MACV,MAAM0P,IAAI,GAAG3B,UAAU,CAAC/N,CAAC,CAAC;MAC1B,MAAM;QACJkM,IAAI;QACJP,YAAY;QACZjL,KAAK,EAAE4O;MACT,CAAC,GAAGI,IAAI;MACR,MAAMR,MAAM,GAAGpP,iBAAiB,CAACoM,IAAI,CAAC;MACtC,MAAMyD,SAAS,GAAGL,SAAS;MAC3B,IAAI5O,KAAK,GAAGwL,IAAI,KAAK,OAAO,GAAGyD,SAAS,GAAGnR,UAAU,CAACmR,SAAS,CAAC;MAChE;MACAP,SAAS,CAACC,QAAQ,GAAGH,MAAM;MAC3BE,SAAS,CAACE,SAAS,GAAG5O,KAAK;MAC3B0O,SAAS,CAACG,QAAQ,GAAG,IAAI;MACzBH,SAAS,CAACK,aAAa,GAAG1P,SAAS,CAAC,CAAC;MACrCmO,aAAa,CAAChH,KAAK,CAACtC,qBAAqB,EAAEuJ,WAAW,EAAEiB,SAAS,CAAC;MAClE1O,KAAK,GAAG0O,SAAS,CAACE,SAAS;MAC3B;AACN;AACA;MACM,IAAIvG,oBAAoB,KAAKmG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;QAClE;QACAjD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;QACAzN,KAAK,GAAGsI,2BAA2B,GAAGtI,KAAK;MAC7C;MACA;MACA,IAAI6H,YAAY,IAAI3J,UAAU,CAAC,+BAA+B,EAAE8B,KAAK,CAAC,EAAE;QACtEuL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MACA;MACA,IAAIiB,SAAS,CAACK,aAAa,EAAE;QAC3B;MACF;MACA;MACA,IAAI,CAACL,SAAS,CAACG,QAAQ,EAAE;QACvBtD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MACA;MACA,IAAI,CAAC9F,wBAAwB,IAAIzJ,UAAU,CAAC,MAAM,EAAE8B,KAAK,CAAC,EAAE;QAC1DuL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MACA;MACA,IAAI7F,kBAAkB,EAAE;QACtBtL,YAAY,CAAC,CAAC6E,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAEgN,IAAI,IAAI;UAC3DrO,KAAK,GAAGtC,aAAa,CAACsC,KAAK,EAAEqO,IAAI,EAAE,GAAG,CAAC;QACzC,CAAC,CAAC;MACJ;MACA;MACA,MAAME,KAAK,GAAGnP,iBAAiB,CAACqO,WAAW,CAACN,QAAQ,CAAC;MACrD,IAAI,CAACmB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAExO,KAAK,CAAC,EAAE;QAC5CuL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MACA;MACA,IAAIxH,kBAAkB,IAAI,OAAOpD,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,CAACqM,gBAAgB,KAAK,UAAU,EAAE;QACjH,IAAIjE,YAAY,EAAE,CAAC,KAAM;UACvB,QAAQpI,YAAY,CAACqM,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;YAClD,KAAK,aAAa;cAChB;gBACExO,KAAK,GAAGiG,kBAAkB,CAAC5C,UAAU,CAACrD,KAAK,CAAC;gBAC5C;cACF;YACF,KAAK,kBAAkB;cACrB;gBACEA,KAAK,GAAGiG,kBAAkB,CAAC3C,eAAe,CAACtD,KAAK,CAAC;gBACjD;cACF;UACJ;QACF;MACF;MACA;MACA,IAAIA,KAAK,KAAKiP,SAAS,EAAE;QACvB,IAAI;UACF,IAAIhE,YAAY,EAAE;YAChBwC,WAAW,CAAC0B,cAAc,CAAClE,YAAY,EAAEO,IAAI,EAAExL,KAAK,CAAC;UACvD,CAAC,MAAM;YACL;YACAyN,WAAW,CAAC7B,YAAY,CAACJ,IAAI,EAAExL,KAAK,CAAC;UACvC;UACA,IAAIkN,YAAY,CAACO,WAAW,CAAC,EAAE;YAC7BrC,YAAY,CAACqC,WAAW,CAAC;UAC3B,CAAC,MAAM;YACL5Q,QAAQ,CAACyH,SAAS,CAACG,OAAO,CAAC;UAC7B;QACF,CAAC,CAAC,OAAOjB,CAAC,EAAE;UACV+H,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACrC;MACF;IACF;IACA;IACAD,aAAa,CAAChH,KAAK,CAAC5C,uBAAuB,EAAE6J,WAAW,EAAE,IAAI,CAAC;EACjE,CAAC;EACD;AACF;AACA;AACA;AACA;EACE,MAAM2B,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;IAC/D,IAAIC,UAAU,GAAG,IAAI;IACrB,MAAMC,cAAc,GAAG3C,mBAAmB,CAACyC,QAAQ,CAAC;IACpD;IACA7B,aAAa,CAAChH,KAAK,CAACvC,uBAAuB,EAAEoL,QAAQ,EAAE,IAAI,CAAC;IAC5D,OAAOC,UAAU,GAAGC,cAAc,CAACC,QAAQ,CAAC,CAAC,EAAE;MAC7C;MACAhC,aAAa,CAAChH,KAAK,CAACpC,sBAAsB,EAAEkL,UAAU,EAAE,IAAI,CAAC;MAC7D;MACA1B,iBAAiB,CAAC0B,UAAU,CAAC;MAC7B;MACAb,mBAAmB,CAACa,UAAU,CAAC;MAC/B;MACA,IAAIA,UAAU,CAACvJ,OAAO,YAAYhB,gBAAgB,EAAE;QAClDqK,kBAAkB,CAACE,UAAU,CAACvJ,OAAO,CAAC;MACxC;IACF;IACA;IACAyH,aAAa,CAAChH,KAAK,CAAC1C,sBAAsB,EAAEuL,QAAQ,EAAE,IAAI,CAAC;EAC7D,CAAC;EACD;EACA/K,SAAS,CAACmL,QAAQ,GAAG,UAAU3D,KAAK,EAAE;IACpC,IAAI3B,GAAG,GAAGvL,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKS,SAAS,GAAGT,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChF,IAAI2N,IAAI,GAAG,IAAI;IACf,IAAImD,YAAY,GAAG,IAAI;IACvB,IAAIjC,WAAW,GAAG,IAAI;IACtB,IAAIkC,UAAU,GAAG,IAAI;IACrB;AACJ;AACA;IACIvG,cAAc,GAAG,CAAC0C,KAAK;IACvB,IAAI1C,cAAc,EAAE;MAClB0C,KAAK,GAAG,OAAO;IACjB;IACA;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACyB,OAAO,CAACzB,KAAK,CAAC,EAAE;MAChD,IAAI,OAAOA,KAAK,CAACvO,QAAQ,KAAK,UAAU,EAAE;QACxCuO,KAAK,GAAGA,KAAK,CAACvO,QAAQ,CAAC,CAAC;QACxB,IAAI,OAAOuO,KAAK,KAAK,QAAQ,EAAE;UAC7B,MAAMzN,eAAe,CAAC,iCAAiC,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC;MACrD;IACF;IACA;IACA,IAAI,CAACiG,SAAS,CAACM,WAAW,EAAE;MAC1B,OAAOkH,KAAK;IACd;IACA;IACA,IAAI,CAAC/D,UAAU,EAAE;MACfmC,YAAY,CAACC,GAAG,CAAC;IACnB;IACA;IACA7F,SAAS,CAACG,OAAO,GAAG,EAAE;IACtB;IACA,IAAI,OAAOqH,KAAK,KAAK,QAAQ,EAAE;MAC7BtD,QAAQ,GAAG,KAAK;IAClB;IACA,IAAIA,QAAQ,EAAE;MACZ;MACA,IAAIsD,KAAK,CAACqB,QAAQ,EAAE;QAClB,MAAMnC,OAAO,GAAG5L,iBAAiB,CAAC0M,KAAK,CAACqB,QAAQ,CAAC;QACjD,IAAI,CAACxG,YAAY,CAACqE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;UAClD,MAAM3M,eAAe,CAAC,yDAAyD,CAAC;QAClF;MACF;IACF,CAAC,MAAM,IAAIyN,KAAK,YAAY7G,IAAI,EAAE;MAChC;AACN;MACMsH,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC;MAC/B6D,YAAY,GAAGnD,IAAI,CAACvG,aAAa,CAACO,UAAU,CAACuF,KAAK,EAAE,IAAI,CAAC;MACzD,IAAI4D,YAAY,CAAChL,QAAQ,KAAK3C,SAAS,CAACxC,OAAO,IAAImQ,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAAE;QACnF;QACAZ,IAAI,GAAGmD,YAAY;MACrB,CAAC,MAAM,IAAIA,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAAE;QAC3CZ,IAAI,GAAGmD,YAAY;MACrB,CAAC,MAAM;QACL;QACAnD,IAAI,CAACqD,WAAW,CAACF,YAAY,CAAC;MAChC;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACzH,UAAU,IAAI,CAACL,kBAAkB,IAAI,CAACE,cAAc;MACzD;MACAgE,KAAK,CAACjO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACzB,OAAOoI,kBAAkB,IAAIkC,mBAAmB,GAAGlC,kBAAkB,CAAC5C,UAAU,CAACyI,KAAK,CAAC,GAAGA,KAAK;MACjG;MACA;MACAS,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC;MAC3B;MACA,IAAI,CAACS,IAAI,EAAE;QACT,OAAOtE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGjC,SAAS,GAAG,EAAE;MACjE;IACF;IACA;IACA,IAAIqG,IAAI,IAAIvE,UAAU,EAAE;MACtBoD,YAAY,CAACmB,IAAI,CAACsD,UAAU,CAAC;IAC/B;IACA;IACA,MAAMC,YAAY,GAAGlD,mBAAmB,CAACpE,QAAQ,GAAGsD,KAAK,GAAGS,IAAI,CAAC;IACjE;IACA,OAAOkB,WAAW,GAAGqC,YAAY,CAACN,QAAQ,CAAC,CAAC,EAAE;MAC5C;MACA5B,iBAAiB,CAACH,WAAW,CAAC;MAC9B;MACAgB,mBAAmB,CAAChB,WAAW,CAAC;MAChC;MACA,IAAIA,WAAW,CAAC1H,OAAO,YAAYhB,gBAAgB,EAAE;QACnDqK,kBAAkB,CAAC3B,WAAW,CAAC1H,OAAO,CAAC;MACzC;IACF;IACA;IACA,IAAIyC,QAAQ,EAAE;MACZ,OAAOsD,KAAK;IACd;IACA;IACA,IAAI7D,UAAU,EAAE;MACd,IAAIC,mBAAmB,EAAE;QACvByH,UAAU,GAAGtJ,sBAAsB,CAACsG,IAAI,CAACJ,IAAI,CAACvG,aAAa,CAAC;QAC5D,OAAOuG,IAAI,CAACsD,UAAU,EAAE;UACtB;UACAF,UAAU,CAACC,WAAW,CAACrD,IAAI,CAACsD,UAAU,CAAC;QACzC;MACF,CAAC,MAAM;QACLF,UAAU,GAAGpD,IAAI;MACnB;MACA,IAAI1F,YAAY,CAACkJ,UAAU,IAAIlJ,YAAY,CAACmJ,cAAc,EAAE;QAC1D;AACR;AACA;AACA;AACA;AACA;AACA;QACQL,UAAU,GAAGpJ,UAAU,CAACoG,IAAI,CAAC9H,gBAAgB,EAAE8K,UAAU,EAAE,IAAI,CAAC;MAClE;MACA,OAAOA,UAAU;IACnB;IACA,IAAIM,cAAc,GAAGnI,cAAc,GAAGyE,IAAI,CAAC2D,SAAS,GAAG3D,IAAI,CAACD,SAAS;IACrE;IACA,IAAIxE,cAAc,IAAInB,YAAY,CAAC,UAAU,CAAC,IAAI4F,IAAI,CAACvG,aAAa,IAAIuG,IAAI,CAACvG,aAAa,CAACmK,OAAO,IAAI5D,IAAI,CAACvG,aAAa,CAACmK,OAAO,CAAC3E,IAAI,IAAItN,UAAU,CAACyD,YAAY,EAAE4K,IAAI,CAACvG,aAAa,CAACmK,OAAO,CAAC3E,IAAI,CAAC,EAAE;MAClMyE,cAAc,GAAG,YAAY,GAAG1D,IAAI,CAACvG,aAAa,CAACmK,OAAO,CAAC3E,IAAI,GAAG,KAAK,GAAGyE,cAAc;IAC1F;IACA;IACA,IAAIrI,kBAAkB,EAAE;MACtBtL,YAAY,CAAC,CAAC6E,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAEgN,IAAI,IAAI;QAC3D4B,cAAc,GAAGvS,aAAa,CAACuS,cAAc,EAAE5B,IAAI,EAAE,GAAG,CAAC;MAC3D,CAAC,CAAC;IACJ;IACA,OAAOpI,kBAAkB,IAAIkC,mBAAmB,GAAGlC,kBAAkB,CAAC5C,UAAU,CAAC4M,cAAc,CAAC,GAAGA,cAAc;EACnH,CAAC;EACD3L,SAAS,CAAC8L,SAAS,GAAG,YAAY;IAChC,IAAIjG,GAAG,GAAGvL,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKS,SAAS,GAAGT,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChFsL,YAAY,CAACC,GAAG,CAAC;IACjBpC,UAAU,GAAG,IAAI;EACnB,CAAC;EACDzD,SAAS,CAAC+L,WAAW,GAAG,YAAY;IAClCxG,MAAM,GAAG,IAAI;IACb9B,UAAU,GAAG,KAAK;EACpB,CAAC;EACDzD,SAAS,CAACgM,gBAAgB,GAAG,UAAUC,GAAG,EAAEvB,IAAI,EAAEhP,KAAK,EAAE;IACvD;IACA,IAAI,CAAC6J,MAAM,EAAE;MACXK,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB;IACA,MAAMqE,KAAK,GAAGnP,iBAAiB,CAACmR,GAAG,CAAC;IACpC,MAAM/B,MAAM,GAAGpP,iBAAiB,CAAC4P,IAAI,CAAC;IACtC,OAAOV,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAExO,KAAK,CAAC;EAChD,CAAC;EACDsE,SAAS,CAACkM,OAAO,GAAG,UAAUC,UAAU,EAAEC,YAAY,EAAE;IACtD,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;MACtC;IACF;IACA3T,SAAS,CAACyJ,KAAK,CAACiK,UAAU,CAAC,EAAEC,YAAY,CAAC;EAC5C,CAAC;EACDpM,SAAS,CAACqM,UAAU,GAAG,UAAUF,UAAU,EAAEC,YAAY,EAAE;IACzD,IAAIA,YAAY,KAAKrR,SAAS,EAAE;MAC9B,MAAMK,KAAK,GAAG/C,gBAAgB,CAAC6J,KAAK,CAACiK,UAAU,CAAC,EAAEC,YAAY,CAAC;MAC/D,OAAOhR,KAAK,KAAK,CAAC,CAAC,GAAGL,SAAS,GAAGpC,WAAW,CAACuJ,KAAK,CAACiK,UAAU,CAAC,EAAE/Q,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/E;IACA,OAAO7C,QAAQ,CAAC2J,KAAK,CAACiK,UAAU,CAAC,CAAC;EACpC,CAAC;EACDnM,SAAS,CAACsM,WAAW,GAAG,UAAUH,UAAU,EAAE;IAC5CjK,KAAK,CAACiK,UAAU,CAAC,GAAG,EAAE;EACxB,CAAC;EACDnM,SAAS,CAACuM,cAAc,GAAG,YAAY;IACrCrK,KAAK,GAAG7C,eAAe,CAAC,CAAC;EAC3B,CAAC;EACD,OAAOW,SAAS;AAClB;AACA,IAAIwM,MAAM,GAAGzM,eAAe,CAAC,CAAC;AAE9B,SAASyM,MAAM,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}