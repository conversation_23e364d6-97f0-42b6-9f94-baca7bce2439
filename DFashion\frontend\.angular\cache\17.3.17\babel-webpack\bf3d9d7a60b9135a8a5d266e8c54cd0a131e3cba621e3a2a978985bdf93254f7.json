{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { Subject } from 'rxjs';\nimport { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\nimport { ProductDialogComponent } from './product-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/product.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/table\";\nimport * as i10 from \"@angular/material/paginator\";\nimport * as i11 from \"@angular/material/sort\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/tooltip\";\nconst _c0 = () => [5, 10, 25, 50];\nfunction ProductManagementComponent_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r1.label, \" \");\n  }\n}\nfunction ProductManagementComponent_mat_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r2.label, \" \");\n  }\n}\nfunction ProductManagementComponent_th_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Product\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"img\", 26);\n    i0.ɵɵelementStart(3, \"div\", 27)(4, \"div\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.getProductImage(product_r3), i0.ɵɵsanitizeUrl)(\"alt\", product_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r3.brand);\n  }\n}\nfunction ProductManagementComponent_th_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"Category\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"span\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementStart(5, \"small\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, product_r5.category));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, product_r5.subcategory));\n  }\n}\nfunction ProductManagementComponent_th_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"Price\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_39_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r6.originalPrice, \"\");\n  }\n}\nfunction ProductManagementComponent_td_39_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", product_r6.discount, \"% OFF\");\n  }\n}\nfunction ProductManagementComponent_td_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 33)(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProductManagementComponent_td_39_span_4_Template, 2, 1, \"span\", 35)(5, ProductManagementComponent_td_39_span_5_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r6.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.discount > 0);\n  }\n}\nfunction ProductManagementComponent_th_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"div\", 40);\n    i0.ɵɵelementStart(3, \"span\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r3.getStatusColor(product_r7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getStatusText(product_r7));\n  }\n}\nfunction ProductManagementComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 42)(2, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ProductManagementComponent_td_45_Template_button_click_2_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openProductDialog(product_r9));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProductManagementComponent_td_45_Template_button_click_5_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleProductStatus(product_r9));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProductManagementComponent_td_45_Template_button_click_8_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.deleteProduct(product_r9));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matTooltip\", product_r9.isActive ? \"Deactivate product\" : \"Activate product\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r9.isActive ? \"visibility_off\" : \"visibility\");\n  }\n}\nfunction ProductManagementComponent_tr_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 46);\n  }\n}\nfunction ProductManagementComponent_tr_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 47);\n  }\n}\nfunction ProductManagementComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"inventory_2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No products found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or add a new product.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProductManagementComponent {\n  constructor(productService, dialog, snackBar) {\n    this.productService = productService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.destroy$ = new Subject();\n    this.displayedColumns = ['product', 'category', 'price', 'status', 'actions'];\n    this.dataSource = new MatTableDataSource([]);\n    this.isLoading = false;\n    this.totalProducts = 0;\n    // Filters\n    this.searchControl = new FormControl('');\n    this.categoryFilter = new FormControl('');\n    this.statusFilter = new FormControl('');\n    this.categories = [{\n      value: '',\n      label: 'All Categories'\n    }, {\n      value: 'men',\n      label: 'Men'\n    }, {\n      value: 'women',\n      label: 'Women'\n    }, {\n      value: 'children',\n      label: 'Children'\n    }];\n    this.statuses = [{\n      value: '',\n      label: 'All Statuses'\n    }, {\n      value: 'true',\n      label: 'Active'\n    }, {\n      value: 'false',\n      label: 'Inactive'\n    }];\n  }\n  ngOnInit() {\n    this.setupFilters();\n    this.loadProducts();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setupFilters() {\n    this.searchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {\n      this.loadProducts();\n    });\n    [this.categoryFilter, this.statusFilter].forEach(control => {\n      control.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.loadProducts();\n      });\n    });\n  }\n  loadProducts() {\n    this.isLoading = true;\n    // Load from API - empty for now\n    this.dataSource.data = [];\n    this.totalProducts = 0;\n    this.isLoading = false;\n  }\n  onPageChange() {\n    this.loadProducts();\n  }\n  openProductDialog(product) {\n    const dialogRef = this.dialog.open(ProductDialogComponent, {\n      width: '800px',\n      data: product ? {\n        ...product\n      } : null\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.loadProducts();\n      }\n    });\n  }\n  toggleProductStatus(product) {\n    this.snackBar.open('Product status updated', 'Close', {\n      duration: 3000\n    });\n  }\n  deleteProduct(product) {\n    if (confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n      this.snackBar.open('Product deleted', 'Close', {\n        duration: 3000\n      });\n      this.loadProducts();\n    }\n  }\n  getProductImage(product) {\n    return product.images?.[0]?.url || '/assets/images/placeholder-product.jpg';\n  }\n  getStatusColor(product) {\n    return product.isActive ? '#4caf50' : '#f44336';\n  }\n  getStatusText(product) {\n    return product.isActive ? 'Active' : 'Inactive';\n  }\n  static {\n    this.ɵfac = function ProductManagementComponent_Factory(t) {\n      return new (t || ProductManagementComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProductManagementComponent,\n      selectors: [[\"app-product-management\"]],\n      viewQuery: function ProductManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      decls: 50,\n      vars: 13,\n      consts: [[1, \"product-management\"], [1, \"filters-section\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Search by name, brand, or category\", 3, \"formControl\"], [\"matSuffix\", \"\"], [3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"product\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"category\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"price\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"page\", \"length\", \"pageSize\", \"pageSizeOptions\"], [3, \"value\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"product-cell\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [1, \"category-text\"], [1, \"subcategory-text\"], [1, \"price-cell\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"discount\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"discount\"], [1, \"status-indicator\"], [1, \"status-dot\"], [1, \"status-text\"], [1, \"actions-cell\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Edit product\", 3, \"click\"], [\"mat-icon-button\", \"\", 3, \"click\", \"matTooltip\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete product\", \"color\", \"warn\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"]],\n      template: function ProductManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4, \"Product Management\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n          i0.ɵɵtext(6, \"Manage products, inventory, and pricing\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 1)(9, \"mat-form-field\", 2)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Search products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 3);\n          i0.ɵɵelementStart(13, \"mat-icon\", 4);\n          i0.ɵɵtext(14, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-form-field\", 2)(16, \"mat-label\");\n          i0.ɵɵtext(17, \"Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-select\", 5);\n          i0.ɵɵtemplate(19, ProductManagementComponent_mat_option_19_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"mat-form-field\", 2)(21, \"mat-label\");\n          i0.ɵɵtext(22, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"mat-select\", 5);\n          i0.ɵɵtemplate(24, ProductManagementComponent_mat_option_24_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ProductManagementComponent_Template_button_click_25_listener() {\n            return ctx.openProductDialog();\n          });\n          i0.ɵɵelementStart(26, \"mat-icon\");\n          i0.ɵɵtext(27, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \" Add Product \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 8)(30, \"table\", 9);\n          i0.ɵɵelementContainerStart(31, 10);\n          i0.ɵɵtemplate(32, ProductManagementComponent_th_32_Template, 2, 0, \"th\", 11)(33, ProductManagementComponent_td_33_Template, 8, 4, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(34, 13);\n          i0.ɵɵtemplate(35, ProductManagementComponent_th_35_Template, 2, 0, \"th\", 14)(36, ProductManagementComponent_td_36_Template, 8, 6, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(37, 15);\n          i0.ɵɵtemplate(38, ProductManagementComponent_th_38_Template, 2, 0, \"th\", 14)(39, ProductManagementComponent_td_39_Template, 6, 3, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(40, 16);\n          i0.ɵɵtemplate(41, ProductManagementComponent_th_41_Template, 2, 0, \"th\", 11)(42, ProductManagementComponent_td_42_Template, 5, 3, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(43, 17);\n          i0.ɵɵtemplate(44, ProductManagementComponent_th_44_Template, 2, 0, \"th\", 11)(45, ProductManagementComponent_td_45_Template, 11, 2, \"td\", 12);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(46, ProductManagementComponent_tr_46_Template, 1, 0, \"tr\", 18)(47, ProductManagementComponent_tr_47_Template, 1, 0, \"tr\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, ProductManagementComponent_div_48_Template, 7, 0, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"mat-paginator\", 21);\n          i0.ɵɵlistener(\"page\", function ProductManagementComponent_Template_mat_paginator_page_49_listener() {\n            return ctx.onPageChange();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formControl\", ctx.searchControl);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.categoryFilter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formControl\", ctx.statusFilter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.statuses);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dataSource.data.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"length\", ctx.totalProducts)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(12, _c0));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlDirective, i6.MatIcon, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatTable, i9.MatHeaderCellDef, i9.MatHeaderRowDef, i9.MatColumnDef, i9.MatCellDef, i9.MatRowDef, i9.MatHeaderCell, i9.MatCell, i9.MatHeaderRow, i9.MatRow, i10.MatPaginator, i11.MatSort, i11.MatSortHeader, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatSuffix, i14.MatSelect, i15.MatOption, i16.MatTooltip, i4.TitleCasePipe],\n      styles: [\".product-management[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n  flex-wrap: wrap;\\n  align-items: center;\\n}\\n.filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n.filters-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  margin-top: 0.5rem;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  margin-bottom: 1rem;\\n}\\n\\n.product-cell[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.product-cell[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  object-fit: cover;\\n  border-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n.product-cell[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n}\\n.product-cell[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n\\n.category-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.subcategory-text[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.price-cell[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2e7d32;\\n}\\n.price-cell[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #999;\\n  margin-left: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n.price-cell[_ngcontent-%COMP%]   .discount[_ngcontent-%COMP%] {\\n  background: #ff5722;\\n  color: white;\\n  padding: 0.125rem 0.375rem;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  margin-left: 0.5rem;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.status-indicator[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n.status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.actions-cell[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem;\\n  color: #666;\\n}\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 1rem 0 0.5rem 0;\\n  font-weight: 400;\\n}\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .filters-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n  .product-cell[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.5rem;\\n  }\\n  .product-cell[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4vcHJvZHVjdHMvcHJvZHVjdC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxtQkFBQTtBQUNGO0FBQ0U7RUFDRSxnQkFBQTtBQUNKO0FBRUU7RUFDRSx3QkFBQTtFQUFBLG1CQUFBO0VBQ0Esa0JBQUE7QUFBSjs7QUFJQTtFQUNFLGdCQUFBO0VBQ0EsbUJBQUE7QUFERjs7QUFJQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFERjtBQUdFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7QUFESjtBQUtJO0VBQ0UsZ0JBQUE7RUFDQSxzQkFBQTtBQUhOO0FBTUk7RUFDRSxtQkFBQTtFQUNBLFdBQUE7QUFKTjs7QUFTQTtFQUNFLGdCQUFBO0FBTkY7O0FBU0E7RUFDRSxXQUFBO0FBTkY7O0FBVUU7RUFDRSxnQkFBQTtFQUNBLGNBQUE7QUFQSjtBQVVFO0VBQ0UsNkJBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQVJKO0FBV0U7RUFDRSxtQkFBQTtFQUNBLFlBQUE7RUFDQSwwQkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtBQVRKOztBQWFBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQVZGO0FBWUU7RUFDRSxVQUFBO0VBQ0EsV0FBQTtFQUNBLGtCQUFBO0FBVko7QUFhRTtFQUNFLG1CQUFBO0FBWEo7O0FBZUE7RUFDRSxhQUFBO0VBQ0EsWUFBQTtBQVpGOztBQWVBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsV0FBQTtBQVpGO0FBY0U7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUFaSjtBQWVFO0VBQ0UsdUJBQUE7RUFDQSxnQkFBQTtBQWJKO0FBZ0JFO0VBQ0UsU0FBQTtFQUNBLG1CQUFBO0FBZEo7O0FBa0JBO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLG9CQUFBO0VBZkY7RUFpQkU7SUFDRSxlQUFBO0lBQ0EsV0FBQTtFQWZKO0VBbUJBO0lBQ0Usc0JBQUE7SUFDQSx1QkFBQTtJQUNBLFdBQUE7RUFqQkY7RUFtQkU7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQWpCSjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLnByb2R1Y3QtbWFuYWdlbWVudCB7XG4gIHBhZGRpbmc6IDFyZW07XG59XG5cbi5maWx0ZXJzLXNlY3Rpb24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDFyZW07XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG4gIGZsZXgtd3JhcDogd3JhcDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblxuICBtYXQtZm9ybS1maWVsZCB7XG4gICAgbWluLXdpZHRoOiAyMDBweDtcbiAgfVxuXG4gIGJ1dHRvbiB7XG4gICAgaGVpZ2h0OiBmaXQtY29udGVudDtcbiAgICBtYXJnaW4tdG9wOiAwLjVyZW07XG4gIH1cbn1cblxuLnRhYmxlLWNvbnRhaW5lciB7XG4gIG92ZXJmbG93LXg6IGF1dG87XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG5cbi5wcm9kdWN0LWNlbGwge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDFyZW07XG5cbiAgLnByb2R1Y3QtaW1hZ2Uge1xuICAgIHdpZHRoOiA1MHB4O1xuICAgIGhlaWdodDogNTBweDtcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcbiAgfVxuXG4gIC5wcm9kdWN0LWluZm8ge1xuICAgIC5wcm9kdWN0LW5hbWUge1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtYnJhbmQge1xuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cbiAgfVxufVxuXG4uY2F0ZWdvcnktdGV4dCB7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbi5zdWJjYXRlZ29yeS10ZXh0IHtcbiAgY29sb3I6ICM2NjY7XG59XG5cbi5wcmljZS1jZWxsIHtcbiAgLmN1cnJlbnQtcHJpY2Uge1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY29sb3I6ICMyZTdkMzI7XG4gIH1cblxuICAub3JpZ2luYWwtcHJpY2Uge1xuICAgIHRleHQtZGVjb3JhdGlvbjogbGluZS10aHJvdWdoO1xuICAgIGNvbG9yOiAjOTk5O1xuICAgIG1hcmdpbi1sZWZ0OiAwLjVyZW07XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgfVxuXG4gIC5kaXNjb3VudCB7XG4gICAgYmFja2dyb3VuZDogI2ZmNTcyMjtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgcGFkZGluZzogMC4xMjVyZW0gMC4zNzVyZW07XG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICBtYXJnaW4tbGVmdDogMC41cmVtO1xuICB9XG59XG5cbi5zdGF0dXMtaW5kaWNhdG9yIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG5cbiAgLnN0YXR1cy1kb3Qge1xuICAgIHdpZHRoOiA4cHg7XG4gICAgaGVpZ2h0OiA4cHg7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB9XG5cbiAgLnN0YXR1cy10ZXh0IHtcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xuICB9XG59XG5cbi5hY3Rpb25zLWNlbGwge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDAuMjVyZW07XG59XG5cbi5uby1kYXRhIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwYWRkaW5nOiAzcmVtO1xuICBjb2xvcjogIzY2NjtcblxuICBtYXQtaWNvbiB7XG4gICAgZm9udC1zaXplOiA0cmVtO1xuICAgIHdpZHRoOiA0cmVtO1xuICAgIGhlaWdodDogNHJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgIG9wYWNpdHk6IDAuNTtcbiAgfVxuXG4gIGgzIHtcbiAgICBtYXJnaW46IDFyZW0gMCAwLjVyZW0gMDtcbiAgICBmb250LXdlaWdodDogNDAwO1xuICB9XG5cbiAgcCB7XG4gICAgbWFyZ2luOiAwO1xuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5maWx0ZXJzLXNlY3Rpb24ge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG5cbiAgICBtYXQtZm9ybS1maWVsZCB7XG4gICAgICBtaW4td2lkdGg6IGF1dG87XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICB9XG4gIH1cblxuICAucHJvZHVjdC1jZWxsIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgIGdhcDogMC41cmVtO1xuXG4gICAgLnByb2R1Y3QtaW1hZ2Uge1xuICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICBoZWlnaHQ6IDQwcHg7XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MatTableDataSource", "MatPaginator", "MatSort", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "FormControl", "ProductDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "category_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "status_r2", "ɵɵelement", "ctx_r3", "getProductImage", "product_r3", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "brand", "ɵɵpipeBind1", "product_r5", "category", "subcategory", "product_r6", "originalPrice", "discount", "ɵɵtemplate", "ProductManagementComponent_td_39_span_4_Template", "ProductManagementComponent_td_39_span_5_Template", "price", "ɵɵstyleProp", "getStatusColor", "product_r7", "getStatusText", "ɵɵlistener", "ProductManagementComponent_td_45_Template_button_click_2_listener", "product_r9", "ɵɵrestoreView", "_r8", "$implicit", "ɵɵnextContext", "ɵɵresetView", "openProductDialog", "ProductManagementComponent_td_45_Template_button_click_5_listener", "toggleProductStatus", "ProductManagementComponent_td_45_Template_button_click_8_listener", "deleteProduct", "isActive", "ProductManagementComponent", "constructor", "productService", "dialog", "snackBar", "destroy$", "displayedColumns", "dataSource", "isLoading", "totalProducts", "searchControl", "categoryFilter", "statusFilter", "categories", "statuses", "ngOnInit", "setupFilters", "loadProducts", "ngOnDestroy", "next", "complete", "valueChanges", "pipe", "subscribe", "for<PERSON>ach", "control", "data", "onPageChange", "product", "dialogRef", "open", "width", "afterClosed", "result", "duration", "confirm", "images", "url", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "MatDialog", "i3", "MatSnackBar", "selectors", "viewQuery", "ProductManagementComponent_Query", "rf", "ctx", "ProductManagementComponent_mat_option_19_Template", "ProductManagementComponent_mat_option_24_Template", "ProductManagementComponent_Template_button_click_25_listener", "ɵɵelementContainerStart", "ProductManagementComponent_th_32_Template", "ProductManagementComponent_td_33_Template", "ProductManagementComponent_th_35_Template", "ProductManagementComponent_td_36_Template", "ProductManagementComponent_th_38_Template", "ProductManagementComponent_td_39_Template", "ProductManagementComponent_th_41_Template", "ProductManagementComponent_td_42_Template", "ProductManagementComponent_th_44_Template", "ProductManagementComponent_td_45_Template", "ProductManagementComponent_tr_46_Template", "ProductManagementComponent_tr_47_Template", "ProductManagementComponent_div_48_Template", "ProductManagementComponent_Template_mat_paginator_page_49_listener", "length", "ɵɵpureFunction0", "_c0"], "sources": ["E:\\DFashion\\frontend\\src\\app\\admin\\products\\product-management.component.ts"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Subject } from 'rxjs';\nimport { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\nimport { ProductService } from '../services/product.service';\nimport { ProductDialogComponent } from './product-dialog.component';\n\nexport interface Product {\n  _id: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice?: number;\n  discount: number;\n  category: string;\n  subcategory: string;\n  brand: string;\n  images: any[];\n  sizes: any[];\n  colors: any[];\n  vendor: any;\n  isActive: boolean;\n  isFeatured: boolean;\n  rating: { average: number; count: number };\n  createdAt: string;\n}\n\n@Component({\n  selector: 'app-product-management',\n  template: `\n    <div class=\"product-management\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>Product Management</mat-card-title>\n          <mat-card-subtitle>Manage products, inventory, and pricing</mat-card-subtitle>\n        </mat-card-header>\n        \n        <mat-card-content>\n          <!-- Filters -->\n          <div class=\"filters-section\">\n            <mat-form-field appearance=\"outline\">\n              <mat-label>Search products</mat-label>\n              <input matInput [formControl]=\"searchControl\" placeholder=\"Search by name, brand, or category\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n            \n            <mat-form-field appearance=\"outline\">\n              <mat-label>Category</mat-label>\n              <mat-select [formControl]=\"categoryFilter\">\n                <mat-option *ngFor=\"let category of categories\" [value]=\"category.value\">\n                  {{ category.label }}\n                </mat-option>\n              </mat-select>\n            </mat-form-field>\n            \n            <mat-form-field appearance=\"outline\">\n              <mat-label>Status</mat-label>\n              <mat-select [formControl]=\"statusFilter\">\n                <mat-option *ngFor=\"let status of statuses\" [value]=\"status.value\">\n                  {{ status.label }}\n                </mat-option>\n              </mat-select>\n            </mat-form-field>\n            \n            <button mat-raised-button color=\"primary\" (click)=\"openProductDialog()\">\n              <mat-icon>add</mat-icon>\n              Add Product\n            </button>\n          </div>\n          \n          <!-- Products Table -->\n          <div class=\"table-container\">\n            <table mat-table [dataSource]=\"dataSource\" matSort>\n              <!-- Product Column -->\n              <ng-container matColumnDef=\"product\">\n                <th mat-header-cell *matHeaderCellDef>Product</th>\n                <td mat-cell *matCellDef=\"let product\">\n                  <div class=\"product-cell\">\n                    <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" class=\"product-image\">\n                    <div class=\"product-info\">\n                      <div class=\"product-name\">{{ product.name }}</div>\n                      <div class=\"product-brand\">{{ product.brand }}</div>\n                    </div>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <!-- Category Column -->\n              <ng-container matColumnDef=\"category\">\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Category</th>\n                <td mat-cell *matCellDef=\"let product\">\n                  <span class=\"category-text\">{{ product.category | titlecase }}</span>\n                  <br>\n                  <small class=\"subcategory-text\">{{ product.subcategory | titlecase }}</small>\n                </td>\n              </ng-container>\n              \n              <!-- Price Column -->\n              <ng-container matColumnDef=\"price\">\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Price</th>\n                <td mat-cell *matCellDef=\"let product\">\n                  <div class=\"price-cell\">\n                    <span class=\"current-price\">₹{{ product.price }}</span>\n                    <span *ngIf=\"product.originalPrice\" class=\"original-price\">₹{{ product.originalPrice }}</span>\n                    <span *ngIf=\"product.discount > 0\" class=\"discount\">{{ product.discount }}% OFF</span>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <!-- Status Column -->\n              <ng-container matColumnDef=\"status\">\n                <th mat-header-cell *matHeaderCellDef>Status</th>\n                <td mat-cell *matCellDef=\"let product\">\n                  <div class=\"status-indicator\">\n                    <div class=\"status-dot\" [style.background-color]=\"getStatusColor(product)\"></div>\n                    <span class=\"status-text\">{{ getStatusText(product) }}</span>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <!-- Actions Column -->\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let product\">\n                  <div class=\"actions-cell\">\n                    <button mat-icon-button matTooltip=\"Edit product\" (click)=\"openProductDialog(product)\">\n                      <mat-icon>edit</mat-icon>\n                    </button>\n                    <button mat-icon-button \n                            [matTooltip]=\"product.isActive ? 'Deactivate product' : 'Activate product'\"\n                            (click)=\"toggleProductStatus(product)\">\n                      <mat-icon>{{ product.isActive ? 'visibility_off' : 'visibility' }}</mat-icon>\n                    </button>\n                    <button mat-icon-button matTooltip=\"Delete product\" color=\"warn\" (click)=\"deleteProduct(product)\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n                </td>\n              </ng-container>\n              \n              <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n            </table>\n            \n            <!-- No Data Message -->\n            <div *ngIf=\"dataSource.data.length === 0\" class=\"no-data\">\n              <mat-icon>inventory_2</mat-icon>\n              <h3>No products found</h3>\n              <p>Try adjusting your search criteria or add a new product.</p>\n            </div>\n          </div>\n          \n          <!-- Paginator -->\n          <mat-paginator \n            [length]=\"totalProducts\"\n            [pageSize]=\"10\"\n            [pageSizeOptions]=\"[5, 10, 25, 50]\"\n            (page)=\"onPageChange()\"\n            showFirstLastButtons>\n          </mat-paginator>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styleUrls: ['./product-management.component.scss']\n})\nexport class ProductManagementComponent implements OnInit, OnDestroy {\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  private destroy$ = new Subject<void>();\n  \n  displayedColumns: string[] = ['product', 'category', 'price', 'status', 'actions'];\n  dataSource = new MatTableDataSource<Product>([]);\n  isLoading = false;\n  totalProducts = 0;\n  \n  // Filters\n  searchControl = new FormControl('');\n  categoryFilter = new FormControl('');\n  statusFilter = new FormControl('');\n  \n  categories = [\n    { value: '', label: 'All Categories' },\n    { value: 'men', label: 'Men' },\n    { value: 'women', label: 'Women' },\n    { value: 'children', label: 'Children' }\n  ];\n  \n  statuses = [\n    { value: '', label: 'All Statuses' },\n    { value: 'true', label: 'Active' },\n    { value: 'false', label: 'Inactive' }\n  ];\n\n  constructor(\n    private productService: ProductService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.setupFilters();\n    this.loadProducts();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  setupFilters(): void {\n    this.searchControl.valueChanges.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      takeUntil(this.destroy$)\n    ).subscribe(() => {\n      this.loadProducts();\n    });\n\n    [this.categoryFilter, this.statusFilter].forEach(control => {\n      control.valueChanges.pipe(\n        takeUntil(this.destroy$)\n      ).subscribe(() => {\n        this.loadProducts();\n      });\n    });\n  }\n\n  loadProducts(): void {\n    this.isLoading = true;\n\n    // Load from API - empty for now\n    this.dataSource.data = [];\n    this.totalProducts = 0;\n    this.isLoading = false;\n  }\n\n  onPageChange(): void {\n    this.loadProducts();\n  }\n\n  openProductDialog(product?: Product): void {\n    const dialogRef = this.dialog.open(ProductDialogComponent, {\n      width: '800px',\n      data: product ? { ...product } : null\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.loadProducts();\n      }\n    });\n  }\n\n  toggleProductStatus(product: Product): void {\n    this.snackBar.open('Product status updated', 'Close', { duration: 3000 });\n  }\n\n  deleteProduct(product: Product): void {\n    if (confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n      this.snackBar.open('Product deleted', 'Close', { duration: 3000 });\n      this.loadProducts();\n    }\n  }\n\n  getProductImage(product: Product): string {\n    return product.images?.[0]?.url || '/assets/images/placeholder-product.jpg';\n  }\n\n  getStatusColor(product: Product): string {\n    return product.isActive ? '#4caf50' : '#f44336';\n  }\n\n  getStatusText(product: Product): string {\n    return product.isActive ? 'Active' : 'Inactive';\n  }\n}\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAGhD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;AAC9E,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,sBAAsB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;;;;;;IA4CnDC,EAAA,CAAAC,cAAA,qBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAAC,KAAA,CAAwB;IACtEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,WAAA,CAAAI,KAAA,MACF;;;;;IAOAT,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+BH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAJ,KAAA,CAAsB;IAChEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,SAAA,CAAAD,KAAA,MACF;;;;;IAeAT,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAuC,cACX;IACxBD,EAAA,CAAAW,SAAA,cAAiF;IAE/EX,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAGpDF,EAHoD,CAAAG,YAAA,EAAM,EAChD,EACF,EACH;;;;;IANIH,EAAA,CAAAO,SAAA,GAAgC;IAACP,EAAjC,CAAAI,UAAA,QAAAQ,MAAA,CAAAC,eAAA,CAAAC,UAAA,GAAAd,EAAA,CAAAe,aAAA,CAAgC,QAAAD,UAAA,CAAAE,IAAA,CAAqB;IAE9BhB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAiB,iBAAA,CAAAH,UAAA,CAAAE,IAAA,CAAkB;IACjBhB,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAiB,iBAAA,CAAAH,UAAA,CAAAI,KAAA,CAAmB;;;;;IAQpDlB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEjEH,EADF,CAAAC,cAAA,aAAuC,eACT;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAW,SAAA,SAAI;IACJX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAqC;;IACvEF,EADuE,CAAAG,YAAA,EAAQ,EAC1E;;;;IAHyBH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAmB,WAAA,OAAAC,UAAA,CAAAC,QAAA,EAAkC;IAE9BrB,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAmB,WAAA,OAAAC,UAAA,CAAAE,WAAA,EAAqC;;;;;IAMvEtB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAI5DH,EAAA,CAAAC,cAAA,eAA2D;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnCH,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAQ,kBAAA,WAAAe,UAAA,CAAAC,aAAA,KAA4B;;;;;IACvFxB,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlCH,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAQ,kBAAA,KAAAe,UAAA,CAAAE,QAAA,UAA2B;;;;;IAF/EzB,EAFJ,CAAAC,cAAA,aAAuC,cACb,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EADA,CAAA0B,UAAA,IAAAC,gDAAA,mBAA2D,IAAAC,gDAAA,mBACP;IAExD5B,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAJ2BH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,kBAAA,WAAAe,UAAA,CAAAM,KAAA,KAAoB;IACzC7B,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAmB,UAAA,CAAAC,aAAA,CAA2B;IAC3BxB,EAAA,CAAAO,SAAA,EAA0B;IAA1BP,EAAA,CAAAI,UAAA,SAAAmB,UAAA,CAAAE,QAAA,KAA0B;;;;;IAOrCzB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAuC,cACP;IAC5BD,EAAA,CAAAW,SAAA,cAAiF;IACjFX,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAE1DF,EAF0D,CAAAG,YAAA,EAAO,EACzD,EACH;;;;;IAHuBH,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA8B,WAAA,qBAAAlB,MAAA,CAAAmB,cAAA,CAAAC,UAAA,EAAkD;IAChDhC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAiB,iBAAA,CAAAL,MAAA,CAAAqB,aAAA,CAAAD,UAAA,EAA4B;;;;;IAO1DhC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAG9CH,EAFJ,CAAAC,cAAA,aAAuC,cACX,iBAC+D;IAArCD,EAAA,CAAAkC,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,UAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAZ,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAAS7B,MAAA,CAAA8B,iBAAA,CAAAN,UAAA,CAA0B;IAAA,EAAC;IACpFpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAE+C;IAAvCD,EAAA,CAAAkC,UAAA,mBAAAS,kEAAA;MAAA,MAAAP,UAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAZ,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAAS7B,MAAA,CAAAgC,mBAAA,CAAAR,UAAA,CAA4B;IAAA,EAAC;IAC5CpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IACpEF,EADoE,CAAAG,YAAA,EAAW,EACtE;IACTH,EAAA,CAAAC,cAAA,iBAAkG;IAAjCD,EAAA,CAAAkC,UAAA,mBAAAW,kEAAA;MAAA,MAAAT,UAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAZ,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAAS7B,MAAA,CAAAkC,aAAA,CAAAV,UAAA,CAAsB;IAAA,EAAC;IAC/FpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAGtBF,EAHsB,CAAAG,YAAA,EAAW,EACpB,EACL,EACH;;;;IAROH,EAAA,CAAAO,SAAA,GAA2E;IAA3EP,EAAA,CAAAI,UAAA,eAAAgC,UAAA,CAAAW,QAAA,6CAA2E;IAEvE/C,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAiB,iBAAA,CAAAmB,UAAA,CAAAW,QAAA,mCAAwD;;;;;IAS1E/C,EAAA,CAAAW,SAAA,aAA4D;;;;;IAC5DX,EAAA,CAAAW,SAAA,aAAkE;;;;;IAKlEX,EADF,CAAAC,cAAA,cAA0D,eAC9C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+DAAwD;IAC7DF,EAD6D,CAAAG,YAAA,EAAI,EAC3D;;;AAiBlB,OAAM,MAAO6C,0BAA0B;EA6BrCC,YACUC,cAA8B,EAC9BC,MAAiB,EACjBC,QAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IA5BV,KAAAC,QAAQ,GAAG,IAAI3D,OAAO,EAAQ;IAEtC,KAAA4D,gBAAgB,GAAa,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;IAClF,KAAAC,UAAU,GAAG,IAAIhE,kBAAkB,CAAU,EAAE,CAAC;IAChD,KAAAiE,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,CAAC;IAEjB;IACA,KAAAC,aAAa,GAAG,IAAI5D,WAAW,CAAC,EAAE,CAAC;IACnC,KAAA6D,cAAc,GAAG,IAAI7D,WAAW,CAAC,EAAE,CAAC;IACpC,KAAA8D,YAAY,GAAG,IAAI9D,WAAW,CAAC,EAAE,CAAC;IAElC,KAAA+D,UAAU,GAAG,CACX;MAAEvD,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAgB,CAAE,EACtC;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAK,CAAE,EAC9B;MAAEH,KAAK,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEH,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAU,CAAE,CACzC;IAED,KAAAqD,QAAQ,GAAG,CACT;MAAExD,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAc,CAAE,EACpC;MAAEH,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAQ,CAAE,EAClC;MAAEH,KAAK,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAU,CAAE,CACtC;EAME;EAEHsD,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,QAAQ,CAACc,IAAI,EAAE;IACpB,IAAI,CAACd,QAAQ,CAACe,QAAQ,EAAE;EAC1B;EAEAJ,YAAYA,CAAA;IACV,IAAI,CAACN,aAAa,CAACW,YAAY,CAACC,IAAI,CAClC1E,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAAC0D,QAAQ,CAAC,CACzB,CAACkB,SAAS,CAAC,MAAK;MACf,IAAI,CAACN,YAAY,EAAE;IACrB,CAAC,CAAC;IAEF,CAAC,IAAI,CAACN,cAAc,EAAE,IAAI,CAACC,YAAY,CAAC,CAACY,OAAO,CAACC,OAAO,IAAG;MACzDA,OAAO,CAACJ,YAAY,CAACC,IAAI,CACvB3E,SAAS,CAAC,IAAI,CAAC0D,QAAQ,CAAC,CACzB,CAACkB,SAAS,CAAC,MAAK;QACf,IAAI,CAACN,YAAY,EAAE;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACT,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACD,UAAU,CAACmB,IAAI,GAAG,EAAE;IACzB,IAAI,CAACjB,aAAa,GAAG,CAAC;IACtB,IAAI,CAACD,SAAS,GAAG,KAAK;EACxB;EAEAmB,YAAYA,CAAA;IACV,IAAI,CAACV,YAAY,EAAE;EACrB;EAEAvB,iBAAiBA,CAACkC,OAAiB;IACjC,MAAMC,SAAS,GAAG,IAAI,CAAC1B,MAAM,CAAC2B,IAAI,CAAC/E,sBAAsB,EAAE;MACzDgF,KAAK,EAAE,OAAO;MACdL,IAAI,EAAEE,OAAO,GAAG;QAAE,GAAGA;MAAO,CAAE,GAAG;KAClC,CAAC;IAEFC,SAAS,CAACG,WAAW,EAAE,CAACT,SAAS,CAACU,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV,IAAI,CAAChB,YAAY,EAAE;;IAEvB,CAAC,CAAC;EACJ;EAEArB,mBAAmBA,CAACgC,OAAgB;IAClC,IAAI,CAACxB,QAAQ,CAAC0B,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;MAAEI,QAAQ,EAAE;IAAI,CAAE,CAAC;EAC3E;EAEApC,aAAaA,CAAC8B,OAAgB;IAC5B,IAAIO,OAAO,CAAC,oCAAoCP,OAAO,CAAC5D,IAAI,IAAI,CAAC,EAAE;MACjE,IAAI,CAACoC,QAAQ,CAAC0B,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE;QAAEI,QAAQ,EAAE;MAAI,CAAE,CAAC;MAClE,IAAI,CAACjB,YAAY,EAAE;;EAEvB;EAEApD,eAAeA,CAAC+D,OAAgB;IAC9B,OAAOA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;EAC7E;EAEAtD,cAAcA,CAAC6C,OAAgB;IAC7B,OAAOA,OAAO,CAAC7B,QAAQ,GAAG,SAAS,GAAG,SAAS;EACjD;EAEAd,aAAaA,CAAC2C,OAAgB;IAC5B,OAAOA,OAAO,CAAC7B,QAAQ,GAAG,QAAQ,GAAG,UAAU;EACjD;;;uBA9GWC,0BAA0B,EAAAhD,EAAA,CAAAsF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxF,EAAA,CAAAsF,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAA1F,EAAA,CAAAsF,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA1B5C,0BAA0B;MAAA6C,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAC1BxG,YAAY;yBACZC,OAAO;;;;;;;;;;;;;UAvIVO,EAHN,CAAAC,cAAA,aAAgC,eACpB,sBACS,qBACC;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACnDH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,8CAAuC;UAC5DF,EAD4D,CAAAG,YAAA,EAAoB,EAC9D;UAMZH,EAJN,CAAAC,cAAA,uBAAkB,aAEa,wBACU,iBACxB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAW,SAAA,gBAA+F;UAC/FX,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,qBAA2C;UACzCD,EAAA,CAAA0B,UAAA,KAAAwE,iDAAA,wBAAyE;UAI7ElG,EADE,CAAAG,YAAA,EAAa,EACE;UAGfH,EADF,CAAAC,cAAA,yBAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAC,cAAA,qBAAyC;UACvCD,EAAA,CAAA0B,UAAA,KAAAyE,iDAAA,wBAAmE;UAIvEnG,EADE,CAAAG,YAAA,EAAa,EACE;UAEjBH,EAAA,CAAAC,cAAA,iBAAwE;UAA9BD,EAAA,CAAAkC,UAAA,mBAAAkE,6DAAA;YAAA,OAASH,GAAA,CAAAvD,iBAAA,EAAmB;UAAA,EAAC;UACrE1C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,qBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACL;UAIJH,EADF,CAAAC,cAAA,cAA6B,gBACwB;UAEjDD,EAAA,CAAAqG,uBAAA,QAAqC;UAEnCrG,EADA,CAAA0B,UAAA,KAAA4E,yCAAA,iBAAsC,KAAAC,yCAAA,iBACC;;UAYzCvG,EAAA,CAAAqG,uBAAA,QAAsC;UAEpCrG,EADA,CAAA0B,UAAA,KAAA8E,yCAAA,iBAAsD,KAAAC,yCAAA,iBACf;;UAQzCzG,EAAA,CAAAqG,uBAAA,QAAmC;UAEjCrG,EADA,CAAA0B,UAAA,KAAAgF,yCAAA,iBAAsD,KAAAC,yCAAA,iBACf;;UAUzC3G,EAAA,CAAAqG,uBAAA,QAAoC;UAElCrG,EADA,CAAA0B,UAAA,KAAAkF,yCAAA,iBAAsC,KAAAC,yCAAA,iBACC;;UASzC7G,EAAA,CAAAqG,uBAAA,QAAqC;UAEnCrG,EADA,CAAA0B,UAAA,KAAAoF,yCAAA,iBAAsC,KAAAC,yCAAA,kBACC;;UAkBzC/G,EADA,CAAA0B,UAAA,KAAAsF,yCAAA,iBAAuD,KAAAC,yCAAA,iBACM;UAC/DjH,EAAA,CAAAG,YAAA,EAAQ;UAGRH,EAAA,CAAA0B,UAAA,KAAAwF,0CAAA,kBAA0D;UAK5DlH,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,yBAKuB;UADrBD,EAAA,CAAAkC,UAAA,kBAAAiF,mEAAA;YAAA,OAAQlB,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC;UAK/B3E,EAHM,CAAAG,YAAA,EAAgB,EACC,EACV,EACP;;;UAxHoBH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,gBAAA6F,GAAA,CAAAvC,aAAA,CAA6B;UAMjC1D,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,gBAAA6F,GAAA,CAAAtC,cAAA,CAA8B;UACP3D,EAAA,CAAAO,SAAA,EAAa;UAAbP,EAAA,CAAAI,UAAA,YAAA6F,GAAA,CAAApC,UAAA,CAAa;UAQpC7D,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAA6F,GAAA,CAAArC,YAAA,CAA4B;UACP5D,EAAA,CAAAO,SAAA,EAAW;UAAXP,EAAA,CAAAI,UAAA,YAAA6F,GAAA,CAAAnC,QAAA,CAAW;UAc7B9D,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,eAAA6F,GAAA,CAAA1C,UAAA,CAAyB;UAoEpBvD,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAI,UAAA,oBAAA6F,GAAA,CAAA3C,gBAAA,CAAiC;UACpBtD,EAAA,CAAAO,SAAA,EAA0B;UAA1BP,EAAA,CAAAI,UAAA,qBAAA6F,GAAA,CAAA3C,gBAAA,CAA0B;UAIvDtD,EAAA,CAAAO,SAAA,EAAkC;UAAlCP,EAAA,CAAAI,UAAA,SAAA6F,GAAA,CAAA1C,UAAA,CAAAmB,IAAA,CAAA0C,MAAA,OAAkC;UASxCpH,EAAA,CAAAO,SAAA,EAAwB;UAExBP,EAFA,CAAAI,UAAA,WAAA6F,GAAA,CAAAxC,aAAA,CAAwB,gBACT,oBAAAzD,EAAA,CAAAqH,eAAA,KAAAC,GAAA,EACoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}