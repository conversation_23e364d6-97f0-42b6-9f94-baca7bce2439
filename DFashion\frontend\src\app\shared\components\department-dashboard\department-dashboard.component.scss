.department-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  background: #f8f9fa;
  min-height: 100vh;

  // Department Header
  .department-header {
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .header-content {
      position: relative;
      z-index: 2;
      display: grid;
      grid-template-columns: auto 1fr auto;
      gap: 2rem;
      align-items: center;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
      }

      .department-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        @media (max-width: 768px) {
          width: 60px;
          height: 60px;
          font-size: 1.5rem;
          margin: 0 auto;
        }
      }

      .header-text {
        h1 {
          margin: 0 0 0.5rem 0;
          font-size: 2.5rem;
          font-weight: 700;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          @media (max-width: 768px) {
            font-size: 2rem;
          }
        }

        p {
          margin: 0;
          font-size: 1.1rem;
          opacity: 0.9;
        }
      }

      .header-stats {
        display: flex;
        gap: 2rem;

        @media (max-width: 768px) {
          justify-content: center;
          gap: 1rem;
        }

        .stat-item {
          text-align: center;

          .stat-value {
            display: block;
            font-size: 1.8rem;
            font-weight: 700;
            line-height: 1;

            @media (max-width: 768px) {
              font-size: 1.4rem;
            }
          }

          .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.25rem;
          }
        }
      }
    }
  }

  // Quick Actions
  .quick-actions {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    h3 {
      margin: 0 0 1rem 0;
      color: #262626;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      }

      .action-btn {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 1.5rem;
        border: none;
        border-radius: 12px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        &:active {
          transform: translateY(0);
        }

        i {
          font-size: 1.1rem;
        }

        span {
          font-size: 0.9rem;
        }
      }
    }
  }

  // Dashboard Widgets
  .dashboard-widgets {
    .widgets-grid {
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 1.5rem;
      grid-auto-rows: minmax(200px, auto);

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .widget-container {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        // Widget sizes
        &[data-size="small"] {
          grid-column: span 3;

          @media (max-width: 1200px) {
            grid-column: span 4;
          }

          @media (max-width: 768px) {
            grid-column: span 1;
          }
        }

        &[data-size="medium"] {
          grid-column: span 6;

          @media (max-width: 768px) {
            grid-column: span 1;
          }
        }

        &[data-size="large"] {
          grid-column: span 8;

          @media (max-width: 768px) {
            grid-column: span 1;
          }
        }

        &[data-size="full"] {
          grid-column: span 12;
        }
      }
    }
  }

  // Widget Types
  .metric-widget {
    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h4 {
        margin: 0;
        color: #8e8e8e;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      i {
        color: #c0c0c0;
        font-size: 1.1rem;
      }
    }

    .metric-content {
      .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #262626;
        line-height: 1;
        margin-bottom: 0.5rem;
      }

      .metric-change {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 0.25rem;

        &.up {
          color: #4CAF50;
        }

        &.down {
          color: #f44336;
        }

        &.stable {
          color: #ff9800;
        }
      }

      .metric-label {
        color: #8e8e8e;
        font-size: 0.8rem;
      }
    }
  }

  .chart-widget {
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h4 {
        margin: 0;
        color: #262626;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .chart-controls {
        .chart-btn {
          width: 32px;
          height: 32px;
          border: none;
          background: #f8f9fa;
          border-radius: 8px;
          cursor: pointer;
          color: #6c757d;
          transition: all 0.3s ease;

          &:hover {
            background: #e9ecef;
            color: #495057;
          }
        }
      }
    }

    .chart-content {
      .chart-placeholder {
        height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 12px;
        color: #6c757d;

        i {
          font-size: 2rem;
          margin-bottom: 0.5rem;
        }

        p {
          margin: 0;
          font-size: 0.9rem;
        }
      }
    }
  }

  .list-widget {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h4 {
        margin: 0;
        color: #262626;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .view-all {
        color: #667eea;
        text-decoration: none;
        font-size: 0.85rem;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .list-content {
      .list-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f8f9fa;
          margin: 0 -1rem;
          padding-left: 1rem;
          padding-right: 1rem;
          border-radius: 8px;
        }

        .item-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 0.9rem;
        }

        .item-content {
          flex: 1;

          .item-title {
            font-weight: 600;
            color: #262626;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
          }

          .item-subtitle {
            color: #8e8e8e;
            font-size: 0.8rem;
          }
        }

        .item-meta {
          text-align: right;

          .item-time {
            display: block;
            color: #8e8e8e;
            font-size: 0.75rem;
            margin-bottom: 0.25rem;
          }

          .item-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;

            &.urgent {
              background: #ffebee;
              color: #f44336;
            }

            &.normal {
              background: #fff3e0;
              color: #ff9800;
            }

            &.top {
              background: #fff8e1;
              color: #ffc107;
            }

            &.active {
              background: #e8f5e8;
              color: #4caf50;
            }
          }
        }
      }
    }
  }

  .progress-widget {
    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h4 {
        margin: 0;
        color: #262626;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .progress-percentage {
        font-size: 1.5rem;
        font-weight: 700;
        color: #262626;
      }
    }

    .progress-content {
      .progress-bar {
        height: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 1rem;

        .progress-fill {
          height: 100%;
          border-radius: 6px;
          transition: width 0.3s ease;
        }
      }

      .progress-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.85rem;
        color: #8e8e8e;
      }
    }
  }

  .activity-widget {
    .activity-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h4 {
        margin: 0;
        color: #262626;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .activity-filter select {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        background: white;
      }
    }

    .activity-content {
      .activity-timeline {
        .activity-item {
          display: grid;
          grid-template-columns: auto auto 1fr;
          gap: 1rem;
          align-items: center;
          padding: 0.75rem 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .activity-time {
            font-size: 0.75rem;
            color: #8e8e8e;
            white-space: nowrap;
          }

          .activity-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
          }

          .activity-details {
            .activity-title {
              font-weight: 600;
              color: #262626;
              font-size: 0.9rem;
              margin-bottom: 0.25rem;
            }

            .activity-description {
              color: #8e8e8e;
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .department-dashboard {
    background: #121212;

    .quick-actions,
    .widget-container {
      background: #1e1e1e;
      color: #ffffff;
    }

    .metric-widget .metric-content .metric-value,
    .chart-widget .chart-header h4,
    .list-widget .list-header h4,
    .progress-widget .progress-header h4,
    .activity-widget .activity-header h4 {
      color: #ffffff;
    }

    .list-widget .list-content .list-item {
      border-bottom-color: #333;

      &:hover {
        background: #2a2a2a;
      }

      .item-title {
        color: #ffffff;
      }
    }

    .progress-widget .progress-content .progress-bar {
      background: #333;
    }

    .activity-widget .activity-content .activity-timeline .activity-item {
      border-bottom-color: #333;

      .activity-title {
        color: #ffffff;
      }
    }
  }
}
