{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/error-handler.service\";\nimport * as i2 from \"@angular/common\";\nfunction ErrorDisplayComponent_div_0_div_1_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"pre\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, error_r3.details));\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_0_div_1_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const error_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleDetails(error_r3.id));\n    });\n    i0.ɵɵelement(2, \"i\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ErrorDisplayComponent_div_0_div_1_div_9_div_4_Template, 4, 3, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fa-chevron-down\", !ctx_r3.expandedErrors[error_r3.id])(\"fa-chevron-up\", ctx_r3.expandedErrors[error_r3.id]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.expandedErrors[error_r3.id] ? \"Hide\" : \"Show\", \" Details \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.expandedErrors[error_r3.id]);\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_0_div_1_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const error_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onRetry.emit(error_r3));\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"small\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Context: \", error_r3.context, \"\");\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"h4\", 9);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ErrorDisplayComponent_div_0_div_1_div_9_Template, 5, 6, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12);\n    i0.ɵɵtemplate(11, ErrorDisplayComponent_div_0_div_1_button_11_Template, 3, 0, \"button\", 13);\n    i0.ɵɵelementStart(12, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_0_div_1_Template_button_click_12_listener() {\n      const error_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.dismissError(error_r3.id));\n    });\n    i0.ɵɵelement(13, \"i\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(14, ErrorDisplayComponent_div_0_div_1_div_14_Template, 3, 1, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.getErrorClass(error_r3));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getErrorIcon(error_r3));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorTitle(error_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(error_r3.message);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showDetails && error_r3.details);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", error_r3.retryable);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", error_r3.context);\n  }\n}\nfunction ErrorDisplayComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, ErrorDisplayComponent_div_0_div_1_Template, 15, 9, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.globalErrors);\n  }\n}\nfunction ErrorDisplayComponent_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onRetry.emit(ctx_r3.inlineError));\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorDisplayComponent_div_1_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_1_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDismiss.emit());\n    });\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorDisplayComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"p\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 12);\n    i0.ɵɵtemplate(8, ErrorDisplayComponent_div_1_button_8_Template, 3, 0, \"button\", 13)(9, ErrorDisplayComponent_div_1_button_9_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.getErrorClass(ctx_r3.inlineError));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getErrorIcon(ctx_r3.inlineError));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.inlineError.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.inlineError.retryable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dismissible);\n  }\n}\nfunction ErrorDisplayComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_2_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.hideToast());\n    });\n    i0.ɵɵelement(7, \"i\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.getErrorClass(ctx_r3.toastError));\n    i0.ɵɵproperty(\"@slideIn\", undefined);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getErrorIcon(ctx_r3.toastError));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.toastError.message, \" \");\n  }\n}\nexport class ErrorDisplayComponent {\n  constructor(errorHandlerService) {\n    this.errorHandlerService = errorHandlerService;\n    this.showGlobalErrors = false;\n    this.dismissible = true;\n    this.showDetails = false;\n    this.showToast = false;\n    this.autoHideToast = true;\n    this.toastDuration = 5000;\n    this.onRetry = new EventEmitter();\n    this.onDismiss = new EventEmitter();\n    this.globalErrors = [];\n    this.expandedErrors = {};\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    if (this.showGlobalErrors) {\n      this.subscription.add(this.errorHandlerService.errorState$.subscribe(state => {\n        this.globalErrors = state.errors.filter(error => error.userFriendly);\n      }));\n    }\n    if (this.showToast && this.toastError && this.autoHideToast) {\n      this.scheduleToastHide();\n    }\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n  }\n  getErrorClass(error) {\n    return error.type;\n  }\n  getErrorIcon(error) {\n    const icons = {\n      network: 'fas fa-wifi',\n      validation: 'fas fa-exclamation-triangle',\n      authentication: 'fas fa-lock',\n      authorization: 'fas fa-ban',\n      server: 'fas fa-server',\n      client: 'fas fa-bug',\n      unknown: 'fas fa-question-circle'\n    };\n    return icons[error.type] || icons.unknown;\n  }\n  getErrorTitle(error) {\n    const titles = {\n      network: 'Connection Error',\n      validation: 'Validation Error',\n      authentication: 'Authentication Required',\n      authorization: 'Access Denied',\n      server: 'Server Error',\n      client: 'Application Error',\n      unknown: 'Unexpected Error'\n    };\n    return titles[error.type] || titles.unknown;\n  }\n  toggleDetails(errorId) {\n    this.expandedErrors[errorId] = !this.expandedErrors[errorId];\n  }\n  dismissError(errorId) {\n    this.errorHandlerService.clearError(errorId);\n  }\n  hideToast() {\n    this.showToast = false;\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n  }\n  scheduleToastHide() {\n    this.toastTimeout = window.setTimeout(() => {\n      this.hideToast();\n    }, this.toastDuration);\n  }\n  static {\n    this.ɵfac = function ErrorDisplayComponent_Factory(t) {\n      return new (t || ErrorDisplayComponent)(i0.ɵɵdirectiveInject(i1.ErrorHandlerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ErrorDisplayComponent,\n      selectors: [[\"app-error-display\"]],\n      inputs: {\n        showGlobalErrors: \"showGlobalErrors\",\n        inlineError: \"inlineError\",\n        dismissible: \"dismissible\",\n        showDetails: \"showDetails\",\n        showToast: \"showToast\",\n        toastError: \"toastError\",\n        autoHideToast: \"autoHideToast\",\n        toastDuration: \"toastDuration\"\n      },\n      outputs: {\n        onRetry: \"onRetry\",\n        onDismiss: \"onDismiss\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"error-banner\", 4, \"ngIf\"], [\"class\", \"inline-error\", 3, \"class\", 4, \"ngIf\"], [\"class\", \"error-toast\", 3, \"class\", 4, \"ngIf\"], [1, \"error-banner\"], [\"class\", \"error-item\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-item\"], [1, \"error-content\"], [1, \"error-icon\"], [1, \"error-details\"], [1, \"error-title\"], [1, \"error-message\"], [\"class\", \"error-extra\", 4, \"ngIf\"], [1, \"error-actions\"], [\"class\", \"btn-retry\", 3, \"click\", 4, \"ngIf\"], [1, \"btn-dismiss\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"class\", \"error-context\", 4, \"ngIf\"], [1, \"error-extra\"], [1, \"toggle-details\", 3, \"click\"], [1, \"fas\"], [\"class\", \"error-details-content\", 4, \"ngIf\"], [1, \"error-details-content\"], [1, \"btn-retry\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"error-context\"], [1, \"inline-error\"], [\"class\", \"btn-dismiss\", 3, \"click\", 4, \"ngIf\"], [1, \"error-toast\"], [1, \"toast-content\"], [1, \"toast-icon\"], [1, \"toast-message\"], [1, \"toast-close\", 3, \"click\"]],\n      template: function ErrorDisplayComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ErrorDisplayComponent_div_0_Template, 2, 1, \"div\", 0)(1, ErrorDisplayComponent_div_1_Template, 10, 7, \"div\", 1)(2, ErrorDisplayComponent_div_2_Template, 8, 6, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showGlobalErrors && ctx.globalErrors.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.inlineError);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showToast && ctx.toastError);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.JsonPipe],\n      styles: [\".error-banner[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  background: #dc3545;\\n  color: white;\\n  padding: 1rem;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\n.error-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.error-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.error-item.network[_ngcontent-%COMP%] {\\n  background: #fd7e14;\\n}\\n.error-item.validation[_ngcontent-%COMP%] {\\n  background: #ffc107;\\n  color: #212529;\\n}\\n.error-item.authentication[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n}\\n.error-item.authorization[_ngcontent-%COMP%] {\\n  background: #6f42c1;\\n}\\n\\n.error-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  margin-top: 0.25rem;\\n}\\n\\n.error-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.error-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin: 0 0 0.5rem 0;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  line-height: 1.4;\\n}\\n\\n.error-extra[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\n\\n.toggle-details[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: inherit;\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.toggle-details[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.error-details-content[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.error-details-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  white-space: pre-wrap;\\n}\\n\\n.error-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.btn-retry[_ngcontent-%COMP%], .btn-dismiss[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: inherit;\\n  padding: 0.5rem;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  transition: all 0.2s ease;\\n}\\n.btn-retry[_ngcontent-%COMP%]:hover, .btn-dismiss[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n.error-context[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n  opacity: 0.8;\\n  font-size: 0.75rem;\\n}\\n\\n.inline-error[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n  border-radius: 8px;\\n  padding: 1rem;\\n  margin: 1rem 0;\\n}\\n.inline-error.network[_ngcontent-%COMP%] {\\n  background: #ffeaa7;\\n  color: #856404;\\n  border-color: #ffd93d;\\n}\\n.inline-error.validation[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n  border-color: #ffeaa7;\\n}\\n.inline-error.authentication[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n  border-color: #f5c6cb;\\n}\\n\\n.error-toast[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 20px;\\n  right: 20px;\\n  z-index: 1100;\\n  background: #dc3545;\\n  color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  max-width: 400px;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n}\\n\\n.toast-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n}\\n\\n.toast-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n\\n.toast-message[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 0.9rem;\\n}\\n\\n.toast-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: inherit;\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: 4px;\\n}\\n.toast-close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .error-banner[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .error-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n  .error-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n  }\\n  .error-toast[_ngcontent-%COMP%] {\\n    left: 10px;\\n    right: 10px;\\n    max-width: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "Subscription", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "error_r3", "details", "ɵɵlistener", "ErrorDisplayComponent_div_0_div_1_div_9_Template_button_click_1_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "toggleDetails", "id", "ɵɵelement", "ɵɵtemplate", "ErrorDisplayComponent_div_0_div_1_div_9_div_4_Template", "ɵɵclassProp", "expandedErrors", "ɵɵtextInterpolate1", "ɵɵproperty", "ErrorDisplayComponent_div_0_div_1_button_11_Template_button_click_0_listener", "_r5", "onRetry", "emit", "context", "ErrorDisplayComponent_div_0_div_1_div_9_Template", "ErrorDisplayComponent_div_0_div_1_button_11_Template", "ErrorDisplayComponent_div_0_div_1_Template_button_click_12_listener", "_r1", "dismissError", "ErrorDisplayComponent_div_0_div_1_div_14_Template", "ɵɵclassMap", "getErrorClass", "getErrorIcon", "getErrorTitle", "message", "showDetails", "retryable", "ErrorDisplayComponent_div_0_div_1_Template", "globalErrors", "ErrorDisplayComponent_div_1_button_8_Template_button_click_0_listener", "_r6", "inlineError", "ErrorDisplayComponent_div_1_button_9_Template_button_click_0_listener", "_r7", "on<PERSON><PERSON><PERSON>", "ErrorDisplayComponent_div_1_button_8_Template", "ErrorDisplayComponent_div_1_button_9_Template", "dismissible", "ErrorDisplayComponent_div_2_Template_button_click_6_listener", "_r8", "hideToast", "toastError", "undefined", "ErrorDisplayComponent", "constructor", "errorHandlerService", "showGlobalErrors", "showToast", "autoHideToast", "toastDuration", "subscription", "ngOnInit", "add", "errorState$", "subscribe", "state", "errors", "filter", "error", "userFriendly", "scheduleToastHide", "ngOnDestroy", "unsubscribe", "toastTimeout", "clearTimeout", "type", "icons", "network", "validation", "authentication", "authorization", "server", "client", "unknown", "titles", "errorId", "clearError", "window", "setTimeout", "ɵɵdirectiveInject", "i1", "ErrorHandlerService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ErrorDisplayComponent_Template", "rf", "ctx", "ErrorDisplayComponent_div_0_Template", "ErrorDisplayComponent_div_1_Template", "ErrorDisplayComponent_div_2_Template", "length", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "JsonPipe", "styles"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\error-display\\error-display.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { ErrorHandlerService, AppError } from '../../../core/services/error-handler.service';\n\n@Component({\n  selector: 'app-error-display',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <!-- Global Error Banner -->\n    <div *ngIf=\"showGlobalErrors && globalErrors.length > 0\" class=\"error-banner\">\n      <div *ngFor=\"let error of globalErrors\" class=\"error-item\" [class]=\"getErrorClass(error)\">\n        <div class=\"error-content\">\n          <div class=\"error-icon\">\n            <i [class]=\"getErrorIcon(error)\"></i>\n          </div>\n          \n          <div class=\"error-details\">\n            <h4 class=\"error-title\">{{ getErrorTitle(error) }}</h4>\n            <p class=\"error-message\">{{ error.message }}</p>\n            \n            <div *ngIf=\"showDetails && error.details\" class=\"error-extra\">\n              <button class=\"toggle-details\" (click)=\"toggleDetails(error.id)\">\n                <i class=\"fas\" [class.fa-chevron-down]=\"!expandedErrors[error.id]\" \n                   [class.fa-chevron-up]=\"expandedErrors[error.id]\"></i>\n                {{ expandedErrors[error.id] ? 'Hide' : 'Show' }} Details\n              </button>\n              \n              <div *ngIf=\"expandedErrors[error.id]\" class=\"error-details-content\">\n                <pre>{{ error.details | json }}</pre>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"error-actions\">\n            <button *ngIf=\"error.retryable\" \n                    class=\"btn-retry\" \n                    (click)=\"onRetry.emit(error)\">\n              <i class=\"fas fa-redo\"></i>\n              Retry\n            </button>\n            \n            <button class=\"btn-dismiss\" (click)=\"dismissError(error.id)\">\n              <i class=\"fas fa-times\"></i>\n            </button>\n          </div>\n        </div>\n        \n        <div *ngIf=\"error.context\" class=\"error-context\">\n          <small>Context: {{ error.context }}</small>\n        </div>\n      </div>\n    </div>\n\n    <!-- Inline Error Display -->\n    <div *ngIf=\"inlineError\" class=\"inline-error\" [class]=\"getErrorClass(inlineError)\">\n      <div class=\"error-content\">\n        <div class=\"error-icon\">\n          <i [class]=\"getErrorIcon(inlineError)\"></i>\n        </div>\n        \n        <div class=\"error-details\">\n          <p class=\"error-message\">{{ inlineError.message }}</p>\n        </div>\n        \n        <div class=\"error-actions\">\n          <button *ngIf=\"inlineError.retryable\" \n                  class=\"btn-retry\" \n                  (click)=\"onRetry.emit(inlineError)\">\n            <i class=\"fas fa-redo\"></i>\n            Retry\n          </button>\n          \n          <button *ngIf=\"dismissible\" \n                  class=\"btn-dismiss\" \n                  (click)=\"onDismiss.emit()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Error Toast -->\n    <div *ngIf=\"showToast && toastError\" \n         class=\"error-toast\" \n         [class]=\"getErrorClass(toastError)\"\n         [@slideIn]>\n      <div class=\"toast-content\">\n        <div class=\"toast-icon\">\n          <i [class]=\"getErrorIcon(toastError)\"></i>\n        </div>\n        \n        <div class=\"toast-message\">\n          {{ toastError.message }}\n        </div>\n        \n        <button class=\"toast-close\" (click)=\"hideToast()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .error-banner {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 1000;\n      background: #dc3545;\n      color: white;\n      padding: 1rem;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n    }\n\n    .error-item {\n      margin-bottom: 1rem;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      &.network {\n        background: #fd7e14;\n      }\n      \n      &.validation {\n        background: #ffc107;\n        color: #212529;\n      }\n      \n      &.authentication {\n        background: #dc3545;\n      }\n      \n      &.authorization {\n        background: #6f42c1;\n      }\n    }\n\n    .error-content {\n      display: flex;\n      align-items: flex-start;\n      gap: 1rem;\n    }\n\n    .error-icon {\n      font-size: 1.25rem;\n      margin-top: 0.25rem;\n    }\n\n    .error-details {\n      flex: 1;\n    }\n\n    .error-title {\n      font-size: 1rem;\n      font-weight: 600;\n      margin: 0 0 0.5rem 0;\n    }\n\n    .error-message {\n      margin: 0 0 0.5rem 0;\n      line-height: 1.4;\n    }\n\n    .error-extra {\n      margin-top: 0.5rem;\n    }\n\n    .toggle-details {\n      background: none;\n      border: none;\n      color: inherit;\n      cursor: pointer;\n      font-size: 0.875rem;\n      display: flex;\n      align-items: center;\n      gap: 0.25rem;\n      \n      &:hover {\n        text-decoration: underline;\n      }\n    }\n\n    .error-details-content {\n      margin-top: 0.5rem;\n      padding: 0.5rem;\n      background: rgba(0, 0, 0, 0.1);\n      border-radius: 4px;\n      font-size: 0.75rem;\n      max-height: 200px;\n      overflow-y: auto;\n      \n      pre {\n        margin: 0;\n        white-space: pre-wrap;\n      }\n    }\n\n    .error-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n    }\n\n    .btn-retry, .btn-dismiss {\n      background: rgba(255, 255, 255, 0.2);\n      border: 1px solid rgba(255, 255, 255, 0.3);\n      color: inherit;\n      padding: 0.5rem;\n      border-radius: 4px;\n      cursor: pointer;\n      font-size: 0.875rem;\n      display: flex;\n      align-items: center;\n      gap: 0.25rem;\n      transition: all 0.2s ease;\n      \n      &:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    }\n\n    .error-context {\n      margin-top: 0.5rem;\n      opacity: 0.8;\n      font-size: 0.75rem;\n    }\n\n    .inline-error {\n      background: #f8d7da;\n      color: #721c24;\n      border: 1px solid #f5c6cb;\n      border-radius: 8px;\n      padding: 1rem;\n      margin: 1rem 0;\n      \n      &.network {\n        background: #ffeaa7;\n        color: #856404;\n        border-color: #ffd93d;\n      }\n      \n      &.validation {\n        background: #fff3cd;\n        color: #856404;\n        border-color: #ffeaa7;\n      }\n      \n      &.authentication {\n        background: #f8d7da;\n        color: #721c24;\n        border-color: #f5c6cb;\n      }\n    }\n\n    .error-toast {\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      z-index: 1100;\n      background: #dc3545;\n      color: white;\n      border-radius: 8px;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n      max-width: 400px;\n      animation: slideIn 0.3s ease;\n    }\n\n    .toast-content {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n      padding: 1rem;\n    }\n\n    .toast-icon {\n      font-size: 1.25rem;\n    }\n\n    .toast-message {\n      flex: 1;\n      font-size: 0.9rem;\n    }\n\n    .toast-close {\n      background: none;\n      border: none;\n      color: inherit;\n      cursor: pointer;\n      padding: 0.25rem;\n      border-radius: 4px;\n      \n      &:hover {\n        background: rgba(255, 255, 255, 0.2);\n      }\n    }\n\n    @keyframes slideIn {\n      from {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(0);\n        opacity: 1;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .error-banner {\n        padding: 0.75rem;\n      }\n      \n      .error-content {\n        flex-direction: column;\n        gap: 0.75rem;\n      }\n      \n      .error-actions {\n        flex-direction: row;\n      }\n      \n      .error-toast {\n        left: 10px;\n        right: 10px;\n        max-width: none;\n      }\n    }\n  `]\n})\nexport class ErrorDisplayComponent implements OnInit, OnDestroy {\n  @Input() showGlobalErrors: boolean = false;\n  @Input() inlineError?: AppError;\n  @Input() dismissible: boolean = true;\n  @Input() showDetails: boolean = false;\n  @Input() showToast: boolean = false;\n  @Input() toastError?: AppError;\n  @Input() autoHideToast: boolean = true;\n  @Input() toastDuration: number = 5000;\n\n  @Output() onRetry = new EventEmitter<AppError>();\n  @Output() onDismiss = new EventEmitter<void>();\n\n  globalErrors: AppError[] = [];\n  expandedErrors: { [key: string]: boolean } = {};\n  private subscription: Subscription = new Subscription();\n  private toastTimeout?: number;\n\n  constructor(private errorHandlerService: ErrorHandlerService) {}\n\n  ngOnInit() {\n    if (this.showGlobalErrors) {\n      this.subscription.add(\n        this.errorHandlerService.errorState$.subscribe(state => {\n          this.globalErrors = state.errors.filter(error => error.userFriendly);\n        })\n      );\n    }\n\n    if (this.showToast && this.toastError && this.autoHideToast) {\n      this.scheduleToastHide();\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n  }\n\n  getErrorClass(error: AppError): string {\n    return error.type;\n  }\n\n  getErrorIcon(error: AppError): string {\n    const icons = {\n      network: 'fas fa-wifi',\n      validation: 'fas fa-exclamation-triangle',\n      authentication: 'fas fa-lock',\n      authorization: 'fas fa-ban',\n      server: 'fas fa-server',\n      client: 'fas fa-bug',\n      unknown: 'fas fa-question-circle'\n    };\n    return icons[error.type] || icons.unknown;\n  }\n\n  getErrorTitle(error: AppError): string {\n    const titles = {\n      network: 'Connection Error',\n      validation: 'Validation Error',\n      authentication: 'Authentication Required',\n      authorization: 'Access Denied',\n      server: 'Server Error',\n      client: 'Application Error',\n      unknown: 'Unexpected Error'\n    };\n    return titles[error.type] || titles.unknown;\n  }\n\n  toggleDetails(errorId: string): void {\n    this.expandedErrors[errorId] = !this.expandedErrors[errorId];\n  }\n\n  dismissError(errorId: string): void {\n    this.errorHandlerService.clearError(errorId);\n  }\n\n  hideToast(): void {\n    this.showToast = false;\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n  }\n\n  private scheduleToastHide(): void {\n    this.toastTimeout = window.setTimeout(() => {\n      this.hideToast();\n    }, this.toastDuration);\n  }\n}\n"], "mappings": "AAAA,SAAmCA,YAAY,QAA2B,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,MAAM;;;;;;IA4BnBC,EADF,CAAAC,cAAA,cAAoE,UAC7D;IAAAD,EAAA,CAAAE,MAAA,GAA0B;;IACjCF,EADiC,CAAAG,YAAA,EAAM,EACjC;;;;IADCH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,QAAA,CAAAC,OAAA,EAA0B;;;;;;IAPjCR,EADF,CAAAC,cAAA,cAA8D,iBACK;IAAlCD,EAAA,CAAAS,UAAA,mBAAAC,yEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,QAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAE,aAAA,CAAAV,QAAA,CAAAW,EAAA,CAAuB;IAAA,EAAC;IAC9DlB,EAAA,CAAAmB,SAAA,YACwD;IACxDnB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAoB,UAAA,IAAAC,sDAAA,kBAAoE;IAGtErB,EAAA,CAAAG,YAAA,EAAM;;;;;IARaH,EAAA,CAAAI,SAAA,GAAmD;IAC/DJ,EADY,CAAAsB,WAAA,qBAAAP,MAAA,CAAAQ,cAAA,CAAAhB,QAAA,CAAAW,EAAA,EAAmD,kBAAAH,MAAA,CAAAQ,cAAA,CAAAhB,QAAA,CAAAW,EAAA,EACf;IACnDlB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAT,MAAA,CAAAQ,cAAA,CAAAhB,QAAA,CAAAW,EAAA,iCACF;IAEMlB,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAyB,UAAA,SAAAV,MAAA,CAAAQ,cAAA,CAAAhB,QAAA,CAAAW,EAAA,EAA8B;;;;;;IAOtClB,EAAA,CAAAC,cAAA,iBAEsC;IAA9BD,EAAA,CAAAS,UAAA,mBAAAiB,6EAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAgB,GAAA;MAAA,MAAApB,QAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAa,OAAA,CAAAC,IAAA,CAAAtB,QAAA,CAAmB;IAAA,EAAC;IACnCP,EAAA,CAAAmB,SAAA,YAA2B;IAC3BnB,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IASXH,EADF,CAAAC,cAAA,cAAiD,YACxC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACrCF,EADqC,CAAAG,YAAA,EAAQ,EACvC;;;;IADGH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAwB,kBAAA,cAAAjB,QAAA,CAAAuB,OAAA,KAA4B;;;;;;IApCnC9B,EAFJ,CAAAC,cAAA,aAA0F,aAC7D,aACD;IACtBD,EAAA,CAAAmB,SAAA,QAAqC;IACvCnB,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,aAA2B,YACD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EAAA,CAAAoB,UAAA,IAAAW,gDAAA,kBAA8D;IAWhE/B,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAoB,UAAA,KAAAY,oDAAA,qBAEsC;IAKtChC,EAAA,CAAAC,cAAA,kBAA6D;IAAjCD,EAAA,CAAAS,UAAA,mBAAAwB,oEAAA;MAAA,MAAA1B,QAAA,GAAAP,EAAA,CAAAW,aAAA,CAAAuB,GAAA,EAAApB,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAoB,YAAA,CAAA5B,QAAA,CAAAW,EAAA,CAAsB;IAAA,EAAC;IAC1DlB,EAAA,CAAAmB,SAAA,aAA4B;IAGlCnB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAENH,EAAA,CAAAoB,UAAA,KAAAgB,iDAAA,kBAAiD;IAGnDpC,EAAA,CAAAG,YAAA,EAAM;;;;;IAxCqDH,EAAA,CAAAqC,UAAA,CAAAtB,MAAA,CAAAuB,aAAA,CAAA/B,QAAA,EAA8B;IAGhFP,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAqC,UAAA,CAAAtB,MAAA,CAAAwB,YAAA,CAAAhC,QAAA,EAA6B;IAIRP,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAU,MAAA,CAAAyB,aAAA,CAAAjC,QAAA,EAA0B;IACzBP,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAE,QAAA,CAAAkC,OAAA,CAAmB;IAEtCzC,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAyB,UAAA,SAAAV,MAAA,CAAA2B,WAAA,IAAAnC,QAAA,CAAAC,OAAA,CAAkC;IAc/BR,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAyB,UAAA,SAAAlB,QAAA,CAAAoC,SAAA,CAAqB;IAa5B3C,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAyB,UAAA,SAAAlB,QAAA,CAAAuB,OAAA,CAAmB;;;;;IAtC7B9B,EAAA,CAAAC,cAAA,aAA8E;IAC5ED,EAAA,CAAAoB,UAAA,IAAAwB,0CAAA,kBAA0F;IAyC5F5C,EAAA,CAAAG,YAAA,EAAM;;;;IAzCmBH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAyB,UAAA,YAAAV,MAAA,CAAA8B,YAAA,CAAe;;;;;;IAuDlC7C,EAAA,CAAAC,cAAA,iBAE4C;IAApCD,EAAA,CAAAS,UAAA,mBAAAqC,sEAAA;MAAA9C,EAAA,CAAAW,aAAA,CAAAoC,GAAA;MAAA,MAAAhC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAa,OAAA,CAAAC,IAAA,CAAAd,MAAA,CAAAiC,WAAA,CAAyB;IAAA,EAAC;IACzChD,EAAA,CAAAmB,SAAA,YAA2B;IAC3BnB,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAETH,EAAA,CAAAC,cAAA,iBAEmC;IAA3BD,EAAA,CAAAS,UAAA,mBAAAwC,sEAAA;MAAAjD,EAAA,CAAAW,aAAA,CAAAuC,GAAA;MAAA,MAAAnC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAoC,SAAA,CAAAtB,IAAA,EAAgB;IAAA,EAAC;IAChC7B,EAAA,CAAAmB,SAAA,YAA4B;IAC9BnB,EAAA,CAAAG,YAAA,EAAS;;;;;IApBXH,EAFJ,CAAAC,cAAA,cAAmF,aACtD,aACD;IACtBD,EAAA,CAAAmB,SAAA,QAA2C;IAC7CnB,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,aAA2B,YACA;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACpDF,EADoD,CAAAG,YAAA,EAAI,EAClD;IAENH,EAAA,CAAAC,cAAA,cAA2B;IAQzBD,EAPA,CAAAoB,UAAA,IAAAgC,6CAAA,qBAE4C,IAAAC,6CAAA,qBAOT;IAKzCrD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAzBwCH,EAAA,CAAAqC,UAAA,CAAAtB,MAAA,CAAAuB,aAAA,CAAAvB,MAAA,CAAAiC,WAAA,EAAoC;IAGzEhD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAqC,UAAA,CAAAtB,MAAA,CAAAwB,YAAA,CAAAxB,MAAA,CAAAiC,WAAA,EAAmC;IAIbhD,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAU,MAAA,CAAAiC,WAAA,CAAAP,OAAA,CAAyB;IAIzCzC,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAyB,UAAA,SAAAV,MAAA,CAAAiC,WAAA,CAAAL,SAAA,CAA2B;IAO3B3C,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAyB,UAAA,SAAAV,MAAA,CAAAuC,WAAA,CAAiB;;;;;;IAe5BtD,EALJ,CAAAC,cAAA,cAGgB,cACa,cACD;IACtBD,EAAA,CAAAmB,SAAA,QAA0C;IAC5CnB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,iBAAkD;IAAtBD,EAAA,CAAAS,UAAA,mBAAA8C,6DAAA;MAAAvD,EAAA,CAAAW,aAAA,CAAA6C,GAAA;MAAA,MAAAzC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAA0C,SAAA,EAAW;IAAA,EAAC;IAC/CzD,EAAA,CAAAmB,SAAA,YAA4B;IAGlCnB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAfDH,EAAA,CAAAqC,UAAA,CAAAtB,MAAA,CAAAuB,aAAA,CAAAvB,MAAA,CAAA2C,UAAA,EAAmC;IACnC1D,EAAA,CAAAyB,UAAA,aAAAkC,SAAA,CAAU;IAGN3D,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAqC,UAAA,CAAAtB,MAAA,CAAAwB,YAAA,CAAAxB,MAAA,CAAA2C,UAAA,EAAkC;IAIrC1D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAT,MAAA,CAAA2C,UAAA,CAAAjB,OAAA,MACF;;;AA8OR,OAAM,MAAOmB,qBAAqB;EAkBhCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAjB9B,KAAAC,gBAAgB,GAAY,KAAK;IAEjC,KAAAT,WAAW,GAAY,IAAI;IAC3B,KAAAZ,WAAW,GAAY,KAAK;IAC5B,KAAAsB,SAAS,GAAY,KAAK;IAE1B,KAAAC,aAAa,GAAY,IAAI;IAC7B,KAAAC,aAAa,GAAW,IAAI;IAE3B,KAAAtC,OAAO,GAAG,IAAI/B,YAAY,EAAY;IACtC,KAAAsD,SAAS,GAAG,IAAItD,YAAY,EAAQ;IAE9C,KAAAgD,YAAY,GAAe,EAAE;IAC7B,KAAAtB,cAAc,GAA+B,EAAE;IACvC,KAAA4C,YAAY,GAAiB,IAAIpE,YAAY,EAAE;EAGQ;EAE/DqE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,gBAAgB,EAAE;MACzB,IAAI,CAACI,YAAY,CAACE,GAAG,CACnB,IAAI,CAACP,mBAAmB,CAACQ,WAAW,CAACC,SAAS,CAACC,KAAK,IAAG;QACrD,IAAI,CAAC3B,YAAY,GAAG2B,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,YAAY,CAAC;MACtE,CAAC,CAAC,CACH;;IAGH,IAAI,IAAI,CAACZ,SAAS,IAAI,IAAI,CAACN,UAAU,IAAI,IAAI,CAACO,aAAa,EAAE;MAC3D,IAAI,CAACY,iBAAiB,EAAE;;EAE5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,YAAY,CAACY,WAAW,EAAE;IAC/B,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC;;EAEnC;EAEA1C,aAAaA,CAACqC,KAAe;IAC3B,OAAOA,KAAK,CAACO,IAAI;EACnB;EAEA3C,YAAYA,CAACoC,KAAe;IAC1B,MAAMQ,KAAK,GAAG;MACZC,OAAO,EAAE,aAAa;MACtBC,UAAU,EAAE,6BAA6B;MACzCC,cAAc,EAAE,aAAa;MAC7BC,aAAa,EAAE,YAAY;MAC3BC,MAAM,EAAE,eAAe;MACvBC,MAAM,EAAE,YAAY;MACpBC,OAAO,EAAE;KACV;IACD,OAAOP,KAAK,CAACR,KAAK,CAACO,IAAI,CAAC,IAAIC,KAAK,CAACO,OAAO;EAC3C;EAEAlD,aAAaA,CAACmC,KAAe;IAC3B,MAAMgB,MAAM,GAAG;MACbP,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,kBAAkB;MAC9BC,cAAc,EAAE,yBAAyB;MACzCC,aAAa,EAAE,eAAe;MAC9BC,MAAM,EAAE,cAAc;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,OAAO,EAAE;KACV;IACD,OAAOC,MAAM,CAAChB,KAAK,CAACO,IAAI,CAAC,IAAIS,MAAM,CAACD,OAAO;EAC7C;EAEAzE,aAAaA,CAAC2E,OAAe;IAC3B,IAAI,CAACrE,cAAc,CAACqE,OAAO,CAAC,GAAG,CAAC,IAAI,CAACrE,cAAc,CAACqE,OAAO,CAAC;EAC9D;EAEAzD,YAAYA,CAACyD,OAAe;IAC1B,IAAI,CAAC9B,mBAAmB,CAAC+B,UAAU,CAACD,OAAO,CAAC;EAC9C;EAEAnC,SAASA,CAAA;IACP,IAAI,CAACO,SAAS,GAAG,KAAK;IACtB,IAAI,IAAI,CAACgB,YAAY,EAAE;MACrBC,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC;;EAEnC;EAEQH,iBAAiBA,CAAA;IACvB,IAAI,CAACG,YAAY,GAAGc,MAAM,CAACC,UAAU,CAAC,MAAK;MACzC,IAAI,CAACtC,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAACS,aAAa,CAAC;EACxB;;;uBA1FWN,qBAAqB,EAAA5D,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBtC,qBAAqB;MAAAuC,SAAA;MAAAC,MAAA;QAAArC,gBAAA;QAAAf,WAAA;QAAAM,WAAA;QAAAZ,WAAA;QAAAsB,SAAA;QAAAN,UAAA;QAAAO,aAAA;QAAAC,aAAA;MAAA;MAAAmC,OAAA;QAAAzE,OAAA;QAAAuB,SAAA;MAAA;MAAAmD,UAAA;MAAAC,QAAA,GAAAvG,EAAA,CAAAwG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzP9B9G,EAzEA,CAAAoB,UAAA,IAAA4F,oCAAA,iBAA8E,IAAAC,oCAAA,kBA6CK,IAAAC,oCAAA,iBA+BnE;;;UA5EVlH,EAAA,CAAAyB,UAAA,SAAAsF,GAAA,CAAAhD,gBAAA,IAAAgD,GAAA,CAAAlE,YAAA,CAAAsE,MAAA,KAAiD;UA6CjDnH,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAyB,UAAA,SAAAsF,GAAA,CAAA/D,WAAA,CAAiB;UA4BjBhD,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAAyB,UAAA,SAAAsF,GAAA,CAAA/C,SAAA,IAAA+C,GAAA,CAAArD,UAAA,CAA6B;;;qBA5E3B5D,YAAY,EAAAsH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}