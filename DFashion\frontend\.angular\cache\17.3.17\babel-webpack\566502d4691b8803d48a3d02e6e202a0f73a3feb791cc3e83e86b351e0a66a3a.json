{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nfunction FeaturedBrandsComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeaturedBrandsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_12_div_2_Template, 4, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Trending\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"i\", 4);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", brand_r2.rating, \"/5\");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Est. \", brand_r2.establishedYear, \"\");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template_span_click_0_listener($event) {\n      const category_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const brand_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.navigateToCategory(category_r5, brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, category_r5), \" \");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_17_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", brand_r2.categories.length - 3, \" more \");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template, 3, 3, \"span\", 46)(2, FeaturedBrandsComponent_div_13_div_1_div_17_span_2_Template, 2, 1, \"span\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", brand_r2.categories.slice(0, 3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.categories.length > 3);\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_div_click_0_listener() {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r2));\n    })(\"keydown.enter\", function FeaturedBrandsComponent_div_13_div_1_Template_div_keydown_enter_0_listener() {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r2));\n    })(\"keydown.space\", function FeaturedBrandsComponent_div_13_div_1_Template_div_keydown_space_0_listener() {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 22)(2, \"img\", 23);\n    i0.ɵɵlistener(\"error\", function FeaturedBrandsComponent_div_13_div_1_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, FeaturedBrandsComponent_div_13_div_1_div_3_Template, 2, 0, \"div\", 24)(4, FeaturedBrandsComponent_div_13_div_1_div_4_Template, 4, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 26)(6, \"h3\", 27);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 28);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 30);\n    i0.ɵɵelement(12, \"i\", 31);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, FeaturedBrandsComponent_div_13_div_1_div_15_Template, 4, 1, \"div\", 32)(16, FeaturedBrandsComponent_div_13_div_1_div_16_Template, 4, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_13_div_1_div_17_Template, 3, 2, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 34)(19, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_button_click_19_listener($event) {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.navigateToBrand(brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 36);\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Shop Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_button_click_23_listener($event) {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.viewBrandInfo(brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(24, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_button_click_25_listener($event) {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.toggleFavorite(brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(26, \"i\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + brand_r2.name + \" products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", brand_r2.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r2.name + \" logo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.isVerified);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.trending);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(brand_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r2.productCount, \" Products\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.rating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.establishedYear);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.categories && brand_r2.categories.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Shop \" + brand_r2.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + brand_r2.name + \" information\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add \" + brand_r2.name + \" to favorites\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"favorited\", ctx_r2.isFavorite(brand_r2._id));\n  }\n}\nfunction FeaturedBrandsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_13_div_1_Template, 27, 16, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.brands)(\"ngForTrackBy\", ctx_r2.trackByBrandId);\n  }\n}\nfunction FeaturedBrandsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"We're working on adding amazing brands for you!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadBrands());\n    });\n    i0.ɵɵelement(8, \"i\", 54);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeaturedBrandsComponent_div_15_a_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 70);\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2, \" Visit Website \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", ctx_r2.selectedBrand.website, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FeaturedBrandsComponent_div_15_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 66);\n    i0.ɵɵtext(4, \"Rating\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.selectedBrand.rating, \"/5\");\n  }\n}\nfunction FeaturedBrandsComponent_div_15_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 66);\n    i0.ɵɵtext(4, \"Established\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.establishedYear);\n  }\n}\nfunction FeaturedBrandsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeBrandModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeBrandModal());\n    });\n    i0.ɵɵelement(3, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 59);\n    i0.ɵɵelement(5, \"img\", 60);\n    i0.ɵɵelementStart(6, \"div\", 61)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, FeaturedBrandsComponent_div_15_a_11_Template, 3, 1, \"a\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 63)(13, \"div\", 64)(14, \"div\", 65);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 66);\n    i0.ɵɵtext(17, \"Products\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, FeaturedBrandsComponent_div_15_div_18_Template, 5, 1, \"div\", 67)(19, FeaturedBrandsComponent_div_15_div_19_Template, 5, 1, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 68)(21, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(ctx_r2.selectedBrand));\n    });\n    i0.ɵɵelement(22, \"i\", 36);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedBrand.logo, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.selectedBrand.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBrand.website);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.productCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBrand.rating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBrand.establishedYear);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Shop \", ctx_r2.selectedBrand.name, \" \");\n  }\n}\nexport let FeaturedBrandsComponent = /*#__PURE__*/(() => {\n  class FeaturedBrandsComponent {\n    constructor(shopDataService, router) {\n      this.shopDataService = shopDataService;\n      this.router = router;\n      this.maxBrands = 6;\n      this.showHeader = true;\n      this.brands = [];\n      this.isLoading = true;\n      this.selectedBrand = null;\n      this.favoriteBrands = new Set();\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.loadBrands();\n      this.loadFavorites();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadBrands() {\n      this.isLoading = true;\n      this.shopDataService.loadFeaturedBrands().pipe(takeUntil(this.destroy$)).subscribe({\n        next: brands => {\n          this.brands = brands.slice(0, this.maxBrands);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading featured brands:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n    navigateToBrand(brand) {\n      // Track brand click analytics\n      this.trackBrandClick(brand);\n      // Navigate to brand page\n      this.router.navigate(['/shop/brand', brand.slug]);\n    }\n    navigateToCategory(category, brand) {\n      // Navigate to category with brand filter\n      this.router.navigate(['/shop/category', category], {\n        queryParams: {\n          brand: brand.slug\n        }\n      });\n    }\n    viewAllBrands() {\n      this.router.navigate(['/shop/brands']);\n    }\n    viewBrandInfo(brand) {\n      this.selectedBrand = brand;\n    }\n    closeBrandModal() {\n      this.selectedBrand = null;\n    }\n    toggleFavorite(brand) {\n      if (this.favoriteBrands.has(brand._id)) {\n        this.favoriteBrands.delete(brand._id);\n      } else {\n        this.favoriteBrands.add(brand._id);\n      }\n      this.saveFavorites();\n    }\n    isFavorite(brandId) {\n      return this.favoriteBrands.has(brandId);\n    }\n    onImageError(event) {\n      // Set fallback image\n      event.target.src = 'https://via.placeholder.com/200x100/f0f0f0/666?text=Brand+Logo';\n    }\n    trackByBrandId(index, brand) {\n      return brand._id;\n    }\n    trackBrandClick(brand) {\n      // Analytics tracking\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'brand_click', {\n          brand_name: brand.name,\n          brand_id: brand._id,\n          event_category: 'engagement'\n        });\n      }\n    }\n    loadFavorites() {\n      const stored = localStorage.getItem('dfashion_favorite_brands');\n      if (stored) {\n        try {\n          const favorites = JSON.parse(stored);\n          this.favoriteBrands = new Set(favorites);\n        } catch (e) {\n          console.warn('Failed to load favorite brands');\n        }\n      }\n    }\n    saveFavorites() {\n      localStorage.setItem('dfashion_favorite_brands', JSON.stringify(Array.from(this.favoriteBrands)));\n    }\n    static {\n      this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n        return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FeaturedBrandsComponent,\n        selectors: [[\"app-featured-brands\"]],\n        inputs: {\n          maxBrands: \"maxBrands\",\n          showHeader: \"showHeader\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 16,\n        vars: 4,\n        consts: [[1, \"featured-brands-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-star\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"brands-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"brand-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"brand-card-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"brand-card-skeleton\"], [1, \"skeleton-logo\"], [1, \"skeleton-text\"], [1, \"skeleton-text\", \"short\"], [1, \"brands-grid\"], [\"class\", \"brand-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"tabindex\", \"0\", 1, \"brand-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"brand-logo-container\"], [\"loading\", \"lazy\", 1, \"brand-logo\", 3, \"error\", \"src\", \"alt\"], [\"class\", \"verification-badge\", 4, \"ngIf\"], [\"class\", \"trending-badge\", 4, \"ngIf\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-description\"], [1, \"brand-stats\"], [1, \"stat-item\"], [1, \"fas\", \"fa-box\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [\"class\", \"brand-categories\", 4, \"ngIf\"], [1, \"brand-actions\"], [1, \"action-btn\", \"primary\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"action-btn\", \"secondary\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"fas\", \"fa-heart\"], [1, \"verification-badge\"], [1, \"fas\", \"fa-check-circle\"], [1, \"trending-badge\"], [1, \"fas\", \"fa-fire\"], [1, \"fas\", \"fa-calendar\"], [1, \"brand-categories\"], [\"class\", \"category-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-categories\", 4, \"ngIf\"], [1, \"category-tag\", 3, \"click\"], [1, \"more-categories\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"fas\", \"fa-store-slash\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"brand-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"modal-header\"], [1, \"modal-logo\", 3, \"src\", \"alt\"], [1, \"modal-brand-info\"], [\"target\", \"_blank\", \"class\", \"website-link\", 3, \"href\", 4, \"ngIf\"], [1, \"modal-stats\"], [1, \"stat-card\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-card\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"target\", \"_blank\", 1, \"website-link\", 3, \"href\"], [1, \"fas\", \"fa-external-link-alt\"]],\n        template: function FeaturedBrandsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵtext(5, \" Featured Brands \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 5);\n            i0.ɵɵtext(7, \"Discover top brands loved by millions\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_Template_button_click_8_listener() {\n              return ctx.viewAllBrands();\n            });\n            i0.ɵɵelementStart(9, \"span\");\n            i0.ɵɵtext(10, \"View All\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"i\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(12, FeaturedBrandsComponent_div_12_Template, 3, 2, \"div\", 8)(13, FeaturedBrandsComponent_div_13_Template, 2, 2, \"div\", 9)(14, FeaturedBrandsComponent_div_14_Template, 10, 0, \"div\", 10)(15, FeaturedBrandsComponent_div_15_Template, 24, 9, \"div\", 11);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.brands.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.brands.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedBrand);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.TitleCasePipe],\n        styles: [\".featured-brands-section[_ngcontent-%COMP%]{padding:2rem 0;background:#fafafa}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem;padding:0 1rem}@media (max-width: 768px){.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;text-align:center}}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#262626;margin:0 0 .5rem;display:flex;align-items:center;gap:.5rem}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:gold;font-size:1.8rem}@media (max-width: 768px){.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.5rem;justify-content:center}}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{color:#8e8e8e;font-size:1rem;margin:0}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.5rem;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;border-radius:25px;font-weight:600;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 12px #667eea4d}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #667eea66}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:transform .3s ease}.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:translate(3px)}.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{padding:0 1rem}.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:1.5rem}.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1.5rem;box-shadow:0 2px 12px #0000000f}.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%]   .skeleton-logo[_ngcontent-%COMP%]{width:80px;height:80px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:12px;margin-bottom:1rem}.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%]{height:16px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:4px;margin-bottom:.5rem}.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%]   .skeleton-text.short[_ngcontent-%COMP%]{width:60%}.featured-brands-section[_ngcontent-%COMP%]   .brands-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:1.5rem;padding:0 1rem}@media (max-width: 768px){.featured-brands-section[_ngcontent-%COMP%]   .brands-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem}}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1.5rem;box-shadow:0 2px 12px #0000000f;cursor:pointer;transition:all .3s ease;position:relative;overflow:hidden}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 32px #00000026}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]:hover   .brand-actions[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]:focus{outline:3px solid #667eea;outline-offset:2px}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]{position:relative;margin-bottom:1rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]{width:80px;height:80px;object-fit:contain;border-radius:12px;background:#f8f9fa;padding:.5rem;transition:transform .3s ease}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%]{position:absolute;top:-5px;right:50px;background:#4caf50;color:#fff;border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;font-size:.7rem;box-shadow:0 2px 8px #4caf504d}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]{position:absolute;top:-5px;right:-5px;background:linear-gradient(135deg,#ff6b6b,#ff8e53);color:#fff;padding:.25rem .5rem;border-radius:12px;font-size:.7rem;font-weight:600;display:flex;align-items:center;gap:.25rem;box-shadow:0 2px 8px #ff6b6b4d}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#262626;margin:0 0 .5rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-description[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.9rem;margin:0 0 1rem;line-height:1.4}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.75rem;margin-bottom:1rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.8rem;color:#666}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#667eea;width:12px}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]{background:#f0f2ff;color:#667eea;padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:500;cursor:pointer;transition:all .3s ease}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%]   .more-categories[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.75rem;padding:.25rem .5rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]{position:absolute;bottom:1rem;left:1rem;right:1rem;display:flex;gap:.5rem;opacity:0;transform:translateY(10px);transition:all .3s ease}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{flex:1;padding:.75rem;border:none;border-radius:8px;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:.5rem;font-weight:600}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]{background:#667eea;color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover{background:#5a6fd8}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#666;flex:none;width:40px}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#e9ecef}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]   .fa-heart.favorited[_ngcontent-%COMP%]{color:#e91e63}.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{text-align:center;padding:3rem 1rem;color:#8e8e8e}.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem;color:#ddd}.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#666}.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1.5rem}.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#667eea;color:#fff;border:none;padding:.75rem 1.5rem;border-radius:8px;cursor:pointer;display:flex;align-items:center;gap:.5rem;margin:0 auto}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]{position:fixed;inset:0;background:#00000080;display:flex;align-items:center;justify-content:center;z-index:1000;padding:1rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:2rem;max-width:500px;width:100%;position:relative;max-height:90vh;overflow-y:auto}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{position:absolute;top:1rem;right:1rem;background:none;border:none;font-size:1.2rem;cursor:pointer;color:#666;width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover{background:#f0f0f0}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1.5rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-logo[_ngcontent-%COMP%]{width:80px;height:80px;object-fit:contain;border-radius:12px;background:#f8f9fa;padding:.5rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]{flex:1}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#262626}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1rem;color:#8e8e8e}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;display:flex;align-items:center;gap:.5rem;font-size:.9rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(100px,1fr));gap:1rem;margin-bottom:1.5rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{text-align:center;padding:1rem;background:#f8f9fa;border-radius:8px}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#262626}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.8rem;color:#8e8e8e;margin-top:.25rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;padding:1rem;background:#667eea;color:#fff;border:none;border-radius:8px;font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:center;gap:.5rem}.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:#5a6fd8}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.1)}}@media (prefers-color-scheme: dark){.featured-brands-section[_ngcontent-%COMP%]{background:#121212}.featured-brands-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{background:#1e1e1e;color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%]{color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]{background:#2a2a2a}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]{background:#2a2a2a;color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover{background:#333}.featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]{background:#1e1e1e;color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#fff}.featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-logo[_ngcontent-%COMP%], .featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{background:#2a2a2a}.featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{color:#fff}}\"]\n      });\n    }\n  }\n  return FeaturedBrandsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}