import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON>ef, ViewChild, HostListener, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';
import { AuthService } from 'src/app/core/services/auth.service';
import { CartService } from 'src/app/core/services/cart.service';
import { WishlistService } from 'src/app/core/services/wishlist.service';
import { SocialMediaService } from 'src/app/core/services/social-media.service';
import { RealtimeService } from 'src/app/core/services/realtime.service';
import { ButtonActionsService } from 'src/app/core/services/button-actions.service';

interface Story {
  _id: string;
  user: {
    _id: string;
    username: string;
    fullName: string;
    avatar: string;
  };
  media: {
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
    duration?: number;
  };
  mediaUrl: string; // For backward compatibility
  mediaType: 'image' | 'video'; // For backward compatibility
  caption?: string;
  createdAt: string;
  expiresAt: string;
  views: number;
  isActive: boolean;
  linkedContent?: {
    type: 'product' | 'category' | 'brand' | 'collection';
    productId?: string;
    categoryId?: string;
    brandId?: string;
    collectionId?: string;
  };
  products?: Array<{
    _id: string;
    product: {
      _id: string;
      name: string;
      price: number;
      images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;
    };
    position?: {
      x: number;
      y: number;
    };
    size?: string;
    color?: string;
  }>;
}

@Component({
  selector: 'app-view-add-stories',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './view-add-stories.component.html',
  styleUrls: ['./view-add-stories.component.scss']
})
export class ViewAddStoriesComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;
  @ViewChild('storiesTrack', { static: false }) storiesTrack!: ElementRef<HTMLDivElement>;
  @ViewChild('storyVideo', { static: false }) storyVideo!: ElementRef<HTMLVideoElement>;

  currentUser: any = null;
  stories: Story[] = [];
  isLoadingStories = true;

  // Story viewer state
  isOpen = false;
  currentStoryIndex = 0;
  currentUserIndex = 0;
  currentUserStories: any[] = [];
  showProductTags = false;
  isLiked = false;
  storyTimer: any;
  storyProgress = 0;
  storyDuration = 5000; // 5 seconds per story

  // Navigation state
  canScrollLeft = false;
  canScrollRight = false;
  showNavArrows = true;

  // Custom slider properties
  translateX = 0;
  itemWidth = 80; // Width of each story item including margin
  visibleItems = 6; // Number of visible items
  currentSlideIndex = 0;

  // Progress tracking
  private progressTimer: any;
  private progressUpdateTimer: any;
  private storyDuration = 15000; // 15 seconds default
  private progressStartTime = 0;
  currentProgress = 0; // Stable progress value for template binding

  // Touch handling
  private touchStartTime = 0;
  private longPressTimer: any;

  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private http: HttpClient,
    private authService: AuthService,
    private cartService: CartService,
    private wishlistService: WishlistService,
    private socialMediaService: SocialMediaService,
    private realtimeService: RealtimeService,
    private cdr: ChangeDetectorRef,
    private buttonActionsService: ButtonActionsService
  ) {
    console.log('🏗️ ViewAddStoriesComponent constructor called');
  }

  ngOnInit() {
    console.log('🚀 ViewAddStoriesComponent ngOnInit called');
    this.loadStories();
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      console.log('👤 Current user updated:', user);
    });

    // Check if we should show navigation arrows based on screen size
    this.updateNavArrowsVisibility();
  }

  ngAfterViewInit() {
    console.log('ngAfterViewInit called');
    setTimeout(() => {
      console.log('Initializing slider after view init');
      this.calculateSliderDimensions();
      this.updateScrollButtons();
    }, 500);

    // Also try immediate initialization
    setTimeout(() => {
      if (this.stories.length > 0) {
        console.log('Re-initializing slider with stories');
        this.calculateSliderDimensions();
        this.updateScrollButtons();
      }
    }, 1000);
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.clearStoryTimer();
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.updateNavArrowsVisibility();
    this.calculateSliderDimensions();
    this.updateScrollButtons();
  }

  private updateNavArrowsVisibility() {
    this.showNavArrows = window.innerWidth > 768;
  }

  private calculateSliderDimensions() {
    if (this.storiesSlider && this.storiesSlider.nativeElement) {
      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;
      const screenWidth = window.innerWidth;

      console.log('Calculating slider dimensions:', { containerWidth, screenWidth });

      // Responsive visible items
      if (screenWidth <= 600) {
        this.visibleItems = 3;
        this.itemWidth = containerWidth / 3;
      } else if (screenWidth <= 900) {
        this.visibleItems = 4;
        this.itemWidth = containerWidth / 4;
      } else {
        this.visibleItems = 6;
        this.itemWidth = containerWidth / 6;
      }

      console.log('Slider dimensions calculated:', {
        visibleItems: this.visibleItems,
        itemWidth: this.itemWidth
      });

      // Reset slider position if needed
      this.currentSlideIndex = 0;
      this.translateX = 0;
    } else {
      console.warn('Stories slider element not found, using default dimensions');
      // Fallback dimensions
      this.itemWidth = 100;
      this.visibleItems = 6;
    }
  }

  loadStories() {
    this.isLoadingStories = true;
    this.subscriptions.push(
      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({
        next: (response) => {
          console.log('Stories API response:', response);
          if (response.success && response.stories && response.stories.length > 0) {
            // Filter only active stories and map the data structure
            const allStories = response.stories
              .filter((story: any) => story.isActive)
              .map((story: any) => ({
                ...story,
                mediaUrl: story.media?.url || story.mediaUrl,
                mediaType: story.media?.type || story.mediaType
              }));

            // Group stories by user (Instagram style)
            this.stories = this.groupStoriesByUser(allStories);
            console.log('Loaded and grouped stories from API:', this.stories);
            console.log('Total user story groups:', this.stories.length);
          } else {
            console.log('No stories from API, loading fallback stories');
            this.loadFallbackStories();
          }
          this.isLoadingStories = false;
          setTimeout(() => {
            this.calculateSliderDimensions();
            this.updateScrollButtons();
          }, 100);
        },
        error: (error) => {
          console.error('Error loading stories:', error);
          console.log('Loading fallback stories due to error');
          this.loadFallbackStories();
          this.isLoadingStories = false;
          setTimeout(() => {
            this.calculateSliderDimensions();
            this.updateScrollButtons();
          }, 100);
        }
      })
    );
  }

  // Group stories by user like Instagram
  private groupStoriesByUser(allStories: any[]): any[] {
    const userStoriesMap = new Map();

    allStories.forEach(story => {
      const userId = story.user._id;
      if (!userStoriesMap.has(userId)) {
        userStoriesMap.set(userId, {
          user: story.user,
          stories: [],
          hasProducts: false,
          totalProducts: 0,
          latestStoryTime: story.createdAt
        });
      }

      const userGroup = userStoriesMap.get(userId);
      userGroup.stories.push(story);

      // Check if any story has products
      if (story.products && story.products.length > 0) {
        userGroup.hasProducts = true;
        userGroup.totalProducts += story.products.length;
      }

      // Keep track of latest story time for sorting
      if (new Date(story.createdAt) > new Date(userGroup.latestStoryTime)) {
        userGroup.latestStoryTime = story.createdAt;
      }
    });

    // Convert map to array and sort by latest story time
    return Array.from(userStoriesMap.values())
      .sort((a, b) => new Date(b.latestStoryTime).getTime() - new Date(a.latestStoryTime).getTime());
  }

  loadFallbackStories() {
    console.log('❌ No stories available from API');
    this.stories = [];
  }

  // Navigation methods
  scrollLeft() {
    console.log('Scroll left clicked, current index:', this.currentSlideIndex);
    if (this.currentSlideIndex > 0) {
      this.currentSlideIndex--;
      this.updateSliderPosition();
      console.log('Scrolled left to index:', this.currentSlideIndex);
    } else {
      console.log('Cannot scroll left, already at start');
    }
  }

  scrollRight() {
    const totalItems = this.stories.length + 1; // +1 for "Add Story" button
    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);

    console.log('Scroll right clicked:', {
      currentIndex: this.currentSlideIndex,
      totalItems,
      maxSlideIndex,
      visibleItems: this.visibleItems
    });

    if (this.currentSlideIndex < maxSlideIndex) {
      this.currentSlideIndex++;
      this.updateSliderPosition();
      console.log('Scrolled right to index:', this.currentSlideIndex);
    } else {
      console.log('Cannot scroll right, already at end');
    }
  }

  private updateSliderPosition() {
    const newTranslateX = -this.currentSlideIndex * this.itemWidth;
    console.log('Updating slider position:', {
      currentIndex: this.currentSlideIndex,
      itemWidth: this.itemWidth,
      newTranslateX
    });

    this.translateX = newTranslateX;
    this.updateScrollButtons();
  }

  updateScrollButtons() {
    const totalItems = this.stories.length + 1; // +1 for "Add Story" button
    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);

    this.canScrollLeft = this.currentSlideIndex > 0;
    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;

    console.log('Updated scroll buttons:', {
      canScrollLeft: this.canScrollLeft,
      canScrollRight: this.canScrollRight,
      totalItems,
      maxSlideIndex,
      currentIndex: this.currentSlideIndex
    });
  }

  // Story viewer methods
  openUserStories(userGroup: any, userIndex: number) {
    this.currentUserIndex = userIndex;
    this.currentStoryIndex = 0; // Start with first story of this user
    this.currentUserStories = userGroup.stories;
    this.isOpen = true;
    this.showProductTags = false;
    this.startStoryTimer();
    document.body.style.overflow = 'hidden';
    console.log('Opening stories for user:', userGroup.user.username, 'Stories count:', userGroup.stories.length);
  }

  openStory(_story: Story, index: number) {
    // Legacy method - keeping for compatibility
    this.currentStoryIndex = index;
    this.isOpen = true;
    this.showProductTags = false;
    this.startStoryTimer();
    document.body.style.overflow = 'hidden';
  }

  closeStories() {
    this.isOpen = false;
    this.clearStoryTimer();
    this.pauseAllVideos();
    document.body.style.overflow = 'auto';
  }

  nextStory() {
    // First check if there are more stories for current user
    if (this.currentStoryIndex < this.currentUserStories.length - 1) {
      this.currentStoryIndex++;
      this.showProductTags = false;
      this.startStoryTimer();
    } else {
      // Move to next user's stories
      if (this.currentUserIndex < this.stories.length - 1) {
        this.currentUserIndex++;
        this.currentStoryIndex = 0;
        this.currentUserStories = this.stories[this.currentUserIndex].stories;
        this.showProductTags = false;
        this.startStoryTimer();
      } else {
        this.closeStories();
      }
    }
  }

  previousStory() {
    // First check if there are previous stories for current user
    if (this.currentStoryIndex > 0) {
      this.currentStoryIndex--;
      this.showProductTags = false;
      this.startStoryTimer();
    } else {
      // Move to previous user's stories (last story)
      if (this.currentUserIndex > 0) {
        this.currentUserIndex--;
        this.currentUserStories = this.stories[this.currentUserIndex].stories;
        this.currentStoryIndex = this.currentUserStories.length - 1;
        this.showProductTags = false;
        this.startStoryTimer();
      } else {
        this.closeStories();
      }
    }
  }

  getCurrentStory(): Story {
    if (this.currentUserStories && this.currentUserStories.length > 0) {
      return this.currentUserStories[this.currentStoryIndex] || this.currentUserStories[0];
    }
    // Fallback for legacy usage
    return this.stories[this.currentStoryIndex] || this.stories[0];
  }

  getCurrentUser(): any {
    if (this.stories && this.stories.length > 0) {
      return this.stories[this.currentUserIndex]?.user;
    }
    return null;
  }

  hasProducts(): boolean {
    const story = this.getCurrentStory();
    return !!(story && story.products && story.products.length > 0);
  }

  getProductCount(): number {
    const story = this.getCurrentStory();
    return story?.products?.length || 0;
  }

  // Progress tracking
  getProgressWidth(index: number): number {
    if (index < this.currentStoryIndex) return 100;
    if (index > this.currentStoryIndex) return 0;

    // Return the stable progress value for the current story
    return this.currentProgress;
  }

  private startStoryTimer() {
    this.clearStoryTimer();
    this.progressStartTime = Date.now();
    this.currentProgress = 0;

    // Update progress every 100ms for smooth animation
    this.progressUpdateTimer = setInterval(() => {
      if (this.progressStartTime) {
        const elapsed = Date.now() - this.progressStartTime;
        this.currentProgress = Math.min((elapsed / this.storyDuration) * 100, 100);
        this.cdr.detectChanges(); // Trigger change detection manually
      }
    }, 100);

    this.progressTimer = setTimeout(() => {
      this.nextStory();
    }, this.storyDuration);
  }

  private clearStoryTimer() {
    if (this.progressTimer) {
      clearTimeout(this.progressTimer);
      this.progressTimer = null;
    }
    if (this.progressUpdateTimer) {
      clearInterval(this.progressUpdateTimer);
      this.progressUpdateTimer = null;
    }
    this.progressStartTime = 0;
    this.currentProgress = 0;
  }

  // Story interaction methods
  onStoryClick(event: MouseEvent) {
    const clickX = event.clientX;
    const windowWidth = window.innerWidth;

    if (clickX < windowWidth / 3) {
      this.previousStory();
    } else if (clickX > (windowWidth * 2) / 3) {
      this.nextStory();
    }
  }

  // Touch handling
  onTouchStart(_event: TouchEvent) {
    this.touchStartTime = Date.now();
    this.longPressTimer = setTimeout(() => {
      this.clearStoryTimer(); // Pause story progress on long press
    }, 500);
  }

  onTouchMove(_event: TouchEvent) {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  onTouchEnd(event: TouchEvent) {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    const touchDuration = Date.now() - this.touchStartTime;
    if (touchDuration < 500) {
      // Short tap - treat as click
      const touch = event.changedTouches[0];
      this.onStoryClick({ clientX: touch.clientX } as MouseEvent);
    } else {
      // Long press ended - resume story progress
      this.startStoryTimer();
    }
  }

  // Product interaction methods
  toggleProductTags() {
    this.showProductTags = !this.showProductTags;
  }

  openProductDetails(product: any) {
    this.router.navigate(['/product', product._id]);
  }

  // Add product to cart with real-time functionality
  addToCart(product: any) {
    console.log('Adding product to cart:', product);

    this.buttonActionsService.addToCart({
      productId: product._id,
      size: product.size,
      color: product.color,
      quantity: 1,
      addedFrom: 'story'
    }).subscribe({
      next: (result) => {
        if (result.success) {
          console.log('Product added to cart successfully:', result);
          // Show success message or toast
        } else {
          console.error('Failed to add product to cart:', result.message);
        }
      },
      error: (error) => {
        console.error('Error adding product to cart:', error);
      }
    });
  }

  // Add product to wishlist with real-time functionality
  addToWishlist(product: any) {
    console.log('Adding product to wishlist:', product);

    this.buttonActionsService.addToWishlist({
      productId: product._id,
      size: product.size,
      color: product.color,
      addedFrom: 'story'
    }).subscribe({
      next: (result) => {
        if (result.success) {
          console.log('Product added to wishlist successfully:', result);
          // Show success message or toast
        } else {
          console.error('Failed to add product to wishlist:', result.message);
        }
      },
      error: (error) => {
        console.error('Error adding product to wishlist:', error);
      }
    });
  }

  // Buy now functionality
  buyNow(product: any) {
    console.log('Buy now clicked for product:', product);

    this.buttonActionsService.buyNow({
      productId: product._id,
      size: product.size,
      color: product.color,
      quantity: 1,
      addedFrom: 'story'
    }).subscribe({
      next: (result) => {
        if (result.success) {
          console.log('Buy now successful:', result);
          // Navigation to checkout will be handled by the service
        } else {
          console.error('Failed to buy now:', result.message);
        }
      },
      error: (error) => {
        console.error('Error in buy now:', error);
      }
    });
  }

  // Story actions with real-time functionality
  toggleLike() {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return;
    }

    const currentStory = this.getCurrentStory();
    this.isLiked = !this.isLiked;

    // Call API to like/unlike story
    const endpoint = this.isLiked ? 'like' : 'unlike';
    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({
      next: (response) => {
        console.log(`Story ${endpoint}d successfully:`, response);
        // Emit real-time event (using socket directly)
        if (this.realtimeService.isConnected()) {
          // For now, we'll track this locally until we add story-specific emit methods
          console.log('Story liked event:', {
            storyId: currentStory._id,
            userId: this.authService.currentUserValue?._id,
            liked: this.isLiked
          });
        }
      },
      error: (error) => {
        console.error(`Error ${endpoint}ing story:`, error);
        // Revert the like state on error
        this.isLiked = !this.isLiked;
      }
    });
  }

  shareStory() {
    const currentStory = this.getCurrentStory();

    if (navigator.share) {
      // Use native sharing if available
      navigator.share({
        title: `Story by ${currentStory.user.username}`,
        text: currentStory.caption,
        url: `${environment.frontendUrl}/story/${currentStory._id}`
      }).then(() => {
        console.log('Story shared successfully');
        // Track share event
        this.trackStoryShare(currentStory._id);
      }).catch((error) => {
        console.error('Error sharing story:', error);
      });
    } else {
      // Fallback: copy link to clipboard
      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;
      navigator.clipboard.writeText(shareUrl).then(() => {
        console.log('Story link copied to clipboard');
        this.trackStoryShare(currentStory._id);
        // Show toast notification
        this.showToast('Story link copied to clipboard!');
      }).catch((error) => {
        console.error('Error copying to clipboard:', error);
      });
    }
  }

  saveStory() {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return;
    }

    const currentStory = this.getCurrentStory();

    // Call API to save story
    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({
      next: (response) => {
        console.log('Story saved successfully:', response);
        this.showToast('Story saved to your collection!');
        // Emit real-time event (using socket directly)
        if (this.realtimeService.isConnected()) {
          // For now, we'll track this locally until we add story-specific emit methods
          console.log('Story saved event:', {
            storyId: currentStory._id,
            userId: this.authService.currentUserValue?._id
          });
        }
      },
      error: (error) => {
        console.error('Error saving story:', error);
        this.showToast('Error saving story. Please try again.');
      }
    });
  }

  private trackStoryShare(storyId: string) {
    if (this.authService.isAuthenticated) {
      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({
        next: (response) => {
          console.log('Story share tracked:', response);
        },
        error: (error) => {
          console.error('Error tracking story share:', error);
        }
      });
    }
  }

  // Handle middle area click for product/category navigation
  handleMiddleAreaClick(event: Event) {
    event.stopPropagation();
    const currentStory = this.getCurrentStory();

    if (!currentStory?.linkedContent) {
      return;
    }

    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);

    switch (currentStory.linkedContent.type) {
      case 'product':
        if (currentStory.linkedContent.productId) {
          this.router.navigate(['/product', currentStory.linkedContent.productId]);
        } else if (currentStory.products && currentStory.products.length > 0) {
          // Fallback to first product if no specific productId
          this.router.navigate(['/product', currentStory.products[0].product._id]);
        }
        break;

      case 'category':
        if (currentStory.linkedContent.categoryId) {
          this.router.navigate(['/shop'], {
            queryParams: { category: currentStory.linkedContent.categoryId }
          });
        }
        break;

      case 'brand':
        if (currentStory.linkedContent.brandId) {
          this.router.navigate(['/shop'], {
            queryParams: { brand: currentStory.linkedContent.brandId }
          });
        }
        break;

      case 'collection':
        if (currentStory.linkedContent.collectionId) {
          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);
        }
        break;

      default:
        console.warn('Unknown linked content type:', currentStory.linkedContent.type);
    }

    // Track click event
    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);
  }

  // Get text for linked content indicator
  getLinkedContentText(): string {
    const currentStory = this.getCurrentStory();

    if (!currentStory?.linkedContent) {
      return '';
    }

    switch (currentStory.linkedContent.type) {
      case 'product':
        return 'View Product';
      case 'category':
        return 'Browse Category';
      case 'brand':
        return 'View Brand';
      case 'collection':
        return 'View Collection';
      default:
        return 'View Details';
    }
  }

  // Track linked content click for analytics
  private trackLinkedContentClick(storyId: string, linkedContent: any) {
    if (this.authService.isAuthenticated) {
      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {
        contentType: linkedContent.type,
        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId
      }).subscribe({
        next: (response) => {
          console.log('Linked content click tracked:', response);
        },
        error: (error) => {
          console.error('Error tracking linked content click:', error);
        }
      });
    }
  }

  private showToast(message: string) {
    // Simple toast implementation - you can replace with your preferred toast library
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      z-index: 10000;
      font-size: 14px;
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
      document.body.removeChild(toast);
    }, 3000);
  }

  // Add story functionality
  onAdd() {
    this.router.navigate(['/stories/create']);
  }

  // Utility methods
  getTimeAgo(dateString: string | Date): string {
    if (!dateString) return 'Unknown';

    const now = new Date();
    let date: Date;

    if (typeof dateString === 'string') {
      date = new Date(dateString);
    } else {
      date = dateString;
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Unknown';
    }

    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d`;
  }

  private pauseAllVideos() {
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      if (video.pause) {
        video.pause();
      }
    });
  }

  @HostListener('document:keydown', ['$event'])
  handleKeydown(event: KeyboardEvent) {
    if (!this.isOpen) return;

    switch (event.key) {
      case 'ArrowLeft':
        this.previousStory();
        break;
      case 'ArrowRight':
        this.nextStory();
        break;
      case 'Escape':
        this.closeStories();
        break;
    }
  }
}