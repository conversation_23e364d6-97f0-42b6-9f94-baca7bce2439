{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AnalyticsService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:5000/api';\n    this.analyticsData$ = new BehaviorSubject(null);\n  }\n  // Core Analytics\n  getAnalyticsOverview() {\n    return this.http.get(`${this.apiUrl}/analytics/overview`).pipe(map(response => response.success ? response.data : this.getFallbackAnalytics()), catchError(error => {\n      console.error('Error fetching analytics:', error);\n      return [this.getFallbackAnalytics()];\n    }));\n  }\n  // Social Media Analytics\n  getSocialMediaMetrics() {\n    return this.http.get(`${this.apiUrl}/analytics/social-media`).pipe(map(response => response.success ? response.data : this.getFallbackSocialMetrics()), catchError(error => {\n      console.error('Error fetching social media metrics:', error);\n      return [this.getFallbackSocialMetrics()];\n    }));\n  }\n  // Search Engine Analytics\n  getSearchEngineData() {\n    return this.http.get(`${this.apiUrl}/analytics/search-engine`).pipe(map(response => response.success ? response.data : this.getFallbackSearchData()), catchError(error => {\n      console.error('Error fetching search engine data:', error);\n      return [this.getFallbackSearchData()];\n    }));\n  }\n  // Competitor Analysis\n  getCompetitorAnalysis() {\n    return this.http.get(`${this.apiUrl}/analytics/competitors`).pipe(map(response => response.success ? response.data : this.getFallbackCompetitorData()), catchError(error => {\n      console.error('Error fetching competitor analysis:', error);\n      return [this.getFallbackCompetitorData()];\n    }));\n  }\n  // Real-time Data Scraping\n  scrapeInstagramData(username) {\n    return this.http.post(`${this.apiUrl}/analytics/scrape/instagram`, {\n      username\n    }).pipe(map(response => response.success ? response.data : null), catchError(error => {\n      console.error('Error scraping Instagram data:', error);\n      return [null];\n    }));\n  }\n  scrapeGoogleTrends(keyword) {\n    return this.http.post(`${this.apiUrl}/analytics/scrape/google-trends`, {\n      keyword\n    }).pipe(map(response => response.success ? response.data : null), catchError(error => {\n      console.error('Error scraping Google Trends:', error);\n      return [null];\n    }));\n  }\n  // User Behavior Analytics\n  trackUserBehavior(event, data) {\n    return this.http.post(`${this.apiUrl}/analytics/track`, {\n      event,\n      data,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking user behavior:', error);\n      return [];\n    }));\n  }\n  // Export Analytics Data\n  exportAnalyticsData(format, dateRange) {\n    return this.http.post(`${this.apiUrl}/analytics/export`, {\n      format,\n      dateRange\n    }, {\n      responseType: 'blob'\n    }).pipe(catchError(error => {\n      console.error('Error exporting analytics data:', error);\n      throw error;\n    }));\n  }\n  // Fallback Data Methods\n  getFallbackAnalytics() {\n    return {\n      totalUsers: 15847,\n      activeUsers: 3421,\n      totalProducts: 1250,\n      totalOrders: 2847,\n      totalRevenue: 1247500,\n      conversionRate: 3.2,\n      averageOrderValue: 438,\n      topCategories: [{\n        name: 'Women',\n        count: 1247,\n        revenue: 547200\n      }, {\n        name: 'Men',\n        count: 892,\n        revenue: 389400\n      }, {\n        name: 'Accessories',\n        count: 634,\n        revenue: 278100\n      }, {\n        name: 'Footwear',\n        count: 521,\n        revenue: 228700\n      }, {\n        name: 'Beauty',\n        count: 387,\n        revenue: 169800\n      }],\n      userGrowth: this.generateGrowthData(),\n      searchTrends: [{\n        query: 'summer dress',\n        count: 1247,\n        trend: 'up'\n      }, {\n        query: 'casual wear',\n        count: 892,\n        trend: 'stable'\n      }, {\n        query: 'ethnic wear',\n        count: 634,\n        trend: 'up'\n      }, {\n        query: 'formal shoes',\n        count: 521,\n        trend: 'down'\n      }, {\n        query: 'handbags',\n        count: 387,\n        trend: 'up'\n      }],\n      engagementMetrics: {\n        pageViews: 45672,\n        sessionDuration: 4.2,\n        bounceRate: 32.5,\n        clickThroughRate: 2.8\n      }\n    };\n  }\n  getFallbackSocialMetrics() {\n    return [{\n      platform: 'Instagram',\n      followers: 125000,\n      engagement: 8.5,\n      reach: 89000,\n      impressions: 234000,\n      mentions: 1247,\n      sentiment: 'positive',\n      topPosts: [{\n        id: '1',\n        content: 'Summer collection launch',\n        likes: 2847,\n        shares: 234,\n        comments: 156\n      }, {\n        id: '2',\n        content: 'Behind the scenes',\n        likes: 1923,\n        shares: 189,\n        comments: 98\n      }, {\n        id: '3',\n        content: 'Customer spotlight',\n        likes: 1654,\n        shares: 145,\n        comments: 87\n      }]\n    }, {\n      platform: 'Facebook',\n      followers: 89000,\n      engagement: 6.2,\n      reach: 67000,\n      impressions: 178000,\n      mentions: 892,\n      sentiment: 'positive',\n      topPosts: [{\n        id: '1',\n        content: 'New arrivals showcase',\n        likes: 1847,\n        shares: 167,\n        comments: 123\n      }, {\n        id: '2',\n        content: 'Style tips and tricks',\n        likes: 1234,\n        shares: 134,\n        comments: 89\n      }, {\n        id: '3',\n        content: 'Flash sale announcement',\n        likes: 987,\n        shares: 98,\n        comments: 67\n      }]\n    }, {\n      platform: 'Twitter',\n      followers: 45000,\n      engagement: 4.8,\n      reach: 34000,\n      impressions: 89000,\n      mentions: 567,\n      sentiment: 'neutral',\n      topPosts: [{\n        id: '1',\n        content: 'Fashion week highlights',\n        likes: 892,\n        shares: 234,\n        comments: 67\n      }, {\n        id: '2',\n        content: 'Sustainable fashion tips',\n        likes: 634,\n        shares: 156,\n        comments: 45\n      }, {\n        id: '3',\n        content: 'Trend predictions',\n        likes: 521,\n        shares: 123,\n        comments: 34\n      }]\n    }];\n  }\n  getFallbackSearchData() {\n    return {\n      keywords: [{\n        keyword: 'online fashion store',\n        position: 3,\n        searchVolume: 12000,\n        difficulty: 65,\n        trend: 'up'\n      }, {\n        keyword: 'women clothing',\n        position: 7,\n        searchVolume: 8900,\n        difficulty: 72,\n        trend: 'stable'\n      }, {\n        keyword: 'ethnic wear online',\n        position: 5,\n        searchVolume: 5600,\n        difficulty: 58,\n        trend: 'up'\n      }, {\n        keyword: 'designer clothes',\n        position: 12,\n        searchVolume: 4300,\n        difficulty: 78,\n        trend: 'down'\n      }, {\n        keyword: 'fashion accessories',\n        position: 8,\n        searchVolume: 3200,\n        difficulty: 62,\n        trend: 'stable'\n      }],\n      organicTraffic: 23456,\n      clickThroughRate: 3.2,\n      averagePosition: 7.2,\n      impressions: 156789,\n      clicks: 5023\n    };\n  }\n  getFallbackCompetitorData() {\n    return [{\n      competitor: 'Myntra',\n      marketShare: 28.5,\n      priceComparison: 105,\n      trafficEstimate: 2500000,\n      topKeywords: ['online fashion', 'ethnic wear', 'designer clothes'],\n      socialFollowing: 1200000,\n      engagementRate: 6.8\n    }, {\n      competitor: 'Ajio',\n      marketShare: 18.2,\n      priceComparison: 98,\n      trafficEstimate: 1800000,\n      topKeywords: ['trendy fashion', 'casual wear', 'footwear'],\n      socialFollowing: 890000,\n      engagementRate: 5.4\n    }, {\n      competitor: 'Nykaa Fashion',\n      marketShare: 12.7,\n      priceComparison: 112,\n      trafficEstimate: 1200000,\n      topKeywords: ['beauty fashion', 'luxury brands', 'accessories'],\n      socialFollowing: 650000,\n      engagementRate: 7.2\n    }];\n  }\n  generateGrowthData() {\n    const data = [];\n    const today = new Date();\n    for (let i = 29; i >= 0; i--) {\n      const date = new Date(today);\n      date.setDate(date.getDate() - i);\n      data.push({\n        date: date.toISOString().split('T')[0],\n        users: Math.floor(Math.random() * 200) + 100,\n        orders: Math.floor(Math.random() * 50) + 20,\n        revenue: Math.floor(Math.random() * 10000) + 5000\n      });\n    }\n    return data;\n  }\n  // Update analytics data locally\n  updateAnalyticsData(data) {\n    this.analyticsData$.next(data);\n  }\n  // Get current analytics data\n  getCurrentAnalyticsData() {\n    return this.analyticsData$.asObservable();\n  }\n  static {\n    this.ɵfac = function AnalyticsService_Factory(t) {\n      return new (t || AnalyticsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AnalyticsService,\n      factory: AnalyticsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "map", "catchError", "AnalyticsService", "constructor", "http", "apiUrl", "analyticsData$", "getAnalyticsOverview", "get", "pipe", "response", "success", "data", "getFallbackAnalytics", "error", "console", "getSocialMediaMetrics", "getFallbackSocialMetrics", "getSearchEngineData", "getFallbackSearchData", "getCompetitorAnalysis", "getFallbackCompetitorData", "scrapeInstagramData", "username", "post", "scrapeGoogleTrends", "keyword", "trackUserBehavior", "event", "timestamp", "Date", "exportAnalyticsData", "format", "date<PERSON><PERSON><PERSON>", "responseType", "totalUsers", "activeUsers", "totalProducts", "totalOrders", "totalRevenue", "conversionRate", "averageOrderValue", "topCategories", "name", "count", "revenue", "userGrowth", "generateGrowthData", "searchTrends", "query", "trend", "engagementMetrics", "pageViews", "sessionDuration", "bounceRate", "clickThroughRate", "platform", "followers", "engagement", "reach", "impressions", "mentions", "sentiment", "topPosts", "id", "content", "likes", "shares", "comments", "keywords", "position", "searchVolume", "difficulty", "organicTraffic", "averagePosition", "clicks", "competitor", "marketShare", "priceComparison", "trafficEstimate", "topKeywords", "socialFollowing", "engagementRate", "today", "i", "date", "setDate", "getDate", "push", "toISOString", "split", "users", "Math", "floor", "random", "orders", "updateAnalyticsData", "next", "getCurrentAnalyticsData", "asObservable", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\analytics.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\n\nexport interface AnalyticsData {\n  totalUsers: number;\n  activeUsers: number;\n  totalProducts: number;\n  totalOrders: number;\n  totalRevenue: number;\n  conversionRate: number;\n  averageOrderValue: number;\n  topCategories: Array<{\n    name: string;\n    count: number;\n    revenue: number;\n  }>;\n  userGrowth: Array<{\n    date: string;\n    users: number;\n    orders: number;\n    revenue: number;\n  }>;\n  searchTrends: Array<{\n    query: string;\n    count: number;\n    trend: 'up' | 'down' | 'stable';\n  }>;\n  engagementMetrics: {\n    pageViews: number;\n    sessionDuration: number;\n    bounceRate: number;\n    clickThroughRate: number;\n  };\n}\n\nexport interface SocialMediaMetrics {\n  platform: string;\n  followers: number;\n  engagement: number;\n  reach: number;\n  impressions: number;\n  mentions: number;\n  sentiment: 'positive' | 'negative' | 'neutral';\n  topPosts: Array<{\n    id: string;\n    content: string;\n    likes: number;\n    shares: number;\n    comments: number;\n  }>;\n}\n\nexport interface SearchEngineData {\n  keywords: Array<{\n    keyword: string;\n    position: number;\n    searchVolume: number;\n    difficulty: number;\n    trend: 'up' | 'down' | 'stable';\n  }>;\n  organicTraffic: number;\n  clickThroughRate: number;\n  averagePosition: number;\n  impressions: number;\n  clicks: number;\n}\n\nexport interface CompetitorAnalysis {\n  competitor: string;\n  marketShare: number;\n  priceComparison: number;\n  trafficEstimate: number;\n  topKeywords: string[];\n  socialFollowing: number;\n  engagementRate: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AnalyticsService {\n  private apiUrl = 'http://localhost:5000/api';\n  private analyticsData$ = new BehaviorSubject<AnalyticsData | null>(null);\n\n  constructor(private http: HttpClient) {}\n\n  // Core Analytics\n  getAnalyticsOverview(): Observable<AnalyticsData> {\n    return this.http.get<any>(`${this.apiUrl}/analytics/overview`)\n      .pipe(\n        map(response => response.success ? response.data : this.getFallbackAnalytics()),\n        catchError(error => {\n          console.error('Error fetching analytics:', error);\n          return [this.getFallbackAnalytics()];\n        })\n      );\n  }\n\n  // Social Media Analytics\n  getSocialMediaMetrics(): Observable<SocialMediaMetrics[]> {\n    return this.http.get<any>(`${this.apiUrl}/analytics/social-media`)\n      .pipe(\n        map(response => response.success ? response.data : this.getFallbackSocialMetrics()),\n        catchError(error => {\n          console.error('Error fetching social media metrics:', error);\n          return [this.getFallbackSocialMetrics()];\n        })\n      );\n  }\n\n  // Search Engine Analytics\n  getSearchEngineData(): Observable<SearchEngineData> {\n    return this.http.get<any>(`${this.apiUrl}/analytics/search-engine`)\n      .pipe(\n        map(response => response.success ? response.data : this.getFallbackSearchData()),\n        catchError(error => {\n          console.error('Error fetching search engine data:', error);\n          return [this.getFallbackSearchData()];\n        })\n      );\n  }\n\n  // Competitor Analysis\n  getCompetitorAnalysis(): Observable<CompetitorAnalysis[]> {\n    return this.http.get<any>(`${this.apiUrl}/analytics/competitors`)\n      .pipe(\n        map(response => response.success ? response.data : this.getFallbackCompetitorData()),\n        catchError(error => {\n          console.error('Error fetching competitor analysis:', error);\n          return [this.getFallbackCompetitorData()];\n        })\n      );\n  }\n\n  // Real-time Data Scraping\n  scrapeInstagramData(username: string): Observable<any> {\n    return this.http.post<any>(`${this.apiUrl}/analytics/scrape/instagram`, { username })\n      .pipe(\n        map(response => response.success ? response.data : null),\n        catchError(error => {\n          console.error('Error scraping Instagram data:', error);\n          return [null];\n        })\n      );\n  }\n\n  scrapeGoogleTrends(keyword: string): Observable<any> {\n    return this.http.post<any>(`${this.apiUrl}/analytics/scrape/google-trends`, { keyword })\n      .pipe(\n        map(response => response.success ? response.data : null),\n        catchError(error => {\n          console.error('Error scraping Google Trends:', error);\n          return [null];\n        })\n      );\n  }\n\n  // User Behavior Analytics\n  trackUserBehavior(event: string, data: any): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track`, { event, data, timestamp: new Date() })\n      .pipe(\n        catchError(error => {\n          console.error('Error tracking user behavior:', error);\n          return [];\n        })\n      );\n  }\n\n  // Export Analytics Data\n  exportAnalyticsData(format: 'csv' | 'excel' | 'pdf', dateRange: { start: Date; end: Date }): Observable<Blob> {\n    return this.http.post(`${this.apiUrl}/analytics/export`, \n      { format, dateRange }, \n      { responseType: 'blob' }\n    ).pipe(\n      catchError(error => {\n        console.error('Error exporting analytics data:', error);\n        throw error;\n      })\n    );\n  }\n\n  // Fallback Data Methods\n  private getFallbackAnalytics(): AnalyticsData {\n    return {\n      totalUsers: 15847,\n      activeUsers: 3421,\n      totalProducts: 1250,\n      totalOrders: 2847,\n      totalRevenue: 1247500,\n      conversionRate: 3.2,\n      averageOrderValue: 438,\n      topCategories: [\n        { name: 'Women', count: 1247, revenue: 547200 },\n        { name: 'Men', count: 892, revenue: 389400 },\n        { name: 'Accessories', count: 634, revenue: 278100 },\n        { name: 'Footwear', count: 521, revenue: 228700 },\n        { name: 'Beauty', count: 387, revenue: 169800 }\n      ],\n      userGrowth: this.generateGrowthData(),\n      searchTrends: [\n        { query: 'summer dress', count: 1247, trend: 'up' },\n        { query: 'casual wear', count: 892, trend: 'stable' },\n        { query: 'ethnic wear', count: 634, trend: 'up' },\n        { query: 'formal shoes', count: 521, trend: 'down' },\n        { query: 'handbags', count: 387, trend: 'up' }\n      ],\n      engagementMetrics: {\n        pageViews: 45672,\n        sessionDuration: 4.2,\n        bounceRate: 32.5,\n        clickThroughRate: 2.8\n      }\n    };\n  }\n\n  private getFallbackSocialMetrics(): SocialMediaMetrics[] {\n    return [\n      {\n        platform: 'Instagram',\n        followers: 125000,\n        engagement: 8.5,\n        reach: 89000,\n        impressions: 234000,\n        mentions: 1247,\n        sentiment: 'positive',\n        topPosts: [\n          { id: '1', content: 'Summer collection launch', likes: 2847, shares: 234, comments: 156 },\n          { id: '2', content: 'Behind the scenes', likes: 1923, shares: 189, comments: 98 },\n          { id: '3', content: 'Customer spotlight', likes: 1654, shares: 145, comments: 87 }\n        ]\n      },\n      {\n        platform: 'Facebook',\n        followers: 89000,\n        engagement: 6.2,\n        reach: 67000,\n        impressions: 178000,\n        mentions: 892,\n        sentiment: 'positive',\n        topPosts: [\n          { id: '1', content: 'New arrivals showcase', likes: 1847, shares: 167, comments: 123 },\n          { id: '2', content: 'Style tips and tricks', likes: 1234, shares: 134, comments: 89 },\n          { id: '3', content: 'Flash sale announcement', likes: 987, shares: 98, comments: 67 }\n        ]\n      },\n      {\n        platform: 'Twitter',\n        followers: 45000,\n        engagement: 4.8,\n        reach: 34000,\n        impressions: 89000,\n        mentions: 567,\n        sentiment: 'neutral',\n        topPosts: [\n          { id: '1', content: 'Fashion week highlights', likes: 892, shares: 234, comments: 67 },\n          { id: '2', content: 'Sustainable fashion tips', likes: 634, shares: 156, comments: 45 },\n          { id: '3', content: 'Trend predictions', likes: 521, shares: 123, comments: 34 }\n        ]\n      }\n    ];\n  }\n\n  private getFallbackSearchData(): SearchEngineData {\n    return {\n      keywords: [\n        { keyword: 'online fashion store', position: 3, searchVolume: 12000, difficulty: 65, trend: 'up' },\n        { keyword: 'women clothing', position: 7, searchVolume: 8900, difficulty: 72, trend: 'stable' },\n        { keyword: 'ethnic wear online', position: 5, searchVolume: 5600, difficulty: 58, trend: 'up' },\n        { keyword: 'designer clothes', position: 12, searchVolume: 4300, difficulty: 78, trend: 'down' },\n        { keyword: 'fashion accessories', position: 8, searchVolume: 3200, difficulty: 62, trend: 'stable' }\n      ],\n      organicTraffic: 23456,\n      clickThroughRate: 3.2,\n      averagePosition: 7.2,\n      impressions: 156789,\n      clicks: 5023\n    };\n  }\n\n  private getFallbackCompetitorData(): CompetitorAnalysis[] {\n    return [\n      {\n        competitor: 'Myntra',\n        marketShare: 28.5,\n        priceComparison: 105,\n        trafficEstimate: 2500000,\n        topKeywords: ['online fashion', 'ethnic wear', 'designer clothes'],\n        socialFollowing: 1200000,\n        engagementRate: 6.8\n      },\n      {\n        competitor: 'Ajio',\n        marketShare: 18.2,\n        priceComparison: 98,\n        trafficEstimate: 1800000,\n        topKeywords: ['trendy fashion', 'casual wear', 'footwear'],\n        socialFollowing: 890000,\n        engagementRate: 5.4\n      },\n      {\n        competitor: 'Nykaa Fashion',\n        marketShare: 12.7,\n        priceComparison: 112,\n        trafficEstimate: 1200000,\n        topKeywords: ['beauty fashion', 'luxury brands', 'accessories'],\n        socialFollowing: 650000,\n        engagementRate: 7.2\n      }\n    ];\n  }\n\n  private generateGrowthData() {\n    const data = [];\n    const today = new Date();\n    \n    for (let i = 29; i >= 0; i--) {\n      const date = new Date(today);\n      date.setDate(date.getDate() - i);\n      \n      data.push({\n        date: date.toISOString().split('T')[0],\n        users: Math.floor(Math.random() * 200) + 100,\n        orders: Math.floor(Math.random() * 50) + 20,\n        revenue: Math.floor(Math.random() * 10000) + 5000\n      });\n    }\n    \n    return data;\n  }\n\n  // Update analytics data locally\n  updateAnalyticsData(data: AnalyticsData): void {\n    this.analyticsData$.next(data);\n  }\n\n  // Get current analytics data\n  getCurrentAnalyticsData(): Observable<AnalyticsData | null> {\n    return this.analyticsData$.asObservable();\n  }\n}\n"], "mappings": "AAEA,SAAqBA,eAAe,QAAQ,MAAM;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AA+EhD,OAAM,MAAOC,gBAAgB;EAI3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,MAAM,GAAG,2BAA2B;IACpC,KAAAC,cAAc,GAAG,IAAIP,eAAe,CAAuB,IAAI,CAAC;EAEjC;EAEvC;EACAQ,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAM,GAAG,IAAI,CAACH,MAAM,qBAAqB,CAAC,CAC3DI,IAAI,CACHT,GAAG,CAACU,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACC,oBAAoB,EAAE,CAAC,EAC/EZ,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,CAAC,IAAI,CAACD,oBAAoB,EAAE,CAAC;IACtC,CAAC,CAAC,CACH;EACL;EAEA;EACAG,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACZ,IAAI,CAACI,GAAG,CAAM,GAAG,IAAI,CAACH,MAAM,yBAAyB,CAAC,CAC/DI,IAAI,CACHT,GAAG,CAACU,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACK,wBAAwB,EAAE,CAAC,EACnFhB,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,CAAC,IAAI,CAACG,wBAAwB,EAAE,CAAC;IAC1C,CAAC,CAAC,CACH;EACL;EAEA;EACAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACd,IAAI,CAACI,GAAG,CAAM,GAAG,IAAI,CAACH,MAAM,0BAA0B,CAAC,CAChEI,IAAI,CACHT,GAAG,CAACU,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACO,qBAAqB,EAAE,CAAC,EAChFlB,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,CAAC,IAAI,CAACK,qBAAqB,EAAE,CAAC;IACvC,CAAC,CAAC,CACH;EACL;EAEA;EACAC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAChB,IAAI,CAACI,GAAG,CAAM,GAAG,IAAI,CAACH,MAAM,wBAAwB,CAAC,CAC9DI,IAAI,CACHT,GAAG,CAACU,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACS,yBAAyB,EAAE,CAAC,EACpFpB,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,CAAC,IAAI,CAACO,yBAAyB,EAAE,CAAC;IAC3C,CAAC,CAAC,CACH;EACL;EAEA;EACAC,mBAAmBA,CAACC,QAAgB;IAClC,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAM,GAAG,IAAI,CAACnB,MAAM,6BAA6B,EAAE;MAAEkB;IAAQ,CAAE,CAAC,CAClFd,IAAI,CACHT,GAAG,CAACU,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAAC,EACxDX,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEAW,kBAAkBA,CAACC,OAAe;IAChC,OAAO,IAAI,CAACtB,IAAI,CAACoB,IAAI,CAAM,GAAG,IAAI,CAACnB,MAAM,iCAAiC,EAAE;MAAEqB;IAAO,CAAE,CAAC,CACrFjB,IAAI,CACHT,GAAG,CAACU,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAAC,EACxDX,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;EACAa,iBAAiBA,CAACC,KAAa,EAAEhB,IAAS;IACxC,OAAO,IAAI,CAACR,IAAI,CAACoB,IAAI,CAAC,GAAG,IAAI,CAACnB,MAAM,kBAAkB,EAAE;MAAEuB,KAAK;MAAEhB,IAAI;MAAEiB,SAAS,EAAE,IAAIC,IAAI;IAAE,CAAE,CAAC,CAC5FrB,IAAI,CACHR,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACL;EAEA;EACAiB,mBAAmBA,CAACC,MAA+B,EAAEC,SAAqC;IACxF,OAAO,IAAI,CAAC7B,IAAI,CAACoB,IAAI,CAAC,GAAG,IAAI,CAACnB,MAAM,mBAAmB,EACrD;MAAE2B,MAAM;MAAEC;IAAS,CAAE,EACrB;MAAEC,YAAY,EAAE;IAAM,CAAE,CACzB,CAACzB,IAAI,CACJR,UAAU,CAACa,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,OAAO;MACLsB,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,OAAO;MACrBC,cAAc,EAAE,GAAG;MACnBC,iBAAiB,EAAE,GAAG;MACtBC,aAAa,EAAE,CACb;QAAEC,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAM,CAAE,EAC/C;QAAEF,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAM,CAAE,EAC5C;QAAEF,IAAI,EAAE,aAAa;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAM,CAAE,EACpD;QAAEF,IAAI,EAAE,UAAU;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAM,CAAE,EACjD;QAAEF,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAM,CAAE,CAChD;MACDC,UAAU,EAAE,IAAI,CAACC,kBAAkB,EAAE;MACrCC,YAAY,EAAE,CACZ;QAAEC,KAAK,EAAE,cAAc;QAAEL,KAAK,EAAE,IAAI;QAAEM,KAAK,EAAE;MAAI,CAAE,EACnD;QAAED,KAAK,EAAE,aAAa;QAAEL,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE;MAAQ,CAAE,EACrD;QAAED,KAAK,EAAE,aAAa;QAAEL,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE;MAAI,CAAE,EACjD;QAAED,KAAK,EAAE,cAAc;QAAEL,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE;MAAM,CAAE,EACpD;QAAED,KAAK,EAAE,UAAU;QAAEL,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE;MAAI,CAAE,CAC/C;MACDC,iBAAiB,EAAE;QACjBC,SAAS,EAAE,KAAK;QAChBC,eAAe,EAAE,GAAG;QACpBC,UAAU,EAAE,IAAI;QAChBC,gBAAgB,EAAE;;KAErB;EACH;EAEQtC,wBAAwBA,CAAA;IAC9B,OAAO,CACL;MACEuC,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE,MAAM;MACnBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,CACR;QAAEC,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,0BAA0B;QAAEC,KAAK,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAG,CAAE,EACzF;QAAEJ,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,mBAAmB;QAAEC,KAAK,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAE,CAAE,EACjF;QAAEJ,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,oBAAoB;QAAEC,KAAK,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAE,CAAE;KAErF,EACD;MACEZ,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE,MAAM;MACnBC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,CACR;QAAEC,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,uBAAuB;QAAEC,KAAK,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAG,CAAE,EACtF;QAAEJ,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,uBAAuB;QAAEC,KAAK,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAE,CAAE,EACrF;QAAEJ,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,yBAAyB;QAAEC,KAAK,EAAE,GAAG;QAAEC,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAE,CAAE;KAExF,EACD;MACEZ,QAAQ,EAAE,SAAS;MACnBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,CACR;QAAEC,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,yBAAyB;QAAEC,KAAK,EAAE,GAAG;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAE,CAAE,EACtF;QAAEJ,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,0BAA0B;QAAEC,KAAK,EAAE,GAAG;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAE,CAAE,EACvF;QAAEJ,EAAE,EAAE,GAAG;QAAEC,OAAO,EAAE,mBAAmB;QAAEC,KAAK,EAAE,GAAG;QAAEC,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAE,CAAE;KAEnF,CACF;EACH;EAEQjD,qBAAqBA,CAAA;IAC3B,OAAO;MACLkD,QAAQ,EAAE,CACR;QAAE3C,OAAO,EAAE,sBAAsB;QAAE4C,QAAQ,EAAE,CAAC;QAAEC,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAE,EAAE;QAAEtB,KAAK,EAAE;MAAI,CAAE,EAClG;QAAExB,OAAO,EAAE,gBAAgB;QAAE4C,QAAQ,EAAE,CAAC;QAAEC,YAAY,EAAE,IAAI;QAAEC,UAAU,EAAE,EAAE;QAAEtB,KAAK,EAAE;MAAQ,CAAE,EAC/F;QAAExB,OAAO,EAAE,oBAAoB;QAAE4C,QAAQ,EAAE,CAAC;QAAEC,YAAY,EAAE,IAAI;QAAEC,UAAU,EAAE,EAAE;QAAEtB,KAAK,EAAE;MAAI,CAAE,EAC/F;QAAExB,OAAO,EAAE,kBAAkB;QAAE4C,QAAQ,EAAE,EAAE;QAAEC,YAAY,EAAE,IAAI;QAAEC,UAAU,EAAE,EAAE;QAAEtB,KAAK,EAAE;MAAM,CAAE,EAChG;QAAExB,OAAO,EAAE,qBAAqB;QAAE4C,QAAQ,EAAE,CAAC;QAAEC,YAAY,EAAE,IAAI;QAAEC,UAAU,EAAE,EAAE;QAAEtB,KAAK,EAAE;MAAQ,CAAE,CACrG;MACDuB,cAAc,EAAE,KAAK;MACrBlB,gBAAgB,EAAE,GAAG;MACrBmB,eAAe,EAAE,GAAG;MACpBd,WAAW,EAAE,MAAM;MACnBe,MAAM,EAAE;KACT;EACH;EAEQtD,yBAAyBA,CAAA;IAC/B,OAAO,CACL;MACEuD,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAE,IAAI;MACjBC,eAAe,EAAE,GAAG;MACpBC,eAAe,EAAE,OAAO;MACxBC,WAAW,EAAE,CAAC,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,CAAC;MAClEC,eAAe,EAAE,OAAO;MACxBC,cAAc,EAAE;KACjB,EACD;MACEN,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE,IAAI;MACjBC,eAAe,EAAE,EAAE;MACnBC,eAAe,EAAE,OAAO;MACxBC,WAAW,EAAE,CAAC,gBAAgB,EAAE,aAAa,EAAE,UAAU,CAAC;MAC1DC,eAAe,EAAE,MAAM;MACvBC,cAAc,EAAE;KACjB,EACD;MACEN,UAAU,EAAE,eAAe;MAC3BC,WAAW,EAAE,IAAI;MACjBC,eAAe,EAAE,GAAG;MACpBC,eAAe,EAAE,OAAO;MACxBC,WAAW,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,aAAa,CAAC;MAC/DC,eAAe,EAAE,MAAM;MACvBC,cAAc,EAAE;KACjB,CACF;EACH;EAEQnC,kBAAkBA,CAAA;IACxB,MAAMnC,IAAI,GAAG,EAAE;IACf,MAAMuE,KAAK,GAAG,IAAIrD,IAAI,EAAE;IAExB,KAAK,IAAIsD,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMC,IAAI,GAAG,IAAIvD,IAAI,CAACqD,KAAK,CAAC;MAC5BE,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,EAAE,GAAGH,CAAC,CAAC;MAEhCxE,IAAI,CAAC4E,IAAI,CAAC;QACRH,IAAI,EAAEA,IAAI,CAACI,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtCC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;QAC5CC,MAAM,EAAEH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;QAC3CjD,OAAO,EAAE+C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG;OAC9C,CAAC;;IAGJ,OAAOlF,IAAI;EACb;EAEA;EACAoF,mBAAmBA,CAACpF,IAAmB;IACrC,IAAI,CAACN,cAAc,CAAC2F,IAAI,CAACrF,IAAI,CAAC;EAChC;EAEA;EACAsF,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAC5F,cAAc,CAAC6F,YAAY,EAAE;EAC3C;;;uBAlQWjG,gBAAgB,EAAAkG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBrG,gBAAgB;MAAAsG,OAAA,EAAhBtG,gBAAgB,CAAAuG,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}