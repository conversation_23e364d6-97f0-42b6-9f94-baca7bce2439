{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/role-management.service\";\nimport * as i2 from \"../../../core/services/permission-management.service\";\nimport * as i3 from \"@angular/common\";\nfunction DepartmentDashboardComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_12_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DepartmentDashboardComponent_div_12_button_4_Template_button_click_0_listener() {\n      const action_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.handleQuickAction(action_r3.action));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r3 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background\", action_r3.color);\n    i0.ɵɵproperty(\"title\", action_r3.label);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r3.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h3\");\n    i0.ɵɵtext(2, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15);\n    i0.ɵɵtemplate(4, DepartmentDashboardComponent_div_12_button_4_Template, 4, 6, \"button\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getAvailableQuickActions());\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 27)(6, \"div\", 28);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 29);\n    i0.ɵɵelement(9, \"i\");\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 30);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(widget_r5.data.value);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(widget_r5.data.trend);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r3.getTrendIcon(widget_r5.data.trend));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(widget_r5.data.change);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(widget_r5.data.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 33)(5, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function DepartmentDashboardComponent_div_15_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const widget_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshWidget(widget_r5.id));\n    });\n    i0.ɵɵelement(6, \"i\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 36)(8, \"div\", 37);\n    i0.ɵɵelement(9, \"i\", 38);\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", widget_r5.title, \" Chart\");\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function DepartmentDashboardComponent_div_15_div_3_div_7_Template_div_click_0_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.handleListItemClick(item_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 45);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46)(4, \"div\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 49)(9, \"span\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 51);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", item_r8.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(item_r8.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.subtitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8.time);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(item_r8.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.statusText);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 41);\n    i0.ɵɵtext(5, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 42);\n    i0.ɵɵtemplate(7, DepartmentDashboardComponent_div_15_div_3_div_7_Template, 13, 10, \"div\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", widget_r5.data.items);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"div\", 56);\n    i0.ɵɵelement(8, \"div\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 58)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 59);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", widget_r5.data.percentage, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", widget_r5.data.percentage, \"%\")(\"background\", widget_r5.data.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", widget_r5.data.current, \" / \", widget_r5.data.target, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(widget_r5.data.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_5_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 72);\n    i0.ɵɵelementStart(4, \"div\", 73)(5, \"div\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r10.time);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", activity_r10.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r10.description);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 62)(5, \"select\", 63);\n    i0.ɵɵlistener(\"change\", function DepartmentDashboardComponent_div_15_div_5_Template_select_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.filterActivity($event));\n    });\n    i0.ɵɵelementStart(6, \"option\", 64);\n    i0.ɵɵtext(7, \"All Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 65);\n    i0.ɵɵtext(9, \"Today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 66);\n    i0.ɵɵtext(11, \"This Week\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 67)(13, \"div\", 68);\n    i0.ɵɵtemplate(14, DepartmentDashboardComponent_div_15_div_5_div_14_Template, 9, 5, \"div\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", widget_r5.data.activities);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, DepartmentDashboardComponent_div_15_div_1_Template, 14, 8, \"div\", 19)(2, DepartmentDashboardComponent_div_15_div_2_Template, 12, 2, \"div\", 20)(3, DepartmentDashboardComponent_div_15_div_3_Template, 8, 2, \"div\", 21)(4, DepartmentDashboardComponent_div_15_div_4_Template, 14, 9, \"div\", 22)(5, DepartmentDashboardComponent_div_15_div_5_Template, 15, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const widget_r5 = ctx.$implicit;\n    i0.ɵɵattribute(\"data-size\", widget_r5.size)(\"data-type\", widget_r5.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"metric\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"chart\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"list\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"progress\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"activity\");\n  }\n}\nexport class DepartmentDashboardComponent {\n  constructor(roleManagementService, permissionManagementService) {\n    this.roleManagementService = roleManagementService;\n    this.permissionManagementService = permissionManagementService;\n    this.department = null;\n    this.userRole = null;\n    this.departmentConfig = null;\n    this.destroy$ = new Subject();\n    this.departmentConfigs = {\n      administration: {\n        department: 'administration',\n        name: 'Administration',\n        color: '#FF6B6B',\n        icon: 'fas fa-shield-alt',\n        widgets: [{\n          id: 'user_stats',\n          title: 'User Statistics',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '1,247',\n            change: '+12%',\n            trend: 'up',\n            label: 'Total Users'\n          }\n        }, {\n          id: 'system_health',\n          title: 'System Health',\n          type: 'progress',\n          size: 'medium',\n          data: {\n            percentage: 94,\n            current: 94,\n            target: 100,\n            label: 'System Performance',\n            color: '#4CAF50'\n          }\n        }, {\n          id: 'recent_activities',\n          title: 'Recent Activities',\n          type: 'activity',\n          size: 'large',\n          data: {\n            activities: [{\n              time: '2 min ago',\n              title: 'New user registered',\n              description: '<EMAIL>',\n              color: '#4CAF50'\n            }, {\n              time: '15 min ago',\n              title: 'System backup completed',\n              description: 'Daily backup successful',\n              color: '#2196F3'\n            }]\n          }\n        }],\n        quickActions: [{\n          id: 'add_user',\n          label: 'Add User',\n          icon: 'fas fa-user-plus',\n          color: '#4CAF50',\n          action: 'add_user'\n        }, {\n          id: 'system_settings',\n          label: 'System Settings',\n          icon: 'fas fa-cogs',\n          color: '#FF9800',\n          action: 'system_settings'\n        }, {\n          id: 'backup',\n          label: 'Backup System',\n          icon: 'fas fa-download',\n          color: '#2196F3',\n          action: 'backup'\n        }]\n      },\n      sales: {\n        department: 'sales',\n        name: 'Sales',\n        color: '#45B7D1',\n        icon: 'fas fa-chart-line',\n        widgets: [{\n          id: 'monthly_revenue',\n          title: 'Monthly Revenue',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '₹2.4M',\n            change: '+18%',\n            trend: 'up',\n            label: 'This Month'\n          }\n        }, {\n          id: 'sales_target',\n          title: 'Sales Target',\n          type: 'progress',\n          size: 'medium',\n          data: {\n            percentage: 87,\n            current: 87,\n            target: 100,\n            label: 'Monthly Target',\n            color: '#45B7D1'\n          }\n        }, {\n          id: 'top_performers',\n          title: 'Top Performers',\n          type: 'list',\n          size: 'medium',\n          data: {\n            items: [{\n              title: 'Rahul Sharma',\n              subtitle: '₹450K revenue',\n              icon: 'fas fa-trophy',\n              color: '#FFD700',\n              time: 'This month',\n              status: 'top',\n              statusText: 'Top'\n            }, {\n              title: 'Priya Patel',\n              subtitle: '₹380K revenue',\n              icon: 'fas fa-medal',\n              color: '#C0C0C0',\n              time: 'This month',\n              status: 'second',\n              statusText: '2nd'\n            }]\n          }\n        }],\n        quickActions: [{\n          id: 'add_lead',\n          label: 'Add Lead',\n          icon: 'fas fa-user-plus',\n          color: '#4CAF50',\n          action: 'add_lead'\n        }, {\n          id: 'create_quote',\n          label: 'Create Quote',\n          icon: 'fas fa-file-invoice',\n          color: '#FF9800',\n          action: 'create_quote'\n        }, {\n          id: 'view_pipeline',\n          label: 'Sales Pipeline',\n          icon: 'fas fa-funnel-dollar',\n          color: '#2196F3',\n          action: 'view_pipeline'\n        }]\n      },\n      marketing: {\n        department: 'marketing',\n        name: 'Marketing',\n        color: '#F38BA8',\n        icon: 'fas fa-bullhorn',\n        widgets: [{\n          id: 'campaign_reach',\n          title: 'Campaign Reach',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '1.2M',\n            change: '+25%',\n            trend: 'up',\n            label: 'This Week'\n          }\n        }, {\n          id: 'engagement_rate',\n          title: 'Engagement Rate',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '4.8%',\n            change: '+0.3%',\n            trend: 'up',\n            label: 'Average'\n          }\n        }, {\n          id: 'active_campaigns',\n          title: 'Active Campaigns',\n          type: 'list',\n          size: 'large',\n          data: {\n            items: [{\n              title: 'Summer Sale 2024',\n              subtitle: 'Instagram & Facebook',\n              icon: 'fas fa-fire',\n              color: '#FF5722',\n              time: '2 days left',\n              status: 'active',\n              statusText: 'Active'\n            }, {\n              title: 'New Collection Launch',\n              subtitle: 'Multi-platform',\n              icon: 'fas fa-rocket',\n              color: '#9C27B0',\n              time: '1 week left',\n              status: 'scheduled',\n              statusText: 'Scheduled'\n            }]\n          }\n        }],\n        quickActions: [{\n          id: 'create_campaign',\n          label: 'Create Campaign',\n          icon: 'fas fa-plus',\n          color: '#4CAF50',\n          action: 'create_campaign'\n        }, {\n          id: 'content_calendar',\n          label: 'Content Calendar',\n          icon: 'fas fa-calendar',\n          color: '#FF9800',\n          action: 'content_calendar'\n        }, {\n          id: 'analytics',\n          label: 'Analytics',\n          icon: 'fas fa-chart-bar',\n          color: '#2196F3',\n          action: 'analytics'\n        }]\n      },\n      accounting: {\n        department: 'accounting',\n        name: 'Accounting',\n        color: '#FFD93D',\n        icon: 'fas fa-calculator',\n        widgets: [{\n          id: 'monthly_profit',\n          title: 'Monthly Profit',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '₹580K',\n            change: '+12%',\n            trend: 'up',\n            label: 'Net Profit'\n          }\n        }, {\n          id: 'pending_invoices',\n          title: 'Pending Invoices',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '23',\n            change: '-5',\n            trend: 'down',\n            label: 'Outstanding'\n          }\n        }, {\n          id: 'expense_breakdown',\n          title: 'Expense Breakdown',\n          type: 'chart',\n          size: 'medium',\n          data: {\n            chartType: 'pie',\n            categories: ['Operations', 'Marketing', 'Salaries', 'Other']\n          }\n        }],\n        quickActions: [{\n          id: 'create_invoice',\n          label: 'Create Invoice',\n          icon: 'fas fa-file-invoice',\n          color: '#4CAF50',\n          action: 'create_invoice'\n        }, {\n          id: 'expense_report',\n          label: 'Expense Report',\n          icon: 'fas fa-receipt',\n          color: '#FF9800',\n          action: 'expense_report'\n        }, {\n          id: 'financial_summary',\n          label: 'Financial Summary',\n          icon: 'fas fa-chart-pie',\n          color: '#2196F3',\n          action: 'financial_summary'\n        }]\n      },\n      support: {\n        department: 'support',\n        name: 'Customer Support',\n        color: '#FF8C42',\n        icon: 'fas fa-headset',\n        widgets: [{\n          id: 'open_tickets',\n          title: 'Open Tickets',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '47',\n            change: '-8',\n            trend: 'down',\n            label: 'Active Tickets'\n          }\n        }, {\n          id: 'response_time',\n          title: 'Avg Response Time',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '2.3h',\n            change: '-0.5h',\n            trend: 'down',\n            label: 'This Week'\n          }\n        }, {\n          id: 'recent_tickets',\n          title: 'Recent Tickets',\n          type: 'list',\n          size: 'large',\n          data: {\n            items: [{\n              title: 'Payment Issue',\n              subtitle: 'Customer: John Doe',\n              icon: 'fas fa-credit-card',\n              color: '#F44336',\n              time: '5 min ago',\n              status: 'urgent',\n              statusText: 'Urgent'\n            }, {\n              title: 'Product Question',\n              subtitle: 'Customer: Jane Smith',\n              icon: 'fas fa-question-circle',\n              color: '#FF9800',\n              time: '15 min ago',\n              status: 'normal',\n              statusText: 'Normal'\n            }]\n          }\n        }],\n        quickActions: [{\n          id: 'create_ticket',\n          label: 'Create Ticket',\n          icon: 'fas fa-ticket-alt',\n          color: '#4CAF50',\n          action: 'create_ticket'\n        }, {\n          id: 'knowledge_base',\n          label: 'Knowledge Base',\n          icon: 'fas fa-book',\n          color: '#FF9800',\n          action: 'knowledge_base'\n        }, {\n          id: 'customer_feedback',\n          label: 'Customer Feedback',\n          icon: 'fas fa-comments',\n          color: '#2196F3',\n          action: 'customer_feedback'\n        }]\n      },\n      content: {\n        department: 'content',\n        name: 'Content Management',\n        color: '#B19CD9',\n        icon: 'fas fa-edit',\n        widgets: [{\n          id: 'published_content',\n          title: 'Published This Month',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '156',\n            change: '+23',\n            trend: 'up',\n            label: 'Articles'\n          }\n        }, {\n          id: 'content_performance',\n          title: 'Content Performance',\n          type: 'chart',\n          size: 'medium',\n          data: {\n            chartType: 'line',\n            metric: 'engagement'\n          }\n        }, {\n          id: 'editorial_calendar',\n          title: 'Editorial Calendar',\n          type: 'calendar',\n          size: 'large',\n          data: {\n            events: []\n          }\n        }],\n        quickActions: [{\n          id: 'create_article',\n          label: 'Create Article',\n          icon: 'fas fa-pen',\n          color: '#4CAF50',\n          action: 'create_article'\n        }, {\n          id: 'schedule_post',\n          label: 'Schedule Post',\n          icon: 'fas fa-clock',\n          color: '#FF9800',\n          action: 'schedule_post'\n        }, {\n          id: 'content_analytics',\n          label: 'Content Analytics',\n          icon: 'fas fa-chart-line',\n          color: '#2196F3',\n          action: 'content_analytics'\n        }]\n      },\n      vendor_management: {\n        department: 'vendor_management',\n        name: 'Vendor Management',\n        color: '#FFB6C1',\n        icon: 'fas fa-store',\n        widgets: [{\n          id: 'active_vendors',\n          title: 'Active Vendors',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '89',\n            change: '+5',\n            trend: 'up',\n            label: 'Partners'\n          }\n        }, {\n          id: 'contract_renewals',\n          title: 'Contract Renewals',\n          type: 'metric',\n          size: 'small',\n          data: {\n            value: '12',\n            change: '0',\n            trend: 'stable',\n            label: 'Due This Month'\n          }\n        }, {\n          id: 'vendor_performance',\n          title: 'Vendor Performance',\n          type: 'chart',\n          size: 'medium',\n          data: {\n            chartType: 'bar',\n            metric: 'rating'\n          }\n        }],\n        quickActions: [{\n          id: 'add_vendor',\n          label: 'Add Vendor',\n          icon: 'fas fa-plus',\n          color: '#4CAF50',\n          action: 'add_vendor'\n        }, {\n          id: 'review_contracts',\n          label: 'Review Contracts',\n          icon: 'fas fa-file-contract',\n          color: '#FF9800',\n          action: 'review_contracts'\n        }, {\n          id: 'vendor_reports',\n          label: 'Vendor Reports',\n          icon: 'fas fa-chart-bar',\n          color: '#2196F3',\n          action: 'vendor_reports'\n        }]\n      }\n    };\n  }\n  ngOnInit() {\n    if (this.department) {\n      this.departmentConfig = this.departmentConfigs[this.department];\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  getDepartmentGradient() {\n    if (!this.departmentConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n    const color = this.departmentConfig.color;\n    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n  }\n  getRoleDisplayName() {\n    if (!this.userRole) return '';\n    return this.roleManagementService.getRoleConfig(this.userRole).displayName;\n  }\n  getWelcomeMessage() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good Morning';\n    if (hour < 17) return 'Good Afternoon';\n    return 'Good Evening';\n  }\n  getHeaderStats() {\n    // Mock data - would be replaced with real data\n    return [{\n      value: '94%',\n      label: 'Performance'\n    }, {\n      value: '23',\n      label: 'Active Tasks'\n    }, {\n      value: '5.2K',\n      label: 'This Month'\n    }];\n  }\n  getAvailableQuickActions() {\n    if (!this.departmentConfig || !this.userRole) return [];\n    return this.departmentConfig.quickActions.filter(action => {\n      if (!action.permission) return true;\n      const [module, actionName] = action.permission.split('.');\n      return this.permissionManagementService.hasPermission(this.userRole, module, actionName);\n    });\n  }\n  getAvailableWidgets() {\n    if (!this.departmentConfig || !this.userRole) return [];\n    return this.departmentConfig.widgets.filter(widget => {\n      if (!widget.permission) return true;\n      const [module, action] = widget.permission.split('.');\n      return this.permissionManagementService.hasPermission(this.userRole, module, action);\n    });\n  }\n  handleQuickAction(action) {\n    console.log('Quick action triggered:', action);\n    // Implement action handling\n  }\n  handleListItemClick(item) {\n    console.log('List item clicked:', item);\n    // Implement item click handling\n  }\n  refreshWidget(widgetId) {\n    console.log('Refreshing widget:', widgetId);\n    // Implement widget refresh\n  }\n  filterActivity(event) {\n    console.log('Filtering activity:', event.target.value);\n    // Implement activity filtering\n  }\n  getTrendIcon(trend) {\n    switch (trend) {\n      case 'up':\n        return 'fas fa-arrow-up';\n      case 'down':\n        return 'fas fa-arrow-down';\n      default:\n        return 'fas fa-minus';\n    }\n  }\n  static {\n    this.ɵfac = function DepartmentDashboardComponent_Factory(t) {\n      return new (t || DepartmentDashboardComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService), i0.ɵɵdirectiveInject(i2.PermissionManagementService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DepartmentDashboardComponent,\n      selectors: [[\"app-department-dashboard\"]],\n      inputs: {\n        department: \"department\",\n        userRole: \"userRole\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 11,\n      consts: [[1, \"department-dashboard\"], [1, \"department-header\"], [1, \"header-content\"], [1, \"department-icon\"], [1, \"header-text\"], [1, \"header-stats\"], [\"class\", \"stat-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"quick-actions\", 4, \"ngIf\"], [1, \"dashboard-widgets\"], [1, \"widgets-grid\"], [\"class\", \"widget-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"quick-actions\"], [1, \"actions-grid\"], [\"class\", \"action-btn\", 3, \"background\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-btn\", 3, \"click\", \"title\"], [1, \"widget-container\"], [\"class\", \"metric-widget\", 4, \"ngIf\"], [\"class\", \"chart-widget\", 4, \"ngIf\"], [\"class\", \"list-widget\", 4, \"ngIf\"], [\"class\", \"progress-widget\", 4, \"ngIf\"], [\"class\", \"activity-widget\", 4, \"ngIf\"], [1, \"metric-widget\"], [1, \"metric-header\"], [1, \"fas\", \"fa-chart-line\"], [1, \"metric-content\"], [1, \"metric-value\"], [1, \"metric-change\"], [1, \"metric-label\"], [1, \"chart-widget\"], [1, \"chart-header\"], [1, \"chart-controls\"], [1, \"chart-btn\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"chart-content\"], [1, \"chart-placeholder\"], [1, \"fas\", \"fa-chart-bar\"], [1, \"list-widget\"], [1, \"list-header\"], [\"href\", \"#\", 1, \"view-all\"], [1, \"list-content\"], [\"class\", \"list-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-item\", 3, \"click\"], [1, \"item-icon\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-subtitle\"], [1, \"item-meta\"], [1, \"item-time\"], [1, \"item-status\"], [1, \"progress-widget\"], [1, \"progress-header\"], [1, \"progress-percentage\"], [1, \"progress-content\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-details\"], [1, \"progress-label\"], [1, \"activity-widget\"], [1, \"activity-header\"], [1, \"activity-filter\"], [3, \"change\"], [\"value\", \"all\"], [\"value\", \"today\"], [\"value\", \"week\"], [1, \"activity-content\"], [1, \"activity-timeline\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activity-item\"], [1, \"activity-time\"], [1, \"activity-dot\"], [1, \"activity-details\"], [1, \"activity-title\"], [1, \"activity-description\"]],\n      template: function DepartmentDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"i\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"h1\");\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 5);\n          i0.ɵɵtemplate(11, DepartmentDashboardComponent_div_11_Template, 5, 2, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(12, DepartmentDashboardComponent_div_12_Template, 5, 1, \"div\", 7);\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9);\n          i0.ɵɵtemplate(15, DepartmentDashboardComponent_div_15_Template, 6, 7, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-department\", ctx.department);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"background\", ctx.getDepartmentGradient());\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.departmentConfig == null ? null : ctx.departmentConfig.icon);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.departmentConfig == null ? null : ctx.departmentConfig.name, \" Dashboard\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\"\", ctx.getRoleDisplayName(), \" - \", ctx.getWelcomeMessage(), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getHeaderStats());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getAvailableQuickActions().length > 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getAvailableWidgets());\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n      styles: [\".department-dashboard[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  min-height: 100vh;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  padding: 2rem;\\n  margin-bottom: 2rem;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  display: grid;\\n  grid-template-columns: auto 1fr auto;\\n  gap: 2rem;\\n  align-items: center;\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    text-align: center;\\n    gap: 1rem;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .department-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 2rem;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .department-icon[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    font-size: 1.5rem;\\n    margin: 0 auto;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  opacity: 0.9;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    gap: 1rem;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  line-height: 1;\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    font-size: 1.4rem;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n  margin-top: 0.25rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 1.5rem;\\n  margin-bottom: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #262626;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 1rem 1.5rem;\\n  border: none;\\n  border-radius: 12px;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-weight: 600;\\n  text-decoration: none;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(12, 1fr);\\n  gap: 1.5rem;\\n  grid-auto-rows: minmax(200px, auto);\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  transition: all 0.3s ease;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=small][_ngcontent-%COMP%] {\\n  grid-column: span 3;\\n}\\n@media (max-width: 1200px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=small][_ngcontent-%COMP%] {\\n    grid-column: span 4;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=small][_ngcontent-%COMP%] {\\n    grid-column: span 1;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=medium][_ngcontent-%COMP%] {\\n  grid-column: span 6;\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=medium][_ngcontent-%COMP%] {\\n    grid-column: span 1;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=large][_ngcontent-%COMP%] {\\n  grid-column: span 8;\\n}\\n@media (max-width: 768px) {\\n  .department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=large][_ngcontent-%COMP%] {\\n    grid-column: span 1;\\n  }\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=full][_ngcontent-%COMP%] {\\n  grid-column: span 12;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #8e8e8e;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #c0c0c0;\\n  font-size: 1.1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #262626;\\n  line-height: 1;\\n  margin-bottom: 0.5rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  margin-bottom: 0.25rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change.up[_ngcontent-%COMP%] {\\n  color: #4CAF50;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change.down[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change.stable[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.8rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #262626;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   .chart-controls[_ngcontent-%COMP%]   .chart-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: none;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  color: #6c757d;\\n  transition: all 0.3s ease;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   .chart-controls[_ngcontent-%COMP%]   .chart-btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-content[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%] {\\n  height: 200px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  background: #f8f9fa;\\n  border-radius: 12px;\\n  color: #6c757d;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-content[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-content[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #262626;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 0.75rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  margin: 0 -1rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  border-radius: 8px;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 0.9rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-subtitle[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.8rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-time[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #8e8e8e;\\n  font-size: 0.75rem;\\n  margin-bottom: 0.25rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.urgent[_ngcontent-%COMP%] {\\n  background: #ffebee;\\n  color: #f44336;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.normal[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  color: #ff9800;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.top[_ngcontent-%COMP%] {\\n  background: #fff8e1;\\n  color: #ffc107;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.active[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #4caf50;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #262626;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #262626;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: #f0f0f0;\\n  border-radius: 6px;\\n  overflow: hidden;\\n  margin-bottom: 1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  border-radius: 6px;\\n  transition: width 0.3s ease;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  font-size: 0.85rem;\\n  color: #8e8e8e;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #262626;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   .activity-filter[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 6px;\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.8rem;\\n  background: white;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: auto auto 1fr;\\n  gap: 1rem;\\n  align-items: center;\\n  padding: 0.75rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #8e8e8e;\\n  white-space: nowrap;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   .activity-description[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.8rem;\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  .department-dashboard[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .widget-container[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n    color: #ffffff;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%] {\\n    border-bottom-color: #333;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]:hover {\\n    background: #2a2a2a;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n    background: #333;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n    border-bottom-color: #333;\\n  }\\n  .department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvZGVwYXJ0bWVudC1kYXNoYm9hcmQvZGVwYXJ0bWVudC1kYXNoYm9hcmQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtBQUNGO0FBRUU7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBQUo7QUFFSTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSwyVEFBQTtFQUNBLFlBQUE7QUFBTjtBQUdJO0VBQ0Usa0JBQUE7RUFDQSxVQUFBO0VBQ0EsYUFBQTtFQUNBLG9DQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBRE47QUFHTTtFQVJGO0lBU0ksMEJBQUE7SUFDQSxrQkFBQTtJQUNBLFNBQUE7RUFBTjtBQUNGO0FBRU07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG9DQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0EseUNBQUE7QUFBUjtBQUVRO0VBWkY7SUFhSSxXQUFBO0lBQ0EsWUFBQTtJQUNBLGlCQUFBO0lBQ0EsY0FBQTtFQUNSO0FBQ0Y7QUFHUTtFQUNFLG9CQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLHlDQUFBO0FBRFY7QUFHVTtFQU5GO0lBT0ksZUFBQTtFQUFWO0FBQ0Y7QUFHUTtFQUNFLFNBQUE7RUFDQSxpQkFBQTtFQUNBLFlBQUE7QUFEVjtBQUtNO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUFIUjtBQUtRO0VBSkY7SUFLSSx1QkFBQTtJQUNBLFNBQUE7RUFGUjtBQUNGO0FBSVE7RUFDRSxrQkFBQTtBQUZWO0FBSVU7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFGWjtBQUlZO0VBTkY7SUFPSSxpQkFBQTtFQURaO0FBQ0Y7QUFJVTtFQUNFLGlCQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0FBRlo7QUFVRTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSwwQ0FBQTtBQVJKO0FBVUk7RUFDRSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBUk47QUFXSTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBQUE7QUFUTjtBQVdNO0VBTEY7SUFNSSwyREFBQTtFQVJOO0FBQ0Y7QUFVTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxvQkFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0VBQ0EsMENBQUE7QUFSUjtBQVVRO0VBQ0UsMkJBQUE7RUFDQSx5Q0FBQTtBQVJWO0FBV1E7RUFDRSx3QkFBQTtBQVRWO0FBWVE7RUFDRSxpQkFBQTtBQVZWO0FBYVE7RUFDRSxpQkFBQTtBQVhWO0FBbUJJO0VBQ0UsYUFBQTtFQUNBLHNDQUFBO0VBQ0EsV0FBQTtFQUNBLG1DQUFBO0FBakJOO0FBbUJNO0VBTkY7SUFPSSwwQkFBQTtFQWhCTjtBQUNGO0FBa0JNO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSwwQ0FBQTtFQUNBLHlCQUFBO0FBaEJSO0FBa0JRO0VBQ0UsMkJBQUE7RUFDQSx5Q0FBQTtBQWhCVjtBQW9CUTtFQUNFLG1CQUFBO0FBbEJWO0FBb0JVO0VBSEY7SUFJSSxtQkFBQTtFQWpCVjtBQUNGO0FBbUJVO0VBUEY7SUFRSSxtQkFBQTtFQWhCVjtBQUNGO0FBbUJRO0VBQ0UsbUJBQUE7QUFqQlY7QUFtQlU7RUFIRjtJQUlJLG1CQUFBO0VBaEJWO0FBQ0Y7QUFtQlE7RUFDRSxtQkFBQTtBQWpCVjtBQW1CVTtFQUhGO0lBSUksbUJBQUE7RUFoQlY7QUFDRjtBQW1CUTtFQUNFLG9CQUFBO0FBakJWO0FBeUJJO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQXZCTjtBQXlCTTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUF2QlI7QUEwQk07RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUF4QlI7QUE2Qk07RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtBQTNCUjtBQThCTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7QUE1QlI7QUE4QlE7RUFDRSxjQUFBO0FBNUJWO0FBK0JRO0VBQ0UsY0FBQTtBQTdCVjtBQWdDUTtFQUNFLGNBQUE7QUE5QlY7QUFrQ007RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUFoQ1I7QUFzQ0k7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBcENOO0FBc0NNO0VBQ0UsU0FBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBcENSO0FBd0NRO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0Q1Y7QUF3Q1U7RUFDRSxtQkFBQTtFQUNBLGNBQUE7QUF0Q1o7QUE2Q007RUFDRSxhQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0FBM0NSO0FBNkNRO0VBQ0UsZUFBQTtFQUNBLHFCQUFBO0FBM0NWO0FBOENRO0VBQ0UsU0FBQTtFQUNBLGlCQUFBO0FBNUNWO0FBbURJO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQWpETjtBQW1ETTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQWpEUjtBQW9ETTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFsRFI7QUFvRFE7RUFDRSwwQkFBQTtBQWxEVjtBQXdETTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxrQkFBQTtFQUNBLGdDQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBdERSO0FBd0RRO0VBQ0UsbUJBQUE7QUF0RFY7QUF5RFE7RUFDRSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUF2RFY7QUEwRFE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7QUF4RFY7QUEyRFE7RUFDRSxPQUFBO0FBekRWO0FBMkRVO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxzQkFBQTtBQXpEWjtBQTREVTtFQUNFLGNBQUE7RUFDQSxpQkFBQTtBQTFEWjtBQThEUTtFQUNFLGlCQUFBO0FBNURWO0FBOERVO0VBQ0UsY0FBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0FBNURaO0FBK0RVO0VBQ0UsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQTdEWjtBQStEWTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtBQTdEZDtBQWdFWTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtBQTlEZDtBQWlFWTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtBQS9EZDtBQWtFWTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtBQWhFZDtBQXlFSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUF2RU47QUF5RU07RUFDRSxTQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUF2RVI7QUEwRU07RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQXhFUjtBQTZFTTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQTNFUjtBQTZFUTtFQUNFLFlBQUE7RUFDQSxrQkFBQTtFQUNBLDJCQUFBO0FBM0VWO0FBK0VNO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUE3RVI7QUFtRkk7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBakZOO0FBbUZNO0VBQ0UsU0FBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBakZSO0FBb0ZNO0VBQ0UseUJBQUE7RUFDQSxrQkFBQTtFQUNBLHVCQUFBO0VBQ0EsaUJBQUE7RUFDQSxpQkFBQTtBQWxGUjtBQXdGUTtFQUNFLGFBQUE7RUFDQSxvQ0FBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0NBQUE7QUF0RlY7QUF3RlU7RUFDRSxtQkFBQTtBQXRGWjtBQXlGVTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBdkZaO0FBMEZVO0VBQ0UsVUFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBQXhGWjtBQTRGWTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0VBQ0Esc0JBQUE7QUExRmQ7QUE2Rlk7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUEzRmQ7O0FBcUdBO0VBQ0U7SUFDRSxtQkFBQTtFQWxHRjtFQW9HRTs7SUFFRSxtQkFBQTtJQUNBLGNBQUE7RUFsR0o7RUFxR0U7Ozs7O0lBS0UsY0FBQTtFQW5HSjtFQXNHRTtJQUNFLHlCQUFBO0VBcEdKO0VBc0dJO0lBQ0UsbUJBQUE7RUFwR047RUF1R0k7SUFDRSxjQUFBO0VBckdOO0VBeUdFO0lBQ0UsZ0JBQUE7RUF2R0o7RUEwR0U7SUFDRSx5QkFBQTtFQXhHSjtFQTBHSTtJQUNFLGNBQUE7RUF4R047QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5kZXBhcnRtZW50LWRhc2hib2FyZCB7XG4gIG1heC13aWR0aDogMTQwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbiAgcGFkZGluZzogMXJlbTtcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgbWluLWhlaWdodDogMTAwdmg7XG5cbiAgLy8gRGVwYXJ0bWVudCBIZWFkZXJcbiAgLmRlcGFydG1lbnQtaGVhZGVyIHtcbiAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICAgIHBhZGRpbmc6IDJyZW07XG4gICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgICAmOjpiZWZvcmUge1xuICAgICAgY29udGVudDogJyc7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDA7XG4gICAgICBsZWZ0OiAwO1xuICAgICAgcmlnaHQ6IDA7XG4gICAgICBib3R0b206IDA7XG4gICAgICBiYWNrZ3JvdW5kOiB1cmwoJ2RhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiB2aWV3Qm94PVwiMCAwIDEwMCAxMDBcIj48ZGVmcz48cGF0dGVybiBpZD1cImdyYWluXCIgd2lkdGg9XCIxMDBcIiBoZWlnaHQ9XCIxMDBcIiBwYXR0ZXJuVW5pdHM9XCJ1c2VyU3BhY2VPblVzZVwiPjxjaXJjbGUgY3g9XCI1MFwiIGN5PVwiNTBcIiByPVwiMVwiIGZpbGw9XCJ3aGl0ZVwiIG9wYWNpdHk9XCIwLjFcIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD1cIjEwMFwiIGhlaWdodD1cIjEwMFwiIGZpbGw9XCJ1cmwoJTIzZ3JhaW4pXCIvPjwvc3ZnPicpO1xuICAgICAgb3BhY2l0eTogMC4zO1xuICAgIH1cblxuICAgIC5oZWFkZXItY29udGVudCB7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICB6LWluZGV4OiAyO1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogYXV0byAxZnIgYXV0bztcbiAgICAgIGdhcDogMnJlbTtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICBnYXA6IDFyZW07XG4gICAgICB9XG5cbiAgICAgIC5kZXBhcnRtZW50LWljb24ge1xuICAgICAgICB3aWR0aDogODBweDtcbiAgICAgICAgaGVpZ2h0OiA4MHB4O1xuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuICAgICAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcblxuICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgICB3aWR0aDogNjBweDtcbiAgICAgICAgICBoZWlnaHQ6IDYwcHg7XG4gICAgICAgICAgZm9udC1zaXplOiAxLjVyZW07XG4gICAgICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmhlYWRlci10ZXh0IHtcbiAgICAgICAgaDEge1xuICAgICAgICAgIG1hcmdpbjogMCAwIDAuNXJlbSAwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMi41cmVtO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgdGV4dC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG5cbiAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBwIHtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgb3BhY2l0eTogMC45O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5oZWFkZXItc3RhdHMge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBnYXA6IDJyZW07XG5cbiAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgZ2FwOiAxcmVtO1xuICAgICAgICB9XG5cbiAgICAgICAgLnN0YXQtaXRlbSB7XG4gICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuXG4gICAgICAgICAgLnN0YXQtdmFsdWUge1xuICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgICAgICBmb250LXNpemU6IDEuOHJlbTtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMTtcblxuICAgICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS40cmVtO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC5zdGF0LWxhYmVsIHtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICAgICAgb3BhY2l0eTogMC44O1xuICAgICAgICAgICAgbWFyZ2luLXRvcDogMC4yNXJlbTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBRdWljayBBY3Rpb25zXG4gIC5xdWljay1hY3Rpb25zIHtcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgIHBhZGRpbmc6IDEuNXJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICAgIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA2KTtcblxuICAgIGgzIHtcbiAgICAgIG1hcmdpbjogMCAwIDFyZW0gMDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgZm9udC1zaXplOiAxLjJyZW07XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgIH1cblxuICAgIC5hY3Rpb25zLWdyaWQge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpO1xuICAgICAgZ2FwOiAxcmVtO1xuXG4gICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgxNTBweCwgMWZyKSk7XG4gICAgICB9XG5cbiAgICAgIC5hY3Rpb24tYnRuIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgZ2FwOiAwLjc1cmVtO1xuICAgICAgICBwYWRkaW5nOiAxcmVtIDEuNXJlbTtcbiAgICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgICBjb2xvcjogd2hpdGU7XG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjIpO1xuICAgICAgICB9XG5cbiAgICAgICAgJjphY3RpdmUge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGkge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgICAgICB9XG5cbiAgICAgICAgc3BhbiB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBEYXNoYm9hcmQgV2lkZ2V0c1xuICAuZGFzaGJvYXJkLXdpZGdldHMge1xuICAgIC53aWRnZXRzLWdyaWQge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDEyLCAxZnIpO1xuICAgICAgZ2FwOiAxLjVyZW07XG4gICAgICBncmlkLWF1dG8tcm93czogbWlubWF4KDIwMHB4LCBhdXRvKTtcblxuICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgICAgfVxuXG4gICAgICAud2lkZ2V0LWNvbnRhaW5lciB7XG4gICAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgICAgICBwYWRkaW5nOiAxLjVyZW07XG4gICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA2KTtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBXaWRnZXQgc2l6ZXNcbiAgICAgICAgJltkYXRhLXNpemU9XCJzbWFsbFwiXSB7XG4gICAgICAgICAgZ3JpZC1jb2x1bW46IHNwYW4gMztcblxuICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHtcbiAgICAgICAgICAgIGdyaWQtY29sdW1uOiBzcGFuIDQ7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgICAgICBncmlkLWNvbHVtbjogc3BhbiAxO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgICZbZGF0YS1zaXplPVwibWVkaXVtXCJdIHtcbiAgICAgICAgICBncmlkLWNvbHVtbjogc3BhbiA2O1xuXG4gICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgICAgICBncmlkLWNvbHVtbjogc3BhbiAxO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgICZbZGF0YS1zaXplPVwibGFyZ2VcIl0ge1xuICAgICAgICAgIGdyaWQtY29sdW1uOiBzcGFuIDg7XG5cbiAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgICAgIGdyaWQtY29sdW1uOiBzcGFuIDE7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgJltkYXRhLXNpemU9XCJmdWxsXCJdIHtcbiAgICAgICAgICBncmlkLWNvbHVtbjogc3BhbiAxMjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIFdpZGdldCBUeXBlc1xuICAubWV0cmljLXdpZGdldCB7XG4gICAgLm1ldHJpYy1oZWFkZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuXG4gICAgICBoNCB7XG4gICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICAgICAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XG4gICAgICB9XG5cbiAgICAgIGkge1xuICAgICAgICBjb2xvcjogI2MwYzBjMDtcbiAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICB9XG4gICAgfVxuXG4gICAgLm1ldHJpYy1jb250ZW50IHtcbiAgICAgIC5tZXRyaWMtdmFsdWUge1xuICAgICAgICBmb250LXNpemU6IDIuNXJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxO1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG4gICAgICB9XG5cbiAgICAgIC5tZXRyaWMtY2hhbmdlIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgZ2FwOiAwLjI1cmVtO1xuICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XG5cbiAgICAgICAgJi51cCB7XG4gICAgICAgICAgY29sb3I6ICM0Q0FGNTA7XG4gICAgICAgIH1cblxuICAgICAgICAmLmRvd24ge1xuICAgICAgICAgIGNvbG9yOiAjZjQ0MzM2O1xuICAgICAgICB9XG5cbiAgICAgICAgJi5zdGFibGUge1xuICAgICAgICAgIGNvbG9yOiAjZmY5ODAwO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5tZXRyaWMtbGFiZWwge1xuICAgICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLmNoYXJ0LXdpZGdldCB7XG4gICAgLmNoYXJ0LWhlYWRlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG5cbiAgICAgIGg0IHtcbiAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICB9XG5cbiAgICAgIC5jaGFydC1jb250cm9scyB7XG4gICAgICAgIC5jaGFydC1idG4ge1xuICAgICAgICAgIHdpZHRoOiAzMnB4O1xuICAgICAgICAgIGhlaWdodDogMzJweDtcbiAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7XG4gICAgICAgICAgICBjb2xvcjogIzQ5NTA1NztcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAuY2hhcnQtY29udGVudCB7XG4gICAgICAuY2hhcnQtcGxhY2Vob2xkZXIge1xuICAgICAgICBoZWlnaHQ6IDIwMHB4O1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XG5cbiAgICAgICAgaSB7XG4gICAgICAgICAgZm9udC1zaXplOiAycmVtO1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgICAgICAgfVxuXG4gICAgICAgIHAge1xuICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5saXN0LXdpZGdldCB7XG4gICAgLmxpc3QtaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuICAgICAgaDQge1xuICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIH1cblxuICAgICAgLnZpZXctYWxsIHtcbiAgICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLmxpc3QtY29udGVudCB7XG4gICAgICAubGlzdC1pdGVtIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgZ2FwOiAxcmVtO1xuICAgICAgICBwYWRkaW5nOiAwLjc1cmVtIDA7XG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwO1xuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgICAgJjpsYXN0LWNoaWxkIHtcbiAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lO1xuICAgICAgICB9XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgICAgICBtYXJnaW46IDAgLTFyZW07XG4gICAgICAgICAgcGFkZGluZy1sZWZ0OiAxcmVtO1xuICAgICAgICAgIHBhZGRpbmctcmlnaHQ6IDFyZW07XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLml0ZW0taWNvbiB7XG4gICAgICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICB9XG5cbiAgICAgICAgLml0ZW0tY29udGVudCB7XG4gICAgICAgICAgZmxleDogMTtcblxuICAgICAgICAgIC5pdGVtLXRpdGxlIHtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuaXRlbS1zdWJ0aXRsZSB7XG4gICAgICAgICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5pdGVtLW1ldGEge1xuICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0O1xuXG4gICAgICAgICAgLml0ZW0tdGltZSB7XG4gICAgICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuaXRlbS1zdGF0dXMge1xuICAgICAgICAgICAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW07XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgICAgICAgZm9udC1zaXplOiAwLjdyZW07XG4gICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcblxuICAgICAgICAgICAgJi51cmdlbnQge1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZlYmVlO1xuICAgICAgICAgICAgICBjb2xvcjogI2Y0NDMzNjtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgJi5ub3JtYWwge1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmM2UwO1xuICAgICAgICAgICAgICBjb2xvcjogI2ZmOTgwMDtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgJi50b3Age1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOGUxO1xuICAgICAgICAgICAgICBjb2xvcjogI2ZmYzEwNztcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgJi5hY3RpdmUge1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZThmNWU4O1xuICAgICAgICAgICAgICBjb2xvcjogIzRjYWY1MDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAucHJvZ3Jlc3Mtd2lkZ2V0IHtcbiAgICAucHJvZ3Jlc3MtaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuICAgICAgaDQge1xuICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIH1cblxuICAgICAgLnByb2dyZXNzLXBlcmNlbnRhZ2Uge1xuICAgICAgICBmb250LXNpemU6IDEuNXJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLnByb2dyZXNzLWNvbnRlbnQge1xuICAgICAgLnByb2dyZXNzLWJhciB7XG4gICAgICAgIGhlaWdodDogMTJweDtcbiAgICAgICAgYmFja2dyb3VuZDogI2YwZjBmMDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuXG4gICAgICAgIC5wcm9ncmVzcy1maWxsIHtcbiAgICAgICAgICBoZWlnaHQ6IDEwMCU7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgICAgICAgIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAucHJvZ3Jlc3MtZGV0YWlscyB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xuICAgICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuYWN0aXZpdHktd2lkZ2V0IHtcbiAgICAuYWN0aXZpdHktaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuICAgICAgaDQge1xuICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIH1cblxuICAgICAgLmFjdGl2aXR5LWZpbHRlciBzZWxlY3Qge1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwO1xuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xuICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLmFjdGl2aXR5LWNvbnRlbnQge1xuICAgICAgLmFjdGl2aXR5LXRpbWVsaW5lIHtcbiAgICAgICAgLmFjdGl2aXR5LWl0ZW0ge1xuICAgICAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiBhdXRvIGF1dG8gMWZyO1xuICAgICAgICAgIGdhcDogMXJlbTtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMDtcbiAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDtcblxuICAgICAgICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC5hY3Rpdml0eS10aW1lIHtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICAgICAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuYWN0aXZpdHktZG90IHtcbiAgICAgICAgICAgIHdpZHRoOiA4cHg7XG4gICAgICAgICAgICBoZWlnaHQ6IDhweDtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuYWN0aXZpdHktZGV0YWlscyB7XG4gICAgICAgICAgICAuYWN0aXZpdHktdGl0bGUge1xuICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5hY3Rpdml0eS1kZXNjcmlwdGlvbiB7XG4gICAgICAgICAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gRGFyayBtb2RlIHN1cHBvcnRcbkBtZWRpYSAocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspIHtcbiAgLmRlcGFydG1lbnQtZGFzaGJvYXJkIHtcbiAgICBiYWNrZ3JvdW5kOiAjMTIxMjEyO1xuXG4gICAgLnF1aWNrLWFjdGlvbnMsXG4gICAgLndpZGdldC1jb250YWluZXIge1xuICAgICAgYmFja2dyb3VuZDogIzFlMWUxZTtcbiAgICAgIGNvbG9yOiAjZmZmZmZmO1xuICAgIH1cblxuICAgIC5tZXRyaWMtd2lkZ2V0IC5tZXRyaWMtY29udGVudCAubWV0cmljLXZhbHVlLFxuICAgIC5jaGFydC13aWRnZXQgLmNoYXJ0LWhlYWRlciBoNCxcbiAgICAubGlzdC13aWRnZXQgLmxpc3QtaGVhZGVyIGg0LFxuICAgIC5wcm9ncmVzcy13aWRnZXQgLnByb2dyZXNzLWhlYWRlciBoNCxcbiAgICAuYWN0aXZpdHktd2lkZ2V0IC5hY3Rpdml0eS1oZWFkZXIgaDQge1xuICAgICAgY29sb3I6ICNmZmZmZmY7XG4gICAgfVxuXG4gICAgLmxpc3Qtd2lkZ2V0IC5saXN0LWNvbnRlbnQgLmxpc3QtaXRlbSB7XG4gICAgICBib3JkZXItYm90dG9tLWNvbG9yOiAjMzMzO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogIzJhMmEyYTtcbiAgICAgIH1cblxuICAgICAgLml0ZW0tdGl0bGUge1xuICAgICAgICBjb2xvcjogI2ZmZmZmZjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAucHJvZ3Jlc3Mtd2lkZ2V0IC5wcm9ncmVzcy1jb250ZW50IC5wcm9ncmVzcy1iYXIge1xuICAgICAgYmFja2dyb3VuZDogIzMzMztcbiAgICB9XG5cbiAgICAuYWN0aXZpdHktd2lkZ2V0IC5hY3Rpdml0eS1jb250ZW50IC5hY3Rpdml0eS10aW1lbGluZSAuYWN0aXZpdHktaXRlbSB7XG4gICAgICBib3JkZXItYm90dG9tLWNvbG9yOiAjMzMzO1xuXG4gICAgICAuYWN0aXZpdHktdGl0bGUge1xuICAgICAgICBjb2xvcjogI2ZmZmZmZjtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "stat_r1", "value", "label", "ɵɵlistener", "DepartmentDashboardComponent_div_12_button_4_Template_button_click_0_listener", "action_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "handleQuickAction", "action", "ɵɵelement", "ɵɵstyleProp", "color", "ɵɵproperty", "ɵɵclassMap", "icon", "ɵɵtemplate", "DepartmentDashboardComponent_div_12_button_4_Template", "getAvailableQuickActions", "widget_r5", "title", "data", "trend", "getTrendIcon", "change", "DepartmentDashboardComponent_div_15_div_2_Template_button_click_5_listener", "_r6", "refreshWidget", "id", "ɵɵtextInterpolate1", "DepartmentDashboardComponent_div_15_div_3_div_7_Template_div_click_0_listener", "item_r8", "_r7", "handleListItemClick", "subtitle", "time", "status", "statusText", "DepartmentDashboardComponent_div_15_div_3_div_7_Template", "items", "percentage", "ɵɵtextInterpolate2", "current", "target", "activity_r10", "description", "DepartmentDashboardComponent_div_15_div_5_Template_select_change_5_listener", "$event", "_r9", "filterActivity", "DepartmentDashboardComponent_div_15_div_5_div_14_Template", "activities", "DepartmentDashboardComponent_div_15_div_1_Template", "DepartmentDashboardComponent_div_15_div_2_Template", "DepartmentDashboardComponent_div_15_div_3_Template", "DepartmentDashboardComponent_div_15_div_4_Template", "DepartmentDashboardComponent_div_15_div_5_Template", "type", "DepartmentDashboardComponent", "constructor", "roleManagementService", "permissionManagementService", "department", "userRole", "departmentConfig", "destroy$", "departmentConfigs", "administration", "name", "widgets", "size", "quickActions", "sales", "marketing", "accounting", "chartType", "categories", "support", "content", "metric", "events", "vendor_management", "ngOnInit", "ngOnDestroy", "next", "complete", "getDepartmentGradient", "getRoleDisplayName", "getRoleConfig", "displayName", "getWelcomeMessage", "hour", "Date", "getHours", "getHeaderStats", "filter", "permission", "module", "actionName", "split", "hasPermission", "getAvailableWidgets", "widget", "console", "log", "item", "widgetId", "event", "ɵɵdirectiveInject", "i1", "RoleManagementService", "i2", "PermissionManagementService", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DepartmentDashboardComponent_Template", "rf", "ctx", "DepartmentDashboardComponent_div_11_Template", "DepartmentDashboardComponent_div_12_Template", "DepartmentDashboardComponent_div_15_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\department-dashboard\\department-dashboard.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { RoleManagementService, UserRole, Department } from '../../../core/services/role-management.service';\nimport { PermissionManagementService } from '../../../core/services/permission-management.service';\n\nexport interface DashboardWidget {\n  id: string;\n  title: string;\n  type: 'metric' | 'chart' | 'list' | 'progress' | 'calendar' | 'activity';\n  size: 'small' | 'medium' | 'large' | 'full';\n  data: any;\n  permission?: string;\n  refreshInterval?: number;\n}\n\nexport interface DepartmentConfig {\n  department: Department;\n  name: string;\n  color: string;\n  icon: string;\n  widgets: DashboardWidget[];\n  quickActions: QuickAction[];\n}\n\nexport interface QuickAction {\n  id: string;\n  label: string;\n  icon: string;\n  color: string;\n  action: string;\n  permission?: string;\n}\n\n@Component({\n  selector: 'app-department-dashboard',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"department-dashboard\" [attr.data-department]=\"department\">\n      <!-- Department Header -->\n      <div class=\"department-header\" [style.background]=\"getDepartmentGradient()\">\n        <div class=\"header-content\">\n          <div class=\"department-icon\">\n            <i [class]=\"departmentConfig?.icon\"></i>\n          </div>\n          <div class=\"header-text\">\n            <h1>{{ departmentConfig?.name }} Dashboard</h1>\n            <p>{{ getRoleDisplayName() }} - {{ getWelcomeMessage() }}</p>\n          </div>\n          <div class=\"header-stats\">\n            <div class=\"stat-item\" *ngFor=\"let stat of getHeaderStats()\">\n              <span class=\"stat-value\">{{ stat.value }}</span>\n              <span class=\"stat-label\">{{ stat.label }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Quick Actions -->\n      <div class=\"quick-actions\" *ngIf=\"getAvailableQuickActions().length > 0\">\n        <h3>Quick Actions</h3>\n        <div class=\"actions-grid\">\n          <button \n            *ngFor=\"let action of getAvailableQuickActions()\"\n            class=\"action-btn\"\n            [style.background]=\"action.color\"\n            (click)=\"handleQuickAction(action.action)\"\n            [title]=\"action.label\">\n            <i [class]=\"action.icon\"></i>\n            <span>{{ action.label }}</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Dashboard Widgets -->\n      <div class=\"dashboard-widgets\">\n        <div class=\"widgets-grid\">\n          <div \n            *ngFor=\"let widget of getAvailableWidgets()\"\n            class=\"widget-container\"\n            [attr.data-size]=\"widget.size\"\n            [attr.data-type]=\"widget.type\">\n            \n            <!-- Metric Widget -->\n            <div *ngIf=\"widget.type === 'metric'\" class=\"metric-widget\">\n              <div class=\"metric-header\">\n                <h4>{{ widget.title }}</h4>\n                <i class=\"fas fa-chart-line\"></i>\n              </div>\n              <div class=\"metric-content\">\n                <div class=\"metric-value\">{{ widget.data.value }}</div>\n                <div class=\"metric-change\" [class]=\"widget.data.trend\">\n                  <i [class]=\"getTrendIcon(widget.data.trend)\"></i>\n                  <span>{{ widget.data.change }}</span>\n                </div>\n                <div class=\"metric-label\">{{ widget.data.label }}</div>\n              </div>\n            </div>\n\n            <!-- Chart Widget -->\n            <div *ngIf=\"widget.type === 'chart'\" class=\"chart-widget\">\n              <div class=\"chart-header\">\n                <h4>{{ widget.title }}</h4>\n                <div class=\"chart-controls\">\n                  <button class=\"chart-btn\" (click)=\"refreshWidget(widget.id)\">\n                    <i class=\"fas fa-sync-alt\"></i>\n                  </button>\n                </div>\n              </div>\n              <div class=\"chart-content\">\n                <!-- Chart implementation would go here -->\n                <div class=\"chart-placeholder\">\n                  <i class=\"fas fa-chart-bar\"></i>\n                  <p>{{ widget.title }} Chart</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- List Widget -->\n            <div *ngIf=\"widget.type === 'list'\" class=\"list-widget\">\n              <div class=\"list-header\">\n                <h4>{{ widget.title }}</h4>\n                <a href=\"#\" class=\"view-all\">View All</a>\n              </div>\n              <div class=\"list-content\">\n                <div \n                  *ngFor=\"let item of widget.data.items\" \n                  class=\"list-item\"\n                  (click)=\"handleListItemClick(item)\">\n                  <div class=\"item-icon\" [style.background]=\"item.color\">\n                    <i [class]=\"item.icon\"></i>\n                  </div>\n                  <div class=\"item-content\">\n                    <div class=\"item-title\">{{ item.title }}</div>\n                    <div class=\"item-subtitle\">{{ item.subtitle }}</div>\n                  </div>\n                  <div class=\"item-meta\">\n                    <span class=\"item-time\">{{ item.time }}</span>\n                    <span class=\"item-status\" [class]=\"item.status\">{{ item.statusText }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Progress Widget -->\n            <div *ngIf=\"widget.type === 'progress'\" class=\"progress-widget\">\n              <div class=\"progress-header\">\n                <h4>{{ widget.title }}</h4>\n                <span class=\"progress-percentage\">{{ widget.data.percentage }}%</span>\n              </div>\n              <div class=\"progress-content\">\n                <div class=\"progress-bar\">\n                  <div \n                    class=\"progress-fill\" \n                    [style.width.%]=\"widget.data.percentage\"\n                    [style.background]=\"widget.data.color\">\n                  </div>\n                </div>\n                <div class=\"progress-details\">\n                  <span>{{ widget.data.current }} / {{ widget.data.target }}</span>\n                  <span class=\"progress-label\">{{ widget.data.label }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Activity Widget -->\n            <div *ngIf=\"widget.type === 'activity'\" class=\"activity-widget\">\n              <div class=\"activity-header\">\n                <h4>{{ widget.title }}</h4>\n                <div class=\"activity-filter\">\n                  <select (change)=\"filterActivity($event)\">\n                    <option value=\"all\">All Activities</option>\n                    <option value=\"today\">Today</option>\n                    <option value=\"week\">This Week</option>\n                  </select>\n                </div>\n              </div>\n              <div class=\"activity-content\">\n                <div class=\"activity-timeline\">\n                  <div \n                    *ngFor=\"let activity of widget.data.activities\" \n                    class=\"activity-item\">\n                    <div class=\"activity-time\">{{ activity.time }}</div>\n                    <div class=\"activity-dot\" [style.background]=\"activity.color\"></div>\n                    <div class=\"activity-details\">\n                      <div class=\"activity-title\">{{ activity.title }}</div>\n                      <div class=\"activity-description\">{{ activity.description }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./department-dashboard.component.scss']\n})\nexport class DepartmentDashboardComponent implements OnInit, OnDestroy {\n  @Input() department: Department | null = null;\n  @Input() userRole: UserRole | null = null;\n  \n  departmentConfig: DepartmentConfig | null = null;\n  \n  private destroy$ = new Subject<void>();\n\n  private departmentConfigs: Record<Department, DepartmentConfig> = {\n    administration: {\n      department: 'administration',\n      name: 'Administration',\n      color: '#FF6B6B',\n      icon: 'fas fa-shield-alt',\n      widgets: [\n        {\n          id: 'user_stats',\n          title: 'User Statistics',\n          type: 'metric',\n          size: 'small',\n          data: { value: '1,247', change: '+12%', trend: 'up', label: 'Total Users' }\n        },\n        {\n          id: 'system_health',\n          title: 'System Health',\n          type: 'progress',\n          size: 'medium',\n          data: { percentage: 94, current: 94, target: 100, label: 'System Performance', color: '#4CAF50' }\n        },\n        {\n          id: 'recent_activities',\n          title: 'Recent Activities',\n          type: 'activity',\n          size: 'large',\n          data: {\n            activities: [\n              { time: '2 min ago', title: 'New user registered', description: '<EMAIL>', color: '#4CAF50' },\n              { time: '15 min ago', title: 'System backup completed', description: 'Daily backup successful', color: '#2196F3' }\n            ]\n          }\n        }\n      ],\n      quickActions: [\n        { id: 'add_user', label: 'Add User', icon: 'fas fa-user-plus', color: '#4CAF50', action: 'add_user' },\n        { id: 'system_settings', label: 'System Settings', icon: 'fas fa-cogs', color: '#FF9800', action: 'system_settings' },\n        { id: 'backup', label: 'Backup System', icon: 'fas fa-download', color: '#2196F3', action: 'backup' }\n      ]\n    },\n    sales: {\n      department: 'sales',\n      name: 'Sales',\n      color: '#45B7D1',\n      icon: 'fas fa-chart-line',\n      widgets: [\n        {\n          id: 'monthly_revenue',\n          title: 'Monthly Revenue',\n          type: 'metric',\n          size: 'small',\n          data: { value: '₹2.4M', change: '+18%', trend: 'up', label: 'This Month' }\n        },\n        {\n          id: 'sales_target',\n          title: 'Sales Target',\n          type: 'progress',\n          size: 'medium',\n          data: { percentage: 87, current: 87, target: 100, label: 'Monthly Target', color: '#45B7D1' }\n        },\n        {\n          id: 'top_performers',\n          title: 'Top Performers',\n          type: 'list',\n          size: 'medium',\n          data: {\n            items: [\n              { title: 'Rahul Sharma', subtitle: '₹450K revenue', icon: 'fas fa-trophy', color: '#FFD700', time: 'This month', status: 'top', statusText: 'Top' },\n              { title: 'Priya Patel', subtitle: '₹380K revenue', icon: 'fas fa-medal', color: '#C0C0C0', time: 'This month', status: 'second', statusText: '2nd' }\n            ]\n          }\n        }\n      ],\n      quickActions: [\n        { id: 'add_lead', label: 'Add Lead', icon: 'fas fa-user-plus', color: '#4CAF50', action: 'add_lead' },\n        { id: 'create_quote', label: 'Create Quote', icon: 'fas fa-file-invoice', color: '#FF9800', action: 'create_quote' },\n        { id: 'view_pipeline', label: 'Sales Pipeline', icon: 'fas fa-funnel-dollar', color: '#2196F3', action: 'view_pipeline' }\n      ]\n    },\n    marketing: {\n      department: 'marketing',\n      name: 'Marketing',\n      color: '#F38BA8',\n      icon: 'fas fa-bullhorn',\n      widgets: [\n        {\n          id: 'campaign_reach',\n          title: 'Campaign Reach',\n          type: 'metric',\n          size: 'small',\n          data: { value: '1.2M', change: '+25%', trend: 'up', label: 'This Week' }\n        },\n        {\n          id: 'engagement_rate',\n          title: 'Engagement Rate',\n          type: 'metric',\n          size: 'small',\n          data: { value: '4.8%', change: '+0.3%', trend: 'up', label: 'Average' }\n        },\n        {\n          id: 'active_campaigns',\n          title: 'Active Campaigns',\n          type: 'list',\n          size: 'large',\n          data: {\n            items: [\n              { title: 'Summer Sale 2024', subtitle: 'Instagram & Facebook', icon: 'fas fa-fire', color: '#FF5722', time: '2 days left', status: 'active', statusText: 'Active' },\n              { title: 'New Collection Launch', subtitle: 'Multi-platform', icon: 'fas fa-rocket', color: '#9C27B0', time: '1 week left', status: 'scheduled', statusText: 'Scheduled' }\n            ]\n          }\n        }\n      ],\n      quickActions: [\n        { id: 'create_campaign', label: 'Create Campaign', icon: 'fas fa-plus', color: '#4CAF50', action: 'create_campaign' },\n        { id: 'content_calendar', label: 'Content Calendar', icon: 'fas fa-calendar', color: '#FF9800', action: 'content_calendar' },\n        { id: 'analytics', label: 'Analytics', icon: 'fas fa-chart-bar', color: '#2196F3', action: 'analytics' }\n      ]\n    },\n    accounting: {\n      department: 'accounting',\n      name: 'Accounting',\n      color: '#FFD93D',\n      icon: 'fas fa-calculator',\n      widgets: [\n        {\n          id: 'monthly_profit',\n          title: 'Monthly Profit',\n          type: 'metric',\n          size: 'small',\n          data: { value: '₹580K', change: '+12%', trend: 'up', label: 'Net Profit' }\n        },\n        {\n          id: 'pending_invoices',\n          title: 'Pending Invoices',\n          type: 'metric',\n          size: 'small',\n          data: { value: '23', change: '-5', trend: 'down', label: 'Outstanding' }\n        },\n        {\n          id: 'expense_breakdown',\n          title: 'Expense Breakdown',\n          type: 'chart',\n          size: 'medium',\n          data: { chartType: 'pie', categories: ['Operations', 'Marketing', 'Salaries', 'Other'] }\n        }\n      ],\n      quickActions: [\n        { id: 'create_invoice', label: 'Create Invoice', icon: 'fas fa-file-invoice', color: '#4CAF50', action: 'create_invoice' },\n        { id: 'expense_report', label: 'Expense Report', icon: 'fas fa-receipt', color: '#FF9800', action: 'expense_report' },\n        { id: 'financial_summary', label: 'Financial Summary', icon: 'fas fa-chart-pie', color: '#2196F3', action: 'financial_summary' }\n      ]\n    },\n    support: {\n      department: 'support',\n      name: 'Customer Support',\n      color: '#FF8C42',\n      icon: 'fas fa-headset',\n      widgets: [\n        {\n          id: 'open_tickets',\n          title: 'Open Tickets',\n          type: 'metric',\n          size: 'small',\n          data: { value: '47', change: '-8', trend: 'down', label: 'Active Tickets' }\n        },\n        {\n          id: 'response_time',\n          title: 'Avg Response Time',\n          type: 'metric',\n          size: 'small',\n          data: { value: '2.3h', change: '-0.5h', trend: 'down', label: 'This Week' }\n        },\n        {\n          id: 'recent_tickets',\n          title: 'Recent Tickets',\n          type: 'list',\n          size: 'large',\n          data: {\n            items: [\n              { title: 'Payment Issue', subtitle: 'Customer: John Doe', icon: 'fas fa-credit-card', color: '#F44336', time: '5 min ago', status: 'urgent', statusText: 'Urgent' },\n              { title: 'Product Question', subtitle: 'Customer: Jane Smith', icon: 'fas fa-question-circle', color: '#FF9800', time: '15 min ago', status: 'normal', statusText: 'Normal' }\n            ]\n          }\n        }\n      ],\n      quickActions: [\n        { id: 'create_ticket', label: 'Create Ticket', icon: 'fas fa-ticket-alt', color: '#4CAF50', action: 'create_ticket' },\n        { id: 'knowledge_base', label: 'Knowledge Base', icon: 'fas fa-book', color: '#FF9800', action: 'knowledge_base' },\n        { id: 'customer_feedback', label: 'Customer Feedback', icon: 'fas fa-comments', color: '#2196F3', action: 'customer_feedback' }\n      ]\n    },\n    content: {\n      department: 'content',\n      name: 'Content Management',\n      color: '#B19CD9',\n      icon: 'fas fa-edit',\n      widgets: [\n        {\n          id: 'published_content',\n          title: 'Published This Month',\n          type: 'metric',\n          size: 'small',\n          data: { value: '156', change: '+23', trend: 'up', label: 'Articles' }\n        },\n        {\n          id: 'content_performance',\n          title: 'Content Performance',\n          type: 'chart',\n          size: 'medium',\n          data: { chartType: 'line', metric: 'engagement' }\n        },\n        {\n          id: 'editorial_calendar',\n          title: 'Editorial Calendar',\n          type: 'calendar',\n          size: 'large',\n          data: { events: [] }\n        }\n      ],\n      quickActions: [\n        { id: 'create_article', label: 'Create Article', icon: 'fas fa-pen', color: '#4CAF50', action: 'create_article' },\n        { id: 'schedule_post', label: 'Schedule Post', icon: 'fas fa-clock', color: '#FF9800', action: 'schedule_post' },\n        { id: 'content_analytics', label: 'Content Analytics', icon: 'fas fa-chart-line', color: '#2196F3', action: 'content_analytics' }\n      ]\n    },\n    vendor_management: {\n      department: 'vendor_management',\n      name: 'Vendor Management',\n      color: '#FFB6C1',\n      icon: 'fas fa-store',\n      widgets: [\n        {\n          id: 'active_vendors',\n          title: 'Active Vendors',\n          type: 'metric',\n          size: 'small',\n          data: { value: '89', change: '+5', trend: 'up', label: 'Partners' }\n        },\n        {\n          id: 'contract_renewals',\n          title: 'Contract Renewals',\n          type: 'metric',\n          size: 'small',\n          data: { value: '12', change: '0', trend: 'stable', label: 'Due This Month' }\n        },\n        {\n          id: 'vendor_performance',\n          title: 'Vendor Performance',\n          type: 'chart',\n          size: 'medium',\n          data: { chartType: 'bar', metric: 'rating' }\n        }\n      ],\n      quickActions: [\n        { id: 'add_vendor', label: 'Add Vendor', icon: 'fas fa-plus', color: '#4CAF50', action: 'add_vendor' },\n        { id: 'review_contracts', label: 'Review Contracts', icon: 'fas fa-file-contract', color: '#FF9800', action: 'review_contracts' },\n        { id: 'vendor_reports', label: 'Vendor Reports', icon: 'fas fa-chart-bar', color: '#2196F3', action: 'vendor_reports' }\n      ]\n    }\n  };\n\n  constructor(\n    private roleManagementService: RoleManagementService,\n    private permissionManagementService: PermissionManagementService\n  ) {}\n\n  ngOnInit() {\n    if (this.department) {\n      this.departmentConfig = this.departmentConfigs[this.department];\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  getDepartmentGradient(): string {\n    if (!this.departmentConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n    \n    const color = this.departmentConfig.color;\n    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n  }\n\n  getRoleDisplayName(): string {\n    if (!this.userRole) return '';\n    return this.roleManagementService.getRoleConfig(this.userRole).displayName;\n  }\n\n  getWelcomeMessage(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good Morning';\n    if (hour < 17) return 'Good Afternoon';\n    return 'Good Evening';\n  }\n\n  getHeaderStats(): any[] {\n    // Mock data - would be replaced with real data\n    return [\n      { value: '94%', label: 'Performance' },\n      { value: '23', label: 'Active Tasks' },\n      { value: '5.2K', label: 'This Month' }\n    ];\n  }\n\n  getAvailableQuickActions(): QuickAction[] {\n    if (!this.departmentConfig || !this.userRole) return [];\n    \n    return this.departmentConfig.quickActions.filter(action => {\n      if (!action.permission) return true;\n      const [module, actionName] = action.permission.split('.');\n      return this.permissionManagementService.hasPermission(this.userRole!, module, actionName);\n    });\n  }\n\n  getAvailableWidgets(): DashboardWidget[] {\n    if (!this.departmentConfig || !this.userRole) return [];\n    \n    return this.departmentConfig.widgets.filter(widget => {\n      if (!widget.permission) return true;\n      const [module, action] = widget.permission.split('.');\n      return this.permissionManagementService.hasPermission(this.userRole!, module, action);\n    });\n  }\n\n  handleQuickAction(action: string): void {\n    console.log('Quick action triggered:', action);\n    // Implement action handling\n  }\n\n  handleListItemClick(item: any): void {\n    console.log('List item clicked:', item);\n    // Implement item click handling\n  }\n\n  refreshWidget(widgetId: string): void {\n    console.log('Refreshing widget:', widgetId);\n    // Implement widget refresh\n  }\n\n  filterActivity(event: any): void {\n    console.log('Filtering activity:', event.target.value);\n    // Implement activity filtering\n  }\n\n  getTrendIcon(trend: string): string {\n    switch (trend) {\n      case 'up': return 'fas fa-arrow-up';\n      case 'down': return 'fas fa-arrow-down';\n      default: return 'fas fa-minus';\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,QAAmB,MAAM;;;;;;;IAkD3BC,EADF,CAAAC,cAAA,cAA6D,eAClC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;;;;IAFqBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAE,KAAA,CAAgB;;;;;;IAU7CR,EAAA,CAAAC,cAAA,iBAKyB;IADvBD,EAAA,CAAAS,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,SAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,SAAA,CAAAQ,MAAA,CAAgC;IAAA,EAAC;IAE1CnB,EAAA,CAAAoB,SAAA,QAA6B;IAC7BpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EACxB;;;;IALPH,EAAA,CAAAqB,WAAA,eAAAV,SAAA,CAAAW,KAAA,CAAiC;IAEjCtB,EAAA,CAAAuB,UAAA,UAAAZ,SAAA,CAAAH,KAAA,CAAsB;IACnBR,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAwB,UAAA,CAAAb,SAAA,CAAAc,IAAA,CAAqB;IAClBzB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAM,SAAA,CAAAH,KAAA,CAAkB;;;;;IAT5BR,EADF,CAAAC,cAAA,cAAyE,SACnE;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA0B,UAAA,IAAAC,qDAAA,qBAKyB;IAK7B3B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IATmBH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAuB,UAAA,YAAAR,MAAA,CAAAa,wBAAA,GAA6B;;;;;IAuB5C5B,EAFJ,CAAAC,cAAA,cAA4D,cAC/B,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAoB,SAAA,YAAiC;IACnCpB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA4B,cACA;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAoB,SAAA,QAAiD;IACjDpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;IACNH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAErDF,EAFqD,CAAAG,YAAA,EAAM,EACnD,EACF;;;;;IAXEH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAC,KAAA,CAAkB;IAII9B,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAE,IAAA,CAAAxB,KAAA,CAAuB;IACtBP,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAwB,UAAA,CAAAK,SAAA,CAAAE,IAAA,CAAAC,KAAA,CAA2B;IACjDhC,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAwB,UAAA,CAAAT,MAAA,CAAAkB,YAAA,CAAAJ,SAAA,CAAAE,IAAA,CAAAC,KAAA,EAAyC;IACtChC,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAE,IAAA,CAAAG,MAAA,CAAwB;IAENlC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAE,IAAA,CAAAvB,KAAA,CAAuB;;;;;;IAOjDR,EAFJ,CAAAC,cAAA,cAA0D,cAC9B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzBH,EADF,CAAAC,cAAA,cAA4B,iBACmC;IAAnCD,EAAA,CAAAS,UAAA,mBAAA0B,2EAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAAwB,GAAA;MAAA,MAAAP,SAAA,GAAA7B,EAAA,CAAAgB,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsB,aAAA,CAAAR,SAAA,CAAAS,EAAA,CAAwB;IAAA,EAAC;IAC1DtC,EAAA,CAAAoB,SAAA,YAA+B;IAGrCpB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EAFF,CAAAC,cAAA,cAA2B,cAEM;IAC7BD,EAAA,CAAAoB,SAAA,YAAgC;IAChCpB,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAGjCF,EAHiC,CAAAG,YAAA,EAAI,EAC3B,EACF,EACF;;;;IAdEH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAC,KAAA,CAAkB;IAWjB9B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAuC,kBAAA,KAAAV,SAAA,CAAAC,KAAA,WAAwB;;;;;;IAY7B9B,EAAA,CAAAC,cAAA,cAGsC;IAApCD,EAAA,CAAAS,UAAA,mBAAA+B,8EAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAY,aAAA,CAAA8B,GAAA,EAAA5B,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA4B,mBAAA,CAAAF,OAAA,CAAyB;IAAA,EAAC;IACnCzC,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAoB,SAAA,QAA2B;IAC7BpB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,cACA;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAChDF,EADgD,CAAAG,YAAA,EAAM,EAChD;IAEJH,EADF,CAAAC,cAAA,cAAuB,eACG;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAEzEF,EAFyE,CAAAG,YAAA,EAAO,EACxE,EACF;;;;IAXmBH,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAqB,WAAA,eAAAoB,OAAA,CAAAnB,KAAA,CAA+B;IACjDtB,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAwB,UAAA,CAAAiB,OAAA,CAAAhB,IAAA,CAAmB;IAGEzB,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAX,KAAA,CAAgB;IACb9B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAG,QAAA,CAAmB;IAGtB5C,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAI,IAAA,CAAe;IACb7C,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAwB,UAAA,CAAAiB,OAAA,CAAAK,MAAA,CAAqB;IAAC9C,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAM,UAAA,CAAqB;;;;;IAjBzE/C,EAFJ,CAAAC,cAAA,cAAwD,cAC7B,SACnB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAA6B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IACvCF,EADuC,CAAAG,YAAA,EAAI,EACrC;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA0B,UAAA,IAAAsB,wDAAA,oBAGsC;IAc1ChD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IArBEH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAC,KAAA,CAAkB;IAKH9B,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAuB,UAAA,YAAAM,SAAA,CAAAE,IAAA,CAAAkB,KAAA,CAAoB;;;;;IAqBvCjD,EAFJ,CAAAC,cAAA,cAAgE,cACjC,SACvB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAoB,SAAA,cAIM;IACRpB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA8B,YACtB;IAAAD,EAAA,CAAAE,MAAA,IAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAG1DF,EAH0D,CAAAG,YAAA,EAAO,EACvD,EACF,EACF;;;;IAhBEH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAC,KAAA,CAAkB;IACY9B,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAuC,kBAAA,KAAAV,SAAA,CAAAE,IAAA,CAAAmB,UAAA,MAA6B;IAM3DlD,EAAA,CAAAI,SAAA,GAAwC;IACxCJ,EADA,CAAAqB,WAAA,UAAAQ,SAAA,CAAAE,IAAA,CAAAmB,UAAA,MAAwC,eAAArB,SAAA,CAAAE,IAAA,CAAAT,KAAA,CACF;IAIlCtB,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAmD,kBAAA,KAAAtB,SAAA,CAAAE,IAAA,CAAAqB,OAAA,SAAAvB,SAAA,CAAAE,IAAA,CAAAsB,MAAA,KAAoD;IAC7BrD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAE,IAAA,CAAAvB,KAAA,CAAuB;;;;;IAsBlDR,EAHF,CAAAC,cAAA,cAEwB,cACK;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAoB,SAAA,cAAoE;IAElEpB,EADF,CAAAC,cAAA,cAA8B,cACA;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAEhEF,EAFgE,CAAAG,YAAA,EAAM,EAC9D,EACF;;;;IANuBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAiD,YAAA,CAAAT,IAAA,CAAmB;IACpB7C,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAqB,WAAA,eAAAiC,YAAA,CAAAhC,KAAA,CAAmC;IAE/BtB,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAiD,YAAA,CAAAxB,KAAA,CAAoB;IACd9B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAiD,YAAA,CAAAC,WAAA,CAA0B;;;;;;IAlBlEvD,EAFJ,CAAAC,cAAA,cAAgE,cACjC,SACvB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzBH,EADF,CAAAC,cAAA,cAA6B,iBACe;IAAlCD,EAAA,CAAAS,UAAA,oBAAA+C,4EAAAC,MAAA;MAAAzD,EAAA,CAAAY,aAAA,CAAA8C,GAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAUF,MAAA,CAAA4C,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IACvCzD,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3CH,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAGpCF,EAHoC,CAAAG,YAAA,EAAS,EAChC,EACL,EACF;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACG;IAC7BD,EAAA,CAAA0B,UAAA,KAAAkC,yDAAA,kBAEwB;IAU9B5D,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAvBEH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAwB,SAAA,CAAAC,KAAA,CAAkB;IAYG9B,EAAA,CAAAI,SAAA,IAAyB;IAAzBJ,EAAA,CAAAuB,UAAA,YAAAM,SAAA,CAAAE,IAAA,CAAA8B,UAAA,CAAyB;;;;;IAvGxD7D,EAAA,CAAAC,cAAA,cAIiC;IAqF/BD,EAlFA,CAAA0B,UAAA,IAAAoC,kDAAA,mBAA4D,IAAAC,kDAAA,mBAgBF,IAAAC,kDAAA,kBAmBF,IAAAC,kDAAA,mBA0BQ,IAAAC,kDAAA,mBAqBA;IA0BlElE,EAAA,CAAAG,YAAA,EAAM;;;;;IA5GEH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAuB,UAAA,SAAAM,SAAA,CAAAsC,IAAA,cAA8B;IAgB9BnE,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAuB,UAAA,SAAAM,SAAA,CAAAsC,IAAA,aAA6B;IAmB7BnE,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAuB,UAAA,SAAAM,SAAA,CAAAsC,IAAA,YAA4B;IA0B5BnE,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAuB,UAAA,SAAAM,SAAA,CAAAsC,IAAA,gBAAgC;IAqBhCnE,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAuB,UAAA,SAAAM,SAAA,CAAAsC,IAAA,gBAAgC;;;AAiClD,OAAM,MAAOC,4BAA4B;EA6QvCC,YACUC,qBAA4C,EAC5CC,2BAAwD;IADxD,KAAAD,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,2BAA2B,GAA3BA,2BAA2B;IA9Q5B,KAAAC,UAAU,GAAsB,IAAI;IACpC,KAAAC,QAAQ,GAAoB,IAAI;IAEzC,KAAAC,gBAAgB,GAA4B,IAAI;IAExC,KAAAC,QAAQ,GAAG,IAAI5E,OAAO,EAAQ;IAE9B,KAAA6E,iBAAiB,GAAyC;MAChEC,cAAc,EAAE;QACdL,UAAU,EAAE,gBAAgB;QAC5BM,IAAI,EAAE,gBAAgB;QACtBxD,KAAK,EAAE,SAAS;QAChBG,IAAI,EAAE,mBAAmB;QACzBsD,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,YAAY;UAChBR,KAAK,EAAE,iBAAiB;UACxBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,OAAO;YAAE2B,MAAM,EAAE,MAAM;YAAEF,KAAK,EAAE,IAAI;YAAExB,KAAK,EAAE;UAAa;SAC1E,EACD;UACE8B,EAAE,EAAE,eAAe;UACnBR,KAAK,EAAE,eAAe;UACtBqC,IAAI,EAAE,UAAU;UAChBa,IAAI,EAAE,QAAQ;UACdjD,IAAI,EAAE;YAAEmB,UAAU,EAAE,EAAE;YAAEE,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE,GAAG;YAAE7C,KAAK,EAAE,oBAAoB;YAAEc,KAAK,EAAE;UAAS;SAChG,EACD;UACEgB,EAAE,EAAE,mBAAmB;UACvBR,KAAK,EAAE,mBAAmB;UAC1BqC,IAAI,EAAE,UAAU;UAChBa,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YACJ8B,UAAU,EAAE,CACV;cAAEhB,IAAI,EAAE,WAAW;cAAEf,KAAK,EAAE,qBAAqB;cAAEyB,WAAW,EAAE,sBAAsB;cAAEjC,KAAK,EAAE;YAAS,CAAE,EAC1G;cAAEuB,IAAI,EAAE,YAAY;cAAEf,KAAK,EAAE,yBAAyB;cAAEyB,WAAW,EAAE,yBAAyB;cAAEjC,KAAK,EAAE;YAAS,CAAE;;SAGvH,CACF;QACD2D,YAAY,EAAE,CACZ;UAAE3C,EAAE,EAAE,UAAU;UAAE9B,KAAK,EAAE,UAAU;UAAEiB,IAAI,EAAE,kBAAkB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAU,CAAE,EACrG;UAAEmB,EAAE,EAAE,iBAAiB;UAAE9B,KAAK,EAAE,iBAAiB;UAAEiB,IAAI,EAAE,aAAa;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAiB,CAAE,EACrH;UAAEmB,EAAE,EAAE,QAAQ;UAAE9B,KAAK,EAAE,eAAe;UAAEiB,IAAI,EAAE,iBAAiB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAQ,CAAE;OAExG;MACD+D,KAAK,EAAE;QACLV,UAAU,EAAE,OAAO;QACnBM,IAAI,EAAE,OAAO;QACbxD,KAAK,EAAE,SAAS;QAChBG,IAAI,EAAE,mBAAmB;QACzBsD,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,iBAAiB;UACrBR,KAAK,EAAE,iBAAiB;UACxBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,OAAO;YAAE2B,MAAM,EAAE,MAAM;YAAEF,KAAK,EAAE,IAAI;YAAExB,KAAK,EAAE;UAAY;SACzE,EACD;UACE8B,EAAE,EAAE,cAAc;UAClBR,KAAK,EAAE,cAAc;UACrBqC,IAAI,EAAE,UAAU;UAChBa,IAAI,EAAE,QAAQ;UACdjD,IAAI,EAAE;YAAEmB,UAAU,EAAE,EAAE;YAAEE,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE,GAAG;YAAE7C,KAAK,EAAE,gBAAgB;YAAEc,KAAK,EAAE;UAAS;SAC5F,EACD;UACEgB,EAAE,EAAE,gBAAgB;UACpBR,KAAK,EAAE,gBAAgB;UACvBqC,IAAI,EAAE,MAAM;UACZa,IAAI,EAAE,QAAQ;UACdjD,IAAI,EAAE;YACJkB,KAAK,EAAE,CACL;cAAEnB,KAAK,EAAE,cAAc;cAAEc,QAAQ,EAAE,eAAe;cAAEnB,IAAI,EAAE,eAAe;cAAEH,KAAK,EAAE,SAAS;cAAEuB,IAAI,EAAE,YAAY;cAAEC,MAAM,EAAE,KAAK;cAAEC,UAAU,EAAE;YAAK,CAAE,EACnJ;cAAEjB,KAAK,EAAE,aAAa;cAAEc,QAAQ,EAAE,eAAe;cAAEnB,IAAI,EAAE,cAAc;cAAEH,KAAK,EAAE,SAAS;cAAEuB,IAAI,EAAE,YAAY;cAAEC,MAAM,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAK,CAAE;;SAGzJ,CACF;QACDkC,YAAY,EAAE,CACZ;UAAE3C,EAAE,EAAE,UAAU;UAAE9B,KAAK,EAAE,UAAU;UAAEiB,IAAI,EAAE,kBAAkB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAU,CAAE,EACrG;UAAEmB,EAAE,EAAE,cAAc;UAAE9B,KAAK,EAAE,cAAc;UAAEiB,IAAI,EAAE,qBAAqB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAc,CAAE,EACpH;UAAEmB,EAAE,EAAE,eAAe;UAAE9B,KAAK,EAAE,gBAAgB;UAAEiB,IAAI,EAAE,sBAAsB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAe,CAAE;OAE5H;MACDgE,SAAS,EAAE;QACTX,UAAU,EAAE,WAAW;QACvBM,IAAI,EAAE,WAAW;QACjBxD,KAAK,EAAE,SAAS;QAChBG,IAAI,EAAE,iBAAiB;QACvBsD,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,gBAAgB;UACpBR,KAAK,EAAE,gBAAgB;UACvBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAE2B,MAAM,EAAE,MAAM;YAAEF,KAAK,EAAE,IAAI;YAAExB,KAAK,EAAE;UAAW;SACvE,EACD;UACE8B,EAAE,EAAE,iBAAiB;UACrBR,KAAK,EAAE,iBAAiB;UACxBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAE2B,MAAM,EAAE,OAAO;YAAEF,KAAK,EAAE,IAAI;YAAExB,KAAK,EAAE;UAAS;SACtE,EACD;UACE8B,EAAE,EAAE,kBAAkB;UACtBR,KAAK,EAAE,kBAAkB;UACzBqC,IAAI,EAAE,MAAM;UACZa,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YACJkB,KAAK,EAAE,CACL;cAAEnB,KAAK,EAAE,kBAAkB;cAAEc,QAAQ,EAAE,sBAAsB;cAAEnB,IAAI,EAAE,aAAa;cAAEH,KAAK,EAAE,SAAS;cAAEuB,IAAI,EAAE,aAAa;cAAEC,MAAM,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAQ,CAAE,EACnK;cAAEjB,KAAK,EAAE,uBAAuB;cAAEc,QAAQ,EAAE,gBAAgB;cAAEnB,IAAI,EAAE,eAAe;cAAEH,KAAK,EAAE,SAAS;cAAEuB,IAAI,EAAE,aAAa;cAAEC,MAAM,EAAE,WAAW;cAAEC,UAAU,EAAE;YAAW,CAAE;;SAG/K,CACF;QACDkC,YAAY,EAAE,CACZ;UAAE3C,EAAE,EAAE,iBAAiB;UAAE9B,KAAK,EAAE,iBAAiB;UAAEiB,IAAI,EAAE,aAAa;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAiB,CAAE,EACrH;UAAEmB,EAAE,EAAE,kBAAkB;UAAE9B,KAAK,EAAE,kBAAkB;UAAEiB,IAAI,EAAE,iBAAiB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAkB,CAAE,EAC5H;UAAEmB,EAAE,EAAE,WAAW;UAAE9B,KAAK,EAAE,WAAW;UAAEiB,IAAI,EAAE,kBAAkB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAW,CAAE;OAE3G;MACDiE,UAAU,EAAE;QACVZ,UAAU,EAAE,YAAY;QACxBM,IAAI,EAAE,YAAY;QAClBxD,KAAK,EAAE,SAAS;QAChBG,IAAI,EAAE,mBAAmB;QACzBsD,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,gBAAgB;UACpBR,KAAK,EAAE,gBAAgB;UACvBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,OAAO;YAAE2B,MAAM,EAAE,MAAM;YAAEF,KAAK,EAAE,IAAI;YAAExB,KAAK,EAAE;UAAY;SACzE,EACD;UACE8B,EAAE,EAAE,kBAAkB;UACtBR,KAAK,EAAE,kBAAkB;UACzBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,IAAI;YAAE2B,MAAM,EAAE,IAAI;YAAEF,KAAK,EAAE,MAAM;YAAExB,KAAK,EAAE;UAAa;SACvE,EACD;UACE8B,EAAE,EAAE,mBAAmB;UACvBR,KAAK,EAAE,mBAAmB;UAC1BqC,IAAI,EAAE,OAAO;UACba,IAAI,EAAE,QAAQ;UACdjD,IAAI,EAAE;YAAEsD,SAAS,EAAE,KAAK;YAAEC,UAAU,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO;UAAC;SACvF,CACF;QACDL,YAAY,EAAE,CACZ;UAAE3C,EAAE,EAAE,gBAAgB;UAAE9B,KAAK,EAAE,gBAAgB;UAAEiB,IAAI,EAAE,qBAAqB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAgB,CAAE,EAC1H;UAAEmB,EAAE,EAAE,gBAAgB;UAAE9B,KAAK,EAAE,gBAAgB;UAAEiB,IAAI,EAAE,gBAAgB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAgB,CAAE,EACrH;UAAEmB,EAAE,EAAE,mBAAmB;UAAE9B,KAAK,EAAE,mBAAmB;UAAEiB,IAAI,EAAE,kBAAkB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAmB,CAAE;OAEnI;MACDoE,OAAO,EAAE;QACPf,UAAU,EAAE,SAAS;QACrBM,IAAI,EAAE,kBAAkB;QACxBxD,KAAK,EAAE,SAAS;QAChBG,IAAI,EAAE,gBAAgB;QACtBsD,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,cAAc;UAClBR,KAAK,EAAE,cAAc;UACrBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,IAAI;YAAE2B,MAAM,EAAE,IAAI;YAAEF,KAAK,EAAE,MAAM;YAAExB,KAAK,EAAE;UAAgB;SAC1E,EACD;UACE8B,EAAE,EAAE,eAAe;UACnBR,KAAK,EAAE,mBAAmB;UAC1BqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAE2B,MAAM,EAAE,OAAO;YAAEF,KAAK,EAAE,MAAM;YAAExB,KAAK,EAAE;UAAW;SAC1E,EACD;UACE8B,EAAE,EAAE,gBAAgB;UACpBR,KAAK,EAAE,gBAAgB;UACvBqC,IAAI,EAAE,MAAM;UACZa,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YACJkB,KAAK,EAAE,CACL;cAAEnB,KAAK,EAAE,eAAe;cAAEc,QAAQ,EAAE,oBAAoB;cAAEnB,IAAI,EAAE,oBAAoB;cAAEH,KAAK,EAAE,SAAS;cAAEuB,IAAI,EAAE,WAAW;cAAEC,MAAM,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAQ,CAAE,EACnK;cAAEjB,KAAK,EAAE,kBAAkB;cAAEc,QAAQ,EAAE,sBAAsB;cAAEnB,IAAI,EAAE,wBAAwB;cAAEH,KAAK,EAAE,SAAS;cAAEuB,IAAI,EAAE,YAAY;cAAEC,MAAM,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAQ,CAAE;;SAGlL,CACF;QACDkC,YAAY,EAAE,CACZ;UAAE3C,EAAE,EAAE,eAAe;UAAE9B,KAAK,EAAE,eAAe;UAAEiB,IAAI,EAAE,mBAAmB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAe,CAAE,EACrH;UAAEmB,EAAE,EAAE,gBAAgB;UAAE9B,KAAK,EAAE,gBAAgB;UAAEiB,IAAI,EAAE,aAAa;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAgB,CAAE,EAClH;UAAEmB,EAAE,EAAE,mBAAmB;UAAE9B,KAAK,EAAE,mBAAmB;UAAEiB,IAAI,EAAE,iBAAiB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAmB,CAAE;OAElI;MACDqE,OAAO,EAAE;QACPhB,UAAU,EAAE,SAAS;QACrBM,IAAI,EAAE,oBAAoB;QAC1BxD,KAAK,EAAE,SAAS;QAChBG,IAAI,EAAE,aAAa;QACnBsD,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,mBAAmB;UACvBR,KAAK,EAAE,sBAAsB;UAC7BqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,KAAK;YAAE2B,MAAM,EAAE,KAAK;YAAEF,KAAK,EAAE,IAAI;YAAExB,KAAK,EAAE;UAAU;SACpE,EACD;UACE8B,EAAE,EAAE,qBAAqB;UACzBR,KAAK,EAAE,qBAAqB;UAC5BqC,IAAI,EAAE,OAAO;UACba,IAAI,EAAE,QAAQ;UACdjD,IAAI,EAAE;YAAEsD,SAAS,EAAE,MAAM;YAAEI,MAAM,EAAE;UAAY;SAChD,EACD;UACEnD,EAAE,EAAE,oBAAoB;UACxBR,KAAK,EAAE,oBAAoB;UAC3BqC,IAAI,EAAE,UAAU;UAChBa,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAE2D,MAAM,EAAE;UAAE;SACnB,CACF;QACDT,YAAY,EAAE,CACZ;UAAE3C,EAAE,EAAE,gBAAgB;UAAE9B,KAAK,EAAE,gBAAgB;UAAEiB,IAAI,EAAE,YAAY;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAgB,CAAE,EACjH;UAAEmB,EAAE,EAAE,eAAe;UAAE9B,KAAK,EAAE,eAAe;UAAEiB,IAAI,EAAE,cAAc;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAe,CAAE,EAChH;UAAEmB,EAAE,EAAE,mBAAmB;UAAE9B,KAAK,EAAE,mBAAmB;UAAEiB,IAAI,EAAE,mBAAmB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAmB,CAAE;OAEpI;MACDwE,iBAAiB,EAAE;QACjBnB,UAAU,EAAE,mBAAmB;QAC/BM,IAAI,EAAE,mBAAmB;QACzBxD,KAAK,EAAE,SAAS;QAChBG,IAAI,EAAE,cAAc;QACpBsD,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,gBAAgB;UACpBR,KAAK,EAAE,gBAAgB;UACvBqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,IAAI;YAAE2B,MAAM,EAAE,IAAI;YAAEF,KAAK,EAAE,IAAI;YAAExB,KAAK,EAAE;UAAU;SAClE,EACD;UACE8B,EAAE,EAAE,mBAAmB;UACvBR,KAAK,EAAE,mBAAmB;UAC1BqC,IAAI,EAAE,QAAQ;UACda,IAAI,EAAE,OAAO;UACbjD,IAAI,EAAE;YAAExB,KAAK,EAAE,IAAI;YAAE2B,MAAM,EAAE,GAAG;YAAEF,KAAK,EAAE,QAAQ;YAAExB,KAAK,EAAE;UAAgB;SAC3E,EACD;UACE8B,EAAE,EAAE,oBAAoB;UACxBR,KAAK,EAAE,oBAAoB;UAC3BqC,IAAI,EAAE,OAAO;UACba,IAAI,EAAE,QAAQ;UACdjD,IAAI,EAAE;YAAEsD,SAAS,EAAE,KAAK;YAAEI,MAAM,EAAE;UAAQ;SAC3C,CACF;QACDR,YAAY,EAAE,CACZ;UAAE3C,EAAE,EAAE,YAAY;UAAE9B,KAAK,EAAE,YAAY;UAAEiB,IAAI,EAAE,aAAa;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAY,CAAE,EACtG;UAAEmB,EAAE,EAAE,kBAAkB;UAAE9B,KAAK,EAAE,kBAAkB;UAAEiB,IAAI,EAAE,sBAAsB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAkB,CAAE,EACjI;UAAEmB,EAAE,EAAE,gBAAgB;UAAE9B,KAAK,EAAE,gBAAgB;UAAEiB,IAAI,EAAE,kBAAkB;UAAEH,KAAK,EAAE,SAAS;UAAEH,MAAM,EAAE;QAAgB,CAAE;;KAG5H;EAKE;EAEHyE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpB,UAAU,EAAE;MACnB,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACE,iBAAiB,CAAC,IAAI,CAACJ,UAAU,CAAC;;EAEnE;EAEAqB,WAAWA,CAAA;IACT,IAAI,CAAClB,QAAQ,CAACmB,IAAI,EAAE;IACpB,IAAI,CAACnB,QAAQ,CAACoB,QAAQ,EAAE;EAC1B;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACtB,gBAAgB,EAAE,OAAO,mDAAmD;IAEtF,MAAMpD,KAAK,GAAG,IAAI,CAACoD,gBAAgB,CAACpD,KAAK;IACzC,OAAO,2BAA2BA,KAAK,UAAUA,KAAK,UAAU;EAClE;EAEA2E,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACxB,QAAQ,EAAE,OAAO,EAAE;IAC7B,OAAO,IAAI,CAACH,qBAAqB,CAAC4B,aAAa,CAAC,IAAI,CAACzB,QAAQ,CAAC,CAAC0B,WAAW;EAC5E;EAEAC,iBAAiBA,CAAA;IACf,MAAMC,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB;EAEAG,cAAcA,CAAA;IACZ;IACA,OAAO,CACL;MAAEjG,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAa,CAAE,EACtC;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAc,CAAE,EACtC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAY,CAAE,CACvC;EACH;EAEAoB,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC8C,gBAAgB,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE,OAAO,EAAE;IAEvD,OAAO,IAAI,CAACC,gBAAgB,CAACO,YAAY,CAACwB,MAAM,CAACtF,MAAM,IAAG;MACxD,IAAI,CAACA,MAAM,CAACuF,UAAU,EAAE,OAAO,IAAI;MACnC,MAAM,CAACC,MAAM,EAAEC,UAAU,CAAC,GAAGzF,MAAM,CAACuF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC;MACzD,OAAO,IAAI,CAACtC,2BAA2B,CAACuC,aAAa,CAAC,IAAI,CAACrC,QAAS,EAAEkC,MAAM,EAAEC,UAAU,CAAC;IAC3F,CAAC,CAAC;EACJ;EAEAG,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACrC,gBAAgB,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE,OAAO,EAAE;IAEvD,OAAO,IAAI,CAACC,gBAAgB,CAACK,OAAO,CAAC0B,MAAM,CAACO,MAAM,IAAG;MACnD,IAAI,CAACA,MAAM,CAACN,UAAU,EAAE,OAAO,IAAI;MACnC,MAAM,CAACC,MAAM,EAAExF,MAAM,CAAC,GAAG6F,MAAM,CAACN,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC;MACrD,OAAO,IAAI,CAACtC,2BAA2B,CAACuC,aAAa,CAAC,IAAI,CAACrC,QAAS,EAAEkC,MAAM,EAAExF,MAAM,CAAC;IACvF,CAAC,CAAC;EACJ;EAEAD,iBAAiBA,CAACC,MAAc;IAC9B8F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE/F,MAAM,CAAC;IAC9C;EACF;EAEAwB,mBAAmBA,CAACwE,IAAS;IAC3BF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,IAAI,CAAC;IACvC;EACF;EAEA9E,aAAaA,CAAC+E,QAAgB;IAC5BH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;IAC3C;EACF;EAEAzD,cAAcA,CAAC0D,KAAU;IACvBJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,KAAK,CAAChE,MAAM,CAAC9C,KAAK,CAAC;IACtD;EACF;EAEA0B,YAAYA,CAACD,KAAa;IACxB,QAAQA,KAAK;MACX,KAAK,IAAI;QAAE,OAAO,iBAAiB;MACnC,KAAK,MAAM;QAAE,OAAO,mBAAmB;MACvC;QAAS,OAAO,cAAc;;EAElC;;;uBAvWWoC,4BAA4B,EAAApE,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAxH,EAAA,CAAAsH,iBAAA,CAAAG,EAAA,CAAAC,2BAAA;IAAA;EAAA;;;YAA5BtD,4BAA4B;MAAAuD,SAAA;MAAAC,MAAA;QAAApD,UAAA;QAAAC,QAAA;MAAA;MAAAoD,UAAA;MAAAC,QAAA,GAAA9H,EAAA,CAAA+H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7J/BrI,EAJN,CAAAC,cAAA,aAAsE,aAEQ,aAC9C,aACG;UAC3BD,EAAA,CAAAoB,SAAA,QAAwC;UAC1CpB,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAE,MAAA,GAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,GAAsD;UAC3DF,EAD2D,CAAAG,YAAA,EAAI,EACzD;UACNH,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAA0B,UAAA,KAAA6G,4CAAA,iBAA6D;UAMnEvI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAGNH,EAAA,CAAA0B,UAAA,KAAA8G,4CAAA,iBAAyE;UAiBvExI,EADF,CAAAC,cAAA,cAA+B,cACH;UACxBD,EAAA,CAAA0B,UAAA,KAAA+G,4CAAA,kBAIiC;UAkHvCzI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;UA3J2BH,EAAA,CAAAI,SAAA,EAA4C;UAA5CJ,EAAA,CAAAqB,WAAA,eAAAiH,GAAA,CAAAtC,qBAAA,GAA4C;UAGlEhG,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAwB,UAAA,CAAA8G,GAAA,CAAA5D,gBAAA,kBAAA4D,GAAA,CAAA5D,gBAAA,CAAAjD,IAAA,CAAgC;UAG/BzB,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAuC,kBAAA,KAAA+F,GAAA,CAAA5D,gBAAA,kBAAA4D,GAAA,CAAA5D,gBAAA,CAAAI,IAAA,eAAsC;UACvC9E,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAmD,kBAAA,KAAAmF,GAAA,CAAArC,kBAAA,WAAAqC,GAAA,CAAAlC,iBAAA,OAAsD;UAGjBpG,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAuB,UAAA,YAAA+G,GAAA,CAAA9B,cAAA,GAAmB;UASrCxG,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAuB,UAAA,SAAA+G,GAAA,CAAA1G,wBAAA,GAAA8G,MAAA,KAA2C;UAmB9C1I,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAuB,UAAA,YAAA+G,GAAA,CAAAvB,mBAAA,GAAwB;;;qBA1C3CjH,YAAY,EAAA6I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}