.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;

  &.layout-horizontal {
    flex-direction: row;
  }

  &.layout-vertical {
    flex-direction: column;
    align-items: stretch;
  }

  &.layout-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
  }

  &.size-small {
    gap: 4px;
    
    .action-btn {
      --min-height: 32px;
      font-size: 12px;
      
      ion-icon {
        font-size: 16px;
      }
    }
  }

  &.size-large {
    gap: 12px;
    
    .action-btn {
      --min-height: 48px;
      font-size: 16px;
      
      ion-icon {
        font-size: 20px;
      }
    }
  }
}

.action-btn {
  --min-height: 40px;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  ion-icon {
    margin-right: 4px;
    transition: all 0.3s ease;
  }

  ion-spinner {
    margin-left: 4px;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  &[disabled] {
    opacity: 0.6;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Cart Button Styles
.cart-btn {
  --color: white;
  --background: linear-gradient(135deg, #667eea, #764ba2);
  
  &:hover {
    --background: linear-gradient(135deg, #5a6fd8, #6a4190);
  }

  &.in-cart {
    --background: linear-gradient(135deg, #4caf50, #45a049);
    
    ion-icon {
      animation: bounce 0.6s ease;
    }
  }
}

// Wishlist Button Styles
.wishlist-btn {
  --color: #e91e63;
  --border-color: #e91e63;
  
  &:hover {
    --background: rgba(233, 30, 99, 0.1);
  }

  &.in-wishlist {
    --color: white;
    --background: linear-gradient(135deg, #e91e63, #c2185b);
    
    ion-icon {
      animation: heartBeat 0.6s ease;
    }
  }
}

// Buy Now Button Styles
.buy-btn {
  --color: white;
  --background: linear-gradient(135deg, #4caf50, #45a049);
  
  &:hover {
    --background: linear-gradient(135deg, #45a049, #3d8b40);
  }

  ion-icon {
    animation: flash 1s infinite;
  }
}

// Like Button Styles
.like-btn {
  --color: #e91e63;
  
  &:hover {
    --background: rgba(233, 30, 99, 0.1);
  }

  &.liked {
    --color: #e91e63;
    
    ion-icon {
      animation: heartBeat 0.6s ease;
      color: #e91e63;
    }
  }
}

// Comment Button Styles
.comment-btn {
  --color: #2196f3;
  
  &:hover {
    --background: rgba(33, 150, 243, 0.1);
  }
}

// Share Button Styles
.share-btn {
  --color: #ff9800;
  
  &:hover {
    --background: rgba(255, 152, 0, 0.1);
  }
}

// Save Button Styles
.save-btn {
  --color: #9c27b0;
  
  &:hover {
    --background: rgba(156, 39, 176, 0.1);
  }

  &.saved {
    --color: #9c27b0;
    
    ion-icon {
      animation: bounce 0.6s ease;
      color: #9c27b0;
    }
  }
}

// Animations
@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(-5px);
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes flash {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .action-buttons {
    &.layout-horizontal {
      flex-wrap: wrap;
    }

    &.layout-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
  }

  .action-btn {
    --min-height: 36px;
    --padding-start: 8px;
    --padding-end: 8px;
    font-size: 13px;

    ion-icon {
      font-size: 16px;
    }
  }
}

@media (max-width: 480px) {
  .action-buttons {
    gap: 6px;

    &.layout-horizontal {
      justify-content: center;
    }
  }

  .action-btn {
    --min-height: 32px;
    font-size: 12px;
    
    span {
      display: none;
    }

    ion-icon {
      margin-right: 0;
      font-size: 18px;
    }
  }

  .action-buttons.size-large .action-btn span {
    display: inline;
  }
}

// Loading State
.action-btn ion-spinner {
  --color: currentColor;
  width: 16px;
  height: 16px;
}

// Accessibility
.action-btn:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

// High Contrast Mode
@media (prefers-contrast: high) {
  .action-btn {
    --border-width: 2px;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  .action-btn,
  .action-btn ion-icon {
    transition: none;
    animation: none;
  }

  .action-btn:hover {
    transform: none;
  }
}
