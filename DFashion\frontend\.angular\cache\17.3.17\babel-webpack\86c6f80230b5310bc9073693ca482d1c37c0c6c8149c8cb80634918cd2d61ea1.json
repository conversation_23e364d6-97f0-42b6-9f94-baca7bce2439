{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction QuickLinksComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"h2\", 9);\n    i0.ɵɵelement(3, \"i\", 10);\n    i0.ɵɵtext(4, \" Quick Links \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 11);\n    i0.ɵɵtext(6, \"Shop by category - fast and easy\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuickLinksComponent_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const link_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(link_r2.description);\n  }\n}\nfunction QuickLinksComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_3_Template_div_click_0_listener() {\n      const link_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToLink(link_r2));\n    })(\"keydown.enter\", function QuickLinksComponent_div_3_Template_div_keydown_enter_0_listener() {\n      const link_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToLink(link_r2));\n    })(\"keydown.space\", function QuickLinksComponent_div_3_Template_div_keydown_space_0_listener() {\n      const link_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToLink(link_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"h3\", 15);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, QuickLinksComponent_div_3_p_6_Template, 2, 1, \"p\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 17);\n    i0.ɵɵelement(8, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const link_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background\", ctx_r2.getCardBackground(link_r2.color));\n    i0.ɵɵattribute(\"aria-label\", \"Navigate to \" + link_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", link_r2.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(link_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(link_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isCompact);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background\", link_r2.color);\n  }\n}\nfunction QuickLinksComponent_div_4_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_4_button_4_Template_button_click_0_listener() {\n      const category_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToCategory(category_r5.slug));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-color\", category_r5.color)(\"color\", category_r5.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(category_r5.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", category_r5.productCount, \")\");\n  }\n}\nfunction QuickLinksComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h3\", 22);\n    i0.ɵɵtext(2, \"Popular Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵtemplate(4, QuickLinksComponent_div_4_button_4_Template, 6, 8, \"button\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.popularCategories);\n  }\n}\nfunction QuickLinksComponent_div_5_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_5_button_4_Template_button_click_0_listener() {\n      const action_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.performAction(action_r7.action));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r7 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background\", action_r7.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r7.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r7.label);\n  }\n}\nfunction QuickLinksComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h3\", 28);\n    i0.ɵɵtext(2, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtemplate(4, QuickLinksComponent_div_5_button_4_Template, 4, 5, \"button\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.quickActions);\n  }\n}\nfunction QuickLinksComponent_div_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_6_div_4_Template_div_click_0_listener() {\n      const collection_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToCollection(collection_r9.slug));\n    });\n    i0.ɵɵelementStart(1, \"div\", 37);\n    i0.ɵɵelement(2, \"img\", 38);\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"div\", 40)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const collection_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", collection_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", collection_r9.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(collection_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", collection_r9.itemCount, \" items\");\n  }\n}\nfunction QuickLinksComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"h3\", 33);\n    i0.ɵɵtext(2, \"Featured Collections\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, QuickLinksComponent_div_6_div_4_Template, 9, 4, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.featuredCollections);\n  }\n}\nexport let QuickLinksComponent = /*#__PURE__*/(() => {\n  class QuickLinksComponent {\n    constructor(shopDataService, router) {\n      this.shopDataService = shopDataService;\n      this.router = router;\n      this.showHeader = true;\n      this.isCompact = false;\n      this.maxLinks = 8;\n      this.showPopularCategories = true;\n      this.showQuickActions = true;\n      this.showFeaturedCollections = true;\n      this.quickLinks = [];\n      this.popularCategories = [];\n      this.quickActions = [];\n      this.featuredCollections = [];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.loadQuickLinks();\n      this.initializePopularCategories();\n      this.initializeQuickActions();\n      this.initializeFeaturedCollections();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadQuickLinks() {\n      this.shopDataService.quickLinks$.pipe(takeUntil(this.destroy$)).subscribe(links => {\n        this.quickLinks = links.filter(link => link.featured).sort((a, b) => a.order - b.order).slice(0, this.maxLinks);\n      });\n    }\n    navigateToLink(link) {\n      this.trackLinkClick(link);\n      this.router.navigate([link.route]);\n    }\n    navigateToCategory(categorySlug) {\n      this.router.navigate(['/shop/category', categorySlug]);\n    }\n    navigateToCollection(collectionSlug) {\n      this.router.navigate(['/shop/collection', collectionSlug]);\n    }\n    performAction(action) {\n      switch (action) {\n        case 'search':\n          this.router.navigate(['/search']);\n          break;\n        case 'wishlist':\n          this.router.navigate(['/wishlist']);\n          break;\n        case 'cart':\n          this.router.navigate(['/cart']);\n          break;\n        case 'orders':\n          this.router.navigate(['/orders']);\n          break;\n        case 'offers':\n          this.router.navigate(['/offers']);\n          break;\n        case 'support':\n          this.router.navigate(['/support']);\n          break;\n        default:\n          console.log('Action not implemented:', action);\n      }\n    }\n    getCardBackground(color) {\n      return `linear-gradient(135deg, ${color}10 0%, ${color}20 100%)`;\n    }\n    trackByLinkId(index, link) {\n      return link.id;\n    }\n    initializePopularCategories() {\n      this.popularCategories = [{\n        name: 'Men\\'s Fashion',\n        slug: 'men',\n        icon: 'fas fa-male',\n        color: '#3498db',\n        productCount: 1250\n      }, {\n        name: 'Women\\'s Fashion',\n        slug: 'women',\n        icon: 'fas fa-female',\n        color: '#e91e63',\n        productCount: 1890\n      }, {\n        name: 'Footwear',\n        slug: 'footwear',\n        icon: 'fas fa-shoe-prints',\n        color: '#ff9800',\n        productCount: 567\n      }, {\n        name: 'Accessories',\n        slug: 'accessories',\n        icon: 'fas fa-gem',\n        color: '#9c27b0',\n        productCount: 423\n      }, {\n        name: 'Ethnic Wear',\n        slug: 'ethnic',\n        icon: 'fas fa-star-and-crescent',\n        color: '#ff5722',\n        productCount: 789\n      }, {\n        name: 'Sports & Fitness',\n        slug: 'sports',\n        icon: 'fas fa-dumbbell',\n        color: '#4caf50',\n        productCount: 345\n      }];\n    }\n    initializeQuickActions() {\n      this.quickActions = [{\n        label: 'Search',\n        icon: 'fas fa-search',\n        action: 'search',\n        color: '#667eea'\n      }, {\n        label: 'Wishlist',\n        icon: 'fas fa-heart',\n        action: 'wishlist',\n        color: '#e91e63'\n      }, {\n        label: 'Cart',\n        icon: 'fas fa-shopping-cart',\n        action: 'cart',\n        color: '#4caf50'\n      }, {\n        label: 'Orders',\n        icon: 'fas fa-box',\n        action: 'orders',\n        color: '#ff9800'\n      }, {\n        label: 'Offers',\n        icon: 'fas fa-tags',\n        action: 'offers',\n        color: '#f44336'\n      }, {\n        label: 'Support',\n        icon: 'fas fa-headset',\n        action: 'support',\n        color: '#00bcd4'\n      }];\n    }\n    initializeFeaturedCollections() {\n      this.featuredCollections = [{\n        name: 'Summer Collection',\n        slug: 'summer-2024',\n        image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=300&h=200&fit=crop',\n        itemCount: 156\n      }, {\n        name: 'Ethnic Elegance',\n        slug: 'ethnic-elegance',\n        image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300&h=200&fit=crop',\n        itemCount: 89\n      }, {\n        name: 'Casual Comfort',\n        slug: 'casual-comfort',\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=200&fit=crop',\n        itemCount: 234\n      }, {\n        name: 'Formal Wear',\n        slug: 'formal-wear',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',\n        itemCount: 123\n      }];\n    }\n    trackLinkClick(link) {\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'quick_link_click', {\n          link_title: link.title,\n          link_id: link.id,\n          link_category: link.category || 'general',\n          event_category: 'navigation'\n        });\n      }\n      // Also track in console for development\n      console.log('Quick link clicked:', link.title, '→', link.route);\n    }\n    static {\n      this.ɵfac = function QuickLinksComponent_Factory(t) {\n        return new (t || QuickLinksComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: QuickLinksComponent,\n        selectors: [[\"app-quick-links\"]],\n        inputs: {\n          showHeader: \"showHeader\",\n          isCompact: \"isCompact\",\n          maxLinks: \"maxLinks\",\n          showPopularCategories: \"showPopularCategories\",\n          showQuickActions: \"showQuickActions\",\n          showFeaturedCollections: \"showFeaturedCollections\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 7,\n        vars: 8,\n        consts: [[1, \"quick-links-section\"], [\"class\", \"section-header\", 4, \"ngIf\"], [1, \"quick-links-grid\"], [\"class\", \"quick-link-card\", \"tabindex\", \"0\", 3, \"background\", \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"popular-categories\", 4, \"ngIf\"], [\"class\", \"quick-actions\", 4, \"ngIf\"], [\"class\", \"featured-collections\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-bolt\"], [1, \"section-subtitle\"], [\"tabindex\", \"0\", 1, \"quick-link-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"link-icon\"], [1, \"link-content\"], [1, \"link-title\"], [\"class\", \"link-description\", 4, \"ngIf\"], [1, \"link-arrow\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"hover-effect\"], [1, \"link-description\"], [1, \"popular-categories\"], [1, \"categories-title\"], [1, \"categories-list\"], [\"class\", \"category-chip\", 3, \"border-color\", \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-chip\", 3, \"click\"], [1, \"product-count\"], [1, \"quick-actions\"], [1, \"actions-title\"], [1, \"actions-grid\"], [\"class\", \"action-btn\", 3, \"background\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-btn\", 3, \"click\"], [1, \"featured-collections\"], [1, \"collections-title\"], [1, \"collections-scroll\"], [\"class\", \"collection-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"collection-card\", 3, \"click\"], [1, \"collection-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"collection-overlay\"], [1, \"collection-info\"]],\n        template: function QuickLinksComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, QuickLinksComponent_div_1_Template, 7, 0, \"div\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2);\n            i0.ɵɵtemplate(3, QuickLinksComponent_div_3_Template, 10, 11, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, QuickLinksComponent_div_4_Template, 5, 1, \"div\", 4)(5, QuickLinksComponent_div_5_Template, 5, 1, \"div\", 5)(6, QuickLinksComponent_div_6_Template, 5, 1, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"compact\", ctx.isCompact);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.quickLinks)(\"ngForTrackBy\", ctx.trackByLinkId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showPopularCategories);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showQuickActions);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showFeaturedCollections);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n        styles: [\".quick-links-section[_ngcontent-%COMP%]{padding:2rem 0;background:#fff}.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem;padding:0 1rem}.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#262626;margin:0 0 .5rem;display:flex;align-items:center;justify-content:center;gap:.5rem}.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107;font-size:1.8rem}@media (max-width: 768px){.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.5rem}}.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{color:#8e8e8e;font-size:1rem;margin:0}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:1rem;padding:0 1rem;margin-bottom:2rem}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:.75rem}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]{padding:1rem}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{width:40px;height:40px;font-size:1.2rem}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-title[_ngcontent-%COMP%]{font-size:.9rem}@media (max-width: 768px){.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:.75rem}}@media (max-width: 480px){.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 1fr;gap:.5rem}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]{min-height:auto}}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]{position:relative;padding:1.5rem;border-radius:16px;cursor:pointer;transition:all .3s ease;overflow:hidden;border:1px solid rgba(0,0,0,.05);min-height:120px;display:flex;align-items:center;gap:1rem}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #0000001a}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .link-arrow[_ngcontent-%COMP%]{transform:translate(5px)}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .hover-effect[_ngcontent-%COMP%]{opacity:.1;transform:scale(1.1)}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .link-icon[_ngcontent-%COMP%]{transform:scale(1.1)}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:focus{outline:3px solid rgba(102,126,234,.3);outline-offset:2px}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:12px;display:flex;align-items:center;justify-content:center;font-size:1.5rem;background:#fffc;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);transition:all .3s ease;flex-shrink:0}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]{flex:1}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-title[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;color:#262626;margin:0 0 .25rem;line-height:1.2}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-description[_ngcontent-%COMP%]{font-size:.85rem;color:#8e8e8e;margin:0;line-height:1.3}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-arrow[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.9rem;transition:all .3s ease;flex-shrink:0}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .hover-effect[_ngcontent-%COMP%]{position:absolute;inset:0;opacity:0;transition:all .3s ease;border-radius:16px}.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]{margin-bottom:2rem;padding:0 1rem}.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;color:#262626;margin:0 0 1rem;text-align:center}.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.75rem;justify-content:center}.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem;background:#fff;border:2px solid;border-radius:25px;cursor:pointer;transition:all .3s ease;font-weight:500;font-size:.85rem}.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]:hover{background:currentColor;color:#fff!important;transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{font-size:.75rem;opacity:.8}.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{margin-bottom:2rem;padding:0 1rem}.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;color:#262626;margin:0 0 1rem;text-align:center}.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:1rem}@media (max-width: 768px){.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr)}}@media (max-width: 480px){.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;padding:1rem;border:none;border-radius:12px;color:#fff;cursor:pointer;transition:all .3s ease;font-weight:600;box-shadow:0 2px 8px #0000001a}.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 6px 20px #0003}.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.85rem}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]{padding:0 1rem}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-title[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;color:#262626;margin:0 0 1rem;text-align:center}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]{display:flex;gap:1rem;overflow-x:auto;padding-bottom:1rem}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar{height:6px}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a1a1a1}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]{min-width:200px;height:150px;border-radius:12px;overflow:hidden;cursor:pointer;transition:all .3s ease;position:relative}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 8px 25px #00000026}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]:hover   .collection-overlay[_ngcontent-%COMP%]{background:#0009}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]{width:100%;height:100%;position:relative}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#0006;display:flex;align-items:flex-end;padding:1rem;transition:all .3s ease}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]{color:#fff}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:1rem;font-weight:600}.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.8rem;opacity:.9}@media (prefers-color-scheme: dark){.quick-links-section[_ngcontent-%COMP%]{background:#121212}.quick-links-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .collections-title[_ngcontent-%COMP%]{color:#fff}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e1e1e,#2a2a2a);border-color:#333}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-title[_ngcontent-%COMP%]{color:#fff}.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%]{background:#ffffff1a}.quick-links-section[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]{background:#1e1e1e}.quick-links-section[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]:hover{background:currentColor!important}.quick-links-section[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{box-shadow:0 2px 8px #0000004d}.quick-links-section[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{box-shadow:0 6px 20px #0006}}@media (max-width: 480px){.quick-links-section[_ngcontent-%COMP%]{padding:1rem 0}.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-bottom:1rem}.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.3rem}.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:.9rem}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]{margin-bottom:1.5rem}.quick-links-section[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .collections-title[_ngcontent-%COMP%]{font-size:1.1rem}}\"]\n      });\n    }\n  }\n  return QuickLinksComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}