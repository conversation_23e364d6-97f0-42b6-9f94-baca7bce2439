{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  constructor() {}\n  ngOnInit() {\n    // Home component initialization\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 0,\n      consts: [[1, \"home-container\"], [1, \"content-grid\"], [1, \"main-content\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-view-add-stories\")(4, \"app-feed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"app-sidebar\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [CommonModule, ViewAddStoriesComponent, FeedComponent, SidebarComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n  display: flex;\\n  flex: 1;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 600px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 20px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9wYWdlcy9ob21lL2hvbWUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFHSSxhQUFBO0VBQ0YsYUFBQTtFQUNBLHNCQUFBO0FBREY7O0FBSUE7RUFDRSxhQUFBO0VBQ0EsZ0NBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxPQUFBO0VBQ0EsZ0JBQUE7QUFERjs7QUFJQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7QUFERjs7QUFJQTtFQUNFO0lBQ0UsMEJBQUE7SUFDQSxnQkFBQTtFQURGO0FBQ0Y7QUFJQTtFQUNFO0lBQ0UsZUFBQTtFQUZGO0VBS0E7SUFDRSxlQUFBO0lBQ0EsU0FBQTtFQUhGO0VBTUE7SUFDRSxTQUFBO0VBSkY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5ob21lLWNvbnRhaW5lciB7XG4gIC8vIHBhZGRpbmc6IDIwcHggMDtcbiAgLy8gbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDYwcHgpO1xuICAgIGhlaWdodDogMTAwdmg7IC8vIGZ1bGwgc2NyZWVuIGhlaWdodFxuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xufVxuXG4uY29udGVudC1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgNDAwcHg7XG4gIGdhcDogNDBweDtcbiAgbWF4LXdpZHRoOiAxMDAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBwYWRkaW5nOiAwIDIwcHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXg6IDE7XG4gIG92ZXJmbG93OiBoaWRkZW47IC8vIHByZXZlbnQgZ3JpZCBvdmVyZmxvd1xufVxuXG4ubWFpbi1jb250ZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAyNHB4O1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogMTAyNHB4KSB7XG4gIC5jb250ZW50LWdyaWQge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgIG1heC13aWR0aDogNjAwcHg7XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5ob21lLWNvbnRhaW5lciB7XG4gICAgcGFkZGluZzogMTZweCAwO1xuICB9XG5cbiAgLmNvbnRlbnQtZ3JpZCB7XG4gICAgcGFkZGluZzogMCAxNnB4O1xuICAgIGdhcDogMjBweDtcbiAgfVxuXG4gIC5tYWluLWNvbnRlbnQge1xuICAgIGdhcDogMjBweDtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ViewAddStoriesComponent", "FeedComponent", "SidebarComponent", "HomeComponent", "constructor", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, ViewAddStoriesComponent, FeedComponent, SidebarComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  constructor() {}\n\n  ngOnInit() {\n    // Home component initialization\n  }\n}\n", "<div class=\"home-container\">\n  <div class=\"content-grid\">\n    <!-- Main Feed -->\n    <div class=\"main-content\">\n      <!-- Instagram-style Stories -->\n      <app-view-add-stories></app-view-add-stories>\n\n      <!-- Instagram-style Feed with Post<PERSON> and Reels -->\n      <app-feed></app-feed>\n    </div>\n\n    <!-- Sidebar -->\n    <app-sidebar></app-sidebar>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;;AAS7E,OAAM,MAAOC,aAAa;EACxBC,YAAA,GAAe;EAEfC,QAAQA,CAAA;IACN;EAAA;;;uBAJSF,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXtBP,EAHJ,CAAAS,cAAA,aAA4B,aACA,aAEE;UAKxBT,EAHA,CAAAU,SAAA,2BAA6C,eAGxB;UACvBV,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAU,SAAA,kBAA2B;UAE/BV,EADE,CAAAW,YAAA,EAAM,EACF;;;qBDJMrB,YAAY,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,gBAAgB;MAAAmB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}