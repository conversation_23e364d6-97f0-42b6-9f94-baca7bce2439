{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { MobileBottomNavComponent } from '../mobile-bottom-nav/mobile-bottom-nav.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/mobile-optimization.service\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"../../../core/services/cart.service\";\nimport * as i4 from \"../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/forms\";\nconst _c0 = [\"*\"];\nfunction MobileLayoutComponent_header_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n}\nfunction MobileLayoutComponent_header_1_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 50);\n  }\n}\nfunction MobileLayoutComponent_header_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.cartCount));\n  }\n}\nfunction MobileLayoutComponent_header_1_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"header\", 29)(1, \"div\", 30)(2, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleMenu());\n    });\n    i0.ɵɵtemplate(3, MobileLayoutComponent_header_1_i_3_Template, 1, 0, \"i\", 32)(4, MobileLayoutComponent_header_1_i_4_Template, 1, 0, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 34);\n    i0.ɵɵelement(6, \"img\", 35);\n    i0.ɵɵelementStart(7, \"span\", 36);\n    i0.ɵɵtext(8, \"DFashion\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 37)(10, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSearch());\n    });\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 40);\n    i0.ɵɵelement(13, \"i\", 41);\n    i0.ɵɵtemplate(14, MobileLayoutComponent_header_1_span_14_Template, 2, 1, \"span\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 43);\n    i0.ɵɵelement(16, \"i\", 44);\n    i0.ɵɵtemplate(17, MobileLayoutComponent_header_1_span_17_Template, 2, 1, \"span\", 42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 45)(19, \"div\", 46)(20, \"input\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function MobileLayoutComponent_header_1_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function MobileLayoutComponent_header_1_Template_input_keyup_enter_20_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearchSubmit());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearchSubmit());\n    });\n    i0.ɵɵelement(22, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSearch());\n    });\n    i0.ɵɵelement(24, \"i\", 50);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isMenuOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMenuOpen);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartCount > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSearchOpen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n  }\n}\nfunction MobileLayoutComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentUser.avatar || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.email);\n  }\n}\nfunction MobileLayoutComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵelement(2, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 60)(4, \"h3\");\n    i0.ɵɵtext(5, \"Welcome to DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Sign in for personalized experience\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 61)(9, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_6_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵtext(10, \"Sign In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_6_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵtext(12, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MobileLayoutComponent_div_28_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_div_28_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.cartCount));\n  }\n}\nfunction MobileLayoutComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementStart(2, \"a\", 66);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(3, \"i\", 59);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"My Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 67);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(7, \"i\", 68);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"My Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(11, \"i\", 44);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MobileLayoutComponent_div_28_span_14_Template, 2, 1, \"span\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"a\", 71);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(16, \"i\", 41);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, MobileLayoutComponent_div_28_span_19_Template, 2, 1, \"span\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"a\", 72);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(21, \"i\", 73);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(24, \"div\", 65);\n    i0.ɵɵelementStart(25, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelement(26, \"i\", 75);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Logout\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartCount > 0);\n  }\n}\nfunction MobileLayoutComponent_app_mobile_bottom_nav_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-mobile-bottom-nav\");\n  }\n}\nfunction MobileLayoutComponent_footer_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"footer\", 77)(1, \"div\", 78)(2, \"div\", 79)(3, \"a\", 80);\n    i0.ɵɵtext(4, \"About\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"a\", 81);\n    i0.ɵɵtext(6, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 82);\n    i0.ɵɵtext(8, \"Privacy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 83);\n    i0.ɵɵtext(10, \"Terms\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 84)(12, \"p\");\n    i0.ɵɵtext(13, \"\\u00A9 2024 DFashion. All rights reserved.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport let MobileLayoutComponent = /*#__PURE__*/(() => {\n  class MobileLayoutComponent {\n    constructor(mobileService, authService, cartService, wishlistService) {\n      this.mobileService = mobileService;\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.showHeader = true;\n      this.showFooter = true;\n      this.showBottomNav = true;\n      this.menuToggle = new EventEmitter();\n      this.deviceInfo = null;\n      this.breakpoints = null;\n      this.isKeyboardOpen = false;\n      this.currentUser = null;\n      this.cartCount = 0;\n      this.wishlistCount = 0;\n      this.isMenuOpen = false;\n      this.isSearchOpen = false;\n      this.searchQuery = '';\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      // Subscribe to device info\n      this.subscriptions.push(this.mobileService.getDeviceInfo$().subscribe(info => {\n        this.deviceInfo = info;\n        this.updateLayoutForDevice();\n      }));\n      // Subscribe to viewport breakpoints\n      this.subscriptions.push(this.mobileService.getViewportBreakpoints$().subscribe(breakpoints => {\n        this.breakpoints = breakpoints;\n        this.updateLayoutForBreakpoint();\n      }));\n      // Subscribe to keyboard state\n      this.subscriptions.push(this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n        this.isKeyboardOpen = isOpen;\n        this.handleKeyboardState(isOpen);\n      }));\n      // Subscribe to auth state\n      this.subscriptions.push(this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        if (user) {\n          this.loadUserCounts();\n        } else {\n          this.cartCount = 0;\n          this.wishlistCount = 0;\n        }\n      }));\n      // Load initial counts\n      this.loadUserCounts();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    updateLayoutForDevice() {\n      if (!this.deviceInfo) return;\n      // Apply device-specific optimizations\n      if (this.deviceInfo.isMobile) {\n        this.enableMobileOptimizations();\n      } else {\n        this.disableMobileOptimizations();\n      }\n      // Handle orientation changes\n      if (this.deviceInfo.orientation === 'landscape' && this.deviceInfo.isMobile) {\n        this.handleLandscapeMode();\n      } else {\n        this.handlePortraitMode();\n      }\n    }\n    updateLayoutForBreakpoint() {\n      if (!this.breakpoints) return;\n      // Adjust layout based on breakpoints\n      if (this.breakpoints.xs || this.breakpoints.sm) {\n        this.showBottomNav = true;\n        this.enableCompactMode();\n      } else {\n        this.showBottomNav = false;\n        this.disableCompactMode();\n      }\n    }\n    handleKeyboardState(isOpen) {\n      if (isOpen) {\n        // Hide bottom navigation when keyboard is open\n        document.body.classList.add('keyboard-open');\n      } else {\n        document.body.classList.remove('keyboard-open');\n      }\n    }\n    enableMobileOptimizations() {\n      // Enable touch-friendly interactions\n      document.body.classList.add('mobile-device');\n      // Disable hover effects on mobile\n      if (!this.mobileService.supportsHover()) {\n        document.body.classList.add('no-hover');\n      }\n      // Enable GPU acceleration for smooth scrolling\n      const scrollElements = document.querySelectorAll('.scroll-container');\n      scrollElements.forEach(element => {\n        this.mobileService.enableGPUAcceleration(element);\n      });\n    }\n    disableMobileOptimizations() {\n      document.body.classList.remove('mobile-device', 'no-hover');\n    }\n    handleLandscapeMode() {\n      document.body.classList.add('landscape-mode');\n    }\n    handlePortraitMode() {\n      document.body.classList.remove('landscape-mode');\n    }\n    enableCompactMode() {\n      document.body.classList.add('compact-mode');\n    }\n    disableCompactMode() {\n      document.body.classList.remove('compact-mode');\n    }\n    loadUserCounts() {\n      if (!this.currentUser) return;\n      // Set default counts for now\n      this.cartCount = 0;\n      this.wishlistCount = 0;\n    }\n    // Menu Methods\n    toggleMenu() {\n      this.isMenuOpen = !this.isMenuOpen;\n      this.menuToggle.emit(this.isMenuOpen);\n      if (this.isMenuOpen) {\n        this.mobileService.disableBodyScroll();\n      } else {\n        this.mobileService.enableBodyScroll();\n      }\n    }\n    closeMenu() {\n      this.isMenuOpen = false;\n      this.menuToggle.emit(false);\n      this.mobileService.enableBodyScroll();\n    }\n    // Search Methods\n    toggleSearch() {\n      this.isSearchOpen = !this.isSearchOpen;\n      if (this.isSearchOpen) {\n        setTimeout(() => {\n          const searchInput = document.querySelector('.mobile-search-input');\n          if (searchInput) {\n            searchInput.focus();\n          }\n        }, 100);\n      }\n    }\n    onSearchSubmit() {\n      if (this.searchQuery.trim()) {\n        // Navigate to search results\n        console.log('Searching for:', this.searchQuery);\n        this.isSearchOpen = false;\n        this.searchQuery = '';\n      }\n    }\n    // Navigation Methods\n    navigateToProfile() {\n      this.closeMenu();\n      // Navigation logic\n    }\n    navigateToOrders() {\n      this.closeMenu();\n      // Navigation logic\n    }\n    navigateToSettings() {\n      this.closeMenu();\n      // Navigation logic\n    }\n    logout() {\n      // Simple logout without subscription\n      this.closeMenu();\n    }\n    // Utility Methods\n    getTotalCount() {\n      return this.cartCount + this.wishlistCount;\n    }\n    formatCount(count) {\n      if (count > 99) return '99+';\n      return count.toString();\n    }\n    isCurrentRoute(route) {\n      return window.location.pathname === route;\n    }\n    // Touch Event Handlers\n    onTouchStart(event) {\n      // Handle touch start for custom gestures\n    }\n    onTouchMove(event) {\n      // Handle touch move for custom gestures\n    }\n    onTouchEnd(event) {\n      // Handle touch end for custom gestures\n    }\n    // Performance Optimization\n    trackByIndex(index) {\n      return index;\n    }\n    static {\n      this.ɵfac = function MobileLayoutComponent_Factory(t) {\n        return new (t || MobileLayoutComponent)(i0.ɵɵdirectiveInject(i1.MobileOptimizationService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MobileLayoutComponent,\n        selectors: [[\"app-mobile-layout\"]],\n        inputs: {\n          showHeader: \"showHeader\",\n          showFooter: \"showFooter\",\n          showBottomNav: \"showBottomNav\"\n        },\n        outputs: {\n          menuToggle: \"menuToggle\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 46,\n        vars: 24,\n        consts: [[1, \"mobile-layout\"], [\"class\", \"mobile-header\", 4, \"ngIf\"], [1, \"mobile-menu\"], [1, \"menu-overlay\", 3, \"click\"], [1, \"menu-content\"], [\"class\", \"menu-profile\", 4, \"ngIf\"], [\"class\", \"menu-guest\", 4, \"ngIf\"], [1, \"menu-nav\"], [\"routerLink\", \"/\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/categories\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-th-large\"], [\"routerLink\", \"/trending\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [\"routerLink\", \"/brands\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-tags\"], [\"routerLink\", \"/offers\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-percent\"], [\"class\", \"menu-section\", 4, \"ngIf\"], [1, \"menu-footer\"], [1, \"app-info\"], [1, \"social-links\"], [\"href\", \"#\", 1, \"social-link\"], [1, \"fab\", \"fa-instagram\"], [1, \"fab\", \"fa-facebook\"], [1, \"fab\", \"fa-twitter\"], [1, \"mobile-main\"], [4, \"ngIf\"], [\"class\", \"mobile-footer\", 4, \"ngIf\"], [1, \"mobile-header\"], [1, \"header-content\"], [1, \"header-btn\", \"menu-btn\", 3, \"click\"], [\"class\", \"fas fa-bars\", 4, \"ngIf\"], [\"class\", \"fas fa-times\", 4, \"ngIf\"], [\"routerLink\", \"/\", 1, \"header-logo\"], [\"src\", \"assets/images/logo.png\", \"alt\", \"DFashion\", 1, \"logo-image\"], [1, \"logo-text\"], [1, \"header-actions\"], [1, \"header-btn\", \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"routerLink\", \"/cart\", 1, \"header-btn\", \"cart-btn\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"badge\", 4, \"ngIf\"], [\"routerLink\", \"/wishlist\", 1, \"header-btn\", \"wishlist-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"mobile-search\"], [1, \"search-container\"], [\"type\", \"text\", \"placeholder\", \"Search for products, brands...\", 1, \"mobile-search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"search-submit-btn\", 3, \"click\"], [1, \"search-close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"fas\", \"fa-bars\"], [1, \"badge\"], [1, \"menu-profile\"], [1, \"profile-avatar\"], [3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"menu-guest\"], [1, \"guest-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"guest-info\"], [1, \"guest-actions\"], [\"routerLink\", \"/login\", 1, \"btn-primary\", 3, \"click\"], [\"routerLink\", \"/register\", 1, \"btn-secondary\", 3, \"click\"], [1, \"menu-section\"], [1, \"menu-divider\"], [\"routerLink\", \"/profile\", 1, \"menu-item\", 3, \"click\"], [\"routerLink\", \"/orders\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-box\"], [\"routerLink\", \"/wishlist\", 1, \"menu-item\", 3, \"click\"], [\"class\", \"menu-badge\", 4, \"ngIf\"], [\"routerLink\", \"/cart\", 1, \"menu-item\", 3, \"click\"], [\"routerLink\", \"/settings\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-cog\"], [1, \"menu-item\", \"logout-btn\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"menu-badge\"], [1, \"mobile-footer\"], [1, \"footer-content\"], [1, \"footer-links\"], [\"href\", \"/about\"], [\"href\", \"/contact\"], [\"href\", \"/privacy\"], [\"href\", \"/terms\"], [1, \"footer-copyright\"]],\n        template: function MobileLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, MobileLayoutComponent_header_1_Template, 25, 7, \"header\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_div_click_3_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4);\n            i0.ɵɵtemplate(5, MobileLayoutComponent_div_5_Template, 8, 4, \"div\", 5)(6, MobileLayoutComponent_div_6_Template, 13, 0, \"div\", 6);\n            i0.ɵɵelementStart(7, \"nav\", 7)(8, \"a\", 8);\n            i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_8_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵelement(9, \"i\", 9);\n            i0.ɵɵelementStart(10, \"span\");\n            i0.ɵɵtext(11, \"Home\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"a\", 10);\n            i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_12_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵelement(13, \"i\", 11);\n            i0.ɵɵelementStart(14, \"span\");\n            i0.ɵɵtext(15, \"Categories\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"a\", 12);\n            i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_16_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵelement(17, \"i\", 13);\n            i0.ɵɵelementStart(18, \"span\");\n            i0.ɵɵtext(19, \"Trending\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"a\", 14);\n            i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_20_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵelement(21, \"i\", 15);\n            i0.ɵɵelementStart(22, \"span\");\n            i0.ɵɵtext(23, \"Brands\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"a\", 16);\n            i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_24_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵelement(25, \"i\", 17);\n            i0.ɵɵelementStart(26, \"span\");\n            i0.ɵɵtext(27, \"Offers\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(28, MobileLayoutComponent_div_28_Template, 29, 2, \"div\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 19)(30, \"div\", 20)(31, \"p\");\n            i0.ɵɵtext(32, \"DFashion v1.0\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p\");\n            i0.ɵɵtext(34, \"\\u00A9 2024 All rights reserved\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"div\", 21)(36, \"a\", 22);\n            i0.ɵɵelement(37, \"i\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"a\", 22);\n            i0.ɵɵelement(39, \"i\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"a\", 22);\n            i0.ɵɵelement(41, \"i\", 25);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(42, \"main\", 26);\n            i0.ɵɵprojection(43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(44, MobileLayoutComponent_app_mobile_bottom_nav_44_Template, 1, 0, \"app-mobile-bottom-nav\", 27)(45, MobileLayoutComponent_footer_45_Template, 14, 0, \"footer\", 28);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"menu-open\", ctx.isMenuOpen)(\"search-open\", ctx.isSearchOpen)(\"keyboard-open\", ctx.isKeyboardOpen);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.currentUser);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/categories\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/trending\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/brands\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/offers\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngIf\", ctx.showBottomNav && !ctx.isKeyboardOpen);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showFooter && !ctx.showBottomNav);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, RouterModule, i6.RouterLink, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, MobileBottomNavComponent],\n        styles: [\".mobile-layout[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh;position:relative;overflow-x:hidden}.mobile-header[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;background:#fff;border-bottom:1px solid #e0e0e0;z-index:1000;box-shadow:0 2px 10px #0000001a;padding-top:env(safe-area-inset-top)}.header-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;height:56px}.header-btn[_ngcontent-%COMP%]{width:40px;height:40px;border:none;background:none;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.header-btn[_ngcontent-%COMP%]:hover{background:#f0f0f0}.header-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.header-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px;color:#333}.header-logo[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer;text-decoration:none}.header-logo[_ngcontent-%COMP%]   .logo-image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:6px}.header-logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:#333;background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.badge[_ngcontent-%COMP%]{position:absolute;top:-2px;right:-2px;background:#ff6b6b;color:#fff;font-size:10px;font-weight:600;padding:2px 6px;border-radius:10px;min-width:16px;text-align:center;line-height:1.2}.mobile-search[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:#fff;border-bottom:1px solid #e0e0e0;transform:translateY(-100%);opacity:0;visibility:hidden;transition:all .3s ease}.mobile-search.active[_ngcontent-%COMP%]{transform:translateY(0);opacity:1;visibility:visible}.search-container[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px;gap:8px}.mobile-search-input[_ngcontent-%COMP%]{flex:1;padding:12px 16px;border:1px solid #ddd;border-radius:25px;font-size:16px;outline:none}.mobile-search-input[_ngcontent-%COMP%]:focus{border-color:#667eea}.search-submit-btn[_ngcontent-%COMP%], .search-close-btn[_ngcontent-%COMP%]{width:40px;height:40px;border:none;background:#667eea;color:#fff;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center}.search-submit-btn[_ngcontent-%COMP%]:active, .search-close-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.search-close-btn[_ngcontent-%COMP%]{background:#f0f0f0;color:#666}.mobile-menu[_ngcontent-%COMP%]{position:fixed;inset:0;z-index:2000;visibility:hidden}.mobile-menu.active[_ngcontent-%COMP%]{visibility:visible}.mobile-menu.active[_ngcontent-%COMP%]   .menu-overlay[_ngcontent-%COMP%]{opacity:1}.mobile-menu.active[_ngcontent-%COMP%]   .menu-content[_ngcontent-%COMP%]{transform:translate(0)}.menu-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#00000080;opacity:0;transition:opacity .3s ease}.menu-content[_ngcontent-%COMP%]{position:absolute;top:0;left:0;bottom:0;width:280px;background:#fff;transform:translate(-100%);transition:transform .3s ease;overflow-y:auto;display:flex;flex-direction:column;padding-top:env(safe-area-inset-top);padding-bottom:env(safe-area-inset-bottom)}.menu-profile[_ngcontent-%COMP%]{padding:24px 20px;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;display:flex;align-items:center;gap:16px}.profile-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;overflow:hidden;border:3px solid rgba(255,255,255,.3)}.profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.profile-info[_ngcontent-%COMP%]{flex:1}.profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin:0 0 4px}.profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;opacity:.8;margin:0}.menu-guest[_ngcontent-%COMP%]{padding:24px 20px;background:#f8f9fa;text-align:center}.guest-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background:#ddd;display:flex;align-items:center;justify-content:center;margin:0 auto 16px}.guest-avatar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#666}.guest-info[_ngcontent-%COMP%]{margin-bottom:20px}.guest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333;margin:0 0 8px}.guest-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.guest-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%], .guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{flex:1;padding:10px 16px;border-radius:20px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease;text-decoration:none;text-align:center}.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:#667eea;color:#fff;border:none}.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover{background:#5a6fd8}.guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{background:#fff;color:#667eea;border:1px solid #667eea}.guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover{background:#f0f2ff}.menu-nav[_ngcontent-%COMP%]{flex:1;padding:8px 0}.menu-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px 20px;color:#333;text-decoration:none;transition:all .3s ease;position:relative;border:none;background:none;width:100%;cursor:pointer}.menu-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.menu-item.active[_ngcontent-%COMP%]{background:#e7f3ff;color:#667eea}.menu-item.active[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:0;bottom:0;width:4px;background:#667eea}.menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px;width:20px;text-align:center}.menu-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:16px;font-weight:500}.menu-item.logout-btn[_ngcontent-%COMP%]{color:#dc3545}.menu-item.logout-btn[_ngcontent-%COMP%]:hover{background:#fff5f5}.menu-badge[_ngcontent-%COMP%]{margin-left:auto;background:#ff6b6b;color:#fff;font-size:10px;font-weight:600;padding:2px 6px;border-radius:10px;min-width:16px;text-align:center}.menu-section[_ngcontent-%COMP%]   .menu-divider[_ngcontent-%COMP%]{height:1px;background:#e0e0e0;margin:8px 20px}.menu-footer[_ngcontent-%COMP%]{padding:20px;border-top:1px solid #e0e0e0;background:#f8f9fa}.app-info[_ngcontent-%COMP%]{text-align:center;margin-bottom:16px}.app-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#666;margin:4px 0}.social-links[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:16px}.social-link[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background:#e0e0e0;display:flex;align-items:center;justify-content:center;color:#666;text-decoration:none;transition:all .3s ease}.social-link[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff}.social-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.mobile-main[_ngcontent-%COMP%]{flex:1;padding-top:calc(56px + env(safe-area-inset-top));padding-bottom:80px}.keyboard-open[_ngcontent-%COMP%]   .mobile-main[_ngcontent-%COMP%]{padding-bottom:0}.mobile-bottom-nav[_ngcontent-%COMP%]{position:fixed;bottom:0;left:0;right:0;background:#fff;border-top:1px solid #e0e0e0;display:flex;z-index:1000;padding-bottom:env(safe-area-inset-bottom)}.nav-item[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:8px 4px;color:#666;text-decoration:none;transition:all .3s ease;position:relative;min-height:60px}.nav-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.nav-item.active[_ngcontent-%COMP%]{color:#667eea}.nav-item.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transform:scale(1.1)}.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;margin-bottom:4px;transition:transform .3s ease}.nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:10px;font-weight:600;text-align:center}.nav-badge[_ngcontent-%COMP%]{position:absolute;top:4px;right:50%;transform:translate(50%);background:#ff6b6b;color:#fff;font-size:8px;font-weight:600;padding:2px 4px;border-radius:8px;min-width:14px;text-align:center;line-height:1}.mobile-footer[_ngcontent-%COMP%]{background:#f8f9fa;border-top:1px solid #e0e0e0;padding:20px 16px;padding-bottom:calc(20px + env(safe-area-inset-bottom))}.footer-content[_ngcontent-%COMP%]{text-align:center}.footer-links[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:20px;margin-bottom:16px}.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#666;text-decoration:none;font-size:14px}.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#667eea}.footer-copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#999;margin:0}@media (max-width: 480px){.header-content[_ngcontent-%COMP%]{padding:8px 12px}.menu-content[_ngcontent-%COMP%]{width:100%;max-width:320px}.nav-item[_ngcontent-%COMP%]{padding:6px 2px;min-height:56px}.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px}.nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:9px}}@media (orientation: landscape) and (max-height: 500px){.mobile-main[_ngcontent-%COMP%]{padding-top:48px;padding-bottom:0}.mobile-bottom-nav[_ngcontent-%COMP%]{display:none}.menu-profile[_ngcontent-%COMP%], .menu-guest[_ngcontent-%COMP%]{padding:16px 20px}.profile-avatar[_ngcontent-%COMP%], .guest-avatar[_ngcontent-%COMP%]{width:40px;height:40px}}@media (prefers-color-scheme: dark){.mobile-header[_ngcontent-%COMP%], .mobile-search[_ngcontent-%COMP%], .menu-content[_ngcontent-%COMP%], .mobile-bottom-nav[_ngcontent-%COMP%], .mobile-footer[_ngcontent-%COMP%]{background:#1a1a1a;border-color:#333}.header-btn[_ngcontent-%COMP%], .menu-item[_ngcontent-%COMP%]{color:#fff}.header-btn[_ngcontent-%COMP%]:hover, .menu-item[_ngcontent-%COMP%]:hover{background:#333}.mobile-search-input[_ngcontent-%COMP%]{background:#333;border-color:#555;color:#fff}}@media (prefers-reduced-motion: reduce){*[_ngcontent-%COMP%]{transition:none!important;animation:none!important}}@media (prefers-contrast: high){.mobile-header[_ngcontent-%COMP%], .mobile-bottom-nav[_ngcontent-%COMP%]{border-width:2px}.badge[_ngcontent-%COMP%], .nav-badge[_ngcontent-%COMP%], .menu-badge[_ngcontent-%COMP%]{border:1px solid #000}}\"]\n      });\n    }\n  }\n  return MobileLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}