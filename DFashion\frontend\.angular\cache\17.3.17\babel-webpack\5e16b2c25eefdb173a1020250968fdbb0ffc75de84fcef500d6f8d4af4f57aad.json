{"ast": null, "code": "import { BehaviorSubject, Observable } from 'rxjs';\nimport { tap, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport let WishlistNewService = /*#__PURE__*/(() => {\n  class WishlistNewService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n      this.apiUrl = 'http://localhost:5000/api/wishlist-new';\n      this.wishlistSubject = new BehaviorSubject(null);\n      this.wishlistSummarySubject = new BehaviorSubject({\n        totalItems: 0,\n        totalValue: 0,\n        totalSavings: 0,\n        itemCount: 0\n      });\n      this.wishlist$ = this.wishlistSubject.asObservable();\n      this.wishlistSummary$ = this.wishlistSummarySubject.asObservable();\n      this.wishlistItemCount$ = this.wishlistSummarySubject.asObservable().pipe(map(summary => summary.totalItems));\n      // Load wishlist when user logs in\n      this.authService.currentUser$.subscribe(user => {\n        if (user && user.role === 'customer') {\n          this.loadWishlist().subscribe({\n            next: () => {\n              console.log('✅ Wishlist loaded for customer');\n            },\n            error: error => {\n              console.error('❌ Error loading wishlist for customer:', error);\n              // Don't clear wishlist on error, just log it\n            }\n          });\n        } else {\n          this.clearLocalWishlist();\n        }\n      });\n    }\n    get currentWishlist() {\n      return this.wishlistSubject.value;\n    }\n    get wishlistItemCount() {\n      return this.wishlistSummarySubject.value.totalItems;\n    }\n    loadWishlist() {\n      if (!this.authService.requireCustomerAuth('access wishlist')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.get(`${this.apiUrl}`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.wishlistSubject.next(response.wishlist);\n          this.wishlistSummarySubject.next(response.summary);\n          console.log('💝 Wishlist loaded:', response.wishlist?.items?.length || 0, 'items');\n        }\n      }));\n    }\n    addToWishlist(productId, size, color, addedFrom = 'manual', notes, priority = 'medium') {\n      if (!this.authService.requireCustomerAuth('add items to wishlist')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      const payload = {\n        productId,\n        size,\n        color,\n        addedFrom,\n        notes,\n        priority\n      };\n      return this.http.post(`${this.apiUrl}/add`, payload, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.wishlistSubject.next(response.wishlist);\n          this.wishlistSummarySubject.next(response.summary);\n          this.showSuccessMessage(response.message);\n          console.log('💝 Item added to wishlist, count updated:', response.summary?.totalItems || 0);\n        }\n      }));\n    }\n    updateWishlistItem(itemId, updates) {\n      if (!this.authService.requireCustomerAuth('update wishlist items')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.put(`${this.apiUrl}/update/${itemId}`, updates, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.wishlistSubject.next(response.wishlist);\n          this.wishlistSummarySubject.next(response.summary);\n          this.showSuccessMessage(response.message);\n        }\n      }));\n    }\n    removeFromWishlist(itemId) {\n      if (!this.authService.requireCustomerAuth('remove items from wishlist')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.delete(`${this.apiUrl}/remove/${itemId}`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.wishlistSubject.next(response.wishlist);\n          this.wishlistSummarySubject.next(response.summary);\n          this.showSuccessMessage(response.message);\n          console.log('💝 Item removed from wishlist, count updated:', response.summary?.totalItems || 0);\n        }\n      }));\n    }\n    likeWishlistItem(itemId, wishlistUserId) {\n      if (!this.authService.requireAuth('like wishlist items')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      const payload = wishlistUserId ? {\n        wishlistUserId\n      } : {};\n      return this.http.post(`${this.apiUrl}/like/${itemId}`, payload, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.showSuccessMessage(response.message);\n          // Refresh wishlist if it's the current user's wishlist\n          if (!wishlistUserId || wishlistUserId === this.authService.currentUserValue?._id) {\n            this.loadWishlist().subscribe();\n          }\n        }\n      }));\n    }\n    unlikeWishlistItem(itemId, wishlistUserId) {\n      if (!this.authService.requireAuth('unlike wishlist items')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      const payload = wishlistUserId ? {\n        wishlistUserId\n      } : {};\n      return this.http.delete(`${this.apiUrl}/unlike/${itemId}`, {\n        headers: this.authService.getAuthHeaders(),\n        body: payload\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.showSuccessMessage(response.message);\n          // Refresh wishlist if it's the current user's wishlist\n          if (!wishlistUserId || wishlistUserId === this.authService.currentUserValue?._id) {\n            this.loadWishlist().subscribe();\n          }\n        }\n      }));\n    }\n    commentOnWishlistItem(itemId, text, wishlistUserId) {\n      if (!this.authService.requireAuth('comment on wishlist items')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      const payload = {\n        text,\n        ...(wishlistUserId && {\n          wishlistUserId\n        })\n      };\n      return this.http.post(`${this.apiUrl}/comment/${itemId}`, payload, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.showSuccessMessage(response.message);\n          // Refresh wishlist if it's the current user's wishlist\n          if (!wishlistUserId || wishlistUserId === this.authService.currentUserValue?._id) {\n            this.loadWishlist().subscribe();\n          }\n        }\n      }));\n    }\n    moveToCart(itemId, quantity = 1) {\n      if (!this.authService.requireCustomerAuth('move items to cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.post(`${this.apiUrl}/move-to-cart/${itemId}`, {\n        quantity\n      }, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.loadWishlist().subscribe(); // Refresh wishlist\n          this.showSuccessMessage(response.message);\n        }\n      }));\n    }\n    // Quick add methods for different sources\n    addFromPost(productId, size, color) {\n      return this.addToWishlist(productId, size, color, 'post');\n    }\n    addFromStory(productId, size, color) {\n      return this.addToWishlist(productId, size, color, 'story');\n    }\n    addFromProduct(productId, size, color) {\n      return this.addToWishlist(productId, size, color, 'product');\n    }\n    addFromCart(productId, size, color) {\n      return this.addToWishlist(productId, size, color, 'cart');\n    }\n    // Helper methods\n    isInWishlist(productId, size, color) {\n      const wishlist = this.currentWishlist;\n      if (!wishlist) return false;\n      return wishlist.items.some(item => item.product._id === productId && item.size === size && item.color === color);\n    }\n    hasLikedItem(itemId) {\n      const wishlist = this.currentWishlist;\n      if (!wishlist) return false;\n      const item = wishlist.items.find(item => item._id === itemId);\n      if (!item) return false;\n      const currentUserId = this.authService.currentUserValue?._id;\n      return item.likes.some(like => like.user._id === currentUserId);\n    }\n    getTotalSavings() {\n      return this.wishlistSummarySubject.value.totalSavings;\n    }\n    // Method to refresh wishlist on user login\n    refreshWishlistOnLogin() {\n      console.log('🔄 Refreshing wishlist on login...');\n      // Check if user is authenticated and is a customer\n      const token = localStorage.getItem('token');\n      const currentUser = this.authService.currentUserValue;\n      if (!token) {\n        console.log('🔒 User not authenticated, skipping wishlist refresh');\n        return;\n      }\n      if (!currentUser) {\n        console.log('🔒 User data not loaded yet, skipping wishlist refresh');\n        return;\n      }\n      if (currentUser.role !== 'customer') {\n        console.log('🔒 User is not a customer, skipping wishlist refresh');\n        return;\n      }\n      this.loadWishlist().subscribe({\n        next: () => {\n          console.log('✅ Wishlist refreshed on login');\n        },\n        error: error => {\n          console.error('❌ Error refreshing wishlist on login:', error);\n          if (error === 'Authentication required' || error.status === 401 || error.status === 403) {\n            console.log('🔒 Authentication required, clearing local wishlist');\n            this.clearLocalWishlist();\n          }\n        }\n      });\n    }\n    // Method to clear wishlist on logout\n    clearWishlistOnLogout() {\n      console.log('🔄 Clearing wishlist on logout...');\n      this.clearLocalWishlist();\n    }\n    clearLocalWishlist() {\n      this.wishlistSubject.next(null);\n      this.wishlistSummarySubject.next({\n        totalItems: 0,\n        totalValue: 0,\n        totalSavings: 0,\n        itemCount: 0\n      });\n    }\n    showSuccessMessage(message) {\n      // TODO: Implement proper toast/notification system\n      console.log('Wishlist Success:', message);\n    }\n    // Utility methods for wishlist calculations\n    calculateItemSavings(item) {\n      if (!item.originalPrice || item.originalPrice <= item.price) return 0;\n      return item.originalPrice - item.price;\n    }\n    getDiscountPercentage(item) {\n      if (!item.originalPrice || item.originalPrice <= item.price) return 0;\n      return Math.round((item.originalPrice - item.price) / item.originalPrice * 100);\n    }\n    getPriorityColor(priority) {\n      switch (priority) {\n        case 'high':\n          return '#ff4757';\n        case 'medium':\n          return '#ffa726';\n        case 'low':\n          return '#66bb6a';\n        default:\n          return '#666';\n      }\n    }\n    static {\n      this.ɵfac = function WishlistNewService_Factory(t) {\n        return new (t || WishlistNewService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: WishlistNewService,\n        factory: WishlistNewService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return WishlistNewService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}