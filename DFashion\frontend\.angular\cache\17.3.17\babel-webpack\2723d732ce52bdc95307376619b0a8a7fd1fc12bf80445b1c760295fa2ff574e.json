{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction CategorySelectionComponent_div_7_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Avg: \\u20B9\", i0.ɵɵpipeBind2(2, 1, category_r2.avgPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction CategorySelectionComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function CategorySelectionComponent_div_7_Template_div_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r2));\n    })(\"keydown.enter\", function CategorySelectionComponent_div_7_Template_div_keydown_enter_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r2));\n    })(\"keydown.space\", function CategorySelectionComponent_div_7_Template_div_keydown_space_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"img\", 11);\n    i0.ɵɵelementStart(3, \"div\", 12)(4, \"div\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 15)(12, \"span\", 16);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CategorySelectionComponent_div_7_span_14_Template, 3, 4, \"span\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 18)(16, \"button\", 19);\n    i0.ɵɵelement(17, \"i\", 20);\n    i0.ɵɵtext(18, \" Browse \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Select \" + category_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r2.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", category_r2.productCount, \" Products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r2.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatTargetGender(category_r2.targetGender));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r2.avgPrice);\n  }\n}\nexport let CategorySelectionComponent = /*#__PURE__*/(() => {\n  class CategorySelectionComponent {\n    constructor(router) {\n      this.router = router;\n      this.categories = [];\n      this.originalQuery = '';\n      this.categorySelected = new EventEmitter();\n      this.searchAllRequested = new EventEmitter();\n      this.refineSearchRequested = new EventEmitter();\n    }\n    ngOnInit() {\n      // Track category selection page view\n      this.trackPageView();\n    }\n    selectCategory(category) {\n      // Track category selection\n      this.trackCategorySelection(category);\n      // Emit category selection event\n      this.categorySelected.emit(category);\n      // Navigate to search results with category filter\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.originalQuery,\n          category: category.category,\n          subcategory: category.subcategory,\n          targetGender: category.targetGender\n        }\n      });\n    }\n    searchAllCategories() {\n      // Track search all action\n      this.trackSearchAllAction();\n      // Emit search all event\n      this.searchAllRequested.emit(this.originalQuery);\n      // Navigate to search results without category filter\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.originalQuery\n        }\n      });\n    }\n    refineSearch() {\n      // Track refine search action\n      this.trackRefineSearchAction();\n      // Emit refine search event\n      this.refineSearchRequested.emit(this.originalQuery);\n      // Navigate back to search page with query pre-filled\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.originalQuery,\n          refine: 'true'\n        }\n      });\n    }\n    formatTargetGender(gender) {\n      switch (gender.toLowerCase()) {\n        case 'men':\n          return 'Men\\'s Fashion';\n        case 'women':\n          return 'Women\\'s Fashion';\n        case 'unisex':\n          return 'Unisex';\n        case 'boys':\n          return 'Boys\\' Fashion';\n        case 'girls':\n          return 'Girls\\' Fashion';\n        default:\n          return 'Fashion';\n      }\n    }\n    trackPageView() {\n      // Analytics tracking for category selection page\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'page_view', {\n          page_title: 'Category Selection',\n          page_location: window.location.href,\n          custom_parameters: {\n            original_query: this.originalQuery,\n            categories_shown: this.categories.length\n          }\n        });\n      }\n    }\n    trackCategorySelection(category) {\n      // Analytics tracking for category selection\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'select_content', {\n          content_type: 'category',\n          content_id: category.id,\n          custom_parameters: {\n            category_name: category.name,\n            target_gender: category.targetGender,\n            product_count: category.productCount,\n            original_query: this.originalQuery\n          }\n        });\n      }\n    }\n    trackSearchAllAction() {\n      // Analytics tracking for search all action\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'search', {\n          search_term: this.originalQuery,\n          custom_parameters: {\n            search_type: 'all_categories',\n            from_category_selection: true\n          }\n        });\n      }\n    }\n    trackRefineSearchAction() {\n      // Analytics tracking for refine search action\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'search', {\n          search_term: this.originalQuery,\n          custom_parameters: {\n            search_type: 'refine',\n            from_category_selection: true\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function CategorySelectionComponent_Factory(t) {\n        return new (t || CategorySelectionComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CategorySelectionComponent,\n        selectors: [[\"app-category-selection\"]],\n        inputs: {\n          categories: \"categories\",\n          originalQuery: \"originalQuery\"\n        },\n        outputs: {\n          categorySelected: \"categorySelected\",\n          searchAllRequested: \"searchAllRequested\",\n          refineSearchRequested: \"refineSearchRequested\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 15,\n        vars: 2,\n        consts: [[1, \"category-selection-container\"], [1, \"category-selection-header\"], [1, \"category-grid\"], [\"class\", \"category-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\"], [1, \"alternative-actions\"], [\"type\", \"button\", 1, \"alt-action-btn\", \"search-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"type\", \"button\", 1, \"alt-action-btn\", \"refine-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"tabindex\", \"0\", 1, \"category-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"category-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"category-overlay\"], [1, \"product-count\"], [1, \"category-info\"], [1, \"category-meta\"], [1, \"target-gender\"], [\"class\", \"avg-price\", 4, \"ngIf\"], [1, \"category-action\"], [\"type\", \"button\", 1, \"select-btn\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"avg-price\"]],\n        template: function CategorySelectionComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"We found multiple categories that match your search. Please select one:\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 2);\n            i0.ɵɵtemplate(7, CategorySelectionComponent_div_7_Template, 19, 8, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 4)(9, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function CategorySelectionComponent_Template_button_click_9_listener() {\n              return ctx.searchAllCategories();\n            });\n            i0.ɵɵelement(10, \"i\", 6);\n            i0.ɵɵtext(11, \" Search All Categories \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function CategorySelectionComponent_Template_button_click_12_listener() {\n              return ctx.refineSearch();\n            });\n            i0.ɵɵelement(13, \"i\", 8);\n            i0.ɵɵtext(14, \" Refine Search \");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"Choose Category for \\\"\", ctx.originalQuery, \"\\\"\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe],\n        styles: [\".category-selection-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:2rem;background:linear-gradient(135deg,#f5f7fa,#c3cfe2);min-height:100vh}@media (max-width: 768px){.category-selection-container[_ngcontent-%COMP%]{padding:1rem}}.category-selection-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:3rem}.category-selection-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;color:#2c3e50;margin-bottom:1rem}@media (max-width: 768px){.category-selection-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2rem}}.category-selection-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;color:#7f8c8d;max-width:600px;margin:0 auto}@media (max-width: 768px){.category-selection-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}}.category-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:2rem;margin-bottom:3rem}@media (max-width: 768px){.category-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1.5rem}}.category-card[_ngcontent-%COMP%]{background:#fff;border-radius:20px;overflow:hidden;box-shadow:0 10px 30px #0000001a;transition:all .3s ease;cursor:pointer;position:relative}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-10px);box-shadow:0 20px 40px #00000026}.category-card[_ngcontent-%COMP%]:focus{outline:3px solid #3498db;outline-offset:2px}.category-card[_ngcontent-%COMP%]:active{transform:translateY(-5px)}.category-image[_ngcontent-%COMP%]{position:relative;height:200px;overflow:hidden}.category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.category-image[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(to bottom,#0000,#0000004d 70%,#000000b3);display:flex;align-items:flex-end;padding:1rem}.category-image[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{background:#ffffffe6;color:#2c3e50;padding:.5rem 1rem;border-radius:20px;font-weight:600;font-size:.9rem;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.1)}.category-info[_ngcontent-%COMP%]{padding:1.5rem}.category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#2c3e50;margin-bottom:.5rem}.category-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#7f8c8d;margin-bottom:1rem;line-height:1.5}.category-meta[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.category-meta[_ngcontent-%COMP%]   .target-gender[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:.3rem .8rem;border-radius:15px;font-size:.8rem;font-weight:600}.category-meta[_ngcontent-%COMP%]   .avg-price[_ngcontent-%COMP%]{color:#27ae60;font-weight:600;font-size:.9rem}.category-action[_ngcontent-%COMP%]{padding:0 1.5rem 1.5rem}.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]{width:100%;background:linear-gradient(135deg,#3498db,#2980b9);color:#fff;border:none;padding:1rem;border-radius:10px;font-weight:600;font-size:1rem;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:.5rem}.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#2980b9,#1f5f8b);transform:translateY(-2px)}.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:transform .3s ease}.category-card[_ngcontent-%COMP%]:hover   .select-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transform:translate(5px)}.alternative-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:1rem;flex-wrap:wrap}@media (max-width: 768px){.alternative-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}}.alt-action-btn[_ngcontent-%COMP%]{background:#fff;border:2px solid #e0e0e0;color:#2c3e50;padding:1rem 2rem;border-radius:50px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:.5rem;font-size:1rem}.alt-action-btn[_ngcontent-%COMP%]:hover{border-color:#3498db;color:#3498db;transform:translateY(-2px);box-shadow:0 5px 15px #3498db33}.alt-action-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.alt-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}@media (max-width: 768px){.alt-action-btn[_ngcontent-%COMP%]{width:100%;justify-content:center;max-width:300px}}.search-all-btn[_ngcontent-%COMP%]:hover{border-color:#27ae60;color:#27ae60;box-shadow:0 5px 15px #27ae6033}.refine-search-btn[_ngcontent-%COMP%]:hover{border-color:#f39c12;color:#f39c12;box-shadow:0 5px 15px #f39c1233}.category-card.loading[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}@media (prefers-reduced-motion: reduce){.category-card[_ngcontent-%COMP%], .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .select-btn[_ngcontent-%COMP%], .alt-action-btn[_ngcontent-%COMP%]{transition:none}.category-card[_ngcontent-%COMP%]:hover, .category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:none}}@media (prefers-contrast: high){.category-card[_ngcontent-%COMP%]{border:2px solid #000}.category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#000}.select-btn[_ngcontent-%COMP%]{background:#000;color:#fff}}@media (prefers-color-scheme: dark){.category-selection-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2c3e50,#34495e)}.category-selection-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#ecf0f1}.category-selection-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#bdc3c7}.category-card[_ngcontent-%COMP%]{background:#34495e}.category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#ecf0f1}.category-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#bdc3c7}.alt-action-btn[_ngcontent-%COMP%]{background:#34495e;border-color:#7f8c8d;color:#ecf0f1}}\"]\n      });\n    }\n  }\n  return CategorySelectionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}