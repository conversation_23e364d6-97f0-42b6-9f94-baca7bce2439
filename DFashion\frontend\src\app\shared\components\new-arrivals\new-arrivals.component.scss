.new-arrivals-section {
  padding: 2rem 0;
  background: #f8f9fa;

  // Section Header
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 0 1rem;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }

    .header-content {
      .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #262626;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        i {
          color: #00bcd4;
          font-size: 1.8rem;
          animation: sparkle 3s infinite;
        }

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .section-subtitle {
        color: #8e8e8e;
        font-size: 1rem;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      @media (max-width: 768px) {
        flex-direction: column;
        width: 100%;
        align-items: stretch;
      }

      .time-filters {
        display: flex;
        gap: 0.5rem;
        background: white;
        padding: 0.25rem;
        border-radius: 25px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        @media (max-width: 768px) {
          justify-content: center;
        }

        .filter-btn {
          padding: 0.5rem 1rem;
          border: none;
          background: transparent;
          color: #666;
          border-radius: 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-weight: 500;
          font-size: 0.85rem;

          &:hover {
            background: rgba(0, 188, 212, 0.1);
            color: #00bcd4;
          }

          &.active {
            background: #00bcd4;
            color: white;
            box-shadow: 0 2px 8px rgba(0, 188, 212, 0.3);
          }
        }
      }

      .view-all-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);
        color: white;
        border: none;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        white-space: nowrap;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 188, 212, 0.4);
        }

        i {
          transition: transform 0.3s ease;
        }

        &:hover i {
          transform: translateX(3px);
        }
      }
    }
  }

  // Loading State
  .loading-container {
    padding: 0 1rem;

    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1.5rem;

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
      }

      .product-card-skeleton {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

        .skeleton-image {
          width: 100%;
          height: 300px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
        }

        .skeleton-content {
          padding: 1rem;

          .skeleton-text {
            height: 16px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.5rem;

            &.short {
              width: 60%;
            }
          }
        }
      }
    }
  }

  // Products Grid
  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 0 1rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
    }
  }

  // Product Card
  .product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);

      .quick-actions {
        opacity: 1;
        transform: translateY(0);
      }

      .product-image {
        transform: scale(1.05);
      }
    }

    &:focus {
      outline: 3px solid #00bcd4;
      outline-offset: 2px;
    }

    .product-image-container {
      position: relative;
      height: 300px;
      overflow: hidden;
      background: #f8f9fa;

      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .new-badge {
        position: absolute;
        top: 12px;
        left: 12px;
        background: linear-gradient(135deg, #00bcd4, #26c6da);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        box-shadow: 0 2px 8px rgba(0, 188, 212, 0.3);

        i {
          animation: sparkle 3s infinite;
        }
      }

      .discount-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        background: #e91e63;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
      }

      .arrival-date {
        position: absolute;
        bottom: 12px;
        left: 12px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
      }

      .quick-actions {
        position: absolute;
        bottom: 12px;
        right: 12px;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;

        .quick-action-btn {
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.9);
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);

          &:hover {
            background: #00bcd4;
            color: white;
            transform: scale(1.1);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .fa-heart.wishlisted {
            color: #e91e63;
          }
        }
      }
    }

    .product-info {
      padding: 1rem;

      .product-brand {
        color: #8e8e8e;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.25rem;
      }

      .product-name {
        font-size: 1rem;
        font-weight: 600;
        color: #262626;
        margin: 0 0 0.5rem 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;

        .stars {
          display: flex;
          gap: 0.125rem;

          .fa-star {
            color: #ddd;
            font-size: 0.8rem;

            &.filled {
              color: #ffc107;
            }
          }
        }

        .rating-text {
          color: #8e8e8e;
          font-size: 0.8rem;
        }
      }

      .product-pricing {
        margin-bottom: 0.75rem;

        .current-price {
          font-size: 1.2rem;
          font-weight: 700;
          color: #262626;
        }

        .original-price {
          font-size: 0.9rem;
          color: #8e8e8e;
          text-decoration: line-through;
          margin-left: 0.5rem;
        }
      }

      .stock-status {
        font-size: 0.8rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.25rem;

        &.in-stock {
          color: #4caf50;
        }

        &.low-stock {
          color: #ff9800;
        }

        &.out-of-stock {
          color: #f44336;
        }

        i {
          font-size: 0.7rem;
        }
      }
    }
  }

  // Load More
  .load-more-container {
    text-align: center;
    margin-top: 2rem;
    padding: 0 1rem;

    .load-more-btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem 2rem;
      background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);
      color: white;
      border: none;
      border-radius: 25px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 188, 212, 0.4);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }

      i {
        font-size: 0.9rem;
      }
    }
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #8e8e8e;

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #ddd;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      color: #666;
    }

    p {
      margin: 0 0 1.5rem 0;
    }

    .retry-btn {
      background: #00bcd4;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0 auto;
    }
  }
}

// Animations
@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  25% {
    transform: scale(1.1) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    opacity: 0.8;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .new-arrivals-section {
    background: #121212;

    .section-title {
      color: #ffffff;
    }

    .product-card {
      background: #1e1e1e;

      .product-name {
        color: #ffffff;
      }

      .product-image-container {
        background: #2a2a2a;
      }

      .quick-action-btn {
        background: rgba(30, 30, 30, 0.9);
        color: #ffffff;
      }
    }

    .time-filters {
      background: #1e1e1e;

      .filter-btn {
        color: #ffffff;

        &:hover {
          background: rgba(0, 188, 212, 0.2);
        }
      }
    }

    .load-more-btn {
      background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);
    }
  }
}
