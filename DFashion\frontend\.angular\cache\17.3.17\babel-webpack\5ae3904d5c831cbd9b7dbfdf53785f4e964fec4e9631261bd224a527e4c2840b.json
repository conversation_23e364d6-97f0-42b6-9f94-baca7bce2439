{"ast": null, "code": "// src/app-data/index.ts\nvar BUILD = {\n  allRenderFn: false,\n  element: true,\n  event: true,\n  hasRenderFn: true,\n  hostListener: true,\n  hostListenerTargetWindow: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetParent: false,\n  hostListenerTarget: true,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  prop: true,\n  propMutable: true,\n  reflect: true,\n  scoped: true,\n  shadowDom: true,\n  slot: true,\n  cssAnnotations: true,\n  state: true,\n  style: true,\n  formAssociated: false,\n  svg: true,\n  updatable: true,\n  vdomAttribute: true,\n  vdomXlink: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomRef: true,\n  vdomPropOrAttr: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  watchCallback: true,\n  taskQueue: true,\n  hotModuleReplacement: false,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  hydrateServerSide: false,\n  hydrateClientSide: false,\n  lifecycleDOMEvents: false,\n  lazyLoad: false,\n  profile: false,\n  slotRelocation: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  appendChildSlotFix: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  cloneNodeFix: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  // TODO(STENCIL-1305): remove this option\n  scriptDataOpts: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  scopedSlotTextContentFix: false,\n  // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n  shadowDomShim: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  slotChildNodesFix: false,\n  invisiblePrehydration: true,\n  propBoolean: true,\n  propNumber: true,\n  propString: true,\n  constructableCSS: true,\n  devTools: false,\n  shadowDelegatesFocus: true,\n  initializeNextTick: false,\n  asyncLoading: true,\n  asyncQueue: false,\n  transformTagName: false,\n  attachStyles: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  experimentalSlotFixes: false\n};\nvar Env = {};\nvar NAMESPACE = /* default */\n\"app\";\nexport { BUILD, Env, NAMESPACE };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}