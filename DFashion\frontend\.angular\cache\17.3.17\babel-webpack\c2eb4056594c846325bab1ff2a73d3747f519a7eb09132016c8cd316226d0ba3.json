{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction SocialFeedComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_8_Template_div_click_0_listener() {\n      const story_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewStory(story_r2.user._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r2.viewed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", story_r2.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", story_r2.user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r2.user.username);\n  }\n}\nfunction SocialFeedComponent_div_10_i_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction SocialFeedComponent_div_10_div_15_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 58);\n  }\n  if (rf & 2) {\n    const media_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r6.url, i0.ɵɵsanitizeUrl)(\"alt\", media_r6.alt);\n  }\n}\nfunction SocialFeedComponent_div_10_div_15_video_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 59);\n  }\n  if (rf & 2) {\n    const media_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r6.url, i0.ɵɵsanitizeUrl)(\"muted\", true);\n  }\n}\nfunction SocialFeedComponent_div_10_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_15_img_1_Template, 1, 2, \"img\", 56)(2, SocialFeedComponent_div_10_div_15_video_2_Template, 1, 2, \"video\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const media_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r6.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r6.type === \"video\");\n  }\n}\nfunction SocialFeedComponent_div_10_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_16_div_1_Template_div_click_0_listener() {\n      const productTag_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.showProductDetails(productTag_r8.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const productTag_r8 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r8.position.x, \"%\")(\"top\", productTag_r8.position.y, \"%\");\n  }\n}\nfunction SocialFeedComponent_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_16_div_1_Template, 3, 4, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r5.products);\n  }\n}\nfunction SocialFeedComponent_div_10_div_17_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 68);\n  }\n  if (rf & 2) {\n    const i_r9 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r9 === 0);\n  }\n}\nfunction SocialFeedComponent_div_10_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtemplate(2, SocialFeedComponent_div_10_div_17_span_2_Template, 1, 2, \"span\", 67);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r5.media);\n  }\n}\nfunction SocialFeedComponent_div_10_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 1, post_r5.likes.length), \" likes\");\n  }\n}\nfunction SocialFeedComponent_div_10_div_36_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 72);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_36_span_1_Template_span_click_0_listener() {\n      const hashtag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.searchHashtag(hashtag_r11));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hashtag_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" #\", hashtag_r11, \" \");\n  }\n}\nfunction SocialFeedComponent_div_10_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_36_span_1_Template, 2, 1, \"span\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r5.hashtags);\n  }\n}\nfunction SocialFeedComponent_div_10_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_37_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const post_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.buyNow(post_r5));\n    });\n    i0.ɵɵelement(2, \"i\", 75);\n    i0.ɵɵtext(3, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const post_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToCart(post_r5));\n    });\n    i0.ɵɵelement(5, \"i\", 77);\n    i0.ɵɵtext(6, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_37_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const post_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToWishlist(post_r5));\n    });\n    i0.ɵɵelement(8, \"i\", 34);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SocialFeedComponent_div_10_div_38_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_38_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const post_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewAllComments(post_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" View all \", post_r5.comments.length, \" comments \");\n  }\n}\nfunction SocialFeedComponent_div_10_div_38_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"span\", 84);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 85);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r14.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r14.text);\n  }\n}\nfunction SocialFeedComponent_div_10_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_38_div_1_Template, 2, 1, \"div\", 80)(2, SocialFeedComponent_div_10_div_38_div_2_Template, 5, 2, \"div\", 81);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r5.comments.length > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r5.comments.slice(-2));\n  }\n}\nfunction SocialFeedComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"img\", 18);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_img_click_3_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProfile(post_r5.user._id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 19)(5, \"div\", 20)(6, \"span\", 21);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_span_click_6_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProfile(post_r5.user._id));\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SocialFeedComponent_div_10_i_8_Template, 1, 0, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_12_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showPostMenu(post_r5));\n    });\n    i0.ɵɵelement(13, \"i\", 26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 27);\n    i0.ɵɵlistener(\"dblclick\", function SocialFeedComponent_div_10_Template_div_dblclick_14_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleLike(post_r5));\n    });\n    i0.ɵɵtemplate(15, SocialFeedComponent_div_10_div_15_Template, 3, 2, \"div\", 28)(16, SocialFeedComponent_div_10_div_16_Template, 2, 1, \"div\", 29)(17, SocialFeedComponent_div_10_div_17_Template, 3, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 31)(19, \"div\", 32)(20, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_20_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleLike(post_r5));\n    });\n    i0.ɵɵelement(21, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_22_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.focusComment(post_r5._id));\n    });\n    i0.ɵɵelement(23, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_24_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.sharePost(post_r5));\n    });\n    i0.ɵɵelement(25, \"i\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 39)(27, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_27_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSave(post_r5));\n    });\n    i0.ɵɵelement(28, \"i\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 42);\n    i0.ɵɵtemplate(30, SocialFeedComponent_div_10_div_30_Template, 4, 3, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 44)(32, \"span\", 45);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 46);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, SocialFeedComponent_div_10_div_36_Template, 2, 1, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, SocialFeedComponent_div_10_div_37_Template, 10, 0, \"div\", 48)(38, SocialFeedComponent_div_10_div_38_Template, 3, 2, \"div\", 49);\n    i0.ɵɵelementStart(39, \"div\", 50);\n    i0.ɵɵelement(40, \"img\", 51);\n    i0.ɵɵelementStart(41, \"input\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SocialFeedComponent_div_10_Template_input_ngModelChange_41_listener($event) {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.commentTexts[post_r5._id], $event) || (ctx_r2.commentTexts[post_r5._id] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SocialFeedComponent_div_10_Template_input_keyup_enter_41_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addComment(post_r5));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_42_listener() {\n      const post_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addComment(post_r5));\n    });\n    i0.ɵɵtext(43, \" Post \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", post_r5.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", post_r5.user.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(post_r5.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r5.user.isVerified);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getTimeAgo(post_r5.createdAt));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", post_r5.media);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r5.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r5.media.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r5.isLiked);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"saved\", post_r5.isSaved);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", post_r5.likes.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r5.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r5.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r5.hashtags.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r5.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r5.comments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"comment-\" + post_r5._id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.commentTexts[post_r5._id]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.commentTexts[post_r5._id] || !ctx_r2.commentTexts[post_r5._id].trim());\n  }\n}\nfunction SocialFeedComponent_div_11_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 89);\n  }\n}\nfunction SocialFeedComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadMorePosts());\n    });\n    i0.ɵɵtemplate(2, SocialFeedComponent_div_11_i_2_Template, 1, 0, \"i\", 88);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loading ? \"Loading...\" : \"Load More Posts\", \" \");\n  }\n}\nfunction SocialFeedComponent_div_12_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r2.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction SocialFeedComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 91);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 92)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 95);\n    i0.ɵɵelement(8, \"img\", 96);\n    i0.ɵɵelementStart(9, \"div\", 97)(10, \"p\", 98);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 99)(13, \"span\", 100);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SocialFeedComponent_div_12_span_16_Template, 3, 4, \"span\", 101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 102)(18, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedProduct.images[0] == null ? null : ctx_r2.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r2.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedProduct.originalPrice);\n  }\n}\nexport class SocialFeedComponent {\n  constructor(router) {\n    this.router = router;\n    this.posts = [];\n    this.stories = [];\n    this.commentTexts = {};\n    this.selectedProduct = null;\n    this.currentUser = null;\n    this.loading = false;\n    this.hasMorePosts = true;\n  }\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.loadStories();\n    this.loadPosts();\n  }\n  loadCurrentUser() {\n    // TODO: Get current user from auth service\n    this.currentUser = {\n      _id: 'current-user',\n      username: 'you',\n      fullName: 'Your Name',\n      avatar: ''\n    };\n  }\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories').then(response => response.json()).then(data => {\n      if (data.success) {\n        // Group stories by user\n        const userStories = data.stories.reduce((acc, story) => {\n          const userId = story.user._id;\n          if (!acc[userId]) {\n            acc[userId] = {\n              user: story.user,\n              viewed: false,\n              stories: []\n            };\n          }\n          acc[userId].stories.push(story);\n          return acc;\n        }, {});\n        this.stories = Object.values(userStories);\n      }\n    }).catch(error => {\n      console.error('Error loading stories:', error);\n    });\n  }\n  loadPosts() {\n    this.loading = true;\n    // Load posts from real API\n    fetch('http://localhost:5000/api/posts').then(response => response.json()).then(data => {\n      if (data.success) {\n        this.posts = data.posts.map(post => ({\n          ...post,\n          isLiked: false,\n          isSaved: false // TODO: Check if current user saved this post\n        }));\n      }\n      this.loading = false;\n    }).catch(error => {\n      console.error('Error loading posts:', error);\n      this.loading = false;\n    });\n  }\n  loadMorePosts() {\n    if (this.loading || !this.hasMorePosts) return;\n    this.loading = true;\n    const page = Math.floor(this.posts.length / 10) + 1;\n    // Load more posts from real API\n    fetch(`http://localhost:5000/api/posts?page=${page}&limit=10`).then(response => response.json()).then(data => {\n      if (data.success && data.posts.length > 0) {\n        const newPosts = data.posts.map(post => ({\n          ...post,\n          isLiked: false,\n          isSaved: false // TODO: Check if current user saved this post\n        }));\n        this.posts = [...this.posts, ...newPosts];\n        // Check if there are more posts\n        this.hasMorePosts = data.posts.length === 10;\n      } else {\n        this.hasMorePosts = false;\n      }\n      this.loading = false;\n    }).catch(error => {\n      console.error('Error loading more posts:', error);\n      this.loading = false;\n    });\n  }\n  // Stories actions\n  createStory() {\n    this.router.navigate(['/create-story']);\n  }\n  viewStory(userId) {\n    // Navigate to stories viewer with user ID\n    this.router.navigate(['/stories', userId]);\n  }\n  viewStories() {\n    // Navigate to general stories viewer\n    this.router.navigate(['/stories']);\n  }\n  // Post actions\n  viewProfile(userId) {\n    this.router.navigate(['/profile', userId]);\n  }\n  showPostMenu(post) {\n    // TODO: Show post menu (report, share, etc.)\n    console.log('Show menu for post:', post);\n  }\n  toggleLike(post) {\n    post.isLiked = !post.isLiked;\n    if (post.isLiked) {\n      post.likes.push({\n        user: this.currentUser._id,\n        likedAt: new Date()\n      });\n    } else {\n      post.likes = post.likes.filter(like => like.user !== this.currentUser._id);\n    }\n    // TODO: Update like status via API\n    console.log('Toggle like for post:', post._id, post.isLiked);\n  }\n  toggleSave(post) {\n    post.isSaved = !post.isSaved;\n    if (post.isSaved) {\n      post.saves.push({\n        user: this.currentUser._id,\n        savedAt: new Date()\n      });\n    } else {\n      post.saves = post.saves.filter(save => save.user !== this.currentUser._id);\n    }\n    // TODO: Update save status via API\n    console.log('Toggle save for post:', post._id, post.isSaved);\n  }\n  sharePost(post) {\n    // TODO: Implement share functionality\n    console.log('Share post:', post);\n    if (navigator.share) {\n      navigator.share({\n        title: `${post.user.username}'s post`,\n        text: post.caption,\n        url: window.location.href\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link copied to clipboard!');\n    }\n  }\n  focusComment(postId) {\n    const commentInput = document.getElementById(`comment-${postId}`);\n    if (commentInput) {\n      commentInput.focus();\n    }\n  }\n  addComment(post) {\n    const commentText = this.commentTexts[post._id];\n    if (!commentText?.trim()) return;\n    const newComment = {\n      _id: Date.now().toString(),\n      user: {\n        _id: this.currentUser._id,\n        username: this.currentUser.username,\n        fullName: this.currentUser.fullName,\n        avatar: this.currentUser.avatar\n      },\n      text: commentText.trim(),\n      commentedAt: new Date()\n    };\n    post.comments.push(newComment);\n    this.commentTexts[post._id] = '';\n    // TODO: Add comment via API\n    console.log('Add comment to post:', post._id, newComment);\n  }\n  viewAllComments(post) {\n    this.router.navigate(['/post', post._id, 'comments']);\n  }\n  searchHashtag(hashtag) {\n    this.router.navigate(['/search'], {\n      queryParams: {\n        hashtag\n      }\n    });\n  }\n  // E-commerce actions\n  buyNow(post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'post'\n        }\n      });\n    }\n  }\n  addToCart(post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from post:', product);\n      alert(`${product.name} added to cart!`);\n    }\n  }\n  addToWishlist(post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from post:', product);\n      alert(`${product.name} added to wishlist!`);\n    }\n  }\n  // Product modal\n  showProductDetails(product) {\n    this.selectedProduct = product;\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: this.selectedProduct._id,\n          source: 'post'\n        }\n      });\n    }\n  }\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to cart!`);\n      this.closeProductModal();\n    }\n  }\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to wishlist!`);\n      this.closeProductModal();\n    }\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    if (diffMinutes < 1) return 'now';\n    if (diffMinutes < 60) return `${diffMinutes}m`;\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays < 7) return `${diffDays}d`;\n    return new Date(date).toLocaleDateString();\n  }\n  static {\n    this.ɵfac = function SocialFeedComponent_Factory(t) {\n      return new (t || SocialFeedComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SocialFeedComponent,\n      selectors: [[\"app-social-feed\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 4,\n      consts: [[1, \"social-feed\"], [1, \"stories-bar\"], [1, \"stories-container\"], [1, \"story-item\", \"add-story\", 3, \"click\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"posts-container\"], [\"class\", \"post-card\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"load-more\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [3, \"src\", \"alt\"], [1, \"post-card\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"click\", \"src\", \"alt\"], [1, \"user-details\"], [1, \"username-row\"], [1, \"username\", 3, \"click\"], [\"class\", \"fas fa-check-circle verified\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"post-menu\"], [1, \"btn-menu\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\", 3, \"dblclick\"], [\"class\", \"media-container\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"media-nav\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"action-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"action-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"secondary-actions\"], [1, \"action-btn\", \"save\", 3, \"click\"], [1, \"fas\", \"fa-bookmark\"], [1, \"post-stats\"], [\"class\", \"likes-count\", 4, \"ngIf\"], [1, \"post-caption\"], [1, \"username\"], [1, \"caption-text\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"add-comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"id\", \"ngModel\"], [1, \"btn-post-comment\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-check-circle\", \"verified\"], [1, \"media-container\"], [\"class\", \"post-image\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"post-video\", \"controls\", \"\", 3, \"src\", \"muted\", 4, \"ngIf\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"controls\", \"\", 1, \"post-video\", 3, \"src\", \"muted\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"media-nav\"], [1, \"nav-dots\"], [\"class\", \"dot\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"dot\"], [1, \"likes-count\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\", 3, \"click\"], [1, \"ecommerce-actions\"], [1, \"ecom-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"ecom-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecom-btn\", \"wishlist\", 3, \"click\"], [1, \"comments-preview\"], [\"class\", \"view-all-comments\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all-comments\", 3, \"click\"], [1, \"comment\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"load-more\"], [1, \"btn-load-more\", 3, \"click\", \"disabled\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"]],\n      template: function SocialFeedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function SocialFeedComponent_Template_div_click_3_listener() {\n            return ctx.createStory();\n          });\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 6);\n          i0.ɵɵtext(7, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, SocialFeedComponent_div_8_Template, 5, 5, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 8);\n          i0.ɵɵtemplate(10, SocialFeedComponent_div_10_Template, 44, 23, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, SocialFeedComponent_div_11_Template, 4, 3, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, SocialFeedComponent_div_12_Template, 24, 9, \"div\", 11);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.posts);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasMorePosts);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".social-feed[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n  padding: 0 0 80px 0;\\n}\\n\\n\\n\\n.stories-bar[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-bottom: 1px solid #eee;\\n  padding: 16px 0;\\n  margin-bottom: 20px;\\n  position: sticky;\\n  top: 0;\\n  z-index: 100;\\n}\\n\\n.stories-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 20px;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.stories-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  min-width: 70px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: 2px solid #ff6b6b;\\n  padding: 2px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(45deg, #ff6b6b, #ffa726);\\n}\\n\\n.story-avatar.viewed[_ngcontent-%COMP%] {\\n  border-color: #ccc;\\n  background: #ccc;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: #f8f9fa !important;\\n  border-color: #ddd !important;\\n  color: #666;\\n  font-size: 1.2rem;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #333;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.posts-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.post-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n}\\n\\n.verified[_ngcontent-%COMP%] {\\n  color: #1da1f2;\\n  font-size: 0.8rem;\\n}\\n\\n.post-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.btn-menu[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #666;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n}\\n\\n.btn-menu[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n\\n\\n\\n.post-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #000;\\n}\\n\\n.media-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  aspect-ratio: 1;\\n  overflow: hidden;\\n}\\n\\n.post-image[_ngcontent-%COMP%], .post-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #333;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    transform: translate(-50%, -50%) scale(1.1);\\n  }\\n}\\n.media-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 16px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n}\\n\\n.nav-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n}\\n\\n.dot[_ngcontent-%COMP%] {\\n  width: 6px;\\n  height: 6px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n.dot.active[_ngcontent-%COMP%] {\\n  background: #fff;\\n}\\n\\n\\n\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 20px 8px;\\n}\\n\\n.primary-actions[_ngcontent-%COMP%], .secondary-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.2rem;\\n  color: #333;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  transform: scale(1.1);\\n}\\n\\n.action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n\\n.action-btn.saved[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n\\n\\n.post-stats[_ngcontent-%COMP%] {\\n  padding: 0 20px 8px;\\n}\\n\\n.likes-count[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #333;\\n}\\n\\n\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 0 20px 12px;\\n  line-height: 1.4;\\n}\\n\\n.caption-text[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  color: #333;\\n}\\n\\n.hashtags[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\n.hashtag[_ngcontent-%COMP%] {\\n  color: #1da1f2;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n}\\n\\n.hashtag[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  border-top: 1px solid #f0f0f0;\\n}\\n\\n.ecom-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  font-size: 0.85rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  transition: all 0.2s ease;\\n}\\n\\n.buy-now[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.add-cart[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n  color: #fff;\\n}\\n\\n.wishlist[_ngcontent-%COMP%] {\\n  background: #ff9ff3;\\n  color: #fff;\\n}\\n\\n.ecom-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n\\n\\n\\n.comments-preview[_ngcontent-%COMP%] {\\n  padding: 0 20px 12px;\\n}\\n\\n.view-all-comments[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  margin-bottom: 8px;\\n}\\n\\n.view-all-comments[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.comment[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  font-size: 0.9rem;\\n  line-height: 1.3;\\n}\\n\\n.comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  margin-right: 8px;\\n}\\n\\n.comment-text[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n\\n\\n.add-comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 20px;\\n  border-top: 1px solid #f0f0f0;\\n}\\n\\n.comment-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n}\\n\\n.comment-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  font-size: 0.9rem;\\n  padding: 8px 0;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n.btn-post-comment[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #1da1f2;\\n  font-weight: 600;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  padding: 8px;\\n}\\n\\n.btn-post-comment[_ngcontent-%COMP%]:disabled {\\n  color: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n.load-more[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n\\n.btn-load-more[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n}\\n\\n.btn-load-more[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n.product-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  max-width: 400px;\\n  width: 100%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  color: #666;\\n  padding: 4px;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 8px;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: #fff;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #007bff;\\n  border: 1px solid #007bff;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .social-feed[_ngcontent-%COMP%] {\\n    padding: 0 0 60px 0;\\n  }\\n  .stories-bar[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n  }\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 12px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    max-width: 50px;\\n  }\\n  .post-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .post-actions[_ngcontent-%COMP%] {\\n    padding: 8px 16px 6px;\\n  }\\n  .post-stats[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n    padding-right: 16px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    flex-direction: column;\\n    gap: 6px;\\n  }\\n  .ecom-btn[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    font-size: 0.9rem;\\n  }\\n  .add-comment[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SocialFeedComponent_div_8_Template_div_click_0_listener", "story_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "viewStory", "user", "_id", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵclassProp", "viewed", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "media_r6", "url", "alt", "ɵɵtemplate", "SocialFeedComponent_div_10_div_15_img_1_Template", "SocialFeedComponent_div_10_div_15_video_2_Template", "type", "SocialFeedComponent_div_10_div_16_div_1_Template_div_click_0_listener", "productTag_r8", "_r7", "showProductDetails", "product", "ɵɵstyleProp", "position", "x", "y", "SocialFeedComponent_div_10_div_16_div_1_Template", "post_r5", "products", "i_r9", "SocialFeedComponent_div_10_div_17_span_2_Template", "media", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "likes", "length", "SocialFeedComponent_div_10_div_36_span_1_Template_span_click_0_listener", "hashtag_r11", "_r10", "searchHashtag", "SocialFeedComponent_div_10_div_36_span_1_Template", "hashtags", "SocialFeedComponent_div_10_div_37_Template_button_click_1_listener", "_r12", "buyNow", "SocialFeedComponent_div_10_div_37_Template_button_click_4_listener", "addToCart", "SocialFeedComponent_div_10_div_37_Template_button_click_7_listener", "addToWishlist", "SocialFeedComponent_div_10_div_38_div_1_Template_div_click_0_listener", "_r13", "viewAllComments", "comments", "comment_r14", "text", "SocialFeedComponent_div_10_div_38_div_1_Template", "SocialFeedComponent_div_10_div_38_div_2_Template", "slice", "SocialFeedComponent_div_10_Template_img_click_3_listener", "_r4", "viewProfile", "SocialFeedComponent_div_10_Template_span_click_6_listener", "SocialFeedComponent_div_10_i_8_Template", "SocialFeedComponent_div_10_Template_button_click_12_listener", "showPostMenu", "SocialFeedComponent_div_10_Template_div_dblclick_14_listener", "toggleLike", "SocialFeedComponent_div_10_div_15_Template", "SocialFeedComponent_div_10_div_16_Template", "SocialFeedComponent_div_10_div_17_Template", "SocialFeedComponent_div_10_Template_button_click_20_listener", "SocialFeedComponent_div_10_Template_button_click_22_listener", "focusComment", "SocialFeedComponent_div_10_Template_button_click_24_listener", "sharePost", "SocialFeedComponent_div_10_Template_button_click_27_listener", "toggleSave", "SocialFeedComponent_div_10_div_30_Template", "SocialFeedComponent_div_10_div_36_Template", "SocialFeedComponent_div_10_div_37_Template", "SocialFeedComponent_div_10_div_38_Template", "ɵɵtwoWayListener", "SocialFeedComponent_div_10_Template_input_ngModelChange_41_listener", "$event", "ɵɵtwoWayBindingSet", "commentTexts", "SocialFeedComponent_div_10_Template_input_keyup_enter_41_listener", "addComment", "SocialFeedComponent_div_10_Template_button_click_42_listener", "isVerified", "getTimeAgo", "createdAt", "isLiked", "isSaved", "caption", "currentUser", "ɵɵtwoWayProperty", "trim", "SocialFeedComponent_div_11_Template_button_click_1_listener", "_r15", "loadMorePosts", "SocialFeedComponent_div_11_i_2_Template", "loading", "ɵɵpipeBind2", "selectedProduct", "originalPrice", "SocialFeedComponent_div_12_Template_div_click_0_listener", "_r16", "closeProductModal", "SocialFeedComponent_div_12_Template_div_click_1_listener", "stopPropagation", "SocialFeedComponent_div_12_Template_button_click_5_listener", "SocialFeedComponent_div_12_span_16_Template", "SocialFeedComponent_div_12_Template_button_click_18_listener", "buyProductNow", "SocialFeedComponent_div_12_Template_button_click_20_listener", "addProductToCart", "SocialFeedComponent_div_12_Template_button_click_22_listener", "addProductToWishlist", "name", "images", "brand", "price", "SocialFeedComponent", "constructor", "router", "posts", "stories", "hasMorePosts", "ngOnInit", "loadCurrentUser", "loadStories", "loadPosts", "fetch", "then", "response", "json", "data", "success", "userStories", "reduce", "acc", "story", "userId", "push", "Object", "values", "catch", "error", "console", "map", "post", "page", "Math", "floor", "newPosts", "createStory", "navigate", "viewStories", "log", "likedAt", "Date", "filter", "like", "saves", "savedAt", "save", "navigator", "share", "title", "window", "location", "href", "clipboard", "writeText", "alert", "postId", "commentInput", "document", "getElementById", "focus", "commentText", "newComment", "now", "toString", "commentedAt", "hashtag", "queryParams", "productId", "source", "date", "diffMs", "getTime", "diffMinutes", "diffHours", "diffDays", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SocialFeedComponent_Template", "rf", "ctx", "SocialFeedComponent_Template_div_click_3_listener", "SocialFeedComponent_div_8_Template", "SocialFeedComponent_div_10_Template", "SocialFeedComponent_div_11_Template", "SocialFeedComponent_div_12_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\posts\\social-feed.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Post {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar?: string;\n    isVerified?: boolean;\n  };\n  caption: string;\n  media: {\n    type: 'image' | 'video';\n    url: string;\n    alt: string;\n  }[];\n  products: {\n    _id: string;\n    product: {\n      _id: string;\n      name: string;\n      price: number;\n      originalPrice?: number;\n      images: { url: string; alt: string }[];\n      brand: string;\n    };\n    position: { x: number; y: number };\n    size?: string;\n    color?: string;\n  }[];\n  hashtags: string[];\n  likes: { user: string; likedAt: Date }[];\n  comments: {\n    _id: string;\n    user: {\n      _id: string;\n      username: string;\n      fullName: string;\n      avatar?: string;\n    };\n    text: string;\n    commentedAt: Date;\n  }[];\n  shares: { user: string; sharedAt: Date }[];\n  saves: { user: string; savedAt: Date }[];\n  isLiked: boolean;\n  isSaved: boolean;\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-social-feed',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"social-feed\">\n      <!-- Stories Bar -->\n      <div class=\"stories-bar\">\n        <div class=\"stories-container\">\n          <div class=\"story-item add-story\" (click)=\"createStory()\">\n            <div class=\"story-avatar add-avatar\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n            <span class=\"story-username\">Your Story</span>\n          </div>\n\n          <div class=\"story-item\"\n               *ngFor=\"let story of stories\"\n               (click)=\"viewStory(story.user._id)\">\n            <div class=\"story-avatar\" [class.viewed]=\"story.viewed\">\n              <img [src]=\"story.user.avatar || '/assets/images/default-avatar.png'\"\n                   [alt]=\"story.user.fullName\">\n            </div>\n            <span class=\"story-username\">{{ story.user.username }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Posts Feed -->\n      <div class=\"posts-container\">\n        <div class=\"post-card\" *ngFor=\"let post of posts\">\n          <!-- Post Header -->\n          <div class=\"post-header\">\n            <div class=\"user-info\">\n              <img [src]=\"post.user.avatar || '/assets/images/default-avatar.png'\"\n                   [alt]=\"post.user.fullName\"\n                   class=\"user-avatar\"\n                   (click)=\"viewProfile(post.user._id)\">\n              <div class=\"user-details\">\n                <div class=\"username-row\">\n                  <span class=\"username\" (click)=\"viewProfile(post.user._id)\">{{ post.user.username }}</span>\n                  <i class=\"fas fa-check-circle verified\" *ngIf=\"post.user.isVerified\"></i>\n                </div>\n                <span class=\"post-time\">{{ getTimeAgo(post.createdAt) }}</span>\n              </div>\n            </div>\n\n            <div class=\"post-menu\">\n              <button class=\"btn-menu\" (click)=\"showPostMenu(post)\">\n                <i class=\"fas fa-ellipsis-h\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Post Media -->\n          <div class=\"post-media\" (dblclick)=\"toggleLike(post)\">\n            <div class=\"media-container\" *ngFor=\"let media of post.media; let i = index\">\n              <img *ngIf=\"media.type === 'image'\"\n                   [src]=\"media.url\"\n                   [alt]=\"media.alt\"\n                   class=\"post-image\">\n\n              <video *ngIf=\"media.type === 'video'\"\n                     [src]=\"media.url\"\n                     class=\"post-video\"\n                     controls\n                     [muted]=\"true\">\n              </video>\n            </div>\n\n            <!-- Product Tags -->\n            <div class=\"product-tags\" *ngIf=\"post.products.length > 0\">\n              <div class=\"product-tag\"\n                   *ngFor=\"let productTag of post.products\"\n                   [style.left.%]=\"productTag.position.x\"\n                   [style.top.%]=\"productTag.position.y\"\n                   (click)=\"showProductDetails(productTag.product)\">\n                <div class=\"product-tag-icon\">\n                  <i class=\"fas fa-shopping-bag\"></i>\n                </div>\n              </div>\n            </div>\n\n            <!-- Media Navigation (for multiple images) -->\n            <div class=\"media-nav\" *ngIf=\"post.media.length > 1\">\n              <div class=\"nav-dots\">\n                <span class=\"dot\"\n                      *ngFor=\"let media of post.media; let i = index\"\n                      [class.active]=\"i === 0\"></span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Post Actions -->\n          <div class=\"post-actions\">\n            <div class=\"primary-actions\">\n              <button class=\"action-btn like\"\n                      [class.liked]=\"post.isLiked\"\n                      (click)=\"toggleLike(post)\">\n                <i class=\"fas fa-heart\"></i>\n              </button>\n\n              <button class=\"action-btn comment\" (click)=\"focusComment(post._id)\">\n                <i class=\"fas fa-comment\"></i>\n              </button>\n\n              <button class=\"action-btn share\" (click)=\"sharePost(post)\">\n                <i class=\"fas fa-paper-plane\"></i>\n              </button>\n            </div>\n\n            <div class=\"secondary-actions\">\n              <button class=\"action-btn save\"\n                      [class.saved]=\"post.isSaved\"\n                      (click)=\"toggleSave(post)\">\n                <i class=\"fas fa-bookmark\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Post Stats -->\n          <div class=\"post-stats\">\n            <div class=\"likes-count\" *ngIf=\"post.likes.length > 0\">\n              <strong>{{ post.likes.length | number }} likes</strong>\n            </div>\n          </div>\n\n          <!-- Post Caption -->\n          <div class=\"post-caption\">\n            <span class=\"username\">{{ post.user.username }}</span>\n            <span class=\"caption-text\">{{ post.caption }}</span>\n\n            <div class=\"hashtags\" *ngIf=\"post.hashtags.length > 0\">\n              <span class=\"hashtag\"\n                    *ngFor=\"let hashtag of post.hashtags\"\n                    (click)=\"searchHashtag(hashtag)\">\n                #{{ hashtag }}\n              </span>\n            </div>\n          </div>\n\n          <!-- E-commerce Actions -->\n          <div class=\"ecommerce-actions\" *ngIf=\"post.products.length > 0\">\n            <button class=\"ecom-btn buy-now\" (click)=\"buyNow(post)\">\n              <i class=\"fas fa-bolt\"></i>\n              Buy Now\n            </button>\n            <button class=\"ecom-btn add-cart\" (click)=\"addToCart(post)\">\n              <i class=\"fas fa-shopping-cart\"></i>\n              Add to Cart\n            </button>\n            <button class=\"ecom-btn wishlist\" (click)=\"addToWishlist(post)\">\n              <i class=\"fas fa-heart\"></i>\n              Wishlist\n            </button>\n          </div>\n\n          <!-- Comments Preview -->\n          <div class=\"comments-preview\" *ngIf=\"post.comments.length > 0\">\n            <div class=\"view-all-comments\"\n                 *ngIf=\"post.comments.length > 2\"\n                 (click)=\"viewAllComments(post)\">\n              View all {{ post.comments.length }} comments\n            </div>\n\n            <div class=\"comment\"\n                 *ngFor=\"let comment of post.comments.slice(-2)\">\n              <span class=\"comment-username\">{{ comment.user.username }}</span>\n              <span class=\"comment-text\">{{ comment.text }}</span>\n            </div>\n          </div>\n\n          <!-- Add Comment -->\n          <div class=\"add-comment\">\n            <img [src]=\"currentUser?.avatar || '/assets/images/default-avatar.png'\"\n                 [alt]=\"currentUser?.fullName\"\n                 class=\"comment-avatar\">\n            <input type=\"text\"\n                   [id]=\"'comment-' + post._id\"\n                   [(ngModel)]=\"commentTexts[post._id]\"\n                   placeholder=\"Add a comment...\"\n                   (keyup.enter)=\"addComment(post)\"\n                   class=\"comment-input\">\n            <button class=\"btn-post-comment\"\n                    (click)=\"addComment(post)\"\n                    [disabled]=\"!commentTexts[post._id] || !commentTexts[post._id].trim()\">\n              Post\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Load More -->\n      <div class=\"load-more\" *ngIf=\"hasMorePosts\">\n        <button class=\"btn-load-more\" (click)=\"loadMorePosts()\" [disabled]=\"loading\">\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"loading\"></i>\n          {{ loading ? 'Loading...' : 'Load More Posts' }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Product Details Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>{{ selectedProduct.name }}</h3>\n          <button class=\"btn-close\" (click)=\"closeProductModal()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n\n        <div class=\"modal-body\">\n          <img [src]=\"selectedProduct.images[0]?.url\"\n               [alt]=\"selectedProduct.name\"\n               class=\"product-image\">\n\n          <div class=\"product-info\">\n            <p class=\"brand\">{{ selectedProduct.brand }}</p>\n            <div class=\"price\">\n              <span class=\"current-price\">₹{{ selectedProduct.price | number:'1.0-0' }}</span>\n              <span class=\"original-price\" *ngIf=\"selectedProduct.originalPrice\">\n                ₹{{ selectedProduct.originalPrice | number:'1.0-0' }}\n              </span>\n            </div>\n          </div>\n\n          <div class=\"modal-actions\">\n            <button class=\"btn-primary\" (click)=\"buyProductNow()\">Buy Now</button>\n            <button class=\"btn-secondary\" (click)=\"addProductToCart()\">Add to Cart</button>\n            <button class=\"btn-outline\" (click)=\"addProductToWishlist()\">Add to Wishlist</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .social-feed {\n      max-width: 600px;\n      margin: 0 auto;\n      padding: 0 0 80px 0;\n    }\n\n    /* Stories Bar */\n    .stories-bar {\n      background: #fff;\n      border-bottom: 1px solid #eee;\n      padding: 16px 0;\n      margin-bottom: 20px;\n      position: sticky;\n      top: 0;\n      z-index: 100;\n    }\n\n    .stories-container {\n      display: flex;\n      gap: 16px;\n      padding: 0 20px;\n      overflow-x: auto;\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n    }\n\n    .stories-container::-webkit-scrollbar {\n      display: none;\n    }\n\n    .story-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 8px;\n      cursor: pointer;\n      min-width: 70px;\n    }\n\n    .story-avatar {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      border: 2px solid #ff6b6b;\n      padding: 2px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: linear-gradient(45deg, #ff6b6b, #ffa726);\n    }\n\n    .story-avatar.viewed {\n      border-color: #ccc;\n      background: #ccc;\n    }\n\n    .story-avatar img {\n      width: 100%;\n      height: 100%;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .add-avatar {\n      background: #f8f9fa !important;\n      border-color: #ddd !important;\n      color: #666;\n      font-size: 1.2rem;\n    }\n\n    .story-username {\n      font-size: 0.8rem;\n      color: #333;\n      text-align: center;\n      max-width: 70px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    /* Post Cards */\n    .posts-container {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n    }\n\n    .post-card {\n      background: #fff;\n      border-radius: 12px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n      overflow: hidden;\n    }\n\n    .post-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      cursor: pointer;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username-row {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n    }\n\n    .username {\n      font-weight: 600;\n      color: #333;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .verified {\n      color: #1da1f2;\n      font-size: 0.8rem;\n    }\n\n    .post-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .btn-menu {\n      background: none;\n      border: none;\n      color: #666;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 50%;\n    }\n\n    .btn-menu:hover {\n      background: #f8f9fa;\n    }\n\n    /* Post Media */\n    .post-media {\n      position: relative;\n      background: #000;\n    }\n\n    .media-container {\n      width: 100%;\n      aspect-ratio: 1;\n      overflow: hidden;\n    }\n\n    .post-image, .post-video {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n      transform: translate(-50%, -50%);\n    }\n\n    .product-tag-icon {\n      width: 32px;\n      height: 32px;\n      background: rgba(255,255,255,0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #333;\n      animation: pulse 2s infinite;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); }\n      50% { transform: translate(-50%, -50%) scale(1.1); }\n    }\n\n    .media-nav {\n      position: absolute;\n      bottom: 16px;\n      left: 50%;\n      transform: translateX(-50%);\n    }\n\n    .nav-dots {\n      display: flex;\n      gap: 6px;\n    }\n\n    .dot {\n      width: 6px;\n      height: 6px;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.5);\n    }\n\n    .dot.active {\n      background: #fff;\n    }\n\n    /* Post Actions */\n    .post-actions {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px 20px 8px;\n    }\n\n    .primary-actions, .secondary-actions {\n      display: flex;\n      gap: 16px;\n    }\n\n    .action-btn {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      color: #333;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 50%;\n      transition: all 0.2s ease;\n    }\n\n    .action-btn:hover {\n      background: #f8f9fa;\n      transform: scale(1.1);\n    }\n\n    .action-btn.liked {\n      color: #ff6b6b;\n      animation: heartBeat 0.6s ease;\n    }\n\n    .action-btn.saved {\n      color: #333;\n    }\n\n    @keyframes heartBeat {\n      0%, 100% { transform: scale(1); }\n      50% { transform: scale(1.2); }\n    }\n\n    /* Post Stats */\n    .post-stats {\n      padding: 0 20px 8px;\n    }\n\n    .likes-count {\n      font-size: 0.9rem;\n      color: #333;\n    }\n\n    /* Post Caption */\n    .post-caption {\n      padding: 0 20px 12px;\n      line-height: 1.4;\n    }\n\n    .caption-text {\n      margin-left: 8px;\n      color: #333;\n    }\n\n    .hashtags {\n      margin-top: 8px;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n    }\n\n    .hashtag {\n      color: #1da1f2;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .hashtag:hover {\n      text-decoration: underline;\n    }\n\n    /* E-commerce Actions */\n    .ecommerce-actions {\n      display: flex;\n      gap: 8px;\n      padding: 12px 20px;\n      border-top: 1px solid #f0f0f0;\n    }\n\n    .ecom-btn {\n      flex: 1;\n      padding: 10px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      font-size: 0.85rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      transition: all 0.2s ease;\n    }\n\n    .buy-now {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .add-cart {\n      background: #4ecdc4;\n      color: #fff;\n    }\n\n    .wishlist {\n      background: #ff9ff3;\n      color: #fff;\n    }\n\n    .ecom-btn:hover {\n      transform: translateY(-1px);\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    /* Comments */\n    .comments-preview {\n      padding: 0 20px 12px;\n    }\n\n    .view-all-comments {\n      color: #666;\n      font-size: 0.9rem;\n      cursor: pointer;\n      margin-bottom: 8px;\n    }\n\n    .view-all-comments:hover {\n      text-decoration: underline;\n    }\n\n    .comment {\n      margin-bottom: 4px;\n      font-size: 0.9rem;\n      line-height: 1.3;\n    }\n\n    .comment-username {\n      font-weight: 600;\n      color: #333;\n      margin-right: 8px;\n    }\n\n    .comment-text {\n      color: #333;\n    }\n\n    /* Add Comment */\n    .add-comment {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 20px;\n      border-top: 1px solid #f0f0f0;\n    }\n\n    .comment-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n    }\n\n    .comment-input {\n      flex: 1;\n      border: none;\n      outline: none;\n      font-size: 0.9rem;\n      padding: 8px 0;\n    }\n\n    .comment-input::placeholder {\n      color: #999;\n    }\n\n    .btn-post-comment {\n      background: none;\n      border: none;\n      color: #1da1f2;\n      font-weight: 600;\n      cursor: pointer;\n      font-size: 0.9rem;\n      padding: 8px;\n    }\n\n    .btn-post-comment:disabled {\n      color: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Load More */\n    .load-more {\n      text-align: center;\n      padding: 40px 20px;\n    }\n\n    .btn-load-more {\n      background: #007bff;\n      color: #fff;\n      border: none;\n      padding: 12px 24px;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin: 0 auto;\n    }\n\n    .btn-load-more:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Product Modal */\n    .product-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0,0,0,0.8);\n      z-index: 1000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .modal-content {\n      background: #fff;\n      border-radius: 12px;\n      max-width: 400px;\n      width: 100%;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n    }\n\n    .btn-close {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      cursor: pointer;\n      color: #666;\n      padding: 4px;\n    }\n\n    .modal-body {\n      padding: 20px;\n    }\n\n    .product-image {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .product-info {\n      margin-bottom: 20px;\n    }\n\n    .brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    .modal-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n\n    .btn-primary, .btn-secondary, .btn-outline {\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: #fff;\n    }\n\n    .btn-secondary {\n      background: #6c757d;\n      color: #fff;\n    }\n\n    .btn-outline {\n      background: transparent;\n      color: #007bff;\n      border: 1px solid #007bff;\n    }\n\n    /* Responsive */\n    @media (max-width: 768px) {\n      .social-feed {\n        padding: 0 0 60px 0;\n      }\n\n      .stories-bar {\n        padding: 12px 0;\n      }\n\n      .stories-container {\n        padding: 0 16px;\n        gap: 12px;\n      }\n\n      .story-avatar {\n        width: 50px;\n        height: 50px;\n      }\n\n      .story-username {\n        font-size: 0.75rem;\n        max-width: 50px;\n      }\n\n      .post-header {\n        padding: 12px 16px;\n      }\n\n      .post-actions {\n        padding: 8px 16px 6px;\n      }\n\n      .post-stats, .post-caption, .comments-preview {\n        padding-left: 16px;\n        padding-right: 16px;\n      }\n\n      .ecommerce-actions {\n        padding: 8px 16px;\n        flex-direction: column;\n        gap: 6px;\n      }\n\n      .ecom-btn {\n        padding: 12px;\n        font-size: 0.9rem;\n      }\n\n      .add-comment {\n        padding: 8px 16px;\n      }\n    }\n  `]\n})\nexport class SocialFeedComponent implements OnInit {\n  posts: Post[] = [];\n  stories: any[] = [];\n  commentTexts: { [key: string]: string } = {};\n  selectedProduct: any = null;\n  currentUser: any = null;\n  loading = false;\n  hasMorePosts = true;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.loadStories();\n    this.loadPosts();\n  }\n\n  loadCurrentUser() {\n    // TODO: Get current user from auth service\n    this.currentUser = {\n      _id: 'current-user',\n      username: 'you',\n      fullName: 'Your Name',\n      avatar: ''\n    };\n  }\n\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories')\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          // Group stories by user\n          const userStories = data.stories.reduce((acc: any, story: any) => {\n            const userId = story.user._id;\n            if (!acc[userId]) {\n              acc[userId] = {\n                user: story.user,\n                viewed: false, // TODO: Check if current user viewed this user's stories\n                stories: []\n              };\n            }\n            acc[userId].stories.push(story);\n            return acc;\n          }, {});\n\n          this.stories = Object.values(userStories);\n        }\n      })\n      .catch(error => {\n        console.error('Error loading stories:', error);\n      });\n  }\n\n  loadPosts() {\n    this.loading = true;\n\n    // Load posts from real API\n    fetch('http://localhost:5000/api/posts')\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.posts = data.posts.map((post: any) => ({\n            ...post,\n            isLiked: false, // TODO: Check if current user liked this post\n            isSaved: false  // TODO: Check if current user saved this post\n          }));\n        }\n        this.loading = false;\n      })\n      .catch(error => {\n        console.error('Error loading posts:', error);\n        this.loading = false;\n      });\n  }\n\n  loadMorePosts() {\n    if (this.loading || !this.hasMorePosts) return;\n\n    this.loading = true;\n    const page = Math.floor(this.posts.length / 10) + 1;\n\n    // Load more posts from real API\n    fetch(`http://localhost:5000/api/posts?page=${page}&limit=10`)\n      .then(response => response.json())\n      .then(data => {\n        if (data.success && data.posts.length > 0) {\n          const newPosts = data.posts.map((post: any) => ({\n            ...post,\n            isLiked: false, // TODO: Check if current user liked this post\n            isSaved: false  // TODO: Check if current user saved this post\n          }));\n          this.posts = [...this.posts, ...newPosts];\n\n          // Check if there are more posts\n          this.hasMorePosts = data.posts.length === 10;\n        } else {\n          this.hasMorePosts = false;\n        }\n        this.loading = false;\n      })\n      .catch(error => {\n        console.error('Error loading more posts:', error);\n        this.loading = false;\n      });\n  }\n\n  // Stories actions\n  createStory() {\n    this.router.navigate(['/create-story']);\n  }\n\n  viewStory(userId: string) {\n    // Navigate to stories viewer with user ID\n    this.router.navigate(['/stories', userId]);\n  }\n\n  viewStories() {\n    // Navigate to general stories viewer\n    this.router.navigate(['/stories']);\n  }\n\n  // Post actions\n  viewProfile(userId: string) {\n    this.router.navigate(['/profile', userId]);\n  }\n\n  showPostMenu(post: Post) {\n    // TODO: Show post menu (report, share, etc.)\n    console.log('Show menu for post:', post);\n  }\n\n  toggleLike(post: Post) {\n    post.isLiked = !post.isLiked;\n\n    if (post.isLiked) {\n      post.likes.push({\n        user: this.currentUser._id,\n        likedAt: new Date()\n      });\n    } else {\n      post.likes = post.likes.filter(like => like.user !== this.currentUser._id);\n    }\n\n    // TODO: Update like status via API\n    console.log('Toggle like for post:', post._id, post.isLiked);\n  }\n\n  toggleSave(post: Post) {\n    post.isSaved = !post.isSaved;\n\n    if (post.isSaved) {\n      post.saves.push({\n        user: this.currentUser._id,\n        savedAt: new Date()\n      });\n    } else {\n      post.saves = post.saves.filter(save => save.user !== this.currentUser._id);\n    }\n\n    // TODO: Update save status via API\n    console.log('Toggle save for post:', post._id, post.isSaved);\n  }\n\n  sharePost(post: Post) {\n    // TODO: Implement share functionality\n    console.log('Share post:', post);\n\n    if (navigator.share) {\n      navigator.share({\n        title: `${post.user.username}'s post`,\n        text: post.caption,\n        url: window.location.href\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link copied to clipboard!');\n    }\n  }\n\n  focusComment(postId: string) {\n    const commentInput = document.getElementById(`comment-${postId}`) as HTMLInputElement;\n    if (commentInput) {\n      commentInput.focus();\n    }\n  }\n\n  addComment(post: Post) {\n    const commentText = this.commentTexts[post._id];\n    if (!commentText?.trim()) return;\n\n    const newComment = {\n      _id: Date.now().toString(),\n      user: {\n        _id: this.currentUser._id,\n        username: this.currentUser.username,\n        fullName: this.currentUser.fullName,\n        avatar: this.currentUser.avatar\n      },\n      text: commentText.trim(),\n      commentedAt: new Date()\n    };\n\n    post.comments.push(newComment);\n    this.commentTexts[post._id] = '';\n\n    // TODO: Add comment via API\n    console.log('Add comment to post:', post._id, newComment);\n  }\n\n  viewAllComments(post: Post) {\n    this.router.navigate(['/post', post._id, 'comments']);\n  }\n\n  searchHashtag(hashtag: string) {\n    this.router.navigate(['/search'], { queryParams: { hashtag } });\n  }\n\n  // E-commerce actions\n  buyNow(post: Post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: { productId: product._id, source: 'post' }\n      });\n    }\n  }\n\n  addToCart(post: Post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from post:', product);\n      alert(`${product.name} added to cart!`);\n    }\n  }\n\n  addToWishlist(post: Post) {\n    if (post.products.length > 0) {\n      const product = post.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from post:', product);\n      alert(`${product.name} added to wishlist!`);\n    }\n  }\n\n  // Product modal\n  showProductDetails(product: any) {\n    this.selectedProduct = product;\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: { productId: this.selectedProduct._id, source: 'post' }\n      });\n    }\n  }\n\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to cart!`);\n      this.closeProductModal();\n    }\n  }\n\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to wishlist!`);\n      this.closeProductModal();\n    }\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffMinutes < 1) return 'now';\n    if (diffMinutes < 60) return `${diffMinutes}m`;\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays < 7) return `${diffDays}d`;\n    return new Date(date).toLocaleDateString();\n  }\n\n  // Removed mock data - now using real API data from seeder\n}"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IAmElCC,EAAA,CAAAC,cAAA,cAEyC;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,QAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAP,QAAA,CAAAQ,IAAA,CAAAC,GAAA,CAAyB;IAAA,EAAC;IACtCb,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAc,SAAA,cACiC;IACnCd,EAAA,CAAAe,YAAA,EAAM;IACNf,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAgB,MAAA,GAAyB;IACxDhB,EADwD,CAAAe,YAAA,EAAO,EACzD;;;;IALsBf,EAAA,CAAAiB,SAAA,EAA6B;IAA7BjB,EAAA,CAAAkB,WAAA,WAAAd,QAAA,CAAAe,MAAA,CAA6B;IAChDnB,EAAA,CAAAiB,SAAA,EAAgE;IAChEjB,EADA,CAAAoB,UAAA,QAAAhB,QAAA,CAAAQ,IAAA,CAAAS,MAAA,yCAAArB,EAAA,CAAAsB,aAAA,CAAgE,QAAAlB,QAAA,CAAAQ,IAAA,CAAAW,QAAA,CACrC;IAELvB,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAwB,iBAAA,CAAApB,QAAA,CAAAQ,IAAA,CAAAa,QAAA,CAAyB;;;;;IAkBhDzB,EAAA,CAAAc,SAAA,YAAyE;;;;;IAgB7Ed,EAAA,CAAAc,SAAA,cAGwB;;;;IADnBd,EADA,CAAAoB,UAAA,QAAAM,QAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAAsB,aAAA,CAAiB,QAAAI,QAAA,CAAAE,GAAA,CACA;;;;;IAGtB5B,EAAA,CAAAc,SAAA,gBAKQ;;;;IADDd,EAHA,CAAAoB,UAAA,QAAAM,QAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAAsB,aAAA,CAAiB,eAGH;;;;;IAVvBtB,EAAA,CAAAC,cAAA,cAA6E;IAM3ED,EALA,CAAA6B,UAAA,IAAAC,gDAAA,kBAGwB,IAAAC,kDAAA,oBAMF;IAExB/B,EAAA,CAAAe,YAAA,EAAM;;;;IAXEf,EAAA,CAAAiB,SAAA,EAA4B;IAA5BjB,EAAA,CAAAoB,UAAA,SAAAM,QAAA,CAAAM,IAAA,aAA4B;IAK1BhC,EAAA,CAAAiB,SAAA,EAA4B;IAA5BjB,EAAA,CAAAoB,UAAA,SAAAM,QAAA,CAAAM,IAAA,aAA4B;;;;;;IAUpChC,EAAA,CAAAC,cAAA,cAIsD;IAAjDD,EAAA,CAAAE,UAAA,mBAAA+B,sEAAA;MAAA,MAAAC,aAAA,GAAAlC,EAAA,CAAAK,aAAA,CAAA8B,GAAA,EAAA5B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4B,kBAAA,CAAAF,aAAA,CAAAG,OAAA,CAAsC;IAAA,EAAC;IACnDrC,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAc,SAAA,YAAmC;IAEvCd,EADE,CAAAe,YAAA,EAAM,EACF;;;;IALDf,EADA,CAAAsC,WAAA,SAAAJ,aAAA,CAAAK,QAAA,CAAAC,CAAA,MAAsC,QAAAN,aAAA,CAAAK,QAAA,CAAAE,CAAA,MACD;;;;;IAJ5CzC,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAA6B,UAAA,IAAAa,gDAAA,kBAIsD;IAKxD1C,EAAA,CAAAe,YAAA,EAAM;;;;IARwBf,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAoB,UAAA,YAAAuB,OAAA,CAAAC,QAAA,CAAgB;;;;;IAa1C5C,EAAA,CAAAc,SAAA,eAEsC;;;;IAAhCd,EAAA,CAAAkB,WAAA,WAAA2B,IAAA,OAAwB;;;;;IAHhC7C,EADF,CAAAC,cAAA,cAAqD,cAC7B;IACpBD,EAAA,CAAA6B,UAAA,IAAAiB,iDAAA,mBAE+B;IAEnC9C,EADE,CAAAe,YAAA,EAAM,EACF;;;;IAHsBf,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAoB,UAAA,YAAAuB,OAAA,CAAAI,KAAA,CAAe;;;;;IAoCzC/C,EADF,CAAAC,cAAA,cAAuD,aAC7C;IAAAD,EAAA,CAAAgB,MAAA,GAAsC;;IAChDhB,EADgD,CAAAe,YAAA,EAAS,EACnD;;;;IADIf,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAAgD,kBAAA,KAAAhD,EAAA,CAAAiD,WAAA,OAAAN,OAAA,CAAAO,KAAA,CAAAC,MAAA,YAAsC;;;;;;IAU9CnD,EAAA,CAAAC,cAAA,eAEuC;IAAjCD,EAAA,CAAAE,UAAA,mBAAAkD,wEAAA;MAAA,MAAAC,WAAA,GAAArD,EAAA,CAAAK,aAAA,CAAAiD,IAAA,EAAA/C,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+C,aAAA,CAAAF,WAAA,CAAsB;IAAA,EAAC;IACpCrD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAe,YAAA,EAAO;;;;IADLf,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAgD,kBAAA,OAAAK,WAAA,MACF;;;;;IALFrD,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAA6B,UAAA,IAAA2B,iDAAA,mBAEuC;IAGzCxD,EAAA,CAAAe,YAAA,EAAM;;;;IAJsBf,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAoB,UAAA,YAAAuB,OAAA,CAAAc,QAAA,CAAgB;;;;;;IAS5CzD,EADF,CAAAC,cAAA,cAAgE,iBACN;IAAvBD,EAAA,CAAAE,UAAA,mBAAAwD,mEAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAhB,OAAA,GAAA3C,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoD,MAAA,CAAAjB,OAAA,CAAY;IAAA,EAAC;IACrD3C,EAAA,CAAAc,SAAA,YAA2B;IAC3Bd,EAAA,CAAAgB,MAAA,gBACF;IAAAhB,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAC,cAAA,iBAA4D;IAA1BD,EAAA,CAAAE,UAAA,mBAAA2D,mEAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAhB,OAAA,GAAA3C,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsD,SAAA,CAAAnB,OAAA,CAAe;IAAA,EAAC;IACzD3C,EAAA,CAAAc,SAAA,YAAoC;IACpCd,EAAA,CAAAgB,MAAA,oBACF;IAAAhB,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAC,cAAA,iBAAgE;IAA9BD,EAAA,CAAAE,UAAA,mBAAA6D,mEAAA;MAAA/D,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAAhB,OAAA,GAAA3C,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAwD,aAAA,CAAArB,OAAA,CAAmB;IAAA,EAAC;IAC7D3C,EAAA,CAAAc,SAAA,YAA4B;IAC5Bd,EAAA,CAAAgB,MAAA,iBACF;IACFhB,EADE,CAAAe,YAAA,EAAS,EACL;;;;;;IAIJf,EAAA,CAAAC,cAAA,cAEqC;IAAhCD,EAAA,CAAAE,UAAA,mBAAA+D,sEAAA;MAAAjE,EAAA,CAAAK,aAAA,CAAA6D,IAAA;MAAA,MAAAvB,OAAA,GAAA3C,EAAA,CAAAS,aAAA,IAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA2D,eAAA,CAAAxB,OAAA,CAAqB;IAAA,EAAC;IAClC3C,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAe,YAAA,EAAM;;;;IADJf,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAgD,kBAAA,eAAAL,OAAA,CAAAyB,QAAA,CAAAjB,MAAA,eACF;;;;;IAIEnD,EAFF,CAAAC,cAAA,cACqD,eACpB;IAAAD,EAAA,CAAAgB,MAAA,GAA2B;IAAAhB,EAAA,CAAAe,YAAA,EAAO;IACjEf,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAgB,MAAA,GAAkB;IAC/ChB,EAD+C,CAAAe,YAAA,EAAO,EAChD;;;;IAF2Bf,EAAA,CAAAiB,SAAA,GAA2B;IAA3BjB,EAAA,CAAAwB,iBAAA,CAAA6C,WAAA,CAAAzD,IAAA,CAAAa,QAAA,CAA2B;IAC/BzB,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAwB,iBAAA,CAAA6C,WAAA,CAAAC,IAAA,CAAkB;;;;;IAVjDtE,EAAA,CAAAC,cAAA,cAA+D;IAO7DD,EANA,CAAA6B,UAAA,IAAA0C,gDAAA,kBAEqC,IAAAC,gDAAA,kBAKgB;IAIvDxE,EAAA,CAAAe,YAAA,EAAM;;;;IAVEf,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAAyB,QAAA,CAAAjB,MAAA,KAA8B;IAMXnD,EAAA,CAAAiB,SAAA,EAA0B;IAA1BjB,EAAA,CAAAoB,UAAA,YAAAuB,OAAA,CAAAyB,QAAA,CAAAK,KAAA,KAA0B;;;;;;IApIjDzE,EAJN,CAAAC,cAAA,cAAkD,cAEvB,cACA,cAIqB;IAArCD,EAAA,CAAAE,UAAA,mBAAAwE,yDAAA;MAAA,MAAA/B,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoE,WAAA,CAAAjC,OAAA,CAAA/B,IAAA,CAAAC,GAAA,CAA0B;IAAA,EAAC;IAHzCb,EAAA,CAAAe,YAAA,EAG0C;IAGtCf,EAFJ,CAAAC,cAAA,cAA0B,cACE,eACoC;IAArCD,EAAA,CAAAE,UAAA,mBAAA2E,0DAAA;MAAA,MAAAlC,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoE,WAAA,CAAAjC,OAAA,CAAA/B,IAAA,CAAAC,GAAA,CAA0B;IAAA,EAAC;IAACb,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAe,YAAA,EAAO;IAC3Ff,EAAA,CAAA6B,UAAA,IAAAiD,uCAAA,gBAAqE;IACvE9E,EAAA,CAAAe,YAAA,EAAM;IACNf,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAgB,MAAA,IAAgC;IAE5DhB,EAF4D,CAAAe,YAAA,EAAO,EAC3D,EACF;IAGJf,EADF,CAAAC,cAAA,eAAuB,kBACiC;IAA7BD,EAAA,CAAAE,UAAA,mBAAA6E,6DAAA;MAAA,MAAApC,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAwE,YAAA,CAAArC,OAAA,CAAkB;IAAA,EAAC;IACnD3C,EAAA,CAAAc,SAAA,aAAiC;IAGvCd,EAFI,CAAAe,YAAA,EAAS,EACL,EACF;IAGNf,EAAA,CAAAC,cAAA,eAAsD;IAA9BD,EAAA,CAAAE,UAAA,sBAAA+E,6DAAA;MAAA,MAAAtC,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAA0E,UAAA,CAAAvC,OAAA,CAAgB;IAAA,EAAC;IA6BnD3C,EA5BA,CAAA6B,UAAA,KAAAsD,0CAAA,kBAA6E,KAAAC,0CAAA,kBAelB,KAAAC,0CAAA,kBAaN;IAOvDrF,EAAA,CAAAe,YAAA,EAAM;IAKFf,EAFJ,CAAAC,cAAA,eAA0B,eACK,kBAGQ;IAA3BD,EAAA,CAAAE,UAAA,mBAAAoF,6DAAA;MAAA,MAAA3C,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA0E,UAAA,CAAAvC,OAAA,CAAgB;IAAA,EAAC;IAChC3C,EAAA,CAAAc,SAAA,aAA4B;IAC9Bd,EAAA,CAAAe,YAAA,EAAS;IAETf,EAAA,CAAAC,cAAA,kBAAoE;IAAjCD,EAAA,CAAAE,UAAA,mBAAAqF,6DAAA;MAAA,MAAA5C,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgF,YAAA,CAAA7C,OAAA,CAAA9B,GAAA,CAAsB;IAAA,EAAC;IACjEb,EAAA,CAAAc,SAAA,aAA8B;IAChCd,EAAA,CAAAe,YAAA,EAAS;IAETf,EAAA,CAAAC,cAAA,kBAA2D;IAA1BD,EAAA,CAAAE,UAAA,mBAAAuF,6DAAA;MAAA,MAAA9C,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAkF,SAAA,CAAA/C,OAAA,CAAe;IAAA,EAAC;IACxD3C,EAAA,CAAAc,SAAA,aAAkC;IAEtCd,EADE,CAAAe,YAAA,EAAS,EACL;IAGJf,EADF,CAAAC,cAAA,eAA+B,kBAGM;IAA3BD,EAAA,CAAAE,UAAA,mBAAAyF,6DAAA;MAAA,MAAAhD,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoF,UAAA,CAAAjD,OAAA,CAAgB;IAAA,EAAC;IAChC3C,EAAA,CAAAc,SAAA,aAA+B;IAGrCd,EAFI,CAAAe,YAAA,EAAS,EACL,EACF;IAGNf,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA6B,UAAA,KAAAgE,0CAAA,kBAAuD;IAGzD7F,EAAA,CAAAe,YAAA,EAAM;IAIJf,EADF,CAAAC,cAAA,eAA0B,gBACD;IAAAD,EAAA,CAAAgB,MAAA,IAAwB;IAAAhB,EAAA,CAAAe,YAAA,EAAO;IACtDf,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAgB,MAAA,IAAkB;IAAAhB,EAAA,CAAAe,YAAA,EAAO;IAEpDf,EAAA,CAAA6B,UAAA,KAAAiE,0CAAA,kBAAuD;IAOzD9F,EAAA,CAAAe,YAAA,EAAM;IAmBNf,EAhBA,CAAA6B,UAAA,KAAAkE,0CAAA,mBAAgE,KAAAC,0CAAA,kBAgBD;IAe/DhG,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAc,SAAA,eAE4B;IAC5Bd,EAAA,CAAAC,cAAA,iBAK6B;IAHtBD,EAAA,CAAAiG,gBAAA,2BAAAC,oEAAAC,MAAA;MAAA,MAAAxD,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAoG,kBAAA,CAAA5F,MAAA,CAAA6F,YAAA,CAAA1D,OAAA,CAAA9B,GAAA,GAAAsF,MAAA,MAAA3F,MAAA,CAAA6F,YAAA,CAAA1D,OAAA,CAAA9B,GAAA,IAAAsF,MAAA;MAAA,OAAAnG,EAAA,CAAAU,WAAA,CAAAyF,MAAA;IAAA,EAAoC;IAEpCnG,EAAA,CAAAE,UAAA,yBAAAoG,kEAAA;MAAA,MAAA3D,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAeF,MAAA,CAAA+F,UAAA,CAAA5D,OAAA,CAAgB;IAAA,EAAC;IAJvC3C,EAAA,CAAAe,YAAA,EAK6B;IAC7Bf,EAAA,CAAAC,cAAA,kBAE+E;IADvED,EAAA,CAAAE,UAAA,mBAAAsG,6DAAA;MAAA,MAAA7D,OAAA,GAAA3C,EAAA,CAAAK,aAAA,CAAAsE,GAAA,EAAApE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+F,UAAA,CAAA5D,OAAA,CAAgB;IAAA,EAAC;IAEhC3C,EAAA,CAAAgB,MAAA,cACF;IAEJhB,EAFI,CAAAe,YAAA,EAAS,EACL,EACF;;;;;IA3JKf,EAAA,CAAAiB,SAAA,GAA+D;IAC/DjB,EADA,CAAAoB,UAAA,QAAAuB,OAAA,CAAA/B,IAAA,CAAAS,MAAA,yCAAArB,EAAA,CAAAsB,aAAA,CAA+D,QAAAqB,OAAA,CAAA/B,IAAA,CAAAW,QAAA,CACrC;IAKiCvB,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAwB,iBAAA,CAAAmB,OAAA,CAAA/B,IAAA,CAAAa,QAAA,CAAwB;IAC3CzB,EAAA,CAAAiB,SAAA,EAA0B;IAA1BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAA/B,IAAA,CAAA6F,UAAA,CAA0B;IAE7CzG,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAwB,iBAAA,CAAAhB,MAAA,CAAAkG,UAAA,CAAA/D,OAAA,CAAAgE,SAAA,EAAgC;IAab3G,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAoB,UAAA,YAAAuB,OAAA,CAAAI,KAAA,CAAe;IAenC/C,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAAC,QAAA,CAAAO,MAAA,KAA8B;IAajCnD,EAAA,CAAAiB,SAAA,EAA2B;IAA3BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAAI,KAAA,CAAAI,MAAA,KAA2B;IAazCnD,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,WAAA,UAAAyB,OAAA,CAAAiE,OAAA,CAA4B;IAgB5B5G,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,WAAA,UAAAyB,OAAA,CAAAkE,OAAA,CAA4B;IASZ7G,EAAA,CAAAiB,SAAA,GAA2B;IAA3BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAAO,KAAA,CAAAC,MAAA,KAA2B;IAO9BnD,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAwB,iBAAA,CAAAmB,OAAA,CAAA/B,IAAA,CAAAa,QAAA,CAAwB;IACpBzB,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAwB,iBAAA,CAAAmB,OAAA,CAAAmE,OAAA,CAAkB;IAEtB9G,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAAc,QAAA,CAAAN,MAAA,KAA8B;IAUvBnD,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAAC,QAAA,CAAAO,MAAA,KAA8B;IAgB/BnD,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAoB,UAAA,SAAAuB,OAAA,CAAAyB,QAAA,CAAAjB,MAAA,KAA8B;IAgBtDnD,EAAA,CAAAiB,SAAA,GAAkE;IAClEjB,EADA,CAAAoB,UAAA,SAAAZ,MAAA,CAAAuG,WAAA,kBAAAvG,MAAA,CAAAuG,WAAA,CAAA1F,MAAA,0CAAArB,EAAA,CAAAsB,aAAA,CAAkE,QAAAd,MAAA,CAAAuG,WAAA,kBAAAvG,MAAA,CAAAuG,WAAA,CAAAxF,QAAA,CACrC;IAG3BvB,EAAA,CAAAiB,SAAA,EAA4B;IAA5BjB,EAAA,CAAAoB,UAAA,oBAAAuB,OAAA,CAAA9B,GAAA,CAA4B;IAC5Bb,EAAA,CAAAgH,gBAAA,YAAAxG,MAAA,CAAA6F,YAAA,CAAA1D,OAAA,CAAA9B,GAAA,EAAoC;IAMnCb,EAAA,CAAAiB,SAAA,EAAsE;IAAtEjB,EAAA,CAAAoB,UAAA,cAAAZ,MAAA,CAAA6F,YAAA,CAAA1D,OAAA,CAAA9B,GAAA,MAAAL,MAAA,CAAA6F,YAAA,CAAA1D,OAAA,CAAA9B,GAAA,EAAAoG,IAAA,GAAsE;;;;;IAUhFjH,EAAA,CAAAc,SAAA,YAAsD;;;;;;IADxDd,EADF,CAAAC,cAAA,cAA4C,iBACmC;IAA/CD,EAAA,CAAAE,UAAA,mBAAAgH,4DAAA;MAAAlH,EAAA,CAAAK,aAAA,CAAA8G,IAAA;MAAA,MAAA3G,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4G,aAAA,EAAe;IAAA,EAAC;IACrDpH,EAAA,CAAA6B,UAAA,IAAAwF,uCAAA,gBAAkD;IAClDrH,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAe,YAAA,EAAS,EACL;;;;IAJoDf,EAAA,CAAAiB,SAAA,EAAoB;IAApBjB,EAAA,CAAAoB,UAAA,aAAAZ,MAAA,CAAA8G,OAAA,CAAoB;IACvCtH,EAAA,CAAAiB,SAAA,EAAa;IAAbjB,EAAA,CAAAoB,UAAA,SAAAZ,MAAA,CAAA8G,OAAA,CAAa;IAChDtH,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAgD,kBAAA,MAAAxC,MAAA,CAAA8G,OAAA,yCACF;;;;;IAuBMtH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAgB,MAAA,GACF;;IAAAhB,EAAA,CAAAe,YAAA,EAAO;;;;IADLf,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAgD,kBAAA,YAAAhD,EAAA,CAAAuH,WAAA,OAAA/G,MAAA,CAAAgH,eAAA,CAAAC,aAAA,gBACF;;;;;;IApBVzH,EAAA,CAAAC,cAAA,cAAiF;IAA9BD,EAAA,CAAAE,UAAA,mBAAAwH,yDAAA;MAAA1H,EAAA,CAAAK,aAAA,CAAAsH,IAAA;MAAA,MAAAnH,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoH,iBAAA,EAAmB;IAAA,EAAC;IAC9E5H,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAE,UAAA,mBAAA2H,yDAAA1B,MAAA;MAAAnG,EAAA,CAAAK,aAAA,CAAAsH,IAAA;MAAA,OAAA3H,EAAA,CAAAU,WAAA,CAASyF,MAAA,CAAA2B,eAAA,EAAwB;IAAA,EAAC;IAEzD9H,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAgB,MAAA,GAA0B;IAAAhB,EAAA,CAAAe,YAAA,EAAK;IACnCf,EAAA,CAAAC,cAAA,iBAAwD;IAA9BD,EAAA,CAAAE,UAAA,mBAAA6H,4DAAA;MAAA/H,EAAA,CAAAK,aAAA,CAAAsH,IAAA;MAAA,MAAAnH,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoH,iBAAA,EAAmB;IAAA,EAAC;IACrD5H,EAAA,CAAAc,SAAA,YAA4B;IAEhCd,EADE,CAAAe,YAAA,EAAS,EACL;IAENf,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAc,SAAA,cAE2B;IAGzBd,EADF,CAAAC,cAAA,cAA0B,aACP;IAAAD,EAAA,CAAAgB,MAAA,IAA2B;IAAAhB,EAAA,CAAAe,YAAA,EAAI;IAE9Cf,EADF,CAAAC,cAAA,eAAmB,iBACW;IAAAD,EAAA,CAAAgB,MAAA,IAA6C;;IAAAhB,EAAA,CAAAe,YAAA,EAAO;IAChFf,EAAA,CAAA6B,UAAA,KAAAmG,2CAAA,oBAAmE;IAIvEhI,EADE,CAAAe,YAAA,EAAM,EACF;IAGJf,EADF,CAAAC,cAAA,gBAA2B,mBAC6B;IAA1BD,EAAA,CAAAE,UAAA,mBAAA+H,6DAAA;MAAAjI,EAAA,CAAAK,aAAA,CAAAsH,IAAA;MAAA,MAAAnH,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA0H,aAAA,EAAe;IAAA,EAAC;IAAClI,EAAA,CAAAgB,MAAA,eAAO;IAAAhB,EAAA,CAAAe,YAAA,EAAS;IACtEf,EAAA,CAAAC,cAAA,mBAA2D;IAA7BD,EAAA,CAAAE,UAAA,mBAAAiI,6DAAA;MAAAnI,EAAA,CAAAK,aAAA,CAAAsH,IAAA;MAAA,MAAAnH,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4H,gBAAA,EAAkB;IAAA,EAAC;IAACpI,EAAA,CAAAgB,MAAA,mBAAW;IAAAhB,EAAA,CAAAe,YAAA,EAAS;IAC/Ef,EAAA,CAAAC,cAAA,mBAA6D;IAAjCD,EAAA,CAAAE,UAAA,mBAAAmI,6DAAA;MAAArI,EAAA,CAAAK,aAAA,CAAAsH,IAAA;MAAA,MAAAnH,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8H,oBAAA,EAAsB;IAAA,EAAC;IAACtI,EAAA,CAAAgB,MAAA,uBAAe;IAIpFhB,EAJoF,CAAAe,YAAA,EAAS,EACjF,EACF,EACF,EACF;;;;IA5BIf,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAwB,iBAAA,CAAAhB,MAAA,CAAAgH,eAAA,CAAAe,IAAA,CAA0B;IAOzBvI,EAAA,CAAAiB,SAAA,GAAsC;IACtCjB,EADA,CAAAoB,UAAA,QAAAZ,MAAA,CAAAgH,eAAA,CAAAgB,MAAA,qBAAAhI,MAAA,CAAAgH,eAAA,CAAAgB,MAAA,IAAA7G,GAAA,EAAA3B,EAAA,CAAAsB,aAAA,CAAsC,QAAAd,MAAA,CAAAgH,eAAA,CAAAe,IAAA,CACV;IAIdvI,EAAA,CAAAiB,SAAA,GAA2B;IAA3BjB,EAAA,CAAAwB,iBAAA,CAAAhB,MAAA,CAAAgH,eAAA,CAAAiB,KAAA,CAA2B;IAEdzI,EAAA,CAAAiB,SAAA,GAA6C;IAA7CjB,EAAA,CAAAgD,kBAAA,WAAAhD,EAAA,CAAAuH,WAAA,QAAA/G,MAAA,CAAAgH,eAAA,CAAAkB,KAAA,eAA6C;IAC3C1I,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAoB,UAAA,SAAAZ,MAAA,CAAAgH,eAAA,CAAAC,aAAA,CAAmC;;;AAkoB/E,OAAM,MAAOkB,mBAAmB;EAS9BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAR1B,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAA1C,YAAY,GAA8B,EAAE;IAC5C,KAAAmB,eAAe,GAAQ,IAAI;IAC3B,KAAAT,WAAW,GAAQ,IAAI;IACvB,KAAAO,OAAO,GAAG,KAAK;IACf,KAAA0B,YAAY,GAAG,IAAI;EAEkB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAF,eAAeA,CAAA;IACb;IACA,IAAI,CAACnC,WAAW,GAAG;MACjBlG,GAAG,EAAE,cAAc;MACnBY,QAAQ,EAAE,KAAK;MACfF,QAAQ,EAAE,WAAW;MACrBF,MAAM,EAAE;KACT;EACH;EAEA8H,WAAWA,CAAA;IACT;IACAE,KAAK,CAAC,mCAAmC,CAAC,CACvCC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB;QACA,MAAMC,WAAW,GAAGF,IAAI,CAACV,OAAO,CAACa,MAAM,CAAC,CAACC,GAAQ,EAAEC,KAAU,KAAI;UAC/D,MAAMC,MAAM,GAAGD,KAAK,CAAClJ,IAAI,CAACC,GAAG;UAC7B,IAAI,CAACgJ,GAAG,CAACE,MAAM,CAAC,EAAE;YAChBF,GAAG,CAACE,MAAM,CAAC,GAAG;cACZnJ,IAAI,EAAEkJ,KAAK,CAAClJ,IAAI;cAChBO,MAAM,EAAE,KAAK;cACb4H,OAAO,EAAE;aACV;;UAEHc,GAAG,CAACE,MAAM,CAAC,CAAChB,OAAO,CAACiB,IAAI,CAACF,KAAK,CAAC;UAC/B,OAAOD,GAAG;QACZ,CAAC,EAAE,EAAE,CAAC;QAEN,IAAI,CAACd,OAAO,GAAGkB,MAAM,CAACC,MAAM,CAACP,WAAW,CAAC;;IAE7C,CAAC,CAAC,CACDQ,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,CAAC;EACN;EAEAhB,SAASA,CAAA;IACP,IAAI,CAAC9B,OAAO,GAAG,IAAI;IAEnB;IACA+B,KAAK,CAAC,iCAAiC,CAAC,CACrCC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAACZ,KAAK,GAAGW,IAAI,CAACX,KAAK,CAACwB,GAAG,CAAEC,IAAS,KAAM;UAC1C,GAAGA,IAAI;UACP3D,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK,CAAE;SACjB,CAAC,CAAC;;MAEL,IAAI,CAACS,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC,CACD6C,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,IAAI,CAAC9C,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACN;EAEAF,aAAaA,CAAA;IACX,IAAI,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAAC0B,YAAY,EAAE;IAExC,IAAI,CAAC1B,OAAO,GAAG,IAAI;IACnB,MAAMkD,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC5B,KAAK,CAAC3F,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;IAEnD;IACAkG,KAAK,CAAC,wCAAwCmB,IAAI,WAAW,CAAC,CAC3DlB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,IAAID,IAAI,CAACX,KAAK,CAAC3F,MAAM,GAAG,CAAC,EAAE;QACzC,MAAMwH,QAAQ,GAAGlB,IAAI,CAACX,KAAK,CAACwB,GAAG,CAAEC,IAAS,KAAM;UAC9C,GAAGA,IAAI;UACP3D,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK,CAAE;SACjB,CAAC,CAAC;QACH,IAAI,CAACiC,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAG6B,QAAQ,CAAC;QAEzC;QACA,IAAI,CAAC3B,YAAY,GAAGS,IAAI,CAACX,KAAK,CAAC3F,MAAM,KAAK,EAAE;OAC7C,MAAM;QACL,IAAI,CAAC6F,YAAY,GAAG,KAAK;;MAE3B,IAAI,CAAC1B,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC,CACD6C,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,IAAI,CAAC9C,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACN;EAEA;EACAsD,WAAWA,CAAA;IACT,IAAI,CAAC/B,MAAM,CAACgC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAlK,SAASA,CAACoJ,MAAc;IACtB;IACA,IAAI,CAAClB,MAAM,CAACgC,QAAQ,CAAC,CAAC,UAAU,EAAEd,MAAM,CAAC,CAAC;EAC5C;EAEAe,WAAWA,CAAA;IACT;IACA,IAAI,CAACjC,MAAM,CAACgC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;EACAjG,WAAWA,CAACmF,MAAc;IACxB,IAAI,CAAClB,MAAM,CAACgC,QAAQ,CAAC,CAAC,UAAU,EAAEd,MAAM,CAAC,CAAC;EAC5C;EAEA/E,YAAYA,CAACuF,IAAU;IACrB;IACAF,OAAO,CAACU,GAAG,CAAC,qBAAqB,EAAER,IAAI,CAAC;EAC1C;EAEArF,UAAUA,CAACqF,IAAU;IACnBA,IAAI,CAAC3D,OAAO,GAAG,CAAC2D,IAAI,CAAC3D,OAAO;IAE5B,IAAI2D,IAAI,CAAC3D,OAAO,EAAE;MAChB2D,IAAI,CAACrH,KAAK,CAAC8G,IAAI,CAAC;QACdpJ,IAAI,EAAE,IAAI,CAACmG,WAAW,CAAClG,GAAG;QAC1BmK,OAAO,EAAE,IAAIC,IAAI;OAClB,CAAC;KACH,MAAM;MACLV,IAAI,CAACrH,KAAK,GAAGqH,IAAI,CAACrH,KAAK,CAACgI,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvK,IAAI,KAAK,IAAI,CAACmG,WAAW,CAAClG,GAAG,CAAC;;IAG5E;IACAwJ,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAER,IAAI,CAAC1J,GAAG,EAAE0J,IAAI,CAAC3D,OAAO,CAAC;EAC9D;EAEAhB,UAAUA,CAAC2E,IAAU;IACnBA,IAAI,CAAC1D,OAAO,GAAG,CAAC0D,IAAI,CAAC1D,OAAO;IAE5B,IAAI0D,IAAI,CAAC1D,OAAO,EAAE;MAChB0D,IAAI,CAACa,KAAK,CAACpB,IAAI,CAAC;QACdpJ,IAAI,EAAE,IAAI,CAACmG,WAAW,CAAClG,GAAG;QAC1BwK,OAAO,EAAE,IAAIJ,IAAI;OAClB,CAAC;KACH,MAAM;MACLV,IAAI,CAACa,KAAK,GAAGb,IAAI,CAACa,KAAK,CAACF,MAAM,CAACI,IAAI,IAAIA,IAAI,CAAC1K,IAAI,KAAK,IAAI,CAACmG,WAAW,CAAClG,GAAG,CAAC;;IAG5E;IACAwJ,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAER,IAAI,CAAC1J,GAAG,EAAE0J,IAAI,CAAC1D,OAAO,CAAC;EAC9D;EAEAnB,SAASA,CAAC6E,IAAU;IAClB;IACAF,OAAO,CAACU,GAAG,CAAC,aAAa,EAAER,IAAI,CAAC;IAEhC,IAAIgB,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,GAAGlB,IAAI,CAAC3J,IAAI,CAACa,QAAQ,SAAS;QACrC6C,IAAI,EAAEiG,IAAI,CAACzD,OAAO;QAClBnF,GAAG,EAAE+J,MAAM,CAACC,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACL;MACAL,SAAS,CAACM,SAAS,CAACC,SAAS,CAACJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MACnDG,KAAK,CAAC,2BAA2B,CAAC;;EAEtC;EAEAvG,YAAYA,CAACwG,MAAc;IACzB,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAWH,MAAM,EAAE,CAAqB;IACrF,IAAIC,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,EAAE;;EAExB;EAEA7F,UAAUA,CAACgE,IAAU;IACnB,MAAM8B,WAAW,GAAG,IAAI,CAAChG,YAAY,CAACkE,IAAI,CAAC1J,GAAG,CAAC;IAC/C,IAAI,CAACwL,WAAW,EAAEpF,IAAI,EAAE,EAAE;IAE1B,MAAMqF,UAAU,GAAG;MACjBzL,GAAG,EAAEoK,IAAI,CAACsB,GAAG,EAAE,CAACC,QAAQ,EAAE;MAC1B5L,IAAI,EAAE;QACJC,GAAG,EAAE,IAAI,CAACkG,WAAW,CAAClG,GAAG;QACzBY,QAAQ,EAAE,IAAI,CAACsF,WAAW,CAACtF,QAAQ;QACnCF,QAAQ,EAAE,IAAI,CAACwF,WAAW,CAACxF,QAAQ;QACnCF,MAAM,EAAE,IAAI,CAAC0F,WAAW,CAAC1F;OAC1B;MACDiD,IAAI,EAAE+H,WAAW,CAACpF,IAAI,EAAE;MACxBwF,WAAW,EAAE,IAAIxB,IAAI;KACtB;IAEDV,IAAI,CAACnG,QAAQ,CAAC4F,IAAI,CAACsC,UAAU,CAAC;IAC9B,IAAI,CAACjG,YAAY,CAACkE,IAAI,CAAC1J,GAAG,CAAC,GAAG,EAAE;IAEhC;IACAwJ,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAER,IAAI,CAAC1J,GAAG,EAAEyL,UAAU,CAAC;EAC3D;EAEAnI,eAAeA,CAACoG,IAAU;IACxB,IAAI,CAAC1B,MAAM,CAACgC,QAAQ,CAAC,CAAC,OAAO,EAAEN,IAAI,CAAC1J,GAAG,EAAE,UAAU,CAAC,CAAC;EACvD;EAEA0C,aAAaA,CAACmJ,OAAe;IAC3B,IAAI,CAAC7D,MAAM,CAACgC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAAE8B,WAAW,EAAE;QAAED;MAAO;IAAE,CAAE,CAAC;EACjE;EAEA;EACA9I,MAAMA,CAAC2G,IAAU;IACf,IAAIA,IAAI,CAAC3H,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMd,OAAO,GAAGkI,IAAI,CAAC3H,QAAQ,CAAC,CAAC,CAAC,CAACP,OAAO;MACxC,IAAI,CAACwG,MAAM,CAACgC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClC8B,WAAW,EAAE;UAAEC,SAAS,EAAEvK,OAAO,CAACxB,GAAG;UAAEgM,MAAM,EAAE;QAAM;OACtD,CAAC;;EAEN;EAEA/I,SAASA,CAACyG,IAAU;IAClB,IAAIA,IAAI,CAAC3H,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMd,OAAO,GAAGkI,IAAI,CAAC3H,QAAQ,CAAC,CAAC,CAAC,CAACP,OAAO;MACxC;MACAgI,OAAO,CAACU,GAAG,CAAC,wBAAwB,EAAE1I,OAAO,CAAC;MAC9C0J,KAAK,CAAC,GAAG1J,OAAO,CAACkG,IAAI,iBAAiB,CAAC;;EAE3C;EAEAvE,aAAaA,CAACuG,IAAU;IACtB,IAAIA,IAAI,CAAC3H,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMd,OAAO,GAAGkI,IAAI,CAAC3H,QAAQ,CAAC,CAAC,CAAC,CAACP,OAAO;MACxC;MACAgI,OAAO,CAACU,GAAG,CAAC,4BAA4B,EAAE1I,OAAO,CAAC;MAClD0J,KAAK,CAAC,GAAG1J,OAAO,CAACkG,IAAI,qBAAqB,CAAC;;EAE/C;EAEA;EACAnG,kBAAkBA,CAACC,OAAY;IAC7B,IAAI,CAACmF,eAAe,GAAGnF,OAAO;EAChC;EAEAuF,iBAAiBA,CAAA;IACf,IAAI,CAACJ,eAAe,GAAG,IAAI;EAC7B;EAEAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAACqB,MAAM,CAACgC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClC8B,WAAW,EAAE;UAAEC,SAAS,EAAE,IAAI,CAACpF,eAAe,CAAC3G,GAAG;UAAEgM,MAAM,EAAE;QAAM;OACnE,CAAC;;EAEN;EAEAzE,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACZ,eAAe,EAAE;MACxB;MACA6C,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACvD,eAAe,CAAC;MACzDuE,KAAK,CAAC,GAAG,IAAI,CAACvE,eAAe,CAACe,IAAI,iBAAiB,CAAC;MACpD,IAAI,CAACX,iBAAiB,EAAE;;EAE5B;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACd,eAAe,EAAE;MACxB;MACA6C,OAAO,CAACU,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACvD,eAAe,CAAC;MAC7DuE,KAAK,CAAC,GAAG,IAAI,CAACvE,eAAe,CAACe,IAAI,qBAAqB,CAAC;MACxD,IAAI,CAACX,iBAAiB,EAAE;;EAE5B;EAEAlB,UAAUA,CAACoG,IAAU;IACnB,MAAMP,GAAG,GAAG,IAAItB,IAAI,EAAE;IACtB,MAAM8B,MAAM,GAAGR,GAAG,CAACS,OAAO,EAAE,GAAG,IAAI/B,IAAI,CAAC6B,IAAI,CAAC,CAACE,OAAO,EAAE;IACvD,MAAMC,WAAW,GAAGxC,IAAI,CAACC,KAAK,CAACqC,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACpD,MAAMG,SAAS,GAAGzC,IAAI,CAACC,KAAK,CAACqC,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMI,QAAQ,GAAG1C,IAAI,CAACC,KAAK,CAACqC,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE3D,IAAIE,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK;IACjC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,IAAIC,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,GAAG;IAC1C,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,GAAG;IACvC,OAAO,IAAIlC,IAAI,CAAC6B,IAAI,CAAC,CAACM,kBAAkB,EAAE;EAC5C;;;uBAvSWzE,mBAAmB,EAAA3I,EAAA,CAAAqN,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAnB5E,mBAAmB;MAAA6E,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1N,EAAA,CAAA2N,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAr1BtBjO,EAJN,CAAAC,cAAA,aAAyB,aAEE,aACQ,aAC6B;UAAxBD,EAAA,CAAAE,UAAA,mBAAAiO,kDAAA;YAAA,OAASD,GAAA,CAAAtD,WAAA,EAAa;UAAA,EAAC;UACvD5K,EAAA,CAAAC,cAAA,aAAqC;UACnCD,EAAA,CAAAc,SAAA,WAA2B;UAC7Bd,EAAA,CAAAe,YAAA,EAAM;UACNf,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAgB,MAAA,iBAAU;UACzChB,EADyC,CAAAe,YAAA,EAAO,EAC1C;UAENf,EAAA,CAAA6B,UAAA,IAAAuM,kCAAA,iBAEyC;UAQ7CpO,EADE,CAAAe,YAAA,EAAM,EACF;UAGNf,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAA6B,UAAA,KAAAwM,mCAAA,mBAAkD;UAgKpDrO,EAAA,CAAAe,YAAA,EAAM;UAGNf,EAAA,CAAA6B,UAAA,KAAAyM,mCAAA,kBAA4C;UAM9CtO,EAAA,CAAAe,YAAA,EAAM;UAGNf,EAAA,CAAA6B,UAAA,KAAA0M,mCAAA,mBAAiF;;;UAzLpDvO,EAAA,CAAAiB,SAAA,GAAU;UAAVjB,EAAA,CAAAoB,UAAA,YAAA8M,GAAA,CAAAnF,OAAA,CAAU;UAaK/I,EAAA,CAAAiB,SAAA,GAAQ;UAARjB,EAAA,CAAAoB,UAAA,YAAA8M,GAAA,CAAApF,KAAA,CAAQ;UAmK1B9I,EAAA,CAAAiB,SAAA,EAAkB;UAAlBjB,EAAA,CAAAoB,UAAA,SAAA8M,GAAA,CAAAlF,YAAA,CAAkB;UAShBhJ,EAAA,CAAAiB,SAAA,EAAqB;UAArBjB,EAAA,CAAAoB,UAAA,SAAA8M,GAAA,CAAA1G,eAAA,CAAqB;;;qBAvMzC1H,YAAY,EAAA0O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE5O,WAAW,EAAA6O,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}