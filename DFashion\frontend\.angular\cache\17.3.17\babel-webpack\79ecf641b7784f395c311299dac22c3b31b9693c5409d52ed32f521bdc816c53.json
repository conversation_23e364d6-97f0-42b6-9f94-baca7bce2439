{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, EventEmitter, booleanAttribute, Directive, Output, ContentChildren, Input, numberAttribute, ANIMATION_MODULE_TYPE, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Attribute, ViewChild, NgModule } from '@angular/core';\nimport { MatRipple, _MatInternalFormField, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport * as i2 from '@angular/cdk/collections';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n// Increasing integer for generating unique ids for radio components.\nconst _c0 = [\"input\"];\nconst _c1 = [\"formField\"];\nconst _c2 = [\"*\"];\nlet nextUniqueId = 0;\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n  constructor(/** The radio button that emits the change event. */\n  source, /** The value of the radio button. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatRadioGroup),\n  multi: true\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = /*#__PURE__*/new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-radio-default-options', {\n  providedIn: 'root',\n  factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY\n});\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent'\n  };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nlet MatRadioGroup = /*#__PURE__*/(() => {\n  class MatRadioGroup {\n    /** Name of the radio button group. All radio buttons inside this group will use this name. */\n    get name() {\n      return this._name;\n    }\n    set name(value) {\n      this._name = value;\n      this._updateRadioButtonNames();\n    }\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    get labelPosition() {\n      return this._labelPosition;\n    }\n    set labelPosition(v) {\n      this._labelPosition = v === 'before' ? 'before' : 'after';\n      this._markRadiosForCheck();\n    }\n    /**\n     * Value for the radio-group. Should equal the value of the selected radio button if there is\n     * a corresponding radio button with a matching value. If there is not such a corresponding\n     * radio button, this value persists to be applied in case a new radio button is added with a\n     * matching value.\n     */\n    get value() {\n      return this._value;\n    }\n    set value(newValue) {\n      if (this._value !== newValue) {\n        // Set this before proceeding to ensure no circular loop occurs with selection.\n        this._value = newValue;\n        this._updateSelectedRadioFromValue();\n        this._checkSelectedRadioButton();\n      }\n    }\n    _checkSelectedRadioButton() {\n      if (this._selected && !this._selected.checked) {\n        this._selected.checked = true;\n      }\n    }\n    /**\n     * The currently selected radio button. If set to a new radio button, the radio group value\n     * will be updated to match the new selected button.\n     */\n    get selected() {\n      return this._selected;\n    }\n    set selected(selected) {\n      this._selected = selected;\n      this.value = selected ? selected.value : null;\n      this._checkSelectedRadioButton();\n    }\n    /** Whether the radio group is disabled */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._markRadiosForCheck();\n    }\n    /** Whether the radio group is required */\n    get required() {\n      return this._required;\n    }\n    set required(value) {\n      this._required = value;\n      this._markRadiosForCheck();\n    }\n    constructor(_changeDetector) {\n      this._changeDetector = _changeDetector;\n      /** Selected value for the radio group. */\n      this._value = null;\n      /** The HTML name attribute applied to radio buttons in this group. */\n      this._name = `mat-radio-group-${nextUniqueId++}`;\n      /** The currently selected radio button. Should match value. */\n      this._selected = null;\n      /** Whether the `value` has been set to its initial value. */\n      this._isInitialized = false;\n      /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n      this._labelPosition = 'after';\n      /** Whether the radio group is disabled. */\n      this._disabled = false;\n      /** Whether the radio group is required. */\n      this._required = false;\n      /** The method to be called in order to update ngModel */\n      this._controlValueAccessorChangeFn = () => {};\n      /**\n       * onTouch function registered via registerOnTouch (ControlValueAccessor).\n       * @docs-private\n       */\n      this.onTouched = () => {};\n      /**\n       * Event emitted when the group value changes.\n       * Change events are only emitted when the value changes due to user interaction with\n       * a radio button (the same behavior as `<input type-\"radio\">`).\n       */\n      this.change = new EventEmitter();\n    }\n    /**\n     * Initialize properties once content children are available.\n     * This allows us to propagate relevant attributes to associated buttons.\n     */\n    ngAfterContentInit() {\n      // Mark this component as initialized in AfterContentInit because the initial value can\n      // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n      // NgModel occurs *after* the OnInit of the MatRadioGroup.\n      this._isInitialized = true;\n      // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n      // buttons depends on it. Note that we don't clear the `value`, because the radio button\n      // may be swapped out with a similar one and there are some internal apps that depend on\n      // that behavior.\n      this._buttonChanges = this._radios.changes.subscribe(() => {\n        if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n          this._selected = null;\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._buttonChanges?.unsubscribe();\n    }\n    /**\n     * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n     * radio buttons upon their blur.\n     */\n    _touch() {\n      if (this.onTouched) {\n        this.onTouched();\n      }\n    }\n    _updateRadioButtonNames() {\n      if (this._radios) {\n        this._radios.forEach(radio => {\n          radio.name = this.name;\n          radio._markForCheck();\n        });\n      }\n    }\n    /** Updates the `selected` radio button from the internal _value state. */\n    _updateSelectedRadioFromValue() {\n      // If the value already matches the selected radio, do nothing.\n      const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n      if (this._radios && !isAlreadySelected) {\n        this._selected = null;\n        this._radios.forEach(radio => {\n          radio.checked = this.value === radio.value;\n          if (radio.checked) {\n            this._selected = radio;\n          }\n        });\n      }\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent() {\n      if (this._isInitialized) {\n        this.change.emit(new MatRadioChange(this._selected, this._value));\n      }\n    }\n    _markRadiosForCheck() {\n      if (this._radios) {\n        this._radios.forEach(radio => radio._markForCheck());\n      }\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value\n     */\n    writeValue(value) {\n      this.value = value;\n      this._changeDetector.markForCheck();\n    }\n    /**\n     * Registers a callback to be triggered when the model value changes.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnChange(fn) {\n      this._controlValueAccessorChangeFn = fn;\n    }\n    /**\n     * Registers a callback to be triggered when the control is touched.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    /**\n     * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n     * @param isDisabled Whether the control should be disabled.\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._changeDetector.markForCheck();\n    }\n    static {\n      this.ɵfac = function MatRadioGroup_Factory(t) {\n        return new (t || MatRadioGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatRadioGroup,\n        selectors: [[\"mat-radio-group\"]],\n        contentQueries: function MatRadioGroup_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatRadioButton, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._radios = _t);\n          }\n        },\n        hostAttrs: [\"role\", \"radiogroup\", 1, \"mat-mdc-radio-group\"],\n        inputs: {\n          color: \"color\",\n          name: \"name\",\n          labelPosition: \"labelPosition\",\n          value: \"value\",\n          selected: \"selected\",\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n          required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute]\n        },\n        outputs: {\n          change: \"change\"\n        },\n        exportAs: [\"matRadioGroup\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n          provide: MAT_RADIO_GROUP,\n          useExisting: MatRadioGroup\n        }]), i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return MatRadioGroup;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatRadioButton = /*#__PURE__*/(() => {\n  class MatRadioButton {\n    /** Whether this radio button is checked. */\n    get checked() {\n      return this._checked;\n    }\n    set checked(value) {\n      if (this._checked !== value) {\n        this._checked = value;\n        if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n          this.radioGroup.selected = this;\n        } else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n          // When unchecking the selected radio button, update the selected radio\n          // property on the group.\n          this.radioGroup.selected = null;\n        }\n        if (value) {\n          // Notify all radio buttons with the same name to un-check.\n          this._radioDispatcher.notify(this.id, this.name);\n        }\n        this._changeDetector.markForCheck();\n      }\n    }\n    /** The value of this radio button. */\n    get value() {\n      return this._value;\n    }\n    set value(value) {\n      if (this._value !== value) {\n        this._value = value;\n        if (this.radioGroup !== null) {\n          if (!this.checked) {\n            // Update checked when the value changed to match the radio group's value\n            this.checked = this.radioGroup.value === value;\n          }\n          if (this.checked) {\n            this.radioGroup.selected = this;\n          }\n        }\n      }\n    }\n    /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n    get labelPosition() {\n      return this._labelPosition || this.radioGroup && this.radioGroup.labelPosition || 'after';\n    }\n    set labelPosition(value) {\n      this._labelPosition = value;\n    }\n    /** Whether the radio button is disabled. */\n    get disabled() {\n      return this._disabled || this.radioGroup !== null && this.radioGroup.disabled;\n    }\n    set disabled(value) {\n      this._setDisabled(value);\n    }\n    /** Whether the radio button is required. */\n    get required() {\n      return this._required || this.radioGroup && this.radioGroup.required;\n    }\n    set required(value) {\n      this._required = value;\n    }\n    /** Theme color of the radio button. */\n    get color() {\n      // As per Material design specifications the selection control radio should use the accent color\n      // palette by default. https://material.io/guidelines/components/selection-controls.html\n      return this._color || this.radioGroup && this.radioGroup.color || this._providerOverride && this._providerOverride.color || 'accent';\n    }\n    set color(newValue) {\n      this._color = newValue;\n    }\n    /** ID of the native input element inside `<mat-radio-button>` */\n    get inputId() {\n      return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(radioGroup, _elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n      this._elementRef = _elementRef;\n      this._changeDetector = _changeDetector;\n      this._focusMonitor = _focusMonitor;\n      this._radioDispatcher = _radioDispatcher;\n      this._providerOverride = _providerOverride;\n      this._uniqueId = `mat-radio-${++nextUniqueId}`;\n      /** The unique ID for the radio button. */\n      this.id = this._uniqueId;\n      /** Whether ripples are disabled inside the radio button */\n      this.disableRipple = false;\n      /** Tabindex of the radio button. */\n      this.tabIndex = 0;\n      /**\n       * Event emitted when the checked state of this radio button changes.\n       * Change events are only emitted when the value changes due to user interaction with\n       * the radio button (the same behavior as `<input type-\"radio\">`).\n       */\n      this.change = new EventEmitter();\n      /** Whether this radio is checked. */\n      this._checked = false;\n      /** Value assigned to this radio. */\n      this._value = null;\n      /** Unregister function for _radioDispatcher */\n      this._removeUniqueSelectionListener = () => {};\n      // Assertions. Ideally these should be stripped out by the compiler.\n      // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n      this.radioGroup = radioGroup;\n      this._noopAnimations = animationMode === 'NoopAnimations';\n      if (tabIndex) {\n        this.tabIndex = numberAttribute(tabIndex, 0);\n      }\n    }\n    /** Focuses the radio button. */\n    focus(options, origin) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._inputElement, origin, options);\n      } else {\n        this._inputElement.nativeElement.focus(options);\n      }\n    }\n    /**\n     * Marks the radio button as needing checking for change detection.\n     * This method is exposed because the parent radio group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n      // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n      // update radio button's status\n      this._changeDetector.markForCheck();\n    }\n    ngOnInit() {\n      if (this.radioGroup) {\n        // If the radio is inside a radio group, determine if it should be checked\n        this.checked = this.radioGroup.value === this._value;\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n        // Copy name from parent radio group\n        this.name = this.radioGroup.name;\n      }\n      this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n        if (id !== this.id && name === this.name) {\n          this.checked = false;\n        }\n      });\n    }\n    ngDoCheck() {\n      this._updateTabIndex();\n    }\n    ngAfterViewInit() {\n      this._updateTabIndex();\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n        if (!focusOrigin && this.radioGroup) {\n          this.radioGroup._touch();\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._removeUniqueSelectionListener();\n    }\n    /** Dispatch change event with current value. */\n    _emitChangeEvent() {\n      this.change.emit(new MatRadioChange(this, this._value));\n    }\n    _isRippleDisabled() {\n      return this.disableRipple || this.disabled;\n    }\n    _onInputClick(event) {\n      // We have to stop propagation for click events on the visual hidden input element.\n      // By default, when a user clicks on a label element, a generated click event will be\n      // dispatched on the associated input element. Since we are using a label element as our\n      // root container, the click event on the `radio-button` will be executed twice.\n      // The real click event will bubble up, and the generated click event also tries to bubble up.\n      // This will lead to multiple click events.\n      // Preventing bubbling for the second event will solve that issue.\n      event.stopPropagation();\n    }\n    /** Triggered when the radio button receives an interaction from the user. */\n    _onInputInteraction(event) {\n      // We always have to stop propagation on the change event.\n      // Otherwise the change event, from the input element, will bubble up and\n      // emit its event object to the `change` output.\n      event.stopPropagation();\n      if (!this.checked && !this.disabled) {\n        const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n        this.checked = true;\n        this._emitChangeEvent();\n        if (this.radioGroup) {\n          this.radioGroup._controlValueAccessorChangeFn(this.value);\n          if (groupValueChanged) {\n            this.radioGroup._emitChangeEvent();\n          }\n        }\n      }\n    }\n    /** Triggered when the user clicks on the touch target. */\n    _onTouchTargetClick(event) {\n      this._onInputInteraction(event);\n      if (!this.disabled) {\n        // Normally the input should be focused already, but if the click\n        // comes from the touch target, then we might have to focus it ourselves.\n        this._inputElement.nativeElement.focus();\n      }\n    }\n    /** Sets the disabled state and marks for check if a change occurred. */\n    _setDisabled(value) {\n      if (this._disabled !== value) {\n        this._disabled = value;\n        this._changeDetector.markForCheck();\n      }\n    }\n    /** Gets the tabindex for the underlying input element. */\n    _updateTabIndex() {\n      const group = this.radioGroup;\n      let value;\n      // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n      // necessary, because the browser handles the tab order for inputs inside a group automatically,\n      // but we need an explicitly higher tabindex for the selected button in order for things like\n      // the focus trap to pick it up correctly.\n      if (!group || !group.selected || this.disabled) {\n        value = this.tabIndex;\n      } else {\n        value = group.selected === this ? this.tabIndex : -1;\n      }\n      if (value !== this._previousTabIndex) {\n        // We have to set the tabindex directly on the DOM node, because it depends on\n        // the selected state which is prone to \"changed after checked errors\".\n        const input = this._inputElement?.nativeElement;\n        if (input) {\n          input.setAttribute('tabindex', value + '');\n          this._previousTabIndex = value;\n        }\n      }\n    }\n    static {\n      this.ɵfac = function MatRadioButton_Factory(t) {\n        return new (t || MatRadioButton)(i0.ɵɵdirectiveInject(MAT_RADIO_GROUP, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i2.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_RADIO_DEFAULT_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatRadioButton,\n        selectors: [[\"mat-radio-button\"]],\n        viewQuery: function MatRadioButton_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 7, ElementRef);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._rippleTrigger = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mat-mdc-radio-button\"],\n        hostVars: 15,\n        hostBindings: function MatRadioButton_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"focus\", function MatRadioButton_focus_HostBindingHandler() {\n              return ctx._inputElement.nativeElement.focus();\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n            i0.ɵɵclassProp(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-mdc-radio-checked\", ctx.checked)(\"_mat-animation-noopable\", ctx._noopAnimations);\n          }\n        },\n        inputs: {\n          id: \"id\",\n          name: \"name\",\n          ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n          ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n          ariaDescribedby: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"ariaDescribedby\"],\n          disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n          tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n          checked: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"checked\", \"checked\", booleanAttribute],\n          value: \"value\",\n          labelPosition: \"labelPosition\",\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n          required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n          color: \"color\"\n        },\n        outputs: {\n          change: \"change\"\n        },\n        exportAs: [\"matRadioButton\"],\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c2,\n        decls: 13,\n        vars: 16,\n        consts: [[\"formField\", \"\"], [\"input\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"labelPosition\"], [1, \"mdc-radio\"], [1, \"mat-mdc-radio-touch-target\", 3, \"click\"], [\"type\", \"radio\", 1, \"mdc-radio__native-control\", 3, \"change\", \"id\", \"checked\", \"disabled\", \"required\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"], [\"mat-ripple\", \"\", 1, \"mat-radio-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mat-ripple-element\", \"mat-radio-persistent-ripple\"], [1, \"mdc-label\", 3, \"for\"]],\n        template: function MatRadioButton_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"div\", 4);\n            i0.ɵɵlistener(\"click\", function MatRadioButton_Template_div_click_3_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onTouchTargetClick($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"input\", 5, 1);\n            i0.ɵɵlistener(\"change\", function MatRadioButton_Template_input_change_4_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onInputInteraction($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 6);\n            i0.ɵɵelement(7, \"div\", 7)(8, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 9);\n            i0.ɵɵelement(10, \"div\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"label\", 11);\n            i0.ɵɵprojection(12);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx.disabled);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"id\", ctx.inputId)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n            i0.ɵɵattribute(\"name\", ctx.name)(\"value\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"matRippleTrigger\", ctx._rippleTrigger.nativeElement)(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"for\", ctx.inputId);\n          }\n        },\n        dependencies: [MatRipple, _MatInternalFormField],\n        styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatRadioButton;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatRadioModule = /*#__PURE__*/(() => {\n  class MatRadioModule {\n    static {\n      this.ɵfac = function MatRadioModule_Factory(t) {\n        return new (t || MatRadioModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatRadioModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioButton, MatCommonModule]\n      });\n    }\n  }\n  return MatRadioModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };\n//# sourceMappingURL=radio.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}