{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/auth.service\";\nimport * as i2 from \"../../../core/services/cart.service\";\nimport * as i3 from \"../../../core/services/wishlist-new.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction HeaderComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_10_div_1_Template_div_click_0_listener() {\n      const suggestion_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectSuggestion(suggestion_r2));\n    });\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"span\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 34);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const suggestion_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getSuggestionIcon(suggestion_r2.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r2.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r2.type);\n  }\n}\nfunction HeaderComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, HeaderComponent_div_10_div_1_Template, 6, 3, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchSuggestions);\n  }\n}\nfunction HeaderComponent_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.wishlistItemCount);\n  }\n}\nfunction HeaderComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.cartItemCount);\n  }\n}\nfunction HeaderComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getFormattedCartTotal());\n  }\n}\nfunction HeaderComponent_div_37_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getTotalItemCount());\n  }\n}\nfunction HeaderComponent_div_37_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"i\", 16);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HeaderComponent_div_37_span_4_Template, 2, 1, \"span\", 42)(5, HeaderComponent_div_37_span_5_Template, 2, 0, \"span\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTotalItemCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTotalItemCount() === 0);\n  }\n}\nfunction HeaderComponent_div_38_a_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 61);\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵtext(2, \" Vendor Dashboard \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_38_a_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Admin Panel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_38_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 55);\n  }\n}\nfunction HeaderComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_38_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleUserMenu());\n    });\n    i0.ɵɵelement(1, \"img\", 47);\n    i0.ɵɵelementStart(2, \"span\", 48);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 49);\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"a\", 51);\n    i0.ɵɵelement(7, \"i\", 52);\n    i0.ɵɵtext(8, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 53);\n    i0.ɵɵelement(10, \"i\", 54);\n    i0.ɵɵtext(11, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"div\", 55);\n    i0.ɵɵtemplate(13, HeaderComponent_div_38_a_13_Template, 3, 0, \"a\", 56)(14, HeaderComponent_div_38_a_14_Template, 3, 0, \"a\", 57)(15, HeaderComponent_div_38_div_15_Template, 1, 0, \"div\", 58);\n    i0.ɵɵelementStart(16, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_38_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.logout());\n    });\n    i0.ɵɵelement(17, \"i\", 60);\n    i0.ɵɵtext(18, \" Logout \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.currentUser.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"show\", ctx_r2.showUserMenu);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser.role === \"vendor\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser.role === \"admin\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser.role !== \"customer\");\n  }\n}\nfunction HeaderComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"a\", 66);\n    i0.ɵɵtext(2, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 67);\n    i0.ɵɵtext(4, \"Sign Up\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let HeaderComponent = /*#__PURE__*/(() => {\n  class HeaderComponent {\n    constructor(authService, cartService, wishlistService, router) {\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.router = router;\n      this.currentUser = null;\n      this.searchQuery = '';\n      this.showUserMenu = false;\n      this.cartItemCount = 0;\n      this.wishlistItemCount = 0;\n      this.totalItemCount = 0;\n      this.cartTotalAmount = 0;\n      this.showCartTotalPrice = false;\n      // Search functionality\n      this.showSuggestions = false;\n      this.searchSuggestions = [];\n    }\n    ngOnInit() {\n      // Subscribe to user changes and refresh counts on login\n      this.authService.currentUser$.subscribe(user => {\n        const wasLoggedOut = !this.currentUser;\n        this.currentUser = user;\n        // If user just logged in, refresh total count\n        if (user && wasLoggedOut) {\n          console.log('🔄 User logged in, refreshing total count...');\n          setTimeout(() => {\n            this.cartService.refreshTotalCount();\n          }, 100);\n        } else if (!user && !wasLoggedOut) {\n          // User logged out, reset total count\n          console.log('🔄 User logged out, resetting total count...');\n          this.totalItemCount = 0;\n        }\n      });\n      // Subscribe to individual cart count\n      this.cartService.cartItemCount$.subscribe(count => {\n        this.cartItemCount = count;\n        console.log('🛒 Header cart count updated:', count);\n      });\n      // Subscribe to individual wishlist count\n      this.wishlistService.wishlistItemCount$.subscribe(count => {\n        this.wishlistItemCount = count;\n        console.log('💝 Header wishlist count updated:', count);\n      });\n      // Subscribe to total count (cart + wishlist)\n      this.cartService.totalItemCount$.subscribe(count => {\n        this.totalItemCount = count;\n        console.log('🔢 Header total count updated:', count);\n      });\n      // Subscribe to cart total amount\n      this.cartService.cartTotalAmount$.subscribe(amount => {\n        this.cartTotalAmount = amount;\n        console.log('💰 Header cart total amount updated:', amount);\n      });\n      // Subscribe to cart price display flag\n      this.cartService.showCartTotalPrice$.subscribe(showPrice => {\n        this.showCartTotalPrice = showPrice;\n        console.log('💲 Header show cart total price updated:', showPrice);\n      });\n      // Refresh counts when user logs in\n      if (this.currentUser) {\n        this.cartService.refreshTotalCount();\n        this.wishlistService.refreshWishlistOnLogin();\n      }\n      // Load cart and wishlist on init\n      this.cartService.loadCart();\n      this.wishlistService.loadWishlist();\n      // Close dropdown when clicking outside\n      document.addEventListener('click', event => {\n        const target = event.target;\n        if (!target.closest('.user-menu')) {\n          this.showUserMenu = false;\n        }\n      });\n    }\n    toggleUserMenu() {\n      this.showUserMenu = !this.showUserMenu;\n    }\n    openSearch() {\n      this.router.navigate(['/search']);\n    }\n    // Get total count for display (cart + wishlist items for logged-in user)\n    getTotalItemCount() {\n      if (!this.currentUser) {\n        return 0; // Return 0 if user is not logged in\n      }\n      return this.totalItemCount || 0;\n    }\n    // Get formatted cart total amount\n    getFormattedCartTotal() {\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n      }).format(this.cartTotalAmount || 0);\n    }\n    // Check if cart total price should be displayed\n    shouldShowCartTotalPrice() {\n      return this.currentUser !== null && this.showCartTotalPrice;\n    }\n    onSearch() {\n      if (this.searchQuery.trim()) {\n        this.router.navigate(['/search'], {\n          queryParams: {\n            q: this.searchQuery\n          }\n        });\n        this.hideSuggestions();\n      } else {\n        this.router.navigate(['/search']);\n      }\n    }\n    onSearchInput() {\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      this.searchTimeout = setTimeout(() => {\n        if (this.searchQuery.trim().length > 2) {\n          this.loadSearchSuggestions();\n        } else {\n          this.hideSuggestions();\n        }\n      }, 300);\n    }\n    onSearchFocus() {\n      if (this.searchQuery.trim().length > 2) {\n        this.showSuggestions = true;\n      }\n    }\n    onSearchBlur() {\n      // Delay hiding to allow clicking on suggestions\n      setTimeout(() => {\n        this.hideSuggestions();\n      }, 200);\n    }\n    loadSearchSuggestions() {\n      // Simulate API call for search suggestions\n      const query = this.searchQuery.toLowerCase();\n      // Mock suggestions based on query\n      this.searchSuggestions = [{\n        text: `${this.searchQuery} in Products`,\n        type: 'product',\n        icon: 'fa-shopping-bag'\n      }, {\n        text: `${this.searchQuery} in Brands`,\n        type: 'brand',\n        icon: 'fa-tags'\n      }, {\n        text: `${this.searchQuery} in Categories`,\n        type: 'category',\n        icon: 'fa-list'\n      }];\n      this.showSuggestions = true;\n    }\n    selectSuggestion(suggestion) {\n      this.searchQuery = suggestion.text;\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.searchQuery,\n          type: suggestion.type\n        }\n      });\n      this.hideSuggestions();\n    }\n    getSuggestionIcon(type) {\n      switch (type) {\n        case 'product':\n          return 'fa-shopping-bag';\n        case 'brand':\n          return 'fa-tags';\n        case 'category':\n          return 'fa-list';\n        default:\n          return 'fa-search';\n      }\n    }\n    hideSuggestions() {\n      this.showSuggestions = false;\n      this.searchSuggestions = [];\n    }\n    logout() {\n      this.authService.logout();\n      this.showUserMenu = false;\n    }\n    static {\n      this.ɵfac = function HeaderComponent_Factory(t) {\n        return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.WishlistNewService), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HeaderComponent,\n        selectors: [[\"app-header\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 40,\n        vars: 10,\n        consts: [[1, \"header\"], [1, \"container\"], [1, \"header-content\"], [1, \"logo\"], [\"routerLink\", \"/home\"], [1, \"gradient-text\"], [1, \"search-bar\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, categories...\", 3, \"ngModelChange\", \"keyup.enter\", \"input\", \"focus\", \"blur\", \"ngModel\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [1, \"nav-menu\"], [\"routerLink\", \"/home\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/explore\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-compass\"], [\"routerLink\", \"/shop\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-shopping-bag\"], [\"routerLink\", \"/wishlist\", \"routerLinkActive\", \"active\", 1, \"nav-item\", \"wishlist-item\"], [1, \"fas\", \"fa-heart\"], [\"class\", \"wishlist-badge\", 4, \"ngIf\"], [\"class\", \"wishlist-badge zero\", 4, \"ngIf\"], [\"routerLink\", \"/cart\", \"routerLinkActive\", \"active\", 1, \"nav-item\", \"cart-item\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"cart-badge\", 4, \"ngIf\"], [\"class\", \"cart-badge zero\", 4, \"ngIf\"], [\"class\", \"cart-total-display\", 4, \"ngIf\"], [\"class\", \"total-count-item\", 4, \"ngIf\"], [\"class\", \"user-menu\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"auth-buttons\", 4, \"ngIf\"], [1, \"search-suggestions\"], [\"class\", \"suggestion-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [1, \"suggestion-text\"], [1, \"suggestion-type\"], [1, \"wishlist-badge\"], [1, \"wishlist-badge\", \"zero\"], [1, \"cart-badge\"], [1, \"cart-badge\", \"zero\"], [1, \"cart-total-display\"], [1, \"cart-total-text\"], [1, \"total-count-item\"], [\"class\", \"total-count-badge\", 4, \"ngIf\"], [\"class\", \"total-count-badge zero\", 4, \"ngIf\"], [1, \"total-count-badge\"], [1, \"total-count-badge\", \"zero\"], [1, \"user-menu\", 3, \"click\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"username\"], [1, \"fas\", \"fa-chevron-down\"], [1, \"dropdown-menu\"], [\"routerLink\", \"/profile\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-user\"], [\"routerLink\", \"/settings\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-cog\"], [1, \"dropdown-divider\"], [\"routerLink\", \"/vendor/dashboard\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"routerLink\", \"/admin\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"class\", \"dropdown-divider\", 4, \"ngIf\"], [1, \"dropdown-item\", \"logout\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [\"routerLink\", \"/vendor/dashboard\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-store\"], [\"routerLink\", \"/admin\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"auth-buttons\"], [\"routerLink\", \"/auth/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/auth/register\", 1, \"btn\", \"btn-primary\"]],\n        template: function HeaderComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"h1\", 5);\n            i0.ɵɵtext(6, \"DFashion\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 6);\n            i0.ɵɵelement(8, \"i\", 7);\n            i0.ɵɵelementStart(9, \"input\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HeaderComponent_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function HeaderComponent_Template_input_keyup_enter_9_listener() {\n              return ctx.onSearch();\n            })(\"input\", function HeaderComponent_Template_input_input_9_listener() {\n              return ctx.onSearchInput();\n            })(\"focus\", function HeaderComponent_Template_input_focus_9_listener() {\n              return ctx.onSearchFocus();\n            })(\"blur\", function HeaderComponent_Template_input_blur_9_listener() {\n              return ctx.onSearchBlur();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, HeaderComponent_div_10_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nav\", 10)(12, \"a\", 11);\n            i0.ɵɵelement(13, \"i\", 12);\n            i0.ɵɵelementStart(14, \"span\");\n            i0.ɵɵtext(15, \"Home\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"a\", 13);\n            i0.ɵɵelement(17, \"i\", 14);\n            i0.ɵɵelementStart(18, \"span\");\n            i0.ɵɵtext(19, \"Explore\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"a\", 15);\n            i0.ɵɵelement(21, \"i\", 16);\n            i0.ɵɵelementStart(22, \"span\");\n            i0.ɵɵtext(23, \"Shop\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"a\", 17);\n            i0.ɵɵelement(25, \"i\", 18);\n            i0.ɵɵelementStart(26, \"span\");\n            i0.ɵɵtext(27, \"Wishlist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(28, HeaderComponent_span_28_Template, 2, 1, \"span\", 19)(29, HeaderComponent_span_29_Template, 2, 0, \"span\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"a\", 21);\n            i0.ɵɵelement(31, \"i\", 22);\n            i0.ɵɵelementStart(32, \"span\");\n            i0.ɵɵtext(33, \"Cart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(34, HeaderComponent_span_34_Template, 2, 1, \"span\", 23)(35, HeaderComponent_span_35_Template, 2, 0, \"span\", 24)(36, HeaderComponent_div_36_Template, 3, 1, \"div\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(37, HeaderComponent_div_37_Template, 6, 2, \"div\", 26)(38, HeaderComponent_div_38_Template, 19, 8, \"div\", 27)(39, HeaderComponent_div_39_Template, 5, 0, \"div\", 28);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSuggestions && ctx.searchSuggestions.length > 0);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistItemCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser && ctx.wishlistItemCount === 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartItemCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser && ctx.cartItemCount === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser && ctx.cartTotalAmount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.currentUser);\n          }\n        },\n        dependencies: [CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, RouterModule, i4.RouterLink, i4.RouterLinkActive, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n        styles: [\".header[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #dbdbdb;position:fixed;top:0;left:0;right:0;z-index:1000;height:60px}.header-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:60px}.logo[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:700;margin:0}.search-bar[_ngcontent-%COMP%]{position:relative;flex:1;max-width:400px;margin:0 40px}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:8px 16px 8px 40px;border:1px solid #dbdbdb;border-radius:8px;background:#fafafa;font-size:14px;outline:none;transition:all .2s}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{background:#fff;border-color:var(--primary-color)}.search-bar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:12px;top:50%;transform:translateY(-50%);color:#8e8e8e}.search-suggestions[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:#fff;border-radius:8px;box-shadow:0 4px 20px #0000001a;z-index:1000;margin-top:5px;max-height:300px;overflow-y:auto;border:1px solid #e0e0e0}.suggestion-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px;cursor:pointer;border-bottom:1px solid #f0f0f0;transition:background-color .2s ease}.suggestion-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.suggestion-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.suggestion-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d;margin-right:12px;width:16px;position:static;transform:none}.suggestion-text[_ngcontent-%COMP%]{flex:1;font-size:14px;color:#333}.suggestion-type[_ngcontent-%COMP%]{font-size:12px;color:#6c757d;text-transform:uppercase;font-weight:500}.nav-menu[_ngcontent-%COMP%]{display:flex;align-items:center;gap:24px}.nav-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-decoration:none;color:#262626;font-size:12px;transition:color .2s;padding:8px;border-radius:4px}.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;margin-bottom:4px}.nav-item.active[_ngcontent-%COMP%], .nav-item[_ngcontent-%COMP%]:hover{color:var(--primary-color)}.cart-item[_ngcontent-%COMP%], .wishlist-item[_ngcontent-%COMP%]{position:relative}.cart-badge[_ngcontent-%COMP%], .wishlist-badge[_ngcontent-%COMP%]{position:absolute;top:-2px;right:-2px;background:#ef4444;color:#fff;font-size:10px;font-weight:600;padding:2px 6px;border-radius:10px;min-width:16px;text-align:center;line-height:1.2}.cart-badge.zero[_ngcontent-%COMP%], .wishlist-badge.zero[_ngcontent-%COMP%]{background:#6c757d}.total-count-item[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;gap:8px;padding:8px 12px;background:linear-gradient(135deg,#4834d4,#686de0);color:#fff;border-radius:8px;font-size:14px;font-weight:600;margin-left:16px}.total-count-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.total-count-badge[_ngcontent-%COMP%]{background:#28a745;color:#fff;font-size:12px;font-weight:700;padding:2px 8px;border-radius:12px;min-width:20px;text-align:center;margin-left:auto}.total-count-badge.zero[_ngcontent-%COMP%]{background:#6c757d}.cart-total-display[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;background:#28a745;color:#fff;font-size:9px;font-weight:600;padding:2px 4px;border-radius:4px;white-space:nowrap;margin-top:2px;box-shadow:0 2px 4px #0000001a}.cart-total-text[_ngcontent-%COMP%]{font-size:9px}.total-count-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;padding:4px 8px;background:linear-gradient(135deg,#4834d4,#686de0);color:#fff;border-radius:12px;font-size:12px;font-weight:600;margin-left:8px}.total-count-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.total-count-text[_ngcontent-%COMP%]{font-size:12px;min-width:16px;text-align:center}.auth-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.btn[_ngcontent-%COMP%]{padding:8px 16px;border-radius:6px;text-decoration:none;font-size:14px;font-weight:500;transition:all .2s;border:1px solid transparent}.btn-outline[_ngcontent-%COMP%]{color:var(--primary-color);border-color:var(--primary-color);background:transparent}.btn-outline[_ngcontent-%COMP%]:hover, .btn-primary[_ngcontent-%COMP%]{background:var(--primary-color);color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{background:var(--primary-dark)}.user-menu[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;gap:8px;cursor:pointer;padding:8px 12px;border-radius:8px;transition:background .2s}.user-menu[_ngcontent-%COMP%]:hover{background:#f1f5f9}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;object-fit:cover}.username[_ngcontent-%COMP%]{font-weight:500;font-size:14px}.user-menu[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;color:#64748b;transition:transform .2s}.user-menu.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transform:rotate(180deg)}.dropdown-menu[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;background:#fff;border:1px solid #e2e8f0;border-radius:8px;box-shadow:0 4px 12px #00000026;min-width:200px;opacity:0;visibility:hidden;transform:translateY(-10px);transition:all .2s;z-index:1000}.dropdown-menu.show[_ngcontent-%COMP%]{opacity:1;visibility:visible;transform:translateY(0)}.dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 16px;text-decoration:none;color:#262626;font-size:14px;transition:background .2s;border:none;background:none;width:100%;text-align:left;cursor:pointer}.dropdown-item[_ngcontent-%COMP%]:hover{background:#f8fafc}.dropdown-item.logout[_ngcontent-%COMP%]{color:#ef4444}.dropdown-item.logout[_ngcontent-%COMP%]:hover{background:#fef2f2}.dropdown-divider[_ngcontent-%COMP%]{height:1px;background:#e2e8f0;margin:8px 0}@media (max-width: 768px){.search-bar[_ngcontent-%COMP%]{display:none}.nav-menu[_ngcontent-%COMP%]{gap:16px}.nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .username[_ngcontent-%COMP%]{display:none}.cart-item[_ngcontent-%COMP%], .wishlist-item[_ngcontent-%COMP%]{position:relative}.cart-badge[_ngcontent-%COMP%], .wishlist-badge[_ngcontent-%COMP%]{font-size:8px;padding:1px 4px;min-width:12px}.total-count-item[_ngcontent-%COMP%]{padding:6px 8px;font-size:12px;margin-left:8px}.total-count-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.total-count-badge){display:none}.total-count-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.total-count-badge[_ngcontent-%COMP%]{font-size:10px;padding:1px 6px;min-width:16px}.cart-total-display[_ngcontent-%COMP%]{font-size:8px;padding:1px 3px}.cart-total-text[_ngcontent-%COMP%]{font-size:8px}}\"]\n      });\n    }\n  }\n  return HeaderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}