{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/no-data-config.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => [1, 2, 3, 4, 5];\nfunction SearchComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function SearchComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchComponent_div_9_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_div_11_Template_div_click_0_listener() {\n      const category_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectCategoryForSearch(category_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(category_r6.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r6.count, \" products\");\n  }\n}\nfunction SearchComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.dismissCategorySelection());\n    });\n    i0.ɵɵelement(6, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 20)(8, \"p\");\n    i0.ɵɵtext(9, \"We found multiple categories that match your search. Please select one:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 21);\n    i0.ɵɵtemplate(11, SearchComponent_div_9_div_11_Template, 9, 4, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchInAllCategories());\n    });\n    i0.ɵɵtext(14, \" Search in All Categories \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Choose Category for \\\"\", ctx_r2.searchQuery, \"\\\"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.suggestedCategories);\n  }\n}\nfunction SearchComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"div\", 30);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Searching...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchComponent_div_13_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r8);\n  }\n}\nfunction SearchComponent_div_13_app_no_data_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 52);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"title\", ctx_r2.noDataConfig.title)(\"message\", ctx_r2.noDataConfig.message)(\"iconClass\", ctx_r2.noDataConfig.iconClass)(\"containerClass\", ctx_r2.noDataConfig.containerClass)(\"showActions\", ctx_r2.noDataConfig.showActions)(\"primaryAction\", ctx_r2.noDataConfig.primaryAction)(\"secondaryAction\", ctx_r2.noDataConfig.secondaryAction)(\"suggestions\", ctx_r2.noDataConfig.suggestions)(\"suggestionsTitle\", ctx_r2.noDataConfig.suggestionsTitle);\n  }\n}\nfunction SearchComponent_div_13_div_45_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getDiscountPercentage(product_r10), \"% OFF\");\n  }\n}\nfunction SearchComponent_div_13_div_45_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r10.price, \"1.0-0\"), \"\");\n  }\n}\nfunction SearchComponent_div_13_div_45_div_1_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 77);\n  }\n  if (rf & 2) {\n    const i_r11 = ctx.index;\n    const product_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", i_r11 < product_r10.rating.average);\n  }\n}\nfunction SearchComponent_div_13_div_45_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74);\n    i0.ɵɵtemplate(2, SearchComponent_div_13_div_45_div_1_div_17_i_2_Template, 1, 2, \"i\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 76);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r10.rating.count, \")\");\n  }\n}\nfunction SearchComponent_div_13_div_45_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_13_div_45_div_1_Template_div_click_0_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r10._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 56);\n    i0.ɵɵelement(2, \"img\", 57);\n    i0.ɵɵtemplate(3, SearchComponent_div_13_div_45_div_1_div_3_Template, 3, 1, \"div\", 58);\n    i0.ɵɵelementStart(4, \"div\", 59)(5, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_13_div_45_div_1_Template_button_click_5_listener($event) {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      ctx_r2.toggleWishlist(product_r10._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(6, \"i\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 61)(8, \"h3\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 62);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 63)(13, \"span\", 64);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SearchComponent_div_13_div_45_div_1_span_16_Template, 3, 4, \"span\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, SearchComponent_div_13_div_45_div_1_div_17_Template, 5, 3, \"div\", 66);\n    i0.ɵɵelementStart(18, \"div\", 67)(19, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_13_div_45_div_1_Template_button_click_19_listener($event) {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      ctx_r2.addToCart(product_r10._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 69);\n    i0.ɵɵtext(21, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_13_div_45_div_1_Template_button_click_22_listener($event) {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      ctx_r2.buyNow(product_r10._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(23, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r10.images == null ? null : product_r10.images[0]) || \"/assets/images/placeholder-product.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r10.discountPrice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r2.isInWishlist(product_r10._id) ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r10.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 10, product_r10.discountPrice || product_r10.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r10.discountPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r10.rating);\n  }\n}\nfunction SearchComponent_div_13_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, SearchComponent_div_13_div_45_div_1_Template, 24, 13, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchResults);\n  }\n}\nfunction SearchComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"h2\");\n    i0.ɵɵtext(3, \"Search Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"div\", 35)(8, \"label\");\n    i0.ɵɵtext(9, \"Category:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"select\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_13_Template_select_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedCategory, $event) || (ctx_r2.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_13_Template_select_change_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(11, \"option\", 37);\n    i0.ɵɵtext(12, \"All Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SearchComponent_div_13_option_13_Template, 2, 2, \"option\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 35)(15, \"label\");\n    i0.ɵɵtext(16, \"Price Range:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"select\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_13_Template_select_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedPriceRange, $event) || (ctx_r2.selectedPriceRange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_13_Template_select_change_17_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(18, \"option\", 37);\n    i0.ɵɵtext(19, \"All Prices\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 39);\n    i0.ɵɵtext(21, \"Under \\u20B91,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 40);\n    i0.ɵɵtext(23, \"\\u20B91,000 - \\u20B92,500\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 41);\n    i0.ɵɵtext(25, \"\\u20B92,500 - \\u20B95,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 42);\n    i0.ɵɵtext(27, \"\\u20B95,000 - \\u20B910,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"option\", 43);\n    i0.ɵɵtext(29, \"Above \\u20B910,000\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 35)(31, \"label\");\n    i0.ɵɵtext(32, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"select\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_13_Template_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.sortBy, $event) || (ctx_r2.sortBy = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_13_Template_select_change_33_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(34, \"option\", 44);\n    i0.ɵɵtext(35, \"Relevance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"option\", 45);\n    i0.ɵɵtext(37, \"Price: Low to High\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"option\", 46);\n    i0.ɵɵtext(39, \"Price: High to Low\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"option\", 47);\n    i0.ɵɵtext(41, \"Newest First\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"option\", 48);\n    i0.ɵɵtext(43, \"Highest Rated\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(44, SearchComponent_div_13_app_no_data_44_Template, 1, 9, \"app-no-data\", 49)(45, SearchComponent_div_13_div_45_Template, 2, 1, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.searchResults.length, \" products found\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categories);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedPriceRange);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.sortBy);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.length > 0);\n  }\n}\nfunction SearchComponent_div_14_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_14_div_1_span_4_Template_span_click_0_listener() {\n      const search_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.searchFor(search_r13));\n    });\n    i0.ɵɵelement(1, \"i\", 87);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"i\", 88);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_14_div_1_span_4_Template_i_click_3_listener($event) {\n      const search_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      ctx_r2.removeRecentSearch(search_r13);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const search_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r13, \" \");\n  }\n}\nfunction SearchComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"h3\");\n    i0.ɵɵtext(2, \"Recent Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtemplate(4, SearchComponent_div_14_div_1_span_4_Template, 4, 1, \"span\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentSearches);\n  }\n}\nfunction SearchComponent_div_14_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 89);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_14_span_6_Template_span_click_0_listener() {\n      const search_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.searchFor(search_r15));\n    });\n    i0.ɵɵelement(1, \"i\", 90);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const search_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r15, \" \");\n  }\n}\nfunction SearchComponent_div_14_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_14_div_11_Template_div_click_0_listener() {\n      const category_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.browseCategory(category_r17.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(category_r17.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r17.count, \" items\");\n  }\n}\nfunction SearchComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtemplate(1, SearchComponent_div_14_div_1_Template, 5, 1, \"div\", 79);\n    i0.ɵɵelementStart(2, \"div\", 80)(3, \"h3\");\n    i0.ɵɵtext(4, \"Popular Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 81);\n    i0.ɵɵtemplate(6, SearchComponent_div_14_span_6_Template, 3, 1, \"span\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 80)(8, \"h3\");\n    i0.ɵɵtext(9, \"Browse Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 83);\n    i0.ɵɵtemplate(11, SearchComponent_div_14_div_11_Template, 7, 4, \"div\", 84);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentSearches.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.popularSearches);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categoryList);\n  }\n}\nexport class SearchComponent {\n  constructor(route, router, productService, noDataConfigService) {\n    this.route = route;\n    this.router = router;\n    this.productService = productService;\n    this.noDataConfigService = noDataConfigService;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.recentSearches = [];\n    this.isLoading = false;\n    this.hasSearched = false;\n    // Intelligent search features\n    this.showCategorySelection = false;\n    this.suggestedCategories = [];\n    this.searchIntent = 'product';\n    // Filters\n    this.selectedCategory = '';\n    this.selectedPriceRange = '';\n    this.sortBy = 'relevance';\n    this.categories = ['Men', 'Women', 'Kids', 'Accessories', 'Footwear', 'Electronics'];\n    // Data\n    this.popularSearches = ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Jackets', 'Accessories', 'Formal Wear', 'Casual Wear'];\n    this.categoryList = [{\n      name: 'Men',\n      icon: 'fas fa-male',\n      count: 1250\n    }, {\n      name: 'Women',\n      icon: 'fas fa-female',\n      count: 1890\n    }, {\n      name: 'Kids',\n      icon: 'fas fa-child',\n      count: 650\n    }, {\n      name: 'Accessories',\n      icon: 'fas fa-gem',\n      count: 890\n    }, {\n      name: 'Footwear',\n      icon: 'fas fa-shoe-prints',\n      count: 750\n    }, {\n      name: 'Electronics',\n      icon: 'fas fa-mobile-alt',\n      count: 450\n    }];\n    // Category mapping for intelligent search\n    this.categoryKeywords = {\n      'Men': ['men', 'mens', 'man', 'male', 'boy', 'boys', 'gentleman', 'kurta', 'kurtas'],\n      'Women': ['women', 'womens', 'woman', 'female', 'girl', 'girls', 'lady', 'ladies', 'saree', 'sarees', 'kurti', 'kurtis'],\n      'Kids': ['kids', 'children', 'child', 'baby', 'toddler', 'infant'],\n      'Accessories': ['accessories', 'jewelry', 'jewellery', 'watch', 'watches', 'belt', 'belts'],\n      'Footwear': ['shoes', 'footwear', 'sandals', 'boots', 'sneakers', 'heels'],\n      'Electronics': ['mobile', 'phone', 'laptop', 'tablet', 'headphones', 'earphones']\n    };\n    this.wishlistItems = [];\n    this.noDataConfig = {};\n    this.searchSubject = new Subject();\n    this.noDataConfig = this.noDataConfigService.getSearchConfig();\n  }\n  ngOnInit() {\n    this.loadRecentSearches();\n    this.loadWishlistItems();\n    // Check for query parameter\n    this.route.queryParams.subscribe(params => {\n      if (params['q']) {\n        this.searchQuery = params['q'];\n        this.performSearch();\n      }\n    });\n    // Setup search with debounce\n    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), switchMap(query => {\n      if (query.trim().length > 0) {\n        this.isLoading = true;\n        const searchParams = {\n          search: query.trim(),\n          category: this.selectedCategory,\n          minPrice: this.getPriceRange().min,\n          maxPrice: this.getPriceRange().max,\n          sortBy: this.getSortField(),\n          sortOrder: this.getSortOrder(),\n          page: 1,\n          limit: 20\n        };\n        return this.productService.getProducts(searchParams);\n      } else {\n        this.searchResults = [];\n        this.hasSearched = false;\n        this.isLoading = false;\n        return [];\n      }\n    })).subscribe({\n      next: response => {\n        console.log('Search response:', response);\n        this.searchResults = response.products || response.data?.products || [];\n        this.hasSearched = true;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Search error:', error);\n        this.isLoading = false;\n        this.hasSearched = true;\n        this.searchResults = [];\n        // Show user-friendly error message\n        this.showErrorMessage('Search failed. Please try again.');\n      }\n    });\n  }\n  onSearchInput(event) {\n    this.searchQuery = event.target.value;\n    if (this.searchQuery.trim().length > 0) {\n      // Detect search intent and suggest categories\n      this.detectSearchIntent(this.searchQuery);\n      this.searchSubject.next(this.searchQuery);\n    } else {\n      this.searchResults = [];\n      this.hasSearched = false;\n      this.showCategorySelection = false;\n      this.suggestedCategories = [];\n    }\n  }\n  detectSearchIntent(query) {\n    const lowerQuery = query.toLowerCase().trim();\n    this.suggestedCategories = [];\n    // Check if query matches category keywords\n    for (const [category, keywords] of Object.entries(this.categoryKeywords)) {\n      if (keywords.some(keyword => lowerQuery.includes(keyword))) {\n        const categoryData = this.categoryList.find(cat => cat.name === category);\n        if (categoryData && !this.suggestedCategories.find(cat => cat.name === category)) {\n          this.suggestedCategories.push(categoryData);\n        }\n      }\n    }\n    // Show category selection if we found relevant categories\n    this.showCategorySelection = this.suggestedCategories.length > 0;\n    // Determine search intent\n    if (this.suggestedCategories.length > 0) {\n      this.searchIntent = 'category';\n    } else {\n      this.searchIntent = 'product';\n    }\n  }\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      this.saveRecentSearch(this.searchQuery.trim());\n      this.performSearch();\n    }\n  }\n  performSearch() {\n    if (this.searchQuery.trim()) {\n      this.searchSubject.next(this.searchQuery);\n    }\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.hasSearched = false;\n  }\n  searchFor(term) {\n    this.searchQuery = term;\n    this.saveRecentSearch(term);\n    this.performSearch();\n  }\n  applyFilters() {\n    if (this.searchQuery) {\n      this.performSearch();\n    }\n  }\n  browseCategory(category) {\n    this.selectedCategory = category;\n    this.searchQuery = category;\n    this.performSearch();\n  }\n  selectCategoryForSearch(category) {\n    // Intelligent redirection based on category selection\n    this.selectedCategory = category.name;\n    this.showCategorySelection = false;\n    // Update search query to include category filter\n    const originalQuery = this.searchQuery;\n    this.searchQuery = `${originalQuery} in ${category.name}`;\n    // Navigate to filtered category page\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        q: originalQuery,\n        category: category.name.toLowerCase(),\n        intent: 'category'\n      }\n    });\n  }\n  dismissCategorySelection() {\n    this.showCategorySelection = false;\n    this.suggestedCategories = [];\n  }\n  searchInAllCategories() {\n    this.showCategorySelection = false;\n    this.selectedCategory = '';\n    this.performSearch();\n  }\n  viewProduct(productId) {\n    this.router.navigate(['/shop/product', productId]);\n  }\n  toggleWishlist(productId) {\n    if (this.isInWishlist(productId)) {\n      this.wishlistItems = this.wishlistItems.filter(id => id !== productId);\n    } else {\n      this.wishlistItems.push(productId);\n    }\n    localStorage.setItem('wishlist', JSON.stringify(this.wishlistItems));\n  }\n  isInWishlist(productId) {\n    return this.wishlistItems.includes(productId);\n  }\n  addToCart(productId) {\n    // Add to cart logic\n    console.log('Added to cart:', productId);\n    this.showNotification('Added to cart!', 'success');\n  }\n  buyNow(productId) {\n    // Buy now logic\n    console.log('Buy now:', productId);\n    this.showNotification('Redirecting to checkout...', 'info');\n  }\n  getDiscountPercentage(product) {\n    if (product.discountPrice) {\n      return Math.round((product.price - product.discountPrice) / product.price * 100);\n    }\n    return 0;\n  }\n  removeRecentSearch(term) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n  loadRecentSearches() {\n    const saved = localStorage.getItem('recentSearches');\n    this.recentSearches = saved ? JSON.parse(saved) : [];\n  }\n  loadWishlistItems() {\n    const saved = localStorage.getItem('wishlist');\n    this.wishlistItems = saved ? JSON.parse(saved) : [];\n  }\n  saveRecentSearch(term) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    this.recentSearches.unshift(term);\n    this.recentSearches = this.recentSearches.slice(0, 10);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n  showErrorMessage(message) {\n    // Create error notification\n    const notification = document.createElement('div');\n    notification.className = 'search-error-notification';\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #f44336;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      animation: slideIn 0.3s ease;\n    `;\n    document.body.appendChild(notification);\n    setTimeout(() => {\n      notification.remove();\n    }, 3000);\n  }\n  getPriceRange() {\n    if (!this.selectedPriceRange) return {\n      min: undefined,\n      max: undefined\n    };\n    const ranges = {\n      '0-1000': {\n        min: 0,\n        max: 1000\n      },\n      '1000-2500': {\n        min: 1000,\n        max: 2500\n      },\n      '2500-5000': {\n        min: 2500,\n        max: 5000\n      },\n      '5000-10000': {\n        min: 5000,\n        max: 10000\n      },\n      '10000+': {\n        min: 10000\n      }\n    };\n    return ranges[this.selectedPriceRange] || {\n      min: undefined,\n      max: undefined\n    };\n  }\n  getSortField() {\n    const sortMap = {\n      'relevance': 'createdAt',\n      'price-low': 'price',\n      'price-high': 'price',\n      'newest': 'createdAt',\n      'rating': 'rating'\n    };\n    return sortMap[this.sortBy] || 'createdAt';\n  }\n  getSortOrder() {\n    return this.sortBy === 'price-high' ? 'desc' : 'asc';\n  }\n  showNotification(message, type) {\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      right: 20px;\n      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};\n      color: white;\n      padding: 1rem 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 0.9rem;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n    document.body.appendChild(notification);\n    setTimeout(() => notification.remove(), 3000);\n  }\n  static {\n    this.ɵfac = function SearchComponent_Factory(t) {\n      return new (t || SearchComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.NoDataConfigService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchComponent,\n      selectors: [[\"app-search\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 6,\n      consts: [[\"searchInput\", \"\"], [1, \"search-page\"], [1, \"search-header\"], [1, \"container\"], [1, \"search-bar-container\"], [1, \"search-input-wrapper\"], [1, \"fas\", \"fa-search\", \"search-icon\"], [\"type\", \"text\", \"placeholder\", \"Search for fashion, brands, and more...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"clear-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"category-selection-overlay\", 4, \"ngIf\"], [1, \"search-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"default-content\", 4, \"ngIf\"], [1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"category-selection-overlay\"], [1, \"category-selection-modal\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"category-options\"], [\"class\", \"category-option\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-actions\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"category-option\", 3, \"click\"], [1, \"category-icon\"], [1, \"category-details\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"search-results\"], [1, \"results-header\"], [1, \"results-count\"], [1, \"filters-section\"], [1, \"filter-group\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"0-1000\"], [\"value\", \"1000-2500\"], [\"value\", \"2500-5000\"], [\"value\", \"5000-10000\"], [\"value\", \"10000+\"], [\"value\", \"relevance\"], [\"value\", \"price-low\"], [\"value\", \"price-high\"], [\"value\", \"newest\"], [\"value\", \"rating\"], [3, \"title\", \"message\", \"iconClass\", \"containerClass\", \"showActions\", \"primaryAction\", \"secondaryAction\", \"suggestions\", \"suggestionsTitle\", 4, \"ngIf\"], [\"class\", \"results-grid\", 4, \"ngIf\"], [3, \"value\"], [3, \"title\", \"message\", \"iconClass\", \"containerClass\", \"showActions\", \"primaryAction\", \"secondaryAction\", \"suggestions\", \"suggestionsTitle\"], [1, \"results-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [\"class\", \"product-badge\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price-container\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"rating\", 4, \"ngIf\"], [1, \"product-buttons\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy\", 3, \"click\"], [1, \"product-badge\"], [1, \"original-price\"], [1, \"rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"fas\", \"fa-star\"], [1, \"default-content\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"section\"], [1, \"search-chips\"], [\"class\", \"search-chip popular\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"search-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-chip\", 3, \"click\"], [1, \"fas\", \"fa-clock\"], [1, \"fas\", \"fa-times\", 3, \"click\"], [1, \"search-chip\", \"popular\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [1, \"category-card\", 3, \"click\"]],\n      template: function SearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n          i0.ɵɵelement(5, \"i\", 6);\n          i0.ɵɵelementStart(6, \"input\", 7, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_Template_input_ngModelChange_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function SearchComponent_Template_input_input_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchInput($event));\n          })(\"keyup.enter\", function SearchComponent_Template_input_keyup_enter_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchSubmit());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, SearchComponent_button_8_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(9, SearchComponent_div_9_Template, 15, 2, \"div\", 9);\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 3);\n          i0.ɵɵtemplate(12, SearchComponent_div_12_Template, 4, 0, \"div\", 11)(13, SearchComponent_div_13_Template, 46, 7, \"div\", 12)(14, SearchComponent_div_14_Template, 12, 3, \"div\", 13);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCategorySelection);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasSearched && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasSearched && !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, FormsModule, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, NoDataComponent],\n      styles: [\".search-page[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 60px);\\n  background: #f8f9fa;\\n}\\n\\n.search-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 2rem 0;\\n  position: sticky;\\n  top: 60px;\\n  z-index: 100;\\n}\\n\\n.search-bar-container[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.search-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  color: #6c757d;\\n  z-index: 1;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem 1rem 1rem 3rem;\\n  border: 2px solid #e9ecef;\\n  border-radius: 50px;\\n  font-size: 1rem;\\n  outline: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.search-input[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  color: #6c757d;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n}\\n\\n.category-selection-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.5);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.category-selection-modal[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  max-width: 500px;\\n  width: 90%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.25rem;\\n  color: #6c757d;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  color: #495057;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n  font-size: 0.95rem;\\n}\\n\\n.category-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.category-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  border: 2px solid #e9ecef;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.category-option[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  background: #f8f9ff;\\n}\\n\\n.category-option[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  background: linear-gradient(135deg, #007bff, #0056b3);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.25rem;\\n}\\n\\n.category-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.category-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.category-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.85rem;\\n  color: #6c757d;\\n}\\n\\n.category-option[_ngcontent-%COMP%]   .fa-arrow-right[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e9ecef;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.search-content[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 1rem;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem 0;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #007bff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 1rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.results-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.results-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.results-count[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 0.9rem;\\n  color: #495057;\\n}\\n\\n.filter-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  font-size: 0.9rem;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem 2rem;\\n  background: white;\\n  border-radius: 8px;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #dee2e6;\\n  margin-bottom: 1rem;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.5rem;\\n  color: #495057;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 2rem;\\n}\\n\\n.suggested-searches[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  font-size: 1rem;\\n  color: #495057;\\n}\\n\\n.search-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n  justify-content: center;\\n}\\n\\n.search-tag[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #495057;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  transition: all 0.2s ease;\\n}\\n\\n.search-tag[_ngcontent-%COMP%]:hover {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.results-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n\\n.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.product-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.5rem;\\n  left: 0.5rem;\\n  background: #dc3545;\\n  color: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.5rem;\\n  right: 0.5rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  color: #6c757d;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  color: #dc3545;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #212529;\\n  line-height: 1.3;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.85rem;\\n  margin: 0 0 0.5rem 0;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.price-container[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 700;\\n  color: #28a745;\\n  margin-right: 0.5rem;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n  text-decoration: line-through;\\n}\\n\\n.rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.125rem;\\n}\\n\\n.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #dee2e6;\\n}\\n\\n.stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n.rating-count[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n}\\n\\n.product-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%], .btn-buy[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.5rem;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #495057;\\n  border: 1px solid #dee2e6;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n}\\n\\n.btn-buy[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.btn-buy[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.default-content[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n\\n.section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.search-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n\\n.search-chip[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dee2e6;\\n  color: #495057;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.search-chip[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  color: #007bff;\\n}\\n\\n.search-chip.popular[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #feca57);\\n  color: white;\\n  border: none;\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 2rem 1rem;\\n  border-radius: 12px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.category-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  background: linear-gradient(45deg, #007bff, #0056b3);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 1rem;\\n}\\n\\n.category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: white;\\n}\\n\\n.category-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.category-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .filters-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .results-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 1rem;\\n  }\\n  .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  }\\n  .search-header[_ngcontent-%COMP%] {\\n    padding: 1rem 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "debounceTime", "distinctUntilChanged", "switchMap", "Subject", "NoDataComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "SearchComponent_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "SearchComponent_div_9_div_11_Template_div_click_0_listener", "category_r6", "_r5", "$implicit", "selectCategoryForSearch", "ɵɵtext", "ɵɵadvance", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "count", "SearchComponent_div_9_Template_button_click_5_listener", "_r4", "dismissCategorySelection", "ɵɵtemplate", "SearchComponent_div_9_div_11_Template", "SearchComponent_div_9_Template_button_click_13_listener", "searchInAllCategories", "searchQuery", "ɵɵproperty", "suggestedCategories", "category_r8", "noDataConfig", "title", "message", "iconClass", "containerClass", "showActions", "primaryAction", "secondaryAction", "suggestions", "<PERSON><PERSON><PERSON>le", "getDiscountPercentage", "product_r10", "ɵɵpipeBind2", "price", "ɵɵclassProp", "i_r11", "rating", "average", "SearchComponent_div_13_div_45_div_1_div_17_i_2_Template", "ɵɵpureFunction0", "_c0", "SearchComponent_div_13_div_45_div_1_Template_div_click_0_listener", "_r9", "viewProduct", "_id", "SearchComponent_div_13_div_45_div_1_div_3_Template", "SearchComponent_div_13_div_45_div_1_Template_button_click_5_listener", "$event", "toggleWishlist", "stopPropagation", "SearchComponent_div_13_div_45_div_1_span_16_Template", "SearchComponent_div_13_div_45_div_1_div_17_Template", "SearchComponent_div_13_div_45_div_1_Template_button_click_19_listener", "addToCart", "SearchComponent_div_13_div_45_div_1_Template_button_click_22_listener", "buyNow", "images", "ɵɵsanitizeUrl", "discountPrice", "isInWishlist", "brand", "SearchComponent_div_13_div_45_div_1_Template", "searchResults", "ɵɵtwoWayListener", "SearchComponent_div_13_Template_select_ngModelChange_10_listener", "_r7", "ɵɵtwoWayBindingSet", "selectedCate<PERSON><PERSON>", "SearchComponent_div_13_Template_select_change_10_listener", "applyFilters", "SearchComponent_div_13_option_13_Template", "SearchComponent_div_13_Template_select_ngModelChange_17_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SearchComponent_div_13_Template_select_change_17_listener", "SearchComponent_div_13_Template_select_ngModelChange_33_listener", "sortBy", "SearchComponent_div_13_Template_select_change_33_listener", "SearchComponent_div_13_app_no_data_44_Template", "SearchComponent_div_13_div_45_Template", "length", "ɵɵtwoWayProperty", "categories", "SearchComponent_div_14_div_1_span_4_Template_span_click_0_listener", "search_r13", "_r12", "searchFor", "SearchComponent_div_14_div_1_span_4_Template_i_click_3_listener", "removeRecentSearch", "SearchComponent_div_14_div_1_span_4_Template", "recentSearches", "SearchComponent_div_14_span_6_Template_span_click_0_listener", "search_r15", "_r14", "SearchComponent_div_14_div_11_Template_div_click_0_listener", "category_r17", "_r16", "browseCategory", "SearchComponent_div_14_div_1_Template", "SearchComponent_div_14_span_6_Template", "SearchComponent_div_14_div_11_Template", "popularSearches", "categoryList", "SearchComponent", "constructor", "route", "router", "productService", "noDataConfigService", "isLoading", "hasSearched", "showCategorySelection", "searchIntent", "categoryKeywords", "wishlistItems", "searchSubject", "getSearchConfig", "ngOnInit", "loadRecentSearches", "loadWishlistItems", "queryParams", "subscribe", "params", "performSearch", "pipe", "query", "trim", "searchParams", "search", "category", "minPrice", "getPriceRange", "min", "maxPrice", "max", "getSortField", "sortOrder", "getSortOrder", "page", "limit", "getProducts", "next", "response", "console", "log", "products", "data", "error", "showErrorMessage", "onSearchInput", "event", "target", "value", "detectSearchIntent", "lowerQuery", "toLowerCase", "keywords", "Object", "entries", "some", "keyword", "includes", "categoryData", "find", "cat", "push", "onSearchSubmit", "saveRecentSearch", "term", "originalQuery", "navigate", "q", "intent", "productId", "filter", "id", "localStorage", "setItem", "JSON", "stringify", "showNotification", "product", "Math", "round", "saved", "getItem", "parse", "unshift", "slice", "notification", "document", "createElement", "className", "textContent", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "remove", "undefined", "ranges", "sortMap", "type", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProductService", "i3", "NoDataConfigService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SearchComponent_Template", "rf", "ctx", "SearchComponent_Template_input_ngModelChange_6_listener", "_r1", "SearchComponent_Template_input_input_6_listener", "SearchComponent_Template_input_keyup_enter_6_listener", "SearchComponent_button_8_Template", "SearchComponent_div_9_Template", "SearchComponent_div_12_Template", "SearchComponent_div_13_Template", "SearchComponent_div_14_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i5", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fahion\\DFashion\\frontend\\src\\app\\features\\search\\pages\\search\\search.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { NoDataConfigService } from '../../../../core/services/no-data-config.service';\n\n@Component({\n  selector: 'app-search',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NoDataComponent],\n  template: `\n    <div class=\"search-page\">\n      <!-- Search Header -->\n      <div class=\"search-header\">\n        <div class=\"container\">\n          <div class=\"search-bar-container\">\n            <div class=\"search-input-wrapper\">\n              <i class=\"fas fa-search search-icon\"></i>\n              <input \n                type=\"text\" \n                class=\"search-input\"\n                placeholder=\"Search for fashion, brands, and more...\"\n                [(ngModel)]=\"searchQuery\"\n                (input)=\"onSearchInput($event)\"\n                (keyup.enter)=\"onSearchSubmit()\"\n                #searchInput\n              >\n              <button \n                *ngIf=\"searchQuery\" \n                class=\"clear-btn\" \n                (click)=\"clearSearch()\"\n              >\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Intelligent Category Selection -->\n      <div *ngIf=\"showCategorySelection\" class=\"category-selection-overlay\">\n        <div class=\"category-selection-modal\">\n          <div class=\"modal-header\">\n            <h3>Choose Category for \"{{ searchQuery }}\"</h3>\n            <button class=\"close-btn\" (click)=\"dismissCategorySelection()\">\n              <i class=\"fas fa-times\"></i>\n            </button>\n          </div>\n\n          <div class=\"modal-content\">\n            <p>We found multiple categories that match your search. Please select one:</p>\n\n            <div class=\"category-options\">\n              <div\n                *ngFor=\"let category of suggestedCategories\"\n                class=\"category-option\"\n                (click)=\"selectCategoryForSearch(category)\"\n              >\n                <div class=\"category-icon\">\n                  <i [class]=\"category.icon\"></i>\n                </div>\n                <div class=\"category-details\">\n                  <h4>{{ category.name }}</h4>\n                  <p>{{ category.count }} products</p>\n                </div>\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n\n            <div class=\"modal-actions\">\n              <button class=\"btn-secondary\" (click)=\"searchInAllCategories()\">\n                Search in All Categories\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Search Content -->\n      <div class=\"search-content\">\n        <div class=\"container\">\n          <!-- Loading -->\n          <div *ngIf=\"isLoading\" class=\"loading-container\">\n            <div class=\"loading-spinner\"></div>\n            <p>Searching...</p>\n          </div>\n\n          <!-- Search Results -->\n          <div *ngIf=\"hasSearched && !isLoading\" class=\"search-results\">\n            <div class=\"results-header\">\n              <h2>Search Results</h2>\n              <span class=\"results-count\">{{ searchResults.length }} products found</span>\n            </div>\n\n            <!-- Filters -->\n            <div class=\"filters-section\">\n              <div class=\"filter-group\">\n                <label>Category:</label>\n                <select [(ngModel)]=\"selectedCategory\" (change)=\"applyFilters()\">\n                  <option value=\"\">All Categories</option>\n                  <option *ngFor=\"let category of categories\" [value]=\"category\">{{ category }}</option>\n                </select>\n              </div>\n              <div class=\"filter-group\">\n                <label>Price Range:</label>\n                <select [(ngModel)]=\"selectedPriceRange\" (change)=\"applyFilters()\">\n                  <option value=\"\">All Prices</option>\n                  <option value=\"0-1000\">Under ₹1,000</option>\n                  <option value=\"1000-2500\">₹1,000 - ₹2,500</option>\n                  <option value=\"2500-5000\">₹2,500 - ₹5,000</option>\n                  <option value=\"5000-10000\">₹5,000 - ₹10,000</option>\n                  <option value=\"10000+\">Above ₹10,000</option>\n                </select>\n              </div>\n              <div class=\"filter-group\">\n                <label>Sort by:</label>\n                <select [(ngModel)]=\"sortBy\" (change)=\"applyFilters()\">\n                  <option value=\"relevance\">Relevance</option>\n                  <option value=\"price-low\">Price: Low to High</option>\n                  <option value=\"price-high\">Price: High to Low</option>\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"rating\">Highest Rated</option>\n                </select>\n              </div>\n            </div>\n\n            <!-- No Results -->\n            <app-no-data\n              *ngIf=\"searchResults.length === 0\"\n              [title]=\"noDataConfig.title\"\n              [message]=\"noDataConfig.message\"\n              [iconClass]=\"noDataConfig.iconClass\"\n              [containerClass]=\"noDataConfig.containerClass\"\n              [showActions]=\"noDataConfig.showActions\"\n              [primaryAction]=\"noDataConfig.primaryAction\"\n              [secondaryAction]=\"noDataConfig.secondaryAction\"\n              [suggestions]=\"noDataConfig.suggestions\"\n              [suggestionsTitle]=\"noDataConfig.suggestionsTitle\">\n            </app-no-data>\n\n            <!-- Results Grid -->\n            <div *ngIf=\"searchResults.length > 0\" class=\"results-grid\">\n              <div *ngFor=\"let product of searchResults\" class=\"product-card\" (click)=\"viewProduct(product._id)\">\n                <div class=\"product-image\">\n                  <img [src]=\"product.images?.[0] || '/assets/images/placeholder-product.jpg'\" [alt]=\"product.name\">\n                  <div class=\"product-badge\" *ngIf=\"product.discountPrice\">\n                    <span>{{ getDiscountPercentage(product) }}% OFF</span>\n                  </div>\n                  <div class=\"product-actions\">\n                    <button class=\"action-btn wishlist-btn\" (click)=\"toggleWishlist(product._id); $event.stopPropagation()\">\n                      <i [class]=\"isInWishlist(product._id) ? 'fas fa-heart' : 'far fa-heart'\"></i>\n                    </button>\n                  </div>\n                </div>\n                <div class=\"product-info\">\n                  <h3>{{ product.name }}</h3>\n                  <p class=\"brand\">{{ product.brand }}</p>\n                  <div class=\"price-container\">\n                    <span class=\"current-price\">₹{{ product.discountPrice || product.price | number:'1.0-0' }}</span>\n                    <span *ngIf=\"product.discountPrice\" class=\"original-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                  </div>\n                  <div class=\"rating\" *ngIf=\"product.rating\">\n                    <div class=\"stars\">\n                      <i *ngFor=\"let star of [1,2,3,4,5]; let i = index\" \n                         class=\"fas fa-star\" \n                         [class.filled]=\"i < product.rating.average\"></i>\n                    </div>\n                    <span class=\"rating-count\">({{ product.rating.count }})</span>\n                  </div>\n                  <div class=\"product-buttons\">\n                    <button class=\"btn-cart\" (click)=\"addToCart(product._id); $event.stopPropagation()\">\n                      <i class=\"fas fa-shopping-cart\"></i>\n                      Add to Cart\n                    </button>\n                    <button class=\"btn-buy\" (click)=\"buyNow(product._id); $event.stopPropagation()\">\n                      Buy Now\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Default Content (when not searching) -->\n          <div *ngIf=\"!hasSearched && !isLoading\" class=\"default-content\">\n            <!-- Recent Searches -->\n            <div *ngIf=\"recentSearches.length > 0\" class=\"section\">\n              <h3>Recent Searches</h3>\n              <div class=\"search-chips\">\n                <span *ngFor=\"let search of recentSearches\" class=\"search-chip\" (click)=\"searchFor(search)\">\n                  <i class=\"fas fa-clock\"></i>\n                  {{ search }}\n                  <i class=\"fas fa-times\" (click)=\"removeRecentSearch(search); $event.stopPropagation()\"></i>\n                </span>\n              </div>\n            </div>\n\n            <!-- Popular Searches -->\n            <div class=\"section\">\n              <h3>Popular Searches</h3>\n              <div class=\"search-chips\">\n                <span *ngFor=\"let search of popularSearches\" class=\"search-chip popular\" (click)=\"searchFor(search)\">\n                  <i class=\"fas fa-fire\"></i>\n                  {{ search }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Categories -->\n            <div class=\"section\">\n              <h3>Browse Categories</h3>\n              <div class=\"categories-grid\">\n                <div *ngFor=\"let category of categoryList\" class=\"category-card\" (click)=\"browseCategory(category.name)\">\n                  <div class=\"category-icon\">\n                    <i [class]=\"category.icon\"></i>\n                  </div>\n                  <h4>{{ category.name }}</h4>\n                  <p>{{ category.count }} items</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .search-page {\n      min-height: calc(100vh - 60px);\n      background: #f8f9fa;\n    }\n\n    .search-header {\n      background: white;\n      border-bottom: 1px solid #e9ecef;\n      padding: 2rem 0;\n      position: sticky;\n      top: 60px;\n      z-index: 100;\n    }\n\n    .search-bar-container {\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .search-input-wrapper {\n      position: relative;\n      display: flex;\n      align-items: center;\n    }\n\n    .search-icon {\n      position: absolute;\n      left: 1rem;\n      color: #6c757d;\n      z-index: 1;\n    }\n\n    .search-input {\n      width: 100%;\n      padding: 1rem 1rem 1rem 3rem;\n      border: 2px solid #e9ecef;\n      border-radius: 50px;\n      font-size: 1rem;\n      outline: none;\n      transition: all 0.3s ease;\n    }\n\n    .search-input:focus {\n      border-color: #007bff;\n      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n    }\n\n    .clear-btn {\n      position: absolute;\n      right: 1rem;\n      background: none;\n      border: none;\n      color: #6c757d;\n      cursor: pointer;\n      padding: 0.5rem;\n    }\n\n    .category-selection-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0, 0, 0, 0.5);\n      z-index: 1000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      animation: fadeIn 0.3s ease;\n    }\n\n    .category-selection-modal {\n      background: white;\n      border-radius: 12px;\n      max-width: 500px;\n      width: 90%;\n      max-height: 80vh;\n      overflow-y: auto;\n      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n      animation: slideUp 0.3s ease;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 1.5rem;\n      border-bottom: 1px solid #e9ecef;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: 1.25rem;\n      color: #6c757d;\n      cursor: pointer;\n      padding: 0.5rem;\n      border-radius: 50%;\n      transition: all 0.2s ease;\n    }\n\n    .close-btn:hover {\n      background: #f8f9fa;\n      color: #495057;\n    }\n\n    .modal-content {\n      padding: 1.5rem;\n    }\n\n    .modal-content p {\n      color: #6c757d;\n      margin-bottom: 1.5rem;\n      font-size: 0.95rem;\n    }\n\n    .category-options {\n      display: flex;\n      flex-direction: column;\n      gap: 0.75rem;\n      margin-bottom: 1.5rem;\n    }\n\n    .category-option {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n      padding: 1rem;\n      border: 2px solid #e9ecef;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .category-option:hover {\n      border-color: #007bff;\n      background: #f8f9ff;\n    }\n\n    .category-option .category-icon {\n      width: 48px;\n      height: 48px;\n      background: linear-gradient(135deg, #007bff, #0056b3);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: 1.25rem;\n    }\n\n    .category-details {\n      flex: 1;\n    }\n\n    .category-details h4 {\n      margin: 0 0 0.25rem 0;\n      font-size: 1rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .category-details p {\n      margin: 0;\n      font-size: 0.85rem;\n      color: #6c757d;\n    }\n\n    .category-option .fa-arrow-right {\n      color: #6c757d;\n      font-size: 0.9rem;\n    }\n\n    .modal-actions {\n      text-align: center;\n      padding-top: 1rem;\n      border-top: 1px solid #e9ecef;\n    }\n\n    .btn-secondary {\n      background: #f8f9fa;\n      color: #6c757d;\n      border: 1px solid #dee2e6;\n      padding: 0.75rem 1.5rem;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 0.9rem;\n      font-weight: 500;\n      transition: all 0.2s ease;\n    }\n\n    .btn-secondary:hover {\n      background: #e9ecef;\n      color: #495057;\n    }\n\n    @keyframes fadeIn {\n      from { opacity: 0; }\n      to { opacity: 1; }\n    }\n\n    @keyframes slideUp {\n      from {\n        opacity: 0;\n        transform: translateY(30px);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0);\n      }\n    }\n\n    .search-content {\n      padding: 2rem 0;\n    }\n\n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 0 1rem;\n    }\n\n    .loading-container {\n      text-align: center;\n      padding: 4rem 0;\n    }\n\n    .loading-spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid #f3f3f3;\n      border-top: 4px solid #007bff;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto 1rem;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .results-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 2rem;\n    }\n\n    .results-header h2 {\n      margin: 0;\n      font-size: 1.5rem;\n      font-weight: 600;\n    }\n\n    .results-count {\n      color: #6c757d;\n      font-size: 0.9rem;\n    }\n\n    .filters-section {\n      display: flex;\n      gap: 2rem;\n      margin-bottom: 2rem;\n      padding: 1rem;\n      background: white;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .filter-group {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n    }\n\n    .filter-group label {\n      font-weight: 500;\n      font-size: 0.9rem;\n      color: #495057;\n    }\n\n    .filter-group select {\n      padding: 0.5rem;\n      border: 1px solid #ced4da;\n      border-radius: 4px;\n      font-size: 0.9rem;\n    }\n\n    .no-results {\n      text-align: center;\n      padding: 4rem 2rem;\n      background: white;\n      border-radius: 8px;\n    }\n\n    .no-results i {\n      font-size: 4rem;\n      color: #dee2e6;\n      margin-bottom: 1rem;\n    }\n\n    .no-results h3 {\n      margin: 0 0 0.5rem 0;\n      font-size: 1.5rem;\n      color: #495057;\n    }\n\n    .no-results p {\n      color: #6c757d;\n      margin-bottom: 2rem;\n    }\n\n    .suggested-searches h4 {\n      margin: 0 0 1rem 0;\n      font-size: 1rem;\n      color: #495057;\n    }\n\n    .search-tags {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n      justify-content: center;\n    }\n\n    .search-tag {\n      background: #e9ecef;\n      color: #495057;\n      padding: 0.5rem 1rem;\n      border-radius: 20px;\n      cursor: pointer;\n      font-size: 0.9rem;\n      transition: all 0.2s ease;\n    }\n\n    .search-tag:hover {\n      background: #007bff;\n      color: white;\n    }\n\n    .results-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n      gap: 1.5rem;\n    }\n\n    .product-card {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .product-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n\n    .product-image {\n      position: relative;\n      width: 100%;\n      height: 200px;\n      overflow: hidden;\n    }\n\n    .product-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .product-card:hover .product-image img {\n      transform: scale(1.05);\n    }\n\n    .product-badge {\n      position: absolute;\n      top: 0.5rem;\n      left: 0.5rem;\n      background: #dc3545;\n      color: white;\n      padding: 0.25rem 0.5rem;\n      border-radius: 4px;\n      font-size: 0.75rem;\n      font-weight: 600;\n    }\n\n    .product-actions {\n      position: absolute;\n      top: 0.5rem;\n      right: 0.5rem;\n    }\n\n    .action-btn {\n      background: rgba(255, 255, 255, 0.9);\n      border: none;\n      width: 36px;\n      height: 36px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      color: #6c757d;\n    }\n\n    .action-btn:hover {\n      background: white;\n      color: #dc3545;\n    }\n\n    .product-info {\n      padding: 1rem;\n    }\n\n    .product-info h3 {\n      margin: 0 0 0.5rem 0;\n      font-size: 1rem;\n      font-weight: 600;\n      color: #212529;\n      line-height: 1.3;\n    }\n\n    .brand {\n      color: #6c757d;\n      font-size: 0.85rem;\n      margin: 0 0 0.5rem 0;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    .price-container {\n      margin-bottom: 0.5rem;\n    }\n\n    .current-price {\n      font-size: 1.125rem;\n      font-weight: 700;\n      color: #28a745;\n      margin-right: 0.5rem;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #6c757d;\n      text-decoration: line-through;\n    }\n\n    .rating {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      margin-bottom: 1rem;\n    }\n\n    .stars {\n      display: flex;\n      gap: 0.125rem;\n    }\n\n    .stars i {\n      font-size: 0.875rem;\n      color: #dee2e6;\n    }\n\n    .stars i.filled {\n      color: #ffc107;\n    }\n\n    .rating-count {\n      font-size: 0.8rem;\n      color: #6c757d;\n    }\n\n    .product-buttons {\n      display: flex;\n      gap: 0.5rem;\n    }\n\n    .btn-cart,\n    .btn-buy {\n      flex: 1;\n      padding: 0.5rem;\n      border: none;\n      border-radius: 6px;\n      font-size: 0.85rem;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-cart {\n      background: #f8f9fa;\n      color: #495057;\n      border: 1px solid #dee2e6;\n    }\n\n    .btn-cart:hover {\n      background: #e9ecef;\n    }\n\n    .btn-buy {\n      background: #007bff;\n      color: white;\n    }\n\n    .btn-buy:hover {\n      background: #0056b3;\n    }\n\n    .default-content {\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .section {\n      margin-bottom: 3rem;\n    }\n\n    .section h3 {\n      margin: 0 0 1rem 0;\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .search-chips {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n    }\n\n    .search-chip {\n      background: white;\n      border: 1px solid #dee2e6;\n      color: #495057;\n      padding: 0.5rem 1rem;\n      border-radius: 20px;\n      cursor: pointer;\n      font-size: 0.9rem;\n      transition: all 0.2s ease;\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .search-chip:hover {\n      border-color: #007bff;\n      color: #007bff;\n    }\n\n    .search-chip.popular {\n      background: linear-gradient(45deg, #ff6b6b, #feca57);\n      color: white;\n      border: none;\n    }\n\n    .categories-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 1rem;\n    }\n\n    .category-card {\n      background: white;\n      padding: 2rem 1rem;\n      border-radius: 12px;\n      text-align: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    }\n\n    .category-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n    }\n\n    .category-icon {\n      width: 60px;\n      height: 60px;\n      background: linear-gradient(45deg, #007bff, #0056b3);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 1rem;\n    }\n\n    .category-icon i {\n      font-size: 1.5rem;\n      color: white;\n    }\n\n    .category-card h4 {\n      margin: 0 0 0.5rem 0;\n      font-size: 1.125rem;\n      font-weight: 600;\n      color: #212529;\n    }\n\n    .category-card p {\n      margin: 0;\n      color: #6c757d;\n      font-size: 0.9rem;\n    }\n\n    @media (max-width: 768px) {\n      .filters-section {\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .results-grid {\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n        gap: 1rem;\n      }\n\n      .categories-grid {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n\n      .search-header {\n        padding: 1rem 0;\n      }\n    }\n  `]\n})\nexport class SearchComponent implements OnInit {\n  searchQuery = '';\n  searchResults: any[] = [];\n  recentSearches: string[] = [];\n  isLoading = false;\n  hasSearched = false;\n\n  // Intelligent search features\n  showCategorySelection = false;\n  suggestedCategories: any[] = [];\n  searchIntent: 'product' | 'category' | 'brand' = 'product';\n\n  // Filters\n  selectedCategory = '';\n  selectedPriceRange = '';\n  sortBy = 'relevance';\n  categories: string[] = ['Men', 'Women', 'Kids', 'Accessories', 'Footwear', 'Electronics'];\n\n  // Data\n  popularSearches = ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Jackets', 'Accessories', 'Formal Wear', 'Casual Wear'];\n  categoryList = [\n    { name: 'Men', icon: 'fas fa-male', count: 1250 },\n    { name: 'Women', icon: 'fas fa-female', count: 1890 },\n    { name: 'Kids', icon: 'fas fa-child', count: 650 },\n    { name: 'Accessories', icon: 'fas fa-gem', count: 890 },\n    { name: 'Footwear', icon: 'fas fa-shoe-prints', count: 750 },\n    { name: 'Electronics', icon: 'fas fa-mobile-alt', count: 450 }\n  ];\n\n  // Category mapping for intelligent search\n  categoryKeywords: { [key: string]: string[] } = {\n    'Men': ['men', 'mens', 'man', 'male', 'boy', 'boys', 'gentleman', 'kurta', 'kurtas'],\n    'Women': ['women', 'womens', 'woman', 'female', 'girl', 'girls', 'lady', 'ladies', 'saree', 'sarees', 'kurti', 'kurtis'],\n    'Kids': ['kids', 'children', 'child', 'baby', 'toddler', 'infant'],\n    'Accessories': ['accessories', 'jewelry', 'jewellery', 'watch', 'watches', 'belt', 'belts'],\n    'Footwear': ['shoes', 'footwear', 'sandals', 'boots', 'sneakers', 'heels'],\n    'Electronics': ['mobile', 'phone', 'laptop', 'tablet', 'headphones', 'earphones']\n  };\n\n  wishlistItems: string[] = [];\n  noDataConfig: any = {};\n  private searchSubject = new Subject<string>();\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private productService: ProductService,\n    private noDataConfigService: NoDataConfigService\n  ) {\n    this.noDataConfig = this.noDataConfigService.getSearchConfig();\n  }\n\n  ngOnInit() {\n    this.loadRecentSearches();\n    this.loadWishlistItems();\n    \n    // Check for query parameter\n    this.route.queryParams.subscribe(params => {\n      if (params['q']) {\n        this.searchQuery = params['q'];\n        this.performSearch();\n      }\n    });\n\n    // Setup search with debounce\n    this.searchSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      switchMap(query => {\n        if (query.trim().length > 0) {\n          this.isLoading = true;\n          const searchParams = {\n            search: query.trim(),\n            category: this.selectedCategory,\n            minPrice: this.getPriceRange().min,\n            maxPrice: this.getPriceRange().max,\n            sortBy: this.getSortField(),\n            sortOrder: this.getSortOrder(),\n            page: 1,\n            limit: 20\n          };\n          return this.productService.getProducts(searchParams);\n        } else {\n          this.searchResults = [];\n          this.hasSearched = false;\n          this.isLoading = false;\n          return [];\n        }\n      })\n    ).subscribe({\n      next: (response: any) => {\n        console.log('Search response:', response);\n        this.searchResults = response.products || response.data?.products || [];\n        this.hasSearched = true;\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Search error:', error);\n        this.isLoading = false;\n        this.hasSearched = true;\n        this.searchResults = [];\n        // Show user-friendly error message\n        this.showErrorMessage('Search failed. Please try again.');\n      }\n    });\n  }\n\n  onSearchInput(event: any) {\n    this.searchQuery = event.target.value;\n    if (this.searchQuery.trim().length > 0) {\n      // Detect search intent and suggest categories\n      this.detectSearchIntent(this.searchQuery);\n      this.searchSubject.next(this.searchQuery);\n    } else {\n      this.searchResults = [];\n      this.hasSearched = false;\n      this.showCategorySelection = false;\n      this.suggestedCategories = [];\n    }\n  }\n\n  detectSearchIntent(query: string) {\n    const lowerQuery = query.toLowerCase().trim();\n    this.suggestedCategories = [];\n\n    // Check if query matches category keywords\n    for (const [category, keywords] of Object.entries(this.categoryKeywords)) {\n      if (keywords.some(keyword => lowerQuery.includes(keyword))) {\n        const categoryData = this.categoryList.find(cat => cat.name === category);\n        if (categoryData && !this.suggestedCategories.find(cat => cat.name === category)) {\n          this.suggestedCategories.push(categoryData);\n        }\n      }\n    }\n\n    // Show category selection if we found relevant categories\n    this.showCategorySelection = this.suggestedCategories.length > 0;\n\n    // Determine search intent\n    if (this.suggestedCategories.length > 0) {\n      this.searchIntent = 'category';\n    } else {\n      this.searchIntent = 'product';\n    }\n  }\n\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      this.saveRecentSearch(this.searchQuery.trim());\n      this.performSearch();\n    }\n  }\n\n  performSearch() {\n    if (this.searchQuery.trim()) {\n      this.searchSubject.next(this.searchQuery);\n    }\n  }\n\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.hasSearched = false;\n  }\n\n  searchFor(term: string) {\n    this.searchQuery = term;\n    this.saveRecentSearch(term);\n    this.performSearch();\n  }\n\n  applyFilters() {\n    if (this.searchQuery) {\n      this.performSearch();\n    }\n  }\n\n  browseCategory(category: string) {\n    this.selectedCategory = category;\n    this.searchQuery = category;\n    this.performSearch();\n  }\n\n  selectCategoryForSearch(category: any) {\n    // Intelligent redirection based on category selection\n    this.selectedCategory = category.name;\n    this.showCategorySelection = false;\n\n    // Update search query to include category filter\n    const originalQuery = this.searchQuery;\n    this.searchQuery = `${originalQuery} in ${category.name}`;\n\n    // Navigate to filtered category page\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        q: originalQuery,\n        category: category.name.toLowerCase(),\n        intent: 'category'\n      }\n    });\n  }\n\n  dismissCategorySelection() {\n    this.showCategorySelection = false;\n    this.suggestedCategories = [];\n  }\n\n  searchInAllCategories() {\n    this.showCategorySelection = false;\n    this.selectedCategory = '';\n    this.performSearch();\n  }\n\n  viewProduct(productId: string) {\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  toggleWishlist(productId: string) {\n    if (this.isInWishlist(productId)) {\n      this.wishlistItems = this.wishlistItems.filter(id => id !== productId);\n    } else {\n      this.wishlistItems.push(productId);\n    }\n    localStorage.setItem('wishlist', JSON.stringify(this.wishlistItems));\n  }\n\n  isInWishlist(productId: string): boolean {\n    return this.wishlistItems.includes(productId);\n  }\n\n  addToCart(productId: string) {\n    // Add to cart logic\n    console.log('Added to cart:', productId);\n    this.showNotification('Added to cart!', 'success');\n  }\n\n  buyNow(productId: string) {\n    // Buy now logic\n    console.log('Buy now:', productId);\n    this.showNotification('Redirecting to checkout...', 'info');\n  }\n\n  getDiscountPercentage(product: any): number {\n    if (product.discountPrice) {\n      return Math.round(((product.price - product.discountPrice) / product.price) * 100);\n    }\n    return 0;\n  }\n\n  removeRecentSearch(term: string) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n\n  private loadRecentSearches() {\n    const saved = localStorage.getItem('recentSearches');\n    this.recentSearches = saved ? JSON.parse(saved) : [];\n  }\n\n  private loadWishlistItems() {\n    const saved = localStorage.getItem('wishlist');\n    this.wishlistItems = saved ? JSON.parse(saved) : [];\n  }\n\n  private saveRecentSearch(term: string) {\n    this.recentSearches = this.recentSearches.filter(search => search !== term);\n    this.recentSearches.unshift(term);\n    this.recentSearches = this.recentSearches.slice(0, 10);\n    localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n  }\n\n  private showErrorMessage(message: string) {\n    // Create error notification\n    const notification = document.createElement('div');\n    notification.className = 'search-error-notification';\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #f44336;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      animation: slideIn 0.3s ease;\n    `;\n\n    document.body.appendChild(notification);\n\n    setTimeout(() => {\n      notification.remove();\n    }, 3000);\n  }\n\n  private getPriceRange() {\n    if (!this.selectedPriceRange) return { min: undefined, max: undefined };\n    \n    const ranges: { [key: string]: { min?: number; max?: number } } = {\n      '0-1000': { min: 0, max: 1000 },\n      '1000-2500': { min: 1000, max: 2500 },\n      '2500-5000': { min: 2500, max: 5000 },\n      '5000-10000': { min: 5000, max: 10000 },\n      '10000+': { min: 10000 }\n    };\n    \n    return ranges[this.selectedPriceRange] || { min: undefined, max: undefined };\n  }\n\n  private getSortField(): string {\n    const sortMap: { [key: string]: string } = {\n      'relevance': 'createdAt',\n      'price-low': 'price',\n      'price-high': 'price',\n      'newest': 'createdAt',\n      'rating': 'rating'\n    };\n    return sortMap[this.sortBy] || 'createdAt';\n  }\n\n  private getSortOrder(): 'asc' | 'desc' {\n    return this.sortBy === 'price-high' ? 'desc' : 'asc';\n  }\n\n  private showNotification(message: string, type: 'success' | 'info' | 'error') {\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n    \n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      right: 20px;\n      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};\n      color: white;\n      padding: 1rem 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 0.9rem;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n\n    document.body.appendChild(notification);\n    setTimeout(() => notification.remove(), 3000);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,QAAQ,MAAM;AAG9B,SAASC,eAAe,QAAQ,yDAAyD;;;;;;;;;;;IAwB3EC,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAEvBT,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAoBTX,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAU,2DAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,uBAAA,CAAAH,WAAA,CAAiC;IAAA,EAAC;IAE3Cb,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAU,SAAA,QAA+B;IACjCV,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,cAA8B,SACxB;IAAAD,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC5BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAiB,MAAA,GAA6B;IAClCjB,EADkC,CAAAW,YAAA,EAAI,EAChC;IACNX,EAAA,CAAAU,SAAA,YAAkC;IACpCV,EAAA,CAAAW,YAAA,EAAM;;;;IAPCX,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAAmB,UAAA,CAAAN,WAAA,CAAAO,IAAA,CAAuB;IAGtBpB,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAqB,iBAAA,CAAAR,WAAA,CAAAS,IAAA,CAAmB;IACpBtB,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAuB,kBAAA,KAAAV,WAAA,CAAAW,KAAA,cAA6B;;;;;;IApBtCxB,EAHN,CAAAC,cAAA,cAAsE,cAC9B,cACV,SACpB;IAAAD,EAAA,CAAAiB,MAAA,GAAuC;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,iBAA+D;IAArCD,EAAA,CAAAE,UAAA,mBAAAuB,uDAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqB,wBAAA,EAA0B;IAAA,EAAC;IAC5D3B,EAAA,CAAAU,SAAA,YAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;IAGJX,EADF,CAAAC,cAAA,cAA2B,QACtB;IAAAD,EAAA,CAAAiB,MAAA,8EAAuE;IAAAjB,EAAA,CAAAW,YAAA,EAAI;IAE9EX,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAA4B,UAAA,KAAAC,qCAAA,kBAIC;IAUH7B,EAAA,CAAAW,YAAA,EAAM;IAGJX,EADF,CAAAC,cAAA,eAA2B,kBACuC;IAAlCD,EAAA,CAAAE,UAAA,mBAAA4B,wDAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyB,qBAAA,EAAuB;IAAA,EAAC;IAC7D/B,EAAA,CAAAiB,MAAA,kCACF;IAIRjB,EAJQ,CAAAW,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAjCIX,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAAuB,kBAAA,2BAAAjB,MAAA,CAAA0B,WAAA,OAAuC;IAWlBhC,EAAA,CAAAkB,SAAA,GAAsB;IAAtBlB,EAAA,CAAAiC,UAAA,YAAA3B,MAAA,CAAA4B,mBAAA,CAAsB;;;;;IA4BjDlC,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAiB,MAAA,mBAAY;IACjBjB,EADiB,CAAAW,YAAA,EAAI,EACf;;;;;IAeEX,EAAA,CAAAC,cAAA,iBAA+D;IAAAD,EAAA,CAAAiB,MAAA,GAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAS;;;;IAA1CX,EAAA,CAAAiC,UAAA,UAAAE,WAAA,CAAkB;IAACnC,EAAA,CAAAkB,SAAA,EAAc;IAAdlB,EAAA,CAAAqB,iBAAA,CAAAc,WAAA,CAAc;;;;;IA2BnFnC,EAAA,CAAAU,SAAA,sBAWc;;;;IADZV,EARA,CAAAiC,UAAA,UAAA3B,MAAA,CAAA8B,YAAA,CAAAC,KAAA,CAA4B,YAAA/B,MAAA,CAAA8B,YAAA,CAAAE,OAAA,CACI,cAAAhC,MAAA,CAAA8B,YAAA,CAAAG,SAAA,CACI,mBAAAjC,MAAA,CAAA8B,YAAA,CAAAI,cAAA,CACU,gBAAAlC,MAAA,CAAA8B,YAAA,CAAAK,WAAA,CACN,kBAAAnC,MAAA,CAAA8B,YAAA,CAAAM,aAAA,CACI,oBAAApC,MAAA,CAAA8B,YAAA,CAAAO,eAAA,CACI,gBAAArC,MAAA,CAAA8B,YAAA,CAAAQ,WAAA,CACR,qBAAAtC,MAAA,CAAA8B,YAAA,CAAAS,gBAAA,CACU;;;;;IAS5C7C,EADF,CAAAC,cAAA,cAAyD,WACjD;IAAAD,EAAA,CAAAiB,MAAA,GAAyC;IACjDjB,EADiD,CAAAW,YAAA,EAAO,EAClD;;;;;IADEX,EAAA,CAAAkB,SAAA,GAAyC;IAAzClB,EAAA,CAAAuB,kBAAA,KAAAjB,MAAA,CAAAwC,qBAAA,CAAAC,WAAA,WAAyC;;;;;IAa/C/C,EAAA,CAAAC,cAAA,eAA2D;IAAAD,EAAA,CAAAiB,MAAA,GAAqC;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA5CX,EAAA,CAAAkB,SAAA,EAAqC;IAArClB,EAAA,CAAAuB,kBAAA,WAAAvB,EAAA,CAAAgD,WAAA,OAAAD,WAAA,CAAAE,KAAA,eAAqC;;;;;IAI9FjD,EAAA,CAAAU,SAAA,YAEmD;;;;;IAAhDV,EAAA,CAAAkD,WAAA,WAAAC,KAAA,GAAAJ,WAAA,CAAAK,MAAA,CAAAC,OAAA,CAA2C;;;;;IAHhDrD,EADF,CAAAC,cAAA,cAA2C,cACtB;IACjBD,EAAA,CAAA4B,UAAA,IAAA0B,uDAAA,gBAE+C;IACjDtD,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAiB,MAAA,GAA4B;IACzDjB,EADyD,CAAAW,YAAA,EAAO,EAC1D;;;;IALkBX,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAAiC,UAAA,YAAAjC,EAAA,CAAAuD,eAAA,IAAAC,GAAA,EAAgB;IAIXxD,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAuB,kBAAA,MAAAwB,WAAA,CAAAK,MAAA,CAAA5B,KAAA,MAA4B;;;;;;IAzB7DxB,EAAA,CAAAC,cAAA,cAAmG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAuD,kEAAA;MAAA,MAAAV,WAAA,GAAA/C,EAAA,CAAAI,aAAA,CAAAsD,GAAA,EAAA3C,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqD,WAAA,CAAAZ,WAAA,CAAAa,GAAA,CAAwB;IAAA,EAAC;IAChG5D,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAU,SAAA,cAAkG;IAClGV,EAAA,CAAA4B,UAAA,IAAAiC,kDAAA,kBAAyD;IAIvD7D,EADF,CAAAC,cAAA,cAA6B,iBAC6E;IAAhED,EAAA,CAAAE,UAAA,mBAAA4D,qEAAAC,MAAA;MAAA,MAAAhB,WAAA,GAAA/C,EAAA,CAAAI,aAAA,CAAAsD,GAAA,EAAA3C,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA0D,cAAA,CAAAjB,WAAA,CAAAa,GAAA,CAA2B;MAAA,OAAA5D,EAAA,CAAAQ,WAAA,CAAEuD,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IACrGjE,EAAA,CAAAU,SAAA,QAA6E;IAGnFV,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;IAEJX,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAiB,MAAA,GAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC3BX,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAiB,MAAA,IAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAI;IAEtCX,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAiB,MAAA,IAA8D;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IACjGX,EAAA,CAAA4B,UAAA,KAAAsC,oDAAA,mBAA2D;IAC7DlE,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA4B,UAAA,KAAAuC,mDAAA,kBAA2C;IASzCnE,EADF,CAAAC,cAAA,eAA6B,kBACyD;IAA3DD,EAAA,CAAAE,UAAA,mBAAAkE,sEAAAL,MAAA;MAAA,MAAAhB,WAAA,GAAA/C,EAAA,CAAAI,aAAA,CAAAsD,GAAA,EAAA3C,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+D,SAAA,CAAAtB,WAAA,CAAAa,GAAA,CAAsB;MAAA,OAAA5D,EAAA,CAAAQ,WAAA,CAAEuD,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IACjFjE,EAAA,CAAAU,SAAA,aAAoC;IACpCV,EAAA,CAAAiB,MAAA,qBACF;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAgF;IAAxDD,EAAA,CAAAE,UAAA,mBAAAoE,sEAAAP,MAAA;MAAA,MAAAhB,WAAA,GAAA/C,EAAA,CAAAI,aAAA,CAAAsD,GAAA,EAAA3C,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAiE,MAAA,CAAAxB,WAAA,CAAAa,GAAA,CAAmB;MAAA,OAAA5D,EAAA,CAAAQ,WAAA,CAAEuD,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IAC7EjE,EAAA,CAAAiB,MAAA,iBACF;IAGNjB,EAHM,CAAAW,YAAA,EAAS,EACL,EACF,EACF;;;;;IAnCGX,EAAA,CAAAkB,SAAA,GAAuE;IAAClB,EAAxE,CAAAiC,UAAA,SAAAc,WAAA,CAAAyB,MAAA,kBAAAzB,WAAA,CAAAyB,MAAA,kDAAAxE,EAAA,CAAAyE,aAAA,CAAuE,QAAA1B,WAAA,CAAAzB,IAAA,CAAqB;IACrEtB,EAAA,CAAAkB,SAAA,EAA2B;IAA3BlB,EAAA,CAAAiC,UAAA,SAAAc,WAAA,CAAA2B,aAAA,CAA2B;IAKhD1E,EAAA,CAAAkB,SAAA,GAAqE;IAArElB,EAAA,CAAAmB,UAAA,CAAAb,MAAA,CAAAqE,YAAA,CAAA5B,WAAA,CAAAa,GAAA,oCAAqE;IAKxE5D,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAqB,iBAAA,CAAA0B,WAAA,CAAAzB,IAAA,CAAkB;IACLtB,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAqB,iBAAA,CAAA0B,WAAA,CAAA6B,KAAA,CAAmB;IAEN5E,EAAA,CAAAkB,SAAA,GAA8D;IAA9DlB,EAAA,CAAAuB,kBAAA,WAAAvB,EAAA,CAAAgD,WAAA,SAAAD,WAAA,CAAA2B,aAAA,IAAA3B,WAAA,CAAAE,KAAA,eAA8D;IACnFjD,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAAiC,UAAA,SAAAc,WAAA,CAAA2B,aAAA,CAA2B;IAEf1E,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAAiC,UAAA,SAAAc,WAAA,CAAAK,MAAA,CAAoB;;;;;IApB/CpD,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAA4B,UAAA,IAAAiD,4CAAA,oBAAmG;IAsCrG7E,EAAA,CAAAW,YAAA,EAAM;;;;IAtCqBX,EAAA,CAAAkB,SAAA,EAAgB;IAAhBlB,EAAA,CAAAiC,UAAA,YAAA3B,MAAA,CAAAwE,aAAA,CAAgB;;;;;;IApDzC9E,EAFJ,CAAAC,cAAA,cAA8D,cAChC,SACtB;IAAAD,EAAA,CAAAiB,MAAA,qBAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IACvBX,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAiB,MAAA,GAAyC;IACvEjB,EADuE,CAAAW,YAAA,EAAO,EACxE;IAKFX,EAFJ,CAAAC,cAAA,cAA6B,cACD,YACjB;IAAAD,EAAA,CAAAiB,MAAA,gBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IACxBX,EAAA,CAAAC,cAAA,kBAAiE;IAAzDD,EAAA,CAAA+E,gBAAA,2BAAAC,iEAAAjB,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkF,kBAAA,CAAA5E,MAAA,CAAA6E,gBAAA,EAAApB,MAAA,MAAAzD,MAAA,CAAA6E,gBAAA,GAAApB,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAA8B;IAAC/D,EAAA,CAAAE,UAAA,oBAAAkF,0DAAA;MAAApF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA+E,YAAA,EAAc;IAAA,EAAC;IAC9DrF,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAiB,MAAA,sBAAc;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACxCX,EAAA,CAAA4B,UAAA,KAAA0D,yCAAA,qBAA+D;IAEnEtF,EADE,CAAAW,YAAA,EAAS,EACL;IAEJX,EADF,CAAAC,cAAA,eAA0B,aACjB;IAAAD,EAAA,CAAAiB,MAAA,oBAAY;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAC3BX,EAAA,CAAAC,cAAA,kBAAmE;IAA3DD,EAAA,CAAA+E,gBAAA,2BAAAQ,iEAAAxB,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkF,kBAAA,CAAA5E,MAAA,CAAAkF,kBAAA,EAAAzB,MAAA,MAAAzD,MAAA,CAAAkF,kBAAA,GAAAzB,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAAgC;IAAC/D,EAAA,CAAAE,UAAA,oBAAAuF,0DAAA;MAAAzF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA+E,YAAA,EAAc;IAAA,EAAC;IAChErF,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAiB,MAAA,kBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACpCX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,yBAAY;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAC5CX,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,iCAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAClDX,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,iCAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAClDX,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAiB,MAAA,kCAAgB;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACpDX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,0BAAa;IAExCjB,EAFwC,CAAAW,YAAA,EAAS,EACtC,EACL;IAEJX,EADF,CAAAC,cAAA,eAA0B,aACjB;IAAAD,EAAA,CAAAiB,MAAA,gBAAQ;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IACvBX,EAAA,CAAAC,cAAA,kBAAuD;IAA/CD,EAAA,CAAA+E,gBAAA,2BAAAW,iEAAA3B,MAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkF,kBAAA,CAAA5E,MAAA,CAAAqF,MAAA,EAAA5B,MAAA,MAAAzD,MAAA,CAAAqF,MAAA,GAAA5B,MAAA;MAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;IAAA,EAAoB;IAAC/D,EAAA,CAAAE,UAAA,oBAAA0F,0DAAA;MAAA5F,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA+E,YAAA,EAAc;IAAA,EAAC;IACpDrF,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,iBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAC5CX,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAiB,MAAA,0BAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACrDX,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAiB,MAAA,0BAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IACtDX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,oBAAY;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAC5CX,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAiB,MAAA,qBAAa;IAG1CjB,EAH0C,CAAAW,YAAA,EAAS,EACtC,EACL,EACF;IAiBNX,EAdA,CAAA4B,UAAA,KAAAiE,8CAAA,0BAUqD,KAAAC,sCAAA,kBAIM;IAwC7D9F,EAAA,CAAAW,YAAA,EAAM;;;;IA1F0BX,EAAA,CAAAkB,SAAA,GAAyC;IAAzClB,EAAA,CAAAuB,kBAAA,KAAAjB,MAAA,CAAAwE,aAAA,CAAAiB,MAAA,oBAAyC;IAO3D/F,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAgG,gBAAA,YAAA1F,MAAA,CAAA6E,gBAAA,CAA8B;IAEPnF,EAAA,CAAAkB,SAAA,GAAa;IAAblB,EAAA,CAAAiC,UAAA,YAAA3B,MAAA,CAAA2F,UAAA,CAAa;IAKpCjG,EAAA,CAAAkB,SAAA,GAAgC;IAAhClB,EAAA,CAAAgG,gBAAA,YAAA1F,MAAA,CAAAkF,kBAAA,CAAgC;IAWhCxF,EAAA,CAAAkB,SAAA,IAAoB;IAApBlB,EAAA,CAAAgG,gBAAA,YAAA1F,MAAA,CAAAqF,MAAA,CAAoB;IAY7B3F,EAAA,CAAAkB,SAAA,IAAgC;IAAhClB,EAAA,CAAAiC,UAAA,SAAA3B,MAAA,CAAAwE,aAAA,CAAAiB,MAAA,OAAgC;IAa7B/F,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAAiC,UAAA,SAAA3B,MAAA,CAAAwE,aAAA,CAAAiB,MAAA,KAA8B;;;;;;IAgDhC/F,EAAA,CAAAC,cAAA,eAA4F;IAA5BD,EAAA,CAAAE,UAAA,mBAAAgG,mEAAA;MAAA,MAAAC,UAAA,GAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA,EAAArF,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+F,SAAA,CAAAF,UAAA,CAAiB;IAAA,EAAC;IACzFnG,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAiB,MAAA,GACA;IAAAjB,EAAA,CAAAC,cAAA,YAAuF;IAA/DD,EAAA,CAAAE,UAAA,mBAAAoG,gEAAAvC,MAAA;MAAA,MAAAoC,UAAA,GAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA,EAAArF,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAiG,kBAAA,CAAAJ,UAAA,CAA0B;MAAA,OAAAnG,EAAA,CAAAQ,WAAA,CAAEuD,MAAA,CAAAE,eAAA,EAAwB;IAAA,EAAC;IACxFjE,EADyF,CAAAW,YAAA,EAAI,EACtF;;;;IAFLX,EAAA,CAAAkB,SAAA,GACA;IADAlB,EAAA,CAAAuB,kBAAA,MAAA4E,UAAA,MACA;;;;;IALJnG,EADF,CAAAC,cAAA,cAAuD,SACjD;IAAAD,EAAA,CAAAiB,MAAA,sBAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IACxBX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA4B,UAAA,IAAA4E,4CAAA,mBAA4F;IAMhGxG,EADE,CAAAW,YAAA,EAAM,EACF;;;;IANuBX,EAAA,CAAAkB,SAAA,GAAiB;IAAjBlB,EAAA,CAAAiC,UAAA,YAAA3B,MAAA,CAAAmG,cAAA,CAAiB;;;;;;IAY1CzG,EAAA,CAAAC,cAAA,eAAqG;IAA5BD,EAAA,CAAAE,UAAA,mBAAAwG,6DAAA;MAAA,MAAAC,UAAA,GAAA3G,EAAA,CAAAI,aAAA,CAAAwG,IAAA,EAAA7F,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+F,SAAA,CAAAM,UAAA,CAAiB;IAAA,EAAC;IAClG3G,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAuB,kBAAA,MAAAoF,UAAA,MACF;;;;;;IAQA3G,EAAA,CAAAC,cAAA,cAAyG;IAAxCD,EAAA,CAAAE,UAAA,mBAAA2G,4DAAA;MAAA,MAAAC,YAAA,GAAA9G,EAAA,CAAAI,aAAA,CAAA2G,IAAA,EAAAhG,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0G,cAAA,CAAAF,YAAA,CAAAxF,IAAA,CAA6B;IAAA,EAAC;IACtGtB,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAU,SAAA,QAA+B;IACjCV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC5BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAiB,MAAA,GAA0B;IAC/BjB,EAD+B,CAAAW,YAAA,EAAI,EAC7B;;;;IAJCX,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAAmB,UAAA,CAAA2F,YAAA,CAAA1F,IAAA,CAAuB;IAExBpB,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAqB,iBAAA,CAAAyF,YAAA,CAAAxF,IAAA,CAAmB;IACpBtB,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAuB,kBAAA,KAAAuF,YAAA,CAAAtF,KAAA,WAA0B;;;;;IAjCrCxB,EAAA,CAAAC,cAAA,cAAgE;IAE9DD,EAAA,CAAA4B,UAAA,IAAAqF,qCAAA,kBAAuD;IAarDjH,EADF,CAAAC,cAAA,cAAqB,SACf;IAAAD,EAAA,CAAAiB,MAAA,uBAAgB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IACzBX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA4B,UAAA,IAAAsF,sCAAA,mBAAqG;IAKzGlH,EADE,CAAAW,YAAA,EAAM,EACF;IAIJX,EADF,CAAAC,cAAA,cAAqB,SACf;IAAAD,EAAA,CAAAiB,MAAA,wBAAiB;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC1BX,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA4B,UAAA,KAAAuF,sCAAA,kBAAyG;IAS/GnH,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;IAnCEX,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAAiC,UAAA,SAAA3B,MAAA,CAAAmG,cAAA,CAAAV,MAAA,KAA+B;IAeR/F,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAiC,UAAA,YAAA3B,MAAA,CAAA8G,eAAA,CAAkB;IAWjBpH,EAAA,CAAAkB,SAAA,GAAe;IAAflB,EAAA,CAAAiC,UAAA,YAAA3B,MAAA,CAAA+G,YAAA,CAAe;;;AA+oBzD,OAAM,MAAOC,eAAe;EA2C1BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA9C7B,KAAA3F,WAAW,GAAG,EAAE;IAChB,KAAA8C,aAAa,GAAU,EAAE;IACzB,KAAA2B,cAAc,GAAa,EAAE;IAC7B,KAAAmB,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAG,KAAK;IAEnB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAA5F,mBAAmB,GAAU,EAAE;IAC/B,KAAA6F,YAAY,GAAqC,SAAS;IAE1D;IACA,KAAA5C,gBAAgB,GAAG,EAAE;IACrB,KAAAK,kBAAkB,GAAG,EAAE;IACvB,KAAAG,MAAM,GAAG,WAAW;IACpB,KAAAM,UAAU,GAAa,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,CAAC;IAEzF;IACA,KAAAmB,eAAe,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACtH,KAAAC,YAAY,GAAG,CACb;MAAE/F,IAAI,EAAE,KAAK;MAAEF,IAAI,EAAE,aAAa;MAAEI,KAAK,EAAE;IAAI,CAAE,EACjD;MAAEF,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE,eAAe;MAAEI,KAAK,EAAE;IAAI,CAAE,EACrD;MAAEF,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE,cAAc;MAAEI,KAAK,EAAE;IAAG,CAAE,EAClD;MAAEF,IAAI,EAAE,aAAa;MAAEF,IAAI,EAAE,YAAY;MAAEI,KAAK,EAAE;IAAG,CAAE,EACvD;MAAEF,IAAI,EAAE,UAAU;MAAEF,IAAI,EAAE,oBAAoB;MAAEI,KAAK,EAAE;IAAG,CAAE,EAC5D;MAAEF,IAAI,EAAE,aAAa;MAAEF,IAAI,EAAE,mBAAmB;MAAEI,KAAK,EAAE;IAAG,CAAE,CAC/D;IAED;IACA,KAAAwG,gBAAgB,GAAgC;MAC9C,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC;MACpF,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;MACxH,MAAM,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;MAClE,aAAa,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;MAC3F,UAAU,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC;MAC1E,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW;KACjF;IAED,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAA7F,YAAY,GAAQ,EAAE;IACd,KAAA8F,aAAa,GAAG,IAAIpI,OAAO,EAAU;IAQ3C,IAAI,CAACsC,YAAY,GAAG,IAAI,CAACuF,mBAAmB,CAACQ,eAAe,EAAE;EAChE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACd,KAAK,CAACe,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,GAAG,CAAC,EAAE;QACf,IAAI,CAACzG,WAAW,GAAGyG,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAACC,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF;IACA,IAAI,CAACR,aAAa,CAACS,IAAI,CACrBhJ,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC+I,KAAK,IAAG;MAChB,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAC9C,MAAM,GAAG,CAAC,EAAE;QAC3B,IAAI,CAAC6B,SAAS,GAAG,IAAI;QACrB,MAAMkB,YAAY,GAAG;UACnBC,MAAM,EAAEH,KAAK,CAACC,IAAI,EAAE;UACpBG,QAAQ,EAAE,IAAI,CAAC7D,gBAAgB;UAC/B8D,QAAQ,EAAE,IAAI,CAACC,aAAa,EAAE,CAACC,GAAG;UAClCC,QAAQ,EAAE,IAAI,CAACF,aAAa,EAAE,CAACG,GAAG;UAClC1D,MAAM,EAAE,IAAI,CAAC2D,YAAY,EAAE;UAC3BC,SAAS,EAAE,IAAI,CAACC,YAAY,EAAE;UAC9BC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE;SACR;QACD,OAAO,IAAI,CAAChC,cAAc,CAACiC,WAAW,CAACb,YAAY,CAAC;OACrD,MAAM;QACL,IAAI,CAAChE,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC+C,WAAW,GAAG,KAAK;QACxB,IAAI,CAACD,SAAS,GAAG,KAAK;QACtB,OAAO,EAAE;;IAEb,CAAC,CAAC,CACH,CAACY,SAAS,CAAC;MACVoB,IAAI,EAAGC,QAAa,IAAI;QACtBC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,QAAQ,CAAC;QACzC,IAAI,CAAC/E,aAAa,GAAG+E,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACI,IAAI,EAAED,QAAQ,IAAI,EAAE;QACvE,IAAI,CAACnC,WAAW,GAAG,IAAI;QACvB,IAAI,CAACD,SAAS,GAAG,KAAK;MACxB,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC,IAAI,CAACtC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC/C,aAAa,GAAG,EAAE;QACvB;QACA,IAAI,CAACqF,gBAAgB,CAAC,kCAAkC,CAAC;MAC3D;KACD,CAAC;EACJ;EAEAC,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACrI,WAAW,GAAGqI,KAAK,CAACC,MAAM,CAACC,KAAK;IACrC,IAAI,IAAI,CAACvI,WAAW,CAAC6G,IAAI,EAAE,CAAC9C,MAAM,GAAG,CAAC,EAAE;MACtC;MACA,IAAI,CAACyE,kBAAkB,CAAC,IAAI,CAACxI,WAAW,CAAC;MACzC,IAAI,CAACkG,aAAa,CAAC0B,IAAI,CAAC,IAAI,CAAC5H,WAAW,CAAC;KAC1C,MAAM;MACL,IAAI,CAAC8C,aAAa,GAAG,EAAE;MACvB,IAAI,CAAC+C,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAAC5F,mBAAmB,GAAG,EAAE;;EAEjC;EAEAsI,kBAAkBA,CAAC5B,KAAa;IAC9B,MAAM6B,UAAU,GAAG7B,KAAK,CAAC8B,WAAW,EAAE,CAAC7B,IAAI,EAAE;IAC7C,IAAI,CAAC3G,mBAAmB,GAAG,EAAE;IAE7B;IACA,KAAK,MAAM,CAAC8G,QAAQ,EAAE2B,QAAQ,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC7C,gBAAgB,CAAC,EAAE;MACxE,IAAI2C,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAIN,UAAU,CAACO,QAAQ,CAACD,OAAO,CAAC,CAAC,EAAE;QAC1D,MAAME,YAAY,GAAG,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC7J,IAAI,KAAK0H,QAAQ,CAAC;QACzE,IAAIiC,YAAY,IAAI,CAAC,IAAI,CAAC/I,mBAAmB,CAACgJ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC7J,IAAI,KAAK0H,QAAQ,CAAC,EAAE;UAChF,IAAI,CAAC9G,mBAAmB,CAACkJ,IAAI,CAACH,YAAY,CAAC;;;;IAKjD;IACA,IAAI,CAACnD,qBAAqB,GAAG,IAAI,CAAC5F,mBAAmB,CAAC6D,MAAM,GAAG,CAAC;IAEhE;IACA,IAAI,IAAI,CAAC7D,mBAAmB,CAAC6D,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACgC,YAAY,GAAG,UAAU;KAC/B,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,SAAS;;EAEjC;EAEAsD,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACrJ,WAAW,CAAC6G,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACyC,gBAAgB,CAAC,IAAI,CAACtJ,WAAW,CAAC6G,IAAI,EAAE,CAAC;MAC9C,IAAI,CAACH,aAAa,EAAE;;EAExB;EAEAA,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC1G,WAAW,CAAC6G,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACX,aAAa,CAAC0B,IAAI,CAAC,IAAI,CAAC5H,WAAW,CAAC;;EAE7C;EAEAvB,WAAWA,CAAA;IACT,IAAI,CAACuB,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC8C,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC+C,WAAW,GAAG,KAAK;EAC1B;EAEAxB,SAASA,CAACkF,IAAY;IACpB,IAAI,CAACvJ,WAAW,GAAGuJ,IAAI;IACvB,IAAI,CAACD,gBAAgB,CAACC,IAAI,CAAC;IAC3B,IAAI,CAAC7C,aAAa,EAAE;EACtB;EAEArD,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrD,WAAW,EAAE;MACpB,IAAI,CAAC0G,aAAa,EAAE;;EAExB;EAEA1B,cAAcA,CAACgC,QAAgB;IAC7B,IAAI,CAAC7D,gBAAgB,GAAG6D,QAAQ;IAChC,IAAI,CAAChH,WAAW,GAAGgH,QAAQ;IAC3B,IAAI,CAACN,aAAa,EAAE;EACtB;EAEA1H,uBAAuBA,CAACgI,QAAa;IACnC;IACA,IAAI,CAAC7D,gBAAgB,GAAG6D,QAAQ,CAAC1H,IAAI;IACrC,IAAI,CAACwG,qBAAqB,GAAG,KAAK;IAElC;IACA,MAAM0D,aAAa,GAAG,IAAI,CAACxJ,WAAW;IACtC,IAAI,CAACA,WAAW,GAAG,GAAGwJ,aAAa,OAAOxC,QAAQ,CAAC1H,IAAI,EAAE;IAEzD;IACA,IAAI,CAACmG,MAAM,CAACgE,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BlD,WAAW,EAAE;QACXmD,CAAC,EAAEF,aAAa;QAChBxC,QAAQ,EAAEA,QAAQ,CAAC1H,IAAI,CAACoJ,WAAW,EAAE;QACrCiB,MAAM,EAAE;;KAEX,CAAC;EACJ;EAEAhK,wBAAwBA,CAAA;IACtB,IAAI,CAACmG,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAAC5F,mBAAmB,GAAG,EAAE;EAC/B;EAEAH,qBAAqBA,CAAA;IACnB,IAAI,CAAC+F,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAAC3C,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACuD,aAAa,EAAE;EACtB;EAEA/E,WAAWA,CAACiI,SAAiB;IAC3B,IAAI,CAACnE,MAAM,CAACgE,QAAQ,CAAC,CAAC,eAAe,EAAEG,SAAS,CAAC,CAAC;EACpD;EAEA5H,cAAcA,CAAC4H,SAAiB;IAC9B,IAAI,IAAI,CAACjH,YAAY,CAACiH,SAAS,CAAC,EAAE;MAChC,IAAI,CAAC3D,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC4D,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKF,SAAS,CAAC;KACvE,MAAM;MACL,IAAI,CAAC3D,aAAa,CAACmD,IAAI,CAACQ,SAAS,CAAC;;IAEpCG,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACjE,aAAa,CAAC,CAAC;EACtE;EAEAtD,YAAYA,CAACiH,SAAiB;IAC5B,OAAO,IAAI,CAAC3D,aAAa,CAAC+C,QAAQ,CAACY,SAAS,CAAC;EAC/C;EAEAvH,SAASA,CAACuH,SAAiB;IACzB;IACA9B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6B,SAAS,CAAC;IACxC,IAAI,CAACO,gBAAgB,CAAC,gBAAgB,EAAE,SAAS,CAAC;EACpD;EAEA5H,MAAMA,CAACqH,SAAiB;IACtB;IACA9B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6B,SAAS,CAAC;IAClC,IAAI,CAACO,gBAAgB,CAAC,4BAA4B,EAAE,MAAM,CAAC;EAC7D;EAEArJ,qBAAqBA,CAACsJ,OAAY;IAChC,IAAIA,OAAO,CAAC1H,aAAa,EAAE;MACzB,OAAO2H,IAAI,CAACC,KAAK,CAAE,CAACF,OAAO,CAACnJ,KAAK,GAAGmJ,OAAO,CAAC1H,aAAa,IAAI0H,OAAO,CAACnJ,KAAK,GAAI,GAAG,CAAC;;IAEpF,OAAO,CAAC;EACV;EAEAsD,kBAAkBA,CAACgF,IAAY;IAC7B,IAAI,CAAC9E,cAAc,GAAG,IAAI,CAACA,cAAc,CAACoF,MAAM,CAAC9C,MAAM,IAAIA,MAAM,KAAKwC,IAAI,CAAC;IAC3EQ,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzF,cAAc,CAAC,CAAC;EAC7E;EAEQ4B,kBAAkBA,CAAA;IACxB,MAAMkE,KAAK,GAAGR,YAAY,CAACS,OAAO,CAAC,gBAAgB,CAAC;IACpD,IAAI,CAAC/F,cAAc,GAAG8F,KAAK,GAAGN,IAAI,CAACQ,KAAK,CAACF,KAAK,CAAC,GAAG,EAAE;EACtD;EAEQjE,iBAAiBA,CAAA;IACvB,MAAMiE,KAAK,GAAGR,YAAY,CAACS,OAAO,CAAC,UAAU,CAAC;IAC9C,IAAI,CAACvE,aAAa,GAAGsE,KAAK,GAAGN,IAAI,CAACQ,KAAK,CAACF,KAAK,CAAC,GAAG,EAAE;EACrD;EAEQjB,gBAAgBA,CAACC,IAAY;IACnC,IAAI,CAAC9E,cAAc,GAAG,IAAI,CAACA,cAAc,CAACoF,MAAM,CAAC9C,MAAM,IAAIA,MAAM,KAAKwC,IAAI,CAAC;IAC3E,IAAI,CAAC9E,cAAc,CAACiG,OAAO,CAACnB,IAAI,CAAC;IACjC,IAAI,CAAC9E,cAAc,GAAG,IAAI,CAACA,cAAc,CAACkG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACtDZ,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzF,cAAc,CAAC,CAAC;EAC7E;EAEQ0D,gBAAgBA,CAAC7H,OAAe;IACtC;IACA,MAAMsK,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,2BAA2B;IACpDH,YAAY,CAACI,WAAW,GAAG1K,OAAO;IAClCsK,YAAY,CAACK,KAAK,CAACC,OAAO,GAAG;;;;;;;;;;;;KAY5B;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,YAAY,CAAC;IAEvCS,UAAU,CAAC,MAAK;MACdT,YAAY,CAACU,MAAM,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQpE,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC1D,kBAAkB,EAAE,OAAO;MAAE2D,GAAG,EAAEoE,SAAS;MAAElE,GAAG,EAAEkE;IAAS,CAAE;IAEvE,MAAMC,MAAM,GAAsD;MAChE,QAAQ,EAAE;QAAErE,GAAG,EAAE,CAAC;QAAEE,GAAG,EAAE;MAAI,CAAE;MAC/B,WAAW,EAAE;QAAEF,GAAG,EAAE,IAAI;QAAEE,GAAG,EAAE;MAAI,CAAE;MACrC,WAAW,EAAE;QAAEF,GAAG,EAAE,IAAI;QAAEE,GAAG,EAAE;MAAI,CAAE;MACrC,YAAY,EAAE;QAAEF,GAAG,EAAE,IAAI;QAAEE,GAAG,EAAE;MAAK,CAAE;MACvC,QAAQ,EAAE;QAAEF,GAAG,EAAE;MAAK;KACvB;IAED,OAAOqE,MAAM,CAAC,IAAI,CAAChI,kBAAkB,CAAC,IAAI;MAAE2D,GAAG,EAAEoE,SAAS;MAAElE,GAAG,EAAEkE;IAAS,CAAE;EAC9E;EAEQjE,YAAYA,CAAA;IAClB,MAAMmE,OAAO,GAA8B;MACzC,WAAW,EAAE,WAAW;MACxB,WAAW,EAAE,OAAO;MACpB,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,WAAW;MACrB,QAAQ,EAAE;KACX;IACD,OAAOA,OAAO,CAAC,IAAI,CAAC9H,MAAM,CAAC,IAAI,WAAW;EAC5C;EAEQ6D,YAAYA,CAAA;IAClB,OAAO,IAAI,CAAC7D,MAAM,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;EACtD;EAEQwG,gBAAgBA,CAAC7J,OAAe,EAAEoL,IAAkC;IAC1E,MAAMd,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,6BAA6BW,IAAI,EAAE;IAC5Dd,YAAY,CAACI,WAAW,GAAG1K,OAAO;IAElCsK,YAAY,CAACK,KAAK,CAACC,OAAO,GAAG;;;;oBAIbQ,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGA,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;;;;;;;;;KASxF;IAEDb,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,YAAY,CAAC;IACvCS,UAAU,CAAC,MAAMT,YAAY,CAACU,MAAM,EAAE,EAAE,IAAI,CAAC;EAC/C;;;uBA5VWhG,eAAe,EAAAtH,EAAA,CAAA2N,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7N,EAAA,CAAA2N,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA9N,EAAA,CAAA2N,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAhO,EAAA,CAAA2N,iBAAA,CAAAM,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAf5G,eAAe;MAAA6G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArO,EAAA,CAAAsO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UAn1BhB5O,EALR,CAAAC,cAAA,aAAyB,aAEI,aACF,aACa,aACE;UAChCD,EAAA,CAAAU,SAAA,WAAyC;UACzCV,EAAA,CAAAC,cAAA,kBAQC;UAJCD,EAAA,CAAA+E,gBAAA,2BAAA+J,wDAAA/K,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAA2O,GAAA;YAAA/O,EAAA,CAAAkF,kBAAA,CAAA2J,GAAA,CAAA7M,WAAA,EAAA+B,MAAA,MAAA8K,GAAA,CAAA7M,WAAA,GAAA+B,MAAA;YAAA,OAAA/D,EAAA,CAAAQ,WAAA,CAAAuD,MAAA;UAAA,EAAyB;UAEzB/D,EADA,CAAAE,UAAA,mBAAA8O,gDAAAjL,MAAA;YAAA/D,EAAA,CAAAI,aAAA,CAAA2O,GAAA;YAAA,OAAA/O,EAAA,CAAAQ,WAAA,CAASqO,GAAA,CAAAzE,aAAA,CAAArG,MAAA,CAAqB;UAAA,EAAC,yBAAAkL,sDAAA;YAAAjP,EAAA,CAAAI,aAAA,CAAA2O,GAAA;YAAA,OAAA/O,EAAA,CAAAQ,WAAA,CAChBqO,GAAA,CAAAxD,cAAA,EAAgB;UAAA,EAAC;UANlCrL,EAAA,CAAAW,YAAA,EAQC;UACDX,EAAA,CAAA4B,UAAA,IAAAsN,iCAAA,oBAIC;UAMTlP,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;UAGNX,EAAA,CAAA4B,UAAA,IAAAuN,8BAAA,kBAAsE;UAwCpEnP,EADF,CAAAC,cAAA,eAA4B,cACH;UAwGrBD,EAtGA,CAAA4B,UAAA,KAAAwN,+BAAA,kBAAiD,KAAAC,+BAAA,mBAMa,KAAAC,+BAAA,mBAgGE;UAwCtEtP,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;UA1MMX,EAAA,CAAAkB,SAAA,GAAyB;UAAzBlB,EAAA,CAAAgG,gBAAA,YAAA6I,GAAA,CAAA7M,WAAA,CAAyB;UAMxBhC,EAAA,CAAAkB,SAAA,GAAiB;UAAjBlB,EAAA,CAAAiC,UAAA,SAAA4M,GAAA,CAAA7M,WAAA,CAAiB;UAYtBhC,EAAA,CAAAkB,SAAA,EAA2B;UAA3BlB,EAAA,CAAAiC,UAAA,SAAA4M,GAAA,CAAA/G,qBAAA,CAA2B;UA0CvB9H,EAAA,CAAAkB,SAAA,GAAe;UAAflB,EAAA,CAAAiC,UAAA,SAAA4M,GAAA,CAAAjH,SAAA,CAAe;UAMf5H,EAAA,CAAAkB,SAAA,EAA+B;UAA/BlB,EAAA,CAAAiC,UAAA,SAAA4M,GAAA,CAAAhH,WAAA,KAAAgH,GAAA,CAAAjH,SAAA,CAA+B;UAgG/B5H,EAAA,CAAAkB,SAAA,EAAgC;UAAhClB,EAAA,CAAAiC,UAAA,UAAA4M,GAAA,CAAAhH,WAAA,KAAAgH,GAAA,CAAAjH,SAAA,CAAgC;;;qBA/KpCnI,YAAY,EAAA8P,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEhQ,WAAW,EAAAiQ,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,0BAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,OAAA,EAAElQ,eAAe;MAAAmQ,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}