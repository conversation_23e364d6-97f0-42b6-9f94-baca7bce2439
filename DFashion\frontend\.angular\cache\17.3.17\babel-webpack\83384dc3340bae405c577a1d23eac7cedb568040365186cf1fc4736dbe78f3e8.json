{"ast": null, "code": "/*!\n    localForage -- Offline Storage, Improved\n    Version 1.10.0\n    https://localforage.github.io/localForage\n    (c) 2013-2017 Mozilla, Apache License 2.0\n*/\n(function (f) {\n  if (typeof exports === \"object\" && typeof module !== \"undefined\") {\n    module.exports = f();\n  } else if (typeof define === \"function\" && define.amd) {\n    define([], f);\n  } else {\n    var g;\n    if (typeof window !== \"undefined\") {\n      g = window;\n    } else if (typeof global !== \"undefined\") {\n      g = global;\n    } else if (typeof self !== \"undefined\") {\n      g = self;\n    } else {\n      g = this;\n    }\n    g.localforage = f();\n  }\n})(function () {\n  var define, module, exports;\n  return function e(t, n, r) {\n    function s(o, u) {\n      if (!n[o]) {\n        if (!t[o]) {\n          var a = typeof require == \"function\" && require;\n          if (!u && a) return a(o, !0);\n          if (i) return i(o, !0);\n          var f = new Error(\"Cannot find module '\" + o + \"'\");\n          throw f.code = \"MODULE_NOT_FOUND\", f;\n        }\n        var l = n[o] = {\n          exports: {}\n        };\n        t[o][0].call(l.exports, function (e) {\n          var n = t[o][1][e];\n          return s(n ? n : e);\n        }, l, l.exports, e, t, n, r);\n      }\n      return n[o].exports;\n    }\n    var i = typeof require == \"function\" && require;\n    for (var o = 0; o < r.length; o++) s(r[o]);\n    return s;\n  }({\n    1: [function (_dereq_, module, exports) {\n      (function (global) {\n        'use strict';\n\n        var Mutation = global.MutationObserver || global.WebKitMutationObserver;\n        var scheduleDrain;\n        {\n          if (Mutation) {\n            var called = 0;\n            var observer = new Mutation(nextTick);\n            var element = global.document.createTextNode('');\n            observer.observe(element, {\n              characterData: true\n            });\n            scheduleDrain = function () {\n              element.data = called = ++called % 2;\n            };\n          } else if (!global.setImmediate && typeof global.MessageChannel !== 'undefined') {\n            var channel = new global.MessageChannel();\n            channel.port1.onmessage = nextTick;\n            scheduleDrain = function () {\n              channel.port2.postMessage(0);\n            };\n          } else if ('document' in global && 'onreadystatechange' in global.document.createElement('script')) {\n            scheduleDrain = function () {\n              // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n              // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n              var scriptEl = global.document.createElement('script');\n              scriptEl.onreadystatechange = function () {\n                nextTick();\n                scriptEl.onreadystatechange = null;\n                scriptEl.parentNode.removeChild(scriptEl);\n                scriptEl = null;\n              };\n              global.document.documentElement.appendChild(scriptEl);\n            };\n          } else {\n            scheduleDrain = function () {\n              setTimeout(nextTick, 0);\n            };\n          }\n        }\n        var draining;\n        var queue = [];\n        //named nextTick for less confusing stack traces\n        function nextTick() {\n          draining = true;\n          var i, oldQueue;\n          var len = queue.length;\n          while (len) {\n            oldQueue = queue;\n            queue = [];\n            i = -1;\n            while (++i < len) {\n              oldQueue[i]();\n            }\n            len = queue.length;\n          }\n          draining = false;\n        }\n        module.exports = immediate;\n        function immediate(task) {\n          if (queue.push(task) === 1 && !draining) {\n            scheduleDrain();\n          }\n        }\n      }).call(this, typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {});\n    }, {}],\n    2: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var immediate = _dereq_(1);\n\n      /* istanbul ignore next */\n      function INTERNAL() {}\n      var handlers = {};\n      var REJECTED = ['REJECTED'];\n      var FULFILLED = ['FULFILLED'];\n      var PENDING = ['PENDING'];\n      module.exports = Promise;\n      function Promise(resolver) {\n        if (typeof resolver !== 'function') {\n          throw new TypeError('resolver must be a function');\n        }\n        this.state = PENDING;\n        this.queue = [];\n        this.outcome = void 0;\n        if (resolver !== INTERNAL) {\n          safelyResolveThenable(this, resolver);\n        }\n      }\n      Promise.prototype[\"catch\"] = function (onRejected) {\n        return this.then(null, onRejected);\n      };\n      Promise.prototype.then = function (onFulfilled, onRejected) {\n        if (typeof onFulfilled !== 'function' && this.state === FULFILLED || typeof onRejected !== 'function' && this.state === REJECTED) {\n          return this;\n        }\n        var promise = new this.constructor(INTERNAL);\n        if (this.state !== PENDING) {\n          var resolver = this.state === FULFILLED ? onFulfilled : onRejected;\n          unwrap(promise, resolver, this.outcome);\n        } else {\n          this.queue.push(new QueueItem(promise, onFulfilled, onRejected));\n        }\n        return promise;\n      };\n      function QueueItem(promise, onFulfilled, onRejected) {\n        this.promise = promise;\n        if (typeof onFulfilled === 'function') {\n          this.onFulfilled = onFulfilled;\n          this.callFulfilled = this.otherCallFulfilled;\n        }\n        if (typeof onRejected === 'function') {\n          this.onRejected = onRejected;\n          this.callRejected = this.otherCallRejected;\n        }\n      }\n      QueueItem.prototype.callFulfilled = function (value) {\n        handlers.resolve(this.promise, value);\n      };\n      QueueItem.prototype.otherCallFulfilled = function (value) {\n        unwrap(this.promise, this.onFulfilled, value);\n      };\n      QueueItem.prototype.callRejected = function (value) {\n        handlers.reject(this.promise, value);\n      };\n      QueueItem.prototype.otherCallRejected = function (value) {\n        unwrap(this.promise, this.onRejected, value);\n      };\n      function unwrap(promise, func, value) {\n        immediate(function () {\n          var returnValue;\n          try {\n            returnValue = func(value);\n          } catch (e) {\n            return handlers.reject(promise, e);\n          }\n          if (returnValue === promise) {\n            handlers.reject(promise, new TypeError('Cannot resolve promise with itself'));\n          } else {\n            handlers.resolve(promise, returnValue);\n          }\n        });\n      }\n      handlers.resolve = function (self, value) {\n        var result = tryCatch(getThen, value);\n        if (result.status === 'error') {\n          return handlers.reject(self, result.value);\n        }\n        var thenable = result.value;\n        if (thenable) {\n          safelyResolveThenable(self, thenable);\n        } else {\n          self.state = FULFILLED;\n          self.outcome = value;\n          var i = -1;\n          var len = self.queue.length;\n          while (++i < len) {\n            self.queue[i].callFulfilled(value);\n          }\n        }\n        return self;\n      };\n      handlers.reject = function (self, error) {\n        self.state = REJECTED;\n        self.outcome = error;\n        var i = -1;\n        var len = self.queue.length;\n        while (++i < len) {\n          self.queue[i].callRejected(error);\n        }\n        return self;\n      };\n      function getThen(obj) {\n        // Make sure we only access the accessor once as required by the spec\n        var then = obj && obj.then;\n        if (obj && (typeof obj === 'object' || typeof obj === 'function') && typeof then === 'function') {\n          return function appyThen() {\n            then.apply(obj, arguments);\n          };\n        }\n      }\n      function safelyResolveThenable(self, thenable) {\n        // Either fulfill, reject or reject with error\n        var called = false;\n        function onError(value) {\n          if (called) {\n            return;\n          }\n          called = true;\n          handlers.reject(self, value);\n        }\n        function onSuccess(value) {\n          if (called) {\n            return;\n          }\n          called = true;\n          handlers.resolve(self, value);\n        }\n        function tryToUnwrap() {\n          thenable(onSuccess, onError);\n        }\n        var result = tryCatch(tryToUnwrap);\n        if (result.status === 'error') {\n          onError(result.value);\n        }\n      }\n      function tryCatch(func, value) {\n        var out = {};\n        try {\n          out.value = func(value);\n          out.status = 'success';\n        } catch (e) {\n          out.status = 'error';\n          out.value = e;\n        }\n        return out;\n      }\n      Promise.resolve = resolve;\n      function resolve(value) {\n        if (value instanceof this) {\n          return value;\n        }\n        return handlers.resolve(new this(INTERNAL), value);\n      }\n      Promise.reject = reject;\n      function reject(reason) {\n        var promise = new this(INTERNAL);\n        return handlers.reject(promise, reason);\n      }\n      Promise.all = all;\n      function all(iterable) {\n        var self = this;\n        if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n          return this.reject(new TypeError('must be an array'));\n        }\n        var len = iterable.length;\n        var called = false;\n        if (!len) {\n          return this.resolve([]);\n        }\n        var values = new Array(len);\n        var resolved = 0;\n        var i = -1;\n        var promise = new this(INTERNAL);\n        while (++i < len) {\n          allResolver(iterable[i], i);\n        }\n        return promise;\n        function allResolver(value, i) {\n          self.resolve(value).then(resolveFromAll, function (error) {\n            if (!called) {\n              called = true;\n              handlers.reject(promise, error);\n            }\n          });\n          function resolveFromAll(outValue) {\n            values[i] = outValue;\n            if (++resolved === len && !called) {\n              called = true;\n              handlers.resolve(promise, values);\n            }\n          }\n        }\n      }\n      Promise.race = race;\n      function race(iterable) {\n        var self = this;\n        if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n          return this.reject(new TypeError('must be an array'));\n        }\n        var len = iterable.length;\n        var called = false;\n        if (!len) {\n          return this.resolve([]);\n        }\n        var i = -1;\n        var promise = new this(INTERNAL);\n        while (++i < len) {\n          resolver(iterable[i]);\n        }\n        return promise;\n        function resolver(value) {\n          self.resolve(value).then(function (response) {\n            if (!called) {\n              called = true;\n              handlers.resolve(promise, response);\n            }\n          }, function (error) {\n            if (!called) {\n              called = true;\n              handlers.reject(promise, error);\n            }\n          });\n        }\n      }\n    }, {\n      \"1\": 1\n    }],\n    3: [function (_dereq_, module, exports) {\n      (function (global) {\n        'use strict';\n\n        if (typeof global.Promise !== 'function') {\n          global.Promise = _dereq_(2);\n        }\n      }).call(this, typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {});\n    }, {\n      \"2\": 2\n    }],\n    4: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n        return typeof obj;\n      } : function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n      function _classCallCheck(instance, Constructor) {\n        if (!(instance instanceof Constructor)) {\n          throw new TypeError(\"Cannot call a class as a function\");\n        }\n      }\n      function getIDB() {\n        /* global indexedDB,webkitIndexedDB,mozIndexedDB,OIndexedDB,msIndexedDB */\n        try {\n          if (typeof indexedDB !== 'undefined') {\n            return indexedDB;\n          }\n          if (typeof webkitIndexedDB !== 'undefined') {\n            return webkitIndexedDB;\n          }\n          if (typeof mozIndexedDB !== 'undefined') {\n            return mozIndexedDB;\n          }\n          if (typeof OIndexedDB !== 'undefined') {\n            return OIndexedDB;\n          }\n          if (typeof msIndexedDB !== 'undefined') {\n            return msIndexedDB;\n          }\n        } catch (e) {\n          return;\n        }\n      }\n      var idb = getIDB();\n      function isIndexedDBValid() {\n        try {\n          // Initialize IndexedDB; fall back to vendor-prefixed versions\n          // if needed.\n          if (!idb || !idb.open) {\n            return false;\n          }\n          // We mimic PouchDB here;\n          //\n          // We test for openDatabase because IE Mobile identifies itself\n          // as Safari. Oh the lulz...\n          var isSafari = typeof openDatabase !== 'undefined' && /(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent) && !/BlackBerry/.test(navigator.platform);\n          var hasFetch = typeof fetch === 'function' && fetch.toString().indexOf('[native code') !== -1;\n\n          // Safari <10.1 does not meet our requirements for IDB support\n          // (see: https://github.com/pouchdb/pouchdb/issues/5572).\n          // Safari 10.1 shipped with fetch, we can use that to detect it.\n          // Note: this creates issues with `window.fetch` polyfills and\n          // overrides; see:\n          // https://github.com/localForage/localForage/issues/856\n          return (!isSafari || hasFetch) && typeof indexedDB !== 'undefined' &&\n          // some outdated implementations of IDB that appear on Samsung\n          // and HTC Android devices <4.4 are missing IDBKeyRange\n          // See: https://github.com/mozilla/localForage/issues/128\n          // See: https://github.com/mozilla/localForage/issues/272\n          typeof IDBKeyRange !== 'undefined';\n        } catch (e) {\n          return false;\n        }\n      }\n\n      // Abstracts constructing a Blob object, so it also works in older\n      // browsers that don't support the native Blob constructor. (i.e.\n      // old QtWebKit versions, at least).\n      // Abstracts constructing a Blob object, so it also works in older\n      // browsers that don't support the native Blob constructor. (i.e.\n      // old QtWebKit versions, at least).\n      function createBlob(parts, properties) {\n        /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n        parts = parts || [];\n        properties = properties || {};\n        try {\n          return new Blob(parts, properties);\n        } catch (e) {\n          if (e.name !== 'TypeError') {\n            throw e;\n          }\n          var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder : typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder : typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : WebKitBlobBuilder;\n          var builder = new Builder();\n          for (var i = 0; i < parts.length; i += 1) {\n            builder.append(parts[i]);\n          }\n          return builder.getBlob(properties.type);\n        }\n      }\n\n      // This is CommonJS because lie is an external dependency, so Rollup\n      // can just ignore it.\n      if (typeof Promise === 'undefined') {\n        // In the \"nopromises\" build this will just throw if you don't have\n        // a global promise object, but it would throw anyway later.\n        _dereq_(3);\n      }\n      var Promise$1 = Promise;\n      function executeCallback(promise, callback) {\n        if (callback) {\n          promise.then(function (result) {\n            callback(null, result);\n          }, function (error) {\n            callback(error);\n          });\n        }\n      }\n      function executeTwoCallbacks(promise, callback, errorCallback) {\n        if (typeof callback === 'function') {\n          promise.then(callback);\n        }\n        if (typeof errorCallback === 'function') {\n          promise[\"catch\"](errorCallback);\n        }\n      }\n      function normalizeKey(key) {\n        // Cast the key to a string, as that's all we can set as a key.\n        if (typeof key !== 'string') {\n          console.warn(key + ' used as a key, but it is not a string.');\n          key = String(key);\n        }\n        return key;\n      }\n      function getCallback() {\n        if (arguments.length && typeof arguments[arguments.length - 1] === 'function') {\n          return arguments[arguments.length - 1];\n        }\n      }\n\n      // Some code originally from async_storage.js in\n      // [Gaia](https://github.com/mozilla-b2g/gaia).\n\n      var DETECT_BLOB_SUPPORT_STORE = 'local-forage-detect-blob-support';\n      var supportsBlobs = void 0;\n      var dbContexts = {};\n      var toString = Object.prototype.toString;\n\n      // Transaction Modes\n      var READ_ONLY = 'readonly';\n      var READ_WRITE = 'readwrite';\n\n      // Transform a binary string to an array buffer, because otherwise\n      // weird stuff happens when you try to work with the binary string directly.\n      // It is known.\n      // From http://stackoverflow.com/questions/14967647/ (continues on next line)\n      // encode-decode-image-with-base64-breaks-image (2013-04-21)\n      function _binStringToArrayBuffer(bin) {\n        var length = bin.length;\n        var buf = new ArrayBuffer(length);\n        var arr = new Uint8Array(buf);\n        for (var i = 0; i < length; i++) {\n          arr[i] = bin.charCodeAt(i);\n        }\n        return buf;\n      }\n\n      //\n      // Blobs are not supported in all versions of IndexedDB, notably\n      // Chrome <37 and Android <5. In those versions, storing a blob will throw.\n      //\n      // Various other blob bugs exist in Chrome v37-42 (inclusive).\n      // Detecting them is expensive and confusing to users, and Chrome 37-42\n      // is at very low usage worldwide, so we do a hacky userAgent check instead.\n      //\n      // content-type bug: https://code.google.com/p/chromium/issues/detail?id=408120\n      // 404 bug: https://code.google.com/p/chromium/issues/detail?id=447916\n      // FileReader bug: https://code.google.com/p/chromium/issues/detail?id=447836\n      //\n      // Code borrowed from PouchDB. See:\n      // https://github.com/pouchdb/pouchdb/blob/master/packages/node_modules/pouchdb-adapter-idb/src/blobSupport.js\n      //\n      function _checkBlobSupportWithoutCaching(idb) {\n        return new Promise$1(function (resolve) {\n          var txn = idb.transaction(DETECT_BLOB_SUPPORT_STORE, READ_WRITE);\n          var blob = createBlob(['']);\n          txn.objectStore(DETECT_BLOB_SUPPORT_STORE).put(blob, 'key');\n          txn.onabort = function (e) {\n            // If the transaction aborts now its due to not being able to\n            // write to the database, likely due to the disk being full\n            e.preventDefault();\n            e.stopPropagation();\n            resolve(false);\n          };\n          txn.oncomplete = function () {\n            var matchedChrome = navigator.userAgent.match(/Chrome\\/(\\d+)/);\n            var matchedEdge = navigator.userAgent.match(/Edge\\//);\n            // MS Edge pretends to be Chrome 42:\n            // https://msdn.microsoft.com/en-us/library/hh869301%28v=vs.85%29.aspx\n            resolve(matchedEdge || !matchedChrome || parseInt(matchedChrome[1], 10) >= 43);\n          };\n        })[\"catch\"](function () {\n          return false; // error, so assume unsupported\n        });\n      }\n      function _checkBlobSupport(idb) {\n        if (typeof supportsBlobs === 'boolean') {\n          return Promise$1.resolve(supportsBlobs);\n        }\n        return _checkBlobSupportWithoutCaching(idb).then(function (value) {\n          supportsBlobs = value;\n          return supportsBlobs;\n        });\n      }\n      function _deferReadiness(dbInfo) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Create a deferred object representing the current database operation.\n        var deferredOperation = {};\n        deferredOperation.promise = new Promise$1(function (resolve, reject) {\n          deferredOperation.resolve = resolve;\n          deferredOperation.reject = reject;\n        });\n\n        // Enqueue the deferred operation.\n        dbContext.deferredOperations.push(deferredOperation);\n\n        // Chain its promise to the database readiness.\n        if (!dbContext.dbReady) {\n          dbContext.dbReady = deferredOperation.promise;\n        } else {\n          dbContext.dbReady = dbContext.dbReady.then(function () {\n            return deferredOperation.promise;\n          });\n        }\n      }\n      function _advanceReadiness(dbInfo) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Dequeue a deferred operation.\n        var deferredOperation = dbContext.deferredOperations.pop();\n\n        // Resolve its promise (which is part of the database readiness\n        // chain of promises).\n        if (deferredOperation) {\n          deferredOperation.resolve();\n          return deferredOperation.promise;\n        }\n      }\n      function _rejectReadiness(dbInfo, err) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Dequeue a deferred operation.\n        var deferredOperation = dbContext.deferredOperations.pop();\n\n        // Reject its promise (which is part of the database readiness\n        // chain of promises).\n        if (deferredOperation) {\n          deferredOperation.reject(err);\n          return deferredOperation.promise;\n        }\n      }\n      function _getConnection(dbInfo, upgradeNeeded) {\n        return new Promise$1(function (resolve, reject) {\n          dbContexts[dbInfo.name] = dbContexts[dbInfo.name] || createDbContext();\n          if (dbInfo.db) {\n            if (upgradeNeeded) {\n              _deferReadiness(dbInfo);\n              dbInfo.db.close();\n            } else {\n              return resolve(dbInfo.db);\n            }\n          }\n          var dbArgs = [dbInfo.name];\n          if (upgradeNeeded) {\n            dbArgs.push(dbInfo.version);\n          }\n          var openreq = idb.open.apply(idb, dbArgs);\n          if (upgradeNeeded) {\n            openreq.onupgradeneeded = function (e) {\n              var db = openreq.result;\n              try {\n                db.createObjectStore(dbInfo.storeName);\n                if (e.oldVersion <= 1) {\n                  // Added when support for blob shims was added\n                  db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);\n                }\n              } catch (ex) {\n                if (ex.name === 'ConstraintError') {\n                  console.warn('The database \"' + dbInfo.name + '\"' + ' has been upgraded from version ' + e.oldVersion + ' to version ' + e.newVersion + ', but the storage \"' + dbInfo.storeName + '\" already exists.');\n                } else {\n                  throw ex;\n                }\n              }\n            };\n          }\n          openreq.onerror = function (e) {\n            e.preventDefault();\n            reject(openreq.error);\n          };\n          openreq.onsuccess = function () {\n            var db = openreq.result;\n            db.onversionchange = function (e) {\n              // Triggered when the database is modified (e.g. adding an objectStore) or\n              // deleted (even when initiated by other sessions in different tabs).\n              // Closing the connection here prevents those operations from being blocked.\n              // If the database is accessed again later by this instance, the connection\n              // will be reopened or the database recreated as needed.\n              e.target.close();\n            };\n            resolve(db);\n            _advanceReadiness(dbInfo);\n          };\n        });\n      }\n      function _getOriginalConnection(dbInfo) {\n        return _getConnection(dbInfo, false);\n      }\n      function _getUpgradedConnection(dbInfo) {\n        return _getConnection(dbInfo, true);\n      }\n      function _isUpgradeNeeded(dbInfo, defaultVersion) {\n        if (!dbInfo.db) {\n          return true;\n        }\n        var isNewStore = !dbInfo.db.objectStoreNames.contains(dbInfo.storeName);\n        var isDowngrade = dbInfo.version < dbInfo.db.version;\n        var isUpgrade = dbInfo.version > dbInfo.db.version;\n        if (isDowngrade) {\n          // If the version is not the default one\n          // then warn for impossible downgrade.\n          if (dbInfo.version !== defaultVersion) {\n            console.warn('The database \"' + dbInfo.name + '\"' + \" can't be downgraded from version \" + dbInfo.db.version + ' to version ' + dbInfo.version + '.');\n          }\n          // Align the versions to prevent errors.\n          dbInfo.version = dbInfo.db.version;\n        }\n        if (isUpgrade || isNewStore) {\n          // If the store is new then increment the version (if needed).\n          // This will trigger an \"upgradeneeded\" event which is required\n          // for creating a store.\n          if (isNewStore) {\n            var incVersion = dbInfo.db.version + 1;\n            if (incVersion > dbInfo.version) {\n              dbInfo.version = incVersion;\n            }\n          }\n          return true;\n        }\n        return false;\n      }\n\n      // encode a blob for indexeddb engines that don't support blobs\n      function _encodeBlob(blob) {\n        return new Promise$1(function (resolve, reject) {\n          var reader = new FileReader();\n          reader.onerror = reject;\n          reader.onloadend = function (e) {\n            var base64 = btoa(e.target.result || '');\n            resolve({\n              __local_forage_encoded_blob: true,\n              data: base64,\n              type: blob.type\n            });\n          };\n          reader.readAsBinaryString(blob);\n        });\n      }\n\n      // decode an encoded blob\n      function _decodeBlob(encodedBlob) {\n        var arrayBuff = _binStringToArrayBuffer(atob(encodedBlob.data));\n        return createBlob([arrayBuff], {\n          type: encodedBlob.type\n        });\n      }\n\n      // is this one of our fancy encoded blobs?\n      function _isEncodedBlob(value) {\n        return value && value.__local_forage_encoded_blob;\n      }\n\n      // Specialize the default `ready()` function by making it dependent\n      // on the current database operations. Thus, the driver will be actually\n      // ready when it's been initialized (default) *and* there are no pending\n      // operations on the database (initiated by some other instances).\n      function _fullyReady(callback) {\n        var self = this;\n        var promise = self._initReady().then(function () {\n          var dbContext = dbContexts[self._dbInfo.name];\n          if (dbContext && dbContext.dbReady) {\n            return dbContext.dbReady;\n          }\n        });\n        executeTwoCallbacks(promise, callback, callback);\n        return promise;\n      }\n\n      // Try to establish a new db connection to replace the\n      // current one which is broken (i.e. experiencing\n      // InvalidStateError while creating a transaction).\n      function _tryReconnect(dbInfo) {\n        _deferReadiness(dbInfo);\n        var dbContext = dbContexts[dbInfo.name];\n        var forages = dbContext.forages;\n        for (var i = 0; i < forages.length; i++) {\n          var forage = forages[i];\n          if (forage._dbInfo.db) {\n            forage._dbInfo.db.close();\n            forage._dbInfo.db = null;\n          }\n        }\n        dbInfo.db = null;\n        return _getOriginalConnection(dbInfo).then(function (db) {\n          dbInfo.db = db;\n          if (_isUpgradeNeeded(dbInfo)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n          }\n          return db;\n        }).then(function (db) {\n          // store the latest db reference\n          // in case the db was upgraded\n          dbInfo.db = dbContext.db = db;\n          for (var i = 0; i < forages.length; i++) {\n            forages[i]._dbInfo.db = db;\n          }\n        })[\"catch\"](function (err) {\n          _rejectReadiness(dbInfo, err);\n          throw err;\n        });\n      }\n\n      // FF doesn't like Promises (micro-tasks) and IDDB store operations,\n      // so we have to do it with callbacks\n      function createTransaction(dbInfo, mode, callback, retries) {\n        if (retries === undefined) {\n          retries = 1;\n        }\n        try {\n          var tx = dbInfo.db.transaction(dbInfo.storeName, mode);\n          callback(null, tx);\n        } catch (err) {\n          if (retries > 0 && (!dbInfo.db || err.name === 'InvalidStateError' || err.name === 'NotFoundError')) {\n            return Promise$1.resolve().then(function () {\n              if (!dbInfo.db || err.name === 'NotFoundError' && !dbInfo.db.objectStoreNames.contains(dbInfo.storeName) && dbInfo.version <= dbInfo.db.version) {\n                // increase the db version, to create the new ObjectStore\n                if (dbInfo.db) {\n                  dbInfo.version = dbInfo.db.version + 1;\n                }\n                // Reopen the database for upgrading.\n                return _getUpgradedConnection(dbInfo);\n              }\n            }).then(function () {\n              return _tryReconnect(dbInfo).then(function () {\n                createTransaction(dbInfo, mode, callback, retries - 1);\n              });\n            })[\"catch\"](callback);\n          }\n          callback(err);\n        }\n      }\n      function createDbContext() {\n        return {\n          // Running localForages sharing a database.\n          forages: [],\n          // Shared database.\n          db: null,\n          // Database readiness (promise).\n          dbReady: null,\n          // Deferred operations on the database.\n          deferredOperations: []\n        };\n      }\n\n      // Open the IndexedDB database (automatically creates one if one didn't\n      // previously exist), using any options set in the config.\n      function _initStorage(options) {\n        var self = this;\n        var dbInfo = {\n          db: null\n        };\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = options[i];\n          }\n        }\n\n        // Get the current context of the database;\n        var dbContext = dbContexts[dbInfo.name];\n\n        // ...or create a new context.\n        if (!dbContext) {\n          dbContext = createDbContext();\n          // Register the new context in the global container.\n          dbContexts[dbInfo.name] = dbContext;\n        }\n\n        // Register itself as a running localForage in the current context.\n        dbContext.forages.push(self);\n\n        // Replace the default `ready()` function with the specialized one.\n        if (!self._initReady) {\n          self._initReady = self.ready;\n          self.ready = _fullyReady;\n        }\n\n        // Create an array of initialization states of the related localForages.\n        var initPromises = [];\n        function ignoreErrors() {\n          // Don't handle errors here,\n          // just makes sure related localForages aren't pending.\n          return Promise$1.resolve();\n        }\n        for (var j = 0; j < dbContext.forages.length; j++) {\n          var forage = dbContext.forages[j];\n          if (forage !== self) {\n            // Don't wait for itself...\n            initPromises.push(forage._initReady()[\"catch\"](ignoreErrors));\n          }\n        }\n\n        // Take a snapshot of the related localForages.\n        var forages = dbContext.forages.slice(0);\n\n        // Initialize the connection process only when\n        // all the related localForages aren't pending.\n        return Promise$1.all(initPromises).then(function () {\n          dbInfo.db = dbContext.db;\n          // Get the connection or open a new one without upgrade.\n          return _getOriginalConnection(dbInfo);\n        }).then(function (db) {\n          dbInfo.db = db;\n          if (_isUpgradeNeeded(dbInfo, self._defaultConfig.version)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n          }\n          return db;\n        }).then(function (db) {\n          dbInfo.db = dbContext.db = db;\n          self._dbInfo = dbInfo;\n          // Share the final connection amongst related localForages.\n          for (var k = 0; k < forages.length; k++) {\n            var forage = forages[k];\n            if (forage !== self) {\n              // Self is already up-to-date.\n              forage._dbInfo.db = dbInfo.db;\n              forage._dbInfo.version = dbInfo.version;\n            }\n          }\n        });\n      }\n      function getItem(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.get(key);\n                req.onsuccess = function () {\n                  var value = req.result;\n                  if (value === undefined) {\n                    value = null;\n                  }\n                  if (_isEncodedBlob(value)) {\n                    value = _decodeBlob(value);\n                  }\n                  resolve(value);\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Iterate over all items stored in database.\n      function iterate(iterator, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.openCursor();\n                var iterationNumber = 1;\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (cursor) {\n                    var value = cursor.value;\n                    if (_isEncodedBlob(value)) {\n                      value = _decodeBlob(value);\n                    }\n                    var result = iterator(value, cursor.key, iterationNumber++);\n\n                    // when the iterator callback returns any\n                    // (non-`undefined`) value, then we stop\n                    // the iteration immediately\n                    if (result !== void 0) {\n                      resolve(result);\n                    } else {\n                      cursor[\"continue\"]();\n                    }\n                  } else {\n                    resolve();\n                  }\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function setItem(key, value, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          var dbInfo;\n          self.ready().then(function () {\n            dbInfo = self._dbInfo;\n            if (toString.call(value) === '[object Blob]') {\n              return _checkBlobSupport(dbInfo.db).then(function (blobSupport) {\n                if (blobSupport) {\n                  return value;\n                }\n                return _encodeBlob(value);\n              });\n            }\n            return value;\n          }).then(function (value) {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n\n                // The reason we don't _save_ null is because IE 10 does\n                // not support saving the `null` type in IndexedDB. How\n                // ironic, given the bug below!\n                // See: https://github.com/mozilla/localForage/issues/161\n                if (value === null) {\n                  value = undefined;\n                }\n                var req = store.put(value, key);\n                transaction.oncomplete = function () {\n                  // Cast to undefined so the value passed to\n                  // callback/promise is the same as what one would get out\n                  // of `getItem()` later. This leads to some weirdness\n                  // (setItem('foo', undefined) will return `null`), but\n                  // it's not my fault localStorage is our baseline and that\n                  // it's weird.\n                  if (value === undefined) {\n                    value = null;\n                  }\n                  resolve(value);\n                };\n                transaction.onabort = transaction.onerror = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function removeItem(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                // We use a Grunt task to make this safe for IE and some\n                // versions of Android (including those used by Cordova).\n                // Normally IE won't like `.delete()` and will insist on\n                // using `['delete']()`, but we have a build step that\n                // fixes this for us now.\n                var req = store[\"delete\"](key);\n                transaction.oncomplete = function () {\n                  resolve();\n                };\n                transaction.onerror = function () {\n                  reject(req.error);\n                };\n\n                // The request will be also be aborted if we've exceeded our storage\n                // space.\n                transaction.onabort = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function clear(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.clear();\n                transaction.oncomplete = function () {\n                  resolve();\n                };\n                transaction.onabort = transaction.onerror = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function length(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.count();\n                req.onsuccess = function () {\n                  resolve(req.result);\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function key(n, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          if (n < 0) {\n            resolve(null);\n            return;\n          }\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var advanced = false;\n                var req = store.openKeyCursor();\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (!cursor) {\n                    // this means there weren't enough keys\n                    resolve(null);\n                    return;\n                  }\n                  if (n === 0) {\n                    // We have the first key, return it if that's what they\n                    // wanted.\n                    resolve(cursor.key);\n                  } else {\n                    if (!advanced) {\n                      // Otherwise, ask the cursor to skip ahead n\n                      // records.\n                      advanced = true;\n                      cursor.advance(n);\n                    } else {\n                      // When we get here, we've got the nth key.\n                      resolve(cursor.key);\n                    }\n                  }\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.openKeyCursor();\n                var keys = [];\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (!cursor) {\n                    resolve(keys);\n                    return;\n                  }\n                  keys.push(cursor.key);\n                  cursor[\"continue\"]();\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function dropInstance(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        var currentConfig = this.config();\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          var isCurrentDb = options.name === currentConfig.name && self._dbInfo.db;\n          var dbPromise = isCurrentDb ? Promise$1.resolve(self._dbInfo.db) : _getOriginalConnection(options).then(function (db) {\n            var dbContext = dbContexts[options.name];\n            var forages = dbContext.forages;\n            dbContext.db = db;\n            for (var i = 0; i < forages.length; i++) {\n              forages[i]._dbInfo.db = db;\n            }\n            return db;\n          });\n          if (!options.storeName) {\n            promise = dbPromise.then(function (db) {\n              _deferReadiness(options);\n              var dbContext = dbContexts[options.name];\n              var forages = dbContext.forages;\n              db.close();\n              for (var i = 0; i < forages.length; i++) {\n                var forage = forages[i];\n                forage._dbInfo.db = null;\n              }\n              var dropDBPromise = new Promise$1(function (resolve, reject) {\n                var req = idb.deleteDatabase(options.name);\n                req.onerror = function () {\n                  var db = req.result;\n                  if (db) {\n                    db.close();\n                  }\n                  reject(req.error);\n                };\n                req.onblocked = function () {\n                  // Closing all open connections in onversionchange handler should prevent this situation, but if\n                  // we do get here, it just means the request remains pending - eventually it will succeed or error\n                  console.warn('dropInstance blocked for database \"' + options.name + '\" until all open connections are closed');\n                };\n                req.onsuccess = function () {\n                  var db = req.result;\n                  if (db) {\n                    db.close();\n                  }\n                  resolve(db);\n                };\n              });\n              return dropDBPromise.then(function (db) {\n                dbContext.db = db;\n                for (var i = 0; i < forages.length; i++) {\n                  var _forage = forages[i];\n                  _advanceReadiness(_forage._dbInfo);\n                }\n              })[\"catch\"](function (err) {\n                (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                throw err;\n              });\n            });\n          } else {\n            promise = dbPromise.then(function (db) {\n              if (!db.objectStoreNames.contains(options.storeName)) {\n                return;\n              }\n              var newVersion = db.version + 1;\n              _deferReadiness(options);\n              var dbContext = dbContexts[options.name];\n              var forages = dbContext.forages;\n              db.close();\n              for (var i = 0; i < forages.length; i++) {\n                var forage = forages[i];\n                forage._dbInfo.db = null;\n                forage._dbInfo.version = newVersion;\n              }\n              var dropObjectPromise = new Promise$1(function (resolve, reject) {\n                var req = idb.open(options.name, newVersion);\n                req.onerror = function (err) {\n                  var db = req.result;\n                  db.close();\n                  reject(err);\n                };\n                req.onupgradeneeded = function () {\n                  var db = req.result;\n                  db.deleteObjectStore(options.storeName);\n                };\n                req.onsuccess = function () {\n                  var db = req.result;\n                  db.close();\n                  resolve(db);\n                };\n              });\n              return dropObjectPromise.then(function (db) {\n                dbContext.db = db;\n                for (var j = 0; j < forages.length; j++) {\n                  var _forage2 = forages[j];\n                  _forage2._dbInfo.db = db;\n                  _advanceReadiness(_forage2._dbInfo);\n                }\n              })[\"catch\"](function (err) {\n                (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                throw err;\n              });\n            });\n          }\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var asyncStorage = {\n        _driver: 'asyncStorage',\n        _initStorage: _initStorage,\n        _support: isIndexedDBValid(),\n        iterate: iterate,\n        getItem: getItem,\n        setItem: setItem,\n        removeItem: removeItem,\n        clear: clear,\n        length: length,\n        key: key,\n        keys: keys,\n        dropInstance: dropInstance\n      };\n      function isWebSQLValid() {\n        return typeof openDatabase === 'function';\n      }\n\n      // Sadly, the best way to save binary data in WebSQL/localStorage is serializing\n      // it to Base64, so this is how we store it to prevent very strange errors with less\n      // verbose ways of binary <-> string data storage.\n      var BASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n      var BLOB_TYPE_PREFIX = '~~local_forage_type~';\n      var BLOB_TYPE_PREFIX_REGEX = /^~~local_forage_type~([^~]+)~/;\n      var SERIALIZED_MARKER = '__lfsc__:';\n      var SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER.length;\n\n      // OMG the serializations!\n      var TYPE_ARRAYBUFFER = 'arbf';\n      var TYPE_BLOB = 'blob';\n      var TYPE_INT8ARRAY = 'si08';\n      var TYPE_UINT8ARRAY = 'ui08';\n      var TYPE_UINT8CLAMPEDARRAY = 'uic8';\n      var TYPE_INT16ARRAY = 'si16';\n      var TYPE_INT32ARRAY = 'si32';\n      var TYPE_UINT16ARRAY = 'ur16';\n      var TYPE_UINT32ARRAY = 'ui32';\n      var TYPE_FLOAT32ARRAY = 'fl32';\n      var TYPE_FLOAT64ARRAY = 'fl64';\n      var TYPE_SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER_LENGTH + TYPE_ARRAYBUFFER.length;\n      var toString$1 = Object.prototype.toString;\n      function stringToBuffer(serializedString) {\n        // Fill the string into a ArrayBuffer.\n        var bufferLength = serializedString.length * 0.75;\n        var len = serializedString.length;\n        var i;\n        var p = 0;\n        var encoded1, encoded2, encoded3, encoded4;\n        if (serializedString[serializedString.length - 1] === '=') {\n          bufferLength--;\n          if (serializedString[serializedString.length - 2] === '=') {\n            bufferLength--;\n          }\n        }\n        var buffer = new ArrayBuffer(bufferLength);\n        var bytes = new Uint8Array(buffer);\n        for (i = 0; i < len; i += 4) {\n          encoded1 = BASE_CHARS.indexOf(serializedString[i]);\n          encoded2 = BASE_CHARS.indexOf(serializedString[i + 1]);\n          encoded3 = BASE_CHARS.indexOf(serializedString[i + 2]);\n          encoded4 = BASE_CHARS.indexOf(serializedString[i + 3]);\n\n          /*jslint bitwise: true */\n          bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n          bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n          bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n        }\n        return buffer;\n      }\n\n      // Converts a buffer to a string to store, serialized, in the backend\n      // storage library.\n      function bufferToString(buffer) {\n        // base64-arraybuffer\n        var bytes = new Uint8Array(buffer);\n        var base64String = '';\n        var i;\n        for (i = 0; i < bytes.length; i += 3) {\n          /*jslint bitwise: true */\n          base64String += BASE_CHARS[bytes[i] >> 2];\n          base64String += BASE_CHARS[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n          base64String += BASE_CHARS[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n          base64String += BASE_CHARS[bytes[i + 2] & 63];\n        }\n        if (bytes.length % 3 === 2) {\n          base64String = base64String.substring(0, base64String.length - 1) + '=';\n        } else if (bytes.length % 3 === 1) {\n          base64String = base64String.substring(0, base64String.length - 2) + '==';\n        }\n        return base64String;\n      }\n\n      // Serialize a value, afterwards executing a callback (which usually\n      // instructs the `setItem()` callback/promise to be executed). This is how\n      // we store binary data with localStorage.\n      function serialize(value, callback) {\n        var valueType = '';\n        if (value) {\n          valueType = toString$1.call(value);\n        }\n\n        // Cannot use `value instanceof ArrayBuffer` or such here, as these\n        // checks fail when running the tests using casper.js...\n        //\n        // TODO: See why those tests fail and use a better solution.\n        if (value && (valueType === '[object ArrayBuffer]' || value.buffer && toString$1.call(value.buffer) === '[object ArrayBuffer]')) {\n          // Convert binary arrays to a string and prefix the string with\n          // a special marker.\n          var buffer;\n          var marker = SERIALIZED_MARKER;\n          if (value instanceof ArrayBuffer) {\n            buffer = value;\n            marker += TYPE_ARRAYBUFFER;\n          } else {\n            buffer = value.buffer;\n            if (valueType === '[object Int8Array]') {\n              marker += TYPE_INT8ARRAY;\n            } else if (valueType === '[object Uint8Array]') {\n              marker += TYPE_UINT8ARRAY;\n            } else if (valueType === '[object Uint8ClampedArray]') {\n              marker += TYPE_UINT8CLAMPEDARRAY;\n            } else if (valueType === '[object Int16Array]') {\n              marker += TYPE_INT16ARRAY;\n            } else if (valueType === '[object Uint16Array]') {\n              marker += TYPE_UINT16ARRAY;\n            } else if (valueType === '[object Int32Array]') {\n              marker += TYPE_INT32ARRAY;\n            } else if (valueType === '[object Uint32Array]') {\n              marker += TYPE_UINT32ARRAY;\n            } else if (valueType === '[object Float32Array]') {\n              marker += TYPE_FLOAT32ARRAY;\n            } else if (valueType === '[object Float64Array]') {\n              marker += TYPE_FLOAT64ARRAY;\n            } else {\n              callback(new Error('Failed to get type for BinaryArray'));\n            }\n          }\n          callback(marker + bufferToString(buffer));\n        } else if (valueType === '[object Blob]') {\n          // Conver the blob to a binaryArray and then to a string.\n          var fileReader = new FileReader();\n          fileReader.onload = function () {\n            // Backwards-compatible prefix for the blob type.\n            var str = BLOB_TYPE_PREFIX + value.type + '~' + bufferToString(this.result);\n            callback(SERIALIZED_MARKER + TYPE_BLOB + str);\n          };\n          fileReader.readAsArrayBuffer(value);\n        } else {\n          try {\n            callback(JSON.stringify(value));\n          } catch (e) {\n            console.error(\"Couldn't convert value into a JSON string: \", value);\n            callback(null, e);\n          }\n        }\n      }\n\n      // Deserialize data we've inserted into a value column/field. We place\n      // special markers into our strings to mark them as encoded; this isn't\n      // as nice as a meta field, but it's the only sane thing we can do whilst\n      // keeping localStorage support intact.\n      //\n      // Oftentimes this will just deserialize JSON content, but if we have a\n      // special marker (SERIALIZED_MARKER, defined above), we will extract\n      // some kind of arraybuffer/binary data/typed array out of the string.\n      function deserialize(value) {\n        // If we haven't marked this string as being specially serialized (i.e.\n        // something other than serialized JSON), we can just return it and be\n        // done with it.\n        if (value.substring(0, SERIALIZED_MARKER_LENGTH) !== SERIALIZED_MARKER) {\n          return JSON.parse(value);\n        }\n\n        // The following code deals with deserializing some kind of Blob or\n        // TypedArray. First we separate out the type of data we're dealing\n        // with from the data itself.\n        var serializedString = value.substring(TYPE_SERIALIZED_MARKER_LENGTH);\n        var type = value.substring(SERIALIZED_MARKER_LENGTH, TYPE_SERIALIZED_MARKER_LENGTH);\n        var blobType;\n        // Backwards-compatible blob type serialization strategy.\n        // DBs created with older versions of localForage will simply not have the blob type.\n        if (type === TYPE_BLOB && BLOB_TYPE_PREFIX_REGEX.test(serializedString)) {\n          var matcher = serializedString.match(BLOB_TYPE_PREFIX_REGEX);\n          blobType = matcher[1];\n          serializedString = serializedString.substring(matcher[0].length);\n        }\n        var buffer = stringToBuffer(serializedString);\n\n        // Return the right type based on the code/type set during\n        // serialization.\n        switch (type) {\n          case TYPE_ARRAYBUFFER:\n            return buffer;\n          case TYPE_BLOB:\n            return createBlob([buffer], {\n              type: blobType\n            });\n          case TYPE_INT8ARRAY:\n            return new Int8Array(buffer);\n          case TYPE_UINT8ARRAY:\n            return new Uint8Array(buffer);\n          case TYPE_UINT8CLAMPEDARRAY:\n            return new Uint8ClampedArray(buffer);\n          case TYPE_INT16ARRAY:\n            return new Int16Array(buffer);\n          case TYPE_UINT16ARRAY:\n            return new Uint16Array(buffer);\n          case TYPE_INT32ARRAY:\n            return new Int32Array(buffer);\n          case TYPE_UINT32ARRAY:\n            return new Uint32Array(buffer);\n          case TYPE_FLOAT32ARRAY:\n            return new Float32Array(buffer);\n          case TYPE_FLOAT64ARRAY:\n            return new Float64Array(buffer);\n          default:\n            throw new Error('Unkown type: ' + type);\n        }\n      }\n      var localforageSerializer = {\n        serialize: serialize,\n        deserialize: deserialize,\n        stringToBuffer: stringToBuffer,\n        bufferToString: bufferToString\n      };\n\n      /*\n       * Includes code from:\n       *\n       * base64-arraybuffer\n       * https://github.com/niklasvh/base64-arraybuffer\n       *\n       * Copyright (c) 2012 Niklas von Hertzen\n       * Licensed under the MIT license.\n       */\n\n      function createDbTable(t, dbInfo, callback, errorCallback) {\n        t.executeSql('CREATE TABLE IF NOT EXISTS ' + dbInfo.storeName + ' ' + '(id INTEGER PRIMARY KEY, key unique, value)', [], callback, errorCallback);\n      }\n\n      // Open the WebSQL database (automatically creates one if one didn't\n      // previously exist), using any options set in the config.\n      function _initStorage$1(options) {\n        var self = this;\n        var dbInfo = {\n          db: null\n        };\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = typeof options[i] !== 'string' ? options[i].toString() : options[i];\n          }\n        }\n        var dbInfoPromise = new Promise$1(function (resolve, reject) {\n          // Open the database; the openDatabase API will automatically\n          // create it for us if it doesn't exist.\n          try {\n            dbInfo.db = openDatabase(dbInfo.name, String(dbInfo.version), dbInfo.description, dbInfo.size);\n          } catch (e) {\n            return reject(e);\n          }\n\n          // Create our key/value table if it doesn't exist.\n          dbInfo.db.transaction(function (t) {\n            createDbTable(t, dbInfo, function () {\n              self._dbInfo = dbInfo;\n              resolve();\n            }, function (t, error) {\n              reject(error);\n            });\n          }, reject);\n        });\n        dbInfo.serializer = localforageSerializer;\n        return dbInfoPromise;\n      }\n      function tryExecuteSql(t, dbInfo, sqlStatement, args, callback, errorCallback) {\n        t.executeSql(sqlStatement, args, callback, function (t, error) {\n          if (error.code === error.SYNTAX_ERR) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name = ?\", [dbInfo.storeName], function (t, results) {\n              if (!results.rows.length) {\n                // if the table is missing (was deleted)\n                // re-create it table and retry\n                createDbTable(t, dbInfo, function () {\n                  t.executeSql(sqlStatement, args, callback, errorCallback);\n                }, errorCallback);\n              } else {\n                errorCallback(t, error);\n              }\n            }, errorCallback);\n          } else {\n            errorCallback(t, error);\n          }\n        }, errorCallback);\n      }\n      function getItem$1(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName + ' WHERE key = ? LIMIT 1', [key], function (t, results) {\n                var result = results.rows.length ? results.rows.item(0).value : null;\n\n                // Check to see if this is serialized content we need to\n                // unpack.\n                if (result) {\n                  result = dbInfo.serializer.deserialize(result);\n                }\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function iterate$1(iterator, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName, [], function (t, results) {\n                var rows = results.rows;\n                var length = rows.length;\n                for (var i = 0; i < length; i++) {\n                  var item = rows.item(i);\n                  var result = item.value;\n\n                  // Check to see if this is serialized content\n                  // we need to unpack.\n                  if (result) {\n                    result = dbInfo.serializer.deserialize(result);\n                  }\n                  result = iterator(result, item.key, i + 1);\n\n                  // void(0) prevents problems with redefinition\n                  // of `undefined`.\n                  if (result !== void 0) {\n                    resolve(result);\n                    return;\n                  }\n                }\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function _setItem(key, value, callback, retriesLeft) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            // The localStorage API doesn't return undefined values in an\n            // \"expected\" way, so undefined is always cast to null in all\n            // drivers. See: https://github.com/mozilla/localForage/pull/42\n            if (value === undefined) {\n              value = null;\n            }\n\n            // Save the original value to pass to the callback.\n            var originalValue = value;\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n              if (error) {\n                reject(error);\n              } else {\n                dbInfo.db.transaction(function (t) {\n                  tryExecuteSql(t, dbInfo, 'INSERT OR REPLACE INTO ' + dbInfo.storeName + ' ' + '(key, value) VALUES (?, ?)', [key, value], function () {\n                    resolve(originalValue);\n                  }, function (t, error) {\n                    reject(error);\n                  });\n                }, function (sqlError) {\n                  // The transaction failed; check\n                  // to see if it's a quota error.\n                  if (sqlError.code === sqlError.QUOTA_ERR) {\n                    // We reject the callback outright for now, but\n                    // it's worth trying to re-run the transaction.\n                    // Even if the user accepts the prompt to use\n                    // more storage on Safari, this error will\n                    // be called.\n                    //\n                    // Try to re-run the transaction.\n                    if (retriesLeft > 0) {\n                      resolve(_setItem.apply(self, [key, originalValue, callback, retriesLeft - 1]));\n                      return;\n                    }\n                    reject(sqlError);\n                  }\n                });\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function setItem$1(key, value, callback) {\n        return _setItem.apply(this, [key, value, callback, 1]);\n      }\n      function removeItem$1(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName + ' WHERE key = ?', [key], function () {\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Deletes every item in the table.\n      // TODO: Find out if this resets the AUTO_INCREMENT number.\n      function clear$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName, [], function () {\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Does a simple `COUNT(key)` to get the number of items stored in\n      // localForage.\n      function length$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              // Ahhh, SQL makes this one soooooo easy.\n              tryExecuteSql(t, dbInfo, 'SELECT COUNT(key) as c FROM ' + dbInfo.storeName, [], function (t, results) {\n                var result = results.rows.item(0).c;\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Return the key located at key index X; essentially gets the key from a\n      // `WHERE id = ?`. This is the most efficient way I can think to implement\n      // this rarely-used (in my experience) part of the API, but it can seem\n      // inconsistent, because we do `INSERT OR REPLACE INTO` on `setItem()`, so\n      // the ID of each key will change every time it's updated. Perhaps a stored\n      // procedure for the `setItem()` SQL would solve this problem?\n      // TODO: Don't change ID on `setItem()`.\n      function key$1(n, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName + ' WHERE id = ? LIMIT 1', [n + 1], function (t, results) {\n                var result = results.rows.length ? results.rows.item(0).key : null;\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName, [], function (t, results) {\n                var keys = [];\n                for (var i = 0; i < results.rows.length; i++) {\n                  keys.push(results.rows.item(i).key);\n                }\n                resolve(keys);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // https://www.w3.org/TR/webdatabase/#databases\n      // > There is no way to enumerate or delete the databases available for an origin from this API.\n      function getAllStoreNames(db) {\n        return new Promise$1(function (resolve, reject) {\n          db.transaction(function (t) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'\", [], function (t, results) {\n              var storeNames = [];\n              for (var i = 0; i < results.rows.length; i++) {\n                storeNames.push(results.rows.item(i).name);\n              }\n              resolve({\n                db: db,\n                storeNames: storeNames\n              });\n            }, function (t, error) {\n              reject(error);\n            });\n          }, function (sqlError) {\n            reject(sqlError);\n          });\n        });\n      }\n      function dropInstance$1(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        var currentConfig = this.config();\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          promise = new Promise$1(function (resolve) {\n            var db;\n            if (options.name === currentConfig.name) {\n              // use the db reference of the current instance\n              db = self._dbInfo.db;\n            } else {\n              db = openDatabase(options.name, '', '', 0);\n            }\n            if (!options.storeName) {\n              // drop all database tables\n              resolve(getAllStoreNames(db));\n            } else {\n              resolve({\n                db: db,\n                storeNames: [options.storeName]\n              });\n            }\n          }).then(function (operationInfo) {\n            return new Promise$1(function (resolve, reject) {\n              operationInfo.db.transaction(function (t) {\n                function dropTable(storeName) {\n                  return new Promise$1(function (resolve, reject) {\n                    t.executeSql('DROP TABLE IF EXISTS ' + storeName, [], function () {\n                      resolve();\n                    }, function (t, error) {\n                      reject(error);\n                    });\n                  });\n                }\n                var operations = [];\n                for (var i = 0, len = operationInfo.storeNames.length; i < len; i++) {\n                  operations.push(dropTable(operationInfo.storeNames[i]));\n                }\n                Promise$1.all(operations).then(function () {\n                  resolve();\n                })[\"catch\"](function (e) {\n                  reject(e);\n                });\n              }, function (sqlError) {\n                reject(sqlError);\n              });\n            });\n          });\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var webSQLStorage = {\n        _driver: 'webSQLStorage',\n        _initStorage: _initStorage$1,\n        _support: isWebSQLValid(),\n        iterate: iterate$1,\n        getItem: getItem$1,\n        setItem: setItem$1,\n        removeItem: removeItem$1,\n        clear: clear$1,\n        length: length$1,\n        key: key$1,\n        keys: keys$1,\n        dropInstance: dropInstance$1\n      };\n      function isLocalStorageValid() {\n        try {\n          return typeof localStorage !== 'undefined' && 'setItem' in localStorage &&\n          // in IE8 typeof localStorage.setItem === 'object'\n          !!localStorage.setItem;\n        } catch (e) {\n          return false;\n        }\n      }\n      function _getKeyPrefix(options, defaultConfig) {\n        var keyPrefix = options.name + '/';\n        if (options.storeName !== defaultConfig.storeName) {\n          keyPrefix += options.storeName + '/';\n        }\n        return keyPrefix;\n      }\n\n      // Check if localStorage throws when saving an item\n      function checkIfLocalStorageThrows() {\n        var localStorageTestKey = '_localforage_support_test';\n        try {\n          localStorage.setItem(localStorageTestKey, true);\n          localStorage.removeItem(localStorageTestKey);\n          return false;\n        } catch (e) {\n          return true;\n        }\n      }\n\n      // Check if localStorage is usable and allows to save an item\n      // This method checks if localStorage is usable in Safari Private Browsing\n      // mode, or in any other case where the available quota for localStorage\n      // is 0 and there wasn't any saved items yet.\n      function _isLocalStorageUsable() {\n        return !checkIfLocalStorageThrows() || localStorage.length > 0;\n      }\n\n      // Config the localStorage backend, using options set in the config.\n      function _initStorage$2(options) {\n        var self = this;\n        var dbInfo = {};\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = options[i];\n          }\n        }\n        dbInfo.keyPrefix = _getKeyPrefix(options, self._defaultConfig);\n        if (!_isLocalStorageUsable()) {\n          return Promise$1.reject();\n        }\n        self._dbInfo = dbInfo;\n        dbInfo.serializer = localforageSerializer;\n        return Promise$1.resolve();\n      }\n\n      // Remove all keys from the datastore, effectively destroying all data in\n      // the app's key/value store!\n      function clear$2(callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var keyPrefix = self._dbInfo.keyPrefix;\n          for (var i = localStorage.length - 1; i >= 0; i--) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) === 0) {\n              localStorage.removeItem(key);\n            }\n          }\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Retrieve an item from the store. Unlike the original async_storage\n      // library in Gaia, we don't modify return values at all. If a key's value\n      // is `undefined`, we pass that value to the callback function.\n      function getItem$2(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var result = localStorage.getItem(dbInfo.keyPrefix + key);\n\n          // If a result was found, parse it from the serialized\n          // string into a JS object. If result isn't truthy, the key\n          // is likely undefined and we'll pass it straight to the\n          // callback.\n          if (result) {\n            result = dbInfo.serializer.deserialize(result);\n          }\n          return result;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Iterate over all items in the store.\n      function iterate$2(iterator, callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var keyPrefix = dbInfo.keyPrefix;\n          var keyPrefixLength = keyPrefix.length;\n          var length = localStorage.length;\n\n          // We use a dedicated iterator instead of the `i` variable below\n          // so other keys we fetch in localStorage aren't counted in\n          // the `iterationNumber` argument passed to the `iterate()`\n          // callback.\n          //\n          // See: github.com/mozilla/localForage/pull/435#discussion_r38061530\n          var iterationNumber = 1;\n          for (var i = 0; i < length; i++) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) !== 0) {\n              continue;\n            }\n            var value = localStorage.getItem(key);\n\n            // If a result was found, parse it from the serialized\n            // string into a JS object. If result isn't truthy, the\n            // key is likely undefined and we'll pass it straight\n            // to the iterator.\n            if (value) {\n              value = dbInfo.serializer.deserialize(value);\n            }\n            value = iterator(value, key.substring(keyPrefixLength), iterationNumber++);\n            if (value !== void 0) {\n              return value;\n            }\n          }\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Same as localStorage's key() method, except takes a callback.\n      function key$2(n, callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var result;\n          try {\n            result = localStorage.key(n);\n          } catch (error) {\n            result = null;\n          }\n\n          // Remove the prefix from the key, if a key is found.\n          if (result) {\n            result = result.substring(dbInfo.keyPrefix.length);\n          }\n          return result;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys$2(callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var length = localStorage.length;\n          var keys = [];\n          for (var i = 0; i < length; i++) {\n            var itemKey = localStorage.key(i);\n            if (itemKey.indexOf(dbInfo.keyPrefix) === 0) {\n              keys.push(itemKey.substring(dbInfo.keyPrefix.length));\n            }\n          }\n          return keys;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Supply the number of keys in the datastore to the callback function.\n      function length$2(callback) {\n        var self = this;\n        var promise = self.keys().then(function (keys) {\n          return keys.length;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Remove an item from the store, nice and simple.\n      function removeItem$2(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          localStorage.removeItem(dbInfo.keyPrefix + key);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Set a key's value and run an optional callback once the value is set.\n      // Unlike Gaia's implementation, the callback function is passed the value,\n      // in case you want to operate on that value only after you're sure it\n      // saved, or something like that.\n      function setItem$2(key, value, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          // Convert undefined values to null.\n          // https://github.com/mozilla/localForage/pull/42\n          if (value === undefined) {\n            value = null;\n          }\n\n          // Save the original value to pass to the callback.\n          var originalValue = value;\n          return new Promise$1(function (resolve, reject) {\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n              if (error) {\n                reject(error);\n              } else {\n                try {\n                  localStorage.setItem(dbInfo.keyPrefix + key, value);\n                  resolve(originalValue);\n                } catch (e) {\n                  // localStorage capacity exceeded.\n                  // TODO: Make this a specific error/event.\n                  if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {\n                    reject(e);\n                  }\n                  reject(e);\n                }\n              }\n            });\n          });\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function dropInstance$2(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          var currentConfig = this.config();\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          promise = new Promise$1(function (resolve) {\n            if (!options.storeName) {\n              resolve(options.name + '/');\n            } else {\n              resolve(_getKeyPrefix(options, self._defaultConfig));\n            }\n          }).then(function (keyPrefix) {\n            for (var i = localStorage.length - 1; i >= 0; i--) {\n              var key = localStorage.key(i);\n              if (key.indexOf(keyPrefix) === 0) {\n                localStorage.removeItem(key);\n              }\n            }\n          });\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var localStorageWrapper = {\n        _driver: 'localStorageWrapper',\n        _initStorage: _initStorage$2,\n        _support: isLocalStorageValid(),\n        iterate: iterate$2,\n        getItem: getItem$2,\n        setItem: setItem$2,\n        removeItem: removeItem$2,\n        clear: clear$2,\n        length: length$2,\n        key: key$2,\n        keys: keys$2,\n        dropInstance: dropInstance$2\n      };\n      var sameValue = function sameValue(x, y) {\n        return x === y || typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y);\n      };\n      var includes = function includes(array, searchElement) {\n        var len = array.length;\n        var i = 0;\n        while (i < len) {\n          if (sameValue(array[i], searchElement)) {\n            return true;\n          }\n          i++;\n        }\n        return false;\n      };\n      var isArray = Array.isArray || function (arg) {\n        return Object.prototype.toString.call(arg) === '[object Array]';\n      };\n\n      // Drivers are stored here when `defineDriver()` is called.\n      // They are shared across all instances of localForage.\n      var DefinedDrivers = {};\n      var DriverSupport = {};\n      var DefaultDrivers = {\n        INDEXEDDB: asyncStorage,\n        WEBSQL: webSQLStorage,\n        LOCALSTORAGE: localStorageWrapper\n      };\n      var DefaultDriverOrder = [DefaultDrivers.INDEXEDDB._driver, DefaultDrivers.WEBSQL._driver, DefaultDrivers.LOCALSTORAGE._driver];\n      var OptionalDriverMethods = ['dropInstance'];\n      var LibraryMethods = ['clear', 'getItem', 'iterate', 'key', 'keys', 'length', 'removeItem', 'setItem'].concat(OptionalDriverMethods);\n      var DefaultConfig = {\n        description: '',\n        driver: DefaultDriverOrder.slice(),\n        name: 'localforage',\n        // Default DB size is _JUST UNDER_ 5MB, as it's the highest size\n        // we can use without a prompt.\n        size: 4980736,\n        storeName: 'keyvaluepairs',\n        version: 1.0\n      };\n      function callWhenReady(localForageInstance, libraryMethod) {\n        localForageInstance[libraryMethod] = function () {\n          var _args = arguments;\n          return localForageInstance.ready().then(function () {\n            return localForageInstance[libraryMethod].apply(localForageInstance, _args);\n          });\n        };\n      }\n      function extend() {\n        for (var i = 1; i < arguments.length; i++) {\n          var arg = arguments[i];\n          if (arg) {\n            for (var _key in arg) {\n              if (arg.hasOwnProperty(_key)) {\n                if (isArray(arg[_key])) {\n                  arguments[0][_key] = arg[_key].slice();\n                } else {\n                  arguments[0][_key] = arg[_key];\n                }\n              }\n            }\n          }\n        }\n        return arguments[0];\n      }\n      var LocalForage = function () {\n        function LocalForage(options) {\n          _classCallCheck(this, LocalForage);\n          for (var driverTypeKey in DefaultDrivers) {\n            if (DefaultDrivers.hasOwnProperty(driverTypeKey)) {\n              var driver = DefaultDrivers[driverTypeKey];\n              var driverName = driver._driver;\n              this[driverTypeKey] = driverName;\n              if (!DefinedDrivers[driverName]) {\n                // we don't need to wait for the promise,\n                // since the default drivers can be defined\n                // in a blocking manner\n                this.defineDriver(driver);\n              }\n            }\n          }\n          this._defaultConfig = extend({}, DefaultConfig);\n          this._config = extend({}, this._defaultConfig, options);\n          this._driverSet = null;\n          this._initDriver = null;\n          this._ready = false;\n          this._dbInfo = null;\n          this._wrapLibraryMethodsWithReady();\n          this.setDriver(this._config.driver)[\"catch\"](function () {});\n        }\n\n        // Set any config values for localForage; can be called anytime before\n        // the first API call (e.g. `getItem`, `setItem`).\n        // We loop through options so we don't overwrite existing config\n        // values.\n\n        LocalForage.prototype.config = function config(options) {\n          // If the options argument is an object, we use it to set values.\n          // Otherwise, we return either a specified config value or all\n          // config values.\n          if ((typeof options === 'undefined' ? 'undefined' : _typeof(options)) === 'object') {\n            // If localforage is ready and fully initialized, we can't set\n            // any new configuration values. Instead, we return an error.\n            if (this._ready) {\n              return new Error(\"Can't call config() after localforage \" + 'has been used.');\n            }\n            for (var i in options) {\n              if (i === 'storeName') {\n                options[i] = options[i].replace(/\\W/g, '_');\n              }\n              if (i === 'version' && typeof options[i] !== 'number') {\n                return new Error('Database version must be a number.');\n              }\n              this._config[i] = options[i];\n            }\n\n            // after all config options are set and\n            // the driver option is used, try setting it\n            if ('driver' in options && options.driver) {\n              return this.setDriver(this._config.driver);\n            }\n            return true;\n          } else if (typeof options === 'string') {\n            return this._config[options];\n          } else {\n            return this._config;\n          }\n        };\n\n        // Used to define a custom driver, shared across all instances of\n        // localForage.\n\n        LocalForage.prototype.defineDriver = function defineDriver(driverObject, callback, errorCallback) {\n          var promise = new Promise$1(function (resolve, reject) {\n            try {\n              var driverName = driverObject._driver;\n              var complianceError = new Error('Custom driver not compliant; see ' + 'https://mozilla.github.io/localForage/#definedriver');\n\n              // A driver name should be defined and not overlap with the\n              // library-defined, default drivers.\n              if (!driverObject._driver) {\n                reject(complianceError);\n                return;\n              }\n              var driverMethods = LibraryMethods.concat('_initStorage');\n              for (var i = 0, len = driverMethods.length; i < len; i++) {\n                var driverMethodName = driverMethods[i];\n\n                // when the property is there,\n                // it should be a method even when optional\n                var isRequired = !includes(OptionalDriverMethods, driverMethodName);\n                if ((isRequired || driverObject[driverMethodName]) && typeof driverObject[driverMethodName] !== 'function') {\n                  reject(complianceError);\n                  return;\n                }\n              }\n              var configureMissingMethods = function configureMissingMethods() {\n                var methodNotImplementedFactory = function methodNotImplementedFactory(methodName) {\n                  return function () {\n                    var error = new Error('Method ' + methodName + ' is not implemented by the current driver');\n                    var promise = Promise$1.reject(error);\n                    executeCallback(promise, arguments[arguments.length - 1]);\n                    return promise;\n                  };\n                };\n                for (var _i = 0, _len = OptionalDriverMethods.length; _i < _len; _i++) {\n                  var optionalDriverMethod = OptionalDriverMethods[_i];\n                  if (!driverObject[optionalDriverMethod]) {\n                    driverObject[optionalDriverMethod] = methodNotImplementedFactory(optionalDriverMethod);\n                  }\n                }\n              };\n              configureMissingMethods();\n              var setDriverSupport = function setDriverSupport(support) {\n                if (DefinedDrivers[driverName]) {\n                  console.info('Redefining LocalForage driver: ' + driverName);\n                }\n                DefinedDrivers[driverName] = driverObject;\n                DriverSupport[driverName] = support;\n                // don't use a then, so that we can define\n                // drivers that have simple _support methods\n                // in a blocking manner\n                resolve();\n              };\n              if ('_support' in driverObject) {\n                if (driverObject._support && typeof driverObject._support === 'function') {\n                  driverObject._support().then(setDriverSupport, reject);\n                } else {\n                  setDriverSupport(!!driverObject._support);\n                }\n              } else {\n                setDriverSupport(true);\n              }\n            } catch (e) {\n              reject(e);\n            }\n          });\n          executeTwoCallbacks(promise, callback, errorCallback);\n          return promise;\n        };\n        LocalForage.prototype.driver = function driver() {\n          return this._driver || null;\n        };\n        LocalForage.prototype.getDriver = function getDriver(driverName, callback, errorCallback) {\n          var getDriverPromise = DefinedDrivers[driverName] ? Promise$1.resolve(DefinedDrivers[driverName]) : Promise$1.reject(new Error('Driver not found.'));\n          executeTwoCallbacks(getDriverPromise, callback, errorCallback);\n          return getDriverPromise;\n        };\n        LocalForage.prototype.getSerializer = function getSerializer(callback) {\n          var serializerPromise = Promise$1.resolve(localforageSerializer);\n          executeTwoCallbacks(serializerPromise, callback);\n          return serializerPromise;\n        };\n        LocalForage.prototype.ready = function ready(callback) {\n          var self = this;\n          var promise = self._driverSet.then(function () {\n            if (self._ready === null) {\n              self._ready = self._initDriver();\n            }\n            return self._ready;\n          });\n          executeTwoCallbacks(promise, callback, callback);\n          return promise;\n        };\n        LocalForage.prototype.setDriver = function setDriver(drivers, callback, errorCallback) {\n          var self = this;\n          if (!isArray(drivers)) {\n            drivers = [drivers];\n          }\n          var supportedDrivers = this._getSupportedDrivers(drivers);\n          function setDriverToConfig() {\n            self._config.driver = self.driver();\n          }\n          function extendSelfWithDriver(driver) {\n            self._extend(driver);\n            setDriverToConfig();\n            self._ready = self._initStorage(self._config);\n            return self._ready;\n          }\n          function initDriver(supportedDrivers) {\n            return function () {\n              var currentDriverIndex = 0;\n              function driverPromiseLoop() {\n                while (currentDriverIndex < supportedDrivers.length) {\n                  var driverName = supportedDrivers[currentDriverIndex];\n                  currentDriverIndex++;\n                  self._dbInfo = null;\n                  self._ready = null;\n                  return self.getDriver(driverName).then(extendSelfWithDriver)[\"catch\"](driverPromiseLoop);\n                }\n                setDriverToConfig();\n                var error = new Error('No available storage method found.');\n                self._driverSet = Promise$1.reject(error);\n                return self._driverSet;\n              }\n              return driverPromiseLoop();\n            };\n          }\n\n          // There might be a driver initialization in progress\n          // so wait for it to finish in order to avoid a possible\n          // race condition to set _dbInfo\n          var oldDriverSetDone = this._driverSet !== null ? this._driverSet[\"catch\"](function () {\n            return Promise$1.resolve();\n          }) : Promise$1.resolve();\n          this._driverSet = oldDriverSetDone.then(function () {\n            var driverName = supportedDrivers[0];\n            self._dbInfo = null;\n            self._ready = null;\n            return self.getDriver(driverName).then(function (driver) {\n              self._driver = driver._driver;\n              setDriverToConfig();\n              self._wrapLibraryMethodsWithReady();\n              self._initDriver = initDriver(supportedDrivers);\n            });\n          })[\"catch\"](function () {\n            setDriverToConfig();\n            var error = new Error('No available storage method found.');\n            self._driverSet = Promise$1.reject(error);\n            return self._driverSet;\n          });\n          executeTwoCallbacks(this._driverSet, callback, errorCallback);\n          return this._driverSet;\n        };\n        LocalForage.prototype.supports = function supports(driverName) {\n          return !!DriverSupport[driverName];\n        };\n        LocalForage.prototype._extend = function _extend(libraryMethodsAndProperties) {\n          extend(this, libraryMethodsAndProperties);\n        };\n        LocalForage.prototype._getSupportedDrivers = function _getSupportedDrivers(drivers) {\n          var supportedDrivers = [];\n          for (var i = 0, len = drivers.length; i < len; i++) {\n            var driverName = drivers[i];\n            if (this.supports(driverName)) {\n              supportedDrivers.push(driverName);\n            }\n          }\n          return supportedDrivers;\n        };\n        LocalForage.prototype._wrapLibraryMethodsWithReady = function _wrapLibraryMethodsWithReady() {\n          // Add a stub for each driver API method that delays the call to the\n          // corresponding driver method until localForage is ready. These stubs\n          // will be replaced by the driver methods as soon as the driver is\n          // loaded, so there is no performance impact.\n          for (var i = 0, len = LibraryMethods.length; i < len; i++) {\n            callWhenReady(this, LibraryMethods[i]);\n          }\n        };\n        LocalForage.prototype.createInstance = function createInstance(options) {\n          return new LocalForage(options);\n        };\n        return LocalForage;\n      }();\n\n      // The actual localForage object that we expose as a module or via a\n      // global. It's extended by pulling in one of our other libraries.\n\n      var localforage_js = new LocalForage();\n      module.exports = localforage_js;\n    }, {\n      \"3\": 3\n    }]\n  }, {}, [4])(4);\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}