{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/loading.service\";\nimport * as i2 from \"@angular/common\";\nfunction LoadingSpinnerComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"div\", 12)(2, \"div\", 12)(3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoadingSpinnerComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n}\nfunction LoadingSpinnerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"div\", 15)(2, \"div\", 15)(3, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoadingSpinnerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.message);\n  }\n}\nfunction LoadingSpinnerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵelement(2, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.progress, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.progress, \"%\");\n  }\n}\nfunction LoadingSpinnerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n    i0.ɵɵtemplate(3, LoadingSpinnerComponent_div_0_div_3_Template, 4, 0, \"div\", 6)(4, LoadingSpinnerComponent_div_0_div_4_Template, 1, 0, \"div\", 7)(5, LoadingSpinnerComponent_div_0_div_5_Template, 4, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, LoadingSpinnerComponent_div_0_div_6_Template, 2, 1, \"div\", 9)(7, LoadingSpinnerComponent_div_0_div_7_Template, 5, 3, \"div\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r0.webSpinnerType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.webSpinnerType === \"dots\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.webSpinnerType === \"circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.webSpinnerType === \"pulse\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.message);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showProgress && ctx_r0.progress !== undefined);\n  }\n}\nfunction LoadingSpinnerComponent_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"div\", 12)(2, \"div\", 12)(3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoadingSpinnerComponent_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n}\nfunction LoadingSpinnerComponent_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"div\", 15)(2, \"div\", 15)(3, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoadingSpinnerComponent_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.message);\n  }\n}\nfunction LoadingSpinnerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, LoadingSpinnerComponent_div_1_div_2_Template, 4, 0, \"div\", 6)(3, LoadingSpinnerComponent_div_1_div_3_Template, 1, 0, \"div\", 7)(4, LoadingSpinnerComponent_div_1_div_4_Template, 4, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, LoadingSpinnerComponent_div_1_div_5_Template, 2, 1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"small\", ctx_r0.size === \"small\")(\"large\", ctx_r0.size === \"large\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r0.webSpinnerType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.webSpinnerType === \"dots\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.webSpinnerType === \"circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.webSpinnerType === \"pulse\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.message);\n  }\n}\nfunction LoadingSpinnerComponent_div_2_p_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.message);\n  }\n}\nfunction LoadingSpinnerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 13);\n    i0.ɵɵtemplate(3, LoadingSpinnerComponent_div_2_p_3_Template, 2, 1, \"p\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.message);\n  }\n}\nexport let LoadingSpinnerComponent = /*#__PURE__*/(() => {\n  class LoadingSpinnerComponent {\n    constructor(loadingService) {\n      this.loadingService = loadingService;\n      this.message = '';\n      this.spinnerType = 'crescent'; // Legacy Ionic spinner type\n      this.webSpinnerType = 'circle'; // New web spinner types\n      this.color = 'primary';\n      this.overlay = false;\n      this.showGlobalLoading = false;\n      this.showInlineLoading = false;\n      this.size = 'medium';\n      this.showProgress = false;\n      this.isGlobalLoading = false;\n      this.isInlineLoading = false;\n      this.subscription = new Subscription();\n    }\n    ngOnInit() {\n      if (this.showGlobalLoading) {\n        this.subscription.add(this.loadingService.isLoading$.subscribe(loading => {\n          this.isGlobalLoading = loading;\n        }));\n      }\n      if (this.loadingKey) {\n        this.subscription.add(this.loadingService.isLoadingKey(this.loadingKey).subscribe(loading => {\n          this.isInlineLoading = loading;\n        }));\n        // Subscribe to loading state for progress and message updates\n        this.subscription.add(this.loadingService.loadingState$.subscribe(state => {\n          const loadingState = state[this.loadingKey];\n          if (loadingState) {\n            this.message = this.message || loadingState.message || '';\n            this.progress = loadingState.progress;\n          }\n        }));\n      }\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n    }\n    static {\n      this.ɵfac = function LoadingSpinnerComponent_Factory(t) {\n        return new (t || LoadingSpinnerComponent)(i0.ɵɵdirectiveInject(i1.LoadingService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoadingSpinnerComponent,\n        selectors: [[\"app-loading-spinner\"]],\n        inputs: {\n          message: \"message\",\n          spinnerType: \"spinnerType\",\n          webSpinnerType: \"webSpinnerType\",\n          color: \"color\",\n          overlay: \"overlay\",\n          showGlobalLoading: \"showGlobalLoading\",\n          showInlineLoading: \"showInlineLoading\",\n          loadingKey: \"loadingKey\",\n          size: \"size\",\n          showProgress: \"showProgress\",\n          progress: \"progress\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 3,\n        consts: [[\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"inline-loading\", 3, \"small\", \"large\", 4, \"ngIf\"], [\"class\", \"loading-container overlay\", 4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"loading-content\"], [1, \"spinner\"], [\"class\", \"dots-spinner\", 4, \"ngIf\"], [\"class\", \"circle-spinner\", 4, \"ngIf\"], [\"class\", \"pulse-spinner\", 4, \"ngIf\"], [\"class\", \"loading-message\", 4, \"ngIf\"], [\"class\", \"loading-progress\", 4, \"ngIf\"], [1, \"dots-spinner\"], [1, \"dot\"], [1, \"circle-spinner\"], [1, \"pulse-spinner\"], [1, \"pulse-dot\"], [1, \"loading-message\"], [1, \"loading-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"inline-loading\"], [1, \"loading-container\", \"overlay\"], [1, \"spinner-wrapper\"]],\n        template: function LoadingSpinnerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, LoadingSpinnerComponent_div_0_Template, 8, 7, \"div\", 0)(1, LoadingSpinnerComponent_div_1_Template, 6, 10, \"div\", 1)(2, LoadingSpinnerComponent_div_2_Template, 4, 1, \"div\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.showGlobalLoading && ctx.isGlobalLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showInlineLoading && ctx.isInlineLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.overlay && !ctx.showGlobalLoading);\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf],\n        styles: [\".loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:2rem}.loading-container.overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#00000080;z-index:9999}.spinner-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1rem}.loading-message[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.875rem;text-align:center}.loading-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#00000080;display:flex;align-items:center;justify-content:center;z-index:9999;-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}.loading-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:2rem;text-align:center;box-shadow:0 10px 40px #0000004d;max-width:300px;width:90%}.inline-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1rem;padding:2rem}.inline-loading.small[_ngcontent-%COMP%]{padding:1rem}.inline-loading.small[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{transform:scale(.7)}.inline-loading.large[_ngcontent-%COMP%]{padding:3rem}.inline-loading.large[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{transform:scale(1.3)}.loading-progress[_ngcontent-%COMP%]{margin-top:1.5rem}.progress-bar[_ngcontent-%COMP%]{width:100%;height:6px;background:#e9ecef;border-radius:3px;overflow:hidden}.progress-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#007bff,#0056b3);border-radius:3px;transition:width .3s ease}.progress-text[_ngcontent-%COMP%]{margin-top:.5rem;font-size:.8rem;color:#666}.spinner[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.dots-spinner[_ngcontent-%COMP%]{display:flex;gap:.25rem}.dot[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background:#007bff;animation:_ngcontent-%COMP%_dotPulse 1.4s infinite ease-in-out}.dot[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.dot[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}@keyframes _ngcontent-%COMP%_dotPulse{0%,80%,to{transform:scale(0);opacity:.5}40%{transform:scale(1);opacity:1}}.circle-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_circleRotate 1s linear infinite}@keyframes _ngcontent-%COMP%_circleRotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.pulse-spinner[_ngcontent-%COMP%]{display:flex;gap:.25rem}.pulse-dot[_ngcontent-%COMP%]{width:10px;height:10px;border-radius:50%;background:#007bff;animation:_ngcontent-%COMP%_pulseBeat 1.4s infinite ease-in-out}.pulse-dot[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.pulse-dot[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}@keyframes _ngcontent-%COMP%_pulseBeat{0%,80%,to{transform:scale(.6);opacity:.5}40%{transform:scale(1);opacity:1}}\"]\n      });\n    }\n  }\n  return LoadingSpinnerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}