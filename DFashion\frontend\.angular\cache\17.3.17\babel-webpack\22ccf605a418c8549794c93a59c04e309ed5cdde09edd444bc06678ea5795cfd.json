{"ast": null, "code": "// Generated by CoffeeScript 1.12.2\n(function () {\n  var getNanoSeconds, hrtime, loadTime, moduleLoadTime, nodeLoadTime, upTime;\n  if (typeof performance !== \"undefined\" && performance !== null && performance.now) {\n    module.exports = function () {\n      return performance.now();\n    };\n  } else if (typeof process !== \"undefined\" && process !== null && process.hrtime) {\n    module.exports = function () {\n      return (getNanoSeconds() - nodeLoadTime) / 1e6;\n    };\n    hrtime = process.hrtime;\n    getNanoSeconds = function () {\n      var hr;\n      hr = hrtime();\n      return hr[0] * 1e9 + hr[1];\n    };\n    moduleLoadTime = getNanoSeconds();\n    upTime = process.uptime() * 1e9;\n    nodeLoadTime = moduleLoadTime - upTime;\n  } else if (Date.now) {\n    module.exports = function () {\n      return Date.now() - loadTime;\n    };\n    loadTime = Date.now();\n  } else {\n    module.exports = function () {\n      return new Date().getTime() - loadTime;\n    };\n    loadTime = new Date().getTime();\n  }\n}).call(this);", "map": {"version": 3, "names": ["getNanoSeconds", "hrtime", "loadTime", "moduleLoadTime", "nodeLoadTime", "upTime", "performance", "now", "module", "exports", "process", "hr", "uptime", "Date", "getTime", "call"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/performance-now/lib/performance-now.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.2\n(function() {\n  var getNanoSeconds, hrtime, loadTime, moduleLoadTime, nodeLoadTime, upTime;\n\n  if ((typeof performance !== \"undefined\" && performance !== null) && performance.now) {\n    module.exports = function() {\n      return performance.now();\n    };\n  } else if ((typeof process !== \"undefined\" && process !== null) && process.hrtime) {\n    module.exports = function() {\n      return (getNanoSeconds() - nodeLoadTime) / 1e6;\n    };\n    hrtime = process.hrtime;\n    getNanoSeconds = function() {\n      var hr;\n      hr = hrtime();\n      return hr[0] * 1e9 + hr[1];\n    };\n    moduleLoadTime = getNanoSeconds();\n    upTime = process.uptime() * 1e9;\n    nodeLoadTime = moduleLoadTime - upTime;\n  } else if (Date.now) {\n    module.exports = function() {\n      return Date.now() - loadTime;\n    };\n    loadTime = Date.now();\n  } else {\n    module.exports = function() {\n      return new Date().getTime() - loadTime;\n    };\n    loadTime = new Date().getTime();\n  }\n\n}).call(this);\n\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,YAAY,EAAEC,MAAM;EAE1E,IAAK,OAAOC,WAAW,KAAK,WAAW,IAAIA,WAAW,KAAK,IAAI,IAAKA,WAAW,CAACC,GAAG,EAAE;IACnFC,MAAM,CAACC,OAAO,GAAG,YAAW;MAC1B,OAAOH,WAAW,CAACC,GAAG,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,MAAM,IAAK,OAAOG,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,IAAI,IAAKA,OAAO,CAACT,MAAM,EAAE;IACjFO,MAAM,CAACC,OAAO,GAAG,YAAW;MAC1B,OAAO,CAACT,cAAc,CAAC,CAAC,GAAGI,YAAY,IAAI,GAAG;IAChD,CAAC;IACDH,MAAM,GAAGS,OAAO,CAACT,MAAM;IACvBD,cAAc,GAAG,SAAAA,CAAA,EAAW;MAC1B,IAAIW,EAAE;MACNA,EAAE,GAAGV,MAAM,CAAC,CAAC;MACb,OAAOU,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC;IACDR,cAAc,GAAGH,cAAc,CAAC,CAAC;IACjCK,MAAM,GAAGK,OAAO,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;IAC/BR,YAAY,GAAGD,cAAc,GAAGE,MAAM;EACxC,CAAC,MAAM,IAAIQ,IAAI,CAACN,GAAG,EAAE;IACnBC,MAAM,CAACC,OAAO,GAAG,YAAW;MAC1B,OAAOI,IAAI,CAACN,GAAG,CAAC,CAAC,GAAGL,QAAQ;IAC9B,CAAC;IACDA,QAAQ,GAAGW,IAAI,CAACN,GAAG,CAAC,CAAC;EACvB,CAAC,MAAM;IACLC,MAAM,CAACC,OAAO,GAAG,YAAW;MAC1B,OAAO,IAAII,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGZ,QAAQ;IACxC,CAAC;IACDA,QAAQ,GAAG,IAAIW,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACjC;AAEF,CAAC,EAAEC,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}