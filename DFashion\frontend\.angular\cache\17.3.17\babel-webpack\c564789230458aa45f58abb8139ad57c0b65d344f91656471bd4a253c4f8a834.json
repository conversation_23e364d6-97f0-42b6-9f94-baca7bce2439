{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/common\";\nfunction CreateStoryComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Upload Image or Video\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Stories disappear after 24 hours\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreateStoryComponent_div_15_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 38);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.selectedMedia.preview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CreateStoryComponent_div_15_video_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 39);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.selectedMedia.preview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CreateStoryComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, CreateStoryComponent_div_15_img_1_Template, 1, 1, \"img\", 34)(2, CreateStoryComponent_div_15_video_2_Template, 1, 1, \"video\", 35);\n    i0.ɵɵelementStart(3, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CreateStoryComponent_div_15_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeMedia());\n    });\n    i0.ɵɵelement(4, \"i\", 37);\n    i0.ɵɵtext(5, \" Change Media \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMedia.type.startsWith(\"image\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMedia.type.startsWith(\"video\"));\n  }\n}\nfunction CreateStoryComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function CreateStoryComponent_div_27_div_1_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addProductTag(product_r6));\n    });\n    i0.ɵɵelement(1, \"img\", 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 45)(9, \"button\", 46);\n    i0.ɵɵtext(10, \"Tag\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r6.images[0] == null ? null : product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(7, 4, product_r6.price, \"1.0-0\"), \"\");\n  }\n}\nfunction CreateStoryComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CreateStoryComponent_div_27_div_1_Template, 11, 7, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.searchResults);\n  }\n}\nfunction CreateStoryComponent_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"img\", 43);\n    i0.ɵɵelementStart(2, \"div\", 51)(3, \"span\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 54)(9, \"span\", 55);\n    i0.ɵɵtext(10, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 56);\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 57);\n    i0.ɵɵtext(14, \"\\u2661 Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function CreateStoryComponent_div_28_div_4_Template_button_click_15_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.removeProductTag(i_r8));\n    });\n    i0.ɵɵelement(16, \"i\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r9.images[0] == null ? null : product_r9.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(7, 4, product_r9.price, \"1.0-0\"), \"\");\n  }\n}\nfunction CreateStoryComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"h4\");\n    i0.ɵɵtext(2, \"Tagged Products:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 48);\n    i0.ɵɵtemplate(4, CreateStoryComponent_div_28_div_4_Template, 17, 7, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.taggedProducts);\n  }\n}\nfunction CreateStoryComponent_span_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Publishing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateStoryComponent_span_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Publish Story\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateStoryComponent_div_67_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 38);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.selectedMedia.preview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CreateStoryComponent_div_67_video_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 67);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.selectedMedia.preview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CreateStoryComponent_div_67_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r3.storyForm.get(\"caption\")) == null ? null : tmp_3_0.value, \" \");\n  }\n}\nfunction CreateStoryComponent_div_67_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"img\", 43);\n    i0.ɵɵelementStart(3, \"div\", 51)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 45)(10, \"button\", 73);\n    i0.ɵɵtext(11, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 74);\n    i0.ɵɵtext(13, \"Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 75);\n    i0.ɵɵtext(15, \"\\u2661\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r10.images[0] == null ? null : product_r10.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 4, product_r10.price, \"1.0-0\"), \"\");\n  }\n}\nfunction CreateStoryComponent_div_67_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, CreateStoryComponent_div_67_div_9_div_1_Template, 16, 7, \"div\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.taggedProducts);\n  }\n}\nfunction CreateStoryComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"h3\");\n    i0.ɵɵtext(2, \"Preview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61)(4, \"div\", 62);\n    i0.ɵɵtemplate(5, CreateStoryComponent_div_67_img_5_Template, 1, 1, \"img\", 34)(6, CreateStoryComponent_div_67_video_6_Template, 1, 1, \"video\", 63);\n    i0.ɵɵelementStart(7, \"div\", 64);\n    i0.ɵɵtemplate(8, CreateStoryComponent_div_67_div_8_Template, 2, 1, \"div\", 65)(9, CreateStoryComponent_div_67_div_9_Template, 2, 1, \"div\", 66);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMedia.type.startsWith(\"image\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMedia.type.startsWith(\"video\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r3.storyForm.get(\"caption\")) == null ? null : tmp_4_0.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.taggedProducts.length > 0);\n  }\n}\nexport let CreateStoryComponent = /*#__PURE__*/(() => {\n  class CreateStoryComponent {\n    constructor(fb, router, http) {\n      this.fb = fb;\n      this.router = router;\n      this.http = http;\n      this.selectedMedia = null;\n      this.taggedProducts = [];\n      this.searchResults = [];\n      this.uploading = false;\n      // New properties for mandatory linking\n      this.linkType = 'product';\n      this.selectedCategory = null;\n      this.categories = [];\n      this.categorySearchResults = [];\n      this.storyForm = this.fb.group({\n        caption: ['', [Validators.maxLength(500)]],\n        allowReplies: [true],\n        showViewers: [true],\n        highlightProducts: [true],\n        duration: ['24'],\n        linkType: ['product', Validators.required]\n      });\n    }\n    ngOnInit() {}\n    onFileSelect(event) {\n      const file = event.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.selectedMedia = {\n            file,\n            preview: e.target.result,\n            type: file.type,\n            name: file.name\n          };\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    removeMedia() {\n      this.selectedMedia = null;\n    }\n    searchProducts(event) {\n      const query = event.target.value;\n      if (query.length > 2) {\n        // TODO: Implement actual product search API\n        this.searchResults = [{\n          _id: '1',\n          name: 'Summer Dress',\n          price: 2999,\n          images: [{\n            url: '/assets/images/product1.jpg'\n          }]\n        }, {\n          _id: '2',\n          name: 'Casual Shirt',\n          price: 1599,\n          images: [{\n            url: '/assets/images/product2.jpg'\n          }]\n        }].filter(p => p.name.toLowerCase().includes(query.toLowerCase()));\n      } else {\n        this.searchResults = [];\n      }\n    }\n    addProductTag(product) {\n      if (!this.taggedProducts.find(p => p._id === product._id)) {\n        this.taggedProducts.push(product);\n      }\n      this.searchResults = [];\n    }\n    removeProductTag(index) {\n      this.taggedProducts.splice(index, 1);\n    }\n    saveDraft() {\n      console.log('Saving as draft...');\n    }\n    onSubmit() {\n      // Validate that at least one product is tagged\n      if (this.taggedProducts.length === 0) {\n        alert('Please tag at least one product before sharing your story. This helps customers discover and purchase your products!');\n        return;\n      }\n      if (this.storyForm.valid && this.selectedMedia) {\n        this.uploading = true;\n        const storyData = {\n          media: {\n            type: this.selectedMedia.type.startsWith('image') ? 'image' : 'video',\n            url: this.selectedMedia.preview // In real implementation, upload to server first\n          },\n          caption: this.storyForm.value.caption,\n          products: this.taggedProducts.map(p => ({\n            product: p._id,\n            position: {\n              x: 50,\n              y: 50\n            }\n          })),\n          settings: {\n            allowReplies: this.storyForm.value.allowReplies,\n            showViewers: this.storyForm.value.showViewers,\n            highlightProducts: this.storyForm.value.highlightProducts\n          },\n          duration: parseInt(this.storyForm.value.duration)\n        };\n        // TODO: Implement actual story creation API\n        this.http.post('/api/stories', storyData).subscribe({\n          next: response => {\n            this.uploading = false;\n            alert('Story created successfully!');\n            this.router.navigate(['/vendor/stories']);\n          },\n          error: error => {\n            this.uploading = false;\n            alert('Error creating story: ' + (error.error?.message || 'Unknown error'));\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function CreateStoryComponent_Factory(t) {\n        return new (t || CreateStoryComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CreateStoryComponent,\n        selectors: [[\"app-create-story\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 68,\n        vars: 12,\n        consts: [[\"fileInput\", \"\"], [1, \"create-story-container\"], [1, \"header\"], [1, \"story-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"media-upload\"], [1, \"upload-area\", 3, \"click\"], [\"type\", \"file\", \"accept\", \"image/*,video/*\", 2, \"display\", \"none\", 3, \"change\"], [\"class\", \"upload-content\", 4, \"ngIf\"], [\"class\", \"media-preview\", 4, \"ngIf\"], [\"formControlName\", \"caption\", \"placeholder\", \"Add a caption to your story...\", \"rows\", \"3\", \"maxlength\", \"500\"], [1, \"char-count\"], [1, \"product-search\"], [\"type\", \"text\", \"placeholder\", \"Search your products to tag...\", 1, \"search-input\", 3, \"input\"], [\"class\", \"product-results\", 4, \"ngIf\"], [\"class\", \"tagged-products\", 4, \"ngIf\"], [1, \"settings-grid\"], [1, \"setting-item\"], [\"type\", \"checkbox\", \"formControlName\", \"allowReplies\"], [\"type\", \"checkbox\", \"formControlName\", \"showViewers\"], [\"type\", \"checkbox\", \"formControlName\", \"highlightProducts\"], [1, \"duration-options\"], [1, \"duration-option\"], [\"type\", \"radio\", \"name\", \"duration\", \"value\", \"24\", \"formControlName\", \"duration\"], [\"type\", \"radio\", \"name\", \"duration\", \"value\", \"12\", \"formControlName\", \"duration\"], [\"type\", \"radio\", \"name\", \"duration\", \"value\", \"6\", \"formControlName\", \"duration\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-primary\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"story-preview\", 4, \"ngIf\"], [1, \"upload-content\"], [1, \"fas\", \"fa-camera\"], [1, \"media-preview\"], [\"alt\", \"Story preview\", 3, \"src\", 4, \"ngIf\"], [\"controls\", \"\", 3, \"src\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"change-media\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"alt\", \"Story preview\", 3, \"src\"], [\"controls\", \"\", 3, \"src\"], [1, \"product-results\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-item\", 3, \"click\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-actions\"], [\"type\", \"button\", 1, \"btn-tag\"], [1, \"tagged-products\"], [1, \"tagged-list\"], [\"class\", \"tagged-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"tagged-item\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"product-buttons\"], [1, \"buy-btn\"], [1, \"cart-btn\"], [1, \"wishlist-btn\"], [\"type\", \"button\", 1, \"remove-tag\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-preview\"], [1, \"preview-container\"], [1, \"story-frame\"], [\"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [1, \"story-overlay\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [\"class\", \"story-products\", 4, \"ngIf\"], [\"muted\", \"\", 3, \"src\"], [1, \"story-caption\"], [1, \"story-products\"], [\"class\", \"product-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\"], [1, \"product-info-popup\"], [1, \"btn-buy\"], [1, \"btn-cart\"], [1, \"btn-wishlist\"]],\n        template: function CreateStoryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\");\n            i0.ɵɵtext(3, \"Create New Story\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Share a 24-hour story with your products\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"form\", 3);\n            i0.ɵɵlistener(\"ngSubmit\", function CreateStoryComponent_Template_form_ngSubmit_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"h3\");\n            i0.ɵɵtext(9, \"Story Media\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6);\n            i0.ɵɵlistener(\"click\", function CreateStoryComponent_Template_div_click_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const fileInput_r2 = i0.ɵɵreference(13);\n              return i0.ɵɵresetView(fileInput_r2.click());\n            });\n            i0.ɵɵelementStart(12, \"input\", 7, 0);\n            i0.ɵɵlistener(\"change\", function CreateStoryComponent_Template_input_change_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelect($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(14, CreateStoryComponent_div_14_Template, 6, 0, \"div\", 8)(15, CreateStoryComponent_div_15_Template, 6, 2, \"div\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 4)(17, \"h3\");\n            i0.ɵɵtext(18, \"Caption (Optional)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"textarea\", 10);\n            i0.ɵɵelementStart(20, \"div\", 11);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 4)(23, \"h3\");\n            i0.ɵɵtext(24, \"Tag Products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 12)(26, \"input\", 13);\n            i0.ɵɵlistener(\"input\", function CreateStoryComponent_Template_input_input_26_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchProducts($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, CreateStoryComponent_div_27_Template, 2, 1, \"div\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(28, CreateStoryComponent_div_28_Template, 5, 1, \"div\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 4)(30, \"h3\");\n            i0.ɵɵtext(31, \"Story Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 16)(33, \"label\", 17);\n            i0.ɵɵelement(34, \"input\", 18);\n            i0.ɵɵelementStart(35, \"span\");\n            i0.ɵɵtext(36, \"Allow replies\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"label\", 17);\n            i0.ɵɵelement(38, \"input\", 19);\n            i0.ɵɵelementStart(39, \"span\");\n            i0.ɵɵtext(40, \"Show viewers\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"label\", 17);\n            i0.ɵɵelement(42, \"input\", 20);\n            i0.ɵɵelementStart(43, \"span\");\n            i0.ɵɵtext(44, \"Highlight products\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(45, \"div\", 4)(46, \"h3\");\n            i0.ɵɵtext(47, \"Duration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 21)(49, \"label\", 22);\n            i0.ɵɵelement(50, \"input\", 23);\n            i0.ɵɵelementStart(51, \"span\");\n            i0.ɵɵtext(52, \"24 Hours (Default)\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"label\", 22);\n            i0.ɵɵelement(54, \"input\", 24);\n            i0.ɵɵelementStart(55, \"span\");\n            i0.ɵɵtext(56, \"12 Hours\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"label\", 22);\n            i0.ɵɵelement(58, \"input\", 25);\n            i0.ɵɵelementStart(59, \"span\");\n            i0.ɵɵtext(60, \"6 Hours\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(61, \"div\", 26)(62, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function CreateStoryComponent_Template_button_click_62_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.saveDraft());\n            });\n            i0.ɵɵtext(63, \"Save as Draft\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"button\", 28);\n            i0.ɵɵtemplate(65, CreateStoryComponent_span_65_Template, 2, 0, \"span\", 29)(66, CreateStoryComponent_span_66_Template, 2, 0, \"span\", 29);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(67, CreateStoryComponent_div_67_Template, 10, 4, \"div\", 30);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            let tmp_5_0;\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.storyForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"has-media\", ctx.selectedMedia);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", !ctx.selectedMedia);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedMedia);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\"\", ((tmp_5_0 = ctx.storyForm.get(\"caption\")) == null ? null : tmp_5_0.value == null ? null : tmp_5_0.value.length) || 0, \"/500\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.searchResults.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.taggedProducts.length > 0);\n            i0.ɵɵadvance(36);\n            i0.ɵɵproperty(\"disabled\", !ctx.storyForm.valid || !ctx.selectedMedia || ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedMedia);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".create-story-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px;display:grid;grid-template-columns:1fr 300px;gap:40px}.header[_ngcontent-%COMP%]{grid-column:1/-1;margin-bottom:20px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:8px}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}.story-form[_ngcontent-%COMP%]{background:#fff;border-radius:8px;padding:30px;border:1px solid #eee;height:-moz-fit-content;height:fit-content}.form-section[_ngcontent-%COMP%]{margin-bottom:25px}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:12px}.upload-area[_ngcontent-%COMP%]{border:2px dashed #ddd;border-radius:8px;padding:30px;text-align:center;cursor:pointer;transition:all .2s;min-height:200px;display:flex;align-items:center;justify-content:center}.upload-area[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.upload-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2.5rem;color:#ddd;margin-bottom:15px}.upload-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;margin-bottom:5px}.upload-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.media-preview[_ngcontent-%COMP%]{position:relative;width:100%}.media-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .media-preview[_ngcontent-%COMP%]   video[_ngcontent-%COMP%]{width:100%;max-height:300px;object-fit:cover;border-radius:8px}.change-media[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;background:#000000b3;color:#fff;border:none;padding:8px 12px;border-radius:6px;cursor:pointer}textarea[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px;font-family:inherit;resize:vertical}.char-count[_ngcontent-%COMP%]{text-align:right;color:#666;font-size:.85rem;margin-top:5px}.search-input[_ngcontent-%COMP%]{width:100%;padding:10px;border:1px solid #ddd;border-radius:6px;margin-bottom:10px}.product-results[_ngcontent-%COMP%]{max-height:150px;overflow-y:auto;border:1px solid #eee;border-radius:6px}.product-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;padding:10px;cursor:pointer;border-bottom:1px solid #f5f5f5}.product-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.product-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:cover;border-radius:4px}.product-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.85rem;margin-bottom:2px}.product-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.8rem}.btn-tag[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:4px 8px;border-radius:4px;font-size:.8rem;cursor:pointer}.tagged-list[_ngcontent-%COMP%]{margin-top:10px}.tagged-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;background:#f8f9fa;padding:10px;border-radius:6px;margin-bottom:8px}.tagged-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:cover;border-radius:4px}.product-details[_ngcontent-%COMP%]{flex:1}.product-name[_ngcontent-%COMP%]{display:block;font-size:.9rem;font-weight:500}.product-price[_ngcontent-%COMP%]{color:#666;font-size:.8rem}.product-buttons[_ngcontent-%COMP%]{display:flex;gap:5px}.buy-btn[_ngcontent-%COMP%], .cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]{font-size:.7rem;padding:2px 6px;border-radius:3px;background:#e9ecef;color:#495057}.settings-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr;gap:10px}.setting-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer}.duration-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.duration-option[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer}.form-actions[_ngcontent-%COMP%]{display:flex;gap:12px;margin-top:25px;padding-top:20px;border-top:1px solid #eee}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%]{padding:10px 20px;border-radius:6px;font-weight:500;cursor:pointer;border:none;transition:all .2s;flex:1}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6}.story-preview[_ngcontent-%COMP%]{position:sticky;top:20px;height:-moz-fit-content;height:fit-content}.story-preview[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:15px}.preview-container[_ngcontent-%COMP%]{background:#000;border-radius:12px;overflow:hidden;aspect-ratio:9/16;position:relative}.story-frame[_ngcontent-%COMP%]{width:100%;height:100%;position:relative}.story-frame[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .story-frame[_ngcontent-%COMP%]   video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.story-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(transparent 60%,#0000004d);padding:20px;display:flex;flex-direction:column;justify-content:flex-end}.story-caption[_ngcontent-%COMP%]{color:#fff;font-size:.9rem;margin-bottom:10px;text-shadow:0 1px 3px rgba(0,0,0,.5)}.product-tag[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.product-info-popup[_ngcontent-%COMP%]{background:#fff;border-radius:8px;padding:10px;box-shadow:0 4px 12px #0000004d;min-width:200px}.product-info-popup[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:80px;object-fit:cover;border-radius:4px;margin-bottom:8px}.product-info-popup[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.85rem;margin-bottom:4px}.product-info-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.8rem;margin-bottom:8px}.product-actions[_ngcontent-%COMP%]{display:flex;gap:5px}.btn-buy[_ngcontent-%COMP%], .btn-cart[_ngcontent-%COMP%], .btn-wishlist[_ngcontent-%COMP%]{padding:4px 8px;border:none;border-radius:4px;font-size:.7rem;cursor:pointer}.btn-buy[_ngcontent-%COMP%]{background:#28a745;color:#fff}.btn-cart[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-wishlist[_ngcontent-%COMP%]{background:#f8f9fa;color:#666}@media (max-width: 768px){.create-story-container[_ngcontent-%COMP%]{grid-template-columns:1fr}.story-preview[_ngcontent-%COMP%]{order:-1}.preview-container[_ngcontent-%COMP%]{max-width:200px;margin:0 auto}}\"]\n      });\n    }\n  }\n  return CreateStoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}