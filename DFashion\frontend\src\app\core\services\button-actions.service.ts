import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { tap, catchError } from 'rxjs/operators';
import { AuthService } from './auth.service';
import { CartNewService } from './cart-new.service';
import { WishlistNewService } from './wishlist-new.service';
import { SocialMediaService } from './social-media.service';
import { RealtimeService } from './realtime.service';
import { Router } from '@angular/router';

export interface ButtonActionResult {
  success: boolean;
  message: string;
  data?: any;
}

export interface ProductActionData {
  productId: string;
  size?: string;
  color?: string;
  quantity?: number;
  addedFrom?: string;
}

export interface PostActionData {
  postId: string;
  text?: string;
}

export interface StoryActionData {
  storyId: string;
}

@Injectable({
  providedIn: 'root'
})
export class ButtonActionsService {
  private readonly API_URL = 'http://localhost:5000/api';
  
  // Real-time state subjects
  private likedPostsSubject = new BehaviorSubject<Set<string>>(new Set());
  private likedStoriesSubject = new BehaviorSubject<Set<string>>(new Set());
  private savedPostsSubject = new BehaviorSubject<Set<string>>(new Set());
  private cartItemsSubject = new BehaviorSubject<Set<string>>(new Set());
  private wishlistItemsSubject = new BehaviorSubject<Set<string>>(new Set());

  // Observables for components to subscribe to
  public likedPosts$ = this.likedPostsSubject.asObservable();
  public likedStories$ = this.likedStoriesSubject.asObservable();
  public savedPosts$ = this.savedPostsSubject.asObservable();
  public cartItems$ = this.cartItemsSubject.asObservable();
  public wishlistItems$ = this.wishlistItemsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private cartService: CartNewService,
    private wishlistService: WishlistNewService,
    private socialMediaService: SocialMediaService,
    private realtimeService: RealtimeService,
    private router: Router
  ) {
    this.initializeUserState();
  }

  private initializeUserState(): void {
    // Initialize user's liked posts, cart items, etc. when service starts
    if (this.authService.isAuthenticated) {
      this.loadUserState();
    }

    // Listen to auth changes
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        this.loadUserState();
      } else {
        this.clearUserState();
      }
    });
  }

  private loadUserState(): void {
    // Load user's current state (liked posts, cart items, etc.)
    this.loadLikedPosts();
    this.loadCartItems();
    this.loadWishlistItems();
  }

  private clearUserState(): void {
    this.likedPostsSubject.next(new Set());
    this.likedStoriesSubject.next(new Set());
    this.savedPostsSubject.next(new Set());
    this.cartItemsSubject.next(new Set());
    this.wishlistItemsSubject.next(new Set());
  }

  private loadLikedPosts(): void {
    // This would load user's liked posts from API
    // For now, we'll track them as they happen
  }

  private loadCartItems(): void {
    this.cartService.cart$.subscribe(cart => {
      if (cart?.items) {
        const productIds = new Set(cart.items.map(item => {
          const product = item.product;
          return typeof product === 'string' ? product : product._id;
        }));
        this.cartItemsSubject.next(productIds);
      }
    });
  }

  private loadWishlistItems(): void {
    this.wishlistService.wishlist$.subscribe(wishlist => {
      if (wishlist?.items) {
        const productIds = new Set(wishlist.items.map(item => {
          const product = item.product;
          return typeof product === 'string' ? product : product._id;
        }));
        this.wishlistItemsSubject.next(productIds);
      }
    });
  }

  // ==================== CART ACTIONS ====================

  addToCart(data: ProductActionData): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      // Navigate to login page
      this.router.navigate(['/auth/login']);
      return of({ success: false, message: 'Please login to add items to cart' });
    }

    return this.cartService.addToCart(
      data.productId, 
      data.quantity || 1, 
      data.size, 
      data.color, 
      data.addedFrom || 'manual'
    ).pipe(
      tap(response => {
        if (response.success) {
          // Update local state
          const currentItems = this.cartItemsSubject.value;
          currentItems.add(data.productId);
          this.cartItemsSubject.next(currentItems);
          
          // Emit real-time event
          this.realtimeService.emitCartAdd(data);
        }
      }),
      catchError(error => {
        console.error('Add to cart error:', error);
        return of({ success: false, message: 'Failed to add item to cart' });
      })
    );
  }

  // ==================== WISHLIST ACTIONS ====================

  addToWishlist(data: ProductActionData): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return of({ success: false, message: 'Please login to add items to wishlist' });
    }

    return this.wishlistService.addToWishlist(
      data.productId,
      data.size,
      data.color,
      data.addedFrom || 'manual'
    ).pipe(
      tap(response => {
        if (response.success) {
          // Update local state
          const currentItems = this.wishlistItemsSubject.value;
          currentItems.add(data.productId);
          this.wishlistItemsSubject.next(currentItems);
          
          // Emit real-time event
          this.realtimeService.emitWishlistAdd(data);
        }
      }),
      catchError(error => {
        console.error('Add to wishlist error:', error);
        return of({ success: false, message: 'Failed to add item to wishlist' });
      })
    );
  }

  removeFromWishlist(productId: string): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      return of({ success: false, message: 'Please login to manage wishlist' });
    }

    return this.wishlistService.removeFromWishlist(productId).pipe(
      tap(response => {
        if (response.success) {
          // Update local state
          const currentItems = this.wishlistItemsSubject.value;
          currentItems.delete(productId);
          this.wishlistItemsSubject.next(currentItems);
          
          // Emit real-time event
          this.realtimeService.emitWishlistRemove(productId);
        }
      }),
      catchError(error => {
        console.error('Remove from wishlist error:', error);
        return of({ success: false, message: 'Failed to remove item from wishlist' });
      })
    );
  }

  // ==================== BUY NOW ACTION ====================

  buyNow(data: ProductActionData): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return of({ success: false, message: 'Please login to purchase items' });
    }

    // Add to cart first, then redirect to checkout
    return this.addToCart(data).pipe(
      tap(response => {
        if (response.success) {
          // Navigate to checkout page
          this.router.navigate(['/checkout']);
        }
      })
    );
  }

  // ==================== POST ACTIONS ====================

  likePost(postId: string): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return of({ success: false, message: 'Please login to like posts' });
    }

    return this.socialMediaService.likePost(postId).pipe(
      tap(response => {
        if (response.success) {
          // Update local state
          const currentLiked = this.likedPostsSubject.value;
          currentLiked.add(postId);
          this.likedPostsSubject.next(currentLiked);
        }
      }),
      catchError(error => {
        console.error('Like post error:', error);
        return of({ success: false, message: 'Failed to like post' });
      })
    );
  }

  unlikePost(postId: string): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      return of({ success: false, message: 'Please login to unlike posts' });
    }

    return this.socialMediaService.unlikePost(postId).pipe(
      tap(response => {
        if (response.success) {
          // Update local state
          const currentLiked = this.likedPostsSubject.value;
          currentLiked.delete(postId);
          this.likedPostsSubject.next(currentLiked);
        }
      }),
      catchError(error => {
        console.error('Unlike post error:', error);
        return of({ success: false, message: 'Failed to unlike post' });
      })
    );
  }

  commentOnPost(data: PostActionData): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return of({ success: false, message: 'Please login to comment on posts' });
    }

    if (!data.text?.trim()) {
      return of({ success: false, message: 'Comment text is required' });
    }

    return this.socialMediaService.commentOnPost(data.postId, data.text).pipe(
      catchError(error => {
        console.error('Comment on post error:', error);
        return of({ success: false, message: 'Failed to add comment' });
      })
    );
  }

  sharePost(postId: string): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return of({ success: false, message: 'Please login to share posts' });
    }

    return this.socialMediaService.sharePost(postId).pipe(
      catchError(error => {
        console.error('Share post error:', error);
        return of({ success: false, message: 'Failed to share post' });
      })
    );
  }

  savePost(postId: string): Observable<ButtonActionResult> {
    if (!this.authService.isAuthenticated) {
      this.router.navigate(['/auth/login']);
      return of({ success: false, message: 'Please login to save posts' });
    }

    return this.socialMediaService.savePost(postId).pipe(
      tap(response => {
        if (response.success) {
          // Update local state
          const currentSaved = this.savedPostsSubject.value;
          currentSaved.add(postId);
          this.savedPostsSubject.next(currentSaved);
        }
      }),
      catchError(error => {
        console.error('Save post error:', error);
        return of({ success: false, message: 'Failed to save post' });
      })
    );
  }

  // ==================== UTILITY METHODS ====================

  isInCart(productId: string): boolean {
    return this.cartItemsSubject.value.has(productId);
  }

  isInWishlist(productId: string): boolean {
    return this.wishlistItemsSubject.value.has(productId);
  }

  isPostLiked(postId: string): boolean {
    return this.likedPostsSubject.value.has(postId);
  }

  isPostSaved(postId: string): boolean {
    return this.savedPostsSubject.value.has(postId);
  }

  private showSuccessMessage(message: string): void {
    // This would show a toast/snackbar message
    console.log('✅', message);
  }

  private showErrorMessage(message: string): void {
    // This would show a toast/snackbar message
    console.error('❌', message);
  }
}
