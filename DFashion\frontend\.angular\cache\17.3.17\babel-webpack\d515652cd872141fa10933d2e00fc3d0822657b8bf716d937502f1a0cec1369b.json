{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport { AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\n\n/** @docs-private */\nconst _c0 = [\"mat-sort-header\", \"\"];\nconst _c1 = [\"*\"];\nfunction MatSortHeader_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"@arrowPosition.start\", function MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_start_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = true);\n    })(\"@arrowPosition.done\", function MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_done_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = false);\n    });\n    i0.ɵɵelement(1, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4);\n    i0.ɵɵelement(3, \"div\", 5)(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@arrowOpacity\", ctx_r1._getArrowViewState())(\"@arrowPosition\", ctx_r1._getArrowViewState())(\"@allowChildren\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@indicator\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@leftPointer\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@rightPointer\", ctx_r1._getArrowDirectionState());\n  }\n}\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nlet MatSort = /*#__PURE__*/(() => {\n  class MatSort {\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n      return this._direction;\n    }\n    set direction(direction) {\n      if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getSortInvalidDirectionError(direction);\n      }\n      this._direction = direction;\n    }\n    constructor(_defaultOptions) {\n      this._defaultOptions = _defaultOptions;\n      this._initializedStream = new ReplaySubject(1);\n      /** Collection of all registered sortables that this directive manages. */\n      this.sortables = new Map();\n      /** Used to notify any child components listening to state changes. */\n      this._stateChanges = new Subject();\n      /**\n       * The direction to set when an MatSortable is initially sorted.\n       * May be overridden by the MatSortable's sort start.\n       */\n      this.start = 'asc';\n      this._direction = '';\n      /** Whether the sortable is disabled. */\n      this.disabled = false;\n      /** Event emitted when the user changes either the active sort or sort direction. */\n      this.sortChange = new EventEmitter();\n      /** Emits when the paginator is initialized. */\n      this.initialized = this._initializedStream;\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (!sortable.id) {\n          throw getSortHeaderMissingIdError();\n        }\n        if (this.sortables.has(sortable.id)) {\n          throw getSortDuplicateSortableIdError(sortable.id);\n        }\n      }\n      this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n      this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n      if (this.active != sortable.id) {\n        this.active = sortable.id;\n        this.direction = sortable.start ? sortable.start : this.start;\n      } else {\n        this.direction = this.getNextSortDirection(sortable);\n      }\n      this.sortChange.emit({\n        active: this.active,\n        direction: this.direction\n      });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n      if (!sortable) {\n        return '';\n      }\n      // Get the sort direction cycle with the potential sortable overrides.\n      const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n      let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n      // Get and return the next direction in the cycle\n      let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n      if (nextDirectionIndex >= sortDirectionCycle.length) {\n        nextDirectionIndex = 0;\n      }\n      return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n      this._initializedStream.next();\n    }\n    ngOnChanges() {\n      this._stateChanges.next();\n    }\n    ngOnDestroy() {\n      this._stateChanges.complete();\n      this._initializedStream.complete();\n    }\n    static {\n      this.ɵfac = function MatSort_Factory(t) {\n        return new (t || MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatSort,\n        selectors: [[\"\", \"matSort\", \"\"]],\n        hostAttrs: [1, \"mat-sort\"],\n        inputs: {\n          active: [i0.ɵɵInputFlags.None, \"matSortActive\", \"active\"],\n          start: [i0.ɵɵInputFlags.None, \"matSortStart\", \"start\"],\n          direction: [i0.ɵɵInputFlags.None, \"matSortDirection\", \"direction\"],\n          disableClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matSortDisableClear\", \"disableClear\", booleanAttribute],\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matSortDisabled\", \"disabled\", booleanAttribute]\n        },\n        outputs: {\n          sortChange: \"matSortChange\"\n        },\n        exportAs: [\"matSort\"],\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return MatSort;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n  /** Animation that moves the sort indicator. */\n  indicator: /*#__PURE__*/trigger('indicator', [/*#__PURE__*/state('active-asc, asc', /*#__PURE__*/style({\n    transform: 'translateY(0px)'\n  })),\n  /*#__PURE__*/\n  // 10px is the height of the sort indicator, minus the width of the pointers\n  state('active-desc, desc', /*#__PURE__*/style({\n    transform: 'translateY(10px)'\n  })), /*#__PURE__*/transition('active-asc <=> active-desc', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: /*#__PURE__*/trigger('leftPointer', [/*#__PURE__*/state('active-asc, asc', /*#__PURE__*/style({\n    transform: 'rotate(-45deg)'\n  })), /*#__PURE__*/state('active-desc, desc', /*#__PURE__*/style({\n    transform: 'rotate(45deg)'\n  })), /*#__PURE__*/transition('active-asc <=> active-desc', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: /*#__PURE__*/trigger('rightPointer', [/*#__PURE__*/state('active-asc, asc', /*#__PURE__*/style({\n    transform: 'rotate(45deg)'\n  })), /*#__PURE__*/state('active-desc, desc', /*#__PURE__*/style({\n    transform: 'rotate(-45deg)'\n  })), /*#__PURE__*/transition('active-asc <=> active-desc', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: /*#__PURE__*/trigger('arrowOpacity', [/*#__PURE__*/state('desc-to-active, asc-to-active, active', /*#__PURE__*/style({\n    opacity: 1\n  })), /*#__PURE__*/state('desc-to-hint, asc-to-hint, hint', /*#__PURE__*/style({\n    opacity: 0.54\n  })), /*#__PURE__*/state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', /*#__PURE__*/style({\n    opacity: 0\n  })),\n  /*#__PURE__*/\n  // Transition between all states except for immediate transitions\n  transition('* => asc, * => desc, * => active, * => hint, * => void', /*#__PURE__*/animate('0ms')), /*#__PURE__*/transition('* <=> *', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION))]),\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: /*#__PURE__*/trigger('arrowPosition', [\n  /*#__PURE__*/\n  // Hidden Above => Hint Center\n  transition('* => desc-to-hint, * => desc-to-active', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION, /*#__PURE__*/keyframes([/*#__PURE__*/style({\n    transform: 'translateY(-25%)'\n  }), /*#__PURE__*/style({\n    transform: 'translateY(0)'\n  })]))),\n  /*#__PURE__*/\n  // Hint Center => Hidden Below\n  transition('* => hint-to-desc, * => active-to-desc', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION, /*#__PURE__*/keyframes([/*#__PURE__*/style({\n    transform: 'translateY(0)'\n  }), /*#__PURE__*/style({\n    transform: 'translateY(25%)'\n  })]))),\n  /*#__PURE__*/\n  // Hidden Below => Hint Center\n  transition('* => asc-to-hint, * => asc-to-active', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION, /*#__PURE__*/keyframes([/*#__PURE__*/style({\n    transform: 'translateY(25%)'\n  }), /*#__PURE__*/style({\n    transform: 'translateY(0)'\n  })]))),\n  /*#__PURE__*/\n  // Hint Center => Hidden Above\n  transition('* => hint-to-asc, * => active-to-asc', /*#__PURE__*/animate(SORT_ANIMATION_TRANSITION, /*#__PURE__*/keyframes([/*#__PURE__*/style({\n    transform: 'translateY(0)'\n  }), /*#__PURE__*/style({\n    transform: 'translateY(-25%)'\n  })]))), /*#__PURE__*/state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', /*#__PURE__*/style({\n    transform: 'translateY(0)'\n  })), /*#__PURE__*/state('hint-to-desc, active-to-desc, desc', /*#__PURE__*/style({\n    transform: 'translateY(-25%)'\n  })), /*#__PURE__*/state('hint-to-asc, active-to-asc, asc', /*#__PURE__*/style({\n    transform: 'translateY(25%)'\n  }))]),\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: /*#__PURE__*/trigger('allowChildren', [/*#__PURE__*/transition('* <=> *', [/*#__PURE__*/query('@*', /*#__PURE__*/animateChild(), {\n    optional: true\n  })])])\n};\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nlet MatSortHeaderIntl = /*#__PURE__*/(() => {\n  class MatSortHeaderIntl {\n    constructor() {\n      /**\n       * Stream that emits whenever the labels here are changed. Use this to notify\n       * components if the labels have changed after initialization.\n       */\n      this.changes = new Subject();\n    }\n    static {\n      this.ɵfac = function MatSortHeaderIntl_Factory(t) {\n        return new (t || MatSortHeaderIntl)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: MatSortHeaderIntl,\n        factory: MatSortHeaderIntl.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MatSortHeaderIntl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nlet MatSortHeader = /*#__PURE__*/(() => {\n  class MatSortHeader {\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n      return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n      this._updateSortActionDescription(value);\n    }\n    constructor(\n    /**\n     * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n     * @breaking-change 13.0.0\n     */\n    _intl, _changeDetectorRef,\n    // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n    // tslint:disable-next-line: lightweight-tokens\n    _sort, _columnDef, _focusMonitor, _elementRef, /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n    _ariaDescriber, defaultOptions) {\n      this._intl = _intl;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._sort = _sort;\n      this._columnDef = _columnDef;\n      this._focusMonitor = _focusMonitor;\n      this._elementRef = _elementRef;\n      this._ariaDescriber = _ariaDescriber;\n      /**\n       * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n       * provide an affordance that the header is sortable by showing on focus and hover.\n       */\n      this._showIndicatorHint = false;\n      /**\n       * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n       * position through the animation. If animations are currently disabled, the fromState is removed\n       * so that there is no animation displayed.\n       */\n      this._viewState = {};\n      /** The direction the arrow should be facing according to the current state. */\n      this._arrowDirection = '';\n      /**\n       * Whether the view state animation should show the transition between the `from` and `to` states.\n       */\n      this._disableViewStateAnimation = false;\n      /** Sets the position of the arrow that displays when sorted. */\n      this.arrowPosition = 'after';\n      /** whether the sort header is disabled. */\n      this.disabled = false;\n      // Default the action description to \"Sort\" because it's better than nothing.\n      // Without a description, the button's label comes from the sort header text content,\n      // which doesn't give any indication that it performs a sorting operation.\n      this._sortActionDescription = 'Sort';\n      // Note that we use a string token for the `_columnDef`, because the value is provided both by\n      // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n      // and we want to avoid having the sort header depending on the CDK table because\n      // of this single reference.\n      if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getSortHeaderNotContainedWithinSortError();\n      }\n      if (defaultOptions?.arrowPosition) {\n        this.arrowPosition = defaultOptions?.arrowPosition;\n      }\n      this._handleStateChanges();\n    }\n    ngOnInit() {\n      if (!this.id && this._columnDef) {\n        this.id = this._columnDef.name;\n      }\n      // Initialize the direction of the arrow and set the view state to be immediately that state.\n      this._updateArrowDirection();\n      this._setAnimationTransitionState({\n        toState: this._isSorted() ? 'active' : this._arrowDirection\n      });\n      this._sort.register(this);\n      this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n      this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n      // We use the focus monitor because we also want to style\n      // things differently based on the focus origin.\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n        const newState = !!origin;\n        if (newState !== this._showIndicatorHint) {\n          this._setIndicatorHintVisible(newState);\n          this._changeDetectorRef.markForCheck();\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._sort.deregister(this);\n      this._rerenderSubscription.unsubscribe();\n      if (this._sortButton) {\n        this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      }\n    }\n    /**\n     * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n     * user showing what the active sort will become. If set to false, the arrow will fade away.\n     */\n    _setIndicatorHintVisible(visible) {\n      // No-op if the sort header is disabled - should not make the hint visible.\n      if (this._isDisabled() && visible) {\n        return;\n      }\n      this._showIndicatorHint = visible;\n      if (!this._isSorted()) {\n        this._updateArrowDirection();\n        if (this._showIndicatorHint) {\n          this._setAnimationTransitionState({\n            fromState: this._arrowDirection,\n            toState: 'hint'\n          });\n        } else {\n          this._setAnimationTransitionState({\n            fromState: 'hint',\n            toState: this._arrowDirection\n          });\n        }\n      }\n    }\n    /**\n     * Sets the animation transition view state for the arrow's position and opacity. If the\n     * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n     * no animation appears.\n     */\n    _setAnimationTransitionState(viewState) {\n      this._viewState = viewState || {};\n      // If the animation for arrow position state (opacity/translation) should be disabled,\n      // remove the fromState so that it jumps right to the toState.\n      if (this._disableViewStateAnimation) {\n        this._viewState = {\n          toState: viewState.toState\n        };\n      }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n      this._sort.sort(this);\n      // Do not show the animation if the header was already shown in the right position.\n      if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n        this._disableViewStateAnimation = true;\n      }\n    }\n    _handleClick() {\n      if (!this._isDisabled()) {\n        this._sort.sort(this);\n      }\n    }\n    _handleKeydown(event) {\n      if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n        event.preventDefault();\n        this._toggleOnInteraction();\n      }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n      return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n    }\n    /** Returns the animation state for the arrow direction (indicator and pointers). */\n    _getArrowDirectionState() {\n      return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n    }\n    /** Returns the arrow position state (opacity, translation). */\n    _getArrowViewState() {\n      const fromState = this._viewState.fromState;\n      return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n    }\n    /**\n     * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n     * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n     * active sorted direction. The reason this is updated through a function is because the direction\n     * should only be changed at specific times - when deactivated but the hint is displayed and when\n     * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n     * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n     * preserving its direction, even though the next sort direction is actually different and should\n     * only be changed once the arrow displays again (hint or activation).\n     */\n    _updateArrowDirection() {\n      this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n    }\n    _isDisabled() {\n      return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n      if (!this._isSorted()) {\n        return 'none';\n      }\n      return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n      return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n      // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n      // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n      // for every *cell* in the table, creating a lot of unnecessary noise.\n      // If _sortButton is undefined, the component hasn't been initialized yet so there's\n      // nothing to update in the DOM.\n      if (this._sortButton) {\n        // removeDescription will no-op if there is no existing message.\n        // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n        this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n        this._ariaDescriber?.describe(this._sortButton, newDescription);\n      }\n      this._sortActionDescription = newDescription;\n    }\n    /** Handles changes in the sorting state. */\n    _handleStateChanges() {\n      this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n        if (this._isSorted()) {\n          this._updateArrowDirection();\n          // Do not show the animation if the header was already shown in the right position.\n          if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n            this._disableViewStateAnimation = true;\n          }\n          this._setAnimationTransitionState({\n            fromState: this._arrowDirection,\n            toState: 'active'\n          });\n          this._showIndicatorHint = false;\n        }\n        // If this header was recently active and now no longer sorted, animate away the arrow.\n        if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n          this._disableViewStateAnimation = false;\n          this._setAnimationTransitionState({\n            fromState: 'active',\n            toState: this._arrowDirection\n          });\n        }\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    static {\n      this.ɵfac = function MatSortHeader_Factory(t) {\n        return new (t || MatSortHeader)(i0.ɵɵdirectiveInject(MatSortHeaderIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MatSort, 8), i0.ɵɵdirectiveInject('MAT_SORT_HEADER_COLUMN_DEF', 8), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.AriaDescriber, 8), i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatSortHeader,\n        selectors: [[\"\", \"mat-sort-header\", \"\"]],\n        hostAttrs: [1, \"mat-sort-header\"],\n        hostVars: 3,\n        hostBindings: function MatSortHeader_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function MatSortHeader_click_HostBindingHandler() {\n              return ctx._handleClick();\n            })(\"keydown\", function MatSortHeader_keydown_HostBindingHandler($event) {\n              return ctx._handleKeydown($event);\n            })(\"mouseenter\", function MatSortHeader_mouseenter_HostBindingHandler() {\n              return ctx._setIndicatorHintVisible(true);\n            })(\"mouseleave\", function MatSortHeader_mouseleave_HostBindingHandler() {\n              return ctx._setIndicatorHintVisible(false);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n            i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n          }\n        },\n        inputs: {\n          id: [i0.ɵɵInputFlags.None, \"mat-sort-header\", \"id\"],\n          arrowPosition: \"arrowPosition\",\n          start: \"start\",\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n          sortActionDescription: \"sortActionDescription\",\n          disableClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableClear\", \"disableClear\", booleanAttribute]\n        },\n        exportAs: [\"matSortHeader\"],\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n        attrs: _c0,\n        ngContentSelectors: _c1,\n        decls: 4,\n        vars: 7,\n        consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [1, \"mat-sort-header-arrow\"], [1, \"mat-sort-header-stem\"], [1, \"mat-sort-header-indicator\"], [1, \"mat-sort-header-pointer-left\"], [1, \"mat-sort-header-pointer-right\"], [1, \"mat-sort-header-pointer-middle\"]],\n        template: function MatSortHeader_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵprojection(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(3, MatSortHeader_Conditional_3_Template, 6, 6, \"div\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\");\n            i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵconditional(3, ctx._renderArrow() ? 3 : -1);\n          }\n        },\n        styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;color:var(--mat-sort-arrow-color);opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"],\n        encapsulation: 2,\n        data: {\n          animation: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren]\n        },\n        changeDetection: 0\n      });\n    }\n  }\n  return MatSortHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSortModule = /*#__PURE__*/(() => {\n  class MatSortModule {\n    static {\n      this.ɵfac = function MatSortModule_Factory(t) {\n        return new (t || MatSortModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatSortModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n        imports: [MatCommonModule]\n      });\n    }\n  }\n  return MatSortModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n//# sourceMappingURL=sort.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}