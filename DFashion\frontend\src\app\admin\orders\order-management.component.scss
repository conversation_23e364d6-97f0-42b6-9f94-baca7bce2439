.order-management {
  padding: 1rem;
}

.filters-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  align-items: center;

  mat-form-field {
    min-width: 200px;
  }
}

.table-container {
  overflow-x: auto;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 0.25rem;
}

.order-date {
  font-size: 0.875rem;
  color: #666;
}

.customer-info {
  .customer-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }

  .customer-email {
    font-size: 0.875rem;
    color: #666;
  }
}

.items-info {
  .items-count {
    font-size: 0.875rem;
    color: #666;
  }
}

.amount-cell {
  .total-amount {
    font-weight: 600;
    color: #2e7d32;
    display: block;
    margin-bottom: 0.25rem;
  }

  .payment-method {
    font-size: 0.75rem;
    color: #666;
    text-transform: uppercase;
  }
}

.status-cell {
  .status-chip {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    margin-bottom: 0.25rem;
  }

  .payment-status {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
  }
}

.actions-cell {
  display: flex;
  gap: 0.25rem;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #666;

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    margin: 1rem 0 0.5rem 0;
    font-weight: 400;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

@media (max-width: 768px) {
  .filters-section {
    flex-direction: column;
    align-items: stretch;

    mat-form-field {
      min-width: auto;
      width: 100%;
    }
  }

  .customer-info,
  .amount-cell,
  .status-cell {
    font-size: 0.875rem;
  }
}
