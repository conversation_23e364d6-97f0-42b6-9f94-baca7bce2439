// Payment Modal Styles
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.payment-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Modal Header
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: #f3f4f6;
      color: #374151;
    }
  }
}

// Order Summary
.order-summary {
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
  }

  .amount-display {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .label {
      font-size: 14px;
      color: #6b7280;
    }

    .amount {
      font-size: 18px;
      font-weight: 700;
      color: #667eea;
    }
  }
}

// Payment Methods
.payment-methods {
  padding: 24px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
  }
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(.disabled) {
    border-color: #667eea;
    background: #f8fafc;
  }

  &.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .payment-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    border-radius: 12px;
    margin-right: 16px;
    font-size: 20px;
  }

  .payment-info {
    flex: 1;

    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #6b7280;
    }
  }

  .payment-radio {
    input[type="radio"] {
      width: 20px;
      height: 20px;
      margin: 0;
      accent-color: #667eea;
    }
  }
}

// Payment Details
.payment-details {
  padding: 0 24px 24px;

  .selected-method-info {
    background: #f8fafc;
    border-radius: 12px;
    padding: 16px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }

    .method-details {
      p {
        margin: 8px 0;
        font-size: 13px;
        color: #6b7280;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #667eea;
          width: 16px;
        }
      }
    }
  }
}

// Modal Actions
.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;

  .cancel-btn, .proceed-btn {
    flex: 1;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;

    &:hover:not(:disabled) {
      background: #e5e7eb;
    }
  }

  .proceed-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    &.processing {
      background: #9ca3af;
    }
  }
}

// Security Notice
.security-notice {
  padding: 16px 24px;
  background: #f0fdf4;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #166534;

  i {
    color: #16a34a;
  }
}

// Processing Overlay
.processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.processing-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  max-width: 300px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Mobile Responsive
@media (max-width: 768px) {
  .payment-modal {
    width: 95%;
    margin: 20px;
    max-height: 85vh;
  }

  .modal-header, .order-summary, .payment-methods, .modal-actions {
    padding-left: 16px;
    padding-right: 16px;
  }

  .payment-option {
    padding: 12px;

    .payment-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      font-size: 18px;
    }

    .payment-info {
      h4 {
        font-size: 14px;
      }

      p {
        font-size: 12px;
      }
    }
  }

  .modal-actions {
    flex-direction: column;

    .cancel-btn, .proceed-btn {
      padding: 14px 24px;
    }
  }
}
