{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/error-handler.service\";\nimport * as i2 from \"@angular/common\";\nfunction ErrorDisplayComponent_div_0_div_1_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"pre\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, error_r3.details));\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_0_div_1_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const error_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleDetails(error_r3.id));\n    });\n    i0.ɵɵelement(2, \"i\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ErrorDisplayComponent_div_0_div_1_div_9_div_4_Template, 4, 3, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fa-chevron-down\", !ctx_r3.expandedErrors[error_r3.id])(\"fa-chevron-up\", ctx_r3.expandedErrors[error_r3.id]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.expandedErrors[error_r3.id] ? \"Hide\" : \"Show\", \" Details \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.expandedErrors[error_r3.id]);\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_0_div_1_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const error_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onRetry.emit(error_r3));\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"small\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Context: \", error_r3.context, \"\");\n  }\n}\nfunction ErrorDisplayComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"h4\", 9);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ErrorDisplayComponent_div_0_div_1_div_9_Template, 5, 6, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12);\n    i0.ɵɵtemplate(11, ErrorDisplayComponent_div_0_div_1_button_11_Template, 3, 0, \"button\", 13);\n    i0.ɵɵelementStart(12, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_0_div_1_Template_button_click_12_listener() {\n      const error_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.dismissError(error_r3.id));\n    });\n    i0.ɵɵelement(13, \"i\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(14, ErrorDisplayComponent_div_0_div_1_div_14_Template, 3, 1, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.getErrorClass(error_r3));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getErrorIcon(error_r3));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorTitle(error_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(error_r3.message);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showDetails && error_r3.details);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", error_r3.retryable);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", error_r3.context);\n  }\n}\nfunction ErrorDisplayComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, ErrorDisplayComponent_div_0_div_1_Template, 15, 9, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.globalErrors);\n  }\n}\nfunction ErrorDisplayComponent_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onRetry.emit(ctx_r3.inlineError));\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorDisplayComponent_div_1_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_1_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDismiss.emit());\n    });\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorDisplayComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"p\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 12);\n    i0.ɵɵtemplate(8, ErrorDisplayComponent_div_1_button_8_Template, 3, 0, \"button\", 13)(9, ErrorDisplayComponent_div_1_button_9_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.getErrorClass(ctx_r3.inlineError));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getErrorIcon(ctx_r3.inlineError));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.inlineError.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.inlineError.retryable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.dismissible);\n  }\n}\nfunction ErrorDisplayComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ErrorDisplayComponent_div_2_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.hideToast());\n    });\n    i0.ɵɵelement(7, \"i\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.getErrorClass(ctx_r3.toastError));\n    i0.ɵɵproperty(\"@slideIn\", undefined);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getErrorIcon(ctx_r3.toastError));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.toastError.message, \" \");\n  }\n}\nexport let ErrorDisplayComponent = /*#__PURE__*/(() => {\n  class ErrorDisplayComponent {\n    constructor(errorHandlerService) {\n      this.errorHandlerService = errorHandlerService;\n      this.showGlobalErrors = false;\n      this.dismissible = true;\n      this.showDetails = false;\n      this.showToast = false;\n      this.autoHideToast = true;\n      this.toastDuration = 5000;\n      this.onRetry = new EventEmitter();\n      this.onDismiss = new EventEmitter();\n      this.globalErrors = [];\n      this.expandedErrors = {};\n      this.subscription = new Subscription();\n    }\n    ngOnInit() {\n      if (this.showGlobalErrors) {\n        this.subscription.add(this.errorHandlerService.errorState$.subscribe(state => {\n          this.globalErrors = state.errors.filter(error => error.userFriendly);\n        }));\n      }\n      if (this.showToast && this.toastError && this.autoHideToast) {\n        this.scheduleToastHide();\n      }\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n      if (this.toastTimeout) {\n        clearTimeout(this.toastTimeout);\n      }\n    }\n    getErrorClass(error) {\n      return error.type;\n    }\n    getErrorIcon(error) {\n      const icons = {\n        network: 'fas fa-wifi',\n        validation: 'fas fa-exclamation-triangle',\n        authentication: 'fas fa-lock',\n        authorization: 'fas fa-ban',\n        server: 'fas fa-server',\n        client: 'fas fa-bug',\n        unknown: 'fas fa-question-circle'\n      };\n      return icons[error.type] || icons.unknown;\n    }\n    getErrorTitle(error) {\n      const titles = {\n        network: 'Connection Error',\n        validation: 'Validation Error',\n        authentication: 'Authentication Required',\n        authorization: 'Access Denied',\n        server: 'Server Error',\n        client: 'Application Error',\n        unknown: 'Unexpected Error'\n      };\n      return titles[error.type] || titles.unknown;\n    }\n    toggleDetails(errorId) {\n      this.expandedErrors[errorId] = !this.expandedErrors[errorId];\n    }\n    dismissError(errorId) {\n      this.errorHandlerService.clearError(errorId);\n    }\n    hideToast() {\n      this.showToast = false;\n      if (this.toastTimeout) {\n        clearTimeout(this.toastTimeout);\n      }\n    }\n    scheduleToastHide() {\n      this.toastTimeout = window.setTimeout(() => {\n        this.hideToast();\n      }, this.toastDuration);\n    }\n    static {\n      this.ɵfac = function ErrorDisplayComponent_Factory(t) {\n        return new (t || ErrorDisplayComponent)(i0.ɵɵdirectiveInject(i1.ErrorHandlerService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ErrorDisplayComponent,\n        selectors: [[\"app-error-display\"]],\n        inputs: {\n          showGlobalErrors: \"showGlobalErrors\",\n          inlineError: \"inlineError\",\n          dismissible: \"dismissible\",\n          showDetails: \"showDetails\",\n          showToast: \"showToast\",\n          toastError: \"toastError\",\n          autoHideToast: \"autoHideToast\",\n          toastDuration: \"toastDuration\"\n        },\n        outputs: {\n          onRetry: \"onRetry\",\n          onDismiss: \"onDismiss\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 3,\n        consts: [[\"class\", \"error-banner\", 4, \"ngIf\"], [\"class\", \"inline-error\", 3, \"class\", 4, \"ngIf\"], [\"class\", \"error-toast\", 3, \"class\", 4, \"ngIf\"], [1, \"error-banner\"], [\"class\", \"error-item\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-item\"], [1, \"error-content\"], [1, \"error-icon\"], [1, \"error-details\"], [1, \"error-title\"], [1, \"error-message\"], [\"class\", \"error-extra\", 4, \"ngIf\"], [1, \"error-actions\"], [\"class\", \"btn-retry\", 3, \"click\", 4, \"ngIf\"], [1, \"btn-dismiss\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"class\", \"error-context\", 4, \"ngIf\"], [1, \"error-extra\"], [1, \"toggle-details\", 3, \"click\"], [1, \"fas\"], [\"class\", \"error-details-content\", 4, \"ngIf\"], [1, \"error-details-content\"], [1, \"btn-retry\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"error-context\"], [1, \"inline-error\"], [\"class\", \"btn-dismiss\", 3, \"click\", 4, \"ngIf\"], [1, \"error-toast\"], [1, \"toast-content\"], [1, \"toast-icon\"], [1, \"toast-message\"], [1, \"toast-close\", 3, \"click\"]],\n        template: function ErrorDisplayComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ErrorDisplayComponent_div_0_Template, 2, 1, \"div\", 0)(1, ErrorDisplayComponent_div_1_Template, 10, 7, \"div\", 1)(2, ErrorDisplayComponent_div_2_Template, 8, 6, \"div\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.showGlobalErrors && ctx.globalErrors.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.inlineError);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showToast && ctx.toastError);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.JsonPipe],\n        styles: [\".error-banner[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;z-index:1000;background:#dc3545;color:#fff;padding:1rem;box-shadow:0 2px 10px #0003}.error-item[_ngcontent-%COMP%]{margin-bottom:1rem}.error-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.error-item.network[_ngcontent-%COMP%]{background:#fd7e14}.error-item.validation[_ngcontent-%COMP%]{background:#ffc107;color:#212529}.error-item.authentication[_ngcontent-%COMP%]{background:#dc3545}.error-item.authorization[_ngcontent-%COMP%]{background:#6f42c1}.error-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem}.error-icon[_ngcontent-%COMP%]{font-size:1.25rem;margin-top:.25rem}.error-details[_ngcontent-%COMP%]{flex:1}.error-title[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;margin:0 0 .5rem}.error-message[_ngcontent-%COMP%]{margin:0 0 .5rem;line-height:1.4}.error-extra[_ngcontent-%COMP%]{margin-top:.5rem}.toggle-details[_ngcontent-%COMP%]{background:none;border:none;color:inherit;cursor:pointer;font-size:.875rem;display:flex;align-items:center;gap:.25rem}.toggle-details[_ngcontent-%COMP%]:hover{text-decoration:underline}.error-details-content[_ngcontent-%COMP%]{margin-top:.5rem;padding:.5rem;background:#0000001a;border-radius:4px;font-size:.75rem;max-height:200px;overflow-y:auto}.error-details-content[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{margin:0;white-space:pre-wrap}.error-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.btn-retry[_ngcontent-%COMP%], .btn-dismiss[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.3);color:inherit;padding:.5rem;border-radius:4px;cursor:pointer;font-size:.875rem;display:flex;align-items:center;gap:.25rem;transition:all .2s ease}.btn-retry[_ngcontent-%COMP%]:hover, .btn-dismiss[_ngcontent-%COMP%]:hover{background:#ffffff4d}.error-context[_ngcontent-%COMP%]{margin-top:.5rem;opacity:.8;font-size:.75rem}.inline-error[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb;border-radius:8px;padding:1rem;margin:1rem 0}.inline-error.network[_ngcontent-%COMP%]{background:#ffeaa7;color:#856404;border-color:#ffd93d}.inline-error.validation[_ngcontent-%COMP%]{background:#fff3cd;color:#856404;border-color:#ffeaa7}.inline-error.authentication[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24;border-color:#f5c6cb}.error-toast[_ngcontent-%COMP%]{position:fixed;top:20px;right:20px;z-index:1100;background:#dc3545;color:#fff;border-radius:8px;box-shadow:0 4px 20px #0000004d;max-width:400px;animation:_ngcontent-%COMP%_slideIn .3s ease}.toast-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem}.toast-icon[_ngcontent-%COMP%]{font-size:1.25rem}.toast-message[_ngcontent-%COMP%]{flex:1;font-size:.9rem}.toast-close[_ngcontent-%COMP%]{background:none;border:none;color:inherit;cursor:pointer;padding:.25rem;border-radius:4px}.toast-close[_ngcontent-%COMP%]:hover{background:#fff3}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translate(100%);opacity:0}to{transform:translate(0);opacity:1}}@media (max-width: 768px){.error-banner[_ngcontent-%COMP%]{padding:.75rem}.error-content[_ngcontent-%COMP%]{flex-direction:column;gap:.75rem}.error-actions[_ngcontent-%COMP%]{flex-direction:row}.error-toast[_ngcontent-%COMP%]{left:10px;right:10px;max-width:none}}\"]\n      });\n    }\n  }\n  return ErrorDisplayComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}