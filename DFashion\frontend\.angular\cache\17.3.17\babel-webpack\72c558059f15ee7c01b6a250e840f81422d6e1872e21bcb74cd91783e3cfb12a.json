{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-be245865.js';\nimport { a as printIonError } from './index-9b0d46f4.js';\n\n/**\n * Used to update a scoped component that uses emulated slots. This fires when\n * content is passed into the slot or when the content inside of a slot changes.\n * This is not needed for components using native slots in the Shadow DOM.\n * @internal\n * @param el The host element to observe\n * @param slotName mutationCallback will fire when nodes on these slot(s) change\n * @param mutationCallback The callback to fire whenever the slotted content changes\n */\nconst createSlotMutationController = (el, slotName, mutationCallback) => {\n  let hostMutationObserver;\n  let slottedContentMutationObserver;\n  if (win !== undefined && 'MutationObserver' in win) {\n    const slots = Array.isArray(slotName) ? slotName : [slotName];\n    hostMutationObserver = new MutationObserver(entries => {\n      for (const entry of entries) {\n        for (const node of entry.addedNodes) {\n          /**\n           * Check to see if the added node\n           *  is our slotted content.\n           */\n          if (node.nodeType === Node.ELEMENT_NODE && slots.includes(node.slot)) {\n            /**\n             * If so, we want to watch the slotted\n             * content itself for changes. This lets us\n             * detect when content inside of the slot changes.\n             */\n            mutationCallback();\n            /**\n             * Adding the listener in an raf\n             * waits until Stencil moves the slotted element\n             * into the correct place in the event that\n             * slotted content is being added.\n             */\n            raf(() => watchForSlotChange(node));\n            return;\n          }\n        }\n      }\n    });\n    hostMutationObserver.observe(el, {\n      childList: true\n    });\n  }\n  /**\n   * Listen for changes inside of the slotted content.\n   * We can listen for subtree changes here to be\n   * informed of text within the slotted content\n   * changing. Doing this on the host is possible\n   * but it is much more expensive to do because\n   * it also listens for changes to the internals\n   * of the component.\n   */\n  const watchForSlotChange = slottedEl => {\n    var _a;\n    if (slottedContentMutationObserver) {\n      slottedContentMutationObserver.disconnect();\n      slottedContentMutationObserver = undefined;\n    }\n    slottedContentMutationObserver = new MutationObserver(entries => {\n      mutationCallback();\n      for (const entry of entries) {\n        for (const node of entry.removedNodes) {\n          /**\n           * If the element was removed then we\n           * need to destroy the MutationObserver\n           * so the element can be garbage collected.\n           */\n          if (node.nodeType === Node.ELEMENT_NODE && node.slot === slotName) {\n            destroySlottedContentObserver();\n          }\n        }\n      }\n    });\n    /**\n     * Listen for changes inside of the element\n     * as well as anything deep in the tree.\n     * We listen on the parentElement so that we can\n     * detect when slotted element itself is removed.\n     */\n    slottedContentMutationObserver.observe((_a = slottedEl.parentElement) !== null && _a !== void 0 ? _a : slottedEl, {\n      subtree: true,\n      childList: true\n    });\n  };\n  const destroy = () => {\n    if (hostMutationObserver) {\n      hostMutationObserver.disconnect();\n      hostMutationObserver = undefined;\n    }\n    destroySlottedContentObserver();\n  };\n  const destroySlottedContentObserver = () => {\n    if (slottedContentMutationObserver) {\n      slottedContentMutationObserver.disconnect();\n      slottedContentMutationObserver = undefined;\n    }\n  };\n  return {\n    destroy\n  };\n};\nconst getCounterText = (value, maxLength, counterFormatter) => {\n  const valueLength = value == null ? 0 : value.toString().length;\n  const defaultCounterText = defaultCounterFormatter(valueLength, maxLength);\n  /**\n   * If developers did not pass a custom formatter,\n   * use the default one.\n   */\n  if (counterFormatter === undefined) {\n    return defaultCounterText;\n  }\n  /**\n   * Otherwise, try to use the custom formatter\n   * and fallback to the default formatter if\n   * there was an error.\n   */\n  try {\n    return counterFormatter(valueLength, maxLength);\n  } catch (e) {\n    printIonError('Exception in provided `counterFormatter`.', e);\n    return defaultCounterText;\n  }\n};\nconst defaultCounterFormatter = (length, maxlength) => {\n  return `${length} / ${maxlength}`;\n};\nexport { createSlotMutationController as c, getCounterText as g };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}