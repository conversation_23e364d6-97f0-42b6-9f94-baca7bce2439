{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MediaService {\n  constructor() {\n    this.mediaErrors = new BehaviorSubject([]);\n    this.mediaErrors$ = this.mediaErrors.asObservable();\n    // Fallback images for different scenarios\n    this.fallbackImages = {\n      user: '/assets/images/default-avatar.svg',\n      product: '/assets/images/default-product.svg',\n      post: '/assets/images/default-post.svg',\n      story: '/assets/images/default-story.svg'\n    };\n    // Backup fallback images (simple colored placeholders)\n    this.backupFallbacks = {\n      user: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo=',\n      product: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',\n      post: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkJGQ0ZEIi8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',\n      story: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkVGM0Y0Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNGQ0E1QTUiLz4KPC9zdmc+'\n    };\n    // Video library - should be loaded from API\n    this.sampleVideos = [];\n    // Broken URL patterns to fix\n    this.brokenUrlPatterns = ['/uploads/stories/images/', '/uploads/stories/videos/', 'sample-videos.com', 'localhost:4200/assets/', 'file://'];\n  }\n  /**\n   * Get a reliable fallback image that always works\n   */\n  getReliableFallback(type = 'post') {\n    return this.backupFallbacks[type];\n  }\n  /**\n   * Check if an image URL is likely to fail\n   */\n  isLikelyToFail(url) {\n    return this.isExternalImageUrl(url) || this.isBrokenUrl(url);\n  }\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url, type = 'post') {\n    if (!url || url.trim() === '') {\n      return this.backupFallbacks[type]; // Use base64 fallback for empty URLs\n    }\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n    // Handle localhost URLs that might be broken\n    if (url.includes('localhost:4200/assets/')) {\n      const assetPath = url.split('localhost:4200')[1];\n      return assetPath;\n    }\n    // For external images that might fail, provide a more reliable fallback\n    if (this.isExternalImageUrl(url)) {\n      // Return the URL but we know it might fail and will fallback gracefully\n      return url;\n    }\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return base64 fallback for invalid URLs\n      return this.backupFallbacks[type];\n    }\n  }\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  fixBrokenUrl(url, type) {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getRandomSampleVideo().url;\n        }\n        if (pattern === 'localhost:4200/assets/') {\n          // Extract the asset path and return it as relative\n          const assetPath = url.split('localhost:4200')[1];\n          return assetPath;\n        }\n        if (pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  getReplacementMediaUrl(originalUrl, type) {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n  /**\n   * Handle image load errors with progressive fallback\n   */\n  handleImageError(event, fallbackType = 'post') {\n    const img = event.target;\n    if (!img) return;\n    const originalSrc = img.src;\n    // First try: Use SVG fallback from assets\n    if (!originalSrc.includes(this.fallbackImages[fallbackType]) && !originalSrc.startsWith('data:')) {\n      img.src = this.fallbackImages[fallbackType];\n      // Only log meaningful errors (not external image failures)\n      if (!this.isExternalImageUrl(originalSrc) && !originalSrc.includes('localhost:4200')) {\n        this.logMediaError({\n          id: originalSrc,\n          type: 'load_error',\n          message: `Failed to load image: ${originalSrc}`,\n          fallbackUrl: this.fallbackImages[fallbackType]\n        });\n      }\n      return;\n    }\n    // Second try: Use base64 backup fallback if SVG also fails\n    if (originalSrc.includes(this.fallbackImages[fallbackType])) {\n      img.src = this.backupFallbacks[fallbackType];\n      // Only warn for local asset failures, not external\n      if (!this.isExternalImageUrl(originalSrc)) {\n        console.warn(`SVG fallback failed, using backup for ${fallbackType}:`, originalSrc);\n      }\n      return;\n    }\n  }\n  /**\n   * Check if URL is an external image (Unsplash, etc.)\n   */\n  isExternalImageUrl(url) {\n    const externalDomains = ['unsplash.com', 'images.unsplash.com', 'picsum.photos', 'via.placeholder.com', 'placehold.it', 'placeholder.com'];\n    return externalDomains.some(domain => url.includes(domain));\n  }\n  /**\n   * Get a random sample video with enhanced metadata\n   */\n  getRandomSampleVideo() {\n    const randomIndex = Math.floor(Math.random() * this.sampleVideos.length);\n    return this.sampleVideos[randomIndex];\n  }\n  /**\n   * Get all sample videos with enhanced metadata\n   */\n  getSampleVideos() {\n    return [...this.sampleVideos];\n  }\n  /**\n   * Get video by content type (for better matching)\n   */\n  getVideoByType(contentType) {\n    const typeMapping = {\n      'fashion': 0,\n      'style': 1,\n      'showcase': 2,\n      'tutorial': 3,\n      'story': 5 // Sintel - Fashion Story\n    };\n    const index = typeMapping[contentType] || 0;\n    return this.sampleVideos[index] || this.sampleVideos[0];\n  }\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url) {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || lowerUrl.includes('video') || lowerUrl.includes('.mp4');\n  }\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl) {\n    // Check if it's one of our sample videos\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    if (sampleVideo) {\n      return sampleVideo.thumbnail;\n    }\n    // For other videos, try to generate thumbnail URL or use fallback\n    return this.fallbackImages.post;\n  }\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl) {\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    return sampleVideo ? sampleVideo.duration : 30; // Default 30 seconds\n  }\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray) {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16 / 9 : 1),\n        size: media.size\n      };\n    });\n  }\n  /**\n   * Add sample videos to existing media array with intelligent content matching\n   */\n  enhanceWithSampleVideos(mediaArray, videoCount = 2, contentHint) {\n    const processedMedia = this.processMediaItems(mediaArray);\n    // Add sample videos if we don't have enough media or if videos are broken\n    const needsVideoEnhancement = processedMedia.length < 3 || processedMedia.some(media => media.type === 'video' && this.isBrokenUrl(media.url));\n    if (needsVideoEnhancement) {\n      const videosToAdd = Math.min(videoCount, this.sampleVideos.length);\n      for (let i = 0; i < videosToAdd; i++) {\n        // Try to match video content to the hint\n        let sampleVideo;\n        if (contentHint) {\n          sampleVideo = this.getVideoByContentHint(contentHint, i);\n        } else {\n          sampleVideo = this.sampleVideos[i];\n        }\n        processedMedia.push({\n          id: `enhanced_video_${i}`,\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnailUrl: sampleVideo.thumbnail,\n          alt: sampleVideo.title || `Enhanced video ${i + 1}`,\n          duration: sampleVideo.duration,\n          aspectRatio: 16 / 9\n        });\n      }\n    }\n    return processedMedia;\n  }\n  /**\n   * Get video based on content hint for better matching\n   */\n  getVideoByContentHint(hint, fallbackIndex = 0) {\n    const lowerHint = hint.toLowerCase();\n    if (lowerHint.includes('fashion') || lowerHint.includes('style')) {\n      return this.getVideoByType('fashion');\n    }\n    if (lowerHint.includes('tutorial') || lowerHint.includes('tips')) {\n      return this.getVideoByType('tutorial');\n    }\n    if (lowerHint.includes('showcase') || lowerHint.includes('collection')) {\n      return this.getVideoByType('showcase');\n    }\n    if (lowerHint.includes('story') || lowerHint.includes('behind')) {\n      return this.getVideoByType('story');\n    }\n    return this.sampleVideos[fallbackIndex] || this.sampleVideos[0];\n  }\n  /**\n   * Check if URL is broken\n   */\n  isBrokenUrl(url) {\n    return this.brokenUrlPatterns.some(pattern => url.includes(pattern));\n  }\n  /**\n   * Log media errors for debugging (with smart filtering)\n   */\n  logMediaError(error) {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n    // Only log to console if it's not an external image failure\n    if (!this.isExternalImageUrl(error.id)) {\n      console.warn('Media Error:', error);\n    }\n  }\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors() {\n    this.mediaErrors.next([]);\n  }\n  /**\n   * Preload media for better performance with graceful error handling\n   */\n  preloadMedia(mediaItems) {\n    const promises = mediaItems.map(media => {\n      return new Promise(resolve => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => {\n            // Only log errors for non-external images\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload image: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => {\n            // Only log errors for non-external videos\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload video: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          video.src = media.url;\n          video.load();\n        } else {\n          resolve(); // Unknown type, just resolve\n        }\n      });\n    });\n    return Promise.all(promises);\n  }\n  static {\n    this.ɵfac = function MediaService_Factory(t) {\n      return new (t || MediaService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MediaService,\n      factory: MediaService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "MediaService", "constructor", "mediaErrors", "mediaErrors$", "asObservable", "fallbackImages", "user", "product", "post", "story", "backupFallbacks", "sampleVideos", "brokenUrlPatterns", "getReliable<PERSON><PERSON>back", "type", "isLikelyToFail", "url", "isExternalImageUrl", "isBrokenUrl", "getSafeImageUrl", "trim", "fixedUrl", "fixBrokenUrl", "includes", "assetPath", "split", "URL", "startsWith", "pattern", "getReplacementMediaUrl", "getRandomSampleVideo", "originalUrl", "urlMappings", "key", "replacementUrl", "Object", "entries", "toLowerCase", "handleImageError", "event", "fallbackType", "img", "target", "originalSrc", "src", "logMediaError", "id", "message", "fallbackUrl", "console", "warn", "externalDomains", "some", "domain", "randomIndex", "Math", "floor", "random", "length", "getSampleVideos", "getVideoByType", "contentType", "typeMapping", "index", "isVideoUrl", "videoExtensions", "lowerUrl", "ext", "getVideoThumbnail", "videoUrl", "sampleVideo", "find", "v", "thumbnail", "getVideoDuration", "duration", "processMediaItems", "mediaArray", "Array", "isArray", "map", "media", "isVideo", "_id", "thumbnailUrl", "undefined", "alt", "aspectRatio", "size", "enhanceWithSampleVideos", "videoCount", "contentHint", "processedMedia", "needsVideoEnhancement", "videosToAdd", "min", "i", "getVideoByContentHint", "push", "title", "hint", "fallbackIndex", "lowerHint", "error", "currentErrors", "value", "next", "clearMediaErrors", "preloadMedia", "mediaItems", "promises", "Promise", "resolve", "Image", "onload", "onerror", "video", "document", "createElement", "onloadeddata", "load", "all", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\media.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\nexport interface MediaItem {\n  id: string;\n  type: 'image' | 'video';\n  url: string;\n  thumbnailUrl?: string;\n  alt?: string;\n  duration?: number; // for videos in seconds\n  aspectRatio?: number;\n  size?: number; // file size in bytes\n}\n\nexport interface MediaError {\n  id: string;\n  type: 'load_error' | 'network_error' | 'format_error';\n  message: string;\n  fallbackUrl?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MediaService {\n  private mediaErrors = new BehaviorSubject<MediaError[]>([]);\n  public mediaErrors$ = this.mediaErrors.asObservable();\n\n  // Fallback images for different scenarios\n  private readonly fallbackImages = {\n    user: '/assets/images/default-avatar.svg',\n    product: '/assets/images/default-product.svg',\n    post: '/assets/images/default-post.svg',\n    story: '/assets/images/default-story.svg'\n  };\n\n  // Backup fallback images (simple colored placeholders)\n  private readonly backupFallbacks = {\n    user: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo=',\n    product: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',\n    post: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkJGQ0ZEIi8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',\n    story: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkVGM0Y0Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNGQ0E1QTUiLz4KPC9zdmc+'\n  };\n\n  // Video library - should be loaded from API\n  private readonly sampleVideos: any[] = [];\n\n  // Broken URL patterns to fix\n  private readonly brokenUrlPatterns = [\n    '/uploads/stories/images/',\n    '/uploads/stories/videos/',\n    'sample-videos.com',\n    'localhost:4200/assets/',\n    'file://'\n  ];\n\n  constructor() {}\n\n  /**\n   * Get a reliable fallback image that always works\n   */\n  getReliableFallback(type: 'user' | 'product' | 'post' | 'story' = 'post'): string {\n    return this.backupFallbacks[type];\n  }\n\n  /**\n   * Check if an image URL is likely to fail\n   */\n  isLikelyToFail(url: string): boolean {\n    return this.isExternalImageUrl(url) || this.isBrokenUrl(url);\n  }\n\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url: string | undefined, type: 'user' | 'product' | 'post' | 'story' = 'post'): string {\n    if (!url || url.trim() === '') {\n      return this.backupFallbacks[type]; // Use base64 fallback for empty URLs\n    }\n\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n\n    // Handle localhost URLs that might be broken\n    if (url.includes('localhost:4200/assets/')) {\n      const assetPath = url.split('localhost:4200')[1];\n      return assetPath;\n    }\n\n    // For external images that might fail, provide a more reliable fallback\n    if (this.isExternalImageUrl(url)) {\n      // Return the URL but we know it might fail and will fallback gracefully\n      return url;\n    }\n\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return base64 fallback for invalid URLs\n      return this.backupFallbacks[type];\n    }\n  }\n\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  private fixBrokenUrl(url: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getRandomSampleVideo().url;\n        }\n        if (pattern === 'localhost:4200/assets/') {\n          // Extract the asset path and return it as relative\n          const assetPath = url.split('localhost:4200')[1];\n          return assetPath;\n        }\n        if (pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  private getReplacementMediaUrl(originalUrl: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings: { [key: string]: string } = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n\n  /**\n   * Handle image load errors with progressive fallback\n   */\n  handleImageError(event: Event, fallbackType: 'user' | 'product' | 'post' | 'story' = 'post'): void {\n    const img = event.target as HTMLImageElement;\n    if (!img) return;\n\n    const originalSrc = img.src;\n\n    // First try: Use SVG fallback from assets\n    if (!originalSrc.includes(this.fallbackImages[fallbackType]) && !originalSrc.startsWith('data:')) {\n      img.src = this.fallbackImages[fallbackType];\n\n      // Only log meaningful errors (not external image failures)\n      if (!this.isExternalImageUrl(originalSrc) && !originalSrc.includes('localhost:4200')) {\n        this.logMediaError({\n          id: originalSrc,\n          type: 'load_error',\n          message: `Failed to load image: ${originalSrc}`,\n          fallbackUrl: this.fallbackImages[fallbackType]\n        });\n      }\n      return;\n    }\n\n    // Second try: Use base64 backup fallback if SVG also fails\n    if (originalSrc.includes(this.fallbackImages[fallbackType])) {\n      img.src = this.backupFallbacks[fallbackType];\n      // Only warn for local asset failures, not external\n      if (!this.isExternalImageUrl(originalSrc)) {\n        console.warn(`SVG fallback failed, using backup for ${fallbackType}:`, originalSrc);\n      }\n      return;\n    }\n  }\n\n  /**\n   * Check if URL is an external image (Unsplash, etc.)\n   */\n  private isExternalImageUrl(url: string): boolean {\n    const externalDomains = [\n      'unsplash.com',\n      'images.unsplash.com',\n      'picsum.photos',\n      'via.placeholder.com',\n      'placehold.it',\n      'placeholder.com'\n    ];\n\n    return externalDomains.some(domain => url.includes(domain));\n  }\n\n  /**\n   * Get a random sample video with enhanced metadata\n   */\n  getRandomSampleVideo(): { url: string; thumbnail: string; duration: number; title?: string; description?: string } {\n    const randomIndex = Math.floor(Math.random() * this.sampleVideos.length);\n    return this.sampleVideos[randomIndex];\n  }\n\n  /**\n   * Get all sample videos with enhanced metadata\n   */\n  getSampleVideos(): { url: string; thumbnail: string; duration: number; title?: string; description?: string }[] {\n    return [...this.sampleVideos];\n  }\n\n  /**\n   * Get video by content type (for better matching)\n   */\n  getVideoByType(contentType: 'fashion' | 'style' | 'showcase' | 'tutorial' | 'story'): { url: string; thumbnail: string; duration: number; title?: string; description?: string } {\n    const typeMapping: { [key: string]: number } = {\n      'fashion': 0, // ForBiggerBlazes - Fashion Showcase\n      'style': 1,   // ForBiggerEscapes - Style Journey\n      'showcase': 2, // ForBiggerFun - Fashion Fun\n      'tutorial': 3, // ForBiggerJoyrides - Style Ride\n      'story': 5    // Sintel - Fashion Story\n    };\n\n    const index = typeMapping[contentType] || 0;\n    return this.sampleVideos[index] || this.sampleVideos[0];\n  }\n\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url: string): boolean {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || \n           lowerUrl.includes('video') || \n           lowerUrl.includes('.mp4');\n  }\n\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl: string): string {\n    // Check if it's one of our sample videos\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    if (sampleVideo) {\n      return sampleVideo.thumbnail;\n    }\n\n    // For other videos, try to generate thumbnail URL or use fallback\n    return this.fallbackImages.post;\n  }\n\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl: string): number {\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    return sampleVideo ? sampleVideo.duration : 30; // Default 30 seconds\n  }\n\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray: any[]): MediaItem[] {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      \n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16/9 : 1),\n        size: media.size\n      };\n    });\n  }\n\n  /**\n   * Add sample videos to existing media array with intelligent content matching\n   */\n  enhanceWithSampleVideos(mediaArray: any[], videoCount: number = 2, contentHint?: string): MediaItem[] {\n    const processedMedia = this.processMediaItems(mediaArray);\n\n    // Add sample videos if we don't have enough media or if videos are broken\n    const needsVideoEnhancement = processedMedia.length < 3 ||\n      processedMedia.some(media => media.type === 'video' && this.isBrokenUrl(media.url));\n\n    if (needsVideoEnhancement) {\n      const videosToAdd = Math.min(videoCount, this.sampleVideos.length);\n\n      for (let i = 0; i < videosToAdd; i++) {\n        // Try to match video content to the hint\n        let sampleVideo;\n        if (contentHint) {\n          sampleVideo = this.getVideoByContentHint(contentHint, i);\n        } else {\n          sampleVideo = this.sampleVideos[i];\n        }\n\n        processedMedia.push({\n          id: `enhanced_video_${i}`,\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnailUrl: sampleVideo.thumbnail,\n          alt: sampleVideo.title || `Enhanced video ${i + 1}`,\n          duration: sampleVideo.duration,\n          aspectRatio: 16/9\n        });\n      }\n    }\n\n    return processedMedia;\n  }\n\n  /**\n   * Get video based on content hint for better matching\n   */\n  private getVideoByContentHint(hint: string, fallbackIndex: number = 0): any {\n    const lowerHint = hint.toLowerCase();\n\n    if (lowerHint.includes('fashion') || lowerHint.includes('style')) {\n      return this.getVideoByType('fashion');\n    }\n    if (lowerHint.includes('tutorial') || lowerHint.includes('tips')) {\n      return this.getVideoByType('tutorial');\n    }\n    if (lowerHint.includes('showcase') || lowerHint.includes('collection')) {\n      return this.getVideoByType('showcase');\n    }\n    if (lowerHint.includes('story') || lowerHint.includes('behind')) {\n      return this.getVideoByType('story');\n    }\n\n    return this.sampleVideos[fallbackIndex] || this.sampleVideos[0];\n  }\n\n  /**\n   * Check if URL is broken\n   */\n  private isBrokenUrl(url: string): boolean {\n    return this.brokenUrlPatterns.some(pattern => url.includes(pattern));\n  }\n\n  /**\n   * Log media errors for debugging (with smart filtering)\n   */\n  private logMediaError(error: MediaError): void {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n\n    // Only log to console if it's not an external image failure\n    if (!this.isExternalImageUrl(error.id)) {\n      console.warn('Media Error:', error);\n    }\n  }\n\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors(): void {\n    this.mediaErrors.next([]);\n  }\n\n  /**\n   * Preload media for better performance with graceful error handling\n   */\n  preloadMedia(mediaItems: MediaItem[]): Promise<void[]> {\n    const promises = mediaItems.map(media => {\n      return new Promise<void>((resolve) => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => {\n            // Only log errors for non-external images\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload image: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => {\n            // Only log errors for non-external videos\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload video: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          video.src = media.url;\n          video.load();\n        } else {\n          resolve(); // Unknown type, just resolve\n        }\n      });\n    });\n\n    return Promise.all(promises);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAuBtC,OAAM,MAAOC,YAAY;EAgCvBC,YAAA;IA/BQ,KAAAC,WAAW,GAAG,IAAIH,eAAe,CAAe,EAAE,CAAC;IACpD,KAAAI,YAAY,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;IAErD;IACiB,KAAAC,cAAc,GAAG;MAChCC,IAAI,EAAE,mCAAmC;MACzCC,OAAO,EAAE,oCAAoC;MAC7CC,IAAI,EAAE,iCAAiC;MACvCC,KAAK,EAAE;KACR;IAED;IACiB,KAAAC,eAAe,GAAG;MACjCJ,IAAI,EAAE,osBAAosB;MAC1sBC,OAAO,EAAE,4uBAA4uB;MACrvBC,IAAI,EAAE,4uBAA4uB;MAClvBC,KAAK,EAAE;KACR;IAED;IACiB,KAAAE,YAAY,GAAU,EAAE;IAEzC;IACiB,KAAAC,iBAAiB,GAAG,CACnC,0BAA0B,EAC1B,0BAA0B,EAC1B,mBAAmB,EACnB,wBAAwB,EACxB,SAAS,CACV;EAEc;EAEf;;;EAGAC,mBAAmBA,CAACC,IAAA,GAA8C,MAAM;IACtE,OAAO,IAAI,CAACJ,eAAe,CAACI,IAAI,CAAC;EACnC;EAEA;;;EAGAC,cAAcA,CAACC,GAAW;IACxB,OAAO,IAAI,CAACC,kBAAkB,CAACD,GAAG,CAAC,IAAI,IAAI,CAACE,WAAW,CAACF,GAAG,CAAC;EAC9D;EAEA;;;EAGAG,eAAeA,CAACH,GAAuB,EAAEF,IAAA,GAA8C,MAAM;IAC3F,IAAI,CAACE,GAAG,IAAIA,GAAG,CAACI,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7B,OAAO,IAAI,CAACV,eAAe,CAACI,IAAI,CAAC,CAAC,CAAC;;IAGrC;IACA,MAAMO,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACN,GAAG,EAAEF,IAAI,CAAC;IAC7C,IAAIO,QAAQ,KAAKL,GAAG,EAAE;MACpB,OAAOK,QAAQ;;IAGjB;IACA,IAAIL,GAAG,CAACO,QAAQ,CAAC,wBAAwB,CAAC,EAAE;MAC1C,MAAMC,SAAS,GAAGR,GAAG,CAACS,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAChD,OAAOD,SAAS;;IAGlB;IACA,IAAI,IAAI,CAACP,kBAAkB,CAACD,GAAG,CAAC,EAAE;MAChC;MACA,OAAOA,GAAG;;IAGZ;IACA,IAAI;MACF,IAAIU,GAAG,CAACV,GAAG,CAAC;MACZ,OAAOA,GAAG;KACX,CAAC,MAAM;MACN;MACA,IAAIA,GAAG,CAACW,UAAU,CAAC,GAAG,CAAC,IAAIX,GAAG,CAACW,UAAU,CAAC,IAAI,CAAC,IAAIX,GAAG,CAACW,UAAU,CAAC,KAAK,CAAC,EAAE;QACxE,OAAOX,GAAG;;MAEZ;MACA,OAAO,IAAI,CAACN,eAAe,CAACI,IAAI,CAAC;;EAErC;EAEA;;;EAGQQ,YAAYA,CAACN,GAAW,EAAEF,IAA2C;IAC3E;IACA,KAAK,MAAMc,OAAO,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC5C,IAAII,GAAG,CAACO,QAAQ,CAACK,OAAO,CAAC,EAAE;QACzB;QACA,IAAIA,OAAO,KAAK,0BAA0B,IAAIA,OAAO,KAAK,0BAA0B,EAAE;UACpF,OAAO,IAAI,CAACC,sBAAsB,CAACb,GAAG,EAAEF,IAAI,CAAC;;QAE/C,IAAIc,OAAO,KAAK,mBAAmB,EAAE;UACnC,OAAO,IAAI,CAACE,oBAAoB,EAAE,CAACd,GAAG;;QAExC,IAAIY,OAAO,KAAK,wBAAwB,EAAE;UACxC;UACA,MAAMJ,SAAS,GAAGR,GAAG,CAACS,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAChD,OAAOD,SAAS;;QAElB,IAAII,OAAO,KAAK,SAAS,EAAE;UACzB,OAAO,IAAI,CAACvB,cAAc,CAACS,IAAI,CAAC;;;;IAItC,OAAOE,GAAG;EACZ;EAEA;;;EAGQa,sBAAsBA,CAACE,WAAmB,EAAEjB,IAA2C;IAC7F;IACA,MAAMkB,WAAW,GAA8B;MAC7C,mBAAmB,EAAE,oEAAoE;MACzF,eAAe,EAAE,oEAAoE;MACrF,oBAAoB,EAAE,oEAAoE;MAC1F,cAAc,EAAE,oEAAoE;MACpF,QAAQ,EAAE;KACX;IAED;IACA,KAAK,MAAM,CAACC,GAAG,EAAEC,cAAc,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;MAC/D,IAAID,WAAW,CAACM,WAAW,EAAE,CAACd,QAAQ,CAACU,GAAG,CAAC,EAAE;QAC3C,OAAOC,cAAc;;;IAIzB;IACA,OAAO,IAAI,CAAC7B,cAAc,CAACS,IAAI,CAAC;EAClC;EAEA;;;EAGAwB,gBAAgBA,CAACC,KAAY,EAAEC,YAAA,GAAsD,MAAM;IACzF,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAA0B;IAC5C,IAAI,CAACD,GAAG,EAAE;IAEV,MAAME,WAAW,GAAGF,GAAG,CAACG,GAAG;IAE3B;IACA,IAAI,CAACD,WAAW,CAACpB,QAAQ,CAAC,IAAI,CAAClB,cAAc,CAACmC,YAAY,CAAC,CAAC,IAAI,CAACG,WAAW,CAAChB,UAAU,CAAC,OAAO,CAAC,EAAE;MAChGc,GAAG,CAACG,GAAG,GAAG,IAAI,CAACvC,cAAc,CAACmC,YAAY,CAAC;MAE3C;MACA,IAAI,CAAC,IAAI,CAACvB,kBAAkB,CAAC0B,WAAW,CAAC,IAAI,CAACA,WAAW,CAACpB,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACpF,IAAI,CAACsB,aAAa,CAAC;UACjBC,EAAE,EAAEH,WAAW;UACf7B,IAAI,EAAE,YAAY;UAClBiC,OAAO,EAAE,yBAAyBJ,WAAW,EAAE;UAC/CK,WAAW,EAAE,IAAI,CAAC3C,cAAc,CAACmC,YAAY;SAC9C,CAAC;;MAEJ;;IAGF;IACA,IAAIG,WAAW,CAACpB,QAAQ,CAAC,IAAI,CAAClB,cAAc,CAACmC,YAAY,CAAC,CAAC,EAAE;MAC3DC,GAAG,CAACG,GAAG,GAAG,IAAI,CAAClC,eAAe,CAAC8B,YAAY,CAAC;MAC5C;MACA,IAAI,CAAC,IAAI,CAACvB,kBAAkB,CAAC0B,WAAW,CAAC,EAAE;QACzCM,OAAO,CAACC,IAAI,CAAC,yCAAyCV,YAAY,GAAG,EAAEG,WAAW,CAAC;;MAErF;;EAEJ;EAEA;;;EAGQ1B,kBAAkBA,CAACD,GAAW;IACpC,MAAMmC,eAAe,GAAG,CACtB,cAAc,EACd,qBAAqB,EACrB,eAAe,EACf,qBAAqB,EACrB,cAAc,EACd,iBAAiB,CAClB;IAED,OAAOA,eAAe,CAACC,IAAI,CAACC,MAAM,IAAIrC,GAAG,CAACO,QAAQ,CAAC8B,MAAM,CAAC,CAAC;EAC7D;EAEA;;;EAGAvB,oBAAoBA,CAAA;IAClB,MAAMwB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAAC9C,YAAY,CAAC+C,MAAM,CAAC;IACxE,OAAO,IAAI,CAAC/C,YAAY,CAAC2C,WAAW,CAAC;EACvC;EAEA;;;EAGAK,eAAeA,CAAA;IACb,OAAO,CAAC,GAAG,IAAI,CAAChD,YAAY,CAAC;EAC/B;EAEA;;;EAGAiD,cAAcA,CAACC,WAAoE;IACjF,MAAMC,WAAW,GAA8B;MAC7C,SAAS,EAAE,CAAC;MACZ,OAAO,EAAE,CAAC;MACV,UAAU,EAAE,CAAC;MACb,UAAU,EAAE,CAAC;MACb,OAAO,EAAE,CAAC,CAAI;KACf;IAED,MAAMC,KAAK,GAAGD,WAAW,CAACD,WAAW,CAAC,IAAI,CAAC;IAC3C,OAAO,IAAI,CAAClD,YAAY,CAACoD,KAAK,CAAC,IAAI,IAAI,CAACpD,YAAY,CAAC,CAAC,CAAC;EACzD;EAEA;;;EAGAqD,UAAUA,CAAChD,GAAW;IACpB,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;IACtB,MAAMiD,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjE,MAAMC,QAAQ,GAAGlD,GAAG,CAACqB,WAAW,EAAE;IAClC,OAAO4B,eAAe,CAACb,IAAI,CAACe,GAAG,IAAID,QAAQ,CAAC3C,QAAQ,CAAC4C,GAAG,CAAC,CAAC,IACnDD,QAAQ,CAAC3C,QAAQ,CAAC,OAAO,CAAC,IAC1B2C,QAAQ,CAAC3C,QAAQ,CAAC,MAAM,CAAC;EAClC;EAEA;;;EAGA6C,iBAAiBA,CAACC,QAAgB;IAChC;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC3D,YAAY,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxD,GAAG,KAAKqD,QAAQ,CAAC;IACnE,IAAIC,WAAW,EAAE;MACf,OAAOA,WAAW,CAACG,SAAS;;IAG9B;IACA,OAAO,IAAI,CAACpE,cAAc,CAACG,IAAI;EACjC;EAEA;;;EAGAkE,gBAAgBA,CAACL,QAAgB;IAC/B,MAAMC,WAAW,GAAG,IAAI,CAAC3D,YAAY,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxD,GAAG,KAAKqD,QAAQ,CAAC;IACnE,OAAOC,WAAW,GAAGA,WAAW,CAACK,QAAQ,GAAG,EAAE,CAAC,CAAC;EAClD;EAEA;;;EAGAC,iBAAiBA,CAACC,UAAiB;IACjC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;;IAGX,OAAOA,UAAU,CAACG,GAAG,CAAC,CAACC,KAAK,EAAElB,KAAK,KAAI;MACrC,MAAMmB,OAAO,GAAG,IAAI,CAAClB,UAAU,CAACiB,KAAK,CAACjE,GAAG,CAAC;MAE1C,OAAO;QACL8B,EAAE,EAAEmC,KAAK,CAACE,GAAG,IAAI,SAASpB,KAAK,EAAE;QACjCjD,IAAI,EAAEoE,OAAO,GAAG,OAAO,GAAG,OAAO;QACjClE,GAAG,EAAE,IAAI,CAACG,eAAe,CAAC8D,KAAK,CAACjE,GAAG,CAAC;QACpCoE,YAAY,EAAEF,OAAO,GAAG,IAAI,CAACd,iBAAiB,CAACa,KAAK,CAACjE,GAAG,CAAC,GAAGqE,SAAS;QACrEC,GAAG,EAAEL,KAAK,CAACK,GAAG,IAAI,EAAE;QACpBX,QAAQ,EAAEO,OAAO,GAAG,IAAI,CAACR,gBAAgB,CAACO,KAAK,CAACjE,GAAG,CAAC,GAAGqE,SAAS;QAChEE,WAAW,EAAEN,KAAK,CAACM,WAAW,KAAKL,OAAO,GAAG,EAAE,GAAC,CAAC,GAAG,CAAC,CAAC;QACtDM,IAAI,EAAEP,KAAK,CAACO;OACb;IACH,CAAC,CAAC;EACJ;EAEA;;;EAGAC,uBAAuBA,CAACZ,UAAiB,EAAEa,UAAA,GAAqB,CAAC,EAAEC,WAAoB;IACrF,MAAMC,cAAc,GAAG,IAAI,CAAChB,iBAAiB,CAACC,UAAU,CAAC;IAEzD;IACA,MAAMgB,qBAAqB,GAAGD,cAAc,CAAClC,MAAM,GAAG,CAAC,IACrDkC,cAAc,CAACxC,IAAI,CAAC6B,KAAK,IAAIA,KAAK,CAACnE,IAAI,KAAK,OAAO,IAAI,IAAI,CAACI,WAAW,CAAC+D,KAAK,CAACjE,GAAG,CAAC,CAAC;IAErF,IAAI6E,qBAAqB,EAAE;MACzB,MAAMC,WAAW,GAAGvC,IAAI,CAACwC,GAAG,CAACL,UAAU,EAAE,IAAI,CAAC/E,YAAY,CAAC+C,MAAM,CAAC;MAElE,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,EAAEE,CAAC,EAAE,EAAE;QACpC;QACA,IAAI1B,WAAW;QACf,IAAIqB,WAAW,EAAE;UACfrB,WAAW,GAAG,IAAI,CAAC2B,qBAAqB,CAACN,WAAW,EAAEK,CAAC,CAAC;SACzD,MAAM;UACL1B,WAAW,GAAG,IAAI,CAAC3D,YAAY,CAACqF,CAAC,CAAC;;QAGpCJ,cAAc,CAACM,IAAI,CAAC;UAClBpD,EAAE,EAAE,kBAAkBkD,CAAC,EAAE;UACzBlF,IAAI,EAAE,OAAO;UACbE,GAAG,EAAEsD,WAAW,CAACtD,GAAG;UACpBoE,YAAY,EAAEd,WAAW,CAACG,SAAS;UACnCa,GAAG,EAAEhB,WAAW,CAAC6B,KAAK,IAAI,kBAAkBH,CAAC,GAAG,CAAC,EAAE;UACnDrB,QAAQ,EAAEL,WAAW,CAACK,QAAQ;UAC9BY,WAAW,EAAE,EAAE,GAAC;SACjB,CAAC;;;IAIN,OAAOK,cAAc;EACvB;EAEA;;;EAGQK,qBAAqBA,CAACG,IAAY,EAAEC,aAAA,GAAwB,CAAC;IACnE,MAAMC,SAAS,GAAGF,IAAI,CAAC/D,WAAW,EAAE;IAEpC,IAAIiE,SAAS,CAAC/E,QAAQ,CAAC,SAAS,CAAC,IAAI+E,SAAS,CAAC/E,QAAQ,CAAC,OAAO,CAAC,EAAE;MAChE,OAAO,IAAI,CAACqC,cAAc,CAAC,SAAS,CAAC;;IAEvC,IAAI0C,SAAS,CAAC/E,QAAQ,CAAC,UAAU,CAAC,IAAI+E,SAAS,CAAC/E,QAAQ,CAAC,MAAM,CAAC,EAAE;MAChE,OAAO,IAAI,CAACqC,cAAc,CAAC,UAAU,CAAC;;IAExC,IAAI0C,SAAS,CAAC/E,QAAQ,CAAC,UAAU,CAAC,IAAI+E,SAAS,CAAC/E,QAAQ,CAAC,YAAY,CAAC,EAAE;MACtE,OAAO,IAAI,CAACqC,cAAc,CAAC,UAAU,CAAC;;IAExC,IAAI0C,SAAS,CAAC/E,QAAQ,CAAC,OAAO,CAAC,IAAI+E,SAAS,CAAC/E,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC/D,OAAO,IAAI,CAACqC,cAAc,CAAC,OAAO,CAAC;;IAGrC,OAAO,IAAI,CAACjD,YAAY,CAAC0F,aAAa,CAAC,IAAI,IAAI,CAAC1F,YAAY,CAAC,CAAC,CAAC;EACjE;EAEA;;;EAGQO,WAAWA,CAACF,GAAW;IAC7B,OAAO,IAAI,CAACJ,iBAAiB,CAACwC,IAAI,CAACxB,OAAO,IAAIZ,GAAG,CAACO,QAAQ,CAACK,OAAO,CAAC,CAAC;EACtE;EAEA;;;EAGQiB,aAAaA,CAAC0D,KAAiB;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACtG,WAAW,CAACuG,KAAK;IAC5C,IAAI,CAACvG,WAAW,CAACwG,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAED,KAAK,CAAC,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAACtF,kBAAkB,CAACsF,KAAK,CAACzD,EAAE,CAAC,EAAE;MACtCG,OAAO,CAACC,IAAI,CAAC,cAAc,EAAEqD,KAAK,CAAC;;EAEvC;EAEA;;;EAGAI,gBAAgBA,CAAA;IACd,IAAI,CAACzG,WAAW,CAACwG,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEA;;;EAGAE,YAAYA,CAACC,UAAuB;IAClC,MAAMC,QAAQ,GAAGD,UAAU,CAAC7B,GAAG,CAACC,KAAK,IAAG;MACtC,OAAO,IAAI8B,OAAO,CAAQC,OAAO,IAAI;QACnC,IAAI/B,KAAK,CAACnE,IAAI,KAAK,OAAO,EAAE;UAC1B,MAAM2B,GAAG,GAAG,IAAIwE,KAAK,EAAE;UACvBxE,GAAG,CAACyE,MAAM,GAAG,MAAMF,OAAO,EAAE;UAC5BvE,GAAG,CAAC0E,OAAO,GAAG,MAAK;YACjB;YACA,IAAI,CAAC,IAAI,CAAClG,kBAAkB,CAACgE,KAAK,CAACjE,GAAG,CAAC,EAAE;cACvCiC,OAAO,CAACC,IAAI,CAAC,4BAA4B+B,KAAK,CAACjE,GAAG,EAAE,CAAC;;YAEvDgG,OAAO,EAAE,CAAC,CAAC;UACb,CAAC;UACDvE,GAAG,CAACG,GAAG,GAAGqC,KAAK,CAACjE,GAAG;SACpB,MAAM,IAAIiE,KAAK,CAACnE,IAAI,KAAK,OAAO,EAAE;UACjC,MAAMsG,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;UAC7CF,KAAK,CAACG,YAAY,GAAG,MAAMP,OAAO,EAAE;UACpCI,KAAK,CAACD,OAAO,GAAG,MAAK;YACnB;YACA,IAAI,CAAC,IAAI,CAAClG,kBAAkB,CAACgE,KAAK,CAACjE,GAAG,CAAC,EAAE;cACvCiC,OAAO,CAACC,IAAI,CAAC,4BAA4B+B,KAAK,CAACjE,GAAG,EAAE,CAAC;;YAEvDgG,OAAO,EAAE,CAAC,CAAC;UACb,CAAC;UACDI,KAAK,CAACxE,GAAG,GAAGqC,KAAK,CAACjE,GAAG;UACrBoG,KAAK,CAACI,IAAI,EAAE;SACb,MAAM;UACLR,OAAO,EAAE,CAAC,CAAC;;MAEf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOD,OAAO,CAACU,GAAG,CAACX,QAAQ,CAAC;EAC9B;;;uBAlZW9G,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAA0H,OAAA,EAAZ1H,YAAY,CAAA2H,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}