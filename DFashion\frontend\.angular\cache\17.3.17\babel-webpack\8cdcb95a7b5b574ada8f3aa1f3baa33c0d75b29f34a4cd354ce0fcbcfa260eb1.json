{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction TrendingProductsComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵelement(3, \"div\", 17)(4, \"div\", 18)(5, \"div\", 17);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_8_div_2_Template, 6, 0, \"div\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingProductsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"i\", 21);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Unable to load trending products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_9_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.retry());\n    });\n    i0.ɵɵelement(8, \"i\", 23);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_div_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 58);\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TrendingProductsComponent_div_10_div_1_div_1_i_3_Template, 1, 0, \"i\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r5 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"#\", i_r5 + 1, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r5 === 0);\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" -\", ctx_r1.getDiscountPercentage(product_r4), \"% \");\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_i_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 60);\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatPrice(product_r4.originalPrice), \" \");\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_div_27_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 66);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r4.rating.average);\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_10_div_1_div_27_i_2_Template, 1, 2, \"i\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 65);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(3, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", product_r4.rating.average, \" (\", ctx_r1.formatNumber(product_r4.rating.count), \") \");\n  }\n}\nfunction TrendingProductsComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_10_div_1_Template_div_click_0_listener() {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r4));\n    });\n    i0.ɵɵtemplate(1, TrendingProductsComponent_div_10_div_1_div_1_Template, 4, 2, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelement(3, \"img\", 29);\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵelement(5, \"i\", 3);\n    i0.ɵɵtext(6, \" TRENDING \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TrendingProductsComponent_div_10_div_1_div_7_Template, 2, 1, \"div\", 31);\n    i0.ɵɵelementStart(8, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_10_div_1_Template_button_click_8_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleWishlist(product_r4, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 34)(11, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_10_div_1_Template_button_click_11_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r4, $event));\n    });\n    i0.ɵɵelement(12, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_10_div_1_Template_button_click_13_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(14, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 39)(16, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_10_div_1_Template_div_click_16_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.viewVendor(product_r4.vendor);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(17, \"img\", 41);\n    i0.ɵɵelementStart(18, \"span\", 42);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingProductsComponent_div_10_div_1_i_20_Template, 1, 0, \"i\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"h3\", 44);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 45)(24, \"span\", 46);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, TrendingProductsComponent_div_10_div_1_span_26_Template, 2, 1, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, TrendingProductsComponent_div_10_div_1_div_27_Template, 5, 4, \"div\", 48);\n    i0.ɵɵelementStart(28, \"div\", 49)(29, \"div\", 50);\n    i0.ɵɵelement(30, \"i\", 51);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 52);\n    i0.ɵɵelement(34, \"i\", 53);\n    i0.ɵɵelementStart(35, \"span\");\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 52);\n    i0.ɵɵelement(38, \"i\", 54);\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"rank-1\", i_r5 === 0)(\"rank-2\", i_r5 === 1)(\"rank-3\", i_r5 === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r5 < 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r4.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r4) > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"src\", product_r4.vendor.avatar, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.vendor.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.vendor.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.vendor.isInfluencer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.rating);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(product_r4.analytics.purchases), \" sold\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatNumber(product_r4.analytics.views));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatNumber(product_r4.analytics.likes));\n  }\n}\nfunction TrendingProductsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, TrendingProductsComponent_div_10_div_1_Template, 41, 21, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.products)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction TrendingProductsComponent_div_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Load More Trending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingProductsComponent_div_11_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵtext(2, \" Loading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingProductsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadMore());\n    });\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_11_span_2_Template, 2, 0, \"span\", 69)(3, TrendingProductsComponent_div_11_span_3_Template, 3, 0, \"span\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n  }\n}\nfunction TrendingProductsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"i\", 3);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No trending products available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Check back later to see what's hot in fashion!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.router.navigate([\"/shop\"]));\n    });\n    i0.ɵɵtext(8, \" Browse All Products \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let TrendingProductsComponent = /*#__PURE__*/(() => {\n  class TrendingProductsComponent {\n    constructor(router, http) {\n      this.router = router;\n      this.http = http;\n      this.products = [];\n      this.isLoading = true;\n      this.error = null;\n      this.currentPage = 1;\n      this.totalPages = 1;\n      this.hasMore = false;\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.loadTrendingProducts();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    loadTrendingProducts(page = 1) {\n      this.isLoading = true;\n      this.error = null;\n      this.subscriptions.push(this.http.get(`${environment.apiUrl}/products/trending?page=${page}&limit=12`).subscribe({\n        next: response => {\n          if (response.success) {\n            if (page === 1) {\n              this.products = response.products || [];\n            } else {\n              this.products = [...this.products, ...(response.products || [])];\n            }\n            // Handle pagination safely\n            if (response.pagination) {\n              this.currentPage = response.pagination.page || page;\n              this.totalPages = response.pagination.pages || 1;\n              this.hasMore = this.currentPage < this.totalPages;\n            } else {\n              this.currentPage = page;\n              this.totalPages = 1;\n              this.hasMore = false;\n            }\n          } else {\n            this.loadFallbackProducts();\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading trending products:', error);\n          if (page === 1) {\n            this.loadFallbackProducts();\n          }\n          this.error = 'Failed to load trending products';\n          this.isLoading = false;\n        }\n      }));\n    }\n    loadFallbackProducts() {\n      this.products = [{\n        _id: '1',\n        name: 'Vintage Denim Jacket',\n        price: 89.99,\n        originalPrice: 129.99,\n        discount: 31,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400',\n          isPrimary: true\n        }],\n        vendor: {\n          _id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',\n          isInfluencer: true\n        },\n        analytics: {\n          views: 25420,\n          likes: 1892,\n          shares: 356,\n          purchases: 534\n        },\n        rating: {\n          average: 4.8,\n          count: 256\n        }\n      }, {\n        _id: '2',\n        name: 'Silk Slip Dress',\n        price: 159.99,\n        originalPrice: 199.99,\n        discount: 20,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400',\n          isPrimary: true\n        }],\n        vendor: {\n          _id: '2',\n          username: 'style_guru_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n          isInfluencer: true\n        },\n        analytics: {\n          views: 18890,\n          likes: 2205,\n          shares: 489,\n          purchases: 367\n        },\n        rating: {\n          average: 4.6,\n          count: 189\n        }\n      }, {\n        _id: '3',\n        name: 'Designer Sneakers',\n        price: 199.99,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',\n          isPrimary: true\n        }],\n        vendor: {\n          _id: '4',\n          username: 'streetstyle_mike',\n          fullName: 'Mike Thompson',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',\n          isInfluencer: true\n        },\n        analytics: {\n          views: 22945,\n          likes: 1567,\n          shares: 289,\n          purchases: 423\n        },\n        rating: {\n          average: 4.9,\n          count: 167\n        }\n      }];\n    }\n    loadMore() {\n      if (this.hasMore && !this.isLoading) {\n        this.loadTrendingProducts(this.currentPage + 1);\n      }\n    }\n    viewProduct(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    viewVendor(vendor) {\n      this.router.navigate(['/vendor', vendor.username]);\n    }\n    addToCart(product, event) {\n      event.stopPropagation();\n      // TODO: Implement add to cart functionality\n      console.log('Add to cart:', product);\n    }\n    toggleWishlist(product, event) {\n      event.stopPropagation();\n      // TODO: Implement wishlist functionality\n      console.log('Toggle wishlist:', product);\n    }\n    shareProduct(product, event) {\n      event.stopPropagation();\n      // TODO: Implement share functionality\n      console.log('Share product:', product);\n    }\n    getDiscountPercentage(product) {\n      if (product.originalPrice && product.originalPrice > product.price) {\n        return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n      }\n      return product.discount || 0;\n    }\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n      }).format(price);\n    }\n    formatNumber(num) {\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    retry() {\n      this.loadTrendingProducts(1);\n    }\n    trackByProductId(index, product) {\n      return product._id;\n    }\n    static {\n      this.ɵfac = function TrendingProductsComponent_Factory(t) {\n        return new (t || TrendingProductsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TrendingProductsComponent,\n        selectors: [[\"app-trending-products\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 5,\n        consts: [[1, \"trending-products-container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"fas\", \"fa-fire\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"load-more-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"product-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-skeleton\"], [1, \"skeleton-image\"], [1, \"skeleton-content\"], [1, \"skeleton-line\"], [1, \"skeleton-line\", \"short\"], [1, \"error-container\"], [1, \"error-content\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"products-grid\"], [\"class\", \"product-card trending-card\", 3, \"rank-1\", \"rank-2\", \"rank-3\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"product-card\", \"trending-card\", 3, \"click\"], [\"class\", \"trending-rank\", 4, \"ngIf\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [\"title\", \"Add to wishlist\", 1, \"wishlist-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"quick-actions\"], [\"title\", \"Add to cart\", 1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [\"title\", \"Share\", 1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"product-info\"], [1, \"vendor-info\", 3, \"click\"], [1, \"vendor-avatar\", 3, \"src\", \"alt\"], [1, \"vendor-name\"], [\"class\", \"fas fa-check-circle verified-icon\", \"title\", \"Verified Influencer\", 4, \"ngIf\"], [1, \"product-name\"], [1, \"price-container\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"rating-container\", 4, \"ngIf\"], [1, \"trending-analytics\"], [1, \"analytics-item\", \"highlight\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"analytics-item\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-heart\"], [1, \"trending-rank\"], [1, \"rank-number\"], [\"class\", \"fas fa-crown\", 4, \"ngIf\"], [1, \"fas\", \"fa-crown\"], [1, \"discount-badge\"], [\"title\", \"Verified Influencer\", 1, \"fas\", \"fa-check-circle\", \"verified-icon\"], [1, \"original-price\"], [1, \"rating-container\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"fas\", \"fa-star\"], [1, \"load-more-container\"], [1, \"load-more-btn\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"empty-container\"], [1, \"empty-content\"], [1, \"browse-btn\", 3, \"click\"]],\n        template: function TrendingProductsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" Trending Now \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function TrendingProductsComponent_Template_button_click_5_listener() {\n              return ctx.router.navigate([\"/shop\"], {\n                queryParams: {\n                  filter: \"trending\"\n                }\n              });\n            });\n            i0.ɵɵtext(6, \" View All \");\n            i0.ɵɵelement(7, \"i\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(8, TrendingProductsComponent_div_8_Template, 3, 2, \"div\", 6)(9, TrendingProductsComponent_div_9_Template, 10, 1, \"div\", 7)(10, TrendingProductsComponent_div_10_Template, 2, 2, \"div\", 8)(11, TrendingProductsComponent_div_11_Template, 4, 3, \"div\", 9)(12, TrendingProductsComponent_div_12_Template, 9, 0, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading && (!ctx.products || ctx.products.length === 0));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && (!ctx.products || ctx.products.length === 0));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.products && ctx.products.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.hasMore);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.products && ctx.products.length === 0 && !ctx.isLoading && !ctx.error);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n        styles: [\".trending-products-container[_ngcontent-%COMP%]{padding:24px;background:linear-gradient(135deg,#ff9a9e,#fecfef 50% 100%);border-radius:16px;margin:16px;box-shadow:0 8px 32px #ff9a9e4d}@media (max-width: 768px){.trending-products-container[_ngcontent-%COMP%]{margin:8px;padding:16px;border-radius:12px}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:24px;font-weight:700;color:#2d3748;margin:0}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#f56565;font-size:20px;animation:_ngcontent-%COMP%_flicker 2s infinite}@media (max-width: 768px){.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:20px}}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f56565,#e53e3e);border:none;border-radius:20px;padding:8px 16px;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #f5656566}@media (max-width: 768px){.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{padding:6px 12px;font-size:14px}}@keyframes _ngcontent-%COMP%_flicker{0%,to{opacity:1}50%{opacity:.7}}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:20px}@media (max-width: 768px){.products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(160px,1fr));gap:12px}}.trending-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 20px #00000014;position:relative}.trending-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.trending-card[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.trending-card.rank-1[_ngcontent-%COMP%]{border:3px solid #ffd700;box-shadow:0 8px 32px #ffd7004d}.trending-card.rank-1[_ngcontent-%COMP%]   .trending-rank[_ngcontent-%COMP%]{background:linear-gradient(135deg,gold,#ffed4e);color:#744210}.trending-card.rank-2[_ngcontent-%COMP%]{border:3px solid #c0c0c0;box-shadow:0 8px 32px #c0c0c04d}.trending-card.rank-2[_ngcontent-%COMP%]   .trending-rank[_ngcontent-%COMP%]{background:linear-gradient(135deg,silver,#e2e8f0);color:#4a5568}.trending-card.rank-3[_ngcontent-%COMP%]{border:3px solid #cd7f32;box-shadow:0 8px 32px #cd7f324d}.trending-card.rank-3[_ngcontent-%COMP%]   .trending-rank[_ngcontent-%COMP%]{background:linear-gradient(135deg,#cd7f32,#d69e2e);color:#744210}@media (max-width: 768px){.trending-card[_ngcontent-%COMP%]{border-radius:12px}.trending-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px)}}.trending-rank[_ngcontent-%COMP%]{position:absolute;top:8px;left:8px;background:linear-gradient(135deg,#f56565,#e53e3e);color:#fff;padding:4px 8px;border-radius:12px;font-size:12px;font-weight:700;z-index:5;display:flex;align-items:center;gap:4px;box-shadow:0 2px 8px #f5656566}.trending-rank[_ngcontent-%COMP%]   .rank-number[_ngcontent-%COMP%]{font-size:14px}.trending-rank[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px;animation:_ngcontent-%COMP%_sparkle 1.5s infinite}@keyframes _ngcontent-%COMP%_sparkle{0%,to{transform:scale(1)}50%{transform:scale(1.2)}}.product-image-container[_ngcontent-%COMP%]{position:relative;aspect-ratio:1;overflow:hidden}.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.product-image-container[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]{transform:scale(1.05)}.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:linear-gradient(135deg,#f56565,#e53e3e);color:#fff;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600;display:flex;align-items:center;gap:4px;box-shadow:0 2px 8px #f5656566;animation:_ngcontent-%COMP%_pulse 2s infinite}.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:8px}.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:40px;right:8px;background:linear-gradient(135deg,#f093fb,#f5576c);color:#fff;padding:4px 8px;border-radius:12px;font-size:12px;font-weight:600;box-shadow:0 2px 8px #f093fb66}.product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]{position:absolute;bottom:12px;left:12px;background:#ffffffe6;border:none;border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1);color:#f5576c}.product-image-container[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{position:absolute;bottom:12px;right:12px;display:flex;gap:8px;opacity:0;transform:translateY(20px);transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]{background:#ffffffe6;border:none;border-radius:50%;width:32px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}@media (max-width: 768px){.product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}}.product-info[_ngcontent-%COMP%]{padding:16px}@media (max-width: 768px){.product-info[_ngcontent-%COMP%]{padding:12px}}.vendor-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:8px;cursor:pointer;transition:opacity .3s ease}.vendor-info[_ngcontent-%COMP%]:hover{opacity:.8}.vendor-info[_ngcontent-%COMP%]   .vendor-avatar[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;object-fit:cover}.vendor-info[_ngcontent-%COMP%]   .vendor-name[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#f56565}.vendor-info[_ngcontent-%COMP%]   .verified-icon[_ngcontent-%COMP%]{color:#f56565;font-size:12px}.product-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#2d3748;margin:0 0 8px;line-height:1.3;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}@media (max-width: 768px){.product-name[_ngcontent-%COMP%]{font-size:14px}}.price-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:8px}.price-container[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748}.price-container[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:14px;color:#a0aec0;text-decoration:line-through}@media (max-width: 768px){.price-container[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:16px}.price-container[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:12px}}.rating-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:8px}.rating-container[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:2px}.rating-container[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;color:#e2e8f0}.rating-container[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%]{color:#f6ad55}.rating-container[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{font-size:12px;color:#718096}.trending-analytics[_ngcontent-%COMP%]{display:flex;gap:12px}.trending-analytics[_ngcontent-%COMP%]   .analytics-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-size:11px;color:#718096}.trending-analytics[_ngcontent-%COMP%]   .analytics-item.highlight[_ngcontent-%COMP%]{color:#f56565;font-weight:600}.trending-analytics[_ngcontent-%COMP%]   .analytics-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}@media (max-width: 768px){.trending-analytics[_ngcontent-%COMP%]{gap:8px}.trending-analytics[_ngcontent-%COMP%]   .analytics-item[_ngcontent-%COMP%]{font-size:10px}}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:20px}@media (max-width: 768px){.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(160px,1fr));gap:12px}}.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%]{aspect-ratio:1;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-line[_ngcontent-%COMP%]{height:12px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:6px;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .product-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-line.short[_ngcontent-%COMP%]{width:60%}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.8}}.error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:300px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]{text-align:center;max-width:400px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:48px;color:#f56565;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#2d3748;margin-bottom:8px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#718096;margin-bottom:24px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f56565,#e53e3e);border:none;border-radius:20px;padding:12px 24px;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #f5656566}.load-more-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:32px}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f56565,#e53e3e);border:none;border-radius:25px;padding:12px 32px;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 4px 12px #f5656566}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px}\"]\n      });\n    }\n  }\n  return TrendingProductsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}