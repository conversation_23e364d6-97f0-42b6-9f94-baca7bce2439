.order-details-content {
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.info-card {
  margin: 0;

  mat-card-title {
    font-size: 1rem;
    font-weight: 600;
  }

  mat-card-content {
    padding-top: 1rem;
  }
}

.status-info {
  .status-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }

  .status-details p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
  }
}

.customer-info,
.address-info,
.payment-info {
  p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
    line-height: 1.4;
  }
}

.items-card {
  margin: 0 0 1.5rem 0;
}

.items-list {
  .item-row {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e0e0e0;

    &:last-child {
      border-bottom: none;
    }

    .item-image {
      width: 60px;
      height: 60px;
      margin-right: 1rem;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
      }
    }

    .item-details {
      flex: 1;

      h4 {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        font-weight: 500;
      }

      .item-specs {
        margin: 0.25rem 0;
        font-size: 0.75rem;
        color: #666;

        span {
          margin-right: 1rem;
        }
      }

      .item-price {
        margin: 0.25rem 0 0 0;
        font-size: 0.875rem;
        color: #666;
      }
    }

    .item-total {
      font-weight: 600;
      font-size: 1rem;
      color: #2e7d32;
    }
  }
}

.order-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;

  mat-form-field {
    min-width: 200px;
  }
}

.action-spinner {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .order-info-grid {
    grid-template-columns: 1fr;
  }

  .item-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;

    .item-image {
      margin-right: 0;
    }

    .item-total {
      align-self: flex-end;
    }
  }

  .order-actions {
    flex-direction: column;

    mat-form-field {
      min-width: auto;
      width: 100%;
    }
  }
}
