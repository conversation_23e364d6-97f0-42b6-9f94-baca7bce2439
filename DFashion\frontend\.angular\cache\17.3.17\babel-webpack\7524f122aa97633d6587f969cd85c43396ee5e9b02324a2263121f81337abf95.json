{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let PaymentService = /*#__PURE__*/(() => {\n  class PaymentService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl;\n      this.paymentStatusSubject = new BehaviorSubject('idle');\n      this.paymentStatus$ = this.paymentStatusSubject.asObservable();\n      this.loadRazorpayScript();\n    }\n    loadRazorpayScript() {\n      return new Promise(resolve => {\n        if (typeof Razorpay !== 'undefined') {\n          resolve(true);\n          return;\n        }\n        const script = document.createElement('script');\n        script.src = 'https://checkout.razorpay.com/v1/checkout.js';\n        script.onload = () => resolve(true);\n        script.onerror = () => resolve(false);\n        document.head.appendChild(script);\n      });\n    }\n    createOrder(amount, currency = 'INR') {\n      const orderData = {\n        amount: amount * 100,\n        currency: currency\n      };\n      return this.http.post(`${this.apiUrl}/payment/create-order`, orderData);\n    }\n    verifyPayment(paymentData) {\n      return this.http.post(`${this.apiUrl}/payment/verify`, paymentData);\n    }\n    initiatePayment(orderData, userDetails) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.paymentStatusSubject.next('processing');\n          // Create order on backend\n          const order = yield _this.createOrder(orderData.amount).toPromise();\n          if (!order) {\n            throw new Error('Failed to create order');\n          }\n          // Prepare Razorpay options\n          const options = {\n            key: environment.razorpayKeyId,\n            amount: order.amount,\n            currency: order.currency,\n            name: 'DFashion',\n            description: 'Fashion Purchase',\n            order_id: order.id,\n            handler: response => {\n              _this.handlePaymentSuccess(response, orderData);\n            },\n            prefill: {\n              name: userDetails.name,\n              email: userDetails.email,\n              contact: userDetails.phone\n            },\n            theme: {\n              color: '#667eea'\n            },\n            modal: {\n              ondismiss: () => {\n                _this.paymentStatusSubject.next('cancelled');\n              }\n            }\n          };\n          // Open Razorpay checkout\n          const rzp = new Razorpay(options);\n          rzp.open();\n        } catch (error) {\n          console.error('Payment initiation failed:', error);\n          _this.paymentStatusSubject.next('failed');\n          throw error;\n        }\n      })();\n    }\n    handlePaymentSuccess(response, orderData) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Verify payment on backend\n          const verificationData = {\n            razorpay_order_id: response.razorpay_order_id,\n            razorpay_payment_id: response.razorpay_payment_id,\n            razorpay_signature: response.razorpay_signature\n          };\n          const verification = yield _this2.verifyPayment(verificationData).toPromise();\n          if (verification) {\n            _this2.paymentStatusSubject.next('success');\n            // Generate and send bill\n            yield _this2.generateAndSendBill({\n              ...orderData,\n              paymentId: response.razorpay_payment_id,\n              orderId: response.razorpay_order_id\n            });\n          } else {\n            _this2.paymentStatusSubject.next('failed');\n          }\n        } catch (error) {\n          console.error('Payment verification failed:', error);\n          _this2.paymentStatusSubject.next('failed');\n        }\n      })();\n    }\n    generateAndSendBill(orderData) {\n      return _asyncToGenerator(function* () {\n        try {\n          // This will be implemented with the bill service\n          console.log('Generating bill for order:', orderData);\n        } catch (error) {\n          console.error('Bill generation failed:', error);\n        }\n      })();\n    }\n    // Payment method selection\n    getPaymentMethods() {\n      return [{\n        id: 'razorpay',\n        name: 'Credit/Debit Card',\n        description: 'Pay securely with your card',\n        icon: 'fas fa-credit-card',\n        enabled: true\n      }, {\n        id: 'upi',\n        name: 'UPI',\n        description: 'Pay with UPI apps',\n        icon: 'fas fa-mobile-alt',\n        enabled: true\n      }, {\n        id: 'netbanking',\n        name: 'Net Banking',\n        description: 'Pay with your bank account',\n        icon: 'fas fa-university',\n        enabled: true\n      }, {\n        id: 'wallet',\n        name: 'Wallet',\n        description: 'Pay with digital wallets',\n        icon: 'fas fa-wallet',\n        enabled: true\n      }, {\n        id: 'cod',\n        name: 'Cash on Delivery',\n        description: 'Pay when you receive',\n        icon: 'fas fa-money-bill-wave',\n        enabled: true\n      }];\n    }\n    // Reset payment status\n    resetPaymentStatus() {\n      this.paymentStatusSubject.next('idle');\n    }\n    // Get current payment status\n    getCurrentPaymentStatus() {\n      return this.paymentStatusSubject.value;\n    }\n    static {\n      this.ɵfac = function PaymentService_Factory(t) {\n        return new (t || PaymentService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PaymentService,\n        factory: PaymentService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PaymentService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}