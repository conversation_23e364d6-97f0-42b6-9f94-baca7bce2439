// Payment Success Page Styles
.payment-success-container {
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  text-align: center;
  margin-bottom: 40px;
}

// Success Icon Animation
.success-icon {
  margin-bottom: 30px;
}

.checkmark-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  margin: 0 auto;
  position: relative;
  animation: scaleIn 0.6s ease-out;
}

.checkmark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 15px;
  border-left: 4px solid white;
  border-bottom: 4px solid white;
  transform: translate(-50%, -60%) rotate(-45deg);
  animation: checkmarkDraw 0.4s ease-out 0.3s both;
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkmarkDraw {
  0% {
    width: 0;
    height: 0;
  }
  50% {
    width: 30px;
    height: 0;
  }
  100% {
    width: 30px;
    height: 15px;
  }
}

// Success Content
.success-content {
  h1 {
    font-size: 32px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 16px;
    animation: fadeInUp 0.6s ease-out 0.2s both;
  }

  .success-message {
    font-size: 16px;
    color: #718096;
    line-height: 1.6;
    margin-bottom: 30px;
    animation: fadeInUp 0.6s ease-out 0.4s both;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Order Details
.order-details {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  text-align: left;
  animation: fadeInUp 0.6s ease-out 0.6s both;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    text-align: center;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-weight: 500;
      color: #4a5568;
    }

    .value {
      font-weight: 600;
      color: #2d3748;
    }
  }
}

// Email Notification
.email-notification {
  background: #e6fffa;
  border: 1px solid #81e6d9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: fadeInUp 0.6s ease-out 0.8s both;

  i {
    color: #319795;
    font-size: 20px;
  }

  p {
    margin: 0;
    color: #2c7a7b;
    font-size: 14px;
  }
}

// Action Buttons
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 40px;
  animation: fadeInUp 0.6s ease-out 1s both;

  .btn {
    padding: 14px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    border: none;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    i {
      font-size: 14px;
    }
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
  }

  .btn-secondary {
    background: #4a5568;
    color: white;

    &:hover {
      background: #2d3748;
      transform: translateY(-2px);
    }
  }

  .btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;

    &:hover {
      background: #667eea;
      color: white;
      transform: translateY(-2px);
    }
  }
}

// Additional Information
.additional-info {
  text-align: left;
  animation: fadeInUp 0.6s ease-out 1.2s both;

  .info-item {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;

    i {
      color: #667eea;
      font-size: 20px;
      margin-top: 4px;
      flex-shrink: 0;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #718096;
      margin: 0;
      line-height: 1.5;

      a {
        color: #667eea;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// Recommendations
.recommendations {
  background: white;
  border-radius: 20px;
  padding: 30px;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    text-align: center;
    margin-bottom: 30px;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .product-card {
    background: #f8fafc;
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    img {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: 8px;
      margin-bottom: 12px;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 8px 0;
    }

    .price {
      font-size: 18px;
      font-weight: 700;
      color: #667eea;
      margin: 0;
    }
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .payment-success-container {
    padding: 20px 16px;
  }

  .success-card {
    padding: 30px 20px;
  }

  .success-content h1 {
    font-size: 28px;
  }

  .action-buttons {
    .btn {
      padding: 12px 20px;
      font-size: 14px;
    }
  }

  .recommendations {
    padding: 20px;

    .products-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .product-card {
      padding: 12px;

      img {
        height: 100px;
      }

      h4 {
        font-size: 14px;
      }

      .price {
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .order-details .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .additional-info .info-item {
    flex-direction: column;
    gap: 8px;
  }

  .recommendations .products-grid {
    grid-template-columns: 1fr;
  }
}
