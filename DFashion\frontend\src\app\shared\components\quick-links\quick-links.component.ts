import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ShopDataService, QuickLink } from '../../../core/services/shop-data.service';

@Component({
  selector: 'app-quick-links',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="quick-links-section">
      <!-- Section Header -->
      <div class="section-header" *ngIf="showHeader">
        <div class="header-content">
          <h2 class="section-title">
            <i class="fas fa-bolt"></i>
            Quick Links
          </h2>
          <p class="section-subtitle">Shop by category - fast and easy</p>
        </div>
      </div>

      <!-- Quick Links Grid -->
      <div class="quick-links-grid" [class.compact]="isCompact">
        <div 
          *ngFor="let link of quickLinks; trackBy: trackByLinkId" 
          class="quick-link-card"
          [style.background]="getCardBackground(link.color)"
          (click)="navigateToLink(link)"
          [attr.aria-label]="'Navigate to ' + link.title"
          tabindex="0"
          (keydown.enter)="navigateToLink(link)"
          (keydown.space)="navigateToLink(link)">
          
          <!-- Link Icon -->
          <div class="link-icon" [style.color]="link.color">
            <i [class]="link.icon"></i>
          </div>
          
          <!-- Link Content -->
          <div class="link-content">
            <h3 class="link-title">{{ link.title }}</h3>
            <p class="link-description" *ngIf="!isCompact">{{ link.description }}</p>
          </div>
          
          <!-- Arrow Icon -->
          <div class="link-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
          
          <!-- Hover Effect -->
          <div class="hover-effect" [style.background]="link.color"></div>
        </div>
      </div>

      <!-- Popular Categories -->
      <div class="popular-categories" *ngIf="showPopularCategories">
        <h3 class="categories-title">Popular Categories</h3>
        <div class="categories-list">
          <button 
            *ngFor="let category of popularCategories"
            class="category-chip"
            (click)="navigateToCategory(category.slug)"
            [style.border-color]="category.color"
            [style.color]="category.color">
            <i [class]="category.icon"></i>
            <span>{{ category.name }}</span>
            <span class="product-count">({{ category.productCount }})</span>
          </button>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions" *ngIf="showQuickActions">
        <h3 class="actions-title">Quick Actions</h3>
        <div class="actions-grid">
          <button 
            *ngFor="let action of quickActions"
            class="action-btn"
            (click)="performAction(action.action)"
            [style.background]="action.color">
            <i [class]="action.icon"></i>
            <span>{{ action.label }}</span>
          </button>
        </div>
      </div>

      <!-- Featured Collections -->
      <div class="featured-collections" *ngIf="showFeaturedCollections">
        <h3 class="collections-title">Featured Collections</h3>
        <div class="collections-scroll">
          <div 
            *ngFor="let collection of featuredCollections"
            class="collection-card"
            (click)="navigateToCollection(collection.slug)">
            <div class="collection-image">
              <img [src]="collection.image" [alt]="collection.name" loading="lazy">
              <div class="collection-overlay">
                <div class="collection-info">
                  <h4>{{ collection.name }}</h4>
                  <p>{{ collection.itemCount }} items</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./quick-links.component.scss']
})
export class QuickLinksComponent implements OnInit, OnDestroy {
  @Input() showHeader: boolean = true;
  @Input() isCompact: boolean = false;
  @Input() maxLinks: number = 8;
  @Input() showPopularCategories: boolean = true;
  @Input() showQuickActions: boolean = true;
  @Input() showFeaturedCollections: boolean = true;
  
  quickLinks: QuickLink[] = [];
  popularCategories: any[] = [];
  quickActions: any[] = [];
  featuredCollections: any[] = [];
  
  private destroy$ = new Subject<void>();

  constructor(
    private shopDataService: ShopDataService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadQuickLinks();
    this.initializePopularCategories();
    this.initializeQuickActions();
    this.initializeFeaturedCollections();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadQuickLinks(): void {
    this.shopDataService.quickLinks$
      .pipe(takeUntil(this.destroy$))
      .subscribe(links => {
        this.quickLinks = links
          .filter(link => link.featured)
          .sort((a, b) => a.order - b.order)
          .slice(0, this.maxLinks);
      });
  }

  navigateToLink(link: QuickLink): void {
    this.trackLinkClick(link);
    this.router.navigate([link.route]);
  }

  navigateToCategory(categorySlug: string): void {
    this.router.navigate(['/shop/category', categorySlug]);
  }

  navigateToCollection(collectionSlug: string): void {
    this.router.navigate(['/shop/collection', collectionSlug]);
  }

  performAction(action: string): void {
    switch (action) {
      case 'search':
        this.router.navigate(['/search']);
        break;
      case 'wishlist':
        this.router.navigate(['/wishlist']);
        break;
      case 'cart':
        this.router.navigate(['/cart']);
        break;
      case 'orders':
        this.router.navigate(['/orders']);
        break;
      case 'offers':
        this.router.navigate(['/offers']);
        break;
      case 'support':
        this.router.navigate(['/support']);
        break;
      default:
        console.log('Action not implemented:', action);
    }
  }

  getCardBackground(color: string): string {
    return `linear-gradient(135deg, ${color}10 0%, ${color}20 100%)`;
  }

  trackByLinkId(index: number, link: QuickLink): string {
    return link.id;
  }

  private initializePopularCategories(): void {
    this.popularCategories = [
      {
        name: 'Men\'s Fashion',
        slug: 'men',
        icon: 'fas fa-male',
        color: '#3498db',
        productCount: 1250
      },
      {
        name: 'Women\'s Fashion',
        slug: 'women',
        icon: 'fas fa-female',
        color: '#e91e63',
        productCount: 1890
      },
      {
        name: 'Footwear',
        slug: 'footwear',
        icon: 'fas fa-shoe-prints',
        color: '#ff9800',
        productCount: 567
      },
      {
        name: 'Accessories',
        slug: 'accessories',
        icon: 'fas fa-gem',
        color: '#9c27b0',
        productCount: 423
      },
      {
        name: 'Ethnic Wear',
        slug: 'ethnic',
        icon: 'fas fa-star-and-crescent',
        color: '#ff5722',
        productCount: 789
      },
      {
        name: 'Sports & Fitness',
        slug: 'sports',
        icon: 'fas fa-dumbbell',
        color: '#4caf50',
        productCount: 345
      }
    ];
  }

  private initializeQuickActions(): void {
    this.quickActions = [
      {
        label: 'Search',
        icon: 'fas fa-search',
        action: 'search',
        color: '#667eea'
      },
      {
        label: 'Wishlist',
        icon: 'fas fa-heart',
        action: 'wishlist',
        color: '#e91e63'
      },
      {
        label: 'Cart',
        icon: 'fas fa-shopping-cart',
        action: 'cart',
        color: '#4caf50'
      },
      {
        label: 'Orders',
        icon: 'fas fa-box',
        action: 'orders',
        color: '#ff9800'
      },
      {
        label: 'Offers',
        icon: 'fas fa-tags',
        action: 'offers',
        color: '#f44336'
      },
      {
        label: 'Support',
        icon: 'fas fa-headset',
        action: 'support',
        color: '#00bcd4'
      }
    ];
  }

  private initializeFeaturedCollections(): void {
    this.featuredCollections = [
      {
        name: 'Summer Collection',
        slug: 'summer-2024',
        image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=300&h=200&fit=crop',
        itemCount: 156
      },
      {
        name: 'Ethnic Elegance',
        slug: 'ethnic-elegance',
        image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300&h=200&fit=crop',
        itemCount: 89
      },
      {
        name: 'Casual Comfort',
        slug: 'casual-comfort',
        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=200&fit=crop',
        itemCount: 234
      },
      {
        name: 'Formal Wear',
        slug: 'formal-wear',
        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',
        itemCount: 123
      }
    ];
  }

  private trackLinkClick(link: QuickLink): void {
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'quick_link_click', {
        link_title: link.title,
        link_id: link.id,
        link_category: link.category || 'general',
        event_category: 'navigation'
      });
    }

    // Also track in console for development
    console.log('Quick link clicked:', link.title, '→', link.route);
  }
}
