{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Import existing sidebar components\nimport { SuggestedProductsComponent } from '../suggested-products/suggested-products.component';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { TopInfluencersComponent } from '../top-influencers/top-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction SidebarComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"h4\", 34);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 36);\n    i0.ɵɵelement(9, \"i\", 37);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Profile\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatar || \"/assets/images/default-avatar.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.currentUser.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.fullName);\n  }\n}\nfunction SidebarComponent_section_2_div_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getDiscountPercentage(item_r4), \"% OFF \");\n  }\n}\nfunction SidebarComponent_section_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_2_div_10_Template_div_click_0_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(item_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 44);\n    i0.ɵɵelement(2, \"img\", 45);\n    i0.ɵɵelementStart(3, \"div\", 46);\n    i0.ɵɵelement(4, \"i\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 48)(6, \"span\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SidebarComponent_section_2_div_10_div_10_Template, 3, 1, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (item_r4.images == null ? null : item_r4.images[0] == null ? null : item_r4.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", item_r4.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(item_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getDiscountPercentage(item_r4) > 0);\n  }\n}\nfunction SidebarComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 38)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 39);\n    i0.ɵɵtext(4, \" Summer Collection 2024\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewSummerCollection());\n    });\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"View All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 41);\n    i0.ɵɵtemplate(10, SidebarComponent_section_2_div_10_Template, 11, 5, \"div\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.summerCollection.slice(0, 3))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_section_14_div_10_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatPrice(product_r7.originalPrice), \" \");\n  }\n}\nfunction SidebarComponent_section_14_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_14_div_10_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(product_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 59);\n    i0.ɵɵelement(2, \"img\", 60);\n    i0.ɵɵelementStart(3, \"div\", 61);\n    i0.ɵɵelement(4, \"i\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 63)(6, \"span\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 65);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 66)(11, \"span\", 67);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SidebarComponent_section_14_div_10_span_13_Template, 2, 1, \"span\", 68);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r7.images == null ? null : product_r7.images[0] == null ? null : product_r7.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(product_r7.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice);\n  }\n}\nfunction SidebarComponent_section_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 54)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 55);\n    i0.ɵɵtext(4, \" Featured Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_14_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewFeaturedProducts());\n    });\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"View All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 56);\n    i0.ɵɵtemplate(10, SidebarComponent_section_14_div_10_Template, 14, 6, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredProducts.slice(0, 4))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_section_23_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_23_div_8_Template_div_click_0_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(product_r10));\n    });\n    i0.ɵɵelement(1, \"img\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵtext(3, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 76)(5, \"span\", 77);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 78);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 79);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", (product_r10.images == null ? null : product_r10.images[0] == null ? null : product_r10.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r10.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(product_r10.price));\n  }\n}\nfunction SidebarComponent_section_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 10)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 70);\n    i0.ɵɵtext(4, \" New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_23_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewNewArrivals());\n    });\n    i0.ɵɵtext(6, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 71);\n    i0.ɵɵtemplate(8, SidebarComponent_section_23_div_8_Template, 11, 5, \"div\", 72);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.newArrivals.slice(0, 4))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_40_Template_div_click_0_listener() {\n      const category_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCategoryClick(category_r12));\n    });\n    i0.ɵɵelement(1, \"img\", 81);\n    i0.ɵɵelementStart(2, \"span\", 82);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r12.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r12.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(router) {\n    this.router = router;\n    // Data passed from home component\n    this.currentUser = null;\n    this.featuredProducts = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.summerCollection = [];\n    this.categories = [];\n  }\n  ngOnInit() {\n    console.log('✅ Sidebar component initialized with data:', {\n      featuredProducts: this.featuredProducts.length,\n      trendingProducts: this.trendingProducts.length,\n      newArrivals: this.newArrivals.length,\n      summerCollection: this.summerCollection.length,\n      categories: this.categories.length\n    });\n  }\n  // Navigation methods\n  onProductClick(product) {\n    console.log('🛍️ Navigate to product:', product.name);\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n  onCategoryClick(category) {\n    console.log('📂 Navigate to category:', category.name);\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        category: category.slug || category.name.toLowerCase()\n      }\n    });\n  }\n  onUserClick(user) {\n    console.log('👤 Navigate to user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n  followUser(user) {\n    console.log('➕ Follow user:', user.username);\n    // TODO: Implement follow functionality\n  }\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        collection: 'summer2024'\n      }\n    });\n  }\n  viewFeaturedProducts() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        filter: 'featured'\n      }\n    });\n  }\n  viewNewArrivals() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        filter: 'new'\n      }\n    });\n  }\n  // Utility methods\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return product.discount || 0;\n  }\n  // Track by functions for performance\n  trackByProductId(index, product) {\n    return product._id || product.id;\n  }\n  trackByCategoryId(index, category) {\n    return category._id || category.id;\n  }\n  // Get categories to display (real data or fallback)\n  getDisplayCategories() {\n    if (this.categories && this.categories.length > 0) {\n      return this.categories.slice(0, 4);\n    }\n    // Fallback categories with images\n    return [{\n      _id: 'women',\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      _id: 'men',\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      _id: 'children',\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      _id: 'ethnic',\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      inputs: {\n        currentUser: \"currentUser\",\n        featuredProducts: \"featuredProducts\",\n        trendingProducts: \"trendingProducts\",\n        newArrivals: \"newArrivals\",\n        summerCollection: \"summerCollection\",\n        categories: \"categories\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 60,\n      vars: 6,\n      consts: [[1, \"home-sidebar\"], [\"class\", \"profile-card\", 4, \"ngIf\"], [\"class\", \"sidebar-section summer-collection\", 4, \"ngIf\"], [1, \"sidebar-section\", \"suggested-section\"], [1, \"section-header\"], [1, \"fas\", \"fa-magic\"], [\"routerLink\", \"/shop?filter=suggested\", 1, \"view-all-btn\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"component-wrapper\"], [\"class\", \"sidebar-section featured-section\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"fas\", \"fa-fire\"], [\"routerLink\", \"/shop?filter=trending\", 1, \"see-all-btn\"], [\"class\", \"sidebar-section\", 4, \"ngIf\"], [1, \"fas\", \"fa-crown\"], [\"routerLink\", \"/influencers\", 1, \"see-all-btn\"], [1, \"fas\", \"fa-th-large\"], [1, \"see-all-btn\", 3, \"click\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"sidebar-footer\"], [1, \"footer-links\"], [\"href\", \"#\", \"routerLink\", \"/about\"], [\"href\", \"#\", \"routerLink\", \"/help\"], [\"href\", \"#\", \"routerLink\", \"/press\"], [\"href\", \"#\", \"routerLink\", \"/api\"], [\"href\", \"#\", \"routerLink\", \"/jobs\"], [\"href\", \"#\", \"routerLink\", \"/privacy\"], [\"href\", \"#\", \"routerLink\", \"/terms\"], [1, \"copyright\"], [1, \"profile-card\"], [1, \"profile-avatar-container\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"profile-username\"], [1, \"profile-name\"], [\"routerLink\", \"/profile\", 1, \"switch-btn\"], [1, \"fas\", \"fa-user-cog\"], [1, \"sidebar-section\", \"summer-collection\"], [1, \"fas\", \"fa-sun\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"collection-items\"], [\"class\", \"collection-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"collection-item\", 3, \"click\"], [1, \"collection-image-container\"], [1, \"collection-image\", 3, \"src\", \"alt\"], [1, \"collection-overlay\"], [1, \"fas\", \"fa-eye\"], [1, \"collection-info\"], [1, \"collection-name\"], [1, \"collection-price\"], [\"class\", \"collection-discount\", 4, \"ngIf\"], [1, \"collection-discount\"], [1, \"fas\", \"fa-tag\"], [1, \"sidebar-section\", \"featured-section\"], [1, \"fas\", \"fa-star\"], [1, \"featured-grid\"], [\"class\", \"featured-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"featured-item\", 3, \"click\"], [1, \"featured-image-container\"], [1, \"featured-image\", 3, \"src\", \"alt\"], [1, \"featured-overlay\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"featured-info\"], [1, \"featured-name\"], [1, \"featured-brand\"], [1, \"featured-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"fas\", \"fa-sparkles\"], [1, \"arrivals-list\"], [\"class\", \"arrival-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"arrival-item\", 3, \"click\"], [1, \"arrival-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [1, \"arrival-info\"], [1, \"arrival-name\"], [1, \"arrival-brand\"], [1, \"arrival-price\"], [1, \"category-item\", 3, \"click\"], [1, \"category-image\", 3, \"src\", \"alt\"], [1, \"category-name\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, SidebarComponent_div_1_Template, 12, 4, \"div\", 1)(2, SidebarComponent_section_2_Template, 11, 2, \"section\", 2);\n          i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"h3\");\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \" Suggested for You\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 6)(9, \"span\");\n          i0.ɵɵtext(10, \"View All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵelement(13, \"app-suggested-products\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, SidebarComponent_section_14_Template, 11, 2, \"section\", 9);\n          i0.ɵɵelementStart(15, \"section\", 10)(16, \"div\", 4)(17, \"h3\");\n          i0.ɵɵelement(18, \"i\", 11);\n          i0.ɵɵtext(19, \" Trending Now\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 12);\n          i0.ɵɵtext(21, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(22, \"app-trending-products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, SidebarComponent_section_23_Template, 9, 2, \"section\", 13);\n          i0.ɵɵelementStart(24, \"section\", 10)(25, \"div\", 4)(26, \"h3\");\n          i0.ɵɵelement(27, \"i\", 14);\n          i0.ɵɵtext(28, \" Top Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 15);\n          i0.ɵɵtext(30, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(31, \"app-top-influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"section\", 10)(33, \"div\", 4)(34, \"h3\");\n          i0.ɵɵelement(35, \"i\", 16);\n          i0.ɵɵtext(36, \" Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_37_listener() {\n            return ctx.viewAllCategories();\n          });\n          i0.ɵɵtext(38, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 18);\n          i0.ɵɵtemplate(40, SidebarComponent_div_40_Template, 4, 3, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 20)(42, \"div\", 21)(43, \"a\", 22);\n          i0.ɵɵtext(44, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"a\", 23);\n          i0.ɵɵtext(46, \"Help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"a\", 24);\n          i0.ɵɵtext(48, \"Press\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"a\", 25);\n          i0.ɵɵtext(50, \"API\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"a\", 26);\n          i0.ɵɵtext(52, \"Jobs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"a\", 27);\n          i0.ɵɵtext(54, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"a\", 28);\n          i0.ɵɵtext(56, \"Terms\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 29)(58, \"span\");\n          i0.ɵɵtext(59, \"\\u00A9 2024 DFashion\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.summerCollection.length > 0);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.featuredProducts.length > 0);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.newArrivals.length > 0);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getDisplayCategories())(\"ngForTrackBy\", ctx.trackByCategoryId);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, i1.RouterLink, SuggestedProductsComponent, TrendingProductsComponent, TopInfluencersComponent],\n      styles: [\".home-sidebar[_ngcontent-%COMP%] {\\n  width: 320px;\\n  padding: 0 16px;\\n  position: sticky;\\n  top: 84px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n}\\n@media (max-width: 1024px) {\\n  .home-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #dbdbdb;\\n  border-radius: 2px;\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #c7c7c7;\\n}\\n\\n.profile-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px 0;\\n  margin-bottom: 24px;\\n  border-bottom: 1px solid #efefef;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-right: 12px;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 2px 0;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]:hover {\\n  color: #00376b;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #8e8e8e;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #0095f6;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #262626;\\n  font-size: 12px;\\n  font-weight: 400;\\n  cursor: pointer;\\n  transition: color 0.2s ease;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover {\\n  color: #0095f6;\\n}\\n\\n.collection-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fafafa;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border-radius: 6px;\\n  object-fit: cover;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  margin-bottom: 2px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-discount[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #ed4956;\\n  font-weight: 600;\\n  margin-top: 2px;\\n}\\n\\n.featured-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 12px;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: transform 0.2s ease;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 120px;\\n  object-fit: cover;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%] {\\n  padding: 8px;\\n  background: white;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #262626;\\n  margin-bottom: 2px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 11px;\\n  color: #8e8e8e;\\n  margin-bottom: 4px;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #8e8e8e;\\n  text-decoration: line-through;\\n  margin-left: 4px;\\n}\\n\\n.arrivals-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  position: relative;\\n  transition: background-color 0.2s ease;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fafafa;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-image[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border-radius: 6px;\\n  object-fit: cover;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  left: 4px;\\n  background: #ed4956;\\n  color: white;\\n  font-size: 8px;\\n  font-weight: 600;\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  margin-bottom: 2px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  margin-bottom: 2px;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 12px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 12px 8px;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: 1px solid #efefef;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fafafa;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 8px;\\n  border: 2px solid #fff;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #262626;\\n  text-align: center;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 6px;\\n  background: linear-gradient(45deg, #0095f6, #00d4ff);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 14px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #262626;\\n  margin-bottom: 2px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #8e8e8e;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .trending-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .trending-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ed4956;\\n  font-size: 10px;\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  padding-top: 16px;\\n  border-top: 1px solid #efefef;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n  text-decoration: none;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SuggestedProductsComponent", "TrendingProductsComponent", "TopInfluencersComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "currentUser", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "fullName", "ɵɵtextInterpolate1", "getDiscountPercentage", "item_r4", "ɵɵlistener", "SidebarComponent_section_2_div_10_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onProductClick", "ɵɵtemplate", "SidebarComponent_section_2_div_10_div_10_Template", "images", "url", "name", "formatPrice", "price", "SidebarComponent_section_2_Template_button_click_5_listener", "_r2", "viewSummerCollection", "SidebarComponent_section_2_div_10_Template", "summerCollection", "slice", "trackByProductId", "product_r7", "originalPrice", "SidebarComponent_section_14_div_10_Template_div_click_0_listener", "_r6", "SidebarComponent_section_14_div_10_span_13_Template", "brand", "SidebarComponent_section_14_Template_button_click_5_listener", "_r5", "viewFeaturedProducts", "SidebarComponent_section_14_div_10_Template", "featuredProducts", "SidebarComponent_section_23_div_8_Template_div_click_0_listener", "product_r10", "_r9", "SidebarComponent_section_23_Template_button_click_5_listener", "_r8", "viewNewArrivals", "SidebarComponent_section_23_div_8_Template", "newArrivals", "SidebarComponent_div_40_Template_div_click_0_listener", "category_r12", "_r11", "onCategoryClick", "image", "SidebarComponent", "constructor", "router", "trendingProducts", "categories", "ngOnInit", "console", "log", "length", "product", "navigate", "_id", "id", "category", "queryParams", "slug", "toLowerCase", "onUserClick", "user", "followUser", "viewAllCategories", "collection", "filter", "Intl", "NumberFormat", "style", "currency", "format", "formatNumber", "num", "toFixed", "toString", "Math", "round", "discount", "index", "trackByCategoryId", "getDisplayCategories", "ɵɵdirectiveInject", "i1", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_1_Template", "SidebarComponent_section_2_Template", "SidebarComponent_section_14_Template", "SidebarComponent_section_23_Template", "SidebarComponent_Template_button_click_37_listener", "SidebarComponent_div_40_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\n\n// Import existing sidebar components\nimport { SuggestedProductsComponent } from '../suggested-products/suggested-products.component';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { TopInfluencersComponent } from '../top-influencers/top-influencers.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule, \n    RouterModule, \n    SuggestedProductsComponent, \n    TrendingProductsComponent, \n    TopInfluencersComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  // Data passed from home component\n  @Input() currentUser: any = null;\n  @Input() featuredProducts: any[] = [];\n  @Input() trendingProducts: any[] = [];\n  @Input() newArrivals: any[] = [];\n  @Input() summerCollection: any[] = [];\n  @Input() categories: any[] = [];\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    console.log('✅ Sidebar component initialized with data:', {\n      featuredProducts: this.featuredProducts.length,\n      trendingProducts: this.trendingProducts.length,\n      newArrivals: this.newArrivals.length,\n      summerCollection: this.summerCollection.length,\n      categories: this.categories.length\n    });\n  }\n\n  // Navigation methods\n  onProductClick(product: any) {\n    console.log('🛍️ Navigate to product:', product.name);\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n\n  onCategoryClick(category: any) {\n    console.log('📂 Navigate to category:', category.name);\n    this.router.navigate(['/shop'], {\n      queryParams: { category: category.slug || category.name.toLowerCase() }\n    });\n  }\n\n  onUserClick(user: any) {\n    console.log('👤 Navigate to user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  followUser(user: any) {\n    console.log('➕ Follow user:', user.username);\n    // TODO: Implement follow functionality\n  }\n\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });\n  }\n\n  viewFeaturedProducts() {\n    this.router.navigate(['/shop'], { queryParams: { filter: 'featured' } });\n  }\n\n  viewNewArrivals() {\n    this.router.navigate(['/shop'], { queryParams: { filter: 'new' } });\n  }\n\n  // Utility methods\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  getDiscountPercentage(product: any): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return product.discount || 0;\n  }\n\n  // Track by functions for performance\n  trackByProductId(index: number, product: any): string {\n    return product._id || product.id;\n  }\n\n  trackByCategoryId(index: number, category: any): string {\n    return category._id || category.id;\n  }\n\n  // Get categories to display (real data or fallback)\n  getDisplayCategories() {\n    if (this.categories && this.categories.length > 0) {\n      return this.categories.slice(0, 4);\n    }\n\n    // Fallback categories with images\n    return [\n      {\n        _id: 'women',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        _id: 'men',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        _id: 'children',\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        _id: 'ethnic',\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n}\n", "<div class=\"home-sidebar\">\n  <!-- User Profile Card -->\n  <div class=\"profile-card\" *ngIf=\"currentUser\">\n    <div class=\"profile-avatar-container\">\n      <img [src]=\"currentUser.avatar || '/assets/images/default-avatar.jpg'\"\n           [alt]=\"currentUser.username\" class=\"profile-avatar\">\n    </div>\n    <div class=\"profile-info\">\n      <h4 class=\"profile-username\">{{ currentUser.username }}</h4>\n      <span class=\"profile-name\">{{ currentUser.fullName }}</span>\n    </div>\n    <button class=\"switch-btn\" routerLink=\"/profile\">\n      <i class=\"fas fa-user-cog\"></i>\n      <span>Profile</span>\n    </button>\n  </div>\n\n  <!-- Summer Collection 2024 -->\n  <section class=\"sidebar-section summer-collection\" *ngIf=\"summerCollection.length > 0\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-sun\"></i> Summer Collection 2024</h3>\n      <button class=\"view-all-btn\" (click)=\"viewSummerCollection()\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"collection-items\">\n      <div *ngFor=\"let item of summerCollection.slice(0, 3); trackBy: trackByProductId\"\n           class=\"collection-item\" (click)=\"onProductClick(item)\">\n        <div class=\"collection-image-container\">\n          <img [src]=\"item.images?.[0]?.url || '/assets/images/product-placeholder.jpg'\"\n               [alt]=\"item.name\" class=\"collection-image\">\n          <div class=\"collection-overlay\">\n            <i class=\"fas fa-eye\"></i>\n          </div>\n        </div>\n        <div class=\"collection-info\">\n          <span class=\"collection-name\">{{ item.name }}</span>\n          <span class=\"collection-price\">{{ formatPrice(item.price) }}</span>\n          <div class=\"collection-discount\" *ngIf=\"getDiscountPercentage(item) > 0\">\n            <i class=\"fas fa-tag\"></i>\n            {{ getDiscountPercentage(item) }}% OFF\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n\n  <!-- Suggested Products Component -->\n  <section class=\"sidebar-section suggested-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-magic\"></i> Suggested for You</h3>\n      <button class=\"view-all-btn\" routerLink=\"/shop?filter=suggested\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"component-wrapper\">\n      <app-suggested-products></app-suggested-products>\n    </div>\n  </section>\n\n  <!-- Featured Products -->\n  <section class=\"sidebar-section featured-section\" *ngIf=\"featuredProducts.length > 0\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-star\"></i> Featured Products</h3>\n      <button class=\"view-all-btn\" (click)=\"viewFeaturedProducts()\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"featured-grid\">\n      <div *ngFor=\"let product of featuredProducts.slice(0, 4); trackBy: trackByProductId\"\n           class=\"featured-item\" (click)=\"onProductClick(product)\">\n        <div class=\"featured-image-container\">\n          <img [src]=\"product.images?.[0]?.url || '/assets/images/product-placeholder.jpg'\"\n               [alt]=\"product.name\" class=\"featured-image\">\n          <div class=\"featured-overlay\">\n            <i class=\"fas fa-shopping-bag\"></i>\n          </div>\n        </div>\n        <div class=\"featured-info\">\n          <span class=\"featured-name\">{{ product.name }}</span>\n          <span class=\"featured-brand\">{{ product.brand }}</span>\n          <div class=\"featured-price\">\n            <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n            <span class=\"original-price\" *ngIf=\"product.originalPrice\">\n              {{ formatPrice(product.originalPrice) }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n\n  <!-- Trending Products Component -->\n  <section class=\"sidebar-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-fire\"></i> Trending Now</h3>\n      <button class=\"see-all-btn\" routerLink=\"/shop?filter=trending\">View All</button>\n    </div>\n    <app-trending-products></app-trending-products>\n  </section>\n\n  <!-- New Arrivals -->\n  <section class=\"sidebar-section\" *ngIf=\"newArrivals.length > 0\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-sparkles\"></i> New Arrivals</h3>\n      <button class=\"see-all-btn\" (click)=\"viewNewArrivals()\">View All</button>\n    </div>\n    <div class=\"arrivals-list\">\n      <div *ngFor=\"let product of newArrivals.slice(0, 4); trackBy: trackByProductId\" \n           class=\"arrival-item\" (click)=\"onProductClick(product)\">\n        <img [src]=\"product.images?.[0]?.url || '/assets/images/product-placeholder.jpg'\" \n             [alt]=\"product.name\" class=\"arrival-image\">\n        <div class=\"new-badge\">New</div>\n        <div class=\"arrival-info\">\n          <span class=\"arrival-name\">{{ product.name }}</span>\n          <span class=\"arrival-brand\">{{ product.brand }}</span>\n          <span class=\"arrival-price\">{{ formatPrice(product.price) }}</span>\n        </div>\n      </div>\n    </div>\n  </section>\n\n  <!-- Top Influencers Component -->\n  <section class=\"sidebar-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-crown\"></i> Top Influencers</h3>\n      <button class=\"see-all-btn\" routerLink=\"/influencers\">View All</button>\n    </div>\n    <app-top-influencers></app-top-influencers>\n  </section>\n\n  <!-- Categories -->\n  <section class=\"sidebar-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-th-large\"></i> Shop by Category</h3>\n      <button class=\"see-all-btn\" (click)=\"viewAllCategories()\">View All</button>\n    </div>\n    <div class=\"categories-grid\">\n      <div *ngFor=\"let category of getDisplayCategories(); trackBy: trackByCategoryId\"\n           class=\"category-item\" (click)=\"onCategoryClick(category)\">\n        <img [src]=\"category.image\" [alt]=\"category.name\" class=\"category-image\">\n        <span class=\"category-name\">{{ category.name }}</span>\n      </div>\n    </div>\n  </section>\n\n  <!-- Footer Links -->\n  <div class=\"sidebar-footer\">\n    <div class=\"footer-links\">\n      <a href=\"#\" routerLink=\"/about\">About</a>\n      <a href=\"#\" routerLink=\"/help\">Help</a>\n      <a href=\"#\" routerLink=\"/press\">Press</a>\n      <a href=\"#\" routerLink=\"/api\">API</a>\n      <a href=\"#\" routerLink=\"/jobs\">Jobs</a>\n      <a href=\"#\" routerLink=\"/privacy\">Privacy</a>\n      <a href=\"#\" routerLink=\"/terms\">Terms</a>\n    </div>\n    <div class=\"copyright\">\n      <span>© 2024 DFashion</span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAEtD;AACA,SAASC,0BAA0B,QAAQ,oDAAoD;AAC/F,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;;;;;;ICJlFC,EADF,CAAAC,cAAA,cAA8C,cACN;IACpCD,EAAA,CAAAE,SAAA,cACyD;IAC3DF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,aACK;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IACvDJ,EADuD,CAAAG,YAAA,EAAO,EACxD;IACNH,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAEjBJ,EAFiB,CAAAG,YAAA,EAAO,EACb,EACL;;;;IAXGH,EAAA,CAAAK,SAAA,GAAiE;IACjEL,EADA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,WAAA,CAAAC,MAAA,yCAAAT,EAAA,CAAAU,aAAA,CAAiE,QAAAH,MAAA,CAAAC,WAAA,CAAAG,QAAA,CACrC;IAGJX,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAC,WAAA,CAAAG,QAAA,CAA0B;IAC5BX,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAC,WAAA,CAAAK,QAAA,CAA0B;;;;;IA8BjDb,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAE,SAAA,YAA0B;IAC1BF,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAc,kBAAA,MAAAP,MAAA,CAAAQ,qBAAA,CAAAC,OAAA,YACF;;;;;;IAfJhB,EAAA,CAAAC,cAAA,cAC4D;IAA/BD,EAAA,CAAAiB,UAAA,mBAAAC,gEAAA;MAAA,MAAAF,OAAA,GAAAhB,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAiB,cAAA,CAAAR,OAAA,CAAoB;IAAA,EAAC;IACzDhB,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAAE,SAAA,cACgD;IAChDF,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,SAAA,YAA0B;IAE9BF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAA6B,eACG;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAyB,UAAA,KAAAC,iDAAA,kBAAyE;IAK7E1B,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAdGH,EAAA,CAAAK,SAAA,GAAyE;IACzEL,EADA,CAAAM,UAAA,SAAAU,OAAA,CAAAW,MAAA,kBAAAX,OAAA,CAAAW,MAAA,qBAAAX,OAAA,CAAAW,MAAA,IAAAC,GAAA,+CAAA5B,EAAA,CAAAU,aAAA,CAAyE,QAAAM,OAAA,CAAAa,IAAA,CACxD;IAMQ7B,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAY,iBAAA,CAAAI,OAAA,CAAAa,IAAA,CAAe;IACd7B,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAd,OAAA,CAAAe,KAAA,EAA6B;IAC1B/B,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAQ,qBAAA,CAAAC,OAAA,MAAqC;;;;;;IAnB3EhB,EAFJ,CAAAC,cAAA,kBAAuF,aACzD,SACtB;IAAAD,EAAA,CAAAE,SAAA,YAA0B;IAACF,EAAA,CAAAI,MAAA,8BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,iBAA8D;IAAjCD,EAAA,CAAAiB,UAAA,mBAAAe,4DAAA;MAAAhC,EAAA,CAAAmB,aAAA,CAAAc,GAAA;MAAA,MAAA1B,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAA2B,oBAAA,EAAsB;IAAA,EAAC;IAC3DlC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAE,SAAA,WAAkC;IAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAyB,UAAA,KAAAU,0CAAA,mBAC4D;IAkBhEnC,EADE,CAAAG,YAAA,EAAM,EACE;;;;IAnBgBH,EAAA,CAAAK,SAAA,IAAiC;IAAAL,EAAjC,CAAAM,UAAA,YAAAC,MAAA,CAAA6B,gBAAA,CAAAC,KAAA,OAAiC,iBAAA9B,MAAA,CAAA+B,gBAAA,CAAyB;;;;;IA2D1EtC,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAc,kBAAA,MAAAP,MAAA,CAAAuB,WAAA,CAAAS,UAAA,CAAAC,aAAA,OACF;;;;;;IAhBNxC,EAAA,CAAAC,cAAA,cAC6D;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAwB,iEAAA;MAAA,MAAAF,UAAA,GAAAvC,EAAA,CAAAmB,aAAA,CAAAuB,GAAA,EAAArB,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAiB,cAAA,CAAAe,UAAA,CAAuB;IAAA,EAAC;IAC1DvC,EAAA,CAAAC,cAAA,cAAsC;IACpCD,EAAA,CAAAE,SAAA,cACiD;IACjDF,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,YAAmC;IAEvCF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAA2B,eACG;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErDH,EADF,CAAAC,cAAA,eAA4B,gBACE;IAAAD,EAAA,CAAAI,MAAA,IAAgC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAyB,UAAA,KAAAkB,mDAAA,mBAA2D;IAKjE3C,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAhBGH,EAAA,CAAAK,SAAA,GAA4E;IAC5EL,EADA,CAAAM,UAAA,SAAAiC,UAAA,CAAAZ,MAAA,kBAAAY,UAAA,CAAAZ,MAAA,qBAAAY,UAAA,CAAAZ,MAAA,IAAAC,GAAA,+CAAA5B,EAAA,CAAAU,aAAA,CAA4E,QAAA6B,UAAA,CAAAV,IAAA,CACxD;IAMG7B,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAY,iBAAA,CAAA2B,UAAA,CAAAV,IAAA,CAAkB;IACjB7B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,iBAAA,CAAA2B,UAAA,CAAAK,KAAA,CAAmB;IAElB5C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAS,UAAA,CAAAR,KAAA,EAAgC;IAC9B/B,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,UAAA,SAAAiC,UAAA,CAAAC,aAAA,CAA2B;;;;;;IArB/DxC,EAFJ,CAAAC,cAAA,kBAAsF,aACxD,SACtB;IAAAD,EAAA,CAAAE,SAAA,YAA2B;IAACF,EAAA,CAAAI,MAAA,yBAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,iBAA8D;IAAjCD,EAAA,CAAAiB,UAAA,mBAAA4B,6DAAA;MAAA7C,EAAA,CAAAmB,aAAA,CAAA2B,GAAA;MAAA,MAAAvC,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAwC,oBAAA,EAAsB;IAAA,EAAC;IAC3D/C,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAE,SAAA,WAAkC;IAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAyB,UAAA,KAAAuB,2CAAA,mBAC6D;IAoBjEhD,EADE,CAAAG,YAAA,EAAM,EACE;;;;IArBmBH,EAAA,CAAAK,SAAA,IAAiC;IAAAL,EAAjC,CAAAM,UAAA,YAAAC,MAAA,CAAA0C,gBAAA,CAAAZ,KAAA,OAAiC,iBAAA9B,MAAA,CAAA+B,gBAAA,CAAyB;;;;;;IAuCnFtC,EAAA,CAAAC,cAAA,cAC4D;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAiC,gEAAA;MAAA,MAAAC,WAAA,GAAAnD,EAAA,CAAAmB,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAiB,cAAA,CAAA2B,WAAA,CAAuB;IAAA,EAAC;IACzDnD,EAAA,CAAAE,SAAA,cACgD;IAChDF,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,UAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE9BH,EADF,CAAAC,cAAA,cAA0B,eACG;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,IAAgC;IAEhEJ,EAFgE,CAAAG,YAAA,EAAO,EAC/D,EACF;;;;;IARCH,EAAA,CAAAK,SAAA,EAA4E;IAC5EL,EADA,CAAAM,UAAA,SAAA6C,WAAA,CAAAxB,MAAA,kBAAAwB,WAAA,CAAAxB,MAAA,qBAAAwB,WAAA,CAAAxB,MAAA,IAAAC,GAAA,+CAAA5B,EAAA,CAAAU,aAAA,CAA4E,QAAAyC,WAAA,CAAAtB,IAAA,CACxD;IAGI7B,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAY,iBAAA,CAAAuC,WAAA,CAAAtB,IAAA,CAAkB;IACjB7B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,iBAAA,CAAAuC,WAAA,CAAAP,KAAA,CAAmB;IACnB5C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAqB,WAAA,CAAApB,KAAA,EAAgC;;;;;;IAZhE/B,EAFJ,CAAAC,cAAA,kBAAgE,aAClC,SACtB;IAAAD,EAAA,CAAAE,SAAA,YAA+B;IAACF,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,iBAAwD;IAA5BD,EAAA,CAAAiB,UAAA,mBAAAoC,6DAAA;MAAArD,EAAA,CAAAmB,aAAA,CAAAmC,GAAA;MAAA,MAAA/C,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAgD,eAAA,EAAiB;IAAA,EAAC;IAACvD,EAAA,CAAAI,MAAA,eAAQ;IAClEJ,EADkE,CAAAG,YAAA,EAAS,EACrE;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAyB,UAAA,IAAA+B,0CAAA,mBAC4D;IAWhExD,EADE,CAAAG,YAAA,EAAM,EACE;;;;IAZmBH,EAAA,CAAAK,SAAA,GAA4B;IAAAL,EAA5B,CAAAM,UAAA,YAAAC,MAAA,CAAAkD,WAAA,CAAApB,KAAA,OAA4B,iBAAA9B,MAAA,CAAA+B,gBAAA,CAAyB;;;;;;IA8B9EtC,EAAA,CAAAC,cAAA,cAC+D;IAApCD,EAAA,CAAAiB,UAAA,mBAAAyC,sDAAA;MAAA,MAAAC,YAAA,GAAA3D,EAAA,CAAAmB,aAAA,CAAAyC,IAAA,EAAAvC,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAsD,eAAA,CAAAF,YAAA,CAAyB;IAAA,EAAC;IAC5D3D,EAAA,CAAAE,SAAA,cAAyE;IACzEF,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IACjDJ,EADiD,CAAAG,YAAA,EAAO,EAClD;;;;IAFCH,EAAA,CAAAK,SAAA,EAAsB;IAACL,EAAvB,CAAAM,UAAA,QAAAqD,YAAA,CAAAG,KAAA,EAAA9D,EAAA,CAAAU,aAAA,CAAsB,QAAAiD,YAAA,CAAA9B,IAAA,CAAsB;IACrB7B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,iBAAA,CAAA+C,YAAA,CAAA9B,IAAA,CAAmB;;;AD1HvD,OAAM,MAAOkC,gBAAgB;EAS3BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAR1B;IACS,KAAAzD,WAAW,GAAQ,IAAI;IACvB,KAAAyC,gBAAgB,GAAU,EAAE;IAC5B,KAAAiB,gBAAgB,GAAU,EAAE;IAC5B,KAAAT,WAAW,GAAU,EAAE;IACvB,KAAArB,gBAAgB,GAAU,EAAE;IAC5B,KAAA+B,UAAU,GAAU,EAAE;EAEM;EAErCC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;MACxDrB,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACsB,MAAM;MAC9CL,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACK,MAAM;MAC9Cd,WAAW,EAAE,IAAI,CAACA,WAAW,CAACc,MAAM;MACpCnC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACmC,MAAM;MAC9CJ,UAAU,EAAE,IAAI,CAACA,UAAU,CAACI;KAC7B,CAAC;EACJ;EAEA;EACA/C,cAAcA,CAACgD,OAAY;IACzBH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEE,OAAO,CAAC3C,IAAI,CAAC;IACrD,IAAI,CAACoC,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE,CAAC,CAAC;EAC/D;EAEAd,eAAeA,CAACe,QAAa;IAC3BP,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEM,QAAQ,CAAC/C,IAAI,CAAC;IACtD,IAAI,CAACoC,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BI,WAAW,EAAE;QAAED,QAAQ,EAAEA,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAAC/C,IAAI,CAACkD,WAAW;MAAE;KACtE,CAAC;EACJ;EAEAC,WAAWA,CAACC,IAAS;IACnBZ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,IAAI,CAACtE,QAAQ,CAAC;IAC1D,IAAI,CAACsD,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,EAAEQ,IAAI,CAACtE,QAAQ,CAAC,CAAC;EACnD;EAEAuE,UAAUA,CAACD,IAAS;IAClBZ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEW,IAAI,CAACtE,QAAQ,CAAC;IAC5C;EACF;EAEAwE,iBAAiBA,CAAA;IACf,IAAI,CAAClB,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEAvC,oBAAoBA,CAAA;IAClB,IAAI,CAAC+B,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEI,WAAW,EAAE;QAAEO,UAAU,EAAE;MAAY;IAAE,CAAE,CAAC;EAChF;EAEArC,oBAAoBA,CAAA;IAClB,IAAI,CAACkB,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEI,WAAW,EAAE;QAAEQ,MAAM,EAAE;MAAU;IAAE,CAAE,CAAC;EAC1E;EAEA9B,eAAeA,CAAA;IACb,IAAI,CAACU,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEI,WAAW,EAAE;QAAEQ,MAAM,EAAE;MAAK;IAAE,CAAE,CAAC;EACrE;EAEA;EACAvD,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIuD,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAC;EAClB;EAEA4D,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEA/E,qBAAqBA,CAACyD,OAAY;IAChC,IAAIA,OAAO,CAAChC,aAAa,IAAIgC,OAAO,CAAChC,aAAa,GAAGgC,OAAO,CAACzC,KAAK,EAAE;MAClE,OAAOgE,IAAI,CAACC,KAAK,CAAE,CAACxB,OAAO,CAAChC,aAAa,GAAGgC,OAAO,CAACzC,KAAK,IAAIyC,OAAO,CAAChC,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAOgC,OAAO,CAACyB,QAAQ,IAAI,CAAC;EAC9B;EAEA;EACA3D,gBAAgBA,CAAC4D,KAAa,EAAE1B,OAAY;IAC1C,OAAOA,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE;EAClC;EAEAwB,iBAAiBA,CAACD,KAAa,EAAEtB,QAAa;IAC5C,OAAOA,QAAQ,CAACF,GAAG,IAAIE,QAAQ,CAACD,EAAE;EACpC;EAEA;EACAyB,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACjC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACjD,OAAO,IAAI,CAACJ,UAAU,CAAC9B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGpC;IACA,OAAO,CACL;MACEqC,GAAG,EAAE,OAAO;MACZ7C,IAAI,EAAE,OAAO;MACbiD,IAAI,EAAE,OAAO;MACbhB,KAAK,EAAE;KACR,EACD;MACEY,GAAG,EAAE,KAAK;MACV7C,IAAI,EAAE,KAAK;MACXiD,IAAI,EAAE,KAAK;MACXhB,KAAK,EAAE;KACR,EACD;MACEY,GAAG,EAAE,UAAU;MACf7C,IAAI,EAAE,MAAM;MACZiD,IAAI,EAAE,UAAU;MAChBhB,KAAK,EAAE;KACR,EACD;MACEY,GAAG,EAAE,QAAQ;MACb7C,IAAI,EAAE,QAAQ;MACdiD,IAAI,EAAE,QAAQ;MACdhB,KAAK,EAAE;KACR,CACF;EACH;;;uBA9HWC,gBAAgB,EAAA/D,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBxC,gBAAgB;MAAAyC,SAAA;MAAAC,MAAA;QAAAjG,WAAA;QAAAyC,gBAAA;QAAAiB,gBAAA;QAAAT,WAAA;QAAArB,gBAAA;QAAA+B,UAAA;MAAA;MAAAuC,UAAA;MAAAC,QAAA,GAAA3G,EAAA,CAAA4G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB7BlH,EAAA,CAAAC,cAAA,aAA0B;UAkBxBD,EAhBA,CAAAyB,UAAA,IAAA2F,+BAAA,kBAA8C,IAAAC,mCAAA,sBAgByC;UAiCnFrH,EAFJ,CAAAC,cAAA,iBAAmD,aACrB,SACtB;UAAAD,EAAA,CAAAE,SAAA,WAA4B;UAACF,EAAA,CAAAI,MAAA,yBAAiB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAErDH,EADF,CAAAC,cAAA,gBAAiE,WACzD;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAE,SAAA,YAAkC;UAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;UACNH,EAAA,CAAAC,cAAA,cAA+B;UAC7BD,EAAA,CAAAE,SAAA,8BAAiD;UAErDF,EADE,CAAAG,YAAA,EAAM,EACE;UAGVH,EAAA,CAAAyB,UAAA,KAAA6F,oCAAA,sBAAsF;UAmClFtH,EAFJ,CAAAC,cAAA,mBAAiC,cACH,UACtB;UAAAD,EAAA,CAAAE,SAAA,aAA2B;UAACF,EAAA,CAAAI,MAAA,qBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACjDH,EAAA,CAAAC,cAAA,kBAA+D;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UACzEJ,EADyE,CAAAG,YAAA,EAAS,EAC5E;UACNH,EAAA,CAAAE,SAAA,6BAA+C;UACjDF,EAAA,CAAAG,YAAA,EAAU;UAGVH,EAAA,CAAAyB,UAAA,KAAA8F,oCAAA,sBAAgE;UAuB5DvH,EAFJ,CAAAC,cAAA,mBAAiC,cACH,UACtB;UAAAD,EAAA,CAAAE,SAAA,aAA4B;UAACF,EAAA,CAAAI,MAAA,wBAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAChEJ,EADgE,CAAAG,YAAA,EAAS,EACnE;UACNH,EAAA,CAAAE,SAAA,2BAA2C;UAC7CF,EAAA,CAAAG,YAAA,EAAU;UAKNH,EAFJ,CAAAC,cAAA,mBAAiC,cACH,UACtB;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAACF,EAAA,CAAAI,MAAA,yBAAgB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,kBAA0D;UAA9BD,EAAA,CAAAiB,UAAA,mBAAAuG,mDAAA;YAAA,OAASL,GAAA,CAAAhC,iBAAA,EAAmB;UAAA,EAAC;UAACnF,EAAA,CAAAI,MAAA,gBAAQ;UACpEJ,EADoE,CAAAG,YAAA,EAAS,EACvE;UACNH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAyB,UAAA,KAAAgG,gCAAA,kBAC+D;UAKnEzH,EADE,CAAAG,YAAA,EAAM,EACE;UAKNH,EAFJ,CAAAC,cAAA,eAA4B,eACA,aACQ;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACzCH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACvCH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACzCH,EAAA,CAAAC,cAAA,aAA8B;UAAAD,EAAA,CAAAI,MAAA,WAAG;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACrCH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACvCH,EAAA,CAAAC,cAAA,aAAkC;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAC7CH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,MAAA,aAAK;UACvCJ,EADuC,CAAAG,YAAA,EAAI,EACrC;UAEJH,EADF,CAAAC,cAAA,eAAuB,YACf;UAAAD,EAAA,CAAAI,MAAA,4BAAe;UAG3BJ,EAH2B,CAAAG,YAAA,EAAO,EACxB,EACF,EACF;;;UAlKuBH,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAA3G,WAAA,CAAiB;UAgBQR,EAAA,CAAAK,SAAA,EAAiC;UAAjCL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAA/E,gBAAA,CAAAmC,MAAA,KAAiC;UA6ClCvE,EAAA,CAAAK,SAAA,IAAiC;UAAjCL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAAlE,gBAAA,CAAAsB,MAAA,KAAiC;UA0ClDvE,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAA1D,WAAA,CAAAc,MAAA,KAA4B;UAoChCvE,EAAA,CAAAK,SAAA,IAA2B;UAAAL,EAA3B,CAAAM,UAAA,YAAA6G,GAAA,CAAAf,oBAAA,GAA2B,iBAAAe,GAAA,CAAAhB,iBAAA,CAA0B;;;qBDhIjFxG,YAAY,EAAA+H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZhI,YAAY,EAAA0G,EAAA,CAAAuB,UAAA,EACZhI,0BAA0B,EAC1BC,yBAAyB,EACzBC,uBAAuB;MAAA+H,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}