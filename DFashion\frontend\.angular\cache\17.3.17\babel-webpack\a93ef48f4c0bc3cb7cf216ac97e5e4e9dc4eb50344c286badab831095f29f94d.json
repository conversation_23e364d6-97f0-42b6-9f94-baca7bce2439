{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\n// Angular Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDividerModule } from '@angular/material/divider';\n// Chart.js (commented out until ng2-charts is properly installed)\n// import { NgChartsModule } from 'ng2-charts';\n// Routing\nimport { AdminRoutingModule } from './admin-routing.module';\n// Services\nimport { AdminAuthService } from './services/admin-auth.service';\nimport { AdminApiService } from './services/admin-api.service';\n///import { UserService } from './services/user.service';\nimport { ProductService } from './services/product.service';\nimport { OrderService } from './services/order.service';\nimport { AnalyticsService } from './services/analytics.service';\n// Guards\nimport { AdminAuthGuard } from './guards/admin-auth.guard';\nimport { PermissionGuard } from './guards/permission.guard';\nimport * as i0 from \"@angular/core\";\nexport let AdminModule = /*#__PURE__*/(() => {\n  class AdminModule {\n    static {\n      this.ɵfac = function AdminModule_Factory(t) {\n        return new (t || AdminModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AdminModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [AdminAuthService, AdminApiService,\n        //UserService,\n        ProductService, OrderService, AnalyticsService, AdminAuthGuard, PermissionGuard],\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, AdminRoutingModule,\n        // NgChartsModule, // Commented out until ng2-charts is properly installed\n        // Angular Material Modules\n        MatToolbarModule, MatSidenavModule, MatListModule, MatIconModule, MatButtonModule, MatCardModule, MatTableModule, MatPaginatorModule, MatSortModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatChipsModule, MatTabsModule, MatMenuModule, MatBadgeModule, MatSlideToggleModule, MatCheckboxModule, MatRadioModule, MatExpansionModule, MatStepperModule, MatTooltipModule, MatDividerModule]\n      });\n    }\n  }\n  return AdminModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}