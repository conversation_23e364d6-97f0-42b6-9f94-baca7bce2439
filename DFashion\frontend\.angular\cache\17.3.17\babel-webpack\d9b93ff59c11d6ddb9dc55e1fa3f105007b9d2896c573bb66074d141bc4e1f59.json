{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/tabs\";\nimport * as i10 from \"@angular/material/checkbox\";\nexport class SettingsComponent {\n  constructor(fb, snackBar) {\n    this.fb = fb;\n    this.snackBar = snackBar;\n  }\n  ngOnInit() {\n    this.createForms();\n    this.loadSettings();\n  }\n  createForms() {\n    this.generalForm = this.fb.group({\n      appName: ['DFashion', Validators.required],\n      companyName: ['DFashion Inc.', Validators.required],\n      supportEmail: ['<EMAIL>', [Validators.required, Validators.email]],\n      defaultCurrency: ['INR', Validators.required],\n      timezone: ['Asia/Kolkata', Validators.required]\n    });\n    this.ecommerceForm = this.fb.group({\n      storeName: ['DFashion Store', Validators.required],\n      storeDescription: ['Your one-stop fashion destination'],\n      taxRate: [18, [Validators.required, Validators.min(0), Validators.max(100)]],\n      shippingFee: [99, [Validators.required, Validators.min(0)]],\n      freeShippingThreshold: [999, [Validators.required, Validators.min(0)]],\n      enableInventoryTracking: [true],\n      enableReviews: [true],\n      enableWishlist: [true]\n    });\n    this.notificationForm = this.fb.group({\n      emailNewOrders: [true],\n      emailLowStock: [true],\n      emailNewUsers: [false],\n      emailReviews: [false],\n      pushNewOrders: [true],\n      pushUrgentAlerts: [true],\n      pushDailyReports: [false]\n    });\n    this.securityForm = this.fb.group({\n      sessionTimeout: [30, [Validators.required, Validators.min(5), Validators.max(480)]],\n      passwordMinLength: [8, [Validators.required, Validators.min(6), Validators.max(20)]],\n      requireTwoFactor: [false],\n      enableLoginAttempts: [true],\n      enableAuditLog: [true]\n    });\n  }\n  loadSettings() {\n    // In a real app, load settings from the backend\n    console.log('Loading settings...');\n  }\n  saveGeneralSettings() {\n    if (this.generalForm.valid) {\n      console.log('Saving general settings:', this.generalForm.value);\n      this.showSuccessMessage('General settings saved successfully');\n    }\n  }\n  saveEcommerceSettings() {\n    if (this.ecommerceForm.valid) {\n      console.log('Saving e-commerce settings:', this.ecommerceForm.value);\n      this.showSuccessMessage('E-commerce settings saved successfully');\n    }\n  }\n  saveNotificationSettings() {\n    if (this.notificationForm.valid) {\n      console.log('Saving notification settings:', this.notificationForm.value);\n      this.showSuccessMessage('Notification settings saved successfully');\n    }\n  }\n  saveSecuritySettings() {\n    if (this.securityForm.valid) {\n      console.log('Saving security settings:', this.securityForm.value);\n      this.showSuccessMessage('Security settings saved successfully');\n    }\n  }\n  showSuccessMessage(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  static {\n    this.ɵfac = function SettingsComponent_Factory(t) {\n      return new (t || SettingsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingsComponent,\n      selectors: [[\"app-settings\"]],\n      decls: 153,\n      vars: 4,\n      consts: [[1, \"settings-container\"], [1, \"settings-header\"], [\"label\", \"General\"], [1, \"tab-content\"], [1, \"settings-form\", 3, \"formGroup\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"appName\"], [\"matInput\", \"\", \"formControlName\", \"companyName\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"supportEmail\"], [\"formControlName\", \"defaultCurrency\"], [\"value\", \"INR\"], [\"value\", \"USD\"], [\"value\", \"EUR\"], [\"formControlName\", \"timezone\"], [\"value\", \"Asia/Kolkata\"], [\"value\", \"America/New_York\"], [\"value\", \"Europe/London\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"label\", \"E-commerce\"], [\"matInput\", \"\", \"formControlName\", \"storeName\"], [\"matInput\", \"\", \"rows\", \"3\", \"formControlName\", \"storeDescription\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"taxRate\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"shippingFee\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"freeShippingThreshold\"], [1, \"checkbox-group\"], [\"formControlName\", \"enableInventoryTracking\"], [\"formControlName\", \"enableReviews\"], [\"formControlName\", \"enableWishlist\"], [\"label\", \"Notifications\"], [\"formControlName\", \"emailNewOrders\"], [\"formControlName\", \"emailLowStock\"], [\"formControlName\", \"emailNewUsers\"], [\"formControlName\", \"emailReviews\"], [\"formControlName\", \"pushNewOrders\"], [\"formControlName\", \"pushUrgentAlerts\"], [\"formControlName\", \"pushDailyReports\"], [\"label\", \"Security\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"sessionTimeout\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"passwordMinLength\"], [\"formControlName\", \"requireTwoFactor\"], [\"formControlName\", \"enableLoginAttempts\"], [\"formControlName\", \"enableAuditLog\"]],\n      template: function SettingsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Manage your application settings and preferences\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-tab-group\")(7, \"mat-tab\", 2)(8, \"div\", 3)(9, \"mat-card\")(10, \"mat-card-header\")(11, \"mat-card-title\");\n          i0.ɵɵtext(12, \"General Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-card-subtitle\");\n          i0.ɵɵtext(14, \"Basic application configuration\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"form\", 4)(17, \"mat-form-field\", 5)(18, \"mat-label\");\n          i0.ɵɵtext(19, \"Application Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-form-field\", 5)(22, \"mat-label\");\n          i0.ɵɵtext(23, \"Company Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-form-field\", 5)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Support Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-form-field\", 5)(30, \"mat-label\");\n          i0.ɵɵtext(31, \"Default Currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-select\", 9)(33, \"mat-option\", 10);\n          i0.ɵɵtext(34, \"Indian Rupee (\\u20B9)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"mat-option\", 11);\n          i0.ɵɵtext(36, \"US Dollar ($)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-option\", 12);\n          i0.ɵɵtext(38, \"Euro (\\u20AC)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 5)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Timezone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"mat-select\", 13)(43, \"mat-option\", 14);\n          i0.ɵɵtext(44, \"Asia/Kolkata\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-option\", 15);\n          i0.ɵɵtext(46, \"America/New_York\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-option\", 16);\n          i0.ɵɵtext(48, \"Europe/London\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"div\", 17)(50, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_50_listener() {\n            return ctx.saveGeneralSettings();\n          });\n          i0.ɵɵtext(51, \" Save Changes \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(52, \"mat-tab\", 19)(53, \"div\", 3)(54, \"mat-card\")(55, \"mat-card-header\")(56, \"mat-card-title\");\n          i0.ɵɵtext(57, \"E-commerce Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"mat-card-subtitle\");\n          i0.ɵɵtext(59, \"Configure your online store\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"mat-card-content\")(61, \"form\", 4)(62, \"mat-form-field\", 5)(63, \"mat-label\");\n          i0.ɵɵtext(64, \"Store Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"mat-form-field\", 5)(67, \"mat-label\");\n          i0.ɵɵtext(68, \"Store Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"textarea\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"mat-form-field\", 5)(71, \"mat-label\");\n          i0.ɵɵtext(72, \"Default Tax Rate (%)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(73, \"input\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"mat-form-field\", 5)(75, \"mat-label\");\n          i0.ɵɵtext(76, \"Shipping Fee\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(77, \"input\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"mat-form-field\", 5)(79, \"mat-label\");\n          i0.ɵɵtext(80, \"Free Shipping Threshold\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"input\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 25)(83, \"mat-checkbox\", 26);\n          i0.ɵɵtext(84, \" Enable Inventory Tracking \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-checkbox\", 27);\n          i0.ɵɵtext(86, \" Enable Product Reviews \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"mat-checkbox\", 28);\n          i0.ɵɵtext(88, \" Enable Wishlist \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 17)(90, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_90_listener() {\n            return ctx.saveEcommerceSettings();\n          });\n          i0.ɵɵtext(91, \" Save Changes \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(92, \"mat-tab\", 29)(93, \"div\", 3)(94, \"mat-card\")(95, \"mat-card-header\")(96, \"mat-card-title\");\n          i0.ɵɵtext(97, \"Notification Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"mat-card-subtitle\");\n          i0.ɵɵtext(99, \"Configure email and push notifications\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"mat-card-content\")(101, \"form\", 4)(102, \"h3\");\n          i0.ɵɵtext(103, \"Email Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 25)(105, \"mat-checkbox\", 30);\n          i0.ɵɵtext(106, \" New Orders \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"mat-checkbox\", 31);\n          i0.ɵɵtext(108, \" Low Stock Alerts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"mat-checkbox\", 32);\n          i0.ɵɵtext(110, \" New User Registrations \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"mat-checkbox\", 33);\n          i0.ɵɵtext(112, \" New Product Reviews \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"h3\");\n          i0.ɵɵtext(114, \"Push Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\", 25)(116, \"mat-checkbox\", 34);\n          i0.ɵɵtext(117, \" New Orders \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"mat-checkbox\", 35);\n          i0.ɵɵtext(119, \" Urgent System Alerts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"mat-checkbox\", 36);\n          i0.ɵɵtext(121, \" Daily Reports \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(122, \"div\", 17)(123, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_123_listener() {\n            return ctx.saveNotificationSettings();\n          });\n          i0.ɵɵtext(124, \" Save Changes \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(125, \"mat-tab\", 37)(126, \"div\", 3)(127, \"mat-card\")(128, \"mat-card-header\")(129, \"mat-card-title\");\n          i0.ɵɵtext(130, \"Security Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"mat-card-subtitle\");\n          i0.ɵɵtext(132, \"Manage security and access controls\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(133, \"mat-card-content\")(134, \"form\", 4)(135, \"mat-form-field\", 5)(136, \"mat-label\");\n          i0.ɵɵtext(137, \"Session Timeout (minutes)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(138, \"input\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"mat-form-field\", 5)(140, \"mat-label\");\n          i0.ɵɵtext(141, \"Password Minimum Length\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(142, \"input\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"div\", 25)(144, \"mat-checkbox\", 40);\n          i0.ɵɵtext(145, \" Require Two-Factor Authentication \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"mat-checkbox\", 41);\n          i0.ɵɵtext(147, \" Enable Login Attempt Limits \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(148, \"mat-checkbox\", 42);\n          i0.ɵɵtext(149, \" Enable Audit Logging \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(150, \"div\", 17)(151, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_151_listener() {\n            return ctx.saveSecuritySettings();\n          });\n          i0.ɵɵtext(152, \" Save Changes \");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"formGroup\", ctx.generalForm);\n          i0.ɵɵadvance(45);\n          i0.ɵɵproperty(\"formGroup\", ctx.ecommerceForm);\n          i0.ɵɵadvance(40);\n          i0.ɵɵproperty(\"formGroup\", ctx.notificationForm);\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"formGroup\", ctx.securityForm);\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardSubtitle, i4.MatCardTitle, i5.MatInput, i6.MatFormField, i6.MatLabel, i7.MatSelect, i8.MatOption, i9.MatTab, i9.MatTabGroup, i10.MatCheckbox],\n      styles: [\".settings-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n}\\n\\n.settings-header[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.settings-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.settings-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem 0;\\n}\\n\\n.settings-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n  max-width: 600px;\\n}\\n.settings-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.settings-form[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0 1rem 0;\\n  color: #333;\\n  font-size: 1.125rem;\\n  font-weight: 500;\\n}\\n.settings-form[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  margin: 1rem 0;\\n}\\n.checkbox-group[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\nmat-card[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\nmat-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\nmat-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 500;\\n}\\nmat-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n  margin-top: 0.25rem;\\n}\\n\\nmat-tab-group[_ngcontent-%COMP%]     .mat-tab-label {\\n  min-width: 120px;\\n}\\nmat-tab-group[_ngcontent-%COMP%]     .mat-tab-body-content {\\n  overflow: visible;\\n}\\n\\n@media (max-width: 768px) {\\n  .settings-container[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .settings-form[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n  mat-tab-group[_ngcontent-%COMP%]     .mat-tab-label {\\n    min-width: auto;\\n    padding: 0 12px;\\n  }\\n  mat-tab-group[_ngcontent-%COMP%]     .mat-tab-label-content {\\n    font-size: 0.875rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "SettingsComponent", "constructor", "fb", "snackBar", "ngOnInit", "createForms", "loadSettings", "generalForm", "group", "appName", "required", "companyName", "supportEmail", "email", "defaultCurrency", "timezone", "ecommerceForm", "storeName", "storeDescription", "taxRate", "min", "max", "shippingFee", "freeShippingThreshold", "enableInventoryTracking", "enableReviews", "enableWishlist", "notificationForm", "emailNewOrders", "emailLowStock", "emailNewUsers", "emailReviews", "pushNewOrders", "pushUrgent<PERSON><PERSON><PERSON>", "pushDailyReports", "securityForm", "sessionTimeout", "<PERSON><PERSON>in<PERSON><PERSON><PERSON>", "requireTwoFactor", "enableLoginAttempts", "enableAuditLog", "console", "log", "saveGeneralSettings", "valid", "value", "showSuccessMessage", "saveEcommerceSettings", "saveNotificationSettings", "saveSecuritySettings", "message", "open", "duration", "panelClass", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "SettingsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "SettingsComponent_Template_button_click_50_listener", "SettingsComponent_Template_button_click_90_listener", "SettingsComponent_Template_button_click_123_listener", "SettingsComponent_Template_button_click_151_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\settings\\settings.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-settings',\n  template: `\n    <div class=\"settings-container\">\n      <div class=\"settings-header\">\n        <h1>Settings</h1>\n        <p>Manage your application settings and preferences</p>\n      </div>\n\n      <mat-tab-group>\n        <!-- General Settings -->\n        <mat-tab label=\"General\">\n          <div class=\"tab-content\">\n            <mat-card>\n              <mat-card-header>\n                <mat-card-title>General Settings</mat-card-title>\n                <mat-card-subtitle>Basic application configuration</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <form [formGroup]=\"generalForm\" class=\"settings-form\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Application Name</mat-label>\n                    <input matInput formControlName=\"appName\">\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Company Name</mat-label>\n                    <input matInput formControlName=\"companyName\">\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Support Email</mat-label>\n                    <input matInput type=\"email\" formControlName=\"supportEmail\">\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Default Currency</mat-label>\n                    <mat-select formControlName=\"defaultCurrency\">\n                      <mat-option value=\"INR\">Indian Rupee (₹)</mat-option>\n                      <mat-option value=\"USD\">US Dollar ($)</mat-option>\n                      <mat-option value=\"EUR\">Euro (€)</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Timezone</mat-label>\n                    <mat-select formControlName=\"timezone\">\n                      <mat-option value=\"Asia/Kolkata\">Asia/Kolkata</mat-option>\n                      <mat-option value=\"America/New_York\">America/New_York</mat-option>\n                      <mat-option value=\"Europe/London\">Europe/London</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n\n                  <div class=\"form-actions\">\n                    <button mat-raised-button color=\"primary\" (click)=\"saveGeneralSettings()\">\n                      Save Changes\n                    </button>\n                  </div>\n                </form>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </mat-tab>\n\n        <!-- E-commerce Settings -->\n        <mat-tab label=\"E-commerce\">\n          <div class=\"tab-content\">\n            <mat-card>\n              <mat-card-header>\n                <mat-card-title>E-commerce Settings</mat-card-title>\n                <mat-card-subtitle>Configure your online store</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <form [formGroup]=\"ecommerceForm\" class=\"settings-form\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Store Name</mat-label>\n                    <input matInput formControlName=\"storeName\">\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Store Description</mat-label>\n                    <textarea matInput rows=\"3\" formControlName=\"storeDescription\"></textarea>\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Default Tax Rate (%)</mat-label>\n                    <input matInput type=\"number\" formControlName=\"taxRate\">\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Shipping Fee</mat-label>\n                    <input matInput type=\"number\" formControlName=\"shippingFee\">\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Free Shipping Threshold</mat-label>\n                    <input matInput type=\"number\" formControlName=\"freeShippingThreshold\">\n                  </mat-form-field>\n\n                  <div class=\"checkbox-group\">\n                    <mat-checkbox formControlName=\"enableInventoryTracking\">\n                      Enable Inventory Tracking\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"enableReviews\">\n                      Enable Product Reviews\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"enableWishlist\">\n                      Enable Wishlist\n                    </mat-checkbox>\n                  </div>\n\n                  <div class=\"form-actions\">\n                    <button mat-raised-button color=\"primary\" (click)=\"saveEcommerceSettings()\">\n                      Save Changes\n                    </button>\n                  </div>\n                </form>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </mat-tab>\n\n        <!-- Notifications -->\n        <mat-tab label=\"Notifications\">\n          <div class=\"tab-content\">\n            <mat-card>\n              <mat-card-header>\n                <mat-card-title>Notification Settings</mat-card-title>\n                <mat-card-subtitle>Configure email and push notifications</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <form [formGroup]=\"notificationForm\" class=\"settings-form\">\n                  <h3>Email Notifications</h3>\n                  <div class=\"checkbox-group\">\n                    <mat-checkbox formControlName=\"emailNewOrders\">\n                      New Orders\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"emailLowStock\">\n                      Low Stock Alerts\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"emailNewUsers\">\n                      New User Registrations\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"emailReviews\">\n                      New Product Reviews\n                    </mat-checkbox>\n                  </div>\n\n                  <h3>Push Notifications</h3>\n                  <div class=\"checkbox-group\">\n                    <mat-checkbox formControlName=\"pushNewOrders\">\n                      New Orders\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"pushUrgentAlerts\">\n                      Urgent System Alerts\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"pushDailyReports\">\n                      Daily Reports\n                    </mat-checkbox>\n                  </div>\n\n                  <div class=\"form-actions\">\n                    <button mat-raised-button color=\"primary\" (click)=\"saveNotificationSettings()\">\n                      Save Changes\n                    </button>\n                  </div>\n                </form>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </mat-tab>\n\n        <!-- Security -->\n        <mat-tab label=\"Security\">\n          <div class=\"tab-content\">\n            <mat-card>\n              <mat-card-header>\n                <mat-card-title>Security Settings</mat-card-title>\n                <mat-card-subtitle>Manage security and access controls</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <form [formGroup]=\"securityForm\" class=\"settings-form\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Session Timeout (minutes)</mat-label>\n                    <input matInput type=\"number\" formControlName=\"sessionTimeout\">\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Password Minimum Length</mat-label>\n                    <input matInput type=\"number\" formControlName=\"passwordMinLength\">\n                  </mat-form-field>\n\n                  <div class=\"checkbox-group\">\n                    <mat-checkbox formControlName=\"requireTwoFactor\">\n                      Require Two-Factor Authentication\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"enableLoginAttempts\">\n                      Enable Login Attempt Limits\n                    </mat-checkbox>\n                    <mat-checkbox formControlName=\"enableAuditLog\">\n                      Enable Audit Logging\n                    </mat-checkbox>\n                  </div>\n\n                  <div class=\"form-actions\">\n                    <button mat-raised-button color=\"primary\" (click)=\"saveSecuritySettings()\">\n                      Save Changes\n                    </button>\n                  </div>\n                </form>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styleUrls: ['./settings.component.scss']\n})\nexport class SettingsComponent implements OnInit {\n  generalForm!: FormGroup;\n  ecommerceForm!: FormGroup;\n  notificationForm!: FormGroup;\n  securityForm!: FormGroup;\n\n  constructor(\n    private fb: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.createForms();\n    this.loadSettings();\n  }\n\n  createForms(): void {\n    this.generalForm = this.fb.group({\n      appName: ['DFashion', Validators.required],\n      companyName: ['DFashion Inc.', Validators.required],\n      supportEmail: ['<EMAIL>', [Validators.required, Validators.email]],\n      defaultCurrency: ['INR', Validators.required],\n      timezone: ['Asia/Kolkata', Validators.required]\n    });\n\n    this.ecommerceForm = this.fb.group({\n      storeName: ['DFashion Store', Validators.required],\n      storeDescription: ['Your one-stop fashion destination'],\n      taxRate: [18, [Validators.required, Validators.min(0), Validators.max(100)]],\n      shippingFee: [99, [Validators.required, Validators.min(0)]],\n      freeShippingThreshold: [999, [Validators.required, Validators.min(0)]],\n      enableInventoryTracking: [true],\n      enableReviews: [true],\n      enableWishlist: [true]\n    });\n\n    this.notificationForm = this.fb.group({\n      emailNewOrders: [true],\n      emailLowStock: [true],\n      emailNewUsers: [false],\n      emailReviews: [false],\n      pushNewOrders: [true],\n      pushUrgentAlerts: [true],\n      pushDailyReports: [false]\n    });\n\n    this.securityForm = this.fb.group({\n      sessionTimeout: [30, [Validators.required, Validators.min(5), Validators.max(480)]],\n      passwordMinLength: [8, [Validators.required, Validators.min(6), Validators.max(20)]],\n      requireTwoFactor: [false],\n      enableLoginAttempts: [true],\n      enableAuditLog: [true]\n    });\n  }\n\n  loadSettings(): void {\n    // In a real app, load settings from the backend\n    console.log('Loading settings...');\n  }\n\n  saveGeneralSettings(): void {\n    if (this.generalForm.valid) {\n      console.log('Saving general settings:', this.generalForm.value);\n      this.showSuccessMessage('General settings saved successfully');\n    }\n  }\n\n  saveEcommerceSettings(): void {\n    if (this.ecommerceForm.valid) {\n      console.log('Saving e-commerce settings:', this.ecommerceForm.value);\n      this.showSuccessMessage('E-commerce settings saved successfully');\n    }\n  }\n\n  saveNotificationSettings(): void {\n    if (this.notificationForm.valid) {\n      console.log('Saving notification settings:', this.notificationForm.value);\n      this.showSuccessMessage('Notification settings saved successfully');\n    }\n  }\n\n  saveSecuritySettings(): void {\n    if (this.securityForm.valid) {\n      console.log('Saving security settings:', this.securityForm.value);\n      this.showSuccessMessage('Security settings saved successfully');\n    }\n  }\n\n  private showSuccessMessage(message: string): void {\n    this.snackBar.open(message, 'Close', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n}\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;AA8NnE,OAAM,MAAOC,iBAAiB;EAM5BC,YACUC,EAAe,EACfC,QAAqB;IADrB,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;EACf;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACE,WAAW,GAAG,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,UAAU,EAAEV,UAAU,CAACW,QAAQ,CAAC;MAC1CC,WAAW,EAAE,CAAC,eAAe,EAAEZ,UAAU,CAACW,QAAQ,CAAC;MACnDE,YAAY,EAAE,CAAC,sBAAsB,EAAE,CAACb,UAAU,CAACW,QAAQ,EAAEX,UAAU,CAACc,KAAK,CAAC,CAAC;MAC/EC,eAAe,EAAE,CAAC,KAAK,EAAEf,UAAU,CAACW,QAAQ,CAAC;MAC7CK,QAAQ,EAAE,CAAC,cAAc,EAAEhB,UAAU,CAACW,QAAQ;KAC/C,CAAC;IAEF,IAAI,CAACM,aAAa,GAAG,IAAI,CAACd,EAAE,CAACM,KAAK,CAAC;MACjCS,SAAS,EAAE,CAAC,gBAAgB,EAAElB,UAAU,CAACW,QAAQ,CAAC;MAClDQ,gBAAgB,EAAE,CAAC,mCAAmC,CAAC;MACvDC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACW,QAAQ,EAAEX,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,EAAErB,UAAU,CAACsB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5EC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACW,QAAQ,EAAEX,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DG,qBAAqB,EAAE,CAAC,GAAG,EAAE,CAACxB,UAAU,CAACW,QAAQ,EAAEX,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACtEI,uBAAuB,EAAE,CAAC,IAAI,CAAC;MAC/BC,aAAa,EAAE,CAAC,IAAI,CAAC;MACrBC,cAAc,EAAE,CAAC,IAAI;KACtB,CAAC;IAEF,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACzB,EAAE,CAACM,KAAK,CAAC;MACpCoB,cAAc,EAAE,CAAC,IAAI,CAAC;MACtBC,aAAa,EAAE,CAAC,IAAI,CAAC;MACrBC,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,YAAY,EAAE,CAAC,KAAK,CAAC;MACrBC,aAAa,EAAE,CAAC,IAAI,CAAC;MACrBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;MACxBC,gBAAgB,EAAE,CAAC,KAAK;KACzB,CAAC;IAEF,IAAI,CAACC,YAAY,GAAG,IAAI,CAACjC,EAAE,CAACM,KAAK,CAAC;MAChC4B,cAAc,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACW,QAAQ,EAAEX,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,EAAErB,UAAU,CAACsB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACnFgB,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAACtC,UAAU,CAACW,QAAQ,EAAEX,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,EAAErB,UAAU,CAACsB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACpFiB,gBAAgB,EAAE,CAAC,KAAK,CAAC;MACzBC,mBAAmB,EAAE,CAAC,IAAI,CAAC;MAC3BC,cAAc,EAAE,CAAC,IAAI;KACtB,CAAC;EACJ;EAEAlC,YAAYA,CAAA;IACV;IACAmC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACpC,WAAW,CAACqC,KAAK,EAAE;MAC1BH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACnC,WAAW,CAACsC,KAAK,CAAC;MAC/D,IAAI,CAACC,kBAAkB,CAAC,qCAAqC,CAAC;;EAElE;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAC/B,aAAa,CAAC4B,KAAK,EAAE;MAC5BH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC1B,aAAa,CAAC6B,KAAK,CAAC;MACpE,IAAI,CAACC,kBAAkB,CAAC,wCAAwC,CAAC;;EAErE;EAEAE,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACrB,gBAAgB,CAACiB,KAAK,EAAE;MAC/BH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACf,gBAAgB,CAACkB,KAAK,CAAC;MACzE,IAAI,CAACC,kBAAkB,CAAC,0CAA0C,CAAC;;EAEvE;EAEAG,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACd,YAAY,CAACS,KAAK,EAAE;MAC3BH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACP,YAAY,CAACU,KAAK,CAAC;MACjE,IAAI,CAACC,kBAAkB,CAAC,sCAAsC,CAAC;;EAEnE;EAEQA,kBAAkBA,CAACI,OAAe;IACxC,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;MACnCE,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;;;uBA7FWrD,iBAAiB,EAAAsD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjB3D,iBAAiB;MAAA4D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtNtBZ,EAFJ,CAAAc,cAAA,aAAgC,aACD,SACvB;UAAAd,EAAA,CAAAe,MAAA,eAAQ;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACjBhB,EAAA,CAAAc,cAAA,QAAG;UAAAd,EAAA,CAAAe,MAAA,uDAAgD;UACrDf,EADqD,CAAAgB,YAAA,EAAI,EACnD;UAQIhB,EANV,CAAAc,cAAA,oBAAe,iBAEY,aACE,eACb,uBACS,sBACC;UAAAd,EAAA,CAAAe,MAAA,wBAAgB;UAAAf,EAAA,CAAAgB,YAAA,EAAiB;UACjDhB,EAAA,CAAAc,cAAA,yBAAmB;UAAAd,EAAA,CAAAe,MAAA,uCAA+B;UACpDf,EADoD,CAAAgB,YAAA,EAAoB,EACtD;UAIZhB,EAHN,CAAAc,cAAA,wBAAkB,eACsC,yBACf,iBACxB;UAAAd,EAAA,CAAAe,MAAA,wBAAgB;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UACvChB,EAAA,CAAAiB,SAAA,gBAA0C;UAC5CjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,oBAAY;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UACnChB,EAAA,CAAAiB,SAAA,gBAA8C;UAChDjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,qBAAa;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UACpChB,EAAA,CAAAiB,SAAA,gBAA4D;UAC9DjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,wBAAgB;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UAErChB,EADF,CAAAc,cAAA,qBAA8C,sBACpB;UAAAd,EAAA,CAAAe,MAAA,6BAAgB;UAAAf,EAAA,CAAAgB,YAAA,EAAa;UACrDhB,EAAA,CAAAc,cAAA,sBAAwB;UAAAd,EAAA,CAAAe,MAAA,qBAAa;UAAAf,EAAA,CAAAgB,YAAA,EAAa;UAClDhB,EAAA,CAAAc,cAAA,sBAAwB;UAAAd,EAAA,CAAAe,MAAA,qBAAQ;UAEpCf,EAFoC,CAAAgB,YAAA,EAAa,EAClC,EACE;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UAE7BhB,EADF,CAAAc,cAAA,sBAAuC,sBACJ;UAAAd,EAAA,CAAAe,MAAA,oBAAY;UAAAf,EAAA,CAAAgB,YAAA,EAAa;UAC1DhB,EAAA,CAAAc,cAAA,sBAAqC;UAAAd,EAAA,CAAAe,MAAA,wBAAgB;UAAAf,EAAA,CAAAgB,YAAA,EAAa;UAClEhB,EAAA,CAAAc,cAAA,sBAAkC;UAAAd,EAAA,CAAAe,MAAA,qBAAa;UAEnDf,EAFmD,CAAAgB,YAAA,EAAa,EACjD,EACE;UAGfhB,EADF,CAAAc,cAAA,eAA0B,kBACkD;UAAhCd,EAAA,CAAAkB,UAAA,mBAAAC,oDAAA;YAAA,OAASN,GAAA,CAAAxB,mBAAA,EAAqB;UAAA,EAAC;UACvEW,EAAA,CAAAe,MAAA,sBACF;UAMZf,EANY,CAAAgB,YAAA,EAAS,EACL,EACD,EACU,EACV,EACP,EACE;UAOFhB,EAJR,CAAAc,cAAA,mBAA4B,cACD,gBACb,uBACS,sBACC;UAAAd,EAAA,CAAAe,MAAA,2BAAmB;UAAAf,EAAA,CAAAgB,YAAA,EAAiB;UACpDhB,EAAA,CAAAc,cAAA,yBAAmB;UAAAd,EAAA,CAAAe,MAAA,mCAA2B;UAChDf,EADgD,CAAAgB,YAAA,EAAoB,EAClD;UAIZhB,EAHN,CAAAc,cAAA,wBAAkB,eACwC,yBACjB,iBACxB;UAAAd,EAAA,CAAAe,MAAA,kBAAU;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UACjChB,EAAA,CAAAiB,SAAA,iBAA4C;UAC9CjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,yBAAiB;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UACxChB,EAAA,CAAAiB,SAAA,oBAA0E;UAC5EjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,4BAAoB;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UAC3ChB,EAAA,CAAAiB,SAAA,iBAAwD;UAC1DjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,oBAAY;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UACnChB,EAAA,CAAAiB,SAAA,iBAA4D;UAC9DjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,yBAAqC,iBACxB;UAAAd,EAAA,CAAAe,MAAA,+BAAuB;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UAC9ChB,EAAA,CAAAiB,SAAA,iBAAsE;UACxEjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,eAA4B,wBAC8B;UACtDd,EAAA,CAAAe,MAAA,mCACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,wBAA8C;UAC5Cd,EAAA,CAAAe,MAAA,gCACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,wBAA+C;UAC7Cd,EAAA,CAAAe,MAAA,yBACF;UACFf,EADE,CAAAgB,YAAA,EAAe,EACX;UAGJhB,EADF,CAAAc,cAAA,eAA0B,kBACoD;UAAlCd,EAAA,CAAAkB,UAAA,mBAAAE,oDAAA;YAAA,OAASP,GAAA,CAAApB,qBAAA,EAAuB;UAAA,EAAC;UACzEO,EAAA,CAAAe,MAAA,sBACF;UAMZf,EANY,CAAAgB,YAAA,EAAS,EACL,EACD,EACU,EACV,EACP,EACE;UAOFhB,EAJR,CAAAc,cAAA,mBAA+B,cACJ,gBACb,uBACS,sBACC;UAAAd,EAAA,CAAAe,MAAA,6BAAqB;UAAAf,EAAA,CAAAgB,YAAA,EAAiB;UACtDhB,EAAA,CAAAc,cAAA,yBAAmB;UAAAd,EAAA,CAAAe,MAAA,8CAAsC;UAC3Df,EAD2D,CAAAgB,YAAA,EAAoB,EAC7D;UAGdhB,EAFJ,CAAAc,cAAA,yBAAkB,gBAC2C,WACrD;UAAAd,EAAA,CAAAe,MAAA,4BAAmB;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAE1BhB,EADF,CAAAc,cAAA,gBAA4B,yBACqB;UAC7Cd,EAAA,CAAAe,MAAA,qBACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,yBAA8C;UAC5Cd,EAAA,CAAAe,MAAA,2BACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,yBAA8C;UAC5Cd,EAAA,CAAAe,MAAA,iCACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,yBAA6C;UAC3Cd,EAAA,CAAAe,MAAA,8BACF;UACFf,EADE,CAAAgB,YAAA,EAAe,EACX;UAENhB,EAAA,CAAAc,cAAA,WAAI;UAAAd,EAAA,CAAAe,MAAA,2BAAkB;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAEzBhB,EADF,CAAAc,cAAA,gBAA4B,yBACoB;UAC5Cd,EAAA,CAAAe,MAAA,qBACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,yBAAiD;UAC/Cd,EAAA,CAAAe,MAAA,+BACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,yBAAiD;UAC/Cd,EAAA,CAAAe,MAAA,wBACF;UACFf,EADE,CAAAgB,YAAA,EAAe,EACX;UAGJhB,EADF,CAAAc,cAAA,gBAA0B,mBACuD;UAArCd,EAAA,CAAAkB,UAAA,mBAAAG,qDAAA;YAAA,OAASR,GAAA,CAAAnB,wBAAA,EAA0B;UAAA,EAAC;UAC5EM,EAAA,CAAAe,MAAA,uBACF;UAMZf,EANY,CAAAgB,YAAA,EAAS,EACL,EACD,EACU,EACV,EACP,EACE;UAOFhB,EAJR,CAAAc,cAAA,oBAA0B,eACC,iBACb,wBACS,uBACC;UAAAd,EAAA,CAAAe,MAAA,0BAAiB;UAAAf,EAAA,CAAAgB,YAAA,EAAiB;UAClDhB,EAAA,CAAAc,cAAA,0BAAmB;UAAAd,EAAA,CAAAe,MAAA,4CAAmC;UACxDf,EADwD,CAAAgB,YAAA,EAAoB,EAC1D;UAIZhB,EAHN,CAAAc,cAAA,yBAAkB,gBACuC,0BAChB,kBACxB;UAAAd,EAAA,CAAAe,MAAA,kCAAyB;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UAChDhB,EAAA,CAAAiB,SAAA,kBAA+D;UACjEjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,0BAAqC,kBACxB;UAAAd,EAAA,CAAAe,MAAA,gCAAuB;UAAAf,EAAA,CAAAgB,YAAA,EAAY;UAC9ChB,EAAA,CAAAiB,SAAA,kBAAkE;UACpEjB,EAAA,CAAAgB,YAAA,EAAiB;UAGfhB,EADF,CAAAc,cAAA,gBAA4B,yBACuB;UAC/Cd,EAAA,CAAAe,MAAA,4CACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,yBAAoD;UAClDd,EAAA,CAAAe,MAAA,sCACF;UAAAf,EAAA,CAAAgB,YAAA,EAAe;UACfhB,EAAA,CAAAc,cAAA,yBAA+C;UAC7Cd,EAAA,CAAAe,MAAA,+BACF;UACFf,EADE,CAAAgB,YAAA,EAAe,EACX;UAGJhB,EADF,CAAAc,cAAA,gBAA0B,mBACmD;UAAjCd,EAAA,CAAAkB,UAAA,mBAAAI,qDAAA;YAAA,OAAST,GAAA,CAAAlB,oBAAA,EAAsB;UAAA,EAAC;UACxEK,EAAA,CAAAe,MAAA,uBACF;UAQhBf,EARgB,CAAAgB,YAAA,EAAS,EACL,EACD,EACU,EACV,EACP,EACE,EACI,EACZ;;;UApMYhB,EAAA,CAAAuB,SAAA,IAAyB;UAAzBvB,EAAA,CAAAwB,UAAA,cAAAX,GAAA,CAAA5D,WAAA,CAAyB;UAsDzB+C,EAAA,CAAAuB,SAAA,IAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,cAAAX,GAAA,CAAAnD,aAAA,CAA2B;UA0D3BsC,EAAA,CAAAuB,SAAA,IAA8B;UAA9BvB,EAAA,CAAAwB,UAAA,cAAAX,GAAA,CAAAxC,gBAAA,CAA8B;UAkD9B2B,EAAA,CAAAuB,SAAA,IAA0B;UAA1BvB,EAAA,CAAAwB,UAAA,cAAAX,GAAA,CAAAhC,YAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}