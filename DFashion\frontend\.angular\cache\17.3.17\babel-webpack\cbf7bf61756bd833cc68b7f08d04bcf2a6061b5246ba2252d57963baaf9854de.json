{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nfunction FeaturedBrandsComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeaturedBrandsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_12_div_2_Template, 4, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Trending\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"i\", 4);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", brand_r2.rating, \"/5\");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Est. \", brand_r2.establishedYear, \"\");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template_span_click_0_listener($event) {\n      const category_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const brand_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.navigateToCategory(category_r5, brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, category_r5), \" \");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_17_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", brand_r2.categories.length - 3, \" more \");\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template, 3, 3, \"span\", 46)(2, FeaturedBrandsComponent_div_13_div_1_div_17_span_2_Template, 2, 1, \"span\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", brand_r2.categories.slice(0, 3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.categories.length > 3);\n  }\n}\nfunction FeaturedBrandsComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_div_click_0_listener() {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r2));\n    })(\"keydown.enter\", function FeaturedBrandsComponent_div_13_div_1_Template_div_keydown_enter_0_listener() {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r2));\n    })(\"keydown.space\", function FeaturedBrandsComponent_div_13_div_1_Template_div_keydown_space_0_listener() {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 22)(2, \"img\", 23);\n    i0.ɵɵlistener(\"error\", function FeaturedBrandsComponent_div_13_div_1_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, FeaturedBrandsComponent_div_13_div_1_div_3_Template, 2, 0, \"div\", 24)(4, FeaturedBrandsComponent_div_13_div_1_div_4_Template, 4, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 26)(6, \"h3\", 27);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 28);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 30);\n    i0.ɵɵelement(12, \"i\", 31);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, FeaturedBrandsComponent_div_13_div_1_div_15_Template, 4, 1, \"div\", 32)(16, FeaturedBrandsComponent_div_13_div_1_div_16_Template, 4, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_13_div_1_div_17_Template, 3, 2, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 34)(19, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_button_click_19_listener($event) {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.navigateToBrand(brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 36);\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Shop Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_button_click_23_listener($event) {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.viewBrandInfo(brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(24, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_13_div_1_Template_button_click_25_listener($event) {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.toggleFavorite(brand_r2);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(26, \"i\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + brand_r2.name + \" products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", brand_r2.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r2.name + \" logo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.isVerified);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.trending);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(brand_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r2.productCount, \" Products\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.rating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.establishedYear);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r2.categories && brand_r2.categories.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Shop \" + brand_r2.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + brand_r2.name + \" information\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add \" + brand_r2.name + \" to favorites\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"favorited\", ctx_r2.isFavorite(brand_r2._id));\n  }\n}\nfunction FeaturedBrandsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_13_div_1_Template, 27, 16, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.brands)(\"ngForTrackBy\", ctx_r2.trackByBrandId);\n  }\n}\nfunction FeaturedBrandsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"We're working on adding amazing brands for you!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadBrands());\n    });\n    i0.ɵɵelement(8, \"i\", 54);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeaturedBrandsComponent_div_15_a_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 70);\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2, \" Visit Website \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", ctx_r2.selectedBrand.website, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FeaturedBrandsComponent_div_15_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 66);\n    i0.ɵɵtext(4, \"Rating\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.selectedBrand.rating, \"/5\");\n  }\n}\nfunction FeaturedBrandsComponent_div_15_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 66);\n    i0.ɵɵtext(4, \"Established\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.establishedYear);\n  }\n}\nfunction FeaturedBrandsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeBrandModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeBrandModal());\n    });\n    i0.ɵɵelement(3, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 59);\n    i0.ɵɵelement(5, \"img\", 60);\n    i0.ɵɵelementStart(6, \"div\", 61)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, FeaturedBrandsComponent_div_15_a_11_Template, 3, 1, \"a\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 63)(13, \"div\", 64)(14, \"div\", 65);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 66);\n    i0.ɵɵtext(17, \"Products\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, FeaturedBrandsComponent_div_15_div_18_Template, 5, 1, \"div\", 67)(19, FeaturedBrandsComponent_div_15_div_19_Template, 5, 1, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 68)(21, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_15_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(ctx_r2.selectedBrand));\n    });\n    i0.ɵɵelement(22, \"i\", 36);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedBrand.logo, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.selectedBrand.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBrand.website);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBrand.productCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBrand.rating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBrand.establishedYear);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Shop \", ctx_r2.selectedBrand.name, \" \");\n  }\n}\nexport class FeaturedBrandsComponent {\n  constructor(shopDataService, router) {\n    this.shopDataService = shopDataService;\n    this.router = router;\n    this.maxBrands = 6;\n    this.showHeader = true;\n    this.brands = [];\n    this.isLoading = true;\n    this.selectedBrand = null;\n    this.favoriteBrands = new Set();\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadBrands();\n    this.loadFavorites();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadBrands() {\n    this.isLoading = true;\n    this.shopDataService.loadFeaturedBrands().pipe(takeUntil(this.destroy$)).subscribe({\n      next: brands => {\n        this.brands = brands.slice(0, this.maxBrands);\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading featured brands:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  navigateToBrand(brand) {\n    // Track brand click analytics\n    this.trackBrandClick(brand);\n    // Navigate to brand page\n    this.router.navigate(['/shop/brand', brand.slug]);\n  }\n  navigateToCategory(category, brand) {\n    // Navigate to category with brand filter\n    this.router.navigate(['/shop/category', category], {\n      queryParams: {\n        brand: brand.slug\n      }\n    });\n  }\n  viewAllBrands() {\n    this.router.navigate(['/shop/brands']);\n  }\n  viewBrandInfo(brand) {\n    this.selectedBrand = brand;\n  }\n  closeBrandModal() {\n    this.selectedBrand = null;\n  }\n  toggleFavorite(brand) {\n    if (this.favoriteBrands.has(brand._id)) {\n      this.favoriteBrands.delete(brand._id);\n    } else {\n      this.favoriteBrands.add(brand._id);\n    }\n    this.saveFavorites();\n  }\n  isFavorite(brandId) {\n    return this.favoriteBrands.has(brandId);\n  }\n  onImageError(event) {\n    // Set fallback image\n    event.target.src = 'https://via.placeholder.com/200x100/f0f0f0/666?text=Brand+Logo';\n  }\n  trackByBrandId(index, brand) {\n    return brand._id;\n  }\n  trackBrandClick(brand) {\n    // Analytics tracking\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'brand_click', {\n        brand_name: brand.name,\n        brand_id: brand._id,\n        event_category: 'engagement'\n      });\n    }\n  }\n  loadFavorites() {\n    const stored = localStorage.getItem('dfashion_favorite_brands');\n    if (stored) {\n      try {\n        const favorites = JSON.parse(stored);\n        this.favoriteBrands = new Set(favorites);\n      } catch (e) {\n        console.warn('Failed to load favorite brands');\n      }\n    }\n  }\n  saveFavorites() {\n    localStorage.setItem('dfashion_favorite_brands', JSON.stringify(Array.from(this.favoriteBrands)));\n  }\n  static {\n    this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n      return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeaturedBrandsComponent,\n      selectors: [[\"app-featured-brands\"]],\n      inputs: {\n        maxBrands: \"maxBrands\",\n        showHeader: \"showHeader\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 4,\n      consts: [[1, \"featured-brands-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-star\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"brands-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"brand-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"brand-card-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"brand-card-skeleton\"], [1, \"skeleton-logo\"], [1, \"skeleton-text\"], [1, \"skeleton-text\", \"short\"], [1, \"brands-grid\"], [\"class\", \"brand-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"tabindex\", \"0\", 1, \"brand-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"brand-logo-container\"], [\"loading\", \"lazy\", 1, \"brand-logo\", 3, \"error\", \"src\", \"alt\"], [\"class\", \"verification-badge\", 4, \"ngIf\"], [\"class\", \"trending-badge\", 4, \"ngIf\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-description\"], [1, \"brand-stats\"], [1, \"stat-item\"], [1, \"fas\", \"fa-box\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [\"class\", \"brand-categories\", 4, \"ngIf\"], [1, \"brand-actions\"], [1, \"action-btn\", \"primary\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"action-btn\", \"secondary\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"fas\", \"fa-heart\"], [1, \"verification-badge\"], [1, \"fas\", \"fa-check-circle\"], [1, \"trending-badge\"], [1, \"fas\", \"fa-fire\"], [1, \"fas\", \"fa-calendar\"], [1, \"brand-categories\"], [\"class\", \"category-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-categories\", 4, \"ngIf\"], [1, \"category-tag\", 3, \"click\"], [1, \"more-categories\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"fas\", \"fa-store-slash\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"brand-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"modal-header\"], [1, \"modal-logo\", 3, \"src\", \"alt\"], [1, \"modal-brand-info\"], [\"target\", \"_blank\", \"class\", \"website-link\", 3, \"href\", 4, \"ngIf\"], [1, \"modal-stats\"], [1, \"stat-card\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-card\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"target\", \"_blank\", 1, \"website-link\", 3, \"href\"], [1, \"fas\", \"fa-external-link-alt\"]],\n      template: function FeaturedBrandsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵtext(5, \" Featured Brands \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Discover top brands loved by millions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_Template_button_click_8_listener() {\n            return ctx.viewAllBrands();\n          });\n          i0.ɵɵelementStart(9, \"span\");\n          i0.ɵɵtext(10, \"View All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, FeaturedBrandsComponent_div_12_Template, 3, 2, \"div\", 8)(13, FeaturedBrandsComponent_div_13_Template, 2, 2, \"div\", 9)(14, FeaturedBrandsComponent_div_14_Template, 10, 0, \"div\", 10)(15, FeaturedBrandsComponent_div_15_Template, 24, 9, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.brands.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.brands.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedBrand);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.TitleCasePipe],\n      styles: [\".featured-brands-section[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n  background: #fafafa;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding: 0 1rem;\\n}\\n@media (max-width: 768px) {\\n  .featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    text-align: center;\\n  }\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #262626;\\n  margin: 0 0 0.5rem 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n  font-size: 1.8rem;\\n}\\n@media (max-width: 768px) {\\n  .featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n    justify-content: center;\\n  }\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border: none;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(3px);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%]   .skeleton-logo[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 12px;\\n  margin-bottom: 1rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]   .brand-card-skeleton[_ngcontent-%COMP%]   .skeleton-text.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n  padding: 0 1rem;\\n}\\n@media (max-width: 768px) {\\n  .featured-brands-section[_ngcontent-%COMP%]   .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 1rem;\\n  }\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]:hover   .brand-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]:focus {\\n  outline: 3px solid #667eea;\\n  outline-offset: 2px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 1rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  object-fit: contain;\\n  border-radius: 12px;\\n  background: #f8f9fa;\\n  padding: 0.5rem;\\n  transition: transform 0.3s ease;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: 50px;\\n  background: #4caf50;\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.7rem;\\n  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: -5px;\\n  background: linear-gradient(135deg, #ff6b6b, #ff8e53);\\n  color: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #262626;\\n  margin: 0 0 0.5rem 0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-description[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.9rem;\\n  margin: 0 0 1rem 0;\\n  line-height: 1.4;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  width: 12px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%] {\\n  background: #f0f2ff;\\n  color: #667eea;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]   .brand-categories[_ngcontent-%COMP%]   .more-categories[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 1rem;\\n  left: 1rem;\\n  right: 1rem;\\n  display: flex;\\n  gap: 0.5rem;\\n  opacity: 0;\\n  transform: translateY(10px);\\n  transition: all 0.3s ease;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.75rem;\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  font-weight: 600;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover {\\n  background: #5a6fd8;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n  flex: none;\\n  width: 40px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-actions[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]   .fa-heart.favorited[_ngcontent-%COMP%] {\\n  color: #e91e63;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem 1rem;\\n  color: #8e8e8e;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  margin-bottom: 1rem;\\n  color: #ddd;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #666;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin: 0 auto;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 2rem;\\n  max-width: 500px;\\n  width: 100%;\\n  position: relative;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  color: #666;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-logo[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  object-fit: contain;\\n  border-radius: 12px;\\n  background: #f8f9fa;\\n  padding: 0.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #262626;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #8e8e8e;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   .website-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #262626;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #8e8e8e;\\n  margin-top: 0.25rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  background: #667eea;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brand-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #5a6fd8;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .featured-brands-section[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n    color: #ffffff;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n    color: #ffffff;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .action-btn.secondary[_ngcontent-%COMP%]:hover {\\n    background: #333;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n    color: #ffffff;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-brand-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-logo[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n  }\\n  .featured-brands-section[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "FeaturedBrandsComponent_div_12_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵtextInterpolate1", "brand_r2", "rating", "establishedYear", "ɵɵlistener", "FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template_span_click_0_listener", "$event", "category_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ctx_r2", "navigateToCategory", "ɵɵresetView", "stopPropagation", "ɵɵpipeBind1", "categories", "length", "FeaturedBrandsComponent_div_13_div_1_div_17_span_1_Template", "FeaturedBrandsComponent_div_13_div_1_div_17_span_2_Template", "slice", "FeaturedBrandsComponent_div_13_div_1_Template_div_click_0_listener", "_r1", "navigate<PERSON><PERSON>Brand", "FeaturedBrandsComponent_div_13_div_1_Template_div_keydown_enter_0_listener", "FeaturedBrandsComponent_div_13_div_1_Template_div_keydown_space_0_listener", "FeaturedBrandsComponent_div_13_div_1_Template_img_error_2_listener", "onImageError", "FeaturedBrandsComponent_div_13_div_1_div_3_Template", "FeaturedBrandsComponent_div_13_div_1_div_4_Template", "FeaturedBrandsComponent_div_13_div_1_div_15_Template", "FeaturedBrandsComponent_div_13_div_1_div_16_Template", "FeaturedBrandsComponent_div_13_div_1_div_17_Template", "FeaturedBrandsComponent_div_13_div_1_Template_button_click_19_listener", "FeaturedBrandsComponent_div_13_div_1_Template_button_click_23_listener", "viewBrandInfo", "FeaturedBrandsComponent_div_13_div_1_Template_button_click_25_listener", "toggleFavorite", "logo", "ɵɵsanitizeUrl", "name", "isVerified", "trending", "ɵɵtextInterpolate", "description", "productCount", "ɵɵclassProp", "isFavorite", "_id", "FeaturedBrandsComponent_div_13_div_1_Template", "brands", "trackByBrandId", "FeaturedBrandsComponent_div_14_Template_button_click_7_listener", "_r6", "loadBrands", "<PERSON><PERSON><PERSON>", "website", "FeaturedBrandsComponent_div_15_Template_div_click_0_listener", "_r7", "closeBrandModal", "FeaturedBrandsComponent_div_15_Template_div_click_1_listener", "FeaturedBrandsComponent_div_15_Template_button_click_2_listener", "FeaturedBrandsComponent_div_15_a_11_Template", "FeaturedBrandsComponent_div_15_div_18_Template", "FeaturedBrandsComponent_div_15_div_19_Template", "FeaturedBrandsComponent_div_15_Template_button_click_21_listener", "FeaturedBrandsComponent", "constructor", "shopDataService", "router", "maxBrands", "showHeader", "isLoading", "favoriteBrands", "Set", "destroy$", "ngOnInit", "loadFavorites", "ngOnDestroy", "next", "complete", "loadFeaturedBrands", "pipe", "subscribe", "error", "console", "brand", "trackBrandClick", "navigate", "slug", "category", "queryParams", "viewAllBrands", "has", "delete", "add", "saveFavorites", "brandId", "event", "target", "src", "index", "window", "gtag", "brand_name", "brand_id", "event_category", "stored", "localStorage", "getItem", "favorites", "JSON", "parse", "e", "warn", "setItem", "stringify", "Array", "from", "ɵɵdirectiveInject", "i1", "ShopDataService", "i2", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeaturedBrandsComponent_Template", "rf", "ctx", "FeaturedBrandsComponent_Template_button_click_8_listener", "FeaturedBrandsComponent_div_12_Template", "FeaturedBrandsComponent_div_13_Template", "FeaturedBrandsComponent_div_14_Template", "FeaturedBrandsComponent_div_15_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "TitleCasePipe", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\featured-brands\\featured-brands.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { ShopDataService, Brand } from '../../../core/services/shop-data.service';\n\n@Component({\n  selector: 'app-featured-brands',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"featured-brands-section\">\n      <!-- Section Header -->\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <h2 class=\"section-title\">\n            <i class=\"fas fa-star\"></i>\n            Featured Brands\n          </h2>\n          <p class=\"section-subtitle\">Discover top brands loved by millions</p>\n        </div>\n        <button class=\"view-all-btn\" (click)=\"viewAllBrands()\">\n          <span>View All</span>\n          <i class=\"fas fa-arrow-right\"></i>\n        </button>\n      </div>\n\n      <!-- Loading State -->\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-grid\">\n          <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"brand-card-skeleton\">\n            <div class=\"skeleton-logo\"></div>\n            <div class=\"skeleton-text\"></div>\n            <div class=\"skeleton-text short\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Brands Grid -->\n      <div *ngIf=\"!isLoading && brands.length > 0\" class=\"brands-grid\">\n        <div \n          *ngFor=\"let brand of brands; trackBy: trackByBrandId\" \n          class=\"brand-card\"\n          (click)=\"navigateToBrand(brand)\"\n          [attr.aria-label]=\"'View ' + brand.name + ' products'\"\n          tabindex=\"0\"\n          (keydown.enter)=\"navigateToBrand(brand)\"\n          (keydown.space)=\"navigateToBrand(brand)\">\n          \n          <!-- Brand Logo -->\n          <div class=\"brand-logo-container\">\n            <img \n              [src]=\"brand.logo\" \n              [alt]=\"brand.name + ' logo'\"\n              class=\"brand-logo\"\n              loading=\"lazy\"\n              (error)=\"onImageError($event)\">\n            \n            <!-- Verification Badge -->\n            <div class=\"verification-badge\" *ngIf=\"brand.isVerified\">\n              <i class=\"fas fa-check-circle\"></i>\n            </div>\n            \n            <!-- Trending Badge -->\n            <div class=\"trending-badge\" *ngIf=\"brand.trending\">\n              <i class=\"fas fa-fire\"></i>\n              <span>Trending</span>\n            </div>\n          </div>\n\n          <!-- Brand Info -->\n          <div class=\"brand-info\">\n            <h3 class=\"brand-name\">{{ brand.name }}</h3>\n            <p class=\"brand-description\">{{ brand.description }}</p>\n            \n            <!-- Brand Stats -->\n            <div class=\"brand-stats\">\n              <div class=\"stat-item\">\n                <i class=\"fas fa-box\"></i>\n                <span>{{ brand.productCount }} Products</span>\n              </div>\n              <div class=\"stat-item\" *ngIf=\"brand.rating\">\n                <i class=\"fas fa-star\"></i>\n                <span>{{ brand.rating }}/5</span>\n              </div>\n              <div class=\"stat-item\" *ngIf=\"brand.establishedYear\">\n                <i class=\"fas fa-calendar\"></i>\n                <span>Est. {{ brand.establishedYear }}</span>\n              </div>\n            </div>\n\n            <!-- Categories -->\n            <div class=\"brand-categories\" *ngIf=\"brand.categories && brand.categories.length > 0\">\n              <span \n                *ngFor=\"let category of brand.categories.slice(0, 3)\" \n                class=\"category-tag\"\n                (click)=\"navigateToCategory(category, brand); $event.stopPropagation()\">\n                {{ category | titlecase }}\n              </span>\n              <span *ngIf=\"brand.categories.length > 3\" class=\"more-categories\">\n                +{{ brand.categories.length - 3 }} more\n              </span>\n            </div>\n          </div>\n\n          <!-- Hover Actions -->\n          <div class=\"brand-actions\">\n            <button \n              class=\"action-btn primary\"\n              (click)=\"navigateToBrand(brand); $event.stopPropagation()\"\n              [attr.aria-label]=\"'Shop ' + brand.name\">\n              <i class=\"fas fa-shopping-bag\"></i>\n              <span>Shop Now</span>\n            </button>\n            <button \n              class=\"action-btn secondary\"\n              (click)=\"viewBrandInfo(brand); $event.stopPropagation()\"\n              [attr.aria-label]=\"'View ' + brand.name + ' information'\">\n              <i class=\"fas fa-info-circle\"></i>\n            </button>\n            <button \n              class=\"action-btn secondary\"\n              (click)=\"toggleFavorite(brand); $event.stopPropagation()\"\n              [attr.aria-label]=\"'Add ' + brand.name + ' to favorites'\">\n              <i class=\"fas fa-heart\" [class.favorited]=\"isFavorite(brand._id)\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Empty State -->\n      <div *ngIf=\"!isLoading && brands.length === 0\" class=\"empty-state\">\n        <div class=\"empty-icon\">\n          <i class=\"fas fa-store-slash\"></i>\n        </div>\n        <h3>No Featured Brands</h3>\n        <p>We're working on adding amazing brands for you!</p>\n        <button class=\"retry-btn\" (click)=\"loadBrands()\">\n          <i class=\"fas fa-refresh\"></i>\n          Try Again\n        </button>\n      </div>\n\n      <!-- Brand Info Modal -->\n      <div class=\"brand-modal\" *ngIf=\"selectedBrand\" (click)=\"closeBrandModal()\">\n        <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n          <button class=\"close-btn\" (click)=\"closeBrandModal()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n          \n          <div class=\"modal-header\">\n            <img [src]=\"selectedBrand.logo\" [alt]=\"selectedBrand.name\" class=\"modal-logo\">\n            <div class=\"modal-brand-info\">\n              <h3>{{ selectedBrand.name }}</h3>\n              <p>{{ selectedBrand.description }}</p>\n              <a *ngIf=\"selectedBrand.website\" \n                 [href]=\"selectedBrand.website\" \n                 target=\"_blank\" \n                 class=\"website-link\">\n                <i class=\"fas fa-external-link-alt\"></i>\n                Visit Website\n              </a>\n            </div>\n          </div>\n          \n          <div class=\"modal-stats\">\n            <div class=\"stat-card\">\n              <div class=\"stat-value\">{{ selectedBrand.productCount }}</div>\n              <div class=\"stat-label\">Products</div>\n            </div>\n            <div class=\"stat-card\" *ngIf=\"selectedBrand.rating\">\n              <div class=\"stat-value\">{{ selectedBrand.rating }}/5</div>\n              <div class=\"stat-label\">Rating</div>\n            </div>\n            <div class=\"stat-card\" *ngIf=\"selectedBrand.establishedYear\">\n              <div class=\"stat-value\">{{ selectedBrand.establishedYear }}</div>\n              <div class=\"stat-label\">Established</div>\n            </div>\n          </div>\n          \n          <div class=\"modal-actions\">\n            <button class=\"btn btn-primary\" (click)=\"navigateToBrand(selectedBrand)\">\n              <i class=\"fas fa-shopping-bag\"></i>\n              Shop {{ selectedBrand.name }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./featured-brands.component.scss']\n})\nexport class FeaturedBrandsComponent implements OnInit, OnDestroy {\n  @Input() maxBrands: number = 6;\n  @Input() showHeader: boolean = true;\n  \n  brands: Brand[] = [];\n  isLoading: boolean = true;\n  selectedBrand: Brand | null = null;\n  favoriteBrands: Set<string> = new Set();\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private shopDataService: ShopDataService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadBrands();\n    this.loadFavorites();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadBrands(): void {\n    this.isLoading = true;\n    \n    this.shopDataService.loadFeaturedBrands()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (brands) => {\n          this.brands = brands.slice(0, this.maxBrands);\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading featured brands:', error);\n          this.isLoading = false;\n        }\n      });\n  }\n\n  navigateToBrand(brand: Brand): void {\n    // Track brand click analytics\n    this.trackBrandClick(brand);\n    \n    // Navigate to brand page\n    this.router.navigate(['/shop/brand', brand.slug]);\n  }\n\n  navigateToCategory(category: string, brand: Brand): void {\n    // Navigate to category with brand filter\n    this.router.navigate(['/shop/category', category], {\n      queryParams: { brand: brand.slug }\n    });\n  }\n\n  viewAllBrands(): void {\n    this.router.navigate(['/shop/brands']);\n  }\n\n  viewBrandInfo(brand: Brand): void {\n    this.selectedBrand = brand;\n  }\n\n  closeBrandModal(): void {\n    this.selectedBrand = null;\n  }\n\n  toggleFavorite(brand: Brand): void {\n    if (this.favoriteBrands.has(brand._id)) {\n      this.favoriteBrands.delete(brand._id);\n    } else {\n      this.favoriteBrands.add(brand._id);\n    }\n    this.saveFavorites();\n  }\n\n  isFavorite(brandId: string): boolean {\n    return this.favoriteBrands.has(brandId);\n  }\n\n  onImageError(event: any): void {\n    // Set fallback image\n    event.target.src = 'https://via.placeholder.com/200x100/f0f0f0/666?text=Brand+Logo';\n  }\n\n  trackByBrandId(index: number, brand: Brand): string {\n    return brand._id;\n  }\n\n  private trackBrandClick(brand: Brand): void {\n    // Analytics tracking\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'brand_click', {\n        brand_name: brand.name,\n        brand_id: brand._id,\n        event_category: 'engagement'\n      });\n    }\n  }\n\n  private loadFavorites(): void {\n    const stored = localStorage.getItem('dfashion_favorite_brands');\n    if (stored) {\n      try {\n        const favorites = JSON.parse(stored);\n        this.favoriteBrands = new Set(favorites);\n      } catch (e) {\n        console.warn('Failed to load favorite brands');\n      }\n    }\n  }\n\n  private saveFavorites(): void {\n    localStorage.setItem('dfashion_favorite_brands', JSON.stringify(Array.from(this.favoriteBrands)));\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;IA2B/BC,EAAA,CAAAC,cAAA,cAAoE;IAGlED,EAFA,CAAAE,SAAA,cAAiC,cACA,cACM;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBAAoE;IAMxEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IANoBH,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAgB;;;;;IA6BpCT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAChBV,EADgB,CAAAG,YAAA,EAAO,EACjB;;;;;IAcJH,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAE,SAAA,WAA2B;IAC3BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAAoB;IAC5BV,EAD4B,CAAAG,YAAA,EAAO,EAC7B;;;;IADEH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAW,kBAAA,KAAAC,QAAA,CAAAC,MAAA,OAAoB;;;;;IAE5Bb,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAAgC;IACxCV,EADwC,CAAAG,YAAA,EAAO,EACzC;;;;IADEH,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAW,kBAAA,UAAAC,QAAA,CAAAE,eAAA,KAAgC;;;;;;IAMxCd,EAAA,CAAAC,cAAA,eAG0E;IAAxED,EAAA,CAAAe,UAAA,mBAAAC,kFAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,QAAA,GAAAZ,EAAA,CAAAsB,aAAA,IAAAD,SAAA;MAAA,MAAAE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAASC,MAAA,CAAAC,kBAAA,CAAAN,WAAA,EAAAN,QAAA,CAAmC;MAAA,OAAAZ,EAAA,CAAAyB,WAAA,CAAER,MAAA,CAAAS,eAAA,EAAwB;IAAA,EAAC;IACvE1B,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAW,kBAAA,MAAAX,EAAA,CAAA2B,WAAA,OAAAT,WAAA,OACF;;;;;IACAlB,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAW,kBAAA,OAAAC,QAAA,CAAAgB,UAAA,CAAAC,MAAA,eACF;;;;;IATF7B,EAAA,CAAAC,cAAA,cAAsF;IAOpFD,EANA,CAAAI,UAAA,IAAA0B,2DAAA,mBAG0E,IAAAC,2DAAA,mBAGR;IAGpE/B,EAAA,CAAAG,YAAA,EAAM;;;;IARmBH,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,YAAAK,QAAA,CAAAgB,UAAA,CAAAI,KAAA,OAA+B;IAK/ChC,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,UAAA,SAAAK,QAAA,CAAAgB,UAAA,CAAAC,MAAA,KAAiC;;;;;;IA3D9C7B,EAAA,CAAAC,cAAA,cAO2C;IAAzCD,EAJA,CAAAe,UAAA,mBAAAkB,mEAAA;MAAA,MAAArB,QAAA,GAAAZ,EAAA,CAAAmB,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAAE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAY,eAAA,CAAAvB,QAAA,CAAsB;IAAA,EAAC,2BAAAwB,2EAAA;MAAA,MAAAxB,QAAA,GAAAZ,EAAA,CAAAmB,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAAE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAGfF,MAAA,CAAAY,eAAA,CAAAvB,QAAA,CAAsB;IAAA,EAAC,2BAAAyB,2EAAA;MAAA,MAAAzB,QAAA,GAAAZ,EAAA,CAAAmB,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAAE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CACvBF,MAAA,CAAAY,eAAA,CAAAvB,QAAA,CAAsB;IAAA,EAAC;IAItCZ,EADF,CAAAC,cAAA,cAAkC,cAMC;IAA/BD,EAAA,CAAAe,UAAA,mBAAAuB,mEAAArB,MAAA;MAAAjB,EAAA,CAAAmB,aAAA,CAAAe,GAAA;MAAA,MAAAX,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAgB,YAAA,CAAAtB,MAAA,CAAoB;IAAA,EAAC;IALhCjB,EAAA,CAAAG,YAAA,EAKiC;IAQjCH,EALA,CAAAI,UAAA,IAAAoC,mDAAA,kBAAyD,IAAAC,mDAAA,kBAKN;IAIrDzC,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAwB,aACC;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,YAA6B;IAAAD,EAAA,CAAAU,MAAA,GAAuB;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAItDH,EADF,CAAAC,cAAA,eAAyB,eACA;IACrBD,EAAA,CAAAE,SAAA,aAA0B;IAC1BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAiC;IACzCV,EADyC,CAAAG,YAAA,EAAO,EAC1C;IAKNH,EAJA,CAAAI,UAAA,KAAAsC,oDAAA,kBAA4C,KAAAC,oDAAA,kBAIS;IAIvD3C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,KAAAwC,oDAAA,kBAAsF;IAWxF5C,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2B,kBAIkB;IADzCD,EAAA,CAAAe,UAAA,mBAAA8B,uEAAA5B,MAAA;MAAA,MAAAL,QAAA,GAAAZ,EAAA,CAAAmB,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAAE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAASC,MAAA,CAAAY,eAAA,CAAAvB,QAAA,CAAsB;MAAA,OAAAZ,EAAA,CAAAyB,WAAA,CAAER,MAAA,CAAAS,eAAA,EAAwB;IAAA,EAAC;IAE1D1B,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,gBAAQ;IAChBV,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAC,cAAA,kBAG4D;IAD1DD,EAAA,CAAAe,UAAA,mBAAA+B,uEAAA7B,MAAA;MAAA,MAAAL,QAAA,GAAAZ,EAAA,CAAAmB,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAAE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAASC,MAAA,CAAAwB,aAAA,CAAAnC,QAAA,CAAoB;MAAA,OAAAZ,EAAA,CAAAyB,WAAA,CAAER,MAAA,CAAAS,eAAA,EAAwB;IAAA,EAAC;IAExD1B,EAAA,CAAAE,SAAA,aAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAG4D;IAD1DD,EAAA,CAAAe,UAAA,mBAAAiC,uEAAA/B,MAAA;MAAA,MAAAL,QAAA,GAAAZ,EAAA,CAAAmB,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAAE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAASC,MAAA,CAAA0B,cAAA,CAAArC,QAAA,CAAqB;MAAA,OAAAZ,EAAA,CAAAyB,WAAA,CAAER,MAAA,CAAAS,eAAA,EAAwB;IAAA,EAAC;IAEzD1B,EAAA,CAAAE,SAAA,aAAsE;IAG5EF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;;IA3EAH,EAAA,CAAAM,SAAA,GAAkB;IAClBN,EADA,CAAAO,UAAA,QAAAK,QAAA,CAAAsC,IAAA,EAAAlD,EAAA,CAAAmD,aAAA,CAAkB,QAAAvC,QAAA,CAAAwC,IAAA,WACU;IAMGpD,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAO,UAAA,SAAAK,QAAA,CAAAyC,UAAA,CAAsB;IAK1BrD,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAO,UAAA,SAAAK,QAAA,CAAA0C,QAAA,CAAoB;IAQ1BtD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAuD,iBAAA,CAAA3C,QAAA,CAAAwC,IAAA,CAAgB;IACVpD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAuD,iBAAA,CAAA3C,QAAA,CAAA4C,WAAA,CAAuB;IAM1CxD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAW,kBAAA,KAAAC,QAAA,CAAA6C,YAAA,cAAiC;IAEjBzD,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAO,UAAA,SAAAK,QAAA,CAAAC,MAAA,CAAkB;IAIlBb,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,SAAAK,QAAA,CAAAE,eAAA,CAA2B;IAOtBd,EAAA,CAAAM,SAAA,EAAqD;IAArDN,EAAA,CAAAO,UAAA,SAAAK,QAAA,CAAAgB,UAAA,IAAAhB,QAAA,CAAAgB,UAAA,CAAAC,MAAA,KAAqD;IAkBlF7B,EAAA,CAAAM,SAAA,GAAwC;;IAOxCN,EAAA,CAAAM,SAAA,GAAyD;;IAMzDN,EAAA,CAAAM,SAAA,GAAyD;;IACjCN,EAAA,CAAAM,SAAA,EAAyC;IAAzCN,EAAA,CAAA0D,WAAA,cAAAnC,MAAA,CAAAoC,UAAA,CAAA/C,QAAA,CAAAgD,GAAA,EAAyC;;;;;IArFzE5D,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAI,UAAA,IAAAyD,6CAAA,oBAO2C;IAiF7C7D,EAAA,CAAAG,YAAA,EAAM;;;;IAvFgBH,EAAA,CAAAM,SAAA,EAAW;IAAAN,EAAX,CAAAO,UAAA,YAAAgB,MAAA,CAAAuC,MAAA,CAAW,iBAAAvC,MAAA,CAAAwC,cAAA,CAAuB;;;;;;IA2FtD/D,EADF,CAAAC,cAAA,cAAmE,cACzC;IACtBD,EAAA,CAAAE,SAAA,YAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,yBAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,sDAA+C;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,iBAAiD;IAAvBD,EAAA,CAAAe,UAAA,mBAAAiD,gEAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,GAAA;MAAA,MAAA1C,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAA2C,UAAA,EAAY;IAAA,EAAC;IAC9ClE,EAAA,CAAAE,SAAA,YAA8B;IAC9BF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAcEH,EAAA,CAAAC,cAAA,YAGwB;IACtBD,EAAA,CAAAE,SAAA,YAAwC;IACxCF,EAAA,CAAAU,MAAA,sBACF;IAAAV,EAAA,CAAAG,YAAA,EAAI;;;;IALDH,EAAA,CAAAO,UAAA,SAAAgB,MAAA,CAAA4C,aAAA,CAAAC,OAAA,EAAApE,EAAA,CAAAmD,aAAA,CAA8B;;;;;IAejCnD,EADF,CAAAC,cAAA,cAAoD,cAC1B;IAAAD,EAAA,CAAAU,MAAA,GAA4B;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAChCV,EADgC,CAAAG,YAAA,EAAM,EAChC;;;;IAFoBH,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAW,kBAAA,KAAAY,MAAA,CAAA4C,aAAA,CAAAtD,MAAA,OAA4B;;;;;IAIpDb,EADF,CAAAC,cAAA,cAA6D,cACnC;IAAAD,EAAA,CAAAU,MAAA,GAAmC;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAU,MAAA,kBAAW;IACrCV,EADqC,CAAAG,YAAA,EAAM,EACrC;;;;IAFoBH,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAuD,iBAAA,CAAAhC,MAAA,CAAA4C,aAAA,CAAArD,eAAA,CAAmC;;;;;;IA/BnEd,EAAA,CAAAC,cAAA,cAA2E;IAA5BD,EAAA,CAAAe,UAAA,mBAAAsD,6DAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAmD,GAAA;MAAA,MAAA/C,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAgD,eAAA,EAAiB;IAAA,EAAC;IACxEvE,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAe,UAAA,mBAAAyD,6DAAAvD,MAAA;MAAAjB,EAAA,CAAAmB,aAAA,CAAAmD,GAAA;MAAA,OAAAtE,EAAA,CAAAyB,WAAA,CAASR,MAAA,CAAAS,eAAA,EAAwB;IAAA,EAAC;IAC3D1B,EAAA,CAAAC,cAAA,iBAAsD;IAA5BD,EAAA,CAAAe,UAAA,mBAAA0D,gEAAA;MAAAzE,EAAA,CAAAmB,aAAA,CAAAmD,GAAA;MAAA,MAAA/C,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAgD,eAAA,EAAiB;IAAA,EAAC;IACnDvE,EAAA,CAAAE,SAAA,YAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,cAA8E;IAE5EF,EADF,CAAAC,cAAA,cAA8B,SACxB;IAAAD,EAAA,CAAAU,MAAA,GAAwB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,IAA+B;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACtCH,EAAA,CAAAI,UAAA,KAAAsE,4CAAA,gBAGwB;IAK5B1E,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAyB,eACA,eACG;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAC9DH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAU,MAAA,gBAAQ;IAClCV,EADkC,CAAAG,YAAA,EAAM,EAClC;IAKNH,EAJA,CAAAI,UAAA,KAAAuE,8CAAA,kBAAoD,KAAAC,8CAAA,kBAIS;IAI/D5E,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAA2B,kBACgD;IAAzCD,EAAA,CAAAe,UAAA,mBAAA8D,iEAAA;MAAA7E,EAAA,CAAAmB,aAAA,CAAAmD,GAAA;MAAA,MAAA/C,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAY,eAAA,CAAAZ,MAAA,CAAA4C,aAAA,CAA8B;IAAA,EAAC;IACtEnE,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAU,MAAA,IACF;IAGNV,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IApCKH,EAAA,CAAAM,SAAA,GAA0B;IAACN,EAA3B,CAAAO,UAAA,QAAAgB,MAAA,CAAA4C,aAAA,CAAAjB,IAAA,EAAAlD,EAAA,CAAAmD,aAAA,CAA0B,QAAA5B,MAAA,CAAA4C,aAAA,CAAAf,IAAA,CAA2B;IAEpDpD,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAuD,iBAAA,CAAAhC,MAAA,CAAA4C,aAAA,CAAAf,IAAA,CAAwB;IACzBpD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAuD,iBAAA,CAAAhC,MAAA,CAAA4C,aAAA,CAAAX,WAAA,CAA+B;IAC9BxD,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,SAAAgB,MAAA,CAAA4C,aAAA,CAAAC,OAAA,CAA2B;IAYPpE,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAuD,iBAAA,CAAAhC,MAAA,CAAA4C,aAAA,CAAAV,YAAA,CAAgC;IAGlCzD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAO,UAAA,SAAAgB,MAAA,CAAA4C,aAAA,CAAAtD,MAAA,CAA0B;IAI1Bb,EAAA,CAAAM,SAAA,EAAmC;IAAnCN,EAAA,CAAAO,UAAA,SAAAgB,MAAA,CAAA4C,aAAA,CAAArD,eAAA,CAAmC;IASzDd,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAW,kBAAA,WAAAY,MAAA,CAAA4C,aAAA,CAAAf,IAAA,MACF;;;AAQZ,OAAM,MAAO0B,uBAAuB;EAWlCC,YACUC,eAAgC,EAChCC,MAAc;IADd,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAZP,KAAAC,SAAS,GAAW,CAAC;IACrB,KAAAC,UAAU,GAAY,IAAI;IAEnC,KAAArB,MAAM,GAAY,EAAE;IACpB,KAAAsB,SAAS,GAAY,IAAI;IACzB,KAAAjB,aAAa,GAAiB,IAAI;IAClC,KAAAkB,cAAc,GAAgB,IAAIC,GAAG,EAAE;IAE/B,KAAAC,QAAQ,GAAG,IAAIzF,OAAO,EAAQ;EAKnC;EAEH0F,QAAQA,CAAA;IACN,IAAI,CAACtB,UAAU,EAAE;IACjB,IAAI,CAACuB,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEA1B,UAAUA,CAAA;IACR,IAAI,CAACkB,SAAS,GAAG,IAAI;IAErB,IAAI,CAACJ,eAAe,CAACa,kBAAkB,EAAE,CACtCC,IAAI,CAAC/F,SAAS,CAAC,IAAI,CAACwF,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTJ,IAAI,EAAG7B,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAC9B,KAAK,CAAC,CAAC,EAAE,IAAI,CAACkD,SAAS,CAAC;QAC7C,IAAI,CAACE,SAAS,GAAG,KAAK;MACxB,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACZ,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAjD,eAAeA,CAAC+D,KAAY;IAC1B;IACA,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;IAE3B;IACA,IAAI,CAACjB,MAAM,CAACmB,QAAQ,CAAC,CAAC,aAAa,EAAEF,KAAK,CAACG,IAAI,CAAC,CAAC;EACnD;EAEA7E,kBAAkBA,CAAC8E,QAAgB,EAAEJ,KAAY;IAC/C;IACA,IAAI,CAACjB,MAAM,CAACmB,QAAQ,CAAC,CAAC,gBAAgB,EAAEE,QAAQ,CAAC,EAAE;MACjDC,WAAW,EAAE;QAAEL,KAAK,EAAEA,KAAK,CAACG;MAAI;KACjC,CAAC;EACJ;EAEAG,aAAaA,CAAA;IACX,IAAI,CAACvB,MAAM,CAACmB,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEArD,aAAaA,CAACmD,KAAY;IACxB,IAAI,CAAC/B,aAAa,GAAG+B,KAAK;EAC5B;EAEA3B,eAAeA,CAAA;IACb,IAAI,CAACJ,aAAa,GAAG,IAAI;EAC3B;EAEAlB,cAAcA,CAACiD,KAAY;IACzB,IAAI,IAAI,CAACb,cAAc,CAACoB,GAAG,CAACP,KAAK,CAACtC,GAAG,CAAC,EAAE;MACtC,IAAI,CAACyB,cAAc,CAACqB,MAAM,CAACR,KAAK,CAACtC,GAAG,CAAC;KACtC,MAAM;MACL,IAAI,CAACyB,cAAc,CAACsB,GAAG,CAACT,KAAK,CAACtC,GAAG,CAAC;;IAEpC,IAAI,CAACgD,aAAa,EAAE;EACtB;EAEAjD,UAAUA,CAACkD,OAAe;IACxB,OAAO,IAAI,CAACxB,cAAc,CAACoB,GAAG,CAACI,OAAO,CAAC;EACzC;EAEAtE,YAAYA,CAACuE,KAAU;IACrB;IACAA,KAAK,CAACC,MAAM,CAACC,GAAG,GAAG,gEAAgE;EACrF;EAEAjD,cAAcA,CAACkD,KAAa,EAAEf,KAAY;IACxC,OAAOA,KAAK,CAACtC,GAAG;EAClB;EAEQuC,eAAeA,CAACD,KAAY;IAClC;IACA,IAAI,OAAQgB,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE;QAC3CC,UAAU,EAAElB,KAAK,CAAC9C,IAAI;QACtBiE,QAAQ,EAAEnB,KAAK,CAACtC,GAAG;QACnB0D,cAAc,EAAE;OACjB,CAAC;;EAEN;EAEQ7B,aAAaA,CAAA;IACnB,MAAM8B,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC;IAC/D,IAAIF,MAAM,EAAE;MACV,IAAI;QACF,MAAMG,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;QACpC,IAAI,CAAClC,cAAc,GAAG,IAAIC,GAAG,CAACoC,SAAS,CAAC;OACzC,CAAC,OAAOG,CAAC,EAAE;QACV5B,OAAO,CAAC6B,IAAI,CAAC,gCAAgC,CAAC;;;EAGpD;EAEQlB,aAAaA,CAAA;IACnBY,YAAY,CAACO,OAAO,CAAC,0BAA0B,EAAEJ,IAAI,CAACK,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7C,cAAc,CAAC,CAAC,CAAC;EACnG;;;uBArHWP,uBAAuB,EAAA9E,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAArI,EAAA,CAAAmI,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBzD,uBAAuB;MAAA0D,SAAA;MAAAC,MAAA;QAAAvD,SAAA;QAAAC,UAAA;MAAA;MAAAuD,UAAA;MAAAC,QAAA,GAAA3I,EAAA,CAAA4I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjL1BlJ,EAJN,CAAAC,cAAA,aAAqC,aAEP,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,WAA2B;UAC3BF,EAAA,CAAAU,MAAA,wBACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,4CAAqC;UACnEV,EADmE,CAAAG,YAAA,EAAI,EACjE;UACNH,EAAA,CAAAC,cAAA,gBAAuD;UAA1BD,EAAA,CAAAe,UAAA,mBAAAqI,yDAAA;YAAA,OAASD,GAAA,CAAA3C,aAAA,EAAe;UAAA,EAAC;UACpDxG,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAU,MAAA,gBAAQ;UAAAV,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAE,SAAA,YAAkC;UAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;UAuHNH,EApHA,CAAAI,UAAA,KAAAiJ,uCAAA,iBAAiD,KAAAC,uCAAA,iBAWgB,KAAAC,uCAAA,mBA4FE,KAAAC,uCAAA,mBAaQ;UA4C7ExJ,EAAA,CAAAG,YAAA,EAAM;;;UAhKEH,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAO,UAAA,SAAA4I,GAAA,CAAA/D,SAAA,CAAe;UAWfpF,EAAA,CAAAM,SAAA,EAAqC;UAArCN,EAAA,CAAAO,UAAA,UAAA4I,GAAA,CAAA/D,SAAA,IAAA+D,GAAA,CAAArF,MAAA,CAAAjC,MAAA,KAAqC;UA4FrC7B,EAAA,CAAAM,SAAA,EAAuC;UAAvCN,EAAA,CAAAO,UAAA,UAAA4I,GAAA,CAAA/D,SAAA,IAAA+D,GAAA,CAAArF,MAAA,CAAAjC,MAAA,OAAuC;UAanB7B,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAO,UAAA,SAAA4I,GAAA,CAAAhF,aAAA,CAAmB;;;qBAvIvCtE,YAAY,EAAA4J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,aAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}