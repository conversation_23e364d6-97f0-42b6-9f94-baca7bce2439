{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Import existing sidebar components\nimport { SuggestedProductsComponent } from '../suggested-products/suggested-products.component';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { TopInfluencersComponent } from '../top-influencers/top-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction SidebarComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"img\", 26);\n    i0.ɵɵelementStart(2, \"div\", 27)(3, \"h4\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 30);\n    i0.ɵɵtext(8, \"Switch\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatar || \"/assets/images/default-avatar.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.currentUser.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.fullName);\n  }\n}\nfunction SidebarComponent_section_2_div_8_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getDiscountPercentage(item_r4), \"% OFF \");\n  }\n}\nfunction SidebarComponent_section_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_2_div_8_Template_div_click_0_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(item_r4));\n    });\n    i0.ɵɵelement(1, \"img\", 35);\n    i0.ɵɵelementStart(2, \"div\", 36)(3, \"span\", 37);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SidebarComponent_section_2_div_8_div_7_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", (item_r4.images == null ? null : item_r4.images[0] == null ? null : item_r4.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", item_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(item_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getDiscountPercentage(item_r4) > 0);\n  }\n}\nfunction SidebarComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 31);\n    i0.ɵɵtext(4, \" Summer Collection 2024\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewSummerCollection());\n    });\n    i0.ɵɵtext(6, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32);\n    i0.ɵɵtemplate(8, SidebarComponent_section_2_div_8_Template, 8, 5, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.summerCollection.slice(0, 3))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_section_11_div_8_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatPrice(product_r7.originalPrice), \" \");\n  }\n}\nfunction SidebarComponent_section_11_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_11_div_8_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(product_r7));\n    });\n    i0.ɵɵelement(1, \"img\", 45);\n    i0.ɵɵelementStart(2, \"div\", 46)(3, \"span\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 49)(8, \"span\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SidebarComponent_section_11_div_8_span_10_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", (product_r7.images == null ? null : product_r7.images[0] == null ? null : product_r7.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(product_r7.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice);\n  }\n}\nfunction SidebarComponent_section_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 41);\n    i0.ɵɵtext(4, \" Featured Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewFeaturedProducts());\n    });\n    i0.ɵɵtext(6, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 42);\n    i0.ɵɵtemplate(8, SidebarComponent_section_11_div_8_Template, 11, 6, \"div\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredProducts.slice(0, 4))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_section_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_20_div_8_Template_div_click_0_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(product_r10));\n    });\n    i0.ɵɵelement(1, \"img\", 57);\n    i0.ɵɵelementStart(2, \"div\", 58);\n    i0.ɵɵtext(3, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 59)(5, \"span\", 60);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 61);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 62);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", (product_r10.images == null ? null : product_r10.images[0] == null ? null : product_r10.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r10.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(product_r10.price));\n  }\n}\nfunction SidebarComponent_section_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 53);\n    i0.ɵɵtext(4, \" New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_20_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewNewArrivals());\n    });\n    i0.ɵɵtext(6, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 54);\n    i0.ɵɵtemplate(8, SidebarComponent_section_20_div_8_Template, 11, 5, \"div\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.newArrivals.slice(0, 4))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_37_Template_div_click_0_listener() {\n      const category_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCategoryClick(category_r12));\n    });\n    i0.ɵɵelement(1, \"img\", 64);\n    i0.ɵɵelementStart(2, \"span\", 65);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r12.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r12.name);\n  }\n}\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    constructor(router) {\n      this.router = router;\n      // Data passed from home component\n      this.currentUser = null;\n      this.featuredProducts = [];\n      this.trendingProducts = [];\n      this.newArrivals = [];\n      this.summerCollection = [];\n      this.categories = [];\n    }\n    ngOnInit() {\n      console.log('✅ Sidebar component initialized with data:', {\n        featuredProducts: this.featuredProducts.length,\n        trendingProducts: this.trendingProducts.length,\n        newArrivals: this.newArrivals.length,\n        summerCollection: this.summerCollection.length,\n        categories: this.categories.length\n      });\n    }\n    // Navigation methods\n    onProductClick(product) {\n      console.log('🛍️ Navigate to product:', product.name);\n      this.router.navigate(['/product', product._id || product.id]);\n    }\n    onCategoryClick(category) {\n      console.log('📂 Navigate to category:', category.name);\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          category: category.slug || category.name.toLowerCase()\n        }\n      });\n    }\n    onUserClick(user) {\n      console.log('👤 Navigate to user profile:', user.username);\n      this.router.navigate(['/profile', user.username]);\n    }\n    followUser(user) {\n      console.log('➕ Follow user:', user.username);\n      // TODO: Implement follow functionality\n    }\n    viewAllCategories() {\n      this.router.navigate(['/shop']);\n    }\n    viewSummerCollection() {\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          collection: 'summer2024'\n        }\n      });\n    }\n    viewFeaturedProducts() {\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          filter: 'featured'\n        }\n      });\n    }\n    viewNewArrivals() {\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          filter: 'new'\n        }\n      });\n    }\n    // Utility methods\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n      }).format(price);\n    }\n    formatNumber(num) {\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    getDiscountPercentage(product) {\n      if (product.originalPrice && product.originalPrice > product.price) {\n        return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n      }\n      return product.discount || 0;\n    }\n    // Track by functions for performance\n    trackByProductId(index, product) {\n      return product._id || product.id;\n    }\n    trackByCategoryId(index, category) {\n      return category._id || category.id;\n    }\n    // Get categories to display (real data or fallback)\n    getDisplayCategories() {\n      if (this.categories && this.categories.length > 0) {\n        return this.categories.slice(0, 4);\n      }\n      // Fallback categories with images\n      return [{\n        _id: 'women',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      }, {\n        _id: 'men',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      }, {\n        _id: 'children',\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      }, {\n        _id: 'ethnic',\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }];\n    }\n    static {\n      this.ɵfac = function SidebarComponent_Factory(t) {\n        return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SidebarComponent,\n        selectors: [[\"app-sidebar\"]],\n        inputs: {\n          currentUser: \"currentUser\",\n          featuredProducts: \"featuredProducts\",\n          trendingProducts: \"trendingProducts\",\n          newArrivals: \"newArrivals\",\n          summerCollection: \"summerCollection\",\n          categories: \"categories\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 57,\n        vars: 6,\n        consts: [[1, \"home-sidebar\"], [\"class\", \"profile-card\", 4, \"ngIf\"], [\"class\", \"sidebar-section\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"section-header\"], [1, \"fas\", \"fa-magic\"], [\"routerLink\", \"/shop?filter=suggested\", 1, \"see-all-btn\"], [1, \"fas\", \"fa-fire\"], [\"routerLink\", \"/shop?filter=trending\", 1, \"see-all-btn\"], [1, \"fas\", \"fa-crown\"], [\"routerLink\", \"/influencers\", 1, \"see-all-btn\"], [1, \"fas\", \"fa-th-large\"], [1, \"see-all-btn\", 3, \"click\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"sidebar-footer\"], [1, \"footer-links\"], [\"href\", \"#\", \"routerLink\", \"/about\"], [\"href\", \"#\", \"routerLink\", \"/help\"], [\"href\", \"#\", \"routerLink\", \"/press\"], [\"href\", \"#\", \"routerLink\", \"/api\"], [\"href\", \"#\", \"routerLink\", \"/jobs\"], [\"href\", \"#\", \"routerLink\", \"/privacy\"], [\"href\", \"#\", \"routerLink\", \"/terms\"], [1, \"copyright\"], [1, \"profile-card\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"profile-username\"], [1, \"profile-name\"], [\"routerLink\", \"/profile\", 1, \"switch-btn\"], [1, \"fas\", \"fa-sun\"], [1, \"collection-items\"], [\"class\", \"collection-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"collection-item\", 3, \"click\"], [1, \"collection-image\", 3, \"src\", \"alt\"], [1, \"collection-info\"], [1, \"collection-name\"], [1, \"collection-price\"], [\"class\", \"collection-discount\", 4, \"ngIf\"], [1, \"collection-discount\"], [1, \"fas\", \"fa-star\"], [1, \"featured-grid\"], [\"class\", \"featured-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"featured-item\", 3, \"click\"], [1, \"featured-image\", 3, \"src\", \"alt\"], [1, \"featured-info\"], [1, \"featured-name\"], [1, \"featured-brand\"], [1, \"featured-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"fas\", \"fa-sparkles\"], [1, \"arrivals-list\"], [\"class\", \"arrival-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"arrival-item\", 3, \"click\"], [1, \"arrival-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [1, \"arrival-info\"], [1, \"arrival-name\"], [1, \"arrival-brand\"], [1, \"arrival-price\"], [1, \"category-item\", 3, \"click\"], [1, \"category-image\", 3, \"src\", \"alt\"], [1, \"category-name\"]],\n        template: function SidebarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, SidebarComponent_div_1_Template, 9, 4, \"div\", 1)(2, SidebarComponent_section_2_Template, 9, 2, \"section\", 2);\n            i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"h3\");\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵtext(7, \" Suggested for You\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"button\", 6);\n            i0.ɵɵtext(9, \"See All\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(10, \"app-suggested-products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(11, SidebarComponent_section_11_Template, 9, 2, \"section\", 2);\n            i0.ɵɵelementStart(12, \"section\", 3)(13, \"div\", 4)(14, \"h3\");\n            i0.ɵɵelement(15, \"i\", 7);\n            i0.ɵɵtext(16, \" Trending Now\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"button\", 8);\n            i0.ɵɵtext(18, \"See All\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(19, \"app-trending-products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(20, SidebarComponent_section_20_Template, 9, 2, \"section\", 2);\n            i0.ɵɵelementStart(21, \"section\", 3)(22, \"div\", 4)(23, \"h3\");\n            i0.ɵɵelement(24, \"i\", 9);\n            i0.ɵɵtext(25, \" Top Influencers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"button\", 10);\n            i0.ɵɵtext(27, \"See All\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(28, \"app-top-influencers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"section\", 3)(30, \"div\", 4)(31, \"h3\");\n            i0.ɵɵelement(32, \"i\", 11);\n            i0.ɵɵtext(33, \" Shop by Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_34_listener() {\n              return ctx.viewAllCategories();\n            });\n            i0.ɵɵtext(35, \"See All\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 13);\n            i0.ɵɵtemplate(37, SidebarComponent_div_37_Template, 4, 3, \"div\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 15)(39, \"div\", 16)(40, \"a\", 17);\n            i0.ɵɵtext(41, \"About\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"a\", 18);\n            i0.ɵɵtext(43, \"Help\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"a\", 19);\n            i0.ɵɵtext(45, \"Press\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"a\", 20);\n            i0.ɵɵtext(47, \"API\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"a\", 21);\n            i0.ɵɵtext(49, \"Jobs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"a\", 22);\n            i0.ɵɵtext(51, \"Privacy\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"a\", 23);\n            i0.ɵɵtext(53, \"Terms\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"div\", 24)(55, \"span\");\n            i0.ɵɵtext(56, \"\\u00A9 2024 DFashion\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.summerCollection.length > 0);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.featuredProducts.length > 0);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.newArrivals.length > 0);\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getDisplayCategories())(\"ngForTrackBy\", ctx.trackByCategoryId);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, i1.RouterLink, SuggestedProductsComponent, TrendingProductsComponent, TopInfluencersComponent],\n        styles: [\".home-sidebar[_ngcontent-%COMP%]{width:320px;padding:0 16px;position:sticky;top:84px;height:-moz-fit-content;height:fit-content;max-height:calc(100vh - 100px);overflow-y:auto}@media (max-width: 1024px){.home-sidebar[_ngcontent-%COMP%]{display:none}}.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#dbdbdb;border-radius:2px}.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#c7c7c7}.profile-card[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px 0;margin-bottom:24px;border-bottom:1px solid #efefef}.profile-card[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;object-fit:cover;margin-right:12px}.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]{flex:1}.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#262626;margin:0 0 2px}.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e}.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]{background:none;border:none;color:#0095f6;font-size:12px;font-weight:600;cursor:pointer}.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]:hover{color:#00376b}.sidebar-section[_ngcontent-%COMP%]{margin-bottom:32px}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#8e8e8e;margin:0;display:flex;align-items:center;gap:8px}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;color:#0095f6}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]{background:none;border:none;color:#262626;font-size:12px;font-weight:400;cursor:pointer;transition:color .2s ease}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover{color:#0095f6}.collection-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:8px;border-radius:8px;cursor:pointer;transition:background-color .2s ease}.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]:hover{background-color:#fafafa}.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]{width:44px;height:44px;border-radius:6px;object-fit:cover}.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]{flex:1}.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:500;color:#262626;margin-bottom:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e}.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-discount[_ngcontent-%COMP%]{font-size:10px;color:#ed4956;font-weight:600;margin-top:2px}.featured-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:12px}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]{cursor:pointer;border-radius:8px;overflow:hidden;transition:transform .2s ease}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image[_ngcontent-%COMP%]{width:100%;height:120px;object-fit:cover}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]{padding:8px;background:#fff}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:500;color:#262626;margin-bottom:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%]{display:block;font-size:11px;color:#8e8e8e;margin-bottom:4px}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#262626}.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:10px;color:#8e8e8e;text-decoration:line-through;margin-left:4px}.arrivals-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:8px;border-radius:8px;cursor:pointer;position:relative;transition:background-color .2s ease}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]:hover{background-color:#fafafa}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-image[_ngcontent-%COMP%]{width:44px;height:44px;border-radius:6px;object-fit:cover}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]{position:absolute;top:4px;left:4px;background:#ed4956;color:#fff;font-size:8px;font-weight:600;padding:2px 4px;border-radius:3px}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]{flex:1}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:500;color:#262626;margin-bottom:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%]{display:block;font-size:12px;color:#8e8e8e;margin-bottom:2px}.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#262626}.categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:12px}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:12px 8px;border-radius:12px;cursor:pointer;transition:all .2s ease;border:1px solid #efefef}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover{background-color:#fafafa;transform:translateY(-2px);box-shadow:0 4px 12px #0000001a}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;object-fit:cover;margin-bottom:8px;border:2px solid #fff;box-shadow:0 2px 8px #0000001a}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#262626;text-align:center}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:6px;background:linear-gradient(45deg,#0095f6,#00d4ff);display:flex;align-items:center;justify-content:center}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:14px}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]{flex:1}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:500;color:#262626;margin-bottom:2px}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%]{font-size:10px;color:#8e8e8e}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .trending-indicator[_ngcontent-%COMP%]{position:absolute;top:4px;right:4px}.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .trending-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ed4956;font-size:10px}.sidebar-footer[_ngcontent-%COMP%]{margin-top:32px;padding-top:16px;border-top:1px solid #efefef}.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:16px}.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-size:11px;color:#c7c7c7;text-decoration:none}.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#8e8e8e}.sidebar-footer[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%]{font-size:11px;color:#c7c7c7}\"]\n      });\n    }\n  }\n  return SidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}