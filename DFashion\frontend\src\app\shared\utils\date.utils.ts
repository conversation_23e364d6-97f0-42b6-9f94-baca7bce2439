/**
 * Date utility functions for consistent date handling across the application
 */

export class DateUtils {
  /**
   * Get time ago string from a date
   * @param date - Date object or string
   * @returns Human readable time ago string
   */
  static getTimeAgo(date: Date | string | null | undefined): string {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    
    // Handle different date formats
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return 'Unknown';
    }
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Unknown';
    }
    
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    // Handle future dates
    if (diffInSeconds < 0) {
      return 'Just now';
    }

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    return `${Math.floor(diffInSeconds / 604800)}w`;
  }

  /**
   * Get detailed time ago string
   * @param date - Date object or string
   * @returns Detailed human readable time ago string
   */
  static getDetailedTimeAgo(date: Date | string | null | undefined): string {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return 'Unknown';
    }
    
    if (isNaN(dateObj.getTime())) {
      return 'Unknown';
    }
    
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    if (diffInSeconds < 0) {
      return 'just now';
    }

    const intervals = [
      { label: 'year', seconds: 31536000 },
      { label: 'month', seconds: 2592000 },
      { label: 'week', seconds: 604800 },
      { label: 'day', seconds: 86400 },
      { label: 'hour', seconds: 3600 },
      { label: 'minute', seconds: 60 },
      { label: 'second', seconds: 1 }
    ];

    for (const interval of intervals) {
      const count = Math.floor(diffInSeconds / interval.seconds);
      if (count >= 1) {
        return count === 1 
          ? `1 ${interval.label} ago`
          : `${count} ${interval.label}s ago`;
      }
    }

    return 'just now';
  }

  /**
   * Format date for display
   * @param date - Date object or string
   * @param format - Format type ('short', 'medium', 'long')
   * @returns Formatted date string
   */
  static formatDate(date: Date | string | null | undefined, format: 'short' | 'medium' | 'long' = 'medium'): string {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return 'Unknown';
    }
    
    if (isNaN(dateObj.getTime())) {
      return 'Unknown';
    }

    const options: Intl.DateTimeFormatOptions = {};
    
    switch (format) {
      case 'short':
        options.month = 'short';
        options.day = 'numeric';
        break;
      case 'medium':
        options.year = 'numeric';
        options.month = 'short';
        options.day = 'numeric';
        break;
      case 'long':
        options.year = 'numeric';
        options.month = 'long';
        options.day = 'numeric';
        options.hour = '2-digit';
        options.minute = '2-digit';
        break;
    }

    return dateObj.toLocaleDateString('en-US', options);
  }

  /**
   * Check if date is valid
   * @param date - Date object or string
   * @returns Boolean indicating if date is valid
   */
  static isValidDate(date: Date | string | null | undefined): boolean {
    if (!date) return false;
    
    let dateObj: Date;
    
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return false;
    }
    
    return !isNaN(dateObj.getTime());
  }

  /**
   * Get time remaining until expiry
   * @param expiryDate - Expiry date
   * @returns Time remaining in milliseconds
   */
  static getTimeRemaining(expiryDate: Date | string): number {
    if (!expiryDate) return 0;
    
    let dateObj: Date;
    
    if (typeof expiryDate === 'string') {
      dateObj = new Date(expiryDate);
    } else {
      dateObj = expiryDate;
    }
    
    if (isNaN(dateObj.getTime())) {
      return 0;
    }
    
    const now = new Date().getTime();
    const expiry = dateObj.getTime();
    return Math.max(0, expiry - now);
  }

  /**
   * Format duration in milliseconds to human readable string
   * @param milliseconds - Duration in milliseconds
   * @returns Human readable duration string
   */
  static formatDuration(milliseconds: number): string {
    if (milliseconds <= 0) return '0m';
    
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }
}
