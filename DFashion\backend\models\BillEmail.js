const mongoose = require('mongoose');

const billEmailSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  billpdf: {
    type: String,
    required: true
  },
  message: {
    type: String,
    default: 'Thank you for your purchase! Please find your invoice attached.'
  },
  email: {
    type: String,
    required: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  paymentType: {
    type: Object,
    required: true,
    default: {}
  },
  sentAt: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pending', 'sent', 'failed'],
    default: 'pending'
  },
  emailProvider: {
    type: String,
    default: 'gmail'
  },
  retryCount: {
    type: Number,
    default: 0
  },
  lastRetryAt: {
    type: Date
  },
  errorMessage: {
    type: String
  }
}, {
  timestamps: true
});

// Index for efficient queries
billEmailSchema.index({ email: 1, sentAt: -1 });
billEmailSchema.index({ userId: 1, sentAt: -1 });
billEmailSchema.index({ status: 1 });

// Virtual for formatted sent date
billEmailSchema.virtual('formattedSentAt').get(function() {
  return this.sentAt.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// Method to mark as sent
billEmailSchema.methods.markAsSent = function() {
  this.status = 'sent';
  this.sentAt = new Date();
  return this.save();
};

// Method to mark as failed
billEmailSchema.methods.markAsFailed = function(errorMessage) {
  this.status = 'failed';
  this.errorMessage = errorMessage;
  this.retryCount += 1;
  this.lastRetryAt = new Date();
  return this.save();
};

// Static method to find bills by user
billEmailSchema.statics.findByUser = function(userId) {
  return this.find({ userId }).sort({ sentAt: -1 });
};

// Static method to find failed bills for retry
billEmailSchema.statics.findFailedBills = function() {
  return this.find({ 
    status: 'failed',
    retryCount: { $lt: 3 } // Retry up to 3 times
  });
};

module.exports = mongoose.model('BillEmail', billEmailSchema);
