<div class="profile-container">
  <!-- Profile Header -->
  <div class="profile-header">
    <div class="user-avatar">
      <img [src]="currentUser?.avatar || 'assets/images/default-avatar.png'" 
           [alt]="currentUser?.fullName">
      <div class="role-badge" [style.background-color]="getRoleColor()">
        {{ getRoleDisplayName() }}
      </div>
    </div>
    
    <div class="user-info">
      <h1 class="user-name">{{ currentUser?.fullName }}</h1>
      <p class="user-email">{{ currentUser?.email }}</p>
      <p class="user-username">{{ '@' + (currentUser?.username || '') }}</p>
      
      <!-- Role-specific information -->
      <div class="role-info">
        <span class="role-label">Role:</span>
        <span class="role-value" [style.color]="getRoleColor()">
          {{ getRoleDisplayName() }}
        </span>
      </div>
      
      <!-- Vendor info for sellers -->
      <div *appRoleAccess="'role:seller'" class="vendor-info">
        <span class="vendor-status" 
              [class.approved]="currentUser?.vendorInfo?.isApproved"
              [class.pending]="!currentUser?.vendorInfo?.isApproved">
          {{ currentUser?.vendorInfo?.isApproved ? 'Verified Seller' : 'Pending Verification' }}
        </span>
      </div>
    </div>
    
    <div class="profile-actions">
      <button class="btn-edit" 
              *appRoleAccess="'profile:write'"
              (click)="updateProfile()">
        <i class="fas fa-edit"></i>
        Edit Profile
      </button>
      
      <button class="btn-security" 
              *appRoleAccess="'profile:write'"
              (click)="changePassword()">
        <i class="fas fa-key"></i>
        Change Password
      </button>
    </div>
  </div>

  <!-- Profile Sections Grid -->
  <div class="profile-sections">
    <div *ngFor="let section of profileSections" 
         class="section-card"
         [class.editable]="section.editable"
         (click)="navigateToSection(section.id)">
      
      <div class="section-icon">
        <i [class]="section.icon"></i>
      </div>
      
      <div class="section-content">
        <h3 class="section-title">{{ section.title }}</h3>
        <p class="section-description">
          {{ getSectionDescription(section.id) }}
        </p>
      </div>
      
      <div class="section-actions">
        <i class="fas fa-chevron-right"></i>
        
        <!-- Edit indicator for editable sections -->
        <span *ngIf="section.editable" class="edit-indicator">
          <i class="fas fa-edit"></i>
        </span>
      </div>
    </div>
  </div>

  <!-- Role-specific Quick Actions -->
  <div class="quick-actions">
    <h2>Quick Actions</h2>
    
    <div class="actions-grid">
      <!-- Buyer Actions -->
      <div *appRoleAccess="'role:buyer'" class="action-group">
        <h3>Shopping</h3>
        <button class="action-btn" routerLink="/cart">
          <i class="fas fa-shopping-cart"></i>
          View Cart
        </button>
        <button class="action-btn" routerLink="/wishlist">
          <i class="fas fa-heart"></i>
          My Wishlist
        </button>
        <button class="action-btn" routerLink="/orders">
          <i class="fas fa-receipt"></i>
          Order History
        </button>
      </div>

      <!-- Seller Actions -->
      <div *appRoleAccess="'role:seller'" class="action-group">
        <h3>Seller Tools</h3>
        <button class="action-btn" routerLink="/vendor/products">
          <i class="fas fa-box"></i>
          Manage Products
        </button>
        <button class="action-btn" routerLink="/vendor/orders">
          <i class="fas fa-clipboard-list"></i>
          Seller Orders
        </button>
        <button class="action-btn" routerLink="/vendor/analytics">
          <i class="fas fa-chart-line"></i>
          Sales Analytics
        </button>
        <button class="action-btn" routerLink="/vendor/posts">
          <i class="fas fa-camera"></i>
          Create Content
        </button>
      </div>

      <!-- Admin Actions -->
      <div *appRoleAccess="'level:3'" class="action-group">
        <h3>Administration</h3>
        <button class="action-btn" routerLink="/admin/dashboard">
          <i class="fas fa-tachometer-alt"></i>
          Admin Dashboard
        </button>
        <button class="action-btn" 
                *appRoleAccess="'users:read'"
                routerLink="/admin/users">
          <i class="fas fa-users"></i>
          User Management
        </button>
        <button class="action-btn" 
                *appRoleAccess="'products:admin'"
                routerLink="/admin/products">
          <i class="fas fa-boxes"></i>
          Product Management
        </button>
        <button class="action-btn" 
                *appRoleAccess="'analytics:read'"
                routerLink="/admin/analytics">
          <i class="fas fa-chart-bar"></i>
          System Analytics
        </button>
      </div>
    </div>
  </div>

  <!-- Permissions Overview (for admins) -->
  <div *appRoleAccess="'level:3'" class="permissions-overview">
    <h2>Access Permissions</h2>
    
    <div class="permissions-grid">
      <div class="permission-category">
        <h4>Available Modules</h4>
        <div class="permission-list">
          <span *ngFor="let module of availableModules" 
                class="permission-tag available">
            {{ module }}
          </span>
        </div>
      </div>
      
      <div class="permission-category">
        <h4>Editable Modules</h4>
        <div class="permission-list">
          <span *ngFor="let module of editableModules" 
                class="permission-tag editable">
            {{ module }}
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Account Statistics -->
  <div class="account-stats">
    <h2>Account Statistics</h2>
    
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="stat-content">
          <h4>Member Since</h4>
          <p>{{ currentUser?.createdAt | date:'MMM yyyy' }}</p>
        </div>
      </div>
      
      <div class="stat-card" *appRoleAccess="'orders:read'">
        <div class="stat-icon">
          <i class="fas fa-shopping-bag"></i>
        </div>
        <div class="stat-content">
          <h4>Total Orders</h4>
          <p>{{ currentUser?.orderCount || 0 }}</p>
        </div>
      </div>
      
      <div class="stat-card" *appRoleAccess="'role:seller'">
        <div class="stat-icon">
          <i class="fas fa-box"></i>
        </div>
        <div class="stat-content">
          <h4>Products Listed</h4>
          <p>{{ currentUser?.productCount || 0 }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-heart"></i>
        </div>
        <div class="stat-content">
          <h4>Wishlist Items</h4>
          <p>{{ currentUser?.wishlistCount || 0 }}</p>
        </div>
      </div>
    </div>
  </div>
</div>
