{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { j as debounceEvent, i as inheritAriaAttributes, k as inheritAttributes, c as componentOnReady, h as findItemLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-a445f677.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as closeCircle, d as closeSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\nconst inputIosCss = \".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}.legacy-input.sc-ion-input-ios-h{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;background:var(--background)}.legacy-input.sc-ion-input-ios-h .native-input.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}ion-item.sc-ion-input-ios-h:not(.item-label):not(.item-has-modern-input),ion-item:not(.item-label):not(.item-has-modern-input) .sc-ion-input-ios-h{--padding-start:0}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.legacy-input.ion-color.sc-ion-input-ios-h{color:var(--ion-color-base)}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.sc-ion-input-ios-h:not(.legacy-input){min-height:44px}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-input-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-input-ios{left:0}[dir=rtl].sc-ion-input-ios-h .cloned-input.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h .cloned-input.sc-ion-input-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-ios .cloned-input.sc-ion-input-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-input-ios:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.legacy-input.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, #666666);visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.has-focus.legacy-input.sc-ion-input-ios-h{pointer-events:none}.has-focus.legacy-input.sc-ion-input-ios-h input.sc-ion-input-ios,.has-focus.legacy-input.sc-ion-input-ios-h a.sc-ion-input-ios,.has-focus.legacy-input.sc-ion-input-ios-h button.sc-ion-input-ios{pointer-events:auto}.item-label-floating.item-has-placeholder.sc-ion-input-ios-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-input-ios-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-input-ios-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-input-ios-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));font-size:inherit}.legacy-input.sc-ion-input-ios-h{--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:0}.item-label-stacked.sc-ion-input-ios-h,.item-label-stacked .sc-ion-input-ios-h,.item-label-floating.sc-ion-input-ios-h,.item-label-floating .sc-ion-input-ios-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0px}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.legacy-input.sc-ion-input-ios-h .native-input[disabled].sc-ion-input-ios,.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonInputIosStyle0 = inputIosCss;\nconst inputMdCss = \".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}.legacy-input.sc-ion-input-md-h{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;background:var(--background)}.legacy-input.sc-ion-input-md-h .native-input.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}ion-item.sc-ion-input-md-h:not(.item-label):not(.item-has-modern-input),ion-item:not(.item-label):not(.item-has-modern-input) .sc-ion-input-md-h{--padding-start:0}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.legacy-input.ion-color.sc-ion-input-md-h{color:var(--ion-color-base)}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.sc-ion-input-md-h:not(.legacy-input){min-height:44px}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h .cloned-input.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h .cloned-input.sc-ion-input-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-md .cloned-input.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-input-md:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-input-md:disabled{opacity:1}.legacy-input.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, #666666);visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.has-focus.legacy-input.sc-ion-input-md-h{pointer-events:none}.has-focus.legacy-input.sc-ion-input-md-h input.sc-ion-input-md,.has-focus.legacy-input.sc-ion-input-md-h a.sc-ion-input-md,.has-focus.legacy-input.sc-ion-input-md-h button.sc-ion-input-md{pointer-events:auto}.item-label-floating.item-has-placeholder.sc-ion-input-md-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-input-md-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-input-md-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-input-md-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl].input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.input-fill-solid.sc-ion-input-md-h:dir(rtl) .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, #404040)}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:2px;--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl].input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl] .input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.input-fill-outline.sc-ion-input-md-h:dir(rtl) .input-outline-start.sc-ion-input-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px;-ms-flex-positive:1;flex-grow:1}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl].input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl] .input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}@supports selector(:dir(rtl)){.input-fill-outline.sc-ion-input-md-h:dir(rtl) .input-outline-end.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));font-size:inherit}.legacy-input.sc-ion-input-md-h{--padding-top:10px;--padding-end:0;--padding-bottom:10px;--padding-start:8px}.item-label-stacked.sc-ion-input-md-h,.item-label-stacked .sc-ion-input-md-h,.item-label-floating.sc-ion-input-md-h,.item-label-floating .sc-ion-input-md-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.legacy-input.sc-ion-input-md-h .native-input[disabled].sc-ion-input-md,.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.input-highlight.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.input-highlight.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-md .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.input-highlight.sc-ion-input-md:dir(rtl){left:unset;right:unset;right:0}}}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}@supports (inset-inline-start: 0){.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h -no-combinator.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl].in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.in-item.sc-ion-input-md-h:dir(rtl) .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}}}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonInputMdStyle0 = inputMdCss;\nconst Input = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-input-${inputIds++}`;\n    this.inheritedAttributes = {};\n    this.isComposing = false;\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    /**\n     * `true` if the input was cleared as a result of the user typing\n     * with `clearOnEdit` enabled.\n     *\n     * Resets when the input loses focus.\n     */\n    this.didInputClearOnEdit = false;\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value || '';\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    this.onBlur = ev => {\n      this.hasFocus = false;\n      this.emitStyle();\n      if (this.focusedValue !== this.value) {\n        /**\n         * Emits the `ionChange` event when the input value\n         * is different than the value when the input was focused.\n         */\n        this.emitValueChange(ev);\n      }\n      this.didInputClearOnEdit = false;\n      this.ionBlur.emit(ev);\n    };\n    this.onFocus = ev => {\n      this.hasFocus = true;\n      this.focusedValue = this.value;\n      this.emitStyle();\n      this.ionFocus.emit(ev);\n    };\n    this.onKeydown = ev => {\n      this.checkClearOnEdit(ev);\n    };\n    this.onCompositionStart = () => {\n      this.isComposing = true;\n    };\n    this.onCompositionEnd = () => {\n      this.isComposing = false;\n    };\n    this.clearTextInput = ev => {\n      if (this.clearInput && !this.readonly && !this.disabled && ev) {\n        ev.preventDefault();\n        ev.stopPropagation();\n        // Attempt to focus input again after pressing clear button\n        this.setFocus();\n      }\n      this.value = '';\n      this.emitInputChange(ev);\n    };\n    this.hasFocus = false;\n    this.color = undefined;\n    this.accept = undefined;\n    this.autocapitalize = 'off';\n    this.autocomplete = 'off';\n    this.autocorrect = 'off';\n    this.autofocus = false;\n    this.clearInput = false;\n    this.clearOnEdit = undefined;\n    this.counter = false;\n    this.counterFormatter = undefined;\n    this.debounce = undefined;\n    this.disabled = false;\n    this.enterkeyhint = undefined;\n    this.errorText = undefined;\n    this.fill = undefined;\n    this.inputmode = undefined;\n    this.helperText = undefined;\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.legacy = undefined;\n    this.max = undefined;\n    this.maxlength = undefined;\n    this.min = undefined;\n    this.minlength = undefined;\n    this.multiple = undefined;\n    this.name = this.inputId;\n    this.pattern = undefined;\n    this.placeholder = undefined;\n    this.readonly = false;\n    this.required = false;\n    this.shape = undefined;\n    this.spellcheck = false;\n    this.step = undefined;\n    this.size = undefined;\n    this.type = 'text';\n    this.value = '';\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  disabledChanged() {\n    this.emitStyle();\n  }\n  /**\n   * Update the item classes when the placeholder changes\n   */\n  placeholderChanged() {\n    this.emitStyle();\n  }\n  /**\n   * Update the native input element when the value changes\n   */\n  valueChanged() {\n    const nativeInput = this.nativeInput;\n    const value = this.getValue();\n    if (nativeInput && nativeInput.value !== value && !this.isComposing) {\n      /**\n       * Assigning the native input's value on attribute\n       * value change, allows `ionInput` implementations\n       * to override the control's value.\n       *\n       * Used for patterns such as input trimming (removing whitespace),\n       * or input masking.\n       */\n      nativeInput.value = value;\n    }\n    this.emitStyle();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['tabindex', 'title', 'data-form-type']));\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    this.legacyFormController = createLegacyFormController(el);\n    this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n    this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n    this.emitStyle();\n    this.debounceChanged();\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n        detail: this.el\n      }));\n    }\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  disconnectedCallback() {\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n        detail: this.el\n      }));\n    }\n    if (this.slotMutationController) {\n      this.slotMutationController.destroy();\n      this.slotMutationController = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Sets focus on the native `input` in `ion-input`. Use this method instead of the global\n   * `input.focus()`.\n   *\n   * Developers who wish to focus an input when a page enters\n   * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n   *\n   * Developers who wish to focus an input when an overlay is presented\n   * should call `setFocus` after `didPresent` has resolved.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  setFocus() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.nativeInput) {\n        _this.nativeInput.focus();\n      }\n    })();\n  }\n  /**\n   * Returns the native `<input>` element used under the hood.\n   */\n  getInputElement() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n       * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n       */\n      if (!_this2.nativeInput) {\n        yield new Promise(resolve => componentOnReady(_this2.el, resolve));\n      }\n      return Promise.resolve(_this2.nativeInput);\n    })();\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionInput.emit({\n      value: newValue,\n      event\n    });\n  }\n  shouldClearOnEdit() {\n    const {\n      type,\n      clearOnEdit\n    } = this;\n    return clearOnEdit === undefined ? type === 'password' : clearOnEdit;\n  }\n  getValue() {\n    return typeof this.value === 'number' ? this.value.toString() : (this.value || '').toString();\n  }\n  emitStyle() {\n    if (this.legacyFormController.hasLegacyControl()) {\n      this.ionStyle.emit({\n        interactive: true,\n        input: true,\n        'has-placeholder': this.placeholder !== undefined,\n        'has-value': this.hasValue(),\n        'has-focus': this.hasFocus,\n        'interactive-disabled': this.disabled,\n        // TODO(FW-2764): remove this\n        legacy: !!this.legacy\n      });\n    }\n  }\n  checkClearOnEdit(ev) {\n    if (!this.shouldClearOnEdit()) {\n      return;\n    }\n    /**\n     * The following keys do not modify the\n     * contents of the input. As a result, pressing\n     * them should not edit the input.\n     *\n     * We can't check to see if the value of the input\n     * was changed because we call checkClearOnEdit\n     * in a keydown listener, and the key has not yet\n     * been added to the input.\n     */\n    const IGNORED_KEYS = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n    const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n    /**\n     * Clear the input if the control has not been previously cleared during focus.\n     * Do not clear if the user hitting enter to submit a form.\n     */\n    if (!this.didInputClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n      this.value = '';\n      this.emitInputChange(ev);\n    }\n    /**\n     * Pressing an IGNORED_KEYS first and\n     * then an allowed key will cause the input to not\n     * be cleared.\n     */\n    if (!pressedIgnoredKey) {\n      this.didInputClearOnEdit = true;\n    }\n  }\n  hasValue() {\n    return this.getValue().length > 0;\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText\n    } = this;\n    return [h(\"div\", {\n      class: \"helper-text\"\n    }, helperText), h(\"div\", {\n      class: \"error-text\"\n    }, errorText)];\n  }\n  renderCounter() {\n    const {\n      counter,\n      maxlength,\n      counterFormatter,\n      value\n    } = this;\n    if (counter !== true || maxlength === undefined) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"counter\"\n    }, getCounterText(value, maxlength, counterFormatter));\n  }\n  /**\n   * Responsible for rendering helper text,\n   * error text, and counter. This element should only\n   * be rendered if hint text is set or counter is enabled.\n   */\n  renderBottomContent() {\n    const {\n      counter,\n      helperText,\n      errorText,\n      maxlength\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    const hasCounter = counter === true && maxlength !== undefined;\n    if (!hasHintText && !hasCounter) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"input-bottom\"\n    }, this.renderHintText(), this.renderCounter());\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      }\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the input and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"input-outline-container\"\n      }, h(\"div\", {\n        class: \"input-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'input-outline-notch': true,\n          'input-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"input-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  renderInput() {\n    const {\n      disabled,\n      fill,\n      readonly,\n      shape,\n      inputId,\n      labelPlacement,\n      el,\n      hasFocus\n    } = this;\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    /**\n     * If the label is stacked, it should always sit above the input.\n     * For floating labels, the label should move above the input if\n     * the input has a value, is focused, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the input is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots);\n    return h(Host, {\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'has-value': hasValue,\n        'has-focus': hasFocus,\n        'label-floating': labelShouldFloat,\n        [`input-fill-${fill}`]: fill !== undefined,\n        [`input-shape-${shape}`]: shape !== undefined,\n        [`input-label-placement-${labelPlacement}`]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', this.el),\n        'input-disabled': disabled\n      })\n    }, h(\"label\", {\n      class: \"input-wrapper\",\n      htmlFor: inputId\n    }, this.renderLabelContainer(), h(\"div\", {\n      class: \"native-wrapper\"\n    }, h(\"slot\", {\n      name: \"start\"\n    }), h(\"input\", Object.assign({\n      class: \"native-input\",\n      ref: input => this.nativeInput = input,\n      id: inputId,\n      disabled: disabled,\n      accept: this.accept,\n      autoCapitalize: this.autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      min: this.min,\n      max: this.max,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      multiple: this.multiple,\n      name: this.name,\n      pattern: this.pattern,\n      placeholder: this.placeholder || '',\n      readOnly: readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      step: this.step,\n      size: this.size,\n      type: this.type,\n      value: value,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeydown,\n      onCompositionstart: this.onCompositionStart,\n      onCompositionend: this.onCompositionEnd\n    }, this.inheritedAttributes)), this.clearInput && !readonly && !disabled && h(\"button\", {\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      class: \"input-clear-icon\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onFocusin: ev => {\n        /**\n         * Prevent the focusin event from bubbling otherwise it will cause the focusin\n         * event listener in scroll assist to fire. When this fires, focus will be moved\n         * back to the input even if the clear button was never tapped. This poses issues\n         * for screen readers as it means users would be unable to swipe past the clear button.\n         */\n        ev.stopPropagation();\n      },\n      onClick: this.clearTextInput\n    }, h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: mode === 'ios' ? closeCircle : closeSharp\n    })), h(\"slot\", {\n      name: \"end\"\n    })), shouldRenderHighlight && h(\"div\", {\n      class: \"input-highlight\"\n    })), this.renderBottomContent());\n  }\n  // TODO FW-2764 Remove this\n  renderLegacyInput() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-input now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-input label=\"Email\"></ion-input>\nExample with aria-label: <ion-input aria-label=\"Email\"></ion-input>\n\nFor inputs that do not render the label immediately next to the input, developers may continue to use \"ion-label\" but must manually associate the label with the input by using \"aria-labelledby\".\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-input is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new input syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const labelId = this.inputId + '-lbl';\n    const label = findItemLabel(this.el);\n    if (label) {\n      label.id = labelId;\n    }\n    return h(Host, {\n      \"aria-disabled\": this.disabled ? 'true' : null,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'has-value': this.hasValue(),\n        'has-focus': this.hasFocus,\n        'legacy-input': true,\n        'in-item-color': hostContext('ion-item.ion-color', this.el)\n      })\n    }, h(\"input\", Object.assign({\n      class: \"native-input\",\n      ref: input => this.nativeInput = input,\n      \"aria-labelledby\": label ? label.id : null,\n      disabled: this.disabled,\n      accept: this.accept,\n      autoCapitalize: this.autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      min: this.min,\n      max: this.max,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      multiple: this.multiple,\n      name: this.name,\n      pattern: this.pattern,\n      placeholder: this.placeholder || '',\n      readOnly: this.readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      step: this.step,\n      size: this.size,\n      type: this.type,\n      value: value,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeydown\n    }, this.inheritedAttributes)), this.clearInput && !this.readonly && !this.disabled && h(\"button\", {\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      class: \"input-clear-icon\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: this.clearTextInput\n    }, h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: mode === 'ios' ? closeCircle : closeSharp\n    })));\n  }\n  render() {\n    const {\n      legacyFormController\n    } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacyInput() : this.renderInput();\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"debounce\": [\"debounceChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"placeholder\": [\"placeholderChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet inputIds = 0;\nInput.style = {\n  ios: IonInputIosStyle0,\n  md: IonInputMdStyle0\n};\nexport { Input as ion_input };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "i", "forceUpdate", "c", "createLegacyFormController", "createNotchController", "j", "debounceEvent", "inheritAriaAttributes", "k", "inheritAttributes", "componentOnReady", "findItemLabel", "p", "printIonWarning", "createSlotMutationController", "g", "getCounterText", "hostContext", "createColorClasses", "b", "closeCircle", "closeSharp", "getIonMode", "inputIosCss", "IonInputIosStyle0", "inputMdCss", "IonInputMdStyle0", "Input", "constructor", "hostRef", "ionInput", "ionChange", "ionBlur", "ionFocus", "ionStyle", "inputId", "inputIds", "inheritedAttributes", "isComposing", "hasLoggedDeprecationWarning", "didInputClearOnEdit", "onInput", "ev", "input", "target", "value", "emitInputChange", "onChange", "emitValueChange", "onBlur", "hasFocus", "emitStyle", "focusedValue", "emit", "onFocus", "onKeydown", "checkClearOnEdit", "onCompositionStart", "onCompositionEnd", "clearTextInput", "clearInput", "readonly", "disabled", "preventDefault", "stopPropagation", "setFocus", "color", "undefined", "accept", "autocapitalize", "autocomplete", "autocorrect", "autofocus", "clearOnEdit", "counter", "counterFormatter", "debounce", "enterkeyhint", "errorText", "fill", "inputmode", "helperText", "label", "labelPlacement", "legacy", "max", "maxlength", "min", "minlength", "multiple", "name", "pattern", "placeholder", "required", "shape", "spellcheck", "step", "size", "type", "debounce<PERSON><PERSON>ed", "originalIonInput", "disabled<PERSON><PERSON>ed", "placeholder<PERSON><PERSON>ed", "valueChanged", "nativeInput", "getValue", "componentWillLoad", "Object", "assign", "el", "connectedCallback", "legacyFormController", "slotMutationController", "notchController", "notchSpacerEl", "labelSlot", "document", "dispatchEvent", "CustomEvent", "detail", "componentDidLoad", "componentDidRender", "_a", "calculateNotchWidth", "disconnectedCallback", "destroy", "_this", "_asyncToGenerator", "focus", "getInputElement", "_this2", "Promise", "resolve", "event", "newValue", "toString", "shouldClearOnEdit", "hasLegacyControl", "interactive", "hasValue", "IGNORED_KEYS", "pressedIgnoredKey", "includes", "key", "length", "renderHintText", "class", "renderCounter", "renderBottomContent", "hasHintText", "<PERSON><PERSON><PERSON><PERSON>", "renderLabel", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "renderLabelContainer", "mode", "hasOutlineFill", "ref", "renderInput", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "labelShouldFloat", "htmlFor", "id", "autoCapitalize", "autoComplete", "autoCorrect", "autoFocus", "enterKeyHint", "inputMode", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "readOnly", "onKeyDown", "onCompositionstart", "onCompositionend", "onPointerDown", "onFocusin", "onClick", "icon", "renderLegacyInput", "labelId", "render", "watchers", "style", "ios", "md", "ion_input"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-input.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { j as debounceEvent, i as inheritAriaAttributes, k as inheritAttributes, c as componentOnReady, h as findItemLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-a445f677.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as closeCircle, d as closeSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\n\nconst inputIosCss = \".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}.legacy-input.sc-ion-input-ios-h{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;background:var(--background)}.legacy-input.sc-ion-input-ios-h .native-input.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}ion-item.sc-ion-input-ios-h:not(.item-label):not(.item-has-modern-input),ion-item:not(.item-label):not(.item-has-modern-input) .sc-ion-input-ios-h{--padding-start:0}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.legacy-input.ion-color.sc-ion-input-ios-h{color:var(--ion-color-base)}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.sc-ion-input-ios-h:not(.legacy-input){min-height:44px}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-input-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-input-ios{left:0}[dir=rtl].sc-ion-input-ios-h .cloned-input.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h .cloned-input.sc-ion-input-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-ios .cloned-input.sc-ion-input-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-input-ios:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.legacy-input.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, #666666);visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.has-focus.legacy-input.sc-ion-input-ios-h{pointer-events:none}.has-focus.legacy-input.sc-ion-input-ios-h input.sc-ion-input-ios,.has-focus.legacy-input.sc-ion-input-ios-h a.sc-ion-input-ios,.has-focus.legacy-input.sc-ion-input-ios-h button.sc-ion-input-ios{pointer-events:auto}.item-label-floating.item-has-placeholder.sc-ion-input-ios-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-input-ios-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-input-ios-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-input-ios-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));font-size:inherit}.legacy-input.sc-ion-input-ios-h{--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:0}.item-label-stacked.sc-ion-input-ios-h,.item-label-stacked .sc-ion-input-ios-h,.item-label-floating.sc-ion-input-ios-h,.item-label-floating .sc-ion-input-ios-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0px}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.legacy-input.sc-ion-input-ios-h .native-input[disabled].sc-ion-input-ios,.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonInputIosStyle0 = inputIosCss;\n\nconst inputMdCss = \".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}.legacy-input.sc-ion-input-md-h{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;background:var(--background)}.legacy-input.sc-ion-input-md-h .native-input.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}ion-item.sc-ion-input-md-h:not(.item-label):not(.item-has-modern-input),ion-item:not(.item-label):not(.item-has-modern-input) .sc-ion-input-md-h{--padding-start:0}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.legacy-input.ion-color.sc-ion-input-md-h{color:var(--ion-color-base)}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.sc-ion-input-md-h:not(.legacy-input){min-height:44px}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h .cloned-input.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h .cloned-input.sc-ion-input-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-md .cloned-input.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-input-md:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-input-md:disabled{opacity:1}.legacy-input.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, #666666);visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.has-focus.legacy-input.sc-ion-input-md-h{pointer-events:none}.has-focus.legacy-input.sc-ion-input-md-h input.sc-ion-input-md,.has-focus.legacy-input.sc-ion-input-md-h a.sc-ion-input-md,.has-focus.legacy-input.sc-ion-input-md-h button.sc-ion-input-md{pointer-events:auto}.item-label-floating.item-has-placeholder.sc-ion-input-md-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-input-md-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-input-md-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-input-md-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl].input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.input-fill-solid.sc-ion-input-md-h:dir(rtl) .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, #404040)}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:2px;--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl].input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl] .input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.input-fill-outline.sc-ion-input-md-h:dir(rtl) .input-outline-start.sc-ion-input-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px;-ms-flex-positive:1;flex-grow:1}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl].input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl] .input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}@supports selector(:dir(rtl)){.input-fill-outline.sc-ion-input-md-h:dir(rtl) .input-outline-end.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));font-size:inherit}.legacy-input.sc-ion-input-md-h{--padding-top:10px;--padding-end:0;--padding-bottom:10px;--padding-start:8px}.item-label-stacked.sc-ion-input-md-h,.item-label-stacked .sc-ion-input-md-h,.item-label-floating.sc-ion-input-md-h,.item-label-floating .sc-ion-input-md-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.legacy-input.sc-ion-input-md-h .native-input[disabled].sc-ion-input-md,.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.input-highlight.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.input-highlight.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-md .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.input-highlight.sc-ion-input-md:dir(rtl){left:unset;right:unset;right:0}}}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}@supports (inset-inline-start: 0){.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h -no-combinator.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl].in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.in-item.sc-ion-input-md-h:dir(rtl) .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}}}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonInputMdStyle0 = inputMdCss;\n\nconst Input = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-input-${inputIds++}`;\n        this.inheritedAttributes = {};\n        this.isComposing = false;\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        /**\n         * `true` if the input was cleared as a result of the user typing\n         * with `clearOnEdit` enabled.\n         *\n         * Resets when the input loses focus.\n         */\n        this.didInputClearOnEdit = false;\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value || '';\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        this.onBlur = (ev) => {\n            this.hasFocus = false;\n            this.emitStyle();\n            if (this.focusedValue !== this.value) {\n                /**\n                 * Emits the `ionChange` event when the input value\n                 * is different than the value when the input was focused.\n                 */\n                this.emitValueChange(ev);\n            }\n            this.didInputClearOnEdit = false;\n            this.ionBlur.emit(ev);\n        };\n        this.onFocus = (ev) => {\n            this.hasFocus = true;\n            this.focusedValue = this.value;\n            this.emitStyle();\n            this.ionFocus.emit(ev);\n        };\n        this.onKeydown = (ev) => {\n            this.checkClearOnEdit(ev);\n        };\n        this.onCompositionStart = () => {\n            this.isComposing = true;\n        };\n        this.onCompositionEnd = () => {\n            this.isComposing = false;\n        };\n        this.clearTextInput = (ev) => {\n            if (this.clearInput && !this.readonly && !this.disabled && ev) {\n                ev.preventDefault();\n                ev.stopPropagation();\n                // Attempt to focus input again after pressing clear button\n                this.setFocus();\n            }\n            this.value = '';\n            this.emitInputChange(ev);\n        };\n        this.hasFocus = false;\n        this.color = undefined;\n        this.accept = undefined;\n        this.autocapitalize = 'off';\n        this.autocomplete = 'off';\n        this.autocorrect = 'off';\n        this.autofocus = false;\n        this.clearInput = false;\n        this.clearOnEdit = undefined;\n        this.counter = false;\n        this.counterFormatter = undefined;\n        this.debounce = undefined;\n        this.disabled = false;\n        this.enterkeyhint = undefined;\n        this.errorText = undefined;\n        this.fill = undefined;\n        this.inputmode = undefined;\n        this.helperText = undefined;\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.legacy = undefined;\n        this.max = undefined;\n        this.maxlength = undefined;\n        this.min = undefined;\n        this.minlength = undefined;\n        this.multiple = undefined;\n        this.name = this.inputId;\n        this.pattern = undefined;\n        this.placeholder = undefined;\n        this.readonly = false;\n        this.required = false;\n        this.shape = undefined;\n        this.spellcheck = false;\n        this.step = undefined;\n        this.size = undefined;\n        this.type = 'text';\n        this.value = '';\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    disabledChanged() {\n        this.emitStyle();\n    }\n    /**\n     * Update the item classes when the placeholder changes\n     */\n    placeholderChanged() {\n        this.emitStyle();\n    }\n    /**\n     * Update the native input element when the value changes\n     */\n    valueChanged() {\n        const nativeInput = this.nativeInput;\n        const value = this.getValue();\n        if (nativeInput && nativeInput.value !== value && !this.isComposing) {\n            /**\n             * Assigning the native input's value on attribute\n             * value change, allows `ionInput` implementations\n             * to override the control's value.\n             *\n             * Used for patterns such as input trimming (removing whitespace),\n             * or input masking.\n             */\n            nativeInput.value = value;\n        }\n        this.emitStyle();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['tabindex', 'title', 'data-form-type']));\n    }\n    connectedCallback() {\n        const { el } = this;\n        this.legacyFormController = createLegacyFormController(el);\n        this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.emitStyle();\n        this.debounceChanged();\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n                detail: this.el,\n            }));\n        }\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    disconnectedCallback() {\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n                detail: this.el,\n            }));\n        }\n        if (this.slotMutationController) {\n            this.slotMutationController.destroy();\n            this.slotMutationController = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Sets focus on the native `input` in `ion-input`. Use this method instead of the global\n     * `input.focus()`.\n     *\n     * Developers who wish to focus an input when a page enters\n     * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n     *\n     * Developers who wish to focus an input when an overlay is presented\n     * should call `setFocus` after `didPresent` has resolved.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<input>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        this.ionInput.emit({ value: newValue, event });\n    }\n    shouldClearOnEdit() {\n        const { type, clearOnEdit } = this;\n        return clearOnEdit === undefined ? type === 'password' : clearOnEdit;\n    }\n    getValue() {\n        return typeof this.value === 'number' ? this.value.toString() : (this.value || '').toString();\n    }\n    emitStyle() {\n        if (this.legacyFormController.hasLegacyControl()) {\n            this.ionStyle.emit({\n                interactive: true,\n                input: true,\n                'has-placeholder': this.placeholder !== undefined,\n                'has-value': this.hasValue(),\n                'has-focus': this.hasFocus,\n                'interactive-disabled': this.disabled,\n                // TODO(FW-2764): remove this\n                legacy: !!this.legacy,\n            });\n        }\n    }\n    checkClearOnEdit(ev) {\n        if (!this.shouldClearOnEdit()) {\n            return;\n        }\n        /**\n         * The following keys do not modify the\n         * contents of the input. As a result, pressing\n         * them should not edit the input.\n         *\n         * We can't check to see if the value of the input\n         * was changed because we call checkClearOnEdit\n         * in a keydown listener, and the key has not yet\n         * been added to the input.\n         */\n        const IGNORED_KEYS = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n        const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n        /**\n         * Clear the input if the control has not been previously cleared during focus.\n         * Do not clear if the user hitting enter to submit a form.\n         */\n        if (!this.didInputClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n            this.value = '';\n            this.emitInputChange(ev);\n        }\n        /**\n         * Pressing an IGNORED_KEYS first and\n         * then an allowed key will cause the input to not\n         * be cleared.\n         */\n        if (!pressedIgnoredKey) {\n            this.didInputClearOnEdit = true;\n        }\n    }\n    hasValue() {\n        return this.getValue().length > 0;\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText } = this;\n        return [h(\"div\", { class: \"helper-text\" }, helperText), h(\"div\", { class: \"error-text\" }, errorText)];\n    }\n    renderCounter() {\n        const { counter, maxlength, counterFormatter, value } = this;\n        if (counter !== true || maxlength === undefined) {\n            return;\n        }\n        return h(\"div\", { class: \"counter\" }, getCounterText(value, maxlength, counterFormatter));\n    }\n    /**\n     * Responsible for rendering helper text,\n     * error text, and counter. This element should only\n     * be rendered if hint text is set or counter is enabled.\n     */\n    renderBottomContent() {\n        const { counter, helperText, errorText, maxlength } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        const hasCounter = counter === true && maxlength !== undefined;\n        if (!hasHintText && !hasCounter) {\n            return;\n        }\n        return (h(\"div\", { class: \"input-bottom\" }, this.renderHintText(), this.renderCounter()));\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            } }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the input and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"input-outline-container\" }, h(\"div\", { class: \"input-outline-start\" }), h(\"div\", { class: {\n                        'input-outline-notch': true,\n                        'input-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"input-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    renderInput() {\n        const { disabled, fill, readonly, shape, inputId, labelPlacement, el, hasFocus } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        /**\n         * If the label is stacked, it should always sit above the input.\n         * For floating labels, the label should move above the input if\n         * the input has a value, is focused, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the input is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots));\n        return (h(Host, { class: createColorClasses(this.color, {\n                [mode]: true,\n                'has-value': hasValue,\n                'has-focus': hasFocus,\n                'label-floating': labelShouldFloat,\n                [`input-fill-${fill}`]: fill !== undefined,\n                [`input-shape-${shape}`]: shape !== undefined,\n                [`input-label-placement-${labelPlacement}`]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', this.el),\n                'input-disabled': disabled,\n            }) }, h(\"label\", { class: \"input-wrapper\", htmlFor: inputId }, this.renderLabelContainer(), h(\"div\", { class: \"native-wrapper\" }, h(\"slot\", { name: \"start\" }), h(\"input\", Object.assign({ class: \"native-input\", ref: (input) => (this.nativeInput = input), id: inputId, disabled: disabled, accept: this.accept, autoCapitalize: this.autocapitalize, autoComplete: this.autocomplete, autoCorrect: this.autocorrect, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, min: this.min, max: this.max, minLength: this.minlength, maxLength: this.maxlength, multiple: this.multiple, name: this.name, pattern: this.pattern, placeholder: this.placeholder || '', readOnly: readonly, required: this.required, spellcheck: this.spellcheck, step: this.step, size: this.size, type: this.type, value: value, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeydown, onCompositionstart: this.onCompositionStart, onCompositionend: this.onCompositionEnd }, this.inheritedAttributes)), this.clearInput && !readonly && !disabled && (h(\"button\", { \"aria-label\": \"reset\", type: \"button\", class: \"input-clear-icon\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the clear\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onFocusin: (ev) => {\n                /**\n                 * Prevent the focusin event from bubbling otherwise it will cause the focusin\n                 * event listener in scroll assist to fire. When this fires, focus will be moved\n                 * back to the input even if the clear button was never tapped. This poses issues\n                 * for screen readers as it means users would be unable to swipe past the clear button.\n                 */\n                ev.stopPropagation();\n            }, onClick: this.clearTextInput }, h(\"ion-icon\", { \"aria-hidden\": \"true\", icon: mode === 'ios' ? closeCircle : closeSharp }))), h(\"slot\", { name: \"end\" })), shouldRenderHighlight && h(\"div\", { class: \"input-highlight\" })), this.renderBottomContent()));\n    }\n    // TODO FW-2764 Remove this\n    renderLegacyInput() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-input now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-input label=\"Email\"></ion-input>\nExample with aria-label: <ion-input aria-label=\"Email\"></ion-input>\n\nFor inputs that do not render the label immediately next to the input, developers may continue to use \"ion-label\" but must manually associate the label with the input by using \"aria-labelledby\".\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-input is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new input syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const labelId = this.inputId + '-lbl';\n        const label = findItemLabel(this.el);\n        if (label) {\n            label.id = labelId;\n        }\n        return (h(Host, { \"aria-disabled\": this.disabled ? 'true' : null, class: createColorClasses(this.color, {\n                [mode]: true,\n                'has-value': this.hasValue(),\n                'has-focus': this.hasFocus,\n                'legacy-input': true,\n                'in-item-color': hostContext('ion-item.ion-color', this.el),\n            }) }, h(\"input\", Object.assign({ class: \"native-input\", ref: (input) => (this.nativeInput = input), \"aria-labelledby\": label ? label.id : null, disabled: this.disabled, accept: this.accept, autoCapitalize: this.autocapitalize, autoComplete: this.autocomplete, autoCorrect: this.autocorrect, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, min: this.min, max: this.max, minLength: this.minlength, maxLength: this.maxlength, multiple: this.multiple, name: this.name, pattern: this.pattern, placeholder: this.placeholder || '', readOnly: this.readonly, required: this.required, spellcheck: this.spellcheck, step: this.step, size: this.size, type: this.type, value: value, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeydown }, this.inheritedAttributes)), this.clearInput && !this.readonly && !this.disabled && (h(\"button\", { \"aria-label\": \"reset\", type: \"button\", class: \"input-clear-icon\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the clear\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: this.clearTextInput }, h(\"ion-icon\", { \"aria-hidden\": \"true\", icon: mode === 'ios' ? closeCircle : closeSharp })))));\n    }\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacyInput() : this.renderInput();\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"placeholder\": [\"placeholderChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet inputIds = 0;\nInput.style = {\n    ios: IonInputIosStyle0,\n    md: IonInputMdStyle0\n};\n\nexport { Input as ion_input };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AAC9H,SAASC,CAAC,IAAIC,0BAA0B,QAAQ,+BAA+B;AAC/E,SAASD,CAAC,IAAIE,qBAAqB,QAAQ,gCAAgC;AAC3E,SAASC,CAAC,IAAIC,aAAa,EAAEN,CAAC,IAAIO,qBAAqB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEP,CAAC,IAAIQ,gBAAgB,EAAEf,CAAC,IAAIgB,aAAa,QAAQ,uBAAuB;AACzJ,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASX,CAAC,IAAIY,4BAA4B,EAAEC,CAAC,IAAIC,cAAc,QAAQ,2BAA2B;AAClG,SAASrB,CAAC,IAAIsB,WAAW,EAAEf,CAAC,IAAIgB,kBAAkB,QAAQ,qBAAqB;AAC/E,SAASC,CAAC,IAAIC,WAAW,EAAE3B,CAAC,IAAI4B,UAAU,QAAQ,qBAAqB;AACvE,SAASF,CAAC,IAAIG,UAAU,QAAQ,4BAA4B;AAC5D,OAAO,qBAAqB;AAE5B,MAAMC,WAAW,GAAG,+wbAA+wb;AACnyb,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,4gyBAA4gyB;AAC/hyB,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBrC,gBAAgB,CAAC,IAAI,EAAEqC,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGpC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACqC,SAAS,GAAGrC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACsC,OAAO,GAAGtC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACuC,QAAQ,GAAGvC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACwC,QAAQ,GAAGxC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyC,OAAO,GAAG,aAAaC,QAAQ,EAAE,EAAE;IACxC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,KAAK,GAAGD,EAAE,CAACE,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAI,EAAE;MAClC;MACA,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACK,QAAQ,GAAIL,EAAE,IAAK;MACpB,IAAI,CAACM,eAAe,CAACN,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACO,MAAM,GAAIP,EAAE,IAAK;MAClB,IAAI,CAACQ,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,SAAS,CAAC,CAAC;MAChB,IAAI,IAAI,CAACC,YAAY,KAAK,IAAI,CAACP,KAAK,EAAE;QAClC;AAChB;AACA;AACA;QACgB,IAAI,CAACG,eAAe,CAACN,EAAE,CAAC;MAC5B;MACA,IAAI,CAACF,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACR,OAAO,CAACqB,IAAI,CAACX,EAAE,CAAC;IACzB,CAAC;IACD,IAAI,CAACY,OAAO,GAAIZ,EAAE,IAAK;MACnB,IAAI,CAACQ,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACP,KAAK;MAC9B,IAAI,CAACM,SAAS,CAAC,CAAC;MAChB,IAAI,CAAClB,QAAQ,CAACoB,IAAI,CAACX,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACa,SAAS,GAAIb,EAAE,IAAK;MACrB,IAAI,CAACc,gBAAgB,CAACd,EAAE,CAAC;IAC7B,CAAC;IACD,IAAI,CAACe,kBAAkB,GAAG,MAAM;MAC5B,IAAI,CAACnB,WAAW,GAAG,IAAI;IAC3B,CAAC;IACD,IAAI,CAACoB,gBAAgB,GAAG,MAAM;MAC1B,IAAI,CAACpB,WAAW,GAAG,KAAK;IAC5B,CAAC;IACD,IAAI,CAACqB,cAAc,GAAIjB,EAAE,IAAK;MAC1B,IAAI,IAAI,CAACkB,UAAU,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAIpB,EAAE,EAAE;QAC3DA,EAAE,CAACqB,cAAc,CAAC,CAAC;QACnBrB,EAAE,CAACsB,eAAe,CAAC,CAAC;QACpB;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAACpB,KAAK,GAAG,EAAE;MACf,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACQ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACgB,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,MAAM,GAAGD,SAAS;IACvB,IAAI,CAACE,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACZ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACa,WAAW,GAAGN,SAAS;IAC5B,IAAI,CAACO,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,gBAAgB,GAAGR,SAAS;IACjC,IAAI,CAACS,QAAQ,GAAGT,SAAS;IACzB,IAAI,CAACL,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,YAAY,GAAGV,SAAS;IAC7B,IAAI,CAACW,SAAS,GAAGX,SAAS;IAC1B,IAAI,CAACY,IAAI,GAAGZ,SAAS;IACrB,IAAI,CAACa,SAAS,GAAGb,SAAS;IAC1B,IAAI,CAACc,UAAU,GAAGd,SAAS;IAC3B,IAAI,CAACe,KAAK,GAAGf,SAAS;IACtB,IAAI,CAACgB,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,MAAM,GAAGjB,SAAS;IACvB,IAAI,CAACkB,GAAG,GAAGlB,SAAS;IACpB,IAAI,CAACmB,SAAS,GAAGnB,SAAS;IAC1B,IAAI,CAACoB,GAAG,GAAGpB,SAAS;IACpB,IAAI,CAACqB,SAAS,GAAGrB,SAAS;IAC1B,IAAI,CAACsB,QAAQ,GAAGtB,SAAS;IACzB,IAAI,CAACuB,IAAI,GAAG,IAAI,CAACvD,OAAO;IACxB,IAAI,CAACwD,OAAO,GAAGxB,SAAS;IACxB,IAAI,CAACyB,WAAW,GAAGzB,SAAS;IAC5B,IAAI,CAACN,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACgC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,KAAK,GAAG3B,SAAS;IACtB,IAAI,CAAC4B,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,IAAI,GAAG7B,SAAS;IACrB,IAAI,CAAC8B,IAAI,GAAG9B,SAAS;IACrB,IAAI,CAAC+B,IAAI,GAAG,MAAM;IAClB,IAAI,CAACrD,KAAK,GAAG,EAAE;EACnB;EACAsD,eAAeA,CAAA,EAAG;IACd,MAAM;MAAErE,QAAQ;MAAE8C,QAAQ;MAAEwB;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACtE,QAAQ,GAAG8C,QAAQ,KAAKT,SAAS,GAAGiC,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGtE,QAAQ,GAAGxB,aAAa,CAACwB,QAAQ,EAAE8C,QAAQ,CAAC;EACvK;EACAyB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAClD,SAAS,CAAC,CAAC;EACpB;EACA;AACJ;AACA;EACImD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACnD,SAAS,CAAC,CAAC;EACpB;EACA;AACJ;AACA;EACIoD,YAAYA,CAAA,EAAG;IACX,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAM3D,KAAK,GAAG,IAAI,CAAC4D,QAAQ,CAAC,CAAC;IAC7B,IAAID,WAAW,IAAIA,WAAW,CAAC3D,KAAK,KAAKA,KAAK,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;MACjE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYkE,WAAW,CAAC3D,KAAK,GAAGA,KAAK;IAC7B;IACA,IAAI,CAACM,SAAS,CAAC,CAAC;EACpB;EACAuD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACrE,mBAAmB,GAAGsE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErG,qBAAqB,CAAC,IAAI,CAACsG,EAAE,CAAC,CAAC,EAAEpG,iBAAiB,CAAC,IAAI,CAACoG,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC;EACpK;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAED;IAAG,CAAC,GAAG,IAAI;IACnB,IAAI,CAACE,oBAAoB,GAAG5G,0BAA0B,CAAC0G,EAAE,CAAC;IAC1D,IAAI,CAACG,sBAAsB,GAAGlG,4BAA4B,CAAC+F,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM5G,WAAW,CAAC,IAAI,CAAC,CAAC;IAClH,IAAI,CAACgH,eAAe,GAAG7G,qBAAqB,CAACyG,EAAE,EAAE,MAAM,IAAI,CAACK,aAAa,EAAE,MAAM,IAAI,CAACC,SAAS,CAAC;IAChG,IAAI,CAAChE,SAAS,CAAC,CAAC;IAChB,IAAI,CAACgD,eAAe,CAAC,CAAC;IACtB;MACIiB,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;QACtDC,MAAM,EAAE,IAAI,CAACV;MACjB,CAAC,CAAC,CAAC;IACP;EACJ;EACAW,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACpB,gBAAgB,GAAG,IAAI,CAACtE,QAAQ;EACzC;EACA2F,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACT,eAAe,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACAC,oBAAoBA,CAAA,EAAG;IACnB;MACIR,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;QACxDC,MAAM,EAAE,IAAI,CAACV;MACjB,CAAC,CAAC,CAAC;IACP;IACA,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACa,OAAO,CAAC,CAAC;MACrC,IAAI,CAACb,sBAAsB,GAAG7C,SAAS;IAC3C;IACA,IAAI,IAAI,CAAC8C,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACY,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACZ,eAAe,GAAG9C,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUF,QAAQA,CAAA,EAAG;IAAA,IAAA6D,KAAA;IAAA,OAAAC,iBAAA;MACb,IAAID,KAAI,CAACtB,WAAW,EAAE;QAClBsB,KAAI,CAACtB,WAAW,CAACwB,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,iBAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAACG,MAAI,CAAC1B,WAAW,EAAE;QACnB,MAAM,IAAI2B,OAAO,CAAEC,OAAO,IAAK1H,gBAAgB,CAACwH,MAAI,CAACrB,EAAE,EAAEuB,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAAC1B,WAAW,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIxD,eAAeA,CAACqF,KAAK,EAAE;IACnB,MAAM;MAAExF;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMyF,QAAQ,GAAGzF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAAC0F,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAACnF,YAAY,GAAGkF,QAAQ;IAC5B,IAAI,CAACvG,SAAS,CAACsB,IAAI,CAAC;MAAER,KAAK,EAAEyF,QAAQ;MAAED;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACIvF,eAAeA,CAACuF,KAAK,EAAE;IACnB,MAAM;MAAExF;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMyF,QAAQ,GAAGzF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAAC0F,QAAQ,CAAC,CAAC;IACzD,IAAI,CAACzG,QAAQ,CAACuB,IAAI,CAAC;MAAER,KAAK,EAAEyF,QAAQ;MAAED;IAAM,CAAC,CAAC;EAClD;EACAG,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEtC,IAAI;MAAEzB;IAAY,CAAC,GAAG,IAAI;IAClC,OAAOA,WAAW,KAAKN,SAAS,GAAG+B,IAAI,KAAK,UAAU,GAAGzB,WAAW;EACxE;EACAgC,QAAQA,CAAA,EAAG;IACP,OAAO,OAAO,IAAI,CAAC5D,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACA,KAAK,CAAC0F,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC1F,KAAK,IAAI,EAAE,EAAE0F,QAAQ,CAAC,CAAC;EACjG;EACApF,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC4D,oBAAoB,CAAC0B,gBAAgB,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACvG,QAAQ,CAACmB,IAAI,CAAC;QACfqF,WAAW,EAAE,IAAI;QACjB/F,KAAK,EAAE,IAAI;QACX,iBAAiB,EAAE,IAAI,CAACiD,WAAW,KAAKzB,SAAS;QACjD,WAAW,EAAE,IAAI,CAACwE,QAAQ,CAAC,CAAC;QAC5B,WAAW,EAAE,IAAI,CAACzF,QAAQ;QAC1B,sBAAsB,EAAE,IAAI,CAACY,QAAQ;QACrC;QACAsB,MAAM,EAAE,CAAC,CAAC,IAAI,CAACA;MACnB,CAAC,CAAC;IACN;EACJ;EACA5B,gBAAgBA,CAACd,EAAE,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC8F,iBAAiB,CAAC,CAAC,EAAE;MAC3B;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMI,YAAY,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IACxE,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,QAAQ,CAACpG,EAAE,CAACqG,GAAG,CAAC;IACvD;AACR;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACvG,mBAAmB,IAAI,IAAI,CAACmG,QAAQ,CAAC,CAAC,IAAI,CAACE,iBAAiB,EAAE;MACpE,IAAI,CAAChG,KAAK,GAAG,EAAE;MACf,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmG,iBAAiB,EAAE;MACpB,IAAI,CAACrG,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAmG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClC,QAAQ,CAAC,CAAC,CAACuC,MAAM,GAAG,CAAC;EACrC;EACA;AACJ;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEhE,UAAU;MAAEH;IAAU,CAAC,GAAG,IAAI;IACtC,OAAO,CAACnF,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;IAAc,CAAC,EAAEjE,UAAU,CAAC,EAAEtF,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;IAAa,CAAC,EAAEpE,SAAS,CAAC,CAAC;EACzG;EACAqE,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEzE,OAAO;MAAEY,SAAS;MAAEX,gBAAgB;MAAE9B;IAAM,CAAC,GAAG,IAAI;IAC5D,IAAI6B,OAAO,KAAK,IAAI,IAAIY,SAAS,KAAKnB,SAAS,EAAE;MAC7C;IACJ;IACA,OAAOxE,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;IAAU,CAAC,EAAElI,cAAc,CAAC6B,KAAK,EAAEyC,SAAS,EAAEX,gBAAgB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;EACIyE,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAE1E,OAAO;MAAEO,UAAU;MAAEH,SAAS;MAAEQ;IAAU,CAAC,GAAG,IAAI;IAC1D;AACR;AACA;AACA;IACQ,MAAM+D,WAAW,GAAG,CAAC,CAACpE,UAAU,IAAI,CAAC,CAACH,SAAS;IAC/C,MAAMwE,UAAU,GAAG5E,OAAO,KAAK,IAAI,IAAIY,SAAS,KAAKnB,SAAS;IAC9D,IAAI,CAACkF,WAAW,IAAI,CAACC,UAAU,EAAE;MAC7B;IACJ;IACA,OAAQ3J,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;IAAe,CAAC,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,EAAE,IAAI,CAACE,aAAa,CAAC,CAAC,CAAC;EAC5F;EACAI,WAAWA,CAAA,EAAG;IACV,MAAM;MAAErE;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQvF,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACM;MACvC;IAAE,CAAC,EAAEtE,KAAK,KAAKf,SAAS,GAAGxE,CAAC,CAAC,MAAM,EAAE;MAAE+F,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG/F,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;IAAa,CAAC,EAAEhE,KAAK,CAAC,CAAC;EAC3G;EACA;AACJ;AACA;AACA;EACI,IAAIiC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACN,EAAE,CAAC4C,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtE,KAAK,KAAKf,SAAS,IAAI,IAAI,CAACgD,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIuC,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,IAAI,GAAGrI,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMsI,cAAc,GAAGD,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC5E,IAAI,KAAK,SAAS;IAC/D,IAAI6E,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHjK,CAAC,CAAC,KAAK,EAAE;QAAEuJ,KAAK,EAAE;MAA0B,CAAC,EAAEvJ,CAAC,CAAC,KAAK,EAAE;QAAEuJ,KAAK,EAAE;MAAsB,CAAC,CAAC,EAAEvJ,CAAC,CAAC,KAAK,EAAE;QAAEuJ,KAAK,EAAE;UACrG,qBAAqB,EAAE,IAAI;UAC3B,4BAA4B,EAAE,CAAC,IAAI,CAACM;QACxC;MAAE,CAAC,EAAE7J,CAAC,CAAC,KAAK,EAAE;QAAEuJ,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEW,GAAG,EAAGhD,EAAE,IAAM,IAAI,CAACK,aAAa,GAAGL;MAAI,CAAC,EAAE,IAAI,CAAC3B,KAAK,CAAC,CAAC,EAAEvF,CAAC,CAAC,KAAK,EAAE;QAAEuJ,KAAK,EAAE;MAAoB,CAAC,CAAC,CAAC,EACnK,IAAI,CAACK,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACAO,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEhG,QAAQ;MAAEiB,IAAI;MAAElB,QAAQ;MAAEiC,KAAK;MAAE3D,OAAO;MAAEgD,cAAc;MAAE0B,EAAE;MAAE3D;IAAS,CAAC,GAAG,IAAI;IACvF,MAAMyG,IAAI,GAAGrI,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuB,KAAK,GAAG,IAAI,CAAC4D,QAAQ,CAAC,CAAC;IAC7B,MAAMsD,MAAM,GAAG9I,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC4F,EAAE,CAAC;IAC/C,MAAMmD,qBAAqB,GAAGL,IAAI,KAAK,IAAI,IAAI5E,IAAI,KAAK,SAAS,IAAI,CAACgF,MAAM;IAC5E,MAAMpB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMsB,gBAAgB,GAAGpD,EAAE,CAAC4C,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMS,gBAAgB,GAAG/E,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAKwD,QAAQ,IAAIzF,QAAQ,IAAI+G,gBAAgB,CAAE;IACtI,OAAQtK,CAAC,CAACE,IAAI,EAAE;MAAEqJ,KAAK,EAAEhI,kBAAkB,CAAC,IAAI,CAACgD,KAAK,EAAE;QAChD,CAACyF,IAAI,GAAG,IAAI;QACZ,WAAW,EAAEhB,QAAQ;QACrB,WAAW,EAAEzF,QAAQ;QACrB,gBAAgB,EAAEgH,gBAAgB;QAClC,CAAC,cAAcnF,IAAI,EAAE,GAAGA,IAAI,KAAKZ,SAAS;QAC1C,CAAC,eAAe2B,KAAK,EAAE,GAAGA,KAAK,KAAK3B,SAAS;QAC7C,CAAC,yBAAyBgB,cAAc,EAAE,GAAG,IAAI;QACjD,SAAS,EAAE4E,MAAM;QACjB,eAAe,EAAE9I,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC4F,EAAE,CAAC;QAC3D,gBAAgB,EAAE/C;MACtB,CAAC;IAAE,CAAC,EAAEnE,CAAC,CAAC,OAAO,EAAE;MAAEuJ,KAAK,EAAE,eAAe;MAAEiB,OAAO,EAAEhI;IAAQ,CAAC,EAAE,IAAI,CAACuH,oBAAoB,CAAC,CAAC,EAAE/J,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;IAAiB,CAAC,EAAEvJ,CAAC,CAAC,MAAM,EAAE;MAAE+F,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE/F,CAAC,CAAC,OAAO,EAAEgH,MAAM,CAACC,MAAM,CAAC;MAAEsC,KAAK,EAAE,cAAc;MAAEW,GAAG,EAAGlH,KAAK,IAAM,IAAI,CAAC6D,WAAW,GAAG7D,KAAM;MAAEyH,EAAE,EAAEjI,OAAO;MAAE2B,QAAQ,EAAEA,QAAQ;MAAEM,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEiG,cAAc,EAAE,IAAI,CAAChG,cAAc;MAAEiG,YAAY,EAAE,IAAI,CAAChG,YAAY;MAAEiG,WAAW,EAAE,IAAI,CAAChG,WAAW;MAAEiG,SAAS,EAAE,IAAI,CAAChG,SAAS;MAAEiG,YAAY,EAAE,IAAI,CAAC5F,YAAY;MAAE6F,SAAS,EAAE,IAAI,CAAC1F,SAAS;MAAEO,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEF,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEsF,SAAS,EAAE,IAAI,CAACnF,SAAS;MAAEoF,SAAS,EAAE,IAAI,CAACtF,SAAS;MAAEG,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAEiF,QAAQ,EAAEhH,QAAQ;MAAEgC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEE,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAErD,KAAK,EAAEA,KAAK;MAAEJ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEK,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEwH,SAAS,EAAE,IAAI,CAACvH,SAAS;MAAEwH,kBAAkB,EAAE,IAAI,CAACtH,kBAAkB;MAAEuH,gBAAgB,EAAE,IAAI,CAACtH;IAAiB,CAAC,EAAE,IAAI,CAACrB,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAACuB,UAAU,IAAI,CAACC,QAAQ,IAAI,CAACC,QAAQ,IAAKnE,CAAC,CAAC,QAAQ,EAAE;MAAE,YAAY,EAAE,OAAO;MAAEuG,IAAI,EAAE,QAAQ;MAAEgD,KAAK,EAAE,kBAAkB;MAAE+B,aAAa,EAAGvI,EAAE,IAAK;QACjrC;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACqB,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEmH,SAAS,EAAGxI,EAAE,IAAK;QAClB;AAChB;AACA;AACA;AACA;AACA;QACgBA,EAAE,CAACsB,eAAe,CAAC,CAAC;MACxB,CAAC;MAAEmH,OAAO,EAAE,IAAI,CAACxH;IAAe,CAAC,EAAEhE,CAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEyL,IAAI,EAAEzB,IAAI,KAAK,KAAK,GAAGvI,WAAW,GAAGC;IAAW,CAAC,CAAC,CAAE,EAAE1B,CAAC,CAAC,MAAM,EAAE;MAAE+F,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAEsE,qBAAqB,IAAIrK,CAAC,CAAC,KAAK,EAAE;MAAEuJ,KAAK,EAAE;IAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACE,mBAAmB,CAAC,CAAC,CAAC;EAClQ;EACA;EACAiC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC9I,2BAA2B,EAAE;MACnC1B,eAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACgG,EAAE,CAAC;MAC9M,IAAI,IAAI,CAACzB,MAAM,EAAE;QACbvE,eAAe,CAAC;AAChC;AACA,qHAAqH,EAAE,IAAI,CAACgG,EAAE,CAAC;MACnH;MACA,IAAI,CAACtE,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAMoH,IAAI,GAAGrI,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuB,KAAK,GAAG,IAAI,CAAC4D,QAAQ,CAAC,CAAC;IAC7B,MAAM6E,OAAO,GAAG,IAAI,CAACnJ,OAAO,GAAG,MAAM;IACrC,MAAM+C,KAAK,GAAGvE,aAAa,CAAC,IAAI,CAACkG,EAAE,CAAC;IACpC,IAAI3B,KAAK,EAAE;MACPA,KAAK,CAACkF,EAAE,GAAGkB,OAAO;IACtB;IACA,OAAQ3L,CAAC,CAACE,IAAI,EAAE;MAAE,eAAe,EAAE,IAAI,CAACiE,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEoF,KAAK,EAAEhI,kBAAkB,CAAC,IAAI,CAACgD,KAAK,EAAE;QAChG,CAACyF,IAAI,GAAG,IAAI;QACZ,WAAW,EAAE,IAAI,CAAChB,QAAQ,CAAC,CAAC;QAC5B,WAAW,EAAE,IAAI,CAACzF,QAAQ;QAC1B,cAAc,EAAE,IAAI;QACpB,eAAe,EAAEjC,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC4F,EAAE;MAC9D,CAAC;IAAE,CAAC,EAAElH,CAAC,CAAC,OAAO,EAAEgH,MAAM,CAACC,MAAM,CAAC;MAAEsC,KAAK,EAAE,cAAc;MAAEW,GAAG,EAAGlH,KAAK,IAAM,IAAI,CAAC6D,WAAW,GAAG7D,KAAM;MAAE,iBAAiB,EAAEuC,KAAK,GAAGA,KAAK,CAACkF,EAAE,GAAG,IAAI;MAAEtG,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEM,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEiG,cAAc,EAAE,IAAI,CAAChG,cAAc;MAAEiG,YAAY,EAAE,IAAI,CAAChG,YAAY;MAAEiG,WAAW,EAAE,IAAI,CAAChG,WAAW;MAAEiG,SAAS,EAAE,IAAI,CAAChG,SAAS;MAAEiG,YAAY,EAAE,IAAI,CAAC5F,YAAY;MAAE6F,SAAS,EAAE,IAAI,CAAC1F,SAAS;MAAEO,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEF,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEsF,SAAS,EAAE,IAAI,CAACnF,SAAS;MAAEoF,SAAS,EAAE,IAAI,CAACtF,SAAS;MAAEG,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAEiF,QAAQ,EAAE,IAAI,CAAChH,QAAQ;MAAEgC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEE,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAErD,KAAK,EAAEA,KAAK;MAAEJ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEK,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEwH,SAAS,EAAE,IAAI,CAACvH;IAAU,CAAC,EAAE,IAAI,CAAClB,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAACuB,UAAU,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAKnE,CAAC,CAAC,QAAQ,EAAE;MAAE,YAAY,EAAE,OAAO;MAAEuG,IAAI,EAAE,QAAQ;MAAEgD,KAAK,EAAE,kBAAkB;MAAE+B,aAAa,EAAGvI,EAAE,IAAK;QACp/B;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACqB,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEoH,OAAO,EAAE,IAAI,CAACxH;IAAe,CAAC,EAAEhE,CAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEyL,IAAI,EAAEzB,IAAI,KAAK,KAAK,GAAGvI,WAAW,GAAGC;IAAW,CAAC,CAAC,CAAE,CAAC;EACvI;EACAkK,MAAMA,CAAA,EAAG;IACL,MAAM;MAAExE;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAAC0B,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC4C,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACvB,WAAW,CAAC,CAAC;EAClG;EACA,IAAIjD,EAAEA,CAAA,EAAG;IAAE,OAAO9G,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWyL,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,aAAa,EAAE,CAAC,oBAAoB,CAAC;MACrC,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIpJ,QAAQ,GAAG,CAAC;AAChBT,KAAK,CAAC8J,KAAK,GAAG;EACVC,GAAG,EAAElK,iBAAiB;EACtBmK,EAAE,EAAEjK;AACR,CAAC;AAED,SAASC,KAAK,IAAIiK,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}