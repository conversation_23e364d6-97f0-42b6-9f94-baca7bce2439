{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { environment } from 'src/environments/environment';\nlet SuggestedProductsComponent = class SuggestedProductsComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.products = [];\n    this.isLoading = true;\n    this.error = null;\n    this.currentPage = 1;\n    this.totalPages = 1;\n    this.hasMore = false;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.loadSuggestedProducts();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadSuggestedProducts(page = 1) {\n    this.isLoading = true;\n    this.error = null;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/products/suggested?page=${page}&limit=12`).subscribe({\n      next: response => {\n        if (response.success) {\n          if (page === 1) {\n            this.products = response.products;\n          } else {\n            this.products = [...this.products, ...response.products];\n          }\n          this.currentPage = response.pagination.page;\n          this.totalPages = response.pagination.pages;\n          this.hasMore = this.currentPage < this.totalPages;\n        } else {\n          this.loadFallbackProducts();\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading suggested products:', error);\n        if (page === 1) {\n          this.loadFallbackProducts();\n        }\n        this.error = 'Failed to load suggested products';\n        this.isLoading = false;\n      }\n    }));\n  }\n  loadFallbackProducts() {\n    this.products = [{\n      _id: '1',\n      name: 'Vintage Denim Jacket',\n      price: 89.99,\n      originalPrice: 129.99,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400',\n        isPrimary: true\n      }],\n      vendor: {\n        _id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',\n        isInfluencer: true\n      },\n      analytics: {\n        views: 15420,\n        likes: 892,\n        shares: 156,\n        purchases: 234\n      },\n      rating: {\n        average: 4.2,\n        count: 156\n      }\n    }, {\n      _id: '2',\n      name: 'Silk Slip Dress',\n      price: 159.99,\n      originalPrice: 199.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400',\n        isPrimary: true\n      }],\n      vendor: {\n        _id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Chen',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        isInfluencer: true\n      },\n      analytics: {\n        views: 12890,\n        likes: 1205,\n        shares: 289,\n        purchases: 167\n      },\n      rating: {\n        average: 4.5,\n        count: 89\n      }\n    }, {\n      _id: '3',\n      name: 'Chunky Knit Sweater',\n      price: 79.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400',\n        isPrimary: true\n      }],\n      vendor: {\n        _id: '3',\n        username: 'beauty_by_sarah',\n        fullName: 'Sarah Johnson',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        isInfluencer: true\n      },\n      analytics: {\n        views: 8945,\n        likes: 567,\n        shares: 89,\n        purchases: 123\n      },\n      rating: {\n        average: 4.7,\n        count: 67\n      }\n    }];\n  }\n  loadMore() {\n    if (this.hasMore && !this.isLoading) {\n      this.loadSuggestedProducts(this.currentPage + 1);\n    }\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  viewVendor(vendor) {\n    this.router.navigate(['/vendor', vendor.username]);\n  }\n  addToCart(product, event) {\n    event.stopPropagation();\n    // TODO: Implement add to cart functionality\n    console.log('Add to cart:', product);\n  }\n  toggleWishlist(product, event) {\n    event.stopPropagation();\n    // TODO: Implement wishlist functionality\n    console.log('Toggle wishlist:', product);\n  }\n  shareProduct(product, event) {\n    event.stopPropagation();\n    // TODO: Implement share functionality\n    console.log('Share product:', product);\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return product.discount || 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  retry() {\n    this.loadSuggestedProducts(1);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n};\nSuggestedProductsComponent = __decorate([Component({\n  selector: 'app-suggested-products',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './suggested-products.component.html',\n  styleUrls: ['./suggested-products.component.scss']\n})], SuggestedProductsComponent);\nexport { SuggestedProductsComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "environment", "SuggestedProductsComponent", "constructor", "router", "http", "products", "isLoading", "error", "currentPage", "totalPages", "hasMore", "subscriptions", "ngOnInit", "loadSuggestedProducts", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "page", "push", "get", "apiUrl", "subscribe", "next", "response", "success", "pagination", "pages", "loadFallbackProducts", "console", "_id", "name", "price", "originalPrice", "discount", "images", "url", "isPrimary", "vendor", "username", "fullName", "avatar", "isInfluencer", "analytics", "views", "likes", "shares", "purchases", "rating", "average", "count", "loadMore", "viewProduct", "product", "navigate", "viewVendor", "addToCart", "event", "stopPropagation", "log", "toggleWishlist", "shareProduct", "getDiscountPercentage", "Math", "round", "formatPrice", "Intl", "NumberFormat", "style", "currency", "format", "formatNumber", "num", "toFixed", "toString", "retry", "trackByProductId", "index", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\suggested-products\\suggested-products.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\n\ninterface Product {\n  _id: string;\n  name: string;\n  price: number;\n  originalPrice?: number;\n  discount?: number;\n  images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;\n  vendor: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n    isInfluencer: boolean;\n  };\n  createdBy?: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n    isInfluencer: boolean;\n  };\n  analytics: {\n    views: number;\n    likes: number;\n    shares: number;\n    purchases: number;\n  };\n  rating?: {\n    average: number;\n    count: number;\n  };\n}\n\n@Component({\n  selector: 'app-suggested-products',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './suggested-products.component.html',\n  styleUrls: ['./suggested-products.component.scss']\n})\nexport class SuggestedProductsComponent implements OnInit, OnDestroy {\n  products: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  currentPage = 1;\n  totalPages = 1;\n  hasMore = false;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    public router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  loadSuggestedProducts(page: number = 1) {\n    this.isLoading = true;\n    this.error = null;\n\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/products/suggested?page=${page}&limit=12`).subscribe({\n        next: (response) => {\n          if (response.success) {\n            if (page === 1) {\n              this.products = response.products;\n            } else {\n              this.products = [...this.products, ...response.products];\n            }\n            \n            this.currentPage = response.pagination.page;\n            this.totalPages = response.pagination.pages;\n            this.hasMore = this.currentPage < this.totalPages;\n          } else {\n            this.loadFallbackProducts();\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading suggested products:', error);\n          if (page === 1) {\n            this.loadFallbackProducts();\n          }\n          this.error = 'Failed to load suggested products';\n          this.isLoading = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackProducts() {\n    this.products = [\n      {\n        _id: '1',\n        name: 'Vintage Denim Jacket',\n        price: 89.99,\n        originalPrice: 129.99,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400', isPrimary: true }],\n        vendor: {\n          _id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',\n          isInfluencer: true\n        },\n        analytics: { views: 15420, likes: 892, shares: 156, purchases: 234 },\n        rating: { average: 4.2, count: 156 }\n      },\n      {\n        _id: '2',\n        name: 'Silk Slip Dress',\n        price: 159.99,\n        originalPrice: 199.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400', isPrimary: true }],\n        vendor: {\n          _id: '2',\n          username: 'style_guru_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n          isInfluencer: true\n        },\n        analytics: { views: 12890, likes: 1205, shares: 289, purchases: 167 },\n        rating: { average: 4.5, count: 89 }\n      },\n      {\n        _id: '3',\n        name: 'Chunky Knit Sweater',\n        price: 79.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400', isPrimary: true }],\n        vendor: {\n          _id: '3',\n          username: 'beauty_by_sarah',\n          fullName: 'Sarah Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n          isInfluencer: true\n        },\n        analytics: { views: 8945, likes: 567, shares: 89, purchases: 123 },\n        rating: { average: 4.7, count: 67 }\n      }\n    ];\n  }\n\n  loadMore() {\n    if (this.hasMore && !this.isLoading) {\n      this.loadSuggestedProducts(this.currentPage + 1);\n    }\n  }\n\n  viewProduct(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  viewVendor(vendor: any) {\n    this.router.navigate(['/vendor', vendor.username]);\n  }\n\n  addToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement add to cart functionality\n    console.log('Add to cart:', product);\n  }\n\n  toggleWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement wishlist functionality\n    console.log('Toggle wishlist:', product);\n  }\n\n  shareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Implement share functionality\n    console.log('Share product:', product);\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return product.discount || 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  retry() {\n    this.loadSuggestedProducts(1);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAI9C,SAASC,WAAW,QAAQ,8BAA8B;AA0CnD,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAUrCC,YACSC,MAAc,EACbC,IAAgB;IADjB,KAAAD,MAAM,GAANA,MAAM;IACL,KAAAC,IAAI,GAAJA,IAAI;IAXd,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,OAAO,GAAG,KAAK;IAEP,KAAAC,aAAa,GAAmB,EAAE;EAKvC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,aAAa,CAACI,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEAJ,qBAAqBA,CAACK,IAAA,GAAe,CAAC;IACpC,IAAI,CAACZ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACI,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAM,GAAGpB,WAAW,CAACqB,MAAM,4BAA4BH,IAAI,WAAW,CAAC,CAACI,SAAS,CAAC;MAC7FC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAIP,IAAI,KAAK,CAAC,EAAE;YACd,IAAI,CAACb,QAAQ,GAAGmB,QAAQ,CAACnB,QAAQ;WAClC,MAAM;YACL,IAAI,CAACA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,GAAGmB,QAAQ,CAACnB,QAAQ,CAAC;;UAG1D,IAAI,CAACG,WAAW,GAAGgB,QAAQ,CAACE,UAAU,CAACR,IAAI;UAC3C,IAAI,CAACT,UAAU,GAAGe,QAAQ,CAACE,UAAU,CAACC,KAAK;UAC3C,IAAI,CAACjB,OAAO,GAAG,IAAI,CAACF,WAAW,GAAG,IAAI,CAACC,UAAU;SAClD,MAAM;UACL,IAAI,CAACmB,oBAAoB,EAAE;;QAE7B,IAAI,CAACtB,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfsB,OAAO,CAACtB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAIW,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAACU,oBAAoB,EAAE;;QAE7B,IAAI,CAACrB,KAAK,GAAG,mCAAmC;QAChD,IAAI,CAACD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC,CACH;EACH;EAEAsB,oBAAoBA,CAAA;IAClB,IAAI,CAACvB,QAAQ,GAAG,CACd;MACEyB,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,sBAAsB;MAC5BC,KAAK,EAAE,KAAK;MACZC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,iEAAiE;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MACrGC,MAAM,EAAE;QACNR,GAAG,EAAE,GAAG;QACRS,QAAQ,EAAE,kBAAkB;QAC5BC,QAAQ,EAAE,gBAAgB;QAC1BC,MAAM,EAAE,oEAAoE;QAC5EC,YAAY,EAAE;OACf;MACDC,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,GAAG;QAAEC,MAAM,EAAE,GAAG;QAAEC,SAAS,EAAE;MAAG,CAAE;MACpEC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG;KACnC,EACD;MACEpB,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,MAAM;MACbC,aAAa,EAAE,MAAM;MACrBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MACxGC,MAAM,EAAE;QACNR,GAAG,EAAE,GAAG;QACRS,QAAQ,EAAE,iBAAiB;QAC3BC,QAAQ,EAAE,WAAW;QACrBC,MAAM,EAAE,oEAAoE;QAC5EC,YAAY,EAAE;OACf;MACDC,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,SAAS,EAAE;MAAG,CAAE;MACrEC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE;KAClC,EACD;MACEpB,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,qBAAqB;MAC3BC,KAAK,EAAE,KAAK;MACZG,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MACxGC,MAAM,EAAE;QACNR,GAAG,EAAE,GAAG;QACRS,QAAQ,EAAE,iBAAiB;QAC3BC,QAAQ,EAAE,eAAe;QACzBC,MAAM,EAAE,oEAAoE;QAC5EC,YAAY,EAAE;OACf;MACDC,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,GAAG;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAClEC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE;KAClC,CACF;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzC,OAAO,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACnC,IAAI,CAACO,qBAAqB,CAAC,IAAI,CAACL,WAAW,GAAG,CAAC,CAAC;;EAEpD;EAEA4C,WAAWA,CAACC,OAAgB;IAC1B,IAAI,CAAClD,MAAM,CAACmD,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACvB,GAAG,CAAC,CAAC;EACjD;EAEAyB,UAAUA,CAACjB,MAAW;IACpB,IAAI,CAACnC,MAAM,CAACmD,QAAQ,CAAC,CAAC,SAAS,EAAEhB,MAAM,CAACC,QAAQ,CAAC,CAAC;EACpD;EAEAiB,SAASA,CAACH,OAAgB,EAAEI,KAAY;IACtCA,KAAK,CAACC,eAAe,EAAE;IACvB;IACA7B,OAAO,CAAC8B,GAAG,CAAC,cAAc,EAAEN,OAAO,CAAC;EACtC;EAEAO,cAAcA,CAACP,OAAgB,EAAEI,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB;IACA7B,OAAO,CAAC8B,GAAG,CAAC,kBAAkB,EAAEN,OAAO,CAAC;EAC1C;EAEAQ,YAAYA,CAACR,OAAgB,EAAEI,KAAY;IACzCA,KAAK,CAACC,eAAe,EAAE;IACvB;IACA7B,OAAO,CAAC8B,GAAG,CAAC,gBAAgB,EAAEN,OAAO,CAAC;EACxC;EAEAS,qBAAqBA,CAACT,OAAgB;IACpC,IAAIA,OAAO,CAACpB,aAAa,IAAIoB,OAAO,CAACpB,aAAa,GAAGoB,OAAO,CAACrB,KAAK,EAAE;MAClE,OAAO+B,IAAI,CAACC,KAAK,CAAE,CAACX,OAAO,CAACpB,aAAa,GAAGoB,OAAO,CAACrB,KAAK,IAAIqB,OAAO,CAACpB,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAOoB,OAAO,CAACnB,QAAQ,IAAI,CAAC;EAC9B;EAEA+B,WAAWA,CAACjC,KAAa;IACvB,OAAO,IAAIkC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAC;EAClB;EAEAuC,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAAC9D,qBAAqB,CAAC,CAAC,CAAC;EAC/B;EAEA+D,gBAAgBA,CAACC,KAAa,EAAExB,OAAgB;IAC9C,OAAOA,OAAO,CAACvB,GAAG;EACpB;CACD;AA7KY7B,0BAA0B,GAAA6E,UAAA,EAPtChF,SAAS,CAAC;EACTiF,QAAQ,EAAE,wBAAwB;EAClCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAClF,YAAY,CAAC;EACvBmF,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,CAAC,qCAAqC;CAClD,CAAC,C,EACWlF,0BAA0B,CA6KtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}