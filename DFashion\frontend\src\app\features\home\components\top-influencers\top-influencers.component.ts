import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';

interface Influencer {
  _id: string;
  username: string;
  fullName: string;
  avatar: string;
  bio: string;
  isInfluencer: boolean;
  socialStats: {
    followersCount: number;
    followingCount: number;
    postsCount: number;
  };
  influencerStats: {
    category: string;
    engagementRate: number;
    averageLikes: number;
    averageViews: number;
    verifiedAt: Date;
  };
}

@Component({
  selector: 'app-top-influencers',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './top-influencers.component.html',
  styleUrls: ['./top-influencers.component.scss']
})
export class TopInfluencersComponent implements OnInit, OnDestroy {
  influencers: Influencer[] = [];
  isLoading = true;
  error: string | null = null;
  currentPage = 1;
  totalPages = 1;
  hasMore = false;
  selectedCategory = '';

  categories = [
    { value: '', label: 'All Categories' },
    { value: 'fashion', label: 'Fashion' },
    { value: 'beauty', label: 'Beauty' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'fitness', label: 'Fitness' },
    { value: 'travel', label: 'Travel' }
  ];

  private subscriptions: Subscription[] = [];

  constructor(
    public router: Router,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.loadTopInfluencers();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadTopInfluencers(page: number = 1, category: string = '') {
    this.isLoading = true;
    this.error = null;

    const params = new URLSearchParams({
      page: page.toString(),
      limit: '12'
    });

    if (category) {
      params.append('category', category);
    }

    this.subscriptions.push(
      this.http.get<any>(`${environment.apiUrl}/users/influencers?${params.toString()}`).subscribe({
        next: (response) => {
          if (response.success) {
            if (page === 1) {
              this.influencers = response.influencers;
            } else {
              this.influencers = [...this.influencers, ...response.influencers];
            }
            
            this.currentPage = response.pagination.page;
            this.totalPages = response.pagination.pages;
            this.hasMore = this.currentPage < this.totalPages;
          } else {
            this.loadFallbackInfluencers();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading top influencers:', error);
          if (page === 1) {
            this.loadFallbackInfluencers();
          }
          this.error = 'Failed to load top influencers';
          this.isLoading = false;
        }
      })
    );
  }

  loadFallbackInfluencers() {
    this.influencers = [
      {
        _id: '1',
        username: 'fashionista_maya',
        fullName: 'Maya Rodriguez',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',
        bio: '✨ Fashion & Lifestyle Influencer | 📍 NYC | Shop my looks ⬇️',
        isInfluencer: true,
        socialStats: { followersCount: 125000, followingCount: 890, postsCount: 342 },
        influencerStats: {
          category: 'fashion',
          engagementRate: 8.5,
          averageLikes: 10500,
          averageViews: 45000,
          verifiedAt: new Date()
        }
      },
      {
        _id: '2',
        username: 'style_guru_alex',
        fullName: 'Alex Chen',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        bio: '👗 Style Curator | 🌟 Sustainable Fashion Advocate | DM for collabs',
        isInfluencer: true,
        socialStats: { followersCount: 89000, followingCount: 1200, postsCount: 278 },
        influencerStats: {
          category: 'fashion',
          engagementRate: 12.3,
          averageLikes: 8900,
          averageViews: 32000,
          verifiedAt: new Date()
        }
      },
      {
        _id: '3',
        username: 'beauty_by_sarah',
        fullName: 'Sarah Johnson',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        bio: '💄 Beauty & Fashion | 📱 Daily OOTD | 🛍️ Affordable luxury finds',
        isInfluencer: true,
        socialStats: { followersCount: 156000, followingCount: 567, postsCount: 445 },
        influencerStats: {
          category: 'beauty',
          engagementRate: 9.8,
          averageLikes: 15300,
          averageViews: 58000,
          verifiedAt: new Date()
        }
      }
    ];
  }

  onCategoryChange(category: string) {
    this.selectedCategory = category;
    this.loadTopInfluencers(1, category);
  }

  loadMore() {
    if (this.hasMore && !this.isLoading) {
      this.loadTopInfluencers(this.currentPage + 1, this.selectedCategory);
    }
  }

  viewInfluencer(influencer: Influencer) {
    this.router.navigate(['/influencer', influencer.username]);
  }

  followInfluencer(influencer: Influencer, event: Event) {
    event.stopPropagation();
    // TODO: Implement follow functionality
    console.log('Follow influencer:', influencer);
  }

  shareInfluencer(influencer: Influencer, event: Event) {
    event.stopPropagation();
    // TODO: Implement share functionality
    console.log('Share influencer:', influencer);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  getCategoryIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'fashion': 'fas fa-tshirt',
      'beauty': 'fas fa-palette',
      'lifestyle': 'fas fa-heart',
      'fitness': 'fas fa-dumbbell',
      'travel': 'fas fa-plane',
      'food': 'fas fa-utensils',
      'tech': 'fas fa-laptop'
    };
    return icons[category] || 'fas fa-star';
  }

  getCategoryColor(category: string): string {
    const colors: { [key: string]: string } = {
      'fashion': '#667eea',
      'beauty': '#f093fb',
      'lifestyle': '#f6ad55',
      'fitness': '#48bb78',
      'travel': '#38b2ac',
      'food': '#ed8936',
      'tech': '#4299e1'
    };
    return colors[category] || '#a0aec0';
  }

  retry() {
    this.loadTopInfluencers(1, this.selectedCategory);
  }

  trackByInfluencerId(index: number, influencer: Influencer): string {
    return influencer._id;
  }
}
