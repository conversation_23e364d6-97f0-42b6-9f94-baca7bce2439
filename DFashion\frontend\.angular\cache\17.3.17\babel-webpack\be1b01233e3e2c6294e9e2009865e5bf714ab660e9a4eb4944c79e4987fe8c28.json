{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/story.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/media.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"storiesSlider\"];\nfunction StoriesComponent_div_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_2_button_1_Template_button_click_0_listener() {\n      const i_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.goToSlide(i_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r3.currentSlide);\n  }\n}\nfunction StoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, StoriesComponent_div_2_button_1_Template, 1, 2, \"button\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dots);\n  }\n}\nfunction StoriesComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_12_Template_div_click_0_listener() {\n      const storyGroup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openStoryViewer(storyGroup_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 19)(3, \"img\", 20);\n    i0.ɵɵlistener(\"error\", function StoriesComponent_div_12_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onImageError($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const storyGroup_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-story\", storyGroup_r6.stories.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.getSafeImageUrl(storyGroup_r6.user.avatar), i0.ɵɵsanitizeUrl)(\"alt\", storyGroup_r6.user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(storyGroup_r6.user.username);\n  }\n}\nfunction StoriesComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"div\", 22);\n    i0.ɵɵelementStart(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Auto-sliding\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideLeft());\n    });\n    i0.ɵɵelement(1, \"i\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideLeft);\n  }\n}\nfunction StoriesComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideRight());\n    });\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideRight);\n  }\n}\nexport class StoriesComponent {\n  constructor(storyService, authService, router, mediaService) {\n    this.storyService = storyService;\n    this.authService = authService;\n    this.router = router;\n    this.mediaService = mediaService;\n    this.storyGroups = [];\n    this.currentUser = null;\n    // Slider properties\n    this.translateX = 0;\n    this.currentSlide = 0;\n    this.isTransitioning = false;\n    // Navigation properties\n    this.canSlideLeft = false;\n    this.canSlideRight = false;\n    this.showArrows = false;\n    this.showDots = false;\n    this.dots = [];\n    // Touch properties\n    this.touchStartX = 0;\n    this.touchCurrentX = 0;\n    this.isDragging = false;\n    // Responsive properties\n    this.slidesPerView = 1;\n    this.slideWidth = 0;\n    this.autoSlideDelay = 3000; // 3 seconds\n    this.isAutoSliding = true;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.loadStories();\n  }\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n  loadStories() {\n    this.storyService.getStories().subscribe({\n      next: response => {\n        this.storyGroups = response.storyGroups;\n        // Update slider after stories load\n        setTimeout(() => {\n          this.calculateResponsiveSettings();\n          this.updateSliderState();\n          this.startAutoSlide();\n        }, 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.storyGroups = [];\n      }\n    });\n  }\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Reset to beginning\n      this.currentSlide = 0;\n      this.updateSliderPosition();\n    }\n  }\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  goToSlide(slideIndex) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n  getMaxSlide() {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n  // Touch gesture methods\n  onTouchStart(event) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n    this.isDragging = false;\n  }\n  // Image handling methods\n  getSafeImageUrl(url) {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n  onImageError(event) {\n    this.mediaService.handleImageError(event, 'user');\n  }\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n  showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n    // Add global function for file handling\n    window.handleFileSelect = input => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n  showFilePreview(file) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ? `<video src=\"${fileURL}\" controls autoplay muted></video>` : `<img src=\"${fileURL}\" alt=\"Story preview\">`}\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n  openStoryViewer(storyGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Navigate to story viewer with user ID and start from first story\n      this.router.navigate(['/story', storyGroup.user._id, 0]);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n  static {\n    this.ɵfac = function StoriesComponent_Factory(t) {\n      return new (t || StoriesComponent)(i0.ɵɵdirectiveInject(i1.StoryService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesComponent,\n      selectors: [[\"app-stories\"]],\n      viewQuery: function StoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 9,\n      consts: [[\"storiesSlider\", \"\"], [1, \"stories-section\"], [1, \"stories-slider-wrapper\"], [\"class\", \"nav-dots\", 4, \"ngIf\"], [1, \"stories-slider\", 3, \"touchstart\", \"touchmove\", \"touchend\", \"scroll\", \"mouseenter\", \"mouseleave\"], [1, \"stories-track\"], [1, \"story-slide\", \"add-story\", 3, \"click\"], [1, \"story-avatar\"], [1, \"avatar-ring\", \"add-ring\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-slide\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"auto-slide-indicator\", 4, \"ngIf\"], [\"class\", \"nav-arrow nav-left\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"nav-arrow nav-right\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"nav-dots\"], [\"class\", \"nav-dot\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-dot\", 3, \"click\"], [1, \"story-slide\", 3, \"click\"], [1, \"avatar-ring\"], [1, \"avatar-image\", 3, \"error\", \"src\", \"alt\"], [1, \"auto-slide-indicator\"], [1, \"slide-progress\"], [1, \"slide-text\"], [1, \"nav-arrow\", \"nav-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"nav-arrow\", \"nav-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function StoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtemplate(2, StoriesComponent_div_2_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4, 0);\n          i0.ɵɵlistener(\"touchstart\", function StoriesComponent_Template_div_touchstart_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchStart($event));\n          })(\"touchmove\", function StoriesComponent_Template_div_touchmove_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchMove($event));\n          })(\"touchend\", function StoriesComponent_Template_div_touchend_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchEnd($event));\n          })(\"scroll\", function StoriesComponent_Template_div_scroll_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onScroll());\n          })(\"mouseenter\", function StoriesComponent_Template_div_mouseenter_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function StoriesComponent_Template_div_mouseleave_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function StoriesComponent_Template_div_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openAddStory());\n          });\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"span\", 10);\n          i0.ɵɵtext(11, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, StoriesComponent_div_12_Template, 6, 5, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, StoriesComponent_div_13_Template, 4, 0, \"div\", 12)(14, StoriesComponent_button_14_Template, 2, 1, \"button\", 13)(15, StoriesComponent_button_15_Template, 2, 1, \"button\", 14);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDots);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.translateX + \"px)\")(\"transition\", ctx.isTransitioning ? \"transform 0.3s ease-out\" : \"none\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.storyGroups);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isAutoSliding && ctx.storyGroups.length > ctx.slidesPerView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf],\n      styles: [\".stories-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  width: 100%;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0;\\n  will-change: transform;\\n  padding: 16px 0;\\n}\\n\\n.story-slide[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 0 12px;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 80px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:first-child {\\n  padding-left: 16px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:last-child {\\n  padding-right: 16px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  position: relative;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  padding: 3px;\\n  background: #fafafa;\\n  border: 1px solid #dbdbdb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.avatar-ring.has-story[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  border: none;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border: 2px dashed #dbdbdb;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  background: rgba(0, 149, 246, 0.05);\\n}\\n\\n.avatar-image[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #fff;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 20px;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n.auto-slide-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  z-index: 5;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  animation: _ngcontent-%COMP%_fadeInSlide 0.3s ease;\\n}\\n\\n.slide-progress[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: var(--primary-color, #007bff);\\n  animation: _ngcontent-%COMP%_slideProgress 3s linear infinite;\\n}\\n\\n.slide-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInSlide {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideProgress {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.2);\\n    opacity: 0.7;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.95);\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\\n  color: #262626;\\n  font-size: 16px;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #fff;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n  transform: translateY(-50%);\\n}\\n\\n.nav-arrow.nav-left[_ngcontent-%COMP%] {\\n  left: 12px;\\n}\\n\\n.nav-arrow.nav-right[_ngcontent-%COMP%] {\\n  right: 12px;\\n}\\n\\n\\n\\n.nav-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 12px 0 8px;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #dbdbdb;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.nav-dot.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  transform: scale(1.2);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  opacity: 0.7;\\n}\\n\\n\\n\\n@media (min-width: 1024px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    padding: 0 15px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 72px;\\n    height: 72px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    max-width: 80px;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 1023px) and (min-width: 768px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 80px;\\n    padding: 0 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n    font-size: 14px;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 767px) and (min-width: 481px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 14px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    padding: 0 10px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n}\\n\\n\\n@media (max-width: 480px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 65px;\\n    padding: 0 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 12px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 52px;\\n    height: 52px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 55px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n    padding: 8px 0 6px;\\n  }\\n  .nav-dot[_ngcontent-%COMP%] {\\n    width: 6px;\\n    height: 6px;\\n  }\\n}\\n\\n\\n@media (max-width: 360px) {\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    padding: 0 6px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 8px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 50px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "StoriesComponent_div_2_button_1_Template_button_click_0_listener", "i_r3", "ɵɵrestoreView", "_r2", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "goToSlide", "ɵɵelementEnd", "ɵɵclassProp", "currentSlide", "ɵɵtemplate", "StoriesComponent_div_2_button_1_Template", "ɵɵadvance", "ɵɵproperty", "dots", "StoriesComponent_div_12_Template_div_click_0_listener", "storyGroup_r6", "_r5", "$implicit", "openStoryViewer", "StoriesComponent_div_12_Template_img_error_3_listener", "$event", "onImageError", "ɵɵtext", "stories", "length", "getSafeImageUrl", "user", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "ɵɵelement", "StoriesComponent_button_14_Template_button_click_0_listener", "_r7", "slideLeft", "canSlideLeft", "StoriesComponent_button_15_Template_button_click_0_listener", "_r8", "slideRight", "canSlideRight", "StoriesComponent", "constructor", "storyService", "authService", "router", "mediaService", "storyGroups", "currentUser", "translateX", "isTransitioning", "showArrows", "showDots", "touchStartX", "touchCurrentX", "isDragging", "<PERSON><PERSON><PERSON><PERSON>iew", "slideWidth", "autoSlideDelay", "isAutoSliding", "ngOnInit", "currentUser$", "subscribe", "loadStories", "ngAfterViewInit", "setTimeout", "calculateResponsiveSettings", "updateSliderState", "startAutoSlide", "window", "addEventListener", "ngOnDestroy", "stopAutoSlide", "storiesSlider", "containerWidth", "nativeElement", "clientWidth", "screenWidth", "innerWidth", "Math", "floor", "max", "totalSlides", "totalPages", "ceil", "Array", "fill", "map", "_", "i", "getStories", "next", "response", "error", "console", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "pauseAutoSlide", "resumeAutoSlide", "maxSlide", "getMaxSlide", "updateSliderPosition", "min", "slideIndex", "onScroll", "onTouchStart", "event", "touches", "clientX", "onTouchMove", "deltaX", "abs", "preventDefault", "onTouchEnd", "threshold", "url", "handleImageError", "openAddStory", "showStoryCreationModal", "modalOverlay", "document", "createElement", "className", "innerHTML", "styles", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "handleFileSelect", "input", "files", "file", "log", "name", "type", "showFilePreview", "remove", "previewModal", "fileURL", "URL", "createObjectURL", "isVideo", "startsWith", "previewStyles", "storyGroup", "navigate", "_id", "onMouseEnter", "onMouseLeave", "ɵɵdirectiveInject", "i1", "StoryService", "i2", "AuthService", "i3", "Router", "i4", "MediaService", "selectors", "viewQuery", "StoriesComponent_Query", "rf", "ctx", "StoriesComponent_div_2_Template", "StoriesComponent_Template_div_touchstart_3_listener", "_r1", "StoriesComponent_Template_div_touchmove_3_listener", "StoriesComponent_Template_div_touchend_3_listener", "StoriesComponent_Template_div_scroll_3_listener", "StoriesComponent_Template_div_mouseenter_3_listener", "StoriesComponent_Template_div_mouseleave_3_listener", "StoriesComponent_Template_div_click_6_listener", "StoriesComponent_div_12_Template", "StoriesComponent_div_13_Template", "StoriesComponent_button_14_Template", "StoriesComponent_button_15_Template", "ɵɵstyleProp", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { MediaService } from '../../../../core/services/media.service';\n\nimport { StoryService } from '../../../../core/services/story.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { StoryGroup, Story } from '../../../../core/models/story.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-stories',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './stories.component.html',\n  styleUrls: ['./stories.component.scss']\n\n\n})\nexport class StoriesComponent implements OnInit, AfterViewInit {\n  @ViewChild('storiesSlider') storiesSlider!: ElementRef<HTMLDivElement>;\n\n  storyGroups: StoryGroup[] = [];\n  currentUser: User | null = null;\n\n  // Slider properties\n  translateX = 0;\n  currentSlide = 0;\n  isTransitioning = false;\n\n  // Navigation properties\n  canSlideLeft = false;\n  canSlideRight = false;\n  showArrows = false;\n  showDots = false;\n  dots: number[] = [];\n\n  // Touch properties\n  touchStartX = 0;\n  touchCurrentX = 0;\n  isDragging = false;\n\n  // Responsive properties\n  slidesPerView = 1;\n  slideWidth = 0;\n\n  // Auto-slide properties\n  autoSlideInterval: any;\n  autoSlideDelay = 3000; // 3 seconds\n  isAutoSliding = true;\n\n  constructor(\n    private storyService: StoryService,\n    private authService: AuthService,\n    private router: Router,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    this.loadStories();\n  }\n\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n\n  loadStories() {\n    this.storyService.getStories().subscribe({\n      next: (response) => {\n        this.storyGroups = response.storyGroups;\n        // Update slider after stories load\n        setTimeout(() => {\n          this.calculateResponsiveSettings();\n          this.updateSliderState();\n          this.startAutoSlide();\n        }, 100);\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        this.storyGroups = [];\n      }\n    });\n  }\n\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Reset to beginning\n      this.currentSlide = 0;\n      this.updateSliderPosition();\n    }\n  }\n\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  goToSlide(slideIndex: number) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n\n  getMaxSlide(): number {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n\n  // Touch gesture methods\n  onTouchStart(event: TouchEvent) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n\n    this.isDragging = false;\n  }\n\n  // Image handling methods\n  getSafeImageUrl(url: string | undefined): string {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n\n  onImageError(event: Event): void {\n    this.mediaService.handleImageError(event, 'user');\n  }\n\n\n\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n\n  private showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n\n    // Add global function for file handling\n    (window as any).handleFileSelect = (input: HTMLInputElement) => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n\n  private showFilePreview(file: File) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ?\n            `<video src=\"${fileURL}\" controls autoplay muted></video>` :\n            `<img src=\"${fileURL}\" alt=\"Story preview\">`\n          }\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n\n  openStoryViewer(storyGroup: StoryGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Navigate to story viewer with user ID and start from first story\n      this.router.navigate(['/story', storyGroup.user._id, 0]);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n}\n", "<div class=\"stories-section\">\n  <div class=\"stories-slider-wrapper\">\n    <!-- Navigation Dots (Mobile) -->\n    <div class=\"nav-dots\" *ngIf=\"showDots\">\n      <button \n        *ngFor=\"let dot of dots; let i = index\"\n        class=\"nav-dot\"\n        [class.active]=\"i === currentSlide\"\n        (click)=\"goToSlide(i)\">\n      </button>\n    </div>\n\n    <!-- Stories Slider Container -->\n    <div class=\"stories-slider\"\n         #storiesSlider\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\"\n         (scroll)=\"onScroll()\"\n         (mouseenter)=\"onMouseEnter()\"\n         (mouseleave)=\"onMouseLeave()\">\n      \n      <!-- Stories Track -->\n      <div class=\"stories-track\" \n           [style.transform]=\"'translateX(' + translateX + 'px)'\"\n           [style.transition]=\"isTransitioning ? 'transform 0.3s ease-out' : 'none'\">\n        \n        <!-- Add Story -->\n        <div class=\"story-slide add-story\" (click)=\"openAddStory()\">\n          <div class=\"story-avatar\">\n            <div class=\"avatar-ring add-ring\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n          </div>\n          <span class=\"story-username\">Your Story</span>\n        </div>\n\n        <!-- User Stories -->\n        <div \n          *ngFor=\"let storyGroup of storyGroups; let i = index\"\n          class=\"story-slide\"\n          (click)=\"openStoryViewer(storyGroup)\"\n        >\n          <div class=\"story-avatar\">\n            <div class=\"avatar-ring\" [class.has-story]=\"storyGroup.stories.length > 0\">\n              <img [src]=\"getSafeImageUrl(storyGroup.user.avatar)\"\n                   [alt]=\"storyGroup.user.fullName\"\n                   (error)=\"onImageError($event)\"\n                   class=\"avatar-image\">\n            </div>\n          </div>\n          <span class=\"story-username\">{{ storyGroup.user.username }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Auto-slide Indicator -->\n    <div class=\"auto-slide-indicator\" *ngIf=\"isAutoSliding && storyGroups.length > slidesPerView\">\n      <div class=\"slide-progress\"></div>\n      <span class=\"slide-text\">Auto-sliding</span>\n    </div>\n\n    <!-- Navigation Arrows (Desktop/Tablet) -->\n    <button class=\"nav-arrow nav-left\"\n            (click)=\"slideLeft()\"\n            [disabled]=\"!canSlideLeft\"\n            *ngIf=\"showArrows\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n\n    <button class=\"nav-arrow nav-right\"\n            (click)=\"slideRight()\"\n            [disabled]=\"!canSlideRight\"\n            *ngIf=\"showArrows\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;;;;;ICGxCC,EAAA,CAAAC,cAAA,iBAIyB;IAAvBD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,IAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAP,IAAA,CAAY;IAAA,EAAC;IACxBJ,EAAA,CAAAY,YAAA,EAAS;;;;;IAFPZ,EAAA,CAAAa,WAAA,WAAAT,IAAA,KAAAI,MAAA,CAAAM,YAAA,CAAmC;;;;;IAJvCd,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAe,UAAA,IAAAC,wCAAA,qBAIyB;IAE3BhB,EAAA,CAAAY,YAAA,EAAM;;;;IALcZ,EAAA,CAAAiB,SAAA,EAAS;IAATjB,EAAA,CAAAkB,UAAA,YAAAV,MAAA,CAAAW,IAAA,CAAS;;;;;;IAiCzBnB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAkB,sDAAA;MAAA,MAAAC,aAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAf,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgB,eAAA,CAAAH,aAAA,CAA2B;IAAA,EAAC;IAIjCrB,EAFJ,CAAAC,cAAA,aAA0B,cACmD,cAI/C;IADrBD,EAAA,CAAAE,UAAA,mBAAAuB,sDAAAC,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAiB,GAAA;MAAA,MAAAd,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmB,YAAA,CAAAD,MAAA,CAAoB;IAAA,EAAC;IAGvC1B,EALI,CAAAY,YAAA,EAG0B,EACtB,EACF;IACNZ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAA4B,MAAA,GAA8B;IAC7D5B,EAD6D,CAAAY,YAAA,EAAO,EAC9D;;;;;IARuBZ,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAa,WAAA,cAAAQ,aAAA,CAAAQ,OAAA,CAAAC,MAAA,KAAiD;IACnE9B,EAAA,CAAAiB,SAAA,EAA+C;IAC/CjB,EADA,CAAAkB,UAAA,QAAAV,MAAA,CAAAuB,eAAA,CAAAV,aAAA,CAAAW,IAAA,CAAAC,MAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAA+C,QAAAb,aAAA,CAAAW,IAAA,CAAAG,QAAA,CACf;IAKZnC,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAoC,iBAAA,CAAAf,aAAA,CAAAW,IAAA,CAAAK,QAAA,CAA8B;;;;;IAMjErC,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAsC,SAAA,cAAkC;IAClCtC,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAA4B,MAAA,mBAAY;IACvC5B,EADuC,CAAAY,YAAA,EAAO,EACxC;;;;;;IAGNZ,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAqC,4DAAA;MAAAvC,EAAA,CAAAK,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,SAAA,EAAW;IAAA,EAAC;IAG3BzC,EAAA,CAAAsC,SAAA,YAAmC;IACrCtC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAkC,YAAA,CAA0B;;;;;;IAKlC1C,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAyC,4DAAA;MAAA3C,EAAA,CAAAK,aAAA,CAAAuC,GAAA;MAAA,MAAApC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqC,UAAA,EAAY;IAAA,EAAC;IAG5B7C,EAAA,CAAAsC,SAAA,YAAoC;IACtCtC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAsC,aAAA,CAA2B;;;ADrDvC,OAAM,MAAOC,gBAAgB;EAgC3BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAH1B,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAjCtB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAzC,YAAY,GAAG,CAAC;IAChB,KAAA0C,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAd,YAAY,GAAG,KAAK;IACpB,KAAAI,aAAa,GAAG,KAAK;IACrB,KAAAW,UAAU,GAAG,KAAK;IAClB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAvC,IAAI,GAAa,EAAE;IAEnB;IACA,KAAAwC,WAAW,GAAG,CAAC;IACf,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,CAAC;IAId,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;EAOjB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAAChB,WAAW,CAACiB,YAAY,CAACC,SAAS,CAACpC,IAAI,IAAG;MAC7C,IAAI,CAACsB,WAAW,GAAGtB,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACqC,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;IAEP;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACJ,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAN,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACO,aAAa,EAAE;IAEzB,MAAMC,cAAc,GAAG,IAAI,CAACD,aAAa,CAACE,aAAa,CAACC,WAAW;IACnE,MAAMC,WAAW,GAAGR,MAAM,CAACS,UAAU;IAErC;IACA,IAAID,WAAW,IAAI,IAAI,EAAE;MACvB,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAIyB,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAIyB,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM,IAAIyB,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM;MACL,IAAI,CAACI,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;;IAGtB;IACA,IAAI,CAACI,aAAa,GAAGuB,IAAI,CAACE,GAAG,CAAC,IAAI,CAACzB,aAAa,EAAE,CAAC,CAAC;IAEpD;IACA,IAAI,IAAI,CAACJ,QAAQ,EAAE;MACjB,MAAM8B,WAAW,GAAG,IAAI,CAACnC,WAAW,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC;MACjD,MAAM2D,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAACF,WAAW,GAAG,IAAI,CAAC1B,aAAa,CAAC;MAC9D,IAAI,CAAC3C,IAAI,GAAGwE,KAAK,CAACF,UAAU,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;;EAE1D;EAEA1B,WAAWA,CAAA;IACT,IAAI,CAACpB,YAAY,CAAC+C,UAAU,EAAE,CAAC5B,SAAS,CAAC;MACvC6B,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC7C,WAAW,GAAG6C,QAAQ,CAAC7C,WAAW;QACvC;QACAkB,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,2BAA2B,EAAE;UAClC,IAAI,CAACC,iBAAiB,EAAE;UACxB,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDyB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAC9C,WAAW,GAAG,EAAE;MACvB;KACD,CAAC;EACJ;EAEA;EACAqB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACrB,WAAW,CAACvB,MAAM,IAAI,IAAI,CAACgC,aAAa,EAAE,OAAO,CAAC;IAE3D,IAAI,CAACgB,aAAa,EAAE,CAAC,CAAC;IACtB,IAAI,CAACuB,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAACrC,aAAa,EAAE;QACtB,IAAI,CAACsC,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACvC,cAAc,CAAC;EACzB;EAEAc,aAAaA,CAAA;IACX,IAAI,IAAI,CAACuB,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAACxC,aAAa,GAAG,KAAK;EAC5B;EAEAyC,eAAeA,CAAA;IACb,IAAI,CAACzC,aAAa,GAAG,IAAI;EAC3B;EAEAsC,aAAaA,CAAA;IACX,MAAMI,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,IAAI,CAAC9F,YAAY,GAAG6F,QAAQ,EAAE;MAChC,IAAI,CAAC9D,UAAU,EAAE;KAClB,MAAM;MACL;MACA,IAAI,CAAC/B,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC+F,oBAAoB,EAAE;;EAE/B;EAEA;EACApE,SAASA,CAAA;IACP,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAAC+D,cAAc,EAAE;MACrB,IAAI,CAAC3F,YAAY,GAAGuE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzE,YAAY,GAAG,CAAC,CAAC;MACtD,IAAI,CAAC+F,oBAAoB,EAAE;MAC3B;MACAtC,UAAU,CAAC,MAAM,IAAI,CAACmC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEA7D,UAAUA,CAAA;IACR,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAAC2D,cAAc,EAAE;MACrB,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAAC9F,YAAY,GAAGuE,IAAI,CAACyB,GAAG,CAACH,QAAQ,EAAE,IAAI,CAAC7F,YAAY,GAAG,CAAC,CAAC;MAC7D,IAAI,CAAC+F,oBAAoB,EAAE;MAC3B;MACAtC,UAAU,CAAC,MAAM,IAAI,CAACmC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEA/F,SAASA,CAACoG,UAAkB;IAC1B,IAAI,CAACN,cAAc,EAAE;IACrB,IAAI,CAAC3F,YAAY,GAAGiG,UAAU;IAC9B,IAAI,CAACF,oBAAoB,EAAE;IAC3B;IACAtC,UAAU,CAAC,MAAM,IAAI,CAACmC,eAAe,EAAE,EAAE,IAAI,CAAC;EAChD;EAEAG,oBAAoBA,CAAA;IAClB,IAAI,CAACrD,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACD,UAAU,GAAG,CAAC,IAAI,CAACzC,YAAY,GAAG,IAAI,CAACiD,UAAU,GAAG,IAAI,CAACD,aAAa;IAC3E,IAAI,CAACW,iBAAiB,EAAE;IAExB;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAACf,eAAe,GAAG,KAAK;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAiB,iBAAiBA,CAAA;IACf,MAAMkC,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,CAAClE,YAAY,GAAG,IAAI,CAAC5B,YAAY,GAAG,CAAC;IACzC,IAAI,CAACgC,aAAa,GAAG,IAAI,CAAChC,YAAY,GAAG6F,QAAQ;EACnD;EAEAC,WAAWA,CAAA;IACT,MAAMpB,WAAW,GAAG,IAAI,CAACnC,WAAW,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC;IACjD,OAAOuD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACK,IAAI,CAACF,WAAW,GAAG,IAAI,CAAC1B,aAAa,CAAC,GAAG,CAAC,CAAC;EACrE;EAEAkD,QAAQA,CAAA;IACN;IACA,IAAI,CAACvC,iBAAiB,EAAE;EAC1B;EAEA;EACAwC,YAAYA,CAACC,KAAiB;IAC5B,IAAI,CAACvD,WAAW,GAAGuD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC3C,IAAI,CAACxD,aAAa,GAAG,IAAI,CAACD,WAAW;IACrC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC4C,cAAc,EAAE;EACvB;EAEAY,WAAWA,CAACH,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACrD,UAAU,EAAE;IAEtB,IAAI,CAACD,aAAa,GAAGsD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC7C,MAAME,MAAM,GAAG,IAAI,CAAC1D,aAAa,GAAG,IAAI,CAACD,WAAW;IAEpD;IACA,IAAI0B,IAAI,CAACkC,GAAG,CAACD,MAAM,CAAC,GAAG,EAAE,EAAE;MACzBJ,KAAK,CAACM,cAAc,EAAE;;EAE1B;EAEAC,UAAUA,CAACP,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAACrD,UAAU,EAAE;IAEtB,MAAMyD,MAAM,GAAG,IAAI,CAAC1D,aAAa,GAAG,IAAI,CAACD,WAAW;IACpD,MAAM+D,SAAS,GAAG,EAAE,CAAC,CAAC;IAEtB,IAAIrC,IAAI,CAACkC,GAAG,CAACD,MAAM,CAAC,GAAGI,SAAS,EAAE;MAChC,IAAIJ,MAAM,GAAG,CAAC,EAAE;QACd;QACA,IAAI,CAAC7E,SAAS,EAAE;OACjB,MAAM;QACL;QACA,IAAI,CAACI,UAAU,EAAE;;KAEpB,MAAM;MACL;MACA0B,UAAU,CAAC,MAAM,IAAI,CAACmC,eAAe,EAAE,EAAE,IAAI,CAAC;;IAGhD,IAAI,CAAC7C,UAAU,GAAG,KAAK;EACzB;EAEA;EACA9B,eAAeA,CAAC4F,GAAuB;IACrC,OAAO,IAAI,CAACvE,YAAY,CAACrB,eAAe,CAAC4F,GAAG,EAAE,MAAM,CAAC;EACvD;EAEAhG,YAAYA,CAACuF,KAAY;IACvB,IAAI,CAAC9D,YAAY,CAACwE,gBAAgB,CAACV,KAAK,EAAE,MAAM,CAAC;EACnD;EAIAW,YAAYA,CAAA;IACV,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,sBAAsB;IAC/CH,YAAY,CAACI,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2DxB;IAED;IACA,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC9CG,MAAM,CAACC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqLpB;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACjCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACCpD,MAAc,CAAC8D,gBAAgB,GAAIC,KAAuB,IAAI;MAC7D,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACzB,IAAIA,KAAK,IAAIA,KAAK,CAAC7G,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAM8G,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;QACrBvC,OAAO,CAACyC,GAAG,CAAC,gBAAgB,EAAED,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACG,IAAI,CAAC;QAEnD;QACA,IAAI,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC1Bb,YAAY,CAACkB,MAAM,EAAE;;IAEzB,CAAC;EACH;EAEQD,eAAeA,CAACJ,IAAU;IAChC,MAAMM,YAAY,GAAGlB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDiB,YAAY,CAAChB,SAAS,GAAG,qBAAqB;IAE9C,MAAMiB,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAC;IACzC,MAAMU,OAAO,GAAGV,IAAI,CAACG,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC;IAE9CL,YAAY,CAACf,SAAS,GAAG;;;;;;;;;;YAUjBmB,OAAO,GACP,eAAeH,OAAO,oCAAoC,GAC1D,aAAaA,OAAO,wBACtB;;;;;;;;;;;;;;KAcL;IAED;IACA,MAAMK,aAAa,GAAGxB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACrDuB,aAAa,CAACnB,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8E3B;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACiB,aAAa,CAAC;IACxCxB,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACW,YAAY,CAAC;EACzC;EAEA1H,eAAeA,CAACiI,UAAsB;IACpC,IAAIA,UAAU,CAAC5H,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACjC;MACA,IAAI,CAACqB,MAAM,CAACuG,QAAQ,CAAC,CAAC,QAAQ,EAAED,UAAU,CAACzH,IAAI,CAAC2H,GAAG,EAAE,CAAC,CAAC,CAAC;KACzD,MAAM;MACLvD,OAAO,CAACyC,GAAG,CAAC,2BAA2B,EAAEY,UAAU,CAACzH,IAAI,CAACK,QAAQ,CAAC;;EAEtE;EAEA;EACAuH,YAAYA,CAAA;IACV,IAAI,CAACnD,cAAc,EAAE;EACvB;EAEAoD,YAAYA,CAAA;IACV,IAAI,CAACnD,eAAe,EAAE;EACxB;;;uBA9qBW3D,gBAAgB,EAAA/C,EAAA,CAAA8J,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAhK,EAAA,CAAA8J,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlK,EAAA,CAAA8J,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAApK,EAAA,CAAA8J,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAhBvH,gBAAgB;MAAAwH,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UClB3B1K,EADF,CAAAC,cAAA,aAA6B,aACS;UAElCD,EAAA,CAAAe,UAAA,IAAA6J,+BAAA,iBAAuC;UAUvC5K,EAAA,CAAAC,cAAA,gBAOmC;UAA9BD,EALA,CAAAE,UAAA,wBAAA2K,oDAAAnJ,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAyK,GAAA;YAAA,OAAA9K,EAAA,CAAAU,WAAA,CAAciK,GAAA,CAAA1D,YAAA,CAAAvF,MAAA,CAAoB;UAAA,EAAC,uBAAAqJ,mDAAArJ,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAyK,GAAA;YAAA,OAAA9K,EAAA,CAAAU,WAAA,CACtBiK,GAAA,CAAAtD,WAAA,CAAA3F,MAAA,CAAmB;UAAA,EAAC,sBAAAsJ,kDAAAtJ,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAyK,GAAA;YAAA,OAAA9K,EAAA,CAAAU,WAAA,CACrBiK,GAAA,CAAAlD,UAAA,CAAA/F,MAAA,CAAkB;UAAA,EAAC,oBAAAuJ,gDAAA;YAAAjL,EAAA,CAAAK,aAAA,CAAAyK,GAAA;YAAA,OAAA9K,EAAA,CAAAU,WAAA,CACrBiK,GAAA,CAAA3D,QAAA,EAAU;UAAA,EAAC,wBAAAkE,oDAAA;YAAAlL,EAAA,CAAAK,aAAA,CAAAyK,GAAA;YAAA,OAAA9K,EAAA,CAAAU,WAAA,CACPiK,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC,wBAAAuB,oDAAA;YAAAnL,EAAA,CAAAK,aAAA,CAAAyK,GAAA;YAAA,OAAA9K,EAAA,CAAAU,WAAA,CACfiK,GAAA,CAAAd,YAAA,EAAc;UAAA,EAAC;UAQ9B7J,EALF,CAAAC,cAAA,aAE+E,aAGjB;UAAzBD,EAAA,CAAAE,UAAA,mBAAAkL,+CAAA;YAAApL,EAAA,CAAAK,aAAA,CAAAyK,GAAA;YAAA,OAAA9K,EAAA,CAAAU,WAAA,CAASiK,GAAA,CAAA9C,YAAA,EAAc;UAAA,EAAC;UAEvD7H,EADF,CAAAC,cAAA,aAA0B,aACU;UAChCD,EAAA,CAAAsC,SAAA,WAA2B;UAE/BtC,EADE,CAAAY,YAAA,EAAM,EACF;UACNZ,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAA4B,MAAA,kBAAU;UACzC5B,EADyC,CAAAY,YAAA,EAAO,EAC1C;UAGNZ,EAAA,CAAAe,UAAA,KAAAsK,gCAAA,kBAIC;UAYLrL,EADE,CAAAY,YAAA,EAAM,EACF;UAgBNZ,EAbA,CAAAe,UAAA,KAAAuK,gCAAA,kBAA8F,KAAAC,mCAAA,qBASnE,KAAAC,mCAAA,qBAOA;UAI/BxL,EADE,CAAAY,YAAA,EAAM,EACF;;;UA1EqBZ,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAyJ,GAAA,CAAAjH,QAAA,CAAc;UAqB9B1D,EAAA,CAAAiB,SAAA,GAAsD;UACtDjB,EADA,CAAAyL,WAAA,8BAAAd,GAAA,CAAApH,UAAA,SAAsD,eAAAoH,GAAA,CAAAnH,eAAA,sCACmB;UAcnDxD,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,YAAAyJ,GAAA,CAAAtH,WAAA,CAAgB;UAkBVrD,EAAA,CAAAiB,SAAA,EAAyD;UAAzDjB,EAAA,CAAAkB,UAAA,SAAAyJ,GAAA,CAAA1G,aAAA,IAAA0G,GAAA,CAAAtH,WAAA,CAAAvB,MAAA,GAAA6I,GAAA,CAAA7G,aAAA,CAAyD;UASnF9D,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAyJ,GAAA,CAAAlH,UAAA,CAAgB;UAOhBzD,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAyJ,GAAA,CAAAlH,UAAA,CAAgB;;;qBD5DjB1D,YAAY,EAAA2L,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAxD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}