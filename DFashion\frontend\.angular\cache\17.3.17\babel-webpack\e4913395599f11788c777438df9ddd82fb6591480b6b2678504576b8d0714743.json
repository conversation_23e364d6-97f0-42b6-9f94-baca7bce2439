{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class CurrencyFormatPipe {\n  transform(value, currency = 'INR', locale = 'en-IN') {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n    try {\n      return new Intl.NumberFormat(locale, {\n        style: 'currency',\n        currency: currency,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 2\n      }).format(value);\n    } catch (error) {\n      // Fallback for unsupported locales\n      return `₹${value.toLocaleString()}`;\n    }\n  }\n  static {\n    this.ɵfac = function CurrencyFormatPipe_Factory(t) {\n      return new (t || CurrencyFormatPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"currencyFormat\",\n      type: CurrencyFormatPipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["CurrencyFormatPipe", "transform", "value", "currency", "locale", "undefined", "isNaN", "Intl", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "format", "error", "toLocaleString", "pure"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\pipes\\currency-format.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n\n@Pipe({\n  name: 'currencyFormat'\n})\nexport class CurrencyFormatPipe implements PipeTransform {\n\n  transform(value: number, currency: string = 'INR', locale: string = 'en-IN'): string {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    try {\n      return new Intl.NumberFormat(locale, {\n        style: 'currency',\n        currency: currency,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 2\n      }).format(value);\n    } catch (error) {\n      // Fallback for unsupported locales\n      return `₹${value.toLocaleString()}`;\n    }\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,kBAAkB;EAE7BC,SAASA,CAACC,KAAa,EAAEC,QAAA,GAAmB,KAAK,EAAEC,MAAA,GAAiB,OAAO;IACzE,IAAIF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKG,SAAS,IAAIC,KAAK,CAACJ,KAAK,CAAC,EAAE;MACzD,OAAO,IAAI;;IAGb,IAAI;MACF,OAAO,IAAIK,IAAI,CAACC,YAAY,CAACJ,MAAM,EAAE;QACnCK,KAAK,EAAE,UAAU;QACjBN,QAAQ,EAAEA,QAAQ;QAClBO,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;OACxB,CAAC,CAACC,MAAM,CAACV,KAAK,CAAC;KACjB,CAAC,OAAOW,KAAK,EAAE;MACd;MACA,OAAO,IAAIX,KAAK,CAACY,cAAc,EAAE,EAAE;;EAEvC;;;uBAlBWd,kBAAkB;IAAA;EAAA;;;;YAAlBA,kBAAkB;MAAAe,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}