{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, createComponent, EnvironmentInjector, ANIMATION_MODULE_TYPE, booleanAttribute, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { InteractivityChecker, A11yModule } from '@angular/cdk/a11y';\nimport { DOCUMENT } from '@angular/common';\nlet nextId = 0;\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/** Keeps track of the apps currently containing badges. */\nconst badgeApps = new Set();\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\nclass _MatBadgeStyleLoader {\n  static {\n    this.ɵfac = function _MatBadgeStyleLoader_Factory(t) {\n      return new (t || _MatBadgeStyleLoader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _MatBadgeStyleLoader,\n      selectors: [[\"ng-component\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function _MatBadgeStyleLoader_Template(rf, ctx) {},\n      styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:var(--mat-badge-text-font);font-weight:var(--mat-badge-text-weight);border-radius:var(--mat-badge-container-shape)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, unset);min-height:var(--mat-badge-small-size-container-size, unset);line-height:var(--mat-badge-legacy-small-size-container-size, var(--mat-badge-small-size-container-size));padding:var(--mat-badge-small-size-container-padding);font-size:var(--mat-badge-small-size-text-size);margin:var(--mat-badge-small-size-container-offset)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, unset);min-height:var(--mat-badge-container-size, unset);line-height:var(--mat-badge-legacy-container-size, var(--mat-badge-container-size));padding:var(--mat-badge-container-padding);font-size:var(--mat-badge-text-size);margin:var(--mat-badge-container-offset)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, unset);min-height:var(--mat-badge-large-size-container-size, unset);line-height:var(--mat-badge-legacy-large-size-container-size, var(--mat-badge-large-size-container-size));padding:var(--mat-badge-large-size-container-padding);font-size:var(--mat-badge-large-size-text-size);margin:var(--mat-badge-large-size-container-offset)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset)}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatBadgeStyleLoader, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:var(--mat-badge-text-font);font-weight:var(--mat-badge-text-weight);border-radius:var(--mat-badge-container-shape)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, unset);min-height:var(--mat-badge-small-size-container-size, unset);line-height:var(--mat-badge-legacy-small-size-container-size, var(--mat-badge-small-size-container-size));padding:var(--mat-badge-small-size-container-padding);font-size:var(--mat-badge-small-size-text-size);margin:var(--mat-badge-small-size-container-offset)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, unset);min-height:var(--mat-badge-container-size, unset);line-height:var(--mat-badge-legacy-container-size, var(--mat-badge-container-size));padding:var(--mat-badge-container-padding);font-size:var(--mat-badge-text-size);margin:var(--mat-badge-container-offset)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, unset);min-height:var(--mat-badge-large-size-container-size, unset);line-height:var(--mat-badge-legacy-large-size-container-size, var(--mat-badge-large-size-container-size));padding:var(--mat-badge-large-size-container-padding);font-size:var(--mat-badge-large-size-text-size);margin:var(--mat-badge-large-size-container-offset)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset)}\"]\n    }]\n  }], null, null);\n})();\n/** Directive to display a text badge. */\nclass MatBadge {\n  /** The color of the badge. Can be `primary`, `accent`, or `warn`. */\n  get color() {\n    return this._color;\n  }\n  set color(value) {\n    this._setColor(value);\n    this._color = value;\n  }\n  /** The content for the badge */\n  get content() {\n    return this._content;\n  }\n  set content(newContent) {\n    this._updateRenderedContent(newContent);\n  }\n  /** Message used to describe the decorated element via aria-describedby */\n  get description() {\n    return this._description;\n  }\n  set description(newDescription) {\n    this._updateDescription(newDescription);\n  }\n  constructor(_ngZone, _elementRef, _ariaDescriber, _renderer, _animationMode) {\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._ariaDescriber = _ariaDescriber;\n    this._renderer = _renderer;\n    this._animationMode = _animationMode;\n    this._color = 'primary';\n    /** Whether the badge should overlap its contents or not */\n    this.overlap = true;\n    /**\n     * Position the badge should reside.\n     * Accepts any combination of 'above'|'below' and 'before'|'after'\n     */\n    this.position = 'above after';\n    /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n    this.size = 'medium';\n    /** Unique id for the badge */\n    this._id = nextId++;\n    /** Whether the OnInit lifecycle hook has run yet */\n    this._isInitialized = false;\n    /** InteractivityChecker to determine if the badge host is focusable. */\n    this._interactivityChecker = inject(InteractivityChecker);\n    this._document = inject(DOCUMENT);\n    const appRef = inject(ApplicationRef);\n    if (!badgeApps.has(appRef)) {\n      badgeApps.add(appRef);\n      const componentRef = createComponent(_MatBadgeStyleLoader, {\n        environmentInjector: inject(EnvironmentInjector)\n      });\n      appRef.onDestroy(() => {\n        badgeApps.delete(appRef);\n        if (badgeApps.size === 0) {\n          componentRef.destroy();\n        }\n      });\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const nativeElement = _elementRef.nativeElement;\n      if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n        throw Error('matBadge must be attached to an element node.');\n      }\n      const matIconTagName = 'mat-icon';\n      // Heads-up for developers to avoid putting matBadge on <mat-icon>\n      // as it is aria-hidden by default docs mention this at:\n      // https://material.angular.io/components/badge/overview#accessibility\n      if (nativeElement.tagName.toLowerCase() === matIconTagName && nativeElement.getAttribute('aria-hidden') === 'true') {\n        console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` + `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` + `\\n${nativeElement.outerHTML}`);\n      }\n    }\n  }\n  /** Whether the badge is above the host or not */\n  isAbove() {\n    return this.position.indexOf('below') === -1;\n  }\n  /** Whether the badge is after the host or not */\n  isAfter() {\n    return this.position.indexOf('before') === -1;\n  }\n  /**\n   * Gets the element into which the badge's content is being rendered. Undefined if the element\n   * hasn't been created (e.g. if the badge doesn't have content).\n   */\n  getBadgeElement() {\n    return this._badgeElement;\n  }\n  ngOnInit() {\n    // We may have server-side rendered badge that we need to clear.\n    // We need to do this in ngOnInit because the full content of the component\n    // on which the badge is attached won't necessarily be in the DOM until this point.\n    this._clearExistingBadges();\n    if (this.content && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n      this._updateRenderedContent(this.content);\n    }\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n    // We have to destroy it ourselves, otherwise it'll be retained in memory.\n    if (this._renderer.destroyNode) {\n      this._renderer.destroyNode(this._badgeElement);\n      this._inlineBadgeDescription?.remove();\n    }\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n  }\n  /** Gets whether the badge's host element is interactive. */\n  _isHostInteractive() {\n    // Ignore visibility since it requires an expensive style caluclation.\n    return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n      ignoreVisibility: true\n    });\n  }\n  /** Creates the badge element */\n  _createBadgeElement() {\n    const badgeElement = this._renderer.createElement('span');\n    const activeClass = 'mat-badge-active';\n    badgeElement.setAttribute('id', `mat-badge-content-${this._id}`);\n    // The badge is aria-hidden because we don't want it to appear in the page's navigation\n    // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n    badgeElement.setAttribute('aria-hidden', 'true');\n    badgeElement.classList.add(BADGE_CONTENT_CLASS);\n    if (this._animationMode === 'NoopAnimations') {\n      badgeElement.classList.add('_mat-animation-noopable');\n    }\n    this._elementRef.nativeElement.appendChild(badgeElement);\n    // animate in after insertion\n    if (typeof requestAnimationFrame === 'function' && this._animationMode !== 'NoopAnimations') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          badgeElement.classList.add(activeClass);\n        });\n      });\n    } else {\n      badgeElement.classList.add(activeClass);\n    }\n    return badgeElement;\n  }\n  /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n  _updateRenderedContent(newContent) {\n    const newContentNormalized = `${newContent ?? ''}`.trim();\n    // Don't create the badge element if the directive isn't initialized because we want to\n    // append the badge element to the *end* of the host element's content for backwards\n    // compatibility.\n    if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n    }\n    if (this._badgeElement) {\n      this._badgeElement.textContent = newContentNormalized;\n    }\n    this._content = newContentNormalized;\n  }\n  /** Updates the host element's aria description via AriaDescriber. */\n  _updateDescription(newDescription) {\n    // Always start by removing the aria-describedby; we will add a new one if necessary.\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    // NOTE: We only check whether the host is interactive here, which happens during\n    // when then badge content changes. It is possible that the host changes\n    // interactivity status separate from one of these. However, watching the interactivity\n    // status of the host would require a `MutationObserver`, which is likely more code + overhead\n    // than it's worth; from usages inside Google, we see that the vats majority of badges either\n    // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n    if (!newDescription || this._isHostInteractive()) {\n      this._removeInlineDescription();\n    }\n    this._description = newDescription;\n    // We don't add `aria-describedby` for non-interactive hosts elements because we\n    // instead insert the description inline.\n    if (this._isHostInteractive()) {\n      this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n    } else {\n      this._updateInlineDescription();\n    }\n  }\n  _updateInlineDescription() {\n    // Create the inline description element if it doesn't exist\n    if (!this._inlineBadgeDescription) {\n      this._inlineBadgeDescription = this._document.createElement('span');\n      this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n    }\n    this._inlineBadgeDescription.textContent = this.description;\n    this._badgeElement?.appendChild(this._inlineBadgeDescription);\n  }\n  _removeInlineDescription() {\n    this._inlineBadgeDescription?.remove();\n    this._inlineBadgeDescription = undefined;\n  }\n  /** Adds css theme class given the color to the component host */\n  _setColor(colorPalette) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(`mat-badge-${this._color}`);\n    if (colorPalette) {\n      classList.add(`mat-badge-${colorPalette}`);\n    }\n  }\n  /** Clears any existing badges that might be left over from server-side rendering. */\n  _clearExistingBadges() {\n    // Only check direct children of this host element in order to avoid deleting\n    // any badges that might exist in descendant elements.\n    const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n    for (const badgeElement of Array.from(badges)) {\n      if (badgeElement !== this._badgeElement) {\n        badgeElement.remove();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatBadge_Factory(t) {\n      return new (t || MatBadge)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.AriaDescriber), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatBadge,\n      selectors: [[\"\", \"matBadge\", \"\"]],\n      hostAttrs: [1, \"mat-badge\"],\n      hostVars: 20,\n      hostBindings: function MatBadge_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-badge-overlap\", ctx.overlap)(\"mat-badge-above\", ctx.isAbove())(\"mat-badge-below\", !ctx.isAbove())(\"mat-badge-before\", !ctx.isAfter())(\"mat-badge-after\", ctx.isAfter())(\"mat-badge-small\", ctx.size === \"small\")(\"mat-badge-medium\", ctx.size === \"medium\")(\"mat-badge-large\", ctx.size === \"large\")(\"mat-badge-hidden\", ctx.hidden || !ctx.content)(\"mat-badge-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        color: [i0.ɵɵInputFlags.None, \"matBadgeColor\", \"color\"],\n        overlap: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matBadgeOverlap\", \"overlap\", booleanAttribute],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matBadgeDisabled\", \"disabled\", booleanAttribute],\n        position: [i0.ɵɵInputFlags.None, \"matBadgePosition\", \"position\"],\n        content: [i0.ɵɵInputFlags.None, \"matBadge\", \"content\"],\n        description: [i0.ɵɵInputFlags.None, \"matBadgeDescription\", \"description\"],\n        size: [i0.ɵɵInputFlags.None, \"matBadgeSize\", \"size\"],\n        hidden: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matBadgeHidden\", \"hidden\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBadge, [{\n    type: Directive,\n    args: [{\n      selector: '[matBadge]',\n      host: {\n        'class': 'mat-badge',\n        '[class.mat-badge-overlap]': 'overlap',\n        '[class.mat-badge-above]': 'isAbove()',\n        '[class.mat-badge-below]': '!isAbove()',\n        '[class.mat-badge-before]': '!isAfter()',\n        '[class.mat-badge-after]': 'isAfter()',\n        '[class.mat-badge-small]': 'size === \"small\"',\n        '[class.mat-badge-medium]': 'size === \"medium\"',\n        '[class.mat-badge-large]': 'size === \"large\"',\n        '[class.mat-badge-hidden]': 'hidden || !content',\n        '[class.mat-badge-disabled]': 'disabled'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.AriaDescriber\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    color: [{\n      type: Input,\n      args: ['matBadgeColor']\n    }],\n    overlap: [{\n      type: Input,\n      args: [{\n        alias: 'matBadgeOverlap',\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'matBadgeDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    position: [{\n      type: Input,\n      args: ['matBadgePosition']\n    }],\n    content: [{\n      type: Input,\n      args: ['matBadge']\n    }],\n    description: [{\n      type: Input,\n      args: ['matBadgeDescription']\n    }],\n    size: [{\n      type: Input,\n      args: ['matBadgeSize']\n    }],\n    hidden: [{\n      type: Input,\n      args: [{\n        alias: 'matBadgeHidden',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatBadgeModule {\n  static {\n    this.ɵfac = function MatBadgeModule_Factory(t) {\n      return new (t || MatBadgeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatBadgeModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [A11yModule, MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBadgeModule, [{\n    type: NgModule,\n    args: [{\n      // Note: we _shouldn't_ have to import `_MatBadgeStyleLoader`,\n      // but it seems to be necessary for tests.\n      imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader],\n      exports: [MatBadge, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatBadge, MatBadgeModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "ApplicationRef", "createComponent", "EnvironmentInjector", "ANIMATION_MODULE_TYPE", "booleanAttribute", "Directive", "Optional", "Inject", "Input", "NgModule", "MatCommonModule", "i1", "InteractivityChecker", "A11yModule", "DOCUMENT", "nextId", "BADGE_CONTENT_CLASS", "badgeApps", "Set", "_MatBadgeStyleLoader", "ɵfac", "_MatBadgeStyleLoader_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "_MatBadgeStyleLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "MatBadge", "color", "_color", "value", "_setColor", "content", "_content", "newContent", "_updateR<PERSON><PERSON><PERSON><PERSON>nt", "description", "_description", "newDescription", "_updateDescription", "constructor", "_ngZone", "_elementRef", "_ariaDescriber", "_renderer", "_animationMode", "overlap", "position", "size", "_id", "_isInitialized", "_interactivityC<PERSON>cker", "_document", "appRef", "has", "add", "componentRef", "environmentInjector", "onDestroy", "delete", "destroy", "nativeElement", "nodeType", "ELEMENT_NODE", "Error", "matIconTagName", "tagName", "toLowerCase", "getAttribute", "console", "warn", "outerHTML", "isAbove", "indexOf", "isAfter", "getBadgeElement", "_badgeElement", "ngOnInit", "_clearExistingBadges", "_createBadgeElement", "ngOnDestroy", "destroyNode", "_inlineBadgeDescription", "remove", "removeDescription", "_isHostInteractive", "isFocusable", "ignoreVisibility", "badgeElement", "createElement", "activeClass", "setAttribute", "classList", "append<PERSON><PERSON><PERSON>", "requestAnimationFrame", "runOutsideAngular", "newContentNormalized", "trim", "textContent", "_removeInlineDescription", "describe", "_updateInlineDescription", "undefined", "colorPalette", "badges", "querySelectorAll", "Array", "from", "MatBadge_Factory", "ɵɵdirectiveInject", "NgZone", "ElementRef", "AriaDescriber", "Renderer2", "ɵdir", "ɵɵdefineDirective", "hostAttrs", "hostVars", "hostBindings", "MatBadge_HostBindings", "ɵɵclassProp", "hidden", "disabled", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "ɵɵInputTransformsFeature", "selector", "host", "decorators", "alias", "transform", "MatBadgeModule", "MatBadgeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@angular/material/fesm2022/badge.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, createComponent, EnvironmentInjector, ANIMATION_MODULE_TYPE, booleanAttribute, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { InteractivityChecker, A11yModule } from '@angular/cdk/a11y';\nimport { DOCUMENT } from '@angular/common';\n\nlet nextId = 0;\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/** Keeps track of the apps currently containing badges. */\nconst badgeApps = new Set();\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\nclass _MatBadgeStyleLoader {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatBadgeStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: _MatBadgeStyleLoader, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: '', isInline: true, styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:var(--mat-badge-text-font);font-weight:var(--mat-badge-text-weight);border-radius:var(--mat-badge-container-shape)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, unset);min-height:var(--mat-badge-small-size-container-size, unset);line-height:var(--mat-badge-legacy-small-size-container-size, var(--mat-badge-small-size-container-size));padding:var(--mat-badge-small-size-container-padding);font-size:var(--mat-badge-small-size-text-size);margin:var(--mat-badge-small-size-container-offset)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, unset);min-height:var(--mat-badge-container-size, unset);line-height:var(--mat-badge-legacy-container-size, var(--mat-badge-container-size));padding:var(--mat-badge-container-padding);font-size:var(--mat-badge-text-size);margin:var(--mat-badge-container-offset)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, unset);min-height:var(--mat-badge-large-size-container-size, unset);line-height:var(--mat-badge-legacy-large-size-container-size, var(--mat-badge-large-size-container-size));padding:var(--mat-badge-large-size-container-padding);font-size:var(--mat-badge-large-size-text-size);margin:var(--mat-badge-large-size-container-offset)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset)}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatBadgeStyleLoader, decorators: [{\n            type: Component,\n            args: [{ standalone: true, encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:var(--mat-badge-text-font);font-weight:var(--mat-badge-text-weight);border-radius:var(--mat-badge-container-shape)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, unset);min-height:var(--mat-badge-small-size-container-size, unset);line-height:var(--mat-badge-legacy-small-size-container-size, var(--mat-badge-small-size-container-size));padding:var(--mat-badge-small-size-container-padding);font-size:var(--mat-badge-small-size-text-size);margin:var(--mat-badge-small-size-container-offset)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, unset);min-height:var(--mat-badge-container-size, unset);line-height:var(--mat-badge-legacy-container-size, var(--mat-badge-container-size));padding:var(--mat-badge-container-padding);font-size:var(--mat-badge-text-size);margin:var(--mat-badge-container-offset)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, unset);min-height:var(--mat-badge-large-size-container-size, unset);line-height:var(--mat-badge-legacy-large-size-container-size, var(--mat-badge-large-size-container-size));padding:var(--mat-badge-large-size-container-padding);font-size:var(--mat-badge-large-size-text-size);margin:var(--mat-badge-large-size-container-offset)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset)}\"] }]\n        }] });\n/** Directive to display a text badge. */\nclass MatBadge {\n    /** The color of the badge. Can be `primary`, `accent`, or `warn`. */\n    get color() {\n        return this._color;\n    }\n    set color(value) {\n        this._setColor(value);\n        this._color = value;\n    }\n    /** The content for the badge */\n    get content() {\n        return this._content;\n    }\n    set content(newContent) {\n        this._updateRenderedContent(newContent);\n    }\n    /** Message used to describe the decorated element via aria-describedby */\n    get description() {\n        return this._description;\n    }\n    set description(newDescription) {\n        this._updateDescription(newDescription);\n    }\n    constructor(_ngZone, _elementRef, _ariaDescriber, _renderer, _animationMode) {\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._ariaDescriber = _ariaDescriber;\n        this._renderer = _renderer;\n        this._animationMode = _animationMode;\n        this._color = 'primary';\n        /** Whether the badge should overlap its contents or not */\n        this.overlap = true;\n        /**\n         * Position the badge should reside.\n         * Accepts any combination of 'above'|'below' and 'before'|'after'\n         */\n        this.position = 'above after';\n        /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n        this.size = 'medium';\n        /** Unique id for the badge */\n        this._id = nextId++;\n        /** Whether the OnInit lifecycle hook has run yet */\n        this._isInitialized = false;\n        /** InteractivityChecker to determine if the badge host is focusable. */\n        this._interactivityChecker = inject(InteractivityChecker);\n        this._document = inject(DOCUMENT);\n        const appRef = inject(ApplicationRef);\n        if (!badgeApps.has(appRef)) {\n            badgeApps.add(appRef);\n            const componentRef = createComponent(_MatBadgeStyleLoader, {\n                environmentInjector: inject(EnvironmentInjector),\n            });\n            appRef.onDestroy(() => {\n                badgeApps.delete(appRef);\n                if (badgeApps.size === 0) {\n                    componentRef.destroy();\n                }\n            });\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const nativeElement = _elementRef.nativeElement;\n            if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n                throw Error('matBadge must be attached to an element node.');\n            }\n            const matIconTagName = 'mat-icon';\n            // Heads-up for developers to avoid putting matBadge on <mat-icon>\n            // as it is aria-hidden by default docs mention this at:\n            // https://material.angular.io/components/badge/overview#accessibility\n            if (nativeElement.tagName.toLowerCase() === matIconTagName &&\n                nativeElement.getAttribute('aria-hidden') === 'true') {\n                console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` +\n                    `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` +\n                    `\\n${nativeElement.outerHTML}`);\n            }\n        }\n    }\n    /** Whether the badge is above the host or not */\n    isAbove() {\n        return this.position.indexOf('below') === -1;\n    }\n    /** Whether the badge is after the host or not */\n    isAfter() {\n        return this.position.indexOf('before') === -1;\n    }\n    /**\n     * Gets the element into which the badge's content is being rendered. Undefined if the element\n     * hasn't been created (e.g. if the badge doesn't have content).\n     */\n    getBadgeElement() {\n        return this._badgeElement;\n    }\n    ngOnInit() {\n        // We may have server-side rendered badge that we need to clear.\n        // We need to do this in ngOnInit because the full content of the component\n        // on which the badge is attached won't necessarily be in the DOM until this point.\n        this._clearExistingBadges();\n        if (this.content && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n            this._updateRenderedContent(this.content);\n        }\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n        // We have to destroy it ourselves, otherwise it'll be retained in memory.\n        if (this._renderer.destroyNode) {\n            this._renderer.destroyNode(this._badgeElement);\n            this._inlineBadgeDescription?.remove();\n        }\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    }\n    /** Gets whether the badge's host element is interactive. */\n    _isHostInteractive() {\n        // Ignore visibility since it requires an expensive style caluclation.\n        return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n            ignoreVisibility: true,\n        });\n    }\n    /** Creates the badge element */\n    _createBadgeElement() {\n        const badgeElement = this._renderer.createElement('span');\n        const activeClass = 'mat-badge-active';\n        badgeElement.setAttribute('id', `mat-badge-content-${this._id}`);\n        // The badge is aria-hidden because we don't want it to appear in the page's navigation\n        // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n        badgeElement.setAttribute('aria-hidden', 'true');\n        badgeElement.classList.add(BADGE_CONTENT_CLASS);\n        if (this._animationMode === 'NoopAnimations') {\n            badgeElement.classList.add('_mat-animation-noopable');\n        }\n        this._elementRef.nativeElement.appendChild(badgeElement);\n        // animate in after insertion\n        if (typeof requestAnimationFrame === 'function' && this._animationMode !== 'NoopAnimations') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    badgeElement.classList.add(activeClass);\n                });\n            });\n        }\n        else {\n            badgeElement.classList.add(activeClass);\n        }\n        return badgeElement;\n    }\n    /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n    _updateRenderedContent(newContent) {\n        const newContentNormalized = `${newContent ?? ''}`.trim();\n        // Don't create the badge element if the directive isn't initialized because we want to\n        // append the badge element to the *end* of the host element's content for backwards\n        // compatibility.\n        if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n        }\n        if (this._badgeElement) {\n            this._badgeElement.textContent = newContentNormalized;\n        }\n        this._content = newContentNormalized;\n    }\n    /** Updates the host element's aria description via AriaDescriber. */\n    _updateDescription(newDescription) {\n        // Always start by removing the aria-describedby; we will add a new one if necessary.\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n        // NOTE: We only check whether the host is interactive here, which happens during\n        // when then badge content changes. It is possible that the host changes\n        // interactivity status separate from one of these. However, watching the interactivity\n        // status of the host would require a `MutationObserver`, which is likely more code + overhead\n        // than it's worth; from usages inside Google, we see that the vats majority of badges either\n        // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n        if (!newDescription || this._isHostInteractive()) {\n            this._removeInlineDescription();\n        }\n        this._description = newDescription;\n        // We don't add `aria-describedby` for non-interactive hosts elements because we\n        // instead insert the description inline.\n        if (this._isHostInteractive()) {\n            this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n        }\n        else {\n            this._updateInlineDescription();\n        }\n    }\n    _updateInlineDescription() {\n        // Create the inline description element if it doesn't exist\n        if (!this._inlineBadgeDescription) {\n            this._inlineBadgeDescription = this._document.createElement('span');\n            this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n        }\n        this._inlineBadgeDescription.textContent = this.description;\n        this._badgeElement?.appendChild(this._inlineBadgeDescription);\n    }\n    _removeInlineDescription() {\n        this._inlineBadgeDescription?.remove();\n        this._inlineBadgeDescription = undefined;\n    }\n    /** Adds css theme class given the color to the component host */\n    _setColor(colorPalette) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(`mat-badge-${this._color}`);\n        if (colorPalette) {\n            classList.add(`mat-badge-${colorPalette}`);\n        }\n    }\n    /** Clears any existing badges that might be left over from server-side rendering. */\n    _clearExistingBadges() {\n        // Only check direct children of this host element in order to avoid deleting\n        // any badges that might exist in descendant elements.\n        const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n        for (const badgeElement of Array.from(badges)) {\n            if (badgeElement !== this._badgeElement) {\n                badgeElement.remove();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadge, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i1.AriaDescriber }, { token: i0.Renderer2 }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatBadge, isStandalone: true, selector: \"[matBadge]\", inputs: { color: [\"matBadgeColor\", \"color\"], overlap: [\"matBadgeOverlap\", \"overlap\", booleanAttribute], disabled: [\"matBadgeDisabled\", \"disabled\", booleanAttribute], position: [\"matBadgePosition\", \"position\"], content: [\"matBadge\", \"content\"], description: [\"matBadgeDescription\", \"description\"], size: [\"matBadgeSize\", \"size\"], hidden: [\"matBadgeHidden\", \"hidden\", booleanAttribute] }, host: { properties: { \"class.mat-badge-overlap\": \"overlap\", \"class.mat-badge-above\": \"isAbove()\", \"class.mat-badge-below\": \"!isAbove()\", \"class.mat-badge-before\": \"!isAfter()\", \"class.mat-badge-after\": \"isAfter()\", \"class.mat-badge-small\": \"size === \\\"small\\\"\", \"class.mat-badge-medium\": \"size === \\\"medium\\\"\", \"class.mat-badge-large\": \"size === \\\"large\\\"\", \"class.mat-badge-hidden\": \"hidden || !content\", \"class.mat-badge-disabled\": \"disabled\" }, classAttribute: \"mat-badge\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadge, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matBadge]',\n                    host: {\n                        'class': 'mat-badge',\n                        '[class.mat-badge-overlap]': 'overlap',\n                        '[class.mat-badge-above]': 'isAbove()',\n                        '[class.mat-badge-below]': '!isAbove()',\n                        '[class.mat-badge-before]': '!isAfter()',\n                        '[class.mat-badge-after]': 'isAfter()',\n                        '[class.mat-badge-small]': 'size === \"small\"',\n                        '[class.mat-badge-medium]': 'size === \"medium\"',\n                        '[class.mat-badge-large]': 'size === \"large\"',\n                        '[class.mat-badge-hidden]': 'hidden || !content',\n                        '[class.mat-badge-disabled]': 'disabled',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i1.AriaDescriber }, { type: i0.Renderer2 }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { color: [{\n                type: Input,\n                args: ['matBadgeColor']\n            }], overlap: [{\n                type: Input,\n                args: [{ alias: 'matBadgeOverlap', transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'matBadgeDisabled', transform: booleanAttribute }]\n            }], position: [{\n                type: Input,\n                args: ['matBadgePosition']\n            }], content: [{\n                type: Input,\n                args: ['matBadge']\n            }], description: [{\n                type: Input,\n                args: ['matBadgeDescription']\n            }], size: [{\n                type: Input,\n                args: ['matBadgeSize']\n            }], hidden: [{\n                type: Input,\n                args: [{ alias: 'matBadgeHidden', transform: booleanAttribute }]\n            }] } });\n\nclass MatBadgeModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader], exports: [MatBadge, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, imports: [A11yModule, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    // Note: we _shouldn't_ have to import `_MatBadgeStyleLoader`,\n                    // but it seems to be necessary for tests.\n                    imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader],\n                    exports: [MatBadge, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatBadge, MatBadgeModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,cAAc,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC1O,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,oBAAoB,EAAEC,UAAU,QAAQ,mBAAmB;AACpE,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C,IAAIC,MAAM,GAAG,CAAC;AACd,MAAMC,mBAAmB,GAAG,mBAAmB;AAC/C;AACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACI,IAAI,kBAD8E5B,EAAE,CAAA6B,iBAAA;MAAAC,IAAA,EACJN,oBAAoB;MAAAO,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADlBjC,EAAE,CAAAkC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACyxG;EAAE;AACj4G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG5C,EAAE,CAAA6C,iBAAA,CAGXrB,oBAAoB,EAAc,CAAC;IAClHM,IAAI,EAAE7B,SAAS;IACf6C,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEU,aAAa,EAAExC,iBAAiB,CAAC6C,IAAI;MAAEV,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAExC,uBAAuB,CAAC6C,MAAM;MAAEP,MAAM,EAAE,CAAC,kkGAAkkG;IAAE,CAAC;EACntG,CAAC,CAAC;AAAA;AACV;AACA,MAAMQ,QAAQ,CAAC;EACX;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACE,KAAK,EAAE;IACb,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC;IACrB,IAAI,CAACD,MAAM,GAAGC,KAAK;EACvB;EACA;EACA,IAAIE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,UAAU,EAAE;IACpB,IAAI,CAACC,sBAAsB,CAACD,UAAU,CAAC;EAC3C;EACA;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACE,cAAc,EAAE;IAC5B,IAAI,CAACC,kBAAkB,CAACD,cAAc,CAAC;EAC3C;EACAE,WAAWA,CAACC,OAAO,EAAEC,WAAW,EAAEC,cAAc,EAAEC,SAAS,EAAEC,cAAc,EAAE;IACzE,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAChB,MAAM,GAAG,SAAS;IACvB;IACA,IAAI,CAACiB,OAAO,GAAG,IAAI;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,aAAa;IAC7B;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,GAAG,GAAGnD,MAAM,EAAE;IACnB;IACA,IAAI,CAACoD,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,qBAAqB,GAAGrE,MAAM,CAACa,oBAAoB,CAAC;IACzD,IAAI,CAACyD,SAAS,GAAGtE,MAAM,CAACe,QAAQ,CAAC;IACjC,MAAMwD,MAAM,GAAGvE,MAAM,CAACC,cAAc,CAAC;IACrC,IAAI,CAACiB,SAAS,CAACsD,GAAG,CAACD,MAAM,CAAC,EAAE;MACxBrD,SAAS,CAACuD,GAAG,CAACF,MAAM,CAAC;MACrB,MAAMG,YAAY,GAAGxE,eAAe,CAACkB,oBAAoB,EAAE;QACvDuD,mBAAmB,EAAE3E,MAAM,CAACG,mBAAmB;MACnD,CAAC,CAAC;MACFoE,MAAM,CAACK,SAAS,CAAC,MAAM;QACnB1D,SAAS,CAAC2D,MAAM,CAACN,MAAM,CAAC;QACxB,IAAIrD,SAAS,CAACgD,IAAI,KAAK,CAAC,EAAE;UACtBQ,YAAY,CAACI,OAAO,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN;IACA,IAAI,OAAOtC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMuC,aAAa,GAAGnB,WAAW,CAACmB,aAAa;MAC/C,IAAIA,aAAa,CAACC,QAAQ,KAAKD,aAAa,CAACE,YAAY,EAAE;QACvD,MAAMC,KAAK,CAAC,+CAA+C,CAAC;MAChE;MACA,MAAMC,cAAc,GAAG,UAAU;MACjC;MACA;MACA;MACA,IAAIJ,aAAa,CAACK,OAAO,CAACC,WAAW,CAAC,CAAC,KAAKF,cAAc,IACtDJ,aAAa,CAACO,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;QACtDC,OAAO,CAACC,IAAI,CAAC,wDAAwD,GACjE,gGAAgG,GAChG,KAAKT,aAAa,CAACU,SAAS,EAAE,CAAC;MACvC;IACJ;EACJ;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACzB,QAAQ,CAAC0B,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC3B,QAAQ,CAAC0B,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACIE,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa;EAC7B;EACAC,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,IAAI,CAAC9C,OAAO,IAAI,CAAC,IAAI,CAAC4C,aAAa,EAAE;MACrC,IAAI,CAACA,aAAa,GAAG,IAAI,CAACG,mBAAmB,CAAC,CAAC;MAC/C,IAAI,CAAC5C,sBAAsB,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7C;IACA,IAAI,CAACkB,cAAc,GAAG,IAAI;EAC9B;EACA8B,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,IAAI,CAACpC,SAAS,CAACqC,WAAW,EAAE;MAC5B,IAAI,CAACrC,SAAS,CAACqC,WAAW,CAAC,IAAI,CAACL,aAAa,CAAC;MAC9C,IAAI,CAACM,uBAAuB,EAAEC,MAAM,CAAC,CAAC;IAC1C;IACA,IAAI,CAACxC,cAAc,CAACyC,iBAAiB,CAAC,IAAI,CAAC1C,WAAW,CAACmB,aAAa,EAAE,IAAI,CAACzB,WAAW,CAAC;EAC3F;EACA;EACAiD,kBAAkBA,CAAA,EAAG;IACjB;IACA,OAAO,IAAI,CAAClC,qBAAqB,CAACmC,WAAW,CAAC,IAAI,CAAC5C,WAAW,CAACmB,aAAa,EAAE;MAC1E0B,gBAAgB,EAAE;IACtB,CAAC,CAAC;EACN;EACA;EACAR,mBAAmBA,CAAA,EAAG;IAClB,MAAMS,YAAY,GAAG,IAAI,CAAC5C,SAAS,CAAC6C,aAAa,CAAC,MAAM,CAAC;IACzD,MAAMC,WAAW,GAAG,kBAAkB;IACtCF,YAAY,CAACG,YAAY,CAAC,IAAI,EAAE,qBAAqB,IAAI,CAAC1C,GAAG,EAAE,CAAC;IAChE;IACA;IACAuC,YAAY,CAACG,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChDH,YAAY,CAACI,SAAS,CAACrC,GAAG,CAACxD,mBAAmB,CAAC;IAC/C,IAAI,IAAI,CAAC8C,cAAc,KAAK,gBAAgB,EAAE;MAC1C2C,YAAY,CAACI,SAAS,CAACrC,GAAG,CAAC,yBAAyB,CAAC;IACzD;IACA,IAAI,CAACb,WAAW,CAACmB,aAAa,CAACgC,WAAW,CAACL,YAAY,CAAC;IACxD;IACA,IAAI,OAAOM,qBAAqB,KAAK,UAAU,IAAI,IAAI,CAACjD,cAAc,KAAK,gBAAgB,EAAE;MACzF,IAAI,CAACJ,OAAO,CAACsD,iBAAiB,CAAC,MAAM;QACjCD,qBAAqB,CAAC,MAAM;UACxBN,YAAY,CAACI,SAAS,CAACrC,GAAG,CAACmC,WAAW,CAAC;QAC3C,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACDF,YAAY,CAACI,SAAS,CAACrC,GAAG,CAACmC,WAAW,CAAC;IAC3C;IACA,OAAOF,YAAY;EACvB;EACA;EACArD,sBAAsBA,CAACD,UAAU,EAAE;IAC/B,MAAM8D,oBAAoB,GAAG,GAAG9D,UAAU,IAAI,EAAE,EAAE,CAAC+D,IAAI,CAAC,CAAC;IACzD;IACA;IACA;IACA,IAAI,IAAI,CAAC/C,cAAc,IAAI8C,oBAAoB,IAAI,CAAC,IAAI,CAACpB,aAAa,EAAE;MACpE,IAAI,CAACA,aAAa,GAAG,IAAI,CAACG,mBAAmB,CAAC,CAAC;IACnD;IACA,IAAI,IAAI,CAACH,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACsB,WAAW,GAAGF,oBAAoB;IACzD;IACA,IAAI,CAAC/D,QAAQ,GAAG+D,oBAAoB;EACxC;EACA;EACAzD,kBAAkBA,CAACD,cAAc,EAAE;IAC/B;IACA,IAAI,CAACK,cAAc,CAACyC,iBAAiB,CAAC,IAAI,CAAC1C,WAAW,CAACmB,aAAa,EAAE,IAAI,CAACzB,WAAW,CAAC;IACvF;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,cAAc,IAAI,IAAI,CAAC+C,kBAAkB,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACc,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAAC9D,YAAY,GAAGC,cAAc;IAClC;IACA;IACA,IAAI,IAAI,CAAC+C,kBAAkB,CAAC,CAAC,EAAE;MAC3B,IAAI,CAAC1C,cAAc,CAACyD,QAAQ,CAAC,IAAI,CAAC1D,WAAW,CAACmB,aAAa,EAAEvB,cAAc,CAAC;IAChF,CAAC,MACI;MACD,IAAI,CAAC+D,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACAA,wBAAwBA,CAAA,EAAG;IACvB;IACA,IAAI,CAAC,IAAI,CAACnB,uBAAuB,EAAE;MAC/B,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAAC9B,SAAS,CAACqC,aAAa,CAAC,MAAM,CAAC;MACnE,IAAI,CAACP,uBAAuB,CAACU,SAAS,CAACrC,GAAG,CAAC,qBAAqB,CAAC;IACrE;IACA,IAAI,CAAC2B,uBAAuB,CAACgB,WAAW,GAAG,IAAI,CAAC9D,WAAW;IAC3D,IAAI,CAACwC,aAAa,EAAEiB,WAAW,CAAC,IAAI,CAACX,uBAAuB,CAAC;EACjE;EACAiB,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACjB,uBAAuB,EAAEC,MAAM,CAAC,CAAC;IACtC,IAAI,CAACD,uBAAuB,GAAGoB,SAAS;EAC5C;EACA;EACAvE,SAASA,CAACwE,YAAY,EAAE;IACpB,MAAMX,SAAS,GAAG,IAAI,CAAClD,WAAW,CAACmB,aAAa,CAAC+B,SAAS;IAC1DA,SAAS,CAACT,MAAM,CAAC,aAAa,IAAI,CAACtD,MAAM,EAAE,CAAC;IAC5C,IAAI0E,YAAY,EAAE;MACdX,SAAS,CAACrC,GAAG,CAAC,aAAagD,YAAY,EAAE,CAAC;IAC9C;EACJ;EACA;EACAzB,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA,MAAM0B,MAAM,GAAG,IAAI,CAAC9D,WAAW,CAACmB,aAAa,CAAC4C,gBAAgB,CAAC,aAAa1G,mBAAmB,EAAE,CAAC;IAClG,KAAK,MAAMyF,YAAY,IAAIkB,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,EAAE;MAC3C,IAAIhB,YAAY,KAAK,IAAI,CAACZ,aAAa,EAAE;QACrCY,YAAY,CAACL,MAAM,CAAC,CAAC;MACzB;IACJ;EACJ;EACA;IAAS,IAAI,CAAChF,IAAI,YAAAyG,iBAAAvG,CAAA;MAAA,YAAAA,CAAA,IAAwFsB,QAAQ,EA7NlBjD,EAAE,CAAAmI,iBAAA,CA6NkCnI,EAAE,CAACoI,MAAM,GA7N7CpI,EAAE,CAAAmI,iBAAA,CA6NwDnI,EAAE,CAACqI,UAAU,GA7NvErI,EAAE,CAAAmI,iBAAA,CA6NkFnH,EAAE,CAACsH,aAAa,GA7NpGtI,EAAE,CAAAmI,iBAAA,CA6N+GnI,EAAE,CAACuI,SAAS,GA7N7HvI,EAAE,CAAAmI,iBAAA,CA6NwI3H,qBAAqB;IAAA,CAA4D;EAAE;EAC7T;IAAS,IAAI,CAACgI,IAAI,kBA9N8ExI,EAAE,CAAAyI,iBAAA;MAAA3G,IAAA,EA8NJmB,QAAQ;MAAAlB,SAAA;MAAA2G,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAAtG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9NNvC,EAAE,CAAA8I,WAAA,sBAAAtG,GAAA,CAAA4B,OA8NG,CAAC,oBAAR5B,GAAA,CAAAsD,OAAA,CAAQ,CAAD,CAAC,qBAAPtD,GAAA,CAAAsD,OAAA,CAAQ,CAAF,CAAC,sBAAPtD,GAAA,CAAAwD,OAAA,CAAQ,CAAF,CAAC,oBAARxD,GAAA,CAAAwD,OAAA,CAAQ,CAAD,CAAC,oBAAAxD,GAAA,CAAA8B,IAAA,KAAC,OAAF,CAAC,qBAAA9B,GAAA,CAAA8B,IAAA,KAAC,QAAF,CAAC,oBAAA9B,GAAA,CAAA8B,IAAA,KAAC,OAAF,CAAC,qBAAA9B,GAAA,CAAAuG,MAAA,KAAAvG,GAAA,CAAAc,OAAD,CAAC,uBAAAd,GAAA,CAAAwG,QAAD,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAA/F,KAAA,GA9NNlD,EAAE,CAAAkJ,YAAA,CAAAnG,IAAA;QAAAqB,OAAA,GAAFpE,EAAE,CAAAkJ,YAAA,CAAAC,0BAAA,gCA8NuI1I,gBAAgB;QAAAuI,QAAA,GA9NzJhJ,EAAE,CAAAkJ,YAAA,CAAAC,0BAAA,kCA8NqM1I,gBAAgB;QAAA4D,QAAA,GA9NvNrE,EAAE,CAAAkJ,YAAA,CAAAnG,IAAA;QAAAO,OAAA,GAAFtD,EAAE,CAAAkJ,YAAA,CAAAnG,IAAA;QAAAW,WAAA,GAAF1D,EAAE,CAAAkJ,YAAA,CAAAnG,IAAA;QAAAuB,IAAA,GAAFtE,EAAE,CAAAkJ,YAAA,CAAAnG,IAAA;QAAAgG,MAAA,GAAF/I,EAAE,CAAAkJ,YAAA,CAAAC,0BAAA,8BA8Nga1I,gBAAgB;MAAA;MAAAuB,UAAA;MAAAC,QAAA,GA9NlbjC,EAAE,CAAAoJ,wBAAA;IAAA,EA8Nm6B;EAAE;AAC3gC;AACA;EAAA,QAAAxG,SAAA,oBAAAA,SAAA,KAhOoG5C,EAAE,CAAA6C,iBAAA,CAgOXI,QAAQ,EAAc,CAAC;IACtGnB,IAAI,EAAEpB,SAAS;IACfoC,IAAI,EAAE,CAAC;MACCuG,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE;QACF,OAAO,EAAE,WAAW;QACpB,2BAA2B,EAAE,SAAS;QACtC,yBAAyB,EAAE,WAAW;QACtC,yBAAyB,EAAE,YAAY;QACvC,0BAA0B,EAAE,YAAY;QACxC,yBAAyB,EAAE,WAAW;QACtC,yBAAyB,EAAE,kBAAkB;QAC7C,0BAA0B,EAAE,mBAAmB;QAC/C,yBAAyB,EAAE,kBAAkB;QAC7C,0BAA0B,EAAE,oBAAoB;QAChD,4BAA4B,EAAE;MAClC,CAAC;MACDtH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE9B,EAAE,CAACoI;EAAO,CAAC,EAAE;IAAEtG,IAAI,EAAE9B,EAAE,CAACqI;EAAW,CAAC,EAAE;IAAEvG,IAAI,EAAEd,EAAE,CAACsH;EAAc,CAAC,EAAE;IAAExG,IAAI,EAAE9B,EAAE,CAACuI;EAAU,CAAC,EAAE;IAAEzG,IAAI,EAAE8F,SAAS;IAAE2B,UAAU,EAAE,CAAC;MACjJzH,IAAI,EAAEnB;IACV,CAAC,EAAE;MACCmB,IAAI,EAAElB,MAAM;MACZkC,IAAI,EAAE,CAACtC,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE0C,KAAK,EAAE,CAAC;MACjCpB,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEsB,OAAO,EAAE,CAAC;MACVtC,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC;QAAE0G,KAAK,EAAE,iBAAiB;QAAEC,SAAS,EAAEhJ;MAAiB,CAAC;IACpE,CAAC,CAAC;IAAEuI,QAAQ,EAAE,CAAC;MACXlH,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC;QAAE0G,KAAK,EAAE,kBAAkB;QAAEC,SAAS,EAAEhJ;MAAiB,CAAC;IACrE,CAAC,CAAC;IAAE4D,QAAQ,EAAE,CAAC;MACXvC,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEQ,OAAO,EAAE,CAAC;MACVxB,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEY,WAAW,EAAE,CAAC;MACd5B,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEwB,IAAI,EAAE,CAAC;MACPxC,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEiG,MAAM,EAAE,CAAC;MACTjH,IAAI,EAAEjB,KAAK;MACXiC,IAAI,EAAE,CAAC;QAAE0G,KAAK,EAAE,gBAAgB;QAAEC,SAAS,EAAEhJ;MAAiB,CAAC;IACnE,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiJ,cAAc,CAAC;EACjB;IAAS,IAAI,CAACjI,IAAI,YAAAkI,uBAAAhI,CAAA;MAAA,YAAAA,CAAA,IAAwF+H,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBApR8E5J,EAAE,CAAA6J,gBAAA;MAAA/H,IAAA,EAoRS4H;IAAc,EAAiH;EAAE;EAC5O;IAAS,IAAI,CAACI,IAAI,kBArR8E9J,EAAE,CAAA+J,gBAAA;MAAAC,OAAA,GAqRmC9I,UAAU,EAAEH,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC3L;AACA;EAAA,QAAA6B,SAAA,oBAAAA,SAAA,KAvRoG5C,EAAE,CAAA6C,iBAAA,CAuRX6G,cAAc,EAAc,CAAC;IAC5G5H,IAAI,EAAEhB,QAAQ;IACdgC,IAAI,EAAE,CAAC;MACC;MACA;MACAkH,OAAO,EAAE,CAAC9I,UAAU,EAAEH,eAAe,EAAEkC,QAAQ,EAAEzB,oBAAoB,CAAC;MACtEyI,OAAO,EAAE,CAAChH,QAAQ,EAAElC,eAAe;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASkC,QAAQ,EAAEyG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}