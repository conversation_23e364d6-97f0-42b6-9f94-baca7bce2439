import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Subject, takeUntil } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ProductService } from '../../../../core/services/product.service';
import { AuthService } from '../../../../core/services/auth.service';
import { CartService } from '../../../../core/services/cart.service';
import { WishlistService } from '../../../../core/services/wishlist.service';
import { ShopDataService } from '../../../../core/services/shop-data.service';
import { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';
import { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';
import { ErrorDisplayComponent } from '../../../../shared/components/error-display/error-display.component';
import { FeaturedBrandsComponent } from '../../../../shared/components/featured-brands/featured-brands.component';
import { TrendingNowComponent } from '../../../../shared/components/trending-now/trending-now.component';
import { NewArrivalsComponent } from '../../../../shared/components/new-arrivals/new-arrivals.component';
import { QuickLinksComponent } from '../../../../shared/components/quick-links/quick-links.component';
import { ErrorHandlerService } from '../../../../core/services/error-handler.service';
import { LoadingService } from '../../../../core/services/loading.service';

@Component({
  selector: 'app-shop',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NoDataComponent,
    LoadingSpinnerComponent,
    ErrorDisplayComponent,
    FeaturedBrandsComponent,
    TrendingNowComponent,
    NewArrivalsComponent,
    QuickLinksComponent
  ],
  templateUrl: './shop.component.html',
  styleUrls: ['./shop.component.scss']
})
export class ShopComponent implements OnInit, OnDestroy {
  // Main shop data
  searchQuery: string = '';
  isLoading = true;
  showMainSections = true;
  shopStats: any = null;

  // Enhanced filtering and search
  selectedCategory = '';
  selectedSubcategory = '';
  selectedBrand = '';
  filterType = ''; // 'suggested', 'trending', etc.
  sortBy = 'featured';
  priceRange = { min: 0, max: 10000 };

  // Pagination for filtered results
  currentPage = 1;
  itemsPerPage = 24;
  hasMore = false;

  // Filtered products (when searching/filtering)
  allProducts: any[] = [];
  filteredProducts: any[] = [];

  private destroy$ = new Subject<void>();

  constructor(
    private productService: ProductService,
    private authService: AuthService,
    private cartService: CartService,
    private wishlistService: WishlistService,
    private shopDataService: ShopDataService,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    private errorHandlerService: ErrorHandlerService,
    private loadingService: LoadingService
  ) {}

  ngOnInit() {
    // Check for query parameters
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.searchQuery = params['q'] || '';
        this.selectedCategory = params['category'] || '';
        this.filterType = params['filter'] || '';
        this.sortBy = params['sort'] || 'featured';

        this.loadShopData();
      });

    // Load shop statistics
    this.loadShopStats();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadShopData() {
    this.isLoading = true;

    // If we have filters or search, load filtered products and hide main sections
    if (this.searchQuery || this.selectedCategory || this.filterType) {
      this.showMainSections = false;
      this.loadProductsWithFilters();
    } else {
      // Show main sections and load all shop data
      this.showMainSections = true;
      this.shopDataService.loadAllShopData()
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (data) => {
            console.log('✅ All shop data loaded successfully');
            this.isLoading = false;
          },
          error: (error) => {
            console.error('❌ Error loading shop data:', error);
            this.isLoading = false;
          }
        });
    }
  }

  loadShopStats() {
    this.shopDataService.loadShopStats()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (stats) => {
          this.shopStats = stats;
        },
        error: (error) => {
          console.error('Error loading shop stats:', error);
        }
      });
  }

  // Search and navigation methods
  search() {
    if (this.searchQuery.trim()) {
      this.updateUrlParams();
      this.loadShopData();
    }
  }

  navigateToCategory(categorySlug: string) {
    this.router.navigate(['/shop/category', categorySlug]);
  }

  navigateToBrand(brandSlug: string) {
    this.router.navigate(['/shop/brand', brandSlug]);
  }

  navigateToProduct(productId: string) {
    this.router.navigate(['/product', productId]);
  }

  // Enhanced filtering methods
  onCategoryChange() {
    this.selectedSubcategory = '';
    this.updateUrlParams();
    this.loadShopData();
  }

  onFilterChange() {
    this.updateUrlParams();
    this.loadShopData();
  }

  onSortChange() {
    this.updateUrlParams();
    this.loadShopData();
  }

  clearFilters() {
    this.searchQuery = '';
    this.selectedCategory = '';
    this.selectedSubcategory = '';
    this.selectedBrand = '';
    this.filterType = '';
    this.sortBy = 'featured';
    this.priceRange = { min: 0, max: 10000 };
    this.updateUrlParams();
    this.loadShopData();
  }

  private updateUrlParams() {
    const queryParams: any = {};

    if (this.searchQuery) queryParams.q = this.searchQuery;
    if (this.selectedCategory) queryParams.category = this.selectedCategory;
    if (this.filterType) queryParams.filter = this.filterType;
    if (this.sortBy !== 'featured') queryParams.sort = this.sortBy;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge'
    });
  }

  // Enhanced product loading with filtering
  loadProductsWithFilters() {
    this.isLoading = true;

    const filters = {
      category: this.selectedCategory,
      subcategory: this.selectedSubcategory,
      brand: this.selectedBrand,
      sortBy: this.sortBy,
      page: this.currentPage,
      limit: this.itemsPerPage
    };

    this.shopDataService.searchProducts(this.searchQuery, filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.allProducts = response.products || [];
          this.filteredProducts = this.allProducts;
          this.hasMore = response.totalCount > (this.currentPage * this.itemsPerPage);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading filtered products:', error);
          this.allProducts = [];
          this.filteredProducts = [];
          this.isLoading = false;
        }
      });
  }

  // Utility methods
  getProductImage(product: any): string {
    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';
  }

  getDiscountPercentage(product: any): number {
    if (!product.originalPrice || product.originalPrice <= product.price) return 0;
    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }
}
