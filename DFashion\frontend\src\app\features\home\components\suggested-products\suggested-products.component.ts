import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';

interface Product {
  _id: string;
  name: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;
  vendor: {
    _id: string;
    username: string;
    fullName: string;
    avatar: string;
    isInfluencer: boolean;
  };
  createdBy?: {
    _id: string;
    username: string;
    fullName: string;
    avatar: string;
    isInfluencer: boolean;
  };
  analytics: {
    views: number;
    likes: number;
    shares: number;
    purchases: number;
  };
  rating?: {
    average: number;
    count: number;
  };
}

@Component({
  selector: 'app-suggested-products',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './suggested-products.component.html',
  styleUrls: ['./suggested-products.component.scss']
})
export class SuggestedProductsComponent implements OnInit, OnDestroy {
  products: Product[] = [];
  isLoading = true;
  error: string | null = null;
  currentPage = 1;
  totalPages = 1;
  hasMore = false;

  private subscriptions: Subscription[] = [];

  constructor(
    public router: Router,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.loadSuggestedProducts();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadSuggestedProducts(page: number = 1) {
    this.isLoading = true;
    this.error = null;

    this.subscriptions.push(
      this.http.get<any>(`${environment.apiUrl}/products/suggested?page=${page}&limit=12`).subscribe({
        next: (response) => {
          if (response.success) {
            if (page === 1) {
              this.products = response.products;
            } else {
              this.products = [...this.products, ...response.products];
            }
            
            this.currentPage = response.pagination.page;
            this.totalPages = response.pagination.pages;
            this.hasMore = this.currentPage < this.totalPages;
          } else {
            this.loadFallbackProducts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading suggested products:', error);
          if (page === 1) {
            this.loadFallbackProducts();
          }
          this.error = 'Failed to load suggested products';
          this.isLoading = false;
        }
      })
    );
  }

  loadFallbackProducts() {
    this.products = [
      {
        _id: '1',
        name: 'Vintage Denim Jacket',
        price: 89.99,
        originalPrice: 129.99,
        discount: 31,
        images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400', isPrimary: true }],
        vendor: {
          _id: '1',
          username: 'fashionista_maya',
          fullName: 'Maya Rodriguez',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',
          isInfluencer: true
        },
        analytics: { views: 15420, likes: 892, shares: 156, purchases: 234 },
        rating: { average: 4.2, count: 156 }
      },
      {
        _id: '2',
        name: 'Silk Slip Dress',
        price: 159.99,
        originalPrice: 199.99,
        discount: 20,
        images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400', isPrimary: true }],
        vendor: {
          _id: '2',
          username: 'style_guru_alex',
          fullName: 'Alex Chen',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
          isInfluencer: true
        },
        analytics: { views: 12890, likes: 1205, shares: 289, purchases: 167 },
        rating: { average: 4.5, count: 89 }
      },
      {
        _id: '3',
        name: 'Chunky Knit Sweater',
        price: 79.99,
        images: [{ url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400', isPrimary: true }],
        vendor: {
          _id: '3',
          username: 'beauty_by_sarah',
          fullName: 'Sarah Johnson',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
          isInfluencer: true
        },
        analytics: { views: 8945, likes: 567, shares: 89, purchases: 123 },
        rating: { average: 4.7, count: 67 }
      }
    ];
  }

  loadMore() {
    if (this.hasMore && !this.isLoading) {
      this.loadSuggestedProducts(this.currentPage + 1);
    }
  }

  viewProduct(product: Product) {
    this.router.navigate(['/product', product._id]);
  }

  viewVendor(vendor: any) {
    this.router.navigate(['/vendor', vendor.username]);
  }

  addToCart(product: Product, event: Event) {
    event.stopPropagation();
    // TODO: Implement add to cart functionality
    console.log('Add to cart:', product);
  }

  toggleWishlist(product: Product, event: Event) {
    event.stopPropagation();
    // TODO: Implement wishlist functionality
    console.log('Toggle wishlist:', product);
  }

  shareProduct(product: Product, event: Event) {
    event.stopPropagation();
    // TODO: Implement share functionality
    console.log('Share product:', product);
  }

  getDiscountPercentage(product: Product): number {
    if (product.originalPrice && product.originalPrice > product.price) {
      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    }
    return product.discount || 0;
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  retry() {
    this.loadSuggestedProducts(1);
  }

  trackByProductId(index: number, product: Product): string {
    return product._id;
  }
}
