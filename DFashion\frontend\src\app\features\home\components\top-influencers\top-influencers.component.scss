.top-influencers-container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);

  @media (max-width: 768px) {
    margin: 8px;
    padding: 16px;
    border-radius: 12px;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin: 0;

    i {
      color: #ffd700;
      font-size: 20px;
      animation: sparkle 2s infinite;
    }

    @media (max-width: 768px) {
      font-size: 20px;
    }
  }

  .view-all-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
    }

    @media (max-width: 768px) {
      padding: 6px 12px;
      font-size: 14px;
    }
  }
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
}

.category-filter {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;

  .category-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 8px 16px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
      font-weight: 600;
    }

    i {
      font-size: 12px;
    }

    @media (max-width: 768px) {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}

.influencers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
}

.influencer-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  &.top-rank {
    border: 2px solid #ffd700;
    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.3);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
      animation: shimmer 2s infinite;
    }
  }

  @media (max-width: 768px) {
    padding: 16px;
    border-radius: 12px;

    &:hover {
      transform: translateY(-4px);
    }
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.rank-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #744210;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);

  .rank-number {
    font-size: 14px;
  }

  i {
    font-size: 10px;
    animation: bounce 1.5s infinite;
  }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.influencer-avatar-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 16px;

  .influencer-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #667eea;
    transition: transform 0.3s ease;
  }

  &:hover .influencer-avatar {
    transform: scale(1.05);
  }

  .verified-badge {
    position: absolute;
    bottom: 0;
    right: calc(50% - 50px);
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);

    i {
      font-size: 12px;
    }
  }

  .category-badge {
    position: absolute;
    bottom: 0;
    left: calc(50% - 50px);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    i {
      font-size: 10px;
    }
  }
}

.influencer-info {
  text-align: center;

  .influencer-name {
    font-size: 18px;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 4px 0;
    line-height: 1.2;
  }

  .influencer-username {
    font-size: 14px;
    color: #667eea;
    font-weight: 500;
    margin: 0 0 12px 0;
  }

  .influencer-bio {
    font-size: 13px;
    color: #718096;
    line-height: 1.4;
    margin: 0 0 16px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  @media (max-width: 768px) {
    .influencer-name {
      font-size: 16px;
    }

    .influencer-username {
      font-size: 13px;
    }

    .influencer-bio {
      font-size: 12px;
    }
  }
}

.influencer-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;

  .stat-item {
    text-align: center;

    .stat-number {
      display: block;
      font-size: 16px;
      font-weight: 700;
      color: #2d3748;
      line-height: 1;
    }

    .stat-label {
      font-size: 11px;
      color: #718096;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  @media (max-width: 768px) {
    .stat-item .stat-number {
      font-size: 14px;
    }

    .stat-item .stat-label {
      font-size: 10px;
    }
  }
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;

  .metric-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #718096;

    i {
      color: #667eea;
      font-size: 11px;
      width: 12px;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 8px;

  .follow-btn {
    flex: 1;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    i {
      font-size: 12px;
    }
  }

  .share-btn {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #667eea;

    &:hover {
      background: rgba(102, 126, 234, 0.2);
      transform: scale(1.1);
    }

    i {
      font-size: 14px;
    }
  }
}

// Loading States
.loading-container {
  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 16px;
    }
  }

  .influencer-skeleton {
    background: white;
    border-radius: 16px;
    padding: 20px;
    animation: pulse 1.5s ease-in-out infinite;

    .skeleton-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin: 0 auto 16px;
    }

    .skeleton-content {
      .skeleton-line {
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 6px;
        margin-bottom: 8px;

        &.short {
          width: 60%;
          margin: 0 auto 8px;
        }
      }
    }
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

// Error and Empty States
.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  .error-content,
  .empty-content {
    text-align: center;
    max-width: 400px;

    i {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 16px;
    }

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: white;
      margin-bottom: 8px;
    }

    p {
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 24px;
    }

    .retry-btn,
    .browse-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 20px;
      padding: 12px 24px;
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
      }
    }
  }
}

// Load More
.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;

  .load-more-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    padding: 12px 32px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    i {
      margin-right: 8px;
    }
  }
}
