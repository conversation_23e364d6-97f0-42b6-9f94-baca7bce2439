{"ast": null, "code": "import { ConnectableObservable, isObservable, of, Subject } from 'rxjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable } from '@angular/core';\nclass DataSource {}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n  // Check if the value is a DataSource by observing if it has a connect function. Cannot\n  // be checked as an `instanceof DataSource` since people could create their own sources\n  // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n  // here, because of some internal apps.\n  return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n  constructor(_data) {\n    super();\n    this._data = _data;\n  }\n  connect() {\n    return isObservable(this._data) ? this._data : of(this._data);\n  }\n  disconnect() {}\n}\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nvar _ViewRepeaterOperation;\n(function (_ViewRepeaterOperation) {\n  /** The content of an existing view was replaced with another item. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REPLACED\"] = 0] = \"REPLACED\";\n  /** A new view was created with `createEmbeddedView`. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"INSERTED\"] = 1] = \"INSERTED\";\n  /** The position of a view changed, but the content remains the same. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"MOVED\"] = 2] = \"MOVED\";\n  /** A view was detached from the view container. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REMOVED\"] = 3] = \"REMOVED\";\n})(_ViewRepeaterOperation || (_ViewRepeaterOperation = {}));\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n        operation = _ViewRepeaterOperation.INSERTED;\n      } else if (currentIndex == null) {\n        viewContainerRef.remove(adjustedPreviousIndex);\n        operation = _ViewRepeaterOperation.REMOVED;\n      } else {\n        view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        operation = _ViewRepeaterOperation.MOVED;\n      }\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {}\n}\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n  constructor() {\n    /**\n     * The size of the cache used to store unused views.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n     */\n    this.viewCacheSize = 20;\n    /**\n     * View cache that stores embedded view instances that have been previously stamped out,\n     * but don't are not currently rendered. The view repeater will reuse these views rather than\n     * creating brand new ones.\n     *\n     * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n     */\n    this._viewCache = [];\n  }\n  /** Apply changes to the DOM. */\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        // Item added.\n        const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n      } else if (currentIndex == null) {\n        // Item removed.\n        this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n        operation = _ViewRepeaterOperation.REMOVED;\n      } else {\n        // Item moved.\n        view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = _ViewRepeaterOperation.MOVED;\n      }\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n    this._viewCache = [];\n  }\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n  _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n    const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n  }\n  /** Detaches the view at the given index and inserts into the view cache. */\n  _detachAndCacheView(index, viewContainerRef) {\n    const detachedView = viewContainerRef.detach(index);\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n  /** Moves view at the previous index to the current index. */\n  _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n    const view = viewContainerRef.get(adjustedPreviousIndex);\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n  _maybeCacheView(view, viewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view);\n      // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n  /** Inserts a recycled view from the cache at the given index. */\n  _insertViewFromCache(index, viewContainerRef) {\n    const cachedView = this._viewCache.pop();\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n    return cachedView || null;\n  }\n}\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n  /** Selected values. */\n  get selected() {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n    return this._selected;\n  }\n  constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n    this._multiple = _multiple;\n    this._emitChanges = _emitChanges;\n    this.compareWith = compareWith;\n    /** Currently-selected values. */\n    this._selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    this._deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    this._selectedToEmit = [];\n    /** Event emitted when the value has changed. */\n    this.changed = new Subject();\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n  /**\n   * Selects a value or an array of values.\n   * @param values The values to select\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  select(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Deselects a value or an array of values.\n   * @param values The values to deselect\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  deselect(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Sets the selected values\n   * @param values The new selected values\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  setSelection(...values) {\n    this._verifyValueAssignment(values);\n    const oldValues = this.selected;\n    const newSelectedSet = new Set(values);\n    values.forEach(value => this._markSelected(value));\n    oldValues.filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet))).forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Toggles a value between selected and deselected.\n   * @param value The value to toggle\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  toggle(value) {\n    return this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n  /**\n   * Clears all of the selected values.\n   * @param flushEvent Whether to flush the changes in an event.\n   *   If false, the changes to the selection will be flushed along with the next event.\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  clear(flushEvent = true) {\n    this._unmarkAll();\n    const changed = this._hasQueuedChanges();\n    if (flushEvent) {\n      this._emitChangeEvent();\n    }\n    return changed;\n  }\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value) {\n    return this._selection.has(this._getConcreteValue(value));\n  }\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty() {\n    return this._selection.size === 0;\n  }\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue() {\n    return !this.isEmpty();\n  }\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate) {\n    if (this._multiple && this.selected) {\n      this._selected.sort(predicate);\n    }\n  }\n  /**\n   * Gets whether multiple values can be selected.\n   */\n  isMultipleSelection() {\n    return this._multiple;\n  }\n  /** Emits a change event and clears the records of selected and deselected values. */\n  _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit\n      });\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n  /** Selects a value. */\n  _markSelected(value) {\n    value = this._getConcreteValue(value);\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n      if (!this.isSelected(value)) {\n        this._selection.add(value);\n      }\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n  /** Deselects a value. */\n  _unmarkSelected(value) {\n    value = this._getConcreteValue(value);\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n  /** Clears out the selected values. */\n  _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  _verifyValueAssignment(values) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n  /** Whether there are queued up change to be emitted. */\n  _hasQueuedChanges() {\n    return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n  }\n  /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n  _getConcreteValue(inputValue, selection) {\n    if (!this.compareWith) {\n      return inputValue;\n    } else {\n      selection = selection ?? this._selection;\n      for (let selectedValue of selection) {\n        if (this.compareWith(inputValue, selectedValue)) {\n          return selectedValue;\n        }\n      }\n      return inputValue;\n    }\n  }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  constructor() {\n    this._listeners = [];\n  }\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n  static {\n    this.ɵfac = function UniqueSelectionDispatcher_Factory(t) {\n      return new (t || UniqueSelectionDispatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UniqueSelectionDispatcher,\n      factory: UniqueSelectionDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrayDataSource, DataSource, SelectionModel, UniqueSelectionDispatcher, _DisposeViewRepeaterStrategy, _RecycleViewRepeaterStrategy, _VIEW_REPEATER_STRATEGY, _ViewRepeaterOperation, getMultipleValuesInSingleSelectionError, isDataSource };", "map": {"version": 3, "names": ["ConnectableObservable", "isObservable", "of", "Subject", "i0", "InjectionToken", "Injectable", "DataSource", "isDataSource", "value", "connect", "ArrayDataSource", "constructor", "_data", "disconnect", "_ViewRepeaterOperation", "_VIEW_REPEATER_STRATEGY", "_DisposeViewRepeaterStrategy", "applyChanges", "changes", "viewContainerRef", "itemContextFactory", "itemValueResolver", "itemViewChanged", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "view", "operation", "previousIndex", "insertContext", "createEmbeddedView", "templateRef", "context", "index", "INSERTED", "remove", "REMOVED", "get", "move", "MOVED", "detach", "_RecycleViewRepeaterStrategy", "viewCacheSize", "_viewCache", "viewArgsFactory", "_insertView", "REPLACED", "_detachAndCacheView", "_moveView", "destroy", "cachedView", "_insertViewFromCache", "$implicit", "undefined", "viewArgs", "detached<PERSON>iew", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "push", "indexOf", "pop", "insert", "SelectionModel", "selected", "_selected", "Array", "from", "_selection", "values", "_multiple", "initiallySelectedValues", "_emitChanges", "compareWith", "Set", "_deselectedToEmit", "_selectedToEmit", "changed", "for<PERSON>ach", "_markSelected", "select", "_verifyValueAssignment", "_hasQueuedChanges", "_emitChangeEvent", "deselect", "_unmarkSelected", "setSelection", "oldValues", "newSelectedSet", "filter", "has", "_getConcreteValue", "toggle", "isSelected", "clear", "flushEvent", "_unmarkAll", "isEmpty", "size", "hasValue", "sort", "predicate", "isMultipleSelection", "next", "source", "added", "removed", "add", "delete", "ngDevMode", "getMultipleValuesInSingleSelectionError", "inputValue", "selection", "selected<PERSON><PERSON><PERSON>", "Error", "UniqueSelectionDispatcher", "_listeners", "notify", "id", "name", "listener", "listen", "registered", "ngOnDestroy", "ɵfac", "UniqueSelectionDispatcher_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args"], "sources": ["E:/Fahion/DFashion/DFashion/frontend/node_modules/@angular/cdk/fesm2022/collections.mjs"], "sourcesContent": ["import { ConnectableObservable, isObservable, of, Subject } from 'rxjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable } from '@angular/core';\n\nclass DataSource {\n}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n    // Check if the value is a DataSource by observing if it has a connect function. Cannot\n    // be checked as an `instanceof DataSource` since people could create their own sources\n    // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n    // here, because of some internal apps.\n    return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n    constructor(_data) {\n        super();\n        this._data = _data;\n    }\n    connect() {\n        return isObservable(this._data) ? this._data : of(this._data);\n    }\n    disconnect() { }\n}\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nvar _ViewRepeaterOperation;\n(function (_ViewRepeaterOperation) {\n    /** The content of an existing view was replaced with another item. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"REPLACED\"] = 0] = \"REPLACED\";\n    /** A new view was created with `createEmbeddedView`. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"INSERTED\"] = 1] = \"INSERTED\";\n    /** The position of a view changed, but the content remains the same. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"MOVED\"] = 2] = \"MOVED\";\n    /** A view was detached from the view container. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"REMOVED\"] = 3] = \"REMOVED\";\n})(_ViewRepeaterOperation || (_ViewRepeaterOperation = {}));\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n                operation = _ViewRepeaterOperation.INSERTED;\n            }\n            else if (currentIndex == null) {\n                viewContainerRef.remove(adjustedPreviousIndex);\n                operation = _ViewRepeaterOperation.REMOVED;\n            }\n            else {\n                view = viewContainerRef.get(adjustedPreviousIndex);\n                viewContainerRef.move(view, currentIndex);\n                operation = _ViewRepeaterOperation.MOVED;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() { }\n}\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n    constructor() {\n        /**\n         * The size of the cache used to store unused views.\n         * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n         */\n        this.viewCacheSize = 20;\n        /**\n         * View cache that stores embedded view instances that have been previously stamped out,\n         * but don't are not currently rendered. The view repeater will reuse these views rather than\n         * creating brand new ones.\n         *\n         * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n         */\n        this._viewCache = [];\n    }\n    /** Apply changes to the DOM. */\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        // Rearrange the views to put them in the right location.\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                // Item added.\n                const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n            }\n            else if (currentIndex == null) {\n                // Item removed.\n                this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n                operation = _ViewRepeaterOperation.REMOVED;\n            }\n            else {\n                // Item moved.\n                view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = _ViewRepeaterOperation.MOVED;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() {\n        for (const view of this._viewCache) {\n            view.destroy();\n        }\n        this._viewCache = [];\n    }\n    /**\n     * Inserts a view for a new item, either from the cache or by creating a new\n     * one. Returns `undefined` if the item was inserted into a cached view.\n     */\n    _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n        const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n        if (cachedView) {\n            cachedView.context.$implicit = value;\n            return undefined;\n        }\n        const viewArgs = viewArgsFactory();\n        return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n    }\n    /** Detaches the view at the given index and inserts into the view cache. */\n    _detachAndCacheView(index, viewContainerRef) {\n        const detachedView = viewContainerRef.detach(index);\n        this._maybeCacheView(detachedView, viewContainerRef);\n    }\n    /** Moves view at the previous index to the current index. */\n    _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n        const view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        view.context.$implicit = value;\n        return view;\n    }\n    /**\n     * Cache the given detached view. If the cache is full, the view will be\n     * destroyed.\n     */\n    _maybeCacheView(view, viewContainerRef) {\n        if (this._viewCache.length < this.viewCacheSize) {\n            this._viewCache.push(view);\n        }\n        else {\n            const index = viewContainerRef.indexOf(view);\n            // The host component could remove views from the container outside of\n            // the view repeater. It's unlikely this will occur, but just in case,\n            // destroy the view on its own, otherwise destroy it through the\n            // container to ensure that all the references are removed.\n            if (index === -1) {\n                view.destroy();\n            }\n            else {\n                viewContainerRef.remove(index);\n            }\n        }\n    }\n    /** Inserts a recycled view from the cache at the given index. */\n    _insertViewFromCache(index, viewContainerRef) {\n        const cachedView = this._viewCache.pop();\n        if (cachedView) {\n            viewContainerRef.insert(cachedView, index);\n        }\n        return cachedView || null;\n    }\n}\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        /** Currently-selected values. */\n        this._selection = new Set();\n        /** Keeps track of the deselected options that haven't been emitted by the change event. */\n        this._deselectedToEmit = [];\n        /** Keeps track of the selected options that haven't been emitted by the change event. */\n        this._selectedToEmit = [];\n        /** Event emitted when the value has changed. */\n        this.changed = new Subject();\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values);\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue, selection) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            selection = selection ?? this._selection;\n            for (let selectedValue of selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n    constructor() {\n        this._listeners = [];\n    }\n    /**\n     * Notify other items that selection for the given name has been set.\n     * @param id ID of the item.\n     * @param name Name of the item.\n     */\n    notify(id, name) {\n        for (let listener of this._listeners) {\n            listener(id, name);\n        }\n    }\n    /**\n     * Listen for future changes to item selection.\n     * @return Function used to deregister listener\n     */\n    listen(listener) {\n        this._listeners.push(listener);\n        return () => {\n            this._listeners = this._listeners.filter((registered) => {\n                return listener !== registered;\n            });\n        };\n    }\n    ngOnDestroy() {\n        this._listeners = [];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: UniqueSelectionDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: UniqueSelectionDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: UniqueSelectionDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrayDataSource, DataSource, SelectionModel, UniqueSelectionDispatcher, _DisposeViewRepeaterStrategy, _RecycleViewRepeaterStrategy, _VIEW_REPEATER_STRATEGY, _ViewRepeaterOperation, getMultipleValuesInSingleSelectionError, isDataSource };\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,YAAY,EAAEC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AACvE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,QAAQ,eAAe;AAE1D,MAAMC,UAAU,CAAC;AAEjB;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB;EACA;EACA;EACA;EACA,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACC,OAAO,KAAK,UAAU,IAAI,EAAED,KAAK,YAAYT,qBAAqB,CAAC;AACpG;;AAEA;AACA,MAAMW,eAAe,SAASJ,UAAU,CAAC;EACrCK,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAH,OAAOA,CAAA,EAAG;IACN,OAAOT,YAAY,CAAC,IAAI,CAACY,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,GAAGX,EAAE,CAAC,IAAI,CAACW,KAAK,CAAC;EACjE;EACAC,UAAUA,CAAA,EAAG,CAAE;AACnB;;AAEA;AACA,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/B;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3E;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3E;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACrE;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;AAC7E,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,IAAIX,cAAc,CAAC,eAAe,CAAC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,4BAA4B,CAAC;EAC/BC,YAAYA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5FJ,OAAO,CAACK,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B,MAAMC,aAAa,GAAGV,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QACrFC,IAAI,GAAGR,gBAAgB,CAACY,kBAAkB,CAACD,aAAa,CAACE,WAAW,EAAEF,aAAa,CAACG,OAAO,EAAEH,aAAa,CAACI,KAAK,CAAC;QACjHN,SAAS,GAAGd,sBAAsB,CAACqB,QAAQ;MAC/C,CAAC,MACI,IAAIT,YAAY,IAAI,IAAI,EAAE;QAC3BP,gBAAgB,CAACiB,MAAM,CAACX,qBAAqB,CAAC;QAC9CG,SAAS,GAAGd,sBAAsB,CAACuB,OAAO;MAC9C,CAAC,MACI;QACDV,IAAI,GAAGR,gBAAgB,CAACmB,GAAG,CAACb,qBAAqB,CAAC;QAClDN,gBAAgB,CAACoB,IAAI,CAACZ,IAAI,EAAED,YAAY,CAAC;QACzCE,SAAS,GAAGd,sBAAsB,CAAC0B,KAAK;MAC5C;MACA,IAAIlB,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,EAAEM,OAAO;UACtBL,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAiB,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/B/B,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACgC,aAAa,GAAG,EAAE;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,EAAE;EACxB;EACA;EACA3B,YAAYA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5F;IACAJ,OAAO,CAACK,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B;QACA,MAAMgB,eAAe,GAAGA,CAAA,KAAMzB,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QAC7FC,IAAI,GAAG,IAAI,CAACmB,WAAW,CAACD,eAAe,EAAEnB,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACnGI,SAAS,GAAGD,IAAI,GAAGb,sBAAsB,CAACqB,QAAQ,GAAGrB,sBAAsB,CAACiC,QAAQ;MACxF,CAAC,MACI,IAAIrB,YAAY,IAAI,IAAI,EAAE;QAC3B;QACA,IAAI,CAACsB,mBAAmB,CAACvB,qBAAqB,EAAEN,gBAAgB,CAAC;QACjES,SAAS,GAAGd,sBAAsB,CAACuB,OAAO;MAC9C,CAAC,MACI;QACD;QACAV,IAAI,GAAG,IAAI,CAACsB,SAAS,CAACxB,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACvGI,SAAS,GAAGd,sBAAsB,CAAC0B,KAAK;MAC5C;MACA,IAAIlB,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,EAAEM,OAAO;UACtBL,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAiB,MAAMA,CAAA,EAAG;IACL,KAAK,MAAMd,IAAI,IAAI,IAAI,CAACiB,UAAU,EAAE;MAChCjB,IAAI,CAACuB,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,CAACN,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIE,WAAWA,CAACD,eAAe,EAAEnB,YAAY,EAAEP,gBAAgB,EAAEX,KAAK,EAAE;IAChE,MAAM2C,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAAC1B,YAAY,EAAEP,gBAAgB,CAAC;IAC5E,IAAIgC,UAAU,EAAE;MACZA,UAAU,CAAClB,OAAO,CAACoB,SAAS,GAAG7C,KAAK;MACpC,OAAO8C,SAAS;IACpB;IACA,MAAMC,QAAQ,GAAGV,eAAe,CAAC,CAAC;IAClC,OAAO1B,gBAAgB,CAACY,kBAAkB,CAACwB,QAAQ,CAACvB,WAAW,EAAEuB,QAAQ,CAACtB,OAAO,EAAEsB,QAAQ,CAACrB,KAAK,CAAC;EACtG;EACA;EACAc,mBAAmBA,CAACd,KAAK,EAAEf,gBAAgB,EAAE;IACzC,MAAMqC,YAAY,GAAGrC,gBAAgB,CAACsB,MAAM,CAACP,KAAK,CAAC;IACnD,IAAI,CAACuB,eAAe,CAACD,YAAY,EAAErC,gBAAgB,CAAC;EACxD;EACA;EACA8B,SAASA,CAACxB,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEX,KAAK,EAAE;IACpE,MAAMmB,IAAI,GAAGR,gBAAgB,CAACmB,GAAG,CAACb,qBAAqB,CAAC;IACxDN,gBAAgB,CAACoB,IAAI,CAACZ,IAAI,EAAED,YAAY,CAAC;IACzCC,IAAI,CAACM,OAAO,CAACoB,SAAS,GAAG7C,KAAK;IAC9B,OAAOmB,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI8B,eAAeA,CAAC9B,IAAI,EAAER,gBAAgB,EAAE;IACpC,IAAI,IAAI,CAACyB,UAAU,CAACc,MAAM,GAAG,IAAI,CAACf,aAAa,EAAE;MAC7C,IAAI,CAACC,UAAU,CAACe,IAAI,CAAChC,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,MAAMO,KAAK,GAAGf,gBAAgB,CAACyC,OAAO,CAACjC,IAAI,CAAC;MAC5C;MACA;MACA;MACA;MACA,IAAIO,KAAK,KAAK,CAAC,CAAC,EAAE;QACdP,IAAI,CAACuB,OAAO,CAAC,CAAC;MAClB,CAAC,MACI;QACD/B,gBAAgB,CAACiB,MAAM,CAACF,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;EACAkB,oBAAoBA,CAAClB,KAAK,EAAEf,gBAAgB,EAAE;IAC1C,MAAMgC,UAAU,GAAG,IAAI,CAACP,UAAU,CAACiB,GAAG,CAAC,CAAC;IACxC,IAAIV,UAAU,EAAE;MACZhC,gBAAgB,CAAC2C,MAAM,CAACX,UAAU,EAAEjB,KAAK,CAAC;IAC9C;IACA,OAAOiB,UAAU,IAAI,IAAI;EAC7B;AACJ;;AAEA;AACA;AACA;AACA,MAAMY,cAAc,CAAC;EACjB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC;IACzD;IACA,OAAO,IAAI,CAACJ,SAAS;EACzB;EACAtD,WAAWA,CAAC2D,SAAS,GAAG,KAAK,EAAEC,uBAAuB,EAAEC,YAAY,GAAG,IAAI,EAAEC,WAAW,EAAE;IACtF,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACL,UAAU,GAAG,IAAIM,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;IACA,IAAI,CAACC,OAAO,GAAG,IAAI3E,OAAO,CAAC,CAAC;IAC5B,IAAIqE,uBAAuB,IAAIA,uBAAuB,CAACb,MAAM,EAAE;MAC3D,IAAIY,SAAS,EAAE;QACXC,uBAAuB,CAACO,OAAO,CAACtE,KAAK,IAAI,IAAI,CAACuE,aAAa,CAACvE,KAAK,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAACuE,aAAa,CAACR,uBAAuB,CAAC,CAAC,CAAC,CAAC;MAClD;MACA;MACA,IAAI,CAACK,eAAe,CAAClB,MAAM,GAAG,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsB,MAAMA,CAAC,GAAGX,MAAM,EAAE;IACd,IAAI,CAACY,sBAAsB,CAACZ,MAAM,CAAC;IACnCA,MAAM,CAACS,OAAO,CAACtE,KAAK,IAAI,IAAI,CAACuE,aAAa,CAACvE,KAAK,CAAC,CAAC;IAClD,MAAMqE,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAON,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIO,QAAQA,CAAC,GAAGf,MAAM,EAAE;IAChB,IAAI,CAACY,sBAAsB,CAACZ,MAAM,CAAC;IACnCA,MAAM,CAACS,OAAO,CAACtE,KAAK,IAAI,IAAI,CAAC6E,eAAe,CAAC7E,KAAK,CAAC,CAAC;IACpD,MAAMqE,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAON,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIS,YAAYA,CAAC,GAAGjB,MAAM,EAAE;IACpB,IAAI,CAACY,sBAAsB,CAACZ,MAAM,CAAC;IACnC,MAAMkB,SAAS,GAAG,IAAI,CAACvB,QAAQ;IAC/B,MAAMwB,cAAc,GAAG,IAAId,GAAG,CAACL,MAAM,CAAC;IACtCA,MAAM,CAACS,OAAO,CAACtE,KAAK,IAAI,IAAI,CAACuE,aAAa,CAACvE,KAAK,CAAC,CAAC;IAClD+E,SAAS,CACJE,MAAM,CAACjF,KAAK,IAAI,CAACgF,cAAc,CAACE,GAAG,CAAC,IAAI,CAACC,iBAAiB,CAACnF,KAAK,EAAEgF,cAAc,CAAC,CAAC,CAAC,CACnFV,OAAO,CAACtE,KAAK,IAAI,IAAI,CAAC6E,eAAe,CAAC7E,KAAK,CAAC,CAAC;IAClD,MAAMqE,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAON,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIe,MAAMA,CAACpF,KAAK,EAAE;IACV,OAAO,IAAI,CAACqF,UAAU,CAACrF,KAAK,CAAC,GAAG,IAAI,CAAC4E,QAAQ,CAAC5E,KAAK,CAAC,GAAG,IAAI,CAACwE,MAAM,CAACxE,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIsF,KAAKA,CAACC,UAAU,GAAG,IAAI,EAAE;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,MAAMnB,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;IACxC,IAAIa,UAAU,EAAE;MACZ,IAAI,CAACZ,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAON,OAAO;EAClB;EACA;AACJ;AACA;EACIgB,UAAUA,CAACrF,KAAK,EAAE;IACd,OAAO,IAAI,CAAC4D,UAAU,CAACsB,GAAG,CAAC,IAAI,CAACC,iBAAiB,CAACnF,KAAK,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;EACIyF,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC7B,UAAU,CAAC8B,IAAI,KAAK,CAAC;EACrC;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;EACIG,IAAIA,CAACC,SAAS,EAAE;IACZ,IAAI,IAAI,CAAC/B,SAAS,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjC,IAAI,CAACC,SAAS,CAACmC,IAAI,CAACC,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;EACIC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAChC,SAAS;EACzB;EACA;EACAa,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAAClB,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACW,eAAe,CAAClB,MAAM,IAAI,IAAI,CAACiB,iBAAiB,CAACjB,MAAM,EAAE;MAC9D,IAAI,CAACmB,OAAO,CAAC0B,IAAI,CAAC;QACdC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,IAAI,CAAC7B,eAAe;QAC3B8B,OAAO,EAAE,IAAI,CAAC/B;MAClB,CAAC,CAAC;MACF,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,eAAe,GAAG,EAAE;IAC7B;EACJ;EACA;EACAG,aAAaA,CAACvE,KAAK,EAAE;IACjBA,KAAK,GAAG,IAAI,CAACmF,iBAAiB,CAACnF,KAAK,CAAC;IACrC,IAAI,CAAC,IAAI,CAACqF,UAAU,CAACrF,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC,IAAI,CAAC8D,SAAS,EAAE;QACjB,IAAI,CAAC0B,UAAU,CAAC,CAAC;MACrB;MACA,IAAI,CAAC,IAAI,CAACH,UAAU,CAACrF,KAAK,CAAC,EAAE;QACzB,IAAI,CAAC4D,UAAU,CAACuC,GAAG,CAACnG,KAAK,CAAC;MAC9B;MACA,IAAI,IAAI,CAACgE,YAAY,EAAE;QACnB,IAAI,CAACI,eAAe,CAACjB,IAAI,CAACnD,KAAK,CAAC;MACpC;IACJ;EACJ;EACA;EACA6E,eAAeA,CAAC7E,KAAK,EAAE;IACnBA,KAAK,GAAG,IAAI,CAACmF,iBAAiB,CAACnF,KAAK,CAAC;IACrC,IAAI,IAAI,CAACqF,UAAU,CAACrF,KAAK,CAAC,EAAE;MACxB,IAAI,CAAC4D,UAAU,CAACwC,MAAM,CAACpG,KAAK,CAAC;MAC7B,IAAI,IAAI,CAACgE,YAAY,EAAE;QACnB,IAAI,CAACG,iBAAiB,CAAChB,IAAI,CAACnD,KAAK,CAAC;MACtC;IACJ;EACJ;EACA;EACAwF,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MACjB,IAAI,CAAC7B,UAAU,CAACU,OAAO,CAACtE,KAAK,IAAI,IAAI,CAAC6E,eAAe,CAAC7E,KAAK,CAAC,CAAC;IACjE;EACJ;EACA;AACJ;AACA;AACA;EACIyE,sBAAsBA,CAACZ,MAAM,EAAE;IAC3B,IAAIA,MAAM,CAACX,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACY,SAAS,KAAK,OAAOuC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzF,MAAMC,uCAAuC,CAAC,CAAC;IACnD;EACJ;EACA;EACA5B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAACP,iBAAiB,CAACjB,MAAM,IAAI,IAAI,CAACkB,eAAe,CAAClB,MAAM,CAAC;EAC3E;EACA;EACAiC,iBAAiBA,CAACoB,UAAU,EAAEC,SAAS,EAAE;IACrC,IAAI,CAAC,IAAI,CAACvC,WAAW,EAAE;MACnB,OAAOsC,UAAU;IACrB,CAAC,MACI;MACDC,SAAS,GAAGA,SAAS,IAAI,IAAI,CAAC5C,UAAU;MACxC,KAAK,IAAI6C,aAAa,IAAID,SAAS,EAAE;QACjC,IAAI,IAAI,CAACvC,WAAW,CAACsC,UAAU,EAAEE,aAAa,CAAC,EAAE;UAC7C,OAAOA,aAAa;QACxB;MACJ;MACA,OAAOF,UAAU;IACrB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,uCAAuCA,CAAA,EAAG;EAC/C,OAAOI,KAAK,CAAC,yEAAyE,CAAC;AAC3F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BxG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyG,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACb,KAAK,IAAIC,QAAQ,IAAI,IAAI,CAACJ,UAAU,EAAE;MAClCI,QAAQ,CAACF,EAAE,EAAEC,IAAI,CAAC;IACtB;EACJ;EACA;AACJ;AACA;AACA;EACIE,MAAMA,CAACD,QAAQ,EAAE;IACb,IAAI,CAACJ,UAAU,CAACzD,IAAI,CAAC6D,QAAQ,CAAC;IAC9B,OAAO,MAAM;MACT,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC3B,MAAM,CAAEiC,UAAU,IAAK;QACrD,OAAOF,QAAQ,KAAKE,UAAU;MAClC,CAAC,CAAC;IACN,CAAC;EACL;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACP,UAAU,GAAG,EAAE;EACxB;EACA;IAAS,IAAI,CAACQ,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFX,yBAAyB;IAAA,CAAoD;EAAE;EACzL;IAAS,IAAI,CAACY,KAAK,kBAD6E5H,EAAE,CAAA6H,kBAAA;MAAAC,KAAA,EACYd,yBAAyB;MAAAe,OAAA,EAAzBf,yBAAyB,CAAAS,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AACpK;AACA;EAAA,QAAAtB,SAAA,oBAAAA,SAAA,KAHoG1G,EAAE,CAAAiI,iBAAA,CAGXjB,yBAAyB,EAAc,CAAC;IACvHkB,IAAI,EAAEhI,UAAU;IAChBiI,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASzH,eAAe,EAAEJ,UAAU,EAAEyD,cAAc,EAAEoD,yBAAyB,EAAEnG,4BAA4B,EAAE0B,4BAA4B,EAAE3B,uBAAuB,EAAED,sBAAsB,EAAEgG,uCAAuC,EAAEvG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}