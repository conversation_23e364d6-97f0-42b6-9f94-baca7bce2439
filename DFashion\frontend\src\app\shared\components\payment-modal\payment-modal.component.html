<!-- Payment Modal Overlay -->
<div class="payment-modal-overlay" *ngIf="isVisible" (click)="closeModal()">
  <div class="payment-modal" (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="modal-header">
      <h2>Choose Payment Method</h2>
      <button class="close-btn" (click)="closeModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Order Summary -->
    <div class="order-summary" *ngIf="paymentData">
      <h3>Order Summary</h3>
      <div class="amount-display">
        <span class="label">Total Amount:</span>
        <span class="amount">{{ formatAmount(paymentData.amount) }}</span>
      </div>
    </div>

    <!-- Payment Methods -->
    <div class="payment-methods">
      <h3>Select Payment Method</h3>
      
      <div class="payment-options">
        <div 
          *ngFor="let method of paymentMethods" 
          class="payment-option"
          [class.selected]="selectedPaymentMethod === method.id"
          [class.disabled]="!method.enabled"
          (click)="method.enabled && selectPaymentMethod(method.id)"
        >
          <div class="payment-icon" [style.color]="getPaymentMethodColor(method.id)">
            <i [class]="getPaymentMethodIcon(method)"></i>
          </div>
          
          <div class="payment-info">
            <h4>{{ method.name }}</h4>
            <p>{{ method.description }}</p>
          </div>
          
          <div class="payment-radio">
            <input 
              type="radio" 
              [id]="method.id" 
              [value]="method.id"
              [(ngModel)]="selectedPaymentMethod"
              [disabled]="!method.enabled"
            >
            <label [for]="method.id"></label>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Details for Selected Method -->
    <div class="payment-details" *ngIf="selectedPaymentMethod">
      <div class="selected-method-info">
        <h4>Payment Details</h4>
        
        <!-- Credit/Debit Card Info -->
        <div *ngIf="selectedPaymentMethod === 'razorpay'" class="method-details">
          <p><i class="fas fa-shield-alt"></i> Secure payment powered by Razorpay</p>
          <p><i class="fas fa-lock"></i> Your card details are encrypted and secure</p>
          <p><i class="fas fa-credit-card"></i> Supports all major cards and UPI</p>
        </div>

        <!-- UPI Info -->
        <div *ngIf="selectedPaymentMethod === 'upi'" class="method-details">
          <p><i class="fas fa-mobile-alt"></i> Pay using any UPI app</p>
          <p><i class="fas fa-qrcode"></i> Scan QR code or enter UPI ID</p>
          <p><i class="fas fa-bolt"></i> Instant payment confirmation</p>
        </div>

        <!-- Net Banking Info -->
        <div *ngIf="selectedPaymentMethod === 'netbanking'" class="method-details">
          <p><i class="fas fa-university"></i> Pay directly from your bank account</p>
          <p><i class="fas fa-shield-alt"></i> Bank-grade security</p>
          <p><i class="fas fa-clock"></i> Real-time payment processing</p>
        </div>

        <!-- Wallet Info -->
        <div *ngIf="selectedPaymentMethod === 'wallet'" class="method-details">
          <p><i class="fas fa-wallet"></i> Pay using digital wallets</p>
          <p><i class="fas fa-zap"></i> Quick and convenient</p>
          <p><i class="fas fa-gift"></i> Earn cashback and rewards</p>
        </div>

        <!-- COD Info -->
        <div *ngIf="selectedPaymentMethod === 'cod'" class="method-details">
          <p><i class="fas fa-money-bill-wave"></i> Pay when you receive your order</p>
          <p><i class="fas fa-truck"></i> Cash payment to delivery person</p>
          <p><i class="fas fa-info-circle"></i> Additional ₹50 COD charges may apply</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="modal-actions">
      <button 
        class="cancel-btn" 
        (click)="closeModal()"
        [disabled]="isProcessing"
      >
        Cancel
      </button>
      
      <button 
        class="proceed-btn" 
        (click)="proceedWithPayment()"
        [disabled]="!selectedPaymentMethod || isProcessing"
        [class.processing]="isProcessing"
      >
        <span *ngIf="!isProcessing">
          <i class="fas fa-lock"></i>
          {{ selectedPaymentMethod === 'cod' ? 'Place Order' : 'Proceed to Pay' }}
        </span>
        <span *ngIf="isProcessing">
          <i class="fas fa-spinner fa-spin"></i>
          Processing...
        </span>
      </button>
    </div>

    <!-- Security Notice -->
    <div class="security-notice">
      <i class="fas fa-shield-alt"></i>
      <span>Your payment information is secure and encrypted</span>
    </div>
  </div>
</div>

<!-- Processing Overlay -->
<div class="processing-overlay" *ngIf="isProcessing">
  <div class="processing-content">
    <div class="spinner"></div>
    <h3>Processing Payment...</h3>
    <p>Please don't close this window</p>
  </div>
</div>
