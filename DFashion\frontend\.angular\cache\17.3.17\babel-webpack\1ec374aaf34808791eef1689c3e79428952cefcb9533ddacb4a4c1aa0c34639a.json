{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let OrderService = /*#__PURE__*/(() => {\n  class OrderService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/orders`;\n      this.ordersSubject = new BehaviorSubject([]);\n      this.orders$ = this.ordersSubject.asObservable();\n    }\n    // Get all orders with filters\n    getOrders(filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(this.apiUrl, {\n        params\n      });\n    }\n    // Get order by ID\n    getOrderById(id) {\n      return this.http.get(`${this.apiUrl}/${id}`);\n    }\n    // Get order by order number\n    getOrderByNumber(orderNumber) {\n      return this.http.get(`${this.apiUrl}/number/${orderNumber}`);\n    }\n    // Create new order\n    createOrder(order) {\n      return this.http.post(this.apiUrl, order);\n    }\n    // Update order\n    updateOrder(id, order) {\n      return this.http.put(`${this.apiUrl}/${id}`, order);\n    }\n    // Update order status\n    updateOrderStatus(id, status, notes) {\n      return this.http.patch(`${this.apiUrl}/${id}/status`, {\n        status,\n        notes\n      });\n    }\n    // Update payment status\n    updatePaymentStatus(id, paymentStatus) {\n      return this.http.patch(`${this.apiUrl}/${id}/payment-status`, {\n        paymentStatus\n      });\n    }\n    // Add tracking number\n    addTrackingNumber(id, trackingNumber) {\n      return this.http.patch(`${this.apiUrl}/${id}/tracking`, {\n        trackingNumber\n      });\n    }\n    // Cancel order\n    cancelOrder(id, reason) {\n      return this.http.patch(`${this.apiUrl}/${id}/cancel`, {\n        reason\n      });\n    }\n    // Process refund\n    processRefund(id, amount, reason) {\n      return this.http.post(`${this.apiUrl}/${id}/refund`, {\n        amount,\n        reason\n      });\n    }\n    // Get order statistics\n    getOrderStats(period = '30d') {\n      return this.http.get(`${this.apiUrl}/stats?period=${period}`);\n    }\n    // Get orders by customer\n    getOrdersByCustomer(customerId, filters = {}) {\n      const customerFilters = {\n        ...filters,\n        customer: customerId\n      };\n      return this.getOrders(customerFilters);\n    }\n    // Get orders by vendor\n    getOrdersByVendor(vendorId, filters = {}) {\n      const vendorFilters = {\n        ...filters,\n        vendor: vendorId\n      };\n      return this.getOrders(vendorFilters);\n    }\n    // Search orders\n    searchOrders(query, filters = {}) {\n      const searchFilters = {\n        ...filters,\n        search: query\n      };\n      return this.getOrders(searchFilters);\n    }\n    // Get recent orders\n    getRecentOrders(limit = 10) {\n      return this.http.get(`${this.apiUrl}/recent?limit=${limit}`);\n    }\n    // Export orders\n    exportOrders(filters = {}, format = 'csv') {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      params = params.set('format', format);\n      return this.http.get(`${this.apiUrl}/export`, {\n        params,\n        responseType: 'blob'\n      });\n    }\n    // Generate invoice\n    generateInvoice(orderId) {\n      return this.http.get(`${this.apiUrl}/${orderId}/invoice`, {\n        responseType: 'blob'\n      });\n    }\n    // Send order confirmation email\n    sendOrderConfirmation(orderId) {\n      return this.http.post(`${this.apiUrl}/${orderId}/send-confirmation`, {});\n    }\n    // Send shipping notification\n    sendShippingNotification(orderId) {\n      return this.http.post(`${this.apiUrl}/${orderId}/send-shipping-notification`, {});\n    }\n    // Get order timeline\n    getOrderTimeline(orderId) {\n      return this.http.get(`${this.apiUrl}/${orderId}/timeline`);\n    }\n    // Bulk operations\n    bulkUpdateOrderStatus(orderIds, status) {\n      return this.http.patch(`${this.apiUrl}/bulk-update-status`, {\n        orderIds,\n        status\n      });\n    }\n    bulkExportOrders(orderIds, format = 'csv') {\n      return this.http.post(`${this.apiUrl}/bulk-export`, {\n        orderIds,\n        format\n      }, {\n        responseType: 'blob'\n      });\n    }\n    // Update orders subject\n    updateOrdersSubject(orders) {\n      this.ordersSubject.next(orders);\n    }\n    // Get current orders\n    getCurrentOrders() {\n      return this.ordersSubject.value;\n    }\n    static {\n      this.ɵfac = function OrderService_Factory(t) {\n        return new (t || OrderService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: OrderService,\n        factory: OrderService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return OrderService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}