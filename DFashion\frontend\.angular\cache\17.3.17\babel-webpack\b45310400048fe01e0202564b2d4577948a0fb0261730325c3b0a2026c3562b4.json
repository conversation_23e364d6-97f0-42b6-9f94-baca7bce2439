{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent } from '../../../../shared/components/payment-modal/payment-modal.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"../../../../core/services/button-actions.service\";\nimport * as i5 from \"../../../../core/services/posts.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"div\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading amazing fashion...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_2_article_5_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(post_r2.location);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleProductTags(post_r2));\n    });\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", post_r2.showProductTags);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_13_button_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"img\", 51);\n    i0.ɵɵelementStart(2, \"div\", 52)(3, \"span\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.getTagImageUrl(productTag_r6), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.getTagName(productTag_r6));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.getTagName(productTag_r6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getTagSubtitle(productTag_r6));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_13_button_1_Template_button_click_0_listener() {\n      const productTag_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.onProductTagClick(productTag_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelement(2, \"i\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HomeComponent_div_2_article_5_div_13_button_1_div_3_Template, 7, 4, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const productTag_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(\"tag-type-\" + (productTag_r6.navigationType || \"product\"));\n    i0.ɵɵstyleProp(\"left\", (productTag_r6.position == null ? null : productTag_r6.position.x) || 50, \"%\")(\"top\", (productTag_r6.position == null ? null : productTag_r6.position.y) || 50, \"%\");\n    i0.ɵɵproperty(\"title\", ctx_r3.getTagTooltip(productTag_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"dot-\" + (productTag_r6.navigationType || \"product\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r3.getTagIcon(productTag_r6.navigationType || \"product\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", productTag_r6.showPreview);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, HomeComponent_div_2_article_5_div_13_button_1_Template, 4, 12, \"button\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.formatLikesCount(post_r2.analytics.likes), \" likes\");\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵelement(1, \"img\", 68);\n    i0.ɵɵelementStart(2, \"div\", 69)(3, \"span\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r8.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatPrice(product_r8.price));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵtemplate(2, HomeComponent_div_2_article_5_div_30_div_2_Template, 7, 4, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 60)(4, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.buyNow(post_r2.products[0]));\n    });\n    i0.ɵɵelement(5, \"i\", 62);\n    i0.ɵɵtext(6, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addToWishlist(post_r2.products[0]));\n    });\n    i0.ɵɵelement(8, \"i\", 64);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addToCart(post_r2.products[0]));\n    });\n    i0.ɵɵelement(11, \"i\", 66);\n    i0.ɵɵtext(12, \" Add to Cart \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_31_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"span\", 75);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 76);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.text);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleComments(post_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 72);\n    i0.ɵɵtemplate(4, HomeComponent_div_2_article_5_div_31_div_4_Template, 5, 2, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" View all \", post_r2.comments.length, \" comments \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r2.comments.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"article\", 12)(1, \"header\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"img\", 15);\n    i0.ɵɵelementStart(4, \"div\", 16)(5, \"h3\", 17);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, HomeComponent_div_2_article_5_span_7_Template, 2, 1, \"span\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 19);\n    i0.ɵɵelement(9, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 21);\n    i0.ɵɵelement(11, \"img\", 22);\n    i0.ɵɵtemplate(12, HomeComponent_div_2_article_5_div_12_Template, 3, 2, \"div\", 23)(13, HomeComponent_div_2_article_5_div_13_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 24)(15, \"div\", 25)(16, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_16_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleLike(post_r2));\n    });\n    i0.ɵɵelement(17, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_18_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.focusCommentInput(post_r2));\n    });\n    i0.ɵɵelement(19, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_20_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.sharePost(post_r2));\n    });\n    i0.ɵɵelement(21, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_22_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleSave(post_r2));\n    });\n    i0.ɵɵelement(23, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HomeComponent_div_2_article_5_div_24_Template, 3, 1, \"div\", 32);\n    i0.ɵɵelementStart(25, \"div\", 33)(26, \"span\", 17);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 34);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, HomeComponent_div_2_article_5_div_30_Template, 13, 1, \"div\", 35)(31, HomeComponent_div_2_article_5_div_31_Template, 5, 2, \"div\", 36);\n    i0.ɵɵelementStart(32, \"div\", 37);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 38)(35, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HomeComponent_div_2_article_5_Template_input_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.newComment, $event) || (ctx_r3.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function HomeComponent_div_2_article_5_Template_input_keyup_enter_35_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_36_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵtext(37, \"Post\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", post_r2.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.user.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r2.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.location);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", post_r2.mediaUrl, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0 && post_r2.showProductTags);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r2.isLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"saved\", post_r2.isSaved);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.analytics.likes > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r2.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r2.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.comments && post_r2.comments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getTimeAgo(post_r2.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.newComment || !ctx_r3.newComment.trim());\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"section\", 8);\n    i0.ɵɵelement(3, \"app-view-add-stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"section\", 9);\n    i0.ɵɵtemplate(5, HomeComponent_div_2_article_5_Template, 38, 24, \"article\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"app-sidebar\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.instagramPosts)(\"ngForTrackBy\", ctx_r3.trackByPostId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"currentUser\", ctx_r3.currentUser)(\"featuredProducts\", ctx_r3.featuredProducts)(\"trendingProducts\", ctx_r3.trendingProducts)(\"newArrivals\", ctx_r3.newArrivals)(\"summerCollection\", ctx_r3.summerCollection)(\"categories\", ctx_r3.categories);\n  }\n}\nexport class HomeComponent {\n  constructor(router, productService, authService, buttonActionsService, postsService) {\n    this.router = router;\n    this.productService = productService;\n    this.authService = authService;\n    this.buttonActionsService = buttonActionsService;\n    this.postsService = postsService;\n    this.featuredProducts = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.trendingPosts = [];\n    this.categories = [];\n    this.isLoading = true;\n    this.isAuthenticated = false;\n    // Instagram-style data\n    this.instagramPosts = [];\n    this.summerCollection = [];\n    this.suggestedUsers = [];\n    this.currentUser = null;\n    this.newComment = '';\n    // Payment Modal\n    this.showPaymentModal = false;\n    this.paymentModalData = null;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n  loadInstagramPosts() {\n    // Load real Instagram-style posts from API\n    this.postsService.getPosts(1, 10).subscribe({\n      next: response => {\n        if (response.success) {\n          this.instagramPosts = response.posts;\n          console.log('✅ Instagram posts loaded:', this.instagramPosts.length);\n        } else {\n          this.instagramPosts = [];\n          console.log('⚠️ No Instagram posts available');\n        }\n      },\n      error: error => {\n        console.error('❌ Error loading Instagram posts:', error);\n        this.instagramPosts = [];\n      }\n    });\n  }\n  loadSummerCollection() {\n    // Load summer collection from API using category products\n    this.productService.getCategoryProducts('summer').subscribe({\n      next: response => {\n        this.summerCollection = response.products || [];\n        console.log('✅ Summer collection loaded:', this.summerCollection.length);\n      },\n      error: error => {\n        console.error('❌ Error loading summer collection:', error);\n        this.summerCollection = [];\n      }\n    });\n  }\n  loadSuggestedUsers() {\n    // Load suggested users from API\n    // For now, set empty array until users API is implemented\n    this.suggestedUsers = [];\n    console.log('✅ Suggested users loaded:', this.suggestedUsers.length);\n  }\n  loadHomeData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        // Load all data in parallel\n        const [featured, trending, arrivals] = yield Promise.all([_this.productService.getFeaturedProducts().toPromise(), _this.productService.getTrendingProducts().toPromise(), _this.productService.getNewArrivals().toPromise()]);\n        _this.featuredProducts = (featured?.products || featured?.data || featured || []).slice(0, 8);\n        _this.trendingProducts = (trending?.products || trending?.data || trending || []).slice(0, 8);\n        _this.newArrivals = (arrivals?.products || arrivals?.data || arrivals || []).slice(0, 8);\n        // Log loaded data counts\n        console.log('✅ Featured products loaded:', _this.featuredProducts.length);\n        console.log('✅ Trending products loaded:', _this.trendingProducts.length);\n        console.log('✅ New arrivals loaded:', _this.newArrivals.length);\n        // Load categories\n        _this.loadCategories();\n        // Load trending posts (mock data for now)\n        _this.loadTrendingPosts();\n      } catch (error) {\n        console.error('❌ Error loading home data:', error);\n        // Set empty arrays instead of fallback data\n        _this.featuredProducts = [];\n        _this.trendingProducts = [];\n        _this.newArrivals = [];\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadCategories() {\n    // Load categories from API\n    this.productService.getCategories().subscribe({\n      next: response => {\n        this.categories = response.data || [];\n        console.log('✅ Categories loaded:', this.categories.length);\n      },\n      error: error => {\n        console.error('❌ Error loading categories:', error);\n        this.categories = [];\n      }\n    });\n  }\n  loadTrendingPosts() {\n    // Load trending posts from API\n    this.postsService.getTrendingPosts(6).subscribe({\n      next: response => {\n        if (response.success) {\n          this.trendingPosts = response.posts;\n          console.log('✅ Trending posts loaded:', this.trendingPosts.length);\n        } else {\n          this.trendingPosts = [];\n          console.log('⚠️ No trending posts available');\n        }\n      },\n      error: error => {\n        console.error('❌ Error loading trending posts:', error);\n        this.trendingPosts = [];\n      }\n    });\n  }\n  // Navigation methods\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        category: category.name.toLowerCase()\n      }\n    });\n  }\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n  // Instagram-style interaction methods\n  toggleLike(post) {\n    if (!post._id) return;\n    // Optimistic update\n    const wasLiked = post.isLiked;\n    post.isLiked = !post.isLiked;\n    post.analytics.likes += post.isLiked ? 1 : -1;\n    this.postsService.toggleLike(post._id).subscribe({\n      next: result => {\n        if (result.success && result.likesCount !== undefined) {\n          post.analytics.likes = result.likesCount;\n          console.log('✅ Post like toggled successfully');\n        } else {\n          // Revert on failure\n          post.isLiked = wasLiked;\n          post.analytics.likes += wasLiked ? 1 : -1;\n          console.error('❌ Failed to toggle like:', result.message);\n        }\n      },\n      error: error => {\n        // Revert on error\n        post.isLiked = wasLiked;\n        post.analytics.likes += wasLiked ? 1 : -1;\n        console.error('❌ Error toggling like:', error);\n      }\n    });\n  }\n  toggleSave(post) {\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    // Optimistic update\n    post.isSaved = !post.isSaved;\n    this.buttonActionsService.savePost(postId).subscribe({\n      next: result => {\n        if (!result.success) {\n          // Revert on failure\n          post.isSaved = !post.isSaved;\n          console.error('Failed to save post:', result.message);\n        }\n      },\n      error: error => {\n        // Revert on error\n        post.isSaved = !post.isSaved;\n        console.error('Error saving post:', error);\n      }\n    });\n  }\n  sharePost(post) {\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    this.buttonActionsService.sharePost(postId).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Post shared successfully');\n        } else {\n          console.error('Failed to share post:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error sharing post:', error);\n      }\n    });\n  }\n  focusCommentInput(post) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n  addComment(post) {\n    if (!this.newComment || !this.newComment.trim()) return;\n    if (!post._id) return;\n    const commentText = this.newComment.trim();\n    this.postsService.addComment(post._id, commentText).subscribe({\n      next: result => {\n        if (result.success && result.comment) {\n          // Add comment to local state\n          post.comments.push(result.comment);\n          post.analytics.comments += 1;\n          this.newComment = '';\n          console.log('✅ Comment added successfully');\n        } else {\n          console.error('❌ Failed to add comment:', result.message);\n        }\n      },\n      error: error => {\n        console.error('❌ Error adding comment:', error);\n      }\n    });\n  }\n  showProductDetails(product) {\n    console.log('🛍️ Show product details:', product);\n    if (product._id) {\n      this.router.navigate(['/product', product._id]);\n    } else {\n      // Toggle product preview for posts\n      product.showPreview = !product.showPreview;\n    }\n  }\n  // View full post\n  viewPost(post) {\n    console.log('📱 View post:', post._id);\n    this.router.navigate(['/post', post._id]);\n  }\n  // View user profile\n  viewUserProfile(user) {\n    console.log('👤 View user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n  // Toggle product tags visibility\n  toggleProductTags(post) {\n    post.showProductTags = !post.showProductTags;\n    console.log('🏷️ Product tags toggled:', post.showProductTags);\n  }\n  // E-commerce methods\n  buyNow(product) {\n    console.log('Buy now:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.buyNow({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Buy now successful, redirecting to checkout');\n          // Navigation will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n          // Fallback to payment modal if needed\n          this.showPaymentModalFallback(product);\n        }\n      },\n      error: error => {\n        console.error('Error in buy now:', error);\n        // Fallback to payment modal\n        this.showPaymentModalFallback(product);\n      }\n    });\n  }\n  showPaymentModalFallback(product) {\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: '/home'\n        }\n      });\n      return;\n    }\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + product.price * 0.18\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n    this.showPaymentModal = true;\n  }\n  addToWishlist(product) {\n    console.log('Add to wishlist:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.addToWishlist({\n      productId: productId,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully');\n        } else {\n          console.error('Failed to add to wishlist:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n  addToCart(product) {\n    console.log('Add to cart:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.addToCart({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to cart successfully');\n        } else {\n          console.error('Failed to add to cart:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n  followUser(user) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        collection: 'summer2024'\n      }\n    });\n  }\n  toggleComments(post) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n  // Utility methods\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatLikesCount(likes) {\n    return this.formatNumber(likes);\n  }\n  getTimeAgo(date) {\n    if (!date) return 'Unknown';\n    let dateObj;\n    // Handle different date formats\n    if (typeof date === 'string') {\n      dateObj = new Date(date);\n    } else if (date instanceof Date) {\n      dateObj = date;\n    } else {\n      return 'Unknown';\n    }\n    // Check if date is valid\n    if (isNaN(dateObj.getTime())) {\n      return 'Unknown';\n    }\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n  trackByPostId(index, post) {\n    return post.id;\n  }\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult) {\n    this.showPaymentModal = false;\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n  generateOrderId() {\n    return 'ORD' + Date.now().toString();\n  }\n  // Enhanced product tag methods\n  onProductTagClick(productTag) {\n    if (!productTag) return;\n    const navigationType = productTag.navigationType || 'product';\n    const targetData = productTag.product || productTag.category || productTag.vendor || productTag.brand;\n    switch (navigationType) {\n      case 'product':\n        this.navigateToProduct(targetData);\n        break;\n      case 'category':\n        this.navigateToCategory(targetData);\n        break;\n      case 'vendor':\n        this.navigateToVendor(targetData);\n        break;\n      case 'brand':\n        this.navigateToBrand(targetData);\n        break;\n      default:\n        this.navigateToProduct(targetData);\n    }\n  }\n  navigateToProduct(product) {\n    if (product?._id || product?.id) {\n      const productId = product._id || product.id;\n      this.router.navigate(['/product', productId]);\n    }\n  }\n  navigateToCategory(category) {\n    if (category?.slug || category?.name) {\n      const categorySlug = category.slug || category.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          category: categorySlug\n        }\n      });\n    }\n  }\n  navigateToVendor(vendor) {\n    if (vendor?._id || vendor?.id || vendor?.username) {\n      const vendorId = vendor._id || vendor.id || vendor.username;\n      this.router.navigate(['/vendor', vendorId]);\n    }\n  }\n  navigateToBrand(brand) {\n    if (brand?.slug || brand?.name) {\n      const brandSlug = brand.slug || brand.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          brand: brandSlug\n        }\n      });\n    }\n  }\n  getTagTooltip(productTag) {\n    const type = productTag.navigationType || 'product';\n    const name = this.getTagName(productTag);\n    switch (type) {\n      case 'product':\n        return `View product: ${name}`;\n      case 'category':\n        return `Browse category: ${name}`;\n      case 'vendor':\n        return `Visit vendor: ${name}`;\n      case 'brand':\n        return `Shop brand: ${name}`;\n      default:\n        return `View: ${name}`;\n    }\n  }\n  getTagIcon(navigationType) {\n    switch (navigationType) {\n      case 'product':\n        return 'fas fa-tag';\n      case 'category':\n        return 'fas fa-th-large';\n      case 'vendor':\n        return 'fas fa-store';\n      case 'brand':\n        return 'fas fa-crown';\n      default:\n        return 'fas fa-tag';\n    }\n  }\n  getTagName(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.name || 'Product';\n      case 'category':\n        return productTag.category?.name || 'Category';\n      case 'vendor':\n        return productTag.vendor?.fullName || productTag.vendor?.username || 'Vendor';\n      case 'brand':\n        return productTag.brand?.name || 'Brand';\n      default:\n        return productTag.product?.name || 'Item';\n    }\n  }\n  getTagSubtitle(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.price ? `$${productTag.product.price}` : '';\n      case 'category':\n        return productTag.category?.productCount ? `${productTag.category.productCount} products` : 'Browse category';\n      case 'vendor':\n        return productTag.vendor?.location || 'Visit store';\n      case 'brand':\n        return productTag.brand?.productCount ? `${productTag.brand.productCount} products` : 'Shop brand';\n      default:\n        return '';\n    }\n  }\n  getTagImageUrl(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n      case 'category':\n        return productTag.category?.image || '/assets/images/category-placeholder.jpg';\n      case 'vendor':\n        return productTag.vendor?.avatar || '/assets/images/vendor-placeholder.jpg';\n      case 'brand':\n        return productTag.brand?.logo || '/assets/images/brand-placeholder.jpg';\n      default:\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ButtonActionsService), i0.ɵɵdirectiveInject(i5.PostsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"instagram-home-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"instagram-layout\", 4, \"ngIf\"], [3, \"close\", \"paymentCompleted\", \"isVisible\", \"paymentData\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"instagram-layout\"], [1, \"main-feed\"], [1, \"stories-section\"], [1, \"posts-feed\"], [\"class\", \"instagram-post\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"currentUser\", \"featuredProducts\", \"trendingProducts\", \"newArrivals\", \"summerCollection\", \"categories\"], [1, \"instagram-post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [\"class\", \"location\", 4, \"ngIf\"], [1, \"more-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"class\", \"product-tags-overlay\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"far\", \"fa-paper-plane\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"class\", \"likes-section\", 4, \"ngIf\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"add-comment-section\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"post-comment-btn\", 3, \"click\", \"disabled\"], [1, \"location\"], [1, \"product-tags-overlay\"], [\"title\", \"View Products\", 1, \"product-tag-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [\"class\", \"product-tag\", 3, \"class\", \"left\", \"top\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\", \"title\"], [1, \"tag-dot\"], [1, \"tag-icon\"], [\"class\", \"product-preview\", 4, \"ngIf\"], [1, \"product-preview\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"likes-section\"], [1, \"likes-count\"], [1, \"ecommerce-actions\"], [1, \"product-showcase\"], [\"class\", \"featured-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"shopping-buttons\"], [1, \"shop-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"shop-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"shop-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"featured-product\"], [1, \"product-thumbnail\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"comments-preview\"], [1, \"view-comments-btn\", 3, \"click\"], [1, \"recent-comments\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment\"], [1, \"comment-username\"], [1, \"comment-text\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 4, 0, \"div\", 1)(2, HomeComponent_div_2_Template, 7, 8, \"div\", 2);\n          i0.ɵɵelementStart(3, \"app-payment-modal\", 3);\n          i0.ɵɵlistener(\"close\", function HomeComponent_Template_app_payment_modal_close_3_listener() {\n            return ctx.onPaymentModalClose();\n          })(\"paymentCompleted\", function HomeComponent_Template_app_payment_modal_paymentCompleted_3_listener($event) {\n            return ctx.onPaymentCompleted($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"isVisible\", ctx.showPaymentModal)(\"paymentData\", ctx.paymentModalData);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, RouterModule, ViewAddStoriesComponent, SidebarComponent, PaymentModalComponent],\n      styles: [\".instagram-home-container[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 80px);\\n  background: #fafafa;\\n  padding-top: 20px;\\n}\\n\\n.instagram-layout[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 320px;\\n  gap: 30px;\\n  max-width: 975px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n@media (max-width: 1024px) {\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 614px;\\n    gap: 0;\\n    padding: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    padding: 0;\\n    gap: 0;\\n  }\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  gap: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid #e3e3e3;\\n  border-top: 3px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 14px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.main-feed[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  width: 100%;\\n  max-width: 614px;\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-bottom: 24px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-container {\\n  margin: 0;\\n  padding: 16px;\\n  background: white;\\n  border-radius: 0;\\n  box-shadow: none;\\n  border: none;\\n  -webkit-backdrop-filter: none;\\n          backdrop-filter: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-header {\\n  display: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-slider-wrapper {\\n  gap: 8px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .nav-arrow {\\n  display: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-item {\\n  margin: 0 4px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-avatar {\\n  width: 56px;\\n  height: 56px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-avatar {\\n    width: 56px;\\n    height: 56px;\\n  }\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-avatar-inner {\\n  width: 50px;\\n  height: 50px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-avatar-inner {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-username {\\n  font-size: 12px;\\n  color: #262626;\\n  text-shadow: none;\\n  max-width: 64px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-username {\\n    max-width: 64px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n  }\\n}\\n\\n.posts-feed[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.instagram-post[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-bottom: 24px;\\n  max-width: 614px;\\n  width: 100%;\\n}\\n@media (max-width: 768px) {\\n  .instagram-post[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n    border-bottom: none;\\n  }\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0;\\n  color: #262626;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  color: #262626;\\n}\\n.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.post-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  aspect-ratio: 1;\\n  overflow: hidden;\\n}\\n.post-media[_ngcontent-%COMP%]   .post-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.product-tags-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  pointer-events: all;\\n  transition: all 0.3s ease;\\n  font-size: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translate(-50%, -50%) scale(1.1);\\n}\\n.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(0, 149, 246, 0.9);\\n  transform: translate(-50%, -50%) scale(1.1);\\n}\\n.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  z-index: 5;\\n}\\n.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 2px solid #262626;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 6px;\\n  height: 6px;\\n  background: #262626;\\n  border-radius: 50%;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  border-radius: 8px;\\n  padding: 8px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 200px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 4px;\\n  object-fit: cover;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #f5f5f5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px 8px;\\n}\\n.post-actions[_ngcontent-%COMP%]   .primary-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 24px;\\n  color: #262626;\\n  transition: all 0.3s ease;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ed4956;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn.saved[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n.likes-section[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n.likes-section[_ngcontent-%COMP%]   .likes-count[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n.post-caption[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  margin-right: 8px;\\n}\\n.post-caption[_ngcontent-%COMP%]   .caption-text[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border-top: 1px solid #efefef;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  flex: 1;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 6px;\\n  object-fit: cover;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 16px;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  color: white;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  color: #262626;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  border-color: #c6c6c6;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  color: white;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n\\n.comments-preview[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #8e8e8e;\\n  font-size: 14px;\\n  cursor: pointer;\\n  margin-bottom: 4px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%]:hover {\\n  color: #262626;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n  margin-bottom: 2px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  margin-right: 8px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.post-time[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 10px;\\n  color: #8e8e8e;\\n  text-transform: uppercase;\\n  letter-spacing: 0.2px;\\n}\\n\\n.add-comment-section[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-top: 1px solid #efefef;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  font-size: 14px;\\n  color: #262626;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%]::placeholder {\\n  color: #8e8e8e;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:disabled {\\n  color: #c7c7c7;\\n  cursor: not-allowed;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:not(:disabled):hover {\\n  color: #00376b;\\n}\\n\\n.instagram-sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 84px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  padding-left: 0;\\n}\\n@media (max-width: 1024px) {\\n  .instagram-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.profile-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px 0 24px 0;\\n  margin-bottom: 8px;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 2px 0;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]:hover {\\n  color: #00376b;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #8e8e8e;\\n  margin: 0;\\n  text-transform: none;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #262626;\\n  font-size: 12px;\\n  font-weight: 400;\\n  cursor: pointer;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.collection-items[_ngcontent-%COMP%], .featured-items[_ngcontent-%COMP%], .trending-items[_ngcontent-%COMP%], .arrivals-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.collection-item[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 4px 0;\\n  border-radius: 3px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.collection-item[_ngcontent-%COMP%]:hover, .featured-item[_ngcontent-%COMP%]:hover, .trending-item[_ngcontent-%COMP%]:hover, .arrival-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.collection-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border-radius: 3px;\\n  object-fit: cover;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  text-decoration: line-through;\\n  font-size: 10px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n  font-size: 10px;\\n  color: #8e8e8e;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 8px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%], .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 8px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ffa500);\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n}\\n.trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 6px;\\n}\\n\\n.new-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.suggested-users[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.suggested-user[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-followers[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: #0095f6;\\n  border: none;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: #00376b;\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  padding-top: 16px;\\n  border-top: 1px solid #efefef;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n  text-decoration: none;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n}\\n\\n@media (max-width: 768px) {\\n  .instagram-home-container[_ngcontent-%COMP%] {\\n    padding-top: 0;\\n  }\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    padding: 0;\\n    max-width: 100%;\\n  }\\n  .main-feed[_ngcontent-%COMP%] {\\n    gap: 0;\\n  }\\n  .instagram-post[_ngcontent-%COMP%] {\\n    margin-bottom: 0;\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    max-width: 100%;\\n  }\\n  .instagram-post[_ngcontent-%COMP%]:last-child {\\n    border-bottom: 1px solid #dbdbdb;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n    border-bottom: 1px solid #dbdbdb;\\n  }\\n  .post-header[_ngcontent-%COMP%] {\\n    padding: 14px 16px;\\n  }\\n  .post-actions[_ngcontent-%COMP%] {\\n    padding: 6px 16px 4px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 12px;\\n  }\\n  .add-comment-section[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .likes-section[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%], .post-time[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n    padding-right: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9wYWdlcy9ob21lL2hvbWUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDRSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7QUFBRjs7QUFHQTtFQUNFLGFBQUE7RUFDQSxnQ0FBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBQUY7QUFFRTtFQVJGO0lBU0ksMEJBQUE7SUFDQSxnQkFBQTtJQUNBLE1BQUE7SUFDQSxVQUFBO0VBQ0Y7QUFDRjtBQUNFO0VBZkY7SUFnQkksVUFBQTtJQUNBLE1BQUE7RUFFRjtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGFBQUE7RUFDQSxTQUFBO0FBQ0Y7QUFDRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EseUJBQUE7RUFDQSw2QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0NBQUE7QUFDSjtBQUVFO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUFBSjs7QUFJQTtFQUNFO0lBQUssdUJBQUE7RUFBTDtFQUNBO0lBQU8seUJBQUE7RUFFUDtBQUNGO0FBQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBQ0Y7QUFHSTtFQUNFLFNBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLDZCQUFBO1VBQUEscUJBQUE7QUFETjtBQUlJO0VBQ0UsYUFBQTtBQUZOO0FBS0k7RUFDRSxRQUFBO0FBSE47QUFNSTtFQUNFLGFBQUE7QUFKTjtBQU9JO0VBQ0UsYUFBQTtBQUxOO0FBUUk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQU5OO0FBUU07RUFKRjtJQUtJLFdBQUE7SUFDQSxZQUFBO0VBTE47QUFDRjtBQVFJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFOTjtBQVFNO0VBSkY7SUFLSSxXQUFBO0lBQ0EsWUFBQTtFQUxOO0FBQ0Y7QUFRSTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxlQUFBO0FBTk47QUFRTTtFQU5GO0lBT0ksZUFBQTtFQUxOO0FBQ0Y7QUFTRTtFQW5FRjtJQW9FSSxnQkFBQTtJQUNBLGlCQUFBO0lBQ0Esa0JBQUE7SUFDQSxnQkFBQTtFQU5GO0FBQ0Y7O0FBU0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBTkY7O0FBVUE7RUFDRSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0FBUEY7QUFTRTtFQVRGO0lBVUksZ0JBQUE7SUFDQSxpQkFBQTtJQUNBLGtCQUFBO0lBQ0EsZ0JBQUE7SUFDQSxtQkFBQTtFQU5GO0FBQ0Y7O0FBU0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGFBQUE7QUFORjtBQVFFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQU5KO0FBU0U7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUFQSjtBQVdJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLGNBQUE7QUFUTjtBQVlJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUFWTjtBQWNFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FBWko7QUFjSTtFQUNFLGNBQUE7QUFaTjs7QUFpQkE7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFkRjtBQWdCRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0FBZEo7O0FBbUJBO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esb0JBQUE7QUFoQkY7QUFrQkU7RUFDRSxrQkFBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsZ0NBQUE7RUFDQSw4QkFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxlQUFBO0VBQ0EsbUNBQUE7VUFBQSwyQkFBQTtBQWhCSjtBQWtCSTtFQUNFLDhCQUFBO0VBQ0EsMkNBQUE7QUFoQk47QUFtQkk7RUFDRSxrQ0FBQTtFQUNBLDJDQUFBO0FBakJOO0FBb0JJO0VBQ0UsZUFBQTtBQWxCTjs7QUF1QkE7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLFVBQUE7QUFwQkY7QUFzQkU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsNEJBQUE7QUFwQko7QUFzQkk7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLGdDQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBcEJOO0FBd0JFO0VBQ0Usa0JBQUE7RUFDQSxZQUFBO0VBQ0EsU0FBQTtFQUNBLDJCQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0FBdEJKO0FBd0JJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0FBdEJOO0FBeUJJO0VBQ0UsWUFBQTtBQXZCTjtBQXlCTTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUF2QlI7QUEwQk07RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQXhCUjs7QUE4QkE7RUFDRTtJQUFXLG1CQUFBO0VBMUJYO0VBMkJBO0lBQU0scUJBQUE7RUF4Qk47QUFDRjtBQTJCQTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0Esc0JBQUE7QUF6QkY7QUEyQkU7RUFDRSxhQUFBO0VBQ0EsU0FBQTtBQXpCSjtBQTRCRTtFQUNFLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQTFCSjtBQTRCSTtFQUNFLGNBQUE7QUExQk47QUE2Qkk7RUFDRSxjQUFBO0VBQ0EsOEJBQUE7QUEzQk47QUE4Qkk7RUFDRSxjQUFBO0FBNUJOOztBQWlDQTtFQUNFO0lBQVcsbUJBQUE7RUE3Qlg7RUE4QkE7SUFBTSxxQkFBQTtFQTNCTjtBQUNGO0FBOEJBO0VBQ0UsbUJBQUE7QUE1QkY7QUE4QkU7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBNUJKOztBQWdDQTtFQUNFLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBN0JGO0FBK0JFO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7QUE3Qko7QUFnQ0U7RUFDRSxjQUFBO0FBOUJKOztBQW1DQTtFQUNFLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSw2QkFBQTtBQWhDRjtBQWtDRTtFQUNFLGFBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFoQ0o7QUFrQ0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsT0FBQTtBQWhDTjtBQWtDTTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQWhDUjtBQW1DTTtFQUNFLE9BQUE7QUFqQ1I7QUFtQ1E7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBakNWO0FBb0NRO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFsQ1Y7QUF3Q0U7RUFDRSxhQUFBO0VBQ0EsUUFBQTtBQXRDSjtBQXdDSTtFQUNFLE9BQUE7RUFDQSxpQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsUUFBQTtBQXRDTjtBQXdDTTtFQUNFLHFEQUFBO0VBQ0EsWUFBQTtBQXRDUjtBQXdDUTtFQUNFLDJCQUFBO0VBQ0EsK0NBQUE7QUF0Q1Y7QUEwQ007RUFDRSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0EsY0FBQTtBQXhDUjtBQTBDUTtFQUNFLG1CQUFBO0VBQ0EscUJBQUE7QUF4Q1Y7QUE0Q007RUFDRSxxREFBQTtFQUNBLFlBQUE7QUExQ1I7QUE0Q1E7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBMUNWO0FBOENNO0VBQ0UsZUFBQTtBQTVDUjs7QUFtREE7RUFDRSxtQkFBQTtBQWhERjtBQWtERTtFQUNFLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0FBaERKO0FBa0RJO0VBQ0UsY0FBQTtBQWhETjtBQXFESTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBbkROO0FBcURNO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7QUFuRFI7QUFzRE07RUFDRSxjQUFBO0FBcERSOztBQTBEQTtFQUNFLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtFQUNBLHFCQUFBO0FBdkRGOztBQTBEQTtFQUNFLGtCQUFBO0VBQ0EsNkJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FBdkRGO0FBeURFO0VBQ0UsT0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7QUF2REo7QUF5REk7RUFDRSxjQUFBO0FBdkROO0FBMkRFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7QUF6REo7QUEyREk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7QUF6RE47QUE0REk7RUFDRSxjQUFBO0FBMUROOztBQWdFQTtFQUNFLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLHdCQUFBO0VBQUEsbUJBQUE7RUFDQSxlQUFBO0FBN0RGO0FBK0RFO0VBTkY7SUFPSSxhQUFBO0VBNURGO0FBQ0Y7O0FBK0RBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLHNCQUFBO0VBQ0Esa0JBQUE7QUE1REY7QUE4REU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUE1REo7QUErREU7RUFDRSxPQUFBO0FBN0RKO0FBK0RJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBN0ROO0FBZ0VJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUE5RE47QUFrRUU7RUFDRSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtBQWhFSjtBQWtFSTtFQUNFLGNBQUE7QUFoRU47O0FBcUVBO0VBQ0UsbUJBQUE7QUFsRUY7QUFvRUU7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBbEVKO0FBb0VJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7RUFDQSxvQkFBQTtBQWxFTjtBQXFFSTtFQUNFLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0FBbkVOO0FBcUVNO0VBQ0UsY0FBQTtBQW5FUjs7QUEwRUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxRQUFBO0FBdkVGOztBQTBFQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0Esc0NBQUE7QUF2RUY7QUF5RUU7RUFDRSwrQkFBQTtBQXZFSjtBQTBFRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQXhFSjtBQTJFRTtFQUNFLE9BQUE7QUF6RUo7QUEyRUk7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUF6RU47QUE0RUk7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQTFFTjtBQTZFSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUEzRU47QUE4RUk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FBNUVOO0FBOEVNO0VBQ0UsY0FBQTtBQTVFUjtBQStFTTtFQUNFLGNBQUE7RUFDQSw2QkFBQTtFQUNBLGVBQUE7QUE3RVI7QUFpRkk7RUFDRSxhQUFBO0VBQ0EsUUFBQTtFQUNBLGtCQUFBO0FBL0VOO0FBaUZNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0FBL0VSO0FBaUZRO0VBQ0UsY0FBQTtBQS9FVjs7QUF1RkE7RUFDRSxrQkFBQTtFQUNBLFFBQUE7RUFDQSxVQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUFwRkY7O0FBdUZBO0VBQ0UscURBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FBcEZGO0FBc0ZFO0VBQ0UsY0FBQTtBQXBGSjs7QUF3RkE7RUFDRSxxREFBQTtBQXJGRjs7QUF3RkE7RUFDRSxrQkFBQTtBQXJGRjs7QUF5RkE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxRQUFBO0FBdEZGOztBQXlGQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxZQUFBO0FBdEZGO0FBd0ZFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0FBdEZKO0FBeUZFO0VBQ0UsT0FBQTtBQXZGSjtBQXlGSTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUF2Rk47QUEwRkk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtBQXhGTjtBQTRGRTtFQUNFLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHNDQUFBO0FBMUZKO0FBNEZJO0VBQ0UsbUJBQUE7QUExRk47O0FBZ0dBO0VBQ0UsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLDZCQUFBO0FBN0ZGO0FBK0ZFO0VBQ0UsYUFBQTtFQUNBLGVBQUE7RUFDQSxRQUFBO0VBQ0EsbUJBQUE7QUE3Rko7QUErRkk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLHFCQUFBO0FBN0ZOO0FBK0ZNO0VBQ0UsMEJBQUE7QUE3RlI7QUFrR0U7RUFDRSxlQUFBO0VBQ0EsY0FBQTtBQWhHSjs7QUFxR0E7RUFDRTtJQUNFLGNBQUE7RUFsR0Y7RUFxR0E7SUFDRSxVQUFBO0lBQ0EsZUFBQTtFQW5HRjtFQXNHQTtJQUNFLE1BQUE7RUFwR0Y7RUF1R0E7SUFDRSxnQkFBQTtJQUNBLGdCQUFBO0lBQ0EsaUJBQUE7SUFDQSxrQkFBQTtJQUNBLGVBQUE7RUFyR0Y7RUF1R0U7SUFDRSxnQ0FBQTtFQXJHSjtFQXlHQTtJQUNFLGdCQUFBO0lBQ0EsaUJBQUE7SUFDQSxrQkFBQTtJQUNBLGdCQUFBO0lBQ0EsZ0NBQUE7RUF2R0Y7RUEwR0E7SUFDRSxrQkFBQTtFQXhHRjtFQTJHQTtJQUNFLHFCQUFBO0VBekdGO0VBNEdBO0lBQ0Usa0JBQUE7RUExR0Y7RUE2R0k7SUFDRSxpQkFBQTtJQUNBLGVBQUE7RUEzR047RUFnSEE7SUFDRSxrQkFBQTtFQTlHRjtFQWlIQTtJQUNFLGtCQUFBO0lBQ0EsbUJBQUE7RUEvR0Y7QUFDRjtBQWtIQTtFQUVJO0lBQ0Usc0JBQUE7SUFDQSxRQUFBO0VBakhKO0VBb0hNO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUFsSFI7RUF1SEU7SUFDRSxzQkFBQTtJQUNBLFFBQUE7RUFySEo7RUF1SEk7SUFDRSxpQkFBQTtJQUNBLGVBQUE7RUFySE47QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi8vIEluc3RhZ3JhbS1zdHlsZSBIb21lIExheW91dFxuLmluc3RhZ3JhbS1ob21lLWNvbnRhaW5lciB7XG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4MHB4KTtcbiAgYmFja2dyb3VuZDogI2ZhZmFmYTtcbiAgcGFkZGluZy10b3A6IDIwcHg7XG59XG5cbi5pbnN0YWdyYW0tbGF5b3V0IHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgMzIwcHg7XG4gIGdhcDogMzBweDtcbiAgbWF4LXdpZHRoOiA5NzVweDtcbiAgbWFyZ2luOiAwIGF1dG87XG4gIHBhZGRpbmc6IDAgMjBweDtcblxuICBAbWVkaWEgKG1heC13aWR0aDogMTAyNHB4KSB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgbWF4LXdpZHRoOiA2MTRweDtcbiAgICBnYXA6IDA7XG4gICAgcGFkZGluZzogMDtcbiAgfVxuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIHBhZGRpbmc6IDA7XG4gICAgZ2FwOiAwO1xuICB9XG59XG5cbi8vIExvYWRpbmcgU3RhdGVcbi5sb2FkaW5nLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBoZWlnaHQ6IDIwMHB4O1xuICBnYXA6IDE2cHg7XG5cbiAgLmxvYWRpbmctc3Bpbm5lciB7XG4gICAgd2lkdGg6IDQwcHg7XG4gICAgaGVpZ2h0OiA0MHB4O1xuICAgIGJvcmRlcjogM3B4IHNvbGlkICNlM2UzZTM7XG4gICAgYm9yZGVyLXRvcDogM3B4IHNvbGlkICM2NjdlZWE7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XG4gIH1cblxuICBwIHtcbiAgICBjb2xvcjogIzZjNzU3ZDtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gIH1cbn1cblxuQGtleWZyYW1lcyBzcGluIHtcbiAgMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxuICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxufVxuXG4vLyBNYWluIEZlZWQgU3R5bGVzXG4ubWFpbi1mZWVkIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAyNHB4O1xuICB3aWR0aDogMTAwJTtcbiAgbWF4LXdpZHRoOiA2MTRweDtcbn1cblxuLnN0b3JpZXMtc2VjdGlvbiB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZGJkYmRiO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XG5cbiAgLy8gT3ZlcnJpZGUgdGhlIHN0b3JpZXMgY29tcG9uZW50IHN0eWxlcyB0byBtYWtlIGl0IG1vcmUgY29tcGFjdFxuICA6Om5nLWRlZXAge1xuICAgIC5zdG9yaWVzLWNvbnRhaW5lciB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAwO1xuICAgICAgYm94LXNoYWRvdzogbm9uZTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGJhY2tkcm9wLWZpbHRlcjogbm9uZTtcbiAgICB9XG5cbiAgICAuc3Rvcmllcy1oZWFkZXIge1xuICAgICAgZGlzcGxheTogbm9uZTsgLy8gSGlkZSB0aGUgaGVhZGVyIHRvIG1ha2UgaXQgbW9yZSBJbnN0YWdyYW0tbGlrZVxuICAgIH1cblxuICAgIC5zdG9yaWVzLXNsaWRlci13cmFwcGVyIHtcbiAgICAgIGdhcDogOHB4O1xuICAgIH1cblxuICAgIC5uYXYtYXJyb3cge1xuICAgICAgZGlzcGxheTogbm9uZTsgLy8gSGlkZSBhcnJvd3MgZm9yIGNsZWFuZXIgbG9va1xuICAgIH1cblxuICAgIC5zdG9yeS1pdGVtIHtcbiAgICAgIG1hcmdpbjogMCA0cHg7XG4gICAgfVxuXG4gICAgLnN0b3J5LWF2YXRhciB7XG4gICAgICB3aWR0aDogNTZweDtcbiAgICAgIGhlaWdodDogNTZweDtcblxuICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgIHdpZHRoOiA1NnB4O1xuICAgICAgICBoZWlnaHQ6IDU2cHg7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLnN0b3J5LWF2YXRhci1pbm5lciB7XG4gICAgICB3aWR0aDogNTBweDtcbiAgICAgIGhlaWdodDogNTBweDtcblxuICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAgIHdpZHRoOiA1MHB4O1xuICAgICAgICBoZWlnaHQ6IDUwcHg7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLnN0b3J5LXVzZXJuYW1lIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgdGV4dC1zaGFkb3c6IG5vbmU7XG4gICAgICBtYXgtd2lkdGg6IDY0cHg7XG5cbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgICBtYXgtd2lkdGg6IDY0cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgYm9yZGVyLXJhZGl1czogMDtcbiAgICBib3JkZXItbGVmdDogbm9uZTtcbiAgICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gICAgbWFyZ2luLWJvdHRvbTogMDtcbiAgfVxufVxuXG4ucG9zdHMtZmVlZCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMjRweDtcbn1cblxuLy8gSW5zdGFncmFtIFBvc3QgU3R5bGVzXG4uaW5zdGFncmFtLXBvc3Qge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyOiAxcHggc29saWQgI2RiZGJkYjtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICBtYXgtd2lkdGg6IDYxNHB4O1xuICB3aWR0aDogMTAwJTtcblxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBib3JkZXItcmFkaXVzOiAwO1xuICAgIGJvcmRlci1sZWZ0OiBub25lO1xuICAgIGJvcmRlci1yaWdodDogbm9uZTtcbiAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gIH1cbn1cblxuLnBvc3QtaGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBwYWRkaW5nOiAxNnB4O1xuXG4gIC51c2VyLWluZm8ge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDEycHg7XG4gIH1cblxuICAudXNlci1hdmF0YXIge1xuICAgIHdpZHRoOiAzMnB4O1xuICAgIGhlaWdodDogMzJweDtcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgb2JqZWN0LWZpdDogY292ZXI7XG4gIH1cblxuICAudXNlci1kZXRhaWxzIHtcbiAgICAudXNlcm5hbWUge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgIH1cblxuICAgIC5sb2NhdGlvbiB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICB9XG4gIH1cblxuICAubW9yZS1vcHRpb25zIHtcbiAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgcGFkZGluZzogOHB4O1xuICAgIGNvbG9yOiAjMjYyNjI2O1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICB9XG4gIH1cbn1cblxuLnBvc3QtbWVkaWEge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiAxMDAlO1xuICBhc3BlY3QtcmF0aW86IDE7XG4gIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgLnBvc3QtaW1hZ2Uge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIGhlaWdodDogMTAwJTtcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuXG4vLyBQcm9kdWN0IFRhZ3MgT3ZlcmxheVxuLnByb2R1Y3QtdGFncy1vdmVybGF5IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xuXG4gIC5wcm9kdWN0LXRhZy1idG4ge1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDUwJTtcbiAgICBsZWZ0OiA1MCU7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XG4gICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpO1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIHdpZHRoOiA0MHB4O1xuICAgIGhlaWdodDogNDBweDtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIHBvaW50ZXItZXZlbnRzOiBhbGw7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICBmb250LXNpemU6IDE2cHg7XG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuOSk7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKSBzY2FsZSgxLjEpO1xuICAgIH1cblxuICAgICYuYWN0aXZlIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMTQ5LCAyNDYsIDAuOSk7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKSBzY2FsZSgxLjEpO1xuICAgIH1cblxuICAgIGkge1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgIH1cbiAgfVxufVxuXG4ucHJvZHVjdC10YWcge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHBvaW50ZXItZXZlbnRzOiBhbGw7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgei1pbmRleDogNTtcblxuICAudGFnLWRvdCB7XG4gICAgd2lkdGg6IDIwcHg7XG4gICAgaGVpZ2h0OiAyMHB4O1xuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICBib3JkZXI6IDJweCBzb2xpZCAjMjYyNjI2O1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xuXG4gICAgJjo6YWZ0ZXIge1xuICAgICAgY29udGVudDogJyc7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDUwJTtcbiAgICAgIGxlZnQ6IDUwJTtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpO1xuICAgICAgd2lkdGg6IDZweDtcbiAgICAgIGhlaWdodDogNnB4O1xuICAgICAgYmFja2dyb3VuZDogIzI2MjYyNjtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICB9XG4gIH1cblxuICAucHJvZHVjdC1wcmV2aWV3IHtcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgYm90dG9tOiAzMHB4O1xuICAgIGxlZnQ6IDUwJTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XG4gICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjgpO1xuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICBwYWRkaW5nOiA4cHg7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogOHB4O1xuICAgIG1pbi13aWR0aDogMjAwcHg7XG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuXG4gICAgaW1nIHtcbiAgICAgIHdpZHRoOiA0MHB4O1xuICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtaW5mbyB7XG4gICAgICBjb2xvcjogd2hpdGU7XG5cbiAgICAgIC5wcm9kdWN0LW5hbWUge1xuICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgfVxuXG4gICAgICAucHJvZHVjdC1wcmljZSB7XG4gICAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGNvbG9yOiAjZjVmNWY1O1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcbiAgMCUsIDEwMCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEpOyB9XG4gIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4xKTsgfVxufVxuXG4vLyBQb3N0IEFjdGlvbnNcbi5wb3N0LWFjdGlvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDEycHggMTZweCA4cHg7XG5cbiAgLnByaW1hcnktYWN0aW9ucyB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBnYXA6IDE2cHg7XG4gIH1cblxuICAuYWN0aW9uLWJ0biB7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIHBhZGRpbmc6IDhweDtcbiAgICBmb250LXNpemU6IDI0cHg7XG4gICAgY29sb3I6ICMyNjI2MjY7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICY6aG92ZXIge1xuICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgfVxuXG4gICAgJi5saWtlZCB7XG4gICAgICBjb2xvcjogI2VkNDk1NjtcbiAgICAgIGFuaW1hdGlvbjogaGVhcnRCZWF0IDAuNnMgZWFzZTtcbiAgICB9XG5cbiAgICAmLnNhdmVkIHtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgIH1cbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIGhlYXJ0QmVhdCB7XG4gIDAlLCAxMDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxKTsgfVxuICA1MCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEuMik7IH1cbn1cblxuLy8gUG9zdCBDb250ZW50IFNlY3Rpb25zXG4ubGlrZXMtc2VjdGlvbiB7XG4gIHBhZGRpbmc6IDAgMTZweCA4cHg7XG5cbiAgLmxpa2VzLWNvdW50IHtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogIzI2MjYyNjtcbiAgfVxufVxuXG4ucG9zdC1jYXB0aW9uIHtcbiAgcGFkZGluZzogMCAxNnB4IDhweDtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBsaW5lLWhlaWdodDogMS40O1xuXG4gIC51c2VybmFtZSB7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogIzI2MjYyNjtcbiAgICBtYXJnaW4tcmlnaHQ6IDhweDtcbiAgfVxuXG4gIC5jYXB0aW9uLXRleHQge1xuICAgIGNvbG9yOiAjMjYyNjI2O1xuICB9XG59XG5cbi8vIEUtY29tbWVyY2UgQWN0aW9uc1xuLmVjb21tZXJjZS1hY3Rpb25zIHtcbiAgcGFkZGluZzogMTJweCAxNnB4O1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICBib3JkZXItdG9wOiAxcHggc29saWQgI2VmZWZlZjtcblxuICAucHJvZHVjdC1zaG93Y2FzZSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBnYXA6IDEycHg7XG4gICAgbWFyZ2luLWJvdHRvbTogMTJweDtcblxuICAgIC5mZWF0dXJlZC1wcm9kdWN0IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBmbGV4OiAxO1xuXG4gICAgICAucHJvZHVjdC10aHVtYm5haWwge1xuICAgICAgICB3aWR0aDogNDBweDtcbiAgICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgICAgfVxuXG4gICAgICAucHJvZHVjdC1kZXRhaWxzIHtcbiAgICAgICAgZmxleDogMTtcblxuICAgICAgICAucHJvZHVjdC1uYW1lIHtcbiAgICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgICAgICB9XG5cbiAgICAgICAgLnByb2R1Y3QtcHJpY2Uge1xuICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLnNob3BwaW5nLWJ1dHRvbnMge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZ2FwOiA4cHg7XG5cbiAgICAuc2hvcC1idG4ge1xuICAgICAgZmxleDogMTtcbiAgICAgIHBhZGRpbmc6IDhweCAxNnB4O1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgZ2FwOiA0cHg7XG5cbiAgICAgICYuYnV5LWJ0biB7XG4gICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICAgICAgICBjb2xvcjogd2hpdGU7XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgICYud2lzaGxpc3QtYnRuIHtcbiAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkYmRiZGI7XG4gICAgICAgIGNvbG9yOiAjMjYyNjI2O1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYzZjNmM2O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgICYuY2FydC1idG4ge1xuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjA5M2ZiLCAjZjU1NzZjKTtcbiAgICAgICAgY29sb3I6IHdoaXRlO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMjQwLCAxNDcsIDI1MSwgMC40KTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpIHtcbiAgICAgICAgZm9udC1zaXplOiAxMHB4O1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBDb21tZW50cyBTZWN0aW9uXG4uY29tbWVudHMtcHJldmlldyB7XG4gIHBhZGRpbmc6IDAgMTZweCA4cHg7XG5cbiAgLnZpZXctY29tbWVudHMtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDRweDtcblxuICAgICY6aG92ZXIge1xuICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgfVxuICB9XG5cbiAgLnJlY2VudC1jb21tZW50cyB7XG4gICAgLmNvbW1lbnQge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDJweDtcblxuICAgICAgLmNvbW1lbnQtdXNlcm5hbWUge1xuICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gICAgICB9XG5cbiAgICAgIC5jb21tZW50LXRleHQge1xuICAgICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLnBvc3QtdGltZSB7XG4gIHBhZGRpbmc6IDAgMTZweCA4cHg7XG4gIGZvbnQtc2l6ZTogMTBweDtcbiAgY29sb3I6ICM4ZThlOGU7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gIGxldHRlci1zcGFjaW5nOiAwLjJweDtcbn1cblxuLmFkZC1jb21tZW50LXNlY3Rpb24ge1xuICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZWZlZmVmO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcblxuICAuY29tbWVudC1pbnB1dCB7XG4gICAgZmxleDogMTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgb3V0bGluZTogbm9uZTtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgY29sb3I6ICMyNjI2MjY7XG5cbiAgICAmOjpwbGFjZWhvbGRlciB7XG4gICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICB9XG4gIH1cblxuICAucG9zdC1jb21tZW50LWJ0biB7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY29sb3I6ICMwMDk1ZjY7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuXG4gICAgJjpkaXNhYmxlZCB7XG4gICAgICBjb2xvcjogI2M3YzdjNztcbiAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gICAgfVxuXG4gICAgJjpub3QoOmRpc2FibGVkKTpob3ZlciB7XG4gICAgICBjb2xvcjogIzAwMzc2YjtcbiAgICB9XG4gIH1cbn1cblxuLy8gSW5zdGFncmFtIFNpZGViYXJcbi5pbnN0YWdyYW0tc2lkZWJhciB7XG4gIHBvc2l0aW9uOiBzdGlja3k7XG4gIHRvcDogODRweDsgLy8gQWNjb3VudCBmb3IgaGVhZGVyIGhlaWdodFxuICBoZWlnaHQ6IGZpdC1jb250ZW50O1xuICBwYWRkaW5nLWxlZnQ6IDA7XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDEwMjRweCkge1xuICAgIGRpc3BsYXk6IG5vbmU7XG4gIH1cbn1cblxuLnByb2ZpbGUtY2FyZCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgcGFkZGluZzogMTZweCAwIDI0cHggMDtcbiAgbWFyZ2luLWJvdHRvbTogOHB4O1xuXG4gIC5wcm9maWxlLWF2YXRhciB7XG4gICAgd2lkdGg6IDU2cHg7XG4gICAgaGVpZ2h0OiA1NnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgfVxuXG4gIC5wcm9maWxlLWluZm8ge1xuICAgIGZsZXg6IDE7XG5cbiAgICAucHJvZmlsZS11c2VybmFtZSB7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICBtYXJnaW46IDAgMCAycHggMDtcbiAgICB9XG5cbiAgICAucHJvZmlsZS1uYW1lIHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgIH1cbiAgfVxuXG4gIC5zd2l0Y2gtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBjb2xvcjogIzAwOTVmNjtcbiAgICBmb250LXNpemU6IDEycHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGNvbG9yOiAjMDAzNzZiO1xuICAgIH1cbiAgfVxufVxuXG4uc2lkZWJhci1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMzJweDtcblxuICAuc2VjdGlvbi1oZWFkZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcblxuICAgIGgzIHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIHRleHQtdHJhbnNmb3JtOiBub25lO1xuICAgIH1cblxuICAgIC5zZWUtYWxsLWJ0biB7XG4gICAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBmb250LXdlaWdodDogNDAwO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIENvbGxlY3Rpb24gSXRlbXNcbi5jb2xsZWN0aW9uLWl0ZW1zLCAuZmVhdHVyZWQtaXRlbXMsIC50cmVuZGluZy1pdGVtcywgLmFycml2YWxzLWl0ZW1zIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiA4cHg7XG59XG5cbi5jb2xsZWN0aW9uLWl0ZW0sIC5mZWF0dXJlZC1pdGVtLCAudHJlbmRpbmctaXRlbSwgLmFycml2YWwtaXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgcGFkZGluZzogNHB4IDA7XG4gIGJvcmRlci1yYWRpdXM6IDNweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnMgZWFzZTtcblxuICAmOmhvdmVyIHtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICB9XG5cbiAgaW1nIHtcbiAgICB3aWR0aDogNDRweDtcbiAgICBoZWlnaHQ6IDQ0cHg7XG4gICAgYm9yZGVyLXJhZGl1czogM3B4O1xuICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICB9XG5cbiAgLmNvbGxlY3Rpb24taW5mbywgLmZlYXR1cmVkLWluZm8sIC50cmVuZGluZy1pbmZvLCAuYXJyaXZhbC1pbmZvIHtcbiAgICBmbGV4OiAxO1xuXG4gICAgLmNvbGxlY3Rpb24tbmFtZSwgLmZlYXR1cmVkLW5hbWUsIC50cmVuZGluZy1uYW1lLCAuYXJyaXZhbC1uYW1lIHtcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDJweDtcbiAgICB9XG5cbiAgICAuZmVhdHVyZWQtYnJhbmQsIC5hcnJpdmFsLWJyYW5kIHtcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycHg7XG4gICAgfVxuXG4gICAgLmNvbGxlY3Rpb24tcHJpY2UsIC5mZWF0dXJlZC1wcmljZSwgLnRyZW5kaW5nLXByaWNlLCAuYXJyaXZhbC1wcmljZSB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgfVxuXG4gICAgLmZlYXR1cmVkLXByaWNlIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA2cHg7XG5cbiAgICAgIC5jdXJyZW50LXByaWNlIHtcbiAgICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgICB9XG5cbiAgICAgIC5vcmlnaW5hbC1wcmljZSB7XG4gICAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgICAgICB0ZXh0LWRlY29yYXRpb246IGxpbmUtdGhyb3VnaDtcbiAgICAgICAgZm9udC1zaXplOiAxMHB4O1xuICAgICAgfVxuICAgIH1cblxuICAgIC50cmVuZGluZy1zdGF0cyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycHg7XG5cbiAgICAgIHNwYW4ge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBnYXA6IDJweDtcbiAgICAgICAgZm9udC1zaXplOiAxMHB4O1xuICAgICAgICBjb2xvcjogIzhlOGU4ZTtcblxuICAgICAgICBpIHtcbiAgICAgICAgICBmb250LXNpemU6IDhweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBTcGVjaWFsIGJhZGdlc1xuLnRyZW5kaW5nLWJhZGdlLCAubmV3LWJhZGdlIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDRweDtcbiAgcmlnaHQ6IDRweDtcbiAgcGFkZGluZzogMnB4IDZweDtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgZm9udC1zaXplOiA4cHg7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiB3aGl0ZTtcbn1cblxuLnRyZW5kaW5nLWJhZGdlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmI2YiwgI2ZmYTUwMCk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMnB4O1xuXG4gIGkge1xuICAgIGZvbnQtc2l6ZTogNnB4O1xuICB9XG59XG5cbi5uZXctYmFkZ2Uge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDNlOTdiLCAjMzhmOWQ3KTtcbn1cblxuLnRyZW5kaW5nLWl0ZW0sIC5hcnJpdmFsLWl0ZW0ge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG59XG5cbi8vIFN1Z2dlc3RlZCBVc2Vyc1xuLnN1Z2dlc3RlZC11c2VycyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogOHB4O1xufVxuXG4uc3VnZ2VzdGVkLXVzZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEycHg7XG4gIHBhZGRpbmc6IDhweDtcblxuICAuc3VnZ2VzdGVkLWF2YXRhciB7XG4gICAgd2lkdGg6IDMycHg7XG4gICAgaGVpZ2h0OiAzMnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgfVxuXG4gIC5zdWdnZXN0ZWQtaW5mbyB7XG4gICAgZmxleDogMTtcblxuICAgIC5zdWdnZXN0ZWQtdXNlcm5hbWUge1xuICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgIH1cblxuICAgIC5zdWdnZXN0ZWQtZm9sbG93ZXJzIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgIH1cbiAgfVxuXG4gIC5mb2xsb3ctYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiAjMDA5NWY2O1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgcGFkZGluZzogNHB4IDEycHg7XG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuM3MgZWFzZTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogIzAwMzc2YjtcbiAgICB9XG4gIH1cbn1cblxuLy8gU2lkZWJhciBGb290ZXJcbi5zaWRlYmFyLWZvb3RlciB7XG4gIG1hcmdpbi10b3A6IDMycHg7XG4gIHBhZGRpbmctdG9wOiAxNnB4O1xuICBib3JkZXItdG9wOiAxcHggc29saWQgI2VmZWZlZjtcblxuICAuZm9vdGVyLWxpbmtzIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtd3JhcDogd3JhcDtcbiAgICBnYXA6IDhweDtcbiAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuXG4gICAgYSB7XG4gICAgICBmb250LXNpemU6IDExcHg7XG4gICAgICBjb2xvcjogI2M3YzdjNztcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5jb3B5cmlnaHQge1xuICAgIGZvbnQtc2l6ZTogMTFweDtcbiAgICBjb2xvcjogI2M3YzdjNztcbiAgfVxufVxuXG4vLyBNb2JpbGUgUmVzcG9uc2l2ZW5lc3NcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuaW5zdGFncmFtLWhvbWUtY29udGFpbmVyIHtcbiAgICBwYWRkaW5nLXRvcDogMDtcbiAgfVxuXG4gIC5pbnN0YWdyYW0tbGF5b3V0IHtcbiAgICBwYWRkaW5nOiAwO1xuICAgIG1heC13aWR0aDogMTAwJTtcbiAgfVxuXG4gIC5tYWluLWZlZWQge1xuICAgIGdhcDogMDtcbiAgfVxuXG4gIC5pbnN0YWdyYW0tcG9zdCB7XG4gICAgbWFyZ2luLWJvdHRvbTogMDtcbiAgICBib3JkZXItcmFkaXVzOiAwO1xuICAgIGJvcmRlci1sZWZ0OiBub25lO1xuICAgIGJvcmRlci1yaWdodDogbm9uZTtcbiAgICBtYXgtd2lkdGg6IDEwMCU7XG5cbiAgICAmOmxhc3QtY2hpbGQge1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNkYmRiZGI7XG4gICAgfVxuICB9XG5cbiAgLnN0b3JpZXMtc2VjdGlvbiB7XG4gICAgYm9yZGVyLXJhZGl1czogMDtcbiAgICBib3JkZXItbGVmdDogbm9uZTtcbiAgICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gICAgbWFyZ2luLWJvdHRvbTogMDtcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2RiZGJkYjtcbiAgfVxuXG4gIC5wb3N0LWhlYWRlciB7XG4gICAgcGFkZGluZzogMTRweCAxNnB4O1xuICB9XG5cbiAgLnBvc3QtYWN0aW9ucyB7XG4gICAgcGFkZGluZzogNnB4IDE2cHggNHB4O1xuICB9XG5cbiAgLmVjb21tZXJjZS1hY3Rpb25zIHtcbiAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XG5cbiAgICAuc2hvcHBpbmctYnV0dG9ucyB7XG4gICAgICAuc2hvcC1idG4ge1xuICAgICAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5hZGQtY29tbWVudC1zZWN0aW9uIHtcbiAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gIH1cblxuICAubGlrZXMtc2VjdGlvbiwgLnBvc3QtY2FwdGlvbiwgLmNvbW1lbnRzLXByZXZpZXcsIC5wb3N0LXRpbWUge1xuICAgIHBhZGRpbmctbGVmdDogMTZweDtcbiAgICBwYWRkaW5nLXJpZ2h0OiAxNnB4O1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAuZWNvbW1lcmNlLWFjdGlvbnMge1xuICAgIC5wcm9kdWN0LXNob3djYXNlIHtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBnYXA6IDhweDtcblxuICAgICAgLmZlYXR1cmVkLXByb2R1Y3Qge1xuICAgICAgICAucHJvZHVjdC10aHVtYm5haWwge1xuICAgICAgICAgIHdpZHRoOiAzMnB4O1xuICAgICAgICAgIGhlaWdodDogMzJweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5zaG9wcGluZy1idXR0b25zIHtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBnYXA6IDZweDtcblxuICAgICAgLnNob3AtYnRuIHtcbiAgICAgICAgcGFkZGluZzogOHB4IDE2cHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "RouterModule", "ViewAddStoriesComponent", "PaymentModalComponent", "SidebarComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "post_r2", "location", "ɵɵlistener", "HomeComponent_div_2_article_5_div_12_Template_button_click_1_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "toggleProductTags", "ɵɵclassProp", "showProductTags", "ɵɵproperty", "getTagImageUrl", "productTag_r6", "ɵɵsanitizeUrl", "getTagName", "getTagSubtitle", "HomeComponent_div_2_article_5_div_13_button_1_Template_button_click_0_listener", "_r5", "onProductTagClick", "ɵɵtemplate", "HomeComponent_div_2_article_5_div_13_button_1_div_3_Template", "ɵɵclassMap", "navigationType", "ɵɵstyleProp", "position", "x", "y", "getTagTooltip", "getTagIcon", "showPreview", "HomeComponent_div_2_article_5_div_13_button_1_Template", "products", "ɵɵtextInterpolate1", "formatLikesCount", "analytics", "likes", "product_r8", "image", "name", "formatPrice", "price", "HomeComponent_div_2_article_5_div_30_div_2_Template", "HomeComponent_div_2_article_5_div_30_Template_button_click_4_listener", "_r7", "buyNow", "HomeComponent_div_2_article_5_div_30_Template_button_click_7_listener", "addToWishlist", "HomeComponent_div_2_article_5_div_30_Template_button_click_10_listener", "addToCart", "slice", "comment_r10", "username", "text", "HomeComponent_div_2_article_5_div_31_Template_button_click_1_listener", "_r9", "toggleComments", "HomeComponent_div_2_article_5_div_31_div_4_Template", "comments", "length", "HomeComponent_div_2_article_5_span_7_Template", "HomeComponent_div_2_article_5_div_12_Template", "HomeComponent_div_2_article_5_div_13_Template", "HomeComponent_div_2_article_5_Template_button_click_16_listener", "_r1", "toggleLike", "HomeComponent_div_2_article_5_Template_button_click_18_listener", "focusCommentInput", "HomeComponent_div_2_article_5_Template_button_click_20_listener", "sharePost", "HomeComponent_div_2_article_5_Template_button_click_22_listener", "toggleSave", "HomeComponent_div_2_article_5_div_24_Template", "HomeComponent_div_2_article_5_div_30_Template", "HomeComponent_div_2_article_5_div_31_Template", "ɵɵtwoWayListener", "HomeComponent_div_2_article_5_Template_input_ngModelChange_35_listener", "$event", "ɵɵtwoWayBindingSet", "newComment", "HomeComponent_div_2_article_5_Template_input_keyup_enter_35_listener", "addComment", "HomeComponent_div_2_article_5_Template_button_click_36_listener", "user", "avatar", "mediaUrl", "caption", "isLiked", "isSaved", "getTimeAgo", "createdAt", "ɵɵtwoWayProperty", "trim", "HomeComponent_div_2_article_5_Template", "instagramPosts", "trackByPostId", "currentUser", "featuredProducts", "trendingProducts", "newArrivals", "summerCollection", "categories", "HomeComponent", "constructor", "router", "productService", "authService", "buttonActionsService", "postsService", "trendingPosts", "isLoading", "isAuthenticated", "suggestedUsers", "showPaymentModal", "paymentModalData", "ngOnInit", "loadHomeData", "checkAuthStatus", "loadInstagramData", "currentUser$", "subscribe", "loadInstagramPosts", "loadSummerCollection", "loadSuggestedUsers", "getPosts", "next", "response", "success", "posts", "console", "log", "error", "getCategoryProducts", "_this", "_asyncToGenerator", "featured", "trending", "arrivals", "Promise", "all", "getFeaturedProducts", "to<PERSON>romise", "getTrendingProducts", "getNewArrivals", "data", "loadCategories", "loadTrendingPosts", "getCategories", "getTrendingPosts", "onProductClick", "product", "navigate", "_id", "id", "onCategoryClick", "category", "queryParams", "toLowerCase", "viewAllCategories", "post", "wasLiked", "result", "likesCount", "undefined", "message", "postId", "savePost", "commentText", "comment", "push", "showProductDetails", "viewPost", "viewUserProfile", "productId", "quantity", "addedFrom", "showPaymentModalFallback", "returnUrl", "amount", "orderData", "items", "subtotal", "tax", "shipping", "discount", "total", "userDetails", "fullName", "email", "phone", "followUser", "viewSummerCollection", "collection", "Intl", "NumberFormat", "style", "currency", "format", "formatNumber", "num", "toFixed", "toString", "date", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "now", "diffInSeconds", "Math", "floor", "index", "onPaymentCompleted", "paymentResult", "status", "orderId", "generateOrderId", "method", "alert", "onPaymentModalClose", "productTag", "targetData", "vendor", "brand", "navigateToProduct", "navigateToCategory", "navigateToVendor", "navigate<PERSON><PERSON>Brand", "slug", "categorySlug", "replace", "vendorId", "brandSlug", "type", "productCount", "images", "url", "logo", "ɵɵdirectiveInject", "i1", "Router", "i2", "ProductService", "i3", "AuthService", "i4", "ButtonActionsService", "i5", "PostsService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "HomeComponent_Template_app_payment_modal_close_3_listener", "HomeComponent_Template_app_payment_modal_paymentCompleted_3_listener", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { ButtonActionsService } from '../../../../core/services/button-actions.service';\nimport { PostsService, InstagramPost } from '../../../../core/services/posts.service';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent, PaymentModalData } from '../../../../shared/components/payment-modal/payment-modal.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RouterModule, ViewAddStoriesComponent,\n    SidebarComponent, PaymentModalComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  featuredProducts: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  trendingPosts: any[] = [];\n  categories: any[] = [];\n  isLoading = true;\n  isAuthenticated = false;\n\n  // Instagram-style data\n  instagramPosts: any[] = [];\n  summerCollection: any[] = [];\n  suggestedUsers: any[] = [];\n  currentUser: any = null;\n  newComment = '';\n\n  // Payment Modal\n  showPaymentModal = false;\n  paymentModalData: PaymentModalData | null = null;\n\n  constructor(\n    private router: Router,\n    private productService: ProductService,\n    private authService: AuthService,\n    private buttonActionsService: ButtonActionsService,\n    private postsService: PostsService\n  ) {}\n\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n\n  loadInstagramPosts() {\n    // Load real Instagram-style posts from API\n    this.postsService.getPosts(1, 10).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.instagramPosts = response.posts;\n          console.log('✅ Instagram posts loaded:', this.instagramPosts.length);\n        } else {\n          this.instagramPosts = [];\n          console.log('⚠️ No Instagram posts available');\n        }\n      },\n      error: (error) => {\n        console.error('❌ Error loading Instagram posts:', error);\n        this.instagramPosts = [];\n      }\n    });\n  }\n\n  loadSummerCollection() {\n    // Load summer collection from API using category products\n    this.productService.getCategoryProducts('summer').subscribe({\n      next: (response: any) => {\n        this.summerCollection = response.products || [];\n        console.log('✅ Summer collection loaded:', this.summerCollection.length);\n      },\n      error: (error: any) => {\n        console.error('❌ Error loading summer collection:', error);\n        this.summerCollection = [];\n      }\n    });\n  }\n\n  loadSuggestedUsers() {\n    // Load suggested users from API\n    // For now, set empty array until users API is implemented\n    this.suggestedUsers = [];\n    console.log('✅ Suggested users loaded:', this.suggestedUsers.length);\n  }\n\n  async loadHomeData() {\n    try {\n      this.isLoading = true;\n\n      // Load all data in parallel\n      const [featured, trending, arrivals] = await Promise.all([\n        this.productService.getFeaturedProducts().toPromise(),\n        this.productService.getTrendingProducts().toPromise(),\n        this.productService.getNewArrivals().toPromise()\n      ]);\n\n      this.featuredProducts = ((featured as any)?.products || (featured as any)?.data || featured || []).slice(0, 8);\n      this.trendingProducts = ((trending as any)?.products || (trending as any)?.data || trending || []).slice(0, 8);\n      this.newArrivals = ((arrivals as any)?.products || (arrivals as any)?.data || arrivals || []).slice(0, 8);\n\n      // Log loaded data counts\n      console.log('✅ Featured products loaded:', this.featuredProducts.length);\n      console.log('✅ Trending products loaded:', this.trendingProducts.length);\n      console.log('✅ New arrivals loaded:', this.newArrivals.length);\n\n      // Load categories\n      this.loadCategories();\n\n      // Load trending posts (mock data for now)\n      this.loadTrendingPosts();\n\n    } catch (error) {\n      console.error('❌ Error loading home data:', error);\n      // Set empty arrays instead of fallback data\n      this.featuredProducts = [];\n      this.trendingProducts = [];\n      this.newArrivals = [];\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  loadCategories() {\n    // Load categories from API\n    this.productService.getCategories().subscribe({\n      next: (response: any) => {\n        this.categories = response.data || [];\n        console.log('✅ Categories loaded:', this.categories.length);\n      },\n      error: (error: any) => {\n        console.error('❌ Error loading categories:', error);\n        this.categories = [];\n      }\n    });\n  }\n\n  loadTrendingPosts() {\n    // Load trending posts from API\n    this.postsService.getTrendingPosts(6).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.trendingPosts = response.posts;\n          console.log('✅ Trending posts loaded:', this.trendingPosts.length);\n        } else {\n          this.trendingPosts = [];\n          console.log('⚠️ No trending posts available');\n        }\n      },\n      error: (error) => {\n        console.error('❌ Error loading trending posts:', error);\n        this.trendingPosts = [];\n      }\n    });\n  }\n\n\n\n  // Navigation methods\n  onProductClick(product: any) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n\n  onCategoryClick(category: any) {\n    this.router.navigate(['/shop'], {\n      queryParams: { category: category.name.toLowerCase() }\n    });\n  }\n\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n\n  // Instagram-style interaction methods\n  toggleLike(post: InstagramPost) {\n    if (!post._id) return;\n\n    // Optimistic update\n    const wasLiked = post.isLiked;\n    post.isLiked = !post.isLiked;\n    post.analytics.likes += post.isLiked ? 1 : -1;\n\n    this.postsService.toggleLike(post._id).subscribe({\n      next: (result) => {\n        if (result.success && result.likesCount !== undefined) {\n          post.analytics.likes = result.likesCount;\n          console.log('✅ Post like toggled successfully');\n        } else {\n          // Revert on failure\n          post.isLiked = wasLiked;\n          post.analytics.likes += wasLiked ? 1 : -1;\n          console.error('❌ Failed to toggle like:', result.message);\n        }\n      },\n      error: (error) => {\n        // Revert on error\n        post.isLiked = wasLiked;\n        post.analytics.likes += wasLiked ? 1 : -1;\n        console.error('❌ Error toggling like:', error);\n      }\n    });\n  }\n\n  toggleSave(post: any) {\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n\n    // Optimistic update\n    post.isSaved = !post.isSaved;\n\n    this.buttonActionsService.savePost(postId).subscribe({\n      next: (result) => {\n        if (!result.success) {\n          // Revert on failure\n          post.isSaved = !post.isSaved;\n          console.error('Failed to save post:', result.message);\n        }\n      },\n      error: (error) => {\n        // Revert on error\n        post.isSaved = !post.isSaved;\n        console.error('Error saving post:', error);\n      }\n    });\n  }\n\n  sharePost(post: any) {\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n\n    this.buttonActionsService.sharePost(postId).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Post shared successfully');\n        } else {\n          console.error('Failed to share post:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error sharing post:', error);\n      }\n    });\n  }\n\n  focusCommentInput(post: any) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n\n  addComment(post: InstagramPost) {\n    if (!this.newComment || !this.newComment.trim()) return;\n    if (!post._id) return;\n\n    const commentText = this.newComment.trim();\n\n    this.postsService.addComment(post._id, commentText).subscribe({\n      next: (result) => {\n        if (result.success && result.comment) {\n          // Add comment to local state\n          post.comments.push(result.comment);\n          post.analytics.comments += 1;\n          this.newComment = '';\n          console.log('✅ Comment added successfully');\n        } else {\n          console.error('❌ Failed to add comment:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('❌ Error adding comment:', error);\n      }\n    });\n  }\n\n  showProductDetails(product: any) {\n    console.log('🛍️ Show product details:', product);\n    if (product._id) {\n      this.router.navigate(['/product', product._id]);\n    } else {\n      // Toggle product preview for posts\n      product.showPreview = !product.showPreview;\n    }\n  }\n\n  // View full post\n  viewPost(post: InstagramPost) {\n    console.log('📱 View post:', post._id);\n    this.router.navigate(['/post', post._id]);\n  }\n\n  // View user profile\n  viewUserProfile(user: any) {\n    console.log('👤 View user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  // Toggle product tags visibility\n  toggleProductTags(post: InstagramPost) {\n    post.showProductTags = !post.showProductTags;\n    console.log('🏷️ Product tags toggled:', post.showProductTags);\n  }\n\n  // E-commerce methods\n  buyNow(product: any) {\n    console.log('Buy now:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.buyNow({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Buy now successful, redirecting to checkout');\n          // Navigation will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n          // Fallback to payment modal if needed\n          this.showPaymentModalFallback(product);\n        }\n      },\n      error: (error) => {\n        console.error('Error in buy now:', error);\n        // Fallback to payment modal\n        this.showPaymentModalFallback(product);\n      }\n    });\n  }\n\n  private showPaymentModalFallback(product: any) {\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], { queryParams: { returnUrl: '/home' } });\n      return;\n    }\n\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + (product.price * 0.18)\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n\n    this.showPaymentModal = true;\n  }\n\n  addToWishlist(product: any) {\n    console.log('Add to wishlist:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.addToWishlist({\n      productId: productId,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully');\n        } else {\n          console.error('Failed to add to wishlist:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n\n  addToCart(product: any) {\n    console.log('Add to cart:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.addToCart({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Product added to cart successfully');\n        } else {\n          console.error('Failed to add to cart:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n\n  followUser(user: any) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });\n  }\n\n  toggleComments(post: any) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n\n  // Utility methods\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatLikesCount(likes: number): string {\n    return this.formatNumber(likes);\n  }\n\n  getTimeAgo(date: Date | string): string {\n    if (!date) return 'Unknown';\n\n    let dateObj: Date;\n\n    // Handle different date formats\n    if (typeof date === 'string') {\n      dateObj = new Date(date);\n    } else if (date instanceof Date) {\n      dateObj = date;\n    } else {\n      return 'Unknown';\n    }\n\n    // Check if date is valid\n    if (isNaN(dateObj.getTime())) {\n      return 'Unknown';\n    }\n\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n\n  trackByPostId(index: number, post: any): string {\n    return post.id;\n  }\n\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult: any) {\n    this.showPaymentModal = false;\n\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n\n  private generateOrderId(): string {\n    return 'ORD' + Date.now().toString();\n  }\n\n  // Enhanced product tag methods\n  onProductTagClick(productTag: any): void {\n    if (!productTag) return;\n\n    const navigationType = productTag.navigationType || 'product';\n    const targetData = productTag.product || productTag.category || productTag.vendor || productTag.brand;\n\n    switch (navigationType) {\n      case 'product':\n        this.navigateToProduct(targetData);\n        break;\n      case 'category':\n        this.navigateToCategory(targetData);\n        break;\n      case 'vendor':\n        this.navigateToVendor(targetData);\n        break;\n      case 'brand':\n        this.navigateToBrand(targetData);\n        break;\n      default:\n        this.navigateToProduct(targetData);\n    }\n  }\n\n  private navigateToProduct(product: any): void {\n    if (product?._id || product?.id) {\n      const productId = product._id || product.id;\n      this.router.navigate(['/product', productId]);\n    }\n  }\n\n  private navigateToCategory(category: any): void {\n    if (category?.slug || category?.name) {\n      const categorySlug = category.slug || category.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: { category: categorySlug }\n      });\n    }\n  }\n\n  private navigateToVendor(vendor: any): void {\n    if (vendor?._id || vendor?.id || vendor?.username) {\n      const vendorId = vendor._id || vendor.id || vendor.username;\n      this.router.navigate(['/vendor', vendorId]);\n    }\n  }\n\n  private navigateToBrand(brand: any): void {\n    if (brand?.slug || brand?.name) {\n      const brandSlug = brand.slug || brand.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: { brand: brandSlug }\n      });\n    }\n  }\n\n  getTagTooltip(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n    const name = this.getTagName(productTag);\n\n    switch (type) {\n      case 'product':\n        return `View product: ${name}`;\n      case 'category':\n        return `Browse category: ${name}`;\n      case 'vendor':\n        return `Visit vendor: ${name}`;\n      case 'brand':\n        return `Shop brand: ${name}`;\n      default:\n        return `View: ${name}`;\n    }\n  }\n\n  getTagIcon(navigationType: string): string {\n    switch (navigationType) {\n      case 'product':\n        return 'fas fa-tag';\n      case 'category':\n        return 'fas fa-th-large';\n      case 'vendor':\n        return 'fas fa-store';\n      case 'brand':\n        return 'fas fa-crown';\n      default:\n        return 'fas fa-tag';\n    }\n  }\n\n  getTagName(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.name || 'Product';\n      case 'category':\n        return productTag.category?.name || 'Category';\n      case 'vendor':\n        return productTag.vendor?.fullName || productTag.vendor?.username || 'Vendor';\n      case 'brand':\n        return productTag.brand?.name || 'Brand';\n      default:\n        return productTag.product?.name || 'Item';\n    }\n  }\n\n  getTagSubtitle(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.price ? `$${productTag.product.price}` : '';\n      case 'category':\n        return productTag.category?.productCount ? `${productTag.category.productCount} products` : 'Browse category';\n      case 'vendor':\n        return productTag.vendor?.location || 'Visit store';\n      case 'brand':\n        return productTag.brand?.productCount ? `${productTag.brand.productCount} products` : 'Shop brand';\n      default:\n        return '';\n    }\n  }\n\n  getTagImageUrl(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n      case 'category':\n        return productTag.category?.image || '/assets/images/category-placeholder.jpg';\n      case 'vendor':\n        return productTag.vendor?.avatar || '/assets/images/vendor-placeholder.jpg';\n      case 'brand':\n        return productTag.brand?.logo || '/assets/images/brand-placeholder.jpg';\n      default:\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n    }\n  }\n}\n", "<div class=\"instagram-home-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Loading amazing fashion...</p>\n  </div>\n\n  <!-- Instagram-style Layout -->\n  <div *ngIf=\"!isLoading\" class=\"instagram-layout\">\n    <!-- Main Feed -->\n    <div class=\"main-feed\">\n      <!-- Instagram Stories Section -->\n      <section class=\"stories-section\">\n        <app-view-add-stories></app-view-add-stories>\n      </section>\n\n      <!-- Instagram-style Posts Feed -->\n      <section class=\"posts-feed\">\n        <article *ngFor=\"let post of instagramPosts; trackBy: trackByPostId\" class=\"instagram-post\">\n          <!-- Post Header -->\n          <header class=\"post-header\">\n            <div class=\"user-info\">\n              <img [src]=\"post.user.avatar\" [alt]=\"post.user.username\" class=\"user-avatar\">\n              <div class=\"user-details\">\n                <h3 class=\"username\">{{ post.user.username }}</h3>\n                <span class=\"location\" *ngIf=\"post.location\">{{ post.location }}</span>\n              </div>\n            </div>\n            <button class=\"more-options\">\n              <i class=\"fas fa-ellipsis-h\"></i>\n            </button>\n          </header>\n\n          <!-- Post Media -->\n          <div class=\"post-media\">\n            <img [src]=\"post.mediaUrl\" [alt]=\"post.caption\" class=\"post-image\">\n\n            <!-- Central Product Tags Button -->\n            <div class=\"product-tags-overlay\" *ngIf=\"post.products && post.products.length > 0\">\n              <button class=\"product-tag-btn\" (click)=\"toggleProductTags(post)\"\n                      [class.active]=\"post.showProductTags\"\n                      title=\"View Products\">\n                <i class=\"fas fa-shopping-bag\"></i>\n              </button>\n            </div>\n\n            <!-- Enhanced Product Tags Overlay -->\n            <div class=\"product-tags-overlay\" *ngIf=\"post.products && post.products.length > 0 && post.showProductTags\">\n              <button *ngFor=\"let productTag of post.products\"\n                      class=\"product-tag\"\n                      [class]=\"'tag-type-' + (productTag.navigationType || 'product')\"\n                      [style.left.%]=\"productTag.position?.x || 50\"\n                      [style.top.%]=\"productTag.position?.y || 50\"\n                      (click)=\"onProductTagClick(productTag)\"\n                      [title]=\"getTagTooltip(productTag)\">\n                <div class=\"tag-dot\" [class]=\"'dot-' + (productTag.navigationType || 'product')\">\n                  <i class=\"tag-icon\" [class]=\"getTagIcon(productTag.navigationType || 'product')\"></i>\n                </div>\n                <div class=\"product-preview\" *ngIf=\"productTag.showPreview\">\n                  <img [src]=\"getTagImageUrl(productTag)\" [alt]=\"getTagName(productTag)\">\n                  <div class=\"product-info\">\n                    <span class=\"product-name\">{{ getTagName(productTag) }}</span>\n                    <span class=\"product-price\">{{ getTagSubtitle(productTag) }}</span>\n                  </div>\n                </div>\n              </button>\n            </div>\n          </div>\n\n          <!-- Post Actions -->\n          <div class=\"post-actions\">\n            <div class=\"primary-actions\">\n              <button class=\"action-btn like-btn\" [class.liked]=\"post.isLiked\" (click)=\"toggleLike(post)\">\n                <i [class]=\"post.isLiked ? 'fas fa-heart' : 'far fa-heart'\"></i>\n              </button>\n              <button class=\"action-btn comment-btn\" (click)=\"focusCommentInput(post)\">\n                <i class=\"far fa-comment\"></i>\n              </button>\n              <button class=\"action-btn share-btn\" (click)=\"sharePost(post)\">\n                <i class=\"far fa-paper-plane\"></i>\n              </button>\n            </div>\n            <button class=\"action-btn save-btn\" [class.saved]=\"post.isSaved\" (click)=\"toggleSave(post)\">\n              <i [class]=\"post.isSaved ? 'fas fa-bookmark' : 'far fa-bookmark'\"></i>\n            </button>\n          </div>\n\n          <!-- Likes Count -->\n          <div class=\"likes-section\" *ngIf=\"post.analytics.likes > 0\">\n            <span class=\"likes-count\">{{ formatLikesCount(post.analytics.likes) }} likes</span>\n          </div>\n\n          <!-- Post Caption -->\n          <div class=\"post-caption\">\n            <span class=\"username\">{{ post.user.username }}</span>\n            <span class=\"caption-text\">{{ post.caption }}</span>\n          </div>\n\n          <!-- E-commerce Actions -->\n          <div class=\"ecommerce-actions\" *ngIf=\"post.products && post.products.length > 0\">\n            <div class=\"product-showcase\">\n              <div *ngFor=\"let product of post.products.slice(0, 2)\" class=\"featured-product\">\n                <img [src]=\"product.image\" [alt]=\"product.name\" class=\"product-thumbnail\">\n                <div class=\"product-details\">\n                  <span class=\"product-name\">{{ product.name }}</span>\n                  <span class=\"product-price\">{{ formatPrice(product.price) }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"shopping-buttons\">\n              <button class=\"shop-btn buy-btn\" (click)=\"buyNow(post.products[0])\">\n                <i class=\"fas fa-bolt\"></i>\n                Buy Now\n              </button>\n              <button class=\"shop-btn wishlist-btn\" (click)=\"addToWishlist(post.products[0])\">\n                <i class=\"far fa-heart\"></i>\n                Wishlist\n              </button>\n              <button class=\"shop-btn cart-btn\" (click)=\"addToCart(post.products[0])\">\n                <i class=\"fas fa-shopping-cart\"></i>\n                Add to Cart\n              </button>\n            </div>\n          </div>\n\n          <!-- Comments Preview -->\n          <div class=\"comments-preview\" *ngIf=\"post.comments && post.comments.length > 0\">\n            <button class=\"view-comments-btn\" (click)=\"toggleComments(post)\">\n              View all {{ post.comments.length }} comments\n            </button>\n            <div class=\"recent-comments\">\n              <div *ngFor=\"let comment of post.comments.slice(0, 2)\" class=\"comment\">\n                <span class=\"comment-username\">{{ comment.username }}</span>\n                <span class=\"comment-text\">{{ comment.text }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Post Time -->\n          <div class=\"post-time\">\n            {{ getTimeAgo(post.createdAt) }}\n          </div>\n\n          <!-- Add Comment -->\n          <div class=\"add-comment-section\">\n            <input type=\"text\" placeholder=\"Add a comment...\" class=\"comment-input\"\n                   [(ngModel)]=\"newComment\" (keyup.enter)=\"addComment(post)\">\n            <button class=\"post-comment-btn\" (click)=\"addComment(post)\"\n                    [disabled]=\"!newComment || !newComment.trim()\">Post</button>\n          </div>\n        </article>\n      </section>\n    </div>\n\n    <!-- Home Sidebar Component with Existing Components -->\n    <app-sidebar\n      [currentUser]=\"currentUser\"\n      [featuredProducts]=\"featuredProducts\"\n      [trendingProducts]=\"trendingProducts\"\n      [newArrivals]=\"newArrivals\"\n      [summerCollection]=\"summerCollection\"\n      [categories]=\"categories\">\n    </app-sidebar>\n  </div>\n\n  <!-- Payment Modal -->\n  <app-payment-modal\n    [isVisible]=\"showPaymentModal\"\n    [paymentData]=\"paymentModalData\"\n    (close)=\"onPaymentModalClose()\"\n    (paymentCompleted)=\"onPaymentCompleted($event)\">\n  </app-payment-modal>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAiBC,YAAY,QAAQ,iBAAiB;AAKtD,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,qBAAqB,QAA0B,qEAAqE;AAC7H,SAASC,gBAAgB,QAAQ,4CAA4C;;;;;;;;;;;ICR3EC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAC/BH,EAD+B,CAAAI,YAAA,EAAI,EAC7B;;;;;IAoBQJ,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA1BJ,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAmB;;;;;;IAclER,EADF,CAAAC,cAAA,cAAoF,iBAGpD;IAFED,EAAA,CAAAS,UAAA,mBAAAC,sEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,OAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAE,iBAAA,CAAAV,OAAA,CAAuB;IAAA,EAAC;IAG/DP,EAAA,CAAAE,SAAA,YAAmC;IAEvCF,EADE,CAAAI,YAAA,EAAS,EACL;;;;IAJIJ,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAkB,WAAA,WAAAX,OAAA,CAAAY,eAAA,CAAqC;;;;;IAkB3CnB,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,cAAuE;IAErEF,EADF,CAAAC,cAAA,cAA0B,eACG;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9DJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAAkC;IAACL,EAAnC,CAAAoB,UAAA,QAAAL,MAAA,CAAAM,cAAA,CAAAC,aAAA,GAAAtB,EAAA,CAAAuB,aAAA,CAAkC,QAAAR,MAAA,CAAAS,UAAA,CAAAF,aAAA,EAA+B;IAEzCtB,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAS,MAAA,CAAAS,UAAA,CAAAF,aAAA,EAA4B;IAC3BtB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAS,MAAA,CAAAU,cAAA,CAAAH,aAAA,EAAgC;;;;;;IAdlEtB,EAAA,CAAAC,cAAA,iBAM4C;IADpCD,EAAA,CAAAS,UAAA,mBAAAiB,+EAAA;MAAA,MAAAJ,aAAA,GAAAtB,EAAA,CAAAW,aAAA,CAAAgB,GAAA,EAAAb,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAa,iBAAA,CAAAN,aAAA,CAA6B;IAAA,EAAC;IAE7CtB,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,SAAA,YAAqF;IACvFF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAA6B,UAAA,IAAAC,4DAAA,kBAA4D;IAO9D9B,EAAA,CAAAI,YAAA,EAAS;;;;;IAfDJ,EAAA,CAAA+B,UAAA,gBAAAT,aAAA,CAAAU,cAAA,eAAgE;IAEhEhC,EADA,CAAAiC,WAAA,UAAAX,aAAA,CAAAY,QAAA,kBAAAZ,aAAA,CAAAY,QAAA,CAAAC,CAAA,aAA6C,SAAAb,aAAA,CAAAY,QAAA,kBAAAZ,aAAA,CAAAY,QAAA,CAAAE,CAAA,aACD;IAE5CpC,EAAA,CAAAoB,UAAA,UAAAL,MAAA,CAAAsB,aAAA,CAAAf,aAAA,EAAmC;IACpBtB,EAAA,CAAAK,SAAA,EAA2D;IAA3DL,EAAA,CAAA+B,UAAA,WAAAT,aAAA,CAAAU,cAAA,eAA2D;IAC1DhC,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAA+B,UAAA,CAAAhB,MAAA,CAAAuB,UAAA,CAAAhB,aAAA,CAAAU,cAAA,eAA4D;IAEpDhC,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAoB,UAAA,SAAAE,aAAA,CAAAiB,WAAA,CAA4B;;;;;IAX9DvC,EAAA,CAAAC,cAAA,cAA4G;IAC1GD,EAAA,CAAA6B,UAAA,IAAAW,sDAAA,sBAM4C;IAY9CxC,EAAA,CAAAI,YAAA,EAAM;;;;IAlB2BJ,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAoB,UAAA,YAAAb,OAAA,CAAAkC,QAAA,CAAgB;;;;;IAyCjDzC,EADF,CAAAC,cAAA,cAA4D,eAChC;IAAAD,EAAA,CAAAG,MAAA,GAAkD;IAC9EH,EAD8E,CAAAI,YAAA,EAAO,EAC/E;;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAA0C,kBAAA,KAAA3B,MAAA,CAAA4B,gBAAA,CAAApC,OAAA,CAAAqC,SAAA,CAAAC,KAAA,YAAkD;;;;;IAY1E7C,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,cAA0E;IAExEF,EADF,CAAAC,cAAA,cAA6B,eACA;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAAqB;IAACL,EAAtB,CAAAoB,UAAA,QAAA0B,UAAA,CAAAC,KAAA,EAAA/C,EAAA,CAAAuB,aAAA,CAAqB,QAAAuB,UAAA,CAAAE,IAAA,CAAqB;IAElBhD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAwC,UAAA,CAAAE,IAAA,CAAkB;IACjBhD,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAS,MAAA,CAAAkC,WAAA,CAAAH,UAAA,CAAAI,KAAA,EAAgC;;;;;;IALlElD,EADF,CAAAC,cAAA,cAAiF,cACjD;IAC5BD,EAAA,CAAA6B,UAAA,IAAAsB,mDAAA,kBAAgF;IAOlFnD,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAA8B,iBACwC;IAAnCD,EAAA,CAAAS,UAAA,mBAAA2C,sEAAA;MAAApD,EAAA,CAAAW,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,OAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAuC,MAAA,CAAA/C,OAAA,CAAAkC,QAAA,CAAqB,CAAC,EAAE;IAAA,EAAC;IACjEzC,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAAgF;IAA1CD,EAAA,CAAAS,UAAA,mBAAA8C,sEAAA;MAAAvD,EAAA,CAAAW,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,OAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAyC,aAAA,CAAAjD,OAAA,CAAAkC,QAAA,CAA4B,CAAC,EAAE;IAAA,EAAC;IAC7EzC,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAwE;IAAtCD,EAAA,CAAAS,UAAA,mBAAAgD,uEAAA;MAAAzD,EAAA,CAAAW,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,OAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAA2C,SAAA,CAAAnD,OAAA,CAAAkC,QAAA,CAAwB,CAAC,EAAE;IAAA,EAAC;IACrEzC,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAG,MAAA,qBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IAtBuBJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAoB,UAAA,YAAAb,OAAA,CAAAkC,QAAA,CAAAkB,KAAA,OAA4B;;;;;IA+BnD3D,EADF,CAAAC,cAAA,cAAuE,eACtC;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5DJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAC/CH,EAD+C,CAAAI,YAAA,EAAO,EAChD;;;;IAF2BJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAsD,WAAA,CAAAC,QAAA,CAAsB;IAC1B7D,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAsD,WAAA,CAAAE,IAAA,CAAkB;;;;;;IANjD9D,EADF,CAAAC,cAAA,cAAgF,iBACb;IAA/BD,EAAA,CAAAS,UAAA,mBAAAsD,sEAAA;MAAA/D,EAAA,CAAAW,aAAA,CAAAqD,GAAA;MAAA,MAAAzD,OAAA,GAAAP,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAkD,cAAA,CAAA1D,OAAA,CAAoB;IAAA,EAAC;IAC9DP,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAA6B,UAAA,IAAAqC,mDAAA,kBAAuE;IAK3ElE,EADE,CAAAI,YAAA,EAAM,EACF;;;;IARFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAA0C,kBAAA,eAAAnC,OAAA,CAAA4D,QAAA,CAAAC,MAAA,eACF;IAE2BpE,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAoB,UAAA,YAAAb,OAAA,CAAA4D,QAAA,CAAAR,KAAA,OAA4B;;;;;;IA9GvD3D,EAHJ,CAAAC,cAAA,kBAA4F,iBAE9D,cACH;IACrBD,EAAA,CAAAE,SAAA,cAA6E;IAE3EF,EADF,CAAAC,cAAA,cAA0B,aACH;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAA6B,UAAA,IAAAwC,6CAAA,mBAA6C;IAEjDrE,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,YAAiC;IAErCF,EADE,CAAAI,YAAA,EAAS,EACF;IAGTJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAE,SAAA,eAAmE;IAYnEF,EATA,CAAA6B,UAAA,KAAAyC,6CAAA,kBAAoF,KAAAC,6CAAA,kBASwB;IAoB9GvE,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAC,cAAA,eAA0B,eACK,kBACiE;IAA3BD,EAAA,CAAAS,UAAA,mBAAA+D,gEAAA;MAAA,MAAAjE,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAA8D,GAAA,EAAA3D,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAA2D,UAAA,CAAAnE,OAAA,CAAgB;IAAA,EAAC;IACzFP,EAAA,CAAAE,SAAA,SAAgE;IAClEF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAyE;IAAlCD,EAAA,CAAAS,UAAA,mBAAAkE,gEAAA;MAAA,MAAApE,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAA8D,GAAA,EAAA3D,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAA6D,iBAAA,CAAArE,OAAA,CAAuB;IAAA,EAAC;IACtEP,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA+D;IAA1BD,EAAA,CAAAS,UAAA,mBAAAoE,gEAAA;MAAA,MAAAtE,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAA8D,GAAA,EAAA3D,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAA+D,SAAA,CAAAvE,OAAA,CAAe;IAAA,EAAC;IAC5DP,EAAA,CAAAE,SAAA,aAAkC;IAEtCF,EADE,CAAAI,YAAA,EAAS,EACL;IACNJ,EAAA,CAAAC,cAAA,kBAA4F;IAA3BD,EAAA,CAAAS,UAAA,mBAAAsE,gEAAA;MAAA,MAAAxE,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAA8D,GAAA,EAAA3D,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAAiE,UAAA,CAAAzE,OAAA,CAAgB;IAAA,EAAC;IACzFP,EAAA,CAAAE,SAAA,SAAsE;IAE1EF,EADE,CAAAI,YAAA,EAAS,EACL;IAGNJ,EAAA,CAAA6B,UAAA,KAAAoD,6CAAA,kBAA4D;IAM1DjF,EADF,CAAAC,cAAA,eAA0B,gBACD;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAC/CH,EAD+C,CAAAI,YAAA,EAAO,EAChD;IA8BNJ,EA3BA,CAAA6B,UAAA,KAAAqD,6CAAA,mBAAiF,KAAAC,6CAAA,kBA2BD;IAahFnF,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EADF,CAAAC,cAAA,eAAiC,iBAEkC;IAA1DD,EAAA,CAAAoF,gBAAA,2BAAAC,uEAAAC,MAAA;MAAAtF,EAAA,CAAAW,aAAA,CAAA8D,GAAA;MAAA,MAAA1D,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAuF,kBAAA,CAAAxE,MAAA,CAAAyE,UAAA,EAAAF,MAAA,MAAAvE,MAAA,CAAAyE,UAAA,GAAAF,MAAA;MAAA,OAAAtF,EAAA,CAAAgB,WAAA,CAAAsE,MAAA;IAAA,EAAwB;IAACtF,EAAA,CAAAS,UAAA,yBAAAgF,qEAAA;MAAA,MAAAlF,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAA8D,GAAA,EAAA3D,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAAeD,MAAA,CAAA2E,UAAA,CAAAnF,OAAA,CAAgB;IAAA,EAAC;IADhEP,EAAA,CAAAI,YAAA,EACiE;IACjEJ,EAAA,CAAAC,cAAA,kBACuD;IADtBD,EAAA,CAAAS,UAAA,mBAAAkF,gEAAA;MAAA,MAAApF,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAA8D,GAAA,EAAA3D,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAgB,WAAA,CAASD,MAAA,CAAA2E,UAAA,CAAAnF,OAAA,CAAgB;IAAA,EAAC;IACJP,EAAA,CAAAG,MAAA,YAAI;IAE/DH,EAF+D,CAAAI,YAAA,EAAS,EAChE,EACE;;;;;IAhICJ,EAAA,CAAAK,SAAA,GAAwB;IAACL,EAAzB,CAAAoB,UAAA,QAAAb,OAAA,CAAAqF,IAAA,CAAAC,MAAA,EAAA7F,EAAA,CAAAuB,aAAA,CAAwB,QAAAhB,OAAA,CAAAqF,IAAA,CAAA/B,QAAA,CAA2B;IAEjC7D,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAqF,IAAA,CAAA/B,QAAA,CAAwB;IACrB7D,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAAC,QAAA,CAAmB;IAU1CR,EAAA,CAAAK,SAAA,GAAqB;IAACL,EAAtB,CAAAoB,UAAA,QAAAb,OAAA,CAAAuF,QAAA,EAAA9F,EAAA,CAAAuB,aAAA,CAAqB,QAAAhB,OAAA,CAAAwF,OAAA,CAAqB;IAGZ/F,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAAkC,QAAA,IAAAlC,OAAA,CAAAkC,QAAA,CAAA2B,MAAA,KAA+C;IAS/CpE,EAAA,CAAAK,SAAA,EAAuE;IAAvEL,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAAkC,QAAA,IAAAlC,OAAA,CAAAkC,QAAA,CAAA2B,MAAA,QAAA7D,OAAA,CAAAY,eAAA,CAAuE;IAyBpEnB,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkB,WAAA,UAAAX,OAAA,CAAAyF,OAAA,CAA4B;IAC3DhG,EAAA,CAAAK,SAAA,EAAwD;IAAxDL,EAAA,CAAA+B,UAAA,CAAAxB,OAAA,CAAAyF,OAAA,mCAAwD;IAS3BhG,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkB,WAAA,UAAAX,OAAA,CAAA0F,OAAA,CAA4B;IAC3DjG,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAA+B,UAAA,CAAAxB,OAAA,CAAA0F,OAAA,yCAA8D;IAKzCjG,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAAqC,SAAA,CAAAC,KAAA,KAA8B;IAMjC7C,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAqF,IAAA,CAAA/B,QAAA,CAAwB;IACpB7D,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAwF,OAAA,CAAkB;IAIf/F,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAAkC,QAAA,IAAAlC,OAAA,CAAAkC,QAAA,CAAA2B,MAAA,KAA+C;IA2BhDpE,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAoB,UAAA,SAAAb,OAAA,CAAA4D,QAAA,IAAA5D,OAAA,CAAA4D,QAAA,CAAAC,MAAA,KAA+C;IAc5EpE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAA0C,kBAAA,MAAA3B,MAAA,CAAAmF,UAAA,CAAA3F,OAAA,CAAA4F,SAAA,OACF;IAKSnG,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAoG,gBAAA,YAAArF,MAAA,CAAAyE,UAAA,CAAwB;IAEvBxF,EAAA,CAAAK,SAAA,EAA8C;IAA9CL,EAAA,CAAAoB,UAAA,cAAAL,MAAA,CAAAyE,UAAA,KAAAzE,MAAA,CAAAyE,UAAA,CAAAa,IAAA,GAA8C;;;;;IAxI5DrG,EAJJ,CAAAC,cAAA,aAAiD,aAExB,iBAEY;IAC/BD,EAAA,CAAAE,SAAA,2BAA6C;IAC/CF,EAAA,CAAAI,YAAA,EAAU;IAGVJ,EAAA,CAAAC,cAAA,iBAA4B;IAC1BD,EAAA,CAAA6B,UAAA,IAAAyE,sCAAA,wBAA4F;IAsIhGtG,EADE,CAAAI,YAAA,EAAU,EACN;IAGNJ,EAAA,CAAAE,SAAA,sBAOc;IAChBF,EAAA,CAAAI,YAAA,EAAM;;;;IAjJ0BJ,EAAA,CAAAK,SAAA,GAAmB;IAAAL,EAAnB,CAAAoB,UAAA,YAAAL,MAAA,CAAAwF,cAAA,CAAmB,iBAAAxF,MAAA,CAAAyF,aAAA,CAAsB;IA0IrExG,EAAA,CAAAK,SAAA,EAA2B;IAK3BL,EALA,CAAAoB,UAAA,gBAAAL,MAAA,CAAA0F,WAAA,CAA2B,qBAAA1F,MAAA,CAAA2F,gBAAA,CACU,qBAAA3F,MAAA,CAAA4F,gBAAA,CACA,gBAAA5F,MAAA,CAAA6F,WAAA,CACV,qBAAA7F,MAAA,CAAA8F,gBAAA,CACU,eAAA9F,MAAA,CAAA+F,UAAA,CACZ;;;AD7I/B,OAAM,MAAOC,aAAa;EAoBxBC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,oBAA0C,EAC1CC,YAA0B;IAJ1B,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IAxBtB,KAAAX,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAU,aAAa,GAAU,EAAE;IACzB,KAAAR,UAAU,GAAU,EAAE;IACtB,KAAAS,SAAS,GAAG,IAAI;IAChB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAjB,cAAc,GAAU,EAAE;IAC1B,KAAAM,gBAAgB,GAAU,EAAE;IAC5B,KAAAY,cAAc,GAAU,EAAE;IAC1B,KAAAhB,WAAW,GAAQ,IAAI;IACvB,KAAAjB,UAAU,GAAG,EAAE;IAEf;IACA,KAAAkC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,gBAAgB,GAA4B,IAAI;EAQ7C;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAD,eAAeA,CAAA;IACb,IAAI,CAACX,WAAW,CAACa,YAAY,CAACC,SAAS,CAACrC,IAAI,IAAG;MAC7C,IAAI,CAAC4B,eAAe,GAAG,CAAC,CAAC5B,IAAI;MAC7B,IAAI,CAACa,WAAW,GAAGb,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAmC,iBAAiBA,CAAA;IACf,IAAI,CAACG,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAF,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACb,YAAY,CAACgB,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAACJ,SAAS,CAAC;MAC1CK,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACjC,cAAc,GAAGgC,QAAQ,CAACE,KAAK;UACpCC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACpC,cAAc,CAACnC,MAAM,CAAC;SACrE,MAAM;UACL,IAAI,CAACmC,cAAc,GAAG,EAAE;UACxBmC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;MAElD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACrC,cAAc,GAAG,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA4B,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACjB,cAAc,CAAC2B,mBAAmB,CAAC,QAAQ,CAAC,CAACZ,SAAS,CAAC;MAC1DK,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC1B,gBAAgB,GAAG0B,QAAQ,CAAC9F,QAAQ,IAAI,EAAE;QAC/CiG,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC9B,gBAAgB,CAACzC,MAAM,CAAC;MAC1E,CAAC;MACDwE,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC/B,gBAAgB,GAAG,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAuB,kBAAkBA,CAAA;IAChB;IACA;IACA,IAAI,CAACX,cAAc,GAAG,EAAE;IACxBiB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAClB,cAAc,CAACrD,MAAM,CAAC;EACtE;EAEMyD,YAAYA,CAAA;IAAA,IAAAiB,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI;QACFD,KAAI,CAACvB,SAAS,GAAG,IAAI;QAErB;QACA,MAAM,CAACyB,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACvDN,KAAI,CAAC5B,cAAc,CAACmC,mBAAmB,EAAE,CAACC,SAAS,EAAE,EACrDR,KAAI,CAAC5B,cAAc,CAACqC,mBAAmB,EAAE,CAACD,SAAS,EAAE,EACrDR,KAAI,CAAC5B,cAAc,CAACsC,cAAc,EAAE,CAACF,SAAS,EAAE,CACjD,CAAC;QAEFR,KAAI,CAACpC,gBAAgB,GAAG,CAAEsC,QAAgB,EAAEvG,QAAQ,IAAKuG,QAAgB,EAAES,IAAI,IAAIT,QAAQ,IAAI,EAAE,EAAErF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9GmF,KAAI,CAACnC,gBAAgB,GAAG,CAAEsC,QAAgB,EAAExG,QAAQ,IAAKwG,QAAgB,EAAEQ,IAAI,IAAIR,QAAQ,IAAI,EAAE,EAAEtF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9GmF,KAAI,CAAClC,WAAW,GAAG,CAAEsC,QAAgB,EAAEzG,QAAQ,IAAKyG,QAAgB,EAAEO,IAAI,IAAIP,QAAQ,IAAI,EAAE,EAAEvF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEzG;QACA+E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,KAAI,CAACpC,gBAAgB,CAACtC,MAAM,CAAC;QACxEsE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,KAAI,CAACnC,gBAAgB,CAACvC,MAAM,CAAC;QACxEsE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,KAAI,CAAClC,WAAW,CAACxC,MAAM,CAAC;QAE9D;QACA0E,KAAI,CAACY,cAAc,EAAE;QAErB;QACAZ,KAAI,CAACa,iBAAiB,EAAE;OAEzB,CAAC,OAAOf,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;QACAE,KAAI,CAACpC,gBAAgB,GAAG,EAAE;QAC1BoC,KAAI,CAACnC,gBAAgB,GAAG,EAAE;QAC1BmC,KAAI,CAAClC,WAAW,GAAG,EAAE;OACtB,SAAS;QACRkC,KAAI,CAACvB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAmC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACxC,cAAc,CAAC0C,aAAa,EAAE,CAAC3B,SAAS,CAAC;MAC5CK,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACzB,UAAU,GAAGyB,QAAQ,CAACkB,IAAI,IAAI,EAAE;QACrCf,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC7B,UAAU,CAAC1C,MAAM,CAAC;MAC7D,CAAC;MACDwE,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAAC9B,UAAU,GAAG,EAAE;MACtB;KACD,CAAC;EACJ;EAEA6C,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACtC,YAAY,CAACwC,gBAAgB,CAAC,CAAC,CAAC,CAAC5B,SAAS,CAAC;MAC9CK,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAClB,aAAa,GAAGiB,QAAQ,CAACE,KAAK;UACnCC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACrB,aAAa,CAAClD,MAAM,CAAC;SACnE,MAAM;UACL,IAAI,CAACkD,aAAa,GAAG,EAAE;UACvBoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAEjD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACtB,aAAa,GAAG,EAAE;MACzB;KACD,CAAC;EACJ;EAIA;EACAwC,cAAcA,CAACC,OAAY;IACzB,IAAI,CAAC9C,MAAM,CAAC+C,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE,CAAC,CAAC;EAC/D;EAEAC,eAAeA,CAACC,QAAa;IAC3B,IAAI,CAACnD,MAAM,CAAC+C,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BK,WAAW,EAAE;QAAED,QAAQ,EAAEA,QAAQ,CAACpH,IAAI,CAACsH,WAAW;MAAE;KACrD,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACtD,MAAM,CAAC+C,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA;EACAtF,UAAUA,CAAC8F,IAAmB;IAC5B,IAAI,CAACA,IAAI,CAACP,GAAG,EAAE;IAEf;IACA,MAAMQ,QAAQ,GAAGD,IAAI,CAACxE,OAAO;IAC7BwE,IAAI,CAACxE,OAAO,GAAG,CAACwE,IAAI,CAACxE,OAAO;IAC5BwE,IAAI,CAAC5H,SAAS,CAACC,KAAK,IAAI2H,IAAI,CAACxE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;IAE7C,IAAI,CAACqB,YAAY,CAAC3C,UAAU,CAAC8F,IAAI,CAACP,GAAG,CAAC,CAAChC,SAAS,CAAC;MAC/CK,IAAI,EAAGoC,MAAM,IAAI;QACf,IAAIA,MAAM,CAAClC,OAAO,IAAIkC,MAAM,CAACC,UAAU,KAAKC,SAAS,EAAE;UACrDJ,IAAI,CAAC5H,SAAS,CAACC,KAAK,GAAG6H,MAAM,CAACC,UAAU;UACxCjC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;SAChD,MAAM;UACL;UACA6B,IAAI,CAACxE,OAAO,GAAGyE,QAAQ;UACvBD,IAAI,CAAC5H,SAAS,CAACC,KAAK,IAAI4H,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;UACzC/B,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAE8B,MAAM,CAACG,OAAO,CAAC;;MAE7D,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACf;QACA4B,IAAI,CAACxE,OAAO,GAAGyE,QAAQ;QACvBD,IAAI,CAAC5H,SAAS,CAACC,KAAK,IAAI4H,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;QACzC/B,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEA5D,UAAUA,CAACwF,IAAS;IAClB,IAAI,CAACA,IAAI,CAACN,EAAE,IAAI,CAACM,IAAI,CAACP,GAAG,EAAE;IAE3B,MAAMa,MAAM,GAAGN,IAAI,CAACN,EAAE,IAAIM,IAAI,CAACP,GAAG;IAElC;IACAO,IAAI,CAACvE,OAAO,GAAG,CAACuE,IAAI,CAACvE,OAAO;IAE5B,IAAI,CAACmB,oBAAoB,CAAC2D,QAAQ,CAACD,MAAM,CAAC,CAAC7C,SAAS,CAAC;MACnDK,IAAI,EAAGoC,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,CAAClC,OAAO,EAAE;UACnB;UACAgC,IAAI,CAACvE,OAAO,GAAG,CAACuE,IAAI,CAACvE,OAAO;UAC5ByC,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAE8B,MAAM,CAACG,OAAO,CAAC;;MAEzD,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACf;QACA4B,IAAI,CAACvE,OAAO,GAAG,CAACuE,IAAI,CAACvE,OAAO;QAC5ByC,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;KACD,CAAC;EACJ;EAEA9D,SAASA,CAAC0F,IAAS;IACjB,IAAI,CAACA,IAAI,CAACN,EAAE,IAAI,CAACM,IAAI,CAACP,GAAG,EAAE;IAE3B,MAAMa,MAAM,GAAGN,IAAI,CAACN,EAAE,IAAIM,IAAI,CAACP,GAAG;IAElC,IAAI,CAAC7C,oBAAoB,CAACtC,SAAS,CAACgG,MAAM,CAAC,CAAC7C,SAAS,CAAC;MACpDK,IAAI,EAAGoC,MAAM,IAAI;QACf,IAAIA,MAAM,CAAClC,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;SACxC,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAE8B,MAAM,CAACG,OAAO,CAAC;;MAE1D,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;KACD,CAAC;EACJ;EAEAhE,iBAAiBA,CAAC4F,IAAS;IACzB;IACA9B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6B,IAAI,CAACN,EAAE,CAAC;EACjD;EAEAxE,UAAUA,CAAC8E,IAAmB;IAC5B,IAAI,CAAC,IAAI,CAAChF,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACa,IAAI,EAAE,EAAE;IACjD,IAAI,CAACmE,IAAI,CAACP,GAAG,EAAE;IAEf,MAAMe,WAAW,GAAG,IAAI,CAACxF,UAAU,CAACa,IAAI,EAAE;IAE1C,IAAI,CAACgB,YAAY,CAAC3B,UAAU,CAAC8E,IAAI,CAACP,GAAG,EAAEe,WAAW,CAAC,CAAC/C,SAAS,CAAC;MAC5DK,IAAI,EAAGoC,MAAM,IAAI;QACf,IAAIA,MAAM,CAAClC,OAAO,IAAIkC,MAAM,CAACO,OAAO,EAAE;UACpC;UACAT,IAAI,CAACrG,QAAQ,CAAC+G,IAAI,CAACR,MAAM,CAACO,OAAO,CAAC;UAClCT,IAAI,CAAC5H,SAAS,CAACuB,QAAQ,IAAI,CAAC;UAC5B,IAAI,CAACqB,UAAU,GAAG,EAAE;UACpBkD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;SAC5C,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAE8B,MAAM,CAACG,OAAO,CAAC;;MAE7D,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEAuC,kBAAkBA,CAACpB,OAAY;IAC7BrB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEoB,OAAO,CAAC;IACjD,IAAIA,OAAO,CAACE,GAAG,EAAE;MACf,IAAI,CAAChD,MAAM,CAAC+C,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;KAChD,MAAM;MACL;MACAF,OAAO,CAACxH,WAAW,GAAG,CAACwH,OAAO,CAACxH,WAAW;;EAE9C;EAEA;EACA6I,QAAQA,CAACZ,IAAmB;IAC1B9B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE6B,IAAI,CAACP,GAAG,CAAC;IACtC,IAAI,CAAChD,MAAM,CAAC+C,QAAQ,CAAC,CAAC,OAAO,EAAEQ,IAAI,CAACP,GAAG,CAAC,CAAC;EAC3C;EAEA;EACAoB,eAAeA,CAACzF,IAAS;IACvB8C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE/C,IAAI,CAAC/B,QAAQ,CAAC;IACnD,IAAI,CAACoD,MAAM,CAAC+C,QAAQ,CAAC,CAAC,UAAU,EAAEpE,IAAI,CAAC/B,QAAQ,CAAC,CAAC;EACnD;EAEA;EACA5C,iBAAiBA,CAACuJ,IAAmB;IACnCA,IAAI,CAACrJ,eAAe,GAAG,CAACqJ,IAAI,CAACrJ,eAAe;IAC5CuH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,IAAI,CAACrJ,eAAe,CAAC;EAChE;EAEA;EACAmC,MAAMA,CAACyG,OAAY;IACjBrB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoB,OAAO,CAAC;IAEhC,IAAI,CAACA,OAAO,CAACG,EAAE,IAAI,CAACH,OAAO,CAACE,GAAG,EAAE;IAEjC,MAAMqB,SAAS,GAAGvB,OAAO,CAACG,EAAE,IAAIH,OAAO,CAACE,GAAG;IAE3C,IAAI,CAAC7C,oBAAoB,CAAC9D,MAAM,CAAC;MAC/BgI,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAACvD,SAAS,CAAC;MACXK,IAAI,EAAGoC,MAAM,IAAI;QACf,IAAIA,MAAM,CAAClC,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAC1D;SACD,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAE8B,MAAM,CAACG,OAAO,CAAC;UACnD;UACA,IAAI,CAACY,wBAAwB,CAAC1B,OAAO,CAAC;;MAE1C,CAAC;MACDnB,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC;QACA,IAAI,CAAC6C,wBAAwB,CAAC1B,OAAO,CAAC;MACxC;KACD,CAAC;EACJ;EAEQ0B,wBAAwBA,CAAC1B,OAAY;IAC3C,IAAI,CAAC,IAAI,CAACtD,WAAW,EAAE;MACrB,IAAI,CAACQ,MAAM,CAAC+C,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QAAEK,WAAW,EAAE;UAAEqB,SAAS,EAAE;QAAO;MAAE,CAAE,CAAC;MAC9E;;IAGF;IACA,IAAI,CAAC/D,gBAAgB,GAAG;MACtBgE,MAAM,EAAE5B,OAAO,CAAC7G,KAAK;MACrB0I,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;UACN3B,EAAE,EAAEH,OAAO,CAACG,EAAE;UACdlH,IAAI,EAAE+G,OAAO,CAAC/G,IAAI;UAClBE,KAAK,EAAE6G,OAAO,CAAC7G,KAAK;UACpBqI,QAAQ,EAAE,CAAC;UACXxI,KAAK,EAAEgH,OAAO,CAAChH;SAChB,CAAC;QACF+I,QAAQ,EAAE/B,OAAO,CAAC7G,KAAK;QACvB6I,GAAG,EAAEhC,OAAO,CAAC7G,KAAK,GAAG,IAAI;QACzB8I,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAEnC,OAAO,CAAC7G,KAAK,GAAI6G,OAAO,CAAC7G,KAAK,GAAG;OACzC;MACDiJ,WAAW,EAAE;QACXnJ,IAAI,EAAE,IAAI,CAACyD,WAAW,CAAC2F,QAAQ,IAAI,IAAI,CAAC3F,WAAW,CAAC5C,QAAQ;QAC5DwI,KAAK,EAAE,IAAI,CAAC5F,WAAW,CAAC4F,KAAK;QAC7BC,KAAK,EAAE,IAAI,CAAC7F,WAAW,CAAC6F,KAAK,IAAI;;KAEpC;IAED,IAAI,CAAC5E,gBAAgB,GAAG,IAAI;EAC9B;EAEAlE,aAAaA,CAACuG,OAAY;IACxBrB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEoB,OAAO,CAAC;IAExC,IAAI,CAACA,OAAO,CAACG,EAAE,IAAI,CAACH,OAAO,CAACE,GAAG,EAAE;IAEjC,MAAMqB,SAAS,GAAGvB,OAAO,CAACG,EAAE,IAAIH,OAAO,CAACE,GAAG;IAE3C,IAAI,CAAC7C,oBAAoB,CAAC5D,aAAa,CAAC;MACtC8H,SAAS,EAAEA,SAAS;MACpBE,SAAS,EAAE;KACZ,CAAC,CAACvD,SAAS,CAAC;MACXK,IAAI,EAAGoC,MAAM,IAAI;QACf,IAAIA,MAAM,CAAClC,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;SACtD,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAE8B,MAAM,CAACG,OAAO,CAAC;;MAE/D,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEAlF,SAASA,CAACqG,OAAY;IACpBrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoB,OAAO,CAAC;IAEpC,IAAI,CAACA,OAAO,CAACG,EAAE,IAAI,CAACH,OAAO,CAACE,GAAG,EAAE;IAEjC,MAAMqB,SAAS,GAAGvB,OAAO,CAACG,EAAE,IAAIH,OAAO,CAACE,GAAG;IAE3C,IAAI,CAAC7C,oBAAoB,CAAC1D,SAAS,CAAC;MAClC4H,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAACvD,SAAS,CAAC;MACXK,IAAI,EAAGoC,MAAM,IAAI;QACf,IAAIA,MAAM,CAAClC,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;SAClD,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAE8B,MAAM,CAACG,OAAO,CAAC;;MAE3D,CAAC;MACDjC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA2D,UAAUA,CAAC3G,IAAS;IAClB8C,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE/C,IAAI,CAAC/B,QAAQ,CAAC;IAC1C;EACF;EAEA2I,oBAAoBA,CAAA;IAClB,IAAI,CAACvF,MAAM,CAAC+C,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEK,WAAW,EAAE;QAAEoC,UAAU,EAAE;MAAY;IAAE,CAAE,CAAC;EAChF;EAEAxI,cAAcA,CAACuG,IAAS;IACtB9B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,IAAI,CAACN,EAAE,CAAC;IACjD;EACF;EAEA;EACAjH,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIwJ,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC5J,KAAK,CAAC;EAClB;EAEA6J,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAvK,gBAAgBA,CAACE,KAAa;IAC5B,OAAO,IAAI,CAACkK,YAAY,CAAClK,KAAK,CAAC;EACjC;EAEAqD,UAAUA,CAACiH,IAAmB;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS;IAE3B,IAAIC,OAAa;IAEjB;IACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC5BC,OAAO,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;KACzB,MAAM,IAAIA,IAAI,YAAYE,IAAI,EAAE;MAC/BD,OAAO,GAAGD,IAAI;KACf,MAAM;MACL,OAAO,SAAS;;IAGlB;IACA,IAAIG,KAAK,CAACF,OAAO,CAACG,OAAO,EAAE,CAAC,EAAE;MAC5B,OAAO,SAAS;;IAGlB,MAAMC,GAAG,GAAG,IAAIH,IAAI,EAAE;IACtB,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACD,OAAO,EAAE,GAAGH,OAAO,CAACG,OAAO,EAAE,IAAI,IAAI,CAAC;IAE5E,IAAIE,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,GAAG;IACrE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,GAAG;IACxE,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAC,GAAG;IAC1E,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,MAAM,CAAC,GAAG;EACjD;EAEAjH,aAAaA,CAACoH,KAAa,EAAEpD,IAAS;IACpC,OAAOA,IAAI,CAACN,EAAE;EAChB;EAEA;EACA2D,kBAAkBA,CAACC,aAAkB;IACnC,IAAI,CAACpG,gBAAgB,GAAG,KAAK;IAE7B,IAAIoG,aAAa,CAACC,MAAM,KAAK,SAAS,EAAE;MACtC;MACA,IAAI,CAAC9G,MAAM,CAAC+C,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;QACzCK,WAAW,EAAE;UACX2D,OAAO,EAAEF,aAAa,CAACE,OAAO,IAAI,IAAI,CAACC,eAAe,EAAE;UACxDC,MAAM,EAAEJ,aAAa,CAACI;;OAEzB,CAAC;KACH,MAAM;MACL;MACAC,KAAK,CAAC,mCAAmC,CAAC;;EAE9C;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAAC1G,gBAAgB,GAAG,KAAK;EAC/B;EAEQuG,eAAeA,CAAA;IACrB,OAAO,KAAK,GAAGZ,IAAI,CAACG,GAAG,EAAE,CAACN,QAAQ,EAAE;EACtC;EAEA;EACAtL,iBAAiBA,CAACyM,UAAe;IAC/B,IAAI,CAACA,UAAU,EAAE;IAEjB,MAAMrM,cAAc,GAAGqM,UAAU,CAACrM,cAAc,IAAI,SAAS;IAC7D,MAAMsM,UAAU,GAAGD,UAAU,CAACtE,OAAO,IAAIsE,UAAU,CAACjE,QAAQ,IAAIiE,UAAU,CAACE,MAAM,IAAIF,UAAU,CAACG,KAAK;IAErG,QAAQxM,cAAc;MACpB,KAAK,SAAS;QACZ,IAAI,CAACyM,iBAAiB,CAACH,UAAU,CAAC;QAClC;MACF,KAAK,UAAU;QACb,IAAI,CAACI,kBAAkB,CAACJ,UAAU,CAAC;QACnC;MACF,KAAK,QAAQ;QACX,IAAI,CAACK,gBAAgB,CAACL,UAAU,CAAC;QACjC;MACF,KAAK,OAAO;QACV,IAAI,CAACM,eAAe,CAACN,UAAU,CAAC;QAChC;MACF;QACE,IAAI,CAACG,iBAAiB,CAACH,UAAU,CAAC;;EAExC;EAEQG,iBAAiBA,CAAC1E,OAAY;IACpC,IAAIA,OAAO,EAAEE,GAAG,IAAIF,OAAO,EAAEG,EAAE,EAAE;MAC/B,MAAMoB,SAAS,GAAGvB,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE;MAC3C,IAAI,CAACjD,MAAM,CAAC+C,QAAQ,CAAC,CAAC,UAAU,EAAEsB,SAAS,CAAC,CAAC;;EAEjD;EAEQoD,kBAAkBA,CAACtE,QAAa;IACtC,IAAIA,QAAQ,EAAEyE,IAAI,IAAIzE,QAAQ,EAAEpH,IAAI,EAAE;MACpC,MAAM8L,YAAY,GAAG1E,QAAQ,CAACyE,IAAI,IAAIzE,QAAQ,CAACpH,IAAI,CAACsH,WAAW,EAAE,CAACyE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MACtF,IAAI,CAAC9H,MAAM,CAAC+C,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BK,WAAW,EAAE;UAAED,QAAQ,EAAE0E;QAAY;OACtC,CAAC;;EAEN;EAEQH,gBAAgBA,CAACJ,MAAW;IAClC,IAAIA,MAAM,EAAEtE,GAAG,IAAIsE,MAAM,EAAErE,EAAE,IAAIqE,MAAM,EAAE1K,QAAQ,EAAE;MACjD,MAAMmL,QAAQ,GAAGT,MAAM,CAACtE,GAAG,IAAIsE,MAAM,CAACrE,EAAE,IAAIqE,MAAM,CAAC1K,QAAQ;MAC3D,IAAI,CAACoD,MAAM,CAAC+C,QAAQ,CAAC,CAAC,SAAS,EAAEgF,QAAQ,CAAC,CAAC;;EAE/C;EAEQJ,eAAeA,CAACJ,KAAU;IAChC,IAAIA,KAAK,EAAEK,IAAI,IAAIL,KAAK,EAAExL,IAAI,EAAE;MAC9B,MAAMiM,SAAS,GAAGT,KAAK,CAACK,IAAI,IAAIL,KAAK,CAACxL,IAAI,CAACsH,WAAW,EAAE,CAACyE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MAC7E,IAAI,CAAC9H,MAAM,CAAC+C,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BK,WAAW,EAAE;UAAEmE,KAAK,EAAES;QAAS;OAChC,CAAC;;EAEN;EAEA5M,aAAaA,CAACgM,UAAe;IAC3B,MAAMa,IAAI,GAAGb,UAAU,CAACrM,cAAc,IAAI,SAAS;IACnD,MAAMgB,IAAI,GAAG,IAAI,CAACxB,UAAU,CAAC6M,UAAU,CAAC;IAExC,QAAQa,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,iBAAiBlM,IAAI,EAAE;MAChC,KAAK,UAAU;QACb,OAAO,oBAAoBA,IAAI,EAAE;MACnC,KAAK,QAAQ;QACX,OAAO,iBAAiBA,IAAI,EAAE;MAChC,KAAK,OAAO;QACV,OAAO,eAAeA,IAAI,EAAE;MAC9B;QACE,OAAO,SAASA,IAAI,EAAE;;EAE5B;EAEAV,UAAUA,CAACN,cAAsB;IAC/B,QAAQA,cAAc;MACpB,KAAK,SAAS;QACZ,OAAO,YAAY;MACrB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B,KAAK,QAAQ;QACX,OAAO,cAAc;MACvB,KAAK,OAAO;QACV,OAAO,cAAc;MACvB;QACE,OAAO,YAAY;;EAEzB;EAEAR,UAAUA,CAAC6M,UAAe;IACxB,MAAMa,IAAI,GAAGb,UAAU,CAACrM,cAAc,IAAI,SAAS;IAEnD,QAAQkN,IAAI;MACV,KAAK,SAAS;QACZ,OAAOb,UAAU,CAACtE,OAAO,EAAE/G,IAAI,IAAI,SAAS;MAC9C,KAAK,UAAU;QACb,OAAOqL,UAAU,CAACjE,QAAQ,EAAEpH,IAAI,IAAI,UAAU;MAChD,KAAK,QAAQ;QACX,OAAOqL,UAAU,CAACE,MAAM,EAAEnC,QAAQ,IAAIiC,UAAU,CAACE,MAAM,EAAE1K,QAAQ,IAAI,QAAQ;MAC/E,KAAK,OAAO;QACV,OAAOwK,UAAU,CAACG,KAAK,EAAExL,IAAI,IAAI,OAAO;MAC1C;QACE,OAAOqL,UAAU,CAACtE,OAAO,EAAE/G,IAAI,IAAI,MAAM;;EAE/C;EAEAvB,cAAcA,CAAC4M,UAAe;IAC5B,MAAMa,IAAI,GAAGb,UAAU,CAACrM,cAAc,IAAI,SAAS;IAEnD,QAAQkN,IAAI;MACV,KAAK,SAAS;QACZ,OAAOb,UAAU,CAACtE,OAAO,EAAE7G,KAAK,GAAG,IAAImL,UAAU,CAACtE,OAAO,CAAC7G,KAAK,EAAE,GAAG,EAAE;MACxE,KAAK,UAAU;QACb,OAAOmL,UAAU,CAACjE,QAAQ,EAAE+E,YAAY,GAAG,GAAGd,UAAU,CAACjE,QAAQ,CAAC+E,YAAY,WAAW,GAAG,iBAAiB;MAC/G,KAAK,QAAQ;QACX,OAAOd,UAAU,CAACE,MAAM,EAAE/N,QAAQ,IAAI,aAAa;MACrD,KAAK,OAAO;QACV,OAAO6N,UAAU,CAACG,KAAK,EAAEW,YAAY,GAAG,GAAGd,UAAU,CAACG,KAAK,CAACW,YAAY,WAAW,GAAG,YAAY;MACpG;QACE,OAAO,EAAE;;EAEf;EAEA9N,cAAcA,CAACgN,UAAe;IAC5B,MAAMa,IAAI,GAAGb,UAAU,CAACrM,cAAc,IAAI,SAAS;IAEnD,QAAQkN,IAAI;MACV,KAAK,SAAS;QACZ,OAAOb,UAAU,CAACtE,OAAO,EAAEqF,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;MACzF,KAAK,UAAU;QACb,OAAOhB,UAAU,CAACjE,QAAQ,EAAErH,KAAK,IAAI,yCAAyC;MAChF,KAAK,QAAQ;QACX,OAAOsL,UAAU,CAACE,MAAM,EAAE1I,MAAM,IAAI,uCAAuC;MAC7E,KAAK,OAAO;QACV,OAAOwI,UAAU,CAACG,KAAK,EAAEc,IAAI,IAAI,sCAAsC;MACzE;QACE,OAAOjB,UAAU,CAACtE,OAAO,EAAEqF,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;;EAE7F;;;uBA1oBWtI,aAAa,EAAA/G,EAAA,CAAAuP,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzP,EAAA,CAAAuP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3P,EAAA,CAAAuP,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA7P,EAAA,CAAAuP,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAA/P,EAAA,CAAAuP,iBAAA,CAAAS,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAblJ,aAAa;MAAAmJ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApQ,EAAA,CAAAqQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB1B3Q,EAAA,CAAAC,cAAA,aAAsC;UAQpCD,EANA,CAAA6B,UAAA,IAAAgP,4BAAA,iBAAiD,IAAAC,4BAAA,iBAMA;UA8JjD9Q,EAAA,CAAAC,cAAA,2BAIkD;UAAhDD,EADA,CAAAS,UAAA,mBAAAsQ,0DAAA;YAAA,OAASH,GAAA,CAAAxC,mBAAA,EAAqB;UAAA,EAAC,8BAAA4C,qEAAA1L,MAAA;YAAA,OACXsL,GAAA,CAAA/C,kBAAA,CAAAvI,MAAA,CAA0B;UAAA,EAAC;UAEnDtF,EADE,CAAAI,YAAA,EAAoB,EAChB;;;UA1KEJ,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAoB,UAAA,SAAAwP,GAAA,CAAArJ,SAAA,CAAe;UAMfvH,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAoB,UAAA,UAAAwP,GAAA,CAAArJ,SAAA,CAAgB;UA+JpBvH,EAAA,CAAAK,SAAA,EAA8B;UAC9BL,EADA,CAAAoB,UAAA,cAAAwP,GAAA,CAAAlJ,gBAAA,CAA8B,gBAAAkJ,GAAA,CAAAjJ,gBAAA,CACE;;;qBDzJxBjI,YAAY,EAAAuR,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExR,WAAW,EAAAyR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAE3R,YAAY,EAAEC,uBAAuB,EACxEE,gBAAgB,EAAED,qBAAqB;MAAA0R,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}