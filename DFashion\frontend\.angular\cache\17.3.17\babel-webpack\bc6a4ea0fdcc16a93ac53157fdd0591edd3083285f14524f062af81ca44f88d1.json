{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nlet ShopComponent = class ShopComponent {\n  constructor(productService, authService, cartService, wishlistService, router) {\n    this.productService = productService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.categories = [];\n    this.searchQuery = '';\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.loadShopData();\n  }\n  loadShopData() {\n    this.loading = true;\n    Promise.all([this.loadFeaturedBrands(), this.loadTrendingProducts(), this.loadNewArrivals(), this.loadCategories()]).finally(() => {\n      this.loading = false;\n    });\n  }\n  loadFeaturedBrands() {\n    return this.productService.getFeaturedBrands().toPromise().then(response => {\n      this.featuredBrands = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading featured brands:', error);\n      this.featuredBrands = [];\n    });\n  }\n  loadTrendingProducts() {\n    return this.productService.getTrendingProducts().toPromise().then(response => {\n      this.trendingProducts = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading trending products:', error);\n      this.trendingProducts = [];\n    });\n  }\n  loadNewArrivals() {\n    return this.productService.getNewArrivals().toPromise().then(response => {\n      this.newArrivals = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading new arrivals:', error);\n      this.newArrivals = [];\n    });\n  }\n  loadCategories() {\n    return this.productService.getCategories().toPromise().then(response => {\n      this.categories = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading categories:', error);\n      this.categories = [];\n    });\n  }\n};\nShopComponent = __decorate([Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.scss']\n})], ShopComponent);\nexport { ShopComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "ShopComponent", "constructor", "productService", "authService", "cartService", "wishlistService", "router", "featuredB<PERSON>s", "trendingProducts", "newArrivals", "categories", "searchQuery", "loading", "ngOnInit", "loadShopData", "Promise", "all", "loadFeaturedBrands", "loadTrendingProducts", "loadNewArrivals", "loadCategories", "finally", "getFeaturedBrands", "to<PERSON>romise", "then", "response", "data", "catch", "error", "console", "getTrendingProducts", "getNewArrivals", "getCategories", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\shop\\shop.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.scss']\n})\nexport class ShopComponent implements OnInit {\n  featuredBrands: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  categories: any[] = [];\n  searchQuery: string = '';\n  loading = true;\n\n  constructor(\n    private productService: ProductService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadShopData();\n  }\n\n  loadShopData() {\n    this.loading = true;\n    Promise.all([\n      this.loadFeaturedBrands(),\n      this.loadTrendingProducts(),\n      this.loadNewArrivals(),\n      this.loadCategories()\n    ]).finally(() => {\n      this.loading = false;\n    });\n  }\n\n  loadFeaturedBrands() {\n    return this.productService.getFeaturedBrands().toPromise().then(\n      (response) => {\n        this.featuredBrands = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading featured brands:', error);\n      this.featuredBrands = [];\n    });\n  }\n\n  loadTrendingProducts() {\n    return this.productService.getTrendingProducts().toPromise().then(\n      (response) => {\n        this.trendingProducts = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading trending products:', error);\n      this.trendingProducts = [];\n    });\n  }\n\n  loadNewArrivals() {\n    return this.productService.getNewArrivals().toPromise().then(\n      (response) => {\n        this.newArrivals = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading new arrivals:', error);\n      this.newArrivals = [];\n    });\n  }\n\n  loadCategories() {\n    return this.productService.getCategories().toPromise().then(\n      (response) => {\n        this.categories = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading categories:', error);\n      this.categories = [];\n    });\n  }\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAarC,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAQxBC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAZhB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,OAAO,GAAG,IAAI;EAQX;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACF,OAAO,GAAG,IAAI;IACnBG,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,kBAAkB,EAAE,EACzB,IAAI,CAACC,oBAAoB,EAAE,EAC3B,IAAI,CAACC,eAAe,EAAE,EACtB,IAAI,CAACC,cAAc,EAAE,CACtB,CAAC,CAACC,OAAO,CAAC,MAAK;MACd,IAAI,CAACT,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;EAEAK,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACf,cAAc,CAACoB,iBAAiB,EAAE,CAACC,SAAS,EAAE,CAACC,IAAI,CAC5DC,QAAQ,IAAI;MACX,IAAI,CAAClB,cAAc,GAAGkB,QAAQ,EAAEC,IAAI,IAAI,EAAE;IAC5C,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAACrB,cAAc,GAAG,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAW,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChB,cAAc,CAAC4B,mBAAmB,EAAE,CAACP,SAAS,EAAE,CAACC,IAAI,CAC9DC,QAAQ,IAAI;MACX,IAAI,CAACjB,gBAAgB,GAAGiB,QAAQ,EAAEC,IAAI,IAAI,EAAE;IAC9C,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACpB,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEAW,eAAeA,CAAA;IACb,OAAO,IAAI,CAACjB,cAAc,CAAC6B,cAAc,EAAE,CAACR,SAAS,EAAE,CAACC,IAAI,CACzDC,QAAQ,IAAI;MACX,IAAI,CAAChB,WAAW,GAAGgB,QAAQ,EAAEC,IAAI,IAAI,EAAE;IACzC,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,CAACnB,WAAW,GAAG,EAAE;IACvB,CAAC,CAAC;EACJ;EAEAW,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClB,cAAc,CAAC8B,aAAa,EAAE,CAACT,SAAS,EAAE,CAACC,IAAI,CACxDC,QAAQ,IAAI;MACX,IAAI,CAACf,UAAU,GAAGe,QAAQ,EAAEC,IAAI,IAAI,EAAE;IACxC,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,IAAI,CAAClB,UAAU,GAAG,EAAE;IACtB,CAAC,CAAC;EACJ;CAAC;AA1EUV,aAAa,GAAAiC,UAAA,EAPzBpC,SAAS,CAAC;EACTqC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtC,YAAY,EAAEC,WAAW,CAAC;EACpCsC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,uBAAuB;CACpC,CAAC,C,EACWtC,aAAa,CA0EvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}