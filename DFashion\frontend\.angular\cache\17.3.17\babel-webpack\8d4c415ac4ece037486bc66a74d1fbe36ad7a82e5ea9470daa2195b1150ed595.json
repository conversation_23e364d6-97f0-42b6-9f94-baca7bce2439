{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { i as inheritAriaAttributes } from './helpers-be245865.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { c as chevronBack, a as arrowBackSharp } from './index-f7dc70ba.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nconst backButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-hover:transparent;--background-hover-opacity:1;--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #3880ff);--icon-margin-end:1px;--icon-margin-start:-4px;--icon-font-size:1.6em;--min-height:32px;font-size:clamp(17px, 1.0625rem, 21.998px)}.button-native{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:visible;z-index:99}:host(.ion-activated) .button-native{opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst IonBackButtonIosStyle0 = backButtonIosCss;\nconst backButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--border-radius:4px;--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:0.04;--color:currentColor;--icon-margin-end:0;--icon-margin-start:0;--icon-font-size:1.5rem;--icon-font-weight:normal;--min-height:32px;--min-width:44px;--padding-start:12px;--padding-end:12px;font-size:0.875rem;font-weight:500;text-transform:uppercase}:host(.back-button-has-icon-only){--border-radius:50%;min-width:48px;min-height:48px;aspect-ratio:1/1}.button-native{-webkit-box-shadow:none;box-shadow:none}.button-text{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0}ion-icon{line-height:0.67;text-align:start}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}\";\nconst IonBackButtonMdStyle0 = backButtonMdCss;\nconst BackButton = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    this.onClick = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (ev) {\n        const nav = _this.el.closest('ion-nav');\n        ev.preventDefault();\n        if (nav && (yield nav.canGoBack())) {\n          return nav.pop({\n            animationBuilder: _this.routerAnimation,\n            skipIfBusy: true\n          });\n        }\n        return openURL(_this.defaultHref, ev, 'back', _this.routerAnimation);\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    this.color = undefined;\n    this.defaultHref = undefined;\n    this.disabled = false;\n    this.icon = undefined;\n    this.text = undefined;\n    this.type = 'button';\n    this.routerAnimation = undefined;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n    if (this.defaultHref === undefined) {\n      this.defaultHref = config.get('backButtonDefaultHref');\n    }\n  }\n  get backButtonIcon() {\n    const icon = this.icon;\n    if (icon != null) {\n      // icon is set on the component or by the config\n      return icon;\n    }\n    if (getIonMode(this) === 'ios') {\n      // default ios back button icon\n      return config.get('backButtonIcon', chevronBack);\n    }\n    // default md back button icon\n    return config.get('backButtonIcon', arrowBackSharp);\n  }\n  get backButtonText() {\n    const defaultBackButtonText = getIonMode(this) === 'ios' ? 'Back' : null;\n    return this.text != null ? this.text : config.get('backButtonText', defaultBackButtonText);\n  }\n  get hasIconOnly() {\n    return this.backButtonIcon && !this.backButtonText;\n  }\n  get rippleType() {\n    // If the button only has an icon we use the unbounded\n    // \"circular\" ripple effect\n    if (this.hasIconOnly) {\n      return 'unbounded';\n    }\n    return 'bounded';\n  }\n  render() {\n    const {\n      color,\n      defaultHref,\n      disabled,\n      type,\n      hasIconOnly,\n      backButtonIcon,\n      backButtonText,\n      icon,\n      inheritedAttributes\n    } = this;\n    const showBackButton = defaultHref !== undefined;\n    const mode = getIonMode(this);\n    const ariaLabel = inheritedAttributes['aria-label'] || backButtonText || 'back';\n    return h(Host, {\n      key: '9e837be314ee8ea487ba2771d36668ef58442faa',\n      onClick: this.onClick,\n      class: createColorClasses(color, {\n        [mode]: true,\n        button: true,\n        // ion-buttons target .button\n        'back-button-disabled': disabled,\n        'back-button-has-icon-only': hasIconOnly,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': true,\n        'ion-focusable': true,\n        'show-back-button': showBackButton\n      })\n    }, h(\"button\", {\n      key: 'c4531348bd80cf6163931c5fb432526aa8127043',\n      type: type,\n      disabled: disabled,\n      class: \"button-native\",\n      part: \"native\",\n      \"aria-label\": ariaLabel\n    }, h(\"span\", {\n      key: 'aab8c6d5f0a84aed9f4e4a2532a239b28c7d5589',\n      class: \"button-inner\"\n    }, backButtonIcon && h(\"ion-icon\", {\n      key: '121eae10947b46a3d109709b79f4126b1c40ade4',\n      part: \"icon\",\n      icon: backButtonIcon,\n      \"aria-hidden\": \"true\",\n      lazy: false,\n      \"flip-rtl\": icon === undefined\n    }), backButtonText && h(\"span\", {\n      key: '11c28875cc52b99470525d18c1f88f27c390a75a',\n      part: \"text\",\n      \"aria-hidden\": \"true\",\n      class: \"button-text\"\n    }, backButtonText)), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '804bf8a06cd033c99d61ec2665c842b3b25945fa',\n      type: this.rippleType\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nBackButton.style = {\n  ios: IonBackButtonIosStyle0,\n  md: IonBackButtonMdStyle0\n};\nexport { BackButton as ion_back_button };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}