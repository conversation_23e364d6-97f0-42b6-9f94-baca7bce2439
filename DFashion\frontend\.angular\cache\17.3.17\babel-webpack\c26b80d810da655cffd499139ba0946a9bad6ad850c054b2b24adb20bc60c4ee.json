{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./admin-auth.service\";\nexport let AdminApiService = /*#__PURE__*/(() => {\n  class AdminApiService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n      this.apiUrl = 'http://localhost:5000/api';\n    }\n    // Get authorization headers\n    getHeaders() {\n      return this.authService.getAuthHeaders();\n    }\n    // Handle API errors\n    handleError(error) {\n      console.error('API Error:', error);\n      if (error.status === 401) {\n        this.authService.logout();\n      }\n      return throwError(error);\n    }\n    // Dashboard APIs\n    getDashboardStats() {\n      return this.http.get(`${this.apiUrl}/admin/dashboard`, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data), catchError(this.handleError.bind(this)));\n    }\n    // User Management APIs\n    getUsers(params = {}) {\n      let httpParams = new HttpParams();\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n      return this.http.get(`${this.apiUrl}/admin/users`, {\n        headers: this.getHeaders(),\n        params: httpParams\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    getUserById(id) {\n      return this.http.get(`${this.apiUrl}/admin/users/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data), catchError(this.handleError.bind(this)));\n    }\n    createUser(userData) {\n      return this.http.post(`${this.apiUrl}/admin/users`, userData, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    updateUser(id, userData) {\n      return this.http.put(`${this.apiUrl}/admin/users/${id}`, userData, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    deleteUser(id) {\n      return this.http.delete(`${this.apiUrl}/admin/users/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    activateUser(id) {\n      return this.http.put(`${this.apiUrl}/admin/users/${id}/activate`, {}, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    updateUserPassword(id, newPassword) {\n      return this.http.put(`${this.apiUrl}/admin/users/${id}/password`, {\n        newPassword\n      }, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    // Product Management APIs\n    getProducts(params = {}) {\n      let httpParams = new HttpParams();\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n      return this.http.get(`${this.apiUrl}/admin/products`, {\n        headers: this.getHeaders(),\n        params: httpParams\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    getProductById(id) {\n      return this.http.get(`${this.apiUrl}/admin/products/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data), catchError(this.handleError.bind(this)));\n    }\n    createProduct(productData) {\n      return this.http.post(`${this.apiUrl}/admin/products`, productData, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    updateProduct(id, productData) {\n      return this.http.put(`${this.apiUrl}/admin/products/${id}`, productData, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    deleteProduct(id) {\n      return this.http.delete(`${this.apiUrl}/admin/products/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    approveProduct(id) {\n      return this.http.put(`${this.apiUrl}/admin/products/${id}/approve`, {}, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    rejectProduct(id, reason) {\n      return this.http.put(`${this.apiUrl}/admin/products/${id}/reject`, {\n        rejectionReason: reason\n      }, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    toggleProductFeatured(id) {\n      return this.http.put(`${this.apiUrl}/admin/products/${id}/featured`, {}, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    // Order Management APIs\n    getOrders(params = {}) {\n      let httpParams = new HttpParams();\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n      return this.http.get(`${this.apiUrl}/admin/orders`, {\n        headers: this.getHeaders(),\n        params: httpParams\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    getOrderById(id) {\n      return this.http.get(`${this.apiUrl}/admin/orders/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data), catchError(this.handleError.bind(this)));\n    }\n    updateOrderStatus(id, status, trackingNumber, notes) {\n      return this.http.put(`${this.apiUrl}/admin/orders/${id}/status`, {\n        status,\n        trackingNumber,\n        notes\n      }, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    cancelOrder(id, reason) {\n      return this.http.put(`${this.apiUrl}/admin/orders/${id}/cancel`, {\n        reason\n      }, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    processRefund(id, amount, reason) {\n      return this.http.put(`${this.apiUrl}/admin/orders/${id}/refund`, {\n        amount,\n        reason\n      }, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    // Analytics APIs\n    getAnalytics(params = {}) {\n      let httpParams = new HttpParams();\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n      return this.http.get(`${this.apiUrl}/admin/analytics/overview`, {\n        headers: this.getHeaders(),\n        params: httpParams\n      }).pipe(map(response => response.data), catchError(this.handleError.bind(this)));\n    }\n    getSalesReport(params = {}) {\n      let httpParams = new HttpParams();\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.set(key, params[key].toString());\n        }\n      });\n      return this.http.get(`${this.apiUrl}/admin/reports/sales`, {\n        headers: this.getHeaders(),\n        params: httpParams\n      }).pipe(map(response => response.data), catchError(this.handleError.bind(this)));\n    }\n    // Settings APIs\n    getSettings() {\n      return this.http.get(`${this.apiUrl}/admin/settings`, {\n        headers: this.getHeaders()\n      }).pipe(map(response => response.data), catchError(this.handleError.bind(this)));\n    }\n    updateSettings(settings) {\n      return this.http.put(`${this.apiUrl}/admin/settings`, settings, {\n        headers: this.getHeaders()\n      }).pipe(catchError(this.handleError.bind(this)));\n    }\n    static {\n      this.ɵfac = function AdminApiService_Factory(t) {\n        return new (t || AdminApiService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AdminAuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AdminApiService,\n        factory: AdminApiService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AdminApiService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}