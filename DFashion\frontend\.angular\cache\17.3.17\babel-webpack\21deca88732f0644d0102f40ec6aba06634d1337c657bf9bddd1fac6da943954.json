{"ast": null, "code": "import { BehaviorSubject, Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nimport * as i3 from \"./cart-new.service\";\nimport * as i4 from \"./wishlist-new.service\";\nexport let SocialMediaService = /*#__PURE__*/(() => {\n  class SocialMediaService {\n    constructor(http, authService, cartService, wishlistService) {\n      this.http = http;\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.apiUrl = 'http://localhost:5000/api';\n      this.postsSubject = new BehaviorSubject([]);\n      this.storiesSubject = new BehaviorSubject([]);\n      this.posts$ = this.postsSubject.asObservable();\n      this.stories$ = this.storiesSubject.asObservable();\n    }\n    // Posts API\n    loadPosts(page = 1, limit = 10) {\n      return this.http.get(`${this.apiUrl}/posts`, {\n        params: {\n          page: page.toString(),\n          limit: limit.toString()\n        },\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          if (page === 1) {\n            this.postsSubject.next(response.posts);\n          } else {\n            const currentPosts = this.postsSubject.value;\n            this.postsSubject.next([...currentPosts, ...response.posts]);\n          }\n        }\n      }));\n    }\n    likePost(postId) {\n      if (!this.authService.requireAuth('like posts')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.post(`${this.apiUrl}/posts/${postId}/like`, {}, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.updatePostInList(postId, {\n            isLiked: true\n          });\n          this.showSuccessMessage('Post liked!');\n        }\n      }));\n    }\n    unlikePost(postId) {\n      if (!this.authService.requireAuth('unlike posts')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.delete(`${this.apiUrl}/posts/${postId}/like`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.updatePostInList(postId, {\n            isLiked: false\n          });\n        }\n      }));\n    }\n    commentOnPost(postId, text) {\n      if (!this.authService.requireAuth('comment on posts')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.post(`${this.apiUrl}/posts/${postId}/comment`, {\n        text\n      }, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.showSuccessMessage('Comment added!');\n          // Refresh posts to get updated comments\n          this.loadPosts().subscribe();\n        }\n      }));\n    }\n    sharePost(postId) {\n      if (!this.authService.requireAuth('share posts')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.post(`${this.apiUrl}/posts/${postId}/share`, {}, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.showSuccessMessage('Post shared!');\n        }\n      }));\n    }\n    savePost(postId) {\n      if (!this.authService.requireAuth('save posts')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.post(`${this.apiUrl}/posts/${postId}/save`, {}, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.updatePostInList(postId, {\n            isSaved: true\n          });\n          this.showSuccessMessage('Post saved!');\n        }\n      }));\n    }\n    unsavePost(postId) {\n      if (!this.authService.requireAuth('unsave posts')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.delete(`${this.apiUrl}/posts/${postId}/save`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.updatePostInList(postId, {\n            isSaved: false\n          });\n        }\n      }));\n    }\n    // Stories API\n    loadStories() {\n      return this.http.get(`${this.apiUrl}/stories`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.storiesSubject.next(response.stories);\n        }\n      }));\n    }\n    viewStory(storyId) {\n      return this.http.post(`${this.apiUrl}/stories/${storyId}/view`, {}, {\n        headers: this.authService.getAuthHeaders()\n      });\n    }\n    // E-commerce integration\n    buyNowFromPost(postId, productId) {\n      if (!this.authService.requireCustomerAuth('purchase items')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      // Track analytics\n      this.trackProductClick(postId, productId, 'buy_now').subscribe();\n      // Navigate to checkout (handled by component)\n      return new Observable(observer => {\n        observer.next({\n          success: true,\n          action: 'navigate_to_checkout'\n        });\n        observer.complete();\n      });\n    }\n    addToCartFromPost(postId, productId, quantity = 1, size, color) {\n      if (!this.authService.requireCustomerAuth('add items to cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      // Track analytics\n      this.trackProductClick(postId, productId, 'add_to_cart').subscribe();\n      // Add to cart\n      return this.cartService.addFromPost(productId, quantity, size, color);\n    }\n    addToWishlistFromPost(postId, productId, size, color) {\n      if (!this.authService.requireCustomerAuth('add items to wishlist')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      // Track analytics\n      this.trackProductClick(postId, productId, 'add_to_wishlist').subscribe();\n      // Add to wishlist\n      return this.wishlistService.addFromPost(productId, size, color);\n    }\n    addToCartFromStory(storyId, productId, quantity = 1, size, color) {\n      if (!this.authService.requireCustomerAuth('add items to cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      // Track analytics\n      this.trackStoryProductClick(storyId, productId, 'add_to_cart').subscribe();\n      // Add to cart\n      return this.cartService.addFromStory(productId, quantity, size, color);\n    }\n    addToWishlistFromStory(storyId, productId, size, color) {\n      if (!this.authService.requireCustomerAuth('add items to wishlist')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      // Track analytics\n      this.trackStoryProductClick(storyId, productId, 'add_to_wishlist').subscribe();\n      // Add to wishlist\n      return this.wishlistService.addFromStory(productId, size, color);\n    }\n    // Analytics tracking\n    trackProductClick(postId, productId, action) {\n      return this.http.post(`${this.apiUrl}/posts/${postId}/analytics/product-click`, {\n        productId,\n        action\n      }, {\n        headers: this.authService.getAuthHeaders()\n      });\n    }\n    trackStoryProductClick(storyId, productId, action) {\n      return this.http.post(`${this.apiUrl}/stories/${storyId}/analytics/product-click`, {\n        productId,\n        action\n      }, {\n        headers: this.authService.getAuthHeaders()\n      });\n    }\n    // Helper methods\n    updatePostInList(postId, updates) {\n      const currentPosts = this.postsSubject.value;\n      const updatedPosts = currentPosts.map(post => post._id === postId ? {\n        ...post,\n        ...updates\n      } : post);\n      this.postsSubject.next(updatedPosts);\n    }\n    showSuccessMessage(message) {\n      // TODO: Implement proper toast/notification system\n      console.log('Social Media Success:', message);\n    }\n    // Utility methods\n    getTimeAgo(date) {\n      const now = new Date();\n      const diffMs = now.getTime() - new Date(date).getTime();\n      const diffMinutes = Math.floor(diffMs / (1000 * 60));\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n      if (diffMinutes < 1) return 'now';\n      if (diffMinutes < 60) return `${diffMinutes}m`;\n      if (diffHours < 24) return `${diffHours}h`;\n      if (diffDays < 7) return `${diffDays}d`;\n      return new Date(date).toLocaleDateString();\n    }\n    formatNumber(num) {\n      if (num < 1000) return num.toString();\n      if (num < 1000000) return (num / 1000).toFixed(1) + 'K';\n      return (num / 1000000).toFixed(1) + 'M';\n    }\n    static {\n      this.ɵfac = function SocialMediaService_Factory(t) {\n        return new (t || SocialMediaService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService), i0.ɵɵinject(i3.CartNewService), i0.ɵɵinject(i4.WishlistNewService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SocialMediaService,\n        factory: SocialMediaService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SocialMediaService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}