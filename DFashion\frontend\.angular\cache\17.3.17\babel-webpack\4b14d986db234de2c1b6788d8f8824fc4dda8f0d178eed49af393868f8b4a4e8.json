{"ast": null, "code": "import { AuthGuard } from './core/guards/auth.guard';\nexport const routes = [\n// Home Route (Public)\n{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n},\n// Authentication Routes\n{\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n},\n// Home with Auth\n{\n  path: 'home',\n  loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n  canActivate: [AuthGuard]\n},\n// Explore Routes\n{\n  path: 'explore',\n  loadComponent: () => import('./features/explore/explore.component').then(m => m.ExploreComponent),\n  title: 'Explore - DFashion'\n},\n// Shop Routes\n{\n  path: 'shop',\n  loadComponent: () => import('./features/shop/shop.component').then(m => m.ShopComponent),\n  title: 'Shop - DFashion'\n},\n// Category Routes\n{\n  path: 'category/:category',\n  loadComponent: () => import('./features/category/category.component').then(m => m.CategoryComponent),\n  title: 'Category - DFashion'\n},\n// Wishlist Routes\n{\n  path: 'wishlist',\n  loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),\n  title: 'My Wishlist - DFashion'\n},\n// Social Media Routes\n{\n  path: 'social',\n  loadComponent: () => import('./features/social-media/social-media.component').then(m => m.SocialMediaComponent),\n  title: 'Social Feed - DFashion'\n}, {\n  path: 'feed',\n  loadComponent: () => import('./features/posts/social-feed.component').then(m => m.SocialFeedComponent),\n  title: 'Social Feed - DFashion'\n}, {\n  path: 'stories',\n  loadComponent: () => import('./features/stories/stories-viewer.component').then(m => m.StoriesViewerComponent),\n  title: 'Stories - DFashion'\n}, {\n  path: 'stories/create',\n  loadComponent: () => import('./features/stories/story-create.component').then(m => m.StoryCreateComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Story - DFashion'\n}, {\n  path: 'stories/:userId',\n  loadComponent: () => import('./features/stories/stories-viewer.component').then(m => m.StoriesViewerComponent),\n  title: 'User Stories - DFashion'\n}, {\n  path: 'post/:id',\n  loadComponent: () => import('./features/posts/post-detail.component').then(m => m.PostDetailComponent),\n  title: 'Post Detail - DFashion'\n},\n// E-commerce Hub Routes\n{\n  path: 'hub',\n  loadComponent: () => import('./features/ecommerce/ecommerce-hub.component').then(m => m.EcommerceHubComponent),\n  title: 'E-commerce Hub - DFashion'\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./features/ecommerce/ecommerce-hub.component').then(m => m.EcommerceHubComponent),\n  title: 'Dashboard - DFashion'\n},\n// Products Routes (using existing product detail)\n{\n  path: 'product/:id',\n  loadComponent: () => import('./features/product/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n}, {\n  path: 'products/:id',\n  redirectTo: 'product/:id'\n},\n// Shopping Cart & Wishlist (will be created)\n{\n  path: 'cart',\n  loadComponent: () => import('./features/shop/pages/cart/cart.component').then(m => m.CartComponent),\n  canActivate: [AuthGuard],\n  title: 'Shopping Cart - DFashion'\n},\n// Checkout Process (using existing checkout)\n{\n  path: 'checkout',\n  loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n  canActivate: [AuthGuard],\n  title: 'Checkout - DFashion'\n},\n// Payment Routes\n{\n  path: 'payment-success',\n  loadComponent: () => import('./features/payment/payment-success/payment-success.component').then(m => m.PaymentSuccessComponent),\n  title: 'Payment Successful - DFashion'\n}, {\n  path: 'payment-failed',\n  loadComponent: () => import('./features/payment/payment-failed/payment-failed.component').then(m => m.PaymentFailedComponent),\n  title: 'Payment Failed - DFashion'\n},\n// User Account Management\n{\n  path: 'account',\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'profile',\n    pathMatch: 'full'\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes)\n  }, {\n    path: 'orders',\n    loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n    title: 'My Orders - DFashion'\n  }]\n},\n// Public Vendor Profile (no auth required) - temporarily using dashboard\n{\n  path: 'vendor/:id',\n  loadComponent: () => import('./features/vendor/pages/dashboard/vendor-dashboard.component').then(m => m.VendorDashboardComponent),\n  title: 'Vendor Profile - DFashion'\n},\n// Vendor Dashboard (auth required)\n{\n  path: 'vendor',\n  loadChildren: () => import('./features/vendor/vendor.routes').then(m => m.vendorRoutes),\n  canActivate: [AuthGuard],\n  title: 'Vendor Dashboard - DFashion'\n},\n// Legacy Routes (maintain compatibility)\n{\n  path: 'shop',\n  loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'search',\n  loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n}, {\n  path: 'product/:id',\n  redirectTo: 'products/:id'\n},\n// Admin Routes\n{\n  path: 'admin',\n  loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n},\n// Support & Help (using existing profile as placeholder)\n{\n  path: 'support',\n  loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n  title: 'Support - DFashion'\n},\n// Wildcard route\n{\n  path: '**',\n  redirectTo: '/'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}