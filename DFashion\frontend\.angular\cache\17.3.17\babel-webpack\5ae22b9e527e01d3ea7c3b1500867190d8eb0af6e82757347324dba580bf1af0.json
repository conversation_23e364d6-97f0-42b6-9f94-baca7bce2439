{"ast": null, "code": "import { BehaviorSubject, fromEvent } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport let MobileOptimizationService = /*#__PURE__*/(() => {\n  class MobileOptimizationService {\n    constructor() {\n      this.deviceInfo$ = new BehaviorSubject(this.getDeviceInfo());\n      this.viewportBreakpoints$ = new BehaviorSubject(this.getViewportBreakpoints());\n      this.isKeyboardOpen$ = new BehaviorSubject(false);\n      this.initializeListeners();\n    }\n    // Device Information\n    getDeviceInfo() {\n      const userAgent = navigator.userAgent;\n      const screenWidth = window.innerWidth;\n      const screenHeight = window.innerHeight;\n      return {\n        isMobile: this.isMobileDevice(),\n        isTablet: this.isTabletDevice(),\n        isDesktop: this.isDesktopDevice(),\n        screenWidth,\n        screenHeight,\n        orientation: screenWidth > screenHeight ? 'landscape' : 'portrait',\n        devicePixelRatio: window.devicePixelRatio || 1,\n        touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,\n        platform: this.getPlatform(),\n        userAgent\n      };\n    }\n    // Viewport Breakpoints\n    getViewportBreakpoints() {\n      const width = window.innerWidth;\n      return {\n        xs: width < 576,\n        sm: width >= 576 && width < 768,\n        md: width >= 768 && width < 992,\n        lg: width >= 992 && width < 1200,\n        xl: width >= 1200 && width < 1400,\n        xxl: width >= 1400\n      };\n    }\n    // Observable Streams\n    getDeviceInfo$() {\n      return this.deviceInfo$.asObservable();\n    }\n    getViewportBreakpoints$() {\n      return this.viewportBreakpoints$.asObservable();\n    }\n    getIsKeyboardOpen$() {\n      return this.isKeyboardOpen$.asObservable();\n    }\n    // Device Detection Methods\n    isMobileDevice() {\n      const userAgent = navigator.userAgent;\n      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;\n      return mobileRegex.test(userAgent) || window.innerWidth <= 768;\n    }\n    isTabletDevice() {\n      const userAgent = navigator.userAgent;\n      const tabletRegex = /iPad|Android(?!.*Mobile)/i;\n      return tabletRegex.test(userAgent) || window.innerWidth > 768 && window.innerWidth <= 1024;\n    }\n    isDesktopDevice() {\n      return !this.isMobileDevice() && !this.isTabletDevice();\n    }\n    getPlatform() {\n      const userAgent = navigator.userAgent;\n      if (/iPhone|iPad|iPod/i.test(userAgent)) return 'iOS';\n      if (/Android/i.test(userAgent)) return 'Android';\n      if (/Windows/i.test(userAgent)) return 'Windows';\n      if (/Mac/i.test(userAgent)) return 'macOS';\n      if (/Linux/i.test(userAgent)) return 'Linux';\n      return 'Unknown';\n    }\n    // Touch and Gesture Support\n    isTouchDevice() {\n      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n    supportsHover() {\n      return window.matchMedia('(hover: hover)').matches;\n    }\n    // Viewport Utilities\n    getViewportWidth() {\n      return window.innerWidth;\n    }\n    getViewportHeight() {\n      return window.innerHeight;\n    }\n    getScrollbarWidth() {\n      const outer = document.createElement('div');\n      outer.style.visibility = 'hidden';\n      outer.style.overflow = 'scroll';\n      outer.style.msOverflowStyle = 'scrollbar';\n      document.body.appendChild(outer);\n      const inner = document.createElement('div');\n      outer.appendChild(inner);\n      const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;\n      outer.parentNode?.removeChild(outer);\n      return scrollbarWidth;\n    }\n    // Safe Area Support (for iOS notch)\n    getSafeAreaInsets() {\n      const style = getComputedStyle(document.documentElement);\n      return {\n        top: parseInt(style.getPropertyValue('--sat') || '0', 10),\n        right: parseInt(style.getPropertyValue('--sar') || '0', 10),\n        bottom: parseInt(style.getPropertyValue('--sab') || '0', 10),\n        left: parseInt(style.getPropertyValue('--sal') || '0', 10)\n      };\n    }\n    // Performance Optimization\n    enableGPUAcceleration(element) {\n      element.style.transform = 'translateZ(0)';\n      element.style.willChange = 'transform';\n    }\n    disableGPUAcceleration(element) {\n      element.style.transform = '';\n      element.style.willChange = '';\n    }\n    // Scroll Management\n    disableBodyScroll() {\n      document.body.style.overflow = 'hidden';\n      document.body.style.position = 'fixed';\n      document.body.style.width = '100%';\n    }\n    enableBodyScroll() {\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n    }\n    // Touch Event Helpers\n    getTouchCoordinates(event) {\n      const touch = event.touches[0] || event.changedTouches[0];\n      return {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n    }\n    // Responsive Image Loading\n    getOptimalImageSize(containerWidth) {\n      const devicePixelRatio = window.devicePixelRatio || 1;\n      const targetWidth = containerWidth * devicePixelRatio;\n      if (targetWidth <= 400) return 'w=400';\n      if (targetWidth <= 800) return 'w=800';\n      if (targetWidth <= 1200) return 'w=1200';\n      if (targetWidth <= 1600) return 'w=1600';\n      return 'w=2000';\n    }\n    // Keyboard Detection (for mobile)\n    detectKeyboard() {\n      const initialViewportHeight = window.innerHeight;\n      fromEvent(window, 'resize').pipe(debounceTime(100), map(() => window.innerHeight), distinctUntilChanged()).subscribe(currentHeight => {\n        const heightDifference = initialViewportHeight - currentHeight;\n        const isKeyboardOpen = heightDifference > 150; // Threshold for keyboard detection\n        this.isKeyboardOpen$.next(isKeyboardOpen);\n      });\n    }\n    // Initialize Event Listeners\n    initializeListeners() {\n      // Resize listener\n      fromEvent(window, 'resize').pipe(debounceTime(250)).subscribe(() => {\n        this.deviceInfo$.next(this.getDeviceInfo());\n        this.viewportBreakpoints$.next(this.getViewportBreakpoints());\n      });\n      // Orientation change listener\n      fromEvent(window, 'orientationchange').pipe(debounceTime(500)).subscribe(() => {\n        this.deviceInfo$.next(this.getDeviceInfo());\n        this.viewportBreakpoints$.next(this.getViewportBreakpoints());\n      });\n      // Keyboard detection for mobile\n      if (this.isMobileDevice()) {\n        this.detectKeyboard();\n      }\n      // Add CSS custom properties for safe area\n      this.updateSafeAreaProperties();\n    }\n    // Update CSS Custom Properties for Safe Area\n    updateSafeAreaProperties() {\n      const root = document.documentElement;\n      // Set safe area inset properties\n      root.style.setProperty('--sat', 'env(safe-area-inset-top)');\n      root.style.setProperty('--sar', 'env(safe-area-inset-right)');\n      root.style.setProperty('--sab', 'env(safe-area-inset-bottom)');\n      root.style.setProperty('--sal', 'env(safe-area-inset-left)');\n    }\n    // Utility Methods\n    isCurrentBreakpoint(breakpoint) {\n      return this.viewportBreakpoints$.value[breakpoint];\n    }\n    getCurrentBreakpoint() {\n      const breakpoints = this.viewportBreakpoints$.value;\n      if (breakpoints.xxl) return 'xxl';\n      if (breakpoints.xl) return 'xl';\n      if (breakpoints.lg) return 'lg';\n      if (breakpoints.md) return 'md';\n      if (breakpoints.sm) return 'sm';\n      return 'xs';\n    }\n    // Performance Monitoring\n    measurePerformance(name, fn) {\n      const start = performance.now();\n      fn();\n      const end = performance.now();\n      const duration = end - start;\n      console.log(`${name} took ${duration.toFixed(2)} milliseconds`);\n      return duration;\n    }\n    // Memory Management\n    cleanupEventListeners() {\n      // This would be called in component ngOnDestroy\n      // Implementation depends on specific use case\n    }\n    static {\n      this.ɵfac = function MobileOptimizationService_Factory(t) {\n        return new (t || MobileOptimizationService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MobileOptimizationService,\n        factory: MobileOptimizationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MobileOptimizationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}