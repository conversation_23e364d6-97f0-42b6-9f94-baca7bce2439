{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-slick-carousel\";\nconst _c0 = [\"slickCarousel\"];\nconst _c1 = [\"storyVideo\"];\nfunction ViewAddStoriesComponent_div_23_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"i\", 30);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_23_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", story_r4.products.length, \" item\", story_r4.products.length > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_23_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const story_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.openStory(story_r4, i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"div\", 25)(3, \"div\", 16);\n    i0.ɵɵelement(4, \"img\", 26);\n    i0.ɵɵtemplate(5, ViewAddStoriesComponent_div_23_div_5_Template, 2, 0, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ViewAddStoriesComponent_div_23_div_8_Template, 2, 2, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-products\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", story_r4.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r5.currentStoryIndex)(\"completed\", i_r8 < ctx_r5.currentStoryIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r5.getProgressWidth(i_r8), \"%\");\n  }\n}\nfunction ViewAddStoriesComponent_div_26_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 60, 3);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_div_17_div_1_Template_div_click_0_listener($event) {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      ctx_r5.openProductDetails(productTag_r10.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"div\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65);\n    i0.ɵɵelement(3, \"img\", 66);\n    i0.ɵɵelementStart(4, \"div\", 67)(5, \"span\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 69);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const productTag_r10 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", (productTag_r10.position == null ? null : productTag_r10.position.x) || 50, \"%\")(\"top\", (productTag_r10.position == null ? null : productTag_r10.position.y) || 50, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", productTag_r10.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", productTag_r10.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r10.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", productTag_r10.product.price, \"\");\n  }\n}\nfunction ViewAddStoriesComponent_div_26_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_26_div_17_div_1_Template, 9, 8, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", (tmp_5_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_5_0.products);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_button_18_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      ctx_r5.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 30);\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r5.showProductTags);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getProductCount());\n  }\n}\nfunction ViewAddStoriesComponent_div_26_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.previousStory());\n    });\n    i0.ɵɵelement(1, \"i\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_26_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.nextStory());\n    });\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33, 2)(3, \"div\", 34);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_26_div_4_Template, 2, 6, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36);\n    i0.ɵɵelement(6, \"img\", 37);\n    i0.ɵɵelementStart(7, \"div\", 38)(8, \"span\", 39);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 40);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.closeStories());\n    });\n    i0.ɵɵelement(13, \"i\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_div_click_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_26_Template_div_touchstart_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_26_Template_div_touchmove_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_26_Template_div_touchend_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchEnd($event));\n    });\n    i0.ɵɵtemplate(15, ViewAddStoriesComponent_div_26_img_15_Template, 1, 1, \"img\", 44)(16, ViewAddStoriesComponent_div_26_video_16_Template, 2, 1, \"video\", 45)(17, ViewAddStoriesComponent_div_26_div_17_Template, 2, 1, \"div\", 46)(18, ViewAddStoriesComponent_div_26_button_18_Template, 4, 3, \"button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 48)(20, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.toggleLike());\n    });\n    i0.ɵɵelement(21, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.shareStory());\n    });\n    i0.ɵɵelement(23, \"i\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.saveStory());\n    });\n    i0.ɵɵelement(25, \"i\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(26, ViewAddStoriesComponent_div_26_button_26_Template, 2, 0, \"button\", 55)(27, ViewAddStoriesComponent_div_26_button_27_Template, 2, 0, \"button\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_10_0;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.stories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().user.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getCurrentStory().user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getTimeAgo(ctx_r5.getCurrentStory().createdAt));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showProductTags && ((tmp_10_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_10_0.products));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.hasProducts());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r5.isLiked);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex < ctx_r5.stories.length - 1);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    // Story viewer state\n    this.isOpen = false;\n    this.currentStoryIndex = 0;\n    this.showProductTags = false;\n    this.isLiked = false;\n    // Navigation state\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.showNavArrows = true;\n    this.storyDuration = 15000; // 15 seconds default\n    this.progressStartTime = 0;\n    // Touch handling\n    this.touchStartTime = 0;\n    this.subscriptions = [];\n    // Slider configuration\n    this.storySliderConfig = {\n      slidesToShow: 6,\n      slidesToScroll: 2,\n      infinite: false,\n      arrows: false,\n      dots: false,\n      swipeToSlide: true,\n      responsive: [{\n        breakpoint: 900,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    // Check if we should show navigation arrows based on screen size\n    this.updateNavArrowsVisibility();\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.updateScrollButtons();\n    }, 500);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n  onResize() {\n    this.updateNavArrowsVisibility();\n    this.updateScrollButtons();\n  }\n  updateNavArrowsVisibility() {\n    this.showNavArrows = window.innerWidth > 768;\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.stories) {\n          this.stories = response.stories.map(story => ({\n            ...story,\n            mediaUrl: story.media?.url || story.mediaUrl,\n            mediaType: story.media?.type || story.mediaType\n          }));\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n        setTimeout(() => this.updateScrollButtons(), 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    this.stories = [{\n      _id: '1',\n      user: {\n        _id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150'\n      },\n      media: {\n        type: 'image',\n        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600',\n      mediaType: 'image',\n      caption: 'Check out this amazing vintage denim jacket! 💙',\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n      views: 1250,\n      isActive: true,\n      products: [{\n        _id: 'p1',\n        product: {\n          _id: 'prod1',\n          name: 'Vintage Denim Jacket',\n          price: 89.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300',\n            isPrimary: true\n          }]\n        },\n        position: {\n          x: 30,\n          y: 40\n        }\n      }]\n    }];\n  }\n  // Navigation methods\n  scrollLeft() {\n    if (this.slickCarousel) {\n      this.slickCarousel.slickPrev();\n    }\n  }\n  scrollRight() {\n    if (this.slickCarousel) {\n      this.slickCarousel.slickNext();\n    }\n  }\n  updateScrollButtons() {\n    // This will be updated based on slick carousel state\n    this.canScrollLeft = this.currentStoryIndex > 0;\n    this.canScrollRight = this.currentStoryIndex < this.stories.length - 1;\n  }\n  // Story viewer methods\n  openStory(story, index) {\n    this.currentStoryIndex = index;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.clearStoryTimer();\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n  }\n  nextStory() {\n    if (this.currentStoryIndex < this.stories.length - 1) {\n      this.currentStoryIndex++;\n      this.startStoryTimer();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.startStoryTimer();\n    }\n  }\n  getCurrentStory() {\n    return this.stories[this.currentStoryIndex] || this.stories[0];\n  }\n  // Progress tracking\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) return 100;\n    if (index > this.currentStoryIndex) return 0;\n    if (this.progressStartTime) {\n      const elapsed = Date.now() - this.progressStartTime;\n      return Math.min(elapsed / this.storyDuration * 100, 100);\n    }\n    return 0;\n  }\n  startStoryTimer() {\n    this.clearStoryTimer();\n    this.progressStartTime = Date.now();\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n  clearStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n      this.progressTimer = null;\n    }\n    this.progressStartTime = 0;\n  }\n  // Story interaction methods\n  onStoryClick(event) {\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else if (clickX > windowWidth * 2 / 3) {\n      this.nextStory();\n    }\n  }\n  // Touch handling\n  onTouchStart(event) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.clearStoryTimer(); // Pause story progress on long press\n    }, 500);\n  }\n  onTouchMove(event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onTouchEnd(event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n    const touchDuration = Date.now() - this.touchStartTime;\n    if (touchDuration < 500) {\n      // Short tap - treat as click\n      const touch = event.changedTouches[0];\n      this.onStoryClick({\n        clientX: touch.clientX\n      });\n    } else {\n      // Long press ended - resume story progress\n      this.startStoryTimer();\n    }\n  }\n  // Product interaction methods\n  toggleProductTags() {\n    this.showProductTags = !this.showProductTags;\n  }\n  openProductDetails(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  // Story actions\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Implement like API call\n  }\n  shareStory() {\n    // TODO: Implement share functionality\n    console.log('Share story');\n  }\n  saveStory() {\n    // TODO: Implement save functionality\n    console.log('Save story');\n  }\n  // Add story functionality\n  onAdd() {\n    this.router.navigate(['/stories/create']);\n  }\n  // Utility methods\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slickCarousel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow)(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 27,\n      vars: 10,\n      consts: [[\"storiesContainer\", \"\"], [\"slickCarousel\", \"\"], [\"feedCover\", \"\"], [\"storyVideo\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [1, \"create-story-btn\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"stories-slider-wrapper\"], [1, \"nav-arrow\", \"nav-arrow-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"stories-slider\", 3, \"config\"], [\"ngxSlickItem\", \"\", 1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"ngxSlickItem\", \"\", \"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-arrow\", \"nav-arrow-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [\"class\", \"stories-overlay\", 4, \"ngIf\"], [\"ngxSlickItem\", \"\", 1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [\"class\", \"shopping-bag-indicator\", \"title\", \"Shoppable content\", 4, \"ngIf\"], [\"class\", \"product-count-badge\", 4, \"ngIf\"], [\"title\", \"Shoppable content\", 1, \"shopping-bag-indicator\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-count-badge\"], [1, \"stories-overlay\"], [1, \"stories-content\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [\"alt\", \"Avatar\", 1, \"story-header-avatar\", 3, \"src\"], [1, \"story-header-info\"], [1, \"username\"], [1, \"time\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-media\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [\"class\", \"story-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-video\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"shopping-bag-btn\", 3, \"active\", \"click\", 4, \"ngIf\"], [1, \"story-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [1, \"fas\", \"fa-bookmark\"], [\"class\", \"story-nav-btn story-nav-prev\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-nav-btn story-nav-next\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-image\", 3, \"src\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-video\", 3, \"src\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-dot\"], [1, \"product-tag-info\"], [1, \"product-tag-image\", 3, \"src\", \"alt\"], [1, \"product-tag-details\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"shopping-bag-btn\", 3, \"click\"], [1, \"product-count\"], [1, \"story-nav-btn\", \"story-nav-prev\", 3, \"click\"], [1, \"story-nav-btn\", \"story-nav-next\", 3, \"click\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4, 0)(2, \"div\", 5)(3, \"h3\", 6);\n          i0.ɵɵtext(4, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelement(6, \"i\", 8);\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"Create\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollLeft());\n          });\n          i0.ɵɵelement(11, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ngx-slick-carousel\", 12, 1)(14, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_div_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"div\", 15)(17, \"div\", 16);\n          i0.ɵɵelement(18, \"img\", 17);\n          i0.ɵɵelementStart(19, \"span\", 18);\n          i0.ɵɵtext(20, \"+\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 19);\n          i0.ɵɵtext(22, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, ViewAddStoriesComponent_div_23_Template, 9, 7, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollRight());\n          });\n          i0.ɵɵelement(25, \"i\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(26, ViewAddStoriesComponent_div_26_Template, 28, 12, \"div\", 23);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollLeft);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"config\", ctx.storySliderConfig);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollRight);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, SlickCarouselModule, i5.SlickCarouselComponent, i5.SlickItemDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  overflow: hidden;\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 8px;\\n    padding: 12px;\\n    border-radius: 12px;\\n  }\\n}\\n\\n.stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  color: white;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n}\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: white;\\n  transform: scale(1.1);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.nav-arrow.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.nav-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n}\\n@media (max-width: 768px) {\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 0 8px;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n}\\n@media (max-width: 768px) {\\n  .story-item[_ngcontent-%COMP%] {\\n    margin: 0 6px;\\n  }\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  padding: 3px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.story-avatar.has-products[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.8;\\n  }\\n}\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    width: 54px;\\n    height: 54px;\\n  }\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.shopping-bag-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-indicator[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n  .shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: white;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: 80px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n  }\\n}\\n\\n.product-count-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -8px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  white-space: nowrap;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .product-count-badge[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 1px 4px;\\n  }\\n}\\n\\n\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: 2px dashed rgba(255, 255, 255, 0.5);\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  font-size: 18px;\\n  font-weight: bold;\\n  color: #fff;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  bottom: -2px;\\n  right: -2px;\\n  z-index: 1;\\n  border: 2px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n@media (max-width: 768px) {\\n  .add-story-plus[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    font-size: 14px;\\n  }\\n}\\n\\n\\n\\n.stories-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95));\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-overlay[_ngcontent-%COMP%] {\\n    background: #000;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n.stories-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 90%;\\n  max-width: 400px;\\n  height: 80vh;\\n  max-height: 700px;\\n  background: #000;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100vh;\\n    max-height: none;\\n    border-radius: 0;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(50px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n.progress-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 4px;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 3px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n  overflow: hidden;\\n}\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progressFill 5s linear;\\n}\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  border-radius: 2px;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progressFill {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story-header[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);\\n  color: #fff;\\n  z-index: 10;\\n}\\n@media (max-width: 768px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n\\n.story-header-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-header-avatar[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n\\n.story-header-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 2px;\\n}\\n@media (max-width: 768px) {\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-weight: 400;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #fff;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n@media (max-width: 768px) {\\n  .close-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  background: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n\\n.story-image[_ngcontent-%COMP%], .story-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.product-tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 3px solid #667eea;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_ripple 2s infinite;\\n}\\n.product-tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #667eea;\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_ripple {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);\\n  }\\n}\\n.product-tag-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  min-width: 200px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.product-tag-image[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.product-tag-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 14px;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #667eea;\\n  font-weight: 700;\\n  font-size: 16px;\\n}\\n\\n.shopping-bag-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  right: 20px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 56px;\\n  height: 56px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\\n  z-index: 5;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);\\n}\\n.shopping-bag-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  animation: _ngcontent-%COMP%_bounce 0.6s ease;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: #f5576c;\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  border: 2px solid white;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n    bottom: 16px;\\n    right: 16px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    font-size: 10px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-10px);\\n  }\\n  80% {\\n    transform: translateY(-5px);\\n  }\\n}\\n.story-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  left: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  z-index: 5;\\n}\\n@media (max-width: 768px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 16px;\\n    left: 16px;\\n    gap: 12px;\\n  }\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n}\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.action-btn.liked[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  color: white;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n@media (max-width: 768px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n.story-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n  z-index: 5;\\n}\\n.story-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.story-nav-btn.story-nav-prev[_ngcontent-%COMP%] {\\n  left: 20px;\\n}\\n.story-nav-btn.story-nav-next[_ngcontent-%COMP%] {\\n  right: 20px;\\n}\\n.story-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n@media (max-width: 768px) {\\n  .story-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n    padding: 8px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n.story-item.loading[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .shopping-bag-btn[_ngcontent-%COMP%]:focus, .nav-arrow[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #667eea;\\n  outline-offset: 2px;\\n}\\n\\n@media (prefers-contrast: high) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    border: 2px solid #000;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    border: 2px solid #fff;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "SlickCarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "story_r4", "products", "length", "ɵɵlistener", "ViewAddStoriesComponent_div_23_Template_div_click_0_listener", "ctx_r2", "ɵɵrestoreView", "_r2", "$implicit", "i_r5", "index", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "openStory", "ɵɵtemplate", "ViewAddStoriesComponent_div_23_div_5_Template", "ViewAddStoriesComponent_div_23_div_8_Template", "ɵɵclassProp", "ɵɵproperty", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "i_r8", "currentStoryIndex", "ɵɵstyleProp", "getProgressWidth", "getCurrentStory", "mediaUrl", "ViewAddStoriesComponent_div_26_div_17_div_1_Template_div_click_0_listener", "$event", "productTag_r10", "_r9", "openProductDetails", "product", "stopPropagation", "position", "x", "y", "images", "url", "name", "ɵɵtextInterpolate1", "price", "ViewAddStoriesComponent_div_26_div_17_div_1_Template", "tmp_5_0", "ViewAddStoriesComponent_div_26_button_18_Template_button_click_0_listener", "_r11", "toggleProductTags", "showProductTags", "getProductCount", "ViewAddStoriesComponent_div_26_button_26_Template_button_click_0_listener", "_r12", "previousStory", "ViewAddStoriesComponent_div_26_button_27_Template_button_click_0_listener", "_r13", "nextStory", "ViewAddStoriesComponent_div_26_div_4_Template", "ViewAddStoriesComponent_div_26_Template_button_click_12_listener", "_r7", "closeStories", "ViewAddStoriesComponent_div_26_Template_div_click_14_listener", "onStoryClick", "ViewAddStoriesComponent_div_26_Template_div_touchstart_14_listener", "onTouchStart", "ViewAddStoriesComponent_div_26_Template_div_touchmove_14_listener", "onTouchMove", "ViewAddStoriesComponent_div_26_Template_div_touchend_14_listener", "onTouchEnd", "ViewAddStoriesComponent_div_26_img_15_Template", "ViewAddStoriesComponent_div_26_video_16_Template", "ViewAddStoriesComponent_div_26_div_17_Template", "ViewAddStoriesComponent_div_26_button_18_Template", "ViewAddStoriesComponent_div_26_Template_button_click_20_listener", "toggleLike", "ViewAddStoriesComponent_div_26_Template_button_click_22_listener", "shareStory", "ViewAddStoriesComponent_div_26_Template_button_click_24_listener", "saveStory", "ViewAddStoriesComponent_div_26_button_26_Template", "ViewAddStoriesComponent_div_26_button_27_Template", "stories", "getTimeAgo", "createdAt", "mediaType", "tmp_10_0", "hasProducts", "isLiked", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "currentUser", "isLoadingStories", "isOpen", "canScrollLeft", "canScrollRight", "showNavArrows", "storyDuration", "progressStartTime", "touchStartTime", "subscriptions", "storySliderConfig", "slidesToShow", "slidesToScroll", "infinite", "arrows", "dots", "swipeToSlide", "responsive", "breakpoint", "settings", "ngOnInit", "loadStories", "currentUser$", "subscribe", "updateNavArrowsVisibility", "ngAfterViewInit", "setTimeout", "updateScrollButtons", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "clearStoryTimer", "onResize", "window", "innerWidth", "push", "get", "apiUrl", "next", "response", "success", "map", "story", "media", "type", "loadFallbackStories", "error", "console", "_id", "fullName", "caption", "Date", "now", "toISOString", "expiresAt", "views", "isActive", "isPrimary", "scrollLeft", "slickCarousel", "slick<PERSON>rev", "scrollRight", "slickNext", "startStoryTimer", "document", "body", "style", "overflow", "pauseAllVideos", "elapsed", "Math", "min", "progressTimer", "clearTimeout", "event", "clickX", "clientX", "windowWidth", "longPressTimer", "touchDuration", "touch", "changedTouches", "navigate", "log", "onAdd", "dateString", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "videos", "querySelectorAll", "video", "pause", "handleKeydown", "key", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_Template_button_click_5_listener", "_r1", "ViewAddStoriesComponent_Template_button_click_10_listener", "ViewAddStoriesComponent_Template_div_click_14_listener", "ViewAddStoriesComponent_div_23_Template", "ViewAddStoriesComponent_Template_button_click_24_listener", "ViewAddStoriesComponent_div_26_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "SlickCarouselComponent", "SlickItemDirective", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild, HostListener, AfterViewInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Subscription } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\n\r\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\r\n\r\ninterface Story {\r\n  _id: string;\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  media: {\r\n    type: 'image' | 'video';\r\n    url: string;\r\n    thumbnail?: string;\r\n    duration?: number;\r\n  };\r\n  mediaUrl: string; // For backward compatibility\r\n  mediaType: 'image' | 'video'; // For backward compatibility\r\n  caption?: string;\r\n  createdAt: string;\r\n  expiresAt: string;\r\n  views: number;\r\n  isActive: boolean;\r\n  products?: Array<{\r\n    _id: string;\r\n    product: {\r\n      _id: string;\r\n      name: string;\r\n      price: number;\r\n      images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;\r\n    };\r\n    position?: {\r\n      x: number;\r\n      y: number;\r\n    };\r\n    size?: string;\r\n    color?: string;\r\n  }>;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-view-add-stories',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, SlickCarouselModule],\r\n  templateUrl: './view-add-stories.component.html',\r\n  styleUrls: ['./view-add-stories.component.scss']\r\n})\r\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('slickCarousel', { static: false }) slickCarousel!: any;\r\n  @ViewChild('storyVideo', { static: false }) storyVideo!: ElementRef<HTMLVideoElement>;\r\n\r\n  currentUser: any = null;\r\n  stories: Story[] = [];\r\n  isLoadingStories = true;\r\n\r\n  // Story viewer state\r\n  isOpen = false;\r\n  currentStoryIndex = 0;\r\n  showProductTags = false;\r\n  isLiked = false;\r\n\r\n  // Navigation state\r\n  canScrollLeft = false;\r\n  canScrollRight = false;\r\n  showNavArrows = true;\r\n\r\n  // Progress tracking\r\n  private progressTimer: any;\r\n  private storyDuration = 15000; // 15 seconds default\r\n  private progressStartTime = 0;\r\n\r\n  // Touch handling\r\n  private touchStartTime = 0;\r\n  private longPressTimer: any;\r\n\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  // Slider configuration\r\n  storySliderConfig = {\r\n    slidesToShow: 6,\r\n    slidesToScroll: 2,\r\n    infinite: false,\r\n    arrows: false,\r\n    dots: false,\r\n    swipeToSlide: true,\r\n    responsive: [\r\n      { breakpoint: 900, settings: { slidesToShow: 4 } },\r\n      { breakpoint: 600, settings: { slidesToShow: 3 } }\r\n    ]\r\n  };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private authService: AuthService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadStories();\r\n    this.authService.currentUser$.subscribe(user => {\r\n      this.currentUser = user;\r\n    });\r\n\r\n    // Check if we should show navigation arrows based on screen size\r\n    this.updateNavArrowsVisibility();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    setTimeout(() => {\r\n      this.updateScrollButtons();\r\n    }, 500);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n    this.clearStoryTimer();\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize() {\r\n    this.updateNavArrowsVisibility();\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  private updateNavArrowsVisibility() {\r\n    this.showNavArrows = window.innerWidth > 768;\r\n  }\r\n\r\n  loadStories() {\r\n    this.isLoadingStories = true;\r\n    this.subscriptions.push(\r\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\r\n        next: (response) => {\r\n          if (response.success && response.stories) {\r\n            this.stories = response.stories.map((story: any) => ({\r\n              ...story,\r\n              mediaUrl: story.media?.url || story.mediaUrl,\r\n              mediaType: story.media?.type || story.mediaType\r\n            }));\r\n          } else {\r\n            this.loadFallbackStories();\r\n          }\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => this.updateScrollButtons(), 100);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading stories:', error);\r\n          this.loadFallbackStories();\r\n          this.isLoadingStories = false;\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  loadFallbackStories() {\r\n    this.stories = [\r\n      {\r\n        _id: '1',\r\n        user: {\r\n          _id: '1',\r\n          username: 'fashionista_maya',\r\n          fullName: 'Maya Rodriguez',\r\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150'\r\n        },\r\n        media: {\r\n          type: 'image',\r\n          url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600'\r\n        },\r\n        mediaUrl: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600',\r\n        mediaType: 'image',\r\n        caption: 'Check out this amazing vintage denim jacket! 💙',\r\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\r\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\r\n        views: 1250,\r\n        isActive: true,\r\n        products: [\r\n          {\r\n            _id: 'p1',\r\n            product: {\r\n              _id: 'prod1',\r\n              name: 'Vintage Denim Jacket',\r\n              price: 89.99,\r\n              images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300', isPrimary: true }]\r\n            },\r\n            position: { x: 30, y: 40 }\r\n          }\r\n        ]\r\n      }\r\n    ];\r\n  }\r\n\r\n  // Navigation methods\r\n  scrollLeft() {\r\n    if (this.slickCarousel) {\r\n      this.slickCarousel.slickPrev();\r\n    }\r\n  }\r\n\r\n  scrollRight() {\r\n    if (this.slickCarousel) {\r\n      this.slickCarousel.slickNext();\r\n    }\r\n  }\r\n\r\n  updateScrollButtons() {\r\n    // This will be updated based on slick carousel state\r\n    this.canScrollLeft = this.currentStoryIndex > 0;\r\n    this.canScrollRight = this.currentStoryIndex < this.stories.length - 1;\r\n  }\r\n\r\n  // Story viewer methods\r\n  openStory(story: Story, index: number) {\r\n    this.currentStoryIndex = index;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n  }\r\n\r\n  closeStories() {\r\n    this.isOpen = false;\r\n    this.clearStoryTimer();\r\n    this.pauseAllVideos();\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  nextStory() {\r\n    if (this.currentStoryIndex < this.stories.length - 1) {\r\n      this.currentStoryIndex++;\r\n      this.startStoryTimer();\r\n    } else {\r\n      this.closeStories();\r\n    }\r\n  }\r\n\r\n  previousStory() {\r\n    if (this.currentStoryIndex > 0) {\r\n      this.currentStoryIndex--;\r\n      this.startStoryTimer();\r\n    }\r\n  }\r\n\r\n  getCurrentStory(): Story {\r\n    return this.stories[this.currentStoryIndex] || this.stories[0];\r\n  }\r\n\r\n  // Progress tracking\r\n  getProgressWidth(index: number): number {\r\n    if (index < this.currentStoryIndex) return 100;\r\n    if (index > this.currentStoryIndex) return 0;\r\n\r\n    if (this.progressStartTime) {\r\n      const elapsed = Date.now() - this.progressStartTime;\r\n      return Math.min((elapsed / this.storyDuration) * 100, 100);\r\n    }\r\n    return 0;\r\n  }\r\n\r\n  private startStoryTimer() {\r\n    this.clearStoryTimer();\r\n    this.progressStartTime = Date.now();\r\n\r\n    this.progressTimer = setTimeout(() => {\r\n      this.nextStory();\r\n    }, this.storyDuration);\r\n  }\r\n\r\n  private clearStoryTimer() {\r\n    if (this.progressTimer) {\r\n      clearTimeout(this.progressTimer);\r\n      this.progressTimer = null;\r\n    }\r\n    this.progressStartTime = 0;\r\n  }\r\n\r\n  // Story interaction methods\r\n  onStoryClick(event: MouseEvent) {\r\n    const clickX = event.clientX;\r\n    const windowWidth = window.innerWidth;\r\n\r\n    if (clickX < windowWidth / 3) {\r\n      this.previousStory();\r\n    } else if (clickX > (windowWidth * 2) / 3) {\r\n      this.nextStory();\r\n    }\r\n  }\r\n\r\n  // Touch handling\r\n  onTouchStart(event: TouchEvent) {\r\n    this.touchStartTime = Date.now();\r\n    this.longPressTimer = setTimeout(() => {\r\n      this.clearStoryTimer(); // Pause story progress on long press\r\n    }, 500);\r\n  }\r\n\r\n  onTouchMove(event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n  }\r\n\r\n  onTouchEnd(event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n\r\n    const touchDuration = Date.now() - this.touchStartTime;\r\n    if (touchDuration < 500) {\r\n      // Short tap - treat as click\r\n      const touch = event.changedTouches[0];\r\n      this.onStoryClick({ clientX: touch.clientX } as MouseEvent);\r\n    } else {\r\n      // Long press ended - resume story progress\r\n      this.startStoryTimer();\r\n    }\r\n  }\r\n\r\n  // Product interaction methods\r\n  toggleProductTags() {\r\n    this.showProductTags = !this.showProductTags;\r\n  }\r\n\r\n  openProductDetails(product: any) {\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  // Story actions\r\n  toggleLike() {\r\n    this.isLiked = !this.isLiked;\r\n    // TODO: Implement like API call\r\n  }\r\n\r\n  shareStory() {\r\n    // TODO: Implement share functionality\r\n    console.log('Share story');\r\n  }\r\n\r\n  saveStory() {\r\n    // TODO: Implement save functionality\r\n    console.log('Save story');\r\n  }\r\n\r\n  // Add story functionality\r\n  onAdd() {\r\n    this.router.navigate(['/stories/create']);\r\n  }\r\n\r\n  // Utility methods\r\n  getTimeAgo(dateString: string): string {\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\r\n\r\n    if (diffInMinutes < 1) return 'now';\r\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\r\n\r\n    const diffInHours = Math.floor(diffInMinutes / 60);\r\n    if (diffInHours < 24) return `${diffInHours}h`;\r\n\r\n    const diffInDays = Math.floor(diffInHours / 24);\r\n    return `${diffInDays}d`;\r\n  }\r\n\r\n  private pauseAllVideos() {\r\n    const videos = document.querySelectorAll('video');\r\n    videos.forEach(video => {\r\n      if (video.pause) {\r\n        video.pause();\r\n      }\r\n    });\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeydown(event: KeyboardEvent) {\r\n    if (!this.isOpen) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        this.previousStory();\r\n        break;\r\n      case 'ArrowRight':\r\n        this.nextStory();\r\n        break;\r\n      case 'Escape':\r\n        this.closeStories();\r\n        break;\r\n    }\r\n  }\r\n}", "<div class=\"stories-container\" #storiesContainer>\n  <!-- Stories Header -->\n  <div class=\"stories-header\">\n    <h3 class=\"stories-title\">Stories</h3>\n    <button class=\"create-story-btn\" (click)=\"onAdd()\">\n      <i class=\"fas fa-plus\"></i>\n      <span>Create</span>\n    </button>\n  </div>\n\n  <!-- Stories Slider with Navigation -->\n  <div class=\"stories-slider-wrapper\">\n    <!-- Navigation Arrow Left -->\n    <button class=\"nav-arrow nav-arrow-left\"\n            (click)=\"scrollLeft()\"\n            [disabled]=\"!canScrollLeft\"\n            [class.hidden]=\"!showNavArrows\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n\n    <!-- Carousel for Stories (Thumbnails) -->\n    <ngx-slick-carousel class=\"stories-slider\" [config]=\"storySliderConfig\" #slickCarousel>\n      <!-- Add Story Button -->\n      <div ngxSlickItem class=\"story-item add-story-item\" (click)=\"onAdd()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar add-avatar\">\n            <div class=\"story-avatar-inner\">\n              <img\n                class=\"story-avatar-img\"\n                [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\"\n                alt=\"Your Story\"\n              />\n              <span class=\"add-story-plus\">+</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"story-username\">Your Story</div>\n      </div>\n\n      <!-- Existing Stories -->\n      <div ngxSlickItem class=\"story-item\"\n           *ngFor=\"let story of stories; let i = index\"\n           (click)=\"openStory(story, i)\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar\" [class.has-products]=\"story.products && story.products.length > 0\">\n            <div class=\"story-avatar-inner\">\n              <img\n                class=\"story-avatar-img\"\n                [src]=\"story.user.avatar\"\n                [alt]=\"story.user.username\"\n              />\n              <!-- Shopping bag indicator -->\n              <div class=\"shopping-bag-indicator\"\n                   *ngIf=\"story.products && story.products.length > 0\"\n                   title=\"Shoppable content\">\n                <i class=\"fas fa-shopping-bag\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"story-username\">{{ story.user.username }}</div>\n        <!-- Product count badge -->\n        <div class=\"product-count-badge\"\n             *ngIf=\"story.products && story.products.length > 0\">\n          {{ story.products.length }} item{{ story.products.length > 1 ? 's' : '' }}\n        </div>\n      </div>\n    </ngx-slick-carousel>\n\n    <!-- Navigation Arrow Right -->\n    <button class=\"nav-arrow nav-arrow-right\"\n            (click)=\"scrollRight()\"\n            [disabled]=\"!canScrollRight\"\n            [class.hidden]=\"!showNavArrows\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n\n<!-- Fullscreen Stories Viewer (shown when isOpen is true) -->\n<div class=\"stories-overlay\" *ngIf=\"isOpen\">\n  <div class=\"stories-content\" #feedCover>\n    <!-- Progress Bars -->\n    <div class=\"progress-container\">\n      <div class=\"progress-bar\"\n           *ngFor=\"let story of stories; let i = index\"\n           [class.active]=\"i === currentStoryIndex\"\n           [class.completed]=\"i < currentStoryIndex\">\n        <div class=\"progress-fill\"\n             [style.width.%]=\"getProgressWidth(i)\"></div>\n      </div>\n    </div>\n\n    <!-- Story Header -->\n    <div class=\"story-header\">\n      <img class=\"story-header-avatar\" [src]=\"getCurrentStory().user.avatar\" alt=\"Avatar\"/>\n      <div class=\"story-header-info\">\n        <span class=\"username\">{{ getCurrentStory().user.username }}</span>\n        <span class=\"time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</span>\n      </div>\n      <button (click)=\"closeStories()\" class=\"close-btn\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Story Media -->\n    <div class=\"story-media\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n\n      <!-- Image Story -->\n      <img *ngIf=\"getCurrentStory().mediaType === 'image'\"\n           [src]=\"getCurrentStory().mediaUrl\"\n           class=\"story-image\"/>\n\n      <!-- Video Story -->\n      <video *ngIf=\"getCurrentStory().mediaType === 'video'\"\n             class=\"story-video\"\n             [src]=\"getCurrentStory().mediaUrl\"\n             autoplay muted #storyVideo></video>\n\n      <!-- Product Tags -->\n      <div class=\"product-tags\" *ngIf=\"showProductTags && getCurrentStory()?.products\">\n        <div class=\"product-tag\"\n             *ngFor=\"let productTag of getCurrentStory()?.products\"\n             [style.left.%]=\"productTag.position?.x || 50\"\n             [style.top.%]=\"productTag.position?.y || 50\"\n             (click)=\"openProductDetails(productTag.product); $event.stopPropagation()\">\n          <div class=\"product-tag-dot\"></div>\n          <div class=\"product-tag-info\">\n            <img [src]=\"productTag.product.images[0].url\"\n                 [alt]=\"productTag.product.name\"\n                 class=\"product-tag-image\">\n            <div class=\"product-tag-details\">\n              <span class=\"product-tag-name\">{{ productTag.product.name }}</span>\n              <span class=\"product-tag-price\">${{ productTag.product.price }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Shopping Bag Button -->\n      <button class=\"shopping-bag-btn\"\n              *ngIf=\"hasProducts()\"\n              (click)=\"toggleProductTags(); $event.stopPropagation()\"\n              [class.active]=\"showProductTags\">\n        <i class=\"fas fa-shopping-bag\"></i>\n        <span class=\"product-count\">{{ getProductCount() }}</span>\n      </button>\n    </div>\n\n    <!-- Story Actions -->\n    <div class=\"story-actions\">\n      <button class=\"action-btn like-btn\"\n              (click)=\"toggleLike()\"\n              [class.liked]=\"isLiked\">\n        <i class=\"fas fa-heart\"></i>\n      </button>\n      <button class=\"action-btn share-btn\" (click)=\"shareStory()\">\n        <i class=\"fas fa-share\"></i>\n      </button>\n      <button class=\"action-btn save-btn\" (click)=\"saveStory()\">\n        <i class=\"fas fa-bookmark\"></i>\n      </button>\n    </div>\n\n    <!-- Navigation Arrows -->\n    <button class=\"story-nav-btn story-nav-prev\"\n            (click)=\"previousStory()\"\n            *ngIf=\"currentStoryIndex > 0\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n    <button class=\"story-nav-btn story-nav-next\"\n            (click)=\"nextStory()\"\n            *ngIf=\"currentStoryIndex < stories.length - 1\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,mBAAmB,QAAQ,oBAAoB;;;;;;;;;;;IC2C1CC,EAAA,CAAAC,cAAA,cAE+B;IAC7BD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMZH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,QAAA,CAAAC,QAAA,CAAAC,MAAA,WAAAF,QAAA,CAAAC,QAAA,CAAAC,MAAA,qBACF;;;;;;IAzBFT,EAAA,CAAAC,cAAA,cAEmC;IAA9BD,EAAA,CAAAU,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,QAAA,GAAAK,MAAA,CAAAG,SAAA;MAAA,MAAAC,IAAA,GAAAJ,MAAA,CAAAK,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAd,QAAA,EAAAS,IAAA,CAAmB;IAAA,EAAC;IAG5BhB,EAFJ,CAAAC,cAAA,cAAoC,cAC2D,cAC3D;IAC9BD,EAAA,CAAAE,SAAA,cAIE;IAEFF,EAAA,CAAAsB,UAAA,IAAAC,6CAAA,kBAE+B;IAKrCvB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE3DH,EAAA,CAAAsB,UAAA,IAAAE,6CAAA,kBACyD;IAG3DxB,EAAA,CAAAG,YAAA,EAAM;;;;IAtBwBH,EAAA,CAAAK,SAAA,GAAkE;IAAlEL,EAAA,CAAAyB,WAAA,iBAAAlB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAkE;IAItFT,EAAA,CAAAK,SAAA,GAAyB;IACzBL,EADA,CAAA0B,UAAA,QAAAnB,QAAA,CAAAoB,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAyB,QAAAtB,QAAA,CAAAoB,IAAA,CAAAG,QAAA,CACE;IAIvB9B,EAAA,CAAAK,SAAA,EAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAnB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;IAOjCT,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA+B,iBAAA,CAAAxB,QAAA,CAAAoB,IAAA,CAAAG,QAAA,CAAyB;IAG/C9B,EAAA,CAAAK,SAAA,EAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAnB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;;;;;IAqBzDT,EAAA,CAAAC,cAAA,cAG+C;IAC7CD,EAAA,CAAAE,SAAA,cACiD;IACnDF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EADA,CAAAyB,WAAA,WAAAO,IAAA,KAAAd,MAAA,CAAAe,iBAAA,CAAwC,cAAAD,IAAA,GAAAd,MAAA,CAAAe,iBAAA,CACC;IAEvCjC,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAkC,WAAA,UAAAhB,MAAA,CAAAiB,gBAAA,CAAAH,IAAA,OAAqC;;;;;IAwB5ChC,EAAA,CAAAE,SAAA,cAE0B;;;;IADrBF,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAC,QAAA,EAAArC,EAAA,CAAA6B,aAAA,CAAkC;;;;;IAIvC7B,EAAA,CAAAE,SAAA,mBAG0C;;;;IADnCF,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAC,QAAA,EAAArC,EAAA,CAAA6B,aAAA,CAAkC;;;;;;IAKvC7B,EAAA,CAAAC,cAAA,cAIgF;IAA3ED,EAAA,CAAAU,UAAA,mBAAA4B,0EAAAC,MAAA;MAAA,MAAAC,cAAA,GAAAxC,EAAA,CAAAa,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAwB,kBAAA,CAAAF,cAAA,CAAAG,OAAA,CAAsC;MAAA,OAAA3C,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAC7E5C,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,cAE+B;IAE7BF,EADF,CAAAC,cAAA,cAAiC,eACA;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAGrEJ,EAHqE,CAAAG,YAAA,EAAO,EAClE,EACF,EACF;;;;IAZDH,EADA,CAAAkC,WAAA,UAAAM,cAAA,CAAAK,QAAA,kBAAAL,cAAA,CAAAK,QAAA,CAAAC,CAAA,aAA6C,SAAAN,cAAA,CAAAK,QAAA,kBAAAL,cAAA,CAAAK,QAAA,CAAAE,CAAA,aACD;IAIxC/C,EAAA,CAAAK,SAAA,GAAwC;IACxCL,EADA,CAAA0B,UAAA,QAAAc,cAAA,CAAAG,OAAA,CAAAK,MAAA,IAAAC,GAAA,EAAAjD,EAAA,CAAA6B,aAAA,CAAwC,QAAAW,cAAA,CAAAG,OAAA,CAAAO,IAAA,CACT;IAGHlD,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAA+B,iBAAA,CAAAS,cAAA,CAAAG,OAAA,CAAAO,IAAA,CAA6B;IAC5BlD,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAmD,kBAAA,MAAAX,cAAA,CAAAG,OAAA,CAAAS,KAAA,KAA+B;;;;;IAbvEpD,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAsB,UAAA,IAAA+B,oDAAA,kBAIgF;IAYlFrD,EAAA,CAAAG,YAAA,EAAM;;;;;IAfwBH,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAA0B,UAAA,aAAA4B,OAAA,GAAApC,MAAA,CAAAkB,eAAA,qBAAAkB,OAAA,CAAA9C,QAAA,CAA8B;;;;;;IAkB5DR,EAAA,CAAAC,cAAA,iBAGyC;IADjCD,EAAA,CAAAU,UAAA,mBAAA6C,0EAAAhB,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA2C,IAAA;MAAA,MAAAtC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAuC,iBAAA,EAAmB;MAAA,OAAAzD,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAE7D5C,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAuB;IACrDJ,EADqD,CAAAG,YAAA,EAAO,EACnD;;;;IAHDH,EAAA,CAAAyB,WAAA,WAAAP,MAAA,CAAAwC,eAAA,CAAgC;IAEV1D,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAyC,eAAA,GAAuB;;;;;;IAoBvD3D,EAAA,CAAAC,cAAA,iBAEsC;IAD9BD,EAAA,CAAAU,UAAA,mBAAAkD,0EAAA;MAAA5D,EAAA,CAAAa,aAAA,CAAAgD,IAAA;MAAA,MAAA3C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA4C,aAAA,EAAe;IAAA,EAAC;IAE/B9D,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAEuD;IAD/CD,EAAA,CAAAU,UAAA,mBAAAqD,0EAAA;MAAA/D,EAAA,CAAAa,aAAA,CAAAmD,IAAA;MAAA,MAAA9C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA+C,SAAA,EAAW;IAAA,EAAC;IAE3BjE,EAAA,CAAAE,SAAA,YAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA/FTH,EAHJ,CAAAC,cAAA,cAA4C,iBACF,cAEN;IAC9BD,EAAA,CAAAsB,UAAA,IAAA4C,6CAAA,kBAG+C;IAIjDlE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,cAAqF;IAEnFF,EADF,CAAAC,cAAA,cAA+B,eACN;IAAAD,EAAA,CAAAI,MAAA,GAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,IAA6C;IAClEJ,EADkE,CAAAG,YAAA,EAAO,EACnE;IACNH,EAAA,CAAAC,cAAA,kBAAmD;IAA3CD,EAAA,CAAAU,UAAA,mBAAAyD,iEAAA;MAAAnE,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAmD,YAAA,EAAc;IAAA,EAAC;IAC9BrE,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAIqC;IAAhCD,EAHA,CAAAU,UAAA,mBAAA4D,8DAAA/B,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAqD,YAAA,CAAAhC,MAAA,CAAoB;IAAA,EAAC,wBAAAiC,mEAAAjC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAChBF,MAAA,CAAAuD,YAAA,CAAAlC,MAAA,CAAoB;IAAA,EAAC,uBAAAmC,kEAAAnC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACtBF,MAAA,CAAAyD,WAAA,CAAApC,MAAA,CAAmB;IAAA,EAAC,sBAAAqC,iEAAArC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACrBF,MAAA,CAAA2D,UAAA,CAAAtC,MAAA,CAAkB;IAAA,EAAC;IAkClCvC,EA/BA,CAAAsB,UAAA,KAAAwD,8CAAA,kBAE0B,KAAAC,gDAAA,oBAMQ,KAAAC,8CAAA,kBAG+C,KAAAC,iDAAA,qBAuBxC;IAI3CjF,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2B,kBAGO;IADxBD,EAAA,CAAAU,UAAA,mBAAAwE,iEAAA;MAAAlF,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAiE,UAAA,EAAY;IAAA,EAAC;IAE5BnF,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAvBD,EAAA,CAAAU,UAAA,mBAAA0E,iEAAA;MAAApF,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAmE,UAAA,EAAY;IAAA,EAAC;IACzDrF,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA4E,iEAAA;MAAAtF,EAAA,CAAAa,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAqE,SAAA,EAAW;IAAA,EAAC;IACvDvF,EAAA,CAAAE,SAAA,aAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAS,EACL;IAQNH,EALA,CAAAsB,UAAA,KAAAkE,iDAAA,qBAEsC,KAAAC,iDAAA,qBAKiB;IAI3DzF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IA/FuBH,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAA0B,UAAA,YAAAR,MAAA,CAAAwE,OAAA,CAAY;IAUF1F,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAT,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAqC;IAE7C7B,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAkB,eAAA,GAAAT,IAAA,CAAAG,QAAA,CAAqC;IACzC9B,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAyE,UAAA,CAAAzE,MAAA,CAAAkB,eAAA,GAAAwD,SAAA,EAA6C;IAe5D5F,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAkB,eAAA,GAAAyD,SAAA,aAA6C;IAK3C7F,EAAA,CAAAK,SAAA,EAA6C;IAA7CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAkB,eAAA,GAAAyD,SAAA,aAA6C;IAM1B7F,EAAA,CAAAK,SAAA,EAAoD;IAApDL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAwC,eAAA,MAAAoC,QAAA,GAAA5E,MAAA,CAAAkB,eAAA,qBAAA0D,QAAA,CAAAtF,QAAA,EAAoD;IAqBtER,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAA6E,WAAA,GAAmB;IAYpB/F,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAyB,WAAA,UAAAP,MAAA,CAAA8E,OAAA,CAAuB;IAcxBhG,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAe,iBAAA,KAA2B;IAK3BjC,EAAA,CAAAK,SAAA,EAA4C;IAA5CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAe,iBAAA,GAAAf,MAAA,CAAAwE,OAAA,CAAAjF,MAAA,KAA4C;;;ADxHzD,OAAM,MAAOwF,uBAAuB;EA4ClCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IA3CrB,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAZ,OAAO,GAAY,EAAE;IACrB,KAAAa,gBAAgB,GAAG,IAAI;IAEvB;IACA,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAvE,iBAAiB,GAAG,CAAC;IACrB,KAAAyB,eAAe,GAAG,KAAK;IACvB,KAAAsC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAS,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI;IAIZ,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,iBAAiB,GAAG,CAAC;IAE7B;IACQ,KAAAC,cAAc,GAAG,CAAC;IAGlB,KAAAC,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAAC,iBAAiB,GAAG;MAClBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CACV;QAAEC,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAER,YAAY,EAAE;QAAC;MAAE,CAAE,EAClD;QAAEO,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAER,YAAY,EAAE;QAAC;MAAE,CAAE;KAErD;EAME;EAEHS,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACtB,WAAW,CAACuB,YAAY,CAACC,SAAS,CAAClG,IAAI,IAAG;MAC7C,IAAI,CAAC2E,WAAW,GAAG3E,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACmG,yBAAyB,EAAE;EAClC;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnB,aAAa,CAACoB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACT,yBAAyB,EAAE;IAChC,IAAI,CAACG,mBAAmB,EAAE;EAC5B;EAEQH,yBAAyBA,CAAA;IAC/B,IAAI,CAACnB,aAAa,GAAG6B,MAAM,CAACC,UAAU,GAAG,GAAG;EAC9C;EAEAd,WAAWA,CAAA;IACT,IAAI,CAACpB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,aAAa,CAAC2B,IAAI,CACrB,IAAI,CAACtC,IAAI,CAACuC,GAAG,CAAM,GAAG7I,WAAW,CAAC8I,MAAM,UAAU,CAAC,CAACf,SAAS,CAAC;MAC5DgB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACpD,OAAO,EAAE;UACxC,IAAI,CAACA,OAAO,GAAGoD,QAAQ,CAACpD,OAAO,CAACsD,GAAG,CAAEC,KAAU,KAAM;YACnD,GAAGA,KAAK;YACR5G,QAAQ,EAAE4G,KAAK,CAACC,KAAK,EAAEjG,GAAG,IAAIgG,KAAK,CAAC5G,QAAQ;YAC5CwD,SAAS,EAAEoD,KAAK,CAACC,KAAK,EAAEC,IAAI,IAAIF,KAAK,CAACpD;WACvC,CAAC,CAAC;SACJ,MAAM;UACL,IAAI,CAACuD,mBAAmB,EAAE;;QAE5B,IAAI,CAAC7C,gBAAgB,GAAG,KAAK;QAC7ByB,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;MACnD,CAAC;MACDoB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAAC7C,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEA6C,mBAAmBA,CAAA;IACjB,IAAI,CAAC1D,OAAO,GAAG,CACb;MACE6D,GAAG,EAAE,GAAG;MACR5H,IAAI,EAAE;QACJ4H,GAAG,EAAE,GAAG;QACRzH,QAAQ,EAAE,kBAAkB;QAC5B0H,QAAQ,EAAE,gBAAgB;QAC1B5H,MAAM,EAAE;OACT;MACDsH,KAAK,EAAE;QACLC,IAAI,EAAE,OAAO;QACblG,GAAG,EAAE;OACN;MACDZ,QAAQ,EAAE,iEAAiE;MAC3EwD,SAAS,EAAE,OAAO;MAClB4D,OAAO,EAAE,iDAAiD;MAC1D7D,SAAS,EAAE,IAAI8D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,IAAI;MACdvJ,QAAQ,EAAE,CACR;QACE+I,GAAG,EAAE,IAAI;QACT5G,OAAO,EAAE;UACP4G,GAAG,EAAE,OAAO;UACZrG,IAAI,EAAE,sBAAsB;UAC5BE,KAAK,EAAE,KAAK;UACZJ,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE,iEAAiE;YAAE+G,SAAS,EAAE;UAAI,CAAE;SACrG;QACDnH,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB;KAEJ,CACF;EACH;EAEA;EACAkH,UAAUA,CAAA;IACR,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,SAAS,EAAE;;EAElC;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACG,SAAS,EAAE;;EAElC;EAEApC,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACxE,iBAAiB,GAAG,CAAC;IAC/C,IAAI,CAACyE,cAAc,GAAG,IAAI,CAACzE,iBAAiB,GAAG,IAAI,CAACyD,OAAO,CAACjF,MAAM,GAAG,CAAC;EACxE;EAEA;EACAY,SAASA,CAAC4H,KAAY,EAAEhI,KAAa;IACnC,IAAI,CAACgB,iBAAiB,GAAGhB,KAAK;IAC9B,IAAI,CAACuF,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC9C,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC4G,eAAe,EAAE;IACtBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEArG,YAAYA,CAAA;IACV,IAAI,CAACmC,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC8B,eAAe,EAAE;IACtB,IAAI,CAACqC,cAAc,EAAE;IACrBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAzG,SAASA,CAAA;IACP,IAAI,IAAI,CAAChC,iBAAiB,GAAG,IAAI,CAACyD,OAAO,CAACjF,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACwB,iBAAiB,EAAE;MACxB,IAAI,CAACqI,eAAe,EAAE;KACvB,MAAM;MACL,IAAI,CAACjG,YAAY,EAAE;;EAEvB;EAEAP,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7B,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACqI,eAAe,EAAE;;EAE1B;EAEAlI,eAAeA,CAAA;IACb,OAAO,IAAI,CAACsD,OAAO,CAAC,IAAI,CAACzD,iBAAiB,CAAC,IAAI,IAAI,CAACyD,OAAO,CAAC,CAAC,CAAC;EAChE;EAEA;EACAvD,gBAAgBA,CAAClB,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACgB,iBAAiB,EAAE,OAAO,GAAG;IAC9C,IAAIhB,KAAK,GAAG,IAAI,CAACgB,iBAAiB,EAAE,OAAO,CAAC;IAE5C,IAAI,IAAI,CAAC4E,iBAAiB,EAAE;MAC1B,MAAM+D,OAAO,GAAGlB,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC9C,iBAAiB;MACnD,OAAOgE,IAAI,CAACC,GAAG,CAAEF,OAAO,GAAG,IAAI,CAAChE,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;;IAE5D,OAAO,CAAC;EACV;EAEQ0D,eAAeA,CAAA;IACrB,IAAI,CAAChC,eAAe,EAAE;IACtB,IAAI,CAACzB,iBAAiB,GAAG6C,IAAI,CAACC,GAAG,EAAE;IAEnC,IAAI,CAACoB,aAAa,GAAG/C,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC/D,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAAC2C,aAAa,CAAC;EACxB;EAEQ0B,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACyC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;;IAE3B,IAAI,CAAClE,iBAAiB,GAAG,CAAC;EAC5B;EAEA;EACAtC,YAAYA,CAAC0G,KAAiB;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACE,OAAO;IAC5B,MAAMC,WAAW,GAAG5C,MAAM,CAACC,UAAU;IAErC,IAAIyC,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACtH,aAAa,EAAE;KACrB,MAAM,IAAIoH,MAAM,GAAIE,WAAW,GAAG,CAAC,GAAI,CAAC,EAAE;MACzC,IAAI,CAACnH,SAAS,EAAE;;EAEpB;EAEA;EACAQ,YAAYA,CAACwG,KAAiB;IAC5B,IAAI,CAACnE,cAAc,GAAG4C,IAAI,CAACC,GAAG,EAAE;IAChC,IAAI,CAAC0B,cAAc,GAAGrD,UAAU,CAAC,MAAK;MACpC,IAAI,CAACM,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA3D,WAAWA,CAACsG,KAAiB;IAC3B,IAAI,IAAI,CAACI,cAAc,EAAE;MACvBL,YAAY,CAAC,IAAI,CAACK,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAxG,UAAUA,CAACoG,KAAiB;IAC1B,IAAI,IAAI,CAACI,cAAc,EAAE;MACvBL,YAAY,CAAC,IAAI,CAACK,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,MAAMC,aAAa,GAAG5B,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC7C,cAAc;IACtD,IAAIwE,aAAa,GAAG,GAAG,EAAE;MACvB;MACA,MAAMC,KAAK,GAAGN,KAAK,CAACO,cAAc,CAAC,CAAC,CAAC;MACrC,IAAI,CAACjH,YAAY,CAAC;QAAE4G,OAAO,EAAEI,KAAK,CAACJ;MAAO,CAAgB,CAAC;KAC5D,MAAM;MACL;MACA,IAAI,CAACb,eAAe,EAAE;;EAE1B;EAEA;EACA7G,iBAAiBA,CAAA;IACf,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAhB,kBAAkBA,CAACC,OAAY;IAC7B,IAAI,CAACwD,MAAM,CAACsF,QAAQ,CAAC,CAAC,UAAU,EAAE9I,OAAO,CAAC4G,GAAG,CAAC,CAAC;EACjD;EAEA;EACApE,UAAUA,CAAA;IACR,IAAI,CAACa,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;EACF;EAEAX,UAAUA,CAAA;IACR;IACAiE,OAAO,CAACoC,GAAG,CAAC,aAAa,CAAC;EAC5B;EAEAnG,SAASA,CAAA;IACP;IACA+D,OAAO,CAACoC,GAAG,CAAC,YAAY,CAAC;EAC3B;EAEA;EACAC,KAAKA,CAAA;IACH,IAAI,CAACxF,MAAM,CAACsF,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA;EACA9F,UAAUA,CAACiG,UAAkB;IAC3B,MAAMjC,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMmC,IAAI,GAAG,IAAInC,IAAI,CAACkC,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGjB,IAAI,CAACkB,KAAK,CAAC,CAACpC,GAAG,CAACqC,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMG,WAAW,GAAGpB,IAAI,CAACkB,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGrB,IAAI,CAACkB,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEQvB,cAAcA,CAAA;IACpB,MAAMwB,MAAM,GAAG5B,QAAQ,CAAC6B,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAAChE,OAAO,CAACkE,KAAK,IAAG;MACrB,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAGAC,aAAaA,CAACtB,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACzE,MAAM,EAAE;IAElB,QAAQyE,KAAK,CAACuB,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAAC1I,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACG,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACI,YAAY,EAAE;QACnB;;EAEN;;;uBAtVW4B,uBAAuB,EAAAjG,EAAA,CAAAyM,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA3M,EAAA,CAAAyM,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA7M,EAAA,CAAAyM,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvB9G,uBAAuB;MAAA+G,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAvBnN,EAAA,CAAAU,UAAA,oBAAA2M,kDAAA9K,MAAA;YAAA,OAAA6K,GAAA,CAAA7E,QAAA,CAAAhG,MAAA,CAAgB;UAAA,UAAAvC,EAAA,CAAAsN,eAAA,CAAO,qBAAAC,mDAAAhL,MAAA;YAAA,OAAvB6K,GAAA,CAAAb,aAAA,CAAAhK,MAAA,CAAqB;UAAA,UAAAvC,EAAA,CAAAwN,iBAAA,CAAE;;;;;;;;;;;UCrDhCxN,EAHJ,CAAAC,cAAA,gBAAiD,aAEnB,YACA;UAAAD,EAAA,CAAAI,MAAA,cAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,gBAAmD;UAAlBD,EAAA,CAAAU,UAAA,mBAAA+M,yDAAA;YAAAzN,EAAA,CAAAa,aAAA,CAAA6M,GAAA;YAAA,OAAA1N,EAAA,CAAAoB,WAAA,CAASgM,GAAA,CAAAzB,KAAA,EAAO;UAAA,EAAC;UAChD3L,EAAA,CAAAE,SAAA,WAA2B;UAC3BF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAI,MAAA,aAAM;UAEhBJ,EAFgB,CAAAG,YAAA,EAAO,EACZ,EACL;UAKJH,EAFF,CAAAC,cAAA,aAAoC,kBAKM;UAFhCD,EAAA,CAAAU,UAAA,mBAAAiN,0DAAA;YAAA3N,EAAA,CAAAa,aAAA,CAAA6M,GAAA;YAAA,OAAA1N,EAAA,CAAAoB,WAAA,CAASgM,GAAA,CAAAnD,UAAA,EAAY;UAAA,EAAC;UAG5BjK,EAAA,CAAAE,SAAA,aAAmC;UACrCF,EAAA,CAAAG,YAAA,EAAS;UAKPH,EAFF,CAAAC,cAAA,iCAAuF,eAEf;UAAlBD,EAAA,CAAAU,UAAA,mBAAAkN,uDAAA;YAAA5N,EAAA,CAAAa,aAAA,CAAA6M,GAAA;YAAA,OAAA1N,EAAA,CAAAoB,WAAA,CAASgM,GAAA,CAAAzB,KAAA,EAAO;UAAA,EAAC;UAG/D3L,EAFJ,CAAAC,cAAA,eAAoC,eACG,eACH;UAC9BD,EAAA,CAAAE,SAAA,eAIE;UACFF,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAGpCJ,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UACxCJ,EADwC,CAAAG,YAAA,EAAM,EACxC;UAGNH,EAAA,CAAAsB,UAAA,KAAAuM,uCAAA,kBAEmC;UAyBrC7N,EAAA,CAAAG,YAAA,EAAqB;UAGrBH,EAAA,CAAAC,cAAA,kBAGwC;UAFhCD,EAAA,CAAAU,UAAA,mBAAAoN,0DAAA;YAAA9N,EAAA,CAAAa,aAAA,CAAA6M,GAAA;YAAA,OAAA1N,EAAA,CAAAoB,WAAA,CAASgM,GAAA,CAAAhD,WAAA,EAAa;UAAA,EAAC;UAG7BpK,EAAA,CAAAE,SAAA,aAAoC;UAG1CF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGNH,EAAA,CAAAsB,UAAA,KAAAyM,uCAAA,oBAA4C;;;UAhEhC/N,EAAA,CAAAK,SAAA,IAA+B;UAA/BL,EAAA,CAAAyB,WAAA,YAAA2L,GAAA,CAAAzG,aAAA,CAA+B;UAD/B3G,EAAA,CAAA0B,UAAA,cAAA0L,GAAA,CAAA3G,aAAA,CAA2B;UAMQzG,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAA0B,UAAA,WAAA0L,GAAA,CAAApG,iBAAA,CAA4B;UAQ3DhH,EAAA,CAAAK,SAAA,GAA0D;UAA1DL,EAAA,CAAA0B,UAAA,SAAA0L,GAAA,CAAA9G,WAAA,kBAAA8G,GAAA,CAAA9G,WAAA,CAAA1E,MAAA,kCAAA5B,EAAA,CAAA6B,aAAA,CAA0D;UAY7C7B,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAA0B,UAAA,YAAA0L,GAAA,CAAA1H,OAAA,CAAY;UAgC7B1F,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAAyB,WAAA,YAAA2L,GAAA,CAAAzG,aAAA,CAA+B;UAD/B3G,EAAA,CAAA0B,UAAA,cAAA0L,GAAA,CAAA1G,cAAA,CAA4B;UAQV1G,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAA0B,UAAA,SAAA0L,GAAA,CAAA5G,MAAA,CAAY;;;qBD5B9B5G,YAAY,EAAAoO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAErO,WAAW,EAAEE,mBAAmB,EAAAoO,EAAA,CAAAC,sBAAA,EAAAD,EAAA,CAAAE,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}