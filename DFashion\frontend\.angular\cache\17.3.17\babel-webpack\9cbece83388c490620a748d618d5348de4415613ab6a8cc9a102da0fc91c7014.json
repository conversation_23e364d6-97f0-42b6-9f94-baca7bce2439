{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers.js';\n\n// TODO(FW-2832): types\nconst attachComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (delegate, container, component, cssClasses, componentProps, inline) {\n    var _a;\n    if (delegate) {\n      return delegate.attachViewToDom(container, component, componentProps, cssClasses);\n    }\n    if (!inline && typeof component !== 'string' && !(component instanceof HTMLElement)) {\n      throw new Error('framework delegate is missing');\n    }\n    const el = typeof component === 'string' ? (_a = container.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(component) : component;\n    if (cssClasses) {\n      cssClasses.forEach(c => el.classList.add(c));\n    }\n    if (componentProps) {\n      Object.assign(el, componentProps);\n    }\n    container.appendChild(el);\n    yield new Promise(resolve => componentOnReady(el, resolve));\n    return el;\n  });\n  return function attachComponent(_x, _x2, _x3, _x4, _x5, _x6) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst detachComponent = (delegate, element) => {\n  if (element) {\n    if (delegate) {\n      const container = element.parentElement;\n      return delegate.removeViewFromDom(container, element);\n    }\n    element.remove();\n  }\n  return Promise.resolve();\n};\nconst CoreDelegate = () => {\n  let BaseComponent;\n  let Reference;\n  const attachViewToDom = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(function* (parentElement, userComponent, userComponentProps = {}, cssClasses = []) {\n      var _a, _b;\n      BaseComponent = parentElement;\n      let ChildComponent;\n      /**\n       * If passing in a component via the `component` props\n       * we need to append it inside of our overlay component.\n       */\n      if (userComponent) {\n        /**\n         * If passing in the tag name, create\n         * the element otherwise just get a reference\n         * to the component.\n         */\n        const el = typeof userComponent === 'string' ? (_a = BaseComponent.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(userComponent) : userComponent;\n        /**\n         * Add any css classes passed in\n         * via the cssClasses prop on the overlay.\n         */\n        cssClasses.forEach(c => el.classList.add(c));\n        /**\n         * Add any props passed in\n         * via the componentProps prop on the overlay.\n         */\n        Object.assign(el, userComponentProps);\n        /**\n         * Finally, append the component\n         * inside of the overlay component.\n         */\n        BaseComponent.appendChild(el);\n        ChildComponent = el;\n        yield new Promise(resolve => componentOnReady(el, resolve));\n      } else if (BaseComponent.children.length > 0 && (BaseComponent.tagName === 'ION-MODAL' || BaseComponent.tagName === 'ION-POPOVER')) {\n        /**\n         * The delegate host wrapper el is only needed for modals and popovers\n         * because they allow the dev to provide custom content to the overlay.\n         */\n        const root = ChildComponent = BaseComponent.children[0];\n        if (!root.classList.contains('ion-delegate-host')) {\n          /**\n           * If the root element is not a delegate host, it means\n           * that the overlay has not been presented yet and we need\n           * to create the containing element with the specified classes.\n           */\n          const el = (_b = BaseComponent.ownerDocument) === null || _b === void 0 ? void 0 : _b.createElement('div');\n          // Add a class to track if the root element was created by the delegate.\n          el.classList.add('ion-delegate-host');\n          cssClasses.forEach(c => el.classList.add(c));\n          // Move each child from the original template to the new parent element.\n          el.append(...BaseComponent.children);\n          // Append the new parent element to the original parent element.\n          BaseComponent.appendChild(el);\n          /**\n           * Update the ChildComponent to be the\n           * newly created div in the event that one\n           * does not already exist.\n           */\n          ChildComponent = el;\n        }\n      }\n      /**\n       * Get the root of the app and\n       * add the overlay there.\n       */\n      const app = document.querySelector('ion-app') || document.body;\n      /**\n       * Create a placeholder comment so that\n       * we can return this component to where\n       * it was previously.\n       */\n      Reference = document.createComment('ionic teleport');\n      BaseComponent.parentNode.insertBefore(Reference, BaseComponent);\n      app.appendChild(BaseComponent);\n      /**\n       * We return the child component rather than the overlay\n       * reference itself since modal and popover will\n       * use this to wait for any Ionic components in the child view\n       * to be ready (i.e. componentOnReady) when using the\n       * lazy loaded component bundle.\n       *\n       * However, we fall back to returning BaseComponent\n       * in the event that a modal or popover is presented\n       * with no child content.\n       */\n      return ChildComponent !== null && ChildComponent !== void 0 ? ChildComponent : BaseComponent;\n    });\n    return function attachViewToDom(_x7, _x8) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  const removeViewFromDom = () => {\n    /**\n     * Return component to where it was previously in the DOM.\n     */\n    if (BaseComponent && Reference) {\n      Reference.parentNode.insertBefore(BaseComponent, Reference);\n      Reference.remove();\n    }\n    return Promise.resolve();\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\nexport { CoreDelegate as C, attachComponent as a, detachComponent as d };", "map": {"version": 3, "names": ["c", "componentOnReady", "attachComponent", "_ref", "_asyncToGenerator", "delegate", "container", "component", "cssClasses", "componentProps", "inline", "_a", "attachViewToDom", "HTMLElement", "Error", "el", "ownerDocument", "createElement", "for<PERSON>ach", "classList", "add", "Object", "assign", "append<PERSON><PERSON><PERSON>", "Promise", "resolve", "_x", "_x2", "_x3", "_x4", "_x5", "_x6", "apply", "arguments", "detachComponent", "element", "parentElement", "removeViewFromDom", "remove", "CoreDelegate", "BaseComponent", "Reference", "_ref2", "userComponent", "userComponentProps", "_b", "ChildComponent", "children", "length", "tagName", "root", "contains", "append", "app", "document", "querySelector", "body", "createComment", "parentNode", "insertBefore", "_x7", "_x8", "C", "a", "d"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/components/framework-delegate.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers.js';\n\n// TODO(FW-2832): types\nconst attachComponent = async (delegate, container, component, cssClasses, componentProps, inline) => {\n    var _a;\n    if (delegate) {\n        return delegate.attachViewToDom(container, component, componentProps, cssClasses);\n    }\n    if (!inline && typeof component !== 'string' && !(component instanceof HTMLElement)) {\n        throw new Error('framework delegate is missing');\n    }\n    const el = typeof component === 'string' ? (_a = container.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(component) : component;\n    if (cssClasses) {\n        cssClasses.forEach((c) => el.classList.add(c));\n    }\n    if (componentProps) {\n        Object.assign(el, componentProps);\n    }\n    container.appendChild(el);\n    await new Promise((resolve) => componentOnReady(el, resolve));\n    return el;\n};\nconst detachComponent = (delegate, element) => {\n    if (element) {\n        if (delegate) {\n            const container = element.parentElement;\n            return delegate.removeViewFromDom(container, element);\n        }\n        element.remove();\n    }\n    return Promise.resolve();\n};\nconst CoreDelegate = () => {\n    let BaseComponent;\n    let Reference;\n    const attachViewToDom = async (parentElement, userComponent, userComponentProps = {}, cssClasses = []) => {\n        var _a, _b;\n        BaseComponent = parentElement;\n        let ChildComponent;\n        /**\n         * If passing in a component via the `component` props\n         * we need to append it inside of our overlay component.\n         */\n        if (userComponent) {\n            /**\n             * If passing in the tag name, create\n             * the element otherwise just get a reference\n             * to the component.\n             */\n            const el = typeof userComponent === 'string' ? (_a = BaseComponent.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(userComponent) : userComponent;\n            /**\n             * Add any css classes passed in\n             * via the cssClasses prop on the overlay.\n             */\n            cssClasses.forEach((c) => el.classList.add(c));\n            /**\n             * Add any props passed in\n             * via the componentProps prop on the overlay.\n             */\n            Object.assign(el, userComponentProps);\n            /**\n             * Finally, append the component\n             * inside of the overlay component.\n             */\n            BaseComponent.appendChild(el);\n            ChildComponent = el;\n            await new Promise((resolve) => componentOnReady(el, resolve));\n        }\n        else if (BaseComponent.children.length > 0 &&\n            (BaseComponent.tagName === 'ION-MODAL' || BaseComponent.tagName === 'ION-POPOVER')) {\n            /**\n             * The delegate host wrapper el is only needed for modals and popovers\n             * because they allow the dev to provide custom content to the overlay.\n             */\n            const root = (ChildComponent = BaseComponent.children[0]);\n            if (!root.classList.contains('ion-delegate-host')) {\n                /**\n                 * If the root element is not a delegate host, it means\n                 * that the overlay has not been presented yet and we need\n                 * to create the containing element with the specified classes.\n                 */\n                const el = (_b = BaseComponent.ownerDocument) === null || _b === void 0 ? void 0 : _b.createElement('div');\n                // Add a class to track if the root element was created by the delegate.\n                el.classList.add('ion-delegate-host');\n                cssClasses.forEach((c) => el.classList.add(c));\n                // Move each child from the original template to the new parent element.\n                el.append(...BaseComponent.children);\n                // Append the new parent element to the original parent element.\n                BaseComponent.appendChild(el);\n                /**\n                 * Update the ChildComponent to be the\n                 * newly created div in the event that one\n                 * does not already exist.\n                 */\n                ChildComponent = el;\n            }\n        }\n        /**\n         * Get the root of the app and\n         * add the overlay there.\n         */\n        const app = document.querySelector('ion-app') || document.body;\n        /**\n         * Create a placeholder comment so that\n         * we can return this component to where\n         * it was previously.\n         */\n        Reference = document.createComment('ionic teleport');\n        BaseComponent.parentNode.insertBefore(Reference, BaseComponent);\n        app.appendChild(BaseComponent);\n        /**\n         * We return the child component rather than the overlay\n         * reference itself since modal and popover will\n         * use this to wait for any Ionic components in the child view\n         * to be ready (i.e. componentOnReady) when using the\n         * lazy loaded component bundle.\n         *\n         * However, we fall back to returning BaseComponent\n         * in the event that a modal or popover is presented\n         * with no child content.\n         */\n        return ChildComponent !== null && ChildComponent !== void 0 ? ChildComponent : BaseComponent;\n    };\n    const removeViewFromDom = () => {\n        /**\n         * Return component to where it was previously in the DOM.\n         */\n        if (BaseComponent && Reference) {\n            Reference.parentNode.insertBefore(BaseComponent, Reference);\n            Reference.remove();\n        }\n        return Promise.resolve();\n    };\n    return { attachViewToDom, removeViewFromDom };\n};\n\nexport { CoreDelegate as C, attachComponent as a, detachComponent as d };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,QAAQ,cAAc;;AAEpD;AACA,MAAMC,eAAe;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAK;IAClG,IAAIC,EAAE;IACN,IAAIN,QAAQ,EAAE;MACV,OAAOA,QAAQ,CAACO,eAAe,CAACN,SAAS,EAAEC,SAAS,EAAEE,cAAc,EAAED,UAAU,CAAC;IACrF;IACA,IAAI,CAACE,MAAM,IAAI,OAAOH,SAAS,KAAK,QAAQ,IAAI,EAAEA,SAAS,YAAYM,WAAW,CAAC,EAAE;MACjF,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;IACpD;IACA,MAAMC,EAAE,GAAG,OAAOR,SAAS,KAAK,QAAQ,GAAG,CAACI,EAAE,GAAGL,SAAS,CAACU,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,aAAa,CAACV,SAAS,CAAC,GAAGA,SAAS;IACtJ,IAAIC,UAAU,EAAE;MACZA,UAAU,CAACU,OAAO,CAAElB,CAAC,IAAKe,EAAE,CAACI,SAAS,CAACC,GAAG,CAACpB,CAAC,CAAC,CAAC;IAClD;IACA,IAAIS,cAAc,EAAE;MAChBY,MAAM,CAACC,MAAM,CAACP,EAAE,EAAEN,cAAc,CAAC;IACrC;IACAH,SAAS,CAACiB,WAAW,CAACR,EAAE,CAAC;IACzB,MAAM,IAAIS,OAAO,CAAEC,OAAO,IAAKxB,gBAAgB,CAACc,EAAE,EAAEU,OAAO,CAAC,CAAC;IAC7D,OAAOV,EAAE;EACb,CAAC;EAAA,gBAlBKb,eAAeA,CAAAwB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAA5B,IAAA,CAAA6B,KAAA,OAAAC,SAAA;EAAA;AAAA,GAkBpB;AACD,MAAMC,eAAe,GAAGA,CAAC7B,QAAQ,EAAE8B,OAAO,KAAK;EAC3C,IAAIA,OAAO,EAAE;IACT,IAAI9B,QAAQ,EAAE;MACV,MAAMC,SAAS,GAAG6B,OAAO,CAACC,aAAa;MACvC,OAAO/B,QAAQ,CAACgC,iBAAiB,CAAC/B,SAAS,EAAE6B,OAAO,CAAC;IACzD;IACAA,OAAO,CAACG,MAAM,CAAC,CAAC;EACpB;EACA,OAAOd,OAAO,CAACC,OAAO,CAAC,CAAC;AAC5B,CAAC;AACD,MAAMc,YAAY,GAAGA,CAAA,KAAM;EACvB,IAAIC,aAAa;EACjB,IAAIC,SAAS;EACb,MAAM7B,eAAe;IAAA,IAAA8B,KAAA,GAAAtC,iBAAA,CAAG,WAAOgC,aAAa,EAAEO,aAAa,EAAEC,kBAAkB,GAAG,CAAC,CAAC,EAAEpC,UAAU,GAAG,EAAE,EAAK;MACtG,IAAIG,EAAE,EAAEkC,EAAE;MACVL,aAAa,GAAGJ,aAAa;MAC7B,IAAIU,cAAc;MAClB;AACR;AACA;AACA;MACQ,IAAIH,aAAa,EAAE;QACf;AACZ;AACA;AACA;AACA;QACY,MAAM5B,EAAE,GAAG,OAAO4B,aAAa,KAAK,QAAQ,GAAG,CAAChC,EAAE,GAAG6B,aAAa,CAACxB,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,aAAa,CAAC0B,aAAa,CAAC,GAAGA,aAAa;QACtK;AACZ;AACA;AACA;QACYnC,UAAU,CAACU,OAAO,CAAElB,CAAC,IAAKe,EAAE,CAACI,SAAS,CAACC,GAAG,CAACpB,CAAC,CAAC,CAAC;QAC9C;AACZ;AACA;AACA;QACYqB,MAAM,CAACC,MAAM,CAACP,EAAE,EAAE6B,kBAAkB,CAAC;QACrC;AACZ;AACA;AACA;QACYJ,aAAa,CAACjB,WAAW,CAACR,EAAE,CAAC;QAC7B+B,cAAc,GAAG/B,EAAE;QACnB,MAAM,IAAIS,OAAO,CAAEC,OAAO,IAAKxB,gBAAgB,CAACc,EAAE,EAAEU,OAAO,CAAC,CAAC;MACjE,CAAC,MACI,IAAIe,aAAa,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,KACrCR,aAAa,CAACS,OAAO,KAAK,WAAW,IAAIT,aAAa,CAACS,OAAO,KAAK,aAAa,CAAC,EAAE;QACpF;AACZ;AACA;AACA;QACY,MAAMC,IAAI,GAAIJ,cAAc,GAAGN,aAAa,CAACO,QAAQ,CAAC,CAAC,CAAE;QACzD,IAAI,CAACG,IAAI,CAAC/B,SAAS,CAACgC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UAC/C;AAChB;AACA;AACA;AACA;UACgB,MAAMpC,EAAE,GAAG,CAAC8B,EAAE,GAAGL,aAAa,CAACxB,aAAa,MAAM,IAAI,IAAI6B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5B,aAAa,CAAC,KAAK,CAAC;UAC1G;UACAF,EAAE,CAACI,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;UACrCZ,UAAU,CAACU,OAAO,CAAElB,CAAC,IAAKe,EAAE,CAACI,SAAS,CAACC,GAAG,CAACpB,CAAC,CAAC,CAAC;UAC9C;UACAe,EAAE,CAACqC,MAAM,CAAC,GAAGZ,aAAa,CAACO,QAAQ,CAAC;UACpC;UACAP,aAAa,CAACjB,WAAW,CAACR,EAAE,CAAC;UAC7B;AAChB;AACA;AACA;AACA;UACgB+B,cAAc,GAAG/B,EAAE;QACvB;MACJ;MACA;AACR;AACA;AACA;MACQ,MAAMsC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,SAAS,CAAC,IAAID,QAAQ,CAACE,IAAI;MAC9D;AACR;AACA;AACA;AACA;MACQf,SAAS,GAAGa,QAAQ,CAACG,aAAa,CAAC,gBAAgB,CAAC;MACpDjB,aAAa,CAACkB,UAAU,CAACC,YAAY,CAAClB,SAAS,EAAED,aAAa,CAAC;MAC/Da,GAAG,CAAC9B,WAAW,CAACiB,aAAa,CAAC;MAC9B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,OAAOM,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGN,aAAa;IAChG,CAAC;IAAA,gBAvFK5B,eAAeA,CAAAgD,GAAA,EAAAC,GAAA;MAAA,OAAAnB,KAAA,CAAAV,KAAA,OAAAC,SAAA;IAAA;EAAA,GAuFpB;EACD,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC5B;AACR;AACA;IACQ,IAAIG,aAAa,IAAIC,SAAS,EAAE;MAC5BA,SAAS,CAACiB,UAAU,CAACC,YAAY,CAACnB,aAAa,EAAEC,SAAS,CAAC;MAC3DA,SAAS,CAACH,MAAM,CAAC,CAAC;IACtB;IACA,OAAOd,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B,CAAC;EACD,OAAO;IAAEb,eAAe;IAAEyB;EAAkB,CAAC;AACjD,CAAC;AAED,SAASE,YAAY,IAAIuB,CAAC,EAAE5D,eAAe,IAAI6D,CAAC,EAAE7B,eAAe,IAAI8B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}