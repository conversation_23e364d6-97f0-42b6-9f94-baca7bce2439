{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { SocialFeedComponent } from '../posts/social-feed.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction SocialMediaComponent_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.wishlistCount);\n  }\n}\nfunction SocialMediaComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cartCount);\n  }\n}\nfunction SocialMediaComponent_div_30_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_30_div_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goProfile());\n    });\n    i0.ɵɵelement(2, \"i\", 34);\n    i0.ɵɵtext(3, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_30_div_2_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goSettings());\n    });\n    i0.ɵɵelement(5, \"i\", 35);\n    i0.ɵɵtext(6, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_30_div_2_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelement(8, \"i\", 36);\n    i0.ɵɵtext(9, \" Logout \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SocialMediaComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"img\", 30);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_30_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserMenu());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SocialMediaComponent_div_30_div_2_Template, 10, 0, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.currentUser.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showUserMenu);\n  }\n}\nfunction SocialMediaComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_ng_template_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goLogin());\n    });\n    i0.ɵɵtext(1, \" Login \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SocialMediaComponent_app_social_feed_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-social-feed\");\n  }\n}\nfunction SocialMediaComponent_router_outlet_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nfunction SocialMediaComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.wishlistCount);\n  }\n}\nfunction SocialMediaComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cartCount);\n  }\n}\nfunction SocialMediaComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_59_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeCreateMenu());\n    });\n    i0.ɵɵelementStart(1, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_59_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Create Content\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_59_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createPost());\n    });\n    i0.ɵɵelement(6, \"i\", 43);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Create Post\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10, \"Share photos with products\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_59_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createStory());\n    });\n    i0.ɵɵelement(12, \"i\", 44);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Create Story\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\");\n    i0.ɵɵtext(16, \"Share temporary content\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_59_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goLive());\n    });\n    i0.ɵɵelement(18, \"i\", 45);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Go Live\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22, \"Live shopping session\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_div_59_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeCreateMenu());\n    });\n    i0.ɵɵtext(24, \" Cancel \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SocialMediaComponent_button_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function SocialMediaComponent_button_60_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showCreateMenu());\n    });\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nexport let SocialMediaComponent = /*#__PURE__*/(() => {\n  class SocialMediaComponent {\n    constructor(router) {\n      this.router = router;\n      this.currentView = 'feed';\n      this.currentUser = null;\n      this.showUserMenu = false;\n      this.showCreateModal = false;\n      this.searchQuery = '';\n      this.cartCount = 0;\n      this.wishlistCount = 0;\n      this.isMobile = false;\n    }\n    ngOnInit() {\n      this.checkMobile();\n      this.loadCurrentUser();\n      this.loadCounts();\n      // Listen for route changes to update current view\n      this.router.events.subscribe(() => {\n        this.updateCurrentView();\n      });\n    }\n    checkMobile() {\n      this.isMobile = window.innerWidth <= 768;\n      window.addEventListener('resize', () => {\n        this.isMobile = window.innerWidth <= 768;\n      });\n    }\n    loadCurrentUser() {\n      // Get from auth service\n      this.currentUser = null;\n    }\n    loadCounts() {\n      // TODO: Get actual counts from services\n      this.cartCount = 3;\n      this.wishlistCount = 5;\n    }\n    updateCurrentView() {\n      const url = this.router.url;\n      if (url.includes('/shop')) this.currentView = 'shop';else if (url.includes('/wishlist')) this.currentView = 'wishlist';else if (url.includes('/cart')) this.currentView = 'cart';else if (url.includes('/home')) this.currentView = 'home';else this.currentView = 'feed';\n    }\n    // Navigation methods\n    goHome() {\n      this.router.navigate(['/home']);\n    }\n    goShop() {\n      this.router.navigate(['/shop']);\n    }\n    goWishlist() {\n      this.router.navigate(['/wishlist']);\n    }\n    goCart() {\n      this.router.navigate(['/cart']);\n    }\n    goProfile() {\n      this.router.navigate(['/profile']);\n      this.showUserMenu = false;\n    }\n    goSettings() {\n      this.router.navigate(['/settings']);\n      this.showUserMenu = false;\n    }\n    goLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    // User menu\n    toggleUserMenu() {\n      this.showUserMenu = !this.showUserMenu;\n    }\n    logout() {\n      // TODO: Implement logout\n      this.showUserMenu = false;\n      this.router.navigate(['/auth/login']);\n    }\n    // Search\n    search() {\n      if (this.searchQuery.trim()) {\n        this.router.navigate(['/search'], {\n          queryParams: {\n            q: this.searchQuery\n          }\n        });\n      }\n    }\n    // Create content\n    showCreateMenu() {\n      this.showCreateModal = true;\n    }\n    closeCreateMenu() {\n      this.showCreateModal = false;\n    }\n    createPost() {\n      this.router.navigate(['/create/post']);\n      this.closeCreateMenu();\n    }\n    createStory() {\n      this.router.navigate(['/create/story']);\n      this.closeCreateMenu();\n    }\n    goLive() {\n      this.router.navigate(['/live']);\n      this.closeCreateMenu();\n    }\n    static {\n      this.ɵfac = function SocialMediaComponent_Factory(t) {\n        return new (t || SocialMediaComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SocialMediaComponent,\n        selectors: [[\"app-social-media\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 61,\n        vars: 27,\n        consts: [[\"loginButton\", \"\"], [1, \"social-media-platform\"], [1, \"platform-header\"], [1, \"header-content\"], [1, \"logo-section\"], [1, \"platform-logo\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"search-section\"], [1, \"search-bar\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, or users...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"nav-actions\"], [1, \"nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-home\"], [1, \"fas\", \"fa-store\"], [1, \"fas\", \"fa-heart\"], [\"class\", \"badge\", 4, \"ngIf\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"user-menu\", 4, \"ngIf\", \"ngIfElse\"], [1, \"platform-content\"], [4, \"ngIf\"], [1, \"mobile-nav\"], [1, \"mobile-nav-btn\", 3, \"click\"], [1, \"create-btn\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [\"class\", \"mobile-badge\", 4, \"ngIf\"], [\"class\", \"create-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"fab-create\", 3, \"click\", 4, \"ngIf\"], [1, \"badge\"], [1, \"user-menu\"], [1, \"user-avatar\", 3, \"click\", \"src\", \"alt\"], [\"class\", \"user-dropdown\", 4, \"ngIf\"], [1, \"user-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fas\", \"fa-user\"], [1, \"fas\", \"fa-cog\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"btn-login\", 3, \"click\"], [1, \"mobile-badge\"], [1, \"create-modal\", 3, \"click\"], [1, \"create-content\", 3, \"click\"], [1, \"create-options\"], [1, \"create-option\", 3, \"click\"], [1, \"fas\", \"fa-camera\"], [1, \"fas\", \"fa-plus-circle\"], [1, \"fas\", \"fa-video\"], [1, \"btn-close-create\", 3, \"click\"], [1, \"fab-create\", 3, \"click\"]],\n        template: function SocialMediaComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"header\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\", 5);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_h1_click_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goHome());\n            });\n            i0.ɵɵelement(5, \"i\", 6);\n            i0.ɵɵtext(6, \" DFashion \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n            i0.ɵɵelement(9, \"i\", 9);\n            i0.ɵɵelementStart(10, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SocialMediaComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function SocialMediaComponent_Template_input_keyup_enter_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.search());\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 11)(12, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_12_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goHome());\n            });\n            i0.ɵɵelement(13, \"i\", 13);\n            i0.ɵɵelementStart(14, \"span\");\n            i0.ɵɵtext(15, \"Home\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goShop());\n            });\n            i0.ɵɵelement(17, \"i\", 14);\n            i0.ɵɵelementStart(18, \"span\");\n            i0.ɵɵtext(19, \"Shop\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_20_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goWishlist());\n            });\n            i0.ɵɵelement(21, \"i\", 15);\n            i0.ɵɵelementStart(22, \"span\");\n            i0.ɵɵtext(23, \"Wishlist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(24, SocialMediaComponent_span_24_Template, 2, 1, \"span\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_25_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goCart());\n            });\n            i0.ɵɵelement(26, \"i\", 17);\n            i0.ɵɵelementStart(27, \"span\");\n            i0.ɵɵtext(28, \"Cart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(29, SocialMediaComponent_span_29_Template, 2, 1, \"span\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(30, SocialMediaComponent_div_30_Template, 3, 3, \"div\", 18)(31, SocialMediaComponent_ng_template_31_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(33, \"main\", 19);\n            i0.ɵɵtemplate(34, SocialMediaComponent_app_social_feed_34_Template, 1, 0, \"app-social-feed\", 20)(35, SocialMediaComponent_router_outlet_35_Template, 1, 0, \"router-outlet\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"nav\", 21)(37, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_37_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goHome());\n            });\n            i0.ɵɵelement(38, \"i\", 13);\n            i0.ɵɵelementStart(39, \"span\");\n            i0.ɵɵtext(40, \"Home\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_41_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goShop());\n            });\n            i0.ɵɵelement(42, \"i\", 14);\n            i0.ɵɵelementStart(43, \"span\");\n            i0.ɵɵtext(44, \"Shop\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_45_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showCreateMenu());\n            });\n            i0.ɵɵelement(46, \"i\", 24);\n            i0.ɵɵelementStart(47, \"span\");\n            i0.ɵɵtext(48, \"Create\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_49_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goWishlist());\n            });\n            i0.ɵɵelement(50, \"i\", 15);\n            i0.ɵɵelementStart(51, \"span\");\n            i0.ɵɵtext(52, \"Wishlist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(53, SocialMediaComponent_span_53_Template, 2, 1, \"span\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SocialMediaComponent_Template_button_click_54_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goCart());\n            });\n            i0.ɵɵelement(55, \"i\", 17);\n            i0.ɵɵelementStart(56, \"span\");\n            i0.ɵɵtext(57, \"Cart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(58, SocialMediaComponent_span_58_Template, 2, 1, \"span\", 25);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(59, SocialMediaComponent_div_59_Template, 25, 0, \"div\", 26)(60, SocialMediaComponent_button_60_Template, 2, 0, \"button\", 27);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const loginButton_r8 = i0.ɵɵreference(32);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"home\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"shop\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"wishlist\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"cart\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser)(\"ngIfElse\", loginButton_r8);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"feed\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentView !== \"feed\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"home\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"shop\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"wishlist\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"cart\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showCreateModal);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, RouterOutlet, SocialFeedComponent],\n        styles: [\".social-media-platform[_ngcontent-%COMP%]{min-height:100vh;background:#f8f9fa}.platform-header[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #eee;position:sticky;top:0;z-index:1000;box-shadow:0 2px 4px #0000001a}.header-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;display:flex;align-items:center;justify-content:space-between;padding:12px 20px;gap:20px}.logo-section[_ngcontent-%COMP%]{flex-shrink:0}.platform-logo[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#007bff;margin:0;cursor:pointer;display:flex;align-items:center;gap:8px}.search-section[_ngcontent-%COMP%]{flex:1;max-width:400px}.search-bar[_ngcontent-%COMP%]{position:relative;width:100%}.search-bar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:12px;top:50%;transform:translateY(-50%);color:#666}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:10px 12px 10px 40px;border:1px solid #ddd;border-radius:20px;font-size:.9rem;background:#f8f9fa}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff;background:#fff}.nav-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;flex-shrink:0}.nav-btn[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:4px;background:none;border:none;color:#666;cursor:pointer;padding:8px 12px;border-radius:8px;transition:all .2s ease;position:relative;font-size:.8rem}.nav-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;color:#007bff}.nav-btn.active[_ngcontent-%COMP%]{color:#007bff;background:#e3f2fd}.nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.badge[_ngcontent-%COMP%]{position:absolute;top:4px;right:4px;background:#ff6b6b;color:#fff;border-radius:10px;padding:2px 6px;font-size:.7rem;min-width:16px;text-align:center}.user-menu[_ngcontent-%COMP%]{position:relative}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;cursor:pointer;border:2px solid transparent;transition:border-color .2s ease}.user-avatar[_ngcontent-%COMP%]:hover{border-color:#007bff}.user-dropdown[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;background:#fff;border:1px solid #eee;border-radius:8px;box-shadow:0 4px 12px #00000026;min-width:150px;z-index:1001;margin-top:8px}.dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:12px 16px;cursor:pointer;font-size:.9rem;color:#333;transition:background .2s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.dropdown-item[_ngcontent-%COMP%]:first-child{border-radius:8px 8px 0 0}.dropdown-item[_ngcontent-%COMP%]:last-child{border-radius:0 0 8px 8px}.btn-login[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:8px 16px;border-radius:6px;font-weight:500;cursor:pointer;transition:background .2s ease}.btn-login[_ngcontent-%COMP%]:hover{background:#0056b3}.platform-content[_ngcontent-%COMP%]{min-height:calc(100vh - 80px);padding-bottom:80px}.mobile-nav[_ngcontent-%COMP%]{display:none;position:fixed;bottom:0;left:0;right:0;background:#fff;border-top:1px solid #eee;padding:8px 0;z-index:1000}.mobile-nav[_ngcontent-%COMP%]{display:flex;justify-content:space-around;align-items:center}.mobile-nav-btn[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:4px;background:none;border:none;color:#666;cursor:pointer;padding:8px;border-radius:8px;transition:all .2s ease;position:relative;font-size:.7rem;min-width:60px}.mobile-nav-btn[_ngcontent-%COMP%]:hover, .mobile-nav-btn.active[_ngcontent-%COMP%]{color:#007bff}.mobile-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.mobile-nav-btn.create-btn[_ngcontent-%COMP%]{background:#007bff;color:#fff;border-radius:50%;width:50px;height:50px;margin-top:-10px}.mobile-nav-btn.create-btn[_ngcontent-%COMP%]:hover{background:#0056b3;color:#fff}.mobile-badge[_ngcontent-%COMP%]{position:absolute;top:2px;right:8px;background:#ff6b6b;color:#fff;border-radius:8px;padding:1px 4px;font-size:.6rem;min-width:14px;text-align:center}.create-modal[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#00000080;z-index:2000;display:flex;align-items:center;justify-content:center;padding:20px}.create-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:24px;max-width:400px;width:100%}.create-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 20px;text-align:center;font-size:1.3rem;color:#333}.create-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:20px}.create-option[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px;border:1px solid #eee;border-radius:8px;background:#fff;cursor:pointer;transition:all .2s ease;text-align:left}.create-option[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9fa}.create-option[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;color:#007bff;width:24px;text-align:center}.create-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:600;color:#333;display:block;margin-bottom:4px}.create-option[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.8rem;color:#666}.btn-close-create[_ngcontent-%COMP%]{width:100%;padding:12px;background:#f8f9fa;border:1px solid #ddd;border-radius:6px;color:#666;cursor:pointer;font-weight:500}.btn-close-create[_ngcontent-%COMP%]:hover{background:#e9ecef}.fab-create[_ngcontent-%COMP%]{position:fixed;bottom:30px;right:30px;width:60px;height:60px;border-radius:50%;background:#007bff;color:#fff;border:none;font-size:1.5rem;cursor:pointer;box-shadow:0 4px 12px #007bff4d;transition:all .3s ease;z-index:1000}.fab-create[_ngcontent-%COMP%]:hover{background:#0056b3;transform:scale(1.1);box-shadow:0 6px 20px #007bff66}@media (max-width: 768px){.header-content[_ngcontent-%COMP%]{padding:8px 16px;gap:12px}.platform-logo[_ngcontent-%COMP%]{font-size:1.3rem}.search-section[_ngcontent-%COMP%]{max-width:none;flex:1}.nav-actions[_ngcontent-%COMP%]{display:none}.mobile-nav[_ngcontent-%COMP%]{display:flex}.platform-content[_ngcontent-%COMP%]{padding-bottom:70px}.fab-create[_ngcontent-%COMP%]{display:none}}@media (max-width: 480px){.header-content[_ngcontent-%COMP%]{gap:8px}.platform-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-size:.8rem;padding:8px 10px 8px 36px}}\"]\n      });\n    }\n  }\n  return SocialMediaComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}