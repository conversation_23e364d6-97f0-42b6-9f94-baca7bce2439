{"ast": null, "code": "export const COMPLETE_NOTIFICATION = (() => createNotification('C', undefined, undefined))();\nexport function errorNotification(error) {\n  return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n  return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n  return {\n    kind,\n    value,\n    error\n  };\n}\n//# sourceMappingURL=NotificationFactories.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}