{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';\nimport { ErrorDisplayComponent } from '../../../../shared/components/error-display/error-display.component';\nimport { FeaturedBrandsComponent } from '../../../../shared/components/featured-brands/featured-brands.component';\nimport { TrendingNowComponent } from '../../../../shared/components/trending-now/trending-now.component';\nimport { NewArrivalsComponent } from '../../../../shared/components/new-arrivals/new-arrivals.component';\nimport { QuickLinksComponent } from '../../../../shared/components/quick-links/quick-links.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"../../../../core/services/shop-data.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common/http\";\nimport * as i8 from \"../../../../core/services/error-handler.service\";\nimport * as i9 from \"../../../../core/services/loading.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nconst _c0 = () => [\"Men's Clothing\", \"Women's Fashion\", \"Footwear\", \"Accessories\"];\nfunction ShopComponent_app_loading_spinner_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-loading-spinner\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showGlobalLoading\", true);\n  }\n}\nfunction ShopComponent_div_3_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.searchQuery = \"\";\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.filterType = \"trending\";\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelement(2, \"i\", 26);\n    i0.ɵɵtext(3, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_17_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.filterType = \"new\";\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelement(5, \"i\", 27);\n    i0.ɵɵtext(6, \" New Arrivals \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_17_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.filterType = \"sale\";\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵtext(9, \" On Sale \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopComponent_div_3_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 32);\n    i0.ɵɵtext(5, \"Products\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 30)(7, \"span\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 32);\n    i0.ɵɵtext(10, \"Brands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 30)(12, \"span\", 31);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 32);\n    i0.ɵɵtext(15, \"Happy Customers\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.shopStats.totalProducts), \"+\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.shopStats.totalBrands), \"+\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.shopStats.totalUsers), \"+\");\n  }\n}\nfunction ShopComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"app-quick-links\", 34)(2, \"app-featured-brands\", 35)(3, \"app-trending-now\", 36)(4, \"app-new-arrivals\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showHeader\", true)(\"maxLinks\", 8)(\"showPopularCategories\", true)(\"showQuickActions\", true)(\"showFeaturedCollections\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"maxBrands\", 6)(\"showHeader\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"maxProducts\", 12)(\"showHeader\", true)(\"showScrollButtons\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"maxProducts\", 8)(\"showHeader\", true)(\"showLoadMore\", true);\n  }\n}\nfunction ShopComponent_div_3_div_20_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Search Results for \\\"\", ctx_r1.searchQuery, \"\\\"\");\n  }\n}\nfunction ShopComponent_div_3_div_20_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, ctx_r1.selectedCategory), \" Products\");\n  }\n}\nfunction ShopComponent_div_3_div_20_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, ctx_r1.filterType), \" Products\");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r7), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_12_div_1_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.navigateToProduct(product_r7._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 50);\n    i0.ɵɵelement(2, \"img\", 51);\n    i0.ɵɵtemplate(3, ShopComponent_div_3_div_20_div_12_div_1_div_3_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 53)(5, \"h3\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 55);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 56)(10, \"span\", 57);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ShopComponent_div_3_div_20_div_12_div_1_span_12_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r7), i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r7) > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice && product_r7.originalPrice > product_r7.price);\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, ShopComponent_div_3_div_20_div_12_div_1_Template, 13, 7, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredProducts);\n  }\n}\nfunction ShopComponent_div_3_div_20_app_no_data_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 61);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showActions\", true)(\"suggestions\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction ShopComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"h2\", 40);\n    i0.ɵɵtemplate(3, ShopComponent_div_3_div_20_span_3_Template, 2, 1, \"span\", 41)(4, ShopComponent_div_3_div_20_span_4_Template, 3, 3, \"span\", 41)(5, ShopComponent_div_3_div_20_span_5_Template, 3, 3, \"span\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 42)(7, \"span\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearFilters());\n    });\n    i0.ɵɵelement(10, \"i\", 23);\n    i0.ɵɵtext(11, \" Clear Filters \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, ShopComponent_div_3_div_20_div_12_Template, 2, 1, \"div\", 45)(13, ShopComponent_div_3_div_20_app_no_data_13_Template, 1, 3, \"app-no-data\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchQuery && ctx_r1.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchQuery && !ctx_r1.selectedCategory && ctx_r1.filterType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.filteredProducts.length, \" products found\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredProducts.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredProducts.length === 0 && !ctx_r1.isLoading);\n  }\n}\nfunction ShopComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Discover Fashion That Defines You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 9);\n    i0.ɵɵtext(6, \"Explore thousands of products from top brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"div\", 11)(9, \"div\", 12);\n    i0.ɵɵelement(10, \"i\", 13);\n    i0.ɵɵelementStart(11, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ShopComponent_div_3_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function ShopComponent_div_3_Template_input_keyup_enter_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ShopComponent_div_3_button_12_Template, 2, 0, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelement(14, \"i\", 17);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Search\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, ShopComponent_div_3_div_17_Template, 10, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ShopComponent_div_3_div_18_Template, 16, 3, \"div\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ShopComponent_div_3_div_19_Template, 5, 13, \"div\", 20)(20, ShopComponent_div_3_div_20_Template, 14, 6, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shopStats);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showMainSections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showMainSections);\n  }\n}\nexport class ShopComponent {\n  constructor(productService, authService, cartService, wishlistService, shopDataService, router, route, http, errorHandlerService, loadingService) {\n    this.productService = productService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.shopDataService = shopDataService;\n    this.router = router;\n    this.route = route;\n    this.http = http;\n    this.errorHandlerService = errorHandlerService;\n    this.loadingService = loadingService;\n    // Main shop data\n    this.searchQuery = '';\n    this.isLoading = true;\n    this.showMainSections = true;\n    this.shopStats = null;\n    // Enhanced filtering and search\n    this.selectedCategory = '';\n    this.selectedSubcategory = '';\n    this.selectedBrand = '';\n    this.filterType = ''; // 'suggested', 'trending', etc.\n    this.sortBy = 'featured';\n    this.priceRange = {\n      min: 0,\n      max: 10000\n    };\n    // Pagination for filtered results\n    this.currentPage = 1;\n    this.itemsPerPage = 24;\n    this.hasMore = false;\n    // Filtered products (when searching/filtering)\n    this.allProducts = [];\n    this.filteredProducts = [];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    // Check for query parameters\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.searchQuery = params['q'] || '';\n      this.selectedCategory = params['category'] || '';\n      this.filterType = params['filter'] || '';\n      this.sortBy = params['sort'] || 'featured';\n      this.loadShopData();\n    });\n    // Load shop statistics\n    this.loadShopStats();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadShopData() {\n    this.isLoading = true;\n    // If we have filters or search, load filtered products and hide main sections\n    if (this.searchQuery || this.selectedCategory || this.filterType) {\n      this.showMainSections = false;\n      this.loadProductsWithFilters();\n    } else {\n      // Show main sections and load all shop data\n      this.showMainSections = true;\n      this.shopDataService.loadAllShopData().pipe(takeUntil(this.destroy$)).subscribe({\n        next: data => {\n          console.log('✅ All shop data loaded successfully');\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('❌ Error loading shop data:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  loadShopStats() {\n    this.shopDataService.loadShopStats().pipe(takeUntil(this.destroy$)).subscribe({\n      next: stats => {\n        this.shopStats = stats;\n      },\n      error: error => {\n        console.error('Error loading shop stats:', error);\n      }\n    });\n  }\n  // Search and navigation methods\n  search() {\n    if (this.searchQuery.trim()) {\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n  }\n  navigateToCategory(categorySlug) {\n    this.router.navigate(['/shop/category', categorySlug]);\n  }\n  navigateToBrand(brandSlug) {\n    this.router.navigate(['/shop/brand', brandSlug]);\n  }\n  navigateToProduct(productId) {\n    this.router.navigate(['/product', productId]);\n  }\n  // Enhanced filtering methods\n  onCategoryChange() {\n    this.selectedSubcategory = '';\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  onFilterChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  onSortChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedCategory = '';\n    this.selectedSubcategory = '';\n    this.selectedBrand = '';\n    this.filterType = '';\n    this.sortBy = 'featured';\n    this.priceRange = {\n      min: 0,\n      max: 10000\n    };\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  updateUrlParams() {\n    const queryParams = {};\n    if (this.searchQuery) queryParams.q = this.searchQuery;\n    if (this.selectedCategory) queryParams.category = this.selectedCategory;\n    if (this.filterType) queryParams.filter = this.filterType;\n    if (this.sortBy !== 'featured') queryParams.sort = this.sortBy;\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams,\n      queryParamsHandling: 'merge'\n    });\n  }\n  // Enhanced product loading with filtering\n  loadProductsWithFilters() {\n    this.isLoading = true;\n    const filters = {\n      category: this.selectedCategory,\n      subcategory: this.selectedSubcategory,\n      brand: this.selectedBrand,\n      sortBy: this.sortBy,\n      page: this.currentPage,\n      limit: this.itemsPerPage\n    };\n    this.shopDataService.searchProducts(this.searchQuery, filters).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.allProducts = response.products || [];\n        this.filteredProducts = this.allProducts;\n        this.hasMore = response.totalCount > this.currentPage * this.itemsPerPage;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading filtered products:', error);\n        this.allProducts = [];\n        this.filteredProducts = [];\n        this.isLoading = false;\n      }\n    });\n  }\n  // Utility methods\n  getProductImage(product) {\n    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n  }\n  getDiscountPercentage(product) {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.ShopDataService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.HttpClient), i0.ɵɵdirectiveInject(i8.ErrorHandlerService), i0.ɵɵdirectiveInject(i9.LoadingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"shop-container\"], [3, \"showGlobalErrors\"], [\"message\", \"Loading shop data...\", \"webSpinnerType\", \"pulse\", 3, \"showGlobalLoading\", 4, \"ngIf\"], [\"class\", \"shop-content\", 4, \"ngIf\"], [\"message\", \"Loading shop data...\", \"webSpinnerType\", \"pulse\", 3, \"showGlobalLoading\"], [1, \"shop-content\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"search-container\"], [1, \"search-bar\"], [1, \"search-input-wrapper\"], [1, \"fas\", \"fa-search\", \"search-icon\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, categories...\", 1, \"search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"class\", \"clear-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"quick-filters\", 4, \"ngIf\"], [\"class\", \"shop-stats\", 4, \"ngIf\"], [\"class\", \"main-sections\", 4, \"ngIf\"], [\"class\", \"filtered-results\", 4, \"ngIf\"], [1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"quick-filters\"], [1, \"filter-chip\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [1, \"fas\", \"fa-sparkles\"], [1, \"fas\", \"fa-tags\"], [1, \"shop-stats\"], [1, \"stat-item\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"main-sections\"], [3, \"showHeader\", \"maxLinks\", \"showPopularCategories\", \"showQuickActions\", \"showFeaturedCollections\"], [3, \"maxBrands\", \"showHeader\"], [3, \"maxProducts\", \"showHeader\", \"showScrollButtons\"], [3, \"maxProducts\", \"showHeader\", \"showLoadMore\"], [1, \"filtered-results\"], [1, \"results-header\"], [1, \"results-title\"], [4, \"ngIf\"], [1, \"results-meta\"], [1, \"results-count\"], [1, \"clear-filters-btn\", 3, \"click\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"title\", \"No Products Found\", \"message\", \"Try adjusting your search or filters to find what you're looking for.\", \"iconClass\", \"fas fa-search\", \"containerClass\", \"compact\", \"primaryAction\", \"Clear Filters\", \"secondaryAction\", \"Browse All Products\", \"suggestionsTitle\", \"Popular Categories:\", 3, \"showActions\", \"suggestions\", 4, \"ngIf\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [1, \"product-image\", 3, \"src\", \"alt\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"discount-badge\"], [1, \"original-price\"], [\"title\", \"No Products Found\", \"message\", \"Try adjusting your search or filters to find what you're looking for.\", \"iconClass\", \"fas fa-search\", \"containerClass\", \"compact\", \"primaryAction\", \"Clear Filters\", \"secondaryAction\", \"Browse All Products\", \"suggestionsTitle\", \"Popular Categories:\", 3, \"showActions\", \"suggestions\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-error-display\", 1);\n          i0.ɵɵtemplate(2, ShopComponent_app_loading_spinner_2_Template, 1, 1, \"app-loading-spinner\", 2)(3, ShopComponent_div_3_Template, 21, 6, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"showGlobalErrors\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i10.NgForOf, i10.NgIf, i10.TitleCasePipe, FormsModule, i11.DefaultValueAccessor, i11.NgControlStatus, i11.NgModel, NoDataComponent, LoadingSpinnerComponent, ErrorDisplayComponent, FeaturedBrandsComponent, TrendingNowComponent, NewArrivalsComponent, QuickLinksComponent],\n      styles: [\".shop-container[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0;\\n  background: #f8f9fa;\\n  min-height: 100vh;\\n}\\n\\n.shop-content[_ngcontent-%COMP%] {\\n  padding-bottom: 2rem;\\n}\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 4rem 2rem;\\n  text-align: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 800;\\n  margin: 0 0 1rem 0;\\n  line-height: 1.2;\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  margin: 0 0 2rem 0;\\n  opacity: 0.9;\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  max-width: 700px;\\n  margin: 0 auto 1.5rem auto;\\n}\\n@media (max-width: 768px) {\\n  .search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 25px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #8e8e8e;\\n  font-size: 1rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem 1rem 1rem 3rem;\\n  border: none;\\n  outline: none;\\n  background: transparent;\\n  font-size: 1rem;\\n  color: #262626;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder {\\n  color: #8e8e8e;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: none;\\n  border: none;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n  padding: 1rem 2rem;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 25px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n@media (max-width: 768px) {\\n  .search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n\\n.shop-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 3rem;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .shop-stats[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .shop-stats[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n}\\n.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 2rem;\\n  font-weight: 800;\\n  line-height: 1;\\n  margin-bottom: 0.25rem;\\n}\\n@media (max-width: 768px) {\\n  .shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n@media (max-width: 768px) {\\n  .shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n\\n.main-sections[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n}\\n\\n.filtered-results[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  background: white;\\n  margin: 2rem;\\n  border-radius: 16px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n@media (max-width: 768px) {\\n  .filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: flex-start;\\n  }\\n}\\n.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #262626;\\n  margin: 0;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%]   .results-count[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.9rem;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  background: #f8f9fa;\\n  color: #666;\\n  border: 1px solid #dee2e6;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-size: 0.85rem;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 1rem;\\n  }\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 250px;\\n  overflow: hidden;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #e91e63;\\n  color: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 0.25rem 0;\\n  line-height: 1.3;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.85rem;\\n  margin: 0 0 0.5rem 0;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 700;\\n  color: #262626;\\n}\\n.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #8e8e8e;\\n  text-decoration: line-through;\\n  margin-left: 0.5rem;\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  .shop-container[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .hero-section[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\\n  }\\n  .filtered-results[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n  }\\n  .filtered-results[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .filtered-results[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n  }\\n  .filtered-results[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .filtered-results[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n    color: #ffffff;\\n    border-color: #333;\\n  }\\n  .filtered-results[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover {\\n    background: #333;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "Subject", "takeUntil", "NoDataComponent", "LoadingSpinnerComponent", "ErrorDisplayComponent", "FeaturedBrandsComponent", "TrendingNowComponent", "NewArrivalsComponent", "QuickLinksComponent", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵelementStart", "ɵɵlistener", "ShopComponent_div_3_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "searchQuery", "ɵɵresetView", "search", "ɵɵelementEnd", "ShopComponent_div_3_div_17_Template_button_click_1_listener", "_r4", "filterType", "onFilterChange", "ɵɵtext", "ShopComponent_div_3_div_17_Template_button_click_4_listener", "ShopComponent_div_3_div_17_Template_button_click_7_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "formatNumber", "shopStats", "totalProducts", "totalBrands", "totalUsers", "ɵɵpipeBind1", "selectedCate<PERSON><PERSON>", "getDiscountPercentage", "product_r7", "originalPrice", "ShopComponent_div_3_div_20_div_12_div_1_Template_div_click_0_listener", "_r6", "$implicit", "navigateToProduct", "_id", "ɵɵtemplate", "ShopComponent_div_3_div_20_div_12_div_1_div_3_Template", "ShopComponent_div_3_div_20_div_12_div_1_span_12_Template", "getProductImage", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "brand", "price", "ShopComponent_div_3_div_20_div_12_div_1_Template", "filteredProducts", "ɵɵpureFunction0", "_c0", "ShopComponent_div_3_div_20_span_3_Template", "ShopComponent_div_3_div_20_span_4_Template", "ShopComponent_div_3_div_20_span_5_Template", "ShopComponent_div_3_div_20_Template_button_click_9_listener", "_r5", "clearFilters", "ShopComponent_div_3_div_20_div_12_Template", "ShopComponent_div_3_div_20_app_no_data_13_Template", "length", "isLoading", "ɵɵtwoWayListener", "ShopComponent_div_3_Template_input_ngModelChange_11_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "ShopComponent_div_3_Template_input_keyup_enter_11_listener", "ShopComponent_div_3_button_12_Template", "ShopComponent_div_3_Template_button_click_13_listener", "ShopComponent_div_3_div_17_Template", "ShopComponent_div_3_div_18_Template", "ShopComponent_div_3_div_19_Template", "ShopComponent_div_3_div_20_Template", "ɵɵtwoWayProperty", "showMainSections", "ShopComponent", "constructor", "productService", "authService", "cartService", "wishlistService", "shopDataService", "router", "route", "http", "errorHandlerService", "loadingService", "selectedSubcategory", "<PERSON><PERSON><PERSON>", "sortBy", "priceRange", "min", "max", "currentPage", "itemsPerPage", "hasMore", "allProducts", "destroy$", "ngOnInit", "queryParams", "pipe", "subscribe", "params", "loadShopData", "loadShopStats", "ngOnDestroy", "next", "complete", "loadProductsWithFilters", "loadAllShopData", "data", "console", "log", "error", "stats", "trim", "updateUrlParams", "navigateToCategory", "categorySlug", "navigate", "navigate<PERSON><PERSON>Brand", "brandSlug", "productId", "onCategoryChange", "onSortChange", "q", "category", "filter", "sort", "relativeTo", "queryParamsHandling", "filters", "subcategory", "page", "limit", "searchProducts", "response", "products", "totalCount", "product", "images", "url", "Math", "round", "num", "toFixed", "toString", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "AuthService", "i3", "CartService", "i4", "WishlistService", "i5", "ShopDataService", "i6", "Router", "ActivatedRoute", "i7", "HttpClient", "i8", "ErrorHandlerService", "i9", "LoadingService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "ShopComponent_app_loading_spinner_2_Template", "ShopComponent_div_3_Template", "i10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "TitleCasePipe", "i11", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\shop\\shop.component.ts", "E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\shop\\shop.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClient } from '@angular/common/http';\nimport { Subject, takeUntil } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { ShopDataService } from '../../../../core/services/shop-data.service';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';\nimport { ErrorDisplayComponent } from '../../../../shared/components/error-display/error-display.component';\nimport { FeaturedBrandsComponent } from '../../../../shared/components/featured-brands/featured-brands.component';\nimport { TrendingNowComponent } from '../../../../shared/components/trending-now/trending-now.component';\nimport { NewArrivalsComponent } from '../../../../shared/components/new-arrivals/new-arrivals.component';\nimport { QuickLinksComponent } from '../../../../shared/components/quick-links/quick-links.component';\nimport { ErrorHandlerService } from '../../../../core/services/error-handler.service';\nimport { LoadingService } from '../../../../core/services/loading.service';\n\n@Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NoDataComponent,\n    LoadingSpinnerComponent,\n    ErrorDisplayComponent,\n    FeaturedBrandsComponent,\n    TrendingNowComponent,\n    NewArrivalsComponent,\n    QuickLinksComponent\n  ],\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.scss']\n})\nexport class ShopComponent implements OnInit, OnDestroy {\n  // Main shop data\n  searchQuery: string = '';\n  isLoading = true;\n  showMainSections = true;\n  shopStats: any = null;\n\n  // Enhanced filtering and search\n  selectedCategory = '';\n  selectedSubcategory = '';\n  selectedBrand = '';\n  filterType = ''; // 'suggested', 'trending', etc.\n  sortBy = 'featured';\n  priceRange = { min: 0, max: 10000 };\n\n  // Pagination for filtered results\n  currentPage = 1;\n  itemsPerPage = 24;\n  hasMore = false;\n\n  // Filtered products (when searching/filtering)\n  allProducts: any[] = [];\n  filteredProducts: any[] = [];\n\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private productService: ProductService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private shopDataService: ShopDataService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private http: HttpClient,\n    private errorHandlerService: ErrorHandlerService,\n    private loadingService: LoadingService\n  ) {}\n\n  ngOnInit() {\n    // Check for query parameters\n    this.route.queryParams\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(params => {\n        this.searchQuery = params['q'] || '';\n        this.selectedCategory = params['category'] || '';\n        this.filterType = params['filter'] || '';\n        this.sortBy = params['sort'] || 'featured';\n\n        this.loadShopData();\n      });\n\n    // Load shop statistics\n    this.loadShopStats();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadShopData() {\n    this.isLoading = true;\n\n    // If we have filters or search, load filtered products and hide main sections\n    if (this.searchQuery || this.selectedCategory || this.filterType) {\n      this.showMainSections = false;\n      this.loadProductsWithFilters();\n    } else {\n      // Show main sections and load all shop data\n      this.showMainSections = true;\n      this.shopDataService.loadAllShopData()\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (data) => {\n            console.log('✅ All shop data loaded successfully');\n            this.isLoading = false;\n          },\n          error: (error) => {\n            console.error('❌ Error loading shop data:', error);\n            this.isLoading = false;\n          }\n        });\n    }\n  }\n\n  loadShopStats() {\n    this.shopDataService.loadShopStats()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (stats) => {\n          this.shopStats = stats;\n        },\n        error: (error) => {\n          console.error('Error loading shop stats:', error);\n        }\n      });\n  }\n\n  // Search and navigation methods\n  search() {\n    if (this.searchQuery.trim()) {\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n  }\n\n  navigateToCategory(categorySlug: string) {\n    this.router.navigate(['/shop/category', categorySlug]);\n  }\n\n  navigateToBrand(brandSlug: string) {\n    this.router.navigate(['/shop/brand', brandSlug]);\n  }\n\n  navigateToProduct(productId: string) {\n    this.router.navigate(['/product', productId]);\n  }\n\n  // Enhanced filtering methods\n  onCategoryChange() {\n    this.selectedSubcategory = '';\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  onFilterChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  onSortChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedCategory = '';\n    this.selectedSubcategory = '';\n    this.selectedBrand = '';\n    this.filterType = '';\n    this.sortBy = 'featured';\n    this.priceRange = { min: 0, max: 10000 };\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  private updateUrlParams() {\n    const queryParams: any = {};\n\n    if (this.searchQuery) queryParams.q = this.searchQuery;\n    if (this.selectedCategory) queryParams.category = this.selectedCategory;\n    if (this.filterType) queryParams.filter = this.filterType;\n    if (this.sortBy !== 'featured') queryParams.sort = this.sortBy;\n\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams,\n      queryParamsHandling: 'merge'\n    });\n  }\n\n  // Enhanced product loading with filtering\n  loadProductsWithFilters() {\n    this.isLoading = true;\n\n    const filters = {\n      category: this.selectedCategory,\n      subcategory: this.selectedSubcategory,\n      brand: this.selectedBrand,\n      sortBy: this.sortBy,\n      page: this.currentPage,\n      limit: this.itemsPerPage\n    };\n\n    this.shopDataService.searchProducts(this.searchQuery, filters)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          this.allProducts = response.products || [];\n          this.filteredProducts = this.allProducts;\n          this.hasMore = response.totalCount > (this.currentPage * this.itemsPerPage);\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading filtered products:', error);\n          this.allProducts = [];\n          this.filteredProducts = [];\n          this.isLoading = false;\n        }\n      });\n  }\n\n  // Utility methods\n  getProductImage(product: any): string {\n    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n  }\n\n  getDiscountPercentage(product: any): number {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n}\n", "<div class=\"shop-container\">\n  <!-- Global Error Display -->\n  <app-error-display [showGlobalErrors]=\"true\"></app-error-display>\n\n  <!-- Loading State -->\n  <app-loading-spinner\n    *ngIf=\"isLoading\"\n    [showGlobalLoading]=\"true\"\n    message=\"Loading shop data...\"\n    webSpinnerType=\"pulse\">\n  </app-loading-spinner>\n\n  <!-- Shop Content -->\n  <div *ngIf=\"!isLoading\" class=\"shop-content\">\n    <!-- Hero Section with Search -->\n    <div class=\"hero-section\">\n      <div class=\"hero-content\">\n        <h1 class=\"hero-title\">Discover Fashion That Defines You</h1>\n        <p class=\"hero-subtitle\">Explore thousands of products from top brands</p>\n\n        <!-- Enhanced Search Bar -->\n        <div class=\"search-container\">\n          <div class=\"search-bar\">\n            <div class=\"search-input-wrapper\">\n              <i class=\"fas fa-search search-icon\"></i>\n              <input\n                type=\"text\"\n                [(ngModel)]=\"searchQuery\"\n                placeholder=\"Search products, brands, categories...\"\n                (keyup.enter)=\"search()\"\n                class=\"search-input\">\n              <button *ngIf=\"searchQuery\" (click)=\"searchQuery = ''; search()\" class=\"clear-btn\">\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n            <button (click)=\"search()\" class=\"search-btn\">\n              <i class=\"fas fa-search\"></i>\n              <span>Search</span>\n            </button>\n          </div>\n\n          <!-- Quick Filters -->\n          <div class=\"quick-filters\" *ngIf=\"!searchQuery\">\n            <button class=\"filter-chip\" (click)=\"filterType = 'trending'; onFilterChange()\">\n              <i class=\"fas fa-fire\"></i>\n              Trending\n            </button>\n            <button class=\"filter-chip\" (click)=\"filterType = 'new'; onFilterChange()\">\n              <i class=\"fas fa-sparkles\"></i>\n              New Arrivals\n            </button>\n            <button class=\"filter-chip\" (click)=\"filterType = 'sale'; onFilterChange()\">\n              <i class=\"fas fa-tags\"></i>\n              On Sale\n            </button>\n          </div>\n        </div>\n\n        <!-- Shop Stats -->\n        <div class=\"shop-stats\" *ngIf=\"shopStats\">\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">{{ formatNumber(shopStats.totalProducts) }}+</span>\n            <span class=\"stat-label\">Products</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">{{ formatNumber(shopStats.totalBrands) }}+</span>\n            <span class=\"stat-label\">Brands</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">{{ formatNumber(shopStats.totalUsers) }}+</span>\n            <span class=\"stat-label\">Happy Customers</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Shop Sections (when not filtering) -->\n    <div *ngIf=\"showMainSections\" class=\"main-sections\">\n      <!-- Quick Links Section -->\n      <app-quick-links\n        [showHeader]=\"true\"\n        [maxLinks]=\"8\"\n        [showPopularCategories]=\"true\"\n        [showQuickActions]=\"true\"\n        [showFeaturedCollections]=\"true\">\n      </app-quick-links>\n\n      <!-- Featured Brands Section -->\n      <app-featured-brands\n        [maxBrands]=\"6\"\n        [showHeader]=\"true\">\n      </app-featured-brands>\n\n      <!-- Trending Now Section -->\n      <app-trending-now\n        [maxProducts]=\"12\"\n        [showHeader]=\"true\"\n        [showScrollButtons]=\"true\">\n      </app-trending-now>\n\n      <!-- New Arrivals Section -->\n      <app-new-arrivals\n        [maxProducts]=\"8\"\n        [showHeader]=\"true\"\n        [showLoadMore]=\"true\">\n      </app-new-arrivals>\n    </div>\n\n    <!-- Filtered Results Section (when searching/filtering) -->\n    <div *ngIf=\"!showMainSections\" class=\"filtered-results\">\n      <div class=\"results-header\">\n        <h2 class=\"results-title\">\n          <span *ngIf=\"searchQuery\">Search Results for \"{{ searchQuery }}\"</span>\n          <span *ngIf=\"!searchQuery && selectedCategory\">{{ selectedCategory | titlecase }} Products</span>\n          <span *ngIf=\"!searchQuery && !selectedCategory && filterType\">{{ filterType | titlecase }} Products</span>\n        </h2>\n        <div class=\"results-meta\">\n          <span class=\"results-count\">{{ filteredProducts.length }} products found</span>\n          <button class=\"clear-filters-btn\" (click)=\"clearFilters()\">\n            <i class=\"fas fa-times\"></i>\n            Clear Filters\n          </button>\n        </div>\n      </div>\n\n      <!-- Filtered Products Grid -->\n      <div *ngIf=\"filteredProducts.length > 0\" class=\"products-grid\">\n        <div *ngFor=\"let product of filteredProducts\" class=\"product-card\" (click)=\"navigateToProduct(product._id)\">\n          <div class=\"product-image-container\">\n            <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" class=\"product-image\">\n            <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n              {{ getDiscountPercentage(product) }}% OFF\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h3 class=\"product-name\">{{ product.name }}</h3>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-pricing\">\n              <span class=\"current-price\">₹{{ product.price }}</span>\n              <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\"\n                    class=\"original-price\">₹{{ product.originalPrice }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- No Results State -->\n      <app-no-data\n        *ngIf=\"filteredProducts.length === 0 && !isLoading\"\n        title=\"No Products Found\"\n        message=\"Try adjusting your search or filters to find what you're looking for.\"\n        iconClass=\"fas fa-search\"\n        containerClass=\"compact\"\n        [showActions]=\"true\"\n        primaryAction=\"Clear Filters\"\n        secondaryAction=\"Browse All Products\"\n        [suggestions]=\"['Men\\'s Clothing', 'Women\\'s Fashion', 'Footwear', 'Accessories']\"\n        suggestionsTitle=\"Popular Categories:\">\n      </app-no-data>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAOzC,SAASC,eAAe,QAAQ,yDAAyD;AACzF,SAASC,uBAAuB,QAAQ,yEAAyE;AACjH,SAASC,qBAAqB,QAAQ,qEAAqE;AAC3G,SAASC,uBAAuB,QAAQ,yEAAyE;AACjH,SAASC,oBAAoB,QAAQ,mEAAmE;AACxG,SAASC,oBAAoB,QAAQ,mEAAmE;AACxG,SAASC,mBAAmB,QAAQ,iEAAiE;;;;;;;;;;;;;;;;ICbnGC,EAAA,CAAAC,SAAA,6BAKsB;;;IAHpBD,EAAA,CAAAE,UAAA,2BAA0B;;;;;;IAwBhBF,EAAA,CAAAG,cAAA,iBAAmF;IAAvDH,EAAA,CAAAI,UAAA,mBAAAC,+DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAD,MAAA,CAAAE,WAAA,GAAuB,EAAE;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAEH,MAAA,CAAAI,MAAA,EAAQ;IAAA,EAAC;IAC9DZ,EAAA,CAAAC,SAAA,YAA4B;IAC9BD,EAAA,CAAAa,YAAA,EAAS;;;;;;IAUXb,EADF,CAAAG,cAAA,cAAgD,iBACkC;IAApDH,EAAA,CAAAI,UAAA,mBAAAU,4DAAA;MAAAd,EAAA,CAAAM,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAD,MAAA,CAAAQ,UAAA,GAAsB,UAAU;MAAA,OAAAhB,EAAA,CAAAW,WAAA,CAAEH,MAAA,CAAAS,cAAA,EAAgB;IAAA,EAAC;IAC7EjB,EAAA,CAAAC,SAAA,YAA2B;IAC3BD,EAAA,CAAAkB,MAAA,iBACF;IAAAlB,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAG,cAAA,iBAA2E;IAA/CH,EAAA,CAAAI,UAAA,mBAAAe,4DAAA;MAAAnB,EAAA,CAAAM,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAD,MAAA,CAAAQ,UAAA,GAAsB,KAAK;MAAA,OAAAhB,EAAA,CAAAW,WAAA,CAAEH,MAAA,CAAAS,cAAA,EAAgB;IAAA,EAAC;IACxEjB,EAAA,CAAAC,SAAA,YAA+B;IAC/BD,EAAA,CAAAkB,MAAA,qBACF;IAAAlB,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAG,cAAA,iBAA4E;IAAhDH,EAAA,CAAAI,UAAA,mBAAAgB,4DAAA;MAAApB,EAAA,CAAAM,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAD,MAAA,CAAAQ,UAAA,GAAsB,MAAM;MAAA,OAAAhB,EAAA,CAAAW,WAAA,CAAEH,MAAA,CAAAS,cAAA,EAAgB;IAAA,EAAC;IACzEjB,EAAA,CAAAC,SAAA,YAA2B;IAC3BD,EAAA,CAAAkB,MAAA,gBACF;IACFlB,EADE,CAAAa,YAAA,EAAS,EACL;;;;;IAMJb,EAFJ,CAAAG,cAAA,cAA0C,cACjB,eACK;IAAAH,EAAA,CAAAkB,MAAA,GAA4C;IAAAlB,EAAA,CAAAa,YAAA,EAAO;IAC7Eb,EAAA,CAAAG,cAAA,eAAyB;IAAAH,EAAA,CAAAkB,MAAA,eAAQ;IACnClB,EADmC,CAAAa,YAAA,EAAO,EACpC;IAEJb,EADF,CAAAG,cAAA,cAAuB,eACK;IAAAH,EAAA,CAAAkB,MAAA,GAA0C;IAAAlB,EAAA,CAAAa,YAAA,EAAO;IAC3Eb,EAAA,CAAAG,cAAA,eAAyB;IAAAH,EAAA,CAAAkB,MAAA,cAAM;IACjClB,EADiC,CAAAa,YAAA,EAAO,EAClC;IAEJb,EADF,CAAAG,cAAA,eAAuB,gBACK;IAAAH,EAAA,CAAAkB,MAAA,IAAyC;IAAAlB,EAAA,CAAAa,YAAA,EAAO;IAC1Eb,EAAA,CAAAG,cAAA,gBAAyB;IAAAH,EAAA,CAAAkB,MAAA,uBAAe;IAE5ClB,EAF4C,CAAAa,YAAA,EAAO,EAC3C,EACF;;;;IAXwBb,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAsB,kBAAA,KAAAd,MAAA,CAAAe,YAAA,CAAAf,MAAA,CAAAgB,SAAA,CAAAC,aAAA,OAA4C;IAI5CzB,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAAsB,kBAAA,KAAAd,MAAA,CAAAe,YAAA,CAAAf,MAAA,CAAAgB,SAAA,CAAAE,WAAA,OAA0C;IAI1C1B,EAAA,CAAAqB,SAAA,GAAyC;IAAzCrB,EAAA,CAAAsB,kBAAA,KAAAd,MAAA,CAAAe,YAAA,CAAAf,MAAA,CAAAgB,SAAA,CAAAG,UAAA,OAAyC;;;;;IAQ3E3B,EAAA,CAAAG,cAAA,cAAoD;IAwBlDH,EAtBA,CAAAC,SAAA,0BAMkB,8BAMI,2BAOH,2BAOA;IACrBD,EAAA,CAAAa,YAAA,EAAM;;;IA1BFb,EAAA,CAAAqB,SAAA,EAAmB;IAInBrB,EAJA,CAAAE,UAAA,oBAAmB,eACL,+BACgB,0BACL,iCACO;IAKhCF,EAAA,CAAAqB,SAAA,EAAe;IACfrB,EADA,CAAAE,UAAA,gBAAe,oBACI;IAKnBF,EAAA,CAAAqB,SAAA,EAAkB;IAElBrB,EAFA,CAAAE,UAAA,mBAAkB,oBACC,2BACO;IAK1BF,EAAA,CAAAqB,SAAA,EAAiB;IAEjBrB,EAFA,CAAAE,UAAA,kBAAiB,oBACE,sBACE;;;;;IAQnBF,EAAA,CAAAG,cAAA,WAA0B;IAAAH,EAAA,CAAAkB,MAAA,GAAsC;IAAAlB,EAAA,CAAAa,YAAA,EAAO;;;;IAA7Cb,EAAA,CAAAqB,SAAA,EAAsC;IAAtCrB,EAAA,CAAAsB,kBAAA,0BAAAd,MAAA,CAAAE,WAAA,OAAsC;;;;;IAChEV,EAAA,CAAAG,cAAA,WAA+C;IAAAH,EAAA,CAAAkB,MAAA,GAA2C;;IAAAlB,EAAA,CAAAa,YAAA,EAAO;;;;IAAlDb,EAAA,CAAAqB,SAAA,EAA2C;IAA3CrB,EAAA,CAAAsB,kBAAA,KAAAtB,EAAA,CAAA4B,WAAA,OAAApB,MAAA,CAAAqB,gBAAA,eAA2C;;;;;IAC1F7B,EAAA,CAAAG,cAAA,WAA8D;IAAAH,EAAA,CAAAkB,MAAA,GAAqC;;IAAAlB,EAAA,CAAAa,YAAA,EAAO;;;;IAA5Cb,EAAA,CAAAqB,SAAA,EAAqC;IAArCrB,EAAA,CAAAsB,kBAAA,KAAAtB,EAAA,CAAA4B,WAAA,OAAApB,MAAA,CAAAQ,UAAA,eAAqC;;;;;IAgBjGhB,EAAA,CAAAG,cAAA,cAAuE;IACrEH,EAAA,CAAAkB,MAAA,GACF;IAAAlB,EAAA,CAAAa,YAAA,EAAM;;;;;IADJb,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAd,MAAA,CAAAsB,qBAAA,CAAAC,UAAA,YACF;;;;;IAOE/B,EAAA,CAAAG,cAAA,eAC6B;IAAAH,EAAA,CAAAkB,MAAA,GAA4B;IAAAlB,EAAA,CAAAa,YAAA,EAAO;;;;IAAnCb,EAAA,CAAAqB,SAAA,EAA4B;IAA5BrB,EAAA,CAAAsB,kBAAA,WAAAS,UAAA,CAAAC,aAAA,KAA4B;;;;;;IAb/DhC,EAAA,CAAAG,cAAA,cAA4G;IAAzCH,EAAA,CAAAI,UAAA,mBAAA6B,sEAAA;MAAA,MAAAF,UAAA,GAAA/B,EAAA,CAAAM,aAAA,CAAA4B,GAAA,EAAAC,SAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAW,WAAA,CAASH,MAAA,CAAA4B,iBAAA,CAAAL,UAAA,CAAAM,GAAA,CAA8B;IAAA,EAAC;IACzGrC,EAAA,CAAAG,cAAA,cAAqC;IACnCH,EAAA,CAAAC,SAAA,cAAiF;IACjFD,EAAA,CAAAsC,UAAA,IAAAC,sDAAA,kBAAuE;IAGzEvC,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAG,cAAA,cAA0B,aACC;IAAAH,EAAA,CAAAkB,MAAA,GAAkB;IAAAlB,EAAA,CAAAa,YAAA,EAAK;IAChDb,EAAA,CAAAG,cAAA,YAAyB;IAAAH,EAAA,CAAAkB,MAAA,GAAmB;IAAAlB,EAAA,CAAAa,YAAA,EAAI;IAE9Cb,EADF,CAAAG,cAAA,cAA6B,gBACC;IAAAH,EAAA,CAAAkB,MAAA,IAAoB;IAAAlB,EAAA,CAAAa,YAAA,EAAO;IACvDb,EAAA,CAAAsC,UAAA,KAAAE,wDAAA,mBAC6B;IAGnCxC,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;;;;;IAdGb,EAAA,CAAAqB,SAAA,GAAgC;IAACrB,EAAjC,CAAAE,UAAA,QAAAM,MAAA,CAAAiC,eAAA,CAAAV,UAAA,GAAA/B,EAAA,CAAA0C,aAAA,CAAgC,QAAAX,UAAA,CAAAY,IAAA,CAAqB;IACpD3C,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAE,UAAA,SAAAM,MAAA,CAAAsB,qBAAA,CAAAC,UAAA,MAAwC;IAKrB/B,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAA4C,iBAAA,CAAAb,UAAA,CAAAY,IAAA,CAAkB;IAClB3C,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA4C,iBAAA,CAAAb,UAAA,CAAAc,KAAA,CAAmB;IAEd7C,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,kBAAA,WAAAS,UAAA,CAAAe,KAAA,KAAoB;IACzC9C,EAAA,CAAAqB,SAAA,EAAoE;IAApErB,EAAA,CAAAE,UAAA,SAAA6B,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,GAAAD,UAAA,CAAAe,KAAA,CAAoE;;;;;IAbnF9C,EAAA,CAAAG,cAAA,cAA+D;IAC7DH,EAAA,CAAAsC,UAAA,IAAAS,gDAAA,mBAA4G;IAiB9G/C,EAAA,CAAAa,YAAA,EAAM;;;;IAjBqBb,EAAA,CAAAqB,SAAA,EAAmB;IAAnBrB,EAAA,CAAAE,UAAA,YAAAM,MAAA,CAAAwC,gBAAA,CAAmB;;;;;IAoB9ChD,EAAA,CAAAC,SAAA,sBAWc;;;IAFZD,EAHA,CAAAE,UAAA,qBAAoB,gBAAAF,EAAA,CAAAiD,eAAA,IAAAC,GAAA,EAG8D;;;;;;IA7ClFlD,EAFJ,CAAAG,cAAA,cAAwD,cAC1B,aACA;IAGxBH,EAFA,CAAAsC,UAAA,IAAAa,0CAAA,mBAA0B,IAAAC,0CAAA,mBACqB,IAAAC,0CAAA,mBACe;IAChErD,EAAA,CAAAa,YAAA,EAAK;IAEHb,EADF,CAAAG,cAAA,cAA0B,eACI;IAAAH,EAAA,CAAAkB,MAAA,GAA4C;IAAAlB,EAAA,CAAAa,YAAA,EAAO;IAC/Eb,EAAA,CAAAG,cAAA,iBAA2D;IAAzBH,EAAA,CAAAI,UAAA,mBAAAkD,4DAAA;MAAAtD,EAAA,CAAAM,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAW,WAAA,CAASH,MAAA,CAAAgD,YAAA,EAAc;IAAA,EAAC;IACxDxD,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAkB,MAAA,uBACF;IAEJlB,EAFI,CAAAa,YAAA,EAAS,EACL,EACF;IAwBNb,EArBA,CAAAsC,UAAA,KAAAmB,0CAAA,kBAA+D,KAAAC,kDAAA,0BA+BtB;IAE3C1D,EAAA,CAAAa,YAAA,EAAM;;;;IA/COb,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAE,UAAA,SAAAM,MAAA,CAAAE,WAAA,CAAiB;IACjBV,EAAA,CAAAqB,SAAA,EAAsC;IAAtCrB,EAAA,CAAAE,UAAA,UAAAM,MAAA,CAAAE,WAAA,IAAAF,MAAA,CAAAqB,gBAAA,CAAsC;IACtC7B,EAAA,CAAAqB,SAAA,EAAqD;IAArDrB,EAAA,CAAAE,UAAA,UAAAM,MAAA,CAAAE,WAAA,KAAAF,MAAA,CAAAqB,gBAAA,IAAArB,MAAA,CAAAQ,UAAA,CAAqD;IAGhChB,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAsB,kBAAA,KAAAd,MAAA,CAAAwC,gBAAA,CAAAW,MAAA,oBAA4C;IAStE3D,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAE,UAAA,SAAAM,MAAA,CAAAwC,gBAAA,CAAAW,MAAA,KAAiC;IAsBpC3D,EAAA,CAAAqB,SAAA,EAAiD;IAAjDrB,EAAA,CAAAE,UAAA,SAAAM,MAAA,CAAAwC,gBAAA,CAAAW,MAAA,WAAAnD,MAAA,CAAAoD,SAAA,CAAiD;;;;;;IAnIlD5D,EAJN,CAAAG,cAAA,aAA6C,aAEjB,aACE,YACD;IAAAH,EAAA,CAAAkB,MAAA,wCAAiC;IAAAlB,EAAA,CAAAa,YAAA,EAAK;IAC7Db,EAAA,CAAAG,cAAA,WAAyB;IAAAH,EAAA,CAAAkB,MAAA,oDAA6C;IAAAlB,EAAA,CAAAa,YAAA,EAAI;IAKtEb,EAFJ,CAAAG,cAAA,cAA8B,cACJ,cACY;IAChCH,EAAA,CAAAC,SAAA,aAAyC;IACzCD,EAAA,CAAAG,cAAA,iBAKuB;IAHrBH,EAAA,CAAA6D,gBAAA,2BAAAC,6DAAAC,MAAA;MAAA/D,EAAA,CAAAM,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiE,kBAAA,CAAAzD,MAAA,CAAAE,WAAA,EAAAqD,MAAA,MAAAvD,MAAA,CAAAE,WAAA,GAAAqD,MAAA;MAAA,OAAA/D,EAAA,CAAAW,WAAA,CAAAoD,MAAA;IAAA,EAAyB;IAEzB/D,EAAA,CAAAI,UAAA,yBAAA8D,2DAAA;MAAAlE,EAAA,CAAAM,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAW,WAAA,CAAeH,MAAA,CAAAI,MAAA,EAAQ;IAAA,EAAC;IAJ1BZ,EAAA,CAAAa,YAAA,EAKuB;IACvBb,EAAA,CAAAsC,UAAA,KAAA6B,sCAAA,qBAAmF;IAGrFnE,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAG,cAAA,kBAA8C;IAAtCH,EAAA,CAAAI,UAAA,mBAAAgE,sDAAA;MAAApE,EAAA,CAAAM,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAW,WAAA,CAASH,MAAA,CAAAI,MAAA,EAAQ;IAAA,EAAC;IACxBZ,EAAA,CAAAC,SAAA,aAA6B;IAC7BD,EAAA,CAAAG,cAAA,YAAM;IAAAH,EAAA,CAAAkB,MAAA,cAAM;IAEhBlB,EAFgB,CAAAa,YAAA,EAAO,EACZ,EACL;IAGNb,EAAA,CAAAsC,UAAA,KAAA+B,mCAAA,mBAAgD;IAclDrE,EAAA,CAAAa,YAAA,EAAM;IAGNb,EAAA,CAAAsC,UAAA,KAAAgC,mCAAA,mBAA0C;IAe9CtE,EADE,CAAAa,YAAA,EAAM,EACF;IAmCNb,EAhCA,CAAAsC,UAAA,KAAAiC,mCAAA,mBAAoD,KAAAC,mCAAA,mBAgCI;IAmD1DxE,EAAA,CAAAa,YAAA,EAAM;;;;IArIQb,EAAA,CAAAqB,SAAA,IAAyB;IAAzBrB,EAAA,CAAAyE,gBAAA,YAAAjE,MAAA,CAAAE,WAAA,CAAyB;IAIlBV,EAAA,CAAAqB,SAAA,EAAiB;IAAjBrB,EAAA,CAAAE,UAAA,SAAAM,MAAA,CAAAE,WAAA,CAAiB;IAWFV,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAE,UAAA,UAAAM,MAAA,CAAAE,WAAA,CAAkB;IAiBvBV,EAAA,CAAAqB,SAAA,EAAe;IAAfrB,EAAA,CAAAE,UAAA,SAAAM,MAAA,CAAAgB,SAAA,CAAe;IAkBtCxB,EAAA,CAAAqB,SAAA,EAAsB;IAAtBrB,EAAA,CAAAE,UAAA,SAAAM,MAAA,CAAAkE,gBAAA,CAAsB;IAgCtB1E,EAAA,CAAAqB,SAAA,EAAuB;IAAvBrB,EAAA,CAAAE,UAAA,UAAAM,MAAA,CAAAkE,gBAAA,CAAuB;;;ADtEjC,OAAM,MAAOC,aAAa;EA0BxBC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,eAAgC,EAChCC,MAAc,EACdC,KAAqB,EACrBC,IAAgB,EAChBC,mBAAwC,EACxCC,cAA8B;IAT9B,KAAAT,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IAnCxB;IACA,KAAA5E,WAAW,GAAW,EAAE;IACxB,KAAAkD,SAAS,GAAG,IAAI;IAChB,KAAAc,gBAAgB,GAAG,IAAI;IACvB,KAAAlD,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAK,gBAAgB,GAAG,EAAE;IACrB,KAAA0D,mBAAmB,GAAG,EAAE;IACxB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAxE,UAAU,GAAG,EAAE,CAAC,CAAC;IACjB,KAAAyE,MAAM,GAAG,UAAU;IACnB,KAAAC,UAAU,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAK,CAAE;IAEnC;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAhD,gBAAgB,GAAU,EAAE;IAEpB,KAAAiD,QAAQ,GAAG,IAAI1G,OAAO,EAAQ;EAanC;EAEH2G,QAAQA,CAAA;IACN;IACA,IAAI,CAACf,KAAK,CAACgB,WAAW,CACnBC,IAAI,CAAC5G,SAAS,CAAC,IAAI,CAACyG,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAACC,MAAM,IAAG;MAClB,IAAI,CAAC5F,WAAW,GAAG4F,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;MACpC,IAAI,CAACzE,gBAAgB,GAAGyE,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE;MAChD,IAAI,CAACtF,UAAU,GAAGsF,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;MACxC,IAAI,CAACb,MAAM,GAAGa,MAAM,CAAC,MAAM,CAAC,IAAI,UAAU;MAE1C,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEAJ,YAAYA,CAAA;IACV,IAAI,CAAC3C,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,IAAI,CAAClD,WAAW,IAAI,IAAI,CAACmB,gBAAgB,IAAI,IAAI,CAACb,UAAU,EAAE;MAChE,IAAI,CAAC0D,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACkC,uBAAuB,EAAE;KAC/B,MAAM;MACL;MACA,IAAI,CAAClC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACO,eAAe,CAAC4B,eAAe,EAAE,CACnCT,IAAI,CAAC5G,SAAS,CAAC,IAAI,CAACyG,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;QACTK,IAAI,EAAGI,IAAI,IAAI;UACbC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;UAClD,IAAI,CAACpD,SAAS,GAAG,KAAK;QACxB,CAAC;QACDqD,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,IAAI,CAACrD,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;;EAER;EAEA4C,aAAaA,CAAA;IACX,IAAI,CAACvB,eAAe,CAACuB,aAAa,EAAE,CACjCJ,IAAI,CAAC5G,SAAS,CAAC,IAAI,CAACyG,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;MACTK,IAAI,EAAGQ,KAAK,IAAI;QACd,IAAI,CAAC1F,SAAS,GAAG0F,KAAK;MACxB,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACN;EAEA;EACArG,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACF,WAAW,CAACyG,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACb,YAAY,EAAE;;EAEvB;EAEAc,kBAAkBA,CAACC,YAAoB;IACrC,IAAI,CAACpC,MAAM,CAACqC,QAAQ,CAAC,CAAC,gBAAgB,EAAED,YAAY,CAAC,CAAC;EACxD;EAEAE,eAAeA,CAACC,SAAiB;IAC/B,IAAI,CAACvC,MAAM,CAACqC,QAAQ,CAAC,CAAC,aAAa,EAAEE,SAAS,CAAC,CAAC;EAClD;EAEArF,iBAAiBA,CAACsF,SAAiB;IACjC,IAAI,CAACxC,MAAM,CAACqC,QAAQ,CAAC,CAAC,UAAU,EAAEG,SAAS,CAAC,CAAC;EAC/C;EAEA;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAACpC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC6B,eAAe,EAAE;IACtB,IAAI,CAACb,YAAY,EAAE;EACrB;EAEAtF,cAAcA,CAAA;IACZ,IAAI,CAACmG,eAAe,EAAE;IACtB,IAAI,CAACb,YAAY,EAAE;EACrB;EAEAqB,YAAYA,CAAA;IACV,IAAI,CAACR,eAAe,EAAE;IACtB,IAAI,CAACb,YAAY,EAAE;EACrB;EAEA/C,YAAYA,CAAA;IACV,IAAI,CAAC9C,WAAW,GAAG,EAAE;IACrB,IAAI,CAACmB,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC0D,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACxE,UAAU,GAAG,EAAE;IACpB,IAAI,CAACyE,MAAM,GAAG,UAAU;IACxB,IAAI,CAACC,UAAU,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAK,CAAE;IACxC,IAAI,CAACwB,eAAe,EAAE;IACtB,IAAI,CAACb,YAAY,EAAE;EACrB;EAEQa,eAAeA,CAAA;IACrB,MAAMjB,WAAW,GAAQ,EAAE;IAE3B,IAAI,IAAI,CAACzF,WAAW,EAAEyF,WAAW,CAAC0B,CAAC,GAAG,IAAI,CAACnH,WAAW;IACtD,IAAI,IAAI,CAACmB,gBAAgB,EAAEsE,WAAW,CAAC2B,QAAQ,GAAG,IAAI,CAACjG,gBAAgB;IACvE,IAAI,IAAI,CAACb,UAAU,EAAEmF,WAAW,CAAC4B,MAAM,GAAG,IAAI,CAAC/G,UAAU;IACzD,IAAI,IAAI,CAACyE,MAAM,KAAK,UAAU,EAAEU,WAAW,CAAC6B,IAAI,GAAG,IAAI,CAACvC,MAAM;IAE9D,IAAI,CAACP,MAAM,CAACqC,QAAQ,CAAC,EAAE,EAAE;MACvBU,UAAU,EAAE,IAAI,CAAC9C,KAAK;MACtBgB,WAAW;MACX+B,mBAAmB,EAAE;KACtB,CAAC;EACJ;EAEA;EACAtB,uBAAuBA,CAAA;IACrB,IAAI,CAAChD,SAAS,GAAG,IAAI;IAErB,MAAMuE,OAAO,GAAG;MACdL,QAAQ,EAAE,IAAI,CAACjG,gBAAgB;MAC/BuG,WAAW,EAAE,IAAI,CAAC7C,mBAAmB;MACrC1C,KAAK,EAAE,IAAI,CAAC2C,aAAa;MACzBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB4C,IAAI,EAAE,IAAI,CAACxC,WAAW;MACtByC,KAAK,EAAE,IAAI,CAACxC;KACb;IAED,IAAI,CAACb,eAAe,CAACsD,cAAc,CAAC,IAAI,CAAC7H,WAAW,EAAEyH,OAAO,CAAC,CAC3D/B,IAAI,CAAC5G,SAAS,CAAC,IAAI,CAACyG,QAAQ,CAAC,CAAC,CAC9BI,SAAS,CAAC;MACTK,IAAI,EAAG8B,QAAQ,IAAI;QACjB,IAAI,CAACxC,WAAW,GAAGwC,QAAQ,CAACC,QAAQ,IAAI,EAAE;QAC1C,IAAI,CAACzF,gBAAgB,GAAG,IAAI,CAACgD,WAAW;QACxC,IAAI,CAACD,OAAO,GAAGyC,QAAQ,CAACE,UAAU,GAAI,IAAI,CAAC7C,WAAW,GAAG,IAAI,CAACC,YAAa;QAC3E,IAAI,CAAClC,SAAS,GAAG,KAAK;MACxB,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACjB,WAAW,GAAG,EAAE;QACrB,IAAI,CAAChD,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACY,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEA;EACAnB,eAAeA,CAACkG,OAAY;IAC1B,OAAOA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,gCAAgC;EACrE;EAEA/G,qBAAqBA,CAAC6G,OAAY;IAChC,IAAI,CAACA,OAAO,CAAC3G,aAAa,IAAI2G,OAAO,CAAC3G,aAAa,IAAI2G,OAAO,CAAC7F,KAAK,EAAE,OAAO,CAAC;IAC9E,OAAOgG,IAAI,CAACC,KAAK,CAAE,CAACJ,OAAO,CAAC3G,aAAa,GAAG2G,OAAO,CAAC7F,KAAK,IAAI6F,OAAO,CAAC3G,aAAa,GAAI,GAAG,CAAC;EAC5F;EAEAT,YAAYA,CAACyH,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;;;uBAnNWvE,aAAa,EAAA3E,EAAA,CAAAmJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArJ,EAAA,CAAAmJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAvJ,EAAA,CAAAmJ,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAzJ,EAAA,CAAAmJ,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA3J,EAAA,CAAAmJ,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAA7J,EAAA,CAAAmJ,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAA/J,EAAA,CAAAmJ,iBAAA,CAAAW,EAAA,CAAAE,cAAA,GAAAhK,EAAA,CAAAmJ,iBAAA,CAAAc,EAAA,CAAAC,UAAA,GAAAlK,EAAA,CAAAmJ,iBAAA,CAAAgB,EAAA,CAAAC,mBAAA,GAAApK,EAAA,CAAAmJ,iBAAA,CAAAkB,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAb3F,aAAa;MAAA4F,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzK,EAAA,CAAA0K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvC1BhL,EAAA,CAAAG,cAAA,aAA4B;UAE1BH,EAAA,CAAAC,SAAA,2BAAiE;UAWjED,EARA,CAAAsC,UAAA,IAAA4I,4CAAA,iCAIyB,IAAAC,4BAAA,kBAIoB;UAoJ/CnL,EAAA,CAAAa,YAAA,EAAM;;;UA/Jeb,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAE,UAAA,0BAAyB;UAIzCF,EAAA,CAAAqB,SAAA,EAAe;UAAfrB,EAAA,CAAAE,UAAA,SAAA+K,GAAA,CAAArH,SAAA,CAAe;UAOZ5D,EAAA,CAAAqB,SAAA,EAAgB;UAAhBrB,EAAA,CAAAE,UAAA,UAAA+K,GAAA,CAAArH,SAAA,CAAgB;;;qBDapBvE,YAAY,EAAA+L,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAAF,GAAA,CAAAG,aAAA,EACZjM,WAAW,EAAAkM,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EACXlM,eAAe,EACfC,uBAAuB,EACvBC,qBAAqB,EACrBC,uBAAuB,EACvBC,oBAAoB,EACpBC,oBAAoB,EACpBC,mBAAmB;MAAA6L,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}