import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, NavigationEnd } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth.service';
import { filter } from 'rxjs/operators';

interface NavItem {
  icon: string;
  activeIcon: string;
  label: string;
  route: string;
  badge?: number;
  requiresAuth?: boolean;
}

@Component({
  selector: 'app-mobile-bottom-nav',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './mobile-bottom-nav.component.html',
  styleUrls: ['./mobile-bottom-nav.component.scss']
})
export class MobileBottomNavComponent implements OnInit {
  currentRoute = '';
  isAuthenticated = false;
  currentUser: any = null;
  showCreateOptions = false;

  navItems: NavItem[] = [
    {
      icon: 'far fa-home',
      activeIcon: 'fas fa-home',
      label: 'Home',
      route: '/home'
    },
    {
      icon: 'far fa-search',
      activeIcon: 'fas fa-search',
      label: 'Explore',
      route: '/explore'
    },
    {
      icon: 'far fa-shopping-bag',
      activeIcon: 'fas fa-shopping-bag',
      label: 'Shop',
      route: '/shop'
    },
    {
      icon: 'far fa-heart',
      activeIcon: 'fas fa-heart',
      label: 'Wishlist',
      route: '/wishlist',
      requiresAuth: true
    },
    {
      icon: 'far fa-user-circle',
      activeIcon: 'fas fa-user-circle',
      label: 'Profile',
      route: '/profile',
      requiresAuth: true
    }
  ];

  constructor(
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit() {
    // Track current route
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event) => {
        if (event instanceof NavigationEnd) {
          this.currentRoute = event.url.split('?')[0]; // Remove query params
        }
      });

    // Track authentication status
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.isAuthenticated = !!user;
    });

    // Set initial route
    this.currentRoute = this.router.url.split('?')[0];
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }

  isActive(route: string): boolean {
    if (route === '/home') {
      return this.currentRoute === '/' || this.currentRoute === '/home';
    }
    return this.currentRoute.startsWith(route);
  }

  getVisibleNavItems(): NavItem[] {
    return this.navItems.filter(item => {
      if (item.requiresAuth && !this.isAuthenticated) {
        return false;
      }
      return true;
    });
  }

  shouldShowBottomNav(): boolean {
    // Hide on certain pages
    const hiddenRoutes = [
      '/login',
      '/register',
      '/onboarding',
      '/stories/create',
      '/posts/create'
    ];

    return !hiddenRoutes.some(route => this.currentRoute.startsWith(route));
  }

  getUserAvatar(): string {
    return this.currentUser?.avatar || 'assets/default-avatar.png';
  }

  // Create menu methods
  showCreateMenu() {
    this.showCreateOptions = true;
  }

  hideCreateMenu() {
    this.showCreateOptions = false;
  }

  createStory() {
    this.hideCreateMenu();
    this.router.navigate(['/stories/create']);
  }

  createPost() {
    this.hideCreateMenu();
    this.router.navigate(['/posts/create']);
  }

  createReel() {
    this.hideCreateMenu();
    this.router.navigate(['/reels/create']);
  }

  addProduct() {
    this.hideCreateMenu();
    this.router.navigate(['/vendor/products/add']);
  }

  trackByRoute(index: number, item: NavItem): string {
    return item.route;
  }
}
