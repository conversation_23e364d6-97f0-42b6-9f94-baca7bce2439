{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Based on:\n * https://stackoverflow.com/questions/7348009/y-coordinate-for-a-given-x-cubic-bezier\n * https://math.stackexchange.com/questions/26846/is-there-an-explicit-form-for-cubic-b%C3%A9zier-curves\n */\n/**\n * EXPERIMENTAL\n * Given a cubic-bezier curve, get the x value (time) given\n * the y value (progression).\n * Ex: cubic-bezier(0.32, 0.72, 0, 1);\n * P0: (0, 0)\n * P1: (0.32, 0.72)\n * P2: (0, 1)\n * P3: (1, 1)\n *\n * If you give a cubic bezier curve that never reaches the\n * provided progression, this function will return an empty array.\n */\nconst getTimeGivenProgression = (p0, p1, p2, p3, progression) => {\n  return solveCubicBezier(p0[1], p1[1], p2[1], p3[1], progression).map(tValue => {\n    return solveCubicParametricEquation(p0[0], p1[0], p2[0], p3[0], tValue);\n  });\n};\n/**\n * Solve a cubic equation in one dimension (time)\n */\nconst solveCubicParametricEquation = (p0, p1, p2, p3, t) => {\n  const partA = 3 * p1 * Math.pow(t - 1, 2);\n  const partB = -3 * p2 * t + 3 * p2 + p3 * t;\n  const partC = p0 * Math.pow(t - 1, 3);\n  return t * (partA + t * partB) - partC;\n};\n/**\n * Find the `t` value for a cubic bezier using Cardano's formula\n */\nconst solveCubicBezier = (p0, p1, p2, p3, refPoint) => {\n  p0 -= refPoint;\n  p1 -= refPoint;\n  p2 -= refPoint;\n  p3 -= refPoint;\n  const roots = solveCubicEquation(p3 - 3 * p2 + 3 * p1 - p0, 3 * p2 - 6 * p1 + 3 * p0, 3 * p1 - 3 * p0, p0);\n  return roots.filter(root => root >= 0 && root <= 1);\n};\nconst solveQuadraticEquation = (a, b, c) => {\n  const discriminant = b * b - 4 * a * c;\n  if (discriminant < 0) {\n    return [];\n  } else {\n    return [(-b + Math.sqrt(discriminant)) / (2 * a), (-b - Math.sqrt(discriminant)) / (2 * a)];\n  }\n};\nconst solveCubicEquation = (a, b, c, d) => {\n  if (a === 0) {\n    return solveQuadraticEquation(b, c, d);\n  }\n  b /= a;\n  c /= a;\n  d /= a;\n  const p = (3 * c - b * b) / 3;\n  const q = (2 * b * b * b - 9 * b * c + 27 * d) / 27;\n  if (p === 0) {\n    return [Math.pow(-q, 1 / 3)];\n  } else if (q === 0) {\n    return [Math.sqrt(-p), -Math.sqrt(-p)];\n  }\n  const discriminant = Math.pow(q / 2, 2) + Math.pow(p / 3, 3);\n  if (discriminant === 0) {\n    return [Math.pow(q / 2, 1 / 2) - b / 3];\n  } else if (discriminant > 0) {\n    return [Math.pow(-(q / 2) + Math.sqrt(discriminant), 1 / 3) - Math.pow(q / 2 + Math.sqrt(discriminant), 1 / 3) - b / 3];\n  }\n  const r = Math.sqrt(Math.pow(-(p / 3), 3));\n  const phi = Math.acos(-(q / (2 * Math.sqrt(Math.pow(-(p / 3), 3)))));\n  const s = 2 * Math.pow(r, 1 / 3);\n  return [s * Math.cos(phi / 3) - b / 3, s * Math.cos((phi + 2 * Math.PI) / 3) - b / 3, s * Math.cos((phi + 4 * Math.PI) / 3) - b / 3];\n};\nexport { getTimeGivenProgression as g };", "map": {"version": 3, "names": ["getTimeGivenProgression", "p0", "p1", "p2", "p3", "progression", "solveCubicBezier", "map", "tValue", "solveCubicParametricEquation", "t", "partA", "Math", "pow", "partB", "partC", "refPoint", "roots", "solveCubicEquation", "filter", "root", "solveQuadraticEquation", "a", "b", "c", "discriminant", "sqrt", "d", "p", "q", "r", "phi", "acos", "s", "cos", "PI", "g"], "sources": ["E:/Fahion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/cubic-bezier-fe2083dc.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Based on:\n * https://stackoverflow.com/questions/7348009/y-coordinate-for-a-given-x-cubic-bezier\n * https://math.stackexchange.com/questions/26846/is-there-an-explicit-form-for-cubic-b%C3%A9zier-curves\n */\n/**\n * EXPERIMENTAL\n * Given a cubic-bezier curve, get the x value (time) given\n * the y value (progression).\n * Ex: cubic-bezier(0.32, 0.72, 0, 1);\n * P0: (0, 0)\n * P1: (0.32, 0.72)\n * P2: (0, 1)\n * P3: (1, 1)\n *\n * If you give a cubic bezier curve that never reaches the\n * provided progression, this function will return an empty array.\n */\nconst getTimeGivenProgression = (p0, p1, p2, p3, progression) => {\n    return solveCubicBezier(p0[1], p1[1], p2[1], p3[1], progression).map((tValue) => {\n        return solveCubicParametricEquation(p0[0], p1[0], p2[0], p3[0], tValue);\n    });\n};\n/**\n * Solve a cubic equation in one dimension (time)\n */\nconst solveCubicParametricEquation = (p0, p1, p2, p3, t) => {\n    const partA = 3 * p1 * Math.pow(t - 1, 2);\n    const partB = -3 * p2 * t + 3 * p2 + p3 * t;\n    const partC = p0 * Math.pow(t - 1, 3);\n    return t * (partA + t * partB) - partC;\n};\n/**\n * Find the `t` value for a cubic bezier using Cardano's formula\n */\nconst solveCubicBezier = (p0, p1, p2, p3, refPoint) => {\n    p0 -= refPoint;\n    p1 -= refPoint;\n    p2 -= refPoint;\n    p3 -= refPoint;\n    const roots = solveCubicEquation(p3 - 3 * p2 + 3 * p1 - p0, 3 * p2 - 6 * p1 + 3 * p0, 3 * p1 - 3 * p0, p0);\n    return roots.filter((root) => root >= 0 && root <= 1);\n};\nconst solveQuadraticEquation = (a, b, c) => {\n    const discriminant = b * b - 4 * a * c;\n    if (discriminant < 0) {\n        return [];\n    }\n    else {\n        return [(-b + Math.sqrt(discriminant)) / (2 * a), (-b - Math.sqrt(discriminant)) / (2 * a)];\n    }\n};\nconst solveCubicEquation = (a, b, c, d) => {\n    if (a === 0) {\n        return solveQuadraticEquation(b, c, d);\n    }\n    b /= a;\n    c /= a;\n    d /= a;\n    const p = (3 * c - b * b) / 3;\n    const q = (2 * b * b * b - 9 * b * c + 27 * d) / 27;\n    if (p === 0) {\n        return [Math.pow(-q, 1 / 3)];\n    }\n    else if (q === 0) {\n        return [Math.sqrt(-p), -Math.sqrt(-p)];\n    }\n    const discriminant = Math.pow(q / 2, 2) + Math.pow(p / 3, 3);\n    if (discriminant === 0) {\n        return [Math.pow(q / 2, 1 / 2) - b / 3];\n    }\n    else if (discriminant > 0) {\n        return [\n            Math.pow(-(q / 2) + Math.sqrt(discriminant), 1 / 3) - Math.pow(q / 2 + Math.sqrt(discriminant), 1 / 3) - b / 3,\n        ];\n    }\n    const r = Math.sqrt(Math.pow(-(p / 3), 3));\n    const phi = Math.acos(-(q / (2 * Math.sqrt(Math.pow(-(p / 3), 3)))));\n    const s = 2 * Math.pow(r, 1 / 3);\n    return [\n        s * Math.cos(phi / 3) - b / 3,\n        s * Math.cos((phi + 2 * Math.PI) / 3) - b / 3,\n        s * Math.cos((phi + 4 * Math.PI) / 3) - b / 3,\n    ];\n};\n\nexport { getTimeGivenProgression as g };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,uBAAuB,GAAGA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,WAAW,KAAK;EAC7D,OAAOC,gBAAgB,CAACL,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,WAAW,CAAC,CAACE,GAAG,CAAEC,MAAM,IAAK;IAC7E,OAAOC,4BAA4B,CAACR,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEI,MAAM,CAAC;EAC3E,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,MAAMC,4BAA4B,GAAGA,CAACR,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEM,CAAC,KAAK;EACxD,MAAMC,KAAK,GAAG,CAAC,GAAGT,EAAE,GAAGU,IAAI,CAACC,GAAG,CAACH,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACzC,MAAMI,KAAK,GAAG,CAAC,CAAC,GAAGX,EAAE,GAAGO,CAAC,GAAG,CAAC,GAAGP,EAAE,GAAGC,EAAE,GAAGM,CAAC;EAC3C,MAAMK,KAAK,GAAGd,EAAE,GAAGW,IAAI,CAACC,GAAG,CAACH,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACrC,OAAOA,CAAC,IAAIC,KAAK,GAAGD,CAAC,GAAGI,KAAK,CAAC,GAAGC,KAAK;AAC1C,CAAC;AACD;AACA;AACA;AACA,MAAMT,gBAAgB,GAAGA,CAACL,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEY,QAAQ,KAAK;EACnDf,EAAE,IAAIe,QAAQ;EACdd,EAAE,IAAIc,QAAQ;EACdb,EAAE,IAAIa,QAAQ;EACdZ,EAAE,IAAIY,QAAQ;EACd,MAAMC,KAAK,GAAGC,kBAAkB,CAACd,EAAE,GAAG,CAAC,GAAGD,EAAE,GAAG,CAAC,GAAGD,EAAE,GAAGD,EAAE,EAAE,CAAC,GAAGE,EAAE,GAAG,CAAC,GAAGD,EAAE,GAAG,CAAC,GAAGD,EAAE,EAAE,CAAC,GAAGC,EAAE,GAAG,CAAC,GAAGD,EAAE,EAAEA,EAAE,CAAC;EAC1G,OAAOgB,KAAK,CAACE,MAAM,CAAEC,IAAI,IAAKA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC;AACzD,CAAC;AACD,MAAMC,sBAAsB,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EACxC,MAAMC,YAAY,GAAGF,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGE,CAAC;EACtC,IAAIC,YAAY,GAAG,CAAC,EAAE;IAClB,OAAO,EAAE;EACb,CAAC,MACI;IACD,OAAO,CAAC,CAAC,CAACF,CAAC,GAAGX,IAAI,CAACc,IAAI,CAACD,YAAY,CAAC,KAAK,CAAC,GAAGH,CAAC,CAAC,EAAE,CAAC,CAACC,CAAC,GAAGX,IAAI,CAACc,IAAI,CAACD,YAAY,CAAC,KAAK,CAAC,GAAGH,CAAC,CAAC,CAAC;EAC/F;AACJ,CAAC;AACD,MAAMJ,kBAAkB,GAAGA,CAACI,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,KAAK;EACvC,IAAIL,CAAC,KAAK,CAAC,EAAE;IACT,OAAOD,sBAAsB,CAACE,CAAC,EAAEC,CAAC,EAAEG,CAAC,CAAC;EAC1C;EACAJ,CAAC,IAAID,CAAC;EACNE,CAAC,IAAIF,CAAC;EACNK,CAAC,IAAIL,CAAC;EACN,MAAMM,CAAC,GAAG,CAAC,CAAC,GAAGJ,CAAC,GAAGD,CAAC,GAAGA,CAAC,IAAI,CAAC;EAC7B,MAAMM,CAAC,GAAG,CAAC,CAAC,GAAGN,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAG,EAAE,GAAGG,CAAC,IAAI,EAAE;EACnD,IAAIC,CAAC,KAAK,CAAC,EAAE;IACT,OAAO,CAAChB,IAAI,CAACC,GAAG,CAAC,CAACgB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;EAChC,CAAC,MACI,IAAIA,CAAC,KAAK,CAAC,EAAE;IACd,OAAO,CAACjB,IAAI,CAACc,IAAI,CAAC,CAACE,CAAC,CAAC,EAAE,CAAChB,IAAI,CAACc,IAAI,CAAC,CAACE,CAAC,CAAC,CAAC;EAC1C;EACA,MAAMH,YAAY,GAAGb,IAAI,CAACC,GAAG,CAACgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAGjB,IAAI,CAACC,GAAG,CAACe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC5D,IAAIH,YAAY,KAAK,CAAC,EAAE;IACpB,OAAO,CAACb,IAAI,CAACC,GAAG,CAACgB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAGN,CAAC,GAAG,CAAC,CAAC;EAC3C,CAAC,MACI,IAAIE,YAAY,GAAG,CAAC,EAAE;IACvB,OAAO,CACHb,IAAI,CAACC,GAAG,CAAC,EAAEgB,CAAC,GAAG,CAAC,CAAC,GAAGjB,IAAI,CAACc,IAAI,CAACD,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAGb,IAAI,CAACC,GAAG,CAACgB,CAAC,GAAG,CAAC,GAAGjB,IAAI,CAACc,IAAI,CAACD,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAGF,CAAC,GAAG,CAAC,CACjH;EACL;EACA,MAAMO,CAAC,GAAGlB,IAAI,CAACc,IAAI,CAACd,IAAI,CAACC,GAAG,CAAC,EAAEe,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1C,MAAMG,GAAG,GAAGnB,IAAI,CAACoB,IAAI,CAAC,EAAEH,CAAC,IAAI,CAAC,GAAGjB,IAAI,CAACc,IAAI,CAACd,IAAI,CAACC,GAAG,CAAC,EAAEe,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMK,CAAC,GAAG,CAAC,GAAGrB,IAAI,CAACC,GAAG,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;EAChC,OAAO,CACHG,CAAC,GAAGrB,IAAI,CAACsB,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGR,CAAC,GAAG,CAAC,EAC7BU,CAAC,GAAGrB,IAAI,CAACsB,GAAG,CAAC,CAACH,GAAG,GAAG,CAAC,GAAGnB,IAAI,CAACuB,EAAE,IAAI,CAAC,CAAC,GAAGZ,CAAC,GAAG,CAAC,EAC7CU,CAAC,GAAGrB,IAAI,CAACsB,GAAG,CAAC,CAACH,GAAG,GAAG,CAAC,GAAGnB,IAAI,CAACuB,EAAE,IAAI,CAAC,CAAC,GAAGZ,CAAC,GAAG,CAAC,CAChD;AACL,CAAC;AAED,SAASvB,uBAAuB,IAAIoC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}