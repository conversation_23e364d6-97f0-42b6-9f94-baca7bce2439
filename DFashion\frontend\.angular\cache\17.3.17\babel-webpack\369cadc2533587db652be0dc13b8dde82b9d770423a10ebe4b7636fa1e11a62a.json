{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nexport function throwError(errorOrErrorFactory, scheduler) {\n  const errorFactory = isFunction(errorOrErrorFactory) ? errorOrErrorFactory : () => errorOrErrorFactory;\n  const init = subscriber => subscriber.error(errorFactory());\n  return new Observable(scheduler ? subscriber => scheduler.schedule(init, 0, subscriber) : init);\n}", "map": {"version": 3, "names": ["Observable", "isFunction", "throwError", "errorOrErrorFactory", "scheduler", "errorFactory", "init", "subscriber", "error", "schedule"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/rxjs/dist/esm/internal/observable/throwError.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nexport function throwError(errorOrErrorFactory, scheduler) {\n    const errorFactory = isFunction(errorOrErrorFactory) ? errorOrErrorFactory : () => errorOrErrorFactory;\n    const init = (subscriber) => subscriber.error(errorFactory());\n    return new Observable(scheduler ? (subscriber) => scheduler.schedule(init, 0, subscriber) : init);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,UAAUA,CAACC,mBAAmB,EAAEC,SAAS,EAAE;EACvD,MAAMC,YAAY,GAAGJ,UAAU,CAACE,mBAAmB,CAAC,GAAGA,mBAAmB,GAAG,MAAMA,mBAAmB;EACtG,MAAMG,IAAI,GAAIC,UAAU,IAAKA,UAAU,CAACC,KAAK,CAACH,YAAY,CAAC,CAAC,CAAC;EAC7D,OAAO,IAAIL,UAAU,CAACI,SAAS,GAAIG,UAAU,IAAKH,SAAS,CAACK,QAAQ,CAACH,IAAI,EAAE,CAAC,EAAEC,UAAU,CAAC,GAAGD,IAAI,CAAC;AACrG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}