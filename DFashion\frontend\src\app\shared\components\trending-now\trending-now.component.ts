import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ShopDataService, TrendingProduct } from '../../../core/services/shop-data.service';

@Component({
  selector: 'app-trending-now',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="trending-now-section">
      <!-- Section Header -->
      <div class="section-header">
        <div class="header-content">
          <h2 class="section-title">
            <i class="fas fa-fire"></i>
            Trending Now
          </h2>
          <p class="section-subtitle">Hot picks that everyone's talking about</p>
        </div>
        <div class="header-actions">
          <div class="trend-filters">
            <button 
              *ngFor="let filter of trendFilters"
              class="filter-btn"
              [class.active]="activeFilter === filter.value"
              (click)="setActiveFilter(filter.value)">
              {{ filter.label }}
            </button>
          </div>
          <button class="view-all-btn" (click)="viewAllTrending()">
            <span>View All</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="products-scroll">
          <div *ngFor="let item of [1,2,3,4,5,6]" class="product-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-content">
              <div class="skeleton-text"></div>
              <div class="skeleton-text short"></div>
              <div class="skeleton-text"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Products Scroll -->
      <div *ngIf="!isLoading && products.length > 0" class="products-container">
        <div class="products-scroll" #productsScroll>
          <div 
            *ngFor="let product of products; trackBy: trackByProductId" 
            class="product-card"
            (click)="navigateToProduct(product)"
            [attr.aria-label]="'View ' + product.name"
            tabindex="0"
            (keydown.enter)="navigateToProduct(product)"
            (keydown.space)="navigateToProduct(product)">
            
            <!-- Product Image -->
            <div class="product-image-container">
              <img 
                [src]="getProductImage(product)" 
                [alt]="product.name"
                class="product-image"
                loading="lazy"
                (error)="onImageError($event)">
              
              <!-- Trending Badge -->
              <div class="trending-badge">
                <i class="fas fa-fire"></i>
                <span>{{ product.trendingScore }}%</span>
              </div>
              
              <!-- Discount Badge -->
              <div class="discount-badge" *ngIf="product.pricing.discountPercentage > 0">
                {{ product.pricing.discountPercentage }}% OFF
              </div>
              
              <!-- Quick Actions -->
              <div class="quick-actions">
                <button 
                  class="quick-action-btn"
                  (click)="toggleWishlist(product); $event.stopPropagation()"
                  [attr.aria-label]="'Add to wishlist'">
                  <i class="fas fa-heart" [class.wishlisted]="isInWishlist(product._id)"></i>
                </button>
                <button 
                  class="quick-action-btn"
                  (click)="quickView(product); $event.stopPropagation()"
                  [attr.aria-label]="'Quick view'">
                  <i class="fas fa-eye"></i>
                </button>
                <button 
                  class="quick-action-btn"
                  (click)="addToCart(product); $event.stopPropagation()"
                  [attr.aria-label]="'Add to cart'"
                  [disabled]="product.availability.status !== 'in-stock'">
                  <i class="fas fa-shopping-cart"></i>
                </button>
              </div>
            </div>

            <!-- Product Info -->
            <div class="product-info">
              <div class="product-brand">{{ product.brand }}</div>
              <h3 class="product-name">{{ product.name }}</h3>
              
              <!-- Rating -->
              <div class="product-rating" *ngIf="product.rating.count > 0">
                <div class="stars">
                  <i *ngFor="let star of getStarArray(product.rating.average)" 
                     class="fas fa-star" 
                     [class.filled]="star <= product.rating.average"></i>
                </div>
                <span class="rating-text">({{ product.rating.count }})</span>
              </div>
              
              <!-- Pricing -->
              <div class="product-pricing">
                <span class="current-price">₹{{ product.pricing.sellingPrice | number:'1.0-0' }}</span>
                <span class="original-price" *ngIf="product.pricing.discountPercentage > 0">
                  ₹{{ product.pricing.mrp | number:'1.0-0' }}
                </span>
              </div>
              
              <!-- Trending Stats -->
              <div class="trending-stats">
                <div class="stat-item">
                  <i class="fas fa-eye"></i>
                  <span>{{ formatNumber(product.analytics.views) }}</span>
                </div>
                <div class="stat-item">
                  <i class="fas fa-heart"></i>
                  <span>{{ formatNumber(product.analytics.likes) }}</span>
                </div>
                <div class="stat-item">
                  <i class="fas fa-shopping-bag"></i>
                  <span>{{ formatNumber(product.analytics.purchases) }}</span>
                </div>
              </div>
              
              <!-- Stock Status -->
              <div class="stock-status" [class]="product.availability.status">
                <span *ngIf="product.availability.status === 'in-stock'">
                  <i class="fas fa-check-circle"></i>
                  In Stock ({{ product.availability.totalStock }})
                </span>
                <span *ngIf="product.availability.status === 'low-stock'">
                  <i class="fas fa-exclamation-triangle"></i>
                  Only {{ product.availability.totalStock }} left
                </span>
                <span *ngIf="product.availability.status === 'out-of-stock'">
                  <i class="fas fa-times-circle"></i>
                  Out of Stock
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Scroll Navigation -->
        <button class="scroll-btn scroll-left" 
                (click)="scrollProducts('left')"
                [disabled]="!canScrollLeft"
                *ngIf="showScrollButtons">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button class="scroll-btn scroll-right" 
                (click)="scrollProducts('right')"
                [disabled]="!canScrollRight"
                *ngIf="showScrollButtons">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && products.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-fire-extinguisher"></i>
        </div>
        <h3>No Trending Products</h3>
        <p>Check back later for the hottest trends!</p>
        <button class="retry-btn" (click)="loadProducts()">
          <i class="fas fa-refresh"></i>
          Try Again
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./trending-now.component.scss']
})
export class TrendingNowComponent implements OnInit, OnDestroy {
  @Input() maxProducts: number = 12;
  @Input() showHeader: boolean = true;
  @Input() showScrollButtons: boolean = true;
  
  products: TrendingProduct[] = [];
  isLoading: boolean = true;
  activeFilter: string = 'all';
  wishlistItems: Set<string> = new Set();
  canScrollLeft: boolean = false;
  canScrollRight: boolean = true;
  
  trendFilters = [
    { label: 'All', value: 'all' },
    { label: 'Today', value: 'today' },
    { label: 'This Week', value: 'week' },
    { label: 'This Month', value: 'month' }
  ];
  
  private destroy$ = new Subject<void>();

  constructor(
    private shopDataService: ShopDataService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadProducts();
    this.loadWishlist();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadProducts(): void {
    this.isLoading = true;
    
    this.shopDataService.loadTrendingProducts(this.maxProducts)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (products) => {
          this.products = products;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading trending products:', error);
          this.isLoading = false;
        }
      });
  }

  setActiveFilter(filter: string): void {
    this.activeFilter = filter;
    // In a real implementation, this would filter the products
    // For now, we'll just reload the products
    this.loadProducts();
  }

  navigateToProduct(product: TrendingProduct): void {
    // Track product click
    this.trackProductClick(product);
    
    // Navigate to product detail page
    this.router.navigate(['/product', product._id]);
  }

  viewAllTrending(): void {
    this.router.navigate(['/shop/trending']);
  }

  toggleWishlist(product: TrendingProduct): void {
    if (this.wishlistItems.has(product._id)) {
      this.wishlistItems.delete(product._id);
    } else {
      this.wishlistItems.add(product._id);
    }
    this.saveWishlist();
  }

  isInWishlist(productId: string): boolean {
    return this.wishlistItems.has(productId);
  }

  quickView(product: TrendingProduct): void {
    // Implement quick view modal
    console.log('Quick view:', product);
  }

  addToCart(product: TrendingProduct): void {
    if (product.availability.status !== 'in-stock') {
      return;
    }
    
    // Add to cart logic
    console.log('Add to cart:', product);
    
    // Show success message
    this.showAddToCartSuccess(product);
  }

  scrollProducts(direction: 'left' | 'right'): void {
    const container = document.querySelector('.products-scroll') as HTMLElement;
    if (!container) return;
    
    const scrollAmount = 300;
    const currentScroll = container.scrollLeft;
    
    if (direction === 'left') {
      container.scrollTo({
        left: currentScroll - scrollAmount,
        behavior: 'smooth'
      });
    } else {
      container.scrollTo({
        left: currentScroll + scrollAmount,
        behavior: 'smooth'
      });
    }
    
    // Update scroll button states
    setTimeout(() => {
      this.updateScrollButtons();
    }, 300);
  }

  getProductImage(product: TrendingProduct): string {
    const primaryImage = product.images.find(img => img.isPrimary);
    return primaryImage ? primaryImage.url : product.images[0]?.url || '';
  }

  getStarArray(rating: number): number[] {
    return Array(5).fill(0).map((_, i) => i + 1);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  onImageError(event: any): void {
    event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';
  }

  trackByProductId(index: number, product: TrendingProduct): string {
    return product._id;
  }

  private updateScrollButtons(): void {
    const container = document.querySelector('.products-scroll') as HTMLElement;
    if (!container) return;
    
    this.canScrollLeft = container.scrollLeft > 0;
    this.canScrollRight = container.scrollLeft < (container.scrollWidth - container.clientWidth);
  }

  private trackProductClick(product: TrendingProduct): void {
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'product_click', {
        product_name: product.name,
        product_id: product._id,
        product_brand: product.brand,
        product_category: product.category,
        event_category: 'engagement'
      });
    }
  }

  private loadWishlist(): void {
    const stored = localStorage.getItem('dfashion_wishlist');
    if (stored) {
      try {
        const wishlist = JSON.parse(stored);
        this.wishlistItems = new Set(wishlist);
      } catch (e) {
        console.warn('Failed to load wishlist');
      }
    }
  }

  private saveWishlist(): void {
    localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));
  }

  private showAddToCartSuccess(product: TrendingProduct): void {
    // Show a toast or notification
    console.log(`${product.name} added to cart!`);

    // In a real implementation, you might show a toast notification
    // this.toastService.show(`${product.name} added to cart!`, 'success');
  }
}
