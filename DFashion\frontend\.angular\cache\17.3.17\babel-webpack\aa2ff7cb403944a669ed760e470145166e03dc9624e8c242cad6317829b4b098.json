{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { isDevMode, Injectable, InjectionToken, PLATFORM_ID, Inject, Optional, input, Directive, HostListener, Component, output, signal, ContentChildren, ChangeDetectionStrategy, Input, Attribute, HostBinding, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport { toObservable } from '@angular/core/rxjs-interop';\nimport { Subject, merge, of, fromEvent, from } from 'rxjs';\nimport { tap, filter, switchMap, first, take, skip, map, toArray, pairwise, delay } from 'rxjs/operators';\nimport * as i1 from '@angular/router';\nimport { NavigationEnd } from '@angular/router';\nimport { trigger, state, transition, style, animate } from '@angular/animations';\n\n/**\n * Defaults value of options\n */\nconst _forTrack0 = ($index, $item) => $item.id;\nconst _c0 = (a0, a1, a2, a3, a4) => ({\n  \"width\": a0,\n  \"transform\": a1,\n  \"transition\": a2,\n  \"padding-left\": a3,\n  \"padding-right\": a4\n});\nconst _c1 = (a0, a1, a2, a3) => ({\n  \"width\": a0,\n  \"margin-left\": a1,\n  \"margin-right\": a2,\n  \"left\": a3\n});\nconst _c2 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction StageComponent_For_3_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction StageComponent_For_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, StageComponent_For_3_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const slide_r2 = ctx_r3.$implicit;\n    const i_r5 = ctx_r3.$index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", slide_r2.tplRef)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r2.preparePublicSlide(slide_r2), i_r5));\n  }\n}\nfunction StageComponent_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"animationend\", function StageComponent_For_3_Template_div_animationend_0_listener() {\n      const slide_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clear(slide_r2.id));\n    });\n    i0.ɵɵtemplate(1, StageComponent_For_3_Conditional_1_Template, 1, 5, null, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const slide_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", slide_r2.classes)(\"ngStyle\", i0.ɵɵpureFunction4(4, _c1, slide_r2.width + \"px\", slide_r2.marginL ? slide_r2.marginL + \"px\" : \"\", slide_r2.marginR ? slide_r2.marginR + \"px\" : \"\", slide_r2.left))(\"@autoHeight\", slide_r2.heightState);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, slide_r2.load ? 1 : -1);\n  }\n}\nconst _c3 = (a0, a1, a2, a3, a4) => ({\n  \"owl-rtl\": a0,\n  \"owl-loaded\": a1,\n  \"owl-responsive\": a2,\n  \"owl-drag\": a3,\n  \"owl-grab\": a4\n});\nconst _c4 = (a0, a1) => ({\n  \"isMouseDragable\": a0,\n  \"isTouchDragable\": a1\n});\nconst _c5 = a0 => ({\n  \"disabled\": a0\n});\nconst _c6 = (a0, a1) => ({\n  \"active\": a0,\n  \"owl-dot-text\": a1\n});\nfunction CarouselComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"owl-stage\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"owlDraggable\", i0.ɵɵpureFunction2(3, _c4, (tmp_2_0 = ctx_r1.owlDOMData()) == null ? null : tmp_2_0.isMouseDragable, (tmp_2_0 = ctx_r1.owlDOMData()) == null ? null : tmp_2_0.isTouchDragable))(\"stageData\", ctx_r1.stageData())(\"slidesData\", ctx_r1.slidesData());\n  }\n}\nfunction CarouselComponent_Conditional_3_For_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_Conditional_3_For_5_Template_div_click_0_listener() {\n      const dot_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveByDot(dot_r5.id));\n    });\n    i0.ɵɵelement(1, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dot_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c6, dot_r5.active, dot_r5.showInnerContent));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dot_r5.innerContent, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction CarouselComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_Conditional_3_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prev());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_Conditional_3_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 7);\n    i0.ɵɵrepeaterCreate(4, CarouselComponent_Conditional_3_For_5_Template, 2, 5, \"div\", 8, _forTrack0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c5, (tmp_2_0 = ctx_r1.navData()) == null ? null : tmp_2_0.disabled));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c5, (tmp_3_0 = ctx_r1.navData()) == null ? null : tmp_3_0.prev == null ? null : tmp_3_0.prev.disabled))(\"innerHTML\", (tmp_4_0 = ctx_r1.navData()) == null ? null : tmp_4_0.prev == null ? null : tmp_4_0.prev.htmlText, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c5, (tmp_5_0 = ctx_r1.navData()) == null ? null : tmp_5_0.next == null ? null : tmp_5_0.next.disabled))(\"innerHTML\", (tmp_6_0 = ctx_r1.navData()) == null ? null : tmp_6_0.next == null ? null : tmp_6_0.next.htmlText, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c5, (tmp_7_0 = ctx_r1.dotsData()) == null ? null : tmp_7_0.disabled));\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater((tmp_8_0 = ctx_r1.dotsData()) == null ? null : tmp_8_0.dots);\n  }\n}\nclass OwlCarouselOConfig {\n  items = 3;\n  skip_validateItems = false;\n  loop = false;\n  center = false;\n  rewind = false;\n  mouseDrag = true;\n  touchDrag = true;\n  pullDrag = true;\n  freeDrag = false;\n  margin = 0;\n  stagePadding = 0;\n  merge = false;\n  mergeFit = true;\n  autoWidth = false;\n  startPosition = 0;\n  rtl = false;\n  smartSpeed = 250;\n  fluidSpeed = false;\n  dragEndSpeed = false;\n  responsive = {};\n  responsiveRefreshRate = 200;\n  // defaults to Navigation\n  nav = false;\n  navText = ['prev', 'next'];\n  navSpeed = false;\n  slideBy = 1; // stage moves on 1 width of slide; if slideBy = 2, stage moves on 2 widths of slide\n  dots = true;\n  dotsEach = false;\n  dotsData = false;\n  dotsSpeed = false;\n  // defaults to Autoplay\n  autoplay = false;\n  autoplayTimeout = 5000;\n  autoplayHoverPause = false;\n  autoplaySpeed = false;\n  autoplayMouseleaveTimeout = 1;\n  // defaults to LazyLoading\n  lazyLoad = false;\n  lazyLoadEager = 0;\n  // defaults to Animate\n  slideTransition = '';\n  animateOut = false;\n  animateIn = false;\n  // defaults to AutoHeight\n  autoHeight = false;\n  // defaults to Hash\n  URLhashListener = false;\n  constructor() {}\n}\n/**\n * we can't read types from OwlOptions in javascript because of props have undefined value and types of those props are used for validating inputs\n * class below is copy of OwlOptions but its all props have string value showing certain type;\n * this is class is being used just in method _validateOptions() of CarouselService;\n */\nclass OwlOptionsMockedTypes {\n  items = 'number';\n  skip_validateItems = 'boolean';\n  loop = 'boolean';\n  center = 'boolean';\n  rewind = 'boolean';\n  mouseDrag = 'boolean';\n  touchDrag = 'boolean';\n  pullDrag = 'boolean';\n  freeDrag = 'boolean';\n  margin = 'number';\n  stagePadding = 'number';\n  merge = 'boolean';\n  mergeFit = 'boolean';\n  autoWidth = 'boolean';\n  startPosition = 'number|string';\n  rtl = 'boolean';\n  smartSpeed = 'number';\n  fluidSpeed = 'boolean';\n  dragEndSpeed = 'number|boolean';\n  responsive = {};\n  responsiveRefreshRate = 'number';\n  // defaults to Navigation\n  nav = 'boolean';\n  navText = 'string[]';\n  navSpeed = 'number|boolean';\n  slideBy = 'number|string'; // stage moves on 1 width of slide; if slideBy = 2, stage moves on 2 widths of slide\n  dots = 'boolean';\n  dotsEach = 'number|boolean';\n  dotsData = 'boolean';\n  dotsSpeed = 'number|boolean';\n  // defaults to Autoplay\n  autoplay = 'boolean';\n  autoplayTimeout = 'number';\n  autoplayHoverPause = 'boolean';\n  autoplaySpeed = 'number|boolean';\n  autoplayMouseleaveTimeout = 'number';\n  // defaults to LazyLoading\n  lazyLoad = 'boolean';\n  lazyLoadEager = 'number';\n  // defaults to Animate\n  slideTransition = 'string';\n  animateOut = 'string|boolean';\n  animateIn = 'string|boolean';\n  // defaults to AutoHeight\n  autoHeight = 'boolean';\n  // defaults to Hash\n  URLhashListener = \"boolean\";\n  constructor() {}\n}\nclass OwlLogger {\n  errorHandler;\n  constructor(errorHandler) {\n    this.errorHandler = errorHandler;\n  }\n  log(value, ...rest) {\n    if (isDevMode()) {\n      console.log(value, ...rest);\n    }\n  }\n  error(error) {\n    this.errorHandler.handleError(error);\n  }\n  warn(value, ...rest) {\n    console.warn(value, ...rest);\n  }\n  static ɵfac = function OwlLogger_Factory(t) {\n    return new (t || OwlLogger)(i0.ɵɵinject(i0.ErrorHandler));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OwlLogger,\n    factory: OwlLogger.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OwlLogger, [{\n    type: Injectable\n  }], () => [{\n    type: i0.ErrorHandler\n  }], null);\n})();\n\n/**\n * Current state information and their tags.\n */\nclass States {\n  current;\n  tags;\n}\n/**\n * Enumeration for types.\n * @enum {String}\n */\nvar Type;\n(function (Type) {\n  Type[\"Event\"] = \"event\";\n  Type[\"State\"] = \"state\";\n})(Type || (Type = {}));\n;\n/**\n * Enumeration for width.\n * @enum {String}\n */\nvar Width;\n(function (Width) {\n  Width[\"Default\"] = \"default\";\n  Width[\"Inner\"] = \"inner\";\n  Width[\"Outer\"] = \"outer\";\n})(Width || (Width = {}));\n;\n/**\n * Model for coords of .owl-stage\n */\nclass Coords {\n  x;\n  y;\n}\n/**\n * Model for all current data of carousel\n */\nclass CarouselCurrentData {\n  owlDOMData;\n  stageData;\n  slidesData;\n  navData;\n  dotsData;\n}\nclass CarouselService {\n  logger;\n  /**\n   * Subject for passing data needed for managing View\n   */\n  _viewSettingsShipper$ = new Subject();\n  /**\n   * Subject for notification when the carousel got initializes\n   */\n  _initializedCarousel$ = new Subject();\n  /**\n   * Subject for notification when the carousel's settings start changinf\n   */\n  _changeSettingsCarousel$ = new Subject();\n  /**\n   * Subject for notification when the carousel's settings have changed\n   */\n  _changedSettingsCarousel$ = new Subject();\n  /**\n   * Subject for notification when the carousel starts translating or moving\n   */\n  _translateCarousel$ = new Subject();\n  /**\n   * Subject for notification when the carousel stopped translating or moving\n   */\n  _translatedCarousel$ = new Subject();\n  /**\n   * Subject for notification when the carousel's rebuilding caused by 'resize' event starts\n   */\n  _resizeCarousel$ = new Subject();\n  /**\n   * Subject for notification  when the carousel's rebuilding caused by 'resize' event is ended\n   */\n  _resizedCarousel$ = new Subject();\n  /**\n   * Subject for notification when the refresh of carousel starts\n   */\n  _refreshCarousel$ = new Subject();\n  /**\n   * Subject for notification when the refresh of carousel is ended\n   */\n  _refreshedCarousel$ = new Subject();\n  /**\n   * Subject for notification when the dragging of carousel starts\n   */\n  _dragCarousel$ = new Subject();\n  /**\n   * Subject for notification when the dragging of carousel is ended\n   */\n  _draggedCarousel$ = new Subject();\n  /**\n   * Current settings for the carousel.\n   */\n  settings = {\n    items: 0\n  };\n  /**\n   * Initial data for setting classes to element .owl-carousel\n   */\n  owlDOMData = {\n    rtl: false,\n    isResponsive: false,\n    isRefreshed: false,\n    isLoaded: false,\n    isLoading: false,\n    isMouseDragable: false,\n    isGrab: false,\n    isTouchDragable: false\n  };\n  /**\n   * Initial data of .owl-stage\n   */\n  stageData = {\n    transform: 'translate3d(0px,0px,0px)',\n    transition: '0s',\n    width: 0,\n    paddingL: 0,\n    paddingR: 0\n  };\n  /**\n   *  Data of every slide\n   */\n  slidesData;\n  /**\n   * Data of navigation block\n   */\n  navData;\n  /**\n   * Data of dots block\n   */\n  dotsData;\n  /**\n   * Carousel width\n   */\n  _width;\n  /**\n   * All real items.\n   */\n  _items = []; // is equal to this.slides\n  /**\n   * Array with width of every slide.\n   */\n  _widths = [];\n  /**\n   * Currently suppressed events to prevent them from beeing retriggered.\n   */\n  _supress = {};\n  /**\n   * References to the running plugins of this carousel.\n   */\n  _plugins = {};\n  /**\n   * Absolute current position.\n   */\n  _current = null;\n  /**\n   * All cloned items.\n   */\n  _clones = [];\n  /**\n   * Merge values of all items.\n   * @todo Maybe this could be part of a plugin.\n   */\n  _mergers = [];\n  /**\n   * Animation speed in milliseconds.\n   */\n  _speed = null;\n  /**\n   * Coordinates of all items in pixel.\n   * @todo The name of this member is missleading.\n   */\n  _coordinates = [];\n  /**\n   * Current breakpoint.\n   * @todo Real media queries would be nice.\n   */\n  _breakpoint = null;\n  /**\n   * Prefix for id of cloned slides\n   */\n  clonedIdPrefix = 'cloned-';\n  /**\n   * Current options set by the caller including defaults.\n   */\n  _options = {};\n  /**\n   * Invalidated parts within the update process.\n   */\n  _invalidated = {};\n  // Is needed for tests\n  get invalidated() {\n    return this._invalidated;\n  }\n  /**\n   * Current state information and their tags.\n   */\n  _states = {\n    current: {},\n    tags: {\n      initializing: ['busy'],\n      animating: ['busy'],\n      dragging: ['interacting']\n    }\n  };\n  // is needed for tests\n  get states() {\n    return this._states;\n  }\n  /**\n       * Ordered list of workers for the update process.\n   */\n  _pipe = [\n  // {\n  //   filter: ['width', 'settings'],\n  //   run: () => {\n  //     this._width = this.carouselWindowWidth;\n  //   }\n  // },\n  {\n    filter: ['width', 'items', 'settings'],\n    run: cache => {\n      cache.current = this._items && this._items[this.relative(this._current)]?.id();\n    }\n  },\n  // {\n  //   filter: ['items', 'settings'],\n  //   run: function() {\n  //     // this.$stage.children('.cloned').remove();\n  //   }\n  // },\n  {\n    filter: ['width', 'items', 'settings'],\n    run: cache => {\n      const margin = this.settings.margin || '',\n        grid = !this.settings.autoWidth,\n        rtl = this.settings.rtl,\n        css = {\n          'margin-left': rtl ? margin : '',\n          'margin-right': rtl ? '' : margin\n        };\n      if (!grid) {\n        this.slidesData.forEach(slide => {\n          slide.marginL = css['margin-left'];\n          slide.marginR = css['margin-right'];\n        });\n      }\n      cache.css = css;\n    }\n  }, {\n    filter: ['width', 'items', 'settings'],\n    run: cache => {\n      const width = +(this.width() / (this.settings.items || 1)).toFixed(3) - (this.settings.margin || 0),\n        grid = !this.settings.autoWidth,\n        widths = [];\n      let merge = 0,\n        iterator = this._items.length;\n      cache.items = {\n        merge: false,\n        width: width\n      };\n      while (iterator-- > 0) {\n        merge = this._mergers[iterator] || 1;\n        merge = this.settings.mergeFit && Math.min(merge, this.settings.items || 1) || merge;\n        cache.items.merge = merge > 1 || cache.items.merge;\n        widths[iterator] = !grid ? this._items[iterator].width() ? this._items[iterator].width() : width : width * merge;\n      }\n      this._widths = widths;\n      this.slidesData.forEach((slide, i) => {\n        slide.width = this._widths[i];\n        slide.marginR = cache.css['margin-right'];\n        slide.marginL = cache.css['margin-left'];\n      });\n    }\n  }, {\n    filter: ['items', 'settings'],\n    run: () => {\n      const clones = [],\n        items = this._items,\n        settings = this.settings,\n        // TODO: Should be computed from number of min width items in stage\n        view = Math.max(settings.items * 2, 4),\n        size = Math.ceil(items.length / 2) * 2;\n      let append = [],\n        prepend = [],\n        repeat = settings.loop && items.length ? settings.rewind ? view : Math.max(view, size) : 0;\n      repeat /= 2;\n      while (repeat-- > 0) {\n        // Switch to only using appended clones\n        clones.push(this.normalize(clones.length / 2, true));\n        append.push({\n          ...this.slidesData[clones[clones.length - 1]]\n        });\n        clones.push(this.normalize(items.length - 1 - (clones.length - 1) / 2, true));\n        prepend.unshift({\n          ...this.slidesData[clones[clones.length - 1]]\n        });\n      }\n      this._clones = clones;\n      append = append.map(slide => ({\n        ...slide,\n        id: `${this.clonedIdPrefix}${slide.id}-append`,\n        isActive: false,\n        isCloned: true\n      }));\n      prepend = prepend.map(slide => ({\n        ...slide,\n        id: `${this.clonedIdPrefix}${slide.id}`,\n        isActive: false,\n        isCloned: true\n      }));\n      this.slidesData = prepend.concat(this.slidesData).concat(append);\n    }\n  }, {\n    filter: ['width', 'items', 'settings'],\n    run: () => {\n      const rtl = this.settings.rtl ? 1 : -1,\n        size = this._clones.length + this._items.length,\n        coordinates = [];\n      let iterator = -1,\n        previous = 0,\n        current = 0;\n      while (++iterator < size) {\n        previous = coordinates[iterator - 1] || 0;\n        current = this._widths[this.relative(iterator)] + this.settings.margin;\n        coordinates.push(previous + current * rtl);\n      }\n      this._coordinates = coordinates;\n    }\n  }, {\n    filter: ['width', 'items', 'settings'],\n    run: () => {\n      const padding = this.settings.stagePadding || 0,\n        coordinates = this._coordinates,\n        css = {\n          'width': Math.ceil(Math.abs(coordinates[coordinates.length - 1])) + padding * 2,\n          'padding-left': padding || '',\n          'padding-right': padding || ''\n        };\n      this.stageData.width = css.width; // use this property in *ngIf directive for .owl-stage element\n      this.stageData.paddingL = css['padding-left'];\n      this.stageData.paddingR = css['padding-right'];\n    }\n  }, {\n    //   filter: [ 'width', 'items', 'settings' ],\n    //   run: cache => {\n    // \t\t// this method sets the width for every slide, but I set it in different way earlier\n    // \t\tconst grid = !this.settings.autoWidth,\n    // \t\titems = this.$stage.children(); // use this.slidesData\n    //     let iterator = this._coordinates.length;\n    //     if (grid && cache.items.merge) {\n    //       while (iterator--) {\n    //         cache.css.width = this._widths[this.relative(iterator)];\n    //         items.eq(iterator).css(cache.css);\n    //       }\n    //     } else if (grid) {\n    //       cache.css.width = cache.items.width;\n    //       items.css(cache.css);\n    //     }\n    //   }\n    // }, {\n    //   filter: [ 'items' ],\n    //   run: function() {\n    //     this._coordinates.length < 1 && this.$stage.removeAttr('style');\n    //   }\n    // }, {\n    filter: ['width', 'items', 'settings'],\n    run: cache => {\n      let current = cache.current ? this.slidesData.findIndex(slide => slide.id === cache.current) : 0;\n      current = Math.max(this.minimum(), Math.min(this.maximum(), current));\n      this.reset(current);\n    }\n  }, {\n    filter: ['position'],\n    run: () => {\n      this.animate(this.coordinates(this._current));\n    }\n  }, {\n    filter: ['width', 'position', 'items', 'settings'],\n    run: () => {\n      const rtl = this.settings.rtl ? 1 : -1,\n        padding = (this.settings.stagePadding || 0) * 2,\n        matches = [];\n      let begin, end, inner, outer, i, n;\n      begin = this.coordinates(this.current());\n      if (typeof begin === 'number') {\n        begin += padding;\n      } else {\n        begin = 0;\n      }\n      end = begin + this.width() * rtl;\n      if (rtl === -1 && this.settings.center) {\n        const result = this._coordinates.filter(element => {\n          return (this.settings.items || 1) % 2 === 1 ? element >= begin : element > begin;\n        });\n        begin = result.length ? result[result.length - 1] : begin;\n      }\n      for (i = 0, n = this._coordinates.length; i < n; i++) {\n        inner = Math.ceil(this._coordinates[i - 1] || 0);\n        outer = Math.ceil(Math.abs(this._coordinates[i]) + padding * rtl);\n        if (this._op(inner, '<=', begin) && this._op(inner, '>', end) || this._op(outer, '<', begin) && this._op(outer, '>', end)) {\n          matches.push(i);\n        }\n      }\n      this.slidesData.forEach(slide => {\n        slide.isActive = false;\n        return slide;\n      });\n      matches.forEach(item => {\n        this.slidesData[item].isActive = true;\n      });\n      if (this.settings.center) {\n        this.slidesData.forEach(slide => {\n          slide.isCentered = false;\n          return slide;\n        });\n        if (this.slidesData[this.current()]) {\n          this.slidesData[this.current()].isCentered = true;\n        }\n      }\n    }\n  }];\n  constructor(logger) {\n    this.logger = logger;\n  }\n  /**\n   * Makes _viewSettingsShipper$ Subject become Observable\n   * @returns Observable of _viewSettingsShipper$ Subject\n   */\n  getViewCurSettings() {\n    return this._viewSettingsShipper$.asObservable();\n  }\n  /**\n   * Makes _initializedCarousel$ Subject become Observable\n   * @returns Observable of _initializedCarousel$ Subject\n   */\n  getInitializedState() {\n    return this._initializedCarousel$.asObservable();\n  }\n  /**\n   * Makes _changeSettingsCarousel$ Subject become Observable\n   * @returns Observable of _changeSettingsCarousel$ Subject\n   */\n  getChangeState() {\n    return this._changeSettingsCarousel$.asObservable();\n  }\n  /**\n   * Makes _changedSettingsCarousel$ Subject become Observable\n   * @returns Observable of _changedSettingsCarousel$ Subject\n   */\n  getChangedState() {\n    return this._changedSettingsCarousel$.asObservable();\n  }\n  /**\n   * Makes _translateCarousel$ Subject become Observable\n   * @returns Observable of _translateCarousel$ Subject\n   */\n  getTranslateState() {\n    return this._translateCarousel$.asObservable();\n  }\n  /**\n   * Makes _translatedCarousel$ Subject become Observable\n   * @returns Observable of _translatedCarousel$ Subject\n   */\n  getTranslatedState() {\n    return this._translatedCarousel$.asObservable();\n  }\n  /**\n   * Makes _resizeCarousel$ Subject become Observable\n   * @returns Observable of _resizeCarousel$ Subject\n   */\n  getResizeState() {\n    return this._resizeCarousel$.asObservable();\n  }\n  /**\n   * Makes _resizedCarousel$ Subject become Observable\n   * @returns Observable of _resizedCarousel$ Subject\n   */\n  getResizedState() {\n    return this._resizedCarousel$.asObservable();\n  }\n  /**\n   * Makes _refreshCarousel$ Subject become Observable\n   * @returns Observable of _refreshCarousel$ Subject\n   */\n  getRefreshState() {\n    return this._refreshCarousel$.asObservable();\n  }\n  /**\n   * Makes _refreshedCarousel$ Subject become Observable\n   * @returns Observable of _refreshedCarousel$ Subject\n   */\n  getRefreshedState() {\n    return this._refreshedCarousel$.asObservable();\n  }\n  /**\n   * Makes _dragCarousel$ Subject become Observable\n   * @returns Observable of _dragCarousel$ Subject\n   */\n  getDragState() {\n    return this._dragCarousel$.asObservable();\n  }\n  /**\n   * Makes _draggedCarousel$ Subject become Observable\n   * @returns Observable of _draggedCarousel$ Subject\n   */\n  getDraggedState() {\n    return this._draggedCarousel$.asObservable();\n  }\n  /**\n   * Setups custom options expanding default options\n   * @param options custom options\n   */\n  setOptions(options) {\n    const configOptions = new OwlCarouselOConfig();\n    const checkedOptions = this._validateOptions(options, configOptions);\n    this._options = {\n      ...configOptions,\n      ...checkedOptions\n    };\n  }\n  /**\n   * Checks whether user's option are set properly. Cheking is based on typings;\n   * @param options options set by user\n   * @param configOptions default options\n   * @returns checked and modified (if it's needed) user's options\n   *\n   * Notes:\n   * \t- if user set option with wrong type, it'll be written in console\n   */\n  _validateOptions(options, configOptions) {\n    const checkedOptions = {\n      ...options\n    };\n    const mockedTypes = new OwlOptionsMockedTypes();\n    const setRightOption = (type, key) => {\n      this.logger.log(`options.${key} must be type of ${type}; ${key}=${options[key]} skipped to defaults: ${key}=${configOptions[key]}`);\n      return configOptions[key];\n    };\n    for (const key in checkedOptions) {\n      if (checkedOptions.hasOwnProperty(key)) {\n        // condition could be shortened but it gets harder for understanding\n        if (mockedTypes[key] === 'number') {\n          if (this._isNumeric(checkedOptions[key])) {\n            checkedOptions[key] = +checkedOptions[key];\n            checkedOptions[key] = key === 'items' ? this._validateItems(checkedOptions[key], checkedOptions.skip_validateItems) : checkedOptions[key];\n          } else {\n            checkedOptions[key] = setRightOption(mockedTypes[key], key);\n          }\n        } else if (mockedTypes[key] === 'boolean' && typeof checkedOptions[key] !== 'boolean') {\n          checkedOptions[key] = setRightOption(mockedTypes[key], key);\n        } else if (mockedTypes[key] === 'number|boolean' && !this._isNumberOrBoolean(checkedOptions[key])) {\n          checkedOptions[key] = setRightOption(mockedTypes[key], key);\n        } else if (mockedTypes[key] === 'number|string' && !this._isNumberOrString(checkedOptions[key])) {\n          checkedOptions[key] = setRightOption(mockedTypes[key], key);\n        } else if (mockedTypes[key] === 'string|boolean' && !this._isStringOrBoolean(checkedOptions[key])) {\n          checkedOptions[key] = setRightOption(mockedTypes[key], key);\n        } else if (mockedTypes[key] === 'string[]') {\n          if (Array.isArray(checkedOptions[key])) {\n            let isString = false;\n            checkedOptions[key].forEach(element => {\n              isString = typeof element === 'string' ? true : false;\n            });\n            if (!isString) {\n              checkedOptions[key] = setRightOption(mockedTypes[key], key);\n            }\n            ;\n          } else {\n            checkedOptions[key] = setRightOption(mockedTypes[key], key);\n          }\n        }\n      }\n    }\n    return checkedOptions;\n  }\n  /**\n   * Checks the option `items` set by user and if it bigger than number of slides, the function returns number of slides\n   * @param items option items set by user\n   * @param skip_validateItems option `skip_validateItems` set by user\n   * @returns right number of items\n   */\n  _validateItems(items, skip_validateItems) {\n    let result = items;\n    if (items > this._items.length) {\n      if (skip_validateItems) {\n        this.logger.log('The option \\'items\\' in your options is bigger than the number of slides. The navigation got disabled');\n      } else {\n        result = this._items.length;\n        this.logger.log('The option \\'items\\' in your options is bigger than the number of slides. This option is updated to the current number of slides and the navigation got disabled');\n      }\n    } else {\n      if (items === this._items.length && (this.settings.dots || this.settings.nav)) {\n        this.logger.log('Option \\'items\\' in your options is equal to the number of slides. So the navigation got disabled');\n      }\n    }\n    return result;\n  }\n  /**\n   * Set current width of carousel\n   * @param width width of carousel Window\n   */\n  setCarouselWidth(width) {\n    this._width = width;\n  }\n  /**\n   * Setups the current settings.\n   * @todo Remove responsive classes. Why should adaptive designs be brought into IE8?\n   * @todo Support for media queries by using `matchMedia` would be nice.\n   * @param carouselWidth width of carousel\n   * @param slides array of slides\n   * @param options options set by user\n   */\n  setup(carouselWidth, slides, options) {\n    this.setCarouselWidth(carouselWidth);\n    this.setItems(slides);\n    this._defineSlidesData();\n    this.setOptions(options);\n    this.settings = {\n      ...this._options\n    };\n    this.setOptionsForViewport();\n    this._trigger('change', {\n      property: {\n        name: 'settings',\n        value: this.settings\n      }\n    });\n    this.invalidate('settings'); // must be call of this function;\n    this._trigger('changed', {\n      property: {\n        name: 'settings',\n        value: this.settings\n      }\n    });\n  }\n  /**\n   * Set options for current viewport\n   */\n  setOptionsForViewport() {\n    const viewport = this._width,\n      overwrites = this._options.responsive || {};\n    let match = -1;\n    if (!Object.keys(overwrites).length) {\n      return;\n    }\n    if (!viewport) {\n      this.settings.items = 1;\n      return;\n    }\n    for (const key in overwrites) {\n      if (overwrites.hasOwnProperty(key)) {\n        if (+key <= viewport && +key > match) {\n          match = Number(key);\n        }\n      }\n    }\n    this.settings = {\n      ...this._options,\n      ...overwrites[match],\n      items: overwrites[match] && overwrites[match].items ? this._validateItems(overwrites[match].items, this._options.skip_validateItems) : this._options.items\n    };\n    // if (typeof this.settings.stagePadding === 'function') {\n    // \tthis.settings.stagePadding = this.settings.stagePadding();\n    // }\n    delete this.settings.responsive;\n    this.owlDOMData.isResponsive = true;\n    this.owlDOMData.isMouseDragable = this.settings.mouseDrag;\n    this.owlDOMData.isTouchDragable = this.settings.touchDrag;\n    const mergers = [];\n    this._items.forEach(item => {\n      const mergeN = this.settings.merge ? item.dataMerge() : 1;\n      mergers.push(mergeN);\n    });\n    this._mergers = mergers;\n    this._breakpoint = match;\n    this.invalidate('settings');\n  }\n  /**\n   * Initializes the carousel.\n   * @param slides array of CarouselSlideDirective\n   */\n  initialize(slides) {\n    this.enter('initializing');\n    // this.trigger('initialize');\n    this.owlDOMData.rtl = this.settings.rtl;\n    if (this._mergers.length) {\n      this._mergers = [];\n    }\n    slides.forEach(item => {\n      const mergeN = this.settings.merge ? item.dataMerge() : 1;\n      this._mergers.push(mergeN);\n    });\n    this._clones = [];\n    this.reset(this._isNumeric(this.settings.startPosition) ? +(this.settings?.startPosition || 0) : 0);\n    this.invalidate('items');\n    this.refresh();\n    this.owlDOMData.isLoaded = true;\n    this.owlDOMData.isMouseDragable = this.settings.mouseDrag;\n    this.owlDOMData.isTouchDragable = this.settings.touchDrag;\n    this.sendChanges();\n    this.leave('initializing');\n    this._trigger('initialized');\n  }\n  /**\n   * Sends all data needed for View\n   */\n  sendChanges() {\n    this._viewSettingsShipper$.next({\n      owlDOMData: this.owlDOMData,\n      stageData: this.stageData,\n      slidesData: this.slidesData,\n      navData: this.navData,\n      dotsData: this.dotsData\n    });\n  }\n  /**\n   * Updates option logic if necessery\n   */\n  _optionsLogic() {\n    if (this.settings.autoWidth) {\n      this.settings.stagePadding = 0;\n      this.settings.merge = false;\n    }\n  }\n  /**\n   * Updates the view\n   */\n  update() {\n    let i = 0;\n    const n = this._pipe.length,\n      filter = item => this._invalidated[item],\n      cache = {};\n    while (i < n) {\n      const filteredPipe = this._pipe[i].filter.filter(filter);\n      if (this._invalidated.all || filteredPipe.length > 0) {\n        this._pipe[i].run(cache);\n      }\n      i++;\n    }\n    this.slidesData.forEach(slide => slide.classes = this.setCurSlideClasses(slide));\n    this.sendChanges();\n    this._invalidated = {};\n    if (!this.is('valid')) {\n      this.enter('valid');\n    }\n  }\n  /**\n   * Gets the width of the view.\n   * @param [dimension=Width.Default] The dimension to return\n   * @returns The width of the view in pixel.\n   */\n  width(dimension) {\n    dimension = dimension || Width.Default;\n    switch (dimension) {\n      case Width.Inner:\n      case Width.Outer:\n        return this._width;\n      default:\n        return this._width - (this.settings.stagePadding || 0) * 2 + (this.settings.margin || 0);\n    }\n  }\n  /**\n   * Refreshes the carousel primarily for adaptive purposes.\n   */\n  refresh() {\n    this.enter('refreshing');\n    this._trigger('refresh');\n    this._defineSlidesData();\n    this.setOptionsForViewport();\n    this._optionsLogic();\n    // this.$element.addClass(this.options.refreshClass);\n    this.update();\n    // this.$element.removeClass(this.options.refreshClass);\n    this.leave('refreshing');\n    this._trigger('refreshed');\n  }\n  /**\n   * Checks window `resize` event.\n   * @param curWidth width of .owl-carousel\n   */\n  onResize(curWidth) {\n    if (!this._items.length) {\n      return false;\n    }\n    this.setCarouselWidth(curWidth);\n    this.enter('resizing');\n    // if (this.trigger('resize').isDefaultPrevented()) {\n    // \tthis.leave('resizing');\n    // \treturn false;\n    // }\n    this._trigger('resize');\n    this.invalidate('width');\n    this.refresh();\n    this.leave('resizing');\n    this._trigger('resized');\n  }\n  /**\n   * Prepares data for dragging carousel. It starts after firing `touchstart` and `mousedown` events.\n   * @todo Horizontal swipe threshold as option\n   * @todo #261\n   * @param event - The event arguments.\n   * @returns stage - object with 'x' and 'y' coordinates of .owl-stage\n   */\n  prepareDragging(event) {\n    let stage, transformArr;\n    // could be 5 commented lines below; However there's stage transform in stageData and in updates after each move of stage\n    // stage = getComputedStyle(this.el.nativeElement).transform.replace(/.*\\(|\\)| /g, '').split(',');\n    // stage = {\n    //   x: stage[stage.length === 16 ? 12 : 4],\n    //   y: stage[stage.length === 16 ? 13 : 5]\n    // };\n    transformArr = this.stageData.transform.replace(/.*\\(|\\)| |[^,-\\d]\\w|\\)/g, '').split(',');\n    stage = {\n      x: +transformArr[0],\n      y: +transformArr[1]\n    };\n    if (this.is('animating')) {\n      this.invalidate('position');\n    }\n    if (event.type === 'mousedown') {\n      this.owlDOMData.isGrab = true;\n    }\n    this.speed(0);\n    return stage;\n  }\n  /**\n   * Enters into a 'dragging' state\n   */\n  enterDragging() {\n    this.enter('dragging');\n    this._trigger('drag');\n  }\n  /**\n   * Defines new coords for .owl-stage while dragging it\n   * @todo #261\n   * @param event the event arguments.\n   * @param dragData initial data got after starting dragging\n   * @returns coords or false\n   */\n  defineNewCoordsDrag(event, dragData) {\n    let minimum,\n      maximum,\n      pull = 0;\n    const delta = this.difference(dragData.pointer, this.pointer(event)),\n      stage = this.difference(dragData.stage.start, delta);\n    if (!this.is('dragging')) {\n      return false;\n    }\n    if (this.settings.loop) {\n      minimum = this.coordinates(this.minimum());\n      maximum = +this.coordinates(this.maximum() + 1) - minimum;\n      stage.x = ((stage.x - minimum) % maximum + maximum) % maximum + minimum;\n    } else {\n      minimum = this.settings.rtl ? this.coordinates(this.maximum()) : this.coordinates(this.minimum());\n      maximum = this.settings.rtl ? this.coordinates(this.minimum()) : this.coordinates(this.maximum());\n      pull = this.settings.pullDrag ? -1 * delta.x / 5 : 0;\n      stage.x = Math.max(Math.min(stage.x, minimum + pull), maximum + pull);\n    }\n    return stage;\n  }\n  /**\n   * Finishes dragging of carousel when `touchend` and `mouseup` events fire.\n   * @todo #261\n   * @todo Threshold for click event\n   * @param event the event arguments.\n   * @param dragObj the object with dragging settings and states\n   * @param clickAttacher function which attaches click handler to slide or its children elements in order to prevent event bubling\n   */\n  finishDragging(event, dragObj, clickAttacher) {\n    const directions = ['right', 'left'],\n      delta = this.difference(dragObj.pointer, this.pointer(event)),\n      stage = dragObj.stage.current,\n      direction = directions[+(this.settings.rtl ? delta.x < +this.settings.rtl : delta.x > +(this.settings.rtl || 0))];\n    let currentSlideI, current, newCurrent;\n    if (delta.x !== 0 && this.is('dragging') || !this.is('valid')) {\n      this.speed(+(this.settings.dragEndSpeed || 0) || this.settings.smartSpeed);\n      currentSlideI = this.closest(stage.x, delta.x !== 0 ? direction : dragObj.direction);\n      current = this.current();\n      newCurrent = this.current(currentSlideI === -1 ? undefined : currentSlideI);\n      if (current !== newCurrent) {\n        this.invalidate('position');\n        this.update();\n      }\n      dragObj.direction = direction;\n      if (Math.abs(delta.x) > 3 || new Date().getTime() - dragObj.time > 300) {\n        clickAttacher();\n      }\n    }\n    if (!this.is('dragging')) {\n      return;\n    }\n    this.leave('dragging');\n    this._trigger('dragged');\n  }\n  /**\n   * Gets absolute position of the closest item for a coordinate.\n   * @todo Setting `freeDrag` makes `closest` not reusable. See #165.\n   * @param coordinate The coordinate in pixel.\n   * @param direction The direction to check for the closest item. Ether `left` or `right`.\n   * @returns The absolute position of the closest item.\n   */\n  closest(coordinate, direction) {\n    const pull = 30,\n      width = this.width();\n    let coordinates = this.coordinates(),\n      position = -1;\n    if (this.settings.center) {\n      coordinates = coordinates.map(item => {\n        if (item === 0) {\n          item += 0.000001;\n        }\n        return item;\n      });\n    }\n    // option 'freeDrag' doesn't have realization and using it here creates problem:\n    // variable 'position' stays unchanged (it equals -1 at the begging) and thus method returns -1\n    // Returning value is consumed by method current(), which taking -1 as argument calculates the index of new current slide\n    // In case of having 5 slides ans 'loop=false; calling 'current(-1)' sets props '_current' as 4. Just last slide remains visible instead of 3 last slides.\n    // if (!this.settings.freeDrag) {\n    // check closest item\n    for (let i = 0; i < coordinates.length; i++) {\n      if (direction === 'left' && coordinate > coordinates[i] - pull && coordinate < coordinates[i] + pull) {\n        position = i;\n        // on a right pull, check on previous index\n        // to do so, subtract width from value and set position = index + 1\n      } else if (direction === 'right' && coordinate > coordinates[i] - width - pull && coordinate < coordinates[i] - width + pull) {\n        position = i + 1;\n      } else if (this._op(coordinate, '<', coordinates[i]) && this._op(coordinate, '>', coordinates[i + 1] || coordinates[i] - width)) {\n        position = direction === 'left' ? i + 1 : i;\n      } else if (direction === null && coordinate > coordinates[i] - pull && coordinate < coordinates[i] + pull) {\n        position = i;\n      }\n      if (position !== -1) {\n        break;\n      }\n      ;\n    }\n    // }\n    if (!this.settings.loop) {\n      // non loop boundries\n      if (this._op(coordinate, '>', coordinates[this.minimum()])) {\n        position = coordinate = this.minimum();\n      } else if (this._op(coordinate, '<', coordinates[this.maximum()])) {\n        position = coordinate = this.maximum();\n      }\n    }\n    return position;\n  }\n  /**\n   * Animates the stage.\n   * @todo #270\n   * @param coordinate The coordinate in pixels.\n   */\n  animate(coordinate) {\n    const animate = this.speed() > 0;\n    if (this.is('animating')) {\n      this.onTransitionEnd();\n    }\n    if (animate) {\n      this.enter('animating');\n      this._trigger('translate');\n    }\n    this.stageData.transform = 'translate3d(' + coordinate + 'px,0px,0px)';\n    this.stageData.transition = this.speed() / 1000 + 's' + (this.settings.slideTransition ? ' ' + this.settings.slideTransition : '');\n    // also there was transition by means of JQuery.animate or css-changing property left\n  }\n  /**\n   * Checks whether the carousel is in a specific state or not.\n   * @param state The state to check.\n   * @returns The flag which indicates if the carousel is busy.\n   */\n  is(state) {\n    return this._states.current[state] && this._states.current[state] > 0;\n  }\n  /**\n   * Sets the absolute position of the current item.\n   * @param position The new absolute position or nothing to leave it unchanged.\n   * @returns The absolute position of the current item.\n   */\n  current(position) {\n    if (position === undefined) {\n      return this._current;\n    }\n    if (this._items.length === 0) {\n      return undefined;\n    }\n    position = this.normalize(position);\n    if (this._current !== position) {\n      const event = this._trigger('change', {\n        property: {\n          name: 'position',\n          value: position\n        }\n      });\n      // if (event.data !== undefined) {\n      // \tposition = this.normalize(event.data);\n      // }\n      this._current = position;\n      this.invalidate('position');\n      this._trigger('changed', {\n        property: {\n          name: 'position',\n          value: this._current\n        }\n      });\n    }\n    return this._current;\n  }\n  /**\n   * Invalidates the given part of the update routine.\n   * @param part The part to invalidate.\n   * @returns The invalidated parts.\n   */\n  invalidate(part) {\n    if (typeof part === 'string') {\n      this._invalidated[part] = true;\n      if (this.is('valid')) {\n        this.leave('valid');\n      }\n    }\n    return Object.keys(this._invalidated);\n  }\n  /**\n   * Resets the absolute position of the current item.\n   * @param position the absolute position of the new item.\n   */\n  reset(position) {\n    position = this.normalize(position);\n    if (position === undefined) {\n      return;\n    }\n    this._speed = 0;\n    this._current = position;\n    this._suppress(['translate', 'translated']);\n    this.animate(this.coordinates(position));\n    this._release(['translate', 'translated']);\n  }\n  /**\n   * Normalizes an absolute or a relative position of an item.\n   * @param position The absolute or relative position to normalize.\n   * @param relative Whether the given position is relative or not.\n   * @returns The normalized position.\n   */\n  normalize(position, relative) {\n    const n = this._items.length,\n      m = relative ? 0 : this._clones.length;\n    let result = position;\n    if (!this._isNumeric(position) || n < 1) {\n      result = undefined;\n    } else if (position < 0 || position >= n + m) {\n      result = ((position - m / 2) % n + n) % n + m / 2;\n    }\n    return result;\n  }\n  /**\n   * Converts an absolute position of an item into a relative one.\n   * @param position The absolute position to convert.\n   * @returns The converted position.\n   */\n  relative(position) {\n    position -= this._clones.length / 2;\n    return this.normalize(position, true);\n  }\n  /**\n   * Gets the maximum position for the current item.\n   * @param relative Whether to return an absolute position or a relative position.\n   * @returns number of maximum position\n   */\n  maximum(relative = false) {\n    const settings = this.settings;\n    let maximum = this._coordinates.length,\n      iterator,\n      reciprocalItemsWidth,\n      elementWidth;\n    if (settings.loop) {\n      maximum = this._clones.length / 2 + this._items.length - 1;\n    } else if (settings.autoWidth || settings.merge) {\n      iterator = this._items.length;\n      reciprocalItemsWidth = this.slidesData[--iterator].width;\n      elementWidth = this._width;\n      while (iterator-- > 0) {\n        // it could be use this._items instead of this.slidesData;\n        reciprocalItemsWidth += +(this.slidesData[iterator].width || 0) + (this.settings.margin || 0);\n        if (reciprocalItemsWidth > elementWidth) {\n          break;\n        }\n      }\n      maximum = iterator + 1;\n    } else if (settings.center) {\n      maximum = this._items.length - 1;\n    } else {\n      maximum = this._items.length - (settings.items || 1);\n    }\n    if (relative) {\n      maximum -= this._clones.length / 2;\n    }\n    return Math.max(maximum, 0);\n  }\n  /**\n   * Gets the minimum position for the current item.\n   * @param relative Whether to return an absolute position or a relative position.\n   * @returns number of minimum position\n   */\n  minimum(relative = false) {\n    return relative ? 0 : this._clones.length / 2;\n  }\n  /**\n   * Gets an item at the specified relative position.\n   * @param position The relative position of the item.\n   * @returns The item at the given position or all items if no position was given.\n   */\n  items(position) {\n    if (position === undefined) {\n      return this._items.slice();\n    }\n    position = this.normalize(position, true);\n    return [this._items[position]];\n  }\n  /**\n   * Gets an item at the specified relative position.\n   * @param position The relative position of the item.\n   * @returns The item at the given position or all items if no position was given.\n   */\n  mergers(position) {\n    if (position === undefined) {\n      return this._mergers.slice();\n    }\n    position = this.normalize(position, true);\n    return this._mergers[position];\n  }\n  /**\n   * Gets the absolute positions of clones for an item.\n   * @param position The relative position of the item.\n   * @returns The absolute positions of clones for the item or all if no position was given.\n   */\n  clones(position) {\n    const odd = this._clones.length / 2,\n      even = odd + this._items.length,\n      map = index => index % 2 === 0 ? even + index / 2 : odd - (index + 1) / 2;\n    if (position === undefined) {\n      return this._clones.map((v, i) => map(i));\n    }\n    return this._clones.map((v, i) => v === position ? map(i) : null).filter(item => item !== null);\n  }\n  /**\n   * Sets the current animation speed.\n   * @param speed The animation speed in milliseconds or nothing to leave it unchanged.\n   * @returns The current animation speed in milliseconds.\n   */\n  speed(speed) {\n    if (speed !== undefined) {\n      this._speed = speed;\n    }\n    return this._speed;\n  }\n  /**\n   * Gets the coordinate of an item.\n   * @todo The name of this method is missleanding.\n   * @param position The absolute position of the item within `minimum()` and `maximum()`.\n   * @returns The coordinate of the item in pixel or all coordinates.\n   */\n  coordinates(position) {\n    let multiplier = 1,\n      newPosition = (position || 0) - 1,\n      coordinate,\n      result;\n    if (position === undefined) {\n      result = this._coordinates.map((item, index) => {\n        return this.coordinates(index);\n      });\n      return result;\n    }\n    if (this.settings.center) {\n      if (this.settings.rtl) {\n        multiplier = -1;\n        newPosition = position + 1;\n      }\n      coordinate = this._coordinates[position];\n      coordinate += (this.width() - coordinate + (this._coordinates[newPosition] || 0)) / 2 * multiplier;\n    } else {\n      coordinate = this._coordinates[newPosition] || 0;\n    }\n    coordinate = Math.ceil(coordinate);\n    return coordinate;\n  }\n  /**\n   * Calculates the speed for a translation.\n   * @param from The absolute position of the start item.\n   * @param to The absolute position of the target item.\n   * @param factor [factor=undefined] - The time factor in milliseconds.\n   * @returns The time in milliseconds for the translation.\n   */\n  _duration(from, to, factor) {\n    if (factor === 0) {\n      return 0;\n    }\n    return Math.min(Math.max(Math.abs(to - from), 1), 6) * Math.abs(+(factor || 0) || this.settings.smartSpeed || 0);\n  }\n  /**\n   * Slides to the specified item.\n   * @param position The position of the item.\n   * @param speed The time in milliseconds for the transition.\n   */\n  to(position, speed) {\n    let current = this.current(),\n      revert,\n      distance = position - this.relative(current),\n      maximum = this.maximum(),\n      delayForLoop = 0;\n    const direction = +(distance > 0) - +(distance < 0),\n      items = this._items.length,\n      minimum = this.minimum();\n    if (this.settings.loop) {\n      if (!this.settings.rewind && Math.abs(distance) > items / 2) {\n        distance += direction * -1 * items;\n      }\n      position = current + distance;\n      revert = ((position - minimum) % items + items) % items + minimum;\n      if (revert !== position && revert - distance <= maximum && revert - distance > 0) {\n        current = revert - distance;\n        position = revert;\n        delayForLoop = 30;\n        this.reset(current);\n        this.sendChanges();\n      }\n    } else if (this.settings.rewind) {\n      maximum += 1;\n      position = (position % maximum + maximum) % maximum;\n    } else {\n      position = Math.max(minimum, Math.min(maximum, position));\n    }\n    setTimeout(() => {\n      this.speed(this._duration(current, position, speed));\n      this.current(position);\n      this.update();\n    }, delayForLoop);\n  }\n  /**\n   * Slides to the next item.\n   * @param speed The time in milliseconds for the transition.\n   */\n  next(speed) {\n    speed = speed || false;\n    this.to(this.relative(this.current()) + 1, speed);\n  }\n  /**\n   * Slides to the previous item.\n   * @param speed The time in milliseconds for the transition.\n   */\n  prev(speed) {\n    speed = speed || false;\n    this.to(this.relative(this.current()) - 1, speed);\n  }\n  /**\n   * Handles the end of an animation.\n   * @param event - The event arguments.\n   */\n  onTransitionEnd(event) {\n    // if css2 animation then event object is undefined\n    if (event !== undefined) {\n      // event.stopPropagation();\n      // // Catch only owl-stage transitionEnd event\n      // if ((event.target || event.srcElement || event.originalTarget) !== this.$stage.get(0)\t) {\n      // \treturn false;\n      // }\n      return false;\n    }\n    this.leave('animating');\n    this._trigger('translated');\n  }\n  /**\n   * Gets viewport width.\n   * @returns - The width in pixel.\n   */\n  _viewport() {\n    let width;\n    if (this._width) {\n      width = this._width;\n    } else {\n      this.logger.log('Can not detect viewport width.');\n    }\n    return width;\n  }\n  /**\n   * Sets _items\n   * @param content The list of slides put into CarouselSlideDirectives.\n   */\n  setItems(content) {\n    this._items = content;\n  }\n  /**\n   * Sets slidesData using this._items\n   */\n  _defineSlidesData() {\n    // Maybe creating and using loadMap would be better in LazyLoadService.\n    // Hovewer in that case when 'resize' event fires, prop 'load' of all slides will get 'false' and such state of prop will be seen by View during its updating. Accordingly the code will remove slides's content from DOM even if it was loaded before.\n    // Thus it would be needed to add that content into DOM again.\n    // In order to avoid additional removing/adding loaded slides's content we use loadMap here and set restore state of prop 'load' before the View will get it.\n    let loadMap;\n    if (this.slidesData && this.slidesData.length) {\n      loadMap = new Map();\n      this.slidesData.forEach(item => {\n        if (item.load) {\n          loadMap.set(item.id, item.load);\n        }\n      });\n    }\n    this.slidesData = this._items.map(slide => {\n      return {\n        id: `${slide.id()}`,\n        isActive: false,\n        tplRef: slide.tplRef,\n        dataMerge: slide.dataMerge(),\n        width: 0,\n        isCloned: false,\n        load: loadMap ? loadMap.get(slide.id()) : false,\n        hashFragment: slide.dataHash()\n      };\n    });\n  }\n  /**\n   * Sets current classes for slide\n   * @param slide Slide of carousel\n   * @returns object with names of css-classes which are keys and true/false values\n   */\n  setCurSlideClasses(slide) {\n    // CSS classes: added/removed per current state of component properties\n    const currentClasses = {\n      'active': slide.isActive || false,\n      'center': slide.isCentered || false,\n      'cloned': slide.isCloned || false,\n      'animated': slide.isAnimated || false,\n      'owl-animated-in': slide.isDefAnimatedIn || false,\n      'owl-animated-out': slide.isDefAnimatedOut || false\n    };\n    if (this.settings.animateIn) {\n      currentClasses[this.settings.animateIn] = slide.isCustomAnimatedIn || false;\n    }\n    if (this.settings.animateOut) {\n      currentClasses[this.settings.animateOut] = slide.isCustomAnimatedOut || false;\n    }\n    return currentClasses;\n  }\n  /**\n   * Operators to calculate right-to-left and left-to-right.\n   * @param a - The left side operand.\n   * @param o - The operator.\n   * @param b - The right side operand.\n   * @returns true/false meaning right-to-left or left-to-right\n   */\n  _op(a, o, b) {\n    const rtl = this.settings.rtl;\n    switch (o) {\n      case '<':\n        return rtl ? a > b : a < b;\n      case '>':\n        return rtl ? a < b : a > b;\n      case '>=':\n        return rtl ? a <= b : a >= b;\n      case '<=':\n        return rtl ? a >= b : a <= b;\n      default:\n        break;\n    }\n    return false;\n  }\n  /**\n   * Triggers a public event.\n   * @todo Remove `status`, `relatedTarget` should be used instead.\n   * @param name The event name.\n   * @param data The event data.\n   * @param namespace The event namespace.\n   * @param state The state which is associated with the event.\n   * @param enter Indicates if the call enters the specified state or not.\n   */\n  _trigger(name, data, namespace, state, enter) {\n    switch (name) {\n      case 'initialized':\n        this._initializedCarousel$.next(name);\n        break;\n      case 'change':\n        this._changeSettingsCarousel$.next(data);\n        break;\n      case 'changed':\n        this._changedSettingsCarousel$.next(data);\n        break;\n      case 'drag':\n        this._dragCarousel$.next(name);\n        break;\n      case 'dragged':\n        this._draggedCarousel$.next(name);\n        break;\n      case 'resize':\n        this._resizeCarousel$.next(name);\n        break;\n      case 'resized':\n        this._resizedCarousel$.next(name);\n        break;\n      case 'refresh':\n        this._refreshCarousel$.next(name);\n        break;\n      case 'refreshed':\n        this._refreshedCarousel$.next(name);\n        break;\n      case 'translate':\n        this._translateCarousel$.next(name);\n        break;\n      case 'translated':\n        this._translatedCarousel$.next(name);\n        break;\n      default:\n        break;\n    }\n  }\n  /**\n   * Enters a state.\n   * @param name - The state name.\n   */\n  enter(name) {\n    [name].concat(this._states.tags[name] || []).forEach(stateName => {\n      if (this._states.current[stateName] === undefined) {\n        this._states.current[stateName] = 0;\n      }\n      this._states.current[stateName]++;\n    });\n  }\n  /**\n   * Leaves a state.\n   * @param name - The state name.\n   */\n  leave(name) {\n    [name].concat(this._states.tags[name] || []).forEach(stateName => {\n      if (this._states.current[stateName] === 0 || !!this._states.current[stateName]) {\n        this._states.current[stateName]--;\n      }\n    });\n  }\n  /**\n   * Registers an event or state.\n   * @param object - The event or state to register.\n   */\n  register(object) {\n    if (object.type === Type.State) {\n      if (!this._states.tags[object.name]) {\n        this._states.tags[object.name] = object.tags;\n      } else {\n        this._states.tags[object.name] = this._states.tags[object.name].concat(object.tags);\n      }\n      this._states.tags[object.name] = this._states.tags[object.name].filter((tag, i) => {\n        return this._states.tags[object.name].indexOf(tag) === i;\n      });\n    }\n  }\n  /**\n   * Suppresses events.\n   * @param events The events to suppress.\n   */\n  _suppress(events) {\n    events.forEach(event => {\n      this._supress[event] = true;\n    });\n  }\n  /**\n   * Releases suppressed events.\n   * @param events The events to release.\n   */\n  _release(events) {\n    events.forEach(event => {\n      delete this._supress[event];\n    });\n  }\n  /**\n   * Gets unified pointer coordinates from event.\n   * @todo #261\n   * @param event The `mousedown` or `touchstart` event.\n   * @returns Object Coords which contains `x` and `y` coordinates of current pointer position.\n   */\n  pointer(event) {\n    const result = {\n      x: 0,\n      y: 0\n    };\n    event = event.originalEvent || event || window.event;\n    event = event.touches && event.touches.length ? event.touches[0] : event.changedTouches && event.changedTouches.length ? event.changedTouches[0] : event;\n    if (event.pageX) {\n      result.x = event.pageX;\n      result.y = event.pageY;\n    } else {\n      result.x = event.clientX;\n      result.y = event.clientY;\n    }\n    return result;\n  }\n  /**\n   * Determines if the input is a Number or something that can be coerced to a Number\n   * @param number The input to be tested\n   * @returns An indication if the input is a Number or can be coerced to a Number\n   */\n  _isNumeric(number) {\n    return !isNaN(parseFloat(number));\n  }\n  /**\n   * Determines whether value is number or boolean type\n   * @param value The input to be tested\n   * @returns An indication if the input is a Number or can be coerced to a Number, or Boolean\n   */\n  _isNumberOrBoolean(value) {\n    return this._isNumeric(value) || typeof value === 'boolean';\n  }\n  /**\n   * Determines whether value is number or string type\n   * @param value The input to be tested\n   * @returns An indication if the input is a Number or can be coerced to a Number, or String\n   */\n  _isNumberOrString(value) {\n    return this._isNumeric(value) || typeof value === 'string';\n  }\n  /**\n   * Determines whether value is number or string type\n   * @param value The input to be tested\n   * @returns An indication if the input is a Number or can be coerced to a Number, or String\n   */\n  _isStringOrBoolean(value) {\n    return typeof value === 'string' || typeof value === 'boolean';\n  }\n  /**\n   * Gets the difference of two vectors.\n   * @todo #261\n   * @param first The first vector.\n   * @param second The second vector.\n   * @returns The difference.\n   */\n  difference(first, second) {\n    if (null === first || null === second) {\n      return {\n        x: 0,\n        y: 0\n      };\n    }\n    return {\n      x: first.x - second.x,\n      y: first.y - second.y\n    };\n  }\n  static ɵfac = function CarouselService_Factory(t) {\n    return new (t || CarouselService)(i0.ɵɵinject(OwlLogger));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CarouselService,\n    factory: CarouselService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselService, [{\n    type: Injectable\n  }], () => [{\n    type: OwlLogger\n  }], null);\n})();\nclass NavigationService {\n  carouselService;\n  /**\n   * Subscrioption to merge Observable  from CarouselService\n   */\n  navSubscription;\n  /**\n   * Indicates whether the plugin is initialized or not.\n   */\n  _initialized = false;\n  /**\n   * The current paging indexes.\n   */\n  _pages = [];\n  /**\n   * Data for navigation elements of the user interface.\n   */\n  _navData = {\n    disabled: false,\n    prev: {\n      disabled: false,\n      htmlText: ''\n    },\n    next: {\n      disabled: false,\n      htmlText: ''\n    }\n  };\n  /**\n   * Data for dot elements of the user interface.\n   */\n  _dotsData = {\n    disabled: false,\n    dots: []\n  };\n  constructor(carouselService) {\n    this.carouselService = carouselService;\n    this.spyDataStreams();\n  }\n  ngOnDestroy() {\n    this.navSubscription.unsubscribe();\n  }\n  /**\n   * Defines Observables which service must observe\n   */\n  spyDataStreams() {\n    const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(state => {\n      this.initialize();\n      this._updateNavPages();\n      this.draw();\n      this.update();\n      this.carouselService.sendChanges();\n    }));\n    // mostly changes in carouselService and carousel at all causes carouselService.to(). It moves stage right-left by its code and calling needed functions\n    // Thus this method by calling carouselService.current(position) notifies about changes\n    const changedSettings$ = this.carouselService.getChangedState().pipe(filter(data => data.property.name === 'position'), tap(data => {\n      this.update();\n      // should be the call of the function written at the end of comment\n      // but the method carouselServive.to() has setTimeout(f, 0) which contains carouselServive.update() which calls sendChanges() method.\n      // carouselService.navData and carouselService.dotsData update earlier than carouselServive.update() gets called\n      // updates of carouselService.navData and carouselService.dotsData are being happening withing carouselService.current(position) method which calls next() of _changedSettingsCarousel$\n      // carouselService.current(position) is being calling earlier than carouselServive.update();\n      // this.carouselService.sendChanges();\n    }));\n    const refreshedCarousel$ = this.carouselService.getRefreshedState().pipe(tap(() => {\n      this._updateNavPages();\n      this.draw();\n      this.update();\n      this.carouselService.sendChanges();\n    }));\n    const navMerge$ = merge(initializedCarousel$, changedSettings$, refreshedCarousel$);\n    this.navSubscription = navMerge$.subscribe(() => {});\n  }\n  /**\n   * Initializes the layout of the plugin and extends the carousel.\n   */\n  initialize() {\n    this._navData.disabled = true;\n    this._navData.prev.htmlText = this.carouselService.settings.navText[0];\n    this._navData.next.htmlText = this.carouselService.settings.navText[1];\n    this._dotsData.disabled = true;\n    this.carouselService.navData = this._navData;\n    this.carouselService.dotsData = this._dotsData;\n  }\n  /**\n   * Calculates internal states and updates prop _pages\n   */\n  _updateNavPages() {\n    let i, j, k;\n    const lower = this.carouselService.clones().length / 2,\n      upper = lower + this.carouselService.items().length,\n      maximum = this.carouselService.maximum(true),\n      pages = [],\n      settings = this.carouselService.settings;\n    let size = settings.center || settings.autoWidth || settings.dotsData ? 1 : Math.floor(Number(settings.dotsEach)) || Math.floor(settings.items);\n    size = +size;\n    if (settings.slideBy !== 'page') {\n      settings.slideBy = Math.min(+settings.slideBy, settings.items);\n    }\n    if (settings.dots || settings.slideBy === 'page') {\n      for (i = lower, j = 0, k = 0; i < upper; i++) {\n        if (j >= size || j === 0) {\n          pages.push({\n            start: Math.min(maximum, i - lower),\n            end: i - lower + size - 1\n          });\n          if (Math.min(maximum, i - lower) === maximum) {\n            break;\n          }\n          j = 0, ++k;\n        }\n        j += this.carouselService.mergers(this.carouselService.relative(i));\n      }\n    }\n    this._pages = pages;\n  }\n  /**\n   * Draws the user interface.\n   * @todo The option `dotsData` wont work.\n   */\n  draw() {\n    let difference;\n    const settings = this.carouselService.settings,\n      items = this.carouselService.items(),\n      disabled = items.length <= settings.items;\n    this._navData.disabled = !settings.nav || disabled;\n    this._dotsData.disabled = !settings.dots || disabled;\n    if (settings.dots) {\n      difference = this._pages.length - this._dotsData.dots.length;\n      if (settings.dotsData && difference !== 0) {\n        this._dotsData.dots = [];\n        items.forEach(item => {\n          this._dotsData.dots.push({\n            active: false,\n            id: `dot-${item.id}`,\n            innerContent: item.dotContent(),\n            showInnerContent: true\n          });\n        });\n      } else if (difference > 0) {\n        const startI = this._dotsData.dots.length > 0 ? this._dotsData.dots.length : 0;\n        for (let i = 0; i < difference; i++) {\n          this._dotsData.dots.push({\n            active: false,\n            id: `dot-${i + startI}`,\n            innerContent: '',\n            showInnerContent: false\n          });\n        }\n      } else if (difference < 0) {\n        this._dotsData.dots.splice(difference, Math.abs(difference));\n      }\n    }\n    this.carouselService.navData = this._navData;\n    this.carouselService.dotsData = this._dotsData;\n  }\n  /**\n   * Updates navigation buttons's and dots's states\n   */\n  update() {\n    this._updateNavButtons();\n    this._updateDots();\n  }\n  /**\n   * Changes state of nav buttons (disabled, enabled)\n   */\n  _updateNavButtons() {\n    const settings = this.carouselService.settings,\n      loop = settings.loop || settings.rewind,\n      index = this.carouselService.relative(this.carouselService.current());\n    if (settings.nav) {\n      this._navData.prev.disabled = !loop && index <= this.carouselService.minimum(true);\n      this._navData.next.disabled = !loop && index >= this.carouselService.maximum(true);\n    }\n    this.carouselService.navData = this._navData;\n  }\n  /**\n   * Changes active dot if page becomes changed\n   */\n  _updateDots() {\n    let curActiveDotI;\n    if (!this.carouselService.settings.dots) {\n      return;\n    }\n    this._dotsData.dots.forEach(item => {\n      if (item.active === true) {\n        item.active = false;\n      }\n    });\n    curActiveDotI = this._current();\n    if (this._dotsData.dots.length) {\n      this._dotsData.dots[curActiveDotI].active = true;\n    }\n    this.carouselService.dotsData = this._dotsData;\n  }\n  /**\n   * Gets the current page position of the carousel.\n   * @returns the current page position of the carousel\n   */\n  _current() {\n    const current = this.carouselService.relative(this.carouselService.current());\n    let finalCurrent;\n    const pages = this._pages.filter((page, index) => {\n      return page.start <= current && page.end >= current;\n    }).pop();\n    finalCurrent = this._pages.findIndex(page => {\n      return page.start === pages.start && page.end === pages.end;\n    });\n    return finalCurrent;\n  }\n  /**\n   * Gets the current succesor/predecessor position.\n   * @param sussessor position of slide\n   * @returns the current succesor/predecessor position\n   */\n  _getPosition(successor) {\n    let position, length;\n    const settings = this.carouselService.settings;\n    if (settings.slideBy === 'page') {\n      position = this._current();\n      length = this._pages.length;\n      successor ? ++position : --position;\n      position = this._pages[(position % length + length) % length].start;\n    } else {\n      position = this.carouselService.relative(this.carouselService.current());\n      length = this.carouselService.items().length;\n      successor ? position += +settings.slideBy : position -= +settings.slideBy;\n    }\n    return position;\n  }\n  /**\n   * Slides to the next item or page.\n   * @param speed The time in milliseconds for the transition.\n   */\n  next(speed) {\n    this.carouselService.to(this._getPosition(true), speed);\n  }\n  /**\n   * Slides to the previous item or page.\n   * @param speed The time in milliseconds for the transition.\n   */\n  prev(speed) {\n    this.carouselService.to(this._getPosition(false), speed);\n  }\n  /**\n  * Slides to the specified item or page.\n  * @param position - The position of the item or page.\n  * @param speed - The time in milliseconds for the transition.\n  * @param standard - Whether to use the standard behaviour or not. Default meaning false\n  */\n  to(position, speed, standard) {\n    let length;\n    if (!standard && this._pages.length) {\n      length = this._pages.length;\n      this.carouselService.to(this._pages[(position % length + length) % length].start, speed);\n    } else {\n      this.carouselService.to(position, speed);\n    }\n  }\n  /**\n   * Moves carousel after user's clicking on any dots\n   */\n  moveByDot(dotId) {\n    const index = this._dotsData.dots.findIndex(dot => dotId === dot.id);\n    this.to(index, this.carouselService.settings.dotsSpeed);\n  }\n  /**\n   * rewinds carousel to slide with needed id\n   * @param id id of slide\n   */\n  toSlideById(id) {\n    const position = this.carouselService.slidesData.findIndex(slide => slide.id === id && slide.isCloned === false);\n    if (position === -1 || position === this.carouselService.current()) {\n      return;\n    }\n    this.carouselService.to(this.carouselService.relative(position), false);\n  }\n  static ɵfac = function NavigationService_Factory(t) {\n    return new (t || NavigationService)(i0.ɵɵinject(CarouselService));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NavigationService,\n    factory: NavigationService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavigationService, [{\n    type: Injectable\n  }], () => [{\n    type: CarouselService\n  }], null);\n})();\n\n// import { Injectable } from '@angular/core';\n// function _window(): any {\n//    // return the global native browser window object\n//    return window;\n// }\n// @Injectable()\n// export class WindowRefService {\n//    get nativeWindow(): any {\n//       return _window();\n//    }\n// }\n/**\n * Create a new injection token for injecting the window into a component.\n */\nconst WINDOW = new InjectionToken('WindowToken');\n/**\n * Define abstract class for obtaining reference to the global window object.\n */\nclass WindowRef {\n  get nativeWindow() {\n    throw new Error('Not implemented.');\n  }\n}\n/**\n * Define class that implements the abstract class and returns the native window object.\n */\nclass BrowserWindowRef extends WindowRef {\n  constructor() {\n    super();\n  }\n  /**\n   * @returns window object\n   */\n  get nativeWindow() {\n    return window;\n  }\n  static ɵfac = function BrowserWindowRef_Factory(t) {\n    return new (t || BrowserWindowRef)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserWindowRef,\n    factory: BrowserWindowRef.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserWindowRef, [{\n    type: Injectable\n  }], () => [], null);\n})();\n/**\n * Create an factory function that returns the native window object.\n * @param browserWindowRef Native window object\n * @param platformId id of platform\n * @returns type of platform of empty object\n */\nfunction windowFactory(browserWindowRef, platformId) {\n  if (isPlatformBrowser(platformId)) {\n    return browserWindowRef.nativeWindow;\n  }\n  const obj = {\n    setTimeout: (func, time) => {},\n    clearTimeout: a => {}\n  };\n  return obj;\n}\n/**\n * Create a injectable provider for the WindowRef token that uses the BrowserWindowRef class.\n */\nconst browserWindowProvider = {\n  provide: WindowRef,\n  useClass: BrowserWindowRef\n};\n/**\n * Create an injectable provider that uses the windowFactory function for returning the native window object.\n */\nconst windowProvider = {\n  provide: WINDOW,\n  useFactory: windowFactory,\n  deps: [WindowRef, PLATFORM_ID]\n};\n/**\n * Create an array of providers.\n */\nconst WINDOW_PROVIDERS = [browserWindowProvider, windowProvider];\n\n/**\n * Create a new injection token for injecting the Document into a component.\n */\nconst DOCUMENT = new InjectionToken('DocumentToken');\n/**\n * Define abstract class for obtaining reference to the global Document object.\n */\nclass DocumentRef {\n  get nativeDocument() {\n    throw new Error('Not implemented.');\n  }\n}\n/**\n * Define class that implements the abstract class and returns the native Document object.\n */\nclass BrowserDocumentRef extends DocumentRef {\n  constructor() {\n    super();\n  }\n  /**\n   * @returns Document object\n   */\n  get nativeDocument() {\n    return document;\n  }\n  static ɵfac = function BrowserDocumentRef_Factory(t) {\n    return new (t || BrowserDocumentRef)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserDocumentRef,\n    factory: BrowserDocumentRef.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserDocumentRef, [{\n    type: Injectable\n  }], () => [], null);\n})();\n/**\n * Create an factory function that returns the native Document object.\n * @param browserDocumentRef Native Document object\n * @param platformId id of platform\n * @returns type of platform of empty object\n */\nfunction documentFactory(browserDocumentRef, platformId) {\n  if (isPlatformBrowser(platformId)) {\n    return browserDocumentRef.nativeDocument;\n  }\n  const doc = {\n    hidden: false,\n    visibilityState: 'visible'\n  };\n  return doc;\n}\n/**\n * Create a injectable provider for the DocumentRef token that uses the BrowserDocumentRef class.\n */\nconst browserDocumentProvider = {\n  provide: DocumentRef,\n  useClass: BrowserDocumentRef\n};\n/**\n * Create an injectable provider that uses the DocumentFactory function for returning the native Document object.\n */\nconst documentProvider = {\n  provide: DOCUMENT,\n  useFactory: documentFactory,\n  deps: [DocumentRef, PLATFORM_ID]\n};\n/**\n * Create an array of providers.\n */\nconst DOCUMENT_PROVIDERS = [browserDocumentProvider, documentProvider];\nclass AutoplayService {\n  carouselService;\n  ngZone;\n  /**\n   * Subscrioption to merge Observables from CarouselService\n   */\n  autoplaySubscription;\n  /**\n   * The autoplay timeout.\n   */\n  _timeout = null;\n  /**\n   * Indicates whenever the autoplay is paused.\n   */\n  _paused = false;\n  /**\n   * Shows whether the code (the plugin) changed the option 'AutoplayTimeout' for own needs\n   */\n  _isArtificialAutoplayTimeout;\n  /**\n   * Shows whether the autoplay is paused for unlimited time by the developer.\n   * Use to prevent autoplaying in case of firing `mouseleave` by adding layers to `<body>` like `mat-menu` does\n   */\n  _isAutoplayStopped = false;\n  get isAutoplayStopped() {\n    return this._isAutoplayStopped;\n  }\n  set isAutoplayStopped(value) {\n    this._isAutoplayStopped = value;\n  }\n  winRef;\n  docRef;\n  constructor(carouselService, winRef, docRef, ngZone) {\n    this.carouselService = carouselService;\n    this.ngZone = ngZone;\n    this.winRef = winRef;\n    this.docRef = docRef;\n    this.spyDataStreams();\n  }\n  ngOnDestroy() {\n    this.autoplaySubscription.unsubscribe();\n  }\n  /**\n   * Defines Observables which service must observe\n   */\n  spyDataStreams() {\n    const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n      if (this.carouselService.settings.autoplay) {\n        this.play();\n      }\n    }));\n    const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n      this._handleChangeObservable(data);\n    }));\n    const resized$ = this.carouselService.getResizedState().pipe(tap(() => {\n      if (this.carouselService.settings.autoplay && !this._isAutoplayStopped) {\n        this.play();\n      } else {\n        this.stop();\n      }\n    }));\n    // original Autoplay Plugin has listeners on play.owl.core and stop.owl.core events.\n    // They are triggered by Video Plugin\n    const autoplayMerge$ = merge(initializedCarousel$, changedSettings$, resized$);\n    this.autoplaySubscription = autoplayMerge$.subscribe(() => {});\n  }\n  /**\n     * Starts the autoplay.\n     * @param timeout The interval before the next animation starts.\n     * @param speed The animation speed for the animations.\n     */\n  play(timeout, speed) {\n    if (this._paused) {\n      this._paused = false;\n      this._setAutoPlayInterval(this.carouselService.settings.autoplayMouseleaveTimeout);\n    }\n    if (this.carouselService.is('rotating')) {\n      return;\n    }\n    this.carouselService.enter('rotating');\n    this._setAutoPlayInterval();\n  }\n  /**\n     * Gets a new timeout\n     * @param timeout - The interval before the next animation starts.\n     * @param speed - The animation speed for the animations.\n     * @return\n     */\n  _getNextTimeout(timeout, speed) {\n    if (this._timeout) {\n      this.winRef.clearTimeout(this._timeout);\n    }\n    this._isArtificialAutoplayTimeout = timeout ? true : false;\n    return this.ngZone.runOutsideAngular(() => {\n      return this.winRef.setTimeout(() => {\n        this.ngZone.run(() => {\n          if (this._paused || this.carouselService.is('busy') || this.carouselService.is('interacting') || this.docRef.hidden) {\n            return;\n          }\n          this.carouselService.next(speed || this.carouselService.settings.autoplaySpeed);\n        });\n      }, timeout || this.carouselService.settings.autoplayTimeout);\n    });\n  }\n  /**\n     * Sets autoplay in motion.\n     */\n  _setAutoPlayInterval(timeout) {\n    this._timeout = this._getNextTimeout(timeout);\n  }\n  /**\n   * Stops the autoplay.\n   */\n  stop() {\n    if (!this.carouselService.is('rotating')) {\n      return;\n    }\n    this._paused = true;\n    this.winRef.clearTimeout(this._timeout);\n    this.carouselService.leave('rotating');\n  }\n  /**\n     * Stops the autoplay.\n     */\n  pause() {\n    if (!this.carouselService.is('rotating')) {\n      return;\n    }\n    this._paused = true;\n  }\n  /**\n   * Manages by autoplaying according to data passed by _changedSettingsCarousel$ Obsarvable\n   * @param data object with current position of carousel and type of change\n   */\n  _handleChangeObservable(data) {\n    if (data.property.name === 'settings') {\n      if (this.carouselService.settings.autoplay) {\n        this.play();\n      } else {\n        this.stop();\n      }\n    } else if (data.property.name === 'position') {\n      //console.log('play?', e);\n      if (this.carouselService.settings.autoplay) {\n        this._setAutoPlayInterval();\n      }\n    }\n  }\n  /**\n   * Starts autoplaying of the carousel in the case when user leaves the carousel before it starts translateing (moving)\n   */\n  _playAfterTranslated() {\n    of('translated').pipe(switchMap(data => this.carouselService.getTranslatedState()), first(), filter(() => this._isArtificialAutoplayTimeout), tap(() => this._setAutoPlayInterval())).subscribe(() => {});\n  }\n  /**\n   * Starts pausing\n   */\n  startPausing() {\n    if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n      this.pause();\n    }\n  }\n  /**\n   * Starts playing after mouse leaves carousel\n   */\n  startPlayingMouseLeave() {\n    if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n      this.play();\n      this._playAfterTranslated();\n    }\n  }\n  /**\n   * Starts playing after touch ends\n   */\n  startPlayingTouchEnd() {\n    if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n      this.play();\n      this._playAfterTranslated();\n    }\n  }\n  static ɵfac = function AutoplayService_Factory(t) {\n    return new (t || AutoplayService)(i0.ɵɵinject(CarouselService), i0.ɵɵinject(WINDOW), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AutoplayService,\n    factory: AutoplayService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoplayService, [{\n    type: Injectable\n  }], () => [{\n    type: CarouselService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [WINDOW]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\nclass LazyLoadService {\n  carouselService;\n  /**\n   * Subscrioption to merge Observable  from CarouselService\n   */\n  lazyLoadSubscription;\n  constructor(carouselService) {\n    this.carouselService = carouselService;\n    this.spyDataStreams();\n  }\n  ngOnDestroy() {\n    this.lazyLoadSubscription.unsubscribe();\n  }\n  /**\n   * Defines Observables which service must observe\n   */\n  spyDataStreams() {\n    const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n      const isLazyLoad = this.carouselService.settings && !this.carouselService.settings.lazyLoad;\n      this.carouselService.slidesData.forEach(item => item.load = isLazyLoad ? true : false);\n    }));\n    const changeSettings$ = this.carouselService.getChangeState();\n    const resizedCarousel$ = this.carouselService.getResizedState();\n    const lazyLoadMerge$ = merge(initializedCarousel$, changeSettings$, resizedCarousel$).pipe(tap(data => this._defineLazyLoadSlides(data)));\n    this.lazyLoadSubscription = lazyLoadMerge$.subscribe(() => {});\n  }\n  _defineLazyLoadSlides(data) {\n    if (!this.carouselService.settings || !this.carouselService.settings.lazyLoad) {\n      return;\n    }\n    if (data.property && data.property.name === 'position' || data === 'initialized' || data === \"resized\") {\n      const settings = this.carouselService.settings,\n        clones = this.carouselService.clones().length;\n      let n = settings.center && Math.ceil(settings.items / 2) || settings.items,\n        i = settings.center && n * -1 || 0,\n        position = (data.property && data.property.value !== undefined ? data.property.value : this.carouselService.current()) + i;\n      // load = $.proxy(function(i, v) { this.load(v) }, this);\n      //TODO: Need documentation for this new option\n      if (settings.lazyLoadEager > 0) {\n        n += settings.lazyLoadEager;\n        // If the carousel is looping also preload images that are to the \"left\"\n        if (settings.loop) {\n          position -= settings.lazyLoadEager;\n          n++;\n        }\n      }\n      while (i++ < n) {\n        this._load(clones / 2 + this.carouselService.relative(position));\n        if (clones) {\n          this.carouselService.clones(this.carouselService.relative(position)).forEach(value => this._load(value));\n        }\n        position++;\n      }\n    }\n  }\n  /**\n     * Loads all resources of an item at the specified position.\n     * @param position - The absolute position of the item.\n     */\n  _load(position) {\n    if (this.carouselService.slidesData[position].load) {\n      return;\n    }\n    this.carouselService.slidesData[position].load = true;\n  }\n  static ɵfac = function LazyLoadService_Factory(t) {\n    return new (t || LazyLoadService)(i0.ɵɵinject(CarouselService));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LazyLoadService,\n    factory: LazyLoadService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LazyLoadService, [{\n    type: Injectable\n  }], () => [{\n    type: CarouselService\n  }], null);\n})();\nclass AnimateService {\n  carouselService;\n  /**\n   * Subscrioption to merge Observable  from CarouselService\n   */\n  animateSubscription;\n  /**\n   * s\n   */\n  swapping = true;\n  /**\n   * active slide before translating\n   */\n  previous = undefined;\n  /**\n   * new active slide after translating\n   */\n  next = undefined;\n  constructor(carouselService) {\n    this.carouselService = carouselService;\n    this.spyDataStreams();\n  }\n  ngOnDestroy() {\n    this.animateSubscription.unsubscribe();\n  }\n  /**\n   * Defines Observables which service must observe\n   */\n  spyDataStreams() {\n    const changeSettings$ = this.carouselService.getChangeState().pipe(tap(data => {\n      if (data.property.name === 'position') {\n        this.previous = this.carouselService.current();\n        this.next = data.property.value;\n      }\n    }));\n    const dragCarousel$ = this.carouselService.getDragState();\n    const draggedCarousel$ = this.carouselService.getDraggedState();\n    const translatedCarousel$ = this.carouselService.getTranslatedState();\n    const dragTranslatedMerge$ = merge(dragCarousel$, draggedCarousel$, translatedCarousel$).pipe(tap(data => this.swapping = data === 'translated'));\n    const translateCarousel$ = this.carouselService.getTranslateState().pipe(tap(data => {\n      if (this.swapping && (this.carouselService._options.animateOut || this.carouselService._options.animateIn)) {\n        this._swap();\n      }\n    }));\n    const animateMerge$ = merge(changeSettings$, translateCarousel$, dragTranslatedMerge$).pipe();\n    this.animateSubscription = animateMerge$.subscribe(() => {});\n  }\n  /**\n     * Toggles the animation classes whenever an translations starts.\n     * @returns\n     */\n  _swap() {\n    if (this.carouselService.settings.items !== 1) {\n      return;\n    }\n    // if (!$.support.animation || !$.support.transition) {\n    // \treturn;\n    // }\n    this.carouselService.speed(0);\n    let left;\n    const previous = this.carouselService.slidesData[this.previous],\n      next = this.carouselService.slidesData[this.next],\n      incoming = this.carouselService.settings.animateIn,\n      outgoing = this.carouselService.settings.animateOut;\n    if (this.carouselService.current() === this.previous) {\n      return;\n    }\n    if (outgoing) {\n      left = +this.carouselService.coordinates(this.previous) - +this.carouselService.coordinates(this.next);\n      this.carouselService.slidesData.forEach(slide => {\n        if (slide.id === previous.id) {\n          slide.left = `${left}px`;\n          slide.isAnimated = true;\n          slide.isDefAnimatedOut = true;\n          slide.isCustomAnimatedOut = true;\n        }\n      });\n    }\n    if (incoming) {\n      this.carouselService.slidesData.forEach(slide => {\n        if (slide.id === next.id) {\n          slide.isAnimated = true;\n          slide.isDefAnimatedIn = true;\n          slide.isCustomAnimatedIn = true;\n        }\n      });\n    }\n  }\n  /**\n   * Handles the end of 'animationend' event\n   * @param id Id of slides\n   */\n  clear(id) {\n    this.carouselService.slidesData.forEach(slide => {\n      if (slide.id === id) {\n        slide.left = '';\n        slide.isAnimated = false;\n        slide.isDefAnimatedOut = false;\n        slide.isCustomAnimatedOut = false;\n        slide.isDefAnimatedIn = false;\n        slide.isCustomAnimatedIn = false;\n        slide.classes = this.carouselService.setCurSlideClasses(slide);\n      }\n    });\n    this.carouselService.onTransitionEnd();\n  }\n  static ɵfac = function AnimateService_Factory(t) {\n    return new (t || AnimateService)(i0.ɵɵinject(CarouselService));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AnimateService,\n    factory: AnimateService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimateService, [{\n    type: Injectable\n  }], () => [{\n    type: CarouselService\n  }], null);\n})();\nclass AutoHeightService {\n  carouselService;\n  /**\n   * Subscrioption to merge Observable  from CarouselService\n   */\n  autoHeightSubscription;\n  constructor(carouselService) {\n    this.carouselService = carouselService;\n    this.spyDataStreams();\n  }\n  ngOnDestroy() {\n    this.autoHeightSubscription.unsubscribe();\n  }\n  /**\n   * Defines Observables which service must observe\n   */\n  spyDataStreams() {\n    const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(data => {\n      if (this.carouselService.settings.autoHeight) {\n        this.update();\n      } else {\n        this.carouselService.slidesData.forEach(slide => slide.heightState = 'full');\n      }\n    }));\n    const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n      if (this.carouselService.settings.autoHeight && data.property.name === 'position') {\n        this.update();\n      }\n    }));\n    const refreshedCarousel$ = this.carouselService.getRefreshedState().pipe(tap(data => {\n      if (this.carouselService.settings.autoHeight) {\n        this.update();\n      }\n    }));\n    const autoHeight$ = merge(initializedCarousel$, changedSettings$, refreshedCarousel$);\n    this.autoHeightSubscription = autoHeight$.subscribe(() => {});\n  }\n  /**\n   * Updates the prop 'heightState' of slides\n   */\n  update() {\n    const items = this.carouselService.settings.items;\n    let start = this.carouselService.current(),\n      end = start + items;\n    if (this.carouselService.settings.center) {\n      start = items % 2 === 1 ? start - (items - 1) / 2 : start - items / 2;\n      end = items % 2 === 1 ? start + items : start + items + 1;\n    }\n    this.carouselService.slidesData.forEach((slide, i) => {\n      slide.heightState = i >= start && i < end ? 'full' : 'nulled';\n    });\n  }\n  static ɵfac = function AutoHeightService_Factory(t) {\n    return new (t || AutoHeightService)(i0.ɵɵinject(CarouselService));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AutoHeightService,\n    factory: AutoHeightService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoHeightService, [{\n    type: Injectable\n  }], () => [{\n    type: CarouselService\n  }], null);\n})();\nclass HashService {\n  carouselService;\n  route;\n  router;\n  /**\n   * Subscription to merge Observable from CarouselService\n   */\n  hashSubscription;\n  /**\n   * Current url fragment (hash)\n   */\n  currentHashFragment;\n  constructor(carouselService, route, router) {\n    this.carouselService = carouselService;\n    this.route = route;\n    this.router = router;\n    this.spyDataStreams();\n    if (!this.route) {\n      this.route = {\n        fragment: of('no route').pipe(take(1))\n      };\n    }\n    ;\n    if (!this.router) {\n      this.router = {\n        navigate: (commands, extras) => {\n          return;\n        }\n      };\n    }\n  }\n  ngOnDestroy() {\n    this.hashSubscription.unsubscribe();\n  }\n  /**\n   * Defines Observables which service must observe\n   */\n  spyDataStreams() {\n    const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => this.listenToRoute()));\n    const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n      if (this.carouselService.settings.URLhashListener && data.property.name === 'position') {\n        const newCurSlide = this.carouselService.current();\n        const newCurFragment = this.carouselService.slidesData[newCurSlide].hashFragment;\n        if (!newCurFragment || newCurFragment === this.currentHashFragment) {\n          return;\n        }\n        this.router.navigate(['./'], {\n          fragment: newCurFragment,\n          relativeTo: this.route\n        });\n      }\n    }));\n    const hashFragment$ = merge(initializedCarousel$, changedSettings$);\n    this.hashSubscription = hashFragment$.subscribe(() => {});\n  }\n  /**\n   * rewinds carousel to slide which has the same hashFragment as fragment of current url\n   * @param fragment fragment of url\n   */\n  rewind(fragment) {\n    const position = this.carouselService.slidesData.findIndex(slide => slide.hashFragment === fragment && slide.isCloned === false);\n    if (position === -1 || position === this.carouselService.current()) {\n      return;\n    }\n    this.carouselService.to(this.carouselService.relative(position), false);\n  }\n  /**\n   * Initiate listening to ActivatedRoute.fragment\n   */\n  listenToRoute() {\n    const count = this.carouselService.settings.startPosition === 'URLHash' ? 0 : 2;\n    this.route.fragment.pipe(skip(count)).subscribe(fragment => {\n      this.currentHashFragment = fragment;\n      this.rewind(fragment);\n    });\n  }\n  static ɵfac = function HashService_Factory(t) {\n    return new (t || HashService)(i0.ɵɵinject(CarouselService), i0.ɵɵinject(i1.ActivatedRoute, 8), i0.ɵɵinject(i1.Router, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HashService,\n    factory: HashService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HashService, [{\n    type: Injectable\n  }], () => [{\n    type: CarouselService\n  }, {\n    type: i1.ActivatedRoute,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i1.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nlet nextId = 0;\nclass CarouselSlideDirective {\n  tplRef;\n  /**\n   * Unique slide identifier. Must be unique for the entire document for proper accessibility support.\n   * Will be auto-generated if not provided.\n   */\n  id = input(`owl-slide-${nextId++}`);\n  /**\n   * Defines how much widths of common slide will current slide have\n   * e.g. if dataMerge=2, the slide will twice wider then slides with dataMerge=1\n   */\n  dataMerge = input(1, {\n    transform: data => {\n      return +data || 1;\n    }\n  });\n  /**\n   * Width of slide\n   */\n  width = input(0);\n  /**\n   * Inner content of dot for certain slide; can be html-markup\n   */\n  dotContent = input('');\n  /**\n   * Hash (fragment) of url which corresponds to certain slide\n   */\n  dataHash = input('');\n  constructor(tplRef) {\n    this.tplRef = tplRef;\n  }\n  /**\n     * Determines if the input is a Number or something that can be coerced to a Number\n     * @param - The input to be tested\n     * @returns - An indication if the input is a Number or can be coerced to a Number\n     */\n  isNumeric(number) {\n    return !isNaN(parseFloat(number));\n  }\n  static ɵfac = function CarouselSlideDirective_Factory(t) {\n    return new (t || CarouselSlideDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CarouselSlideDirective,\n    selectors: [[\"ng-template\", \"carouselSlide\", \"\"]],\n    inputs: {\n      id: [i0.ɵɵInputFlags.SignalBased, \"id\"],\n      dataMerge: [i0.ɵɵInputFlags.SignalBased, \"dataMerge\"],\n      width: [i0.ɵɵInputFlags.SignalBased, \"width\"],\n      dotContent: [i0.ɵɵInputFlags.SignalBased, \"dotContent\"],\n      dataHash: [i0.ɵɵInputFlags.SignalBased, \"dataHash\"]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselSlideDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[carouselSlide]',\n      standalone: false\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass ResizeService {\n  resizeObservable$;\n  docRef;\n  /**\n   * Makes resizeSubject become Observable\n   * @returns Observable of resizeSubject\n   */\n  get onResize$() {\n    return this.resizeObservable$.pipe(filter(() => !this.docRef?.fullscreenElement));\n  }\n  constructor(winRef, docRef, platformId) {\n    this.docRef = docRef;\n    this.resizeObservable$ = isPlatformBrowser(platformId) ? fromEvent(winRef, 'resize') : new Subject().asObservable();\n  }\n  static ɵfac = function ResizeService_Factory(t) {\n    return new (t || ResizeService)(i0.ɵɵinject(WINDOW), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ResizeService,\n    factory: ResizeService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizeService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [WINDOW]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nclass StageComponent {\n  zone;\n  el;\n  renderer;\n  carouselService;\n  animateService;\n  /**\n   * Object with settings which make carousel draggable by touch or mouse\n   */\n  owlDraggable = input();\n  /**\n   * Data of owl-stage\n   */\n  stageData = input();\n  /**\n   *  Data of every slide\n   */\n  slidesData = input();\n  /**\n   * Function wich will be returned after attaching listener to 'mousemove' event\n   */\n  listenerMouseMove;\n  /**\n   * Function wich will be returned after attaching listener to 'touchmove' event\n   */\n  listenerTouchMove;\n  /**\n   * Function wich will be returned after attaching listener to 'mousemove' event\n   */\n  listenerOneMouseMove;\n  /**\n   * Function wich will be returned after attaching listener to 'touchmove' event\n   */\n  listenerOneTouchMove;\n  /**\n   * Function wich will be returned after attaching listener to 'mouseup' event\n   */\n  listenerMouseUp;\n  /**\n   * Function wich will be returned after attaching listener to 'touchend' event\n   */\n  listenerTouchEnd;\n  /**\n   * Function wich will be returned after attaching listener to 'click' event\n   */\n  listenerOneClick;\n  listenerATag;\n  /**\n   * Object with data needed for dragging\n   */\n  _drag = {\n    time: null,\n    target: null,\n    pointer: null,\n    stage: {\n      start: null,\n      current: null\n    },\n    direction: null,\n    active: false,\n    moving: false\n  };\n  /**\n   * Subject for notification when the carousel's rebuilding caused by resize event starts\n   */\n  _oneDragMove$ = new Subject();\n  /**\n   * Subsctiption to _oneDragMove$ Subject\n   */\n  _oneMoveSubsription;\n  preparePublicSlide = slide => {\n    const newSlide = {\n      ...slide\n    };\n    delete newSlide.tplRef;\n    return newSlide;\n  };\n  constructor(zone, el, renderer, carouselService, animateService) {\n    this.zone = zone;\n    this.el = el;\n    this.renderer = renderer;\n    this.carouselService = carouselService;\n    this.animateService = animateService;\n  }\n  onMouseDown(event) {\n    if (this.owlDraggable()?.isMouseDragable) {\n      this._onDragStart(event);\n    }\n  }\n  onTouchStart(event) {\n    if (event.targetTouches.length >= 2) {\n      return false;\n    }\n    if (this.owlDraggable()?.isTouchDragable) {\n      this._onDragStart(event);\n    }\n  }\n  onTouchCancel(event) {\n    this._onDragEnd(event);\n  }\n  onDragStart() {\n    if (this.owlDraggable()?.isMouseDragable) {\n      return false;\n    }\n  }\n  onSelectStart() {\n    if (this.owlDraggable()?.isMouseDragable) {\n      return false;\n    }\n  }\n  ngOnInit() {\n    this._oneMoveSubsription = this._oneDragMove$.pipe(first()).subscribe(() => {\n      this._sendChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._oneMoveSubsription.unsubscribe();\n  }\n  /**\n   * Passes this to _oneMouseTouchMove();\n   */\n  bindOneMouseTouchMove = ev => {\n    this._oneMouseTouchMove(ev);\n  };\n  /**\n   * Passes this to _onDragMove();\n   */\n  bindOnDragMove = ev => {\n    this._onDragMove(ev);\n  };\n  /**\n   * Passes this to _onDragMove();\n   */\n  bindOnDragEnd = ev => {\n    // this.zone.run(() => {\n    this._onDragEnd(ev);\n    // });\n  };\n  /**\n   * Handles `touchstart` and `mousedown` events.\n   * @todo Horizontal swipe threshold as option\n   * @todo #261\n   * @param event - The event arguments.\n   */\n  _onDragStart(event) {\n    if (event.which === 3) {\n      return;\n    }\n    const stage = this._prepareDragging(event);\n    this._drag.time = new Date().getTime();\n    this._drag.target = event.target;\n    this._drag.stage.start = stage;\n    this._drag.stage.current = stage;\n    this._drag.pointer = this._pointer(event);\n    this.listenerMouseUp = this.renderer.listen(document, 'mouseup', this.bindOnDragEnd);\n    this.listenerTouchEnd = this.renderer.listen(document, 'touchend', this.bindOnDragEnd);\n    this.zone.runOutsideAngular(() => {\n      this.listenerOneMouseMove = this.renderer.listen(document, 'mousemove', this.bindOneMouseTouchMove);\n      this.listenerOneTouchMove = this.renderer.listen(document, 'touchmove', this.bindOneMouseTouchMove);\n    });\n  }\n  /**\n   * Attaches listeners to `touchmove` and `mousemove` events; initiates updating carousel after starting dragging\n   * @param event event objech of mouse or touch event\n   */\n  _oneMouseTouchMove(event) {\n    const delta = this._difference(this._drag.pointer, this._pointer(event));\n    if (this.listenerATag) {\n      this.listenerATag();\n    }\n    if (Math.abs(delta.x) < 3 && Math.abs(delta.y) < 3 && this._is('valid')) {\n      return;\n    }\n    if (Math.abs(delta.x) < 3 && Math.abs(delta.x) < Math.abs(delta.y) && this._is('valid')) {\n      return;\n    }\n    this.listenerOneMouseMove();\n    this.listenerOneTouchMove();\n    this._drag.moving = true;\n    this.blockClickAnchorInDragging(event);\n    this.listenerMouseMove = this.renderer.listen(document, 'mousemove', this.bindOnDragMove);\n    this.listenerTouchMove = this.renderer.listen(document, 'touchmove', this.bindOnDragMove);\n    event.preventDefault();\n    this._enterDragging();\n    this._oneDragMove$.next(event);\n    // this._sendChanges();\n  }\n  /**\n   * Attaches handler to HTMLAnchorElement for preventing click while carousel is being dragged\n   * @param event event object\n   */\n  blockClickAnchorInDragging(event) {\n    let target = event.target;\n    while (target && !(target instanceof HTMLAnchorElement)) {\n      target = target.parentElement;\n    }\n    if (target instanceof HTMLAnchorElement) {\n      this.listenerATag = this.renderer.listen(target, 'click', () => false);\n    }\n  }\n  /**\n  * Handles the `touchmove` and `mousemove` events.\n  * @todo #261\n  * @param event - The event arguments.\n  */\n  _onDragMove(event) {\n    let stage;\n    const stageOrExit = this.carouselService.defineNewCoordsDrag(event, this._drag);\n    if (stageOrExit === false) {\n      return;\n    }\n    stage = stageOrExit;\n    event.preventDefault();\n    this._drag.stage.current = stage;\n    this._animate(stage.x - this._drag.stage.start.x);\n  }\n  /**\n   * Moves .owl-stage left-right\n   * @param coordinate coordinate to be set to .owl-stage\n   */\n  _animate(coordinate) {\n    this.renderer.setStyle(this.el.nativeElement.children[0], 'transform', `translate3d(${coordinate}px,0px,0px`);\n    this.renderer.setStyle(this.el.nativeElement.children[0], 'transition', '0s');\n  }\n  /**\n   * Handles the `touchend` and `mouseup` events.\n   * @todo #261\n   * @todo Threshold for click event\n   * @param event - The event arguments.\n   */\n  _onDragEnd(event) {\n    this.carouselService.owlDOMData.isGrab = false;\n    this.listenerOneMouseMove();\n    this.listenerOneTouchMove();\n    if (this._drag.moving) {\n      this.renderer.setStyle(this.el.nativeElement.children[0], 'transform', ``);\n      this.renderer.setStyle(this.el.nativeElement.children[0], 'transition', this.carouselService.speed(+(this.carouselService?.settings?.dragEndSpeed || 0) || this.carouselService.settings.smartSpeed) / 1000 + 's');\n      this._finishDragging(event);\n      this.listenerMouseMove();\n      this.listenerTouchMove();\n    }\n    this._drag = {\n      time: null,\n      target: null,\n      pointer: null,\n      stage: {\n        start: null,\n        current: null\n      },\n      direction: null,\n      active: false,\n      moving: false\n    };\n    // this.carouselService.trigger('dragged');\n    this.listenerMouseUp();\n    this.listenerTouchEnd();\n  }\n  /**\n   * Prepares data for dragging carousel. It starts after firing `touchstart` and `mousedown` events.\n   * @param event - The event arguments.\n   * @returns stage - object with 'x' and 'y' coordinates of .owl-stage\n   */\n  _prepareDragging(event) {\n    return this.carouselService.prepareDragging(event);\n  }\n  /**\n   * Attaches handler for 'click' event on any element in .owl-stage in order to prevent dragging when moving of cursor is less than 3px\n   */\n  _oneClickHandler = () => {\n    this.listenerOneClick = this.renderer.listen(this._drag.target, 'click', () => false);\n    this.listenerOneClick();\n  };\n  /**\n   * Finishes dragging\n   * @param event object event of 'mouseUp' of 'touchend' events\n   */\n  _finishDragging(event) {\n    this.carouselService.finishDragging(event, this._drag, this._oneClickHandler);\n  }\n  /**\n   * Gets unified pointer coordinates from event.\n   * @param event The `mousedown` or `touchstart` event.\n   * @returns Contains `x` and `y` coordinates of current pointer position.\n   */\n  _pointer(event) {\n    return this.carouselService.pointer(event);\n  }\n  /**\n   * Gets the difference of two vectors.\n   * @param first The first vector.\n   * @param second The second vector.\n   * @returns The difference.\n   */\n  _difference(firstC, second) {\n    return this.carouselService.difference(firstC, second);\n  }\n  /**\n   * Checks whether the carousel is in a specific state or not.\n   * @param specificState The state to check.\n   * @returns The flag which indicates if the carousel is busy.\n   */\n  _is(specificState) {\n    return this.carouselService.is(specificState);\n  }\n  /**\n  * Enters a state.\n  * @param name The state name.\n  */\n  _enter(name) {\n    this.carouselService.enter(name);\n  }\n  /**\n   * Sends all data needed for View.\n   */\n  _sendChanges() {\n    this.carouselService.sendChanges();\n  }\n  /**\n   * Handler for transitioend event\n   */\n  onTransitionEnd() {\n    this.carouselService.onTransitionEnd();\n  }\n  /**\n   * Enters into a 'dragging' state\n   */\n  _enterDragging() {\n    this.carouselService.enterDragging();\n  }\n  /**\n   * Handles the end of 'animationend' event\n   * @param id Id of slides\n   */\n  clear(id) {\n    this.animateService.clear(id);\n  }\n  static ɵfac = function StageComponent_Factory(t) {\n    return new (t || StageComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(CarouselService), i0.ɵɵdirectiveInject(AnimateService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: StageComponent,\n    selectors: [[\"owl-stage\"]],\n    hostBindings: function StageComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mousedown\", function StageComponent_mousedown_HostBindingHandler($event) {\n          return ctx.onMouseDown($event);\n        })(\"touchstart\", function StageComponent_touchstart_HostBindingHandler($event) {\n          return ctx.onTouchStart($event);\n        })(\"touchcancel\", function StageComponent_touchcancel_HostBindingHandler($event) {\n          return ctx.onTouchCancel($event);\n        })(\"dragstart\", function StageComponent_dragstart_HostBindingHandler() {\n          return ctx.onDragStart();\n        })(\"selectstart\", function StageComponent_selectstart_HostBindingHandler() {\n          return ctx.onSelectStart();\n        });\n      }\n    },\n    inputs: {\n      owlDraggable: [i0.ɵɵInputFlags.SignalBased, \"owlDraggable\"],\n      stageData: [i0.ɵɵInputFlags.SignalBased, \"stageData\"],\n      slidesData: [i0.ɵɵInputFlags.SignalBased, \"slidesData\"]\n    },\n    decls: 4,\n    vars: 7,\n    consts: [[1, \"owl-stage\", 3, \"transitionend\", \"ngStyle\"], [1, \"owl-item\", 3, \"ngClass\", \"ngStyle\"], [1, \"owl-item\", 3, \"animationend\", \"ngClass\", \"ngStyle\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function StageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n        i0.ɵɵlistener(\"transitionend\", function StageComponent_Template_div_transitionend_1_listener() {\n          return ctx.onTransitionEnd();\n        });\n        i0.ɵɵrepeaterCreate(2, StageComponent_For_3_Template, 2, 9, \"div\", 1, _forTrack0);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction5(1, _c0, ctx.stageData().width + \"px\", ctx.stageData().transform, ctx.stageData().transition, ctx.stageData().paddingL ? ctx.stageData().paddingL + \"px\" : \"\", ctx.stageData().paddingR ? ctx.stageData().paddingR + \"px\" : \"\"));\n        i0.ɵɵadvance();\n        i0.ɵɵrepeater(ctx.slidesData());\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgTemplateOutlet, i3.NgStyle],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('autoHeight', [state('nulled', style({\n        height: 0\n      })), state('full', style({\n        height: '*'\n      })), transition('full => nulled', [\n      // style({height: '*'}),\n      animate('700ms 350ms')]), transition('nulled => full', [\n      // style({height: 0}),\n      animate(350)])])]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StageComponent, [{\n    type: Component,\n    args: [{\n      selector: 'owl-stage',\n      template: `\n    <div>\n      <div class=\"owl-stage\" [ngStyle]=\"{'width': stageData().width + 'px',\n                                        'transform': stageData().transform,\n                                        'transition': stageData().transition,\n                                        'padding-left': stageData().paddingL ? stageData().paddingL + 'px' : '',\n                                        'padding-right': stageData().paddingR ? stageData().paddingR + 'px' : '' }\"\n          (transitionend)=\"onTransitionEnd()\"\n      >\n\n        @for(slide of slidesData(); track slide.id; let i = $index) {\n          <div class=\"owl-item\" [ngClass]=\"slide.classes\"\n                                [ngStyle]=\"{'width': slide.width + 'px',\n                                            'margin-left': slide.marginL ? slide.marginL + 'px' : '',\n                                            'margin-right': slide.marginR ? slide.marginR + 'px' : '',\n                                            'left': slide.left}\"\n                                (animationend)=\"clear(slide.id)\"\n                                [@autoHeight]=\"slide.heightState\">\n              @if(slide.load) {\n                <ng-template  [ngTemplateOutlet]=\"slide.tplRef\" [ngTemplateOutletContext]=\"{ \n                  $implicit: preparePublicSlide(slide), \n                  index: i\n                }\">\n                </ng-template>\n              }\n          </div><!-- /.owl-item -->\n        }  \n      \n      </div><!-- /.owl-stage -->\n    </div>\n  `,\n      animations: [trigger('autoHeight', [state('nulled', style({\n        height: 0\n      })), state('full', style({\n        height: '*'\n      })), transition('full => nulled', [\n      // style({height: '*'}),\n      animate('700ms 350ms')]), transition('nulled => full', [\n      // style({height: 0}),\n      animate(350)])])],\n      standalone: false\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: CarouselService\n  }, {\n    type: AnimateService\n  }], {\n    onMouseDown: [{\n      type: HostListener,\n      args: ['mousedown', ['$event']]\n    }],\n    onTouchStart: [{\n      type: HostListener,\n      args: ['touchstart', ['$event']]\n    }],\n    onTouchCancel: [{\n      type: HostListener,\n      args: ['touchcancel', ['$event']]\n    }],\n    onDragStart: [{\n      type: HostListener,\n      args: ['dragstart']\n    }],\n    onSelectStart: [{\n      type: HostListener,\n      args: ['selectstart']\n    }]\n  });\n})();\nclass CarouselComponent {\n  el;\n  resizeService;\n  carouselService;\n  navigationService;\n  autoplayService;\n  lazyLoadService;\n  animateService;\n  autoHeightService;\n  hashService;\n  logger;\n  changeDetectorRef;\n  //  Cannot implement via contentChildren() because of inputs are a little bit late and I get default input values\n  // in the case of converting slides to Observable and subscribing to it \n  // when using effect I get endless loop, because it also uses options() input and they fire one after another\n  slides;\n  translated = output();\n  dragging = output();\n  change = output();\n  changed = output();\n  initialized = output();\n  /**\n   * Width of carousel window (tag with class .owl-carousel), in wich we can see moving sliders\n   */\n  carouselWindowWidth;\n  /**\n   * Subscription to 'resize' event\n   */\n  resizeSubscription;\n  /**\n   * Subscription merge Observable, which merges all Observables in the component except 'resize' Observable and this.slides.changes()\n   */\n  _allObservSubscription;\n  /**\n   * Subscription to `this.slides.changes().\n   * It could be included in 'this._allObservSubscription', but that subcription get created during the initializing of component\n   * and 'this.slides' are undefined at that moment. So it's needed to wait for initialization of content.\n   */\n  _slidesChangesSubscription;\n  /**\n   * Current settings for the carousel.\n   */\n  _owlDOMData = signal(null);\n  owlDOMData = this._owlDOMData.asReadonly();\n  /**\n   * Data of owl-stage\n   */\n  _stageData = signal(null);\n  stageData = this._stageData.asReadonly();\n  /**\n   *  Data of every slide\n   */\n  _slidesData = signal([]);\n  slidesData = this._slidesData.asReadonly();\n  /**\n   * Data of navigation block\n   */\n  _navData = signal(null);\n  navData = this._navData.asReadonly();\n  /**\n   * Data of dots block\n   */\n  _dotsData = signal(null);\n  dotsData = this._dotsData.asReadonly();\n  /**\n   * Data, wich are passed out of carousel after ending of transioning of carousel\n   */\n  slidesOutputData;\n  /**\n   * Shows whether carousel is loaded of not.\n   */\n  _carouselLoaded = signal(false);\n  carouselLoaded = this._carouselLoaded.asReadonly();\n  /**\n   * User's options\n   */\n  options = input();\n  /**\n   * Observable for user's options\n   * It is used to track changes of options and re-render carousel if needed\n   */\n  _options$ = toObservable(this.options);\n  /**\n   * Previous options, used for checking whether options were changed\n   */\n  _optionsPrevAndCur$;\n  /**\n   * Observable for getting current View Settings\n   */\n  _viewCurSettings$;\n  /**\n   * Observable for catching the end of transition of carousel\n   */\n  _translatedCarousel$;\n  /**\n   * Observable for catching the start of dragging of the carousel\n   */\n  _draggingCarousel$;\n  /**\n   * Observable for catching the start of changing of the carousel\n   */\n  _changeCarousel$;\n  /**\n   * Observable for catching the moment when the data about slides changed, more exactly when the position changed.\n   */\n  _changedCarousel$;\n  /**\n   * Observable for catching the initialization of changing the carousel\n   */\n  _initializedCarousel$;\n  /**\n   * Observable for merging all Observables and creating one subscription\n   */\n  _carouselMerge$;\n  docRef;\n  constructor(el, resizeService, carouselService, navigationService, autoplayService, lazyLoadService, animateService, autoHeightService, hashService, logger, changeDetectorRef, docRef) {\n    this.el = el;\n    this.resizeService = resizeService;\n    this.carouselService = carouselService;\n    this.navigationService = navigationService;\n    this.autoplayService = autoplayService;\n    this.lazyLoadService = lazyLoadService;\n    this.animateService = animateService;\n    this.autoHeightService = autoHeightService;\n    this.hashService = hashService;\n    this.logger = logger;\n    this.changeDetectorRef = changeDetectorRef;\n    this.docRef = docRef;\n  }\n  onVisibilityChange(ev) {\n    if (!this.carouselService.settings.autoplay) return;\n    switch (this.docRef.visibilityState) {\n      case 'visible':\n        !this.autoplayService.isAutoplayStopped && this.autoplayService.play();\n        break;\n      case 'hidden':\n        this.autoplayService.pause();\n        break;\n      default:\n        break;\n    }\n  }\n  ngOnInit() {\n    this.spyDataStreams();\n    this.carouselWindowWidth = this.el.nativeElement.querySelector('.owl-carousel').clientWidth;\n  }\n  ngAfterContentInit() {\n    if (this.slides.toArray().length) {\n      this.carouselService.setup(this.carouselWindowWidth, this.slides.toArray(), this.options());\n      this.carouselService.initialize(this.slides.toArray());\n      this._winResizeWatcher();\n    } else {\n      this.logger.log(`There are no slides to show. So the carousel won't be rendered`);\n    }\n    this._slidesChangesSubscription = this.slides.changes.pipe(tap(slides => {\n      this.carouselService.setup(this.carouselWindowWidth, slides.toArray(), this.options());\n      this.carouselService.initialize(slides.toArray());\n      if (!slides.toArray().length) {\n        this._carouselLoaded.set(false);\n      }\n      if (slides.toArray().length && !this.resizeSubscription) {\n        this._winResizeWatcher();\n      }\n    })).subscribe(() => {});\n  }\n  ngOnDestroy() {\n    if (this.resizeSubscription) {\n      this.resizeSubscription.unsubscribe();\n    }\n    if (this._slidesChangesSubscription) {\n      this._slidesChangesSubscription.unsubscribe();\n    }\n    if (this._allObservSubscription) {\n      this._allObservSubscription.unsubscribe();\n    }\n  }\n  /**\n   * Joins the observable login in one place: sets values to some observables, merges this observables and\n   * subcribes to merge func\n   */\n  spyDataStreams() {\n    this._viewCurSettings$ = this.carouselService.getViewCurSettings().pipe(tap(data => {\n      this._owlDOMData.set(data.owlDOMData);\n      this._stageData.set(data.stageData);\n      this._slidesData.set(data.slidesData);\n      if (!this._carouselLoaded()) {\n        this._carouselLoaded.set(true);\n      }\n      this._navData.set(data.navData);\n      this._dotsData.set(data.dotsData);\n      this.changeDetectorRef.markForCheck(); // despite the fact we have signals here, they work with some delay, so we need to trigger change detection manually\n    }));\n    this._initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n      this.gatherTranslatedData();\n      this.initialized.emit(this.slidesOutputData);\n      // this.slidesOutputData = {};\n    }));\n    this._translatedCarousel$ = this.carouselService.getTranslatedState().pipe(tap(() => {\n      this.gatherTranslatedData();\n      this.translated.emit(this.slidesOutputData);\n      // this.slidesOutputData = {};\n    }));\n    this._changeCarousel$ = this.carouselService.getChangeState().pipe(tap(() => {\n      this.gatherTranslatedData();\n      this.change.emit(this.slidesOutputData);\n      // this.slidesOutputData = {};\n    }));\n    this._changedCarousel$ = this.carouselService.getChangeState().pipe(switchMap(value => {\n      const changedPosition = of(value).pipe(filter(() => value.property.name === 'position'), switchMap(() => from(this._slidesData())), skip(value.property.value), take(this.carouselService?.settings?.items || 0), map(slide => {\n        const clonedIdPrefix = this.carouselService.clonedIdPrefix;\n        const id = slide.id.indexOf(clonedIdPrefix) >= 0 ? slide.id.slice(clonedIdPrefix.length) : slide.id;\n        return {\n          ...slide,\n          id: id,\n          isActive: true\n        };\n      }), toArray(), map(slides => {\n        return {\n          slides: slides,\n          startPosition: this.carouselService.relative(value.property.value)\n        };\n      }));\n      // const changedSetting: Observable<SlidesOutputData> = of(value).pipe(\n      //   filter(() => value.property.name === 'settings'),\n      //   map(() => {\n      //     return {\n      //       slides: [],\n      //       startPosition: this.carouselService.relative(value.property.value)\n      //     }\n      //   })\n      // )\n      return merge(changedPosition);\n    }), tap(slidesData => {\n      this.gatherTranslatedData();\n      this.changed.emit(slidesData?.slides?.length ? slidesData : this.slidesOutputData);\n      // console.log(this.slidesOutputData);\n      // this.slidesOutputData = {};\n    }));\n    this._draggingCarousel$ = this.carouselService.getDragState().pipe(tap(() => {\n      this.gatherTranslatedData();\n      this.dragging.emit({\n        dragging: true,\n        data: this.slidesOutputData\n      });\n    }), switchMap(() => this.carouselService.getDraggedState().pipe(map(() => !!this.carouselService.is('animating')))), switchMap(anim => {\n      if (anim) {\n        return this.carouselService.getTranslatedState().pipe(first());\n      } else {\n        return of('not animating');\n      }\n    }), tap(() => {\n      this.dragging.emit({\n        dragging: false,\n        data: this.slidesOutputData\n      });\n    }));\n    this._optionsPrevAndCur$ = this._options$.pipe(pairwise(), tap(([prev, cur]) => {\n      const slides = this.slides.toArray();\n      if (prev) {\n        this.carouselService.setup(this.carouselWindowWidth, slides, cur);\n        this.carouselService.initialize(slides);\n      }\n      if (prev && !slides.length) {\n        this.logger.log(`There are no slides to show.`);\n        this._carouselLoaded.set(false);\n      }\n      if (!prev) {\n        this._carouselLoaded.set(false);\n      }\n    }));\n    this._carouselMerge$ = merge(this._viewCurSettings$, this._translatedCarousel$, this._draggingCarousel$, this._changeCarousel$, this._changedCarousel$, this._initializedCarousel$, this._optionsPrevAndCur$);\n    this._allObservSubscription = this._carouselMerge$.subscribe(() => {});\n  }\n  /**\n   * Init subscription to resize event and attaches handler for this event\n   */\n  _winResizeWatcher() {\n    if (Object.keys(this.carouselService?._options?.responsive || {}).length) {\n      this.resizeSubscription = this.resizeService.onResize$.pipe(filter(() => this.carouselWindowWidth !== this.el.nativeElement.querySelector('.owl-carousel').clientWidth), delay(this.carouselService.settings.responsiveRefreshRate || 200)).subscribe(() => {\n        this.carouselService.onResize(this.el.nativeElement.querySelector('.owl-carousel').clientWidth);\n        this.carouselWindowWidth = this.el.nativeElement.querySelector('.owl-carousel').clientWidth;\n      });\n    }\n  }\n  /**\n   * Handler for transitioend event\n   */\n  onTransitionEnd() {\n    this.carouselService.onTransitionEnd();\n  }\n  /**\n   * Handler for click event, attached to next button\n   */\n  next() {\n    if (!this._carouselLoaded()) return;\n    this.navigationService.next(this.carouselService.settings.navSpeed || false);\n  }\n  /**\n   * Handler for click event, attached to prev button\n   */\n  prev() {\n    if (!this._carouselLoaded()) return;\n    this.navigationService.prev(this.carouselService.settings.navSpeed || false);\n  }\n  /**\n   * Handler for click event, attached to dots\n   */\n  moveByDot(dotId) {\n    if (!this._carouselLoaded()) return;\n    this.navigationService.moveByDot(dotId);\n  }\n  /**\n   * rewinds carousel to slide with needed id\n   * @param id fragment of url\n   */\n  to(id) {\n    // if (!this.carouselLoaded || ((this.navData && this.navData.disabled) && (this.dotsData && this.dotsData.disabled))) return;\n    if (!this._carouselLoaded()) return;\n    this.navigationService.toSlideById(id);\n  }\n  /**\n   * Gathers and prepares data intended for passing to the user by means of firing event translatedCarousel\n   */\n  gatherTranslatedData() {\n    let startPosition;\n    const clonedIdPrefix = this.carouselService.clonedIdPrefix;\n    const activeSlides = this._slidesData().filter(slide => slide.isActive === true).map(slide => {\n      const id = slide.id.indexOf(clonedIdPrefix) >= 0 ? slide.id.slice(clonedIdPrefix.length) : slide.id;\n      return {\n        id: id,\n        width: slide.width,\n        marginL: slide.marginL,\n        marginR: slide.marginR,\n        center: slide.isCentered\n      };\n    });\n    startPosition = this.carouselService.relative(this.carouselService.current());\n    this.slidesOutputData = {\n      startPosition: startPosition,\n      slides: activeSlides\n    };\n  }\n  /**\n   * Starts pausing\n   */\n  startPausing() {\n    this.autoplayService.startPausing();\n  }\n  /**\n   * Starts playing after mouse leaves carousel\n   */\n  startPlayML() {\n    this.autoplayService.startPlayingMouseLeave();\n  }\n  /**\n   * Starts playing after touch ends\n   */\n  startPlayTE() {\n    this.autoplayService.startPlayingTouchEnd();\n  }\n  stopAutoplay() {\n    this.autoplayService.isAutoplayStopped = true;\n    this.autoplayService.stop();\n  }\n  startAutoplay() {\n    this.autoplayService.isAutoplayStopped = false;\n    this.autoplayService.play();\n  }\n  static ɵfac = function CarouselComponent_Factory(t) {\n    return new (t || CarouselComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ResizeService), i0.ɵɵdirectiveInject(CarouselService), i0.ɵɵdirectiveInject(NavigationService), i0.ɵɵdirectiveInject(AutoplayService), i0.ɵɵdirectiveInject(LazyLoadService), i0.ɵɵdirectiveInject(AnimateService), i0.ɵɵdirectiveInject(AutoHeightService), i0.ɵɵdirectiveInject(HashService), i0.ɵɵdirectiveInject(OwlLogger), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CarouselComponent,\n    selectors: [[\"owl-carousel-o\"]],\n    contentQueries: function CarouselComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CarouselSlideDirective, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slides = _t);\n      }\n    },\n    hostBindings: function CarouselComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"visibilitychange\", function CarouselComponent_visibilitychange_HostBindingHandler($event) {\n          return ctx.onVisibilityChange($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    inputs: {\n      options: [i0.ɵɵInputFlags.SignalBased, \"options\"]\n    },\n    outputs: {\n      translated: \"translated\",\n      dragging: \"dragging\",\n      change: \"change\",\n      changed: \"changed\",\n      initialized: \"initialized\"\n    },\n    features: [i0.ɵɵProvidersFeature([NavigationService, AutoplayService, CarouselService, LazyLoadService, AnimateService, AutoHeightService, HashService])],\n    decls: 4,\n    vars: 9,\n    consts: [[\"owlCarousel\", \"\"], [1, \"owl-carousel\", \"owl-theme\", 3, \"mouseover\", \"mouseleave\", \"touchstart\", \"touchend\", \"ngClass\"], [1, \"owl-stage-outer\"], [3, \"owlDraggable\", \"stageData\", \"slidesData\"], [1, \"owl-nav\", 3, \"ngClass\"], [1, \"owl-prev\", 3, \"click\", \"ngClass\", \"innerHTML\"], [1, \"owl-next\", 3, \"click\", \"ngClass\", \"innerHTML\"], [1, \"owl-dots\", 3, \"ngClass\"], [1, \"owl-dot\", 3, \"ngClass\"], [1, \"owl-dot\", 3, \"click\", \"ngClass\"], [3, \"innerHTML\"]],\n    template: function CarouselComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵlistener(\"mouseover\", function CarouselComponent_Template_div_mouseover_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.startPausing());\n        })(\"mouseleave\", function CarouselComponent_Template_div_mouseleave_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.startPlayML());\n        })(\"touchstart\", function CarouselComponent_Template_div_touchstart_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.startPausing());\n        })(\"touchend\", function CarouselComponent_Template_div_touchend_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.startPlayTE());\n        });\n        i0.ɵɵtemplate(2, CarouselComponent_Conditional_2_Template, 2, 6, \"div\", 2)(3, CarouselComponent_Conditional_3_Template, 6, 14);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(3, _c3, (tmp_1_0 = ctx.owlDOMData()) == null ? null : tmp_1_0.rtl, (tmp_1_0 = ctx.owlDOMData()) == null ? null : tmp_1_0.isLoaded, (tmp_1_0 = ctx.owlDOMData()) == null ? null : tmp_1_0.isResponsive, (tmp_1_0 = ctx.owlDOMData()) == null ? null : tmp_1_0.isMouseDragable, (tmp_1_0 = ctx.owlDOMData()) == null ? null : tmp_1_0.isGrab));\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(2, ctx.carouselLoaded() ? 2 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(3, ctx.slides.toArray().length ? 3 : -1);\n      }\n    },\n    dependencies: [i3.NgClass, StageComponent],\n    styles: [\".owl-theme[_ngcontent-%COMP%]{display:block}\"],\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselComponent, [{\n    type: Component,\n    args: [{\n      selector: 'owl-carousel-o',\n      template: `\n    <div class=\"owl-carousel owl-theme\" #owlCarousel\n      [ngClass]=\"{'owl-rtl': owlDOMData()?.rtl,\n                  'owl-loaded': owlDOMData()?.isLoaded,\n                  'owl-responsive': owlDOMData()?.isResponsive,\n                  'owl-drag': owlDOMData()?.isMouseDragable,\n                  'owl-grab': owlDOMData()?.isGrab}\"\n      (mouseover)=\"startPausing()\"\n      (mouseleave)=\"startPlayML()\"\n      (touchstart)=\"startPausing()\"\n      (touchend)=\"startPlayTE()\">\n\n      @if(carouselLoaded()) {\n        <div class=\"owl-stage-outer\">\n          <owl-stage [owlDraggable]=\"{\n                        'isMouseDragable': owlDOMData()?.isMouseDragable, \n                        'isTouchDragable': owlDOMData()?.isTouchDragable\n                      }\"\n                      [stageData]=\"stageData()\"\n                      [slidesData]=\"slidesData()\"></owl-stage>\n        </div> <!-- /.owl-stage-outer -->\n      }\n\n      @if(slides.toArray().length) {\n          <div class=\"owl-nav\" [ngClass]=\"{'disabled': navData()?.disabled}\">\n            <div class=\"owl-prev\" [ngClass]=\"{'disabled': navData()?.prev?.disabled}\" (click)=\"prev()\" [innerHTML]=\"navData()?.prev?.htmlText\"></div>\n            <div class=\"owl-next\" [ngClass]=\"{'disabled': navData()?.next?.disabled}\" (click)=\"next()\" [innerHTML]=\"navData()?.next?.htmlText\"></div>\n          </div> <!-- /.owl-nav -->\n          <div class=\"owl-dots\" [ngClass]=\"{'disabled': dotsData()?.disabled}\">\n\n            @for (dot of dotsData()?.dots; track dot.id) {\n              <div  class=\"owl-dot\" [ngClass]=\"{'active': dot.active, 'owl-dot-text': dot.showInnerContent}\" (click)=\"moveByDot(dot.id)\">\n                <span [innerHTML]=\"dot.innerContent\"></span>\n              </div>\n            }\n            \n          </div> <!-- /.owl-dots -->\n      }\n    </div> <!-- /.owl-carousel owl-loaded -->\n  `,\n      providers: [NavigationService, AutoplayService, CarouselService, LazyLoadService, AnimateService, AutoHeightService, HashService],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      styles: [\".owl-theme{display:block}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: ResizeService\n  }, {\n    type: CarouselService\n  }, {\n    type: NavigationService\n  }, {\n    type: AutoplayService\n  }, {\n    type: LazyLoadService\n  }, {\n    type: AnimateService\n  }, {\n    type: AutoHeightService\n  }, {\n    type: HashService\n  }, {\n    type: OwlLogger\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    slides: [{\n      type: ContentChildren,\n      args: [CarouselSlideDirective]\n    }],\n    onVisibilityChange: [{\n      type: HostListener,\n      args: ['document:visibilitychange', ['$event']]\n    }]\n  });\n})();\nclass OwlRouterLinkDirective {\n  router;\n  route;\n  // TODO(issue/24571): remove '!'.\n  queryParams;\n  // TODO(issue/24571): remove '!'.\n  fragment;\n  // TODO(issue/24571): remove '!'.\n  queryParamsHandling;\n  // TODO(issue/24571): remove '!'.\n  preserveFragment;\n  // TODO(issue/24571): remove '!'.\n  skipLocationChange;\n  // TODO(issue/24571): remove '!'.\n  replaceUrl;\n  stopLink = false;\n  commands = [];\n  // TODO(issue/24571): remove '!'.\n  preserve;\n  constructor(router, route, tabIndex, renderer, el) {\n    this.router = router;\n    this.route = route;\n    if (tabIndex == null) {\n      renderer.setAttribute(el.nativeElement, 'tabindex', '0');\n    }\n  }\n  set owlRouterLink(commands) {\n    if (commands != null) {\n      this.commands = Array.isArray(commands) ? commands : [commands];\n    } else {\n      this.commands = [];\n    }\n  }\n  /**\n   * @deprecated 4.0.0 use `queryParamsHandling` instead.\n   */\n  set preserveQueryParams(value) {\n    if (isDevMode() && console && console.warn) {\n      console.warn('preserveQueryParams is deprecated!, use queryParamsHandling instead.');\n    }\n    this.preserve = value;\n  }\n  onClick() {\n    const extras = {\n      skipLocationChange: attrBoolValue(this.skipLocationChange),\n      replaceUrl: attrBoolValue(this.replaceUrl)\n    };\n    if (this.stopLink) {\n      return false;\n    }\n    this.router.navigateByUrl(this.urlTree, extras);\n    return true;\n  }\n  get urlTree() {\n    return this.router.createUrlTree(this.commands, {\n      relativeTo: this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: attrBoolValue(this.preserveFragment)\n    });\n  }\n  static ɵfac = function OwlRouterLinkDirective_Factory(t) {\n    return new (t || OwlRouterLinkDirective)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: OwlRouterLinkDirective,\n    selectors: [[\"\", \"owlRouterLink\", \"\", 5, \"a\"]],\n    hostBindings: function OwlRouterLinkDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function OwlRouterLinkDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      queryParams: \"queryParams\",\n      fragment: \"fragment\",\n      queryParamsHandling: \"queryParamsHandling\",\n      preserveFragment: \"preserveFragment\",\n      skipLocationChange: \"skipLocationChange\",\n      replaceUrl: \"replaceUrl\",\n      stopLink: \"stopLink\",\n      owlRouterLink: \"owlRouterLink\",\n      preserveQueryParams: \"preserveQueryParams\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OwlRouterLinkDirective, [{\n    type: Directive,\n    args: [{\n      selector: ':not(a)[owlRouterLink]',\n      standalone: false\n    }]\n  }], () => [{\n    type: i1.Router\n  }, {\n    type: i1.ActivatedRoute\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    queryParams: [{\n      type: Input\n    }],\n    fragment: [{\n      type: Input\n    }],\n    queryParamsHandling: [{\n      type: Input\n    }],\n    preserveFragment: [{\n      type: Input\n    }],\n    skipLocationChange: [{\n      type: Input\n    }],\n    replaceUrl: [{\n      type: Input\n    }],\n    stopLink: [{\n      type: Input\n    }],\n    owlRouterLink: [{\n      type: Input\n    }],\n    preserveQueryParams: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\n/**\n * @description\n *\n * Lets you link to specific routes in your app.\n *\n * See `RouterLink` for more information.\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass OwlRouterLinkWithHrefDirective {\n  router;\n  route;\n  locationStrategy;\n  // TODO(issue/24571): remove '!'.\n  target;\n  // TODO(issue/24571): remove '!'.\n  queryParams;\n  // TODO(issue/24571): remove '!'.\n  fragment;\n  // TODO(issue/24571): remove '!'.\n  queryParamsHandling;\n  // TODO(issue/24571): remove '!'.\n  preserveFragment;\n  // TODO(issue/24571): remove '!'.\n  skipLocationChange;\n  // TODO(issue/24571): remove '!'.\n  replaceUrl;\n  stopLink = false;\n  commands = [];\n  subscription;\n  // TODO(issue/24571): remove '!'.\n  preserve;\n  // the url displayed on the anchor element.\n  // TODO(issue/24571): remove '!'.\n  href;\n  constructor(router, route, locationStrategy) {\n    this.router = router;\n    this.route = route;\n    this.locationStrategy = locationStrategy;\n    this.subscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.updateTargetUrlAndHref();\n      }\n    });\n  }\n  set owlRouterLink(commands) {\n    if (commands != null) {\n      this.commands = Array.isArray(commands) ? commands : [commands];\n    } else {\n      this.commands = [];\n    }\n  }\n  set preserveQueryParams(value) {\n    if (isDevMode() && console && console.warn) {\n      console.warn('preserveQueryParams is deprecated, use queryParamsHandling instead.');\n    }\n    this.preserve = value;\n  }\n  ngOnChanges(changes) {\n    this.updateTargetUrlAndHref();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  onClick(button, ctrlKey, metaKey, shiftKey) {\n    if (button !== 0 || ctrlKey || metaKey || shiftKey) {\n      return true;\n    }\n    if (typeof this.target === 'string' && this.target !== '_self') {\n      return true;\n    }\n    if (this.stopLink) {\n      return false;\n    }\n    const extras = {\n      skipLocationChange: attrBoolValue(this.skipLocationChange),\n      replaceUrl: attrBoolValue(this.replaceUrl)\n    };\n    this.router.navigateByUrl(this.urlTree, extras);\n    return false;\n  }\n  updateTargetUrlAndHref() {\n    this.href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.urlTree));\n  }\n  get urlTree() {\n    return this.router.createUrlTree(this.commands, {\n      relativeTo: this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: attrBoolValue(this.preserveFragment)\n    });\n  }\n  static ɵfac = function OwlRouterLinkWithHrefDirective_Factory(t) {\n    return new (t || OwlRouterLinkWithHrefDirective)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: OwlRouterLinkWithHrefDirective,\n    selectors: [[\"a\", \"owlRouterLink\", \"\"]],\n    hostVars: 2,\n    hostBindings: function OwlRouterLinkWithHrefDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function OwlRouterLinkWithHrefDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event.button, $event.ctrlKey, $event.metaKey, $event.shiftKey);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"href\", ctx.href, i0.ɵɵsanitizeUrl);\n        i0.ɵɵattribute(\"target\", ctx.target);\n      }\n    },\n    inputs: {\n      target: \"target\",\n      queryParams: \"queryParams\",\n      fragment: \"fragment\",\n      queryParamsHandling: \"queryParamsHandling\",\n      preserveFragment: \"preserveFragment\",\n      skipLocationChange: \"skipLocationChange\",\n      replaceUrl: \"replaceUrl\",\n      stopLink: \"stopLink\",\n      owlRouterLink: \"owlRouterLink\",\n      preserveQueryParams: \"preserveQueryParams\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OwlRouterLinkWithHrefDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[owlRouterLink]',\n      standalone: false\n    }]\n  }], () => [{\n    type: i1.Router\n  }, {\n    type: i1.ActivatedRoute\n  }, {\n    type: i3.LocationStrategy\n  }], {\n    target: [{\n      type: HostBinding,\n      args: ['attr.target']\n    }, {\n      type: Input\n    }],\n    queryParams: [{\n      type: Input\n    }],\n    fragment: [{\n      type: Input\n    }],\n    queryParamsHandling: [{\n      type: Input\n    }],\n    preserveFragment: [{\n      type: Input\n    }],\n    skipLocationChange: [{\n      type: Input\n    }],\n    replaceUrl: [{\n      type: Input\n    }],\n    stopLink: [{\n      type: Input\n    }],\n    href: [{\n      type: HostBinding\n    }],\n    owlRouterLink: [{\n      type: Input\n    }],\n    preserveQueryParams: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event.button', '$event.ctrlKey', '$event.metaKey', '$event.shiftKey']]\n    }]\n  });\n})();\nfunction attrBoolValue(s) {\n  return s === '' || !!s;\n}\n\n/**\n * Data which will be passed out after ending of transition of carousel\n */\nclass SlidesOutputData {\n  startPosition;\n  slides;\n}\n;\nconst routes = [];\nclass CarouselModule {\n  static ɵfac = function CarouselModule_Factory(t) {\n    return new (t || CarouselModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CarouselModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [WINDOW_PROVIDERS, ResizeService, DOCUMENT_PROVIDERS, OwlLogger],\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule\n      // BrowserAnimationsModule, // there's an issue with this import while using lazy loading of module consuming this library. I don't remove it because it could be needed during future enhancement of this lib.\n      // RouterModule.forChild(routes)\n      ],\n      declarations: [CarouselComponent, CarouselSlideDirective, StageComponent, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective],\n      exports: [CarouselComponent, CarouselSlideDirective, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective],\n      providers: [WINDOW_PROVIDERS, ResizeService, DOCUMENT_PROVIDERS, OwlLogger]\n    }]\n  }], null, null);\n})();\nclass SlideModel {\n  /**\n   * Id of slide\n   */\n  id;\n  /**\n   * Active state of slide. If true slide gets css-class .active\n   */\n  isActive;\n  /**\n   * TemplateRef of slide. In other words its html-markup\n   */\n  tplRef;\n  /**\n   * Number of grid parts to be used\n   */\n  dataMerge;\n  /**\n   * Width of slide\n   */\n  width;\n  /**\n   * Css-rule 'margin-left'\n   */\n  marginL;\n  /**\n   * Css-rule 'margin-right'\n   */\n  marginR;\n  /**\n   * Make slide to be on center of the carousel\n   */\n  isCentered;\n  /**\n   * Mark slide to be on center of the carousel (has .center)\n   */\n  center;\n  /**\n   * Cloned slide. It's being used when 'loop'=true\n   */\n  isCloned;\n  /**\n   * Indicates whether slide should be lazy loaded\n   */\n  load;\n  /**\n   * Css-rule 'left'\n   */\n  left;\n  /**\n   * Changeable classes of slide\n   */\n  classes;\n  /**\n   * Shows whether slide could be animated and could have css-class '.animated'\n   */\n  isAnimated;\n  /**\n   * Shows whether slide could be animated-in and could have css-class '.owl-animated-in'\n   */\n  isDefAnimatedIn;\n  /**\n   * Shows whether slide could be animated-out and could have css-class '.owl-animated-out'\n   */\n  isDefAnimatedOut;\n  /**\n   * Shows whether slide could be animated-in and could have animation css-class defined by user\n   */\n  isCustomAnimatedIn;\n  /**\n   * Shows whether slide could be animated-out and could have animation css-class defined by user\n   */\n  isCustomAnimatedOut;\n  /**\n   * State for defining the height of slide.It's values could be 'full' and 'nulled'. 'Full' sets css-height to 'auto', 'nulled' sets height to '0'.\n   */\n  heightState;\n  /**\n   * Hash (fragment) of url which corresponds to slide\n   */\n  hashFragment;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CarouselComponent, CarouselModule, CarouselSlideDirective, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective, SlideModel, SlidesOutputData };", "map": {"version": 3, "names": ["i0", "isDevMode", "Injectable", "InjectionToken", "PLATFORM_ID", "Inject", "Optional", "input", "Directive", "HostListener", "Component", "output", "signal", "ContentChildren", "ChangeDetectionStrategy", "Input", "Attribute", "HostBinding", "NgModule", "i3", "isPlatformBrowser", "CommonModule", "toObservable", "Subject", "merge", "of", "fromEvent", "from", "tap", "filter", "switchMap", "first", "take", "skip", "map", "toArray", "pairwise", "delay", "i1", "NavigationEnd", "trigger", "state", "transition", "style", "animate", "_forTrack0", "$index", "$item", "id", "_c0", "a0", "a1", "a2", "a3", "a4", "_c1", "_c2", "$implicit", "index", "StageComponent_For_3_Conditional_1_ng_template_0_Template", "rf", "ctx", "StageComponent_For_3_Conditional_1_Template", "ɵɵtemplate", "ctx_r3", "ɵɵnextContext", "slide_r2", "i_r5", "ctx_r2", "ɵɵproperty", "tplRef", "ɵɵpureFunction2", "preparePublicSlide", "StageComponent_For_3_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "StageComponent_For_3_Template_div_animationend_0_listener", "ɵɵrestoreView", "ɵɵresetView", "clear", "ɵɵelementEnd", "classes", "ɵɵpureFunction4", "width", "marginL", "marginR", "left", "heightState", "ɵɵadvance", "ɵɵconditional", "load", "_c3", "_c4", "_c5", "_c6", "CarouselComponent_Conditional_2_Template", "ɵɵelement", "tmp_2_0", "ctx_r1", "owlDOMData", "isMouseDragable", "isTouchDragable", "stageData", "slidesData", "CarouselComponent_Conditional_3_For_5_Template", "_r4", "CarouselComponent_Conditional_3_For_5_Template_div_click_0_listener", "dot_r5", "moveByDot", "active", "showInnerContent", "innerContent", "ɵɵsanitizeHtml", "CarouselComponent_Conditional_3_Template", "_r3", "CarouselComponent_Conditional_3_Template_div_click_1_listener", "prev", "CarouselComponent_Conditional_3_Template_div_click_2_listener", "next", "ɵɵrepeaterCreate", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "ɵɵpureFunction1", "navData", "disabled", "htmlText", "dotsData", "ɵɵrepeater", "dots", "OwlCarouselOConfig", "items", "skip_validateItems", "loop", "center", "rewind", "mouseDrag", "touchDrag", "pullDrag", "freeDrag", "margin", "stagePadding", "mergeFit", "autoWidth", "startPosition", "rtl", "smartSpeed", "fluidSpeed", "dragEndSpeed", "responsive", "responsiveRefreshRate", "nav", "navText", "navSpeed", "slideBy", "dotsEach", "dotsSpeed", "autoplay", "autoplayTimeout", "autoplayHoverPause", "autoplaySpeed", "autoplayMouseleaveTimeout", "lazyLoad", "lazyLoadEager", "slideTransition", "animateOut", "animateIn", "autoHeight", "URLhashListener", "constructor", "OwlOptionsMockedTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "log", "value", "rest", "console", "error", "handleError", "warn", "ɵfac", "OwlLogger_Factory", "t", "ɵɵinject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "States", "current", "tags", "Type", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "x", "y", "CarouselCurrentData", "CarouselService", "logger", "_viewSettingsShipper$", "_initializedCarousel$", "_changeSettingsCarousel$", "_changedSettingsCarousel$", "_translateCarousel$", "_translatedCarousel$", "_resizeCarousel$", "_resizedCarousel$", "_refreshCarousel$", "_refreshedCarousel$", "_dragCarousel$", "_draggedCarousel$", "settings", "isResponsive", "isRefreshed", "isLoaded", "isLoading", "isGrab", "transform", "paddingL", "paddingR", "_width", "_items", "_widths", "_supress", "_plugins", "_current", "_clones", "_mergers", "_speed", "_coordinates", "_breakpoint", "clonedIdPrefix", "_options", "_invalidated", "invalidated", "_states", "initializing", "animating", "dragging", "states", "_pipe", "run", "cache", "relative", "grid", "css", "for<PERSON>ach", "slide", "toFixed", "widths", "iterator", "length", "Math", "min", "i", "clones", "view", "max", "size", "ceil", "append", "prepend", "repeat", "push", "normalize", "unshift", "isActive", "isCloned", "concat", "coordinates", "previous", "padding", "abs", "findIndex", "minimum", "maximum", "reset", "matches", "begin", "end", "inner", "outer", "n", "result", "element", "_op", "item", "isCentered", "getViewCurSettings", "asObservable", "getInitializedState", "getChangeState", "getChangedState", "getTranslateState", "getTranslatedState", "getResizeState", "getResizedState", "getRefreshState", "getRefreshedState", "getDragState", "getDraggedState", "setOptions", "options", "configOptions", "checkedOptions", "_validateOptions", "mockedTypes", "setRightOption", "key", "hasOwnProperty", "_isNumeric", "_validateItems", "_isNumberOrBoolean", "_isNumberOrString", "_isStringOrBoolean", "Array", "isArray", "isString", "setCarouselWidth", "setup", "carouselWidth", "slides", "setItems", "_defineSlidesData", "setOptionsForViewport", "_trigger", "property", "name", "invalidate", "viewport", "overwrites", "match", "Object", "keys", "Number", "mergers", "mergeN", "dataMerge", "initialize", "enter", "refresh", "sendChanges", "leave", "_optionsLogic", "update", "filteredPipe", "all", "setCurSlideClasses", "is", "dimension", "<PERSON><PERSON><PERSON>", "Inner", "Outer", "onResize", "cur<PERSON><PERSON>th", "prepareDragging", "event", "stage", "transformArr", "replace", "split", "speed", "enterDragging", "defineNewCoordsDrag", "dragData", "pull", "delta", "difference", "pointer", "start", "finishDragging", "dragObj", "click<PERSON>ttacher", "directions", "direction", "currentSlideI", "newCurrent", "closest", "undefined", "Date", "getTime", "time", "coordinate", "position", "onTransitionEnd", "part", "_suppress", "_release", "m", "reciprocalItemsWidth", "elementWidth", "slice", "odd", "even", "v", "multiplier", "newPosition", "_duration", "to", "factor", "revert", "distance", "delayForLoop", "setTimeout", "_viewport", "content", "loadMap", "Map", "set", "get", "hashFragment", "dataHash", "currentClasses", "isAnimated", "isDefAnimatedIn", "isDefAnimatedOut", "isCustomAnimatedIn", "isCustomAnimatedOut", "a", "o", "b", "data", "namespace", "stateName", "register", "object", "State", "tag", "indexOf", "events", "originalEvent", "window", "touches", "changedTouches", "pageX", "pageY", "clientX", "clientY", "number", "isNaN", "parseFloat", "second", "CarouselService_Factory", "NavigationService", "carouselService", "navSubscription", "_initialized", "_pages", "_navData", "_dotsData", "spyDataStreams", "ngOnDestroy", "unsubscribe", "initializedCarousel$", "pipe", "_updateNavPages", "draw", "changedSettings$", "refreshedCarousel$", "navMerge$", "subscribe", "j", "k", "lower", "upper", "pages", "floor", "dotContent", "startI", "splice", "_updateNavButtons", "_updateDots", "curActiveDotI", "finalCurrent", "page", "pop", "_getPosition", "successor", "standard", "dotId", "dot", "toSlideById", "NavigationService_Factory", "WINDOW", "WindowRef", "nativeWindow", "Error", "BrowserWindowRef", "BrowserWindowRef_Factory", "windowFactory", "browserWindowRef", "platformId", "obj", "func", "clearTimeout", "browserWindowProvider", "provide", "useClass", "windowProvider", "useFactory", "deps", "WINDOW_PROVIDERS", "DOCUMENT", "DocumentRef", "nativeDocument", "BrowserDocumentRef", "document", "BrowserDocumentRef_Factory", "documentFactory", "browserDocumentRef", "doc", "hidden", "visibilityState", "browserDocumentProvider", "documentProvider", "DOCUMENT_PROVIDERS", "AutoplayService", "ngZone", "autoplaySubscription", "_timeout", "_paused", "_isArtificialAutoplayTimeout", "_isAutoplayStopped", "isAutoplayStopped", "winRef", "doc<PERSON>ef", "play", "_handleChangeObservable", "resized$", "stop", "autoplayMerge$", "timeout", "_setAutoPlayInterval", "_getNextTimeout", "runOutsideAngular", "pause", "_playAfterTranslated", "startPausing", "startPlayingMouseLeave", "startPlayingTouchEnd", "AutoplayService_Factory", "NgZone", "decorators", "args", "LazyLoadService", "lazyLoadSubscription", "isLazyLoad", "changeSettings$", "resizedCarousel$", "lazyLoadMerge$", "_defineLazyLoadSlides", "_load", "LazyLoadService_Factory", "AnimateService", "animateSubscription", "swapping", "dragCarousel$", "draggedCarousel$", "translatedCarousel$", "dragTranslatedMerge$", "translateCarousel$", "_swap", "animateMerge$", "incoming", "outgoing", "AnimateService_Factory", "AutoHeightService", "autoHeightSubscription", "autoHeight$", "AutoHeightService_Factory", "HashService", "route", "router", "hashSubscription", "currentHashFragment", "fragment", "navigate", "commands", "extras", "listenToRoute", "newCurSlide", "newCurFragment", "relativeTo", "hashFragment$", "count", "HashService_Factory", "ActivatedRoute", "Router", "nextId", "CarouselSlideDirective", "isNumeric", "CarouselSlideDirective_Factory", "ɵɵdirectiveInject", "TemplateRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "ɵɵInputFlags", "SignalBased", "selector", "standalone", "ResizeService", "resizeObservable$", "onResize$", "fullscreenElement", "ResizeService_Factory", "StageComponent", "zone", "el", "renderer", "animateService", "owlDraggable", "listenerMouseMove", "listenerTouchMove", "listenerOneMouseMove", "listenerOneTouchMove", "listenerMouseUp", "listenerTouchEnd", "listenerOneClick", "listenerATag", "_drag", "target", "moving", "_oneDragMove$", "_oneMoveSubsription", "newSlide", "onMouseDown", "_onDragStart", "onTouchStart", "targetTouches", "onTouchCancel", "_onDragEnd", "onDragStart", "onSelectStart", "ngOnInit", "_sendChanges", "bindOneMouseTouchMove", "ev", "_oneMouseTouchMove", "bindOnDragMove", "_onDragMove", "bindOnDragEnd", "which", "_prepareDragging", "_pointer", "listen", "_difference", "_is", "blockClickAnchorInDragging", "preventDefault", "_enterDragging", "HTMLAnchorElement", "parentElement", "stageOrExit", "_animate", "setStyle", "nativeElement", "children", "_finishDragging", "_oneClickHandler", "firstC", "specificState", "_enter", "StageComponent_Factory", "ElementRef", "Renderer2", "ɵcmp", "ɵɵdefineComponent", "hostBindings", "StageComponent_HostBindings", "StageComponent_mousedown_HostBindingHandler", "$event", "StageComponent_touchstart_HostBindingHandler", "StageComponent_touchcancel_HostBindingHandler", "StageComponent_dragstart_HostBindingHandler", "StageComponent_selectstart_HostBindingHandler", "decls", "vars", "consts", "template", "StageComponent_Template", "StageComponent_Template_div_transitionend_1_listener", "ɵɵpureFunction5", "dependencies", "Ng<PERSON><PERSON>", "NgTemplateOutlet", "NgStyle", "encapsulation", "animation", "height", "animations", "CarouselComponent", "resizeService", "navigationService", "autoplayService", "lazyLoadService", "autoHeightService", "hashService", "changeDetectorRef", "translated", "change", "changed", "initialized", "carouselWindowWidth", "resizeSubscription", "_allObservSubscription", "_slidesChangesSubscription", "_owlDOMData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_stageData", "_slidesData", "slidesOutputData", "_carouselLoaded", "carouselLoaded", "_options$", "_optionsPrevAndCur$", "_viewCurSettings$", "_draggingCarousel$", "_changeCarousel$", "_changedCarousel$", "_carouselMerge$", "onVisibilityChange", "querySelector", "clientWidth", "ngAfterContentInit", "_winResizeWatcher", "changes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gatherTranslatedData", "emit", "changedPosition", "anim", "cur", "activeSlides", "startPlayML", "startPlayTE", "stopAutoplay", "startAutoplay", "CarouselComponent_Factory", "ChangeDetectorRef", "contentQueries", "CarouselComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "CarouselComponent_HostBindings", "CarouselComponent_visibilitychange_HostBindingHandler", "ɵɵresolveDocument", "outputs", "features", "ɵɵProvidersFeature", "CarouselComponent_Template", "CarouselComponent_Template_div_mouseover_0_listener", "CarouselComponent_Template_div_mouseleave_0_listener", "CarouselComponent_Template_div_touchstart_0_listener", "CarouselComponent_Template_div_touchend_0_listener", "tmp_1_0", "styles", "changeDetection", "providers", "OnPush", "OwlRouterLinkDirective", "queryParams", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "stopLink", "preserve", "tabIndex", "setAttribute", "owlRouterLink", "preserveQueryParams", "onClick", "attrBoolValue", "navigateByUrl", "urlTree", "createUrlTree", "OwlRouterLinkDirective_Factory", "ɵɵinjectAttribute", "OwlRouterLinkDirective_HostBindings", "OwlRouterLinkDirective_click_HostBindingHandler", "OwlRouterLinkWithHrefDirective", "locationStrategy", "subscription", "href", "s", "updateTargetUrlAndHref", "ngOnChanges", "button", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "prepareExternalUrl", "serializeUrl", "OwlRouterLinkWithHrefDirective_Factory", "LocationStrategy", "hostVars", "OwlRouterLinkWithHrefDirective_HostBindings", "OwlRouterLinkWithHrefDirective_click_HostBindingHandler", "ɵɵhostProperty", "ɵɵsanitizeUrl", "ɵɵattribute", "ɵɵNgOnChangesFeature", "SlidesOutputData", "routes", "CarouselModule", "CarouselModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports", "SlideModel"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/ngx-owl-carousel-o/fesm2022/ngx-owl-carousel-o.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { isDevMode, Injectable, InjectionToken, PLATFORM_ID, Inject, Optional, input, Directive, HostListener, Component, output, signal, ContentChildren, ChangeDetectionStrategy, Input, Attribute, HostBinding, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport { toObservable } from '@angular/core/rxjs-interop';\nimport { Subject, merge, of, fromEvent, from } from 'rxjs';\nimport { tap, filter, switchMap, first, take, skip, map, toArray, pairwise, delay } from 'rxjs/operators';\nimport * as i1 from '@angular/router';\nimport { NavigationEnd } from '@angular/router';\nimport { trigger, state, transition, style, animate } from '@angular/animations';\n\n/**\n * Defaults value of options\n */\nclass OwlCarouselOConfig {\n    items = 3;\n    skip_validateItems = false;\n    loop = false;\n    center = false;\n    rewind = false;\n    mouseDrag = true;\n    touchDrag = true;\n    pullDrag = true;\n    freeDrag = false;\n    margin = 0;\n    stagePadding = 0;\n    merge = false;\n    mergeFit = true;\n    autoWidth = false;\n    startPosition = 0;\n    rtl = false;\n    smartSpeed = 250;\n    fluidSpeed = false;\n    dragEndSpeed = false;\n    responsive = {};\n    responsiveRefreshRate = 200;\n    // defaults to Navigation\n    nav = false;\n    navText = ['prev', 'next'];\n    navSpeed = false;\n    slideBy = 1; // stage moves on 1 width of slide; if slideBy = 2, stage moves on 2 widths of slide\n    dots = true;\n    dotsEach = false;\n    dotsData = false;\n    dotsSpeed = false;\n    // defaults to Autoplay\n    autoplay = false;\n    autoplayTimeout = 5000;\n    autoplayHoverPause = false;\n    autoplaySpeed = false;\n    autoplayMouseleaveTimeout = 1;\n    // defaults to LazyLoading\n    lazyLoad = false;\n    lazyLoadEager = 0;\n    // defaults to Animate\n    slideTransition = '';\n    animateOut = false;\n    animateIn = false;\n    // defaults to AutoHeight\n    autoHeight = false;\n    // defaults to Hash\n    URLhashListener = false;\n    constructor() { }\n}\n/**\n * we can't read types from OwlOptions in javascript because of props have undefined value and types of those props are used for validating inputs\n * class below is copy of OwlOptions but its all props have string value showing certain type;\n * this is class is being used just in method _validateOptions() of CarouselService;\n */\nclass OwlOptionsMockedTypes {\n    items = 'number';\n    skip_validateItems = 'boolean';\n    loop = 'boolean';\n    center = 'boolean';\n    rewind = 'boolean';\n    mouseDrag = 'boolean';\n    touchDrag = 'boolean';\n    pullDrag = 'boolean';\n    freeDrag = 'boolean';\n    margin = 'number';\n    stagePadding = 'number';\n    merge = 'boolean';\n    mergeFit = 'boolean';\n    autoWidth = 'boolean';\n    startPosition = 'number|string';\n    rtl = 'boolean';\n    smartSpeed = 'number';\n    fluidSpeed = 'boolean';\n    dragEndSpeed = 'number|boolean';\n    responsive = {};\n    responsiveRefreshRate = 'number';\n    // defaults to Navigation\n    nav = 'boolean';\n    navText = 'string[]';\n    navSpeed = 'number|boolean';\n    slideBy = 'number|string'; // stage moves on 1 width of slide; if slideBy = 2, stage moves on 2 widths of slide\n    dots = 'boolean';\n    dotsEach = 'number|boolean';\n    dotsData = 'boolean';\n    dotsSpeed = 'number|boolean';\n    // defaults to Autoplay\n    autoplay = 'boolean';\n    autoplayTimeout = 'number';\n    autoplayHoverPause = 'boolean';\n    autoplaySpeed = 'number|boolean';\n    autoplayMouseleaveTimeout = 'number';\n    // defaults to LazyLoading\n    lazyLoad = 'boolean';\n    lazyLoadEager = 'number';\n    // defaults to Animate\n    slideTransition = 'string';\n    animateOut = 'string|boolean';\n    animateIn = 'string|boolean';\n    // defaults to AutoHeight\n    autoHeight = 'boolean';\n    // defaults to Hash\n    URLhashListener = \"boolean\";\n    constructor() { }\n}\n\nclass OwlLogger {\n    errorHandler;\n    constructor(errorHandler) {\n        this.errorHandler = errorHandler;\n    }\n    log(value, ...rest) {\n        if (isDevMode()) {\n            console.log(value, ...rest);\n        }\n    }\n    error(error) {\n        this.errorHandler.handleError(error);\n    }\n    warn(value, ...rest) {\n        console.warn(value, ...rest);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: OwlLogger, deps: [{ token: i0.ErrorHandler }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: OwlLogger });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: OwlLogger, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.ErrorHandler }] });\n\n/**\n * Current state information and their tags.\n */\nclass States {\n    current;\n    tags;\n}\n/**\n * Enumeration for types.\n * @enum {String}\n */\nvar Type;\n(function (Type) {\n    Type[\"Event\"] = \"event\";\n    Type[\"State\"] = \"state\";\n})(Type || (Type = {}));\n;\n/**\n * Enumeration for width.\n * @enum {String}\n */\nvar Width;\n(function (Width) {\n    Width[\"Default\"] = \"default\";\n    Width[\"Inner\"] = \"inner\";\n    Width[\"Outer\"] = \"outer\";\n})(Width || (Width = {}));\n;\n/**\n * Model for coords of .owl-stage\n */\nclass Coords {\n    x;\n    y;\n}\n/**\n * Model for all current data of carousel\n */\nclass CarouselCurrentData {\n    owlDOMData;\n    stageData;\n    slidesData;\n    navData;\n    dotsData;\n}\nclass CarouselService {\n    logger;\n    /**\n     * Subject for passing data needed for managing View\n     */\n    _viewSettingsShipper$ = new Subject();\n    /**\n     * Subject for notification when the carousel got initializes\n     */\n    _initializedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel's settings start changinf\n     */\n    _changeSettingsCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel's settings have changed\n     */\n    _changedSettingsCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel starts translating or moving\n     */\n    _translateCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel stopped translating or moving\n     */\n    _translatedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel's rebuilding caused by 'resize' event starts\n     */\n    _resizeCarousel$ = new Subject();\n    /**\n     * Subject for notification  when the carousel's rebuilding caused by 'resize' event is ended\n     */\n    _resizedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the refresh of carousel starts\n     */\n    _refreshCarousel$ = new Subject();\n    /**\n     * Subject for notification when the refresh of carousel is ended\n     */\n    _refreshedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the dragging of carousel starts\n     */\n    _dragCarousel$ = new Subject();\n    /**\n     * Subject for notification when the dragging of carousel is ended\n     */\n    _draggedCarousel$ = new Subject();\n    /**\n     * Current settings for the carousel.\n     */\n    settings = {\n        items: 0\n    };\n    /**\n     * Initial data for setting classes to element .owl-carousel\n     */\n    owlDOMData = {\n        rtl: false,\n        isResponsive: false,\n        isRefreshed: false,\n        isLoaded: false,\n        isLoading: false,\n        isMouseDragable: false,\n        isGrab: false,\n        isTouchDragable: false\n    };\n    /**\n     * Initial data of .owl-stage\n     */\n    stageData = {\n        transform: 'translate3d(0px,0px,0px)',\n        transition: '0s',\n        width: 0,\n        paddingL: 0,\n        paddingR: 0\n    };\n    /**\n     *  Data of every slide\n     */\n    slidesData;\n    /**\n     * Data of navigation block\n     */\n    navData;\n    /**\n     * Data of dots block\n     */\n    dotsData;\n    /**\n     * Carousel width\n     */\n    _width;\n    /**\n     * All real items.\n     */\n    _items = []; // is equal to this.slides\n    /**\n     * Array with width of every slide.\n     */\n    _widths = [];\n    /**\n     * Currently suppressed events to prevent them from beeing retriggered.\n     */\n    _supress = {};\n    /**\n     * References to the running plugins of this carousel.\n     */\n    _plugins = {};\n    /**\n     * Absolute current position.\n     */\n    _current = null;\n    /**\n     * All cloned items.\n     */\n    _clones = [];\n    /**\n     * Merge values of all items.\n     * @todo Maybe this could be part of a plugin.\n     */\n    _mergers = [];\n    /**\n     * Animation speed in milliseconds.\n     */\n    _speed = null;\n    /**\n     * Coordinates of all items in pixel.\n     * @todo The name of this member is missleading.\n     */\n    _coordinates = [];\n    /**\n     * Current breakpoint.\n     * @todo Real media queries would be nice.\n     */\n    _breakpoint = null;\n    /**\n     * Prefix for id of cloned slides\n     */\n    clonedIdPrefix = 'cloned-';\n    /**\n     * Current options set by the caller including defaults.\n     */\n    _options = {};\n    /**\n     * Invalidated parts within the update process.\n     */\n    _invalidated = {};\n    // Is needed for tests\n    get invalidated() {\n        return this._invalidated;\n    }\n    /**\n     * Current state information and their tags.\n     */\n    _states = {\n        current: {},\n        tags: {\n            initializing: ['busy'],\n            animating: ['busy'],\n            dragging: ['interacting']\n        }\n    };\n    // is needed for tests\n    get states() {\n        return this._states;\n    }\n    /**\n         * Ordered list of workers for the update process.\n     */\n    _pipe = [\n        // {\n        //   filter: ['width', 'settings'],\n        //   run: () => {\n        //     this._width = this.carouselWindowWidth;\n        //   }\n        // },\n        {\n            filter: ['width', 'items', 'settings'],\n            run: cache => {\n                cache.current = this._items && this._items[this.relative(this._current)]?.id();\n            }\n        },\n        // {\n        //   filter: ['items', 'settings'],\n        //   run: function() {\n        //     // this.$stage.children('.cloned').remove();\n        //   }\n        // },\n        {\n            filter: ['width', 'items', 'settings'],\n            run: (cache) => {\n                const margin = this.settings.margin || '', grid = !this.settings.autoWidth, rtl = this.settings.rtl, css = {\n                    'margin-left': rtl ? margin : '',\n                    'margin-right': rtl ? '' : margin\n                };\n                if (!grid) {\n                    this.slidesData.forEach(slide => {\n                        slide.marginL = css['margin-left'];\n                        slide.marginR = css['margin-right'];\n                    });\n                }\n                cache.css = css;\n            }\n        }, {\n            filter: ['width', 'items', 'settings'],\n            run: (cache) => {\n                const width = +(this.width() / (this.settings.items || 1)).toFixed(3) - (this.settings.margin || 0), grid = !this.settings.autoWidth, widths = [];\n                let merge = 0, iterator = this._items.length;\n                cache.items = {\n                    merge: false,\n                    width: width\n                };\n                while (iterator-- > 0) {\n                    merge = this._mergers[iterator] || 1;\n                    merge = this.settings.mergeFit && Math.min(merge, this.settings.items || 1) || merge;\n                    cache.items.merge = merge > 1 || cache.items.merge;\n                    widths[iterator] = !grid ? this._items[iterator].width() ? this._items[iterator].width() : width : width * merge;\n                }\n                this._widths = widths;\n                this.slidesData.forEach((slide, i) => {\n                    slide.width = this._widths[i];\n                    slide.marginR = cache.css['margin-right'];\n                    slide.marginL = cache.css['margin-left'];\n                });\n            }\n        }, {\n            filter: ['items', 'settings'],\n            run: () => {\n                const clones = [], items = this._items, settings = this.settings, \n                // TODO: Should be computed from number of min width items in stage\n                view = Math.max(settings.items * 2, 4), size = Math.ceil(items.length / 2) * 2;\n                let append = [], prepend = [], repeat = settings.loop && items.length ? settings.rewind ? view : Math.max(view, size) : 0;\n                repeat /= 2;\n                while (repeat-- > 0) {\n                    // Switch to only using appended clones\n                    clones.push(this.normalize(clones.length / 2, true));\n                    append.push({ ...this.slidesData[clones[clones.length - 1]] });\n                    clones.push(this.normalize(items.length - 1 - (clones.length - 1) / 2, true));\n                    prepend.unshift({ ...this.slidesData[clones[clones.length - 1]] });\n                }\n                this._clones = clones;\n                append = append.map(slide => ({\n                    ...slide,\n                    id: `${this.clonedIdPrefix}${slide.id}-append`,\n                    isActive: false,\n                    isCloned: true,\n                }));\n                prepend = prepend.map(slide => ({\n                    ...slide,\n                    id: `${this.clonedIdPrefix}${slide.id}`,\n                    isActive: false,\n                    isCloned: true,\n                }));\n                this.slidesData = prepend.concat(this.slidesData).concat(append);\n            }\n        }, {\n            filter: ['width', 'items', 'settings'],\n            run: () => {\n                const rtl = this.settings.rtl ? 1 : -1, size = this._clones.length + this._items.length, coordinates = [];\n                let iterator = -1, previous = 0, current = 0;\n                while (++iterator < size) {\n                    previous = coordinates[iterator - 1] || 0;\n                    current = this._widths[this.relative(iterator)] + this.settings.margin;\n                    coordinates.push(previous + current * rtl);\n                }\n                this._coordinates = coordinates;\n            }\n        }, {\n            filter: ['width', 'items', 'settings'],\n            run: () => {\n                const padding = this.settings.stagePadding || 0, coordinates = this._coordinates, css = {\n                    'width': Math.ceil(Math.abs(coordinates[coordinates.length - 1])) + padding * 2,\n                    'padding-left': padding || '',\n                    'padding-right': padding || ''\n                };\n                this.stageData.width = css.width; // use this property in *ngIf directive for .owl-stage element\n                this.stageData.paddingL = css['padding-left'];\n                this.stageData.paddingR = css['padding-right'];\n            }\n        }, {\n            //   filter: [ 'width', 'items', 'settings' ],\n            //   run: cache => {\n            // \t\t// this method sets the width for every slide, but I set it in different way earlier\n            // \t\tconst grid = !this.settings.autoWidth,\n            // \t\titems = this.$stage.children(); // use this.slidesData\n            //     let iterator = this._coordinates.length;\n            //     if (grid && cache.items.merge) {\n            //       while (iterator--) {\n            //         cache.css.width = this._widths[this.relative(iterator)];\n            //         items.eq(iterator).css(cache.css);\n            //       }\n            //     } else if (grid) {\n            //       cache.css.width = cache.items.width;\n            //       items.css(cache.css);\n            //     }\n            //   }\n            // }, {\n            //   filter: [ 'items' ],\n            //   run: function() {\n            //     this._coordinates.length < 1 && this.$stage.removeAttr('style');\n            //   }\n            // }, {\n            filter: ['width', 'items', 'settings'],\n            run: cache => {\n                let current = cache.current ? this.slidesData.findIndex(slide => slide.id === cache.current) : 0;\n                current = Math.max(this.minimum(), Math.min(this.maximum(), current));\n                this.reset(current);\n            }\n        }, {\n            filter: ['position'],\n            run: () => {\n                this.animate(this.coordinates(this._current));\n            }\n        }, {\n            filter: ['width', 'position', 'items', 'settings'],\n            run: () => {\n                const rtl = this.settings.rtl ? 1 : -1, padding = (this.settings.stagePadding || 0) * 2, matches = [];\n                let begin, end, inner, outer, i, n;\n                begin = this.coordinates(this.current());\n                if (typeof begin === 'number') {\n                    begin += padding;\n                }\n                else {\n                    begin = 0;\n                }\n                end = begin + this.width() * rtl;\n                if (rtl === -1 && this.settings.center) {\n                    const result = this._coordinates.filter(element => {\n                        return (this.settings.items || 1) % 2 === 1 ? element >= begin : element > begin;\n                    });\n                    begin = result.length ? result[result.length - 1] : begin;\n                }\n                for (i = 0, n = this._coordinates.length; i < n; i++) {\n                    inner = Math.ceil(this._coordinates[i - 1] || 0);\n                    outer = Math.ceil(Math.abs(this._coordinates[i]) + padding * rtl);\n                    if ((this._op(inner, '<=', begin) && (this._op(inner, '>', end)))\n                        || (this._op(outer, '<', begin) && this._op(outer, '>', end))) {\n                        matches.push(i);\n                    }\n                }\n                this.slidesData.forEach(slide => {\n                    slide.isActive = false;\n                    return slide;\n                });\n                matches.forEach(item => {\n                    this.slidesData[item].isActive = true;\n                });\n                if (this.settings.center) {\n                    this.slidesData.forEach(slide => {\n                        slide.isCentered = false;\n                        return slide;\n                    });\n                    if (this.slidesData[this.current()]) {\n                        this.slidesData[this.current()].isCentered = true;\n                    }\n                }\n            }\n        }\n    ];\n    constructor(logger) {\n        this.logger = logger;\n    }\n    /**\n     * Makes _viewSettingsShipper$ Subject become Observable\n     * @returns Observable of _viewSettingsShipper$ Subject\n     */\n    getViewCurSettings() {\n        return this._viewSettingsShipper$.asObservable();\n    }\n    /**\n     * Makes _initializedCarousel$ Subject become Observable\n     * @returns Observable of _initializedCarousel$ Subject\n     */\n    getInitializedState() {\n        return this._initializedCarousel$.asObservable();\n    }\n    /**\n     * Makes _changeSettingsCarousel$ Subject become Observable\n     * @returns Observable of _changeSettingsCarousel$ Subject\n     */\n    getChangeState() {\n        return this._changeSettingsCarousel$.asObservable();\n    }\n    /**\n     * Makes _changedSettingsCarousel$ Subject become Observable\n     * @returns Observable of _changedSettingsCarousel$ Subject\n     */\n    getChangedState() {\n        return this._changedSettingsCarousel$.asObservable();\n    }\n    /**\n     * Makes _translateCarousel$ Subject become Observable\n     * @returns Observable of _translateCarousel$ Subject\n     */\n    getTranslateState() {\n        return this._translateCarousel$.asObservable();\n    }\n    /**\n     * Makes _translatedCarousel$ Subject become Observable\n     * @returns Observable of _translatedCarousel$ Subject\n     */\n    getTranslatedState() {\n        return this._translatedCarousel$.asObservable();\n    }\n    /**\n     * Makes _resizeCarousel$ Subject become Observable\n     * @returns Observable of _resizeCarousel$ Subject\n     */\n    getResizeState() {\n        return this._resizeCarousel$.asObservable();\n    }\n    /**\n     * Makes _resizedCarousel$ Subject become Observable\n     * @returns Observable of _resizedCarousel$ Subject\n     */\n    getResizedState() {\n        return this._resizedCarousel$.asObservable();\n    }\n    /**\n     * Makes _refreshCarousel$ Subject become Observable\n     * @returns Observable of _refreshCarousel$ Subject\n     */\n    getRefreshState() {\n        return this._refreshCarousel$.asObservable();\n    }\n    /**\n     * Makes _refreshedCarousel$ Subject become Observable\n     * @returns Observable of _refreshedCarousel$ Subject\n     */\n    getRefreshedState() {\n        return this._refreshedCarousel$.asObservable();\n    }\n    /**\n     * Makes _dragCarousel$ Subject become Observable\n     * @returns Observable of _dragCarousel$ Subject\n     */\n    getDragState() {\n        return this._dragCarousel$.asObservable();\n    }\n    /**\n     * Makes _draggedCarousel$ Subject become Observable\n     * @returns Observable of _draggedCarousel$ Subject\n     */\n    getDraggedState() {\n        return this._draggedCarousel$.asObservable();\n    }\n    /**\n     * Setups custom options expanding default options\n     * @param options custom options\n     */\n    setOptions(options) {\n        const configOptions = new OwlCarouselOConfig();\n        const checkedOptions = this._validateOptions(options, configOptions);\n        this._options = { ...configOptions, ...checkedOptions };\n    }\n    /**\n     * Checks whether user's option are set properly. Cheking is based on typings;\n     * @param options options set by user\n     * @param configOptions default options\n     * @returns checked and modified (if it's needed) user's options\n     *\n     * Notes:\n     * \t- if user set option with wrong type, it'll be written in console\n     */\n    _validateOptions(options, configOptions) {\n        const checkedOptions = { ...options };\n        const mockedTypes = new OwlOptionsMockedTypes();\n        const setRightOption = (type, key) => {\n            this.logger.log(`options.${key} must be type of ${type}; ${key}=${options[key]} skipped to defaults: ${key}=${configOptions[key]}`);\n            return configOptions[key];\n        };\n        for (const key in checkedOptions) {\n            if (checkedOptions.hasOwnProperty(key)) {\n                // condition could be shortened but it gets harder for understanding\n                if (mockedTypes[key] === 'number') {\n                    if (this._isNumeric(checkedOptions[key])) {\n                        checkedOptions[key] = +checkedOptions[key];\n                        checkedOptions[key] = key === 'items'\n                            ? this._validateItems(checkedOptions[key], checkedOptions.skip_validateItems)\n                            : checkedOptions[key];\n                    }\n                    else {\n                        checkedOptions[key] = setRightOption(mockedTypes[key], key);\n                    }\n                }\n                else if (mockedTypes[key] === 'boolean' && typeof checkedOptions[key] !== 'boolean') {\n                    checkedOptions[key] = setRightOption(mockedTypes[key], key);\n                }\n                else if (mockedTypes[key] === 'number|boolean' && !this._isNumberOrBoolean(checkedOptions[key])) {\n                    checkedOptions[key] = setRightOption(mockedTypes[key], key);\n                }\n                else if (mockedTypes[key] === 'number|string' && !this._isNumberOrString(checkedOptions[key])) {\n                    checkedOptions[key] = setRightOption(mockedTypes[key], key);\n                }\n                else if (mockedTypes[key] === 'string|boolean' && !this._isStringOrBoolean(checkedOptions[key])) {\n                    checkedOptions[key] = setRightOption(mockedTypes[key], key);\n                }\n                else if (mockedTypes[key] === 'string[]') {\n                    if (Array.isArray(checkedOptions[key])) {\n                        let isString = false;\n                        checkedOptions[key].forEach(element => {\n                            isString = typeof element === 'string' ? true : false;\n                        });\n                        if (!isString) {\n                            checkedOptions[key] = setRightOption(mockedTypes[key], key);\n                        }\n                        ;\n                    }\n                    else {\n                        checkedOptions[key] = setRightOption(mockedTypes[key], key);\n                    }\n                }\n            }\n        }\n        return checkedOptions;\n    }\n    /**\n     * Checks the option `items` set by user and if it bigger than number of slides, the function returns number of slides\n     * @param items option items set by user\n     * @param skip_validateItems option `skip_validateItems` set by user\n     * @returns right number of items\n     */\n    _validateItems(items, skip_validateItems) {\n        let result = items;\n        if (items > this._items.length) {\n            if (skip_validateItems) {\n                this.logger.log('The option \\'items\\' in your options is bigger than the number of slides. The navigation got disabled');\n            }\n            else {\n                result = this._items.length;\n                this.logger.log('The option \\'items\\' in your options is bigger than the number of slides. This option is updated to the current number of slides and the navigation got disabled');\n            }\n        }\n        else {\n            if (items === this._items.length && (this.settings.dots || this.settings.nav)) {\n                this.logger.log('Option \\'items\\' in your options is equal to the number of slides. So the navigation got disabled');\n            }\n        }\n        return result;\n    }\n    /**\n     * Set current width of carousel\n     * @param width width of carousel Window\n     */\n    setCarouselWidth(width) {\n        this._width = width;\n    }\n    /**\n     * Setups the current settings.\n     * @todo Remove responsive classes. Why should adaptive designs be brought into IE8?\n     * @todo Support for media queries by using `matchMedia` would be nice.\n     * @param carouselWidth width of carousel\n     * @param slides array of slides\n     * @param options options set by user\n     */\n    setup(carouselWidth, slides, options) {\n        this.setCarouselWidth(carouselWidth);\n        this.setItems(slides);\n        this._defineSlidesData();\n        this.setOptions(options);\n        this.settings = { ...this._options };\n        this.setOptionsForViewport();\n        this._trigger('change', { property: { name: 'settings', value: this.settings } });\n        this.invalidate('settings'); // must be call of this function;\n        this._trigger('changed', { property: { name: 'settings', value: this.settings } });\n    }\n    /**\n     * Set options for current viewport\n     */\n    setOptionsForViewport() {\n        const viewport = this._width, overwrites = this._options.responsive || {};\n        let match = -1;\n        if (!Object.keys(overwrites).length) {\n            return;\n        }\n        if (!viewport) {\n            this.settings.items = 1;\n            return;\n        }\n        for (const key in overwrites) {\n            if (overwrites.hasOwnProperty(key)) {\n                if (+key <= viewport && +key > match) {\n                    match = Number(key);\n                }\n            }\n        }\n        this.settings = {\n            ...this._options,\n            ...overwrites[match],\n            items: (overwrites[match] && overwrites[match].items)\n                ? this._validateItems(overwrites[match].items, this._options.skip_validateItems)\n                : this._options.items\n        };\n        // if (typeof this.settings.stagePadding === 'function') {\n        // \tthis.settings.stagePadding = this.settings.stagePadding();\n        // }\n        delete this.settings.responsive;\n        this.owlDOMData.isResponsive = true;\n        this.owlDOMData.isMouseDragable = this.settings.mouseDrag;\n        this.owlDOMData.isTouchDragable = this.settings.touchDrag;\n        const mergers = [];\n        this._items.forEach(item => {\n            const mergeN = this.settings.merge ? item.dataMerge() : 1;\n            mergers.push(mergeN);\n        });\n        this._mergers = mergers;\n        this._breakpoint = match;\n        this.invalidate('settings');\n    }\n    /**\n     * Initializes the carousel.\n     * @param slides array of CarouselSlideDirective\n     */\n    initialize(slides) {\n        this.enter('initializing');\n        // this.trigger('initialize');\n        this.owlDOMData.rtl = this.settings.rtl;\n        if (this._mergers.length) {\n            this._mergers = [];\n        }\n        slides.forEach(item => {\n            const mergeN = this.settings.merge ? item.dataMerge() : 1;\n            this._mergers.push(mergeN);\n        });\n        this._clones = [];\n        this.reset(this._isNumeric(this.settings.startPosition) ? +(this.settings?.startPosition || 0) : 0);\n        this.invalidate('items');\n        this.refresh();\n        this.owlDOMData.isLoaded = true;\n        this.owlDOMData.isMouseDragable = this.settings.mouseDrag;\n        this.owlDOMData.isTouchDragable = this.settings.touchDrag;\n        this.sendChanges();\n        this.leave('initializing');\n        this._trigger('initialized');\n    }\n    ;\n    /**\n     * Sends all data needed for View\n     */\n    sendChanges() {\n        this._viewSettingsShipper$.next({\n            owlDOMData: this.owlDOMData,\n            stageData: this.stageData,\n            slidesData: this.slidesData,\n            navData: this.navData,\n            dotsData: this.dotsData\n        });\n    }\n    /**\n     * Updates option logic if necessery\n     */\n    _optionsLogic() {\n        if (this.settings.autoWidth) {\n            this.settings.stagePadding = 0;\n            this.settings.merge = false;\n        }\n    }\n    /**\n     * Updates the view\n     */\n    update() {\n        let i = 0;\n        const n = this._pipe.length, filter = item => this._invalidated[item], cache = {};\n        while (i < n) {\n            const filteredPipe = this._pipe[i].filter.filter(filter);\n            if (this._invalidated.all || filteredPipe.length > 0) {\n                this._pipe[i].run(cache);\n            }\n            i++;\n        }\n        this.slidesData.forEach(slide => slide.classes = this.setCurSlideClasses(slide));\n        this.sendChanges();\n        this._invalidated = {};\n        if (!this.is('valid')) {\n            this.enter('valid');\n        }\n    }\n    /**\n     * Gets the width of the view.\n     * @param [dimension=Width.Default] The dimension to return\n     * @returns The width of the view in pixel.\n     */\n    width(dimension) {\n        dimension = dimension || Width.Default;\n        switch (dimension) {\n            case Width.Inner:\n            case Width.Outer:\n                return this._width;\n            default:\n                return this._width - (this.settings.stagePadding || 0) * 2 + (this.settings.margin || 0);\n        }\n    }\n    /**\n     * Refreshes the carousel primarily for adaptive purposes.\n     */\n    refresh() {\n        this.enter('refreshing');\n        this._trigger('refresh');\n        this._defineSlidesData();\n        this.setOptionsForViewport();\n        this._optionsLogic();\n        // this.$element.addClass(this.options.refreshClass);\n        this.update();\n        // this.$element.removeClass(this.options.refreshClass);\n        this.leave('refreshing');\n        this._trigger('refreshed');\n    }\n    /**\n     * Checks window `resize` event.\n     * @param curWidth width of .owl-carousel\n     */\n    onResize(curWidth) {\n        if (!this._items.length) {\n            return false;\n        }\n        this.setCarouselWidth(curWidth);\n        this.enter('resizing');\n        // if (this.trigger('resize').isDefaultPrevented()) {\n        // \tthis.leave('resizing');\n        // \treturn false;\n        // }\n        this._trigger('resize');\n        this.invalidate('width');\n        this.refresh();\n        this.leave('resizing');\n        this._trigger('resized');\n    }\n    /**\n     * Prepares data for dragging carousel. It starts after firing `touchstart` and `mousedown` events.\n     * @todo Horizontal swipe threshold as option\n     * @todo #261\n     * @param event - The event arguments.\n     * @returns stage - object with 'x' and 'y' coordinates of .owl-stage\n     */\n    prepareDragging(event) {\n        let stage, transformArr;\n        // could be 5 commented lines below; However there's stage transform in stageData and in updates after each move of stage\n        // stage = getComputedStyle(this.el.nativeElement).transform.replace(/.*\\(|\\)| /g, '').split(',');\n        // stage = {\n        //   x: stage[stage.length === 16 ? 12 : 4],\n        //   y: stage[stage.length === 16 ? 13 : 5]\n        // };\n        transformArr = this.stageData.transform.replace(/.*\\(|\\)| |[^,-\\d]\\w|\\)/g, '').split(',');\n        stage = {\n            x: +transformArr[0],\n            y: +transformArr[1]\n        };\n        if (this.is('animating')) {\n            this.invalidate('position');\n        }\n        if (event.type === 'mousedown') {\n            this.owlDOMData.isGrab = true;\n        }\n        this.speed(0);\n        return stage;\n    }\n    /**\n     * Enters into a 'dragging' state\n     */\n    enterDragging() {\n        this.enter('dragging');\n        this._trigger('drag');\n    }\n    /**\n     * Defines new coords for .owl-stage while dragging it\n     * @todo #261\n     * @param event the event arguments.\n     * @param dragData initial data got after starting dragging\n     * @returns coords or false\n     */\n    defineNewCoordsDrag(event, dragData) {\n        let minimum, maximum, pull = 0;\n        const delta = this.difference(dragData.pointer, this.pointer(event)), stage = this.difference(dragData.stage.start, delta);\n        if (!this.is('dragging')) {\n            return false;\n        }\n        if (this.settings.loop) {\n            minimum = this.coordinates(this.minimum());\n            maximum = +this.coordinates(this.maximum() + 1) - minimum;\n            stage.x = (((stage.x - minimum) % maximum + maximum) % maximum) + minimum;\n        }\n        else {\n            minimum = this.settings.rtl ? this.coordinates(this.maximum()) : this.coordinates(this.minimum());\n            maximum = this.settings.rtl ? this.coordinates(this.minimum()) : this.coordinates(this.maximum());\n            pull = this.settings.pullDrag ? -1 * delta.x / 5 : 0;\n            stage.x = Math.max(Math.min(stage.x, minimum + pull), maximum + pull);\n        }\n        return stage;\n    }\n    /**\n     * Finishes dragging of carousel when `touchend` and `mouseup` events fire.\n     * @todo #261\n     * @todo Threshold for click event\n     * @param event the event arguments.\n     * @param dragObj the object with dragging settings and states\n     * @param clickAttacher function which attaches click handler to slide or its children elements in order to prevent event bubling\n     */\n    finishDragging(event, dragObj, clickAttacher) {\n        const directions = ['right', 'left'], delta = this.difference(dragObj.pointer, this.pointer(event)), stage = dragObj.stage.current, direction = directions[+(this.settings.rtl ? delta.x < +this.settings.rtl : delta.x > +(this.settings.rtl || 0))];\n        let currentSlideI, current, newCurrent;\n        if (delta.x !== 0 && this.is('dragging') || !this.is('valid')) {\n            this.speed(+(this.settings.dragEndSpeed || 0) || this.settings.smartSpeed);\n            currentSlideI = this.closest(stage.x, delta.x !== 0 ? direction : dragObj.direction);\n            current = this.current();\n            newCurrent = this.current(currentSlideI === -1 ? undefined : currentSlideI);\n            if (current !== newCurrent) {\n                this.invalidate('position');\n                this.update();\n            }\n            dragObj.direction = direction;\n            if (Math.abs(delta.x) > 3 || new Date().getTime() - dragObj.time > 300) {\n                clickAttacher();\n            }\n        }\n        if (!this.is('dragging')) {\n            return;\n        }\n        this.leave('dragging');\n        this._trigger('dragged');\n    }\n    /**\n     * Gets absolute position of the closest item for a coordinate.\n     * @todo Setting `freeDrag` makes `closest` not reusable. See #165.\n     * @param coordinate The coordinate in pixel.\n     * @param direction The direction to check for the closest item. Ether `left` or `right`.\n     * @returns The absolute position of the closest item.\n     */\n    closest(coordinate, direction) {\n        const pull = 30, width = this.width();\n        let coordinates = this.coordinates(), position = -1;\n        if (this.settings.center) {\n            coordinates = coordinates.map(item => {\n                if (item === 0) {\n                    item += 0.000001;\n                }\n                return item;\n            });\n        }\n        // option 'freeDrag' doesn't have realization and using it here creates problem:\n        // variable 'position' stays unchanged (it equals -1 at the begging) and thus method returns -1\n        // Returning value is consumed by method current(), which taking -1 as argument calculates the index of new current slide\n        // In case of having 5 slides ans 'loop=false; calling 'current(-1)' sets props '_current' as 4. Just last slide remains visible instead of 3 last slides.\n        // if (!this.settings.freeDrag) {\n        // check closest item\n        for (let i = 0; i < coordinates.length; i++) {\n            if (direction === 'left' && coordinate > coordinates[i] - pull && coordinate < coordinates[i] + pull) {\n                position = i;\n                // on a right pull, check on previous index\n                // to do so, subtract width from value and set position = index + 1\n            }\n            else if (direction === 'right' && coordinate > coordinates[i] - width - pull && coordinate < coordinates[i] - width + pull) {\n                position = i + 1;\n            }\n            else if (this._op(coordinate, '<', coordinates[i])\n                && this._op(coordinate, '>', coordinates[i + 1] || coordinates[i] - width)) {\n                position = direction === 'left' ? i + 1 : i;\n            }\n            else if (direction === null && coordinate > coordinates[i] - pull && coordinate < coordinates[i] + pull) {\n                position = i;\n            }\n            if (position !== -1) {\n                break;\n            }\n            ;\n        }\n        // }\n        if (!this.settings.loop) {\n            // non loop boundries\n            if (this._op(coordinate, '>', coordinates[this.minimum()])) {\n                position = coordinate = this.minimum();\n            }\n            else if (this._op(coordinate, '<', coordinates[this.maximum()])) {\n                position = coordinate = this.maximum();\n            }\n        }\n        return position;\n    }\n    /**\n     * Animates the stage.\n     * @todo #270\n     * @param coordinate The coordinate in pixels.\n     */\n    animate(coordinate) {\n        const animate = this.speed() > 0;\n        if (this.is('animating')) {\n            this.onTransitionEnd();\n        }\n        if (animate) {\n            this.enter('animating');\n            this._trigger('translate');\n        }\n        this.stageData.transform = 'translate3d(' + coordinate + 'px,0px,0px)';\n        this.stageData.transition = (this.speed() / 1000) + 's' + (this.settings.slideTransition ? ' ' + this.settings.slideTransition : '');\n        // also there was transition by means of JQuery.animate or css-changing property left\n    }\n    /**\n     * Checks whether the carousel is in a specific state or not.\n     * @param state The state to check.\n     * @returns The flag which indicates if the carousel is busy.\n     */\n    is(state) {\n        return this._states.current[state] && this._states.current[state] > 0;\n    }\n    ;\n    /**\n     * Sets the absolute position of the current item.\n     * @param position The new absolute position or nothing to leave it unchanged.\n     * @returns The absolute position of the current item.\n     */\n    current(position) {\n        if (position === undefined) {\n            return this._current;\n        }\n        if (this._items.length === 0) {\n            return undefined;\n        }\n        position = this.normalize(position);\n        if (this._current !== position) {\n            const event = this._trigger('change', { property: { name: 'position', value: position } });\n            // if (event.data !== undefined) {\n            // \tposition = this.normalize(event.data);\n            // }\n            this._current = position;\n            this.invalidate('position');\n            this._trigger('changed', { property: { name: 'position', value: this._current } });\n        }\n        return this._current;\n    }\n    /**\n     * Invalidates the given part of the update routine.\n     * @param part The part to invalidate.\n     * @returns The invalidated parts.\n     */\n    invalidate(part) {\n        if (typeof part === 'string') {\n            this._invalidated[part] = true;\n            if (this.is('valid')) {\n                this.leave('valid');\n            }\n        }\n        return Object.keys(this._invalidated);\n    }\n    ;\n    /**\n     * Resets the absolute position of the current item.\n     * @param position the absolute position of the new item.\n     */\n    reset(position) {\n        position = this.normalize(position);\n        if (position === undefined) {\n            return;\n        }\n        this._speed = 0;\n        this._current = position;\n        this._suppress(['translate', 'translated']);\n        this.animate(this.coordinates(position));\n        this._release(['translate', 'translated']);\n    }\n    /**\n     * Normalizes an absolute or a relative position of an item.\n     * @param position The absolute or relative position to normalize.\n     * @param relative Whether the given position is relative or not.\n     * @returns The normalized position.\n     */\n    normalize(position, relative) {\n        const n = this._items.length, m = relative ? 0 : this._clones.length;\n        let result = position;\n        if (!this._isNumeric(position) || n < 1) {\n            result = undefined;\n        }\n        else if (position < 0 || position >= n + m) {\n            result = ((position - m / 2) % n + n) % n + m / 2;\n        }\n        return result;\n    }\n    /**\n     * Converts an absolute position of an item into a relative one.\n     * @param position The absolute position to convert.\n     * @returns The converted position.\n     */\n    relative(position) {\n        position -= this._clones.length / 2;\n        return this.normalize(position, true);\n    }\n    /**\n     * Gets the maximum position for the current item.\n     * @param relative Whether to return an absolute position or a relative position.\n     * @returns number of maximum position\n     */\n    maximum(relative = false) {\n        const settings = this.settings;\n        let maximum = this._coordinates.length, iterator, reciprocalItemsWidth, elementWidth;\n        if (settings.loop) {\n            maximum = this._clones.length / 2 + this._items.length - 1;\n        }\n        else if (settings.autoWidth || settings.merge) {\n            iterator = this._items.length;\n            reciprocalItemsWidth = this.slidesData[--iterator].width;\n            elementWidth = this._width;\n            while (iterator-- > 0) {\n                // it could be use this._items instead of this.slidesData;\n                reciprocalItemsWidth += +(this.slidesData[iterator].width || 0) + (this.settings.margin || 0);\n                if (reciprocalItemsWidth > elementWidth) {\n                    break;\n                }\n            }\n            maximum = iterator + 1;\n        }\n        else if (settings.center) {\n            maximum = this._items.length - 1;\n        }\n        else {\n            maximum = this._items.length - (settings.items || 1);\n        }\n        if (relative) {\n            maximum -= this._clones.length / 2;\n        }\n        return Math.max(maximum, 0);\n    }\n    /**\n     * Gets the minimum position for the current item.\n     * @param relative Whether to return an absolute position or a relative position.\n     * @returns number of minimum position\n     */\n    minimum(relative = false) {\n        return relative ? 0 : this._clones.length / 2;\n    }\n    /**\n     * Gets an item at the specified relative position.\n     * @param position The relative position of the item.\n     * @returns The item at the given position or all items if no position was given.\n     */\n    items(position) {\n        if (position === undefined) {\n            return this._items.slice();\n        }\n        position = this.normalize(position, true);\n        return [this._items[position]];\n    }\n    /**\n     * Gets an item at the specified relative position.\n     * @param position The relative position of the item.\n     * @returns The item at the given position or all items if no position was given.\n     */\n    mergers(position) {\n        if (position === undefined) {\n            return this._mergers.slice();\n        }\n        position = this.normalize(position, true);\n        return this._mergers[position];\n    }\n    /**\n     * Gets the absolute positions of clones for an item.\n     * @param position The relative position of the item.\n     * @returns The absolute positions of clones for the item or all if no position was given.\n     */\n    clones(position) {\n        const odd = this._clones.length / 2, even = odd + this._items.length, map = (index) => index % 2 === 0 ? even + index / 2 : odd - (index + 1) / 2;\n        if (position === undefined) {\n            return this._clones.map((v, i) => map(i));\n        }\n        return this._clones.map((v, i) => v === position ? map(i) : null).filter(item => item !== null);\n    }\n    /**\n     * Sets the current animation speed.\n     * @param speed The animation speed in milliseconds or nothing to leave it unchanged.\n     * @returns The current animation speed in milliseconds.\n     */\n    speed(speed) {\n        if (speed !== undefined) {\n            this._speed = speed;\n        }\n        return this._speed;\n    }\n    /**\n     * Gets the coordinate of an item.\n     * @todo The name of this method is missleanding.\n     * @param position The absolute position of the item within `minimum()` and `maximum()`.\n     * @returns The coordinate of the item in pixel or all coordinates.\n     */\n    coordinates(position) {\n        let multiplier = 1, newPosition = (position || 0) - 1, coordinate, result;\n        if (position === undefined) {\n            result = this._coordinates.map((item, index) => {\n                return this.coordinates(index);\n            });\n            return result;\n        }\n        if (this.settings.center) {\n            if (this.settings.rtl) {\n                multiplier = -1;\n                newPosition = position + 1;\n            }\n            coordinate = this._coordinates[position];\n            coordinate += (this.width() - coordinate + (this._coordinates[newPosition] || 0)) / 2 * multiplier;\n        }\n        else {\n            coordinate = this._coordinates[newPosition] || 0;\n        }\n        coordinate = Math.ceil(coordinate);\n        return coordinate;\n    }\n    /**\n     * Calculates the speed for a translation.\n     * @param from The absolute position of the start item.\n     * @param to The absolute position of the target item.\n     * @param factor [factor=undefined] - The time factor in milliseconds.\n     * @returns The time in milliseconds for the translation.\n     */\n    _duration(from, to, factor) {\n        if (factor === 0) {\n            return 0;\n        }\n        return Math.min(Math.max(Math.abs(to - from), 1), 6) * Math.abs(+(factor || 0) || this.settings.smartSpeed || 0);\n    }\n    /**\n     * Slides to the specified item.\n     * @param position The position of the item.\n     * @param speed The time in milliseconds for the transition.\n     */\n    to(position, speed) {\n        let current = this.current(), revert, distance = position - this.relative(current), maximum = this.maximum(), delayForLoop = 0;\n        const direction = +(distance > 0) - +(distance < 0), items = this._items.length, minimum = this.minimum();\n        if (this.settings.loop) {\n            if (!this.settings.rewind && Math.abs(distance) > items / 2) {\n                distance += direction * -1 * items;\n            }\n            position = current + distance;\n            revert = ((position - minimum) % items + items) % items + minimum;\n            if (revert !== position && revert - distance <= maximum && revert - distance > 0) {\n                current = revert - distance;\n                position = revert;\n                delayForLoop = 30;\n                this.reset(current);\n                this.sendChanges();\n            }\n        }\n        else if (this.settings.rewind) {\n            maximum += 1;\n            position = (position % maximum + maximum) % maximum;\n        }\n        else {\n            position = Math.max(minimum, Math.min(maximum, position));\n        }\n        setTimeout(() => {\n            this.speed(this._duration(current, position, speed));\n            this.current(position);\n            this.update();\n        }, delayForLoop);\n    }\n    /**\n     * Slides to the next item.\n     * @param speed The time in milliseconds for the transition.\n     */\n    next(speed) {\n        speed = speed || false;\n        this.to(this.relative(this.current()) + 1, speed);\n    }\n    /**\n     * Slides to the previous item.\n     * @param speed The time in milliseconds for the transition.\n     */\n    prev(speed) {\n        speed = speed || false;\n        this.to(this.relative(this.current()) - 1, speed);\n    }\n    /**\n     * Handles the end of an animation.\n     * @param event - The event arguments.\n     */\n    onTransitionEnd(event) {\n        // if css2 animation then event object is undefined\n        if (event !== undefined) {\n            // event.stopPropagation();\n            // // Catch only owl-stage transitionEnd event\n            // if ((event.target || event.srcElement || event.originalTarget) !== this.$stage.get(0)\t) {\n            // \treturn false;\n            // }\n            return false;\n        }\n        this.leave('animating');\n        this._trigger('translated');\n    }\n    /**\n     * Gets viewport width.\n     * @returns - The width in pixel.\n     */\n    _viewport() {\n        let width;\n        if (this._width) {\n            width = this._width;\n        }\n        else {\n            this.logger.log('Can not detect viewport width.');\n        }\n        return width;\n    }\n    /**\n     * Sets _items\n     * @param content The list of slides put into CarouselSlideDirectives.\n     */\n    setItems(content) {\n        this._items = content;\n    }\n    /**\n     * Sets slidesData using this._items\n     */\n    _defineSlidesData() {\n        // Maybe creating and using loadMap would be better in LazyLoadService.\n        // Hovewer in that case when 'resize' event fires, prop 'load' of all slides will get 'false' and such state of prop will be seen by View during its updating. Accordingly the code will remove slides's content from DOM even if it was loaded before.\n        // Thus it would be needed to add that content into DOM again.\n        // In order to avoid additional removing/adding loaded slides's content we use loadMap here and set restore state of prop 'load' before the View will get it.\n        let loadMap;\n        if (this.slidesData && this.slidesData.length) {\n            loadMap = new Map();\n            this.slidesData.forEach(item => {\n                if (item.load) {\n                    loadMap.set(item.id, item.load);\n                }\n            });\n        }\n        this.slidesData = this._items.map(slide => {\n            return {\n                id: `${slide.id()}`,\n                isActive: false,\n                tplRef: slide.tplRef,\n                dataMerge: slide.dataMerge(),\n                width: 0,\n                isCloned: false,\n                load: loadMap ? loadMap.get(slide.id()) : false,\n                hashFragment: slide.dataHash()\n            };\n        });\n    }\n    /**\n     * Sets current classes for slide\n     * @param slide Slide of carousel\n     * @returns object with names of css-classes which are keys and true/false values\n     */\n    setCurSlideClasses(slide) {\n        // CSS classes: added/removed per current state of component properties\n        const currentClasses = {\n            'active': slide.isActive || false,\n            'center': slide.isCentered || false,\n            'cloned': slide.isCloned || false,\n            'animated': slide.isAnimated || false,\n            'owl-animated-in': slide.isDefAnimatedIn || false,\n            'owl-animated-out': slide.isDefAnimatedOut || false\n        };\n        if (this.settings.animateIn) {\n            currentClasses[this.settings.animateIn] = slide.isCustomAnimatedIn || false;\n        }\n        if (this.settings.animateOut) {\n            currentClasses[this.settings.animateOut] = slide.isCustomAnimatedOut || false;\n        }\n        return currentClasses;\n    }\n    /**\n     * Operators to calculate right-to-left and left-to-right.\n     * @param a - The left side operand.\n     * @param o - The operator.\n     * @param b - The right side operand.\n     * @returns true/false meaning right-to-left or left-to-right\n     */\n    _op(a, o, b) {\n        const rtl = this.settings.rtl;\n        switch (o) {\n            case '<':\n                return rtl ? a > b : a < b;\n            case '>':\n                return rtl ? a < b : a > b;\n            case '>=':\n                return rtl ? a <= b : a >= b;\n            case '<=':\n                return rtl ? a >= b : a <= b;\n            default:\n                break;\n        }\n        return false;\n    }\n    /**\n     * Triggers a public event.\n     * @todo Remove `status`, `relatedTarget` should be used instead.\n     * @param name The event name.\n     * @param data The event data.\n     * @param namespace The event namespace.\n     * @param state The state which is associated with the event.\n     * @param enter Indicates if the call enters the specified state or not.\n     */\n    _trigger(name, data, namespace, state, enter) {\n        switch (name) {\n            case 'initialized':\n                this._initializedCarousel$.next(name);\n                break;\n            case 'change':\n                this._changeSettingsCarousel$.next(data);\n                break;\n            case 'changed':\n                this._changedSettingsCarousel$.next(data);\n                break;\n            case 'drag':\n                this._dragCarousel$.next(name);\n                break;\n            case 'dragged':\n                this._draggedCarousel$.next(name);\n                break;\n            case 'resize':\n                this._resizeCarousel$.next(name);\n                break;\n            case 'resized':\n                this._resizedCarousel$.next(name);\n                break;\n            case 'refresh':\n                this._refreshCarousel$.next(name);\n                break;\n            case 'refreshed':\n                this._refreshedCarousel$.next(name);\n                break;\n            case 'translate':\n                this._translateCarousel$.next(name);\n                break;\n            case 'translated':\n                this._translatedCarousel$.next(name);\n                break;\n            default:\n                break;\n        }\n    }\n    /**\n     * Enters a state.\n     * @param name - The state name.\n     */\n    enter(name) {\n        [name].concat(this._states.tags[name] || []).forEach((stateName) => {\n            if (this._states.current[stateName] === undefined) {\n                this._states.current[stateName] = 0;\n            }\n            this._states.current[stateName]++;\n        });\n    }\n    ;\n    /**\n     * Leaves a state.\n     * @param name - The state name.\n     */\n    leave(name) {\n        [name].concat(this._states.tags[name] || []).forEach((stateName) => {\n            if (this._states.current[stateName] === 0 || !!this._states.current[stateName]) {\n                this._states.current[stateName]--;\n            }\n        });\n    }\n    ;\n    /**\n     * Registers an event or state.\n     * @param object - The event or state to register.\n     */\n    register(object) {\n        if (object.type === Type.State) {\n            if (!this._states.tags[object.name]) {\n                this._states.tags[object.name] = object.tags;\n            }\n            else {\n                this._states.tags[object.name] = this._states.tags[object.name].concat(object.tags);\n            }\n            this._states.tags[object.name] = this._states.tags[object.name].filter((tag, i) => {\n                return this._states.tags[object.name].indexOf(tag) === i;\n            });\n        }\n    }\n    /**\n     * Suppresses events.\n     * @param events The events to suppress.\n     */\n    _suppress(events) {\n        events.forEach(event => {\n            this._supress[event] = true;\n        });\n    }\n    /**\n     * Releases suppressed events.\n     * @param events The events to release.\n     */\n    _release(events) {\n        events.forEach(event => {\n            delete this._supress[event];\n        });\n    }\n    /**\n     * Gets unified pointer coordinates from event.\n     * @todo #261\n     * @param event The `mousedown` or `touchstart` event.\n     * @returns Object Coords which contains `x` and `y` coordinates of current pointer position.\n     */\n    pointer(event) {\n        const result = { x: 0, y: 0 };\n        event = event.originalEvent || event || window.event;\n        event = event.touches && event.touches.length ?\n            event.touches[0] : event.changedTouches && event.changedTouches.length ?\n            event.changedTouches[0] : event;\n        if (event.pageX) {\n            result.x = event.pageX;\n            result.y = event.pageY;\n        }\n        else {\n            result.x = event.clientX;\n            result.y = event.clientY;\n        }\n        return result;\n    }\n    /**\n     * Determines if the input is a Number or something that can be coerced to a Number\n     * @param number The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number\n     */\n    _isNumeric(number) {\n        return !isNaN(parseFloat(number));\n    }\n    /**\n     * Determines whether value is number or boolean type\n     * @param value The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number, or Boolean\n     */\n    _isNumberOrBoolean(value) {\n        return this._isNumeric(value) || typeof value === 'boolean';\n    }\n    /**\n     * Determines whether value is number or string type\n     * @param value The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number, or String\n     */\n    _isNumberOrString(value) {\n        return this._isNumeric(value) || typeof value === 'string';\n    }\n    /**\n     * Determines whether value is number or string type\n     * @param value The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number, or String\n     */\n    _isStringOrBoolean(value) {\n        return typeof value === 'string' || typeof value === 'boolean';\n    }\n    /**\n     * Gets the difference of two vectors.\n     * @todo #261\n     * @param first The first vector.\n     * @param second The second vector.\n     * @returns The difference.\n     */\n    difference(first, second) {\n        if (null === first || null === second) {\n            return {\n                x: 0,\n                y: 0,\n            };\n        }\n        return {\n            x: first.x - second.x,\n            y: first.y - second.y\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselService, deps: [{ token: OwlLogger }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: OwlLogger }] });\n\nclass NavigationService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    navSubscription;\n    /**\n     * Indicates whether the plugin is initialized or not.\n     */\n    _initialized = false;\n    /**\n     * The current paging indexes.\n     */\n    _pages = [];\n    /**\n     * Data for navigation elements of the user interface.\n     */\n    _navData = {\n        disabled: false,\n        prev: {\n            disabled: false,\n            htmlText: ''\n        },\n        next: {\n            disabled: false,\n            htmlText: ''\n        },\n    };\n    /**\n     * Data for dot elements of the user interface.\n     */\n    _dotsData = {\n        disabled: false,\n        dots: []\n    };\n    constructor(carouselService) {\n        this.carouselService = carouselService;\n        this.spyDataStreams();\n    }\n    ngOnDestroy() {\n        this.navSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n        const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(state => {\n            this.initialize();\n            this._updateNavPages();\n            this.draw();\n            this.update();\n            this.carouselService.sendChanges();\n        }));\n        // mostly changes in carouselService and carousel at all causes carouselService.to(). It moves stage right-left by its code and calling needed functions\n        // Thus this method by calling carouselService.current(position) notifies about changes\n        const changedSettings$ = this.carouselService.getChangedState().pipe(filter(data => data.property.name === 'position'), tap(data => {\n            this.update();\n            // should be the call of the function written at the end of comment\n            // but the method carouselServive.to() has setTimeout(f, 0) which contains carouselServive.update() which calls sendChanges() method.\n            // carouselService.navData and carouselService.dotsData update earlier than carouselServive.update() gets called\n            // updates of carouselService.navData and carouselService.dotsData are being happening withing carouselService.current(position) method which calls next() of _changedSettingsCarousel$\n            // carouselService.current(position) is being calling earlier than carouselServive.update();\n            // this.carouselService.sendChanges();\n        }));\n        const refreshedCarousel$ = this.carouselService.getRefreshedState().pipe(tap(() => {\n            this._updateNavPages();\n            this.draw();\n            this.update();\n            this.carouselService.sendChanges();\n        }));\n        const navMerge$ = merge(initializedCarousel$, changedSettings$, refreshedCarousel$);\n        this.navSubscription = navMerge$.subscribe(() => { });\n    }\n    /**\n     * Initializes the layout of the plugin and extends the carousel.\n     */\n    initialize() {\n        this._navData.disabled = true;\n        this._navData.prev.htmlText = this.carouselService.settings.navText[0];\n        this._navData.next.htmlText = this.carouselService.settings.navText[1];\n        this._dotsData.disabled = true;\n        this.carouselService.navData = this._navData;\n        this.carouselService.dotsData = this._dotsData;\n    }\n    /**\n     * Calculates internal states and updates prop _pages\n     */\n    _updateNavPages() {\n        let i, j, k;\n        const lower = this.carouselService.clones().length / 2, upper = lower + this.carouselService.items().length, maximum = this.carouselService.maximum(true), pages = [], settings = this.carouselService.settings;\n        let size = settings.center || settings.autoWidth || settings.dotsData\n            ? 1 : Math.floor(Number(settings.dotsEach)) || Math.floor(settings.items);\n        size = +size;\n        if (settings.slideBy !== 'page') {\n            settings.slideBy = Math.min(+settings.slideBy, settings.items);\n        }\n        if (settings.dots || settings.slideBy === 'page') {\n            for (i = lower, j = 0, k = 0; i < upper; i++) {\n                if (j >= size || j === 0) {\n                    pages.push({\n                        start: Math.min(maximum, i - lower),\n                        end: i - lower + size - 1\n                    });\n                    if (Math.min(maximum, i - lower) === maximum) {\n                        break;\n                    }\n                    j = 0, ++k;\n                }\n                j += this.carouselService.mergers(this.carouselService.relative(i));\n            }\n        }\n        this._pages = pages;\n    }\n    /**\n     * Draws the user interface.\n     * @todo The option `dotsData` wont work.\n     */\n    draw() {\n        let difference;\n        const settings = this.carouselService.settings, items = this.carouselService.items(), disabled = items.length <= settings.items;\n        this._navData.disabled = !settings.nav || disabled;\n        this._dotsData.disabled = !settings.dots || disabled;\n        if (settings.dots) {\n            difference = this._pages.length - this._dotsData.dots.length;\n            if (settings.dotsData && difference !== 0) {\n                this._dotsData.dots = [];\n                items.forEach(item => {\n                    this._dotsData.dots.push({\n                        active: false,\n                        id: `dot-${item.id}`,\n                        innerContent: item.dotContent(),\n                        showInnerContent: true\n                    });\n                });\n            }\n            else if (difference > 0) {\n                const startI = this._dotsData.dots.length > 0 ? this._dotsData.dots.length : 0;\n                for (let i = 0; i < difference; i++) {\n                    this._dotsData.dots.push({\n                        active: false,\n                        id: `dot-${i + startI}`,\n                        innerContent: '',\n                        showInnerContent: false\n                    });\n                }\n            }\n            else if (difference < 0) {\n                this._dotsData.dots.splice(difference, Math.abs(difference));\n            }\n        }\n        this.carouselService.navData = this._navData;\n        this.carouselService.dotsData = this._dotsData;\n    }\n    ;\n    /**\n     * Updates navigation buttons's and dots's states\n     */\n    update() {\n        this._updateNavButtons();\n        this._updateDots();\n    }\n    /**\n     * Changes state of nav buttons (disabled, enabled)\n     */\n    _updateNavButtons() {\n        const settings = this.carouselService.settings, loop = settings.loop || settings.rewind, index = this.carouselService.relative(this.carouselService.current());\n        if (settings.nav) {\n            this._navData.prev.disabled = !loop && index <= this.carouselService.minimum(true);\n            this._navData.next.disabled = !loop && index >= this.carouselService.maximum(true);\n        }\n        this.carouselService.navData = this._navData;\n    }\n    /**\n     * Changes active dot if page becomes changed\n     */\n    _updateDots() {\n        let curActiveDotI;\n        if (!this.carouselService.settings.dots) {\n            return;\n        }\n        this._dotsData.dots.forEach(item => {\n            if (item.active === true) {\n                item.active = false;\n            }\n        });\n        curActiveDotI = this._current();\n        if (this._dotsData.dots.length) {\n            this._dotsData.dots[curActiveDotI].active = true;\n        }\n        this.carouselService.dotsData = this._dotsData;\n    }\n    /**\n     * Gets the current page position of the carousel.\n     * @returns the current page position of the carousel\n     */\n    _current() {\n        const current = this.carouselService.relative(this.carouselService.current());\n        let finalCurrent;\n        const pages = this._pages.filter((page, index) => {\n            return page.start <= current && page.end >= current;\n        }).pop();\n        finalCurrent = this._pages.findIndex(page => {\n            return page.start === pages.start && page.end === pages.end;\n        });\n        return finalCurrent;\n    }\n    ;\n    /**\n     * Gets the current succesor/predecessor position.\n     * @param sussessor position of slide\n     * @returns the current succesor/predecessor position\n     */\n    _getPosition(successor) {\n        let position, length;\n        const settings = this.carouselService.settings;\n        if (settings.slideBy === 'page') {\n            position = this._current();\n            length = this._pages.length;\n            successor ? ++position : --position;\n            position = this._pages[((position % length) + length) % length].start;\n        }\n        else {\n            position = this.carouselService.relative(this.carouselService.current());\n            length = this.carouselService.items().length;\n            successor ? position += +settings.slideBy : position -= +settings.slideBy;\n        }\n        return position;\n    }\n    ;\n    /**\n     * Slides to the next item or page.\n     * @param speed The time in milliseconds for the transition.\n     */\n    next(speed) {\n        this.carouselService.to(this._getPosition(true), speed);\n    }\n    ;\n    /**\n     * Slides to the previous item or page.\n     * @param speed The time in milliseconds for the transition.\n     */\n    prev(speed) {\n        this.carouselService.to(this._getPosition(false), speed);\n    }\n    ;\n    /**\n   * Slides to the specified item or page.\n   * @param position - The position of the item or page.\n   * @param speed - The time in milliseconds for the transition.\n   * @param standard - Whether to use the standard behaviour or not. Default meaning false\n   */\n    to(position, speed, standard) {\n        let length;\n        if (!standard && this._pages.length) {\n            length = this._pages.length;\n            this.carouselService.to(this._pages[((position % length) + length) % length].start, speed);\n        }\n        else {\n            this.carouselService.to(position, speed);\n        }\n    }\n    ;\n    /**\n     * Moves carousel after user's clicking on any dots\n     */\n    moveByDot(dotId) {\n        const index = this._dotsData.dots.findIndex(dot => dotId === dot.id);\n        this.to(index, this.carouselService.settings.dotsSpeed);\n    }\n    /**\n     * rewinds carousel to slide with needed id\n     * @param id id of slide\n     */\n    toSlideById(id) {\n        const position = this.carouselService.slidesData.findIndex(slide => slide.id === id && slide.isCloned === false);\n        if (position === -1 || position === this.carouselService.current()) {\n            return;\n        }\n        this.carouselService.to(this.carouselService.relative(position), false);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: NavigationService, deps: [{ token: CarouselService }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: NavigationService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: NavigationService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: CarouselService }] });\n\n// import { Injectable } from '@angular/core';\n// function _window(): any {\n//    // return the global native browser window object\n//    return window;\n// }\n// @Injectable()\n// export class WindowRefService {\n//    get nativeWindow(): any {\n//       return _window();\n//    }\n// }\n/**\n * Create a new injection token for injecting the window into a component.\n */\nconst WINDOW = new InjectionToken('WindowToken');\n/**\n * Define abstract class for obtaining reference to the global window object.\n */\nclass WindowRef {\n    get nativeWindow() {\n        throw new Error('Not implemented.');\n    }\n}\n/**\n * Define class that implements the abstract class and returns the native window object.\n */\nclass BrowserWindowRef extends WindowRef {\n    constructor() {\n        super();\n    }\n    /**\n     * @returns window object\n     */\n    get nativeWindow() {\n        return window;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: BrowserWindowRef, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: BrowserWindowRef });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: BrowserWindowRef, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n/**\n * Create an factory function that returns the native window object.\n * @param browserWindowRef Native window object\n * @param platformId id of platform\n * @returns type of platform of empty object\n */\nfunction windowFactory(browserWindowRef, platformId) {\n    if (isPlatformBrowser(platformId)) {\n        return browserWindowRef.nativeWindow;\n    }\n    const obj = {\n        setTimeout: (func, time) => { },\n        clearTimeout: (a) => { }\n    };\n    return obj;\n}\n/**\n * Create a injectable provider for the WindowRef token that uses the BrowserWindowRef class.\n */\nconst browserWindowProvider = {\n    provide: WindowRef,\n    useClass: BrowserWindowRef\n};\n/**\n * Create an injectable provider that uses the windowFactory function for returning the native window object.\n */\nconst windowProvider = {\n    provide: WINDOW,\n    useFactory: windowFactory,\n    deps: [WindowRef, PLATFORM_ID]\n};\n/**\n * Create an array of providers.\n */\nconst WINDOW_PROVIDERS = [browserWindowProvider, windowProvider];\n\n/**\n * Create a new injection token for injecting the Document into a component.\n */\nconst DOCUMENT = new InjectionToken('DocumentToken');\n/**\n * Define abstract class for obtaining reference to the global Document object.\n */\nclass DocumentRef {\n    get nativeDocument() {\n        throw new Error('Not implemented.');\n    }\n}\n/**\n * Define class that implements the abstract class and returns the native Document object.\n */\nclass BrowserDocumentRef extends DocumentRef {\n    constructor() {\n        super();\n    }\n    /**\n     * @returns Document object\n     */\n    get nativeDocument() {\n        return document;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: BrowserDocumentRef, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: BrowserDocumentRef });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: BrowserDocumentRef, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n/**\n * Create an factory function that returns the native Document object.\n * @param browserDocumentRef Native Document object\n * @param platformId id of platform\n * @returns type of platform of empty object\n */\nfunction documentFactory(browserDocumentRef, platformId) {\n    if (isPlatformBrowser(platformId)) {\n        return browserDocumentRef.nativeDocument;\n    }\n    const doc = {\n        hidden: false,\n        visibilityState: 'visible'\n    };\n    return doc;\n}\n/**\n * Create a injectable provider for the DocumentRef token that uses the BrowserDocumentRef class.\n */\nconst browserDocumentProvider = {\n    provide: DocumentRef,\n    useClass: BrowserDocumentRef\n};\n/**\n * Create an injectable provider that uses the DocumentFactory function for returning the native Document object.\n */\nconst documentProvider = {\n    provide: DOCUMENT,\n    useFactory: documentFactory,\n    deps: [DocumentRef, PLATFORM_ID]\n};\n/**\n * Create an array of providers.\n */\nconst DOCUMENT_PROVIDERS = [browserDocumentProvider, documentProvider];\n\nclass AutoplayService {\n    carouselService;\n    ngZone;\n    /**\n     * Subscrioption to merge Observables from CarouselService\n     */\n    autoplaySubscription;\n    /**\n     * The autoplay timeout.\n     */\n    _timeout = null;\n    /**\n     * Indicates whenever the autoplay is paused.\n     */\n    _paused = false;\n    /**\n     * Shows whether the code (the plugin) changed the option 'AutoplayTimeout' for own needs\n     */\n    _isArtificialAutoplayTimeout;\n    /**\n     * Shows whether the autoplay is paused for unlimited time by the developer.\n     * Use to prevent autoplaying in case of firing `mouseleave` by adding layers to `<body>` like `mat-menu` does\n     */\n    _isAutoplayStopped = false;\n    get isAutoplayStopped() {\n        return this._isAutoplayStopped;\n    }\n    set isAutoplayStopped(value) {\n        this._isAutoplayStopped = value;\n    }\n    winRef;\n    docRef;\n    constructor(carouselService, winRef, docRef, ngZone) {\n        this.carouselService = carouselService;\n        this.ngZone = ngZone;\n        this.winRef = winRef;\n        this.docRef = docRef;\n        this.spyDataStreams();\n    }\n    ngOnDestroy() {\n        this.autoplaySubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n        const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n            if (this.carouselService.settings.autoplay) {\n                this.play();\n            }\n        }));\n        const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n            this._handleChangeObservable(data);\n        }));\n        const resized$ = this.carouselService.getResizedState().pipe(tap(() => {\n            if (this.carouselService.settings.autoplay && !this._isAutoplayStopped) {\n                this.play();\n            }\n            else {\n                this.stop();\n            }\n        }));\n        // original Autoplay Plugin has listeners on play.owl.core and stop.owl.core events.\n        // They are triggered by Video Plugin\n        const autoplayMerge$ = merge(initializedCarousel$, changedSettings$, resized$);\n        this.autoplaySubscription = autoplayMerge$.subscribe(() => { });\n    }\n    /**\n       * Starts the autoplay.\n       * @param timeout The interval before the next animation starts.\n       * @param speed The animation speed for the animations.\n       */\n    play(timeout, speed) {\n        if (this._paused) {\n            this._paused = false;\n            this._setAutoPlayInterval(this.carouselService.settings.autoplayMouseleaveTimeout);\n        }\n        if (this.carouselService.is('rotating')) {\n            return;\n        }\n        this.carouselService.enter('rotating');\n        this._setAutoPlayInterval();\n    }\n    ;\n    /**\n       * Gets a new timeout\n       * @param timeout - The interval before the next animation starts.\n       * @param speed - The animation speed for the animations.\n       * @return\n       */\n    _getNextTimeout(timeout, speed) {\n        if (this._timeout) {\n            this.winRef.clearTimeout(this._timeout);\n        }\n        this._isArtificialAutoplayTimeout = timeout ? true : false;\n        return this.ngZone.runOutsideAngular(() => {\n            return this.winRef.setTimeout(() => {\n                this.ngZone.run(() => {\n                    if (this._paused || this.carouselService.is('busy') || this.carouselService.is('interacting') || this.docRef.hidden) {\n                        return;\n                    }\n                    this.carouselService.next(speed || this.carouselService.settings.autoplaySpeed);\n                });\n            }, timeout || this.carouselService.settings.autoplayTimeout);\n        });\n    }\n    ;\n    /**\n       * Sets autoplay in motion.\n       */\n    _setAutoPlayInterval(timeout) {\n        this._timeout = this._getNextTimeout(timeout);\n    }\n    ;\n    /**\n     * Stops the autoplay.\n     */\n    stop() {\n        if (!this.carouselService.is('rotating')) {\n            return;\n        }\n        this._paused = true;\n        this.winRef.clearTimeout(this._timeout);\n        this.carouselService.leave('rotating');\n    }\n    ;\n    /**\n       * Stops the autoplay.\n       */\n    pause() {\n        if (!this.carouselService.is('rotating')) {\n            return;\n        }\n        this._paused = true;\n    }\n    ;\n    /**\n     * Manages by autoplaying according to data passed by _changedSettingsCarousel$ Obsarvable\n     * @param data object with current position of carousel and type of change\n     */\n    _handleChangeObservable(data) {\n        if (data.property.name === 'settings') {\n            if (this.carouselService.settings.autoplay) {\n                this.play();\n            }\n            else {\n                this.stop();\n            }\n        }\n        else if (data.property.name === 'position') {\n            //console.log('play?', e);\n            if (this.carouselService.settings.autoplay) {\n                this._setAutoPlayInterval();\n            }\n        }\n    }\n    /**\n     * Starts autoplaying of the carousel in the case when user leaves the carousel before it starts translateing (moving)\n     */\n    _playAfterTranslated() {\n        of('translated').pipe(switchMap(data => this.carouselService.getTranslatedState()), first(), filter(() => this._isArtificialAutoplayTimeout), tap(() => this._setAutoPlayInterval())).subscribe(() => { });\n    }\n    /**\n     * Starts pausing\n     */\n    startPausing() {\n        if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n            this.pause();\n        }\n    }\n    /**\n     * Starts playing after mouse leaves carousel\n     */\n    startPlayingMouseLeave() {\n        if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n            this.play();\n            this._playAfterTranslated();\n        }\n    }\n    /**\n     * Starts playing after touch ends\n     */\n    startPlayingTouchEnd() {\n        if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n            this.play();\n            this._playAfterTranslated();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AutoplayService, deps: [{ token: CarouselService }, { token: WINDOW }, { token: DOCUMENT }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AutoplayService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AutoplayService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: CarouselService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [WINDOW]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }] });\n\nclass LazyLoadService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    lazyLoadSubscription;\n    constructor(carouselService) {\n        this.carouselService = carouselService;\n        this.spyDataStreams();\n    }\n    ngOnDestroy() {\n        this.lazyLoadSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n        const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n            const isLazyLoad = this.carouselService.settings && !this.carouselService.settings.lazyLoad;\n            this.carouselService.slidesData.forEach(item => item.load = isLazyLoad ? true : false);\n        }));\n        const changeSettings$ = this.carouselService.getChangeState();\n        const resizedCarousel$ = this.carouselService.getResizedState();\n        const lazyLoadMerge$ = merge(initializedCarousel$, changeSettings$, resizedCarousel$).pipe(tap(data => this._defineLazyLoadSlides(data)));\n        this.lazyLoadSubscription = lazyLoadMerge$.subscribe(() => { });\n    }\n    _defineLazyLoadSlides(data) {\n        if (!this.carouselService.settings || !this.carouselService.settings.lazyLoad) {\n            return;\n        }\n        if ((data.property && data.property.name === 'position') || data === 'initialized' || data === \"resized\") {\n            const settings = this.carouselService.settings, clones = this.carouselService.clones().length;\n            let n = (settings.center && Math.ceil(settings.items / 2) || settings.items), i = ((settings.center && n * -1) || 0), position = (data.property && data.property.value !== undefined ? data.property.value : this.carouselService.current()) + i;\n            // load = $.proxy(function(i, v) { this.load(v) }, this);\n            //TODO: Need documentation for this new option\n            if (settings.lazyLoadEager > 0) {\n                n += settings.lazyLoadEager;\n                // If the carousel is looping also preload images that are to the \"left\"\n                if (settings.loop) {\n                    position -= settings.lazyLoadEager;\n                    n++;\n                }\n            }\n            while (i++ < n) {\n                this._load(clones / 2 + this.carouselService.relative(position));\n                if (clones) {\n                    this.carouselService.clones(this.carouselService.relative(position)).forEach(value => this._load(value));\n                }\n                position++;\n            }\n        }\n    }\n    /**\n       * Loads all resources of an item at the specified position.\n       * @param position - The absolute position of the item.\n       */\n    _load(position) {\n        if (this.carouselService.slidesData[position].load) {\n            return;\n        }\n        this.carouselService.slidesData[position].load = true;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: LazyLoadService, deps: [{ token: CarouselService }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: LazyLoadService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: LazyLoadService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: CarouselService }] });\n\nclass AnimateService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    animateSubscription;\n    /**\n     * s\n     */\n    swapping = true;\n    /**\n     * active slide before translating\n     */\n    previous = undefined;\n    /**\n     * new active slide after translating\n     */\n    next = undefined;\n    constructor(carouselService) {\n        this.carouselService = carouselService;\n        this.spyDataStreams();\n    }\n    ngOnDestroy() {\n        this.animateSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n        const changeSettings$ = this.carouselService.getChangeState().pipe(tap(data => {\n            if (data.property.name === 'position') {\n                this.previous = this.carouselService.current();\n                this.next = data.property.value;\n            }\n        }));\n        const dragCarousel$ = this.carouselService.getDragState();\n        const draggedCarousel$ = this.carouselService.getDraggedState();\n        const translatedCarousel$ = this.carouselService.getTranslatedState();\n        const dragTranslatedMerge$ = merge(dragCarousel$, draggedCarousel$, translatedCarousel$).pipe(tap(data => this.swapping = data === 'translated'));\n        const translateCarousel$ = this.carouselService.getTranslateState().pipe(tap(data => {\n            if (this.swapping && (this.carouselService._options.animateOut || this.carouselService._options.animateIn)) {\n                this._swap();\n            }\n        }));\n        const animateMerge$ = merge(changeSettings$, translateCarousel$, dragTranslatedMerge$).pipe();\n        this.animateSubscription = animateMerge$.subscribe(() => { });\n    }\n    /**\n       * Toggles the animation classes whenever an translations starts.\n       * @returns\n       */\n    _swap() {\n        if (this.carouselService.settings.items !== 1) {\n            return;\n        }\n        // if (!$.support.animation || !$.support.transition) {\n        // \treturn;\n        // }\n        this.carouselService.speed(0);\n        let left;\n        const previous = this.carouselService.slidesData[this.previous], next = this.carouselService.slidesData[this.next], incoming = this.carouselService.settings.animateIn, outgoing = this.carouselService.settings.animateOut;\n        if (this.carouselService.current() === this.previous) {\n            return;\n        }\n        if (outgoing) {\n            left = +this.carouselService.coordinates(this.previous) - +this.carouselService.coordinates(this.next);\n            this.carouselService.slidesData.forEach(slide => {\n                if (slide.id === previous.id) {\n                    slide.left = `${left}px`;\n                    slide.isAnimated = true;\n                    slide.isDefAnimatedOut = true;\n                    slide.isCustomAnimatedOut = true;\n                }\n            });\n        }\n        if (incoming) {\n            this.carouselService.slidesData.forEach(slide => {\n                if (slide.id === next.id) {\n                    slide.isAnimated = true;\n                    slide.isDefAnimatedIn = true;\n                    slide.isCustomAnimatedIn = true;\n                }\n            });\n        }\n    }\n    ;\n    /**\n     * Handles the end of 'animationend' event\n     * @param id Id of slides\n     */\n    clear(id) {\n        this.carouselService.slidesData.forEach(slide => {\n            if (slide.id === id) {\n                slide.left = '';\n                slide.isAnimated = false;\n                slide.isDefAnimatedOut = false;\n                slide.isCustomAnimatedOut = false;\n                slide.isDefAnimatedIn = false;\n                slide.isCustomAnimatedIn = false;\n                slide.classes = this.carouselService.setCurSlideClasses(slide);\n            }\n        });\n        this.carouselService.onTransitionEnd();\n    }\n    ;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AnimateService, deps: [{ token: CarouselService }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AnimateService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AnimateService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: CarouselService }] });\n\nclass AutoHeightService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    autoHeightSubscription;\n    constructor(carouselService) {\n        this.carouselService = carouselService;\n        this.spyDataStreams();\n    }\n    ngOnDestroy() {\n        this.autoHeightSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n        const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(data => {\n            if (this.carouselService.settings.autoHeight) {\n                this.update();\n            }\n            else {\n                this.carouselService.slidesData.forEach(slide => slide.heightState = 'full');\n            }\n        }));\n        const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n            if (this.carouselService.settings.autoHeight && data.property.name === 'position') {\n                this.update();\n            }\n        }));\n        const refreshedCarousel$ = this.carouselService.getRefreshedState().pipe(tap(data => {\n            if (this.carouselService.settings.autoHeight) {\n                this.update();\n            }\n        }));\n        const autoHeight$ = merge(initializedCarousel$, changedSettings$, refreshedCarousel$);\n        this.autoHeightSubscription = autoHeight$.subscribe(() => { });\n    }\n    /**\n     * Updates the prop 'heightState' of slides\n     */\n    update() {\n        const items = this.carouselService.settings.items;\n        let start = this.carouselService.current(), end = start + items;\n        if (this.carouselService.settings.center) {\n            start = items % 2 === 1 ? start - (items - 1) / 2 : start - items / 2;\n            end = items % 2 === 1 ? start + items : start + items + 1;\n        }\n        this.carouselService.slidesData.forEach((slide, i) => {\n            slide.heightState = (i >= start && i < end) ? 'full' : 'nulled';\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AutoHeightService, deps: [{ token: CarouselService }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AutoHeightService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: AutoHeightService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: CarouselService }] });\n\nclass HashService {\n    carouselService;\n    route;\n    router;\n    /**\n     * Subscription to merge Observable from CarouselService\n     */\n    hashSubscription;\n    /**\n     * Current url fragment (hash)\n     */\n    currentHashFragment;\n    constructor(carouselService, route, router) {\n        this.carouselService = carouselService;\n        this.route = route;\n        this.router = router;\n        this.spyDataStreams();\n        if (!this.route) {\n            this.route = {\n                fragment: of('no route').pipe(take(1))\n            };\n        }\n        ;\n        if (!this.router) {\n            this.router = {\n                navigate: (commands, extras) => { return; }\n            };\n        }\n    }\n    ngOnDestroy() {\n        this.hashSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n        const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => this.listenToRoute()));\n        const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n            if (this.carouselService.settings.URLhashListener && data.property.name === 'position') {\n                const newCurSlide = this.carouselService.current();\n                const newCurFragment = this.carouselService.slidesData[newCurSlide].hashFragment;\n                if (!newCurFragment || newCurFragment === this.currentHashFragment) {\n                    return;\n                }\n                this.router.navigate(['./'], { fragment: newCurFragment, relativeTo: this.route });\n            }\n        }));\n        const hashFragment$ = merge(initializedCarousel$, changedSettings$);\n        this.hashSubscription = hashFragment$.subscribe(() => { });\n    }\n    /**\n     * rewinds carousel to slide which has the same hashFragment as fragment of current url\n     * @param fragment fragment of url\n     */\n    rewind(fragment) {\n        const position = this.carouselService.slidesData.findIndex(slide => slide.hashFragment === fragment && slide.isCloned === false);\n        if (position === -1 || position === this.carouselService.current()) {\n            return;\n        }\n        this.carouselService.to(this.carouselService.relative(position), false);\n    }\n    /**\n     * Initiate listening to ActivatedRoute.fragment\n     */\n    listenToRoute() {\n        const count = this.carouselService.settings.startPosition === 'URLHash' ? 0 : 2;\n        this.route.fragment.pipe(skip(count))\n            .subscribe(fragment => {\n            this.currentHashFragment = fragment;\n            this.rewind(fragment);\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: HashService, deps: [{ token: CarouselService }, { token: i1.ActivatedRoute, optional: true }, { token: i1.Router, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: HashService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: HashService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: CarouselService }, { type: i1.ActivatedRoute, decorators: [{\n                    type: Optional\n                }] }, { type: i1.Router, decorators: [{\n                    type: Optional\n                }] }] });\n\nlet nextId = 0;\nclass CarouselSlideDirective {\n    tplRef;\n    /**\n     * Unique slide identifier. Must be unique for the entire document for proper accessibility support.\n     * Will be auto-generated if not provided.\n     */\n    id = input(`owl-slide-${nextId++}`);\n    /**\n     * Defines how much widths of common slide will current slide have\n     * e.g. if dataMerge=2, the slide will twice wider then slides with dataMerge=1\n     */\n    dataMerge = input(1, {\n        transform: (data) => {\n            return +data || 1;\n        }\n    });\n    /**\n     * Width of slide\n     */\n    width = input(0);\n    /**\n     * Inner content of dot for certain slide; can be html-markup\n     */\n    dotContent = input('');\n    /**\n     * Hash (fragment) of url which corresponds to certain slide\n     */\n    dataHash = input('');\n    constructor(tplRef) {\n        this.tplRef = tplRef;\n    }\n    /**\n       * Determines if the input is a Number or something that can be coerced to a Number\n       * @param - The input to be tested\n       * @returns - An indication if the input is a Number or can be coerced to a Number\n       */\n    isNumeric(number) {\n        return !isNaN(parseFloat(number));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselSlideDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"17.1.0\", version: \"20.0.2\", type: CarouselSlideDirective, isStandalone: false, selector: \"ng-template[carouselSlide]\", inputs: { id: { classPropertyName: \"id\", publicName: \"id\", isSignal: true, isRequired: false, transformFunction: null }, dataMerge: { classPropertyName: \"dataMerge\", publicName: \"dataMerge\", isSignal: true, isRequired: false, transformFunction: null }, width: { classPropertyName: \"width\", publicName: \"width\", isSignal: true, isRequired: false, transformFunction: null }, dotContent: { classPropertyName: \"dotContent\", publicName: \"dotContent\", isSignal: true, isRequired: false, transformFunction: null }, dataHash: { classPropertyName: \"dataHash\", publicName: \"dataHash\", isSignal: true, isRequired: false, transformFunction: null } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselSlideDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[carouselSlide]',\n                    standalone: false\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\nclass ResizeService {\n    resizeObservable$;\n    docRef;\n    /**\n     * Makes resizeSubject become Observable\n     * @returns Observable of resizeSubject\n     */\n    get onResize$() {\n        return this.resizeObservable$.pipe(filter(() => !this.docRef?.fullscreenElement));\n    }\n    constructor(winRef, docRef, platformId) {\n        this.docRef = docRef;\n        this.resizeObservable$ = isPlatformBrowser(platformId)\n            ? fromEvent(winRef, 'resize')\n            : (new Subject()).asObservable();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: ResizeService, deps: [{ token: WINDOW }, { token: DOCUMENT }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: ResizeService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: ResizeService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [WINDOW]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }] });\n\nclass StageComponent {\n    zone;\n    el;\n    renderer;\n    carouselService;\n    animateService;\n    /**\n     * Object with settings which make carousel draggable by touch or mouse\n     */\n    owlDraggable = input();\n    /**\n     * Data of owl-stage\n     */\n    stageData = input();\n    /**\n     *  Data of every slide\n     */\n    slidesData = input();\n    /**\n     * Function wich will be returned after attaching listener to 'mousemove' event\n     */\n    listenerMouseMove;\n    /**\n     * Function wich will be returned after attaching listener to 'touchmove' event\n     */\n    listenerTouchMove;\n    /**\n     * Function wich will be returned after attaching listener to 'mousemove' event\n     */\n    listenerOneMouseMove;\n    /**\n     * Function wich will be returned after attaching listener to 'touchmove' event\n     */\n    listenerOneTouchMove;\n    /**\n     * Function wich will be returned after attaching listener to 'mouseup' event\n     */\n    listenerMouseUp;\n    /**\n     * Function wich will be returned after attaching listener to 'touchend' event\n     */\n    listenerTouchEnd;\n    /**\n     * Function wich will be returned after attaching listener to 'click' event\n     */\n    listenerOneClick;\n    listenerATag;\n    /**\n     * Object with data needed for dragging\n     */\n    _drag = {\n        time: null,\n        target: null,\n        pointer: null,\n        stage: {\n            start: null,\n            current: null\n        },\n        direction: null,\n        active: false,\n        moving: false\n    };\n    /**\n     * Subject for notification when the carousel's rebuilding caused by resize event starts\n     */\n    _oneDragMove$ = new Subject();\n    /**\n     * Subsctiption to _oneDragMove$ Subject\n     */\n    _oneMoveSubsription;\n    preparePublicSlide = (slide) => {\n        const newSlide = { ...slide };\n        delete newSlide.tplRef;\n        return newSlide;\n    };\n    constructor(zone, el, renderer, carouselService, animateService) {\n        this.zone = zone;\n        this.el = el;\n        this.renderer = renderer;\n        this.carouselService = carouselService;\n        this.animateService = animateService;\n    }\n    onMouseDown(event) {\n        if (this.owlDraggable()?.isMouseDragable) {\n            this._onDragStart(event);\n        }\n    }\n    onTouchStart(event) {\n        if (event.targetTouches.length >= 2) {\n            return false;\n        }\n        if (this.owlDraggable()?.isTouchDragable) {\n            this._onDragStart(event);\n        }\n    }\n    onTouchCancel(event) {\n        this._onDragEnd(event);\n    }\n    onDragStart() {\n        if (this.owlDraggable()?.isMouseDragable) {\n            return false;\n        }\n    }\n    onSelectStart() {\n        if (this.owlDraggable()?.isMouseDragable) {\n            return false;\n        }\n    }\n    ngOnInit() {\n        this._oneMoveSubsription = this._oneDragMove$\n            .pipe(first())\n            .subscribe(() => {\n            this._sendChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._oneMoveSubsription.unsubscribe();\n    }\n    /**\n     * Passes this to _oneMouseTouchMove();\n     */\n    bindOneMouseTouchMove = (ev) => {\n        this._oneMouseTouchMove(ev);\n    };\n    /**\n     * Passes this to _onDragMove();\n     */\n    bindOnDragMove = (ev) => {\n        this._onDragMove(ev);\n    };\n    /**\n     * Passes this to _onDragMove();\n     */\n    bindOnDragEnd = (ev) => {\n        // this.zone.run(() => {\n        this._onDragEnd(ev);\n        // });\n    };\n    /**\n     * Handles `touchstart` and `mousedown` events.\n     * @todo Horizontal swipe threshold as option\n     * @todo #261\n     * @param event - The event arguments.\n     */\n    _onDragStart(event) {\n        if (event.which === 3) {\n            return;\n        }\n        const stage = this._prepareDragging(event);\n        this._drag.time = new Date().getTime();\n        this._drag.target = event.target;\n        this._drag.stage.start = stage;\n        this._drag.stage.current = stage;\n        this._drag.pointer = this._pointer(event);\n        this.listenerMouseUp = this.renderer.listen(document, 'mouseup', this.bindOnDragEnd);\n        this.listenerTouchEnd = this.renderer.listen(document, 'touchend', this.bindOnDragEnd);\n        this.zone.runOutsideAngular(() => {\n            this.listenerOneMouseMove = this.renderer.listen(document, 'mousemove', this.bindOneMouseTouchMove);\n            this.listenerOneTouchMove = this.renderer.listen(document, 'touchmove', this.bindOneMouseTouchMove);\n        });\n    }\n    /**\n     * Attaches listeners to `touchmove` and `mousemove` events; initiates updating carousel after starting dragging\n     * @param event event objech of mouse or touch event\n     */\n    _oneMouseTouchMove(event) {\n        const delta = this._difference(this._drag.pointer, this._pointer(event));\n        if (this.listenerATag) {\n            this.listenerATag();\n        }\n        if (Math.abs(delta.x) < 3 && Math.abs(delta.y) < 3 && this._is('valid')) {\n            return;\n        }\n        if ((Math.abs(delta.x) < 3 && Math.abs(delta.x) < Math.abs(delta.y)) && this._is('valid')) {\n            return;\n        }\n        this.listenerOneMouseMove();\n        this.listenerOneTouchMove();\n        this._drag.moving = true;\n        this.blockClickAnchorInDragging(event);\n        this.listenerMouseMove = this.renderer.listen(document, 'mousemove', this.bindOnDragMove);\n        this.listenerTouchMove = this.renderer.listen(document, 'touchmove', this.bindOnDragMove);\n        event.preventDefault();\n        this._enterDragging();\n        this._oneDragMove$.next(event);\n        // this._sendChanges();\n    }\n    /**\n     * Attaches handler to HTMLAnchorElement for preventing click while carousel is being dragged\n     * @param event event object\n     */\n    blockClickAnchorInDragging(event) {\n        let target = event.target;\n        while (target && !(target instanceof HTMLAnchorElement)) {\n            target = target.parentElement;\n        }\n        if (target instanceof HTMLAnchorElement) {\n            this.listenerATag = this.renderer.listen(target, 'click', () => false);\n        }\n    }\n    /**\n   * Handles the `touchmove` and `mousemove` events.\n   * @todo #261\n   * @param event - The event arguments.\n   */\n    _onDragMove(event) {\n        let stage;\n        const stageOrExit = this.carouselService.defineNewCoordsDrag(event, this._drag);\n        if (stageOrExit === false) {\n            return;\n        }\n        stage = stageOrExit;\n        event.preventDefault();\n        this._drag.stage.current = stage;\n        this._animate(stage.x - this._drag.stage.start.x);\n    }\n    ;\n    /**\n     * Moves .owl-stage left-right\n     * @param coordinate coordinate to be set to .owl-stage\n     */\n    _animate(coordinate) {\n        this.renderer.setStyle(this.el.nativeElement.children[0], 'transform', `translate3d(${coordinate}px,0px,0px`);\n        this.renderer.setStyle(this.el.nativeElement.children[0], 'transition', '0s');\n    }\n    /**\n     * Handles the `touchend` and `mouseup` events.\n     * @todo #261\n     * @todo Threshold for click event\n     * @param event - The event arguments.\n     */\n    _onDragEnd(event) {\n        this.carouselService.owlDOMData.isGrab = false;\n        this.listenerOneMouseMove();\n        this.listenerOneTouchMove();\n        if (this._drag.moving) {\n            this.renderer.setStyle(this.el.nativeElement.children[0], 'transform', ``);\n            this.renderer.setStyle(this.el.nativeElement.children[0], 'transition', this.carouselService.speed(+(this.carouselService?.settings?.dragEndSpeed || 0) || this.carouselService.settings.smartSpeed) / 1000 + 's');\n            this._finishDragging(event);\n            this.listenerMouseMove();\n            this.listenerTouchMove();\n        }\n        this._drag = {\n            time: null,\n            target: null,\n            pointer: null,\n            stage: {\n                start: null,\n                current: null\n            },\n            direction: null,\n            active: false,\n            moving: false\n        };\n        // this.carouselService.trigger('dragged');\n        this.listenerMouseUp();\n        this.listenerTouchEnd();\n    }\n    ;\n    /**\n     * Prepares data for dragging carousel. It starts after firing `touchstart` and `mousedown` events.\n     * @param event - The event arguments.\n     * @returns stage - object with 'x' and 'y' coordinates of .owl-stage\n     */\n    _prepareDragging(event) {\n        return this.carouselService.prepareDragging(event);\n    }\n    /**\n     * Attaches handler for 'click' event on any element in .owl-stage in order to prevent dragging when moving of cursor is less than 3px\n     */\n    _oneClickHandler = () => {\n        this.listenerOneClick = this.renderer.listen(this._drag.target, 'click', () => false);\n        this.listenerOneClick();\n    };\n    /**\n     * Finishes dragging\n     * @param event object event of 'mouseUp' of 'touchend' events\n     */\n    _finishDragging(event) {\n        this.carouselService.finishDragging(event, this._drag, this._oneClickHandler);\n    }\n    /**\n     * Gets unified pointer coordinates from event.\n     * @param event The `mousedown` or `touchstart` event.\n     * @returns Contains `x` and `y` coordinates of current pointer position.\n     */\n    _pointer(event) {\n        return this.carouselService.pointer(event);\n    }\n    /**\n     * Gets the difference of two vectors.\n     * @param first The first vector.\n     * @param second The second vector.\n     * @returns The difference.\n     */\n    _difference(firstC, second) {\n        return this.carouselService.difference(firstC, second);\n    }\n    /**\n     * Checks whether the carousel is in a specific state or not.\n     * @param specificState The state to check.\n     * @returns The flag which indicates if the carousel is busy.\n     */\n    _is(specificState) {\n        return this.carouselService.is(specificState);\n    }\n    /**\n    * Enters a state.\n    * @param name The state name.\n    */\n    _enter(name) {\n        this.carouselService.enter(name);\n    }\n    /**\n     * Sends all data needed for View.\n     */\n    _sendChanges() {\n        this.carouselService.sendChanges();\n    }\n    /**\n     * Handler for transitioend event\n     */\n    onTransitionEnd() {\n        this.carouselService.onTransitionEnd();\n    }\n    /**\n     * Enters into a 'dragging' state\n     */\n    _enterDragging() {\n        this.carouselService.enterDragging();\n    }\n    /**\n     * Handles the end of 'animationend' event\n     * @param id Id of slides\n     */\n    clear(id) {\n        this.animateService.clear(id);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: StageComponent, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: CarouselService }, { token: AnimateService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.2\", type: StageComponent, isStandalone: false, selector: \"owl-stage\", inputs: { owlDraggable: { classPropertyName: \"owlDraggable\", publicName: \"owlDraggable\", isSignal: true, isRequired: false, transformFunction: null }, stageData: { classPropertyName: \"stageData\", publicName: \"stageData\", isSignal: true, isRequired: false, transformFunction: null }, slidesData: { classPropertyName: \"slidesData\", publicName: \"slidesData\", isSignal: true, isRequired: false, transformFunction: null } }, host: { listeners: { \"mousedown\": \"onMouseDown($event)\", \"touchstart\": \"onTouchStart($event)\", \"touchcancel\": \"onTouchCancel($event)\", \"dragstart\": \"onDragStart()\", \"selectstart\": \"onSelectStart()\" } }, ngImport: i0, template: `\n    <div>\n      <div class=\"owl-stage\" [ngStyle]=\"{'width': stageData().width + 'px',\n                                        'transform': stageData().transform,\n                                        'transition': stageData().transition,\n                                        'padding-left': stageData().paddingL ? stageData().paddingL + 'px' : '',\n                                        'padding-right': stageData().paddingR ? stageData().paddingR + 'px' : '' }\"\n          (transitionend)=\"onTransitionEnd()\"\n      >\n\n        @for(slide of slidesData(); track slide.id; let i = $index) {\n          <div class=\"owl-item\" [ngClass]=\"slide.classes\"\n                                [ngStyle]=\"{'width': slide.width + 'px',\n                                            'margin-left': slide.marginL ? slide.marginL + 'px' : '',\n                                            'margin-right': slide.marginR ? slide.marginR + 'px' : '',\n                                            'left': slide.left}\"\n                                (animationend)=\"clear(slide.id)\"\n                                [@autoHeight]=\"slide.heightState\">\n              @if(slide.load) {\n                <ng-template  [ngTemplateOutlet]=\"slide.tplRef\" [ngTemplateOutletContext]=\"{ \n                  $implicit: preparePublicSlide(slide), \n                  index: i\n                }\">\n                </ng-template>\n              }\n          </div><!-- /.owl-item -->\n        }  \n      \n      </div><!-- /.owl-stage -->\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: i3.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i3.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i3.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], animations: [\n            trigger('autoHeight', [\n                state('nulled', style({ height: 0 })),\n                state('full', style({ height: '*' })),\n                transition('full => nulled', [\n                    // style({height: '*'}),\n                    animate('700ms 350ms')\n                ]),\n                transition('nulled => full', [\n                    // style({height: 0}),\n                    animate(350)\n                ]),\n            ])\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: StageComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'owl-stage',\n                    template: `\n    <div>\n      <div class=\"owl-stage\" [ngStyle]=\"{'width': stageData().width + 'px',\n                                        'transform': stageData().transform,\n                                        'transition': stageData().transition,\n                                        'padding-left': stageData().paddingL ? stageData().paddingL + 'px' : '',\n                                        'padding-right': stageData().paddingR ? stageData().paddingR + 'px' : '' }\"\n          (transitionend)=\"onTransitionEnd()\"\n      >\n\n        @for(slide of slidesData(); track slide.id; let i = $index) {\n          <div class=\"owl-item\" [ngClass]=\"slide.classes\"\n                                [ngStyle]=\"{'width': slide.width + 'px',\n                                            'margin-left': slide.marginL ? slide.marginL + 'px' : '',\n                                            'margin-right': slide.marginR ? slide.marginR + 'px' : '',\n                                            'left': slide.left}\"\n                                (animationend)=\"clear(slide.id)\"\n                                [@autoHeight]=\"slide.heightState\">\n              @if(slide.load) {\n                <ng-template  [ngTemplateOutlet]=\"slide.tplRef\" [ngTemplateOutletContext]=\"{ \n                  $implicit: preparePublicSlide(slide), \n                  index: i\n                }\">\n                </ng-template>\n              }\n          </div><!-- /.owl-item -->\n        }  \n      \n      </div><!-- /.owl-stage -->\n    </div>\n  `,\n                    animations: [\n                        trigger('autoHeight', [\n                            state('nulled', style({ height: 0 })),\n                            state('full', style({ height: '*' })),\n                            transition('full => nulled', [\n                                // style({height: '*'}),\n                                animate('700ms 350ms')\n                            ]),\n                            transition('nulled => full', [\n                                // style({height: 0}),\n                                animate(350)\n                            ]),\n                        ])\n                    ],\n                    standalone: false\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: CarouselService }, { type: AnimateService }], propDecorators: { onMouseDown: [{\n                type: HostListener,\n                args: ['mousedown', ['$event']]\n            }], onTouchStart: [{\n                type: HostListener,\n                args: ['touchstart', ['$event']]\n            }], onTouchCancel: [{\n                type: HostListener,\n                args: ['touchcancel', ['$event']]\n            }], onDragStart: [{\n                type: HostListener,\n                args: ['dragstart']\n            }], onSelectStart: [{\n                type: HostListener,\n                args: ['selectstart']\n            }] } });\n\nclass CarouselComponent {\n    el;\n    resizeService;\n    carouselService;\n    navigationService;\n    autoplayService;\n    lazyLoadService;\n    animateService;\n    autoHeightService;\n    hashService;\n    logger;\n    changeDetectorRef;\n    //  Cannot implement via contentChildren() because of inputs are a little bit late and I get default input values\n    // in the case of converting slides to Observable and subscribing to it \n    // when using effect I get endless loop, because it also uses options() input and they fire one after another\n    slides;\n    translated = output();\n    dragging = output();\n    change = output();\n    changed = output();\n    initialized = output();\n    /**\n     * Width of carousel window (tag with class .owl-carousel), in wich we can see moving sliders\n     */\n    carouselWindowWidth;\n    /**\n     * Subscription to 'resize' event\n     */\n    resizeSubscription;\n    /**\n     * Subscription merge Observable, which merges all Observables in the component except 'resize' Observable and this.slides.changes()\n     */\n    _allObservSubscription;\n    /**\n     * Subscription to `this.slides.changes().\n     * It could be included in 'this._allObservSubscription', but that subcription get created during the initializing of component\n     * and 'this.slides' are undefined at that moment. So it's needed to wait for initialization of content.\n     */\n    _slidesChangesSubscription;\n    /**\n     * Current settings for the carousel.\n     */\n    _owlDOMData = signal(null);\n    owlDOMData = this._owlDOMData.asReadonly();\n    /**\n     * Data of owl-stage\n     */\n    _stageData = signal(null);\n    stageData = this._stageData.asReadonly();\n    /**\n     *  Data of every slide\n     */\n    _slidesData = signal([]);\n    slidesData = this._slidesData.asReadonly();\n    /**\n     * Data of navigation block\n     */\n    _navData = signal(null);\n    navData = this._navData.asReadonly();\n    /**\n     * Data of dots block\n     */\n    _dotsData = signal(null);\n    dotsData = this._dotsData.asReadonly();\n    /**\n     * Data, wich are passed out of carousel after ending of transioning of carousel\n     */\n    slidesOutputData;\n    /**\n     * Shows whether carousel is loaded of not.\n     */\n    _carouselLoaded = signal(false);\n    carouselLoaded = this._carouselLoaded.asReadonly();\n    /**\n     * User's options\n     */\n    options = input();\n    /**\n     * Observable for user's options\n     * It is used to track changes of options and re-render carousel if needed\n     */\n    _options$ = toObservable(this.options);\n    /**\n     * Previous options, used for checking whether options were changed\n     */\n    _optionsPrevAndCur$;\n    /**\n     * Observable for getting current View Settings\n     */\n    _viewCurSettings$;\n    /**\n     * Observable for catching the end of transition of carousel\n     */\n    _translatedCarousel$;\n    /**\n     * Observable for catching the start of dragging of the carousel\n     */\n    _draggingCarousel$;\n    /**\n     * Observable for catching the start of changing of the carousel\n     */\n    _changeCarousel$;\n    /**\n     * Observable for catching the moment when the data about slides changed, more exactly when the position changed.\n     */\n    _changedCarousel$;\n    /**\n     * Observable for catching the initialization of changing the carousel\n     */\n    _initializedCarousel$;\n    /**\n     * Observable for merging all Observables and creating one subscription\n     */\n    _carouselMerge$;\n    docRef;\n    constructor(el, resizeService, carouselService, navigationService, autoplayService, lazyLoadService, animateService, autoHeightService, hashService, logger, changeDetectorRef, docRef) {\n        this.el = el;\n        this.resizeService = resizeService;\n        this.carouselService = carouselService;\n        this.navigationService = navigationService;\n        this.autoplayService = autoplayService;\n        this.lazyLoadService = lazyLoadService;\n        this.animateService = animateService;\n        this.autoHeightService = autoHeightService;\n        this.hashService = hashService;\n        this.logger = logger;\n        this.changeDetectorRef = changeDetectorRef;\n        this.docRef = docRef;\n    }\n    onVisibilityChange(ev) {\n        if (!this.carouselService.settings.autoplay)\n            return;\n        switch (this.docRef.visibilityState) {\n            case 'visible':\n                !this.autoplayService.isAutoplayStopped && this.autoplayService.play();\n                break;\n            case 'hidden':\n                this.autoplayService.pause();\n                break;\n            default:\n                break;\n        }\n    }\n    ;\n    ngOnInit() {\n        this.spyDataStreams();\n        this.carouselWindowWidth = this.el.nativeElement.querySelector('.owl-carousel').clientWidth;\n    }\n    ngAfterContentInit() {\n        if (this.slides.toArray().length) {\n            this.carouselService.setup(this.carouselWindowWidth, this.slides.toArray(), this.options());\n            this.carouselService.initialize(this.slides.toArray());\n            this._winResizeWatcher();\n        }\n        else {\n            this.logger.log(`There are no slides to show. So the carousel won't be rendered`);\n        }\n        this._slidesChangesSubscription = this.slides.changes.pipe(tap((slides) => {\n            this.carouselService.setup(this.carouselWindowWidth, slides.toArray(), this.options());\n            this.carouselService.initialize(slides.toArray());\n            if (!slides.toArray().length) {\n                this._carouselLoaded.set(false);\n            }\n            if (slides.toArray().length && !this.resizeSubscription) {\n                this._winResizeWatcher();\n            }\n        })).subscribe(() => { });\n    }\n    ngOnDestroy() {\n        if (this.resizeSubscription) {\n            this.resizeSubscription.unsubscribe();\n        }\n        if (this._slidesChangesSubscription) {\n            this._slidesChangesSubscription.unsubscribe();\n        }\n        if (this._allObservSubscription) {\n            this._allObservSubscription.unsubscribe();\n        }\n    }\n    /**\n     * Joins the observable login in one place: sets values to some observables, merges this observables and\n     * subcribes to merge func\n     */\n    spyDataStreams() {\n        this._viewCurSettings$ = this.carouselService.getViewCurSettings().pipe(tap(data => {\n            this._owlDOMData.set(data.owlDOMData);\n            this._stageData.set(data.stageData);\n            this._slidesData.set(data.slidesData);\n            if (!this._carouselLoaded()) {\n                this._carouselLoaded.set(true);\n            }\n            this._navData.set(data.navData);\n            this._dotsData.set(data.dotsData);\n            this.changeDetectorRef.markForCheck(); // despite the fact we have signals here, they work with some delay, so we need to trigger change detection manually\n        }));\n        this._initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n            this.gatherTranslatedData();\n            this.initialized.emit(this.slidesOutputData);\n            // this.slidesOutputData = {};\n        }));\n        this._translatedCarousel$ = this.carouselService.getTranslatedState().pipe(tap(() => {\n            this.gatherTranslatedData();\n            this.translated.emit(this.slidesOutputData);\n            // this.slidesOutputData = {};\n        }));\n        this._changeCarousel$ = this.carouselService.getChangeState().pipe(tap(() => {\n            this.gatherTranslatedData();\n            this.change.emit(this.slidesOutputData);\n            // this.slidesOutputData = {};\n        }));\n        this._changedCarousel$ = this.carouselService.getChangeState().pipe(switchMap(value => {\n            const changedPosition = of(value).pipe(filter(() => value.property.name === 'position'), switchMap(() => from(this._slidesData())), skip(value.property.value), take(this.carouselService?.settings?.items || 0), map(slide => {\n                const clonedIdPrefix = this.carouselService.clonedIdPrefix;\n                const id = slide.id.indexOf(clonedIdPrefix) >= 0 ? slide.id.slice(clonedIdPrefix.length) : slide.id;\n                return { ...slide, id: id, isActive: true };\n            }), toArray(), map(slides => {\n                return {\n                    slides: slides,\n                    startPosition: this.carouselService.relative(value.property.value)\n                };\n            }));\n            // const changedSetting: Observable<SlidesOutputData> = of(value).pipe(\n            //   filter(() => value.property.name === 'settings'),\n            //   map(() => {\n            //     return {\n            //       slides: [],\n            //       startPosition: this.carouselService.relative(value.property.value)\n            //     }\n            //   })\n            // )\n            return merge(changedPosition);\n        }), tap(slidesData => {\n            this.gatherTranslatedData();\n            this.changed.emit(slidesData?.slides?.length ? slidesData : this.slidesOutputData);\n            // console.log(this.slidesOutputData);\n            // this.slidesOutputData = {};\n        }));\n        this._draggingCarousel$ = this.carouselService.getDragState().pipe(tap(() => {\n            this.gatherTranslatedData();\n            this.dragging.emit({ dragging: true, data: this.slidesOutputData });\n        }), switchMap(() => this.carouselService.getDraggedState().pipe(map(() => !!this.carouselService.is('animating')))), switchMap(anim => {\n            if (anim) {\n                return this.carouselService.getTranslatedState().pipe(first());\n            }\n            else {\n                return of('not animating');\n            }\n        }), tap(() => {\n            this.dragging.emit({ dragging: false, data: this.slidesOutputData });\n        }));\n        this._optionsPrevAndCur$ = this._options$.pipe(pairwise(), tap(([prev, cur]) => {\n            const slides = this.slides.toArray();\n            if (prev) {\n                this.carouselService.setup(this.carouselWindowWidth, slides, cur);\n                this.carouselService.initialize(slides);\n            }\n            if (prev && !slides.length) {\n                this.logger.log(`There are no slides to show.`);\n                this._carouselLoaded.set(false);\n            }\n            if (!prev) {\n                this._carouselLoaded.set(false);\n            }\n        }));\n        this._carouselMerge$ = merge(this._viewCurSettings$, this._translatedCarousel$, this._draggingCarousel$, this._changeCarousel$, this._changedCarousel$, this._initializedCarousel$, this._optionsPrevAndCur$);\n        this._allObservSubscription = this._carouselMerge$.subscribe(() => { });\n    }\n    /**\n     * Init subscription to resize event and attaches handler for this event\n     */\n    _winResizeWatcher() {\n        if (Object.keys(this.carouselService?._options?.responsive || {}).length) {\n            this.resizeSubscription = this.resizeService.onResize$\n                .pipe(filter(() => this.carouselWindowWidth !== this.el.nativeElement.querySelector('.owl-carousel').clientWidth), delay(this.carouselService.settings.responsiveRefreshRate || 200))\n                .subscribe(() => {\n                this.carouselService.onResize(this.el.nativeElement.querySelector('.owl-carousel').clientWidth);\n                this.carouselWindowWidth = this.el.nativeElement.querySelector('.owl-carousel').clientWidth;\n            });\n        }\n    }\n    /**\n     * Handler for transitioend event\n     */\n    onTransitionEnd() {\n        this.carouselService.onTransitionEnd();\n    }\n    /**\n     * Handler for click event, attached to next button\n     */\n    next() {\n        if (!this._carouselLoaded())\n            return;\n        this.navigationService.next(this.carouselService.settings.navSpeed || false);\n    }\n    /**\n     * Handler for click event, attached to prev button\n     */\n    prev() {\n        if (!this._carouselLoaded())\n            return;\n        this.navigationService.prev(this.carouselService.settings.navSpeed || false);\n    }\n    /**\n     * Handler for click event, attached to dots\n     */\n    moveByDot(dotId) {\n        if (!this._carouselLoaded())\n            return;\n        this.navigationService.moveByDot(dotId);\n    }\n    /**\n     * rewinds carousel to slide with needed id\n     * @param id fragment of url\n     */\n    to(id) {\n        // if (!this.carouselLoaded || ((this.navData && this.navData.disabled) && (this.dotsData && this.dotsData.disabled))) return;\n        if (!this._carouselLoaded())\n            return;\n        this.navigationService.toSlideById(id);\n    }\n    /**\n     * Gathers and prepares data intended for passing to the user by means of firing event translatedCarousel\n     */\n    gatherTranslatedData() {\n        let startPosition;\n        const clonedIdPrefix = this.carouselService.clonedIdPrefix;\n        const activeSlides = this._slidesData()\n            .filter(slide => slide.isActive === true)\n            .map(slide => {\n            const id = slide.id.indexOf(clonedIdPrefix) >= 0 ? slide.id.slice(clonedIdPrefix.length) : slide.id;\n            return {\n                id: id,\n                width: slide.width,\n                marginL: slide.marginL,\n                marginR: slide.marginR,\n                center: slide.isCentered\n            };\n        });\n        startPosition = this.carouselService.relative(this.carouselService.current());\n        this.slidesOutputData = {\n            startPosition: startPosition,\n            slides: activeSlides\n        };\n    }\n    /**\n     * Starts pausing\n     */\n    startPausing() {\n        this.autoplayService.startPausing();\n    }\n    /**\n     * Starts playing after mouse leaves carousel\n     */\n    startPlayML() {\n        this.autoplayService.startPlayingMouseLeave();\n    }\n    /**\n     * Starts playing after touch ends\n     */\n    startPlayTE() {\n        this.autoplayService.startPlayingTouchEnd();\n    }\n    stopAutoplay() {\n        this.autoplayService.isAutoplayStopped = true;\n        this.autoplayService.stop();\n    }\n    startAutoplay() {\n        this.autoplayService.isAutoplayStopped = false;\n        this.autoplayService.play();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselComponent, deps: [{ token: i0.ElementRef }, { token: ResizeService }, { token: CarouselService }, { token: NavigationService }, { token: AutoplayService }, { token: LazyLoadService }, { token: AnimateService }, { token: AutoHeightService }, { token: HashService }, { token: OwlLogger }, { token: i0.ChangeDetectorRef }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.2\", type: CarouselComponent, isStandalone: false, selector: \"owl-carousel-o\", inputs: { options: { classPropertyName: \"options\", publicName: \"options\", isSignal: true, isRequired: false, transformFunction: null } }, outputs: { translated: \"translated\", dragging: \"dragging\", change: \"change\", changed: \"changed\", initialized: \"initialized\" }, host: { listeners: { \"document:visibilitychange\": \"onVisibilityChange($event)\" } }, providers: [\n            NavigationService,\n            AutoplayService,\n            CarouselService,\n            LazyLoadService,\n            AnimateService,\n            AutoHeightService,\n            HashService\n        ], queries: [{ propertyName: \"slides\", predicate: CarouselSlideDirective }], ngImport: i0, template: `\n    <div class=\"owl-carousel owl-theme\" #owlCarousel\n      [ngClass]=\"{'owl-rtl': owlDOMData()?.rtl,\n                  'owl-loaded': owlDOMData()?.isLoaded,\n                  'owl-responsive': owlDOMData()?.isResponsive,\n                  'owl-drag': owlDOMData()?.isMouseDragable,\n                  'owl-grab': owlDOMData()?.isGrab}\"\n      (mouseover)=\"startPausing()\"\n      (mouseleave)=\"startPlayML()\"\n      (touchstart)=\"startPausing()\"\n      (touchend)=\"startPlayTE()\">\n\n      @if(carouselLoaded()) {\n        <div class=\"owl-stage-outer\">\n          <owl-stage [owlDraggable]=\"{\n                        'isMouseDragable': owlDOMData()?.isMouseDragable, \n                        'isTouchDragable': owlDOMData()?.isTouchDragable\n                      }\"\n                      [stageData]=\"stageData()\"\n                      [slidesData]=\"slidesData()\"></owl-stage>\n        </div> <!-- /.owl-stage-outer -->\n      }\n\n      @if(slides.toArray().length) {\n          <div class=\"owl-nav\" [ngClass]=\"{'disabled': navData()?.disabled}\">\n            <div class=\"owl-prev\" [ngClass]=\"{'disabled': navData()?.prev?.disabled}\" (click)=\"prev()\" [innerHTML]=\"navData()?.prev?.htmlText\"></div>\n            <div class=\"owl-next\" [ngClass]=\"{'disabled': navData()?.next?.disabled}\" (click)=\"next()\" [innerHTML]=\"navData()?.next?.htmlText\"></div>\n          </div> <!-- /.owl-nav -->\n          <div class=\"owl-dots\" [ngClass]=\"{'disabled': dotsData()?.disabled}\">\n\n            @for (dot of dotsData()?.dots; track dot.id) {\n              <div  class=\"owl-dot\" [ngClass]=\"{'active': dot.active, 'owl-dot-text': dot.showInnerContent}\" (click)=\"moveByDot(dot.id)\">\n                <span [innerHTML]=\"dot.innerContent\"></span>\n              </div>\n            }\n            \n          </div> <!-- /.owl-dots -->\n      }\n    </div> <!-- /.owl-carousel owl-loaded -->\n  `, isInline: true, styles: [\".owl-theme{display:block}\\n\"], dependencies: [{ kind: \"directive\", type: i3.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"component\", type: StageComponent, selector: \"owl-stage\", inputs: [\"owlDraggable\", \"stageData\", \"slidesData\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'owl-carousel-o', template: `\n    <div class=\"owl-carousel owl-theme\" #owlCarousel\n      [ngClass]=\"{'owl-rtl': owlDOMData()?.rtl,\n                  'owl-loaded': owlDOMData()?.isLoaded,\n                  'owl-responsive': owlDOMData()?.isResponsive,\n                  'owl-drag': owlDOMData()?.isMouseDragable,\n                  'owl-grab': owlDOMData()?.isGrab}\"\n      (mouseover)=\"startPausing()\"\n      (mouseleave)=\"startPlayML()\"\n      (touchstart)=\"startPausing()\"\n      (touchend)=\"startPlayTE()\">\n\n      @if(carouselLoaded()) {\n        <div class=\"owl-stage-outer\">\n          <owl-stage [owlDraggable]=\"{\n                        'isMouseDragable': owlDOMData()?.isMouseDragable, \n                        'isTouchDragable': owlDOMData()?.isTouchDragable\n                      }\"\n                      [stageData]=\"stageData()\"\n                      [slidesData]=\"slidesData()\"></owl-stage>\n        </div> <!-- /.owl-stage-outer -->\n      }\n\n      @if(slides.toArray().length) {\n          <div class=\"owl-nav\" [ngClass]=\"{'disabled': navData()?.disabled}\">\n            <div class=\"owl-prev\" [ngClass]=\"{'disabled': navData()?.prev?.disabled}\" (click)=\"prev()\" [innerHTML]=\"navData()?.prev?.htmlText\"></div>\n            <div class=\"owl-next\" [ngClass]=\"{'disabled': navData()?.next?.disabled}\" (click)=\"next()\" [innerHTML]=\"navData()?.next?.htmlText\"></div>\n          </div> <!-- /.owl-nav -->\n          <div class=\"owl-dots\" [ngClass]=\"{'disabled': dotsData()?.disabled}\">\n\n            @for (dot of dotsData()?.dots; track dot.id) {\n              <div  class=\"owl-dot\" [ngClass]=\"{'active': dot.active, 'owl-dot-text': dot.showInnerContent}\" (click)=\"moveByDot(dot.id)\">\n                <span [innerHTML]=\"dot.innerContent\"></span>\n              </div>\n            }\n            \n          </div> <!-- /.owl-dots -->\n      }\n    </div> <!-- /.owl-carousel owl-loaded -->\n  `, providers: [\n                        NavigationService,\n                        AutoplayService,\n                        CarouselService,\n                        LazyLoadService,\n                        AnimateService,\n                        AutoHeightService,\n                        HashService\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, standalone: false, styles: [\".owl-theme{display:block}\\n\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: ResizeService }, { type: CarouselService }, { type: NavigationService }, { type: AutoplayService }, { type: LazyLoadService }, { type: AnimateService }, { type: AutoHeightService }, { type: HashService }, { type: OwlLogger }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { slides: [{\n                type: ContentChildren,\n                args: [CarouselSlideDirective]\n            }], onVisibilityChange: [{\n                type: HostListener,\n                args: ['document:visibilitychange', ['$event']]\n            }] } });\n\nclass OwlRouterLinkDirective {\n    router;\n    route;\n    // TODO(issue/24571): remove '!'.\n    queryParams;\n    // TODO(issue/24571): remove '!'.\n    fragment;\n    // TODO(issue/24571): remove '!'.\n    queryParamsHandling;\n    // TODO(issue/24571): remove '!'.\n    preserveFragment;\n    // TODO(issue/24571): remove '!'.\n    skipLocationChange;\n    // TODO(issue/24571): remove '!'.\n    replaceUrl;\n    stopLink = false;\n    commands = [];\n    // TODO(issue/24571): remove '!'.\n    preserve;\n    constructor(router, route, tabIndex, renderer, el) {\n        this.router = router;\n        this.route = route;\n        if (tabIndex == null) {\n            renderer.setAttribute(el.nativeElement, 'tabindex', '0');\n        }\n    }\n    set owlRouterLink(commands) {\n        if (commands != null) {\n            this.commands = Array.isArray(commands) ? commands : [commands];\n        }\n        else {\n            this.commands = [];\n        }\n    }\n    /**\n     * @deprecated 4.0.0 use `queryParamsHandling` instead.\n     */\n    set preserveQueryParams(value) {\n        if (isDevMode() && console && console.warn) {\n            console.warn('preserveQueryParams is deprecated!, use queryParamsHandling instead.');\n        }\n        this.preserve = value;\n    }\n    onClick() {\n        const extras = {\n            skipLocationChange: attrBoolValue(this.skipLocationChange),\n            replaceUrl: attrBoolValue(this.replaceUrl),\n        };\n        if (this.stopLink) {\n            return false;\n        }\n        this.router.navigateByUrl(this.urlTree, extras);\n        return true;\n    }\n    get urlTree() {\n        return this.router.createUrlTree(this.commands, {\n            relativeTo: this.route,\n            queryParams: this.queryParams,\n            fragment: this.fragment,\n            queryParamsHandling: this.queryParamsHandling,\n            preserveFragment: attrBoolValue(this.preserveFragment)\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: OwlRouterLinkDirective, deps: [{ token: i1.Router }, { token: i1.ActivatedRoute }, { token: 'tabindex', attribute: true }, { token: i0.Renderer2 }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.2\", type: OwlRouterLinkDirective, isStandalone: false, selector: \":not(a)[owlRouterLink]\", inputs: { queryParams: \"queryParams\", fragment: \"fragment\", queryParamsHandling: \"queryParamsHandling\", preserveFragment: \"preserveFragment\", skipLocationChange: \"skipLocationChange\", replaceUrl: \"replaceUrl\", stopLink: \"stopLink\", owlRouterLink: \"owlRouterLink\", preserveQueryParams: \"preserveQueryParams\" }, host: { listeners: { \"click\": \"onClick()\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: OwlRouterLinkDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: ':not(a)[owlRouterLink]',\n                    standalone: false\n                }]\n        }], ctorParameters: () => [{ type: i1.Router }, { type: i1.ActivatedRoute }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i0.Renderer2 }, { type: i0.ElementRef }], propDecorators: { queryParams: [{\n                type: Input\n            }], fragment: [{\n                type: Input\n            }], queryParamsHandling: [{\n                type: Input\n            }], preserveFragment: [{\n                type: Input\n            }], skipLocationChange: [{\n                type: Input\n            }], replaceUrl: [{\n                type: Input\n            }], stopLink: [{\n                type: Input\n            }], owlRouterLink: [{\n                type: Input\n            }], preserveQueryParams: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }] } });\n/**\n * @description\n *\n * Lets you link to specific routes in your app.\n *\n * See `RouterLink` for more information.\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass OwlRouterLinkWithHrefDirective {\n    router;\n    route;\n    locationStrategy;\n    // TODO(issue/24571): remove '!'.\n    target;\n    // TODO(issue/24571): remove '!'.\n    queryParams;\n    // TODO(issue/24571): remove '!'.\n    fragment;\n    // TODO(issue/24571): remove '!'.\n    queryParamsHandling;\n    // TODO(issue/24571): remove '!'.\n    preserveFragment;\n    // TODO(issue/24571): remove '!'.\n    skipLocationChange;\n    // TODO(issue/24571): remove '!'.\n    replaceUrl;\n    stopLink = false;\n    commands = [];\n    subscription;\n    // TODO(issue/24571): remove '!'.\n    preserve;\n    // the url displayed on the anchor element.\n    // TODO(issue/24571): remove '!'.\n    href;\n    constructor(router, route, locationStrategy) {\n        this.router = router;\n        this.route = route;\n        this.locationStrategy = locationStrategy;\n        this.subscription = router.events.subscribe((s) => {\n            if (s instanceof NavigationEnd) {\n                this.updateTargetUrlAndHref();\n            }\n        });\n    }\n    set owlRouterLink(commands) {\n        if (commands != null) {\n            this.commands = Array.isArray(commands) ? commands : [commands];\n        }\n        else {\n            this.commands = [];\n        }\n    }\n    set preserveQueryParams(value) {\n        if (isDevMode() && console && console.warn) {\n            console.warn('preserveQueryParams is deprecated, use queryParamsHandling instead.');\n        }\n        this.preserve = value;\n    }\n    ngOnChanges(changes) { this.updateTargetUrlAndHref(); }\n    ngOnDestroy() { this.subscription.unsubscribe(); }\n    onClick(button, ctrlKey, metaKey, shiftKey) {\n        if (button !== 0 || ctrlKey || metaKey || shiftKey) {\n            return true;\n        }\n        if (typeof this.target === 'string' && this.target !== '_self') {\n            return true;\n        }\n        if (this.stopLink) {\n            return false;\n        }\n        const extras = {\n            skipLocationChange: attrBoolValue(this.skipLocationChange),\n            replaceUrl: attrBoolValue(this.replaceUrl),\n        };\n        this.router.navigateByUrl(this.urlTree, extras);\n        return false;\n    }\n    updateTargetUrlAndHref() {\n        this.href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.urlTree));\n    }\n    get urlTree() {\n        return this.router.createUrlTree(this.commands, {\n            relativeTo: this.route,\n            queryParams: this.queryParams,\n            fragment: this.fragment,\n            queryParamsHandling: this.queryParamsHandling,\n            preserveFragment: attrBoolValue(this.preserveFragment)\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: OwlRouterLinkWithHrefDirective, deps: [{ token: i1.Router }, { token: i1.ActivatedRoute }, { token: i3.LocationStrategy }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.2\", type: OwlRouterLinkWithHrefDirective, isStandalone: false, selector: \"a[owlRouterLink]\", inputs: { target: \"target\", queryParams: \"queryParams\", fragment: \"fragment\", queryParamsHandling: \"queryParamsHandling\", preserveFragment: \"preserveFragment\", skipLocationChange: \"skipLocationChange\", replaceUrl: \"replaceUrl\", stopLink: \"stopLink\", owlRouterLink: \"owlRouterLink\", preserveQueryParams: \"preserveQueryParams\" }, host: { listeners: { \"click\": \"onClick($event.button,$event.ctrlKey,$event.metaKey,$event.shiftKey)\" }, properties: { \"attr.target\": \"this.target\", \"href\": \"this.href\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: OwlRouterLinkWithHrefDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'a[owlRouterLink]',\n                    standalone: false\n                }]\n        }], ctorParameters: () => [{ type: i1.Router }, { type: i1.ActivatedRoute }, { type: i3.LocationStrategy }], propDecorators: { target: [{\n                type: HostBinding,\n                args: ['attr.target']\n            }, {\n                type: Input\n            }], queryParams: [{\n                type: Input\n            }], fragment: [{\n                type: Input\n            }], queryParamsHandling: [{\n                type: Input\n            }], preserveFragment: [{\n                type: Input\n            }], skipLocationChange: [{\n                type: Input\n            }], replaceUrl: [{\n                type: Input\n            }], stopLink: [{\n                type: Input\n            }], href: [{\n                type: HostBinding\n            }], owlRouterLink: [{\n                type: Input\n            }], preserveQueryParams: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event.button', '$event.ctrlKey', '$event.metaKey', '$event.shiftKey']]\n            }] } });\nfunction attrBoolValue(s) {\n    return s === '' || !!s;\n}\n\n/**\n * Data which will be passed out after ending of transition of carousel\n */\nclass SlidesOutputData {\n    startPosition;\n    slides;\n}\n;\n\nconst routes = [];\nclass CarouselModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselModule, declarations: [CarouselComponent, CarouselSlideDirective, StageComponent, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective], imports: [CommonModule], exports: [CarouselComponent, CarouselSlideDirective, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselModule, providers: [WINDOW_PROVIDERS, ResizeService, DOCUMENT_PROVIDERS, OwlLogger], imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.2\", ngImport: i0, type: CarouselModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        // BrowserAnimationsModule, // there's an issue with this import while using lazy loading of module consuming this library. I don't remove it because it could be needed during future enhancement of this lib.\n                        // RouterModule.forChild(routes)\n                    ],\n                    declarations: [CarouselComponent, CarouselSlideDirective, StageComponent, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective],\n                    exports: [CarouselComponent, CarouselSlideDirective, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective],\n                    providers: [WINDOW_PROVIDERS, ResizeService, DOCUMENT_PROVIDERS, OwlLogger]\n                }]\n        }] });\n\nclass SlideModel {\n    /**\n     * Id of slide\n     */\n    id;\n    /**\n     * Active state of slide. If true slide gets css-class .active\n     */\n    isActive;\n    /**\n     * TemplateRef of slide. In other words its html-markup\n     */\n    tplRef;\n    /**\n     * Number of grid parts to be used\n     */\n    dataMerge;\n    /**\n     * Width of slide\n     */\n    width;\n    /**\n     * Css-rule 'margin-left'\n     */\n    marginL;\n    /**\n     * Css-rule 'margin-right'\n     */\n    marginR;\n    /**\n     * Make slide to be on center of the carousel\n     */\n    isCentered;\n    /**\n     * Mark slide to be on center of the carousel (has .center)\n     */\n    center;\n    /**\n     * Cloned slide. It's being used when 'loop'=true\n     */\n    isCloned;\n    /**\n     * Indicates whether slide should be lazy loaded\n     */\n    load;\n    /**\n     * Css-rule 'left'\n     */\n    left;\n    /**\n     * Changeable classes of slide\n     */\n    classes;\n    /**\n     * Shows whether slide could be animated and could have css-class '.animated'\n     */\n    isAnimated;\n    /**\n     * Shows whether slide could be animated-in and could have css-class '.owl-animated-in'\n     */\n    isDefAnimatedIn;\n    /**\n     * Shows whether slide could be animated-out and could have css-class '.owl-animated-out'\n     */\n    isDefAnimatedOut;\n    /**\n     * Shows whether slide could be animated-in and could have animation css-class defined by user\n     */\n    isCustomAnimatedIn;\n    /**\n     * Shows whether slide could be animated-out and could have animation css-class defined by user\n     */\n    isCustomAnimatedOut;\n    /**\n     * State for defining the height of slide.It's values could be 'full' and 'nulled'. 'Full' sets css-height to 'auto', 'nulled' sets height to '0'.\n     */\n    heightState;\n    /**\n     * Hash (fragment) of url which corresponds to slide\n     */\n    hashFragment;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CarouselComponent, CarouselModule, CarouselSlideDirective, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective, SlideModel, SlidesOutputData };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,UAAU,EAAEC,cAAc,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,eAAe,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,eAAe;AAClP,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,OAAO,EAAEC,KAAK,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,QAAQ,MAAM;AAC1D,SAASC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,gBAAgB;AACzG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;AAEhF;AACA;AACA;AAFA,MAAAC,UAAA,GAAAA,CAAAC,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,EAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA,SAAAJ,EAAA;EAAA,aAAAC,EAAA;EAAA,cAAAC,EAAA;EAAA,gBAAAC,EAAA;EAAA,iBAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAL,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA,SAAAH,EAAA;EAAA,eAAAC,EAAA;EAAA,gBAAAC,EAAA;EAAA,QAAAC;AAAA;AAAA,MAAAG,GAAA,GAAAA,CAAAN,EAAA,EAAAC,EAAA;EAAAM,SAAA,EAAAP,EAAA;EAAAQ,KAAA,EAAAP;AAAA;AAAA,SAAAQ,0DAAAC,EAAA,EAAAC,GAAA;AAAA,SAAAC,4CAAAF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA6H6F5D,EAAE,CAAA+D,UAAA,IAAAJ,yDAAA,wBA22F7E,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAI,MAAA,GA32F0EhE,EAAE,CAAAiE,aAAA;IAAA,MAAAC,QAAA,GAAAF,MAAA,CAAAP,SAAA;IAAA,MAAAU,IAAA,GAAAH,MAAA,CAAAlB,MAAA;IAAA,MAAAsB,MAAA,GAAFpE,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAAqE,UAAA,qBAAAH,QAAA,CAAAI,MAw2FjC,CAAC,4BAx2F8BtE,EAAE,CAAAuE,eAAA,IAAAf,GAAA,EAAAY,MAAA,CAAAI,kBAAA,CAAAN,QAAA,GAAAC,IAAA,CA22F9E,CAAC;EAAA;AAAA;AAAA,SAAAM,8BAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAc,GAAA,GA32F2E1E,EAAE,CAAA2E,gBAAA;IAAF3E,EAAE,CAAA4E,cAAA,YAs2F9B,CAAC;IAt2F2B5E,EAAE,CAAA6E,UAAA,0BAAAC,0DAAA;MAAA,MAAAZ,QAAA,GAAFlE,EAAE,CAAA+E,aAAA,CAAAL,GAAA,EAAAjB,SAAA;MAAA,MAAAW,MAAA,GAAFpE,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAgF,WAAA,CAq2F/CZ,MAAA,CAAAa,KAAA,CAAAf,QAAA,CAAAlB,EAAc,CAAC;IAAA,EAAC;IAr2F6BhD,EAAE,CAAA+D,UAAA,IAAAD,2CAAA,eAu2FjE,CAAC;IAv2F8D9D,EAAE,CAAAkF,YAAA,CA82FhF,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAM,QAAA,GAAAL,GAAA,CAAAJ,SAAA;IA92F6EzD,EAAE,CAAAqE,UAAA,YAAAH,QAAA,CAAAiB,OAg2FvC,CAAC,YAh2FoCnF,EAAE,CAAAoF,eAAA,IAAA7B,GAAA,EAAAW,QAAA,CAAAmB,KAAA,SAAAnB,QAAA,CAAAoB,OAAA,GAAApB,QAAA,CAAAoB,OAAA,cAAApB,QAAA,CAAAqB,OAAA,GAAArB,QAAA,CAAAqB,OAAA,cAAArB,QAAA,CAAAsB,IAAA,CAo2FhC,CAAC,gBAAAtB,QAAA,CAAAuB,WAEA,CAAC;IAt2F4BzF,EAAE,CAAA0F,SAAA,CA62FjF,CAAC;IA72F8E1F,EAAE,CAAA2F,aAAA,IAAAzB,QAAA,CAAA0B,IAAA,SA62FjF,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA3C,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA,WAAAJ,EAAA;EAAA,cAAAC,EAAA;EAAA,kBAAAC,EAAA;EAAA,YAAAC,EAAA;EAAA,YAAAC;AAAA;AAAA,MAAAwC,GAAA,GAAAA,CAAA5C,EAAA,EAAAC,EAAA;EAAA,mBAAAD,EAAA;EAAA,mBAAAC;AAAA;AAAA,MAAA4C,GAAA,GAAA7C,EAAA;EAAA,YAAAA;AAAA;AAAA,MAAA8C,GAAA,GAAAA,CAAA9C,EAAA,EAAAC,EAAA;EAAA,UAAAD,EAAA;EAAA,gBAAAC;AAAA;AAAA,SAAA8C,yCAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA72F8E5D,EAAE,CAAA4E,cAAA,YA80G3D,CAAC;IA90GwD5E,EAAE,CAAAkG,SAAA,kBAo1GlC,CAAC;IAp1G+BlG,EAAE,CAAAkF,YAAA,CAq1GlF,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,IAAAuC,OAAA;IAAA,MAAAC,MAAA,GAr1G+EpG,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAA0F,SAAA,CAk1GxE,CAAC;IAl1GqE1F,EAAE,CAAAqE,UAAA,iBAAFrE,EAAE,CAAAuE,eAAA,IAAAuB,GAAA,GAAAK,OAAA,GAAAC,MAAA,CAAAC,UAAA,qBAAAF,OAAA,CAAAG,eAAA,GAAAH,OAAA,GAAAC,MAAA,CAAAC,UAAA,qBAAAF,OAAA,CAAAI,eAAA,CAk1GxE,CAAC,cAAAH,MAAA,CAAAI,SAAA,EACsB,CAAC,eAAAJ,MAAA,CAAAK,UAAA,EACC,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+C,GAAA,GAp1G4C3G,EAAE,CAAA2E,gBAAA;IAAF3E,EAAE,CAAA4E,cAAA,YAg2GyC,CAAC;IAh2G5C5E,EAAE,CAAA6E,UAAA,mBAAA+B,oEAAA;MAAA,MAAAC,MAAA,GAAF7G,EAAE,CAAA+E,aAAA,CAAA4B,GAAA,EAAAlD,SAAA;MAAA,MAAA2C,MAAA,GAAFpG,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAgF,WAAA,CAg2GuBoB,MAAA,CAAAU,SAAA,CAAAD,MAAA,CAAA7D,EAAgB,CAAC;IAAA,EAAC;IAh2G3ChD,EAAE,CAAAkG,SAAA,cAi2GpC,CAAC;IAj2GiClG,EAAE,CAAAkF,YAAA,CAk2G5E,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAiD,MAAA,GAAAhD,GAAA,CAAAJ,SAAA;IAl2GyEzD,EAAE,CAAAqE,UAAA,YAAFrE,EAAE,CAAAuE,eAAA,IAAAyB,GAAA,EAAAa,MAAA,CAAAE,MAAA,EAAAF,MAAA,CAAAG,gBAAA,CAg2GY,CAAC;IAh2GfhH,EAAE,CAAA0F,SAAA,CAi2G5C,CAAC;IAj2GyC1F,EAAE,CAAAqE,UAAA,cAAAwC,MAAA,CAAAI,YAAA,EAAFjH,EAAE,CAAAkH,cAi2G5C,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwD,GAAA,GAj2GyCpH,EAAE,CAAA2E,gBAAA;IAAF3E,EAAE,CAAA4E,cAAA,YAy1GnB,CAAC,YACiE,CAAC;IA11GlD5E,EAAE,CAAA6E,UAAA,mBAAAwC,8DAAA;MAAFrH,EAAE,CAAA+E,aAAA,CAAAqC,GAAA;MAAA,MAAAhB,MAAA,GAAFpG,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAgF,WAAA,CA01GAoB,MAAA,CAAAkB,IAAA,CAAK,CAAC;IAAA,EAAC;IA11GTtH,EAAE,CAAAkF,YAAA,CA01GqD,CAAC;IA11GxDlF,EAAE,CAAA4E,cAAA,YA21G+C,CAAC;IA31GlD5E,EAAE,CAAA6E,UAAA,mBAAA0C,8DAAA;MAAFvH,EAAE,CAAA+E,aAAA,CAAAqC,GAAA;MAAA,MAAAhB,MAAA,GAAFpG,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAgF,WAAA,CA21GAoB,MAAA,CAAAoB,IAAA,CAAK,CAAC;IAAA,EAAC;IA31GTxH,EAAE,CAAAkF,YAAA,CA21GqD,CAAC,CACtI,CAAC;IA51G6ElF,EAAE,CAAA4E,cAAA,YA61GjB,CAAC;IA71Gc5E,EAAE,CAAAyH,gBAAA,IAAAf,8CAAA,kBAAA7D,UAm2GnF,CAAC;IAn2GgF7C,EAAE,CAAAkF,YAAA,CAq2GhF,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,IAAAuC,OAAA;IAAA,IAAAuB,OAAA;IAAA,IAAAC,OAAA;IAAA,IAAAC,OAAA;IAAA,IAAAC,OAAA;IAAA,IAAAC,OAAA;IAAA,IAAAC,OAAA;IAAA,MAAA3B,MAAA,GAr2G6EpG,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAAqE,UAAA,YAAFrE,EAAE,CAAAgI,eAAA,IAAAjC,GAAA,GAAAI,OAAA,GAAAC,MAAA,CAAA6B,OAAA,qBAAA9B,OAAA,CAAA+B,QAAA,CAy1GpB,CAAC;IAz1GiBlI,EAAE,CAAA0F,SAAA,CA01GX,CAAC;IA11GQ1F,EAAE,CAAAqE,UAAA,YAAFrE,EAAE,CAAAgI,eAAA,IAAAjC,GAAA,GAAA2B,OAAA,GAAAtB,MAAA,CAAA6B,OAAA,qBAAAP,OAAA,CAAAJ,IAAA,kBAAAI,OAAA,CAAAJ,IAAA,CAAAY,QAAA,CA01GX,CAAC,eAAAP,OAAA,GAAAvB,MAAA,CAAA6B,OAAA,qBAAAN,OAAA,CAAAL,IAAA,kBAAAK,OAAA,CAAAL,IAAA,CAAAa,QAAA,EA11GQnI,EAAE,CAAAkH,cA01G8C,CAAC;IA11GjDlH,EAAE,CAAA0F,SAAA,CA21GX,CAAC;IA31GQ1F,EAAE,CAAAqE,UAAA,YAAFrE,EAAE,CAAAgI,eAAA,KAAAjC,GAAA,GAAA6B,OAAA,GAAAxB,MAAA,CAAA6B,OAAA,qBAAAL,OAAA,CAAAJ,IAAA,kBAAAI,OAAA,CAAAJ,IAAA,CAAAU,QAAA,CA21GX,CAAC,eAAAL,OAAA,GAAAzB,MAAA,CAAA6B,OAAA,qBAAAJ,OAAA,CAAAL,IAAA,kBAAAK,OAAA,CAAAL,IAAA,CAAAW,QAAA,EA31GQnI,EAAE,CAAAkH,cA21G8C,CAAC;IA31GjDlH,EAAE,CAAA0F,SAAA,CA61GlB,CAAC;IA71Ge1F,EAAE,CAAAqE,UAAA,YAAFrE,EAAE,CAAAgI,eAAA,KAAAjC,GAAA,GAAA+B,OAAA,GAAA1B,MAAA,CAAAgC,QAAA,qBAAAN,OAAA,CAAAI,QAAA,CA61GlB,CAAC;IA71GelI,EAAE,CAAA0F,SAAA,CAm2GnF,CAAC;IAn2GgF1F,EAAE,CAAAqI,UAAA,EAAAN,OAAA,GA+1GnF3B,MAAA,CAAAgC,QAAA,CAAS,CAAC,mBAAAL,OAAA,CAAAO,IAIV,CAAC;EAAA;AAAA;AA79Gb,MAAMC,kBAAkB,CAAC;EACrBC,KAAK,GAAG,CAAC;EACTC,kBAAkB,GAAG,KAAK;EAC1BC,IAAI,GAAG,KAAK;EACZC,MAAM,GAAG,KAAK;EACdC,MAAM,GAAG,KAAK;EACdC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,IAAI;EAChBC,QAAQ,GAAG,IAAI;EACfC,QAAQ,GAAG,KAAK;EAChBC,MAAM,GAAG,CAAC;EACVC,YAAY,GAAG,CAAC;EAChB1H,KAAK,GAAG,KAAK;EACb2H,QAAQ,GAAG,IAAI;EACfC,SAAS,GAAG,KAAK;EACjBC,aAAa,GAAG,CAAC;EACjBC,GAAG,GAAG,KAAK;EACXC,UAAU,GAAG,GAAG;EAChBC,UAAU,GAAG,KAAK;EAClBC,YAAY,GAAG,KAAK;EACpBC,UAAU,GAAG,CAAC,CAAC;EACfC,qBAAqB,GAAG,GAAG;EAC3B;EACAC,GAAG,GAAG,KAAK;EACXC,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;EAC1BC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,CAAC,CAAC,CAAC;EACbzB,IAAI,GAAG,IAAI;EACX0B,QAAQ,GAAG,KAAK;EAChB5B,QAAQ,GAAG,KAAK;EAChB6B,SAAS,GAAG,KAAK;EACjB;EACAC,QAAQ,GAAG,KAAK;EAChBC,eAAe,GAAG,IAAI;EACtBC,kBAAkB,GAAG,KAAK;EAC1BC,aAAa,GAAG,KAAK;EACrBC,yBAAyB,GAAG,CAAC;EAC7B;EACAC,QAAQ,GAAG,KAAK;EAChBC,aAAa,GAAG,CAAC;EACjB;EACAC,eAAe,GAAG,EAAE;EACpBC,UAAU,GAAG,KAAK;EAClBC,SAAS,GAAG,KAAK;EACjB;EACAC,UAAU,GAAG,KAAK;EAClB;EACAC,eAAe,GAAG,KAAK;EACvBC,WAAWA,CAAA,EAAG,CAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBvC,KAAK,GAAG,QAAQ;EAChBC,kBAAkB,GAAG,SAAS;EAC9BC,IAAI,GAAG,SAAS;EAChBC,MAAM,GAAG,SAAS;EAClBC,MAAM,GAAG,SAAS;EAClBC,SAAS,GAAG,SAAS;EACrBC,SAAS,GAAG,SAAS;EACrBC,QAAQ,GAAG,SAAS;EACpBC,QAAQ,GAAG,SAAS;EACpBC,MAAM,GAAG,QAAQ;EACjBC,YAAY,GAAG,QAAQ;EACvB1H,KAAK,GAAG,SAAS;EACjB2H,QAAQ,GAAG,SAAS;EACpBC,SAAS,GAAG,SAAS;EACrBC,aAAa,GAAG,eAAe;EAC/BC,GAAG,GAAG,SAAS;EACfC,UAAU,GAAG,QAAQ;EACrBC,UAAU,GAAG,SAAS;EACtBC,YAAY,GAAG,gBAAgB;EAC/BC,UAAU,GAAG,CAAC,CAAC;EACfC,qBAAqB,GAAG,QAAQ;EAChC;EACAC,GAAG,GAAG,SAAS;EACfC,OAAO,GAAG,UAAU;EACpBC,QAAQ,GAAG,gBAAgB;EAC3BC,OAAO,GAAG,eAAe,CAAC,CAAC;EAC3BzB,IAAI,GAAG,SAAS;EAChB0B,QAAQ,GAAG,gBAAgB;EAC3B5B,QAAQ,GAAG,SAAS;EACpB6B,SAAS,GAAG,gBAAgB;EAC5B;EACAC,QAAQ,GAAG,SAAS;EACpBC,eAAe,GAAG,QAAQ;EAC1BC,kBAAkB,GAAG,SAAS;EAC9BC,aAAa,GAAG,gBAAgB;EAChCC,yBAAyB,GAAG,QAAQ;EACpC;EACAC,QAAQ,GAAG,SAAS;EACpBC,aAAa,GAAG,QAAQ;EACxB;EACAC,eAAe,GAAG,QAAQ;EAC1BC,UAAU,GAAG,gBAAgB;EAC7BC,SAAS,GAAG,gBAAgB;EAC5B;EACAC,UAAU,GAAG,SAAS;EACtB;EACAC,eAAe,GAAG,SAAS;EAC3BC,WAAWA,CAAA,EAAG,CAAE;AACpB;AAEA,MAAME,SAAS,CAAC;EACZC,YAAY;EACZH,WAAWA,CAACG,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACAC,GAAGA,CAACC,KAAK,EAAE,GAAGC,IAAI,EAAE;IAChB,IAAInL,SAAS,CAAC,CAAC,EAAE;MACboL,OAAO,CAACH,GAAG,CAACC,KAAK,EAAE,GAAGC,IAAI,CAAC;IAC/B;EACJ;EACAE,KAAKA,CAACA,KAAK,EAAE;IACT,IAAI,CAACL,YAAY,CAACM,WAAW,CAACD,KAAK,CAAC;EACxC;EACAE,IAAIA,CAACL,KAAK,EAAE,GAAGC,IAAI,EAAE;IACjBC,OAAO,CAACG,IAAI,CAACL,KAAK,EAAE,GAAGC,IAAI,CAAC;EAChC;EACA,OAAOK,IAAI,YAAAC,kBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFX,SAAS,EAAnBhL,EAAE,CAAA4L,QAAA,CAAmC5L,EAAE,CAAC6L,YAAY;EAAA;EAC7I,OAAOC,KAAK,kBAD6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EACYhB,SAAS;IAAAiB,OAAA,EAATjB,SAAS,CAAAS;EAAA;AACpH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH6FlM,EAAE,CAAAmM,iBAAA,CAGJnB,SAAS,EAAc,CAAC;IACvGoB,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEpM,EAAE,CAAC6L;EAAa,CAAC,CAAC;AAAA;;AAE7D;AACA;AACA;AACA,MAAMQ,MAAM,CAAC;EACTC,OAAO;EACPC,IAAI;AACR;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI;AACR,CAAC,UAAUA,IAAI,EAAE;EACbA,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO;EACvBA,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO;AAC3B,CAAC,EAAEA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK;AACT,CAAC,UAAUA,KAAK,EAAE;EACdA,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS;EAC5BA,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;EACxBA,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;AAC5B,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA,MAAMC,MAAM,CAAC;EACTC,CAAC;EACDC,CAAC;AACL;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBxG,UAAU;EACVG,SAAS;EACTC,UAAU;EACVwB,OAAO;EACPG,QAAQ;AACZ;AACA,MAAM0E,eAAe,CAAC;EAClBC,MAAM;EACN;AACJ;AACA;EACIC,qBAAqB,GAAG,IAAIzL,OAAO,CAAC,CAAC;EACrC;AACJ;AACA;EACI0L,qBAAqB,GAAG,IAAI1L,OAAO,CAAC,CAAC;EACrC;AACJ;AACA;EACI2L,wBAAwB,GAAG,IAAI3L,OAAO,CAAC,CAAC;EACxC;AACJ;AACA;EACI4L,yBAAyB,GAAG,IAAI5L,OAAO,CAAC,CAAC;EACzC;AACJ;AACA;EACI6L,mBAAmB,GAAG,IAAI7L,OAAO,CAAC,CAAC;EACnC;AACJ;AACA;EACI8L,oBAAoB,GAAG,IAAI9L,OAAO,CAAC,CAAC;EACpC;AACJ;AACA;EACI+L,gBAAgB,GAAG,IAAI/L,OAAO,CAAC,CAAC;EAChC;AACJ;AACA;EACIgM,iBAAiB,GAAG,IAAIhM,OAAO,CAAC,CAAC;EACjC;AACJ;AACA;EACIiM,iBAAiB,GAAG,IAAIjM,OAAO,CAAC,CAAC;EACjC;AACJ;AACA;EACIkM,mBAAmB,GAAG,IAAIlM,OAAO,CAAC,CAAC;EACnC;AACJ;AACA;EACImM,cAAc,GAAG,IAAInM,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA;EACIoM,iBAAiB,GAAG,IAAIpM,OAAO,CAAC,CAAC;EACjC;AACJ;AACA;EACIqM,QAAQ,GAAG;IACPpF,KAAK,EAAE;EACX,CAAC;EACD;AACJ;AACA;EACInC,UAAU,GAAG;IACTiD,GAAG,EAAE,KAAK;IACVuE,YAAY,EAAE,KAAK;IACnBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,KAAK;IAChB1H,eAAe,EAAE,KAAK;IACtB2H,MAAM,EAAE,KAAK;IACb1H,eAAe,EAAE;EACrB,CAAC;EACD;AACJ;AACA;EACIC,SAAS,GAAG;IACR0H,SAAS,EAAE,0BAA0B;IACrCxL,UAAU,EAAE,IAAI;IAChB2C,KAAK,EAAE,CAAC;IACR8I,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE;EACd,CAAC;EACD;AACJ;AACA;EACI3H,UAAU;EACV;AACJ;AACA;EACIwB,OAAO;EACP;AACJ;AACA;EACIG,QAAQ;EACR;AACJ;AACA;EACIiG,MAAM;EACN;AACJ;AACA;EACIC,MAAM,GAAG,EAAE,CAAC,CAAC;EACb;AACJ;AACA;EACIC,OAAO,GAAG,EAAE;EACZ;AACJ;AACA;EACIC,QAAQ,GAAG,CAAC,CAAC;EACb;AACJ;AACA;EACIC,QAAQ,GAAG,CAAC,CAAC;EACb;AACJ;AACA;EACIC,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;EACIC,OAAO,GAAG,EAAE;EACZ;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,EAAE;EACb;AACJ;AACA;EACIC,MAAM,GAAG,IAAI;EACb;AACJ;AACA;AACA;EACIC,YAAY,GAAG,EAAE;EACjB;AACJ;AACA;AACA;EACIC,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;EACIC,cAAc,GAAG,SAAS;EAC1B;AACJ;AACA;EACIC,QAAQ,GAAG,CAAC,CAAC;EACb;AACJ;AACA;EACIC,YAAY,GAAG,CAAC,CAAC;EACjB;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,YAAY;EAC5B;EACA;AACJ;AACA;EACIE,OAAO,GAAG;IACN9C,OAAO,EAAE,CAAC,CAAC;IACXC,IAAI,EAAE;MACF8C,YAAY,EAAE,CAAC,MAAM,CAAC;MACtBC,SAAS,EAAE,CAAC,MAAM,CAAC;MACnBC,QAAQ,EAAE,CAAC,aAAa;IAC5B;EACJ,CAAC;EACD;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACJ,OAAO;EACvB;EACA;AACJ;AACA;EACIK,KAAK,GAAG;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACI5N,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;IACtC6N,GAAG,EAAEC,KAAK,IAAI;MACVA,KAAK,CAACrD,OAAO,GAAG,IAAI,CAACgC,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,IAAI,CAACsB,QAAQ,CAAC,IAAI,CAAClB,QAAQ,CAAC,CAAC,EAAE1L,EAAE,CAAC,CAAC;IAClF;EACJ,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;IACInB,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;IACtC6N,GAAG,EAAGC,KAAK,IAAK;MACZ,MAAM1G,MAAM,GAAG,IAAI,CAAC2E,QAAQ,CAAC3E,MAAM,IAAI,EAAE;QAAE4G,IAAI,GAAG,CAAC,IAAI,CAACjC,QAAQ,CAACxE,SAAS;QAAEE,GAAG,GAAG,IAAI,CAACsE,QAAQ,CAACtE,GAAG;QAAEwG,GAAG,GAAG;UACvG,aAAa,EAAExG,GAAG,GAAGL,MAAM,GAAG,EAAE;UAChC,cAAc,EAAEK,GAAG,GAAG,EAAE,GAAGL;QAC/B,CAAC;MACD,IAAI,CAAC4G,IAAI,EAAE;QACP,IAAI,CAACpJ,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAI;UAC7BA,KAAK,CAAC1K,OAAO,GAAGwK,GAAG,CAAC,aAAa,CAAC;UAClCE,KAAK,CAACzK,OAAO,GAAGuK,GAAG,CAAC,cAAc,CAAC;QACvC,CAAC,CAAC;MACN;MACAH,KAAK,CAACG,GAAG,GAAGA,GAAG;IACnB;EACJ,CAAC,EAAE;IACCjO,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;IACtC6N,GAAG,EAAGC,KAAK,IAAK;MACZ,MAAMtK,KAAK,GAAG,CAAC,CAAC,IAAI,CAACA,KAAK,CAAC,CAAC,IAAI,IAAI,CAACuI,QAAQ,CAACpF,KAAK,IAAI,CAAC,CAAC,EAAEyH,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAACrC,QAAQ,CAAC3E,MAAM,IAAI,CAAC,CAAC;QAAE4G,IAAI,GAAG,CAAC,IAAI,CAACjC,QAAQ,CAACxE,SAAS;QAAE8G,MAAM,GAAG,EAAE;MACjJ,IAAI1O,KAAK,GAAG,CAAC;QAAE2O,QAAQ,GAAG,IAAI,CAAC7B,MAAM,CAAC8B,MAAM;MAC5CT,KAAK,CAACnH,KAAK,GAAG;QACVhH,KAAK,EAAE,KAAK;QACZ6D,KAAK,EAAEA;MACX,CAAC;MACD,OAAO8K,QAAQ,EAAE,GAAG,CAAC,EAAE;QACnB3O,KAAK,GAAG,IAAI,CAACoN,QAAQ,CAACuB,QAAQ,CAAC,IAAI,CAAC;QACpC3O,KAAK,GAAG,IAAI,CAACoM,QAAQ,CAACzE,QAAQ,IAAIkH,IAAI,CAACC,GAAG,CAAC9O,KAAK,EAAE,IAAI,CAACoM,QAAQ,CAACpF,KAAK,IAAI,CAAC,CAAC,IAAIhH,KAAK;QACpFmO,KAAK,CAACnH,KAAK,CAAChH,KAAK,GAAGA,KAAK,GAAG,CAAC,IAAImO,KAAK,CAACnH,KAAK,CAAChH,KAAK;QAClD0O,MAAM,CAACC,QAAQ,CAAC,GAAG,CAACN,IAAI,GAAG,IAAI,CAACvB,MAAM,CAAC6B,QAAQ,CAAC,CAAC9K,KAAK,CAAC,CAAC,GAAG,IAAI,CAACiJ,MAAM,CAAC6B,QAAQ,CAAC,CAAC9K,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAGA,KAAK,GAAG7D,KAAK;MACpH;MACA,IAAI,CAAC+M,OAAO,GAAG2B,MAAM;MACrB,IAAI,CAACzJ,UAAU,CAACsJ,OAAO,CAAC,CAACC,KAAK,EAAEO,CAAC,KAAK;QAClCP,KAAK,CAAC3K,KAAK,GAAG,IAAI,CAACkJ,OAAO,CAACgC,CAAC,CAAC;QAC7BP,KAAK,CAACzK,OAAO,GAAGoK,KAAK,CAACG,GAAG,CAAC,cAAc,CAAC;QACzCE,KAAK,CAAC1K,OAAO,GAAGqK,KAAK,CAACG,GAAG,CAAC,aAAa,CAAC;MAC5C,CAAC,CAAC;IACN;EACJ,CAAC,EAAE;IACCjO,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;IAC7B6N,GAAG,EAAEA,CAAA,KAAM;MACP,MAAMc,MAAM,GAAG,EAAE;QAAEhI,KAAK,GAAG,IAAI,CAAC8F,MAAM;QAAEV,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAChE;QACA6C,IAAI,GAAGJ,IAAI,CAACK,GAAG,CAAC9C,QAAQ,CAACpF,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;QAAEmI,IAAI,GAAGN,IAAI,CAACO,IAAI,CAACpI,KAAK,CAAC4H,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9E,IAAIS,MAAM,GAAG,EAAE;QAAEC,OAAO,GAAG,EAAE;QAAEC,MAAM,GAAGnD,QAAQ,CAAClF,IAAI,IAAIF,KAAK,CAAC4H,MAAM,GAAGxC,QAAQ,CAAChF,MAAM,GAAG6H,IAAI,GAAGJ,IAAI,CAACK,GAAG,CAACD,IAAI,EAAEE,IAAI,CAAC,GAAG,CAAC;MACzHI,MAAM,IAAI,CAAC;MACX,OAAOA,MAAM,EAAE,GAAG,CAAC,EAAE;QACjB;QACAP,MAAM,CAACQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAACT,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QACpDS,MAAM,CAACG,IAAI,CAAC;UAAE,GAAG,IAAI,CAACvK,UAAU,CAAC+J,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC;QAAE,CAAC,CAAC;QAC9DI,MAAM,CAACQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAACzI,KAAK,CAAC4H,MAAM,GAAG,CAAC,GAAG,CAACI,MAAM,CAACJ,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7EU,OAAO,CAACI,OAAO,CAAC;UAAE,GAAG,IAAI,CAACzK,UAAU,CAAC+J,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC;QAAE,CAAC,CAAC;MACtE;MACA,IAAI,CAACzB,OAAO,GAAG6B,MAAM;MACrBK,MAAM,GAAGA,MAAM,CAAC3O,GAAG,CAAC8N,KAAK,KAAK;QAC1B,GAAGA,KAAK;QACRhN,EAAE,EAAE,GAAG,IAAI,CAACgM,cAAc,GAAGgB,KAAK,CAAChN,EAAE,SAAS;QAC9CmO,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE;MACd,CAAC,CAAC,CAAC;MACHN,OAAO,GAAGA,OAAO,CAAC5O,GAAG,CAAC8N,KAAK,KAAK;QAC5B,GAAGA,KAAK;QACRhN,EAAE,EAAE,GAAG,IAAI,CAACgM,cAAc,GAAGgB,KAAK,CAAChN,EAAE,EAAE;QACvCmO,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE;MACd,CAAC,CAAC,CAAC;MACH,IAAI,CAAC3K,UAAU,GAAGqK,OAAO,CAACO,MAAM,CAAC,IAAI,CAAC5K,UAAU,CAAC,CAAC4K,MAAM,CAACR,MAAM,CAAC;IACpE;EACJ,CAAC,EAAE;IACChP,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;IACtC6N,GAAG,EAAEA,CAAA,KAAM;MACP,MAAMpG,GAAG,GAAG,IAAI,CAACsE,QAAQ,CAACtE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QAAEqH,IAAI,GAAG,IAAI,CAAChC,OAAO,CAACyB,MAAM,GAAG,IAAI,CAAC9B,MAAM,CAAC8B,MAAM;QAAEkB,WAAW,GAAG,EAAE;MACzG,IAAInB,QAAQ,GAAG,CAAC,CAAC;QAAEoB,QAAQ,GAAG,CAAC;QAAEjF,OAAO,GAAG,CAAC;MAC5C,OAAO,EAAE6D,QAAQ,GAAGQ,IAAI,EAAE;QACtBY,QAAQ,GAAGD,WAAW,CAACnB,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC;QACzC7D,OAAO,GAAG,IAAI,CAACiC,OAAO,CAAC,IAAI,CAACqB,QAAQ,CAACO,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACvC,QAAQ,CAAC3E,MAAM;QACtEqI,WAAW,CAACN,IAAI,CAACO,QAAQ,GAAGjF,OAAO,GAAGhD,GAAG,CAAC;MAC9C;MACA,IAAI,CAACwF,YAAY,GAAGwC,WAAW;IACnC;EACJ,CAAC,EAAE;IACCzP,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;IACtC6N,GAAG,EAAEA,CAAA,KAAM;MACP,MAAM8B,OAAO,GAAG,IAAI,CAAC5D,QAAQ,CAAC1E,YAAY,IAAI,CAAC;QAAEoI,WAAW,GAAG,IAAI,CAACxC,YAAY;QAAEgB,GAAG,GAAG;UACpF,OAAO,EAAEO,IAAI,CAACO,IAAI,CAACP,IAAI,CAACoB,GAAG,CAACH,WAAW,CAACA,WAAW,CAAClB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGoB,OAAO,GAAG,CAAC;UAC/E,cAAc,EAAEA,OAAO,IAAI,EAAE;UAC7B,eAAe,EAAEA,OAAO,IAAI;QAChC,CAAC;MACD,IAAI,CAAChL,SAAS,CAACnB,KAAK,GAAGyK,GAAG,CAACzK,KAAK,CAAC,CAAC;MAClC,IAAI,CAACmB,SAAS,CAAC2H,QAAQ,GAAG2B,GAAG,CAAC,cAAc,CAAC;MAC7C,IAAI,CAACtJ,SAAS,CAAC4H,QAAQ,GAAG0B,GAAG,CAAC,eAAe,CAAC;IAClD;EACJ,CAAC,EAAE;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAjO,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;IACtC6N,GAAG,EAAEC,KAAK,IAAI;MACV,IAAIrD,OAAO,GAAGqD,KAAK,CAACrD,OAAO,GAAG,IAAI,CAAC7F,UAAU,CAACiL,SAAS,CAAC1B,KAAK,IAAIA,KAAK,CAAChN,EAAE,KAAK2M,KAAK,CAACrD,OAAO,CAAC,GAAG,CAAC;MAChGA,OAAO,GAAG+D,IAAI,CAACK,GAAG,CAAC,IAAI,CAACiB,OAAO,CAAC,CAAC,EAAEtB,IAAI,CAACC,GAAG,CAAC,IAAI,CAACsB,OAAO,CAAC,CAAC,EAAEtF,OAAO,CAAC,CAAC;MACrE,IAAI,CAACuF,KAAK,CAACvF,OAAO,CAAC;IACvB;EACJ,CAAC,EAAE;IACCzK,MAAM,EAAE,CAAC,UAAU,CAAC;IACpB6N,GAAG,EAAEA,CAAA,KAAM;MACP,IAAI,CAAC9M,OAAO,CAAC,IAAI,CAAC0O,WAAW,CAAC,IAAI,CAAC5C,QAAQ,CAAC,CAAC;IACjD;EACJ,CAAC,EAAE;IACC7M,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;IAClD6N,GAAG,EAAEA,CAAA,KAAM;MACP,MAAMpG,GAAG,GAAG,IAAI,CAACsE,QAAQ,CAACtE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QAAEkI,OAAO,GAAG,CAAC,IAAI,CAAC5D,QAAQ,CAAC1E,YAAY,IAAI,CAAC,IAAI,CAAC;QAAE4I,OAAO,GAAG,EAAE;MACrG,IAAIC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAE3B,CAAC,EAAE4B,CAAC;MAClCJ,KAAK,GAAG,IAAI,CAACT,WAAW,CAAC,IAAI,CAAChF,OAAO,CAAC,CAAC,CAAC;MACxC,IAAI,OAAOyF,KAAK,KAAK,QAAQ,EAAE;QAC3BA,KAAK,IAAIP,OAAO;MACpB,CAAC,MACI;QACDO,KAAK,GAAG,CAAC;MACb;MACAC,GAAG,GAAGD,KAAK,GAAG,IAAI,CAAC1M,KAAK,CAAC,CAAC,GAAGiE,GAAG;MAChC,IAAIA,GAAG,KAAK,CAAC,CAAC,IAAI,IAAI,CAACsE,QAAQ,CAACjF,MAAM,EAAE;QACpC,MAAMyJ,MAAM,GAAG,IAAI,CAACtD,YAAY,CAACjN,MAAM,CAACwQ,OAAO,IAAI;UAC/C,OAAO,CAAC,IAAI,CAACzE,QAAQ,CAACpF,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG6J,OAAO,IAAIN,KAAK,GAAGM,OAAO,GAAGN,KAAK;QACpF,CAAC,CAAC;QACFA,KAAK,GAAGK,MAAM,CAAChC,MAAM,GAAGgC,MAAM,CAACA,MAAM,CAAChC,MAAM,GAAG,CAAC,CAAC,GAAG2B,KAAK;MAC7D;MACA,KAAKxB,CAAC,GAAG,CAAC,EAAE4B,CAAC,GAAG,IAAI,CAACrD,YAAY,CAACsB,MAAM,EAAEG,CAAC,GAAG4B,CAAC,EAAE5B,CAAC,EAAE,EAAE;QAClD0B,KAAK,GAAG5B,IAAI,CAACO,IAAI,CAAC,IAAI,CAAC9B,YAAY,CAACyB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QAChD2B,KAAK,GAAG7B,IAAI,CAACO,IAAI,CAACP,IAAI,CAACoB,GAAG,CAAC,IAAI,CAAC3C,YAAY,CAACyB,CAAC,CAAC,CAAC,GAAGiB,OAAO,GAAGlI,GAAG,CAAC;QACjE,IAAK,IAAI,CAACgJ,GAAG,CAACL,KAAK,EAAE,IAAI,EAAEF,KAAK,CAAC,IAAK,IAAI,CAACO,GAAG,CAACL,KAAK,EAAE,GAAG,EAAED,GAAG,CAAE,IACxD,IAAI,CAACM,GAAG,CAACJ,KAAK,EAAE,GAAG,EAAEH,KAAK,CAAC,IAAI,IAAI,CAACO,GAAG,CAACJ,KAAK,EAAE,GAAG,EAAEF,GAAG,CAAE,EAAE;UAC/DF,OAAO,CAACd,IAAI,CAACT,CAAC,CAAC;QACnB;MACJ;MACA,IAAI,CAAC9J,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAI;QAC7BA,KAAK,CAACmB,QAAQ,GAAG,KAAK;QACtB,OAAOnB,KAAK;MAChB,CAAC,CAAC;MACF8B,OAAO,CAAC/B,OAAO,CAACwC,IAAI,IAAI;QACpB,IAAI,CAAC9L,UAAU,CAAC8L,IAAI,CAAC,CAACpB,QAAQ,GAAG,IAAI;MACzC,CAAC,CAAC;MACF,IAAI,IAAI,CAACvD,QAAQ,CAACjF,MAAM,EAAE;QACtB,IAAI,CAAClC,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAI;UAC7BA,KAAK,CAACwC,UAAU,GAAG,KAAK;UACxB,OAAOxC,KAAK;QAChB,CAAC,CAAC;QACF,IAAI,IAAI,CAACvJ,UAAU,CAAC,IAAI,CAAC6F,OAAO,CAAC,CAAC,CAAC,EAAE;UACjC,IAAI,CAAC7F,UAAU,CAAC,IAAI,CAAC6F,OAAO,CAAC,CAAC,CAAC,CAACkG,UAAU,GAAG,IAAI;QACrD;MACJ;IACJ;EACJ,CAAC,CACJ;EACD1H,WAAWA,CAACiC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;AACA;EACI0F,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACzF,qBAAqB,CAAC0F,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC1F,qBAAqB,CAACyF,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1F,wBAAwB,CAACwF,YAAY,CAAC,CAAC;EACvD;EACA;AACJ;AACA;AACA;EACIG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1F,yBAAyB,CAACuF,YAAY,CAAC,CAAC;EACxD;EACA;AACJ;AACA;AACA;EACII,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC1F,mBAAmB,CAACsF,YAAY,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACIK,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC1F,oBAAoB,CAACqF,YAAY,CAAC,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACIM,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1F,gBAAgB,CAACoF,YAAY,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;EACIO,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1F,iBAAiB,CAACmF,YAAY,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIQ,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1F,iBAAiB,CAACkF,YAAY,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIS,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC1F,mBAAmB,CAACiF,YAAY,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACIU,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1F,cAAc,CAACgF,YAAY,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIW,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1F,iBAAiB,CAAC+E,YAAY,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIY,UAAUA,CAACC,OAAO,EAAE;IAChB,MAAMC,aAAa,GAAG,IAAIjL,kBAAkB,CAAC,CAAC;IAC9C,MAAMkL,cAAc,GAAG,IAAI,CAACC,gBAAgB,CAACH,OAAO,EAAEC,aAAa,CAAC;IACpE,IAAI,CAACvE,QAAQ,GAAG;MAAE,GAAGuE,aAAa;MAAE,GAAGC;IAAe,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,gBAAgBA,CAACH,OAAO,EAAEC,aAAa,EAAE;IACrC,MAAMC,cAAc,GAAG;MAAE,GAAGF;IAAQ,CAAC;IACrC,MAAMI,WAAW,GAAG,IAAI5I,qBAAqB,CAAC,CAAC;IAC/C,MAAM6I,cAAc,GAAGA,CAACxH,IAAI,EAAEyH,GAAG,KAAK;MAClC,IAAI,CAAC9G,MAAM,CAAC7B,GAAG,CAAC,WAAW2I,GAAG,oBAAoBzH,IAAI,KAAKyH,GAAG,IAAIN,OAAO,CAACM,GAAG,CAAC,yBAAyBA,GAAG,IAAIL,aAAa,CAACK,GAAG,CAAC,EAAE,CAAC;MACnI,OAAOL,aAAa,CAACK,GAAG,CAAC;IAC7B,CAAC;IACD,KAAK,MAAMA,GAAG,IAAIJ,cAAc,EAAE;MAC9B,IAAIA,cAAc,CAACK,cAAc,CAACD,GAAG,CAAC,EAAE;QACpC;QACA,IAAIF,WAAW,CAACE,GAAG,CAAC,KAAK,QAAQ,EAAE;UAC/B,IAAI,IAAI,CAACE,UAAU,CAACN,cAAc,CAACI,GAAG,CAAC,CAAC,EAAE;YACtCJ,cAAc,CAACI,GAAG,CAAC,GAAG,CAACJ,cAAc,CAACI,GAAG,CAAC;YAC1CJ,cAAc,CAACI,GAAG,CAAC,GAAGA,GAAG,KAAK,OAAO,GAC/B,IAAI,CAACG,cAAc,CAACP,cAAc,CAACI,GAAG,CAAC,EAAEJ,cAAc,CAAChL,kBAAkB,CAAC,GAC3EgL,cAAc,CAACI,GAAG,CAAC;UAC7B,CAAC,MACI;YACDJ,cAAc,CAACI,GAAG,CAAC,GAAGD,cAAc,CAACD,WAAW,CAACE,GAAG,CAAC,EAAEA,GAAG,CAAC;UAC/D;QACJ,CAAC,MACI,IAAIF,WAAW,CAACE,GAAG,CAAC,KAAK,SAAS,IAAI,OAAOJ,cAAc,CAACI,GAAG,CAAC,KAAK,SAAS,EAAE;UACjFJ,cAAc,CAACI,GAAG,CAAC,GAAGD,cAAc,CAACD,WAAW,CAACE,GAAG,CAAC,EAAEA,GAAG,CAAC;QAC/D,CAAC,MACI,IAAIF,WAAW,CAACE,GAAG,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAAI,CAACI,kBAAkB,CAACR,cAAc,CAACI,GAAG,CAAC,CAAC,EAAE;UAC7FJ,cAAc,CAACI,GAAG,CAAC,GAAGD,cAAc,CAACD,WAAW,CAACE,GAAG,CAAC,EAAEA,GAAG,CAAC;QAC/D,CAAC,MACI,IAAIF,WAAW,CAACE,GAAG,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,CAACK,iBAAiB,CAACT,cAAc,CAACI,GAAG,CAAC,CAAC,EAAE;UAC3FJ,cAAc,CAACI,GAAG,CAAC,GAAGD,cAAc,CAACD,WAAW,CAACE,GAAG,CAAC,EAAEA,GAAG,CAAC;QAC/D,CAAC,MACI,IAAIF,WAAW,CAACE,GAAG,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAAI,CAACM,kBAAkB,CAACV,cAAc,CAACI,GAAG,CAAC,CAAC,EAAE;UAC7FJ,cAAc,CAACI,GAAG,CAAC,GAAGD,cAAc,CAACD,WAAW,CAACE,GAAG,CAAC,EAAEA,GAAG,CAAC;QAC/D,CAAC,MACI,IAAIF,WAAW,CAACE,GAAG,CAAC,KAAK,UAAU,EAAE;UACtC,IAAIO,KAAK,CAACC,OAAO,CAACZ,cAAc,CAACI,GAAG,CAAC,CAAC,EAAE;YACpC,IAAIS,QAAQ,GAAG,KAAK;YACpBb,cAAc,CAACI,GAAG,CAAC,CAAC9D,OAAO,CAACsC,OAAO,IAAI;cACnCiC,QAAQ,GAAG,OAAOjC,OAAO,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK;YACzD,CAAC,CAAC;YACF,IAAI,CAACiC,QAAQ,EAAE;cACXb,cAAc,CAACI,GAAG,CAAC,GAAGD,cAAc,CAACD,WAAW,CAACE,GAAG,CAAC,EAAEA,GAAG,CAAC;YAC/D;YACA;UACJ,CAAC,MACI;YACDJ,cAAc,CAACI,GAAG,CAAC,GAAGD,cAAc,CAACD,WAAW,CAACE,GAAG,CAAC,EAAEA,GAAG,CAAC;UAC/D;QACJ;MACJ;IACJ;IACA,OAAOJ,cAAc;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIO,cAAcA,CAACxL,KAAK,EAAEC,kBAAkB,EAAE;IACtC,IAAI2J,MAAM,GAAG5J,KAAK;IAClB,IAAIA,KAAK,GAAG,IAAI,CAAC8F,MAAM,CAAC8B,MAAM,EAAE;MAC5B,IAAI3H,kBAAkB,EAAE;QACpB,IAAI,CAACsE,MAAM,CAAC7B,GAAG,CAAC,uGAAuG,CAAC;MAC5H,CAAC,MACI;QACDkH,MAAM,GAAG,IAAI,CAAC9D,MAAM,CAAC8B,MAAM;QAC3B,IAAI,CAACrD,MAAM,CAAC7B,GAAG,CAAC,kKAAkK,CAAC;MACvL;IACJ,CAAC,MACI;MACD,IAAI1C,KAAK,KAAK,IAAI,CAAC8F,MAAM,CAAC8B,MAAM,KAAK,IAAI,CAACxC,QAAQ,CAACtF,IAAI,IAAI,IAAI,CAACsF,QAAQ,CAAChE,GAAG,CAAC,EAAE;QAC3E,IAAI,CAACmD,MAAM,CAAC7B,GAAG,CAAC,mGAAmG,CAAC;MACxH;IACJ;IACA,OAAOkH,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACImC,gBAAgBA,CAAClP,KAAK,EAAE;IACpB,IAAI,CAACgJ,MAAM,GAAGhJ,KAAK;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACImP,KAAKA,CAACC,aAAa,EAAEC,MAAM,EAAEnB,OAAO,EAAE;IAClC,IAAI,CAACgB,gBAAgB,CAACE,aAAa,CAAC;IACpC,IAAI,CAACE,QAAQ,CAACD,MAAM,CAAC;IACrB,IAAI,CAACE,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACtB,UAAU,CAACC,OAAO,CAAC;IACxB,IAAI,CAAC3F,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACqB;IAAS,CAAC;IACpC,IAAI,CAAC4F,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE;MAAEC,QAAQ,EAAE;QAAEC,IAAI,EAAE,UAAU;QAAE7J,KAAK,EAAE,IAAI,CAACyC;MAAS;IAAE,CAAC,CAAC;IACjF,IAAI,CAACqH,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACH,QAAQ,CAAC,SAAS,EAAE;MAAEC,QAAQ,EAAE;QAAEC,IAAI,EAAE,UAAU;QAAE7J,KAAK,EAAE,IAAI,CAACyC;MAAS;IAAE,CAAC,CAAC;EACtF;EACA;AACJ;AACA;EACIiH,qBAAqBA,CAAA,EAAG;IACpB,MAAMK,QAAQ,GAAG,IAAI,CAAC7G,MAAM;MAAE8G,UAAU,GAAG,IAAI,CAAClG,QAAQ,CAACvF,UAAU,IAAI,CAAC,CAAC;IACzE,IAAI0L,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC/E,MAAM,EAAE;MACjC;IACJ;IACA,IAAI,CAAC8E,QAAQ,EAAE;MACX,IAAI,CAACtH,QAAQ,CAACpF,KAAK,GAAG,CAAC;MACvB;IACJ;IACA,KAAK,MAAMqL,GAAG,IAAIsB,UAAU,EAAE;MAC1B,IAAIA,UAAU,CAACrB,cAAc,CAACD,GAAG,CAAC,EAAE;QAChC,IAAI,CAACA,GAAG,IAAIqB,QAAQ,IAAI,CAACrB,GAAG,GAAGuB,KAAK,EAAE;UAClCA,KAAK,GAAGG,MAAM,CAAC1B,GAAG,CAAC;QACvB;MACJ;IACJ;IACA,IAAI,CAACjG,QAAQ,GAAG;MACZ,GAAG,IAAI,CAACqB,QAAQ;MAChB,GAAGkG,UAAU,CAACC,KAAK,CAAC;MACpB5M,KAAK,EAAG2M,UAAU,CAACC,KAAK,CAAC,IAAID,UAAU,CAACC,KAAK,CAAC,CAAC5M,KAAK,GAC9C,IAAI,CAACwL,cAAc,CAACmB,UAAU,CAACC,KAAK,CAAC,CAAC5M,KAAK,EAAE,IAAI,CAACyG,QAAQ,CAACxG,kBAAkB,CAAC,GAC9E,IAAI,CAACwG,QAAQ,CAACzG;IACxB,CAAC;IACD;IACA;IACA;IACA,OAAO,IAAI,CAACoF,QAAQ,CAAClE,UAAU;IAC/B,IAAI,CAACrD,UAAU,CAACwH,YAAY,GAAG,IAAI;IACnC,IAAI,CAACxH,UAAU,CAACC,eAAe,GAAG,IAAI,CAACsH,QAAQ,CAAC/E,SAAS;IACzD,IAAI,CAACxC,UAAU,CAACE,eAAe,GAAG,IAAI,CAACqH,QAAQ,CAAC9E,SAAS;IACzD,MAAM0M,OAAO,GAAG,EAAE;IAClB,IAAI,CAAClH,MAAM,CAACyB,OAAO,CAACwC,IAAI,IAAI;MACxB,MAAMkD,MAAM,GAAG,IAAI,CAAC7H,QAAQ,CAACpM,KAAK,GAAG+Q,IAAI,CAACmD,SAAS,CAAC,CAAC,GAAG,CAAC;MACzDF,OAAO,CAACxE,IAAI,CAACyE,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,IAAI,CAAC7G,QAAQ,GAAG4G,OAAO;IACvB,IAAI,CAACzG,WAAW,GAAGqG,KAAK;IACxB,IAAI,CAACH,UAAU,CAAC,UAAU,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIU,UAAUA,CAACjB,MAAM,EAAE;IACf,IAAI,CAACkB,KAAK,CAAC,cAAc,CAAC;IAC1B;IACA,IAAI,CAACvP,UAAU,CAACiD,GAAG,GAAG,IAAI,CAACsE,QAAQ,CAACtE,GAAG;IACvC,IAAI,IAAI,CAACsF,QAAQ,CAACwB,MAAM,EAAE;MACtB,IAAI,CAACxB,QAAQ,GAAG,EAAE;IACtB;IACA8F,MAAM,CAAC3E,OAAO,CAACwC,IAAI,IAAI;MACnB,MAAMkD,MAAM,GAAG,IAAI,CAAC7H,QAAQ,CAACpM,KAAK,GAAG+Q,IAAI,CAACmD,SAAS,CAAC,CAAC,GAAG,CAAC;MACzD,IAAI,CAAC9G,QAAQ,CAACoC,IAAI,CAACyE,MAAM,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,CAAC9G,OAAO,GAAG,EAAE;IACjB,IAAI,CAACkD,KAAK,CAAC,IAAI,CAACkC,UAAU,CAAC,IAAI,CAACnG,QAAQ,CAACvE,aAAa,CAAC,GAAG,EAAE,IAAI,CAACuE,QAAQ,EAAEvE,aAAa,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACnG,IAAI,CAAC4L,UAAU,CAAC,OAAO,CAAC;IACxB,IAAI,CAACY,OAAO,CAAC,CAAC;IACd,IAAI,CAACxP,UAAU,CAAC0H,QAAQ,GAAG,IAAI;IAC/B,IAAI,CAAC1H,UAAU,CAACC,eAAe,GAAG,IAAI,CAACsH,QAAQ,CAAC/E,SAAS;IACzD,IAAI,CAACxC,UAAU,CAACE,eAAe,GAAG,IAAI,CAACqH,QAAQ,CAAC9E,SAAS;IACzD,IAAI,CAACgN,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,KAAK,CAAC,cAAc,CAAC;IAC1B,IAAI,CAACjB,QAAQ,CAAC,aAAa,CAAC;EAChC;EAEA;AACJ;AACA;EACIgB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9I,qBAAqB,CAACxF,IAAI,CAAC;MAC5BnB,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BG,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BwB,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBG,QAAQ,EAAE,IAAI,CAACA;IACnB,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI4N,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACpI,QAAQ,CAACxE,SAAS,EAAE;MACzB,IAAI,CAACwE,QAAQ,CAAC1E,YAAY,GAAG,CAAC;MAC9B,IAAI,CAAC0E,QAAQ,CAACpM,KAAK,GAAG,KAAK;IAC/B;EACJ;EACA;AACJ;AACA;EACIyU,MAAMA,CAAA,EAAG;IACL,IAAI1F,CAAC,GAAG,CAAC;IACT,MAAM4B,CAAC,GAAG,IAAI,CAAC1C,KAAK,CAACW,MAAM;MAAEvO,MAAM,GAAG0Q,IAAI,IAAI,IAAI,CAACrD,YAAY,CAACqD,IAAI,CAAC;MAAE5C,KAAK,GAAG,CAAC,CAAC;IACjF,OAAOY,CAAC,GAAG4B,CAAC,EAAE;MACV,MAAM+D,YAAY,GAAG,IAAI,CAACzG,KAAK,CAACc,CAAC,CAAC,CAAC1O,MAAM,CAACA,MAAM,CAACA,MAAM,CAAC;MACxD,IAAI,IAAI,CAACqN,YAAY,CAACiH,GAAG,IAAID,YAAY,CAAC9F,MAAM,GAAG,CAAC,EAAE;QAClD,IAAI,CAACX,KAAK,CAACc,CAAC,CAAC,CAACb,GAAG,CAACC,KAAK,CAAC;MAC5B;MACAY,CAAC,EAAE;IACP;IACA,IAAI,CAAC9J,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7K,OAAO,GAAG,IAAI,CAACiR,kBAAkB,CAACpG,KAAK,CAAC,CAAC;IAChF,IAAI,CAAC8F,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC5G,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAAC,IAAI,CAACmH,EAAE,CAAC,OAAO,CAAC,EAAE;MACnB,IAAI,CAACT,KAAK,CAAC,OAAO,CAAC;IACvB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIvQ,KAAKA,CAACiR,SAAS,EAAE;IACbA,SAAS,GAAGA,SAAS,IAAI7J,KAAK,CAAC8J,OAAO;IACtC,QAAQD,SAAS;MACb,KAAK7J,KAAK,CAAC+J,KAAK;MAChB,KAAK/J,KAAK,CAACgK,KAAK;QACZ,OAAO,IAAI,CAACpI,MAAM;MACtB;QACI,OAAO,IAAI,CAACA,MAAM,GAAG,CAAC,IAAI,CAACT,QAAQ,CAAC1E,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC0E,QAAQ,CAAC3E,MAAM,IAAI,CAAC,CAAC;IAChG;EACJ;EACA;AACJ;AACA;EACI4M,OAAOA,CAAA,EAAG;IACN,IAAI,CAACD,KAAK,CAAC,YAAY,CAAC;IACxB,IAAI,CAACd,QAAQ,CAAC,SAAS,CAAC;IACxB,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACmB,aAAa,CAAC,CAAC;IACpB;IACA,IAAI,CAACC,MAAM,CAAC,CAAC;IACb;IACA,IAAI,CAACF,KAAK,CAAC,YAAY,CAAC;IACxB,IAAI,CAACjB,QAAQ,CAAC,WAAW,CAAC;EAC9B;EACA;AACJ;AACA;AACA;EACI4B,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAAC,IAAI,CAACrI,MAAM,CAAC8B,MAAM,EAAE;MACrB,OAAO,KAAK;IAChB;IACA,IAAI,CAACmE,gBAAgB,CAACoC,QAAQ,CAAC;IAC/B,IAAI,CAACf,KAAK,CAAC,UAAU,CAAC;IACtB;IACA;IACA;IACA;IACA,IAAI,CAACd,QAAQ,CAAC,QAAQ,CAAC;IACvB,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC;IACxB,IAAI,CAACY,OAAO,CAAC,CAAC;IACd,IAAI,CAACE,KAAK,CAAC,UAAU,CAAC;IACtB,IAAI,CAACjB,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8B,eAAeA,CAACC,KAAK,EAAE;IACnB,IAAIC,KAAK,EAAEC,YAAY;IACvB;IACA;IACA;IACA;IACA;IACA;IACAA,YAAY,GAAG,IAAI,CAACvQ,SAAS,CAAC0H,SAAS,CAAC8I,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACzFH,KAAK,GAAG;MACJnK,CAAC,EAAE,CAACoK,YAAY,CAAC,CAAC,CAAC;MACnBnK,CAAC,EAAE,CAACmK,YAAY,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,IAAI,CAACV,EAAE,CAAC,WAAW,CAAC,EAAE;MACtB,IAAI,CAACpB,UAAU,CAAC,UAAU,CAAC;IAC/B;IACA,IAAI4B,KAAK,CAACzK,IAAI,KAAK,WAAW,EAAE;MAC5B,IAAI,CAAC/F,UAAU,CAAC4H,MAAM,GAAG,IAAI;IACjC;IACA,IAAI,CAACiJ,KAAK,CAAC,CAAC,CAAC;IACb,OAAOJ,KAAK;EAChB;EACA;AACJ;AACA;EACIK,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACvB,KAAK,CAAC,UAAU,CAAC;IACtB,IAAI,CAACd,QAAQ,CAAC,MAAM,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIsC,mBAAmBA,CAACP,KAAK,EAAEQ,QAAQ,EAAE;IACjC,IAAI1F,OAAO;MAAEC,OAAO;MAAE0F,IAAI,GAAG,CAAC;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAACH,QAAQ,CAACI,OAAO,EAAE,IAAI,CAACA,OAAO,CAACZ,KAAK,CAAC,CAAC;MAAEC,KAAK,GAAG,IAAI,CAACU,UAAU,CAACH,QAAQ,CAACP,KAAK,CAACY,KAAK,EAAEH,KAAK,CAAC;IAC1H,IAAI,CAAC,IAAI,CAAClB,EAAE,CAAC,UAAU,CAAC,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACzI,QAAQ,CAAClF,IAAI,EAAE;MACpBiJ,OAAO,GAAG,IAAI,CAACL,WAAW,CAAC,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC;MAC1CC,OAAO,GAAG,CAAC,IAAI,CAACN,WAAW,CAAC,IAAI,CAACM,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGD,OAAO;MACzDmF,KAAK,CAACnK,CAAC,GAAI,CAAC,CAACmK,KAAK,CAACnK,CAAC,GAAGgF,OAAO,IAAIC,OAAO,GAAGA,OAAO,IAAIA,OAAO,GAAID,OAAO;IAC7E,CAAC,MACI;MACDA,OAAO,GAAG,IAAI,CAAC/D,QAAQ,CAACtE,GAAG,GAAG,IAAI,CAACgI,WAAW,CAAC,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACN,WAAW,CAAC,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC;MACjGC,OAAO,GAAG,IAAI,CAAChE,QAAQ,CAACtE,GAAG,GAAG,IAAI,CAACgI,WAAW,CAAC,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACL,WAAW,CAAC,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC;MACjG0F,IAAI,GAAG,IAAI,CAAC1J,QAAQ,CAAC7E,QAAQ,GAAG,CAAC,CAAC,GAAGwO,KAAK,CAAC5K,CAAC,GAAG,CAAC,GAAG,CAAC;MACpDmK,KAAK,CAACnK,CAAC,GAAG0D,IAAI,CAACK,GAAG,CAACL,IAAI,CAACC,GAAG,CAACwG,KAAK,CAACnK,CAAC,EAAEgF,OAAO,GAAG2F,IAAI,CAAC,EAAE1F,OAAO,GAAG0F,IAAI,CAAC;IACzE;IACA,OAAOR,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,cAAcA,CAACd,KAAK,EAAEe,OAAO,EAAEC,aAAa,EAAE;IAC1C,MAAMC,UAAU,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;MAAEP,KAAK,GAAG,IAAI,CAACC,UAAU,CAACI,OAAO,CAACH,OAAO,EAAE,IAAI,CAACA,OAAO,CAACZ,KAAK,CAAC,CAAC;MAAEC,KAAK,GAAGc,OAAO,CAACd,KAAK,CAACxK,OAAO;MAAEyL,SAAS,GAAGD,UAAU,CAAC,EAAE,IAAI,CAAClK,QAAQ,CAACtE,GAAG,GAAGiO,KAAK,CAAC5K,CAAC,GAAG,CAAC,IAAI,CAACiB,QAAQ,CAACtE,GAAG,GAAGiO,KAAK,CAAC5K,CAAC,GAAG,EAAE,IAAI,CAACiB,QAAQ,CAACtE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACrP,IAAI0O,aAAa,EAAE1L,OAAO,EAAE2L,UAAU;IACtC,IAAIV,KAAK,CAAC5K,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC0J,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAACA,EAAE,CAAC,OAAO,CAAC,EAAE;MAC3D,IAAI,CAACa,KAAK,CAAC,EAAE,IAAI,CAACtJ,QAAQ,CAACnE,YAAY,IAAI,CAAC,CAAC,IAAI,IAAI,CAACmE,QAAQ,CAACrE,UAAU,CAAC;MAC1EyO,aAAa,GAAG,IAAI,CAACE,OAAO,CAACpB,KAAK,CAACnK,CAAC,EAAE4K,KAAK,CAAC5K,CAAC,KAAK,CAAC,GAAGoL,SAAS,GAAGH,OAAO,CAACG,SAAS,CAAC;MACpFzL,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;MACxB2L,UAAU,GAAG,IAAI,CAAC3L,OAAO,CAAC0L,aAAa,KAAK,CAAC,CAAC,GAAGG,SAAS,GAAGH,aAAa,CAAC;MAC3E,IAAI1L,OAAO,KAAK2L,UAAU,EAAE;QACxB,IAAI,CAAChD,UAAU,CAAC,UAAU,CAAC;QAC3B,IAAI,CAACgB,MAAM,CAAC,CAAC;MACjB;MACA2B,OAAO,CAACG,SAAS,GAAGA,SAAS;MAC7B,IAAI1H,IAAI,CAACoB,GAAG,CAAC8F,KAAK,CAAC5K,CAAC,CAAC,GAAG,CAAC,IAAI,IAAIyL,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGT,OAAO,CAACU,IAAI,GAAG,GAAG,EAAE;QACpET,aAAa,CAAC,CAAC;MACnB;IACJ;IACA,IAAI,CAAC,IAAI,CAACxB,EAAE,CAAC,UAAU,CAAC,EAAE;MACtB;IACJ;IACA,IAAI,CAACN,KAAK,CAAC,UAAU,CAAC;IACtB,IAAI,CAACjB,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIoD,OAAOA,CAACK,UAAU,EAAER,SAAS,EAAE;IAC3B,MAAMT,IAAI,GAAG,EAAE;MAAEjS,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IACrC,IAAIiM,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC;MAAEkH,QAAQ,GAAG,CAAC,CAAC;IACnD,IAAI,IAAI,CAAC5K,QAAQ,CAACjF,MAAM,EAAE;MACtB2I,WAAW,GAAGA,WAAW,CAACpP,GAAG,CAACqQ,IAAI,IAAI;QAClC,IAAIA,IAAI,KAAK,CAAC,EAAE;UACZA,IAAI,IAAI,QAAQ;QACpB;QACA,OAAOA,IAAI;MACf,CAAC,CAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,WAAW,CAAClB,MAAM,EAAEG,CAAC,EAAE,EAAE;MACzC,IAAIwH,SAAS,KAAK,MAAM,IAAIQ,UAAU,GAAGjH,WAAW,CAACf,CAAC,CAAC,GAAG+G,IAAI,IAAIiB,UAAU,GAAGjH,WAAW,CAACf,CAAC,CAAC,GAAG+G,IAAI,EAAE;QAClGkB,QAAQ,GAAGjI,CAAC;QACZ;QACA;MACJ,CAAC,MACI,IAAIwH,SAAS,KAAK,OAAO,IAAIQ,UAAU,GAAGjH,WAAW,CAACf,CAAC,CAAC,GAAGlL,KAAK,GAAGiS,IAAI,IAAIiB,UAAU,GAAGjH,WAAW,CAACf,CAAC,CAAC,GAAGlL,KAAK,GAAGiS,IAAI,EAAE;QACxHkB,QAAQ,GAAGjI,CAAC,GAAG,CAAC;MACpB,CAAC,MACI,IAAI,IAAI,CAAC+B,GAAG,CAACiG,UAAU,EAAE,GAAG,EAAEjH,WAAW,CAACf,CAAC,CAAC,CAAC,IAC3C,IAAI,CAAC+B,GAAG,CAACiG,UAAU,EAAE,GAAG,EAAEjH,WAAW,CAACf,CAAC,GAAG,CAAC,CAAC,IAAIe,WAAW,CAACf,CAAC,CAAC,GAAGlL,KAAK,CAAC,EAAE;QAC5EmT,QAAQ,GAAGT,SAAS,KAAK,MAAM,GAAGxH,CAAC,GAAG,CAAC,GAAGA,CAAC;MAC/C,CAAC,MACI,IAAIwH,SAAS,KAAK,IAAI,IAAIQ,UAAU,GAAGjH,WAAW,CAACf,CAAC,CAAC,GAAG+G,IAAI,IAAIiB,UAAU,GAAGjH,WAAW,CAACf,CAAC,CAAC,GAAG+G,IAAI,EAAE;QACrGkB,QAAQ,GAAGjI,CAAC;MAChB;MACA,IAAIiI,QAAQ,KAAK,CAAC,CAAC,EAAE;QACjB;MACJ;MACA;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAAC5K,QAAQ,CAAClF,IAAI,EAAE;MACrB;MACA,IAAI,IAAI,CAAC4J,GAAG,CAACiG,UAAU,EAAE,GAAG,EAAEjH,WAAW,CAAC,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACxD6G,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC5G,OAAO,CAAC,CAAC;MAC1C,CAAC,MACI,IAAI,IAAI,CAACW,GAAG,CAACiG,UAAU,EAAE,GAAG,EAAEjH,WAAW,CAAC,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7D4G,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC3G,OAAO,CAAC,CAAC;MAC1C;IACJ;IACA,OAAO4G,QAAQ;EACnB;EACA;AACJ;AACA;AACA;AACA;EACI5V,OAAOA,CAAC2V,UAAU,EAAE;IAChB,MAAM3V,OAAO,GAAG,IAAI,CAACsU,KAAK,CAAC,CAAC,GAAG,CAAC;IAChC,IAAI,IAAI,CAACb,EAAE,CAAC,WAAW,CAAC,EAAE;MACtB,IAAI,CAACoC,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI7V,OAAO,EAAE;MACT,IAAI,CAACgT,KAAK,CAAC,WAAW,CAAC;MACvB,IAAI,CAACd,QAAQ,CAAC,WAAW,CAAC;IAC9B;IACA,IAAI,CAACtO,SAAS,CAAC0H,SAAS,GAAG,cAAc,GAAGqK,UAAU,GAAG,aAAa;IACtE,IAAI,CAAC/R,SAAS,CAAC9D,UAAU,GAAI,IAAI,CAACwU,KAAK,CAAC,CAAC,GAAG,IAAI,GAAI,GAAG,IAAI,IAAI,CAACtJ,QAAQ,CAACnD,eAAe,GAAG,GAAG,GAAG,IAAI,CAACmD,QAAQ,CAACnD,eAAe,GAAG,EAAE,CAAC;IACpI;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI4L,EAAEA,CAAC5T,KAAK,EAAE;IACN,OAAO,IAAI,CAAC2M,OAAO,CAAC9C,OAAO,CAAC7J,KAAK,CAAC,IAAI,IAAI,CAAC2M,OAAO,CAAC9C,OAAO,CAAC7J,KAAK,CAAC,GAAG,CAAC;EACzE;EAEA;AACJ;AACA;AACA;AACA;EACI6J,OAAOA,CAACkM,QAAQ,EAAE;IACd,IAAIA,QAAQ,KAAKL,SAAS,EAAE;MACxB,OAAO,IAAI,CAACzJ,QAAQ;IACxB;IACA,IAAI,IAAI,CAACJ,MAAM,CAAC8B,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO+H,SAAS;IACpB;IACAK,QAAQ,GAAG,IAAI,CAACvH,SAAS,CAACuH,QAAQ,CAAC;IACnC,IAAI,IAAI,CAAC9J,QAAQ,KAAK8J,QAAQ,EAAE;MAC5B,MAAM3B,KAAK,GAAG,IAAI,CAAC/B,QAAQ,CAAC,QAAQ,EAAE;QAAEC,QAAQ,EAAE;UAAEC,IAAI,EAAE,UAAU;UAAE7J,KAAK,EAAEqN;QAAS;MAAE,CAAC,CAAC;MAC1F;MACA;MACA;MACA,IAAI,CAAC9J,QAAQ,GAAG8J,QAAQ;MACxB,IAAI,CAACvD,UAAU,CAAC,UAAU,CAAC;MAC3B,IAAI,CAACH,QAAQ,CAAC,SAAS,EAAE;QAAEC,QAAQ,EAAE;UAAEC,IAAI,EAAE,UAAU;UAAE7J,KAAK,EAAE,IAAI,CAACuD;QAAS;MAAE,CAAC,CAAC;IACtF;IACA,OAAO,IAAI,CAACA,QAAQ;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIuG,UAAUA,CAACyD,IAAI,EAAE;IACb,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACxJ,YAAY,CAACwJ,IAAI,CAAC,GAAG,IAAI;MAC9B,IAAI,IAAI,CAACrC,EAAE,CAAC,OAAO,CAAC,EAAE;QAClB,IAAI,CAACN,KAAK,CAAC,OAAO,CAAC;MACvB;IACJ;IACA,OAAOV,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpG,YAAY,CAAC;EACzC;EAEA;AACJ;AACA;AACA;EACI2C,KAAKA,CAAC2G,QAAQ,EAAE;IACZA,QAAQ,GAAG,IAAI,CAACvH,SAAS,CAACuH,QAAQ,CAAC;IACnC,IAAIA,QAAQ,KAAKL,SAAS,EAAE;MACxB;IACJ;IACA,IAAI,CAACtJ,MAAM,GAAG,CAAC;IACf,IAAI,CAACH,QAAQ,GAAG8J,QAAQ;IACxB,IAAI,CAACG,SAAS,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IAC3C,IAAI,CAAC/V,OAAO,CAAC,IAAI,CAAC0O,WAAW,CAACkH,QAAQ,CAAC,CAAC;IACxC,IAAI,CAACI,QAAQ,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;EACI3H,SAASA,CAACuH,QAAQ,EAAE5I,QAAQ,EAAE;IAC1B,MAAMuC,CAAC,GAAG,IAAI,CAAC7D,MAAM,CAAC8B,MAAM;MAAEyI,CAAC,GAAGjJ,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACjB,OAAO,CAACyB,MAAM;IACpE,IAAIgC,MAAM,GAAGoG,QAAQ;IACrB,IAAI,CAAC,IAAI,CAACzE,UAAU,CAACyE,QAAQ,CAAC,IAAIrG,CAAC,GAAG,CAAC,EAAE;MACrCC,MAAM,GAAG+F,SAAS;IACtB,CAAC,MACI,IAAIK,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAIrG,CAAC,GAAG0G,CAAC,EAAE;MACxCzG,MAAM,GAAG,CAAC,CAACoG,QAAQ,GAAGK,CAAC,GAAG,CAAC,IAAI1G,CAAC,GAAGA,CAAC,IAAIA,CAAC,GAAG0G,CAAC,GAAG,CAAC;IACrD;IACA,OAAOzG,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIxC,QAAQA,CAAC4I,QAAQ,EAAE;IACfA,QAAQ,IAAI,IAAI,CAAC7J,OAAO,CAACyB,MAAM,GAAG,CAAC;IACnC,OAAO,IAAI,CAACa,SAAS,CAACuH,QAAQ,EAAE,IAAI,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACI5G,OAAOA,CAAChC,QAAQ,GAAG,KAAK,EAAE;IACtB,MAAMhC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIgE,OAAO,GAAG,IAAI,CAAC9C,YAAY,CAACsB,MAAM;MAAED,QAAQ;MAAE2I,oBAAoB;MAAEC,YAAY;IACpF,IAAInL,QAAQ,CAAClF,IAAI,EAAE;MACfkJ,OAAO,GAAG,IAAI,CAACjD,OAAO,CAACyB,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC9B,MAAM,CAAC8B,MAAM,GAAG,CAAC;IAC9D,CAAC,MACI,IAAIxC,QAAQ,CAACxE,SAAS,IAAIwE,QAAQ,CAACpM,KAAK,EAAE;MAC3C2O,QAAQ,GAAG,IAAI,CAAC7B,MAAM,CAAC8B,MAAM;MAC7B0I,oBAAoB,GAAG,IAAI,CAACrS,UAAU,CAAC,EAAE0J,QAAQ,CAAC,CAAC9K,KAAK;MACxD0T,YAAY,GAAG,IAAI,CAAC1K,MAAM;MAC1B,OAAO8B,QAAQ,EAAE,GAAG,CAAC,EAAE;QACnB;QACA2I,oBAAoB,IAAI,EAAE,IAAI,CAACrS,UAAU,CAAC0J,QAAQ,CAAC,CAAC9K,KAAK,IAAI,CAAC,CAAC,IAAI,IAAI,CAACuI,QAAQ,CAAC3E,MAAM,IAAI,CAAC,CAAC;QAC7F,IAAI6P,oBAAoB,GAAGC,YAAY,EAAE;UACrC;QACJ;MACJ;MACAnH,OAAO,GAAGzB,QAAQ,GAAG,CAAC;IAC1B,CAAC,MACI,IAAIvC,QAAQ,CAACjF,MAAM,EAAE;MACtBiJ,OAAO,GAAG,IAAI,CAACtD,MAAM,CAAC8B,MAAM,GAAG,CAAC;IACpC,CAAC,MACI;MACDwB,OAAO,GAAG,IAAI,CAACtD,MAAM,CAAC8B,MAAM,IAAIxC,QAAQ,CAACpF,KAAK,IAAI,CAAC,CAAC;IACxD;IACA,IAAIoH,QAAQ,EAAE;MACVgC,OAAO,IAAI,IAAI,CAACjD,OAAO,CAACyB,MAAM,GAAG,CAAC;IACtC;IACA,OAAOC,IAAI,CAACK,GAAG,CAACkB,OAAO,EAAE,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACID,OAAOA,CAAC/B,QAAQ,GAAG,KAAK,EAAE;IACtB,OAAOA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACjB,OAAO,CAACyB,MAAM,GAAG,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;EACI5H,KAAKA,CAACgQ,QAAQ,EAAE;IACZ,IAAIA,QAAQ,KAAKL,SAAS,EAAE;MACxB,OAAO,IAAI,CAAC7J,MAAM,CAAC0K,KAAK,CAAC,CAAC;IAC9B;IACAR,QAAQ,GAAG,IAAI,CAACvH,SAAS,CAACuH,QAAQ,EAAE,IAAI,CAAC;IACzC,OAAO,CAAC,IAAI,CAAClK,MAAM,CAACkK,QAAQ,CAAC,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIhD,OAAOA,CAACgD,QAAQ,EAAE;IACd,IAAIA,QAAQ,KAAKL,SAAS,EAAE;MACxB,OAAO,IAAI,CAACvJ,QAAQ,CAACoK,KAAK,CAAC,CAAC;IAChC;IACAR,QAAQ,GAAG,IAAI,CAACvH,SAAS,CAACuH,QAAQ,EAAE,IAAI,CAAC;IACzC,OAAO,IAAI,CAAC5J,QAAQ,CAAC4J,QAAQ,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIhI,MAAMA,CAACgI,QAAQ,EAAE;IACb,MAAMS,GAAG,GAAG,IAAI,CAACtK,OAAO,CAACyB,MAAM,GAAG,CAAC;MAAE8I,IAAI,GAAGD,GAAG,GAAG,IAAI,CAAC3K,MAAM,CAAC8B,MAAM;MAAElO,GAAG,GAAIwB,KAAK,IAAKA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGwV,IAAI,GAAGxV,KAAK,GAAG,CAAC,GAAGuV,GAAG,GAAG,CAACvV,KAAK,GAAG,CAAC,IAAI,CAAC;IACjJ,IAAI8U,QAAQ,KAAKL,SAAS,EAAE;MACxB,OAAO,IAAI,CAACxJ,OAAO,CAACzM,GAAG,CAAC,CAACiX,CAAC,EAAE5I,CAAC,KAAKrO,GAAG,CAACqO,CAAC,CAAC,CAAC;IAC7C;IACA,OAAO,IAAI,CAAC5B,OAAO,CAACzM,GAAG,CAAC,CAACiX,CAAC,EAAE5I,CAAC,KAAK4I,CAAC,KAAKX,QAAQ,GAAGtW,GAAG,CAACqO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC1O,MAAM,CAAC0Q,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;EACnG;EACA;AACJ;AACA;AACA;AACA;EACI2E,KAAKA,CAACA,KAAK,EAAE;IACT,IAAIA,KAAK,KAAKiB,SAAS,EAAE;MACrB,IAAI,CAACtJ,MAAM,GAAGqI,KAAK;IACvB;IACA,OAAO,IAAI,CAACrI,MAAM;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIyC,WAAWA,CAACkH,QAAQ,EAAE;IAClB,IAAIY,UAAU,GAAG,CAAC;MAAEC,WAAW,GAAG,CAACb,QAAQ,IAAI,CAAC,IAAI,CAAC;MAAED,UAAU;MAAEnG,MAAM;IACzE,IAAIoG,QAAQ,KAAKL,SAAS,EAAE;MACxB/F,MAAM,GAAG,IAAI,CAACtD,YAAY,CAAC5M,GAAG,CAAC,CAACqQ,IAAI,EAAE7O,KAAK,KAAK;QAC5C,OAAO,IAAI,CAAC4N,WAAW,CAAC5N,KAAK,CAAC;MAClC,CAAC,CAAC;MACF,OAAO0O,MAAM;IACjB;IACA,IAAI,IAAI,CAACxE,QAAQ,CAACjF,MAAM,EAAE;MACtB,IAAI,IAAI,CAACiF,QAAQ,CAACtE,GAAG,EAAE;QACnB8P,UAAU,GAAG,CAAC,CAAC;QACfC,WAAW,GAAGb,QAAQ,GAAG,CAAC;MAC9B;MACAD,UAAU,GAAG,IAAI,CAACzJ,YAAY,CAAC0J,QAAQ,CAAC;MACxCD,UAAU,IAAI,CAAC,IAAI,CAAClT,KAAK,CAAC,CAAC,GAAGkT,UAAU,IAAI,IAAI,CAACzJ,YAAY,CAACuK,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAGD,UAAU;IACtG,CAAC,MACI;MACDb,UAAU,GAAG,IAAI,CAACzJ,YAAY,CAACuK,WAAW,CAAC,IAAI,CAAC;IACpD;IACAd,UAAU,GAAGlI,IAAI,CAACO,IAAI,CAAC2H,UAAU,CAAC;IAClC,OAAOA,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIe,SAASA,CAAC3X,IAAI,EAAE4X,EAAE,EAAEC,MAAM,EAAE;IACxB,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,CAAC;IACZ;IACA,OAAOnJ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACL,IAAI,CAACoB,GAAG,CAAC8H,EAAE,GAAG5X,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG0O,IAAI,CAACoB,GAAG,CAAC,EAAE+H,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC5L,QAAQ,CAACrE,UAAU,IAAI,CAAC,CAAC;EACpH;EACA;AACJ;AACA;AACA;AACA;EACIgQ,EAAEA,CAACf,QAAQ,EAAEtB,KAAK,EAAE;IAChB,IAAI5K,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEmN,MAAM;MAAEC,QAAQ,GAAGlB,QAAQ,GAAG,IAAI,CAAC5I,QAAQ,CAACtD,OAAO,CAAC;MAAEsF,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;MAAE+H,YAAY,GAAG,CAAC;IAC9H,MAAM5B,SAAS,GAAG,EAAE2B,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAAElR,KAAK,GAAG,IAAI,CAAC8F,MAAM,CAAC8B,MAAM;MAAEuB,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;IACzG,IAAI,IAAI,CAAC/D,QAAQ,CAAClF,IAAI,EAAE;MACpB,IAAI,CAAC,IAAI,CAACkF,QAAQ,CAAChF,MAAM,IAAIyH,IAAI,CAACoB,GAAG,CAACiI,QAAQ,CAAC,GAAGlR,KAAK,GAAG,CAAC,EAAE;QACzDkR,QAAQ,IAAI3B,SAAS,GAAG,CAAC,CAAC,GAAGvP,KAAK;MACtC;MACAgQ,QAAQ,GAAGlM,OAAO,GAAGoN,QAAQ;MAC7BD,MAAM,GAAG,CAAC,CAACjB,QAAQ,GAAG7G,OAAO,IAAInJ,KAAK,GAAGA,KAAK,IAAIA,KAAK,GAAGmJ,OAAO;MACjE,IAAI8H,MAAM,KAAKjB,QAAQ,IAAIiB,MAAM,GAAGC,QAAQ,IAAI9H,OAAO,IAAI6H,MAAM,GAAGC,QAAQ,GAAG,CAAC,EAAE;QAC9EpN,OAAO,GAAGmN,MAAM,GAAGC,QAAQ;QAC3BlB,QAAQ,GAAGiB,MAAM;QACjBE,YAAY,GAAG,EAAE;QACjB,IAAI,CAAC9H,KAAK,CAACvF,OAAO,CAAC;QACnB,IAAI,CAACwJ,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,MACI,IAAI,IAAI,CAAClI,QAAQ,CAAChF,MAAM,EAAE;MAC3BgJ,OAAO,IAAI,CAAC;MACZ4G,QAAQ,GAAG,CAACA,QAAQ,GAAG5G,OAAO,GAAGA,OAAO,IAAIA,OAAO;IACvD,CAAC,MACI;MACD4G,QAAQ,GAAGnI,IAAI,CAACK,GAAG,CAACiB,OAAO,EAAEtB,IAAI,CAACC,GAAG,CAACsB,OAAO,EAAE4G,QAAQ,CAAC,CAAC;IAC7D;IACAoB,UAAU,CAAC,MAAM;MACb,IAAI,CAAC1C,KAAK,CAAC,IAAI,CAACoC,SAAS,CAAChN,OAAO,EAAEkM,QAAQ,EAAEtB,KAAK,CAAC,CAAC;MACpD,IAAI,CAAC5K,OAAO,CAACkM,QAAQ,CAAC;MACtB,IAAI,CAACvC,MAAM,CAAC,CAAC;IACjB,CAAC,EAAE0D,YAAY,CAAC;EACpB;EACA;AACJ;AACA;AACA;EACInS,IAAIA,CAAC0P,KAAK,EAAE;IACRA,KAAK,GAAGA,KAAK,IAAI,KAAK;IACtB,IAAI,CAACqC,EAAE,CAAC,IAAI,CAAC3J,QAAQ,CAAC,IAAI,CAACtD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE4K,KAAK,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACI5P,IAAIA,CAAC4P,KAAK,EAAE;IACRA,KAAK,GAAGA,KAAK,IAAI,KAAK;IACtB,IAAI,CAACqC,EAAE,CAAC,IAAI,CAAC3J,QAAQ,CAAC,IAAI,CAACtD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE4K,KAAK,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACIuB,eAAeA,CAAC5B,KAAK,EAAE;IACnB;IACA,IAAIA,KAAK,KAAKsB,SAAS,EAAE;MACrB;MACA;MACA;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA,IAAI,CAACpC,KAAK,CAAC,WAAW,CAAC;IACvB,IAAI,CAACjB,QAAQ,CAAC,YAAY,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACI+E,SAASA,CAAA,EAAG;IACR,IAAIxU,KAAK;IACT,IAAI,IAAI,CAACgJ,MAAM,EAAE;MACbhJ,KAAK,GAAG,IAAI,CAACgJ,MAAM;IACvB,CAAC,MACI;MACD,IAAI,CAACtB,MAAM,CAAC7B,GAAG,CAAC,gCAAgC,CAAC;IACrD;IACA,OAAO7F,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACIsP,QAAQA,CAACmF,OAAO,EAAE;IACd,IAAI,CAACxL,MAAM,GAAGwL,OAAO;EACzB;EACA;AACJ;AACA;EACIlF,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA;IACA;IACA,IAAImF,OAAO;IACX,IAAI,IAAI,CAACtT,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC2J,MAAM,EAAE;MAC3C2J,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;MACnB,IAAI,CAACvT,UAAU,CAACsJ,OAAO,CAACwC,IAAI,IAAI;QAC5B,IAAIA,IAAI,CAAC3M,IAAI,EAAE;UACXmU,OAAO,CAACE,GAAG,CAAC1H,IAAI,CAACvP,EAAE,EAAEuP,IAAI,CAAC3M,IAAI,CAAC;QACnC;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACa,UAAU,GAAG,IAAI,CAAC6H,MAAM,CAACpM,GAAG,CAAC8N,KAAK,IAAI;MACvC,OAAO;QACHhN,EAAE,EAAE,GAAGgN,KAAK,CAAChN,EAAE,CAAC,CAAC,EAAE;QACnBmO,QAAQ,EAAE,KAAK;QACf7M,MAAM,EAAE0L,KAAK,CAAC1L,MAAM;QACpBoR,SAAS,EAAE1F,KAAK,CAAC0F,SAAS,CAAC,CAAC;QAC5BrQ,KAAK,EAAE,CAAC;QACR+L,QAAQ,EAAE,KAAK;QACfxL,IAAI,EAAEmU,OAAO,GAAGA,OAAO,CAACG,GAAG,CAAClK,KAAK,CAAChN,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK;QAC/CmX,YAAY,EAAEnK,KAAK,CAACoK,QAAQ,CAAC;MACjC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIhE,kBAAkBA,CAACpG,KAAK,EAAE;IACtB;IACA,MAAMqK,cAAc,GAAG;MACnB,QAAQ,EAAErK,KAAK,CAACmB,QAAQ,IAAI,KAAK;MACjC,QAAQ,EAAEnB,KAAK,CAACwC,UAAU,IAAI,KAAK;MACnC,QAAQ,EAAExC,KAAK,CAACoB,QAAQ,IAAI,KAAK;MACjC,UAAU,EAAEpB,KAAK,CAACsK,UAAU,IAAI,KAAK;MACrC,iBAAiB,EAAEtK,KAAK,CAACuK,eAAe,IAAI,KAAK;MACjD,kBAAkB,EAAEvK,KAAK,CAACwK,gBAAgB,IAAI;IAClD,CAAC;IACD,IAAI,IAAI,CAAC5M,QAAQ,CAACjD,SAAS,EAAE;MACzB0P,cAAc,CAAC,IAAI,CAACzM,QAAQ,CAACjD,SAAS,CAAC,GAAGqF,KAAK,CAACyK,kBAAkB,IAAI,KAAK;IAC/E;IACA,IAAI,IAAI,CAAC7M,QAAQ,CAAClD,UAAU,EAAE;MAC1B2P,cAAc,CAAC,IAAI,CAACzM,QAAQ,CAAClD,UAAU,CAAC,GAAGsF,KAAK,CAAC0K,mBAAmB,IAAI,KAAK;IACjF;IACA,OAAOL,cAAc;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI/H,GAAGA,CAACqI,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACT,MAAMvR,GAAG,GAAG,IAAI,CAACsE,QAAQ,CAACtE,GAAG;IAC7B,QAAQsR,CAAC;MACL,KAAK,GAAG;QACJ,OAAOtR,GAAG,GAAGqR,CAAC,GAAGE,CAAC,GAAGF,CAAC,GAAGE,CAAC;MAC9B,KAAK,GAAG;QACJ,OAAOvR,GAAG,GAAGqR,CAAC,GAAGE,CAAC,GAAGF,CAAC,GAAGE,CAAC;MAC9B,KAAK,IAAI;QACL,OAAOvR,GAAG,GAAGqR,CAAC,IAAIE,CAAC,GAAGF,CAAC,IAAIE,CAAC;MAChC,KAAK,IAAI;QACL,OAAOvR,GAAG,GAAGqR,CAAC,IAAIE,CAAC,GAAGF,CAAC,IAAIE,CAAC;MAChC;QACI;IACR;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/F,QAAQA,CAACE,IAAI,EAAE8F,IAAI,EAAEC,SAAS,EAAEtY,KAAK,EAAEmT,KAAK,EAAE;IAC1C,QAAQZ,IAAI;MACR,KAAK,aAAa;QACd,IAAI,CAAC/H,qBAAqB,CAACzF,IAAI,CAACwN,IAAI,CAAC;QACrC;MACJ,KAAK,QAAQ;QACT,IAAI,CAAC9H,wBAAwB,CAAC1F,IAAI,CAACsT,IAAI,CAAC;QACxC;MACJ,KAAK,SAAS;QACV,IAAI,CAAC3N,yBAAyB,CAAC3F,IAAI,CAACsT,IAAI,CAAC;QACzC;MACJ,KAAK,MAAM;QACP,IAAI,CAACpN,cAAc,CAAClG,IAAI,CAACwN,IAAI,CAAC;QAC9B;MACJ,KAAK,SAAS;QACV,IAAI,CAACrH,iBAAiB,CAACnG,IAAI,CAACwN,IAAI,CAAC;QACjC;MACJ,KAAK,QAAQ;QACT,IAAI,CAAC1H,gBAAgB,CAAC9F,IAAI,CAACwN,IAAI,CAAC;QAChC;MACJ,KAAK,SAAS;QACV,IAAI,CAACzH,iBAAiB,CAAC/F,IAAI,CAACwN,IAAI,CAAC;QACjC;MACJ,KAAK,SAAS;QACV,IAAI,CAACxH,iBAAiB,CAAChG,IAAI,CAACwN,IAAI,CAAC;QACjC;MACJ,KAAK,WAAW;QACZ,IAAI,CAACvH,mBAAmB,CAACjG,IAAI,CAACwN,IAAI,CAAC;QACnC;MACJ,KAAK,WAAW;QACZ,IAAI,CAAC5H,mBAAmB,CAAC5F,IAAI,CAACwN,IAAI,CAAC;QACnC;MACJ,KAAK,YAAY;QACb,IAAI,CAAC3H,oBAAoB,CAAC7F,IAAI,CAACwN,IAAI,CAAC;QACpC;MACJ;QACI;IACR;EACJ;EACA;AACJ;AACA;AACA;EACIY,KAAKA,CAACZ,IAAI,EAAE;IACR,CAACA,IAAI,CAAC,CAAC3D,MAAM,CAAC,IAAI,CAACjC,OAAO,CAAC7C,IAAI,CAACyI,IAAI,CAAC,IAAI,EAAE,CAAC,CAACjF,OAAO,CAAEiL,SAAS,IAAK;MAChE,IAAI,IAAI,CAAC5L,OAAO,CAAC9C,OAAO,CAAC0O,SAAS,CAAC,KAAK7C,SAAS,EAAE;QAC/C,IAAI,CAAC/I,OAAO,CAAC9C,OAAO,CAAC0O,SAAS,CAAC,GAAG,CAAC;MACvC;MACA,IAAI,CAAC5L,OAAO,CAAC9C,OAAO,CAAC0O,SAAS,CAAC,EAAE;IACrC,CAAC,CAAC;EACN;EAEA;AACJ;AACA;AACA;EACIjF,KAAKA,CAACf,IAAI,EAAE;IACR,CAACA,IAAI,CAAC,CAAC3D,MAAM,CAAC,IAAI,CAACjC,OAAO,CAAC7C,IAAI,CAACyI,IAAI,CAAC,IAAI,EAAE,CAAC,CAACjF,OAAO,CAAEiL,SAAS,IAAK;MAChE,IAAI,IAAI,CAAC5L,OAAO,CAAC9C,OAAO,CAAC0O,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC5L,OAAO,CAAC9C,OAAO,CAAC0O,SAAS,CAAC,EAAE;QAC5E,IAAI,CAAC5L,OAAO,CAAC9C,OAAO,CAAC0O,SAAS,CAAC,EAAE;MACrC;IACJ,CAAC,CAAC;EACN;EAEA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,MAAM,EAAE;IACb,IAAIA,MAAM,CAAC9O,IAAI,KAAKI,IAAI,CAAC2O,KAAK,EAAE;MAC5B,IAAI,CAAC,IAAI,CAAC/L,OAAO,CAAC7C,IAAI,CAAC2O,MAAM,CAAClG,IAAI,CAAC,EAAE;QACjC,IAAI,CAAC5F,OAAO,CAAC7C,IAAI,CAAC2O,MAAM,CAAClG,IAAI,CAAC,GAAGkG,MAAM,CAAC3O,IAAI;MAChD,CAAC,MACI;QACD,IAAI,CAAC6C,OAAO,CAAC7C,IAAI,CAAC2O,MAAM,CAAClG,IAAI,CAAC,GAAG,IAAI,CAAC5F,OAAO,CAAC7C,IAAI,CAAC2O,MAAM,CAAClG,IAAI,CAAC,CAAC3D,MAAM,CAAC6J,MAAM,CAAC3O,IAAI,CAAC;MACvF;MACA,IAAI,CAAC6C,OAAO,CAAC7C,IAAI,CAAC2O,MAAM,CAAClG,IAAI,CAAC,GAAG,IAAI,CAAC5F,OAAO,CAAC7C,IAAI,CAAC2O,MAAM,CAAClG,IAAI,CAAC,CAACnT,MAAM,CAAC,CAACuZ,GAAG,EAAE7K,CAAC,KAAK;QAC/E,OAAO,IAAI,CAACnB,OAAO,CAAC7C,IAAI,CAAC2O,MAAM,CAAClG,IAAI,CAAC,CAACqG,OAAO,CAACD,GAAG,CAAC,KAAK7K,CAAC;MAC5D,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACIoI,SAASA,CAAC2C,MAAM,EAAE;IACdA,MAAM,CAACvL,OAAO,CAAC8G,KAAK,IAAI;MACpB,IAAI,CAACrI,QAAQ,CAACqI,KAAK,CAAC,GAAG,IAAI;IAC/B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI+B,QAAQA,CAAC0C,MAAM,EAAE;IACbA,MAAM,CAACvL,OAAO,CAAC8G,KAAK,IAAI;MACpB,OAAO,IAAI,CAACrI,QAAQ,CAACqI,KAAK,CAAC;IAC/B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIY,OAAOA,CAACZ,KAAK,EAAE;IACX,MAAMzE,MAAM,GAAG;MAAEzF,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC7BiK,KAAK,GAAGA,KAAK,CAAC0E,aAAa,IAAI1E,KAAK,IAAI2E,MAAM,CAAC3E,KAAK;IACpDA,KAAK,GAAGA,KAAK,CAAC4E,OAAO,IAAI5E,KAAK,CAAC4E,OAAO,CAACrL,MAAM,GACzCyG,KAAK,CAAC4E,OAAO,CAAC,CAAC,CAAC,GAAG5E,KAAK,CAAC6E,cAAc,IAAI7E,KAAK,CAAC6E,cAAc,CAACtL,MAAM,GACtEyG,KAAK,CAAC6E,cAAc,CAAC,CAAC,CAAC,GAAG7E,KAAK;IACnC,IAAIA,KAAK,CAAC8E,KAAK,EAAE;MACbvJ,MAAM,CAACzF,CAAC,GAAGkK,KAAK,CAAC8E,KAAK;MACtBvJ,MAAM,CAACxF,CAAC,GAAGiK,KAAK,CAAC+E,KAAK;IAC1B,CAAC,MACI;MACDxJ,MAAM,CAACzF,CAAC,GAAGkK,KAAK,CAACgF,OAAO;MACxBzJ,MAAM,CAACxF,CAAC,GAAGiK,KAAK,CAACiF,OAAO;IAC5B;IACA,OAAO1J,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACI2B,UAAUA,CAACgI,MAAM,EAAE;IACf,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,MAAM,CAAC,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACI9H,kBAAkBA,CAAC9I,KAAK,EAAE;IACtB,OAAO,IAAI,CAAC4I,UAAU,CAAC5I,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,SAAS;EAC/D;EACA;AACJ;AACA;AACA;AACA;EACI+I,iBAAiBA,CAAC/I,KAAK,EAAE;IACrB,OAAO,IAAI,CAAC4I,UAAU,CAAC5I,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ;EAC9D;EACA;AACJ;AACA;AACA;AACA;EACIgJ,kBAAkBA,CAAChJ,KAAK,EAAE;IACtB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqM,UAAUA,CAACzV,KAAK,EAAEma,MAAM,EAAE;IACtB,IAAI,IAAI,KAAKna,KAAK,IAAI,IAAI,KAAKma,MAAM,EAAE;MACnC,OAAO;QACHvP,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACP,CAAC;IACL;IACA,OAAO;MACHD,CAAC,EAAE5K,KAAK,CAAC4K,CAAC,GAAGuP,MAAM,CAACvP,CAAC;MACrBC,CAAC,EAAE7K,KAAK,CAAC6K,CAAC,GAAGsP,MAAM,CAACtP;IACxB,CAAC;EACL;EACA,OAAOnB,IAAI,YAAA0Q,wBAAAxQ,CAAA;IAAA,YAAAA,CAAA,IAAwFmB,eAAe,EA5+CzB9M,EAAE,CAAA4L,QAAA,CA4+CyCZ,SAAS;EAAA;EAC7I,OAAOc,KAAK,kBA7+C6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EA6+CYc,eAAe;IAAAb,OAAA,EAAfa,eAAe,CAAArB;EAAA;AAC1H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA/+C6FlM,EAAE,CAAAmM,iBAAA,CA++CJW,eAAe,EAAc,CAAC;IAC7GV,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEpB;EAAU,CAAC,CAAC;AAAA;AAEvD,MAAMoR,iBAAiB,CAAC;EACpBC,eAAe;EACf;AACJ;AACA;EACIC,eAAe;EACf;AACJ;AACA;EACIC,YAAY,GAAG,KAAK;EACpB;AACJ;AACA;EACIC,MAAM,GAAG,EAAE;EACX;AACJ;AACA;EACIC,QAAQ,GAAG;IACPvU,QAAQ,EAAE,KAAK;IACfZ,IAAI,EAAE;MACFY,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;IACd,CAAC;IACDX,IAAI,EAAE;MACFU,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;IACd;EACJ,CAAC;EACD;AACJ;AACA;EACIuU,SAAS,GAAG;IACRxU,QAAQ,EAAE,KAAK;IACfI,IAAI,EAAE;EACV,CAAC;EACDwC,WAAWA,CAACuR,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACM,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,eAAe,CAACO,WAAW,CAAC,CAAC;EACtC;EACA;AACJ;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,MAAMG,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAAC1J,mBAAmB,CAAC,CAAC,CAACoK,IAAI,CAACnb,GAAG,CAACa,KAAK,IAAI;MACtF,IAAI,CAACkT,UAAU,CAAC,CAAC;MACjB,IAAI,CAACqH,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,CAAChH,MAAM,CAAC,CAAC;MACb,IAAI,CAACoG,eAAe,CAACvG,WAAW,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IACH;IACA;IACA,MAAMoH,gBAAgB,GAAG,IAAI,CAACb,eAAe,CAACxJ,eAAe,CAAC,CAAC,CAACkK,IAAI,CAAClb,MAAM,CAACiZ,IAAI,IAAIA,IAAI,CAAC/F,QAAQ,CAACC,IAAI,KAAK,UAAU,CAAC,EAAEpT,GAAG,CAACkZ,IAAI,IAAI;MAChI,IAAI,CAAC7E,MAAM,CAAC,CAAC;MACb;MACA;MACA;MACA;MACA;MACA;IACJ,CAAC,CAAC,CAAC;IACH,MAAMkH,kBAAkB,GAAG,IAAI,CAACd,eAAe,CAAClJ,iBAAiB,CAAC,CAAC,CAAC4J,IAAI,CAACnb,GAAG,CAAC,MAAM;MAC/E,IAAI,CAACob,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,CAAChH,MAAM,CAAC,CAAC;MACb,IAAI,CAACoG,eAAe,CAACvG,WAAW,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IACH,MAAMsH,SAAS,GAAG5b,KAAK,CAACsb,oBAAoB,EAAEI,gBAAgB,EAAEC,kBAAkB,CAAC;IACnF,IAAI,CAACb,eAAe,GAAGc,SAAS,CAACC,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EACzD;EACA;AACJ;AACA;EACI1H,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC8G,QAAQ,CAACvU,QAAQ,GAAG,IAAI;IAC7B,IAAI,CAACuU,QAAQ,CAACnV,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACkU,eAAe,CAACzO,QAAQ,CAAC/D,OAAO,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC4S,QAAQ,CAACjV,IAAI,CAACW,QAAQ,GAAG,IAAI,CAACkU,eAAe,CAACzO,QAAQ,CAAC/D,OAAO,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC6S,SAAS,CAACxU,QAAQ,GAAG,IAAI;IAC9B,IAAI,CAACmU,eAAe,CAACpU,OAAO,GAAG,IAAI,CAACwU,QAAQ;IAC5C,IAAI,CAACJ,eAAe,CAACjU,QAAQ,GAAG,IAAI,CAACsU,SAAS;EAClD;EACA;AACJ;AACA;EACIM,eAAeA,CAAA,EAAG;IACd,IAAIzM,CAAC,EAAE+M,CAAC,EAAEC,CAAC;IACX,MAAMC,KAAK,GAAG,IAAI,CAACnB,eAAe,CAAC7L,MAAM,CAAC,CAAC,CAACJ,MAAM,GAAG,CAAC;MAAEqN,KAAK,GAAGD,KAAK,GAAG,IAAI,CAACnB,eAAe,CAAC7T,KAAK,CAAC,CAAC,CAAC4H,MAAM;MAAEwB,OAAO,GAAG,IAAI,CAACyK,eAAe,CAACzK,OAAO,CAAC,IAAI,CAAC;MAAE8L,KAAK,GAAG,EAAE;MAAE9P,QAAQ,GAAG,IAAI,CAACyO,eAAe,CAACzO,QAAQ;IAC/M,IAAI+C,IAAI,GAAG/C,QAAQ,CAACjF,MAAM,IAAIiF,QAAQ,CAACxE,SAAS,IAAIwE,QAAQ,CAACxF,QAAQ,GAC/D,CAAC,GAAGiI,IAAI,CAACsN,KAAK,CAACpI,MAAM,CAAC3H,QAAQ,CAAC5D,QAAQ,CAAC,CAAC,IAAIqG,IAAI,CAACsN,KAAK,CAAC/P,QAAQ,CAACpF,KAAK,CAAC;IAC7EmI,IAAI,GAAG,CAACA,IAAI;IACZ,IAAI/C,QAAQ,CAAC7D,OAAO,KAAK,MAAM,EAAE;MAC7B6D,QAAQ,CAAC7D,OAAO,GAAGsG,IAAI,CAACC,GAAG,CAAC,CAAC1C,QAAQ,CAAC7D,OAAO,EAAE6D,QAAQ,CAACpF,KAAK,CAAC;IAClE;IACA,IAAIoF,QAAQ,CAACtF,IAAI,IAAIsF,QAAQ,CAAC7D,OAAO,KAAK,MAAM,EAAE;MAC9C,KAAKwG,CAAC,GAAGiN,KAAK,EAAEF,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEhN,CAAC,GAAGkN,KAAK,EAAElN,CAAC,EAAE,EAAE;QAC1C,IAAI+M,CAAC,IAAI3M,IAAI,IAAI2M,CAAC,KAAK,CAAC,EAAE;UACtBI,KAAK,CAAC1M,IAAI,CAAC;YACP0G,KAAK,EAAErH,IAAI,CAACC,GAAG,CAACsB,OAAO,EAAErB,CAAC,GAAGiN,KAAK,CAAC;YACnCxL,GAAG,EAAEzB,CAAC,GAAGiN,KAAK,GAAG7M,IAAI,GAAG;UAC5B,CAAC,CAAC;UACF,IAAIN,IAAI,CAACC,GAAG,CAACsB,OAAO,EAAErB,CAAC,GAAGiN,KAAK,CAAC,KAAK5L,OAAO,EAAE;YAC1C;UACJ;UACA0L,CAAC,GAAG,CAAC,EAAE,EAAEC,CAAC;QACd;QACAD,CAAC,IAAI,IAAI,CAACjB,eAAe,CAAC7G,OAAO,CAAC,IAAI,CAAC6G,eAAe,CAACzM,QAAQ,CAACW,CAAC,CAAC,CAAC;MACvE;IACJ;IACA,IAAI,CAACiM,MAAM,GAAGkB,KAAK;EACvB;EACA;AACJ;AACA;AACA;EACIT,IAAIA,CAAA,EAAG;IACH,IAAIzF,UAAU;IACd,MAAM5J,QAAQ,GAAG,IAAI,CAACyO,eAAe,CAACzO,QAAQ;MAAEpF,KAAK,GAAG,IAAI,CAAC6T,eAAe,CAAC7T,KAAK,CAAC,CAAC;MAAEN,QAAQ,GAAGM,KAAK,CAAC4H,MAAM,IAAIxC,QAAQ,CAACpF,KAAK;IAC/H,IAAI,CAACiU,QAAQ,CAACvU,QAAQ,GAAG,CAAC0F,QAAQ,CAAChE,GAAG,IAAI1B,QAAQ;IAClD,IAAI,CAACwU,SAAS,CAACxU,QAAQ,GAAG,CAAC0F,QAAQ,CAACtF,IAAI,IAAIJ,QAAQ;IACpD,IAAI0F,QAAQ,CAACtF,IAAI,EAAE;MACfkP,UAAU,GAAG,IAAI,CAACgF,MAAM,CAACpM,MAAM,GAAG,IAAI,CAACsM,SAAS,CAACpU,IAAI,CAAC8H,MAAM;MAC5D,IAAIxC,QAAQ,CAACxF,QAAQ,IAAIoP,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAACkF,SAAS,CAACpU,IAAI,GAAG,EAAE;QACxBE,KAAK,CAACuH,OAAO,CAACwC,IAAI,IAAI;UAClB,IAAI,CAACmK,SAAS,CAACpU,IAAI,CAAC0I,IAAI,CAAC;YACrBjK,MAAM,EAAE,KAAK;YACb/D,EAAE,EAAE,OAAOuP,IAAI,CAACvP,EAAE,EAAE;YACpBiE,YAAY,EAAEsL,IAAI,CAACqL,UAAU,CAAC,CAAC;YAC/B5W,gBAAgB,EAAE;UACtB,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI,IAAIwQ,UAAU,GAAG,CAAC,EAAE;QACrB,MAAMqG,MAAM,GAAG,IAAI,CAACnB,SAAS,CAACpU,IAAI,CAAC8H,MAAM,GAAG,CAAC,GAAG,IAAI,CAACsM,SAAS,CAACpU,IAAI,CAAC8H,MAAM,GAAG,CAAC;QAC9E,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiH,UAAU,EAAEjH,CAAC,EAAE,EAAE;UACjC,IAAI,CAACmM,SAAS,CAACpU,IAAI,CAAC0I,IAAI,CAAC;YACrBjK,MAAM,EAAE,KAAK;YACb/D,EAAE,EAAE,OAAOuN,CAAC,GAAGsN,MAAM,EAAE;YACvB5W,YAAY,EAAE,EAAE;YAChBD,gBAAgB,EAAE;UACtB,CAAC,CAAC;QACN;MACJ,CAAC,MACI,IAAIwQ,UAAU,GAAG,CAAC,EAAE;QACrB,IAAI,CAACkF,SAAS,CAACpU,IAAI,CAACwV,MAAM,CAACtG,UAAU,EAAEnH,IAAI,CAACoB,GAAG,CAAC+F,UAAU,CAAC,CAAC;MAChE;IACJ;IACA,IAAI,CAAC6E,eAAe,CAACpU,OAAO,GAAG,IAAI,CAACwU,QAAQ;IAC5C,IAAI,CAACJ,eAAe,CAACjU,QAAQ,GAAG,IAAI,CAACsU,SAAS;EAClD;EAEA;AACJ;AACA;EACIzG,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC8H,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACA;AACJ;AACA;EACID,iBAAiBA,CAAA,EAAG;IAChB,MAAMnQ,QAAQ,GAAG,IAAI,CAACyO,eAAe,CAACzO,QAAQ;MAAElF,IAAI,GAAGkF,QAAQ,CAAClF,IAAI,IAAIkF,QAAQ,CAAChF,MAAM;MAAElF,KAAK,GAAG,IAAI,CAAC2Y,eAAe,CAACzM,QAAQ,CAAC,IAAI,CAACyM,eAAe,CAAC/P,OAAO,CAAC,CAAC,CAAC;IAC9J,IAAIsB,QAAQ,CAAChE,GAAG,EAAE;MACd,IAAI,CAAC6S,QAAQ,CAACnV,IAAI,CAACY,QAAQ,GAAG,CAACQ,IAAI,IAAIhF,KAAK,IAAI,IAAI,CAAC2Y,eAAe,CAAC1K,OAAO,CAAC,IAAI,CAAC;MAClF,IAAI,CAAC8K,QAAQ,CAACjV,IAAI,CAACU,QAAQ,GAAG,CAACQ,IAAI,IAAIhF,KAAK,IAAI,IAAI,CAAC2Y,eAAe,CAACzK,OAAO,CAAC,IAAI,CAAC;IACtF;IACA,IAAI,CAACyK,eAAe,CAACpU,OAAO,GAAG,IAAI,CAACwU,QAAQ;EAChD;EACA;AACJ;AACA;EACIuB,WAAWA,CAAA,EAAG;IACV,IAAIC,aAAa;IACjB,IAAI,CAAC,IAAI,CAAC5B,eAAe,CAACzO,QAAQ,CAACtF,IAAI,EAAE;MACrC;IACJ;IACA,IAAI,CAACoU,SAAS,CAACpU,IAAI,CAACyH,OAAO,CAACwC,IAAI,IAAI;MAChC,IAAIA,IAAI,CAACxL,MAAM,KAAK,IAAI,EAAE;QACtBwL,IAAI,CAACxL,MAAM,GAAG,KAAK;MACvB;IACJ,CAAC,CAAC;IACFkX,aAAa,GAAG,IAAI,CAACvP,QAAQ,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACgO,SAAS,CAACpU,IAAI,CAAC8H,MAAM,EAAE;MAC5B,IAAI,CAACsM,SAAS,CAACpU,IAAI,CAAC2V,aAAa,CAAC,CAAClX,MAAM,GAAG,IAAI;IACpD;IACA,IAAI,CAACsV,eAAe,CAACjU,QAAQ,GAAG,IAAI,CAACsU,SAAS;EAClD;EACA;AACJ;AACA;AACA;EACIhO,QAAQA,CAAA,EAAG;IACP,MAAMpC,OAAO,GAAG,IAAI,CAAC+P,eAAe,CAACzM,QAAQ,CAAC,IAAI,CAACyM,eAAe,CAAC/P,OAAO,CAAC,CAAC,CAAC;IAC7E,IAAI4R,YAAY;IAChB,MAAMR,KAAK,GAAG,IAAI,CAAClB,MAAM,CAAC3a,MAAM,CAAC,CAACsc,IAAI,EAAEza,KAAK,KAAK;MAC9C,OAAOya,IAAI,CAACzG,KAAK,IAAIpL,OAAO,IAAI6R,IAAI,CAACnM,GAAG,IAAI1F,OAAO;IACvD,CAAC,CAAC,CAAC8R,GAAG,CAAC,CAAC;IACRF,YAAY,GAAG,IAAI,CAAC1B,MAAM,CAAC9K,SAAS,CAACyM,IAAI,IAAI;MACzC,OAAOA,IAAI,CAACzG,KAAK,KAAKgG,KAAK,CAAChG,KAAK,IAAIyG,IAAI,CAACnM,GAAG,KAAK0L,KAAK,CAAC1L,GAAG;IAC/D,CAAC,CAAC;IACF,OAAOkM,YAAY;EACvB;EAEA;AACJ;AACA;AACA;AACA;EACIG,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI9F,QAAQ,EAAEpI,MAAM;IACpB,MAAMxC,QAAQ,GAAG,IAAI,CAACyO,eAAe,CAACzO,QAAQ;IAC9C,IAAIA,QAAQ,CAAC7D,OAAO,KAAK,MAAM,EAAE;MAC7ByO,QAAQ,GAAG,IAAI,CAAC9J,QAAQ,CAAC,CAAC;MAC1B0B,MAAM,GAAG,IAAI,CAACoM,MAAM,CAACpM,MAAM;MAC3BkO,SAAS,GAAG,EAAE9F,QAAQ,GAAG,EAAEA,QAAQ;MACnCA,QAAQ,GAAG,IAAI,CAACgE,MAAM,CAAC,CAAEhE,QAAQ,GAAGpI,MAAM,GAAIA,MAAM,IAAIA,MAAM,CAAC,CAACsH,KAAK;IACzE,CAAC,MACI;MACDc,QAAQ,GAAG,IAAI,CAAC6D,eAAe,CAACzM,QAAQ,CAAC,IAAI,CAACyM,eAAe,CAAC/P,OAAO,CAAC,CAAC,CAAC;MACxE8D,MAAM,GAAG,IAAI,CAACiM,eAAe,CAAC7T,KAAK,CAAC,CAAC,CAAC4H,MAAM;MAC5CkO,SAAS,GAAG9F,QAAQ,IAAI,CAAC5K,QAAQ,CAAC7D,OAAO,GAAGyO,QAAQ,IAAI,CAAC5K,QAAQ,CAAC7D,OAAO;IAC7E;IACA,OAAOyO,QAAQ;EACnB;EAEA;AACJ;AACA;AACA;EACIhR,IAAIA,CAAC0P,KAAK,EAAE;IACR,IAAI,CAACmF,eAAe,CAAC9C,EAAE,CAAC,IAAI,CAAC8E,YAAY,CAAC,IAAI,CAAC,EAAEnH,KAAK,CAAC;EAC3D;EAEA;AACJ;AACA;AACA;EACI5P,IAAIA,CAAC4P,KAAK,EAAE;IACR,IAAI,CAACmF,eAAe,CAAC9C,EAAE,CAAC,IAAI,CAAC8E,YAAY,CAAC,KAAK,CAAC,EAAEnH,KAAK,CAAC;EAC5D;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIqC,EAAEA,CAACf,QAAQ,EAAEtB,KAAK,EAAEqH,QAAQ,EAAE;IAC1B,IAAInO,MAAM;IACV,IAAI,CAACmO,QAAQ,IAAI,IAAI,CAAC/B,MAAM,CAACpM,MAAM,EAAE;MACjCA,MAAM,GAAG,IAAI,CAACoM,MAAM,CAACpM,MAAM;MAC3B,IAAI,CAACiM,eAAe,CAAC9C,EAAE,CAAC,IAAI,CAACiD,MAAM,CAAC,CAAEhE,QAAQ,GAAGpI,MAAM,GAAIA,MAAM,IAAIA,MAAM,CAAC,CAACsH,KAAK,EAAER,KAAK,CAAC;IAC9F,CAAC,MACI;MACD,IAAI,CAACmF,eAAe,CAAC9C,EAAE,CAACf,QAAQ,EAAEtB,KAAK,CAAC;IAC5C;EACJ;EAEA;AACJ;AACA;EACIpQ,SAASA,CAAC0X,KAAK,EAAE;IACb,MAAM9a,KAAK,GAAG,IAAI,CAACgZ,SAAS,CAACpU,IAAI,CAACoJ,SAAS,CAAC+M,GAAG,IAAID,KAAK,KAAKC,GAAG,CAACzb,EAAE,CAAC;IACpE,IAAI,CAACuW,EAAE,CAAC7V,KAAK,EAAE,IAAI,CAAC2Y,eAAe,CAACzO,QAAQ,CAAC3D,SAAS,CAAC;EAC3D;EACA;AACJ;AACA;AACA;EACIyU,WAAWA,CAAC1b,EAAE,EAAE;IACZ,MAAMwV,QAAQ,GAAG,IAAI,CAAC6D,eAAe,CAAC5V,UAAU,CAACiL,SAAS,CAAC1B,KAAK,IAAIA,KAAK,CAAChN,EAAE,KAAKA,EAAE,IAAIgN,KAAK,CAACoB,QAAQ,KAAK,KAAK,CAAC;IAChH,IAAIoH,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAAC6D,eAAe,CAAC/P,OAAO,CAAC,CAAC,EAAE;MAChE;IACJ;IACA,IAAI,CAAC+P,eAAe,CAAC9C,EAAE,CAAC,IAAI,CAAC8C,eAAe,CAACzM,QAAQ,CAAC4I,QAAQ,CAAC,EAAE,KAAK,CAAC;EAC3E;EACA,OAAO/M,IAAI,YAAAkT,0BAAAhT,CAAA;IAAA,YAAAA,CAAA,IAAwFyQ,iBAAiB,EA3wD3Bpc,EAAE,CAAA4L,QAAA,CA2wD2CkB,eAAe;EAAA;EACrJ,OAAOhB,KAAK,kBA5wD6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EA4wDYoQ,iBAAiB;IAAAnQ,OAAA,EAAjBmQ,iBAAiB,CAAA3Q;EAAA;AAC5H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA9wD6FlM,EAAE,CAAAmM,iBAAA,CA8wDJiQ,iBAAiB,EAAc,CAAC;IAC/GhQ,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEU;EAAgB,CAAC,CAAC;AAAA;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8R,MAAM,GAAG,IAAIze,cAAc,CAAC,aAAa,CAAC;AAChD;AACA;AACA;AACA,MAAM0e,SAAS,CAAC;EACZ,IAAIC,YAAYA,CAAA,EAAG;IACf,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;EACvC;AACJ;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,SAASH,SAAS,CAAC;EACrC/T,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACA;AACJ;AACA;EACI,IAAIgU,YAAYA,CAAA,EAAG;IACf,OAAOtD,MAAM;EACjB;EACA,OAAO/P,IAAI,YAAAwT,yBAAAtT,CAAA;IAAA,YAAAA,CAAA,IAAwFqT,gBAAgB;EAAA;EACnH,OAAOlT,KAAK,kBAvzD6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EAuzDYgT,gBAAgB;IAAA/S,OAAA,EAAhB+S,gBAAgB,CAAAvT;EAAA;AAC3H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAzzD6FlM,EAAE,CAAAmM,iBAAA,CAyzDJ6S,gBAAgB,EAAc,CAAC;IAC9G5S,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASgf,aAAaA,CAACC,gBAAgB,EAAEC,UAAU,EAAE;EACjD,IAAIhe,iBAAiB,CAACge,UAAU,CAAC,EAAE;IAC/B,OAAOD,gBAAgB,CAACL,YAAY;EACxC;EACA,MAAMO,GAAG,GAAG;IACRzF,UAAU,EAAEA,CAAC0F,IAAI,EAAEhH,IAAI,KAAK,CAAE,CAAC;IAC/BiH,YAAY,EAAG5E,CAAC,IAAK,CAAE;EAC3B,CAAC;EACD,OAAO0E,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMG,qBAAqB,GAAG;EAC1BC,OAAO,EAAEZ,SAAS;EAClBa,QAAQ,EAAEV;AACd,CAAC;AACD;AACA;AACA;AACA,MAAMW,cAAc,GAAG;EACnBF,OAAO,EAAEb,MAAM;EACfgB,UAAU,EAAEV,aAAa;EACzBW,IAAI,EAAE,CAAChB,SAAS,EAAEze,WAAW;AACjC,CAAC;AACD;AACA;AACA;AACA,MAAM0f,gBAAgB,GAAG,CAACN,qBAAqB,EAAEG,cAAc,CAAC;;AAEhE;AACA;AACA;AACA,MAAMI,QAAQ,GAAG,IAAI5f,cAAc,CAAC,eAAe,CAAC;AACpD;AACA;AACA;AACA,MAAM6f,WAAW,CAAC;EACd,IAAIC,cAAcA,CAAA,EAAG;IACjB,MAAM,IAAIlB,KAAK,CAAC,kBAAkB,CAAC;EACvC;AACJ;AACA;AACA;AACA;AACA,MAAMmB,kBAAkB,SAASF,WAAW,CAAC;EACzClV,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACA;AACJ;AACA;EACI,IAAImV,cAAcA,CAAA,EAAG;IACjB,OAAOE,QAAQ;EACnB;EACA,OAAO1U,IAAI,YAAA2U,2BAAAzU,CAAA;IAAA,YAAAA,CAAA,IAAwFuU,kBAAkB;EAAA;EACrH,OAAOpU,KAAK,kBA13D6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EA03DYkU,kBAAkB;IAAAjU,OAAA,EAAlBiU,kBAAkB,CAAAzU;EAAA;AAC7H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA53D6FlM,EAAE,CAAAmM,iBAAA,CA43DJ+T,kBAAkB,EAAc,CAAC;IAChH9T,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASmgB,eAAeA,CAACC,kBAAkB,EAAElB,UAAU,EAAE;EACrD,IAAIhe,iBAAiB,CAACge,UAAU,CAAC,EAAE;IAC/B,OAAOkB,kBAAkB,CAACL,cAAc;EAC5C;EACA,MAAMM,GAAG,GAAG;IACRC,MAAM,EAAE,KAAK;IACbC,eAAe,EAAE;EACrB,CAAC;EACD,OAAOF,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMG,uBAAuB,GAAG;EAC5BjB,OAAO,EAAEO,WAAW;EACpBN,QAAQ,EAAEQ;AACd,CAAC;AACD;AACA;AACA;AACA,MAAMS,gBAAgB,GAAG;EACrBlB,OAAO,EAAEM,QAAQ;EACjBH,UAAU,EAAES,eAAe;EAC3BR,IAAI,EAAE,CAACG,WAAW,EAAE5f,WAAW;AACnC,CAAC;AACD;AACA;AACA;AACA,MAAMwgB,kBAAkB,GAAG,CAACF,uBAAuB,EAAEC,gBAAgB,CAAC;AAEtE,MAAME,eAAe,CAAC;EAClBxE,eAAe;EACfyE,MAAM;EACN;AACJ;AACA;EACIC,oBAAoB;EACpB;AACJ;AACA;EACIC,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;EACIC,OAAO,GAAG,KAAK;EACf;AACJ;AACA;EACIC,4BAA4B;EAC5B;AACJ;AACA;AACA;EACIC,kBAAkB,GAAG,KAAK;EAC1B,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACD,kBAAkB;EAClC;EACA,IAAIC,iBAAiBA,CAACjW,KAAK,EAAE;IACzB,IAAI,CAACgW,kBAAkB,GAAGhW,KAAK;EACnC;EACAkW,MAAM;EACNC,MAAM;EACNxW,WAAWA,CAACuR,eAAe,EAAEgF,MAAM,EAAEC,MAAM,EAAER,MAAM,EAAE;IACjD,IAAI,CAACzE,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACyE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACO,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC3E,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmE,oBAAoB,CAAClE,WAAW,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,MAAMG,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAAC1J,mBAAmB,CAAC,CAAC,CAACoK,IAAI,CAACnb,GAAG,CAAC,MAAM;MACnF,IAAI,IAAI,CAACya,eAAe,CAACzO,QAAQ,CAAC1D,QAAQ,EAAE;QACxC,IAAI,CAACqX,IAAI,CAAC,CAAC;MACf;IACJ,CAAC,CAAC,CAAC;IACH,MAAMrE,gBAAgB,GAAG,IAAI,CAACb,eAAe,CAACxJ,eAAe,CAAC,CAAC,CAACkK,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MAC7E,IAAI,CAAC0G,uBAAuB,CAAC1G,IAAI,CAAC;IACtC,CAAC,CAAC,CAAC;IACH,MAAM2G,QAAQ,GAAG,IAAI,CAACpF,eAAe,CAACpJ,eAAe,CAAC,CAAC,CAAC8J,IAAI,CAACnb,GAAG,CAAC,MAAM;MACnE,IAAI,IAAI,CAACya,eAAe,CAACzO,QAAQ,CAAC1D,QAAQ,IAAI,CAAC,IAAI,CAACiX,kBAAkB,EAAE;QACpE,IAAI,CAACI,IAAI,CAAC,CAAC;MACf,CAAC,MACI;QACD,IAAI,CAACG,IAAI,CAAC,CAAC;MACf;IACJ,CAAC,CAAC,CAAC;IACH;IACA;IACA,MAAMC,cAAc,GAAGngB,KAAK,CAACsb,oBAAoB,EAAEI,gBAAgB,EAAEuE,QAAQ,CAAC;IAC9E,IAAI,CAACV,oBAAoB,GAAGY,cAAc,CAACtE,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACIkE,IAAIA,CAACK,OAAO,EAAE1K,KAAK,EAAE;IACjB,IAAI,IAAI,CAAC+J,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACY,oBAAoB,CAAC,IAAI,CAACxF,eAAe,CAACzO,QAAQ,CAACtD,yBAAyB,CAAC;IACtF;IACA,IAAI,IAAI,CAAC+R,eAAe,CAAChG,EAAE,CAAC,UAAU,CAAC,EAAE;MACrC;IACJ;IACA,IAAI,CAACgG,eAAe,CAACzG,KAAK,CAAC,UAAU,CAAC;IACtC,IAAI,CAACiM,oBAAoB,CAAC,CAAC;EAC/B;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACF,OAAO,EAAE1K,KAAK,EAAE;IAC5B,IAAI,IAAI,CAAC8J,QAAQ,EAAE;MACf,IAAI,CAACK,MAAM,CAAC9B,YAAY,CAAC,IAAI,CAACyB,QAAQ,CAAC;IAC3C;IACA,IAAI,CAACE,4BAA4B,GAAGU,OAAO,GAAG,IAAI,GAAG,KAAK;IAC1D,OAAO,IAAI,CAACd,MAAM,CAACiB,iBAAiB,CAAC,MAAM;MACvC,OAAO,IAAI,CAACV,MAAM,CAACzH,UAAU,CAAC,MAAM;QAChC,IAAI,CAACkH,MAAM,CAACpR,GAAG,CAAC,MAAM;UAClB,IAAI,IAAI,CAACuR,OAAO,IAAI,IAAI,CAAC5E,eAAe,CAAChG,EAAE,CAAC,MAAM,CAAC,IAAI,IAAI,CAACgG,eAAe,CAAChG,EAAE,CAAC,aAAa,CAAC,IAAI,IAAI,CAACiL,MAAM,CAACd,MAAM,EAAE;YACjH;UACJ;UACA,IAAI,CAACnE,eAAe,CAAC7U,IAAI,CAAC0P,KAAK,IAAI,IAAI,CAACmF,eAAe,CAACzO,QAAQ,CAACvD,aAAa,CAAC;QACnF,CAAC,CAAC;MACN,CAAC,EAAEuX,OAAO,IAAI,IAAI,CAACvF,eAAe,CAACzO,QAAQ,CAACzD,eAAe,CAAC;IAChE,CAAC,CAAC;EACN;EAEA;AACJ;AACA;EACI0X,oBAAoBA,CAACD,OAAO,EAAE;IAC1B,IAAI,CAACZ,QAAQ,GAAG,IAAI,CAACc,eAAe,CAACF,OAAO,CAAC;EACjD;EAEA;AACJ;AACA;EACIF,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACrF,eAAe,CAAChG,EAAE,CAAC,UAAU,CAAC,EAAE;MACtC;IACJ;IACA,IAAI,CAAC4K,OAAO,GAAG,IAAI;IACnB,IAAI,CAACI,MAAM,CAAC9B,YAAY,CAAC,IAAI,CAACyB,QAAQ,CAAC;IACvC,IAAI,CAAC3E,eAAe,CAACtG,KAAK,CAAC,UAAU,CAAC;EAC1C;EAEA;AACJ;AACA;EACIiM,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAAC3F,eAAe,CAAChG,EAAE,CAAC,UAAU,CAAC,EAAE;MACtC;IACJ;IACA,IAAI,CAAC4K,OAAO,GAAG,IAAI;EACvB;EAEA;AACJ;AACA;AACA;EACIO,uBAAuBA,CAAC1G,IAAI,EAAE;IAC1B,IAAIA,IAAI,CAAC/F,QAAQ,CAACC,IAAI,KAAK,UAAU,EAAE;MACnC,IAAI,IAAI,CAACqH,eAAe,CAACzO,QAAQ,CAAC1D,QAAQ,EAAE;QACxC,IAAI,CAACqX,IAAI,CAAC,CAAC;MACf,CAAC,MACI;QACD,IAAI,CAACG,IAAI,CAAC,CAAC;MACf;IACJ,CAAC,MACI,IAAI5G,IAAI,CAAC/F,QAAQ,CAACC,IAAI,KAAK,UAAU,EAAE;MACxC;MACA,IAAI,IAAI,CAACqH,eAAe,CAACzO,QAAQ,CAAC1D,QAAQ,EAAE;QACxC,IAAI,CAAC2X,oBAAoB,CAAC,CAAC;MAC/B;IACJ;EACJ;EACA;AACJ;AACA;EACII,oBAAoBA,CAAA,EAAG;IACnBxgB,EAAE,CAAC,YAAY,CAAC,CAACsb,IAAI,CAACjb,SAAS,CAACgZ,IAAI,IAAI,IAAI,CAACuB,eAAe,CAACtJ,kBAAkB,CAAC,CAAC,CAAC,EAAEhR,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC,MAAM,IAAI,CAACqf,4BAA4B,CAAC,EAAEtf,GAAG,CAAC,MAAM,IAAI,CAACigB,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAACxE,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAC9M;EACA;AACJ;AACA;EACI6E,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC7F,eAAe,CAACzO,QAAQ,CAACxD,kBAAkB,IAAI,IAAI,CAACiS,eAAe,CAAChG,EAAE,CAAC,UAAU,CAAC,EAAE;MACzF,IAAI,CAAC2L,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;EACIG,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC9F,eAAe,CAACzO,QAAQ,CAACxD,kBAAkB,IAAI,IAAI,CAACiS,eAAe,CAAChG,EAAE,CAAC,UAAU,CAAC,EAAE;MACzF,IAAI,CAACkL,IAAI,CAAC,CAAC;MACX,IAAI,CAACU,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;EACIG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC/F,eAAe,CAACzO,QAAQ,CAACxD,kBAAkB,IAAI,IAAI,CAACiS,eAAe,CAAChG,EAAE,CAAC,UAAU,CAAC,EAAE;MACzF,IAAI,CAACkL,IAAI,CAAC,CAAC;MACX,IAAI,CAACU,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA,OAAOxW,IAAI,YAAA4W,wBAAA1W,CAAA;IAAA,YAAAA,CAAA,IAAwFkV,eAAe,EA/lEzB7gB,EAAE,CAAA4L,QAAA,CA+lEyCkB,eAAe,GA/lE1D9M,EAAE,CAAA4L,QAAA,CA+lEqEgT,MAAM,GA/lE7E5e,EAAE,CAAA4L,QAAA,CA+lEwFmU,QAAQ,GA/lElG/f,EAAE,CAAA4L,QAAA,CA+lE6G5L,EAAE,CAACsiB,MAAM;EAAA;EACjN,OAAOxW,KAAK,kBAhmE6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EAgmEY6U,eAAe;IAAA5U,OAAA,EAAf4U,eAAe,CAAApV;EAAA;AAC1H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAlmE6FlM,EAAE,CAAAmM,iBAAA,CAkmEJ0U,eAAe,EAAc,CAAC;IAC7GzU,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEU;EAAgB,CAAC,EAAE;IAAEV,IAAI,EAAE+L,SAAS;IAAEoK,UAAU,EAAE,CAAC;MAC1EnW,IAAI,EAAE/L,MAAM;MACZmiB,IAAI,EAAE,CAAC5D,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAExS,IAAI,EAAE+L,SAAS;IAAEoK,UAAU,EAAE,CAAC;MAClCnW,IAAI,EAAE/L,MAAM;MACZmiB,IAAI,EAAE,CAACzC,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE3T,IAAI,EAAEpM,EAAE,CAACsiB;EAAO,CAAC,CAAC;AAAA;AAE1C,MAAMG,eAAe,CAAC;EAClBpG,eAAe;EACf;AACJ;AACA;EACIqG,oBAAoB;EACpB5X,WAAWA,CAACuR,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACM,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8F,oBAAoB,CAAC7F,WAAW,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,MAAMG,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAAC1J,mBAAmB,CAAC,CAAC,CAACoK,IAAI,CAACnb,GAAG,CAAC,MAAM;MACnF,MAAM+gB,UAAU,GAAG,IAAI,CAACtG,eAAe,CAACzO,QAAQ,IAAI,CAAC,IAAI,CAACyO,eAAe,CAACzO,QAAQ,CAACrD,QAAQ;MAC3F,IAAI,CAAC8R,eAAe,CAAC5V,UAAU,CAACsJ,OAAO,CAACwC,IAAI,IAAIA,IAAI,CAAC3M,IAAI,GAAG+c,UAAU,GAAG,IAAI,GAAG,KAAK,CAAC;IAC1F,CAAC,CAAC,CAAC;IACH,MAAMC,eAAe,GAAG,IAAI,CAACvG,eAAe,CAACzJ,cAAc,CAAC,CAAC;IAC7D,MAAMiQ,gBAAgB,GAAG,IAAI,CAACxG,eAAe,CAACpJ,eAAe,CAAC,CAAC;IAC/D,MAAM6P,cAAc,GAAGthB,KAAK,CAACsb,oBAAoB,EAAE8F,eAAe,EAAEC,gBAAgB,CAAC,CAAC9F,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI,IAAI,CAACiI,qBAAqB,CAACjI,IAAI,CAAC,CAAC,CAAC;IACzI,IAAI,CAAC4H,oBAAoB,GAAGI,cAAc,CAACzF,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EACnE;EACA0F,qBAAqBA,CAACjI,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAACuB,eAAe,CAACzO,QAAQ,IAAI,CAAC,IAAI,CAACyO,eAAe,CAACzO,QAAQ,CAACrD,QAAQ,EAAE;MAC3E;IACJ;IACA,IAAKuQ,IAAI,CAAC/F,QAAQ,IAAI+F,IAAI,CAAC/F,QAAQ,CAACC,IAAI,KAAK,UAAU,IAAK8F,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtG,MAAMlN,QAAQ,GAAG,IAAI,CAACyO,eAAe,CAACzO,QAAQ;QAAE4C,MAAM,GAAG,IAAI,CAAC6L,eAAe,CAAC7L,MAAM,CAAC,CAAC,CAACJ,MAAM;MAC7F,IAAI+B,CAAC,GAAIvE,QAAQ,CAACjF,MAAM,IAAI0H,IAAI,CAACO,IAAI,CAAChD,QAAQ,CAACpF,KAAK,GAAG,CAAC,CAAC,IAAIoF,QAAQ,CAACpF,KAAM;QAAE+H,CAAC,GAAK3C,QAAQ,CAACjF,MAAM,IAAIwJ,CAAC,GAAG,CAAC,CAAC,IAAK,CAAE;QAAEqG,QAAQ,GAAG,CAACsC,IAAI,CAAC/F,QAAQ,IAAI+F,IAAI,CAAC/F,QAAQ,CAAC5J,KAAK,KAAKgN,SAAS,GAAG2C,IAAI,CAAC/F,QAAQ,CAAC5J,KAAK,GAAG,IAAI,CAACkR,eAAe,CAAC/P,OAAO,CAAC,CAAC,IAAIiE,CAAC;MAChP;MACA;MACA,IAAI3C,QAAQ,CAACpD,aAAa,GAAG,CAAC,EAAE;QAC5B2H,CAAC,IAAIvE,QAAQ,CAACpD,aAAa;QAC3B;QACA,IAAIoD,QAAQ,CAAClF,IAAI,EAAE;UACf8P,QAAQ,IAAI5K,QAAQ,CAACpD,aAAa;UAClC2H,CAAC,EAAE;QACP;MACJ;MACA,OAAO5B,CAAC,EAAE,GAAG4B,CAAC,EAAE;QACZ,IAAI,CAAC6Q,KAAK,CAACxS,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6L,eAAe,CAACzM,QAAQ,CAAC4I,QAAQ,CAAC,CAAC;QAChE,IAAIhI,MAAM,EAAE;UACR,IAAI,CAAC6L,eAAe,CAAC7L,MAAM,CAAC,IAAI,CAAC6L,eAAe,CAACzM,QAAQ,CAAC4I,QAAQ,CAAC,CAAC,CAACzI,OAAO,CAAC5E,KAAK,IAAI,IAAI,CAAC6X,KAAK,CAAC7X,KAAK,CAAC,CAAC;QAC5G;QACAqN,QAAQ,EAAE;MACd;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIwK,KAAKA,CAACxK,QAAQ,EAAE;IACZ,IAAI,IAAI,CAAC6D,eAAe,CAAC5V,UAAU,CAAC+R,QAAQ,CAAC,CAAC5S,IAAI,EAAE;MAChD;IACJ;IACA,IAAI,CAACyW,eAAe,CAAC5V,UAAU,CAAC+R,QAAQ,CAAC,CAAC5S,IAAI,GAAG,IAAI;EACzD;EACA,OAAO6F,IAAI,YAAAwX,wBAAAtX,CAAA;IAAA,YAAAA,CAAA,IAAwF8W,eAAe,EA1qEzBziB,EAAE,CAAA4L,QAAA,CA0qEyCkB,eAAe;EAAA;EACnJ,OAAOhB,KAAK,kBA3qE6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EA2qEYyW,eAAe;IAAAxW,OAAA,EAAfwW,eAAe,CAAAhX;EAAA;AAC1H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA7qE6FlM,EAAE,CAAAmM,iBAAA,CA6qEJsW,eAAe,EAAc,CAAC;IAC7GrW,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEU;EAAgB,CAAC,CAAC;AAAA;AAE7D,MAAMoW,cAAc,CAAC;EACjB7G,eAAe;EACf;AACJ;AACA;EACI8G,mBAAmB;EACnB;AACJ;AACA;EACIC,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;EACI7R,QAAQ,GAAG4G,SAAS;EACpB;AACJ;AACA;EACI3Q,IAAI,GAAG2Q,SAAS;EAChBrN,WAAWA,CAACuR,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACM,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuG,mBAAmB,CAACtG,WAAW,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,MAAMiG,eAAe,GAAG,IAAI,CAACvG,eAAe,CAACzJ,cAAc,CAAC,CAAC,CAACmK,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MAC3E,IAAIA,IAAI,CAAC/F,QAAQ,CAACC,IAAI,KAAK,UAAU,EAAE;QACnC,IAAI,CAACzD,QAAQ,GAAG,IAAI,CAAC8K,eAAe,CAAC/P,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC9E,IAAI,GAAGsT,IAAI,CAAC/F,QAAQ,CAAC5J,KAAK;MACnC;IACJ,CAAC,CAAC,CAAC;IACH,MAAMkY,aAAa,GAAG,IAAI,CAAChH,eAAe,CAACjJ,YAAY,CAAC,CAAC;IACzD,MAAMkQ,gBAAgB,GAAG,IAAI,CAACjH,eAAe,CAAChJ,eAAe,CAAC,CAAC;IAC/D,MAAMkQ,mBAAmB,GAAG,IAAI,CAAClH,eAAe,CAACtJ,kBAAkB,CAAC,CAAC;IACrE,MAAMyQ,oBAAoB,GAAGhiB,KAAK,CAAC6hB,aAAa,EAAEC,gBAAgB,EAAEC,mBAAmB,CAAC,CAACxG,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI,IAAI,CAACsI,QAAQ,GAAGtI,IAAI,KAAK,YAAY,CAAC,CAAC;IACjJ,MAAM2I,kBAAkB,GAAG,IAAI,CAACpH,eAAe,CAACvJ,iBAAiB,CAAC,CAAC,CAACiK,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MACjF,IAAI,IAAI,CAACsI,QAAQ,KAAK,IAAI,CAAC/G,eAAe,CAACpN,QAAQ,CAACvE,UAAU,IAAI,IAAI,CAAC2R,eAAe,CAACpN,QAAQ,CAACtE,SAAS,CAAC,EAAE;QACxG,IAAI,CAAC+Y,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC,CAAC;IACH,MAAMC,aAAa,GAAGniB,KAAK,CAACohB,eAAe,EAAEa,kBAAkB,EAAED,oBAAoB,CAAC,CAACzG,IAAI,CAAC,CAAC;IAC7F,IAAI,CAACoG,mBAAmB,GAAGQ,aAAa,CAACtG,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EACjE;EACA;AACJ;AACA;AACA;EACIqG,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACrH,eAAe,CAACzO,QAAQ,CAACpF,KAAK,KAAK,CAAC,EAAE;MAC3C;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC6T,eAAe,CAACnF,KAAK,CAAC,CAAC,CAAC;IAC7B,IAAI1R,IAAI;IACR,MAAM+L,QAAQ,GAAG,IAAI,CAAC8K,eAAe,CAAC5V,UAAU,CAAC,IAAI,CAAC8K,QAAQ,CAAC;MAAE/J,IAAI,GAAG,IAAI,CAAC6U,eAAe,CAAC5V,UAAU,CAAC,IAAI,CAACe,IAAI,CAAC;MAAEoc,QAAQ,GAAG,IAAI,CAACvH,eAAe,CAACzO,QAAQ,CAACjD,SAAS;MAAEkZ,QAAQ,GAAG,IAAI,CAACxH,eAAe,CAACzO,QAAQ,CAAClD,UAAU;IAC3N,IAAI,IAAI,CAAC2R,eAAe,CAAC/P,OAAO,CAAC,CAAC,KAAK,IAAI,CAACiF,QAAQ,EAAE;MAClD;IACJ;IACA,IAAIsS,QAAQ,EAAE;MACVre,IAAI,GAAG,CAAC,IAAI,CAAC6W,eAAe,CAAC/K,WAAW,CAAC,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC8K,eAAe,CAAC/K,WAAW,CAAC,IAAI,CAAC9J,IAAI,CAAC;MACtG,IAAI,CAAC6U,eAAe,CAAC5V,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAAChN,EAAE,KAAKuO,QAAQ,CAACvO,EAAE,EAAE;UAC1BgN,KAAK,CAACxK,IAAI,GAAG,GAAGA,IAAI,IAAI;UACxBwK,KAAK,CAACsK,UAAU,GAAG,IAAI;UACvBtK,KAAK,CAACwK,gBAAgB,GAAG,IAAI;UAC7BxK,KAAK,CAAC0K,mBAAmB,GAAG,IAAI;QACpC;MACJ,CAAC,CAAC;IACN;IACA,IAAIkJ,QAAQ,EAAE;MACV,IAAI,CAACvH,eAAe,CAAC5V,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAAChN,EAAE,KAAKwE,IAAI,CAACxE,EAAE,EAAE;UACtBgN,KAAK,CAACsK,UAAU,GAAG,IAAI;UACvBtK,KAAK,CAACuK,eAAe,GAAG,IAAI;UAC5BvK,KAAK,CAACyK,kBAAkB,GAAG,IAAI;QACnC;MACJ,CAAC,CAAC;IACN;EACJ;EAEA;AACJ;AACA;AACA;EACIxV,KAAKA,CAACjC,EAAE,EAAE;IACN,IAAI,CAACqZ,eAAe,CAAC5V,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAAChN,EAAE,KAAKA,EAAE,EAAE;QACjBgN,KAAK,CAACxK,IAAI,GAAG,EAAE;QACfwK,KAAK,CAACsK,UAAU,GAAG,KAAK;QACxBtK,KAAK,CAACwK,gBAAgB,GAAG,KAAK;QAC9BxK,KAAK,CAAC0K,mBAAmB,GAAG,KAAK;QACjC1K,KAAK,CAACuK,eAAe,GAAG,KAAK;QAC7BvK,KAAK,CAACyK,kBAAkB,GAAG,KAAK;QAChCzK,KAAK,CAAC7K,OAAO,GAAG,IAAI,CAACkX,eAAe,CAACjG,kBAAkB,CAACpG,KAAK,CAAC;MAClE;IACJ,CAAC,CAAC;IACF,IAAI,CAACqM,eAAe,CAAC5D,eAAe,CAAC,CAAC;EAC1C;EAEA,OAAOhN,IAAI,YAAAqY,uBAAAnY,CAAA;IAAA,YAAAA,CAAA,IAAwFuX,cAAc,EA1xExBljB,EAAE,CAAA4L,QAAA,CA0xEwCkB,eAAe;EAAA;EAClJ,OAAOhB,KAAK,kBA3xE6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EA2xEYkX,cAAc;IAAAjX,OAAA,EAAdiX,cAAc,CAAAzX;EAAA;AACzH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA7xE6FlM,EAAE,CAAAmM,iBAAA,CA6xEJ+W,cAAc,EAAc,CAAC;IAC5G9W,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEU;EAAgB,CAAC,CAAC;AAAA;AAE7D,MAAMiX,iBAAiB,CAAC;EACpB1H,eAAe;EACf;AACJ;AACA;EACI2H,sBAAsB;EACtBlZ,WAAWA,CAACuR,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACM,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoH,sBAAsB,CAACnH,WAAW,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,MAAMG,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAAC1J,mBAAmB,CAAC,CAAC,CAACoK,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MACrF,IAAI,IAAI,CAACuB,eAAe,CAACzO,QAAQ,CAAChD,UAAU,EAAE;QAC1C,IAAI,CAACqL,MAAM,CAAC,CAAC;MACjB,CAAC,MACI;QACD,IAAI,CAACoG,eAAe,CAAC5V,UAAU,CAACsJ,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACvK,WAAW,GAAG,MAAM,CAAC;MAChF;IACJ,CAAC,CAAC,CAAC;IACH,MAAMyX,gBAAgB,GAAG,IAAI,CAACb,eAAe,CAACxJ,eAAe,CAAC,CAAC,CAACkK,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MAC7E,IAAI,IAAI,CAACuB,eAAe,CAACzO,QAAQ,CAAChD,UAAU,IAAIkQ,IAAI,CAAC/F,QAAQ,CAACC,IAAI,KAAK,UAAU,EAAE;QAC/E,IAAI,CAACiB,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC,CAAC;IACH,MAAMkH,kBAAkB,GAAG,IAAI,CAACd,eAAe,CAAClJ,iBAAiB,CAAC,CAAC,CAAC4J,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MACjF,IAAI,IAAI,CAACuB,eAAe,CAACzO,QAAQ,CAAChD,UAAU,EAAE;QAC1C,IAAI,CAACqL,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC,CAAC;IACH,MAAMgO,WAAW,GAAGziB,KAAK,CAACsb,oBAAoB,EAAEI,gBAAgB,EAAEC,kBAAkB,CAAC;IACrF,IAAI,CAAC6G,sBAAsB,GAAGC,WAAW,CAAC5G,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAClE;EACA;AACJ;AACA;EACIpH,MAAMA,CAAA,EAAG;IACL,MAAMzN,KAAK,GAAG,IAAI,CAAC6T,eAAe,CAACzO,QAAQ,CAACpF,KAAK;IACjD,IAAIkP,KAAK,GAAG,IAAI,CAAC2E,eAAe,CAAC/P,OAAO,CAAC,CAAC;MAAE0F,GAAG,GAAG0F,KAAK,GAAGlP,KAAK;IAC/D,IAAI,IAAI,CAAC6T,eAAe,CAACzO,QAAQ,CAACjF,MAAM,EAAE;MACtC+O,KAAK,GAAGlP,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGkP,KAAK,GAAG,CAAClP,KAAK,GAAG,CAAC,IAAI,CAAC,GAAGkP,KAAK,GAAGlP,KAAK,GAAG,CAAC;MACrEwJ,GAAG,GAAGxJ,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGkP,KAAK,GAAGlP,KAAK,GAAGkP,KAAK,GAAGlP,KAAK,GAAG,CAAC;IAC7D;IACA,IAAI,CAAC6T,eAAe,CAAC5V,UAAU,CAACsJ,OAAO,CAAC,CAACC,KAAK,EAAEO,CAAC,KAAK;MAClDP,KAAK,CAACvK,WAAW,GAAI8K,CAAC,IAAImH,KAAK,IAAInH,CAAC,GAAGyB,GAAG,GAAI,MAAM,GAAG,QAAQ;IACnE,CAAC,CAAC;EACN;EACA,OAAOvG,IAAI,YAAAyY,0BAAAvY,CAAA;IAAA,YAAAA,CAAA,IAAwFoY,iBAAiB,EAr1E3B/jB,EAAE,CAAA4L,QAAA,CAq1E2CkB,eAAe;EAAA;EACrJ,OAAOhB,KAAK,kBAt1E6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EAs1EY+X,iBAAiB;IAAA9X,OAAA,EAAjB8X,iBAAiB,CAAAtY;EAAA;AAC5H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAx1E6FlM,EAAE,CAAAmM,iBAAA,CAw1EJ4X,iBAAiB,EAAc,CAAC;IAC/G3X,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEU;EAAgB,CAAC,CAAC;AAAA;AAE7D,MAAMqX,WAAW,CAAC;EACd9H,eAAe;EACf+H,KAAK;EACLC,MAAM;EACN;AACJ;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;EACIC,mBAAmB;EACnBzZ,WAAWA,CAACuR,eAAe,EAAE+H,KAAK,EAAEC,MAAM,EAAE;IACxC,IAAI,CAAChI,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC+H,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1H,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC,IAAI,CAACyH,KAAK,EAAE;MACb,IAAI,CAACA,KAAK,GAAG;QACTI,QAAQ,EAAE/iB,EAAE,CAAC,UAAU,CAAC,CAACsb,IAAI,CAAC/a,IAAI,CAAC,CAAC,CAAC;MACzC,CAAC;IACL;IACA;IACA,IAAI,CAAC,IAAI,CAACqiB,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG;QACVI,QAAQ,EAAEA,CAACC,QAAQ,EAAEC,MAAM,KAAK;UAAE;QAAQ;MAC9C,CAAC;IACL;EACJ;EACA/H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0H,gBAAgB,CAACzH,WAAW,CAAC,CAAC;EACvC;EACA;AACJ;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,MAAMG,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAAC1J,mBAAmB,CAAC,CAAC,CAACoK,IAAI,CAACnb,GAAG,CAAC,MAAM,IAAI,CAACgjB,aAAa,CAAC,CAAC,CAAC,CAAC;IAC7G,MAAM1H,gBAAgB,GAAG,IAAI,CAACb,eAAe,CAACxJ,eAAe,CAAC,CAAC,CAACkK,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MAC7E,IAAI,IAAI,CAACuB,eAAe,CAACzO,QAAQ,CAAC/C,eAAe,IAAIiQ,IAAI,CAAC/F,QAAQ,CAACC,IAAI,KAAK,UAAU,EAAE;QACpF,MAAM6P,WAAW,GAAG,IAAI,CAACxI,eAAe,CAAC/P,OAAO,CAAC,CAAC;QAClD,MAAMwY,cAAc,GAAG,IAAI,CAACzI,eAAe,CAAC5V,UAAU,CAACoe,WAAW,CAAC,CAAC1K,YAAY;QAChF,IAAI,CAAC2K,cAAc,IAAIA,cAAc,KAAK,IAAI,CAACP,mBAAmB,EAAE;UAChE;QACJ;QACA,IAAI,CAACF,MAAM,CAACI,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE;UAAED,QAAQ,EAAEM,cAAc;UAAEC,UAAU,EAAE,IAAI,CAACX;QAAM,CAAC,CAAC;MACtF;IACJ,CAAC,CAAC,CAAC;IACH,MAAMY,aAAa,GAAGxjB,KAAK,CAACsb,oBAAoB,EAAEI,gBAAgB,CAAC;IACnE,IAAI,CAACoH,gBAAgB,GAAGU,aAAa,CAAC3H,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACIzU,MAAMA,CAAC4b,QAAQ,EAAE;IACb,MAAMhM,QAAQ,GAAG,IAAI,CAAC6D,eAAe,CAAC5V,UAAU,CAACiL,SAAS,CAAC1B,KAAK,IAAIA,KAAK,CAACmK,YAAY,KAAKqK,QAAQ,IAAIxU,KAAK,CAACoB,QAAQ,KAAK,KAAK,CAAC;IAChI,IAAIoH,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAAC6D,eAAe,CAAC/P,OAAO,CAAC,CAAC,EAAE;MAChE;IACJ;IACA,IAAI,CAAC+P,eAAe,CAAC9C,EAAE,CAAC,IAAI,CAAC8C,eAAe,CAACzM,QAAQ,CAAC4I,QAAQ,CAAC,EAAE,KAAK,CAAC;EAC3E;EACA;AACJ;AACA;EACIoM,aAAaA,CAAA,EAAG;IACZ,MAAMK,KAAK,GAAG,IAAI,CAAC5I,eAAe,CAACzO,QAAQ,CAACvE,aAAa,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;IAC/E,IAAI,CAAC+a,KAAK,CAACI,QAAQ,CAACzH,IAAI,CAAC9a,IAAI,CAACgjB,KAAK,CAAC,CAAC,CAChC5H,SAAS,CAACmH,QAAQ,IAAI;MACvB,IAAI,CAACD,mBAAmB,GAAGC,QAAQ;MACnC,IAAI,CAAC5b,MAAM,CAAC4b,QAAQ,CAAC;IACzB,CAAC,CAAC;EACN;EACA,OAAO/Y,IAAI,YAAAyZ,oBAAAvZ,CAAA;IAAA,YAAAA,CAAA,IAAwFwY,WAAW,EAp6ErBnkB,EAAE,CAAA4L,QAAA,CAo6EqCkB,eAAe,GAp6EtD9M,EAAE,CAAA4L,QAAA,CAo6EiEtJ,EAAE,CAAC6iB,cAAc,MAp6EpFnlB,EAAE,CAAA4L,QAAA,CAo6E+GtJ,EAAE,CAAC8iB,MAAM;EAAA;EACnN,OAAOtZ,KAAK,kBAr6E6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EAq6EYmY,WAAW;IAAAlY,OAAA,EAAXkY,WAAW,CAAA1Y;EAAA;AACtH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAv6E6FlM,EAAE,CAAAmM,iBAAA,CAu6EJgY,WAAW,EAAc,CAAC;IACzG/X,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAEU;EAAgB,CAAC,EAAE;IAAEV,IAAI,EAAE9J,EAAE,CAAC6iB,cAAc;IAAE5C,UAAU,EAAE,CAAC;MAClFnW,IAAI,EAAE9L;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8L,IAAI,EAAE9J,EAAE,CAAC8iB,MAAM;IAAE7C,UAAU,EAAE,CAAC;MAClCnW,IAAI,EAAE9L;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,IAAI+kB,MAAM,GAAG,CAAC;AACd,MAAMC,sBAAsB,CAAC;EACzBhhB,MAAM;EACN;AACJ;AACA;AACA;EACItB,EAAE,GAAGzC,KAAK,CAAC,aAAa8kB,MAAM,EAAE,EAAE,CAAC;EACnC;AACJ;AACA;AACA;EACI3P,SAAS,GAAGnV,KAAK,CAAC,CAAC,EAAE;IACjB2N,SAAS,EAAG4M,IAAI,IAAK;MACjB,OAAO,CAACA,IAAI,IAAI,CAAC;IACrB;EACJ,CAAC,CAAC;EACF;AACJ;AACA;EACIzV,KAAK,GAAG9E,KAAK,CAAC,CAAC,CAAC;EAChB;AACJ;AACA;EACIqd,UAAU,GAAGrd,KAAK,CAAC,EAAE,CAAC;EACtB;AACJ;AACA;EACI6Z,QAAQ,GAAG7Z,KAAK,CAAC,EAAE,CAAC;EACpBuK,WAAWA,CAACxG,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIihB,SAASA,CAACxJ,MAAM,EAAE;IACd,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,MAAM,CAAC,CAAC;EACrC;EACA,OAAOtQ,IAAI,YAAA+Z,+BAAA7Z,CAAA;IAAA,YAAAA,CAAA,IAAwF2Z,sBAAsB,EAv9EhCtlB,EAAE,CAAAylB,iBAAA,CAu9EgDzlB,EAAE,CAAC0lB,WAAW;EAAA;EACzJ,OAAOC,IAAI,kBAx9E8E3lB,EAAE,CAAA4lB,iBAAA;IAAAxZ,IAAA,EAw9EJkZ,sBAAsB;IAAAO,SAAA;IAAAC,MAAA;MAAA9iB,EAAA,GAx9EpBhD,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;MAAAtQ,SAAA,GAAF1V,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;MAAA3gB,KAAA,GAAFrF,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;MAAApI,UAAA,GAAF5d,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;MAAA5L,QAAA,GAAFpa,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;IAAA;EAAA;AAy9E/F;AACA;EAAA,QAAA9Z,SAAA,oBAAAA,SAAA,KA19E6FlM,EAAE,CAAAmM,iBAAA,CA09EJmZ,sBAAsB,EAAc,CAAC;IACpHlZ,IAAI,EAAE5L,SAAS;IACfgiB,IAAI,EAAE,CAAC;MACCyD,QAAQ,EAAE,4BAA4B;MACtCC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9Z,IAAI,EAAEpM,EAAE,CAAC0lB;EAAY,CAAC,CAAC;AAAA;AAE5D,MAAMS,aAAa,CAAC;EAChBC,iBAAiB;EACjB9E,MAAM;EACN;AACJ;AACA;AACA;EACI,IAAI+E,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,iBAAiB,CAACrJ,IAAI,CAAClb,MAAM,CAAC,MAAM,CAAC,IAAI,CAACyf,MAAM,EAAEgF,iBAAiB,CAAC,CAAC;EACrF;EACAxb,WAAWA,CAACuW,MAAM,EAAEC,MAAM,EAAElC,UAAU,EAAE;IACpC,IAAI,CAACkC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC8E,iBAAiB,GAAGhlB,iBAAiB,CAACge,UAAU,CAAC,GAChD1d,SAAS,CAAC2f,MAAM,EAAE,QAAQ,CAAC,GAC1B,IAAI9f,OAAO,CAAC,CAAC,CAAEmR,YAAY,CAAC,CAAC;EACxC;EACA,OAAOjH,IAAI,YAAA8a,sBAAA5a,CAAA;IAAA,YAAAA,CAAA,IAAwFwa,aAAa,EAl/EvBnmB,EAAE,CAAA4L,QAAA,CAk/EuCgT,MAAM,GAl/E/C5e,EAAE,CAAA4L,QAAA,CAk/E0DmU,QAAQ,GAl/EpE/f,EAAE,CAAA4L,QAAA,CAk/E+ExL,WAAW;EAAA;EACrL,OAAO0L,KAAK,kBAn/E6E9L,EAAE,CAAA+L,kBAAA;IAAAC,KAAA,EAm/EYma,aAAa;IAAAla,OAAA,EAAbka,aAAa,CAAA1a;EAAA;AACxH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAr/E6FlM,EAAE,CAAAmM,iBAAA,CAq/EJga,aAAa,EAAc,CAAC;IAC3G/Z,IAAI,EAAElM;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEkM,IAAI,EAAE+L,SAAS;IAAEoK,UAAU,EAAE,CAAC;MAC/CnW,IAAI,EAAE/L,MAAM;MACZmiB,IAAI,EAAE,CAAC5D,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAExS,IAAI,EAAE+L,SAAS;IAAEoK,UAAU,EAAE,CAAC;MAClCnW,IAAI,EAAE/L,MAAM;MACZmiB,IAAI,EAAE,CAACzC,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE3T,IAAI,EAAEiJ,MAAM;IAAEkN,UAAU,EAAE,CAAC;MAC/BnW,IAAI,EAAE/L,MAAM;MACZmiB,IAAI,EAAE,CAACpiB,WAAW;IACtB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMomB,cAAc,CAAC;EACjBC,IAAI;EACJC,EAAE;EACFC,QAAQ;EACRtK,eAAe;EACfuK,cAAc;EACd;AACJ;AACA;EACIC,YAAY,GAAGtmB,KAAK,CAAC,CAAC;EACtB;AACJ;AACA;EACIiG,SAAS,GAAGjG,KAAK,CAAC,CAAC;EACnB;AACJ;AACA;EACIkG,UAAU,GAAGlG,KAAK,CAAC,CAAC;EACpB;AACJ;AACA;EACIumB,iBAAiB;EACjB;AACJ;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;EACIC,oBAAoB;EACpB;AACJ;AACA;EACIC,oBAAoB;EACpB;AACJ;AACA;EACIC,eAAe;EACf;AACJ;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;EACIC,gBAAgB;EAChBC,YAAY;EACZ;AACJ;AACA;EACIC,KAAK,GAAG;IACJhP,IAAI,EAAE,IAAI;IACViP,MAAM,EAAE,IAAI;IACZ9P,OAAO,EAAE,IAAI;IACbX,KAAK,EAAE;MACHY,KAAK,EAAE,IAAI;MACXpL,OAAO,EAAE;IACb,CAAC;IACDyL,SAAS,EAAE,IAAI;IACfhR,MAAM,EAAE,KAAK;IACbygB,MAAM,EAAE;EACZ,CAAC;EACD;AACJ;AACA;EACIC,aAAa,GAAG,IAAIlmB,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;EACImmB,mBAAmB;EACnBljB,kBAAkB,GAAIwL,KAAK,IAAK;IAC5B,MAAM2X,QAAQ,GAAG;MAAE,GAAG3X;IAAM,CAAC;IAC7B,OAAO2X,QAAQ,CAACrjB,MAAM;IACtB,OAAOqjB,QAAQ;EACnB,CAAC;EACD7c,WAAWA,CAAC2b,IAAI,EAAEC,EAAE,EAAEC,QAAQ,EAAEtK,eAAe,EAAEuK,cAAc,EAAE;IAC7D,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACtK,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACuK,cAAc,GAAGA,cAAc;EACxC;EACAgB,WAAWA,CAAC/Q,KAAK,EAAE;IACf,IAAI,IAAI,CAACgQ,YAAY,CAAC,CAAC,EAAEvgB,eAAe,EAAE;MACtC,IAAI,CAACuhB,YAAY,CAAChR,KAAK,CAAC;IAC5B;EACJ;EACAiR,YAAYA,CAACjR,KAAK,EAAE;IAChB,IAAIA,KAAK,CAACkR,aAAa,CAAC3X,MAAM,IAAI,CAAC,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACyW,YAAY,CAAC,CAAC,EAAEtgB,eAAe,EAAE;MACtC,IAAI,CAACshB,YAAY,CAAChR,KAAK,CAAC;IAC5B;EACJ;EACAmR,aAAaA,CAACnR,KAAK,EAAE;IACjB,IAAI,CAACoR,UAAU,CAACpR,KAAK,CAAC;EAC1B;EACAqR,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACrB,YAAY,CAAC,CAAC,EAAEvgB,eAAe,EAAE;MACtC,OAAO,KAAK;IAChB;EACJ;EACA6hB,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACtB,YAAY,CAAC,CAAC,EAAEvgB,eAAe,EAAE;MACtC,OAAO,KAAK;IAChB;EACJ;EACA8hB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACV,mBAAmB,GAAG,IAAI,CAACD,aAAa,CACxC1K,IAAI,CAAChb,KAAK,CAAC,CAAC,CAAC,CACbsb,SAAS,CAAC,MAAM;MACjB,IAAI,CAACgL,YAAY,CAAC,CAAC;IACvB,CAAC,CAAC;EACN;EACAzL,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8K,mBAAmB,CAAC7K,WAAW,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;EACIyL,qBAAqB,GAAIC,EAAE,IAAK;IAC5B,IAAI,CAACC,kBAAkB,CAACD,EAAE,CAAC;EAC/B,CAAC;EACD;AACJ;AACA;EACIE,cAAc,GAAIF,EAAE,IAAK;IACrB,IAAI,CAACG,WAAW,CAACH,EAAE,CAAC;EACxB,CAAC;EACD;AACJ;AACA;EACII,aAAa,GAAIJ,EAAE,IAAK;IACpB;IACA,IAAI,CAACN,UAAU,CAACM,EAAE,CAAC;IACnB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIV,YAAYA,CAAChR,KAAK,EAAE;IAChB,IAAIA,KAAK,CAAC+R,KAAK,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,MAAM9R,KAAK,GAAG,IAAI,CAAC+R,gBAAgB,CAAChS,KAAK,CAAC;IAC1C,IAAI,CAACyQ,KAAK,CAAChP,IAAI,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtC,IAAI,CAACiP,KAAK,CAACC,MAAM,GAAG1Q,KAAK,CAAC0Q,MAAM;IAChC,IAAI,CAACD,KAAK,CAACxQ,KAAK,CAACY,KAAK,GAAGZ,KAAK;IAC9B,IAAI,CAACwQ,KAAK,CAACxQ,KAAK,CAACxK,OAAO,GAAGwK,KAAK;IAChC,IAAI,CAACwQ,KAAK,CAAC7P,OAAO,GAAG,IAAI,CAACqR,QAAQ,CAACjS,KAAK,CAAC;IACzC,IAAI,CAACqQ,eAAe,GAAG,IAAI,CAACP,QAAQ,CAACoC,MAAM,CAAC5I,QAAQ,EAAE,SAAS,EAAE,IAAI,CAACwI,aAAa,CAAC;IACpF,IAAI,CAACxB,gBAAgB,GAAG,IAAI,CAACR,QAAQ,CAACoC,MAAM,CAAC5I,QAAQ,EAAE,UAAU,EAAE,IAAI,CAACwI,aAAa,CAAC;IACtF,IAAI,CAAClC,IAAI,CAAC1E,iBAAiB,CAAC,MAAM;MAC9B,IAAI,CAACiF,oBAAoB,GAAG,IAAI,CAACL,QAAQ,CAACoC,MAAM,CAAC5I,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACmI,qBAAqB,CAAC;MACnG,IAAI,CAACrB,oBAAoB,GAAG,IAAI,CAACN,QAAQ,CAACoC,MAAM,CAAC5I,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACmI,qBAAqB,CAAC;IACvG,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIE,kBAAkBA,CAAC3R,KAAK,EAAE;IACtB,MAAMU,KAAK,GAAG,IAAI,CAACyR,WAAW,CAAC,IAAI,CAAC1B,KAAK,CAAC7P,OAAO,EAAE,IAAI,CAACqR,QAAQ,CAACjS,KAAK,CAAC,CAAC;IACxE,IAAI,IAAI,CAACwQ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC,CAAC;IACvB;IACA,IAAIhX,IAAI,CAACoB,GAAG,CAAC8F,KAAK,CAAC5K,CAAC,CAAC,GAAG,CAAC,IAAI0D,IAAI,CAACoB,GAAG,CAAC8F,KAAK,CAAC3K,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACqc,GAAG,CAAC,OAAO,CAAC,EAAE;MACrE;IACJ;IACA,IAAK5Y,IAAI,CAACoB,GAAG,CAAC8F,KAAK,CAAC5K,CAAC,CAAC,GAAG,CAAC,IAAI0D,IAAI,CAACoB,GAAG,CAAC8F,KAAK,CAAC5K,CAAC,CAAC,GAAG0D,IAAI,CAACoB,GAAG,CAAC8F,KAAK,CAAC3K,CAAC,CAAC,IAAK,IAAI,CAACqc,GAAG,CAAC,OAAO,CAAC,EAAE;MACvF;IACJ;IACA,IAAI,CAACjC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACK,KAAK,CAACE,MAAM,GAAG,IAAI;IACxB,IAAI,CAAC0B,0BAA0B,CAACrS,KAAK,CAAC;IACtC,IAAI,CAACiQ,iBAAiB,GAAG,IAAI,CAACH,QAAQ,CAACoC,MAAM,CAAC5I,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACsI,cAAc,CAAC;IACzF,IAAI,CAAC1B,iBAAiB,GAAG,IAAI,CAACJ,QAAQ,CAACoC,MAAM,CAAC5I,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACsI,cAAc,CAAC;IACzF5R,KAAK,CAACsS,cAAc,CAAC,CAAC;IACtB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC3B,aAAa,CAACjgB,IAAI,CAACqP,KAAK,CAAC;IAC9B;EACJ;EACA;AACJ;AACA;AACA;EACIqS,0BAA0BA,CAACrS,KAAK,EAAE;IAC9B,IAAI0Q,MAAM,GAAG1Q,KAAK,CAAC0Q,MAAM;IACzB,OAAOA,MAAM,IAAI,EAAEA,MAAM,YAAY8B,iBAAiB,CAAC,EAAE;MACrD9B,MAAM,GAAGA,MAAM,CAAC+B,aAAa;IACjC;IACA,IAAI/B,MAAM,YAAY8B,iBAAiB,EAAE;MACrC,IAAI,CAAChC,YAAY,GAAG,IAAI,CAACV,QAAQ,CAACoC,MAAM,CAACxB,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,CAAC;IAC1E;EACJ;EACA;AACJ;AACA;AACA;AACA;EACImB,WAAWA,CAAC7R,KAAK,EAAE;IACf,IAAIC,KAAK;IACT,MAAMyS,WAAW,GAAG,IAAI,CAAClN,eAAe,CAACjF,mBAAmB,CAACP,KAAK,EAAE,IAAI,CAACyQ,KAAK,CAAC;IAC/E,IAAIiC,WAAW,KAAK,KAAK,EAAE;MACvB;IACJ;IACAzS,KAAK,GAAGyS,WAAW;IACnB1S,KAAK,CAACsS,cAAc,CAAC,CAAC;IACtB,IAAI,CAAC7B,KAAK,CAACxQ,KAAK,CAACxK,OAAO,GAAGwK,KAAK;IAChC,IAAI,CAAC0S,QAAQ,CAAC1S,KAAK,CAACnK,CAAC,GAAG,IAAI,CAAC2a,KAAK,CAACxQ,KAAK,CAACY,KAAK,CAAC/K,CAAC,CAAC;EACrD;EAEA;AACJ;AACA;AACA;EACI6c,QAAQA,CAACjR,UAAU,EAAE;IACjB,IAAI,CAACoO,QAAQ,CAAC8C,QAAQ,CAAC,IAAI,CAAC/C,EAAE,CAACgD,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,eAAepR,UAAU,YAAY,CAAC;IAC7G,IAAI,CAACoO,QAAQ,CAAC8C,QAAQ,CAAC,IAAI,CAAC/C,EAAE,CAACgD,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC;EACjF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI1B,UAAUA,CAACpR,KAAK,EAAE;IACd,IAAI,CAACwF,eAAe,CAAChW,UAAU,CAAC4H,MAAM,GAAG,KAAK;IAC9C,IAAI,CAAC+Y,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACK,KAAK,CAACE,MAAM,EAAE;MACnB,IAAI,CAACb,QAAQ,CAAC8C,QAAQ,CAAC,IAAI,CAAC/C,EAAE,CAACgD,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE,CAAC;MAC1E,IAAI,CAAChD,QAAQ,CAAC8C,QAAQ,CAAC,IAAI,CAAC/C,EAAE,CAACgD,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,CAACtN,eAAe,CAACnF,KAAK,CAAC,EAAE,IAAI,CAACmF,eAAe,EAAEzO,QAAQ,EAAEnE,YAAY,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC4S,eAAe,CAACzO,QAAQ,CAACrE,UAAU,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;MAClN,IAAI,CAACqgB,eAAe,CAAC/S,KAAK,CAAC;MAC3B,IAAI,CAACiQ,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACO,KAAK,GAAG;MACThP,IAAI,EAAE,IAAI;MACViP,MAAM,EAAE,IAAI;MACZ9P,OAAO,EAAE,IAAI;MACbX,KAAK,EAAE;QACHY,KAAK,EAAE,IAAI;QACXpL,OAAO,EAAE;MACb,CAAC;MACDyL,SAAS,EAAE,IAAI;MACfhR,MAAM,EAAE,KAAK;MACbygB,MAAM,EAAE;IACZ,CAAC;IACD;IACA,IAAI,CAACN,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B;EAEA;AACJ;AACA;AACA;AACA;EACI0B,gBAAgBA,CAAChS,KAAK,EAAE;IACpB,OAAO,IAAI,CAACwF,eAAe,CAACzF,eAAe,CAACC,KAAK,CAAC;EACtD;EACA;AACJ;AACA;EACIgT,gBAAgB,GAAGA,CAAA,KAAM;IACrB,IAAI,CAACzC,gBAAgB,GAAG,IAAI,CAACT,QAAQ,CAACoC,MAAM,CAAC,IAAI,CAACzB,KAAK,CAACC,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,CAAC;IACrF,IAAI,CAACH,gBAAgB,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;AACA;EACIwC,eAAeA,CAAC/S,KAAK,EAAE;IACnB,IAAI,CAACwF,eAAe,CAAC1E,cAAc,CAACd,KAAK,EAAE,IAAI,CAACyQ,KAAK,EAAE,IAAI,CAACuC,gBAAgB,CAAC;EACjF;EACA;AACJ;AACA;AACA;AACA;EACIf,QAAQA,CAACjS,KAAK,EAAE;IACZ,OAAO,IAAI,CAACwF,eAAe,CAAC5E,OAAO,CAACZ,KAAK,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;EACImS,WAAWA,CAACc,MAAM,EAAE5N,MAAM,EAAE;IACxB,OAAO,IAAI,CAACG,eAAe,CAAC7E,UAAU,CAACsS,MAAM,EAAE5N,MAAM,CAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACI+M,GAAGA,CAACc,aAAa,EAAE;IACf,OAAO,IAAI,CAAC1N,eAAe,CAAChG,EAAE,CAAC0T,aAAa,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAAChV,IAAI,EAAE;IACT,IAAI,CAACqH,eAAe,CAACzG,KAAK,CAACZ,IAAI,CAAC;EACpC;EACA;AACJ;AACA;EACIqT,YAAYA,CAAA,EAAG;IACX,IAAI,CAAChM,eAAe,CAACvG,WAAW,CAAC,CAAC;EACtC;EACA;AACJ;AACA;EACI2C,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC4D,eAAe,CAAC5D,eAAe,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;EACI2Q,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC/M,eAAe,CAAClF,aAAa,CAAC,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIlS,KAAKA,CAACjC,EAAE,EAAE;IACN,IAAI,CAAC4jB,cAAc,CAAC3hB,KAAK,CAACjC,EAAE,CAAC;EACjC;EACA,OAAOyI,IAAI,YAAAwe,uBAAAte,CAAA;IAAA,YAAAA,CAAA,IAAwF6a,cAAc,EAp1FxBxmB,EAAE,CAAAylB,iBAAA,CAo1FwCzlB,EAAE,CAACsiB,MAAM,GAp1FnDtiB,EAAE,CAAAylB,iBAAA,CAo1F8DzlB,EAAE,CAACkqB,UAAU,GAp1F7ElqB,EAAE,CAAAylB,iBAAA,CAo1FwFzlB,EAAE,CAACmqB,SAAS,GAp1FtGnqB,EAAE,CAAAylB,iBAAA,CAo1FiH3Y,eAAe,GAp1FlI9M,EAAE,CAAAylB,iBAAA,CAo1F6IvC,cAAc;EAAA;EACtP,OAAOkH,IAAI,kBAr1F8EpqB,EAAE,CAAAqqB,iBAAA;IAAAje,IAAA,EAq1FJoa,cAAc;IAAAX,SAAA;IAAAyE,YAAA,WAAAC,4BAAA3mB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAr1FZ5D,EAAE,CAAA6E,UAAA,uBAAA2lB,4CAAAC,MAAA;UAAA,OAq1FJ5mB,GAAA,CAAA+jB,WAAA,CAAA6C,MAAkB,CAAC;QAAA,CAAN,CAAC,wBAAAC,6CAAAD,MAAA;UAAA,OAAd5mB,GAAA,CAAAikB,YAAA,CAAA2C,MAAmB,CAAC;QAAA,CAAP,CAAC,yBAAAE,8CAAAF,MAAA;UAAA,OAAd5mB,GAAA,CAAAmkB,aAAA,CAAAyC,MAAoB,CAAC;QAAA,CAAR,CAAC,uBAAAG,4CAAA;UAAA,OAAd/mB,GAAA,CAAAqkB,WAAA,CAAY,CAAC;QAAA,EAAC,yBAAA2C,8CAAA;UAAA,OAAdhnB,GAAA,CAAAskB,aAAA,CAAc,CAAC;QAAA,CAAF,CAAC;MAAA;IAAA;IAAArC,MAAA;MAAAe,YAAA,GAr1FZ7mB,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;MAAAxf,SAAA,GAAFxG,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;MAAAvf,UAAA,GAAFzG,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;IAAA;IAAA8E,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAtnB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5D,EAAE,CAAA4E,cAAA,SAs1FvF,CAAC,YAOH,CAAC;QA71FsF5E,EAAE,CAAA6E,UAAA,2BAAAsmB,qDAAA;UAAA,OA41FpEtnB,GAAA,CAAA4U,eAAA,CAAgB,CAAC;QAAA,EAAC;QA51FgDzY,EAAE,CAAAyH,gBAAA,IAAAhD,6BAAA,kBAAA5B,UA+2FvF,CAAC;QA/2FoF7C,EAAE,CAAAkF,YAAA,CAi3FpF,CAAC,CACH,CAAC;MAAA;MAAA,IAAAtB,EAAA;QAl3FmF5D,EAAE,CAAA0F,SAAA,CA21FmB,CAAC;QA31FtB1F,EAAE,CAAAqE,UAAA,YAAFrE,EAAE,CAAAorB,eAAA,IAAAnoB,GAAA,EAAAY,GAAA,CAAA2C,SAAA,GAAAnB,KAAA,SAAAxB,GAAA,CAAA2C,SAAA,GAAA0H,SAAA,EAAArK,GAAA,CAAA2C,SAAA,GAAA9D,UAAA,EAAAmB,GAAA,CAAA2C,SAAA,GAAA2H,QAAA,GAAAtK,GAAA,CAAA2C,SAAA,GAAA2H,QAAA,cAAAtK,GAAA,CAAA2C,SAAA,GAAA4H,QAAA,GAAAvK,GAAA,CAAA2C,SAAA,GAAA4H,QAAA,aA21FmB,CAAC;QA31FtBpO,EAAE,CAAA0F,SAAA,CA+2FvF,CAAC;QA/2FoF1F,EAAE,CAAAqI,UAAA,CA+1FvFxE,GAAA,CAAA4C,UAAA,CAAW,CAgBX,CAAC;MAAA;IAAA;IAAA4kB,YAAA,GAIsDlqB,EAAE,CAACmqB,OAAO,EAAoFnqB,EAAE,CAACoqB,gBAAgB,EAAoJpqB,EAAE,CAACqqB,OAAO;IAAAC,aAAA;IAAA3Q,IAAA;MAAA4Q,SAAA,EAA6D,CAC/XlpB,OAAO,CAAC,YAAY,EAAE,CAClBC,KAAK,CAAC,QAAQ,EAAEE,KAAK,CAAC;QAAEgpB,MAAM,EAAE;MAAE,CAAC,CAAC,CAAC,EACrClpB,KAAK,CAAC,MAAM,EAAEE,KAAK,CAAC;QAAEgpB,MAAM,EAAE;MAAI,CAAC,CAAC,CAAC,EACrCjpB,UAAU,CAAC,gBAAgB,EAAE;MACzB;MACAE,OAAO,CAAC,aAAa,CAAC,CACzB,CAAC,EACFF,UAAU,CAAC,gBAAgB,EAAE;MACzB;MACAE,OAAO,CAAC,GAAG,CAAC,CACf,CAAC,CACL,CAAC;IACL;EAAA;AACT;AACA;EAAA,QAAAsJ,SAAA,oBAAAA,SAAA,KAl4F6FlM,EAAE,CAAAmM,iBAAA,CAk4FJqa,cAAc,EAAc,CAAC;IAC5Gpa,IAAI,EAAE1L,SAAS;IACf8hB,IAAI,EAAE,CAAC;MACCyD,QAAQ,EAAE,WAAW;MACrBgF,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBW,UAAU,EAAE,CACRppB,OAAO,CAAC,YAAY,EAAE,CAClBC,KAAK,CAAC,QAAQ,EAAEE,KAAK,CAAC;QAAEgpB,MAAM,EAAE;MAAE,CAAC,CAAC,CAAC,EACrClpB,KAAK,CAAC,MAAM,EAAEE,KAAK,CAAC;QAAEgpB,MAAM,EAAE;MAAI,CAAC,CAAC,CAAC,EACrCjpB,UAAU,CAAC,gBAAgB,EAAE;MACzB;MACAE,OAAO,CAAC,aAAa,CAAC,CACzB,CAAC,EACFF,UAAU,CAAC,gBAAgB,EAAE;MACzB;MACAE,OAAO,CAAC,GAAG,CAAC,CACf,CAAC,CACL,CAAC,CACL;MACDsjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9Z,IAAI,EAAEpM,EAAE,CAACsiB;EAAO,CAAC,EAAE;IAAElW,IAAI,EAAEpM,EAAE,CAACkqB;EAAW,CAAC,EAAE;IAAE9d,IAAI,EAAEpM,EAAE,CAACmqB;EAAU,CAAC,EAAE;IAAE/d,IAAI,EAAEU;EAAgB,CAAC,EAAE;IAAEV,IAAI,EAAE8W;EAAe,CAAC,CAAC,EAAkB;IAAE0E,WAAW,EAAE,CAAC;MAC/Kxb,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAClC,CAAC,CAAC;IAAEsF,YAAY,EAAE,CAAC;MACf1b,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;IACnC,CAAC,CAAC;IAAEwF,aAAa,EAAE,CAAC;MAChB5b,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC;IACpC,CAAC,CAAC;IAAE0F,WAAW,EAAE,CAAC;MACd9b,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE2F,aAAa,EAAE,CAAC;MAChB/b,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqJ,iBAAiB,CAAC;EACpBnF,EAAE;EACFoF,aAAa;EACbzP,eAAe;EACf0P,iBAAiB;EACjBC,eAAe;EACfC,eAAe;EACfrF,cAAc;EACdsF,iBAAiB;EACjBC,WAAW;EACXpf,MAAM;EACNqf,iBAAiB;EACjB;EACA;EACA;EACA1X,MAAM;EACN2X,UAAU,GAAG1rB,MAAM,CAAC,CAAC;EACrB4O,QAAQ,GAAG5O,MAAM,CAAC,CAAC;EACnB2rB,MAAM,GAAG3rB,MAAM,CAAC,CAAC;EACjB4rB,OAAO,GAAG5rB,MAAM,CAAC,CAAC;EAClB6rB,WAAW,GAAG7rB,MAAM,CAAC,CAAC;EACtB;AACJ;AACA;EACI8rB,mBAAmB;EACnB;AACJ;AACA;EACIC,kBAAkB;EAClB;AACJ;AACA;EACIC,sBAAsB;EACtB;AACJ;AACA;AACA;AACA;EACIC,0BAA0B;EAC1B;AACJ;AACA;EACIC,WAAW,GAAGjsB,MAAM,CAAC,IAAI,CAAC;EAC1ByF,UAAU,GAAG,IAAI,CAACwmB,WAAW,CAACC,UAAU,CAAC,CAAC;EAC1C;AACJ;AACA;EACIC,UAAU,GAAGnsB,MAAM,CAAC,IAAI,CAAC;EACzB4F,SAAS,GAAG,IAAI,CAACumB,UAAU,CAACD,UAAU,CAAC,CAAC;EACxC;AACJ;AACA;EACIE,WAAW,GAAGpsB,MAAM,CAAC,EAAE,CAAC;EACxB6F,UAAU,GAAG,IAAI,CAACumB,WAAW,CAACF,UAAU,CAAC,CAAC;EAC1C;AACJ;AACA;EACIrQ,QAAQ,GAAG7b,MAAM,CAAC,IAAI,CAAC;EACvBqH,OAAO,GAAG,IAAI,CAACwU,QAAQ,CAACqQ,UAAU,CAAC,CAAC;EACpC;AACJ;AACA;EACIpQ,SAAS,GAAG9b,MAAM,CAAC,IAAI,CAAC;EACxBwH,QAAQ,GAAG,IAAI,CAACsU,SAAS,CAACoQ,UAAU,CAAC,CAAC;EACtC;AACJ;AACA;EACIG,gBAAgB;EAChB;AACJ;AACA;EACIC,eAAe,GAAGtsB,MAAM,CAAC,KAAK,CAAC;EAC/BusB,cAAc,GAAG,IAAI,CAACD,eAAe,CAACJ,UAAU,CAAC,CAAC;EAClD;AACJ;AACA;EACIvZ,OAAO,GAAGhT,KAAK,CAAC,CAAC;EACjB;AACJ;AACA;AACA;EACI6sB,SAAS,GAAG9rB,YAAY,CAAC,IAAI,CAACiS,OAAO,CAAC;EACtC;AACJ;AACA;EACI8Z,mBAAmB;EACnB;AACJ;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;EACIjgB,oBAAoB;EACpB;AACJ;AACA;EACIkgB,kBAAkB;EAClB;AACJ;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;EACIxgB,qBAAqB;EACrB;AACJ;AACA;EACIygB,eAAe;EACfpM,MAAM;EACNxW,WAAWA,CAAC4b,EAAE,EAAEoF,aAAa,EAAEzP,eAAe,EAAE0P,iBAAiB,EAAEC,eAAe,EAAEC,eAAe,EAAErF,cAAc,EAAEsF,iBAAiB,EAAEC,WAAW,EAAEpf,MAAM,EAAEqf,iBAAiB,EAAE9K,MAAM,EAAE;IACpL,IAAI,CAACoF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACoF,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzP,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC0P,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACrF,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACsF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACpf,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqf,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC9K,MAAM,GAAGA,MAAM;EACxB;EACAqM,kBAAkBA,CAACpF,EAAE,EAAE;IACnB,IAAI,CAAC,IAAI,CAAClM,eAAe,CAACzO,QAAQ,CAAC1D,QAAQ,EACvC;IACJ,QAAQ,IAAI,CAACoX,MAAM,CAACb,eAAe;MAC/B,KAAK,SAAS;QACV,CAAC,IAAI,CAACuL,eAAe,CAAC5K,iBAAiB,IAAI,IAAI,CAAC4K,eAAe,CAACzK,IAAI,CAAC,CAAC;QACtE;MACJ,KAAK,QAAQ;QACT,IAAI,CAACyK,eAAe,CAAChK,KAAK,CAAC,CAAC;QAC5B;MACJ;QACI;IACR;EACJ;EAEAoG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACzL,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC8P,mBAAmB,GAAG,IAAI,CAAC/F,EAAE,CAACgD,aAAa,CAACkE,aAAa,CAAC,eAAe,CAAC,CAACC,WAAW;EAC/F;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACpZ,MAAM,CAACvS,OAAO,CAAC,CAAC,CAACiO,MAAM,EAAE;MAC9B,IAAI,CAACiM,eAAe,CAAC7H,KAAK,CAAC,IAAI,CAACiY,mBAAmB,EAAE,IAAI,CAAC/X,MAAM,CAACvS,OAAO,CAAC,CAAC,EAAE,IAAI,CAACoR,OAAO,CAAC,CAAC,CAAC;MAC3F,IAAI,CAAC8I,eAAe,CAAC1G,UAAU,CAAC,IAAI,CAACjB,MAAM,CAACvS,OAAO,CAAC,CAAC,CAAC;MACtD,IAAI,CAAC4rB,iBAAiB,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAAChhB,MAAM,CAAC7B,GAAG,CAAC,gEAAgE,CAAC;IACrF;IACA,IAAI,CAAC0hB,0BAA0B,GAAG,IAAI,CAAClY,MAAM,CAACsZ,OAAO,CAACjR,IAAI,CAACnb,GAAG,CAAE8S,MAAM,IAAK;MACvE,IAAI,CAAC2H,eAAe,CAAC7H,KAAK,CAAC,IAAI,CAACiY,mBAAmB,EAAE/X,MAAM,CAACvS,OAAO,CAAC,CAAC,EAAE,IAAI,CAACoR,OAAO,CAAC,CAAC,CAAC;MACtF,IAAI,CAAC8I,eAAe,CAAC1G,UAAU,CAACjB,MAAM,CAACvS,OAAO,CAAC,CAAC,CAAC;MACjD,IAAI,CAACuS,MAAM,CAACvS,OAAO,CAAC,CAAC,CAACiO,MAAM,EAAE;QAC1B,IAAI,CAAC8c,eAAe,CAACjT,GAAG,CAAC,KAAK,CAAC;MACnC;MACA,IAAIvF,MAAM,CAACvS,OAAO,CAAC,CAAC,CAACiO,MAAM,IAAI,CAAC,IAAI,CAACsc,kBAAkB,EAAE;QACrD,IAAI,CAACqB,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC,CAAC,CAAC1Q,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAC5B;EACAT,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC8P,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAAC7P,WAAW,CAAC,CAAC;IACzC;IACA,IAAI,IAAI,CAAC+P,0BAA0B,EAAE;MACjC,IAAI,CAACA,0BAA0B,CAAC/P,WAAW,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAAC8P,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC9P,WAAW,CAAC,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC2Q,iBAAiB,GAAG,IAAI,CAACjR,eAAe,CAAC5J,kBAAkB,CAAC,CAAC,CAACsK,IAAI,CAACnb,GAAG,CAACkZ,IAAI,IAAI;MAChF,IAAI,CAAC+R,WAAW,CAAC5S,GAAG,CAACa,IAAI,CAACzU,UAAU,CAAC;MACrC,IAAI,CAAC0mB,UAAU,CAAC9S,GAAG,CAACa,IAAI,CAACtU,SAAS,CAAC;MACnC,IAAI,CAACwmB,WAAW,CAAC/S,GAAG,CAACa,IAAI,CAACrU,UAAU,CAAC;MACrC,IAAI,CAAC,IAAI,CAACymB,eAAe,CAAC,CAAC,EAAE;QACzB,IAAI,CAACA,eAAe,CAACjT,GAAG,CAAC,IAAI,CAAC;MAClC;MACA,IAAI,CAACwC,QAAQ,CAACxC,GAAG,CAACa,IAAI,CAAC7S,OAAO,CAAC;MAC/B,IAAI,CAACyU,SAAS,CAACzC,GAAG,CAACa,IAAI,CAAC1S,QAAQ,CAAC;MACjC,IAAI,CAACgkB,iBAAiB,CAAC6B,YAAY,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,IAAI,CAAChhB,qBAAqB,GAAG,IAAI,CAACoP,eAAe,CAAC1J,mBAAmB,CAAC,CAAC,CAACoK,IAAI,CAACnb,GAAG,CAAC,MAAM;MACnF,IAAI,CAACssB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC1B,WAAW,CAAC2B,IAAI,CAAC,IAAI,CAAClB,gBAAgB,CAAC;MAC5C;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC5f,oBAAoB,GAAG,IAAI,CAACgP,eAAe,CAACtJ,kBAAkB,CAAC,CAAC,CAACgK,IAAI,CAACnb,GAAG,CAAC,MAAM;MACjF,IAAI,CAACssB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC7B,UAAU,CAAC8B,IAAI,CAAC,IAAI,CAAClB,gBAAgB,CAAC;MAC3C;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACO,gBAAgB,GAAG,IAAI,CAACnR,eAAe,CAACzJ,cAAc,CAAC,CAAC,CAACmK,IAAI,CAACnb,GAAG,CAAC,MAAM;MACzE,IAAI,CAACssB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC5B,MAAM,CAAC6B,IAAI,CAAC,IAAI,CAAClB,gBAAgB,CAAC;MACvC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACQ,iBAAiB,GAAG,IAAI,CAACpR,eAAe,CAACzJ,cAAc,CAAC,CAAC,CAACmK,IAAI,CAACjb,SAAS,CAACqJ,KAAK,IAAI;MACnF,MAAMijB,eAAe,GAAG3sB,EAAE,CAAC0J,KAAK,CAAC,CAAC4R,IAAI,CAAClb,MAAM,CAAC,MAAMsJ,KAAK,CAAC4J,QAAQ,CAACC,IAAI,KAAK,UAAU,CAAC,EAAElT,SAAS,CAAC,MAAMH,IAAI,CAAC,IAAI,CAACqrB,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE/qB,IAAI,CAACkJ,KAAK,CAAC4J,QAAQ,CAAC5J,KAAK,CAAC,EAAEnJ,IAAI,CAAC,IAAI,CAACqa,eAAe,EAAEzO,QAAQ,EAAEpF,KAAK,IAAI,CAAC,CAAC,EAAEtG,GAAG,CAAC8N,KAAK,IAAI;QAC3N,MAAMhB,cAAc,GAAG,IAAI,CAACqN,eAAe,CAACrN,cAAc;QAC1D,MAAMhM,EAAE,GAAGgN,KAAK,CAAChN,EAAE,CAACqY,OAAO,CAACrM,cAAc,CAAC,IAAI,CAAC,GAAGgB,KAAK,CAAChN,EAAE,CAACgW,KAAK,CAAChK,cAAc,CAACoB,MAAM,CAAC,GAAGJ,KAAK,CAAChN,EAAE;QACnG,OAAO;UAAE,GAAGgN,KAAK;UAAEhN,EAAE,EAAEA,EAAE;UAAEmO,QAAQ,EAAE;QAAK,CAAC;MAC/C,CAAC,CAAC,EAAEhP,OAAO,CAAC,CAAC,EAAED,GAAG,CAACwS,MAAM,IAAI;QACzB,OAAO;UACHA,MAAM,EAAEA,MAAM;UACdrL,aAAa,EAAE,IAAI,CAACgT,eAAe,CAACzM,QAAQ,CAACzE,KAAK,CAAC4J,QAAQ,CAAC5J,KAAK;QACrE,CAAC;MACL,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO3J,KAAK,CAAC4sB,eAAe,CAAC;IACjC,CAAC,CAAC,EAAExsB,GAAG,CAAC6E,UAAU,IAAI;MAClB,IAAI,CAACynB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC3B,OAAO,CAAC4B,IAAI,CAAC1nB,UAAU,EAAEiO,MAAM,EAAEtE,MAAM,GAAG3J,UAAU,GAAG,IAAI,CAACwmB,gBAAgB,CAAC;MAClF;MACA;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACM,kBAAkB,GAAG,IAAI,CAAClR,eAAe,CAACjJ,YAAY,CAAC,CAAC,CAAC2J,IAAI,CAACnb,GAAG,CAAC,MAAM;MACzE,IAAI,CAACssB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC3e,QAAQ,CAAC4e,IAAI,CAAC;QAAE5e,QAAQ,EAAE,IAAI;QAAEuL,IAAI,EAAE,IAAI,CAACmS;MAAiB,CAAC,CAAC;IACvE,CAAC,CAAC,EAAEnrB,SAAS,CAAC,MAAM,IAAI,CAACua,eAAe,CAAChJ,eAAe,CAAC,CAAC,CAAC0J,IAAI,CAAC7a,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAACma,eAAe,CAAChG,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEvU,SAAS,CAACusB,IAAI,IAAI;MACnI,IAAIA,IAAI,EAAE;QACN,OAAO,IAAI,CAAChS,eAAe,CAACtJ,kBAAkB,CAAC,CAAC,CAACgK,IAAI,CAAChb,KAAK,CAAC,CAAC,CAAC;MAClE,CAAC,MACI;QACD,OAAON,EAAE,CAAC,eAAe,CAAC;MAC9B;IACJ,CAAC,CAAC,EAAEG,GAAG,CAAC,MAAM;MACV,IAAI,CAAC2N,QAAQ,CAAC4e,IAAI,CAAC;QAAE5e,QAAQ,EAAE,KAAK;QAAEuL,IAAI,EAAE,IAAI,CAACmS;MAAiB,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IACH,IAAI,CAACI,mBAAmB,GAAG,IAAI,CAACD,SAAS,CAACrQ,IAAI,CAAC3a,QAAQ,CAAC,CAAC,EAAER,GAAG,CAAC,CAAC,CAAC0F,IAAI,EAAEgnB,GAAG,CAAC,KAAK;MAC5E,MAAM5Z,MAAM,GAAG,IAAI,CAACA,MAAM,CAACvS,OAAO,CAAC,CAAC;MACpC,IAAImF,IAAI,EAAE;QACN,IAAI,CAAC+U,eAAe,CAAC7H,KAAK,CAAC,IAAI,CAACiY,mBAAmB,EAAE/X,MAAM,EAAE4Z,GAAG,CAAC;QACjE,IAAI,CAACjS,eAAe,CAAC1G,UAAU,CAACjB,MAAM,CAAC;MAC3C;MACA,IAAIpN,IAAI,IAAI,CAACoN,MAAM,CAACtE,MAAM,EAAE;QACxB,IAAI,CAACrD,MAAM,CAAC7B,GAAG,CAAC,8BAA8B,CAAC;QAC/C,IAAI,CAACgiB,eAAe,CAACjT,GAAG,CAAC,KAAK,CAAC;MACnC;MACA,IAAI,CAAC3S,IAAI,EAAE;QACP,IAAI,CAAC4lB,eAAe,CAACjT,GAAG,CAAC,KAAK,CAAC;MACnC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACyT,eAAe,GAAGlsB,KAAK,CAAC,IAAI,CAAC8rB,iBAAiB,EAAE,IAAI,CAACjgB,oBAAoB,EAAE,IAAI,CAACkgB,kBAAkB,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACxgB,qBAAqB,EAAE,IAAI,CAACogB,mBAAmB,CAAC;IAC7M,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACe,eAAe,CAACrQ,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAC3E;EACA;AACJ;AACA;EACI0Q,iBAAiBA,CAAA,EAAG;IAChB,IAAI1Y,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC+G,eAAe,EAAEpN,QAAQ,EAAEvF,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC0G,MAAM,EAAE;MACtE,IAAI,CAACsc,kBAAkB,GAAG,IAAI,CAACZ,aAAa,CAACzF,SAAS,CACjDtJ,IAAI,CAAClb,MAAM,CAAC,MAAM,IAAI,CAAC4qB,mBAAmB,KAAK,IAAI,CAAC/F,EAAE,CAACgD,aAAa,CAACkE,aAAa,CAAC,eAAe,CAAC,CAACC,WAAW,CAAC,EAAExrB,KAAK,CAAC,IAAI,CAACga,eAAe,CAACzO,QAAQ,CAACjE,qBAAqB,IAAI,GAAG,CAAC,CAAC,CACpL0T,SAAS,CAAC,MAAM;QACjB,IAAI,CAAChB,eAAe,CAAC3F,QAAQ,CAAC,IAAI,CAACgQ,EAAE,CAACgD,aAAa,CAACkE,aAAa,CAAC,eAAe,CAAC,CAACC,WAAW,CAAC;QAC/F,IAAI,CAACpB,mBAAmB,GAAG,IAAI,CAAC/F,EAAE,CAACgD,aAAa,CAACkE,aAAa,CAAC,eAAe,CAAC,CAACC,WAAW;MAC/F,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACIpV,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC4D,eAAe,CAAC5D,eAAe,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;EACIjR,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC0lB,eAAe,CAAC,CAAC,EACvB;IACJ,IAAI,CAACnB,iBAAiB,CAACvkB,IAAI,CAAC,IAAI,CAAC6U,eAAe,CAACzO,QAAQ,CAAC9D,QAAQ,IAAI,KAAK,CAAC;EAChF;EACA;AACJ;AACA;EACIxC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC4lB,eAAe,CAAC,CAAC,EACvB;IACJ,IAAI,CAACnB,iBAAiB,CAACzkB,IAAI,CAAC,IAAI,CAAC+U,eAAe,CAACzO,QAAQ,CAAC9D,QAAQ,IAAI,KAAK,CAAC;EAChF;EACA;AACJ;AACA;EACIhD,SAASA,CAAC0X,KAAK,EAAE;IACb,IAAI,CAAC,IAAI,CAAC0O,eAAe,CAAC,CAAC,EACvB;IACJ,IAAI,CAACnB,iBAAiB,CAACjlB,SAAS,CAAC0X,KAAK,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIjF,EAAEA,CAACvW,EAAE,EAAE;IACH;IACA,IAAI,CAAC,IAAI,CAACkqB,eAAe,CAAC,CAAC,EACvB;IACJ,IAAI,CAACnB,iBAAiB,CAACrN,WAAW,CAAC1b,EAAE,CAAC;EAC1C;EACA;AACJ;AACA;EACIkrB,oBAAoBA,CAAA,EAAG;IACnB,IAAI7kB,aAAa;IACjB,MAAM2F,cAAc,GAAG,IAAI,CAACqN,eAAe,CAACrN,cAAc;IAC1D,MAAMuf,YAAY,GAAG,IAAI,CAACvB,WAAW,CAAC,CAAC,CAClCnrB,MAAM,CAACmO,KAAK,IAAIA,KAAK,CAACmB,QAAQ,KAAK,IAAI,CAAC,CACxCjP,GAAG,CAAC8N,KAAK,IAAI;MACd,MAAMhN,EAAE,GAAGgN,KAAK,CAAChN,EAAE,CAACqY,OAAO,CAACrM,cAAc,CAAC,IAAI,CAAC,GAAGgB,KAAK,CAAChN,EAAE,CAACgW,KAAK,CAAChK,cAAc,CAACoB,MAAM,CAAC,GAAGJ,KAAK,CAAChN,EAAE;MACnG,OAAO;QACHA,EAAE,EAAEA,EAAE;QACNqC,KAAK,EAAE2K,KAAK,CAAC3K,KAAK;QAClBC,OAAO,EAAE0K,KAAK,CAAC1K,OAAO;QACtBC,OAAO,EAAEyK,KAAK,CAACzK,OAAO;QACtBoD,MAAM,EAAEqH,KAAK,CAACwC;MAClB,CAAC;IACL,CAAC,CAAC;IACFnJ,aAAa,GAAG,IAAI,CAACgT,eAAe,CAACzM,QAAQ,CAAC,IAAI,CAACyM,eAAe,CAAC/P,OAAO,CAAC,CAAC,CAAC;IAC7E,IAAI,CAAC2gB,gBAAgB,GAAG;MACpB5jB,aAAa,EAAEA,aAAa;MAC5BqL,MAAM,EAAE6Z;IACZ,CAAC;EACL;EACA;AACJ;AACA;EACIrM,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC8J,eAAe,CAAC9J,YAAY,CAAC,CAAC;EACvC;EACA;AACJ;AACA;EACIsM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxC,eAAe,CAAC7J,sBAAsB,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACIsM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzC,eAAe,CAAC5J,oBAAoB,CAAC,CAAC;EAC/C;EACAsM,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC1C,eAAe,CAAC5K,iBAAiB,GAAG,IAAI;IAC7C,IAAI,CAAC4K,eAAe,CAACtK,IAAI,CAAC,CAAC;EAC/B;EACAiN,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC3C,eAAe,CAAC5K,iBAAiB,GAAG,KAAK;IAC9C,IAAI,CAAC4K,eAAe,CAACzK,IAAI,CAAC,CAAC;EAC/B;EACA,OAAO9V,IAAI,YAAAmjB,0BAAAjjB,CAAA;IAAA,YAAAA,CAAA,IAAwFkgB,iBAAiB,EAxzG3B7rB,EAAE,CAAAylB,iBAAA,CAwzG2CzlB,EAAE,CAACkqB,UAAU,GAxzG1DlqB,EAAE,CAAAylB,iBAAA,CAwzGqEU,aAAa,GAxzGpFnmB,EAAE,CAAAylB,iBAAA,CAwzG+F3Y,eAAe,GAxzGhH9M,EAAE,CAAAylB,iBAAA,CAwzG2HrJ,iBAAiB,GAxzG9Ipc,EAAE,CAAAylB,iBAAA,CAwzGyJ5E,eAAe,GAxzG1K7gB,EAAE,CAAAylB,iBAAA,CAwzGqLhD,eAAe,GAxzGtMziB,EAAE,CAAAylB,iBAAA,CAwzGiNvC,cAAc,GAxzGjOljB,EAAE,CAAAylB,iBAAA,CAwzG4O1B,iBAAiB,GAxzG/P/jB,EAAE,CAAAylB,iBAAA,CAwzG0QtB,WAAW,GAxzGvRnkB,EAAE,CAAAylB,iBAAA,CAwzGkSza,SAAS,GAxzG7ShL,EAAE,CAAAylB,iBAAA,CAwzGwTzlB,EAAE,CAAC6uB,iBAAiB,GAxzG9U7uB,EAAE,CAAAylB,iBAAA,CAwzGyV1F,QAAQ;EAAA;EAC5b,OAAOqK,IAAI,kBAzzG8EpqB,EAAE,CAAAqqB,iBAAA;IAAAje,IAAA,EAyzGJyf,iBAAiB;IAAAhG,SAAA;IAAAiJ,cAAA,WAAAC,iCAAAnrB,EAAA,EAAAC,GAAA,EAAAmrB,QAAA;MAAA,IAAAprB,EAAA;QAzzGf5D,EAAE,CAAAivB,cAAA,CAAAD,QAAA,EAi0GrC1J,sBAAsB;MAAA;MAAA,IAAA1hB,EAAA;QAAA,IAAAsrB,EAAA;QAj0GalvB,EAAE,CAAAmvB,cAAA,CAAAD,EAAA,GAAFlvB,EAAE,CAAAovB,WAAA,QAAAvrB,GAAA,CAAA6Q,MAAA,GAAAwa,EAAA;MAAA;IAAA;IAAA5E,YAAA,WAAA+E,+BAAAzrB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5D,EAAE,CAAA6E,UAAA,8BAAAyqB,sDAAA7E,MAAA;UAAA,OAyzGJ5mB,GAAA,CAAA8pB,kBAAA,CAAAlD,MAAyB,CAAC;QAAA,UAzzGxBzqB,EAAE,CAAAuvB,iBAyzGY,CAAC;MAAA;IAAA;IAAAzJ,MAAA;MAAAvS,OAAA,GAzzGfvT,EAAE,CAAA+lB,YAAA,CAAAC,WAAA;IAAA;IAAAwJ,OAAA;MAAAnD,UAAA;MAAA9c,QAAA;MAAA+c,MAAA;MAAAC,OAAA;MAAAC,WAAA;IAAA;IAAAiD,QAAA,GAAFzvB,EAAE,CAAA0vB,kBAAA,CAyzGwa,CAC3ftT,iBAAiB,EACjByE,eAAe,EACf/T,eAAe,EACf2V,eAAe,EACfS,cAAc,EACda,iBAAiB,EACjBI,WAAW,CACd;IAAA2G,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAA0E,2BAAA/rB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAc,GAAA,GAj0GoF1E,EAAE,CAAA2E,gBAAA;QAAF3E,EAAE,CAAA4E,cAAA,eA20G/D,CAAC;QA30G4D5E,EAAE,CAAA6E,UAAA,uBAAA+qB,oDAAA;UAAF5vB,EAAE,CAAA+E,aAAA,CAAAL,GAAA;UAAA,OAAF1E,EAAE,CAAAgF,WAAA,CAw0G5EnB,GAAA,CAAAqe,YAAA,CAAa,CAAC;QAAA,EAAC,wBAAA2N,qDAAA;UAx0G2D7vB,EAAE,CAAA+E,aAAA,CAAAL,GAAA;UAAA,OAAF1E,EAAE,CAAAgF,WAAA,CAy0G3EnB,GAAA,CAAA2qB,WAAA,CAAY,CAAC;QAAA,EAAC,wBAAAsB,qDAAA;UAz0G2D9vB,EAAE,CAAA+E,aAAA,CAAAL,GAAA;UAAA,OAAF1E,EAAE,CAAAgF,WAAA,CA00G3EnB,GAAA,CAAAqe,YAAA,CAAa,CAAC;QAAA,EAAC,sBAAA6N,mDAAA;UA10G0D/vB,EAAE,CAAA+E,aAAA,CAAAL,GAAA;UAAA,OAAF1E,EAAE,CAAAgF,WAAA,CA20G7EnB,GAAA,CAAA4qB,WAAA,CAAY,CAAC;QAAA,EAAC;QA30G6DzuB,EAAE,CAAA+D,UAAA,IAAAkC,wCAAA,gBA60GnE,CAAC,IAAAkB,wCAAA,OAWM,CAAC;QAx1GyDnH,EAAE,CAAAkF,YAAA,CAu2GtF,CAAC;MAAA;MAAA,IAAAtB,EAAA;QAAA,IAAAosB,OAAA;QAv2GmFhwB,EAAE,CAAAqE,UAAA,YAAFrE,EAAE,CAAAorB,eAAA,IAAAvlB,GAAA,GAAAmqB,OAAA,GAAAnsB,GAAA,CAAAwC,UAAA,qBAAA2pB,OAAA,CAAA1mB,GAAA,GAAA0mB,OAAA,GAAAnsB,GAAA,CAAAwC,UAAA,qBAAA2pB,OAAA,CAAAjiB,QAAA,GAAAiiB,OAAA,GAAAnsB,GAAA,CAAAwC,UAAA,qBAAA2pB,OAAA,CAAAniB,YAAA,GAAAmiB,OAAA,GAAAnsB,GAAA,CAAAwC,UAAA,qBAAA2pB,OAAA,CAAA1pB,eAAA,GAAA0pB,OAAA,GAAAnsB,GAAA,CAAAwC,UAAA,qBAAA2pB,OAAA,CAAA/hB,MAAA,CAu0G5C,CAAC;QAv0GyCjO,EAAE,CAAA0F,SAAA,EAs1GzF,CAAC;QAt1GsF1F,EAAE,CAAA2F,aAAA,IAAA9B,GAAA,CAAAspB,cAAA,WAs1GzF,CAAC;QAt1GsFntB,EAAE,CAAA0F,SAAA,CAs2GzF,CAAC;QAt2GsF1F,EAAE,CAAA2F,aAAA,IAAA9B,GAAA,CAAA6Q,MAAA,CAAAvS,OAAA,GAAAiO,MAAA,SAs2GzF,CAAC;MAAA;IAAA;IAAAib,YAAA,GAEiGlqB,EAAE,CAACmqB,OAAO,EAAoF9E,cAAc;IAAAyJ,MAAA;IAAAC,eAAA;EAAA;AACpN;AACA;EAAA,QAAAhkB,SAAA,oBAAAA,SAAA,KA12G6FlM,EAAE,CAAAmM,iBAAA,CA02GJ0f,iBAAiB,EAAc,CAAC;IAC/Gzf,IAAI,EAAE1L,SAAS;IACf8hB,IAAI,EAAE,CAAC;MAAEyD,QAAQ,EAAE,gBAAgB;MAAEgF,QAAQ,EAAE;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEkF,SAAS,EAAE,CACQ/T,iBAAiB,EACjByE,eAAe,EACf/T,eAAe,EACf2V,eAAe,EACfS,cAAc,EACda,iBAAiB,EACjBI,WAAW,CACd;MAAE+L,eAAe,EAAEpvB,uBAAuB,CAACsvB,MAAM;MAAElK,UAAU,EAAE,KAAK;MAAE+J,MAAM,EAAE,CAAC,6BAA6B;IAAE,CAAC;EAC5H,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7jB,IAAI,EAAEpM,EAAE,CAACkqB;EAAW,CAAC,EAAE;IAAE9d,IAAI,EAAE+Z;EAAc,CAAC,EAAE;IAAE/Z,IAAI,EAAEU;EAAgB,CAAC,EAAE;IAAEV,IAAI,EAAEgQ;EAAkB,CAAC,EAAE;IAAEhQ,IAAI,EAAEyU;EAAgB,CAAC,EAAE;IAAEzU,IAAI,EAAEqW;EAAgB,CAAC,EAAE;IAAErW,IAAI,EAAE8W;EAAe,CAAC,EAAE;IAAE9W,IAAI,EAAE2X;EAAkB,CAAC,EAAE;IAAE3X,IAAI,EAAE+X;EAAY,CAAC,EAAE;IAAE/X,IAAI,EAAEpB;EAAU,CAAC,EAAE;IAAEoB,IAAI,EAAEpM,EAAE,CAAC6uB;EAAkB,CAAC,EAAE;IAAEziB,IAAI,EAAE+L,SAAS;IAAEoK,UAAU,EAAE,CAAC;MAClVnW,IAAI,EAAE/L,MAAM;MACZmiB,IAAI,EAAE,CAACzC,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAErL,MAAM,EAAE,CAAC;MAClCtI,IAAI,EAAEvL,eAAe;MACrB2hB,IAAI,EAAE,CAAC8C,sBAAsB;IACjC,CAAC,CAAC;IAAEqI,kBAAkB,EAAE,CAAC;MACrBvhB,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,2BAA2B,EAAE,CAAC,QAAQ,CAAC;IAClD,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6N,sBAAsB,CAAC;EACzBhM,MAAM;EACND,KAAK;EACL;EACAkM,WAAW;EACX;EACA9L,QAAQ;EACR;EACA+L,mBAAmB;EACnB;EACAC,gBAAgB;EAChB;EACAC,kBAAkB;EAClB;EACAC,UAAU;EACVC,QAAQ,GAAG,KAAK;EAChBjM,QAAQ,GAAG,EAAE;EACb;EACAkM,QAAQ;EACR9lB,WAAWA,CAACuZ,MAAM,EAAED,KAAK,EAAEyM,QAAQ,EAAElK,QAAQ,EAAED,EAAE,EAAE;IAC/C,IAAI,CAACrC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAIyM,QAAQ,IAAI,IAAI,EAAE;MAClBlK,QAAQ,CAACmK,YAAY,CAACpK,EAAE,CAACgD,aAAa,EAAE,UAAU,EAAE,GAAG,CAAC;IAC5D;EACJ;EACA,IAAIqH,aAAaA,CAACrM,QAAQ,EAAE;IACxB,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAGtQ,KAAK,CAACC,OAAO,CAACqQ,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IACnE,CAAC,MACI;MACD,IAAI,CAACA,QAAQ,GAAG,EAAE;IACtB;EACJ;EACA;AACJ;AACA;EACI,IAAIsM,mBAAmBA,CAAC7lB,KAAK,EAAE;IAC3B,IAAIlL,SAAS,CAAC,CAAC,IAAIoL,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;MACxCH,OAAO,CAACG,IAAI,CAAC,sEAAsE,CAAC;IACxF;IACA,IAAI,CAAColB,QAAQ,GAAGzlB,KAAK;EACzB;EACA8lB,OAAOA,CAAA,EAAG;IACN,MAAMtM,MAAM,GAAG;MACX8L,kBAAkB,EAAES,aAAa,CAAC,IAAI,CAACT,kBAAkB,CAAC;MAC1DC,UAAU,EAAEQ,aAAa,CAAC,IAAI,CAACR,UAAU;IAC7C,CAAC;IACD,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,OAAO,KAAK;IAChB;IACA,IAAI,CAACtM,MAAM,CAAC8M,aAAa,CAAC,IAAI,CAACC,OAAO,EAAEzM,MAAM,CAAC;IAC/C,OAAO,IAAI;EACf;EACA,IAAIyM,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/M,MAAM,CAACgN,aAAa,CAAC,IAAI,CAAC3M,QAAQ,EAAE;MAC5CK,UAAU,EAAE,IAAI,CAACX,KAAK;MACtBkM,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B9L,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB+L,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,gBAAgB,EAAEU,aAAa,CAAC,IAAI,CAACV,gBAAgB;IACzD,CAAC,CAAC;EACN;EACA,OAAO/kB,IAAI,YAAA6lB,+BAAA3lB,CAAA;IAAA,YAAAA,CAAA,IAAwF0kB,sBAAsB,EAt+GhCrwB,EAAE,CAAAylB,iBAAA,CAs+GgDnjB,EAAE,CAAC8iB,MAAM,GAt+G3DplB,EAAE,CAAAylB,iBAAA,CAs+GsEnjB,EAAE,CAAC6iB,cAAc,GAt+GzFnlB,EAAE,CAAAuxB,iBAAA,CAs+GoG,UAAU,GAt+GhHvxB,EAAE,CAAAylB,iBAAA,CAs+G4IzlB,EAAE,CAACmqB,SAAS,GAt+G1JnqB,EAAE,CAAAylB,iBAAA,CAs+GqKzlB,EAAE,CAACkqB,UAAU;EAAA;EAC7Q,OAAOvE,IAAI,kBAv+G8E3lB,EAAE,CAAA4lB,iBAAA;IAAAxZ,IAAA,EAu+GJikB,sBAAsB;IAAAxK,SAAA;IAAAyE,YAAA,WAAAkH,oCAAA5tB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAv+GpB5D,EAAE,CAAA6E,UAAA,mBAAA4sB,gDAAA;UAAA,OAu+GJ5tB,GAAA,CAAAotB,OAAA,CAAQ,CAAC;QAAA,CAAY,CAAC;MAAA;IAAA;IAAAnL,MAAA;MAAAwK,WAAA;MAAA9L,QAAA;MAAA+L,mBAAA;MAAAC,gBAAA;MAAAC,kBAAA;MAAAC,UAAA;MAAAC,QAAA;MAAAI,aAAA;MAAAC,mBAAA;IAAA;EAAA;AACjH;AACA;EAAA,QAAA9kB,SAAA,oBAAAA,SAAA,KAz+G6FlM,EAAE,CAAAmM,iBAAA,CAy+GJkkB,sBAAsB,EAAc,CAAC;IACpHjkB,IAAI,EAAE5L,SAAS;IACfgiB,IAAI,EAAE,CAAC;MACCyD,QAAQ,EAAE,wBAAwB;MAClCC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9Z,IAAI,EAAE9J,EAAE,CAAC8iB;EAAO,CAAC,EAAE;IAAEhZ,IAAI,EAAE9J,EAAE,CAAC6iB;EAAe,CAAC,EAAE;IAAE/Y,IAAI,EAAE+L,SAAS;IAAEoK,UAAU,EAAE,CAAC;MACjGnW,IAAI,EAAEpL,SAAS;MACfwhB,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAEpW,IAAI,EAAEpM,EAAE,CAACmqB;EAAU,CAAC,EAAE;IAAE/d,IAAI,EAAEpM,EAAE,CAACkqB;EAAW,CAAC,CAAC,EAAkB;IAAEoG,WAAW,EAAE,CAAC;MACxFlkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEyjB,QAAQ,EAAE,CAAC;MACXpY,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEwvB,mBAAmB,EAAE,CAAC;MACtBnkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEyvB,gBAAgB,EAAE,CAAC;MACnBpkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE0vB,kBAAkB,EAAE,CAAC;MACrBrkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE2vB,UAAU,EAAE,CAAC;MACbtkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE4vB,QAAQ,EAAE,CAAC;MACXvkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEgwB,aAAa,EAAE,CAAC;MAChB3kB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEiwB,mBAAmB,EAAE,CAAC;MACtB5kB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEkwB,OAAO,EAAE,CAAC;MACV7kB,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkP,8BAA8B,CAAC;EACjCrN,MAAM;EACND,KAAK;EACLuN,gBAAgB;EAChB;EACApK,MAAM;EACN;EACA+I,WAAW;EACX;EACA9L,QAAQ;EACR;EACA+L,mBAAmB;EACnB;EACAC,gBAAgB;EAChB;EACAC,kBAAkB;EAClB;EACAC,UAAU;EACVC,QAAQ,GAAG,KAAK;EAChBjM,QAAQ,GAAG,EAAE;EACbkN,YAAY;EACZ;EACAhB,QAAQ;EACR;EACA;EACAiB,IAAI;EACJ/mB,WAAWA,CAACuZ,MAAM,EAAED,KAAK,EAAEuN,gBAAgB,EAAE;IACzC,IAAI,CAACtN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuN,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,YAAY,GAAGvN,MAAM,CAAC/I,MAAM,CAAC+B,SAAS,CAAEyU,CAAC,IAAK;MAC/C,IAAIA,CAAC,YAAYvvB,aAAa,EAAE;QAC5B,IAAI,CAACwvB,sBAAsB,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC;EACN;EACA,IAAIhB,aAAaA,CAACrM,QAAQ,EAAE;IACxB,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAGtQ,KAAK,CAACC,OAAO,CAACqQ,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IACnE,CAAC,MACI;MACD,IAAI,CAACA,QAAQ,GAAG,EAAE;IACtB;EACJ;EACA,IAAIsM,mBAAmBA,CAAC7lB,KAAK,EAAE;IAC3B,IAAIlL,SAAS,CAAC,CAAC,IAAIoL,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;MACxCH,OAAO,CAACG,IAAI,CAAC,qEAAqE,CAAC;IACvF;IACA,IAAI,CAAColB,QAAQ,GAAGzlB,KAAK;EACzB;EACA6mB,WAAWA,CAAChE,OAAO,EAAE;IAAE,IAAI,CAAC+D,sBAAsB,CAAC,CAAC;EAAE;EACtDnV,WAAWA,CAAA,EAAG;IAAE,IAAI,CAACgV,YAAY,CAAC/U,WAAW,CAAC,CAAC;EAAE;EACjDoU,OAAOA,CAACgB,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACxC,IAAIH,MAAM,KAAK,CAAC,IAAIC,OAAO,IAAIC,OAAO,IAAIC,QAAQ,EAAE;MAChD,OAAO,IAAI;IACf;IACA,IAAI,OAAO,IAAI,CAAC7K,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACA,MAAM,KAAK,OAAO,EAAE;MAC5D,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACoJ,QAAQ,EAAE;MACf,OAAO,KAAK;IAChB;IACA,MAAMhM,MAAM,GAAG;MACX8L,kBAAkB,EAAES,aAAa,CAAC,IAAI,CAACT,kBAAkB,CAAC;MAC1DC,UAAU,EAAEQ,aAAa,CAAC,IAAI,CAACR,UAAU;IAC7C,CAAC;IACD,IAAI,CAACrM,MAAM,CAAC8M,aAAa,CAAC,IAAI,CAACC,OAAO,EAAEzM,MAAM,CAAC;IAC/C,OAAO,KAAK;EAChB;EACAoN,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACF,IAAI,GAAG,IAAI,CAACF,gBAAgB,CAACU,kBAAkB,CAAC,IAAI,CAAChO,MAAM,CAACiO,YAAY,CAAC,IAAI,CAAClB,OAAO,CAAC,CAAC;EAChG;EACA,IAAIA,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/M,MAAM,CAACgN,aAAa,CAAC,IAAI,CAAC3M,QAAQ,EAAE;MAC5CK,UAAU,EAAE,IAAI,CAACX,KAAK;MACtBkM,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B9L,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB+L,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,gBAAgB,EAAEU,aAAa,CAAC,IAAI,CAACV,gBAAgB;IACzD,CAAC,CAAC;EACN;EACA,OAAO/kB,IAAI,YAAA8mB,uCAAA5mB,CAAA;IAAA,YAAAA,CAAA,IAAwF+lB,8BAA8B,EApmHxC1xB,EAAE,CAAAylB,iBAAA,CAomHwDnjB,EAAE,CAAC8iB,MAAM,GApmHnEplB,EAAE,CAAAylB,iBAAA,CAomH8EnjB,EAAE,CAAC6iB,cAAc,GApmHjGnlB,EAAE,CAAAylB,iBAAA,CAomH4GtkB,EAAE,CAACqxB,gBAAgB;EAAA;EAC1N,OAAO7M,IAAI,kBArmH8E3lB,EAAE,CAAA4lB,iBAAA;IAAAxZ,IAAA,EAqmHJslB,8BAA8B;IAAA7L,SAAA;IAAA4M,QAAA;IAAAnI,YAAA,WAAAoI,4CAAA9uB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QArmH5B5D,EAAE,CAAA6E,UAAA,mBAAA8tB,wDAAAlI,MAAA;UAAA,OAqmHJ5mB,GAAA,CAAAotB,OAAA,CAAAxG,MAAA,CAAAwH,MAAA,EAAAxH,MAAA,CAAAyH,OAAA,EAAAzH,MAAA,CAAA0H,OAAA,EAAA1H,MAAA,CAAA2H,QAAmE,CAAC;QAAA,CAAvC,CAAC;MAAA;MAAA,IAAAxuB,EAAA;QArmH5B5D,EAAE,CAAA4yB,cAAA,SAAA/uB,GAAA,CAAAguB,IAAA,EAAF7xB,EAAE,CAAA6yB,aAqmHyB,CAAC;QArmH5B7yB,EAAE,CAAA8yB,WAAA,WAAAjvB,GAAA,CAAA0jB,MAAA;MAAA;IAAA;IAAAzB,MAAA;MAAAyB,MAAA;MAAA+I,WAAA;MAAA9L,QAAA;MAAA+L,mBAAA;MAAAC,gBAAA;MAAAC,kBAAA;MAAAC,UAAA;MAAAC,QAAA;MAAAI,aAAA;MAAAC,mBAAA;IAAA;IAAAvB,QAAA,GAAFzvB,EAAE,CAAA+yB,oBAAA;EAAA;AAsmH/F;AACA;EAAA,QAAA7mB,SAAA,oBAAAA,SAAA,KAvmH6FlM,EAAE,CAAAmM,iBAAA,CAumHJulB,8BAA8B,EAAc,CAAC;IAC5HtlB,IAAI,EAAE5L,SAAS;IACfgiB,IAAI,EAAE,CAAC;MACCyD,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9Z,IAAI,EAAE9J,EAAE,CAAC8iB;EAAO,CAAC,EAAE;IAAEhZ,IAAI,EAAE9J,EAAE,CAAC6iB;EAAe,CAAC,EAAE;IAAE/Y,IAAI,EAAEjL,EAAE,CAACqxB;EAAiB,CAAC,CAAC,EAAkB;IAAEjL,MAAM,EAAE,CAAC;MAChInb,IAAI,EAAEnL,WAAW;MACjBuhB,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,EAAE;MACCpW,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEuvB,WAAW,EAAE,CAAC;MACdlkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEyjB,QAAQ,EAAE,CAAC;MACXpY,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEwvB,mBAAmB,EAAE,CAAC;MACtBnkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEyvB,gBAAgB,EAAE,CAAC;MACnBpkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE0vB,kBAAkB,EAAE,CAAC;MACrBrkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE2vB,UAAU,EAAE,CAAC;MACbtkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE4vB,QAAQ,EAAE,CAAC;MACXvkB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE8wB,IAAI,EAAE,CAAC;MACPzlB,IAAI,EAAEnL;IACV,CAAC,CAAC;IAAE8vB,aAAa,EAAE,CAAC;MAChB3kB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEiwB,mBAAmB,EAAE,CAAC;MACtB5kB,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEkwB,OAAO,EAAE,CAAC;MACV7kB,IAAI,EAAE3L,YAAY;MAClB+hB,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,CAAC;IAC5F,CAAC;EAAE,CAAC;AAAA;AAChB,SAAS0O,aAAaA,CAACY,CAAC,EAAE;EACtB,OAAOA,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,CAAC;AAC1B;;AAEA;AACA;AACA;AACA,MAAMkB,gBAAgB,CAAC;EACnB3pB,aAAa;EACbqL,MAAM;AACV;AACA;AAEA,MAAMue,MAAM,GAAG,EAAE;AACjB,MAAMC,cAAc,CAAC;EACjB,OAAOznB,IAAI,YAAA0nB,uBAAAxnB,CAAA;IAAA,YAAAA,CAAA,IAAwFunB,cAAc;EAAA;EACjH,OAAOE,IAAI,kBA1pH8EpzB,EAAE,CAAAqzB,gBAAA;IAAAjnB,IAAA,EA0pHS8mB;EAAc;EAClH,OAAOI,IAAI,kBA3pH8EtzB,EAAE,CAAAuzB,gBAAA;IAAApD,SAAA,EA2pHoC,CAACrQ,gBAAgB,EAAEqG,aAAa,EAAEvF,kBAAkB,EAAE5V,SAAS,CAAC;IAAAwoB,OAAA,GAAYnyB,YAAY;EAAA;AAC3N;AACA;EAAA,QAAA6K,SAAA,oBAAAA,SAAA,KA7pH6FlM,EAAE,CAAAmM,iBAAA,CA6pHJ+mB,cAAc,EAAc,CAAC;IAC5G9mB,IAAI,EAAElL,QAAQ;IACdshB,IAAI,EAAE,CAAC;MACCgR,OAAO,EAAE,CACLnyB;MACA;MACA;MAAA,CACH;MACDoyB,YAAY,EAAE,CAAC5H,iBAAiB,EAAEvG,sBAAsB,EAAEkB,cAAc,EAAE6J,sBAAsB,EAAEqB,8BAA8B,CAAC;MACjIgC,OAAO,EAAE,CAAC7H,iBAAiB,EAAEvG,sBAAsB,EAAE+K,sBAAsB,EAAEqB,8BAA8B,CAAC;MAC5GvB,SAAS,EAAE,CAACrQ,gBAAgB,EAAEqG,aAAa,EAAEvF,kBAAkB,EAAE5V,SAAS;IAC9E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM2oB,UAAU,CAAC;EACb;AACJ;AACA;EACI3wB,EAAE;EACF;AACJ;AACA;EACImO,QAAQ;EACR;AACJ;AACA;EACI7M,MAAM;EACN;AACJ;AACA;EACIoR,SAAS;EACT;AACJ;AACA;EACIrQ,KAAK;EACL;AACJ;AACA;EACIC,OAAO;EACP;AACJ;AACA;EACIC,OAAO;EACP;AACJ;AACA;EACIiN,UAAU;EACV;AACJ;AACA;EACI7J,MAAM;EACN;AACJ;AACA;EACIyI,QAAQ;EACR;AACJ;AACA;EACIxL,IAAI;EACJ;AACJ;AACA;EACIJ,IAAI;EACJ;AACJ;AACA;EACIL,OAAO;EACP;AACJ;AACA;EACImV,UAAU;EACV;AACJ;AACA;EACIC,eAAe;EACf;AACJ;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;EACIC,kBAAkB;EAClB;AACJ;AACA;EACIC,mBAAmB;EACnB;AACJ;AACA;EACIjV,WAAW;EACX;AACJ;AACA;EACI0U,YAAY;AAChB;;AAEA;AACA;AACA;;AAEA,SAAS0R,iBAAiB,EAAEqH,cAAc,EAAE5N,sBAAsB,EAAE+K,sBAAsB,EAAEqB,8BAA8B,EAAEiC,UAAU,EAAEX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}