{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Observable, finalize } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport let LoadingService = /*#__PURE__*/(() => {\n  class LoadingService {\n    constructor(loadingController) {\n      this.loadingController = loadingController;\n      this.loadingSubject = new BehaviorSubject(false);\n      this.loadingCount = 0;\n      this.currentLoading = null;\n      // Enhanced loading state management\n      this.loadingStateSubject = new BehaviorSubject({});\n      this.loadingState$ = this.loadingStateSubject.asObservable();\n      this.isLoading$ = this.loadingSubject.asObservable();\n    }\n    show() {\n      var _this = this;\n      return _asyncToGenerator(function* (message = 'Loading...', duration) {\n        _this.loadingCount++;\n        if (_this.loadingCount === 1) {\n          _this.loadingSubject.next(true);\n          _this.currentLoading = yield _this.loadingController.create({\n            message,\n            duration,\n            spinner: 'crescent',\n            cssClass: 'custom-loading'\n          });\n          yield _this.currentLoading.present();\n        }\n      }).apply(this, arguments);\n    }\n    hide() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (_this2.loadingCount > 0) {\n          _this2.loadingCount--;\n        }\n        if (_this2.loadingCount === 0) {\n          _this2.loadingSubject.next(false);\n          if (_this2.currentLoading) {\n            yield _this2.currentLoading.dismiss();\n            _this2.currentLoading = null;\n          }\n        }\n      })();\n    }\n    hideAll() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        _this3.loadingCount = 0;\n        _this3.loadingSubject.next(false);\n        if (_this3.currentLoading) {\n          yield _this3.currentLoading.dismiss();\n          _this3.currentLoading = null;\n        }\n      })();\n    }\n    isLoading() {\n      return this.loadingSubject.value;\n    }\n    // Enhanced loading methods\n    startLoading(key, message) {\n      const currentState = this.loadingStateSubject.value;\n      const newState = {\n        ...currentState,\n        [key]: {\n          isLoading: true,\n          message,\n          progress: 0,\n          startTime: new Date()\n        }\n      };\n      this.loadingStateSubject.next(newState);\n      this.updateGlobalLoading();\n    }\n    stopLoading(key) {\n      const currentState = this.loadingStateSubject.value;\n      const newState = {\n        ...currentState\n      };\n      if (newState[key]) {\n        delete newState[key];\n      }\n      this.loadingStateSubject.next(newState);\n      this.updateGlobalLoading();\n    }\n    updateProgress(key, progress, message) {\n      const currentState = this.loadingStateSubject.value;\n      if (currentState[key]) {\n        const newState = {\n          ...currentState,\n          [key]: {\n            ...currentState[key],\n            progress: Math.max(0, Math.min(100, progress)),\n            message: message || currentState[key].message\n          }\n        };\n        this.loadingStateSubject.next(newState);\n      }\n    }\n    isLoadingKey(key) {\n      return new Observable(observer => {\n        this.loadingState$.subscribe(state => {\n          observer.next(!!state[key]?.isLoading);\n        });\n      });\n    }\n    isLoadingKeySync(key) {\n      const currentState = this.loadingStateSubject.value;\n      return !!currentState[key]?.isLoading;\n    }\n    updateGlobalLoading() {\n      const currentState = this.loadingStateSubject.value;\n      const hasLoading = Object.values(currentState).some(state => state.isLoading);\n      this.loadingSubject.next(hasLoading);\n    }\n    wrapWithLoading(source, key, message) {\n      this.startLoading(key, message);\n      return source.pipe(finalize(() => this.stopLoading(key)));\n    }\n    // Predefined loading keys\n    static {\n      this.KEYS = {\n        LOGIN: 'auth.login',\n        LOGOUT: 'auth.logout',\n        REGISTER: 'auth.register',\n        PROFILE_LOAD: 'user.profile.load',\n        PRODUCTS_LOAD: 'shop.products.load',\n        CART_LOAD: 'cart.load',\n        SEARCH: 'search.execute',\n        BRANDS_LOAD: 'shop.brands.load',\n        GLOBAL: 'global'\n      };\n    }\n    static {\n      this.ɵfac = function LoadingService_Factory(t) {\n        return new (t || LoadingService)(i0.ɵɵinject(i1.LoadingController));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LoadingService,\n        factory: LoadingService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return LoadingService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}