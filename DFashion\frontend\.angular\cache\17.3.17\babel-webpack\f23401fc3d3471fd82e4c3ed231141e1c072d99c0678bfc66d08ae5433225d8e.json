{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { IonicModule } from '@ionic/angular';\nimport { IonicStorageModule } from '@ionic/storage-angular';\nimport { routes } from './app.routes';\nimport { authInterceptor } from './core/interceptors/auth.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor])), provideAnimations(), importProvidersFrom(MatSnackBarModule, MatDialogModule, IonicModule.forRoot({\n    mode: 'ios',\n    rippleEffect: true,\n    animated: true\n  }), IonicStorageModule.forRoot())]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}