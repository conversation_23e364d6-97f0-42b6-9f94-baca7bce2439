{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/services/bill.service\";\nimport * as i3 from \"@angular/common\";\nfunction PaymentSuccessComponent_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"span\", 25);\n    i0.ɵɵtext(2, \"Payment Method:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.getPaymentMethodName(ctx_r0.paymentMethod));\n  }\n}\nfunction PaymentSuccessComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"Order Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 24)(4, \"span\", 25);\n    i0.ɵɵtext(5, \"Order ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 26);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, PaymentSuccessComponent_div_10_div_8_Template, 5, 1, \"div\", 27);\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"span\", 25);\n    i0.ɵɵtext(11, \"Order Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 26);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.orderId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.paymentMethod);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 3, ctx_r0.orderDate, \"medium\"));\n  }\n}\nfunction PaymentSuccessComponent_div_42_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function PaymentSuccessComponent_div_42_div_4_Template_div_click_0_listener() {\n      const product_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.viewProduct(product_r3));\n    });\n    i0.ɵɵelement(1, \"img\", 32);\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r3.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(product_r3.price));\n  }\n}\nfunction PaymentSuccessComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"h3\");\n    i0.ɵɵtext(2, \"You might also like\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtemplate(4, PaymentSuccessComponent_div_42_div_4_Template, 6, 4, \"div\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.recommendedProducts);\n  }\n}\nexport let PaymentSuccessComponent = /*#__PURE__*/(() => {\n  class PaymentSuccessComponent {\n    constructor(route, router, billService) {\n      this.route = route;\n      this.router = router;\n      this.billService = billService;\n      this.orderId = '';\n      this.paymentMethod = '';\n      this.orderDate = new Date();\n      this.isGeneratingBill = false;\n      this.recommendedProducts = [];\n    }\n    ngOnInit() {\n      // Get order details from query parameters\n      this.route.queryParams.subscribe(params => {\n        this.orderId = params['orderId'] || '';\n        this.paymentMethod = params['method'] || '';\n      });\n      this.loadRecommendedProducts();\n    }\n    loadRecommendedProducts() {\n      // Mock recommended products - in real app, this would come from API\n      this.recommendedProducts = [{\n        id: '1',\n        name: 'Summer Dress',\n        price: 129.99,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n      }, {\n        id: '2',\n        name: 'Casual Shirt',\n        price: 79.99,\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'\n      }, {\n        id: '3',\n        name: 'Denim Jacket',\n        price: 159.99,\n        image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=200'\n      }];\n    }\n    downloadBill() {\n      this.isGeneratingBill = true;\n      // Mock bill data - in real app, this would come from the order\n      const billData = {\n        orderId: this.orderId,\n        paymentId: 'pay_' + Date.now(),\n        customerName: 'Customer Name',\n        customerEmail: '<EMAIL>',\n        customerPhone: '+91 9876543210',\n        customerAddress: 'Sample Address, City, State - 123456',\n        items: [{\n          name: 'Sample Product',\n          description: 'Product description',\n          quantity: 1,\n          price: 99.99,\n          total: 99.99\n        }],\n        subtotal: 99.99,\n        tax: 17.99,\n        shipping: 0,\n        discount: 0,\n        total: 117.98,\n        paymentMethod: this.getPaymentMethodName(this.paymentMethod),\n        orderDate: this.orderDate,\n        billNumber: this.billService.generateBillNumber()\n      };\n      try {\n        this.billService.downloadBill(billData);\n      } catch (error) {\n        console.error('Error downloading bill:', error);\n        alert('Error downloading bill. Please try again.');\n      } finally {\n        this.isGeneratingBill = false;\n      }\n    }\n    viewOrder() {\n      this.router.navigate(['/account/orders', this.orderId]);\n    }\n    continueShopping() {\n      this.router.navigate(['/']);\n    }\n    viewProduct(product) {\n      this.router.navigate(['/product', product.id]);\n    }\n    getPaymentMethodName(method) {\n      const methodNames = {\n        'razorpay': 'Credit/Debit Card',\n        'upi': 'UPI',\n        'netbanking': 'Net Banking',\n        'wallet': 'Digital Wallet',\n        'cod': 'Cash on Delivery'\n      };\n      return methodNames[method] || method;\n    }\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR'\n      }).format(price);\n    }\n    static {\n      this.ɵfac = function PaymentSuccessComponent_Factory(t) {\n        return new (t || PaymentSuccessComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.BillService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PaymentSuccessComponent,\n        selectors: [[\"app-payment-success\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 43,\n        vars: 4,\n        consts: [[1, \"payment-success-container\"], [1, \"success-card\"], [1, \"success-icon\"], [1, \"checkmark-circle\"], [1, \"checkmark\"], [1, \"success-content\"], [1, \"success-message\"], [\"class\", \"order-details\", 4, \"ngIf\"], [1, \"email-notification\"], [1, \"fas\", \"fa-envelope\"], [1, \"action-buttons\"], [1, \"btn\", \"btn-primary\", \"download-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"btn\", \"btn-outline\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"additional-info\"], [1, \"info-item\"], [1, \"fas\", \"fa-truck\"], [1, \"fas\", \"fa-headset\"], [\"href\", \"mailto:<EMAIL>\"], [\"class\", \"recommendations\", 4, \"ngIf\"], [1, \"order-details\"], [1, \"detail-row\"], [1, \"label\"], [1, \"value\"], [\"class\", \"detail-row\", 4, \"ngIf\"], [1, \"recommendations\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [3, \"src\", \"alt\"], [1, \"price\"]],\n        template: function PaymentSuccessComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"div\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"h1\");\n            i0.ɵɵtext(7, \"Payment Successful!\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 6);\n            i0.ɵɵtext(9, \" Thank you for your purchase. Your order has been confirmed and you will receive an email confirmation shortly. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, PaymentSuccessComponent_div_10_Template, 15, 6, \"div\", 7);\n            i0.ɵɵelementStart(11, \"div\", 8);\n            i0.ɵɵelement(12, \"i\", 9);\n            i0.ɵɵelementStart(13, \"p\");\n            i0.ɵɵtext(14, \"A detailed invoice has been sent to your email address.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 10)(16, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function PaymentSuccessComponent_Template_button_click_16_listener() {\n              return ctx.downloadBill();\n            });\n            i0.ɵɵelement(17, \"i\", 12);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function PaymentSuccessComponent_Template_button_click_19_listener() {\n              return ctx.viewOrder();\n            });\n            i0.ɵɵelement(20, \"i\", 14);\n            i0.ɵɵtext(21, \" View Order \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function PaymentSuccessComponent_Template_button_click_22_listener() {\n              return ctx.continueShopping();\n            });\n            i0.ɵɵelement(23, \"i\", 16);\n            i0.ɵɵtext(24, \" Continue Shopping \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 17)(26, \"div\", 18);\n            i0.ɵɵelement(27, \"i\", 19);\n            i0.ɵɵelementStart(28, \"div\")(29, \"h4\");\n            i0.ɵɵtext(30, \"Shipping Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"p\");\n            i0.ɵɵtext(32, \"Your order will be shipped within 2-3 business days. You'll receive tracking information via email.\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(33, \"div\", 18);\n            i0.ɵɵelement(34, \"i\", 20);\n            i0.ɵɵelementStart(35, \"div\")(36, \"h4\");\n            i0.ɵɵtext(37, \"Customer Support\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"p\");\n            i0.ɵɵtext(39, \"Need help? Contact our support team at \");\n            i0.ɵɵelementStart(40, \"a\", 21);\n            i0.ɵɵtext(41, \"<EMAIL>\");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵtemplate(42, PaymentSuccessComponent_div_42_Template, 5, 1, \"div\", 22);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ctx.orderId);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.isGeneratingBill);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isGeneratingBill ? \"Generating...\" : \"Download Invoice\", \" \");\n            i0.ɵɵadvance(24);\n            i0.ɵɵproperty(\"ngIf\", ctx.recommendedProducts.length > 0);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DatePipe, RouterModule],\n        styles: [\".payment-success-container[_ngcontent-%COMP%]{min-height:calc(100vh - 80px);background:linear-gradient(135deg,#667eea,#764ba2);padding:40px 20px;display:flex;flex-direction:column;align-items:center}.success-card[_ngcontent-%COMP%]{background:#fff;border-radius:20px;padding:40px;max-width:600px;width:100%;box-shadow:0 20px 60px #0003;text-align:center;margin-bottom:40px}.success-icon[_ngcontent-%COMP%]{margin-bottom:30px}.checkmark-circle[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:linear-gradient(135deg,#43e97b,#38f9d7);margin:0 auto;position:relative;animation:_ngcontent-%COMP%_scaleIn .6s ease-out}.checkmark[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:30px;height:15px;border-left:4px solid white;border-bottom:4px solid white;transform:translate(-50%,-60%) rotate(-45deg);animation:_ngcontent-%COMP%_checkmarkDraw .4s ease-out .3s both}@keyframes _ngcontent-%COMP%_scaleIn{0%{transform:scale(0);opacity:0}50%{transform:scale(1.1)}to{transform:scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_checkmarkDraw{0%{width:0;height:0}50%{width:30px;height:0}to{width:30px;height:15px}}.success-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#2d3748;margin-bottom:16px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .2s both}.success-content[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%]{font-size:16px;color:#718096;line-height:1.6;margin-bottom:30px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .4s both}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.order-details[_ngcontent-%COMP%]{background:#f8fafc;border-radius:12px;padding:20px;margin-bottom:30px;text-align:left;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .6s both}.order-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#2d3748;margin-bottom:16px;text-align:center}.order-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 0;border-bottom:1px solid #e2e8f0}.order-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]:last-child{border-bottom:none}.order-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#4a5568}.order-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-weight:600;color:#2d3748}.email-notification[_ngcontent-%COMP%]{background:#e6fffa;border:1px solid #81e6d9;border-radius:8px;padding:16px;margin-bottom:30px;display:flex;align-items:center;gap:12px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .8s both}.email-notification[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#319795;font-size:20px}.email-notification[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#2c7a7b;font-size:14px}.action-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:40px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out 1s both}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:14px 24px;border-radius:8px;font-size:16px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:8px;text-decoration:none;border:none}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.action-buttons[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.action-buttons[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 8px 25px #667eea66}.action-buttons[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{background:#4a5568;color:#fff}.action-buttons[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover{background:#2d3748;transform:translateY(-2px)}.action-buttons[_ngcontent-%COMP%]   .btn-outline[_ngcontent-%COMP%]{background:transparent;border:2px solid #667eea;color:#667eea}.action-buttons[_ngcontent-%COMP%]   .btn-outline[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff;transform:translateY(-2px)}.additional-info[_ngcontent-%COMP%]{text-align:left;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out 1.2s both}.additional-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:20px;padding:16px;background:#f8fafc;border-radius:8px}.additional-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#667eea;font-size:20px;margin-top:4px;flex-shrink:0}.additional-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#2d3748;margin:0 0 8px}.additional-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0;line-height:1.5}.additional-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#667eea;text-decoration:none}.additional-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.recommendations[_ngcontent-%COMP%]{background:#fff;border-radius:20px;padding:30px;max-width:800px;width:100%;box-shadow:0 10px 40px #0000001a}.recommendations[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#2d3748;text-align:center;margin-bottom:30px}.recommendations[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{background:#f8fafc;border-radius:12px;padding:16px;cursor:pointer;transition:all .3s ease;text-align:center}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #0000001a}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:120px;object-fit:cover;border-radius:8px;margin-bottom:12px}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#2d3748;margin:0 0 8px}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#667eea;margin:0}@media (max-width: 768px){.payment-success-container[_ngcontent-%COMP%]{padding:20px 16px}.success-card[_ngcontent-%COMP%]{padding:30px 20px}.success-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:28px}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:12px 20px;font-size:14px}.recommendations[_ngcontent-%COMP%]{padding:20px}.recommendations[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:16px}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{padding:12px}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100px}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px}.recommendations[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{font-size:16px}}@media (max-width: 480px){.order-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:4px}.additional-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{flex-direction:column;gap:8px}.recommendations[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return PaymentSuccessComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}