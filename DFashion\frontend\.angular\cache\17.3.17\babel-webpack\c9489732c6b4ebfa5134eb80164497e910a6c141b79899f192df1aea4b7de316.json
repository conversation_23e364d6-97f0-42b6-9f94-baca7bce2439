{"ast": null, "code": "import { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RecommendationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/api`;\n    this.userAnalytics$ = new BehaviorSubject(null);\n  }\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId, limit = 10) {\n    const params = new URLSearchParams();\n    if (userId) params.append('userId', userId);\n    params.append('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/recommendations/suggested?${params.toString()}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching suggested products:', error);\n      return this.getFallbackSuggestedProducts(limit);\n    }));\n  }\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category, limit = 10) {\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/recommendations/trending?${params.toString()}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching trending products:', error);\n      return this.getFallbackTrendingProducts(limit);\n    }));\n  }\n  // Similar Products - Based on Product\n  getSimilarProducts(productId, limit = 6) {\n    return this.http.get(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching similar products:', error);\n      return this.getFallbackSimilarProducts(limit);\n    }));\n  }\n  // Recently Viewed Products\n  getRecentlyViewed(userId, limit = 8) {\n    return this.http.get(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching recently viewed:', error);\n      return this.getFallbackRecentProducts(limit);\n    }));\n  }\n  // Track User Behavior for Analytics\n  trackProductView(productId, category, duration = 0) {\n    return this.http.post(`${this.apiUrl}/analytics/track-view`, {\n      productId,\n      category,\n      duration,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking product view:', error);\n      return [];\n    }));\n  }\n  trackSearch(query, category, resultsClicked = 0) {\n    return this.http.post(`${this.apiUrl}/analytics/track-search`, {\n      query,\n      category,\n      resultsClicked,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking search:', error);\n      return [];\n    }));\n  }\n  trackPurchase(productId, category, price) {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking purchase:', error);\n      return [];\n    }));\n  }\n  // User Analytics\n  getUserAnalytics(userId) {\n    return this.http.get(`${this.apiUrl}/analytics/user/${userId}`).pipe(map(response => response.success ? response.data : this.getDefaultAnalytics(userId)), catchError(error => {\n      console.error('Error fetching user analytics:', error);\n      return [this.getDefaultAnalytics(userId)];\n    }));\n  }\n  // Category-based Recommendations\n  getCategoryRecommendations(category, limit = 8) {\n    return this.http.get(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching category recommendations:', error);\n      return this.getFallbackCategoryProducts(category, limit);\n    }));\n  }\n  // Fallback methods for offline/error scenarios\n  getFallbackSuggestedProducts(limit) {\n    // Return mock suggested products based on popular items\n    const mockProducts = [{\n      _id: 'suggested-1',\n      name: 'Trending Cotton T-Shirt',\n      description: 'Popular cotton t-shirt based on your preferences',\n      price: 899,\n      originalPrice: 1299,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        alt: 'Cotton T-Shirt',\n        isPrimary: true\n      }],\n      category: 'men',\n      subcategory: 'shirts',\n      brand: 'ComfortWear',\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      tags: ['cotton', 'casual', 'trending'],\n      isActive: true,\n      isFeatured: true,\n      recommendationScore: 0.85,\n      recommendationReason: 'Based on your recent views'\n    }];\n    return new Observable(observer => {\n      observer.next(mockProducts.slice(0, limit));\n      observer.complete();\n    });\n  }\n  getFallbackTrendingProducts(limit) {\n    const mockTrending = [{\n      _id: 'trending-1',\n      name: 'Viral Summer Dress',\n      description: 'This dress is trending across social media',\n      price: 2499,\n      originalPrice: 3499,\n      discount: 29,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        alt: 'Summer Dress',\n        isPrimary: true\n      }],\n      category: 'women',\n      subcategory: 'dresses',\n      brand: 'StyleHub',\n      rating: {\n        average: 4.5,\n        count: 89\n      },\n      tags: ['summer', 'trending', 'viral'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.92,\n      trendingReason: 'Viral on social media',\n      viewCount: 15420,\n      purchaseCount: 342,\n      shareCount: 1250,\n      engagementRate: 8.7\n    }];\n    return new Observable(observer => {\n      observer.next(mockTrending.slice(0, limit));\n      observer.complete();\n    });\n  }\n  getFallbackSimilarProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackRecentProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackCategoryProducts(category, limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getDefaultAnalytics(userId) {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: {\n        min: 500,\n        max: 5000\n      },\n      brandPreferences: []\n    };\n  }\n  // Update user analytics locally\n  updateUserAnalytics(analytics) {\n    this.userAnalytics$.next(analytics);\n  }\n  // Get current user analytics\n  getCurrentUserAnalytics() {\n    return this.userAnalytics$.asObservable();\n  }\n  static {\n    this.ɵfac = function RecommendationService_Factory(t) {\n      return new (t || RecommendationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RecommendationService,\n      factory: RecommendationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "BehaviorSubject", "map", "catchError", "RecommendationService", "constructor", "http", "apiUrl", "environment", "userAnalytics$", "getSuggestedProducts", "userId", "limit", "params", "URLSearchParams", "append", "toString", "get", "pipe", "response", "success", "data", "error", "console", "getFallbackSuggestedProducts", "getTrendingProducts", "category", "getFallbackTrendingProducts", "getSimilarProducts", "productId", "getFallbackSimilarProducts", "getRecently<PERSON>iewed", "getFallbackRecentProducts", "trackProductView", "duration", "post", "timestamp", "Date", "trackSearch", "query", "resultsClicked", "trackPurchase", "price", "getUserAnalytics", "getDefaultAnalytics", "getCategoryRecommendations", "getFallbackCategoryProducts", "mockProducts", "_id", "name", "description", "originalPrice", "discount", "images", "url", "alt", "isPrimary", "subcategory", "brand", "rating", "average", "count", "tags", "isActive", "isFeatured", "recommendationScore", "recommendationReason", "observer", "next", "slice", "complete", "mockTrending", "trendingScore", "trendingReason", "viewCount", "purchaseCount", "shareCount", "engagementRate", "viewHistory", "searchHistory", "purchaseHistory", "wishlistItems", "cartItems", "preferredCategories", "priceRange", "min", "max", "brandPreferences", "updateUserAnalytics", "analytics", "getCurrentUserAnalytics", "asObservable", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\recommendation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\n\nexport interface RecommendationProduct {\n  _id: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice: number;\n  discount: number;\n  images: Array<{ url: string; alt: string; isPrimary: boolean }>;\n  category: string;\n  subcategory: string;\n  brand: string;\n  rating: { average: number; count: number };\n  tags: string[];\n  isActive: boolean;\n  isFeatured: boolean;\n  recommendationScore?: number;\n  recommendationReason?: string;\n}\n\nexport interface TrendingProduct extends RecommendationProduct {\n  trendingScore: number;\n  trendingReason: string;\n  viewCount: number;\n  purchaseCount: number;\n  shareCount: number;\n  engagementRate: number;\n}\n\nexport interface UserAnalytics {\n  userId: string;\n  viewHistory: Array<{\n    productId: string;\n    category: string;\n    timestamp: Date;\n    duration: number;\n  }>;\n  searchHistory: Array<{\n    query: string;\n    category?: string;\n    timestamp: Date;\n    resultsClicked: number;\n  }>;\n  purchaseHistory: Array<{\n    productId: string;\n    category: string;\n    price: number;\n    timestamp: Date;\n  }>;\n  wishlistItems: string[];\n  cartItems: string[];\n  preferredCategories: string[];\n  priceRange: { min: number; max: number };\n  brandPreferences: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RecommendationService {\n  private apiUrl = `${environment.apiUrl}/api`;\n  private userAnalytics$ = new BehaviorSubject<UserAnalytics | null>(null);\n\n  constructor(private http: HttpClient) {}\n\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId?: string, limit: number = 10): Observable<RecommendationProduct[]> {\n    const params = new URLSearchParams();\n    if (userId) params.append('userId', userId);\n    params.append('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/suggested?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching suggested products:', error);\n          return this.getFallbackSuggestedProducts(limit);\n        })\n      );\n  }\n\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category?: string, limit: number = 10): Observable<TrendingProduct[]> {\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/trending?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching trending products:', error);\n          return this.getFallbackTrendingProducts(limit);\n        })\n      );\n  }\n\n  // Similar Products - Based on Product\n  getSimilarProducts(productId: string, limit: number = 6): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching similar products:', error);\n          return this.getFallbackSimilarProducts(limit);\n        })\n      );\n  }\n\n  // Recently Viewed Products\n  getRecentlyViewed(userId: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching recently viewed:', error);\n          return this.getFallbackRecentProducts(limit);\n        })\n      );\n  }\n\n  // Track User Behavior for Analytics\n  trackProductView(productId: string, category: string, duration: number = 0): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-view`, {\n      productId,\n      category,\n      duration,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking product view:', error);\n        return [];\n      })\n    );\n  }\n\n  trackSearch(query: string, category?: string, resultsClicked: number = 0): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-search`, {\n      query,\n      category,\n      resultsClicked,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking search:', error);\n        return [];\n      })\n    );\n  }\n\n  trackPurchase(productId: string, category: string, price: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking purchase:', error);\n        return [];\n      })\n    );\n  }\n\n  // User Analytics\n  getUserAnalytics(userId: string): Observable<UserAnalytics> {\n    return this.http.get<any>(`${this.apiUrl}/analytics/user/${userId}`)\n      .pipe(\n        map(response => response.success ? response.data : this.getDefaultAnalytics(userId)),\n        catchError(error => {\n          console.error('Error fetching user analytics:', error);\n          return [this.getDefaultAnalytics(userId)];\n        })\n      );\n  }\n\n  // Category-based Recommendations\n  getCategoryRecommendations(category: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching category recommendations:', error);\n          return this.getFallbackCategoryProducts(category, limit);\n        })\n      );\n  }\n\n  // Fallback methods for offline/error scenarios\n  private getFallbackSuggestedProducts(limit: number): Observable<RecommendationProduct[]> {\n    // Return mock suggested products based on popular items\n    const mockProducts: RecommendationProduct[] = [\n      {\n        _id: 'suggested-1',\n        name: 'Trending Cotton T-Shirt',\n        description: 'Popular cotton t-shirt based on your preferences',\n        price: 899,\n        originalPrice: 1299,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', alt: 'Cotton T-Shirt', isPrimary: true }],\n        category: 'men',\n        subcategory: 'shirts',\n        brand: 'ComfortWear',\n        rating: { average: 4.2, count: 156 },\n        tags: ['cotton', 'casual', 'trending'],\n        isActive: true,\n        isFeatured: true,\n        recommendationScore: 0.85,\n        recommendationReason: 'Based on your recent views'\n      }\n    ];\n    return new Observable(observer => {\n      observer.next(mockProducts.slice(0, limit));\n      observer.complete();\n    });\n  }\n\n  private getFallbackTrendingProducts(limit: number): Observable<TrendingProduct[]> {\n    const mockTrending: TrendingProduct[] = [\n      {\n        _id: 'trending-1',\n        name: 'Viral Summer Dress',\n        description: 'This dress is trending across social media',\n        price: 2499,\n        originalPrice: 3499,\n        discount: 29,\n        images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400', alt: 'Summer Dress', isPrimary: true }],\n        category: 'women',\n        subcategory: 'dresses',\n        brand: 'StyleHub',\n        rating: { average: 4.5, count: 89 },\n        tags: ['summer', 'trending', 'viral'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.92,\n        trendingReason: 'Viral on social media',\n        viewCount: 15420,\n        purchaseCount: 342,\n        shareCount: 1250,\n        engagementRate: 8.7\n      }\n    ];\n    return new Observable(observer => {\n      observer.next(mockTrending.slice(0, limit));\n      observer.complete();\n    });\n  }\n\n  private getFallbackSimilarProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackRecentProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackCategoryProducts(category: string, limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getDefaultAnalytics(userId: string): UserAnalytics {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: { min: 500, max: 5000 },\n      brandPreferences: []\n    };\n  }\n\n  // Update user analytics locally\n  updateUserAnalytics(analytics: UserAnalytics): void {\n    this.userAnalytics$.next(analytics);\n  }\n\n  // Get current user analytics\n  getCurrentUserAnalytics(): Observable<UserAnalytics | null> {\n    return this.userAnalytics$.asObservable();\n  }\n}\n"], "mappings": "AAEA,SAASA,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AA4DhD,OAAM,MAAOC,qBAAqB;EAIhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,MAAM,GAAG,GAAGC,WAAW,CAACD,MAAM,MAAM;IACpC,KAAAE,cAAc,GAAG,IAAIR,eAAe,CAAuB,IAAI,CAAC;EAEjC;EAEvC;EACAS,oBAAoBA,CAACC,MAAe,EAAEC,KAAA,GAAgB,EAAE;IACtD,MAAMC,MAAM,GAAG,IAAIC,eAAe,EAAE;IACpC,IAAIH,MAAM,EAAEE,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAAC;IAC3CE,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEH,KAAK,CAACI,QAAQ,EAAE,CAAC;IAExC,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAM,GAAG,IAAI,CAACV,MAAM,8BAA8BM,MAAM,CAACG,QAAQ,EAAE,EAAE,CAAC,CACvFE,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDlB,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,IAAI,CAACE,4BAA4B,CAACZ,KAAK,CAAC;IACjD,CAAC,CAAC,CACH;EACL;EAEA;EACAa,mBAAmBA,CAACC,QAAiB,EAAEd,KAAA,GAAgB,EAAE;IACvD,MAAMC,MAAM,GAAG,IAAIC,eAAe,EAAE;IACpC,IAAIY,QAAQ,EAAEb,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEW,QAAQ,CAAC;IACjDb,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEH,KAAK,CAACI,QAAQ,EAAE,CAAC;IAExC,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAM,GAAG,IAAI,CAACV,MAAM,6BAA6BM,MAAM,CAACG,QAAQ,EAAE,EAAE,CAAC,CACtFE,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDlB,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI,CAACK,2BAA2B,CAACf,KAAK,CAAC;IAChD,CAAC,CAAC,CACH;EACL;EAEA;EACAgB,kBAAkBA,CAACC,SAAiB,EAAEjB,KAAA,GAAgB,CAAC;IACrD,OAAO,IAAI,CAACN,IAAI,CAACW,GAAG,CAAM,GAAG,IAAI,CAACV,MAAM,4BAA4BsB,SAAS,UAAUjB,KAAK,EAAE,CAAC,CAC5FM,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDlB,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI,CAACQ,0BAA0B,CAAClB,KAAK,CAAC;IAC/C,CAAC,CAAC,CACH;EACL;EAEA;EACAmB,iBAAiBA,CAACpB,MAAc,EAAEC,KAAA,GAAgB,CAAC;IACjD,OAAO,IAAI,CAACN,IAAI,CAACW,GAAG,CAAM,GAAG,IAAI,CAACV,MAAM,2BAA2BI,MAAM,UAAUC,KAAK,EAAE,CAAC,CACxFM,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDlB,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,IAAI,CAACU,yBAAyB,CAACpB,KAAK,CAAC;IAC9C,CAAC,CAAC,CACH;EACL;EAEA;EACAqB,gBAAgBA,CAACJ,SAAiB,EAAEH,QAAgB,EAAEQ,QAAA,GAAmB,CAAC;IACxE,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAAC5B,MAAM,uBAAuB,EAAE;MAC3DsB,SAAS;MACTH,QAAQ;MACRQ,QAAQ;MACRE,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACnB,IAAI,CACLf,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEAgB,WAAWA,CAACC,KAAa,EAAEb,QAAiB,EAAEc,cAAA,GAAyB,CAAC;IACtE,OAAO,IAAI,CAAClC,IAAI,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAAC5B,MAAM,yBAAyB,EAAE;MAC7DgC,KAAK;MACLb,QAAQ;MACRc,cAAc;MACdJ,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACnB,IAAI,CACLf,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEAmB,aAAaA,CAACZ,SAAiB,EAAEH,QAAgB,EAAEgB,KAAa;IAC9D,OAAO,IAAI,CAACpC,IAAI,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAAC5B,MAAM,2BAA2B,EAAE;MAC/DsB,SAAS;MACTH,QAAQ;MACRgB,KAAK;MACLN,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACnB,IAAI,CACLf,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEA;EACAqB,gBAAgBA,CAAChC,MAAc;IAC7B,OAAO,IAAI,CAACL,IAAI,CAACW,GAAG,CAAM,GAAG,IAAI,CAACV,MAAM,mBAAmBI,MAAM,EAAE,CAAC,CACjEO,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACuB,mBAAmB,CAACjC,MAAM,CAAC,CAAC,EACpFR,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,CAAC,IAAI,CAACsB,mBAAmB,CAACjC,MAAM,CAAC,CAAC;IAC3C,CAAC,CAAC,CACH;EACL;EAEA;EACAkC,0BAA0BA,CAACnB,QAAgB,EAAEd,KAAA,GAAgB,CAAC;IAC5D,OAAO,IAAI,CAACN,IAAI,CAACW,GAAG,CAAM,GAAG,IAAI,CAACV,MAAM,6BAA6BmB,QAAQ,UAAUd,KAAK,EAAE,CAAC,CAC5FM,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDlB,UAAU,CAACmB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,OAAO,IAAI,CAACwB,2BAA2B,CAACpB,QAAQ,EAAEd,KAAK,CAAC;IAC1D,CAAC,CAAC,CACH;EACL;EAEA;EACQY,4BAA4BA,CAACZ,KAAa;IAChD;IACA,MAAMmC,YAAY,GAA4B,CAC5C;MACEC,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE,kDAAkD;MAC/DR,KAAK,EAAE,GAAG;MACVS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC/H9B,QAAQ,EAAE,KAAK;MACf+B,WAAW,EAAE,QAAQ;MACrBC,KAAK,EAAE,aAAa;MACpBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;MACtCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,IAAI;MACzBC,oBAAoB,EAAE;KACvB,CACF;IACD,OAAO,IAAIlE,UAAU,CAACmE,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAACrB,YAAY,CAACsB,KAAK,CAAC,CAAC,EAAEzD,KAAK,CAAC,CAAC;MAC3CuD,QAAQ,CAACG,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQ3C,2BAA2BA,CAACf,KAAa;IAC/C,MAAM2D,YAAY,GAAsB,CACtC;MACEvB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,4CAA4C;MACzDR,KAAK,EAAE,IAAI;MACXS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,cAAc;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC7H9B,QAAQ,EAAE,OAAO;MACjB+B,WAAW,EAAE,SAAS;MACtBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnCC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;MACrCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBQ,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,uBAAuB;MACvCC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE;KACjB,CACF;IACD,OAAO,IAAI7E,UAAU,CAACmE,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAACG,YAAY,CAACF,KAAK,CAAC,CAAC,EAAEzD,KAAK,CAAC,CAAC;MAC3CuD,QAAQ,CAACG,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQxC,0BAA0BA,CAAClB,KAAa;IAC9C,OAAO,IAAI,CAACY,4BAA4B,CAACZ,KAAK,CAAC;EACjD;EAEQoB,yBAAyBA,CAACpB,KAAa;IAC7C,OAAO,IAAI,CAACY,4BAA4B,CAACZ,KAAK,CAAC;EACjD;EAEQkC,2BAA2BA,CAACpB,QAAgB,EAAEd,KAAa;IACjE,OAAO,IAAI,CAACY,4BAA4B,CAACZ,KAAK,CAAC;EACjD;EAEQgC,mBAAmBA,CAACjC,MAAc;IACxC,OAAO;MACLA,MAAM;MACNmE,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MACjBC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC;MACpDC,UAAU,EAAE;QAAEC,GAAG,EAAE,GAAG;QAAEC,GAAG,EAAE;MAAI,CAAE;MACnCC,gBAAgB,EAAE;KACnB;EACH;EAEA;EACAC,mBAAmBA,CAACC,SAAwB;IAC1C,IAAI,CAAChF,cAAc,CAAC2D,IAAI,CAACqB,SAAS,CAAC;EACrC;EAEA;EACAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACjF,cAAc,CAACkF,YAAY,EAAE;EAC3C;;;uBA/NWvF,qBAAqB,EAAAwF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArB3F,qBAAqB;MAAA4F,OAAA,EAArB5F,qBAAqB,CAAA6F,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}