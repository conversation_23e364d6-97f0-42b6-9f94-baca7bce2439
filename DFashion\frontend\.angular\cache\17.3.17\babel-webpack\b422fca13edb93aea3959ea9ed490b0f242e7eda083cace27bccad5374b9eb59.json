{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class AdminDashboardComponent {\n  static {\n    this.ɵfac = function AdminDashboardComponent_Factory(t) {\n      return new (t || AdminDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashboardComponent,\n      selectors: [[\"app-admin-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"admin-container\"]],\n      template: function AdminDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Admin Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Admin functionality coming soon...\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [CommonModule],\n      styles: [\".admin-container[_ngcontent-%COMP%] {\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYWRtaW4vcGFnZXMvYWRtaW4tZGFzaGJvYXJkL2FkbWluLWRhc2hib2FyZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSxrQkFBQTtFQUNBLGtCQUFBO0FBQU4iLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAuYWRtaW4tY29udGFpbmVyIHtcbiAgICAgIHBhZGRpbmc6IDQwcHggMjBweDtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "AdminDashboardComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AdminDashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\admin\\pages\\admin-dashboard\\admin-dashboard.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-admin-dashboard',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"admin-container\">\n      <h2>Admin Dashboard</h2>\n      <p>Admin functionality coming soon...</p>\n    </div>\n  `,\n  styles: [`\n    .admin-container {\n      padding: 40px 20px;\n      text-align: center;\n    }\n  `]\n})\nexport class AdminDashboardComponent {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAmB9C,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAX9BP,EADF,CAAAS,cAAA,aAA6B,SACvB;UAAAT,EAAA,CAAAU,MAAA,sBAAe;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACxBX,EAAA,CAAAS,cAAA,QAAG;UAAAT,EAAA,CAAAU,MAAA,yCAAkC;UACvCV,EADuC,CAAAW,YAAA,EAAI,EACrC;;;qBALEhB,YAAY;MAAAiB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}