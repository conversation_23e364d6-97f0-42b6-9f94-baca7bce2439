{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/role-management.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction DynamicProfileComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function DynamicProfileComponent_button_4_Template_button_click_0_listener() {\n      const action_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleAction(action_r2.action));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r2 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background\", action_r2.color);\n    i0.ɵɵproperty(\"title\", action_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r2.icon);\n  }\n}\nfunction DynamicProfileComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 31);\n  }\n}\nfunction DynamicProfileComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DynamicProfileComponent_p_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.userProfile == null ? null : ctx_r2.userProfile.bio);\n  }\n}\nfunction DynamicProfileComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.userProfile == null ? null : ctx_r2.userProfile.location);\n  }\n}\nfunction DynamicProfileComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Joined \", i0.ɵɵpipeBind2(4, 1, ctx_r2.userProfile == null ? null : ctx_r2.userProfile.joinDate, \"MMM yyyy\"), \"\");\n  }\n}\nfunction DynamicProfileComponent_div_33_div_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"trend-\" + stat_r4.trend);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getTrendIcon(stat_r4.trend));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r4.trendValue);\n  }\n}\nfunction DynamicProfileComponent_div_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"div\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 44);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DynamicProfileComponent_div_33_div_2_div_8_Template, 4, 5, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r4 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r4.color);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", stat_r4.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(stat_r4.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r4.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r4.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", stat_r4.trend);\n  }\n}\nfunction DynamicProfileComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵtemplate(2, DynamicProfileComponent_div_33_div_2_Template, 9, 9, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getProfileStats());\n  }\n}\nfunction DynamicProfileComponent_div_35_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"div\", 57);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const initiative_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(initiative_r5.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(initiative_r5.name);\n  }\n}\nfunction DynamicProfileComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48)(2, \"div\", 49)(3, \"h3\");\n    i0.ɵɵtext(4, \"Leadership Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"div\", 51)(7, \"span\", 52);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 53);\n    i0.ɵɵtext(10, \"Team Members\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 51)(12, \"span\", 52);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 53);\n    i0.ɵɵtext(15, \"Departments\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 49)(17, \"h3\");\n    i0.ɵɵtext(18, \"Strategic Initiatives\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 54);\n    i0.ɵɵtemplate(20, DynamicProfileComponent_div_35_div_20_Template, 4, 3, \"div\", 55);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.getTeamSize());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getDepartmentCount());\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStrategicInitiatives());\n  }\n}\nfunction DynamicProfileComponent_div_36_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"img\", 67);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 68);\n    i0.ɵɵelement(6, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const member_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", member_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", member_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(member_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", member_r6.performance, \"%\");\n  }\n}\nfunction DynamicProfileComponent_div_36_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 72)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 73)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const goal_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background\", ctx_r2.getProgressColor(goal_r7.progress));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", goal_r7.progress, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(goal_r7.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r7.description);\n  }\n}\nfunction DynamicProfileComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60)(3, \"h3\");\n    i0.ɵɵtext(4, \"Team Performance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61);\n    i0.ɵɵtemplate(6, DynamicProfileComponent_div_36_div_6_Template, 7, 5, \"div\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 60)(8, \"h3\");\n    i0.ɵɵtext(9, \"Department Goals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 63);\n    i0.ɵɵtemplate(11, DynamicProfileComponent_div_36_div_11_Template, 10, 5, \"div\", 64);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getTeamMembers());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getDepartmentGoals());\n  }\n}\nfunction DynamicProfileComponent_div_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const metric_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", metric_r8.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r8.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r8.label);\n  }\n}\nfunction DynamicProfileComponent_div_37_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 85)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 86);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const achievement_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", achievement_r9.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(achievement_r9.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(achievement_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(achievement_r9.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 7, achievement_r9.date, \"short\"));\n  }\n}\nfunction DynamicProfileComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\", 76)(3, \"h3\");\n    i0.ɵɵtext(4, \"My Performance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 77);\n    i0.ɵɵtemplate(6, DynamicProfileComponent_div_37_div_6_Template, 6, 4, \"div\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 76)(8, \"h3\");\n    i0.ɵɵtext(9, \"Recent Achievements\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 79);\n    i0.ɵɵtemplate(11, DynamicProfileComponent_div_37_div_11_Template, 11, 10, \"div\", 80);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getPersonalMetrics());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getRecentAchievements());\n  }\n}\nfunction DynamicProfileComponent_div_38_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 96)(4, \"span\", 52);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const metric_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", metric_r10.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(metric_r10.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(metric_r10.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r10.label);\n  }\n}\nfunction DynamicProfileComponent_div_38_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 99)(5, \"span\", 100);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 101);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 3, activity_r11.time, \"short\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(activity_r11.user);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r11.action);\n  }\n}\nfunction DynamicProfileComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"div\", 89)(3, \"h3\");\n    i0.ɵɵtext(4, \"System Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 90);\n    i0.ɵɵtemplate(6, DynamicProfileComponent_div_38_div_6_Template, 8, 6, \"div\", 91);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 89)(8, \"h3\");\n    i0.ɵɵtext(9, \"Recent Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 92);\n    i0.ɵɵtemplate(11, DynamicProfileComponent_div_38_div_11_Template, 9, 6, \"div\", 93);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSystemMetrics());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getRecentActivities());\n  }\n}\nexport let DynamicProfileComponent = /*#__PURE__*/(() => {\n  class DynamicProfileComponent {\n    constructor(roleManagementService, router) {\n      this.roleManagementService = roleManagementService;\n      this.router = router;\n      this.userProfile = null;\n      this.roleConfig = null;\n      this.profileLayout = 'specialist';\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      if (this.userProfile?.role) {\n        this.roleConfig = this.roleManagementService.getRoleConfig(this.userProfile.role);\n        this.profileLayout = this.roleConfig.profileLayout;\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    getCoverGradient() {\n      if (!this.roleConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n      const color = this.roleConfig.color;\n      return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n    }\n    getProfileActions() {\n      if (!this.userProfile?.role) return [];\n      const baseActions = [{\n        label: 'Edit Profile',\n        icon: 'fas fa-edit',\n        action: 'edit',\n        color: '#4ECDC4'\n      }, {\n        label: 'Settings',\n        icon: 'fas fa-cog',\n        action: 'settings',\n        color: '#45B7D1'\n      }];\n      // Add role-specific actions\n      if (this.roleManagementService.isManager(this.userProfile.role)) {\n        baseActions.push({\n          label: 'Team Management',\n          icon: 'fas fa-users',\n          action: 'team',\n          color: '#96CEB4'\n        }, {\n          label: 'Reports',\n          icon: 'fas fa-chart-bar',\n          action: 'reports',\n          color: '#FFEAA7'\n        });\n      }\n      if (this.userProfile.role === 'super_admin' || this.userProfile.role === 'admin') {\n        baseActions.push({\n          label: 'Admin Panel',\n          icon: 'fas fa-shield-alt',\n          action: 'admin',\n          color: '#FF6B6B'\n        });\n      }\n      return baseActions;\n    }\n    getProfileStats() {\n      // This would be populated based on role and real data\n      // For now, returning mock data based on role\n      if (!this.userProfile?.role) return [];\n      const role = this.userProfile.role;\n      if (role.includes('sales')) {\n        return [{\n          label: 'Sales This Month',\n          value: '₹2.4M',\n          icon: 'fas fa-rupee-sign',\n          color: '#45B7D1',\n          trend: 'up',\n          trendValue: '+12%'\n        }, {\n          label: 'Deals Closed',\n          value: 47,\n          icon: 'fas fa-handshake',\n          color: '#96CEB4',\n          trend: 'up',\n          trendValue: '+8%'\n        }, {\n          label: 'Target Achievement',\n          value: '94%',\n          icon: 'fas fa-target',\n          color: '#FFEAA7',\n          trend: 'stable'\n        }];\n      }\n      if (role.includes('marketing')) {\n        return [{\n          label: 'Campaign Reach',\n          value: '1.2M',\n          icon: 'fas fa-eye',\n          color: '#F38BA8',\n          trend: 'up',\n          trendValue: '+15%'\n        }, {\n          label: 'Engagement Rate',\n          value: '4.8%',\n          icon: 'fas fa-heart',\n          color: '#DDA0DD',\n          trend: 'up',\n          trendValue: '+0.3%'\n        }, {\n          label: 'Conversions',\n          value: 2847,\n          icon: 'fas fa-exchange-alt',\n          color: '#FFB6C1',\n          trend: 'down',\n          trendValue: '-2%'\n        }];\n      }\n      // Default stats for other roles\n      return [{\n        label: 'Tasks Completed',\n        value: 156,\n        icon: 'fas fa-check-circle',\n        color: '#6BCF7F',\n        trend: 'up',\n        trendValue: '+5%'\n      }, {\n        label: 'Projects Active',\n        value: 8,\n        icon: 'fas fa-project-diagram',\n        color: '#4ECDC4',\n        trend: 'stable'\n      }];\n    }\n    handleAction(action) {\n      switch (action) {\n        case 'edit':\n          this.router.navigate(['/profile/edit']);\n          break;\n        case 'settings':\n          this.router.navigate(['/profile/settings']);\n          break;\n        case 'team':\n          this.router.navigate(['/team/management']);\n          break;\n        case 'reports':\n          this.router.navigate(['/reports']);\n          break;\n        case 'admin':\n          this.router.navigate(['/admin']);\n          break;\n      }\n    }\n    getLastActiveText() {\n      if (!this.userProfile?.lastActive) return 'Unknown';\n      const now = new Date();\n      const lastActive = new Date(this.userProfile.lastActive);\n      const diffMs = now.getTime() - lastActive.getTime();\n      const diffMins = Math.floor(diffMs / 60000);\n      if (diffMins < 1) return 'Just now';\n      if (diffMins < 60) return `${diffMins}m ago`;\n      if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;\n      return `${Math.floor(diffMins / 1440)}d ago`;\n    }\n    getTrendIcon(trend) {\n      switch (trend) {\n        case 'up':\n          return 'fas fa-arrow-up';\n        case 'down':\n          return 'fas fa-arrow-down';\n        default:\n          return 'fas fa-minus';\n      }\n    }\n    // Mock data methods - these would be replaced with real data services\n    getTeamSize() {\n      return 12;\n    }\n    getDepartmentCount() {\n      return 3;\n    }\n    getStrategicInitiatives() {\n      return [];\n    }\n    getTeamMembers() {\n      return [];\n    }\n    getDepartmentGoals() {\n      return [];\n    }\n    getPersonalMetrics() {\n      return [];\n    }\n    getRecentAchievements() {\n      return [];\n    }\n    getSystemMetrics() {\n      return [];\n    }\n    getRecentActivities() {\n      return [];\n    }\n    getProgressColor(progress) {\n      return progress > 75 ? '#6BCF7F' : progress > 50 ? '#FFEAA7' : '#FF6B6B';\n    }\n    static {\n      this.ɵfac = function DynamicProfileComponent_Factory(t) {\n        return new (t || DynamicProfileComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DynamicProfileComponent,\n        selectors: [[\"app-dynamic-profile\"]],\n        inputs: {\n          userProfile: \"userProfile\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 39,\n        vars: 30,\n        consts: [[1, \"dynamic-profile\"], [1, \"profile-header\"], [1, \"profile-cover\"], [1, \"profile-actions\"], [\"class\", \"action-btn\", 3, \"background\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-info\"], [1, \"avatar-section\"], [1, \"avatar-container\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [\"class\", \"online-indicator\", 4, \"ngIf\"], [1, \"role-badge\"], [1, \"profile-details\"], [1, \"name-section\"], [1, \"profile-name\"], [\"class\", \"verification-badge\", 4, \"ngIf\"], [1, \"profile-username\"], [1, \"role-info\"], [1, \"role-title\"], [1, \"department\"], [\"class\", \"profile-bio\", 4, \"ngIf\"], [1, \"profile-meta\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [1, \"meta-item\"], [1, \"fas\", \"fa-clock\"], [\"class\", \"profile-stats\", 4, \"ngIf\"], [1, \"role-content\"], [\"class\", \"executive-layout\", 4, \"ngIf\"], [\"class\", \"manager-layout\", 4, \"ngIf\"], [\"class\", \"specialist-layout\", 4, \"ngIf\"], [\"class\", \"admin-layout\", 4, \"ngIf\"], [1, \"action-btn\", 3, \"click\", \"title\"], [1, \"online-indicator\"], [1, \"verification-badge\"], [1, \"fas\", \"fa-check-circle\"], [1, \"profile-bio\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"fas\", \"fa-calendar-alt\"], [1, \"profile-stats\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [\"class\", \"stat-trend\", 3, \"class\", 4, \"ngIf\"], [1, \"stat-trend\"], [1, \"executive-layout\"], [1, \"executive-grid\"], [1, \"executive-card\"], [1, \"leadership-metrics\"], [1, \"metric\"], [1, \"metric-value\"], [1, \"metric-label\"], [1, \"initiatives-list\"], [\"class\", \"initiative-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"initiative-item\"], [1, \"initiative-status\"], [1, \"manager-layout\"], [1, \"manager-grid\"], [1, \"manager-card\"], [1, \"performance-chart\"], [\"class\", \"performance-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"goals-list\"], [\"class\", \"goal-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"performance-item\"], [1, \"member-info\"], [1, \"member-avatar\", 3, \"src\", \"alt\"], [1, \"performance-bar\"], [1, \"progress\"], [1, \"goal-item\"], [1, \"goal-progress\"], [1, \"progress-circle\"], [1, \"goal-details\"], [1, \"specialist-layout\"], [1, \"specialist-grid\"], [1, \"specialist-card\"], [1, \"performance-metrics\"], [\"class\", \"metric-circle\", 4, \"ngFor\", \"ngForOf\"], [1, \"achievements-list\"], [\"class\", \"achievement-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"metric-circle\"], [1, \"circle-progress\"], [1, \"achievement-item\"], [1, \"achievement-icon\"], [1, \"achievement-details\"], [1, \"achievement-date\"], [1, \"admin-layout\"], [1, \"admin-grid\"], [1, \"admin-card\"], [1, \"system-stats\"], [\"class\", \"system-metric\", 4, \"ngFor\", \"ngForOf\"], [1, \"activities-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"system-metric\"], [1, \"metric-icon\"], [1, \"metric-data\"], [1, \"activity-item\"], [1, \"activity-time\"], [1, \"activity-content\"], [1, \"activity-user\"], [1, \"activity-action\"]],\n        template: function DynamicProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵtemplate(4, DynamicProfileComponent_button_4_Template, 2, 5, \"button\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n            i0.ɵɵelement(8, \"img\", 8);\n            i0.ɵɵtemplate(9, DynamicProfileComponent_div_9_Template, 1, 0, \"div\", 9);\n            i0.ɵɵelementStart(10, \"div\", 10);\n            i0.ɵɵelement(11, \"i\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"h1\", 13);\n            i0.ɵɵtext(15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(16, DynamicProfileComponent_div_16_Template, 2, 0, \"div\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"p\", 15);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 16)(20, \"span\", 17);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"span\", 18);\n            i0.ɵɵtext(23);\n            i0.ɵɵpipe(24, \"titlecase\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(25, DynamicProfileComponent_p_25_Template, 2, 1, \"p\", 19);\n            i0.ɵɵelementStart(26, \"div\", 20);\n            i0.ɵɵtemplate(27, DynamicProfileComponent_div_27_Template, 4, 1, \"div\", 21)(28, DynamicProfileComponent_div_28_Template, 5, 4, \"div\", 21);\n            i0.ɵɵelementStart(29, \"div\", 22);\n            i0.ɵɵelement(30, \"i\", 23);\n            i0.ɵɵelementStart(31, \"span\");\n            i0.ɵɵtext(32);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵtemplate(33, DynamicProfileComponent_div_33_Template, 3, 1, \"div\", 24);\n            i0.ɵɵelementStart(34, \"div\", 25);\n            i0.ɵɵtemplate(35, DynamicProfileComponent_div_35_Template, 21, 3, \"div\", 26)(36, DynamicProfileComponent_div_36_Template, 12, 2, \"div\", 27)(37, DynamicProfileComponent_div_37_Template, 12, 2, \"div\", 28)(38, DynamicProfileComponent_div_38_Template, 12, 2, \"div\", 29);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"data-layout\", ctx.profileLayout)(\"data-role\", ctx.userProfile == null ? null : ctx.userProfile.role);\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"background\", ctx.getCoverGradient());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getProfileActions());\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"src\", (ctx.userProfile == null ? null : ctx.userProfile.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.userProfile == null ? null : ctx.userProfile.fullName);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.isOnline);\n            i0.ɵɵadvance();\n            i0.ɵɵstyleProp(\"background\", ctx.roleConfig == null ? null : ctx.roleConfig.color);\n            i0.ɵɵadvance();\n            i0.ɵɵclassMap(ctx.roleConfig == null ? null : ctx.roleConfig.icon);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.userProfile == null ? null : ctx.userProfile.fullName);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.isVerified);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"@\", (ctx.userProfile == null ? null : ctx.userProfile.username) || \"username\", \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"color\", ctx.roleConfig == null ? null : ctx.roleConfig.color);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.roleConfig == null ? null : ctx.roleConfig.displayName, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 28, ctx.roleConfig == null ? null : ctx.roleConfig.department));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.bio);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.location);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.userProfile == null ? null : ctx.userProfile.joinDate);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\"Last active \", ctx.getLastActiveText(), \"\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getProfileStats().length > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"executive\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"manager\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"specialist\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.profileLayout === \"admin\");\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.TitleCasePipe, i3.DatePipe],\n        styles: [\".dynamic-profile[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0;background:#fafafa;min-height:100vh}.dynamic-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]{position:relative;background:#fff;border-radius:0 0 24px 24px;overflow:hidden;box-shadow:0 4px 20px #00000014;margin-bottom:2rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-cover[_ngcontent-%COMP%]{height:200px;position:relative;display:flex;align-items:flex-end;justify-content:flex-end;padding:1rem}@media (max-width: 768px){.dynamic-profile[_ngcontent-%COMP%]   .profile-cover[_ngcontent-%COMP%]{height:150px}}.dynamic-profile[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem;z-index:2}.dynamic-profile[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:none;color:#fff;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.dynamic-profile[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);box-shadow:0 4px 12px #0003}.dynamic-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]{padding:0 2rem 2rem;margin-top:-60px;position:relative;z-index:3}@media (max-width: 768px){.dynamic-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]{padding:0 1rem 1.5rem;margin-top:-40px}}.dynamic-profile[_ngcontent-%COMP%]   .avatar-section[_ngcontent-%COMP%]{display:flex;gap:1.5rem;align-items:flex-start}@media (max-width: 768px){.dynamic-profile[_ngcontent-%COMP%]   .avatar-section[_ngcontent-%COMP%]{flex-direction:column;align-items:center;text-align:center;gap:1rem}}.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]{position:relative;flex-shrink:0}.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:120px;height:120px;border-radius:50%;border:4px solid white;box-shadow:0 8px 24px #00000026;object-fit:cover}@media (max-width: 768px){.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:80px;height:80px}}.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:8px;right:8px;width:20px;height:20px;background:#4caf50;border:3px solid white;border-radius:50%;animation:_ngcontent-%COMP%_pulse 2s infinite}.dynamic-profile[_ngcontent-%COMP%]   .avatar-container[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%]{position:absolute;top:-5px;right:-5px;width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:.8rem;border:2px solid white;box-shadow:0 2px 8px #0003}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]{flex:1;min-width:0}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.25rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#262626;margin:0;line-height:1.2}@media (max-width: 768px){.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{font-size:1.5rem}}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .name-section[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%]{color:#1da1f2;font-size:1.2rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%]{color:#8e8e8e;font-size:1rem;margin:0 0 .5rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .role-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .role-info[_ngcontent-%COMP%]   .role-title[_ngcontent-%COMP%]{font-weight:600;font-size:.95rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .role-info[_ngcontent-%COMP%]   .department[_ngcontent-%COMP%]{background:#f0f0f0;color:#666;padding:.25rem .75rem;border-radius:12px;font-size:.8rem;text-transform:uppercase;letter-spacing:.5px}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-bio[_ngcontent-%COMP%]{color:#262626;line-height:1.5;margin:0 0 1rem;font-size:.95rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:1rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#8e8e8e;font-size:.85rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:14px;text-align:center}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]{margin-bottom:2rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem;padding:0 1rem}@media (max-width: 768px){.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1.5rem;display:flex;align-items:center;gap:1rem;box-shadow:0 2px 12px #0000000f;border-left:4px solid;transition:transform .2s ease}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{font-size:1.5rem;width:48px;height:48px;display:flex;align-items:center;justify-content:center;background:#0000000d;border-radius:12px}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]{flex:1}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:700;color:#262626;line-height:1}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.85rem;margin-top:.25rem}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;margin-top:.5rem;font-size:.8rem;font-weight:600}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend.trend-up[_ngcontent-%COMP%]{color:#4caf50}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend.trend-down[_ngcontent-%COMP%]{color:#f44336}.dynamic-profile[_ngcontent-%COMP%]   .profile-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-trend.trend-stable[_ngcontent-%COMP%]{color:#ff9800}.dynamic-profile[_ngcontent-%COMP%]   .role-content[_ngcontent-%COMP%]{padding:0 1rem}.dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .executive-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .manager-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .specialist-grid[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .admin-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem}.dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1.5rem;box-shadow:0 2px 12px #0000000f}.dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-layout[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 1rem;color:#262626;font-size:1.1rem;font-weight:600}.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:space-around}.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]{text-align:center}.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%]{display:block;font-size:1.5rem;font-weight:700;color:#262626}.dynamic-profile[_ngcontent-%COMP%]   .leadership-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .performance-metrics[_ngcontent-%COMP%]   .metric[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%]{font-size:.8rem;color:#8e8e8e;margin-top:.25rem}.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;margin-bottom:1rem}.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .member-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;min-width:120px}.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .member-info[_ngcontent-%COMP%]   .member-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;object-fit:cover}.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .performance-bar[_ngcontent-%COMP%]{flex:1;height:8px;background:#f0f0f0;border-radius:4px;overflow:hidden}.dynamic-profile[_ngcontent-%COMP%]   .performance-item[_ngcontent-%COMP%]   .performance-bar[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#4caf50,#8bc34a);transition:width .3s ease}.dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:.75rem 0;border-bottom:1px solid #f0f0f0}.dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .goals-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .achievements-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .goal-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .achievement-item[_ngcontent-%COMP%]:last-child, .dynamic-profile[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.dynamic-profile[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-weight:600;font-size:.8rem}.dynamic-profile[_ngcontent-%COMP%]   .achievement-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff}.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:.75rem 0}.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]{font-size:1.2rem}.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%]   .metric-data[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%]{display:block;font-weight:600;color:#262626}.dynamic-profile[_ngcontent-%COMP%]   .system-stats[_ngcontent-%COMP%]   .system-metric[_ngcontent-%COMP%]   .metric-data[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%]{font-size:.8rem;color:#8e8e8e}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #4caf50b3}70%{box-shadow:0 0 0 10px #4caf5000}to{box-shadow:0 0 #4caf5000}}@media (prefers-color-scheme: dark){.dynamic-profile[_ngcontent-%COMP%]{background:#121212}.dynamic-profile[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .executive-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .manager-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .specialist-card[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .admin-card[_ngcontent-%COMP%]{background:#1e1e1e;color:#fff}.dynamic-profile[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{color:#fff}.dynamic-profile[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%], .dynamic-profile[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{color:#b3b3b3}}\"]\n      });\n    }\n  }\n  return DynamicProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}