{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"h2\");\n    i0.ɵɵtext(4, \"Allow Camera & File Access?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"To add a story, we need access to your camera and files. This is required to take a photo/video or select from your gallery.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handlePermissionResponse(false));\n    });\n    i0.ɵɵtext(9, \"Block\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handlePermissionResponse(true));\n    });\n    i0.ɵɵtext(11, \"Allow\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"h2\");\n    i0.ɵɵtext(4, \"Add Story\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 11)(6, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openCamera());\n    });\n    i0.ɵɵtext(7, \"Open Camera\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openGallery());\n    });\n    i0.ɵɵtext(9, \"Choose from Gallery\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"h2\");\n    i0.ɵɵtext(4, \"Permission Denied\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"You blocked camera and file access. You cannot add a story unless you allow access.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.permissionDenied = false);\n    });\n    i0.ɵɵtext(9, \"Close\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_div_1_div_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeAddStoryModal());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"h2\");\n    i0.ɵɵtext(4, \"Add Story\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function ViewAddStoriesComponent_div_4_div_1_div_2_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.submitNewStory());\n    });\n    i0.ɵɵelementStart(6, \"input\", 20);\n    i0.ɵɵlistener(\"change\", function ViewAddStoriesComponent_div_4_div_1_div_2_Template_input_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onStoryFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ViewAddStoriesComponent_div_4_div_1_div_2_Template_textarea_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newStoryCaption, $event) || (ctx_r1.newStoryCaption = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_div_1_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeAddStoryModal());\n    });\n    i0.ɵɵtext(10, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 23);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newStoryCaption);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newStoryFile || ctx_r1.isUploadingStory);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isUploadingStory ? \"Uploading...\" : \"Post Story\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵtemplate(2, ViewAddStoriesComponent_div_4_div_1_div_2_Template, 13, 6, \"div\", 2);\n    i0.ɵɵelement(3, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showAddStoryModal);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_4_div_1_Template, 4, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction ViewAddStoriesComponent_div_5_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_5_div_7_Template_div_click_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26);\n    i0.ɵɵelement(2, \"div\", 32)(3, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r9.user.avatar + \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(story_r9.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_5_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddStory());\n    });\n    i0.ɵɵelementStart(2, \"div\", 26)(3, \"div\", 27);\n    i0.ɵɵelement(4, \"i\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵtext(6, \"Add Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, ViewAddStoriesComponent_div_5_div_7_Template, 6, 3, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r11 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r11 === ctx_r1.currentIndex)(\"completed\", i_r11 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_6_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_6_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_6_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_6_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_6_div_21_div_1_Template_div_click_0_listener() {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r13));\n    });\n    i0.ɵɵelementStart(1, \"div\", 76);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 77)(4, \"div\", 78);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 79);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r13.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_6_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_6_div_21_div_1_Template, 8, 2, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35, 0)(3, \"div\", 36);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_6_div_4_Template, 2, 4, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_6_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_6_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_6_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_6_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 39)(7, \"div\", 40);\n    i0.ɵɵelement(8, \"div\", 41);\n    i0.ɵɵelementStart(9, \"div\", 42);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 43);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 44);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_6_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 47);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_6_video_18_Template, 1, 1, \"video\", 48)(19, ViewAddStoriesComponent_div_6_div_19_Template, 1, 2, \"div\", 49)(20, ViewAddStoriesComponent_div_6_div_20_Template, 2, 1, \"div\", 50)(21, ViewAddStoriesComponent_div_6_div_21_Template, 2, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 52)(23, \"div\", 53)(24, \"button\", 54);\n    i0.ɵɵelement(25, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 56);\n    i0.ɵɵelement(27, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 58);\n    i0.ɵɵelement(29, \"i\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 60)(31, \"button\", 61);\n    i0.ɵɵelement(32, \"i\", 62);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"button\", 63);\n    i0.ɵɵelement(36, \"i\", 55);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"button\", 64);\n    i0.ɵɵelement(40, \"i\", 28);\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Add to Cart\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(43, \"div\", 65)(44, \"div\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(45, \"div\", 67, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(24);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81);\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 83)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 84);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    // Handler for Add Story button\n    // Modal state\n    this.showAddStoryModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Try to load from API first\n    this.subscriptions.push(this.http.get(`http://localhost:3000/api/stories/active`).subscribe({\n      next: response => {\n        if (response.success && response.data) {\n          this.stories = response.data;\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [{\n      _id: 'story-1',\n      user: {\n        _id: 'user-1',\n        username: 'ai_fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n      mediaType: 'image',\n      caption: 'Sustainable fashion is the future! 🌱✨',\n      createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n      views: 1247,\n      isActive: true,\n      products: [{\n        _id: 'prod-1',\n        name: 'Eco-Friendly Summer Dress',\n        price: 2499,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n      }]\n    }, {\n      _id: 'story-2',\n      user: {\n        _id: 'user-2',\n        username: 'ai_stylist_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n      mediaType: 'image',\n      caption: 'Street style essentials for the modern man 🔥',\n      createdAt: new Date(Date.now() - 12 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18.8 * 60 * 60 * 1000).toISOString(),\n      views: 892,\n      isActive: true,\n      products: [{\n        _id: 'prod-2',\n        name: 'Urban Cotton T-Shirt',\n        price: 899,\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'\n      }]\n    }, {\n      _id: 'story-3',\n      user: {\n        _id: 'user-3',\n        username: 'ai_trendsetter_zara',\n        fullName: 'Zara Patel',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n      mediaType: 'image',\n      caption: 'Ethnic fusion at its finest! Traditional meets modern ✨',\n      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 21 * 60 * 60 * 1000).toISOString(),\n      views: 2156,\n      isActive: true,\n      products: [{\n        _id: 'prod-3',\n        name: 'Designer Ethnic Kurti',\n        price: 1899,\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=200'\n      }]\n    }, {\n      _id: 'story-4',\n      user: {\n        _id: 'user-4',\n        username: 'ai_minimalist_kai',\n        fullName: 'Kai Thompson',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',\n      mediaType: 'image',\n      caption: 'Less is more. Minimalist wardrobe essentials 🌿',\n      createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n      views: 1543,\n      isActive: true,\n      products: [{\n        _id: 'prod-4',\n        name: 'Classic Denim Jeans',\n        price: 2999,\n        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=200'\n      }]\n    }, {\n      _id: 'story-5',\n      user: {\n        _id: 'user-5',\n        username: 'ai_glamour_sophia',\n        fullName: 'Sophia Williams',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n      mediaType: 'image',\n      caption: 'Luxury accessories for the discerning fashionista 💎',\n      createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),\n      views: 3421,\n      isActive: true,\n      products: [{\n        _id: 'prod-5',\n        name: 'Luxury Leather Handbag',\n        price: 4999,\n        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200'\n      }]\n    }];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    // Left third goes back, right two-thirds go forward\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      // Snap back to current position\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    // Simple easing\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    // Check if animation is complete\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    // Update transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {\n    // Additional event listeners can be added here\n  }\n  removeEventListeners() {\n    // Clean up event listeners\n  }\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAddStory() {\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.newStoryFile) return;\n      _this.isUploadingStory = true;\n      try {\n        // 1. Upload the file to /api/stories/upload\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this.newStoryFile);\n        const uploadRes = yield _this.http.post('/api/stories/upload', uploadForm).toPromise();\n        // 2. Post the story with the returned URL\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this.newStoryCaption\n        };\n        yield _this.http.post('/api/stories', storyPayload).toPromise();\n        _this.showAddStoryModal = false;\n        _this.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 7,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [\"class\", \"add-story-modal\", 4, \"ngIf\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-slider\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"add-story-modal\"], [1, \"add-story-modal-backdrop\"], [1, \"add-story-modal-content\"], [1, \"add-story-modal-actions\"], [3, \"click\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"add-story-modal-backdrop\", 3, \"click\"], [\"autocomplete\", \"off\", 3, \"ngSubmit\"], [\"id\", \"story-file-input\", \"type\", \"file\", \"accept\", \"image/*,video/*\", \"required\", \"\", 2, \"display\", \"none\", 3, \"change\", \"disabled\"], [\"placeholder\", \"Write a caption...\", \"name\", \"caption\", \"rows\", \"2\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"button\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 3, \"disabled\"], [1, \"stories-slider\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-story-avatar\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-ring\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\"], [1, \"ecommerce-btn\", \"cart-btn\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ViewAddStoriesComponent_div_0_Template, 12, 0, \"div\", 2)(1, ViewAddStoriesComponent_div_1_Template, 10, 0, \"div\", 2)(2, ViewAddStoriesComponent_div_2_Template, 10, 0, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_4_Template, 2, 2, \"div\", 4)(5, ViewAddStoriesComponent_div_5_Template, 8, 1, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, ViewAddStoriesComponent_div_6_Template, 47, 15, \"div\", 6)(7, ViewAddStoriesComponent_div_7_Template, 9, 0, \"div\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showPermissionModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCameraOrGallery);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.permissionDenied);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, FormsModule, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.NgModel, i4.NgForm],\n      styles: [\".add-story-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.5);\\n  z-index: 10000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.add-story-modal-backdrop[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.5);\\n  z-index: 1;\\n}\\n\\n.add-story-modal-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  background: #fff;\\n  border-radius: 16px;\\n  padding: 32px 24px 24px 24px;\\n  min-width: 320px;\\n  max-width: 90vw;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.add-story-modal-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 16px;\\n  font-size: 1.3rem;\\n  font-weight: 700;\\n  color: #222;\\n}\\n\\n.add-story-modal-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-size: 1rem;\\n  margin-bottom: 20px;\\n  text-align: center;\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-top: 16px;\\n  justify-content: center;\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 10px 24px;\\n  border-radius: 8px;\\n  border: none;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  background: linear-gradient(90deg, #f09433 0%, #e6683c 50%, #dc2743 100%);\\n  color: #fff;\\n  cursor: pointer;\\n  transition: background 0.2s, transform 0.2s;\\n  box-shadow: 0 2px 8px rgba(220, 39, 67, 0.08);\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(90deg, #dc2743 0%, #e6683c 100%);\\n  transform: translateY(-2px) scale(1.04);\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #dbdbdb;\\n  padding: 16px 0;\\n  margin-bottom: 0;\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.stories-slider[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: transform 0.2s ease;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ViewAddStoriesComponent_div_0_Template_button_click_8_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "handlePermissionResponse", "ViewAddStoriesComponent_div_0_Template_button_click_10_listener", "ViewAddStoriesComponent_div_1_Template_button_click_6_listener", "_r3", "openCamera", "ViewAddStoriesComponent_div_1_Template_button_click_8_listener", "openGallery", "ViewAddStoriesComponent_div_2_Template_button_click_8_listener", "_r4", "permissionDenied", "ViewAddStoriesComponent_div_4_div_1_div_2_Template_div_click_1_listener", "_r5", "closeAddStoryModal", "ViewAddStoriesComponent_div_4_div_1_div_2_Template_form_ngSubmit_5_listener", "submitNewStory", "ViewAddStoriesComponent_div_4_div_1_div_2_Template_input_change_6_listener", "$event", "onStoryFileSelected", "ɵɵtwoWayListener", "ViewAddStoriesComponent_div_4_div_1_div_2_Template_textarea_ngModelChange_7_listener", "ɵɵtwoWayBindingSet", "newStoryCaption", "ViewAddStoriesComponent_div_4_div_1_div_2_Template_button_click_9_listener", "ɵɵadvance", "ɵɵproperty", "isUploadingStory", "ɵɵtwoWayProperty", "newStoryFile", "ɵɵtextInterpolate1", "ɵɵtemplate", "ViewAddStoriesComponent_div_4_div_1_div_2_Template", "showAddStoryModal", "ViewAddStoriesComponent_div_4_div_1_Template", "ɵɵpureFunction0", "_c2", "ViewAddStoriesComponent_div_5_div_7_Template_div_click_0_listener", "i_r8", "_r7", "index", "openStories", "ɵɵstyleProp", "story_r9", "user", "avatar", "ɵɵtextInterpolate", "username", "ViewAddStoriesComponent_div_5_Template_div_click_1_listener", "_r6", "onAddStory", "ViewAddStoriesComponent_div_5_div_7_Template", "stories", "ɵɵclassProp", "i_r11", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "caption", "ViewAddStoriesComponent_div_6_div_21_div_1_Template_div_click_0_listener", "product_r13", "_r12", "$implicit", "viewProduct", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_6_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_6_div_4_Template", "ViewAddStoriesComponent_div_6_Template_div_click_5_listener", "_r10", "onStoryClick", "ViewAddStoriesComponent_div_6_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_6_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_6_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_6_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_6_video_18_Template", "ViewAddStoriesComponent_div_6_div_19_Template", "ViewAddStoriesComponent_div_6_div_20_Template", "ViewAddStoriesComponent_div_6_div_21_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "showPermissionModal", "showCameraOrGallery", "ngOnInit", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "push", "get", "subscribe", "next", "response", "success", "data", "loadFallbackStories", "error", "console", "_id", "Date", "now", "toISOString", "expiresAt", "isActive", "products", "image", "showStory", "document", "body", "style", "overflow", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "clickX", "clientX", "windowWidth", "window", "innerWidth", "touches", "updateDragPosition", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getStoryProgress", "dateString", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "num", "toFixed", "toString", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "product", "navigate", "story", "allow", "input", "getElementById", "setAttribute", "click", "removeAttribute", "file", "target", "files", "_this", "_asyncToGenerator", "uploadForm", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "storyPayload", "media", "type", "url", "err", "alert", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_0_Template", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "ViewAddStoriesComponent_div_4_Template", "ViewAddStoriesComponent_div_5_Template", "ViewAddStoriesComponent_div_6_Template", "ViewAddStoriesComponent_div_7_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "NgForm", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild, HostListener\n \n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n// import { environment } from '../../../../environments/environment';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Try to load from API first\n    this.subscriptions.push(\n      this.http.get<any>(`http://localhost:3000/api/stories/active`).subscribe({\n        next: (response) => {\n          if (response.success && response.data) {\n            this.stories = response.data;\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [\n      {\n        _id: 'story-1',\n        user: {\n          _id: 'user-1',\n          username: 'ai_fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        mediaType: 'image',\n        caption: 'Sustainable fashion is the future! 🌱✨',\n        createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago\n        expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(), // 19 hours from now\n        views: 1247,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-1',\n            name: 'Eco-Friendly Summer Dress',\n            price: 2499,\n            image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-2',\n        user: {\n          _id: 'user-2',\n          username: 'ai_stylist_alex',\n          fullName: 'Alex Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        mediaType: 'image',\n        caption: 'Street style essentials for the modern man 🔥',\n        createdAt: new Date(Date.now() - 12 * 60 * 1000).toISOString(), // 12 minutes ago\n        expiresAt: new Date(Date.now() + 18.8 * 60 * 60 * 1000).toISOString(),\n        views: 892,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-2',\n            name: 'Urban Cotton T-Shirt',\n            price: 899,\n            image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-3',\n        user: {\n          _id: 'user-3',\n          username: 'ai_trendsetter_zara',\n          fullName: 'Zara Patel',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n        mediaType: 'image',\n        caption: 'Ethnic fusion at its finest! Traditional meets modern ✨',\n        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago\n        expiresAt: new Date(Date.now() + 21 * 60 * 60 * 1000).toISOString(),\n        views: 2156,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-3',\n            name: 'Designer Ethnic Kurti',\n            price: 1899,\n            image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-4',\n        user: {\n          _id: 'user-4',\n          username: 'ai_minimalist_kai',\n          fullName: 'Kai Thompson',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',\n        mediaType: 'image',\n        caption: 'Less is more. Minimalist wardrobe essentials 🌿',\n        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago\n        expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n        views: 1543,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-4',\n            name: 'Classic Denim Jeans',\n            price: 2999,\n            image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-5',\n        user: {\n          _id: 'user-5',\n          username: 'ai_glamour_sophia',\n          fullName: 'Sophia Williams',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n        mediaType: 'image',\n        caption: 'Luxury accessories for the discerning fashionista 💎',\n        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago\n        expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),\n        views: 3421,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-5',\n            name: 'Luxury Leather Handbag',\n            price: 4999,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200'\n          }\n        ]\n      }\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    \n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    \n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    \n    // Left third goes back, right two-thirds go forward\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    \n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    \n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      // Snap back to current position\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    \n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n    \n    // Simple easing\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    \n    // Check if animation is complete\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      \n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    \n    // Update transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    \n    requestAnimationFrame(() => this.update());\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n\n  private setupEventListeners() {\n    // Additional event listeners can be added here\n  }\n\n  private removeEventListeners() {\n    // Clean up event listeners\n  }\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n\n  // Handler for Add Story button\n  // Modal state\n  showAddStoryModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n\n  onAddStory() {\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      // 1. Upload the file to /api/stories/upload\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n      // 2. Post the story with the returned URL\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post('/api/stories', storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n}\n", "\n<!-- Permission Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showPermissionModal\">\n  <div class=\"add-story-modal-backdrop\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Allow Camera & File Access?</h2>\n    <p>To add a story, we need access to your camera and files. This is required to take a photo/video or select from your gallery.</p>\n    <div class=\"add-story-modal-actions\">\n      <button (click)=\"handlePermissionResponse(false)\">Block</button>\n      <button (click)=\"handlePermissionResponse(true)\">Allow</button>\n    </div>\n  </div>\n</div>\n\n<!-- Camera/Gallery Choice Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showCameraOrGallery\">\n  <div class=\"add-story-modal-backdrop\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Add Story</h2>\n    <div class=\"add-story-modal-actions\">\n      <button (click)=\"openCamera()\">Open Camera</button>\n      <button (click)=\"openGallery()\">Choose from Gallery</button>\n    </div>\n  </div>\n</div>\n\n<!-- Permission Denied Message -->\n<div class=\"add-story-modal\" *ngIf=\"permissionDenied\">\n  <div class=\"add-story-modal-backdrop\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Permission Denied</h2>\n    <p>You blocked camera and file access. You cannot add a story unless you allow access.</p>\n    <div class=\"add-story-modal-actions\">\n      <button (click)=\"permissionDenied = false\">Close</button>\n    </div>\n  </div>\n</div>\n\n<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n\n<!-- Add Story Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showAddStoryModal\">\n  <div class=\"add-story-modal-backdrop\" (click)=\"closeAddStoryModal()\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Add Story</h2>\n    <form (ngSubmit)=\"submitNewStory()\" autocomplete=\"off\">\n      <input id=\"story-file-input\" type=\"file\" accept=\"image/*,video/*\" (change)=\"onStoryFileSelected($event)\" [disabled]=\"isUploadingStory\" required style=\"display:none;\" />\n      <textarea placeholder=\"Write a caption...\" [(ngModel)]=\"newStoryCaption\" name=\"caption\" rows=\"2\" [disabled]=\"isUploadingStory\"></textarea>\n      <div class=\"add-story-modal-actions\">\n        <button type=\"button\" (click)=\"closeAddStoryModal()\" [disabled]=\"isUploadingStory\">Cancel</button>\n        <button type=\"submit\" [disabled]=\"!newStoryFile || isUploadingStory\">\n          {{ isUploadingStory ? 'Uploading...' : 'Post Story' }}\n        </button>\n      </div>\n    </form>\n  </div>\n</div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Slider -->\n  <div class=\"stories-slider\" *ngIf=\"!isLoadingStories\">\n\n    <!-- Add Story Button -->\n    <div class=\"story-item add-story-item\" (click)=\"onAddStory()\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar add-story-avatar\">\n          <i class=\"fas fa-plus\"></i>\n        </div>\n      </div>\n      <div class=\"story-username\">Add Story</div>\n    </div>\n    <div\n      *ngFor=\"let story of stories; let i = index\"\n      class=\"story-item\"\n      (click)=\"openStories(i)\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar\" [style.background-image]=\"'url(' + story.user.avatar + ')'\"></div>\n        <div class=\"story-ring\"></div>\n      </div>\n      <div class=\"story-username\">{{ story.user.username }}</div>\n    </div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\">\n          <button class=\"ecommerce-btn buy-now-btn\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": ";AAGA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;ICF5CC,EAAA,CAAAC,cAAA,aAAyD;IACvDD,EAAA,CAAAE,SAAA,aAA4C;IAE1CF,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,kCAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mIAA4H;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjIJ,EADF,CAAAC,cAAA,cAAqC,iBACe;IAA1CD,EAAA,CAAAK,UAAA,mBAAAC,+DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAyB,KAAK,CAAC;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAChEJ,EAAA,CAAAC,cAAA,kBAAiD;IAAzCD,EAAA,CAAAK,UAAA,mBAAAQ,gEAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAyB,IAAI,CAAC;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,aAAK;IAG5DH,EAH4D,CAAAI,YAAA,EAAS,EAC3D,EACF,EACF;;;;;;IAGNJ,EAAA,CAAAC,cAAA,aAAyD;IACvDD,EAAA,CAAAE,SAAA,aAA4C;IAE1CF,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEhBJ,EADF,CAAAC,cAAA,cAAqC,iBACJ;IAAvBD,EAAA,CAAAK,UAAA,mBAAAS,+DAAA;MAAAd,EAAA,CAAAO,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAO,UAAA,EAAY;IAAA,EAAC;IAAChB,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACnDJ,EAAA,CAAAC,cAAA,iBAAgC;IAAxBD,EAAA,CAAAK,UAAA,mBAAAY,+DAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,0BAAmB;IAGzDH,EAHyD,CAAAI,YAAA,EAAS,EACxD,EACF,EACF;;;;;;IAGNJ,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAE,SAAA,aAA4C;IAE1CF,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0FAAmF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAExFJ,EADF,CAAAC,cAAA,cAAqC,iBACQ;IAAnCD,EAAA,CAAAK,UAAA,mBAAAc,+DAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAY,gBAAA,GAA4B,KAAK;IAAA,EAAC;IAACrB,EAAA,CAAAG,MAAA,YAAK;IAGtDH,EAHsD,CAAAI,YAAA,EAAS,EACrD,EACF,EACF;;;;;;IAWJJ,EADF,CAAAC,cAAA,aAAuD,cACgB;IAA/BD,EAAA,CAAAK,UAAA,mBAAAiB,wEAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,kBAAA,EAAoB;IAAA,EAAC;IAACxB,EAAA,CAAAI,YAAA,EAAM;IAEzEJ,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClBJ,EAAA,CAAAC,cAAA,eAAuD;IAAjDD,EAAA,CAAAK,UAAA,sBAAAoB,4EAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAiB,cAAA,EAAgB;IAAA,EAAC;IACjC1B,EAAA,CAAAC,cAAA,gBAAwK;IAAtGD,EAAA,CAAAK,UAAA,oBAAAsB,2EAAAC,MAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAoB,mBAAA,CAAAD,MAAA,CAA2B;IAAA,EAAC;IAAxG5B,EAAA,CAAAI,YAAA,EAAwK;IACxKJ,EAAA,CAAAC,cAAA,mBAA+H;IAApFD,EAAA,CAAA8B,gBAAA,2BAAAC,qFAAAH,MAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAgC,kBAAA,CAAAvB,MAAA,CAAAwB,eAAA,EAAAL,MAAA,MAAAnB,MAAA,CAAAwB,eAAA,GAAAL,MAAA;MAAA,OAAA5B,EAAA,CAAAW,WAAA,CAAAiB,MAAA;IAAA,EAA6B;IAAuD5B,EAAA,CAAAI,YAAA,EAAW;IAExIJ,EADF,CAAAC,cAAA,cAAqC,iBACgD;IAA7DD,EAAA,CAAAK,UAAA,mBAAA6B,2EAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,kBAAA,EAAoB;IAAA,EAAC;IAA+BxB,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClGJ,EAAA,CAAAC,cAAA,kBAAqE;IACnED,EAAA,CAAAG,MAAA,IACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;IAVyGJ,EAAA,CAAAmC,SAAA,GAA6B;IAA7BnC,EAAA,CAAAoC,UAAA,aAAA3B,MAAA,CAAA4B,gBAAA,CAA6B;IAC3FrC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAsC,gBAAA,YAAA7B,MAAA,CAAAwB,eAAA,CAA6B;IAAyBjC,EAAA,CAAAoC,UAAA,aAAA3B,MAAA,CAAA4B,gBAAA,CAA6B;IAEvErC,EAAA,CAAAmC,SAAA,GAA6B;IAA7BnC,EAAA,CAAAoC,UAAA,aAAA3B,MAAA,CAAA4B,gBAAA,CAA6B;IAC5DrC,EAAA,CAAAmC,SAAA,GAA8C;IAA9CnC,EAAA,CAAAoC,UAAA,cAAA3B,MAAA,CAAA8B,YAAA,IAAA9B,MAAA,CAAA4B,gBAAA,CAA8C;IAClErC,EAAA,CAAAmC,SAAA,EACF;IADEnC,EAAA,CAAAwC,kBAAA,MAAA/B,MAAA,CAAA4B,gBAAA,sCACF;;;;;IAfJrC,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,SAAA,cAAmC;IAGzCF,EAAA,CAAAyC,UAAA,IAAAC,kDAAA,kBAAuD;IAgBjD1C,EAAA,CAAAE,SAAA,cAAiC;IACnCF,EAAA,CAAAI,YAAA,EAAM;;;;IAjBoBJ,EAAA,CAAAmC,SAAA,GAAuB;IAAvBnC,EAAA,CAAAoC,UAAA,SAAA3B,MAAA,CAAAkC,iBAAA,CAAuB;;;;;IALnD3C,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAyC,UAAA,IAAAG,4CAAA,kBAA6D;IAsB/D5C,EAAA,CAAAI,YAAA,EAAM;;;IAtBkBJ,EAAA,CAAAmC,SAAA,EAAc;IAAdnC,EAAA,CAAAoC,UAAA,YAAApC,EAAA,CAAA6C,eAAA,IAAAC,GAAA,EAAc;;;;;;IAoCpC9C,EAAA,CAAAC,cAAA,cAG2B;IAAzBD,EAAA,CAAAK,UAAA,mBAAA0C,kEAAA;MAAA,MAAAC,IAAA,GAAAhD,EAAA,CAAAO,aAAA,CAAA0C,GAAA,EAAAC,KAAA;MAAA,MAAAzC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0C,WAAA,CAAAH,IAAA,CAAc;IAAA,EAAC;IACxBhD,EAAA,CAAAC,cAAA,cAAoC;IAElCD,EADA,CAAAE,SAAA,cAA4F,cAC9D;IAChCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IACvDH,EADuD,CAAAI,YAAA,EAAM,EACvD;;;;IAJwBJ,EAAA,CAAAmC,SAAA,GAA2D;IAA3DnC,EAAA,CAAAoD,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAG3DvD,EAAA,CAAAmC,SAAA,GAAyB;IAAzBnC,EAAA,CAAAwD,iBAAA,CAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;;IAhBvDzD,EAHF,CAAAC,cAAA,cAAsD,cAGU;IAAvBD,EAAA,CAAAK,UAAA,mBAAAqD,4DAAA;MAAA1D,EAAA,CAAAO,aAAA,CAAAoD,GAAA;MAAA,MAAAlD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmD,UAAA,EAAY;IAAA,EAAC;IAEzD5D,EADF,CAAAC,cAAA,cAAoC,cACS;IACzCD,EAAA,CAAAE,SAAA,YAA2B;IAE/BF,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IACvCH,EADuC,CAAAI,YAAA,EAAM,EACvC;IACNJ,EAAA,CAAAyC,UAAA,IAAAoB,4CAAA,kBAG2B;IAO7B7D,EAAA,CAAAI,YAAA,EAAM;;;;IATgBJ,EAAA,CAAAmC,SAAA,GAAY;IAAZnC,EAAA,CAAAoC,UAAA,YAAA3B,MAAA,CAAAqD,OAAA,CAAY;;;;;IAkB9B9D,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAI,YAAA,EAAM;;;;;IAFJJ,EADA,CAAA+D,WAAA,WAAAC,KAAA,KAAAvD,MAAA,CAAAwD,YAAA,CAAmC,cAAAD,KAAA,GAAAvD,MAAA,CAAAwD,YAAA,CACC;;;;;IA6BpCjE,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAAoC,UAAA,QAAA3B,MAAA,CAAAyD,eAAA,GAAAC,QAAA,EAAAnE,EAAA,CAAAoE,aAAA,CAAkC;;;;;IAQpCpE,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAoD,WAAA,8BAAA3C,MAAA,CAAAyD,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItEnE,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAmC,SAAA,EACF;IADEnC,EAAA,CAAAwC,kBAAA,MAAA/B,MAAA,CAAAyD,eAAA,GAAAG,OAAA,MACF;;;;;;IAIErE,EAAA,CAAAC,cAAA,cAGiC;IAA/BD,EAAA,CAAAK,UAAA,mBAAAiE,yEAAA;MAAA,MAAAC,WAAA,GAAAvE,EAAA,CAAAO,aAAA,CAAAiE,IAAA,EAAAC,SAAA;MAAA,MAAAhE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiE,WAAA,CAAAH,WAAA,CAAoB;IAAA,EAAC;IAC9BvE,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAG,MAAA,yBAAG;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAErCJ,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACtDJ,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEnEH,EAFmE,CAAAI,YAAA,EAAM,EACjE,EACF;;;;;IAH4BJ,EAAA,CAAAmC,SAAA,GAAkB;IAAlBnC,EAAA,CAAAwD,iBAAA,CAAAe,WAAA,CAAAI,IAAA,CAAkB;IACjB3E,EAAA,CAAAmC,SAAA,GAAgC;IAAhCnC,EAAA,CAAAwD,iBAAA,CAAA/C,MAAA,CAAAmE,WAAA,CAAAL,WAAA,CAAAM,KAAA,EAAgC;;;;;IARrE7E,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAyC,UAAA,IAAAqC,mDAAA,kBAGiC;IAOnC9E,EAAA,CAAAI,YAAA,EAAM;;;;IATkBJ,EAAA,CAAAmC,SAAA,EAAqB;IAArBnC,EAAA,CAAAoC,UAAA,YAAA3B,MAAA,CAAAsE,gBAAA,GAAqB;;;;;;IA3DjD/E,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAAyC,UAAA,IAAAuC,4CAAA,kBAIuC;IAGzChF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAK,UAAA,mBAAA4E,4DAAArD,MAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0E,YAAA,CAAAvD,MAAA,CAAoB;IAAA,EAAC,wBAAAwD,iEAAAxD,MAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAChBF,MAAA,CAAA4E,YAAA,CAAAzD,MAAA,CAAoB;IAAA,EAAC,uBAAA0D,gEAAA1D,MAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACtBF,MAAA,CAAA8E,WAAA,CAAA3D,MAAA,CAAmB;IAAA,EAAC,sBAAA4D,+DAAA5D,MAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACrBF,MAAA,CAAAgF,UAAA,CAAA7D,MAAA,CAAkB;IAAA,EAAC;IAIhC5B,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAAyG;IACzGF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpEJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,IAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC5EJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,IAAiD;IAC7EH,EAD6E,CAAAI,YAAA,EAAM,EAC7E;IACNJ,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAK,UAAA,mBAAAqF,gEAAA;MAAA1F,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkF,YAAA,EAAc;IAAA,EAAC;IACnD3F,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAI,YAAA,EAAS,EACL;IAGNJ,EAAA,CAAAC,cAAA,eAA4B;IAyB1BD,EAvBA,CAAAyC,UAAA,KAAAmD,+CAAA,oBAOc,KAAAC,6CAAA,kBAOyD,KAAAC,6CAAA,kBAIT,KAAAC,6CAAA,kBAKP;IAYzD/F,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAI,YAAA,EAAS,EACL;IAIJJ,EADF,CAAAC,cAAA,eAAsC,kBACM;IACxCD,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACfH,EADe,CAAAI,YAAA,EAAO,EACb;IACTJ,EAAA,CAAAC,cAAA,kBAA2C;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACd;IACTJ,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAGvBH,EAHuB,CAAAI,YAAA,EAAO,EACjB,EACL,EACF;IAINJ,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAI,YAAA,EAAM,EACF;IAGNJ,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAI,YAAA,EAAM;;;;IAlHuBJ,EAAA,CAAA+D,WAAA,YAAAtD,MAAA,CAAAuF,MAAA,CAAwB;IAM3BhG,EAAA,CAAAmC,SAAA,GAAY;IAAZnC,EAAA,CAAAoC,UAAA,YAAA3B,MAAA,CAAAqD,OAAA,CAAY;IAU7B9D,EAAA,CAAAmC,SAAA,EAAmC;;IASPnC,EAAA,CAAAmC,SAAA,GAAuE;IAAvEnC,EAAA,CAAAoD,WAAA,8BAAA3C,MAAA,CAAAyD,eAAA,GAAAZ,IAAA,CAAAC,MAAA,OAAuE;IACzEvD,EAAA,CAAAmC,SAAA,GAAqC;IAArCnC,EAAA,CAAAwD,iBAAA,CAAA/C,MAAA,CAAAyD,eAAA,GAAAZ,IAAA,CAAA2C,QAAA,CAAqC;IACrCjG,EAAA,CAAAmC,SAAA,GAA6C;IAA7CnC,EAAA,CAAAwD,iBAAA,CAAA/C,MAAA,CAAAyF,UAAA,CAAAzF,MAAA,CAAAyD,eAAA,GAAAiC,SAAA,EAA6C;IAC5CnG,EAAA,CAAAmC,SAAA,GAAiD;IAAjDnC,EAAA,CAAAwC,kBAAA,KAAA/B,MAAA,CAAA2F,YAAA,CAAA3F,MAAA,CAAAyD,eAAA,GAAAmC,KAAA,YAAiD;IAW1ErG,EAAA,CAAAmC,SAAA,GAA6C;IAA7CnC,EAAA,CAAAoC,UAAA,SAAA3B,MAAA,CAAAyD,eAAA,GAAAoC,SAAA,aAA6C;IAW7CtG,EAAA,CAAAmC,SAAA,EAA6C;IAA7CnC,EAAA,CAAAoC,UAAA,SAAA3B,MAAA,CAAAyD,eAAA,GAAAoC,SAAA,aAA6C;IAM1CtG,EAAA,CAAAmC,SAAA,EAA+B;IAA/BnC,EAAA,CAAAoC,UAAA,SAAA3B,MAAA,CAAAyD,eAAA,GAAAG,OAAA,CAA+B;IAK/BrE,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAoC,UAAA,SAAA3B,MAAA,CAAA8F,WAAA,GAAmB;IAoDKvG,EAAA,CAAAmC,SAAA,IAA0B;IAA1BnC,EAAA,CAAA+D,WAAA,cAAAtD,MAAA,CAAAuF,MAAA,CAA0B;;;;;IAK9DhG,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IACtBH,EADsB,CAAAI,YAAA,EAAO,EACvB;IAEJJ,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5BJ,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAI,YAAA,EAAM,EACF;;;ADlLN,OAAM,MAAOoG,uBAAuB;EAwBlCC,YACUC,MAAc,EACdC,IAAgB;IADhB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IAtBd,KAAA7C,OAAO,GAAY,EAAE;IACrB,KAAA8C,gBAAgB,GAAG,IAAI;IAEvB,KAAA3C,YAAY,GAAG,CAAC;IAChB,KAAA+B,MAAM,GAAG,KAAK;IACd,KAAAa,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEX,KAAAC,aAAa,GAAmB,EAAE;IA6Y1C;IACA;IACA,KAAA5E,iBAAiB,GAAG,KAAK;IACzB,KAAA6E,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAApG,gBAAgB,GAAG,KAAK;IACxB,KAAAkB,YAAY,GAAgB,IAAI;IAChC,KAAAN,eAAe,GAAG,EAAE;IACpB,KAAAI,gBAAgB,GAAG,KAAK;EAhZrB;EAEHqF,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACf,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACW,aAAa,CAACW,IAAI,CACrB,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAM,0CAA0C,CAAC,CAACC,SAAS,CAAC;MACvEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAAC1E,OAAO,GAAGwE,QAAQ,CAACE,IAAI;SAC7B,MAAM;UACL,IAAI,CAACC,mBAAmB,EAAE;;QAE5B,IAAI,CAAC7B,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAAC7B,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEA6B,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC3E,OAAO,GAAG,CACb;MACE8E,GAAG,EAAE,SAAS;MACdtF,IAAI,EAAE;QACJsF,GAAG,EAAE,QAAQ;QACbnF,QAAQ,EAAE,qBAAqB;QAC/BwC,QAAQ,EAAE,WAAW;QACrB1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,oEAAoE;MAC9EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,wCAAwC;MACjD8B,SAAS,EAAE,IAAI0C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC7DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1C,KAAK,EAAE,IAAI;MACX4C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACbjE,IAAI,EAAE,2BAA2B;QACjCE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdtF,IAAI,EAAE;QACJsF,GAAG,EAAE,QAAQ;QACbnF,QAAQ,EAAE,iBAAiB;QAC3BwC,QAAQ,EAAE,gBAAgB;QAC1B1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,oEAAoE;MAC9EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,+CAA+C;MACxD8B,SAAS,EAAE,IAAI0C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC9DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACrE1C,KAAK,EAAE,GAAG;MACV4C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACbjE,IAAI,EAAE,sBAAsB;QAC5BE,KAAK,EAAE,GAAG;QACVsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdtF,IAAI,EAAE;QACJsF,GAAG,EAAE,QAAQ;QACbnF,QAAQ,EAAE,qBAAqB;QAC/BwC,QAAQ,EAAE,YAAY;QACtB1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,oEAAoE;MAC9EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,yDAAyD;MAClE8B,SAAS,EAAE,IAAI0C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1C,KAAK,EAAE,IAAI;MACX4C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACbjE,IAAI,EAAE,uBAAuB;QAC7BE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdtF,IAAI,EAAE;QACJsF,GAAG,EAAE,QAAQ;QACbnF,QAAQ,EAAE,mBAAmB;QAC7BwC,QAAQ,EAAE,cAAc;QACxB1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,iEAAiE;MAC3EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,iDAAiD;MAC1D8B,SAAS,EAAE,IAAI0C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1C,KAAK,EAAE,IAAI;MACX4C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACbjE,IAAI,EAAE,qBAAqB;QAC3BE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdtF,IAAI,EAAE;QACJsF,GAAG,EAAE,QAAQ;QACbnF,QAAQ,EAAE,mBAAmB;QAC7BwC,QAAQ,EAAE,iBAAiB;QAC3B1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,iEAAiE;MAC3EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,sDAAsD;MAC/D8B,SAAS,EAAE,IAAI0C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnE1C,KAAK,EAAE,IAAI;MACX4C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACbjE,IAAI,EAAE,wBAAwB;QAC9BE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,CACF;EACH;EAEAhG,WAAWA,CAACD,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACe,YAAY,GAAGf,KAAK;IACzB,IAAI,CAAC8C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACoD,SAAS,CAAClG,KAAK,CAAC;IACrBmG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEA7D,YAAYA,CAAA;IACV,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACyD,cAAc,EAAE;IACrBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAX,SAASA,CAAClG,KAAa;IACrB,IAAI,CAACe,YAAY,GAAGf,KAAK;IACzB,IAAI,CAAC6D,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAAC2C,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAChG,YAAY,GAAG,IAAI,CAACH,OAAO,CAACoG,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAClD,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACsD,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACxE,YAAY,EAAE;;EAEvB;EAEAyE,aAAaA,CAAA;IACX,IAAI,IAAI,CAACnG,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC+C,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACsD,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACxE,YAAY,EAAE;;EAEvB;EAGA0E,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACtE,MAAM,EAAE;IAElB,QAAQsE,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACtE,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAACmF,KAAiB;IAC5B,IAAI,IAAI,CAACzD,UAAU,EAAE;IAErB,MAAM2D,MAAM,GAAGF,KAAK,CAACG,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IAErC;IACA,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACN,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EAEA5E,YAAYA,CAACiF,KAAiB;IAC5B,IAAI,CAACxD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGoD,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC1C,IAAI,CAACtD,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEA3B,WAAWA,CAAC+E,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACxD,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGmD,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC5C,IAAI,CAACK,kBAAkB,EAAE;EAC3B;EAEArF,UAAUA,CAAC6E,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAACxD,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IAEvB,MAAMiE,SAAS,GAAG,IAAI,CAAC5D,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAM8D,SAAS,GAAGL,MAAM,CAACC,UAAU,GAAG,IAAI,CAACxD,0BAA0B;IAErE,IAAI6D,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACX,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL;MACA,IAAI,CAACjD,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACsD,MAAM,EAAE;;EAEjB;EAEQW,kBAAkBA,CAAA;IACxB,MAAMC,SAAS,GAAG,IAAI,CAAC5D,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAIgE,SAAS,GAAGJ,MAAM,CAACC,UAAU,GAAI,EAAE;IAEnD,IAAI,IAAI,CAAClB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAACjD,OAAO,MAAM;;EAErD;EAEQoD,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACtD,UAAU,EAAE;IAEtB;IACA,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAE1E;IACA,IAAI2D,IAAI,CAACC,GAAG,CAAC,IAAI,CAACnE,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAMkE,QAAQ,GAAG,IAAI,CAAClE,eAAe,KAAK,SAAS,GAC/C,IAAI,CAAChD,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAACmF,SAAS,CAAC+B,QAAQ,CAAC;QACxB,IAAI,CAAClE,eAAe,GAAG,IAAI;;MAE7B;;IAGF;IACA,IAAI,IAAI,CAACyC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAACjD,OAAO,MAAM;;IAGnDqE,qBAAqB,CAAC,MAAM,IAAI,CAACjB,MAAM,EAAE,CAAC;EAC5C;EAEQV,cAAcA,CAAA;IACpB,MAAM4B,MAAM,GAAGhC,QAAQ,CAACiC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAACvD,OAAO,CAAEyD,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAEQ5D,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGF/D,eAAeA,CAAA;IACb,OAAO,IAAI,CAACJ,OAAO,CAAC,IAAI,CAACG,YAAY,CAAC;EACxC;EAEAwH,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACxH,YAAY,GAAG,CAAC,IAAI,IAAI,CAACH,OAAO,CAACoG,MAAM,GAAI,GAAG;EAC9D;EAEAhE,UAAUA,CAACwF,UAAkB;IAC3B,MAAM5C,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM8C,IAAI,GAAG,IAAI9C,IAAI,CAAC6C,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGX,IAAI,CAACY,KAAK,CAAC,CAAC/C,GAAG,CAACgD,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMG,WAAW,GAAGd,IAAI,CAACY,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGf,IAAI,CAACY,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEA5F,YAAYA,CAAC6F,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAvH,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIuH,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpC9C,KAAK,EAAE,UAAU;MACjB+C,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC3H,KAAK,CAAC;EAClB;EAEAH,WAAWA,CAAC+H,OAAY;IACtB,IAAI,CAAC/F,MAAM,CAACgG,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC7D,GAAG,CAAC,CAAC;EACjD;EAEArC,WAAWA,CAAA;IACT,MAAMoG,KAAK,GAAG,IAAI,CAACzI,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEyI,KAAK,IAAIA,KAAK,CAACzD,QAAQ,IAAIyD,KAAK,CAACzD,QAAQ,CAACgB,MAAM,GAAG,CAAC,CAAC;EACjE;EAEAnF,gBAAgBA,CAAA;IACd,MAAM4H,KAAK,GAAG,IAAI,CAACzI,eAAe,EAAE;IACpC,OAAOyI,KAAK,EAAEzD,QAAQ,IAAI,EAAE;EAC9B;EAYAtF,UAAUA,CAAA;IACR,IAAI,CAAC4D,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACnG,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACsB,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC8E,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAClF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,eAAe,GAAG,EAAE;EAC3B;EAEArB,wBAAwBA,CAACgM,KAAc;IACrC,IAAI,CAACpF,mBAAmB,GAAG,KAAK;IAChC,IAAIoF,KAAK,EAAE;MACT,IAAI,CAACnF,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACpG,gBAAgB,GAAG,IAAI;;EAEhC;EAEAL,UAAUA,CAAA;IACR,IAAI,CAACyG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC9E,iBAAiB,GAAG,IAAI;IAC7BmH,UAAU,CAAC,MAAK;MACd,MAAM+C,KAAK,GAAQxD,QAAQ,CAACyD,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CF,KAAK,CAACG,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA9L,WAAWA,CAAA;IACT,IAAI,CAACuG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC9E,iBAAiB,GAAG,IAAI;IAC7BmH,UAAU,CAAC,MAAK;MACd,MAAM+C,KAAK,GAAQxD,QAAQ,CAACyD,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAID,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACG,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAnL,mBAAmBA,CAACyI,KAAU;IAC5B,MAAM4C,IAAI,GAAG5C,KAAK,CAAC6C,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAAC3K,YAAY,GAAG2K,IAAI;;EAE5B;EAEMxL,cAAcA,CAAA;IAAA,IAAA2L,KAAA;IAAA,OAAAC,iBAAA;MAClB,IAAI,CAACD,KAAI,CAAC9K,YAAY,EAAE;MACxB8K,KAAI,CAAChL,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF;QACA,MAAMkL,UAAU,GAAG,IAAIC,QAAQ,EAAE;QACjCD,UAAU,CAACE,MAAM,CAAC,OAAO,EAAEJ,KAAI,CAAC9K,YAAY,CAAC;QAC7C,MAAMmL,SAAS,SAAcL,KAAI,CAAC1G,IAAI,CAACgH,IAAI,CAAC,qBAAqB,EAAEJ,UAAU,CAAC,CAACK,SAAS,EAAE;QAC1F;QACA,MAAMC,YAAY,GAAG;UACnBC,KAAK,EAAE;YACLC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,GAAG,EAAEN,SAAS,CAACM;WAChB;UACD3J,OAAO,EAAEgJ,KAAI,CAACpL;SACf;QACD,MAAMoL,KAAI,CAAC1G,IAAI,CAACgH,IAAI,CAAC,cAAc,EAAEE,YAAY,CAAC,CAACD,SAAS,EAAE;QAC9DP,KAAI,CAAC1K,iBAAiB,GAAG,KAAK;QAC9B0K,KAAI,CAAC1F,WAAW,EAAE;OACnB,CAAC,OAAOsG,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACRb,KAAI,CAAChL,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EAEAb,kBAAkBA,CAAA;IAChB,IAAI,CAACmB,iBAAiB,GAAG,KAAK;EAChC;;;uBA1fW6D,uBAAuB,EAAAxG,EAAA,CAAAmO,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArO,EAAA,CAAAmO,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAvB/H,uBAAuB;MAAAgI,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAvB3O,EAAA,CAAAK,UAAA,qBAAAwO,mDAAAjN,MAAA;YAAA,OAAAgN,GAAA,CAAAvE,aAAA,CAAAzI,MAAA,CAAqB;UAAA,UAAA5B,EAAA,CAAA8O,iBAAA,CAAE;;;;;;;;;;UCbpC9O,EAzBA,CAAAyC,UAAA,IAAAsM,sCAAA,kBAAyD,IAAAC,sCAAA,kBAaA,IAAAC,sCAAA,kBAYH;UAYtDjP,EAAA,CAAAC,cAAA,aAA+B;UA4B7BD,EA1BA,CAAAyC,UAAA,IAAAyM,sCAAA,iBAAsD,IAAAC,sCAAA,iBA0BA;UAsBxDnP,EAAA,CAAAI,YAAA,EAAM;UAwHNJ,EArHA,CAAAyC,UAAA,IAAA2M,sCAAA,mBAAqE,IAAAC,sCAAA,iBAqHxB;;;UA/MfrP,EAAA,CAAAoC,UAAA,SAAAwM,GAAA,CAAApH,mBAAA,CAAyB;UAazBxH,EAAA,CAAAmC,SAAA,EAAyB;UAAzBnC,EAAA,CAAAoC,UAAA,SAAAwM,GAAA,CAAAnH,mBAAA,CAAyB;UAYzBzH,EAAA,CAAAmC,SAAA,EAAsB;UAAtBnC,EAAA,CAAAoC,UAAA,SAAAwM,GAAA,CAAAvN,gBAAA,CAAsB;UAc5CrB,EAAA,CAAAmC,SAAA,GAAsB;UAAtBnC,EAAA,CAAAoC,UAAA,SAAAwM,GAAA,CAAAhI,gBAAA,CAAsB;UA0BC5G,EAAA,CAAAmC,SAAA,EAAuB;UAAvBnC,EAAA,CAAAoC,UAAA,UAAAwM,GAAA,CAAAhI,gBAAA,CAAuB;UAyBC5G,EAAA,CAAAmC,SAAA,EAAY;UAAZnC,EAAA,CAAAoC,UAAA,SAAAwM,GAAA,CAAA5I,MAAA,CAAY;UAqHpChG,EAAA,CAAAmC,SAAA,EAAY;UAAZnC,EAAA,CAAAoC,UAAA,SAAAwM,GAAA,CAAA5I,MAAA,CAAY;;;qBD7K/BlG,YAAY,EAAAwP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEzP,WAAW,EAAA0P,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}