{"ast": null, "code": "import { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RecommendationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:5000/api';\n    this.userAnalytics$ = new BehaviorSubject(null);\n  }\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId, limit = 10) {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log('🎯 Loading suggested products (offline mode)');\n    return this.getFallbackSuggestedProducts(limit);\n    /* API version - uncomment when backend is available\n    const params = new URLSearchParams();\n    if (userId) params.append('userId', userId);\n    params.append('limit', limit.toString());\n         return this.http.get<any>(`${this.apiUrl}/recommendations/suggested?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching suggested products:', error);\n          return this.getFallbackSuggestedProducts(limit);\n        })\n      );\n    */\n  }\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category, limit = 10) {\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/recommendations/trending?${params.toString()}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching trending products:', error);\n      // Return fallback data immediately\n      return this.getFallbackTrendingProducts(limit);\n    }));\n  }\n  // Similar Products - Based on Product\n  getSimilarProducts(productId, limit = 6) {\n    return this.http.get(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching similar products:', error);\n      return this.getFallbackSimilarProducts(limit);\n    }));\n  }\n  // Recently Viewed Products\n  getRecentlyViewed(userId, limit = 8) {\n    return this.http.get(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching recently viewed:', error);\n      return this.getFallbackRecentProducts(limit);\n    }));\n  }\n  // Track User Behavior for Analytics\n  trackProductView(productId, category, duration = 0) {\n    return this.http.post(`${this.apiUrl}/analytics/track-view`, {\n      productId,\n      category,\n      duration,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking product view:', error);\n      return [];\n    }));\n  }\n  trackSearch(query, category, resultsClicked = 0) {\n    return this.http.post(`${this.apiUrl}/analytics/track-search`, {\n      query,\n      category,\n      resultsClicked,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking search:', error);\n      return [];\n    }));\n  }\n  trackPurchase(productId, category, price) {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking purchase:', error);\n      return [];\n    }));\n  }\n  // User Analytics\n  getUserAnalytics(userId) {\n    return this.http.get(`${this.apiUrl}/analytics/user/${userId}`).pipe(map(response => response.success ? response.data : this.getDefaultAnalytics(userId)), catchError(error => {\n      console.error('Error fetching user analytics:', error);\n      return [this.getDefaultAnalytics(userId)];\n    }));\n  }\n  // Category-based Recommendations\n  getCategoryRecommendations(category, limit = 8) {\n    return this.http.get(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching category recommendations:', error);\n      // Return fallback data immediately\n      return this.getFallbackCategoryProducts(category, limit);\n    }));\n  }\n  // Fallback methods for offline/error scenarios\n  getFallbackSuggestedProducts(limit) {\n    // Return mock suggested products based on popular items\n    const mockProducts = [{\n      _id: 'suggested-1',\n      name: 'Trending Cotton T-Shirt',\n      description: 'Popular cotton t-shirt based on your preferences',\n      price: 899,\n      originalPrice: 1299,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        alt: 'Cotton T-Shirt',\n        isPrimary: true\n      }],\n      category: 'men',\n      subcategory: 'shirts',\n      brand: 'ComfortWear',\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      tags: ['cotton', 'casual', 'trending'],\n      isActive: true,\n      isFeatured: true,\n      recommendationScore: 0.85,\n      recommendationReason: 'Based on your recent views'\n    }];\n    return new Observable(observer => {\n      observer.next(mockProducts.slice(0, limit));\n      observer.complete();\n    });\n  }\n  getFallbackTrendingProducts(limit) {\n    const mockTrending = [{\n      _id: 'trending-1',\n      name: 'Viral Summer Dress',\n      description: 'This dress is trending across social media',\n      price: 2499,\n      originalPrice: 3499,\n      discount: 29,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        alt: 'Summer Dress',\n        isPrimary: true\n      }],\n      category: 'women',\n      subcategory: 'dresses',\n      brand: 'StyleHub',\n      rating: {\n        average: 4.5,\n        count: 89\n      },\n      tags: ['summer', 'trending', 'viral'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.92,\n      trendingReason: 'Viral on social media',\n      viewCount: 15420,\n      purchaseCount: 342,\n      shareCount: 1250,\n      engagementRate: 8.7\n    }, {\n      _id: 'trending-2',\n      name: 'Trending Casual T-Shirt',\n      description: 'Popular casual wear for everyday comfort',\n      price: 899,\n      originalPrice: 1299,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        alt: 'Casual T-Shirt',\n        isPrimary: true\n      }],\n      category: 'men',\n      subcategory: 'shirts',\n      brand: 'ComfortWear',\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      tags: ['casual', 'trending', 'comfort'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.85,\n      trendingReason: 'High demand this week',\n      viewCount: 12300,\n      purchaseCount: 287,\n      shareCount: 890,\n      engagementRate: 7.4\n    }, {\n      _id: 'trending-3',\n      name: 'Stylish Ethnic Kurta',\n      description: 'Traditional wear with modern styling',\n      price: 1899,\n      originalPrice: 2499,\n      discount: 24,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n        alt: 'Ethnic Kurta',\n        isPrimary: true\n      }],\n      category: 'women',\n      subcategory: 'ethnic',\n      brand: 'EthnicChic',\n      rating: {\n        average: 4.6,\n        count: 203\n      },\n      tags: ['ethnic', 'traditional', 'festive'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.88,\n      trendingReason: 'Festival season favorite',\n      viewCount: 9800,\n      purchaseCount: 198,\n      shareCount: 567,\n      engagementRate: 8.1\n    }];\n    return new Observable(observer => {\n      observer.next(mockTrending.slice(0, limit));\n      observer.complete();\n    });\n  }\n  getFallbackSimilarProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackRecentProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackCategoryProducts(category, limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getDefaultAnalytics(userId) {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: {\n        min: 500,\n        max: 5000\n      },\n      brandPreferences: []\n    };\n  }\n  // Update user analytics locally\n  updateUserAnalytics(analytics) {\n    this.userAnalytics$.next(analytics);\n  }\n  // Get current user analytics\n  getCurrentUserAnalytics() {\n    return this.userAnalytics$.asObservable();\n  }\n  static {\n    this.ɵfac = function RecommendationService_Factory(t) {\n      return new (t || RecommendationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RecommendationService,\n      factory: RecommendationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "BehaviorSubject", "map", "catchError", "RecommendationService", "constructor", "http", "apiUrl", "userAnalytics$", "getSuggestedProducts", "userId", "limit", "console", "log", "getFallbackSuggestedProducts", "getTrendingProducts", "category", "params", "URLSearchParams", "append", "toString", "get", "pipe", "response", "success", "data", "error", "getFallbackTrendingProducts", "getSimilarProducts", "productId", "getFallbackSimilarProducts", "getRecently<PERSON>iewed", "getFallbackRecentProducts", "trackProductView", "duration", "post", "timestamp", "Date", "trackSearch", "query", "resultsClicked", "trackPurchase", "price", "getUserAnalytics", "getDefaultAnalytics", "getCategoryRecommendations", "getFallbackCategoryProducts", "mockProducts", "_id", "name", "description", "originalPrice", "discount", "images", "url", "alt", "isPrimary", "subcategory", "brand", "rating", "average", "count", "tags", "isActive", "isFeatured", "recommendationScore", "recommendationReason", "observer", "next", "slice", "complete", "mockTrending", "trendingScore", "trendingReason", "viewCount", "purchaseCount", "shareCount", "engagementRate", "viewHistory", "searchHistory", "purchaseHistory", "wishlistItems", "cartItems", "preferredCategories", "priceRange", "min", "max", "brandPreferences", "updateUserAnalytics", "analytics", "getCurrentUserAnalytics", "asObservable", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\recommendation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\n\nexport interface RecommendationProduct {\n  _id: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice: number;\n  discount: number;\n  images: Array<{ url: string; alt: string; isPrimary: boolean }>;\n  category: string;\n  subcategory: string;\n  brand: string;\n  rating: { average: number; count: number };\n  tags: string[];\n  isActive: boolean;\n  isFeatured: boolean;\n  recommendationScore?: number;\n  recommendationReason?: string;\n}\n\nexport interface TrendingProduct extends RecommendationProduct {\n  trendingScore: number;\n  trendingReason: string;\n  viewCount: number;\n  purchaseCount: number;\n  shareCount: number;\n  engagementRate: number;\n}\n\nexport interface UserAnalytics {\n  userId: string;\n  viewHistory: Array<{\n    productId: string;\n    category: string;\n    timestamp: Date;\n    duration: number;\n  }>;\n  searchHistory: Array<{\n    query: string;\n    category?: string;\n    timestamp: Date;\n    resultsClicked: number;\n  }>;\n  purchaseHistory: Array<{\n    productId: string;\n    category: string;\n    price: number;\n    timestamp: Date;\n  }>;\n  wishlistItems: string[];\n  cartItems: string[];\n  preferredCategories: string[];\n  priceRange: { min: number; max: number };\n  brandPreferences: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RecommendationService {\n  private apiUrl = 'http://localhost:5000/api';\n  private userAnalytics$ = new BehaviorSubject<UserAnalytics | null>(null);\n\n  constructor(private http: HttpClient) {}\n\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId?: string, limit: number = 10): Observable<RecommendationProduct[]> {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log('🎯 Loading suggested products (offline mode)');\n    return this.getFallbackSuggestedProducts(limit);\n\n    /* API version - uncomment when backend is available\n    const params = new URLSearchParams();\n    if (userId) params.append('userId', userId);\n    params.append('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/suggested?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching suggested products:', error);\n          return this.getFallbackSuggestedProducts(limit);\n        })\n      );\n    */\n  }\n\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category?: string, limit: number = 10): Observable<TrendingProduct[]> {\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/trending?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching trending products:', error);\n          // Return fallback data immediately\n          return this.getFallbackTrendingProducts(limit);\n        })\n      );\n  }\n\n  // Similar Products - Based on Product\n  getSimilarProducts(productId: string, limit: number = 6): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching similar products:', error);\n          return this.getFallbackSimilarProducts(limit);\n        })\n      );\n  }\n\n  // Recently Viewed Products\n  getRecentlyViewed(userId: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching recently viewed:', error);\n          return this.getFallbackRecentProducts(limit);\n        })\n      );\n  }\n\n  // Track User Behavior for Analytics\n  trackProductView(productId: string, category: string, duration: number = 0): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-view`, {\n      productId,\n      category,\n      duration,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking product view:', error);\n        return [];\n      })\n    );\n  }\n\n  trackSearch(query: string, category?: string, resultsClicked: number = 0): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-search`, {\n      query,\n      category,\n      resultsClicked,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking search:', error);\n        return [];\n      })\n    );\n  }\n\n  trackPurchase(productId: string, category: string, price: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking purchase:', error);\n        return [];\n      })\n    );\n  }\n\n  // User Analytics\n  getUserAnalytics(userId: string): Observable<UserAnalytics> {\n    return this.http.get<any>(`${this.apiUrl}/analytics/user/${userId}`)\n      .pipe(\n        map(response => response.success ? response.data : this.getDefaultAnalytics(userId)),\n        catchError(error => {\n          console.error('Error fetching user analytics:', error);\n          return [this.getDefaultAnalytics(userId)];\n        })\n      );\n  }\n\n  // Category-based Recommendations\n  getCategoryRecommendations(category: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching category recommendations:', error);\n          // Return fallback data immediately\n          return this.getFallbackCategoryProducts(category, limit);\n        })\n      );\n  }\n\n  // Fallback methods for offline/error scenarios\n  private getFallbackSuggestedProducts(limit: number): Observable<RecommendationProduct[]> {\n    // Return mock suggested products based on popular items\n    const mockProducts: RecommendationProduct[] = [\n      {\n        _id: 'suggested-1',\n        name: 'Trending Cotton T-Shirt',\n        description: 'Popular cotton t-shirt based on your preferences',\n        price: 899,\n        originalPrice: 1299,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', alt: 'Cotton T-Shirt', isPrimary: true }],\n        category: 'men',\n        subcategory: 'shirts',\n        brand: 'ComfortWear',\n        rating: { average: 4.2, count: 156 },\n        tags: ['cotton', 'casual', 'trending'],\n        isActive: true,\n        isFeatured: true,\n        recommendationScore: 0.85,\n        recommendationReason: 'Based on your recent views'\n      }\n    ];\n    return new Observable(observer => {\n      observer.next(mockProducts.slice(0, limit));\n      observer.complete();\n    });\n  }\n\n  private getFallbackTrendingProducts(limit: number): Observable<TrendingProduct[]> {\n    const mockTrending: TrendingProduct[] = [\n      {\n        _id: 'trending-1',\n        name: 'Viral Summer Dress',\n        description: 'This dress is trending across social media',\n        price: 2499,\n        originalPrice: 3499,\n        discount: 29,\n        images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400', alt: 'Summer Dress', isPrimary: true }],\n        category: 'women',\n        subcategory: 'dresses',\n        brand: 'StyleHub',\n        rating: { average: 4.5, count: 89 },\n        tags: ['summer', 'trending', 'viral'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.92,\n        trendingReason: 'Viral on social media',\n        viewCount: 15420,\n        purchaseCount: 342,\n        shareCount: 1250,\n        engagementRate: 8.7\n      },\n      {\n        _id: 'trending-2',\n        name: 'Trending Casual T-Shirt',\n        description: 'Popular casual wear for everyday comfort',\n        price: 899,\n        originalPrice: 1299,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', alt: 'Casual T-Shirt', isPrimary: true }],\n        category: 'men',\n        subcategory: 'shirts',\n        brand: 'ComfortWear',\n        rating: { average: 4.2, count: 156 },\n        tags: ['casual', 'trending', 'comfort'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.85,\n        trendingReason: 'High demand this week',\n        viewCount: 12300,\n        purchaseCount: 287,\n        shareCount: 890,\n        engagementRate: 7.4\n      },\n      {\n        _id: 'trending-3',\n        name: 'Stylish Ethnic Kurta',\n        description: 'Traditional wear with modern styling',\n        price: 1899,\n        originalPrice: 2499,\n        discount: 24,\n        images: [{ url: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400', alt: 'Ethnic Kurta', isPrimary: true }],\n        category: 'women',\n        subcategory: 'ethnic',\n        brand: 'EthnicChic',\n        rating: { average: 4.6, count: 203 },\n        tags: ['ethnic', 'traditional', 'festive'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.88,\n        trendingReason: 'Festival season favorite',\n        viewCount: 9800,\n        purchaseCount: 198,\n        shareCount: 567,\n        engagementRate: 8.1\n      }\n    ];\n    return new Observable(observer => {\n      observer.next(mockTrending.slice(0, limit));\n      observer.complete();\n    });\n  }\n\n  private getFallbackSimilarProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackRecentProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackCategoryProducts(category: string, limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getDefaultAnalytics(userId: string): UserAnalytics {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: { min: 500, max: 5000 },\n      brandPreferences: []\n    };\n  }\n\n  // Update user analytics locally\n  updateUserAnalytics(analytics: UserAnalytics): void {\n    this.userAnalytics$.next(analytics);\n  }\n\n  // Get current user analytics\n  getCurrentUserAnalytics(): Observable<UserAnalytics | null> {\n    return this.userAnalytics$.asObservable();\n  }\n}\n"], "mappings": "AAEA,SAASA,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AA4DhD,OAAM,MAAOC,qBAAqB;EAIhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,MAAM,GAAG,2BAA2B;IACpC,KAAAC,cAAc,GAAG,IAAIP,eAAe,CAAuB,IAAI,CAAC;EAEjC;EAEvC;EACAQ,oBAAoBA,CAACC,MAAe,EAAEC,KAAA,GAAgB,EAAE;IACtD;IACAC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3D,OAAO,IAAI,CAACC,4BAA4B,CAACH,KAAK,CAAC;IAE/C;;;;;;;;;;;;;EAcF;EAEA;EACAI,mBAAmBA,CAACC,QAAiB,EAAEL,KAAA,GAAgB,EAAE;IACvD,MAAMM,MAAM,GAAG,IAAIC,eAAe,EAAE;IACpC,IAAIF,QAAQ,EAAEC,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;IACjDC,MAAM,CAACE,MAAM,CAAC,OAAO,EAAER,KAAK,CAACS,QAAQ,EAAE,CAAC;IAExC,OAAO,IAAI,CAACd,IAAI,CAACe,GAAG,CAAM,GAAG,IAAI,CAACd,MAAM,6BAA6BU,MAAM,CAACG,QAAQ,EAAE,EAAE,CAAC,CACtFE,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDtB,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD;MACA,OAAO,IAAI,CAACC,2BAA2B,CAAChB,KAAK,CAAC;IAChD,CAAC,CAAC,CACH;EACL;EAEA;EACAiB,kBAAkBA,CAACC,SAAiB,EAAElB,KAAA,GAAgB,CAAC;IACrD,OAAO,IAAI,CAACL,IAAI,CAACe,GAAG,CAAM,GAAG,IAAI,CAACd,MAAM,4BAA4BsB,SAAS,UAAUlB,KAAK,EAAE,CAAC,CAC5FW,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDtB,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI,CAACI,0BAA0B,CAACnB,KAAK,CAAC;IAC/C,CAAC,CAAC,CACH;EACL;EAEA;EACAoB,iBAAiBA,CAACrB,MAAc,EAAEC,KAAA,GAAgB,CAAC;IACjD,OAAO,IAAI,CAACL,IAAI,CAACe,GAAG,CAAM,GAAG,IAAI,CAACd,MAAM,2BAA2BG,MAAM,UAAUC,KAAK,EAAE,CAAC,CACxFW,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDtB,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,IAAI,CAACM,yBAAyB,CAACrB,KAAK,CAAC;IAC9C,CAAC,CAAC,CACH;EACL;EAEA;EACAsB,gBAAgBA,CAACJ,SAAiB,EAAEb,QAAgB,EAAEkB,QAAA,GAAmB,CAAC;IACxE,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAAC5B,MAAM,uBAAuB,EAAE;MAC3DsB,SAAS;MACTb,QAAQ;MACRkB,QAAQ;MACRE,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACf,IAAI,CACLnB,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEAY,WAAWA,CAACC,KAAa,EAAEvB,QAAiB,EAAEwB,cAAA,GAAyB,CAAC;IACtE,OAAO,IAAI,CAAClC,IAAI,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAAC5B,MAAM,yBAAyB,EAAE;MAC7DgC,KAAK;MACLvB,QAAQ;MACRwB,cAAc;MACdJ,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACf,IAAI,CACLnB,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEAe,aAAaA,CAACZ,SAAiB,EAAEb,QAAgB,EAAE0B,KAAa;IAC9D,OAAO,IAAI,CAACpC,IAAI,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAAC5B,MAAM,2BAA2B,EAAE;MAC/DsB,SAAS;MACTb,QAAQ;MACR0B,KAAK;MACLN,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACf,IAAI,CACLnB,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEA;EACAiB,gBAAgBA,CAACjC,MAAc;IAC7B,OAAO,IAAI,CAACJ,IAAI,CAACe,GAAG,CAAM,GAAG,IAAI,CAACd,MAAM,mBAAmBG,MAAM,EAAE,CAAC,CACjEY,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACmB,mBAAmB,CAAClC,MAAM,CAAC,CAAC,EACpFP,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,CAAC,IAAI,CAACkB,mBAAmB,CAAClC,MAAM,CAAC,CAAC;IAC3C,CAAC,CAAC,CACH;EACL;EAEA;EACAmC,0BAA0BA,CAAC7B,QAAgB,EAAEL,KAAA,GAAgB,CAAC;IAC5D,OAAO,IAAI,CAACL,IAAI,CAACe,GAAG,CAAM,GAAG,IAAI,CAACd,MAAM,6BAA6BS,QAAQ,UAAUL,KAAK,EAAE,CAAC,CAC5FW,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDtB,UAAU,CAACuB,KAAK,IAAG;MACjBd,OAAO,CAACc,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE;MACA,OAAO,IAAI,CAACoB,2BAA2B,CAAC9B,QAAQ,EAAEL,KAAK,CAAC;IAC1D,CAAC,CAAC,CACH;EACL;EAEA;EACQG,4BAA4BA,CAACH,KAAa;IAChD;IACA,MAAMoC,YAAY,GAA4B,CAC5C;MACEC,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE,kDAAkD;MAC/DR,KAAK,EAAE,GAAG;MACVS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC/HxC,QAAQ,EAAE,KAAK;MACfyC,WAAW,EAAE,QAAQ;MACrBC,KAAK,EAAE,aAAa;MACpBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;MACtCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,IAAI;MACzBC,oBAAoB,EAAE;KACvB,CACF;IACD,OAAO,IAAIlE,UAAU,CAACmE,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAACrB,YAAY,CAACsB,KAAK,CAAC,CAAC,EAAE1D,KAAK,CAAC,CAAC;MAC3CwD,QAAQ,CAACG,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQ3C,2BAA2BA,CAAChB,KAAa;IAC/C,MAAM4D,YAAY,GAAsB,CACtC;MACEvB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,4CAA4C;MACzDR,KAAK,EAAE,IAAI;MACXS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,cAAc;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC7HxC,QAAQ,EAAE,OAAO;MACjByC,WAAW,EAAE,SAAS;MACtBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnCC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;MACrCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBQ,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,uBAAuB;MACvCC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE;KACjB,EACD;MACE7B,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE,0CAA0C;MACvDR,KAAK,EAAE,GAAG;MACVS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC/HxC,QAAQ,EAAE,KAAK;MACfyC,WAAW,EAAE,QAAQ;MACrBC,KAAK,EAAE,aAAa;MACpBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;MACvCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBQ,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,uBAAuB;MACvCC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,GAAG;MACfC,cAAc,EAAE;KACjB,EACD;MACE7B,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,sBAAsB;MAC5BC,WAAW,EAAE,sCAAsC;MACnDR,KAAK,EAAE,IAAI;MACXS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,cAAc;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC7HxC,QAAQ,EAAE,OAAO;MACjByC,WAAW,EAAE,QAAQ;MACrBC,KAAK,EAAE,YAAY;MACnBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,IAAI,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,SAAS,CAAC;MAC1CC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBQ,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,0BAA0B;MAC1CC,SAAS,EAAE,IAAI;MACfC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,GAAG;MACfC,cAAc,EAAE;KACjB,CACF;IACD,OAAO,IAAI7E,UAAU,CAACmE,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAACG,YAAY,CAACF,KAAK,CAAC,CAAC,EAAE1D,KAAK,CAAC,CAAC;MAC3CwD,QAAQ,CAACG,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQxC,0BAA0BA,CAACnB,KAAa;IAC9C,OAAO,IAAI,CAACG,4BAA4B,CAACH,KAAK,CAAC;EACjD;EAEQqB,yBAAyBA,CAACrB,KAAa;IAC7C,OAAO,IAAI,CAACG,4BAA4B,CAACH,KAAK,CAAC;EACjD;EAEQmC,2BAA2BA,CAAC9B,QAAgB,EAAEL,KAAa;IACjE,OAAO,IAAI,CAACG,4BAA4B,CAACH,KAAK,CAAC;EACjD;EAEQiC,mBAAmBA,CAAClC,MAAc;IACxC,OAAO;MACLA,MAAM;MACNoE,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MACjBC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC;MACpDC,UAAU,EAAE;QAAEC,GAAG,EAAE,GAAG;QAAEC,GAAG,EAAE;MAAI,CAAE;MACnCC,gBAAgB,EAAE;KACnB;EACH;EAEA;EACAC,mBAAmBA,CAACC,SAAwB;IAC1C,IAAI,CAACjF,cAAc,CAAC4D,IAAI,CAACqB,SAAS,CAAC;EACrC;EAEA;EACAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAClF,cAAc,CAACmF,YAAY,EAAE;EAC3C;;;uBAnRWvF,qBAAqB,EAAAwF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArB3F,qBAAqB;MAAA4F,OAAA,EAArB5F,qBAAqB,CAAA6F,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}