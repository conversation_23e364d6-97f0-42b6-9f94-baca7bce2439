{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { r as raf, a as addEventListener, b as removeEventListener, k as inheritAttributes } from './helpers-be245865.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst segmentIosCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.065);border-radius:8px;overflow:hidden;z-index:0}:host(.ion-color){background:rgba(var(--ion-color-base-rgb), 0.065)}:host(.in-toolbar){-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:auto}:host(.in-toolbar:not(.ion-color)){background:var(--ion-toolbar-segment-background, var(--background))}:host(.in-toolbar-color:not(.ion-color)){background:rgba(var(--ion-color-contrast-rgb), 0.11)}\";\nconst IonSegmentIosStyle0 = segmentIosCss;\nconst segmentMdCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:transparent;grid-auto-columns:minmax(auto, 360px)}:host(.in-toolbar){min-height:var(--min-height)}:host(.segment-scrollable) ::slotted(ion-segment-button){min-width:auto}\";\nconst IonSegmentMdStyle0 = segmentMdCss;\nconst Segment = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionSelect = createEvent(this, \"ionSelect\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.onClick = ev => {\n      const current = ev.target;\n      const previous = this.checked;\n      // If the current element is a segment then that means\n      // the user tried to swipe to a segment button and\n      // click a segment button at the same time so we should\n      // not update the checked segment button\n      if (current.tagName === 'ION-SEGMENT') {\n        return;\n      }\n      this.value = current.value;\n      if (current !== previous) {\n        this.emitValueChange();\n      }\n      if (this.scrollable || !this.swipeGesture) {\n        if (previous) {\n          this.checkButton(previous, current);\n        } else {\n          this.setCheckedClasses();\n        }\n      }\n    };\n    this.getSegmentButton = selector => {\n      var _a, _b;\n      const buttons = this.getButtons().filter(button => !button.disabled);\n      const currIndex = buttons.findIndex(button => button === document.activeElement);\n      switch (selector) {\n        case 'first':\n          return buttons[0];\n        case 'last':\n          return buttons[buttons.length - 1];\n        case 'next':\n          return (_a = buttons[currIndex + 1]) !== null && _a !== void 0 ? _a : buttons[0];\n        case 'previous':\n          return (_b = buttons[currIndex - 1]) !== null && _b !== void 0 ? _b : buttons[buttons.length - 1];\n        default:\n          return null;\n      }\n    };\n    this.activated = false;\n    this.color = undefined;\n    this.disabled = false;\n    this.scrollable = false;\n    this.swipeGesture = true;\n    this.value = undefined;\n    this.selectOnFocus = false;\n  }\n  colorChanged(value, oldValue) {\n    /**\n     * If color is set after not having\n     * previously been set (or vice versa),\n     * we need to emit style so the segment-buttons\n     * can apply their color classes properly.\n     */\n    if (oldValue === undefined && value !== undefined || oldValue !== undefined && value === undefined) {\n      this.emitStyle();\n    }\n  }\n  swipeGestureChanged() {\n    this.gestureChanged();\n  }\n  valueChanged(value) {\n    /**\n     * `ionSelect` is emitted every time the value changes (internal or external changes).\n     * Used by `ion-segment-button` to determine if the button should be checked.\n     */\n    this.ionSelect.emit({\n      value\n    });\n    this.scrollActiveButtonIntoView();\n  }\n  disabledChanged() {\n    this.gestureChanged();\n    const buttons = this.getButtons();\n    for (const button of buttons) {\n      button.disabled = this.disabled;\n    }\n  }\n  gestureChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.scrollable && !this.disabled && this.swipeGesture);\n    }\n  }\n  connectedCallback() {\n    this.emitStyle();\n  }\n  componentWillLoad() {\n    this.emitStyle();\n  }\n  componentDidLoad() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.setCheckedClasses();\n      /**\n       * If the value changes before watchers\n       * are setup, then the ionSelect watch callback\n       * will not fire. As a result, we manually\n       * fire this event when Select is loaded.\n       */\n      _this.ionSelect.emit({\n        value: _this.value\n      });\n      /**\n       * We need to wait for the buttons to all be rendered\n       * before we can scroll.\n       */\n      raf(() => {\n        /**\n         * When the segment loads for the first\n         * time we just want to snap the active button into\n         * place instead of scroll. Smooth scrolling should only\n         * happen when the user interacts with the segment.\n         */\n        _this.scrollActiveButtonIntoView(false);\n      });\n      _this.gesture = (yield import('./index-2cf77112.js')).createGesture({\n        el: _this.el,\n        gestureName: 'segment',\n        gesturePriority: 100,\n        threshold: 0,\n        passive: false,\n        onStart: ev => _this.onStart(ev),\n        onMove: ev => _this.onMove(ev),\n        onEnd: ev => _this.onEnd(ev)\n      });\n      _this.gestureChanged();\n      if (_this.disabled) {\n        _this.disabledChanged();\n      }\n    })();\n  }\n  onStart(detail) {\n    this.valueBeforeGesture = this.value;\n    this.activate(detail);\n  }\n  onMove(detail) {\n    this.setNextIndex(detail);\n  }\n  onEnd(detail) {\n    this.setActivated(false);\n    this.setNextIndex(detail, true);\n    detail.event.stopImmediatePropagation();\n    const value = this.value;\n    if (value !== undefined) {\n      if (this.valueBeforeGesture !== value) {\n        this.emitValueChange();\n      }\n    }\n    this.valueBeforeGesture = undefined;\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange() {\n    const {\n      value\n    } = this;\n    this.ionChange.emit({\n      value\n    });\n  }\n  getButtons() {\n    return Array.from(this.el.querySelectorAll('ion-segment-button'));\n  }\n  get checked() {\n    return this.getButtons().find(button => button.value === this.value);\n  }\n  /*\n   * Activate both the segment and the buttons\n   * due to a bug with ::slotted in Safari\n   */\n  setActivated(activated) {\n    const buttons = this.getButtons();\n    buttons.forEach(button => {\n      if (activated) {\n        button.classList.add('segment-button-activated');\n      } else {\n        button.classList.remove('segment-button-activated');\n      }\n    });\n    this.activated = activated;\n  }\n  activate(detail) {\n    const clicked = detail.event.target;\n    const buttons = this.getButtons();\n    const checked = buttons.find(button => button.value === this.value);\n    // Make sure we are only checking for activation on a segment button\n    // since disabled buttons will get the click on the segment\n    if (clicked.tagName !== 'ION-SEGMENT-BUTTON') {\n      return;\n    }\n    // If there are no checked buttons, set the current button to checked\n    if (!checked) {\n      this.value = clicked.value;\n      this.setCheckedClasses();\n    }\n    // If the gesture began on the clicked button with the indicator\n    // then we should activate the indicator\n    if (this.value === clicked.value) {\n      this.setActivated(true);\n    }\n  }\n  getIndicator(button) {\n    const root = button.shadowRoot || button;\n    return root.querySelector('.segment-button-indicator');\n  }\n  checkButton(previous, current) {\n    const previousIndicator = this.getIndicator(previous);\n    const currentIndicator = this.getIndicator(current);\n    if (previousIndicator === null || currentIndicator === null) {\n      return;\n    }\n    const previousClientRect = previousIndicator.getBoundingClientRect();\n    const currentClientRect = currentIndicator.getBoundingClientRect();\n    const widthDelta = previousClientRect.width / currentClientRect.width;\n    const xPosition = previousClientRect.left - currentClientRect.left;\n    // Scale the indicator width to match the previous indicator width\n    // and translate it on top of the previous indicator\n    const transform = `translate3d(${xPosition}px, 0, 0) scaleX(${widthDelta})`;\n    writeTask(() => {\n      // Remove the transition before positioning on top of the previous indicator\n      currentIndicator.classList.remove('segment-button-indicator-animated');\n      currentIndicator.style.setProperty('transform', transform);\n      // Force a repaint to ensure the transform happens\n      currentIndicator.getBoundingClientRect();\n      // Add the transition to move the indicator into place\n      currentIndicator.classList.add('segment-button-indicator-animated');\n      // Remove the transform to slide the indicator back to the button clicked\n      currentIndicator.style.setProperty('transform', '');\n    });\n    this.value = current.value;\n    this.setCheckedClasses();\n  }\n  setCheckedClasses() {\n    const buttons = this.getButtons();\n    const index = buttons.findIndex(button => button.value === this.value);\n    const next = index + 1;\n    for (const button of buttons) {\n      button.classList.remove('segment-button-after-checked');\n    }\n    if (next < buttons.length) {\n      buttons[next].classList.add('segment-button-after-checked');\n    }\n  }\n  scrollActiveButtonIntoView(smoothScroll = true) {\n    const {\n      scrollable,\n      value,\n      el\n    } = this;\n    if (scrollable) {\n      const buttons = this.getButtons();\n      const activeButton = buttons.find(button => button.value === value);\n      if (activeButton !== undefined) {\n        const scrollContainerBox = el.getBoundingClientRect();\n        const activeButtonBox = activeButton.getBoundingClientRect();\n        /**\n         * Subtract the active button x position from the scroll\n         * container x position. This will give us the x position\n         * of the active button within the scroll container.\n         */\n        const activeButtonLeft = activeButtonBox.x - scrollContainerBox.x;\n        /**\n         * If we just used activeButtonLeft, then the active button\n         * would be aligned with the left edge of the scroll container.\n         * Instead, we want the segment button to be centered. As a result,\n         * we subtract half of the scroll container width. This will position\n         * the left edge of the active button at the midpoint of the scroll container.\n         * We then add half of the active button width. This will position the active\n         * button such that the midpoint of the active button is at the midpoint of the\n         * scroll container.\n         */\n        const centeredX = activeButtonLeft - scrollContainerBox.width / 2 + activeButtonBox.width / 2;\n        /**\n         * We intentionally use scrollBy here instead of scrollIntoView\n         * to avoid a WebKit bug where accelerated animations break\n         * when using scrollIntoView. Using scrollIntoView will cause the\n         * segment container to jump during the transition and then snap into place.\n         * This is because scrollIntoView can potentially cause parent element\n         * containers to also scroll. scrollBy does not have this same behavior, so\n         * we use this API instead.\n         *\n         * Note that if there is not enough scrolling space to center the element\n         * within the scroll container, the browser will attempt\n         * to center by as much as it can.\n         */\n        el.scrollBy({\n          top: 0,\n          left: centeredX,\n          behavior: smoothScroll ? 'smooth' : 'instant'\n        });\n      }\n    }\n  }\n  setNextIndex(detail, isEnd = false) {\n    const rtl = isRTL(this.el);\n    const activated = this.activated;\n    const buttons = this.getButtons();\n    const index = buttons.findIndex(button => button.value === this.value);\n    const previous = buttons[index];\n    let current;\n    let nextIndex;\n    if (index === -1) {\n      return;\n    }\n    // Get the element that the touch event started on in case\n    // it was the checked button, then we will move the indicator\n    const rect = previous.getBoundingClientRect();\n    const left = rect.left;\n    const width = rect.width;\n    // Get the element that the gesture is on top of based on the currentX of the\n    // gesture event and the Y coordinate of the starting element, since the gesture\n    // can move up and down off of the segment\n    const currentX = detail.currentX;\n    const previousY = rect.top + rect.height / 2;\n    /**\n     * Segment can be used inside the shadow dom\n     * so doing document.elementFromPoint would never\n     * return a segment button in that instance.\n     * We use getRootNode to which will return the parent\n     * shadow root if used inside a shadow component and\n     * returns document otherwise.\n     */\n    const root = this.el.getRootNode();\n    const nextEl = root.elementFromPoint(currentX, previousY);\n    const decreaseIndex = rtl ? currentX > left + width : currentX < left;\n    const increaseIndex = rtl ? currentX < left : currentX > left + width;\n    // If the indicator is currently activated then we have started the gesture\n    // on top of the checked button so we need to slide the indicator\n    // by checking the button next to it as we move\n    if (activated && !isEnd) {\n      // Decrease index, move left in LTR & right in RTL\n      if (decreaseIndex) {\n        const newIndex = index - 1;\n        if (newIndex >= 0) {\n          nextIndex = newIndex;\n        }\n        // Increase index, moves right in LTR & left in RTL\n      } else if (increaseIndex) {\n        if (activated && !isEnd) {\n          const newIndex = index + 1;\n          if (newIndex < buttons.length) {\n            nextIndex = newIndex;\n          }\n        }\n      }\n      if (nextIndex !== undefined && !buttons[nextIndex].disabled) {\n        current = buttons[nextIndex];\n      }\n    }\n    // If the indicator is not activated then we will just set the indicator\n    // to the element where the gesture ended\n    if (!activated && isEnd) {\n      current = nextEl;\n    }\n    if (current != null) {\n      /**\n       * If current element is ion-segment then that means\n       * user tried to select a disabled ion-segment-button,\n       * and we should not update the ripple.\n       */\n      if (current.tagName === 'ION-SEGMENT') {\n        return false;\n      }\n      if (previous !== current) {\n        this.checkButton(previous, current);\n      }\n    }\n    return true;\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      segment: true\n    });\n  }\n  onKeyDown(ev) {\n    const rtl = isRTL(this.el);\n    let keyDownSelectsButton = this.selectOnFocus;\n    let current;\n    switch (ev.key) {\n      case 'ArrowRight':\n        ev.preventDefault();\n        current = rtl ? this.getSegmentButton('previous') : this.getSegmentButton('next');\n        break;\n      case 'ArrowLeft':\n        ev.preventDefault();\n        current = rtl ? this.getSegmentButton('next') : this.getSegmentButton('previous');\n        break;\n      case 'Home':\n        ev.preventDefault();\n        current = this.getSegmentButton('first');\n        break;\n      case 'End':\n        ev.preventDefault();\n        current = this.getSegmentButton('last');\n        break;\n      case ' ':\n      case 'Enter':\n        ev.preventDefault();\n        current = document.activeElement;\n        keyDownSelectsButton = true;\n    }\n    if (!current) {\n      return;\n    }\n    if (keyDownSelectsButton) {\n      const previous = this.checked;\n      this.checkButton(previous || current, current);\n      if (current !== previous) {\n        this.emitValueChange();\n      }\n    }\n    current.setFocus();\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '01c94f9fcc51fb5a7612588c87155a130b7c8a9d',\n      role: \"tablist\",\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'segment-activated': this.activated,\n        'segment-disabled': this.disabled,\n        'segment-scrollable': this.scrollable\n      })\n    }, h(\"slot\", {\n      key: 'b0afdfcb7506a765df42d1d6eb4e1d81bf8fd92a'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"color\": [\"colorChanged\"],\n      \"swipeGesture\": [\"swipeGestureChanged\"],\n      \"value\": [\"valueChanged\"],\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nSegment.style = {\n  ios: IonSegmentIosStyle0,\n  md: IonSegmentMdStyle0\n};\nconst segmentButtonIosCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:none;--background-hover-opacity:0;--background-focused:none;--background-focused-opacity:0;--border-radius:7px;--border-width:1px;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.12);--border-style:solid;--indicator-box-shadow:0 0 5px rgba(0, 0, 0, 0.16);--indicator-color:var(--ion-color-step-350, var(--ion-background-color, #fff));--indicator-height:100%;--indicator-transition:transform 260ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--transition:100ms all linear;--padding-top:0;--padding-end:13px;--padding-bottom:0;--padding-start:13px;margin-top:2px;margin-bottom:2px;position:relative;-ms-flex-direction:row;flex-direction:row;min-width:70px;min-height:28px;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);font-size:13px;font-weight:450;line-height:37px}:host::before{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;-webkit-transition:160ms opacity ease-in-out;transition:160ms opacity ease-in-out;-webkit-transition-delay:100ms;transition-delay:100ms;border-left:var(--border-width) var(--border-style) var(--border-color);content:\\\"\\\";opacity:1;will-change:opacity}:host(:first-of-type)::before{border-left-color:transparent}:host(.segment-button-disabled){opacity:0.3}::slotted(ion-icon){font-size:24px}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:2px;margin-inline-end:2px}.segment-button-indicator{-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;left:0;right:0;top:0;bottom:0}.segment-button-indicator-background{border-radius:var(--border-radius);background:var(--indicator-color)}.segment-button-indicator-background{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked)::before,:host(.segment-button-after-checked)::before{opacity:0}:host(.segment-button-checked){z-index:-1}:host(.segment-button-activated){--indicator-transform:scale(0.95)}:host(.ion-focused) .button-native{opacity:0.7}@media (any-hover: hover){:host(:hover) .button-native{opacity:0.5}:host(.segment-button-checked:hover) .button-native{opacity:1}}:host(.in-segment-color){background:none;color:var(--ion-text-color, #000)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-step-350, var(--ion-background-color, #fff))}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native,:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-text-color, #000)}}:host(.in-toolbar:not(.in-segment-color)){--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, var(--ion-toolbar-color), initial);--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-toolbar-color), initial);--indicator-color:var(--ion-toolbar-segment-indicator-color, var(--ion-color-step-350, var(--ion-background-color, #fff)))}:host(.in-toolbar-color) .segment-button-indicator-background{background:var(--ion-color-contrast)}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color):hover) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color):hover) .button-native{color:var(--ion-color-base)}}\";\nconst IonSegmentButtonIosStyle0 = segmentButtonIosCss;\nconst segmentButtonMdCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:var(--color-checked);--background-focused:var(--color-checked);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #3880ff);--indicator-box-shadow:none;--indicator-color:var(--color-checked);--indicator-height:2px;--indicator-transition:transform 250ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--padding-top:0;--padding-end:16px;--padding-bottom:0;--padding-start:16px;--transition:color 0.15s linear 0s, opacity 0.15s linear 0s;min-width:90px;min-height:48px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);font-size:14px;font-weight:500;letter-spacing:0.06em;line-height:40px;text-transform:uppercase}:host(.segment-button-disabled){opacity:0.3}:host(.in-segment-color){background:none;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color) ion-ripple-effect{color:var(--ion-color-base)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked) .button-native{color:var(--ion-color-base)}:host(.in-segment-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color:hover) .button-native::after{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-segment-color)){--background:var(--ion-toolbar-segment-background, none);--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6));--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-color-primary, #3880ff));--indicator-color:var(--ion-toolbar-segment-color-checked, var(--color-checked))}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:rgba(var(--ion-color-contrast-rgb), 0.6)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color)) .button-native::after{background:var(--ion-color-contrast)}}::slotted(ion-icon){margin-top:12px;margin-bottom:12px;font-size:24px}::slotted(ion-label){margin-top:12px;margin-bottom:12px}:host(.segment-button-layout-icon-top) ::slotted(ion-label),:host(.segment-button-layout-icon-bottom) ::slotted(ion-icon){margin-top:0}:host(.segment-button-layout-icon-top) ::slotted(ion-icon),:host(.segment-button-layout-icon-bottom) ::slotted(ion-label){margin-bottom:0}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px}:host(.segment-button-has-icon-only) ::slotted(ion-icon){margin-top:12px;margin-bottom:12px}:host(.segment-button-has-label-only) ::slotted(ion-label){margin-top:12px;margin-bottom:12px}.segment-button-indicator{left:0;right:0;bottom:0}.segment-button-indicator-background{background:var(--indicator-color)}:host(.in-toolbar:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-toolbar-segment-indicator-color, var(--indicator-color))}:host(.in-toolbar-color:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-color-contrast)}\";\nconst IonSegmentButtonMdStyle0 = segmentButtonMdCss;\nlet ids = 0;\nconst SegmentButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.segmentEl = null;\n    this.inheritedAttributes = {};\n    this.updateStyle = () => {\n      forceUpdate(this);\n    };\n    this.updateState = () => {\n      const {\n        segmentEl\n      } = this;\n      if (segmentEl) {\n        this.checked = segmentEl.value === this.value;\n        if (segmentEl.disabled) {\n          this.disabled = true;\n        }\n      }\n    };\n    this.checked = false;\n    this.disabled = false;\n    this.layout = 'icon-top';\n    this.type = 'button';\n    this.value = 'ion-sb-' + ids++;\n  }\n  valueChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    const segmentEl = this.segmentEl = this.el.closest('ion-segment');\n    if (segmentEl) {\n      this.updateState();\n      addEventListener(segmentEl, 'ionSelect', this.updateState);\n      addEventListener(segmentEl, 'ionStyle', this.updateStyle);\n    }\n  }\n  disconnectedCallback() {\n    const segmentEl = this.segmentEl;\n    if (segmentEl) {\n      removeEventListener(segmentEl, 'ionSelect', this.updateState);\n      removeEventListener(segmentEl, 'ionStyle', this.updateStyle);\n      this.segmentEl = null;\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['aria-label']));\n  }\n  get hasLabel() {\n    return !!this.el.querySelector('ion-label');\n  }\n  get hasIcon() {\n    return !!this.el.querySelector('ion-icon');\n  }\n  /**\n   * @internal\n   * Focuses the native <button> element\n   * inside of ion-segment-button.\n   */\n  setFocus() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        nativeEl\n      } = _this2;\n      if (nativeEl !== undefined) {\n        nativeEl.focus();\n      }\n    })();\n  }\n  render() {\n    const {\n      checked,\n      type,\n      disabled,\n      hasIcon,\n      hasLabel,\n      layout,\n      segmentEl\n    } = this;\n    const mode = getIonMode(this);\n    const hasSegmentColor = () => (segmentEl === null || segmentEl === void 0 ? void 0 : segmentEl.color) !== undefined;\n    return h(Host, {\n      key: 'daed55711fdb550747d2ad5fb6e012bff6f6b176',\n      class: {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'in-segment': hostContext('ion-segment', this.el),\n        'in-segment-color': hasSegmentColor(),\n        'segment-button-has-label': hasLabel,\n        'segment-button-has-icon': hasIcon,\n        'segment-button-has-label-only': hasLabel && !hasIcon,\n        'segment-button-has-icon-only': hasIcon && !hasLabel,\n        'segment-button-disabled': disabled,\n        'segment-button-checked': checked,\n        [`segment-button-layout-${layout}`]: true,\n        'ion-activatable': true,\n        'ion-activatable-instant': true,\n        'ion-focusable': true\n      }\n    }, h(\"button\", Object.assign({\n      key: '851a4b8ff7e45f5fc559c4d8e9c3102441dbd10e',\n      \"aria-selected\": checked ? 'true' : 'false',\n      role: \"tab\",\n      ref: el => this.nativeEl = el,\n      type: type,\n      class: \"button-native\",\n      part: \"native\",\n      disabled: disabled\n    }, this.inheritedAttributes), h(\"span\", {\n      key: 'b2dc87747e81ce49dfa1269355027d9380226a6a',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: '00e21fdcafb533605518beebb9557c724fc88e76'\n    })), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '358c90ab1b43aae3bc6eb1e58fb38d4892894a68'\n    })), h(\"div\", {\n      key: '20fd3f7c43dd117541314e852d894af855050707',\n      part: \"indicator\",\n      class: {\n        'segment-button-indicator': true,\n        'segment-button-indicator-animated': true\n      }\n    }, h(\"div\", {\n      key: 'bebdeb84466c404d52ce48feda1ece5fb1f202b0',\n      part: \"indicator-background\",\n      class: \"segment-button-indicator-background\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nSegmentButton.style = {\n  ios: IonSegmentButtonIosStyle0,\n  md: IonSegmentButtonMdStyle0\n};\nexport { Segment as ion_segment, SegmentButton as ion_segment_button };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}