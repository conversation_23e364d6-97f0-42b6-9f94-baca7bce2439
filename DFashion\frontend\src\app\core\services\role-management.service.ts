import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type UserRole = 
  | 'super_admin' 
  | 'admin' 
  | 'sales_manager' 
  | 'sales_executive' 
  | 'marketing_manager' 
  | 'marketing_executive' 
  | 'account_manager' 
  | 'accountant' 
  | 'support_manager' 
  | 'support_agent' 
  | 'content_manager' 
  | 'vendor_manager';

export type Department = 
  | 'administration' 
  | 'sales' 
  | 'marketing' 
  | 'accounting' 
  | 'support' 
  | 'content' 
  | 'vendor_management';

export interface RoleConfig {
  role: UserRole;
  department: Department;
  hierarchy: number; // 1 = highest authority, 10 = lowest
  displayName: string;
  description: string;
  color: string;
  icon: string;
  permissions: Permission[];
  profileLayout: ProfileLayoutType;
  dashboardWidgets: string[];
}

export interface Permission {
  module: string;
  actions: string[];
  scope: 'global' | 'department' | 'team' | 'self';
}

export type ProfileLayoutType = 
  | 'executive' 
  | 'manager' 
  | 'specialist' 
  | 'admin';

@Injectable({
  providedIn: 'root'
})
export class RoleManagementService {
  private currentUserRoleSubject = new BehaviorSubject<UserRole | null>(null);
  public currentUserRole$ = this.currentUserRoleSubject.asObservable();

  private readonly roleConfigurations: Record<UserRole, RoleConfig> = {
    super_admin: {
      role: 'super_admin',
      department: 'administration',
      hierarchy: 1,
      displayName: 'Super Administrator',
      description: 'Full system access and control',
      color: '#FF6B6B',
      icon: 'fas fa-crown',
      permissions: [
        { module: '*', actions: ['*'], scope: 'global' }
      ],
      profileLayout: 'admin',
      dashboardWidgets: ['system_overview', 'user_management', 'analytics', 'security', 'audit_logs']
    },
    admin: {
      role: 'admin',
      department: 'administration',
      hierarchy: 2,
      displayName: 'Administrator',
      description: 'System administration and user management',
      color: '#4ECDC4',
      icon: 'fas fa-user-shield',
      permissions: [
        { module: 'users', actions: ['create', 'read', 'update', 'delete'], scope: 'global' },
        { module: 'roles', actions: ['read', 'update'], scope: 'global' },
        { module: 'system', actions: ['read', 'update'], scope: 'global' }
      ],
      profileLayout: 'admin',
      dashboardWidgets: ['user_stats', 'role_management', 'system_health', 'recent_activities']
    },
    sales_manager: {
      role: 'sales_manager',
      department: 'sales',
      hierarchy: 3,
      displayName: 'Sales Manager',
      description: 'Sales team leadership and strategy',
      color: '#45B7D1',
      icon: 'fas fa-chart-line',
      permissions: [
        { module: 'sales', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },
        { module: 'customers', actions: ['read', 'update'], scope: 'department' },
        { module: 'reports', actions: ['read'], scope: 'department' },
        { module: 'team', actions: ['read', 'update'], scope: 'department' }
      ],
      profileLayout: 'manager',
      dashboardWidgets: ['sales_overview', 'team_performance', 'revenue_trends', 'customer_insights']
    },
    sales_executive: {
      role: 'sales_executive',
      department: 'sales',
      hierarchy: 6,
      displayName: 'Sales Executive',
      description: 'Direct sales and customer relationship management',
      color: '#96CEB4',
      icon: 'fas fa-handshake',
      permissions: [
        { module: 'sales', actions: ['create', 'read', 'update'], scope: 'self' },
        { module: 'customers', actions: ['create', 'read', 'update'], scope: 'self' },
        { module: 'leads', actions: ['create', 'read', 'update'], scope: 'self' }
      ],
      profileLayout: 'specialist',
      dashboardWidgets: ['my_sales', 'my_customers', 'targets', 'commission']
    },
    marketing_manager: {
      role: 'marketing_manager',
      department: 'marketing',
      hierarchy: 3,
      displayName: 'Marketing Manager',
      description: 'Marketing strategy and campaign management',
      color: '#F38BA8',
      icon: 'fas fa-bullhorn',
      permissions: [
        { module: 'marketing', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },
        { module: 'campaigns', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },
        { module: 'content', actions: ['read', 'update'], scope: 'department' },
        { module: 'analytics', actions: ['read'], scope: 'department' }
      ],
      profileLayout: 'manager',
      dashboardWidgets: ['campaign_performance', 'audience_insights', 'content_metrics', 'roi_analysis']
    },
    marketing_executive: {
      role: 'marketing_executive',
      department: 'marketing',
      hierarchy: 6,
      displayName: 'Marketing Executive',
      description: 'Campaign execution and content creation',
      color: '#DDA0DD',
      icon: 'fas fa-palette',
      permissions: [
        { module: 'campaigns', actions: ['create', 'read', 'update'], scope: 'self' },
        { module: 'content', actions: ['create', 'read', 'update'], scope: 'self' },
        { module: 'social_media', actions: ['create', 'read', 'update'], scope: 'self' }
      ],
      profileLayout: 'specialist',
      dashboardWidgets: ['my_campaigns', 'content_calendar', 'social_metrics', 'creative_assets']
    },
    account_manager: {
      role: 'account_manager',
      department: 'accounting',
      hierarchy: 4,
      displayName: 'Account Manager',
      description: 'Financial oversight and accounting management',
      color: '#FFD93D',
      icon: 'fas fa-calculator',
      permissions: [
        { module: 'accounting', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },
        { module: 'financial_reports', actions: ['create', 'read'], scope: 'department' },
        { module: 'budgets', actions: ['create', 'read', 'update'], scope: 'department' }
      ],
      profileLayout: 'manager',
      dashboardWidgets: ['financial_overview', 'budget_tracking', 'expense_analysis', 'profit_margins']
    },
    accountant: {
      role: 'accountant',
      department: 'accounting',
      hierarchy: 7,
      displayName: 'Accountant',
      description: 'Financial record keeping and transaction management',
      color: '#6BCF7F',
      icon: 'fas fa-file-invoice-dollar',
      permissions: [
        { module: 'transactions', actions: ['create', 'read', 'update'], scope: 'self' },
        { module: 'invoices', actions: ['create', 'read', 'update'], scope: 'self' },
        { module: 'expenses', actions: ['create', 'read', 'update'], scope: 'self' }
      ],
      profileLayout: 'specialist',
      dashboardWidgets: ['daily_transactions', 'pending_invoices', 'expense_tracker', 'tax_summary']
    },
    support_manager: {
      role: 'support_manager',
      department: 'support',
      hierarchy: 4,
      displayName: 'Support Manager',
      description: 'Customer support team leadership',
      color: '#FF8C42',
      icon: 'fas fa-headset',
      permissions: [
        { module: 'support', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },
        { module: 'tickets', actions: ['read', 'update', 'assign'], scope: 'department' },
        { module: 'knowledge_base', actions: ['create', 'read', 'update'], scope: 'department' }
      ],
      profileLayout: 'manager',
      dashboardWidgets: ['support_overview', 'ticket_analytics', 'team_workload', 'satisfaction_scores']
    },
    support_agent: {
      role: 'support_agent',
      department: 'support',
      hierarchy: 8,
      displayName: 'Support Agent',
      description: 'Direct customer support and issue resolution',
      color: '#A8E6CF',
      icon: 'fas fa-life-ring',
      permissions: [
        { module: 'tickets', actions: ['read', 'update'], scope: 'self' },
        { module: 'knowledge_base', actions: ['read'], scope: 'department' },
        { module: 'customer_communication', actions: ['create', 'read'], scope: 'self' }
      ],
      profileLayout: 'specialist',
      dashboardWidgets: ['my_tickets', 'response_times', 'customer_feedback', 'knowledge_search']
    },
    content_manager: {
      role: 'content_manager',
      department: 'content',
      hierarchy: 5,
      displayName: 'Content Manager',
      description: 'Content strategy and editorial oversight',
      color: '#B19CD9',
      icon: 'fas fa-edit',
      permissions: [
        { module: 'content', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },
        { module: 'editorial', actions: ['create', 'read', 'update'], scope: 'department' },
        { module: 'publishing', actions: ['approve', 'schedule'], scope: 'department' }
      ],
      profileLayout: 'manager',
      dashboardWidgets: ['content_pipeline', 'editorial_calendar', 'engagement_metrics', 'seo_performance']
    },
    vendor_manager: {
      role: 'vendor_manager',
      department: 'vendor_management',
      hierarchy: 5,
      displayName: 'Vendor Manager',
      description: 'Vendor relationship and partnership management',
      color: '#FFB6C1',
      icon: 'fas fa-store',
      permissions: [
        { module: 'vendors', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },
        { module: 'contracts', actions: ['create', 'read', 'update'], scope: 'department' },
        { module: 'vendor_performance', actions: ['read', 'evaluate'], scope: 'department' }
      ],
      profileLayout: 'manager',
      dashboardWidgets: ['vendor_overview', 'contract_status', 'performance_metrics', 'payment_tracking']
    }
  };

  constructor() {}

  setCurrentUserRole(role: UserRole): void {
    this.currentUserRoleSubject.next(role);
  }

  getCurrentUserRole(): UserRole | null {
    return this.currentUserRoleSubject.value;
  }

  getRoleConfig(role: UserRole): RoleConfig {
    return this.roleConfigurations[role];
  }

  getAllRoles(): RoleConfig[] {
    return Object.values(this.roleConfigurations);
  }

  getRolesByDepartment(department: Department): RoleConfig[] {
    return Object.values(this.roleConfigurations)
      .filter(config => config.department === department)
      .sort((a, b) => a.hierarchy - b.hierarchy);
  }

  hasPermission(role: UserRole, module: string, action: string): boolean {
    const config = this.getRoleConfig(role);
    
    // Super admin has all permissions
    if (role === 'super_admin') return true;
    
    return config.permissions.some(permission => {
      const moduleMatch = permission.module === '*' || permission.module === module;
      const actionMatch = permission.actions.includes('*') || permission.actions.includes(action);
      return moduleMatch && actionMatch;
    });
  }

  getAccessibleModules(role: UserRole): string[] {
    const config = this.getRoleConfig(role);
    if (role === 'super_admin') return ['*'];
    
    return config.permissions.map(p => p.module);
  }

  isManager(role: UserRole): boolean {
    return role.includes('_manager') || role === 'super_admin' || role === 'admin';
  }

  getSubordinateRoles(role: UserRole): UserRole[] {
    const config = this.getRoleConfig(role);
    return Object.values(this.roleConfigurations)
      .filter(c => c.department === config.department && c.hierarchy > config.hierarchy)
      .map(c => c.role);
  }

  getDepartmentHierarchy(department: Department): RoleConfig[] {
    return this.getRolesByDepartment(department);
  }
}
