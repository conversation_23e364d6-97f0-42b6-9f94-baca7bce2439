{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./storage.service\";\nimport * as i3 from \"@ionic/angular\";\nexport class CartService {\n  constructor(http, storageService, toastController) {\n    this.http = http;\n    this.storageService = storageService;\n    this.toastController = toastController;\n    this.API_URL = environment.apiUrl;\n    this.cartItems = new BehaviorSubject([]);\n    this.cartSummary = new BehaviorSubject(null);\n    this.cartItemCount = new BehaviorSubject(0);\n    this.cartItems$ = this.cartItems.asObservable();\n    this.cartSummary$ = this.cartSummary.asObservable();\n    this.cartItemCount$ = this.cartItemCount.asObservable();\n    this.loadCart();\n  }\n  // Get cart from API\n  getCart() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart-new`, options);\n  }\n  // Get cart count only (lightweight endpoint)\n  getCartCount() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart-new/count`, options);\n  }\n  // Load cart and update local state\n  loadCart() {\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is logged in - load from API\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      this.loadCartFromStorage();\n    }\n  }\n  // Load cart from API for logged-in users\n  loadCartFromAPI() {\n    this.getCart().subscribe({\n      next: response => {\n        if (response.success && response.cart) {\n          this.cartItems.next(response.cart.items || []);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', response.cart.items?.length || 0, 'items');\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        }\n      },\n      error: error => {\n        console.log('❌ API cart not available, using local storage fallback');\n        this.loadCartFromStorage();\n      }\n    });\n  }\n  loadCartFromStorage() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Wait a bit for storage to initialize\n        yield new Promise(resolve => setTimeout(resolve, 100));\n        const cart = yield _this.storageService.getCart();\n        _this.cartItems.next(cart || []);\n        _this.updateCartCount();\n      } catch (error) {\n        console.error('Error loading cart from storage:', error);\n        _this.cartItems.next([]);\n        _this.updateCartCount();\n      }\n    })();\n  }\n  saveCartToStorage() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.storageService.setCart(_this2.cartItems.value);\n      } catch (error) {\n        console.error('Error saving cart to storage:', error);\n      }\n    })();\n  }\n  updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n  // Method to refresh only cart count (lightweight)\n  refreshCartCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.getCartCount().subscribe({\n        next: response => {\n          if (response.success) {\n            this.cartItemCount.next(response.count);\n            console.log('🛒 Cart count refreshed:', response.count);\n          }\n        },\n        error: error => {\n          console.error('❌ Error refreshing cart count:', error);\n        }\n      });\n    }\n  }\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.cartItemCount.next(0);\n  }\n  // Add item to cart via API\n  addToCart(productId, quantity = 1, size, color) {\n    const payload = {\n      productId,\n      quantity,\n      size,\n      color\n    };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.post(`${this.API_URL}/cart-new/add`, payload, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method for backward compatibility - works for guest users\n  addToCartLegacy(_x) {\n    var _this3 = this;\n    return _asyncToGenerator(function* (product, quantity = 1, size, color) {\n      try {\n        const productId = product._id || product.id;\n        // Try API first, but fallback to local storage for guest users\n        try {\n          const response = yield _this3.addToCart(productId, quantity, size, color).toPromise();\n          if (response?.success) {\n            yield _this3.showToast('Item added to cart', 'success');\n            _this3.loadCart(); // Refresh cart\n            return true;\n          }\n        } catch (apiError) {\n          console.log('API not available, using local storage');\n        }\n        // Fallback to local storage (for guest users)\n        const cartItem = {\n          _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n          product: {\n            _id: productId,\n            name: product.name,\n            price: product.price,\n            originalPrice: product.originalPrice,\n            images: product.images || [],\n            brand: product.brand || '',\n            discount: product.discount\n          },\n          quantity,\n          size,\n          color,\n          addedAt: new Date()\n        };\n        const currentCart = _this3.cartItems.value;\n        const existingItemIndex = currentCart.findIndex(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n        if (existingItemIndex >= 0) {\n          currentCart[existingItemIndex].quantity += quantity;\n        } else {\n          currentCart.push(cartItem);\n        }\n        _this3.cartItems.next(currentCart);\n        _this3.updateCartCount();\n        yield _this3.saveCartToStorage();\n        yield _this3.showToast('Item added to cart', 'success');\n        return true;\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n        yield _this3.showToast('Failed to add item to cart', 'danger');\n        return false;\n      }\n    }).apply(this, arguments);\n  }\n  // Remove item from cart via API\n  removeFromCart(itemId) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: {\n        itemIds\n      },\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {\n      body: {\n        itemIds\n      }\n    };\n    return this.http.delete(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  removeFromCartLegacy(itemId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this4.removeFromCart(itemId).toPromise();\n        if (response?.success) {\n          yield _this4.showToast('Item removed from cart', 'success');\n          _this4.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error removing from cart:', error);\n        yield _this4.showToast('Failed to remove item from cart', 'danger');\n      }\n    })();\n  }\n  // Update cart item quantity via API\n  updateCartItem(itemId, quantity) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.put(`${this.API_URL}/cart-new/update/${itemId}`, {\n      quantity\n    }, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  updateQuantity(itemId, quantity) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (quantity <= 0) {\n          yield _this5.removeFromCartLegacy(itemId);\n          return;\n        }\n        const response = yield _this5.updateCartItem(itemId, quantity).toPromise();\n        if (response?.success) {\n          _this5.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error updating quantity:', error);\n        yield _this5.showToast('Failed to update quantity', 'danger');\n      }\n    })();\n  }\n  // Clear cart via API\n  clearCartAPI() {\n    const token = localStorage.getItem('token');\n    const headers = token ? {\n      'Authorization': `Bearer ${token}`\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/clear`, {\n      headers\n    });\n  }\n  clearCart() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this6.clearCartAPI().toPromise();\n        if (response?.success) {\n          _this6.cartItems.next([]);\n          _this6.cartSummary.next(null);\n          _this6.updateCartCount();\n          yield _this6.showToast('Cart cleared', 'success');\n        }\n      } catch (error) {\n        console.error('Error clearing cart:', error);\n        yield _this6.showToast('Failed to clear cart', 'danger');\n      }\n    })();\n  }\n  getCartTotal() {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + price * item.quantity;\n    }, 0);\n  }\n  getCartItemCount() {\n    return this.cartItemCount.value;\n  }\n  isInCart(productId, size, color) {\n    return this.cartItems.value.some(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  getCartItem(productId, size, color) {\n    return this.cartItems.value.find(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  showToast(message, color) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this7.toastController.create({\n        message: message,\n        duration: 2000,\n        color: color,\n        position: 'bottom'\n      });\n      toast.present();\n    })();\n  }\n  static {\n    this.ɵfac = function CartService_Factory(t) {\n      return new (t || CartService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.StorageService), i0.ɵɵinject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CartService,\n      factory: CartService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "environment", "CartService", "constructor", "http", "storageService", "toastController", "API_URL", "apiUrl", "cartItems", "cartSummary", "cartItemCount", "cartItems$", "asObservable", "cartSummary$", "cartItemCount$", "loadCart", "getCart", "token", "localStorage", "getItem", "options", "headers", "get", "getCartCount", "loadCartFromAPI", "loadCartFromStorage", "subscribe", "next", "response", "success", "cart", "items", "summary", "updateCartCount", "console", "log", "length", "error", "_this", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "saveCartToStorage", "_this2", "setCart", "value", "count", "reduce", "total", "item", "quantity", "refreshCartOnLogin", "refreshCartCount", "clearCartOnLogout", "addToCart", "productId", "size", "color", "payload", "post", "pipe", "addToCartLegacy", "_x", "_this3", "product", "_id", "id", "to<PERSON>romise", "showToast", "apiError", "cartItem", "name", "price", "originalPrice", "images", "brand", "discount", "addedAt", "Date", "currentCart", "existingItemIndex", "findIndex", "push", "apply", "arguments", "removeFromCart", "itemId", "delete", "bulkRemoveFromCart", "itemIds", "body", "removeFromCartLegacy", "_this4", "updateCartItem", "put", "updateQuantity", "_this5", "clearCartAPI", "clearCart", "_this6", "getCartTotal", "getCartItemCount", "isInCart", "some", "getCartItem", "find", "message", "_this7", "toast", "create", "duration", "position", "present", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "StorageService", "i3", "ToastController", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\cart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { HttpClient } from '@angular/common/http';\nimport { StorageService } from './storage.service';\nimport { ToastController } from '@ionic/angular';\nimport { environment } from '../../../environments/environment';\n\nexport interface CartItem {\n  _id: string;\n  product: {\n    _id: string;\n    name: string;\n    price: number;\n    originalPrice?: number;\n    images: { url: string; isPrimary: boolean }[];\n    brand: string;\n    discount?: number;\n  };\n  quantity: number;\n  size?: string;\n  color?: string;\n  addedAt: Date;\n}\n\nexport interface CartSummary {\n  itemCount: number;\n  totalQuantity: number;\n  subtotal: number;\n  discount: number;\n  total: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CartService {\n  private readonly API_URL = environment.apiUrl;\n  private cartItems = new BehaviorSubject<CartItem[]>([]);\n  private cartSummary = new BehaviorSubject<CartSummary | null>(null);\n  private cartItemCount = new BehaviorSubject<number>(0);\n\n  public cartItems$ = this.cartItems.asObservable();\n  public cartSummary$ = this.cartSummary.asObservable();\n  public cartItemCount$ = this.cartItemCount.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private storageService: StorageService,\n    private toastController: ToastController\n  ) {\n    this.loadCart();\n  }\n\n  // Get cart from API\n  getCart(): Observable<{ success: boolean; cart: any; summary: any }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; cart: any; summary: any }>(`${this.API_URL}/cart-new`, options);\n  }\n\n  // Get cart count only (lightweight endpoint)\n  getCartCount(): Observable<{ success: boolean; count: number; totalItems: number; itemCount: number }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; count: number; totalItems: number; itemCount: number }>(`${this.API_URL}/cart-new/count`, options);\n  }\n\n  // Load cart and update local state\n  loadCart() {\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n\n    if (token) {\n      // User is logged in - load from API\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      this.loadCartFromStorage();\n    }\n  }\n\n  // Load cart from API for logged-in users\n  private loadCartFromAPI() {\n    this.getCart().subscribe({\n      next: (response) => {\n        if (response.success && response.cart) {\n          this.cartItems.next(response.cart.items || []);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', response.cart.items?.length || 0, 'items');\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        }\n      },\n      error: (error) => {\n        console.log('❌ API cart not available, using local storage fallback');\n        this.loadCartFromStorage();\n      }\n    });\n  }\n\n  private async loadCartFromStorage() {\n    try {\n      // Wait a bit for storage to initialize\n      await new Promise(resolve => setTimeout(resolve, 100));\n      const cart = await this.storageService.getCart();\n      this.cartItems.next(cart || []);\n      this.updateCartCount();\n    } catch (error) {\n      console.error('Error loading cart from storage:', error);\n      this.cartItems.next([]);\n      this.updateCartCount();\n    }\n  }\n\n  private async saveCartToStorage() {\n    try {\n      await this.storageService.setCart(this.cartItems.value);\n    } catch (error) {\n      console.error('Error saving cart to storage:', error);\n    }\n  }\n\n  private updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n\n  // Method to refresh only cart count (lightweight)\n  refreshCartCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.getCartCount().subscribe({\n        next: (response) => {\n          if (response.success) {\n            this.cartItemCount.next(response.count);\n            console.log('🛒 Cart count refreshed:', response.count);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error refreshing cart count:', error);\n        }\n      });\n    }\n  }\n\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.cartItemCount.next(0);\n  }\n\n  // Add item to cart via API\n  addToCart(productId: string, quantity: number = 1, size?: string, color?: string): Observable<{ success: boolean; message: string }> {\n    const payload = { productId, quantity, size, color };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/add`, payload, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method for backward compatibility - works for guest users\n  async addToCartLegacy(product: any, quantity: number = 1, size?: string, color?: string): Promise<boolean> {\n    try {\n      const productId = product._id || product.id;\n\n      // Try API first, but fallback to local storage for guest users\n      try {\n        const response = await this.addToCart(productId, quantity, size, color).toPromise();\n        if (response?.success) {\n          await this.showToast('Item added to cart', 'success');\n          this.loadCart(); // Refresh cart\n          return true;\n        }\n      } catch (apiError) {\n        console.log('API not available, using local storage');\n      }\n\n      // Fallback to local storage (for guest users)\n      const cartItem: CartItem = {\n        _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n        product: {\n          _id: productId,\n          name: product.name,\n          price: product.price,\n          originalPrice: product.originalPrice,\n          images: product.images || [],\n          brand: product.brand || '',\n          discount: product.discount\n        },\n        quantity,\n        size,\n        color,\n        addedAt: new Date()\n      };\n\n      const currentCart = this.cartItems.value;\n      const existingItemIndex = currentCart.findIndex(item =>\n        item.product._id === productId &&\n        (item.size || 'default') === (size || 'default') &&\n        (item.color || 'default') === (color || 'default')\n      );\n\n      if (existingItemIndex >= 0) {\n        currentCart[existingItemIndex].quantity += quantity;\n      } else {\n        currentCart.push(cartItem);\n      }\n\n      this.cartItems.next(currentCart);\n      this.updateCartCount();\n      await this.saveCartToStorage();\n      await this.showToast('Item added to cart', 'success');\n      return true;\n\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      await this.showToast('Failed to add item to cart', 'danger');\n      return false;\n    }\n  }\n\n  // Remove item from cart via API\n  removeFromCart(itemId: string): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds: string[]): Observable<{ success: boolean; message: string; removedCount: number }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: { itemIds },\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {\n      body: { itemIds }\n    };\n    return this.http.delete<{ success: boolean; message: string; removedCount: number }>(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async removeFromCartLegacy(itemId: string): Promise<void> {\n    try {\n      const response = await this.removeFromCart(itemId).toPromise();\n      if (response?.success) {\n        await this.showToast('Item removed from cart', 'success');\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error removing from cart:', error);\n      await this.showToast('Failed to remove item from cart', 'danger');\n    }\n  }\n\n  // Update cart item quantity via API\n  updateCartItem(itemId: string, quantity: number): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.put<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/update/${itemId}`, { quantity }, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async updateQuantity(itemId: string, quantity: number): Promise<void> {\n    try {\n      if (quantity <= 0) {\n        await this.removeFromCartLegacy(itemId);\n        return;\n      }\n\n      const response = await this.updateCartItem(itemId, quantity).toPromise();\n      if (response?.success) {\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error updating quantity:', error);\n      await this.showToast('Failed to update quantity', 'danger');\n    }\n  }\n\n  // Clear cart via API\n  clearCartAPI(): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/clear`, { headers });\n  }\n\n  async clearCart(): Promise<void> {\n    try {\n      const response = await this.clearCartAPI().toPromise();\n      if (response?.success) {\n        this.cartItems.next([]);\n        this.cartSummary.next(null);\n        this.updateCartCount();\n        await this.showToast('Cart cleared', 'success');\n      }\n    } catch (error) {\n      console.error('Error clearing cart:', error);\n      await this.showToast('Failed to clear cart', 'danger');\n    }\n  }\n\n  getCartTotal(): number {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + (price * item.quantity);\n    }, 0);\n  }\n\n  getCartItemCount(): number {\n    return this.cartItemCount.value;\n  }\n\n  isInCart(productId: string, size?: string, color?: string): boolean {\n    return this.cartItems.value.some(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  getCartItem(productId: string, size?: string, color?: string): CartItem | undefined {\n    return this.cartItems.value.find(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  private async showToast(message: string, color: string) {\n    const toast = await this.toastController.create({\n      message: message,\n      duration: 2000,\n      color: color,\n      position: 'bottom'\n    });\n    toast.present();\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,QAAwB,MAAM;AACtD,SAASC,GAAG,QAAQ,gBAAgB;AAIpC,SAASC,WAAW,QAAQ,mCAAmC;;;;;AA8B/D,OAAM,MAAOC,WAAW;EAUtBC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,eAAgC;IAFhC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAZR,KAAAC,OAAO,GAAGN,WAAW,CAACO,MAAM;IACrC,KAAAC,SAAS,GAAG,IAAIV,eAAe,CAAa,EAAE,CAAC;IAC/C,KAAAW,WAAW,GAAG,IAAIX,eAAe,CAAqB,IAAI,CAAC;IAC3D,KAAAY,aAAa,GAAG,IAAIZ,eAAe,CAAS,CAAC,CAAC;IAE/C,KAAAa,UAAU,GAAG,IAAI,CAACH,SAAS,CAACI,YAAY,EAAE;IAC1C,KAAAC,YAAY,GAAG,IAAI,CAACJ,WAAW,CAACG,YAAY,EAAE;IAC9C,KAAAE,cAAc,GAAG,IAAI,CAACJ,aAAa,CAACE,YAAY,EAAE;IAOvD,IAAI,CAACG,QAAQ,EAAE;EACjB;EAEA;EACAC,OAAOA,CAAA;IACL,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACmB,GAAG,CAAgD,GAAG,IAAI,CAAChB,OAAO,WAAW,EAAEc,OAAO,CAAC;EAC1G;EAEA;EACAG,YAAYA,CAAA;IACV,MAAMN,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACmB,GAAG,CAA6E,GAAG,IAAI,CAAChB,OAAO,iBAAiB,EAAEc,OAAO,CAAC;EAC7I;EAEA;EACAL,QAAQA,CAAA;IACN;IACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAIF,KAAK,EAAE;MACT;MACA,IAAI,CAACO,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,CAACC,mBAAmB,EAAE;;EAE9B;EAEA;EACQD,eAAeA,CAAA;IACrB,IAAI,CAACR,OAAO,EAAE,CAACU,SAAS,CAAC;MACvBC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAACtB,SAAS,CAACmB,IAAI,CAACC,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;UAC9C,IAAI,CAACtB,WAAW,CAACkB,IAAI,CAACC,QAAQ,CAACI,OAAO,CAAC;UACvC,IAAI,CAACC,eAAe,EAAE;UACtBC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEP,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAEK,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;SAClF,MAAM;UACL;UACA,IAAI,CAAC5B,SAAS,CAACmB,IAAI,CAAC,EAAE,CAAC;UACvB,IAAI,CAAClB,WAAW,CAACkB,IAAI,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACM,eAAe,EAAE;;MAE1B,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,IAAI,CAACV,mBAAmB,EAAE;MAC5B;KACD,CAAC;EACJ;EAEcA,mBAAmBA,CAAA;IAAA,IAAAa,KAAA;IAAA,OAAAC,iBAAA;MAC/B,IAAI;QACF;QACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,MAAMX,IAAI,SAASQ,KAAI,CAAClC,cAAc,CAACY,OAAO,EAAE;QAChDsB,KAAI,CAAC9B,SAAS,CAACmB,IAAI,CAACG,IAAI,IAAI,EAAE,CAAC;QAC/BQ,KAAI,CAACL,eAAe,EAAE;OACvB,CAAC,OAAOI,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDC,KAAI,CAAC9B,SAAS,CAACmB,IAAI,CAAC,EAAE,CAAC;QACvBW,KAAI,CAACL,eAAe,EAAE;;IACvB;EACH;EAEcU,iBAAiBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAC7B,IAAI;QACF,MAAMK,MAAI,CAACxC,cAAc,CAACyC,OAAO,CAACD,MAAI,CAACpC,SAAS,CAACsC,KAAK,CAAC;OACxD,CAAC,OAAOT,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;IACtD;EACH;EAEQJ,eAAeA,CAAA;IACrB,MAAMF,KAAK,GAAG,IAAI,CAACvB,SAAS,CAACsC,KAAK,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGhB,KAAK,CAACiB,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;IACrE,IAAI,CAACzC,aAAa,CAACiB,IAAI,CAACoB,KAAK,CAAC;IAC9Bb,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEY,KAAK,CAAC;EAC9C;EAEA;EACAK,kBAAkBA,CAAA;IAChBlB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAI,CAACX,eAAe,EAAE;EACxB;EAEA;EACA6B,gBAAgBA,CAAA;IACd,MAAMpC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAACM,YAAY,EAAE,CAACG,SAAS,CAAC;QAC5BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACnB,aAAa,CAACiB,IAAI,CAACC,QAAQ,CAACmB,KAAK,CAAC;YACvCb,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEP,QAAQ,CAACmB,KAAK,CAAC;;QAE3D,CAAC;QACDV,KAAK,EAAGA,KAAK,IAAI;UACfH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;OACD,CAAC;;EAEN;EAEA;EACAiB,iBAAiBA,CAAA;IACfpB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAAC3B,SAAS,CAACmB,IAAI,CAAC,EAAE,CAAC;IACvB,IAAI,CAAClB,WAAW,CAACkB,IAAI,CAAC,IAAI,CAAC;IAC3B,IAAI,CAACjB,aAAa,CAACiB,IAAI,CAAC,CAAC,CAAC;EAC5B;EAEA;EACA4B,SAASA,CAACC,SAAiB,EAAEL,QAAA,GAAmB,CAAC,EAAEM,IAAa,EAAEC,KAAc;IAC9E,MAAMC,OAAO,GAAG;MAAEH,SAAS;MAAEL,QAAQ;MAAEM,IAAI;MAAEC;IAAK,CAAE;IACpD,MAAMzC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACyD,IAAI,CAAwC,GAAG,IAAI,CAACtD,OAAO,eAAe,EAAEqD,OAAO,EAAEvC,OAAO,CAAC,CAACyC,IAAI,CACjH9D,GAAG,CAAC6B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACL,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMsC,eAAeA,CAAAC,EAAA,EAAkE;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA,YAAjE0B,OAAY,EAAEd,QAAA,GAAmB,CAAC,EAAEM,IAAa,EAAEC,KAAc;MACrF,IAAI;QACF,MAAMF,SAAS,GAAGS,OAAO,CAACC,GAAG,IAAID,OAAO,CAACE,EAAE;QAE3C;QACA,IAAI;UACF,MAAMvC,QAAQ,SAASoC,MAAI,CAACT,SAAS,CAACC,SAAS,EAAEL,QAAQ,EAAEM,IAAI,EAAEC,KAAK,CAAC,CAACU,SAAS,EAAE;UACnF,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;YACrB,MAAMmC,MAAI,CAACK,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;YACrDL,MAAI,CAACjD,QAAQ,EAAE,CAAC,CAAC;YACjB,OAAO,IAAI;;SAEd,CAAC,OAAOuD,QAAQ,EAAE;UACjBpC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAGvD;QACA,MAAMoC,QAAQ,GAAa;UACzBL,GAAG,EAAE,GAAGV,SAAS,IAAIC,IAAI,IAAI,SAAS,IAAIC,KAAK,IAAI,SAAS,EAAE;UAC9DO,OAAO,EAAE;YACPC,GAAG,EAAEV,SAAS;YACdgB,IAAI,EAAEP,OAAO,CAACO,IAAI;YAClBC,KAAK,EAAER,OAAO,CAACQ,KAAK;YACpBC,aAAa,EAAET,OAAO,CAACS,aAAa;YACpCC,MAAM,EAAEV,OAAO,CAACU,MAAM,IAAI,EAAE;YAC5BC,KAAK,EAAEX,OAAO,CAACW,KAAK,IAAI,EAAE;YAC1BC,QAAQ,EAAEZ,OAAO,CAACY;WACnB;UACD1B,QAAQ;UACRM,IAAI;UACJC,KAAK;UACLoB,OAAO,EAAE,IAAIC,IAAI;SAClB;QAED,MAAMC,WAAW,GAAGhB,MAAI,CAACxD,SAAS,CAACsC,KAAK;QACxC,MAAMmC,iBAAiB,GAAGD,WAAW,CAACE,SAAS,CAAChC,IAAI,IAClDA,IAAI,CAACe,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACN,IAAI,CAACO,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACP,IAAI,CAACQ,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;QAED,IAAIuB,iBAAiB,IAAI,CAAC,EAAE;UAC1BD,WAAW,CAACC,iBAAiB,CAAC,CAAC9B,QAAQ,IAAIA,QAAQ;SACpD,MAAM;UACL6B,WAAW,CAACG,IAAI,CAACZ,QAAQ,CAAC;;QAG5BP,MAAI,CAACxD,SAAS,CAACmB,IAAI,CAACqD,WAAW,CAAC;QAChChB,MAAI,CAAC/B,eAAe,EAAE;QACtB,MAAM+B,MAAI,CAACrB,iBAAiB,EAAE;QAC9B,MAAMqB,MAAI,CAACK,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;QACrD,OAAO,IAAI;OAEZ,CAAC,OAAOhC,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAM2B,MAAI,CAACK,SAAS,CAAC,4BAA4B,EAAE,QAAQ,CAAC;QAC5D,OAAO,KAAK;;IACb,GAAAe,KAAA,OAAAC,SAAA;EACH;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B,MAAMtE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACqF,MAAM,CAAwC,GAAG,IAAI,CAAClF,OAAO,oBAAoBiF,MAAM,EAAE,EAAEnE,OAAO,CAAC,CAACyC,IAAI,CACvH9D,GAAG,CAAC6B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACL,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACAiE,kBAAkBA,CAACC,OAAiB;IAClC,MAAMzE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtB0E,IAAI,EAAE;QAAED;MAAO,CAAE;MACjBrE,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG;MACF0E,IAAI,EAAE;QAAED;MAAO;KAChB;IACD,OAAO,IAAI,CAACvF,IAAI,CAACqF,MAAM,CAA8D,GAAG,IAAI,CAAClF,OAAO,uBAAuB,EAAEc,OAAO,CAAC,CAACyC,IAAI,CACxI9D,GAAG,CAAC6B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACL,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMoE,oBAAoBA,CAACL,MAAc;IAAA,IAAAM,MAAA;IAAA,OAAAtD,iBAAA;MACvC,IAAI;QACF,MAAMX,QAAQ,SAASiE,MAAI,CAACP,cAAc,CAACC,MAAM,CAAC,CAACnB,SAAS,EAAE;QAC9D,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;UACrB,MAAMgE,MAAI,CAACxB,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC;UACzDwB,MAAI,CAAC9E,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOsB,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMwD,MAAI,CAACxB,SAAS,CAAC,iCAAiC,EAAE,QAAQ,CAAC;;IAClE;EACH;EAEA;EACAyB,cAAcA,CAACP,MAAc,EAAEpC,QAAgB;IAC7C,MAAMlC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAAC4F,GAAG,CAAwC,GAAG,IAAI,CAACzF,OAAO,oBAAoBiF,MAAM,EAAE,EAAE;MAAEpC;IAAQ,CAAE,EAAE/B,OAAO,CAAC,CAACyC,IAAI,CAClI9D,GAAG,CAAC6B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACL,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMwE,cAAcA,CAACT,MAAc,EAAEpC,QAAgB;IAAA,IAAA8C,MAAA;IAAA,OAAA1D,iBAAA;MACnD,IAAI;QACF,IAAIY,QAAQ,IAAI,CAAC,EAAE;UACjB,MAAM8C,MAAI,CAACL,oBAAoB,CAACL,MAAM,CAAC;UACvC;;QAGF,MAAM3D,QAAQ,SAASqE,MAAI,CAACH,cAAc,CAACP,MAAM,EAAEpC,QAAQ,CAAC,CAACiB,SAAS,EAAE;QACxE,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;UACrBoE,MAAI,CAAClF,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOsB,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAM4D,MAAI,CAAC5B,SAAS,CAAC,2BAA2B,EAAE,QAAQ,CAAC;;IAC5D;EACH;EAEA;EACA6B,YAAYA,CAAA;IACV,MAAMjF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAME,OAAO,GAAGJ,KAAK,GAAG;MAAE,eAAe,EAAE,UAAUA,KAAK;IAAE,CAAE,GAAG,EAAE;IACnE,OAAO,IAAI,CAACd,IAAI,CAACqF,MAAM,CAAwC,GAAG,IAAI,CAAClF,OAAO,iBAAiB,EAAE;MAAEe;IAAO,CAAE,CAAC;EAC/G;EAEM8E,SAASA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7D,iBAAA;MACb,IAAI;QACF,MAAMX,QAAQ,SAASwE,MAAI,CAACF,YAAY,EAAE,CAAC9B,SAAS,EAAE;QACtD,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;UACrBuE,MAAI,CAAC5F,SAAS,CAACmB,IAAI,CAAC,EAAE,CAAC;UACvByE,MAAI,CAAC3F,WAAW,CAACkB,IAAI,CAAC,IAAI,CAAC;UAC3ByE,MAAI,CAACnE,eAAe,EAAE;UACtB,MAAMmE,MAAI,CAAC/B,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC;;OAElD,CAAC,OAAOhC,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAM+D,MAAI,CAAC/B,SAAS,CAAC,sBAAsB,EAAE,QAAQ,CAAC;;IACvD;EACH;EAEAgC,YAAYA,CAAA;IACV,MAAMrE,OAAO,GAAG,IAAI,CAACvB,WAAW,CAACqC,KAAK;IACtC,IAAId,OAAO,EAAE;MACX,OAAOA,OAAO,CAACiB,KAAK;;IAGtB;IACA,OAAO,IAAI,CAACzC,SAAS,CAACsC,KAAK,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAI;MACjD,MAAMuB,KAAK,GAAGvB,IAAI,CAACe,OAAO,CAACQ,KAAK;MAChC,OAAOxB,KAAK,GAAIwB,KAAK,GAAGvB,IAAI,CAACC,QAAS;IACxC,CAAC,EAAE,CAAC,CAAC;EACP;EAEAmD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC5F,aAAa,CAACoC,KAAK;EACjC;EAEAyD,QAAQA,CAAC/C,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IACvD,OAAO,IAAI,CAAClD,SAAS,CAACsC,KAAK,CAAC0D,IAAI,CAACtD,IAAI,IACnCA,IAAI,CAACe,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACN,IAAI,CAACO,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACP,IAAI,CAACQ,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEA+C,WAAWA,CAACjD,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IAC1D,OAAO,IAAI,CAAClD,SAAS,CAACsC,KAAK,CAAC4D,IAAI,CAACxD,IAAI,IACnCA,IAAI,CAACe,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACN,IAAI,CAACO,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACP,IAAI,CAACQ,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEcW,SAASA,CAACsC,OAAe,EAAEjD,KAAa;IAAA,IAAAkD,MAAA;IAAA,OAAArE,iBAAA;MACpD,MAAMsE,KAAK,SAASD,MAAI,CAACvG,eAAe,CAACyG,MAAM,CAAC;QAC9CH,OAAO,EAAEA,OAAO;QAChBI,QAAQ,EAAE,IAAI;QACdrD,KAAK,EAAEA,KAAK;QACZsD,QAAQ,EAAE;OACX,CAAC;MACFH,KAAK,CAACI,OAAO,EAAE;IAAC;EAClB;;;uBAtWWhH,WAAW,EAAAiH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAXxH,WAAW;MAAAyH,OAAA,EAAXzH,WAAW,CAAA0H,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}