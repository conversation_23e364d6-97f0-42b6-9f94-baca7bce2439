{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst spinners = {\n  bubbles: {\n    dur: 1000,\n    circles: 9,\n    fn: (dur, index, total) => {\n      const animationDelay = `${dur * index / total - dur}ms`;\n      const angle = 2 * Math.PI * index / total;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circles: {\n    dur: 1000,\n    circles: 8,\n    fn: (dur, index, total) => {\n      const step = index / total;\n      const animationDelay = `${dur * step - dur}ms`;\n      const angle = 2 * Math.PI * step;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circular: {\n    dur: 1400,\n    elmDuration: true,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 20,\n        cx: 48,\n        cy: 48,\n        fill: 'none',\n        viewBox: '24 24 48 48',\n        transform: 'translate(0,0)',\n        style: {}\n      };\n    }\n  },\n  crescent: {\n    dur: 750,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 26,\n        style: {}\n      };\n    }\n  },\n  dots: {\n    dur: 750,\n    circles: 3,\n    fn: (_, index) => {\n      const animationDelay = -(110 * index) + 'ms';\n      return {\n        r: 6,\n        style: {\n          left: `${32 - 32 * index}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  lines: {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 14,\n        y2: 26,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-small': {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 17,\n        y2: 29,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp-small': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  }\n};\nconst SPINNERS = spinners;\nexport { SPINNERS as S };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}