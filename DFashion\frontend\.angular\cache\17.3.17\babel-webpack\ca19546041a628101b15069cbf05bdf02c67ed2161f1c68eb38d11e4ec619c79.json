{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/story.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/media.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"storiesSlider\"];\nfunction StoriesComponent_div_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_2_button_1_Template_button_click_0_listener() {\n      const i_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.goToSlide(i_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r3.currentSlide);\n  }\n}\nfunction StoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, StoriesComponent_div_2_button_1_Template, 1, 2, \"button\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dots);\n  }\n}\nfunction StoriesComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_12_Template_div_click_0_listener() {\n      const storyGroup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openStoryViewer(storyGroup_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 20)(3, \"img\", 21);\n    i0.ɵɵlistener(\"error\", function StoriesComponent_div_12_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onImageError($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const storyGroup_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-story\", storyGroup_r6.stories.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.getSafeImageUrl(storyGroup_r6.user.avatar), i0.ɵɵsanitizeUrl)(\"alt\", storyGroup_r6.user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(storyGroup_r6.user.username);\n  }\n}\nfunction StoriesComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 7)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"div\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"div\", 26);\n    i0.ɵɵelementStart(2, \"span\", 27);\n    i0.ɵɵtext(3, \"Auto-sliding\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideLeft());\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideLeft);\n  }\n}\nfunction StoriesComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideRight());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideRight);\n  }\n}\nexport class StoriesComponent {\n  constructor(storyService, authService, router, mediaService) {\n    this.storyService = storyService;\n    this.authService = authService;\n    this.router = router;\n    this.mediaService = mediaService;\n    this.storyGroups = [];\n    this.currentUser = null;\n    // Slider properties\n    this.translateX = 0;\n    this.currentSlide = 0;\n    this.isTransitioning = false;\n    // Navigation properties\n    this.canSlideLeft = false;\n    this.canSlideRight = false;\n    this.showArrows = false;\n    this.showDots = false;\n    this.dots = [];\n    // Touch properties\n    this.touchStartX = 0;\n    this.touchCurrentX = 0;\n    this.isDragging = false;\n    // Responsive properties\n    this.slidesPerView = 1;\n    this.slideWidth = 0;\n    this.autoSlideDelay = 3000; // 3 seconds\n    this.isAutoSliding = true;\n    // Auto-loading properties\n    this.isLoadingMore = false;\n    this.hasMoreStories = true;\n    this.currentPage = 1;\n    this.storiesPerPage = 10;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.loadStories();\n  }\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n  loadStories(page = 1, append = false) {\n    if (this.isLoadingMore) return;\n    this.isLoadingMore = true;\n    this.storyService.getStories(page, this.storiesPerPage).subscribe({\n      next: response => {\n        if (response.storyGroups && response.storyGroups.length > 0) {\n          if (append) {\n            this.storyGroups = [...this.storyGroups, ...response.storyGroups];\n          } else {\n            this.storyGroups = response.storyGroups;\n          }\n          // Check if there are more stories\n          this.hasMoreStories = response.storyGroups.length === this.storiesPerPage;\n          // Update slider after stories load\n          setTimeout(() => {\n            this.calculateResponsiveSettings();\n            this.updateSliderState();\n            if (!append) {\n              this.startAutoSlide();\n            }\n          }, 100);\n        } else {\n          console.log('No stories available from database');\n          if (!append) {\n            this.storyGroups = [];\n          }\n          this.hasMoreStories = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        if (!append) {\n          this.storyGroups = [];\n        }\n        this.hasMoreStories = false;\n        this.isLoadingMore = false;\n      }\n    });\n  }\n  loadMoreStories() {\n    if (this.hasMoreStories && !this.isLoadingMore) {\n      this.currentPage++;\n      this.loadStories(this.currentPage, true);\n    }\n  }\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Check if we need to load more stories\n      if (this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      } else {\n        // Reset to beginning\n        this.currentSlide = 0;\n        this.updateSliderPosition();\n      }\n    }\n  }\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n      // Check if we're near the end and need to load more\n      if (this.currentSlide >= maxSlide - 1 && this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      }\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  goToSlide(slideIndex) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n  getMaxSlide() {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n  // Touch gesture methods\n  onTouchStart(event) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n    this.isDragging = false;\n  }\n  // Image handling methods\n  getSafeImageUrl(url) {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n  onImageError(event) {\n    this.mediaService.handleImageError(event, 'user');\n  }\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n  showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n    // Add global function for file handling\n    window.handleFileSelect = input => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n  showFilePreview(file) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ? `<video src=\"${fileURL}\" controls autoplay muted></video>` : `<img src=\"${fileURL}\" alt=\"Story preview\">`}\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n  openStoryViewer(storyGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Create Instagram-like story viewer modal\n      this.createStoryViewerModal(storyGroup);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n  createStoryViewerModal(storyGroup) {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-viewer-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"story-viewer-container\">\n        <div class=\"story-viewer-content\">\n          <!-- Progress bars -->\n          <div class=\"progress-container\">\n            ${storyGroup.stories.map((_, index) => `\n              <div class=\"progress-bar ${index === 0 ? 'active' : ''}\" data-index=\"${index}\">\n                <div class=\"progress-fill\"></div>\n              </div>\n            `).join('')}\n          </div>\n\n          <!-- Header -->\n          <div class=\"story-header\">\n            <div class=\"user-info\">\n              <img src=\"${storyGroup.user.avatar || '/assets/images/default-avatar.png'}\"\n                   alt=\"${storyGroup.user.fullName}\" class=\"user-avatar\">\n              <div class=\"user-details\">\n                <span class=\"username\">${storyGroup.user.username}</span>\n                <span class=\"timestamp\">${this.getTimeAgo(storyGroup.stories[0].createdAt)}</span>\n              </div>\n            </div>\n            <div class=\"story-controls\">\n              <button class=\"btn-close\" onclick=\"this.closest('.story-viewer-modal').remove()\">\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Story content -->\n          <div class=\"story-content\" id=\"story-content\">\n            <!-- Will be populated dynamically -->\n          </div>\n\n          <!-- Navigation areas -->\n          <div class=\"nav-area nav-prev\" onclick=\"window.storyViewer.previousStory()\"></div>\n          <div class=\"nav-area nav-next\" onclick=\"window.storyViewer.nextStory()\"></div>\n\n          <!-- Bottom actions -->\n          <div class=\"story-actions\">\n            <div class=\"social-actions\">\n              <button class=\"social-btn like\" onclick=\"window.storyViewer.toggleLike()\">\n                <i class=\"fas fa-heart\"></i>\n              </button>\n              <button class=\"social-btn comment\" onclick=\"window.storyViewer.openComments()\">\n                <i class=\"fas fa-comment\"></i>\n              </button>\n              <button class=\"social-btn share\" onclick=\"window.storyViewer.shareStory()\">\n                <i class=\"fas fa-share\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    `;\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-viewer-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100vw;\n        height: 100vh;\n        background: #000;\n        z-index: 10000;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .story-viewer-container {\n        width: 100%;\n        height: 100%;\n        max-width: 400px;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .story-viewer-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .progress-container {\n        display: flex;\n        gap: 2px;\n        padding: 8px 20px;\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        z-index: 10;\n      }\n\n      .progress-bar {\n        flex: 1;\n        height: 2px;\n        background: rgba(255,255,255,0.3);\n        border-radius: 1px;\n        overflow: hidden;\n      }\n\n      .progress-fill {\n        height: 100%;\n        background: #fff;\n        width: 0%;\n        transition: width 0.1s ease;\n      }\n\n      .progress-bar.active .progress-fill {\n        animation: progress 15s linear;\n      }\n\n      .progress-bar.completed .progress-fill {\n        width: 100%;\n      }\n\n      @keyframes progress {\n        from { width: 0%; }\n        to { width: 100%; }\n      }\n\n      .story-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 16px 20px;\n        background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n        position: relative;\n        z-index: 10;\n      }\n\n      .user-info {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n\n      .user-avatar {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        border: 2px solid #fff;\n      }\n\n      .user-details {\n        display: flex;\n        flex-direction: column;\n      }\n\n      .username {\n        color: #fff;\n        font-weight: 600;\n        font-size: 0.9rem;\n      }\n\n      .timestamp {\n        color: rgba(255,255,255,0.7);\n        font-size: 0.8rem;\n      }\n\n      .btn-close {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.2rem;\n        cursor: pointer;\n        padding: 8px;\n      }\n\n      .story-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #000;\n      }\n\n      .story-media {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .nav-area {\n        position: absolute;\n        top: 0;\n        bottom: 0;\n        width: 30%;\n        cursor: pointer;\n        z-index: 5;\n      }\n\n      .nav-prev {\n        left: 0;\n      }\n\n      .nav-next {\n        right: 0;\n      }\n\n      .story-actions {\n        padding: 16px 20px;\n        background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n      }\n\n      .social-actions {\n        display: flex;\n        justify-content: center;\n        gap: 24px;\n      }\n\n      .social-btn {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.5rem;\n        cursor: pointer;\n        padding: 8px;\n        border-radius: 50%;\n        transition: all 0.2s ease;\n      }\n\n      .social-btn:hover {\n        background: rgba(255,255,255,0.1);\n        transform: scale(1.1);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n\n      @media (max-width: 768px) {\n        .story-viewer-container {\n          max-width: 100%;\n        }\n      }\n    `;\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n    // Initialize story viewer functionality\n    this.initializeStoryViewer(storyGroup, modalOverlay);\n  }\n  initializeStoryViewer(storyGroup, modalElement) {\n    let currentIndex = 0;\n    let progressTimer;\n    const storyViewer = {\n      currentIndex,\n      stories: storyGroup.stories,\n      showStory: index => {\n        const story = storyGroup.stories[index];\n        const contentElement = modalElement.querySelector('#story-content');\n        if (contentElement) {\n          if (story.media.type === 'image') {\n            contentElement.innerHTML = `\n              <img src=\"${story.media.url}\" alt=\"${story.caption || ''}\" class=\"story-media\">\n            `;\n          } else {\n            contentElement.innerHTML = `\n              <video src=\"${story.media.url}\" class=\"story-media\" autoplay muted>\n              </video>\n            `;\n          }\n        }\n        // Update progress bars\n        const progressBars = modalElement.querySelectorAll('.progress-bar');\n        progressBars.forEach((bar, i) => {\n          bar.classList.remove('active', 'completed');\n          if (i < index) {\n            bar.classList.add('completed');\n          } else if (i === index) {\n            bar.classList.add('active');\n          }\n        });\n        this.startProgressTimer(15000); // 15 seconds\n      },\n      nextStory: () => {\n        if (currentIndex < storyGroup.stories.length - 1) {\n          currentIndex++;\n          storyViewer.showStory(currentIndex);\n        } else {\n          modalElement.remove();\n        }\n      },\n      previousStory: () => {\n        if (currentIndex > 0) {\n          currentIndex--;\n          storyViewer.showStory(currentIndex);\n        }\n      },\n      toggleLike: () => {\n        console.log('Toggle like for story');\n      },\n      openComments: () => {\n        console.log('Open comments for story');\n      },\n      shareStory: () => {\n        console.log('Share story');\n      }\n    };\n    // Make it globally accessible for onclick handlers\n    window.storyViewer = storyViewer;\n    // Show first story\n    storyViewer.showStory(0);\n    // Cleanup when modal is removed\n    const observer = new MutationObserver(mutations => {\n      mutations.forEach(mutation => {\n        if (mutation.type === 'childList') {\n          mutation.removedNodes.forEach(node => {\n            if (node === modalElement) {\n              delete window.storyViewer;\n              if (progressTimer) clearTimeout(progressTimer);\n              observer.disconnect();\n            }\n          });\n        }\n      });\n    });\n    observer.observe(document.body, {\n      childList: true\n    });\n  }\n  startProgressTimer(duration) {\n    // This would be implemented to handle progress timing\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    if (hours < 1) {\n      const minutes = Math.floor(diff / (1000 * 60));\n      return `${minutes}m`;\n    } else if (hours < 24) {\n      return `${hours}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      return `${days}d`;\n    }\n  }\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n  static {\n    this.ɵfac = function StoriesComponent_Factory(t) {\n      return new (t || StoriesComponent)(i0.ɵɵdirectiveInject(i1.StoryService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesComponent,\n      selectors: [[\"app-stories\"]],\n      viewQuery: function StoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 17,\n      vars: 10,\n      consts: [[\"storiesSlider\", \"\"], [1, \"stories-section\"], [1, \"stories-slider-wrapper\"], [\"class\", \"nav-dots\", 4, \"ngIf\"], [1, \"stories-slider\", 3, \"touchstart\", \"touchmove\", \"touchend\", \"scroll\", \"mouseenter\", \"mouseleave\"], [1, \"stories-track\"], [1, \"story-slide\", \"add-story\", 3, \"click\"], [1, \"story-avatar\"], [1, \"avatar-ring\", \"add-ring\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-slide\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"story-slide loading-slide\", 4, \"ngIf\"], [\"class\", \"auto-slide-indicator\", 4, \"ngIf\"], [\"class\", \"nav-arrow nav-left\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"nav-arrow nav-right\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"nav-dots\"], [\"class\", \"nav-dot\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-dot\", 3, \"click\"], [1, \"story-slide\", 3, \"click\"], [1, \"avatar-ring\"], [1, \"avatar-image\", 3, \"error\", \"src\", \"alt\"], [1, \"story-slide\", \"loading-slide\"], [1, \"avatar-ring\", \"loading-ring\"], [1, \"loading-spinner\"], [1, \"auto-slide-indicator\"], [1, \"slide-progress\"], [1, \"slide-text\"], [1, \"nav-arrow\", \"nav-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"nav-arrow\", \"nav-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function StoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtemplate(2, StoriesComponent_div_2_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4, 0);\n          i0.ɵɵlistener(\"touchstart\", function StoriesComponent_Template_div_touchstart_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchStart($event));\n          })(\"touchmove\", function StoriesComponent_Template_div_touchmove_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchMove($event));\n          })(\"touchend\", function StoriesComponent_Template_div_touchend_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchEnd($event));\n          })(\"scroll\", function StoriesComponent_Template_div_scroll_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onScroll());\n          })(\"mouseenter\", function StoriesComponent_Template_div_mouseenter_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function StoriesComponent_Template_div_mouseleave_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function StoriesComponent_Template_div_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openAddStory());\n          });\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"span\", 10);\n          i0.ɵɵtext(11, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, StoriesComponent_div_12_Template, 6, 5, \"div\", 11)(13, StoriesComponent_div_13_Template, 6, 0, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, StoriesComponent_div_14_Template, 4, 0, \"div\", 13)(15, StoriesComponent_button_15_Template, 2, 1, \"button\", 14)(16, StoriesComponent_button_16_Template, 2, 1, \"button\", 15);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDots);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.translateX + \"px)\")(\"transition\", ctx.isTransitioning ? \"transform 0.3s ease-out\" : \"none\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.storyGroups);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingMore);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isAutoSliding && ctx.storyGroups.length > ctx.slidesPerView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf],\n      styles: [\".stories-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  width: 100%;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0;\\n  will-change: transform;\\n  padding: 16px 0;\\n}\\n\\n.story-slide[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 0 12px;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 80px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:first-child {\\n  padding-left: 16px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:last-child {\\n  padding-right: 16px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  position: relative;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  padding: 3px;\\n  background: #fafafa;\\n  border: 1px solid #dbdbdb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.avatar-ring.has-story[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  border: none;\\n  animation: _ngcontent-%COMP%_storyPulse 2s infinite;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.avatar-ring.viewed-story[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  border: 1px solid #dbdbdb;\\n  animation: none;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border: 2px dashed #dbdbdb;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  background: rgba(0, 149, 246, 0.05);\\n}\\n\\n.avatar-ring.loading-ring[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 2px solid #e9ecef;\\n  animation: _ngcontent-%COMP%_loadingPulse 1.5s ease-in-out infinite;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 3px solid #e9ecef;\\n  border-top: 3px solid var(--primary-color);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loadingPulse {\\n  0%, 100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 0.7;\\n    transform: scale(1.05);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-slide[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n}\\n.loading-slide[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-style: italic;\\n}\\n\\n.avatar-image[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #fff;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 20px;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyPulse {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  }\\n  50% {\\n    transform: scale(1.02);\\n    box-shadow: 0 4px 16px rgba(240, 148, 51, 0.3);\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  }\\n}\\n\\n\\n.auto-slide-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  z-index: 5;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  animation: _ngcontent-%COMP%_fadeInSlide 0.3s ease;\\n}\\n\\n.slide-progress[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: var(--primary-color, #007bff);\\n  animation: _ngcontent-%COMP%_slideProgress 3s linear infinite;\\n}\\n\\n.slide-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInSlide {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideProgress {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.2);\\n    opacity: 0.7;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.95);\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\\n  color: #262626;\\n  font-size: 16px;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #fff;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n  transform: translateY(-50%);\\n}\\n\\n.nav-arrow.nav-left[_ngcontent-%COMP%] {\\n  left: 120px; \\n\\n}\\n\\n.nav-arrow.nav-right[_ngcontent-%COMP%] {\\n  right: 12px;\\n}\\n\\n\\n\\n.nav-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 12px 0 8px;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #dbdbdb;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.nav-dot.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  transform: scale(1.2);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  opacity: 0.7;\\n}\\n\\n\\n\\n@media (min-width: 1024px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    padding: 0 15px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 72px;\\n    height: 72px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    max-width: 80px;\\n  }\\n  .nav-arrow.nav-left[_ngcontent-%COMP%] {\\n    left: 140px; \\n\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 1023px) and (min-width: 768px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 80px;\\n    padding: 0 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n    font-size: 14px;\\n  }\\n  .nav-arrow.nav-left[_ngcontent-%COMP%] {\\n    left: 120px; \\n\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 767px) and (min-width: 481px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 14px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    padding: 0 10px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n}\\n\\n\\n@media (max-width: 480px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 65px;\\n    padding: 0 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 12px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 52px;\\n    height: 52px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 55px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n    padding: 8px 0 6px;\\n  }\\n  .nav-dot[_ngcontent-%COMP%] {\\n    width: 6px;\\n    height: 6px;\\n  }\\n}\\n\\n\\n@media (max-width: 360px) {\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    padding: 0 6px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 8px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 50px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "StoriesComponent_div_2_button_1_Template_button_click_0_listener", "i_r3", "ɵɵrestoreView", "_r2", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "goToSlide", "ɵɵelementEnd", "ɵɵclassProp", "currentSlide", "ɵɵtemplate", "StoriesComponent_div_2_button_1_Template", "ɵɵadvance", "ɵɵproperty", "dots", "StoriesComponent_div_12_Template_div_click_0_listener", "storyGroup_r6", "_r5", "$implicit", "openStoryViewer", "StoriesComponent_div_12_Template_img_error_3_listener", "$event", "onImageError", "ɵɵtext", "stories", "length", "getSafeImageUrl", "user", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "ɵɵelement", "StoriesComponent_button_15_Template_button_click_0_listener", "_r7", "slideLeft", "canSlideLeft", "StoriesComponent_button_16_Template_button_click_0_listener", "_r8", "slideRight", "canSlideRight", "StoriesComponent", "constructor", "storyService", "authService", "router", "mediaService", "storyGroups", "currentUser", "translateX", "isTransitioning", "showArrows", "showDots", "touchStartX", "touchCurrentX", "isDragging", "<PERSON><PERSON><PERSON><PERSON>iew", "slideWidth", "autoSlideDelay", "isAutoSliding", "isLoadingMore", "hasMoreStories", "currentPage", "storiesPerPage", "ngOnInit", "currentUser$", "subscribe", "loadStories", "ngAfterViewInit", "setTimeout", "calculateResponsiveSettings", "updateSliderState", "startAutoSlide", "window", "addEventListener", "ngOnDestroy", "stopAutoSlide", "storiesSlider", "containerWidth", "nativeElement", "clientWidth", "screenWidth", "innerWidth", "Math", "floor", "max", "totalSlides", "totalPages", "ceil", "Array", "fill", "map", "_", "i", "page", "append", "getStories", "next", "response", "console", "log", "error", "loadMoreStories", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "pauseAutoSlide", "resumeAutoSlide", "maxSlide", "getMaxSlide", "updateSliderPosition", "min", "slideIndex", "onScroll", "onTouchStart", "event", "touches", "clientX", "onTouchMove", "deltaX", "abs", "preventDefault", "onTouchEnd", "threshold", "url", "handleImageError", "openAddStory", "showStoryCreationModal", "modalOverlay", "document", "createElement", "className", "innerHTML", "styles", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "handleFileSelect", "input", "files", "file", "name", "type", "showFilePreview", "remove", "previewModal", "fileURL", "URL", "createObjectURL", "isVideo", "startsWith", "previewStyles", "storyGroup", "createStoryViewerModal", "join", "getTimeAgo", "createdAt", "initializeStoryViewer", "modalElement", "currentIndex", "progressTimer", "story<PERSON>iewer", "showStory", "story", "contentElement", "querySelector", "media", "caption", "progressBars", "querySelectorAll", "for<PERSON>ach", "bar", "classList", "add", "startProgressTimer", "nextStory", "previousStory", "toggleLike", "openComments", "shareStory", "observer", "MutationObserver", "mutations", "mutation", "removedNodes", "node", "clearTimeout", "disconnect", "observe", "childList", "duration", "date", "now", "Date", "diff", "getTime", "hours", "minutes", "days", "onMouseEnter", "onMouseLeave", "ɵɵdirectiveInject", "i1", "StoryService", "i2", "AuthService", "i3", "Router", "i4", "MediaService", "selectors", "viewQuery", "StoriesComponent_Query", "rf", "ctx", "StoriesComponent_div_2_Template", "StoriesComponent_Template_div_touchstart_3_listener", "_r1", "StoriesComponent_Template_div_touchmove_3_listener", "StoriesComponent_Template_div_touchend_3_listener", "StoriesComponent_Template_div_scroll_3_listener", "StoriesComponent_Template_div_mouseenter_3_listener", "StoriesComponent_Template_div_mouseleave_3_listener", "StoriesComponent_Template_div_click_6_listener", "StoriesComponent_div_12_Template", "StoriesComponent_div_13_Template", "StoriesComponent_div_14_Template", "StoriesComponent_button_15_Template", "StoriesComponent_button_16_Template", "ɵɵstyleProp", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { MediaService } from '../../../../core/services/media.service';\n\nimport { StoryService } from '../../../../core/services/story.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { StoryGroup, Story } from '../../../../core/models/story.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-stories',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './stories.component.html',\n  styleUrls: ['./stories.component.scss']\n\n\n})\nexport class StoriesComponent implements OnInit, AfterViewInit {\n  @ViewChild('storiesSlider') storiesSlider!: ElementRef<HTMLDivElement>;\n\n  storyGroups: StoryGroup[] = [];\n  currentUser: User | null = null;\n\n  // Slider properties\n  translateX = 0;\n  currentSlide = 0;\n  isTransitioning = false;\n\n  // Navigation properties\n  canSlideLeft = false;\n  canSlideRight = false;\n  showArrows = false;\n  showDots = false;\n  dots: number[] = [];\n\n  // Touch properties\n  touchStartX = 0;\n  touchCurrentX = 0;\n  isDragging = false;\n\n  // Responsive properties\n  slidesPerView = 1;\n  slideWidth = 0;\n\n  // Auto-slide properties\n  autoSlideInterval: any;\n  autoSlideDelay = 3000; // 3 seconds\n  isAutoSliding = true;\n\n  // Auto-loading properties\n  isLoadingMore = false;\n  hasMoreStories = true;\n  currentPage = 1;\n  storiesPerPage = 10;\n\n  constructor(\n    private storyService: StoryService,\n    private authService: AuthService,\n    private router: Router,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    this.loadStories();\n  }\n\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n\n  loadStories(page: number = 1, append: boolean = false) {\n    if (this.isLoadingMore) return;\n\n    this.isLoadingMore = true;\n\n    this.storyService.getStories(page, this.storiesPerPage).subscribe({\n      next: (response) => {\n        if (response.storyGroups && response.storyGroups.length > 0) {\n          if (append) {\n            this.storyGroups = [...this.storyGroups, ...response.storyGroups];\n          } else {\n            this.storyGroups = response.storyGroups;\n          }\n\n          // Check if there are more stories\n          this.hasMoreStories = response.storyGroups.length === this.storiesPerPage;\n\n          // Update slider after stories load\n          setTimeout(() => {\n            this.calculateResponsiveSettings();\n            this.updateSliderState();\n            if (!append) {\n              this.startAutoSlide();\n            }\n          }, 100);\n        } else {\n          console.log('No stories available from database');\n          if (!append) {\n            this.storyGroups = [];\n          }\n          this.hasMoreStories = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        if (!append) {\n          this.storyGroups = [];\n        }\n        this.hasMoreStories = false;\n        this.isLoadingMore = false;\n      }\n    });\n  }\n\n  loadMoreStories() {\n    if (this.hasMoreStories && !this.isLoadingMore) {\n      this.currentPage++;\n      this.loadStories(this.currentPage, true);\n    }\n  }\n\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Check if we need to load more stories\n      if (this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      } else {\n        // Reset to beginning\n        this.currentSlide = 0;\n        this.updateSliderPosition();\n      }\n    }\n  }\n\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n\n      // Check if we're near the end and need to load more\n      if (this.currentSlide >= maxSlide - 1 && this.hasMoreStories && !this.isLoadingMore) {\n        this.loadMoreStories();\n      }\n\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  goToSlide(slideIndex: number) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n\n  getMaxSlide(): number {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n\n  // Touch gesture methods\n  onTouchStart(event: TouchEvent) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n\n    this.isDragging = false;\n  }\n\n  // Image handling methods\n  getSafeImageUrl(url: string | undefined): string {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n\n  onImageError(event: Event): void {\n    this.mediaService.handleImageError(event, 'user');\n  }\n\n\n\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n\n  private showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n\n    // Add global function for file handling\n    (window as any).handleFileSelect = (input: HTMLInputElement) => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n\n  private showFilePreview(file: File) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ?\n            `<video src=\"${fileURL}\" controls autoplay muted></video>` :\n            `<img src=\"${fileURL}\" alt=\"Story preview\">`\n          }\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n\n  openStoryViewer(storyGroup: StoryGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Create Instagram-like story viewer modal\n      this.createStoryViewerModal(storyGroup);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n\n  private createStoryViewerModal(storyGroup: StoryGroup) {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-viewer-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"story-viewer-container\">\n        <div class=\"story-viewer-content\">\n          <!-- Progress bars -->\n          <div class=\"progress-container\">\n            ${storyGroup.stories.map((_, index) => `\n              <div class=\"progress-bar ${index === 0 ? 'active' : ''}\" data-index=\"${index}\">\n                <div class=\"progress-fill\"></div>\n              </div>\n            `).join('')}\n          </div>\n\n          <!-- Header -->\n          <div class=\"story-header\">\n            <div class=\"user-info\">\n              <img src=\"${storyGroup.user.avatar || '/assets/images/default-avatar.png'}\"\n                   alt=\"${storyGroup.user.fullName}\" class=\"user-avatar\">\n              <div class=\"user-details\">\n                <span class=\"username\">${storyGroup.user.username}</span>\n                <span class=\"timestamp\">${this.getTimeAgo(storyGroup.stories[0].createdAt)}</span>\n              </div>\n            </div>\n            <div class=\"story-controls\">\n              <button class=\"btn-close\" onclick=\"this.closest('.story-viewer-modal').remove()\">\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Story content -->\n          <div class=\"story-content\" id=\"story-content\">\n            <!-- Will be populated dynamically -->\n          </div>\n\n          <!-- Navigation areas -->\n          <div class=\"nav-area nav-prev\" onclick=\"window.storyViewer.previousStory()\"></div>\n          <div class=\"nav-area nav-next\" onclick=\"window.storyViewer.nextStory()\"></div>\n\n          <!-- Bottom actions -->\n          <div class=\"story-actions\">\n            <div class=\"social-actions\">\n              <button class=\"social-btn like\" onclick=\"window.storyViewer.toggleLike()\">\n                <i class=\"fas fa-heart\"></i>\n              </button>\n              <button class=\"social-btn comment\" onclick=\"window.storyViewer.openComments()\">\n                <i class=\"fas fa-comment\"></i>\n              </button>\n              <button class=\"social-btn share\" onclick=\"window.storyViewer.shareStory()\">\n                <i class=\"fas fa-share\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    `;\n\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-viewer-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100vw;\n        height: 100vh;\n        background: #000;\n        z-index: 10000;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .story-viewer-container {\n        width: 100%;\n        height: 100%;\n        max-width: 400px;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .story-viewer-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .progress-container {\n        display: flex;\n        gap: 2px;\n        padding: 8px 20px;\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        z-index: 10;\n      }\n\n      .progress-bar {\n        flex: 1;\n        height: 2px;\n        background: rgba(255,255,255,0.3);\n        border-radius: 1px;\n        overflow: hidden;\n      }\n\n      .progress-fill {\n        height: 100%;\n        background: #fff;\n        width: 0%;\n        transition: width 0.1s ease;\n      }\n\n      .progress-bar.active .progress-fill {\n        animation: progress 15s linear;\n      }\n\n      .progress-bar.completed .progress-fill {\n        width: 100%;\n      }\n\n      @keyframes progress {\n        from { width: 0%; }\n        to { width: 100%; }\n      }\n\n      .story-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 16px 20px;\n        background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n        position: relative;\n        z-index: 10;\n      }\n\n      .user-info {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n\n      .user-avatar {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        border: 2px solid #fff;\n      }\n\n      .user-details {\n        display: flex;\n        flex-direction: column;\n      }\n\n      .username {\n        color: #fff;\n        font-weight: 600;\n        font-size: 0.9rem;\n      }\n\n      .timestamp {\n        color: rgba(255,255,255,0.7);\n        font-size: 0.8rem;\n      }\n\n      .btn-close {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.2rem;\n        cursor: pointer;\n        padding: 8px;\n      }\n\n      .story-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #000;\n      }\n\n      .story-media {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .nav-area {\n        position: absolute;\n        top: 0;\n        bottom: 0;\n        width: 30%;\n        cursor: pointer;\n        z-index: 5;\n      }\n\n      .nav-prev {\n        left: 0;\n      }\n\n      .nav-next {\n        right: 0;\n      }\n\n      .story-actions {\n        padding: 16px 20px;\n        background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n      }\n\n      .social-actions {\n        display: flex;\n        justify-content: center;\n        gap: 24px;\n      }\n\n      .social-btn {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.5rem;\n        cursor: pointer;\n        padding: 8px;\n        border-radius: 50%;\n        transition: all 0.2s ease;\n      }\n\n      .social-btn:hover {\n        background: rgba(255,255,255,0.1);\n        transform: scale(1.1);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n\n      @media (max-width: 768px) {\n        .story-viewer-container {\n          max-width: 100%;\n        }\n      }\n    `;\n\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n\n    // Initialize story viewer functionality\n    this.initializeStoryViewer(storyGroup, modalOverlay);\n  }\n\n  private initializeStoryViewer(storyGroup: StoryGroup, modalElement: HTMLElement) {\n    let currentIndex = 0;\n    let progressTimer: any;\n\n    const storyViewer = {\n      currentIndex,\n      stories: storyGroup.stories,\n\n      showStory: (index: number) => {\n        const story = storyGroup.stories[index];\n        const contentElement = modalElement.querySelector('#story-content');\n\n        if (contentElement) {\n          if (story.media.type === 'image') {\n            contentElement.innerHTML = `\n              <img src=\"${story.media.url}\" alt=\"${story.caption || ''}\" class=\"story-media\">\n            `;\n          } else {\n            contentElement.innerHTML = `\n              <video src=\"${story.media.url}\" class=\"story-media\" autoplay muted>\n              </video>\n            `;\n          }\n        }\n\n        // Update progress bars\n        const progressBars = modalElement.querySelectorAll('.progress-bar');\n        progressBars.forEach((bar, i) => {\n          bar.classList.remove('active', 'completed');\n          if (i < index) {\n            bar.classList.add('completed');\n          } else if (i === index) {\n            bar.classList.add('active');\n          }\n        });\n\n        this.startProgressTimer(15000); // 15 seconds\n      },\n\n      nextStory: () => {\n        if (currentIndex < storyGroup.stories.length - 1) {\n          currentIndex++;\n          storyViewer.showStory(currentIndex);\n        } else {\n          modalElement.remove();\n        }\n      },\n\n      previousStory: () => {\n        if (currentIndex > 0) {\n          currentIndex--;\n          storyViewer.showStory(currentIndex);\n        }\n      },\n\n      toggleLike: () => {\n        console.log('Toggle like for story');\n      },\n\n      openComments: () => {\n        console.log('Open comments for story');\n      },\n\n      shareStory: () => {\n        console.log('Share story');\n      }\n    };\n\n    // Make it globally accessible for onclick handlers\n    (window as any).storyViewer = storyViewer;\n\n    // Show first story\n    storyViewer.showStory(0);\n\n    // Cleanup when modal is removed\n    const observer = new MutationObserver((mutations) => {\n      mutations.forEach((mutation) => {\n        if (mutation.type === 'childList') {\n          mutation.removedNodes.forEach((node) => {\n            if (node === modalElement) {\n              delete (window as any).storyViewer;\n              if (progressTimer) clearTimeout(progressTimer);\n              observer.disconnect();\n            }\n          });\n        }\n      });\n    });\n\n    observer.observe(document.body, { childList: true });\n  }\n\n  private startProgressTimer(duration: number) {\n    // This would be implemented to handle progress timing\n  }\n\n  private getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n\n    if (hours < 1) {\n      const minutes = Math.floor(diff / (1000 * 60));\n      return `${minutes}m`;\n    } else if (hours < 24) {\n      return `${hours}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      return `${days}d`;\n    }\n  }\n\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n\n\n}\n", "<div class=\"stories-section\">\n  <div class=\"stories-slider-wrapper\">\n    <!-- Navigation Dots (Mobile) -->\n    <div class=\"nav-dots\" *ngIf=\"showDots\">\n      <button \n        *ngFor=\"let dot of dots; let i = index\"\n        class=\"nav-dot\"\n        [class.active]=\"i === currentSlide\"\n        (click)=\"goToSlide(i)\">\n      </button>\n    </div>\n\n    <!-- Stories Slider Container -->\n    <div class=\"stories-slider\"\n         #storiesSlider\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\"\n         (scroll)=\"onScroll()\"\n         (mouseenter)=\"onMouseEnter()\"\n         (mouseleave)=\"onMouseLeave()\">\n      \n      <!-- Stories Track -->\n      <div class=\"stories-track\" \n           [style.transform]=\"'translateX(' + translateX + 'px)'\"\n           [style.transition]=\"isTransitioning ? 'transform 0.3s ease-out' : 'none'\">\n        \n        <!-- Add Story -->\n        <div class=\"story-slide add-story\" (click)=\"openAddStory()\">\n          <div class=\"story-avatar\">\n            <div class=\"avatar-ring add-ring\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n          </div>\n          <span class=\"story-username\">Your Story</span>\n        </div>\n\n        <!-- User Stories -->\n        <div\n          *ngFor=\"let storyGroup of storyGroups; let i = index\"\n          class=\"story-slide\"\n          (click)=\"openStoryViewer(storyGroup)\"\n        >\n          <div class=\"story-avatar\">\n            <div class=\"avatar-ring\" [class.has-story]=\"storyGroup.stories.length > 0\">\n              <img [src]=\"getSafeImageUrl(storyGroup.user.avatar)\"\n                   [alt]=\"storyGroup.user.fullName\"\n                   (error)=\"onImageError($event)\"\n                   class=\"avatar-image\">\n            </div>\n          </div>\n          <span class=\"story-username\">{{ storyGroup.user.username }}</span>\n        </div>\n\n        <!-- Loading More Stories -->\n        <div *ngIf=\"isLoadingMore\" class=\"story-slide loading-slide\">\n          <div class=\"story-avatar\">\n            <div class=\"avatar-ring loading-ring\">\n              <div class=\"loading-spinner\"></div>\n            </div>\n          </div>\n          <span class=\"story-username\">Loading...</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Auto-slide Indicator -->\n    <div class=\"auto-slide-indicator\" *ngIf=\"isAutoSliding && storyGroups.length > slidesPerView\">\n      <div class=\"slide-progress\"></div>\n      <span class=\"slide-text\">Auto-sliding</span>\n    </div>\n\n    <!-- Navigation Arrows (Desktop/Tablet) -->\n    <button class=\"nav-arrow nav-left\"\n            (click)=\"slideLeft()\"\n            [disabled]=\"!canSlideLeft\"\n            *ngIf=\"showArrows\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n\n    <button class=\"nav-arrow nav-right\"\n            (click)=\"slideRight()\"\n            [disabled]=\"!canSlideRight\"\n            *ngIf=\"showArrows\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;;;;;ICGxCC,EAAA,CAAAC,cAAA,iBAIyB;IAAvBD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,IAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAP,IAAA,CAAY;IAAA,EAAC;IACxBJ,EAAA,CAAAY,YAAA,EAAS;;;;;IAFPZ,EAAA,CAAAa,WAAA,WAAAT,IAAA,KAAAI,MAAA,CAAAM,YAAA,CAAmC;;;;;IAJvCd,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAe,UAAA,IAAAC,wCAAA,qBAIyB;IAE3BhB,EAAA,CAAAY,YAAA,EAAM;;;;IALcZ,EAAA,CAAAiB,SAAA,EAAS;IAATjB,EAAA,CAAAkB,UAAA,YAAAV,MAAA,CAAAW,IAAA,CAAS;;;;;;IAiCzBnB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAkB,sDAAA;MAAA,MAAAC,aAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAf,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgB,eAAA,CAAAH,aAAA,CAA2B;IAAA,EAAC;IAIjCrB,EAFJ,CAAAC,cAAA,aAA0B,cACmD,cAI/C;IADrBD,EAAA,CAAAE,UAAA,mBAAAuB,sDAAAC,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAiB,GAAA;MAAA,MAAAd,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmB,YAAA,CAAAD,MAAA,CAAoB;IAAA,EAAC;IAGvC1B,EALI,CAAAY,YAAA,EAG0B,EACtB,EACF;IACNZ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAA4B,MAAA,GAA8B;IAC7D5B,EAD6D,CAAAY,YAAA,EAAO,EAC9D;;;;;IARuBZ,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAa,WAAA,cAAAQ,aAAA,CAAAQ,OAAA,CAAAC,MAAA,KAAiD;IACnE9B,EAAA,CAAAiB,SAAA,EAA+C;IAC/CjB,EADA,CAAAkB,UAAA,QAAAV,MAAA,CAAAuB,eAAA,CAAAV,aAAA,CAAAW,IAAA,CAAAC,MAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAA+C,QAAAb,aAAA,CAAAW,IAAA,CAAAG,QAAA,CACf;IAKZnC,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAoC,iBAAA,CAAAf,aAAA,CAAAW,IAAA,CAAAK,QAAA,CAA8B;;;;;IAMzDrC,EAFJ,CAAAC,cAAA,cAA6D,aACjC,cACc;IACpCD,EAAA,CAAAsC,SAAA,cAAmC;IAEvCtC,EADE,CAAAY,YAAA,EAAM,EACF;IACNZ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAA4B,MAAA,iBAAU;IACzC5B,EADyC,CAAAY,YAAA,EAAO,EAC1C;;;;;IAKVZ,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAsC,SAAA,cAAkC;IAClCtC,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAA4B,MAAA,mBAAY;IACvC5B,EADuC,CAAAY,YAAA,EAAO,EACxC;;;;;;IAGNZ,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAqC,4DAAA;MAAAvC,EAAA,CAAAK,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,SAAA,EAAW;IAAA,EAAC;IAG3BzC,EAAA,CAAAsC,SAAA,YAAmC;IACrCtC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAkC,YAAA,CAA0B;;;;;;IAKlC1C,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAyC,4DAAA;MAAA3C,EAAA,CAAAK,aAAA,CAAAuC,GAAA;MAAA,MAAApC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqC,UAAA,EAAY;IAAA,EAAC;IAG5B7C,EAAA,CAAAsC,SAAA,YAAoC;IACtCtC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAsC,aAAA,CAA2B;;;AD/DvC,OAAM,MAAOC,gBAAgB;EAsC3BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAH1B,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAvCtB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAzC,YAAY,GAAG,CAAC;IAChB,KAAA0C,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAd,YAAY,GAAG,KAAK;IACpB,KAAAI,aAAa,GAAG,KAAK;IACrB,KAAAW,UAAU,GAAG,KAAK;IAClB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAvC,IAAI,GAAa,EAAE;IAEnB;IACA,KAAAwC,WAAW,GAAG,CAAC;IACf,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,CAAC;IAId,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE;EAOhB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACpB,WAAW,CAACqB,YAAY,CAACC,SAAS,CAACxC,IAAI,IAAG;MAC7C,IAAI,CAACsB,WAAW,GAAGtB,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACyC,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;IAEP;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACJ,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAN,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACO,aAAa,EAAE;IAEzB,MAAMC,cAAc,GAAG,IAAI,CAACD,aAAa,CAACE,aAAa,CAACC,WAAW;IACnE,MAAMC,WAAW,GAAGR,MAAM,CAACS,UAAU;IAErC;IACA,IAAID,WAAW,IAAI,IAAI,EAAE;MACvB,IAAI,CAACzB,aAAa,GAAG2B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACrB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAI6B,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACzB,aAAa,GAAG2B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACrB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAI6B,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACzB,aAAa,GAAG2B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACrB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM,IAAI6B,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACzB,aAAa,GAAG2B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACrB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM;MACL,IAAI,CAACI,aAAa,GAAG2B,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACrB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;;IAGtB;IACA,IAAI,CAACI,aAAa,GAAG2B,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC7B,aAAa,EAAE,CAAC,CAAC;IAEpD;IACA,IAAI,IAAI,CAACJ,QAAQ,EAAE;MACjB,MAAMkC,WAAW,GAAG,IAAI,CAACvC,WAAW,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC;MACjD,MAAM+D,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAACF,WAAW,GAAG,IAAI,CAAC9B,aAAa,CAAC;MAC9D,IAAI,CAAC3C,IAAI,GAAG4E,KAAK,CAACF,UAAU,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;;EAE1D;EAEA1B,WAAWA,CAAC2B,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACnD,IAAI,IAAI,CAACnC,aAAa,EAAE;IAExB,IAAI,CAACA,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACjB,YAAY,CAACqD,UAAU,CAACF,IAAI,EAAE,IAAI,CAAC/B,cAAc,CAAC,CAACG,SAAS,CAAC;MAChE+B,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACnD,WAAW,IAAImD,QAAQ,CAACnD,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;UAC3D,IAAIuE,MAAM,EAAE;YACV,IAAI,CAAChD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,EAAE,GAAGmD,QAAQ,CAACnD,WAAW,CAAC;WAClE,MAAM;YACL,IAAI,CAACA,WAAW,GAAGmD,QAAQ,CAACnD,WAAW;;UAGzC;UACA,IAAI,CAACc,cAAc,GAAGqC,QAAQ,CAACnD,WAAW,CAACvB,MAAM,KAAK,IAAI,CAACuC,cAAc;UAEzE;UACAM,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,2BAA2B,EAAE;YAClC,IAAI,CAACC,iBAAiB,EAAE;YACxB,IAAI,CAACwB,MAAM,EAAE;cACX,IAAI,CAACvB,cAAc,EAAE;;UAEzB,CAAC,EAAE,GAAG,CAAC;SACR,MAAM;UACL2B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,IAAI,CAACL,MAAM,EAAE;YACX,IAAI,CAAChD,WAAW,GAAG,EAAE;;UAEvB,IAAI,CAACc,cAAc,GAAG,KAAK;;QAE7B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDyC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACN,MAAM,EAAE;UACX,IAAI,CAAChD,WAAW,GAAG,EAAE;;QAEvB,IAAI,CAACc,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACJ;EAEA0C,eAAeA,CAAA;IACb,IAAI,IAAI,CAACzC,cAAc,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;MAC9C,IAAI,CAACE,WAAW,EAAE;MAClB,IAAI,CAACK,WAAW,CAAC,IAAI,CAACL,WAAW,EAAE,IAAI,CAAC;;EAE5C;EAEA;EACAU,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACzB,WAAW,CAACvB,MAAM,IAAI,IAAI,CAACgC,aAAa,EAAE,OAAO,CAAC;IAE3D,IAAI,CAACoB,aAAa,EAAE,CAAC,CAAC;IACtB,IAAI,CAAC2B,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAAC7C,aAAa,EAAE;QACtB,IAAI,CAAC8C,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC/C,cAAc,CAAC;EACzB;EAEAkB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC2B,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAAChD,aAAa,GAAG,KAAK;EAC5B;EAEAiD,eAAeA,CAAA;IACb,IAAI,CAACjD,aAAa,GAAG,IAAI;EAC3B;EAEA8C,aAAaA,CAAA;IACX,MAAMI,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,IAAI,CAACtG,YAAY,GAAGqG,QAAQ,EAAE;MAChC,IAAI,CAACtE,UAAU,EAAE;KAClB,MAAM;MACL;MACA,IAAI,IAAI,CAACsB,cAAc,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;QAC9C,IAAI,CAAC0C,eAAe,EAAE;OACvB,MAAM;QACL;QACA,IAAI,CAAC9F,YAAY,GAAG,CAAC;QACrB,IAAI,CAACuG,oBAAoB,EAAE;;;EAGjC;EAEA;EACA5E,SAASA,CAAA;IACP,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAACuE,cAAc,EAAE;MACrB,IAAI,CAACnG,YAAY,GAAG2E,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7E,YAAY,GAAG,CAAC,CAAC;MACtD,IAAI,CAACuG,oBAAoB,EAAE;MAC3B;MACA1C,UAAU,CAAC,MAAM,IAAI,CAACuC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEArE,UAAUA,CAAA;IACR,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACmE,cAAc,EAAE;MACrB,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACtG,YAAY,GAAG2E,IAAI,CAAC6B,GAAG,CAACH,QAAQ,EAAE,IAAI,CAACrG,YAAY,GAAG,CAAC,CAAC;MAC7D,IAAI,CAACuG,oBAAoB,EAAE;MAE3B;MACA,IAAI,IAAI,CAACvG,YAAY,IAAIqG,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAChD,cAAc,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;QACnF,IAAI,CAAC0C,eAAe,EAAE;;MAGxB;MACAjC,UAAU,CAAC,MAAM,IAAI,CAACuC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEAvG,SAASA,CAAC4G,UAAkB;IAC1B,IAAI,CAACN,cAAc,EAAE;IACrB,IAAI,CAACnG,YAAY,GAAGyG,UAAU;IAC9B,IAAI,CAACF,oBAAoB,EAAE;IAC3B;IACA1C,UAAU,CAAC,MAAM,IAAI,CAACuC,eAAe,EAAE,EAAE,IAAI,CAAC;EAChD;EAEAG,oBAAoBA,CAAA;IAClB,IAAI,CAAC7D,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACD,UAAU,GAAG,CAAC,IAAI,CAACzC,YAAY,GAAG,IAAI,CAACiD,UAAU,GAAG,IAAI,CAACD,aAAa;IAC3E,IAAI,CAACe,iBAAiB,EAAE;IAExB;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAACnB,eAAe,GAAG,KAAK;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAqB,iBAAiBA,CAAA;IACf,MAAMsC,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,CAAC1E,YAAY,GAAG,IAAI,CAAC5B,YAAY,GAAG,CAAC;IACzC,IAAI,CAACgC,aAAa,GAAG,IAAI,CAAChC,YAAY,GAAGqG,QAAQ;EACnD;EAEAC,WAAWA,CAAA;IACT,MAAMxB,WAAW,GAAG,IAAI,CAACvC,WAAW,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC;IACjD,OAAO2D,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACK,IAAI,CAACF,WAAW,GAAG,IAAI,CAAC9B,aAAa,CAAC,GAAG,CAAC,CAAC;EACrE;EAEA0D,QAAQA,CAAA;IACN;IACA,IAAI,CAAC3C,iBAAiB,EAAE;EAC1B;EAEA;EACA4C,YAAYA,CAACC,KAAiB;IAC5B,IAAI,CAAC/D,WAAW,GAAG+D,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC3C,IAAI,CAAChE,aAAa,GAAG,IAAI,CAACD,WAAW;IACrC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACoD,cAAc,EAAE;EACvB;EAEAY,WAAWA,CAACH,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC7D,UAAU,EAAE;IAEtB,IAAI,CAACD,aAAa,GAAG8D,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC7C,MAAME,MAAM,GAAG,IAAI,CAAClE,aAAa,GAAG,IAAI,CAACD,WAAW;IAEpD;IACA,IAAI8B,IAAI,CAACsC,GAAG,CAACD,MAAM,CAAC,GAAG,EAAE,EAAE;MACzBJ,KAAK,CAACM,cAAc,EAAE;;EAE1B;EAEAC,UAAUA,CAACP,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAC7D,UAAU,EAAE;IAEtB,MAAMiE,MAAM,GAAG,IAAI,CAAClE,aAAa,GAAG,IAAI,CAACD,WAAW;IACpD,MAAMuE,SAAS,GAAG,EAAE,CAAC,CAAC;IAEtB,IAAIzC,IAAI,CAACsC,GAAG,CAACD,MAAM,CAAC,GAAGI,SAAS,EAAE;MAChC,IAAIJ,MAAM,GAAG,CAAC,EAAE;QACd;QACA,IAAI,CAACrF,SAAS,EAAE;OACjB,MAAM;QACL;QACA,IAAI,CAACI,UAAU,EAAE;;KAEpB,MAAM;MACL;MACA8B,UAAU,CAAC,MAAM,IAAI,CAACuC,eAAe,EAAE,EAAE,IAAI,CAAC;;IAGhD,IAAI,CAACrD,UAAU,GAAG,KAAK;EACzB;EAEA;EACA9B,eAAeA,CAACoG,GAAuB;IACrC,OAAO,IAAI,CAAC/E,YAAY,CAACrB,eAAe,CAACoG,GAAG,EAAE,MAAM,CAAC;EACvD;EAEAxG,YAAYA,CAAC+F,KAAY;IACvB,IAAI,CAACtE,YAAY,CAACgF,gBAAgB,CAACV,KAAK,EAAE,MAAM,CAAC;EACnD;EAIAW,YAAYA,CAAA;IACV,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,sBAAsB;IAC/CH,YAAY,CAACI,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2DxB;IAED;IACA,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC9CG,MAAM,CAACC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqLpB;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACjCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACCxD,MAAc,CAACkE,gBAAgB,GAAIC,KAAuB,IAAI;MAC7D,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACzB,IAAIA,KAAK,IAAIA,KAAK,CAACrH,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMsH,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;QACrB1C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0C,IAAI,CAACC,IAAI,EAAED,IAAI,CAACE,IAAI,CAAC;QAEnD;QACA,IAAI,CAACC,eAAe,CAACH,IAAI,CAAC;QAC1Bb,YAAY,CAACiB,MAAM,EAAE;;IAEzB,CAAC;EACH;EAEQD,eAAeA,CAACH,IAAU;IAChC,MAAMK,YAAY,GAAGjB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDgB,YAAY,CAACf,SAAS,GAAG,qBAAqB;IAE9C,MAAMgB,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACzC,MAAMS,OAAO,GAAGT,IAAI,CAACE,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC;IAE9CL,YAAY,CAACd,SAAS,GAAG;;;;;;;;;;YAUjBkB,OAAO,GACP,eAAeH,OAAO,oCAAoC,GAC1D,aAAaA,OAAO,wBACtB;;;;;;;;;;;;;;KAcL;IAED;IACA,MAAMK,aAAa,GAAGvB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACrDsB,aAAa,CAAClB,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8E3B;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACgB,aAAa,CAAC;IACxCvB,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACU,YAAY,CAAC;EACzC;EAEAjI,eAAeA,CAACwI,UAAsB;IACpC,IAAIA,UAAU,CAACnI,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACjC;MACA,IAAI,CAACmI,sBAAsB,CAACD,UAAU,CAAC;KACxC,MAAM;MACLvD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsD,UAAU,CAAChI,IAAI,CAACK,QAAQ,CAAC;;EAEtE;EAEQ4H,sBAAsBA,CAACD,UAAsB;IACnD;IACA,MAAMzB,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,oBAAoB;IAC7CH,YAAY,CAACI,SAAS,GAAG;;;;;cAKfqB,UAAU,CAACnI,OAAO,CAACoE,GAAG,CAAC,CAACC,CAAC,EAAE3F,KAAK,KAAK;yCACVA,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,iBAAiBA,KAAK;;;aAG7E,CAAC,CAAC2J,IAAI,CAAC,EAAE,CAAC;;;;;;0BAMGF,UAAU,CAAChI,IAAI,CAACC,MAAM,IAAI,mCAAmC;0BAC7D+H,UAAU,CAAChI,IAAI,CAACG,QAAQ;;yCAET6H,UAAU,CAAChI,IAAI,CAACK,QAAQ;0CACvB,IAAI,CAAC8H,UAAU,CAACH,UAAU,CAACnI,OAAO,CAAC,CAAC,CAAC,CAACuI,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmCrF;IAED;IACA,MAAMxB,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC9CG,MAAM,CAACC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2LpB;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACjCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACA,IAAI,CAAC8B,qBAAqB,CAACL,UAAU,EAAEzB,YAAY,CAAC;EACtD;EAEQ8B,qBAAqBA,CAACL,UAAsB,EAAEM,YAAyB;IAC7E,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,aAAkB;IAEtB,MAAMC,WAAW,GAAG;MAClBF,YAAY;MACZ1I,OAAO,EAAEmI,UAAU,CAACnI,OAAO;MAE3B6I,SAAS,EAAGnK,KAAa,IAAI;QAC3B,MAAMoK,KAAK,GAAGX,UAAU,CAACnI,OAAO,CAACtB,KAAK,CAAC;QACvC,MAAMqK,cAAc,GAAGN,YAAY,CAACO,aAAa,CAAC,gBAAgB,CAAC;QAEnE,IAAID,cAAc,EAAE;UAClB,IAAID,KAAK,CAACG,KAAK,CAACxB,IAAI,KAAK,OAAO,EAAE;YAChCsB,cAAc,CAACjC,SAAS,GAAG;0BACbgC,KAAK,CAACG,KAAK,CAAC3C,GAAG,UAAUwC,KAAK,CAACI,OAAO,IAAI,EAAE;aACzD;WACF,MAAM;YACLH,cAAc,CAACjC,SAAS,GAAG;4BACXgC,KAAK,CAACG,KAAK,CAAC3C,GAAG;;aAE9B;;;QAIL;QACA,MAAM6C,YAAY,GAAGV,YAAY,CAACW,gBAAgB,CAAC,eAAe,CAAC;QACnED,YAAY,CAACE,OAAO,CAAC,CAACC,GAAG,EAAEhF,CAAC,KAAI;UAC9BgF,GAAG,CAACC,SAAS,CAAC5B,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;UAC3C,IAAIrD,CAAC,GAAG5F,KAAK,EAAE;YACb4K,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;WAC/B,MAAM,IAAIlF,CAAC,KAAK5F,KAAK,EAAE;YACtB4K,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;;QAE/B,CAAC,CAAC;QAEF,IAAI,CAACC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;MAClC,CAAC;MAEDC,SAAS,EAAEA,CAAA,KAAK;QACd,IAAIhB,YAAY,GAAGP,UAAU,CAACnI,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UAChDyI,YAAY,EAAE;UACdE,WAAW,CAACC,SAAS,CAACH,YAAY,CAAC;SACpC,MAAM;UACLD,YAAY,CAACd,MAAM,EAAE;;MAEzB,CAAC;MAEDgC,aAAa,EAAEA,CAAA,KAAK;QAClB,IAAIjB,YAAY,GAAG,CAAC,EAAE;UACpBA,YAAY,EAAE;UACdE,WAAW,CAACC,SAAS,CAACH,YAAY,CAAC;;MAEvC,CAAC;MAEDkB,UAAU,EAAEA,CAAA,KAAK;QACfhF,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtC,CAAC;MAEDgF,YAAY,EAAEA,CAAA,KAAK;QACjBjF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACxC,CAAC;MAEDiF,UAAU,EAAEA,CAAA,KAAK;QACflF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC5B;KACD;IAED;IACC3B,MAAc,CAAC0F,WAAW,GAAGA,WAAW;IAEzC;IACAA,WAAW,CAACC,SAAS,CAAC,CAAC,CAAC;IAExB;IACA,MAAMkB,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAI;MAClDA,SAAS,CAACZ,OAAO,CAAEa,QAAQ,IAAI;QAC7B,IAAIA,QAAQ,CAACzC,IAAI,KAAK,WAAW,EAAE;UACjCyC,QAAQ,CAACC,YAAY,CAACd,OAAO,CAAEe,IAAI,IAAI;YACrC,IAAIA,IAAI,KAAK3B,YAAY,EAAE;cACzB,OAAQvF,MAAc,CAAC0F,WAAW;cAClC,IAAID,aAAa,EAAE0B,YAAY,CAAC1B,aAAa,CAAC;cAC9CoB,QAAQ,CAACO,UAAU,EAAE;;UAEzB,CAAC,CAAC;;MAEN,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFP,QAAQ,CAACQ,OAAO,CAAC5D,QAAQ,CAACQ,IAAI,EAAE;MAAEqD,SAAS,EAAE;IAAI,CAAE,CAAC;EACtD;EAEQf,kBAAkBA,CAACgB,QAAgB;IACzC;EAAA;EAGMnC,UAAUA,CAACoC,IAAU;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACrD,MAAMC,KAAK,GAAGnH,IAAI,CAACC,KAAK,CAACgH,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIE,KAAK,GAAG,CAAC,EAAE;MACb,MAAMC,OAAO,GAAGpH,IAAI,CAACC,KAAK,CAACgH,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;MAC9C,OAAO,GAAGG,OAAO,GAAG;KACrB,MAAM,IAAID,KAAK,GAAG,EAAE,EAAE;MACrB,OAAO,GAAGA,KAAK,GAAG;KACnB,MAAM;MACL,MAAME,IAAI,GAAGrH,IAAI,CAACC,KAAK,CAACkH,KAAK,GAAG,EAAE,CAAC;MACnC,OAAO,GAAGE,IAAI,GAAG;;EAErB;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAAC9F,cAAc,EAAE;EACvB;EAEA+F,YAAYA,CAAA;IACV,IAAI,CAAC9F,eAAe,EAAE;EACxB;;;uBAnlCWnE,gBAAgB,EAAA/C,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAnN,EAAA,CAAAiN,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAArN,EAAA,CAAAiN,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAvN,EAAA,CAAAiN,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAhB1K,gBAAgB;MAAA2K,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UClB3B7N,EADF,CAAAC,cAAA,aAA6B,aACS;UAElCD,EAAA,CAAAe,UAAA,IAAAgN,+BAAA,iBAAuC;UAUvC/N,EAAA,CAAAC,cAAA,gBAOmC;UAA9BD,EALA,CAAAE,UAAA,wBAAA8N,oDAAAtM,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAA4N,GAAA;YAAA,OAAAjO,EAAA,CAAAU,WAAA,CAAcoN,GAAA,CAAArG,YAAA,CAAA/F,MAAA,CAAoB;UAAA,EAAC,uBAAAwM,mDAAAxM,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAA4N,GAAA;YAAA,OAAAjO,EAAA,CAAAU,WAAA,CACtBoN,GAAA,CAAAjG,WAAA,CAAAnG,MAAA,CAAmB;UAAA,EAAC,sBAAAyM,kDAAAzM,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAA4N,GAAA;YAAA,OAAAjO,EAAA,CAAAU,WAAA,CACrBoN,GAAA,CAAA7F,UAAA,CAAAvG,MAAA,CAAkB;UAAA,EAAC,oBAAA0M,gDAAA;YAAApO,EAAA,CAAAK,aAAA,CAAA4N,GAAA;YAAA,OAAAjO,EAAA,CAAAU,WAAA,CACrBoN,GAAA,CAAAtG,QAAA,EAAU;UAAA,EAAC,wBAAA6G,oDAAA;YAAArO,EAAA,CAAAK,aAAA,CAAA4N,GAAA;YAAA,OAAAjO,EAAA,CAAAU,WAAA,CACPoN,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC,wBAAAuB,oDAAA;YAAAtO,EAAA,CAAAK,aAAA,CAAA4N,GAAA;YAAA,OAAAjO,EAAA,CAAAU,WAAA,CACfoN,GAAA,CAAAd,YAAA,EAAc;UAAA,EAAC;UAQ9BhN,EALF,CAAAC,cAAA,aAE+E,aAGjB;UAAzBD,EAAA,CAAAE,UAAA,mBAAAqO,+CAAA;YAAAvO,EAAA,CAAAK,aAAA,CAAA4N,GAAA;YAAA,OAAAjO,EAAA,CAAAU,WAAA,CAASoN,GAAA,CAAAzF,YAAA,EAAc;UAAA,EAAC;UAEvDrI,EADF,CAAAC,cAAA,aAA0B,aACU;UAChCD,EAAA,CAAAsC,SAAA,WAA2B;UAE/BtC,EADE,CAAAY,YAAA,EAAM,EACF;UACNZ,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAA4B,MAAA,kBAAU;UACzC5B,EADyC,CAAAY,YAAA,EAAO,EAC1C;UAoBNZ,EAjBA,CAAAe,UAAA,KAAAyN,gCAAA,kBAIC,KAAAC,gCAAA,kBAa4D;UASjEzO,EADE,CAAAY,YAAA,EAAM,EACF;UAgBNZ,EAbA,CAAAe,UAAA,KAAA2N,gCAAA,kBAA8F,KAAAC,mCAAA,qBASnE,KAAAC,mCAAA,qBAOA;UAI/B5O,EADE,CAAAY,YAAA,EAAM,EACF;;;UApFqBZ,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAA4M,GAAA,CAAApK,QAAA,CAAc;UAqB9B1D,EAAA,CAAAiB,SAAA,GAAsD;UACtDjB,EADA,CAAA6O,WAAA,8BAAAf,GAAA,CAAAvK,UAAA,SAAsD,eAAAuK,GAAA,CAAAtK,eAAA,sCACmB;UAcnDxD,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,YAAA4M,GAAA,CAAAzK,WAAA,CAAgB;UAgBnCrD,EAAA,CAAAiB,SAAA,EAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,SAAA4M,GAAA,CAAA5J,aAAA,CAAmB;UAYMlE,EAAA,CAAAiB,SAAA,EAAyD;UAAzDjB,EAAA,CAAAkB,UAAA,SAAA4M,GAAA,CAAA7J,aAAA,IAAA6J,GAAA,CAAAzK,WAAA,CAAAvB,MAAA,GAAAgM,GAAA,CAAAhK,aAAA,CAAyD;UASnF9D,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAA4M,GAAA,CAAArK,UAAA,CAAgB;UAOhBzD,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAA4M,GAAA,CAAArK,UAAA,CAAgB;;;qBDtEjB1D,YAAY,EAAA+O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAApG,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}