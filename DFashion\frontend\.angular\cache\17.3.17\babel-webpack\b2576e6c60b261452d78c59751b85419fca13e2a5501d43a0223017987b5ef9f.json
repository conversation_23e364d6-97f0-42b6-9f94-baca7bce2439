{"ast": null, "code": "import { AuthGuard } from './core/guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n}, {\n  path: 'home',\n  loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'profile',\n  loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'shop',\n  loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'search',\n  loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n}, {\n  path: '',\n  loadChildren: () => import('./features/story/story.routes').then(m => m.storyRoutes)\n}, {\n  path: 'product/:id',\n  loadComponent: () => import('./features/product/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n}, {\n  path: 'admin',\n  loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n}, {\n  path: '**',\n  redirectTo: '/home'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "authRoutes", "homeRoutes", "canActivate", "profileRoutes", "shopRoutes", "searchRoutes", "storyRoutes", "loadComponent", "ProductDetailComponent", "AdminModule"], "sources": ["E:\\DFashion\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    redirectTo: '/home',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n  },\n  {\n    path: 'home',\n    loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'shop',\n    loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'search',\n    loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n  },\n  {\n    path: '',\n    loadChildren: () => import('./features/story/story.routes').then(m => m.storyRoutes)\n  },\n  {\n    path: 'product/:id',\n    loadComponent: () => import('./features/product/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n  },\n  {\n    path: 'admin',\n    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n  },\n  {\n    path: '**',\n    redirectTo: '/home'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,0BAA0B;AAEpD,OAAO,MAAMC,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF,EACD;EACEN,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,UAAU,CAAC;EACjFC,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,aAAa,CAAC;EAC1FD,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,UAAU,CAAC;EACjFF,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,YAAY;CACvF,EACD;EACEX,IAAI,EAAE,EAAE;EACRG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,WAAW;CACpF,EACD;EACEZ,IAAI,EAAE,aAAa;EACnBa,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4DAA4D,CAAC,CAACT,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,sBAAsB;CAC7H,EACD;EACEd,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,WAAW;CAC3E,EACD;EACEf,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}