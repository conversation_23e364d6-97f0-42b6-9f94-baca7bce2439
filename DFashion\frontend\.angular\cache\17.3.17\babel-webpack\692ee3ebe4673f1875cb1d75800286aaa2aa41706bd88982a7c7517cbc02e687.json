{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { r as raf, g as getElementRoot } from './helpers-be245865.js';\nimport { a as hapticSelectionStart, b as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-554688a5.js';\nimport { a as isPlatform, b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst pickerColumnInternalIosCss = \":host{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;height:200px;outline:none;font-size:22px;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none;text-align:center}:host::-webkit-scrollbar{display:none}:host .picker-item{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden;scroll-snap-align:center}:host .picker-item-empty,:host .picker-item[disabled]{cursor:default}:host .picker-item-empty,:host(:not([disabled])) .picker-item[disabled]{scroll-snap-align:none}:host([disabled]){overflow-y:hidden}:host .picker-item[disabled]{opacity:0.4}:host(.picker-column-active) .picker-item.picker-item-active{color:var(--ion-color-base)}@media (any-hover: hover){:host(:focus){outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}\";\nconst IonPickerColumnInternalIosStyle0 = pickerColumnInternalIosCss;\nconst pickerColumnInternalMdCss = \":host{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;height:200px;outline:none;font-size:22px;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none;text-align:center}:host::-webkit-scrollbar{display:none}:host .picker-item{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden;scroll-snap-align:center}:host .picker-item-empty,:host .picker-item[disabled]{cursor:default}:host .picker-item-empty,:host(:not([disabled])) .picker-item[disabled]{scroll-snap-align:none}:host([disabled]){overflow-y:hidden}:host .picker-item[disabled]{opacity:0.4}:host(.picker-column-active) .picker-item.picker-item-active{color:var(--ion-color-base)}@media (any-hover: hover){:host(:focus){outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}:host .picker-item-active{color:var(--ion-color-base)}\";\nconst IonPickerColumnInternalMdStyle0 = pickerColumnInternalMdCss;\nconst PickerColumnInternal = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.isScrolling = false;\n    this.isColumnVisible = false;\n    this.canExitInputMode = true;\n    this.centerPickerItemInView = (target, smooth = true, canExitInputMode = true) => {\n      const {\n        el,\n        isColumnVisible\n      } = this;\n      if (isColumnVisible) {\n        // (Vertical offset from parent) - (three empty picker rows) + (half the height of the target to ensure the scroll triggers)\n        const top = target.offsetTop - 3 * target.clientHeight + target.clientHeight / 2;\n        if (el.scrollTop !== top) {\n          /**\n           * Setting this flag prevents input\n           * mode from exiting in the picker column's\n           * scroll callback. This is useful when the user manually\n           * taps an item or types on the keyboard as both\n           * of these can cause a scroll to occur.\n           */\n          this.canExitInputMode = canExitInputMode;\n          el.scroll({\n            top,\n            left: 0,\n            behavior: smooth ? 'smooth' : undefined\n          });\n        }\n      }\n    };\n    this.setPickerItemActiveState = (item, isActive) => {\n      if (isActive) {\n        item.classList.add(PICKER_ITEM_ACTIVE_CLASS);\n        item.part.add(PICKER_ITEM_ACTIVE_PART);\n      } else {\n        item.classList.remove(PICKER_ITEM_ACTIVE_CLASS);\n        item.part.remove(PICKER_ITEM_ACTIVE_PART);\n      }\n    };\n    /**\n     * When ionInputModeChange is emitted, each column\n     * needs to check if it is the one being made available\n     * for text entry.\n     */\n    this.inputModeChange = ev => {\n      if (!this.numericInput) {\n        return;\n      }\n      const {\n        useInputMode,\n        inputModeColumn\n      } = ev.detail;\n      /**\n       * If inputModeColumn is undefined then this means\n       * all numericInput columns are being selected.\n       */\n      const isColumnActive = inputModeColumn === undefined || inputModeColumn === this.el;\n      if (!useInputMode || !isColumnActive) {\n        this.setInputModeActive(false);\n        return;\n      }\n      this.setInputModeActive(true);\n    };\n    /**\n     * Setting isActive will cause a re-render.\n     * As a result, we do not want to cause the\n     * re-render mid scroll as this will cause\n     * the picker column to jump back to\n     * whatever value was selected at the\n     * start of the scroll interaction.\n     */\n    this.setInputModeActive = state => {\n      if (this.isScrolling) {\n        this.scrollEndCallback = () => {\n          this.isActive = state;\n        };\n        return;\n      }\n      this.isActive = state;\n    };\n    /**\n     * When the column scrolls, the component\n     * needs to determine which item is centered\n     * in the view and will emit an ionChange with\n     * the item object.\n     */\n    this.initializeScrollListener = () => {\n      /**\n       * The haptics for the wheel picker are\n       * an iOS-only feature. As a result, they should\n       * be disabled on Android.\n       */\n      const enableHaptics = isPlatform('ios');\n      const {\n        el\n      } = this;\n      let timeout;\n      let activeEl = this.activeItem;\n      const scrollCallback = () => {\n        raf(() => {\n          if (timeout) {\n            clearTimeout(timeout);\n            timeout = undefined;\n          }\n          if (!this.isScrolling) {\n            enableHaptics && hapticSelectionStart();\n            this.isScrolling = true;\n          }\n          /**\n           * Select item in the center of the column\n           * which is the month/year that we want to select\n           */\n          const bbox = el.getBoundingClientRect();\n          const centerX = bbox.x + bbox.width / 2;\n          const centerY = bbox.y + bbox.height / 2;\n          const activeElement = el.shadowRoot.elementFromPoint(centerX, centerY);\n          if (activeEl !== null) {\n            this.setPickerItemActiveState(activeEl, false);\n          }\n          if (activeElement === null || activeElement.disabled) {\n            return;\n          }\n          /**\n           * If we are selecting a new value,\n           * we need to run haptics again.\n           */\n          if (activeElement !== activeEl) {\n            enableHaptics && hapticSelectionChanged();\n            if (this.canExitInputMode) {\n              /**\n               * The native iOS wheel picker\n               * only dismisses the keyboard\n               * once the selected item has changed\n               * as a result of a swipe\n               * from the user. If `canExitInputMode` is\n               * `false` then this means that the\n               * scroll is happening as a result of\n               * the `value` property programmatically changing\n               * either by an application or by the user via the keyboard.\n               */\n              this.exitInputMode();\n            }\n          }\n          activeEl = activeElement;\n          this.setPickerItemActiveState(activeElement, true);\n          timeout = setTimeout(() => {\n            this.isScrolling = false;\n            enableHaptics && hapticSelectionEnd();\n            /**\n             * Certain tasks (such as those that\n             * cause re-renders) should only be done\n             * once scrolling has finished, otherwise\n             * flickering may occur.\n             */\n            const {\n              scrollEndCallback\n            } = this;\n            if (scrollEndCallback) {\n              scrollEndCallback();\n              this.scrollEndCallback = undefined;\n            }\n            /**\n             * Reset this flag as the\n             * next scroll interaction could\n             * be a scroll from the user. In this\n             * case, we should exit input mode.\n             */\n            this.canExitInputMode = true;\n            const dataIndex = activeElement.getAttribute('data-index');\n            /**\n             * If no value it is\n             * possible we hit one of the\n             * empty padding columns.\n             */\n            if (dataIndex === null) {\n              return;\n            }\n            const index = parseInt(dataIndex, 10);\n            const selectedItem = this.items[index];\n            if (selectedItem.value !== this.value) {\n              this.setValue(selectedItem.value);\n            }\n          }, 250);\n        });\n      };\n      /**\n       * Wrap this in an raf so that the scroll callback\n       * does not fire when component is initially shown.\n       */\n      raf(() => {\n        el.addEventListener('scroll', scrollCallback);\n        this.destroyScrollListener = () => {\n          el.removeEventListener('scroll', scrollCallback);\n        };\n      });\n    };\n    /**\n     * Tells the parent picker to\n     * exit text entry mode. This is only called\n     * when the selected item changes during scroll, so\n     * we know that the user likely wants to scroll\n     * instead of type.\n     */\n    this.exitInputMode = () => {\n      const {\n        parentEl\n      } = this;\n      if (parentEl == null) return;\n      parentEl.exitInputMode();\n      /**\n       * setInputModeActive only takes\n       * effect once scrolling stops to avoid\n       * a component re-render while scrolling.\n       * However, we want the visual active\n       * indicator to go away immediately, so\n       * we call classList.remove here.\n       */\n      this.el.classList.remove('picker-column-active');\n    };\n    this.isActive = false;\n    this.disabled = false;\n    this.items = [];\n    this.value = undefined;\n    this.color = 'primary';\n    this.numericInput = false;\n  }\n  valueChange() {\n    if (this.isColumnVisible) {\n      /**\n       * Only scroll the active item into view when the picker column\n       * is actively visible to the user.\n       */\n      this.scrollActiveItemIntoView();\n    }\n  }\n  /**\n   * Only setup scroll listeners\n   * when the picker is visible, otherwise\n   * the container will have a scroll\n   * height of 0px.\n   */\n  componentWillLoad() {\n    const visibleCallback = entries => {\n      /**\n       * Browsers will sometimes group multiple IO events into a single callback.\n       * As a result, we want to grab the last/most recent event in case there are multiple events.\n       */\n      const ev = entries[entries.length - 1];\n      if (ev.isIntersecting) {\n        const {\n          activeItem,\n          el\n        } = this;\n        this.isColumnVisible = true;\n        /**\n         * Because this initial call to scrollActiveItemIntoView has to fire before\n         * the scroll listener is set up, we need to manage the active class manually.\n         */\n        const oldActive = getElementRoot(el).querySelector(`.${PICKER_ITEM_ACTIVE_CLASS}`);\n        if (oldActive) {\n          this.setPickerItemActiveState(oldActive, false);\n        }\n        this.scrollActiveItemIntoView();\n        if (activeItem) {\n          this.setPickerItemActiveState(activeItem, true);\n        }\n        this.initializeScrollListener();\n      } else {\n        this.isColumnVisible = false;\n        if (this.destroyScrollListener) {\n          this.destroyScrollListener();\n          this.destroyScrollListener = undefined;\n        }\n      }\n    };\n    new IntersectionObserver(visibleCallback, {\n      threshold: 0.001\n    }).observe(this.el);\n    const parentEl = this.parentEl = this.el.closest('ion-picker-internal');\n    if (parentEl !== null) {\n      // TODO(FW-2832): type\n      parentEl.addEventListener('ionInputModeChange', ev => this.inputModeChange(ev));\n    }\n  }\n  componentDidRender() {\n    var _a;\n    const {\n      activeItem,\n      items,\n      isColumnVisible,\n      value\n    } = this;\n    if (isColumnVisible) {\n      if (activeItem) {\n        this.scrollActiveItemIntoView();\n      } else if (((_a = items[0]) === null || _a === void 0 ? void 0 : _a.value) !== value) {\n        /**\n         * If the picker column does not have an active item and the current value\n         * does not match the first item in the picker column, that means\n         * the value is out of bounds. In this case, we assign the value to the\n         * first item to match the scroll position of the column.\n         *\n         */\n        this.setValue(items[0].value);\n      }\n    }\n  }\n  /** @internal  */\n  scrollActiveItemIntoView() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const activeEl = _this.activeItem;\n      if (activeEl) {\n        _this.centerPickerItemInView(activeEl, false, false);\n      }\n    })();\n  }\n  /**\n   * Sets the value prop and fires the ionChange event.\n   * This is used when we need to fire ionChange from\n   * user-generated events that cannot be caught with normal\n   * input/change event listeners.\n   * @internal\n   */\n  setValue(value) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        items\n      } = _this2;\n      _this2.value = value;\n      const findItem = items.find(item => item.value === value && item.disabled !== true);\n      if (findItem) {\n        _this2.ionChange.emit(findItem);\n      }\n    })();\n  }\n  get activeItem() {\n    // If the whole picker column is disabled, the current value should appear active\n    // If the current value item is specifically disabled, it should not appear active\n    const selector = `.picker-item[data-value=\"${this.value}\"]${this.disabled ? '' : ':not([disabled])'}`;\n    return getElementRoot(this.el).querySelector(selector);\n  }\n  render() {\n    const {\n      items,\n      color,\n      disabled: pickerDisabled,\n      isActive,\n      numericInput\n    } = this;\n    const mode = getIonMode(this);\n    /**\n     * exportparts is needed so ion-datetime can expose the parts\n     * from two layers of shadow nesting. If this causes problems,\n     * the attribute can be moved to datetime.tsx and set on every\n     * instance of ion-picker-column-internal there instead.\n     */\n    return h(Host, {\n      key: '42a034f2533d30d19f96a121eb74d5f757e1c684',\n      exportparts: `${PICKER_ITEM_PART}, ${PICKER_ITEM_ACTIVE_PART}`,\n      disabled: pickerDisabled,\n      tabindex: pickerDisabled ? null : 0,\n      class: createColorClasses(color, {\n        [mode]: true,\n        ['picker-column-active']: isActive,\n        ['picker-column-numeric-input']: numericInput\n      })\n    }, h(\"div\", {\n      key: '85efccb40c87d473c06026b8041d57b40d2369c3',\n      class: \"picker-item picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '9fae4dd6697f23acba18c218ba250ea77954b18d',\n      class: \"picker-item picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: 'f117afeb204a4f6bb34a1cd0e1b786fa479d8b32',\n      class: \"picker-item picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), items.map((item, index) => {\n      const isItemDisabled = pickerDisabled || item.disabled || false;\n      return h(\"button\", {\n        tabindex: \"-1\",\n        class: {\n          'picker-item': true\n        },\n        \"data-value\": item.value,\n        \"data-index\": index,\n        onClick: ev => {\n          this.centerPickerItemInView(ev.target, true);\n        },\n        disabled: isItemDisabled,\n        part: PICKER_ITEM_PART\n      }, item.text);\n    }), h(\"div\", {\n      key: '28aa37f9ce90e88b9c3a5b2c399e3066e9f339e1',\n      class: \"picker-item picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: 'ef4ae6bee2b17918f0c2aba9d5c720c1d95987e4',\n      class: \"picker-item picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '564967bc8e42a9018163850da3a967a933b3de7b',\n      class: \"picker-item picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChange\"]\n    };\n  }\n};\nconst PICKER_ITEM_ACTIVE_CLASS = 'picker-item-active';\nconst PICKER_ITEM_PART = 'wheel-item';\nconst PICKER_ITEM_ACTIVE_PART = 'active';\nPickerColumnInternal.style = {\n  ios: IonPickerColumnInternalIosStyle0,\n  md: IonPickerColumnInternalMdStyle0\n};\nexport { PickerColumnInternal as ion_picker_column_internal };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}