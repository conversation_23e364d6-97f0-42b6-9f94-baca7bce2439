{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nclass GestureController {\n  constructor() {\n    this.gestureId = 0;\n    this.requestedStart = new Map();\n    this.disabledGestures = new Map();\n    this.disabledScroll = new Set();\n  }\n  /**\n   * Creates a gesture delegate based on the GestureConfig passed\n   */\n  createGesture(config) {\n    var _a;\n    return new GestureDelegate(this, this.newID(), config.name, (_a = config.priority) !== null && _a !== void 0 ? _a : 0, !!config.disableScroll);\n  }\n  /**\n   * Creates a blocker that will block any other gesture events from firing. Set in the ion-gesture component.\n   */\n  createBlocker(opts = {}) {\n    return new BlockerDelegate(this, this.newID(), opts.disable, !!opts.disableScroll);\n  }\n  start(gestureName, id, priority) {\n    if (!this.canStart(gestureName)) {\n      this.requestedStart.delete(id);\n      return false;\n    }\n    this.requestedStart.set(id, priority);\n    return true;\n  }\n  capture(gestureName, id, priority) {\n    if (!this.start(gestureName, id, priority)) {\n      return false;\n    }\n    const requestedStart = this.requestedStart;\n    let maxPriority = -10000;\n    requestedStart.forEach(value => {\n      maxPriority = Math.max(maxPriority, value);\n    });\n    if (maxPriority === priority) {\n      this.capturedId = id;\n      requestedStart.clear();\n      const event = new CustomEvent('ionGestureCaptured', {\n        detail: {\n          gestureName\n        }\n      });\n      document.dispatchEvent(event);\n      return true;\n    }\n    requestedStart.delete(id);\n    return false;\n  }\n  release(id) {\n    this.requestedStart.delete(id);\n    if (this.capturedId === id) {\n      this.capturedId = undefined;\n    }\n  }\n  disableGesture(gestureName, id) {\n    let set = this.disabledGestures.get(gestureName);\n    if (set === undefined) {\n      set = new Set();\n      this.disabledGestures.set(gestureName, set);\n    }\n    set.add(id);\n  }\n  enableGesture(gestureName, id) {\n    const set = this.disabledGestures.get(gestureName);\n    if (set !== undefined) {\n      set.delete(id);\n    }\n  }\n  disableScroll(id) {\n    this.disabledScroll.add(id);\n    if (this.disabledScroll.size === 1) {\n      document.body.classList.add(BACKDROP_NO_SCROLL);\n    }\n  }\n  enableScroll(id) {\n    this.disabledScroll.delete(id);\n    if (this.disabledScroll.size === 0) {\n      document.body.classList.remove(BACKDROP_NO_SCROLL);\n    }\n  }\n  canStart(gestureName) {\n    if (this.capturedId !== undefined) {\n      // a gesture already captured\n      return false;\n    }\n    if (this.isDisabled(gestureName)) {\n      return false;\n    }\n    return true;\n  }\n  isCaptured() {\n    return this.capturedId !== undefined;\n  }\n  isScrollDisabled() {\n    return this.disabledScroll.size > 0;\n  }\n  isDisabled(gestureName) {\n    const disabled = this.disabledGestures.get(gestureName);\n    if (disabled && disabled.size > 0) {\n      return true;\n    }\n    return false;\n  }\n  newID() {\n    this.gestureId++;\n    return this.gestureId;\n  }\n}\nclass GestureDelegate {\n  constructor(ctrl, id, name, priority, disableScroll) {\n    this.id = id;\n    this.name = name;\n    this.disableScroll = disableScroll;\n    this.priority = priority * 1000000 + id;\n    this.ctrl = ctrl;\n  }\n  canStart() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.canStart(this.name);\n  }\n  start() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.start(this.name, this.id, this.priority);\n  }\n  capture() {\n    if (!this.ctrl) {\n      return false;\n    }\n    const captured = this.ctrl.capture(this.name, this.id, this.priority);\n    if (captured && this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n    return captured;\n  }\n  release() {\n    if (this.ctrl) {\n      this.ctrl.release(this.id);\n      if (this.disableScroll) {\n        this.ctrl.enableScroll(this.id);\n      }\n    }\n  }\n  destroy() {\n    this.release();\n    this.ctrl = undefined;\n  }\n}\nclass BlockerDelegate {\n  constructor(ctrl, id, disable, disableScroll) {\n    this.id = id;\n    this.disable = disable;\n    this.disableScroll = disableScroll;\n    this.ctrl = ctrl;\n  }\n  block() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.disableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n  }\n  unblock() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.enableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.enableScroll(this.id);\n    }\n  }\n  destroy() {\n    this.unblock();\n    this.ctrl = undefined;\n  }\n}\nconst BACKDROP_NO_SCROLL = 'backdrop-no-scroll';\nconst GESTURE_CONTROLLER = new GestureController();\nexport { GESTURE_CONTROLLER as G };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}