{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { o as assert, s as shallowEqualStringMap } from './helpers-be245865.js';\nimport { l as lifecycle, t as transition, s as setPageHidden, d as LIFECYCLE_WILL_UNLOAD, b as LIFECYCLE_WILL_LEAVE, c as LIFECYCLE_DID_LEAVE } from './index-fae1515c.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { a as attachComponent } from './framework-delegate-ed4ba327.js';\nconst VIEW_STATE_NEW = 1;\nconst VIEW_STATE_ATTACHED = 2;\nconst VIEW_STATE_DESTROYED = 3;\n// TODO(FW-2832): types\nclass ViewController {\n  constructor(component, params) {\n    this.component = component;\n    this.params = params;\n    this.state = VIEW_STATE_NEW;\n  }\n  init(container) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.state = VIEW_STATE_ATTACHED;\n      if (!_this.element) {\n        const component = _this.component;\n        _this.element = yield attachComponent(_this.delegate, container, component, ['ion-page', 'ion-page-invisible'], _this.params);\n      }\n    })();\n  }\n  /**\n   * DOM WRITE\n   */\n  _destroy() {\n    assert(this.state !== VIEW_STATE_DESTROYED, 'view state must be ATTACHED');\n    const element = this.element;\n    if (element) {\n      if (this.delegate) {\n        this.delegate.removeViewFromDom(element.parentElement, element);\n      } else {\n        element.remove();\n      }\n    }\n    this.nav = undefined;\n    this.state = VIEW_STATE_DESTROYED;\n  }\n}\nconst matches = (view, id, params) => {\n  if (!view) {\n    return false;\n  }\n  if (view.component !== id) {\n    return false;\n  }\n  return shallowEqualStringMap(view.params, params);\n};\nconst convertToView = (page, params) => {\n  if (!page) {\n    return null;\n  }\n  if (page instanceof ViewController) {\n    return page;\n  }\n  return new ViewController(page, params);\n};\nconst convertToViews = pages => {\n  return pages.map(page => {\n    if (page instanceof ViewController) {\n      return page;\n    }\n    if ('component' in page) {\n      return convertToView(page.component, page.componentProps === null ? undefined : page.componentProps);\n    }\n    return convertToView(page, undefined);\n  }).filter(v => v !== null);\n};\nconst navCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst IonNavStyle0 = navCss;\nconst Nav = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n    this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n    this.transInstr = [];\n    this.gestureOrAnimationInProgress = false;\n    this.useRouter = false;\n    this.isTransitioning = false;\n    this.destroyed = false;\n    this.views = [];\n    this.didLoad = false;\n    this.delegate = undefined;\n    this.swipeGesture = undefined;\n    this.animated = true;\n    this.animation = undefined;\n    this.rootParams = undefined;\n    this.root = undefined;\n  }\n  swipeGestureChanged() {\n    if (this.gesture) {\n      this.gesture.enable(this.swipeGesture === true);\n    }\n  }\n  rootChanged() {\n    if (this.root === undefined) {\n      return;\n    }\n    if (this.didLoad === false) {\n      /**\n       * If the component has not loaded yet, we can skip setting up the root component.\n       * It will be called when `componentDidLoad` fires.\n       */\n      return;\n    }\n    if (!this.useRouter) {\n      if (this.root !== undefined) {\n        this.setRoot(this.root, this.rootParams);\n      }\n    }\n  }\n  componentWillLoad() {\n    this.useRouter = document.querySelector('ion-router') !== null && this.el.closest('[no-router]') === null;\n    if (this.swipeGesture === undefined) {\n      const mode = getIonMode(this);\n      this.swipeGesture = config.getBoolean('swipeBackEnabled', mode === 'ios');\n    }\n    this.ionNavWillLoad.emit();\n  }\n  componentDidLoad() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // We want to set this flag before any watch callbacks are manually called\n      _this2.didLoad = true;\n      _this2.rootChanged();\n      _this2.gesture = (yield import('./swipe-back-37a22d34.js')).createSwipeBackGesture(_this2.el, _this2.canStart.bind(_this2), _this2.onStart.bind(_this2), _this2.onMove.bind(_this2), _this2.onEnd.bind(_this2));\n      _this2.swipeGestureChanged();\n    })();\n  }\n  connectedCallback() {\n    this.destroyed = false;\n  }\n  disconnectedCallback() {\n    for (const view of this.views) {\n      lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n      view._destroy();\n    }\n    // Release swipe back gesture and transition.\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.transInstr.length = 0;\n    this.views.length = 0;\n    this.destroyed = true;\n  }\n  /**\n   * Push a new component onto the current navigation stack. Pass any additional\n   * information along as an object. This additional information is accessible\n   * through NavParams.\n   *\n   * @param component The component to push onto the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  push(component, componentProps, opts, done) {\n    return this.insert(-1, component, componentProps, opts, done);\n  }\n  /**\n   * Inserts a component into the navigation stack at the specified index.\n   * This is useful to add a component at any point in the navigation stack.\n   *\n   * @param insertIndex The index to insert the component at in the stack.\n   * @param component The component to insert into the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  insert(insertIndex, component, componentProps, opts, done) {\n    return this.insertPages(insertIndex, [{\n      component,\n      componentProps\n    }], opts, done);\n  }\n  /**\n   * Inserts an array of components into the navigation stack at the specified index.\n   * The last component in the array will become instantiated as a view, and animate\n   * in to become the active view.\n   *\n   * @param insertIndex The index to insert the components at in the stack.\n   * @param insertComponents The components to insert into the navigation stack.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  insertPages(insertIndex, insertComponents, opts, done) {\n    return this.queueTrns({\n      insertStart: insertIndex,\n      insertViews: insertComponents,\n      opts\n    }, done);\n  }\n  /**\n   * Pop a component off of the navigation stack. Navigates back from the current\n   * component.\n   *\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  pop(opts, done) {\n    return this.removeIndex(-1, 1, opts, done);\n  }\n  /**\n   * Pop to a specific index in the navigation stack.\n   *\n   * @param indexOrViewCtrl The index or view controller to pop to.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  popTo(indexOrViewCtrl, opts, done) {\n    const ti = {\n      removeStart: -1,\n      removeCount: -1,\n      opts\n    };\n    if (typeof indexOrViewCtrl === 'object' && indexOrViewCtrl.component) {\n      ti.removeView = indexOrViewCtrl;\n      ti.removeStart = 1;\n    } else if (typeof indexOrViewCtrl === 'number') {\n      ti.removeStart = indexOrViewCtrl + 1;\n    }\n    return this.queueTrns(ti, done);\n  }\n  /**\n   * Navigate back to the root of the stack, no matter how far back that is.\n   *\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  popToRoot(opts, done) {\n    return this.removeIndex(1, -1, opts, done);\n  }\n  /**\n   * Removes a component from the navigation stack at the specified index.\n   *\n   * @param startIndex The number to begin removal at.\n   * @param removeCount The number of components to remove.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  removeIndex(startIndex, removeCount = 1, opts, done) {\n    return this.queueTrns({\n      removeStart: startIndex,\n      removeCount,\n      opts\n    }, done);\n  }\n  /**\n   * Set the root for the current navigation stack to a component.\n   *\n   * @param component The component to set as the root of the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  setRoot(component, componentProps, opts, done) {\n    return this.setPages([{\n      component,\n      componentProps\n    }], opts, done);\n  }\n  /**\n   * Set the views of the current navigation stack and navigate to the last view.\n   * By default animations are disabled, but they can be enabled by passing options\n   * to the navigation controller. Navigation parameters can also be passed to the\n   * individual pages in the array.\n   *\n   * @param views The list of views to set as the navigation stack.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  setPages(views, opts, done) {\n    opts !== null && opts !== void 0 ? opts : opts = {};\n    // if animation wasn't set to true then default it to NOT animate\n    if (opts.animated !== true) {\n      opts.animated = false;\n    }\n    return this.queueTrns({\n      insertStart: 0,\n      insertViews: views,\n      removeStart: 0,\n      removeCount: -1,\n      opts\n    }, done);\n  }\n  /**\n   * Called by the router to update the view.\n   *\n   * @param id The component tag.\n   * @param params The component params.\n   * @param direction A direction hint.\n   * @param animation an AnimationBuilder.\n   *\n   * @return the status.\n   * @internal\n   */\n  setRouteId(id, params, direction, animation) {\n    const active = this.getActiveSync();\n    if (matches(active, id, params)) {\n      return Promise.resolve({\n        changed: false,\n        element: active.element\n      });\n    }\n    let resolve;\n    const promise = new Promise(r => resolve = r);\n    let finish;\n    const commonOpts = {\n      updateURL: false,\n      viewIsReady: enteringEl => {\n        let mark;\n        const p = new Promise(r => mark = r);\n        resolve({\n          changed: true,\n          element: enteringEl,\n          markVisible: function () {\n            var _ref = _asyncToGenerator(function* () {\n              mark();\n              yield finish;\n            });\n            return function markVisible() {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        });\n        return p;\n      }\n    };\n    if (direction === 'root') {\n      finish = this.setRoot(id, params, commonOpts);\n    } else {\n      // Look for a view matching the target in the view stack.\n      const viewController = this.views.find(v => matches(v, id, params));\n      if (viewController) {\n        finish = this.popTo(viewController, Object.assign(Object.assign({}, commonOpts), {\n          direction: 'back',\n          animationBuilder: animation\n        }));\n      } else if (direction === 'forward') {\n        finish = this.push(id, params, Object.assign(Object.assign({}, commonOpts), {\n          animationBuilder: animation\n        }));\n      } else if (direction === 'back') {\n        finish = this.setRoot(id, params, Object.assign(Object.assign({}, commonOpts), {\n          direction: 'back',\n          animated: true,\n          animationBuilder: animation\n        }));\n      }\n    }\n    return promise;\n  }\n  /**\n   * Called by <ion-router> to retrieve the current component.\n   *\n   * @internal\n   */\n  getRouteId() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const active = _this3.getActiveSync();\n      if (active) {\n        return {\n          id: active.element.tagName,\n          params: active.params,\n          element: active.element\n        };\n      }\n      return undefined;\n    })();\n  }\n  /**\n   * Get the active view.\n   */\n  getActive() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      return _this4.getActiveSync();\n    })();\n  }\n  /**\n   * Get the view at the specified index.\n   *\n   * @param index The index of the view.\n   */\n  getByIndex(index) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      return _this5.views[index];\n    })();\n  }\n  /**\n   * Returns `true` if the current view can go back.\n   *\n   * @param view The view to check.\n   */\n  canGoBack(view) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      return _this6.canGoBackSync(view);\n    })();\n  }\n  /**\n   * Get the previous view.\n   *\n   * @param view The view to get.\n   */\n  getPrevious(view) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      return _this7.getPreviousSync(view);\n    })();\n  }\n  getLength() {\n    return this.views.length;\n  }\n  getActiveSync() {\n    return this.views[this.views.length - 1];\n  }\n  canGoBackSync(view = this.getActiveSync()) {\n    return !!(view && this.getPreviousSync(view));\n  }\n  getPreviousSync(view = this.getActiveSync()) {\n    if (!view) {\n      return undefined;\n    }\n    const views = this.views;\n    const index = views.indexOf(view);\n    return index > 0 ? views[index - 1] : undefined;\n  }\n  /**\n   * Adds a navigation stack change to the queue and schedules it to run.\n   *\n   * @returns Whether the transition succeeds.\n   */\n  queueTrns(ti, done) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b;\n      if (_this8.isTransitioning && ((_a = ti.opts) === null || _a === void 0 ? void 0 : _a.skipIfBusy)) {\n        return false;\n      }\n      const promise = new Promise((resolve, reject) => {\n        ti.resolve = resolve;\n        ti.reject = reject;\n      });\n      ti.done = done;\n      /**\n       * If using router, check to see if navigation hooks\n       * will allow us to perform this transition. This\n       * is required in order for hooks to work with\n       * the ion-back-button or swipe to go back.\n       */\n      if (ti.opts && ti.opts.updateURL !== false && _this8.useRouter) {\n        const router = document.querySelector('ion-router');\n        if (router) {\n          const canTransition = yield router.canTransition();\n          if (canTransition === false) {\n            return false;\n          }\n          if (typeof canTransition === 'string') {\n            router.push(canTransition, ti.opts.direction || 'back');\n            return false;\n          }\n        }\n      }\n      // Normalize empty\n      if (((_b = ti.insertViews) === null || _b === void 0 ? void 0 : _b.length) === 0) {\n        ti.insertViews = undefined;\n      }\n      // Enqueue transition instruction\n      _this8.transInstr.push(ti);\n      // if there isn't a transition already happening\n      // then this will kick off this transition\n      _this8.nextTrns();\n      return promise;\n    })();\n  }\n  success(result, ti) {\n    if (this.destroyed) {\n      this.fireError('nav controller was destroyed', ti);\n      return;\n    }\n    if (ti.done) {\n      ti.done(result.hasCompleted, result.requiresTransition, result.enteringView, result.leavingView, result.direction);\n    }\n    ti.resolve(result.hasCompleted);\n    if (ti.opts.updateURL !== false && this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        const direction = result.direction === 'back' ? 'back' : 'forward';\n        router.navChanged(direction);\n      }\n    }\n  }\n  failed(rejectReason, ti) {\n    if (this.destroyed) {\n      this.fireError('nav controller was destroyed', ti);\n      return;\n    }\n    this.transInstr.length = 0;\n    this.fireError(rejectReason, ti);\n  }\n  fireError(rejectReason, ti) {\n    if (ti.done) {\n      ti.done(false, false, rejectReason);\n    }\n    if (ti.reject && !this.destroyed) {\n      ti.reject(rejectReason);\n    } else {\n      ti.resolve(false);\n    }\n  }\n  /**\n   * Consumes the next transition in the queue.\n   *\n   * @returns whether the transition is executed.\n   */\n  nextTrns() {\n    // this is the framework's bread 'n butta function\n    // only one transition is allowed at any given time\n    if (this.isTransitioning) {\n      return false;\n    }\n    // there is no transition happening right now, executes the next instructions.\n    const ti = this.transInstr.shift();\n    if (!ti) {\n      return false;\n    }\n    this.runTransition(ti);\n    return true;\n  }\n  /** Executes all the transition instruction from the queue. */\n  runTransition(ti) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // set that this nav is actively transitioning\n        _this9.ionNavWillChange.emit();\n        _this9.isTransitioning = true;\n        _this9.prepareTI(ti);\n        const leavingView = _this9.getActiveSync();\n        const enteringView = _this9.getEnteringView(ti, leavingView);\n        if (!leavingView && !enteringView) {\n          throw new Error('no views in the stack to be removed');\n        }\n        if (enteringView && enteringView.state === VIEW_STATE_NEW) {\n          yield enteringView.init(_this9.el);\n        }\n        _this9.postViewInit(enteringView, leavingView, ti);\n        // Needs transition?\n        const requiresTransition = (ti.enteringRequiresTransition || ti.leavingRequiresTransition) && enteringView !== leavingView;\n        if (requiresTransition && ti.opts && leavingView) {\n          const isBackDirection = ti.opts.direction === 'back';\n          /**\n           * If heading back, use the entering page's animation\n           * unless otherwise specified by the developer.\n           */\n          if (isBackDirection) {\n            ti.opts.animationBuilder = ti.opts.animationBuilder || (enteringView === null || enteringView === void 0 ? void 0 : enteringView.animationBuilder);\n          }\n          leavingView.animationBuilder = ti.opts.animationBuilder;\n        }\n        let result;\n        if (requiresTransition) {\n          result = yield _this9.transition(enteringView, leavingView, ti);\n        } else {\n          // transition is not required, so we are already done!\n          // they're inserting/removing the views somewhere in the middle or\n          // beginning, so visually nothing needs to animate/transition\n          // resolve immediately because there's no animation that's happening\n          result = {\n            hasCompleted: true,\n            requiresTransition: false\n          };\n        }\n        _this9.success(result, ti);\n        _this9.ionNavDidChange.emit();\n      } catch (rejectReason) {\n        _this9.failed(rejectReason, ti);\n      }\n      _this9.isTransitioning = false;\n      _this9.nextTrns();\n    })();\n  }\n  prepareTI(ti) {\n    var _a, _b;\n    var _c;\n    const viewsLength = this.views.length;\n    (_a = ti.opts) !== null && _a !== void 0 ? _a : ti.opts = {};\n    (_b = (_c = ti.opts).delegate) !== null && _b !== void 0 ? _b : _c.delegate = this.delegate;\n    if (ti.removeView !== undefined) {\n      assert(ti.removeStart !== undefined, 'removeView needs removeStart');\n      assert(ti.removeCount !== undefined, 'removeView needs removeCount');\n      const index = this.views.indexOf(ti.removeView);\n      if (index < 0) {\n        throw new Error('removeView was not found');\n      }\n      ti.removeStart += index;\n    }\n    if (ti.removeStart !== undefined) {\n      if (ti.removeStart < 0) {\n        ti.removeStart = viewsLength - 1;\n      }\n      if (ti.removeCount < 0) {\n        ti.removeCount = viewsLength - ti.removeStart;\n      }\n      ti.leavingRequiresTransition = ti.removeCount > 0 && ti.removeStart + ti.removeCount === viewsLength;\n    }\n    if (ti.insertViews) {\n      // allow -1 to be passed in to auto push it on the end\n      // and clean up the index if it's larger then the size of the stack\n      if (ti.insertStart < 0 || ti.insertStart > viewsLength) {\n        ti.insertStart = viewsLength;\n      }\n      ti.enteringRequiresTransition = ti.insertStart === viewsLength;\n    }\n    const insertViews = ti.insertViews;\n    if (!insertViews) {\n      return;\n    }\n    assert(insertViews.length > 0, 'length can not be zero');\n    const viewControllers = convertToViews(insertViews);\n    if (viewControllers.length === 0) {\n      throw new Error('invalid views to insert');\n    }\n    // Check all the inserted view are correct\n    for (const view of viewControllers) {\n      view.delegate = ti.opts.delegate;\n      const nav = view.nav;\n      if (nav && nav !== this) {\n        throw new Error('inserted view was already inserted');\n      }\n      if (view.state === VIEW_STATE_DESTROYED) {\n        throw new Error('inserted view was already destroyed');\n      }\n    }\n    ti.insertViews = viewControllers;\n  }\n  /**\n   * Returns the view that will be entered considering the transition instructions.\n   *\n   * @param ti The instructions.\n   * @param leavingView The view being left or undefined if none.\n   *\n   * @returns The view that will be entered, undefined if none.\n   */\n  getEnteringView(ti, leavingView) {\n    // The last inserted view will be entered when view are inserted.\n    const insertViews = ti.insertViews;\n    if (insertViews !== undefined) {\n      return insertViews[insertViews.length - 1];\n    }\n    // When views are deleted, we will enter the last view that is not removed and not the view being left.\n    const removeStart = ti.removeStart;\n    if (removeStart !== undefined) {\n      const views = this.views;\n      const removeEnd = removeStart + ti.removeCount;\n      for (let i = views.length - 1; i >= 0; i--) {\n        const view = views[i];\n        if ((i < removeStart || i >= removeEnd) && view !== leavingView) {\n          return view;\n        }\n      }\n    }\n    return undefined;\n  }\n  /**\n   * Adds and Removes the views from the navigation stack.\n   *\n   * @param enteringView The view being entered.\n   * @param leavingView The view being left.\n   * @param ti The instructions.\n   */\n  postViewInit(enteringView, leavingView, ti) {\n    var _a, _b, _c;\n    assert(leavingView || enteringView, 'Both leavingView and enteringView are null');\n    assert(ti.resolve, 'resolve must be valid');\n    assert(ti.reject, 'reject must be valid');\n    // Compute the views to remove.\n    const opts = ti.opts;\n    const {\n      insertViews,\n      removeStart,\n      removeCount\n    } = ti;\n    /** Records the view to destroy */\n    let destroyQueue;\n    // there are views to remove\n    if (removeStart !== undefined && removeCount !== undefined) {\n      assert(removeStart >= 0, 'removeStart can not be negative');\n      assert(removeCount >= 0, 'removeCount can not be negative');\n      destroyQueue = [];\n      for (let i = removeStart; i < removeStart + removeCount; i++) {\n        const view = this.views[i];\n        if (view !== undefined && view !== enteringView && view !== leavingView) {\n          destroyQueue.push(view);\n        }\n      }\n      // default the direction to \"back\"\n      (_a = opts.direction) !== null && _a !== void 0 ? _a : opts.direction = 'back';\n    }\n    const finalNumViews = this.views.length + ((_b = insertViews === null || insertViews === void 0 ? void 0 : insertViews.length) !== null && _b !== void 0 ? _b : 0) - (removeCount !== null && removeCount !== void 0 ? removeCount : 0);\n    assert(finalNumViews >= 0, 'final balance can not be negative');\n    if (finalNumViews === 0) {\n      console.warn(`You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.`, this, this.el);\n      throw new Error('navigation stack needs at least one root page');\n    }\n    // At this point the transition can not be rejected, any throw should be an error\n    // Insert the new views in the stack.\n    if (insertViews) {\n      // add the views to the\n      let insertIndex = ti.insertStart;\n      for (const view of insertViews) {\n        this.insertViewAt(view, insertIndex);\n        insertIndex++;\n      }\n      if (ti.enteringRequiresTransition) {\n        // default to forward if not already set\n        (_c = opts.direction) !== null && _c !== void 0 ? _c : opts.direction = 'forward';\n      }\n    }\n    // if the views to be removed are in the beginning or middle\n    // and there is not a view that needs to visually transition out\n    // then just destroy them and don't transition anything\n    // batch all of lifecycles together\n    // let's make sure, callbacks are zoned\n    if (destroyQueue && destroyQueue.length > 0) {\n      for (const view of destroyQueue) {\n        lifecycle(view.element, LIFECYCLE_WILL_LEAVE);\n        lifecycle(view.element, LIFECYCLE_DID_LEAVE);\n        lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n      }\n      // once all lifecycle events has been delivered, we can safely detroy the views\n      for (const view of destroyQueue) {\n        this.destroyView(view);\n      }\n    }\n  }\n  transition(enteringView, leavingView, ti) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      // we should animate (duration > 0) if the pushed page is not the first one (startup)\n      // or if it is a portal (modal, actionsheet, etc.)\n      const opts = ti.opts;\n      const progressCallback = opts.progressAnimation ? ani => {\n        /**\n         * Because this progress callback is called asynchronously\n         * it is possible for the gesture to start and end before\n         * the animation is ever set. In that scenario, we should\n         * immediately call progressEnd so that the transition promise\n         * resolves and the gesture does not get locked up.\n         */\n        if (ani !== undefined && !_this0.gestureOrAnimationInProgress) {\n          _this0.gestureOrAnimationInProgress = true;\n          ani.onFinish(() => {\n            _this0.gestureOrAnimationInProgress = false;\n          }, {\n            oneTimeCallback: true\n          });\n          /**\n           * Playing animation to beginning\n           * with a duration of 0 prevents\n           * any flickering when the animation\n           * is later cleaned up.\n           */\n          ani.progressEnd(0, 0, 0);\n        } else {\n          _this0.sbAni = ani;\n        }\n      } : undefined;\n      const mode = getIonMode(_this0);\n      const enteringEl = enteringView.element;\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      const leavingEl = leavingView && leavingView.element;\n      const animationOpts = Object.assign(Object.assign({\n        mode,\n        showGoBack: _this0.canGoBackSync(enteringView),\n        baseEl: _this0.el,\n        progressCallback,\n        animated: _this0.animated && config.getBoolean('animated', true),\n        enteringEl,\n        leavingEl\n      }, opts), {\n        animationBuilder: opts.animationBuilder || _this0.animation || config.get('navAnimation')\n      });\n      const {\n        hasCompleted\n      } = yield transition(animationOpts);\n      return _this0.transitionFinish(hasCompleted, enteringView, leavingView, opts);\n    })();\n  }\n  transitionFinish(hasCompleted, enteringView, leavingView, opts) {\n    /**\n     * If the transition did not complete, the leavingView will still be the active\n     * view on the stack. Otherwise unmount all the views after the enteringView.\n     */\n    const activeView = hasCompleted ? enteringView : leavingView;\n    if (activeView) {\n      this.unmountInactiveViews(activeView);\n    }\n    return {\n      hasCompleted,\n      requiresTransition: true,\n      enteringView,\n      leavingView,\n      direction: opts.direction\n    };\n  }\n  /**\n   * Inserts a view at the specified index.\n   *\n   * When the view already is in the stack it will be moved to the new position.\n   *\n   * @param view The view to insert.\n   * @param index The index where to insert the view.\n   */\n  insertViewAt(view, index) {\n    const views = this.views;\n    const existingIndex = views.indexOf(view);\n    if (existingIndex > -1) {\n      assert(view.nav === this, 'view is not part of the nav');\n      // The view already in the stack, removes it.\n      views.splice(existingIndex, 1);\n      // and add it back at the requested index.\n      views.splice(index, 0, view);\n    } else {\n      assert(!view.nav, 'nav is used');\n      // this is a new view to add to the stack\n      // create the new entering view\n      view.nav = this;\n      views.splice(index, 0, view);\n    }\n  }\n  /**\n   * Removes a view from the stack.\n   *\n   * @param view The view to remove.\n   */\n  removeView(view) {\n    assert(view.state === VIEW_STATE_ATTACHED || view.state === VIEW_STATE_DESTROYED, 'view state should be loaded or destroyed');\n    const views = this.views;\n    const index = views.indexOf(view);\n    assert(index > -1, 'view must be part of the stack');\n    if (index >= 0) {\n      views.splice(index, 1);\n    }\n  }\n  destroyView(view) {\n    view._destroy();\n    this.removeView(view);\n  }\n  /**\n   * Unmounts all inactive views after the specified active view.\n   *\n   * DOM WRITE\n   *\n   * @param activeView The view that is actively visible in the stack. Used to calculate which views to unmount.\n   */\n  unmountInactiveViews(activeView) {\n    // ok, cleanup time!! Destroy all of the views that are\n    // INACTIVE and come after the active view\n    // only do this if the views exist, though\n    if (this.destroyed) {\n      return;\n    }\n    const views = this.views;\n    const activeViewIndex = views.indexOf(activeView);\n    for (let i = views.length - 1; i >= 0; i--) {\n      const view = views[i];\n      /**\n       * When inserting multiple views via insertPages\n       * the last page will be transitioned to, but the\n       * others will not be. As a result, a DOM element\n       * will only be created for the last page inserted.\n       * As a result, it is possible to have views in the\n       * stack that do not have `view.element` yet.\n       */\n      const element = view.element;\n      if (element) {\n        if (i > activeViewIndex) {\n          // this view comes after the active view\n          // let's unload it\n          lifecycle(element, LIFECYCLE_WILL_UNLOAD);\n          this.destroyView(view);\n        } else if (i < activeViewIndex) {\n          // this view comes before the active view\n          // and it is not a portal then ensure it is hidden\n          setPageHidden(element, true);\n        }\n      }\n    }\n  }\n  canStart() {\n    return !this.gestureOrAnimationInProgress && !!this.swipeGesture && !this.isTransitioning && this.transInstr.length === 0 && this.canGoBackSync();\n  }\n  onStart() {\n    this.gestureOrAnimationInProgress = true;\n    this.pop({\n      direction: 'back',\n      progressAnimation: true\n    });\n  }\n  onMove(stepValue) {\n    if (this.sbAni) {\n      this.sbAni.progressStep(stepValue);\n    }\n  }\n  onEnd(shouldComplete, stepValue, dur) {\n    if (this.sbAni) {\n      this.sbAni.onFinish(() => {\n        this.gestureOrAnimationInProgress = false;\n      }, {\n        oneTimeCallback: true\n      });\n      // Account for rounding errors in JS\n      let newStepValue = shouldComplete ? -0.001 : 0.001;\n      /**\n       * Animation will be reversed here, so need to\n       * reverse the easing curve as well\n       *\n       * Additionally, we need to account for the time relative\n       * to the new easing curve, as `stepValue` is going to be given\n       * in terms of a linear curve.\n       */\n      if (!shouldComplete) {\n        this.sbAni.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n        newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], stepValue)[0];\n      } else {\n        newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], stepValue)[0];\n      }\n      this.sbAni.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n    } else {\n      this.gestureOrAnimationInProgress = false;\n    }\n  }\n  render() {\n    return h(\"slot\", {\n      key: '6894eccc60e446294b01261477691ea1e88348ab'\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"swipeGesture\": [\"swipeGestureChanged\"],\n      \"root\": [\"rootChanged\"]\n    };\n  }\n};\nNav.style = IonNavStyle0;\nconst navLink = (el, routerDirection, component, componentProps, routerAnimation) => {\n  const nav = el.closest('ion-nav');\n  if (nav) {\n    if (routerDirection === 'forward') {\n      if (component !== undefined) {\n        return nav.push(component, componentProps, {\n          skipIfBusy: true,\n          animationBuilder: routerAnimation\n        });\n      }\n    } else if (routerDirection === 'root') {\n      if (component !== undefined) {\n        return nav.setRoot(component, componentProps, {\n          skipIfBusy: true,\n          animationBuilder: routerAnimation\n        });\n      }\n    } else if (routerDirection === 'back') {\n      return nav.pop({\n        skipIfBusy: true,\n        animationBuilder: routerAnimation\n      });\n    }\n  }\n  return Promise.resolve(false);\n};\nconst NavLink = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = () => {\n      return navLink(this.el, this.routerDirection, this.component, this.componentProps, this.routerAnimation);\n    };\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n  }\n  render() {\n    return h(Host, {\n      key: 'dab6e8a908395d99c87452c5e5aa4e61d9e72435',\n      onClick: this.onClick\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nexport { Nav as ion_nav, NavLink as ion_nav_link };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}