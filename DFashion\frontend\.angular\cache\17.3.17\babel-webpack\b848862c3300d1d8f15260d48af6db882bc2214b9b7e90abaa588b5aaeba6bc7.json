{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Directive, Input } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/role-access.service\";\nimport * as i2 from \"../../core/services/auth.service\";\nexport class RoleAccessDirective {\n  set appRoleAccess(permission) {\n    this.checkPermission(permission);\n  }\n  constructor(templateRef, viewContainer, roleAccessService, authService) {\n    this.templateRef = templateRef;\n    this.viewContainer = viewContainer;\n    this.roleAccessService = roleAccessService;\n    this.authService = authService;\n    this.subscription = new Subscription();\n    this.hasView = false;\n  }\n  ngOnInit() {\n    // Subscribe to auth changes to update permissions dynamically\n    this.subscription.add(this.authService.currentUser$.subscribe(() => {\n      // Re-check permission when user changes\n      const permission = this.templateRef.elementRef?.nativeElement?.getAttribute('appRoleAccess');\n      if (permission) {\n        this.checkPermission(permission);\n      }\n    }));\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  checkPermission(permission) {\n    const hasPermission = this.evaluatePermission(permission);\n    if (hasPermission && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!hasPermission && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n      // Show else template if provided\n      if (this.appRoleAccessElse) {\n        this.viewContainer.createEmbeddedView(this.appRoleAccessElse);\n      }\n    }\n  }\n  evaluatePermission(permission) {\n    if (!permission) return true;\n    // Handle different permission formats\n    if (permission.includes(':')) {\n      // Format: \"module:action\" (e.g., \"products:write\")\n      const [module, action] = permission.split(':');\n      return this.roleAccessService.hasPermission(module, action);\n    } else if (permission.startsWith('role:')) {\n      // Format: \"role:roleName\" (e.g., \"role:admin\")\n      const roleName = permission.replace('role:', '');\n      return this.roleAccessService.getCurrentUserRole() === roleName;\n    } else if (permission.startsWith('level:')) {\n      // Format: \"level:number\" (e.g., \"level:3\")\n      const level = parseInt(permission.replace('level:', ''));\n      return this.roleAccessService.hasRoleLevel(level);\n    } else {\n      // Assume it's a module name for read access\n      return this.roleAccessService.canRead(permission);\n    }\n  }\n  static {\n    this.ɵfac = function RoleAccessDirective_Factory(t) {\n      return new (t || RoleAccessDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.RoleAccessService), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: RoleAccessDirective,\n      selectors: [[\"\", \"appRoleAccess\", \"\"]],\n      inputs: {\n        appRoleAccess: \"appRoleAccess\",\n        appRoleAccessElse: \"appRoleAccessElse\"\n      },\n      standalone: true\n    });\n  }\n}\n// Additional directive for role-based styling\nlet RoleClassDirective = class RoleClassDirective {\n  set appRoleClass(config) {\n    this.applyRoleClass(config);\n  }\n  constructor(roleAccessService, authService, elementRef) {\n    this.roleAccessService = roleAccessService;\n    this.authService = authService;\n    this.elementRef = elementRef;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.subscription.add(this.authService.currentUser$.subscribe(() => {\n      // Re-apply classes when user changes\n      const config = this.elementRef.nativeElement?.getAttribute('appRoleClass');\n      if (config) {\n        this.applyRoleClass(JSON.parse(config));\n      }\n    }));\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  applyRoleClass(config) {\n    const currentRole = this.roleAccessService.getCurrentUserRole();\n    const element = this.elementRef.nativeElement;\n    // Remove all role classes first\n    Object.values(config).forEach(className => {\n      element.classList.remove(className);\n    });\n    // Add class for current role\n    if (config[currentRole]) {\n      element.classList.add(config[currentRole]);\n    }\n  }\n};\n__decorate([Input()], RoleClassDirective.prototype, \"appRoleClass\", null);\nRoleClassDirective = __decorate([Directive({\n  selector: '[appRoleClass]',\n  standalone: true\n})], RoleClassDirective);\nexport { RoleClassDirective };\n// Directive for disabling elements based on permissions\nlet RoleDisableDirective = class RoleDisableDirective {\n  set appRoleDisable(permission) {\n    this.checkAndDisable(permission);\n  }\n  constructor(roleAccessService, authService, elementRef) {\n    this.roleAccessService = roleAccessService;\n    this.authService = authService;\n    this.elementRef = elementRef;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.subscription.add(this.authService.currentUser$.subscribe(() => {\n      const permission = this.elementRef.nativeElement?.getAttribute('appRoleDisable');\n      if (permission) {\n        this.checkAndDisable(permission);\n      }\n    }));\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  checkAndDisable(permission) {\n    const hasPermission = this.evaluatePermission(permission);\n    const element = this.elementRef.nativeElement;\n    if (!hasPermission) {\n      element.disabled = true;\n      element.classList.add('role-disabled');\n      element.title = 'You do not have permission to perform this action';\n    } else {\n      element.disabled = false;\n      element.classList.remove('role-disabled');\n      element.title = '';\n    }\n  }\n  evaluatePermission(permission) {\n    if (!permission) return true;\n    if (permission.includes(':')) {\n      const [module, action] = permission.split(':');\n      return this.roleAccessService.hasPermission(module, action);\n    } else if (permission.startsWith('role:')) {\n      const roleName = permission.replace('role:', '');\n      return this.roleAccessService.getCurrentUserRole() === roleName;\n    } else if (permission.startsWith('level:')) {\n      const level = parseInt(permission.replace('level:', ''));\n      return this.roleAccessService.hasRoleLevel(level);\n    } else {\n      return this.roleAccessService.canRead(permission);\n    }\n  }\n};\n__decorate([Input()], RoleDisableDirective.prototype, \"appRoleDisable\", null);\nRoleDisableDirective = __decorate([Directive({\n  selector: '[appRoleDisable]',\n  standalone: true\n})], RoleDisableDirective);\nexport { RoleDisableDirective };", "map": {"version": 3, "names": ["Directive", "Input", "Subscription", "RoleAccessDirective", "appRoleAccess", "permission", "checkPermission", "constructor", "templateRef", "viewContainer", "roleAccessService", "authService", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "add", "currentUser$", "subscribe", "elementRef", "nativeElement", "getAttribute", "ngOnDestroy", "unsubscribe", "hasPermission", "evaluatePermission", "createEmbeddedView", "clear", "appRoleAccessElse", "includes", "module", "action", "split", "startsWith", "<PERSON><PERSON><PERSON>", "replace", "getCurrentUserRole", "level", "parseInt", "hasRoleLevel", "canRead", "i0", "ɵɵdirectiveInject", "TemplateRef", "ViewContainerRef", "i1", "RoleAccessService", "i2", "AuthService", "selectors", "inputs", "standalone", "RoleClassDirective", "appRoleClass", "config", "applyRoleClass", "JSON", "parse", "currentRole", "element", "Object", "values", "for<PERSON>ach", "className", "classList", "remove", "__decorate", "selector", "RoleDisableDirective", "appRoleDisable", "checkAndDisable", "disabled", "title"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\directives\\role-access.directive.ts"], "sourcesContent": ["import { Directive, Input, TemplateRef, ViewContainerRef, OnInit, OnDestroy, ElementRef } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { RoleAccessService } from '../../core/services/role-access.service';\nimport { AuthService } from '../../core/services/auth.service';\n\n@Directive({\n  selector: '[appRoleAccess]',\n  standalone: true\n})\nexport class RoleAccessDirective implements OnInit, OnDestroy {\n  private subscription: Subscription = new Subscription();\n  private hasView = false;\n\n  @Input() set appRoleAccess(permission: string) {\n    this.checkPermission(permission);\n  }\n\n  @Input() appRoleAccessElse?: TemplateRef<any>;\n\n  constructor(\n    private templateRef: TemplateRef<any>,\n    private viewContainer: ViewContainerRef,\n    private roleAccessService: RoleAccessService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to auth changes to update permissions dynamically\n    this.subscription.add(\n      this.authService.currentUser$.subscribe(() => {\n        // Re-check permission when user changes\n        const permission = this.templateRef.elementRef?.nativeElement?.getAttribute('appRoleAccess');\n        if (permission) {\n          this.checkPermission(permission);\n        }\n      })\n    );\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private checkPermission(permission: string) {\n    const hasPermission = this.evaluatePermission(permission);\n    \n    if (hasPermission && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!hasPermission && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n      \n      // Show else template if provided\n      if (this.appRoleAccessElse) {\n        this.viewContainer.createEmbeddedView(this.appRoleAccessElse);\n      }\n    }\n  }\n\n  private evaluatePermission(permission: string): boolean {\n    if (!permission) return true;\n\n    // Handle different permission formats\n    if (permission.includes(':')) {\n      // Format: \"module:action\" (e.g., \"products:write\")\n      const [module, action] = permission.split(':');\n      return this.roleAccessService.hasPermission(module, action as any);\n    } else if (permission.startsWith('role:')) {\n      // Format: \"role:roleName\" (e.g., \"role:admin\")\n      const roleName = permission.replace('role:', '');\n      return this.roleAccessService.getCurrentUserRole() === roleName;\n    } else if (permission.startsWith('level:')) {\n      // Format: \"level:number\" (e.g., \"level:3\")\n      const level = parseInt(permission.replace('level:', ''));\n      return this.roleAccessService.hasRoleLevel(level);\n    } else {\n      // Assume it's a module name for read access\n      return this.roleAccessService.canRead(permission);\n    }\n  }\n}\n\n// Additional directive for role-based styling\n@Directive({\n  selector: '[appRoleClass]',\n  standalone: true\n})\nexport class RoleClassDirective implements OnInit, OnDestroy {\n  private subscription: Subscription = new Subscription();\n\n  @Input() set appRoleClass(config: { [role: string]: string }) {\n    this.applyRoleClass(config);\n  }\n\n  constructor(\n    private roleAccessService: RoleAccessService,\n    private authService: AuthService,\n    private elementRef: any\n  ) {}\n\n  ngOnInit() {\n    this.subscription.add(\n      this.authService.currentUser$.subscribe(() => {\n        // Re-apply classes when user changes\n        const config = this.elementRef.nativeElement?.getAttribute('appRoleClass');\n        if (config) {\n          this.applyRoleClass(JSON.parse(config));\n        }\n      })\n    );\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private applyRoleClass(config: { [role: string]: string }) {\n    const currentRole = this.roleAccessService.getCurrentUserRole();\n    const element = this.elementRef.nativeElement;\n\n    // Remove all role classes first\n    Object.values(config).forEach(className => {\n      element.classList.remove(className);\n    });\n\n    // Add class for current role\n    if (config[currentRole]) {\n      element.classList.add(config[currentRole]);\n    }\n  }\n}\n\n// Directive for disabling elements based on permissions\n@Directive({\n  selector: '[appRoleDisable]',\n  standalone: true\n})\nexport class RoleDisableDirective implements OnInit, OnDestroy {\n  private subscription: Subscription = new Subscription();\n\n  @Input() set appRoleDisable(permission: string) {\n    this.checkAndDisable(permission);\n  }\n\n  constructor(\n    private roleAccessService: RoleAccessService,\n    private authService: AuthService,\n    private elementRef: any\n  ) {}\n\n  ngOnInit() {\n    this.subscription.add(\n      this.authService.currentUser$.subscribe(() => {\n        const permission = this.elementRef.nativeElement?.getAttribute('appRoleDisable');\n        if (permission) {\n          this.checkAndDisable(permission);\n        }\n      })\n    );\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private checkAndDisable(permission: string) {\n    const hasPermission = this.evaluatePermission(permission);\n    const element = this.elementRef.nativeElement;\n\n    if (!hasPermission) {\n      element.disabled = true;\n      element.classList.add('role-disabled');\n      element.title = 'You do not have permission to perform this action';\n    } else {\n      element.disabled = false;\n      element.classList.remove('role-disabled');\n      element.title = '';\n    }\n  }\n\n  private evaluatePermission(permission: string): boolean {\n    if (!permission) return true;\n\n    if (permission.includes(':')) {\n      const [module, action] = permission.split(':');\n      return this.roleAccessService.hasPermission(module, action as any);\n    } else if (permission.startsWith('role:')) {\n      const roleName = permission.replace('role:', '');\n      return this.roleAccessService.getCurrentUserRole() === roleName;\n    } else if (permission.startsWith('level:')) {\n      const level = parseInt(permission.replace('level:', ''));\n      return this.roleAccessService.hasRoleLevel(level);\n    } else {\n      return this.roleAccessService.canRead(permission);\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,QAAsE,eAAe;AAC9G,SAASC,YAAY,QAAQ,MAAM;;;;AAQnC,OAAM,MAAOC,mBAAmB;EAI9B,IAAaC,aAAaA,CAACC,UAAkB;IAC3C,IAAI,CAACC,eAAe,CAACD,UAAU,CAAC;EAClC;EAIAE,YACUC,WAA6B,EAC7BC,aAA+B,EAC/BC,iBAAoC,EACpCC,WAAwB;IAHxB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IAbb,KAAAC,YAAY,GAAiB,IAAIV,YAAY,EAAE;IAC/C,KAAAW,OAAO,GAAG,KAAK;EAapB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,YAAY,CAACG,GAAG,CACnB,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3C;MACA,MAAMZ,UAAU,GAAG,IAAI,CAACG,WAAW,CAACU,UAAU,EAAEC,aAAa,EAAEC,YAAY,CAAC,eAAe,CAAC;MAC5F,IAAIf,UAAU,EAAE;QACd,IAAI,CAACC,eAAe,CAACD,UAAU,CAAC;;IAEpC,CAAC,CAAC,CACH;EACH;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACU,WAAW,EAAE;EACjC;EAEQhB,eAAeA,CAACD,UAAkB;IACxC,MAAMkB,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACnB,UAAU,CAAC;IAEzD,IAAIkB,aAAa,IAAI,CAAC,IAAI,CAACV,OAAO,EAAE;MAClC,IAAI,CAACJ,aAAa,CAACgB,kBAAkB,CAAC,IAAI,CAACjB,WAAW,CAAC;MACvD,IAAI,CAACK,OAAO,GAAG,IAAI;KACpB,MAAM,IAAI,CAACU,aAAa,IAAI,IAAI,CAACV,OAAO,EAAE;MACzC,IAAI,CAACJ,aAAa,CAACiB,KAAK,EAAE;MAC1B,IAAI,CAACb,OAAO,GAAG,KAAK;MAEpB;MACA,IAAI,IAAI,CAACc,iBAAiB,EAAE;QAC1B,IAAI,CAAClB,aAAa,CAACgB,kBAAkB,CAAC,IAAI,CAACE,iBAAiB,CAAC;;;EAGnE;EAEQH,kBAAkBA,CAACnB,UAAkB;IAC3C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAE5B;IACA,IAAIA,UAAU,CAACuB,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5B;MACA,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGzB,UAAU,CAAC0B,KAAK,CAAC,GAAG,CAAC;MAC9C,OAAO,IAAI,CAACrB,iBAAiB,CAACa,aAAa,CAACM,MAAM,EAAEC,MAAa,CAAC;KACnE,MAAM,IAAIzB,UAAU,CAAC2B,UAAU,CAAC,OAAO,CAAC,EAAE;MACzC;MACA,MAAMC,QAAQ,GAAG5B,UAAU,CAAC6B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAChD,OAAO,IAAI,CAACxB,iBAAiB,CAACyB,kBAAkB,EAAE,KAAKF,QAAQ;KAChE,MAAM,IAAI5B,UAAU,CAAC2B,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1C;MACA,MAAMI,KAAK,GAAGC,QAAQ,CAAChC,UAAU,CAAC6B,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MACxD,OAAO,IAAI,CAACxB,iBAAiB,CAAC4B,YAAY,CAACF,KAAK,CAAC;KAClD,MAAM;MACL;MACA,OAAO,IAAI,CAAC1B,iBAAiB,CAAC6B,OAAO,CAAClC,UAAU,CAAC;;EAErD;;;uBAvEWF,mBAAmB,EAAAqC,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,WAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnB5C,mBAAmB;MAAA6C,SAAA;MAAAC,MAAA;QAAA7C,aAAA;QAAAuB,iBAAA;MAAA;MAAAuB,UAAA;IAAA;EAAA;;AA0EhC;AAKO,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAGpB,IAAIC,YAAYA,CAACC,MAAkC;IAC1D,IAAI,CAACC,cAAc,CAACD,MAAM,CAAC;EAC7B;EAEA9C,YACUG,iBAAoC,EACpCC,WAAwB,EACxBO,UAAe;IAFf,KAAAR,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAO,UAAU,GAAVA,UAAU;IATZ,KAAAN,YAAY,GAAiB,IAAIV,YAAY,EAAE;EAUpD;EAEHY,QAAQA,CAAA;IACN,IAAI,CAACF,YAAY,CAACG,GAAG,CACnB,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3C;MACA,MAAMoC,MAAM,GAAG,IAAI,CAACnC,UAAU,CAACC,aAAa,EAAEC,YAAY,CAAC,cAAc,CAAC;MAC1E,IAAIiC,MAAM,EAAE;QACV,IAAI,CAACC,cAAc,CAACC,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC,CAAC;;IAE3C,CAAC,CAAC,CACH;EACH;EAEAhC,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACU,WAAW,EAAE;EACjC;EAEQgC,cAAcA,CAACD,MAAkC;IACvD,MAAMI,WAAW,GAAG,IAAI,CAAC/C,iBAAiB,CAACyB,kBAAkB,EAAE;IAC/D,MAAMuB,OAAO,GAAG,IAAI,CAACxC,UAAU,CAACC,aAAa;IAE7C;IACAwC,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC,CAACQ,OAAO,CAACC,SAAS,IAAG;MACxCJ,OAAO,CAACK,SAAS,CAACC,MAAM,CAACF,SAAS,CAAC;IACrC,CAAC,CAAC;IAEF;IACA,IAAIT,MAAM,CAACI,WAAW,CAAC,EAAE;MACvBC,OAAO,CAACK,SAAS,CAAChD,GAAG,CAACsC,MAAM,CAACI,WAAW,CAAC,CAAC;;EAE9C;CACD;AAxCUQ,UAAA,EAARhE,KAAK,EAAE,C,qDAEP;AALUkD,kBAAkB,GAAAc,UAAA,EAJ9BjE,SAAS,CAAC;EACTkE,QAAQ,EAAE,gBAAgB;EAC1BhB,UAAU,EAAE;CACb,CAAC,C,EACWC,kBAAkB,CA2C9B;;AAED;AAKO,IAAMgB,oBAAoB,GAA1B,MAAMA,oBAAoB;EAGtB,IAAIC,cAAcA,CAAC/D,UAAkB;IAC5C,IAAI,CAACgE,eAAe,CAAChE,UAAU,CAAC;EAClC;EAEAE,YACUG,iBAAoC,EACpCC,WAAwB,EACxBO,UAAe;IAFf,KAAAR,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAO,UAAU,GAAVA,UAAU;IATZ,KAAAN,YAAY,GAAiB,IAAIV,YAAY,EAAE;EAUpD;EAEHY,QAAQA,CAAA;IACN,IAAI,CAACF,YAAY,CAACG,GAAG,CACnB,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3C,MAAMZ,UAAU,GAAG,IAAI,CAACa,UAAU,CAACC,aAAa,EAAEC,YAAY,CAAC,gBAAgB,CAAC;MAChF,IAAIf,UAAU,EAAE;QACd,IAAI,CAACgE,eAAe,CAAChE,UAAU,CAAC;;IAEpC,CAAC,CAAC,CACH;EACH;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACU,WAAW,EAAE;EACjC;EAEQ+C,eAAeA,CAAChE,UAAkB;IACxC,MAAMkB,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACnB,UAAU,CAAC;IACzD,MAAMqD,OAAO,GAAG,IAAI,CAACxC,UAAU,CAACC,aAAa;IAE7C,IAAI,CAACI,aAAa,EAAE;MAClBmC,OAAO,CAACY,QAAQ,GAAG,IAAI;MACvBZ,OAAO,CAACK,SAAS,CAAChD,GAAG,CAAC,eAAe,CAAC;MACtC2C,OAAO,CAACa,KAAK,GAAG,mDAAmD;KACpE,MAAM;MACLb,OAAO,CAACY,QAAQ,GAAG,KAAK;MACxBZ,OAAO,CAACK,SAAS,CAACC,MAAM,CAAC,eAAe,CAAC;MACzCN,OAAO,CAACa,KAAK,GAAG,EAAE;;EAEtB;EAEQ/C,kBAAkBA,CAACnB,UAAkB;IAC3C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAE5B,IAAIA,UAAU,CAACuB,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5B,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGzB,UAAU,CAAC0B,KAAK,CAAC,GAAG,CAAC;MAC9C,OAAO,IAAI,CAACrB,iBAAiB,CAACa,aAAa,CAACM,MAAM,EAAEC,MAAa,CAAC;KACnE,MAAM,IAAIzB,UAAU,CAAC2B,UAAU,CAAC,OAAO,CAAC,EAAE;MACzC,MAAMC,QAAQ,GAAG5B,UAAU,CAAC6B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAChD,OAAO,IAAI,CAACxB,iBAAiB,CAACyB,kBAAkB,EAAE,KAAKF,QAAQ;KAChE,MAAM,IAAI5B,UAAU,CAAC2B,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1C,MAAMI,KAAK,GAAGC,QAAQ,CAAChC,UAAU,CAAC6B,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MACxD,OAAO,IAAI,CAACxB,iBAAiB,CAAC4B,YAAY,CAACF,KAAK,CAAC;KAClD,MAAM;MACL,OAAO,IAAI,CAAC1B,iBAAiB,CAAC6B,OAAO,CAAClC,UAAU,CAAC;;EAErD;CACD;AAxDU4D,UAAA,EAARhE,KAAK,EAAE,C,yDAEP;AALUkE,oBAAoB,GAAAF,UAAA,EAJhCjE,SAAS,CAAC;EACTkE,QAAQ,EAAE,kBAAkB;EAC5BhB,UAAU,EAAE;CACb,CAAC,C,EACWiB,oBAAoB,CA2DhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}