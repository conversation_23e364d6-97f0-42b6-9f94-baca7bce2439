["389-exception", "Asterisk-exception", "Autoconf-exception-2.0", "Autoconf-exception-3.0", "Autoconf-exception-generic", "Autoconf-exception-generic-3.0", "Autoconf-exception-macro", "Bison-exception-1.24", "Bison-exception-2.2", "Bootloader-exception", "Classpath-exception-2.0", "CLISP-exception-2.0", "cryptsetup-OpenSSL-exception", "DigiRule-FOSS-exception", "eCos-exception-2.0", "Fawkes-Runtime-exception", "FLTK-exception", "fmt-exception", "Font-exception-2.0", "freertos-exception-2.0", "GCC-exception-2.0", "GCC-exception-2.0-note", "GCC-exception-3.1", "Gmsh-exception", "GNAT-exception", "GNOME-examples-exception", "GNU-compiler-exception", "gnu-javamail-exception", "GPL-3.0-interface-exception", "GPL-3.0-linking-exception", "GPL-3.0-linking-source-exception", "GPL-CC-1.0", "GStreamer-exception-2005", "GStreamer-exception-2008", "i2p-gpl-java-exception", "KiCad-libraries-exception", "LGPL-3.0-linking-exception", "libpri-OpenH323-exception", "Libtool-exception", "Linux-syscall-note", "LLGPL", "LLVM-exception", "LZMA-exception", "mif-exception", "OCaml-LGPL-linking-exception", "OCCT-exception-1.0", "OpenJDK-assembly-exception-1.0", "openvpn-openssl-exception", "PS-or-PDF-font-exception-20170817", "QPL-1.0-INRIA-2004-exception", "Qt-GPL-exception-1.0", "Qt-LGPL-exception-1.1", "Qwt-exception-1.0", "SANE-exception", "SHL-2.0", "SHL-2.1", "stunnel-exception", "SWI-exception", "Swift-exception", "Texinfo-exception", "u-boot-exception-2.0", "UBDL-exception", "Universal-FOSS-exception-1.0", "vsftpd-openssl-exception", "WxWindows-exception-3.1", "x11vnc-openssl-exception"]