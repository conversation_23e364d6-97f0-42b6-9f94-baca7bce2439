{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProductService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:5000/api';\n  }\n  getProducts(filters = {}) {\n    let params = new HttpParams();\n    Object.keys(filters).forEach(key => {\n      const value = filters[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n    return this.http.get(`${this.API_URL}/products`, {\n      params\n    });\n  }\n  getProduct(id) {\n    return this.http.get(`${this.API_URL}/products/${id}`);\n  }\n  createProduct(productData) {\n    return this.http.post(`${this.API_URL}/products`, productData);\n  }\n  updateProduct(id, productData) {\n    return this.http.put(`${this.API_URL}/products/${id}`, productData);\n  }\n  deleteProduct(id) {\n    return this.http.delete(`${this.API_URL}/products/${id}`);\n  }\n  addReview(productId, reviewData) {\n    return this.http.post(`${this.API_URL}/products/${productId}/review`, reviewData);\n  }\n  getFeaturedProducts() {\n    return this.http.get(`${this.API_URL}/products/featured`);\n  }\n  getTrendingProducts() {\n    return this.http.get(`${this.API_URL}/products/trending`);\n  }\n  getVendorProducts(vendorId, filters = {}) {\n    let params = new HttpParams();\n    Object.keys(filters).forEach(key => {\n      const value = filters[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n    return this.http.get(`${this.API_URL}/products/vendor/${vendorId}`, {\n      params\n    });\n  }\n  searchProducts(query, filters = {}) {\n    const searchFilters = {\n      ...filters,\n      search: query\n    };\n    return this.getProducts(searchFilters);\n  }\n  getCategories() {\n    return this.http.get(`${this.API_URL}/categories`);\n  }\n  getBrands() {\n    return this.http.get(`${this.API_URL}/products/brands`);\n  }\n  // Featured Brands\n  getFeaturedBrands() {\n    return this.http.get(`${this.API_URL}/brands/featured`);\n  }\n  // New Arrivals\n  getNewArrivals() {\n    return this.http.get(`${this.API_URL}/products/new-arrivals`);\n  }\n  // Product interactions\n  toggleProductLike(productId) {\n    return this.http.post(`${this.API_URL}/products/${productId}/like`, {});\n  }\n  shareProduct(productId) {\n    return this.http.post(`${this.API_URL}/products/${productId}/share`, {});\n  }\n  // Category products\n  getCategoryProducts(categorySlug, filters = {}) {\n    let params = new HttpParams();\n    Object.keys(filters).forEach(key => {\n      const value = filters[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n    return this.http.get(`${this.API_URL}/products/category/${categorySlug}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function ProductService_Factory(t) {\n      return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductService,\n      factory: ProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "ProductService", "constructor", "http", "API_URL", "getProducts", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getProduct", "id", "createProduct", "productData", "post", "updateProduct", "put", "deleteProduct", "delete", "add<PERSON>eview", "productId", "reviewData", "getFeaturedProducts", "getTrendingProducts", "getVendorProducts", "vendorId", "searchProducts", "query", "searchFilters", "search", "getCategories", "getBrands", "getFeaturedBrands", "getNewArrivals", "toggleProductLike", "shareProduct", "getCategoryProducts", "categorySlug", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\product.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\n\nimport { Product, ProductsResponse, ProductFilters } from '../models/product.model';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProductService {\n  private readonly API_URL = 'http://localhost:5000/api';\n\n  constructor(private http: HttpClient) {}\n\n  getProducts(filters: ProductFilters = {}): Observable<ProductsResponse> {\n    let params = new HttpParams();\n    \n    Object.keys(filters).forEach(key => {\n      const value = (filters as any)[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n\n    return this.http.get<ProductsResponse>(`${this.API_URL}/products`, { params });\n  }\n\n  getProduct(id: string): Observable<{ product: Product }> {\n    return this.http.get<{ product: Product }>(`${this.API_URL}/products/${id}`);\n  }\n\n  createProduct(productData: any): Observable<{ message: string; product: Product }> {\n    return this.http.post<{ message: string; product: Product }>(`${this.API_URL}/products`, productData);\n  }\n\n  updateProduct(id: string, productData: any): Observable<{ message: string; product: Product }> {\n    return this.http.put<{ message: string; product: Product }>(`${this.API_URL}/products/${id}`, productData);\n  }\n\n  deleteProduct(id: string): Observable<{ message: string }> {\n    return this.http.delete<{ message: string }>(`${this.API_URL}/products/${id}`);\n  }\n\n  addReview(productId: string, reviewData: any): Observable<{ message: string }> {\n    return this.http.post<{ message: string }>(`${this.API_URL}/products/${productId}/review`, reviewData);\n  }\n\n  getFeaturedProducts(): Observable<{ products: Product[] }> {\n    return this.http.get<{ products: Product[] }>(`${this.API_URL}/products/featured`);\n  }\n\n  getTrendingProducts(): Observable<{ success: boolean; data: Product[] }> {\n    return this.http.get<{ success: boolean; data: Product[] }>(`${this.API_URL}/products/trending`);\n  }\n\n  getVendorProducts(vendorId: string, filters: ProductFilters = {}): Observable<ProductsResponse> {\n    let params = new HttpParams();\n    \n    Object.keys(filters).forEach(key => {\n      const value = (filters as any)[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n\n    return this.http.get<ProductsResponse>(`${this.API_URL}/products/vendor/${vendorId}`, { params });\n  }\n\n  searchProducts(query: string, filters: ProductFilters = {}): Observable<ProductsResponse> {\n    const searchFilters = { ...filters, search: query };\n    return this.getProducts(searchFilters);\n  }\n\n  getCategories(): Observable<{ success: boolean; data: any[] }> {\n    return this.http.get<{ success: boolean; data: any[] }>(`${this.API_URL}/categories`);\n  }\n\n  getBrands(): Observable<{ brands: string[] }> {\n    return this.http.get<{ brands: string[] }>(`${this.API_URL}/products/brands`);\n  }\n\n  // Featured Brands\n  getFeaturedBrands(): Observable<{ success: boolean; data: any[] }> {\n    return this.http.get<{ success: boolean; data: any[] }>(`${this.API_URL}/brands/featured`);\n  }\n\n  // New Arrivals\n  getNewArrivals(): Observable<{ success: boolean; data: Product[] }> {\n    return this.http.get<{ success: boolean; data: Product[] }>(`${this.API_URL}/products/new-arrivals`);\n  }\n\n  // Product interactions\n  toggleProductLike(productId: string): Observable<{ success: boolean; message: string }> {\n    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/products/${productId}/like`, {});\n  }\n\n  shareProduct(productId: string): Observable<{ success: boolean; message: string }> {\n    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/products/${productId}/share`, {});\n  }\n\n  // Category products\n  getCategoryProducts(categorySlug: string, filters: ProductFilters = {}): Observable<ProductsResponse> {\n    let params = new HttpParams();\n\n    Object.keys(filters).forEach(key => {\n      const value = (filters as any)[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n\n    return this.http.get<ProductsResponse>(`${this.API_URL}/products/category/${categorySlug}`, { params });\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;;;AAS7D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,2BAA2B;EAEf;EAEvCC,WAAWA,CAACC,OAAA,GAA0B,EAAE;IACtC,IAAIC,MAAM,GAAG,IAAIP,UAAU,EAAE;IAE7BQ,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;MACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;MACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;IAE9C,CAAC,CAAC;IAEF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAmB,GAAG,IAAI,CAACZ,OAAO,WAAW,EAAE;MAAEG;IAAM,CAAE,CAAC;EAChF;EAEAU,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAACf,IAAI,CAACa,GAAG,CAAuB,GAAG,IAAI,CAACZ,OAAO,aAAac,EAAE,EAAE,CAAC;EAC9E;EAEAC,aAAaA,CAACC,WAAgB;IAC5B,OAAO,IAAI,CAACjB,IAAI,CAACkB,IAAI,CAAwC,GAAG,IAAI,CAACjB,OAAO,WAAW,EAAEgB,WAAW,CAAC;EACvG;EAEAE,aAAaA,CAACJ,EAAU,EAAEE,WAAgB;IACxC,OAAO,IAAI,CAACjB,IAAI,CAACoB,GAAG,CAAwC,GAAG,IAAI,CAACnB,OAAO,aAAac,EAAE,EAAE,EAAEE,WAAW,CAAC;EAC5G;EAEAI,aAAaA,CAACN,EAAU;IACtB,OAAO,IAAI,CAACf,IAAI,CAACsB,MAAM,CAAsB,GAAG,IAAI,CAACrB,OAAO,aAAac,EAAE,EAAE,CAAC;EAChF;EAEAQ,SAASA,CAACC,SAAiB,EAAEC,UAAe;IAC1C,OAAO,IAAI,CAACzB,IAAI,CAACkB,IAAI,CAAsB,GAAG,IAAI,CAACjB,OAAO,aAAauB,SAAS,SAAS,EAAEC,UAAU,CAAC;EACxG;EAEAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC1B,IAAI,CAACa,GAAG,CAA0B,GAAG,IAAI,CAACZ,OAAO,oBAAoB,CAAC;EACpF;EAEA0B,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC3B,IAAI,CAACa,GAAG,CAAwC,GAAG,IAAI,CAACZ,OAAO,oBAAoB,CAAC;EAClG;EAEA2B,iBAAiBA,CAACC,QAAgB,EAAE1B,OAAA,GAA0B,EAAE;IAC9D,IAAIC,MAAM,GAAG,IAAIP,UAAU,EAAE;IAE7BQ,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;MACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;MACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;IAE9C,CAAC,CAAC;IAEF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAmB,GAAG,IAAI,CAACZ,OAAO,oBAAoB4B,QAAQ,EAAE,EAAE;MAAEzB;IAAM,CAAE,CAAC;EACnG;EAEA0B,cAAcA,CAACC,KAAa,EAAE5B,OAAA,GAA0B,EAAE;IACxD,MAAM6B,aAAa,GAAG;MAAE,GAAG7B,OAAO;MAAE8B,MAAM,EAAEF;IAAK,CAAE;IACnD,OAAO,IAAI,CAAC7B,WAAW,CAAC8B,aAAa,CAAC;EACxC;EAEAE,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClC,IAAI,CAACa,GAAG,CAAoC,GAAG,IAAI,CAACZ,OAAO,aAAa,CAAC;EACvF;EAEAkC,SAASA,CAAA;IACP,OAAO,IAAI,CAACnC,IAAI,CAACa,GAAG,CAAuB,GAAG,IAAI,CAACZ,OAAO,kBAAkB,CAAC;EAC/E;EAEA;EACAmC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACpC,IAAI,CAACa,GAAG,CAAoC,GAAG,IAAI,CAACZ,OAAO,kBAAkB,CAAC;EAC5F;EAEA;EACAoC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACrC,IAAI,CAACa,GAAG,CAAwC,GAAG,IAAI,CAACZ,OAAO,wBAAwB,CAAC;EACtG;EAEA;EACAqC,iBAAiBA,CAACd,SAAiB;IACjC,OAAO,IAAI,CAACxB,IAAI,CAACkB,IAAI,CAAwC,GAAG,IAAI,CAACjB,OAAO,aAAauB,SAAS,OAAO,EAAE,EAAE,CAAC;EAChH;EAEAe,YAAYA,CAACf,SAAiB;IAC5B,OAAO,IAAI,CAACxB,IAAI,CAACkB,IAAI,CAAwC,GAAG,IAAI,CAACjB,OAAO,aAAauB,SAAS,QAAQ,EAAE,EAAE,CAAC;EACjH;EAEA;EACAgB,mBAAmBA,CAACC,YAAoB,EAAEtC,OAAA,GAA0B,EAAE;IACpE,IAAIC,MAAM,GAAG,IAAIP,UAAU,EAAE;IAE7BQ,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;MACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;MACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;IAE9C,CAAC,CAAC;IAEF,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAmB,GAAG,IAAI,CAACZ,OAAO,sBAAsBwC,YAAY,EAAE,EAAE;MAAErC;IAAM,CAAE,CAAC;EACzG;;;uBAvGWN,cAAc,EAAA4C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd/C,cAAc;MAAAgD,OAAA,EAAdhD,cAAc,CAAAiD,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}