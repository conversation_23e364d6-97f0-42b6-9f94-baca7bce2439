const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Socket.IO configuration and event handlers
module.exports = (io) => {
  console.log('🔌 Setting up Socket.IO event handlers...');

  // Middleware for authentication
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (token) {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret_key_for_development');
        const user = await User.findById(decoded.id).select('-password');
        
        if (user) {
          socket.userId = user._id.toString();
          socket.user = user;
          console.log(`👤 User ${user.username} connected via Socket.IO`);
        }
      }
      
      next();
    } catch (error) {
      console.log('🔒 Anonymous user connected via Socket.IO');
      next(); // Allow anonymous connections
    }
  });

  // Connection handler
  io.on('connection', (socket) => {
    console.log(`🔗 Socket connected: ${socket.id}${socket.userId ? ` (User: ${socket.user.username})` : ' (Anonymous)'}`);

    // Join user to their personal room for targeted updates
    if (socket.userId) {
      socket.join(`user_${socket.userId}`);
      socket.join('authenticated_users');
      
      // Update user's online status
      updateUserOnlineStatus(socket.userId, true);
    }

    // Cart Events
    socket.on('cart:add', (data) => {
      console.log('🛒 Cart add event:', data);
      // Broadcast to user's other sessions
      if (socket.userId) {
        socket.to(`user_${socket.userId}`).emit('cart:updated', {
          action: 'add',
          item: data,
          timestamp: new Date()
        });
      }
    });

    socket.on('cart:remove', (data) => {
      console.log('🛒 Cart remove event:', data);
      if (socket.userId) {
        socket.to(`user_${socket.userId}`).emit('cart:updated', {
          action: 'remove',
          itemId: data.itemId,
          timestamp: new Date()
        });
      }
    });

    socket.on('cart:update', (data) => {
      console.log('🛒 Cart update event:', data);
      if (socket.userId) {
        socket.to(`user_${socket.userId}`).emit('cart:updated', {
          action: 'update',
          item: data,
          timestamp: new Date()
        });
      }
    });

    // Wishlist Events
    socket.on('wishlist:add', (data) => {
      console.log('❤️ Wishlist add event:', data);
      if (socket.userId) {
        socket.to(`user_${socket.userId}`).emit('wishlist:updated', {
          action: 'add',
          item: data,
          timestamp: new Date()
        });
      }
    });

    socket.on('wishlist:remove', (data) => {
      console.log('❤️ Wishlist remove event:', data);
      if (socket.userId) {
        socket.to(`user_${socket.userId}`).emit('wishlist:updated', {
          action: 'remove',
          itemId: data.itemId,
          timestamp: new Date()
        });
      }
    });

    // Post Events
    socket.on('post:like', (data) => {
      console.log('👍 Post like event:', data);
      // Broadcast to all users viewing the post
      socket.broadcast.emit('post:liked', {
        postId: data.postId,
        userId: socket.userId,
        username: socket.user?.username,
        timestamp: new Date()
      });
    });

    socket.on('post:comment', (data) => {
      console.log('💬 Post comment event:', data);
      // Broadcast to all users viewing the post
      socket.broadcast.emit('post:commented', {
        postId: data.postId,
        comment: data.comment,
        userId: socket.userId,
        username: socket.user?.username,
        timestamp: new Date()
      });
    });

    // Story Events
    socket.on('story:view', (data) => {
      console.log('👁️ Story view event:', data);
      // Notify story owner
      if (data.storyOwnerId && data.storyOwnerId !== socket.userId) {
        socket.to(`user_${data.storyOwnerId}`).emit('story:viewed', {
          storyId: data.storyId,
          viewerId: socket.userId,
          viewerUsername: socket.user?.username,
          timestamp: new Date()
        });
      }
    });

    // Product Events
    socket.on('product:view', (data) => {
      console.log('👀 Product view event:', data);
      // Track product views for analytics
      socket.broadcast.emit('product:viewed', {
        productId: data.productId,
        userId: socket.userId,
        timestamp: new Date()
      });
    });

    // Join specific rooms for real-time updates
    socket.on('join:product', (productId) => {
      socket.join(`product_${productId}`);
      console.log(`📦 User joined product room: ${productId}`);
    });

    socket.on('leave:product', (productId) => {
      socket.leave(`product_${productId}`);
      console.log(`📦 User left product room: ${productId}`);
    });

    socket.on('join:post', (postId) => {
      socket.join(`post_${postId}`);
      console.log(`📝 User joined post room: ${postId}`);
    });

    socket.on('leave:post', (postId) => {
      socket.leave(`post_${postId}`);
      console.log(`📝 User left post room: ${postId}`);
    });

    // Disconnect handler
    socket.on('disconnect', () => {
      console.log(`🔌 Socket disconnected: ${socket.id}`);
      
      if (socket.userId) {
        // Update user's online status
        updateUserOnlineStatus(socket.userId, false);
      }
    });

    // Error handler
    socket.on('error', (error) => {
      console.error('🚨 Socket error:', error);
    });
  });

  // Helper function to update user online status
  async function updateUserOnlineStatus(userId, isOnline) {
    try {
      await User.findByIdAndUpdate(userId, {
        isOnline: isOnline,
        lastSeen: new Date()
      });
    } catch (error) {
      console.error('Error updating user online status:', error);
    }
  }

  console.log('✅ Socket.IO event handlers configured');
};

// Utility functions for emitting events from other parts of the application
const emitToUser = (userId, event, data) => {
  if (global.io) {
    global.io.to(`user_${userId}`).emit(event, data);
  }
};

const emitToAll = (event, data) => {
  if (global.io) {
    global.io.emit(event, data);
  }
};

const emitToRoom = (room, event, data) => {
  if (global.io) {
    global.io.to(room).emit(event, data);
  }
};

module.exports.emitToUser = emitToUser;
module.exports.emitToAll = emitToAll;
module.exports.emitToRoom = emitToRoom;
