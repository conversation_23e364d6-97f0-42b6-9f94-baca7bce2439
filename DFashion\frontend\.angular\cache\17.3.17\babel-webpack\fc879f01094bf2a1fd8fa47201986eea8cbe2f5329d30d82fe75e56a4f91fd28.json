{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { register } from 'swiper/element/bundle';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/cart.service\";\nimport * as i5 from \"src/app/core/services/wishlist.service\";\nimport * as i6 from \"src/app/core/services/social-media.service\";\nimport * as i7 from \"src/app/core/services/realtime.service\";\nimport * as i8 from \"src/app/core/services/button-actions.service\";\nimport * as i9 from \"@angular/common\";\nconst _c0 = [\"storiesSlider\"];\nconst _c1 = [\"storiesTrack\"];\nconst _c2 = [\"storyVideo\"];\nfunction ViewAddStoriesComponent_swiper_slide_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"swiper-slide\")(1, \"div\", 26)(2, \"div\", 14)(3, \"div\", 27)(4, \"div\", 16);\n    i0.ɵɵelement(5, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵtext(7, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_swiper_slide_26_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_swiper_slide_26_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const userGroup_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", userGroup_r4.totalProducts, \" item\", userGroup_r4.totalProducts > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_swiper_slide_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"swiper-slide\")(1, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_swiper_slide_26_Template_div_click_1_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const userGroup_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.openUserStories(userGroup_r4, i_r5));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14)(3, \"div\", 30)(4, \"div\", 16);\n    i0.ɵɵelement(5, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ViewAddStoriesComponent_swiper_slide_26_div_6_Template, 2, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 20);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 33);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ViewAddStoriesComponent_swiper_slide_26_div_11_Template, 2, 2, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const userGroup_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"has-products\", userGroup_r4.hasProducts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", userGroup_r4.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", userGroup_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", userGroup_r4.hasProducts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(userGroup_r4.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", userGroup_r4.stories.length, \" \", userGroup_r4.stories.length === 1 ? \"story\" : \"stories\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", userGroup_r4.hasProducts);\n  }\n}\nfunction ViewAddStoriesComponent_div_27_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r5.currentStoryIndex)(\"completed\", i_r8 < ctx_r5.currentStoryIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r5.getProgressWidth(i_r8), \"%\");\n  }\n}\nfunction ViewAddStoriesComponent_div_27_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 66);\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (tmp_4_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_4_0.mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_27_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 67, 2);\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (tmp_5_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_5_0.mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_27_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_div_17_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.handleMiddleAreaClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 69);\n    i0.ɵɵelement(2, \"i\", 70);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \"View linked \", (tmp_4_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_4_0.linkedContent == null ? null : tmp_4_0.linkedContent.type, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.getLinkedContentText());\n  }\n}\nfunction ViewAddStoriesComponent_div_27_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_div_18_div_1_Template_div_click_0_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.openProductDetails(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"div\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵelement(3, \"img\", 76);\n    i0.ɵɵelementStart(4, \"div\", 77)(5, \"span\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 79);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 80)(10, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_div_18_div_1_Template_button_click_10_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.addToCart(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(11, \"i\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_div_18_div_1_Template_button_click_12_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.addToWishlist(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(13, \"i\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_div_18_div_1_Template_button_click_14_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.buyNow(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(15, \"i\", 86);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const productTag_r11 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", (productTag_r11.position == null ? null : productTag_r11.position.x) || 50, \"%\")(\"top\", (productTag_r11.position == null ? null : productTag_r11.position.y) || 50, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", productTag_r11.product.images[0].url || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", productTag_r11.product.name || \"Product\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r11.product.name || \"Product\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", productTag_r11.product.price || 0, \"\");\n  }\n}\nfunction ViewAddStoriesComponent_div_27_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_27_div_18_div_1_Template, 16, 8, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", (tmp_4_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_4_0.products);\n  }\n}\nfunction ViewAddStoriesComponent_div_27_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_button_19_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      ctx_r5.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 88);\n    i0.ɵɵelementStart(2, \"span\", 89);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r5.showProductTags);\n    i0.ɵɵproperty(\"title\", \"View \" + ctx_r5.getProductCount() + \" product\" + (ctx_r5.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵattribute(\"aria-label\", \"View \" + ctx_r5.getProductCount() + \" product\" + (ctx_r5.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getProductCount());\n  }\n}\nfunction ViewAddStoriesComponent_div_27_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.previousStory());\n    });\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_27_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.nextStory());\n    });\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39, 1)(3, \"div\", 40);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_27_div_4_Template, 2, 6, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵelement(6, \"img\", 43);\n    i0.ɵɵelementStart(7, \"div\", 44)(8, \"span\", 45);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 46);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.closeStories());\n    });\n    i0.ɵɵelement(13, \"i\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_Template_div_click_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_27_Template_div_touchstart_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_27_Template_div_touchmove_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_27_Template_div_touchend_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchEnd($event));\n    });\n    i0.ɵɵtemplate(15, ViewAddStoriesComponent_div_27_img_15_Template, 1, 1, \"img\", 50)(16, ViewAddStoriesComponent_div_27_video_16_Template, 2, 1, \"video\", 51)(17, ViewAddStoriesComponent_div_27_div_17_Template, 5, 3, \"div\", 52)(18, ViewAddStoriesComponent_div_27_div_18_Template, 2, 1, \"div\", 53)(19, ViewAddStoriesComponent_div_27_button_19_Template, 4, 5, \"button\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 55)(21, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.toggleLike());\n    });\n    i0.ɵɵelement(22, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.shareStory());\n    });\n    i0.ɵɵelement(24, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_27_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.saveStory());\n    });\n    i0.ɵɵelement(26, \"i\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, ViewAddStoriesComponent_div_27_button_27_Template, 2, 0, \"button\", 62)(28, ViewAddStoriesComponent_div_27_button_28_Template, 2, 0, \"button\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.stories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (tmp_4_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_4_0.user == null ? null : tmp_4_0.user.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_5_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_5_0.user == null ? null : tmp_5_0.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getTimeAgo(((tmp_6_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_6_0.createdAt) || \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_7_0.mediaType) === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_8_0.mediaType) === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_9_0.linkedContent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showProductTags && ((tmp_10_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_10_0.products));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.hasProducts());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r5.isLiked);\n    i0.ɵɵproperty(\"title\", ctx_r5.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r5.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex < ctx_r5.stories.length - 1);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService, cartService, wishlistService, socialMediaService, realtimeService, cdr, buttonActionsService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.socialMediaService = socialMediaService;\n    this.realtimeService = realtimeService;\n    this.cdr = cdr;\n    this.buttonActionsService = buttonActionsService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    // Story viewer state\n    this.isOpen = false;\n    this.currentStoryIndex = 0;\n    this.currentUserIndex = 0;\n    this.currentUserStories = [];\n    this.showProductTags = false;\n    this.isLiked = false;\n    this.storyProgress = 0;\n    // Navigation state\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.showNavArrows = true;\n    // Custom slider properties\n    this.translateX = 0;\n    this.itemWidth = 80; // Width of each story item including margin\n    this.visibleItems = 6; // Number of visible items\n    this.currentSlideIndex = 0;\n    this.storyDuration = 15000; // 15 seconds default\n    this.progressStartTime = 0;\n    this.currentProgress = 0; // Stable progress value for template binding\n    // Touch handling\n    this.touchStartTime = 0;\n    // Swiper configuration (adjusted for stories only, no add story slide)\n    this.swiperConfig = {\n      slidesPerView: 5,\n      spaceBetween: 16,\n      navigation: {\n        nextEl: '.swiper-button-next',\n        prevEl: '.swiper-button-prev'\n      },\n      autoplay: {\n        delay: 3000,\n        disableOnInteraction: false,\n        pauseOnMouseEnter: true\n      },\n      loop: true,\n      breakpoints: {\n        0: {\n          slidesPerView: 2,\n          spaceBetween: 12\n        },\n        600: {\n          slidesPerView: 3,\n          spaceBetween: 14\n        },\n        900: {\n          slidesPerView: 5,\n          spaceBetween: 16\n        }\n      }\n    };\n    // Auto-scroll control\n    this.isAutoScrollEnabled = true;\n    this.subscriptions = [];\n    console.log('🏗️ ViewAddStoriesComponent constructor called');\n  }\n  ngOnInit() {\n    console.log('🚀 ViewAddStoriesComponent ngOnInit called');\n    // Register Swiper custom elements\n    register();\n    this.loadStories();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      console.log('👤 Current user updated:', user);\n    });\n    // Check if we should show navigation arrows based on screen size\n    this.updateNavArrowsVisibility();\n  }\n  // Toggle auto-scroll functionality\n  toggleAutoScroll() {\n    this.isAutoScrollEnabled = !this.isAutoScrollEnabled;\n    const swiperEl = document.querySelector('.stories-swiper');\n    if (swiperEl && swiperEl.swiper) {\n      if (this.isAutoScrollEnabled) {\n        swiperEl.swiper.autoplay.start();\n      } else {\n        swiperEl.swiper.autoplay.stop();\n      }\n    }\n  }\n  // Get auto-scroll button icon\n  getAutoScrollIcon() {\n    return this.isAutoScrollEnabled ? 'fas fa-pause' : 'fas fa-play';\n  }\n  // Get auto-scroll button title\n  getAutoScrollTitle() {\n    return this.isAutoScrollEnabled ? 'Pause auto-scroll' : 'Start auto-scroll';\n  }\n  ngAfterViewInit() {\n    console.log('ngAfterViewInit called');\n    setTimeout(() => {\n      console.log('Initializing slider after view init');\n      this.calculateSliderDimensions();\n      this.updateScrollButtons();\n    }, 500);\n    // Also try immediate initialization\n    setTimeout(() => {\n      if (this.stories.length > 0) {\n        console.log('Re-initializing slider with stories');\n        this.calculateSliderDimensions();\n        this.updateScrollButtons();\n      }\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n  onResize() {\n    this.updateNavArrowsVisibility();\n    this.calculateSliderDimensions();\n    this.updateScrollButtons();\n  }\n  updateNavArrowsVisibility() {\n    this.showNavArrows = window.innerWidth > 768;\n  }\n  calculateSliderDimensions() {\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\n      const screenWidth = window.innerWidth;\n      console.log('Calculating slider dimensions:', {\n        containerWidth,\n        screenWidth\n      });\n      // Responsive visible items\n      if (screenWidth <= 600) {\n        this.visibleItems = 3;\n        this.itemWidth = containerWidth / 3;\n      } else if (screenWidth <= 900) {\n        this.visibleItems = 4;\n        this.itemWidth = containerWidth / 4;\n      } else {\n        this.visibleItems = 6;\n        this.itemWidth = containerWidth / 6;\n      }\n      console.log('Slider dimensions calculated:', {\n        visibleItems: this.visibleItems,\n        itemWidth: this.itemWidth\n      });\n      // Reset slider position if needed\n      this.currentSlideIndex = 0;\n      this.translateX = 0;\n    } else {\n      console.warn('Stories slider element not found, using default dimensions');\n      // Fallback dimensions\n      this.itemWidth = 100;\n      this.visibleItems = 6;\n    }\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        console.log('Stories API response:', response);\n        if (response.success && response.stories && response.stories.length > 0) {\n          // Filter only active stories and map the data structure\n          const allStories = response.stories.filter(story => story.isActive).map(story => ({\n            ...story,\n            mediaUrl: story.media?.url || story.mediaUrl,\n            mediaType: story.media?.type || story.mediaType\n          }));\n          // Group stories by user (Instagram style)\n          this.stories = this.groupStoriesByUser(allStories);\n          console.log('Loaded and grouped stories from API:', this.stories);\n          console.log('Total user story groups:', this.stories.length);\n        } else {\n          console.log('No stories from API, loading fallback stories');\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        console.log('Loading fallback stories due to error');\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      }\n    }));\n  }\n  // Group stories by user like Instagram\n  groupStoriesByUser(allStories) {\n    const userStoriesMap = new Map();\n    allStories.forEach(story => {\n      const userId = story.user._id;\n      if (!userStoriesMap.has(userId)) {\n        userStoriesMap.set(userId, {\n          user: story.user,\n          stories: [],\n          hasProducts: false,\n          totalProducts: 0,\n          latestStoryTime: story.createdAt\n        });\n      }\n      const userGroup = userStoriesMap.get(userId);\n      userGroup.stories.push(story);\n      // Check if any story has products\n      if (story.products && story.products.length > 0) {\n        userGroup.hasProducts = true;\n        userGroup.totalProducts += story.products.length;\n      }\n      // Keep track of latest story time for sorting\n      if (new Date(story.createdAt) > new Date(userGroup.latestStoryTime)) {\n        userGroup.latestStoryTime = story.createdAt;\n      }\n    });\n    // Convert map to array and sort by latest story time\n    return Array.from(userStoriesMap.values()).sort((a, b) => new Date(b.latestStoryTime).getTime() - new Date(a.latestStoryTime).getTime());\n  }\n  loadFallbackStories() {\n    console.log('❌ No stories available from API');\n    this.stories = [];\n  }\n  // Navigation methods\n  scrollLeft() {\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\n    if (this.currentSlideIndex > 0) {\n      this.currentSlideIndex--;\n      this.updateSliderPosition();\n      console.log('Scrolled left to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll left, already at start');\n    }\n  }\n  scrollRight() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    console.log('Scroll right clicked:', {\n      currentIndex: this.currentSlideIndex,\n      totalItems,\n      maxSlideIndex,\n      visibleItems: this.visibleItems\n    });\n    if (this.currentSlideIndex < maxSlideIndex) {\n      this.currentSlideIndex++;\n      this.updateSliderPosition();\n      console.log('Scrolled right to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll right, already at end');\n    }\n  }\n  updateSliderPosition() {\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\n    console.log('Updating slider position:', {\n      currentIndex: this.currentSlideIndex,\n      itemWidth: this.itemWidth,\n      newTranslateX\n    });\n    this.translateX = newTranslateX;\n    this.updateScrollButtons();\n  }\n  updateScrollButtons() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    this.canScrollLeft = this.currentSlideIndex > 0;\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\n    console.log('Updated scroll buttons:', {\n      canScrollLeft: this.canScrollLeft,\n      canScrollRight: this.canScrollRight,\n      totalItems,\n      maxSlideIndex,\n      currentIndex: this.currentSlideIndex\n    });\n  }\n  // Story viewer methods\n  openUserStories(userGroup, userIndex) {\n    this.currentUserIndex = userIndex;\n    this.currentStoryIndex = 0; // Start with first story of this user\n    this.currentUserStories = userGroup.stories;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n    console.log('Opening stories for user:', userGroup.user.username, 'Stories count:', userGroup.stories.length);\n  }\n  openStory(_story, index) {\n    // Legacy method - keeping for compatibility\n    this.currentStoryIndex = index;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.clearStoryTimer();\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n  }\n  nextStory() {\n    // First check if there are more stories for current user\n    if (this.currentStoryIndex < this.currentUserStories.length - 1) {\n      this.currentStoryIndex++;\n      this.showProductTags = false;\n      this.startStoryTimer();\n    } else {\n      // Move to next user's stories\n      if (this.currentUserIndex < this.stories.length - 1) {\n        this.currentUserIndex++;\n        this.currentStoryIndex = 0;\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\n        this.showProductTags = false;\n        this.startStoryTimer();\n      } else {\n        this.closeStories();\n      }\n    }\n  }\n  previousStory() {\n    // First check if there are previous stories for current user\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.showProductTags = false;\n      this.startStoryTimer();\n    } else {\n      // Move to previous user's stories (last story)\n      if (this.currentUserIndex > 0) {\n        this.currentUserIndex--;\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\n        this.currentStoryIndex = this.currentUserStories.length - 1;\n        this.showProductTags = false;\n        this.startStoryTimer();\n      } else {\n        this.closeStories();\n      }\n    }\n  }\n  getCurrentStory() {\n    if (this.currentUserStories && this.currentUserStories.length > 0) {\n      return this.currentUserStories[this.currentStoryIndex] || this.currentUserStories[0];\n    }\n    // Fallback for legacy usage - get first story from first user group\n    if (this.stories && this.stories.length > 0 && this.stories[0].stories.length > 0) {\n      return this.stories[0].stories[0];\n    }\n    return null;\n  }\n  getCurrentUser() {\n    if (this.stories && this.stories.length > 0) {\n      return this.stories[this.currentUserIndex]?.user;\n    }\n    return null;\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getProductCount() {\n    const story = this.getCurrentStory();\n    return story?.products?.length || 0;\n  }\n  // Progress tracking\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) return 100;\n    if (index > this.currentStoryIndex) return 0;\n    // Return the stable progress value for the current story\n    return this.currentProgress;\n  }\n  startStoryTimer() {\n    this.clearStoryTimer();\n    this.progressStartTime = Date.now();\n    this.currentProgress = 0;\n    // Update progress every 100ms for smooth animation\n    this.progressUpdateTimer = setInterval(() => {\n      if (this.progressStartTime) {\n        const elapsed = Date.now() - this.progressStartTime;\n        this.currentProgress = Math.min(elapsed / this.storyDuration * 100, 100);\n        this.cdr.detectChanges(); // Trigger change detection manually\n      }\n    }, 100);\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n  clearStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n      this.progressTimer = null;\n    }\n    if (this.progressUpdateTimer) {\n      clearInterval(this.progressUpdateTimer);\n      this.progressUpdateTimer = null;\n    }\n    this.progressStartTime = 0;\n    this.currentProgress = 0;\n  }\n  // Story interaction methods\n  onStoryClick(event) {\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else if (clickX > windowWidth * 2 / 3) {\n      this.nextStory();\n    }\n  }\n  // Touch handling\n  onTouchStart(_event) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.clearStoryTimer(); // Pause story progress on long press\n    }, 500);\n  }\n  onTouchMove(_event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onTouchEnd(event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n    const touchDuration = Date.now() - this.touchStartTime;\n    if (touchDuration < 500) {\n      // Short tap - treat as click\n      const touch = event.changedTouches[0];\n      this.onStoryClick({\n        clientX: touch.clientX\n      });\n    } else {\n      // Long press ended - resume story progress\n      this.startStoryTimer();\n    }\n  }\n  // Product interaction methods\n  toggleProductTags() {\n    this.showProductTags = !this.showProductTags;\n  }\n  openProductDetails(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  // Add product to cart with real-time functionality\n  addToCart(product) {\n    console.log('Adding product to cart:', product);\n    this.buttonActionsService.addToCart({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      quantity: 1,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to cart successfully:', result);\n          // Show success message or toast\n        } else {\n          console.error('Failed to add product to cart:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding product to cart:', error);\n      }\n    });\n  }\n  // Add product to wishlist with real-time functionality\n  addToWishlist(product) {\n    console.log('Adding product to wishlist:', product);\n    this.buttonActionsService.addToWishlist({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully:', result);\n          // Show success message or toast\n        } else {\n          console.error('Failed to add product to wishlist:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding product to wishlist:', error);\n      }\n    });\n  }\n  // Buy now functionality\n  buyNow(product) {\n    console.log('Buy now clicked for product:', product);\n    this.buttonActionsService.buyNow({\n      productId: product._id,\n      size: product.size,\n      color: product.color,\n      quantity: 1,\n      addedFrom: 'story'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Buy now successful:', result);\n          // Navigation to checkout will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error in buy now:', error);\n      }\n    });\n  }\n  // Story actions with real-time functionality\n  toggleLike() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    this.isLiked = !this.isLiked;\n    // Call API to like/unlike story\n    const endpoint = this.isLiked ? 'like' : 'unlike';\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\n      next: response => {\n        console.log(`Story ${endpoint}d successfully:`, response);\n        // Emit real-time event (using socket directly)\n        if (this.realtimeService.isConnected()) {\n          // For now, we'll track this locally until we add story-specific emit methods\n          console.log('Story liked event:', {\n            storyId: currentStory._id,\n            userId: this.authService.currentUserValue?._id,\n            liked: this.isLiked\n          });\n        }\n      },\n      error: error => {\n        console.error(`Error ${endpoint}ing story:`, error);\n        // Revert the like state on error\n        this.isLiked = !this.isLiked;\n      }\n    });\n  }\n  shareStory() {\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    if (navigator.share) {\n      // Use native sharing if available\n      navigator.share({\n        title: `Story by ${currentStory.user.username}`,\n        text: currentStory.caption,\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\n      }).then(() => {\n        console.log('Story shared successfully');\n        // Track share event\n        this.trackStoryShare(currentStory._id);\n      }).catch(error => {\n        console.error('Error sharing story:', error);\n      });\n    } else {\n      // Fallback: copy link to clipboard\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\n      navigator.clipboard.writeText(shareUrl).then(() => {\n        console.log('Story link copied to clipboard');\n        this.trackStoryShare(currentStory._id);\n        // Show toast notification\n        this.showToast('Story link copied to clipboard!');\n      }).catch(error => {\n        console.error('Error copying to clipboard:', error);\n      });\n    }\n  }\n  saveStory() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    if (!currentStory) return;\n    // Call API to save story\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\n      next: response => {\n        console.log('Story saved successfully:', response);\n        this.showToast('Story saved to your collection!');\n        // Emit real-time event (using socket directly)\n        if (this.realtimeService.isConnected()) {\n          // For now, we'll track this locally until we add story-specific emit methods\n          console.log('Story saved event:', {\n            storyId: currentStory._id,\n            userId: this.authService.currentUserValue?._id\n          });\n        }\n      },\n      error: error => {\n        console.error('Error saving story:', error);\n        this.showToast('Error saving story. Please try again.');\n      }\n    });\n  }\n  trackStoryShare(storyId) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\n        next: response => {\n          console.log('Story share tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking story share:', error);\n        }\n      });\n    }\n  }\n  // Handle middle area click for product/category navigation\n  handleMiddleAreaClick(event) {\n    event.stopPropagation();\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return;\n    }\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        if (currentStory.linkedContent.productId) {\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\n        } else if (currentStory.products && currentStory.products.length > 0) {\n          // Fallback to first product if no specific productId\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\n        }\n        break;\n      case 'category':\n        if (currentStory.linkedContent.categoryId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              category: currentStory.linkedContent.categoryId\n            }\n          });\n        }\n        break;\n      case 'brand':\n        if (currentStory.linkedContent.brandId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              brand: currentStory.linkedContent.brandId\n            }\n          });\n        }\n        break;\n      case 'collection':\n        if (currentStory.linkedContent.collectionId) {\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\n        }\n        break;\n      default:\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\n    }\n    // Track click event\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\n  }\n  // Get text for linked content indicator\n  getLinkedContentText() {\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return '';\n    }\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        return 'View Product';\n      case 'category':\n        return 'Browse Category';\n      case 'brand':\n        return 'View Brand';\n      case 'collection':\n        return 'View Collection';\n      default:\n        return 'View Details';\n    }\n  }\n  // Track linked content click for analytics\n  trackLinkedContentClick(storyId, linkedContent) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\n        contentType: linkedContent.type,\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\n      }).subscribe({\n        next: response => {\n          console.log('Linked content click tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking linked content click:', error);\n        }\n      });\n    }\n  }\n  showToast(message) {\n    // Simple toast implementation - you can replace with your preferred toast library\n    const toast = document.createElement('div');\n    toast.textContent = message;\n    toast.style.cssText = `\n      position: fixed;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-size: 14px;\n    `;\n    document.body.appendChild(toast);\n    setTimeout(() => {\n      document.body.removeChild(toast);\n    }, 3000);\n  }\n  // Add story functionality\n  onAdd() {\n    this.router.navigate(['/stories/create']);\n  }\n  // Utility methods\n  getTimeAgo(dateString) {\n    if (!dateString) return 'Unknown';\n    const now = new Date();\n    let date;\n    if (typeof dateString === 'string') {\n      date = new Date(dateString);\n    } else {\n      date = dateString;\n    }\n    // Check if date is valid\n    if (isNaN(date.getTime())) {\n      return 'Unknown';\n    }\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CartService), i0.ɵɵdirectiveInject(i5.WishlistService), i0.ɵɵdirectiveInject(i6.SocialMediaService), i0.ɵɵdirectiveInject(i7.RealtimeService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i8.ButtonActionsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesTrack = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow)(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 28,\n      vars: 13,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [\"storyVideo\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [1, \"header-controls\"], [\"type\", \"button\", \"aria-label\", \"Toggle auto-scroll\", 1, \"auto-scroll-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\"], [\"type\", \"button\", \"aria-label\", \"Create new story\", \"title\", \"Create a new story\", 1, \"create-story-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-plus\"], [1, \"stories-main-layout\"], [1, \"add-story-section\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus-btn\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [1, \"stories-slider-wrapper\"], [1, \"stories-swiper\", 3, \"slides-per-view\", \"space-between\", \"navigation\", \"autoplay\", \"loop\", \"breakpoints\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"stories-overlay\", 4, \"ngIf\"], [1, \"story-item\", \"loading-story\"], [1, \"story-avatar\", \"loading-avatar\"], [1, \"loading-spinner\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [\"class\", \"shopping-bag-indicator\", \"title\", \"Shoppable content\", 4, \"ngIf\"], [1, \"story-count-badge\"], [\"class\", \"product-count-badge\", 4, \"ngIf\"], [\"title\", \"Shoppable content\", 1, \"shopping-bag-indicator\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-count-badge\"], [1, \"stories-overlay\"], [1, \"stories-content\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [\"alt\", \"Avatar\", 1, \"story-header-avatar\", 3, \"src\"], [1, \"story-header-info\"], [1, \"username\"], [1, \"time\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-media\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [\"class\", \"story-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-video\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-middle-click-area\", 3, \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"shopping-bag-btn\", \"type\", \"button\", 3, \"active\", \"title\", \"click\", 4, \"ngIf\"], [1, \"story-actions\"], [\"type\", \"button\", 1, \"action-btn\", \"like-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"aria-label\", \"Share story\", \"title\", \"Share story\", 1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-share\"], [\"type\", \"button\", \"aria-label\", \"Save story\", \"title\", \"Save story\", 1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-bookmark\"], [\"class\", \"story-nav-btn story-nav-prev\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-nav-btn story-nav-next\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-image\", 3, \"src\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-video\", 3, \"src\"], [1, \"story-middle-click-area\", 3, \"click\", \"title\"], [1, \"middle-click-indicator\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-dot\"], [1, \"product-tag-info\"], [1, \"product-tag-image\", 3, \"src\", \"alt\"], [1, \"product-tag-details\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"product-tag-actions\"], [\"type\", \"button\", \"title\", \"Add to Cart\", 1, \"product-action-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [\"type\", \"button\", \"title\", \"Add to Wishlist\", 1, \"product-action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"title\", \"Buy Now\", 1, \"product-action-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [\"type\", \"button\", 1, \"shopping-bag-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-shopping-bag\"], [\"aria-hidden\", \"true\", 1, \"product-count\"], [1, \"story-nav-btn\", \"story-nav-prev\", 3, \"click\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"story-nav-btn\", \"story-nav-next\", 3, \"click\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3, 0)(2, \"div\", 4)(3, \"h3\", 5);\n          i0.ɵɵtext(4, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleAutoScroll());\n          });\n          i0.ɵɵelement(7, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementStart(10, \"span\");\n          i0.ɵɵtext(11, \"Create\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_div_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"div\", 15)(17, \"div\", 16);\n          i0.ɵɵelement(18, \"img\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 18);\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 20);\n          i0.ɵɵtext(22, \"Your Story\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 21)(24, \"swiper-container\", 22);\n          i0.ɵɵtemplate(25, ViewAddStoriesComponent_swiper_slide_25_Template, 8, 0, \"swiper-slide\", 23)(26, ViewAddStoriesComponent_swiper_slide_26_Template, 12, 9, \"swiper-slide\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(27, ViewAddStoriesComponent_div_27_Template, 29, 15, \"div\", 25);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"title\", ctx.getAutoScrollTitle());\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.getAutoScrollIcon());\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"slides-per-view\", ctx.swiperConfig.slidesPerView)(\"space-between\", ctx.swiperConfig.spaceBetween)(\"navigation\", true)(\"autoplay\", ctx.swiperConfig.autoplay)(\"loop\", ctx.swiperConfig.loop)(\"breakpoints\", ctx.swiperConfig.breakpoints);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i9.NgForOf, i9.NgIf, FormsModule],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  overflow: hidden;\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 8px;\\n    padding: 12px;\\n    border-radius: 12px;\\n  }\\n}\\n\\n.stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  flex: 1;\\n  min-width: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .header-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  flex-shrink: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);\\n  position: relative;\\n  flex-shrink: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px) scale(1.05);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);\\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\\n}\\n.stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) scale(1.02);\\n  transition-duration: 0.1s;\\n}\\n.stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.5);\\n  outline-offset: 2px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n@media (max-width: 768px) {\\n  .stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .auto-scroll-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  color: white;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  white-space: nowrap;\\n  min-height: 36px;\\n  flex-shrink: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.5);\\n  outline-offset: 2px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n@media (max-width: 768px) {\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 8px;\\n    min-width: 36px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 6px;\\n    font-size: 12px;\\n    min-height: 28px;\\n    min-width: 28px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-header[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n\\n\\n.stories-main-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  width: 100%;\\n}\\n@media (max-width: 768px) {\\n  .stories-main-layout[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-main-layout[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n}\\n\\n\\n\\n.add-story-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-right: 8px; \\n\\n}\\n.add-story-section[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n@media (max-width: 768px) {\\n  .add-story-section[_ngcontent-%COMP%] {\\n    margin-right: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .add-story-section[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n  }\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1;\\n  min-width: 0; \\n\\n  min-height: 120px; \\n\\n  padding: 8px 0; \\n\\n}\\n\\n\\n\\n.stories-swiper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0 35px; \\n\\n  position: relative;\\n  \\n\\n  --swiper-navigation-size: 22px;\\n  --swiper-navigation-top-offset: 50%;\\n  --swiper-navigation-sides-offset: 4px;\\n  --swiper-navigation-color: #6b7280;\\n  --swiper-theme-color: #6b7280;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     {\\n  \\n\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-wrapper {\\n  align-items: center;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-slide {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: auto;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev {\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)) !important;\\n  border: 1px solid rgba(0, 0, 0, 0.08) !important;\\n  border-radius: 50% !important;\\n  width: 22px !important;\\n  height: 22px !important;\\n  margin-top: -11px !important;\\n  cursor: pointer !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.04) !important;\\n  z-index: 12 !important;\\n  -webkit-backdrop-filter: blur(6px) !important;\\n          backdrop-filter: blur(6px) !important;\\n  \\n\\n  \\n\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next svg, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev svg {\\n  display: none !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next:after, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev:after {\\n  content: \\\"\\\" !important;\\n  display: none !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next:before, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev:before {\\n  content: \\\"\\\" !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  width: 100% !important;\\n  height: 100% !important;\\n  background-size: 10px 10px !important;\\n  background-repeat: no-repeat !important;\\n  background-position: center !important;\\n  filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8)) !important;\\n  transition: all 0.3s ease !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next:hover, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev:hover {\\n  background: linear-gradient(135deg, #ffffff, #f8fafc) !important;\\n  transform: scale(1.1) translateY(-1px) !important;\\n  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08) !important;\\n  border-color: rgba(79, 70, 229, 0.15) !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next:hover:before, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev:hover:before {\\n  filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8)) brightness(0) saturate(100%) invert(29%) sepia(84%) saturate(1736%) hue-rotate(231deg) brightness(94%) contrast(94%) !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next:active, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev:active {\\n  transform: scale(1.05) translateY(0) !important;\\n  transition-duration: 0.1s !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next.swiper-button-disabled, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev.swiper-button-disabled {\\n  opacity: 0.3 !important;\\n  cursor: not-allowed !important;\\n  pointer-events: none !important;\\n  transform: none !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next.swiper-button-disabled:hover, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev.swiper-button-disabled:hover {\\n  transform: none !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev {\\n  left: 4px !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.5 2.5L4.5 6L7.5 9.5' stroke='%236b7280' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev:hover:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.5 2.5L4.5 6L7.5 9.5' stroke='%234f46e5' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next {\\n  right: 4px !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.5 2.5L7.5 6L4.5 9.5' stroke='%236b7280' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n.stories-swiper[_ngcontent-%COMP%]     .swiper-button-next:hover:before {\\n  \\n\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.5 2.5L7.5 6L4.5 9.5' stroke='%234f46e5' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\\\") !important;\\n}\\n@media (max-width: 768px) {\\n  .stories-swiper[_ngcontent-%COMP%]     .swiper-button-next, .stories-swiper[_ngcontent-%COMP%]     .swiper-button-prev {\\n    display: none !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-swiper[_ngcontent-%COMP%] {\\n    padding: 0 20px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-swiper[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n  }\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n}\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: white;\\n  transform: scale(1.1);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.nav-arrow.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.nav-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n}\\n@media (max-width: 768px) {\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  flex-shrink: 0;\\n  width: 100%;\\n  box-sizing: border-box;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  padding: 3px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.story-avatar.has-products[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b35, #f7931e, #ff6b35, #f7931e);\\n  background-size: 200% 200%;\\n  animation: _ngcontent-%COMP%_gradientShift 3s ease-in-out infinite;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n}\\n.story-avatar.has-products[_ngcontent-%COMP%]:hover {\\n  animation-duration: 1.5s;\\n  transform: scale(1.05);\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientShift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    width: 54px;\\n    height: 54px;\\n  }\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n\\n\\n.shopping-bag-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -4px;\\n  right: -4px;\\n  background: linear-gradient(135deg, #ff6b35, #e55a2b); \\n\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  box-shadow: 0 3px 12px rgba(255, 107, 53, 0.3), 0 1px 4px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  z-index: 3;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.15) translateY(-1px);\\n  background: linear-gradient(135deg, #ff7849, #ff6b35);\\n  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4), 0 3px 8px rgba(0, 0, 0, 0.15);\\n  border-color: rgba(255, 255, 255, 0.9);\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]:active {\\n  transform: scale(1.05) translateY(0);\\n  transition-duration: 0.1s;\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 11px;\\n  font-weight: 700;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-indicator[_ngcontent-%COMP%] {\\n    width: 22px;\\n    height: 22px;\\n    bottom: -3px;\\n    right: -3px;\\n    border-width: 2px;\\n  }\\n  .shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .shopping-bag-indicator[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n    bottom: -2px;\\n    right: -2px;\\n    border-width: 1.5px;\\n  }\\n  .shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: white;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: 80px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n  }\\n}\\n\\n\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #0095f6;\\n  font-weight: 600;\\n  text-shadow: none;\\n}\\n\\n.product-count-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -8px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  white-space: nowrap;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .product-count-badge[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 1px 4px;\\n  }\\n}\\n\\n.story-count-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #8e8e8e;\\n  margin-top: 2px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f9f9f9, #e8e8e8);\\n  border: 2px dashed #c7c7c7;\\n  position: relative;\\n}\\n.add-avatar[_ngcontent-%COMP%]:hover {\\n  border-color: #0095f6;\\n  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);\\n}\\n.add-avatar[_ngcontent-%COMP%]   .story-avatar-img[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n  filter: grayscale(20%);\\n}\\n\\n\\n\\n.add-story-plus-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -4px;\\n  right: -4px;\\n  width: 24px;\\n  height: 24px;\\n  background: linear-gradient(135deg, #0095f6, #0077cc); \\n\\n  border: 2px solid white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 3;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: 0 3px 12px rgba(0, 149, 246, 0.3), 0 1px 4px rgba(0, 0, 0, 0.1);\\n}\\n.add-story-plus-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.15) translateY(-1px);\\n  background: linear-gradient(135deg, #1da1f2, #0095f6);\\n  box-shadow: 0 6px 20px rgba(0, 149, 246, 0.4), 0 3px 8px rgba(0, 0, 0, 0.15);\\n  border-color: rgba(255, 255, 255, 0.9);\\n}\\n.add-story-plus-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(1.05) translateY(0);\\n  transition-duration: 0.1s;\\n}\\n.add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 11px;\\n  font-weight: 700;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n@media (max-width: 768px) {\\n  .add-story-plus-btn[_ngcontent-%COMP%] {\\n    width: 22px;\\n    height: 22px;\\n    bottom: -3px;\\n    right: -3px;\\n    border-width: 2px;\\n  }\\n  .add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .add-story-plus-btn[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n    bottom: -2px;\\n    right: -2px;\\n    border-width: 1.5px;\\n  }\\n  .add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n}\\n\\n\\n\\n.stories-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95));\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-overlay[_ngcontent-%COMP%] {\\n    background: #000;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n.stories-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 90%;\\n  max-width: 400px;\\n  height: 80vh;\\n  max-height: 700px;\\n  background: #000;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100vh;\\n    max-height: none;\\n    border-radius: 0;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(50px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n.progress-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 4px;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 3px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n  overflow: hidden;\\n}\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progressFill 5s linear;\\n}\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  border-radius: 2px;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progressFill {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story-header[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);\\n  color: #fff;\\n  z-index: 10;\\n}\\n@media (max-width: 768px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n\\n.story-header-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-header-avatar[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n\\n.story-header-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 2px;\\n}\\n@media (max-width: 768px) {\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-weight: 400;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #fff;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n@media (max-width: 768px) {\\n  .close-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  background: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n\\n.story-middle-click-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 5;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.story-middle-click-area[_ngcontent-%COMP%]:hover {\\n  transform: translate(-50%, -50%) scale(1.1);\\n}\\n\\n.middle-click-indicator[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  animation: _ngcontent-%COMP%_pulseIndicator 2s infinite;\\n}\\n.middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.middle-click-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n@media (max-width: 768px) {\\n  .middle-click-indicator[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    font-size: 12px;\\n  }\\n  .middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulseIndicator {\\n  0% {\\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  }\\n}\\n.story-image[_ngcontent-%COMP%], .story-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.product-tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 3px solid #667eea;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_ripple 2s infinite;\\n}\\n.product-tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #667eea;\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_ripple {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);\\n  }\\n}\\n.product-tag-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  min-width: 200px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.product-tag-image[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.product-tag-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 14px;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #667eea;\\n  font-weight: 700;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.product-tag-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n  justify-content: flex-start;\\n}\\n\\n.product-action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: #333;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.product-action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.product-action-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.product-action-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n}\\n.product-action-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: #f5576c;\\n  color: white;\\n}\\n.product-action-btn.buy-btn[_ngcontent-%COMP%]:hover {\\n  background: #28a745;\\n  color: white;\\n}\\n.product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n@media (max-width: 768px) {\\n  .product-action-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.shopping-bag-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  right: 20px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 56px;\\n  height: 56px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\\n  z-index: 5;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.8);\\n  outline-offset: 2px;\\n}\\n.shopping-bag-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  animation: _ngcontent-%COMP%_bounce 0.6s ease;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: #f5576c;\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  border: 2px solid white;\\n  min-width: 24px;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n    bottom: 16px;\\n    right: 16px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    font-size: 10px;\\n    min-width: 20px;\\n    top: -6px;\\n    right: -6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n    bottom: 12px;\\n    right: 12px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n    font-size: 9px;\\n    min-width: 18px;\\n    top: -5px;\\n    right: -5px;\\n  }\\n}\\n@media (max-height: 600px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    right: 10px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-10px);\\n  }\\n  80% {\\n    transform: translateY(-5px);\\n  }\\n}\\n.story-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  left: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  z-index: 5;\\n}\\n@media (max-width: 768px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 16px;\\n    left: 16px;\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 12px;\\n    left: 12px;\\n    gap: 10px;\\n  }\\n}\\n@media (max-width: 768px) and (max-height: 600px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    left: 10px;\\n    gap: 8px;\\n  }\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n}\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.action-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.action-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.6);\\n  outline-offset: 2px;\\n}\\n.action-btn.liked[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  color: white;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n@media (max-width: 768px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    min-width: 44px;\\n    min-height: 44px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n\\n\\n.stories-content[_ngcontent-%COMP%]   .story-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: rgba(255, 255, 255, 0.15);\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  border: 1px solid rgba(255, 255, 255, 0.25);\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  color: white;\\n  z-index: 15;\\n  opacity: 0.8;\\n}\\n.stories-content[_ngcontent-%COMP%]   .story-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.25);\\n  transform: translateY(-50%) scale(1.1);\\n  opacity: 1;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n}\\n.stories-content[_ngcontent-%COMP%]   .story-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(1.05);\\n  transition-duration: 0.1s;\\n}\\n.stories-content[_ngcontent-%COMP%]   .story-nav-btn.story-nav-prev[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.stories-content[_ngcontent-%COMP%]   .story-nav-btn.story-nav-next[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n.stories-content[_ngcontent-%COMP%]   .story-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .stories-content[_ngcontent-%COMP%]   .story-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n    padding: 8px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .stories-content[_ngcontent-%COMP%]   .shopping-bag-btn[_ngcontent-%COMP%]:not(:only-child) {\\n    right: 16px;\\n    bottom: 80px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 4px;\\n    padding: 8px;\\n    border-radius: 8px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n    padding: 6px;\\n    bottom: 20px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n}\\n@media (max-width: 768px) and (orientation: landscape) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    height: 100vh;\\n    max-height: none;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    right: 10px;\\n  }\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    left: 10px;\\n    gap: 8px;\\n  }\\n}\\n.story-item.loading[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .shopping-bag-btn[_ngcontent-%COMP%]:focus, .nav-arrow[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #667eea;\\n  outline-offset: 2px;\\n}\\n\\n@media (prefers-contrast: high) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    border: 2px solid #000;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    border: 2px solid #fff;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "register", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "userGroup_r4", "totalProducts", "ɵɵlistener", "ViewAddStoriesComponent_swiper_slide_26_Template_div_click_1_listener", "ctx_r2", "ɵɵrestoreView", "_r2", "$implicit", "i_r5", "index", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "openUserStories", "ɵɵtemplate", "ViewAddStoriesComponent_swiper_slide_26_div_6_Template", "ViewAddStoriesComponent_swiper_slide_26_div_11_Template", "ɵɵclassProp", "hasProducts", "ɵɵproperty", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "stories", "length", "i_r8", "currentStoryIndex", "ɵɵstyleProp", "getProgressWidth", "tmp_4_0", "getCurrentStory", "mediaUrl", "tmp_5_0", "ViewAddStoriesComponent_div_27_div_17_Template_div_click_0_listener", "$event", "_r9", "handleMiddleAreaClick", "ɵɵpropertyInterpolate1", "linkedContent", "type", "getLinkedContentText", "ViewAddStoriesComponent_div_27_div_18_div_1_Template_div_click_0_listener", "productTag_r11", "_r10", "product", "openProductDetails", "stopPropagation", "ViewAddStoriesComponent_div_27_div_18_div_1_Template_button_click_10_listener", "addToCart", "ViewAddStoriesComponent_div_27_div_18_div_1_Template_button_click_12_listener", "addToWishlist", "ViewAddStoriesComponent_div_27_div_18_div_1_Template_button_click_14_listener", "buyNow", "position", "x", "y", "images", "url", "name", "ɵɵtextInterpolate1", "price", "ViewAddStoriesComponent_div_27_div_18_div_1_Template", "products", "ViewAddStoriesComponent_div_27_button_19_Template_button_click_0_listener", "_r12", "toggleProductTags", "showProductTags", "getProductCount", "ViewAddStoriesComponent_div_27_button_27_Template_button_click_0_listener", "_r13", "previousStory", "ViewAddStoriesComponent_div_27_button_28_Template_button_click_0_listener", "_r14", "nextStory", "ViewAddStoriesComponent_div_27_div_4_Template", "ViewAddStoriesComponent_div_27_Template_button_click_12_listener", "_r7", "closeStories", "ViewAddStoriesComponent_div_27_Template_div_click_14_listener", "onStoryClick", "ViewAddStoriesComponent_div_27_Template_div_touchstart_14_listener", "onTouchStart", "ViewAddStoriesComponent_div_27_Template_div_touchmove_14_listener", "onTouchMove", "ViewAddStoriesComponent_div_27_Template_div_touchend_14_listener", "onTouchEnd", "ViewAddStoriesComponent_div_27_img_15_Template", "ViewAddStoriesComponent_div_27_video_16_Template", "ViewAddStoriesComponent_div_27_div_17_Template", "ViewAddStoriesComponent_div_27_div_18_Template", "ViewAddStoriesComponent_div_27_button_19_Template", "ViewAddStoriesComponent_div_27_Template_button_click_21_listener", "toggleLike", "ViewAddStoriesComponent_div_27_Template_button_click_23_listener", "shareStory", "ViewAddStoriesComponent_div_27_Template_button_click_25_listener", "saveStory", "ViewAddStoriesComponent_div_27_button_27_Template", "ViewAddStoriesComponent_div_27_button_28_Template", "getTimeAgo", "tmp_6_0", "createdAt", "tmp_7_0", "mediaType", "tmp_8_0", "tmp_9_0", "tmp_10_0", "isLiked", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "cartService", "wishlistService", "socialMediaService", "realtimeService", "cdr", "buttonActionsService", "currentUser", "isLoadingStories", "isOpen", "currentUserIndex", "currentUserStories", "storyProgress", "canScrollLeft", "canScrollRight", "showNavArrows", "translateX", "itemWidth", "visibleItems", "currentSlideIndex", "storyDuration", "progressStartTime", "currentProgress", "touchStartTime", "swiperConfig", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "navigation", "nextEl", "prevEl", "autoplay", "delay", "disableOnInteraction", "pauseOnMouseEnter", "loop", "breakpoints", "isAutoScrollEnabled", "subscriptions", "console", "log", "ngOnInit", "loadStories", "currentUser$", "subscribe", "updateNavArrowsVisibility", "toggleAutoScroll", "swiperEl", "document", "querySelector", "swiper", "start", "stop", "getAutoScrollIcon", "getAutoScrollTitle", "ngAfterViewInit", "setTimeout", "calculateSliderDimensions", "updateScrollButtons", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "clearStoryTimer", "onResize", "window", "innerWidth", "storiesSlider", "nativeElement", "containerWidth", "offsetWidth", "screenWidth", "warn", "push", "get", "apiUrl", "next", "response", "success", "allStories", "filter", "story", "isActive", "map", "media", "groupStoriesByUser", "loadFallbackStories", "error", "userStoriesMap", "Map", "userId", "_id", "has", "set", "latestStoryTime", "userGroup", "Date", "Array", "from", "values", "sort", "a", "b", "getTime", "scrollLeft", "updateSliderPosition", "scrollRight", "totalItems", "maxSlideIndex", "Math", "max", "currentIndex", "newTranslateX", "userIndex", "startStoryTimer", "body", "style", "overflow", "openStory", "_story", "pauseAllVideos", "getCurrentUser", "now", "progressUpdateTimer", "setInterval", "elapsed", "min", "detectChanges", "progressTimer", "clearTimeout", "clearInterval", "event", "clickX", "clientX", "windowWidth", "_event", "longPressTimer", "touchDuration", "touch", "changedTouches", "navigate", "productId", "size", "color", "quantity", "addedFrom", "result", "message", "isAuthenticated", "currentStory", "endpoint", "post", "isConnected", "storyId", "currentUserValue", "liked", "navigator", "share", "title", "text", "caption", "frontendUrl", "then", "trackStoryShare", "catch", "shareUrl", "clipboard", "writeText", "showToast", "categoryId", "queryParams", "category", "brandId", "brand", "collectionId", "trackLinkedContentClick", "contentType", "contentId", "toast", "createElement", "textContent", "cssText", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onAdd", "dateString", "date", "isNaN", "diffInMinutes", "floor", "diffInHours", "diffInDays", "videos", "querySelectorAll", "video", "pause", "handleKeydown", "key", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "i4", "CartService", "i5", "WishlistService", "i6", "SocialMediaService", "i7", "RealtimeService", "ChangeDetectorRef", "i8", "ButtonActionsService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_Template_button_click_6_listener", "_r1", "ViewAddStoriesComponent_Template_button_click_8_listener", "ViewAddStoriesComponent_Template_div_click_14_listener", "ViewAddStoriesComponent_swiper_slide_25_Template", "ViewAddStoriesComponent_swiper_slide_26_Template", "ViewAddStoriesComponent_div_27_Template", "ɵɵclassMap", "i9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON>ef, ViewChild, HostListener, AfterViewInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Subscription } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CartService } from 'src/app/core/services/cart.service';\r\nimport { WishlistService } from 'src/app/core/services/wishlist.service';\r\nimport { SocialMediaService } from 'src/app/core/services/social-media.service';\r\nimport { RealtimeService } from 'src/app/core/services/realtime.service';\r\nimport { ButtonActionsService } from 'src/app/core/services/button-actions.service';\r\nimport { register } from 'swiper/element/bundle';\r\n\r\ninterface Story {\r\n  _id: string;\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  media: {\r\n    type: 'image' | 'video';\r\n    url: string;\r\n    thumbnail?: string;\r\n    duration?: number;\r\n  };\r\n  mediaUrl: string; // For backward compatibility\r\n  mediaType: 'image' | 'video'; // For backward compatibility\r\n  caption?: string;\r\n  createdAt: string;\r\n  expiresAt: string;\r\n  views: number;\r\n  isActive: boolean;\r\n  linkedContent?: {\r\n    type: 'product' | 'category' | 'brand' | 'collection';\r\n    productId?: string;\r\n    categoryId?: string;\r\n    brandId?: string;\r\n    collectionId?: string;\r\n  };\r\n  products?: Array<{\r\n    _id: string;\r\n    product: {\r\n      _id: string;\r\n      name: string;\r\n      price: number;\r\n      images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;\r\n    };\r\n    position?: {\r\n      x: number;\r\n      y: number;\r\n    };\r\n    size?: string;\r\n    color?: string;\r\n  }>;\r\n}\r\n\r\ninterface UserStoryGroup {\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  stories: Story[];\r\n  hasProducts: boolean;\r\n  totalProducts: number;\r\n  latestStoryTime: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-view-add-stories',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n  templateUrl: './view-add-stories.component.html',\r\n  styleUrls: ['./view-add-stories.component.scss'],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\r\n})\r\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storiesTrack', { static: false }) storiesTrack!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storyVideo', { static: false }) storyVideo!: ElementRef<HTMLVideoElement>;\r\n\r\n  currentUser: any = null;\r\n  stories: UserStoryGroup[] = [];\r\n  isLoadingStories = true;\r\n\r\n  // Story viewer state\r\n  isOpen = false;\r\n  currentStoryIndex = 0;\r\n  currentUserIndex = 0;\r\n  currentUserStories: Story[] = [];\r\n  showProductTags = false;\r\n  isLiked = false;\r\n  storyTimer: any;\r\n  storyProgress = 0;\r\n\r\n  // Navigation state\r\n  canScrollLeft = false;\r\n  canScrollRight = false;\r\n  showNavArrows = true;\r\n\r\n  // Custom slider properties\r\n  translateX = 0;\r\n  itemWidth = 80; // Width of each story item including margin\r\n  visibleItems = 6; // Number of visible items\r\n  currentSlideIndex = 0;\r\n\r\n  // Progress tracking\r\n  private progressTimer: any;\r\n  private progressUpdateTimer: any;\r\n  private storyDuration = 15000; // 15 seconds default\r\n  private progressStartTime = 0;\r\n  currentProgress = 0; // Stable progress value for template binding\r\n\r\n  // Touch handling\r\n  private touchStartTime = 0;\r\n  private longPressTimer: any;\r\n\r\n  // Swiper configuration (adjusted for stories only, no add story slide)\r\n  swiperConfig = {\r\n    slidesPerView: 5,\r\n    spaceBetween: 16,\r\n    navigation: {\r\n      nextEl: '.swiper-button-next',\r\n      prevEl: '.swiper-button-prev',\r\n    },\r\n    autoplay: {\r\n      delay: 3000,\r\n      disableOnInteraction: false,\r\n      pauseOnMouseEnter: true\r\n    },\r\n    loop: true,\r\n    breakpoints: {\r\n      0: {\r\n        slidesPerView: 2,\r\n        spaceBetween: 12\r\n      },\r\n      600: {\r\n        slidesPerView: 3,\r\n        spaceBetween: 14\r\n      },\r\n      900: {\r\n        slidesPerView: 5,\r\n        spaceBetween: 16\r\n      }\r\n    }\r\n  };\r\n\r\n  // Auto-scroll control\r\n  isAutoScrollEnabled = true;\r\n\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private authService: AuthService,\r\n    private cartService: CartService,\r\n    private wishlistService: WishlistService,\r\n    private socialMediaService: SocialMediaService,\r\n    private realtimeService: RealtimeService,\r\n    private cdr: ChangeDetectorRef,\r\n    private buttonActionsService: ButtonActionsService\r\n  ) {\r\n    console.log('🏗️ ViewAddStoriesComponent constructor called');\r\n  }\r\n\r\n  ngOnInit() {\r\n    console.log('🚀 ViewAddStoriesComponent ngOnInit called');\r\n\r\n    // Register Swiper custom elements\r\n    register();\r\n\r\n    this.loadStories();\r\n    this.authService.currentUser$.subscribe(user => {\r\n      this.currentUser = user;\r\n      console.log('👤 Current user updated:', user);\r\n    });\r\n\r\n    // Check if we should show navigation arrows based on screen size\r\n    this.updateNavArrowsVisibility();\r\n  }\r\n\r\n  // Toggle auto-scroll functionality\r\n  toggleAutoScroll() {\r\n    this.isAutoScrollEnabled = !this.isAutoScrollEnabled;\r\n    const swiperEl = document.querySelector('.stories-swiper') as any;\r\n\r\n    if (swiperEl && swiperEl.swiper) {\r\n      if (this.isAutoScrollEnabled) {\r\n        swiperEl.swiper.autoplay.start();\r\n      } else {\r\n        swiperEl.swiper.autoplay.stop();\r\n      }\r\n    }\r\n  }\r\n\r\n  // Get auto-scroll button icon\r\n  getAutoScrollIcon(): string {\r\n    return this.isAutoScrollEnabled ? 'fas fa-pause' : 'fas fa-play';\r\n  }\r\n\r\n  // Get auto-scroll button title\r\n  getAutoScrollTitle(): string {\r\n    return this.isAutoScrollEnabled ? 'Pause auto-scroll' : 'Start auto-scroll';\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    console.log('ngAfterViewInit called');\r\n    setTimeout(() => {\r\n      console.log('Initializing slider after view init');\r\n      this.calculateSliderDimensions();\r\n      this.updateScrollButtons();\r\n    }, 500);\r\n\r\n    // Also try immediate initialization\r\n    setTimeout(() => {\r\n      if (this.stories.length > 0) {\r\n        console.log('Re-initializing slider with stories');\r\n        this.calculateSliderDimensions();\r\n        this.updateScrollButtons();\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n    this.clearStoryTimer();\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize() {\r\n    this.updateNavArrowsVisibility();\r\n    this.calculateSliderDimensions();\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  private updateNavArrowsVisibility() {\r\n    this.showNavArrows = window.innerWidth > 768;\r\n  }\r\n\r\n  private calculateSliderDimensions() {\r\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\r\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\r\n      const screenWidth = window.innerWidth;\r\n\r\n      console.log('Calculating slider dimensions:', { containerWidth, screenWidth });\r\n\r\n      // Responsive visible items\r\n      if (screenWidth <= 600) {\r\n        this.visibleItems = 3;\r\n        this.itemWidth = containerWidth / 3;\r\n      } else if (screenWidth <= 900) {\r\n        this.visibleItems = 4;\r\n        this.itemWidth = containerWidth / 4;\r\n      } else {\r\n        this.visibleItems = 6;\r\n        this.itemWidth = containerWidth / 6;\r\n      }\r\n\r\n      console.log('Slider dimensions calculated:', {\r\n        visibleItems: this.visibleItems,\r\n        itemWidth: this.itemWidth\r\n      });\r\n\r\n      // Reset slider position if needed\r\n      this.currentSlideIndex = 0;\r\n      this.translateX = 0;\r\n    } else {\r\n      console.warn('Stories slider element not found, using default dimensions');\r\n      // Fallback dimensions\r\n      this.itemWidth = 100;\r\n      this.visibleItems = 6;\r\n    }\r\n  }\r\n\r\n  loadStories() {\r\n    this.isLoadingStories = true;\r\n    this.subscriptions.push(\r\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\r\n        next: (response) => {\r\n          console.log('Stories API response:', response);\r\n          if (response.success && response.stories && response.stories.length > 0) {\r\n            // Filter only active stories and map the data structure\r\n            const allStories = response.stories\r\n              .filter((story: any) => story.isActive)\r\n              .map((story: any) => ({\r\n                ...story,\r\n                mediaUrl: story.media?.url || story.mediaUrl,\r\n                mediaType: story.media?.type || story.mediaType\r\n              }));\r\n\r\n            // Group stories by user (Instagram style)\r\n            this.stories = this.groupStoriesByUser(allStories);\r\n            console.log('Loaded and grouped stories from API:', this.stories);\r\n            console.log('Total user story groups:', this.stories.length);\r\n          } else {\r\n            console.log('No stories from API, loading fallback stories');\r\n            this.loadFallbackStories();\r\n          }\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading stories:', error);\r\n          console.log('Loading fallback stories due to error');\r\n          this.loadFallbackStories();\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  // Group stories by user like Instagram\r\n  private groupStoriesByUser(allStories: Story[]): UserStoryGroup[] {\r\n    const userStoriesMap = new Map();\r\n\r\n    allStories.forEach(story => {\r\n      const userId = story.user._id;\r\n      if (!userStoriesMap.has(userId)) {\r\n        userStoriesMap.set(userId, {\r\n          user: story.user,\r\n          stories: [],\r\n          hasProducts: false,\r\n          totalProducts: 0,\r\n          latestStoryTime: story.createdAt\r\n        });\r\n      }\r\n\r\n      const userGroup = userStoriesMap.get(userId);\r\n      userGroup.stories.push(story);\r\n\r\n      // Check if any story has products\r\n      if (story.products && story.products.length > 0) {\r\n        userGroup.hasProducts = true;\r\n        userGroup.totalProducts += story.products.length;\r\n      }\r\n\r\n      // Keep track of latest story time for sorting\r\n      if (new Date(story.createdAt) > new Date(userGroup.latestStoryTime)) {\r\n        userGroup.latestStoryTime = story.createdAt;\r\n      }\r\n    });\r\n\r\n    // Convert map to array and sort by latest story time\r\n    return Array.from(userStoriesMap.values())\r\n      .sort((a, b) => new Date(b.latestStoryTime).getTime() - new Date(a.latestStoryTime).getTime());\r\n  }\r\n\r\n  loadFallbackStories() {\r\n    console.log('❌ No stories available from API');\r\n    this.stories = [];\r\n  }\r\n\r\n  // Navigation methods\r\n  scrollLeft() {\r\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\r\n    if (this.currentSlideIndex > 0) {\r\n      this.currentSlideIndex--;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled left to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll left, already at start');\r\n    }\r\n  }\r\n\r\n  scrollRight() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    console.log('Scroll right clicked:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      visibleItems: this.visibleItems\r\n    });\r\n\r\n    if (this.currentSlideIndex < maxSlideIndex) {\r\n      this.currentSlideIndex++;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled right to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll right, already at end');\r\n    }\r\n  }\r\n\r\n  private updateSliderPosition() {\r\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\r\n    console.log('Updating slider position:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      itemWidth: this.itemWidth,\r\n      newTranslateX\r\n    });\r\n\r\n    this.translateX = newTranslateX;\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  updateScrollButtons() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    this.canScrollLeft = this.currentSlideIndex > 0;\r\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\r\n\r\n    console.log('Updated scroll buttons:', {\r\n      canScrollLeft: this.canScrollLeft,\r\n      canScrollRight: this.canScrollRight,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      currentIndex: this.currentSlideIndex\r\n    });\r\n  }\r\n\r\n  // Story viewer methods\r\n  openUserStories(userGroup: UserStoryGroup, userIndex: number) {\r\n    this.currentUserIndex = userIndex;\r\n    this.currentStoryIndex = 0; // Start with first story of this user\r\n    this.currentUserStories = userGroup.stories;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n    console.log('Opening stories for user:', userGroup.user.username, 'Stories count:', userGroup.stories.length);\r\n  }\r\n\r\n  openStory(_story: Story, index: number) {\r\n    // Legacy method - keeping for compatibility\r\n    this.currentStoryIndex = index;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n  }\r\n\r\n  closeStories() {\r\n    this.isOpen = false;\r\n    this.clearStoryTimer();\r\n    this.pauseAllVideos();\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  nextStory() {\r\n    // First check if there are more stories for current user\r\n    if (this.currentStoryIndex < this.currentUserStories.length - 1) {\r\n      this.currentStoryIndex++;\r\n      this.showProductTags = false;\r\n      this.startStoryTimer();\r\n    } else {\r\n      // Move to next user's stories\r\n      if (this.currentUserIndex < this.stories.length - 1) {\r\n        this.currentUserIndex++;\r\n        this.currentStoryIndex = 0;\r\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\r\n        this.showProductTags = false;\r\n        this.startStoryTimer();\r\n      } else {\r\n        this.closeStories();\r\n      }\r\n    }\r\n  }\r\n\r\n  previousStory() {\r\n    // First check if there are previous stories for current user\r\n    if (this.currentStoryIndex > 0) {\r\n      this.currentStoryIndex--;\r\n      this.showProductTags = false;\r\n      this.startStoryTimer();\r\n    } else {\r\n      // Move to previous user's stories (last story)\r\n      if (this.currentUserIndex > 0) {\r\n        this.currentUserIndex--;\r\n        this.currentUserStories = this.stories[this.currentUserIndex].stories;\r\n        this.currentStoryIndex = this.currentUserStories.length - 1;\r\n        this.showProductTags = false;\r\n        this.startStoryTimer();\r\n      } else {\r\n        this.closeStories();\r\n      }\r\n    }\r\n  }\r\n\r\n  getCurrentStory(): Story | null {\r\n    if (this.currentUserStories && this.currentUserStories.length > 0) {\r\n      return this.currentUserStories[this.currentStoryIndex] || this.currentUserStories[0];\r\n    }\r\n    // Fallback for legacy usage - get first story from first user group\r\n    if (this.stories && this.stories.length > 0 && this.stories[0].stories.length > 0) {\r\n      return this.stories[0].stories[0];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  getCurrentUser(): any {\r\n    if (this.stories && this.stories.length > 0) {\r\n      return this.stories[this.currentUserIndex]?.user;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  hasProducts(): boolean {\r\n    const story = this.getCurrentStory();\r\n    return !!(story && story.products && story.products.length > 0);\r\n  }\r\n\r\n  getProductCount(): number {\r\n    const story = this.getCurrentStory();\r\n    return story?.products?.length || 0;\r\n  }\r\n\r\n  // Progress tracking\r\n  getProgressWidth(index: number): number {\r\n    if (index < this.currentStoryIndex) return 100;\r\n    if (index > this.currentStoryIndex) return 0;\r\n\r\n    // Return the stable progress value for the current story\r\n    return this.currentProgress;\r\n  }\r\n\r\n  private startStoryTimer() {\r\n    this.clearStoryTimer();\r\n    this.progressStartTime = Date.now();\r\n    this.currentProgress = 0;\r\n\r\n    // Update progress every 100ms for smooth animation\r\n    this.progressUpdateTimer = setInterval(() => {\r\n      if (this.progressStartTime) {\r\n        const elapsed = Date.now() - this.progressStartTime;\r\n        this.currentProgress = Math.min((elapsed / this.storyDuration) * 100, 100);\r\n        this.cdr.detectChanges(); // Trigger change detection manually\r\n      }\r\n    }, 100);\r\n\r\n    this.progressTimer = setTimeout(() => {\r\n      this.nextStory();\r\n    }, this.storyDuration);\r\n  }\r\n\r\n  private clearStoryTimer() {\r\n    if (this.progressTimer) {\r\n      clearTimeout(this.progressTimer);\r\n      this.progressTimer = null;\r\n    }\r\n    if (this.progressUpdateTimer) {\r\n      clearInterval(this.progressUpdateTimer);\r\n      this.progressUpdateTimer = null;\r\n    }\r\n    this.progressStartTime = 0;\r\n    this.currentProgress = 0;\r\n  }\r\n\r\n  // Story interaction methods\r\n  onStoryClick(event: MouseEvent) {\r\n    const clickX = event.clientX;\r\n    const windowWidth = window.innerWidth;\r\n\r\n    if (clickX < windowWidth / 3) {\r\n      this.previousStory();\r\n    } else if (clickX > (windowWidth * 2) / 3) {\r\n      this.nextStory();\r\n    }\r\n  }\r\n\r\n  // Touch handling\r\n  onTouchStart(_event: TouchEvent) {\r\n    this.touchStartTime = Date.now();\r\n    this.longPressTimer = setTimeout(() => {\r\n      this.clearStoryTimer(); // Pause story progress on long press\r\n    }, 500);\r\n  }\r\n\r\n  onTouchMove(_event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n  }\r\n\r\n  onTouchEnd(event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n\r\n    const touchDuration = Date.now() - this.touchStartTime;\r\n    if (touchDuration < 500) {\r\n      // Short tap - treat as click\r\n      const touch = event.changedTouches[0];\r\n      this.onStoryClick({ clientX: touch.clientX } as MouseEvent);\r\n    } else {\r\n      // Long press ended - resume story progress\r\n      this.startStoryTimer();\r\n    }\r\n  }\r\n\r\n  // Product interaction methods\r\n  toggleProductTags() {\r\n    this.showProductTags = !this.showProductTags;\r\n  }\r\n\r\n  openProductDetails(product: any) {\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  // Add product to cart with real-time functionality\r\n  addToCart(product: any) {\r\n    console.log('Adding product to cart:', product);\r\n\r\n    this.buttonActionsService.addToCart({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      quantity: 1,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Product added to cart successfully:', result);\r\n          // Show success message or toast\r\n        } else {\r\n          console.error('Failed to add product to cart:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to cart:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Add product to wishlist with real-time functionality\r\n  addToWishlist(product: any) {\r\n    console.log('Adding product to wishlist:', product);\r\n\r\n    this.buttonActionsService.addToWishlist({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Product added to wishlist successfully:', result);\r\n          // Show success message or toast\r\n        } else {\r\n          console.error('Failed to add product to wishlist:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to wishlist:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Buy now functionality\r\n  buyNow(product: any) {\r\n    console.log('Buy now clicked for product:', product);\r\n\r\n    this.buttonActionsService.buyNow({\r\n      productId: product._id,\r\n      size: product.size,\r\n      color: product.color,\r\n      quantity: 1,\r\n      addedFrom: 'story'\r\n    }).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          console.log('Buy now successful:', result);\r\n          // Navigation to checkout will be handled by the service\r\n        } else {\r\n          console.error('Failed to buy now:', result.message);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error in buy now:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Story actions with real-time functionality\r\n  toggleLike() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    this.isLiked = !this.isLiked;\r\n\r\n    // Call API to like/unlike story\r\n    const endpoint = this.isLiked ? 'like' : 'unlike';\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log(`Story ${endpoint}d successfully:`, response);\r\n        // Emit real-time event (using socket directly)\r\n        if (this.realtimeService.isConnected()) {\r\n          // For now, we'll track this locally until we add story-specific emit methods\r\n          console.log('Story liked event:', {\r\n            storyId: currentStory._id,\r\n            userId: this.authService.currentUserValue?._id,\r\n            liked: this.isLiked\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error(`Error ${endpoint}ing story:`, error);\r\n        // Revert the like state on error\r\n        this.isLiked = !this.isLiked;\r\n      }\r\n    });\r\n  }\r\n\r\n  shareStory() {\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    if (navigator.share) {\r\n      // Use native sharing if available\r\n      navigator.share({\r\n        title: `Story by ${currentStory.user.username}`,\r\n        text: currentStory.caption,\r\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\r\n      }).then(() => {\r\n        console.log('Story shared successfully');\r\n        // Track share event\r\n        this.trackStoryShare(currentStory._id);\r\n      }).catch((error) => {\r\n        console.error('Error sharing story:', error);\r\n      });\r\n    } else {\r\n      // Fallback: copy link to clipboard\r\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\r\n      navigator.clipboard.writeText(shareUrl).then(() => {\r\n        console.log('Story link copied to clipboard');\r\n        this.trackStoryShare(currentStory._id);\r\n        // Show toast notification\r\n        this.showToast('Story link copied to clipboard!');\r\n      }).catch((error) => {\r\n        console.error('Error copying to clipboard:', error);\r\n      });\r\n    }\r\n  }\r\n\r\n  saveStory() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n    if (!currentStory) return;\r\n\r\n    // Call API to save story\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log('Story saved successfully:', response);\r\n        this.showToast('Story saved to your collection!');\r\n        // Emit real-time event (using socket directly)\r\n        if (this.realtimeService.isConnected()) {\r\n          // For now, we'll track this locally until we add story-specific emit methods\r\n          console.log('Story saved event:', {\r\n            storyId: currentStory._id,\r\n            userId: this.authService.currentUserValue?._id\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error saving story:', error);\r\n        this.showToast('Error saving story. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private trackStoryShare(storyId: string) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\r\n        next: (response) => {\r\n          console.log('Story share tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking story share:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Handle middle area click for product/category navigation\r\n  handleMiddleAreaClick(event: Event) {\r\n    event.stopPropagation();\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return;\r\n    }\r\n\r\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        if (currentStory.linkedContent.productId) {\r\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\r\n        } else if (currentStory.products && currentStory.products.length > 0) {\r\n          // Fallback to first product if no specific productId\r\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\r\n        }\r\n        break;\r\n\r\n      case 'category':\r\n        if (currentStory.linkedContent.categoryId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { category: currentStory.linkedContent.categoryId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'brand':\r\n        if (currentStory.linkedContent.brandId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { brand: currentStory.linkedContent.brandId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'collection':\r\n        if (currentStory.linkedContent.collectionId) {\r\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\r\n        }\r\n        break;\r\n\r\n      default:\r\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\r\n    }\r\n\r\n    // Track click event\r\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\r\n  }\r\n\r\n  // Get text for linked content indicator\r\n  getLinkedContentText(): string {\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return '';\r\n    }\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        return 'View Product';\r\n      case 'category':\r\n        return 'Browse Category';\r\n      case 'brand':\r\n        return 'View Brand';\r\n      case 'collection':\r\n        return 'View Collection';\r\n      default:\r\n        return 'View Details';\r\n    }\r\n  }\r\n\r\n  // Track linked content click for analytics\r\n  private trackLinkedContentClick(storyId: string, linkedContent: any) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\r\n        contentType: linkedContent.type,\r\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\r\n      }).subscribe({\r\n        next: (response) => {\r\n          console.log('Linked content click tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking linked content click:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private showToast(message: string) {\r\n    // Simple toast implementation - you can replace with your preferred toast library\r\n    const toast = document.createElement('div');\r\n    toast.textContent = message;\r\n    toast.style.cssText = `\r\n      position: fixed;\r\n      bottom: 20px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: white;\r\n      padding: 12px 24px;\r\n      border-radius: 8px;\r\n      z-index: 10000;\r\n      font-size: 14px;\r\n    `;\r\n    document.body.appendChild(toast);\r\n\r\n    setTimeout(() => {\r\n      document.body.removeChild(toast);\r\n    }, 3000);\r\n  }\r\n\r\n  // Add story functionality\r\n  onAdd() {\r\n    this.router.navigate(['/stories/create']);\r\n  }\r\n\r\n  // Utility methods\r\n  getTimeAgo(dateString: string | Date | undefined): string {\r\n    if (!dateString) return 'Unknown';\r\n\r\n    const now = new Date();\r\n    let date: Date;\r\n\r\n    if (typeof dateString === 'string') {\r\n      date = new Date(dateString);\r\n    } else {\r\n      date = dateString;\r\n    }\r\n\r\n    // Check if date is valid\r\n    if (isNaN(date.getTime())) {\r\n      return 'Unknown';\r\n    }\r\n\r\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\r\n\r\n    if (diffInMinutes < 1) return 'now';\r\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\r\n\r\n    const diffInHours = Math.floor(diffInMinutes / 60);\r\n    if (diffInHours < 24) return `${diffInHours}h`;\r\n\r\n    const diffInDays = Math.floor(diffInHours / 24);\r\n    return `${diffInDays}d`;\r\n  }\r\n\r\n  private pauseAllVideos() {\r\n    const videos = document.querySelectorAll('video');\r\n    videos.forEach(video => {\r\n      if (video.pause) {\r\n        video.pause();\r\n      }\r\n    });\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeydown(event: KeyboardEvent) {\r\n    if (!this.isOpen) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        this.previousStory();\r\n        break;\r\n      case 'ArrowRight':\r\n        this.nextStory();\r\n        break;\r\n      case 'Escape':\r\n        this.closeStories();\r\n        break;\r\n    }\r\n  }\r\n}", "<div class=\"stories-container\" #storiesContainer>\n  <!-- Debug Info -->\n\n\n  <!-- Stories Header -->\n  <div class=\"stories-header\">\n    <h3 class=\"stories-title\">Stories</h3>\n    <div class=\"header-controls\">\n      <button class=\"auto-scroll-btn\"\n              (click)=\"toggleAutoScroll()\"\n              [title]=\"getAutoScrollTitle()\"\n              type=\"button\"\n              aria-label=\"Toggle auto-scroll\">\n        <i [class]=\"getAutoScrollIcon()\" aria-hidden=\"true\"></i>\n      </button>\n      <button class=\"create-story-btn\"\n              (click)=\"onAdd()\"\n              type=\"button\"\n              aria-label=\"Create new story\"\n              title=\"Create a new story\">\n        <i class=\"fas fa-plus\" aria-hidden=\"true\"></i>\n        <span>Create</span>\n      </button>\n    </div>\n  </div>\n\n  <!-- Stories Layout: Add Story + Slider -->\n  <div class=\"stories-main-layout\">\n    <!-- Add Story (Static - Outside Slider) -->\n    <div class=\"add-story-section\">\n      <div class=\"story-item add-story-item\" (click)=\"onAdd()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar add-avatar\">\n            <div class=\"story-avatar-inner\">\n              <img\n                class=\"story-avatar-img\"\n                [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\"\n                alt=\"Your Story\"\n              />\n            </div>\n            <!-- Plus button positioned outside the circle like Instagram -->\n            <div class=\"add-story-plus-btn\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n          </div>\n        </div>\n        <div class=\"story-username\">Your Story</div>\n      </div>\n    </div>\n\n    <!-- Stories Slider with Navigation -->\n    <div class=\"stories-slider-wrapper\">\n      <!-- Stories Carousel using Swiper -->\n      <swiper-container\n        class=\"stories-swiper\"\n        [slides-per-view]=\"swiperConfig.slidesPerView\"\n        [space-between]=\"swiperConfig.spaceBetween\"\n        [navigation]=\"true\"\n        [autoplay]=\"swiperConfig.autoplay\"\n        [loop]=\"swiperConfig.loop\"\n        [breakpoints]=\"swiperConfig.breakpoints\">\n\n      <!-- Loading State -->\n      <swiper-slide *ngIf=\"isLoadingStories\">\n        <div class=\"story-item loading-story\">\n          <div class=\"story-avatar-container\">\n            <div class=\"story-avatar loading-avatar\">\n              <div class=\"story-avatar-inner\">\n                <div class=\"loading-spinner\"></div>\n              </div>\n            </div>\n          </div>\n          <div class=\"story-username\">Loading...</div>\n        </div>\n      </swiper-slide>\n\n      <!-- User Story Groups (Instagram style) -->\n      <swiper-slide *ngFor=\"let userGroup of stories; let i = index\">\n        <div class=\"story-item\" (click)=\"openUserStories(userGroup, i)\">\n          <div class=\"story-avatar-container\">\n            <div class=\"story-avatar\" [class.has-products]=\"userGroup.hasProducts\">\n              <div class=\"story-avatar-inner\">\n                <img\n                  class=\"story-avatar-img\"\n                  [src]=\"userGroup.user.avatar\"\n                  [alt]=\"userGroup.user.username\"\n                />\n              </div>\n              <!-- Shopping bag indicator positioned outside the circle like Instagram -->\n              <div class=\"shopping-bag-indicator\"\n                   *ngIf=\"userGroup.hasProducts\"\n                   title=\"Shoppable content\">\n                <i class=\"fas fa-shopping-bag\"></i>\n              </div>\n            </div>\n          </div>\n          <div class=\"story-username\">{{ userGroup.user.username }}</div>\n          <!-- Story count and product count badge -->\n          <div class=\"story-count-badge\">\n            {{ userGroup.stories.length }} {{ userGroup.stories.length === 1 ? 'story' : 'stories' }}\n          </div>\n          <div class=\"product-count-badge\"\n               *ngIf=\"userGroup.hasProducts\">\n            {{ userGroup.totalProducts }} item{{ userGroup.totalProducts > 1 ? 's' : '' }}\n          </div>\n        </div>\n      </swiper-slide>\n    </swiper-container>\n\n\n  </div>\n</div>\n\n<!-- Fullscreen Stories Viewer (shown when isOpen is true) -->\n<div class=\"stories-overlay\" *ngIf=\"isOpen\">\n  <div class=\"stories-content\" #feedCover>\n    <!-- Progress Bars -->\n    <div class=\"progress-container\">\n      <div class=\"progress-bar\"\n           *ngFor=\"let story of stories; let i = index\"\n           [class.active]=\"i === currentStoryIndex\"\n           [class.completed]=\"i < currentStoryIndex\">\n        <div class=\"progress-fill\"\n             [style.width.%]=\"getProgressWidth(i)\"></div>\n      </div>\n    </div>\n\n    <!-- Story Header -->\n    <div class=\"story-header\">\n      <img class=\"story-header-avatar\" [src]=\"getCurrentStory()?.user?.avatar\" alt=\"Avatar\"/>\n      <div class=\"story-header-info\">\n        <span class=\"username\">{{ getCurrentStory()?.user?.username }}</span>\n        <span class=\"time\">{{ getTimeAgo(getCurrentStory()?.createdAt || '') }}</span>\n      </div>\n      <button (click)=\"closeStories()\" class=\"close-btn\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Story Media -->\n    <div class=\"story-media\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n\n      <!-- Image Story -->\n      <img *ngIf=\"getCurrentStory()?.mediaType === 'image'\"\n           [src]=\"getCurrentStory()?.mediaUrl\"\n           class=\"story-image\"/>\n\n      <!-- Video Story -->\n      <video *ngIf=\"getCurrentStory()?.mediaType === 'video'\"\n             class=\"story-video\"\n             [src]=\"getCurrentStory()?.mediaUrl\"\n             autoplay muted #storyVideo></video>\n\n      <!-- Middle Click Area for Product/Category Navigation -->\n      <div class=\"story-middle-click-area\"\n           (click)=\"handleMiddleAreaClick($event)\"\n           *ngIf=\"getCurrentStory()?.linkedContent\"\n           title=\"View linked {{ getCurrentStory()?.linkedContent?.type }}\">\n        <div class=\"middle-click-indicator\">\n          <i class=\"fas fa-external-link-alt\"></i>\n          <span>{{ getLinkedContentText() }}</span>\n        </div>\n      </div>\n\n      <!-- Product Tags -->\n      <div class=\"product-tags\" *ngIf=\"showProductTags && getCurrentStory()?.products\">\n        <div class=\"product-tag\"\n             *ngFor=\"let productTag of getCurrentStory()?.products\"\n             [style.left.%]=\"productTag.position?.x || 50\"\n             [style.top.%]=\"productTag.position?.y || 50\"\n             (click)=\"productTag.product && openProductDetails(productTag.product); $event.stopPropagation()\">\n          <div class=\"product-tag-dot\"></div>\n          <div class=\"product-tag-info\">\n            <img [src]=\"productTag.product.images[0].url || '/assets/images/product-placeholder.jpg'\"\n                 [alt]=\"productTag.product.name || 'Product'\"\n                 class=\"product-tag-image\">\n            <div class=\"product-tag-details\">\n              <span class=\"product-tag-name\">{{ productTag.product.name || 'Product' }}</span>\n              <span class=\"product-tag-price\">${{ productTag.product.price || 0 }}</span>\n              <div class=\"product-tag-actions\">\n                <button class=\"product-action-btn cart-btn\"\n                        (click)=\"productTag.product && addToCart(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Add to Cart\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n                <button class=\"product-action-btn wishlist-btn\"\n                        (click)=\"productTag.product && addToWishlist(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Add to Wishlist\">\n                  <i class=\"fas fa-heart\"></i>\n                </button>\n                <button class=\"product-action-btn buy-btn\"\n                        (click)=\"productTag.product && buyNow(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Buy Now\">\n                  <i class=\"fas fa-bolt\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Shopping Bag Button -->\n      <button class=\"shopping-bag-btn\"\n              *ngIf=\"hasProducts()\"\n              (click)=\"toggleProductTags(); $event.stopPropagation()\"\n              [class.active]=\"showProductTags\"\n              type=\"button\"\n              [attr.aria-label]=\"'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')\"\n              [title]=\"'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')\">\n        <i class=\"fas fa-shopping-bag\" aria-hidden=\"true\"></i>\n        <span class=\"product-count\" aria-hidden=\"true\">{{ getProductCount() }}</span>\n      </button>\n    </div>\n\n    <!-- Story Actions -->\n    <div class=\"story-actions\">\n      <button class=\"action-btn like-btn\"\n              (click)=\"toggleLike()\"\n              [class.liked]=\"isLiked\"\n              type=\"button\"\n              [attr.aria-label]=\"isLiked ? 'Unlike story' : 'Like story'\"\n              [title]=\"isLiked ? 'Unlike story' : 'Like story'\">\n        <i class=\"fas fa-heart\" aria-hidden=\"true\"></i>\n      </button>\n      <button class=\"action-btn share-btn\"\n              (click)=\"shareStory()\"\n              type=\"button\"\n              aria-label=\"Share story\"\n              title=\"Share story\">\n        <i class=\"fas fa-share\" aria-hidden=\"true\"></i>\n      </button>\n      <button class=\"action-btn save-btn\"\n              (click)=\"saveStory()\"\n              type=\"button\"\n              aria-label=\"Save story\"\n              title=\"Save story\">\n        <i class=\"fas fa-bookmark\" aria-hidden=\"true\"></i>\n      </button>\n    </div>\n\n    <!-- Navigation Arrows -->\n    <button class=\"story-nav-btn story-nav-prev\"\n            (click)=\"previousStory()\"\n            *ngIf=\"currentStoryIndex > 0\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n    <button class=\"story-nav-btn story-nav-next\"\n            (click)=\"nextStory()\"\n            *ngIf=\"currentStoryIndex < stories.length - 1\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAO1D,SAASC,QAAQ,QAAQ,uBAAuB;;;;;;;;;;;;;;;;ICsDlCC,EAJR,CAAAC,cAAA,mBAAuC,cACC,cACA,cACO,cACP;IAC9BD,EAAA,CAAAE,SAAA,cAAmC;IAGzCF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAI,MAAA,iBAAU;IAE1CJ,EAF0C,CAAAG,YAAA,EAAM,EACxC,EACO;;;;;IAePH,EAAA,CAAAC,cAAA,cAE+B;IAC7BD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAQVH,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAC,aAAA,WAAAD,YAAA,CAAAC,aAAA,qBACF;;;;;;IA1BFR,EADF,CAAAC,cAAA,mBAA+D,cACG;IAAxCD,EAAA,CAAAS,UAAA,mBAAAC,sEAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAN,YAAA,GAAAI,MAAA,CAAAG,SAAA;MAAA,MAAAC,IAAA,GAAAJ,MAAA,CAAAK,KAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAb,YAAA,EAAAQ,IAAA,CAA6B;IAAA,EAAC;IAGzDf,EAFJ,CAAAC,cAAA,cAAoC,cACqC,cACrC;IAC9BD,EAAA,CAAAE,SAAA,cAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAqB,UAAA,IAAAC,sDAAA,kBAE+B;IAInCtB,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE/DH,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAqB,UAAA,KAAAE,uDAAA,kBACmC;IAIvCvB,EADE,CAAAG,YAAA,EAAM,EACO;;;;IA1BiBH,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAwB,WAAA,iBAAAjB,YAAA,CAAAkB,WAAA,CAA4C;IAIhEzB,EAAA,CAAAK,SAAA,GAA6B;IAC7BL,EADA,CAAA0B,UAAA,QAAAnB,YAAA,CAAAoB,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAA6B,QAAAtB,YAAA,CAAAoB,IAAA,CAAAG,QAAA,CACE;IAK7B9B,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAA0B,UAAA,SAAAnB,YAAA,CAAAkB,WAAA,CAA2B;IAMTzB,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAA+B,iBAAA,CAAAxB,YAAA,CAAAoB,IAAA,CAAAG,QAAA,CAA6B;IAGvD9B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,YAAA,CAAAyB,OAAA,CAAAC,MAAA,OAAA1B,YAAA,CAAAyB,OAAA,CAAAC,MAAA,kCACF;IAEMjC,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAA0B,UAAA,SAAAnB,YAAA,CAAAkB,WAAA,CAA2B;;;;;IAgBrCzB,EAAA,CAAAC,cAAA,cAG+C;IAC7CD,EAAA,CAAAE,SAAA,cACiD;IACnDF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EADA,CAAAwB,WAAA,WAAAU,IAAA,KAAAjB,MAAA,CAAAkB,iBAAA,CAAwC,cAAAD,IAAA,GAAAjB,MAAA,CAAAkB,iBAAA,CACC;IAEvCnC,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAoC,WAAA,UAAAnB,MAAA,CAAAoB,gBAAA,CAAAH,IAAA,OAAqC;;;;;IAwB5ClC,EAAA,CAAAE,SAAA,cAE0B;;;;;IADrBF,EAAA,CAAA0B,UAAA,SAAAY,OAAA,GAAArB,MAAA,CAAAsB,eAAA,qBAAAD,OAAA,CAAAE,QAAA,EAAAxC,EAAA,CAAA6B,aAAA,CAAmC;;;;;IAIxC7B,EAAA,CAAAE,SAAA,mBAG0C;;;;;IADnCF,EAAA,CAAA0B,UAAA,SAAAe,OAAA,GAAAxB,MAAA,CAAAsB,eAAA,qBAAAE,OAAA,CAAAD,QAAA,EAAAxC,EAAA,CAAA6B,aAAA,CAAmC;;;;;;IAI1C7B,EAAA,CAAAC,cAAA,cAGsE;IAFjED,EAAA,CAAAS,UAAA,mBAAAiC,oEAAAC,MAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAgC,GAAA;MAAA,MAAA3B,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAA4B,qBAAA,CAAAF,MAAA,CAA6B;IAAA,EAAC;IAG1C3C,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAE,SAAA,YAAwC;IACxCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAA4B;IAEtCJ,EAFsC,CAAAG,YAAA,EAAO,EACrC,EACF;;;;;IALDH,EAAA,CAAA8C,sBAAA,2BAAAR,OAAA,GAAArB,MAAA,CAAAsB,eAAA,qBAAAD,OAAA,CAAAS,aAAA,kBAAAT,OAAA,CAAAS,aAAA,CAAAC,IAAA,KAAgE;IAG3DhD,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA+B,iBAAA,CAAAd,MAAA,CAAAgC,oBAAA,GAA4B;;;;;;IAMpCjD,EAAA,CAAAC,cAAA,cAIsG;IAAjGD,EAAA,CAAAS,UAAA,mBAAAyC,0EAAAP,MAAA;MAAA,MAAAQ,cAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAwC,IAAA,EAAAtC,SAAA;MAAA,MAAAG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAiC,cAAA,CAAAE,OAAA,IAA+BpC,MAAA,CAAAqC,kBAAA,CAAAH,cAAA,CAAAE,OAAA,CAAsC;MAAA,OAAArD,EAAA,CAAAmB,WAAA,CAAEwB,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IACnGvD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,cAE+B;IAE7BF,EADF,CAAAC,cAAA,cAAiC,eACA;IAAAD,EAAA,CAAAI,MAAA,GAA0C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAAoC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEzEH,EADF,CAAAC,cAAA,cAAiC,kBAIH;IAFpBD,EAAA,CAAAS,UAAA,mBAAA+C,8EAAAb,MAAA;MAAA,MAAAQ,cAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAwC,IAAA,EAAAtC,SAAA;MAAA,MAAAG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAiC,cAAA,CAAAE,OAAA,IAA+BpC,MAAA,CAAAwC,SAAA,CAAAN,cAAA,CAAAE,OAAA,CAA6B;MAAA,OAAArD,EAAA,CAAAmB,WAAA,CAAEwB,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAG7FvD,EAAA,CAAAE,SAAA,aAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGgC;IAFxBD,EAAA,CAAAS,UAAA,mBAAAiD,8EAAAf,MAAA;MAAA,MAAAQ,cAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAwC,IAAA,EAAAtC,SAAA;MAAA,MAAAG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAiC,cAAA,CAAAE,OAAA,IAA+BpC,MAAA,CAAA0C,aAAA,CAAAR,cAAA,CAAAE,OAAA,CAAiC;MAAA,OAAArD,EAAA,CAAAmB,WAAA,CAAEwB,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAGjGvD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGwB;IAFhBD,EAAA,CAAAS,UAAA,mBAAAmD,8EAAAjB,MAAA;MAAA,MAAAQ,cAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAwC,IAAA,EAAAtC,SAAA;MAAA,MAAAG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAiC,cAAA,CAAAE,OAAA,IAA+BpC,MAAA,CAAA4C,MAAA,CAAAV,cAAA,CAAAE,OAAA,CAA0B;MAAA,OAAArD,EAAA,CAAAmB,WAAA,CAAEwB,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAG1FvD,EAAA,CAAAE,SAAA,aAA2B;IAKrCF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAhCDH,EADA,CAAAoC,WAAA,UAAAe,cAAA,CAAAW,QAAA,kBAAAX,cAAA,CAAAW,QAAA,CAAAC,CAAA,aAA6C,SAAAZ,cAAA,CAAAW,QAAA,kBAAAX,cAAA,CAAAW,QAAA,CAAAE,CAAA,aACD;IAIxChE,EAAA,CAAAK,SAAA,GAAoF;IACpFL,EADA,CAAA0B,UAAA,QAAAyB,cAAA,CAAAE,OAAA,CAAAY,MAAA,IAAAC,GAAA,8CAAAlE,EAAA,CAAA6B,aAAA,CAAoF,QAAAsB,cAAA,CAAAE,OAAA,CAAAc,IAAA,cACxC;IAGhBnE,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+B,iBAAA,CAAAoB,cAAA,CAAAE,OAAA,CAAAc,IAAA,cAA0C;IACzCnE,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAoE,kBAAA,MAAAjB,cAAA,CAAAE,OAAA,CAAAgB,KAAA,UAAoC;;;;;IAb5ErE,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAqB,UAAA,IAAAiD,oDAAA,mBAIsG;IAgCxGtE,EAAA,CAAAG,YAAA,EAAM;;;;;IAnCwBH,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAA0B,UAAA,aAAAY,OAAA,GAAArB,MAAA,CAAAsB,eAAA,qBAAAD,OAAA,CAAAiC,QAAA,CAA8B;;;;;;IAsC5DvE,EAAA,CAAAC,cAAA,iBAMgG;IAJxFD,EAAA,CAAAS,UAAA,mBAAA+D,0EAAA7B,MAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAA6D,IAAA;MAAA,MAAAxD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAASD,MAAA,CAAAyD,iBAAA,EAAmB;MAAA,OAAA1E,EAAA,CAAAmB,WAAA,CAAEwB,MAAA,CAAAY,eAAA,EAAwB;IAAA,EAAC;IAK7DvD,EAAA,CAAAE,SAAA,YAAsD;IACtDF,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAI,MAAA,GAAuB;IACxEJ,EADwE,CAAAG,YAAA,EAAO,EACtE;;;;IANDH,EAAA,CAAAwB,WAAA,WAAAP,MAAA,CAAA0D,eAAA,CAAgC;IAGhC3E,EAAA,CAAA0B,UAAA,oBAAAT,MAAA,CAAA2D,eAAA,mBAAA3D,MAAA,CAAA2D,eAAA,mBAAuF;;IAE9C5E,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA+B,iBAAA,CAAAd,MAAA,CAAA2D,eAAA,GAAuB;;;;;;IA+B1E5E,EAAA,CAAAC,cAAA,iBAEsC;IAD9BD,EAAA,CAAAS,UAAA,mBAAAoE,0EAAA;MAAA7E,EAAA,CAAAY,aAAA,CAAAkE,IAAA;MAAA,MAAA7D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAA8D,aAAA,EAAe;IAAA,EAAC;IAE/B/E,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAEuD;IAD/CD,EAAA,CAAAS,UAAA,mBAAAuE,0EAAA;MAAAhF,EAAA,CAAAY,aAAA,CAAAqE,IAAA;MAAA,MAAAhE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAiE,SAAA,EAAW;IAAA,EAAC;IAE3BlF,EAAA,CAAAE,SAAA,YAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA5ITH,EAHJ,CAAAC,cAAA,cAA4C,iBACF,cAEN;IAC9BD,EAAA,CAAAqB,UAAA,IAAA8D,6CAAA,kBAG+C;IAIjDnF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,cAAuF;IAErFF,EADF,CAAAC,cAAA,cAA+B,eACN;IAAAD,EAAA,CAAAI,MAAA,GAAuC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,IAAoD;IACzEJ,EADyE,CAAAG,YAAA,EAAO,EAC1E;IACNH,EAAA,CAAAC,cAAA,kBAAmD;IAA3CD,EAAA,CAAAS,UAAA,mBAAA2E,iEAAA;MAAApF,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAqE,YAAA,EAAc;IAAA,EAAC;IAC9BtF,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAIqC;IAAhCD,EAHA,CAAAS,UAAA,mBAAA8E,8DAAA5C,MAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAuE,YAAA,CAAA7C,MAAA,CAAoB;IAAA,EAAC,wBAAA8C,mEAAA9C,MAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAChBF,MAAA,CAAAyE,YAAA,CAAA/C,MAAA,CAAoB;IAAA,EAAC,uBAAAgD,kEAAAhD,MAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CACtBF,MAAA,CAAA2E,WAAA,CAAAjD,MAAA,CAAmB;IAAA,EAAC,sBAAAkD,iEAAAlD,MAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CACrBF,MAAA,CAAA6E,UAAA,CAAAnD,MAAA,CAAkB;IAAA,EAAC;IAiElC3C,EA9DA,CAAAqB,UAAA,KAAA0E,8CAAA,kBAE0B,KAAAC,gDAAA,oBAMQ,KAAAC,8CAAA,kBAMoC,KAAAC,8CAAA,kBAQW,KAAAC,iDAAA,qBA8Ce;IAIlGnG,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2B,kBAMiC;IAJlDD,EAAA,CAAAS,UAAA,mBAAA2F,iEAAA;MAAApG,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAoF,UAAA,EAAY;IAAA,EAAC;IAK5BrG,EAAA,CAAAE,SAAA,aAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAI4B;IAHpBD,EAAA,CAAAS,UAAA,mBAAA6F,iEAAA;MAAAtG,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAsF,UAAA,EAAY;IAAA,EAAC;IAI5BvG,EAAA,CAAAE,SAAA,aAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAI2B;IAHnBD,EAAA,CAAAS,UAAA,mBAAA+F,iEAAA;MAAAxG,EAAA,CAAAY,aAAA,CAAAyE,GAAA;MAAA,MAAApE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAwF,SAAA,EAAW;IAAA,EAAC;IAI3BzG,EAAA,CAAAE,SAAA,aAAkD;IAEtDF,EADE,CAAAG,YAAA,EAAS,EACL;IAQNH,EALA,CAAAqB,UAAA,KAAAqF,iDAAA,qBAEsC,KAAAC,iDAAA,qBAKiB;IAI3D3G,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;;IA5IuBH,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAA0B,UAAA,YAAAT,MAAA,CAAAe,OAAA,CAAY;IAUFhC,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAA0B,UAAA,SAAAY,OAAA,GAAArB,MAAA,CAAAsB,eAAA,qBAAAD,OAAA,CAAAX,IAAA,kBAAAW,OAAA,CAAAX,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAuC;IAE/C7B,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAA+B,iBAAA,EAAAU,OAAA,GAAAxB,MAAA,CAAAsB,eAAA,qBAAAE,OAAA,CAAAd,IAAA,kBAAAc,OAAA,CAAAd,IAAA,CAAAG,QAAA,CAAuC;IAC3C9B,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA+B,iBAAA,CAAAd,MAAA,CAAA2F,UAAA,GAAAC,OAAA,GAAA5F,MAAA,CAAAsB,eAAA,qBAAAsE,OAAA,CAAAC,SAAA,SAAoD;IAenE9G,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAA0B,UAAA,WAAAqF,OAAA,GAAA9F,MAAA,CAAAsB,eAAA,qBAAAwE,OAAA,CAAAC,SAAA,cAA8C;IAK5ChH,EAAA,CAAAK,SAAA,EAA8C;IAA9CL,EAAA,CAAA0B,UAAA,WAAAuF,OAAA,GAAAhG,MAAA,CAAAsB,eAAA,qBAAA0E,OAAA,CAAAD,SAAA,cAA8C;IAQhDhH,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAA0B,UAAA,UAAAwF,OAAA,GAAAjG,MAAA,CAAAsB,eAAA,qBAAA2E,OAAA,CAAAnE,aAAA,CAAsC;IASjB/C,EAAA,CAAAK,SAAA,EAAoD;IAApDL,EAAA,CAAA0B,UAAA,SAAAT,MAAA,CAAA0D,eAAA,MAAAwC,QAAA,GAAAlG,MAAA,CAAAsB,eAAA,qBAAA4E,QAAA,CAAA5C,QAAA,EAAoD;IAyCtEvE,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAA0B,UAAA,SAAAT,MAAA,CAAAQ,WAAA,GAAmB;IAepBzB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAwB,WAAA,UAAAP,MAAA,CAAAmG,OAAA,CAAuB;IAGvBpH,EAAA,CAAA0B,UAAA,UAAAT,MAAA,CAAAmG,OAAA,iCAAiD;;IAsBlDpH,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,SAAAT,MAAA,CAAAkB,iBAAA,KAA2B;IAK3BnC,EAAA,CAAAK,SAAA,EAA4C;IAA5CL,EAAA,CAAA0B,UAAA,SAAAT,MAAA,CAAAkB,iBAAA,GAAAlB,MAAA,CAAAe,OAAA,CAAAC,MAAA,KAA4C;;;AD9KzD,OAAM,MAAOoF,uBAAuB;EA4ElCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,kBAAsC,EACtCC,eAAgC,EAChCC,GAAsB,EACtBC,oBAA0C;IAR1C,KAAAR,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,oBAAoB,GAApBA,oBAAoB;IAhF9B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAhG,OAAO,GAAqB,EAAE;IAC9B,KAAAiG,gBAAgB,GAAG,IAAI;IAEvB;IACA,KAAAC,MAAM,GAAG,KAAK;IACd,KAAA/F,iBAAiB,GAAG,CAAC;IACrB,KAAAgG,gBAAgB,GAAG,CAAC;IACpB,KAAAC,kBAAkB,GAAY,EAAE;IAChC,KAAAzD,eAAe,GAAG,KAAK;IACvB,KAAAyC,OAAO,GAAG,KAAK;IAEf,KAAAiB,aAAa,GAAG,CAAC;IAEjB;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,SAAS,GAAG,EAAE,CAAC,CAAC;IAChB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IAKb,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,iBAAiB,GAAG,CAAC;IAC7B,KAAAC,eAAe,GAAG,CAAC,CAAC,CAAC;IAErB;IACQ,KAAAC,cAAc,GAAG,CAAC;IAG1B;IACA,KAAAC,YAAY,GAAG;MACbC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE;QACVC,MAAM,EAAE,qBAAqB;QAC7BC,MAAM,EAAE;OACT;MACDC,QAAQ,EAAE;QACRC,KAAK,EAAE,IAAI;QACXC,oBAAoB,EAAE,KAAK;QAC3BC,iBAAiB,EAAE;OACpB;MACDC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE;QACX,CAAC,EAAE;UACDV,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE;SACf;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE;;;KAGnB;IAED;IACA,KAAAU,mBAAmB,GAAG,IAAI;IAElB,KAAAC,aAAa,GAAmB,EAAE;IAaxCC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAC/D;EAEAC,QAAQA,CAAA;IACNF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAEzD;IACAjK,QAAQ,EAAE;IAEV,IAAI,CAACmK,WAAW,EAAE;IAClB,IAAI,CAACzC,WAAW,CAAC0C,YAAY,CAACC,SAAS,CAACzI,IAAI,IAAG;MAC7C,IAAI,CAACqG,WAAW,GAAGrG,IAAI;MACvBoI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAErI,IAAI,CAAC;IAC/C,CAAC,CAAC;IAEF;IACA,IAAI,CAAC0I,yBAAyB,EAAE;EAClC;EAEA;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAACT,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACpD,MAAMU,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAQ;IAEjE,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,MAAM,EAAE;MAC/B,IAAI,IAAI,CAACb,mBAAmB,EAAE;QAC5BU,QAAQ,CAACG,MAAM,CAACnB,QAAQ,CAACoB,KAAK,EAAE;OACjC,MAAM;QACLJ,QAAQ,CAACG,MAAM,CAACnB,QAAQ,CAACqB,IAAI,EAAE;;;EAGrC;EAEA;EACAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAChB,mBAAmB,GAAG,cAAc,GAAG,aAAa;EAClE;EAEA;EACAiB,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACjB,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB;EAC7E;EAEAkB,eAAeA,CAAA;IACbhB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCgB,UAAU,CAAC,MAAK;MACdjB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAACiB,yBAAyB,EAAE;MAChC,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;IAEP;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAChJ,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3B8H,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD,IAAI,CAACiB,yBAAyB,EAAE;QAChC,IAAI,CAACC,mBAAmB,EAAE;;IAE9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACrB,aAAa,CAACsB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACnB,yBAAyB,EAAE;IAChC,IAAI,CAACY,yBAAyB,EAAE;IAChC,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQb,yBAAyBA,CAAA;IAC/B,IAAI,CAAC7B,aAAa,GAAGiD,MAAM,CAACC,UAAU,GAAG,GAAG;EAC9C;EAEQT,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAACU,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,aAAa,EAAE;MAC1D,MAAMC,cAAc,GAAG,IAAI,CAACF,aAAa,CAACC,aAAa,CAACE,WAAW;MACnE,MAAMC,WAAW,GAAGN,MAAM,CAACC,UAAU;MAErC3B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAAE6B,cAAc;QAAEE;MAAW,CAAE,CAAC;MAE9E;MACA,IAAIA,WAAW,IAAI,GAAG,EAAE;QACtB,IAAI,CAACpD,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAGmD,cAAc,GAAG,CAAC;OACpC,MAAM,IAAIE,WAAW,IAAI,GAAG,EAAE;QAC7B,IAAI,CAACpD,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAGmD,cAAc,GAAG,CAAC;OACpC,MAAM;QACL,IAAI,CAAClD,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAGmD,cAAc,GAAG,CAAC;;MAGrC9B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CrB,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BD,SAAS,EAAE,IAAI,CAACA;OACjB,CAAC;MAEF;MACA,IAAI,CAACE,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACH,UAAU,GAAG,CAAC;KACpB,MAAM;MACLsB,OAAO,CAACiC,IAAI,CAAC,4DAA4D,CAAC;MAC1E;MACA,IAAI,CAACtD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;EAEzB;EAEAuB,WAAWA,CAAA;IACT,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC6B,aAAa,CAACmC,IAAI,CACrB,IAAI,CAACzE,IAAI,CAAC0E,GAAG,CAAM,GAAGpM,WAAW,CAACqM,MAAM,UAAU,CAAC,CAAC/B,SAAS,CAAC;MAC5DgC,IAAI,EAAGC,QAAQ,IAAI;QACjBtC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEqC,QAAQ,CAAC;QAC9C,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACrK,OAAO,IAAIqK,QAAQ,CAACrK,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACvE;UACA,MAAMsK,UAAU,GAAGF,QAAQ,CAACrK,OAAO,CAChCwK,MAAM,CAAEC,KAAU,IAAKA,KAAK,CAACC,QAAQ,CAAC,CACtCC,GAAG,CAAEF,KAAU,KAAM;YACpB,GAAGA,KAAK;YACRjK,QAAQ,EAAEiK,KAAK,CAACG,KAAK,EAAE1I,GAAG,IAAIuI,KAAK,CAACjK,QAAQ;YAC5CwE,SAAS,EAAEyF,KAAK,CAACG,KAAK,EAAE5J,IAAI,IAAIyJ,KAAK,CAACzF;WACvC,CAAC,CAAC;UAEL;UACA,IAAI,CAAChF,OAAO,GAAG,IAAI,CAAC6K,kBAAkB,CAACN,UAAU,CAAC;UAClDxC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAChI,OAAO,CAAC;UACjE+H,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAChI,OAAO,CAACC,MAAM,CAAC;SAC7D,MAAM;UACL8H,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D,IAAI,CAAC8C,mBAAmB,EAAE;;QAE5B,IAAI,CAAC7E,gBAAgB,GAAG,KAAK;QAC7B+C,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9ChD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAAC8C,mBAAmB,EAAE;QAC1B,IAAI,CAAC7E,gBAAgB,GAAG,KAAK;QAC7B+C,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC,CACH;EACH;EAEA;EACQ2B,kBAAkBA,CAACN,UAAmB;IAC5C,MAAMS,cAAc,GAAG,IAAIC,GAAG,EAAE;IAEhCV,UAAU,CAACnB,OAAO,CAACqB,KAAK,IAAG;MACzB,MAAMS,MAAM,GAAGT,KAAK,CAAC9K,IAAI,CAACwL,GAAG;MAC7B,IAAI,CAACH,cAAc,CAACI,GAAG,CAACF,MAAM,CAAC,EAAE;QAC/BF,cAAc,CAACK,GAAG,CAACH,MAAM,EAAE;UACzBvL,IAAI,EAAE8K,KAAK,CAAC9K,IAAI;UAChBK,OAAO,EAAE,EAAE;UACXP,WAAW,EAAE,KAAK;UAClBjB,aAAa,EAAE,CAAC;UAChB8M,eAAe,EAAEb,KAAK,CAAC3F;SACxB,CAAC;;MAGJ,MAAMyG,SAAS,GAAGP,cAAc,CAACd,GAAG,CAACgB,MAAM,CAAC;MAC5CK,SAAS,CAACvL,OAAO,CAACiK,IAAI,CAACQ,KAAK,CAAC;MAE7B;MACA,IAAIA,KAAK,CAAClI,QAAQ,IAAIkI,KAAK,CAAClI,QAAQ,CAACtC,MAAM,GAAG,CAAC,EAAE;QAC/CsL,SAAS,CAAC9L,WAAW,GAAG,IAAI;QAC5B8L,SAAS,CAAC/M,aAAa,IAAIiM,KAAK,CAAClI,QAAQ,CAACtC,MAAM;;MAGlD;MACA,IAAI,IAAIuL,IAAI,CAACf,KAAK,CAAC3F,SAAS,CAAC,GAAG,IAAI0G,IAAI,CAACD,SAAS,CAACD,eAAe,CAAC,EAAE;QACnEC,SAAS,CAACD,eAAe,GAAGb,KAAK,CAAC3F,SAAS;;IAE/C,CAAC,CAAC;IAEF;IACA,OAAO2G,KAAK,CAACC,IAAI,CAACV,cAAc,CAACW,MAAM,EAAE,CAAC,CACvCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIN,IAAI,CAACM,CAAC,CAACR,eAAe,CAAC,CAACS,OAAO,EAAE,GAAG,IAAIP,IAAI,CAACK,CAAC,CAACP,eAAe,CAAC,CAACS,OAAO,EAAE,CAAC;EAClG;EAEAjB,mBAAmBA,CAAA;IACjB/C,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAAChI,OAAO,GAAG,EAAE;EACnB;EAEA;EACAgM,UAAUA,CAAA;IACRjE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACpB,iBAAiB,CAAC;IAC1E,IAAI,IAAI,CAACA,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACqF,oBAAoB,EAAE;MAC3BlE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACpB,iBAAiB,CAAC;KAC/D,MAAM;MACLmB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;EAEvD;EAEAkE,WAAWA,CAAA;IACT,MAAMC,UAAU,GAAG,IAAI,CAACnM,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAMmM,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAACxF,YAAY,CAAC;IAEjEoB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCuE,YAAY,EAAE,IAAI,CAAC3F,iBAAiB;MACpCuF,UAAU;MACVC,aAAa;MACbzF,YAAY,EAAE,IAAI,CAACA;KACpB,CAAC;IAEF,IAAI,IAAI,CAACC,iBAAiB,GAAGwF,aAAa,EAAE;MAC1C,IAAI,CAACxF,iBAAiB,EAAE;MACxB,IAAI,CAACqF,oBAAoB,EAAE;MAC3BlE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACpB,iBAAiB,CAAC;KAChE,MAAM;MACLmB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;EAEtD;EAEQiE,oBAAoBA,CAAA;IAC1B,MAAMO,aAAa,GAAG,CAAC,IAAI,CAAC5F,iBAAiB,GAAG,IAAI,CAACF,SAAS;IAC9DqB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCuE,YAAY,EAAE,IAAI,CAAC3F,iBAAiB;MACpCF,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB8F;KACD,CAAC;IAEF,IAAI,CAAC/F,UAAU,GAAG+F,aAAa;IAC/B,IAAI,CAACtD,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMiD,UAAU,GAAG,IAAI,CAACnM,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAMmM,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAACxF,YAAY,CAAC;IAEjE,IAAI,CAACL,aAAa,GAAG,IAAI,CAACM,iBAAiB,GAAG,CAAC;IAC/C,IAAI,CAACL,cAAc,GAAG,IAAI,CAACK,iBAAiB,GAAGwF,aAAa;IAE5DrE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;MACrC1B,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnC4F,UAAU;MACVC,aAAa;MACbG,YAAY,EAAE,IAAI,CAAC3F;KACpB,CAAC;EACJ;EAEA;EACAxH,eAAeA,CAACmM,SAAyB,EAAEkB,SAAiB;IAC1D,IAAI,CAACtG,gBAAgB,GAAGsG,SAAS;IACjC,IAAI,CAACtM,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACiG,kBAAkB,GAAGmF,SAAS,CAACvL,OAAO;IAC3C,IAAI,CAACkG,MAAM,GAAG,IAAI;IAClB,IAAI,CAACvD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC+J,eAAe,EAAE;IACtBlE,QAAQ,CAACmE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvC9E,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuD,SAAS,CAAC5L,IAAI,CAACG,QAAQ,EAAE,gBAAgB,EAAEyL,SAAS,CAACvL,OAAO,CAACC,MAAM,CAAC;EAC/G;EAEA6M,SAASA,CAACC,MAAa,EAAE/N,KAAa;IACpC;IACA,IAAI,CAACmB,iBAAiB,GAAGnB,KAAK;IAC9B,IAAI,CAACkH,MAAM,GAAG,IAAI;IAClB,IAAI,CAACvD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC+J,eAAe,EAAE;IACtBlE,QAAQ,CAACmE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEAvJ,YAAYA,CAAA;IACV,IAAI,CAAC4C,MAAM,GAAG,KAAK;IACnB,IAAI,CAACqD,eAAe,EAAE;IACtB,IAAI,CAACyD,cAAc,EAAE;IACrBxE,QAAQ,CAACmE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA3J,SAASA,CAAA;IACP;IACA,IAAI,IAAI,CAAC/C,iBAAiB,GAAG,IAAI,CAACiG,kBAAkB,CAACnG,MAAM,GAAG,CAAC,EAAE;MAC/D,IAAI,CAACE,iBAAiB,EAAE;MACxB,IAAI,CAACwC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAAC+J,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,IAAI,CAACvG,gBAAgB,GAAG,IAAI,CAACnG,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACkG,gBAAgB,EAAE;QACvB,IAAI,CAAChG,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAACiG,kBAAkB,GAAG,IAAI,CAACpG,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAC,CAACnG,OAAO;QACrE,IAAI,CAAC2C,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC+J,eAAe,EAAE;OACvB,MAAM;QACL,IAAI,CAACpJ,YAAY,EAAE;;;EAGzB;EAEAP,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAAC5C,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACwC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAAC+J,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,IAAI,CAACvG,gBAAgB,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACA,gBAAgB,EAAE;QACvB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACpG,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAC,CAACnG,OAAO;QACrE,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAACiG,kBAAkB,CAACnG,MAAM,GAAG,CAAC;QAC3D,IAAI,CAAC0C,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC+J,eAAe,EAAE;OACvB,MAAM;QACL,IAAI,CAACpJ,YAAY,EAAE;;;EAGzB;EAEA/C,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC6F,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACnG,MAAM,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAACmG,kBAAkB,CAAC,IAAI,CAACjG,iBAAiB,CAAC,IAAI,IAAI,CAACiG,kBAAkB,CAAC,CAAC,CAAC;;IAEtF;IACA,IAAI,IAAI,CAACpG,OAAO,IAAI,IAAI,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACjF,OAAO,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC;;IAEnC,OAAO,IAAI;EACb;EAEAiN,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACjN,OAAO,IAAI,IAAI,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO,IAAI,CAACD,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAC,EAAExG,IAAI;;IAElD,OAAO,IAAI;EACb;EAEAF,WAAWA,CAAA;IACT,MAAMgL,KAAK,GAAG,IAAI,CAAClK,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEkK,KAAK,IAAIA,KAAK,CAAClI,QAAQ,IAAIkI,KAAK,CAAClI,QAAQ,CAACtC,MAAM,GAAG,CAAC,CAAC;EACjE;EAEA2C,eAAeA,CAAA;IACb,MAAM6H,KAAK,GAAG,IAAI,CAAClK,eAAe,EAAE;IACpC,OAAOkK,KAAK,EAAElI,QAAQ,EAAEtC,MAAM,IAAI,CAAC;EACrC;EAEA;EACAI,gBAAgBA,CAACrB,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACmB,iBAAiB,EAAE,OAAO,GAAG;IAC9C,IAAInB,KAAK,GAAG,IAAI,CAACmB,iBAAiB,EAAE,OAAO,CAAC;IAE5C;IACA,OAAO,IAAI,CAAC4G,eAAe;EAC7B;EAEQ2F,eAAeA,CAAA;IACrB,IAAI,CAACnD,eAAe,EAAE;IACtB,IAAI,CAACzC,iBAAiB,GAAG0E,IAAI,CAAC0B,GAAG,EAAE;IACnC,IAAI,CAACnG,eAAe,GAAG,CAAC;IAExB;IACA,IAAI,CAACoG,mBAAmB,GAAGC,WAAW,CAAC,MAAK;MAC1C,IAAI,IAAI,CAACtG,iBAAiB,EAAE;QAC1B,MAAMuG,OAAO,GAAG7B,IAAI,CAAC0B,GAAG,EAAE,GAAG,IAAI,CAACpG,iBAAiB;QACnD,IAAI,CAACC,eAAe,GAAGsF,IAAI,CAACiB,GAAG,CAAED,OAAO,GAAG,IAAI,CAACxG,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;QAC1E,IAAI,CAACf,GAAG,CAACyH,aAAa,EAAE,CAAC,CAAC;;IAE9B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACC,aAAa,GAAGxE,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC9F,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAAC2D,aAAa,CAAC;EACxB;EAEQ0C,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACiE,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;;IAE3B,IAAI,IAAI,CAACL,mBAAmB,EAAE;MAC5BO,aAAa,CAAC,IAAI,CAACP,mBAAmB,CAAC;MACvC,IAAI,CAACA,mBAAmB,GAAG,IAAI;;IAEjC,IAAI,CAACrG,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,CAAC;EAC1B;EAEA;EACAvD,YAAYA,CAACmK,KAAiB;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACE,OAAO;IAC5B,MAAMC,WAAW,GAAGrE,MAAM,CAACC,UAAU;IAErC,IAAIkE,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAAC/K,aAAa,EAAE;KACrB,MAAM,IAAI6K,MAAM,GAAIE,WAAW,GAAG,CAAC,GAAI,CAAC,EAAE;MACzC,IAAI,CAAC5K,SAAS,EAAE;;EAEpB;EAEA;EACAQ,YAAYA,CAACqK,MAAkB;IAC7B,IAAI,CAAC/G,cAAc,GAAGwE,IAAI,CAAC0B,GAAG,EAAE;IAChC,IAAI,CAACc,cAAc,GAAGhF,UAAU,CAAC,MAAK;MACpC,IAAI,CAACO,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA3F,WAAWA,CAACmK,MAAkB;IAC5B,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBP,YAAY,CAAC,IAAI,CAACO,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEAlK,UAAUA,CAAC6J,KAAiB;IAC1B,IAAI,IAAI,CAACK,cAAc,EAAE;MACvBP,YAAY,CAAC,IAAI,CAACO,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,MAAMC,aAAa,GAAGzC,IAAI,CAAC0B,GAAG,EAAE,GAAG,IAAI,CAAClG,cAAc;IACtD,IAAIiH,aAAa,GAAG,GAAG,EAAE;MACvB;MACA,MAAMC,KAAK,GAAGP,KAAK,CAACQ,cAAc,CAAC,CAAC,CAAC;MACrC,IAAI,CAAC3K,YAAY,CAAC;QAAEqK,OAAO,EAAEK,KAAK,CAACL;MAAO,CAAgB,CAAC;KAC5D,MAAM;MACL;MACA,IAAI,CAACnB,eAAe,EAAE;;EAE1B;EAEA;EACAhK,iBAAiBA,CAAA;IACf,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEArB,kBAAkBA,CAACD,OAAY;IAC7B,IAAI,CAACkE,MAAM,CAAC6I,QAAQ,CAAC,CAAC,UAAU,EAAE/M,OAAO,CAAC8J,GAAG,CAAC,CAAC;EACjD;EAEA;EACA1J,SAASA,CAACJ,OAAY;IACpB0G,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE3G,OAAO,CAAC;IAE/C,IAAI,CAAC0E,oBAAoB,CAACtE,SAAS,CAAC;MAClC4M,SAAS,EAAEhN,OAAO,CAAC8J,GAAG;MACtBmD,IAAI,EAAEjN,OAAO,CAACiN,IAAI;MAClBC,KAAK,EAAElN,OAAO,CAACkN,KAAK;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAACrG,SAAS,CAAC;MACXgC,IAAI,EAAGsE,MAAM,IAAI;QACf,IAAIA,MAAM,CAACpE,OAAO,EAAE;UAClBvC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0G,MAAM,CAAC;UAC1D;SACD,MAAM;UACL3G,OAAO,CAACgD,KAAK,CAAC,gCAAgC,EAAE2D,MAAM,CAACC,OAAO,CAAC;;MAEnE,CAAC;MACD5D,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEA;EACApJ,aAAaA,CAACN,OAAY;IACxB0G,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE3G,OAAO,CAAC;IAEnD,IAAI,CAAC0E,oBAAoB,CAACpE,aAAa,CAAC;MACtC0M,SAAS,EAAEhN,OAAO,CAAC8J,GAAG;MACtBmD,IAAI,EAAEjN,OAAO,CAACiN,IAAI;MAClBC,KAAK,EAAElN,OAAO,CAACkN,KAAK;MACpBE,SAAS,EAAE;KACZ,CAAC,CAACrG,SAAS,CAAC;MACXgC,IAAI,EAAGsE,MAAM,IAAI;QACf,IAAIA,MAAM,CAACpE,OAAO,EAAE;UAClBvC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE0G,MAAM,CAAC;UAC9D;SACD,MAAM;UACL3G,OAAO,CAACgD,KAAK,CAAC,oCAAoC,EAAE2D,MAAM,CAACC,OAAO,CAAC;;MAEvE,CAAC;MACD5D,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;EACAlJ,MAAMA,CAACR,OAAY;IACjB0G,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE3G,OAAO,CAAC;IAEpD,IAAI,CAAC0E,oBAAoB,CAAClE,MAAM,CAAC;MAC/BwM,SAAS,EAAEhN,OAAO,CAAC8J,GAAG;MACtBmD,IAAI,EAAEjN,OAAO,CAACiN,IAAI;MAClBC,KAAK,EAAElN,OAAO,CAACkN,KAAK;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAACrG,SAAS,CAAC;MACXgC,IAAI,EAAGsE,MAAM,IAAI;QACf,IAAIA,MAAM,CAACpE,OAAO,EAAE;UAClBvC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0G,MAAM,CAAC;UAC1C;SACD,MAAM;UACL3G,OAAO,CAACgD,KAAK,CAAC,oBAAoB,EAAE2D,MAAM,CAACC,OAAO,CAAC;;MAEvD,CAAC;MACD5D,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACA1G,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACoB,WAAW,CAACmJ,eAAe,EAAE;MACrC,IAAI,CAACrJ,MAAM,CAAC6I,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMS,YAAY,GAAG,IAAI,CAACtO,eAAe,EAAE;IAC3C,IAAI,CAACsO,YAAY,EAAE;IAEnB,IAAI,CAACzJ,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,MAAM0J,QAAQ,GAAG,IAAI,CAAC1J,OAAO,GAAG,MAAM,GAAG,QAAQ;IACjD,IAAI,CAACI,IAAI,CAACuJ,IAAI,CAAC,GAAGjR,WAAW,CAACqM,MAAM,YAAY0E,YAAY,CAAC1D,GAAG,IAAI2D,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC1G,SAAS,CAAC;MAC5FgC,IAAI,EAAGC,QAAQ,IAAI;QACjBtC,OAAO,CAACC,GAAG,CAAC,SAAS8G,QAAQ,iBAAiB,EAAEzE,QAAQ,CAAC;QACzD;QACA,IAAI,IAAI,CAACxE,eAAe,CAACmJ,WAAW,EAAE,EAAE;UACtC;UACAjH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChCiH,OAAO,EAAEJ,YAAY,CAAC1D,GAAG;YACzBD,MAAM,EAAE,IAAI,CAACzF,WAAW,CAACyJ,gBAAgB,EAAE/D,GAAG;YAC9CgE,KAAK,EAAE,IAAI,CAAC/J;WACb,CAAC;;MAEN,CAAC;MACD2F,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,SAAS+D,QAAQ,YAAY,EAAE/D,KAAK,CAAC;QACnD;QACA,IAAI,CAAC3F,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC9B;KACD,CAAC;EACJ;EAEAb,UAAUA,CAAA;IACR,MAAMsK,YAAY,GAAG,IAAI,CAACtO,eAAe,EAAE;IAC3C,IAAI,CAACsO,YAAY,EAAE;IAEnB,IAAIO,SAAS,CAACC,KAAK,EAAE;MACnB;MACAD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,YAAYT,YAAY,CAAClP,IAAI,CAACG,QAAQ,EAAE;QAC/CyP,IAAI,EAAEV,YAAY,CAACW,OAAO;QAC1BtN,GAAG,EAAE,GAAGpE,WAAW,CAAC2R,WAAW,UAAUZ,YAAY,CAAC1D,GAAG;OAC1D,CAAC,CAACuE,IAAI,CAAC,MAAK;QACX3H,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC;QACA,IAAI,CAAC2H,eAAe,CAACd,YAAY,CAAC1D,GAAG,CAAC;MACxC,CAAC,CAAC,CAACyE,KAAK,CAAE7E,KAAK,IAAI;QACjBhD,OAAO,CAACgD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;KACH,MAAM;MACL;MACA,MAAM8E,QAAQ,GAAG,GAAG/R,WAAW,CAAC2R,WAAW,UAAUZ,YAAY,CAAC1D,GAAG,EAAE;MACvEiE,SAAS,CAACU,SAAS,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACH,IAAI,CAAC,MAAK;QAChD3H,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAAC2H,eAAe,CAACd,YAAY,CAAC1D,GAAG,CAAC;QACtC;QACA,IAAI,CAAC6E,SAAS,CAAC,iCAAiC,CAAC;MACnD,CAAC,CAAC,CAACJ,KAAK,CAAE7E,KAAK,IAAI;QACjBhD,OAAO,CAACgD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEAtG,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACgB,WAAW,CAACmJ,eAAe,EAAE;MACrC,IAAI,CAACrJ,MAAM,CAAC6I,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMS,YAAY,GAAG,IAAI,CAACtO,eAAe,EAAE;IAC3C,IAAI,CAACsO,YAAY,EAAE;IAEnB;IACA,IAAI,CAACrJ,IAAI,CAACuJ,IAAI,CAAC,GAAGjR,WAAW,CAACqM,MAAM,YAAY0E,YAAY,CAAC1D,GAAG,OAAO,EAAE,EAAE,CAAC,CAAC/C,SAAS,CAAC;MACrFgC,IAAI,EAAGC,QAAQ,IAAI;QACjBtC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEqC,QAAQ,CAAC;QAClD,IAAI,CAAC2F,SAAS,CAAC,iCAAiC,CAAC;QACjD;QACA,IAAI,IAAI,CAACnK,eAAe,CAACmJ,WAAW,EAAE,EAAE;UACtC;UACAjH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChCiH,OAAO,EAAEJ,YAAY,CAAC1D,GAAG;YACzBD,MAAM,EAAE,IAAI,CAACzF,WAAW,CAACyJ,gBAAgB,EAAE/D;WAC5C,CAAC;;MAEN,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACiF,SAAS,CAAC,uCAAuC,CAAC;MACzD;KACD,CAAC;EACJ;EAEQL,eAAeA,CAACV,OAAe;IACrC,IAAI,IAAI,CAACxJ,WAAW,CAACmJ,eAAe,EAAE;MACpC,IAAI,CAACpJ,IAAI,CAACuJ,IAAI,CAAC,GAAGjR,WAAW,CAACqM,MAAM,YAAY8E,OAAO,QAAQ,EAAE,EAAE,CAAC,CAAC7G,SAAS,CAAC;QAC7EgC,IAAI,EAAGC,QAAQ,IAAI;UACjBtC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqC,QAAQ,CAAC;QAC/C,CAAC;QACDU,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEA;EACAlK,qBAAqBA,CAAC8M,KAAY;IAChCA,KAAK,CAACpM,eAAe,EAAE;IACvB,MAAMsN,YAAY,GAAG,IAAI,CAACtO,eAAe,EAAE;IAE3C,IAAI,CAACsO,YAAY,EAAE9N,aAAa,EAAE;MAChC;;IAGFgH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE6G,YAAY,CAAC9N,aAAa,CAAC;IAE9E,QAAQ8N,YAAY,CAAC9N,aAAa,CAACC,IAAI;MACrC,KAAK,SAAS;QACZ,IAAI6N,YAAY,CAAC9N,aAAa,CAACsN,SAAS,EAAE;UACxC,IAAI,CAAC9I,MAAM,CAAC6I,QAAQ,CAAC,CAAC,UAAU,EAAES,YAAY,CAAC9N,aAAa,CAACsN,SAAS,CAAC,CAAC;SACzE,MAAM,IAAIQ,YAAY,CAACtM,QAAQ,IAAIsM,YAAY,CAACtM,QAAQ,CAACtC,MAAM,GAAG,CAAC,EAAE;UACpE;UACA,IAAI,CAACsF,MAAM,CAAC6I,QAAQ,CAAC,CAAC,UAAU,EAAES,YAAY,CAACtM,QAAQ,CAAC,CAAC,CAAC,CAAClB,OAAO,CAAC8J,GAAG,CAAC,CAAC;;QAE1E;MAEF,KAAK,UAAU;QACb,IAAI0D,YAAY,CAAC9N,aAAa,CAACkP,UAAU,EAAE;UACzC,IAAI,CAAC1K,MAAM,CAAC6I,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9B8B,WAAW,EAAE;cAAEC,QAAQ,EAAEtB,YAAY,CAAC9N,aAAa,CAACkP;YAAU;WAC/D,CAAC;;QAEJ;MAEF,KAAK,OAAO;QACV,IAAIpB,YAAY,CAAC9N,aAAa,CAACqP,OAAO,EAAE;UACtC,IAAI,CAAC7K,MAAM,CAAC6I,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9B8B,WAAW,EAAE;cAAEG,KAAK,EAAExB,YAAY,CAAC9N,aAAa,CAACqP;YAAO;WACzD,CAAC;;QAEJ;MAEF,KAAK,YAAY;QACf,IAAIvB,YAAY,CAAC9N,aAAa,CAACuP,YAAY,EAAE;UAC3C,IAAI,CAAC/K,MAAM,CAAC6I,QAAQ,CAAC,CAAC,aAAa,EAAES,YAAY,CAAC9N,aAAa,CAACuP,YAAY,CAAC,CAAC;;QAEhF;MAEF;QACEvI,OAAO,CAACiC,IAAI,CAAC,8BAA8B,EAAE6E,YAAY,CAAC9N,aAAa,CAACC,IAAI,CAAC;;IAGjF;IACA,IAAI,CAACuP,uBAAuB,CAAC1B,YAAY,CAAC1D,GAAG,EAAE0D,YAAY,CAAC9N,aAAa,CAAC;EAC5E;EAEA;EACAE,oBAAoBA,CAAA;IAClB,MAAM4N,YAAY,GAAG,IAAI,CAACtO,eAAe,EAAE;IAE3C,IAAI,CAACsO,YAAY,EAAE9N,aAAa,EAAE;MAChC,OAAO,EAAE;;IAGX,QAAQ8N,YAAY,CAAC9N,aAAa,CAACC,IAAI;MACrC,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,YAAY;QACf,OAAO,iBAAiB;MAC1B;QACE,OAAO,cAAc;;EAE3B;EAEA;EACQuP,uBAAuBA,CAACtB,OAAe,EAAElO,aAAkB;IACjE,IAAI,IAAI,CAAC0E,WAAW,CAACmJ,eAAe,EAAE;MACpC,IAAI,CAACpJ,IAAI,CAACuJ,IAAI,CAAC,GAAGjR,WAAW,CAACqM,MAAM,YAAY8E,OAAO,QAAQ,EAAE;QAC/DuB,WAAW,EAAEzP,aAAa,CAACC,IAAI;QAC/ByP,SAAS,EAAE1P,aAAa,CAACsN,SAAS,IAAItN,aAAa,CAACkP,UAAU,IAAIlP,aAAa,CAACqP,OAAO,IAAIrP,aAAa,CAACuP;OAC1G,CAAC,CAAClI,SAAS,CAAC;QACXgC,IAAI,EAAGC,QAAQ,IAAI;UACjBtC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEqC,QAAQ,CAAC;QACxD,CAAC;QACDU,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;OACD,CAAC;;EAEN;EAEQiF,SAASA,CAACrB,OAAe;IAC/B;IACA,MAAM+B,KAAK,GAAGlI,QAAQ,CAACmI,aAAa,CAAC,KAAK,CAAC;IAC3CD,KAAK,CAACE,WAAW,GAAGjC,OAAO;IAC3B+B,KAAK,CAAC9D,KAAK,CAACiE,OAAO,GAAG;;;;;;;;;;;KAWrB;IACDrI,QAAQ,CAACmE,IAAI,CAACmE,WAAW,CAACJ,KAAK,CAAC;IAEhC1H,UAAU,CAAC,MAAK;MACdR,QAAQ,CAACmE,IAAI,CAACoE,WAAW,CAACL,KAAK,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAM,KAAKA,CAAA;IACH,IAAI,CAACzL,MAAM,CAAC6I,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA;EACAxJ,UAAUA,CAACqM,UAAqC;IAC9C,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IAEjC,MAAM/D,GAAG,GAAG,IAAI1B,IAAI,EAAE;IACtB,IAAI0F,IAAU;IAEd,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;MAClCC,IAAI,GAAG,IAAI1F,IAAI,CAACyF,UAAU,CAAC;KAC5B,MAAM;MACLC,IAAI,GAAGD,UAAU;;IAGnB;IACA,IAAIE,KAAK,CAACD,IAAI,CAACnF,OAAO,EAAE,CAAC,EAAE;MACzB,OAAO,SAAS;;IAGlB,MAAMqF,aAAa,GAAG/E,IAAI,CAACgF,KAAK,CAAC,CAACnE,GAAG,CAACnB,OAAO,EAAE,GAAGmF,IAAI,CAACnF,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIqF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAME,WAAW,GAAGjF,IAAI,CAACgF,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIE,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGlF,IAAI,CAACgF,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEQvE,cAAcA,CAAA;IACpB,MAAMwE,MAAM,GAAGhJ,QAAQ,CAACiJ,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACpI,OAAO,CAACsI,KAAK,IAAG;MACrB,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAGAC,aAAaA,CAACjE,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACzH,MAAM,EAAE;IAElB,QAAQyH,KAAK,CAACkE,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAAC9O,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACG,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACI,YAAY,EAAE;QACnB;;EAEN;;;uBAz3BW+B,uBAAuB,EAAArH,EAAA,CAAA8T,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhU,EAAA,CAAA8T,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAlU,EAAA,CAAA8T,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAApU,EAAA,CAAA8T,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAtU,EAAA,CAAA8T,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAxU,EAAA,CAAA8T,iBAAA,CAAAW,EAAA,CAAAC,kBAAA,GAAA1U,EAAA,CAAA8T,iBAAA,CAAAa,EAAA,CAAAC,eAAA,GAAA5U,EAAA,CAAA8T,iBAAA,CAAA9T,EAAA,CAAA6U,iBAAA,GAAA7U,EAAA,CAAA8T,iBAAA,CAAAgB,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAvB1N,uBAAuB;MAAA2N,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvBnV,EAAA,CAAAS,UAAA,oBAAA4U,kDAAA1S,MAAA;YAAA,OAAAyS,GAAA,CAAA5J,QAAA,CAAA7I,MAAA,CAAgB;UAAA,UAAA3C,EAAA,CAAAsV,eAAA,CAAO,qBAAAC,mDAAA5S,MAAA;YAAA,OAAvByS,GAAA,CAAAxB,aAAA,CAAAjR,MAAA,CAAqB;UAAA,UAAA3C,EAAA,CAAAwV,iBAAA,CAAE;;;;;;;;;;;UC3EhCxV,EANJ,CAAAC,cAAA,gBAAiD,aAKnB,YACA;UAAAD,EAAA,CAAAI,MAAA,cAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEpCH,EADF,CAAAC,cAAA,aAA6B,gBAKa;UAHhCD,EAAA,CAAAS,UAAA,mBAAAgV,yDAAA;YAAAzV,EAAA,CAAAY,aAAA,CAAA8U,GAAA;YAAA,OAAA1V,EAAA,CAAAmB,WAAA,CAASiU,GAAA,CAAA9K,gBAAA,EAAkB;UAAA,EAAC;UAIlCtK,EAAA,CAAAE,SAAA,WAAwD;UAC1DF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,gBAImC;UAH3BD,EAAA,CAAAS,UAAA,mBAAAkV,yDAAA;YAAA3V,EAAA,CAAAY,aAAA,CAAA8U,GAAA;YAAA,OAAA1V,EAAA,CAAAmB,WAAA,CAASiU,GAAA,CAAApC,KAAA,EAAO;UAAA,EAAC;UAIvBhT,EAAA,CAAAE,SAAA,YAA8C;UAC9CF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAGlBJ,EAHkB,CAAAG,YAAA,EAAO,EACZ,EACL,EACF;UAMFH,EAHJ,CAAAC,cAAA,eAAiC,eAEA,eAC4B;UAAlBD,EAAA,CAAAS,UAAA,mBAAAmV,uDAAA;YAAA5V,EAAA,CAAAY,aAAA,CAAA8U,GAAA;YAAA,OAAA1V,EAAA,CAAAmB,WAAA,CAASiU,GAAA,CAAApC,KAAA,EAAO;UAAA,EAAC;UAGlDhT,EAFJ,CAAAC,cAAA,eAAoC,eACG,eACH;UAC9BD,EAAA,CAAAE,SAAA,eAIE;UACJF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAE,SAAA,aAA2B;UAGjCF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAE1CJ,EAF0C,CAAAG,YAAA,EAAM,EACxC,EACF;UAKJH,EAFF,CAAAC,cAAA,eAAoC,4BASS;UAiB3CD,EAdA,CAAAqB,UAAA,KAAAwU,gDAAA,2BAAuC,KAAAC,gDAAA,4BAcwB;UAkCrE9V,EAJI,CAAAG,YAAA,EAAmB,EAGf,EACF;UAGNH,EAAA,CAAAqB,UAAA,KAAA0U,uCAAA,oBAA4C;UAlH5C/V,EAAA,CAAAG,YAAA,EAAiD;;;UAUnCH,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAA0B,UAAA,UAAA0T,GAAA,CAAAtK,kBAAA,GAA8B;UAGjC9K,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAgW,UAAA,CAAAZ,GAAA,CAAAvK,iBAAA,GAA6B;UAuBxB7K,EAAA,CAAAK,SAAA,IAA0D;UAA1DL,EAAA,CAAA0B,UAAA,SAAA0T,GAAA,CAAApN,WAAA,kBAAAoN,GAAA,CAAApN,WAAA,CAAApG,MAAA,kCAAA5B,EAAA,CAAA6B,aAAA,CAA0D;UAmBlE7B,EAAA,CAAAK,SAAA,GAA8C;UAK9CL,EALA,CAAA0B,UAAA,oBAAA0T,GAAA,CAAAnM,YAAA,CAAAC,aAAA,CAA8C,kBAAAkM,GAAA,CAAAnM,YAAA,CAAAE,YAAA,CACH,oBACxB,aAAAiM,GAAA,CAAAnM,YAAA,CAAAM,QAAA,CACe,SAAA6L,GAAA,CAAAnM,YAAA,CAAAU,IAAA,CACR,gBAAAyL,GAAA,CAAAnM,YAAA,CAAAW,WAAA,CACc;UAG3B5J,EAAA,CAAAK,SAAA,EAAsB;UAAtBL,EAAA,CAAA0B,UAAA,SAAA0T,GAAA,CAAAnN,gBAAA,CAAsB;UAcDjI,EAAA,CAAAK,SAAA,EAAY;UAAZL,EAAA,CAAA0B,UAAA,YAAA0T,GAAA,CAAApT,OAAA,CAAY;UAqCxBhC,EAAA,CAAAK,SAAA,EAAY;UAAZL,EAAA,CAAA0B,UAAA,SAAA0T,GAAA,CAAAlN,MAAA,CAAY;;;qBDtC9BtI,YAAY,EAAAqW,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEtW,WAAW;MAAAuW,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}