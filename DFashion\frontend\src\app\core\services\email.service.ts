import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface EmailNotification {
  type: 'order_confirmation' | 'payment_success' | 'payment_failed' | 'order_shipped' | 'order_delivered' | 'password_reset';
  to: string;
  subject: string;
  data: any;
}

export interface BillEmailData {
  userId: string;
  name: string;
  email: string;
  message: string;
  paymentType: {
    method: string;
    paymentId: string;
    orderId: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  sendBillEmail(formData: FormData): Observable<any> {
    return this.http.post(`${this.apiUrl}/email/send-bill`, formData);
  }

  sendPasswordResetEmail(email: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/email/forgot-password`, { email });
  }

  sendOrderConfirmationEmail(orderData: any): Observable<any> {
    const emailData = {
      type: 'order_confirmation',
      to: orderData.customerEmail,
      subject: 'Order Confirmation - DFashion',
      data: {
        customerName: orderData.customerName,
        orderId: orderData.orderId,
        items: orderData.items,
        total: orderData.total,
        orderDate: orderData.orderDate
      }
    };
    
    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);
  }

  sendPaymentSuccessEmail(paymentData: any): Observable<any> {
    const emailData = {
      type: 'payment_success',
      to: paymentData.customerEmail,
      subject: 'Payment Successful - DFashion',
      data: {
        customerName: paymentData.customerName,
        orderId: paymentData.orderId,
        paymentId: paymentData.paymentId,
        amount: paymentData.amount,
        paymentMethod: paymentData.paymentMethod,
        paymentDate: new Date()
      }
    };
    
    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);
  }

  sendPaymentFailedEmail(paymentData: any): Observable<any> {
    const emailData = {
      type: 'payment_failed',
      to: paymentData.customerEmail,
      subject: 'Payment Failed - DFashion',
      data: {
        customerName: paymentData.customerName,
        orderId: paymentData.orderId,
        amount: paymentData.amount,
        failureReason: paymentData.failureReason || 'Payment processing failed',
        retryLink: `${environment.frontendUrl}/checkout/${paymentData.orderId}`
      }
    };
    
    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);
  }

  sendOrderShippedEmail(orderData: any): Observable<any> {
    const emailData = {
      type: 'order_shipped',
      to: orderData.customerEmail,
      subject: 'Your Order has been Shipped - DFashion',
      data: {
        customerName: orderData.customerName,
        orderId: orderData.orderId,
        trackingNumber: orderData.trackingNumber,
        estimatedDelivery: orderData.estimatedDelivery,
        shippingAddress: orderData.shippingAddress
      }
    };
    
    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);
  }

  sendOrderDeliveredEmail(orderData: any): Observable<any> {
    const emailData = {
      type: 'order_delivered',
      to: orderData.customerEmail,
      subject: 'Order Delivered - DFashion',
      data: {
        customerName: orderData.customerName,
        orderId: orderData.orderId,
        deliveryDate: orderData.deliveryDate,
        feedbackLink: `${environment.frontendUrl}/feedback/${orderData.orderId}`
      }
    };
    
    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);
  }

  // Email templates for different notification types
  getEmailTemplate(type: string): any {
    const templates = {
      order_confirmation: {
        subject: 'Order Confirmation - DFashion',
        template: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">DFashion</h1>
              <p style="color: white; margin: 5px 0;">Order Confirmation</p>
            </div>
            <div style="padding: 20px; background: #f8f9fa;">
              <h2>Hi {{customerName}},</h2>
              <p>Thank you for your order! We're excited to confirm that we've received your order.</p>
              <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h3>Order Details:</h3>
                <p><strong>Order ID:</strong> {{orderId}}</p>
                <p><strong>Order Date:</strong> {{orderDate}}</p>
                <p><strong>Total Amount:</strong> ₹{{total}}</p>
              </div>
              <p>We'll send you another email when your order ships.</p>
              <p>Best regards,<br>DFashion Team</p>
            </div>
          </div>
        `
      },
      payment_success: {
        subject: 'Payment Successful - DFashion',
        template: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">Payment Successful!</h1>
            </div>
            <div style="padding: 20px; background: #f8f9fa;">
              <h2>Hi {{customerName}},</h2>
              <p>Your payment has been processed successfully!</p>
              <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h3>Payment Details:</h3>
                <p><strong>Payment ID:</strong> {{paymentId}}</p>
                <p><strong>Order ID:</strong> {{orderId}}</p>
                <p><strong>Amount:</strong> ₹{{amount}}</p>
                <p><strong>Payment Method:</strong> {{paymentMethod}}</p>
              </div>
              <p>Your order is now being processed and will be shipped soon.</p>
              <p>Best regards,<br>DFashion Team</p>
            </div>
          </div>
        `
      },
      payment_failed: {
        subject: 'Payment Failed - DFashion',
        template: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">Payment Failed</h1>
            </div>
            <div style="padding: 20px; background: #f8f9fa;">
              <h2>Hi {{customerName}},</h2>
              <p>We're sorry, but your payment could not be processed.</p>
              <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h3>Order Details:</h3>
                <p><strong>Order ID:</strong> {{orderId}}</p>
                <p><strong>Amount:</strong> ₹{{amount}}</p>
                <p><strong>Reason:</strong> {{failureReason}}</p>
              </div>
              <p>Please try again or contact our support team for assistance.</p>
              <a href="{{retryLink}}" style="background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;">Retry Payment</a>
              <p>Best regards,<br>DFashion Team</p>
            </div>
          </div>
        `
      }
    };
    
    return templates[type as keyof typeof templates] || null;
  }

  // Utility method to replace template variables
  processTemplate(template: string, data: any): string {
    let processedTemplate = template;
    
    Object.keys(data).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processedTemplate = processedTemplate.replace(regex, data[key]);
    });
    
    return processedTemplate;
  }
}
