{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host } from './index-a1a47f01.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-1bf57181.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropIosStyle0 = backdropIosCss;\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropMdStyle0 = backdropMdCss;\nconst Backdrop = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n    this.blocker = GESTURE_CONTROLLER.createBlocker({\n      disableScroll: true\n    });\n    this.visible = true;\n    this.tappable = true;\n    this.stopPropagation = true;\n  }\n  connectedCallback() {\n    if (this.stopPropagation) {\n      this.blocker.block();\n    }\n  }\n  disconnectedCallback() {\n    this.blocker.unblock();\n  }\n  onMouseDown(ev) {\n    this.emitTap(ev);\n  }\n  emitTap(ev) {\n    if (this.stopPropagation) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n    if (this.tappable) {\n      this.ionBackdropTap.emit();\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '16b1328f4a058b8d3752e58dc56c44bed556c425',\n      tabindex: \"-1\",\n      \"aria-hidden\": \"true\",\n      class: {\n        [mode]: true,\n        'backdrop-hide': !this.visible,\n        'backdrop-no-tappable': !this.tappable\n      }\n    });\n  }\n};\nBackdrop.style = {\n  ios: IonBackdropIosStyle0,\n  md: IonBackdropMdStyle0\n};\nexport { Backdrop as ion_backdrop };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}