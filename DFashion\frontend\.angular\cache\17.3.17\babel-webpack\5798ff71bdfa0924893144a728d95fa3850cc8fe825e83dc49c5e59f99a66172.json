{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction ShopComponent_div_34_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sub_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(sub_r4);\n  }\n}\nfunction ShopComponent_div_34_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"+\", category_r2.subcategories.length - 3, \" more\");\n  }\n}\nfunction ShopComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_34_Template_div_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToCategory(category_r2.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"div\", 35)(4, \"span\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 38);\n    i0.ɵɵtemplate(10, ShopComponent_div_34_span_10_Template, 2, 1, \"span\", 39)(11, ShopComponent_div_34_span_11_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r2.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", category_r2.productCount, \"+ Products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r2.subcategories.slice(0, 3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r2.subcategories.length > 3);\n  }\n}\nfunction ShopComponent_div_42_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtext(1, \"Popular\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_42_Template_div_click_0_listener() {\n      const brand_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r6.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 43);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ShopComponent_div_42_span_5_Template, 2, 0, \"span\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", brand_r6.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r6.isPopular);\n  }\n}\nfunction ShopComponent_div_52_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \"Trending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_52_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1, \"New\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_52_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getDiscountPercentage(product_r8), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_52_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 72);\n  }\n}\nfunction ShopComponent_div_52_div_17_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 73);\n  }\n}\nfunction ShopComponent_div_52_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵtemplate(2, ShopComponent_div_52_div_17_i_2_Template, 1, 0, \"i\", 69)(3, ShopComponent_div_52_div_17_i_3_Template, 1, 0, \"i\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 71);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStars(product_r8.rating.average));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getEmptyStars(product_r8.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", product_r8.rating.average, \" (\", product_r8.rating.count, \")\");\n  }\n}\nfunction ShopComponent_div_52_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r8.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction ShopComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_52_Template_div_click_0_listener() {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"div\", 48);\n    i0.ɵɵtemplate(4, ShopComponent_div_52_span_4_Template, 2, 0, \"span\", 49)(5, ShopComponent_div_52_span_5_Template, 2, 0, \"span\", 50)(6, ShopComponent_div_52_span_6_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 52)(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_52_Template_button_click_8_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToWishlist(product_r8, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_52_Template_button_click_10_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.quickAddToCart(product_r8, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 57)(13, \"h4\", 58);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 59);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ShopComponent_div_52_div_17_Template, 6, 4, \"div\", 60);\n    i0.ɵɵelementStart(18, \"div\", 61)(19, \"span\", 62);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ShopComponent_div_52_span_22_Template, 3, 4, \"span\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r8), i0.ɵɵsanitizeUrl)(\"alt\", product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r8.isTrending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.originalPrice && product_r8.originalPrice > product_r8.price);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r8.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(21, 10, product_r8.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r8.originalPrice && product_r8.originalPrice > product_r8.price);\n  }\n}\nfunction ShopComponent_div_62_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getDiscountPercentage(product_r10), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_62_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 72);\n  }\n}\nfunction ShopComponent_div_62_div_17_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 73);\n  }\n}\nfunction ShopComponent_div_62_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵtemplate(2, ShopComponent_div_62_div_17_i_2_Template, 1, 0, \"i\", 69)(3, ShopComponent_div_62_div_17_i_3_Template, 1, 0, \"i\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 71);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStars(product_r10.rating.average));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getEmptyStars(product_r10.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", product_r10.rating.average, \" (\", product_r10.rating.count, \")\");\n  }\n}\nfunction ShopComponent_div_62_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r10.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction ShopComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_62_Template_div_click_0_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"span\", 65);\n    i0.ɵɵtext(5, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ShopComponent_div_62_span_6_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 52)(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_62_Template_button_click_8_listener($event) {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToWishlist(product_r10, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_62_Template_button_click_10_listener($event) {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.quickAddToCart(product_r10, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 57)(13, \"h4\", 58);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 59);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ShopComponent_div_62_div_17_Template, 6, 4, \"div\", 60);\n    i0.ɵɵelementStart(18, \"div\", 61)(19, \"span\", 62);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ShopComponent_div_62_span_22_Template, 3, 4, \"span\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r10), i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", product_r10.originalPrice && product_r10.originalPrice > product_r10.price);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r10.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r10.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(21, 8, product_r10.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r10.originalPrice && product_r10.originalPrice > product_r10.price);\n  }\n}\nexport let ShopComponent = /*#__PURE__*/(() => {\n  class ShopComponent {\n    constructor(router) {\n      this.router = router;\n      this.searchQuery = '';\n      this.categories = [];\n      this.featuredBrands = [];\n      this.trendingProducts = [];\n      this.newArrivals = [];\n    }\n    ngOnInit() {\n      this.loadCategories();\n      this.loadFeaturedBrands();\n      this.loadTrendingProducts();\n      this.loadNewArrivals();\n    }\n    loadCategories() {\n      this.categories = [{\n        id: 'women',\n        name: 'Women\\'s Fashion',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=300&fit=crop',\n        subcategories: ['Dresses', 'Tops', 'Bottoms', 'Ethnic Wear', 'Accessories'],\n        productCount: 2500\n      }, {\n        id: 'men',\n        name: 'Men\\'s Fashion',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400&h=300&fit=crop',\n        subcategories: ['Shirts', 'T-Shirts', 'Jeans', 'Formal Wear', 'Accessories'],\n        productCount: 1800\n      }, {\n        id: 'kids',\n        name: 'Kids\\' Fashion',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=400&h=300&fit=crop',\n        subcategories: ['Boys', 'Girls', 'Baby', 'Toys', 'Accessories'],\n        productCount: 1200\n      }, {\n        id: 'ethnic',\n        name: 'Ethnic Wear',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400&h=300&fit=crop',\n        subcategories: ['Sarees', 'Kurtas', 'Lehengas', 'Sherwanis', 'Accessories'],\n        productCount: 900\n      }, {\n        id: 'accessories',\n        name: 'Accessories',\n        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=300&fit=crop',\n        subcategories: ['Bags', 'Jewelry', 'Watches', 'Sunglasses', 'Belts'],\n        productCount: 1500\n      }, {\n        id: 'shoes',\n        name: 'Footwear',\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=300&fit=crop',\n        subcategories: ['Sneakers', 'Formal', 'Sandals', 'Boots', 'Sports'],\n        productCount: 800\n      }];\n    }\n    loadFeaturedBrands() {\n      // Load from real API\n      this.featuredBrands = [];\n    }\n    loadTrendingProducts() {\n      // Load from real API\n      this.trendingProducts = [];\n    }\n    loadNewArrivals() {\n      // Load from real API\n      this.newArrivals = [];\n    }\n    search() {\n      if (this.searchQuery.trim()) {\n        this.router.navigate(['/search'], {\n          queryParams: {\n            q: this.searchQuery\n          }\n        });\n      }\n    }\n    navigateToCategory(categoryId) {\n      this.router.navigate(['/category', categoryId]);\n    }\n    navigateToBrand(brandId) {\n      this.router.navigate(['/brand', brandId]);\n    }\n    viewProduct(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    viewAllTrending() {\n      this.router.navigate(['/category/trending']);\n    }\n    viewAllNew() {\n      this.router.navigate(['/category/new-arrivals']);\n    }\n    addToWishlist(product, event) {\n      event.stopPropagation();\n      // TODO: Check authentication first\n      if (!this.isAuthenticated()) {\n        this.showLoginPrompt('add to wishlist');\n        return;\n      }\n      // TODO: Implement wishlist API call\n      console.log('Add to wishlist:', product);\n      this.showSuccessMessage(`${product.name} added to wishlist!`);\n    }\n    quickAddToCart(product, event) {\n      event.stopPropagation();\n      // TODO: Check authentication first\n      if (!this.isAuthenticated()) {\n        this.showLoginPrompt('add to cart');\n        return;\n      }\n      // TODO: Implement add to cart API call\n      console.log('Add to cart:', product);\n      this.showSuccessMessage(`${product.name} added to cart!`);\n    }\n    getProductImage(product) {\n      return product.images[0]?.url || '/assets/images/placeholder.jpg';\n    }\n    getDiscountPercentage(product) {\n      if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    getStars(rating) {\n      return Array(Math.floor(rating)).fill(0);\n    }\n    getEmptyStars(rating) {\n      return Array(5 - Math.floor(rating)).fill(0);\n    }\n    isAuthenticated() {\n      // TODO: Implement actual authentication check\n      return localStorage.getItem('authToken') !== null;\n    }\n    showLoginPrompt(action) {\n      const message = `Please login to ${action}`;\n      if (confirm(`${message}. Would you like to login now?`)) {\n        this.router.navigate(['/auth/login']);\n      }\n    }\n    showSuccessMessage(message) {\n      // TODO: Implement proper toast/notification system\n      alert(message);\n    }\n    static {\n      this.ɵfac = function ShopComponent_Factory(t) {\n        return new (t || ShopComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ShopComponent,\n        selectors: [[\"app-shop\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 92,\n        vars: 5,\n        consts: [[1, \"shop-container\"], [1, \"hero-banner\"], [1, \"hero-content\"], [1, \"hero-search\"], [\"type\", \"text\", \"placeholder\", \"Search for products, brands, categories...\", 3, \"ngModelChange\", \"ngModel\"], [3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"hero-stats\"], [1, \"stat\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"categories-section\"], [1, \"section-header\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"brands-section\"], [1, \"brands-grid\"], [\"class\", \"brand-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"trending-section\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"new-arrivals-section\"], [1, \"quick-links-section\"], [1, \"quick-links-grid\"], [1, \"quick-link\", 3, \"click\"], [1, \"fas\", \"fa-female\"], [1, \"fas\", \"fa-male\"], [1, \"fas\", \"fa-child\"], [1, \"fas\", \"fa-star-and-crescent\"], [1, \"fas\", \"fa-gem\"], [1, \"fas\", \"fa-shoe-prints\"], [1, \"category-card\", 3, \"click\"], [1, \"category-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"category-overlay\"], [1, \"product-count\"], [1, \"category-info\"], [1, \"subcategories\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"more\", 4, \"ngIf\"], [1, \"more\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-logo\"], [\"class\", \"popular-badge\", 4, \"ngIf\"], [1, \"popular-badge\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [1, \"product-badges\"], [\"class\", \"badge trending\", 4, \"ngIf\"], [\"class\", \"badge new\", 4, \"ngIf\"], [\"class\", \"badge discount\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"btn-quick-add\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"badge\", \"trending\"], [1, \"badge\", \"new\"], [1, \"badge\", \"discount\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"far fa-star\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"fas\", \"fa-star\"], [1, \"far\", \"fa-star\"], [1, \"original-price\"]],\n        template: function ShopComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"Discover Fashion That Defines You\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Explore thousands of products from top brands\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"input\", 4);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ShopComponent_Template_input_ngModelChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_button_click_9_listener() {\n              return ctx.search();\n            });\n            i0.ɵɵelement(10, \"i\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"span\", 9);\n            i0.ɵɵtext(14, \"10K+\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"span\", 10);\n            i0.ɵɵtext(16, \"Products\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 8)(18, \"span\", 9);\n            i0.ɵɵtext(19, \"500+\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"span\", 10);\n            i0.ɵɵtext(21, \"Brands\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 8)(23, \"span\", 9);\n            i0.ɵɵtext(24, \"50K+\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"span\", 10);\n            i0.ɵɵtext(26, \"Happy Customers\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(27, \"section\", 11)(28, \"div\", 12)(29, \"h2\");\n            i0.ɵɵtext(30, \"Shop by Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"p\");\n            i0.ɵɵtext(32, \"Find exactly what you're looking for\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"div\", 13);\n            i0.ɵɵtemplate(34, ShopComponent_div_34_Template, 12, 6, \"div\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"section\", 15)(36, \"div\", 12)(37, \"h2\");\n            i0.ɵɵtext(38, \"Featured Brands\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"p\");\n            i0.ɵɵtext(40, \"Shop from your favorite brands\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 16);\n            i0.ɵɵtemplate(42, ShopComponent_div_42_Template, 6, 4, \"div\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"section\", 18)(44, \"div\", 12)(45, \"h2\");\n            i0.ɵɵtext(46, \"Trending Now\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"p\");\n            i0.ɵɵtext(48, \"What everyone's buying\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_button_click_49_listener() {\n              return ctx.viewAllTrending();\n            });\n            i0.ɵɵtext(50, \"View All\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"div\", 20);\n            i0.ɵɵtemplate(52, ShopComponent_div_52_Template, 23, 13, \"div\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"section\", 22)(54, \"div\", 12)(55, \"h2\");\n            i0.ɵɵtext(56, \"New Arrivals\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"p\");\n            i0.ɵɵtext(58, \"Fresh styles just dropped\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_button_click_59_listener() {\n              return ctx.viewAllNew();\n            });\n            i0.ɵɵtext(60, \"View All\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(61, \"div\", 20);\n            i0.ɵɵtemplate(62, ShopComponent_div_62_Template, 23, 11, \"div\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"section\", 23)(64, \"div\", 12)(65, \"h2\");\n            i0.ɵɵtext(66, \"Quick Links\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"div\", 24)(68, \"div\", 25);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_68_listener() {\n              return ctx.navigateToCategory(\"women\");\n            });\n            i0.ɵɵelement(69, \"i\", 26);\n            i0.ɵɵelementStart(70, \"span\");\n            i0.ɵɵtext(71, \"Women's Fashion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(72, \"div\", 25);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_72_listener() {\n              return ctx.navigateToCategory(\"men\");\n            });\n            i0.ɵɵelement(73, \"i\", 27);\n            i0.ɵɵelementStart(74, \"span\");\n            i0.ɵɵtext(75, \"Men's Fashion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(76, \"div\", 25);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_76_listener() {\n              return ctx.navigateToCategory(\"kids\");\n            });\n            i0.ɵɵelement(77, \"i\", 28);\n            i0.ɵɵelementStart(78, \"span\");\n            i0.ɵɵtext(79, \"Kids' Fashion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(80, \"div\", 25);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_80_listener() {\n              return ctx.navigateToCategory(\"ethnic\");\n            });\n            i0.ɵɵelement(81, \"i\", 29);\n            i0.ɵɵelementStart(82, \"span\");\n            i0.ɵɵtext(83, \"Ethnic Wear\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(84, \"div\", 25);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_84_listener() {\n              return ctx.navigateToCategory(\"accessories\");\n            });\n            i0.ɵɵelement(85, \"i\", 30);\n            i0.ɵɵelementStart(86, \"span\");\n            i0.ɵɵtext(87, \"Accessories\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(88, \"div\", 25);\n            i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_88_listener() {\n              return ctx.navigateToCategory(\"shoes\");\n            });\n            i0.ɵɵelement(89, \"i\", 31);\n            i0.ɵɵelementStart(90, \"span\");\n            i0.ɵɵtext(91, \"Footwear\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.featuredBrands);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.trendingProducts);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.newArrivals);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n        styles: [\".shop-container[_ngcontent-%COMP%]{max-width:1400px;margin:0 auto;padding:0 20px}.hero-banner[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:60px 40px;border-radius:20px;margin:20px 0 40px;position:relative;overflow:hidden}.hero-banner[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"25\\\" cy=\\\"25\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"75\\\" cy=\\\"75\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');opacity:.3}.hero-content[_ngcontent-%COMP%]{position:relative;z-index:2;text-align:center;max-width:600px;margin:0 auto}.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;margin-bottom:16px;line-height:1.2}.hero-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:32px;opacity:.9}.hero-search[_ngcontent-%COMP%]{display:flex;max-width:500px;margin:0 auto 40px;background:#fff;border-radius:50px;overflow:hidden;box-shadow:0 10px 30px #0003}.hero-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1;padding:16px 24px;border:none;font-size:1rem;color:#333}.hero-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#999}.hero-search[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:16px 24px;background:#007bff;border:none;color:#fff;cursor:pointer;transition:background .2s}.hero-search[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#0056b3}.hero-stats[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:60px;position:relative;z-index:2}.stat[_ngcontent-%COMP%]{text-align:center}.stat-number[_ngcontent-%COMP%]{display:block;font-size:2rem;font-weight:700;margin-bottom:4px}.stat-label[_ngcontent-%COMP%]{font-size:.9rem;opacity:.8}.section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:40px;position:relative}.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:8px;color:#333}.section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;color:#666;margin:0}.view-all-btn[_ngcontent-%COMP%]{position:absolute;right:0;top:50%;transform:translateY(-50%);background:#007bff;color:#fff;border:none;padding:10px 20px;border-radius:25px;font-weight:500;cursor:pointer;transition:all .2s}.view-all-btn[_ngcontent-%COMP%]:hover{background:#0056b3;transform:translateY(-50%) scale(1.05)}.categories-section[_ngcontent-%COMP%]{margin:60px 0}.categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:24px}.category-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #0000001a;transition:all .3s ease;cursor:pointer}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.category-image[_ngcontent-%COMP%]{position:relative;height:200px;overflow:hidden}.category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.1)}.category-overlay[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent,#000000b3);padding:20px;color:#fff}.product-count[_ngcontent-%COMP%]{font-size:.9rem;font-weight:500}.category-info[_ngcontent-%COMP%]{padding:20px}.category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;margin-bottom:8px;color:#333}.subcategories[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px}.subcategories[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#f8f9fa;color:#666;padding:4px 8px;border-radius:12px;font-size:.8rem}.subcategories[_ngcontent-%COMP%]   .more[_ngcontent-%COMP%]{background:#007bff;color:#fff}.brands-section[_ngcontent-%COMP%]{margin:60px 0;background:#f8f9fa;padding:40px;border-radius:20px}.brands-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:24px}.brand-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:20px;text-align:center;transition:all .3s ease;cursor:pointer;position:relative}.brand-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #0000001a}.brand-logo[_ngcontent-%COMP%]{width:80px;height:80px;margin:0 auto 12px;border-radius:50%;overflow:hidden;background:#f8f9fa;display:flex;align-items:center;justify-content:center}.brand-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.brand-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#333;margin:0}.popular-badge[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:#ff6b6b;color:#fff;padding:2px 8px;border-radius:10px;font-size:.7rem;font-weight:500}.trending-section[_ngcontent-%COMP%], .new-arrivals-section[_ngcontent-%COMP%]{margin:60px 0}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:24px}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 12px #00000014;transition:all .3s ease;cursor:pointer}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.product-image[_ngcontent-%COMP%]{position:relative;height:280px;overflow:hidden}.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.05)}.product-badges[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;display:flex;flex-direction:column;gap:4px}.badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:12px;font-size:.7rem;font-weight:600;text-transform:uppercase}.badge.trending[_ngcontent-%COMP%]{background:#ff6b6b;color:#fff}.badge.new[_ngcontent-%COMP%]{background:#4ecdc4;color:#fff}.badge.discount[_ngcontent-%COMP%]{background:#ffa726;color:#fff}.product-actions[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;display:flex;flex-direction:column;gap:8px;opacity:0;transition:opacity .3s ease}.product-card[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%]{opacity:1}.btn-wishlist[_ngcontent-%COMP%], .btn-quick-add[_ngcontent-%COMP%]{width:36px;height:36px;border:none;border-radius:50%;background:#ffffffe6;color:#333;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.btn-wishlist[_ngcontent-%COMP%]:hover{background:#ff6b6b;color:#fff}.btn-quick-add[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.product-info[_ngcontent-%COMP%]{padding:16px}.product-name[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;margin-bottom:4px;color:#333;line-height:1.3}.product-brand[_ngcontent-%COMP%]{color:#666;font-size:.85rem;margin-bottom:8px}.product-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:8px}.stars[_ngcontent-%COMP%]{display:flex;gap:1px}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem;color:#ffc107}.rating-text[_ngcontent-%COMP%]{font-size:.75rem;color:#666}.product-price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.current-price[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700;color:#333}.original-price[_ngcontent-%COMP%]{font-size:.85rem;color:#999;text-decoration:line-through}.quick-links-section[_ngcontent-%COMP%]{margin:60px 0;background:linear-gradient(135deg,#f093fb,#f5576c);padding:40px;border-radius:20px;color:#fff}.quick-links-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px}.quick-link[_ngcontent-%COMP%]{background:#ffffff1a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:12px;padding:24px;text-align:center;cursor:pointer;transition:all .3s ease;border:1px solid rgba(255,255,255,.2)}.quick-link[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-4px)}.quick-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:12px;display:block}.quick-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}@media (max-width: 768px){.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.hero-stats[_ngcontent-%COMP%]{gap:30px}.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2rem}.view-all-btn[_ngcontent-%COMP%]{position:static;transform:none;margin-top:16px}.categories-grid[_ngcontent-%COMP%], .products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:16px}.brands-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(120px,1fr))}.quick-links-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(150px,1fr))}}\"]\n      });\n    }\n  }\n  return ShopComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}