<div class="instagram-home-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading amazing fashion...</p>
  </div>

  <!-- Instagram-style Layout -->
  <div *ngIf="!isLoading" class="instagram-layout">
    <!-- Main Feed -->
    <div class="main-feed">
      <!-- Instagram Stories Section -->
      <section class="stories-section">

        <app-view-add-stories></app-view-add-stories>
      </section>

      <!-- Instagram-style Posts Feed -->
      <section class="posts-feed">
        <article *ngFor="let post of instagramPosts; trackBy: trackByPostId" class="instagram-post">
          <!-- Post Header -->
          <header class="post-header">
            <div class="user-info">
              <img [src]="post.user.avatar" [alt]="post.user.username" class="user-avatar">
              <div class="user-details">
                <h3 class="username">{{ post.user.username }}</h3>
                <span class="location" *ngIf="post.location">{{ post.location }}</span>
              </div>
            </div>
            <button class="more-options">
              <i class="fas fa-ellipsis-h"></i>
            </button>
          </header>

          <!-- Post Media -->
          <div class="post-media">
            <img [src]="post.mediaUrl" [alt]="post.caption" class="post-image">

            <!-- Central Product Tags Button -->
            <div class="product-tags-overlay" *ngIf="post.products && post.products.length > 0">
              <button class="product-tag-btn" (click)="handleProductTagsClick(post)"
                      [class.active]="post.showProductTags"
                      [title]="getProductTagsButtonTitle(post)">
                <i class="fas fa-shopping-bag"></i>
                <span class="product-count" *ngIf="post.products.length > 1">{{ post.products.length }}</span>
              </button>
            </div>

            <!-- Enhanced Product Tags Overlay -->
            <div class="product-tags-overlay" *ngIf="post.products && post.products.length > 0 && post.showProductTags">
              <button *ngFor="let productTag of post.products"
                      class="product-tag"
                      [class]="'tag-type-' + (productTag.navigationType || 'product')"
                      [style.left.%]="productTag.position?.x || 50"
                      [style.top.%]="productTag.position?.y || 50"
                      (click)="onProductTagClick(productTag)"
                      [title]="getTagTooltip(productTag)">
                <div class="tag-dot" [class]="'dot-' + (productTag.navigationType || 'product')">
                  <i class="tag-icon" [class]="getTagIcon(productTag.navigationType || 'product')"></i>
                </div>
                <div class="product-preview" *ngIf="productTag.showPreview">
                  <img [src]="getTagImageUrl(productTag)" [alt]="getTagName(productTag)">
                  <div class="product-info">
                    <span class="product-name">{{ getTagName(productTag) }}</span>
                    <span class="product-price">{{ getTagSubtitle(productTag) }}</span>
                  </div>
                </div>
              </button>
            </div>
          </div>

          <!-- Post Actions -->
          <div class="post-actions">
            <div class="primary-actions">
              <button class="action-btn like-btn" [class.liked]="post.isLiked" (click)="toggleLike(post)">
                <i [class]="post.isLiked ? 'fas fa-heart' : 'far fa-heart'"></i>
              </button>
              <button class="action-btn comment-btn" (click)="focusCommentInput(post)">
                <i class="far fa-comment"></i>
              </button>
              <button class="action-btn share-btn" (click)="sharePost(post)">
                <i class="far fa-paper-plane"></i>
              </button>
            </div>
            <button class="action-btn save-btn" [class.saved]="post.isSaved" (click)="toggleSave(post)">
              <i [class]="post.isSaved ? 'fas fa-bookmark' : 'far fa-bookmark'"></i>
            </button>
          </div>

          <!-- Likes Count -->
          <div class="likes-section" *ngIf="post.analytics.likes > 0">
            <span class="likes-count">{{ formatLikesCount(post.analytics.likes) }} likes</span>
          </div>

          <!-- Post Caption -->
          <div class="post-caption">
            <span class="username">{{ post.user.username }}</span>
            <span class="caption-text">{{ post.caption }}</span>
          </div>

          <!-- E-commerce Actions -->
          <div class="ecommerce-actions" *ngIf="post.products && post.products.length > 0">
            <div class="product-showcase">
              <div *ngFor="let product of post.products.slice(0, 2)" class="featured-product">
                <img [src]="product.image" [alt]="product.name" class="product-thumbnail">
                <div class="product-details">
                  <span class="product-name">{{ product.name }}</span>
                  <span class="product-price">{{ formatPrice(product.price) }}</span>
                </div>
              </div>
            </div>
            <div class="shopping-buttons">
              <button class="shop-btn buy-btn" (click)="buyNow(post.products[0])">
                <i class="fas fa-bolt"></i>
                Buy Now
              </button>
              <button class="shop-btn wishlist-btn" (click)="addToWishlist(post.products[0])">
                <i class="far fa-heart"></i>
                Wishlist
              </button>
              <button class="shop-btn cart-btn" (click)="addToCart(post.products[0])">
                <i class="fas fa-shopping-cart"></i>
                Add to Cart
              </button>
            </div>
          </div>

          <!-- Comments Preview -->
          <div class="comments-preview" *ngIf="post.comments && post.comments.length > 0">
            <button class="view-comments-btn" (click)="toggleComments(post)">
              View all {{ post.comments.length }} comments
            </button>
            <div class="recent-comments">
              <div *ngFor="let comment of post.comments.slice(0, 2)" class="comment">
                <span class="comment-username">{{ comment.username }}</span>
                <span class="comment-text">{{ comment.text }}</span>
              </div>
            </div>
          </div>

          <!-- Post Time -->
          <div class="post-time">
            {{ getTimeAgo(post.createdAt) }}
          </div>

          <!-- Add Comment -->
          <div class="add-comment-section">
            <input type="text" placeholder="Add a comment..." class="comment-input"
                   [(ngModel)]="newComment" (keyup.enter)="addComment(post)">
            <button class="post-comment-btn" (click)="addComment(post)"
                    [disabled]="!newComment || !newComment.trim()">Post</button>
          </div>
        </article>
      </section>
    </div>

    <!-- Home Sidebar Component with Existing Components -->
    <app-sidebar
      [currentUser]="currentUser"
      [featuredProducts]="featuredProducts"
      [trendingProducts]="trendingProducts"
      [newArrivals]="newArrivals"
      [summerCollection]="summerCollection"
      [categories]="categories">
    </app-sidebar>
  </div>

  <!-- Payment Modal -->
  <app-payment-modal
    [isVisible]="showPaymentModal"
    [paymentData]="paymentModalData"
    (close)="onPaymentModalClose()"
    (paymentCompleted)="onPaymentCompleted($event)">
  </app-payment-modal>
</div>
