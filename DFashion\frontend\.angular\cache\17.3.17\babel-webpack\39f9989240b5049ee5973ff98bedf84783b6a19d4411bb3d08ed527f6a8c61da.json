{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"storyVideo\"];\nconst _c1 = [\"storyThumbnails\"];\nfunction StoriesViewerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r4 === ctx_r1.currentIndex)(\"completed\", i_r4 < ctx_r1.currentIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressWidth(i_r4), \"%\")(\"animation-duration\", ctx_r1.getStoryDuration(story_r3), \"s\");\n  }\n}\nfunction StoriesViewerComponent_div_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSound());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"muted\", ctx_r1.isMuted);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-volume-up\", !ctx_r1.isMuted)(\"fa-volume-mute\", ctx_r1.isMuted);\n  }\n}\nfunction StoriesViewerComponent_div_0_img_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 49, 0);\n    i0.ɵɵlistener(\"load\", function StoriesViewerComponent_div_0_img_20_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption);\n  }\n}\nfunction StoriesViewerComponent_div_0_video_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 50, 1);\n    i0.ɵɵlistener(\"loadeddata\", function StoriesViewerComponent_div_0_video_21_Template_video_loadeddata_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    })(\"ended\", function StoriesViewerComponent_div_0_video_21_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"poster\", ctx_r1.currentStory.media.thumbnail, i0.ɵɵsanitizeUrl)(\"muted\", ctx_r1.isMuted)(\"autoplay\", !ctx_r1.isPaused)(\"loop\", false);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_22_div_1_Template_div_click_0_listener($event) {\n      const productTag_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.showProductModal(productTag_r9.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 52);\n    i0.ɵɵelement(2, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 54)(4, \"span\", 55);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r9 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r9.position.x, \"%\")(\"top\", productTag_r9.position.y, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(productTag_r9.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 6, productTag_r9.product.price, \"1.0-0\"), \"\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, StoriesViewerComponent_div_0_div_22_div_1_Template, 9, 9, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"show-tags\", ctx_r1.showProductTags);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_23_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Tap to view products\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesViewerComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStory.caption, \" \");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_25_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_25_div_6_Template_div_click_0_listener() {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.jumpToStoryIndex(i_r13));\n    });\n    i0.ɵɵelement(1, \"img\", 69);\n    i0.ɵɵelementStart(2, \"div\", 70);\n    i0.ɵɵelement(3, \"img\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r14 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", i_r13 === ctx_r1.currentIndex)(\"viewed\", i_r13 < ctx_r1.currentIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getStoryThumbnail(story_r14), i0.ɵɵsanitizeUrl)(\"alt\", story_r14.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", story_r14.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", story_r14.user.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getThumbnailProgress(i_r13), \"%\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_25_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesLeft());\n    });\n    i0.ɵɵelement(3, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 64, 2);\n    i0.ɵɵtemplate(6, StoriesViewerComponent_div_0_div_25_div_6_Template, 5, 10, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_25_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesRight());\n    });\n    i0.ɵɵelement(8, \"i\", 67);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollLeft);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollRight);\n  }\n}\nfunction StoriesViewerComponent_div_0_img_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 73, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption);\n  }\n}\nfunction StoriesViewerComponent_div_0_video_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 74, 1);\n    i0.ɵɵlistener(\"ended\", function StoriesViewerComponent_div_0_video_28_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"poster\", ctx_r1.currentStory.media.thumbnail, i0.ɵɵsanitizeUrl)(\"muted\", ctx_r1.isMuted)(\"autoplay\", true)(\"loop\", false);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_30_Template_div_click_0_listener() {\n      const productTag_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showProductModal(productTag_r17.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 75);\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 76)(4, \"span\", 55);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r17 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r17.position.x, \"%\")(\"top\", productTag_r17.position.y, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(productTag_r17.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 6, productTag_r17.product.price, \"1.0-0\"), \"\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStory.caption, \" \");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_35_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 79);\n    i0.ɵɵelementStart(3, \"span\", 80);\n    i0.ɵɵtext(4, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 40);\n    i0.ɵɵtext(6, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_35_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(8, \"i\", 82);\n    i0.ɵɵelementStart(9, \"span\", 80);\n    i0.ɵɵtext(10, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 40);\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_35_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(14, \"i\", 39);\n    i0.ɵɵelementStart(15, \"span\", 80);\n    i0.ɵɵtext(16, \"Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 40);\n    i0.ɵɵtext(18, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Buy Now - \" + ctx_r1.getProductName());\n    i0.ɵɵadvance(6);\n    i0.ɵɵattribute(\"aria-label\", \"Add to Cart - \" + ctx_r1.getProductName());\n    i0.ɵɵadvance(6);\n    i0.ɵɵattribute(\"aria-label\", \"Add to Wishlist - \" + ctx_r1.getProductName());\n  }\n}\nfunction StoriesViewerComponent_div_0_button_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_button_49_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSound());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementStart(2, \"span\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-volume-up\", !ctx_r1.isMuted)(\"fa-volume-mute\", ctx_r1.isMuted);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.isMuted ? \"Unmute\" : \"Mute\");\n  }\n}\nfunction StoriesViewerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleStoryClick($event));\n    })(\"keydown\", function StoriesViewerComponent_div_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleKeyDown($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵtemplate(2, StoriesViewerComponent_div_0_div_2_Template, 2, 8, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"div\", 10);\n    i0.ɵɵelement(5, \"img\", 11);\n    i0.ɵɵelementStart(6, \"div\", 12)(7, \"span\", 13);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 15);\n    i0.ɵɵtemplate(12, StoriesViewerComponent_div_0_button_12_Template, 2, 6, \"button\", 16);\n    i0.ɵɵelementStart(13, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.togglePause());\n    });\n    i0.ɵɵelement(14, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 21);\n    i0.ɵɵlistener(\"touchstart\", function StoriesViewerComponent_div_0_Template_div_touchstart_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchend\", function StoriesViewerComponent_div_0_Template_div_touchend_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    })(\"mousedown\", function StoriesViewerComponent_div_0_Template_div_mousedown_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMouseDown($event));\n    })(\"mouseup\", function StoriesViewerComponent_div_0_Template_div_mouseup_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMouseUp($event));\n    });\n    i0.ɵɵelementStart(18, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_19_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, StoriesViewerComponent_div_0_img_20_Template, 2, 2, \"img\", 24)(21, StoriesViewerComponent_div_0_video_21_Template, 2, 5, \"video\", 25)(22, StoriesViewerComponent_div_0_div_22_Template, 2, 3, \"div\", 26)(23, StoriesViewerComponent_div_0_div_23_Template, 4, 0, \"div\", 27)(24, StoriesViewerComponent_div_0_div_24_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, StoriesViewerComponent_div_0_div_25_Template, 9, 3, \"div\", 29);\n    i0.ɵɵelementStart(26, \"div\", 30);\n    i0.ɵɵtemplate(27, StoriesViewerComponent_div_0_img_27_Template, 2, 2, \"img\", 31)(28, StoriesViewerComponent_div_0_video_28_Template, 2, 5, \"video\", 32);\n    i0.ɵɵelementStart(29, \"div\", 33);\n    i0.ɵɵtemplate(30, StoriesViewerComponent_div_0_div_30_Template, 9, 9, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, StoriesViewerComponent_div_0_div_31_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_32_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 35);\n    i0.ɵɵtemplate(35, StoriesViewerComponent_div_0_div_35_Template, 19, 3, \"div\", 36);\n    i0.ɵɵelementStart(36, \"div\", 37)(37, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleLike());\n    });\n    i0.ɵɵelement(38, \"i\", 39);\n    i0.ɵɵelementStart(39, \"span\", 40);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(42, \"i\", 42);\n    i0.ɵɵelementStart(43, \"span\", 40);\n    i0.ɵɵtext(44, \"Comment\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareStory());\n    });\n    i0.ɵɵelement(46, \"i\", 44);\n    i0.ɵɵelementStart(47, \"span\", 40);\n    i0.ɵɵtext(48, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(49, StoriesViewerComponent_div_0_button_49_Template, 4, 5, \"button\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.currentStory.createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"paused\", ctx_r1.isPaused);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-pause\", !ctx_r1.isPaused)(\"fa-play\", ctx_r1.isPaused);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products && ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products && ctx_r1.currentStory.products.length > 0 && !ctx_r1.showProductTags);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.stories.length > 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products && ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isLiked);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.isLiked ? \"Unlike\" : \"Like\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n  }\n}\nfunction StoriesViewerComponent_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r1.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction StoriesViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 86);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 87)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 88);\n    i0.ɵɵelement(8, \"img\", 89);\n    i0.ɵɵelementStart(9, \"div\", 90)(10, \"p\", 91);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 92)(13, \"span\", 93);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, StoriesViewerComponent_div_1_span_16_Template, 3, 4, \"span\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 95)(18, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.images[0] == null ? null : ctx_r1.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r1.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedProduct.originalPrice);\n  }\n}\nfunction StoriesViewerComponent_div_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"img\", 108);\n    i0.ɵɵelementStart(2, \"div\", 109)(3, \"span\", 110);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 111);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 112);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const comment_r22 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", comment_r22.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", comment_r22.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(comment_r22.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r22.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(comment_r22.commentedAt));\n  }\n}\nfunction StoriesViewerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelementStart(1, \"div\", 86);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 87)(3, \"h3\");\n    i0.ɵɵtext(4, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelement(6, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 101);\n    i0.ɵɵtemplate(8, StoriesViewerComponent_div_2_div_8_Template, 9, 5, \"div\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 103)(10, \"input\", 104);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoriesViewerComponent_div_2_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newComment, $event) || (ctx_r1.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function StoriesViewerComponent_div_2_Template_input_keyup_enter_10_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelement(12, \"i\", 106);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.comments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newComment.trim());\n  }\n}\nexport let StoriesViewerComponent = /*#__PURE__*/(() => {\n  class StoriesViewerComponent {\n    constructor(router, route) {\n      this.router = router;\n      this.route = route;\n      this.stories = [];\n      this.currentIndex = 0;\n      this.isLiked = false;\n      this.isMuted = true;\n      this.isPaused = false;\n      this.showProductTags = false;\n      this.selectedProduct = null;\n      this.showCommentsModal = false;\n      this.comments = [];\n      this.newComment = '';\n      // Touch handling\n      this.touchStartTime = 0;\n      // Navigation slider properties\n      this.canScrollLeft = false;\n      this.canScrollRight = false;\n      this.storyDuration = 15000; // 15 seconds default\n    }\n    ngOnInit() {\n      // Handle query parameters for story index\n      this.route.queryParams.subscribe(queryParams => {\n        if (queryParams['index']) {\n          this.currentIndex = parseInt(queryParams['index'], 10) || 0;\n        }\n      });\n      this.route.params.subscribe(params => {\n        if (params['userId']) {\n          this.loadUserStories(params['userId']);\n        } else {\n          this.loadStories();\n        }\n        if (params['storyId']) {\n          this.jumpToStory(params['storyId']);\n        }\n      });\n      // Add keyboard listeners for better UX\n      this.addKeyboardListeners();\n      this.addTouchListeners();\n      // Initialize navigation slider after view init\n      setTimeout(() => {\n        this.updateScrollButtons();\n        this.updateNavigationSlider();\n      }, 100);\n    }\n    ngOnDestroy() {\n      if (this.progressTimer) {\n        clearTimeout(this.progressTimer);\n      }\n      if (this.longPressTimer) {\n        clearTimeout(this.longPressTimer);\n      }\n    }\n    // Instagram-like interactions\n    togglePause() {\n      this.isPaused = !this.isPaused;\n      if (this.storyVideo) {\n        if (this.isPaused) {\n          this.storyVideo.nativeElement.pause();\n          clearTimeout(this.progressTimer);\n        } else {\n          this.storyVideo.nativeElement.play();\n          this.startStoryTimer();\n        }\n      }\n    }\n    toggleProductTags() {\n      this.showProductTags = !this.showProductTags;\n      if (this.showProductTags) {\n        setTimeout(() => {\n          this.showProductTags = false;\n        }, 3000);\n      }\n    }\n    onTouchStart(event) {\n      this.touchStartTime = Date.now();\n      this.longPressTimer = setTimeout(() => {\n        this.isPaused = true;\n        if (this.storyVideo) {\n          this.storyVideo.nativeElement.pause();\n        }\n        clearTimeout(this.progressTimer);\n      }, 200);\n    }\n    onTouchEnd(event) {\n      clearTimeout(this.longPressTimer);\n      if (this.isPaused && Date.now() - this.touchStartTime > 200) {\n        this.isPaused = false;\n        if (this.storyVideo) {\n          this.storyVideo.nativeElement.play();\n        }\n        this.startStoryTimer();\n      }\n    }\n    onMouseDown(event) {\n      this.touchStartTime = Date.now();\n      this.longPressTimer = setTimeout(() => {\n        this.isPaused = true;\n        if (this.storyVideo) {\n          this.storyVideo.nativeElement.pause();\n        }\n        clearTimeout(this.progressTimer);\n      }, 200);\n    }\n    onMouseUp(event) {\n      clearTimeout(this.longPressTimer);\n      if (this.isPaused && Date.now() - this.touchStartTime > 200) {\n        this.isPaused = false;\n        if (this.storyVideo) {\n          this.storyVideo.nativeElement.play();\n        }\n        this.startStoryTimer();\n      }\n    }\n    onMediaLoaded() {\n      // Media loaded, start timer\n      this.startStoryTimer();\n    }\n    getStoryDuration(story) {\n      return story.media.type === 'video' ? story.media.duration : 15;\n    }\n    handleKeyDown(event) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          this.previousStory();\n          break;\n        case 'ArrowRight':\n        case ' ':\n          this.nextStory();\n          break;\n        case 'Escape':\n          this.closeStories();\n          break;\n      }\n    }\n    loadStories() {\n      // Load stories from real API\n      fetch('http://localhost:5000/api/stories').then(response => response.json()).then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter(story => story.isActive);\n          if (this.stories.length > 0) {\n            // Use the index from query params or default to 0\n            const startIndex = Math.min(this.currentIndex, this.stories.length - 1);\n            this.currentIndex = startIndex;\n            this.currentStory = this.stories[startIndex];\n            this.startStoryTimer();\n            // Initialize navigation slider\n            setTimeout(() => {\n              this.updateScrollButtons();\n              this.updateNavigationSlider();\n            }, 100);\n          }\n        }\n      }).catch(error => {\n        console.error('Error loading stories:', error);\n        // Show error message to user\n        alert('Failed to load stories. Please try again.');\n      });\n    }\n    loadUserStories(userId) {\n      // Load specific user's stories from real API\n      fetch(`http://localhost:5000/api/stories/user/${userId}`).then(response => response.json()).then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter(story => story.isActive);\n          if (this.stories.length > 0) {\n            this.currentStory = this.stories[0];\n            this.startStoryTimer();\n          }\n        }\n      }).catch(error => {\n        console.error('Error loading user stories:', error);\n      });\n    }\n    jumpToStory(storyId) {\n      const index = this.stories.findIndex(s => s._id === storyId);\n      if (index !== -1) {\n        this.currentIndex = index;\n        this.currentStory = this.stories[index];\n        this.startStoryTimer();\n      }\n    }\n    startStoryTimer() {\n      if (this.progressTimer) {\n        clearTimeout(this.progressTimer);\n      }\n      const duration = this.currentStory.media.type === 'video' ? this.currentStory.media.duration * 1000 : this.storyDuration;\n      this.progressTimer = setTimeout(() => {\n        this.nextStory();\n      }, duration);\n    }\n    nextStory() {\n      if (this.currentIndex < this.stories.length - 1) {\n        this.currentIndex++;\n        this.currentStory = this.stories[this.currentIndex];\n        this.startStoryTimer();\n        this.updateNavigationSlider();\n      } else {\n        this.closeStories();\n      }\n    }\n    previousStory() {\n      if (this.currentIndex > 0) {\n        this.currentIndex--;\n        this.currentStory = this.stories[this.currentIndex];\n        this.startStoryTimer();\n        this.updateNavigationSlider();\n      }\n    }\n    jumpToStoryIndex(index) {\n      if (index >= 0 && index < this.stories.length && index !== this.currentIndex) {\n        this.currentIndex = index;\n        this.currentStory = this.stories[index];\n        this.startStoryTimer();\n        this.updateNavigationSlider();\n      }\n    }\n    handleStoryClick(event) {\n      const rect = event.target.getBoundingClientRect();\n      const clickX = event.clientX - rect.left;\n      const width = rect.width;\n      if (clickX < width * 0.3) {\n        this.previousStory();\n      } else if (clickX > width * 0.7) {\n        this.nextStory();\n      }\n    }\n    getProgressWidth(index) {\n      if (index < this.currentIndex) return 100;\n      if (index > this.currentIndex) return 0;\n      return 0; // Will be animated by CSS\n    }\n    // Navigation slider methods\n    getStoryThumbnail(story) {\n      if (story.media.type === 'video' && story.media.thumbnail) {\n        return story.media.thumbnail;\n      }\n      return story.media.url;\n    }\n    getThumbnailProgress(index) {\n      if (index < this.currentIndex) return 100;\n      if (index > this.currentIndex) return 0;\n      if (index === this.currentIndex) {\n        // Calculate current progress based on timer\n        return 0; // Will be updated by progress animation\n      }\n      return 0;\n    }\n    scrollStoriesLeft() {\n      if (this.storyThumbnails) {\n        const container = this.storyThumbnails.nativeElement;\n        container.scrollBy({\n          left: -200,\n          behavior: 'smooth'\n        });\n        setTimeout(() => this.updateScrollButtons(), 300);\n      }\n    }\n    scrollStoriesRight() {\n      if (this.storyThumbnails) {\n        const container = this.storyThumbnails.nativeElement;\n        container.scrollBy({\n          left: 200,\n          behavior: 'smooth'\n        });\n        setTimeout(() => this.updateScrollButtons(), 300);\n      }\n    }\n    updateNavigationSlider() {\n      if (this.storyThumbnails && this.stories.length > 5) {\n        const container = this.storyThumbnails.nativeElement;\n        const thumbnailWidth = 56; // 48px + 8px gap\n        const containerWidth = container.clientWidth;\n        const currentThumbnailPosition = this.currentIndex * thumbnailWidth;\n        // Center the current thumbnail\n        const scrollPosition = currentThumbnailPosition - containerWidth / 2 + thumbnailWidth / 2;\n        container.scrollTo({\n          left: scrollPosition,\n          behavior: 'smooth'\n        });\n        setTimeout(() => this.updateScrollButtons(), 300);\n      }\n    }\n    updateScrollButtons() {\n      if (this.storyThumbnails) {\n        const container = this.storyThumbnails.nativeElement;\n        this.canScrollLeft = container.scrollLeft > 0;\n        this.canScrollRight = container.scrollLeft < container.scrollWidth - container.clientWidth;\n      }\n    }\n    closeStories() {\n      // Navigate back to the previous page or home\n      if (window.history.length > 1) {\n        window.history.back();\n      } else {\n        this.router.navigate(['/social']);\n      }\n    }\n    // E-commerce actions\n    buyNow() {\n      if (this.currentStory.products.length > 0) {\n        const product = this.currentStory.products[0].product;\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            productId: product._id,\n            source: 'story'\n          }\n        });\n      }\n    }\n    addToCart() {\n      if (this.currentStory.products.length > 0) {\n        const product = this.currentStory.products[0].product;\n        // TODO: Add to cart via service\n        console.log('Add to cart from story:', product);\n      }\n    }\n    addToWishlist() {\n      if (this.currentStory.products.length > 0) {\n        const product = this.currentStory.products[0].product;\n        // TODO: Add to wishlist via service\n        console.log('Add to wishlist from story:', product);\n      }\n    }\n    // Social actions\n    toggleLike() {\n      this.isLiked = !this.isLiked;\n      // TODO: Like/unlike story via API\n    }\n    openComments() {\n      this.showCommentsModal = true;\n      this.loadComments();\n    }\n    closeComments() {\n      this.showCommentsModal = false;\n    }\n    shareStory() {\n      // TODO: Implement share functionality\n      console.log('Share story:', this.currentStory);\n    }\n    toggleSound() {\n      this.isMuted = !this.isMuted;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.muted = this.isMuted;\n      }\n    }\n    // Product modal\n    showProductModal(product) {\n      this.selectedProduct = product;\n    }\n    closeProductModal() {\n      this.selectedProduct = null;\n    }\n    buyProductNow() {\n      if (this.selectedProduct) {\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            productId: this.selectedProduct._id,\n            source: 'story'\n          }\n        });\n      }\n    }\n    addProductToCart() {\n      if (this.selectedProduct) {\n        // TODO: Add to cart via service\n        console.log('Add product to cart:', this.selectedProduct);\n        this.closeProductModal();\n      }\n    }\n    addProductToWishlist() {\n      if (this.selectedProduct) {\n        // TODO: Add to wishlist via service\n        console.log('Add product to wishlist:', this.selectedProduct);\n        this.closeProductModal();\n      }\n    }\n    // Comments\n    loadComments() {\n      // Load comments from API\n      this.comments = [];\n    }\n    addComment() {\n      if (this.newComment.trim()) {\n        // TODO: Add comment via API\n        console.log('Add comment:', this.newComment);\n        this.newComment = '';\n      }\n    }\n    getTimeAgo(date) {\n      const now = new Date();\n      const diffMs = now.getTime() - new Date(date).getTime();\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      if (diffHours < 1) return 'now';\n      if (diffHours < 24) return `${diffHours}h`;\n      return `${Math.floor(diffHours / 24)}d`;\n    }\n    getProductName() {\n      return this.currentStory?.products?.[0]?.product?.name || 'Product';\n    }\n    // Keyboard and touch event listeners\n    addKeyboardListeners() {\n      document.addEventListener('keydown', event => {\n        if (event.key === 'ArrowLeft') {\n          this.previousStory();\n        } else if (event.key === 'ArrowRight') {\n          this.nextStory();\n        } else if (event.key === 'Escape') {\n          this.closeStories();\n        }\n      });\n    }\n    addTouchListeners() {\n      let startX = 0;\n      let startY = 0;\n      document.addEventListener('touchstart', event => {\n        startX = event.touches[0].clientX;\n        startY = event.touches[0].clientY;\n      });\n      document.addEventListener('touchend', event => {\n        const endX = event.changedTouches[0].clientX;\n        const endY = event.changedTouches[0].clientY;\n        const diffX = startX - endX;\n        const diffY = startY - endY;\n        // Horizontal swipe\n        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {\n          if (diffX > 0) {\n            this.nextStory(); // Swipe left - next story\n          } else {\n            this.previousStory(); // Swipe right - previous story\n          }\n        }\n        // Vertical swipe down to close\n        else if (diffY < -100) {\n          this.closeStories();\n        }\n      });\n    }\n    static {\n      this.ɵfac = function StoriesViewerComponent_Factory(t) {\n        return new (t || StoriesViewerComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: StoriesViewerComponent,\n        selectors: [[\"app-stories-viewer\"]],\n        viewQuery: function StoriesViewerComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyThumbnails = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 3,\n        consts: [[\"storyMedia\", \"\"], [\"storyVideo\", \"\"], [\"storyThumbnails\", \"\"], [\"class\", \"stories-viewer\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"comments-modal\", 3, \"click\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"stories-viewer\", 3, \"click\", \"keydown\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [1, \"timestamp\"], [1, \"story-controls\"], [\"class\", \"btn-sound\", 3, \"muted\", \"click\", 4, \"ngIf\"], [1, \"btn-pause\", 3, \"click\"], [1, \"fas\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-content\", 3, \"touchstart\", \"touchend\", \"mousedown\", \"mouseup\"], [1, \"nav-area\", \"nav-prev\", 3, \"click\"], [1, \"nav-area\", \"nav-next\", 3, \"click\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", \"load\", 4, \"ngIf\"], [\"class\", \"story-media\", 3, \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\", \"loadeddata\", \"ended\", 4, \"ngIf\"], [\"class\", \"product-tags\", 3, \"show-tags\", 4, \"ngIf\"], [\"class\", \"shopping-indicator\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [\"class\", \"story-navigation\", 4, \"ngIf\"], [1, \"story-content\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"story-media\", 3, \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\", \"ended\", 4, \"ngIf\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-actions\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"social-actions\"], [\"title\", \"Like Story\", \"aria-label\", \"Like this story\", 1, \"social-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"tooltip\"], [\"title\", \"Comment on Story\", \"aria-label\", \"Comment on this story\", 1, \"social-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [\"title\", \"Share Story\", \"aria-label\", \"Share this story\", 1, \"social-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [\"class\", \"social-btn sound\", \"title\", \"Toggle Sound\", \"aria-label\", \"Toggle sound on/off\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"btn-sound\", 3, \"click\"], [1, \"story-media\", 3, \"load\", \"src\", \"alt\"], [1, \"story-media\", 3, \"loadeddata\", \"ended\", \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-dot\"], [1, \"product-pulse\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"shopping-indicator\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story-caption\"], [1, \"story-navigation\"], [1, \"nav-slider-container\"], [1, \"nav-slider-btn\", \"prev\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"story-thumbnails\"], [\"class\", \"story-thumbnail\", 3, \"active\", \"viewed\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-slider-btn\", \"next\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"story-thumbnail\", 3, \"click\"], [1, \"thumbnail-image\", 3, \"src\", \"alt\"], [1, \"thumbnail-overlay\"], [1, \"user-thumbnail-avatar\", 3, \"src\", \"alt\"], [1, \"thumbnail-progress\"], [1, \"story-media\", 3, \"src\", \"alt\"], [1, \"story-media\", 3, \"ended\", \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"ecommerce-actions\"], [\"title\", \"Buy Now\", 1, \"action-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"btn-text\"], [\"title\", \"Add to Cart\", 1, \"action-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [\"title\", \"Add to Wishlist\", 1, \"action-btn\", \"wishlist\", 3, \"click\"], [\"title\", \"Toggle Sound\", \"aria-label\", \"Toggle sound on/off\", 1, \"social-btn\", \"sound\", 3, \"click\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"], [1, \"comments-modal\", 3, \"click\"], [1, \"comments-list\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment-input\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [3, \"click\", \"disabled\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [1, \"comment-content\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"comment-time\"]],\n        template: function StoriesViewerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, StoriesViewerComponent_div_0_Template, 50, 27, \"div\", 3)(1, StoriesViewerComponent_div_1_Template, 24, 9, \"div\", 4)(2, StoriesViewerComponent_div_2_Template, 13, 3, \"div\", 5);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStory);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showCommentsModal);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n        styles: [\".stories-viewer[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000;z-index:1000;display:flex;flex-direction:column;-webkit-user-select:none;user-select:none}.story-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;background:linear-gradient(180deg,rgba(0,0,0,.6) 0%,transparent 100%);position:relative;z-index:10}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.user-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:2px solid #fff}.user-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.username[_ngcontent-%COMP%]{color:#fff;font-weight:600;font-size:.9rem}.timestamp[_ngcontent-%COMP%]{color:#ffffffb3;font-size:.8rem}.btn-close[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:1.2rem;cursor:pointer;padding:8px}.progress-container[_ngcontent-%COMP%]{display:flex;gap:2px;padding:0 20px;position:absolute;top:8px;left:0;right:0;z-index:10}.progress-bar[_ngcontent-%COMP%]{flex:1;height:2px;background:#ffffff4d;border-radius:1px;overflow:hidden}.progress-fill[_ngcontent-%COMP%]{height:100%;background:#fff;width:0%;transition:width .1s ease}.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_progress linear}.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{width:100%}@keyframes _ngcontent-%COMP%_progress{0%{width:0%}to{width:100%}}.story-navigation[_ngcontent-%COMP%]{position:absolute;top:70px;left:0;right:0;z-index:15;background:linear-gradient(180deg,rgba(0,0,0,.4) 0%,transparent 100%);padding:12px 0}.nav-slider-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:0 16px}.nav-slider-btn[_ngcontent-%COMP%]{width:32px;height:32px;border:none;border-radius:50%;background:#fff3;color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease;flex-shrink:0}.nav-slider-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#ffffff4d;transform:scale(1.1)}.nav-slider-btn[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.story-thumbnails[_ngcontent-%COMP%]{display:flex;gap:8px;overflow-x:auto;scroll-behavior:smooth;scrollbar-width:none;-ms-overflow-style:none;flex:1;padding:4px 0}.story-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.story-thumbnail[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;border-radius:50%;overflow:hidden;cursor:pointer;transition:all .2s ease;flex-shrink:0;border:2px solid transparent}.story-thumbnail.active[_ngcontent-%COMP%]{border-color:#fff;transform:scale(1.1)}.story-thumbnail.viewed[_ngcontent-%COMP%]{opacity:.6}.story-thumbnail[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.thumbnail-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.thumbnail-overlay[_ngcontent-%COMP%]{position:absolute;bottom:-2px;right:-2px;width:20px;height:20px;border-radius:50%;border:2px solid #000;overflow:hidden}.user-thumbnail-avatar[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.thumbnail-progress[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;height:3px;background:#fff;transition:width .3s ease;border-radius:0 0 50px 50px}.story-content[_ngcontent-%COMP%]{flex:1;position:relative;display:flex;align-items:center;justify-content:center}.story-media[_ngcontent-%COMP%]{max-width:100%;max-height:100%;object-fit:contain}.product-tags[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;opacity:0;transition:opacity .3s ease}.product-tags.show-tags[_ngcontent-%COMP%]{opacity:1}.product-dot[_ngcontent-%COMP%]{position:relative;width:24px;height:24px;background:#ffffffe6;border:2px solid #000;border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 12px #0000004d;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-dot[_ngcontent-%COMP%]:before{content:\\\"\\\";width:8px;height:8px;background:#000;border-radius:50%}.product-pulse[_ngcontent-%COMP%]{position:absolute;width:140%;height:140%;border:2px solid rgba(255,255,255,.8);border-radius:50%;animation:_ngcontent-%COMP%_productPulse 2s infinite;top:50%;left:50%;transform:translate(-50%,-50%)}.product-info[_ngcontent-%COMP%]{position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:#000c;color:#fff;padding:8px 12px;border-radius:8px;font-size:12px;white-space:nowrap;margin-bottom:8px;opacity:0;pointer-events:none;transition:opacity .3s ease}.product-info[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:100%;left:50%;transform:translate(-50%);border:4px solid transparent;border-top-color:#000c}.product-tag[_ngcontent-%COMP%]:hover   .product-info[_ngcontent-%COMP%]{opacity:1}.shopping-indicator[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:16px;background:#000000b3;color:#fff;padding:8px 12px;border-radius:20px;display:flex;align-items:center;gap:6px;font-size:12px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);animation:_ngcontent-%COMP%_fadeInUp .3s ease;cursor:pointer}.shopping-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}@keyframes _ngcontent-%COMP%_productPulse{0%{transform:translate(-50%,-50%) scale(1);opacity:1}to{transform:translate(-50%,-50%) scale(1.4);opacity:0}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.product-tag[_ngcontent-%COMP%]{position:absolute;pointer-events:all;cursor:pointer;transform:translate(-50%,-50%)}.product-tag-icon[_ngcontent-%COMP%]{width:32px;height:32px;background:#ffffffe6;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#333;animation:_ngcontent-%COMP%_pulse 2s infinite}.product-tag-info[_ngcontent-%COMP%]{position:absolute;top:40px;left:50%;transform:translate(-50%);background:#000c;color:#fff;padding:8px 12px;border-radius:8px;white-space:nowrap;font-size:.8rem;opacity:0;transition:opacity .3s ease}.product-tag[_ngcontent-%COMP%]:hover   .product-tag-info[_ngcontent-%COMP%]{opacity:1}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:translate(-50%,-50%) scale(1)}50%{transform:translate(-50%,-50%) scale(1.1)}}.story-caption[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:20px;right:20px;color:#fff;font-size:.9rem;line-height:1.4;background:#00000080;padding:12px;border-radius:8px}.nav-area[_ngcontent-%COMP%]{position:absolute;top:0;bottom:0;width:30%;cursor:pointer;z-index:5}.nav-prev[_ngcontent-%COMP%]{left:0}.nav-next[_ngcontent-%COMP%]{right:0}.story-actions[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(0deg,rgba(0,0,0,.6) 0%,transparent 100%)}.ecommerce-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px}.action-btn[_ngcontent-%COMP%]{flex:1;padding:12px;border:none;border-radius:8px;font-weight:600;font-size:.9rem;cursor:pointer;display:flex;align-items:center;justify-content:center;gap:6px;transition:all .2s ease;position:relative}.action-btn[_ngcontent-%COMP%]   .btn-text[_ngcontent-%COMP%]{display:inline}.action-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]{position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:#000000e6;color:#fff;padding:6px 10px;border-radius:6px;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s ease;margin-bottom:8px;z-index:1000}.action-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:100%;left:50%;transform:translate(-50%);border:4px solid transparent;border-top-color:#000000e6}.action-btn[_ngcontent-%COMP%]:hover   .tooltip[_ngcontent-%COMP%]{opacity:1}.buy-now[_ngcontent-%COMP%]{background:#ff6b6b;color:#fff}.add-cart[_ngcontent-%COMP%]{background:#4ecdc4;color:#fff}.wishlist[_ngcontent-%COMP%]{background:#ff9ff3;color:#fff}.action-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #0000004d}.social-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:24px}.social-btn[_ngcontent-%COMP%]{width:44px;height:44px;border:none;border-radius:50%;background:#fff3;color:#fff;font-size:1.1rem;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .2s ease;position:relative}.social-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]{position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:#000000e6;color:#fff;padding:6px 10px;border-radius:6px;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s ease;margin-bottom:8px;z-index:1000}.social-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:100%;left:50%;transform:translate(-50%);border:4px solid transparent;border-top-color:#000000e6}.social-btn[_ngcontent-%COMP%]:hover   .tooltip[_ngcontent-%COMP%]{opacity:1}.social-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:scale(1.1)}.social-btn.liked[_ngcontent-%COMP%]{background:#ff6b6b;color:#fff}.product-modal[_ngcontent-%COMP%], .comments-modal[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000c;z-index:1100;display:flex;align-items:center;justify-content:center;padding:20px}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;max-width:400px;width:100%;max-height:80vh;overflow-y:auto}.modal-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;border-bottom:1px solid #eee}.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600}.modal-body[_ngcontent-%COMP%]{padding:20px}.product-image[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover;border-radius:8px;margin-bottom:16px}.product-details[_ngcontent-%COMP%]{margin-bottom:20px}.brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:8px}.price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#333}.original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#999;text-decoration:line-through}.modal-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%]{padding:12px;border:none;border-radius:6px;font-weight:600;cursor:pointer;transition:all .2s ease}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-secondary[_ngcontent-%COMP%]{background:#6c757d;color:#fff}.btn-outline[_ngcontent-%COMP%]{background:transparent;color:#007bff;border:1px solid #007bff}.comments-list[_ngcontent-%COMP%]{max-height:300px;overflow-y:auto;margin-bottom:16px}.comment[_ngcontent-%COMP%]{display:flex;gap:12px;margin-bottom:16px}.comment-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%}.comment-content[_ngcontent-%COMP%]{flex:1}.comment-username[_ngcontent-%COMP%]{font-weight:600;font-size:.9rem;color:#333}.comment-text[_ngcontent-%COMP%]{margin:4px 0;font-size:.9rem;line-height:1.4}.comment-time[_ngcontent-%COMP%]{font-size:.8rem;color:#666}.comment-input[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center;padding-top:16px;border-top:1px solid #eee}.comment-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1;padding:8px 12px;border:1px solid #ddd;border-radius:20px;font-size:.9rem}.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:36px;height:36px;border:none;border-radius:50%;background:#007bff;color:#fff;cursor:pointer;display:flex;align-items:center;justify-content:center}.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background:#ccc;cursor:not-allowed}@media (max-width: 768px){.story-navigation[_ngcontent-%COMP%]{top:60px;padding:8px 0}.nav-slider-container[_ngcontent-%COMP%]{padding:0 12px;gap:6px}.nav-slider-btn[_ngcontent-%COMP%]{width:28px;height:28px;font-size:.8rem}.story-thumbnails[_ngcontent-%COMP%]{gap:6px}.story-thumbnail[_ngcontent-%COMP%]{width:40px;height:40px}.thumbnail-overlay[_ngcontent-%COMP%]{width:16px;height:16px}}@media (max-width: 480px){.story-navigation[_ngcontent-%COMP%]{top:55px;padding:6px 0}.nav-slider-container[_ngcontent-%COMP%]{padding:0 8px;gap:4px}.nav-slider-btn[_ngcontent-%COMP%]{width:24px;height:24px;font-size:.7rem}.story-thumbnails[_ngcontent-%COMP%]{gap:4px}.story-thumbnail[_ngcontent-%COMP%]{width:36px;height:36px}.thumbnail-overlay[_ngcontent-%COMP%]{width:14px;height:14px}}@media (max-width: 768px){.story-header[_ngcontent-%COMP%]{padding:12px 16px}.user-avatar[_ngcontent-%COMP%]{width:36px;height:36px}.username[_ngcontent-%COMP%]{font-size:.85rem}.timestamp[_ngcontent-%COMP%]{font-size:.75rem}.progress-container[_ngcontent-%COMP%]{padding:0 16px;top:6px}.story-caption[_ngcontent-%COMP%]{bottom:140px;left:16px;right:16px;font-size:.85rem;padding:10px}.story-actions[_ngcontent-%COMP%]{padding:16px}.ecommerce-actions[_ngcontent-%COMP%]{flex-direction:column;gap:10px;margin-bottom:20px}.action-btn[_ngcontent-%COMP%]{padding:14px;font-size:1rem;min-height:48px}.action-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%], .social-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]{font-size:.7rem;padding:4px 8px;margin-bottom:6px}.social-actions[_ngcontent-%COMP%]{gap:20px}.social-btn[_ngcontent-%COMP%]{width:48px;height:48px;font-size:1.2rem}.product-tag-icon[_ngcontent-%COMP%]{width:28px;height:28px;font-size:.9rem}.product-tag-info[_ngcontent-%COMP%]{font-size:.75rem;padding:6px 10px}.modal-content[_ngcontent-%COMP%]{margin:10px;max-width:calc(100vw - 20px);max-height:calc(100vh - 20px)}.modal-body[_ngcontent-%COMP%]{padding:16px}.product-image[_ngcontent-%COMP%]{height:180px}}@media (max-width: 480px){.story-header[_ngcontent-%COMP%]{padding:10px 12px}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px}.username[_ngcontent-%COMP%]{font-size:.8rem}.progress-container[_ngcontent-%COMP%]{padding:0 12px;top:4px}.story-caption[_ngcontent-%COMP%]{bottom:120px;left:12px;right:12px;font-size:.8rem;padding:8px}.story-actions[_ngcontent-%COMP%]{padding:12px}.ecommerce-actions[_ngcontent-%COMP%]{gap:8px;margin-bottom:16px}.action-btn[_ngcontent-%COMP%]{padding:12px;font-size:.9rem;min-height:44px}.social-actions[_ngcontent-%COMP%]{gap:16px}.social-btn[_ngcontent-%COMP%]{width:44px;height:44px;font-size:1.1rem}.action-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%], .social-btn[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]{font-size:.65rem;padding:3px 6px;margin-bottom:4px}}@media (min-width: 769px){.stories-viewer[_ngcontent-%COMP%]{max-width:400px;margin:0 auto;border-radius:12px;overflow:hidden;height:90vh;top:5vh}.story-media[_ngcontent-%COMP%]{border-radius:8px}.ecommerce-actions[_ngcontent-%COMP%]{flex-direction:row;gap:12px}.action-btn[_ngcontent-%COMP%]{padding:10px 16px;font-size:.9rem}}@media (min-width: 1024px){.stories-viewer[_ngcontent-%COMP%]{max-width:450px;height:85vh;top:7.5vh}}.nav-area[_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent;-webkit-user-select:none;user-select:none}.stories-viewer[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{-webkit-touch-callout:none;-webkit-user-select:none;user-select:none}.story-media[_ngcontent-%COMP%]{transition:opacity .3s ease}.story-media.loading[_ngcontent-%COMP%]{opacity:.5}.btn-close[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .social-btn[_ngcontent-%COMP%]:focus{outline:2px solid #007bff;outline-offset:2px}.stories-viewer[_ngcontent-%COMP%]{will-change:transform;transform:translateZ(0)}.story-media[_ngcontent-%COMP%]{will-change:opacity}.progress-fill[_ngcontent-%COMP%]{will-change:width}\"]\n      });\n    }\n  }\n  return StoriesViewerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}