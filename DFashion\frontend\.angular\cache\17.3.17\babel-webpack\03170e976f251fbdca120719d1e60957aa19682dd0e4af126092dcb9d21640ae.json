{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index5.js';\nimport { r as raf } from './helpers.js';\nlet animationPrefix;\n/**\n * Web Animations requires hyphenated CSS properties\n * to be written in camelCase when animating\n */\nconst processKeyframes = keyframes => {\n  keyframes.forEach(keyframe => {\n    for (const key in keyframe) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (keyframe.hasOwnProperty(key)) {\n        const value = keyframe[key];\n        if (key === 'easing') {\n          const newKey = 'animation-timing-function';\n          keyframe[newKey] = value;\n          delete keyframe[key];\n        } else {\n          const newKey = convertCamelCaseToHypen(key);\n          if (newKey !== key) {\n            keyframe[newKey] = value;\n            delete keyframe[key];\n          }\n        }\n      }\n    }\n  });\n  return keyframes;\n};\nconst convertCamelCaseToHypen = str => {\n  return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n};\nconst getAnimationPrefix = el => {\n  if (animationPrefix === undefined) {\n    const supportsUnprefixed = el.style.animationName !== undefined;\n    const supportsWebkitPrefix = el.style.webkitAnimationName !== undefined;\n    animationPrefix = !supportsUnprefixed && supportsWebkitPrefix ? '-webkit-' : '';\n  }\n  return animationPrefix;\n};\nconst setStyleProperty = (element, propertyName, value) => {\n  const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n  element.style.setProperty(prefix + propertyName, value);\n};\nconst removeStyleProperty = (element, propertyName) => {\n  const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n  element.style.removeProperty(prefix + propertyName);\n};\nconst animationEnd = (el, callback) => {\n  let unRegTrans;\n  const opts = {\n    passive: true\n  };\n  const unregister = () => {\n    if (unRegTrans) {\n      unRegTrans();\n    }\n  };\n  const onTransitionEnd = ev => {\n    if (el === ev.target) {\n      unregister();\n      callback(ev);\n    }\n  };\n  if (el) {\n    el.addEventListener('webkitAnimationEnd', onTransitionEnd, opts);\n    el.addEventListener('animationend', onTransitionEnd, opts);\n    unRegTrans = () => {\n      el.removeEventListener('webkitAnimationEnd', onTransitionEnd, opts);\n      el.removeEventListener('animationend', onTransitionEnd, opts);\n    };\n  }\n  return unregister;\n};\n// TODO(FW-2832): type\nconst generateKeyframeRules = (keyframes = []) => {\n  return keyframes.map(keyframe => {\n    const offset = keyframe.offset;\n    const frameString = [];\n    for (const property in keyframe) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (keyframe.hasOwnProperty(property) && property !== 'offset') {\n        frameString.push(`${property}: ${keyframe[property]};`);\n      }\n    }\n    return `${offset * 100}% { ${frameString.join(' ')} }`;\n  }).join(' ');\n};\nconst keyframeIds = [];\nconst generateKeyframeName = keyframeRules => {\n  let index = keyframeIds.indexOf(keyframeRules);\n  if (index < 0) {\n    index = keyframeIds.push(keyframeRules) - 1;\n  }\n  return `ion-animation-${index}`;\n};\nconst getStyleContainer = element => {\n  // getRootNode is not always available in SSR environments.\n  // TODO(FW-2832): types\n  const rootNode = element.getRootNode !== undefined ? element.getRootNode() : element;\n  return rootNode.head || rootNode;\n};\nconst createKeyframeStylesheet = (keyframeName, keyframeRules, element) => {\n  var _a;\n  const styleContainer = getStyleContainer(element);\n  const keyframePrefix = getAnimationPrefix(element);\n  const existingStylesheet = styleContainer.querySelector('#' + keyframeName);\n  if (existingStylesheet) {\n    return existingStylesheet;\n  }\n  const stylesheet = ((_a = element.ownerDocument) !== null && _a !== void 0 ? _a : document).createElement('style');\n  stylesheet.id = keyframeName;\n  stylesheet.textContent = `@${keyframePrefix}keyframes ${keyframeName} { ${keyframeRules} } @${keyframePrefix}keyframes ${keyframeName}-alt { ${keyframeRules} }`;\n  styleContainer.appendChild(stylesheet);\n  return stylesheet;\n};\nconst addClassToArray = (classes = [], className) => {\n  if (className !== undefined) {\n    const classNameToAppend = Array.isArray(className) ? className : [className];\n    return [...classes, ...classNameToAppend];\n  }\n  return classes;\n};\nconst createAnimation = animationId => {\n  let _delay;\n  let _duration;\n  let _easing;\n  let _iterations;\n  let _fill;\n  let _direction;\n  let _keyframes = [];\n  let beforeAddClasses = [];\n  let beforeRemoveClasses = [];\n  let initialized = false;\n  let parentAnimation;\n  let beforeStylesValue = {};\n  let afterAddClasses = [];\n  let afterRemoveClasses = [];\n  let afterStylesValue = {};\n  let numAnimationsRunning = 0;\n  let shouldForceLinearEasing = false;\n  let shouldForceSyncPlayback = false;\n  let cssAnimationsTimerFallback;\n  let forceDirectionValue;\n  let forceDurationValue;\n  let forceDelayValue;\n  let willComplete = true;\n  let finished = false;\n  let shouldCalculateNumAnimations = true;\n  let keyframeName;\n  let ani;\n  let paused = false;\n  const id = animationId;\n  const onFinishCallbacks = [];\n  const onFinishOneTimeCallbacks = [];\n  const onStopOneTimeCallbacks = [];\n  const elements = [];\n  const childAnimations = [];\n  const stylesheets = [];\n  const _beforeAddReadFunctions = [];\n  const _beforeAddWriteFunctions = [];\n  const _afterAddReadFunctions = [];\n  const _afterAddWriteFunctions = [];\n  const webAnimations = [];\n  const supportsAnimationEffect = typeof AnimationEffect === 'function' || win !== undefined && typeof win.AnimationEffect === 'function';\n  const supportsWebAnimations = typeof Element === 'function' && typeof Element.prototype.animate === 'function' && supportsAnimationEffect;\n  const ANIMATION_END_FALLBACK_PADDING_MS = 100;\n  const getWebAnimations = () => {\n    return webAnimations;\n  };\n  const destroy = clearStyleSheets => {\n    childAnimations.forEach(childAnimation => {\n      childAnimation.destroy(clearStyleSheets);\n    });\n    cleanUp(clearStyleSheets);\n    elements.length = 0;\n    childAnimations.length = 0;\n    _keyframes.length = 0;\n    clearOnFinish();\n    initialized = false;\n    shouldCalculateNumAnimations = true;\n    return ani;\n  };\n  /**\n   * Cancels any Web Animations, removes\n   * any animation properties from the\n   * animation's elements, and removes the\n   * animation's stylesheets from the DOM.\n   */\n  const cleanUp = clearStyleSheets => {\n    cleanUpElements();\n    if (clearStyleSheets) {\n      cleanUpStyleSheets();\n    }\n  };\n  const resetFlags = () => {\n    shouldForceLinearEasing = false;\n    shouldForceSyncPlayback = false;\n    shouldCalculateNumAnimations = true;\n    forceDirectionValue = undefined;\n    forceDurationValue = undefined;\n    forceDelayValue = undefined;\n    numAnimationsRunning = 0;\n    finished = false;\n    willComplete = true;\n    paused = false;\n  };\n  const isRunning = () => {\n    return numAnimationsRunning !== 0 && !paused;\n  };\n  /**\n   * @internal\n   * Remove a callback from a chosen callback array\n   * @param callbackToRemove: A reference to the callback that should be removed\n   * @param callbackObjects: An array of callbacks that callbackToRemove should be removed from.\n   */\n  const clearCallback = (callbackToRemove, callbackObjects) => {\n    const index = callbackObjects.findIndex(callbackObject => callbackObject.c === callbackToRemove);\n    if (index > -1) {\n      callbackObjects.splice(index, 1);\n    }\n  };\n  /**\n   * @internal\n   * Add a callback to be fired when an animation is stopped/cancelled.\n   * @param callback: A reference to the callback that should be fired\n   * @param opts: Any options associated with this particular callback\n   */\n  const onStop = (callback, opts) => {\n    onStopOneTimeCallbacks.push({\n      c: callback,\n      o: opts\n    });\n    return ani;\n  };\n  const onFinish = (callback, opts) => {\n    const callbacks = (opts === null || opts === void 0 ? void 0 : opts.oneTimeCallback) ? onFinishOneTimeCallbacks : onFinishCallbacks;\n    callbacks.push({\n      c: callback,\n      o: opts\n    });\n    return ani;\n  };\n  const clearOnFinish = () => {\n    onFinishCallbacks.length = 0;\n    onFinishOneTimeCallbacks.length = 0;\n    return ani;\n  };\n  /**\n   * Cancels any Web Animations and removes\n   * any animation properties from the\n   * the animation's elements.\n   */\n  const cleanUpElements = () => {\n    if (supportsWebAnimations) {\n      webAnimations.forEach(animation => {\n        animation.cancel();\n      });\n      webAnimations.length = 0;\n    } else {\n      const elementsArray = elements.slice();\n      raf(() => {\n        elementsArray.forEach(element => {\n          removeStyleProperty(element, 'animation-name');\n          removeStyleProperty(element, 'animation-duration');\n          removeStyleProperty(element, 'animation-timing-function');\n          removeStyleProperty(element, 'animation-iteration-count');\n          removeStyleProperty(element, 'animation-delay');\n          removeStyleProperty(element, 'animation-play-state');\n          removeStyleProperty(element, 'animation-fill-mode');\n          removeStyleProperty(element, 'animation-direction');\n        });\n      });\n    }\n  };\n  /**\n   * Removes the animation's stylesheets\n   * from the DOM.\n   */\n  const cleanUpStyleSheets = () => {\n    stylesheets.forEach(stylesheet => {\n      /**\n       * When sharing stylesheets, it's possible\n       * for another animation to have already\n       * cleaned up a particular stylesheet\n       */\n      if (stylesheet === null || stylesheet === void 0 ? void 0 : stylesheet.parentNode) {\n        stylesheet.parentNode.removeChild(stylesheet);\n      }\n    });\n    stylesheets.length = 0;\n  };\n  const beforeAddRead = readFn => {\n    _beforeAddReadFunctions.push(readFn);\n    return ani;\n  };\n  const beforeAddWrite = writeFn => {\n    _beforeAddWriteFunctions.push(writeFn);\n    return ani;\n  };\n  const afterAddRead = readFn => {\n    _afterAddReadFunctions.push(readFn);\n    return ani;\n  };\n  const afterAddWrite = writeFn => {\n    _afterAddWriteFunctions.push(writeFn);\n    return ani;\n  };\n  const beforeAddClass = className => {\n    beforeAddClasses = addClassToArray(beforeAddClasses, className);\n    return ani;\n  };\n  const beforeRemoveClass = className => {\n    beforeRemoveClasses = addClassToArray(beforeRemoveClasses, className);\n    return ani;\n  };\n  /**\n   * Set CSS inline styles to the animation's\n   * elements before the animation begins.\n   */\n  const beforeStyles = (styles = {}) => {\n    beforeStylesValue = styles;\n    return ani;\n  };\n  /**\n   * Clear CSS inline styles from the animation's\n   * elements before the animation begins.\n   */\n  const beforeClearStyles = (propertyNames = []) => {\n    for (const property of propertyNames) {\n      beforeStylesValue[property] = '';\n    }\n    return ani;\n  };\n  const afterAddClass = className => {\n    afterAddClasses = addClassToArray(afterAddClasses, className);\n    return ani;\n  };\n  const afterRemoveClass = className => {\n    afterRemoveClasses = addClassToArray(afterRemoveClasses, className);\n    return ani;\n  };\n  const afterStyles = (styles = {}) => {\n    afterStylesValue = styles;\n    return ani;\n  };\n  const afterClearStyles = (propertyNames = []) => {\n    for (const property of propertyNames) {\n      afterStylesValue[property] = '';\n    }\n    return ani;\n  };\n  const getFill = () => {\n    if (_fill !== undefined) {\n      return _fill;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getFill();\n    }\n    return 'both';\n  };\n  const getDirection = () => {\n    if (forceDirectionValue !== undefined) {\n      return forceDirectionValue;\n    }\n    if (_direction !== undefined) {\n      return _direction;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDirection();\n    }\n    return 'normal';\n  };\n  const getEasing = () => {\n    if (shouldForceLinearEasing) {\n      return 'linear';\n    }\n    if (_easing !== undefined) {\n      return _easing;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getEasing();\n    }\n    return 'linear';\n  };\n  const getDuration = () => {\n    if (shouldForceSyncPlayback) {\n      return 0;\n    }\n    if (forceDurationValue !== undefined) {\n      return forceDurationValue;\n    }\n    if (_duration !== undefined) {\n      return _duration;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDuration();\n    }\n    return 0;\n  };\n  const getIterations = () => {\n    if (_iterations !== undefined) {\n      return _iterations;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getIterations();\n    }\n    return 1;\n  };\n  const getDelay = () => {\n    if (forceDelayValue !== undefined) {\n      return forceDelayValue;\n    }\n    if (_delay !== undefined) {\n      return _delay;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDelay();\n    }\n    return 0;\n  };\n  const getKeyframes = () => {\n    return _keyframes;\n  };\n  const direction = animationDirection => {\n    _direction = animationDirection;\n    update(true);\n    return ani;\n  };\n  const fill = animationFill => {\n    _fill = animationFill;\n    update(true);\n    return ani;\n  };\n  const delay = animationDelay => {\n    _delay = animationDelay;\n    update(true);\n    return ani;\n  };\n  const easing = animationEasing => {\n    _easing = animationEasing;\n    update(true);\n    return ani;\n  };\n  const duration = animationDuration => {\n    /**\n     * CSS Animation Durations of 0ms work fine on Chrome\n     * but do not run on Safari, so force it to 1ms to\n     * get it to run on both platforms.\n     */\n    if (!supportsWebAnimations && animationDuration === 0) {\n      animationDuration = 1;\n    }\n    _duration = animationDuration;\n    update(true);\n    return ani;\n  };\n  const iterations = animationIterations => {\n    _iterations = animationIterations;\n    update(true);\n    return ani;\n  };\n  const parent = animation => {\n    parentAnimation = animation;\n    return ani;\n  };\n  const addElement = el => {\n    if (el != null) {\n      if (el.nodeType === 1) {\n        elements.push(el);\n      } else if (el.length >= 0) {\n        for (let i = 0; i < el.length; i++) {\n          elements.push(el[i]);\n        }\n      } else {\n        console.error('Invalid addElement value');\n      }\n    }\n    return ani;\n  };\n  const addAnimation = animationToAdd => {\n    if (animationToAdd != null) {\n      if (Array.isArray(animationToAdd)) {\n        for (const animation of animationToAdd) {\n          animation.parent(ani);\n          childAnimations.push(animation);\n        }\n      } else {\n        animationToAdd.parent(ani);\n        childAnimations.push(animationToAdd);\n      }\n    }\n    return ani;\n  };\n  const keyframes = keyframeValues => {\n    const different = _keyframes !== keyframeValues;\n    _keyframes = keyframeValues;\n    if (different) {\n      updateKeyframes(_keyframes);\n    }\n    return ani;\n  };\n  const updateKeyframes = keyframeValues => {\n    if (supportsWebAnimations) {\n      getWebAnimations().forEach(animation => {\n        /**\n         * animation.effect's type is AnimationEffect.\n         * However, in this case we have a more specific\n         * type of AnimationEffect called KeyframeEffect which\n         * inherits from AnimationEffect. As a result,\n         * we cast animation.effect to KeyframeEffect.\n         */\n        const keyframeEffect = animation.effect;\n        /**\n         * setKeyframes is not supported in all browser\n         * versions that Ionic supports, so we need to\n         * check for support before using it.\n         */\n        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n        if (keyframeEffect.setKeyframes) {\n          keyframeEffect.setKeyframes(keyframeValues);\n        } else {\n          const newEffect = new KeyframeEffect(keyframeEffect.target, keyframeValues, keyframeEffect.getTiming());\n          animation.effect = newEffect;\n        }\n      });\n    } else {\n      initializeCSSAnimation();\n    }\n  };\n  /**\n   * Run all \"before\" animation hooks.\n   */\n  const beforeAnimation = () => {\n    // Runs all before read callbacks\n    _beforeAddReadFunctions.forEach(callback => callback());\n    // Runs all before write callbacks\n    _beforeAddWriteFunctions.forEach(callback => callback());\n    // Updates styles and classes before animation runs\n    const addClasses = beforeAddClasses;\n    const removeClasses = beforeRemoveClasses;\n    const styles = beforeStylesValue;\n    elements.forEach(el => {\n      const elementClassList = el.classList;\n      addClasses.forEach(c => elementClassList.add(c));\n      removeClasses.forEach(c => elementClassList.remove(c));\n      for (const property in styles) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (styles.hasOwnProperty(property)) {\n          setStyleProperty(el, property, styles[property]);\n        }\n      }\n    });\n  };\n  /**\n   * Run all \"after\" animation hooks.\n   */\n  const afterAnimation = () => {\n    clearCSSAnimationsTimeout();\n    // Runs all after read callbacks\n    _afterAddReadFunctions.forEach(callback => callback());\n    // Runs all after write callbacks\n    _afterAddWriteFunctions.forEach(callback => callback());\n    // Updates styles and classes before animation ends\n    const currentStep = willComplete ? 1 : 0;\n    const addClasses = afterAddClasses;\n    const removeClasses = afterRemoveClasses;\n    const styles = afterStylesValue;\n    elements.forEach(el => {\n      const elementClassList = el.classList;\n      addClasses.forEach(c => elementClassList.add(c));\n      removeClasses.forEach(c => elementClassList.remove(c));\n      for (const property in styles) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (styles.hasOwnProperty(property)) {\n          setStyleProperty(el, property, styles[property]);\n        }\n      }\n    });\n    /**\n     * Clean up any value coercion before\n     * the user callbacks fire otherwise\n     * they may get stale values. For example,\n     * if someone calls progressStart(0) the\n     * animation may still be reversed.\n     */\n    forceDurationValue = undefined;\n    forceDirectionValue = undefined;\n    forceDelayValue = undefined;\n    onFinishCallbacks.forEach(onFinishCallback => {\n      return onFinishCallback.c(currentStep, ani);\n    });\n    onFinishOneTimeCallbacks.forEach(onFinishCallback => {\n      return onFinishCallback.c(currentStep, ani);\n    });\n    onFinishOneTimeCallbacks.length = 0;\n    shouldCalculateNumAnimations = true;\n    if (willComplete) {\n      finished = true;\n    }\n    willComplete = true;\n  };\n  const animationFinish = () => {\n    if (numAnimationsRunning === 0) {\n      return;\n    }\n    numAnimationsRunning--;\n    if (numAnimationsRunning === 0) {\n      afterAnimation();\n      if (parentAnimation) {\n        parentAnimation.animationFinish();\n      }\n    }\n  };\n  const initializeCSSAnimation = (toggleAnimationName = true) => {\n    cleanUpStyleSheets();\n    const processedKeyframes = processKeyframes(_keyframes);\n    elements.forEach(element => {\n      if (processedKeyframes.length > 0) {\n        const keyframeRules = generateKeyframeRules(processedKeyframes);\n        keyframeName = animationId !== undefined ? animationId : generateKeyframeName(keyframeRules);\n        const stylesheet = createKeyframeStylesheet(keyframeName, keyframeRules, element);\n        stylesheets.push(stylesheet);\n        setStyleProperty(element, 'animation-duration', `${getDuration()}ms`);\n        setStyleProperty(element, 'animation-timing-function', getEasing());\n        setStyleProperty(element, 'animation-delay', `${getDelay()}ms`);\n        setStyleProperty(element, 'animation-fill-mode', getFill());\n        setStyleProperty(element, 'animation-direction', getDirection());\n        const iterationsCount = getIterations() === Infinity ? 'infinite' : getIterations().toString();\n        setStyleProperty(element, 'animation-iteration-count', iterationsCount);\n        setStyleProperty(element, 'animation-play-state', 'paused');\n        if (toggleAnimationName) {\n          setStyleProperty(element, 'animation-name', `${stylesheet.id}-alt`);\n        }\n        raf(() => {\n          setStyleProperty(element, 'animation-name', stylesheet.id || null);\n        });\n      }\n    });\n  };\n  const initializeWebAnimation = () => {\n    elements.forEach(element => {\n      const animation = element.animate(_keyframes, {\n        id,\n        delay: getDelay(),\n        duration: getDuration(),\n        easing: getEasing(),\n        iterations: getIterations(),\n        fill: getFill(),\n        direction: getDirection()\n      });\n      animation.pause();\n      webAnimations.push(animation);\n    });\n    if (webAnimations.length > 0) {\n      webAnimations[0].onfinish = () => {\n        animationFinish();\n      };\n    }\n  };\n  const initializeAnimation = (toggleAnimationName = true) => {\n    beforeAnimation();\n    if (_keyframes.length > 0) {\n      if (supportsWebAnimations) {\n        initializeWebAnimation();\n      } else {\n        initializeCSSAnimation(toggleAnimationName);\n      }\n    }\n    initialized = true;\n  };\n  const setAnimationStep = step => {\n    step = Math.min(Math.max(step, 0), 0.9999);\n    if (supportsWebAnimations) {\n      webAnimations.forEach(animation => {\n        // When creating the animation the delay is guaranteed to be set to a number.\n        animation.currentTime = animation.effect.getComputedTiming().delay + getDuration() * step;\n        animation.pause();\n      });\n    } else {\n      const animationDuration = `-${getDuration() * step}ms`;\n      elements.forEach(element => {\n        if (_keyframes.length > 0) {\n          setStyleProperty(element, 'animation-delay', animationDuration);\n          setStyleProperty(element, 'animation-play-state', 'paused');\n        }\n      });\n    }\n  };\n  const updateWebAnimation = step => {\n    webAnimations.forEach(animation => {\n      animation.effect.updateTiming({\n        delay: getDelay(),\n        duration: getDuration(),\n        easing: getEasing(),\n        iterations: getIterations(),\n        fill: getFill(),\n        direction: getDirection()\n      });\n    });\n    if (step !== undefined) {\n      setAnimationStep(step);\n    }\n  };\n  const updateCSSAnimation = (toggleAnimationName = true, step) => {\n    raf(() => {\n      elements.forEach(element => {\n        setStyleProperty(element, 'animation-name', keyframeName || null);\n        setStyleProperty(element, 'animation-duration', `${getDuration()}ms`);\n        setStyleProperty(element, 'animation-timing-function', getEasing());\n        setStyleProperty(element, 'animation-delay', step !== undefined ? `-${step * getDuration()}ms` : `${getDelay()}ms`);\n        setStyleProperty(element, 'animation-fill-mode', getFill() || null);\n        setStyleProperty(element, 'animation-direction', getDirection() || null);\n        const iterationsCount = getIterations() === Infinity ? 'infinite' : getIterations().toString();\n        setStyleProperty(element, 'animation-iteration-count', iterationsCount);\n        if (toggleAnimationName) {\n          setStyleProperty(element, 'animation-name', `${keyframeName}-alt`);\n        }\n        raf(() => {\n          setStyleProperty(element, 'animation-name', keyframeName || null);\n        });\n      });\n    });\n  };\n  const update = (deep = false, toggleAnimationName = true, step) => {\n    if (deep) {\n      childAnimations.forEach(animation => {\n        animation.update(deep, toggleAnimationName, step);\n      });\n    }\n    if (supportsWebAnimations) {\n      updateWebAnimation(step);\n    } else {\n      updateCSSAnimation(toggleAnimationName, step);\n    }\n    return ani;\n  };\n  const progressStart = (forceLinearEasing = false, step) => {\n    childAnimations.forEach(animation => {\n      animation.progressStart(forceLinearEasing, step);\n    });\n    pauseAnimation();\n    shouldForceLinearEasing = forceLinearEasing;\n    if (!initialized) {\n      initializeAnimation();\n    }\n    update(false, true, step);\n    return ani;\n  };\n  const progressStep = step => {\n    childAnimations.forEach(animation => {\n      animation.progressStep(step);\n    });\n    setAnimationStep(step);\n    return ani;\n  };\n  const progressEnd = (playTo, step, dur) => {\n    shouldForceLinearEasing = false;\n    childAnimations.forEach(animation => {\n      animation.progressEnd(playTo, step, dur);\n    });\n    if (dur !== undefined) {\n      forceDurationValue = dur;\n    }\n    finished = false;\n    willComplete = true;\n    if (playTo === 0) {\n      forceDirectionValue = getDirection() === 'reverse' ? 'normal' : 'reverse';\n      if (forceDirectionValue === 'reverse') {\n        willComplete = false;\n      }\n      if (supportsWebAnimations) {\n        update();\n        setAnimationStep(1 - step);\n      } else {\n        forceDelayValue = (1 - step) * getDuration() * -1;\n        update(false, false);\n      }\n    } else if (playTo === 1) {\n      if (supportsWebAnimations) {\n        update();\n        setAnimationStep(step);\n      } else {\n        forceDelayValue = step * getDuration() * -1;\n        update(false, false);\n      }\n    }\n    if (playTo !== undefined && !parentAnimation) {\n      play();\n    }\n    return ani;\n  };\n  const pauseAnimation = () => {\n    if (initialized) {\n      if (supportsWebAnimations) {\n        webAnimations.forEach(animation => {\n          animation.pause();\n        });\n      } else {\n        elements.forEach(element => {\n          setStyleProperty(element, 'animation-play-state', 'paused');\n        });\n      }\n      paused = true;\n    }\n  };\n  const pause = () => {\n    childAnimations.forEach(animation => {\n      animation.pause();\n    });\n    pauseAnimation();\n    return ani;\n  };\n  const onAnimationEndFallback = () => {\n    cssAnimationsTimerFallback = undefined;\n    animationFinish();\n  };\n  const clearCSSAnimationsTimeout = () => {\n    if (cssAnimationsTimerFallback) {\n      clearTimeout(cssAnimationsTimerFallback);\n    }\n  };\n  const playCSSAnimations = () => {\n    clearCSSAnimationsTimeout();\n    raf(() => {\n      elements.forEach(element => {\n        if (_keyframes.length > 0) {\n          setStyleProperty(element, 'animation-play-state', 'running');\n        }\n      });\n    });\n    if (_keyframes.length === 0 || elements.length === 0) {\n      animationFinish();\n    } else {\n      /**\n       * This is a catchall in the event that a CSS Animation did not finish.\n       * The Web Animations API has mechanisms in place for preventing this.\n       * CSS Animations will not fire an `animationend` event\n       * for elements with `display: none`. The Web Animations API\n       * accounts for this, but using raw CSS Animations requires\n       * this workaround.\n       */\n      const animationDelay = getDelay() || 0;\n      const animationDuration = getDuration() || 0;\n      const animationIterations = getIterations() || 1;\n      // No need to set a timeout when animation has infinite iterations\n      if (isFinite(animationIterations)) {\n        cssAnimationsTimerFallback = setTimeout(onAnimationEndFallback, animationDelay + animationDuration * animationIterations + ANIMATION_END_FALLBACK_PADDING_MS);\n      }\n      animationEnd(elements[0], () => {\n        clearCSSAnimationsTimeout();\n        /**\n         * Ensure that clean up\n         * is always done a frame\n         * before the onFinish handlers\n         * are fired. Otherwise, there\n         * may be flickering if a new\n         * animation is started on the same\n         * element too quickly\n         */\n        raf(() => {\n          clearCSSAnimationPlayState();\n          raf(animationFinish);\n        });\n      });\n    }\n  };\n  const clearCSSAnimationPlayState = () => {\n    elements.forEach(element => {\n      removeStyleProperty(element, 'animation-duration');\n      removeStyleProperty(element, 'animation-delay');\n      removeStyleProperty(element, 'animation-play-state');\n    });\n  };\n  const playWebAnimations = () => {\n    webAnimations.forEach(animation => {\n      animation.play();\n    });\n    if (_keyframes.length === 0 || elements.length === 0) {\n      animationFinish();\n    }\n  };\n  const resetAnimation = () => {\n    if (supportsWebAnimations) {\n      setAnimationStep(0);\n      updateWebAnimation();\n    } else {\n      updateCSSAnimation();\n    }\n  };\n  const play = opts => {\n    return new Promise(resolve => {\n      if (opts === null || opts === void 0 ? void 0 : opts.sync) {\n        shouldForceSyncPlayback = true;\n        onFinish(() => shouldForceSyncPlayback = false, {\n          oneTimeCallback: true\n        });\n      }\n      if (!initialized) {\n        initializeAnimation();\n      }\n      if (finished) {\n        resetAnimation();\n        finished = false;\n      }\n      if (shouldCalculateNumAnimations) {\n        numAnimationsRunning = childAnimations.length + 1;\n        shouldCalculateNumAnimations = false;\n      }\n      /**\n       * When one of these callbacks fires we\n       * need to clear the other's callback otherwise\n       * you can potentially get these callbacks\n       * firing multiple times if the play method\n       * is subsequently called.\n       * Example:\n       * animation.play() (onStop and onFinish callbacks are registered)\n       * animation.stop() (onStop callback is fired, onFinish is not)\n       * animation.play() (onStop and onFinish callbacks are registered)\n       * Total onStop callbacks: 1\n       * Total onFinish callbacks: 2\n       */\n      const onStopCallback = () => {\n        clearCallback(onFinishCallback, onFinishOneTimeCallbacks);\n        resolve();\n      };\n      const onFinishCallback = () => {\n        clearCallback(onStopCallback, onStopOneTimeCallbacks);\n        resolve();\n      };\n      /**\n       * The play method resolves when an animation\n       * run either finishes or is cancelled.\n       */\n      onFinish(onFinishCallback, {\n        oneTimeCallback: true\n      });\n      onStop(onStopCallback, {\n        oneTimeCallback: true\n      });\n      childAnimations.forEach(animation => {\n        animation.play();\n      });\n      if (supportsWebAnimations) {\n        playWebAnimations();\n      } else {\n        playCSSAnimations();\n      }\n      paused = false;\n    });\n  };\n  /**\n   * Stops an animation and resets it state to the\n   * beginning. This does not fire any onFinish\n   * callbacks because the animation did not finish.\n   * However, since the animation was not destroyed\n   * (i.e. the animation could run again) we do not\n   * clear the onFinish callbacks.\n   */\n  const stop = () => {\n    childAnimations.forEach(animation => {\n      animation.stop();\n    });\n    if (initialized) {\n      cleanUpElements();\n      initialized = false;\n    }\n    resetFlags();\n    onStopOneTimeCallbacks.forEach(onStopCallback => onStopCallback.c(0, ani));\n    onStopOneTimeCallbacks.length = 0;\n  };\n  const from = (property, value) => {\n    const firstFrame = _keyframes[0];\n    if (firstFrame !== undefined && (firstFrame.offset === undefined || firstFrame.offset === 0)) {\n      firstFrame[property] = value;\n    } else {\n      _keyframes = [{\n        offset: 0,\n        [property]: value\n      }, ..._keyframes];\n    }\n    return ani;\n  };\n  const to = (property, value) => {\n    const lastFrame = _keyframes[_keyframes.length - 1];\n    if (lastFrame !== undefined && (lastFrame.offset === undefined || lastFrame.offset === 1)) {\n      lastFrame[property] = value;\n    } else {\n      _keyframes = [..._keyframes, {\n        offset: 1,\n        [property]: value\n      }];\n    }\n    return ani;\n  };\n  const fromTo = (property, fromValue, toValue) => {\n    return from(property, fromValue).to(property, toValue);\n  };\n  return ani = {\n    parentAnimation,\n    elements,\n    childAnimations,\n    id,\n    animationFinish,\n    from,\n    to,\n    fromTo,\n    parent,\n    play,\n    pause,\n    stop,\n    destroy,\n    keyframes,\n    addAnimation,\n    addElement,\n    update,\n    fill,\n    direction,\n    iterations,\n    duration,\n    easing,\n    delay,\n    getWebAnimations,\n    getKeyframes,\n    getFill,\n    getDirection,\n    getDelay,\n    getIterations,\n    getEasing,\n    getDuration,\n    afterAddRead,\n    afterAddWrite,\n    afterClearStyles,\n    afterStyles,\n    afterRemoveClass,\n    afterAddClass,\n    beforeAddRead,\n    beforeAddWrite,\n    beforeClearStyles,\n    beforeStyles,\n    beforeRemoveClass,\n    beforeAddClass,\n    onFinish,\n    isRunning,\n    progressStart,\n    progressStep,\n    progressEnd\n  };\n};\nexport { createAnimation as c };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}