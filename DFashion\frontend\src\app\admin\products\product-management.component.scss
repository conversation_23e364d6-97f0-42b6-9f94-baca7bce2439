.product-management {
  padding: 1rem;
}

.filters-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  align-items: center;

  mat-form-field {
    min-width: 200px;
  }

  button {
    height: fit-content;
    margin-top: 0.5rem;
  }
}

.table-container {
  overflow-x: auto;
  margin-bottom: 1rem;
}

.product-cell {
  display: flex;
  align-items: center;
  gap: 1rem;

  .product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
  }

  .product-info {
    .product-name {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .product-brand {
      font-size: 0.875rem;
      color: #666;
    }
  }
}

.category-text {
  font-weight: 500;
}

.subcategory-text {
  color: #666;
}

.price-cell {
  .current-price {
    font-weight: 600;
    color: #2e7d32;
  }

  .original-price {
    text-decoration: line-through;
    color: #999;
    margin-left: 0.5rem;
    font-size: 0.875rem;
  }

  .discount {
    background: #ff5722;
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-left: 0.5rem;
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status-text {
    font-size: 0.875rem;
  }
}

.actions-cell {
  display: flex;
  gap: 0.25rem;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #666;

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    margin: 1rem 0 0.5rem 0;
    font-weight: 400;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

@media (max-width: 768px) {
  .filters-section {
    flex-direction: column;
    align-items: stretch;

    mat-form-field {
      min-width: auto;
      width: 100%;
    }
  }

  .product-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;

    .product-image {
      width: 40px;
      height: 40px;
    }
  }
}
