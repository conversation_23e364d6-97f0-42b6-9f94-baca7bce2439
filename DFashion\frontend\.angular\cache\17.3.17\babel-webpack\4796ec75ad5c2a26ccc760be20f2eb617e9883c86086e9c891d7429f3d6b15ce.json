{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AdminAuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = 'http://localhost:5000/api';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.tokenSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.token$ = this.tokenSubject.asObservable();\n    // Check for existing token on service initialization\n    const token = localStorage.getItem('admin_token');\n    const user = localStorage.getItem('admin_user');\n    if (token && user) {\n      this.tokenSubject.next(token);\n      this.currentUserSubject.next(JSON.parse(user));\n    }\n  }\n  // Login\n  login(email, password) {\n    return this.http.post(`${this.apiUrl}/auth/admin/login`, {\n      email,\n      password\n    }).pipe(tap(response => {\n      if (response.success) {\n        // Store token and user data\n        localStorage.setItem('admin_token', response.data.token);\n        localStorage.setItem('admin_user', JSON.stringify(response.data.user));\n        // Update subjects\n        this.tokenSubject.next(response.data.token);\n        this.currentUserSubject.next(response.data.user);\n      }\n    }), catchError(error => {\n      console.error('Login error:', error);\n      return throwError(error);\n    }));\n  }\n  // Logout\n  logout() {\n    // Clear local storage\n    localStorage.removeItem('admin_token');\n    localStorage.removeItem('admin_user');\n    // Clear subjects\n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    // Redirect to login\n    this.router.navigate(['/admin/login']);\n  }\n  // Get current user\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  // Get current token\n  getToken() {\n    return this.tokenSubject.value;\n  }\n  // Check if user is authenticated\n  isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    return !!(token && user);\n  }\n  // Check if user has specific permission\n  hasPermission(module, action) {\n    const user = this.getCurrentUser();\n    if (!user) return false;\n    // Super admin has all permissions\n    if (user.role === 'super_admin') return true;\n    // Check specific permission\n    return user.permissions?.some(permission => permission.module === module && permission.actions.includes(action)) || false;\n  }\n  // Check if user has any of the specified roles\n  hasRole(roles) {\n    const user = this.getCurrentUser();\n    if (!user) return false;\n    const allowedRoles = Array.isArray(roles) ? roles : [roles];\n    return allowedRoles.includes(user.role);\n  }\n  // Verify token with server\n  verifyToken() {\n    const token = this.getToken();\n    if (!token) {\n      return throwError('No token found');\n    }\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    return this.http.get(`${this.apiUrl}/auth/verify`, {\n      headers\n    }).pipe(tap(response => {\n      // Token is valid, update user data if needed\n      if (response && response.data?.user) {\n        this.currentUserSubject.next(response.data.user);\n      }\n    }), catchError(error => {\n      // Token is invalid, logout user\n      this.logout();\n      return throwError(error);\n    }));\n  }\n  // Get authorization headers\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders().set('Authorization', `Bearer ${token}`);\n  }\n  // Refresh user data\n  refreshUserData() {\n    return this.verifyToken().pipe(map(response => response.data.user));\n  }\n  // Update user profile\n  updateProfile(profileData) {\n    const headers = this.getAuthHeaders();\n    return this.http.put(`${this.apiUrl}/admin/profile`, profileData, {\n      headers\n    }).pipe(tap(response => {\n      if (response && response.success) {\n        // Update current user data\n        const currentUser = this.getCurrentUser();\n        if (currentUser) {\n          const updatedUser = {\n            ...currentUser,\n            ...profileData\n          };\n          this.currentUserSubject.next(updatedUser);\n          localStorage.setItem('admin_user', JSON.stringify(updatedUser));\n        }\n      }\n    }));\n  }\n  // Change password\n  changePassword(currentPassword, newPassword) {\n    const headers = this.getAuthHeaders();\n    return this.http.post(`${this.apiUrl}/admin/change-password`, {\n      currentPassword,\n      newPassword\n    }, {\n      headers\n    });\n  }\n  // Get user permissions for display\n  getUserPermissions() {\n    const user = this.getCurrentUser();\n    return user?.permissions || [];\n  }\n  // Check if user can access admin panel\n  canAccessAdmin() {\n    const adminRoles = ['super_admin', 'admin', 'sales_manager', 'marketing_manager', 'account_manager', 'support_manager', 'sales_executive', 'marketing_executive', 'account_executive', 'support_executive'];\n    return this.hasRole(adminRoles);\n  }\n  // Get user's department\n  getUserDepartment() {\n    const user = this.getCurrentUser();\n    return user?.department || '';\n  }\n  // Get user's role display name\n  getRoleDisplayName() {\n    const user = this.getCurrentUser();\n    if (!user) return '';\n    const roleNames = {\n      'super_admin': 'Super Administrator',\n      'admin': 'Administrator',\n      'sales_manager': 'Sales Manager',\n      'marketing_manager': 'Marketing Manager',\n      'account_manager': 'Account Manager',\n      'support_manager': 'Support Manager',\n      'sales_executive': 'Sales Executive',\n      'marketing_executive': 'Marketing Executive',\n      'account_executive': 'Account Executive',\n      'support_executive': 'Support Executive'\n    };\n    return roleNames[user.role] || user.role;\n  }\n  // Auto-logout on token expiration\n  setupTokenExpiration() {\n    // This would typically decode JWT to get expiration time\n    // For now, we'll set a timeout for 8 hours (token expiry time)\n    setTimeout(() => {\n      this.logout();\n    }, 8 * 60 * 60 * 1000); // 8 hours\n  }\n  static {\n    this.ɵfac = function AdminAuthService_Factory(t) {\n      return new (t || AdminAuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminAuthService,\n      factory: AdminAuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "map", "catchError", "tap", "AdminAuthService", "constructor", "http", "router", "apiUrl", "currentUserSubject", "tokenSubject", "currentUser$", "asObservable", "token$", "token", "localStorage", "getItem", "user", "next", "JSON", "parse", "login", "email", "password", "post", "pipe", "response", "success", "setItem", "data", "stringify", "error", "console", "logout", "removeItem", "navigate", "getCurrentUser", "value", "getToken", "isAuthenticated", "hasPermission", "module", "action", "role", "permissions", "some", "permission", "actions", "includes", "hasRole", "roles", "allowedRoles", "Array", "isArray", "verifyToken", "headers", "set", "get", "getAuthHeaders", "refreshUserData", "updateProfile", "profileData", "put", "currentUser", "updatedUser", "changePassword", "currentPassword", "newPassword", "getUserPermissions", "canAccessAdmin", "adminRoles", "getUserDepartment", "department", "getRoleDisplayName", "roleNames", "setupTokenExpiration", "setTimeout", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\services\\admin-auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\n\nexport interface AdminUser {\n  id: string;\n  email: string;\n  fullName: string;\n  role: string;\n  department: string;\n  employeeId: string;\n  permissions: Permission[];\n  avatar?: string;\n}\n\nexport interface Permission {\n  module: string;\n  actions: string[];\n}\n\nexport interface LoginResponse {\n  success: boolean;\n  message: string;\n  data: {\n    token: string;\n    user: AdminUser;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminAuthService {\n  private apiUrl = 'http://localhost:5000/api';\n  private currentUserSubject = new BehaviorSubject<AdminUser | null>(null);\n  private tokenSubject = new BehaviorSubject<string | null>(null);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public token$ = this.tokenSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    // Check for existing token on service initialization\n    const token = localStorage.getItem('admin_token');\n    const user = localStorage.getItem('admin_user');\n    \n    if (token && user) {\n      this.tokenSubject.next(token);\n      this.currentUserSubject.next(JSON.parse(user));\n    }\n  }\n\n  // Login\n  login(email: string, password: string): Observable<LoginResponse> {\n    return this.http.post<LoginResponse>(`${this.apiUrl}/auth/admin/login`, {\n      email,\n      password\n    }).pipe(\n      tap(response => {\n        if (response.success) {\n          // Store token and user data\n          localStorage.setItem('admin_token', response.data.token);\n          localStorage.setItem('admin_user', JSON.stringify(response.data.user));\n          \n          // Update subjects\n          this.tokenSubject.next(response.data.token);\n          this.currentUserSubject.next(response.data.user);\n        }\n      }),\n      catchError(error => {\n        console.error('Login error:', error);\n        return throwError(error);\n      })\n    );\n  }\n\n  // Logout\n  logout(): void {\n    // Clear local storage\n    localStorage.removeItem('admin_token');\n    localStorage.removeItem('admin_user');\n    \n    // Clear subjects\n    this.tokenSubject.next(null);\n    this.currentUserSubject.next(null);\n    \n    // Redirect to login\n    this.router.navigate(['/admin/login']);\n  }\n\n  // Get current user\n  getCurrentUser(): AdminUser | null {\n    return this.currentUserSubject.value;\n  }\n\n  // Get current token\n  getToken(): string | null {\n    return this.tokenSubject.value;\n  }\n\n  // Check if user is authenticated\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    return !!(token && user);\n  }\n\n  // Check if user has specific permission\n  hasPermission(module: string, action: string): boolean {\n    const user = this.getCurrentUser();\n    if (!user) return false;\n\n    // Super admin has all permissions\n    if (user.role === 'super_admin') return true;\n\n    // Check specific permission\n    return user.permissions?.some(permission => \n      permission.module === module && permission.actions.includes(action)\n    ) || false;\n  }\n\n  // Check if user has any of the specified roles\n  hasRole(roles: string | string[]): boolean {\n    const user = this.getCurrentUser();\n    if (!user) return false;\n\n    const allowedRoles = Array.isArray(roles) ? roles : [roles];\n    return allowedRoles.includes(user.role);\n  }\n\n  // Verify token with server\n  verifyToken(): Observable<any> {\n    const token = this.getToken();\n    if (!token) {\n      return throwError('No token found');\n    }\n\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    \n    return this.http.get(`${this.apiUrl}/auth/verify`, { headers }).pipe(\n      tap(response => {\n        // Token is valid, update user data if needed\n        if (response && (response as any).data?.user) {\n          this.currentUserSubject.next((response as any).data.user);\n        }\n      }),\n      catchError(error => {\n        // Token is invalid, logout user\n        this.logout();\n        return throwError(error);\n      })\n    );\n  }\n\n  // Get authorization headers\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders().set('Authorization', `Bearer ${token}`);\n  }\n\n  // Refresh user data\n  refreshUserData(): Observable<AdminUser> {\n    return this.verifyToken().pipe(\n      map(response => response.data.user)\n    );\n  }\n\n  // Update user profile\n  updateProfile(profileData: Partial<AdminUser>): Observable<any> {\n    const headers = this.getAuthHeaders();\n    \n    return this.http.put(`${this.apiUrl}/admin/profile`, profileData, { headers }).pipe(\n      tap(response => {\n        if (response && (response as any).success) {\n          // Update current user data\n          const currentUser = this.getCurrentUser();\n          if (currentUser) {\n            const updatedUser = { ...currentUser, ...profileData };\n            this.currentUserSubject.next(updatedUser);\n            localStorage.setItem('admin_user', JSON.stringify(updatedUser));\n          }\n        }\n      })\n    );\n  }\n\n  // Change password\n  changePassword(currentPassword: string, newPassword: string): Observable<any> {\n    const headers = this.getAuthHeaders();\n    \n    return this.http.post(`${this.apiUrl}/admin/change-password`, {\n      currentPassword,\n      newPassword\n    }, { headers });\n  }\n\n  // Get user permissions for display\n  getUserPermissions(): Permission[] {\n    const user = this.getCurrentUser();\n    return user?.permissions || [];\n  }\n\n  // Check if user can access admin panel\n  canAccessAdmin(): boolean {\n    const adminRoles = [\n      'super_admin', 'admin', 'sales_manager', 'marketing_manager',\n      'account_manager', 'support_manager', 'sales_executive',\n      'marketing_executive', 'account_executive', 'support_executive'\n    ];\n    \n    return this.hasRole(adminRoles);\n  }\n\n  // Get user's department\n  getUserDepartment(): string {\n    const user = this.getCurrentUser();\n    return user?.department || '';\n  }\n\n  // Get user's role display name\n  getRoleDisplayName(): string {\n    const user = this.getCurrentUser();\n    if (!user) return '';\n\n    const roleNames: { [key: string]: string } = {\n      'super_admin': 'Super Administrator',\n      'admin': 'Administrator',\n      'sales_manager': 'Sales Manager',\n      'marketing_manager': 'Marketing Manager',\n      'account_manager': 'Account Manager',\n      'support_manager': 'Support Manager',\n      'sales_executive': 'Sales Executive',\n      'marketing_executive': 'Marketing Executive',\n      'account_executive': 'Account Executive',\n      'support_executive': 'Support Executive'\n    };\n\n    return roleNames[user.role] || user.role;\n  }\n\n  // Auto-logout on token expiration\n  private setupTokenExpiration(): void {\n    // This would typically decode JWT to get expiration time\n    // For now, we'll set a timeout for 8 hours (token expiry time)\n    setTimeout(() => {\n      this.logout();\n    }, 8 * 60 * 60 * 1000); // 8 hours\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AA+BrD,OAAM,MAAOC,gBAAgB;EAQ3BC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATR,KAAAC,MAAM,GAAG,2BAA2B;IACpC,KAAAC,kBAAkB,GAAG,IAAIV,eAAe,CAAmB,IAAI,CAAC;IAChE,KAAAW,YAAY,GAAG,IAAIX,eAAe,CAAgB,IAAI,CAAC;IAExD,KAAAY,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,MAAM,GAAG,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAM9C;IACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAE/C,IAAIF,KAAK,IAAIG,IAAI,EAAE;MACjB,IAAI,CAACP,YAAY,CAACQ,IAAI,CAACJ,KAAK,CAAC;MAC7B,IAAI,CAACL,kBAAkB,CAACS,IAAI,CAACC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAAC;;EAElD;EAEA;EACAI,KAAKA,CAACC,KAAa,EAAEC,QAAgB;IACnC,OAAO,IAAI,CAACjB,IAAI,CAACkB,IAAI,CAAgB,GAAG,IAAI,CAAChB,MAAM,mBAAmB,EAAE;MACtEc,KAAK;MACLC;KACD,CAAC,CAACE,IAAI,CACLtB,GAAG,CAACuB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACAZ,YAAY,CAACa,OAAO,CAAC,aAAa,EAAEF,QAAQ,CAACG,IAAI,CAACf,KAAK,CAAC;QACxDC,YAAY,CAACa,OAAO,CAAC,YAAY,EAAET,IAAI,CAACW,SAAS,CAACJ,QAAQ,CAACG,IAAI,CAACZ,IAAI,CAAC,CAAC;QAEtE;QACA,IAAI,CAACP,YAAY,CAACQ,IAAI,CAACQ,QAAQ,CAACG,IAAI,CAACf,KAAK,CAAC;QAC3C,IAAI,CAACL,kBAAkB,CAACS,IAAI,CAACQ,QAAQ,CAACG,IAAI,CAACZ,IAAI,CAAC;;IAEpD,CAAC,CAAC,EACFf,UAAU,CAAC6B,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO/B,UAAU,CAAC+B,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAEA;EACAE,MAAMA,CAAA;IACJ;IACAlB,YAAY,CAACmB,UAAU,CAAC,aAAa,CAAC;IACtCnB,YAAY,CAACmB,UAAU,CAAC,YAAY,CAAC;IAErC;IACA,IAAI,CAACxB,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACT,kBAAkB,CAACS,IAAI,CAAC,IAAI,CAAC;IAElC;IACA,IAAI,CAACX,MAAM,CAAC4B,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEA;EACAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC3B,kBAAkB,CAAC4B,KAAK;EACtC;EAEA;EACAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAAC5B,YAAY,CAAC2B,KAAK;EAChC;EAEA;EACAE,eAAeA,CAAA;IACb,MAAMzB,KAAK,GAAG,IAAI,CAACwB,QAAQ,EAAE;IAC7B,MAAMrB,IAAI,GAAG,IAAI,CAACmB,cAAc,EAAE;IAClC,OAAO,CAAC,EAAEtB,KAAK,IAAIG,IAAI,CAAC;EAC1B;EAEA;EACAuB,aAAaA,CAACC,MAAc,EAAEC,MAAc;IAC1C,MAAMzB,IAAI,GAAG,IAAI,CAACmB,cAAc,EAAE;IAClC,IAAI,CAACnB,IAAI,EAAE,OAAO,KAAK;IAEvB;IACA,IAAIA,IAAI,CAAC0B,IAAI,KAAK,aAAa,EAAE,OAAO,IAAI;IAE5C;IACA,OAAO1B,IAAI,CAAC2B,WAAW,EAAEC,IAAI,CAACC,UAAU,IACtCA,UAAU,CAACL,MAAM,KAAKA,MAAM,IAAIK,UAAU,CAACC,OAAO,CAACC,QAAQ,CAACN,MAAM,CAAC,CACpE,IAAI,KAAK;EACZ;EAEA;EACAO,OAAOA,CAACC,KAAwB;IAC9B,MAAMjC,IAAI,GAAG,IAAI,CAACmB,cAAc,EAAE;IAClC,IAAI,CAACnB,IAAI,EAAE,OAAO,KAAK;IAEvB,MAAMkC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;IAC3D,OAAOC,YAAY,CAACH,QAAQ,CAAC/B,IAAI,CAAC0B,IAAI,CAAC;EACzC;EAEA;EACAW,WAAWA,CAAA;IACT,MAAMxC,KAAK,GAAG,IAAI,CAACwB,QAAQ,EAAE;IAC7B,IAAI,CAACxB,KAAK,EAAE;MACV,OAAOd,UAAU,CAAC,gBAAgB,CAAC;;IAGrC,MAAMuD,OAAO,GAAG,IAAIzD,WAAW,EAAE,CAAC0D,GAAG,CAAC,eAAe,EAAE,UAAU1C,KAAK,EAAE,CAAC;IAEzE,OAAO,IAAI,CAACR,IAAI,CAACmD,GAAG,CAAC,GAAG,IAAI,CAACjD,MAAM,cAAc,EAAE;MAAE+C;IAAO,CAAE,CAAC,CAAC9B,IAAI,CAClEtB,GAAG,CAACuB,QAAQ,IAAG;MACb;MACA,IAAIA,QAAQ,IAAKA,QAAgB,CAACG,IAAI,EAAEZ,IAAI,EAAE;QAC5C,IAAI,CAACR,kBAAkB,CAACS,IAAI,CAAEQ,QAAgB,CAACG,IAAI,CAACZ,IAAI,CAAC;;IAE7D,CAAC,CAAC,EACFf,UAAU,CAAC6B,KAAK,IAAG;MACjB;MACA,IAAI,CAACE,MAAM,EAAE;MACb,OAAOjC,UAAU,CAAC+B,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAEA;EACA2B,cAAcA,CAAA;IACZ,MAAM5C,KAAK,GAAG,IAAI,CAACwB,QAAQ,EAAE;IAC7B,OAAO,IAAIxC,WAAW,EAAE,CAAC0D,GAAG,CAAC,eAAe,EAAE,UAAU1C,KAAK,EAAE,CAAC;EAClE;EAEA;EACA6C,eAAeA,CAAA;IACb,OAAO,IAAI,CAACL,WAAW,EAAE,CAAC7B,IAAI,CAC5BxB,GAAG,CAACyB,QAAQ,IAAIA,QAAQ,CAACG,IAAI,CAACZ,IAAI,CAAC,CACpC;EACH;EAEA;EACA2C,aAAaA,CAACC,WAA+B;IAC3C,MAAMN,OAAO,GAAG,IAAI,CAACG,cAAc,EAAE;IAErC,OAAO,IAAI,CAACpD,IAAI,CAACwD,GAAG,CAAC,GAAG,IAAI,CAACtD,MAAM,gBAAgB,EAAEqD,WAAW,EAAE;MAAEN;IAAO,CAAE,CAAC,CAAC9B,IAAI,CACjFtB,GAAG,CAACuB,QAAQ,IAAG;MACb,IAAIA,QAAQ,IAAKA,QAAgB,CAACC,OAAO,EAAE;QACzC;QACA,MAAMoC,WAAW,GAAG,IAAI,CAAC3B,cAAc,EAAE;QACzC,IAAI2B,WAAW,EAAE;UACf,MAAMC,WAAW,GAAG;YAAE,GAAGD,WAAW;YAAE,GAAGF;UAAW,CAAE;UACtD,IAAI,CAACpD,kBAAkB,CAACS,IAAI,CAAC8C,WAAW,CAAC;UACzCjD,YAAY,CAACa,OAAO,CAAC,YAAY,EAAET,IAAI,CAACW,SAAS,CAACkC,WAAW,CAAC,CAAC;;;IAGrE,CAAC,CAAC,CACH;EACH;EAEA;EACAC,cAAcA,CAACC,eAAuB,EAAEC,WAAmB;IACzD,MAAMZ,OAAO,GAAG,IAAI,CAACG,cAAc,EAAE;IAErC,OAAO,IAAI,CAACpD,IAAI,CAACkB,IAAI,CAAC,GAAG,IAAI,CAAChB,MAAM,wBAAwB,EAAE;MAC5D0D,eAAe;MACfC;KACD,EAAE;MAAEZ;IAAO,CAAE,CAAC;EACjB;EAEA;EACAa,kBAAkBA,CAAA;IAChB,MAAMnD,IAAI,GAAG,IAAI,CAACmB,cAAc,EAAE;IAClC,OAAOnB,IAAI,EAAE2B,WAAW,IAAI,EAAE;EAChC;EAEA;EACAyB,cAAcA,CAAA;IACZ,MAAMC,UAAU,GAAG,CACjB,aAAa,EAAE,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAC5D,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EACvD,qBAAqB,EAAE,mBAAmB,EAAE,mBAAmB,CAChE;IAED,OAAO,IAAI,CAACrB,OAAO,CAACqB,UAAU,CAAC;EACjC;EAEA;EACAC,iBAAiBA,CAAA;IACf,MAAMtD,IAAI,GAAG,IAAI,CAACmB,cAAc,EAAE;IAClC,OAAOnB,IAAI,EAAEuD,UAAU,IAAI,EAAE;EAC/B;EAEA;EACAC,kBAAkBA,CAAA;IAChB,MAAMxD,IAAI,GAAG,IAAI,CAACmB,cAAc,EAAE;IAClC,IAAI,CAACnB,IAAI,EAAE,OAAO,EAAE;IAEpB,MAAMyD,SAAS,GAA8B;MAC3C,aAAa,EAAE,qBAAqB;MACpC,OAAO,EAAE,eAAe;MACxB,eAAe,EAAE,eAAe;MAChC,mBAAmB,EAAE,mBAAmB;MACxC,iBAAiB,EAAE,iBAAiB;MACpC,iBAAiB,EAAE,iBAAiB;MACpC,iBAAiB,EAAE,iBAAiB;MACpC,qBAAqB,EAAE,qBAAqB;MAC5C,mBAAmB,EAAE,mBAAmB;MACxC,mBAAmB,EAAE;KACtB;IAED,OAAOA,SAAS,CAACzD,IAAI,CAAC0B,IAAI,CAAC,IAAI1B,IAAI,CAAC0B,IAAI;EAC1C;EAEA;EACQgC,oBAAoBA,CAAA;IAC1B;IACA;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3C,MAAM,EAAE;IACf,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;EAC1B;;;uBAzNW7B,gBAAgB,EAAAyE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAhB9E,gBAAgB;MAAA+E,OAAA,EAAhB/E,gBAAgB,CAAAgF,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}