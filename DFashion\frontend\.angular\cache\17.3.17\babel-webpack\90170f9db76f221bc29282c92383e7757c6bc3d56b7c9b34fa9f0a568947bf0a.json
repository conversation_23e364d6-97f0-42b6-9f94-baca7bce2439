{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./role-management.service\";\nexport let PermissionManagementService = /*#__PURE__*/(() => {\n  class PermissionManagementService {\n    constructor(roleManagementService) {\n      this.roleManagementService = roleManagementService;\n      this.currentUserSubject = new BehaviorSubject(null);\n      this.permissionCacheSubject = new BehaviorSubject(new Map());\n      this.currentUser$ = this.currentUserSubject.asObservable();\n      this.permissionCache$ = this.permissionCacheSubject.asObservable();\n      // Feature flags for different functionalities\n      this.featureFlags = [{\n        name: 'advanced_analytics',\n        enabled: true,\n        roles: ['super_admin', 'admin', 'sales_manager', 'marketing_manager']\n      }, {\n        name: 'team_management',\n        enabled: true,\n        condition: user => this.roleManagementService.isManager(user.role)\n      }, {\n        name: 'financial_reports',\n        enabled: true,\n        roles: ['super_admin', 'admin', 'account_manager', 'accountant']\n      }, {\n        name: 'user_management',\n        enabled: true,\n        roles: ['super_admin', 'admin']\n      }, {\n        name: 'system_settings',\n        enabled: true,\n        roles: ['super_admin', 'admin']\n      }, {\n        name: 'content_moderation',\n        enabled: true,\n        roles: ['super_admin', 'admin', 'content_manager']\n      }, {\n        name: 'vendor_management',\n        enabled: true,\n        roles: ['super_admin', 'admin', 'vendor_manager']\n      }, {\n        name: 'support_escalation',\n        enabled: true,\n        roles: ['super_admin', 'admin', 'support_manager']\n      }];\n      // UI component permissions\n      this.uiPermissions = [{\n        component: 'admin_panel',\n        action: 'view',\n        condition: user => ['super_admin', 'admin'].includes(user.role)\n      }, {\n        component: 'user_list',\n        action: 'view',\n        condition: user => this.hasPermission(user.role, 'users', 'read')\n      }, {\n        component: 'user_create',\n        action: 'view',\n        condition: user => this.hasPermission(user.role, 'users', 'create')\n      }, {\n        component: 'user_edit',\n        action: 'view',\n        condition: user => this.hasPermission(user.role, 'users', 'update')\n      }, {\n        component: 'user_delete',\n        action: 'view',\n        condition: user => this.hasPermission(user.role, 'users', 'delete')\n      }, {\n        component: 'team_dashboard',\n        action: 'view',\n        condition: user => this.roleManagementService.isManager(user.role)\n      }, {\n        component: 'financial_dashboard',\n        action: 'view',\n        condition: user => ['account_manager', 'accountant'].includes(user.role)\n      }, {\n        component: 'sales_reports',\n        action: 'view',\n        condition: user => user.role.includes('sales')\n      }, {\n        component: 'marketing_campaigns',\n        action: 'view',\n        condition: user => user.role.includes('marketing')\n      }, {\n        component: 'support_tickets',\n        action: 'view',\n        condition: user => user.role.includes('support')\n      }, {\n        component: 'content_editor',\n        action: 'view',\n        condition: user => ['content_manager', 'marketing_manager', 'marketing_executive'].includes(user.role)\n      }, {\n        component: 'vendor_contracts',\n        action: 'view',\n        condition: user => user.role === 'vendor_manager'\n      }];\n    }\n    setCurrentUser(user) {\n      this.currentUserSubject.next(user);\n      this.clearPermissionCache();\n    }\n    getCurrentUser() {\n      return this.currentUserSubject.value;\n    }\n    /**\n     * Check if current user has specific permission\n     */\n    hasPermission(role, module, action, scope) {\n      const cacheKey = `${role}_${module}_${action}_${scope || 'default'}`;\n      const cache = this.permissionCacheSubject.value;\n      if (cache.has(cacheKey)) {\n        return cache.get(cacheKey);\n      }\n      const hasAccess = this.roleManagementService.hasPermission(role, module, action);\n      // Cache the result\n      cache.set(cacheKey, hasAccess);\n      this.permissionCacheSubject.next(cache);\n      return hasAccess;\n    }\n    /**\n     * Check multiple permissions at once\n     */\n    hasAnyPermission(role, checks) {\n      return checks.some(check => this.hasPermission(role, check.module, check.action, check.scope));\n    }\n    /**\n     * Check if all permissions are granted\n     */\n    hasAllPermissions(role, checks) {\n      return checks.every(check => this.hasPermission(role, check.module, check.action, check.scope));\n    }\n    /**\n     * Get accessible modules for current user\n     */\n    getAccessibleModules(role) {\n      return this.roleManagementService.getAccessibleModules(role);\n    }\n    /**\n     * Check if user can access specific UI component\n     */\n    canAccessComponent(componentName, action = 'view') {\n      return this.currentUser$.pipe(map(user => {\n        if (!user) return false;\n        const permission = this.uiPermissions.find(p => p.component === componentName && p.action === action);\n        if (!permission) return false;\n        return permission.condition ? permission.condition(user) : true;\n      }));\n    }\n    /**\n     * Check if feature flag is enabled for current user\n     */\n    isFeatureEnabled(featureName) {\n      return this.currentUser$.pipe(map(user => {\n        if (!user) return false;\n        const feature = this.featureFlags.find(f => f.name === featureName);\n        if (!feature || !feature.enabled) return false;\n        // Check role-based access\n        if (feature.roles && !feature.roles.includes(user.role)) {\n          return false;\n        }\n        // Check department-based access\n        if (feature.departments && !feature.departments.includes(user.department)) {\n          return false;\n        }\n        // Check custom condition\n        if (feature.condition && !feature.condition(user)) {\n          return false;\n        }\n        return true;\n      }));\n    }\n    /**\n     * Get all enabled features for current user\n     */\n    getEnabledFeatures() {\n      return this.currentUser$.pipe(map(user => {\n        if (!user) return [];\n        return this.featureFlags.filter(feature => {\n          if (!feature.enabled) return false;\n          if (feature.roles && !feature.roles.includes(user.role)) {\n            return false;\n          }\n          if (feature.departments && !feature.departments.includes(user.department)) {\n            return false;\n          }\n          if (feature.condition && !feature.condition(user)) {\n            return false;\n          }\n          return true;\n        }).map(feature => feature.name);\n      }));\n    }\n    /**\n     * Check if user can manage other users\n     */\n    canManageUsers(targetRole) {\n      return this.currentUser$.pipe(map(user => {\n        if (!user) return false;\n        // Super admin can manage everyone\n        if (user.role === 'super_admin') return true;\n        // Admin can manage non-admin users\n        if (user.role === 'admin' && targetRole !== 'super_admin') return true;\n        // Managers can manage their subordinates\n        if (this.roleManagementService.isManager(user.role) && targetRole) {\n          const subordinates = this.roleManagementService.getSubordinateRoles(user.role);\n          return subordinates.includes(targetRole);\n        }\n        return false;\n      }));\n    }\n    /**\n     * Get permission scope for user\n     */\n    getPermissionScope(role, module) {\n      const config = this.roleManagementService.getRoleConfig(role);\n      const permission = config.permissions.find(p => p.module === module || p.module === '*');\n      return permission?.scope || 'self';\n    }\n    /**\n     * Check if user can access resource based on scope\n     */\n    canAccessResource(resourceOwnerId, resourceDepartment) {\n      return this.currentUser$.pipe(map(user => {\n        if (!user) return false;\n        // Super admin has global access\n        if (user.role === 'super_admin') return true;\n        // Check if user owns the resource\n        if (user.id === resourceOwnerId) return true;\n        // Check department-level access\n        if (resourceDepartment && user.department === resourceDepartment) {\n          const scope = this.getPermissionScope(user.role, 'resources');\n          return ['global', 'department'].includes(scope);\n        }\n        // Check team-level access for managers\n        if (this.roleManagementService.isManager(user.role)) {\n          const scope = this.getPermissionScope(user.role, 'resources');\n          return ['global', 'department', 'team'].includes(scope);\n        }\n        return false;\n      }));\n    }\n    /**\n     * Get filtered navigation items based on permissions\n     */\n    getFilteredNavigation(navigationItems) {\n      return this.currentUser$.pipe(map(user => {\n        if (!user) return [];\n        return navigationItems.filter(item => {\n          if (!item.permission) return true;\n          const [module, action] = item.permission.split('.');\n          return this.hasPermission(user.role, module, action);\n        });\n      }));\n    }\n    /**\n     * Clear permission cache\n     */\n    clearPermissionCache() {\n      this.permissionCacheSubject.next(new Map());\n    }\n    /**\n     * Enable/disable feature flag\n     */\n    setFeatureFlag(featureName, enabled) {\n      const feature = this.featureFlags.find(f => f.name === featureName);\n      if (feature) {\n        feature.enabled = enabled;\n      }\n    }\n    /**\n     * Add new UI permission\n     */\n    addUIPermission(permission) {\n      this.uiPermissions.push(permission);\n    }\n    /**\n     * Remove UI permission\n     */\n    removeUIPermission(componentName, action) {\n      const index = this.uiPermissions.findIndex(p => p.component === componentName && p.action === action);\n      if (index > -1) {\n        this.uiPermissions.splice(index, 1);\n      }\n    }\n    /**\n     * Get all permissions for debugging\n     */\n    getAllPermissions() {\n      return {\n        featureFlags: this.featureFlags,\n        uiPermissions: this.uiPermissions,\n        currentUser: this.getCurrentUser(),\n        permissionCache: Array.from(this.permissionCacheSubject.value.entries())\n      };\n    }\n    static {\n      this.ɵfac = function PermissionManagementService_Factory(t) {\n        return new (t || PermissionManagementService)(i0.ɵɵinject(i1.RoleManagementService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PermissionManagementService,\n        factory: PermissionManagementService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PermissionManagementService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}