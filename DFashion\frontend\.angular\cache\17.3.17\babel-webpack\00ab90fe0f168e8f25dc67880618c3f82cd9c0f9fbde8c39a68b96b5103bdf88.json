{"ast": null, "code": "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() {}\nexport class BaseXHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @package\n   */\n  constructor(opts) {\n    super(opts);\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? \"443\" : \"80\";\n      }\n      this.xd = typeof location !== \"undefined\" && opts.hostname !== location.hostname || port !== opts.port;\n    }\n  }\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr post error\", xhrStatus, context);\n    });\n  }\n  /**\n   * Starts a poll cycle.\n   *\n   * @private\n   */\n  doPoll() {\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr poll error\", xhrStatus, context);\n    });\n    this.pollXhr = req;\n  }\n}\nexport let Request = /*#__PURE__*/(() => {\n  class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n      super();\n      this.createRequest = createRequest;\n      installTimerFunctions(this, opts);\n      this._opts = opts;\n      this._method = opts.method || \"GET\";\n      this._uri = uri;\n      this._data = undefined !== opts.data ? opts.data : null;\n      this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n      var _a;\n      const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n      opts.xdomain = !!this._opts.xd;\n      const xhr = this._xhr = this.createRequest(opts);\n      try {\n        xhr.open(this._method, this._uri, true);\n        try {\n          if (this._opts.extraHeaders) {\n            // @ts-ignore\n            xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n            for (let i in this._opts.extraHeaders) {\n              if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n              }\n            }\n          }\n        } catch (e) {}\n        if (\"POST\" === this._method) {\n          try {\n            xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n          } catch (e) {}\n        }\n        try {\n          xhr.setRequestHeader(\"Accept\", \"*/*\");\n        } catch (e) {}\n        (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n        // ie6 check\n        if (\"withCredentials\" in xhr) {\n          xhr.withCredentials = this._opts.withCredentials;\n        }\n        if (this._opts.requestTimeout) {\n          xhr.timeout = this._opts.requestTimeout;\n        }\n        xhr.onreadystatechange = () => {\n          var _a;\n          if (xhr.readyState === 3) {\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n            // @ts-ignore\n            xhr.getResponseHeader(\"set-cookie\"));\n          }\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            this._onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            this.setTimeoutFn(() => {\n              this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n        xhr.send(this._data);\n      } catch (e) {\n        // Need to defer since .create() is called directly from the constructor\n        // and thus the 'error' event can only be only bound *after* this exception\n        // occurs.  Therefore, also, we cannot throw here at all.\n        this.setTimeoutFn(() => {\n          this._onError(e);\n        }, 0);\n        return;\n      }\n      if (typeof document !== \"undefined\") {\n        this._index = Request.requestsCount++;\n        Request.requests[this._index] = this;\n      }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n      this.emitReserved(\"error\", err, this._xhr);\n      this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n      if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n        return;\n      }\n      this._xhr.onreadystatechange = empty;\n      if (fromError) {\n        try {\n          this._xhr.abort();\n        } catch (e) {}\n      }\n      if (typeof document !== \"undefined\") {\n        delete Request.requests[this._index];\n      }\n      this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n      const data = this._xhr.responseText;\n      if (data !== null) {\n        this.emitReserved(\"data\", data);\n        this.emitReserved(\"success\");\n        this._cleanup();\n      }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n      this._cleanup();\n    }\n  }\n  Request.requestsCount = 0;\n  Request.requests = {};\n  /**\n   * Aborts pending requests when unloading the window. This is needed to prevent\n   * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n   * emitted.\n   */\n  return Request;\n})();\nif (typeof document !== \"undefined\") {\n  // @ts-ignore\n  if (typeof attachEvent === \"function\") {\n    // @ts-ignore\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\nconst hasXHR2 = function () {\n  const xhr = newRequest({\n    xdomain: false\n  });\n  return xhr && xhr.responseType !== null;\n}();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n  constructor(opts) {\n    super(opts);\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n  request(opts = {}) {\n    Object.assign(opts, {\n      xd: this.xd\n    }, this.opts);\n    return new Request(newRequest, this.uri(), opts);\n  }\n}\nfunction newRequest(opts) {\n  const xdomain = opts.xdomain;\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n    } catch (e) {}\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}