{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';\nimport { ErrorDisplayComponent } from '../../../../shared/components/error-display/error-display.component';\nimport { LoadingService } from '../../../../core/services/loading.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common/http\";\nimport * as i7 from \"../../../../core/services/error-handler.service\";\nimport * as i8 from \"../../../../core/services/loading.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nconst _c0 = () => [\"Nike\", \"Adidas\", \"Zara\", \"H&M\", \"Uniqlo\"];\nconst _c1 = () => [\"Dresses\", \"Jeans\", \"T-shirts\", \"Sneakers\", \"Accessories\"];\nconst _c2 = () => [\"Summer Collection\", \"Winter Wear\", \"Casual Outfits\", \"Formal Wear\"];\nfunction ShopComponent_app_loading_spinner_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-loading-spinner\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showGlobalLoading\", true);\n  }\n}\nfunction ShopComponent_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_10_Template_div_click_0_listener() {\n      const category_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToCategory(category_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r4.name);\n  }\n}\nfunction ShopComponent_div_3_div_15_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1, \"Popular\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_3_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"img\", 30);\n    i0.ɵɵelementStart(2, \"h3\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ShopComponent_div_3_div_15_div_1_span_4_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", brand_r5.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r5.isPopular);\n  }\n}\nfunction ShopComponent_div_3_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, ShopComponent_div_3_div_15_div_1_Template, 5, 4, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands);\n  }\n}\nfunction ShopComponent_div_3_app_no_data_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 34);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showActions\", true)(\"suggestions\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction ShopComponent_div_3_div_20_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r7), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.likesCount);\n  }\n}\nfunction ShopComponent_div_3_div_20_div_1_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.sharesCount);\n  }\n}\nfunction ShopComponent_div_3_div_20_div_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.commentsCount);\n  }\n}\nfunction ShopComponent_div_3_div_20_div_1_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_1_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵtemplate(3, ShopComponent_div_3_div_20_div_1_div_3_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_1_Template_button_click_5_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.likeProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(6, \"i\", 43);\n    i0.ɵɵtemplate(7, ShopComponent_div_3_div_20_div_1_span_7_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_1_Template_button_click_8_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 46);\n    i0.ɵɵtemplate(10, ShopComponent_div_3_div_20_div_1_span_10_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_1_Template_button_click_11_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.commentOnProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(12, \"i\", 48);\n    i0.ɵɵtemplate(13, ShopComponent_div_3_div_20_div_1_span_13_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 49)(15, \"h3\", 50);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 51);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 52)(20, \"span\", 53);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ShopComponent_div_3_div_20_div_1_span_22_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 55)(24, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_1_Template_button_click_24_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r7, $event));\n    });\n    i0.ɵɵelement(25, \"i\", 43);\n    i0.ɵɵtext(26, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_1_Template_button_click_27_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r7, $event));\n    });\n    i0.ɵɵelement(28, \"i\", 58);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r7), i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r7) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", product_r7.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r7.likesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r7.sharesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r7.commentsCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice && product_r7.originalPrice > product_r7.price);\n  }\n}\nfunction ShopComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, ShopComponent_div_3_div_20_div_1_Template, 30, 12, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts);\n  }\n}\nfunction ShopComponent_div_3_app_no_data_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 61);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showActions\", true)(\"suggestions\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\nfunction ShopComponent_div_3_div_25_div_1_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.likesCount);\n  }\n}\nfunction ShopComponent_div_3_div_25_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.sharesCount);\n  }\n}\nfunction ShopComponent_div_3_div_25_div_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.commentsCount);\n  }\n}\nfunction ShopComponent_div_3_div_25_div_1_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r9.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_3_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_25_div_1_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵelementStart(3, \"div\", 62);\n    i0.ɵɵtext(4, \"NEW\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41)(6, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_25_div_1_Template_button_click_6_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.likeProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(7, \"i\", 43);\n    i0.ɵɵtemplate(8, ShopComponent_div_3_div_25_div_1_span_8_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_25_div_1_Template_button_click_9_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(10, \"i\", 46);\n    i0.ɵɵtemplate(11, ShopComponent_div_3_div_25_div_1_span_11_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_25_div_1_Template_button_click_12_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.commentOnProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(13, \"i\", 48);\n    i0.ɵɵtemplate(14, ShopComponent_div_3_div_25_div_1_span_14_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 49)(16, \"h3\", 50);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 51);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 52)(21, \"span\", 53);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ShopComponent_div_3_div_25_div_1_span_23_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 55)(25, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_25_div_1_Template_button_click_25_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r9, $event));\n    });\n    i0.ɵɵelement(26, \"i\", 43);\n    i0.ɵɵtext(27, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_25_div_1_Template_button_click_28_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r9, $event));\n    });\n    i0.ɵɵelement(29, \"i\", 58);\n    i0.ɵɵtext(30, \" Add to Cart \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r9), i0.ɵɵsanitizeUrl)(\"alt\", product_r9.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"liked\", product_r9.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r9.likesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r9.sharesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r9.commentsCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r9.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r9.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r9.originalPrice && product_r9.originalPrice > product_r9.price);\n  }\n}\nfunction ShopComponent_div_3_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, ShopComponent_div_3_div_25_div_1_Template, 31, 11, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals);\n  }\n}\nfunction ShopComponent_div_3_app_no_data_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 63);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showActions\", true)(\"suggestions\", i0.ɵɵpureFunction0(2, _c2));\n  }\n}\nfunction ShopComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"input\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ShopComponent_div_3_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function ShopComponent_div_3_Template_input_keyup_enter_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelement(5, \"i\", 10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"section\", 11)(7, \"h2\", 12);\n    i0.ɵɵtext(8, \"Shop by Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13);\n    i0.ɵɵtemplate(10, ShopComponent_div_3_div_10_Template, 5, 2, \"div\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"section\", 15)(12, \"h2\", 12);\n    i0.ɵɵtext(13, \"Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"app-loading-spinner\", 16);\n    i0.ɵɵtemplate(15, ShopComponent_div_3_div_15_Template, 2, 1, \"div\", 17)(16, ShopComponent_div_3_app_no_data_16_Template, 1, 3, \"app-no-data\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"section\", 19)(18, \"h2\", 12);\n    i0.ɵɵtext(19, \"Trending Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, ShopComponent_div_3_div_20_Template, 2, 1, \"div\", 20)(21, ShopComponent_div_3_app_no_data_21_Template, 1, 3, \"app-no-data\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"section\", 22)(23, \"h2\", 12);\n    i0.ɵɵtext(24, \"New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, ShopComponent_div_3_div_25_Template, 2, 1, \"div\", 20)(26, ShopComponent_div_3_app_no_data_26_Template, 1, 3, \"app-no-data\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"showInlineLoading\", true)(\"loadingKey\", \"shop.brands.load\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.featuredBrands.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.featuredBrands.length === 0 && !ctx_r1.loading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendingProducts.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendingProducts.length === 0 && !ctx_r1.loading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.newArrivals.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.newArrivals.length === 0 && !ctx_r1.loading);\n  }\n}\nexport class ShopComponent {\n  constructor(productService, authService, cartService, wishlistService, router, route, http, errorHandlerService, loadingService) {\n    this.productService = productService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.route = route;\n    this.http = http;\n    this.errorHandlerService = errorHandlerService;\n    this.loadingService = loadingService;\n    this.featuredBrands = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.categories = [];\n    this.quickLinks = [];\n    this.searchQuery = '';\n    this.loading = true;\n    // Enhanced filtering and search\n    this.selectedCategory = '';\n    this.selectedSubcategory = '';\n    this.selectedBrand = '';\n    this.filterType = ''; // 'suggested', 'trending', etc.\n    this.sortBy = 'featured';\n    this.priceRange = {\n      min: 0,\n      max: 10000\n    };\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 24;\n    this.hasMore = false;\n    // All products for filtering\n    this.allProducts = [];\n    this.filteredProducts = [];\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Check for query parameters\n    this.subscriptions.push(this.route.queryParams.subscribe(params => {\n      this.searchQuery = params['q'] || '';\n      this.selectedCategory = params['category'] || '';\n      this.filterType = params['filter'] || '';\n      this.sortBy = params['sort'] || 'featured';\n      this.loadShopData();\n    }));\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadShopData() {\n    this.loading = true;\n    // If we have filters or search, load filtered products\n    if (this.searchQuery || this.selectedCategory || this.filterType) {\n      this.loadProductsWithFilters();\n    } else {\n      // Load all data in parallel for the main shop view\n      Promise.all([this.loadFeaturedBrands(), this.loadTrendingProducts(), this.loadNewArrivals(), this.loadCategories(), this.loadQuickLinks()]).finally(() => {\n        this.loading = false;\n      });\n    }\n  }\n  loadFeaturedBrands() {\n    this.loadingService.startLoading(LoadingService.KEYS.BRANDS_LOAD, 'Loading featured brands...');\n    return this.productService.getFeaturedBrands().toPromise().then(response => {\n      if (response?.success) {\n        this.featuredBrands = response.data || [];\n        console.log('✅ Featured brands loaded:', this.featuredBrands.length);\n      } else {\n        this.featuredBrands = [];\n        console.warn('⚠️ Featured brands API returned unsuccessful response');\n      }\n    }).catch(error => {\n      console.error('❌ Error loading featured brands:', error);\n      this.featuredBrands = [];\n      this.errorHandlerService.handleError(error, 'Loading featured brands');\n      console.warn('🔄 Featured brands section will show no-data state');\n    }).finally(() => {\n      this.loadingService.stopLoading(LoadingService.KEYS.BRANDS_LOAD);\n    });\n  }\n  loadTrendingProducts() {\n    return this.productService.getTrendingProducts(8).toPromise().then(response => {\n      if (response?.success) {\n        this.trendingProducts = response.data || [];\n        console.log('✅ Trending products loaded:', this.trendingProducts.length);\n      } else {\n        this.trendingProducts = [];\n        console.warn('⚠️ Trending products API returned unsuccessful response');\n      }\n    }).catch(error => {\n      console.error('❌ Error loading trending products:', error);\n      this.trendingProducts = [];\n      this.errorHandlerService.handleError(error, 'Loading trending products');\n      console.warn('🔄 Trending products section will show no-data state');\n    });\n  }\n  loadNewArrivals() {\n    return this.productService.getNewArrivals(8).toPromise().then(response => {\n      if (response?.success) {\n        this.newArrivals = response.data || [];\n        console.log('✅ New arrivals loaded:', this.newArrivals.length);\n      } else {\n        this.newArrivals = [];\n        console.warn('⚠️ New arrivals API returned unsuccessful response');\n      }\n    }).catch(error => {\n      console.error('❌ Error loading new arrivals:', error);\n      this.newArrivals = [];\n      this.errorHandlerService.handleError(error, 'Loading new arrivals');\n      console.warn('🔄 New arrivals section will show no-data state');\n    });\n  }\n  loadCategories() {\n    return this.productService.getCategories().toPromise().then(response => {\n      this.categories = response?.data || [];\n      console.log('✅ Categories loaded:', this.categories.length);\n    }).catch(error => {\n      console.error('❌ Error loading categories:', error);\n      this.categories = [];\n    });\n  }\n  loadQuickLinks() {\n    // Load quick navigation links from API\n    return this.http.get(`${environment.apiUrl}/shop/quick-links`).toPromise().then(response => {\n      this.quickLinks = response?.data || response?.links || [];\n      console.log('✅ Quick links loaded:', this.quickLinks.length);\n    }).catch(error => {\n      console.error('❌ Error loading quick links:', error);\n      this.errorHandlerService.handleError(error, 'Loading quick links');\n      // Set empty array - let no-data component handle the empty state\n      this.quickLinks = [];\n      console.warn('🔄 Quick links section will show no-data state');\n    });\n  }\n  // Product interaction methods\n  likeProduct(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    product.isLiked = !product.isLiked;\n    product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n    this.productService.toggleProductLike(product._id).subscribe({\n      next: response => {\n        console.log('Product like updated:', response);\n      },\n      error: error => {\n        console.error('Error updating product like:', error);\n        product.isLiked = !product.isLiked;\n        product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n      }\n    });\n  }\n  shareProduct(product, event) {\n    event.stopPropagation();\n    const shareData = {\n      title: product.name,\n      text: `Check out this amazing product: ${product.name}`,\n      url: `${window.location.origin}/product/${product._id}`\n    };\n    if (navigator.share) {\n      navigator.share(shareData);\n    } else {\n      navigator.clipboard.writeText(shareData.url).then(() => {\n        alert('Product link copied to clipboard!');\n      });\n    }\n    this.productService.shareProduct(product._id).subscribe({\n      next: response => {\n        product.sharesCount = (product.sharesCount || 0) + 1;\n      },\n      error: error => {\n        console.error('Error tracking product share:', error);\n      }\n    });\n  }\n  commentOnProduct(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.router.navigate(['/product', product._id], {\n      queryParams: {\n        action: 'comment'\n      }\n    });\n  }\n  addToWishlist(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: response => {\n        product.isInWishlist = true;\n        console.log('Product added to wishlist:', response);\n      },\n      error: error => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n  addToCart(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.cartService.addToCart(product._id, 1).subscribe({\n      next: response => {\n        console.log('Product added to cart:', response);\n        alert('Product added to cart successfully!');\n      },\n      error: error => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n  // Navigation methods\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  navigateToCategory(category) {\n    this.router.navigate(['/category', category.slug]);\n  }\n  search() {\n    if (this.searchQuery.trim()) {\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n  }\n  // Enhanced filtering methods\n  onCategoryChange() {\n    this.selectedSubcategory = '';\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  onFilterChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  onSortChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedCategory = '';\n    this.selectedSubcategory = '';\n    this.selectedBrand = '';\n    this.filterType = '';\n    this.sortBy = 'featured';\n    this.priceRange = {\n      min: 0,\n      max: 10000\n    };\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n  updateUrlParams() {\n    const queryParams = {};\n    if (this.searchQuery) queryParams.q = this.searchQuery;\n    if (this.selectedCategory) queryParams.category = this.selectedCategory;\n    if (this.filterType) queryParams.filter = this.filterType;\n    if (this.sortBy !== 'featured') queryParams.sort = this.sortBy;\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams,\n      queryParamsHandling: 'merge'\n    });\n  }\n  // Enhanced product loading with filtering\n  loadProductsWithFilters() {\n    this.loading = true;\n    let endpoint = `${environment.apiUrl}/products`;\n    // Use specific endpoints for filtered content\n    if (this.filterType === 'suggested') {\n      endpoint = `${environment.apiUrl}/products/suggested`;\n    } else if (this.filterType === 'trending') {\n      endpoint = `${environment.apiUrl}/products/trending`;\n    }\n    const params = new URLSearchParams({\n      page: this.currentPage.toString(),\n      limit: this.itemsPerPage.toString(),\n      sort: this.sortBy\n    });\n    if (this.searchQuery) params.append('q', this.searchQuery);\n    if (this.selectedCategory) params.append('category', this.selectedCategory);\n    if (this.selectedSubcategory) params.append('subcategory', this.selectedSubcategory);\n    if (this.selectedBrand) params.append('brand', this.selectedBrand);\n    this.subscriptions.push(this.http.get(`${endpoint}?${params.toString()}`).subscribe({\n      next: response => {\n        if (response.success) {\n          this.allProducts = response.products || [];\n          this.filteredProducts = this.allProducts;\n          this.hasMore = response.pagination?.page < response.pagination?.pages;\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading filtered products:', error);\n        this.loading = false;\n      }\n    }));\n  }\n  getProductImage(product) {\n    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n  }\n  getDiscountPercentage(product) {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n  }\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.HttpClient), i0.ɵɵdirectiveInject(i7.ErrorHandlerService), i0.ɵɵdirectiveInject(i8.LoadingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"shop-container\"], [3, \"showGlobalErrors\"], [\"message\", \"Loading shop data...\", \"webSpinnerType\", \"pulse\", 3, \"showGlobalLoading\", 4, \"ngIf\"], [\"class\", \"shop-content\", 4, \"ngIf\"], [\"message\", \"Loading shop data...\", \"webSpinnerType\", \"pulse\", 3, \"showGlobalLoading\"], [1, \"shop-content\"], [1, \"search-section\"], [1, \"search-bar\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, categories...\", 1, \"search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"category-section\"], [1, \"section-title\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-brands-section\"], [\"message\", \"Loading brands...\", \"size\", \"small\", \"webSpinnerType\", \"dots\", 3, \"showInlineLoading\", \"loadingKey\"], [\"class\", \"brands-slider\", 4, \"ngIf\"], [\"title\", \"No Featured Brands\", \"message\", \"We're working on adding featured brands. Check back soon!\", \"iconClass\", \"fas fa-store\", \"containerClass\", \"compact\", \"primaryAction\", \"Browse All Brands\", \"suggestionsTitle\", \"Popular Brands:\", 3, \"showActions\", \"suggestions\", 4, \"ngIf\"], [1, \"trending-section\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"title\", \"No Trending Products\", \"message\", \"Discover what's hot! We're updating our trending collection.\", \"iconClass\", \"fas fa-fire\", \"containerClass\", \"compact\", \"primaryAction\", \"Browse All Products\", \"secondaryAction\", \"View Categories\", \"suggestionsTitle\", \"Popular Categories:\", 3, \"showActions\", \"suggestions\", 4, \"ngIf\"], [1, \"new-arrivals-section\"], [\"title\", \"No New Arrivals\", \"message\", \"Stay tuned for the latest fashion arrivals!\", \"iconClass\", \"fas fa-sparkles\", \"containerClass\", \"compact\", \"primaryAction\", \"Browse All Products\", \"secondaryAction\", \"Set Alerts\", \"suggestionsTitle\", \"Coming Soon:\", 3, \"showActions\", \"suggestions\", 4, \"ngIf\"], [1, \"category-card\", 3, \"click\"], [1, \"category-icon\"], [1, \"category-name\"], [1, \"brands-slider\"], [\"class\", \"brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"brand-card\"], [1, \"brand-logo\", 3, \"src\", \"alt\"], [1, \"brand-name\"], [\"class\", \"popular-badge\", 4, \"ngIf\"], [1, \"popular-badge\"], [\"title\", \"No Featured Brands\", \"message\", \"We're working on adding featured brands. Check back soon!\", \"iconClass\", \"fas fa-store\", \"containerClass\", \"compact\", \"primaryAction\", \"Browse All Brands\", \"suggestionsTitle\", \"Popular Brands:\", 3, \"showActions\", \"suggestions\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [1, \"product-image\", 3, \"src\", \"alt\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [4, \"ngIf\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-buttons\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"discount-badge\"], [1, \"original-price\"], [\"title\", \"No Trending Products\", \"message\", \"Discover what's hot! We're updating our trending collection.\", \"iconClass\", \"fas fa-fire\", \"containerClass\", \"compact\", \"primaryAction\", \"Browse All Products\", \"secondaryAction\", \"View Categories\", \"suggestionsTitle\", \"Popular Categories:\", 3, \"showActions\", \"suggestions\"], [1, \"new-badge\"], [\"title\", \"No New Arrivals\", \"message\", \"Stay tuned for the latest fashion arrivals!\", \"iconClass\", \"fas fa-sparkles\", \"containerClass\", \"compact\", \"primaryAction\", \"Browse All Products\", \"secondaryAction\", \"Set Alerts\", \"suggestionsTitle\", \"Coming Soon:\", 3, \"showActions\", \"suggestions\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-error-display\", 1);\n          i0.ɵɵtemplate(2, ShopComponent_app_loading_spinner_2_Template, 1, 1, \"app-loading-spinner\", 2)(3, ShopComponent_div_3_Template, 27, 10, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"showGlobalErrors\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i9.NgForOf, i9.NgIf, FormsModule, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgModel, NoDataComponent, LoadingSpinnerComponent, ErrorDisplayComponent],\n      styles: [\".shop-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 400px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #007bff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 20px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  border: 2px solid #e0e0e0;\\n  border-radius: 25px;\\n  overflow: hidden;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 20px;\\n  border: none;\\n  outline: none;\\n  font-size: 16px;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  cursor: pointer;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin-bottom: 30px;\\n  text-align: center;\\n  color: #333;\\n}\\n\\n.category-section[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 20px;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 30px 20px;\\n  text-align: center;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 15px;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n.featured-brands-section[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding: 20px 0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  background: white;\\n  border-radius: 15px;\\n  padding: 20px;\\n  text-align: center;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 15px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .popular-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  background: #ff6b6b;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 600;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 30px;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 250px;\\n  object-fit: cover;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%], .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  left: 10px;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: white;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #666;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  transition: all 0.3s ease;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  color: #333;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  margin-left: 2px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0 0 12px 0;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #999;\\n  text-decoration: line-through;\\n  margin-left: 8px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-wishlist[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-wishlist[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #333;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-cart[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-cart[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n@media (max-width: 768px) {\\n  .shop-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n    gap: 15px;\\n  }\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 20px;\\n  }\\n  .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n    min-width: 150px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "NoDataComponent", "LoadingSpinnerComponent", "ErrorDisplayComponent", "LoadingService", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵelementStart", "ɵɵlistener", "ShopComponent_div_3_div_10_Template_div_click_0_listener", "category_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "navigateToCategory", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "name", "ɵɵtemplate", "ShopComponent_div_3_div_15_div_1_span_4_Template", "brand_r5", "logo", "ɵɵsanitizeUrl", "isPopular", "ShopComponent_div_3_div_15_div_1_Template", "featuredB<PERSON>s", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r7", "likesCount", "sharesCount", "commentsCount", "originalPrice", "ShopComponent_div_3_div_20_div_1_Template_div_click_0_listener", "_r6", "viewProduct", "ShopComponent_div_3_div_20_div_1_div_3_Template", "ShopComponent_div_3_div_20_div_1_Template_button_click_5_listener", "$event", "likeProduct", "ShopComponent_div_3_div_20_div_1_span_7_Template", "ShopComponent_div_3_div_20_div_1_Template_button_click_8_listener", "shareProduct", "ShopComponent_div_3_div_20_div_1_span_10_Template", "ShopComponent_div_3_div_20_div_1_Template_button_click_11_listener", "commentOnProduct", "ShopComponent_div_3_div_20_div_1_span_13_Template", "ShopComponent_div_3_div_20_div_1_span_22_Template", "ShopComponent_div_3_div_20_div_1_Template_button_click_24_listener", "addToWishlist", "ShopComponent_div_3_div_20_div_1_Template_button_click_27_listener", "addToCart", "getProductImage", "ɵɵclassProp", "isLiked", "brand", "price", "ShopComponent_div_3_div_20_div_1_Template", "trendingProducts", "_c1", "product_r9", "ShopComponent_div_3_div_25_div_1_Template_div_click_0_listener", "_r8", "ShopComponent_div_3_div_25_div_1_Template_button_click_6_listener", "ShopComponent_div_3_div_25_div_1_span_8_Template", "ShopComponent_div_3_div_25_div_1_Template_button_click_9_listener", "ShopComponent_div_3_div_25_div_1_span_11_Template", "ShopComponent_div_3_div_25_div_1_Template_button_click_12_listener", "ShopComponent_div_3_div_25_div_1_span_14_Template", "ShopComponent_div_3_div_25_div_1_span_23_Template", "ShopComponent_div_3_div_25_div_1_Template_button_click_25_listener", "ShopComponent_div_3_div_25_div_1_Template_button_click_28_listener", "ShopComponent_div_3_div_25_div_1_Template", "newArrivals", "_c2", "ɵɵtwoWayListener", "ShopComponent_div_3_Template_input_ngModelChange_3_listener", "_r1", "ɵɵtwoWayBindingSet", "searchQuery", "ShopComponent_div_3_Template_input_keyup_enter_3_listener", "search", "ShopComponent_div_3_Template_button_click_4_listener", "ShopComponent_div_3_div_10_Template", "ShopComponent_div_3_div_15_Template", "ShopComponent_div_3_app_no_data_16_Template", "ShopComponent_div_3_div_20_Template", "ShopComponent_div_3_app_no_data_21_Template", "ShopComponent_div_3_div_25_Template", "ShopComponent_div_3_app_no_data_26_Template", "ɵɵtwoWayProperty", "categories", "length", "loading", "ShopComponent", "constructor", "productService", "authService", "cartService", "wishlistService", "router", "route", "http", "errorHandlerService", "loadingService", "quickLinks", "selectedCate<PERSON><PERSON>", "selectedSubcategory", "<PERSON><PERSON><PERSON>", "filterType", "sortBy", "priceRange", "min", "max", "currentPage", "itemsPerPage", "hasMore", "allProducts", "filteredProducts", "subscriptions", "ngOnInit", "push", "queryParams", "subscribe", "params", "loadShopData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "loadProductsWithFilters", "Promise", "all", "loadFeaturedBrands", "loadTrendingProducts", "loadNewArrivals", "loadCategories", "loadQuickLinks", "finally", "startLoading", "KEYS", "BRANDS_LOAD", "getFeaturedBrands", "to<PERSON>romise", "then", "response", "success", "data", "console", "log", "warn", "catch", "error", "handleError", "stopLoading", "getTrendingProducts", "getNewArrivals", "getCategories", "get", "apiUrl", "links", "product", "event", "stopPropagation", "isAuthenticated", "navigate", "toggleProductLike", "_id", "next", "shareData", "title", "text", "url", "window", "location", "origin", "navigator", "share", "clipboard", "writeText", "alert", "action", "isInWishlist", "category", "slug", "trim", "updateUrlParams", "onCategoryChange", "onFilterChange", "onSortChange", "clearFilters", "q", "filter", "sort", "relativeTo", "queryParamsHandling", "endpoint", "URLSearchParams", "page", "toString", "limit", "append", "products", "pagination", "pages", "images", "Math", "round", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "AuthService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "ActivatedRoute", "i6", "HttpClient", "i7", "ErrorHandlerService", "i8", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "ShopComponent_app_loading_spinner_2_Template", "ShopComponent_div_3_Template", "i9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i10", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\shop\\shop.component.ts", "E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\shop\\shop.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';\nimport { ErrorDisplayComponent } from '../../../../shared/components/error-display/error-display.component';\nimport { ErrorHandlerService } from '../../../../core/services/error-handler.service';\nimport { LoadingService } from '../../../../core/services/loading.service';\n\n@Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NoDataComponent, LoadingSpinnerComponent, ErrorDisplayComponent],\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.scss']\n})\nexport class ShopComponent implements OnInit, OnDestroy {\n  featuredBrands: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  categories: any[] = [];\n  quickLinks: any[] = [];\n  searchQuery: string = '';\n  loading = true;\n\n  // Enhanced filtering and search\n  selectedCategory = '';\n  selectedSubcategory = '';\n  selectedBrand = '';\n  filterType = ''; // 'suggested', 'trending', etc.\n  sortBy = 'featured';\n  priceRange = { min: 0, max: 10000 };\n\n  // Pagination\n  currentPage = 1;\n  itemsPerPage = 24;\n  hasMore = false;\n\n  // All products for filtering\n  allProducts: any[] = [];\n  filteredProducts: any[] = [];\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private productService: ProductService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private http: HttpClient,\n    private errorHandlerService: ErrorHandlerService,\n    private loadingService: LoadingService\n  ) {}\n\n  ngOnInit() {\n    // Check for query parameters\n    this.subscriptions.push(\n      this.route.queryParams.subscribe(params => {\n        this.searchQuery = params['q'] || '';\n        this.selectedCategory = params['category'] || '';\n        this.filterType = params['filter'] || '';\n        this.sortBy = params['sort'] || 'featured';\n\n        this.loadShopData();\n      })\n    );\n\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  loadShopData() {\n    this.loading = true;\n\n    // If we have filters or search, load filtered products\n    if (this.searchQuery || this.selectedCategory || this.filterType) {\n      this.loadProductsWithFilters();\n    } else {\n      // Load all data in parallel for the main shop view\n      Promise.all([\n        this.loadFeaturedBrands(),\n        this.loadTrendingProducts(),\n        this.loadNewArrivals(),\n        this.loadCategories(),\n        this.loadQuickLinks()\n      ]).finally(() => {\n        this.loading = false;\n      });\n    }\n  }\n\n  loadFeaturedBrands() {\n    this.loadingService.startLoading(LoadingService.KEYS.BRANDS_LOAD, 'Loading featured brands...');\n\n    return this.productService.getFeaturedBrands().toPromise().then(\n      (response) => {\n        if (response?.success) {\n          this.featuredBrands = response.data || [];\n          console.log('✅ Featured brands loaded:', this.featuredBrands.length);\n        } else {\n          this.featuredBrands = [];\n          console.warn('⚠️ Featured brands API returned unsuccessful response');\n        }\n      }\n    ).catch(error => {\n      console.error('❌ Error loading featured brands:', error);\n      this.featuredBrands = [];\n      this.errorHandlerService.handleError(error, 'Loading featured brands');\n      console.warn('🔄 Featured brands section will show no-data state');\n    }).finally(() => {\n      this.loadingService.stopLoading(LoadingService.KEYS.BRANDS_LOAD);\n    });\n  }\n\n  loadTrendingProducts() {\n    return this.productService.getTrendingProducts(8).toPromise().then(\n      (response) => {\n        if (response?.success) {\n          this.trendingProducts = response.data || [];\n          console.log('✅ Trending products loaded:', this.trendingProducts.length);\n        } else {\n          this.trendingProducts = [];\n          console.warn('⚠️ Trending products API returned unsuccessful response');\n        }\n      }\n    ).catch(error => {\n      console.error('❌ Error loading trending products:', error);\n      this.trendingProducts = [];\n      this.errorHandlerService.handleError(error, 'Loading trending products');\n      console.warn('🔄 Trending products section will show no-data state');\n    });\n  }\n\n  loadNewArrivals() {\n    return this.productService.getNewArrivals(8).toPromise().then(\n      (response) => {\n        if (response?.success) {\n          this.newArrivals = response.data || [];\n          console.log('✅ New arrivals loaded:', this.newArrivals.length);\n        } else {\n          this.newArrivals = [];\n          console.warn('⚠️ New arrivals API returned unsuccessful response');\n        }\n      }\n    ).catch(error => {\n      console.error('❌ Error loading new arrivals:', error);\n      this.newArrivals = [];\n      this.errorHandlerService.handleError(error, 'Loading new arrivals');\n      console.warn('🔄 New arrivals section will show no-data state');\n    });\n  }\n\n  loadCategories() {\n    return this.productService.getCategories().toPromise().then(\n      (response) => {\n        this.categories = response?.data || [];\n        console.log('✅ Categories loaded:', this.categories.length);\n      }\n    ).catch(error => {\n      console.error('❌ Error loading categories:', error);\n      this.categories = [];\n    });\n  }\n\n  loadQuickLinks() {\n    // Load quick navigation links from API\n    return this.http.get<any>(`${environment.apiUrl}/shop/quick-links`).toPromise().then(\n      (response) => {\n        this.quickLinks = response?.data || response?.links || [];\n        console.log('✅ Quick links loaded:', this.quickLinks.length);\n      }\n    ).catch(error => {\n      console.error('❌ Error loading quick links:', error);\n      this.errorHandlerService.handleError(error, 'Loading quick links');\n\n      // Set empty array - let no-data component handle the empty state\n      this.quickLinks = [];\n      console.warn('🔄 Quick links section will show no-data state');\n    });\n  }\n\n  // Product interaction methods\n  likeProduct(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    product.isLiked = !product.isLiked;\n    product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n\n    this.productService.toggleProductLike(product._id).subscribe({\n      next: (response) => {\n        console.log('Product like updated:', response);\n      },\n      error: (error) => {\n        console.error('Error updating product like:', error);\n        product.isLiked = !product.isLiked;\n        product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n      }\n    });\n  }\n\n  shareProduct(product: any, event: Event) {\n    event.stopPropagation();\n    const shareData = {\n      title: product.name,\n      text: `Check out this amazing product: ${product.name}`,\n      url: `${window.location.origin}/product/${product._id}`\n    };\n\n    if (navigator.share) {\n      navigator.share(shareData);\n    } else {\n      navigator.clipboard.writeText(shareData.url).then(() => {\n        alert('Product link copied to clipboard!');\n      });\n    }\n\n    this.productService.shareProduct(product._id).subscribe({\n      next: (response) => {\n        product.sharesCount = (product.sharesCount || 0) + 1;\n      },\n      error: (error) => {\n        console.error('Error tracking product share:', error);\n      }\n    });\n  }\n\n  commentOnProduct(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    this.router.navigate(['/product', product._id], {\n      queryParams: { action: 'comment' }\n    });\n  }\n\n  addToWishlist(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: (response) => {\n        product.isInWishlist = true;\n        console.log('Product added to wishlist:', response);\n      },\n      error: (error) => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n\n  addToCart(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    this.cartService.addToCart(product._id, 1).subscribe({\n      next: (response) => {\n        console.log('Product added to cart:', response);\n        alert('Product added to cart successfully!');\n      },\n      error: (error) => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n\n  // Navigation methods\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  navigateToCategory(category: any) {\n    this.router.navigate(['/category', category.slug]);\n  }\n\n  search() {\n    if (this.searchQuery.trim()) {\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n  }\n\n  // Enhanced filtering methods\n  onCategoryChange() {\n    this.selectedSubcategory = '';\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  onFilterChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  onSortChange() {\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  clearFilters() {\n    this.searchQuery = '';\n    this.selectedCategory = '';\n    this.selectedSubcategory = '';\n    this.selectedBrand = '';\n    this.filterType = '';\n    this.sortBy = 'featured';\n    this.priceRange = { min: 0, max: 10000 };\n    this.updateUrlParams();\n    this.loadShopData();\n  }\n\n  private updateUrlParams() {\n    const queryParams: any = {};\n\n    if (this.searchQuery) queryParams.q = this.searchQuery;\n    if (this.selectedCategory) queryParams.category = this.selectedCategory;\n    if (this.filterType) queryParams.filter = this.filterType;\n    if (this.sortBy !== 'featured') queryParams.sort = this.sortBy;\n\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams,\n      queryParamsHandling: 'merge'\n    });\n  }\n\n  // Enhanced product loading with filtering\n  loadProductsWithFilters() {\n    this.loading = true;\n\n    let endpoint = `${environment.apiUrl}/products`;\n\n    // Use specific endpoints for filtered content\n    if (this.filterType === 'suggested') {\n      endpoint = `${environment.apiUrl}/products/suggested`;\n    } else if (this.filterType === 'trending') {\n      endpoint = `${environment.apiUrl}/products/trending`;\n    }\n\n    const params = new URLSearchParams({\n      page: this.currentPage.toString(),\n      limit: this.itemsPerPage.toString(),\n      sort: this.sortBy\n    });\n\n    if (this.searchQuery) params.append('q', this.searchQuery);\n    if (this.selectedCategory) params.append('category', this.selectedCategory);\n    if (this.selectedSubcategory) params.append('subcategory', this.selectedSubcategory);\n    if (this.selectedBrand) params.append('brand', this.selectedBrand);\n\n    this.subscriptions.push(\n      this.http.get<any>(`${endpoint}?${params.toString()}`).subscribe({\n        next: (response) => {\n          if (response.success) {\n            this.allProducts = response.products || [];\n            this.filteredProducts = this.allProducts;\n            this.hasMore = response.pagination?.page < response.pagination?.pages;\n          }\n          this.loading = false;\n        },\n        error: (error) => {\n          console.error('Error loading filtered products:', error);\n          this.loading = false;\n        }\n      })\n    );\n  }\n\n  getProductImage(product: any): string {\n    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n  }\n\n  getDiscountPercentage(product: any): number {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n  }\n}\n", "<div class=\"shop-container\">\n  <!-- Global Error Display -->\n  <app-error-display [showGlobalErrors]=\"true\"></app-error-display>\n\n  <!-- Loading State -->\n  <app-loading-spinner\n    *ngIf=\"loading\"\n    [showGlobalLoading]=\"true\"\n    message=\"Loading shop data...\"\n    webSpinnerType=\"pulse\">\n  </app-loading-spinner>\n\n  <!-- Shop Content -->\n  <div *ngIf=\"!loading\" class=\"shop-content\">\n    <!-- Global Search Bar -->\n    <div class=\"search-section\">\n      <div class=\"search-bar\">\n        <input \n          type=\"text\" \n          [(ngModel)]=\"searchQuery\" \n          placeholder=\"Search products, brands, categories...\"\n          (keyup.enter)=\"search()\"\n          class=\"search-input\">\n        <button (click)=\"search()\" class=\"search-btn\">\n          <i class=\"fas fa-search\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Shop by Category Section -->\n    <section class=\"category-section\">\n      <h2 class=\"section-title\">Shop by Category</h2>\n      <div class=\"categories-grid\">\n        <div \n          *ngFor=\"let category of categories\" \n          class=\"category-card\"\n          (click)=\"navigateToCategory(category)\">\n          <div class=\"category-icon\">{{ category.icon }}</div>\n          <h3 class=\"category-name\">{{ category.name }}</h3>\n        </div>\n      </div>\n    </section>\n\n    <!-- Featured Brands Section -->\n    <section class=\"featured-brands-section\">\n      <h2 class=\"section-title\">Featured Brands</h2>\n\n      <!-- Loading State for Brands -->\n      <app-loading-spinner\n        [showInlineLoading]=\"true\"\n        [loadingKey]=\"'shop.brands.load'\"\n        message=\"Loading brands...\"\n        size=\"small\"\n        webSpinnerType=\"dots\">\n      </app-loading-spinner>\n\n      <!-- Brands Slider -->\n      <div *ngIf=\"featuredBrands.length > 0\" class=\"brands-slider\">\n        <div *ngFor=\"let brand of featuredBrands\" class=\"brand-card\">\n          <img [src]=\"brand.logo\" [alt]=\"brand.name\" class=\"brand-logo\">\n          <h3 class=\"brand-name\">{{ brand.name }}</h3>\n          <span *ngIf=\"brand.isPopular\" class=\"popular-badge\">Popular</span>\n        </div>\n      </div>\n\n      <!-- No Data State -->\n      <app-no-data\n        *ngIf=\"featuredBrands.length === 0 && !loading\"\n        title=\"No Featured Brands\"\n        message=\"We're working on adding featured brands. Check back soon!\"\n        iconClass=\"fas fa-store\"\n        containerClass=\"compact\"\n        [showActions]=\"true\"\n        primaryAction=\"Browse All Brands\"\n        [suggestions]=\"['Nike', 'Adidas', 'Zara', 'H&M', 'Uniqlo']\"\n        suggestionsTitle=\"Popular Brands:\">\n      </app-no-data>\n    </section>\n\n    <!-- Trending Now Section -->\n    <section class=\"trending-section\">\n      <h2 class=\"section-title\">Trending Now</h2>\n\n      <!-- Products Grid -->\n      <div *ngIf=\"trendingProducts.length > 0\" class=\"products-grid\">\n        <div *ngFor=\"let product of trendingProducts\" class=\"product-card\" (click)=\"viewProduct(product)\">\n          <div class=\"product-image-container\">\n            <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" class=\"product-image\">\n            <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n              {{ getDiscountPercentage(product) }}% OFF\n            </div>\n            <div class=\"product-actions\">\n              <button (click)=\"likeProduct(product, $event)\" class=\"action-btn like-btn\" \n                      [class.liked]=\"product.isLiked\">\n                <i class=\"fas fa-heart\"></i>\n                <span *ngIf=\"product.likesCount\">{{ product.likesCount }}</span>\n              </button>\n              <button (click)=\"shareProduct(product, $event)\" class=\"action-btn share-btn\">\n                <i class=\"fas fa-share\"></i>\n                <span *ngIf=\"product.sharesCount\">{{ product.sharesCount }}</span>\n              </button>\n              <button (click)=\"commentOnProduct(product, $event)\" class=\"action-btn comment-btn\">\n                <i class=\"fas fa-comment\"></i>\n                <span *ngIf=\"product.commentsCount\">{{ product.commentsCount }}</span>\n              </button>\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h3 class=\"product-name\">{{ product.name }}</h3>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-pricing\">\n              <span class=\"current-price\">₹{{ product.price }}</span>\n              <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                    class=\"original-price\">₹{{ product.originalPrice }}</span>\n            </div>\n            <div class=\"product-buttons\">\n              <button (click)=\"addToWishlist(product, $event)\" class=\"btn-wishlist\">\n                <i class=\"fas fa-heart\"></i> Wishlist\n              </button>\n              <button (click)=\"addToCart(product, $event)\" class=\"btn-cart\">\n                <i class=\"fas fa-shopping-cart\"></i> Add to Cart\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- No Data State -->\n      <app-no-data\n        *ngIf=\"trendingProducts.length === 0 && !loading\"\n        title=\"No Trending Products\"\n        message=\"Discover what's hot! We're updating our trending collection.\"\n        iconClass=\"fas fa-fire\"\n        containerClass=\"compact\"\n        [showActions]=\"true\"\n        primaryAction=\"Browse All Products\"\n        secondaryAction=\"View Categories\"\n        [suggestions]=\"['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Accessories']\"\n        suggestionsTitle=\"Popular Categories:\">\n      </app-no-data>\n    </section>\n\n    <!-- New Arrivals Section -->\n    <section class=\"new-arrivals-section\">\n      <h2 class=\"section-title\">New Arrivals</h2>\n\n      <!-- Products Grid -->\n      <div *ngIf=\"newArrivals.length > 0\" class=\"products-grid\">\n        <div *ngFor=\"let product of newArrivals\" class=\"product-card\" (click)=\"viewProduct(product)\">\n          <div class=\"product-image-container\">\n            <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" class=\"product-image\">\n            <div class=\"new-badge\">NEW</div>\n            <div class=\"product-actions\">\n              <button (click)=\"likeProduct(product, $event)\" class=\"action-btn like-btn\" \n                      [class.liked]=\"product.isLiked\">\n                <i class=\"fas fa-heart\"></i>\n                <span *ngIf=\"product.likesCount\">{{ product.likesCount }}</span>\n              </button>\n              <button (click)=\"shareProduct(product, $event)\" class=\"action-btn share-btn\">\n                <i class=\"fas fa-share\"></i>\n                <span *ngIf=\"product.sharesCount\">{{ product.sharesCount }}</span>\n              </button>\n              <button (click)=\"commentOnProduct(product, $event)\" class=\"action-btn comment-btn\">\n                <i class=\"fas fa-comment\"></i>\n                <span *ngIf=\"product.commentsCount\">{{ product.commentsCount }}</span>\n              </button>\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h3 class=\"product-name\">{{ product.name }}</h3>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-pricing\">\n              <span class=\"current-price\">₹{{ product.price }}</span>\n              <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                    class=\"original-price\">₹{{ product.originalPrice }}</span>\n            </div>\n            <div class=\"product-buttons\">\n              <button (click)=\"addToWishlist(product, $event)\" class=\"btn-wishlist\">\n                <i class=\"fas fa-heart\"></i> Wishlist\n              </button>\n              <button (click)=\"addToCart(product, $event)\" class=\"btn-cart\">\n                <i class=\"fas fa-shopping-cart\"></i> Add to Cart\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- No Data State -->\n      <app-no-data\n        *ngIf=\"newArrivals.length === 0 && !loading\"\n        title=\"No New Arrivals\"\n        message=\"Stay tuned for the latest fashion arrivals!\"\n        iconClass=\"fas fa-sparkles\"\n        containerClass=\"compact\"\n        [showActions]=\"true\"\n        primaryAction=\"Browse All Products\"\n        secondaryAction=\"Set Alerts\"\n        [suggestions]=\"['Summer Collection', 'Winter Wear', 'Casual Outfits', 'Formal Wear']\"\n        suggestionsTitle=\"Coming Soon:\">\n      </app-no-data>\n    </section>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,WAAW,QAAQ,8BAA8B;AAK1D,SAASC,eAAe,QAAQ,yDAAyD;AACzF,SAASC,uBAAuB,QAAQ,yEAAyE;AACjH,SAASC,qBAAqB,QAAQ,qEAAqE;AAE3G,SAASC,cAAc,QAAQ,2CAA2C;;;;;;;;;;;;;;;;;ICVxEC,EAAA,CAAAC,SAAA,6BAKsB;;;IAHpBD,EAAA,CAAAE,UAAA,2BAA0B;;;;;;IA0BtBF,EAAA,CAAAG,cAAA,cAGyC;IAAvCH,EAAA,CAAAI,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,kBAAA,CAAAP,WAAA,CAA4B;IAAA,EAAC;IACtCN,EAAA,CAAAG,cAAA,cAA2B;IAAAH,EAAA,CAAAc,MAAA,GAAmB;IAAAd,EAAA,CAAAe,YAAA,EAAM;IACpDf,EAAA,CAAAG,cAAA,aAA0B;IAAAH,EAAA,CAAAc,MAAA,GAAmB;IAC/Cd,EAD+C,CAAAe,YAAA,EAAK,EAC9C;;;;IAFuBf,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAiB,iBAAA,CAAAX,WAAA,CAAAY,IAAA,CAAmB;IACpBlB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAiB,iBAAA,CAAAX,WAAA,CAAAa,IAAA,CAAmB;;;;;IAuB7CnB,EAAA,CAAAG,cAAA,eAAoD;IAAAH,EAAA,CAAAc,MAAA,cAAO;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;;IAHpEf,EAAA,CAAAG,cAAA,cAA6D;IAC3DH,EAAA,CAAAC,SAAA,cAA8D;IAC9DD,EAAA,CAAAG,cAAA,aAAuB;IAAAH,EAAA,CAAAc,MAAA,GAAgB;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAC5Cf,EAAA,CAAAoB,UAAA,IAAAC,gDAAA,mBAAoD;IACtDrB,EAAA,CAAAe,YAAA,EAAM;;;;IAHCf,EAAA,CAAAgB,SAAA,EAAkB;IAAChB,EAAnB,CAAAE,UAAA,QAAAoB,QAAA,CAAAC,IAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAkB,QAAAF,QAAA,CAAAH,IAAA,CAAmB;IACnBnB,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAiB,iBAAA,CAAAK,QAAA,CAAAH,IAAA,CAAgB;IAChCnB,EAAA,CAAAgB,SAAA,EAAqB;IAArBhB,EAAA,CAAAE,UAAA,SAAAoB,QAAA,CAAAG,SAAA,CAAqB;;;;;IAJhCzB,EAAA,CAAAG,cAAA,cAA6D;IAC3DH,EAAA,CAAAoB,UAAA,IAAAM,yCAAA,kBAA6D;IAK/D1B,EAAA,CAAAe,YAAA,EAAM;;;;IALmBf,EAAA,CAAAgB,SAAA,EAAiB;IAAjBhB,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAiB,cAAA,CAAiB;;;;;IAQ1C3B,EAAA,CAAAC,SAAA,sBAUc;;;IAFZD,EAFA,CAAAE,UAAA,qBAAoB,gBAAAF,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAEuC;;;;;IAcvD7B,EAAA,CAAAG,cAAA,cAAuE;IACrEH,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAe,YAAA,EAAM;;;;;IADJf,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAA8B,kBAAA,MAAApB,MAAA,CAAAqB,qBAAA,CAAAC,UAAA,YACF;;;;;IAKIhC,EAAA,CAAAG,cAAA,WAAiC;IAAAH,EAAA,CAAAc,MAAA,GAAwB;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAA/Bf,EAAA,CAAAgB,SAAA,EAAwB;IAAxBhB,EAAA,CAAAiB,iBAAA,CAAAe,UAAA,CAAAC,UAAA,CAAwB;;;;;IAIzDjC,EAAA,CAAAG,cAAA,WAAkC;IAAAH,EAAA,CAAAc,MAAA,GAAyB;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAAhCf,EAAA,CAAAgB,SAAA,EAAyB;IAAzBhB,EAAA,CAAAiB,iBAAA,CAAAe,UAAA,CAAAE,WAAA,CAAyB;;;;;IAI3DlC,EAAA,CAAAG,cAAA,WAAoC;IAAAH,EAAA,CAAAc,MAAA,GAA2B;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAAlCf,EAAA,CAAAgB,SAAA,EAA2B;IAA3BhB,EAAA,CAAAiB,iBAAA,CAAAe,UAAA,CAAAG,aAAA,CAA2B;;;;;IASjEnC,EAAA,CAAAG,cAAA,eAC6B;IAAAH,EAAA,CAAAc,MAAA,GAA4B;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAAnCf,EAAA,CAAAgB,SAAA,EAA4B;IAA5BhB,EAAA,CAAA8B,kBAAA,WAAAE,UAAA,CAAAI,aAAA,KAA4B;;;;;;IA5B/DpC,EAAA,CAAAG,cAAA,cAAkG;IAA/BH,EAAA,CAAAI,UAAA,mBAAAiC,+DAAA;MAAA,MAAAL,UAAA,GAAAhC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA6B,WAAA,CAAAP,UAAA,CAAoB;IAAA,EAAC;IAC/FhC,EAAA,CAAAG,cAAA,cAAqC;IACnCH,EAAA,CAAAC,SAAA,cAAiF;IACjFD,EAAA,CAAAoB,UAAA,IAAAoB,+CAAA,kBAAuE;IAIrExC,EADF,CAAAG,cAAA,cAA6B,iBAEa;IADhCH,EAAA,CAAAI,UAAA,mBAAAqC,kEAAAC,MAAA;MAAA,MAAAV,UAAA,GAAAhC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAiC,WAAA,CAAAX,UAAA,EAAAU,MAAA,CAA4B;IAAA,EAAC;IAE5C1C,EAAA,CAAAC,SAAA,YAA4B;IAC5BD,EAAA,CAAAoB,UAAA,IAAAwB,gDAAA,mBAAiC;IACnC5C,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAG,cAAA,iBAA6E;IAArEH,EAAA,CAAAI,UAAA,mBAAAyC,kEAAAH,MAAA;MAAA,MAAAV,UAAA,GAAAhC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAoC,YAAA,CAAAd,UAAA,EAAAU,MAAA,CAA6B;IAAA,EAAC;IAC7C1C,EAAA,CAAAC,SAAA,YAA4B;IAC5BD,EAAA,CAAAoB,UAAA,KAAA2B,iDAAA,mBAAkC;IACpC/C,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAG,cAAA,kBAAmF;IAA3EH,EAAA,CAAAI,UAAA,mBAAA4C,mEAAAN,MAAA;MAAA,MAAAV,UAAA,GAAAhC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuC,gBAAA,CAAAjB,UAAA,EAAAU,MAAA,CAAiC;IAAA,EAAC;IACjD1C,EAAA,CAAAC,SAAA,aAA8B;IAC9BD,EAAA,CAAAoB,UAAA,KAAA8B,iDAAA,mBAAoC;IAG1ClD,EAFI,CAAAe,YAAA,EAAS,EACL,EACF;IAEJf,EADF,CAAAG,cAAA,eAA0B,cACC;IAAAH,EAAA,CAAAc,MAAA,IAAkB;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAChDf,EAAA,CAAAG,cAAA,aAAyB;IAAAH,EAAA,CAAAc,MAAA,IAAmB;IAAAd,EAAA,CAAAe,YAAA,EAAI;IAE9Cf,EADF,CAAAG,cAAA,eAA6B,gBACC;IAAAH,EAAA,CAAAc,MAAA,IAAoB;IAAAd,EAAA,CAAAe,YAAA,EAAO;IACvDf,EAAA,CAAAoB,UAAA,KAAA+B,iDAAA,mBAC6B;IAC/BnD,EAAA,CAAAe,YAAA,EAAM;IAEJf,EADF,CAAAG,cAAA,eAA6B,kBAC2C;IAA9DH,EAAA,CAAAI,UAAA,mBAAAgD,mEAAAV,MAAA;MAAA,MAAAV,UAAA,GAAAhC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2C,aAAA,CAAArB,UAAA,EAAAU,MAAA,CAA8B;IAAA,EAAC;IAC9C1C,EAAA,CAAAC,SAAA,aAA4B;IAACD,EAAA,CAAAc,MAAA,kBAC/B;IAAAd,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAG,cAAA,kBAA8D;IAAtDH,EAAA,CAAAI,UAAA,mBAAAkD,mEAAAZ,MAAA;MAAA,MAAAV,UAAA,GAAAhC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA6C,SAAA,CAAAvB,UAAA,EAAAU,MAAA,CAA0B;IAAA,EAAC;IAC1C1C,EAAA,CAAAC,SAAA,aAAoC;IAACD,EAAA,CAAAc,MAAA,qBACvC;IAGNd,EAHM,CAAAe,YAAA,EAAS,EACL,EACF,EACF;;;;;IArCGf,EAAA,CAAAgB,SAAA,GAAgC;IAAChB,EAAjC,CAAAE,UAAA,QAAAQ,MAAA,CAAA8C,eAAA,CAAAxB,UAAA,GAAAhC,EAAA,CAAAwB,aAAA,CAAgC,QAAAQ,UAAA,CAAAb,IAAA,CAAqB;IACpDnB,EAAA,CAAAgB,SAAA,EAAwC;IAAxChB,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAqB,qBAAA,CAAAC,UAAA,MAAwC;IAKpChC,EAAA,CAAAgB,SAAA,GAA+B;IAA/BhB,EAAA,CAAAyD,WAAA,UAAAzB,UAAA,CAAA0B,OAAA,CAA+B;IAE9B1D,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAE,UAAA,SAAA8B,UAAA,CAAAC,UAAA,CAAwB;IAIxBjC,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAE,UAAA,SAAA8B,UAAA,CAAAE,WAAA,CAAyB;IAIzBlC,EAAA,CAAAgB,SAAA,GAA2B;IAA3BhB,EAAA,CAAAE,UAAA,SAAA8B,UAAA,CAAAG,aAAA,CAA2B;IAKbnC,EAAA,CAAAgB,SAAA,GAAkB;IAAlBhB,EAAA,CAAAiB,iBAAA,CAAAe,UAAA,CAAAb,IAAA,CAAkB;IAClBnB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAiB,iBAAA,CAAAe,UAAA,CAAA2B,KAAA,CAAmB;IAEd3D,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAA8B,kBAAA,WAAAE,UAAA,CAAA4B,KAAA,KAAoB;IACzC5D,EAAA,CAAAgB,SAAA,EAAoE;IAApEhB,EAAA,CAAAE,UAAA,SAAA8B,UAAA,CAAAI,aAAA,IAAAJ,UAAA,CAAAI,aAAA,GAAAJ,UAAA,CAAA4B,KAAA,CAAoE;;;;;IA5BnF5D,EAAA,CAAAG,cAAA,cAA+D;IAC7DH,EAAA,CAAAoB,UAAA,IAAAyC,yCAAA,oBAAkG;IAwCpG7D,EAAA,CAAAe,YAAA,EAAM;;;;IAxCqBf,EAAA,CAAAgB,SAAA,EAAmB;IAAnBhB,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAoD,gBAAA,CAAmB;;;;;IA2C9C9D,EAAA,CAAAC,SAAA,sBAWc;;;IAFZD,EAHA,CAAAE,UAAA,qBAAoB,gBAAAF,EAAA,CAAA4B,eAAA,IAAAmC,GAAA,EAGuD;;;;;IAmBnE/D,EAAA,CAAAG,cAAA,WAAiC;IAAAH,EAAA,CAAAc,MAAA,GAAwB;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAA/Bf,EAAA,CAAAgB,SAAA,EAAwB;IAAxBhB,EAAA,CAAAiB,iBAAA,CAAA+C,UAAA,CAAA/B,UAAA,CAAwB;;;;;IAIzDjC,EAAA,CAAAG,cAAA,WAAkC;IAAAH,EAAA,CAAAc,MAAA,GAAyB;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAAhCf,EAAA,CAAAgB,SAAA,EAAyB;IAAzBhB,EAAA,CAAAiB,iBAAA,CAAA+C,UAAA,CAAA9B,WAAA,CAAyB;;;;;IAI3DlC,EAAA,CAAAG,cAAA,WAAoC;IAAAH,EAAA,CAAAc,MAAA,GAA2B;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAAlCf,EAAA,CAAAgB,SAAA,EAA2B;IAA3BhB,EAAA,CAAAiB,iBAAA,CAAA+C,UAAA,CAAA7B,aAAA,CAA2B;;;;;IASjEnC,EAAA,CAAAG,cAAA,eAC6B;IAAAH,EAAA,CAAAc,MAAA,GAA4B;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAAnCf,EAAA,CAAAgB,SAAA,EAA4B;IAA5BhB,EAAA,CAAA8B,kBAAA,WAAAkC,UAAA,CAAA5B,aAAA,KAA4B;;;;;;IA1B/DpC,EAAA,CAAAG,cAAA,cAA6F;IAA/BH,EAAA,CAAAI,UAAA,mBAAA6D,+DAAA;MAAA,MAAAD,UAAA,GAAAhE,EAAA,CAAAO,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA6B,WAAA,CAAAyB,UAAA,CAAoB;IAAA,EAAC;IAC1FhE,EAAA,CAAAG,cAAA,cAAqC;IACnCH,EAAA,CAAAC,SAAA,cAAiF;IACjFD,EAAA,CAAAG,cAAA,cAAuB;IAAAH,EAAA,CAAAc,MAAA,UAAG;IAAAd,EAAA,CAAAe,YAAA,EAAM;IAE9Bf,EADF,CAAAG,cAAA,cAA6B,iBAEa;IADhCH,EAAA,CAAAI,UAAA,mBAAA+D,kEAAAzB,MAAA;MAAA,MAAAsB,UAAA,GAAAhE,EAAA,CAAAO,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAiC,WAAA,CAAAqB,UAAA,EAAAtB,MAAA,CAA4B;IAAA,EAAC;IAE5C1C,EAAA,CAAAC,SAAA,YAA4B;IAC5BD,EAAA,CAAAoB,UAAA,IAAAgD,gDAAA,mBAAiC;IACnCpE,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAG,cAAA,iBAA6E;IAArEH,EAAA,CAAAI,UAAA,mBAAAiE,kEAAA3B,MAAA;MAAA,MAAAsB,UAAA,GAAAhE,EAAA,CAAAO,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAoC,YAAA,CAAAkB,UAAA,EAAAtB,MAAA,CAA6B;IAAA,EAAC;IAC7C1C,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAoB,UAAA,KAAAkD,iDAAA,mBAAkC;IACpCtE,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAG,cAAA,kBAAmF;IAA3EH,EAAA,CAAAI,UAAA,mBAAAmE,mEAAA7B,MAAA;MAAA,MAAAsB,UAAA,GAAAhE,EAAA,CAAAO,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuC,gBAAA,CAAAe,UAAA,EAAAtB,MAAA,CAAiC;IAAA,EAAC;IACjD1C,EAAA,CAAAC,SAAA,aAA8B;IAC9BD,EAAA,CAAAoB,UAAA,KAAAoD,iDAAA,mBAAoC;IAG1CxE,EAFI,CAAAe,YAAA,EAAS,EACL,EACF;IAEJf,EADF,CAAAG,cAAA,eAA0B,cACC;IAAAH,EAAA,CAAAc,MAAA,IAAkB;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAChDf,EAAA,CAAAG,cAAA,aAAyB;IAAAH,EAAA,CAAAc,MAAA,IAAmB;IAAAd,EAAA,CAAAe,YAAA,EAAI;IAE9Cf,EADF,CAAAG,cAAA,eAA6B,gBACC;IAAAH,EAAA,CAAAc,MAAA,IAAoB;IAAAd,EAAA,CAAAe,YAAA,EAAO;IACvDf,EAAA,CAAAoB,UAAA,KAAAqD,iDAAA,mBAC6B;IAC/BzE,EAAA,CAAAe,YAAA,EAAM;IAEJf,EADF,CAAAG,cAAA,eAA6B,kBAC2C;IAA9DH,EAAA,CAAAI,UAAA,mBAAAsE,mEAAAhC,MAAA;MAAA,MAAAsB,UAAA,GAAAhE,EAAA,CAAAO,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2C,aAAA,CAAAW,UAAA,EAAAtB,MAAA,CAA8B;IAAA,EAAC;IAC9C1C,EAAA,CAAAC,SAAA,aAA4B;IAACD,EAAA,CAAAc,MAAA,kBAC/B;IAAAd,EAAA,CAAAe,YAAA,EAAS;IACTf,EAAA,CAAAG,cAAA,kBAA8D;IAAtDH,EAAA,CAAAI,UAAA,mBAAAuE,mEAAAjC,MAAA;MAAA,MAAAsB,UAAA,GAAAhE,EAAA,CAAAO,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA6C,SAAA,CAAAS,UAAA,EAAAtB,MAAA,CAA0B;IAAA,EAAC;IAC1C1C,EAAA,CAAAC,SAAA,aAAoC;IAACD,EAAA,CAAAc,MAAA,qBACvC;IAGNd,EAHM,CAAAe,YAAA,EAAS,EACL,EACF,EACF;;;;;IAnCGf,EAAA,CAAAgB,SAAA,GAAgC;IAAChB,EAAjC,CAAAE,UAAA,QAAAQ,MAAA,CAAA8C,eAAA,CAAAQ,UAAA,GAAAhE,EAAA,CAAAwB,aAAA,CAAgC,QAAAwC,UAAA,CAAA7C,IAAA,CAAqB;IAIhDnB,EAAA,CAAAgB,SAAA,GAA+B;IAA/BhB,EAAA,CAAAyD,WAAA,UAAAO,UAAA,CAAAN,OAAA,CAA+B;IAE9B1D,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAE,UAAA,SAAA8D,UAAA,CAAA/B,UAAA,CAAwB;IAIxBjC,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAE,UAAA,SAAA8D,UAAA,CAAA9B,WAAA,CAAyB;IAIzBlC,EAAA,CAAAgB,SAAA,GAA2B;IAA3BhB,EAAA,CAAAE,UAAA,SAAA8D,UAAA,CAAA7B,aAAA,CAA2B;IAKbnC,EAAA,CAAAgB,SAAA,GAAkB;IAAlBhB,EAAA,CAAAiB,iBAAA,CAAA+C,UAAA,CAAA7C,IAAA,CAAkB;IAClBnB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAiB,iBAAA,CAAA+C,UAAA,CAAAL,KAAA,CAAmB;IAEd3D,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAA8B,kBAAA,WAAAkC,UAAA,CAAAJ,KAAA,KAAoB;IACzC5D,EAAA,CAAAgB,SAAA,EAAoE;IAApEhB,EAAA,CAAAE,UAAA,SAAA8D,UAAA,CAAA5B,aAAA,IAAA4B,UAAA,CAAA5B,aAAA,GAAA4B,UAAA,CAAAJ,KAAA,CAAoE;;;;;IA1BnF5D,EAAA,CAAAG,cAAA,cAA0D;IACxDH,EAAA,CAAAoB,UAAA,IAAAwD,yCAAA,oBAA6F;IAsC/F5E,EAAA,CAAAe,YAAA,EAAM;;;;IAtCqBf,EAAA,CAAAgB,SAAA,EAAc;IAAdhB,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAmE,WAAA,CAAc;;;;;IAyCzC7E,EAAA,CAAAC,SAAA,sBAWc;;;IAFZD,EAHA,CAAAE,UAAA,qBAAoB,gBAAAF,EAAA,CAAA4B,eAAA,IAAAkD,GAAA,EAGiE;;;;;;IArLrF9E,EAJN,CAAAG,cAAA,aAA2C,aAEb,aACF,eAMC;IAHrBH,EAAA,CAAA+E,gBAAA,2BAAAC,4DAAAtC,MAAA;MAAA1C,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAvE,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAkF,kBAAA,CAAAxE,MAAA,CAAAyE,WAAA,EAAAzC,MAAA,MAAAhC,MAAA,CAAAyE,WAAA,GAAAzC,MAAA;MAAA,OAAA1C,EAAA,CAAAY,WAAA,CAAA8B,MAAA;IAAA,EAAyB;IAEzB1C,EAAA,CAAAI,UAAA,yBAAAgF,0DAAA;MAAApF,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAvE,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAeF,MAAA,CAAA2E,MAAA,EAAQ;IAAA,EAAC;IAJ1BrF,EAAA,CAAAe,YAAA,EAKuB;IACvBf,EAAA,CAAAG,cAAA,gBAA8C;IAAtCH,EAAA,CAAAI,UAAA,mBAAAkF,qDAAA;MAAAtF,EAAA,CAAAO,aAAA,CAAA0E,GAAA;MAAA,MAAAvE,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2E,MAAA,EAAQ;IAAA,EAAC;IACxBrF,EAAA,CAAAC,SAAA,YAA6B;IAGnCD,EAFI,CAAAe,YAAA,EAAS,EACL,EACF;IAIJf,EADF,CAAAG,cAAA,kBAAkC,aACN;IAAAH,EAAA,CAAAc,MAAA,uBAAgB;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAC/Cf,EAAA,CAAAG,cAAA,cAA6B;IAC3BH,EAAA,CAAAoB,UAAA,KAAAmE,mCAAA,kBAGyC;IAK7CvF,EADE,CAAAe,YAAA,EAAM,EACE;IAIRf,EADF,CAAAG,cAAA,mBAAyC,cACb;IAAAH,EAAA,CAAAc,MAAA,uBAAe;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAG9Cf,EAAA,CAAAC,SAAA,+BAMsB;IAYtBD,EATA,CAAAoB,UAAA,KAAAoE,mCAAA,kBAA6D,KAAAC,2CAAA,0BAkBxB;IAEvCzF,EAAA,CAAAe,YAAA,EAAU;IAIRf,EADF,CAAAG,cAAA,mBAAkC,cACN;IAAAH,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAe,YAAA,EAAK;IA+C3Cf,EA5CA,CAAAoB,UAAA,KAAAsE,mCAAA,kBAA+D,KAAAC,2CAAA,0BAsDtB;IAE3C3F,EAAA,CAAAe,YAAA,EAAU;IAIRf,EADF,CAAAG,cAAA,mBAAsC,cACV;IAAAH,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAe,YAAA,EAAK;IA6C3Cf,EA1CA,CAAAoB,UAAA,KAAAwE,mCAAA,kBAA0D,KAAAC,2CAAA,0BAoDxB;IAGtC7F,EADE,CAAAe,YAAA,EAAU,EACN;;;;IAvLEf,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAA8F,gBAAA,YAAApF,MAAA,CAAAyE,WAAA,CAAyB;IAeJnF,EAAA,CAAAgB,SAAA,GAAa;IAAbhB,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAqF,UAAA,CAAa;IAepC/F,EAAA,CAAAgB,SAAA,GAA0B;IAC1BhB,EADA,CAAAE,UAAA,2BAA0B,kCACO;IAO7BF,EAAA,CAAAgB,SAAA,EAA+B;IAA/BhB,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAiB,cAAA,CAAAqE,MAAA,KAA+B;IAUlChG,EAAA,CAAAgB,SAAA,EAA6C;IAA7ChB,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAiB,cAAA,CAAAqE,MAAA,WAAAtF,MAAA,CAAAuF,OAAA,CAA6C;IAiB1CjG,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAoD,gBAAA,CAAAkC,MAAA,KAAiC;IA6CpChG,EAAA,CAAAgB,SAAA,EAA+C;IAA/ChB,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAoD,gBAAA,CAAAkC,MAAA,WAAAtF,MAAA,CAAAuF,OAAA,CAA+C;IAkB5CjG,EAAA,CAAAgB,SAAA,GAA4B;IAA5BhB,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAmE,WAAA,CAAAmB,MAAA,KAA4B;IA2C/BhG,EAAA,CAAAgB,SAAA,EAA0C;IAA1ChB,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAmE,WAAA,CAAAmB,MAAA,WAAAtF,MAAA,CAAAuF,OAAA,CAA0C;;;ADtKnD,OAAM,MAAOC,aAAa;EA4BxBC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc,EACdC,KAAqB,EACrBC,IAAgB,EAChBC,mBAAwC,EACxCC,cAA8B;IAR9B,KAAAR,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IApCxB,KAAAjF,cAAc,GAAU,EAAE;IAC1B,KAAAmC,gBAAgB,GAAU,EAAE;IAC5B,KAAAe,WAAW,GAAU,EAAE;IACvB,KAAAkB,UAAU,GAAU,EAAE;IACtB,KAAAc,UAAU,GAAU,EAAE;IACtB,KAAA1B,WAAW,GAAW,EAAE;IACxB,KAAAc,OAAO,GAAG,IAAI;IAEd;IACA,KAAAa,gBAAgB,GAAG,EAAE;IACrB,KAAAC,mBAAmB,GAAG,EAAE;IACxB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,UAAU,GAAG,EAAE,CAAC,CAAC;IACjB,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,UAAU,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAK,CAAE;IAEnC;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,gBAAgB,GAAU,EAAE;IAEpB,KAAAC,aAAa,GAAmB,EAAE;EAYvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,aAAa,CAACE,IAAI,CACrB,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAI,CAAC7C,WAAW,GAAG6C,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;MACpC,IAAI,CAAClB,gBAAgB,GAAGkB,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE;MAChD,IAAI,CAACf,UAAU,GAAGe,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;MACxC,IAAI,CAACd,MAAM,GAAGc,MAAM,CAAC,MAAM,CAAC,IAAI,UAAU;MAE1C,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC,CACH;EAEH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,aAAa,CAACQ,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEAJ,YAAYA,CAAA;IACV,IAAI,CAAChC,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,IAAI,CAACd,WAAW,IAAI,IAAI,CAAC2B,gBAAgB,IAAI,IAAI,CAACG,UAAU,EAAE;MAChE,IAAI,CAACqB,uBAAuB,EAAE;KAC/B,MAAM;MACL;MACAC,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,kBAAkB,EAAE,EACzB,IAAI,CAACC,oBAAoB,EAAE,EAC3B,IAAI,CAACC,eAAe,EAAE,EACtB,IAAI,CAACC,cAAc,EAAE,EACrB,IAAI,CAACC,cAAc,EAAE,CACtB,CAAC,CAACC,OAAO,CAAC,MAAK;QACd,IAAI,CAAC7C,OAAO,GAAG,KAAK;MACtB,CAAC,CAAC;;EAEN;EAEAwC,kBAAkBA,CAAA;IAChB,IAAI,CAAC7B,cAAc,CAACmC,YAAY,CAAChJ,cAAc,CAACiJ,IAAI,CAACC,WAAW,EAAE,4BAA4B,CAAC;IAE/F,OAAO,IAAI,CAAC7C,cAAc,CAAC8C,iBAAiB,EAAE,CAACC,SAAS,EAAE,CAACC,IAAI,CAC5DC,QAAQ,IAAI;MACX,IAAIA,QAAQ,EAAEC,OAAO,EAAE;QACrB,IAAI,CAAC3H,cAAc,GAAG0H,QAAQ,CAACE,IAAI,IAAI,EAAE;QACzCC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC9H,cAAc,CAACqE,MAAM,CAAC;OACrE,MAAM;QACL,IAAI,CAACrE,cAAc,GAAG,EAAE;QACxB6H,OAAO,CAACE,IAAI,CAAC,uDAAuD,CAAC;;IAEzE,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdJ,OAAO,CAACI,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACjI,cAAc,GAAG,EAAE;MACxB,IAAI,CAACgF,mBAAmB,CAACkD,WAAW,CAACD,KAAK,EAAE,yBAAyB,CAAC;MACtEJ,OAAO,CAACE,IAAI,CAAC,oDAAoD,CAAC;IACpE,CAAC,CAAC,CAACZ,OAAO,CAAC,MAAK;MACd,IAAI,CAAClC,cAAc,CAACkD,WAAW,CAAC/J,cAAc,CAACiJ,IAAI,CAACC,WAAW,CAAC;IAClE,CAAC,CAAC;EACJ;EAEAP,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACtC,cAAc,CAAC2D,mBAAmB,CAAC,CAAC,CAAC,CAACZ,SAAS,EAAE,CAACC,IAAI,CAC/DC,QAAQ,IAAI;MACX,IAAIA,QAAQ,EAAEC,OAAO,EAAE;QACrB,IAAI,CAACxF,gBAAgB,GAAGuF,QAAQ,CAACE,IAAI,IAAI,EAAE;QAC3CC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC3F,gBAAgB,CAACkC,MAAM,CAAC;OACzE,MAAM;QACL,IAAI,CAAClC,gBAAgB,GAAG,EAAE;QAC1B0F,OAAO,CAACE,IAAI,CAAC,yDAAyD,CAAC;;IAE3E,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdJ,OAAO,CAACI,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAC9F,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAAC6C,mBAAmB,CAACkD,WAAW,CAACD,KAAK,EAAE,2BAA2B,CAAC;MACxEJ,OAAO,CAACE,IAAI,CAAC,sDAAsD,CAAC;IACtE,CAAC,CAAC;EACJ;EAEAf,eAAeA,CAAA;IACb,OAAO,IAAI,CAACvC,cAAc,CAAC4D,cAAc,CAAC,CAAC,CAAC,CAACb,SAAS,EAAE,CAACC,IAAI,CAC1DC,QAAQ,IAAI;MACX,IAAIA,QAAQ,EAAEC,OAAO,EAAE;QACrB,IAAI,CAACzE,WAAW,GAAGwE,QAAQ,CAACE,IAAI,IAAI,EAAE;QACtCC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC5E,WAAW,CAACmB,MAAM,CAAC;OAC/D,MAAM;QACL,IAAI,CAACnB,WAAW,GAAG,EAAE;QACrB2E,OAAO,CAACE,IAAI,CAAC,oDAAoD,CAAC;;IAEtE,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAI,CAAC/E,WAAW,GAAG,EAAE;MACrB,IAAI,CAAC8B,mBAAmB,CAACkD,WAAW,CAACD,KAAK,EAAE,sBAAsB,CAAC;MACnEJ,OAAO,CAACE,IAAI,CAAC,iDAAiD,CAAC;IACjE,CAAC,CAAC;EACJ;EAEAd,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACxC,cAAc,CAAC6D,aAAa,EAAE,CAACd,SAAS,EAAE,CAACC,IAAI,CACxDC,QAAQ,IAAI;MACX,IAAI,CAACtD,UAAU,GAAGsD,QAAQ,EAAEE,IAAI,IAAI,EAAE;MACtCC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC1D,UAAU,CAACC,MAAM,CAAC;IAC7D,CAAC,CACF,CAAC2D,KAAK,CAACC,KAAK,IAAG;MACdJ,OAAO,CAACI,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,CAAC7D,UAAU,GAAG,EAAE;IACtB,CAAC,CAAC;EACJ;EAEA8C,cAAcA,CAAA;IACZ;IACA,OAAO,IAAI,CAACnC,IAAI,CAACwD,GAAG,CAAM,GAAGvK,WAAW,CAACwK,MAAM,mBAAmB,CAAC,CAAChB,SAAS,EAAE,CAACC,IAAI,CACjFC,QAAQ,IAAI;MACX,IAAI,CAACxC,UAAU,GAAGwC,QAAQ,EAAEE,IAAI,IAAIF,QAAQ,EAAEe,KAAK,IAAI,EAAE;MACzDZ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC5C,UAAU,CAACb,MAAM,CAAC;IAC9D,CAAC,CACF,CAAC2D,KAAK,CAACC,KAAK,IAAG;MACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAI,CAACjD,mBAAmB,CAACkD,WAAW,CAACD,KAAK,EAAE,qBAAqB,CAAC;MAElE;MACA,IAAI,CAAC/C,UAAU,GAAG,EAAE;MACpB2C,OAAO,CAACE,IAAI,CAAC,gDAAgD,CAAC;IAChE,CAAC,CAAC;EACJ;EAEA;EACA/G,WAAWA,CAAC0H,OAAY,EAAEC,KAAY;IACpCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAClE,WAAW,CAACmE,eAAe,EAAE;MACrC,IAAI,CAAChE,MAAM,CAACiE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGFJ,OAAO,CAAC3G,OAAO,GAAG,CAAC2G,OAAO,CAAC3G,OAAO;IAClC2G,OAAO,CAACpI,UAAU,GAAGoI,OAAO,CAAC3G,OAAO,GAAG,CAAC2G,OAAO,CAACpI,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAACoI,OAAO,CAACpI,UAAU,IAAI,CAAC,IAAI,CAAC;IAEpG,IAAI,CAACmE,cAAc,CAACsE,iBAAiB,CAACL,OAAO,CAACM,GAAG,CAAC,CAAC5C,SAAS,CAAC;MAC3D6C,IAAI,EAAGvB,QAAQ,IAAI;QACjBG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,QAAQ,CAAC;MAChD,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDS,OAAO,CAAC3G,OAAO,GAAG,CAAC2G,OAAO,CAAC3G,OAAO;QAClC2G,OAAO,CAACpI,UAAU,GAAGoI,OAAO,CAAC3G,OAAO,GAAG,CAAC2G,OAAO,CAACpI,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAACoI,OAAO,CAACpI,UAAU,IAAI,CAAC,IAAI,CAAC;MACtG;KACD,CAAC;EACJ;EAEAa,YAAYA,CAACuH,OAAY,EAAEC,KAAY;IACrCA,KAAK,CAACC,eAAe,EAAE;IACvB,MAAMM,SAAS,GAAG;MAChBC,KAAK,EAAET,OAAO,CAAClJ,IAAI;MACnB4J,IAAI,EAAE,mCAAmCV,OAAO,CAAClJ,IAAI,EAAE;MACvD6J,GAAG,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYd,OAAO,CAACM,GAAG;KACtD;IAED,IAAIS,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAACR,SAAS,CAAC;KAC3B,MAAM;MACLO,SAAS,CAACE,SAAS,CAACC,SAAS,CAACV,SAAS,CAACG,GAAG,CAAC,CAAC5B,IAAI,CAAC,MAAK;QACrDoC,KAAK,CAAC,mCAAmC,CAAC;MAC5C,CAAC,CAAC;;IAGJ,IAAI,CAACpF,cAAc,CAACtD,YAAY,CAACuH,OAAO,CAACM,GAAG,CAAC,CAAC5C,SAAS,CAAC;MACtD6C,IAAI,EAAGvB,QAAQ,IAAI;QACjBgB,OAAO,CAACnI,WAAW,GAAG,CAACmI,OAAO,CAACnI,WAAW,IAAI,CAAC,IAAI,CAAC;MACtD,CAAC;MACD0H,KAAK,EAAGA,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEA3G,gBAAgBA,CAACoH,OAAY,EAAEC,KAAY;IACzCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAClE,WAAW,CAACmE,eAAe,EAAE;MACrC,IAAI,CAAChE,MAAM,CAACiE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF,IAAI,CAACjE,MAAM,CAACiE,QAAQ,CAAC,CAAC,UAAU,EAAEJ,OAAO,CAACM,GAAG,CAAC,EAAE;MAC9C7C,WAAW,EAAE;QAAE2D,MAAM,EAAE;MAAS;KACjC,CAAC;EACJ;EAEApI,aAAaA,CAACgH,OAAY,EAAEC,KAAY;IACtCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAClE,WAAW,CAACmE,eAAe,EAAE;MACrC,IAAI,CAAChE,MAAM,CAACiE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF,IAAI,CAAClE,eAAe,CAAClD,aAAa,CAACgH,OAAO,CAACM,GAAG,CAAC,CAAC5C,SAAS,CAAC;MACxD6C,IAAI,EAAGvB,QAAQ,IAAI;QACjBgB,OAAO,CAACqB,YAAY,GAAG,IAAI;QAC3BlC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEJ,QAAQ,CAAC;MACrD,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEArG,SAASA,CAAC8G,OAAY,EAAEC,KAAY;IAClCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAClE,WAAW,CAACmE,eAAe,EAAE;MACrC,IAAI,CAAChE,MAAM,CAACiE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF,IAAI,CAACnE,WAAW,CAAC/C,SAAS,CAAC8G,OAAO,CAACM,GAAG,EAAE,CAAC,CAAC,CAAC5C,SAAS,CAAC;MACnD6C,IAAI,EAAGvB,QAAQ,IAAI;QACjBG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEJ,QAAQ,CAAC;QAC/CmC,KAAK,CAAC,qCAAqC,CAAC;MAC9C,CAAC;MACD5B,KAAK,EAAGA,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA;EACArH,WAAWA,CAAC8H,OAAY;IACtB,IAAI,CAAC7D,MAAM,CAACiE,QAAQ,CAAC,CAAC,UAAU,EAAEJ,OAAO,CAACM,GAAG,CAAC,CAAC;EACjD;EAEA9J,kBAAkBA,CAAC8K,QAAa;IAC9B,IAAI,CAACnF,MAAM,CAACiE,QAAQ,CAAC,CAAC,WAAW,EAAEkB,QAAQ,CAACC,IAAI,CAAC,CAAC;EACpD;EAEAvG,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACF,WAAW,CAAC0G,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAAC7D,YAAY,EAAE;;EAEvB;EAEA;EACA8D,gBAAgBA,CAAA;IACd,IAAI,CAAChF,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC+E,eAAe,EAAE;IACtB,IAAI,CAAC7D,YAAY,EAAE;EACrB;EAEA+D,cAAcA,CAAA;IACZ,IAAI,CAACF,eAAe,EAAE;IACtB,IAAI,CAAC7D,YAAY,EAAE;EACrB;EAEAgE,YAAYA,CAAA;IACV,IAAI,CAACH,eAAe,EAAE;IACtB,IAAI,CAAC7D,YAAY,EAAE;EACrB;EAEAiE,YAAYA,CAAA;IACV,IAAI,CAAC/G,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC2B,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,MAAM,GAAG,UAAU;IACxB,IAAI,CAACC,UAAU,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAK,CAAE;IACxC,IAAI,CAACyE,eAAe,EAAE;IACtB,IAAI,CAAC7D,YAAY,EAAE;EACrB;EAEQ6D,eAAeA,CAAA;IACrB,MAAMhE,WAAW,GAAQ,EAAE;IAE3B,IAAI,IAAI,CAAC3C,WAAW,EAAE2C,WAAW,CAACqE,CAAC,GAAG,IAAI,CAAChH,WAAW;IACtD,IAAI,IAAI,CAAC2B,gBAAgB,EAAEgB,WAAW,CAAC6D,QAAQ,GAAG,IAAI,CAAC7E,gBAAgB;IACvE,IAAI,IAAI,CAACG,UAAU,EAAEa,WAAW,CAACsE,MAAM,GAAG,IAAI,CAACnF,UAAU;IACzD,IAAI,IAAI,CAACC,MAAM,KAAK,UAAU,EAAEY,WAAW,CAACuE,IAAI,GAAG,IAAI,CAACnF,MAAM;IAE9D,IAAI,CAACV,MAAM,CAACiE,QAAQ,CAAC,EAAE,EAAE;MACvB6B,UAAU,EAAE,IAAI,CAAC7F,KAAK;MACtBqB,WAAW;MACXyE,mBAAmB,EAAE;KACtB,CAAC;EACJ;EAEA;EACAjE,uBAAuBA,CAAA;IACrB,IAAI,CAACrC,OAAO,GAAG,IAAI;IAEnB,IAAIuG,QAAQ,GAAG,GAAG7M,WAAW,CAACwK,MAAM,WAAW;IAE/C;IACA,IAAI,IAAI,CAAClD,UAAU,KAAK,WAAW,EAAE;MACnCuF,QAAQ,GAAG,GAAG7M,WAAW,CAACwK,MAAM,qBAAqB;KACtD,MAAM,IAAI,IAAI,CAAClD,UAAU,KAAK,UAAU,EAAE;MACzCuF,QAAQ,GAAG,GAAG7M,WAAW,CAACwK,MAAM,oBAAoB;;IAGtD,MAAMnC,MAAM,GAAG,IAAIyE,eAAe,CAAC;MACjCC,IAAI,EAAE,IAAI,CAACpF,WAAW,CAACqF,QAAQ,EAAE;MACjCC,KAAK,EAAE,IAAI,CAACrF,YAAY,CAACoF,QAAQ,EAAE;MACnCN,IAAI,EAAE,IAAI,CAACnF;KACZ,CAAC;IAEF,IAAI,IAAI,CAAC/B,WAAW,EAAE6C,MAAM,CAAC6E,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC1H,WAAW,CAAC;IAC1D,IAAI,IAAI,CAAC2B,gBAAgB,EAAEkB,MAAM,CAAC6E,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC/F,gBAAgB,CAAC;IAC3E,IAAI,IAAI,CAACC,mBAAmB,EAAEiB,MAAM,CAAC6E,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC9F,mBAAmB,CAAC;IACpF,IAAI,IAAI,CAACC,aAAa,EAAEgB,MAAM,CAAC6E,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC7F,aAAa,CAAC;IAElE,IAAI,CAACW,aAAa,CAACE,IAAI,CACrB,IAAI,CAACnB,IAAI,CAACwD,GAAG,CAAM,GAAGsC,QAAQ,IAAIxE,MAAM,CAAC2E,QAAQ,EAAE,EAAE,CAAC,CAAC5E,SAAS,CAAC;MAC/D6C,IAAI,EAAGvB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC7B,WAAW,GAAG4B,QAAQ,CAACyD,QAAQ,IAAI,EAAE;UAC1C,IAAI,CAACpF,gBAAgB,GAAG,IAAI,CAACD,WAAW;UACxC,IAAI,CAACD,OAAO,GAAG6B,QAAQ,CAAC0D,UAAU,EAAEL,IAAI,GAAGrD,QAAQ,CAAC0D,UAAU,EAAEC,KAAK;;QAEvE,IAAI,CAAC/G,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2D,KAAK,EAAGA,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAAC3D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;EACH;EAEAzC,eAAeA,CAAC6G,OAAY;IAC1B,OAAOA,OAAO,CAAC4C,MAAM,GAAG,CAAC,CAAC,EAAEjC,GAAG,IAAI,gCAAgC;EACrE;EAEAjJ,qBAAqBA,CAACsI,OAAY;IAChC,IAAI,CAACA,OAAO,CAACjI,aAAa,IAAIiI,OAAO,CAACjI,aAAa,IAAIiI,OAAO,CAACzG,KAAK,EAAE,OAAO,CAAC;IAC9E,OAAOsJ,IAAI,CAACC,KAAK,CAAE,CAAC9C,OAAO,CAACjI,aAAa,GAAGiI,OAAO,CAACzG,KAAK,IAAIyG,OAAO,CAACjI,aAAa,GAAI,GAAG,CAAC;EAC5F;;;uBAvXW8D,aAAa,EAAAlG,EAAA,CAAAoN,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtN,EAAA,CAAAoN,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxN,EAAA,CAAAoN,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1N,EAAA,CAAAoN,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA5N,EAAA,CAAAoN,iBAAA,CAAAS,EAAA,CAAAC,MAAA,GAAA9N,EAAA,CAAAoN,iBAAA,CAAAS,EAAA,CAAAE,cAAA,GAAA/N,EAAA,CAAAoN,iBAAA,CAAAY,EAAA,CAAAC,UAAA,GAAAjO,EAAA,CAAAoN,iBAAA,CAAAc,EAAA,CAAAC,mBAAA,GAAAnO,EAAA,CAAAoN,iBAAA,CAAAgB,EAAA,CAAArO,cAAA;IAAA;EAAA;;;YAAbmG,aAAa;MAAAmI,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvO,EAAA,CAAAwO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxB1B9O,EAAA,CAAAG,cAAA,aAA4B;UAE1BH,EAAA,CAAAC,SAAA,2BAAiE;UAWjED,EARA,CAAAoB,UAAA,IAAA4N,4CAAA,iCAIyB,IAAAC,4BAAA,mBAIkB;UA8L7CjP,EAAA,CAAAe,YAAA,EAAM;;;UAzMef,EAAA,CAAAgB,SAAA,EAAyB;UAAzBhB,EAAA,CAAAE,UAAA,0BAAyB;UAIzCF,EAAA,CAAAgB,SAAA,EAAa;UAAbhB,EAAA,CAAAE,UAAA,SAAA6O,GAAA,CAAA9I,OAAA,CAAa;UAOVjG,EAAA,CAAAgB,SAAA,EAAc;UAAdhB,EAAA,CAAAE,UAAA,UAAA6O,GAAA,CAAA9I,OAAA,CAAc;;;qBDOVxG,YAAY,EAAAyP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1P,WAAW,EAAA2P,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EAAE5P,eAAe,EAAEC,uBAAuB,EAAEC,qBAAqB;MAAA2P,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}