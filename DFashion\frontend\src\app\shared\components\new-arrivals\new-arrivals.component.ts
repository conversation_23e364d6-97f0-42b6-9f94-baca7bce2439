import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ShopDataService, NewArrival } from '../../../core/services/shop-data.service';

@Component({
  selector: 'app-new-arrivals',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="new-arrivals-section">
      <!-- Section Header -->
      <div class="section-header">
        <div class="header-content">
          <h2 class="section-title">
            <i class="fas fa-sparkles"></i>
            New Arrivals
          </h2>
          <p class="section-subtitle">Fresh styles just landed</p>
        </div>
        <div class="header-actions">
          <div class="time-filters">
            <button 
              *ngFor="let filter of timeFilters"
              class="filter-btn"
              [class.active]="activeTimeFilter === filter.value"
              (click)="setActiveTimeFilter(filter.value)">
              {{ filter.label }}
            </button>
          </div>
          <button class="view-all-btn" (click)="viewAllNewArrivals()">
            <span>View All</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="products-grid">
          <div *ngFor="let item of [1,2,3,4,5,6,7,8]" class="product-card-skeleton">
            <div class="skeleton-image"></div>
            <div class="skeleton-content">
              <div class="skeleton-text"></div>
              <div class="skeleton-text short"></div>
              <div class="skeleton-text"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Products Grid -->
      <div *ngIf="!isLoading && products.length > 0" class="products-grid">
        <div 
          *ngFor="let product of products; trackBy: trackByProductId" 
          class="product-card"
          (click)="navigateToProduct(product)"
          [attr.aria-label]="'View ' + product.name"
          tabindex="0"
          (keydown.enter)="navigateToProduct(product)"
          (keydown.space)="navigateToProduct(product)">
          
          <!-- Product Image -->
          <div class="product-image-container">
            <img 
              [src]="getProductImage(product)" 
              [alt]="product.name"
              class="product-image"
              loading="lazy"
              (error)="onImageError($event)">
            
            <!-- New Badge -->
            <div class="new-badge">
              <i class="fas fa-sparkles"></i>
              <span>NEW</span>
            </div>
            
            <!-- Discount Badge -->
            <div class="discount-badge" *ngIf="product.pricing.discountPercentage > 0">
              {{ product.pricing.discountPercentage }}% OFF
            </div>
            
            <!-- Arrival Date -->
            <div class="arrival-date">
              {{ getArrivalDateText(product.createdAt) }}
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
              <button 
                class="quick-action-btn"
                (click)="toggleWishlist(product); $event.stopPropagation()"
                [attr.aria-label]="'Add to wishlist'">
                <i class="fas fa-heart" [class.wishlisted]="isInWishlist(product._id)"></i>
              </button>
              <button 
                class="quick-action-btn"
                (click)="quickView(product); $event.stopPropagation()"
                [attr.aria-label]="'Quick view'">
                <i class="fas fa-eye"></i>
              </button>
              <button 
                class="quick-action-btn"
                (click)="addToCart(product); $event.stopPropagation()"
                [attr.aria-label]="'Add to cart'"
                [disabled]="product.availability.status !== 'in-stock'">
                <i class="fas fa-shopping-cart"></i>
              </button>
            </div>
          </div>

          <!-- Product Info -->
          <div class="product-info">
            <div class="product-brand">{{ product.brand }}</div>
            <h3 class="product-name">{{ product.name }}</h3>
            
            <!-- Rating -->
            <div class="product-rating" *ngIf="product.rating.count > 0">
              <div class="stars">
                <i *ngFor="let star of getStarArray(product.rating.average)" 
                   class="fas fa-star" 
                   [class.filled]="star <= product.rating.average"></i>
              </div>
              <span class="rating-text">({{ product.rating.count }})</span>
            </div>
            
            <!-- Pricing -->
            <div class="product-pricing">
              <span class="current-price">₹{{ product.pricing.sellingPrice | number:'1.0-0' }}</span>
              <span class="original-price" *ngIf="product.pricing.discountPercentage > 0">
                ₹{{ product.pricing.mrp | number:'1.0-0' }}
              </span>
            </div>
            
            <!-- Stock Status -->
            <div class="stock-status" [class]="product.availability.status">
              <span *ngIf="product.availability.status === 'in-stock'">
                <i class="fas fa-check-circle"></i>
                In Stock
              </span>
              <span *ngIf="product.availability.status === 'low-stock'">
                <i class="fas fa-exclamation-triangle"></i>
                Limited Stock
              </span>
              <span *ngIf="product.availability.status === 'out-of-stock'">
                <i class="fas fa-times-circle"></i>
                Out of Stock
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Load More Button -->
      <div class="load-more-container" *ngIf="!isLoading && products.length > 0 && hasMoreProducts">
        <button class="load-more-btn" (click)="loadMoreProducts()" [disabled]="isLoadingMore">
          <i class="fas fa-plus" *ngIf="!isLoadingMore"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="isLoadingMore"></i>
          <span>{{ isLoadingMore ? 'Loading...' : 'Load More' }}</span>
        </button>
      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && products.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-box-open"></i>
        </div>
        <h3>No New Arrivals</h3>
        <p>Check back soon for the latest fashion!</p>
        <button class="retry-btn" (click)="loadProducts()">
          <i class="fas fa-refresh"></i>
          Try Again
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./new-arrivals.component.scss']
})
export class NewArrivalsComponent implements OnInit, OnDestroy {
  @Input() maxProducts: number = 8;
  @Input() showHeader: boolean = true;
  @Input() showLoadMore: boolean = true;
  
  products: NewArrival[] = [];
  isLoading: boolean = true;
  isLoadingMore: boolean = false;
  hasMoreProducts: boolean = true;
  activeTimeFilter: string = 'all';
  wishlistItems: Set<string> = new Set();
  currentPage: number = 1;
  
  timeFilters = [
    { label: 'All Time', value: 'all' },
    { label: 'Today', value: 'today' },
    { label: 'This Week', value: 'week' },
    { label: 'This Month', value: 'month' }
  ];
  
  private destroy$ = new Subject<void>();

  constructor(
    private shopDataService: ShopDataService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadProducts();
    this.loadWishlist();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadProducts(): void {
    this.isLoading = true;
    this.currentPage = 1;
    
    this.shopDataService.loadNewArrivals(this.maxProducts)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (products) => {
          this.products = products;
          this.hasMoreProducts = products.length === this.maxProducts;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading new arrivals:', error);
          this.isLoading = false;
        }
      });
  }

  loadMoreProducts(): void {
    if (this.isLoadingMore || !this.hasMoreProducts) return;
    
    this.isLoadingMore = true;
    this.currentPage++;
    
    // In a real implementation, this would load the next page
    setTimeout(() => {
      // Mock loading more products
      this.isLoadingMore = false;
      this.hasMoreProducts = false; // For demo purposes
    }, 1000);
  }

  setActiveTimeFilter(filter: string): void {
    this.activeTimeFilter = filter;
    this.loadProducts();
  }

  navigateToProduct(product: NewArrival): void {
    this.trackProductClick(product);
    this.router.navigate(['/product', product._id]);
  }

  viewAllNewArrivals(): void {
    this.router.navigate(['/shop/new-arrivals']);
  }

  toggleWishlist(product: NewArrival): void {
    if (this.wishlistItems.has(product._id)) {
      this.wishlistItems.delete(product._id);
    } else {
      this.wishlistItems.add(product._id);
    }
    this.saveWishlist();
  }

  isInWishlist(productId: string): boolean {
    return this.wishlistItems.has(productId);
  }

  quickView(product: NewArrival): void {
    console.log('Quick view:', product);
  }

  addToCart(product: NewArrival): void {
    if (product.availability.status !== 'in-stock') {
      return;
    }
    
    console.log('Add to cart:', product);
    this.showAddToCartSuccess(product);
  }

  getProductImage(product: NewArrival): string {
    const primaryImage = product.images.find(img => img.isPrimary);
    return primaryImage ? primaryImage.url : product.images[0]?.url || '';
  }

  getStarArray(rating: number): number[] {
    return Array(5).fill(0).map((_, i) => i + 1);
  }

  getArrivalDateText(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays <= 7) {
      return `${diffDays} days ago`;
    } else if (diffDays <= 30) {
      const weeks = Math.floor(diffDays / 7);
      return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
  }

  onImageError(event: any): void {
    event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';
  }

  trackByProductId(index: number, product: NewArrival): string {
    return product._id;
  }

  private trackProductClick(product: NewArrival): void {
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'product_click', {
        product_name: product.name,
        product_id: product._id,
        product_brand: product.brand,
        product_category: product.category,
        event_category: 'new_arrivals'
      });
    }
  }

  private loadWishlist(): void {
    const stored = localStorage.getItem('dfashion_wishlist');
    if (stored) {
      try {
        const wishlist = JSON.parse(stored);
        this.wishlistItems = new Set(wishlist);
      } catch (e) {
        console.warn('Failed to load wishlist');
      }
    }
  }

  private saveWishlist(): void {
    localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));
  }

  private showAddToCartSuccess(product: NewArrival): void {
    console.log(`${product.name} added to cart!`);
  }
}
