{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10);\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"h2\");\n    i0.ɵɵtext(4, \"Create\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_0_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddStory());\n    });\n    i0.ɵɵtext(7, \"Create Story\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddReel());\n    });\n    i0.ɵɵtext(9, \"Create Reel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddModal = false);\n    });\n    i0.ɵɵtext(11, \"Create Post\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 14)(13, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_0_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddModal = false);\n    });\n    i0.ɵɵtext(14, \"Cancel\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 12)(2, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_div_6_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startRecording());\n    });\n    i0.ɵɵtext(3, \"Start Recording\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_div_6_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeAddReelModal());\n    });\n    i0.ɵɵtext(5, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_div_6_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.stopRecording());\n    });\n    i0.ɵɵtext(7, \"Stop\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isRecording);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isRecording);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isRecording);\n  }\n}\nfunction ViewAddStoriesComponent_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"video\", 21);\n    i0.ɵɵelementStart(2, \"textarea\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ViewAddStoriesComponent_div_1_div_7_Template_textarea_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newReelCaption, $event) || (ctx_r1.newReelCaption = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 12)(4, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeAddReelModal());\n    });\n    i0.ɵɵtext(5, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_div_7_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.submitNewReel());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.reelPreviewUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newReelCaption);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingReel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingReel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingReel);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.isUploadingReel ? \"Uploading...\" : \"Post Reel\");\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAddReelModal());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"h2\");\n    i0.ɵɵtext(4, \"Create Reel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"video\", 17);\n    i0.ɵɵtemplate(6, ViewAddStoriesComponent_div_1_div_6_Template, 8, 3, \"div\", 18)(7, ViewAddStoriesComponent_div_1_div_7_Template, 8, 6, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reelPreviewUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reelPreviewUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handlePermissionResponse(false));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"h2\");\n    i0.ɵɵtext(4, \"Allow Camera & File Access?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"To add a story, we need access to your camera and files. This is required to take a photo/video or select from your gallery.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 12)(8, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handlePermissionResponse(false));\n    });\n    i0.ɵɵtext(9, \"Block\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handlePermissionResponse(true));\n    });\n    i0.ɵɵtext(11, \"Allow\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showCameraOrGallery = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"h2\");\n    i0.ɵɵtext(4, \"Add Story\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openCamera());\n    });\n    i0.ɵɵtext(7, \"Open Camera\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openGallery());\n    });\n    i0.ɵɵtext(9, \"Choose from Gallery\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showCameraOrGallery = false);\n    });\n    i0.ɵɵtext(11, \"Cancel\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.permissionDenied = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"h2\");\n    i0.ɵɵtext(4, \"Permission Denied\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"You blocked camera and file access. You cannot add a story unless you allow access.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 12)(8, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.permissionDenied = false);\n    });\n    i0.ɵɵtext(9, \"Close\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_5_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAddStoryModal());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"h2\");\n    i0.ɵɵtext(4, \"Add Story\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form\", 23);\n    i0.ɵɵlistener(\"ngSubmit\", function ViewAddStoriesComponent_div_5_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submitNewStory());\n    });\n    i0.ɵɵelementStart(6, \"input\", 24);\n    i0.ɵɵlistener(\"change\", function ViewAddStoriesComponent_div_5_Template_input_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ViewAddStoriesComponent_div_5_Template_textarea_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newStoryCaption, $event) || (ctx_r1.newStoryCaption = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12)(9, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_5_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAddStoryModal());\n    });\n    i0.ɵɵtext(10, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 27);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newStoryCaption);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newStoryFile || ctx_r1.isUploadingStory);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isUploadingStory ? \"Uploading...\" : \"Post Story\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"div\", 31)(2, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_7_div_1_Template, 3, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_div_11_Template_div_click_0_listener() {\n      const i_r12 = i0.ɵɵrestoreView(_r11).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r12));\n    });\n    i0.ɵɵelementStart(1, \"div\", 37);\n    i0.ɵɵelement(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 40);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r13.user.avatar + \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(story_r13.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesLeft());\n    });\n    i0.ɵɵtext(2, \"\\u2190\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35, 0)(5, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAdd());\n    });\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"div\", 38);\n    i0.ɵɵelement(8, \"i\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 40);\n    i0.ɵɵtext(10, \"Add\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, ViewAddStoriesComponent_div_8_div_11_Template, 6, 3, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_8_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesRight());\n    });\n    i0.ɵɵtext(13, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollStoriesLeft);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollStoriesRight);\n  }\n}\nfunction ViewAddStoriesComponent_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵelement(1, \"div\", 81);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r15 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r15 === ctx_r1.currentIndex)(\"completed\", i_r15 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_9_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 82);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_9_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 83);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_9_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_9_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_9_div_21_div_1_Template_div_click_0_listener() {\n      const product_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r17));\n    });\n    i0.ɵɵelementStart(1, \"div\", 88);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 89)(4, \"div\", 90);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 91);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r17.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_9_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_9_div_21_div_1_Template, 8, 2, \"div\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47, 1)(3, \"div\", 48);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_9_div_4_Template, 2, 4, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_9_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_9_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_9_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_9_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 51)(7, \"div\", 52);\n    i0.ɵɵelement(8, \"div\", 53);\n    i0.ɵɵelementStart(9, \"div\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 55);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 56);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_9_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 59);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_9_video_18_Template, 1, 1, \"video\", 60)(19, ViewAddStoriesComponent_div_9_div_19_Template, 1, 2, \"div\", 61)(20, ViewAddStoriesComponent_div_9_div_20_Template, 2, 1, \"div\", 62)(21, ViewAddStoriesComponent_div_9_div_21_Template, 2, 1, \"div\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 64)(23, \"div\", 65)(24, \"button\", 66);\n    i0.ɵɵelement(25, \"i\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 68);\n    i0.ɵɵelement(27, \"i\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 70);\n    i0.ɵɵelement(29, \"i\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 72)(31, \"button\", 73);\n    i0.ɵɵelement(32, \"i\", 74);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"button\", 75);\n    i0.ɵɵelement(36, \"i\", 67);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"button\", 76);\n    i0.ɵɵelement(40, \"i\", 39);\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Add to Cart\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(43, \"div\", 77)(44, \"div\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(45, \"div\", 79, 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(24);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93);\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 95)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 96);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.canScrollStoriesLeft = false;\n    this.canScrollStoriesRight = false;\n    // Handler for Add Story button\n    // Modal state\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    // Reel recording state\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {}\n  removeEventListeners() {}\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        const uploadRes = yield _this2.http.post('/api/stories/upload', formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post('/api/stories', reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories();\n      } catch (err) {\n        alert('Failed to upload reel.');\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post('/api/stories/upload', uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post('/api/stories', storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 11,\n      vars: 10,\n      consts: [[\"storiesSlider\", \"\"], [\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [\"class\", \"add-story-modal\", 4, \"ngIf\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-slider-wrapper\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"add-story-modal\"], [1, \"add-story-modal-backdrop\"], [1, \"add-story-modal-content\"], [1, \"add-story-modal-actions\"], [3, \"click\"], [2, \"margin-top\", \"16px\"], [1, \"cancel-btn\", 3, \"click\"], [1, \"add-story-modal-backdrop\", 3, \"click\"], [\"id\", \"reel-video\", \"width\", \"320\", \"height\", \"480\", \"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 2, \"background\", \"#000\", \"border-radius\", \"12px\", \"margin-bottom\", \"12px\"], [4, \"ngIf\"], [3, \"click\", \"disabled\"], [1, \"cancel-btn\", 3, \"click\", \"disabled\"], [\"width\", \"320\", \"height\", \"480\", \"controls\", \"\", 2, \"border-radius\", \"12px\", 3, \"src\"], [\"placeholder\", \"Write a caption...\", \"name\", \"reelCaption\", \"rows\", \"2\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"autocomplete\", \"off\", 3, \"ngSubmit\"], [\"id\", \"story-file-input\", \"type\", \"file\", \"accept\", \"image/*,video/*\", \"required\", \"\", 2, \"display\", \"none\", 3, \"change\", \"disabled\"], [\"placeholder\", \"Write a caption...\", \"name\", \"caption\", \"rows\", \"2\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"button\", 1, \"cancel-btn\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 3, \"disabled\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-slider-wrapper\"], [1, \"arrow\", \"left\", 3, \"click\", \"disabled\"], [1, \"stories-slider\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-story-avatar\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"arrow\", \"right\", 3, \"click\", \"disabled\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-ring\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\"], [1, \"ecommerce-btn\", \"cart-btn\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ViewAddStoriesComponent_div_0_Template, 15, 0, \"div\", 3)(1, ViewAddStoriesComponent_div_1_Template, 8, 2, \"div\", 3)(2, ViewAddStoriesComponent_div_2_Template, 12, 0, \"div\", 3)(3, ViewAddStoriesComponent_div_3_Template, 12, 0, \"div\", 3)(4, ViewAddStoriesComponent_div_4_Template, 10, 0, \"div\", 3)(5, ViewAddStoriesComponent_div_5_Template, 13, 6, \"div\", 3);\n          i0.ɵɵelementStart(6, \"div\", 4);\n          i0.ɵɵtemplate(7, ViewAddStoriesComponent_div_7_Template, 2, 2, \"div\", 5)(8, ViewAddStoriesComponent_div_8_Template, 14, 3, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, ViewAddStoriesComponent_div_9_Template, 47, 15, \"div\", 7)(10, ViewAddStoriesComponent_div_10_Template, 9, 0, \"div\", 8);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddReelModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showPermissionModal);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCameraOrGallery);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.permissionDenied);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddStoryModal);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.NgModel, i5.NgForm],\n      styles: [\".add-story-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.5);\\n  z-index: 10000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.add-story-modal-backdrop[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.5);\\n  z-index: 1;\\n}\\n\\n.add-story-modal-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  background: #fff;\\n  border-radius: 16px;\\n  padding: 32px 24px 24px 24px;\\n  min-width: 320px;\\n  max-width: 90vw;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.add-story-modal-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 16px;\\n  font-size: 1.3rem;\\n  font-weight: 700;\\n  color: #222;\\n}\\n\\n.add-story-modal-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-size: 1rem;\\n  margin-bottom: 20px;\\n  text-align: center;\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-top: 16px;\\n  justify-content: center;\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 10px 24px;\\n  border-radius: 8px;\\n  border: none;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  background: linear-gradient(90deg, #f09433 0%, #e6683c 50%, #dc2743 100%);\\n  color: #fff;\\n  cursor: pointer;\\n  transition: background 0.2s, transform 0.2s;\\n  box-shadow: 0 2px 8px rgba(220, 39, 67, 0.08);\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(90deg, #dc2743 0%, #e6683c 100%);\\n  transform: translateY(-2px) scale(1.04);\\n}\\n\\n.add-story-modal-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: #e0e0e0 !important;\\n  color: #333 !important;\\n  box-shadow: none !important;\\n  border: 1px solid #ccc !important;\\n  transition: background 0.2s, color 0.2s;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #cacaca !important;\\n  color: #111 !important;\\n}\\n\\n.stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #dbdbdb;\\n  padding: 16px 0;\\n  margin-bottom: 0;\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.stories-slider[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: transform 0.2s ease;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ViewAddStoriesComponent_div_0_Template_button_click_6_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onAddStory", "ViewAddStoriesComponent_div_0_Template_button_click_8_listener", "onAddReel", "ViewAddStoriesComponent_div_0_Template_button_click_10_listener", "showAddModal", "ViewAddStoriesComponent_div_0_Template_button_click_13_listener", "ViewAddStoriesComponent_div_1_div_6_Template_button_click_2_listener", "_r4", "startRecording", "ViewAddStoriesComponent_div_1_div_6_Template_button_click_4_listener", "closeAddReelModal", "ViewAddStoriesComponent_div_1_div_6_Template_button_click_6_listener", "stopRecording", "ɵɵadvance", "ɵɵproperty", "isRecording", "ɵɵtwoWayListener", "ViewAddStoriesComponent_div_1_div_7_Template_textarea_ngModelChange_2_listener", "$event", "_r5", "ɵɵtwoWayBindingSet", "newReelCaption", "ViewAddStoriesComponent_div_1_div_7_Template_button_click_4_listener", "ViewAddStoriesComponent_div_1_div_7_Template_button_click_6_listener", "submitNewReel", "reelPreviewUrl", "ɵɵsanitizeUrl", "ɵɵtwoWayProperty", "isUploadingReel", "ɵɵtextInterpolate", "ViewAddStoriesComponent_div_1_Template_div_click_1_listener", "_r3", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_6_Template", "ViewAddStoriesComponent_div_1_div_7_Template", "ViewAddStoriesComponent_div_2_Template_div_click_1_listener", "_r6", "handlePermissionResponse", "ViewAddStoriesComponent_div_2_Template_button_click_8_listener", "ViewAddStoriesComponent_div_2_Template_button_click_10_listener", "ViewAddStoriesComponent_div_3_Template_div_click_1_listener", "_r7", "showCameraOrGallery", "ViewAddStoriesComponent_div_3_Template_button_click_6_listener", "openCamera", "ViewAddStoriesComponent_div_3_Template_button_click_8_listener", "openGallery", "ViewAddStoriesComponent_div_3_Template_button_click_10_listener", "ViewAddStoriesComponent_div_4_Template_div_click_1_listener", "_r8", "permissionDenied", "ViewAddStoriesComponent_div_4_Template_button_click_8_listener", "ViewAddStoriesComponent_div_5_Template_div_click_1_listener", "_r9", "closeAddStoryModal", "ViewAddStoriesComponent_div_5_Template_form_ngSubmit_5_listener", "submitNewStory", "ViewAddStoriesComponent_div_5_Template_input_change_6_listener", "onStoryFileSelected", "ViewAddStoriesComponent_div_5_Template_textarea_ngModelChange_7_listener", "newStoryCaption", "ViewAddStoriesComponent_div_5_Template_button_click_9_listener", "isUploadingStory", "newStoryFile", "ɵɵtextInterpolate1", "ViewAddStoriesComponent_div_7_div_1_Template", "ɵɵpureFunction0", "_c3", "ViewAddStoriesComponent_div_8_div_11_Template_div_click_0_listener", "i_r12", "_r11", "index", "openStories", "ɵɵstyleProp", "story_r13", "user", "avatar", "username", "ViewAddStoriesComponent_div_8_Template_button_click_1_listener", "_r10", "scrollStoriesLeft", "ViewAddStoriesComponent_div_8_Template_div_click_5_listener", "onAdd", "ViewAddStoriesComponent_div_8_div_11_Template", "ViewAddStoriesComponent_div_8_Template_button_click_12_listener", "scrollStoriesRight", "canScrollStoriesLeft", "stories", "canScrollStoriesRight", "ɵɵclassProp", "i_r15", "currentIndex", "getCurrentStory", "mediaUrl", "caption", "ViewAddStoriesComponent_div_9_div_21_div_1_Template_div_click_0_listener", "product_r17", "_r16", "$implicit", "viewProduct", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_9_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_9_div_4_Template", "ViewAddStoriesComponent_div_9_Template_div_click_5_listener", "_r14", "onStoryClick", "ViewAddStoriesComponent_div_9_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_9_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_9_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_9_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_9_video_18_Template", "ViewAddStoriesComponent_div_9_div_19_Template", "ViewAddStoriesComponent_div_9_div_20_Template", "ViewAddStoriesComponent_div_9_div_21_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "currentUser", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "recordedChunks", "mediaRecorder", "videoStream", "ngOnInit", "loadStories", "setupEventListeners", "currentUser$", "subscribe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "storiesSlider", "nativeElement", "scrollBy", "left", "behavior", "setTimeout", "updateStoriesArrows", "el", "scrollLeft", "scrollWidth", "clientWidth", "ngAfterViewInit", "push", "get", "apiUrl", "next", "response", "success", "storyGroups", "loadFallbackStories", "error", "console", "showStory", "document", "body", "style", "overflow", "pauseAllVideos", "storiesContainer", "classList", "add", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "clickX", "clientX", "windowWidth", "window", "innerWidth", "touches", "updateDragPosition", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getStoryProgress", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "num", "toFixed", "toString", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "product", "navigate", "_id", "story", "products", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stop", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "url", "isReel", "cleanupReelStream", "getTracks", "track", "allow", "input", "setAttribute", "click", "removeAttribute", "file", "target", "files", "_this3", "uploadForm", "storyPayload", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_0_Template", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "ViewAddStoriesComponent_div_3_Template", "ViewAddStoriesComponent_div_4_Template", "ViewAddStoriesComponent_div_5_Template", "ViewAddStoriesComponent_div_7_Template", "ViewAddStoriesComponent_div_8_Template", "ViewAddStoriesComponent_div_9_Template", "ViewAddStoriesComponent_div_10_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "NgForm", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  currentUser: any = null;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // For slider arrows\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n  canScrollStoriesLeft = false;\n  canScrollStoriesRight = false;\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n        next: (response) => {\n          if (response.success && response.storyGroups) {\n            this.stories = response.storyGroups;\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  private update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  private setupEventListeners() {}\n  private removeEventListeners() {}\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  // Handler for Add Story button\n  // Modal state\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  // Reel recording state\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post('/api/stories', reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload reel.');\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post('/api/stories', storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n}\n", "<!-- Add Modal: <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, Post -->\n<div class=\"add-story-modal\" *ngIf=\"showAddModal\">\n  <div class=\"add-story-modal-backdrop\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Create</h2>\n    <div class=\"add-story-modal-actions\">\n      <button (click)=\"onAddStory()\">Create Story</button>\n      <button (click)=\"onAddReel()\">Create Reel</button>\n      <button (click)=\"showAddModal = false\">Create Post</button>\n    </div>\n    <div style=\"margin-top: 16px;\">\n      <button class=\"cancel-btn\" (click)=\"showAddModal = false\">Cancel</button>\n    </div>\n  </div>\n</div>\n\n<!-- Reel Recording Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showAddReelModal\">\n  <div class=\"add-story-modal-backdrop\" (click)=\"closeAddReelModal()\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Create Reel</h2>\n    <video id=\"reel-video\" width=\"320\" height=\"480\" autoplay muted playsinline style=\"background:#000; border-radius:12px; margin-bottom:12px;\"></video>\n    <div *ngIf=\"!reelPreviewUrl\">\n      <div class=\"add-story-modal-actions\">\n        <button (click)=\"startRecording()\" [disabled]=\"isRecording\">Start Recording</button>\n        <button class=\"cancel-btn\" (click)=\"closeAddReelModal()\" [disabled]=\"isRecording\">Cancel</button>\n        <button (click)=\"stopRecording()\" [disabled]=\"!isRecording\">Stop</button>\n      </div>\n    </div>\n    <div *ngIf=\"reelPreviewUrl\">\n      <video [src]=\"reelPreviewUrl\" width=\"320\" height=\"480\" controls style=\"border-radius:12px;\"></video>\n      <textarea placeholder=\"Write a caption...\" [(ngModel)]=\"newReelCaption\" name=\"reelCaption\" rows=\"2\" [disabled]=\"isUploadingReel\"></textarea>\n      <div class=\"add-story-modal-actions\">\n        <button class=\"cancel-btn\" (click)=\"closeAddReelModal()\" [disabled]=\"isUploadingReel\">Cancel</button>\n        <button (click)=\"submitNewReel()\" [disabled]=\"isUploadingReel\">{{ isUploadingReel ? 'Uploading...' : 'Post Reel' }}</button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Permission Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showPermissionModal\">\n  <div class=\"add-story-modal-backdrop\" (click)=\"handlePermissionResponse(false)\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Allow Camera & File Access?</h2>\n    <p>To add a story, we need access to your camera and files. This is required to take a photo/video or select from your gallery.</p>\n    <div class=\"add-story-modal-actions\">\n      <button class=\"cancel-btn\" (click)=\"handlePermissionResponse(false)\">Block</button>\n      <button (click)=\"handlePermissionResponse(true)\">Allow</button>\n    </div>\n  </div>\n</div>\n\n<!-- Camera/Gallery Choice Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showCameraOrGallery\">\n  <div class=\"add-story-modal-backdrop\" (click)=\"showCameraOrGallery = false\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Add Story</h2>\n    <div class=\"add-story-modal-actions\">\n      <button (click)=\"openCamera()\">Open Camera</button>\n      <button (click)=\"openGallery()\">Choose from Gallery</button>\n      <button class=\"cancel-btn\" (click)=\"showCameraOrGallery = false\">Cancel</button>\n    </div>\n  </div>\n</div>\n\n<!-- Permission Denied Message -->\n<div class=\"add-story-modal\" *ngIf=\"permissionDenied\">\n  <div class=\"add-story-modal-backdrop\" (click)=\"permissionDenied = false\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Permission Denied</h2>\n    <p>You blocked camera and file access. You cannot add a story unless you allow access.</p>\n    <div class=\"add-story-modal-actions\">\n      <button class=\"cancel-btn\" (click)=\"permissionDenied = false\">Close</button>\n    </div>\n  </div>\n</div>\n\n<!-- Add Story Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showAddStoryModal\">\n  <div class=\"add-story-modal-backdrop\" (click)=\"closeAddStoryModal()\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Add Story</h2>\n    <form (ngSubmit)=\"submitNewStory()\" autocomplete=\"off\">\n      <input id=\"story-file-input\" type=\"file\" accept=\"image/*,video/*\" (change)=\"onStoryFileSelected($event)\" [disabled]=\"isUploadingStory\" required style=\"display:none;\" />\n      <textarea placeholder=\"Write a caption...\" [(ngModel)]=\"newStoryCaption\" name=\"caption\" rows=\"2\" [disabled]=\"isUploadingStory\"></textarea>\n      <div class=\"add-story-modal-actions\">\n        <button class=\"cancel-btn\" type=\"button\" (click)=\"closeAddStoryModal()\" [disabled]=\"isUploadingStory\">Cancel</button>\n        <button type=\"submit\" [disabled]=\"!newStoryFile || isUploadingStory\">\n          {{ isUploadingStory ? 'Uploading...' : 'Post Story' }}\n        </button>\n      </div>\n    </form>\n  </div>\n</div>\n\n<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Slider with Arrows -->\n  <div class=\"stories-slider-wrapper\" *ngIf=\"!isLoadingStories\">\n    <button class=\"arrow left\" (click)=\"scrollStoriesLeft()\" [disabled]=\"!canScrollStoriesLeft\">&#8592;</button>\n    <div class=\"stories-slider\" #storiesSlider>\n      <!-- Add Button (opens Add Modal) -->\n      <div class=\"story-item add-story-item\" (click)=\"onAdd()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar add-story-avatar\">\n            <i class=\"fas fa-plus\"></i>\n          </div>\n        </div>\n        <div class=\"story-username\">Add</div>\n      </div>\n      <div\n        *ngFor=\"let story of stories; let i = index\"\n        class=\"story-item\"\n        (click)=\"openStories(i)\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar\" [style.background-image]=\"'url(' + story.user.avatar + ')'\" ></div>\n          <div class=\"story-ring\"></div>\n        </div>\n        <div class=\"story-username\">{{ story.user.username }}</div>\n      </div>\n    </div>\n    <button class=\"arrow right\" (click)=\"scrollStoriesRight()\" [disabled]=\"!canScrollStoriesRight\">&#8594;</button>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\" ></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\">\n          <button class=\"ecommerce-btn buy-now-btn\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;ICL1DC,EAAA,CAAAC,cAAA,aAAkD;IAChDD,EAAA,CAAAE,SAAA,cAA4C;IAE1CF,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEbJ,EADF,CAAAC,cAAA,cAAqC,iBACJ;IAAvBD,EAAA,CAAAK,UAAA,mBAAAC,+DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAACZ,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpDJ,EAAA,CAAAC,cAAA,iBAA8B;IAAtBD,EAAA,CAAAK,UAAA,mBAAAQ,+DAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAK,SAAA,EAAW;IAAA,EAAC;IAACd,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClDJ,EAAA,CAAAC,cAAA,kBAAuC;IAA/BD,EAAA,CAAAK,UAAA,mBAAAU,gEAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAO,YAAA,GAAwB,KAAK;IAAA,EAAC;IAAChB,EAAA,CAAAG,MAAA,mBAAW;IACpDH,EADoD,CAAAI,YAAA,EAAS,EACvD;IAEJJ,EADF,CAAAC,cAAA,eAA+B,kBAC6B;IAA/BD,EAAA,CAAAK,UAAA,mBAAAY,gEAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAO,YAAA,GAAwB,KAAK;IAAA,EAAC;IAAChB,EAAA,CAAAG,MAAA,cAAM;IAGtEH,EAHsE,CAAAI,YAAA,EAAS,EACrE,EACF,EACF;;;;;;IAUEJ,EAFJ,CAAAC,cAAA,UAA6B,cACU,iBACyB;IAApDD,EAAA,CAAAK,UAAA,mBAAAa,qEAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,cAAA,EAAgB;IAAA,EAAC;IAA0BpB,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpFJ,EAAA,CAAAC,cAAA,iBAAkF;IAAvDD,EAAA,CAAAK,UAAA,mBAAAgB,qEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,iBAAA,EAAmB;IAAA,EAAC;IAA0BtB,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACjGJ,EAAA,CAAAC,cAAA,iBAA4D;IAApDD,EAAA,CAAAK,UAAA,mBAAAkB,qEAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,aAAA,EAAe;IAAA,EAAC;IAA2BxB,EAAA,CAAAG,MAAA,WAAI;IAEpEH,EAFoE,CAAAI,YAAA,EAAS,EACrE,EACF;;;;IAJiCJ,EAAA,CAAAyB,SAAA,GAAwB;IAAxBzB,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAAkB,WAAA,CAAwB;IACF3B,EAAA,CAAAyB,SAAA,GAAwB;IAAxBzB,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAAkB,WAAA,CAAwB;IAC/C3B,EAAA,CAAAyB,SAAA,GAAyB;IAAzBzB,EAAA,CAAA0B,UAAA,cAAAjB,MAAA,CAAAkB,WAAA,CAAyB;;;;;;IAG/D3B,EAAA,CAAAC,cAAA,UAA4B;IAC1BD,EAAA,CAAAE,SAAA,gBAAoG;IACpGF,EAAA,CAAAC,cAAA,mBAAiI;IAAtFD,EAAA,CAAA4B,gBAAA,2BAAAC,+EAAAC,MAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAgC,kBAAA,CAAAvB,MAAA,CAAAwB,cAAA,EAAAH,MAAA,MAAArB,MAAA,CAAAwB,cAAA,GAAAH,MAAA;MAAA,OAAA9B,EAAA,CAAAW,WAAA,CAAAmB,MAAA;IAAA,EAA4B;IAA0D9B,EAAA,CAAAI,YAAA,EAAW;IAE1IJ,EADF,CAAAC,cAAA,cAAqC,iBACmD;IAA3DD,EAAA,CAAAK,UAAA,mBAAA6B,qEAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,iBAAA,EAAmB;IAAA,EAAC;IAA8BtB,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACrGJ,EAAA,CAAAC,cAAA,iBAA+D;IAAvDD,EAAA,CAAAK,UAAA,mBAAA8B,qEAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,aAAA,EAAe;IAAA,EAAC;IAA8BpC,EAAA,CAAAG,MAAA,GAAoD;IAEvHH,EAFuH,CAAAI,YAAA,EAAS,EACxH,EACF;;;;IANGJ,EAAA,CAAAyB,SAAA,EAAsB;IAAtBzB,EAAA,CAAA0B,UAAA,QAAAjB,MAAA,CAAA4B,cAAA,EAAArC,EAAA,CAAAsC,aAAA,CAAsB;IACctC,EAAA,CAAAyB,SAAA,EAA4B;IAA5BzB,EAAA,CAAAuC,gBAAA,YAAA9B,MAAA,CAAAwB,cAAA,CAA4B;IAA6BjC,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAA+B,eAAA,CAA4B;IAErExC,EAAA,CAAAyB,SAAA,GAA4B;IAA5BzB,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAA+B,eAAA,CAA4B;IACnDxC,EAAA,CAAAyB,SAAA,GAA4B;IAA5BzB,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAA+B,eAAA,CAA4B;IAACxC,EAAA,CAAAyB,SAAA,EAAoD;IAApDzB,EAAA,CAAAyC,iBAAA,CAAAhC,MAAA,CAAA+B,eAAA,gCAAoD;;;;;;IAhBzHxC,EADF,CAAAC,cAAA,aAAsD,cACgB;IAA9BD,EAAA,CAAAK,UAAA,mBAAAqC,4DAAA;MAAA1C,EAAA,CAAAO,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,iBAAA,EAAmB;IAAA,EAAC;IAACtB,EAAA,CAAAI,YAAA,EAAM;IAExEJ,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpBJ,EAAA,CAAAE,SAAA,gBAAoJ;IAQpJF,EAPA,CAAA4C,UAAA,IAAAC,4CAAA,kBAA6B,IAAAC,4CAAA,kBAOD;IAShC9C,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAhBIJ,EAAA,CAAAyB,SAAA,GAAqB;IAArBzB,EAAA,CAAA0B,UAAA,UAAAjB,MAAA,CAAA4B,cAAA,CAAqB;IAOrBrC,EAAA,CAAAyB,SAAA,EAAoB;IAApBzB,EAAA,CAAA0B,UAAA,SAAAjB,MAAA,CAAA4B,cAAA,CAAoB;;;;;;IAa5BrC,EADF,CAAAC,cAAA,aAAyD,cACyB;IAA1CD,EAAA,CAAAK,UAAA,mBAAA0C,4DAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,GAAA;MAAA,MAAAvC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwC,wBAAA,CAAyB,KAAK,CAAC;IAAA,EAAC;IAACjD,EAAA,CAAAI,YAAA,EAAM;IAEpFJ,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,kCAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mIAA4H;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjIJ,EADF,CAAAC,cAAA,cAAqC,iBACkC;IAA1CD,EAAA,CAAAK,UAAA,mBAAA6C,+DAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAAyC,GAAA;MAAA,MAAAvC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwC,wBAAA,CAAyB,KAAK,CAAC;IAAA,EAAC;IAACjD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACnFJ,EAAA,CAAAC,cAAA,kBAAiD;IAAzCD,EAAA,CAAAK,UAAA,mBAAA8C,gEAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAAyC,GAAA;MAAA,MAAAvC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwC,wBAAA,CAAyB,IAAI,CAAC;IAAA,EAAC;IAACjD,EAAA,CAAAG,MAAA,aAAK;IAG5DH,EAH4D,CAAAI,YAAA,EAAS,EAC3D,EACF,EACF;;;;;;IAIJJ,EADF,CAAAC,cAAA,aAAyD,cACqB;IAAtCD,EAAA,CAAAK,UAAA,mBAAA+C,4DAAA;MAAApD,EAAA,CAAAO,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAA6C,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAACtD,EAAA,CAAAI,YAAA,EAAM;IAEhFJ,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEhBJ,EADF,CAAAC,cAAA,cAAqC,iBACJ;IAAvBD,EAAA,CAAAK,UAAA,mBAAAkD,+DAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+C,UAAA,EAAY;IAAA,EAAC;IAACxD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACnDJ,EAAA,CAAAC,cAAA,iBAAgC;IAAxBD,EAAA,CAAAK,UAAA,mBAAAoD,+DAAA;MAAAzD,EAAA,CAAAO,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiD,WAAA,EAAa;IAAA,EAAC;IAAC1D,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5DJ,EAAA,CAAAC,cAAA,kBAAiE;IAAtCD,EAAA,CAAAK,UAAA,mBAAAsD,gEAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAA6C,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAACtD,EAAA,CAAAG,MAAA,cAAM;IAG7EH,EAH6E,CAAAI,YAAA,EAAS,EAC5E,EACF,EACF;;;;;;IAIJJ,EADF,CAAAC,cAAA,aAAsD,cACqB;IAAnCD,EAAA,CAAAK,UAAA,mBAAAuD,4DAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAqD,gBAAA,GAA4B,KAAK;IAAA,EAAC;IAAC9D,EAAA,CAAAI,YAAA,EAAM;IAE7EJ,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0FAAmF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAExFJ,EADF,CAAAC,cAAA,cAAqC,iBAC2B;IAAnCD,EAAA,CAAAK,UAAA,mBAAA0D,+DAAA;MAAA/D,EAAA,CAAAO,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAqD,gBAAA,GAA4B,KAAK;IAAA,EAAC;IAAC9D,EAAA,CAAAG,MAAA,YAAK;IAGzEH,EAHyE,CAAAI,YAAA,EAAS,EACxE,EACF,EACF;;;;;;IAIJJ,EADF,CAAAC,cAAA,aAAuD,cACgB;IAA/BD,EAAA,CAAAK,UAAA,mBAAA2D,4DAAA;MAAAhE,EAAA,CAAAO,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyD,kBAAA,EAAoB;IAAA,EAAC;IAAClE,EAAA,CAAAI,YAAA,EAAM;IAEzEJ,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClBJ,EAAA,CAAAC,cAAA,eAAuD;IAAjDD,EAAA,CAAAK,UAAA,sBAAA8D,gEAAA;MAAAnE,EAAA,CAAAO,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAA2D,cAAA,EAAgB;IAAA,EAAC;IACjCpE,EAAA,CAAAC,cAAA,gBAAwK;IAAtGD,EAAA,CAAAK,UAAA,oBAAAgE,+DAAAvC,MAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA6D,mBAAA,CAAAxC,MAAA,CAA2B;IAAA,EAAC;IAAxG9B,EAAA,CAAAI,YAAA,EAAwK;IACxKJ,EAAA,CAAAC,cAAA,mBAA+H;IAApFD,EAAA,CAAA4B,gBAAA,2BAAA2C,yEAAAzC,MAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAgC,kBAAA,CAAAvB,MAAA,CAAA+D,eAAA,EAAA1C,MAAA,MAAArB,MAAA,CAAA+D,eAAA,GAAA1C,MAAA;MAAA,OAAA9B,EAAA,CAAAW,WAAA,CAAAmB,MAAA;IAAA,EAA6B;IAAuD9B,EAAA,CAAAI,YAAA,EAAW;IAExIJ,EADF,CAAAC,cAAA,cAAqC,iBACmE;IAA7DD,EAAA,CAAAK,UAAA,mBAAAoE,+DAAA;MAAAzE,EAAA,CAAAO,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyD,kBAAA,EAAoB;IAAA,EAAC;IAA+BlE,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACrHJ,EAAA,CAAAC,cAAA,kBAAqE;IACnED,EAAA,CAAAG,MAAA,IACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;IAVyGJ,EAAA,CAAAyB,SAAA,GAA6B;IAA7BzB,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAAiE,gBAAA,CAA6B;IAC3F1E,EAAA,CAAAyB,SAAA,EAA6B;IAA7BzB,EAAA,CAAAuC,gBAAA,YAAA9B,MAAA,CAAA+D,eAAA,CAA6B;IAAyBxE,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAAiE,gBAAA,CAA6B;IAEpD1E,EAAA,CAAAyB,SAAA,GAA6B;IAA7BzB,EAAA,CAAA0B,UAAA,aAAAjB,MAAA,CAAAiE,gBAAA,CAA6B;IAC/E1E,EAAA,CAAAyB,SAAA,GAA8C;IAA9CzB,EAAA,CAAA0B,UAAA,cAAAjB,MAAA,CAAAkE,YAAA,IAAAlE,MAAA,CAAAiE,gBAAA,CAA8C;IAClE1E,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAA4E,kBAAA,MAAAnE,MAAA,CAAAiE,gBAAA,sCACF;;;;;IAUJ1E,EAAA,CAAAC,cAAA,cAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAI,YAAA,EAAM;;;;;IAJRJ,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAA4C,UAAA,IAAAiC,4CAAA,kBAA6D;IAI/D7E,EAAA,CAAAI,YAAA,EAAM;;;IAJkBJ,EAAA,CAAAyB,SAAA,EAAc;IAAdzB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAA8E,eAAA,IAAAC,GAAA,EAAc;;;;;;IAmBlC/E,EAAA,CAAAC,cAAA,cAG2B;IAAzBD,EAAA,CAAAK,UAAA,mBAAA2E,mEAAA;MAAA,MAAAC,KAAA,GAAAjF,EAAA,CAAAO,aAAA,CAAA2E,IAAA,EAAAC,KAAA;MAAA,MAAA1E,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2E,WAAA,CAAAH,KAAA,CAAc;IAAA,EAAC;IACxBjF,EAAA,CAAAC,cAAA,cAAoC;IAElCD,EADA,CAAAE,SAAA,cAA6F,cAC/D;IAChCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IACvDH,EADuD,CAAAI,YAAA,EAAM,EACvD;;;;IAJwBJ,EAAA,CAAAyB,SAAA,GAA2D;IAA3DzB,EAAA,CAAAqF,WAAA,8BAAAC,SAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAG3DxF,EAAA,CAAAyB,SAAA,GAAyB;IAAzBzB,EAAA,CAAAyC,iBAAA,CAAA6C,SAAA,CAAAC,IAAA,CAAAE,QAAA,CAAyB;;;;;;IAnBzDzF,EADF,CAAAC,cAAA,cAA8D,iBACgC;IAAjED,EAAA,CAAAK,UAAA,mBAAAqF,+DAAA;MAAA1F,EAAA,CAAAO,aAAA,CAAAoF,IAAA;MAAA,MAAAlF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmF,iBAAA,EAAmB;IAAA,EAAC;IAAoC5F,EAAA,CAAAG,MAAA,aAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAG1GJ,EAFF,CAAAC,cAAA,iBAA2C,cAEgB;IAAlBD,EAAA,CAAAK,UAAA,mBAAAwF,4DAAA;MAAA7F,EAAA,CAAAO,aAAA,CAAAoF,IAAA;MAAA,MAAAlF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqF,KAAA,EAAO;IAAA,EAAC;IAEpD9F,EADF,CAAAC,cAAA,cAAoC,cACS;IACzCD,EAAA,CAAAE,SAAA,YAA2B;IAE/BF,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,WAAG;IACjCH,EADiC,CAAAI,YAAA,EAAM,EACjC;IACNJ,EAAA,CAAA4C,UAAA,KAAAmD,6CAAA,kBAG2B;IAO7B/F,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,kBAA+F;IAAnED,EAAA,CAAAK,UAAA,mBAAA2F,gEAAA;MAAAhG,EAAA,CAAAO,aAAA,CAAAoF,IAAA;MAAA,MAAAlF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwF,kBAAA,EAAoB;IAAA,EAAC;IAAqCjG,EAAA,CAAAG,MAAA,cAAO;IACxGH,EADwG,CAAAI,YAAA,EAAS,EAC3G;;;;IAvBqDJ,EAAA,CAAAyB,SAAA,EAAkC;IAAlCzB,EAAA,CAAA0B,UAAA,cAAAjB,MAAA,CAAAyF,oBAAA,CAAkC;IAYrElG,EAAA,CAAAyB,SAAA,IAAY;IAAZzB,EAAA,CAAA0B,UAAA,YAAAjB,MAAA,CAAA0F,OAAA,CAAY;IAUyBnG,EAAA,CAAAyB,SAAA,EAAmC;IAAnCzB,EAAA,CAAA0B,UAAA,cAAAjB,MAAA,CAAA2F,qBAAA,CAAmC;;;;;IAS5FpG,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAI,YAAA,EAAM;;;;;IAFJJ,EADA,CAAAqG,WAAA,WAAAC,KAAA,KAAA7F,MAAA,CAAA8F,YAAA,CAAmC,cAAAD,KAAA,GAAA7F,MAAA,CAAA8F,YAAA,CACC;;;;;IA0BpCvG,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAA0B,UAAA,QAAAjB,MAAA,CAAA+F,eAAA,GAAAC,QAAA,EAAAzG,EAAA,CAAAsC,aAAA,CAAkC;;;;;IAOpCtC,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAqF,WAAA,8BAAA5E,MAAA,CAAA+F,eAAA,GAAAC,QAAA,OAAoE;;;;;IAGtEzG,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAA4E,kBAAA,MAAAnE,MAAA,CAAA+F,eAAA,GAAAE,OAAA,MACF;;;;;;IAGE1G,EAAA,CAAAC,cAAA,cAGiC;IAA/BD,EAAA,CAAAK,UAAA,mBAAAsG,yEAAA;MAAA,MAAAC,WAAA,GAAA5G,EAAA,CAAAO,aAAA,CAAAsG,IAAA,EAAAC,SAAA;MAAA,MAAArG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsG,WAAA,CAAAH,WAAA,CAAoB;IAAA,EAAC;IAC9B5G,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAG,MAAA,yBAAG;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAErCJ,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACtDJ,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEnEH,EAFmE,CAAAI,YAAA,EAAM,EACjE,EACF;;;;;IAH4BJ,EAAA,CAAAyB,SAAA,GAAkB;IAAlBzB,EAAA,CAAAyC,iBAAA,CAAAmE,WAAA,CAAAI,IAAA,CAAkB;IACjBhH,EAAA,CAAAyB,SAAA,GAAgC;IAAhCzB,EAAA,CAAAyC,iBAAA,CAAAhC,MAAA,CAAAwG,WAAA,CAAAL,WAAA,CAAAM,KAAA,EAAgC;;;;;IARrElH,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAA4C,UAAA,IAAAuE,mDAAA,kBAGiC;IAOnCnH,EAAA,CAAAI,YAAA,EAAM;;;;IATkBJ,EAAA,CAAAyB,SAAA,EAAqB;IAArBzB,EAAA,CAAA0B,UAAA,YAAAjB,MAAA,CAAA2G,gBAAA,GAAqB;;;;;;IArDjDpH,EAHJ,CAAAC,cAAA,cAAqE,iBAC5B,cAET;IAC1BD,EAAA,CAAA4C,UAAA,IAAAyE,4CAAA,kBAIuC;IAGzCrH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAK,UAAA,mBAAAiH,4DAAAxF,MAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+G,YAAA,CAAA1F,MAAA,CAAoB;IAAA,EAAC,wBAAA2F,iEAAA3F,MAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAChBF,MAAA,CAAAiH,YAAA,CAAA5F,MAAA,CAAoB;IAAA,EAAC,uBAAA6F,gEAAA7F,MAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACtBF,MAAA,CAAAmH,WAAA,CAAA9F,MAAA,CAAmB;IAAA,EAAC,sBAAA+F,+DAAA/F,MAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACrBF,MAAA,CAAAqH,UAAA,CAAAhG,MAAA,CAAkB;IAAA,EAAC;IAGhC9B,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAA0G;IAC1GF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpEJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,IAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC5EJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,IAAiD;IAC7EH,EAD6E,CAAAI,YAAA,EAAM,EAC7E;IACNJ,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAK,UAAA,mBAAA0H,gEAAA;MAAA/H,EAAA,CAAAO,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuH,YAAA,EAAc;IAAA,EAAC;IACnDhI,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAI,YAAA,EAAS,EACL;IAENJ,EAAA,CAAAC,cAAA,eAA4B;IAsB1BD,EApBA,CAAA4C,UAAA,KAAAqF,+CAAA,oBAOc,KAAAC,6CAAA,kBAMyD,KAAAC,6CAAA,kBAGT,KAAAC,6CAAA,kBAIP;IAYzDpI,EAAA,CAAAI,YAAA,EAAM;IAIFJ,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAI,YAAA,EAAS,EACL;IAGJJ,EADF,CAAAC,cAAA,eAAsC,kBACM;IACxCD,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACfH,EADe,CAAAI,YAAA,EAAO,EACb;IACTJ,EAAA,CAAAC,cAAA,kBAA2C;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACd;IACTJ,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAGvBH,EAHuB,CAAAI,YAAA,EAAO,EACjB,EACL,EACF;IAGNJ,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAI,YAAA,EAAM;;;;IAvGuBJ,EAAA,CAAAqG,WAAA,YAAA5F,MAAA,CAAA4H,MAAA,CAAwB;IAK3BrI,EAAA,CAAAyB,SAAA,GAAY;IAAZzB,EAAA,CAAA0B,UAAA,YAAAjB,MAAA,CAAA0F,OAAA,CAAY;IAS7BnG,EAAA,CAAAyB,SAAA,EAAmC;;IAQPzB,EAAA,CAAAyB,SAAA,GAAuE;IAAvEzB,EAAA,CAAAqF,WAAA,8BAAA5E,MAAA,CAAA+F,eAAA,GAAAjB,IAAA,CAAAC,MAAA,OAAuE;IACzExF,EAAA,CAAAyB,SAAA,GAAqC;IAArCzB,EAAA,CAAAyC,iBAAA,CAAAhC,MAAA,CAAA+F,eAAA,GAAAjB,IAAA,CAAA+C,QAAA,CAAqC;IACrCtI,EAAA,CAAAyB,SAAA,GAA6C;IAA7CzB,EAAA,CAAAyC,iBAAA,CAAAhC,MAAA,CAAA8H,UAAA,CAAA9H,MAAA,CAAA+F,eAAA,GAAAgC,SAAA,EAA6C;IAC5CxI,EAAA,CAAAyB,SAAA,GAAiD;IAAjDzB,EAAA,CAAA4E,kBAAA,KAAAnE,MAAA,CAAAgI,YAAA,CAAAhI,MAAA,CAAA+F,eAAA,GAAAkC,KAAA,YAAiD;IAU1E1I,EAAA,CAAAyB,SAAA,GAA6C;IAA7CzB,EAAA,CAAA0B,UAAA,SAAAjB,MAAA,CAAA+F,eAAA,GAAAmC,SAAA,aAA6C;IAU7C3I,EAAA,CAAAyB,SAAA,EAA6C;IAA7CzB,EAAA,CAAA0B,UAAA,SAAAjB,MAAA,CAAA+F,eAAA,GAAAmC,SAAA,aAA6C;IAK1C3I,EAAA,CAAAyB,SAAA,EAA+B;IAA/BzB,EAAA,CAAA0B,UAAA,SAAAjB,MAAA,CAAA+F,eAAA,GAAAE,OAAA,CAA+B;IAI/B1G,EAAA,CAAAyB,SAAA,EAAmB;IAAnBzB,EAAA,CAAA0B,UAAA,SAAAjB,MAAA,CAAAmI,WAAA,GAAmB;IAgDK5I,EAAA,CAAAyB,SAAA,IAA0B;IAA1BzB,EAAA,CAAAqG,WAAA,cAAA5F,MAAA,CAAA4H,MAAA,CAA0B;;;;;IAK9DrI,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IACtBH,EADsB,CAAAI,YAAA,EAAO,EACvB;IAEJJ,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5BJ,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAI,YAAA,EAAM,EACF;;;ADnNN,OAAM,MAAOyI,uBAAuB;EA2BlCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IA3BrB,KAAAC,WAAW,GAAQ,IAAI;IAEvB,KAAA/C,OAAO,GAAY,EAAE;IACrB,KAAAgD,gBAAgB,GAAG,IAAI;IAEvB,KAAA5C,YAAY,GAAG,CAAC;IAChB,KAAA8B,MAAM,GAAG,KAAK;IACd,KAAAe,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAI1C,KAAA5D,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,qBAAqB,GAAG,KAAK;IA6P7B;IACA;IACA,KAAApF,YAAY,GAAG,KAAK;IACpB,KAAA+I,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAA3G,mBAAmB,GAAG,KAAK;IAC3B,KAAAQ,gBAAgB,GAAG,KAAK;IACxB;IACA,KAAAnC,WAAW,GAAG,KAAK;IACnB,KAAAuI,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAA/H,cAAc,GAAkB,IAAI;IACpC,KAAAG,eAAe,GAAG,KAAK;IACvB,KAAAP,cAAc,GAAG,EAAE;IACnB,KAAA0C,YAAY,GAAgB,IAAI;IAChC,KAAAH,eAAe,GAAG,EAAE;IACpB,KAAAE,gBAAgB,GAAG,KAAK;EAzQrB;EAEH2F,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACtB,WAAW,CAACuB,YAAY,CAACC,SAAS,CAAClF,IAAI,IAAG;MAC7C,IAAI,CAAC2D,WAAW,GAAG3D,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAmF,WAAWA,CAAA;IACT,IAAI,CAACZ,aAAa,CAACa,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;EACAlF,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACmF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAAC,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC7EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACApF,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC8E,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC5EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,MAAMO,EAAE,GAAG,IAAI,CAACP,aAAa,CAACC,aAAa;MAC3C,IAAI,CAAC9E,oBAAoB,GAAGoF,EAAE,CAACC,UAAU,GAAG,CAAC;MAC7C,IAAI,CAACnF,qBAAqB,GAAGkF,EAAE,CAACC,UAAU,GAAGD,EAAE,CAACE,WAAW,GAAGF,EAAE,CAACG,WAAW,GAAG,CAAC;;EAEpF;EACAC,eAAeA,CAAA;IACbN,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;EACnD;EAEA;EACAf,WAAWA,CAAA;IACT,IAAI,CAACnB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACW,aAAa,CAAC6B,IAAI,CACrB,IAAI,CAAC3C,IAAI,CAAC4C,GAAG,CAAM,GAAG7L,WAAW,CAAC8L,MAAM,UAAU,CAAC,CAACpB,SAAS,CAAC;MAC5DqB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAAC9F,OAAO,GAAG4F,QAAQ,CAACE,WAAW;SACpC,MAAM;UACL,IAAI,CAACC,mBAAmB,EAAE;;QAE5B,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDgD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEA+C,mBAAmBA,CAAA;IACjB,IAAI,CAAC/F,OAAO,GAAG;MACb;IAAA,CACD;EACH;EAEAf,WAAWA,CAACD,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACoB,YAAY,GAAGpB,KAAK;IACzB,IAAI,CAACkD,MAAM,GAAG,IAAI;IAClB,IAAI,CAACgE,SAAS,CAAClH,KAAK,CAAC;IACrBmH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EACAzE,YAAYA,CAAA;IACV,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACqE,cAAc,EAAE;IACrBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC3B,aAAa,CAAC4B,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhEzB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACuB,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAC3B,aAAa,CAAC4B,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EACAT,SAASA,CAAClH,KAAa;IACrB,IAAI,CAACoB,YAAY,GAAGpB,KAAK;IACzB,IAAI,CAACmE,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAACqD,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC3B,aAAa,CAACwB,KAAK,CAACO,SAAS,GAAG,mBAAmB;;EAE7E;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACzG,YAAY,GAAG,IAAI,CAACJ,OAAO,CAAC8G,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC1D,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC8D,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAAClF,YAAY,EAAE;;EAEvB;EACAmF,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC5G,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACgD,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC8D,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAAClF,YAAY,EAAE;;EAEvB;EAEAoF,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAChF,MAAM,EAAE;IAClB,QAAQgF,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAAChF,YAAY,EAAE;QACnB;;EAEN;EACAR,YAAYA,CAAC6F,KAAiB;IAC5B,IAAI,IAAI,CAACjE,UAAU,EAAE;IACrB,MAAMmE,MAAM,GAAGF,KAAK,CAACG,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACN,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EACAtF,YAAYA,CAAC2F,KAAiB;IAC5B,IAAI,CAAChE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAG4D,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC1C,IAAI,CAAC9D,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EACA7B,WAAWA,CAACyF,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAChE,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAG2D,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC5C,IAAI,CAACK,kBAAkB,EAAE;EAC3B;EACA/F,UAAUA,CAACuF,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAChE,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAMyE,SAAS,GAAG,IAAI,CAACpE,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAMsE,SAAS,GAAGL,MAAM,CAACC,UAAU,GAAG,IAAI,CAAChE,0BAA0B;IACrE,IAAIqE,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACX,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAACzD,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC8D,MAAM,EAAE;;EAEjB;EACQW,kBAAkBA,CAAA;IACxB,MAAMC,SAAS,GAAG,IAAI,CAACpE,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAIwE,SAAS,GAAGJ,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAAChB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC3B,aAAa,CAACwB,KAAK,CAACO,SAAS,GACjD,6BAA6B,IAAI,CAACzD,OAAO,MAAM;;EAErD;EACQ4D,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC9D,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAImE,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC3E,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAM0E,QAAQ,GAAG,IAAI,CAAC1E,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACjD,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAAC8F,SAAS,CAAC6B,QAAQ,CAAC;QACxB,IAAI,CAAC1E,eAAe,GAAG,IAAI;;MAE7B;;IAEF,IAAI,IAAI,CAACmD,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC3B,aAAa,CAACwB,KAAK,CAACO,SAAS,GACjD,6BAA6B,IAAI,CAACzD,OAAO,MAAM;;IAEnD6E,qBAAqB,CAAC,MAAM,IAAI,CAACjB,MAAM,EAAE,CAAC;EAC5C;EACQR,cAAcA,CAAA;IACpB,MAAM0B,MAAM,GAAG9B,QAAQ,CAAC+B,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAACzD,OAAO,CAAE2D,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EACQhE,mBAAmBA,CAAA,GAAI;EACvBO,oBAAoBA,CAAA,GAAI;EAChCtE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACL,OAAO,CAAC,IAAI,CAACI,YAAY,CAAC;EACxC;EACAiI,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACjI,YAAY,GAAG,CAAC,IAAI,IAAI,CAACJ,OAAO,CAAC8G,MAAM,GAAI,GAAG;EAC9D;EACA1E,UAAUA,CAACkG,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGb,IAAI,CAACc,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGhB,IAAI,CAACc,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGjB,IAAI,CAACc,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EACAxG,YAAYA,CAACyG,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EACAnI,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAImI,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpC9C,KAAK,EAAE,UAAU;MACjB+C,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACvI,KAAK,CAAC;EAClB;EACAH,WAAWA,CAAC2I,OAAY;IACtB,IAAI,CAAC3G,MAAM,CAAC4G,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EACAhH,WAAWA,CAAA;IACT,MAAMiH,KAAK,GAAG,IAAI,CAACrJ,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEqJ,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAAC7C,MAAM,GAAG,CAAC,CAAC;EACjE;EACA7F,gBAAgBA,CAAA;IACd,MAAMyI,KAAK,GAAG,IAAI,CAACrJ,eAAe,EAAE;IACpC,OAAOqJ,KAAK,EAAEC,QAAQ,IAAI,EAAE;EAC9B;EAoBAhK,KAAKA,CAAA;IACH,IAAI,CAAC9E,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC+I,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC3G,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACQ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACa,YAAY,GAAG,IAAI;IACxB,IAAI,CAACH,eAAe,GAAG,EAAE;IACzB,IAAI,CAACnC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACJ,cAAc,GAAG,EAAE;EAC1B;EACArB,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,KAAK;IACzB,IAAI,CAACiJ,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACnG,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACiG,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACzG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACqB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACH,eAAe,GAAG,EAAE;EAC3B;EACA1D,SAASA,CAAA;IACP,IAAI,CAACE,YAAY,GAAG,KAAK;IACzB,IAAI,CAACgJ,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC3H,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACJ,cAAc,GAAG,EAAE;IACxBmJ,UAAU,CAAC,MAAM,IAAI,CAAC2E,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAAC5F,WAAW,SAAS8F,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAE9B,KAAK,EAAE,IAAI;UAAE+B,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAM/B,KAAK,GAAQhC,QAAQ,CAACgE,cAAc,CAAC,YAAY,CAAC;QACxD,IAAIhC,KAAK,EAAE;UACTA,KAAK,CAACiC,SAAS,GAAGP,KAAI,CAAC5F,WAAW;UAClCkE,KAAK,CAACkC,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAAChG,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACA5I,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACgJ,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKuD,MAAc,CAACiD,aAAa,CAAC,IAAI,CAACvG,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAACyG,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC7G,cAAc,CAACyB,IAAI,CAACkF,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAAC3G,aAAa,CAAC6G,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAChH,cAAc,EAAE;QAAEiH,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAAC9O,cAAc,GAAG+O,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAAC9G,aAAa,CAACmH,KAAK,EAAE;IAC1B,IAAI,CAAC3P,WAAW,GAAG,IAAI;EACzB;EACAH,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC2I,aAAa,IAAI,IAAI,CAACxI,WAAW,EAAE;MAC1C,IAAI,CAACwI,aAAa,CAACoH,IAAI,EAAE;MACzB,IAAI,CAAC5P,WAAW,GAAG,KAAK;;EAE5B;EACMS,aAAaA,CAAA;IAAA,IAAAoP,MAAA;IAAA,OAAAvB,iBAAA;MACjB,IAAI,CAACuB,MAAI,CAACnP,cAAc,EAAE;MAC1BmP,MAAI,CAAChP,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAMyO,IAAI,GAAG,IAAIC,IAAI,CAACM,MAAI,CAACtH,cAAc,EAAE;UAAEiH,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMM,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEV,IAAI,EAAE,WAAW,CAAC;QAC3C,MAAMW,SAAS,SAAcJ,MAAI,CAACxI,IAAI,CAAC6I,IAAI,CAAC,qBAAqB,EAAEJ,QAAQ,CAAC,CAACK,SAAS,EAAE;QACxF,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLb,IAAI,EAAES,SAAS,CAACT,IAAI;YACpBc,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDvL,OAAO,EAAE8K,MAAI,CAACvP,cAAc;UAC5BiQ,MAAM,EAAE;SACT;QACD,MAAMV,MAAI,CAACxI,IAAI,CAAC6I,IAAI,CAAC,cAAc,EAAEE,WAAW,CAAC,CAACD,SAAS,EAAE;QAC7DN,MAAI,CAACxH,gBAAgB,GAAG,KAAK;QAC7BwH,MAAI,CAAClH,WAAW,EAAE;OACnB,CAAC,OAAOmG,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;OAChC,SAAS;QACRc,MAAI,CAAChP,eAAe,GAAG,KAAK;QAC5BgP,MAAI,CAACW,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC/H,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACgI,SAAS,EAAE,CAACzH,OAAO,CAAC0H,KAAK,IAAIA,KAAK,CAACd,IAAI,EAAE,CAAC;MAC3D,IAAI,CAACnH,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACxI,WAAW,GAAG,KAAK;IACxB,IAAI,CAACU,cAAc,GAAG,IAAI;EAC5B;EACAf,iBAAiBA,CAAA;IACf,IAAI,CAAC0I,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACmI,iBAAiB,EAAE;EAC1B;EACAlP,wBAAwBA,CAACqP,KAAc;IACrC,IAAI,CAACrI,mBAAmB,GAAG,KAAK;IAChC,IAAIqI,KAAK,EAAE;MACT,IAAI,CAAChP,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACQ,gBAAgB,GAAG,IAAI;;EAEhC;EACAN,UAAUA,CAAA;IACR,IAAI,CAACF,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACyG,iBAAiB,GAAG,IAAI;IAC7BqB,UAAU,CAAC,MAAK;MACd,MAAMmH,KAAK,GAAQjG,QAAQ,CAACgE,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIiC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACA/O,WAAWA,CAAA;IACT,IAAI,CAACJ,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACyG,iBAAiB,GAAG,IAAI;IAC7BqB,UAAU,CAAC,MAAK;MACd,MAAMmH,KAAK,GAAQjG,QAAQ,CAACgE,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIiC,KAAK,EAAE;QACTA,KAAK,CAACG,eAAe,CAAC,SAAS,CAAC;QAChCH,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAnO,mBAAmBA,CAAC+I,KAAU;IAC5B,MAAMsF,IAAI,GAAGtF,KAAK,CAACuF,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAAChO,YAAY,GAAGgO,IAAI;;EAE5B;EACMvO,cAAcA,CAAA;IAAA,IAAA0O,MAAA;IAAA,OAAA7C,iBAAA;MAClB,IAAI,CAAC6C,MAAI,CAACnO,YAAY,EAAE;MACxBmO,MAAI,CAACpO,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAMqO,UAAU,GAAG,IAAIrB,QAAQ,EAAE;QACjCqB,UAAU,CAACpB,MAAM,CAAC,OAAO,EAAEmB,MAAI,CAACnO,YAAY,CAAC;QAC7C,MAAMiN,SAAS,SAAckB,MAAI,CAAC9J,IAAI,CAAC6I,IAAI,CAAC,qBAAqB,EAAEkB,UAAU,CAAC,CAACjB,SAAS,EAAE;QAC1F,MAAMkB,YAAY,GAAG;UACnBhB,KAAK,EAAE;YACLb,IAAI,EAAES,SAAS,CAACT,IAAI;YACpBc,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDvL,OAAO,EAAEoM,MAAI,CAACtO;SACf;QACD,MAAMsO,MAAI,CAAC9J,IAAI,CAAC6I,IAAI,CAAC,cAAc,EAAEmB,YAAY,CAAC,CAAClB,SAAS,EAAE;QAC9DgB,MAAI,CAAC/I,iBAAiB,GAAG,KAAK;QAC9B+I,MAAI,CAACxI,WAAW,EAAE;OACnB,CAAC,OAAOmG,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACRoC,MAAI,CAACpO,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAR,kBAAkBA,CAAA;IAChB,IAAI,CAAC6F,iBAAiB,GAAG,KAAK;EAChC;;;uBA1cWlB,uBAAuB,EAAA7I,EAAA,CAAAiT,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnT,EAAA,CAAAiT,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAArT,EAAA,CAAAiT,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvB1K,uBAAuB;MAAA2K,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvB3T,EAAA,CAAAK,UAAA,qBAAAwT,mDAAA/R,MAAA;YAAA,OAAA8R,GAAA,CAAAxG,aAAA,CAAAtL,MAAA,CAAqB;UAAA,UAAA9B,EAAA,CAAA8T,iBAAA,CAAE;;;;;;;;;;UCwCpC9T,EA9EA,CAAA4C,UAAA,IAAAmR,sCAAA,kBAAkD,IAAAC,sCAAA,iBAgBI,IAAAC,sCAAA,kBAwBG,IAAAC,sCAAA,kBAaA,IAAAC,sCAAA,kBAaH,IAAAC,sCAAA,kBAYC;UAkBvDpU,EAAA,CAAAC,cAAA,aAA+B;UAU7BD,EARA,CAAA4C,UAAA,IAAAyR,sCAAA,iBAAsD,IAAAC,sCAAA,kBAQQ;UAyBhEtU,EAAA,CAAAI,YAAA,EAAM;UA6GNJ,EA1GA,CAAA4C,UAAA,IAAA2R,sCAAA,mBAAqE,KAAAC,uCAAA,iBA0GxB;;;UAhPfxU,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAA5S,YAAA,CAAkB;UAgBlBhB,EAAA,CAAAyB,SAAA,EAAsB;UAAtBzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAA5J,gBAAA,CAAsB;UAwBtBhK,EAAA,CAAAyB,SAAA,EAAyB;UAAzBzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAA3J,mBAAA,CAAyB;UAazBjK,EAAA,CAAAyB,SAAA,EAAyB;UAAzBzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAAtQ,mBAAA,CAAyB;UAazBtD,EAAA,CAAAyB,SAAA,EAAsB;UAAtBzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAA9P,gBAAA,CAAsB;UAYtB9D,EAAA,CAAAyB,SAAA,EAAuB;UAAvBzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAA7J,iBAAA,CAAuB;UAoB7C/J,EAAA,CAAAyB,SAAA,GAAsB;UAAtBzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAAzK,gBAAA,CAAsB;UAQSnJ,EAAA,CAAAyB,SAAA,EAAuB;UAAvBzB,EAAA,CAAA0B,UAAA,UAAAkS,GAAA,CAAAzK,gBAAA,CAAuB;UA4BPnJ,EAAA,CAAAyB,SAAA,EAAY;UAAZzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAAvL,MAAA,CAAY;UA0GpCrI,EAAA,CAAAyB,SAAA,EAAY;UAAZzB,EAAA,CAAA0B,UAAA,SAAAkS,GAAA,CAAAvL,MAAA,CAAY;;;qBD9M/BxI,YAAY,EAAA4U,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7U,WAAW,EAAA8U,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}