{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel'; // ✅ Add this\nlet ViewAddStoriesComponent = class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.slideConfig = {\n      slidesToShow: 5,\n      slidesToScroll: 2,\n      infinite: false,\n      arrows: true,\n      nextArrow: '<button class=\"slick-arrow right\">&#8594;</button>',\n      prevArrow: '<button class=\"slick-arrow left\">&#8592;</button>',\n      responsive: [{\n        breakpoint: 768,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.canScrollStoriesLeft = false;\n    this.canScrollStoriesRight = false;\n    // Handler for Add Story button\n    // Modal state\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    // Reel recording state\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    // setTimeout(() => this.updateStoriesArrows(), 500);\n    setTimeout(() => {\n      this.updateStoriesArrows();\n      if (!this.slickModal) {\n        console.warn('⚠️ slickModal is not initialized yet.');\n      }\n    }, 500);\n  }\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n          console.log('stories', this.stories);\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {}\n  removeEventListeners() {}\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        const uploadRes = yield _this2.http.post('/api/stories/upload', formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post('/api/stories', reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories();\n      } catch (err) {\n        alert('Failed to upload reel.');\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post('/api/stories/upload', uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post('/api/stories', storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  get visibleStories() {\n    if (Array.isArray(this.stories) && this.stories.length > 0 && typeof this.stories[0].stories !== 'undefined') {\n      // Grouped structure: { user, stories: Story[] }[]\n      return this.stories.flatMap(group => Array.isArray(group.stories) ? group.stories : []).filter(s => s.isActive);\n    } else if (Array.isArray(this.stories)) {\n      // Flat array: Story[]\n      return this.stories.filter(s => s.isActive);\n    }\n    return [];\n  }\n};\n__decorate([ViewChild('storiesContainer', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"storiesContainer\", void 0);\n__decorate([ViewChild('feedCover', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"feedCover\", void 0);\n__decorate([ViewChild('slickModal')], ViewAddStoriesComponent.prototype, \"slickModal\", void 0);\n__decorate([ViewChild('storiesSlider', {\n  static: false\n})], ViewAddStoriesComponent.prototype, \"storiesSlider\", void 0);\n__decorate([HostListener('document:keydown', ['$event'])], ViewAddStoriesComponent.prototype, \"handleKeydown\", null);\nViewAddStoriesComponent = __decorate([Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})], ViewAddStoriesComponent);\nexport { ViewAddStoriesComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "HostListener", "CommonModule", "FormsModule", "environment", "SlickCarouselModule", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "currentUser", "slideConfig", "slidesToShow", "slidesToScroll", "infinite", "arrows", "nextArrow", "prevArrow", "responsive", "breakpoint", "settings", "stories", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "canScrollStoriesLeft", "canScrollStoriesRight", "showAddModal", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "showCameraOrGallery", "permissionDenied", "isRecording", "recordedChunks", "mediaRecorder", "videoStream", "reelPreviewUrl", "isUploadingReel", "newReelCaption", "newStoryFile", "newStoryCaption", "isUploadingStory", "ngOnInit", "loadStories", "setupEventListeners", "currentUser$", "subscribe", "user", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "scrollStoriesLeft", "storiesSlider", "nativeElement", "scrollBy", "left", "behavior", "setTimeout", "updateStoriesArrows", "scrollStoriesRight", "el", "scrollLeft", "scrollWidth", "clientWidth", "ngAfterViewInit", "slickModal", "console", "warn", "push", "get", "apiUrl", "next", "response", "success", "storyGroups", "log", "loadFallbackStories", "error", "openStories", "index", "showStory", "document", "body", "style", "overflow", "closeStories", "pauseAllVideos", "storiesContainer", "classList", "add", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "onStoryClick", "clickX", "clientX", "windowWidth", "window", "innerWidth", "onTouchStart", "touches", "onTouchMove", "updateDragPosition", "onTouchEnd", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getCurrentStory", "getStoryProgress", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "formatNumber", "num", "toFixed", "toString", "formatPrice", "price", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "viewProduct", "product", "navigate", "_id", "hasProducts", "story", "products", "getStoryProducts", "onAdd", "onAddStory", "onAddReel", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "startRecording", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stopRecording", "stop", "submitNewReel", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "url", "caption", "isReel", "cleanupReelStream", "getTracks", "track", "closeAddReelModal", "handlePermissionResponse", "allow", "openCamera", "input", "setAttribute", "click", "openGallery", "removeAttribute", "onStoryFileSelected", "file", "target", "files", "submitNewStory", "_this3", "uploadForm", "storyPayload", "closeAddStoryModal", "visibleStories", "Array", "isArray", "flatMap", "group", "filter", "s", "isActive", "__decorate", "static", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts"], "sourcesContent": ["import { Component, On<PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, HostListener } \nfrom '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\nimport { SlickCarouselComponent, SlickCarouselModule } from 'ngx-slick-carousel'; // ✅ Add this\nimport { User } from 'src/app/core/models/user.model';\n\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule ],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n@ViewChild('slickModal') slickModal!: SlickCarouselComponent;\n\n  currentUser: any = null;\nslideConfig = {\n  slidesToShow: 5,\n  slidesToScroll: 2,\n  infinite: false,\n  arrows: true,\n  nextArrow: '<button class=\"slick-arrow right\">&#8594;</button>',\n  prevArrow: '<button class=\"slick-arrow left\">&#8592;</button>',\n  responsive: [\n    {\n      breakpoint: 768,\n      settings: {\n        slidesToShow: 4\n      }\n    },\n    {\n      breakpoint: 480,\n      settings: {\n        slidesToShow: 3\n      }\n    }\n  ]\n};\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // For slider arrows\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n  canScrollStoriesLeft = false;\n  canScrollStoriesRight = false;\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n   // setTimeout(() => this.updateStoriesArrows(), 500);\n    setTimeout(() => {\n    this.updateStoriesArrows();\n    if (!this.slickModal) {\n      console.warn('⚠️ slickModal is not initialized yet.');\n    }\n  }, 500);\n  }\n\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n        next: (response) => {\n          if (response.success && response.storyGroups) {\n            this.stories = response.storyGroups;\n            console.log('stories',this.stories)\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  private update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  private setupEventListeners() {}\n  private removeEventListeners() {}\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  // Handler for Add Story button\n  // Modal state\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  // Reel recording state\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post('/api/stories', reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload reel.');\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post('/api/stories', storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n\n \nget visibleStories(): Story[] {\n  if (\n    Array.isArray(this.stories) &&\n    this.stories.length > 0 &&\n    typeof (this.stories[0] as any).stories !== 'undefined'\n  ) {\n    // Grouped structure: { user, stories: Story[] }[]\n    return (this.stories as any[])\n      .flatMap(group => Array.isArray(group.stories) ? group.stories : [])\n      .filter((s: Story) => s.isActive);\n  } else if (Array.isArray(this.stories)) {\n    // Flat array: Story[]\n    return (this.stories as Story[]).filter(s => s.isActive);\n  }\n  return [];\n}\n\n\n}"], "mappings": ";;AAAA,SAASA,SAAS,EAAiCC,SAAS,EAAEC,YAAY,QACrE,eAAe;AACpB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAAiCC,mBAAmB,QAAQ,oBAAoB,CAAC,CAAC;AAkC3E,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAmDlCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAjDrB,KAAAC,WAAW,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAG;MACZC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,oDAAoD;MAC/DC,SAAS,EAAE,mDAAmD;MAC9DC,UAAU,EAAE,CACV;QACEC,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE;UACRR,YAAY,EAAE;;OAEjB,EACD;QACEO,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE;UACRR,YAAY,EAAE;;OAEjB;KAEJ;IAEC,KAAAS,OAAO,GAAY,EAAE;IACrB,KAAAC,gBAAgB,GAAG,IAAI;IAEvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAI1C,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,qBAAqB,GAAG,KAAK;IAoQ7B;IACA;IACA,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB;IACA,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAG,KAAK;EAhRrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAC/C,WAAW,CAACgD,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACjD,WAAW,GAAGiD,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACzB,aAAa,CAAC0B,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAAC,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC7EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACP,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC5EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,MAAMQ,EAAE,GAAG,IAAI,CAACR,aAAa,CAACC,aAAa;MAC3C,IAAI,CAAC/B,oBAAoB,GAAGsC,EAAE,CAACC,UAAU,GAAG,CAAC;MAC7C,IAAI,CAACtC,qBAAqB,GAAGqC,EAAE,CAACC,UAAU,GAAGD,EAAE,CAACE,WAAW,GAAGF,EAAE,CAACG,WAAW,GAAG,CAAC;;EAEpF;EACAC,eAAeA,CAAA;IACd;IACCP,UAAU,CAAC,MAAK;MAChB,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAAC,IAAI,CAACO,UAAU,EAAE;QACpBC,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACP;EAEA;EACA1B,WAAWA,CAAA;IACT,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,aAAa,CAAC+C,IAAI,CACrB,IAAI,CAAC1E,IAAI,CAAC2E,GAAG,CAAM,GAAGhF,WAAW,CAACiF,MAAM,UAAU,CAAC,CAAC1B,SAAS,CAAC;MAC5D2B,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAACnE,OAAO,GAAGiE,QAAQ,CAACE,WAAW;UACnCR,OAAO,CAACS,GAAG,CAAC,SAAS,EAAC,IAAI,CAACpE,OAAO,CAAC;SACpC,MAAM;UACL,IAAI,CAACqE,mBAAmB,EAAE;;QAE5B,IAAI,CAACpE,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDqE,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAACpE,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEAoE,mBAAmBA,CAAA;IACjB,IAAI,CAACrE,OAAO,GAAG;MACb;IAAA,CACD;EACH;EAEAuE,WAAWA,CAACC,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACtE,YAAY,GAAGsE,KAAK;IACzB,IAAI,CAACrE,MAAM,GAAG,IAAI;IAClB,IAAI,CAACsE,SAAS,CAACD,KAAK,CAAC;IACrBE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EACAC,YAAYA,CAAA;IACV,IAAI,CAAC3E,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC4E,cAAc,EAAE;IACrBL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAClC,aAAa,CAACmC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhEhC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAC8B,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAClC,aAAa,CAACmC,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EACAV,SAASA,CAACD,KAAa;IACrB,IAAI,CAACtE,YAAY,GAAGsE,KAAK;IACzB,IAAI,CAAClE,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAAC0E,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAClC,aAAa,CAAC8B,KAAK,CAACQ,SAAS,GAAG,mBAAmB;;EAE7E;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACnF,YAAY,GAAG,IAAI,CAACF,OAAO,CAACsF,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC/E,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EACAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACtF,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EAEAW,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACvF,MAAM,EAAE;IAClB,QAAQuF,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACP,YAAY,EAAE;QACnB;;EAEN;EACAc,YAAYA,CAACF,KAAiB;IAC5B,IAAI,IAAI,CAACtF,UAAU,EAAE;IACrB,MAAMyF,MAAM,GAAGH,KAAK,CAACI,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACP,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EACAa,YAAYA,CAACR,KAAiB;IAC5B,IAAI,CAACrF,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGiF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC1C,IAAI,CAACpF,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EACA2F,WAAWA,CAACV,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACrF,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGgF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC5C,IAAI,CAACO,kBAAkB,EAAE;EAC3B;EACAC,UAAUA,CAACZ,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAACrF,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAMkG,SAAS,GAAG,IAAI,CAAC7F,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAM+F,SAAS,GAAGR,MAAM,CAACC,UAAU,GAAG,IAAI,CAACtF,0BAA0B;IACrE,IAAI8F,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACf,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAAC9E,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmF,MAAM,EAAE;;EAEjB;EACQc,kBAAkBA,CAAA;IACxB,MAAME,SAAS,GAAG,IAAI,CAAC7F,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAIiG,SAAS,GAAGP,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAACjB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAClC,aAAa,CAAC8B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAAC9E,OAAO,MAAM;;EAErD;EACQiF,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACnF,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAI4F,IAAI,CAACC,GAAG,CAAC,IAAI,CAACpG,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAMmG,QAAQ,GAAG,IAAI,CAACnG,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACN,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAACuE,SAAS,CAACkC,QAAQ,CAAC;QACxB,IAAI,CAACnG,eAAe,GAAG,IAAI;;MAE7B;;IAEF,IAAI,IAAI,CAACwE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAClC,aAAa,CAAC8B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAAC9E,OAAO,MAAM;;IAEnDsG,qBAAqB,CAAC,MAAM,IAAI,CAACrB,MAAM,EAAE,CAAC;EAC5C;EACQR,cAAcA,CAAA;IACpB,MAAM8B,MAAM,GAAGnC,QAAQ,CAACoC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAACrE,OAAO,CAAEuE,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EACQ7E,mBAAmBA,CAAA,GAAI;EACvBQ,oBAAoBA,CAAA,GAAI;EAChCsE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACjH,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC;EACxC;EACAgH,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAAChH,YAAY,GAAG,CAAC,IAAI,IAAI,CAACF,OAAO,CAACsF,MAAM,GAAI,GAAG;EAC9D;EACA6B,UAAUA,CAACC,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGlB,IAAI,CAACgB,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGnB,IAAI,CAACgB,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EACAC,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EACAC,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCxD,KAAK,EAAE,UAAU;MACjByD,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EACAM,WAAWA,CAACC,OAAY;IACtB,IAAI,CAACvJ,MAAM,CAACwJ,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EACAC,WAAWA,CAAA;IACT,MAAMC,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE4B,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACxD,MAAM,GAAG,CAAC,CAAC;EACjE;EACAyD,gBAAgBA,CAAA;IACd,MAAMF,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO4B,KAAK,EAAEC,QAAQ,IAAI,EAAE;EAC9B;EAoBAE,KAAKA,CAAA;IACH,IAAI,CAAC/H,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EACAoH,UAAUA,CAAA;IACR,IAAI,CAAChI,YAAY,GAAG,KAAK;IACzB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EACAmH,SAASA,CAAA;IACP,IAAI,CAACjI,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBqB,UAAU,CAAC,MAAM,IAAI,CAACiG,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAAC1H,WAAW,SAAS4H,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAEzC,KAAK,EAAE,IAAI;UAAE0C,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAM1C,KAAK,GAAQrC,QAAQ,CAACgF,cAAc,CAAC,YAAY,CAAC;QACxD,IAAI3C,KAAK,EAAE;UACTA,KAAK,CAAC4C,SAAS,GAAGP,KAAI,CAAC1H,WAAW;UAClCqF,KAAK,CAAC6C,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAACjI,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACA4I,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrI,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKuE,MAAc,CAACgE,aAAa,CAAC,IAAI,CAACtI,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAACwI,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC5I,cAAc,CAACqC,IAAI,CAACqG,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAAC1I,aAAa,CAAC4I,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAC/I,cAAc,EAAE;QAAEgJ,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAAC7I,cAAc,GAAG8I,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAAC7I,aAAa,CAACkJ,KAAK,EAAE;IAC1B,IAAI,CAACpJ,WAAW,GAAG,IAAI;EACzB;EACAqJ,aAAaA,CAAA;IACX,IAAI,IAAI,CAACnJ,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAACoJ,IAAI,EAAE;MACzB,IAAI,CAACtJ,WAAW,GAAG,KAAK;;EAE5B;EACMuJ,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI,CAAC0B,MAAI,CAACpJ,cAAc,EAAE;MAC1BoJ,MAAI,CAACnJ,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAM0I,IAAI,GAAG,IAAIC,IAAI,CAACQ,MAAI,CAACvJ,cAAc,EAAE;UAAEgJ,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEZ,IAAI,EAAE,WAAW,CAAC;QAC3C,MAAMa,SAAS,SAAcJ,MAAI,CAAC5L,IAAI,CAACiM,IAAI,CAAC,qBAAqB,EAAEJ,QAAQ,CAAC,CAACK,SAAS,EAAE;QACxF,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEV,MAAI,CAAClJ,cAAc;UAC5B6J,MAAM,EAAE;SACT;QACD,MAAMX,MAAI,CAAC5L,IAAI,CAACiM,IAAI,CAAC,cAAc,EAAEE,WAAW,CAAC,CAACD,SAAS,EAAE;QAC7DN,MAAI,CAAC5J,gBAAgB,GAAG,KAAK;QAC7B4J,MAAI,CAAC7I,WAAW,EAAE;OACnB,CAAC,OAAO2H,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;OAChC,SAAS;QACRiB,MAAI,CAACnJ,eAAe,GAAG,KAAK;QAC5BmJ,MAAI,CAACY,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACjK,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACkK,SAAS,EAAE,CAACpJ,OAAO,CAACqJ,KAAK,IAAIA,KAAK,CAAChB,IAAI,EAAE,CAAC;MAC3D,IAAI,CAACnJ,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EACAmK,iBAAiBA,CAAA;IACf,IAAI,CAAC3K,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACwK,iBAAiB,EAAE;EAC1B;EACAI,wBAAwBA,CAACC,KAAc;IACrC,IAAI,CAAC5K,mBAAmB,GAAG,KAAK;IAChC,IAAI4K,KAAK,EAAE;MACT,IAAI,CAAC3K,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEhC;EACA2K,UAAUA,CAAA;IACR,IAAI,CAAC5K,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7BgC,UAAU,CAAC,MAAK;MACd,MAAMgJ,KAAK,GAAQxH,QAAQ,CAACgF,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAC,WAAWA,CAAA;IACT,IAAI,CAAChL,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7BgC,UAAU,CAAC,MAAK;MACd,MAAMgJ,KAAK,GAAQxH,QAAQ,CAACgF,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,mBAAmBA,CAAC7G,KAAU;IAC5B,MAAM8G,IAAI,GAAG9G,KAAK,CAAC+G,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAAC1K,YAAY,GAAG0K,IAAI;;EAE5B;EACMG,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MAClB,IAAI,CAACuD,MAAI,CAAC9K,YAAY,EAAE;MACxB8K,MAAI,CAAC5K,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAM6K,UAAU,GAAG,IAAI5B,QAAQ,EAAE;QACjC4B,UAAU,CAAC3B,MAAM,CAAC,OAAO,EAAE0B,MAAI,CAAC9K,YAAY,CAAC;QAC7C,MAAMqJ,SAAS,SAAcyB,MAAI,CAACzN,IAAI,CAACiM,IAAI,CAAC,qBAAqB,EAAEyB,UAAU,CAAC,CAACxB,SAAS,EAAE;QAC1F,MAAMyB,YAAY,GAAG;UACnBvB,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEmB,MAAI,CAAC7K;SACf;QACD,MAAM6K,MAAI,CAACzN,IAAI,CAACiM,IAAI,CAAC,cAAc,EAAE0B,YAAY,CAAC,CAACzB,SAAS,EAAE;QAC9DuB,MAAI,CAAC1L,iBAAiB,GAAG,KAAK;QAC9B0L,MAAI,CAAC1K,WAAW,EAAE;OACnB,CAAC,OAAO2H,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACR8C,MAAI,CAAC5K,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACA+K,kBAAkBA,CAAA;IAChB,IAAI,CAAC7L,iBAAiB,GAAG,KAAK;EAChC;EAGF,IAAI8L,cAAcA,CAAA;IAChB,IACEC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAClN,OAAO,CAAC,IAC3B,IAAI,CAACA,OAAO,CAACsF,MAAM,GAAG,CAAC,IACvB,OAAQ,IAAI,CAACtF,OAAO,CAAC,CAAC,CAAS,CAACA,OAAO,KAAK,WAAW,EACvD;MACA;MACA,OAAQ,IAAI,CAACA,OAAiB,CAC3BmN,OAAO,CAACC,KAAK,IAAIH,KAAK,CAACC,OAAO,CAACE,KAAK,CAACpN,OAAO,CAAC,GAAGoN,KAAK,CAACpN,OAAO,GAAG,EAAE,CAAC,CACnEqN,MAAM,CAAEC,CAAQ,IAAKA,CAAC,CAACC,QAAQ,CAAC;KACpC,MAAM,IAAIN,KAAK,CAACC,OAAO,CAAC,IAAI,CAAClN,OAAO,CAAC,EAAE;MACtC;MACA,OAAQ,IAAI,CAACA,OAAmB,CAACqN,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC;;IAE1D,OAAO,EAAE;EACX;CAGC;AA7fmDC,UAAA,EAAjD9O,SAAS,CAAC,kBAAkB,EAAE;EAAE+O,MAAM,EAAE;AAAK,CAAE,CAAC,C,gEAA+B;AACrCD,UAAA,EAA1C9O,SAAS,CAAC,WAAW,EAAE;EAAE+O,MAAM,EAAE;AAAK,CAAE,CAAC,C,yDAAwB;AAC3CD,UAAA,EAAxB9O,SAAS,CAAC,YAAY,CAAC,C,0DAAqC;AA4CZ8O,UAAA,EAA9C9O,SAAS,CAAC,eAAe,EAAE;EAAE+O,MAAM,EAAE;AAAK,CAAE,CAAC,C,6DAA4C;AAiI1FD,UAAA,EADC7O,YAAY,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,2DAc5C;AA7LUK,uBAAuB,GAAAwO,UAAA,EAPnC/O,SAAS,CAAC;EACTiP,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChP,YAAY,EAAEC,WAAW,EAAEE,mBAAmB,CAAE;EAC1D8O,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC;CAChD,CAAC,C,EACW9O,uBAAuB,CA8fnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}