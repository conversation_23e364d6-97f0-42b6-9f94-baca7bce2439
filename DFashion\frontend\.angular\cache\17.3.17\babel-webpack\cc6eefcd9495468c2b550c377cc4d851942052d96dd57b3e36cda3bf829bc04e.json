{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n  constructor() {\n    super(...arguments);\n    this._polling = false;\n  }\n  get name() {\n    return \"polling\";\n  }\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @protected\n   */\n  doOpen() {\n    this._poll();\n  }\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n   * @package\n   */\n  pause(onPause) {\n    this.readyState = \"pausing\";\n    const pause = () => {\n      this.readyState = \"paused\";\n      onPause();\n    };\n    if (this._polling || !this.writable) {\n      let total = 0;\n      if (this._polling) {\n        total++;\n        this.once(\"pollComplete\", function () {\n          --total || pause();\n        });\n      }\n      if (!this.writable) {\n        total++;\n        this.once(\"drain\", function () {\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n  /**\n   * Starts polling cycle.\n   *\n   * @private\n   */\n  _poll() {\n    this._polling = true;\n    this.doPoll();\n    this.emitReserved(\"poll\");\n  }\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @protected\n   */\n  onData(data) {\n    const callback = packet => {\n      // if its the first message we consider the transport open\n      if (\"opening\" === this.readyState && packet.type === \"open\") {\n        this.onOpen();\n      }\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        this.onClose({\n          description: \"transport closed by the server\"\n        });\n        return false;\n      }\n      // otherwise bypass onData and handle the message\n      this.onPacket(packet);\n    };\n    // decode payload\n    decodePayload(data, this.socket.binaryType).forEach(callback);\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this._polling = false;\n      this.emitReserved(\"pollComplete\");\n      if (\"open\" === this.readyState) {\n        this._poll();\n      } else {}\n    }\n  }\n  /**\n   * For polling, send a close packet.\n   *\n   * @protected\n   */\n  doClose() {\n    const close = () => {\n      this.write([{\n        type: \"close\"\n      }]);\n    };\n    if (\"open\" === this.readyState) {\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n      this.once(\"open\", close);\n    }\n  }\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} packets - data packets\n   * @protected\n   */\n  write(packets) {\n    this.writable = false;\n    encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emitReserved(\"drain\");\n      });\n    });\n  }\n  /**\n   * Generates uri for connection.\n   *\n   * @private\n   */\n  uri() {\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    const query = this.query || {};\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = randomString();\n    }\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n    return this.createUri(schema, query);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}