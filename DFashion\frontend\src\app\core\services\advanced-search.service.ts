import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface SearchQuery {
  query: string;
  category?: string;
  subcategory?: string;
  targetGender?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  brand?: string;
  sortBy?: 'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest';
  filters?: {
    availability?: string[];
    occasion?: string[];
    material?: string[];
    size?: string[];
    color?: string[];
  };
}

export interface SearchResult {
  products: any[];
  totalCount: number;
  suggestions: string[];
  categoryRedirect?: {
    shouldRedirect: boolean;
    categories: CategoryOption[];
    originalQuery: string;
  };
  searchMetadata: {
    queryType: 'specific' | 'ambiguous' | 'category';
    processingTime: number;
    appliedFilters: any;
  };
}

export interface CategoryOption {
  id: string;
  name: string;
  description: string;
  productCount: number;
  targetGender: string;
  image: string;
  category?: string;
  subcategory?: string;
  avgPrice?: number;
}

export interface SearchSuggestion {
  text: string;
  type: 'product' | 'category' | 'brand' | 'keyword';
  count?: number;
}

@Injectable({
  providedIn: 'root'
})
export class AdvancedSearchService {
  private readonly API_URL = `${environment.apiUrl}/search`;
  
  // Search state management
  private searchHistorySubject = new BehaviorSubject<string[]>([]);
  private recentSearchesSubject = new BehaviorSubject<string[]>([]);
  private trendingSearchesSubject = new BehaviorSubject<string[]>([]);
  
  public searchHistory$ = this.searchHistorySubject.asObservable();
  public recentSearches$ = this.recentSearchesSubject.asObservable();
  public trendingSearches$ = this.trendingSearchesSubject.asObservable();

  // Query patterns for intelligent detection
  private readonly queryPatterns = {
    ambiguous: [
      /^kurts?$/i,
      /^shirts?$/i,
      /^pants?$/i,
      /^dresses?$/i,
      /^shoes?$/i,
      /^bags?$/i,
      /^watches?$/i,
      /^jewelry$/i
    ],
    specific: [
      /kurtas?\s+for\s+(men|boys|male)/i,
      /kurtas?\s+for\s+(women|girls|female)/i,
      /(men|boys|male)\s+kurtas?/i,
      /(women|girls|female)\s+kurtas?/i,
      /(men|boys|male)\s+shirts?/i,
      /(women|girls|female)\s+dresses?/i
    ],
    genderSpecific: [
      /(men|boys|male|gents)/i,
      /(women|girls|female|ladies)/i
    ]
  };

  constructor(private http: HttpClient) {
    this.loadSearchHistory();
    this.loadTrendingSearches();
  }

  /**
   * Main search method with intelligent query processing
   */
  search(searchQuery: SearchQuery): Observable<SearchResult> {
    const startTime = Date.now();
    
    // Analyze query type
    const queryAnalysis = this.analyzeQuery(searchQuery.query);
    
    // Handle ambiguous queries that need category selection
    if (queryAnalysis.isAmbiguous && !searchQuery.category) {
      return this.handleAmbiguousQuery(searchQuery.query, startTime);
    }
    
    // Process specific queries
    if (queryAnalysis.isSpecific) {
      searchQuery = this.enhanceSpecificQuery(searchQuery, queryAnalysis);
    }
    
    // Perform the actual search
    return this.performSearch(searchQuery, startTime);
  }

  /**
   * Get search suggestions as user types
   */
  getSuggestions(query: string): Observable<SearchSuggestion[]> {
    if (!query || query.length < 2) {
      return of([]);
    }

    const params = new HttpParams().set('q', query).set('type', 'suggestions');
    
    return this.http.get<any>(`${this.API_URL}/suggestions`, { params }).pipe(
      map(response => response.suggestions || []),
      catchError(() => of(this.getFallbackSuggestions(query)))
    );
  }

  /**
   * Analyze query to determine type and intent
   */
  private analyzeQuery(query: string): any {
    const normalizedQuery = query.toLowerCase().trim();
    
    const analysis = {
      isAmbiguous: false,
      isSpecific: false,
      detectedGender: null as string | null,
      detectedCategory: null as string | null,
      detectedSubcategory: null as string | null,
      confidence: 0
    };

    // Check for ambiguous patterns
    for (const pattern of this.queryPatterns.ambiguous) {
      if (pattern.test(normalizedQuery)) {
        analysis.isAmbiguous = true;
        analysis.detectedCategory = this.extractCategoryFromPattern(normalizedQuery);
        break;
      }
    }

    // Check for specific patterns
    for (const pattern of this.queryPatterns.specific) {
      const match = pattern.exec(normalizedQuery);
      if (match) {
        analysis.isSpecific = true;
        analysis.detectedGender = this.extractGender(match[1] || match[2]);
        analysis.detectedCategory = this.extractCategoryFromQuery(normalizedQuery);
        analysis.confidence = 0.9;
        break;
      }
    }

    // Check for gender-specific terms
    for (const pattern of this.queryPatterns.genderSpecific) {
      const match = pattern.exec(normalizedQuery);
      if (match) {
        analysis.detectedGender = this.extractGender(match[1]);
        break;
      }
    }

    return analysis;
  }

  /**
   * Handle ambiguous queries by providing category options
   */
  private handleAmbiguousQuery(query: string, startTime: number): Observable<SearchResult> {
    const categories = this.getCategoryOptionsForQuery(query);
    
    return of({
      products: [],
      totalCount: 0,
      suggestions: [],
      categoryRedirect: {
        shouldRedirect: true,
        categories: categories,
        originalQuery: query
      },
      searchMetadata: {
        queryType: 'ambiguous',
        processingTime: Date.now() - startTime,
        appliedFilters: {}
      }
    });
  }

  /**
   * Enhance specific queries with detected parameters
   */
  private enhanceSpecificQuery(searchQuery: SearchQuery, analysis: any): SearchQuery {
    const enhanced = { ...searchQuery };
    
    if (analysis.detectedGender) {
      enhanced.targetGender = analysis.detectedGender;
      enhanced.category = analysis.detectedGender === 'men' ? 'men' : 'women';
    }
    
    if (analysis.detectedCategory) {
      enhanced.subcategory = analysis.detectedCategory;
    }
    
    return enhanced;
  }

  /**
   * Perform the actual search API call
   */
  private performSearch(searchQuery: SearchQuery, startTime: number): Observable<SearchResult> {
    let params = new HttpParams();
    
    // Add query parameters
    if (searchQuery.query) params = params.set('q', searchQuery.query);
    if (searchQuery.category) params = params.set('category', searchQuery.category);
    if (searchQuery.subcategory) params = params.set('subcategory', searchQuery.subcategory);
    if (searchQuery.targetGender) params = params.set('targetGender', searchQuery.targetGender);
    if (searchQuery.brand) params = params.set('brand', searchQuery.brand);
    if (searchQuery.sortBy) params = params.set('sortBy', searchQuery.sortBy);
    
    // Add price range
    if (searchQuery.priceRange) {
      params = params.set('minPrice', searchQuery.priceRange.min.toString());
      params = params.set('maxPrice', searchQuery.priceRange.max.toString());
    }
    
    // Add filters
    if (searchQuery.filters) {
      Object.keys(searchQuery.filters).forEach(key => {
        const values = searchQuery.filters![key as keyof typeof searchQuery.filters];
        if (values && values.length > 0) {
          params = params.set(key, values.join(','));
        }
      });
    }

    return this.http.get<any>(`${this.API_URL}/products`, { params }).pipe(
      map(response => ({
        products: response.products || [],
        totalCount: response.totalCount || 0,
        suggestions: response.suggestions || [],
        searchMetadata: {
          queryType: 'specific' as 'specific',
          processingTime: Date.now() - startTime,
          appliedFilters: searchQuery.filters || {}
        }
      })),
      catchError(() => of({
        products: [],
        totalCount: 0,
        suggestions: [],
        searchMetadata: {
          queryType: 'specific' as 'specific',
          processingTime: Date.now() - startTime,
          appliedFilters: {}
        }
      }))
    );
  }

  /**
   * Get category options for ambiguous queries
   */
  private getCategoryOptionsForQuery(query: string): CategoryOption[] {
    const normalizedQuery = query.toLowerCase();
    
    if (normalizedQuery.includes('kurt')) {
      return [
        {
          id: 'men-kurtas',
          name: 'Men\'s Kurtas',
          description: 'Traditional and modern kurtas for men',
          productCount: 45,
          targetGender: 'men',
          image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300'
        },
        {
          id: 'women-kurtis',
          name: 'Women\'s Kurtis',
          description: 'Stylish kurtis and kurtas for women',
          productCount: 67,
          targetGender: 'women',
          image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300'
        }
      ];
    }
    
    // Add more category mappings as needed
    return [];
  }

  /**
   * Extract gender from query text
   */
  private extractGender(genderText: string): string {
    const normalized = genderText.toLowerCase();
    if (['men', 'boys', 'male', 'gents'].includes(normalized)) {
      return 'men';
    }
    if (['women', 'girls', 'female', 'ladies'].includes(normalized)) {
      return 'women';
    }
    return 'unisex';
  }

  /**
   * Extract category from query pattern
   */
  private extractCategoryFromPattern(query: string): string {
    if (query.includes('kurt')) return 'kurtas';
    if (query.includes('shirt')) return 'shirts';
    if (query.includes('pant')) return 'pants';
    if (query.includes('dress')) return 'dresses';
    return '';
  }

  /**
   * Extract category from full query
   */
  private extractCategoryFromQuery(query: string): string {
    if (query.includes('kurta')) return 'kurtas';
    if (query.includes('kurti')) return 'kurtis';
    if (query.includes('shirt')) return 'shirts';
    if (query.includes('pant') || query.includes('trouser')) return 'pants';
    if (query.includes('dress')) return 'dresses';
    return '';
  }

  /**
   * Get fallback suggestions when API fails
   */
  private getFallbackSuggestions(query: string): SearchSuggestion[] {
    const suggestions: SearchSuggestion[] = [];
    
    if (query.toLowerCase().includes('kurt')) {
      suggestions.push(
        { text: 'kurtas for men', type: 'keyword', count: 45 },
        { text: 'kurtas for women', type: 'keyword', count: 67 },
        { text: 'cotton kurtas', type: 'keyword', count: 32 },
        { text: 'silk kurtas', type: 'keyword', count: 18 }
      );
    }
    
    return suggestions;
  }

  /**
   * Save search to history
   */
  saveSearchToHistory(query: string): void {
    const currentHistory = this.searchHistorySubject.value;
    const updatedHistory = [query, ...currentHistory.filter(q => q !== query)].slice(0, 10);
    this.searchHistorySubject.next(updatedHistory);
    localStorage.setItem('dfashion_search_history', JSON.stringify(updatedHistory));
  }

  /**
   * Load search history from storage
   */
  private loadSearchHistory(): void {
    const stored = localStorage.getItem('dfashion_search_history');
    if (stored) {
      try {
        const history = JSON.parse(stored);
        this.searchHistorySubject.next(history);
      } catch (e) {
        console.warn('Failed to load search history');
      }
    }
  }

  /**
   * Load trending searches
   */
  private loadTrendingSearches(): void {
    // This would typically come from an API
    const trending = [
      'kurtas for men',
      'women ethnic wear',
      'cotton shirts',
      'formal dresses',
      'casual wear'
    ];
    this.trendingSearchesSubject.next(trending);
  }

  /**
   * Clear search history
   */
  clearSearchHistory(): void {
    this.searchHistorySubject.next([]);
    localStorage.removeItem('dfashion_search_history');
  }
}
