{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { MatIconButton, MatButtonModule } from '@angular/material/button';\nimport { MatSelect, MatSelectModule } from '@angular/material/select';\nimport { MatTooltip, MatTooltipModule } from '@angular/material/tooltip';\nimport { MatOption } from '@angular/material/core';\nimport { MatFormField } from '@angular/material/form-field';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_Conditional_2_Conditional_3_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r3, \" \");\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 13)(1, \"mat-select\", 15);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1._changePageSize($event.value));\n    });\n    i0.ɵɵrepeaterCreate(2, MatPaginator_Conditional_2_Conditional_3_For_3_Template, 2, 2, \"mat-option\", 16, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1._formFieldAppearance)(\"color\", ctx_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.pageSize)(\"disabled\", ctx_r1.disabled)(\"aria-labelledby\", ctx_r1._pageSizeLabelId)(\"panelClass\", ctx_r1.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r1.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pageSize);\n  }\n}\nfunction MatPaginator_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MatPaginator_Conditional_2_Conditional_3_Template, 4, 7, \"mat-form-field\", 13)(4, MatPaginator_Conditional_2_Conditional_4_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r1._pageSizeLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, ctx_r1._displayedPageSizeOptions.length > 1 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, ctx_r1._displayedPageSizeOptions.length <= 1 ? 4 : -1);\n  }\n}\nfunction MatPaginator_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.firstPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._previousButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.lastPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r1._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._nextButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.lastPageLabel);\n  }\n}\nclass MatPaginatorIntl {\n  constructor() {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    this.changes = new Subject();\n    /** A label for the page size selector. */\n    this.itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    this.nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    this.previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    this.firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    this.lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    this.getRangeLabel = (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n  }\n  static {\n    this.ɵfac = function MatPaginatorIntl_Factory(t) {\n      return new (t || MatPaginatorIntl)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatPaginatorIntl,\n      factory: MatPaginatorIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator {\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  get pageIndex() {\n    return this._pageIndex;\n  }\n  set pageIndex(value) {\n    this._pageIndex = Math.max(value || 0, 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  get length() {\n    return this._length;\n  }\n  set length(value) {\n    this._length = value || 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Number of items to display on a page. By default set to 50. */\n  get pageSize() {\n    return this._pageSize;\n  }\n  set pageSize(value) {\n    this._pageSize = Math.max(value || 0, 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** The set of provided page size options to display to the user. */\n  get pageSizeOptions() {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value) {\n    this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n    this._updateDisplayedPageSizeOptions();\n  }\n  constructor(_intl, _changeDetectorRef, defaults) {\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ID for the DOM node containing the paginator's items per page label. */\n    this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n    this._isInitialized = false;\n    this._initializedStream = new ReplaySubject(1);\n    this._pageIndex = 0;\n    this._length = 0;\n    this._pageSizeOptions = [];\n    /** Whether to hide the page size selection UI from the user. */\n    this.hidePageSize = false;\n    /** Whether to show the first/last buttons UI to the user. */\n    this.showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    this.selectConfig = {};\n    /** Whether the paginator is disabled. */\n    this.disabled = false;\n    /** Event emitted when the paginator changes the page size or page index. */\n    this.page = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    this.initialized = this._initializedStream;\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n    if (defaults) {\n      const {\n        pageSize,\n        pageSizeOptions,\n        hidePageSize,\n        showFirstLastButtons\n      } = defaults;\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n      if (hidePageSize != null) {\n        this.hidePageSize = hidePageSize;\n      }\n      if (showFirstLastButtons != null) {\n        this.showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._initializedStream.next();\n  }\n  ngOnDestroy() {\n    this._initializedStream.complete();\n    this._intlChanges.unsubscribe();\n  }\n  /** Advances to the next page if it exists. */\n  nextPage() {\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex + 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move back to the previous page if it exists. */\n  previousPage() {\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the first page if not already there. */\n  firstPage() {\n    // hasPreviousPage being false implies at the start\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = 0;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the last page if not already there. */\n  lastPage() {\n    // hasNextPage being false implies at the end\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.getNumberOfPages() - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Whether there is a previous page. */\n  hasPreviousPage() {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n  /** Whether there is a next page. */\n  hasNextPage() {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n  /** Calculate the number of pages */\n  getNumberOfPages() {\n    if (!this.pageSize) {\n      return 0;\n    }\n    return Math.ceil(this.length / this.pageSize);\n  }\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  _updateDisplayedPageSizeOptions() {\n    if (!this._isInitialized) {\n      return;\n    }\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  _emitPageEvent(previousPageIndex) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length\n    });\n  }\n  static {\n    this.ɵfac = function MatPaginator_Factory(t) {\n      return new (t || MatPaginator)(i0.ɵɵdirectiveInject(MatPaginatorIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_PAGINATOR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPaginator,\n      selectors: [[\"mat-paginator\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n      inputs: {\n        color: \"color\",\n        pageIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"pageIndex\", \"pageIndex\", numberAttribute],\n        length: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"length\", \"length\", numberAttribute],\n        pageSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"pageSize\", \"pageSize\", numberAttribute],\n        pageSizeOptions: \"pageSizeOptions\",\n        hidePageSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hidePageSize\", \"hidePageSize\", booleanAttribute],\n        showFirstLastButtons: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute],\n        selectConfig: \"selectConfig\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        page: \"page\"\n      },\n      exportAs: [\"matPaginator\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 14,\n      consts: [[1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [1, \"mat-mdc-paginator-page-size-label\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"selectionChange\", \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\"], [3, \"value\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n      template: function MatPaginator_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, MatPaginator_Conditional_2_Template, 5, 4, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatPaginator_Conditional_6_Template, 3, 5, \"button\", 5);\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 7);\n          i0.ɵɵelement(9, \"path\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(11, \"svg\", 7);\n          i0.ɵɵelement(12, \"path\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, MatPaginator_Conditional_13_Template, 3, 5, \"button\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, !ctx.hidePageSize ? 2 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(6, ctx.showFirstLastButtons ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._previousButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._nextButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(13, ctx.showFirstLastButtons ? 13 : -1);\n        }\n      },\n      dependencies: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking);--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginator, [{\n    type: Component,\n    args: [{\n      selector: 'mat-paginator',\n      exportAs: 'matPaginator',\n      host: {\n        'class': 'mat-mdc-paginator',\n        'role': 'group'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"firstPage()\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"lastPage()\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking);--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"]\n    }]\n  }], () => [{\n    type: MatPaginatorIntl\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n    }]\n  }], {\n    color: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    length: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    hidePageSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showFirstLastButtons: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectConfig: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    page: [{\n      type: Output\n    }]\n  });\n})();\nclass MatPaginatorModule {\n  static {\n    this.ɵfac = function MatPaginatorModule_Factory(t) {\n      return new (t || MatPaginatorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPaginatorModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_PAGINATOR_INTL_PROVIDER],\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n      exports: [MatPaginator],\n      providers: [MAT_PAGINATOR_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };", "map": {"version": 3, "names": ["i0", "Injectable", "Optional", "SkipSelf", "InjectionToken", "numberAttribute", "EventEmitter", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "NgModule", "Subject", "ReplaySubject", "MatIconButton", "MatButtonModule", "MatSelect", "MatSelectModule", "MatTooltip", "MatTooltipModule", "MatOption", "MatFormField", "MatPaginator_Conditional_2_Conditional_3_For_3_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "pageSizeOption_r3", "$implicit", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "MatPaginator_Conditional_2_Conditional_3_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_changePageSize", "value", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "_formFieldAppearance", "color", "pageSize", "disabled", "_pageSizeLabelId", "selectConfig", "panelClass", "disableOptionCentering", "ɵɵrepeater", "_displayedPageSizeOptions", "MatPaginator_Conditional_2_Conditional_4_Template", "ɵɵtextInterpolate", "MatPaginator_Conditional_2_Template", "ɵɵtemplate", "ɵɵattribute", "_intl", "itemsPerPageLabel", "ɵɵconditional", "length", "MatPaginator_Conditional_6_Template", "_r4", "MatPaginator_Conditional_6_Template_button_click_0_listener", "firstPage", "ɵɵnamespaceSVG", "ɵɵelement", "firstPageLabel", "_previousButtonsDisabled", "MatPaginator_Conditional_13_Template", "_r5", "MatPaginator_Conditional_13_Template_button_click_0_listener", "lastPage", "lastPageLabel", "_nextButtonsDisabled", "MatPaginatorIntl", "constructor", "changes", "nextPageLabel", "previousPageLabel", "getRangeLabel", "page", "Math", "max", "startIndex", "endIndex", "min", "ɵfac", "MatPaginatorIntl_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "MAT_PAGINATOR_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_PAGINATOR_INTL_PROVIDER", "provide", "deps", "useFactory", "DEFAULT_PAGE_SIZE", "PageEvent", "MAT_PAGINATOR_DEFAULT_OPTIONS", "nextUniqueId", "MatPaginator", "pageIndex", "_pageIndex", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_length", "_pageSize", "_updateDisplayedPageSizeOptions", "pageSizeOptions", "_pageSizeOptions", "map", "p", "defaults", "_isInitialized", "_initializedStream", "hidePageSize", "showFirstLastButtons", "initialized", "_intlChanges", "subscribe", "formFieldAppearance", "ngOnInit", "next", "ngOnDestroy", "complete", "unsubscribe", "nextPage", "hasNextPage", "previousPageIndex", "_emitPageEvent", "previousPage", "hasPreviousPage", "getNumberOfPages", "maxPageIndex", "ceil", "floor", "slice", "indexOf", "push", "sort", "a", "b", "emit", "MatPaginator_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "MatPaginator_Template", "MatPaginator_Template_button_click_7_listener", "ɵɵnamespaceHTML", "MatPaginator_Template_button_click_10_listener", "dependencies", "styles", "encapsulation", "changeDetection", "selector", "host", "OnPush", "None", "imports", "undefined", "decorators", "transform", "MatPaginatorModule", "MatPaginatorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "exports"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@angular/material/fesm2022/paginator.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { MatIconButton, MatButtonModule } from '@angular/material/button';\nimport { MatSelect, MatSelectModule } from '@angular/material/select';\nimport { MatTooltip, MatTooltipModule } from '@angular/material/tooltip';\nimport { MatOption } from '@angular/material/core';\nimport { MatFormField } from '@angular/material/form-field';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nclass MatPaginatorIntl {\n    constructor() {\n        /**\n         * Stream to emit from when labels are changed. Use this to notify components when the labels have\n         * changed after initialization.\n         */\n        this.changes = new Subject();\n        /** A label for the page size selector. */\n        this.itemsPerPageLabel = 'Items per page:';\n        /** A label for the button that increments the current page. */\n        this.nextPageLabel = 'Next page';\n        /** A label for the button that decrements the current page. */\n        this.previousPageLabel = 'Previous page';\n        /** A label for the button that moves to the first page. */\n        this.firstPageLabel = 'First page';\n        /** A label for the button that moves to the last page. */\n        this.lastPageLabel = 'Last page';\n        /** A label for the range of items within the current page and the length of the whole list. */\n        this.getRangeLabel = (page, pageSize, length) => {\n            if (length == 0 || pageSize == 0) {\n                return `0 of ${length}`;\n            }\n            length = Math.max(length, 0);\n            const startIndex = page * pageSize;\n            // If the start index exceeds the list length, do not try and fix the end index to the end.\n            const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n            return `${startIndex + 1} – ${endIndex} of ${length}`;\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatorIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatorIntl, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatorIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n    // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n    provide: MatPaginatorIntl,\n    deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n    useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator {\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n        return this._pageIndex;\n    }\n    set pageIndex(value) {\n        this._pageIndex = Math.max(value || 0, 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n        return this._length;\n    }\n    set length(value) {\n        this._length = value || 0;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n        return this._pageSize;\n    }\n    set pageSize(value) {\n        this._pageSize = Math.max(value || 0, 0);\n        this._updateDisplayedPageSizeOptions();\n    }\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n        return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n        this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n        this._updateDisplayedPageSizeOptions();\n    }\n    constructor(_intl, _changeDetectorRef, defaults) {\n        this._intl = _intl;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** ID for the DOM node containing the paginator's items per page label. */\n        this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n        this._isInitialized = false;\n        this._initializedStream = new ReplaySubject(1);\n        this._pageIndex = 0;\n        this._length = 0;\n        this._pageSizeOptions = [];\n        /** Whether to hide the page size selection UI from the user. */\n        this.hidePageSize = false;\n        /** Whether to show the first/last buttons UI to the user. */\n        this.showFirstLastButtons = false;\n        /** Used to configure the underlying `MatSelect` inside the paginator. */\n        this.selectConfig = {};\n        /** Whether the paginator is disabled. */\n        this.disabled = false;\n        /** Event emitted when the paginator changes the page size or page index. */\n        this.page = new EventEmitter();\n        /** Emits when the paginator is initialized. */\n        this.initialized = this._initializedStream;\n        this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n        if (defaults) {\n            const { pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons } = defaults;\n            if (pageSize != null) {\n                this._pageSize = pageSize;\n            }\n            if (pageSizeOptions != null) {\n                this._pageSizeOptions = pageSizeOptions;\n            }\n            if (hidePageSize != null) {\n                this.hidePageSize = hidePageSize;\n            }\n            if (showFirstLastButtons != null) {\n                this.showFirstLastButtons = showFirstLastButtons;\n            }\n        }\n        this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._updateDisplayedPageSizeOptions();\n        this._initializedStream.next();\n    }\n    ngOnDestroy() {\n        this._initializedStream.complete();\n        this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n        if (!this.hasNextPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.pageIndex + 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n        if (!this.hasPreviousPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.pageIndex - 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n        // hasPreviousPage being false implies at the start\n        if (!this.hasPreviousPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = 0;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n        // hasNextPage being false implies at the end\n        if (!this.hasNextPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.getNumberOfPages() - 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n        return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n        const maxPageIndex = this.getNumberOfPages() - 1;\n        return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n        if (!this.pageSize) {\n            return 0;\n        }\n        return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n        // Current page needs to be updated to reflect the new page size. Navigate to the page\n        // containing the previous page's first item.\n        const startIndex = this.pageIndex * this.pageSize;\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n        this.pageSize = pageSize;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n        return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n        return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n        if (!this._isInitialized) {\n            return;\n        }\n        // If no page size is provided, use the first page size option or the default page size.\n        if (!this.pageSize) {\n            this._pageSize =\n                this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n        }\n        this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n        if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n            this._displayedPageSizeOptions.push(this.pageSize);\n        }\n        // Sort the numbers using a number-specific sort function.\n        this._displayedPageSizeOptions.sort((a, b) => a - b);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n        this.page.emit({\n            previousPageIndex,\n            pageIndex: this.pageIndex,\n            pageSize: this.pageSize,\n            length: this.length,\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginator, deps: [{ token: MatPaginatorIntl }, { token: i0.ChangeDetectorRef }, { token: MAT_PAGINATOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatPaginator, isStandalone: true, selector: \"mat-paginator\", inputs: { color: \"color\", pageIndex: [\"pageIndex\", \"pageIndex\", numberAttribute], length: [\"length\", \"length\", numberAttribute], pageSize: [\"pageSize\", \"pageSize\", numberAttribute], pageSizeOptions: \"pageSizeOptions\", hidePageSize: [\"hidePageSize\", \"hidePageSize\", booleanAttribute], showFirstLastButtons: [\"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute], selectConfig: \"selectConfig\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { page: \"page\" }, host: { attributes: { \"role\": \"group\" }, classAttribute: \"mat-mdc-paginator\" }, exportAs: [\"matPaginator\"], ngImport: i0, template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"firstPage()\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"lastPage()\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking);--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"], dependencies: [{ kind: \"component\", type: MatFormField, selector: \"mat-form-field\", inputs: [\"hideRequiredMarker\", \"color\", \"floatLabel\", \"appearance\", \"subscriptSizing\", \"hintLabel\"], exportAs: [\"matFormField\"] }, { kind: \"component\", type: MatSelect, selector: \"mat-select\", inputs: [\"aria-describedby\", \"panelClass\", \"disabled\", \"disableRipple\", \"tabIndex\", \"hideSingleSelectionIndicator\", \"placeholder\", \"required\", \"multiple\", \"disableOptionCentering\", \"compareWith\", \"value\", \"aria-label\", \"aria-labelledby\", \"errorStateMatcher\", \"typeaheadDebounceInterval\", \"sortComparator\", \"id\", \"panelWidth\"], outputs: [\"openedChange\", \"opened\", \"closed\", \"selectionChange\", \"valueChange\"], exportAs: [\"matSelect\"] }, { kind: \"component\", type: MatOption, selector: \"mat-option\", inputs: [\"value\", \"id\", \"disabled\"], outputs: [\"onSelectionChange\"], exportAs: [\"matOption\"] }, { kind: \"component\", type: MatIconButton, selector: \"button[mat-icon-button]\", exportAs: [\"matButton\"] }, { kind: \"directive\", type: MatTooltip, selector: \"[matTooltip]\", inputs: [\"matTooltipPosition\", \"matTooltipPositionAtOrigin\", \"matTooltipDisabled\", \"matTooltipShowDelay\", \"matTooltipHideDelay\", \"matTooltipTouchGestures\", \"matTooltip\", \"matTooltipClass\"], exportAs: [\"matTooltip\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginator, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-paginator', exportAs: 'matPaginator', host: {\n                        'class': 'mat-mdc-paginator',\n                        'role': 'group',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip], template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"firstPage()\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"lastPage()\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking);--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"] }]\n        }], ctorParameters: () => [{ type: MatPaginatorIntl }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n                }] }], propDecorators: { color: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], length: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], pageSize: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], pageSizeOptions: [{\n                type: Input\n            }], hidePageSize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], showFirstLastButtons: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectConfig: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], page: [{\n                type: Output\n            }] } });\n\nclass MatPaginatorModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatorModule, imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator], exports: [MatPaginator] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatorModule, providers: [MAT_PAGINATOR_INTL_PROVIDER], imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n                    exports: [MatPaginator],\n                    providers: [MAT_PAGINATOR_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,eAAe,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACvN,SAASC,OAAO,EAAEC,aAAa,QAAQ,MAAM;AAC7C,SAASC,aAAa,EAAEC,eAAe,QAAQ,0BAA0B;AACzE,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,UAAU,EAAEC,gBAAgB,QAAQ,2BAA2B;AACxE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AAHA,SAAAC,wDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAiCoG1B,EAAE,CAAA4B,cAAA,oBAmO+uD,CAAC;IAnOlvD5B,EAAE,CAAA6B,MAAA,EAmOuyD,CAAC;IAnO1yD7B,EAAE,CAAA8B,YAAA,CAmOozD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,iBAAA,GAAAJ,GAAA,CAAAK,SAAA;IAnOvzDhC,EAAE,CAAAiC,UAAA,UAAAF,iBAmO8uD,CAAC;IAnOjvD/B,EAAE,CAAAkC,SAAA,CAmOuyD,CAAC;IAnO1yDlC,EAAE,CAAAmC,kBAAA,MAAAJ,iBAAA,KAmOuyD,CAAC;EAAA;AAAA;AAAA,SAAAK,kDAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAW,GAAA,GAnO1yDrC,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAA4B,cAAA,wBAmOosC,CAAC,oBAAuZ,CAAC;IAnO/lD5B,EAAE,CAAAuC,UAAA,6BAAAC,wFAAAC,MAAA;MAAFzC,EAAE,CAAA0C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CAmOihDF,MAAA,CAAAG,eAAA,CAAAL,MAAA,CAAAM,KAA4B,CAAC;IAAA,CAAC,CAAC;IAnOljD/C,EAAE,CAAAgD,gBAAA,IAAAvB,uDAAA,0BAAFzB,EAAE,CAAAiD,yBAmOq0D,CAAC;IAnOx0DjD,EAAE,CAAA8B,YAAA,CAmOg2D,CAAC,CAA4B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAnOh4D3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAiC,UAAA,eAAAU,MAAA,CAAAO,oBAmO0mC,CAAC,UAAAP,MAAA,CAAAQ,KAA8B,CAAC;IAnO5oCnD,EAAE,CAAAkC,SAAA,CAmOiwC,CAAC;IAnOpwClC,EAAE,CAAAiC,UAAA,UAAAU,MAAA,CAAAS,QAmOiwC,CAAC,aAAAT,MAAA,CAAAU,QAAsC,CAAC,oBAAAV,MAAA,CAAAW,gBAAqD,CAAC,eAAAX,MAAA,CAAAY,YAAA,CAAAC,UAAA,MAA6D,CAAC,2BAAAb,MAAA,CAAAY,YAAA,CAAAE,sBAA+E,CAAC;IAnO/+CzD,EAAE,CAAAkC,SAAA,CAmOq0D,CAAC;IAnOx0DlC,EAAE,CAAA0D,UAAA,CAAAf,MAAA,CAAAgB,yBAmOq0D,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnOx0D1B,EAAE,CAAA4B,cAAA,aAmO8/D,CAAC;IAnOjgE5B,EAAE,CAAA6B,MAAA,EAmO0gE,CAAC;IAnO7gE7B,EAAE,CAAA8B,YAAA,CAmOghE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAnOnhE3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAkC,SAAA,CAmO0gE,CAAC;IAnO7gElC,EAAE,CAAA6D,iBAAA,CAAAlB,MAAA,CAAAS,QAmO0gE,CAAC;EAAA;AAAA;AAAA,SAAAU,oCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnO7gE1B,EAAE,CAAA4B,cAAA,YAmOk1B,CAAC,aAAyF,CAAC;IAnO/6B5B,EAAE,CAAA6B,MAAA,EAmO69B,CAAC;IAnOh+B7B,EAAE,CAAA8B,YAAA,CAmOm+B,CAAC;IAnOt+B9B,EAAE,CAAA+D,UAAA,IAAA3B,iDAAA,4BAmO2hC,CAAC,IAAAwB,iDAAA,iBAAq6B,CAAC;IAnOp8D5D,EAAE,CAAA8B,YAAA,CAmOyiE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAnO5iE3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAkC,SAAA,CAmO26B,CAAC;IAnO96BlC,EAAE,CAAAgE,WAAA,OAAArB,MAAA,CAAAW,gBAAA;IAAFtD,EAAE,CAAAkC,SAAA,CAmO69B,CAAC;IAnOh+BlC,EAAE,CAAAmC,kBAAA,MAAAQ,MAAA,CAAAsB,KAAA,CAAAC,iBAAA,KAmO69B,CAAC;IAnOh+BlE,EAAE,CAAAkC,SAAA,CAmOw4D,CAAC;IAnO34DlC,EAAE,CAAAmE,aAAA,IAAAxB,MAAA,CAAAgB,yBAAA,CAAAS,MAAA,aAmOw4D,CAAC;IAnO34DpE,EAAE,CAAAkC,SAAA,CAmO2hE,CAAC;IAnO9hElC,EAAE,CAAAmE,aAAA,IAAAxB,MAAA,CAAAgB,yBAAA,CAAAS,MAAA,cAmO2hE,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4C,GAAA,GAnO9hEtE,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAA4B,cAAA,gBAmOiuF,CAAC;IAnOpuF5B,EAAE,CAAAuC,UAAA,mBAAAgC,4DAAA;MAAFvE,EAAE,CAAA0C,aAAA,CAAA4B,GAAA;MAAA,MAAA3B,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CAmO+6EF,MAAA,CAAA6B,SAAA,CAAU,CAAC;IAAA,CAAC,CAAC;IAnO97ExE,EAAE,CAAAyE,cAAA;IAAFzE,EAAE,CAAA4B,cAAA,YAmO+3F,CAAC;IAnOl4F5B,EAAE,CAAA0E,SAAA,cAmOq9F,CAAC;IAnOx9F1E,EAAE,CAAA8B,YAAA,CAmOu+F,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAnO7/F3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAiC,UAAA,eAAAU,MAAA,CAAAsB,KAAA,CAAAU,cAmO8iF,CAAC,uBAAAhC,MAAA,CAAAiC,wBAAA,EAAoE,CAAC,8BAAiD,CAAC,aAAAjC,MAAA,CAAAiC,wBAAA,EAA0D,CAAC;IAnOnuF5E,EAAE,CAAAgE,WAAA,eAAArB,MAAA,CAAAsB,KAAA,CAAAU,cAAA;EAAA;AAAA;AAAA,SAAAE,qCAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAoD,GAAA,GAAF9E,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAA4B,cAAA,gBAmOwzJ,CAAC;IAnO3zJ5B,EAAE,CAAAuC,UAAA,mBAAAwC,6DAAA;MAAF/E,EAAE,CAAA0C,aAAA,CAAAoC,GAAA;MAAA,MAAAnC,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CAmOihJF,MAAA,CAAAqC,QAAA,CAAS,CAAC;IAAA,CAAC,CAAC;IAnO/hJhF,EAAE,CAAAyE,cAAA;IAAFzE,EAAE,CAAA4B,cAAA,YAmOs9J,CAAC;IAnOz9J5B,EAAE,CAAA0E,SAAA,cAmO4iK,CAAC;IAnO/iK1E,EAAE,CAAA8B,YAAA,CAmO8jK,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAnOplK3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAiC,UAAA,eAAAU,MAAA,CAAAsB,KAAA,CAAAgB,aAmO6oJ,CAAC,uBAAAtC,MAAA,CAAAuC,oBAAA,EAAgE,CAAC,8BAAiD,CAAC,aAAAvC,MAAA,CAAAuC,oBAAA,EAAsD,CAAC;IAnO1zJlF,EAAE,CAAAgE,WAAA,eAAArB,MAAA,CAAAsB,KAAA,CAAAgB,aAAA;EAAA;AAAA;AA7BtG,MAAME,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAItE,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACmD,iBAAiB,GAAG,iBAAiB;IAC1C;IACA,IAAI,CAACoB,aAAa,GAAG,WAAW;IAChC;IACA,IAAI,CAACC,iBAAiB,GAAG,eAAe;IACxC;IACA,IAAI,CAACZ,cAAc,GAAG,YAAY;IAClC;IACA,IAAI,CAACM,aAAa,GAAG,WAAW;IAChC;IACA,IAAI,CAACO,aAAa,GAAG,CAACC,IAAI,EAAErC,QAAQ,EAAEgB,MAAM,KAAK;MAC7C,IAAIA,MAAM,IAAI,CAAC,IAAIhB,QAAQ,IAAI,CAAC,EAAE;QAC9B,OAAO,QAAQgB,MAAM,EAAE;MAC3B;MACAA,MAAM,GAAGsB,IAAI,CAACC,GAAG,CAACvB,MAAM,EAAE,CAAC,CAAC;MAC5B,MAAMwB,UAAU,GAAGH,IAAI,GAAGrC,QAAQ;MAClC;MACA,MAAMyC,QAAQ,GAAGD,UAAU,GAAGxB,MAAM,GAAGsB,IAAI,CAACI,GAAG,CAACF,UAAU,GAAGxC,QAAQ,EAAEgB,MAAM,CAAC,GAAGwB,UAAU,GAAGxC,QAAQ;MACtG,OAAO,GAAGwC,UAAU,GAAG,CAAC,MAAMC,QAAQ,OAAOzB,MAAM,EAAE;IACzD,CAAC;EACL;EACA;IAAS,IAAI,CAAC2B,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFd,gBAAgB;IAAA,CAAoD;EAAE;EAChL;IAAS,IAAI,CAACe,KAAK,kBAD6ElG,EAAE,CAAAmG,kBAAA;MAAAC,KAAA,EACYjB,gBAAgB;MAAAkB,OAAA,EAAhBlB,gBAAgB,CAAAY,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGvG,EAAE,CAAAwG,iBAAA,CAGXrB,gBAAgB,EAAc,CAAC;IAC9GsB,IAAI,EAAExG,UAAU;IAChByG,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASK,mCAAmCA,CAACC,UAAU,EAAE;EACrD,OAAOA,UAAU,IAAI,IAAIzB,gBAAgB,CAAC,CAAC;AAC/C;AACA;AACA,MAAM0B,2BAA2B,GAAG;EAChC;EACAC,OAAO,EAAE3B,gBAAgB;EACzB4B,IAAI,EAAE,CAAC,CAAC,IAAI7G,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEgF,gBAAgB,CAAC,CAAC;EAC1D6B,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA,MAAMM,iBAAiB,GAAG,EAAE;AAC5B;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;AAEhB;AACA,MAAMC,6BAA6B,GAAG,IAAI/G,cAAc,CAAC,+BAA+B,CAAC;AACzF,IAAIgH,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACf;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACvE,KAAK,EAAE;IACjB,IAAI,CAACwE,UAAU,GAAG7B,IAAI,CAACC,GAAG,CAAC5C,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;IACzC,IAAI,CAACyE,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAIrD,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACsD,OAAO;EACvB;EACA,IAAItD,MAAMA,CAACrB,KAAK,EAAE;IACd,IAAI,CAAC2E,OAAO,GAAG3E,KAAK,IAAI,CAAC;IACzB,IAAI,CAACyE,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAIrE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuE,SAAS;EACzB;EACA,IAAIvE,QAAQA,CAACL,KAAK,EAAE;IAChB,IAAI,CAAC4E,SAAS,GAAGjC,IAAI,CAACC,GAAG,CAAC5C,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI,CAAC6E,+BAA+B,CAAC,CAAC;EAC1C;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC9E,KAAK,EAAE;IACvB,IAAI,CAAC+E,gBAAgB,GAAG,CAAC/E,KAAK,IAAI,EAAE,EAAEgF,GAAG,CAACC,CAAC,IAAI3H,eAAe,CAAC2H,CAAC,EAAE,CAAC,CAAC,CAAC;IACrE,IAAI,CAACJ,+BAA+B,CAAC,CAAC;EAC1C;EACAxC,WAAWA,CAACnB,KAAK,EAAEuD,kBAAkB,EAAES,QAAQ,EAAE;IAC7C,IAAI,CAAChE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuD,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAAClE,gBAAgB,GAAG,iCAAiC8D,YAAY,EAAE,EAAE;IACzE,IAAI,CAACc,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,kBAAkB,GAAG,IAAInH,aAAa,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACuG,UAAU,GAAG,CAAC;IACnB,IAAI,CAACG,OAAO,GAAG,CAAC;IAChB,IAAI,CAACI,gBAAgB,GAAG,EAAE;IAC1B;IACA,IAAI,CAACM,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC;IACA,IAAI,CAAC9E,YAAY,GAAG,CAAC,CAAC;IACtB;IACA,IAAI,CAACF,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACoC,IAAI,GAAG,IAAInF,YAAY,CAAC,CAAC;IAC9B;IACA,IAAI,CAACgI,WAAW,GAAG,IAAI,CAACH,kBAAkB;IAC1C,IAAI,CAACI,YAAY,GAAGtE,KAAK,CAACoB,OAAO,CAACmD,SAAS,CAAC,MAAM,IAAI,CAAChB,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IACzF,IAAIQ,QAAQ,EAAE;MACV,MAAM;QAAE7E,QAAQ;QAAEyE,eAAe;QAAEO,YAAY;QAAEC;MAAqB,CAAC,GAAGJ,QAAQ;MAClF,IAAI7E,QAAQ,IAAI,IAAI,EAAE;QAClB,IAAI,CAACuE,SAAS,GAAGvE,QAAQ;MAC7B;MACA,IAAIyE,eAAe,IAAI,IAAI,EAAE;QACzB,IAAI,CAACC,gBAAgB,GAAGD,eAAe;MAC3C;MACA,IAAIO,YAAY,IAAI,IAAI,EAAE;QACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;MACpC;MACA,IAAIC,oBAAoB,IAAI,IAAI,EAAE;QAC9B,IAAI,CAACA,oBAAoB,GAAGA,oBAAoB;MACpD;IACJ;IACA,IAAI,CAACnF,oBAAoB,GAAG+E,QAAQ,EAAEQ,mBAAmB,IAAI,SAAS;EAC1E;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACN,+BAA+B,CAAC,CAAC;IACtC,IAAI,CAACO,kBAAkB,CAACQ,IAAI,CAAC,CAAC;EAClC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACT,kBAAkB,CAACU,QAAQ,CAAC,CAAC;IAClC,IAAI,CAACN,YAAY,CAACO,WAAW,CAAC,CAAC;EACnC;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAAC3B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;IACnC,IAAI,CAAC4B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAE,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACzB;IACJ;IACA,MAAMH,iBAAiB,GAAG,IAAI,CAAC3B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;IACnC,IAAI,CAAC4B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAzE,SAASA,CAAA,EAAG;IACR;IACA,IAAI,CAAC,IAAI,CAAC4E,eAAe,CAAC,CAAC,EAAE;MACzB;IACJ;IACA,MAAMH,iBAAiB,GAAG,IAAI,CAAC3B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC4B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAjE,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAAC,IAAI,CAACgE,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAAC3B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAAC+B,gBAAgB,CAAC,CAAC,GAAG,CAAC;IAC5C,IAAI,CAACH,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC9B,SAAS,IAAI,CAAC,IAAI,IAAI,CAAClE,QAAQ,IAAI,CAAC;EACpD;EACA;EACA4F,WAAWA,CAAA,EAAG;IACV,MAAMM,YAAY,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC,GAAG,CAAC;IAChD,OAAO,IAAI,CAAC/B,SAAS,GAAGgC,YAAY,IAAI,IAAI,CAAClG,QAAQ,IAAI,CAAC;EAC9D;EACA;EACAiG,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACjG,QAAQ,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,OAAOsC,IAAI,CAAC6D,IAAI,CAAC,IAAI,CAACnF,MAAM,GAAG,IAAI,CAAChB,QAAQ,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,eAAeA,CAACM,QAAQ,EAAE;IACtB;IACA;IACA,MAAMwC,UAAU,GAAG,IAAI,CAAC0B,SAAS,GAAG,IAAI,CAAClE,QAAQ;IACjD,MAAM6F,iBAAiB,GAAG,IAAI,CAAC3B,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG5B,IAAI,CAAC8D,KAAK,CAAC5D,UAAU,GAAGxC,QAAQ,CAAC,IAAI,CAAC;IACvD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC8F,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACA/D,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAAC;EAC/C;EACA;EACApE,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACvB,QAAQ,IAAI,CAAC,IAAI,CAAC+F,eAAe,CAAC,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACIxB,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACM,cAAc,EAAE;MACtB;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAAC9E,QAAQ,EAAE;MAChB,IAAI,CAACuE,SAAS,GACV,IAAI,CAACE,eAAe,CAACzD,MAAM,IAAI,CAAC,GAAG,IAAI,CAACyD,eAAe,CAAC,CAAC,CAAC,GAAGZ,iBAAiB;IACtF;IACA,IAAI,CAACtD,yBAAyB,GAAG,IAAI,CAACkE,eAAe,CAAC4B,KAAK,CAAC,CAAC;IAC7D,IAAI,IAAI,CAAC9F,yBAAyB,CAAC+F,OAAO,CAAC,IAAI,CAACtG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9D,IAAI,CAACO,yBAAyB,CAACgG,IAAI,CAAC,IAAI,CAACvG,QAAQ,CAAC;IACtD;IACA;IACA,IAAI,CAACO,yBAAyB,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IACpD,IAAI,CAACtC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACAyB,cAAcA,CAACD,iBAAiB,EAAE;IAC9B,IAAI,CAACxD,IAAI,CAACsE,IAAI,CAAC;MACXd,iBAAiB;MACjB3B,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBlE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBgB,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC2B,IAAI,YAAAiE,qBAAA/D,CAAA;MAAA,YAAAA,CAAA,IAAwFoB,YAAY,EAlOtBrH,EAAE,CAAAiK,iBAAA,CAkOsC9E,gBAAgB,GAlOxDnF,EAAE,CAAAiK,iBAAA,CAkOmEjK,EAAE,CAACkK,iBAAiB,GAlOzFlK,EAAE,CAAAiK,iBAAA,CAkOoG9C,6BAA6B;IAAA,CAA4D;EAAE;EACjS;IAAS,IAAI,CAACgD,IAAI,kBAnO8EnK,EAAE,CAAAoK,iBAAA;MAAA3D,IAAA,EAmOJY,YAAY;MAAAgD,SAAA;MAAAC,SAAA,WAAsjB,OAAO;MAAAC,MAAA;QAAApH,KAAA;QAAAmE,SAAA,GAnOvkBtH,EAAE,CAAAwK,YAAA,CAAAC,0BAAA,4BAmOyHpK,eAAe;QAAA+D,MAAA,GAnO1IpE,EAAE,CAAAwK,YAAA,CAAAC,0BAAA,sBAmOwKpK,eAAe;QAAA+C,QAAA,GAnOzLpD,EAAE,CAAAwK,YAAA,CAAAC,0BAAA,0BAmO6NpK,eAAe;QAAAwH,eAAA;QAAAO,YAAA,GAnO9OpI,EAAE,CAAAwK,YAAA,CAAAC,0BAAA,kCAmOkUlK,gBAAgB;QAAA8H,oBAAA,GAnOpVrI,EAAE,CAAAwK,YAAA,CAAAC,0BAAA,kDAmO4ZlK,gBAAgB;QAAAgD,YAAA;QAAAF,QAAA,GAnO9arD,EAAE,CAAAwK,YAAA,CAAAC,0BAAA,0BAmOgflK,gBAAgB;MAAA;MAAAmK,OAAA;QAAAjF,IAAA;MAAA;MAAAkF,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAnOlgB7K,EAAE,CAAA8K,wBAAA,EAAF9K,EAAE,CAAA+K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAA1J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1B,EAAE,CAAA4B,cAAA,YAmOqtB,CAAC,YAA8C,CAAC;UAnOvwB5B,EAAE,CAAA+D,UAAA,IAAAD,mCAAA,gBAmO+xB,CAAC;UAnOlyB9D,EAAE,CAAA4B,cAAA,YAmOumE,CAAC,YAAyE,CAAC;UAnOprE5B,EAAE,CAAA6B,MAAA,EAmOuvE,CAAC;UAnO1vE7B,EAAE,CAAA8B,YAAA,CAmO6vE,CAAC;UAnOhwE9B,EAAE,CAAA+D,UAAA,IAAAM,mCAAA,mBAmOmyE,CAAC;UAnOtyErE,EAAE,CAAA4B,cAAA,eAmO67G,CAAC;UAnOh8G5B,EAAE,CAAAuC,UAAA,mBAAA8I,8CAAA;YAAA,OAmO4oG1J,GAAA,CAAAwH,YAAA,CAAa,CAAC;UAAA,CAAC,CAAC;UAnO9pGnJ,EAAE,CAAAyE,cAAA;UAAFzE,EAAE,CAAA4B,cAAA,YAmOslH,CAAC;UAnOzlH5B,EAAE,CAAA0E,SAAA,aAmO6pH,CAAC;UAnOhqH1E,EAAE,CAAA8B,YAAA,CAmO6qH,CAAC,CAAgB,CAAC;UAnOjsH9B,EAAE,CAAAsL,eAAA;UAAFtL,EAAE,CAAA4B,cAAA,gBAmOgmI,CAAC;UAnOnmI5B,EAAE,CAAAuC,UAAA,mBAAAgJ,+CAAA;YAAA,OAmOm0H5J,GAAA,CAAAoH,QAAA,CAAS,CAAC;UAAA,CAAC,CAAC;UAnOj1H/I,EAAE,CAAAyE,cAAA;UAAFzE,EAAE,CAAA4B,cAAA,aAmOyvI,CAAC;UAnO5vI5B,EAAE,CAAA0E,SAAA,eAmOi0I,CAAC;UAnOp0I1E,EAAE,CAAA8B,YAAA,CAmOi1I,CAAC,CAAgB,CAAC;UAnOr2I9B,EAAE,CAAA+D,UAAA,KAAAc,oCAAA,oBAmOs4I,CAAC;UAnOz4I7E,EAAE,CAAA8B,YAAA,CAmOsmK,CAAC,CAAS,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAnO3nK1B,EAAE,CAAAkC,SAAA,EAmOgjE,CAAC;UAnOnjElC,EAAE,CAAAmE,aAAA,KAAAxC,GAAA,CAAAyG,YAAA,SAmOgjE,CAAC;UAnOnjEpI,EAAE,CAAAkC,SAAA,EAmOuvE,CAAC;UAnO1vElC,EAAE,CAAAmC,kBAAA,MAAAR,GAAA,CAAAsC,KAAA,CAAAuB,aAAA,CAAA7D,GAAA,CAAA2F,SAAA,EAAA3F,GAAA,CAAAyB,QAAA,EAAAzB,GAAA,CAAAyC,MAAA,MAmOuvE,CAAC;UAnO1vEpE,EAAE,CAAAkC,SAAA,CAmOmgG,CAAC;UAnOtgGlC,EAAE,CAAAmE,aAAA,IAAAxC,GAAA,CAAA0G,oBAAA,SAmOmgG,CAAC;UAnOtgGrI,EAAE,CAAAkC,SAAA,CAmOgxG,CAAC;UAnOnxGlC,EAAE,CAAAiC,UAAA,eAAAN,GAAA,CAAAsC,KAAA,CAAAsB,iBAmOgxG,CAAC,uBAAA5D,GAAA,CAAAiD,wBAAA,EAAkE,CAAC,8BAA+C,CAAC,aAAAjD,GAAA,CAAAiD,wBAAA,EAAwD,CAAC;UAnO/7G5E,EAAE,CAAAgE,WAAA,eAAArC,GAAA,CAAAsC,KAAA,CAAAsB,iBAAA;UAAFvF,EAAE,CAAAkC,SAAA,EAmO27H,CAAC;UAnO97HlC,EAAE,CAAAiC,UAAA,eAAAN,GAAA,CAAAsC,KAAA,CAAAqB,aAmO27H,CAAC,uBAAA3D,GAAA,CAAAuD,oBAAA,EAA8D,CAAC,8BAA+C,CAAC,aAAAvD,GAAA,CAAAuD,oBAAA,EAAoD,CAAC;UAnOlmIlF,EAAE,CAAAgE,WAAA,eAAArC,GAAA,CAAAsC,KAAA,CAAAqB,aAAA;UAAFtF,EAAE,CAAAkC,SAAA,EAmO0lK,CAAC;UAnO7lKlC,EAAE,CAAAmE,aAAA,KAAAxC,GAAA,CAAA0G,oBAAA,UAmO0lK,CAAC;QAAA;MAAA;MAAAmD,YAAA,GAA6jEhK,YAAY,EAA4LL,SAAS,EAAweI,SAAS,EAAqJN,aAAa,EAA6FI,UAAU;MAAAoK,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAsV;EAAE;AACjiR;AACA;EAAA,QAAApF,SAAA,oBAAAA,SAAA,KArOoGvG,EAAE,CAAAwG,iBAAA,CAqOXa,YAAY,EAAc,CAAC;IAC1GZ,IAAI,EAAEjG,SAAS;IACfkG,IAAI,EAAE,CAAC;MAAEkF,QAAQ,EAAE,eAAe;MAAEjB,QAAQ,EAAE,cAAc;MAAEkB,IAAI,EAAE;QACxD,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE;MACZ,CAAC;MAAEF,eAAe,EAAElL,uBAAuB,CAACqL,MAAM;MAAEJ,aAAa,EAAEhL,iBAAiB,CAACqL,IAAI;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,OAAO,EAAE,CAACxK,YAAY,EAAEL,SAAS,EAAEI,SAAS,EAAEN,aAAa,EAAEI,UAAU,CAAC;MAAE8J,QAAQ,EAAE,w9IAAw9I;MAAEM,MAAM,EAAE,CAAC,o+DAAo+D;IAAE,CAAC;EACvpN,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhF,IAAI,EAAEtB;EAAiB,CAAC,EAAE;IAAEsB,IAAI,EAAEzG,EAAE,CAACkK;EAAkB,CAAC,EAAE;IAAEzD,IAAI,EAAEwF,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC3GzF,IAAI,EAAEvG;IACV,CAAC,EAAE;MACCuG,IAAI,EAAE9F,MAAM;MACZ+F,IAAI,EAAE,CAACS,6BAA6B;IACxC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEhE,KAAK,EAAE,CAAC;MACjCsD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE0G,SAAS,EAAE,CAAC;MACZb,IAAI,EAAE7F,KAAK;MACX8F,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAE9L;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE+D,MAAM,EAAE,CAAC;MACTqC,IAAI,EAAE7F,KAAK;MACX8F,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAE9L;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACXqD,IAAI,EAAE7F,KAAK;MACX8F,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAE9L;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEwH,eAAe,EAAE,CAAC;MAClBpB,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEwH,YAAY,EAAE,CAAC;MACf3B,IAAI,EAAE7F,KAAK;MACX8F,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAE5L;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8H,oBAAoB,EAAE,CAAC;MACvB5B,IAAI,EAAE7F,KAAK;MACX8F,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAE5L;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgD,YAAY,EAAE,CAAC;MACfkD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEyC,QAAQ,EAAE,CAAC;MACXoD,IAAI,EAAE7F,KAAK;MACX8F,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAE5L;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkF,IAAI,EAAE,CAAC;MACPgB,IAAI,EAAE5F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuL,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACrG,IAAI,YAAAsG,2BAAApG,CAAA;MAAA,YAAAA,CAAA,IAAwFmG,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACE,IAAI,kBA9Q8EtM,EAAE,CAAAuM,gBAAA;MAAA9F,IAAA,EA8QS2F;IAAkB,EAAyG;EAAE;EACxO;IAAS,IAAI,CAACI,IAAI,kBA/Q8ExM,EAAE,CAAAyM,gBAAA;MAAAC,SAAA,EA+QwC,CAAC7F,2BAA2B,CAAC;MAAAmF,OAAA,GAAY9K,eAAe,EAAEE,eAAe,EAAEE,gBAAgB,EAAE+F,YAAY;IAAA,EAAI;EAAE;AAC7P;AACA;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAjRoGvG,EAAE,CAAAwG,iBAAA,CAiRX4F,kBAAkB,EAAc,CAAC;IAChH3F,IAAI,EAAE3F,QAAQ;IACd4F,IAAI,EAAE,CAAC;MACCsF,OAAO,EAAE,CAAC9K,eAAe,EAAEE,eAAe,EAAEE,gBAAgB,EAAE+F,YAAY,CAAC;MAC3EsF,OAAO,EAAE,CAACtF,YAAY,CAAC;MACvBqF,SAAS,EAAE,CAAC7F,2BAA2B;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASM,6BAA6B,EAAEN,2BAA2B,EAAEF,mCAAmC,EAAEU,YAAY,EAAElC,gBAAgB,EAAEiH,kBAAkB,EAAElF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}