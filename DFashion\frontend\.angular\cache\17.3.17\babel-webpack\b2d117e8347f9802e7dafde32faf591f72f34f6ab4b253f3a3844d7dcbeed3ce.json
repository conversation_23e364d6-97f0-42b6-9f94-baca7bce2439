{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-slick-carousel\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nfunction ViewAddStoriesComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 5)(2, \"div\", 14)(3, \"div\", 7);\n    i0.ɵɵelement(4, \"img\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r2 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", story_r2.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r2.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r2.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_13_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_13_video_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 30);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17, 1)(3, \"div\", 18);\n    i0.ɵɵelement(4, \"img\", 19);\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"span\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 22);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_13_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeStories());\n    });\n    i0.ɵɵtext(11, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_13_Template_div_click_12_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_13_Template_div_touchstart_12_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_13_Template_div_touchmove_12_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_13_Template_div_touchend_12_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onTouchEnd($event));\n    });\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_13_img_13_Template, 1, 1, \"img\", 25)(14, ViewAddStoriesComponent_div_13_video_14_Template, 1, 1, \"video\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 27);\n    i0.ɵɵelement(16, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r3.getCurrentStory().user.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.getCurrentStory().user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getTimeAgo(ctx_r3.getCurrentStory().createdAt));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.getStoryProgress(), \"%\");\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.storyDuration = 5000; // 5 seconds per story\n    // Slider (carousel) configuration\n    this.storySliderConfig = {\n      slidesToShow: 6,\n      slidesToScroll: 2,\n      infinite: false,\n      arrows: false,\n      dots: false,\n      swipeToSlide: true,\n      responsive: [{\n        breakpoint: 900,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n    /*** Handlers for Adding Stories/Reels (using FormData and Multer) ***/\n    // (The code below shows modals and camera logic; see Node backend for upload handling)\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    // Load stories from backend\n    this.loadStories();\n    // Track current user (for \"Your Story\" avatar)\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n  /*** Story Loading ***/\n  loadStories() {\n    this.isLoadingStories = true;\n    const sub = this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n        } else {\n          // Handle fallback or no data\n          this.stories = [];\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.stories = [];\n        this.isLoadingStories = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n  /*** Story Viewer Open/Close ***/\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    this.startStoryTimer(); // start auto-advance\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    this.clearStoryTimer(); // stop auto-advance\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  /*** Show a specific story and reset rotation state ***/\n  showStory(index) {\n    if (index < 0 || index >= this.stories.length) {\n      this.closeStories();\n      return;\n    }\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  /*** Automatic progression ***/\n  startStoryTimer() {\n    this.clearStoryTimer();\n    this.storyInterval = setInterval(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n  clearStoryTimer() {\n    if (this.storyInterval) {\n      clearInterval(this.storyInterval);\n      this.storyInterval = null;\n    }\n  }\n  /*** Navigate to next story ***/\n  nextStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n  /*** Navigate to previous story ***/\n  previousStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n  /*** Apply smooth 3D rotation between stories ***/\n  updateRotation() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n        this.startStoryTimer(); // restart auto-advance after animation\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.updateRotation());\n  }\n  /*** Pause any playing videos when closing ***/\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  /*** Story Progress (percentage of stories seen) ***/\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  /*** Utility: format time ago (e.g., \"5m\", \"2h\") ***/\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  /*** Handle click on story (left half = previous, right half = next) ***/\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  /*** Drag (swipe) support for touch devices ***/\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n    this.clearStoryTimer(); // pause auto while dragging\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.updateRotation();\n      this.startStoryTimer(); // resume auto-advance\n    }\n  }\n  /*** Keydown navigation (Esc to close, arrows to move) ***/\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    if (event.key === 'ArrowLeft') {\n      this.previousStory();\n    } else if (event.key === 'ArrowRight') {\n      this.nextStory();\n    } else if (event.key === 'Escape') {\n      this.closeStories();\n    }\n  }\n  /*** Utilities for Products and Formatting ***/\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        // Upload video to backend\n        const uploadRes = yield _this2.http.post(`${environment.apiUrl}/stories/upload`, formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post(`${environment.apiUrl}/stories`, reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories(); // refresh list\n      } catch (err) {\n        alert('Failed to upload reel.');\n        console.error(err);\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post(`${environment.apiUrl}/stories/upload`, uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post(`${environment.apiUrl}/stories`, storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n        console.error(err);\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [1, \"stories-slider\", 3, \"config\"], [\"ngxSlickItem\", \"\", 1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"ngxSlickItem\", \"\", \"class\", \"story-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"stories-overlay\", 4, \"ngIf\"], [\"ngxSlickItem\", \"\", 1, \"story-item\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [1, \"stories-overlay\"], [1, \"stories-content\"], [1, \"story-header\"], [\"alt\", \"Avatar\", 1, \"story-header-avatar\", 3, \"src\"], [1, \"story-header-info\"], [1, \"username\"], [1, \"time\"], [1, \"close-btn\", 3, \"click\"], [1, \"story-media\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [\"class\", \"story-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-video\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [1, \"story-progress-bar\"], [1, \"progress\"], [1, \"story-image\", 3, \"src\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-video\", 3, \"src\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"ngx-slick-carousel\", 3)(3, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_div_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7);\n          i0.ɵɵelement(7, \"img\", 8);\n          i0.ɵɵelementStart(8, \"span\", 9);\n          i0.ɵɵtext(9, \"+\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 10);\n          i0.ɵɵtext(11, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, ViewAddStoriesComponent_div_12_Template, 7, 3, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_13_Template, 17, 7, \"div\", 12);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"config\", ctx.storySliderConfig);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, SlickCarouselModule, i5.SlickCarouselComponent, i5.SlickItemDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  background-color: #fff;\\n  border-bottom: 1px solid #ddd;\\n  overflow-x: hidden;\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 500px;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 0 6px;\\n  cursor: pointer;\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  padding: 2px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 54px;\\n  height: 54px;\\n  border-radius: 50%;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  margin-top: 6px;\\n  font-size: 12px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: #c5c5c5;\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  font-size: 20px;\\n  font-weight: bold;\\n  color: #fff;\\n  background: #007bff;\\n  border-radius: 50%;\\n  padding: 2px 6px;\\n  bottom: -2px;\\n  right: -2px;\\n  z-index: 1;\\n}\\n\\n\\n\\n.stories-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.8);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.stories-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 90%;\\n  max-width: 400px;\\n  background: #000;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px;\\n  background: #111;\\n  color: #fff;\\n}\\n\\n.story-header-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  margin-right: 8px;\\n}\\n\\n.story-header-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: bold;\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #bbb;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  color: #fff;\\n  cursor: pointer;\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  background: #000;\\n}\\n\\n.story-image[_ngcontent-%COMP%], .story-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n}\\n\\n.story-progress-bar[_ngcontent-%COMP%] {\\n  height: 4px;\\n  background: #444;\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #0f0;\\n  transition: width 0.1s;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9jb21wb25lbnRzL2luc3RhZ3JhbS1zdG9yaWVzL3ZpZXctYWRkLXN0b3JpZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSw2QkFBQTtFQUNBLGtCQUFBO0FBQ0Y7O0FBRUE7RUFDTSxhQUFBO0VBQ0YsbUJBQUE7RUFDQSxZQUFBO0FBQ0o7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLCtFQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUFDRjs7QUFFQTtFQUNFLGVBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0FBQ0Y7O0FBRUEscUJBQUE7QUFDQTtFQUNFLG1CQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLFVBQUE7QUFDRjs7QUFFQSx1QkFBQTtBQUNBO0VBQ0UsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxvQ0FBQTtFQUNBLGFBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQUNGOztBQUVBO0VBQ0Usa0JBQUE7RUFDQSxVQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUVBO0VBQ0UsT0FBQTtBQUNGOztBQUVBO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxlQUFBO0VBQ0EsV0FBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsWUFBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5zdG9yaWVzLWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDEycHg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZGRkO1xuICBvdmVyZmxvdy14OiBoaWRkZW47XG59XG5cbi5zdG9yaWVzLXNsaWRlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgd2lkdGg6IDUwMHB4O1xufVxuXG4uc3RvcnktaXRlbSB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luOiAwIDZweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xufVxuXG4uc3RvcnktYXZhdGFyLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4uc3RvcnktYXZhdGFyIHtcbiAgd2lkdGg6IDYwcHg7XG4gIGhlaWdodDogNjBweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICNmMDk0MzMsICNlNjY4M2MsICNkYzI3NDMsICNjYzIzNjYsICNiYzE4ODgpO1xuICBwYWRkaW5nOiAycHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xufVxuXG4uc3RvcnktYXZhdGFyLWlubmVyIHtcbiAgd2lkdGg6IDU0cHg7XG4gIGhlaWdodDogNTRweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi5zdG9yeS1hdmF0YXItaW1nIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBvYmplY3QtZml0OiBjb3Zlcjtcbn1cblxuLnN0b3J5LXVzZXJuYW1lIHtcbiAgbWFyZ2luLXRvcDogNnB4O1xuICBmb250LXNpemU6IDEycHg7XG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xufVxuXG4vKiBBZGQgU3RvcnkgQnV0dG9uICovXG4uYWRkLWF2YXRhciB7XG4gIGJhY2tncm91bmQ6ICNjNWM1YzU7XG59XG5cbi5hZGQtc3RvcnktcGx1cyB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgY29sb3I6ICNmZmY7XG4gIGJhY2tncm91bmQ6ICMwMDdiZmY7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgcGFkZGluZzogMnB4IDZweDtcbiAgYm90dG9tOiAtMnB4O1xuICByaWdodDogLTJweDtcbiAgei1pbmRleDogMTtcbn1cblxuLyogRnVsbHNjcmVlbiBvdmVybGF5ICovXG4uc3Rvcmllcy1vdmVybGF5IHtcbiAgcG9zaXRpb246IGZpeGVkO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwwLDAsMC44KTtcbiAgei1pbmRleDogMTAwMDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG59XG5cbi5zdG9yaWVzLWNvbnRlbnQge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiA5MCU7XG4gIG1heC13aWR0aDogNDAwcHg7XG4gIGJhY2tncm91bmQ6ICMwMDA7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLnN0b3J5LWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDhweDtcbiAgYmFja2dyb3VuZDogIzExMTtcbiAgY29sb3I6ICNmZmY7XG59XG5cbi5zdG9yeS1oZWFkZXItYXZhdGFyIHtcbiAgd2lkdGg6IDMycHg7XG4gIGhlaWdodDogMzJweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBtYXJnaW4tcmlnaHQ6IDhweDtcbn1cblxuLnN0b3J5LWhlYWRlci1pbmZvIHtcbiAgZmxleDogMTtcbn1cblxuLnVzZXJuYW1lIHtcbiAgZGlzcGxheTogYmxvY2s7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xufVxuXG4udGltZSB7XG4gIGZvbnQtc2l6ZTogMTJweDtcbiAgY29sb3I6ICNiYmI7XG59XG5cbi5jbG9zZS1idG4ge1xuICBiYWNrZ3JvdW5kOiBub25lO1xuICBib3JkZXI6IG5vbmU7XG4gIGZvbnQtc2l6ZTogMjRweDtcbiAgY29sb3I6ICNmZmY7XG4gIGN1cnNvcjogcG9pbnRlcjtcbn1cblxuLnN0b3J5LW1lZGlhIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB3aWR0aDogMTAwJTtcbiAgYmFja2dyb3VuZDogIzAwMDtcbn1cblxuLnN0b3J5LWltYWdlLCAuc3RvcnktdmlkZW8ge1xuICB3aWR0aDogMTAwJTtcbiAgZGlzcGxheTogYmxvY2s7XG59XG5cbi5zdG9yeS1wcm9ncmVzcy1iYXIge1xuICBoZWlnaHQ6IDRweDtcbiAgYmFja2dyb3VuZDogIzQ0NDtcbn1cblxuLnByb2dyZXNzIHtcbiAgaGVpZ2h0OiAxMDAlO1xuICBiYWNrZ3JvdW5kOiAjMGYwO1xuICB0cmFuc2l0aW9uOiB3aWR0aCAwLjFzO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "SlickCarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "story_r2", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "ctx_r3", "getCurrentStory", "mediaUrl", "ɵɵlistener", "ViewAddStoriesComponent_div_13_Template_button_click_10_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "closeStories", "ViewAddStoriesComponent_div_13_Template_div_click_12_listener", "$event", "onStoryClick", "ViewAddStoriesComponent_div_13_Template_div_touchstart_12_listener", "onTouchStart", "ViewAddStoriesComponent_div_13_Template_div_touchmove_12_listener", "onTouchMove", "ViewAddStoriesComponent_div_13_Template_div_touchend_12_listener", "onTouchEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_13_img_13_Template", "ViewAddStoriesComponent_div_13_video_14_Template", "getTimeAgo", "createdAt", "mediaType", "ɵɵstyleProp", "getStoryProgress", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "currentUser", "stories", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "storyDuration", "storySliderConfig", "slidesToShow", "slidesToScroll", "infinite", "arrows", "dots", "swipeToSlide", "responsive", "breakpoint", "settings", "showAddModal", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "showCameraOrGallery", "permissionDenied", "isRecording", "recordedChunks", "mediaRecorder", "videoStream", "reelPreviewUrl", "isUploadingReel", "newReelCaption", "newStoryFile", "newStoryCaption", "isUploadingStory", "ngOnInit", "loadStories", "currentUser$", "subscribe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "clearStoryTimer", "get", "apiUrl", "next", "response", "success", "storyGroups", "error", "console", "push", "openStories", "index", "showStory", "startStoryTimer", "document", "body", "style", "overflow", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "length", "transform", "storyInterval", "setInterval", "nextStory", "clearInterval", "updateRotation", "previousStory", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "event", "clickX", "clientX", "windowWidth", "window", "innerWidth", "touches", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "handleKeydown", "key", "formatNumber", "num", "toFixed", "toString", "formatPrice", "price", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "viewProduct", "product", "navigate", "_id", "hasProducts", "story", "products", "getStoryProducts", "onAdd", "onAddStory", "onAddReel", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "startRecording", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stopRecording", "stop", "submitNewReel", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "url", "caption", "isReel", "cleanupReelStream", "getTracks", "track", "closeAddReelModal", "handlePermissionResponse", "allow", "openCamera", "input", "setAttribute", "click", "openGallery", "removeAttribute", "onStoryFileSelected", "file", "target", "files", "submitNewStory", "_this3", "uploadForm", "storyPayload", "closeAddStoryModal", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_Template_div_click_3_listener", "_r1", "ViewAddStoriesComponent_div_12_Template", "ViewAddStoriesComponent_div_13_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "SlickCarouselComponent", "SlickItemDirective", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["// import { Component, On<PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, HostListener } from '@angular/core';\n// import { CommonModule } from '@angular/common';\n// import { FormsModule } from '@angular/forms';\n// import { Router } from '@angular/router';\n// import { HttpClient } from '@angular/common/http';\n// import { Subscription } from 'rxjs';\n// import { environment } from 'src/environments/environment';\n// import { AuthService } from 'src/app/core/services/auth.service';\n\n// import { SlickCarouselModule } from 'ngx-slick-carousel';\n\n// interface Story {\n//   _id: string;\n//   user: {\n//     _id: string;\n//     username: string;\n//     fullName: string;\n//     avatar: string;\n//   };\n//   mediaUrl: string;\n//   mediaType: 'image' | 'video';\n//   caption?: string;\n//   createdAt: string;\n//   expiresAt: string;\n//   views: number;\n//   isActive: boolean;\n//   products?: Array<{\n//     _id: string;\n//     name: string;\n//     price: number;\n//     image: string;\n//   }>;\n// }\n\n// @Component({\n//   selector: 'app-view-add-stories',\n//   standalone: true,\n//   imports: [CommonModule, FormsModule, SlickCarouselModule],\n//   templateUrl: './view-add-stories.component.html',\n//   styleUrls: ['./view-add-stories.component.scss']\n// })\n// export class ViewAddStoriesComponent implements OnInit, OnDestroy {\n//   @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n//   @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n//   currentUser: any = null;\n\n//   stories: Story[] = [];\n//   isLoadingStories = true;\n\n//   currentIndex = 0;\n//   isOpen = false;\n//   isRotating = false;\n//   isDragging = false;\n//   rotateY = 0;\n//   targetRotateY = 0;\n//   targetDirection: 'forward' | 'back' | null = null;\n//   dragStartX = 0;\n//   dragCurrentX = 0;\n//   minDragPercentToTransition = 0.5;\n//   minVelocityToTransition = 0.65;\n//   transitionSpeed = 6;\n//   private subscriptions: Subscription[] = [];\n\n//   // For slider arrows\n//   @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n//   canScrollStoriesLeft = false;\n//   canScrollStoriesRight = false;\n// storySliderConfig = {\n//   slidesToShow: 6,\n//   slidesToScroll: 2,\n//   infinite: false,\n//   arrows: false,\n//   dots: false,\n//   variableWidth: false,\n//   swipeToSlide: true,\n//   responsive: [\n//     { breakpoint: 900, settings: { slidesToShow: 4 } },\n//     { breakpoint: 600, settings: { slidesToShow: 3 } }\n//   ]\n// };\n//   constructor(\n//     private router: Router,\n//     private http: HttpClient,\n//     private authService: AuthService\n//   ) {}\n\n//   ngOnInit() {\n//     this.loadStories();\n//     this.setupEventListeners();\n//     this.authService.currentUser$.subscribe(user => {\n//       this.currentUser = user;\n//     });\n//   }\n\n//   ngOnDestroy() {\n//     this.subscriptions.forEach(sub => sub.unsubscribe());\n//     this.removeEventListeners();\n//   }\n\n//   // --- Slider Arrow Logic ---\n//   scrollStoriesLeft() {\n//     if (this.storiesSlider) {\n//       this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n//       setTimeout(() => this.updateStoriesArrows(), 300);\n//     }\n//   }\n//   scrollStoriesRight() {\n//     if (this.storiesSlider) {\n//       this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n//       setTimeout(() => this.updateStoriesArrows(), 300);\n//     }\n//   }\n//   updateStoriesArrows() {\n//     if (this.storiesSlider) {\n//       const el = this.storiesSlider.nativeElement;\n//       this.canScrollStoriesLeft = el.scrollLeft > 0;\n//       this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n//     }\n//   }\n//   ngAfterViewInit() {\n//     setTimeout(() => this.updateStoriesArrows(), 500);\n//   }\n\n//   // --- Story Logic (unchanged) ---\n//   loadStories() {\n//     this.isLoadingStories = true;\n//     this.subscriptions.push(\n//       this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n//         next: (response) => {\n//           if (response.success && response.storyGroups) {\n//             this.stories = response.storyGroups;\n//           } else {\n//             this.loadFallbackStories();\n//           }\n//           this.isLoadingStories = false;\n//         },\n//         error: (error) => {\n//           console.error('Error loading stories:', error);\n//           this.loadFallbackStories();\n//           this.isLoadingStories = false;\n//         }\n//       })\n//     );\n//   }\n\n//   loadFallbackStories() {\n//     this.stories = [\n//       // ... (same as before, omitted for brevity)\n//     ];\n//   }\n\n//   openStories(index: number = 0) {\n//     this.currentIndex = index;\n//     this.isOpen = true;\n//     this.showStory(index);\n//     document.body.style.overflow = 'hidden';\n//   }\n//   closeStories() {\n//     this.isOpen = false;\n//     this.pauseAllVideos();\n//     document.body.style.overflow = 'auto';\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.classList.add('is-closed');\n//     }\n//     setTimeout(() => {\n//       if (this.storiesContainer) {\n//         this.storiesContainer.nativeElement.classList.remove('is-closed');\n//       }\n//     }, 300);\n//   }\n//   showStory(index: number) {\n//     this.currentIndex = index;\n//     this.rotateY = 0;\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n//     }\n//   }\n//   nextStory() {\n//     if (this.currentIndex < this.stories.length - 1) {\n//       this.targetRotateY = -90;\n//       this.targetDirection = 'forward';\n//       this.isRotating = true;\n//       this.update();\n//     } else {\n//       this.closeStories();\n//     }\n//   }\n//   previousStory() {\n//     if (this.currentIndex > 0) {\n//       this.targetRotateY = 90;\n//       this.targetDirection = 'back';\n//       this.isRotating = true;\n//       this.update();\n//     } else {\n//       this.closeStories();\n//     }\n//   }\n//   @HostListener('document:keydown', ['$event'])\n//   handleKeydown(event: KeyboardEvent) {\n//     if (!this.isOpen) return;\n//     switch (event.key) {\n//       case 'ArrowLeft':\n//         this.previousStory();\n//         break;\n//       case 'ArrowRight':\n//         this.nextStory();\n//         break;\n//       case 'Escape':\n//         this.closeStories();\n//         break;\n//     }\n//   }\n//   onStoryClick(event: MouseEvent) {\n//     if (this.isRotating) return;\n//     const clickX = event.clientX;\n//     const windowWidth = window.innerWidth;\n//     if (clickX < windowWidth / 3) {\n//       this.previousStory();\n//     } else {\n//       this.nextStory();\n//     }\n//   }\n//   onTouchStart(event: TouchEvent) {\n//     this.isDragging = true;\n//     this.dragStartX = event.touches[0].clientX;\n//     this.dragCurrentX = this.dragStartX;\n//   }\n//   onTouchMove(event: TouchEvent) {\n//     if (!this.isDragging) return;\n//     this.dragCurrentX = event.touches[0].clientX;\n//     this.updateDragPosition();\n//   }\n//   onTouchEnd(event: TouchEvent) {\n//     if (!this.isDragging) return;\n//     this.isDragging = false;\n//     const dragDelta = this.dragCurrentX - this.dragStartX;\n//     const threshold = window.innerWidth * this.minDragPercentToTransition;\n//     if (Math.abs(dragDelta) > threshold) {\n//       if (dragDelta > 0) {\n//         this.previousStory();\n//       } else {\n//         this.nextStory();\n//       }\n//     } else {\n//       this.targetRotateY = 0;\n//       this.isRotating = true;\n//       this.update();\n//     }\n//   }\n//   private updateDragPosition() {\n//     const dragDelta = this.dragCurrentX - this.dragStartX;\n//     this.rotateY = (dragDelta / window.innerWidth) * 90;\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.style.transform = \n//         `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n//     }\n//   }\n//   private update() {\n//     if (!this.isRotating) return;\n//     this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n//     if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n//       this.rotateY = this.targetRotateY;\n//       this.isRotating = false;\n//       if (this.targetDirection) {\n//         const newIndex = this.targetDirection === 'forward' \n//           ? this.currentIndex + 1 \n//           : this.currentIndex - 1;\n//         this.showStory(newIndex);\n//         this.targetDirection = null;\n//       }\n//       return;\n//     }\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.style.transform = \n//         `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n//     }\n//     requestAnimationFrame(() => this.update());\n//   }\n//   private pauseAllVideos() {\n//     const videos = document.querySelectorAll('.story__video');\n//     videos.forEach((video: any) => {\n//       if (video.pause) {\n//         video.pause();\n//       }\n//     });\n//   }\n//   private setupEventListeners() {}\n//   private removeEventListeners() {}\n//   getCurrentStory(): Story {\n//     return this.stories[this.currentIndex];\n//   }\n//   getStoryProgress(): number {\n//     return ((this.currentIndex + 1) / this.stories.length) * 100;\n//   }\n//   getTimeAgo(dateString: string): string {\n//     const now = new Date();\n//     const date = new Date(dateString);\n//     const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n//     if (diffInMinutes < 1) return 'now';\n//     if (diffInMinutes < 60) return `${diffInMinutes}m`;\n//     const diffInHours = Math.floor(diffInMinutes / 60);\n//     if (diffInHours < 24) return `${diffInHours}h`;\n//     const diffInDays = Math.floor(diffInHours / 24);\n//     return `${diffInDays}d`;\n//   }\n//   formatNumber(num: number): string {\n//     if (num >= 1000000) {\n//       return (num / 1000000).toFixed(1) + 'M';\n//     } else if (num >= 1000) {\n//       return (num / 1000).toFixed(1) + 'K';\n//     }\n//     return num.toString();\n//   }\n//   formatPrice(price: number): string {\n//     return new Intl.NumberFormat('en-IN', {\n//       style: 'currency',\n//       currency: 'INR',\n//       minimumFractionDigits: 0\n//     }).format(price);\n//   }\n//   viewProduct(product: any) {\n//     this.router.navigate(['/product', product._id]);\n//   }\n//   hasProducts(): boolean {\n//     const story = this.getCurrentStory();\n//     return !!(story && story.products && story.products.length > 0);\n//   }\n//   getStoryProducts(): any[] {\n//     const story = this.getCurrentStory();\n//     return story?.products || [];\n//   }\n//   // Handler for Add Story button\n//   // Modal state\n//   showAddModal = false;\n//   showAddStoryModal = false;\n//   showAddReelModal = false;\n//   showPermissionModal = false;\n//   showCameraOrGallery = false;\n//   permissionDenied = false;\n//   // Reel recording state\n//   isRecording = false;\n//   recordedChunks: Blob[] = [];\n//   mediaRecorder: any = null;\n//   videoStream: MediaStream | null = null;\n//   reelPreviewUrl: string | null = null;\n//   isUploadingReel = false;\n//   newReelCaption = '';\n//   newStoryFile: File | null = null;\n//   newStoryCaption = '';\n//   isUploadingStory = false;\n//   onAdd() {\n//     this.showAddModal = true;\n//     this.showAddStoryModal = false;\n//     this.showAddReelModal = false;\n//     this.showPermissionModal = false;\n//     this.showCameraOrGallery = false;\n//     this.permissionDenied = false;\n//     this.newStoryFile = null;\n//     this.newStoryCaption = '';\n//     this.reelPreviewUrl = null;\n//     this.newReelCaption = '';\n//   }\n//   onAddStory() {\n//     this.showAddModal = false;\n//     this.showPermissionModal = true;\n//     this.permissionDenied = false;\n//     this.showAddStoryModal = false;\n//     this.showCameraOrGallery = false;\n//     this.newStoryFile = null;\n//     this.newStoryCaption = '';\n//   }\n//   onAddReel() {\n//     this.showAddModal = false;\n//     this.showAddReelModal = true;\n//     this.reelPreviewUrl = null;\n//     this.newReelCaption = '';\n//     setTimeout(() => this.startCameraForReel(), 100);\n//   }\n//   async startCameraForReel() {\n//     try {\n//       this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n//       const video: any = document.getElementById('reel-video');\n//       if (video) {\n//         video.srcObject = this.videoStream;\n//         video.play();\n//       }\n//     } catch (err) {\n//       alert('Could not access camera.');\n//       this.showAddReelModal = false;\n//     }\n//   }\n//   startRecording() {\n//     if (!this.videoStream) return;\n//     this.recordedChunks = [];\n//     this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n//     this.mediaRecorder.ondataavailable = (e: any) => {\n//       if (e.data.size > 0) this.recordedChunks.push(e.data);\n//     };\n//     this.mediaRecorder.onstop = () => {\n//       const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n//       this.reelPreviewUrl = URL.createObjectURL(blob);\n//     };\n//     this.mediaRecorder.start();\n//     this.isRecording = true;\n//   }\n//   stopRecording() {\n//     if (this.mediaRecorder && this.isRecording) {\n//       this.mediaRecorder.stop();\n//       this.isRecording = false;\n//     }\n//   }\n//   async submitNewReel() {\n//     if (!this.reelPreviewUrl) return;\n//     this.isUploadingReel = true;\n//     try {\n//       const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n//       const formData = new FormData();\n//       formData.append('media', blob, 'reel.webm');\n//       const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n//       const reelPayload = {\n//         media: {\n//           type: uploadRes.type,\n//           url: uploadRes.url\n//         },\n//         caption: this.newReelCaption,\n//         isReel: true\n//       };\n//       await this.http.post('/api/stories', reelPayload).toPromise();\n//       this.showAddReelModal = false;\n//       this.loadStories();\n//     } catch (err) {\n//       alert('Failed to upload reel.');\n//     } finally {\n//       this.isUploadingReel = false;\n//       this.cleanupReelStream();\n//     }\n//   }\n//   cleanupReelStream() {\n//     if (this.videoStream) {\n//       this.videoStream.getTracks().forEach(track => track.stop());\n//       this.videoStream = null;\n//     }\n//     this.mediaRecorder = null;\n//     this.isRecording = false;\n//     this.reelPreviewUrl = null;\n//   }\n//   closeAddReelModal() {\n//     this.showAddReelModal = false;\n//     this.cleanupReelStream();\n//   }\n//   handlePermissionResponse(allow: boolean) {\n//     this.showPermissionModal = false;\n//     if (allow) {\n//       this.showCameraOrGallery = true;\n//     } else {\n//       this.permissionDenied = true;\n//     }\n//   }\n//   openCamera() {\n//     this.showCameraOrGallery = false;\n//     this.showAddStoryModal = true;\n//     setTimeout(() => {\n//       const input: any = document.getElementById('story-file-input');\n//       if (input) {\n//         input.setAttribute('capture', 'environment');\n//         input.click();\n//       }\n//     }, 100);\n//   }\n//   openGallery() {\n//     this.showCameraOrGallery = false;\n//     this.showAddStoryModal = true;\n//     setTimeout(() => {\n//       const input: any = document.getElementById('story-file-input');\n//       if (input) {\n//         input.removeAttribute('capture');\n//         input.click();\n//       }\n//     }, 100);\n//   }\n//   onStoryFileSelected(event: any) {\n//     const file = event.target.files[0];\n//     if (file) {\n//       this.newStoryFile = file;\n//     }\n//   }\n//   async submitNewStory() {\n//     if (!this.newStoryFile) return;\n//     this.isUploadingStory = true;\n//     try {\n//       const uploadForm = new FormData();\n//       uploadForm.append('media', this.newStoryFile);\n//       const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n//       const storyPayload = {\n//         media: {\n//           type: uploadRes.type,\n//           url: uploadRes.url\n//         },\n//         caption: this.newStoryCaption\n//       };\n//       await this.http.post('/api/stories', storyPayload).toPromise();\n//       this.showAddStoryModal = false;\n//       this.loadStories();\n//     } catch (err) {\n//       alert('Failed to upload story.');\n//     } finally {\n//       this.isUploadingStory = false;\n//     }\n//   }\n//   closeAddStoryModal() {\n//     this.showAddStoryModal = false;\n//   }\n// }\nimport { Component, OnInit, OnDestroy, ElementRef, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription, interval } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\n\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  currentUser: any = null;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // Auto-advance timer\n  private storyInterval: any;\n  private storyDuration = 5000; // 5 seconds per story\n\n  // Slider (carousel) configuration\n  storySliderConfig = {\n    slidesToShow: 6,\n    slidesToScroll: 2,\n    infinite: false,\n    arrows: false,\n    dots: false,\n    swipeToSlide: true,\n    responsive: [\n      { breakpoint: 900, settings: { slidesToShow: 4 } },\n      { breakpoint: 600, settings: { slidesToShow: 3 } }\n    ]\n  };\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) { }\n\n  ngOnInit() {\n    // Load stories from backend\n    this.loadStories();\n    // Track current user (for \"Your Story\" avatar)\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n\n  /*** Story Loading ***/\n  loadStories() {\n    this.isLoadingStories = true;\n    const sub = this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n      next: (response) => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n        } else {\n          // Handle fallback or no data\n          this.stories = [];\n        }\n        this.isLoadingStories = false;\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        this.stories = [];\n        this.isLoadingStories = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n\n  /*** Story Viewer Open/Close ***/\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    this.startStoryTimer();       // start auto-advance\n    document.body.style.overflow = 'hidden';\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    this.clearStoryTimer();        // stop auto-advance\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  /*** Show a specific story and reset rotation state ***/\n  showStory(index: number) {\n    if (index < 0 || index >= this.stories.length) {\n      this.closeStories();\n      return;\n    }\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  /*** Automatic progression ***/\n  private startStoryTimer() {\n    this.clearStoryTimer();\n    this.storyInterval = setInterval(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n\n  private clearStoryTimer() {\n    if (this.storyInterval) {\n      clearInterval(this.storyInterval);\n      this.storyInterval = null;\n    }\n  }\n\n  /*** Navigate to next story ***/\n  nextStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  /*** Navigate to previous story ***/\n  previousStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  /*** Apply smooth 3D rotation between stories ***/\n  private updateRotation() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n        this.startStoryTimer(); // restart auto-advance after animation\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.updateRotation());\n  }\n\n  /*** Pause any playing videos when closing ***/\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach((video: any) => {\n      if (video.pause) { video.pause(); }\n    });\n  }\n\n  /*** Story Progress (percentage of stories seen) ***/\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n\n  /*** Utility: format time ago (e.g., \"5m\", \"2h\") ***/\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  /*** Handle click on story (left half = previous, right half = next) ***/\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  /*** Drag (swipe) support for touch devices ***/\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n    this.clearStoryTimer(); // pause auto while dragging\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.updateRotation();\n      this.startStoryTimer(); // resume auto-advance\n    }\n  }\n\n  /*** Keydown navigation (Esc to close, arrows to move) ***/\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    if (event.key === 'ArrowLeft') {\n      this.previousStory();\n    } else if (event.key === 'ArrowRight') {\n      this.nextStory();\n    } else if (event.key === 'Escape') {\n      this.closeStories();\n    }\n  }\n\n  /*** Utilities for Products and Formatting ***/\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n\n  /*** Handlers for Adding Stories/Reels (using FormData and Multer) ***/\n  // (The code below shows modals and camera logic; see Node backend for upload handling)\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      // Upload video to backend\n      const uploadRes: any = await this.http.post(`${environment.apiUrl}/stories/upload`, formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post(`${environment.apiUrl}/stories`, reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories(); // refresh list\n    } catch (err) {\n      alert('Failed to upload reel.');\n      console.error(err);\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post(`${environment.apiUrl}/stories/upload`, uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post(`${environment.apiUrl}/stories`, storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n      console.error(err);\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n}\n\n\n", "<div class=\"stories-container\" #storiesContainer>\n  <!-- Carousel for Stories (Thumbnails) -->\n  <ngx-slick-carousel class=\"stories-slider\" [config]=\"storySliderConfig\">\n    <!-- Add Story Button -->\n    <div ngxSlickItem class=\"story-item add-story-item\" (click)=\"onAdd()\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar add-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img\n              class=\"story-avatar-img\"\n              [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\"\n              alt=\"Your Story\"\n            />\n            <span class=\"add-story-plus\">+</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">Your Story</div>\n    </div>\n\n    <!-- Existing Stories -->\n    <div ngxSlickItem class=\"story-item\" *ngFor=\"let story of stories; let i = index\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img\n              class=\"story-avatar-img\"\n              [src]=\"story.user.avatar\"\n              [alt]=\"story.user.username\"\n            />\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">{{ story.user.username }}</div>\n    </div>\n  </ngx-slick-carousel>\n</div>\n\n<!-- Fullscreen Stories Viewer (shown when isOpen is true) -->\n<div class=\"stories-overlay\" *ngIf=\"isOpen\">\n  <div class=\"stories-content\" #feedCover>\n    <div class=\"story-header\">\n      <img class=\"story-header-avatar\" [src]=\"getCurrentStory().user.avatar\" alt=\"Avatar\"/>\n      <div class=\"story-header-info\">\n        <span class=\"username\">{{ getCurrentStory().user.username }}</span>\n        <span class=\"time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</span>\n      </div>\n      <button (click)=\"closeStories()\" class=\"close-btn\">&times;</button>\n    </div>\n    <div class=\"story-media\" (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      <!-- Image Story -->\n      <img *ngIf=\"getCurrentStory().mediaType === 'image'\"\n           [src]=\"getCurrentStory().mediaUrl\"\n           class=\"story-image\"/>\n      <!-- Video Story -->\n      <video *ngIf=\"getCurrentStory().mediaType === 'video'\"\n             class=\"story-video\"\n             [src]=\"getCurrentStory().mediaUrl\"\n             autoplay muted></video>\n    </div>\n    <!-- Progress Bar / Navigation Dots -->\n    <div class=\"story-progress-bar\">\n      <div class=\"progress\"\n           [style.width.%]=\"getStoryProgress()\"></div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AAkgBA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,mBAAmB,QAAQ,oBAAoB;;;;;;;;;;;IClf9CC,EAHN,CAAAC,cAAA,cAAkF,aAC5C,cACR,aACQ;IAC9BD,EAAA,CAAAE,SAAA,cAIE;IAGRF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAyB;IACvDJ,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IAPIH,EAAA,CAAAK,SAAA,GAAyB;IACzBL,EADA,CAAAM,UAAA,QAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,EAAAT,EAAA,CAAAU,aAAA,CAAyB,QAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CACE;IAKPX,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAY,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;IAqBrDX,EAAA,CAAAE,SAAA,cAE0B;;;;IADrBF,EAAA,CAAAM,UAAA,QAAAO,MAAA,CAAAC,eAAA,GAAAC,QAAA,EAAAf,EAAA,CAAAU,aAAA,CAAkC;;;;;IAGvCV,EAAA,CAAAE,SAAA,gBAG8B;;;;IADvBF,EAAA,CAAAM,UAAA,QAAAO,MAAA,CAAAC,eAAA,GAAAC,QAAA,EAAAf,EAAA,CAAAU,aAAA,CAAkC;;;;;;IAnB3CV,EAFJ,CAAAC,cAAA,cAA4C,iBACF,cACZ;IACxBD,EAAA,CAAAE,SAAA,cAAqF;IAEnFF,EADF,CAAAC,cAAA,cAA+B,eACN;IAAAD,EAAA,CAAAI,MAAA,GAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,GAA6C;IAClEJ,EADkE,CAAAG,YAAA,EAAO,EACnE;IACNH,EAAA,CAAAC,cAAA,kBAAmD;IAA3CD,EAAA,CAAAgB,UAAA,mBAAAC,iEAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAb,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASR,MAAA,CAAAS,YAAA,EAAc;IAAA,EAAC;IAAmBtB,EAAA,CAAAI,MAAA,cAAO;IAC5DJ,EAD4D,CAAAG,YAAA,EAAS,EAC/D;IACNH,EAAA,CAAAC,cAAA,eAGqC;IAAhCD,EAHoB,CAAAgB,UAAA,mBAAAO,8DAAAC,MAAA;MAAAxB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAb,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASR,MAAA,CAAAY,YAAA,CAAAD,MAAA,CAAoB;IAAA,EAAC,wBAAAE,mEAAAF,MAAA;MAAAxB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAb,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CACpCR,MAAA,CAAAc,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC,uBAAAI,kEAAAJ,MAAA;MAAAxB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAb,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CACtBR,MAAA,CAAAgB,WAAA,CAAAL,MAAA,CAAmB;IAAA,EAAC,sBAAAM,iEAAAN,MAAA;MAAAxB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAb,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CACrBR,MAAA,CAAAkB,UAAA,CAAAP,MAAA,CAAkB;IAAA,EAAC;IAMlCxB,EAJA,CAAAgC,UAAA,KAAAC,8CAAA,kBAE0B,KAAAC,gDAAA,oBAKJ;IACxBlC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAE,SAAA,eACgD;IAGtDF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA3BiCH,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,UAAA,QAAAO,MAAA,CAAAC,eAAA,GAAAN,IAAA,CAAAC,MAAA,EAAAT,EAAA,CAAAU,aAAA,CAAqC;IAE7CV,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAAC,eAAA,GAAAN,IAAA,CAAAG,QAAA,CAAqC;IACzCX,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAAsB,UAAA,CAAAtB,MAAA,CAAAC,eAAA,GAAAsB,SAAA,EAA6C;IAS5DpC,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAM,UAAA,SAAAO,MAAA,CAAAC,eAAA,GAAAuB,SAAA,aAA6C;IAI3CrC,EAAA,CAAAK,SAAA,EAA6C;IAA7CL,EAAA,CAAAM,UAAA,SAAAO,MAAA,CAAAC,eAAA,GAAAuB,SAAA,aAA6C;IAQhDrC,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAsC,WAAA,UAAAzB,MAAA,CAAA0B,gBAAA,QAAoC;;;ADwe/C,OAAM,MAAOC,uBAAuB;EAuClCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAvCrB,KAAAC,WAAW,GAAQ,IAAI;IAEvB,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,gBAAgB,GAAG,IAAI;IACvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAIlC,KAAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IAE9B;IACA,KAAAC,iBAAiB,GAAG;MAClBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CACV;QAAEC,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAER,YAAY,EAAE;QAAC;MAAE,CAAE,EAClD;QAAEO,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAER,YAAY,EAAE;QAAC;MAAE,CAAE;KAErD;IA+QD;IACA;IACA,KAAAS,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAG,KAAK;EA1RpB;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,WAAW,EAAE;IAClB;IACA,IAAI,CAAC7C,WAAW,CAAC8C,YAAY,CAACC,SAAS,CAACnF,IAAI,IAAG;MAC7C,IAAI,CAACqC,WAAW,GAAGrC,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAoF,WAAWA,CAAA;IACT,IAAI,CAAChC,aAAa,CAACiC,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACAP,WAAWA,CAAA;IACT,IAAI,CAAC1C,gBAAgB,GAAG,IAAI;IAC5B,MAAM+C,GAAG,GAAG,IAAI,CAACnD,IAAI,CAACsD,GAAG,CAAM,GAAGnG,WAAW,CAACoG,MAAM,UAAU,CAAC,CAACP,SAAS,CAAC;MACxEQ,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAACxD,OAAO,GAAGsD,QAAQ,CAACE,WAAW;SACpC,MAAM;UACL;UACA,IAAI,CAACxD,OAAO,GAAG,EAAE;;QAEnB,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACzD,OAAO,GAAG,EAAE;QACjB,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;IACF,IAAI,CAACa,aAAa,CAAC6C,IAAI,CAACX,GAAG,CAAC;EAC9B;EAEA;EACAY,WAAWA,CAACC,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC3D,YAAY,GAAG2D,KAAK;IACzB,IAAI,CAAC1D,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC2D,SAAS,CAACD,KAAK,CAAC;IACrB,IAAI,CAACE,eAAe,EAAE,CAAC,CAAO;IAC9BC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEA3F,YAAYA,CAAA;IACV,IAAI,CAAC2B,MAAM,GAAG,KAAK;IACnB,IAAI,CAACiE,cAAc,EAAE;IACrB,IAAI,CAAClB,eAAe,EAAE,CAAC,CAAQ;IAC/Bc,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAZ,SAASA,CAACD,KAAa;IACrB,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAC7D,OAAO,CAAC2E,MAAM,EAAE;MAC7C,IAAI,CAACnG,YAAY,EAAE;MACnB;;IAEF,IAAI,CAAC0B,YAAY,GAAG2D,KAAK;IACzB,IAAI,CAACvD,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAAC+D,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACU,SAAS,GAAG,mBAAmB;;EAE7E;EAEA;EACQb,eAAeA,CAAA;IACrB,IAAI,CAACb,eAAe,EAAE;IACtB,IAAI,CAAC2B,aAAa,GAAGC,WAAW,CAAC,MAAK;MACpC,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAAChE,aAAa,CAAC;EACxB;EAEQmC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAAC2B,aAAa,EAAE;MACtBG,aAAa,CAAC,IAAI,CAACH,aAAa,CAAC;MACjC,IAAI,CAACA,aAAa,GAAG,IAAI;;EAE7B;EAEA;EACAE,SAASA,CAAA;IACP,IAAI,CAAC7B,eAAe,EAAE;IACtB,IAAI,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC2E,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACpE,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC6E,cAAc,EAAE;KACtB,MAAM;MACL,IAAI,CAACzG,YAAY,EAAE;;EAEvB;EAEA;EACA0G,aAAaA,CAAA;IACX,IAAI,CAAChC,eAAe,EAAE;IACtB,IAAI,IAAI,CAAChD,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC6E,cAAc,EAAE;KACtB,MAAM;MACL,IAAI,CAACzG,YAAY,EAAE;;EAEvB;EAEA;EACQyG,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC7E,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAIsE,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC9E,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAM6E,QAAQ,GAAG,IAAI,CAAC7E,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACN,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAAC4D,SAAS,CAACuB,QAAQ,CAAC;QACxB,IAAI,CAAC7E,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACuD,eAAe,EAAE,CAAC,CAAC;;MAE1B;;IAEF,IAAI,IAAI,CAACM,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACU,SAAS,GACjD,6BAA6B,IAAI,CAACtE,OAAO,MAAM;;IAEnDgF,qBAAqB,CAAC,MAAM,IAAI,CAACL,cAAc,EAAE,CAAC;EACpD;EAEA;EACQb,cAAcA,CAAA;IACpB,MAAMmB,MAAM,GAAGvB,QAAQ,CAACwB,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACxC,OAAO,CAAE0C,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QAAED,KAAK,CAACC,KAAK,EAAE;;IAClC,CAAC,CAAC;EACJ;EAEA;EACAjG,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACS,YAAY,GAAG,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC2E,MAAM,GAAI,GAAG;EAC9D;EAEA;EACAtF,UAAUA,CAACsG,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGZ,IAAI,CAACa,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGf,IAAI,CAACa,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGhB,IAAI,CAACa,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEA;EACAxH,YAAYA,CAACyH,KAAiB;IAC5B,IAAI,IAAI,CAAChG,UAAU,EAAE;IACrB,MAAMiG,MAAM,GAAGD,KAAK,CAACE,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACrB,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EAEA;EACAlG,YAAYA,CAACuH,KAAiB;IAC5B,IAAI,CAAC/F,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAG2F,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC1C,IAAI,CAAC5F,YAAY,GAAG,IAAI,CAACD,UAAU;IACnC,IAAI,CAACyC,eAAe,EAAE,CAAC,CAAC;EAC1B;EAEAnE,WAAWA,CAACqH,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC/F,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAG0F,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC5C,MAAMK,SAAS,GAAG,IAAI,CAACjG,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAIqG,SAAS,GAAGH,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAACpC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACU,SAAS,GACjD,6BAA6B,IAAI,CAACtE,OAAO,MAAM;;EAErD;EAEArB,UAAUA,CAACmH,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAC/F,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAMsG,SAAS,GAAG,IAAI,CAACjG,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAMmG,SAAS,GAAGJ,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC9F,0BAA0B;IACrE,IAAIwE,IAAI,CAACC,GAAG,CAACuB,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACzB,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAACxE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC6E,cAAc,EAAE;MACrB,IAAI,CAAClB,eAAe,EAAE,CAAC,CAAC;;EAE5B;EAEA;EAEA8C,aAAaA,CAACT,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACjG,MAAM,EAAE;IAClB,IAAIiG,KAAK,CAACU,GAAG,KAAK,WAAW,EAAE;MAC7B,IAAI,CAAC5B,aAAa,EAAE;KACrB,MAAM,IAAIkB,KAAK,CAACU,GAAG,KAAK,YAAY,EAAE;MACrC,IAAI,CAAC/B,SAAS,EAAE;KACjB,MAAM,IAAIqB,KAAK,CAACU,GAAG,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACtI,YAAY,EAAE;;EAEvB;EAEA;EACAuI,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAC,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCpD,KAAK,EAAE,UAAU;MACjBqD,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EAEAM,WAAWA,CAACC,OAAY;IACtB,IAAI,CAAC/H,MAAM,CAACgI,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EAEAC,WAAWA,CAAA;IACT,MAAMC,KAAK,GAAG,IAAI,CAAC/J,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE+J,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACrD,MAAM,GAAG,CAAC,CAAC;EACjE;EAEAsD,gBAAgBA,CAAA;IACd,MAAMF,KAAK,GAAG,IAAI,CAAC/J,eAAe,EAAE;IACpC,OAAO+J,KAAK,EAAEC,QAAQ,IAAI,EAAE;EAC9B;EAEAhK,eAAeA,CAAA;IACb,OAAO,IAAI,CAACgC,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC;EACxC;EAqBAgI,KAAKA,CAAA;IACH,IAAI,CAACxG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EACA6F,UAAUA,CAAA;IACR,IAAI,CAACzG,YAAY,GAAG,KAAK;IACzB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EACA4F,SAASA,CAAA;IACP,IAAI,CAAC1G,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBmC,UAAU,CAAC,MAAM,IAAI,CAAC4D,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAACnG,WAAW,SAASqG,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAEjD,KAAK,EAAE,IAAI;UAAEkD,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAMlD,KAAK,GAAQzB,QAAQ,CAAC4E,cAAc,CAAC,YAAY,CAAC;QACxD,IAAInD,KAAK,EAAE;UACTA,KAAK,CAACoD,SAAS,GAAGP,KAAI,CAACnG,WAAW;UAClCsD,KAAK,CAACqD,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAAC1G,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAqH,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC9G,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKsE,MAAc,CAAC0C,aAAa,CAAC,IAAI,CAAC/G,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAACiH,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACrH,cAAc,CAAC0B,IAAI,CAACyF,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAACnH,aAAa,CAACqH,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACxH,cAAc,EAAE;QAAEyH,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAACtH,cAAc,GAAGuH,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAACtH,aAAa,CAAC2H,KAAK,EAAE;IAC1B,IAAI,CAAC7H,WAAW,GAAG,IAAI;EACzB;EACA8H,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC5H,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAAC6H,IAAI,EAAE;MACzB,IAAI,CAAC/H,WAAW,GAAG,KAAK;;EAE5B;EACMgI,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI,CAAC0B,MAAI,CAAC7H,cAAc,EAAE;MAC1B6H,MAAI,CAAC5H,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAMmH,IAAI,GAAG,IAAIC,IAAI,CAACQ,MAAI,CAAChI,cAAc,EAAE;UAAEyH,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEZ,IAAI,EAAE,WAAW,CAAC;QAC3C;QACA,MAAMa,SAAS,SAAcJ,MAAI,CAACpK,IAAI,CAACyK,IAAI,CAAC,GAAGtN,WAAW,CAACoG,MAAM,iBAAiB,EAAE8G,QAAQ,CAAC,CAACK,SAAS,EAAE;QACzG,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEV,MAAI,CAAC3H,cAAc;UAC5BsI,MAAM,EAAE;SACT;QACD,MAAMX,MAAI,CAACpK,IAAI,CAACyK,IAAI,CAAC,GAAGtN,WAAW,CAACoG,MAAM,UAAU,EAAEoH,WAAW,CAAC,CAACD,SAAS,EAAE;QAC9EN,MAAI,CAACrI,gBAAgB,GAAG,KAAK;QAC7BqI,MAAI,CAACtH,WAAW,EAAE,CAAC,CAAC;OACrB,CAAC,OAAOoG,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;QAC/BtF,OAAO,CAACD,KAAK,CAACsF,GAAG,CAAC;OACnB,SAAS;QACRkB,MAAI,CAAC5H,eAAe,GAAG,KAAK;QAC5B4H,MAAI,CAACY,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC1I,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC2I,SAAS,EAAE,CAAC/H,OAAO,CAACgI,KAAK,IAAIA,KAAK,CAAChB,IAAI,EAAE,CAAC;MAC3D,IAAI,CAAC5H,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EACA4I,iBAAiBA,CAAA;IACf,IAAI,CAACpJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACiJ,iBAAiB,EAAE;EAC1B;EACAI,wBAAwBA,CAACC,KAAc;IACrC,IAAI,CAACrJ,mBAAmB,GAAG,KAAK;IAChC,IAAIqJ,KAAK,EAAE;MACT,IAAI,CAACpJ,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEhC;EACAoJ,UAAUA,CAAA;IACR,IAAI,CAACrJ,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B8C,UAAU,CAAC,MAAK;MACd,MAAM2G,KAAK,GAAQpH,QAAQ,CAAC4E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAC,WAAWA,CAAA;IACT,IAAI,CAACzJ,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B8C,UAAU,CAAC,MAAK;MACd,MAAM2G,KAAK,GAAQpH,QAAQ,CAAC4E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,mBAAmBA,CAACrF,KAAU;IAC5B,MAAMsF,IAAI,GAAGtF,KAAK,CAACuF,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACnJ,YAAY,GAAGmJ,IAAI;;EAE5B;EACMG,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MAClB,IAAI,CAACuD,MAAI,CAACvJ,YAAY,EAAE;MACxBuJ,MAAI,CAACrJ,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAMsJ,UAAU,GAAG,IAAI5B,QAAQ,EAAE;QACjC4B,UAAU,CAAC3B,MAAM,CAAC,OAAO,EAAE0B,MAAI,CAACvJ,YAAY,CAAC;QAC7C,MAAM8H,SAAS,SAAcyB,MAAI,CAACjM,IAAI,CAACyK,IAAI,CAAC,GAAGtN,WAAW,CAACoG,MAAM,iBAAiB,EAAE2I,UAAU,CAAC,CAACxB,SAAS,EAAE;QAC3G,MAAMyB,YAAY,GAAG;UACnBvB,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEmB,MAAI,CAACtJ;SACf;QACD,MAAMsJ,MAAI,CAACjM,IAAI,CAACyK,IAAI,CAAC,GAAGtN,WAAW,CAACoG,MAAM,UAAU,EAAE4I,YAAY,CAAC,CAACzB,SAAS,EAAE;QAC/EuB,MAAI,CAACnK,iBAAiB,GAAG,KAAK;QAC9BmK,MAAI,CAACnJ,WAAW,EAAE;OACnB,CAAC,OAAOoG,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;QAChCtF,OAAO,CAACD,KAAK,CAACsF,GAAG,CAAC;OACnB,SAAS;QACR+C,MAAI,CAACrJ,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAwJ,kBAAkBA,CAAA;IAChB,IAAI,CAACtK,iBAAiB,GAAG,KAAK;EAChC;;;uBA3eWjC,uBAAuB,EAAAxC,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAApP,EAAA,CAAAgP,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvB9M,uBAAuB;MAAA+M,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAvB1P,EAAA,CAAAgB,UAAA,qBAAA4O,mDAAApO,MAAA;YAAA,OAAAmO,GAAA,CAAAhG,aAAA,CAAAnI,MAAA,CAAqB;UAAA,UAAAxB,EAAA,CAAA6P,iBAAA,CAAE;;;;;;;;;;;UCtiBhC7P,EAJJ,CAAAC,cAAA,gBAAiD,4BAEyB,aAEA;UAAlBD,EAAA,CAAAgB,UAAA,mBAAA8O,sDAAA;YAAA9P,EAAA,CAAAkB,aAAA,CAAA6O,GAAA;YAAA,OAAA/P,EAAA,CAAAqB,WAAA,CAASsO,GAAA,CAAA3E,KAAA,EAAO;UAAA,EAAC;UAG/DhL,EAFJ,CAAAC,cAAA,aAAoC,aACG,aACH;UAC9BD,EAAA,CAAAE,SAAA,aAIE;UACFF,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAI,MAAA,QAAC;UAGpCJ,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UACxCJ,EADwC,CAAAG,YAAA,EAAM,EACxC;UAGNH,EAAA,CAAAgC,UAAA,KAAAgO,uCAAA,kBAAkF;UAetFhQ,EADE,CAAAG,YAAA,EAAqB,EACjB;UAGNH,EAAA,CAAAgC,UAAA,KAAAiO,uCAAA,mBAA4C;;;UArCCjQ,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAM,UAAA,WAAAqP,GAAA,CAAA7L,iBAAA,CAA4B;UAQ3D9D,EAAA,CAAAK,SAAA,GAA0D;UAA1DL,EAAA,CAAAM,UAAA,SAAAqP,GAAA,CAAA9M,WAAA,kBAAA8M,GAAA,CAAA9M,WAAA,CAAApC,MAAA,kCAAAT,EAAA,CAAAU,aAAA,CAA0D;UAWbV,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAAM,UAAA,YAAAqP,GAAA,CAAA7M,OAAA,CAAY;UAkBzC9C,EAAA,CAAAK,SAAA,EAAY;UAAZL,EAAA,CAAAM,UAAA,SAAAqP,GAAA,CAAA1M,MAAA,CAAY;;;qBD+f9BrD,YAAY,EAAAsQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEvQ,WAAW,EAAEE,mBAAmB,EAAAsQ,EAAA,CAAAC,sBAAA,EAAAD,EAAA,CAAAE,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}