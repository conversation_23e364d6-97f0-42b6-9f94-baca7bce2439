{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { HeaderComponent } from './shared/components/header/header.component';\nimport { NotificationComponent } from './shared/components/notification/notification.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nfunction AppComponent_app_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-header\");\n  }\n}\nexport class AppComponent {\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n    this.title = 'DFashion';\n    this.showHeader = true;\n  }\n  ngOnInit() {\n    // Hide header on auth pages and admin login\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      const navigationEnd = event;\n      const url = navigationEnd.url;\n      // Hide header on auth pages and admin login\n      const shouldHideHeader = url.includes('/auth') || url.includes('/admin/login') || url.includes('/admin/auth') || url.startsWith('/admin/login');\n      this.showHeader = !shouldHideHeader;\n    });\n    // Set initial header visibility\n    const currentUrl = this.router.url;\n    const shouldHideHeader = currentUrl.includes('/auth') || currentUrl.includes('/admin/login') || currentUrl.includes('/admin/auth') || currentUrl.startsWith('/admin/login');\n    this.showHeader = !shouldHideHeader;\n    // Initialize auth state\n    this.authService.initializeAuth();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"app-container\"], [4, \"ngIf\"], [1, \"main-content\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AppComponent_app_header_1_Template, 1, 0, \"app-header\", 1);\n          i0.ɵɵelementStart(2, \"main\", 2);\n          i0.ɵɵelement(3, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(4, \"app-notification\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"with-header\", ctx.showHeader);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, RouterOutlet, HeaderComponent, NotificationComponent],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  transition: all 0.3s ease;\\n}\\n\\n.main-content.with-header[_ngcontent-%COMP%] {\\n  margin-top: 60px;\\n}\\n\\n@media (max-width: 768px) {\\n  .main-content.with-header[_ngcontent-%COMP%] {\\n    margin-top: 56px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0FBQU47O0FBR0k7RUFDRSxPQUFBO0VBQ0EseUJBQUE7QUFBTjs7QUFHSTtFQUNFLGdCQUFBO0FBQU47O0FBR0k7RUFDRTtJQUNFLGdCQUFBO0VBQU47QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5hcHAtY29udGFpbmVyIHtcbiAgICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgfVxuXG4gICAgLm1haW4tY29udGVudCB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICB9XG5cbiAgICAubWFpbi1jb250ZW50LndpdGgtaGVhZGVyIHtcbiAgICAgIG1hcmdpbi10b3A6IDYwcHg7XG4gICAgfVxuXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAubWFpbi1jb250ZW50LndpdGgtaGVhZGVyIHtcbiAgICAgICAgbWFyZ2luLXRvcDogNTZweDtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "NavigationEnd", "filter", "HeaderComponent", "NotificationComponent", "i0", "ɵɵelement", "AppComponent", "constructor", "router", "authService", "title", "showHeader", "ngOnInit", "events", "pipe", "event", "subscribe", "navigationEnd", "url", "should<PERSON>ideHeader", "includes", "startsWith", "currentUrl", "initializeAuth", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "AppComponent_app_header_1_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵclassProp", "i3", "NgIf", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\n\nimport { HeaderComponent } from './shared/components/header/header.component';\nimport { NotificationComponent } from './shared/components/notification/notification.component';\nimport { AuthService } from './core/services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, HeaderComponent, NotificationComponent],\n  template: `\n    <div class=\"app-container\">\n      <app-header *ngIf=\"showHeader\"></app-header>\n      <main class=\"main-content\" [class.with-header]=\"showHeader\">\n        <router-outlet></router-outlet>\n      </main>\n    </div>\n\n    <!-- Notifications -->\n    <app-notification></app-notification>\n  `,\n  styles: [`\n    .app-container {\n      min-height: 100vh;\n      display: flex;\n      flex-direction: column;\n    }\n\n    .main-content {\n      flex: 1;\n      transition: all 0.3s ease;\n    }\n\n    .main-content.with-header {\n      margin-top: 60px;\n    }\n\n    @media (max-width: 768px) {\n      .main-content.with-header {\n        margin-top: 56px;\n      }\n    }\n  `]\n})\nexport class AppComponent implements OnInit {\n  title = 'DFashion';\n  showHeader = true;\n\n  constructor(\n    private router: Router,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    // Hide header on auth pages and admin login\n    this.router.events\n      .pipe(filter(event => event instanceof NavigationEnd))\n      .subscribe((event) => {\n        const navigationEnd = event as NavigationEnd;\n        const url = navigationEnd.url;\n\n        // Hide header on auth pages and admin login\n        const shouldHideHeader = url.includes('/auth') ||\n                                url.includes('/admin/login') ||\n                                url.includes('/admin/auth') ||\n                                url.startsWith('/admin/login');\n\n        this.showHeader = !shouldHideHeader;\n      });\n\n    // Set initial header visibility\n    const currentUrl = this.router.url;\n    const shouldHideHeader = currentUrl.includes('/auth') ||\n                            currentUrl.includes('/admin/login') ||\n                            currentUrl.includes('/admin/auth') ||\n                            currentUrl.startsWith('/admin/login');\n\n    this.showHeader = !shouldHideHeader;\n\n    // Initialize auth state\n    this.authService.initializeAuth();\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAUC,aAAa,QAAQ,iBAAiB;AACrE,SAASC,MAAM,QAAQ,gBAAgB;AAEvC,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,qBAAqB,QAAQ,yDAAyD;;;;;;;IASzFC,EAAA,CAAAC,SAAA,iBAA4C;;;AAgClD,OAAM,MAAOC,YAAY;EAIvBC,YACUC,MAAc,EACdC,WAAwB;IADxB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IALrB,KAAAC,KAAK,GAAG,UAAU;IAClB,KAAAC,UAAU,GAAG,IAAI;EAKd;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,MAAM,CAACK,MAAM,CACfC,IAAI,CAACb,MAAM,CAACc,KAAK,IAAIA,KAAK,YAAYf,aAAa,CAAC,CAAC,CACrDgB,SAAS,CAAED,KAAK,IAAI;MACnB,MAAME,aAAa,GAAGF,KAAsB;MAC5C,MAAMG,GAAG,GAAGD,aAAa,CAACC,GAAG;MAE7B;MACA,MAAMC,gBAAgB,GAAGD,GAAG,CAACE,QAAQ,CAAC,OAAO,CAAC,IACtBF,GAAG,CAACE,QAAQ,CAAC,cAAc,CAAC,IAC5BF,GAAG,CAACE,QAAQ,CAAC,aAAa,CAAC,IAC3BF,GAAG,CAACG,UAAU,CAAC,cAAc,CAAC;MAEtD,IAAI,CAACV,UAAU,GAAG,CAACQ,gBAAgB;IACrC,CAAC,CAAC;IAEJ;IACA,MAAMG,UAAU,GAAG,IAAI,CAACd,MAAM,CAACU,GAAG;IAClC,MAAMC,gBAAgB,GAAGG,UAAU,CAACF,QAAQ,CAAC,OAAO,CAAC,IAC7BE,UAAU,CAACF,QAAQ,CAAC,cAAc,CAAC,IACnCE,UAAU,CAACF,QAAQ,CAAC,aAAa,CAAC,IAClCE,UAAU,CAACD,UAAU,CAAC,cAAc,CAAC;IAE7D,IAAI,CAACV,UAAU,GAAG,CAACQ,gBAAgB;IAEnC;IACA,IAAI,CAACV,WAAW,CAACc,cAAc,EAAE;EACnC;;;uBArCWjB,YAAY,EAAAF,EAAA,CAAAoB,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAtB,EAAA,CAAAoB,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAZtB,YAAY;MAAAuB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3B,EAAA,CAAA4B,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjCrBlC,EAAA,CAAAoC,cAAA,aAA2B;UACzBpC,EAAA,CAAAqC,UAAA,IAAAC,kCAAA,wBAA+B;UAC/BtC,EAAA,CAAAoC,cAAA,cAA4D;UAC1DpC,EAAA,CAAAC,SAAA,oBAA+B;UAEnCD,EADE,CAAAuC,YAAA,EAAO,EACH;UAGNvC,EAAA,CAAAC,SAAA,uBAAqC;;;UAPtBD,EAAA,CAAAwC,SAAA,EAAgB;UAAhBxC,EAAA,CAAAyC,UAAA,SAAAN,GAAA,CAAA5B,UAAA,CAAgB;UACFP,EAAA,CAAAwC,SAAA,EAAgC;UAAhCxC,EAAA,CAAA0C,WAAA,gBAAAP,GAAA,CAAA5B,UAAA,CAAgC;;;qBAJrDb,YAAY,EAAAiD,EAAA,CAAAC,IAAA,EAAEjD,YAAY,EAAEG,eAAe,EAAEC,qBAAqB;MAAA8C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}