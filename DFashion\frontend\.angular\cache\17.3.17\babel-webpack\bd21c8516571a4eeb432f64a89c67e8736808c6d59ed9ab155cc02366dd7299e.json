{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction InstagramStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstagramStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, InstagramStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction InstagramStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function InstagramStoriesComponent_div_2_Template_div_click_0_listener() {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openStories(i_r2));\n    });\n    i0.ɵɵelement(1, \"div\", 13);\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r4.user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r4.user.username);\n  }\n}\nfunction InstagramStoriesComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r6 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r6 === ctx_r2.currentIndex)(\"completed\", i_r6 < ctx_r2.currentIndex);\n  }\n}\nfunction InstagramStoriesComponent_div_3_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 52);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction InstagramStoriesComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 53);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r2.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction InstagramStoriesComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getCurrentStory().caption, \" \");\n  }\n}\nfunction InstagramStoriesComponent_div_3_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function InstagramStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener() {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 58);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 59)(4, \"div\", 60);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(product_r8.price));\n  }\n}\nfunction InstagramStoriesComponent_div_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, InstagramStoriesComponent_div_3_div_21_div_1_Template, 8, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getCurrentStory().products);\n  }\n}\nfunction InstagramStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16, 0)(3, \"div\", 17);\n    i0.ɵɵtemplate(4, InstagramStoriesComponent_div_3_div_4_Template, 2, 4, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function InstagramStoriesComponent_div_3_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onStoryClick($event));\n    })(\"touchstart\", function InstagramStoriesComponent_div_3_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTouchStart($event));\n    })(\"touchmove\", function InstagramStoriesComponent_div_3_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTouchMove($event));\n    })(\"touchend\", function InstagramStoriesComponent_div_3_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"div\", 21);\n    i0.ɵɵelement(8, \"div\", 22);\n    i0.ɵɵelementStart(9, \"div\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 24);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 25);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function InstagramStoriesComponent_div_3_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 28);\n    i0.ɵɵtemplate(18, InstagramStoriesComponent_div_3_video_18_Template, 1, 1, \"video\", 29)(19, InstagramStoriesComponent_div_3_div_19_Template, 1, 2, \"div\", 30)(20, InstagramStoriesComponent_div_3_div_20_Template, 2, 1, \"div\", 31)(21, InstagramStoriesComponent_div_3_div_21_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 33)(23, \"div\", 34)(24, \"button\", 35);\n    i0.ɵɵelement(25, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 37);\n    i0.ɵɵelement(27, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 39);\n    i0.ɵɵelement(29, \"i\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 41)(31, \"button\", 42);\n    i0.ɵɵelement(32, \"i\", 43);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"button\", 44);\n    i0.ɵɵelement(36, \"i\", 36);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"button\", 45);\n    i0.ɵɵelement(40, \"i\", 46);\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Add to Cart\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(43, \"div\", 47)(44, \"div\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(45, \"div\", 49, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r2.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r2.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r2.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getTimeAgo(ctx_r2.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatNumber(ctx_r2.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCurrentStory().products && ctx_r2.getCurrentStory().products.length > 0);\n    i0.ɵɵadvance(24);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r2.isOpen);\n  }\n}\nfunction InstagramStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 65)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 66);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class InstagramStoriesComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Try to load from API first\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/api/stories/active`).subscribe({\n      next: response => {\n        if (response.success && response.data) {\n          this.stories = response.data;\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [{\n      _id: 'story-1',\n      user: {\n        _id: 'user-1',\n        username: 'ai_fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n      mediaType: 'image',\n      caption: 'Sustainable fashion is the future! 🌱✨',\n      createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n      views: 1247,\n      isActive: true,\n      products: [{\n        _id: 'prod-1',\n        name: 'Eco-Friendly Summer Dress',\n        price: 2499,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n      }]\n    }, {\n      _id: 'story-2',\n      user: {\n        _id: 'user-2',\n        username: 'ai_stylist_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n      mediaType: 'image',\n      caption: 'Street style essentials for the modern man 🔥',\n      createdAt: new Date(Date.now() - 12 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18.8 * 60 * 60 * 1000).toISOString(),\n      views: 892,\n      isActive: true,\n      products: [{\n        _id: 'prod-2',\n        name: 'Urban Cotton T-Shirt',\n        price: 899,\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'\n      }]\n    }, {\n      _id: 'story-3',\n      user: {\n        _id: 'user-3',\n        username: 'ai_trendsetter_zara',\n        fullName: 'Zara Patel',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n      mediaType: 'image',\n      caption: 'Ethnic fusion at its finest! Traditional meets modern ✨',\n      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 21 * 60 * 60 * 1000).toISOString(),\n      views: 2156,\n      isActive: true,\n      products: [{\n        _id: 'prod-3',\n        name: 'Designer Ethnic Kurti',\n        price: 1899,\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=200'\n      }]\n    }, {\n      _id: 'story-4',\n      user: {\n        _id: 'user-4',\n        username: 'ai_minimalist_kai',\n        fullName: 'Kai Thompson',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',\n      mediaType: 'image',\n      caption: 'Less is more. Minimalist wardrobe essentials 🌿',\n      createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n      views: 1543,\n      isActive: true,\n      products: [{\n        _id: 'prod-4',\n        name: 'Classic Denim Jeans',\n        price: 2999,\n        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=200'\n      }]\n    }, {\n      _id: 'story-5',\n      user: {\n        _id: 'user-5',\n        username: 'ai_glamour_sophia',\n        fullName: 'Sophia Williams',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n      mediaType: 'image',\n      caption: 'Luxury accessories for the discerning fashionista 💎',\n      createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),\n      views: 3421,\n      isActive: true,\n      products: [{\n        _id: 'prod-5',\n        name: 'Luxury Leather Handbag',\n        price: 4999,\n        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200'\n      }]\n    }];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    // Left third goes back, right two-thirds go forward\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      // Snap back to current position\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    // Simple easing\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    // Check if animation is complete\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    // Update transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {\n    // Additional event listeners can be added here\n  }\n  removeEventListeners() {\n    // Clean up event listeners\n  }\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  static {\n    this.ɵfac = function InstagramStoriesComponent_Factory(t) {\n      return new (t || InstagramStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InstagramStoriesComponent,\n      selectors: [[\"app-instagram-stories\"]],\n      viewQuery: function InstagramStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n        }\n      },\n      hostBindings: function InstagramStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function InstagramStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"story-bar\"], [\"class\", \"story-loading\", 4, \"ngIf\"], [\"class\", \"story-bar__user\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"story-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"story-bar__user\", 3, \"click\"], [1, \"story-bar__user-avatar\"], [1, \"story-bar__user-name\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\"], [1, \"ecommerce-btn\", \"cart-btn\"], [1, \"fas\", \"fa-plus\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function InstagramStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, InstagramStoriesComponent_div_1_Template, 2, 2, \"div\", 3)(2, InstagramStoriesComponent_div_2_Template, 4, 3, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, InstagramStoriesComponent_div_3_Template, 47, 15, \"div\", 5)(4, InstagramStoriesComponent_div_4_Template, 9, 0, \"div\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n      styles: [\".story-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 12px 16px;\\n  gap: 12px;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.story-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "InstagramStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c2", "ɵɵlistener", "InstagramStoriesComponent_div_2_Template_div_click_0_listener", "i_r2", "ɵɵrestoreView", "_r1", "index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openStories", "ɵɵtext", "ɵɵstyleProp", "story_r4", "user", "avatar", "ɵɵtextInterpolate", "username", "ɵɵclassProp", "i_r6", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "caption", "InstagramStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener", "product_r8", "_r7", "$implicit", "viewProduct", "name", "formatPrice", "price", "InstagramStoriesComponent_div_3_div_21_div_1_Template", "products", "InstagramStoriesComponent_div_3_div_4_Template", "InstagramStoriesComponent_div_3_Template_div_click_5_listener", "$event", "_r5", "onStoryClick", "InstagramStoriesComponent_div_3_Template_div_touchstart_5_listener", "onTouchStart", "InstagramStoriesComponent_div_3_Template_div_touchmove_5_listener", "onTouchMove", "InstagramStoriesComponent_div_3_Template_div_touchend_5_listener", "onTouchEnd", "InstagramStoriesComponent_div_3_Template_button_click_15_listener", "closeStories", "InstagramStoriesComponent_div_3_video_18_Template", "InstagramStoriesComponent_div_3_div_19_Template", "InstagramStoriesComponent_div_3_div_20_Template", "InstagramStoriesComponent_div_3_div_21_Template", "isOpen", "stories", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "length", "InstagramStoriesComponent", "constructor", "router", "http", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "ngOnInit", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "push", "get", "apiUrl", "subscribe", "next", "response", "success", "data", "loadFallbackStories", "error", "console", "_id", "Date", "now", "toISOString", "expiresAt", "isActive", "image", "showStory", "document", "body", "style", "overflow", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "update", "previousStory", "handleKeydown", "event", "key", "clickX", "clientX", "windowWidth", "window", "innerWidth", "touches", "updateDragPosition", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getStoryProgress", "dateString", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "num", "toFixed", "toString", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "product", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "viewQuery", "InstagramStoriesComponent_Query", "rf", "ctx", "InstagramStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "InstagramStoriesComponent_div_1_Template", "InstagramStoriesComponent_div_2_Template", "InstagramStoriesComponent_div_3_Template", "InstagramStoriesComponent_div_4_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\instagram-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\instagram-stories.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from '../../../../environments/environment';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-instagram-stories',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './instagram-stories.component.html',\n  styleUrls: ['./instagram-stories.component.scss']\n})\nexport class InstagramStoriesComponent implements OnInit, OnD<PERSON>roy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Try to load from API first\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/api/stories/active`).subscribe({\n        next: (response) => {\n          if (response.success && response.data) {\n            this.stories = response.data;\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [\n      {\n        _id: 'story-1',\n        user: {\n          _id: 'user-1',\n          username: 'ai_fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        mediaType: 'image',\n        caption: 'Sustainable fashion is the future! 🌱✨',\n        createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago\n        expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(), // 19 hours from now\n        views: 1247,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-1',\n            name: 'Eco-Friendly Summer Dress',\n            price: 2499,\n            image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-2',\n        user: {\n          _id: 'user-2',\n          username: 'ai_stylist_alex',\n          fullName: 'Alex Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        mediaType: 'image',\n        caption: 'Street style essentials for the modern man 🔥',\n        createdAt: new Date(Date.now() - 12 * 60 * 1000).toISOString(), // 12 minutes ago\n        expiresAt: new Date(Date.now() + 18.8 * 60 * 60 * 1000).toISOString(),\n        views: 892,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-2',\n            name: 'Urban Cotton T-Shirt',\n            price: 899,\n            image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-3',\n        user: {\n          _id: 'user-3',\n          username: 'ai_trendsetter_zara',\n          fullName: 'Zara Patel',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n        mediaType: 'image',\n        caption: 'Ethnic fusion at its finest! Traditional meets modern ✨',\n        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago\n        expiresAt: new Date(Date.now() + 21 * 60 * 60 * 1000).toISOString(),\n        views: 2156,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-3',\n            name: 'Designer Ethnic Kurti',\n            price: 1899,\n            image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-4',\n        user: {\n          _id: 'user-4',\n          username: 'ai_minimalist_kai',\n          fullName: 'Kai Thompson',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',\n        mediaType: 'image',\n        caption: 'Less is more. Minimalist wardrobe essentials 🌿',\n        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago\n        expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n        views: 1543,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-4',\n            name: 'Classic Denim Jeans',\n            price: 2999,\n            image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-5',\n        user: {\n          _id: 'user-5',\n          username: 'ai_glamour_sophia',\n          fullName: 'Sophia Williams',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n        mediaType: 'image',\n        caption: 'Luxury accessories for the discerning fashionista 💎',\n        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago\n        expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),\n        views: 3421,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-5',\n            name: 'Luxury Leather Handbag',\n            price: 4999,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200'\n          }\n        ]\n      }\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    \n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    \n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    \n    // Left third goes back, right two-thirds go forward\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    \n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    \n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      // Snap back to current position\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    \n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n    \n    // Simple easing\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    \n    // Check if animation is complete\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      \n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    \n    // Update transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    \n    requestAnimationFrame(() => this.update());\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n\n  private setupEventListeners() {\n    // Additional event listeners can be added here\n  }\n\n  private removeEventListeners() {\n    // Clean up event listeners\n  }\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n}\n", "<!-- Story Bar (Avatar Row) -->\n<div class=\"story-bar\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"story-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories -->\n  <div\n    *ngFor=\"let story of stories; let i = index\"\n    class=\"story-bar__user\"\n    (click)=\"openStories(i)\">\n    <div class=\"story-bar__user-avatar\" [style.background-image]=\"'url(' + story.user.avatar + ')'\"></div>\n    <div class=\"story-bar__user-name\">{{ story.user.username }}</div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"getCurrentStory().products && getCurrentStory().products.length > 0\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getCurrentStory().products\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\">\n          <button class=\"ecommerce-btn buy-now-btn\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAI9C,SAASC,WAAW,QAAQ,sCAAsC;;;;;;;;;;ICD9DC,EAAA,CAAAC,cAAA,aAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAoD;IAClDD,EAAA,CAAAI,UAAA,IAAAC,8CAAA,iBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;;IAOtCT,EAAA,CAAAC,cAAA,cAG2B;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,IAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,IAAA,CAAc;IAAA,EAAC;IACxBZ,EAAA,CAAAE,SAAA,cAAsG;IACtGF,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAoB,MAAA,GAAyB;IAC7DpB,EAD6D,CAAAG,YAAA,EAAM,EAC7D;;;;IAFgCH,EAAA,CAAAM,SAAA,EAA2D;IAA3DN,EAAA,CAAAqB,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAC7DxB,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAyB,iBAAA,CAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;IAUzD1B,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFJH,EADA,CAAA2B,WAAA,WAAAC,IAAA,KAAAZ,MAAA,CAAAa,YAAA,CAAmC,cAAAD,IAAA,GAAAZ,MAAA,CAAAa,YAAA,CACC;;;;;IA6BpC7B,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAAO,UAAA,QAAAS,MAAA,CAAAc,eAAA,GAAAC,QAAA,EAAA/B,EAAA,CAAAgC,aAAA,CAAkC;;;;;IAQpChC,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAqB,WAAA,8BAAAL,MAAA,CAAAc,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItE/B,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAiC,kBAAA,MAAAjB,MAAA,CAAAc,eAAA,GAAAI,OAAA,MACF;;;;;;IAIElC,EAAA,CAAAC,cAAA,cAGiC;IAA/BD,EAAA,CAAAU,UAAA,mBAAAyB,2EAAA;MAAA,MAAAC,UAAA,GAAApC,EAAA,CAAAa,aAAA,CAAAwB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAuB,WAAA,CAAAH,UAAA,CAAoB;IAAA,EAAC;IAC9BpC,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAoB,MAAA,yBAAG;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAErCH,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAoB,MAAA,GAAkB;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAoB,MAAA,GAAgC;IAEnEpB,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;;;;;IAH4BH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAyB,iBAAA,CAAAW,UAAA,CAAAI,IAAA,CAAkB;IACjBxC,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAyB,iBAAA,CAAAT,MAAA,CAAAyB,WAAA,CAAAL,UAAA,CAAAM,KAAA,EAAgC;;;;;IARrE1C,EAAA,CAAAC,cAAA,cAA6G;IAC3GD,EAAA,CAAAI,UAAA,IAAAuC,qDAAA,kBAGiC;IAOnC3C,EAAA,CAAAG,YAAA,EAAM;;;;IATkBH,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAO,UAAA,YAAAS,MAAA,CAAAc,eAAA,GAAAc,QAAA,CAA6B;;;;;;IA3DzD5C,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAAI,UAAA,IAAAyC,8CAAA,kBAIuC;IAGzC7C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAU,UAAA,mBAAAoC,8DAAAC,MAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAiC,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC,wBAAAG,mEAAAH,MAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAChBF,MAAA,CAAAmC,YAAA,CAAAJ,MAAA,CAAoB;IAAA,EAAC,uBAAAK,kEAAAL,MAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CACtBF,MAAA,CAAAqC,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAC,sBAAAO,iEAAAP,MAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CACrBF,MAAA,CAAAuC,UAAA,CAAAR,MAAA,CAAkB;IAAA,EAAC;IAIhC/C,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAAyG;IACzGF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAAqC;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAA6C;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAoB,MAAA,IAAiD;IAC7EpB,EAD6E,CAAAG,YAAA,EAAM,EAC7E;IACNH,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAU,UAAA,mBAAA8C,kEAAA;MAAAxD,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAyC,YAAA,EAAc;IAAA,EAAC;IACnDzD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IAyB1BD,EAvBA,CAAAI,UAAA,KAAAsD,iDAAA,oBAOc,KAAAC,+CAAA,kBAOyD,KAAAC,+CAAA,kBAIT,KAAAC,+CAAA,kBAK+C;IAY/G7D,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAIJH,EADF,CAAAC,cAAA,eAAsC,kBACM;IACxCD,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAoB,MAAA,eAAO;IACfpB,EADe,CAAAG,YAAA,EAAO,EACb;IACTH,EAAA,CAAAC,cAAA,kBAA2C;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAoB,MAAA,gBAAQ;IAChBpB,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAoB,MAAA,mBAAW;IAGvBpB,EAHuB,CAAAG,YAAA,EAAO,EACjB,EACL,EACF;IAINH,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;IAlHuBH,EAAA,CAAA2B,WAAA,YAAAX,MAAA,CAAA8C,MAAA,CAAwB;IAM3B9D,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAS,MAAA,CAAA+C,OAAA,CAAY;IAU7B/D,EAAA,CAAAM,SAAA,EAAmC;;IASPN,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAAqB,WAAA,8BAAAL,MAAA,CAAAc,eAAA,GAAAP,IAAA,CAAAC,MAAA,OAAuE;IACzExB,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAyB,iBAAA,CAAAT,MAAA,CAAAc,eAAA,GAAAP,IAAA,CAAAyC,QAAA,CAAqC;IACrChE,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAyB,iBAAA,CAAAT,MAAA,CAAAiD,UAAA,CAAAjD,MAAA,CAAAc,eAAA,GAAAoC,SAAA,EAA6C;IAC5ClE,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAiC,kBAAA,KAAAjB,MAAA,CAAAmD,YAAA,CAAAnD,MAAA,CAAAc,eAAA,GAAAsC,KAAA,YAAiD;IAW1EpE,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAc,eAAA,GAAAuC,SAAA,aAA6C;IAW7CrE,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAc,eAAA,GAAAuC,SAAA,aAA6C;IAM1CrE,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAc,eAAA,GAAAI,OAAA,CAA+B;IAK/BlC,EAAA,CAAAM,SAAA,EAAyE;IAAzEN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAc,eAAA,GAAAc,QAAA,IAAA5B,MAAA,CAAAc,eAAA,GAAAc,QAAA,CAAA0B,MAAA,KAAyE;IAoDjDtE,EAAA,CAAAM,SAAA,IAA0B;IAA1BN,EAAA,CAAA2B,WAAA,cAAAX,MAAA,CAAA8C,MAAA,CAA0B;;;;;IAK9D9D,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,qBAAc;IACtBpB,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAoB,MAAA,sBAAe;IAAApB,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAG,YAAA,EAAM,EACF;;;AD9GN,OAAM,MAAOoE,yBAAyB;EAwBpCC,YACUC,MAAc,EACdC,IAAgB;IADhB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IAtBd,KAAAX,OAAO,GAAY,EAAE;IACrB,KAAAY,gBAAgB,GAAG,IAAI;IAEvB,KAAA9C,YAAY,GAAG,CAAC;IAChB,KAAAiC,MAAM,GAAG,KAAK;IACd,KAAAc,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEX,KAAAC,aAAa,GAAmB,EAAE;EAKvC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACb,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACW,aAAa,CAACS,IAAI,CACrB,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAM,GAAGjG,WAAW,CAACkG,MAAM,qBAAqB,CAAC,CAACC,SAAS,CAAC;MACvEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAACvC,OAAO,GAAGqC,QAAQ,CAACE,IAAI;SAC7B,MAAM;UACL,IAAI,CAACC,mBAAmB,EAAE;;QAE5B,IAAI,CAAC5B,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAAC5B,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEA4B,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACxC,OAAO,GAAG,CACb;MACE2C,GAAG,EAAE,SAAS;MACdnF,IAAI,EAAE;QACJmF,GAAG,EAAE,QAAQ;QACbhF,QAAQ,EAAE,qBAAqB;QAC/BsC,QAAQ,EAAE,WAAW;QACrBxC,MAAM,EAAE;OACT;MACDO,QAAQ,EAAE,oEAAoE;MAC9EsC,SAAS,EAAE,OAAO;MAClBnC,OAAO,EAAE,wCAAwC;MACjDgC,SAAS,EAAE,IAAIyC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC7DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzC,KAAK,EAAE,IAAI;MACX2C,QAAQ,EAAE,IAAI;MACdnE,QAAQ,EAAE,CACR;QACE8D,GAAG,EAAE,QAAQ;QACblE,IAAI,EAAE,2BAA2B;QACjCE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEN,GAAG,EAAE,SAAS;MACdnF,IAAI,EAAE;QACJmF,GAAG,EAAE,QAAQ;QACbhF,QAAQ,EAAE,iBAAiB;QAC3BsC,QAAQ,EAAE,gBAAgB;QAC1BxC,MAAM,EAAE;OACT;MACDO,QAAQ,EAAE,oEAAoE;MAC9EsC,SAAS,EAAE,OAAO;MAClBnC,OAAO,EAAE,+CAA+C;MACxDgC,SAAS,EAAE,IAAIyC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC9DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACrEzC,KAAK,EAAE,GAAG;MACV2C,QAAQ,EAAE,IAAI;MACdnE,QAAQ,EAAE,CACR;QACE8D,GAAG,EAAE,QAAQ;QACblE,IAAI,EAAE,sBAAsB;QAC5BE,KAAK,EAAE,GAAG;QACVsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEN,GAAG,EAAE,SAAS;MACdnF,IAAI,EAAE;QACJmF,GAAG,EAAE,QAAQ;QACbhF,QAAQ,EAAE,qBAAqB;QAC/BsC,QAAQ,EAAE,YAAY;QACtBxC,MAAM,EAAE;OACT;MACDO,QAAQ,EAAE,oEAAoE;MAC9EsC,SAAS,EAAE,OAAO;MAClBnC,OAAO,EAAE,yDAAyD;MAClEgC,SAAS,EAAE,IAAIyC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzC,KAAK,EAAE,IAAI;MACX2C,QAAQ,EAAE,IAAI;MACdnE,QAAQ,EAAE,CACR;QACE8D,GAAG,EAAE,QAAQ;QACblE,IAAI,EAAE,uBAAuB;QAC7BE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEN,GAAG,EAAE,SAAS;MACdnF,IAAI,EAAE;QACJmF,GAAG,EAAE,QAAQ;QACbhF,QAAQ,EAAE,mBAAmB;QAC7BsC,QAAQ,EAAE,cAAc;QACxBxC,MAAM,EAAE;OACT;MACDO,QAAQ,EAAE,iEAAiE;MAC3EsC,SAAS,EAAE,OAAO;MAClBnC,OAAO,EAAE,iDAAiD;MAC1DgC,SAAS,EAAE,IAAIyC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzC,KAAK,EAAE,IAAI;MACX2C,QAAQ,EAAE,IAAI;MACdnE,QAAQ,EAAE,CACR;QACE8D,GAAG,EAAE,QAAQ;QACblE,IAAI,EAAE,qBAAqB;QAC3BE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,EACD;MACEN,GAAG,EAAE,SAAS;MACdnF,IAAI,EAAE;QACJmF,GAAG,EAAE,QAAQ;QACbhF,QAAQ,EAAE,mBAAmB;QAC7BsC,QAAQ,EAAE,iBAAiB;QAC3BxC,MAAM,EAAE;OACT;MACDO,QAAQ,EAAE,iEAAiE;MAC3EsC,SAAS,EAAE,OAAO;MAClBnC,OAAO,EAAE,sDAAsD;MAC/DgC,SAAS,EAAE,IAAIyC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEzC,KAAK,EAAE,IAAI;MACX2C,QAAQ,EAAE,IAAI;MACdnE,QAAQ,EAAE,CACR;QACE8D,GAAG,EAAE,QAAQ;QACblE,IAAI,EAAE,wBAAwB;QAC9BE,KAAK,EAAE,IAAI;QACXsE,KAAK,EAAE;OACR;KAEJ,CACF;EACH;EAEA7F,WAAWA,CAACJ,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACc,YAAY,GAAGd,KAAK;IACzB,IAAI,CAAC+C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACmD,SAAS,CAAClG,KAAK,CAAC;IACrBmG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEA5D,YAAYA,CAAA;IACV,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACwD,cAAc,EAAE;IACrBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAX,SAASA,CAAClG,KAAa;IACrB,IAAI,CAACc,YAAY,GAAGd,KAAK;IACzB,IAAI,CAAC+D,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAACyC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACjG,YAAY,GAAG,IAAI,CAACkC,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACS,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmD,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACtE,YAAY,EAAE;;EAEvB;EAEAuE,aAAaA,CAAA;IACX,IAAI,IAAI,CAACnG,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACkD,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmD,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACtE,YAAY,EAAE;;EAEvB;EAGAwE,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACpE,MAAM,EAAE;IAElB,QAAQoE,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACrE,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAACiF,KAAiB;IAC5B,IAAI,IAAI,CAACtD,UAAU,EAAE;IAErB,MAAMwD,MAAM,GAAGF,KAAK,CAACG,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IAErC;IACA,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACN,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACF,SAAS,EAAE;;EAEpB;EAEA3E,YAAYA,CAAC+E,KAAiB;IAC5B,IAAI,CAACrD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGiD,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC1C,IAAI,CAACnD,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEA5B,WAAWA,CAAC6E,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACrD,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGgD,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC5C,IAAI,CAACK,kBAAkB,EAAE;EAC3B;EAEAnF,UAAUA,CAAC2E,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAACrD,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IAEvB,MAAM8D,SAAS,GAAG,IAAI,CAACzD,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAM2D,SAAS,GAAGL,MAAM,CAACC,UAAU,GAAG,IAAI,CAACrD,0BAA0B;IAErE,IAAI0D,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACX,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACF,SAAS,EAAE;;KAEnB,MAAM;MACL;MACA,IAAI,CAAC/C,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmD,MAAM,EAAE;;EAEjB;EAEQW,kBAAkBA,CAAA;IACxB,MAAMC,SAAS,GAAG,IAAI,CAACzD,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAI6D,SAAS,GAAGJ,MAAM,CAACC,UAAU,GAAI,EAAE;IAEnD,IAAI,IAAI,CAACjB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAAC/C,OAAO,MAAM;;EAErD;EAEQiD,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACnD,UAAU,EAAE;IAEtB;IACA,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAE1E;IACA,IAAIwD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChE,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAM+D,QAAQ,GAAG,IAAI,CAAC/D,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACnD,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAACoF,SAAS,CAAC8B,QAAQ,CAAC;QACxB,IAAI,CAAC/D,eAAe,GAAG,IAAI;;MAE7B;;IAGF;IACA,IAAI,IAAI,CAACuC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAAC/C,OAAO,MAAM;;IAGnDkE,qBAAqB,CAAC,MAAM,IAAI,CAACjB,MAAM,EAAE,CAAC;EAC5C;EAEQT,cAAcA,CAAA;IACpB,MAAM2B,MAAM,GAAG/B,QAAQ,CAACgC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAACtD,OAAO,CAAEwD,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAEQ3D,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGFhE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACiC,OAAO,CAAC,IAAI,CAAClC,YAAY,CAAC;EACxC;EAEAwH,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACxH,YAAY,GAAG,CAAC,IAAI,IAAI,CAACkC,OAAO,CAACO,MAAM,GAAI,GAAG;EAC9D;EAEAL,UAAUA,CAACqF,UAAkB;IAC3B,MAAM1C,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM4C,IAAI,GAAG,IAAI5C,IAAI,CAAC2C,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGX,IAAI,CAACY,KAAK,CAAC,CAAC7C,GAAG,CAAC8C,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMG,WAAW,GAAGd,IAAI,CAACY,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGf,IAAI,CAACY,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEAzF,YAAYA,CAAC0F,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAtH,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIsH,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpC7C,KAAK,EAAE,UAAU;MACjB8C,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC1H,KAAK,CAAC;EAClB;EAEAH,WAAWA,CAAC8H,OAAY;IACtB,IAAI,CAAC5F,MAAM,CAAC6F,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC3D,GAAG,CAAC,CAAC;EACjD;;;uBAvZWnC,yBAAyB,EAAAvE,EAAA,CAAAuK,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzK,EAAA,CAAAuK,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAzBpG,yBAAyB;MAAAqG,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAzB/K,EAAA,CAAAU,UAAA,qBAAAuK,qDAAAlI,MAAA;YAAA,OAAAiI,GAAA,CAAA/C,aAAA,CAAAlF,MAAA,CAAqB;UAAA,UAAA/C,EAAA,CAAAkL,iBAAA,CAAI;;;;;;;;;;UCpCtClL,EAAA,CAAAC,cAAA,aAAuB;UAUrBD,EARA,CAAAI,UAAA,IAAA+K,wCAAA,iBAAoD,IAAAC,wCAAA,iBAWzB;UAI7BpL,EAAA,CAAAG,YAAA,EAAM;UAwHNH,EArHA,CAAAI,UAAA,IAAAiL,wCAAA,mBAAqE,IAAAC,wCAAA,iBAqHxB;;;UAvIrCtL,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAAyK,GAAA,CAAArG,gBAAA,CAAsB;UASR3E,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,YAAAyK,GAAA,CAAAjH,OAAA,CAAY;UASqB/D,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAyK,GAAA,CAAAlH,MAAA,CAAY;UAqHpC9D,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAAyK,GAAA,CAAAlH,MAAA,CAAY;;;qBDzG/BhE,YAAY,EAAAyL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}