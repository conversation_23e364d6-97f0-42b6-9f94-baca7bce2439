{"ast": null, "code": "import { BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { map, distinctUntilChanged, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nimport * as i2 from \"./recommendation.service\";\nimport * as i3 from \"./analytics.service\";\nimport * as i4 from \"./cart.service\";\nimport * as i5 from \"./wishlist.service\";\nimport * as i6 from \"./mobile-optimization.service\";\nexport let DataFlowService = /*#__PURE__*/(() => {\n  class DataFlowService {\n    constructor(authService, recommendationService, analyticsService, cartService, wishlistService, mobileService) {\n      this.authService = authService;\n      this.recommendationService = recommendationService;\n      this.analyticsService = analyticsService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.mobileService = mobileService;\n      this.appState$ = new BehaviorSubject(this.getInitialState());\n      this.isLoading$ = new BehaviorSubject(false);\n      this.errors$ = new BehaviorSubject([]);\n      this.initializeDataFlow();\n    }\n    // Public Observables\n    getAppState$() {\n      return this.appState$.asObservable();\n    }\n    getIsLoading$() {\n      return this.isLoading$.asObservable();\n    }\n    getErrors$() {\n      return this.errors$.asObservable();\n    }\n    // Specific State Selectors\n    getUser$() {\n      return this.appState$.pipe(map(state => state.user), distinctUntilChanged());\n    }\n    getRecommendations$() {\n      return this.appState$.pipe(map(state => state.recommendations), distinctUntilChanged());\n    }\n    getUserCounts$() {\n      return this.appState$.pipe(map(state => state.userCounts), distinctUntilChanged());\n    }\n    getUIState$() {\n      return this.appState$.pipe(map(state => state.ui), distinctUntilChanged());\n    }\n    // Data Loading Methods\n    loadUserData(userId) {\n      this.setLoading(true);\n      return this.authService.currentUser$.pipe(map(user => {\n        const userData = {\n          user,\n          userCounts: {\n            cart: 0,\n            wishlist: 0,\n            total: 0\n          }\n        };\n        this.updateState(userData);\n        this.setLoading(false);\n        return userData;\n      }), catchError(error => {\n        this.addError('Failed to load user data');\n        this.setLoading(false);\n        throw error;\n      }));\n    }\n    loadRecommendations(userId) {\n      this.setLoading(true);\n      return combineLatest([this.recommendationService.getSuggestedProducts(userId, 8), this.recommendationService.getTrendingProducts(undefined, 6), this.recommendationService.getCategoryRecommendations('women', 6)]).pipe(map(([suggested, trending, categories]) => {\n        const recommendations = {\n          recommendations: {\n            suggested,\n            trending,\n            categories\n          }\n        };\n        this.updateState(recommendations);\n        this.setLoading(false);\n        return recommendations;\n      }), catchError(error => {\n        this.addError('Failed to load recommendations');\n        this.setLoading(false);\n        throw error;\n      }));\n    }\n    loadAnalytics() {\n      return this.analyticsService.getAnalyticsOverview().pipe(map(analytics => {\n        const analyticsData = {\n          analytics: {\n            userBehavior: analytics.userGrowth || [],\n            searchHistory: analytics.searchTrends || [],\n            viewHistory: []\n          }\n        };\n        this.updateState(analyticsData);\n        return analyticsData;\n      }), catchError(error => {\n        this.addError('Failed to load analytics');\n        throw error;\n      }));\n    }\n    // User Actions\n    trackUserAction(action, data) {\n      const currentState = this.appState$.value;\n      // Track in analytics\n      this.analyticsService.trackUserBehavior(action, data).subscribe();\n      // Update local analytics\n      const updatedAnalytics = {\n        ...currentState.analytics,\n        userBehavior: [...currentState.analytics.userBehavior, {\n          action,\n          data,\n          timestamp: new Date()\n        }].slice(-100) // Keep last 100 actions\n      };\n      this.updateState({\n        analytics: updatedAnalytics\n      });\n    }\n    addToCart(productId, quantity = 1) {\n      this.trackUserAction('add_to_cart', {\n        productId,\n        quantity\n      });\n      return of({\n        success: true,\n        productId,\n        quantity\n      });\n    }\n    addToWishlist(productId) {\n      this.trackUserAction('add_to_wishlist', {\n        productId\n      });\n      return of({\n        success: true,\n        productId\n      });\n    }\n    removeFromCart(productId) {\n      this.trackUserAction('remove_from_cart', {\n        productId\n      });\n      return of({\n        success: true,\n        productId\n      });\n    }\n    removeFromWishlist(productId) {\n      this.trackUserAction('remove_from_wishlist', {\n        productId\n      });\n      return of({\n        success: true,\n        productId\n      });\n    }\n    // Search and Navigation\n    performSearch(query, category) {\n      this.trackUserAction('search', {\n        query,\n        category\n      });\n      // Track search in recommendations service\n      this.recommendationService.trackSearch(query, category, 0).subscribe();\n      // Update search history\n      const currentState = this.appState$.value;\n      const updatedAnalytics = {\n        ...currentState.analytics,\n        searchHistory: [{\n          query,\n          category,\n          timestamp: new Date()\n        }, ...currentState.analytics.searchHistory].slice(0, 50) // Keep last 50 searches\n      };\n      this.updateState({\n        analytics: updatedAnalytics\n      });\n    }\n    trackProductView(productId, category, duration = 0) {\n      this.trackUserAction('product_view', {\n        productId,\n        category,\n        duration\n      });\n      // Track in recommendations service\n      this.recommendationService.trackProductView(productId, category, duration).subscribe();\n      // Update view history\n      const currentState = this.appState$.value;\n      const updatedAnalytics = {\n        ...currentState.analytics,\n        viewHistory: [{\n          productId,\n          category,\n          duration,\n          timestamp: new Date()\n        }, ...currentState.analytics.viewHistory].slice(0, 100) // Keep last 100 views\n      };\n      this.updateState({\n        analytics: updatedAnalytics\n      });\n    }\n    // Private Methods\n    initializeDataFlow() {\n      // Initialize with device info\n      this.mobileService.getDeviceInfo$().subscribe(deviceInfo => {\n        this.updateState({\n          ui: {\n            isMobile: deviceInfo.isMobile,\n            isTablet: deviceInfo.isTablet,\n            isDesktop: deviceInfo.isDesktop,\n            currentBreakpoint: this.mobileService.getCurrentBreakpoint(),\n            isKeyboardOpen: false\n          }\n        });\n      });\n      // Listen to keyboard state\n      this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n        const currentState = this.appState$.value;\n        this.updateState({\n          ui: {\n            ...currentState.ui,\n            isKeyboardOpen: isOpen\n          }\n        });\n      });\n      // Listen to auth changes\n      this.authService.currentUser$.subscribe(user => {\n        this.updateState({\n          user\n        });\n        if (user) {\n          // Load user-specific data\n          this.loadUserData(user._id).subscribe();\n          this.loadRecommendations(user._id).subscribe();\n        } else {\n          // Reset user-specific data\n          this.updateState({\n            userCounts: {\n              cart: 0,\n              wishlist: 0,\n              total: 0\n            },\n            recommendations: {\n              suggested: [],\n              trending: [],\n              categories: []\n            }\n          });\n        }\n      });\n    }\n    updateUserCounts() {\n      const userCounts = {\n        cart: 0,\n        wishlist: 0,\n        total: 0\n      };\n      this.updateState({\n        userCounts\n      });\n      return of(userCounts);\n    }\n    updateState(partialState) {\n      const currentState = this.appState$.value;\n      const newState = {\n        ...currentState,\n        ...partialState\n      };\n      this.appState$.next(newState);\n    }\n    setLoading(loading) {\n      this.isLoading$.next(loading);\n    }\n    addError(error) {\n      const currentErrors = this.errors$.value;\n      this.errors$.next([...currentErrors, error]);\n      // Auto-remove error after 5 seconds\n      setTimeout(() => {\n        const errors = this.errors$.value;\n        const index = errors.indexOf(error);\n        if (index > -1) {\n          errors.splice(index, 1);\n          this.errors$.next([...errors]);\n        }\n      }, 5000);\n    }\n    getInitialState() {\n      return {\n        user: null,\n        deviceInfo: null,\n        recommendations: {\n          suggested: [],\n          trending: [],\n          categories: []\n        },\n        userCounts: {\n          cart: 0,\n          wishlist: 0,\n          total: 0\n        },\n        analytics: {\n          userBehavior: [],\n          searchHistory: [],\n          viewHistory: []\n        },\n        ui: {\n          isMobile: false,\n          isTablet: false,\n          isDesktop: true,\n          currentBreakpoint: 'lg',\n          isKeyboardOpen: false\n        }\n      };\n    }\n    // Cleanup\n    destroy() {\n      this.appState$.complete();\n      this.isLoading$.complete();\n      this.errors$.complete();\n    }\n    static {\n      this.ɵfac = function DataFlowService_Factory(t) {\n        return new (t || DataFlowService)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.RecommendationService), i0.ɵɵinject(i3.AnalyticsService), i0.ɵɵinject(i4.CartService), i0.ɵɵinject(i5.WishlistService), i0.ɵɵinject(i6.MobileOptimizationService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: DataFlowService,\n        factory: DataFlowService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return DataFlowService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}