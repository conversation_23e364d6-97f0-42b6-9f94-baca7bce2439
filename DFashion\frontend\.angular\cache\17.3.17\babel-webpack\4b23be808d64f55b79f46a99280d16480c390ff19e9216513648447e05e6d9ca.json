{"ast": null, "code": "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n  if (opts.useNativeTimers) {\n    obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n    obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n  } else {\n    obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n    obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n  }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n  if (typeof obj === \"string\") {\n    return utf8Length(obj);\n  }\n  // arraybuffer or blob\n  return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n  let c = 0,\n    length = 0;\n  for (let i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    } else if (c < 0x800) {\n      length += 2;\n    } else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    } else {\n      i++;\n      length += 4;\n    }\n  }\n  return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n  return Date.now().toString(36).substring(3) + Math.random().toString(36).substring(2, 5);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}