{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 5,\n        vars: 0,\n        consts: [[1, \"profile-container\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n            i0.ɵɵtext(2, \"Profile Page\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"p\");\n            i0.ɵɵtext(4, \"Profile functionality coming soon...\");\n            i0.ɵɵelementEnd()();\n          }\n        },\n        dependencies: [CommonModule],\n        styles: [\".profile-container[_ngcontent-%COMP%]{padding:40px 20px;text-align:center}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}