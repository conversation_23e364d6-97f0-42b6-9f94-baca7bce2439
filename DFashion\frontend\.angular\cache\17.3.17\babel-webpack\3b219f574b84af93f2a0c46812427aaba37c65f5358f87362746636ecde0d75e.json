{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/role-management.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction RoleBasedSettingsComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function RoleBasedSettingsComponent_button_13_Template_button_click_0_listener() {\n      const section_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveSection(section_r2.id));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const section_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeSection === section_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(section_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r2.title);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(setting_r4.description);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_div_6_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_div_6_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"label\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", setting_r4.id);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_select_7_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r7.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r7.label, \" \");\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_select_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"select\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_select_7_Template_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_select_7_Template_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵtemplate(1, RoleBasedSettingsComponent_div_15_div_7_select_7_option_1_Template, 2, 2, \"option\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", setting_r4.options);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_input_8_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function RoleBasedSettingsComponent_div_15_div_7_input_8_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵproperty(\"placeholder\", setting_r4.placeholder);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_div_9_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function RoleBasedSettingsComponent_div_15_div_7_div_9_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵproperty(\"min\", setting_r4.min)(\"max\", setting_r4.max)(\"step\", setting_r4.step);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r4.value);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_input_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_input_10_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_input_10_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_11_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getFileName(setting_r4.value));\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"input\", 45);\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_div_11_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFileChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 46);\n    i0.ɵɵelement(3, \"i\", 47);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Choose File\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, RoleBasedSettingsComponent_div_15_div_7_div_11_span_6_Template, 2, 1, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", setting_r4.id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", setting_r4.value);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template_textarea_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵtext(1, \"                \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵproperty(\"placeholder\", setting_r4.placeholder);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"label\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, RoleBasedSettingsComponent_div_15_div_7_p_4_Template, 2, 1, \"p\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 24);\n    i0.ɵɵtemplate(6, RoleBasedSettingsComponent_div_15_div_7_div_6_Template, 3, 3, \"div\", 25)(7, RoleBasedSettingsComponent_div_15_div_7_select_7_Template, 2, 3, \"select\", 26)(8, RoleBasedSettingsComponent_div_15_div_7_input_8_Template, 1, 3, \"input\", 27)(9, RoleBasedSettingsComponent_div_15_div_7_div_9_Template, 4, 6, \"div\", 28)(10, RoleBasedSettingsComponent_div_15_div_7_input_10_Template, 1, 2, \"input\", 29)(11, RoleBasedSettingsComponent_div_15_div_7_div_11_Template, 7, 3, \"div\", 30)(12, RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template, 2, 3, \"textarea\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", setting_r4.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(setting_r4.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"toggle\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"select\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"input\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"range\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"color\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"file\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"textarea\");\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 18);\n    i0.ɵɵtemplate(7, RoleBasedSettingsComponent_div_15_div_7_Template, 13, 10, \"div\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const section_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeSection === section_r13.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(section_r13.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r13.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", section_r13.settings);\n  }\n}\nexport let RoleBasedSettingsComponent = /*#__PURE__*/(() => {\n  class RoleBasedSettingsComponent {\n    constructor(roleManagementService) {\n      this.roleManagementService = roleManagementService;\n      this.currentRole = null;\n      this.roleConfig = null;\n      this.activeSection = 'profile';\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      if (this.currentRole) {\n        this.roleConfig = this.roleManagementService.getRoleConfig(this.currentRole);\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    getAvailableSections() {\n      if (!this.currentRole) return [];\n      const baseSections = [{\n        id: 'profile',\n        title: 'Profile',\n        icon: 'fas fa-user',\n        description: 'Manage your personal profile information',\n        settings: [{\n          id: 'displayName',\n          type: 'input',\n          label: 'Display Name',\n          description: 'Your name as it appears to others',\n          value: '',\n          placeholder: 'Enter your display name'\n        }, {\n          id: 'bio',\n          type: 'textarea',\n          label: 'Bio',\n          description: 'Tell others about yourself',\n          value: '',\n          placeholder: 'Write a short bio...'\n        }, {\n          id: 'avatar',\n          type: 'file',\n          label: 'Profile Picture',\n          description: 'Upload a profile picture',\n          value: null\n        }, {\n          id: 'publicProfile',\n          type: 'toggle',\n          label: 'Public Profile',\n          description: 'Make your profile visible to everyone',\n          value: true\n        }]\n      }, {\n        id: 'notifications',\n        title: 'Notifications',\n        icon: 'fas fa-bell',\n        description: 'Configure your notification preferences',\n        settings: [{\n          id: 'emailNotifications',\n          type: 'toggle',\n          label: 'Email Notifications',\n          description: 'Receive notifications via email',\n          value: true\n        }, {\n          id: 'pushNotifications',\n          type: 'toggle',\n          label: 'Push Notifications',\n          description: 'Receive push notifications in browser',\n          value: true\n        }, {\n          id: 'notificationFrequency',\n          type: 'select',\n          label: 'Notification Frequency',\n          description: 'How often to receive notifications',\n          value: 'immediate',\n          options: [{\n            label: 'Immediate',\n            value: 'immediate'\n          }, {\n            label: 'Hourly',\n            value: 'hourly'\n          }, {\n            label: 'Daily',\n            value: 'daily'\n          }, {\n            label: 'Weekly',\n            value: 'weekly'\n          }]\n        }]\n      }, {\n        id: 'privacy',\n        title: 'Privacy',\n        icon: 'fas fa-shield-alt',\n        description: 'Control your privacy and security settings',\n        settings: [{\n          id: 'profileVisibility',\n          type: 'select',\n          label: 'Profile Visibility',\n          description: 'Who can see your profile',\n          value: 'team',\n          options: [{\n            label: 'Everyone',\n            value: 'public'\n          }, {\n            label: 'Team Members',\n            value: 'team'\n          }, {\n            label: 'Department Only',\n            value: 'department'\n          }, {\n            label: 'Private',\n            value: 'private'\n          }]\n        }, {\n          id: 'showOnlineStatus',\n          type: 'toggle',\n          label: 'Show Online Status',\n          description: 'Let others see when you\\'re online',\n          value: true\n        }, {\n          id: 'allowDirectMessages',\n          type: 'toggle',\n          label: 'Allow Direct Messages',\n          description: 'Allow others to send you direct messages',\n          value: true\n        }]\n      }];\n      // Add role-specific sections\n      const roleSpecificSections = this.getRoleSpecificSections();\n      return [...baseSections, ...roleSpecificSections];\n    }\n    getRoleSpecificSections() {\n      if (!this.currentRole) return [];\n      const sections = [];\n      // Admin-specific settings\n      if (this.currentRole === 'super_admin' || this.currentRole === 'admin') {\n        sections.push({\n          id: 'system',\n          title: 'System',\n          icon: 'fas fa-cogs',\n          description: 'System-wide configuration settings',\n          requiredPermission: 'system.manage',\n          settings: [{\n            id: 'maintenanceMode',\n            type: 'toggle',\n            label: 'Maintenance Mode',\n            description: 'Enable maintenance mode for the system',\n            value: false\n          }, {\n            id: 'userRegistration',\n            type: 'toggle',\n            label: 'User Registration',\n            description: 'Allow new user registrations',\n            value: true\n          }, {\n            id: 'sessionTimeout',\n            type: 'range',\n            label: 'Session Timeout (minutes)',\n            description: 'Automatic logout after inactivity',\n            value: 30,\n            min: 5,\n            max: 480,\n            step: 5\n          }]\n        });\n      }\n      // Manager-specific settings\n      if (this.roleManagementService.isManager(this.currentRole)) {\n        sections.push({\n          id: 'team',\n          title: 'Team Management',\n          icon: 'fas fa-users',\n          description: 'Manage your team settings and preferences',\n          settings: [{\n            id: 'teamVisibility',\n            type: 'select',\n            label: 'Team Visibility',\n            description: 'Who can see your team information',\n            value: 'department',\n            options: [{\n              label: 'Public',\n              value: 'public'\n            }, {\n              label: 'Department',\n              value: 'department'\n            }, {\n              label: 'Team Only',\n              value: 'team'\n            }]\n          }, {\n            id: 'autoAssignTasks',\n            type: 'toggle',\n            label: 'Auto-assign Tasks',\n            description: 'Automatically assign tasks to team members',\n            value: false\n          }, {\n            id: 'teamReportFrequency',\n            type: 'select',\n            label: 'Team Report Frequency',\n            description: 'How often to generate team reports',\n            value: 'weekly',\n            options: [{\n              label: 'Daily',\n              value: 'daily'\n            }, {\n              label: 'Weekly',\n              value: 'weekly'\n            }, {\n              label: 'Monthly',\n              value: 'monthly'\n            }]\n          }]\n        });\n      }\n      // Department-specific settings\n      const departmentSections = this.getDepartmentSpecificSections();\n      sections.push(...departmentSections);\n      return sections;\n    }\n    getDepartmentSpecificSections() {\n      if (!this.roleConfig) return [];\n      const sections = [];\n      switch (this.roleConfig.department) {\n        case 'sales':\n          sections.push({\n            id: 'sales',\n            title: 'Sales Settings',\n            icon: 'fas fa-chart-line',\n            description: 'Configure sales-specific preferences',\n            settings: [{\n              id: 'salesTarget',\n              type: 'input',\n              label: 'Monthly Sales Target',\n              description: 'Your monthly sales target amount',\n              value: '',\n              placeholder: 'Enter target amount'\n            }, {\n              id: 'commissionRate',\n              type: 'range',\n              label: 'Commission Rate (%)',\n              description: 'Your commission rate percentage',\n              value: 5,\n              min: 0,\n              max: 20,\n              step: 0.5\n            }, {\n              id: 'followUpReminders',\n              type: 'toggle',\n              label: 'Follow-up Reminders',\n              description: 'Get reminders for customer follow-ups',\n              value: true\n            }]\n          });\n          break;\n        case 'marketing':\n          sections.push({\n            id: 'marketing',\n            title: 'Marketing Settings',\n            icon: 'fas fa-bullhorn',\n            description: 'Configure marketing-specific preferences',\n            settings: [{\n              id: 'campaignBudget',\n              type: 'input',\n              label: 'Monthly Campaign Budget',\n              description: 'Your monthly campaign budget',\n              value: '',\n              placeholder: 'Enter budget amount'\n            }, {\n              id: 'contentApproval',\n              type: 'toggle',\n              label: 'Content Auto-approval',\n              description: 'Automatically approve content you create',\n              value: false\n            }, {\n              id: 'socialPlatforms',\n              type: 'select',\n              label: 'Primary Social Platform',\n              description: 'Your primary social media platform',\n              value: 'instagram',\n              options: [{\n                label: 'Instagram',\n                value: 'instagram'\n              }, {\n                label: 'Facebook',\n                value: 'facebook'\n              }, {\n                label: 'Twitter',\n                value: 'twitter'\n              }, {\n                label: 'LinkedIn',\n                value: 'linkedin'\n              }]\n            }]\n          });\n          break;\n        case 'support':\n          sections.push({\n            id: 'support',\n            title: 'Support Settings',\n            icon: 'fas fa-headset',\n            description: 'Configure support-specific preferences',\n            settings: [{\n              id: 'ticketAutoAssign',\n              type: 'toggle',\n              label: 'Auto-assign Tickets',\n              description: 'Automatically assign tickets to you',\n              value: true\n            }, {\n              id: 'responseTimeTarget',\n              type: 'range',\n              label: 'Response Time Target (hours)',\n              description: 'Your target response time for tickets',\n              value: 2,\n              min: 1,\n              max: 24,\n              step: 1\n            }, {\n              id: 'escalationThreshold',\n              type: 'range',\n              label: 'Escalation Threshold (hours)',\n              description: 'Auto-escalate tickets after this time',\n              value: 8,\n              min: 1,\n              max: 48,\n              step: 1\n            }]\n          });\n          break;\n      }\n      return sections;\n    }\n    setActiveSection(sectionId) {\n      this.activeSection = sectionId;\n    }\n    onSettingChange(setting, event) {\n      const value = event.target ? event.target.value : event;\n      setting.value = setting.type === 'toggle' ? event.target.checked : value;\n      if (setting.onChange) {\n        setting.onChange(setting.value);\n      }\n    }\n    onFileChange(setting, event) {\n      const file = event.target.files[0];\n      if (file) {\n        setting.value = file;\n        if (setting.onChange) {\n          setting.onChange(file);\n        }\n      }\n    }\n    getFileName(file) {\n      return file ? file.name : '';\n    }\n    resetToDefaults() {\n      // Reset all settings to default values\n      console.log('Resetting to defaults...');\n    }\n    saveSettings() {\n      // Save all settings\n      console.log('Saving settings...');\n      // Show success message\n      this.showSuccessMessage('Settings saved successfully!');\n    }\n    showSuccessMessage(message) {\n      // Implementation for showing success message\n      console.log(message);\n    }\n    static {\n      this.ɵfac = function RoleBasedSettingsComponent_Factory(t) {\n        return new (t || RoleBasedSettingsComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RoleBasedSettingsComponent,\n        selectors: [[\"app-role-based-settings\"]],\n        inputs: {\n          currentRole: \"currentRole\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 23,\n        vars: 11,\n        consts: [[1, \"role-based-settings\"], [1, \"settings-header\"], [1, \"header-content\"], [1, \"role-indicator\"], [1, \"header-text\"], [1, \"settings-navigation\"], [1, \"nav-tabs\"], [\"class\", \"nav-tab\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"settings-content\"], [\"class\", \"settings-section\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"settings-actions\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-save\"], [1, \"nav-tab\", 3, \"click\"], [1, \"settings-section\"], [1, \"section-header\"], [1, \"settings-grid\"], [\"class\", \"setting-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"setting-item\"], [1, \"setting-info\"], [1, \"setting-label\", 3, \"for\"], [\"class\", \"setting-description\", 4, \"ngIf\"], [1, \"setting-control\"], [\"class\", \"toggle-switch\", 4, \"ngIf\"], [\"class\", \"select-input\", 3, \"id\", \"ngModel\", \"ngModelChange\", \"change\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"text-input\", 3, \"id\", \"ngModel\", \"placeholder\", \"ngModelChange\", \"input\", 4, \"ngIf\"], [\"class\", \"range-container\", 4, \"ngIf\"], [\"type\", \"color\", \"class\", \"color-input\", 3, \"id\", \"ngModel\", \"ngModelChange\", \"change\", 4, \"ngIf\"], [\"class\", \"file-upload\", 4, \"ngIf\"], [\"class\", \"textarea-input\", \"rows\", \"4\", 3, \"id\", \"ngModel\", \"placeholder\", \"ngModelChange\", \"input\", 4, \"ngIf\"], [1, \"setting-description\"], [1, \"toggle-switch\"], [\"type\", \"checkbox\", 1, \"toggle-input\", 3, \"ngModelChange\", \"change\", \"id\", \"ngModel\"], [1, \"toggle-label\", 3, \"for\"], [1, \"select-input\", 3, \"ngModelChange\", \"change\", \"id\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"type\", \"text\", 1, \"text-input\", 3, \"ngModelChange\", \"input\", \"id\", \"ngModel\", \"placeholder\"], [1, \"range-container\"], [\"type\", \"range\", 1, \"range-input\", 3, \"ngModelChange\", \"input\", \"id\", \"ngModel\", \"min\", \"max\", \"step\"], [1, \"range-value\"], [\"type\", \"color\", 1, \"color-input\", 3, \"ngModelChange\", \"change\", \"id\", \"ngModel\"], [1, \"file-upload\"], [\"type\", \"file\", \"hidden\", \"\", 1, \"file-input\", 3, \"change\", \"id\"], [1, \"file-label\", 3, \"for\"], [1, \"fas\", \"fa-upload\"], [\"class\", \"file-name\", 4, \"ngIf\"], [1, \"file-name\"], [\"rows\", \"4\", 1, \"textarea-input\", 3, \"ngModelChange\", \"input\", \"id\", \"ngModel\", \"placeholder\"]],\n        template: function RoleBasedSettingsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"i\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"h1\");\n            i0.ɵɵtext(7, \"Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\");\n            i0.ɵɵtext(9);\n            i0.ɵɵpipe(10, \"titlecase\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(11, \"div\", 5)(12, \"div\", 6);\n            i0.ɵɵtemplate(13, RoleBasedSettingsComponent_button_13_Template, 4, 5, \"button\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 8);\n            i0.ɵɵtemplate(15, RoleBasedSettingsComponent_div_15_Template, 8, 5, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 10)(17, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function RoleBasedSettingsComponent_Template_button_click_17_listener() {\n              return ctx.resetToDefaults();\n            });\n            i0.ɵɵelement(18, \"i\", 12);\n            i0.ɵɵtext(19, \" Reset to Defaults \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function RoleBasedSettingsComponent_Template_button_click_20_listener() {\n              return ctx.saveSettings();\n            });\n            i0.ɵɵelement(21, \"i\", 14);\n            i0.ɵɵtext(22, \" Save Changes \");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"data-role\", ctx.currentRole);\n            i0.ɵɵadvance(3);\n            i0.ɵɵstyleProp(\"background\", ctx.roleConfig == null ? null : ctx.roleConfig.color);\n            i0.ɵɵadvance();\n            i0.ɵɵclassMap(ctx.roleConfig == null ? null : ctx.roleConfig.icon);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate2(\"\", ctx.roleConfig == null ? null : ctx.roleConfig.displayName, \" - \", i0.ɵɵpipeBind1(10, 9, ctx.roleConfig == null ? null : ctx.roleConfig.department), \"\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getAvailableSections());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getAvailableSections());\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, FormsModule, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.RangeValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n        styles: [\".role-based-settings[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:2rem;background:#fafafa;min-height:100vh}.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:2rem;margin-bottom:2rem;box-shadow:0 2px 12px #0000000f}.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .role-indicator[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.5rem;box-shadow:0 4px 12px #00000026}.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:2rem;font-weight:700;color:#262626}.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.25rem 0 0;color:#8e8e8e;font-size:1rem}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1rem;margin-bottom:2rem;box-shadow:0 2px 12px #0000000f}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]{display:flex;gap:.5rem;overflow-x:auto;padding-bottom:.5rem}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar{height:4px}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:2px}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:2px}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.5rem;border:none;background:#f8f9fa;color:#6c757d;border-radius:12px;cursor:pointer;transition:all .3s ease;white-space:nowrap;font-weight:500}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;box-shadow:0 4px 12px #667eea4d}.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]{position:relative}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]{display:none;background:#fff;border-radius:16px;padding:2rem;box-shadow:0 2px 12px #0000000f}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section.active[_ngcontent-%COMP%]{display:block}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-bottom:2rem;padding-bottom:1rem;border-bottom:1px solid #f0f0f0}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.5rem;font-weight:600;color:#262626}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#8e8e8e;font-size:.95rem}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]{display:grid;gap:2rem}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr auto;gap:1rem;align-items:start;padding:1.5rem;border:1px solid #f0f0f0;border-radius:12px;transition:all .3s ease}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]:hover{border-color:#e0e0e0;box-shadow:0 2px 8px #0000000a}@media (max-width: 768px){.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-info[_ngcontent-%COMP%]   .setting-label[_ngcontent-%COMP%]{display:block;font-weight:600;color:#262626;margin-bottom:.25rem;font-size:.95rem}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-info[_ngcontent-%COMP%]   .setting-description[_ngcontent-%COMP%]{margin:0;color:#8e8e8e;font-size:.85rem;line-height:1.4}.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-control[_ngcontent-%COMP%]{min-width:200px}@media (max-width: 768px){.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-control[_ngcontent-%COMP%]{min-width:auto;width:100%}}.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]{position:relative;display:inline-block;width:50px;height:24px}.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%]{opacity:0;width:0;height:0}.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%]:checked + .toggle-label[_ngcontent-%COMP%]{background-color:#4caf50}.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%]:checked + .toggle-label[_ngcontent-%COMP%]:before{transform:translate(26px)}.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-label[_ngcontent-%COMP%]{position:absolute;cursor:pointer;inset:0;background-color:#ccc;border-radius:24px;transition:.3s}.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-label[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";height:18px;width:18px;left:3px;bottom:3px;background-color:#fff;border-radius:50%;transition:.3s;box-shadow:0 2px 4px #0003}.role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%]{width:100%;padding:.75rem;border:1px solid #ddd;border-radius:8px;font-size:.9rem;transition:all .3s ease}.role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-input[_ngcontent-%COMP%]{flex:1;height:6px;border-radius:3px;background:#ddd;outline:none;-webkit-appearance:none}.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-input[_ngcontent-%COMP%]::-webkit-slider-thumb{appearance:none;width:18px;height:18px;border-radius:50%;background:#667eea;cursor:pointer;box-shadow:0 2px 4px #0003}.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-input[_ngcontent-%COMP%]::-moz-range-thumb{width:18px;height:18px;border-radius:50%;background:#667eea;cursor:pointer;border:none;box-shadow:0 2px 4px #0003}.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-value[_ngcontent-%COMP%]{min-width:40px;text-align:center;font-weight:600;color:#667eea;background:#f8f9ff;padding:.25rem .5rem;border-radius:6px;font-size:.85rem}.role-based-settings[_ngcontent-%COMP%]   .color-input[_ngcontent-%COMP%]{width:50px;height:40px;border:none;border-radius:8px;cursor:pointer;box-shadow:0 2px 8px #0000001a}.role-based-settings[_ngcontent-%COMP%]   .color-input[_ngcontent-%COMP%]::-webkit-color-swatch-wrapper{padding:0}.role-based-settings[_ngcontent-%COMP%]   .color-input[_ngcontent-%COMP%]::-webkit-color-swatch{border:none;border-radius:8px}.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem;background:#f8f9fa;border:1px dashed #ddd;border-radius:8px;cursor:pointer;transition:all .3s ease;font-size:.9rem;color:#6c757d}.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]:hover{background:#e9ecef;border-color:#adb5bd}.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{font-size:.85rem;color:#28a745;font-weight:500}.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:1rem;margin-top:2rem;padding:1.5rem;background:#fff;border-radius:16px;box-shadow:0 2px 12px #0000000f}@media (max-width: 768px){.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]{flex-direction:column}}.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.5rem;border:none;border-radius:8px;font-weight:600;cursor:pointer;transition:all .3s ease;font-size:.9rem}.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6}.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;box-shadow:0 4px 12px #667eea4d}.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 16px #667eea66}.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}@media (prefers-color-scheme: dark){.role-based-settings[_ngcontent-%COMP%]{background:#121212}.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]{background:#1e1e1e;color:#fff}.role-based-settings[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .setting-label[_ngcontent-%COMP%]{color:#fff}.role-based-settings[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .setting-description[_ngcontent-%COMP%]{color:#b3b3b3}.role-based-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]{border-color:#333}.role-based-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]:hover{border-color:#444}.role-based-settings[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]{background:#2a2a2a;color:#b3b3b3}.role-based-settings[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]:hover{background:#333;color:#fff}.role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%]{background:#2a2a2a;border-color:#444;color:#fff}.role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%]:focus{border-color:#667eea}.role-based-settings[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]{background:#2a2a2a;border-color:#444;color:#b3b3b3}.role-based-settings[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]:hover{background:#333}}\"]\n      });\n    }\n  }\n  return RoleBasedSettingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}