{"ast": null, "code": "import { AuthGuard } from './core/guards/auth.guard';\nexport const routes = [\n// Home Route (Public)\n{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n},\n// Authentication Routes\n{\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n},\n// Home with Auth\n{\n  path: 'home',\n  loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n  canActivate: [AuthGuard]\n},\n// Products Routes\n{\n  path: 'products',\n  children: [{\n    path: '',\n    loadComponent: () => import('./features/products/product-list/product-list.component').then(m => m.ProductListComponent),\n    title: 'Products - DFashion'\n  }, {\n    path: ':id',\n    loadComponent: () => import('./features/products/product-detail/product-detail.component').then(m => m.ProductDetailComponent),\n    title: 'Product Details - DFashion'\n  }]\n},\n// Shopping Cart & Wishlist\n{\n  path: 'cart',\n  loadComponent: () => import('./features/cart/cart.component').then(m => m.CartComponent),\n  canActivate: [AuthGuard],\n  title: 'Shopping Cart - DFashion'\n}, {\n  path: 'wishlist',\n  loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),\n  canActivate: [AuthGuard],\n  title: 'Wishlist - DFashion'\n},\n// Checkout Process\n{\n  path: 'checkout',\n  loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n  canActivate: [AuthGuard],\n  title: 'Checkout - DFashion'\n},\n// User Account Management\n{\n  path: 'account',\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'profile',\n    pathMatch: 'full'\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes)\n  }, {\n    path: 'orders',\n    loadComponent: () => import('./features/account/orders/orders.component').then(m => m.OrdersComponent),\n    title: 'My Orders - DFashion'\n  }]\n},\n// Vendor Dashboard\n{\n  path: 'vendor',\n  canActivate: [AuthGuard],\n  children: [{\n    path: 'dashboard',\n    loadComponent: () => import('./features/vendor/dashboard/vendor-dashboard.component').then(m => m.VendorDashboardComponent),\n    title: 'Vendor Dashboard - DFashion'\n  }]\n},\n// Legacy Routes (maintain compatibility)\n{\n  path: 'shop',\n  loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'search',\n  loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n}, {\n  path: 'stories',\n  loadChildren: () => import('./features/story/story.routes').then(m => m.storyRoutes)\n}, {\n  path: 'product/:id',\n  redirectTo: 'products/:id'\n},\n// Admin Routes\n{\n  path: 'admin',\n  loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n},\n// Support & Help\n{\n  path: 'support',\n  loadComponent: () => import('./features/support/support.component').then(m => m.SupportComponent),\n  title: 'Support - DFashion'\n},\n// Wildcard route\n{\n  path: '**',\n  redirectTo: '/'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "authRoutes", "homeRoutes", "canActivate", "children", "loadComponent", "ProductListComponent", "title", "ProductDetailComponent", "CartComponent", "WishlistComponent", "CheckoutComponent", "profileRoutes", "OrdersComponent", "VendorDashboardComponent", "shopRoutes", "searchRoutes", "storyRoutes", "AdminModule", "SupportComponent"], "sources": ["E:\\DFashion\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\n\nexport const routes: Routes = [\n  // Home Route (Public)\n  {\n    path: '',\n    redirectTo: '/home',\n    pathMatch: 'full'\n  },\n\n  // Authentication Routes\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n  },\n\n  // Home with Auth\n  {\n    path: 'home',\n    loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n    canActivate: [AuthGuard]\n  },\n\n  // Products Routes\n  {\n    path: 'products',\n    children: [\n      {\n        path: '',\n        loadComponent: () => import('./features/products/product-list/product-list.component').then(m => m.ProductListComponent),\n        title: 'Products - DFashion'\n      },\n      {\n        path: ':id',\n        loadComponent: () => import('./features/products/product-detail/product-detail.component').then(m => m.ProductDetailComponent),\n        title: 'Product Details - DFashion'\n      }\n    ]\n  },\n\n  // Shopping Cart & Wishlist\n  {\n    path: 'cart',\n    loadComponent: () => import('./features/cart/cart.component').then(m => m.CartComponent),\n    canActivate: [AuthGuard],\n    title: 'Shopping Cart - DFashion'\n  },\n  {\n    path: 'wishlist',\n    loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),\n    canActivate: [AuthGuard],\n    title: 'Wishlist - DFashion'\n  },\n\n  // Checkout Process\n  {\n    path: 'checkout',\n    loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n    canActivate: [AuthGuard],\n    title: 'Checkout - DFashion'\n  },\n\n  // User Account Management\n  {\n    path: 'account',\n    canActivate: [AuthGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'profile',\n        pathMatch: 'full'\n      },\n      {\n        path: 'profile',\n        loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes)\n      },\n      {\n        path: 'orders',\n        loadComponent: () => import('./features/account/orders/orders.component').then(m => m.OrdersComponent),\n        title: 'My Orders - DFashion'\n      }\n    ]\n  },\n\n  // Vendor Dashboard\n  {\n    path: 'vendor',\n    canActivate: [AuthGuard],\n    children: [\n      {\n        path: 'dashboard',\n        loadComponent: () => import('./features/vendor/dashboard/vendor-dashboard.component').then(m => m.VendorDashboardComponent),\n        title: 'Vendor Dashboard - DFashion'\n      }\n    ]\n  },\n\n  // Legacy Routes (maintain compatibility)\n  {\n    path: 'shop',\n    loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'search',\n    loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n  },\n  {\n    path: 'stories',\n    loadChildren: () => import('./features/story/story.routes').then(m => m.storyRoutes)\n  },\n  {\n    path: 'product/:id',\n    redirectTo: 'products/:id'\n  },\n\n  // Admin Routes\n  {\n    path: 'admin',\n    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n  },\n\n  // Support & Help\n  {\n    path: 'support',\n    loadComponent: () => import('./features/support/support.component').then(m => m.SupportComponent),\n    title: 'Support - DFashion'\n  },\n\n  // Wildcard route\n  {\n    path: '**',\n    redirectTo: '/'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,0BAA0B;AAEpD,OAAO,MAAMC,MAAM,GAAW;AAC5B;AACA;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ;AAED;AACA;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF;AAED;AACA;EACEN,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,UAAU,CAAC;EACjFC,WAAW,EAAE,CAACV,SAAS;CACxB;AAED;AACA;EACEE,IAAI,EAAE,UAAU;EAChBS,QAAQ,EAAE,CACR;IACET,IAAI,EAAE,EAAE;IACRU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yDAAyD,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,oBAAoB,CAAC;IACxHC,KAAK,EAAE;GACR,EACD;IACEZ,IAAI,EAAE,KAAK;IACXU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6DAA6D,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,sBAAsB,CAAC;IAC9HD,KAAK,EAAE;GACR;CAEJ;AAED;AACA;EACEZ,IAAI,EAAE,MAAM;EACZU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,aAAa,CAAC;EACxFN,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBc,KAAK,EAAE;CACR,EACD;EACEZ,IAAI,EAAE,UAAU;EAChBU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,iBAAiB,CAAC;EACpGP,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBc,KAAK,EAAE;CACR;AAED;AACA;EACEZ,IAAI,EAAE,UAAU;EAChBU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,iBAAiB,CAAC;EACpGR,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBc,KAAK,EAAE;CACR;AAED;AACA;EACEZ,IAAI,EAAE,SAAS;EACfQ,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBW,QAAQ,EAAE,CACR;IACET,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE;GACZ,EACD;IACEF,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,aAAa;GAC1F,EACD;IACEjB,IAAI,EAAE,QAAQ;IACdU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,eAAe,CAAC;IACtGN,KAAK,EAAE;GACR;CAEJ;AAED;AACA;EACEZ,IAAI,EAAE,QAAQ;EACdQ,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBW,QAAQ,EAAE,CACR;IACET,IAAI,EAAE,WAAW;IACjBU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wDAAwD,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,wBAAwB,CAAC;IAC3HP,KAAK,EAAE;GACR;CAEJ;AAED;AACA;EACEZ,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACe,UAAU,CAAC;EACjFZ,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACgB,YAAY;CACvF,EACD;EACErB,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACiB,WAAW;CACpF,EACD;EACEtB,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;CACb;AAED;AACA;EACED,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACkB,WAAW;CAC3E;AAED;AACA;EACEvB,IAAI,EAAE,SAAS;EACfU,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACmB,gBAAgB,CAAC;EACjGZ,KAAK,EAAE;CACR;AAED;AACA;EACEZ,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}