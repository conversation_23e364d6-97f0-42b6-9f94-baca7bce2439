import{c as createShadow}from"../shared/create-shadow.min.mjs";import{e as effectInit}from"../shared/effect-init.min.mjs";import{e as effectTarget}from"../shared/effect-target.min.mjs";import{e as effectVirtualTransitionEnd}from"../shared/effect-virtual-transition-end.min.mjs";import{g as getSlideTransformEl,p as getRotateFix}from"../shared/utils.min.mjs";function EffectFlip(e){let{swiper:t,extendParams:s,on:a}=e;s({flipEffect:{slideShadows:!0,limitRotation:!0}});const r=(e,s)=>{let a=t.isHorizontal()?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),r=t.isHorizontal()?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");a||(a=createShadow("flip",e,t.isHorizontal()?"left":"top")),r||(r=createShadow("flip",e,t.isHorizontal()?"right":"bottom")),a&&(a.style.opacity=Math.max(-s,0)),r&&(r.style.opacity=Math.max(s,0))};effectInit({effect:"flip",swiper:t,on:a,setTranslate:()=>{const{slides:e,rtlTranslate:s}=t,a=t.params.flipEffect,i=getRotateFix(t);for(let o=0;o<e.length;o+=1){const l=e[o];let f=l.progress;t.params.flipEffect.limitRotation&&(f=Math.max(Math.min(l.progress,1),-1));const n=l.swiperSlideOffset;let p=-180*f,d=0,m=t.params.cssMode?-n-t.translate:-n,c=0;t.isHorizontal()?s&&(p=-p):(c=m,m=0,d=-p,p=0),l.style.zIndex=-Math.abs(Math.round(f))+e.length,a.slideShadows&&r(l,f);const h=`translate3d(${m}px, ${c}px, 0px) rotateX(${i(d)}deg) rotateY(${i(p)}deg)`;effectTarget(a,l).style.transform=h}},setTransition:e=>{const s=t.slides.map((e=>getSlideTransformEl(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),effectVirtualTransitionEnd({swiper:t,duration:e,transformElements:s})},recreateShadows:()=>{t.params.flipEffect,t.slides.forEach((e=>{let s=e.progress;t.params.flipEffect.limitRotation&&(s=Math.max(Math.min(e.progress,1),-1)),r(e,s)}))},getEffectParams:()=>t.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})}export{EffectFlip as default};
//# sourceMappingURL=effect-flip.min.mjs.map