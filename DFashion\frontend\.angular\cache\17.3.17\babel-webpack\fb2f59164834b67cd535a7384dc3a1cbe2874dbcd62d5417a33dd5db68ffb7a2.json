{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let RolePipe = /*#__PURE__*/(() => {\n  class RolePipe {\n    constructor() {\n      this.roleDisplayNames = {\n        'super_admin': 'Super Administrator',\n        'admin': 'Administrator',\n        'sales_manager': 'Sales Manager',\n        'marketing_manager': 'Marketing Manager',\n        'account_manager': 'Account Manager',\n        'support_manager': 'Support Manager',\n        'sales_executive': 'Sales Executive',\n        'marketing_executive': 'Marketing Executive',\n        'account_executive': 'Account Executive',\n        'support_executive': 'Support Executive',\n        'customer': 'Customer',\n        'vendor': 'Vendor'\n      };\n    }\n    transform(role) {\n      return this.roleDisplayNames[role] || role;\n    }\n    static {\n      this.ɵfac = function RolePipe_Factory(t) {\n        return new (t || RolePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"role\",\n        type: RolePipe,\n        pure: true\n      });\n    }\n  }\n  return RolePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}