const express = require('express');
const router = express.Router();
const Product = require('../models/Product');

/**
 * Advanced Product Search with intelligent query processing
 * GET /api/search/products
 */
router.get('/products', async (req, res) => {
  try {
    const {
      q: query,
      category,
      subcategory,
      targetGender,
      brand,
      minPrice,
      maxPrice,
      sortBy = 'relevance',
      page = 1,
      limit = 20,
      availability,
      occasion,
      material,
      size,
      color
    } = req.query;

    // Build search criteria
    const searchCriteria = { isActive: true };
    
    // Text search across multiple fields
    if (query) {
      searchCriteria.$or = [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { searchKeywords: { $in: [new RegExp(query, 'i')] } },
        { alternativeNames: { $in: [new RegExp(query, 'i')] } },
        { brand: { $regex: query, $options: 'i' } }
      ];
    }

    // Category filters
    if (category) searchCriteria.category = category;
    if (subcategory) searchCriteria.subcategory = subcategory;
    if (targetGender) searchCriteria.targetGender = targetGender;
    if (brand) searchCriteria.brand = { $regex: brand, $options: 'i' };

    // Price range filter
    if (minPrice || maxPrice) {
      searchCriteria['pricing.sellingPrice'] = {};
      if (minPrice) searchCriteria['pricing.sellingPrice'].$gte = parseFloat(minPrice);
      if (maxPrice) searchCriteria['pricing.sellingPrice'].$lte = parseFloat(maxPrice);
    }

    // Availability filter
    if (availability) {
      const availabilityArray = availability.split(',');
      searchCriteria['availability.status'] = { $in: availabilityArray };
    }

    // Occasion filter
    if (occasion) {
      const occasionArray = occasion.split(',');
      searchCriteria['specifications.occasion'] = { $in: occasionArray };
    }

    // Material filter
    if (material) {
      const materialArray = material.split(',');
      searchCriteria['specifications.fabric'] = { $in: materialArray };
    }

    // Size filter
    if (size) {
      const sizeArray = size.split(',');
      searchCriteria['sizes.size'] = { $in: sizeArray };
    }

    // Color filter
    if (color) {
      const colorArray = color.split(',');
      searchCriteria['colors.name'] = { $in: colorArray.map(c => new RegExp(c, 'i')) };
    }

    // Build sort criteria
    let sortCriteria = {};
    switch (sortBy) {
      case 'price-low':
        sortCriteria = { 'pricing.sellingPrice': 1 };
        break;
      case 'price-high':
        sortCriteria = { 'pricing.sellingPrice': -1 };
        break;
      case 'rating':
        sortCriteria = { 'rating.average': -1, 'rating.count': -1 };
        break;
      case 'newest':
        sortCriteria = { createdAt: -1 };
        break;
      case 'relevance':
      default:
        // For text search, use text score for relevance
        if (query) {
          searchCriteria.$text = { $search: query };
          sortCriteria = { score: { $meta: 'textScore' } };
        } else {
          sortCriteria = { 'analytics.views': -1, 'rating.average': -1 };
        }
        break;
    }

    // Execute search with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [products, totalCount] = await Promise.all([
      Product.find(searchCriteria)
        .sort(sortCriteria)
        .skip(skip)
        .limit(parseInt(limit))
        .populate('vendor', 'username fullName avatar')
        .lean(),
      Product.countDocuments(searchCriteria)
    ]);

    // Generate search suggestions based on query
    const suggestions = await generateSearchSuggestions(query, searchCriteria);

    // Calculate relevance scores for products
    const enhancedProducts = products.map(product => ({
      ...product,
      relevanceScore: calculateRelevanceScore(product, query, searchCriteria)
    }));

    res.json({
      success: true,
      products: enhancedProducts,
      totalCount,
      currentPage: parseInt(page),
      totalPages: Math.ceil(totalCount / parseInt(limit)),
      suggestions,
      appliedFilters: {
        query,
        category,
        subcategory,
        targetGender,
        brand,
        priceRange: minPrice || maxPrice ? { min: minPrice, max: maxPrice } : null,
        sortBy
      }
    });

  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed',
      error: error.message
    });
  }
});

/**
 * Get search suggestions as user types
 * GET /api/search/suggestions
 */
router.get('/suggestions', async (req, res) => {
  try {
    const { q: query } = req.query;
    
    if (!query || query.length < 2) {
      return res.json({ success: true, suggestions: [] });
    }

    // Get product name suggestions
    const productSuggestions = await Product.aggregate([
      {
        $match: {
          isActive: true,
          $or: [
            { name: { $regex: query, $options: 'i' } },
            { searchKeywords: { $in: [new RegExp(query, 'i')] } },
            { alternativeNames: { $in: [new RegExp(query, 'i')] } }
          ]
        }
      },
      {
        $group: {
          _id: null,
          names: { $addToSet: '$name' },
          keywords: { $addToSet: '$searchKeywords' },
          brands: { $addToSet: '$brand' }
        }
      }
    ]);

    const suggestions = [];
    
    if (productSuggestions.length > 0) {
      const { names, keywords, brands } = productSuggestions[0];
      
      // Add product name suggestions
      names.forEach(name => {
        if (name.toLowerCase().includes(query.toLowerCase())) {
          suggestions.push({
            text: name,
            type: 'product',
            count: Math.floor(Math.random() * 50) + 10
          });
        }
      });

      // Add keyword suggestions
      keywords.flat().forEach(keyword => {
        if (keyword.toLowerCase().includes(query.toLowerCase())) {
          suggestions.push({
            text: keyword,
            type: 'keyword',
            count: Math.floor(Math.random() * 30) + 5
          });
        }
      });

      // Add brand suggestions
      brands.forEach(brand => {
        if (brand.toLowerCase().includes(query.toLowerCase())) {
          suggestions.push({
            text: brand,
            type: 'brand',
            count: Math.floor(Math.random() * 20) + 3
          });
        }
      });
    }

    // Remove duplicates and limit results
    const uniqueSuggestions = suggestions
      .filter((suggestion, index, self) => 
        index === self.findIndex(s => s.text === suggestion.text)
      )
      .slice(0, 8);

    res.json({
      success: true,
      suggestions: uniqueSuggestions
    });

  } catch (error) {
    console.error('Suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get suggestions',
      error: error.message
    });
  }
});

/**
 * Get category options for ambiguous queries
 * GET /api/search/categories
 */
router.get('/categories', async (req, res) => {
  try {
    const { q: query } = req.query;
    
    // Get category statistics
    const categoryStats = await Product.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: {
            category: '$category',
            subcategory: '$subcategory',
            targetGender: '$targetGender'
          },
          count: { $sum: 1 },
          avgPrice: { $avg: '$pricing.sellingPrice' },
          sampleImage: { $first: '$images.0.url' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Filter categories based on query
    const relevantCategories = categoryStats.filter(stat => {
      const query_lower = query.toLowerCase();
      return stat._id.subcategory.toLowerCase().includes(query_lower) ||
             stat._id.category.toLowerCase().includes(query_lower);
    });

    const categoryOptions = relevantCategories.map(stat => ({
      id: `${stat._id.category}-${stat._id.subcategory}`,
      name: `${stat._id.targetGender === 'men' ? 'Men\'s' : 'Women\'s'} ${stat._id.subcategory}`,
      description: `${stat.count} products available`,
      productCount: stat.count,
      targetGender: stat._id.targetGender,
      category: stat._id.category,
      subcategory: stat._id.subcategory,
      image: stat.sampleImage || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300',
      avgPrice: Math.round(stat.avgPrice)
    }));

    res.json({
      success: true,
      categories: categoryOptions,
      query
    });

  } catch (error) {
    console.error('Categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get categories',
      error: error.message
    });
  }
});

/**
 * Helper function to generate search suggestions
 */
async function generateSearchSuggestions(query, searchCriteria) {
  if (!query) return [];

  try {
    // Get related products for suggestions
    const relatedProducts = await Product.find({
      ...searchCriteria,
      $or: [
        { tags: { $in: [new RegExp(query, 'i')] } },
        { 'specifications.occasion': { $in: [new RegExp(query, 'i')] } }
      ]
    }).limit(10).lean();

    const suggestions = new Set();
    
    relatedProducts.forEach(product => {
      // Add related tags
      product.tags?.forEach(tag => {
        if (tag.toLowerCase() !== query.toLowerCase()) {
          suggestions.add(tag);
        }
      });
      
      // Add related occasions
      product.specifications?.occasion?.forEach(occasion => {
        if (occasion.toLowerCase() !== query.toLowerCase()) {
          suggestions.add(`${query} for ${occasion}`);
        }
      });
    });

    return Array.from(suggestions).slice(0, 5);
  } catch (error) {
    console.error('Error generating suggestions:', error);
    return [];
  }
}

/**
 * Helper function to calculate relevance score
 */
function calculateRelevanceScore(product, query, searchCriteria) {
  if (!query) return 1;

  let score = 0;
  const queryLower = query.toLowerCase();
  
  // Name match (highest weight)
  if (product.name.toLowerCase().includes(queryLower)) {
    score += 10;
  }
  
  // Exact keyword match
  if (product.searchKeywords?.some(keyword => 
    keyword.toLowerCase() === queryLower)) {
    score += 8;
  }
  
  // Tag match
  if (product.tags?.some(tag => 
    tag.toLowerCase().includes(queryLower))) {
    score += 5;
  }
  
  // Brand match
  if (product.brand.toLowerCase().includes(queryLower)) {
    score += 3;
  }
  
  // Rating boost
  score += (product.rating?.average || 0) * 0.5;
  
  // Popularity boost
  score += Math.log(product.analytics?.views || 1) * 0.1;
  
  return score;
}

module.exports = router;
