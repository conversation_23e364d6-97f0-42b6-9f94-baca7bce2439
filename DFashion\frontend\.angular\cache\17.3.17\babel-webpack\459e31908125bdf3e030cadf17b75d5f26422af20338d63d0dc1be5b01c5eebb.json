{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/analytics.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nfunction AnalyticsComponent_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const period_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", period_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", period_r1.label, \" \");\n  }\n}\nfunction AnalyticsComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 40)(10, \"mat-icon\", 41);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", product_r2.sales, \" sales \\u2022 \\u20B9\", i0.ɵɵpipeBind1(8, 6, product_r2.revenue), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", product_r2.trend > 0 ? \"primary\" : \"warn\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r2.trend > 0 ? \"trending_up\" : \"trending_down\", \" \");\n  }\n}\nfunction AnalyticsComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"div\", 44);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 46);\n    i0.ɵɵelement(7, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const source_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(source_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", source_r4.percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", source_r4.percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(10, 5, source_r4.visitors), \" visitors\");\n  }\n}\nexport class AnalyticsComponent {\n  constructor(analyticsService) {\n    this.analyticsService = analyticsService;\n    this.destroy$ = new Subject();\n    this.selectedPeriod = '30d';\n    this.periods = [{\n      value: '7d',\n      label: 'Last 7 days'\n    }, {\n      value: '30d',\n      label: 'Last 30 days'\n    }, {\n      value: '90d',\n      label: 'Last 3 months'\n    }, {\n      value: '12m',\n      label: 'Last 12 months'\n    }];\n    // Mock data\n    this.totalRevenue = 125000;\n    this.revenueGrowth = 12.5;\n    this.totalOrders = 2340;\n    this.orderGrowth = 8.3;\n    this.totalCustomers = 1250;\n    this.customerGrowth = 15.2;\n    this.conversionRate = 3.2;\n    this.conversionChange = 0.5;\n    this.newCustomers = 185;\n    this.newCustomerGrowth = 22;\n    this.returningCustomers = 1065;\n    this.returningCustomerGrowth = 12;\n    this.averageOrderValue = 2850;\n    this.aovChange = 5.2;\n    this.topProducts = [{\n      name: 'Classic White Shirt',\n      sales: 45,\n      revenue: 112500,\n      trend: 1\n    }, {\n      name: 'Denim Jeans',\n      sales: 38,\n      revenue: 95000,\n      trend: 1\n    }, {\n      name: 'Summer Dress',\n      sales: 32,\n      revenue: 80000,\n      trend: -1\n    }, {\n      name: 'Casual Sneakers',\n      sales: 28,\n      revenue: 70000,\n      trend: 1\n    }, {\n      name: 'Leather Jacket',\n      sales: 22,\n      revenue: 55000,\n      trend: -1\n    }];\n    this.trafficSources = [{\n      name: 'Direct',\n      percentage: 35,\n      visitors: 4200\n    }, {\n      name: 'Google Search',\n      percentage: 28,\n      visitors: 3360\n    }, {\n      name: 'Social Media',\n      percentage: 18,\n      visitors: 2160\n    }, {\n      name: 'Email Marketing',\n      percentage: 12,\n      visitors: 1440\n    }, {\n      name: 'Referrals',\n      percentage: 7,\n      visitors: 840\n    }];\n  }\n  ngOnInit() {\n    this.loadAnalyticsData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  onPeriodChange() {\n    this.loadAnalyticsData();\n  }\n  loadAnalyticsData() {\n    // In a real app, this would load data from the analytics service\n    console.log('Loading analytics data for period:', this.selectedPeriod);\n  }\n  exportReport(type) {\n    console.log('Exporting report:', type);\n    // Implement export functionality\n  }\n  static {\n    this.ɵfac = function AnalyticsComponent_Factory(t) {\n      return new (t || AnalyticsComponent)(i0.ɵɵdirectiveInject(i1.AnalyticsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AnalyticsComponent,\n      selectors: [[\"app-analytics\"]],\n      decls: 150,\n      vars: 27,\n      consts: [[1, \"analytics-container\"], [1, \"analytics-header\"], [1, \"period-selector\"], [\"appearance\", \"outline\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"metrics-grid\"], [1, \"metric-card\", \"sales\"], [1, \"metric-content\"], [1, \"metric-icon\"], [1, \"metric-details\"], [1, \"metric-change\", \"positive\"], [1, \"metric-card\", \"orders\"], [1, \"metric-card\", \"customers\"], [1, \"metric-card\", \"conversion\"], [1, \"metric-change\", \"neutral\"], [1, \"charts-grid\"], [1, \"chart-card\"], [1, \"chart-placeholder\"], [1, \"top-products-list\"], [\"class\", \"product-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"customer-insights\"], [1, \"insight-item\"], [1, \"insight-label\"], [1, \"insight-value\"], [1, \"insight-change\", \"positive\"], [1, \"insight-change\", \"neutral\"], [1, \"traffic-sources\"], [\"class\", \"source-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"export-section\"], [1, \"export-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"mat-raised-button\", \"\", 3, \"click\"], [3, \"value\"], [1, \"product-item\"], [1, \"product-rank\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-stats\"], [1, \"product-trend\"], [3, \"color\"], [1, \"source-item\"], [1, \"source-info\"], [1, \"source-name\"], [1, \"source-percentage\"], [1, \"source-bar\"], [1, \"source-fill\"], [1, \"source-visitors\"]],\n      template: function AnalyticsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Analytics Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"mat-form-field\", 3)(6, \"mat-label\");\n          i0.ɵɵtext(7, \"Time Period\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-select\", 4);\n          i0.ɵɵtwoWayListener(\"valueChange\", function AnalyticsComponent_Template_mat_select_valueChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedPeriod, $event) || (ctx.selectedPeriod = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function AnalyticsComponent_Template_mat_select_selectionChange_8_listener() {\n            return ctx.onPeriodChange();\n          });\n          i0.ɵɵtemplate(9, AnalyticsComponent_mat_option_9_Template, 2, 2, \"mat-option\", 5);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"mat-card\", 7)(12, \"mat-card-content\")(13, \"div\", 8)(14, \"div\", 9)(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"trending_up\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 10)(18, \"h3\");\n          i0.ɵɵtext(19);\n          i0.ɵɵpipe(20, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\");\n          i0.ɵɵtext(22, \"Total Revenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 11);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(25, \"mat-card\", 12)(26, \"mat-card-content\")(27, \"div\", 8)(28, \"div\", 9)(29, \"mat-icon\");\n          i0.ɵɵtext(30, \"shopping_cart\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 10)(32, \"h3\");\n          i0.ɵɵtext(33);\n          i0.ɵɵpipe(34, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\");\n          i0.ɵɵtext(36, \"Total Orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 11);\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(39, \"mat-card\", 13)(40, \"mat-card-content\")(41, \"div\", 8)(42, \"div\", 9)(43, \"mat-icon\");\n          i0.ɵɵtext(44, \"people\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 10)(46, \"h3\");\n          i0.ɵɵtext(47);\n          i0.ɵɵpipe(48, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"p\");\n          i0.ɵɵtext(50, \"Total Customers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 11);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(53, \"mat-card\", 14)(54, \"mat-card-content\")(55, \"div\", 8)(56, \"div\", 9)(57, \"mat-icon\");\n          i0.ɵɵtext(58, \"analytics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 10)(60, \"h3\");\n          i0.ɵɵtext(61);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"p\");\n          i0.ɵɵtext(63, \"Conversion Rate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 15);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(66, \"div\", 16)(67, \"mat-card\", 17)(68, \"mat-card-header\")(69, \"mat-card-title\");\n          i0.ɵɵtext(70, \"Sales Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"mat-card-subtitle\");\n          i0.ɵɵtext(72, \"Revenue and orders over time\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"mat-card-content\")(74, \"div\", 18)(75, \"mat-icon\");\n          i0.ɵɵtext(76, \"show_chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"p\");\n          i0.ɵɵtext(78, \"Sales chart will be displayed here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"small\");\n          i0.ɵɵtext(80, \"Chart.js integration pending\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"mat-card\", 17)(82, \"mat-card-header\")(83, \"mat-card-title\");\n          i0.ɵɵtext(84, \"Top Products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-card-subtitle\");\n          i0.ɵɵtext(86, \"Best performing products\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"mat-card-content\")(88, \"div\", 19);\n          i0.ɵɵtemplate(89, AnalyticsComponent_div_89_Template, 12, 8, \"div\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"mat-card\", 17)(91, \"mat-card-header\")(92, \"mat-card-title\");\n          i0.ɵɵtext(93, \"Customer Insights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"mat-card-subtitle\");\n          i0.ɵɵtext(95, \"Customer behavior and demographics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"mat-card-content\")(97, \"div\", 21)(98, \"div\", 22)(99, \"div\", 23);\n          i0.ɵɵtext(100, \"New Customers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"div\", 24);\n          i0.ɵɵtext(102);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"div\", 25);\n          i0.ɵɵtext(104);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(105, \"div\", 22)(106, \"div\", 23);\n          i0.ɵɵtext(107, \"Returning Customers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"div\", 24);\n          i0.ɵɵtext(109);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 25);\n          i0.ɵɵtext(111);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(112, \"div\", 22)(113, \"div\", 23);\n          i0.ɵɵtext(114, \"Average Order Value\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\", 24);\n          i0.ɵɵtext(116);\n          i0.ɵɵpipe(117, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"div\", 26);\n          i0.ɵɵtext(119);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(120, \"mat-card\", 17)(121, \"mat-card-header\")(122, \"mat-card-title\");\n          i0.ɵɵtext(123, \"Traffic Sources\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"mat-card-subtitle\");\n          i0.ɵɵtext(125, \"Where your customers come from\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"mat-card-content\")(127, \"div\", 27);\n          i0.ɵɵtemplate(128, AnalyticsComponent_div_128_Template, 11, 7, \"div\", 28);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(129, \"div\", 29)(130, \"mat-card\")(131, \"mat-card-header\")(132, \"mat-card-title\");\n          i0.ɵɵtext(133, \"Export Reports\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"mat-card-subtitle\");\n          i0.ɵɵtext(135, \"Download analytics data\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(136, \"mat-card-content\")(137, \"div\", 30)(138, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function AnalyticsComponent_Template_button_click_138_listener() {\n            return ctx.exportReport(\"sales\");\n          });\n          i0.ɵɵelementStart(139, \"mat-icon\");\n          i0.ɵɵtext(140, \"file_download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(141, \" Sales Report \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function AnalyticsComponent_Template_button_click_142_listener() {\n            return ctx.exportReport(\"customers\");\n          });\n          i0.ɵɵelementStart(143, \"mat-icon\");\n          i0.ɵɵtext(144, \"file_download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(145, \" Customer Report \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function AnalyticsComponent_Template_button_click_146_listener() {\n            return ctx.exportReport(\"products\");\n          });\n          i0.ɵɵelementStart(147, \"mat-icon\");\n          i0.ɵɵtext(148, \"file_download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(149, \" Product Report \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedPeriod);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.periods);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(20, 18, ctx.totalRevenue, \"1.0-0\"), \"\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"+\", ctx.revenueGrowth, \"%\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(34, 21, ctx.totalOrders));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"+\", ctx.orderGrowth, \"%\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(48, 23, ctx.totalCustomers));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"+\", ctx.customerGrowth, \"%\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\"\", ctx.conversionRate, \"%\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", ctx.conversionChange, \"%\");\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topProducts);\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate(ctx.newCustomers);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"+\", ctx.newCustomerGrowth, \"%\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.returningCustomers);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"+\", ctx.returningCustomerGrowth, \"%\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(117, 25, ctx.averageOrderValue), \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.aovChange, \"%\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.trafficSources);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.MatIcon, i4.MatButton, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatFormField, i6.MatLabel, i7.MatSelect, i8.MatOption, i2.DecimalPipe],\n      styles: [\".analytics-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n.analytics-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n}\\n.analytics-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.analytics-header[_ngcontent-%COMP%]   .period-selector[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 200px;\\n}\\n\\n.metrics-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.metric-card[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: white;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #666;\\n  font-size: 0.875rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change.positive[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change.negative[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change.neutral[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.metric-card.sales[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n.metric-card.orders[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\\n}\\n.metric-card.customers[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\\n}\\n.metric-card.conversion[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\\n}\\n\\n.charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  padding-bottom: 1rem;\\n}\\n.chart-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 500;\\n}\\n.chart-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n\\n.chart-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 300px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n  color: #999;\\n}\\n.chart-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  margin-bottom: 1rem;\\n}\\n.chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1rem;\\n}\\n.chart-placeholder[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-rank[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  background: #e3f2fd;\\n  color: #1976d2;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  margin-right: 1rem;\\n}\\n.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n}\\n.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-stats[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-trend[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n\\n.customer-insights[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 1rem;\\n}\\n.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n}\\n.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 0.5rem;\\n}\\n.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-value[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change.positive[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change.negative[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change.neutral[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 0.75rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-info[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-info[_ngcontent-%COMP%]   .source-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n}\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-info[_ngcontent-%COMP%]   .source-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background: #e0e0e0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-bar[_ngcontent-%COMP%]   .source-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #1976d2;\\n  transition: width 0.3s ease;\\n}\\n.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-visitors[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n  text-align: right;\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n\\n.export-section[_ngcontent-%COMP%]   .export-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.export-section[_ngcontent-%COMP%]   .export-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .analytics-container[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .analytics-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 1rem;\\n  }\\n  .analytics-header[_ngcontent-%COMP%]   .period-selector[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .metrics-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .charts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .customer-insights[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .export-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .export-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "period_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtextInterpolate", "i_r3", "product_r2", "name", "ɵɵtextInterpolate2", "sales", "ɵɵpipeBind1", "revenue", "trend", "ɵɵelement", "source_r4", "percentage", "ɵɵstyleProp", "visitors", "AnalyticsComponent", "constructor", "analyticsService", "destroy$", "<PERSON><PERSON><PERSON><PERSON>", "periods", "totalRevenue", "revenueGrowth", "totalOrders", "orderGrowth", "totalCustomers", "customerGrowth", "conversionRate", "conversionChange", "newCustomers", "newCustomerGrowth", "returningCustomers", "returning<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "averageOrderValue", "aovChange", "topProducts", "trafficSources", "ngOnInit", "loadAnalyticsData", "ngOnDestroy", "next", "complete", "onPeriodChange", "console", "log", "exportReport", "type", "ɵɵdirectiveInject", "i1", "AnalyticsService", "selectors", "decls", "vars", "consts", "template", "AnalyticsComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "AnalyticsComponent_Template_mat_select_valueChange_8_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "AnalyticsComponent_Template_mat_select_selectionChange_8_listener", "ɵɵtemplate", "AnalyticsComponent_mat_option_9_Template", "AnalyticsComponent_div_89_Template", "AnalyticsComponent_div_128_Template", "AnalyticsComponent_Template_button_click_138_listener", "AnalyticsComponent_Template_button_click_142_listener", "AnalyticsComponent_Template_button_click_146_listener", "ɵɵtwoWayProperty", "ɵɵpipeBind2"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\analytics\\analytics.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AnalyticsService } from '../services/analytics.service';\n\n@Component({\n  selector: 'app-analytics',\n  template: `\n    <div class=\"analytics-container\">\n      <div class=\"analytics-header\">\n        <h1>Analytics Dashboard</h1>\n        <div class=\"period-selector\">\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Time Period</mat-label>\n            <mat-select [(value)]=\"selectedPeriod\" (selectionChange)=\"onPeriodChange()\">\n              <mat-option *ngFor=\"let period of periods\" [value]=\"period.value\">\n                {{ period.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Key Metrics -->\n      <div class=\"metrics-grid\">\n        <mat-card class=\"metric-card sales\">\n          <mat-card-content>\n            <div class=\"metric-content\">\n              <div class=\"metric-icon\">\n                <mat-icon>trending_up</mat-icon>\n              </div>\n              <div class=\"metric-details\">\n                <h3>₹{{ totalRevenue | number:'1.0-0' }}</h3>\n                <p>Total Revenue</p>\n                <span class=\"metric-change positive\">+{{ revenueGrowth }}%</span>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card orders\">\n          <mat-card-content>\n            <div class=\"metric-content\">\n              <div class=\"metric-icon\">\n                <mat-icon>shopping_cart</mat-icon>\n              </div>\n              <div class=\"metric-details\">\n                <h3>{{ totalOrders | number }}</h3>\n                <p>Total Orders</p>\n                <span class=\"metric-change positive\">+{{ orderGrowth }}%</span>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card customers\">\n          <mat-card-content>\n            <div class=\"metric-content\">\n              <div class=\"metric-icon\">\n                <mat-icon>people</mat-icon>\n              </div>\n              <div class=\"metric-details\">\n                <h3>{{ totalCustomers | number }}</h3>\n                <p>Total Customers</p>\n                <span class=\"metric-change positive\">+{{ customerGrowth }}%</span>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card conversion\">\n          <mat-card-content>\n            <div class=\"metric-content\">\n              <div class=\"metric-icon\">\n                <mat-icon>analytics</mat-icon>\n              </div>\n              <div class=\"metric-details\">\n                <h3>{{ conversionRate }}%</h3>\n                <p>Conversion Rate</p>\n                <span class=\"metric-change neutral\">{{ conversionChange }}%</span>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Charts Section -->\n      <div class=\"charts-grid\">\n        <!-- Sales Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Sales Overview</mat-card-title>\n            <mat-card-subtitle>Revenue and orders over time</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-placeholder\">\n              <mat-icon>show_chart</mat-icon>\n              <p>Sales chart will be displayed here</p>\n              <small>Chart.js integration pending</small>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Top Products -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Top Products</mat-card-title>\n            <mat-card-subtitle>Best performing products</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"top-products-list\">\n              <div *ngFor=\"let product of topProducts; let i = index\" class=\"product-item\">\n                <div class=\"product-rank\">{{ i + 1 }}</div>\n                <div class=\"product-info\">\n                  <div class=\"product-name\">{{ product.name }}</div>\n                  <div class=\"product-stats\">{{ product.sales }} sales • ₹{{ product.revenue | number }}</div>\n                </div>\n                <div class=\"product-trend\">\n                  <mat-icon [color]=\"product.trend > 0 ? 'primary' : 'warn'\">\n                    {{ product.trend > 0 ? 'trending_up' : 'trending_down' }}\n                  </mat-icon>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Customer Analytics -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Customer Insights</mat-card-title>\n            <mat-card-subtitle>Customer behavior and demographics</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"customer-insights\">\n              <div class=\"insight-item\">\n                <div class=\"insight-label\">New Customers</div>\n                <div class=\"insight-value\">{{ newCustomers }}</div>\n                <div class=\"insight-change positive\">+{{ newCustomerGrowth }}%</div>\n              </div>\n              <div class=\"insight-item\">\n                <div class=\"insight-label\">Returning Customers</div>\n                <div class=\"insight-value\">{{ returningCustomers }}</div>\n                <div class=\"insight-change positive\">+{{ returningCustomerGrowth }}%</div>\n              </div>\n              <div class=\"insight-item\">\n                <div class=\"insight-label\">Average Order Value</div>\n                <div class=\"insight-value\">₹{{ averageOrderValue | number }}</div>\n                <div class=\"insight-change neutral\">{{ aovChange }}%</div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Traffic Sources -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Traffic Sources</mat-card-title>\n            <mat-card-subtitle>Where your customers come from</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"traffic-sources\">\n              <div *ngFor=\"let source of trafficSources\" class=\"source-item\">\n                <div class=\"source-info\">\n                  <div class=\"source-name\">{{ source.name }}</div>\n                  <div class=\"source-percentage\">{{ source.percentage }}%</div>\n                </div>\n                <div class=\"source-bar\">\n                  <div class=\"source-fill\" [style.width.%]=\"source.percentage\"></div>\n                </div>\n                <div class=\"source-visitors\">{{ source.visitors | number }} visitors</div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Export Options -->\n      <div class=\"export-section\">\n        <mat-card>\n          <mat-card-header>\n            <mat-card-title>Export Reports</mat-card-title>\n            <mat-card-subtitle>Download analytics data</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"export-buttons\">\n              <button mat-raised-button color=\"primary\" (click)=\"exportReport('sales')\">\n                <mat-icon>file_download</mat-icon>\n                Sales Report\n              </button>\n              <button mat-raised-button color=\"accent\" (click)=\"exportReport('customers')\">\n                <mat-icon>file_download</mat-icon>\n                Customer Report\n              </button>\n              <button mat-raised-button (click)=\"exportReport('products')\">\n                <mat-icon>file_download</mat-icon>\n                Product Report\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./analytics.component.scss']\n})\nexport class AnalyticsComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  selectedPeriod = '30d';\n  periods = [\n    { value: '7d', label: 'Last 7 days' },\n    { value: '30d', label: 'Last 30 days' },\n    { value: '90d', label: 'Last 3 months' },\n    { value: '12m', label: 'Last 12 months' }\n  ];\n\n  // Mock data\n  totalRevenue = 125000;\n  revenueGrowth = 12.5;\n  totalOrders = 2340;\n  orderGrowth = 8.3;\n  totalCustomers = 1250;\n  customerGrowth = 15.2;\n  conversionRate = 3.2;\n  conversionChange = 0.5;\n\n  newCustomers = 185;\n  newCustomerGrowth = 22;\n  returningCustomers = 1065;\n  returningCustomerGrowth = 12;\n  averageOrderValue = 2850;\n  aovChange = 5.2;\n\n  topProducts = [\n    { name: 'Classic White Shirt', sales: 45, revenue: 112500, trend: 1 },\n    { name: 'Denim Jeans', sales: 38, revenue: 95000, trend: 1 },\n    { name: 'Summer Dress', sales: 32, revenue: 80000, trend: -1 },\n    { name: 'Casual Sneakers', sales: 28, revenue: 70000, trend: 1 },\n    { name: 'Leather Jacket', sales: 22, revenue: 55000, trend: -1 }\n  ];\n\n  trafficSources = [\n    { name: 'Direct', percentage: 35, visitors: 4200 },\n    { name: 'Google Search', percentage: 28, visitors: 3360 },\n    { name: 'Social Media', percentage: 18, visitors: 2160 },\n    { name: 'Email Marketing', percentage: 12, visitors: 1440 },\n    { name: 'Referrals', percentage: 7, visitors: 840 }\n  ];\n\n  constructor(\n    private analyticsService: AnalyticsService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadAnalyticsData();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  onPeriodChange(): void {\n    this.loadAnalyticsData();\n  }\n\n  loadAnalyticsData(): void {\n    // In a real app, this would load data from the analytics service\n    console.log('Loading analytics data for period:', this.selectedPeriod);\n  }\n\n  exportReport(type: string): void {\n    console.log('Exporting report:', type);\n    // Implement export functionality\n  }\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,MAAM;;;;;;;;;;;;IAchBC,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IAC/DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IA+FET,EADF,CAAAC,cAAA,cAA6E,cACjD;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEzCH,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA2D;;IACxFF,EADwF,CAAAG,YAAA,EAAM,EACxF;IAEJH,EADF,CAAAC,cAAA,cAA2B,oBACkC;IACzDD,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAW,EACP,EACF;;;;;IAVsBH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAU,iBAAA,CAAAC,IAAA,KAAW;IAETX,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAU,iBAAA,CAAAE,UAAA,CAAAC,IAAA,CAAkB;IACjBb,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAc,kBAAA,KAAAF,UAAA,CAAAG,KAAA,0BAAAf,EAAA,CAAAgB,WAAA,OAAAJ,UAAA,CAAAK,OAAA,MAA2D;IAG5EjB,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAI,UAAA,UAAAQ,UAAA,CAAAM,KAAA,0BAAgD;IACxDlB,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAI,UAAA,CAAAM,KAAA,4CACF;;;;;IA4CAlB,EAFJ,CAAAC,cAAA,cAA+D,cACpC,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IACzDF,EADyD,CAAAG,YAAA,EAAM,EACzD;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,SAAA,cAAmE;IACrEnB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAuC;;IACtEF,EADsE,CAAAG,YAAA,EAAM,EACtE;;;;IAPuBH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAU,iBAAA,CAAAU,SAAA,CAAAP,IAAA,CAAiB;IACXb,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,kBAAA,KAAAY,SAAA,CAAAC,UAAA,MAAwB;IAG9BrB,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAsB,WAAA,UAAAF,SAAA,CAAAC,UAAA,MAAmC;IAEjCrB,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,kBAAA,KAAAR,EAAA,CAAAgB,WAAA,QAAAI,SAAA,CAAAG,QAAA,eAAuC;;;AAoCpF,OAAM,MAAOC,kBAAkB;EA4C7BC,YACUC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IA5ClB,KAAAC,QAAQ,GAAG,IAAI5B,OAAO,EAAQ;IAEtC,KAAA6B,cAAc,GAAG,KAAK;IACtB,KAAAC,OAAO,GAAG,CACR;MAAEvB,KAAK,EAAE,IAAI;MAAEG,KAAK,EAAE;IAAa,CAAE,EACrC;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAc,CAAE,EACvC;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAe,CAAE,EACxC;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAgB,CAAE,CAC1C;IAED;IACA,KAAAqB,YAAY,GAAG,MAAM;IACrB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,WAAW,GAAG,IAAI;IAClB,KAAAC,WAAW,GAAG,GAAG;IACjB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,GAAG;IACpB,KAAAC,gBAAgB,GAAG,GAAG;IAEtB,KAAAC,YAAY,GAAG,GAAG;IAClB,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,uBAAuB,GAAG,EAAE;IAC5B,KAAAC,iBAAiB,GAAG,IAAI;IACxB,KAAAC,SAAS,GAAG,GAAG;IAEf,KAAAC,WAAW,GAAG,CACZ;MAAE/B,IAAI,EAAE,qBAAqB;MAAEE,KAAK,EAAE,EAAE;MAAEE,OAAO,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAC,CAAE,EACrE;MAAEL,IAAI,EAAE,aAAa;MAAEE,KAAK,EAAE,EAAE;MAAEE,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAC,CAAE,EAC5D;MAAEL,IAAI,EAAE,cAAc;MAAEE,KAAK,EAAE,EAAE;MAAEE,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE,CAAC;IAAC,CAAE,EAC9D;MAAEL,IAAI,EAAE,iBAAiB;MAAEE,KAAK,EAAE,EAAE;MAAEE,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAC,CAAE,EAChE;MAAEL,IAAI,EAAE,gBAAgB;MAAEE,KAAK,EAAE,EAAE;MAAEE,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE,CAAC;IAAC,CAAE,CACjE;IAED,KAAA2B,cAAc,GAAG,CACf;MAAEhC,IAAI,EAAE,QAAQ;MAAEQ,UAAU,EAAE,EAAE;MAAEE,QAAQ,EAAE;IAAI,CAAE,EAClD;MAAEV,IAAI,EAAE,eAAe;MAAEQ,UAAU,EAAE,EAAE;MAAEE,QAAQ,EAAE;IAAI,CAAE,EACzD;MAAEV,IAAI,EAAE,cAAc;MAAEQ,UAAU,EAAE,EAAE;MAAEE,QAAQ,EAAE;IAAI,CAAE,EACxD;MAAEV,IAAI,EAAE,iBAAiB;MAAEQ,UAAU,EAAE,EAAE;MAAEE,QAAQ,EAAE;IAAI,CAAE,EAC3D;MAAEV,IAAI,EAAE,WAAW;MAAEQ,UAAU,EAAE,CAAC;MAAEE,QAAQ,EAAE;IAAG,CAAE,CACpD;EAIE;EAEHuB,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACrB,QAAQ,CAACsB,IAAI,EAAE;IACpB,IAAI,CAACtB,QAAQ,CAACuB,QAAQ,EAAE;EAC1B;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf;IACAK,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACzB,cAAc,CAAC;EACxE;EAEA0B,YAAYA,CAACC,IAAY;IACvBH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,IAAI,CAAC;IACtC;EACF;;;uBArEW/B,kBAAkB,EAAAxB,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAlBlC,kBAAkB;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApMvBjE,EAFJ,CAAAC,cAAA,aAAiC,aACD,SACxB;UAAAD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGxBH,EAFJ,CAAAC,cAAA,aAA6B,wBACU,gBACxB;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAC,cAAA,oBAA4E;UAAhED,EAAA,CAAAmE,gBAAA,yBAAAC,8DAAAC,MAAA;YAAArE,EAAA,CAAAsE,kBAAA,CAAAJ,GAAA,CAAAtC,cAAA,EAAAyC,MAAA,MAAAH,GAAA,CAAAtC,cAAA,GAAAyC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAACrE,EAAA,CAAAuE,UAAA,6BAAAC,kEAAA;YAAA,OAAmBN,GAAA,CAAAf,cAAA,EAAgB;UAAA,EAAC;UACzEnD,EAAA,CAAAyE,UAAA,IAAAC,wCAAA,wBAAkE;UAM1E1E,EAHM,CAAAG,YAAA,EAAa,EACE,EACb,EACF;UAQIH,EALV,CAAAC,cAAA,cAA0B,mBACY,wBAChB,cACY,cACD,gBACb;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAAoC;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7CH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpBH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,IAAqB;UAIlEF,EAJkE,CAAAG,YAAA,EAAO,EAC7D,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAqC,wBACjB,cACY,cACD,gBACb;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACzBF,EADyB,CAAAG,YAAA,EAAW,EAC9B;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAA0B;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnBH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAIhEF,EAJgE,CAAAG,YAAA,EAAO,EAC3D,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAwC,wBACpB,cACY,cACD,gBACb;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAA6B;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtBH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAInEF,EAJmE,CAAAG,YAAA,EAAO,EAC9D,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAyC,wBACrB,cACY,cACD,gBACb;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EAC1B;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtBH,EAAA,CAAAC,cAAA,gBAAoC;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAKrEF,EALqE,CAAAG,YAAA,EAAO,EAC9D,EACF,EACW,EACV,EACP;UAOAH,EAJN,CAAAC,cAAA,eAAyB,oBAEM,uBACV,sBACC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC/CH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UACjDF,EADiD,CAAAG,YAAA,EAAoB,EACnD;UAGdH,EAFJ,CAAAC,cAAA,wBAAkB,eACe,gBACnB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0CAAkC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzCH,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAGzCF,EAHyC,CAAAG,YAAA,EAAQ,EACvC,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,oBAA6B,uBACV,sBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC7CH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAC7CF,EAD6C,CAAAG,YAAA,EAAoB,EAC/C;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACe;UAC7BD,EAAA,CAAAyE,UAAA,KAAAE,kCAAA,mBAA6E;UAcnF3E,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,oBAA6B,uBACV,sBACC;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAClDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,0CAAkC;UACvDF,EADuD,CAAAG,YAAA,EAAoB,EACzD;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACe,eACH,eACG;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC9CH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,KAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,KAAyB;UAChEF,EADgE,CAAAG,YAAA,EAAM,EAChE;UAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACG;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpDH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,KAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzDH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,KAA+B;UACtEF,EADsE,CAAAG,YAAA,EAAM,EACtE;UAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACG;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpDH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,KAAiC;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAClEH,EAAA,CAAAC,cAAA,gBAAoC;UAAAD,EAAA,CAAAE,MAAA,KAAgB;UAI5DF,EAJ4D,CAAAG,YAAA,EAAM,EACtD,EACF,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,qBAA6B,wBACV,uBACC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAChDH,EAAA,CAAAC,cAAA,0BAAmB;UAAAD,EAAA,CAAAE,MAAA,uCAA8B;UACnDF,EADmD,CAAAG,YAAA,EAAoB,EACrD;UAEhBH,EADF,CAAAC,cAAA,yBAAkB,gBACa;UAC3BD,EAAA,CAAAyE,UAAA,MAAAG,mCAAA,mBAA+D;UAavE5E,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;UAMAH,EAHN,CAAAC,cAAA,gBAA4B,iBAChB,wBACS,uBACC;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC/CH,EAAA,CAAAC,cAAA,0BAAmB;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAC5CF,EAD4C,CAAAG,YAAA,EAAoB,EAC9C;UAGdH,EAFJ,CAAAC,cAAA,yBAAkB,gBACY,mBACgD;UAAhCD,EAAA,CAAAuE,UAAA,mBAAAM,sDAAA;YAAA,OAASX,GAAA,CAAAZ,YAAA,CAAa,OAAO,CAAC;UAAA,EAAC;UACvEtD,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAA6E;UAApCD,EAAA,CAAAuE,UAAA,mBAAAO,sDAAA;YAAA,OAASZ,GAAA,CAAAZ,YAAA,CAAa,WAAW,CAAC;UAAA,EAAC;UAC1EtD,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAA6D;UAAnCD,EAAA,CAAAuE,UAAA,mBAAAQ,sDAAA;YAAA,OAASb,GAAA,CAAAZ,YAAA,CAAa,UAAU,CAAC;UAAA,EAAC;UAC1DtD,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAE,MAAA,yBACF;UAKVF,EALU,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP,EACF;;;UA5LcH,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAgF,gBAAA,UAAAd,GAAA,CAAAtC,cAAA,CAA0B;UACL5B,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAA8D,GAAA,CAAArC,OAAA,CAAU;UAiBnC7B,EAAA,CAAAO,SAAA,IAAoC;UAApCP,EAAA,CAAAQ,kBAAA,WAAAR,EAAA,CAAAiF,WAAA,SAAAf,GAAA,CAAApC,YAAA,eAAoC;UAEH9B,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAQ,kBAAA,MAAA0D,GAAA,CAAAnC,aAAA,MAAqB;UAatD/B,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAU,iBAAA,CAAAV,EAAA,CAAAgB,WAAA,SAAAkD,GAAA,CAAAlC,WAAA,EAA0B;UAEOhC,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAQ,kBAAA,MAAA0D,GAAA,CAAAjC,WAAA,MAAmB;UAapDjC,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAU,iBAAA,CAAAV,EAAA,CAAAgB,WAAA,SAAAkD,GAAA,CAAAhC,cAAA,EAA6B;UAEIlC,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAQ,kBAAA,MAAA0D,GAAA,CAAA/B,cAAA,MAAsB;UAavDnC,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAQ,kBAAA,KAAA0D,GAAA,CAAA9B,cAAA,MAAqB;UAEWpC,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAQ,kBAAA,KAAA0D,GAAA,CAAA7B,gBAAA,MAAuB;UAgCpCrC,EAAA,CAAAO,SAAA,IAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA8D,GAAA,CAAAtB,WAAA,CAAgB;UA0BZ5C,EAAA,CAAAO,SAAA,IAAkB;UAAlBP,EAAA,CAAAU,iBAAA,CAAAwD,GAAA,CAAA5B,YAAA,CAAkB;UACRtC,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAQ,kBAAA,MAAA0D,GAAA,CAAA3B,iBAAA,MAAyB;UAInCvC,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAAU,iBAAA,CAAAwD,GAAA,CAAA1B,kBAAA,CAAwB;UACdxC,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAQ,kBAAA,MAAA0D,GAAA,CAAAzB,uBAAA,MAA+B;UAIzCzC,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAQ,kBAAA,WAAAR,EAAA,CAAAgB,WAAA,UAAAkD,GAAA,CAAAxB,iBAAA,MAAiC;UACxB1C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,kBAAA,KAAA0D,GAAA,CAAAvB,SAAA,MAAgB;UAc9B3C,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAA8D,GAAA,CAAArB,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}