{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = asyncScheduler) {\n  return audit(() => timer(duration, scheduler));\n}", "map": {"version": 3, "names": ["asyncScheduler", "audit", "timer", "auditTime", "duration", "scheduler"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/rxjs/dist/esm/internal/operators/auditTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = asyncScheduler) {\n    return audit(() => timer(duration, scheduler));\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAEC,SAAS,GAAGL,cAAc,EAAE;EAC5D,OAAOC,KAAK,CAAC,MAAMC,KAAK,CAACE,QAAQ,EAAEC,SAAS,CAAC,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}