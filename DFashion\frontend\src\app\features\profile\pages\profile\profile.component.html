<div class="profile-page" [attr.data-role]="currentUser?.role" [attr.data-department]="currentDepartment">
  <!-- Profile Navigation -->
  <div class="profile-navigation">
    <div class="nav-container">
      <div class="nav-tabs">
        <button 
          class="nav-tab"
          [class.active]="activeTab === 'profile'"
          (click)="setActiveTab('profile')">
          <i class="fas fa-user"></i>
          <span>Profile</span>
        </button>
        
        <button 
          class="nav-tab"
          [class.active]="activeTab === 'dashboard'"
          (click)="setActiveTab('dashboard')"
          *ngIf="showDashboard">
          <i class="fas fa-tachometer-alt"></i>
          <span>Dashboard</span>
        </button>
        
        <button 
          class="nav-tab"
          [class.active]="activeTab === 'settings'"
          (click)="setActiveTab('settings')">
          <i class="fas fa-cog"></i>
          <span>Settings</span>
        </button>
        
        <button 
          class="nav-tab"
          [class.active]="activeTab === 'team'"
          (click)="setActiveTab('team')"
          *ngIf="canManageTeam">
          <i class="fas fa-users"></i>
          <span>Team</span>
        </button>
        
        <button 
          class="nav-tab"
          [class.active]="activeTab === 'analytics'"
          (click)="setActiveTab('analytics')"
          *ngIf="canViewAnalytics">
          <i class="fas fa-chart-bar"></i>
          <span>Analytics</span>
        </button>
      </div>
      
      <!-- Role Badge -->
      <div class="role-badge" [style.background]="roleConfig?.color" *ngIf="roleConfig">
        <i [class]="roleConfig.icon"></i>
        <span>{{ roleConfig.displayName }}</span>
      </div>
    </div>
  </div>

  <!-- Profile Content -->
  <div class="profile-content">
    <!-- Profile Tab -->
    <div class="tab-content" [class.active]="activeTab === 'profile'">
      <app-dynamic-profile 
        [userProfile]="currentUser"
        *ngIf="currentUser">
      </app-dynamic-profile>
    </div>

    <!-- Dashboard Tab -->
    <div class="tab-content" [class.active]="activeTab === 'dashboard'" *ngIf="showDashboard">
      <app-department-dashboard
        [department]="currentDepartment"
        [userRole]="currentUser?.role || null"
        *ngIf="currentDepartment && currentUser?.role">
      </app-department-dashboard>
    </div>

    <!-- Settings Tab -->
    <div class="tab-content" [class.active]="activeTab === 'settings'">
      <app-role-based-settings
        [currentRole]="currentUser?.role || null"
        *ngIf="currentUser?.role">
      </app-role-based-settings>
    </div>

    <!-- Team Management Tab -->
    <div class="tab-content" [class.active]="activeTab === 'team'" *ngIf="canManageTeam">
      <div class="team-management">
        <div class="team-header">
          <h2>Team Management</h2>
          <p>Manage your team members and their roles</p>
        </div>
        
        <div class="team-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">12</div>
              <div class="stat-label">Team Members</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">10</div>
              <div class="stat-label">Active Members</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">87%</div>
              <div class="stat-label">Team Performance</div>
            </div>
          </div>
        </div>

        <div class="team-actions">
          <button class="btn btn-primary">
            <i class="fas fa-user-plus"></i>
            Add Team Member
          </button>
          <button class="btn btn-secondary">
            <i class="fas fa-download"></i>
            Export Report
          </button>
        </div>
      </div>
    </div>

    <!-- Analytics Tab -->
    <div class="tab-content" [class.active]="activeTab === 'analytics'" *ngIf="canViewAnalytics">
      <div class="analytics-dashboard">
        <div class="analytics-header">
          <h2>Analytics Dashboard</h2>
          <p>Performance insights and metrics</p>
        </div>

        <div class="analytics-grid">
          <div class="analytics-card">
            <h3>Performance Overview</h3>
            <div class="chart-placeholder">
              <i class="fas fa-chart-line"></i>
              <p>Performance Chart</p>
            </div>
          </div>

          <div class="analytics-card">
            <h3>Key Metrics</h3>
            <div class="metrics-list">
              <div class="metric-item">
                <span class="metric-label">Total Revenue</span>
                <span class="metric-value" style="color: #4CAF50">₹2.4M</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">Conversion Rate</span>
                <span class="metric-value" style="color: #2196F3">3.2%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">Customer Satisfaction</span>
                <span class="metric-value" style="color: #FF9800">4.8/5</span>
              </div>
            </div>
          </div>

          <div class="analytics-card">
            <h3>Recent Activities</h3>
            <div class="activities-list">
              <div class="activity-item">
                <div class="activity-icon" style="background: #4CAF50">
                  <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">New sale completed</div>
                  <div class="activity-time">2 hours ago</div>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-icon" style="background: #2196F3">
                  <i class="fas fa-calendar"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Team meeting scheduled</div>
                  <div class="activity-time">4 hours ago</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
