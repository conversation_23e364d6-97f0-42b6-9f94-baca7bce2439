{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/cart.service\";\nimport * as i2 from \"../../../../core/services/wishlist.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction PostCardComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"div\", 29);\n    i0.ɵɵelementStart(2, \"div\", 30);\n    i0.ɵɵelement(3, \"img\", 31);\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"h5\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_14_Template_button_click_10_listener() {\n      const productTag_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onBuyNow(productTag_r2.product._id));\n    });\n    i0.ɵɵtext(11, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const productTag_r2 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"top\", productTag_r2.position.y, \"%\")(\"left\", productTag_r2.position.x, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", productTag_r2.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", productTag_r2.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r2.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(9, 8, productTag_r2.product.price), \"\");\n  }\n}\nfunction PostCardComponent_div_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"img\", 38);\n    i0.ɵɵelementStart(2, \"div\", 39)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 41)(9, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_25_div_2_Template_button_click_9_listener() {\n      const productTag_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addToWishlist(productTag_r5.product._id));\n    });\n    i0.ɵɵelement(10, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_25_div_2_Template_button_click_11_listener() {\n      const productTag_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addToCart(productTag_r5.product._id));\n    });\n    i0.ɵɵelement(12, \"i\", 44);\n    i0.ɵɵtext(13, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_25_div_2_Template_button_click_14_listener() {\n      const productTag_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.buyNow(productTag_r5.product._id));\n    });\n    i0.ɵɵtext(15, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const productTag_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", productTag_r5.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", productTag_r5.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r5.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(7, 8, productTag_r5.product.price), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r2.isInWishlist(productTag_r5.product._id));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.isInWishlist(productTag_r5.product._id) ? \"fas fa-heart\" : \"far fa-heart\");\n  }\n}\nfunction PostCardComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, PostCardComponent_div_25_div_2_Template, 16, 10, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.post.products);\n  }\n}\nfunction PostCardComponent_p_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" View all \", ctx_r2.post.comments.length, \" comments \");\n  }\n}\nfunction PostCardComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(comment_r6.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", comment_r6.text, \" \");\n  }\n}\nfunction PostCardComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addComment());\n    });\n    i0.ɵɵtext(1, \" Post \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PostCardComponent {\n  constructor(cartService, wishlistService, router) {\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.liked = new EventEmitter();\n    this.commented = new EventEmitter();\n    this.shared = new EventEmitter();\n    this.isLiked = false;\n    this.isSaved = false;\n    this.likesCount = 0;\n    this.newComment = '';\n    this.showComments = false;\n    this.wishlistItems = [];\n    this.cartItems = [];\n  }\n  ngOnInit() {\n    this.likesCount = this.post.analytics.likes;\n    this.loadWishlistItems();\n    this.loadCartItems();\n  }\n  loadWishlistItems() {\n    // Load from localStorage for demo\n    const saved = localStorage.getItem('wishlist');\n    this.wishlistItems = saved ? JSON.parse(saved) : [];\n  }\n  loadCartItems() {\n    // Load from localStorage for demo\n    const saved = localStorage.getItem('cart');\n    this.cartItems = saved ? JSON.parse(saved) : [];\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n  formatCaption(caption) {\n    return caption.replace(/#(\\w+)/g, '<span class=\"hashtag\">#$1</span>');\n  }\n  getRecentComments() {\n    return this.post.comments.slice(-2);\n  }\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    this.likesCount += this.isLiked ? 1 : -1;\n    this.liked.emit(this.post._id);\n  }\n  toggleSave() {\n    this.isSaved = !this.isSaved;\n  }\n  toggleComments() {\n    this.showComments = !this.showComments;\n  }\n  addComment() {\n    if (this.newComment.trim()) {\n      this.commented.emit({\n        postId: this.post._id,\n        comment: this.newComment.trim()\n      });\n      this.newComment = '';\n    }\n  }\n  sharePost() {\n    this.shared.emit(this.post._id);\n  }\n  // E-commerce methods\n  isInWishlist(productId) {\n    return this.wishlistService.isInWishlist(productId);\n  }\n  addToWishlist(productId) {\n    this.wishlistService.toggleWishlist(productId).subscribe({\n      next: response => {\n        if (this.isInWishlist(productId)) {\n          this.showNotification('Removed from wishlist', 'info');\n        } else {\n          this.showNotification('Added to wishlist ❤️', 'success');\n        }\n      },\n      error: error => {\n        console.error('Wishlist error:', error);\n        // Fallback to offline mode\n        this.wishlistService.toggleWishlistOffline(this.getProductById(productId));\n        this.showNotification(this.isInWishlist(productId) ? 'Removed from wishlist' : 'Added to wishlist ❤️', 'success');\n      }\n    });\n  }\n  addToCart(productId) {\n    const product = this.getProductById(productId);\n    if (!product) return;\n    this.cartService.addToCart(product, 1).then(success => {\n      if (success) {\n        this.showNotification('Added to cart 🛒', 'success');\n      }\n    }).catch(error => {\n      console.error('Cart error:', error);\n      this.showNotification('Failed to add to cart', 'error');\n    });\n  }\n  buyNow(productId) {\n    const product = this.getProductById(productId);\n    if (!product) return;\n    this.cartService.addToCart(product, 1).then(success => {\n      if (success) {\n        this.showNotification('Redirecting to checkout...', 'info');\n        this.router.navigate(['/checkout']);\n      }\n    }).catch(error => {\n      console.error('Buy now error:', error);\n      this.showNotification('Failed to process purchase', 'error');\n    });\n  }\n  getProductById(productId) {\n    // Find product in post's products array\n    const productTag = this.post.products.find(p => p.product._id === productId);\n    return productTag ? productTag.product : null;\n  }\n  onBuyNow(productId) {\n    this.buyNow(productId);\n  }\n  showNotification(message, type) {\n    // Create notification element\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n    // Add styles\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n    // Add animation styles\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes slideIn {\n        from { transform: translateX(100%); opacity: 0; }\n        to { transform: translateX(0); opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n    document.body.appendChild(notification);\n    // Remove after 3 seconds\n    setTimeout(() => {\n      notification.remove();\n      style.remove();\n    }, 3000);\n  }\n  static {\n    this.ɵfac = function PostCardComponent_Factory(t) {\n      return new (t || PostCardComponent)(i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i2.WishlistService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PostCardComponent,\n      selectors: [[\"app-post-card\"]],\n      inputs: {\n        post: \"post\"\n      },\n      outputs: {\n        liked: \"liked\",\n        commented: \"commented\",\n        shared: \"shared\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 41,\n      vars: 23,\n      consts: [[1, \"post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"more-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\"], [1, \"post-image\", 3, \"src\", \"alt\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"top\", \"left\", 4, \"ngFor\", \"ngForOf\"], [1, \"post-actions\"], [1, \"action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"far\", \"fa-share\"], [1, \"save-btn\", 3, \"click\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"post-stats\"], [1, \"post-caption\"], [3, \"innerHTML\"], [1, \"post-comments\"], [\"class\", \"view-comments\", 4, \"ngIf\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"add-comment\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"class\", \"post-comment-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"product-tag\"], [1, \"tag-dot\"], [1, \"product-info\"], [3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"buy-now-btn\", 3, \"click\"], [1, \"ecommerce-actions\"], [1, \"products-showcase\"], [\"class\", \"product-showcase\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-showcase\"], [1, \"product-thumb\", 3, \"src\", \"alt\"], [1, \"product-info-inline\"], [1, \"price\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy-now\", 3, \"click\"], [1, \"view-comments\"], [1, \"comment\"], [1, \"post-comment-btn\", 3, \"click\"]],\n      template: function PostCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"article\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"h4\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"button\", 5);\n          i0.ɵɵelement(10, \"i\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7);\n          i0.ɵɵelement(12, \"img\", 8);\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵtemplate(14, PostCardComponent_div_14_Template, 12, 10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_17_listener() {\n            return ctx.toggleLike();\n          });\n          i0.ɵɵelement(18, \"i\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_19_listener() {\n            return ctx.toggleComments();\n          });\n          i0.ɵɵelement(20, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_21_listener() {\n            return ctx.sharePost();\n          });\n          i0.ɵɵelement(22, \"i\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_23_listener() {\n            return ctx.toggleSave();\n          });\n          i0.ɵɵelement(24, \"i\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, PostCardComponent_div_25_Template, 3, 1, \"div\", 18);\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"p\")(28, \"strong\");\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 20)(31, \"p\")(32, \"strong\");\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"span\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 22);\n          i0.ɵɵtemplate(36, PostCardComponent_p_36_Template, 2, 1, \"p\", 23)(37, PostCardComponent_div_37_Template, 5, 2, \"div\", 24);\n          i0.ɵɵelementStart(38, \"div\", 25)(39, \"input\", 26);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PostCardComponent_Template_input_ngModelChange_39_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newComment, $event) || (ctx.newComment = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function PostCardComponent_Template_input_keyup_enter_39_listener() {\n            return ctx.addComment();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, PostCardComponent_button_40_Template, 2, 0, \"button\", 27);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.post.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx.post.user.fullName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.post.user.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getTimeAgo(ctx.post.createdAt));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.post.media[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx.post.media[0].alt);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.post.products);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"liked\", ctx.isLiked);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"saved\", ctx.isSaved);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.post.products.length > 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", ctx.likesCount, \" likes\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.post.user.username);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"innerHTML\", ctx.formatCaption(ctx.post.caption), i0.ɵɵsanitizeHtml);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.post.comments.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.getRecentComments());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newComment);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.newComment.trim());\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".post[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.user-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.user-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n\\n.more-options[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  color: #262626;\\n}\\n\\n.post-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  aspect-ratio: 1;\\n}\\n\\n.post-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  cursor: pointer;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: #fff;\\n  border-radius: 50%;\\n  border: 2px solid var(--primary-color);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: var(--primary-color);\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(0, 149, 246, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(0, 149, 246, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(0, 149, 246, 0);\\n  }\\n}\\n.product-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -120px;\\n  left: -100px;\\n  background: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  padding: 12px;\\n  width: 200px;\\n  display: none;\\n  z-index: 10;\\n}\\n\\n.product-tag[_ngcontent-%COMP%]:hover   .product-info[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  object-fit: cover;\\n  float: left;\\n  margin-right: 12px;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.buy-now-btn[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border: none;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  width: 100%;\\n}\\n\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 16px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 20px;\\n  color: #262626;\\n  transition: color 0.2s;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover, .save-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.like-btn.liked[_ngcontent-%COMP%] {\\n  color: #ed4956;\\n}\\n\\n.save-btn.saved[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.post-stats[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.post-stats[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.post-caption[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.post-comments[_ngcontent-%COMP%] {\\n  padding: 0 16px 16px;\\n}\\n\\n.view-comments[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  margin-bottom: 8px;\\n}\\n\\n.comment[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.comment[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.add-comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.add-comment[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  font-size: 14px;\\n  background: transparent;\\n}\\n\\n.post-comment-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  cursor: pointer;\\n  font-size: 14px;\\n}\\n\\n.hashtag[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  cursor: pointer;\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #efefef;\\n  padding: 16px;\\n  background: #fafafa;\\n}\\n\\n.products-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.product-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: white;\\n  padding: 12px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.product-thumb[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 6px;\\n  object-fit: cover;\\n}\\n\\n.product-info-inline[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-info-inline[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n  color: #262626;\\n}\\n\\n.product-info-inline[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #e91e63;\\n  margin: 0 0 8px 0;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%] {\\n  background: none;\\n  border: 1px solid #ddd;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 6px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  color: #666;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%]:hover {\\n  border-color: #e91e63;\\n  color: #e91e63;\\n}\\n\\n.btn-wishlist.active[_ngcontent-%COMP%] {\\n  background: #e91e63;\\n  border-color: #e91e63;\\n  color: white;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: white;\\n  border: none;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  transition: background 0.2s;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%]:hover {\\n  background: #1976d2;\\n}\\n\\n.btn-buy-now[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background 0.2s;\\n}\\n\\n.btn-buy-now[_ngcontent-%COMP%]:hover {\\n  background: #f57c00;\\n}\\n\\n@media (max-width: 768px) {\\n  .product-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n    align-items: stretch;\\n  }\\n  .btn-cart[_ngcontent-%COMP%], .btn-buy-now[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9jb21wb25lbnRzL3Bvc3QtY2FyZC9wb3N0LWNhcmQuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0EsYUFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0FBQU47O0FBR0k7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtBQUFOOztBQUdJO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtBQUFOOztBQUdJO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHNDQUFBO0VBQ0Esa0JBQUE7RUFDQSw0QkFBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxnQ0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsZ0NBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0U7SUFBSywwQ0FBQTtFQUNUO0VBQUk7SUFBTSwyQ0FBQTtFQUdWO0VBRkk7SUFBTyx3Q0FBQTtFQUtYO0FBQ0Y7QUFISTtFQUNFLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsMENBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxXQUFBO0FBS047O0FBRkk7RUFDRSxjQUFBO0FBS047O0FBRkk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7QUFLTjs7QUFGSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBS047O0FBRkk7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSwyQkFBQTtFQUNBLGtCQUFBO0FBS047O0FBRkk7RUFDRSxnQ0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0FBS047O0FBRkk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGlCQUFBO0FBS047O0FBRkk7RUFDRSxhQUFBO0VBQ0EsU0FBQTtBQUtOOztBQUZJOztFQUVFLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxzQkFBQTtBQUtOOztBQUZJOztFQUVFLGNBQUE7QUFLTjs7QUFGSTtFQUNFLGNBQUE7QUFLTjs7QUFGSTtFQUNFLGNBQUE7QUFLTjs7QUFGSTtFQUNFLGVBQUE7RUFDQSxrQkFBQTtBQUtOOztBQUZJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0FBS047O0FBRkk7RUFDRSxlQUFBO0VBQ0Esa0JBQUE7QUFLTjs7QUFGSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtBQUtOOztBQUZJO0VBQ0Usb0JBQUE7QUFLTjs7QUFGSTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0FBS047O0FBRkk7RUFDRSxrQkFBQTtBQUtOOztBQUZJO0VBQ0UsZUFBQTtBQUtOOztBQUZJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLGVBQUE7QUFLTjs7QUFGSTtFQUNFLE9BQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLGVBQUE7RUFDQSx1QkFBQTtBQUtOOztBQUZJO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsMkJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0FBS047O0FBRkk7RUFDRSwyQkFBQTtFQUNBLGVBQUE7QUFLTjs7QUFGSTtFQUNFLDZCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0FBS047O0FBRkk7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBS047O0FBRkk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSx3Q0FBQTtBQUtOOztBQUZJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0FBS047O0FBRkk7RUFDRSxPQUFBO0FBS047O0FBRkk7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFLTjs7QUFGSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtBQUtOOztBQUZJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQUtOOztBQUZJO0VBQ0UsZ0JBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSxvQkFBQTtFQUNBLFdBQUE7QUFLTjs7QUFGSTtFQUNFLHFCQUFBO0VBQ0EsY0FBQTtBQUtOOztBQUZJO0VBQ0UsbUJBQUE7RUFDQSxxQkFBQTtFQUNBLFlBQUE7QUFLTjs7QUFGSTtFQUNFLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSwyQkFBQTtBQUtOOztBQUZJO0VBQ0UsbUJBQUE7QUFLTjs7QUFGSTtFQUNFLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLDJCQUFBO0FBS047O0FBRkk7RUFDRSxtQkFBQTtBQUtOOztBQUZJO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLFFBQUE7SUFDQSxvQkFBQTtFQUtOO0VBRkk7O0lBRUUsdUJBQUE7RUFJTjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnBvc3Qge1xuICAgICAgYmFja2dyb3VuZDogI2ZmZjtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkYmRiZGI7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIH1cblxuICAgIC5wb3N0LWhlYWRlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgfVxuXG4gICAgLnVzZXItaW5mbyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTJweDtcbiAgICB9XG5cbiAgICAudXNlci1hdmF0YXIge1xuICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICBoZWlnaHQ6IDQwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICB9XG5cbiAgICAudXNlci1kZXRhaWxzIGg0IHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycHg7XG4gICAgfVxuXG4gICAgLnVzZXItZGV0YWlscyBzcGFuIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgIH1cblxuICAgIC5tb3JlLW9wdGlvbnMge1xuICAgICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIHBhZGRpbmc6IDhweDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgIH1cblxuICAgIC5wb3N0LW1lZGlhIHtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgYXNwZWN0LXJhdGlvOiAxO1xuICAgIH1cblxuICAgIC5wb3N0LWltYWdlIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtdGFncyB7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDA7XG4gICAgICBsZWZ0OiAwO1xuICAgICAgcmlnaHQ6IDA7XG4gICAgICBib3R0b206IDA7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtdGFnIHtcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICB9XG5cbiAgICAudGFnLWRvdCB7XG4gICAgICB3aWR0aDogMjBweDtcbiAgICAgIGhlaWdodDogMjBweDtcbiAgICAgIGJhY2tncm91bmQ6ICNmZmY7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBib3JkZXI6IDJweCBzb2xpZCB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XG4gICAgfVxuXG4gICAgLnRhZy1kb3Q6OmFmdGVyIHtcbiAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgdG9wOiA1MCU7XG4gICAgICBsZWZ0OiA1MCU7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTtcbiAgICAgIHdpZHRoOiA4cHg7XG4gICAgICBoZWlnaHQ6IDhweDtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIH1cblxuICAgIEBrZXlmcmFtZXMgcHVsc2Uge1xuICAgICAgMCUgeyBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoMCwgMTQ5LCAyNDYsIDAuNyk7IH1cbiAgICAgIDcwJSB7IGJveC1zaGFkb3c6IDAgMCAwIDEwcHggcmdiYSgwLCAxNDksIDI0NiwgMCk7IH1cbiAgICAgIDEwMCUgeyBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoMCwgMTQ5LCAyNDYsIDApOyB9XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtaW5mbyB7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IC0xMjBweDtcbiAgICAgIGxlZnQ6IC0xMDBweDtcbiAgICAgIGJhY2tncm91bmQ6ICNmZmY7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG4gICAgICBwYWRkaW5nOiAxMnB4O1xuICAgICAgd2lkdGg6IDIwMHB4O1xuICAgICAgZGlzcGxheTogbm9uZTtcbiAgICAgIHotaW5kZXg6IDEwO1xuICAgIH1cblxuICAgIC5wcm9kdWN0LXRhZzpob3ZlciAucHJvZHVjdC1pbmZvIHtcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgIH1cblxuICAgIC5wcm9kdWN0LWluZm8gaW1nIHtcbiAgICAgIHdpZHRoOiA2MHB4O1xuICAgICAgaGVpZ2h0OiA2MHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgICBmbG9hdDogbGVmdDtcbiAgICAgIG1hcmdpbi1yaWdodDogMTJweDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1kZXRhaWxzIGg1IHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtZGV0YWlscyBwIHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7XG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XG4gICAgfVxuXG4gICAgLmJ1eS1ub3ctYnRuIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgY29sb3I6ICNmZmY7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBwYWRkaW5nOiA2cHggMTJweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICB9XG5cbiAgICAucG9zdC1hY3Rpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgcGFkZGluZzogOHB4IDE2cHg7XG4gICAgfVxuXG4gICAgLmFjdGlvbi1idXR0b25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDE2cHg7XG4gICAgfVxuXG4gICAgLmFjdGlvbi1idG4sXG4gICAgLnNhdmUtYnRuIHtcbiAgICAgIGJhY2tncm91bmQ6IG5vbmU7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICBwYWRkaW5nOiA4cHg7XG4gICAgICBmb250LXNpemU6IDIwcHg7XG4gICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnM7XG4gICAgfVxuXG4gICAgLmFjdGlvbi1idG46aG92ZXIsXG4gICAgLnNhdmUtYnRuOmhvdmVyIHtcbiAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgIH1cblxuICAgIC5saWtlLWJ0bi5saWtlZCB7XG4gICAgICBjb2xvcjogI2VkNDk1NjtcbiAgICB9XG5cbiAgICAuc2F2ZS1idG4uc2F2ZWQge1xuICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgfVxuXG4gICAgLnBvc3Qtc3RhdHMge1xuICAgICAgcGFkZGluZzogMCAxNnB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgIH1cblxuICAgIC5wb3N0LXN0YXRzIHAge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG5cbiAgICAucG9zdC1jYXB0aW9uIHtcbiAgICAgIHBhZGRpbmc6IDAgMTZweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICB9XG5cbiAgICAucG9zdC1jYXB0aW9uIHAge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgICB9XG5cbiAgICAucG9zdC1jb21tZW50cyB7XG4gICAgICBwYWRkaW5nOiAwIDE2cHggMTZweDtcbiAgICB9XG5cbiAgICAudmlldy1jb21tZW50cyB7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICB9XG5cbiAgICAuY29tbWVudCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgfVxuXG4gICAgLmNvbW1lbnQgcCB7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgfVxuXG4gICAgLmFkZC1jb21tZW50IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBtYXJnaW4tdG9wOiA4cHg7XG4gICAgfVxuXG4gICAgLmFkZC1jb21tZW50IGlucHV0IHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBvdXRsaW5lOiBub25lO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgfVxuXG4gICAgLnBvc3QtY29tbWVudC1idG4ge1xuICAgICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgfVxuXG4gICAgLmhhc2h0YWcge1xuICAgICAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIH1cblxuICAgIC5lY29tbWVyY2UtYWN0aW9ucyB7XG4gICAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2VmZWZlZjtcbiAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmFmYWZhO1xuICAgIH1cblxuICAgIC5wcm9kdWN0cy1zaG93Y2FzZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogMTJweDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1zaG93Y2FzZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTJweDtcbiAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgcGFkZGluZzogMTJweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtdGh1bWIge1xuICAgICAgd2lkdGg6IDYwcHg7XG4gICAgICBoZWlnaHQ6IDYwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1pbmZvLWlubGluZSB7XG4gICAgICBmbGV4OiAxO1xuICAgIH1cblxuICAgIC5wcm9kdWN0LWluZm8taW5saW5lIGg1IHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBtYXJnaW46IDAgMCA0cHggMDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgIH1cblxuICAgIC5wcm9kdWN0LWluZm8taW5saW5lIC5wcmljZSB7XG4gICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgY29sb3I6ICNlOTFlNjM7XG4gICAgICBtYXJnaW46IDAgMCA4cHggMDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1hY3Rpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgfVxuXG4gICAgLmJ0bi13aXNobGlzdCB7XG4gICAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RkZDtcbiAgICAgIHdpZHRoOiAzNnB4O1xuICAgICAgaGVpZ2h0OiAzNnB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgfVxuXG4gICAgLmJ0bi13aXNobGlzdDpob3ZlciB7XG4gICAgICBib3JkZXItY29sb3I6ICNlOTFlNjM7XG4gICAgICBjb2xvcjogI2U5MWU2MztcbiAgICB9XG5cbiAgICAuYnRuLXdpc2hsaXN0LmFjdGl2ZSB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZTkxZTYzO1xuICAgICAgYm9yZGVyLWNvbG9yOiAjZTkxZTYzO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgIH1cblxuICAgIC5idG4tY2FydCB7XG4gICAgICBiYWNrZ3JvdW5kOiAjMjE5NmYzO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgcGFkZGluZzogOHB4IDEycHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDZweDtcbiAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4ycztcbiAgICB9XG5cbiAgICAuYnRuLWNhcnQ6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogIzE5NzZkMjtcbiAgICB9XG5cbiAgICAuYnRuLWJ1eS1ub3cge1xuICAgICAgYmFja2dyb3VuZDogI2ZmOTgwMDtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIHBhZGRpbmc6IDhweCAxNnB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4ycztcbiAgICB9XG5cbiAgICAuYnRuLWJ1eS1ub3c6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogI2Y1N2MwMDtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIC5wcm9kdWN0LWFjdGlvbnMge1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBnYXA6IDZweDtcbiAgICAgICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG4gICAgICB9XG5cbiAgICAgIC5idG4tY2FydCxcbiAgICAgIC5idG4tYnV5LW5vdyB7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgfVxuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PostCardComponent_div_14_Template_button_click_10_listener", "productTag_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onBuyNow", "product", "_id", "ɵɵstyleProp", "position", "y", "x", "ɵɵadvance", "ɵɵproperty", "images", "url", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "price", "PostCardComponent_div_25_div_2_Template_button_click_9_listener", "productTag_r5", "_r4", "addToWishlist", "PostCardComponent_div_25_div_2_Template_button_click_11_listener", "addToCart", "PostCardComponent_div_25_div_2_Template_button_click_14_listener", "buyNow", "ɵɵclassProp", "isInWishlist", "ɵɵclassMap", "ɵɵtemplate", "PostCardComponent_div_25_div_2_Template", "post", "products", "comments", "length", "comment_r6", "user", "username", "text", "PostCardComponent_button_40_Template_button_click_0_listener", "_r7", "addComment", "PostCardComponent", "constructor", "cartService", "wishlistService", "router", "liked", "commented", "shared", "isLiked", "isSaved", "likesCount", "newComment", "showComments", "wishlistItems", "cartItems", "ngOnInit", "analytics", "likes", "loadWishlistItems", "loadCartItems", "saved", "localStorage", "getItem", "JSON", "parse", "getTimeAgo", "date", "now", "Date", "diff", "getTime", "hours", "Math", "floor", "days", "formatCaption", "caption", "replace", "getRecentComments", "slice", "toggleLike", "emit", "toggleSave", "toggleComments", "trim", "postId", "comment", "sharePost", "productId", "toggleWishlist", "subscribe", "next", "response", "showNotification", "error", "console", "toggleWishlistOffline", "getProductById", "then", "success", "catch", "navigate", "productTag", "find", "p", "message", "type", "notification", "document", "createElement", "className", "textContent", "style", "cssText", "head", "append<PERSON><PERSON><PERSON>", "body", "setTimeout", "remove", "ɵɵdirectiveInject", "i1", "CartService", "i2", "WishlistService", "i3", "Router", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PostCardComponent_Template", "rf", "ctx", "PostCardComponent_div_14_Template", "PostCardComponent_Template_button_click_17_listener", "PostCardComponent_Template_button_click_19_listener", "PostCardComponent_Template_button_click_21_listener", "PostCardComponent_Template_button_click_23_listener", "PostCardComponent_div_25_Template", "PostCardComponent_p_36_Template", "PostCardComponent_div_37_Template", "ɵɵtwoWayListener", "PostCardComponent_Template_input_ngModelChange_39_listener", "$event", "ɵɵtwoWayBindingSet", "PostCardComponent_Template_input_keyup_enter_39_listener", "PostCardComponent_button_40_Template", "avatar", "fullName", "createdAt", "media", "alt", "ɵɵsanitizeHtml", "ɵɵtwoWayProperty", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\post-card\\post-card.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { Post } from '../../../../core/models/post.model';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-post-card',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <article class=\"post\">\n      <!-- Post Header -->\n      <div class=\"post-header\">\n        <div class=\"user-info\">\n          <img [src]=\"post.user.avatar\" [alt]=\"post.user.fullName\" class=\"user-avatar\">\n          <div class=\"user-details\">\n            <h4>{{ post.user.username }}</h4>\n            <span>{{ getTimeAgo(post.createdAt) }}</span>\n          </div>\n        </div>\n        <button class=\"more-options\">\n          <i class=\"fas fa-ellipsis-h\"></i>\n        </button>\n      </div>\n\n      <!-- Post Media -->\n      <div class=\"post-media\">\n        <img [src]=\"post.media[0].url\" [alt]=\"post.media[0].alt\" class=\"post-image\">\n        \n        <!-- Product Tags -->\n        <div class=\"product-tags\">\n          <div \n            *ngFor=\"let productTag of post.products\" \n            class=\"product-tag\"\n            [style.top.%]=\"productTag.position.y\"\n            [style.left.%]=\"productTag.position.x\"\n          >\n            <div class=\"tag-dot\"></div>\n            <div class=\"product-info\">\n              <img [src]=\"productTag.product.images[0].url\" [alt]=\"productTag.product.name\">\n              <div class=\"product-details\">\n                <h5>{{ productTag.product.name }}</h5>\n                <p>₹{{ productTag.product.price | number }}</p>\n                <button class=\"buy-now-btn\" (click)=\"onBuyNow(productTag.product._id)\">\n                  Buy Now\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Post Actions -->\n      <div class=\"post-actions\">\n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isLiked\"\n            (click)=\"toggleLike()\"\n          >\n            <i [class]=\"isLiked ? 'fas fa-heart' : 'far fa-heart'\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"toggleComments()\">\n            <i class=\"far fa-comment\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"sharePost()\">\n            <i class=\"far fa-share\"></i>\n          </button>\n        </div>\n        <button class=\"save-btn\" [class.saved]=\"isSaved\" (click)=\"toggleSave()\">\n          <i [class]=\"isSaved ? 'fas fa-bookmark' : 'far fa-bookmark'\"></i>\n        </button>\n      </div>\n\n      <!-- E-commerce Actions -->\n      <div class=\"ecommerce-actions\" *ngIf=\"post.products.length > 0\">\n        <div class=\"products-showcase\">\n          <div *ngFor=\"let productTag of post.products\" class=\"product-showcase\">\n            <img [src]=\"productTag.product.images[0].url\" [alt]=\"productTag.product.name\" class=\"product-thumb\">\n            <div class=\"product-info-inline\">\n              <h5>{{ productTag.product.name }}</h5>\n              <p class=\"price\">₹{{ productTag.product.price | number }}</p>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(productTag.product._id)\" [class.active]=\"isInWishlist(productTag.product._id)\">\n                  <i [class]=\"isInWishlist(productTag.product._id) ? 'fas fa-heart' : 'far fa-heart'\"></i>\n                </button>\n                <button class=\"btn-cart\" (click)=\"addToCart(productTag.product._id)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                  Add to Cart\n                </button>\n                <button class=\"btn-buy-now\" (click)=\"buyNow(productTag.product._id)\">\n                  Buy Now\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Post Stats -->\n      <div class=\"post-stats\">\n        <p><strong>{{ likesCount }} likes</strong></p>\n      </div>\n\n      <!-- Post Caption -->\n      <div class=\"post-caption\">\n        <p>\n          <strong>{{ post.user.username }}</strong> \n          <span [innerHTML]=\"formatCaption(post.caption)\"></span>\n        </p>\n      </div>\n\n      <!-- Post Comments -->\n      <div class=\"post-comments\">\n        <p class=\"view-comments\" *ngIf=\"post.comments.length > 0\">\n          View all {{ post.comments.length }} comments\n        </p>\n        \n        <!-- Recent Comments -->\n        <div *ngFor=\"let comment of getRecentComments()\" class=\"comment\">\n          <p>\n            <strong>{{ comment.user.username }}</strong>\n            {{ comment.text }}\n          </p>\n        </div>\n\n        <!-- Add Comment -->\n        <div class=\"add-comment\">\n          <input \n            type=\"text\" \n            placeholder=\"Add a comment...\"\n            [(ngModel)]=\"newComment\"\n            (keyup.enter)=\"addComment()\"\n          >\n          <button \n            *ngIf=\"newComment.trim()\" \n            (click)=\"addComment()\"\n            class=\"post-comment-btn\"\n          >\n            Post\n          </button>\n        </div>\n      </div>\n    </article>\n  `,\n  styles: [`\n    .post {\n      background: #fff;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      overflow: hidden;\n    }\n\n    .post-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 16px;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .user-details h4 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 2px;\n    }\n\n    .user-details span {\n      font-size: 12px;\n      color: #8e8e8e;\n    }\n\n    .more-options {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 8px;\n      color: #262626;\n    }\n\n    .post-media {\n      position: relative;\n      width: 100%;\n      aspect-ratio: 1;\n    }\n\n    .post-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n    }\n\n    .product-tag {\n      position: absolute;\n      cursor: pointer;\n    }\n\n    .tag-dot {\n      width: 20px;\n      height: 20px;\n      background: #fff;\n      border-radius: 50%;\n      border: 2px solid var(--primary-color);\n      position: relative;\n      animation: pulse 2s infinite;\n    }\n\n    .tag-dot::after {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      width: 8px;\n      height: 8px;\n      background: var(--primary-color);\n      border-radius: 50%;\n    }\n\n    @keyframes pulse {\n      0% { box-shadow: 0 0 0 0 rgba(0, 149, 246, 0.7); }\n      70% { box-shadow: 0 0 0 10px rgba(0, 149, 246, 0); }\n      100% { box-shadow: 0 0 0 0 rgba(0, 149, 246, 0); }\n    }\n\n    .product-info {\n      position: absolute;\n      top: -120px;\n      left: -100px;\n      background: #fff;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      padding: 12px;\n      width: 200px;\n      display: none;\n      z-index: 10;\n    }\n\n    .product-tag:hover .product-info {\n      display: block;\n    }\n\n    .product-info img {\n      width: 60px;\n      height: 60px;\n      border-radius: 4px;\n      object-fit: cover;\n      float: left;\n      margin-right: 12px;\n    }\n\n    .product-details h5 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 4px;\n    }\n\n    .product-details p {\n      font-size: 14px;\n      font-weight: 600;\n      color: var(--primary-color);\n      margin-bottom: 8px;\n    }\n\n    .buy-now-btn {\n      background: var(--primary-color);\n      color: #fff;\n      border: none;\n      padding: 6px 12px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      width: 100%;\n    }\n\n    .post-actions {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 8px 16px;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 16px;\n    }\n\n    .action-btn,\n    .save-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 8px;\n      font-size: 20px;\n      color: #262626;\n      transition: color 0.2s;\n    }\n\n    .action-btn:hover,\n    .save-btn:hover {\n      color: #8e8e8e;\n    }\n\n    .like-btn.liked {\n      color: #ed4956;\n    }\n\n    .save-btn.saved {\n      color: #262626;\n    }\n\n    .post-stats {\n      padding: 0 16px;\n      margin-bottom: 8px;\n    }\n\n    .post-stats p {\n      font-size: 14px;\n      font-weight: 600;\n    }\n\n    .post-caption {\n      padding: 0 16px;\n      margin-bottom: 8px;\n    }\n\n    .post-caption p {\n      font-size: 14px;\n      line-height: 1.4;\n    }\n\n    .post-comments {\n      padding: 0 16px 16px;\n    }\n\n    .view-comments {\n      font-size: 14px;\n      color: #8e8e8e;\n      cursor: pointer;\n      margin-bottom: 8px;\n    }\n\n    .comment {\n      margin-bottom: 4px;\n    }\n\n    .comment p {\n      font-size: 14px;\n    }\n\n    .add-comment {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 8px;\n    }\n\n    .add-comment input {\n      flex: 1;\n      border: none;\n      outline: none;\n      font-size: 14px;\n      background: transparent;\n    }\n\n    .post-comment-btn {\n      background: none;\n      border: none;\n      color: var(--primary-color);\n      font-weight: 600;\n      cursor: pointer;\n      font-size: 14px;\n    }\n\n    .hashtag {\n      color: var(--primary-color);\n      cursor: pointer;\n    }\n\n    .ecommerce-actions {\n      border-top: 1px solid #efefef;\n      padding: 16px;\n      background: #fafafa;\n    }\n\n    .products-showcase {\n      display: flex;\n      flex-direction: column;\n      gap: 12px;\n    }\n\n    .product-showcase {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      background: white;\n      padding: 12px;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .product-thumb {\n      width: 60px;\n      height: 60px;\n      border-radius: 6px;\n      object-fit: cover;\n    }\n\n    .product-info-inline {\n      flex: 1;\n    }\n\n    .product-info-inline h5 {\n      font-size: 14px;\n      font-weight: 600;\n      margin: 0 0 4px 0;\n      color: #262626;\n    }\n\n    .product-info-inline .price {\n      font-size: 16px;\n      font-weight: 700;\n      color: #e91e63;\n      margin: 0 0 8px 0;\n    }\n\n    .product-actions {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .btn-wishlist {\n      background: none;\n      border: 1px solid #ddd;\n      width: 36px;\n      height: 36px;\n      border-radius: 6px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.2s;\n      color: #666;\n    }\n\n    .btn-wishlist:hover {\n      border-color: #e91e63;\n      color: #e91e63;\n    }\n\n    .btn-wishlist.active {\n      background: #e91e63;\n      border-color: #e91e63;\n      color: white;\n    }\n\n    .btn-cart {\n      background: #2196f3;\n      color: white;\n      border: none;\n      padding: 8px 12px;\n      border-radius: 6px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      transition: background 0.2s;\n    }\n\n    .btn-cart:hover {\n      background: #1976d2;\n    }\n\n    .btn-buy-now {\n      background: #ff9800;\n      color: white;\n      border: none;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: background 0.2s;\n    }\n\n    .btn-buy-now:hover {\n      background: #f57c00;\n    }\n\n    @media (max-width: 768px) {\n      .product-actions {\n        flex-direction: column;\n        gap: 6px;\n        align-items: stretch;\n      }\n\n      .btn-cart,\n      .btn-buy-now {\n        justify-content: center;\n      }\n    }\n  `]\n})\nexport class PostCardComponent implements OnInit {\n  @Input() post!: Post;\n  @Output() liked = new EventEmitter<string>();\n  @Output() commented = new EventEmitter<{ postId: string; comment: string }>();\n  @Output() shared = new EventEmitter<string>();\n\n  isLiked = false;\n  isSaved = false;\n  likesCount = 0;\n  newComment = '';\n  showComments = false;\n  wishlistItems: string[] = [];\n  cartItems: string[] = [];\n\n  constructor(\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.likesCount = this.post.analytics.likes;\n    this.loadWishlistItems();\n    this.loadCartItems();\n  }\n\n  loadWishlistItems() {\n    // Load from localStorage for demo\n    const saved = localStorage.getItem('wishlist');\n    this.wishlistItems = saved ? JSON.parse(saved) : [];\n  }\n\n  loadCartItems() {\n    // Load from localStorage for demo\n    const saved = localStorage.getItem('cart');\n    this.cartItems = saved ? JSON.parse(saved) : [];\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    \n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n\n  formatCaption(caption: string): string {\n    return caption.replace(/#(\\w+)/g, '<span class=\"hashtag\">#$1</span>');\n  }\n\n  getRecentComments() {\n    return this.post.comments.slice(-2);\n  }\n\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    this.likesCount += this.isLiked ? 1 : -1;\n    this.liked.emit(this.post._id);\n  }\n\n  toggleSave() {\n    this.isSaved = !this.isSaved;\n  }\n\n  toggleComments() {\n    this.showComments = !this.showComments;\n  }\n\n  addComment() {\n    if (this.newComment.trim()) {\n      this.commented.emit({\n        postId: this.post._id,\n        comment: this.newComment.trim()\n      });\n      this.newComment = '';\n    }\n  }\n\n  sharePost() {\n    this.shared.emit(this.post._id);\n  }\n\n  // E-commerce methods\n  isInWishlist(productId: string): boolean {\n    return this.wishlistService.isInWishlist(productId);\n  }\n\n  addToWishlist(productId: string) {\n    this.wishlistService.toggleWishlist(productId).subscribe({\n      next: (response) => {\n        if (this.isInWishlist(productId)) {\n          this.showNotification('Removed from wishlist', 'info');\n        } else {\n          this.showNotification('Added to wishlist ❤️', 'success');\n        }\n      },\n      error: (error) => {\n        console.error('Wishlist error:', error);\n        // Fallback to offline mode\n        this.wishlistService.toggleWishlistOffline(this.getProductById(productId));\n        this.showNotification(this.isInWishlist(productId) ? 'Removed from wishlist' : 'Added to wishlist ❤️', 'success');\n      }\n    });\n  }\n\n  addToCart(productId: string) {\n    const product = this.getProductById(productId);\n    if (!product) return;\n\n    this.cartService.addToCart(product, 1).then((success) => {\n      if (success) {\n        this.showNotification('Added to cart 🛒', 'success');\n      }\n    }).catch((error) => {\n      console.error('Cart error:', error);\n      this.showNotification('Failed to add to cart', 'error');\n    });\n  }\n\n  buyNow(productId: string) {\n    const product = this.getProductById(productId);\n    if (!product) return;\n\n    this.cartService.addToCart(product, 1).then((success) => {\n      if (success) {\n        this.showNotification('Redirecting to checkout...', 'info');\n        this.router.navigate(['/checkout']);\n      }\n    }).catch((error) => {\n      console.error('Buy now error:', error);\n      this.showNotification('Failed to process purchase', 'error');\n    });\n  }\n\n  private getProductById(productId: string): any {\n    // Find product in post's products array\n    const productTag = this.post.products.find(p => p.product._id === productId);\n    return productTag ? productTag.product : null;\n  }\n\n  onBuyNow(productId: string) {\n    this.buyNow(productId);\n  }\n\n  private showNotification(message: string, type: 'success' | 'info' | 'error') {\n    // Create notification element\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n\n    // Add styles\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n\n    // Add animation styles\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes slideIn {\n        from { transform: translateX(100%); opacity: 0; }\n        to { transform: translateX(0); opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n\n    document.body.appendChild(notification);\n\n    // Remove after 3 seconds\n    setTimeout(() => {\n      notification.remove();\n      style.remove();\n    }, 3000);\n  }\n}\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;IAiClCC,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,cAA2B;IAC3BF,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,cAA8E;IAE5EF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAwC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC/CJ,EAAA,CAAAC,cAAA,kBAAuE;IAA3CD,EAAA,CAAAK,UAAA,mBAAAC,2DAAA;MAAA,MAAAC,aAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAAP,aAAA,CAAAQ,OAAA,CAAAC,GAAA,CAAgC;IAAA,EAAC;IACpEhB,EAAA,CAAAG,MAAA,iBACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;IAbJJ,EADA,CAAAiB,WAAA,QAAAV,aAAA,CAAAW,QAAA,CAAAC,CAAA,MAAqC,SAAAZ,aAAA,CAAAW,QAAA,CAAAE,CAAA,MACC;IAI/BpB,EAAA,CAAAqB,SAAA,GAAwC;IAACrB,EAAzC,CAAAsB,UAAA,QAAAf,aAAA,CAAAQ,OAAA,CAAAQ,MAAA,IAAAC,GAAA,EAAAxB,EAAA,CAAAyB,aAAA,CAAwC,QAAAlB,aAAA,CAAAQ,OAAA,CAAAW,IAAA,CAAgC;IAEvE1B,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAA2B,iBAAA,CAAApB,aAAA,CAAAQ,OAAA,CAAAW,IAAA,CAA6B;IAC9B1B,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAA4B,kBAAA,WAAA5B,EAAA,CAAA6B,WAAA,OAAAtB,aAAA,CAAAQ,OAAA,CAAAe,KAAA,MAAwC;;;;;;IAmCjD9B,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAAoG;IAElGF,EADF,CAAAC,cAAA,cAAiC,SAC3B;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtCJ,EAAA,CAAAC,cAAA,YAAiB;IAAAD,EAAA,CAAAG,MAAA,GAAwC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE3DJ,EADF,CAAAC,cAAA,cAA6B,iBACwG;IAAtGD,EAAA,CAAAK,UAAA,mBAAA0B,gEAAA;MAAA,MAAAC,aAAA,GAAAhC,EAAA,CAAAQ,aAAA,CAAAyB,GAAA,EAAAvB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAuB,aAAA,CAAAF,aAAA,CAAAjB,OAAA,CAAAC,GAAA,CAAqC;IAAA,EAAC;IAC1EhB,EAAA,CAAAE,SAAA,SAAwF;IAC1FF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAqE;IAA5CD,EAAA,CAAAK,UAAA,mBAAA8B,iEAAA;MAAA,MAAAH,aAAA,GAAAhC,EAAA,CAAAQ,aAAA,CAAAyB,GAAA,EAAAvB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAyB,SAAA,CAAAJ,aAAA,CAAAjB,OAAA,CAAAC,GAAA,CAAiC;IAAA,EAAC;IAClEhB,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAqE;IAAzCD,EAAA,CAAAK,UAAA,mBAAAgC,iEAAA;MAAA,MAAAL,aAAA,GAAAhC,EAAA,CAAAQ,aAAA,CAAAyB,GAAA,EAAAvB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA2B,MAAA,CAAAN,aAAA,CAAAjB,OAAA,CAAAC,GAAA,CAA8B;IAAA,EAAC;IAClEhB,EAAA,CAAAG,MAAA,iBACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;IAjBCJ,EAAA,CAAAqB,SAAA,EAAwC;IAACrB,EAAzC,CAAAsB,UAAA,QAAAU,aAAA,CAAAjB,OAAA,CAAAQ,MAAA,IAAAC,GAAA,EAAAxB,EAAA,CAAAyB,aAAA,CAAwC,QAAAO,aAAA,CAAAjB,OAAA,CAAAW,IAAA,CAAgC;IAEvE1B,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAA2B,iBAAA,CAAAK,aAAA,CAAAjB,OAAA,CAAAW,IAAA,CAA6B;IAChB1B,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAA4B,kBAAA,WAAA5B,EAAA,CAAA6B,WAAA,OAAAG,aAAA,CAAAjB,OAAA,CAAAe,KAAA,MAAwC;IAEsB9B,EAAA,CAAAqB,SAAA,GAAqD;IAArDrB,EAAA,CAAAuC,WAAA,WAAA5B,MAAA,CAAA6B,YAAA,CAAAR,aAAA,CAAAjB,OAAA,CAAAC,GAAA,EAAqD;IAC7HhB,EAAA,CAAAqB,SAAA,EAAgF;IAAhFrB,EAAA,CAAAyC,UAAA,CAAA9B,MAAA,CAAA6B,YAAA,CAAAR,aAAA,CAAAjB,OAAA,CAAAC,GAAA,oCAAgF;;;;;IAR7FhB,EADF,CAAAC,cAAA,cAAgE,cAC/B;IAC7BD,EAAA,CAAA0C,UAAA,IAAAC,uCAAA,oBAAuE;IAoB3E3C,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApB0BJ,EAAA,CAAAqB,SAAA,GAAgB;IAAhBrB,EAAA,CAAAsB,UAAA,YAAAX,MAAA,CAAAiC,IAAA,CAAAC,QAAA,CAAgB;;;;;IAqC9C7C,EAAA,CAAAC,cAAA,YAA0D;IACxDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA4B,kBAAA,eAAAjB,MAAA,CAAAiC,IAAA,CAAAE,QAAA,CAAAC,MAAA,eACF;;;;;IAKI/C,EAFJ,CAAAC,cAAA,cAAiE,QAC5D,aACO;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5CJ,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACA;;;;IAHMJ,EAAA,CAAAqB,SAAA,GAA2B;IAA3BrB,EAAA,CAAA2B,iBAAA,CAAAqB,UAAA,CAAAC,IAAA,CAAAC,QAAA,CAA2B;IACnClD,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA4B,kBAAA,MAAAoB,UAAA,CAAAG,IAAA,MACF;;;;;;IAWAnD,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAK,UAAA,mBAAA+C,6DAAA;MAAApD,EAAA,CAAAQ,aAAA,CAAA6C,GAAA;MAAA,MAAA1C,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA2C,UAAA,EAAY;IAAA,EAAC;IAGtBtD,EAAA,CAAAG,MAAA,aACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;AAmYnB,OAAM,MAAOmD,iBAAiB;EAc5BC,YACUC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAfN,KAAAC,KAAK,GAAG,IAAI/D,YAAY,EAAU;IAClC,KAAAgE,SAAS,GAAG,IAAIhE,YAAY,EAAuC;IACnE,KAAAiE,MAAM,GAAG,IAAIjE,YAAY,EAAU;IAE7C,KAAAkE,OAAO,GAAG,KAAK;IACf,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAAC,SAAS,GAAa,EAAE;EAMrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACL,UAAU,GAAG,IAAI,CAACrB,IAAI,CAAC2B,SAAS,CAACC,KAAK;IAC3C,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAD,iBAAiBA,CAAA;IACf;IACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC9C,IAAI,CAACT,aAAa,GAAGO,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,EAAE;EACrD;EAEAD,aAAaA,CAAA;IACX;IACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACR,SAAS,GAAGM,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,EAAE;EACjD;EAEAK,UAAUA,CAACC,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACrD,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIE,KAAK,GAAG,CAAC,EAAE,OAAO,KAAK;IAC3B,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,GAAG;IAClC,MAAMG,IAAI,GAAGF,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,EAAE,CAAC;IACnC,OAAO,GAAGG,IAAI,GAAG;EACnB;EAEAC,aAAaA,CAACC,OAAe;IAC3B,OAAOA,OAAO,CAACC,OAAO,CAAC,SAAS,EAAE,kCAAkC,CAAC;EACvE;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACjD,IAAI,CAACE,QAAQ,CAACgD,KAAK,CAAC,CAAC,CAAC,CAAC;EACrC;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAChC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACE,UAAU,IAAI,IAAI,CAACF,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC,IAAI,CAACH,KAAK,CAACoC,IAAI,CAAC,IAAI,CAACpD,IAAI,CAAC5B,GAAG,CAAC;EAChC;EAEAiF,UAAUA,CAAA;IACR,IAAI,CAACjC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;EAC9B;EAEAkC,cAAcA,CAAA;IACZ,IAAI,CAAC/B,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAb,UAAUA,CAAA;IACR,IAAI,IAAI,CAACY,UAAU,CAACiC,IAAI,EAAE,EAAE;MAC1B,IAAI,CAACtC,SAAS,CAACmC,IAAI,CAAC;QAClBI,MAAM,EAAE,IAAI,CAACxD,IAAI,CAAC5B,GAAG;QACrBqF,OAAO,EAAE,IAAI,CAACnC,UAAU,CAACiC,IAAI;OAC9B,CAAC;MACF,IAAI,CAACjC,UAAU,GAAG,EAAE;;EAExB;EAEAoC,SAASA,CAAA;IACP,IAAI,CAACxC,MAAM,CAACkC,IAAI,CAAC,IAAI,CAACpD,IAAI,CAAC5B,GAAG,CAAC;EACjC;EAEA;EACAwB,YAAYA,CAAC+D,SAAiB;IAC5B,OAAO,IAAI,CAAC7C,eAAe,CAAClB,YAAY,CAAC+D,SAAS,CAAC;EACrD;EAEArE,aAAaA,CAACqE,SAAiB;IAC7B,IAAI,CAAC7C,eAAe,CAAC8C,cAAc,CAACD,SAAS,CAAC,CAACE,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,IAAI,CAACnE,YAAY,CAAC+D,SAAS,CAAC,EAAE;UAChC,IAAI,CAACK,gBAAgB,CAAC,uBAAuB,EAAE,MAAM,CAAC;SACvD,MAAM;UACL,IAAI,CAACA,gBAAgB,CAAC,sBAAsB,EAAE,SAAS,CAAC;;MAE5D,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvC;QACA,IAAI,CAACnD,eAAe,CAACqD,qBAAqB,CAAC,IAAI,CAACC,cAAc,CAACT,SAAS,CAAC,CAAC;QAC1E,IAAI,CAACK,gBAAgB,CAAC,IAAI,CAACpE,YAAY,CAAC+D,SAAS,CAAC,GAAG,uBAAuB,GAAG,sBAAsB,EAAE,SAAS,CAAC;MACnH;KACD,CAAC;EACJ;EAEAnE,SAASA,CAACmE,SAAiB;IACzB,MAAMxF,OAAO,GAAG,IAAI,CAACiG,cAAc,CAACT,SAAS,CAAC;IAC9C,IAAI,CAACxF,OAAO,EAAE;IAEd,IAAI,CAAC0C,WAAW,CAACrB,SAAS,CAACrB,OAAO,EAAE,CAAC,CAAC,CAACkG,IAAI,CAAEC,OAAO,IAAI;MACtD,IAAIA,OAAO,EAAE;QACX,IAAI,CAACN,gBAAgB,CAAC,kBAAkB,EAAE,SAAS,CAAC;;IAExD,CAAC,CAAC,CAACO,KAAK,CAAEN,KAAK,IAAI;MACjBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,IAAI,CAACD,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC;IACzD,CAAC,CAAC;EACJ;EAEAtE,MAAMA,CAACiE,SAAiB;IACtB,MAAMxF,OAAO,GAAG,IAAI,CAACiG,cAAc,CAACT,SAAS,CAAC;IAC9C,IAAI,CAACxF,OAAO,EAAE;IAEd,IAAI,CAAC0C,WAAW,CAACrB,SAAS,CAACrB,OAAO,EAAE,CAAC,CAAC,CAACkG,IAAI,CAAEC,OAAO,IAAI;MACtD,IAAIA,OAAO,EAAE;QACX,IAAI,CAACN,gBAAgB,CAAC,4BAA4B,EAAE,MAAM,CAAC;QAC3D,IAAI,CAACjD,MAAM,CAACyD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;IAEvC,CAAC,CAAC,CAACD,KAAK,CAAEN,KAAK,IAAI;MACjBC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAACD,gBAAgB,CAAC,4BAA4B,EAAE,OAAO,CAAC;IAC9D,CAAC,CAAC;EACJ;EAEQI,cAAcA,CAACT,SAAiB;IACtC;IACA,MAAMc,UAAU,GAAG,IAAI,CAACzE,IAAI,CAACC,QAAQ,CAACyE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxG,OAAO,CAACC,GAAG,KAAKuF,SAAS,CAAC;IAC5E,OAAOc,UAAU,GAAGA,UAAU,CAACtG,OAAO,GAAG,IAAI;EAC/C;EAEAD,QAAQA,CAACyF,SAAiB;IACxB,IAAI,CAACjE,MAAM,CAACiE,SAAS,CAAC;EACxB;EAEQK,gBAAgBA,CAACY,OAAe,EAAEC,IAAkC;IAC1E;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,6BAA6BJ,IAAI,EAAE;IAC5DC,YAAY,CAACI,WAAW,GAAGN,OAAO;IAElC;IACAE,YAAY,CAACK,KAAK,CAACC,OAAO,GAAG;;;;oBAIbP,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGA,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;;;;;;;;;KASxF;IAED;IACA,MAAMM,KAAK,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CG,KAAK,CAACD,WAAW,GAAG;;;;;KAKnB;IACDH,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,KAAK,CAAC;IAEhCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACAU,UAAU,CAAC,MAAK;MACdV,YAAY,CAACW,MAAM,EAAE;MACrBN,KAAK,CAACM,MAAM,EAAE;IAChB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBA1LW9E,iBAAiB,EAAAvD,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxI,EAAA,CAAAsI,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA1I,EAAA,CAAAsI,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjBrF,iBAAiB;MAAAsF,SAAA;MAAAC,MAAA;QAAAlG,IAAA;MAAA;MAAAmG,OAAA;QAAAnF,KAAA;QAAAC,SAAA;QAAAC,MAAA;MAAA;MAAAkF,UAAA;MAAAC,QAAA,GAAAjJ,EAAA,CAAAkJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlgBtBxJ,EAHJ,CAAAC,cAAA,iBAAsB,aAEK,aACA;UACrBD,EAAA,CAAAE,SAAA,aAA6E;UAE3EF,EADF,CAAAC,cAAA,aAA0B,SACpB;UAAAD,EAAA,CAAAG,MAAA,GAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjCJ,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAG,MAAA,GAAgC;UAE1CH,EAF0C,CAAAI,YAAA,EAAO,EACzC,EACF;UACNJ,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAE,SAAA,YAAiC;UAErCF,EADE,CAAAI,YAAA,EAAS,EACL;UAGNJ,EAAA,CAAAC,cAAA,cAAwB;UACtBD,EAAA,CAAAE,SAAA,cAA4E;UAG5EF,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAA0C,UAAA,KAAAgH,iCAAA,oBAKC;UAcL1J,EADE,CAAAI,YAAA,EAAM,EACF;UAKFJ,EAFJ,CAAAC,cAAA,eAA0B,eACI,kBAKzB;UADCD,EAAA,CAAAK,UAAA,mBAAAsJ,oDAAA;YAAA,OAASF,GAAA,CAAA1D,UAAA,EAAY;UAAA,EAAC;UAEtB/F,EAAA,CAAAE,SAAA,SAA2D;UAC7DF,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAAsD;UAA3BD,EAAA,CAAAK,UAAA,mBAAAuJ,oDAAA;YAAA,OAASH,GAAA,CAAAvD,cAAA,EAAgB;UAAA,EAAC;UACnDlG,EAAA,CAAAE,SAAA,aAA8B;UAChCF,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAAiD;UAAtBD,EAAA,CAAAK,UAAA,mBAAAwJ,oDAAA;YAAA,OAASJ,GAAA,CAAAnD,SAAA,EAAW;UAAA,EAAC;UAC9CtG,EAAA,CAAAE,SAAA,aAA4B;UAEhCF,EADE,CAAAI,YAAA,EAAS,EACL;UACNJ,EAAA,CAAAC,cAAA,kBAAwE;UAAvBD,EAAA,CAAAK,UAAA,mBAAAyJ,oDAAA;YAAA,OAASL,GAAA,CAAAxD,UAAA,EAAY;UAAA,EAAC;UACrEjG,EAAA,CAAAE,SAAA,SAAiE;UAErEF,EADE,CAAAI,YAAA,EAAS,EACL;UAGNJ,EAAA,CAAA0C,UAAA,KAAAqH,iCAAA,kBAAgE;UA0B3D/J,EADL,CAAAC,cAAA,eAAwB,SACnB,cAAQ;UAAAD,EAAA,CAAAG,MAAA,IAAsB;UACnCH,EADmC,CAAAI,YAAA,EAAS,EAAI,EAC1C;UAKFJ,EAFJ,CAAAC,cAAA,eAA0B,SACrB,cACO;UAAAD,EAAA,CAAAG,MAAA,IAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACzCJ,EAAA,CAAAE,SAAA,gBAAuD;UAE3DF,EADE,CAAAI,YAAA,EAAI,EACA;UAGNJ,EAAA,CAAAC,cAAA,eAA2B;UAMzBD,EALA,CAAA0C,UAAA,KAAAsH,+BAAA,gBAA0D,KAAAC,iCAAA,kBAKO;UAS/DjK,EADF,CAAAC,cAAA,eAAyB,iBAMtB;UAFCD,EAAA,CAAAkK,gBAAA,2BAAAC,2DAAAC,MAAA;YAAApK,EAAA,CAAAqK,kBAAA,CAAAZ,GAAA,CAAAvF,UAAA,EAAAkG,MAAA,MAAAX,GAAA,CAAAvF,UAAA,GAAAkG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACxBpK,EAAA,CAAAK,UAAA,yBAAAiK,yDAAA;YAAA,OAAeb,GAAA,CAAAnG,UAAA,EAAY;UAAA,EAAC;UAJ9BtD,EAAA,CAAAI,YAAA,EAKC;UACDJ,EAAA,CAAA0C,UAAA,KAAA6H,oCAAA,qBAIC;UAKPvK,EAFI,CAAAI,YAAA,EAAM,EACF,EACE;;;UAjICJ,EAAA,CAAAqB,SAAA,GAAwB;UAACrB,EAAzB,CAAAsB,UAAA,QAAAmI,GAAA,CAAA7G,IAAA,CAAAK,IAAA,CAAAuH,MAAA,EAAAxK,EAAA,CAAAyB,aAAA,CAAwB,QAAAgI,GAAA,CAAA7G,IAAA,CAAAK,IAAA,CAAAwH,QAAA,CAA2B;UAElDzK,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAA2B,iBAAA,CAAA8H,GAAA,CAAA7G,IAAA,CAAAK,IAAA,CAAAC,QAAA,CAAwB;UACtBlD,EAAA,CAAAqB,SAAA,GAAgC;UAAhCrB,EAAA,CAAA2B,iBAAA,CAAA8H,GAAA,CAAAzE,UAAA,CAAAyE,GAAA,CAAA7G,IAAA,CAAA8H,SAAA,EAAgC;UAUrC1K,EAAA,CAAAqB,SAAA,GAAyB;UAACrB,EAA1B,CAAAsB,UAAA,QAAAmI,GAAA,CAAA7G,IAAA,CAAA+H,KAAA,IAAAnJ,GAAA,EAAAxB,EAAA,CAAAyB,aAAA,CAAyB,QAAAgI,GAAA,CAAA7G,IAAA,CAAA+H,KAAA,IAAAC,GAAA,CAA0B;UAK7B5K,EAAA,CAAAqB,SAAA,GAAgB;UAAhBrB,EAAA,CAAAsB,UAAA,YAAAmI,GAAA,CAAA7G,IAAA,CAAAC,QAAA,CAAgB;UAyBvC7C,EAAA,CAAAqB,SAAA,GAAuB;UAAvBrB,EAAA,CAAAuC,WAAA,UAAAkH,GAAA,CAAA1F,OAAA,CAAuB;UAGpB/D,EAAA,CAAAqB,SAAA,EAAmD;UAAnDrB,EAAA,CAAAyC,UAAA,CAAAgH,GAAA,CAAA1F,OAAA,mCAAmD;UASjC/D,EAAA,CAAAqB,SAAA,GAAuB;UAAvBrB,EAAA,CAAAuC,WAAA,UAAAkH,GAAA,CAAAzF,OAAA,CAAuB;UAC3ChE,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAyC,UAAA,CAAAgH,GAAA,CAAAzF,OAAA,yCAAyD;UAKhChE,EAAA,CAAAqB,SAAA,EAA8B;UAA9BrB,EAAA,CAAAsB,UAAA,SAAAmI,GAAA,CAAA7G,IAAA,CAAAC,QAAA,CAAAE,MAAA,KAA8B;UA0BjD/C,EAAA,CAAAqB,SAAA,GAAsB;UAAtBrB,EAAA,CAAA4B,kBAAA,KAAA6H,GAAA,CAAAxF,UAAA,WAAsB;UAMvBjE,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAA2B,iBAAA,CAAA8H,GAAA,CAAA7G,IAAA,CAAAK,IAAA,CAAAC,QAAA,CAAwB;UAC1BlD,EAAA,CAAAqB,SAAA,EAAyC;UAAzCrB,EAAA,CAAAsB,UAAA,cAAAmI,GAAA,CAAA/D,aAAA,CAAA+D,GAAA,CAAA7G,IAAA,CAAA+C,OAAA,GAAA3F,EAAA,CAAA6K,cAAA,CAAyC;UAMvB7K,EAAA,CAAAqB,SAAA,GAA8B;UAA9BrB,EAAA,CAAAsB,UAAA,SAAAmI,GAAA,CAAA7G,IAAA,CAAAE,QAAA,CAAAC,MAAA,KAA8B;UAK/B/C,EAAA,CAAAqB,SAAA,EAAsB;UAAtBrB,EAAA,CAAAsB,UAAA,YAAAmI,GAAA,CAAA5D,iBAAA,GAAsB;UAY3C7F,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAA8K,gBAAA,YAAArB,GAAA,CAAAvF,UAAA,CAAwB;UAIvBlE,EAAA,CAAAqB,SAAA,EAAuB;UAAvBrB,EAAA,CAAAsB,UAAA,SAAAmI,GAAA,CAAAvF,UAAA,CAAAiC,IAAA,GAAuB;;;qBA/HxBrG,YAAY,EAAAiL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEnL,WAAW,EAAAoL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}