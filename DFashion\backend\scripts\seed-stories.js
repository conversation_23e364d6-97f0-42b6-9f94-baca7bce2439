const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Story = require('../models/Story');
const User = require('../models/User');
const Product = require('../models/Product');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/dfashion');
    console.log('✅ MongoDB connected for seeding stories');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample stories data
const sampleStories = [
  {
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600'
    },
    caption: 'Check out this amazing vintage denim jacket! 💙 Perfect for any season.',
    linkedContent: {
      type: 'product'
    }
  },
  {
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600'
    },
    caption: 'Elegant evening look with this silk dress 💫 Perfect for date nights!',
    linkedContent: {
      type: 'product'
    }
  },
  {
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600'
    },
    caption: 'Cozy vibes in this amazing knit sweater! 🍂 Perfect for autumn days.',
    linkedContent: {
      type: 'product'
    }
  },
  {
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=600'
    },
    caption: 'Perfect fit with these high-waisted jeans! 👖 Comfort meets style.',
    linkedContent: {
      type: 'product'
    }
  },
  {
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600'
    },
    caption: 'Summer vibes with this floral dress! 🌸 Light and breezy.',
    linkedContent: {
      type: 'product'
    }
  }
];

// Seed stories function
const seedStories = async () => {
  try {
    console.log('🌱 Starting to seed stories...');

    // Clear existing stories
    await Story.deleteMany({});
    console.log('🗑️ Cleared existing stories');

    // Get sample users and products
    const users = await User.find().limit(5);
    const products = await Product.find().limit(5);

    if (users.length === 0) {
      console.log('⚠️ No users found. Creating sample users...');
      // Create sample users if none exist
      const sampleUsers = [
        {
          username: 'fashionista_maya',
          email: '<EMAIL>',
          fullName: 'Maya Rodriguez',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',
          role: 'customer'
        },
        {
          username: 'style_guru_alex',
          email: '<EMAIL>',
          fullName: 'Alex Chen',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
          role: 'customer'
        },
        {
          username: 'trendy_sarah',
          email: '<EMAIL>',
          fullName: 'Sarah Johnson',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
          role: 'customer'
        },
        {
          username: 'urban_style',
          email: '<EMAIL>',
          fullName: 'Mike Thompson',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
          role: 'customer'
        },
        {
          username: 'boho_chic',
          email: '<EMAIL>',
          fullName: 'Emma Wilson',
          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
          role: 'customer'
        }
      ];

      for (const userData of sampleUsers) {
        const user = new User(userData);
        await user.save();
        users.push(user);
      }
      console.log('✅ Created sample users');
    }

    if (products.length === 0) {
      console.log('⚠️ No products found. Stories will be created without product links.');
    }

    // Create stories
    const stories = [];
    for (let i = 0; i < sampleStories.length; i++) {
      const storyData = sampleStories[i];
      const user = users[i % users.length];
      const product = products[i % products.length];

      const story = new Story({
        user: user._id,
        media: storyData.media,
        caption: storyData.caption,
        linkedContent: {
          type: 'product',
          productId: product ? product._id : null
        },
        products: product ? [{
          product: product._id,
          position: { x: 30 + (i * 10), y: 40 + (i * 5) }
        }] : [],
        isActive: true,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        analytics: {
          views: Math.floor(Math.random() * 1000) + 100,
          likes: Math.floor(Math.random() * 100) + 10,
          shares: Math.floor(Math.random() * 50) + 1
        }
      });

      await story.save();
      stories.push(story);
    }

    console.log(`✅ Created ${stories.length} sample stories`);
    console.log('🎉 Stories seeding completed successfully!');

    // Display created stories
    const createdStories = await Story.find()
      .populate('user', 'username fullName avatar')
      .populate('products.product', 'name price');
    
    console.log('\n📋 Created Stories:');
    createdStories.forEach((story, index) => {
      console.log(`${index + 1}. ${story.user.username}: ${story.caption.substring(0, 50)}...`);
    });

  } catch (error) {
    console.error('❌ Error seeding stories:', error);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await seedStories();
  await mongoose.connection.close();
  console.log('🔌 Database connection closed');
  process.exit(0);
};

// Run the script
main();
