{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nfunction VendorDashboardComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(activity_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r1.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 4, activity_r1.timestamp, \"short\"));\n  }\n}\nexport let VendorDashboardComponent = /*#__PURE__*/(() => {\n  class VendorDashboardComponent {\n    constructor(authService) {\n      this.authService = authService;\n      this.currentUser = null;\n      this.stats = {\n        totalProducts: 0,\n        totalOrders: 0,\n        totalRevenue: 0,\n        totalPosts: 0\n      };\n      this.recentActivity = [{\n        icon: 'fas fa-plus text-success',\n        message: 'New product \"Summer Dress\" was added',\n        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)\n      }, {\n        icon: 'fas fa-shopping-cart text-primary',\n        message: 'Order #12345 was placed',\n        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)\n      }, {\n        icon: 'fas fa-camera text-info',\n        message: 'New post was published',\n        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)\n      }];\n    }\n    ngOnInit() {\n      this.loadUserData();\n      this.loadStats();\n    }\n    loadUserData() {\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n      });\n    }\n    loadStats() {\n      // Load stats from API\n      this.stats = {\n        totalProducts: 0,\n        totalOrders: 0,\n        totalRevenue: 0,\n        totalPosts: 0\n      };\n    }\n    static {\n      this.ɵfac = function VendorDashboardComponent_Factory(t) {\n        return new (t || VendorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VendorDashboardComponent,\n        selectors: [[\"app-vendor-dashboard\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 105,\n        vars: 9,\n        consts: [[1, \"vendor-dashboard\"], [1, \"dashboard-header\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"fas\", \"fa-box\"], [1, \"stat-content\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"fas\", \"fa-rupee-sign\"], [1, \"fas\", \"fa-images\"], [1, \"quick-actions\"], [1, \"actions-grid\"], [\"routerLink\", \"/vendor/products/create\", 1, \"action-card\"], [1, \"action-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"action-content\"], [\"routerLink\", \"/vendor/posts/create\", 1, \"action-card\"], [1, \"fas\", \"fa-camera\"], [\"routerLink\", \"/vendor/stories/create\", 1, \"action-card\"], [1, \"fas\", \"fa-video\"], [\"routerLink\", \"/vendor/orders\", 1, \"action-card\"], [1, \"fas\", \"fa-list\"], [1, \"recent-activity\"], [1, \"activity-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"vendor-menu\"], [1, \"menu-grid\"], [\"routerLink\", \"/vendor/products\", 1, \"menu-item\"], [\"routerLink\", \"/vendor/posts\", 1, \"menu-item\"], [\"routerLink\", \"/vendor/stories\", 1, \"menu-item\"], [1, \"fas\", \"fa-play-circle\"], [\"routerLink\", \"/vendor/orders\", 1, \"menu-item\"], [\"routerLink\", \"/vendor/analytics\", 1, \"menu-item\"], [1, \"fas\", \"fa-chart-bar\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-content\"], [1, \"activity-time\"]],\n        template: function VendorDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Vendor Dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4);\n            i0.ɵɵelement(9, \"i\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 6)(11, \"h3\");\n            i0.ɵɵtext(12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"p\");\n            i0.ɵɵtext(14, \"Total Products\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 3)(16, \"div\", 4);\n            i0.ɵɵelement(17, \"i\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 6)(19, \"h3\");\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"p\");\n            i0.ɵɵtext(22, \"Total Orders\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 3)(24, \"div\", 4);\n            i0.ɵɵelement(25, \"i\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 6)(27, \"h3\");\n            i0.ɵɵtext(28);\n            i0.ɵɵpipe(29, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"p\");\n            i0.ɵɵtext(31, \"Total Revenue\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"div\", 3)(33, \"div\", 4);\n            i0.ɵɵelement(34, \"i\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 6)(36, \"h3\");\n            i0.ɵɵtext(37);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"p\");\n            i0.ɵɵtext(39, \"Posts & Stories\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(40, \"div\", 10)(41, \"h2\");\n            i0.ɵɵtext(42, \"Quick Actions\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 11)(44, \"a\", 12)(45, \"div\", 13);\n            i0.ɵɵelement(46, \"i\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 15)(48, \"h3\");\n            i0.ɵɵtext(49, \"Add Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"p\");\n            i0.ɵɵtext(51, \"Create a new product listing\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(52, \"a\", 16)(53, \"div\", 13);\n            i0.ɵɵelement(54, \"i\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"div\", 15)(56, \"h3\");\n            i0.ɵɵtext(57, \"Create Post\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"p\");\n            i0.ɵɵtext(59, \"Share a new product post\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(60, \"a\", 18)(61, \"div\", 13);\n            i0.ɵɵelement(62, \"i\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"div\", 15)(64, \"h3\");\n            i0.ɵɵtext(65, \"Add Story\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"p\");\n            i0.ɵɵtext(67, \"Create a product story\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(68, \"a\", 20)(69, \"div\", 13);\n            i0.ɵɵelement(70, \"i\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"div\", 15)(72, \"h3\");\n            i0.ɵɵtext(73, \"View Orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"p\");\n            i0.ɵɵtext(75, \"Manage your orders\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(76, \"div\", 22)(77, \"h2\");\n            i0.ɵɵtext(78, \"Recent Activity\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"div\", 23);\n            i0.ɵɵtemplate(80, VendorDashboardComponent_div_80_Template, 9, 7, \"div\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(81, \"div\", 25)(82, \"h2\");\n            i0.ɵɵtext(83, \"Vendor Tools\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"div\", 26)(85, \"a\", 27);\n            i0.ɵɵelement(86, \"i\", 5);\n            i0.ɵɵelementStart(87, \"span\");\n            i0.ɵɵtext(88, \"My Products\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(89, \"a\", 28);\n            i0.ɵɵelement(90, \"i\", 9);\n            i0.ɵɵelementStart(91, \"span\");\n            i0.ɵɵtext(92, \"My Posts\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(93, \"a\", 29);\n            i0.ɵɵelement(94, \"i\", 30);\n            i0.ɵɵelementStart(95, \"span\");\n            i0.ɵɵtext(96, \"My Stories\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(97, \"a\", 31);\n            i0.ɵɵelement(98, \"i\", 7);\n            i0.ɵɵelementStart(99, \"span\");\n            i0.ɵɵtext(100, \"Orders\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(101, \"a\", 32);\n            i0.ɵɵelement(102, \"i\", 33);\n            i0.ɵɵelementStart(103, \"span\");\n            i0.ɵɵtext(104, \"Analytics\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx.currentUser == null ? null : ctx.currentUser.fullName, \"!\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.stats.totalProducts);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate(ctx.stats.totalOrders);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(29, 6, ctx.stats.totalRevenue, \"1.0-0\"), \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate(ctx.stats.totalPosts);\n            i0.ɵɵadvance(43);\n            i0.ɵɵproperty(\"ngForOf\", ctx.recentActivity);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.DecimalPipe, i2.DatePipe, RouterModule, i3.RouterLink],\n        styles: [\".vendor-dashboard[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.dashboard-header[_ngcontent-%COMP%]{margin-bottom:30px}.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:8px}.dashboard-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:1.1rem}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:40px}.stat-card[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;padding:24px;display:flex;align-items:center;gap:16px}.stat-icon[_ngcontent-%COMP%]{width:50px;height:50px;background:#007bff;border-radius:8px;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.2rem}.stat-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:600;margin-bottom:4px}.stat-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.quick-actions[_ngcontent-%COMP%], .recent-activity[_ngcontent-%COMP%], .vendor-menu[_ngcontent-%COMP%]{margin-bottom:40px}.quick-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .recent-activity[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .vendor-menu[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.4rem;font-weight:600;margin-bottom:20px}.actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.action-card[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;padding:24px;display:flex;align-items:center;gap:16px;text-decoration:none;color:inherit;transition:all .2s}.action-card[_ngcontent-%COMP%]:hover{border-color:#007bff;transform:translateY(-2px);box-shadow:0 4px 12px #007bff26}.action-icon[_ngcontent-%COMP%]{width:50px;height:50px;background:#f8f9fa;border-radius:8px;display:flex;align-items:center;justify-content:center;color:#007bff;font-size:1.2rem}.action-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:4px}.action-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.activity-list[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;overflow:hidden}.activity-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px 20px;border-bottom:1px solid #f5f5f5}.activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.activity-icon[_ngcontent-%COMP%]{width:40px;height:40px;background:#f8f9fa;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#007bff}.activity-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:4px;font-weight:500}.activity-time[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.menu-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:16px}.menu-item[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;padding:20px;display:flex;flex-direction:column;align-items:center;gap:12px;text-decoration:none;color:inherit;transition:all .2s}.menu-item[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;color:#007bff}.menu-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}@media (max-width: 768px){.stats-grid[_ngcontent-%COMP%], .actions-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.menu-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}\"]\n      });\n    }\n  }\n  return VendorDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}