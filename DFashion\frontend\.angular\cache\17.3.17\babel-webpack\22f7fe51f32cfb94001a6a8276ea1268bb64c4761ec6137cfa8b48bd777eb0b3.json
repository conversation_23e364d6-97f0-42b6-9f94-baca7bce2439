{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nfunction MobileBottomNavComponent_nav_0_button_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r2.badge > 99 ? \"99+\" : item_r2.badge, \" \");\n  }\n}\nfunction MobileBottomNavComponent_nav_0_button_2_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 13);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.isActive(item_r2.route));\n    i0.ɵɵproperty(\"src\", ctx_r2.getUserAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName) || \"Profile\");\n  }\n}\nfunction MobileBottomNavComponent_nav_0_button_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 14);\n  }\n}\nfunction MobileBottomNavComponent_nav_0_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function MobileBottomNavComponent_nav_0_button_2_Template_button_click_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateTo(item_r2.route));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtemplate(3, MobileBottomNavComponent_nav_0_button_2_span_3_Template, 2, 1, \"span\", 8)(4, MobileBottomNavComponent_nav_0_button_2_img_4_Template, 1, 4, \"img\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, MobileBottomNavComponent_nav_0_button_2_div_7_Template, 1, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.isActive(item_r2.route));\n    i0.ɵɵattribute(\"aria-label\", item_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r2.isActive(item_r2.route) ? item_r2.activeIcon : item_r2.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.badge && item_r2.badge > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.route === \"/profile\" && ctx_r2.isAuthenticated && (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.avatar));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r2.isActive(item_r2.route));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r2.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isActive(item_r2.route));\n  }\n}\nfunction MobileBottomNavComponent_nav_0_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function MobileBottomNavComponent_nav_0_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showCreateMenu());\n    });\n    i0.ɵɵelement(1, \"i\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"expanded\", ctx_r2.showCreateOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"rotated\", ctx_r2.showCreateOptions);\n  }\n}\nfunction MobileBottomNavComponent_nav_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function MobileBottomNavComponent_nav_0_div_4_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.hideCreateMenu());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 19)(3, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function MobileBottomNavComponent_nav_0_div_4_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.createStory());\n    });\n    i0.ɵɵelement(4, \"i\", 21);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function MobileBottomNavComponent_nav_0_div_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.createPost());\n    });\n    i0.ɵɵelement(8, \"i\", 22);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Post\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function MobileBottomNavComponent_nav_0_div_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.createReel());\n    });\n    i0.ɵɵelement(12, \"i\", 23);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Reel\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function MobileBottomNavComponent_nav_0_div_4_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addProduct());\n    });\n    i0.ɵɵelement(16, \"i\", 24);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Product\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"visible\", ctx_r2.showCreateOptions);\n  }\n}\nfunction MobileBottomNavComponent_nav_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nav\", 1)(1, \"div\", 2);\n    i0.ɵɵtemplate(2, MobileBottomNavComponent_nav_0_button_2_Template, 8, 11, \"button\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MobileBottomNavComponent_nav_0_button_3_Template, 2, 4, \"button\", 4)(4, MobileBottomNavComponent_nav_0_div_4_Template, 19, 2, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getVisibleNavItems())(\"ngForTrackBy\", ctx_r2.trackByRoute);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAuthenticated);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAuthenticated);\n  }\n}\nexport let MobileBottomNavComponent = /*#__PURE__*/(() => {\n  class MobileBottomNavComponent {\n    constructor(router, authService) {\n      this.router = router;\n      this.authService = authService;\n      this.currentRoute = '';\n      this.isAuthenticated = false;\n      this.currentUser = null;\n      this.showCreateOptions = false;\n      this.navItems = [{\n        icon: 'far fa-home',\n        activeIcon: 'fas fa-home',\n        label: 'Home',\n        route: '/home'\n      }, {\n        icon: 'far fa-search',\n        activeIcon: 'fas fa-search',\n        label: 'Explore',\n        route: '/explore'\n      }, {\n        icon: 'far fa-shopping-bag',\n        activeIcon: 'fas fa-shopping-bag',\n        label: 'Shop',\n        route: '/shop'\n      }, {\n        icon: 'far fa-heart',\n        activeIcon: 'fas fa-heart',\n        label: 'Wishlist',\n        route: '/wishlist',\n        requiresAuth: true\n      }, {\n        icon: 'far fa-user-circle',\n        activeIcon: 'fas fa-user-circle',\n        label: 'Profile',\n        route: '/profile',\n        requiresAuth: true\n      }];\n    }\n    ngOnInit() {\n      // Track current route\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        if (event instanceof NavigationEnd) {\n          this.currentRoute = event.url.split('?')[0]; // Remove query params\n        }\n      });\n      // Track authentication status\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        this.isAuthenticated = !!user;\n      });\n      // Set initial route\n      this.currentRoute = this.router.url.split('?')[0];\n    }\n    navigateTo(route) {\n      this.router.navigate([route]);\n    }\n    isActive(route) {\n      if (route === '/home') {\n        return this.currentRoute === '/' || this.currentRoute === '/home';\n      }\n      return this.currentRoute.startsWith(route);\n    }\n    getVisibleNavItems() {\n      return this.navItems.filter(item => {\n        if (item.requiresAuth && !this.isAuthenticated) {\n          return false;\n        }\n        return true;\n      });\n    }\n    shouldShowBottomNav() {\n      // Hide on certain pages\n      const hiddenRoutes = ['/login', '/register', '/onboarding', '/stories/create', '/posts/create'];\n      return !hiddenRoutes.some(route => this.currentRoute.startsWith(route));\n    }\n    getUserAvatar() {\n      return this.currentUser?.avatar || 'assets/default-avatar.png';\n    }\n    // Create menu methods\n    showCreateMenu() {\n      this.showCreateOptions = true;\n    }\n    hideCreateMenu() {\n      this.showCreateOptions = false;\n    }\n    createStory() {\n      this.hideCreateMenu();\n      this.router.navigate(['/stories/create']);\n    }\n    createPost() {\n      this.hideCreateMenu();\n      this.router.navigate(['/posts/create']);\n    }\n    createReel() {\n      this.hideCreateMenu();\n      this.router.navigate(['/reels/create']);\n    }\n    addProduct() {\n      this.hideCreateMenu();\n      this.router.navigate(['/vendor/products/add']);\n    }\n    trackByRoute(index, item) {\n      return item.route;\n    }\n    static {\n      this.ɵfac = function MobileBottomNavComponent_Factory(t) {\n        return new (t || MobileBottomNavComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MobileBottomNavComponent,\n        selectors: [[\"app-mobile-bottom-nav\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 1,\n        consts: [[\"class\", \"mobile-bottom-nav\", 4, \"ngIf\"], [1, \"mobile-bottom-nav\"], [1, \"nav-container\"], [\"class\", \"nav-item\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"fab-create\", \"aria-label\", \"Create content\", 3, \"expanded\", \"click\", 4, \"ngIf\"], [\"class\", \"create-menu\", 3, \"visible\", 4, \"ngIf\"], [1, \"nav-item\", 3, \"click\"], [1, \"nav-icon\"], [\"class\", \"nav-badge\", 4, \"ngIf\"], [\"class\", \"profile-avatar\", 3, \"src\", \"alt\", \"active\", 4, \"ngIf\"], [1, \"nav-label\"], [\"class\", \"active-indicator\", 4, \"ngIf\"], [1, \"nav-badge\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [1, \"active-indicator\"], [\"aria-label\", \"Create content\", 1, \"fab-create\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"create-menu\"], [1, \"create-backdrop\", 3, \"click\"], [1, \"create-options\"], [1, \"create-option\", 3, \"click\"], [1, \"fas\", \"fa-camera\"], [1, \"fas\", \"fa-image\"], [1, \"fas\", \"fa-video\"], [1, \"fas\", \"fa-tag\"]],\n        template: function MobileBottomNavComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, MobileBottomNavComponent_nav_0_Template, 5, 4, \"nav\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.shouldShowBottomNav());\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n        styles: [\".mobile-bottom-nav[_ngcontent-%COMP%]{position:fixed;bottom:0;left:0;right:0;z-index:1000;background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-top:1px solid rgba(0,0,0,.1);padding:8px 0 calc(8px + env(safe-area-inset-bottom));box-shadow:0 -4px 20px #0000001a}@media (min-width: 769px){.mobile-bottom-nav[_ngcontent-%COMP%]{display:none}}.nav-container[_ngcontent-%COMP%]{display:flex;justify-content:space-around;align-items:center;max-width:100%;margin:0 auto;padding:0 16px}.nav-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;padding:8px 12px;border-radius:12px;transition:all .3s ease;position:relative;min-width:60px}.nav-item[_ngcontent-%COMP%]:active{transform:scale(.95)}.nav-item.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#667eea;transform:scale(1.1)}.nav-item.active[_ngcontent-%COMP%]   .nav-label[_ngcontent-%COMP%]{color:#667eea;font-weight:600}.nav-item[_ngcontent-%COMP%]:not(.active):hover{background:#667eea1a}.nav-icon[_ngcontent-%COMP%]{position:relative;margin-bottom:4px;display:flex;align-items:center;justify-content:center;width:24px;height:24px}.nav-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;color:#8e8e8e;transition:all .3s ease}.nav-icon[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:linear-gradient(135deg,#f093fb,#f5576c);color:#fff;border-radius:10px;padding:2px 6px;font-size:10px;font-weight:600;min-width:16px;height:16px;display:flex;align-items:center;justify-content:center;border:2px solid white;box-shadow:0 2px 8px #f5576c66;animation:_ngcontent-%COMP%_pulse 2s infinite}.nav-icon[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;object-fit:cover;border:2px solid transparent;transition:all .3s ease}.nav-icon[_ngcontent-%COMP%]   .profile-avatar.active[_ngcontent-%COMP%]{border-color:#667eea;transform:scale(1.1)}.nav-label[_ngcontent-%COMP%]{font-size:10px;color:#8e8e8e;font-weight:500;transition:all .3s ease;text-align:center;line-height:1}.nav-label.active[_ngcontent-%COMP%]{color:#667eea;font-weight:600}.active-indicator[_ngcontent-%COMP%]{position:absolute;bottom:-2px;left:50%;transform:translate(-50%);width:4px;height:4px;background:#667eea;border-radius:50%;animation:_ngcontent-%COMP%_fadeIn .3s ease}.fab-create[_ngcontent-%COMP%]{position:absolute;bottom:60px;right:20px;width:56px;height:56px;background:linear-gradient(135deg,#667eea,#764ba2);border:none;border-radius:50%;color:#fff;font-size:24px;cursor:pointer;box-shadow:0 8px 25px #667eea66;transition:all .3s ease;z-index:1001}.fab-create[_ngcontent-%COMP%]:hover{transform:scale(1.1);box-shadow:0 12px 35px #667eea99}.fab-create[_ngcontent-%COMP%]:active{transform:scale(.95)}.fab-create.expanded[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c);transform:rotate(45deg)}.fab-create[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:transform .3s ease}.fab-create[_ngcontent-%COMP%]   i.rotated[_ngcontent-%COMP%]{transform:rotate(45deg)}.create-menu[_ngcontent-%COMP%]{position:fixed;inset:0;z-index:999;opacity:0;visibility:hidden;transition:all .3s ease}.create-menu.visible[_ngcontent-%COMP%]{opacity:1;visibility:visible}.create-menu.visible[_ngcontent-%COMP%]   .create-options[_ngcontent-%COMP%]{transform:translateY(0)}.create-backdrop[_ngcontent-%COMP%]{position:absolute;inset:0;background:#00000080;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)}.create-options[_ngcontent-%COMP%]{position:absolute;bottom:130px;right:20px;display:flex;flex-direction:column;gap:12px;transform:translateY(20px);transition:transform .3s ease}.create-option[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;background:#fff;border:none;border-radius:25px;padding:12px 20px;cursor:pointer;box-shadow:0 4px 20px #00000026;transition:all .3s ease;min-width:140px}.create-option[_ngcontent-%COMP%]:hover{transform:translate(-8px);box-shadow:0 8px 30px #0003}.create-option[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:20px;height:20px;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border-radius:50%;font-size:10px}.create-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#2d3748}.create-option[_ngcontent-%COMP%]:nth-child(1)   i[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.create-option[_ngcontent-%COMP%]:nth-child(2)   i[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.create-option[_ngcontent-%COMP%]:nth-child(3)   i[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe)}.create-option[_ngcontent-%COMP%]:nth-child(4)   i[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.1)}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translate(-50%) scale(0)}to{opacity:1;transform:translate(-50%) scale(1)}}@supports (padding-bottom: env(safe-area-inset-bottom)){.mobile-bottom-nav[_ngcontent-%COMP%]{padding-bottom:calc(8px + env(safe-area-inset-bottom))}}@media (prefers-color-scheme: dark){.mobile-bottom-nav[_ngcontent-%COMP%]{background:#1a202cf2;border-top-color:#ffffff1a}.mobile-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:not(.active):hover{background:#667eea33}.mobile-bottom-nav[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .mobile-bottom-nav[_ngcontent-%COMP%]   .nav-label[_ngcontent-%COMP%]{color:#a0aec0}.mobile-bottom-nav[_ngcontent-%COMP%]   .create-option[_ngcontent-%COMP%]{background:#2d3748;color:#fff}.mobile-bottom-nav[_ngcontent-%COMP%]   .create-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#fff}}@media (prefers-reduced-motion: reduce){.nav-item[_ngcontent-%COMP%], .nav-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .nav-label[_ngcontent-%COMP%], .fab-create[_ngcontent-%COMP%], .create-option[_ngcontent-%COMP%]{transition:none}.nav-badge[_ngcontent-%COMP%]{animation:none}}@media (prefers-contrast: high){.mobile-bottom-nav[_ngcontent-%COMP%]{border-top:2px solid #000}.nav-item.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .nav-item.active[_ngcontent-%COMP%]   .nav-label[_ngcontent-%COMP%]{color:#000}}\"]\n      });\n    }\n  }\n  return MobileBottomNavComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}