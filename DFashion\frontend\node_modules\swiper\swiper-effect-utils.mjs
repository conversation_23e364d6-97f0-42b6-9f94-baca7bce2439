/**
 * Swiper 11.2.8
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: May 23, 2025
 */

export { c as createShadow } from './shared/create-shadow.mjs';
export { e as effectInit } from './shared/effect-init.mjs';
export { e as effectTarget } from './shared/effect-target.mjs';
export { e as effectVirtualTransitionEnd } from './shared/effect-virtual-transition-end.mjs';
export { g as getSlideTransformEl } from './shared/utils.mjs';
