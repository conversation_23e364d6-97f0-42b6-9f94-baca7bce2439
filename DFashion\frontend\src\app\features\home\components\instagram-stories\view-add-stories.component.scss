.stories-container {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;

  @media (max-width: 768px) {
    margin: 8px;
    padding: 12px;
    border-radius: 12px;
  }
}

.stories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;

  .stories-title {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    flex: 1;
    min-width: 0; // Allow text to shrink
  }

  .create-story-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
    min-height: 36px;
    flex-shrink: 0; // Prevent button from shrinking

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
    }

    &:focus {
      outline: 2px solid rgba(255, 255, 255, 0.5);
      outline-offset: 2px;
    }

    i {
      font-size: 12px;
    }

    @media (max-width: 480px) {
      padding: 6px 12px;
      font-size: 12px;
      min-height: 32px;

      i {
        font-size: 10px;
      }
    }
  }

  @media (max-width: 768px) {
    margin-bottom: 12px;

    .stories-title {
      font-size: 18px;
    }
  }

  @media (max-width: 480px) {
    .stories-title {
      font-size: 16px;
    }
  }
}

.stories-slider-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 120px; /* Ensure enough height for stories and buttons */
  padding: 8px 0; /* Add vertical padding */
}

/* Swiper Carousel Styling */
.stories-swiper {
  width: 100%;
  padding: 0 60px; /* Increased padding to prevent overlap */
  position: relative;

  ::ng-deep {
    .swiper-wrapper {
      align-items: center;
    }

    .swiper-slide {
      display: flex;
      justify-content: center;
      align-items: center;
      height: auto;
    }

    .swiper-button-next,
    .swiper-button-prev {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
      border: 2px solid rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      width: 32px !important;
      height: 32px !important;
      margin-top: 0;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.06);
      z-index: 15;
      color: #4f46e5;
      backdrop-filter: blur(8px);

      &:after {
        font-size: 12px;
        font-weight: 700;
        color: #4f46e5;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      }

      &:hover {
        background: linear-gradient(135deg, #ffffff, #f8fafc);
        transform: scale(1.15) translateY(-1px);
        box-shadow:
          0 8px 25px rgba(79, 70, 229, 0.15),
          0 4px 10px rgba(0, 0, 0, 0.1);
        border-color: rgba(79, 70, 229, 0.3);

        &:after {
          color: #3730a3;
        }
      }

      &:active {
        transform: scale(1.05) translateY(0);
        transition-duration: 0.1s;
      }

      &.swiper-button-disabled {
        opacity: 0.4;
        cursor: not-allowed;
        pointer-events: none;
        transform: none;

        &:hover {
          transform: none;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .swiper-button-prev {
      left: 10px; /* Moved closer to prevent overlap */

      &:after {
        content: '❮';
        font-size: 10px;
        margin-left: -1px;
      }
    }

    .swiper-button-next {
      right: 10px; /* Moved closer to prevent overlap */

      &:after {
        content: '❯';
        font-size: 10px;
        margin-right: -1px;
      }
    }

    @media (max-width: 768px) {
      .swiper-button-next,
      .swiper-button-prev {
        display: none;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 0 16px;
  }

  @media (max-width: 480px) {
    padding: 0 8px;
  }
}

.nav-arrow {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;

  &:hover:not(:disabled) {
    background: white;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.hidden {
    display: none;
  }

  i {
    color: #333;
    font-size: 14px;
  }

  @media (max-width: 768px) {
    display: none;
  }
}

.stories-slider {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.stories-track {
  display: flex;
  transition: transform 0.3s ease;
  will-change: transform;
}

.story-item {
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease;
  position: relative;
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: translateY(-4px);
  }
}

.story-avatar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.story-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;

  &.has-products {
    background: linear-gradient(45deg, #ff6b35, #f7931e, #ff6b35, #f7931e);
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
    border: 3px solid transparent;
    background-clip: padding-box;

    &:hover {
      animation-duration: 1.5s;
      transform: scale(1.05);
    }
  }

  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.story-avatar-inner {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;

  @media (max-width: 768px) {
    width: 54px;
    height: 54px;
  }
}

.story-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

/* Instagram-style shopping bag indicator positioned outside the circle */
.shopping-bag-indicator {
  position: absolute;
  bottom: -4px;
  right: -4px;
  background: linear-gradient(135deg, #ff6b35, #e55a2b); /* Orange gradient for shopping/commerce */
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow:
    0 3px 12px rgba(255, 107, 53, 0.3),
    0 1px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 3;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.15) translateY(-1px);
    background: linear-gradient(135deg, #ff7849, #ff6b35);
    box-shadow:
      0 6px 20px rgba(255, 107, 53, 0.4),
      0 3px 8px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.9);
  }

  &:active {
    transform: scale(1.05) translateY(0);
    transition-duration: 0.1s;
  }

  i {
    color: white;
    font-size: 11px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  @media (max-width: 768px) {
    width: 22px;
    height: 22px;
    bottom: -3px;
    right: -3px;
    border-width: 2px;

    i {
      font-size: 10px;
    }
  }

  @media (max-width: 480px) {
    width: 18px;
    height: 18px;
    bottom: -2px;
    right: -2px;
    border-width: 1.5px;

    i {
      font-size: 8px;
    }
  }
}

.story-username {
  margin-top: 8px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    font-size: 11px;
    max-width: 70px;
  }
}

/* Special styling for "Your Story" text */
.add-story-item .story-username {
  color: #0095f6;
  font-weight: 600;
  text-shadow: none;
}

.product-count-badge {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    font-size: 9px;
    padding: 1px 4px;
  }
}

.story-count-badge {
  font-size: 10px;
  color: #8e8e8e;
  margin-top: 2px;
  text-align: center;
  font-weight: 500;
}

/* Add Story Button */
.add-avatar {
  background: linear-gradient(135deg, #f9f9f9, #e8e8e8);
  border: 2px dashed #c7c7c7;
  position: relative;

  &:hover {
    border-color: #0095f6;
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  }

  .story-avatar-img {
    opacity: 0.8;
    filter: grayscale(20%);
  }
}

/* Instagram-style plus button positioned outside the circle */
.add-story-plus-btn {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #0095f6, #0077cc); /* Instagram blue gradient */
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 3;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 3px 12px rgba(0, 149, 246, 0.3),
    0 1px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: scale(1.15) translateY(-1px);
    background: linear-gradient(135deg, #1da1f2, #0095f6);
    box-shadow:
      0 6px 20px rgba(0, 149, 246, 0.4),
      0 3px 8px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.9);
  }

  &:active {
    transform: scale(1.05) translateY(0);
    transition-duration: 0.1s;
  }

  i {
    color: white;
    font-size: 11px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  @media (max-width: 768px) {
    width: 22px;
    height: 22px;
    bottom: -3px;
    right: -3px;
    border-width: 2px;

    i {
      font-size: 10px;
    }
  }

  @media (max-width: 480px) {
    width: 18px;
    height: 18px;
    bottom: -2px;
    right: -2px;
    border-width: 1.5px;

    i {
      font-size: 8px;
    }
  }
}

/* Fullscreen overlay */
.stories-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95));
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;

  @media (max-width: 768px) {
    background: #000;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.stories-content {
  position: relative;
  width: 90%;
  max-width: 400px;
  height: 80vh;
  max-height: 700px;
  background: #000;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s ease;

  @media (max-width: 768px) {
    width: 100%;
    height: 100vh;
    max-height: none;
    border-radius: 0;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.progress-container {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  z-index: 10;
}

.progress-bar {
  flex: 1;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;

  &.active .progress-fill {
    animation: progressFill 5s linear;
  }

  &.completed .progress-fill {
    width: 100% !important;
  }
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 2px;
  transition: width 0.1s ease;
}

@keyframes progressFill {
  from { width: 0%; }
  to { width: 100%; }
}

.story-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 16px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  color: #fff;
  z-index: 10;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.story-header-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
}

.story-header-info {
  flex: 1;
}

.username {
  display: block;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 2px;

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

.time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  i {
    font-size: 16px;
  }

  @media (max-width: 768px) {
    width: 32px;
    height: 32px;

    i {
      font-size: 14px;
    }
  }
}

.story-media {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.story-middle-click-area {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.middle-click-indicator {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: pulseIndicator 2s infinite;

  i {
    font-size: 12px;
  }

  span {
    white-space: nowrap;
  }

  @media (max-width: 768px) {
    padding: 10px 16px;
    font-size: 12px;

    i {
      font-size: 10px;
    }
  }
}

@keyframes pulseIndicator {
  0% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.2);
  }
  100% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

.story-image, .story-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.product-tags {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.product-tag {
  position: absolute;
  pointer-events: all;
  cursor: pointer;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.product-tag-dot {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  border: 3px solid #667eea;
  position: relative;
  animation: ripple 2s infinite;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
  }
}

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

.product-tag-info {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.product-tag-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: cover;
}

.product-tag-details {
  flex: 1;
  color: white;
}

.product-tag-name {
  display: block;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.2;
}

.product-tag-price {
  display: block;
  color: #667eea;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 8px;
}

.product-tag-actions {
  display: flex;
  gap: 6px;
  justify-content: flex-start;
}

.product-action-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }

  &.cart-btn {
    &:hover {
      background: #667eea;
      color: white;
    }
  }

  &.wishlist-btn {
    &:hover {
      background: #f5576c;
      color: white;
    }
  }

  &.buy-btn {
    &:hover {
      background: #28a745;
      color: white;
    }
  }

  i {
    font-size: 12px;
  }

  @media (max-width: 768px) {
    width: 28px;
    height: 28px;

    i {
      font-size: 10px;
    }
  }
}

.shopping-bag-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  z-index: 5;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
  }

  &.active {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    animation: bounce 0.6s ease;
  }

  i {
    color: white;
    font-size: 20px;
  }

  .product-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #f5576c;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    border: 2px solid white;
    min-width: 24px; // Ensure minimum width for larger numbers
  }

  @media (max-width: 768px) {
    width: 48px;
    height: 48px;
    bottom: 16px;
    right: 16px;

    i {
      font-size: 18px;
    }

    .product-count {
      width: 20px;
      height: 20px;
      font-size: 10px;
      min-width: 20px;
      top: -6px;
      right: -6px;
    }
  }

  @media (max-width: 480px) {
    width: 44px;
    height: 44px;
    bottom: 12px;
    right: 12px;

    i {
      font-size: 16px;
    }

    .product-count {
      width: 18px;
      height: 18px;
      font-size: 9px;
      min-width: 18px;
      top: -5px;
      right: -5px;
    }
  }

  // Ensure button doesn't overlap with other UI elements
  @media (max-height: 600px) {
    bottom: 10px;
    right: 10px;
  }
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  80% { transform: translateY(-5px); }
}

.story-actions {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 5;

  @media (max-width: 768px) {
    bottom: 16px;
    left: 16px;
    gap: 12px;
  }

  @media (max-width: 480px) {
    bottom: 12px;
    left: 12px;
    gap: 10px;
  }

  // Ensure actions don't overlap with shopping bag button
  @media (max-width: 768px) and (max-height: 600px) {
    bottom: 10px;
    left: 10px;
    gap: 8px;
  }
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.6);
    outline-offset: 2px;
  }

  &.liked {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
    animation: heartBeat 0.6s ease;
  }

  i {
    font-size: 18px;
  }

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;

    i {
      font-size: 16px;
    }
  }

  @media (max-width: 480px) {
    width: 36px;
    height: 36px;

    i {
      font-size: 14px;
    }
  }

  // Improve touch targets for mobile
  @media (max-width: 768px) {
    min-width: 44px;
    min-height: 44px;
  }
}

@keyframes heartBeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.story-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  z-index: 5;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
  }

  &.story-nav-prev {
    left: 20px;
  }

  &.story-nav-next {
    right: 20px;
  }

  i {
    font-size: 20px;
  }

  @media (max-width: 768px) {
    display: none;
  }
}

// Enhanced Responsive adjustments
@media (max-width: 768px) {
  .stories-container {
    .stories-header .stories-title {
      font-size: 18px;
    }

    .stories-header .create-story-btn {
      padding: 6px 12px;
      font-size: 12px;
    }
  }

  .product-tag-info {
    min-width: 160px;
    padding: 8px;

    .product-tag-image {
      width: 40px;
      height: 40px;
    }

    .product-tag-name {
      font-size: 12px;
    }

    .product-tag-price {
      font-size: 14px;
    }
  }

  // Prevent UI overlap on mobile
  .stories-content {
    .shopping-bag-btn {
      // Ensure shopping bag doesn't overlap with story actions
      &:not(:only-child) {
        right: 16px;
        bottom: 80px; // Move up when story actions are present
      }
    }
  }
}

// Extra small devices
@media (max-width: 480px) {
  .stories-container {
    margin: 4px;
    padding: 8px;
    border-radius: 8px;
  }

  .product-tag-info {
    min-width: 140px;
    padding: 6px;
    bottom: 20px;

    .product-tag-image {
      width: 35px;
      height: 35px;
    }

    .product-tag-name {
      font-size: 11px;
    }

    .product-tag-price {
      font-size: 13px;
    }
  }
}

// Landscape orientation adjustments
@media (max-width: 768px) and (orientation: landscape) {
  .stories-content {
    height: 100vh;
    max-height: none;
  }

  .shopping-bag-btn {
    bottom: 10px;
    right: 10px;
  }

  .story-actions {
    bottom: 10px;
    left: 10px;
    gap: 8px;
  }
}

// Loading states
.story-item.loading {
  .story-avatar {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// Accessibility improvements
.story-item:focus,
.action-btn:focus,
.shopping-bag-btn:focus,
.nav-arrow:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .story-avatar {
    border: 2px solid #000;
  }

  .shopping-bag-btn {
    border: 2px solid #fff;
  }
}
