{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction ShopComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading shop data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopComponent_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_10_Template_div_click_0_listener() {\n      const category_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToCategory(category_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r4.name);\n  }\n}\nfunction ShopComponent_div_2_div_15_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1, \"Popular\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"img\", 26);\n    i0.ɵɵelementStart(2, \"h3\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ShopComponent_div_2_div_15_span_4_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", brand_r5.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r5.isPopular);\n  }\n}\nfunction ShopComponent_div_2_div_20_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r7), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_2_div_20_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.likesCount);\n  }\n}\nfunction ShopComponent_div_2_div_20_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.sharesCount);\n  }\n}\nfunction ShopComponent_div_2_div_20_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.commentsCount);\n  }\n}\nfunction ShopComponent_div_2_div_20_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_20_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵtemplate(3, ShopComponent_div_2_div_20_div_3_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementStart(4, \"div\", 34)(5, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_20_Template_button_click_5_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.likeProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(6, \"i\", 36);\n    i0.ɵɵtemplate(7, ShopComponent_div_2_div_20_span_7_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_20_Template_button_click_8_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 39);\n    i0.ɵɵtemplate(10, ShopComponent_div_2_div_20_span_10_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_20_Template_button_click_11_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.commentOnProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(12, \"i\", 41);\n    i0.ɵɵtemplate(13, ShopComponent_div_2_div_20_span_13_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 42)(15, \"h3\", 43);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 44);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 45)(20, \"span\", 46);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ShopComponent_div_2_div_20_span_22_Template, 2, 1, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 48)(24, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_20_Template_button_click_24_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r7, $event));\n    });\n    i0.ɵɵelement(25, \"i\", 36);\n    i0.ɵɵtext(26, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_20_Template_button_click_27_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r7, $event));\n    });\n    i0.ɵɵelement(28, \"i\", 51);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r7), i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r7) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", product_r7.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r7.likesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r7.sharesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r7.commentsCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice && product_r7.originalPrice > product_r7.price);\n  }\n}\nfunction ShopComponent_div_2_div_25_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.likesCount);\n  }\n}\nfunction ShopComponent_div_2_div_25_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.sharesCount);\n  }\n}\nfunction ShopComponent_div_2_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.commentsCount);\n  }\n}\nfunction ShopComponent_div_2_div_25_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r9.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_25_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵelementStart(3, \"div\", 54);\n    i0.ɵɵtext(4, \"NEW\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 34)(6, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_25_Template_button_click_6_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.likeProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(7, \"i\", 36);\n    i0.ɵɵtemplate(8, ShopComponent_div_2_div_25_span_8_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_25_Template_button_click_9_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(10, \"i\", 39);\n    i0.ɵɵtemplate(11, ShopComponent_div_2_div_25_span_11_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_25_Template_button_click_12_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.commentOnProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(13, \"i\", 41);\n    i0.ɵɵtemplate(14, ShopComponent_div_2_div_25_span_14_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 42)(16, \"h3\", 43);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 44);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 45)(21, \"span\", 46);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ShopComponent_div_2_div_25_span_23_Template, 2, 1, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 48)(25, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_25_Template_button_click_25_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r9, $event));\n    });\n    i0.ɵɵelement(26, \"i\", 36);\n    i0.ɵɵtext(27, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_25_Template_button_click_28_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r9, $event));\n    });\n    i0.ɵɵelement(29, \"i\", 51);\n    i0.ɵɵtext(30, \" Add to Cart \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r9), i0.ɵɵsanitizeUrl)(\"alt\", product_r9.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"liked\", product_r9.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r9.likesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r9.sharesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r9.commentsCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r9.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r9.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r9.originalPrice && product_r9.originalPrice > product_r9.price);\n  }\n}\nfunction ShopComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"input\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ShopComponent_div_2_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function ShopComponent_div_2_Template_input_keyup_enter_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelement(5, \"i\", 10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"section\", 11)(7, \"h2\", 12);\n    i0.ɵɵtext(8, \"Shop by Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13);\n    i0.ɵɵtemplate(10, ShopComponent_div_2_div_10_Template, 5, 2, \"div\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"section\", 15)(12, \"h2\", 12);\n    i0.ɵɵtext(13, \"Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 16);\n    i0.ɵɵtemplate(15, ShopComponent_div_2_div_15_Template, 5, 4, \"div\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"section\", 18)(17, \"h2\", 12);\n    i0.ɵɵtext(18, \"Trending Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 19);\n    i0.ɵɵtemplate(20, ShopComponent_div_2_div_20_Template, 30, 12, \"div\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"section\", 21)(22, \"h2\", 12);\n    i0.ɵɵtext(23, \"New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 19);\n    i0.ɵɵtemplate(25, ShopComponent_div_2_div_25_Template, 31, 11, \"div\", 20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals);\n  }\n}\nexport let ShopComponent = /*#__PURE__*/(() => {\n  class ShopComponent {\n    constructor(productService, authService, cartService, wishlistService, router) {\n      this.productService = productService;\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.router = router;\n      this.featuredBrands = [];\n      this.trendingProducts = [];\n      this.newArrivals = [];\n      this.categories = [];\n      this.searchQuery = '';\n      this.loading = true;\n    }\n    ngOnInit() {\n      this.loadShopData();\n    }\n    loadShopData() {\n      this.loading = true;\n      Promise.all([this.loadFeaturedBrands(), this.loadTrendingProducts(), this.loadNewArrivals(), this.loadCategories()]).finally(() => {\n        this.loading = false;\n      });\n    }\n    loadFeaturedBrands() {\n      return this.productService.getFeaturedBrands().toPromise().then(response => {\n        this.featuredBrands = response?.data || [];\n      }).catch(error => {\n        console.error('Error loading featured brands:', error);\n        this.featuredBrands = [];\n      });\n    }\n    loadTrendingProducts() {\n      return this.productService.getTrendingProducts().toPromise().then(response => {\n        this.trendingProducts = response?.data || [];\n      }).catch(error => {\n        console.error('Error loading trending products:', error);\n        this.trendingProducts = [];\n      });\n    }\n    loadNewArrivals() {\n      return this.productService.getNewArrivals().toPromise().then(response => {\n        this.newArrivals = response?.data || [];\n      }).catch(error => {\n        console.error('Error loading new arrivals:', error);\n        this.newArrivals = [];\n      });\n    }\n    loadCategories() {\n      return this.productService.getCategories().toPromise().then(response => {\n        this.categories = response?.data || [];\n      }).catch(error => {\n        console.error('Error loading categories:', error);\n        this.categories = [];\n      });\n    }\n    // Product interaction methods\n    likeProduct(product, event) {\n      event.stopPropagation();\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      product.isLiked = !product.isLiked;\n      product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n      this.productService.toggleProductLike(product._id).subscribe({\n        next: response => {\n          console.log('Product like updated:', response);\n        },\n        error: error => {\n          console.error('Error updating product like:', error);\n          product.isLiked = !product.isLiked;\n          product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n        }\n      });\n    }\n    shareProduct(product, event) {\n      event.stopPropagation();\n      const shareData = {\n        title: product.name,\n        text: `Check out this amazing product: ${product.name}`,\n        url: `${window.location.origin}/product/${product._id}`\n      };\n      if (navigator.share) {\n        navigator.share(shareData);\n      } else {\n        navigator.clipboard.writeText(shareData.url).then(() => {\n          alert('Product link copied to clipboard!');\n        });\n      }\n      this.productService.shareProduct(product._id).subscribe({\n        next: response => {\n          product.sharesCount = (product.sharesCount || 0) + 1;\n        },\n        error: error => {\n          console.error('Error tracking product share:', error);\n        }\n      });\n    }\n    commentOnProduct(product, event) {\n      event.stopPropagation();\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      this.router.navigate(['/product', product._id], {\n        queryParams: {\n          action: 'comment'\n        }\n      });\n    }\n    addToWishlist(product, event) {\n      event.stopPropagation();\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          product.isInWishlist = true;\n          console.log('Product added to wishlist:', response);\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n        }\n      });\n    }\n    addToCart(product, event) {\n      event.stopPropagation();\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      this.cartService.addToCart(product._id, 1).subscribe({\n        next: response => {\n          console.log('Product added to cart:', response);\n          alert('Product added to cart successfully!');\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n        }\n      });\n    }\n    // Navigation methods\n    viewProduct(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    navigateToCategory(category) {\n      this.router.navigate(['/category', category.slug]);\n    }\n    search() {\n      if (this.searchQuery.trim()) {\n        this.router.navigate(['/search'], {\n          queryParams: {\n            q: this.searchQuery\n          }\n        });\n      }\n    }\n    getProductImage(product) {\n      return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n    }\n    getDiscountPercentage(product) {\n      if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    static {\n      this.ɵfac = function ShopComponent_Factory(t) {\n        return new (t || ShopComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ShopComponent,\n        selectors: [[\"app-shop\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 2,\n        consts: [[1, \"shop-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"shop-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"shop-content\"], [1, \"search-section\"], [1, \"search-bar\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, categories...\", 1, \"search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"category-section\"], [1, \"section-title\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-brands-section\"], [1, \"brands-slider\"], [\"class\", \"brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"trending-section\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"new-arrivals-section\"], [1, \"category-card\", 3, \"click\"], [1, \"category-icon\"], [1, \"category-name\"], [1, \"brand-card\"], [1, \"brand-logo\", 3, \"src\", \"alt\"], [1, \"brand-name\"], [\"class\", \"popular-badge\", 4, \"ngIf\"], [1, \"popular-badge\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [1, \"product-image\", 3, \"src\", \"alt\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [4, \"ngIf\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-buttons\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"new-badge\"]],\n        template: function ShopComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, ShopComponent_div_1_Template, 4, 0, \"div\", 1)(2, ShopComponent_div_2_Template, 26, 5, \"div\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n        styles: [\".shop-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px}.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:20px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.search-section[_ngcontent-%COMP%]{margin-bottom:40px}.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{display:flex;max-width:600px;margin:0 auto;border:2px solid #e0e0e0;border-radius:25px;overflow:hidden}.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{flex:1;padding:12px 20px;border:none;outline:none;font-size:16px}.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]{padding:12px 20px;background:#007bff;color:#fff;border:none;cursor:pointer}.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover{background:#0056b3}.section-title[_ngcontent-%COMP%]{font-size:28px;font-weight:600;margin-bottom:30px;text-align:center;color:#333}.category-section[_ngcontent-%COMP%]{margin-bottom:60px}.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:20px}.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{background:#fff;border-radius:15px;padding:30px 20px;text-align:center;box-shadow:0 4px 15px #0000001a;cursor:pointer;transition:all .3s ease}.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 25px #00000026}.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:15px}.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333;margin:0}.featured-brands-section[_ngcontent-%COMP%]{margin-bottom:60px}.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]{display:flex;gap:20px;overflow-x:auto;padding:20px 0}.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{min-width:200px;background:#fff;border-radius:15px;padding:20px;text-align:center;box-shadow:0 4px 15px #0000001a;position:relative}.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;margin-bottom:15px}.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#333;margin:0}.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .popular-badge[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;background:#ff6b6b;color:#fff;padding:4px 8px;border-radius:12px;font-size:12px;font-weight:600}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:30px}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:15px;overflow:hidden;box-shadow:0 4px 15px #0000001a;cursor:pointer;transition:all .3s ease}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 25px #00000026}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]{position:relative}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:250px;object-fit:cover}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%], .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]{position:absolute;top:10px;left:10px;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;color:#fff}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{background:#ff6b6b}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]{background:#4ecdc4}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;display:flex;flex-direction:column;gap:8px}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:none;background:#ffffffe6;color:#666;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:14px;transition:all .3s ease}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{background:#fff;color:#333}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.liked[_ngcontent-%COMP%]{color:#ff6b6b}.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:10px;margin-left:2px}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{padding:20px}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333;margin:0 0 8px;line-height:1.3}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{color:#666;font-size:14px;margin:0 0 12px}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]{margin-bottom:15px}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#333}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:16px;color:#999;text-decoration:line-through;margin-left:8px}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]{display:flex;gap:10px}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{flex:1;padding:10px;border:none;border-radius:8px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s ease}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:5px}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-wishlist[_ngcontent-%COMP%]{background:#f8f9fa;color:#666}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-wishlist[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#333}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-cart[_ngcontent-%COMP%]{background:#007bff;color:#fff}.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-cart[_ngcontent-%COMP%]:hover{background:#0056b3}@media (max-width: 768px){.shop-container[_ngcontent-%COMP%]{padding:15px}.section-title[_ngcontent-%COMP%]{font-size:24px}.categories-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:15px}.products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:20px}.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{min-width:150px}}\"]\n      });\n    }\n  }\n  return ShopComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}