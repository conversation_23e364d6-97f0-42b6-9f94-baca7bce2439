{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/sidenav\";\nimport * as i5 from \"@angular/material/divider\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/menu\";\nconst _c0 = [\"*\"];\nfunction SidebarComponent_div_10_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.badge);\n  }\n}\nfunction SidebarComponent_div_10_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SidebarComponent_div_10_a_1_span_5_Template, 2, 1, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r2.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.badge);\n  }\n}\nfunction SidebarComponent_div_10_div_2_a_9_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(child_r5.badge);\n  }\n}\nfunction SidebarComponent_div_10_div_2_a_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 26)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SidebarComponent_div_10_div_2_a_9_span_5_Template, 2, 1, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", child_r5.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(child_r5.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(child_r5.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", child_r5.badge);\n  }\n}\nfunction SidebarComponent_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_10_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const item_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleExpanded(item_r2.label));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-icon\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 24);\n    i0.ɵɵtemplate(9, SidebarComponent_div_10_div_2_a_9_Template, 6, 4, \"a\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r3.isExpanded(item_r2.label));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isExpanded(item_r2.label) ? \"expand_less\" : \"expand_more\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r3.isExpanded(item_r2.label));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", item_r2.children);\n  }\n}\nfunction SidebarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, SidebarComponent_div_10_a_1_Template, 6, 4, \"a\", 16)(2, SidebarComponent_div_10_div_2_Template, 10, 8, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r2.children);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.children);\n  }\n}\nfunction SidebarComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"div\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r3.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 2, ctx_r3.currentUser.role));\n  }\n}\nexport class SidebarComponent {\n  constructor(router, adminAuthService) {\n    this.router = router;\n    this.adminAuthService = adminAuthService;\n    this.sidenavToggle = new EventEmitter();\n    this.currentUser = null;\n    this.expandedItems = new Set();\n    this.currentRoute = '';\n    this.menuItems = [{\n      label: 'Dashboard',\n      icon: 'dashboard',\n      route: '/admin/dashboard'\n    }, {\n      label: 'Users',\n      icon: 'people',\n      route: '/admin/users',\n      children: [{\n        label: 'All Users',\n        icon: 'list',\n        route: '/admin/users'\n      }, {\n        label: 'Add User',\n        icon: 'person_add',\n        route: '/admin/users/new'\n      }, {\n        label: 'Roles & Permissions',\n        icon: 'security',\n        route: '/admin/users/roles'\n      }]\n    }, {\n      label: 'Products',\n      icon: 'inventory_2',\n      route: '/admin/products',\n      children: [{\n        label: 'All Products',\n        icon: 'list',\n        route: '/admin/products'\n      }, {\n        label: 'Add Product',\n        icon: 'add_box',\n        route: '/admin/products/new'\n      }, {\n        label: 'Categories',\n        icon: 'category',\n        route: '/admin/products/categories'\n      }, {\n        label: 'Inventory',\n        icon: 'warehouse',\n        route: '/admin/products/inventory'\n      }]\n    }, {\n      label: 'Orders',\n      icon: 'shopping_cart',\n      route: '/admin/orders',\n      badge: '12'\n    }, {\n      label: 'Analytics',\n      icon: 'analytics',\n      route: '/admin/analytics'\n    }, {\n      label: 'Marketing',\n      icon: 'campaign',\n      route: '/admin/marketing',\n      children: [{\n        label: 'Campaigns',\n        icon: 'email',\n        route: '/admin/marketing/campaigns'\n      }, {\n        label: 'Coupons',\n        icon: 'local_offer',\n        route: '/admin/marketing/coupons'\n      }, {\n        label: 'Reviews',\n        icon: 'rate_review',\n        route: '/admin/marketing/reviews'\n      }]\n    }, {\n      label: 'Settings',\n      icon: 'settings',\n      route: '/admin/settings'\n    }];\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.adminAuthService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    // Track current route\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        this.currentRoute = event.url;\n        this.autoExpandForCurrentRoute();\n      }\n    });\n    // Set initial route\n    this.currentRoute = this.router.url;\n    this.autoExpandForCurrentRoute();\n  }\n  toggleExpanded(itemLabel) {\n    if (this.expandedItems.has(itemLabel)) {\n      this.expandedItems.delete(itemLabel);\n    } else {\n      this.expandedItems.add(itemLabel);\n    }\n  }\n  isExpanded(itemLabel) {\n    return this.expandedItems.has(itemLabel);\n  }\n  autoExpandForCurrentRoute() {\n    // Auto-expand menu items based on current route\n    for (const item of this.menuItems) {\n      if (item.children) {\n        const hasActiveChild = item.children.some(child => this.currentRoute.startsWith(child.route));\n        if (hasActiveChild) {\n          this.expandedItems.add(item.label);\n        }\n      }\n    }\n  }\n  logout() {\n    this.adminAuthService.logout();\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AdminAuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      outputs: {\n        sidenavToggle: \"sidenavToggle\"\n      },\n      ngContentSelectors: _c0,\n      decls: 36,\n      vars: 3,\n      consts: [[\"sidenav\", \"\"], [\"userMenu\", \"matMenu\"], [1, \"sidebar-container\"], [\"mode\", \"side\", \"opened\", \"\", 1, \"sidebar\"], [1, \"sidebar-header\"], [1, \"logo\"], [1, \"sidebar-nav\"], [\"class\", \"nav-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-footer\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"user-menu-trigger\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/profile\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/settings\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"main-content\"], [1, \"nav-item\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", \"class\", \"nav-link\", 3, \"routerLink\", 4, \"ngIf\"], [\"class\", \"nav-group\", 4, \"ngIf\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLink\"], [\"class\", \"nav-badge\", 4, \"ngIf\"], [1, \"nav-badge\"], [1, \"nav-group\"], [\"mat-button\", \"\", 1, \"nav-link\", \"expandable\", 3, \"click\"], [1, \"expand-icon\"], [1, \"sub-menu\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", \"class\", \"nav-link sub-link\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [\"mat-button\", \"\", \"routerLinkActive\", \"active\", 1, \"nav-link\", \"sub-link\", 3, \"routerLink\"], [1, \"user-info\"], [1, \"user-avatar\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-role\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"mat-sidenav-container\", 2)(1, \"mat-sidenav\", 3, 0)(3, \"div\", 4)(4, \"div\", 5)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"store\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"DFashion Admin\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"nav\", 6);\n          i0.ɵɵtemplate(10, SidebarComponent_div_10_Template, 3, 2, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtemplate(12, SidebarComponent_div_12_Template, 10, 4, \"div\", 9);\n          i0.ɵɵelementStart(13, \"button\", 10)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"more_vert\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"mat-menu\", null, 1)(18, \"button\", 11)(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 12)(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(28, \"mat-divider\");\n          i0.ɵɵelementStart(29, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_29_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelementStart(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"span\");\n          i0.ɵɵtext(33, \"Logout\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(34, \"mat-sidenav-content\", 14);\n          i0.ɵɵprojection(35);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const userMenu_r6 = i0.ɵɵreference(17);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r6);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i1.RouterLink, i1.RouterLinkActive, i4.MatSidenav, i4.MatSidenavContainer, i4.MatSidenavContent, i5.MatDivider, i6.MatIcon, i7.MatAnchor, i7.MatButton, i7.MatIconButton, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i3.TitleCasePipe],\n      styles: [\".sidebar-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n}\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: #1a1a1a;\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.sidebar-header[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1rem;\\n  border-bottom: 1px solid #333;\\n}\\n.sidebar-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n}\\n.sidebar-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: #4caf50;\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem 0;\\n  overflow-y: auto;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.75rem 1rem;\\n  color: #ccc;\\n  text-decoration: none;\\n  border-radius: 0;\\n  justify-content: flex-start;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: #999;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: left;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link.expandable[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link.expandable.expanded[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.05);\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link.sub-link[_ngcontent-%COMP%] {\\n  padding-left: 3rem;\\n  font-size: 0.8125rem;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-link.sub-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%] {\\n  background: #f44336;\\n  color: white;\\n  font-size: 0.625rem;\\n  padding: 0.125rem 0.375rem;\\n  border-radius: 10px;\\n  font-weight: 600;\\n  min-width: 18px;\\n  text-align: center;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-group[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]   .nav-group[_ngcontent-%COMP%]   .sub-menu.expanded[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-top: 1px solid #333;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: #4caf50;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: white;\\n  margin-bottom: 0.125rem;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .user-role[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #999;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .user-menu-trigger[_ngcontent-%COMP%] {\\n  color: #ccc;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .user-menu-trigger[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  overflow-x: hidden;\\n}\\n\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    position: fixed;\\n    z-index: 1000;\\n  }\\n  .sidebar-container[_ngcontent-%COMP%]     .mat-sidenav-backdrop {\\n    background: rgba(0, 0, 0, 0.6);\\n  }\\n}\\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #555;\\n  border-radius: 2px;\\n}\\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #777;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4vbGF5b3V0L3NpZGViYXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0FBQ0Y7O0FBRUE7RUFDRSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxvQkFBQTtFQUNBLDZCQUFBO0FBQ0Y7QUFDRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBQ0o7QUFDSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7QUFDTjs7QUFJQTtFQUNFLE9BQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFERjtBQUdFO0VBQ0Usc0JBQUE7QUFESjtBQUlFO0VBQ0UsV0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxxQkFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsMkJBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0FBRko7QUFJSTtFQUNFLG9DQUFBO0VBQ0EsWUFBQTtBQUZOO0FBS0k7RUFDRSxtQkFBQTtFQUNBLFlBQUE7QUFITjtBQUtNO0VBQ0UsWUFBQTtBQUhSO0FBT0k7RUFDRSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0EsV0FBQTtBQUxOO0FBUUk7RUFDRSxPQUFBO0VBQ0EsZ0JBQUE7QUFOTjtBQVVNO0VBQ0UsaUJBQUE7RUFDQSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFSUjtBQVdNO0VBQ0UscUNBQUE7QUFUUjtBQWFJO0VBQ0Usa0JBQUE7RUFDQSxvQkFBQTtBQVhOO0FBYU07RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFYUjtBQWdCRTtFQUNFLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsMEJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0FBZEo7QUFrQkk7RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQ0FBQTtBQWhCTjtBQWtCTTtFQUNFLGlCQUFBO0FBaEJSOztBQXNCQTtFQUNFLGFBQUE7RUFDQSwwQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUFuQkY7QUFxQkU7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtBQW5CSjtBQXNCTTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7QUFwQlI7QUF5Qk07RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLHVCQUFBO0FBdkJSO0FBMEJNO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0FBeEJSO0FBNkJFO0VBQ0UsV0FBQTtBQTNCSjtBQTZCSTtFQUNFLFlBQUE7QUEzQk47O0FBZ0NBO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQTdCRjs7QUFpQ0E7RUFDRTtJQUNFLFdBQUE7SUFDQSxlQUFBO0lBQ0EsYUFBQTtFQTlCRjtFQW1DSTtJQUNFLDhCQUFBO0VBakNOO0FBQ0Y7QUF3Q0U7RUFDRSxVQUFBO0FBdENKO0FBeUNFO0VBQ0UsdUJBQUE7QUF2Q0o7QUEwQ0U7RUFDRSxnQkFBQTtFQUNBLGtCQUFBO0FBeENKO0FBMENJO0VBQ0UsZ0JBQUE7QUF4Q04iLCJzb3VyY2VzQ29udGVudCI6WyIuc2lkZWJhci1jb250YWluZXIge1xuICBoZWlnaHQ6IDEwMHZoO1xufVxuXG4uc2lkZWJhciB7XG4gIHdpZHRoOiAyODBweDtcbiAgYmFja2dyb3VuZDogIzFhMWExYTtcbiAgY29sb3I6IHdoaXRlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xufVxuXG4uc2lkZWJhci1oZWFkZXIge1xuICBwYWRkaW5nOiAxLjVyZW0gMXJlbTtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICMzMzM7XG5cbiAgLmxvZ28ge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDAuNzVyZW07XG4gICAgZm9udC1zaXplOiAxLjI1cmVtO1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG5cbiAgICBtYXQtaWNvbiB7XG4gICAgICBmb250LXNpemU6IDJyZW07XG4gICAgICB3aWR0aDogMnJlbTtcbiAgICAgIGhlaWdodDogMnJlbTtcbiAgICAgIGNvbG9yOiAjNGNhZjUwO1xuICAgIH1cbiAgfVxufVxuXG4uc2lkZWJhci1uYXYge1xuICBmbGV4OiAxO1xuICBwYWRkaW5nOiAxcmVtIDA7XG4gIG92ZXJmbG93LXk6IGF1dG87XG5cbiAgLm5hdi1pdGVtIHtcbiAgICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xuICB9XG5cbiAgLm5hdi1saW5rIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAwLjc1cmVtO1xuICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcbiAgICBjb2xvcjogI2NjYztcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgYm9yZGVyLXJhZGl1czogMDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgfVxuXG4gICAgJi5hY3RpdmUge1xuICAgICAgYmFja2dyb3VuZDogIzRjYWY1MDtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcblxuICAgICAgbWF0LWljb24ge1xuICAgICAgICBjb2xvcjogd2hpdGU7XG4gICAgICB9XG4gICAgfVxuXG4gICAgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xuICAgICAgd2lkdGg6IDEuMjVyZW07XG4gICAgICBoZWlnaHQ6IDEuMjVyZW07XG4gICAgICBjb2xvcjogIzk5OTtcbiAgICB9XG5cbiAgICBzcGFuIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICB0ZXh0LWFsaWduOiBsZWZ0O1xuICAgIH1cblxuICAgICYuZXhwYW5kYWJsZSB7XG4gICAgICAuZXhwYW5kLWljb24ge1xuICAgICAgICBtYXJnaW4tbGVmdDogYXV0bztcbiAgICAgICAgZm9udC1zaXplOiAxcmVtO1xuICAgICAgICB3aWR0aDogMXJlbTtcbiAgICAgICAgaGVpZ2h0OiAxcmVtO1xuICAgICAgfVxuXG4gICAgICAmLmV4cGFuZGVkIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAmLnN1Yi1saW5rIHtcbiAgICAgIHBhZGRpbmctbGVmdDogM3JlbTtcbiAgICAgIGZvbnQtc2l6ZTogMC44MTI1cmVtO1xuXG4gICAgICBtYXQtaWNvbiB7XG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgICAgd2lkdGg6IDFyZW07XG4gICAgICAgIGhlaWdodDogMXJlbTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAubmF2LWJhZGdlIHtcbiAgICBiYWNrZ3JvdW5kOiAjZjQ0MzM2O1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBmb250LXNpemU6IDAuNjI1cmVtO1xuICAgIHBhZGRpbmc6IDAuMTI1cmVtIDAuMzc1cmVtO1xuICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBtaW4td2lkdGg6IDE4cHg7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG5cbiAgLm5hdi1ncm91cCB7XG4gICAgLnN1Yi1tZW51IHtcbiAgICAgIG1heC1oZWlnaHQ6IDA7XG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgdHJhbnNpdGlvbjogbWF4LWhlaWdodCAwLjNzIGVhc2U7XG5cbiAgICAgICYuZXhwYW5kZWQge1xuICAgICAgICBtYXgtaGVpZ2h0OiAzMDBweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLnNpZGViYXItZm9vdGVyIHtcbiAgcGFkZGluZzogMXJlbTtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICMzMzM7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC43NXJlbTtcblxuICAudXNlci1pbmZvIHtcbiAgICBmbGV4OiAxO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDAuNzVyZW07XG5cbiAgICAudXNlci1hdmF0YXIge1xuICAgICAgbWF0LWljb24ge1xuICAgICAgICBmb250LXNpemU6IDJyZW07XG4gICAgICAgIHdpZHRoOiAycmVtO1xuICAgICAgICBoZWlnaHQ6IDJyZW07XG4gICAgICAgIGNvbG9yOiAjNGNhZjUwO1xuICAgICAgfVxuICAgIH1cblxuICAgIC51c2VyLWRldGFpbHMge1xuICAgICAgLnVzZXItbmFtZSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4xMjVyZW07XG4gICAgICB9XG5cbiAgICAgIC51c2VyLXJvbGUge1xuICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XG4gICAgICAgIGNvbG9yOiAjOTk5O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC51c2VyLW1lbnUtdHJpZ2dlciB7XG4gICAgY29sb3I6ICNjY2M7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICB9XG4gIH1cbn1cblxuLm1haW4tY29udGVudCB7XG4gIGJhY2tncm91bmQ6ICNmNWY1ZjU7XG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcbn1cblxuLy8gUmVzcG9uc2l2ZSBkZXNpZ25cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuc2lkZWJhciB7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgcG9zaXRpb246IGZpeGVkO1xuICAgIHotaW5kZXg6IDEwMDA7XG4gIH1cblxuICAuc2lkZWJhci1jb250YWluZXIge1xuICAgIDo6bmctZGVlcCB7XG4gICAgICAubWF0LXNpZGVuYXYtYmFja2Ryb3Age1xuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNik7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIEN1c3RvbSBzY3JvbGxiYXIgZm9yIHNpZGViYXIgbmF2XG4uc2lkZWJhci1uYXYge1xuICAmOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgd2lkdGg6IDRweDtcbiAgfVxuXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgfVxuXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICBiYWNrZ3JvdW5kOiAjNTU1O1xuICAgIGJvcmRlci1yYWRpdXM6IDJweDtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogIzc3NztcbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "NavigationEnd", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r2", "badge", "ɵɵtemplate", "SidebarComponent_div_10_a_1_span_5_Template", "ɵɵproperty", "route", "icon", "label", "child_r5", "SidebarComponent_div_10_div_2_a_9_span_5_Template", "ɵɵlistener", "SidebarComponent_div_10_div_2_Template_button_click_1_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "toggleExpanded", "SidebarComponent_div_10_div_2_a_9_Template", "ɵɵclassProp", "isExpanded", "ɵɵtextInterpolate1", "children", "SidebarComponent_div_10_a_1_Template", "SidebarComponent_div_10_div_2_Template", "currentUser", "fullName", "ɵɵpipeBind1", "role", "SidebarComponent", "constructor", "router", "adminAuthService", "sidenavToggle", "expandedItems", "Set", "currentRoute", "menuItems", "ngOnInit", "currentUser$", "subscribe", "user", "events", "pipe", "event", "url", "autoExpandForCurrentRoute", "itemLabel", "has", "delete", "add", "item", "hasActiveChild", "some", "child", "startsWith", "logout", "ɵɵdirectiveInject", "i1", "Router", "i2", "AdminAuthService", "selectors", "outputs", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_10_Template", "SidebarComponent_div_12_Template", "ɵɵelement", "SidebarComponent_Template_button_click_29_listener", "_r1", "ɵɵprojection", "userMenu_r6"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\layout\\sidebar.component.ts"], "sourcesContent": ["import { Component, OnInit, Output, EventEmitter } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { AdminAuthService } from '../services/admin-auth.service';\n\ninterface MenuItem {\n  label: string;\n  icon: string;\n  route: string;\n  children?: MenuItem[];\n  permission?: string;\n  badge?: string;\n}\n\n@Component({\n  selector: 'app-sidebar',\n  template: `\n    <mat-sidenav-container class=\"sidebar-container\">\n      <mat-sidenav #sidenav mode=\"side\" opened class=\"sidebar\">\n        <!-- Logo Section -->\n        <div class=\"sidebar-header\">\n          <div class=\"logo\">\n            <mat-icon>store</mat-icon>\n            <span>DFashion Admin</span>\n          </div>\n        </div>\n\n        <!-- Navigation Menu -->\n        <nav class=\"sidebar-nav\">\n          <div *ngFor=\"let item of menuItems\" class=\"nav-item\">\n            <!-- Main Menu Item -->\n            <a \n              *ngIf=\"!item.children\"\n              mat-button \n              [routerLink]=\"item.route\"\n              routerLinkActive=\"active\"\n              class=\"nav-link\">\n              <mat-icon>{{ item.icon }}</mat-icon>\n              <span>{{ item.label }}</span>\n              <span *ngIf=\"item.badge\" class=\"nav-badge\">{{ item.badge }}</span>\n            </a>\n\n            <!-- Menu Item with Children -->\n            <div *ngIf=\"item.children\" class=\"nav-group\">\n              <button \n                mat-button \n                class=\"nav-link expandable\"\n                [class.expanded]=\"isExpanded(item.label)\"\n                (click)=\"toggleExpanded(item.label)\">\n                <mat-icon>{{ item.icon }}</mat-icon>\n                <span>{{ item.label }}</span>\n                <mat-icon class=\"expand-icon\">\n                  {{ isExpanded(item.label) ? 'expand_less' : 'expand_more' }}\n                </mat-icon>\n              </button>\n\n              <div class=\"sub-menu\" [class.expanded]=\"isExpanded(item.label)\">\n                <a \n                  *ngFor=\"let child of item.children\"\n                  mat-button \n                  [routerLink]=\"child.route\"\n                  routerLinkActive=\"active\"\n                  class=\"nav-link sub-link\">\n                  <mat-icon>{{ child.icon }}</mat-icon>\n                  <span>{{ child.label }}</span>\n                  <span *ngIf=\"child.badge\" class=\"nav-badge\">{{ child.badge }}</span>\n                </a>\n              </div>\n            </div>\n          </div>\n        </nav>\n\n        <!-- User Section -->\n        <div class=\"sidebar-footer\">\n          <div class=\"user-info\" *ngIf=\"currentUser\">\n            <div class=\"user-avatar\">\n              <mat-icon>account_circle</mat-icon>\n            </div>\n            <div class=\"user-details\">\n              <div class=\"user-name\">{{ currentUser.fullName }}</div>\n              <div class=\"user-role\">{{ currentUser.role | titlecase }}</div>\n            </div>\n          </div>\n          \n          <button mat-icon-button [matMenuTriggerFor]=\"userMenu\" class=\"user-menu-trigger\">\n            <mat-icon>more_vert</mat-icon>\n          </button>\n\n          <mat-menu #userMenu=\"matMenu\">\n            <button mat-menu-item routerLink=\"/admin/profile\">\n              <mat-icon>person</mat-icon>\n              <span>Profile</span>\n            </button>\n            <button mat-menu-item routerLink=\"/admin/settings\">\n              <mat-icon>settings</mat-icon>\n              <span>Settings</span>\n            </button>\n            <mat-divider></mat-divider>\n            <button mat-menu-item (click)=\"logout()\">\n              <mat-icon>logout</mat-icon>\n              <span>Logout</span>\n            </button>\n          </mat-menu>\n        </div>\n      </mat-sidenav>\n\n      <mat-sidenav-content class=\"main-content\">\n        <ng-content></ng-content>\n      </mat-sidenav-content>\n    </mat-sidenav-container>\n  `,\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  @Output() sidenavToggle = new EventEmitter<void>();\n\n  currentUser: any = null;\n  expandedItems: Set<string> = new Set();\n  currentRoute: string = '';\n\n  menuItems: MenuItem[] = [\n    {\n      label: 'Dashboard',\n      icon: 'dashboard',\n      route: '/admin/dashboard'\n    },\n    {\n      label: 'Users',\n      icon: 'people',\n      route: '/admin/users',\n      children: [\n        { label: 'All Users', icon: 'list', route: '/admin/users' },\n        { label: 'Add User', icon: 'person_add', route: '/admin/users/new' },\n        { label: 'Roles & Permissions', icon: 'security', route: '/admin/users/roles' }\n      ]\n    },\n    {\n      label: 'Products',\n      icon: 'inventory_2',\n      route: '/admin/products',\n      children: [\n        { label: 'All Products', icon: 'list', route: '/admin/products' },\n        { label: 'Add Product', icon: 'add_box', route: '/admin/products/new' },\n        { label: 'Categories', icon: 'category', route: '/admin/products/categories' },\n        { label: 'Inventory', icon: 'warehouse', route: '/admin/products/inventory' }\n      ]\n    },\n    {\n      label: 'Orders',\n      icon: 'shopping_cart',\n      route: '/admin/orders',\n      badge: '12'\n    },\n    {\n      label: 'Analytics',\n      icon: 'analytics',\n      route: '/admin/analytics'\n    },\n    {\n      label: 'Marketing',\n      icon: 'campaign',\n      route: '/admin/marketing',\n      children: [\n        { label: 'Campaigns', icon: 'email', route: '/admin/marketing/campaigns' },\n        { label: 'Coupons', icon: 'local_offer', route: '/admin/marketing/coupons' },\n        { label: 'Reviews', icon: 'rate_review', route: '/admin/marketing/reviews' }\n      ]\n    },\n    {\n      label: 'Settings',\n      icon: 'settings',\n      route: '/admin/settings'\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private adminAuthService: AdminAuthService\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.adminAuthService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    // Track current route\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd)\n    ).subscribe((event) => {\n      if (event instanceof NavigationEnd) {\n        this.currentRoute = event.url;\n        this.autoExpandForCurrentRoute();\n      }\n    });\n\n    // Set initial route\n    this.currentRoute = this.router.url;\n    this.autoExpandForCurrentRoute();\n  }\n\n  toggleExpanded(itemLabel: string): void {\n    if (this.expandedItems.has(itemLabel)) {\n      this.expandedItems.delete(itemLabel);\n    } else {\n      this.expandedItems.add(itemLabel);\n    }\n  }\n\n  isExpanded(itemLabel: string): boolean {\n    return this.expandedItems.has(itemLabel);\n  }\n\n  private autoExpandForCurrentRoute(): void {\n    // Auto-expand menu items based on current route\n    for (const item of this.menuItems) {\n      if (item.children) {\n        const hasActiveChild = item.children.some(child => \n          this.currentRoute.startsWith(child.route)\n        );\n        if (hasActiveChild) {\n          this.expandedItems.add(item.label);\n        }\n      }\n    }\n  }\n\n  logout(): void {\n    this.adminAuthService.logout();\n  }\n}\n"], "mappings": "AAAA,SAAoCA,YAAY,QAAQ,eAAe;AACvE,SAAiBC,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;IAqCzBC,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvBH,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAgB;;;;;IAF3DP,EANF,CAAAC,cAAA,YAKmB,eACP;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAQ,UAAA,IAAAC,2CAAA,mBAA2C;IAC7CT,EAAA,CAAAG,YAAA,EAAI;;;;IANFH,EAAA,CAAAU,UAAA,eAAAJ,OAAA,CAAAK,KAAA,CAAyB;IAGfX,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAM,IAAA,CAAe;IACnBZ,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAO,KAAA,CAAgB;IACfb,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAU,UAAA,SAAAJ,OAAA,CAAAC,KAAA,CAAgB;;;;;IA0BnBP,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxBH,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAS,QAAA,CAAAP,KAAA,CAAiB;;;;;IAF7DP,EANF,CAAAC,cAAA,YAK4B,eAChB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAQ,UAAA,IAAAO,iDAAA,mBAA4C;IAC9Cf,EAAA,CAAAG,YAAA,EAAI;;;;IANFH,EAAA,CAAAU,UAAA,eAAAI,QAAA,CAAAH,KAAA,CAA0B;IAGhBX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAS,QAAA,CAAAF,IAAA,CAAgB;IACpBZ,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAS,QAAA,CAAAD,KAAA,CAAiB;IAChBb,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAU,UAAA,SAAAI,QAAA,CAAAP,KAAA,CAAiB;;;;;;IArB5BP,EADF,CAAAC,cAAA,cAA6C,iBAKJ;IAArCD,EAAA,CAAAgB,UAAA,mBAAAC,+DAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAb,OAAA,GAAAN,EAAA,CAAAoB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAuB,WAAA,CAASD,MAAA,CAAAE,cAAA,CAAAlB,OAAA,CAAAO,KAAA,CAA0B;IAAA,EAAC;IACpCb,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAC,cAAA,mBAA8B;IAC5BD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACJ;IAETH,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAQ,UAAA,IAAAiB,0CAAA,gBAK4B;IAMhCzB,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IArBFH,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAA0B,WAAA,aAAAJ,MAAA,CAAAK,UAAA,CAAArB,OAAA,CAAAO,KAAA,EAAyC;IAE/Bb,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAM,IAAA,CAAe;IACnBZ,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAO,KAAA,CAAgB;IAEpBb,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA4B,kBAAA,MAAAN,MAAA,CAAAK,UAAA,CAAArB,OAAA,CAAAO,KAAA,uCACF;IAGoBb,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAA0B,WAAA,aAAAJ,MAAA,CAAAK,UAAA,CAAArB,OAAA,CAAAO,KAAA,EAAyC;IAEzCb,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAU,UAAA,YAAAJ,OAAA,CAAAuB,QAAA,CAAgB;;;;;IA7B1C7B,EAAA,CAAAC,cAAA,cAAqD;IAcnDD,EAZA,CAAAQ,UAAA,IAAAsB,oCAAA,gBAKmB,IAAAC,sCAAA,mBAO0B;IA0B/C/B,EAAA,CAAAG,YAAA,EAAM;;;;IArCDH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAU,UAAA,UAAAJ,OAAA,CAAAuB,QAAA,CAAoB;IAWjB7B,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAU,UAAA,SAAAJ,OAAA,CAAAuB,QAAA,CAAmB;;;;;IAiCvB7B,EAFJ,CAAAC,cAAA,cAA2C,cAChB,eACb;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAC1BF,EAD0B,CAAAG,YAAA,EAAW,EAC/B;IAEJH,EADF,CAAAC,cAAA,cAA0B,cACD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAE7DF,EAF6D,CAAAG,YAAA,EAAM,EAC3D,EACF;;;;IAHqBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAiB,MAAA,CAAAU,WAAA,CAAAC,QAAA,CAA0B;IAC1BjC,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkC,WAAA,OAAAZ,MAAA,CAAAU,WAAA,CAAAG,IAAA,EAAkC;;;AAiCvE,OAAM,MAAOC,gBAAgB;EA8D3BC,YACUC,MAAc,EACdC,gBAAkC;IADlC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA/DhB,KAAAC,aAAa,GAAG,IAAI3C,YAAY,EAAQ;IAElD,KAAAmC,WAAW,GAAQ,IAAI;IACvB,KAAAS,aAAa,GAAgB,IAAIC,GAAG,EAAE;IACtC,KAAAC,YAAY,GAAW,EAAE;IAEzB,KAAAC,SAAS,GAAe,CACtB;MACE/B,KAAK,EAAE,WAAW;MAClBD,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,OAAO;MACdD,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE,cAAc;MACrBkB,QAAQ,EAAE,CACR;QAAEhB,KAAK,EAAE,WAAW;QAAED,IAAI,EAAE,MAAM;QAAED,KAAK,EAAE;MAAc,CAAE,EAC3D;QAAEE,KAAK,EAAE,UAAU;QAAED,IAAI,EAAE,YAAY;QAAED,KAAK,EAAE;MAAkB,CAAE,EACpE;QAAEE,KAAK,EAAE,qBAAqB;QAAED,IAAI,EAAE,UAAU;QAAED,KAAK,EAAE;MAAoB,CAAE;KAElF,EACD;MACEE,KAAK,EAAE,UAAU;MACjBD,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,iBAAiB;MACxBkB,QAAQ,EAAE,CACR;QAAEhB,KAAK,EAAE,cAAc;QAAED,IAAI,EAAE,MAAM;QAAED,KAAK,EAAE;MAAiB,CAAE,EACjE;QAAEE,KAAK,EAAE,aAAa;QAAED,IAAI,EAAE,SAAS;QAAED,KAAK,EAAE;MAAqB,CAAE,EACvE;QAAEE,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,UAAU;QAAED,KAAK,EAAE;MAA4B,CAAE,EAC9E;QAAEE,KAAK,EAAE,WAAW;QAAED,IAAI,EAAE,WAAW;QAAED,KAAK,EAAE;MAA2B,CAAE;KAEhF,EACD;MACEE,KAAK,EAAE,QAAQ;MACfD,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,eAAe;MACtBJ,KAAK,EAAE;KACR,EACD;MACEM,KAAK,EAAE,WAAW;MAClBD,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,WAAW;MAClBD,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,kBAAkB;MACzBkB,QAAQ,EAAE,CACR;QAAEhB,KAAK,EAAE,WAAW;QAAED,IAAI,EAAE,OAAO;QAAED,KAAK,EAAE;MAA4B,CAAE,EAC1E;QAAEE,KAAK,EAAE,SAAS;QAAED,IAAI,EAAE,aAAa;QAAED,KAAK,EAAE;MAA0B,CAAE,EAC5E;QAAEE,KAAK,EAAE,SAAS;QAAED,IAAI,EAAE,aAAa;QAAED,KAAK,EAAE;MAA0B,CAAE;KAE/E,EACD;MACEE,KAAK,EAAE,UAAU;MACjBD,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE;KACR,CACF;EAKE;EAEHkC,QAAQA,CAAA;IACN;IACA,IAAI,CAACN,gBAAgB,CAACO,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAClD,IAAI,CAAChB,WAAW,GAAGgB,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACV,MAAM,CAACW,MAAM,CAACC,IAAI,CACrBnD,MAAM,CAACoD,KAAK,IAAIA,KAAK,YAAYrD,aAAa,CAAC,CAChD,CAACiD,SAAS,CAAEI,KAAK,IAAI;MACpB,IAAIA,KAAK,YAAYrD,aAAa,EAAE;QAClC,IAAI,CAAC6C,YAAY,GAAGQ,KAAK,CAACC,GAAG;QAC7B,IAAI,CAACC,yBAAyB,EAAE;;IAEpC,CAAC,CAAC;IAEF;IACA,IAAI,CAACV,YAAY,GAAG,IAAI,CAACL,MAAM,CAACc,GAAG;IACnC,IAAI,CAACC,yBAAyB,EAAE;EAClC;EAEA7B,cAAcA,CAAC8B,SAAiB;IAC9B,IAAI,IAAI,CAACb,aAAa,CAACc,GAAG,CAACD,SAAS,CAAC,EAAE;MACrC,IAAI,CAACb,aAAa,CAACe,MAAM,CAACF,SAAS,CAAC;KACrC,MAAM;MACL,IAAI,CAACb,aAAa,CAACgB,GAAG,CAACH,SAAS,CAAC;;EAErC;EAEA3B,UAAUA,CAAC2B,SAAiB;IAC1B,OAAO,IAAI,CAACb,aAAa,CAACc,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEQD,yBAAyBA,CAAA;IAC/B;IACA,KAAK,MAAMK,IAAI,IAAI,IAAI,CAACd,SAAS,EAAE;MACjC,IAAIc,IAAI,CAAC7B,QAAQ,EAAE;QACjB,MAAM8B,cAAc,GAAGD,IAAI,CAAC7B,QAAQ,CAAC+B,IAAI,CAACC,KAAK,IAC7C,IAAI,CAAClB,YAAY,CAACmB,UAAU,CAACD,KAAK,CAAClD,KAAK,CAAC,CAC1C;QACD,IAAIgD,cAAc,EAAE;UAClB,IAAI,CAAClB,aAAa,CAACgB,GAAG,CAACC,IAAI,CAAC7C,KAAK,CAAC;;;;EAI1C;EAEAkD,MAAMA,CAAA;IACJ,IAAI,CAACxB,gBAAgB,CAACwB,MAAM,EAAE;EAChC;;;uBApHW3B,gBAAgB,EAAApC,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAhBhC,gBAAgB;MAAAiC,SAAA;MAAAC,OAAA;QAAA9B,aAAA;MAAA;MAAA+B,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;UA3FjB9E,EALR,CAAAC,cAAA,+BAAiD,wBACU,aAE3B,aACR,eACN;UAAAD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAExBF,EAFwB,CAAAG,YAAA,EAAO,EACvB,EACF;UAGNH,EAAA,CAAAC,cAAA,aAAyB;UACvBD,EAAA,CAAAQ,UAAA,KAAAwE,gCAAA,iBAAqD;UAyCvDhF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA4B;UAC1BD,EAAA,CAAAQ,UAAA,KAAAyE,gCAAA,kBAA2C;UAWzCjF,EADF,CAAAC,cAAA,kBAAiF,gBACrE;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UACrBF,EADqB,CAAAG,YAAA,EAAW,EACvB;UAILH,EAFJ,CAAAC,cAAA,yBAA8B,kBACsB,gBACtC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACfF,EADe,CAAAG,YAAA,EAAO,EACb;UAEPH,EADF,CAAAC,cAAA,kBAAmD,gBACvC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAChBF,EADgB,CAAAG,YAAA,EAAO,EACd;UACTH,EAAA,CAAAkF,SAAA,mBAA2B;UAC3BlF,EAAA,CAAAC,cAAA,kBAAyC;UAAnBD,EAAA,CAAAgB,UAAA,mBAAAmE,mDAAA;YAAAnF,EAAA,CAAAkB,aAAA,CAAAkE,GAAA;YAAA,OAAApF,EAAA,CAAAuB,WAAA,CAASwD,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UACtC/D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAIpBF,EAJoB,CAAAG,YAAA,EAAO,EACZ,EACA,EACP,EACM;UAEdH,EAAA,CAAAC,cAAA,+BAA0C;UACxCD,EAAA,CAAAqF,YAAA,IAAyB;UAE7BrF,EADE,CAAAG,YAAA,EAAsB,EACA;;;;UAhFIH,EAAA,CAAAI,SAAA,IAAY;UAAZJ,EAAA,CAAAU,UAAA,YAAAqE,GAAA,CAAAnC,SAAA,CAAY;UA6CV5C,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAU,UAAA,SAAAqE,GAAA,CAAA/C,WAAA,CAAiB;UAUjBhC,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAAU,UAAA,sBAAA4E,WAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}