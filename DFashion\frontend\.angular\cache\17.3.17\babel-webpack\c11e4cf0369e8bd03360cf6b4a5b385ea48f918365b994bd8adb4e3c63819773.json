{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StoriesComponent } from '../../components/stories/stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  constructor() {}\n  ngOnInit() {\n    console.log('Home component initialized');\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 0,\n      consts: [[1, \"home-container\"], [1, \"content-grid\"], [1, \"main-content\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-stories\")(4, \"app-feed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"app-sidebar\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [CommonModule, StoriesComponent, FeedComponent, SidebarComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  min-height: calc(100vh - 60px);\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 600px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 20px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9wYWdlcy9ob21lL2hvbWUuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsZUFBQTtFQUNBLDhCQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsZ0NBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtBQUFOOztBQUdJO0VBQ0U7SUFDRSwwQkFBQTtJQUNBLGdCQUFBO0VBQU47QUFDRjtBQUdJO0VBQ0U7SUFDRSxlQUFBO0VBRE47RUFJSTtJQUNFLGVBQUE7SUFDQSxTQUFBO0VBRk47RUFLSTtJQUNFLFNBQUE7RUFITjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmhvbWUtY29udGFpbmVyIHtcbiAgICAgIHBhZGRpbmc6IDIwcHggMDtcbiAgICAgIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA2MHB4KTtcbiAgICB9XG5cbiAgICAuY29udGVudC1ncmlkIHtcbiAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciA0MDBweDtcbiAgICAgIGdhcDogNDBweDtcbiAgICAgIG1heC13aWR0aDogMTAwMHB4O1xuICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgICBwYWRkaW5nOiAwIDIwcHg7XG4gICAgfVxuXG4gICAgLm1haW4tY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogMjRweDtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogMTAyNHB4KSB7XG4gICAgICAuY29udGVudC1ncmlkIHtcbiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgICAgIG1heC13aWR0aDogNjAwcHg7XG4gICAgICB9XG4gICAgfVxuXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAuaG9tZS1jb250YWluZXIge1xuICAgICAgICBwYWRkaW5nOiAxNnB4IDA7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC5jb250ZW50LWdyaWQge1xuICAgICAgICBwYWRkaW5nOiAwIDE2cHg7XG4gICAgICAgIGdhcDogMjBweDtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLm1haW4tY29udGVudCB7XG4gICAgICAgIGdhcDogMjBweDtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "StoriesComponent", "FeedComponent", "SidebarComponent", "HomeComponent", "constructor", "ngOnInit", "console", "log", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { StoriesComponent } from '../../components/stories/stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, StoriesComponent, FeedComponent, SidebarComponent],\n  template: `\n    <div class=\"home-container\">\n      <div class=\"content-grid\">\n        <!-- Main Feed -->\n        <div class=\"main-content\">\n          <app-stories></app-stories>\n          <app-feed></app-feed>\n        </div>\n        \n        <!-- Sidebar -->\n        <app-sidebar></app-sidebar>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .home-container {\n      padding: 20px 0;\n      min-height: calc(100vh - 60px);\n    }\n\n    .content-grid {\n      display: grid;\n      grid-template-columns: 1fr 400px;\n      gap: 40px;\n      max-width: 1000px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n\n    .main-content {\n      display: flex;\n      flex-direction: column;\n      gap: 24px;\n    }\n\n    @media (max-width: 1024px) {\n      .content-grid {\n        grid-template-columns: 1fr;\n        max-width: 600px;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .home-container {\n        padding: 16px 0;\n      }\n      \n      .content-grid {\n        padding: 0 16px;\n        gap: 20px;\n      }\n      \n      .main-content {\n        gap: 20px;\n      }\n    }\n  `]\n})\nexport class HomeComponent implements OnInit {\n  constructor() {}\n\n  ngOnInit() {\n    console.log('Home component initialized');\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;;AAgE7E,OAAM,MAAOC,aAAa;EACxBC,YAAA,GAAe;EAEfC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;EAC3C;;;uBALWJ,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtDlBP,EAHJ,CAAAS,cAAA,aAA4B,aACA,aAEE;UAExBT,EADA,CAAAU,SAAA,kBAA2B,eACN;UACvBV,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAU,SAAA,kBAA2B;UAE/BV,EADE,CAAAW,YAAA,EAAM,EACF;;;qBAbEvB,YAAY,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,gBAAgB;MAAAqB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}