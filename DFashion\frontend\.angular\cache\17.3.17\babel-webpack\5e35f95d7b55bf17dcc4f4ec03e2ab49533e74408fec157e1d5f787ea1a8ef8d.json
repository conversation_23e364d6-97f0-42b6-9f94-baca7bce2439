{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Import existing sidebar components\nimport { SuggestedProductsComponent } from '../suggested-products/suggested-products.component';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { TopInfluencersComponent } from '../top-influencers/top-influencers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction SidebarComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"img\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"h4\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 36);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 37);\n    i0.ɵɵelement(9, \"i\", 38);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Profile\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatar || \"/assets/images/default-avatar.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.currentUser.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.fullName);\n  }\n}\nfunction SidebarComponent_section_2_div_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getDiscountPercentage(item_r4), \"% OFF \");\n  }\n}\nfunction SidebarComponent_section_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_2_div_10_Template_div_click_0_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(item_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 45);\n    i0.ɵɵelement(2, \"img\", 46);\n    i0.ɵɵelementStart(3, \"div\", 47);\n    i0.ɵɵelement(4, \"i\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 49)(6, \"span\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 51);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SidebarComponent_section_2_div_10_div_10_Template, 3, 1, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (item_r4.images == null ? null : item_r4.images[0] == null ? null : item_r4.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", item_r4.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(item_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getDiscountPercentage(item_r4) > 0);\n  }\n}\nfunction SidebarComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 39)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 40);\n    i0.ɵɵtext(4, \" Summer Collection 2024\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewSummerCollection());\n    });\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"View All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 42);\n    i0.ɵɵtemplate(10, SidebarComponent_section_2_div_10_Template, 11, 5, \"div\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.summerCollection.slice(0, 3))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_section_14_div_10_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatPrice(product_r7.originalPrice), \" \");\n  }\n}\nfunction SidebarComponent_section_14_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_14_div_10_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(product_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 60);\n    i0.ɵɵelement(2, \"img\", 61);\n    i0.ɵɵelementStart(3, \"div\", 62);\n    i0.ɵɵelement(4, \"i\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 64)(6, \"span\", 65);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 66);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 67)(11, \"span\", 68);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SidebarComponent_section_14_div_10_span_13_Template, 2, 1, \"span\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r7.images == null ? null : product_r7.images[0] == null ? null : product_r7.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(product_r7.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice);\n  }\n}\nfunction SidebarComponent_section_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 55)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 56);\n    i0.ɵɵtext(4, \" Featured Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_14_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewFeaturedProducts());\n    });\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"View All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 57);\n    i0.ɵɵtemplate(10, SidebarComponent_section_14_div_10_Template, 14, 6, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredProducts.slice(0, 4))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_section_26_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_26_div_10_Template_div_click_0_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onProductClick(product_r10));\n    });\n    i0.ɵɵelement(1, \"img\", 76);\n    i0.ɵɵelementStart(2, \"div\", 77);\n    i0.ɵɵtext(3, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 78)(5, \"span\", 79);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 80);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 81);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", (product_r10.images == null ? null : product_r10.images[0] == null ? null : product_r10.images[0].url) || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r10.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatPrice(product_r10.price));\n  }\n}\nfunction SidebarComponent_section_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 71)(1, \"div\", 4)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 72);\n    i0.ɵɵtext(4, \" New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_section_26_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.viewNewArrivals());\n    });\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"View All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 73);\n    i0.ɵɵtemplate(10, SidebarComponent_section_26_div_10_Template, 11, 5, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.newArrivals.slice(0, 4))(\"ngForTrackBy\", ctx_r0.trackByProductId);\n  }\n}\nfunction SidebarComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_43_Template_div_click_0_listener() {\n      const category_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCategoryClick(category_r12));\n    });\n    i0.ɵɵelement(1, \"img\", 83);\n    i0.ɵɵelementStart(2, \"span\", 84);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r12.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r12.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(router) {\n    this.router = router;\n    // Data passed from home component\n    this.currentUser = null;\n    this.featuredProducts = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.summerCollection = [];\n    this.categories = [];\n  }\n  ngOnInit() {\n    console.log('✅ Sidebar component initialized with data:', {\n      featuredProducts: this.featuredProducts.length,\n      trendingProducts: this.trendingProducts.length,\n      newArrivals: this.newArrivals.length,\n      summerCollection: this.summerCollection.length,\n      categories: this.categories.length\n    });\n  }\n  // Navigation methods\n  onProductClick(product) {\n    console.log('🛍️ Navigate to product:', product.name);\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n  onCategoryClick(category) {\n    console.log('📂 Navigate to category:', category.name);\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        category: category.slug || category.name.toLowerCase()\n      }\n    });\n  }\n  onUserClick(user) {\n    console.log('👤 Navigate to user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n  followUser(user) {\n    console.log('➕ Follow user:', user.username);\n    // TODO: Implement follow functionality\n  }\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        collection: 'summer2024'\n      }\n    });\n  }\n  viewFeaturedProducts() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        filter: 'featured'\n      }\n    });\n  }\n  viewNewArrivals() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        filter: 'new'\n      }\n    });\n  }\n  // Utility methods\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return product.discount || 0;\n  }\n  // Track by functions for performance\n  trackByProductId(index, product) {\n    return product._id || product.id;\n  }\n  trackByCategoryId(index, category) {\n    return category._id || category.id;\n  }\n  // Get categories to display (real data or fallback)\n  getDisplayCategories() {\n    if (this.categories && this.categories.length > 0) {\n      return this.categories.slice(0, 4);\n    }\n    // Fallback categories with images\n    return [{\n      _id: 'women',\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      _id: 'men',\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      _id: 'children',\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      _id: 'ethnic',\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      inputs: {\n        currentUser: \"currentUser\",\n        featuredProducts: \"featuredProducts\",\n        trendingProducts: \"trendingProducts\",\n        newArrivals: \"newArrivals\",\n        summerCollection: \"summerCollection\",\n        categories: \"categories\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 63,\n      vars: 6,\n      consts: [[1, \"home-sidebar\"], [\"class\", \"profile-card\", 4, \"ngIf\"], [\"class\", \"sidebar-section summer-collection\", 4, \"ngIf\"], [1, \"sidebar-section\", \"suggested-section\"], [1, \"section-header\"], [1, \"fas\", \"fa-magic\"], [\"routerLink\", \"/shop?filter=suggested\", 1, \"view-all-btn\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"component-wrapper\"], [\"class\", \"sidebar-section featured-section\", 4, \"ngIf\"], [1, \"sidebar-section\", \"trending-section\"], [1, \"fas\", \"fa-fire\"], [\"routerLink\", \"/shop?filter=trending\", 1, \"view-all-btn\"], [\"class\", \"sidebar-section arrivals-section\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"fas\", \"fa-crown\"], [\"routerLink\", \"/influencers\", 1, \"see-all-btn\"], [1, \"fas\", \"fa-th-large\"], [1, \"see-all-btn\", 3, \"click\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"sidebar-footer\"], [1, \"footer-links\"], [\"href\", \"#\", \"routerLink\", \"/about\"], [\"href\", \"#\", \"routerLink\", \"/help\"], [\"href\", \"#\", \"routerLink\", \"/press\"], [\"href\", \"#\", \"routerLink\", \"/api\"], [\"href\", \"#\", \"routerLink\", \"/jobs\"], [\"href\", \"#\", \"routerLink\", \"/privacy\"], [\"href\", \"#\", \"routerLink\", \"/terms\"], [1, \"copyright\"], [1, \"profile-card\"], [1, \"profile-avatar-container\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"profile-username\"], [1, \"profile-name\"], [\"routerLink\", \"/profile\", 1, \"switch-btn\"], [1, \"fas\", \"fa-user-cog\"], [1, \"sidebar-section\", \"summer-collection\"], [1, \"fas\", \"fa-sun\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"collection-items\"], [\"class\", \"collection-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"collection-item\", 3, \"click\"], [1, \"collection-image-container\"], [1, \"collection-image\", 3, \"src\", \"alt\"], [1, \"collection-overlay\"], [1, \"fas\", \"fa-eye\"], [1, \"collection-info\"], [1, \"collection-name\"], [1, \"collection-price\"], [\"class\", \"collection-discount\", 4, \"ngIf\"], [1, \"collection-discount\"], [1, \"fas\", \"fa-tag\"], [1, \"sidebar-section\", \"featured-section\"], [1, \"fas\", \"fa-star\"], [1, \"featured-grid\"], [\"class\", \"featured-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"featured-item\", 3, \"click\"], [1, \"featured-image-container\"], [1, \"featured-image\", 3, \"src\", \"alt\"], [1, \"featured-overlay\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"featured-info\"], [1, \"featured-name\"], [1, \"featured-brand\"], [1, \"featured-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"sidebar-section\", \"arrivals-section\"], [1, \"fas\", \"fa-sparkles\"], [1, \"arrivals-list\"], [\"class\", \"arrival-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"arrival-item\", 3, \"click\"], [1, \"arrival-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [1, \"arrival-info\"], [1, \"arrival-name\"], [1, \"arrival-brand\"], [1, \"arrival-price\"], [1, \"category-item\", 3, \"click\"], [1, \"category-image\", 3, \"src\", \"alt\"], [1, \"category-name\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, SidebarComponent_div_1_Template, 12, 4, \"div\", 1)(2, SidebarComponent_section_2_Template, 11, 2, \"section\", 2);\n          i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"h3\");\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \" Suggested for You\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 6)(9, \"span\");\n          i0.ɵɵtext(10, \"View All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵelement(13, \"app-suggested-products\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, SidebarComponent_section_14_Template, 11, 2, \"section\", 9);\n          i0.ɵɵelementStart(15, \"section\", 10)(16, \"div\", 4)(17, \"h3\");\n          i0.ɵɵelement(18, \"i\", 11);\n          i0.ɵɵtext(19, \" Trending Now\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 12)(21, \"span\");\n          i0.ɵɵtext(22, \"View All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 8);\n          i0.ɵɵelement(25, \"app-trending-products\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(26, SidebarComponent_section_26_Template, 11, 2, \"section\", 13);\n          i0.ɵɵelementStart(27, \"section\", 14)(28, \"div\", 4)(29, \"h3\");\n          i0.ɵɵelement(30, \"i\", 15);\n          i0.ɵɵtext(31, \" Top Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"button\", 16);\n          i0.ɵɵtext(33, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(34, \"app-top-influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"section\", 14)(36, \"div\", 4)(37, \"h3\");\n          i0.ɵɵelement(38, \"i\", 17);\n          i0.ɵɵtext(39, \" Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_40_listener() {\n            return ctx.viewAllCategories();\n          });\n          i0.ɵɵtext(41, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 19);\n          i0.ɵɵtemplate(43, SidebarComponent_div_43_Template, 4, 3, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 21)(45, \"div\", 22)(46, \"a\", 23);\n          i0.ɵɵtext(47, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"a\", 24);\n          i0.ɵɵtext(49, \"Help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"a\", 25);\n          i0.ɵɵtext(51, \"Press\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"a\", 26);\n          i0.ɵɵtext(53, \"API\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"a\", 27);\n          i0.ɵɵtext(55, \"Jobs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"a\", 28);\n          i0.ɵɵtext(57, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"a\", 29);\n          i0.ɵɵtext(59, \"Terms\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 30)(61, \"span\");\n          i0.ɵɵtext(62, \"\\u00A9 2024 DFashion\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.summerCollection.length > 0);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.featuredProducts.length > 0);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.newArrivals.length > 0);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getDisplayCategories())(\"ngForTrackBy\", ctx.trackByCategoryId);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, RouterModule, i1.RouterLink, SuggestedProductsComponent, TrendingProductsComponent, TopInfluencersComponent],\n      styles: [\".home-sidebar[_ngcontent-%COMP%] {\\n  width: 340px;\\n  padding: 0;\\n  position: sticky;\\n  top: 84px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  overflow-x: visible;\\n  background: transparent;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n@media (max-width: 1200px) {\\n  .home-sidebar[_ngcontent-%COMP%] {\\n    width: 300px;\\n    gap: 16px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .home-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.02);\\n  border-radius: 3px;\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 3px;\\n}\\n.home-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(135deg, #5a67d8, #6b46c1);\\n}\\n.home-sidebar[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.profile-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 20px;\\n  margin-bottom: 24px;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));\\n  border-radius: 16px;\\n  border: 1px solid rgba(102, 126, 234, 0.1);\\n  gap: 12px;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-avatar-container[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(102, 126, 234, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-avatar-container[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]:hover {\\n  border-color: #667eea;\\n  transform: scale(1.05);\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #2d3748;\\n  margin: 0 0 4px 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #718096;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  transition: all 0.3s ease;\\n  white-space: nowrap;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a67d8, #6b46c1);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 20px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n  overflow: visible;\\n  width: 100%;\\n  flex-shrink: 0;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #2d3748;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  flex: 1;\\n  min-width: 0;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #667eea;\\n  flex-shrink: 0;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%], .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  white-space: nowrap;\\n  flex-shrink: 0;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover, .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a67d8, #6b46c1);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  transition: transform 0.3s ease;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%], .sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(2px);\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .component-wrapper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: visible;\\n}\\n\\n.collection-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  width: 100%;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 14px;\\n  padding: 12px;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: rgba(102, 126, 234, 0.02);\\n  border: 1px solid rgba(102, 126, 234, 0.08);\\n  position: relative;\\n  overflow: visible;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.05);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex-shrink: 0;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 12px;\\n  object-fit: cover;\\n  border: 2px solid rgba(102, 126, 234, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(102, 126, 234, 0.8);\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 16px;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-image-container[_ngcontent-%COMP%]:hover   .collection-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 15px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  margin-bottom: 4px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #667eea;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-discount[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #e53e3e;\\n  font-weight: 700;\\n  background: rgba(229, 62, 62, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 6px;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.collection-items[_ngcontent-%COMP%]   .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-discount[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 9px;\\n}\\n\\n.featured-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n  width: 100%;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  background: white;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n  position: relative;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15), 0 4px 16px rgba(0, 0, 0, 0.08);\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image-container[_ngcontent-%COMP%]   .featured-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 120px;\\n  object-fit: cover;\\n  transition: all 0.3s ease;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image-container[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(102, 126, 234, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image-container[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 18px;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image-container[_ngcontent-%COMP%]:hover   .featured-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image-container[_ngcontent-%COMP%]:hover   .featured-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  background: white;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  margin-bottom: 4px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 11px;\\n  color: #718096;\\n  margin-bottom: 6px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 700;\\n  color: #667eea;\\n}\\n.featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #a0aec0;\\n  text-decoration: line-through;\\n}\\n@media (max-width: 1200px) {\\n  .featured-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-image-container[_ngcontent-%COMP%]   .featured-image[_ngcontent-%COMP%] {\\n    height: 100px;\\n  }\\n  .featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .featured-grid[_ngcontent-%COMP%]   .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n\\n.arrivals-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  width: 100%;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 14px;\\n  padding: 12px;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  position: relative;\\n  transition: all 0.3s ease;\\n  background: rgba(102, 126, 234, 0.02);\\n  border: 1px solid rgba(102, 126, 234, 0.08);\\n  overflow: visible;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.05);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-image[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 12px;\\n  object-fit: cover;\\n  border: 2px solid rgba(102, 126, 234, 0.1);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  background: linear-gradient(135deg, #e53e3e, #c53030);\\n  color: white;\\n  font-size: 9px;\\n  font-weight: 700;\\n  padding: 3px 6px;\\n  border-radius: 6px;\\n  box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);\\n  z-index: 1;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 15px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  margin-bottom: 4px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  line-height: 1.2;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #718096;\\n  margin-bottom: 4px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.arrivals-list[_ngcontent-%COMP%]   .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 700;\\n  color: #667eea;\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n  width: 100%;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 16px 12px;\\n  border-radius: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.02);\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15), 0 4px 16px rgba(0, 0, 0, 0.08);\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 3px solid rgba(102, 126, 234, 0.1);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 8px;\\n  transition: all 0.3s ease;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 16px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: center;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-count[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #718096;\\n  font-weight: 500;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .trending-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .trending-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n  font-size: 12px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover   .category-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #5a67d8, #6b46c1);\\n  transform: scale(1.05);\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  transform: scale(1.05);\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  padding-top: 16px;\\n  border-top: 1px solid #efefef;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n  text-decoration: none;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9jb21wb25lbnRzL3NpZGViYXIvc2lkZWJhci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLFlBQUE7RUFDQSxVQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0VBQ0Esd0JBQUE7RUFBQSxtQkFBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtBQUNGO0FBQ0U7RUFkRjtJQWVJLFlBQUE7SUFDQSxTQUFBO0VBRUY7QUFDRjtBQUFFO0VBbkJGO0lBb0JJLGFBQUE7RUFHRjtBQUNGO0FBQUU7RUFDRSxVQUFBO0FBRUo7QUFDRTtFQUNFLCtCQUFBO0VBQ0Esa0JBQUE7QUFDSjtBQUVFO0VBQ0UscURBQUE7RUFDQSxrQkFBQTtBQUFKO0FBRUk7RUFDRSxxREFBQTtBQUFOO0FBS0U7RUFDRSxjQUFBO0FBSEo7O0FBUUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx3RkFBQTtFQUNBLG1CQUFBO0VBQ0EsMENBQUE7RUFDQSxTQUFBO0FBTEY7QUFPRTtFQUNFLGtCQUFBO0FBTEo7QUFPSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7QUFMTjtBQU9NO0VBQ0UscUJBQUE7RUFDQSxzQkFBQTtBQUxSO0FBVUU7RUFDRSxPQUFBO0VBQ0EsWUFBQTtBQVJKO0FBVUk7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7QUFSTjtBQVdJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7QUFUTjtBQWFFO0VBQ0UscURBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7QUFYSjtBQWFJO0VBQ0UscURBQUE7RUFDQSwyQkFBQTtFQUNBLCtDQUFBO0FBWE47QUFjSTtFQUNFLGVBQUE7QUFaTjs7QUFrQkE7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHlFQUNFO0VBRUYscUNBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxjQUFBO0FBakJGO0FBbUJFO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxRQUFBO0FBakJKO0FBbUJJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLFlBQUE7QUFqQk47QUFtQk07RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7QUFqQlI7QUFxQkk7RUFDRSxxREFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7QUFuQk47QUFxQk07RUFDRSxxREFBQTtFQUNBLDJCQUFBO0VBQ0EsK0NBQUE7QUFuQlI7QUFzQk07RUFDRSxlQUFBO0VBQ0EsK0JBQUE7QUFwQlI7QUF1Qk07RUFDRSwwQkFBQTtBQXJCUjtBQTJCRTtFQUNFLFdBQUE7RUFDQSxpQkFBQTtBQXpCSjs7QUE4QkE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtBQTNCRjtBQTZCRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxxQ0FBQTtFQUNBLDJDQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQTNCSjtBQTZCSTtFQUNFLHFDQUFBO0VBQ0EsMkJBQUE7RUFDQSwrQ0FBQTtBQTNCTjtBQThCSTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtBQTVCTjtBQThCTTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7QUE1QlI7QUErQk07RUFDRSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxvQ0FBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxVQUFBO0VBQ0EseUJBQUE7QUE3QlI7QUErQlE7RUFDRSxZQUFBO0VBQ0EsZUFBQTtBQTdCVjtBQWlDTTtFQUNFLFVBQUE7QUEvQlI7QUFtQ0k7RUFDRSxPQUFBO0VBQ0EsWUFBQTtBQWpDTjtBQW1DTTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQWpDUjtBQW9DTTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQWxDUjtBQXFDTTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQ0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxvQkFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQW5DUjtBQXFDUTtFQUNFLGNBQUE7QUFuQ1Y7O0FBMkNBO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7QUF4Q0Y7QUEwQ0U7RUFDRSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsaUJBQUE7RUFDQSx5RUFDRTtFQUVGLHFDQUFBO0VBQ0Esa0JBQUE7QUExQ0o7QUE0Q0k7RUFDRSwyQkFBQTtFQUNBLGdGQUNFO0FBM0NSO0FBK0NJO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtBQTdDTjtBQStDTTtFQUNFLFdBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkFBQTtBQTdDUjtBQWdETTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLG9DQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxVQUFBO0VBQ0EseUJBQUE7QUE5Q1I7QUFnRFE7RUFDRSxZQUFBO0VBQ0EsZUFBQTtBQTlDVjtBQWtETTtFQUNFLFVBQUE7QUFoRFI7QUFtRE07RUFDRSxzQkFBQTtBQWpEUjtBQXFESTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtBQW5ETjtBQXFETTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQW5EUjtBQXNETTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FBcERSO0FBdURNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQXJEUjtBQXVEUTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFyRFY7QUF3RFE7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLDZCQUFBO0FBdERWO0FBNERFO0VBOUdGO0lBK0dJLFNBQUE7RUF6REY7RUE0REk7SUFDRSxhQUFBO0VBMUROO0VBNkRJO0lBQ0UsYUFBQTtFQTNETjtFQTZETTtJQUNFLGVBQUE7RUEzRFI7RUE4RE07SUFDRSxlQUFBO0VBNURSO0FBQ0Y7O0FBbUVBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7QUFoRUY7QUFrRUU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxxQ0FBQTtFQUNBLDJDQUFBO0VBQ0EsaUJBQUE7QUFoRUo7QUFrRUk7RUFDRSxxQ0FBQTtFQUNBLDJCQUFBO0VBQ0EsK0NBQUE7QUFoRU47QUFtRUk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSwwQ0FBQTtFQUNBLHlCQUFBO0VBQ0EsY0FBQTtBQWpFTjtBQW9FSTtFQUNFLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxxREFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsNENBQUE7RUFDQSxVQUFBO0FBbEVOO0FBcUVJO0VBQ0UsT0FBQTtFQUNBLFlBQUE7QUFuRU47QUFxRU07RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUFuRVI7QUFzRU07RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQXBFUjtBQXVFTTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFyRVI7O0FBNEVBO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7QUF6RUY7QUEyRUU7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxpQkFBQTtFQUNBLHlFQUNFO0VBRUYscUNBQUE7QUEzRUo7QUE2RUk7RUFDRSxxQ0FBQTtFQUNBLDJCQUFBO0VBQ0EsZ0ZBQ0U7QUE1RVI7QUFnRkk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLDBDQUFBO0VBQ0EsMENBQUE7RUFDQSx5QkFBQTtBQTlFTjtBQWlGSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBL0VOO0FBa0ZJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLHFEQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBaEZOO0FBa0ZNO0VBQ0UsWUFBQTtFQUNBLGVBQUE7QUFoRlI7QUFvRkk7RUFDRSxPQUFBO0VBQ0Esa0JBQUE7QUFsRk47QUFvRk07RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFsRlI7QUFxRk07RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBbkZSO0FBdUZJO0VBQ0Usa0JBQUE7RUFDQSxRQUFBO0VBQ0EsVUFBQTtBQXJGTjtBQXVGTTtFQUNFLGNBQUE7RUFDQSxlQUFBO0FBckZSO0FBMEZNO0VBQ0UscURBQUE7RUFDQSxzQkFBQTtBQXhGUjtBQTJGTTtFQUNFLHFCQUFBO0VBQ0Esc0JBQUE7QUF6RlI7O0FBZ0dBO0VBQ0UsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLDZCQUFBO0FBN0ZGO0FBK0ZFO0VBQ0UsYUFBQTtFQUNBLGVBQUE7RUFDQSxRQUFBO0VBQ0EsbUJBQUE7QUE3Rko7QUErRkk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLHFCQUFBO0FBN0ZOO0FBK0ZNO0VBQ0UsY0FBQTtBQTdGUjtBQWtHRTtFQUNFLGVBQUE7RUFDQSxjQUFBO0FBaEdKIiwic291cmNlc0NvbnRlbnQiOlsiLmhvbWUtc2lkZWJhciB7XG4gIHdpZHRoOiAzNDBweDtcbiAgcGFkZGluZzogMDtcbiAgcG9zaXRpb246IHN0aWNreTtcbiAgdG9wOiA4NHB4O1xuICBoZWlnaHQ6IGZpdC1jb250ZW50O1xuICBtYXgtaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTAwcHgpO1xuICBvdmVyZmxvdy15OiBhdXRvO1xuICBvdmVyZmxvdy14OiB2aXNpYmxlO1xuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAyMHB4O1xuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHtcbiAgICB3aWR0aDogMzAwcHg7XG4gICAgZ2FwOiAxNnB4O1xuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDEwMjRweCkge1xuICAgIGRpc3BsYXk6IG5vbmU7XG4gIH1cblxuICAvLyBDdXN0b20gc2Nyb2xsYmFyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcbiAgICB3aWR0aDogNnB4O1xuICB9XG5cbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4wMik7XG4gICAgYm9yZGVyLXJhZGl1czogM3B4O1xuICB9XG5cbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICAgIGJvcmRlci1yYWRpdXM6IDNweDtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzVhNjdkOCwgIzZiNDZjMSk7XG4gICAgfVxuICB9XG5cbiAgLy8gRW5zdXJlIHByb3BlciBzcGFjaW5nIGFuZCBwcmV2ZW50IGNvbnRlbnQgaGlkaW5nXG4gID4gKiB7XG4gICAgZmxleC1zaHJpbms6IDA7XG4gIH1cbn1cblxuLy8gUHJvZmlsZSBDYXJkXG4ucHJvZmlsZS1jYXJkIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMjBweDtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjA1KSwgcmdiYSgxMTgsIDc1LCAxNjIsIDAuMDUpKTtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpO1xuICBnYXA6IDEycHg7XG5cbiAgLnByb2ZpbGUtYXZhdGFyLWNvbnRhaW5lciB7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICAgLnByb2ZpbGUtYXZhdGFyIHtcbiAgICAgIHdpZHRoOiA2MHB4O1xuICAgICAgaGVpZ2h0OiA2MHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgICBib3JkZXI6IDNweCBzb2xpZCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMik7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNjY3ZWVhO1xuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5wcm9maWxlLWluZm8ge1xuICAgIGZsZXg6IDE7XG4gICAgbWluLXdpZHRoOiAwO1xuXG4gICAgLnByb2ZpbGUtdXNlcm5hbWUge1xuICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgIGNvbG9yOiAjMmQzNzQ4O1xuICAgICAgbWFyZ2luOiAwIDAgNHB4IDA7XG4gICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICAgIH1cblxuICAgIC5wcm9maWxlLW5hbWUge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgY29sb3I6ICM3MTgwOTY7XG4gICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICAgIH1cbiAgfVxuXG4gIC5zd2l0Y2gtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY29sb3I6IHdoaXRlO1xuICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDZweDtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1YTY3ZDgsICM2YjQ2YzEpO1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMyk7XG4gICAgfVxuXG4gICAgaSB7XG4gICAgICBmb250LXNpemU6IDEwcHg7XG4gICAgfVxuICB9XG59XG5cbi8vIFNlY3Rpb24gU3R5bGVzXG4uc2lkZWJhci1zZWN0aW9uIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJveC1zaGFkb3c6XG4gICAgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMDQpLFxuICAgIDAgMXB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMDIpO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDQpO1xuICBvdmVyZmxvdzogdmlzaWJsZTtcbiAgd2lkdGg6IDEwMCU7XG4gIGZsZXgtc2hyaW5rOiAwO1xuXG4gIC5zZWN0aW9uLWhlYWRlciB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIGZsZXgtd3JhcDogd3JhcDtcbiAgICBnYXA6IDhweDtcblxuICAgIGgzIHtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICBjb2xvcjogIzJkMzc0ODtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxMHB4O1xuICAgICAgZmxleDogMTtcbiAgICAgIG1pbi13aWR0aDogMDtcblxuICAgICAgaSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgfVxuICAgIH1cblxuICAgIC52aWV3LWFsbC1idG4sIC5zZWUtYWxsLWJ0biB7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDZweDtcbiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICBmbGV4LXNocmluazogMDtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1YTY3ZDgsICM2YjQ2YzEpO1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpO1xuICAgICAgfVxuXG4gICAgICBpIHtcbiAgICAgICAgZm9udC1zaXplOiAxMHB4O1xuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xuICAgICAgfVxuXG4gICAgICAmOmhvdmVyIGkge1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMnB4KTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBDb21wb25lbnQgd3JhcHBlciB0byBlbnN1cmUgcHJvcGVyIHNwYWNpbmdcbiAgLmNvbXBvbmVudC13cmFwcGVyIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBvdmVyZmxvdzogdmlzaWJsZTtcbiAgfVxufVxuXG4vLyBDb2xsZWN0aW9uIEl0ZW1zXG4uY29sbGVjdGlvbi1pdGVtcyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMTZweDtcbiAgd2lkdGg6IDEwMCU7XG5cbiAgLmNvbGxlY3Rpb24taXRlbSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogMTRweDtcbiAgICBwYWRkaW5nOiAxMnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgYmFja2dyb3VuZDogcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjAyKTtcbiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMDgpO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICBvdmVyZmxvdzogdmlzaWJsZTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjA1KTtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpO1xuICAgIH1cblxuICAgIC5jb2xsZWN0aW9uLWltYWdlLWNvbnRhaW5lciB7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICBmbGV4LXNocmluazogMDtcblxuICAgICAgLmNvbGxlY3Rpb24taW1hZ2Uge1xuICAgICAgICB3aWR0aDogNTZweDtcbiAgICAgICAgaGVpZ2h0OiA1NnB4O1xuICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpO1xuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgfVxuXG4gICAgICAuY29sbGVjdGlvbi1vdmVybGF5IHtcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICB0b3A6IDA7XG4gICAgICAgIGxlZnQ6IDA7XG4gICAgICAgIHJpZ2h0OiAwO1xuICAgICAgICBib3R0b206IDA7XG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC44KTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgIG9wYWNpdHk6IDA7XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgICAgaSB7XG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAmOmhvdmVyIC5jb2xsZWN0aW9uLW92ZXJsYXkge1xuICAgICAgICBvcGFjaXR5OiAxO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5jb2xsZWN0aW9uLWluZm8ge1xuICAgICAgZmxleDogMTtcbiAgICAgIG1pbi13aWR0aDogMDtcblxuICAgICAgLmNvbGxlY3Rpb24tbmFtZSB7XG4gICAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgICBmb250LXNpemU6IDE1cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGNvbG9yOiAjMmQzNzQ4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICAgICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgICAgfVxuXG4gICAgICAuY29sbGVjdGlvbi1wcmljZSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcbiAgICAgIH1cblxuICAgICAgLmNvbGxlY3Rpb24tZGlzY291bnQge1xuICAgICAgICBmb250LXNpemU6IDExcHg7XG4gICAgICAgIGNvbG9yOiAjZTUzZTNlO1xuICAgICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDIyOSwgNjIsIDYyLCAwLjEpO1xuICAgICAgICBwYWRkaW5nOiAycHggNnB4O1xuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBnYXA6IDRweDtcblxuICAgICAgICBpIHtcbiAgICAgICAgICBmb250LXNpemU6IDlweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBGZWF0dXJlZCBHcmlkXG4uZmVhdHVyZWQtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjtcbiAgZ2FwOiAxNnB4O1xuICB3aWR0aDogMTAwJTtcblxuICAuZmVhdHVyZWQtaXRlbSB7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgIGJveC1zaGFkb3c6XG4gICAgICAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4wNCksXG4gICAgICAwIDFweCA0cHggcmdiYSgwLCAwLCAwLCAwLjAyKTtcbiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDQpO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICY6aG92ZXIge1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xuICAgICAgYm94LXNoYWRvdzpcbiAgICAgICAgMCA4cHggMzJweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMTUpLFxuICAgICAgICAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gICAgfVxuXG4gICAgLmZlYXR1cmVkLWltYWdlLWNvbnRhaW5lciB7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gICAgICAuZmVhdHVyZWQtaW1hZ2Uge1xuICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgaGVpZ2h0OiAxMjBweDtcbiAgICAgICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICB9XG5cbiAgICAgIC5mZWF0dXJlZC1vdmVybGF5IHtcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICB0b3A6IDA7XG4gICAgICAgIGxlZnQ6IDA7XG4gICAgICAgIHJpZ2h0OiAwO1xuICAgICAgICBib3R0b206IDA7XG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC44KTtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgIG9wYWNpdHk6IDA7XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgICAgaSB7XG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAmOmhvdmVyIC5mZWF0dXJlZC1vdmVybGF5IHtcbiAgICAgICAgb3BhY2l0eTogMTtcbiAgICAgIH1cblxuICAgICAgJjpob3ZlciAuZmVhdHVyZWQtaW1hZ2Uge1xuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5mZWF0dXJlZC1pbmZvIHtcbiAgICAgIHBhZGRpbmc6IDEycHg7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcblxuICAgICAgLmZlYXR1cmVkLW5hbWUge1xuICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgZm9udC1zaXplOiAxM3B4O1xuICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICBjb2xvcjogIzJkMzc0ODtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcbiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICAgIH1cblxuICAgICAgLmZlYXR1cmVkLWJyYW5kIHtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgIGZvbnQtc2l6ZTogMTFweDtcbiAgICAgICAgY29sb3I6ICM3MTgwOTY7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDZweDtcbiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG4gICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICB9XG5cbiAgICAgIC5mZWF0dXJlZC1wcmljZSB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGdhcDogNnB4O1xuXG4gICAgICAgIC5jdXJyZW50LXByaWNlIHtcbiAgICAgICAgICBmb250LXNpemU6IDEzcHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgICAgICBjb2xvcjogIzY2N2VlYTtcbiAgICAgICAgfVxuXG4gICAgICAgIC5vcmlnaW5hbC1wcmljZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAxMXB4O1xuICAgICAgICAgIGNvbG9yOiAjYTBhZWMwO1xuICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogbGluZS10aHJvdWdoO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkge1xuICAgIGdhcDogMTJweDtcblxuICAgIC5mZWF0dXJlZC1pdGVtIHtcbiAgICAgIC5mZWF0dXJlZC1pbWFnZS1jb250YWluZXIgLmZlYXR1cmVkLWltYWdlIHtcbiAgICAgICAgaGVpZ2h0OiAxMDBweDtcbiAgICAgIH1cblxuICAgICAgLmZlYXR1cmVkLWluZm8ge1xuICAgICAgICBwYWRkaW5nOiAxMHB4O1xuXG4gICAgICAgIC5mZWF0dXJlZC1uYW1lIHtcbiAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgIH1cblxuICAgICAgICAuZmVhdHVyZWQtcHJpY2UgLmN1cnJlbnQtcHJpY2Uge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBBcnJpdmFscyBMaXN0XG4uYXJyaXZhbHMtbGlzdCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMTZweDtcbiAgd2lkdGg6IDEwMCU7XG5cbiAgLmFycml2YWwtaXRlbSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogMTRweDtcbiAgICBwYWRkaW5nOiAxMnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4wMik7XG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjA4KTtcbiAgICBvdmVyZmxvdzogdmlzaWJsZTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjA1KTtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpO1xuICAgIH1cblxuICAgIC5hcnJpdmFsLWltYWdlIHtcbiAgICAgIHdpZHRoOiA1NnB4O1xuICAgICAgaGVpZ2h0OiA1NnB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpO1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgIH1cblxuICAgIC5uZXctYmFkZ2Uge1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgdG9wOiA4cHg7XG4gICAgICBsZWZ0OiA4cHg7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZTUzZTNlLCAjYzUzMDMwKTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGZvbnQtc2l6ZTogOXB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgIHBhZGRpbmc6IDNweCA2cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgyMjksIDYyLCA2MiwgMC4zKTtcbiAgICAgIHotaW5kZXg6IDE7XG4gICAgfVxuXG4gICAgLmFycml2YWwtaW5mbyB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgbWluLXdpZHRoOiAwO1xuXG4gICAgICAuYXJyaXZhbC1uYW1lIHtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgIGZvbnQtc2l6ZTogMTVweDtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgY29sb3I6ICMyZDM3NDg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcbiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG4gICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICB9XG5cbiAgICAgIC5hcnJpdmFsLWJyYW5kIHtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgY29sb3I6ICM3MTgwOTY7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcbiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG4gICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICB9XG5cbiAgICAgIC5hcnJpdmFsLXByaWNlIHtcbiAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgICBjb2xvcjogIzY2N2VlYTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gQ2F0ZWdvcmllcyBHcmlkXG4uY2F0ZWdvcmllcy1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgMWZyO1xuICBnYXA6IDE2cHg7XG4gIHdpZHRoOiAxMDAlO1xuXG4gIC5jYXRlZ29yeS1pdGVtIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBwYWRkaW5nOiAxNnB4IDEycHg7XG4gICAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICBib3gtc2hhZG93OlxuICAgICAgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMDQpLFxuICAgICAgMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wMik7XG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjA0KTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjAyKTtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNHB4KTtcbiAgICAgIGJveC1zaGFkb3c6XG4gICAgICAgIDAgOHB4IDMycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjE1KSxcbiAgICAgICAgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xuICAgIH1cblxuICAgIC5jYXRlZ29yeS1pbWFnZSB7XG4gICAgICB3aWR0aDogNjRweDtcbiAgICAgIGhlaWdodDogNjRweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDtcbiAgICAgIGJvcmRlcjogM3B4IHNvbGlkIHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgfVxuXG4gICAgLmNhdGVnb3J5LW5hbWUge1xuICAgICAgZm9udC1zaXplOiAxM3B4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGNvbG9yOiAjMmQzNzQ4O1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICB9XG5cbiAgICAuY2F0ZWdvcnktaWNvbiB7XG4gICAgICB3aWR0aDogMzZweDtcbiAgICAgIGhlaWdodDogMzZweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgIGkge1xuICAgICAgICBjb2xvcjogd2hpdGU7XG4gICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuY2F0ZWdvcnktaW5mbyB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuXG4gICAgICAuY2F0ZWdvcnktbmFtZSB7XG4gICAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgICBmb250LXNpemU6IDEzcHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGNvbG9yOiAjMmQzNzQ4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICB9XG5cbiAgICAgIC5jYXRlZ29yeS1jb3VudCB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTFweDtcbiAgICAgICAgY29sb3I6ICM3MTgwOTY7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLnRyZW5kaW5nLWluZGljYXRvciB7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDhweDtcbiAgICAgIHJpZ2h0OiA4cHg7XG5cbiAgICAgIGkge1xuICAgICAgICBjb2xvcjogI2U1M2UzZTtcbiAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgfVxuICAgIH1cblxuICAgICY6aG92ZXIge1xuICAgICAgLmNhdGVnb3J5LWljb24ge1xuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNWE2N2Q4LCAjNmI0NmMxKTtcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcbiAgICAgIH1cblxuICAgICAgLmNhdGVnb3J5LWltYWdlIHtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNjY3ZWVhO1xuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBGb290ZXJcbi5zaWRlYmFyLWZvb3RlciB7XG4gIG1hcmdpbi10b3A6IDMycHg7XG4gIHBhZGRpbmctdG9wOiAxNnB4O1xuICBib3JkZXItdG9wOiAxcHggc29saWQgI2VmZWZlZjtcblxuICAuZm9vdGVyLWxpbmtzIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtd3JhcDogd3JhcDtcbiAgICBnYXA6IDhweDtcbiAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuXG4gICAgYSB7XG4gICAgICBmb250LXNpemU6IDExcHg7XG4gICAgICBjb2xvcjogI2M3YzdjNztcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5jb3B5cmlnaHQge1xuICAgIGZvbnQtc2l6ZTogMTFweDtcbiAgICBjb2xvcjogI2M3YzdjNztcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SuggestedProductsComponent", "TrendingProductsComponent", "TopInfluencersComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "currentUser", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "fullName", "ɵɵtextInterpolate1", "getDiscountPercentage", "item_r4", "ɵɵlistener", "SidebarComponent_section_2_div_10_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onProductClick", "ɵɵtemplate", "SidebarComponent_section_2_div_10_div_10_Template", "images", "url", "name", "formatPrice", "price", "SidebarComponent_section_2_Template_button_click_5_listener", "_r2", "viewSummerCollection", "SidebarComponent_section_2_div_10_Template", "summerCollection", "slice", "trackByProductId", "product_r7", "originalPrice", "SidebarComponent_section_14_div_10_Template_div_click_0_listener", "_r6", "SidebarComponent_section_14_div_10_span_13_Template", "brand", "SidebarComponent_section_14_Template_button_click_5_listener", "_r5", "viewFeaturedProducts", "SidebarComponent_section_14_div_10_Template", "featuredProducts", "SidebarComponent_section_26_div_10_Template_div_click_0_listener", "product_r10", "_r9", "SidebarComponent_section_26_Template_button_click_5_listener", "_r8", "viewNewArrivals", "SidebarComponent_section_26_div_10_Template", "newArrivals", "SidebarComponent_div_43_Template_div_click_0_listener", "category_r12", "_r11", "onCategoryClick", "image", "SidebarComponent", "constructor", "router", "trendingProducts", "categories", "ngOnInit", "console", "log", "length", "product", "navigate", "_id", "id", "category", "queryParams", "slug", "toLowerCase", "onUserClick", "user", "followUser", "viewAllCategories", "collection", "filter", "Intl", "NumberFormat", "style", "currency", "format", "formatNumber", "num", "toFixed", "toString", "Math", "round", "discount", "index", "trackByCategoryId", "getDisplayCategories", "ɵɵdirectiveInject", "i1", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_1_Template", "SidebarComponent_section_2_Template", "SidebarComponent_section_14_Template", "SidebarComponent_section_26_Template", "SidebarComponent_Template_button_click_40_listener", "SidebarComponent_div_43_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\n\n// Import existing sidebar components\nimport { SuggestedProductsComponent } from '../suggested-products/suggested-products.component';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { TopInfluencersComponent } from '../top-influencers/top-influencers.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule, \n    RouterModule, \n    SuggestedProductsComponent, \n    TrendingProductsComponent, \n    TopInfluencersComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  // Data passed from home component\n  @Input() currentUser: any = null;\n  @Input() featuredProducts: any[] = [];\n  @Input() trendingProducts: any[] = [];\n  @Input() newArrivals: any[] = [];\n  @Input() summerCollection: any[] = [];\n  @Input() categories: any[] = [];\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    console.log('✅ Sidebar component initialized with data:', {\n      featuredProducts: this.featuredProducts.length,\n      trendingProducts: this.trendingProducts.length,\n      newArrivals: this.newArrivals.length,\n      summerCollection: this.summerCollection.length,\n      categories: this.categories.length\n    });\n  }\n\n  // Navigation methods\n  onProductClick(product: any) {\n    console.log('🛍️ Navigate to product:', product.name);\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n\n  onCategoryClick(category: any) {\n    console.log('📂 Navigate to category:', category.name);\n    this.router.navigate(['/shop'], {\n      queryParams: { category: category.slug || category.name.toLowerCase() }\n    });\n  }\n\n  onUserClick(user: any) {\n    console.log('👤 Navigate to user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  followUser(user: any) {\n    console.log('➕ Follow user:', user.username);\n    // TODO: Implement follow functionality\n  }\n\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });\n  }\n\n  viewFeaturedProducts() {\n    this.router.navigate(['/shop'], { queryParams: { filter: 'featured' } });\n  }\n\n  viewNewArrivals() {\n    this.router.navigate(['/shop'], { queryParams: { filter: 'new' } });\n  }\n\n  // Utility methods\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  getDiscountPercentage(product: any): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return product.discount || 0;\n  }\n\n  // Track by functions for performance\n  trackByProductId(index: number, product: any): string {\n    return product._id || product.id;\n  }\n\n  trackByCategoryId(index: number, category: any): string {\n    return category._id || category.id;\n  }\n\n  // Get categories to display (real data or fallback)\n  getDisplayCategories() {\n    if (this.categories && this.categories.length > 0) {\n      return this.categories.slice(0, 4);\n    }\n\n    // Fallback categories with images\n    return [\n      {\n        _id: 'women',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        _id: 'men',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        _id: 'children',\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        _id: 'ethnic',\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n}\n", "<div class=\"home-sidebar\">\n  <!-- User Profile Card -->\n  <div class=\"profile-card\" *ngIf=\"currentUser\">\n    <div class=\"profile-avatar-container\">\n      <img [src]=\"currentUser.avatar || '/assets/images/default-avatar.jpg'\"\n           [alt]=\"currentUser.username\" class=\"profile-avatar\">\n    </div>\n    <div class=\"profile-info\">\n      <h4 class=\"profile-username\">{{ currentUser.username }}</h4>\n      <span class=\"profile-name\">{{ currentUser.fullName }}</span>\n    </div>\n    <button class=\"switch-btn\" routerLink=\"/profile\">\n      <i class=\"fas fa-user-cog\"></i>\n      <span>Profile</span>\n    </button>\n  </div>\n\n  <!-- Summer Collection 2024 -->\n  <section class=\"sidebar-section summer-collection\" *ngIf=\"summerCollection.length > 0\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-sun\"></i> Summer Collection 2024</h3>\n      <button class=\"view-all-btn\" (click)=\"viewSummerCollection()\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"collection-items\">\n      <div *ngFor=\"let item of summerCollection.slice(0, 3); trackBy: trackByProductId\"\n           class=\"collection-item\" (click)=\"onProductClick(item)\">\n        <div class=\"collection-image-container\">\n          <img [src]=\"item.images?.[0]?.url || '/assets/images/product-placeholder.jpg'\"\n               [alt]=\"item.name\" class=\"collection-image\">\n          <div class=\"collection-overlay\">\n            <i class=\"fas fa-eye\"></i>\n          </div>\n        </div>\n        <div class=\"collection-info\">\n          <span class=\"collection-name\">{{ item.name }}</span>\n          <span class=\"collection-price\">{{ formatPrice(item.price) }}</span>\n          <div class=\"collection-discount\" *ngIf=\"getDiscountPercentage(item) > 0\">\n            <i class=\"fas fa-tag\"></i>\n            {{ getDiscountPercentage(item) }}% OFF\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n\n  <!-- Suggested Products Component -->\n  <section class=\"sidebar-section suggested-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-magic\"></i> Suggested for You</h3>\n      <button class=\"view-all-btn\" routerLink=\"/shop?filter=suggested\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"component-wrapper\">\n      <app-suggested-products></app-suggested-products>\n    </div>\n  </section>\n\n  <!-- Featured Products -->\n  <section class=\"sidebar-section featured-section\" *ngIf=\"featuredProducts.length > 0\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-star\"></i> Featured Products</h3>\n      <button class=\"view-all-btn\" (click)=\"viewFeaturedProducts()\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"featured-grid\">\n      <div *ngFor=\"let product of featuredProducts.slice(0, 4); trackBy: trackByProductId\"\n           class=\"featured-item\" (click)=\"onProductClick(product)\">\n        <div class=\"featured-image-container\">\n          <img [src]=\"product.images?.[0]?.url || '/assets/images/product-placeholder.jpg'\"\n               [alt]=\"product.name\" class=\"featured-image\">\n          <div class=\"featured-overlay\">\n            <i class=\"fas fa-shopping-bag\"></i>\n          </div>\n        </div>\n        <div class=\"featured-info\">\n          <span class=\"featured-name\">{{ product.name }}</span>\n          <span class=\"featured-brand\">{{ product.brand }}</span>\n          <div class=\"featured-price\">\n            <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n            <span class=\"original-price\" *ngIf=\"product.originalPrice\">\n              {{ formatPrice(product.originalPrice) }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n\n  <!-- Trending Products Component -->\n  <section class=\"sidebar-section trending-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-fire\"></i> Trending Now</h3>\n      <button class=\"view-all-btn\" routerLink=\"/shop?filter=trending\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"component-wrapper\">\n      <app-trending-products></app-trending-products>\n    </div>\n  </section>\n\n  <!-- New Arrivals -->\n  <section class=\"sidebar-section arrivals-section\" *ngIf=\"newArrivals.length > 0\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-sparkles\"></i> New Arrivals</h3>\n      <button class=\"view-all-btn\" (click)=\"viewNewArrivals()\">\n        <span>View All</span>\n        <i class=\"fas fa-arrow-right\"></i>\n      </button>\n    </div>\n    <div class=\"arrivals-list\">\n      <div *ngFor=\"let product of newArrivals.slice(0, 4); trackBy: trackByProductId\" \n           class=\"arrival-item\" (click)=\"onProductClick(product)\">\n        <img [src]=\"product.images?.[0]?.url || '/assets/images/product-placeholder.jpg'\" \n             [alt]=\"product.name\" class=\"arrival-image\">\n        <div class=\"new-badge\">New</div>\n        <div class=\"arrival-info\">\n          <span class=\"arrival-name\">{{ product.name }}</span>\n          <span class=\"arrival-brand\">{{ product.brand }}</span>\n          <span class=\"arrival-price\">{{ formatPrice(product.price) }}</span>\n        </div>\n      </div>\n    </div>\n  </section>\n\n  <!-- Top Influencers Component -->\n  <section class=\"sidebar-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-crown\"></i> Top Influencers</h3>\n      <button class=\"see-all-btn\" routerLink=\"/influencers\">View All</button>\n    </div>\n    <app-top-influencers></app-top-influencers>\n  </section>\n\n  <!-- Categories -->\n  <section class=\"sidebar-section\">\n    <div class=\"section-header\">\n      <h3><i class=\"fas fa-th-large\"></i> Shop by Category</h3>\n      <button class=\"see-all-btn\" (click)=\"viewAllCategories()\">View All</button>\n    </div>\n    <div class=\"categories-grid\">\n      <div *ngFor=\"let category of getDisplayCategories(); trackBy: trackByCategoryId\"\n           class=\"category-item\" (click)=\"onCategoryClick(category)\">\n        <img [src]=\"category.image\" [alt]=\"category.name\" class=\"category-image\">\n        <span class=\"category-name\">{{ category.name }}</span>\n      </div>\n    </div>\n  </section>\n\n  <!-- Footer Links -->\n  <div class=\"sidebar-footer\">\n    <div class=\"footer-links\">\n      <a href=\"#\" routerLink=\"/about\">About</a>\n      <a href=\"#\" routerLink=\"/help\">Help</a>\n      <a href=\"#\" routerLink=\"/press\">Press</a>\n      <a href=\"#\" routerLink=\"/api\">API</a>\n      <a href=\"#\" routerLink=\"/jobs\">Jobs</a>\n      <a href=\"#\" routerLink=\"/privacy\">Privacy</a>\n      <a href=\"#\" routerLink=\"/terms\">Terms</a>\n    </div>\n    <div class=\"copyright\">\n      <span>© 2024 DFashion</span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAEtD;AACA,SAASC,0BAA0B,QAAQ,oDAAoD;AAC/F,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;;;;;;ICJlFC,EADF,CAAAC,cAAA,cAA8C,cACN;IACpCD,EAAA,CAAAE,SAAA,cACyD;IAC3DF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,aACK;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IACvDJ,EADuD,CAAAG,YAAA,EAAO,EACxD;IACNH,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAEjBJ,EAFiB,CAAAG,YAAA,EAAO,EACb,EACL;;;;IAXGH,EAAA,CAAAK,SAAA,GAAiE;IACjEL,EADA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,WAAA,CAAAC,MAAA,yCAAAT,EAAA,CAAAU,aAAA,CAAiE,QAAAH,MAAA,CAAAC,WAAA,CAAAG,QAAA,CACrC;IAGJX,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAC,WAAA,CAAAG,QAAA,CAA0B;IAC5BX,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAC,WAAA,CAAAK,QAAA,CAA0B;;;;;IA8BjDb,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAE,SAAA,YAA0B;IAC1BF,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAc,kBAAA,MAAAP,MAAA,CAAAQ,qBAAA,CAAAC,OAAA,YACF;;;;;;IAfJhB,EAAA,CAAAC,cAAA,cAC4D;IAA/BD,EAAA,CAAAiB,UAAA,mBAAAC,gEAAA;MAAA,MAAAF,OAAA,GAAAhB,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAiB,cAAA,CAAAR,OAAA,CAAoB;IAAA,EAAC;IACzDhB,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAAE,SAAA,cACgD;IAChDF,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,SAAA,YAA0B;IAE9BF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAA6B,eACG;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAyB,UAAA,KAAAC,iDAAA,kBAAyE;IAK7E1B,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAdGH,EAAA,CAAAK,SAAA,GAAyE;IACzEL,EADA,CAAAM,UAAA,SAAAU,OAAA,CAAAW,MAAA,kBAAAX,OAAA,CAAAW,MAAA,qBAAAX,OAAA,CAAAW,MAAA,IAAAC,GAAA,+CAAA5B,EAAA,CAAAU,aAAA,CAAyE,QAAAM,OAAA,CAAAa,IAAA,CACxD;IAMQ7B,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAY,iBAAA,CAAAI,OAAA,CAAAa,IAAA,CAAe;IACd7B,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAd,OAAA,CAAAe,KAAA,EAA6B;IAC1B/B,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAQ,qBAAA,CAAAC,OAAA,MAAqC;;;;;;IAnB3EhB,EAFJ,CAAAC,cAAA,kBAAuF,aACzD,SACtB;IAAAD,EAAA,CAAAE,SAAA,YAA0B;IAACF,EAAA,CAAAI,MAAA,8BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,iBAA8D;IAAjCD,EAAA,CAAAiB,UAAA,mBAAAe,4DAAA;MAAAhC,EAAA,CAAAmB,aAAA,CAAAc,GAAA;MAAA,MAAA1B,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAA2B,oBAAA,EAAsB;IAAA,EAAC;IAC3DlC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAE,SAAA,WAAkC;IAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAyB,UAAA,KAAAU,0CAAA,mBAC4D;IAkBhEnC,EADE,CAAAG,YAAA,EAAM,EACE;;;;IAnBgBH,EAAA,CAAAK,SAAA,IAAiC;IAAAL,EAAjC,CAAAM,UAAA,YAAAC,MAAA,CAAA6B,gBAAA,CAAAC,KAAA,OAAiC,iBAAA9B,MAAA,CAAA+B,gBAAA,CAAyB;;;;;IA2D1EtC,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAc,kBAAA,MAAAP,MAAA,CAAAuB,WAAA,CAAAS,UAAA,CAAAC,aAAA,OACF;;;;;;IAhBNxC,EAAA,CAAAC,cAAA,cAC6D;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAwB,iEAAA;MAAA,MAAAF,UAAA,GAAAvC,EAAA,CAAAmB,aAAA,CAAAuB,GAAA,EAAArB,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAiB,cAAA,CAAAe,UAAA,CAAuB;IAAA,EAAC;IAC1DvC,EAAA,CAAAC,cAAA,cAAsC;IACpCD,EAAA,CAAAE,SAAA,cACiD;IACjDF,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,YAAmC;IAEvCF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAA2B,eACG;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrDH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErDH,EADF,CAAAC,cAAA,eAA4B,gBACE;IAAAD,EAAA,CAAAI,MAAA,IAAgC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAyB,UAAA,KAAAkB,mDAAA,mBAA2D;IAKjE3C,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAhBGH,EAAA,CAAAK,SAAA,GAA4E;IAC5EL,EADA,CAAAM,UAAA,SAAAiC,UAAA,CAAAZ,MAAA,kBAAAY,UAAA,CAAAZ,MAAA,qBAAAY,UAAA,CAAAZ,MAAA,IAAAC,GAAA,+CAAA5B,EAAA,CAAAU,aAAA,CAA4E,QAAA6B,UAAA,CAAAV,IAAA,CACxD;IAMG7B,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAY,iBAAA,CAAA2B,UAAA,CAAAV,IAAA,CAAkB;IACjB7B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,iBAAA,CAAA2B,UAAA,CAAAK,KAAA,CAAmB;IAElB5C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAS,UAAA,CAAAR,KAAA,EAAgC;IAC9B/B,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,UAAA,SAAAiC,UAAA,CAAAC,aAAA,CAA2B;;;;;;IArB/DxC,EAFJ,CAAAC,cAAA,kBAAsF,aACxD,SACtB;IAAAD,EAAA,CAAAE,SAAA,YAA2B;IAACF,EAAA,CAAAI,MAAA,yBAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,iBAA8D;IAAjCD,EAAA,CAAAiB,UAAA,mBAAA4B,6DAAA;MAAA7C,EAAA,CAAAmB,aAAA,CAAA2B,GAAA;MAAA,MAAAvC,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAwC,oBAAA,EAAsB;IAAA,EAAC;IAC3D/C,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAE,SAAA,WAAkC;IAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAyB,UAAA,KAAAuB,2CAAA,mBAC6D;IAoBjEhD,EADE,CAAAG,YAAA,EAAM,EACE;;;;IArBmBH,EAAA,CAAAK,SAAA,IAAiC;IAAAL,EAAjC,CAAAM,UAAA,YAAAC,MAAA,CAAA0C,gBAAA,CAAAZ,KAAA,OAAiC,iBAAA9B,MAAA,CAAA+B,gBAAA,CAAyB;;;;;;IA+CnFtC,EAAA,CAAAC,cAAA,cAC4D;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAiC,iEAAA;MAAA,MAAAC,WAAA,GAAAnD,EAAA,CAAAmB,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAiB,cAAA,CAAA2B,WAAA,CAAuB;IAAA,EAAC;IACzDnD,EAAA,CAAAE,SAAA,cACgD;IAChDF,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAI,MAAA,UAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE9BH,EADF,CAAAC,cAAA,cAA0B,eACG;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,IAAgC;IAEhEJ,EAFgE,CAAAG,YAAA,EAAO,EAC/D,EACF;;;;;IARCH,EAAA,CAAAK,SAAA,EAA4E;IAC5EL,EADA,CAAAM,UAAA,SAAA6C,WAAA,CAAAxB,MAAA,kBAAAwB,WAAA,CAAAxB,MAAA,qBAAAwB,WAAA,CAAAxB,MAAA,IAAAC,GAAA,+CAAA5B,EAAA,CAAAU,aAAA,CAA4E,QAAAyC,WAAA,CAAAtB,IAAA,CACxD;IAGI7B,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAY,iBAAA,CAAAuC,WAAA,CAAAtB,IAAA,CAAkB;IACjB7B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,iBAAA,CAAAuC,WAAA,CAAAP,KAAA,CAAmB;IACnB5C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAuB,WAAA,CAAAqB,WAAA,CAAApB,KAAA,EAAgC;;;;;;IAfhE/B,EAFJ,CAAAC,cAAA,kBAAiF,aACnD,SACtB;IAAAD,EAAA,CAAAE,SAAA,YAA+B;IAACF,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,iBAAyD;IAA5BD,EAAA,CAAAiB,UAAA,mBAAAoC,6DAAA;MAAArD,EAAA,CAAAmB,aAAA,CAAAmC,GAAA;MAAA,MAAA/C,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAgD,eAAA,EAAiB;IAAA,EAAC;IACtDvD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAE,SAAA,WAAkC;IAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAyB,UAAA,KAAA+B,2CAAA,mBAC4D;IAWhExD,EADE,CAAAG,YAAA,EAAM,EACE;;;;IAZmBH,EAAA,CAAAK,SAAA,IAA4B;IAAAL,EAA5B,CAAAM,UAAA,YAAAC,MAAA,CAAAkD,WAAA,CAAApB,KAAA,OAA4B,iBAAA9B,MAAA,CAAA+B,gBAAA,CAAyB;;;;;;IA8B9EtC,EAAA,CAAAC,cAAA,cAC+D;IAApCD,EAAA,CAAAiB,UAAA,mBAAAyC,sDAAA;MAAA,MAAAC,YAAA,GAAA3D,EAAA,CAAAmB,aAAA,CAAAyC,IAAA,EAAAvC,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAShB,MAAA,CAAAsD,eAAA,CAAAF,YAAA,CAAyB;IAAA,EAAC;IAC5D3D,EAAA,CAAAE,SAAA,cAAyE;IACzEF,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IACjDJ,EADiD,CAAAG,YAAA,EAAO,EAClD;;;;IAFCH,EAAA,CAAAK,SAAA,EAAsB;IAACL,EAAvB,CAAAM,UAAA,QAAAqD,YAAA,CAAAG,KAAA,EAAA9D,EAAA,CAAAU,aAAA,CAAsB,QAAAiD,YAAA,CAAA9B,IAAA,CAAsB;IACrB7B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,iBAAA,CAAA+C,YAAA,CAAA9B,IAAA,CAAmB;;;ADlIvD,OAAM,MAAOkC,gBAAgB;EAS3BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAR1B;IACS,KAAAzD,WAAW,GAAQ,IAAI;IACvB,KAAAyC,gBAAgB,GAAU,EAAE;IAC5B,KAAAiB,gBAAgB,GAAU,EAAE;IAC5B,KAAAT,WAAW,GAAU,EAAE;IACvB,KAAArB,gBAAgB,GAAU,EAAE;IAC5B,KAAA+B,UAAU,GAAU,EAAE;EAEM;EAErCC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;MACxDrB,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACsB,MAAM;MAC9CL,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACK,MAAM;MAC9Cd,WAAW,EAAE,IAAI,CAACA,WAAW,CAACc,MAAM;MACpCnC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACmC,MAAM;MAC9CJ,UAAU,EAAE,IAAI,CAACA,UAAU,CAACI;KAC7B,CAAC;EACJ;EAEA;EACA/C,cAAcA,CAACgD,OAAY;IACzBH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEE,OAAO,CAAC3C,IAAI,CAAC;IACrD,IAAI,CAACoC,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE,CAAC,CAAC;EAC/D;EAEAd,eAAeA,CAACe,QAAa;IAC3BP,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEM,QAAQ,CAAC/C,IAAI,CAAC;IACtD,IAAI,CAACoC,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BI,WAAW,EAAE;QAAED,QAAQ,EAAEA,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAAC/C,IAAI,CAACkD,WAAW;MAAE;KACtE,CAAC;EACJ;EAEAC,WAAWA,CAACC,IAAS;IACnBZ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,IAAI,CAACtE,QAAQ,CAAC;IAC1D,IAAI,CAACsD,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,EAAEQ,IAAI,CAACtE,QAAQ,CAAC,CAAC;EACnD;EAEAuE,UAAUA,CAACD,IAAS;IAClBZ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEW,IAAI,CAACtE,QAAQ,CAAC;IAC5C;EACF;EAEAwE,iBAAiBA,CAAA;IACf,IAAI,CAAClB,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEAvC,oBAAoBA,CAAA;IAClB,IAAI,CAAC+B,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEI,WAAW,EAAE;QAAEO,UAAU,EAAE;MAAY;IAAE,CAAE,CAAC;EAChF;EAEArC,oBAAoBA,CAAA;IAClB,IAAI,CAACkB,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEI,WAAW,EAAE;QAAEQ,MAAM,EAAE;MAAU;IAAE,CAAE,CAAC;EAC1E;EAEA9B,eAAeA,CAAA;IACb,IAAI,CAACU,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEI,WAAW,EAAE;QAAEQ,MAAM,EAAE;MAAK;IAAE,CAAE,CAAC;EACrE;EAEA;EACAvD,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIuD,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAC;EAClB;EAEA4D,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEA/E,qBAAqBA,CAACyD,OAAY;IAChC,IAAIA,OAAO,CAAChC,aAAa,IAAIgC,OAAO,CAAChC,aAAa,GAAGgC,OAAO,CAACzC,KAAK,EAAE;MAClE,OAAOgE,IAAI,CAACC,KAAK,CAAE,CAACxB,OAAO,CAAChC,aAAa,GAAGgC,OAAO,CAACzC,KAAK,IAAIyC,OAAO,CAAChC,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAOgC,OAAO,CAACyB,QAAQ,IAAI,CAAC;EAC9B;EAEA;EACA3D,gBAAgBA,CAAC4D,KAAa,EAAE1B,OAAY;IAC1C,OAAOA,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE;EAClC;EAEAwB,iBAAiBA,CAACD,KAAa,EAAEtB,QAAa;IAC5C,OAAOA,QAAQ,CAACF,GAAG,IAAIE,QAAQ,CAACD,EAAE;EACpC;EAEA;EACAyB,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACjC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACjD,OAAO,IAAI,CAACJ,UAAU,CAAC9B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGpC;IACA,OAAO,CACL;MACEqC,GAAG,EAAE,OAAO;MACZ7C,IAAI,EAAE,OAAO;MACbiD,IAAI,EAAE,OAAO;MACbhB,KAAK,EAAE;KACR,EACD;MACEY,GAAG,EAAE,KAAK;MACV7C,IAAI,EAAE,KAAK;MACXiD,IAAI,EAAE,KAAK;MACXhB,KAAK,EAAE;KACR,EACD;MACEY,GAAG,EAAE,UAAU;MACf7C,IAAI,EAAE,MAAM;MACZiD,IAAI,EAAE,UAAU;MAChBhB,KAAK,EAAE;KACR,EACD;MACEY,GAAG,EAAE,QAAQ;MACb7C,IAAI,EAAE,QAAQ;MACdiD,IAAI,EAAE,QAAQ;MACdhB,KAAK,EAAE;KACR,CACF;EACH;;;uBA9HWC,gBAAgB,EAAA/D,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBxC,gBAAgB;MAAAyC,SAAA;MAAAC,MAAA;QAAAjG,WAAA;QAAAyC,gBAAA;QAAAiB,gBAAA;QAAAT,WAAA;QAAArB,gBAAA;QAAA+B,UAAA;MAAA;MAAAuC,UAAA;MAAAC,QAAA,GAAA3G,EAAA,CAAA4G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB7BlH,EAAA,CAAAC,cAAA,aAA0B;UAkBxBD,EAhBA,CAAAyB,UAAA,IAAA2F,+BAAA,kBAA8C,IAAAC,mCAAA,sBAgByC;UAiCnFrH,EAFJ,CAAAC,cAAA,iBAAmD,aACrB,SACtB;UAAAD,EAAA,CAAAE,SAAA,WAA4B;UAACF,EAAA,CAAAI,MAAA,yBAAiB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAErDH,EADF,CAAAC,cAAA,gBAAiE,WACzD;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAE,SAAA,YAAkC;UAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;UACNH,EAAA,CAAAC,cAAA,cAA+B;UAC7BD,EAAA,CAAAE,SAAA,8BAAiD;UAErDF,EADE,CAAAG,YAAA,EAAM,EACE;UAGVH,EAAA,CAAAyB,UAAA,KAAA6F,oCAAA,sBAAsF;UAmClFtH,EAFJ,CAAAC,cAAA,mBAAkD,cACpB,UACtB;UAAAD,EAAA,CAAAE,SAAA,aAA2B;UAACF,EAAA,CAAAI,MAAA,qBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAE/CH,EADF,CAAAC,cAAA,kBAAgE,YACxD;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAE,SAAA,YAAkC;UAEtCF,EADE,CAAAG,YAAA,EAAS,EACL;UACNH,EAAA,CAAAC,cAAA,cAA+B;UAC7BD,EAAA,CAAAE,SAAA,6BAA+C;UAEnDF,EADE,CAAAG,YAAA,EAAM,EACE;UAGVH,EAAA,CAAAyB,UAAA,KAAA8F,oCAAA,uBAAiF;UA0B7EvH,EAFJ,CAAAC,cAAA,mBAAiC,cACH,UACtB;UAAAD,EAAA,CAAAE,SAAA,aAA4B;UAACF,EAAA,CAAAI,MAAA,wBAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,kBAAsD;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAChEJ,EADgE,CAAAG,YAAA,EAAS,EACnE;UACNH,EAAA,CAAAE,SAAA,2BAA2C;UAC7CF,EAAA,CAAAG,YAAA,EAAU;UAKNH,EAFJ,CAAAC,cAAA,mBAAiC,cACH,UACtB;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAACF,EAAA,CAAAI,MAAA,yBAAgB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,kBAA0D;UAA9BD,EAAA,CAAAiB,UAAA,mBAAAuG,mDAAA;YAAA,OAASL,GAAA,CAAAhC,iBAAA,EAAmB;UAAA,EAAC;UAACnF,EAAA,CAAAI,MAAA,gBAAQ;UACpEJ,EADoE,CAAAG,YAAA,EAAS,EACvE;UACNH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAyB,UAAA,KAAAgG,gCAAA,kBAC+D;UAKnEzH,EADE,CAAAG,YAAA,EAAM,EACE;UAKNH,EAFJ,CAAAC,cAAA,eAA4B,eACA,aACQ;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACzCH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACvCH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACzCH,EAAA,CAAAC,cAAA,aAA8B;UAAAD,EAAA,CAAAI,MAAA,WAAG;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACrCH,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACvCH,EAAA,CAAAC,cAAA,aAAkC;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAC7CH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,MAAA,aAAK;UACvCJ,EADuC,CAAAG,YAAA,EAAI,EACrC;UAEJH,EADF,CAAAC,cAAA,eAAuB,YACf;UAAAD,EAAA,CAAAI,MAAA,4BAAe;UAG3BJ,EAH2B,CAAAG,YAAA,EAAO,EACxB,EACF,EACF;;;UA1KuBH,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAA3G,WAAA,CAAiB;UAgBQR,EAAA,CAAAK,SAAA,EAAiC;UAAjCL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAA/E,gBAAA,CAAAmC,MAAA,KAAiC;UA6ClCvE,EAAA,CAAAK,SAAA,IAAiC;UAAjCL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAAlE,gBAAA,CAAAsB,MAAA,KAAiC;UA+CjCvE,EAAA,CAAAK,SAAA,IAA4B;UAA5BL,EAAA,CAAAM,UAAA,SAAA6G,GAAA,CAAA1D,WAAA,CAAAc,MAAA,KAA4B;UAuCjDvE,EAAA,CAAAK,SAAA,IAA2B;UAAAL,EAA3B,CAAAM,UAAA,YAAA6G,GAAA,CAAAf,oBAAA,GAA2B,iBAAAe,GAAA,CAAAhB,iBAAA,CAA0B;;;qBDxIjFxG,YAAY,EAAA+H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZhI,YAAY,EAAA0G,EAAA,CAAAuB,UAAA,EACZhI,0BAA0B,EAC1BC,yBAAyB,EACzBC,uBAAuB;MAAA+H,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}