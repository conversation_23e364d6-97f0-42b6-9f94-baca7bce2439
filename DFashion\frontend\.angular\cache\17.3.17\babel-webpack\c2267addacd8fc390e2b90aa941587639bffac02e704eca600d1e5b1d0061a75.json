{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EmailService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  sendBillEmail(formData) {\n    return this.http.post(`${this.apiUrl}/email/send-bill`, formData);\n  }\n  sendPasswordResetEmail(email) {\n    return this.http.post(`${this.apiUrl}/email/forgot-password`, {\n      email\n    });\n  }\n  sendOrderConfirmationEmail(orderData) {\n    const emailData = {\n      type: 'order_confirmation',\n      to: orderData.customerEmail,\n      subject: 'Order Confirmation - DFashion',\n      data: {\n        customerName: orderData.customerName,\n        orderId: orderData.orderId,\n        items: orderData.items,\n        total: orderData.total,\n        orderDate: orderData.orderDate\n      }\n    };\n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n  sendPaymentSuccessEmail(paymentData) {\n    const emailData = {\n      type: 'payment_success',\n      to: paymentData.customerEmail,\n      subject: 'Payment Successful - DFashion',\n      data: {\n        customerName: paymentData.customerName,\n        orderId: paymentData.orderId,\n        paymentId: paymentData.paymentId,\n        amount: paymentData.amount,\n        paymentMethod: paymentData.paymentMethod,\n        paymentDate: new Date()\n      }\n    };\n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n  sendPaymentFailedEmail(paymentData) {\n    const emailData = {\n      type: 'payment_failed',\n      to: paymentData.customerEmail,\n      subject: 'Payment Failed - DFashion',\n      data: {\n        customerName: paymentData.customerName,\n        orderId: paymentData.orderId,\n        amount: paymentData.amount,\n        failureReason: paymentData.failureReason || 'Payment processing failed',\n        retryLink: `${environment.frontendUrl}/checkout/${paymentData.orderId}`\n      }\n    };\n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n  sendOrderShippedEmail(orderData) {\n    const emailData = {\n      type: 'order_shipped',\n      to: orderData.customerEmail,\n      subject: 'Your Order has been Shipped - DFashion',\n      data: {\n        customerName: orderData.customerName,\n        orderId: orderData.orderId,\n        trackingNumber: orderData.trackingNumber,\n        estimatedDelivery: orderData.estimatedDelivery,\n        shippingAddress: orderData.shippingAddress\n      }\n    };\n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n  sendOrderDeliveredEmail(orderData) {\n    const emailData = {\n      type: 'order_delivered',\n      to: orderData.customerEmail,\n      subject: 'Order Delivered - DFashion',\n      data: {\n        customerName: orderData.customerName,\n        orderId: orderData.orderId,\n        deliveryDate: orderData.deliveryDate,\n        feedbackLink: `${environment.frontendUrl}/feedback/${orderData.orderId}`\n      }\n    };\n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n  // Email templates for different notification types\n  getEmailTemplate(type) {\n    const templates = {\n      order_confirmation: {\n        subject: 'Order Confirmation - DFashion',\n        template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">DFashion</h1>\n              <p style=\"color: white; margin: 5px 0;\">Order Confirmation</p>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>Thank you for your order! We're excited to confirm that we've received your order.</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Order Details:</h3>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Order Date:</strong> {{orderDate}}</p>\n                <p><strong>Total Amount:</strong> ₹{{total}}</p>\n              </div>\n              <p>We'll send you another email when your order ships.</p>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n      },\n      payment_success: {\n        subject: 'Payment Successful - DFashion',\n        template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Payment Successful!</h1>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>Your payment has been processed successfully!</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Payment Details:</h3>\n                <p><strong>Payment ID:</strong> {{paymentId}}</p>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Amount:</strong> ₹{{amount}}</p>\n                <p><strong>Payment Method:</strong> {{paymentMethod}}</p>\n              </div>\n              <p>Your order is now being processed and will be shipped soon.</p>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n      },\n      payment_failed: {\n        subject: 'Payment Failed - DFashion',\n        template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Payment Failed</h1>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>We're sorry, but your payment could not be processed.</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Order Details:</h3>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Amount:</strong> ₹{{amount}}</p>\n                <p><strong>Reason:</strong> {{failureReason}}</p>\n              </div>\n              <p>Please try again or contact our support team for assistance.</p>\n              <a href=\"{{retryLink}}\" style=\"background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;\">Retry Payment</a>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n      }\n    };\n    return templates[type] || null;\n  }\n  // Utility method to replace template variables\n  processTemplate(template, data) {\n    let processedTemplate = template;\n    Object.keys(data).forEach(key => {\n      const regex = new RegExp(`{{${key}}}`, 'g');\n      processedTemplate = processedTemplate.replace(regex, data[key]);\n    });\n    return processedTemplate;\n  }\n  static {\n    this.ɵfac = function EmailService_Factory(t) {\n      return new (t || EmailService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EmailService,\n      factory: EmailService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "EmailService", "constructor", "http", "apiUrl", "sendBillEmail", "formData", "post", "sendPasswordResetEmail", "email", "sendOrderConfirmationEmail", "orderData", "emailData", "type", "to", "customerEmail", "subject", "data", "customerName", "orderId", "items", "total", "orderDate", "sendPaymentSuccessEmail", "paymentData", "paymentId", "amount", "paymentMethod", "paymentDate", "Date", "sendPaymentFailedEmail", "failureReason", "retryLink", "frontendUrl", "sendOrderShippedEmail", "trackingNumber", "estimatedDelivery", "shippingAddress", "sendOrderDeliveredEmail", "deliveryDate", "feedbackLink", "getEmailTemplate", "templates", "order_confirmation", "template", "payment_success", "payment_failed", "processTemplate", "processedTemplate", "Object", "keys", "for<PERSON>ach", "key", "regex", "RegExp", "replace", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\email.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../../environments/environment';\n\nexport interface EmailNotification {\n  type: 'order_confirmation' | 'payment_success' | 'payment_failed' | 'order_shipped' | 'order_delivered' | 'password_reset';\n  to: string;\n  subject: string;\n  data: any;\n}\n\nexport interface BillEmailData {\n  userId: string;\n  name: string;\n  email: string;\n  message: string;\n  paymentType: {\n    method: string;\n    paymentId: string;\n    orderId: string;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class EmailService {\n  private apiUrl = environment.apiUrl;\n\n  constructor(private http: HttpClient) {}\n\n  sendBillEmail(formData: FormData): Observable<any> {\n    return this.http.post(`${this.apiUrl}/email/send-bill`, formData);\n  }\n\n  sendPasswordResetEmail(email: string): Observable<any> {\n    return this.http.post(`${this.apiUrl}/email/forgot-password`, { email });\n  }\n\n  sendOrderConfirmationEmail(orderData: any): Observable<any> {\n    const emailData = {\n      type: 'order_confirmation',\n      to: orderData.customerEmail,\n      subject: 'Order Confirmation - DFashion',\n      data: {\n        customerName: orderData.customerName,\n        orderId: orderData.orderId,\n        items: orderData.items,\n        total: orderData.total,\n        orderDate: orderData.orderDate\n      }\n    };\n    \n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n\n  sendPaymentSuccessEmail(paymentData: any): Observable<any> {\n    const emailData = {\n      type: 'payment_success',\n      to: paymentData.customerEmail,\n      subject: 'Payment Successful - DFashion',\n      data: {\n        customerName: paymentData.customerName,\n        orderId: paymentData.orderId,\n        paymentId: paymentData.paymentId,\n        amount: paymentData.amount,\n        paymentMethod: paymentData.paymentMethod,\n        paymentDate: new Date()\n      }\n    };\n    \n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n\n  sendPaymentFailedEmail(paymentData: any): Observable<any> {\n    const emailData = {\n      type: 'payment_failed',\n      to: paymentData.customerEmail,\n      subject: 'Payment Failed - DFashion',\n      data: {\n        customerName: paymentData.customerName,\n        orderId: paymentData.orderId,\n        amount: paymentData.amount,\n        failureReason: paymentData.failureReason || 'Payment processing failed',\n        retryLink: `${environment.frontendUrl}/checkout/${paymentData.orderId}`\n      }\n    };\n    \n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n\n  sendOrderShippedEmail(orderData: any): Observable<any> {\n    const emailData = {\n      type: 'order_shipped',\n      to: orderData.customerEmail,\n      subject: 'Your Order has been Shipped - DFashion',\n      data: {\n        customerName: orderData.customerName,\n        orderId: orderData.orderId,\n        trackingNumber: orderData.trackingNumber,\n        estimatedDelivery: orderData.estimatedDelivery,\n        shippingAddress: orderData.shippingAddress\n      }\n    };\n    \n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n\n  sendOrderDeliveredEmail(orderData: any): Observable<any> {\n    const emailData = {\n      type: 'order_delivered',\n      to: orderData.customerEmail,\n      subject: 'Order Delivered - DFashion',\n      data: {\n        customerName: orderData.customerName,\n        orderId: orderData.orderId,\n        deliveryDate: orderData.deliveryDate,\n        feedbackLink: `${environment.frontendUrl}/feedback/${orderData.orderId}`\n      }\n    };\n    \n    return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n  }\n\n  // Email templates for different notification types\n  getEmailTemplate(type: string): any {\n    const templates = {\n      order_confirmation: {\n        subject: 'Order Confirmation - DFashion',\n        template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">DFashion</h1>\n              <p style=\"color: white; margin: 5px 0;\">Order Confirmation</p>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>Thank you for your order! We're excited to confirm that we've received your order.</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Order Details:</h3>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Order Date:</strong> {{orderDate}}</p>\n                <p><strong>Total Amount:</strong> ₹{{total}}</p>\n              </div>\n              <p>We'll send you another email when your order ships.</p>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n      },\n      payment_success: {\n        subject: 'Payment Successful - DFashion',\n        template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Payment Successful!</h1>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>Your payment has been processed successfully!</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Payment Details:</h3>\n                <p><strong>Payment ID:</strong> {{paymentId}}</p>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Amount:</strong> ₹{{amount}}</p>\n                <p><strong>Payment Method:</strong> {{paymentMethod}}</p>\n              </div>\n              <p>Your order is now being processed and will be shipped soon.</p>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n      },\n      payment_failed: {\n        subject: 'Payment Failed - DFashion',\n        template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Payment Failed</h1>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>We're sorry, but your payment could not be processed.</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Order Details:</h3>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Amount:</strong> ₹{{amount}}</p>\n                <p><strong>Reason:</strong> {{failureReason}}</p>\n              </div>\n              <p>Please try again or contact our support team for assistance.</p>\n              <a href=\"{{retryLink}}\" style=\"background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;\">Retry Payment</a>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n      }\n    };\n    \n    return templates[type as keyof typeof templates] || null;\n  }\n\n  // Utility method to replace template variables\n  processTemplate(template: string, data: any): string {\n    let processedTemplate = template;\n    \n    Object.keys(data).forEach(key => {\n      const regex = new RegExp(`{{${key}}}`, 'g');\n      processedTemplate = processedTemplate.replace(regex, data[key]);\n    });\n    \n    return processedTemplate;\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAwB/D,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEI;EAEvCC,aAAaA,CAACC,QAAkB;IAC9B,OAAO,IAAI,CAACH,IAAI,CAACI,IAAI,CAAC,GAAG,IAAI,CAACH,MAAM,kBAAkB,EAAEE,QAAQ,CAAC;EACnE;EAEAE,sBAAsBA,CAACC,KAAa;IAClC,OAAO,IAAI,CAACN,IAAI,CAACI,IAAI,CAAC,GAAG,IAAI,CAACH,MAAM,wBAAwB,EAAE;MAAEK;IAAK,CAAE,CAAC;EAC1E;EAEAC,0BAA0BA,CAACC,SAAc;IACvC,MAAMC,SAAS,GAAG;MAChBC,IAAI,EAAE,oBAAoB;MAC1BC,EAAE,EAAEH,SAAS,CAACI,aAAa;MAC3BC,OAAO,EAAE,+BAA+B;MACxCC,IAAI,EAAE;QACJC,YAAY,EAAEP,SAAS,CAACO,YAAY;QACpCC,OAAO,EAAER,SAAS,CAACQ,OAAO;QAC1BC,KAAK,EAAET,SAAS,CAACS,KAAK;QACtBC,KAAK,EAAEV,SAAS,CAACU,KAAK;QACtBC,SAAS,EAAEX,SAAS,CAACW;;KAExB;IAED,OAAO,IAAI,CAACnB,IAAI,CAACI,IAAI,CAAC,GAAG,IAAI,CAACH,MAAM,0BAA0B,EAAEQ,SAAS,CAAC;EAC5E;EAEAW,uBAAuBA,CAACC,WAAgB;IACtC,MAAMZ,SAAS,GAAG;MAChBC,IAAI,EAAE,iBAAiB;MACvBC,EAAE,EAAEU,WAAW,CAACT,aAAa;MAC7BC,OAAO,EAAE,+BAA+B;MACxCC,IAAI,EAAE;QACJC,YAAY,EAAEM,WAAW,CAACN,YAAY;QACtCC,OAAO,EAAEK,WAAW,CAACL,OAAO;QAC5BM,SAAS,EAAED,WAAW,CAACC,SAAS;QAChCC,MAAM,EAAEF,WAAW,CAACE,MAAM;QAC1BC,aAAa,EAAEH,WAAW,CAACG,aAAa;QACxCC,WAAW,EAAE,IAAIC,IAAI;;KAExB;IAED,OAAO,IAAI,CAAC1B,IAAI,CAACI,IAAI,CAAC,GAAG,IAAI,CAACH,MAAM,0BAA0B,EAAEQ,SAAS,CAAC;EAC5E;EAEAkB,sBAAsBA,CAACN,WAAgB;IACrC,MAAMZ,SAAS,GAAG;MAChBC,IAAI,EAAE,gBAAgB;MACtBC,EAAE,EAAEU,WAAW,CAACT,aAAa;MAC7BC,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE;QACJC,YAAY,EAAEM,WAAW,CAACN,YAAY;QACtCC,OAAO,EAAEK,WAAW,CAACL,OAAO;QAC5BO,MAAM,EAAEF,WAAW,CAACE,MAAM;QAC1BK,aAAa,EAAEP,WAAW,CAACO,aAAa,IAAI,2BAA2B;QACvEC,SAAS,EAAE,GAAGhC,WAAW,CAACiC,WAAW,aAAaT,WAAW,CAACL,OAAO;;KAExE;IAED,OAAO,IAAI,CAAChB,IAAI,CAACI,IAAI,CAAC,GAAG,IAAI,CAACH,MAAM,0BAA0B,EAAEQ,SAAS,CAAC;EAC5E;EAEAsB,qBAAqBA,CAACvB,SAAc;IAClC,MAAMC,SAAS,GAAG;MAChBC,IAAI,EAAE,eAAe;MACrBC,EAAE,EAAEH,SAAS,CAACI,aAAa;MAC3BC,OAAO,EAAE,wCAAwC;MACjDC,IAAI,EAAE;QACJC,YAAY,EAAEP,SAAS,CAACO,YAAY;QACpCC,OAAO,EAAER,SAAS,CAACQ,OAAO;QAC1BgB,cAAc,EAAExB,SAAS,CAACwB,cAAc;QACxCC,iBAAiB,EAAEzB,SAAS,CAACyB,iBAAiB;QAC9CC,eAAe,EAAE1B,SAAS,CAAC0B;;KAE9B;IAED,OAAO,IAAI,CAAClC,IAAI,CAACI,IAAI,CAAC,GAAG,IAAI,CAACH,MAAM,0BAA0B,EAAEQ,SAAS,CAAC;EAC5E;EAEA0B,uBAAuBA,CAAC3B,SAAc;IACpC,MAAMC,SAAS,GAAG;MAChBC,IAAI,EAAE,iBAAiB;MACvBC,EAAE,EAAEH,SAAS,CAACI,aAAa;MAC3BC,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE;QACJC,YAAY,EAAEP,SAAS,CAACO,YAAY;QACpCC,OAAO,EAAER,SAAS,CAACQ,OAAO;QAC1BoB,YAAY,EAAE5B,SAAS,CAAC4B,YAAY;QACpCC,YAAY,EAAE,GAAGxC,WAAW,CAACiC,WAAW,aAAatB,SAAS,CAACQ,OAAO;;KAEzE;IAED,OAAO,IAAI,CAAChB,IAAI,CAACI,IAAI,CAAC,GAAG,IAAI,CAACH,MAAM,0BAA0B,EAAEQ,SAAS,CAAC;EAC5E;EAEA;EACA6B,gBAAgBA,CAAC5B,IAAY;IAC3B,MAAM6B,SAAS,GAAG;MAChBC,kBAAkB,EAAE;QAClB3B,OAAO,EAAE,+BAA+B;QACxC4B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;OAoBX;MACDC,eAAe,EAAE;QACf7B,OAAO,EAAE,+BAA+B;QACxC4B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;OAoBX;MACDE,cAAc,EAAE;QACd9B,OAAO,EAAE,2BAA2B;QACpC4B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;KAqBb;IAED,OAAOF,SAAS,CAAC7B,IAA8B,CAAC,IAAI,IAAI;EAC1D;EAEA;EACAkC,eAAeA,CAACH,QAAgB,EAAE3B,IAAS;IACzC,IAAI+B,iBAAiB,GAAGJ,QAAQ;IAEhCK,MAAM,CAACC,IAAI,CAACjC,IAAI,CAAC,CAACkC,OAAO,CAACC,GAAG,IAAG;MAC9B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,KAAKF,GAAG,IAAI,EAAE,GAAG,CAAC;MAC3CJ,iBAAiB,GAAGA,iBAAiB,CAACO,OAAO,CAACF,KAAK,EAAEpC,IAAI,CAACmC,GAAG,CAAC,CAAC;IACjE,CAAC,CAAC;IAEF,OAAOJ,iBAAiB;EAC1B;;;uBAzLW/C,YAAY,EAAAuD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAZ1D,YAAY;MAAA2D,OAAA,EAAZ3D,YAAY,CAAA4D,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}