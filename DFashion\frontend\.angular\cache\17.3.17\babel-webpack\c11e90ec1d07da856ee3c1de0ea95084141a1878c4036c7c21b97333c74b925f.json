{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction EcommerceHubComponent_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.wishlistCount);\n  }\n}\nfunction EcommerceHubComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cartCount);\n  }\n}\nfunction EcommerceHubComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.notificationCount);\n  }\n}\nfunction EcommerceHubComponent_div_32_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_div_32_div_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"profile\"));\n    });\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵtext(3, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_div_32_div_2_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"orders\"));\n    });\n    i0.ɵɵelement(5, \"i\", 38);\n    i0.ɵɵtext(6, \" Orders \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_div_32_div_2_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"settings\"));\n    });\n    i0.ɵɵelement(8, \"i\", 39);\n    i0.ɵɵtext(9, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"div\", 40);\n    i0.ɵɵelementStart(11, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_div_32_div_2_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelement(12, \"i\", 41);\n    i0.ɵɵtext(13, \" Logout \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EcommerceHubComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"img\", 33);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_div_32_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserMenu());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, EcommerceHubComponent_div_32_div_2_Template, 14, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.currentUser.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showUserMenu);\n  }\n}\nfunction EcommerceHubComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_ng_template_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"auth/login\"));\n    });\n    i0.ɵɵtext(1, \" Login \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EcommerceHubComponent_div_35_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_div_35_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EcommerceHubComponent_div_35_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_div_35_div_6_div_1_Template_div_click_0_listener() {\n      const suggestion_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectSuggestion(suggestion_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 55);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const suggestion_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getSuggestionIcon(suggestion_r9.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r9.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r9.type);\n  }\n}\nfunction EcommerceHubComponent_div_35_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, EcommerceHubComponent_div_35_div_6_div_1_Template, 6, 4, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.searchSuggestions);\n  }\n}\nfunction EcommerceHubComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45);\n    i0.ɵɵelement(3, \"i\", 16);\n    i0.ɵɵelementStart(4, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EcommerceHubComponent_div_35_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function EcommerceHubComponent_div_35_Template_input_keyup_enter_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    })(\"input\", function EcommerceHubComponent_div_35_Template_input_input_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearchInput());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, EcommerceHubComponent_div_35_button_5_Template, 2, 0, \"button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EcommerceHubComponent_div_35_div_6_Template, 2, 1, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchSuggestions.length > 0);\n  }\n}\nfunction EcommerceHubComponent_section_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 56)(1, \"div\", 57)(2, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_36_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"wishlist\"));\n    });\n    i0.ɵɵelementStart(3, \"div\", 59);\n    i0.ɵɵelement(4, \"i\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 60)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Wishlist Items\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_36_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"cart\"));\n    });\n    i0.ɵɵelementStart(11, \"div\", 61);\n    i0.ɵɵelement(12, \"i\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 60)(14, \"h3\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"Cart Items\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_36_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"orders\"));\n    });\n    i0.ɵɵelementStart(19, \"div\", 62);\n    i0.ɵɵelement(20, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 60)(22, \"h3\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\");\n    i0.ɵɵtext(25, \"Orders\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_36_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"social\"));\n    });\n    i0.ɵɵelementStart(27, \"div\", 63);\n    i0.ɵɵelement(28, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 60)(30, \"h3\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Social Posts\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.wishlistCount);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.cartCount);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.orderCount);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.socialCount);\n  }\n}\nfunction EcommerceHubComponent_section_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 64)(1, \"div\", 65)(2, \"h2\");\n    i0.ɵɵtext(3, \"Explore Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 66)(5, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_37_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"shop\"));\n    });\n    i0.ɵɵelementStart(6, \"div\", 68);\n    i0.ɵɵelement(7, \"i\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"h3\");\n    i0.ɵɵtext(9, \"Shop Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Browse our extensive collection of fashion items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 69)(13, \"span\");\n    i0.ɵɵtext(14, \"Explore Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"i\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_37_Template_div_click_16_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"wishlist\"));\n    });\n    i0.ɵɵelementStart(17, \"div\", 68);\n    i0.ɵɵelement(18, \"i\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"h3\");\n    i0.ɵɵtext(20, \"Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22, \"Save your favorite items for later purchase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 69)(24, \"span\");\n    i0.ɵɵtext(25, \"View Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"i\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_37_Template_div_click_27_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"social\"));\n    });\n    i0.ɵɵelementStart(28, \"div\", 68);\n    i0.ɵɵelement(29, \"i\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"h3\");\n    i0.ɵɵtext(31, \"Social Shopping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Discover products through stories and posts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 69)(35, \"span\");\n    i0.ɵɵtext(36, \"Go Social\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"i\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function EcommerceHubComponent_section_37_Template_div_click_38_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"share\"));\n    });\n    i0.ɵɵelementStart(39, \"div\", 68);\n    i0.ɵɵelement(40, \"i\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"h3\");\n    i0.ɵɵtext(42, \"Share & Earn\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\");\n    i0.ɵɵtext(44, \"Share products with friends and family\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 69)(46, \"span\");\n    i0.ɵɵtext(47, \"Start Sharing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"i\", 70);\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction EcommerceHubComponent_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.wishlistCount);\n  }\n}\nfunction EcommerceHubComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cartCount);\n  }\n}\nexport let EcommerceHubComponent = /*#__PURE__*/(() => {\n  class EcommerceHubComponent {\n    constructor(router) {\n      this.router = router;\n      this.currentView = 'dashboard';\n      this.currentUser = null;\n      this.showUserMenu = false;\n      this.showSearchBar = false;\n      this.searchQuery = '';\n      this.searchSuggestions = [];\n      // Counts\n      this.wishlistCount = 0;\n      this.cartCount = 0;\n      this.orderCount = 0;\n      this.socialCount = 0;\n      this.notificationCount = 0;\n    }\n    ngOnInit() {\n      this.loadCurrentUser();\n      this.loadCounts();\n      this.updateCurrentView();\n      // Listen for route changes\n      this.router.events.subscribe(() => {\n        this.updateCurrentView();\n      });\n    }\n    loadCurrentUser() {\n      // Get current user from auth service\n      this.currentUser = null; // Will be set by auth service\n    }\n    loadCounts() {\n      // Get actual counts from services\n      this.wishlistCount = 0;\n      this.cartCount = 0;\n      this.orderCount = 0;\n      this.socialCount = 0;\n      this.notificationCount = 0;\n    }\n    updateCurrentView() {\n      const url = this.router.url;\n      if (url.includes('/shop')) this.currentView = 'shop';else if (url.includes('/wishlist')) this.currentView = 'wishlist';else if (url.includes('/cart')) this.currentView = 'cart';else if (url.includes('/social')) this.currentView = 'social';else if (url.includes('/orders')) this.currentView = 'orders';else if (url.includes('/profile')) this.currentView = 'profile';else this.currentView = 'dashboard';\n    }\n    // Navigation methods\n    goHome() {\n      this.navigateTo('dashboard');\n    }\n    navigateTo(route) {\n      this.router.navigate([route]);\n      this.showUserMenu = false;\n    }\n    // User menu\n    toggleUserMenu() {\n      this.showUserMenu = !this.showUserMenu;\n    }\n    logout() {\n      // Implement logout functionality\n      this.showUserMenu = false;\n      this.router.navigate(['/auth/login']);\n    }\n    // Search functionality\n    toggleSearch() {\n      this.showSearchBar = !this.showSearchBar;\n      if (!this.showSearchBar) {\n        this.clearSearch();\n      }\n    }\n    onSearchInput() {\n      if (this.searchQuery.length > 2) {\n        // Implement search suggestions from API\n        this.searchSuggestions = [];\n      } else {\n        this.searchSuggestions = [];\n      }\n    }\n    search() {\n      if (this.searchQuery.trim()) {\n        this.router.navigate(['/search'], {\n          queryParams: {\n            q: this.searchQuery\n          }\n        });\n        this.showSearchBar = false;\n        this.clearSearch();\n      }\n    }\n    selectSuggestion(suggestion) {\n      this.searchQuery = suggestion.text;\n      this.search();\n    }\n    clearSearch() {\n      this.searchQuery = '';\n      this.searchSuggestions = [];\n    }\n    getSuggestionIcon(type) {\n      switch (type) {\n        case 'product':\n          return 'fa-box';\n        case 'brand':\n          return 'fa-tag';\n        case 'user':\n          return 'fa-user';\n        default:\n          return 'fa-search';\n      }\n    }\n    // Notifications\n    showNotifications() {\n      this.router.navigate(['/notifications']);\n    }\n    static {\n      this.ɵfac = function EcommerceHubComponent_Factory(t) {\n        return new (t || EcommerceHubComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EcommerceHubComponent,\n        selectors: [[\"app-ecommerce-hub\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 63,\n        vars: 26,\n        consts: [[\"loginButton\", \"\"], [1, \"ecommerce-hub\"], [1, \"hub-header\"], [1, \"header-content\"], [1, \"logo-section\"], [1, \"hub-logo\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"main-nav\"], [1, \"nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-store\"], [1, \"fas\", \"fa-heart\"], [\"class\", \"badge\", 4, \"ngIf\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"fas\", \"fa-users\"], [1, \"user-actions\"], [1, \"action-btn\", \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"action-btn\", \"notifications-btn\", 3, \"click\"], [1, \"fas\", \"fa-bell\"], [\"class\", \"notification-badge\", 4, \"ngIf\"], [\"class\", \"user-menu\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"search-section\", 4, \"ngIf\"], [\"class\", \"quick-stats\", 4, \"ngIf\"], [\"class\", \"feature-highlights\", 4, \"ngIf\"], [1, \"hub-content\"], [1, \"mobile-nav\"], [1, \"mobile-nav-btn\", 3, \"click\"], [\"class\", \"mobile-badge\", 4, \"ngIf\"], [1, \"mobile-nav-btn\", \"hub-btn\", 3, \"click\"], [1, \"fas\", \"fa-th-large\"], [1, \"badge\"], [1, \"notification-badge\"], [1, \"user-menu\"], [1, \"user-avatar\", 3, \"click\", \"src\", \"alt\"], [\"class\", \"user-dropdown\", 4, \"ngIf\"], [1, \"user-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fas\", \"fa-user\"], [1, \"fas\", \"fa-box\"], [1, \"fas\", \"fa-cog\"], [1, \"dropdown-divider\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"btn-login\", 3, \"click\"], [1, \"search-section\"], [1, \"search-container\"], [1, \"search-bar\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, or users...\", 3, \"ngModelChange\", \"keyup.enter\", \"input\", \"ngModel\"], [\"class\", \"btn-clear\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [1, \"btn-clear\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"search-suggestions\"], [\"class\", \"suggestion-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\", 3, \"click\"], [1, \"fas\"], [1, \"suggestion-type\"], [1, \"quick-stats\"], [1, \"stats-container\"], [1, \"stat-card\", 3, \"click\"], [1, \"stat-icon\", \"wishlist\"], [1, \"stat-info\"], [1, \"stat-icon\", \"cart\"], [1, \"stat-icon\", \"orders\"], [1, \"stat-icon\", \"social\"], [1, \"feature-highlights\"], [1, \"highlights-container\"], [1, \"feature-grid\"], [1, \"feature-card\", 3, \"click\"], [1, \"feature-icon\"], [1, \"feature-action\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"fas\", \"fa-camera\"], [1, \"fas\", \"fa-share-alt\"], [1, \"mobile-badge\"]],\n        template: function EcommerceHubComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"header\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\", 5);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_h1_click_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goHome());\n            });\n            i0.ɵɵelement(5, \"i\", 6);\n            i0.ɵɵtext(6, \" DFashion Hub \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"nav\", 7)(8, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_8_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"shop\"));\n            });\n            i0.ɵɵelement(9, \"i\", 9);\n            i0.ɵɵelementStart(10, \"span\");\n            i0.ɵɵtext(11, \"Shop\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_12_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"wishlist\"));\n            });\n            i0.ɵɵelement(13, \"i\", 10);\n            i0.ɵɵelementStart(14, \"span\");\n            i0.ɵɵtext(15, \"Wishlist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(16, EcommerceHubComponent_span_16_Template, 2, 1, \"span\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_17_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"cart\"));\n            });\n            i0.ɵɵelement(18, \"i\", 12);\n            i0.ɵɵelementStart(19, \"span\");\n            i0.ɵɵtext(20, \"Cart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(21, EcommerceHubComponent_span_21_Template, 2, 1, \"span\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_22_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"social\"));\n            });\n            i0.ɵɵelement(23, \"i\", 13);\n            i0.ɵɵelementStart(24, \"span\");\n            i0.ɵɵtext(25, \"Social\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_27_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.toggleSearch());\n            });\n            i0.ɵɵelement(28, \"i\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_29_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showNotifications());\n            });\n            i0.ɵɵelement(30, \"i\", 18);\n            i0.ɵɵtemplate(31, EcommerceHubComponent_span_31_Template, 2, 1, \"span\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(32, EcommerceHubComponent_div_32_Template, 3, 3, \"div\", 20)(33, EcommerceHubComponent_ng_template_33_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(35, EcommerceHubComponent_div_35_Template, 7, 3, \"div\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(36, EcommerceHubComponent_section_36_Template, 34, 4, \"section\", 22)(37, EcommerceHubComponent_section_37_Template, 49, 0, \"section\", 23);\n            i0.ɵɵelementStart(38, \"main\", 24);\n            i0.ɵɵelement(39, \"router-outlet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"nav\", 25)(41, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_41_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"shop\"));\n            });\n            i0.ɵɵelement(42, \"i\", 9);\n            i0.ɵɵelementStart(43, \"span\");\n            i0.ɵɵtext(44, \"Shop\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_45_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"wishlist\"));\n            });\n            i0.ɵɵelement(46, \"i\", 10);\n            i0.ɵɵelementStart(47, \"span\");\n            i0.ɵɵtext(48, \"Wishlist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(49, EcommerceHubComponent_span_49_Template, 2, 1, \"span\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"button\", 28);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_50_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"dashboard\"));\n            });\n            i0.ɵɵelement(51, \"i\", 29);\n            i0.ɵɵelementStart(52, \"span\");\n            i0.ɵɵtext(53, \"Hub\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_54_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"cart\"));\n            });\n            i0.ɵɵelement(55, \"i\", 12);\n            i0.ɵɵelementStart(56, \"span\");\n            i0.ɵɵtext(57, \"Cart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(58, EcommerceHubComponent_span_58_Template, 2, 1, \"span\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function EcommerceHubComponent_Template_button_click_59_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateTo(\"social\"));\n            });\n            i0.ɵɵelement(60, \"i\", 13);\n            i0.ɵɵelementStart(61, \"span\");\n            i0.ɵɵtext(62, \"Social\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            const loginButton_r12 = i0.ɵɵreference(34);\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"shop\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"wishlist\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"cart\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"social\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.notificationCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser)(\"ngIfElse\", loginButton_r12);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.showSearchBar);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"dashboard\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentView === \"dashboard\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"shop\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"wishlist\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistCount > 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"cart\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartCount > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.currentView === \"social\");\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, RouterOutlet],\n        styles: [\".ecommerce-hub[_ngcontent-%COMP%]{min-height:100vh;background:#f8f9fa}.hub-header[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #eee;position:sticky;top:0;z-index:1000;box-shadow:0 2px 4px #0000001a}.header-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;display:flex;align-items:center;justify-content:space-between;padding:12px 20px;gap:20px}.hub-logo[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#007bff;margin:0;cursor:pointer;display:flex;align-items:center;gap:8px}.main-nav[_ngcontent-%COMP%]{display:flex;gap:16px}.nav-btn[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:4px;background:none;border:none;color:#666;cursor:pointer;padding:8px 12px;border-radius:8px;transition:all .2s ease;position:relative;font-size:.8rem}.nav-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;color:#007bff}.nav-btn.active[_ngcontent-%COMP%]{color:#007bff;background:#e3f2fd}.nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.badge[_ngcontent-%COMP%]{position:absolute;top:4px;right:4px;background:#ff6b6b;color:#fff;border-radius:10px;padding:2px 6px;font-size:.7rem;min-width:16px;text-align:center}.user-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.action-btn[_ngcontent-%COMP%]{background:none;border:none;color:#666;cursor:pointer;padding:8px;border-radius:50%;position:relative;font-size:1.1rem;transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;color:#007bff}.notification-badge[_ngcontent-%COMP%]{position:absolute;top:2px;right:2px;background:#ff6b6b;color:#fff;border-radius:8px;padding:1px 4px;font-size:.6rem;min-width:12px;text-align:center}.user-menu[_ngcontent-%COMP%]{position:relative}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;cursor:pointer;border:2px solid transparent;transition:border-color .2s ease}.user-avatar[_ngcontent-%COMP%]:hover{border-color:#007bff}.user-dropdown[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;background:#fff;border:1px solid #eee;border-radius:8px;box-shadow:0 4px 12px #00000026;min-width:180px;z-index:1001;margin-top:8px}.dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:12px 16px;cursor:pointer;font-size:.9rem;color:#333;transition:background .2s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.dropdown-divider[_ngcontent-%COMP%]{height:1px;background:#eee;margin:4px 0}.btn-login[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:8px 16px;border-radius:6px;font-weight:500;cursor:pointer;transition:background .2s ease}.btn-login[_ngcontent-%COMP%]:hover{background:#0056b3}.search-section[_ngcontent-%COMP%]{background:#f8f9fa;border-top:1px solid #eee;padding:16px 20px}.search-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto;position:relative}.search-bar[_ngcontent-%COMP%]{position:relative;width:100%}.search-bar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:12px;top:50%;transform:translateY(-50%);color:#666}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:12px 12px 12px 40px;border:1px solid #ddd;border-radius:8px;font-size:.9rem;background:#fff}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff}.btn-clear[_ngcontent-%COMP%]{position:absolute;right:8px;top:50%;transform:translateY(-50%);background:none;border:none;color:#666;cursor:pointer;padding:4px}.search-suggestions[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #ddd;border-top:none;border-radius:0 0 8px 8px;max-height:300px;overflow-y:auto;z-index:1000}.suggestion-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 16px;cursor:pointer;transition:background .2s ease}.suggestion-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.suggestion-type[_ngcontent-%COMP%]{margin-left:auto;font-size:.8rem;color:#666;text-transform:capitalize}.quick-stats[_ngcontent-%COMP%]{padding:40px 20px;background:#fff;margin-bottom:20px}.stats-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.stat-card[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:24px;background:#f8f9fa;border-radius:12px;cursor:pointer;transition:all .2s ease}.stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #0000001a}.stat-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:12px;display:flex;align-items:center;justify-content:center;font-size:1.5rem;color:#fff}.stat-icon.wishlist[_ngcontent-%COMP%]{background:#ff6b6b}.stat-icon.cart[_ngcontent-%COMP%]{background:#4ecdc4}.stat-icon.orders[_ngcontent-%COMP%]{background:#45b7d1}.stat-icon.social[_ngcontent-%COMP%]{background:#96ceb4}.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:2rem;font-weight:700;color:#333}.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.9rem}.feature-highlights[_ngcontent-%COMP%]{padding:40px 20px}.highlights-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.highlights-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{text-align:center;margin-bottom:40px;font-size:2rem;color:#333}.feature-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:24px}.feature-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:32px 24px;text-align:center;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #0000001a}.feature-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.feature-icon[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:linear-gradient(135deg,#007bff,#0056b3);display:flex;align-items:center;justify-content:center;margin:0 auto 20px;font-size:2rem;color:#fff}.feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 12px;font-size:1.3rem;color:#333}.feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 20px;color:#666;line-height:1.5}.feature-action[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;color:#007bff;font-weight:500}.hub-content[_ngcontent-%COMP%]{min-height:calc(100vh - 200px);padding-bottom:80px}.mobile-nav[_ngcontent-%COMP%]{display:none;position:fixed;bottom:0;left:0;right:0;background:#fff;border-top:1px solid #eee;padding:8px 0;z-index:1000}.mobile-nav[_ngcontent-%COMP%]{display:flex;justify-content:space-around;align-items:center}.mobile-nav-btn[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:4px;background:none;border:none;color:#666;cursor:pointer;padding:8px;border-radius:8px;transition:all .2s ease;position:relative;font-size:.7rem;min-width:60px}.mobile-nav-btn[_ngcontent-%COMP%]:hover, .mobile-nav-btn.active[_ngcontent-%COMP%]{color:#007bff}.mobile-nav-btn.hub-btn[_ngcontent-%COMP%]{background:#007bff;color:#fff;border-radius:50%;width:50px;height:50px;margin-top:-10px}.mobile-nav-btn.hub-btn[_ngcontent-%COMP%]:hover{background:#0056b3;color:#fff}.mobile-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.mobile-badge[_ngcontent-%COMP%]{position:absolute;top:2px;right:8px;background:#ff6b6b;color:#fff;border-radius:8px;padding:1px 4px;font-size:.6rem;min-width:14px;text-align:center}@media (max-width: 768px){.header-content[_ngcontent-%COMP%]{padding:8px 16px;gap:12px}.hub-logo[_ngcontent-%COMP%]{font-size:1.3rem}.main-nav[_ngcontent-%COMP%]{display:none}.user-actions[_ngcontent-%COMP%]{gap:8px}.mobile-nav[_ngcontent-%COMP%]{display:flex}.hub-content[_ngcontent-%COMP%]{padding-bottom:70px}.stats-container[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:16px}.feature-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:20px}.search-section[_ngcontent-%COMP%]{padding:12px 16px}}@media (max-width: 480px){.stats-container[_ngcontent-%COMP%]{grid-template-columns:1fr}.stat-card[_ngcontent-%COMP%]{padding:20px}.feature-card[_ngcontent-%COMP%]{padding:24px 20px}}\"]\n      });\n    }\n  }\n  return EcommerceHubComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}