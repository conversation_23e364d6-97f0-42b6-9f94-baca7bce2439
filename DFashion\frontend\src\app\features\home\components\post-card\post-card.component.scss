.post {
  background: #fff;
  border: 1px solid #dbdbdb;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  @media (max-width: 768px) {
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 16px;
  }
}

.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #667eea;
  }

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
}

.user-details {
  h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 2px 0;
    color: #262626;
  }

  span {
    font-size: 12px;
    color: #8e8e8e;
  }
}

.more-options {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #262626;
  border-radius: 50%;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
  }
}

.post-media {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
  background: #f8f9fa;
  cursor: pointer;

  &.video-container {
    background: #000;
  }
}

.media-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f8f9fa;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e3e3e3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.post-image,
.post-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.1s ease;

  &:active {
    transform: scale(0.98);
  }
}

// Enhanced Shopping Bag Button
.shopping-bag-btn {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  z-index: 10;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
  }

  &.active {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    animation: bounce 0.6s ease;
  }

  i {
    color: white;
    font-size: 20px;
  }

  .product-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #f5576c;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    border: 2px solid white;
    animation: pulse 2s infinite;
  }

  .shopping-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.3);
    animation: ripple 2s infinite;
  }

  @media (max-width: 768px) {
    width: 48px;
    height: 48px;
    bottom: 12px;
    right: 12px;

    i {
      font-size: 18px;
    }

    .product-count {
      width: 20px;
      height: 20px;
      font-size: 10px;
    }
  }
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  80% { transform: translateY(-5px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

// Product Tags
.product-tags {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;

  &.show-tags {
    opacity: 1;
    pointer-events: all;
  }
}

.product-tag {
  position: absolute;
  pointer-events: all;
  cursor: pointer;
  animation: fadeInScale 0.3s ease;

  .tag-dot {
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    border: 3px solid #667eea;
    position: relative;
    animation: ripple 2s infinite;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 8px;
      height: 8px;
      background: #667eea;
      border-radius: 50%;
    }

    .tag-pulse {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: rgba(102, 126, 234, 0.3);
      animation: ripple 2s infinite;
    }
  }

  .product-info {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 250px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideUp 0.3s ease;

    img {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      object-fit: cover;
    }

    .product-details {
      flex: 1;
      color: white;

      h5 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        line-height: 1.2;
      }

      p {
        font-size: 16px;
        font-weight: 700;
        color: #667eea;
        margin: 0 0 8px 0;
      }
    }

    .product-quick-actions {
      display: flex;
      gap: 6px;

      .quick-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }

        &.buy-btn:hover {
          background: #f5576c;
          border-color: #f5576c;
        }

        &.cart-btn:hover {
          background: #667eea;
          border-color: #667eea;
        }

        &.wishlist-btn:hover {
          background: #f093fb;
          border-color: #f093fb;
        }

        i {
          font-size: 12px;
        }
      }
    }

    @media (max-width: 768px) {
      min-width: 200px;
      padding: 8px;

      img {
        width: 40px;
        height: 40px;
      }

      .product-details h5 {
        font-size: 12px;
      }

      .product-details p {
        font-size: 14px;
      }

      .quick-btn {
        width: 28px;
        height: 28px;

        i {
          font-size: 10px;
        }
      }
    }
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// Shopping Indicator
.shopping-indicator {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease;

  i {
    font-size: 10px;
    animation: bounce 2s infinite;
  }

  @media (max-width: 768px) {
    bottom: 12px;
    left: 12px;
    padding: 6px 10px;
    font-size: 11px;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// Heart Animation
.heart-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 80px;
  color: #f5576c;
  opacity: 0;
  pointer-events: none;
  z-index: 10;

  &.animate {
    animation: heartPop 0.8s ease;
  }

  @media (max-width: 768px) {
    font-size: 60px;
  }
}

@keyframes heartPop {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

// Post Actions
.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 8px;

  @media (max-width: 768px) {
    padding: 8px 12px 4px;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;

  @media (max-width: 768px) {
    gap: 12px;
  }
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  font-size: 24px;
  color: #262626;
  transition: all 0.3s ease;
  border-radius: 50%;

  &:hover {
    background-color: #f5f5f5;
    transform: scale(1.1);
  }

  &.liked {
    color: #f5576c;
    animation: heartBeat 0.6s ease;
  }

  @media (max-width: 768px) {
    font-size: 20px;
    padding: 6px;
  }
}

.save-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  font-size: 24px;
  color: #262626;
  transition: all 0.3s ease;
  border-radius: 50%;

  &:hover {
    background-color: #f5f5f5;
    transform: scale(1.1);
  }

  &.saved {
    color: #262626;
  }

  @media (max-width: 768px) {
    font-size: 20px;
    padding: 6px;
  }
}

@keyframes heartBeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

// Enhanced E-commerce Actions
.ecommerce-actions {
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-top: 1px solid #e2e8f0;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.products-showcase {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-showcase {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &.featured {
    border: 2px solid #667eea;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
  }

  .product-thumb {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }

    @media (max-width: 768px) {
      width: 50px;
      height: 50px;
    }
  }

  .product-info-inline {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    h5 {
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 4px 0;
      color: #2d3748;
      cursor: pointer;
      transition: color 0.3s ease;
      line-height: 1.3;

      &:hover {
        color: #667eea;
      }

      @media (max-width: 768px) {
        font-size: 13px;
      }
    }

    .price {
      font-size: 16px;
      font-weight: 700;
      color: #667eea;
      margin: 0 0 8px 0;

      @media (max-width: 768px) {
        font-size: 14px;
      }
    }

    .product-rating {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 8px;

      .stars {
        display: flex;
        gap: 2px;

        i {
          font-size: 12px;
          color: #e2e8f0;

          &.filled {
            color: #f6ad55;
          }
        }
      }

      .rating-text {
        font-size: 11px;
        color: #718096;
      }
    }

    .product-actions {
      display: flex;
      gap: 8px;
      align-items: center;

      .btn-wishlist {
        background: none;
        border: 1px solid #e2e8f0;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #718096;

        &:hover {
          border-color: #f093fb;
          color: #f093fb;
          transform: scale(1.1);
        }

        &.active {
          background: #f093fb;
          border-color: #f093fb;
          color: white;
        }

        i {
          font-size: 14px;
        }

        @media (max-width: 768px) {
          width: 28px;
          height: 28px;

          i {
            font-size: 12px;
          }
        }
      }

      .btn-cart {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        color: white;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        i {
          font-size: 11px;
        }

        @media (max-width: 768px) {
          padding: 6px 12px;
          font-size: 11px;
        }
      }

      .btn-buy-now {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        color: white;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);
        }

        i {
          font-size: 11px;
        }

        @media (max-width: 768px) {
          padding: 6px 12px;
          font-size: 11px;
        }
      }
    }
  }
}

.shop-all-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;

  .btn-shop-all {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .total-price {
      background: rgba(255, 255, 255, 0.2);
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 700;
    }

    i {
      font-size: 16px;
    }

    @media (max-width: 768px) {
      padding: 10px 20px;
      font-size: 13px;
    }
  }
}

// Post Stats and Content
.post-stats {
  padding: 0 16px 8px;

  p {
    margin: 0;
    font-size: 14px;
    color: #262626;
  }

  @media (max-width: 768px) {
    padding: 0 12px 6px;

    p {
      font-size: 13px;
    }
  }
}

.post-caption {
  padding: 0 16px 8px;

  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
    color: #262626;

    strong {
      font-weight: 600;
    }
  }

  @media (max-width: 768px) {
    padding: 0 12px 6px;

    p {
      font-size: 13px;
    }
  }
}

.post-comments {
  padding: 0 16px 16px;

  .view-comments {
    font-size: 14px;
    color: #8e8e8e;
    margin: 0 0 8px 0;
    cursor: pointer;

    &:hover {
      color: #262626;
    }
  }

  .comment {
    margin-bottom: 4px;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.4;
      color: #262626;

      strong {
        font-weight: 600;
      }
    }
  }

  .add-comment {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;

    input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 14px;
      color: #262626;

      &::placeholder {
        color: #8e8e8e;
      }
    }

    .post-comment-btn {
      background: none;
      border: none;
      color: #0095f6;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;

      &:hover {
        color: #00376b;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 0 12px 12px;

    .view-comments,
    .comment p,
    input {
      font-size: 13px;
    }
  }
}
