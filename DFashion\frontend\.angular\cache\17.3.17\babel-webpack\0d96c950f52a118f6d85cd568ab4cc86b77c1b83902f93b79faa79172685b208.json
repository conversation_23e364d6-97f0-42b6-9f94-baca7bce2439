{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n  return console.warn(`[Ionic Warning]: ${message}`, ...params);\n};\n/*\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n  return console.error(`[Ionic Error]: ${message}`, ...params);\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n  return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\nexport { printIonError as a, printRequiredElementError as b, printIonWarning as p };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}