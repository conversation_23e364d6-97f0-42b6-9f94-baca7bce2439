import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ShopDataService, Brand } from '../../../core/services/shop-data.service';

@Component({
  selector: 'app-featured-brands',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="featured-brands-section">
      <!-- Section Header -->
      <div class="section-header">
        <div class="header-content">
          <h2 class="section-title">
            <i class="fas fa-star"></i>
            Featured Brands
          </h2>
          <p class="section-subtitle">Discover top brands loved by millions</p>
        </div>
        <button class="view-all-btn" (click)="viewAllBrands()">
          <span>View All</span>
          <i class="fas fa-arrow-right"></i>
        </button>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="loading-grid">
          <div *ngFor="let item of [1,2,3,4,5,6]" class="brand-card-skeleton">
            <div class="skeleton-logo"></div>
            <div class="skeleton-text"></div>
            <div class="skeleton-text short"></div>
          </div>
        </div>
      </div>

      <!-- Brands Grid -->
      <div *ngIf="!isLoading && brands.length > 0" class="brands-grid">
        <div 
          *ngFor="let brand of brands; trackBy: trackByBrandId" 
          class="brand-card"
          (click)="navigateToBrand(brand)"
          [attr.aria-label]="'View ' + brand.name + ' products'"
          tabindex="0"
          (keydown.enter)="navigateToBrand(brand)"
          (keydown.space)="navigateToBrand(brand)">
          
          <!-- Brand Logo -->
          <div class="brand-logo-container">
            <img 
              [src]="brand.logo" 
              [alt]="brand.name + ' logo'"
              class="brand-logo"
              loading="lazy"
              (error)="onImageError($event)">
            
            <!-- Verification Badge -->
            <div class="verification-badge" *ngIf="brand.isVerified">
              <i class="fas fa-check-circle"></i>
            </div>
            
            <!-- Trending Badge -->
            <div class="trending-badge" *ngIf="brand.trending">
              <i class="fas fa-fire"></i>
              <span>Trending</span>
            </div>
          </div>

          <!-- Brand Info -->
          <div class="brand-info">
            <h3 class="brand-name">{{ brand.name }}</h3>
            <p class="brand-description">{{ brand.description }}</p>
            
            <!-- Brand Stats -->
            <div class="brand-stats">
              <div class="stat-item">
                <i class="fas fa-box"></i>
                <span>{{ brand.productCount }} Products</span>
              </div>
              <div class="stat-item" *ngIf="brand.rating">
                <i class="fas fa-star"></i>
                <span>{{ brand.rating }}/5</span>
              </div>
              <div class="stat-item" *ngIf="brand.establishedYear">
                <i class="fas fa-calendar"></i>
                <span>Est. {{ brand.establishedYear }}</span>
              </div>
            </div>

            <!-- Categories -->
            <div class="brand-categories" *ngIf="brand.categories && brand.categories.length > 0">
              <span 
                *ngFor="let category of brand.categories.slice(0, 3)" 
                class="category-tag"
                (click)="navigateToCategory(category, brand); $event.stopPropagation()">
                {{ category | titlecase }}
              </span>
              <span *ngIf="brand.categories.length > 3" class="more-categories">
                +{{ brand.categories.length - 3 }} more
              </span>
            </div>
          </div>

          <!-- Hover Actions -->
          <div class="brand-actions">
            <button 
              class="action-btn primary"
              (click)="navigateToBrand(brand); $event.stopPropagation()"
              [attr.aria-label]="'Shop ' + brand.name">
              <i class="fas fa-shopping-bag"></i>
              <span>Shop Now</span>
            </button>
            <button 
              class="action-btn secondary"
              (click)="viewBrandInfo(brand); $event.stopPropagation()"
              [attr.aria-label]="'View ' + brand.name + ' information'">
              <i class="fas fa-info-circle"></i>
            </button>
            <button 
              class="action-btn secondary"
              (click)="toggleFavorite(brand); $event.stopPropagation()"
              [attr.aria-label]="'Add ' + brand.name + ' to favorites'">
              <i class="fas fa-heart" [class.favorited]="isFavorite(brand._id)"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && brands.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-store-slash"></i>
        </div>
        <h3>No Featured Brands</h3>
        <p>We're working on adding amazing brands for you!</p>
        <button class="retry-btn" (click)="loadBrands()">
          <i class="fas fa-refresh"></i>
          Try Again
        </button>
      </div>

      <!-- Brand Info Modal -->
      <div class="brand-modal" *ngIf="selectedBrand" (click)="closeBrandModal()">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <button class="close-btn" (click)="closeBrandModal()">
            <i class="fas fa-times"></i>
          </button>
          
          <div class="modal-header">
            <img [src]="selectedBrand.logo" [alt]="selectedBrand.name" class="modal-logo">
            <div class="modal-brand-info">
              <h3>{{ selectedBrand.name }}</h3>
              <p>{{ selectedBrand.description }}</p>
              <a *ngIf="selectedBrand.website" 
                 [href]="selectedBrand.website" 
                 target="_blank" 
                 class="website-link">
                <i class="fas fa-external-link-alt"></i>
                Visit Website
              </a>
            </div>
          </div>
          
          <div class="modal-stats">
            <div class="stat-card">
              <div class="stat-value">{{ selectedBrand.productCount }}</div>
              <div class="stat-label">Products</div>
            </div>
            <div class="stat-card" *ngIf="selectedBrand.rating">
              <div class="stat-value">{{ selectedBrand.rating }}/5</div>
              <div class="stat-label">Rating</div>
            </div>
            <div class="stat-card" *ngIf="selectedBrand.establishedYear">
              <div class="stat-value">{{ selectedBrand.establishedYear }}</div>
              <div class="stat-label">Established</div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="btn btn-primary" (click)="navigateToBrand(selectedBrand)">
              <i class="fas fa-shopping-bag"></i>
              Shop {{ selectedBrand.name }}
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./featured-brands.component.scss']
})
export class FeaturedBrandsComponent implements OnInit, OnDestroy {
  @Input() maxBrands: number = 6;
  @Input() showHeader: boolean = true;
  
  brands: Brand[] = [];
  isLoading: boolean = true;
  selectedBrand: Brand | null = null;
  favoriteBrands: Set<string> = new Set();
  
  private destroy$ = new Subject<void>();

  constructor(
    private shopDataService: ShopDataService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadBrands();
    this.loadFavorites();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadBrands(): void {
    this.isLoading = true;
    
    this.shopDataService.loadFeaturedBrands()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (brands) => {
          this.brands = brands.slice(0, this.maxBrands);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading featured brands:', error);
          this.isLoading = false;
        }
      });
  }

  navigateToBrand(brand: Brand): void {
    // Track brand click analytics
    this.trackBrandClick(brand);
    
    // Navigate to brand page
    this.router.navigate(['/shop/brand', brand.slug]);
  }

  navigateToCategory(category: string, brand: Brand): void {
    // Navigate to category with brand filter
    this.router.navigate(['/shop/category', category], {
      queryParams: { brand: brand.slug }
    });
  }

  viewAllBrands(): void {
    this.router.navigate(['/shop/brands']);
  }

  viewBrandInfo(brand: Brand): void {
    this.selectedBrand = brand;
  }

  closeBrandModal(): void {
    this.selectedBrand = null;
  }

  toggleFavorite(brand: Brand): void {
    if (this.favoriteBrands.has(brand._id)) {
      this.favoriteBrands.delete(brand._id);
    } else {
      this.favoriteBrands.add(brand._id);
    }
    this.saveFavorites();
  }

  isFavorite(brandId: string): boolean {
    return this.favoriteBrands.has(brandId);
  }

  onImageError(event: any): void {
    // Set fallback image
    event.target.src = 'https://via.placeholder.com/200x100/f0f0f0/666?text=Brand+Logo';
  }

  trackByBrandId(index: number, brand: Brand): string {
    return brand._id;
  }

  private trackBrandClick(brand: Brand): void {
    // Analytics tracking
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'brand_click', {
        brand_name: brand.name,
        brand_id: brand._id,
        event_category: 'engagement'
      });
    }
  }

  private loadFavorites(): void {
    const stored = localStorage.getItem('dfashion_favorite_brands');
    if (stored) {
      try {
        const favorites = JSON.parse(stored);
        this.favoriteBrands = new Set(favorites);
      } catch (e) {
        console.warn('Failed to load favorite brands');
      }
    }
  }

  private saveFavorites(): void {
    localStorage.setItem('dfashion_favorite_brands', JSON.stringify(Array.from(this.favoriteBrands)));
  }
}
