{"ast": null, "code": "import { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RecommendationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:5000/api';\n    this.userAnalytics$ = new BehaviorSubject(null);\n  }\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId, limit = 10) {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log('🎯 Loading suggested products (offline mode)');\n    return this.getFallbackSuggestedProducts(limit);\n    /* API version - uncomment when backend is available\n    const params = new URLSearchParams();\n    if (userId) params.append('userId', userId);\n    params.append('limit', limit.toString());\n         return this.http.get<any>(`${this.apiUrl}/recommendations/suggested?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching suggested products:', error);\n          return this.getFallbackSuggestedProducts(limit);\n        })\n      );\n    */\n  }\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category, limit = 10) {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log('📈 Loading trending products (offline mode)');\n    return this.getFallbackTrendingProducts(limit);\n    /* API version - uncomment when backend is available\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n         return this.http.get<any>(`${this.apiUrl}/recommendations/trending?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching trending products:', error);\n          return this.getFallbackTrendingProducts(limit);\n        })\n      );\n    */\n  }\n  // Similar Products - Based on Product\n  getSimilarProducts(productId, limit = 6) {\n    return this.http.get(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching similar products:', error);\n      return this.getFallbackSimilarProducts(limit);\n    }));\n  }\n  // Recently Viewed Products\n  getRecentlyViewed(userId, limit = 8) {\n    return this.http.get(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching recently viewed:', error);\n      return this.getFallbackRecentProducts(limit);\n    }));\n  }\n  // Track User Behavior for Analytics\n  trackProductView(productId, category, duration = 0) {\n    return this.http.post(`${this.apiUrl}/analytics/track-view`, {\n      productId,\n      category,\n      duration,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking product view:', error);\n      return [];\n    }));\n  }\n  trackSearch(query, category, resultsClicked = 0) {\n    return this.http.post(`${this.apiUrl}/analytics/track-search`, {\n      query,\n      category,\n      resultsClicked,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking search:', error);\n      return [];\n    }));\n  }\n  trackPurchase(productId, category, price) {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking purchase:', error);\n      return [];\n    }));\n  }\n  // User Analytics\n  getUserAnalytics(userId) {\n    return this.http.get(`${this.apiUrl}/analytics/user/${userId}`).pipe(map(response => response.success ? response.data : this.getDefaultAnalytics(userId)), catchError(error => {\n      console.error('Error fetching user analytics:', error);\n      return [this.getDefaultAnalytics(userId)];\n    }));\n  }\n  // Category-based Recommendations\n  getCategoryRecommendations(category, limit = 8) {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log(`🏷️ Loading ${category} recommendations (offline mode)`);\n    return this.getFallbackCategoryProducts(category, limit);\n    /* API version - uncomment when backend is available\n    return this.http.get<any>(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching category recommendations:', error);\n          return this.getFallbackCategoryProducts(category, limit);\n        })\n      );\n    */\n  }\n  // Fallback methods for offline/error scenarios\n  getFallbackSuggestedProducts(limit) {\n    // Return mock suggested products based on popular items\n    const mockProducts = [{\n      _id: 'suggested-1',\n      name: 'Trending Cotton T-Shirt',\n      description: 'Popular cotton t-shirt based on your preferences',\n      price: 899,\n      originalPrice: 1299,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        alt: 'Cotton T-Shirt',\n        isPrimary: true\n      }],\n      category: 'men',\n      subcategory: 'shirts',\n      brand: 'ComfortWear',\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      tags: ['cotton', 'casual', 'trending'],\n      isActive: true,\n      isFeatured: true,\n      recommendationScore: 0.85,\n      recommendationReason: 'Based on your recent views'\n    }];\n    return new Observable(observer => {\n      observer.next(mockProducts.slice(0, limit));\n      observer.complete();\n    });\n  }\n  getFallbackTrendingProducts(limit) {\n    const mockTrending = [{\n      _id: 'trending-1',\n      name: 'Viral Summer Dress',\n      description: 'This dress is trending across social media',\n      price: 2499,\n      originalPrice: 3499,\n      discount: 29,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        alt: 'Summer Dress',\n        isPrimary: true\n      }],\n      category: 'women',\n      subcategory: 'dresses',\n      brand: 'StyleHub',\n      rating: {\n        average: 4.5,\n        count: 89\n      },\n      tags: ['summer', 'trending', 'viral'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.92,\n      trendingReason: 'Viral on social media',\n      viewCount: 15420,\n      purchaseCount: 342,\n      shareCount: 1250,\n      engagementRate: 8.7\n    }, {\n      _id: 'trending-2',\n      name: 'Trending Casual T-Shirt',\n      description: 'Popular casual wear for everyday comfort',\n      price: 899,\n      originalPrice: 1299,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        alt: 'Casual T-Shirt',\n        isPrimary: true\n      }],\n      category: 'men',\n      subcategory: 'shirts',\n      brand: 'ComfortWear',\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      tags: ['casual', 'trending', 'comfort'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.85,\n      trendingReason: 'High demand this week',\n      viewCount: 12300,\n      purchaseCount: 287,\n      shareCount: 890,\n      engagementRate: 7.4\n    }, {\n      _id: 'trending-3',\n      name: 'Stylish Ethnic Kurta',\n      description: 'Traditional wear with modern styling',\n      price: 1899,\n      originalPrice: 2499,\n      discount: 24,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n        alt: 'Ethnic Kurta',\n        isPrimary: true\n      }],\n      category: 'women',\n      subcategory: 'ethnic',\n      brand: 'EthnicChic',\n      rating: {\n        average: 4.6,\n        count: 203\n      },\n      tags: ['ethnic', 'traditional', 'festive'],\n      isActive: true,\n      isFeatured: true,\n      trendingScore: 0.88,\n      trendingReason: 'Festival season favorite',\n      viewCount: 9800,\n      purchaseCount: 198,\n      shareCount: 567,\n      engagementRate: 8.1\n    }];\n    return new Observable(observer => {\n      observer.next(mockTrending.slice(0, limit));\n      observer.complete();\n    });\n  }\n  getFallbackSimilarProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackRecentProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackCategoryProducts(category, limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getDefaultAnalytics(userId) {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: {\n        min: 500,\n        max: 5000\n      },\n      brandPreferences: []\n    };\n  }\n  // Update user analytics locally\n  updateUserAnalytics(analytics) {\n    this.userAnalytics$.next(analytics);\n  }\n  // Get current user analytics\n  getCurrentUserAnalytics() {\n    return this.userAnalytics$.asObservable();\n  }\n  static {\n    this.ɵfac = function RecommendationService_Factory(t) {\n      return new (t || RecommendationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RecommendationService,\n      factory: RecommendationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "BehaviorSubject", "map", "catchError", "RecommendationService", "constructor", "http", "apiUrl", "userAnalytics$", "getSuggestedProducts", "userId", "limit", "console", "log", "getFallbackSuggestedProducts", "getTrendingProducts", "category", "getFallbackTrendingProducts", "getSimilarProducts", "productId", "get", "pipe", "response", "success", "data", "error", "getFallbackSimilarProducts", "getRecently<PERSON>iewed", "getFallbackRecentProducts", "trackProductView", "duration", "post", "timestamp", "Date", "trackSearch", "query", "resultsClicked", "trackPurchase", "price", "getUserAnalytics", "getDefaultAnalytics", "getCategoryRecommendations", "getFallbackCategoryProducts", "mockProducts", "_id", "name", "description", "originalPrice", "discount", "images", "url", "alt", "isPrimary", "subcategory", "brand", "rating", "average", "count", "tags", "isActive", "isFeatured", "recommendationScore", "recommendationReason", "observer", "next", "slice", "complete", "mockTrending", "trendingScore", "trendingReason", "viewCount", "purchaseCount", "shareCount", "engagementRate", "viewHistory", "searchHistory", "purchaseHistory", "wishlistItems", "cartItems", "preferredCategories", "priceRange", "min", "max", "brandPreferences", "updateUserAnalytics", "analytics", "getCurrentUserAnalytics", "asObservable", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fahion\\DFashion\\frontend\\src\\app\\core\\services\\recommendation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\n\nexport interface RecommendationProduct {\n  _id: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice: number;\n  discount: number;\n  images: Array<{ url: string; alt: string; isPrimary: boolean }>;\n  category: string;\n  subcategory: string;\n  brand: string;\n  rating: { average: number; count: number };\n  tags: string[];\n  isActive: boolean;\n  isFeatured: boolean;\n  recommendationScore?: number;\n  recommendationReason?: string;\n}\n\nexport interface TrendingProduct extends RecommendationProduct {\n  trendingScore: number;\n  trendingReason: string;\n  viewCount: number;\n  purchaseCount: number;\n  shareCount: number;\n  engagementRate: number;\n}\n\nexport interface UserAnalytics {\n  userId: string;\n  viewHistory: Array<{\n    productId: string;\n    category: string;\n    timestamp: Date;\n    duration: number;\n  }>;\n  searchHistory: Array<{\n    query: string;\n    category?: string;\n    timestamp: Date;\n    resultsClicked: number;\n  }>;\n  purchaseHistory: Array<{\n    productId: string;\n    category: string;\n    price: number;\n    timestamp: Date;\n  }>;\n  wishlistItems: string[];\n  cartItems: string[];\n  preferredCategories: string[];\n  priceRange: { min: number; max: number };\n  brandPreferences: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RecommendationService {\n  private apiUrl = 'http://localhost:5000/api';\n  private userAnalytics$ = new BehaviorSubject<UserAnalytics | null>(null);\n\n  constructor(private http: HttpClient) {}\n\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId?: string, limit: number = 10): Observable<RecommendationProduct[]> {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log('🎯 Loading suggested products (offline mode)');\n    return this.getFallbackSuggestedProducts(limit);\n\n    /* API version - uncomment when backend is available\n    const params = new URLSearchParams();\n    if (userId) params.append('userId', userId);\n    params.append('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/suggested?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching suggested products:', error);\n          return this.getFallbackSuggestedProducts(limit);\n        })\n      );\n    */\n  }\n\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category?: string, limit: number = 10): Observable<TrendingProduct[]> {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log('📈 Loading trending products (offline mode)');\n    return this.getFallbackTrendingProducts(limit);\n\n    /* API version - uncomment when backend is available\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/trending?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching trending products:', error);\n          return this.getFallbackTrendingProducts(limit);\n        })\n      );\n    */\n  }\n\n  // Similar Products - Based on Product\n  getSimilarProducts(productId: string, limit: number = 6): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching similar products:', error);\n          return this.getFallbackSimilarProducts(limit);\n        })\n      );\n  }\n\n  // Recently Viewed Products\n  getRecentlyViewed(userId: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching recently viewed:', error);\n          return this.getFallbackRecentProducts(limit);\n        })\n      );\n  }\n\n  // Track User Behavior for Analytics\n  trackProductView(productId: string, category: string, duration: number = 0): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-view`, {\n      productId,\n      category,\n      duration,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking product view:', error);\n        return [];\n      })\n    );\n  }\n\n  trackSearch(query: string, category?: string, resultsClicked: number = 0): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-search`, {\n      query,\n      category,\n      resultsClicked,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking search:', error);\n        return [];\n      })\n    );\n  }\n\n  trackPurchase(productId: string, category: string, price: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking purchase:', error);\n        return [];\n      })\n    );\n  }\n\n  // User Analytics\n  getUserAnalytics(userId: string): Observable<UserAnalytics> {\n    return this.http.get<any>(`${this.apiUrl}/analytics/user/${userId}`)\n      .pipe(\n        map(response => response.success ? response.data : this.getDefaultAnalytics(userId)),\n        catchError(error => {\n          console.error('Error fetching user analytics:', error);\n          return [this.getDefaultAnalytics(userId)];\n        })\n      );\n  }\n\n  // Category-based Recommendations\n  getCategoryRecommendations(category: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log(`🏷️ Loading ${category} recommendations (offline mode)`);\n    return this.getFallbackCategoryProducts(category, limit);\n\n    /* API version - uncomment when backend is available\n    return this.http.get<any>(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching category recommendations:', error);\n          return this.getFallbackCategoryProducts(category, limit);\n        })\n      );\n    */\n  }\n\n  // Fallback methods for offline/error scenarios\n  private getFallbackSuggestedProducts(limit: number): Observable<RecommendationProduct[]> {\n    // Return mock suggested products based on popular items\n    const mockProducts: RecommendationProduct[] = [\n      {\n        _id: 'suggested-1',\n        name: 'Trending Cotton T-Shirt',\n        description: 'Popular cotton t-shirt based on your preferences',\n        price: 899,\n        originalPrice: 1299,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', alt: 'Cotton T-Shirt', isPrimary: true }],\n        category: 'men',\n        subcategory: 'shirts',\n        brand: 'ComfortWear',\n        rating: { average: 4.2, count: 156 },\n        tags: ['cotton', 'casual', 'trending'],\n        isActive: true,\n        isFeatured: true,\n        recommendationScore: 0.85,\n        recommendationReason: 'Based on your recent views'\n      }\n    ];\n    return new Observable(observer => {\n      observer.next(mockProducts.slice(0, limit));\n      observer.complete();\n    });\n  }\n\n  private getFallbackTrendingProducts(limit: number): Observable<TrendingProduct[]> {\n    const mockTrending: TrendingProduct[] = [\n      {\n        _id: 'trending-1',\n        name: 'Viral Summer Dress',\n        description: 'This dress is trending across social media',\n        price: 2499,\n        originalPrice: 3499,\n        discount: 29,\n        images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400', alt: 'Summer Dress', isPrimary: true }],\n        category: 'women',\n        subcategory: 'dresses',\n        brand: 'StyleHub',\n        rating: { average: 4.5, count: 89 },\n        tags: ['summer', 'trending', 'viral'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.92,\n        trendingReason: 'Viral on social media',\n        viewCount: 15420,\n        purchaseCount: 342,\n        shareCount: 1250,\n        engagementRate: 8.7\n      },\n      {\n        _id: 'trending-2',\n        name: 'Trending Casual T-Shirt',\n        description: 'Popular casual wear for everyday comfort',\n        price: 899,\n        originalPrice: 1299,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', alt: 'Casual T-Shirt', isPrimary: true }],\n        category: 'men',\n        subcategory: 'shirts',\n        brand: 'ComfortWear',\n        rating: { average: 4.2, count: 156 },\n        tags: ['casual', 'trending', 'comfort'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.85,\n        trendingReason: 'High demand this week',\n        viewCount: 12300,\n        purchaseCount: 287,\n        shareCount: 890,\n        engagementRate: 7.4\n      },\n      {\n        _id: 'trending-3',\n        name: 'Stylish Ethnic Kurta',\n        description: 'Traditional wear with modern styling',\n        price: 1899,\n        originalPrice: 2499,\n        discount: 24,\n        images: [{ url: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400', alt: 'Ethnic Kurta', isPrimary: true }],\n        category: 'women',\n        subcategory: 'ethnic',\n        brand: 'EthnicChic',\n        rating: { average: 4.6, count: 203 },\n        tags: ['ethnic', 'traditional', 'festive'],\n        isActive: true,\n        isFeatured: true,\n        trendingScore: 0.88,\n        trendingReason: 'Festival season favorite',\n        viewCount: 9800,\n        purchaseCount: 198,\n        shareCount: 567,\n        engagementRate: 8.1\n      }\n    ];\n    return new Observable(observer => {\n      observer.next(mockTrending.slice(0, limit));\n      observer.complete();\n    });\n  }\n\n  private getFallbackSimilarProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackRecentProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackCategoryProducts(category: string, limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getDefaultAnalytics(userId: string): UserAnalytics {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: { min: 500, max: 5000 },\n      brandPreferences: []\n    };\n  }\n\n  // Update user analytics locally\n  updateUserAnalytics(analytics: UserAnalytics): void {\n    this.userAnalytics$.next(analytics);\n  }\n\n  // Get current user analytics\n  getCurrentUserAnalytics(): Observable<UserAnalytics | null> {\n    return this.userAnalytics$.asObservable();\n  }\n}\n"], "mappings": "AAEA,SAASA,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AA4DhD,OAAM,MAAOC,qBAAqB;EAIhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,MAAM,GAAG,2BAA2B;IACpC,KAAAC,cAAc,GAAG,IAAIP,eAAe,CAAuB,IAAI,CAAC;EAEjC;EAEvC;EACAQ,oBAAoBA,CAACC,MAAe,EAAEC,KAAA,GAAgB,EAAE;IACtD;IACAC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3D,OAAO,IAAI,CAACC,4BAA4B,CAACH,KAAK,CAAC;IAE/C;;;;;;;;;;;;;EAcF;EAEA;EACAI,mBAAmBA,CAACC,QAAiB,EAAEL,KAAA,GAAgB,EAAE;IACvD;IACAC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D,OAAO,IAAI,CAACI,2BAA2B,CAACN,KAAK,CAAC;IAE9C;;;;;;;;;;;;;EAcF;EAEA;EACAO,kBAAkBA,CAACC,SAAiB,EAAER,KAAA,GAAgB,CAAC;IACrD,OAAO,IAAI,CAACL,IAAI,CAACc,GAAG,CAAM,GAAG,IAAI,CAACb,MAAM,4BAA4BY,SAAS,UAAUR,KAAK,EAAE,CAAC,CAC5FU,IAAI,CACHnB,GAAG,CAACoB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDrB,UAAU,CAACsB,KAAK,IAAG;MACjBb,OAAO,CAACa,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI,CAACC,0BAA0B,CAACf,KAAK,CAAC;IAC/C,CAAC,CAAC,CACH;EACL;EAEA;EACAgB,iBAAiBA,CAACjB,MAAc,EAAEC,KAAA,GAAgB,CAAC;IACjD,OAAO,IAAI,CAACL,IAAI,CAACc,GAAG,CAAM,GAAG,IAAI,CAACb,MAAM,2BAA2BG,MAAM,UAAUC,KAAK,EAAE,CAAC,CACxFU,IAAI,CACHnB,GAAG,CAACoB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDrB,UAAU,CAACsB,KAAK,IAAG;MACjBb,OAAO,CAACa,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,IAAI,CAACG,yBAAyB,CAACjB,KAAK,CAAC;IAC9C,CAAC,CAAC,CACH;EACL;EAEA;EACAkB,gBAAgBA,CAACV,SAAiB,EAAEH,QAAgB,EAAEc,QAAA,GAAmB,CAAC;IACxE,OAAO,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAAC,GAAG,IAAI,CAACxB,MAAM,uBAAuB,EAAE;MAC3DY,SAAS;MACTH,QAAQ;MACRc,QAAQ;MACRE,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACZ,IAAI,CACLlB,UAAU,CAACsB,KAAK,IAAG;MACjBb,OAAO,CAACa,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEAS,WAAWA,CAACC,KAAa,EAAEnB,QAAiB,EAAEoB,cAAA,GAAyB,CAAC;IACtE,OAAO,IAAI,CAAC9B,IAAI,CAACyB,IAAI,CAAC,GAAG,IAAI,CAACxB,MAAM,yBAAyB,EAAE;MAC7D4B,KAAK;MACLnB,QAAQ;MACRoB,cAAc;MACdJ,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACZ,IAAI,CACLlB,UAAU,CAACsB,KAAK,IAAG;MACjBb,OAAO,CAACa,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEAY,aAAaA,CAAClB,SAAiB,EAAEH,QAAgB,EAAEsB,KAAa;IAC9D,OAAO,IAAI,CAAChC,IAAI,CAACyB,IAAI,CAAC,GAAG,IAAI,CAACxB,MAAM,2BAA2B,EAAE;MAC/DY,SAAS;MACTH,QAAQ;MACRsB,KAAK;MACLN,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAACZ,IAAI,CACLlB,UAAU,CAACsB,KAAK,IAAG;MACjBb,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEA;EACAc,gBAAgBA,CAAC7B,MAAc;IAC7B,OAAO,IAAI,CAACJ,IAAI,CAACc,GAAG,CAAM,GAAG,IAAI,CAACb,MAAM,mBAAmBG,MAAM,EAAE,CAAC,CACjEW,IAAI,CACHnB,GAAG,CAACoB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAACgB,mBAAmB,CAAC9B,MAAM,CAAC,CAAC,EACpFP,UAAU,CAACsB,KAAK,IAAG;MACjBb,OAAO,CAACa,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,CAAC,IAAI,CAACe,mBAAmB,CAAC9B,MAAM,CAAC,CAAC;IAC3C,CAAC,CAAC,CACH;EACL;EAEA;EACA+B,0BAA0BA,CAACzB,QAAgB,EAAEL,KAAA,GAAgB,CAAC;IAC5D;IACAC,OAAO,CAACC,GAAG,CAAC,eAAeG,QAAQ,iCAAiC,CAAC;IACrE,OAAO,IAAI,CAAC0B,2BAA2B,CAAC1B,QAAQ,EAAEL,KAAK,CAAC;IAExD;;;;;;;;;;EAUF;EAEA;EACQG,4BAA4BA,CAACH,KAAa;IAChD;IACA,MAAMgC,YAAY,GAA4B,CAC5C;MACEC,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE,kDAAkD;MAC/DR,KAAK,EAAE,GAAG;MACVS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC/HpC,QAAQ,EAAE,KAAK;MACfqC,WAAW,EAAE,QAAQ;MACrBC,KAAK,EAAE,aAAa;MACpBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;MACtCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,IAAI;MACzBC,oBAAoB,EAAE;KACvB,CACF;IACD,OAAO,IAAI9D,UAAU,CAAC+D,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAACrB,YAAY,CAACsB,KAAK,CAAC,CAAC,EAAEtD,KAAK,CAAC,CAAC;MAC3CoD,QAAQ,CAACG,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQjD,2BAA2BA,CAACN,KAAa;IAC/C,MAAMwD,YAAY,GAAsB,CACtC;MACEvB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,4CAA4C;MACzDR,KAAK,EAAE,IAAI;MACXS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,cAAc;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC7HpC,QAAQ,EAAE,OAAO;MACjBqC,WAAW,EAAE,SAAS;MACtBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnCC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;MACrCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBQ,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,uBAAuB;MACvCC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE;KACjB,EACD;MACE7B,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE,0CAA0C;MACvDR,KAAK,EAAE,GAAG;MACVS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC/HpC,QAAQ,EAAE,KAAK;MACfqC,WAAW,EAAE,QAAQ;MACrBC,KAAK,EAAE,aAAa;MACpBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;MACvCC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBQ,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,uBAAuB;MACvCC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,GAAG;MACfC,cAAc,EAAE;KACjB,EACD;MACE7B,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,sBAAsB;MAC5BC,WAAW,EAAE,sCAAsC;MACnDR,KAAK,EAAE,IAAI;MACXS,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE,oEAAoE;QAAEC,GAAG,EAAE,cAAc;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC;MAC7HpC,QAAQ,EAAE,OAAO;MACjBqC,WAAW,EAAE,QAAQ;MACrBC,KAAK,EAAE,YAAY;MACnBC,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCC,IAAI,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,SAAS,CAAC;MAC1CC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBQ,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,0BAA0B;MAC1CC,SAAS,EAAE,IAAI;MACfC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE,GAAG;MACfC,cAAc,EAAE;KACjB,CACF;IACD,OAAO,IAAIzE,UAAU,CAAC+D,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAACG,YAAY,CAACF,KAAK,CAAC,CAAC,EAAEtD,KAAK,CAAC,CAAC;MAC3CoD,QAAQ,CAACG,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQxC,0BAA0BA,CAACf,KAAa;IAC9C,OAAO,IAAI,CAACG,4BAA4B,CAACH,KAAK,CAAC;EACjD;EAEQiB,yBAAyBA,CAACjB,KAAa;IAC7C,OAAO,IAAI,CAACG,4BAA4B,CAACH,KAAK,CAAC;EACjD;EAEQ+B,2BAA2BA,CAAC1B,QAAgB,EAAEL,KAAa;IACjE,OAAO,IAAI,CAACG,4BAA4B,CAACH,KAAK,CAAC;EACjD;EAEQ6B,mBAAmBA,CAAC9B,MAAc;IACxC,OAAO;MACLA,MAAM;MACNgE,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MACjBC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC;MACpDC,UAAU,EAAE;QAAEC,GAAG,EAAE,GAAG;QAAEC,GAAG,EAAE;MAAI,CAAE;MACnCC,gBAAgB,EAAE;KACnB;EACH;EAEA;EACAC,mBAAmBA,CAACC,SAAwB;IAC1C,IAAI,CAAC7E,cAAc,CAACwD,IAAI,CAACqB,SAAS,CAAC;EACrC;EAEA;EACAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAC9E,cAAc,CAAC+E,YAAY,EAAE;EAC3C;;;uBA7RWnF,qBAAqB,EAAAoF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArBvF,qBAAqB;MAAAwF,OAAA,EAArBxF,qBAAqB,CAAAyF,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}