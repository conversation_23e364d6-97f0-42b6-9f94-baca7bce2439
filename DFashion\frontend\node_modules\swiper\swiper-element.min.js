/**
 * Swiper Custom Element 11.2.8
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: May 23, 2025
 */

!function(){"use strict";function e(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function t(s,i){void 0===s&&(s={}),void 0===i&&(i={});const r=["__proto__","constructor","prototype"];Object.keys(i).filter((e=>r.indexOf(e)<0)).forEach((r=>{void 0===s[r]?s[r]=i[r]:e(i[r])&&e(s[r])&&Object.keys(i[r]).length>0&&t(s[r],i[r])}))}const s={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function i(){const e="undefined"!=typeof document?document:{};return t(e,s),e}const r={document:s,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function n(){const e="undefined"!=typeof window?window:{};return t(e,r),e}function a(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function o(){return Date.now()}function l(e,t){void 0===t&&(t="x");const s=n();let i,r,a;const o=function(e){const t=n();let s;return t.getComputedStyle&&(s=t.getComputedStyle(e,null)),!s&&e.currentStyle&&(s=e.currentStyle),s||(s=e.style),s}(e);return s.WebKitCSSMatrix?(r=o.transform||o.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map((e=>e.replace(",","."))).join(", ")),a=new s.WebKitCSSMatrix("none"===r?"":r)):(a=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=a.toString().split(",")),"x"===t&&(r=s.WebKitCSSMatrix?a.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(r=s.WebKitCSSMatrix?a.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),r||0}function d(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function c(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const r=i<0||arguments.length<=i?void 0:arguments[i];if(null!=r&&(s=r,!("undefined"!=typeof window&&void 0!==window.HTMLElement?s instanceof HTMLElement:s&&(1===s.nodeType||11===s.nodeType)))){const s=Object.keys(Object(r)).filter((e=>t.indexOf(e)<0));for(let t=0,i=s.length;t<i;t+=1){const i=s[t],n=Object.getOwnPropertyDescriptor(r,i);void 0!==n&&n.enumerable&&(d(e[i])&&d(r[i])?r[i].__swiper__?e[i]=r[i]:c(e[i],r[i]):!d(e[i])&&d(r[i])?(e[i]={},r[i].__swiper__?e[i]=r[i]:c(e[i],r[i])):e[i]=r[i])}}}var s;return e}function p(e,t,s){e.style.setProperty(t,s)}function u(e){let{swiper:t,targetPosition:s,side:i}=e;const r=n(),a=-t.translate;let o,l=null;const d=t.params.speed;t.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(t.cssModeFrameID);const c=s>a?"next":"prev",p=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,u=()=>{o=(new Date).getTime(),null===l&&(l=o);const e=Math.max(Math.min((o-l)/d,1),0),n=.5-Math.cos(e*Math.PI)/2;let c=a+n*(s-a);if(p(c,s)&&(c=s),t.wrapperEl.scrollTo({[i]:c}),p(c,s))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[i]:c})})),void r.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=r.requestAnimationFrame(u)};u()}function h(e,t){void 0===t&&(t="");const s=n(),i=[...e.children];return s.HTMLSlotElement&&e instanceof HTMLSlotElement&&i.push(...e.assignedElements()),t?i.filter((e=>e.matches(t))):i}function f(e){try{return void console.warn(e)}catch(e){}}function m(e,t){void 0===t&&(t=[]);const s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:function(e){return void 0===e&&(e=""),e.trim().split(" ").filter((e=>!!e.trim()))}(t)),s}function v(e,t){return n().getComputedStyle(e,null).getPropertyValue(t)}function g(e){let t,s=e;if(s){for(t=0;null!==(s=s.previousSibling);)1===s.nodeType&&(t+=1);return t}}function w(e,t,s){const i=n();return s?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function b(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}let S,T,y;function x(){return S||(S=function(){const e=n(),t=i();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),S}function E(e){return void 0===e&&(e={}),T||(T=function(e){let{userAgent:t}=void 0===e?{}:e;const s=x(),i=n(),r=i.navigator.platform,a=t||i.navigator.userAgent,o={ios:!1,android:!1},l=i.screen.width,d=i.screen.height,c=a.match(/(Android);?[\s\/]+([\d.]+)?/);let p=a.match(/(iPad).*OS\s([\d_]+)/);const u=a.match(/(iPod)(.*OS\s([\d_]+))?/),h=!p&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="Win32"===r;let m="MacIntel"===r;return!p&&m&&s.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${d}`)>=0&&(p=a.match(/(Version)\/([\d.]+)/),p||(p=[0,1,"13_0_0"]),m=!1),c&&!f&&(o.os="android",o.android=!0),(p||h||u)&&(o.os="ios",o.ios=!0),o}(e)),T}function M(){return y||(y=function(){const e=n(),t=E();let s=!1;function i(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(i()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,i]=t.split("Version/")[1].split(" ")[0].split(".").map((e=>Number(e)));s=e<16||16===e&&i<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),a=i();return{isSafari:s||a,needPerspectiveFix:s,need3dFix:a||r&&t.ios,isWebView:r}}()),y}var C={on(e,t,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof t)return i;const r=s?"unshift":"push";return e.split(" ").forEach((e=>{i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][r](t)})),i},once(e,t,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof t)return i;function r(){i.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var s=arguments.length,n=new Array(s),a=0;a<s;a++)n[a]=arguments[a];t.apply(i,n)}return r.__emitterProxy=t,i.on(e,r,s)},onAny(e,t){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof e)return s;const i=t?"unshift":"push";return s.eventsAnyListeners.indexOf(e)<0&&s.eventsAnyListeners[i](e),s},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const s=t.eventsAnyListeners.indexOf(e);return s>=0&&t.eventsAnyListeners.splice(s,1),t},off(e,t){const s=this;return!s.eventsListeners||s.destroyed?s:s.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?s.eventsListeners[e]=[]:s.eventsListeners[e]&&s.eventsListeners[e].forEach(((i,r)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&s.eventsListeners[e].splice(r,1)}))})),s):s},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,s,i;for(var r=arguments.length,n=new Array(r),a=0;a<r;a++)n[a]=arguments[a];"string"==typeof n[0]||Array.isArray(n[0])?(t=n[0],s=n.slice(1,n.length),i=e):(t=n[0].events,s=n[0].data,i=n[0].context||e),s.unshift(i);return(Array.isArray(t)?t:t.split(" ")).forEach((t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach((e=>{e.apply(i,[t,...s])})),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach((e=>{e.apply(i,s)}))})),e}};const P=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)};const k=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)};const L=(e,t)=>{if(!e||e.destroyed||!e.params)return;const s=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(s){let t=s.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(s.shadowRoot?t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame((()=>{s.shadowRoot&&(t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())}))),t&&t.remove()}},z=(e,t)=>{if(!e.slides[t])return;const s=e.slides[t].querySelector('[loading="lazy"]');s&&s.removeAttribute("loading")},I=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const s=e.slides.length;if(!s||!t||t<0)return;t=Math.min(t,s);const i="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const s=r,n=[s-t];return n.push(...Array.from({length:t}).map(((e,t)=>s+i+t))),void e.slides.forEach(((t,s)=>{n.includes(t.column)&&z(e,s)}))}const n=r+i-1;if(e.params.rewind||e.params.loop)for(let i=r-t;i<=n+t;i+=1){const t=(i%s+s)%s;(t<r||t>n)&&z(e,t)}else for(let i=Math.max(r-t,0);i<=Math.min(n+t,s-1);i+=1)i!==r&&(i>n||i<r)&&z(e,i)};var O={updateSize:function(){const e=this;let t,s;const i=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:i.clientWidth,s=void 0!==e.params.height&&null!==e.params.height?e.params.height:i.clientHeight,0===t&&e.isHorizontal()||0===s&&e.isVertical()||(t=t-parseInt(v(i,"padding-left")||0,10)-parseInt(v(i,"padding-right")||0,10),s=s-parseInt(v(i,"padding-top")||0,10)-parseInt(v(i,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(s)&&(s=0),Object.assign(e,{width:t,height:s,size:e.isHorizontal()?t:s}))},updateSlides:function(){const e=this;function t(t,s){return parseFloat(t.getPropertyValue(e.getDirectionLabel(s))||0)}const s=e.params,{wrapperEl:i,slidesEl:r,size:n,rtlTranslate:a,wrongRTL:o}=e,l=e.virtual&&s.virtual.enabled,d=l?e.virtual.slides.length:e.slides.length,c=h(r,`.${e.params.slideClass}, swiper-slide`),u=l?e.virtual.slides.length:c.length;let f=[];const m=[],g=[];let b=s.slidesOffsetBefore;"function"==typeof b&&(b=s.slidesOffsetBefore.call(e));let S=s.slidesOffsetAfter;"function"==typeof S&&(S=s.slidesOffsetAfter.call(e));const T=e.snapGrid.length,y=e.slidesGrid.length;let x=s.spaceBetween,E=-b,M=0,C=0;if(void 0===n)return;"string"==typeof x&&x.indexOf("%")>=0?x=parseFloat(x.replace("%",""))/100*n:"string"==typeof x&&(x=parseFloat(x)),e.virtualSize=-x,c.forEach((e=>{a?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""})),s.centeredSlides&&s.cssMode&&(p(i,"--swiper-centered-offset-before",""),p(i,"--swiper-centered-offset-after",""));const P=s.grid&&s.grid.rows>1&&e.grid;let k;P?e.grid.initSlides(c):e.grid&&e.grid.unsetSlides();const L="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter((e=>void 0!==s.breakpoints[e].slidesPerView)).length>0;for(let i=0;i<u;i+=1){let r;if(k=0,c[i]&&(r=c[i]),P&&e.grid.updateSlide(i,r,c),!c[i]||"none"!==v(r,"display")){if("auto"===s.slidesPerView){L&&(c[i].style[e.getDirectionLabel("width")]="");const n=getComputedStyle(r),a=r.style.transform,o=r.style.webkitTransform;if(a&&(r.style.transform="none"),o&&(r.style.webkitTransform="none"),s.roundLengths)k=e.isHorizontal()?w(r,"width",!0):w(r,"height",!0);else{const e=t(n,"width"),s=t(n,"padding-left"),i=t(n,"padding-right"),a=t(n,"margin-left"),o=t(n,"margin-right"),l=n.getPropertyValue("box-sizing");if(l&&"border-box"===l)k=e+a+o;else{const{clientWidth:t,offsetWidth:n}=r;k=e+s+i+a+o+(n-t)}}a&&(r.style.transform=a),o&&(r.style.webkitTransform=o),s.roundLengths&&(k=Math.floor(k))}else k=(n-(s.slidesPerView-1)*x)/s.slidesPerView,s.roundLengths&&(k=Math.floor(k)),c[i]&&(c[i].style[e.getDirectionLabel("width")]=`${k}px`);c[i]&&(c[i].swiperSlideSize=k),g.push(k),s.centeredSlides?(E=E+k/2+M/2+x,0===M&&0!==i&&(E=E-n/2-x),0===i&&(E=E-n/2-x),Math.abs(E)<.001&&(E=0),s.roundLengths&&(E=Math.floor(E)),C%s.slidesPerGroup==0&&f.push(E),m.push(E)):(s.roundLengths&&(E=Math.floor(E)),(C-Math.min(e.params.slidesPerGroupSkip,C))%e.params.slidesPerGroup==0&&f.push(E),m.push(E),E=E+k+x),e.virtualSize+=k+x,M=k,C+=1}}if(e.virtualSize=Math.max(e.virtualSize,n)+S,a&&o&&("slide"===s.effect||"coverflow"===s.effect)&&(i.style.width=`${e.virtualSize+x}px`),s.setWrapperSize&&(i.style[e.getDirectionLabel("width")]=`${e.virtualSize+x}px`),P&&e.grid.updateWrapperSize(k,f),!s.centeredSlides){const t=[];for(let i=0;i<f.length;i+=1){let r=f[i];s.roundLengths&&(r=Math.floor(r)),f[i]<=e.virtualSize-n&&t.push(r)}f=t,Math.floor(e.virtualSize-n)-Math.floor(f[f.length-1])>1&&f.push(e.virtualSize-n)}if(l&&s.loop){const t=g[0]+x;if(s.slidesPerGroup>1){const i=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/s.slidesPerGroup),r=t*s.slidesPerGroup;for(let e=0;e<i;e+=1)f.push(f[f.length-1]+r)}for(let i=0;i<e.virtual.slidesBefore+e.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&f.push(f[f.length-1]+t),m.push(m[m.length-1]+t),e.virtualSize+=t}if(0===f.length&&(f=[0]),0!==x){const t=e.isHorizontal()&&a?"marginLeft":e.getDirectionLabel("marginRight");c.filter(((e,t)=>!(s.cssMode&&!s.loop)||t!==c.length-1)).forEach((e=>{e.style[t]=`${x}px`}))}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;g.forEach((t=>{e+=t+(x||0)})),e-=x;const t=e>n?e-n:0;f=f.map((e=>e<=0?-b:e>t?t+S:e))}if(s.centerInsufficientSlides){let e=0;g.forEach((t=>{e+=t+(x||0)})),e-=x;const t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<n){const s=(n-e-t)/2;f.forEach(((e,t)=>{f[t]=e-s})),m.forEach(((e,t)=>{m[t]=e+s}))}}if(Object.assign(e,{slides:c,snapGrid:f,slidesGrid:m,slidesSizesGrid:g}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){p(i,"--swiper-centered-offset-before",-f[0]+"px"),p(i,"--swiper-centered-offset-after",e.size/2-g[g.length-1]/2+"px");const t=-e.snapGrid[0],s=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=>e+t)),e.slidesGrid=e.slidesGrid.map((e=>e+s))}if(u!==d&&e.emit("slidesLengthChange"),f.length!==T&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),m.length!==y&&e.emit("slidesGridLengthChange"),s.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(l||s.cssMode||"slide"!==s.effect&&"fade"!==s.effect)){const t=`${s.containerModifierClass}backface-hidden`,i=e.el.classList.contains(t);u<=s.maxBackfaceHiddenSlides?i||e.el.classList.add(t):i&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,s=[],i=t.virtual&&t.params.virtual.enabled;let r,n=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const a=e=>i?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach((e=>{s.push(e)}));else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const e=t.activeIndex+r;if(e>t.slides.length&&!i)break;s.push(a(e))}else s.push(a(t.activeIndex));for(r=0;r<s.length;r+=1)if(void 0!==s[r]){const e=s[r].offsetHeight;n=e>n?e:n}(n||0===n)&&(t.wrapperEl.style.height=`${n}px`)},updateSlidesOffset:function(){const e=this,t=e.slides,s=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let i=0;i<t.length;i+=1)t[i].swiperSlideOffset=(e.isHorizontal()?t[i].offsetLeft:t[i].offsetTop)-s-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,s=t.params,{slides:i,rtlTranslate:r,snapGrid:n}=t;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&t.updateSlidesOffset();let a=-e;r&&(a=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=s.spaceBetween;"string"==typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(let e=0;e<i.length;e+=1){const l=i[e];let d=l.swiperSlideOffset;s.cssMode&&s.centeredSlides&&(d-=i[0].swiperSlideOffset);const c=(a+(s.centeredSlides?t.minTranslate():0)-d)/(l.swiperSlideSize+o),p=(a-n[0]+(s.centeredSlides?t.minTranslate():0)-d)/(l.swiperSlideSize+o),u=-(a-d),h=u+t.slidesSizesGrid[e],f=u>=0&&u<=t.size-t.slidesSizesGrid[e],m=u>=0&&u<t.size-1||h>1&&h<=t.size||u<=0&&h>=t.size;m&&(t.visibleSlides.push(l),t.visibleSlidesIndexes.push(e)),P(l,m,s.slideVisibleClass),P(l,f,s.slideFullyVisibleClass),l.progress=r?-c:c,l.originalProgress=r?-p:p}},updateProgress:function(e){const t=this;if(void 0===e){const s=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*s||0}const s=t.params,i=t.maxTranslate()-t.minTranslate();let{progress:r,isBeginning:n,isEnd:a,progressLoop:o}=t;const l=n,d=a;if(0===i)r=0,n=!0,a=!0;else{r=(e-t.minTranslate())/i;const s=Math.abs(e-t.minTranslate())<1,o=Math.abs(e-t.maxTranslate())<1;n=s||r<=0,a=o||r>=1,s&&(r=0),o&&(r=1)}if(s.loop){const s=t.getSlideIndexByData(0),i=t.getSlideIndexByData(t.slides.length-1),r=t.slidesGrid[s],n=t.slidesGrid[i],a=t.slidesGrid[t.slidesGrid.length-1],l=Math.abs(e);o=l>=r?(l-r)/a:(l+a-n)/a,o>1&&(o-=1)}Object.assign(t,{progress:r,progressLoop:o,isBeginning:n,isEnd:a}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&t.updateSlidesProgress(e),n&&!l&&t.emit("reachBeginning toEdge"),a&&!d&&t.emit("reachEnd toEdge"),(l&&!n||d&&!a)&&t.emit("fromEdge"),t.emit("progress",r)},updateSlidesClasses:function(){const e=this,{slides:t,params:s,slidesEl:i,activeIndex:r}=e,n=e.virtual&&s.virtual.enabled,a=e.grid&&s.grid&&s.grid.rows>1,o=e=>h(i,`.${s.slideClass}${e}, swiper-slide${e}`)[0];let l,d,c;if(n)if(s.loop){let t=r-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),l=o(`[data-swiper-slide-index="${t}"]`)}else l=o(`[data-swiper-slide-index="${r}"]`);else a?(l=t.find((e=>e.column===r)),c=t.find((e=>e.column===r+1)),d=t.find((e=>e.column===r-1))):l=t[r];l&&(a||(c=function(e,t){const s=[];for(;e.nextElementSibling;){const i=e.nextElementSibling;t?i.matches(t)&&s.push(i):s.push(i),e=i}return s}(l,`.${s.slideClass}, swiper-slide`)[0],s.loop&&!c&&(c=t[0]),d=function(e,t){const s=[];for(;e.previousElementSibling;){const i=e.previousElementSibling;t?i.matches(t)&&s.push(i):s.push(i),e=i}return s}(l,`.${s.slideClass}, swiper-slide`)[0],s.loop&&0===!d&&(d=t[t.length-1]))),t.forEach((e=>{k(e,e===l,s.slideActiveClass),k(e,e===c,s.slideNextClass),k(e,e===d,s.slidePrevClass)})),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,s=t.rtlTranslate?t.translate:-t.translate,{snapGrid:i,params:r,activeIndex:n,realIndex:a,snapIndex:o}=t;let l,d=e;const c=e=>{let s=e-t.virtual.slidesBefore;return s<0&&(s=t.virtual.slides.length+s),s>=t.virtual.slides.length&&(s-=t.virtual.slides.length),s};if(void 0===d&&(d=function(e){const{slidesGrid:t,params:s}=e,i=e.rtlTranslate?e.translate:-e.translate;let r;for(let e=0;e<t.length;e+=1)void 0!==t[e+1]?i>=t[e]&&i<t[e+1]-(t[e+1]-t[e])/2?r=e:i>=t[e]&&i<t[e+1]&&(r=e+1):i>=t[e]&&(r=e);return s.normalizeSlideIndex&&(r<0||void 0===r)&&(r=0),r}(t)),i.indexOf(s)>=0)l=i.indexOf(s);else{const e=Math.min(r.slidesPerGroupSkip,d);l=e+Math.floor((d-e)/r.slidesPerGroup)}if(l>=i.length&&(l=i.length-1),d===n&&!t.params.loop)return void(l!==o&&(t.snapIndex=l,t.emit("snapIndexChange")));if(d===n&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=c(d));const p=t.grid&&r.grid&&r.grid.rows>1;let u;if(t.virtual&&r.virtual.enabled&&r.loop)u=c(d);else if(p){const e=t.slides.find((e=>e.column===d));let s=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(s)&&(s=Math.max(t.slides.indexOf(e),0)),u=Math.floor(s/r.grid.rows)}else if(t.slides[d]){const e=t.slides[d].getAttribute("data-swiper-slide-index");u=e?parseInt(e,10):d}else u=d;Object.assign(t,{previousSnapIndex:o,snapIndex:l,previousRealIndex:a,realIndex:u,previousIndex:n,activeIndex:d}),t.initialized&&I(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(a!==u&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const s=this,i=s.params;let r=e.closest(`.${i.slideClass}, swiper-slide`);!r&&s.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach((e=>{!r&&e.matches&&e.matches(`.${i.slideClass}, swiper-slide`)&&(r=e)}));let n,a=!1;if(r)for(let e=0;e<s.slides.length;e+=1)if(s.slides[e]===r){a=!0,n=e;break}if(!r||!a)return s.clickedSlide=void 0,void(s.clickedIndex=void 0);s.clickedSlide=r,s.virtual&&s.params.virtual.enabled?s.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):s.clickedIndex=n,i.slideToClickedSlide&&void 0!==s.clickedIndex&&s.clickedIndex!==s.activeIndex&&s.slideToClickedSlide()}};var _={getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:s,translate:i,wrapperEl:r}=this;if(t.virtualTranslate)return s?-i:i;if(t.cssMode)return i;let n=l(r,e);return n+=this.cssOverflowAdjustment(),s&&(n=-n),n||0},setTranslate:function(e,t){const s=this,{rtlTranslate:i,params:r,wrapperEl:n,progress:a}=s;let o,l=0,d=0;s.isHorizontal()?l=i?-e:e:d=e,r.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?l:d,r.cssMode?n[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-l:-d:r.virtualTranslate||(s.isHorizontal()?l-=s.cssOverflowAdjustment():d-=s.cssOverflowAdjustment(),n.style.transform=`translate3d(${l}px, ${d}px, 0px)`);const c=s.maxTranslate()-s.minTranslate();o=0===c?0:(e-s.minTranslate())/c,o!==a&&s.updateProgress(e),s.emit("setTranslate",s.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,s,i,r){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===s&&(s=!0),void 0===i&&(i=!0);const n=this,{params:a,wrapperEl:o}=n;if(n.animating&&a.preventInteractionOnTransition)return!1;const l=n.minTranslate(),d=n.maxTranslate();let c;if(c=i&&e>l?l:i&&e<d?d:e,n.updateProgress(c),a.cssMode){const e=n.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-c;else{if(!n.support.smoothScroll)return u({swiper:n,targetPosition:-c,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===t?(n.setTransition(0),n.setTranslate(c),s&&(n.emit("beforeTransitionStart",t,r),n.emit("transitionEnd"))):(n.setTransition(t),n.setTranslate(c),s&&(n.emit("beforeTransitionStart",t,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(e){n&&!n.destroyed&&e.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,n.animating=!1,s&&n.emit("transitionEnd"))}),n.wrapperEl.addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd))),!0}};function A(e){let{swiper:t,runCallbacks:s,direction:i,step:r}=e;const{activeIndex:n,previousIndex:a}=t;let o=i;o||(o=n>a?"next":n<a?"prev":"reset"),t.emit(`transition${r}`),s&&"reset"===o?t.emit(`slideResetTransition${r}`):s&&n!==a&&(t.emit(`slideChangeTransition${r}`),"next"===o?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}var G={slideTo:function(e,t,s,i,r){void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e&&(e=parseInt(e,10));const n=this;let a=e;a<0&&(a=0);const{params:o,snapGrid:l,slidesGrid:d,previousIndex:c,activeIndex:p,rtlTranslate:h,wrapperEl:f,enabled:m}=n;if(!m&&!i&&!r||n.destroyed||n.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=n.params.speed);const v=Math.min(n.params.slidesPerGroupSkip,a);let g=v+Math.floor((a-v)/n.params.slidesPerGroup);g>=l.length&&(g=l.length-1);const w=-l[g];if(o.normalizeSlideIndex)for(let e=0;e<d.length;e+=1){const t=-Math.floor(100*w),s=Math.floor(100*d[e]),i=Math.floor(100*d[e+1]);void 0!==d[e+1]?t>=s&&t<i-(i-s)/2?a=e:t>=s&&t<i&&(a=e+1):t>=s&&(a=e)}if(n.initialized&&a!==p){if(!n.allowSlideNext&&(h?w>n.translate&&w>n.minTranslate():w<n.translate&&w<n.minTranslate()))return!1;if(!n.allowSlidePrev&&w>n.translate&&w>n.maxTranslate()&&(p||0)!==a)return!1}let b;a!==(c||0)&&s&&n.emit("beforeSlideChangeStart"),n.updateProgress(w),b=a>p?"next":a<p?"prev":"reset";const S=n.virtual&&n.params.virtual.enabled;if(!(S&&r)&&(h&&-w===n.translate||!h&&w===n.translate))return n.updateActiveIndex(a),o.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),"slide"!==o.effect&&n.setTranslate(w),"reset"!==b&&(n.transitionStart(s,b),n.transitionEnd(s,b)),!1;if(o.cssMode){const e=n.isHorizontal(),s=h?w:-w;if(0===t)S&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),S&&!n._cssModeVirtualInitialSet&&n.params.initialSlide>0?(n._cssModeVirtualInitialSet=!0,requestAnimationFrame((()=>{f[e?"scrollLeft":"scrollTop"]=s}))):f[e?"scrollLeft":"scrollTop"]=s,S&&requestAnimationFrame((()=>{n.wrapperEl.style.scrollSnapType="",n._immediateVirtual=!1}));else{if(!n.support.smoothScroll)return u({swiper:n,targetPosition:s,side:e?"left":"top"}),!0;f.scrollTo({[e?"left":"top"]:s,behavior:"smooth"})}return!0}const T=M().isSafari;return S&&!r&&T&&n.isElement&&n.virtual.update(!1,!1,a),n.setTransition(t),n.setTranslate(w),n.updateActiveIndex(a),n.updateSlidesClasses(),n.emit("beforeTransitionStart",t,i),n.transitionStart(s,b),0===t?n.transitionEnd(s,b):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(e){n&&!n.destroyed&&e.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(s,b))}),n.wrapperEl.addEventListener("transitionend",n.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,s,i){if(void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e){e=parseInt(e,10)}const r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);const n=r.grid&&r.params.grid&&r.params.grid.rows>1;let a=e;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)a+=r.virtual.slidesBefore;else{let e;if(n){const t=a*r.params.grid.rows;e=r.slides.find((e=>1*e.getAttribute("data-swiper-slide-index")===t)).column}else e=r.getSlideIndexByData(a);const t=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:s}=r.params;let o=r.params.slidesPerView;"auto"===o?o=r.slidesPerViewDynamic():(o=Math.ceil(parseFloat(r.params.slidesPerView,10)),s&&o%2==0&&(o+=1));let l=t-e<o;if(s&&(l=l||e<Math.ceil(o/2)),i&&s&&"auto"!==r.params.slidesPerView&&!n&&(l=!1),l){const i=s?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:i,slideTo:!0,activeSlideIndex:"next"===i?e+1:e-t+1,slideRealIndex:"next"===i?r.realIndex:void 0})}if(n){const e=a*r.params.grid.rows;a=r.slides.find((t=>1*t.getAttribute("data-swiper-slide-index")===e)).column}else a=r.getSlideIndexByData(a)}return requestAnimationFrame((()=>{r.slideTo(a,t,s,i)})),r},slideNext:function(e,t,s){void 0===t&&(t=!0);const i=this,{enabled:r,params:n,animating:a}=i;if(!r||i.destroyed)return i;void 0===e&&(e=i.params.speed);let o=n.slidesPerGroup;"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(o=Math.max(i.slidesPerViewDynamic("current",!0),1));const l=i.activeIndex<n.slidesPerGroupSkip?1:o,d=i.virtual&&n.virtual.enabled;if(n.loop){if(a&&!d&&n.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&n.cssMode)return requestAnimationFrame((()=>{i.slideTo(i.activeIndex+l,e,t,s)})),!0}return n.rewind&&i.isEnd?i.slideTo(0,e,t,s):i.slideTo(i.activeIndex+l,e,t,s)},slidePrev:function(e,t,s){void 0===t&&(t=!0);const i=this,{params:r,snapGrid:n,slidesGrid:a,rtlTranslate:o,enabled:l,animating:d}=i;if(!l||i.destroyed)return i;void 0===e&&(e=i.params.speed);const c=i.virtual&&r.virtual.enabled;if(r.loop){if(d&&!c&&r.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=p(o?i.translate:-i.translate),h=n.map((e=>p(e))),f=r.freeMode&&r.freeMode.enabled;let m=n[h.indexOf(u)-1];if(void 0===m&&(r.cssMode||f)){let e;n.forEach(((t,s)=>{u>=t&&(e=s)})),void 0!==e&&(m=f?n[e]:n[e>0?e-1:e])}let v=0;if(void 0!==m&&(v=a.indexOf(m),v<0&&(v=i.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(v=v-i.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),r.rewind&&i.isBeginning){const r=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(r,e,t,s)}return r.loop&&0===i.activeIndex&&r.cssMode?(requestAnimationFrame((()=>{i.slideTo(v,e,t,s)})),!0):i.slideTo(v,e,t,s)},slideReset:function(e,t,s){void 0===t&&(t=!0);const i=this;if(!i.destroyed)return void 0===e&&(e=i.params.speed),i.slideTo(i.activeIndex,e,t,s)},slideToClosest:function(e,t,s,i){void 0===t&&(t=!0),void 0===i&&(i=.5);const r=this;if(r.destroyed)return;void 0===e&&(e=r.params.speed);let n=r.activeIndex;const a=Math.min(r.params.slidesPerGroupSkip,n),o=a+Math.floor((n-a)/r.params.slidesPerGroup),l=r.rtlTranslate?r.translate:-r.translate;if(l>=r.snapGrid[o]){const e=r.snapGrid[o];l-e>(r.snapGrid[o+1]-e)*i&&(n+=r.params.slidesPerGroup)}else{const e=r.snapGrid[o-1];l-e<=(r.snapGrid[o]-e)*i&&(n-=r.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,r.slidesGrid.length-1),r.slideTo(n,e,t,s)},slideToClickedSlide:function(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:s}=e,i="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let r,n=e.clickedIndex;const o=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;r=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?n<e.loopedSlides-i/2||n>e.slides.length-e.loopedSlides+i/2?(e.loopFix(),n=e.getSlideIndex(h(s,`${o}[data-swiper-slide-index="${r}"]`)[0]),a((()=>{e.slideTo(n)}))):e.slideTo(n):n>e.slides.length-i?(e.loopFix(),n=e.getSlideIndex(h(s,`${o}[data-swiper-slide-index="${r}"]`)[0]),a((()=>{e.slideTo(n)}))):e.slideTo(n)}else e.slideTo(n)}};var D={loopCreate:function(e,t){const s=this,{params:i,slidesEl:r}=s;if(!i.loop||s.virtual&&s.params.virtual.enabled)return;const n=()=>{h(r,`.${i.slideClass}, swiper-slide`).forEach(((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}))},a=s.grid&&i.grid&&i.grid.rows>1,o=i.slidesPerGroup*(a?i.grid.rows:1),l=s.slides.length%o!=0,d=a&&s.slides.length%i.grid.rows!=0,c=e=>{for(let t=0;t<e;t+=1){const e=s.isElement?m("swiper-slide",[i.slideBlankClass]):m("div",[i.slideClass,i.slideBlankClass]);s.slidesEl.append(e)}};if(l){if(i.loopAddBlankSlides){c(o-s.slides.length%o),s.recalcSlides(),s.updateSlides()}else f("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else if(d){if(i.loopAddBlankSlides){c(i.grid.rows-s.slides.length%i.grid.rows),s.recalcSlides(),s.updateSlides()}else f("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else n();s.loopFix({slideRealIndex:e,direction:i.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:s=!0,direction:i,setTranslate:r,activeSlideIndex:n,initial:a,byController:o,byMousewheel:l}=void 0===e?{}:e;const d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");const{slides:c,allowSlidePrev:p,allowSlideNext:u,slidesEl:h,params:m}=d,{centeredSlides:v,initialSlide:g}=m;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&m.virtual.enabled)return s&&(m.centeredSlides||0!==d.snapIndex?m.centeredSlides&&d.snapIndex<m.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=p,d.allowSlideNext=u,void d.emit("loopFix");let w=m.slidesPerView;"auto"===w?w=d.slidesPerViewDynamic():(w=Math.ceil(parseFloat(m.slidesPerView,10)),v&&w%2==0&&(w+=1));const b=m.slidesPerGroupAuto?w:m.slidesPerGroup;let S=b;S%b!=0&&(S+=b-S%b),S+=m.loopAdditionalSlides,d.loopedSlides=S;const T=d.grid&&m.grid&&m.grid.rows>1;c.length<w+S||"cards"===d.params.effect&&c.length<w+2*S?f("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):T&&"row"===m.grid.fill&&f("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const y=[],x=[],E=T?Math.ceil(c.length/m.grid.rows):c.length,M=a&&E-g<w&&!v;let C=M?g:d.activeIndex;void 0===n?n=d.getSlideIndex(c.find((e=>e.classList.contains(m.slideActiveClass)))):C=n;const P="next"===i||!i,k="prev"===i||!i;let L=0,z=0;const I=(T?c[n].column:n)+(v&&void 0===r?-w/2+.5:0);if(I<S){L=Math.max(S-I,b);for(let e=0;e<S-I;e+=1){const t=e-Math.floor(e/E)*E;if(T){const e=E-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&y.push(t)}else y.push(E-t-1)}}else if(I+w>E-S){z=Math.max(I-(E-2*S),b),M&&(z=Math.max(z,w-E+g+1));for(let e=0;e<z;e+=1){const t=e-Math.floor(e/E)*E;T?c.forEach(((e,s)=>{e.column===t&&x.push(s)})):x.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame((()=>{d.__preventObserver__=!1})),"cards"===d.params.effect&&c.length<w+2*S&&(x.includes(n)&&x.splice(x.indexOf(n),1),y.includes(n)&&y.splice(y.indexOf(n),1)),k&&y.forEach((e=>{c[e].swiperLoopMoveDOM=!0,h.prepend(c[e]),c[e].swiperLoopMoveDOM=!1})),P&&x.forEach((e=>{c[e].swiperLoopMoveDOM=!0,h.append(c[e]),c[e].swiperLoopMoveDOM=!1})),d.recalcSlides(),"auto"===m.slidesPerView?d.updateSlides():T&&(y.length>0&&k||x.length>0&&P)&&d.slides.forEach(((e,t)=>{d.grid.updateSlide(t,e,d.slides)})),m.watchSlidesProgress&&d.updateSlidesOffset(),s)if(y.length>0&&k){if(void 0===t){const e=d.slidesGrid[C],t=d.slidesGrid[C+L]-e;l?d.setTranslate(d.translate-t):(d.slideTo(C+Math.ceil(L),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(r){const e=T?y.length/m.grid.rows:y.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(x.length>0&&P)if(void 0===t){const e=d.slidesGrid[C],t=d.slidesGrid[C-z]-e;l?d.setTranslate(d.translate-t):(d.slideTo(C-z,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{const e=T?x.length/m.grid.rows:x.length;d.slideTo(d.activeIndex-e,0,!1,!0)}if(d.allowSlidePrev=p,d.allowSlideNext=u,d.controller&&d.controller.control&&!o){const e={slideRealIndex:t,direction:i,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach((t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&s})})):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===m.slidesPerView&&s})}d.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:s}=e;if(!t.loop||!s||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const i=[];e.slides.forEach((e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;i[t]=e})),e.slides.forEach((e=>{e.removeAttribute("data-swiper-slide-index")})),i.forEach((e=>{s.append(e)})),e.recalcSlides(),e.slideTo(e.realIndex,0)}};function B(e,t,s){const i=n(),{params:r}=e,a=r.edgeSwipeDetection,o=r.edgeSwipeThreshold;return!a||!(s<=o||s>=i.innerWidth-o)||"prevent"===a&&(t.preventDefault(),!0)}function N(e){const t=this,s=i();let r=e;r.originalEvent&&(r=r.originalEvent);const a=t.touchEventsData;if("pointerdown"===r.type){if(null!==a.pointerId&&a.pointerId!==r.pointerId)return;a.pointerId=r.pointerId}else"touchstart"===r.type&&1===r.targetTouches.length&&(a.touchId=r.targetTouches[0].identifier);if("touchstart"===r.type)return void B(t,r,r.targetTouches[0].pageX);const{params:l,touches:d,enabled:c}=t;if(!c)return;if(!l.simulateTouch&&"mouse"===r.pointerType)return;if(t.animating&&l.preventInteractionOnTransition)return;!t.animating&&l.cssMode&&l.loop&&t.loopFix();let p=r.target;if("wrapper"===l.touchEventsTarget&&!function(e,t){const s=n();let i=t.contains(e);!i&&s.HTMLSlotElement&&t instanceof HTMLSlotElement&&(i=[...t.assignedElements()].includes(e),i||(i=function(e,t){const s=[t];for(;s.length>0;){const t=s.shift();if(e===t)return!0;s.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t)));return i}(p,t.wrapperEl))return;if("which"in r&&3===r.which)return;if("button"in r&&r.button>0)return;if(a.isTouched&&a.isMoved)return;const u=!!l.noSwipingClass&&""!==l.noSwipingClass,h=r.composedPath?r.composedPath():r.path;u&&r.target&&r.target.shadowRoot&&h&&(p=h[0]);const f=l.noSwipingSelector?l.noSwipingSelector:`.${l.noSwipingClass}`,m=!(!r.target||!r.target.shadowRoot);if(l.noSwiping&&(m?function(e,t){return void 0===t&&(t=this),function t(s){if(!s||s===i()||s===n())return null;s.assignedSlot&&(s=s.assignedSlot);const r=s.closest(e);return r||s.getRootNode?r||t(s.getRootNode().host):null}(t)}(f,p):p.closest(f)))return void(t.allowClick=!0);if(l.swipeHandler&&!p.closest(l.swipeHandler))return;d.currentX=r.pageX,d.currentY=r.pageY;const v=d.currentX,g=d.currentY;if(!B(t,r,v))return;Object.assign(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),d.startX=v,d.startY=g,a.touchStartTime=o(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,l.threshold>0&&(a.allowThresholdMove=!1);let w=!0;p.matches(a.focusableElements)&&(w=!1,"SELECT"===p.nodeName&&(a.isTouched=!1)),s.activeElement&&s.activeElement.matches(a.focusableElements)&&s.activeElement!==p&&("mouse"===r.pointerType||"mouse"!==r.pointerType&&!p.matches(a.focusableElements))&&s.activeElement.blur();const b=w&&t.allowTouchMove&&l.touchStartPreventDefault;!l.touchStartForcePreventDefault&&!b||p.isContentEditable||r.preventDefault(),l.freeMode&&l.freeMode.enabled&&t.freeMode&&t.animating&&!l.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",r)}function V(e){const t=i(),s=this,r=s.touchEventsData,{params:n,touches:a,rtlTranslate:l,enabled:d}=s;if(!d)return;if(!n.simulateTouch&&"mouse"===e.pointerType)return;let c,p=e;if(p.originalEvent&&(p=p.originalEvent),"pointermove"===p.type){if(null!==r.touchId)return;if(p.pointerId!==r.pointerId)return}if("touchmove"===p.type){if(c=[...p.changedTouches].find((e=>e.identifier===r.touchId)),!c||c.identifier!==r.touchId)return}else c=p;if(!r.isTouched)return void(r.startMoving&&r.isScrolling&&s.emit("touchMoveOpposite",p));const u=c.pageX,h=c.pageY;if(p.preventedByNestedSwiper)return a.startX=u,void(a.startY=h);if(!s.allowTouchMove)return p.target.matches(r.focusableElements)||(s.allowClick=!1),void(r.isTouched&&(Object.assign(a,{startX:u,startY:h,currentX:u,currentY:h}),r.touchStartTime=o()));if(n.touchReleaseOnEdges&&!n.loop)if(s.isVertical()){if(h<a.startY&&s.translate<=s.maxTranslate()||h>a.startY&&s.translate>=s.minTranslate())return r.isTouched=!1,void(r.isMoved=!1)}else{if(l&&(u>a.startX&&-s.translate<=s.maxTranslate()||u<a.startX&&-s.translate>=s.minTranslate()))return;if(!l&&(u<a.startX&&s.translate<=s.maxTranslate()||u>a.startX&&s.translate>=s.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==p.target&&"mouse"!==p.pointerType&&t.activeElement.blur(),t.activeElement&&p.target===t.activeElement&&p.target.matches(r.focusableElements))return r.isMoved=!0,void(s.allowClick=!1);r.allowTouchCallbacks&&s.emit("touchMove",p),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=u,a.currentY=h;const f=a.currentX-a.startX,m=a.currentY-a.startY;if(s.params.threshold&&Math.sqrt(f**2+m**2)<s.params.threshold)return;if(void 0===r.isScrolling){let e;s.isHorizontal()&&a.currentY===a.startY||s.isVertical()&&a.currentX===a.startX?r.isScrolling=!1:f*f+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(f))/Math.PI,r.isScrolling=s.isHorizontal()?e>n.touchAngle:90-e>n.touchAngle)}if(r.isScrolling&&s.emit("touchMoveOpposite",p),void 0===r.startMoving&&(a.currentX===a.startX&&a.currentY===a.startY||(r.startMoving=!0)),r.isScrolling||"touchmove"===p.type&&r.preventTouchMoveFromPointerMove)return void(r.isTouched=!1);if(!r.startMoving)return;s.allowClick=!1,!n.cssMode&&p.cancelable&&p.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&p.stopPropagation();let v=s.isHorizontal()?f:m,g=s.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;n.oneWayMovement&&(v=Math.abs(v)*(l?1:-1),g=Math.abs(g)*(l?1:-1)),a.diff=v,v*=n.touchRatio,l&&(v=-v,g=-g);const w=s.touchesDirection;s.swipeDirection=v>0?"prev":"next",s.touchesDirection=g>0?"prev":"next";const b=s.params.loop&&!n.cssMode,S="next"===s.touchesDirection&&s.allowSlideNext||"prev"===s.touchesDirection&&s.allowSlidePrev;if(!r.isMoved){if(b&&S&&s.loopFix({direction:s.swipeDirection}),r.startTranslate=s.getTranslate(),s.setTransition(0),s.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});s.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,!n.grabCursor||!0!==s.allowSlideNext&&!0!==s.allowSlidePrev||s.setGrabCursor(!0),s.emit("sliderFirstMove",p)}if((new Date).getTime(),!1!==n._loopSwapReset&&r.isMoved&&r.allowThresholdMove&&w!==s.touchesDirection&&b&&S&&Math.abs(v)>=1)return Object.assign(a,{startX:u,startY:h,currentX:u,currentY:h,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,void(r.startTranslate=r.currentTranslate);s.emit("sliderMove",p),r.isMoved=!0,r.currentTranslate=v+r.startTranslate;let T=!0,y=n.resistanceRatio;if(n.touchReleaseOnEdges&&(y=0),v>0?(b&&S&&r.allowThresholdMove&&r.currentTranslate>(n.centeredSlides?s.minTranslate()-s.slidesSizesGrid[s.activeIndex+1]-("auto"!==n.slidesPerView&&s.slides.length-n.slidesPerView>=2?s.slidesSizesGrid[s.activeIndex+1]+s.params.spaceBetween:0)-s.params.spaceBetween:s.minTranslate())&&s.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>s.minTranslate()&&(T=!1,n.resistance&&(r.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+r.startTranslate+v)**y))):v<0&&(b&&S&&r.allowThresholdMove&&r.currentTranslate<(n.centeredSlides?s.maxTranslate()+s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween+("auto"!==n.slidesPerView&&s.slides.length-n.slidesPerView>=2?s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween:0):s.maxTranslate())&&s.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:s.slides.length-("auto"===n.slidesPerView?s.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),r.currentTranslate<s.maxTranslate()&&(T=!1,n.resistance&&(r.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-r.startTranslate-v)**y))),T&&(p.preventedByNestedSwiper=!0),!s.allowSlideNext&&"next"===s.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!s.allowSlidePrev&&"prev"===s.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),s.allowSlidePrev||s.allowSlideNext||(r.currentTranslate=r.startTranslate),n.threshold>0){if(!(Math.abs(v)>n.threshold||r.allowThresholdMove))return void(r.currentTranslate=r.startTranslate);if(!r.allowThresholdMove)return r.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,r.currentTranslate=r.startTranslate,void(a.diff=s.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY)}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&s.freeMode||n.watchSlidesProgress)&&(s.updateActiveIndex(),s.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&s.freeMode&&s.freeMode.onTouchMove(),s.updateProgress(r.currentTranslate),s.setTranslate(r.currentTranslate))}function $(e){const t=this,s=t.touchEventsData;let i,r=e;r.originalEvent&&(r=r.originalEvent);if("touchend"===r.type||"touchcancel"===r.type){if(i=[...r.changedTouches].find((e=>e.identifier===s.touchId)),!i||i.identifier!==s.touchId)return}else{if(null!==s.touchId)return;if(r.pointerId!==s.pointerId)return;i=r}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(r.type)){if(!(["pointercancel","contextmenu"].includes(r.type)&&(t.browser.isSafari||t.browser.isWebView)))return}s.pointerId=null,s.touchId=null;const{params:n,touches:l,rtlTranslate:d,slidesGrid:c,enabled:p}=t;if(!p)return;if(!n.simulateTouch&&"mouse"===r.pointerType)return;if(s.allowTouchCallbacks&&t.emit("touchEnd",r),s.allowTouchCallbacks=!1,!s.isTouched)return s.isMoved&&n.grabCursor&&t.setGrabCursor(!1),s.isMoved=!1,void(s.startMoving=!1);n.grabCursor&&s.isMoved&&s.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const u=o(),h=u-s.touchStartTime;if(t.allowClick){const e=r.path||r.composedPath&&r.composedPath();t.updateClickedSlide(e&&e[0]||r.target,e),t.emit("tap click",r),h<300&&u-s.lastClickTime<300&&t.emit("doubleTap doubleClick",r)}if(s.lastClickTime=o(),a((()=>{t.destroyed||(t.allowClick=!0)})),!s.isTouched||!s.isMoved||!t.swipeDirection||0===l.diff&&!s.loopSwapReset||s.currentTranslate===s.startTranslate&&!s.loopSwapReset)return s.isTouched=!1,s.isMoved=!1,void(s.startMoving=!1);let f;if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,f=n.followFinger?d?t.translate:-t.translate:-s.currentTranslate,n.cssMode)return;if(n.freeMode&&n.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:f});const m=f>=-t.maxTranslate()&&!t.params.loop;let v=0,g=t.slidesSizesGrid[0];for(let e=0;e<c.length;e+=e<n.slidesPerGroupSkip?1:n.slidesPerGroup){const t=e<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;void 0!==c[e+t]?(m||f>=c[e]&&f<c[e+t])&&(v=e,g=c[e+t]-c[e]):(m||f>=c[e])&&(v=e,g=c[c.length-1]-c[c.length-2])}let w=null,b=null;n.rewind&&(t.isBeginning?b=n.virtual&&n.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(w=0));const S=(f-c[v])/g,T=v<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;if(h>n.longSwipesMs){if(!n.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(S>=n.longSwipesRatio?t.slideTo(n.rewind&&t.isEnd?w:v+T):t.slideTo(v)),"prev"===t.swipeDirection&&(S>1-n.longSwipesRatio?t.slideTo(v+T):null!==b&&S<0&&Math.abs(S)>n.longSwipesRatio?t.slideTo(b):t.slideTo(v))}else{if(!n.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&&(r.target===t.navigation.nextEl||r.target===t.navigation.prevEl)?r.target===t.navigation.nextEl?t.slideTo(v+T):t.slideTo(v):("next"===t.swipeDirection&&t.slideTo(null!==w?w:v+T),"prev"===t.swipeDirection&&t.slideTo(null!==b?b:v))}}function F(){const e=this,{params:t,el:s}=e;if(s&&0===s.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:r,snapGrid:n}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=a&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout((()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()}),500)),e.allowSlidePrev=r,e.allowSlideNext=i,e.params.watchOverflow&&n!==e.snapGrid&&e.checkOverflow()}function R(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function j(){const e=this,{wrapperEl:t,rtlTranslate:s,enabled:i}=e;if(!i)return;let r;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const n=e.maxTranslate()-e.minTranslate();r=0===n?0:(e.translate-e.minTranslate())/n,r!==e.progress&&e.updateProgress(s?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function H(e){const t=this;L(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function W(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const q=(e,t)=>{const s=i(),{params:r,el:n,wrapperEl:a,device:o}=e,l=!!r.nested,d="on"===t?"addEventListener":"removeEventListener",c=t;n&&"string"!=typeof n&&(s[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:l}),n[d]("touchstart",e.onTouchStart,{passive:!1}),n[d]("pointerdown",e.onTouchStart,{passive:!1}),s[d]("touchmove",e.onTouchMove,{passive:!1,capture:l}),s[d]("pointermove",e.onTouchMove,{passive:!1,capture:l}),s[d]("touchend",e.onTouchEnd,{passive:!0}),s[d]("pointerup",e.onTouchEnd,{passive:!0}),s[d]("pointercancel",e.onTouchEnd,{passive:!0}),s[d]("touchcancel",e.onTouchEnd,{passive:!0}),s[d]("pointerout",e.onTouchEnd,{passive:!0}),s[d]("pointerleave",e.onTouchEnd,{passive:!0}),s[d]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&n[d]("click",e.onClick,!0),r.cssMode&&a[d]("scroll",e.onScroll),r.updateOnWindowResize?e[c](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",F,!0):e[c]("observerUpdate",F,!0),n[d]("load",e.onLoad,{capture:!0}))};const X=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var Y={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function U(e,t){return function(s){void 0===s&&(s={});const i=Object.keys(s)[0],r=s[i];"object"==typeof r&&null!==r?(!0===e[i]&&(e[i]={enabled:!0}),"navigation"===i&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),i in e&&"enabled"in r?("object"!=typeof e[i]||"enabled"in e[i]||(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),c(t,s)):c(t,s)):c(t,s)}}const K={eventsEmitter:C,update:O,translate:_,transition:{setTransition:function(e,t){const s=this;s.params.cssMode||(s.wrapperEl.style.transitionDuration=`${e}ms`,s.wrapperEl.style.transitionDelay=0===e?"0ms":""),s.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const s=this,{params:i}=s;i.cssMode||(i.autoHeight&&s.updateAutoHeight(),A({swiper:s,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const s=this,{params:i}=s;s.animating=!1,i.cssMode||(s.setTransition(0),A({swiper:s,runCallbacks:e,direction:t,step:"End"}))}},slide:G,loop:D,grabCursor:{setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const s="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),s.style.cursor="move",s.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame((()=>{t.__preventObserver__=!1}))},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame((()=>{e.__preventObserver__=!1})))}},events:{attachEvents:function(){const e=this,{params:t}=e;e.onTouchStart=N.bind(e),e.onTouchMove=V.bind(e),e.onTouchEnd=$.bind(e),e.onDocumentTouchStart=W.bind(e),t.cssMode&&(e.onScroll=j.bind(e)),e.onClick=R.bind(e),e.onLoad=H.bind(e),q(e,"on")},detachEvents:function(){q(this,"off")}},breakpoints:{setBreakpoint:function(){const e=this,{realIndex:t,initialized:s,params:r,el:n}=e,a=r.breakpoints;if(!a||a&&0===Object.keys(a).length)return;const o=i(),l="window"!==r.breakpointsBase&&r.breakpointsBase?"container":r.breakpointsBase,d=["window","container"].includes(r.breakpointsBase)||!r.breakpointsBase?e.el:o.querySelector(r.breakpointsBase),p=e.getBreakpoint(a,l,d);if(!p||e.currentBreakpoint===p)return;const u=(p in a?a[p]:void 0)||e.originalParams,h=X(e,r),f=X(e,u),m=e.params.grabCursor,v=u.grabCursor,g=r.enabled;h&&!f?(n.classList.remove(`${r.containerModifierClass}grid`,`${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!h&&f&&(n.classList.add(`${r.containerModifierClass}grid`),(u.grid.fill&&"column"===u.grid.fill||!u.grid.fill&&"column"===r.grid.fill)&&n.classList.add(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),m&&!v?e.unsetGrabCursor():!m&&v&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach((t=>{if(void 0===u[t])return;const s=r[t]&&r[t].enabled,i=u[t]&&u[t].enabled;s&&!i&&e[t].disable(),!s&&i&&e[t].enable()}));const w=u.direction&&u.direction!==r.direction,b=r.loop&&(u.slidesPerView!==r.slidesPerView||w),S=r.loop;w&&s&&e.changeDirection(),c(e.params,u);const T=e.params.enabled,y=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),g&&!T?e.disable():!g&&T&&e.enable(),e.currentBreakpoint=p,e.emit("_beforeBreakpoint",u),s&&(b?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!S&&y?(e.loopCreate(t),e.updateSlides()):S&&!y&&e.loopDestroy()),e.emit("breakpoint",u)},getBreakpoint:function(e,t,s){if(void 0===t&&(t="window"),!e||"container"===t&&!s)return;let i=!1;const r=n(),a="window"===t?r.innerHeight:s.clientHeight,o=Object.keys(e).map((e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:a*t,point:e}}return{value:e,point:e}}));o.sort(((e,t)=>parseInt(e.value,10)-parseInt(t.value,10)));for(let e=0;e<o.length;e+=1){const{point:n,value:a}=o[e];"window"===t?r.matchMedia(`(min-width: ${a}px)`).matches&&(i=n):a<=s.clientWidth&&(i=n)}return i||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:s}=e,{slidesOffsetBefore:i}=s;if(i){const t=e.slides.length-1,s=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*i;e.isLocked=e.size>s}else e.isLocked=1===e.snapGrid.length;!0===s.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===s.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:s,rtl:i,el:r,device:n}=e,a=function(e,t){const s=[];return e.forEach((e=>{"object"==typeof e?Object.keys(e).forEach((i=>{e[i]&&s.push(t+i)})):"string"==typeof e&&s.push(t+e)})),s}(["initialized",s.direction,{"free-mode":e.params.freeMode&&s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:i},{grid:s.grid&&s.grid.rows>1},{"grid-column":s.grid&&s.grid.rows>1&&"column"===s.grid.fill},{android:n.android},{ios:n.ios},{"css-mode":s.cssMode},{centered:s.cssMode&&s.centeredSlides},{"watch-progress":s.watchSlidesProgress}],s.containerModifierClass);t.push(...a),r.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},Z={};class J{constructor(){let e,t;for(var s=arguments.length,r=new Array(s),n=0;n<s;n++)r[n]=arguments[n];1===r.length&&r[0].constructor&&"Object"===Object.prototype.toString.call(r[0]).slice(8,-1)?t=r[0]:[e,t]=r,t||(t={}),t=c({},t),e&&!t.el&&(t.el=e);const a=i();if(t.el&&"string"==typeof t.el&&a.querySelectorAll(t.el).length>1){const e=[];return a.querySelectorAll(t.el).forEach((s=>{const i=c({},t,{el:s});e.push(new J(i))})),e}const o=this;o.__swiper__=!0,o.support=x(),o.device=E({userAgent:t.userAgent}),o.browser=M(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],t.modules&&Array.isArray(t.modules)&&o.modules.push(...t.modules);const l={};o.modules.forEach((e=>{e({params:t,swiper:o,extendParams:U(t,l),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})}));const d=c({},Y,l);return o.params=c({},d,Z,t),o.originalParams=c({},o.params),o.passedParams=c({},t),o.params&&o.params.on&&Object.keys(o.params.on).forEach((e=>{o.on(e,o.params.on[e])})),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===o.params.direction,isVertical:()=>"vertical"===o.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:s}=this,i=g(h(t,`.${s.slideClass}, swiper-slide`)[0]);return g(e)-i}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find((t=>1*t.getAttribute("data-swiper-slide-index")===e)))}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=h(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const i=s.minTranslate(),r=(s.maxTranslate()-i)*e+i;s.translateTo(r,void 0===t?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter((e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach((s=>{const i=e.getSlideClasses(s);t.push({slideEl:s,classNames:i}),e.emit("_slideClass",s,i)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:s,slides:i,slidesGrid:r,slidesSizesGrid:n,size:a,activeIndex:o}=this;let l=1;if("number"==typeof s.slidesPerView)return s.slidesPerView;if(s.centeredSlides){let e,t=i[o]?Math.ceil(i[o].swiperSlideSize):0;for(let s=o+1;s<i.length;s+=1)i[s]&&!e&&(t+=Math.ceil(i[s].swiperSlideSize),l+=1,t>a&&(e=!0));for(let s=o-1;s>=0;s-=1)i[s]&&!e&&(t+=i[s].swiperSlideSize,l+=1,t>a&&(e=!0))}else if("current"===e)for(let e=o+1;e<i.length;e+=1){(t?r[e]+n[e]-r[o]<a:r[e]-r[o]<a)&&(l+=1)}else for(let e=o-1;e>=0;e-=1){r[o]-r[e]<a&&(l+=1)}return l}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;function i(){const t=e.rtlTranslate?-1*e.translate:e.translate,s=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(s),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach((t=>{t.complete&&L(e,t)})),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)i(),s.autoHeight&&e.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const t=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(t.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||i()}s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const s=this,i=s.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(s.el.classList.remove(`${s.params.containerModifierClass}${i}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach((t=>{"vertical"===e?t.style.width="":t.style.height=""})),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let s=e||t.params.el;if("string"==typeof s&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const i=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let r=(()=>{if(s&&s.shadowRoot&&s.shadowRoot.querySelector){return s.shadowRoot.querySelector(i())}return h(s,i())[0]})();return!r&&t.params.createElements&&(r=m("div",t.params.wrapperClass),s.append(r),h(s,`.${t.params.slideClass}`).forEach((e=>{r.append(e)}))),Object.assign(t,{el:s,wrapperEl:r,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:r,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:"rtl"===s.dir.toLowerCase()||"rtl"===v(s,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===s.dir.toLowerCase()||"rtl"===v(s,"direction")),wrongRTL:"-webkit-box"===v(r,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const s=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&s.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),s.forEach((e=>{e.complete?L(t,e):e.addEventListener("load",(e=>{L(t,e.target)}))})),I(t),t.initialized=!0,I(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const s=this,{params:i,el:r,wrapperEl:n,slides:a}=s;return void 0===s.params||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),i.loop&&s.loopDestroy(),t&&(s.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),n&&n.removeAttribute("style"),a&&a.length&&a.forEach((e=>{e.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}))),s.emit("destroy"),Object.keys(s.eventsListeners).forEach((e=>{s.off(e)})),!1!==e&&(s.el&&"string"!=typeof s.el&&(s.el.swiper=null),function(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}(s)),s.destroyed=!0),null}static extendDefaults(e){c(Z,e)}static get extendedDefaults(){return Z}static get defaults(){return Y}static installModule(e){J.prototype.__modules__||(J.prototype.__modules__=[]);const t=J.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach((e=>J.installModule(e))),J):(J.installModule(e),J)}}Object.keys(K).forEach((e=>{Object.keys(K[e]).forEach((t=>{J.prototype[t]=K[e][t]}))})),J.use([function(e){let{swiper:t,on:s,emit:i}=e;const r=n();let a=null,o=null;const l=()=>{t&&!t.destroyed&&t.initialized&&(i("beforeResize"),i("resize"))},d=()=>{t&&!t.destroyed&&t.initialized&&i("orientationchange")};s("init",(()=>{t.params.resizeObserver&&void 0!==r.ResizeObserver?t&&!t.destroyed&&t.initialized&&(a=new ResizeObserver((e=>{o=r.requestAnimationFrame((()=>{const{width:s,height:i}=t;let r=s,n=i;e.forEach((e=>{let{contentBoxSize:s,contentRect:i,target:a}=e;a&&a!==t.el||(r=i?i.width:(s[0]||s).inlineSize,n=i?i.height:(s[0]||s).blockSize)})),r===s&&n===i||l()}))})),a.observe(t.el)):(r.addEventListener("resize",l),r.addEventListener("orientationchange",d))})),s("destroy",(()=>{o&&r.cancelAnimationFrame(o),a&&a.unobserve&&t.el&&(a.unobserve(t.el),a=null),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",d)}))},function(e){let{swiper:t,extendParams:s,on:i,emit:r}=e;const a=[],o=n(),l=function(e,s){void 0===s&&(s={});const i=new(o.MutationObserver||o.WebkitMutationObserver)((e=>{if(t.__preventObserver__)return;if(1===e.length)return void r("observerUpdate",e[0]);const s=function(){r("observerUpdate",e[0])};o.requestAnimationFrame?o.requestAnimationFrame(s):o.setTimeout(s,0)}));i.observe(e,{attributes:void 0===s.attributes||s.attributes,childList:t.isElement||(void 0===s.childList||s).childList,characterData:void 0===s.characterData||s.characterData}),a.push(i)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",(()=>{if(t.params.observer){if(t.params.observeParents){const e=function(e,t){const s=[];let i=e.parentElement;for(;i;)t?i.matches(t)&&s.push(i):s.push(i),i=i.parentElement;return s}(t.hostEl);for(let t=0;t<e.length;t+=1)l(e[t])}l(t.hostEl,{childList:t.params.observeSlideChildren}),l(t.wrapperEl,{attributes:!1})}})),i("destroy",(()=>{a.forEach((e=>{e.disconnect()})),a.splice(0,a.length)}))}]);const Q=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function ee(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function te(e,t){const s=["__proto__","constructor","prototype"];Object.keys(t).filter((e=>s.indexOf(e)<0)).forEach((s=>{void 0===e[s]?e[s]=t[s]:ee(t[s])&&ee(e[s])&&Object.keys(t[s]).length>0?t[s].__swiper__?e[s]=t[s]:te(e[s],t[s]):e[s]=t[s]}))}function se(e){return void 0===e&&(e=""),e.replace(/-[a-z]/g,(e=>e.toUpperCase().replace("-","")))}const ie=e=>{if(parseFloat(e)===Number(e))return Number(e);if("true"===e)return!0;if(""===e)return!0;if("false"===e)return!1;if("null"===e)return null;if("undefined"!==e){if("string"==typeof e&&e.includes("{")&&e.includes("}")&&e.includes('"')){let t;try{t=JSON.parse(e)}catch(s){t=e}return t}return e}},re=["a11y","autoplay","controller","cards-effect","coverflow-effect","creative-effect","cube-effect","fade-effect","flip-effect","free-mode","grid","hash-navigation","history","keyboard","mousewheel","navigation","pagination","parallax","scrollbar","thumbs","virtual","zoom"];function ne(e,t,s){const i={},r={};te(i,Y);const n=[...Q,"on"],a=n.map((e=>e.replace(/_/,"")));n.forEach((t=>{t=t.replace("_",""),void 0!==e[t]&&(r[t]=e[t])}));const o=[...e.attributes];return"string"==typeof t&&void 0!==s&&o.push({name:t,value:ee(s)?{...s}:s}),o.forEach((e=>{const t=re.find((t=>e.name.startsWith(`${t}-`)));if(t){const s=se(t),i=se(e.name.split(`${t}-`)[1]);void 0===r[s]&&(r[s]={}),!0===r[s]&&(r[s]={enabled:!0}),r[s][i]=ie(e.value)}else{const t=se(e.name);if(!a.includes(t))return;const s=ie(e.value);r[t]&&re.includes(e.name)&&!ee(s)?(r[t].constructor!==Object&&(r[t]={}),r[t].enabled=!!s):r[t]=s}})),te(i,r),i.navigation?i.navigation={prevEl:".swiper-button-prev",nextEl:".swiper-button-next",...!0!==i.navigation?i.navigation:{}}:!1===i.navigation&&delete i.navigation,i.scrollbar?i.scrollbar={el:".swiper-scrollbar",...!0!==i.scrollbar?i.scrollbar:{}}:!1===i.scrollbar&&delete i.scrollbar,i.pagination?i.pagination={el:".swiper-pagination",...!0!==i.pagination?i.pagination:{}}:!1===i.pagination&&delete i.pagination,{params:i,passedParams:r}}const ae=":host{--swiper-theme-color:#007aff}:host{position:relative;display:block;margin-left:auto;margin-right:auto;z-index:1}.swiper{width:100%;height:100%;margin-left:auto;margin-right:auto;position:relative;overflow:hidden;list-style:none;padding:0;z-index:1;display:block}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;transition-timing-function:var(--swiper-wrapper-transition-timing-function,initial);box-sizing:content-box}.swiper-android ::slotted(swiper-slide),.swiper-ios ::slotted(swiper-slide),.swiper-wrapper{transform:translate3d(0px,0,0)}.swiper-horizontal{touch-action:pan-y}.swiper-vertical{touch-action:pan-x}::slotted(swiper-slide){flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform;display:block}::slotted(.swiper-slide-invisible-blank){visibility:hidden}.swiper-autoheight,.swiper-autoheight ::slotted(swiper-slide){height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden ::slotted(swiper-slide){transform:translateZ(0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:1200px}.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d{perspective:1200px}.swiper-3d .swiper-cube-shadow,.swiper-3d ::slotted(swiper-slide){transform-style:preserve-3d}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode ::slotted(swiper-slide){scroll-snap-align:start start}.swiper-css-mode.swiper-horizontal>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-css-mode.swiper-vertical>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-css-mode.swiper-free-mode>.swiper-wrapper{scroll-snap-type:none}.swiper-css-mode.swiper-free-mode ::slotted(swiper-slide){scroll-snap-align:none}.swiper-css-mode.swiper-centered>.swiper-wrapper::before{content:'';flex-shrink:0;order:9999}.swiper-css-mode.swiper-centered ::slotted(swiper-slide){scroll-snap-align:center center;scroll-snap-stop:always}.swiper-css-mode.swiper-centered.swiper-horizontal ::slotted(swiper-slide):first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper::before{height:100%;min-height:1px;width:var(--swiper-centered-offset-after)}.swiper-css-mode.swiper-centered.swiper-vertical ::slotted(swiper-slide):first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper::before{width:100%;min-width:1px;height:var(--swiper-centered-offset-after)}";const oe="undefined"==typeof window||"undefined"==typeof HTMLElement?class{}:HTMLElement,le='<svg width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.38296 20.0762C0.111788 19.805 0.111788 19.3654 0.38296 19.0942L9.19758 10.2796L0.38296 1.46497C0.111788 1.19379 0.111788 0.754138 0.38296 0.482966C0.654131 0.211794 1.09379 0.211794 1.36496 0.482966L10.4341 9.55214C10.8359 9.9539 10.8359 10.6053 10.4341 11.007L1.36496 20.0762C1.09379 20.3474 0.654131 20.3474 0.38296 20.0762Z" fill="currentColor"/></svg>\n    ',de=(e,t)=>{if("undefined"!=typeof CSSStyleSheet&&e.adoptedStyleSheets){const s=new CSSStyleSheet;s.replaceSync(t),e.adoptedStyleSheets=[s]}else{const s=document.createElement("style");s.rel="stylesheet",s.textContent=t,e.appendChild(s)}};class ce extends oe{constructor(){super(),this.attachShadow({mode:"open"})}static get nextButtonSvg(){return le}static get prevButtonSvg(){return le.replace("/></svg>",' transform-origin="center" transform="rotate(180)"/></svg>')}cssStyles(){return[ae,...this.injectStyles&&Array.isArray(this.injectStyles)?this.injectStyles:[]].join("\n")}cssLinks(){return this.injectStylesUrls||[]}calcSlideSlots(){const e=this.slideSlots||0,t=[...this.querySelectorAll("[slot^=slide-]")].map((e=>parseInt(e.getAttribute("slot").split("slide-")[1],10)));if(this.slideSlots=t.length?Math.max(...t)+1:0,this.rendered)if(this.slideSlots>e)for(let t=e;t<this.slideSlots;t+=1){const e=document.createElement("swiper-slide");e.setAttribute("part",`slide slide-${t+1}`);const s=document.createElement("slot");s.setAttribute("name",`slide-${t+1}`),e.appendChild(s),this.shadowRoot.querySelector(".swiper-wrapper").appendChild(e)}else if(this.slideSlots<e){const e=this.swiper.slides;for(let t=e.length-1;t>=0;t-=1)t>this.slideSlots&&e[t].remove()}}render(){if(this.rendered)return;this.calcSlideSlots();let e=this.cssStyles();this.slideSlots>0&&(e=e.replace(/::slotted\(([a-z-0-9.]*)\)/g,"$1")),e.length&&de(this.shadowRoot,e),this.cssLinks().forEach((e=>{if(this.shadowRoot.querySelector(`link[href="${e}"]`))return;const t=document.createElement("link");t.rel="stylesheet",t.href=e,this.shadowRoot.appendChild(t)}));const t=document.createElement("div");var s;t.classList.add("swiper"),t.part="container",b(t,`\n      <slot name="container-start"></slot>\n      <div class="swiper-wrapper" part="wrapper">\n        <slot></slot>\n        ${Array.from({length:this.slideSlots}).map(((e,t)=>`\n        <swiper-slide part="slide slide-${t}">\n          <slot name="slide-${t}"></slot>\n        </swiper-slide>\n        `)).join("")}\n      </div>\n      <slot name="container-end"></slot>\n      ${s=this.passedParams,void 0===s&&(s={}),s.navigation&&void 0===s.navigation.nextEl&&void 0===s.navigation.prevEl?`\n        <div part="button-prev" class="swiper-button-prev">${this.constructor.prevButtonSvg}</div>\n        <div part="button-next" class="swiper-button-next">${this.constructor.nextButtonSvg}</div>\n      `:""}\n      ${function(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}(this.passedParams)?'\n        <div part="pagination" class="swiper-pagination"></div>\n      ':""}\n      ${function(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}(this.passedParams)?'\n        <div part="scrollbar" class="swiper-scrollbar"></div>\n      ':""}\n    `),this.shadowRoot.appendChild(t),this.rendered=!0}initialize(){var e=this;if(this.swiper&&this.swiper.initialized)return;const{params:t,passedParams:s}=ne(this);this.swiperParams=t,this.passedParams=s,delete this.swiperParams.init,this.render(),this.swiper=new J(this.shadowRoot.querySelector(".swiper"),{...t.virtual?{}:{observer:!0},...t,touchEventsTarget:"container",onAny:function(s){"observerUpdate"===s&&e.calcSlideSlots();const i=t.eventsPrefix?`${t.eventsPrefix}${s.toLowerCase()}`:s.toLowerCase();for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];const o=new CustomEvent(i,{detail:n,bubbles:"hashChange"!==s,cancelable:!0});e.dispatchEvent(o)}})}connectedCallback(){this.swiper&&this.swiper.initialized&&this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||!1!==this.init&&"false"!==this.getAttribute("init")&&this.initialize()}disconnectedCallback(){this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||this.swiper&&this.swiper.destroy&&this.swiper.destroy()}updateSwiperOnPropChange(e,t){const{params:s,passedParams:i}=ne(this,e,t);this.passedParams=i,this.swiperParams=s,this.swiper&&this.swiper.params[e]===t||function(e){let{swiper:t,slides:s,passedParams:i,changedParams:r,nextEl:n,prevEl:a,scrollbarEl:o,paginationEl:l}=e;const d=r.filter((e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e)),{params:c,pagination:p,navigation:u,scrollbar:h,virtual:f,thumbs:m}=t;let v,g,w,S,T,y,x,E;r.includes("thumbs")&&i.thumbs&&i.thumbs.swiper&&!i.thumbs.swiper.destroyed&&c.thumbs&&(!c.thumbs.swiper||c.thumbs.swiper.destroyed)&&(v=!0),r.includes("controller")&&i.controller&&i.controller.control&&c.controller&&!c.controller.control&&(g=!0),r.includes("pagination")&&i.pagination&&(i.pagination.el||l)&&(c.pagination||!1===c.pagination)&&p&&!p.el&&(w=!0),r.includes("scrollbar")&&i.scrollbar&&(i.scrollbar.el||o)&&(c.scrollbar||!1===c.scrollbar)&&h&&!h.el&&(S=!0),r.includes("navigation")&&i.navigation&&(i.navigation.prevEl||a)&&(i.navigation.nextEl||n)&&(c.navigation||!1===c.navigation)&&u&&!u.prevEl&&!u.nextEl&&(T=!0);const M=e=>{t[e]&&(t[e].destroy(),"navigation"===e?(t.isElement&&(t[e].prevEl.remove(),t[e].nextEl.remove()),c[e].prevEl=void 0,c[e].nextEl=void 0,t[e].prevEl=void 0,t[e].nextEl=void 0):(t.isElement&&t[e].el.remove(),c[e].el=void 0,t[e].el=void 0))};r.includes("loop")&&t.isElement&&(c.loop&&!i.loop?y=!0:!c.loop&&i.loop?x=!0:E=!0),d.forEach((e=>{if(ee(c[e])&&ee(i[e]))Object.assign(c[e],i[e]),"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e||!("enabled"in i[e])||i[e].enabled||M(e);else{const t=i[e];!0!==t&&!1!==t||"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e?c[e]=i[e]:!1===t&&M(e)}})),d.includes("controller")&&!g&&t.controller&&t.controller.control&&c.controller&&c.controller.control&&(t.controller.control=c.controller.control),r.includes("children")&&s&&f&&c.virtual.enabled?(f.slides=s,f.update(!0)):r.includes("virtual")&&f&&c.virtual.enabled&&(s&&(f.slides=s),f.update(!0)),r.includes("children")&&s&&c.loop&&(E=!0),v&&m.init()&&m.update(!0);g&&(t.controller.control=c.controller.control),w&&(!t.isElement||l&&"string"!=typeof l||(l=document.createElement("div"),l.classList.add("swiper-pagination"),l.part.add("pagination"),t.el.appendChild(l)),l&&(c.pagination.el=l),p.init(),p.render(),p.update()),S&&(!t.isElement||o&&"string"!=typeof o||(o=document.createElement("div"),o.classList.add("swiper-scrollbar"),o.part.add("scrollbar"),t.el.appendChild(o)),o&&(c.scrollbar.el=o),h.init(),h.updateSize(),h.setTranslate()),T&&(t.isElement&&(n&&"string"!=typeof n||(n=document.createElement("div"),n.classList.add("swiper-button-next"),b(n,t.hostEl.constructor.nextButtonSvg),n.part.add("button-next"),t.el.appendChild(n)),a&&"string"!=typeof a||(a=document.createElement("div"),a.classList.add("swiper-button-prev"),b(a,t.hostEl.constructor.prevButtonSvg),a.part.add("button-prev"),t.el.appendChild(a))),n&&(c.navigation.nextEl=n),a&&(c.navigation.prevEl=a),u.init(),u.update()),r.includes("allowSlideNext")&&(t.allowSlideNext=i.allowSlideNext),r.includes("allowSlidePrev")&&(t.allowSlidePrev=i.allowSlidePrev),r.includes("direction")&&t.changeDirection(i.direction,!1),(y||E)&&t.loopDestroy(),(x||E)&&t.loopCreate(),t.update()}({swiper:this.swiper,passedParams:this.passedParams,changedParams:[se(e)],..."navigation"===e&&i[e]?{prevEl:".swiper-button-prev",nextEl:".swiper-button-next"}:{},..."pagination"===e&&i[e]?{paginationEl:".swiper-pagination"}:{},..."scrollbar"===e&&i[e]?{scrollbarEl:".swiper-scrollbar"}:{}})}attributeChangedCallback(e,t,s){this.swiper&&this.swiper.initialized&&("true"===t&&null===s&&(s=!1),this.updateSwiperOnPropChange(e,s))}static get observedAttributes(){return Q.filter((e=>e.includes("_"))).map((e=>e.replace(/[A-Z]/g,(e=>`-${e}`)).replace("_","").toLowerCase()))}}Q.forEach((e=>{"init"!==e&&(e=e.replace("_",""),Object.defineProperty(ce.prototype,e,{configurable:!0,get(){return(this.passedParams||{})[e]},set(t){this.passedParams||(this.passedParams={}),this.passedParams[e]=t,this.swiper&&this.swiper.initialized&&this.updateSwiperOnPropChange(e,t)}}))}));class pe extends oe{constructor(){super(),this.attachShadow({mode:"open"})}render(){const e=this.lazy||""===this.getAttribute("lazy")||"true"===this.getAttribute("lazy");if(de(this.shadowRoot,"::slotted(.swiper-slide-shadow),::slotted(.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-top){position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}::slotted(.swiper-slide-shadow){background:rgba(0,0,0,.15)}::slotted(.swiper-slide-shadow-left){background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-right){background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-top){background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-bottom){background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-lazy-preloader{animation:swiper-preloader-spin 1s infinite linear;width:42px;height:42px;position:absolute;left:50%;top:50%;margin-left:-21px;margin-top:-21px;z-index:10;transform-origin:50%;box-sizing:border-box;border:4px solid var(--swiper-preloader-color,var(--swiper-theme-color));border-radius:50%;border-top-color:transparent}@keyframes swiper-preloader-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-zoom-container){width:100%;height:100%;display:flex;justify-content:center;align-items:center;text-align:center}::slotted(.swiper-zoom-container)>canvas,::slotted(.swiper-zoom-container)>img,::slotted(.swiper-zoom-container)>svg{max-width:100%;max-height:100%;object-fit:contain}"),this.shadowRoot.appendChild(document.createElement("slot")),e){const e=document.createElement("div");e.classList.add("swiper-lazy-preloader"),e.part.add("preloader"),this.shadowRoot.appendChild(e)}}initialize(){this.render()}connectedCallback(){this.swiperLoopMoveDOM||this.initialize()}}"undefined"!=typeof window&&(window.SwiperElementRegisterParams=e=>{Q.push(...e)}),"undefined"!=typeof window&&(window.customElements.get("swiper-container")||window.customElements.define("swiper-container",ce),window.customElements.get("swiper-slide")||window.customElements.define("swiper-slide",pe))}();
//# sourceMappingURL=swiper-element.min.js.map