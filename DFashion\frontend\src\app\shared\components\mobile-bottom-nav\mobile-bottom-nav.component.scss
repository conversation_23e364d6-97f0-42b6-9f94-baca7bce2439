.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);

  // Hide on desktop
  @media (min-width: 769px) {
    display: none;
  }
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  min-width: 60px;
  
  &:active {
    transform: scale(0.95);
  }

  &.active {
    .nav-icon i {
      color: #667eea;
      transform: scale(1.1);
    }
    
    .nav-label {
      color: #667eea;
      font-weight: 600;
    }
  }

  &:not(.active):hover {
    background: rgba(102, 126, 234, 0.1);
  }
}

.nav-icon {
  position: relative;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;

  i {
    font-size: 20px;
    color: #8e8e8e;
    transition: all 0.3s ease;
  }

  .nav-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(245, 87, 108, 0.4);
    animation: pulse 2s infinite;
  }

  .profile-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &.active {
      border-color: #667eea;
      transform: scale(1.1);
    }
  }
}

.nav-label {
  font-size: 10px;
  color: #8e8e8e;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1;

  &.active {
    color: #667eea;
    font-weight: 600;
  }
}

.active-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #667eea;
  border-radius: 50%;
  animation: fadeIn 0.3s ease;
}

// Floating Action Button
.fab-create {
  position: absolute;
  bottom: 60px;
  right: 20px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 1001;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
  }

  &:active {
    transform: scale(0.95);
  }

  &.expanded {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    transform: rotate(45deg);
  }

  i {
    transition: transform 0.3s ease;

    &.rotated {
      transform: rotate(45deg);
    }
  }
}

// Create Menu
.create-menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;

    .create-options {
      transform: translateY(0);
    }
  }
}

.create-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.create-options {
  position: absolute;
  bottom: 130px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.create-option {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  border: none;
  border-radius: 25px;
  padding: 12px 20px;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  min-width: 140px;

  &:hover {
    transform: translateX(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  }

  i {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    font-size: 10px;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
  }

  // Different colors for different options
  &:nth-child(1) i {
    background: linear-gradient(135deg, #667eea, #764ba2);
  }

  &:nth-child(2) i {
    background: linear-gradient(135deg, #f093fb, #f5576c);
  }

  &:nth-child(3) i {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
  }

  &:nth-child(4) i {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

// Safe area adjustments for different devices
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .mobile-bottom-nav {
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .mobile-bottom-nav {
    background: rgba(26, 32, 44, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);

    .nav-item:not(.active):hover {
      background: rgba(102, 126, 234, 0.2);
    }

    .nav-icon i {
      color: #a0aec0;
    }

    .nav-label {
      color: #a0aec0;
    }

    .create-option {
      background: #2d3748;
      color: white;

      span {
        color: white;
      }
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .nav-item,
  .nav-icon i,
  .nav-label,
  .fab-create,
  .create-option {
    transition: none;
  }

  .nav-badge {
    animation: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .mobile-bottom-nav {
    border-top: 2px solid #000;
  }

  .nav-item.active .nav-icon i,
  .nav-item.active .nav-label {
    color: #000;
  }
}
