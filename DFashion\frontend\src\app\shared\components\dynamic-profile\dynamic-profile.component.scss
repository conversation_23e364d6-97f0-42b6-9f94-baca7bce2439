.dynamic-profile {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  background: #fafafa;
  min-height: 100vh;

  // Profile Header
  .profile-header {
    position: relative;
    background: white;
    border-radius: 0 0 24px 24px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
  }

  .profile-cover {
    height: 200px;
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    padding: 1rem;

    @media (max-width: 768px) {
      height: 150px;
    }
  }

  .profile-actions {
    display: flex;
    gap: 0.5rem;
    z-index: 2;

    .action-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .profile-info {
    padding: 0 2rem 2rem;
    margin-top: -60px;
    position: relative;
    z-index: 3;

    @media (max-width: 768px) {
      padding: 0 1rem 1.5rem;
      margin-top: -40px;
    }
  }

  .avatar-section {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 1rem;
    }
  }

  .avatar-container {
    position: relative;
    flex-shrink: 0;

    .profile-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 4px solid white;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      object-fit: cover;

      @media (max-width: 768px) {
        width: 80px;
        height: 80px;
      }
    }

    .online-indicator {
      position: absolute;
      bottom: 8px;
      right: 8px;
      width: 20px;
      height: 20px;
      background: #4CAF50;
      border: 3px solid white;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    .role-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 0.8rem;
      border: 2px solid white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }

  .profile-details {
    flex: 1;
    min-width: 0;

    .name-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.25rem;

      .profile-name {
        font-size: 2rem;
        font-weight: 700;
        color: #262626;
        margin: 0;
        line-height: 1.2;

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .verification-badge {
        color: #1DA1F2;
        font-size: 1.2rem;
      }
    }

    .profile-username {
      color: #8e8e8e;
      font-size: 1rem;
      margin: 0 0 0.5rem 0;
    }

    .role-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;

      .role-title {
        font-weight: 600;
        font-size: 0.95rem;
      }

      .department {
        background: #f0f0f0;
        color: #666;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .profile-bio {
      color: #262626;
      line-height: 1.5;
      margin: 0 0 1rem 0;
      font-size: 0.95rem;
    }

    .profile-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #8e8e8e;
        font-size: 0.85rem;

        i {
          width: 14px;
          text-align: center;
        }
      }
    }
  }

  // Profile Stats
  .profile-stats {
    margin-bottom: 2rem;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      padding: 0 1rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .stat-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      border-left: 4px solid;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .stat-icon {
        font-size: 1.5rem;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.05);
        border-radius: 12px;
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 1.8rem;
          font-weight: 700;
          color: #262626;
          line-height: 1;
        }

        .stat-label {
          color: #8e8e8e;
          font-size: 0.85rem;
          margin-top: 0.25rem;
        }

        .stat-trend {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          margin-top: 0.5rem;
          font-size: 0.8rem;
          font-weight: 600;

          &.trend-up {
            color: #4CAF50;
          }

          &.trend-down {
            color: #f44336;
          }

          &.trend-stable {
            color: #ff9800;
          }
        }
      }
    }
  }

  // Role-specific layouts
  .role-content {
    padding: 0 1rem;
  }

  .executive-layout,
  .manager-layout,
  .specialist-layout,
  .admin-layout {
    .executive-grid,
    .manager-grid,
    .specialist-grid,
    .admin-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .executive-card,
    .manager-card,
    .specialist-card,
    .admin-card {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

      h3 {
        margin: 0 0 1rem 0;
        color: #262626;
        font-size: 1.1rem;
        font-weight: 600;
      }
    }
  }

  // Specific layout styles
  .leadership-metrics,
  .performance-metrics {
    display: flex;
    gap: 1rem;
    justify-content: space-around;

    .metric {
      text-align: center;

      .metric-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: #262626;
      }

      .metric-label {
        font-size: 0.8rem;
        color: #8e8e8e;
        margin-top: 0.25rem;
      }
    }
  }

  .performance-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;

    .member-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      min-width: 120px;

      .member-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .performance-bar {
      flex: 1;
      height: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;

      .progress {
        height: 100%;
        background: linear-gradient(90deg, #4CAF50, #8BC34A);
        transition: width 0.3s ease;
      }
    }
  }

  .goals-list,
  .achievements-list,
  .activities-list {
    .goal-item,
    .achievement-item,
    .activity-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .progress-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.8rem;
  }

  .achievement-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .system-stats {
    .system-metric {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;

      .metric-icon {
        font-size: 1.2rem;
      }

      .metric-data {
        .metric-value {
          display: block;
          font-weight: 600;
          color: #262626;
        }

        .metric-label {
          font-size: 0.8rem;
          color: #8e8e8e;
        }
      }
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .dynamic-profile {
    background: #121212;

    .profile-header,
    .stat-card,
    .executive-card,
    .manager-card,
    .specialist-card,
    .admin-card {
      background: #1e1e1e;
      color: #ffffff;
    }

    .profile-name {
      color: #ffffff;
    }

    .profile-username,
    .meta-item,
    .stat-label {
      color: #b3b3b3;
    }
  }
}
