{"name": "dfashion-backend", "version": "1.0.0", "description": "DFashion Social E-commerce Backend API", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "seed": "node scripts/seedRealData.js"}, "keywords": ["fashion", "social", "ecommerce", "api"], "author": "DFashion Team", "license": "MIT", "dependencies": {"bcryptjs": "2.4.3", "compression": "1.7.4", "cors": "2.8.5", "dotenv": "16.3.1", "express": "4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "6.15.0", "helmet": "6.1.5", "jsonwebtoken": "9.0.2", "mongoose": "7.5.0", "multer": "^1.4.3", "nodemailer": "^7.0.3", "razorpay": "^2.9.6", "socket.io": "4.7.2"}, "devDependencies": {"nodemon": "3.0.1"}}