{"ast": null, "code": "import { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Directive, Inject, Optional, Input, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { DOCUMENT, NgClass, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst _c0 = [\"tooltip\"];\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition({\n      scrollThrottle: SCROLL_THROTTLE_MS\n    });\n  }\n});\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    if (value !== this._position) {\n      this._position = value;\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n        this._tooltipInstance?.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  /**\n   * Whether tooltip should be relative to the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  get positionAtOrigin() {\n    return this._positionAtOrigin;\n  }\n  set positionAtOrigin(value) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n  /** Disables the display of the tooltip. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // If tooltip is disabled, hide immediately.\n    if (this._disabled) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n  get showDelay() {\n    return this._showDelay;\n  }\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  get hideDelay() {\n    return this._hideDelay;\n  }\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n  /** The message to be displayed in the tooltip */\n  get message() {\n    return this._message;\n  }\n  set message(value) {\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n      this._ngZone.runOutsideAngular(() => {\n        // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n        // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n        // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n        // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n        Promise.resolve().then(() => {\n          this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n        });\n      });\n    }\n  }\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n  constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n    this._overlay = _overlay;\n    this._elementRef = _elementRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewContainerRef = _viewContainerRef;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._ariaDescriber = _ariaDescriber;\n    this._focusMonitor = _focusMonitor;\n    this._dir = _dir;\n    this._defaultOptions = _defaultOptions;\n    this._position = 'below';\n    this._positionAtOrigin = false;\n    this._disabled = false;\n    this._viewInitialized = false;\n    this._pointerExitEventsInitialized = false;\n    this._tooltipComponent = TooltipComponent;\n    this._viewportMargin = 8;\n    this._cssClassPrefix = 'mat-mdc';\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n    this.touchGestures = 'auto';\n    this._message = '';\n    /** Manually-bound passive event listeners. */\n    this._passiveListeners = [];\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    this._scrollStrategy = scrollStrategy;\n    this._document = _document;\n    if (_defaultOptions) {\n      this._showDelay = _defaultOptions.showDelay;\n      this._hideDelay = _defaultOptions.hideDelay;\n      if (_defaultOptions.position) {\n        this.position = _defaultOptions.position;\n      }\n      if (_defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = _defaultOptions.positionAtOrigin;\n      }\n      if (_defaultOptions.touchGestures) {\n        this.touchGestures = _defaultOptions.touchGestures;\n      }\n    }\n    _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n      }\n    });\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    clearTimeout(this._touchstartTimeout);\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay = this.showDelay, origin) {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = this._tooltipInstance = overlayRef.attach(this._portal).instance;\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay = this.hideDelay) {\n    const instance = this._tooltipInstance;\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n  /** Shows/hides the tooltip */\n  toggle(origin) {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n  _createOverlay(origin) {\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig().positionStrategy;\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n      this._detach();\n    }\n    const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = this._overlay.position().flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = this._overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._scrollStrategy()\n    });\n    this._updatePosition(this._overlayRef);\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n    position.withPositions([this._addOffset({\n      ...origin.main,\n      ...overlay.main\n    }), this._addOffset({\n      ...origin.fallback,\n      ...overlay.fallback\n    })]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  _addOffset(position) {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n      this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      });\n    }\n  }\n  /** Updates the tooltip class */\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition;\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    }\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', event => {\n        this._setupPointerExitEventsIfNeeded();\n        let point = undefined;\n        if (event.x !== undefined && event.y !== undefined) {\n          point = event;\n        }\n        this.show(undefined, point);\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      this._passiveListeners.push(['touchstart', event => {\n        const touch = event.targetTouches?.[0];\n        const origin = touch ? {\n          x: touch.clientX,\n          y: touch.clientY\n        } : undefined;\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n        clearTimeout(this._touchstartTimeout);\n        const DEFAULT_LONGPRESS_DELAY = 500;\n        this._touchstartTimeout = setTimeout(() => this.show(undefined, origin), this._defaultOptions.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n      }]);\n    }\n    this._addListeners(this._passiveListeners);\n  }\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', event => {\n        const newTarget = event.relatedTarget;\n        if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n          this.hide();\n        }\n      }], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        clearTimeout(this._touchstartTimeout);\n        this.hide(this._defaultOptions.touchendHideDelay);\n      };\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      }\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n  static {\n    this.ɵfac = function MatTooltip_Factory(t) {\n      return new (t || MatTooltip)(i0.ɵɵdirectiveInject(i1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(i3.AriaDescriber), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(MAT_TOOLTIP_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i4.Directionality), i0.ɵɵdirectiveInject(MAT_TOOLTIP_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTooltip,\n      selectors: [[\"\", \"matTooltip\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-tooltip-trigger\"],\n      hostVars: 2,\n      hostBindings: function MatTooltip_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tooltip-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        position: [i0.ɵɵInputFlags.None, \"matTooltipPosition\", \"position\"],\n        positionAtOrigin: [i0.ɵɵInputFlags.None, \"matTooltipPositionAtOrigin\", \"positionAtOrigin\"],\n        disabled: [i0.ɵɵInputFlags.None, \"matTooltipDisabled\", \"disabled\"],\n        showDelay: [i0.ɵɵInputFlags.None, \"matTooltipShowDelay\", \"showDelay\"],\n        hideDelay: [i0.ɵɵInputFlags.None, \"matTooltipHideDelay\", \"hideDelay\"],\n        touchGestures: [i0.ɵɵInputFlags.None, \"matTooltipTouchGestures\", \"touchGestures\"],\n        message: [i0.ɵɵInputFlags.None, \"matTooltip\", \"message\"],\n        tooltipClass: [i0.ɵɵInputFlags.None, \"matTooltipClass\", \"tooltipClass\"]\n      },\n      exportAs: [\"matTooltip\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-mdc-tooltip-trigger',\n        '[class.mat-mdc-tooltip-disabled]': 'disabled'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.Overlay\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.ScrollDispatcher\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i2.Platform\n  }, {\n    type: i3.AriaDescriber\n  }, {\n    type: i3.FocusMonitor\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: i4.Directionality\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    positionAtOrigin: [{\n      type: Input,\n      args: ['matTooltipPositionAtOrigin']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n  constructor(_changeDetectorRef, _elementRef, animationMode) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    /* Whether the tooltip text overflows to multiple lines */\n    this._isMultiline = false;\n    /** Whether interactions on the page should close the tooltip */\n    this._closeOnInteraction = false;\n    /** Whether the tooltip is currently visible. */\n    this._isVisible = false;\n    /** Subject for notifying that the tooltip has been hidden from the view */\n    this._onHide = new Subject();\n    /** Name of the show animation and the class that toggles it. */\n    this._showAnimation = 'mat-mdc-tooltip-show';\n    /** Name of the hide animation and the class that toggles it. */\n    this._hideAnimation = 'mat-mdc-tooltip-hide';\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n  isVisible() {\n    return this._isVisible;\n  }\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null;\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  _handleMouseLeave({\n    relatedTarget\n  }) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  _onShow() {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n  /** Whether the tooltip text has overflown to the next line */\n  _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({\n    animationName\n  }) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n  /** Handles the cleanup after an animation has finished. */\n  _finalizeAnimation(toVisible) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n  /** Toggles the visibility of the tooltip element. */\n  _toggleVisibility(isVisible) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    if (this._isVisible !== isVisible) {\n      this._isVisible = isVisible;\n      this._changeDetectorRef.markForCheck();\n    }\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (styles.getPropertyValue('animation-duration') === '0s' || styles.getPropertyValue('animation-name') === 'none') {\n        this._animationsDisabled = true;\n      }\n    }\n    if (isVisible) {\n      this._onShow();\n    }\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n  static {\n    this.ɵfac = function TooltipComponent_Factory(t) {\n      return new (t || TooltipComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TooltipComponent,\n      selectors: [[\"mat-tooltip-component\"]],\n      viewQuery: function TooltipComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tooltip = _t.first);\n        }\n      },\n      hostAttrs: [\"aria-hidden\", \"true\"],\n      hostVars: 2,\n      hostBindings: function TooltipComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseleave\", function TooltipComponent_mouseleave_HostBindingHandler($event) {\n            return ctx._handleMouseLeave($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"zoom\", ctx.isVisible() ? 1 : null);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[\"tooltip\", \"\"], [1, \"mdc-tooltip\", \"mdc-tooltip--shown\", \"mat-mdc-tooltip\", 3, \"animationend\", \"ngClass\"], [1, \"mdc-tooltip__surface\", \"mdc-tooltip__surface-animation\"]],\n      template: function TooltipComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵlistener(\"animationend\", function TooltipComponent_Template_div_animationend_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleAnimationEnd($event));\n          });\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-tooltip--multiline\", ctx._isMultiline);\n          i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.message);\n        }\n      },\n      dependencies: [NgClass],\n      styles: [\".mdc-tooltip__surface{word-break:break-all;word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip--showing-transition .mdc-tooltip__surface-animation{transition:opacity 150ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-tooltip--hide-transition .mdc-tooltip__surface-animation{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - 2*8px);margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - 2*8px);align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mat-mdc-tooltip .mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color)}.mat-mdc-tooltip .mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__caret-surface-top,.mat-mdc-tooltip .mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color)}.mat-mdc-tooltip .mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font);line-height:var(--mdc-plain-tooltip-supporting-text-line-height);font-size:var(--mdc-plain-tooltip-supporting-text-size);font-weight:var(--mdc-plain-tooltip-supporting-text-weight);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n        '[style.zoom]': 'isVisible() ? 1 : null',\n        '(mouseleave)': '_handleMouseLeave($event)',\n        'aria-hidden': 'true'\n      },\n      standalone: true,\n      imports: [NgClass],\n      template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\",\n      styles: [\".mdc-tooltip__surface{word-break:break-all;word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip--showing-transition .mdc-tooltip__surface-animation{transition:opacity 150ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-tooltip--hide-transition .mdc-tooltip__surface-animation{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - 2*8px);margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - 2*8px);align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mat-mdc-tooltip .mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color)}.mat-mdc-tooltip .mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__caret-surface-top,.mat-mdc-tooltip .mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color)}.mat-mdc-tooltip .mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font);line-height:var(--mdc-plain-tooltip-supporting-text-line-height);font-size:var(--mdc-plain-tooltip-supporting-text-size);font-weight:var(--mdc-plain-tooltip-supporting-text-weight);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    _tooltip: [{\n      type: ViewChild,\n      args: ['tooltip', {\n        // Use a static query here since we interact directly with\n        // the DOM which can happen before `ngAfterViewInit`.\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: trigger('state', [\n  // TODO(crisbeto): these values are based on MDC's CSS.\n  // We should be able to use their styles directly once we land #19432.\n  state('initial, void, hidden', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), state('visible', style({\n    transform: 'scale(1)'\n  })), transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)'))])\n};\nclass MatTooltipModule {\n  static {\n    this.ɵfac = function MatTooltipModule_Factory(t) {\n      return new (t || MatTooltipModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTooltipModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, getMatTooltipInvalidPositionError, matTooltipAnimations };", "map": {"version": 3, "names": ["takeUntil", "take", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "i0", "InjectionToken", "inject", "ElementRef", "Directive", "Inject", "Optional", "Input", "ANIMATION_MODULE_TYPE", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "NgModule", "DOCUMENT", "Ng<PERSON><PERSON>", "CommonModule", "i2", "normalizePassiveListenerOptions", "i3", "A11yModule", "i4", "i1", "Overlay", "OverlayModule", "ComponentPortal", "Subject", "trigger", "state", "style", "transition", "animate", "CdkScrollableModule", "MatCommonModule", "_c0", "SCROLL_THROTTLE_MS", "getMatTooltipInvalidPositionError", "position", "Error", "MAT_TOOLTIP_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "useFactory", "MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "touchendHideDelay", "MAT_TOOLTIP_DEFAULT_OPTIONS", "TOOLTIP_PANEL_CLASS", "PANEL_CLASS", "passiveListenerOptions", "passive", "MIN_VIEWPORT_TOOLTIP_THRESHOLD", "UNBOUNDED_ANCHOR_GAP", "MIN_HEIGHT", "MAX_WIDTH", "MatTooltip", "_position", "value", "_overlayRef", "_updatePosition", "_tooltipInstance", "show", "updatePosition", "positionAt<PERSON><PERSON><PERSON>", "_position<PERSON><PERSON><PERSON><PERSON><PERSON>", "_detach", "disabled", "_disabled", "hide", "_setupPointerEnterEventsIfNeeded", "_showDelay", "_hideDelay", "_mouseLeaveHideDelay", "message", "_message", "_ariaDescriber", "removeDescription", "_elementRef", "nativeElement", "String", "trim", "_isTooltipVisible", "_updateTooltipMessage", "_ngZone", "runOutsideAngular", "Promise", "resolve", "then", "describe", "tooltipClass", "_tooltipClass", "_setTooltipClass", "constructor", "_overlay", "_scrollDispatcher", "_viewContainerRef", "_platform", "_focusMonitor", "scrollStrategy", "_dir", "_defaultOptions", "_document", "_viewInitialized", "_pointerExitEventsInitialized", "_tooltipComponent", "TooltipComponent", "_viewportMargin", "_cssClassPrefix", "touchGestures", "_passiveListeners", "_destroyed", "_scrollStrategy", "change", "pipe", "subscribe", "ngAfterViewInit", "monitor", "origin", "run", "ngOnDestroy", "clearTimeout", "_touchstartTimeout", "dispose", "for<PERSON>ach", "event", "listener", "removeEventListener", "length", "next", "complete", "stopMonitoring", "delay", "_cancelPendingAnimations", "overlayRef", "_createOverlay", "_portal", "instance", "attach", "_triggerElement", "afterHidden", "isVisible", "toggle", "undefined", "existingStrategy", "getConfig", "positionStrategy", "_origin", "scrollableAncestors", "getAncestorScrollContainers", "strategy", "flexibleConnectedTo", "withTransformOriginOn", "withFlexibleDimensions", "withViewportMargin", "withScrollableContainers", "position<PERSON><PERSON>es", "_updateCurrentPositionClass", "connectionPair", "scrollableViewProperties", "isOverlayClipped", "create", "direction", "panelClass", "detachments", "outsidePointerEvents", "_handleBodyInteraction", "keydownEvents", "keyCode", "preventDefault", "stopPropagation", "disableTooltipInteractivity", "addPanelClass", "has<PERSON>tta<PERSON>", "detach", "_get<PERSON><PERSON>in", "_getOverlayPosition", "withPositions", "_addOffset", "main", "fallback", "offset", "isLtr", "originY", "offsetY", "originX", "offsetX", "originPosition", "ngDevMode", "x", "y", "_invertPosition", "overlayPosition", "overlayX", "overlayY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "onMicrotaskEmpty", "newPosition", "_currentPosition", "classPrefix", "removePanelClass", "_platformSupportsMouseEvents", "push", "_setupPointerExitEventsIfNeeded", "point", "_disableNativeGesturesIfNecessary", "touch", "targetTouches", "clientX", "clientY", "DEFAULT_LONGPRESS_DELAY", "setTimeout", "touchLongPressShowDelay", "_addListeners", "exitListeners", "newTarget", "relatedTarget", "overlayElement", "contains", "_wheelListener", "touchendListener", "listeners", "addEventListener", "IOS", "ANDROID", "elementUnderPointer", "elementFromPoint", "element", "gestures", "nodeName", "userSelect", "msUserSelect", "webkitUserSelect", "MozUserSelect", "draggable", "webkitUserDrag", "touchAction", "webkitTapHighlightColor", "ɵfac", "MatTooltip_Factory", "t", "ɵɵdirectiveInject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewContainerRef", "NgZone", "Platform", "AriaDescriber", "FocusMonitor", "Directionality", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatTooltip_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "None", "exportAs", "standalone", "ɵsetClassMetadata", "args", "selector", "host", "decorators", "_changeDetectorRef", "animationMode", "_isMultiline", "_closeOnInteraction", "_isVisible", "_onHide", "_showAnimation", "_hideAnimation", "_animationsDisabled", "_hideTimeoutId", "_showTimeoutId", "_toggleVisibility", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_handleMouseLeave", "_finalizeAnimation", "_onShow", "_isTooltipMultiline", "rect", "getBoundingClientRect", "height", "width", "_handleAnimationEnd", "animationName", "toVisible", "tooltip", "_tooltip", "showClass", "hideClass", "classList", "remove", "add", "getComputedStyle", "styles", "getPropertyValue", "TooltipComponent_Factory", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "TooltipComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "TooltipComponent_HostBindings", "ɵɵlistener", "TooltipComponent_mouseleave_HostBindingHandler", "$event", "ɵɵstyleProp", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TooltipComponent_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "TooltipComponent_Template_div_animationend_0_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate", "dependencies", "encapsulation", "changeDetection", "OnPush", "imports", "static", "matTooltipAnimations", "tooltipState", "opacity", "transform", "MatTooltipModule", "MatTooltipModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "exports"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@angular/material/fesm2022/tooltip.mjs"], "sourcesContent": ["import { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Directive, Inject, Optional, Input, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { DOCUMENT, NgClass, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n    },\n});\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n                this._tooltipInstance?.show(0);\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    /**\n     * Whether tooltip should be relative to the click or touch origin\n     * instead of outside the element bounding box.\n     */\n    get positionAtOrigin() {\n        return this._positionAtOrigin;\n    }\n    set positionAtOrigin(value) {\n        this._positionAtOrigin = coerceBooleanProperty(value);\n        this._detach();\n        this._overlayRef = null;\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // If tooltip is disabled, hide immediately.\n        if (this._disabled) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n        }\n    }\n    /** The default delay in ms before showing the tooltip after show is called */\n    get showDelay() {\n        return this._showDelay;\n    }\n    set showDelay(value) {\n        this._showDelay = coerceNumberProperty(value);\n    }\n    /** The default delay in ms before hiding the tooltip after hide is called */\n    get hideDelay() {\n        return this._hideDelay;\n    }\n    set hideDelay(value) {\n        this._hideDelay = coerceNumberProperty(value);\n        if (this._tooltipInstance) {\n            this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n        }\n    }\n    /** The message to be displayed in the tooltip */\n    get message() {\n        return this._message;\n    }\n    set message(value) {\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n            this._ngZone.runOutsideAngular(() => {\n                // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n                // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n                // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n                // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n                Promise.resolve().then(() => {\n                    this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                });\n            });\n        }\n    }\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() {\n        return this._tooltipClass;\n    }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n        this._overlay = _overlay;\n        this._elementRef = _elementRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewContainerRef = _viewContainerRef;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._ariaDescriber = _ariaDescriber;\n        this._focusMonitor = _focusMonitor;\n        this._dir = _dir;\n        this._defaultOptions = _defaultOptions;\n        this._position = 'below';\n        this._positionAtOrigin = false;\n        this._disabled = false;\n        this._viewInitialized = false;\n        this._pointerExitEventsInitialized = false;\n        this._tooltipComponent = TooltipComponent;\n        this._viewportMargin = 8;\n        this._cssClassPrefix = 'mat-mdc';\n        /**\n         * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n         * uses a long press gesture to show and hide, however it can conflict with the native browser\n         * gestures. To work around the conflict, Angular Material disables native gestures on the\n         * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n         * elements). The different values for this option configure the touch event handling as follows:\n         * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n         *   browser gestures on particular elements. In particular, it allows text selection on inputs\n         *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n         * - `on` - Enables touch gestures for all elements and disables native\n         *   browser gestures with no exceptions.\n         * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n         *   showing on touch devices.\n         */\n        this.touchGestures = 'auto';\n        this._message = '';\n        /** Manually-bound passive event listeners. */\n        this._passiveListeners = [];\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        this._scrollStrategy = scrollStrategy;\n        this._document = _document;\n        if (_defaultOptions) {\n            this._showDelay = _defaultOptions.showDelay;\n            this._hideDelay = _defaultOptions.hideDelay;\n            if (_defaultOptions.position) {\n                this.position = _defaultOptions.position;\n            }\n            if (_defaultOptions.positionAtOrigin) {\n                this.positionAtOrigin = _defaultOptions.positionAtOrigin;\n            }\n            if (_defaultOptions.touchGestures) {\n                this.touchGestures = _defaultOptions.touchGestures;\n            }\n        }\n        _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n            }\n        });\n        this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor\n            .monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        clearTimeout(this._touchstartTimeout);\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay, origin) {\n        if (this.disabled || !this.message || this._isTooltipVisible()) {\n            this._tooltipInstance?._cancelPendingAnimations();\n            return;\n        }\n        const overlayRef = this._createOverlay(origin);\n        this._detach();\n        this._portal =\n            this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n        const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n        instance._triggerElement = this._elementRef.nativeElement;\n        instance._mouseLeaveHideDelay = this._hideDelay;\n        instance\n            .afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        instance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        const instance = this._tooltipInstance;\n        if (instance) {\n            if (instance.isVisible()) {\n                instance.hide(delay);\n            }\n            else {\n                instance._cancelPendingAnimations();\n                this._detach();\n            }\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle(origin) {\n        this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay(origin) {\n        if (this._overlayRef) {\n            const existingStrategy = this._overlayRef.getConfig()\n                .positionStrategy;\n            if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n                return this._overlayRef;\n            }\n            this._detach();\n        }\n        const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef)\n            .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n            .withFlexibleDimensions(false)\n            .withViewportMargin(this._viewportMargin)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            this._updateCurrentPositionClass(change.connectionPair);\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = this._overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n            scrollStrategy: this._scrollStrategy(),\n        });\n        this._updatePosition(this._overlayRef);\n        this._overlayRef\n            .detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._overlayRef\n            .outsidePointerEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n        this._overlayRef\n            .keydownEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        });\n        if (this._defaultOptions?.disableTooltipInteractivity) {\n            this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n        }\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition(overlayRef) {\n        const position = overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            this._addOffset({ ...origin.main, ...overlay.main }),\n            this._addOffset({ ...origin.fallback, ...overlay.fallback }),\n        ]);\n    }\n    /** Adds the configured offset to a position. Used as a hook for child classes. */\n    _addOffset(position) {\n        const offset = UNBOUNDED_ANCHOR_GAP;\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        if (position.originY === 'top') {\n            position.offsetY = -offset;\n        }\n        else if (position.originY === 'bottom') {\n            position.offsetY = offset;\n        }\n        else if (position.originX === 'start') {\n            position.offsetX = isLtr ? -offset : offset;\n        }\n        else if (position.originX === 'end') {\n            position.offsetX = isLtr ? offset : -offset;\n        }\n        return position;\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y },\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y },\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Updates the class on the overlay panel based on the current position of the tooltip. */\n    _updateCurrentPositionClass(connectionPair) {\n        const { overlayY, originX, originY } = connectionPair;\n        let newPosition;\n        // If the overlay is in the middle along the Y axis,\n        // it means that it's either before or after.\n        if (overlayY === 'center') {\n            // Note that since this information is used for styling, we want to\n            // resolve `start` and `end` to their real values, otherwise consumers\n            // would have to remember to do it themselves on each consumption.\n            if (this._dir && this._dir.value === 'rtl') {\n                newPosition = originX === 'end' ? 'left' : 'right';\n            }\n            else {\n                newPosition = originX === 'start' ? 'left' : 'right';\n            }\n        }\n        else {\n            newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n        }\n        if (newPosition !== this._currentPosition) {\n            const overlayRef = this._overlayRef;\n            if (overlayRef) {\n                const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n                overlayRef.removePanelClass(classPrefix + this._currentPosition);\n                overlayRef.addPanelClass(classPrefix + newPosition);\n            }\n            this._currentPosition = newPosition;\n        }\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled ||\n            !this.message ||\n            !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners.push([\n                'mouseenter',\n                event => {\n                    this._setupPointerExitEventsIfNeeded();\n                    let point = undefined;\n                    if (event.x !== undefined && event.y !== undefined) {\n                        point = event;\n                    }\n                    this.show(undefined, point);\n                },\n            ]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners.push([\n                'touchstart',\n                event => {\n                    const touch = event.targetTouches?.[0];\n                    const origin = touch ? { x: touch.clientX, y: touch.clientY } : undefined;\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    clearTimeout(this._touchstartTimeout);\n                    const DEFAULT_LONGPRESS_DELAY = 500;\n                    this._touchstartTimeout = setTimeout(() => this.show(undefined, origin), this._defaultOptions.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n                },\n            ]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push([\n                'mouseleave',\n                event => {\n                    const newTarget = event.relatedTarget;\n                    if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n                        this.hide();\n                    }\n                },\n            ], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                clearTimeout(this._touchstartTimeout);\n                this.hide(this._defaultOptions.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect =\n                    style.msUserSelect =\n                        style.webkitUserSelect =\n                            style.MozUserSelect =\n                                'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltip, deps: [{ token: i1.Overlay }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i2.Platform }, { token: i3.AriaDescriber }, { token: i3.FocusMonitor }, { token: MAT_TOOLTIP_SCROLL_STRATEGY }, { token: i4.Directionality }, { token: MAT_TOOLTIP_DEFAULT_OPTIONS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTooltip, isStandalone: true, selector: \"[matTooltip]\", inputs: { position: [\"matTooltipPosition\", \"position\"], positionAtOrigin: [\"matTooltipPositionAtOrigin\", \"positionAtOrigin\"], disabled: [\"matTooltipDisabled\", \"disabled\"], showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, host: { properties: { \"class.mat-mdc-tooltip-disabled\": \"disabled\" }, classAttribute: \"mat-mdc-tooltip-trigger\" }, exportAs: [\"matTooltip\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTooltip]',\n                    exportAs: 'matTooltip',\n                    host: {\n                        'class': 'mat-mdc-tooltip-trigger',\n                        '[class.mat-mdc-tooltip-disabled]': 'disabled',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i2.Platform }, { type: i3.AriaDescriber }, { type: i3.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n                }] }, { type: i4.Directionality }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { position: [{\n                type: Input,\n                args: ['matTooltipPosition']\n            }], positionAtOrigin: [{\n                type: Input,\n                args: ['matTooltipPositionAtOrigin']\n            }], disabled: [{\n                type: Input,\n                args: ['matTooltipDisabled']\n            }], showDelay: [{\n                type: Input,\n                args: ['matTooltipShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['matTooltipHideDelay']\n            }], touchGestures: [{\n                type: Input,\n                args: ['matTooltipTouchGestures']\n            }], message: [{\n                type: Input,\n                args: ['matTooltip']\n            }], tooltipClass: [{\n                type: Input,\n                args: ['matTooltipClass']\n            }] } });\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n    constructor(_changeDetectorRef, _elementRef, animationMode) {\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        /* Whether the tooltip text overflows to multiple lines */\n        this._isMultiline = false;\n        /** Whether interactions on the page should close the tooltip */\n        this._closeOnInteraction = false;\n        /** Whether the tooltip is currently visible. */\n        this._isVisible = false;\n        /** Subject for notifying that the tooltip has been hidden from the view */\n        this._onHide = new Subject();\n        /** Name of the show animation and the class that toggles it. */\n        this._showAnimation = 'mat-mdc-tooltip-show';\n        /** Name of the hide animation and the class that toggles it. */\n        this._hideAnimation = 'mat-mdc-tooltip-hide';\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = setTimeout(() => {\n            this._toggleVisibility(true);\n            this._showTimeoutId = undefined;\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        this._hideTimeoutId = setTimeout(() => {\n            this._toggleVisibility(false);\n            this._hideTimeoutId = undefined;\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._isVisible;\n    }\n    ngOnDestroy() {\n        this._cancelPendingAnimations();\n        this._onHide.complete();\n        this._triggerElement = null;\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    _handleMouseLeave({ relatedTarget }) {\n        if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n            if (this.isVisible()) {\n                this.hide(this._mouseLeaveHideDelay);\n            }\n            else {\n                this._finalizeAnimation(false);\n            }\n        }\n    }\n    /**\n     * Callback for when the timeout in this.show() gets completed.\n     * This method is only needed by the mdc-tooltip, and so it is only implemented\n     * in the mdc-tooltip, not here.\n     */\n    _onShow() {\n        this._isMultiline = this._isTooltipMultiline();\n        this._markForCheck();\n    }\n    /** Whether the tooltip text has overflown to the next line */\n    _isTooltipMultiline() {\n        const rect = this._elementRef.nativeElement.getBoundingClientRect();\n        return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n    }\n    /** Event listener dispatched when an animation on the tooltip finishes. */\n    _handleAnimationEnd({ animationName }) {\n        if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n            this._finalizeAnimation(animationName === this._showAnimation);\n        }\n    }\n    /** Cancels any pending animation sequences. */\n    _cancelPendingAnimations() {\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = this._hideTimeoutId = undefined;\n    }\n    /** Handles the cleanup after an animation has finished. */\n    _finalizeAnimation(toVisible) {\n        if (toVisible) {\n            this._closeOnInteraction = true;\n        }\n        else if (!this.isVisible()) {\n            this._onHide.next();\n        }\n    }\n    /** Toggles the visibility of the tooltip element. */\n    _toggleVisibility(isVisible) {\n        // We set the classes directly here ourselves so that toggling the tooltip state\n        // isn't bound by change detection. This allows us to hide it even if the\n        // view ref has been detached from the CD tree.\n        const tooltip = this._tooltip.nativeElement;\n        const showClass = this._showAnimation;\n        const hideClass = this._hideAnimation;\n        tooltip.classList.remove(isVisible ? hideClass : showClass);\n        tooltip.classList.add(isVisible ? showClass : hideClass);\n        if (this._isVisible !== isVisible) {\n            this._isVisible = isVisible;\n            this._changeDetectorRef.markForCheck();\n        }\n        // It's common for internal apps to disable animations using `* { animation: none !important }`\n        // which can break the opening sequence. Try to detect such cases and work around them.\n        if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n            const styles = getComputedStyle(tooltip);\n            // Use `getPropertyValue` to avoid issues with property renaming.\n            if (styles.getPropertyValue('animation-duration') === '0s' ||\n                styles.getPropertyValue('animation-name') === 'none') {\n                this._animationsDisabled = true;\n            }\n        }\n        if (isVisible) {\n            this._onShow();\n        }\n        if (this._animationsDisabled) {\n            tooltip.classList.add('_mat-animation-noopable');\n            this._finalizeAnimation(isVisible);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: TooltipComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: TooltipComponent, isStandalone: true, selector: \"mat-tooltip-component\", host: { attributes: { \"aria-hidden\": \"true\" }, listeners: { \"mouseleave\": \"_handleMouseLeave($event)\" }, properties: { \"style.zoom\": \"isVisible() ? 1 : null\" } }, viewQueries: [{ propertyName: \"_tooltip\", first: true, predicate: [\"tooltip\"], descendants: true, static: true }], ngImport: i0, template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mdc-tooltip__surface{word-break:break-all;word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip--showing-transition .mdc-tooltip__surface-animation{transition:opacity 150ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-tooltip--hide-transition .mdc-tooltip__surface-animation{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - 2*8px);margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - 2*8px);align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mat-mdc-tooltip .mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color)}.mat-mdc-tooltip .mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__caret-surface-top,.mat-mdc-tooltip .mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color)}.mat-mdc-tooltip .mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font);line-height:var(--mdc-plain-tooltip-supporting-text-line-height);font-size:var(--mdc-plain-tooltip-supporting-text-size);font-weight:var(--mdc-plain-tooltip-supporting-text-weight);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"], dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: TooltipComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tooltip-component', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n                        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n                        '[style.zoom]': 'isVisible() ? 1 : null',\n                        '(mouseleave)': '_handleMouseLeave($event)',\n                        'aria-hidden': 'true',\n                    }, standalone: true, imports: [NgClass], template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mdc-tooltip__surface{word-break:break-all;word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip--showing-transition .mdc-tooltip__surface-animation{transition:opacity 150ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-tooltip--hide-transition .mdc-tooltip__surface-animation{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - 2*8px);margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - 2*8px);align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mat-mdc-tooltip .mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color)}.mat-mdc-tooltip .mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__caret-surface-top,.mat-mdc-tooltip .mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color)}.mat-mdc-tooltip .mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font);line-height:var(--mdc-plain-tooltip-supporting-text-line-height);font-size:var(--mdc-plain-tooltip-supporting-text-size);font-weight:var(--mdc-plain-tooltip-supporting-text-weight);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"] }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _tooltip: [{\n                type: ViewChild,\n                args: ['tooltip', {\n                        // Use a static query here since we interact directly with\n                        // the DOM which can happen before `ngAfterViewInit`.\n                        static: true,\n                    }]\n            }] } });\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n    /** Animation that transitions a tooltip in and out. */\n    tooltipState: trigger('state', [\n        // TODO(crisbeto): these values are based on MDC's CSS.\n        // We should be able to use their styles directly once we land #19432.\n        state('initial, void, hidden', style({ opacity: 0, transform: 'scale(0.8)' })),\n        state('visible', style({ transform: 'scale(1)' })),\n        transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n        transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n    ]),\n};\n\nclass MatTooltipModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent], exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n                    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, getMatTooltipInvalidPositionError, matTooltipAnimations };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACzM,SAASC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,OAAO,EAAEC,aAAa,QAAQ,sBAAsB;AAC7D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AAAA,MAAAC,GAAA;AACA,MAAMC,kBAAkB,GAAG,EAAE;AAC7B;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAACC,QAAQ,EAAE;EACjD,OAAOC,KAAK,CAAC,qBAAqBD,QAAQ,eAAe,CAAC;AAC9D;AACA;AACA,MAAME,2BAA2B,GAAG,IAAItC,cAAc,CAAC,6BAA6B,EAAE;EAClFuC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAGxC,MAAM,CAACqB,OAAO,CAAC;IAC/B,OAAO,MAAMmB,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;MAAEC,cAAc,EAAEV;IAAmB,CAAC,CAAC;EAC5F;AACJ,CAAC,CAAC;AACF;AACA,SAASW,mCAAmCA,CAACJ,OAAO,EAAE;EAClD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAAEC,cAAc,EAAEV;EAAmB,CAAC,CAAC;AAC5F;AACA;AACA,MAAMY,4CAA4C,GAAG;EACjDC,OAAO,EAAET,2BAA2B;EACpCU,IAAI,EAAE,CAAC1B,OAAO,CAAC;EACf2B,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA,SAASK,mCAAmCA,CAAA,EAAG;EAC3C,OAAO;IACHC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE;EACvB,CAAC;AACL;AACA;AACA,MAAMC,2BAA2B,GAAG,IAAItD,cAAc,CAAC,6BAA6B,EAAE;EAClFuC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEU;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMK,mBAAmB,GAAG,uBAAuB;AACnD,MAAMC,WAAW,GAAG,eAAe;AACnC;AACA,MAAMC,sBAAsB,GAAGxC,+BAA+B,CAAC;EAAEyC,OAAO,EAAE;AAAK,CAAC,CAAC;AACjF;AACA;AACA,MAAMC,8BAA8B,GAAG,CAAC;AACxC,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,GAAG,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb;EACA,IAAI3B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC4B,SAAS;EACzB;EACA,IAAI5B,QAAQA,CAAC6B,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACD,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGC,KAAK;MACtB,IAAI,IAAI,CAACC,WAAW,EAAE;QAClB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;QACtC,IAAI,CAACE,gBAAgB,EAAEC,IAAI,CAAC,CAAC,CAAC;QAC9B,IAAI,CAACH,WAAW,CAACI,cAAc,CAAC,CAAC;MACrC;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,iBAAiB;EACjC;EACA,IAAID,gBAAgBA,CAACN,KAAK,EAAE;IACxB,IAAI,CAACO,iBAAiB,GAAG7E,qBAAqB,CAACsE,KAAK,CAAC;IACrD,IAAI,CAACQ,OAAO,CAAC,CAAC;IACd,IAAI,CAACP,WAAW,GAAG,IAAI;EAC3B;EACA;EACA,IAAIQ,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACT,KAAK,EAAE;IAChB,IAAI,CAACU,SAAS,GAAGhF,qBAAqB,CAACsE,KAAK,CAAC;IAC7C;IACA,IAAI,IAAI,CAACU,SAAS,EAAE;MAChB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;IAC3C;EACJ;EACA;EACA,IAAI1B,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC2B,UAAU;EAC1B;EACA,IAAI3B,SAASA,CAACc,KAAK,EAAE;IACjB,IAAI,CAACa,UAAU,GAAGlF,oBAAoB,CAACqE,KAAK,CAAC;EACjD;EACA;EACA,IAAIb,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC2B,UAAU;EAC1B;EACA,IAAI3B,SAASA,CAACa,KAAK,EAAE;IACjB,IAAI,CAACc,UAAU,GAAGnF,oBAAoB,CAACqE,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACY,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAChE;EACJ;EACA;EACA,IAAIE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAAChB,KAAK,EAAE;IACf,IAAI,CAACkB,cAAc,CAACC,iBAAiB,CAAC,IAAI,CAACC,WAAW,CAACC,aAAa,EAAE,IAAI,CAACJ,QAAQ,EAAE,SAAS,CAAC;IAC/F;IACA;IACA;IACA,IAAI,CAACA,QAAQ,GAAGjB,KAAK,IAAI,IAAI,GAAGsB,MAAM,CAACtB,KAAK,CAAC,CAACuB,IAAI,CAAC,CAAC,GAAG,EAAE;IACzD,IAAI,CAAC,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACO,iBAAiB,CAAC,CAAC,EAAE;MAC5C,IAAI,CAACb,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;MACvC,IAAI,CAACa,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM;QACjC;QACA;QACA;QACA;QACAC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACZ,cAAc,CAACa,QAAQ,CAAC,IAAI,CAACX,WAAW,CAACC,aAAa,EAAE,IAAI,CAACL,OAAO,EAAE,SAAS,CAAC;QACzF,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;EACA,IAAIgB,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAAChC,KAAK,EAAE;IACpB,IAAI,CAACiC,aAAa,GAAGjC,KAAK;IAC1B,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACvB,IAAI,CAAC+B,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IAC7C;EACJ;EACAE,WAAWA,CAACC,QAAQ,EAAEhB,WAAW,EAAEiB,iBAAiB,EAAEC,iBAAiB,EAAEZ,OAAO,EAAEa,SAAS,EAAErB,cAAc,EAAEsB,aAAa,EAAEC,cAAc,EAAEC,IAAI,EAAEC,eAAe,EAAEC,SAAS,EAAE;IAC1K,IAAI,CAACR,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACiB,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACa,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACrB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACsB,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC5C,SAAS,GAAG,OAAO;IACxB,IAAI,CAACQ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,6BAA6B,GAAG,KAAK;IAC1C,IAAI,CAACC,iBAAiB,GAAGC,gBAAgB;IACzC,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAAG,SAAS;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,MAAM;IAC3B,IAAI,CAAClC,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACmC,iBAAiB,GAAG,EAAE;IAC3B;IACA,IAAI,CAACC,UAAU,GAAG,IAAI7F,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC8F,eAAe,GAAGb,cAAc;IACrC,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAID,eAAe,EAAE;MACjB,IAAI,CAAC9B,UAAU,GAAG8B,eAAe,CAACzD,SAAS;MAC3C,IAAI,CAAC4B,UAAU,GAAG6B,eAAe,CAACxD,SAAS;MAC3C,IAAIwD,eAAe,CAACxE,QAAQ,EAAE;QAC1B,IAAI,CAACA,QAAQ,GAAGwE,eAAe,CAACxE,QAAQ;MAC5C;MACA,IAAIwE,eAAe,CAACrC,gBAAgB,EAAE;QAClC,IAAI,CAACA,gBAAgB,GAAGqC,eAAe,CAACrC,gBAAgB;MAC5D;MACA,IAAIqC,eAAe,CAACQ,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAGR,eAAe,CAACQ,aAAa;MACtD;IACJ;IACAT,IAAI,CAACa,MAAM,CAACC,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAACI,SAAS,CAAC,MAAM;MACzD,IAAI,IAAI,CAACxD,WAAW,EAAE;QAClB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;MAC1C;IACJ,CAAC,CAAC;IACF,IAAI,CAACgD,eAAe,GAAGvD,8BAA8B;EACzD;EACAgE,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACb,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACjC,gCAAgC,CAAC,CAAC;IACvC,IAAI,CAAC4B,aAAa,CACbmB,OAAO,CAAC,IAAI,CAACvC,WAAW,CAAC,CACzBoC,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAACG,MAAM,IAAI;MACrB;MACA,IAAI,CAACA,MAAM,EAAE;QACT,IAAI,CAAClC,OAAO,CAACmC,GAAG,CAAC,MAAM,IAAI,CAAClD,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MACI,IAAIiD,MAAM,KAAK,UAAU,EAAE;QAC5B,IAAI,CAAClC,OAAO,CAACmC,GAAG,CAAC,MAAM,IAAI,CAACzD,IAAI,CAAC,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI0D,WAAWA,CAAA,EAAG;IACV,MAAMzC,aAAa,GAAG,IAAI,CAACD,WAAW,CAACC,aAAa;IACpD0C,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;IACrC,IAAI,IAAI,CAAC/D,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACgE,OAAO,CAAC,CAAC;MAC1B,IAAI,CAAC9D,gBAAgB,GAAG,IAAI;IAChC;IACA;IACA,IAAI,CAACiD,iBAAiB,CAACc,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MAClD/C,aAAa,CAACgD,mBAAmB,CAACF,KAAK,EAAEC,QAAQ,EAAE5E,sBAAsB,CAAC;IAC9E,CAAC,CAAC;IACF,IAAI,CAAC4D,iBAAiB,CAACkB,MAAM,GAAG,CAAC;IACjC,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAAC,CAAC;IACtB,IAAI,CAAClB,UAAU,CAACmB,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACtD,cAAc,CAACC,iBAAiB,CAACE,aAAa,EAAE,IAAI,CAACL,OAAO,EAAE,SAAS,CAAC;IAC7E,IAAI,CAACwB,aAAa,CAACiC,cAAc,CAACpD,aAAa,CAAC;EACpD;EACA;EACAjB,IAAIA,CAACsE,KAAK,GAAG,IAAI,CAACxF,SAAS,EAAE0E,MAAM,EAAE;IACjC,IAAI,IAAI,CAACnD,QAAQ,IAAI,CAAC,IAAI,CAACO,OAAO,IAAI,IAAI,CAACQ,iBAAiB,CAAC,CAAC,EAAE;MAC5D,IAAI,CAACrB,gBAAgB,EAAEwE,wBAAwB,CAAC,CAAC;MACjD;IACJ;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACjB,MAAM,CAAC;IAC9C,IAAI,CAACpD,OAAO,CAAC,CAAC;IACd,IAAI,CAACsE,OAAO,GACR,IAAI,CAACA,OAAO,IAAI,IAAIvH,eAAe,CAAC,IAAI,CAACwF,iBAAiB,EAAE,IAAI,CAACT,iBAAiB,CAAC;IACvF,MAAMyC,QAAQ,GAAI,IAAI,CAAC5E,gBAAgB,GAAGyE,UAAU,CAACI,MAAM,CAAC,IAAI,CAACF,OAAO,CAAC,CAACC,QAAS;IACnFA,QAAQ,CAACE,eAAe,GAAG,IAAI,CAAC7D,WAAW,CAACC,aAAa;IACzD0D,QAAQ,CAAChE,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAC/CiE,QAAQ,CACHG,WAAW,CAAC,CAAC,CACb1B,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAAC,MAAM,IAAI,CAACjD,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC0B,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IACzC,IAAI,CAACR,qBAAqB,CAAC,CAAC;IAC5BsD,QAAQ,CAAC3E,IAAI,CAACsE,KAAK,CAAC;EACxB;EACA;EACA/D,IAAIA,CAAC+D,KAAK,GAAG,IAAI,CAACvF,SAAS,EAAE;IACzB,MAAM4F,QAAQ,GAAG,IAAI,CAAC5E,gBAAgB;IACtC,IAAI4E,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE;QACtBJ,QAAQ,CAACpE,IAAI,CAAC+D,KAAK,CAAC;MACxB,CAAC,MACI;QACDK,QAAQ,CAACJ,wBAAwB,CAAC,CAAC;QACnC,IAAI,CAACnE,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACA4E,MAAMA,CAACxB,MAAM,EAAE;IACX,IAAI,CAACpC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACb,IAAI,CAAC,CAAC,GAAG,IAAI,CAACP,IAAI,CAACiF,SAAS,EAAEzB,MAAM,CAAC;EACzE;EACA;EACApC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACrB,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACgF,SAAS,CAAC,CAAC;EACvE;EACA;EACAN,cAAcA,CAACjB,MAAM,EAAE;IACnB,IAAI,IAAI,CAAC3D,WAAW,EAAE;MAClB,MAAMqF,gBAAgB,GAAG,IAAI,CAACrF,WAAW,CAACsF,SAAS,CAAC,CAAC,CAChDC,gBAAgB;MACrB,IAAI,CAAC,CAAC,IAAI,CAAClF,gBAAgB,IAAI,CAACsD,MAAM,KAAK0B,gBAAgB,CAACG,OAAO,YAAYxJ,UAAU,EAAE;QACvF,OAAO,IAAI,CAACgE,WAAW;MAC3B;MACA,IAAI,CAACO,OAAO,CAAC,CAAC;IAClB;IACA,MAAMkF,mBAAmB,GAAG,IAAI,CAACrD,iBAAiB,CAACsD,2BAA2B,CAAC,IAAI,CAACvE,WAAW,CAAC;IAChG;IACA,MAAMwE,QAAQ,GAAG,IAAI,CAACxD,QAAQ,CACzBjE,QAAQ,CAAC,CAAC,CACV0H,mBAAmB,CAAC,IAAI,CAACvF,gBAAgB,GAAGsD,MAAM,IAAI,IAAI,CAACxC,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAC1F0E,qBAAqB,CAAC,IAAI,IAAI,CAAC5C,eAAe,UAAU,CAAC,CACzD6C,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,kBAAkB,CAAC,IAAI,CAAC/C,eAAe,CAAC,CACxCgD,wBAAwB,CAACP,mBAAmB,CAAC;IAClDE,QAAQ,CAACM,eAAe,CAAC1C,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAACI,SAAS,CAACF,MAAM,IAAI;MAC1E,IAAI,CAAC4C,2BAA2B,CAAC5C,MAAM,CAAC6C,cAAc,CAAC;MACvD,IAAI,IAAI,CAACjG,gBAAgB,EAAE;QACvB,IAAIoD,MAAM,CAAC8C,wBAAwB,CAACC,gBAAgB,IAAI,IAAI,CAACnG,gBAAgB,CAACgF,SAAS,CAAC,CAAC,EAAE;UACvF;UACA;UACA,IAAI,CAACzD,OAAO,CAACmC,GAAG,CAAC,MAAM,IAAI,CAAClD,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACV,WAAW,GAAG,IAAI,CAACmC,QAAQ,CAACmE,MAAM,CAAC;MACpCC,SAAS,EAAE,IAAI,CAAC9D,IAAI;MACpB8C,gBAAgB,EAAEI,QAAQ;MAC1Ba,UAAU,EAAE,GAAG,IAAI,CAACvD,eAAe,IAAI3D,WAAW,EAAE;MACpDkD,cAAc,EAAE,IAAI,CAACa,eAAe,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAACpD,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;IACtC,IAAI,CAACA,WAAW,CACXyG,WAAW,CAAC,CAAC,CACblD,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAAC,MAAM,IAAI,CAACjD,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACP,WAAW,CACX0G,oBAAoB,CAAC,CAAC,CACtBnD,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAAC,MAAM,IAAI,CAACtD,gBAAgB,EAAEyG,sBAAsB,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC3G,WAAW,CACX4G,aAAa,CAAC,CAAC,CACfrD,IAAI,CAAChI,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAChCI,SAAS,CAACU,KAAK,IAAI;MACpB,IAAI,IAAI,CAAC3C,iBAAiB,CAAC,CAAC,IAAI2C,KAAK,CAAC2C,OAAO,KAAKlL,MAAM,IAAI,CAACC,cAAc,CAACsI,KAAK,CAAC,EAAE;QAChFA,KAAK,CAAC4C,cAAc,CAAC,CAAC;QACtB5C,KAAK,CAAC6C,eAAe,CAAC,CAAC;QACvB,IAAI,CAACtF,OAAO,CAACmC,GAAG,CAAC,MAAM,IAAI,CAAClD,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACgC,eAAe,EAAEsE,2BAA2B,EAAE;MACnD,IAAI,CAAChH,WAAW,CAACiH,aAAa,CAAC,GAAG,IAAI,CAAChE,eAAe,gCAAgC,CAAC;IAC3F;IACA,OAAO,IAAI,CAACjD,WAAW;EAC3B;EACA;EACAO,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACP,WAAW,IAAI,IAAI,CAACA,WAAW,CAACkH,WAAW,CAAC,CAAC,EAAE;MACpD,IAAI,CAAClH,WAAW,CAACmH,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAACjH,gBAAgB,GAAG,IAAI;EAChC;EACA;EACAD,eAAeA,CAAC0E,UAAU,EAAE;IACxB,MAAMzG,QAAQ,GAAGyG,UAAU,CAACW,SAAS,CAAC,CAAC,CAACC,gBAAgB;IACxD,MAAM5B,MAAM,GAAG,IAAI,CAACyD,UAAU,CAAC,CAAC;IAChC,MAAM7I,OAAO,GAAG,IAAI,CAAC8I,mBAAmB,CAAC,CAAC;IAC1CnJ,QAAQ,CAACoJ,aAAa,CAAC,CACnB,IAAI,CAACC,UAAU,CAAC;MAAE,GAAG5D,MAAM,CAAC6D,IAAI;MAAE,GAAGjJ,OAAO,CAACiJ;IAAK,CAAC,CAAC,EACpD,IAAI,CAACD,UAAU,CAAC;MAAE,GAAG5D,MAAM,CAAC8D,QAAQ;MAAE,GAAGlJ,OAAO,CAACkJ;IAAS,CAAC,CAAC,CAC/D,CAAC;EACN;EACA;EACAF,UAAUA,CAACrJ,QAAQ,EAAE;IACjB,MAAMwJ,MAAM,GAAGhI,oBAAoB;IACnC,MAAMiI,KAAK,GAAG,CAAC,IAAI,CAAClF,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,IAAI,KAAK;IACpD,IAAI7B,QAAQ,CAAC0J,OAAO,KAAK,KAAK,EAAE;MAC5B1J,QAAQ,CAAC2J,OAAO,GAAG,CAACH,MAAM;IAC9B,CAAC,MACI,IAAIxJ,QAAQ,CAAC0J,OAAO,KAAK,QAAQ,EAAE;MACpC1J,QAAQ,CAAC2J,OAAO,GAAGH,MAAM;IAC7B,CAAC,MACI,IAAIxJ,QAAQ,CAAC4J,OAAO,KAAK,OAAO,EAAE;MACnC5J,QAAQ,CAAC6J,OAAO,GAAGJ,KAAK,GAAG,CAACD,MAAM,GAAGA,MAAM;IAC/C,CAAC,MACI,IAAIxJ,QAAQ,CAAC4J,OAAO,KAAK,KAAK,EAAE;MACjC5J,QAAQ,CAAC6J,OAAO,GAAGJ,KAAK,GAAGD,MAAM,GAAG,CAACA,MAAM;IAC/C;IACA,OAAOxJ,QAAQ;EACnB;EACA;AACJ;AACA;AACA;EACIkJ,UAAUA,CAAA,EAAG;IACT,MAAMO,KAAK,GAAG,CAAC,IAAI,CAAClF,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,IAAI,KAAK;IACpD,MAAM7B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAI8J,cAAc;IAClB,IAAI9J,QAAQ,IAAI,OAAO,IAAIA,QAAQ,IAAI,OAAO,EAAE;MAC5C8J,cAAc,GAAG;QAAEF,OAAO,EAAE,QAAQ;QAAEF,OAAO,EAAE1J,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG;MAAS,CAAC;IAC3F,CAAC,MACI,IAAIA,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAIyJ,KAAM,IAC5BzJ,QAAQ,IAAI,OAAO,IAAI,CAACyJ,KAAM,EAAE;MACjCK,cAAc,GAAG;QAAEF,OAAO,EAAE,OAAO;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC5D,CAAC,MACI,IAAI1J,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAIyJ,KAAM,IAC7BzJ,QAAQ,IAAI,MAAM,IAAI,CAACyJ,KAAM,EAAE;MAChCK,cAAc,GAAG;QAAEF,OAAO,EAAE,KAAK;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC1D,CAAC,MACI,IAAI,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMhK,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEgK,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACJ,cAAc,CAACF,OAAO,EAAEE,cAAc,CAACJ,OAAO,CAAC;IACrF,OAAO;MACHJ,IAAI,EAAEQ,cAAc;MACpBP,QAAQ,EAAE;QAAEK,OAAO,EAAEI,CAAC;QAAEN,OAAO,EAAEO;MAAE;IACvC,CAAC;EACL;EACA;EACAd,mBAAmBA,CAAA,EAAG;IAClB,MAAMM,KAAK,GAAG,CAAC,IAAI,CAAClF,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,IAAI,KAAK;IACpD,MAAM7B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAImK,eAAe;IACnB,IAAInK,QAAQ,IAAI,OAAO,EAAE;MACrBmK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAChE,CAAC,MACI,IAAIrK,QAAQ,IAAI,OAAO,EAAE;MAC1BmK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAM,CAAC;IAC7D,CAAC,MACI,IAAIrK,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAIyJ,KAAM,IAC5BzJ,QAAQ,IAAI,OAAO,IAAI,CAACyJ,KAAM,EAAE;MACjCU,eAAe,GAAG;QAAEC,QAAQ,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC7D,CAAC,MACI,IAAIrK,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAIyJ,KAAM,IAC7BzJ,QAAQ,IAAI,MAAM,IAAI,CAACyJ,KAAM,EAAE;MAChCU,eAAe,GAAG;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC/D,CAAC,MACI,IAAI,OAAON,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMhK,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEgK,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACC,eAAe,CAACC,QAAQ,EAAED,eAAe,CAACE,QAAQ,CAAC;IACzF,OAAO;MACHf,IAAI,EAAEa,eAAe;MACrBZ,QAAQ,EAAE;QAAEa,QAAQ,EAAEJ,CAAC;QAAEK,QAAQ,EAAEJ;MAAE;IACzC,CAAC;EACL;EACA;EACA3G,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,IAAI,CAACtB,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACa,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5C,IAAI,CAACb,gBAAgB,CAACsI,aAAa,CAAC,CAAC;MACrC,IAAI,CAAC/G,OAAO,CAACgH,gBAAgB,CAAClF,IAAI,CAAC/H,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAACI,SAAS,CAAC,MAAM;QACpF,IAAI,IAAI,CAACtD,gBAAgB,EAAE;UACvB,IAAI,CAACF,WAAW,CAACI,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA6B,gBAAgBA,CAACF,YAAY,EAAE;IAC3B,IAAI,IAAI,CAAC7B,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAC6B,YAAY,GAAGA,YAAY;MACjD,IAAI,CAAC7B,gBAAgB,CAACsI,aAAa,CAAC,CAAC;IACzC;EACJ;EACA;EACAJ,eAAeA,CAACF,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAI,IAAI,CAACjK,QAAQ,KAAK,OAAO,IAAI,IAAI,CAACA,QAAQ,KAAK,OAAO,EAAE;MACxD,IAAIiK,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,QAAQ;MAChB,CAAC,MACI,IAAIA,CAAC,KAAK,QAAQ,EAAE;QACrBA,CAAC,GAAG,KAAK;MACb;IACJ,CAAC,MACI;MACD,IAAID,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,OAAO;MACf,CAAC,MACI,IAAIA,CAAC,KAAK,OAAO,EAAE;QACpBA,CAAC,GAAG,KAAK;MACb;IACJ;IACA,OAAO;MAAEA,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACAjC,2BAA2BA,CAACC,cAAc,EAAE;IACxC,MAAM;MAAEoC,QAAQ;MAAET,OAAO;MAAEF;IAAQ,CAAC,GAAGzB,cAAc;IACrD,IAAIuC,WAAW;IACf;IACA;IACA,IAAIH,QAAQ,KAAK,QAAQ,EAAE;MACvB;MACA;MACA;MACA,IAAI,IAAI,CAAC9F,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC1C,KAAK,KAAK,KAAK,EAAE;QACxC2I,WAAW,GAAGZ,OAAO,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;MACtD,CAAC,MACI;QACDY,WAAW,GAAGZ,OAAO,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;MACxD;IACJ,CAAC,MACI;MACDY,WAAW,GAAGH,QAAQ,KAAK,QAAQ,IAAIX,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;IAChF;IACA,IAAIc,WAAW,KAAK,IAAI,CAACC,gBAAgB,EAAE;MACvC,MAAMhE,UAAU,GAAG,IAAI,CAAC3E,WAAW;MACnC,IAAI2E,UAAU,EAAE;QACZ,MAAMiE,WAAW,GAAG,GAAG,IAAI,CAAC3F,eAAe,IAAI3D,WAAW,GAAG;QAC7DqF,UAAU,CAACkE,gBAAgB,CAACD,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC;QAChEhE,UAAU,CAACsC,aAAa,CAAC2B,WAAW,GAAGF,WAAW,CAAC;MACvD;MACA,IAAI,CAACC,gBAAgB,GAAGD,WAAW;IACvC;EACJ;EACA;EACA/H,gCAAgCA,CAAA,EAAG;IAC/B;IACA,IAAI,IAAI,CAACF,SAAS,IACd,CAAC,IAAI,CAACM,OAAO,IACb,CAAC,IAAI,CAAC6B,gBAAgB,IACtB,IAAI,CAACO,iBAAiB,CAACkB,MAAM,EAAE;MAC/B;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACyE,4BAA4B,CAAC,CAAC,EAAE;MACrC,IAAI,CAAC3F,iBAAiB,CAAC4F,IAAI,CAAC,CACxB,YAAY,EACZ7E,KAAK,IAAI;QACL,IAAI,CAAC8E,+BAA+B,CAAC,CAAC;QACtC,IAAIC,KAAK,GAAG7D,SAAS;QACrB,IAAIlB,KAAK,CAACgE,CAAC,KAAK9C,SAAS,IAAIlB,KAAK,CAACiE,CAAC,KAAK/C,SAAS,EAAE;UAChD6D,KAAK,GAAG/E,KAAK;QACjB;QACA,IAAI,CAAC/D,IAAI,CAACiF,SAAS,EAAE6D,KAAK,CAAC;MAC/B,CAAC,CACJ,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAAC/F,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAACgG,iCAAiC,CAAC,CAAC;MACxC,IAAI,CAAC/F,iBAAiB,CAAC4F,IAAI,CAAC,CACxB,YAAY,EACZ7E,KAAK,IAAI;QACL,MAAMiF,KAAK,GAAGjF,KAAK,CAACkF,aAAa,GAAG,CAAC,CAAC;QACtC,MAAMzF,MAAM,GAAGwF,KAAK,GAAG;UAAEjB,CAAC,EAAEiB,KAAK,CAACE,OAAO;UAAElB,CAAC,EAAEgB,KAAK,CAACG;QAAQ,CAAC,GAAGlE,SAAS;QACzE;QACA;QACA,IAAI,CAAC4D,+BAA+B,CAAC,CAAC;QACtClF,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;QACrC,MAAMwF,uBAAuB,GAAG,GAAG;QACnC,IAAI,CAACxF,kBAAkB,GAAGyF,UAAU,CAAC,MAAM,IAAI,CAACrJ,IAAI,CAACiF,SAAS,EAAEzB,MAAM,CAAC,EAAE,IAAI,CAACjB,eAAe,CAAC+G,uBAAuB,IAAIF,uBAAuB,CAAC;MACrJ,CAAC,CACJ,CAAC;IACN;IACA,IAAI,CAACG,aAAa,CAAC,IAAI,CAACvG,iBAAiB,CAAC;EAC9C;EACA6F,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACnG,6BAA6B,EAAE;MACpC;IACJ;IACA,IAAI,CAACA,6BAA6B,GAAG,IAAI;IACzC,MAAM8G,aAAa,GAAG,EAAE;IACxB,IAAI,IAAI,CAACb,4BAA4B,CAAC,CAAC,EAAE;MACrCa,aAAa,CAACZ,IAAI,CAAC,CACf,YAAY,EACZ7E,KAAK,IAAI;QACL,MAAM0F,SAAS,GAAG1F,KAAK,CAAC2F,aAAa;QACrC,IAAI,CAACD,SAAS,IAAI,CAAC,IAAI,CAAC5J,WAAW,EAAE8J,cAAc,CAACC,QAAQ,CAACH,SAAS,CAAC,EAAE;UACrE,IAAI,CAAClJ,IAAI,CAAC,CAAC;QACf;MACJ,CAAC,CACJ,EAAE,CAAC,OAAO,EAAEwD,KAAK,IAAI,IAAI,CAAC8F,cAAc,CAAC9F,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC,MACI,IAAI,IAAI,CAAChB,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAACgG,iCAAiC,CAAC,CAAC;MACxC,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;QAC3BnG,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;QACrC,IAAI,CAACrD,IAAI,CAAC,IAAI,CAACgC,eAAe,CAACvD,iBAAiB,CAAC;MACrD,CAAC;MACDwK,aAAa,CAACZ,IAAI,CAAC,CAAC,UAAU,EAAEkB,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAEA,gBAAgB,CAAC,CAAC;IACzF;IACA,IAAI,CAACP,aAAa,CAACC,aAAa,CAAC;IACjC,IAAI,CAACxG,iBAAiB,CAAC4F,IAAI,CAAC,GAAGY,aAAa,CAAC;EACjD;EACAD,aAAaA,CAACQ,SAAS,EAAE;IACrBA,SAAS,CAACjG,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MACrC,IAAI,CAAChD,WAAW,CAACC,aAAa,CAAC+I,gBAAgB,CAACjG,KAAK,EAAEC,QAAQ,EAAE5E,sBAAsB,CAAC;IAC5F,CAAC,CAAC;EACN;EACAuJ,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,CAAC,IAAI,CAACxG,SAAS,CAAC8H,GAAG,IAAI,CAAC,IAAI,CAAC9H,SAAS,CAAC+H,OAAO;EACzD;EACA;EACAL,cAAcA,CAAC9F,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC3C,iBAAiB,CAAC,CAAC,EAAE;MAC1B,MAAM+I,mBAAmB,GAAG,IAAI,CAAC3H,SAAS,CAAC4H,gBAAgB,CAACrG,KAAK,CAACmF,OAAO,EAAEnF,KAAK,CAACoF,OAAO,CAAC;MACzF,MAAMkB,OAAO,GAAG,IAAI,CAACrJ,WAAW,CAACC,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAIkJ,mBAAmB,KAAKE,OAAO,IAAI,CAACA,OAAO,CAACT,QAAQ,CAACO,mBAAmB,CAAC,EAAE;QAC3E,IAAI,CAAC5J,IAAI,CAAC,CAAC;MACf;IACJ;EACJ;EACA;EACAwI,iCAAiCA,CAAA,EAAG;IAChC,MAAMuB,QAAQ,GAAG,IAAI,CAACvH,aAAa;IACnC,IAAIuH,QAAQ,KAAK,KAAK,EAAE;MACpB,MAAMD,OAAO,GAAG,IAAI,CAACrJ,WAAW,CAACC,aAAa;MAC9C,MAAM1D,KAAK,GAAG8M,OAAO,CAAC9M,KAAK;MAC3B;MACA;MACA,IAAI+M,QAAQ,KAAK,IAAI,IAAKD,OAAO,CAACE,QAAQ,KAAK,OAAO,IAAIF,OAAO,CAACE,QAAQ,KAAK,UAAW,EAAE;QACxFhN,KAAK,CAACiN,UAAU,GACZjN,KAAK,CAACkN,YAAY,GACdlN,KAAK,CAACmN,gBAAgB,GAClBnN,KAAK,CAACoN,aAAa,GACf,MAAM;MAC1B;MACA;MACA;MACA,IAAIL,QAAQ,KAAK,IAAI,IAAI,CAACD,OAAO,CAACO,SAAS,EAAE;QACzCrN,KAAK,CAACsN,cAAc,GAAG,MAAM;MACjC;MACAtN,KAAK,CAACuN,WAAW,GAAG,MAAM;MAC1BvN,KAAK,CAACwN,uBAAuB,GAAG,aAAa;IACjD;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFxL,UAAU,EAApBhE,EAAE,CAAAyP,iBAAA,CAAoCnO,EAAE,CAACC,OAAO,GAAhDvB,EAAE,CAAAyP,iBAAA,CAA2DzP,EAAE,CAACG,UAAU,GAA1EH,EAAE,CAAAyP,iBAAA,CAAqFnO,EAAE,CAACoO,gBAAgB,GAA1G1P,EAAE,CAAAyP,iBAAA,CAAqHzP,EAAE,CAAC2P,gBAAgB,GAA1I3P,EAAE,CAAAyP,iBAAA,CAAqJzP,EAAE,CAAC4P,MAAM,GAAhK5P,EAAE,CAAAyP,iBAAA,CAA2KxO,EAAE,CAAC4O,QAAQ,GAAxL7P,EAAE,CAAAyP,iBAAA,CAAmMtO,EAAE,CAAC2O,aAAa,GAArN9P,EAAE,CAAAyP,iBAAA,CAAgOtO,EAAE,CAAC4O,YAAY,GAAjP/P,EAAE,CAAAyP,iBAAA,CAA4PlN,2BAA2B,GAAzRvC,EAAE,CAAAyP,iBAAA,CAAoSpO,EAAE,CAAC2O,cAAc,GAAvThQ,EAAE,CAAAyP,iBAAA,CAAkUlM,2BAA2B,MAA/VvD,EAAE,CAAAyP,iBAAA,CAA0X3O,QAAQ;IAAA,CAA4C;EAAE;EAClhB;IAAS,IAAI,CAACmP,IAAI,kBAD8EjQ,EAAE,CAAAkQ,iBAAA;MAAAC,IAAA,EACJnM,UAAU;MAAAoM,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADRzQ,EAAE,CAAA2Q,WAAA,6BAAAD,GAAA,CAAA/L,QACK,CAAC;QAAA;MAAA;MAAAiM,MAAA;QAAAvO,QAAA,GADRrC,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;QAAAtM,gBAAA,GAAFxE,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;QAAAnM,QAAA,GAAF3E,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;QAAA1N,SAAA,GAAFpD,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;QAAAzN,SAAA,GAAFrD,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;QAAAzJ,aAAA,GAAFrH,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;QAAA5L,OAAA,GAAFlF,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;QAAA5K,YAAA,GAAFlG,EAAE,CAAA6Q,YAAA,CAAAC,IAAA;MAAA;MAAAC,QAAA;MAAAC,UAAA;IAAA,EACsnB;EAAE;AAC9tB;AACA;EAAA,QAAA5E,SAAA,oBAAAA,SAAA,KAHoGpM,EAAE,CAAAiR,iBAAA,CAGXjN,UAAU,EAAc,CAAC;IACxGmM,IAAI,EAAE/P,SAAS;IACf8Q,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBJ,QAAQ,EAAE,YAAY;MACtBK,IAAI,EAAE;QACF,OAAO,EAAE,yBAAyB;QAClC,kCAAkC,EAAE;MACxC,CAAC;MACDJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAE7O,EAAE,CAACC;EAAQ,CAAC,EAAE;IAAE4O,IAAI,EAAEnQ,EAAE,CAACG;EAAW,CAAC,EAAE;IAAEgQ,IAAI,EAAE7O,EAAE,CAACoO;EAAiB,CAAC,EAAE;IAAES,IAAI,EAAEnQ,EAAE,CAAC2P;EAAiB,CAAC,EAAE;IAAEQ,IAAI,EAAEnQ,EAAE,CAAC4P;EAAO,CAAC,EAAE;IAAEO,IAAI,EAAElP,EAAE,CAAC4O;EAAS,CAAC,EAAE;IAAEM,IAAI,EAAEhP,EAAE,CAAC2O;EAAc,CAAC,EAAE;IAAEK,IAAI,EAAEhP,EAAE,CAAC4O;EAAa,CAAC,EAAE;IAAEI,IAAI,EAAE5G,SAAS;IAAE8H,UAAU,EAAE,CAAC;MAC/PlB,IAAI,EAAE9P,MAAM;MACZ6Q,IAAI,EAAE,CAAC3O,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAE4N,IAAI,EAAE9O,EAAE,CAAC2O;EAAe,CAAC,EAAE;IAAEG,IAAI,EAAE5G,SAAS;IAAE8H,UAAU,EAAE,CAAC;MAC/DlB,IAAI,EAAE7P;IACV,CAAC,EAAE;MACC6P,IAAI,EAAE9P,MAAM;MACZ6Q,IAAI,EAAE,CAAC3N,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAE4M,IAAI,EAAE5G,SAAS;IAAE8H,UAAU,EAAE,CAAC;MAClClB,IAAI,EAAE9P,MAAM;MACZ6Q,IAAI,EAAE,CAACpQ,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEuB,QAAQ,EAAE,CAAC;MACpC8N,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE1M,gBAAgB,EAAE,CAAC;MACnB2L,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEvM,QAAQ,EAAE,CAAC;MACXwL,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE9N,SAAS,EAAE,CAAC;MACZ+M,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE7N,SAAS,EAAE,CAAC;MACZ8M,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE7J,aAAa,EAAE,CAAC;MAChB8I,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEhM,OAAO,EAAE,CAAC;MACViL,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEhL,YAAY,EAAE,CAAC;MACfiK,IAAI,EAAE5P,KAAK;MACX2Q,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMhK,gBAAgB,CAAC;EACnBb,WAAWA,CAACiL,kBAAkB,EAAEhM,WAAW,EAAEiM,aAAa,EAAE;IACxD,IAAI,CAACD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAChM,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACkM,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,OAAO,GAAG,IAAIjQ,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACkQ,cAAc,GAAG,sBAAsB;IAC5C;IACA,IAAI,CAACC,cAAc,GAAG,sBAAsB;IAC5C,IAAI,CAACC,mBAAmB,GAAGP,aAAa,KAAK,gBAAgB;EACjE;EACA;AACJ;AACA;AACA;EACIjN,IAAIA,CAACsE,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACmJ,cAAc,IAAI,IAAI,EAAE;MAC7B9J,YAAY,CAAC,IAAI,CAAC8J,cAAc,CAAC;IACrC;IACA,IAAI,CAACC,cAAc,GAAGrE,UAAU,CAAC,MAAM;MACnC,IAAI,CAACsE,iBAAiB,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACD,cAAc,GAAGzI,SAAS;IACnC,CAAC,EAAEX,KAAK,CAAC;EACb;EACA;AACJ;AACA;AACA;EACI/D,IAAIA,CAAC+D,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACoJ,cAAc,IAAI,IAAI,EAAE;MAC7B/J,YAAY,CAAC,IAAI,CAAC+J,cAAc,CAAC;IACrC;IACA,IAAI,CAACD,cAAc,GAAGpE,UAAU,CAAC,MAAM;MACnC,IAAI,CAACsE,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACF,cAAc,GAAGxI,SAAS;IACnC,CAAC,EAAEX,KAAK,CAAC;EACb;EACA;EACAQ,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACuI,OAAO;EACvB;EACA;EACAtI,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACqI,UAAU;EAC1B;EACA1J,WAAWA,CAAA,EAAG;IACV,IAAI,CAACa,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAC8I,OAAO,CAACjJ,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACS,eAAe,GAAG,IAAI;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACI2B,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC2G,mBAAmB,EAAE;MAC1B,IAAI,CAAC5M,IAAI,CAAC,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI8H,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC2E,kBAAkB,CAACY,YAAY,CAAC,CAAC;EAC1C;EACAC,iBAAiBA,CAAC;IAAEnE;EAAc,CAAC,EAAE;IACjC,IAAI,CAACA,aAAa,IAAI,CAAC,IAAI,CAAC7E,eAAe,CAAC+E,QAAQ,CAACF,aAAa,CAAC,EAAE;MACjE,IAAI,IAAI,CAAC3E,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAACxE,IAAI,CAAC,IAAI,CAACI,oBAAoB,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACmN,kBAAkB,CAAC,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACb,YAAY,GAAG,IAAI,CAACc,mBAAmB,CAAC,CAAC;IAC9C,IAAI,CAAC3F,aAAa,CAAC,CAAC;EACxB;EACA;EACA2F,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,IAAI,GAAG,IAAI,CAACjN,WAAW,CAACC,aAAa,CAACiN,qBAAqB,CAAC,CAAC;IACnE,OAAOD,IAAI,CAACE,MAAM,GAAG3O,UAAU,IAAIyO,IAAI,CAACG,KAAK,IAAI3O,SAAS;EAC9D;EACA;EACA4O,mBAAmBA,CAAC;IAAEC;EAAc,CAAC,EAAE;IACnC,IAAIA,aAAa,KAAK,IAAI,CAAChB,cAAc,IAAIgB,aAAa,KAAK,IAAI,CAACf,cAAc,EAAE;MAChF,IAAI,CAACO,kBAAkB,CAACQ,aAAa,KAAK,IAAI,CAAChB,cAAc,CAAC;IAClE;EACJ;EACA;EACA/I,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACmJ,cAAc,IAAI,IAAI,EAAE;MAC7B/J,YAAY,CAAC,IAAI,CAAC+J,cAAc,CAAC;IACrC;IACA,IAAI,IAAI,CAACD,cAAc,IAAI,IAAI,EAAE;MAC7B9J,YAAY,CAAC,IAAI,CAAC8J,cAAc,CAAC;IACrC;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,cAAc,GAAGxI,SAAS;EACzD;EACA;EACA6I,kBAAkBA,CAACS,SAAS,EAAE;IAC1B,IAAIA,SAAS,EAAE;MACX,IAAI,CAACpB,mBAAmB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,CAAC,IAAI,CAACpI,SAAS,CAAC,CAAC,EAAE;MACxB,IAAI,CAACsI,OAAO,CAAClJ,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACAwJ,iBAAiBA,CAAC5I,SAAS,EAAE;IACzB;IACA;IACA;IACA,MAAMyJ,OAAO,GAAG,IAAI,CAACC,QAAQ,CAACxN,aAAa;IAC3C,MAAMyN,SAAS,GAAG,IAAI,CAACpB,cAAc;IACrC,MAAMqB,SAAS,GAAG,IAAI,CAACpB,cAAc;IACrCiB,OAAO,CAACI,SAAS,CAACC,MAAM,CAAC9J,SAAS,GAAG4J,SAAS,GAAGD,SAAS,CAAC;IAC3DF,OAAO,CAACI,SAAS,CAACE,GAAG,CAAC/J,SAAS,GAAG2J,SAAS,GAAGC,SAAS,CAAC;IACxD,IAAI,IAAI,CAACvB,UAAU,KAAKrI,SAAS,EAAE;MAC/B,IAAI,CAACqI,UAAU,GAAGrI,SAAS;MAC3B,IAAI,CAACiI,kBAAkB,CAACY,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI7I,SAAS,IAAI,CAAC,IAAI,CAACyI,mBAAmB,IAAI,OAAOuB,gBAAgB,KAAK,UAAU,EAAE;MAClF,MAAMC,MAAM,GAAGD,gBAAgB,CAACP,OAAO,CAAC;MACxC;MACA,IAAIQ,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,IAAI,IACtDD,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,MAAM,EAAE;QACtD,IAAI,CAACzB,mBAAmB,GAAG,IAAI;MACnC;IACJ;IACA,IAAIzI,SAAS,EAAE;MACX,IAAI,CAACgJ,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,IAAI,CAACP,mBAAmB,EAAE;MAC1BgB,OAAO,CAACI,SAAS,CAACE,GAAG,CAAC,yBAAyB,CAAC;MAChD,IAAI,CAAChB,kBAAkB,CAAC/I,SAAS,CAAC;IACtC;EACJ;EACA;IAAS,IAAI,CAACiG,IAAI,YAAAkE,yBAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAwFtI,gBAAgB,EApN1BlH,EAAE,CAAAyP,iBAAA,CAoN0CzP,EAAE,CAACyT,iBAAiB,GApNhEzT,EAAE,CAAAyP,iBAAA,CAoN2EzP,EAAE,CAACG,UAAU,GApN1FH,EAAE,CAAAyP,iBAAA,CAoNqGjP,qBAAqB;IAAA,CAA4D;EAAE;EAC1R;IAAS,IAAI,CAACkT,IAAI,kBArN8E1T,EAAE,CAAA2T,iBAAA;MAAAxD,IAAA,EAqNJjJ,gBAAgB;MAAAkJ,SAAA;MAAAwD,SAAA,WAAAC,uBAAApD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArNdzQ,EAAE,CAAA8T,WAAA,CAAA5R,GAAA;QAAA;QAAA,IAAAuO,EAAA;UAAA,IAAAsD,EAAA;UAAF/T,EAAE,CAAAgU,cAAA,CAAAD,EAAA,GAAF/T,EAAE,CAAAiU,WAAA,QAAAvD,GAAA,CAAAqC,QAAA,GAAAgB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA7D,SAAA,kBAqN0G,MAAM;MAAAC,QAAA;MAAAC,YAAA,WAAA4D,8BAAA1D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArNlHzQ,EAAE,CAAAoU,UAAA,wBAAAC,+CAAAC,MAAA;YAAA,OAqNJ5D,GAAA,CAAAyB,iBAAA,CAAAmC,MAAwB,CAAC;UAAA,CAAV,CAAC;QAAA;QAAA,IAAA7D,EAAA;UArNdzQ,EAAE,CAAAuU,WAAA,SAqNJ7D,GAAA,CAAArH,SAAA,CAAU,CAAC,GAAG,CAAC,GAAG,IAAH,CAAC;QAAA;MAAA;MAAA2H,UAAA;MAAAwD,QAAA,GArNdxU,EAAE,CAAAyU,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAArE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAsE,GAAA,GAAF/U,EAAE,CAAAgV,gBAAA;UAAFhV,EAAE,CAAAiV,cAAA,eAqNmkB,CAAC;UArNtkBjV,EAAE,CAAAoU,UAAA,0BAAAc,sDAAAZ,MAAA;YAAFtU,EAAE,CAAAmV,aAAA,CAAAJ,GAAA;YAAA,OAAF/U,EAAE,CAAAoV,WAAA,CAqNmf1E,GAAA,CAAAiC,mBAAA,CAAA2B,MAA0B,CAAC;UAAA,CAAC,CAAC;UArNlhBtU,EAAE,CAAAiV,cAAA,YAqN0oB,CAAC;UArN7oBjV,EAAE,CAAAqV,MAAA,EAqNqpB,CAAC;UArNxpBrV,EAAE,CAAAsV,YAAA,CAqN2pB,CAAC,CAAO,CAAC;QAAA;QAAA,IAAA7E,EAAA;UArNtqBzQ,EAAE,CAAA2Q,WAAA,2BAAAD,GAAA,CAAAc,YAqNkkB,CAAC;UArNrkBxR,EAAE,CAAAuV,UAAA,YAAA7E,GAAA,CAAAxK,YAqN6d,CAAC;UArNhelG,EAAE,CAAAwV,SAAA,EAqNqpB,CAAC;UArNxpBxV,EAAE,CAAAyV,iBAAA,CAAA/E,GAAA,CAAAxL,OAqNqpB,CAAC;QAAA;MAAA;MAAAwQ,YAAA,GAAsqJ3U,OAAO;MAAAuS,MAAA;MAAAqC,aAAA;MAAAC,eAAA;IAAA,EAAyJ;EAAE;AACpkL;AACA;EAAA,QAAAxJ,SAAA,oBAAAA,SAAA,KAvNoGpM,EAAE,CAAAiR,iBAAA,CAuNX/J,gBAAgB,EAAc,CAAC;IAC9GiJ,IAAI,EAAE1P,SAAS;IACfyQ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,uBAAuB;MAAEwE,aAAa,EAAEjV,iBAAiB,CAACoQ,IAAI;MAAE8E,eAAe,EAAEjV,uBAAuB,CAACkV,MAAM;MAAEzE,IAAI,EAAE;QAC9H;QACA;QACA,cAAc,EAAE,wBAAwB;QACxC,cAAc,EAAE,2BAA2B;QAC3C,aAAa,EAAE;MACnB,CAAC;MAAEJ,UAAU,EAAE,IAAI;MAAE8E,OAAO,EAAE,CAAC/U,OAAO,CAAC;MAAE8T,QAAQ,EAAE,oTAAoT;MAAEvB,MAAM,EAAE,CAAC,6lJAA6lJ;IAAE,CAAC;EAC99J,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnD,IAAI,EAAEnQ,EAAE,CAACyT;EAAkB,CAAC,EAAE;IAAEtD,IAAI,EAAEnQ,EAAE,CAACG;EAAW,CAAC,EAAE;IAAEgQ,IAAI,EAAE5G,SAAS;IAAE8H,UAAU,EAAE,CAAC;MACxGlB,IAAI,EAAE7P;IACV,CAAC,EAAE;MACC6P,IAAI,EAAE9P,MAAM;MACZ6Q,IAAI,EAAE,CAAC1Q,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEuS,QAAQ,EAAE,CAAC;MACpC5C,IAAI,EAAEvP,SAAS;MACfsQ,IAAI,EAAE,CAAC,SAAS,EAAE;QACV;QACA;QACA6E,MAAM,EAAE;MACZ,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB;EACAC,YAAY,EAAEtU,OAAO,CAAC,OAAO,EAAE;EAC3B;EACA;EACAC,KAAK,CAAC,uBAAuB,EAAEC,KAAK,CAAC;IAAEqU,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CAAC,CAAC,EAC9EvU,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IAAEsU,SAAS,EAAE;EAAW,CAAC,CAAC,CAAC,EAClDrU,UAAU,CAAC,cAAc,EAAEC,OAAO,CAAC,kCAAkC,CAAC,CAAC,EACvED,UAAU,CAAC,aAAa,EAAEC,OAAO,CAAC,iCAAiC,CAAC,CAAC,CACxE;AACL,CAAC;AAED,MAAMqU,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAAC9G,IAAI,YAAA+G,yBAAA7G,CAAA;MAAA,YAAAA,CAAA,IAAwF4G,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBAhQ8EtW,EAAE,CAAAuW,gBAAA;MAAApG,IAAA,EAgQSiG;IAAgB,EAAqL;EAAE;EAClT;IAAS,IAAI,CAACI,IAAI,kBAjQ8ExW,EAAE,CAAAyW,gBAAA;MAAAC,SAAA,EAiQsC,CAAC3T,4CAA4C,CAAC;MAAA+S,OAAA,GAAY1U,UAAU,EAAEJ,YAAY,EAAEQ,aAAa,EAAES,eAAe,EAAEA,eAAe,EAAED,mBAAmB;IAAA,EAAI;EAAE;AAC1S;AACA;EAAA,QAAAoK,SAAA,oBAAAA,SAAA,KAnQoGpM,EAAE,CAAAiR,iBAAA,CAmQXmF,gBAAgB,EAAc,CAAC;IAC9GjG,IAAI,EAAEtP,QAAQ;IACdqQ,IAAI,EAAE,CAAC;MACC4E,OAAO,EAAE,CAAC1U,UAAU,EAAEJ,YAAY,EAAEQ,aAAa,EAAES,eAAe,EAAE+B,UAAU,EAAEkD,gBAAgB,CAAC;MACjGyP,OAAO,EAAE,CAAC3S,UAAU,EAAEkD,gBAAgB,EAAEjF,eAAe,EAAED,mBAAmB,CAAC;MAC7E0U,SAAS,EAAE,CAAC3T,4CAA4C;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASQ,2BAA2B,EAAEJ,mCAAmC,EAAEZ,2BAA2B,EAAEO,mCAAmC,EAAEC,4CAA4C,EAAEiB,UAAU,EAAEoS,gBAAgB,EAAEjU,kBAAkB,EAAEqB,mBAAmB,EAAE0D,gBAAgB,EAAE9E,iCAAiC,EAAE4T,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}