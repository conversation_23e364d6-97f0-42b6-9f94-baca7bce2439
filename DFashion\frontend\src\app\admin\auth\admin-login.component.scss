.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 2rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
  }
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;

  .bg-pattern {
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes backgroundShift {
  0%, 100% {
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  }
  50% {
    background:
      radial-gradient(circle at 80% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 20% 80%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 60% 60%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  }
}

.login-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(25px);
  border-radius: 28px;
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  padding: 3.5rem;
  width: 100%;
  max-width: 540px;
  position: relative;
  z-index: 1;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    border-radius: 28px;
    pointer-events: none;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .logo-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: #667eea;
    }

    h1 {
      margin: 0;
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.02em;
    }
  }

  .subtitle {
    color: #666;
    margin: 0;
    font-size: 1rem;
  }
}

.login-form {
  margin-bottom: 2rem;

  .full-width {
    width: 100%;
    margin-bottom: 1rem;
  }

  .login-button {
    height: 56px;
    font-size: 1.125rem;
    font-weight: 600;
    margin-top: 1.5rem;
    position: relative;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    .login-spinner {
      margin-right: 0.5rem;
    }
  }
}

.demo-section {
  margin-bottom: 2rem;

  h3 {
    text-align: center;
    margin: 1.5rem 0 0.5rem 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 500;
  }

  .demo-description {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .demo-accounts {
    display: grid;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;

    .demo-account-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #e0e0e0;
      border-radius: 16px;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
        border-color: #667eea;
        background: rgba(255, 255, 255, 0.95);
      }

      mat-card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem !important;

        .account-info {
          flex: 1;

          .account-role {
            font-weight: 500;
            color: #333;
            font-size: 0.95rem;
          }

          .account-department {
            color: #667eea;
            font-size: 0.85rem;
            margin: 0.25rem 0;
          }

          .account-email {
            color: #666;
            font-size: 0.8rem;
            font-family: 'Courier New', monospace;
          }
        }

        .account-icon {
          color: #667eea;
          opacity: 0.7;
        }
      }
    }
  }
}

.login-footer {
  text-align: center;
  color: #666;
  font-size: 0.85rem;

  p {
    margin: 0 0 0.5rem 0;
  }

  .footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;

    .footer-link {
      color: #667eea;
      text-decoration: none;
      transition: color 0.2s ease;

      &:hover {
        color: #764ba2;
        text-decoration: underline;
      }
    }

    .separator {
      color: #ccc;
    }
  }
}

// Enhanced animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 3s infinite;
  border-radius: 24px;
  pointer-events: none;
}

// Responsive Design
@media (max-width: 600px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    padding: 2rem 1.5rem;
    max-width: none;
    border-radius: 20px;
  }

  .login-header .logo h1 {
    font-size: 2rem;
  }

  .demo-accounts {
    max-height: 250px;
  }

  .login-button {
    height: 52px;
    font-size: 1rem;
  }
}

// Custom Snackbar Styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
}

// Material Form Field Customization
::ng-deep {
  .mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: #e0e0e0;
    }

    &.mat-focused .mat-form-field-outline-thick {
      color: #667eea;
    }

    .mat-form-field-label {
      color: #666;
    }

    &.mat-focused .mat-form-field-label {
      color: #667eea;
    }
  }

  .mat-primary .mat-button-focus-overlay {
    background-color: #667eea;
  }
}

// Loading Animation
.login-spinner {
  ::ng-deep circle {
    stroke: white;
  }
}
