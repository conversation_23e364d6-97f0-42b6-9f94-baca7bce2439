{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as getAssetPath, r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { n as hasShadowDom, i as inheritAriaAttributes } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { o as openURL, c as createColorClasses$1, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode$1 } from './ionic-global-94f25d1b.js';\nlet CACHED_MAP;\nconst getIconMap = () => {\n  if (typeof window === 'undefined') {\n    return new Map();\n  } else {\n    if (!CACHED_MAP) {\n      const win = window;\n      win.Ionicons = win.Ionicons || {};\n      CACHED_MAP = win.Ionicons.map = win.Ionicons.map || new Map();\n    }\n    return CACHED_MAP;\n  }\n};\nconst getUrl = i => {\n  let url = getSrc(i.src);\n  if (url) {\n    return url;\n  }\n  url = getName(i.name, i.icon, i.mode, i.ios, i.md);\n  if (url) {\n    return getNamedUrl(url, i);\n  }\n  if (i.icon) {\n    url = getSrc(i.icon);\n    if (url) {\n      return url;\n    }\n    url = getSrc(i.icon[i.mode]);\n    if (url) {\n      return url;\n    }\n  }\n  return null;\n};\nconst getNamedUrl = (iconName, iconEl) => {\n  const url = getIconMap().get(iconName);\n  if (url) {\n    return url;\n  }\n  try {\n    return getAssetPath(`svg/${iconName}.svg`);\n  } catch (e) {\n    /**\n     * In the custom elements build version of ionicons, referencing an icon\n     * by name will throw an invalid URL error because the asset path is not defined.\n     * This catches that error and logs something that is more developer-friendly.\n     * We also include a reference to the ion-icon element so developers can\n     * figure out which instance of ion-icon needs to be updated.\n     */\n    console.warn(`[Ionicons Warning]: Could not load icon with name \"${iconName}\". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`, iconEl);\n  }\n};\nconst getName = (iconName, icon, mode, ios, md) => {\n  // default to \"md\" if somehow the mode wasn't set\n  mode = (mode && toLower(mode)) === 'ios' ? 'ios' : 'md';\n  // if an icon was passed in using the ios or md attributes\n  // set the iconName to whatever was passed in\n  if (ios && mode === 'ios') {\n    iconName = toLower(ios);\n  } else if (md && mode === 'md') {\n    iconName = toLower(md);\n  } else {\n    if (!iconName && icon && !isSrc(icon)) {\n      iconName = icon;\n    }\n    if (isStr(iconName)) {\n      iconName = toLower(iconName);\n    }\n  }\n  if (!isStr(iconName) || iconName.trim() === '') {\n    return null;\n  }\n  // only allow alpha characters and dash\n  const invalidChars = iconName.replace(/[a-z]|-|\\d/gi, '');\n  if (invalidChars !== '') {\n    return null;\n  }\n  return iconName;\n};\nconst getSrc = src => {\n  if (isStr(src)) {\n    src = src.trim();\n    if (isSrc(src)) {\n      return src;\n    }\n  }\n  return null;\n};\nconst isSrc = str => str.length > 0 && /(\\/|\\.)/.test(str);\nconst isStr = val => typeof val === 'string';\nconst toLower = val => val.toLowerCase();\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n  const attributeObject = {};\n  attributes.forEach(attr => {\n    if (el.hasAttribute(attr)) {\n      const value = el.getAttribute(attr);\n      if (value !== null) {\n        attributeObject[attr] = el.getAttribute(attr);\n      }\n      el.removeAttribute(attr);\n    }\n  });\n  return attributeObject;\n};\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = hostEl => {\n  if (hostEl) {\n    if (hostEl.dir !== '') {\n      return hostEl.dir.toLowerCase() === 'rtl';\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\nconst buttonIosCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #3880ff);--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=icon-only]){font-size:1.8em}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:14px;--padding-top:13px;--padding-bottom:13px;--padding-start:1em;--padding-end:1em;--transition:background-color, opacity 100ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:3.1em;font-size:min(1rem, 48px);font-weight:500;letter-spacing:0}:host(.button-solid){--background-activated:var(--ion-color-primary-shade, #3171e0);--background-focused:var(--ion-color-primary-shade, #3171e0);--background-hover:var(--ion-color-primary-tint, #4c8dff);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1}:host(.button-outline){--border-radius:14px;--border-width:1px;--border-style:solid;--background-activated:var(--ion-color-primary, #3880ff);--background-focused:var(--ion-color-primary, #3880ff);--background-hover:transparent;--background-focused-opacity:.1;--color-activated:var(--ion-color-primary-contrast, #fff)}:host(.button-clear){--background-activated:transparent;--background-activated-opacity:0;--background-focused:var(--ion-color-primary, #3880ff);--background-hover:transparent;--background-focused-opacity:.1;font-size:min(1.0625rem, 51px);font-weight:normal}:host(.in-buttons){font-size:clamp(17px, 1.0625rem, 21.08px);font-weight:400}:host(.button-large){--border-radius:16px;--padding-top:17px;--padding-start:1em;--padding-end:1em;--padding-bottom:17px;min-height:3.1em;font-size:min(1.25rem, 60px)}:host(.button-small){--border-radius:6px;--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:min(0.8125rem, 39px)}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:0}:host(.button-round){--border-radius:64px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-strong){font-weight:600}:host(.button-outline.ion-focused.ion-color) .button-native,:host(.button-clear.ion-focused.ion-color) .button-native{color:var(--ion-color-base)}:host(.button-outline.ion-focused.ion-color) .button-native::after,:host(.button-clear.ion-focused.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.button-clear:not(.ion-activated):hover),:host(.button-outline:not(.ion-activated):hover){opacity:0.6}:host(.button-clear.ion-color:hover) .button-native,:host(.button-outline.ion-color:hover) .button-native{color:var(--ion-color-base)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:transparent}:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}:host(:hover.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color):not(.ion-activated)) .button-native::after{background:#fff;opacity:0.1}}:host(.button-clear.ion-activated){opacity:0.4}:host(.button-outline.ion-activated.ion-color) .button-native{color:var(--ion-color-contrast)}:host(.button-outline.ion-activated.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--color));color:var(--ion-toolbar-background, var(--background), var(--ion-color-primary-contrast, #fff))}\";\nconst IonButtonIosStyle0 = buttonIosCss;\nconst buttonMdCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #3880ff);--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=icon-only]){font-size:1.8em}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:4px;--padding-top:8px;--padding-bottom:8px;--padding-start:1.1em;--padding-end:1.1em;--transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1),\\n                background-color 15ms linear,\\n                color 15ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:36px;font-size:0.875rem;font-weight:500;letter-spacing:0.06em;text-transform:uppercase}:host(.button-solid){--background-activated:transparent;--background-hover:var(--ion-color-primary-contrast, #fff);--background-focused:var(--ion-color-primary-contrast, #fff);--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)}:host(.button-solid.ion-activated){--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12)}:host(.button-outline){--border-width:2px;--border-style:solid;--box-shadow:none;--background-activated:transparent;--background-focused:var(--ion-color-primary, #3880ff);--background-hover:var(--ion-color-primary, #3880ff);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-outline.ion-activated.ion-color) .button-native{background:transparent}:host(.button-clear){--background-activated:transparent;--background-focused:var(--ion-color-primary, #3880ff);--background-hover:var(--ion-color-primary, #3880ff);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-round){--border-radius:64px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-large){--padding-top:14px;--padding-start:1em;--padding-end:1em;--padding-bottom:14px;min-height:2.8em;font-size:1.25rem}:host(.button-small){--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:0.8125rem}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:0}:host(.button-strong){font-weight:bold}::slotted(ion-icon[slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color.ion-focused) .button-native::after,:host(.button-outline.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-background, var(--color));color:var(--ion-toolbar-color, var(--background), var(--ion-color-primary-contrast, #fff))}\";\nconst IonButtonMdStyle0 = buttonMdCss;\nconst Button = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.inItem = false;\n    this.inListHeader = false;\n    this.inToolbar = false;\n    this.formButtonEl = null;\n    this.formEl = null;\n    this.inheritedAttributes = {};\n    this.handleClick = ev => {\n      const {\n        el\n      } = this;\n      if (this.type === 'button') {\n        openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n      } else if (hasShadowDom(el)) {\n        this.submitForm(ev);\n      }\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.color = undefined;\n    this.buttonType = 'button';\n    this.disabled = false;\n    this.expand = undefined;\n    this.fill = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.shape = undefined;\n    this.size = undefined;\n    this.strong = false;\n    this.target = undefined;\n    this.type = 'button';\n    this.form = undefined;\n  }\n  disabledChanged() {\n    const {\n      disabled\n    } = this;\n    if (this.formButtonEl) {\n      this.formButtonEl.disabled = disabled;\n    }\n  }\n  /**\n   * This is responsible for rendering a hidden native\n   * button element inside the associated form. This allows\n   * users to submit a form by pressing \"Enter\" when a text\n   * field inside of the form is focused. The native button\n   * rendered inside of `ion-button` is in the Shadow DOM\n   * and therefore does not participate in form submission\n   * which is why the following code is necessary.\n   */\n  renderHiddenButton() {\n    const formEl = this.formEl = this.findForm();\n    if (formEl) {\n      const {\n        formButtonEl\n      } = this;\n      /**\n       * If the form already has a rendered form button\n       * then do not append a new one again.\n       */\n      if (formButtonEl !== null && formEl.contains(formButtonEl)) {\n        return;\n      }\n      // Create a hidden native button inside of the form\n      const newFormButtonEl = this.formButtonEl = document.createElement('button');\n      newFormButtonEl.type = this.type;\n      newFormButtonEl.style.display = 'none';\n      // Only submit if the button is not disabled.\n      newFormButtonEl.disabled = this.disabled;\n      formEl.appendChild(newFormButtonEl);\n    }\n  }\n  componentWillLoad() {\n    this.inToolbar = !!this.el.closest('ion-buttons');\n    this.inListHeader = !!this.el.closest('ion-list-header');\n    this.inItem = !!this.el.closest('ion-item') || !!this.el.closest('ion-item-divider');\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  get hasIconOnly() {\n    return !!this.el.querySelector('[slot=\"icon-only\"]');\n  }\n  get rippleType() {\n    const hasClearFill = this.fill === undefined || this.fill === 'clear';\n    // If the button is in a toolbar, has a clear fill (which is the default)\n    // and only has an icon we use the unbounded \"circular\" ripple effect\n    if (hasClearFill && this.hasIconOnly && this.inToolbar) {\n      return 'unbounded';\n    }\n    return 'bounded';\n  }\n  /**\n   * Finds the form element based on the provided `form` selector\n   * or element reference provided.\n   */\n  findForm() {\n    const {\n      form\n    } = this;\n    if (form instanceof HTMLFormElement) {\n      return form;\n    }\n    if (typeof form === 'string') {\n      // Check if the string provided is a form id.\n      const el = document.getElementById(form);\n      if (el) {\n        if (el instanceof HTMLFormElement) {\n          return el;\n        } else {\n          /**\n           * The developer specified a string for the form attribute, but the\n           * element with that id is not a form element.\n           */\n          printIonWarning(`Form with selector: \"#${form}\" could not be found. Verify that the id is attached to a <form> element.`, this.el);\n          return null;\n        }\n      } else {\n        /**\n         * The developer specified a string for the form attribute, but the\n         * element with that id could not be found in the DOM.\n         */\n        printIonWarning(`Form with selector: \"#${form}\" could not be found. Verify that the id is correct and the form is rendered in the DOM.`, this.el);\n        return null;\n      }\n    }\n    if (form !== undefined) {\n      /**\n       * The developer specified a HTMLElement for the form attribute,\n       * but the element is not a HTMLFormElement.\n       * This will also catch if the developer tries to pass in null\n       * as the form attribute.\n       */\n      printIonWarning(`The provided \"form\" element is invalid. Verify that the form is a HTMLFormElement and rendered in the DOM.`, this.el);\n      return null;\n    }\n    /**\n     * If the form element is not set, the button may be inside\n     * of a form element. Query the closest form element to the button.\n     */\n    return this.el.closest('form');\n  }\n  submitForm(ev) {\n    // this button wants to specifically submit a form\n    // climb up the dom to see if we're in a <form>\n    // and if so, then use JS to submit it\n    if (this.formEl && this.formButtonEl) {\n      ev.preventDefault();\n      this.formButtonEl.click();\n    }\n  }\n  render() {\n    const mode = getIonMode$1(this);\n    const {\n      buttonType,\n      type,\n      disabled,\n      rel,\n      target,\n      size,\n      href,\n      color,\n      expand,\n      hasIconOnly,\n      shape,\n      strong,\n      inheritedAttributes\n    } = this;\n    const finalSize = size === undefined && this.inItem ? 'small' : size;\n    const TagType = href === undefined ? 'button' : 'a';\n    const attrs = TagType === 'button' ? {\n      type\n    } : {\n      download: this.download,\n      href,\n      rel,\n      target\n    };\n    let fill = this.fill;\n    /**\n     * We check both undefined and null to\n     * work around https://github.com/ionic-team/stencil/issues/3586.\n     */\n    if (fill == null) {\n      fill = this.inToolbar || this.inListHeader ? 'clear' : 'solid';\n    }\n    /**\n     * We call renderHiddenButton in the render function to account\n     * for any properties being set async. For example, changing the\n     * \"type\" prop from \"button\" to \"submit\" after the component has\n     * loaded would warrant the hidden button being added to the\n     * associated form.\n     */\n    {\n      type !== 'button' && this.renderHiddenButton();\n    }\n    return h(Host, {\n      key: '54569350aae49882123f0e22a720956eddee33f5',\n      onClick: this.handleClick,\n      \"aria-disabled\": disabled ? 'true' : null,\n      class: createColorClasses$1(color, {\n        [mode]: true,\n        [buttonType]: true,\n        [`${buttonType}-${expand}`]: expand !== undefined,\n        [`${buttonType}-${finalSize}`]: finalSize !== undefined,\n        [`${buttonType}-${shape}`]: shape !== undefined,\n        [`${buttonType}-${fill}`]: true,\n        [`${buttonType}-strong`]: strong,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'in-buttons': hostContext('ion-buttons', this.el),\n        'button-has-icon-only': hasIconOnly,\n        'button-disabled': disabled,\n        'ion-activatable': true,\n        'ion-focusable': true\n      })\n    }, h(TagType, Object.assign({\n      key: '95c196fc5bf080ef308532c3627e2a9d68f24996'\n    }, attrs, {\n      class: \"button-native\",\n      part: \"native\",\n      disabled: disabled,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur\n    }, inheritedAttributes), h(\"span\", {\n      key: 'e247013b847158187258d28f37ab23905fe49110',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: '58ac0bd8f643f4392ddbe465fab26eedffe02b7a',\n      name: \"icon-only\"\n    }), h(\"slot\", {\n      key: '2289d37a37a64472ffe818b3b1126945cebf101e',\n      name: \"start\"\n    }), h(\"slot\", {\n      key: 'b69a915dff465d251f0466449b91450d4a4048a9'\n    }), h(\"slot\", {\n      key: '594a80d6707fd5edabc03e467f7aae3011d020f6',\n      name: \"end\"\n    })), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: 'b034770cadefe74922ed6564a17147ddc6c9fdb1',\n      type: this.rippleType\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nButton.style = {\n  ios: IonButtonIosStyle0,\n  md: IonButtonMdStyle0\n};\nconst validateContent = svgContent => {\n  const div = document.createElement('div');\n  div.innerHTML = svgContent;\n  // setup this way to ensure it works on our buddy IE\n  for (let i = div.childNodes.length - 1; i >= 0; i--) {\n    if (div.childNodes[i].nodeName.toLowerCase() !== 'svg') {\n      div.removeChild(div.childNodes[i]);\n    }\n  }\n  // must only have 1 root element\n  const svgElm = div.firstElementChild;\n  if (svgElm && svgElm.nodeName.toLowerCase() === 'svg') {\n    const svgClass = svgElm.getAttribute('class') || '';\n    svgElm.setAttribute('class', (svgClass + ' s-ion-icon').trim());\n    // root element must be an svg\n    // lets double check we've got valid elements\n    // do not allow scripts\n    if (isValid(svgElm)) {\n      return div.innerHTML;\n    }\n  }\n  return '';\n};\nconst isValid = elm => {\n  if (elm.nodeType === 1) {\n    if (elm.nodeName.toLowerCase() === 'script') {\n      return false;\n    }\n    for (let i = 0; i < elm.attributes.length; i++) {\n      const name = elm.attributes[i].name;\n      if (isStr(name) && name.toLowerCase().indexOf('on') === 0) {\n        return false;\n      }\n    }\n    for (let i = 0; i < elm.childNodes.length; i++) {\n      if (!isValid(elm.childNodes[i])) {\n        return false;\n      }\n    }\n  }\n  return true;\n};\nconst isSvgDataUrl = url => url.startsWith('data:image/svg+xml');\nconst isEncodedDataUrl = url => url.indexOf(';utf8,') !== -1;\nconst ioniconContent = new Map();\nconst requests = new Map();\nlet parser;\nconst getSvgContent = (url, sanitize) => {\n  // see if we already have a request for this url\n  let req = requests.get(url);\n  if (!req) {\n    if (typeof fetch !== 'undefined' && typeof document !== 'undefined') {\n      /**\n       * If the url is a data url of an svg, then try to parse it\n       * with the DOMParser. This works with content security policies enabled.\n       */\n      if (isSvgDataUrl(url) && isEncodedDataUrl(url)) {\n        if (!parser) {\n          /**\n           * Create an instance of the DOM parser. This creates a single\n           * parser instance for the entire app, which is more efficient.\n           */\n          parser = new DOMParser();\n        }\n        const doc = parser.parseFromString(url, 'text/html');\n        const svg = doc.querySelector('svg');\n        if (svg) {\n          ioniconContent.set(url, svg.outerHTML);\n        }\n        return Promise.resolve();\n      } else {\n        // we don't already have a request\n        req = fetch(url).then(rsp => {\n          if (rsp.ok) {\n            return rsp.text().then(svgContent => {\n              if (svgContent && sanitize !== false) {\n                svgContent = validateContent(svgContent);\n              }\n              ioniconContent.set(url, svgContent || '');\n            });\n          }\n          ioniconContent.set(url, '');\n        });\n        // cache for the same requests\n        requests.set(url, req);\n      }\n    } else {\n      // set to empty for ssr scenarios and resolve promise\n      ioniconContent.set(url, '');\n      return Promise.resolve();\n    }\n  }\n  return req;\n};\nconst iconCss = \":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}\";\nconst IonIconStyle0 = iconCss;\nconst Icon = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.iconName = null;\n    this.inheritedAttributes = {};\n    this.didLoadIcon = false;\n    this.svgContent = undefined;\n    this.isVisible = false;\n    this.mode = getIonMode();\n    this.color = undefined;\n    this.ios = undefined;\n    this.md = undefined;\n    this.flipRtl = undefined;\n    this.name = undefined;\n    this.src = undefined;\n    this.icon = undefined;\n    this.size = undefined;\n    this.lazy = false;\n    this.sanitize = true;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  connectedCallback() {\n    // purposely do not return the promise here because loading\n    // the svg file should not hold up loading the app\n    // only load the svg if it's visible\n    this.waitUntilVisible(this.el, '50px', () => {\n      this.isVisible = true;\n      this.loadIcon();\n    });\n  }\n  componentDidLoad() {\n    /**\n     * Addresses an Angular issue where property values are assigned after the 'connectedCallback' but prior to the registration of watchers.\n     * This enhancement ensures the loading of an icon when the component has finished rendering and the icon has yet to apply the SVG data.\n     * This modification pertains to the usage of Angular's binding syntax:\n     * `<ion-icon [name]=\"myIconName\"></ion-icon>`\n     */\n    if (!this.didLoadIcon) {\n      this.loadIcon();\n    }\n  }\n  disconnectedCallback() {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n  waitUntilVisible(el, rootMargin, cb) {\n    if (this.lazy && typeof window !== 'undefined' && window.IntersectionObserver) {\n      const io = this.io = new window.IntersectionObserver(data => {\n        if (data[0].isIntersecting) {\n          io.disconnect();\n          this.io = undefined;\n          cb();\n        }\n      }, {\n        rootMargin\n      });\n      io.observe(el);\n    } else {\n      // browser doesn't support IntersectionObserver\n      // so just fallback to always show it\n      cb();\n    }\n  }\n  loadIcon() {\n    if (this.isVisible) {\n      const url = getUrl(this);\n      if (url) {\n        if (ioniconContent.has(url)) {\n          // sync if it's already loaded\n          this.svgContent = ioniconContent.get(url);\n        } else {\n          // async if it hasn't been loaded\n          getSvgContent(url, this.sanitize).then(() => this.svgContent = ioniconContent.get(url));\n        }\n        this.didLoadIcon = true;\n      }\n    }\n    this.iconName = getName(this.name, this.icon, this.mode, this.ios, this.md);\n  }\n  render() {\n    const {\n      flipRtl,\n      iconName,\n      inheritedAttributes,\n      el\n    } = this;\n    const mode = this.mode || 'md';\n    // we have designated that arrows & chevrons should automatically flip (unless flip-rtl is set to false) because \"back\" is left in ltr and right in rtl, and \"forward\" is the opposite\n    const shouldAutoFlip = iconName ? (iconName.includes('arrow') || iconName.includes('chevron')) && flipRtl !== false : false;\n    // if shouldBeFlippable is true, the icon should change direction when `dir` changes\n    const shouldBeFlippable = flipRtl || shouldAutoFlip;\n    return h(Host, Object.assign({\n      role: \"img\",\n      class: Object.assign(Object.assign({\n        [mode]: true\n      }, createColorClasses(this.color)), {\n        [`icon-${this.size}`]: !!this.size,\n        'flip-rtl': shouldBeFlippable,\n        'icon-rtl': shouldBeFlippable && isRTL(el)\n      })\n    }, inheritedAttributes), this.svgContent ? h(\"div\", {\n      class: \"icon-inner\",\n      innerHTML: this.svgContent\n    }) : h(\"div\", {\n      class: \"icon-inner\"\n    }));\n  }\n  static get assetsDirs() {\n    return [\"svg\"];\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"name\": [\"loadIcon\"],\n      \"src\": [\"loadIcon\"],\n      \"icon\": [\"loadIcon\"],\n      \"ios\": [\"loadIcon\"],\n      \"md\": [\"loadIcon\"]\n    };\n  }\n};\nconst getIonMode = () => typeof document !== 'undefined' && document.documentElement.getAttribute('mode') || 'md';\nconst createColorClasses = color => {\n  return color ? {\n    'ion-color': true,\n    [`ion-color-${color}`]: true\n  } : null;\n};\nIcon.style = IonIconStyle0;\nexport { Button as ion_button, Icon as ion_icon };", "map": {"version": 3, "names": ["j", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "n", "hasShadowDom", "i", "inheritAriaAttributes", "p", "printIonWarning", "o", "openURL", "c", "createColorClasses$1", "hostContext", "b", "getIonMode$1", "CACHED_MAP", "getIconMap", "window", "Map", "win", "Ionicons", "map", "getUrl", "url", "getSrc", "src", "getName", "name", "icon", "mode", "ios", "md", "getNamedUrl", "iconName", "iconEl", "get", "e", "console", "warn", "<PERSON><PERSON><PERSON><PERSON>", "isSrc", "isStr", "trim", "invalid<PERSON>hars", "replace", "str", "length", "test", "val", "toLowerCase", "inheritAttributes", "el", "attributes", "attributeObject", "for<PERSON>ach", "attr", "hasAttribute", "value", "getAttribute", "removeAttribute", "isRTL", "hostEl", "dir", "document", "buttonIosCss", "IonButtonIosStyle0", "buttonMdCss", "IonButtonMdStyle0", "<PERSON><PERSON>", "constructor", "hostRef", "ionFocus", "ionBlur", "inItem", "inList<PERSON><PERSON>er", "inToolbar", "formButtonEl", "formEl", "inheritedAttributes", "handleClick", "ev", "type", "href", "routerDirection", "routerAnimation", "submitForm", "onFocus", "emit", "onBlur", "color", "undefined", "buttonType", "disabled", "expand", "fill", "download", "rel", "shape", "size", "strong", "target", "form", "disabled<PERSON><PERSON>ed", "renderHiddenButton", "findForm", "contains", "newFormButtonEl", "createElement", "style", "display", "append<PERSON><PERSON><PERSON>", "componentWillLoad", "closest", "hasIconOnly", "querySelector", "rippleType", "hasClearFill", "HTMLFormElement", "getElementById", "preventDefault", "click", "render", "finalSize", "TagType", "attrs", "key", "onClick", "class", "Object", "assign", "part", "watchers", "validateContent", "svgContent", "div", "innerHTML", "childNodes", "nodeName", "<PERSON><PERSON><PERSON><PERSON>", "svgElm", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "svgClass", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "elm", "nodeType", "indexOf", "isSvgDataUrl", "startsWith", "isEncodedDataUrl", "ioniconContent", "requests", "parser", "getSvgContent", "sanitize", "req", "fetch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc", "parseFromString", "svg", "set", "outerHTML", "Promise", "resolve", "then", "rsp", "ok", "text", "iconCss", "IonIconStyle0", "Icon", "didLoadIcon", "isVisible", "getIonMode", "flipRtl", "lazy", "connectedCallback", "waitUntilVisible", "loadIcon", "componentDidLoad", "disconnectedCallback", "io", "disconnect", "rootMargin", "cb", "IntersectionObserver", "data", "isIntersecting", "observe", "has", "shouldAutoFlip", "includes", "shouldBeFlippable", "role", "createColorClasses", "assetsDirs", "documentElement", "ion_button", "ion_icon"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-button_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as getAssetPath, r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { n as hasShadowDom, i as inheritAriaAttributes } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { o as openURL, c as createColorClasses$1, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode$1 } from './ionic-global-94f25d1b.js';\n\nlet CACHED_MAP;\nconst getIconMap = () => {\n  if (typeof window === 'undefined') {\n    return new Map();\n  }\n  else {\n    if (!CACHED_MAP) {\n      const win = window;\n      win.Ionicons = win.Ionicons || {};\n      CACHED_MAP = win.Ionicons.map = win.Ionicons.map || new Map();\n    }\n    return CACHED_MAP;\n  }\n};\nconst getUrl = (i) => {\n  let url = getSrc(i.src);\n  if (url) {\n    return url;\n  }\n  url = getName(i.name, i.icon, i.mode, i.ios, i.md);\n  if (url) {\n    return getNamedUrl(url, i);\n  }\n  if (i.icon) {\n    url = getSrc(i.icon);\n    if (url) {\n      return url;\n    }\n    url = getSrc(i.icon[i.mode]);\n    if (url) {\n      return url;\n    }\n  }\n  return null;\n};\nconst getNamedUrl = (iconName, iconEl) => {\n  const url = getIconMap().get(iconName);\n  if (url) {\n    return url;\n  }\n  try {\n    return getAssetPath(`svg/${iconName}.svg`);\n  }\n  catch (e) {\n    /**\n     * In the custom elements build version of ionicons, referencing an icon\n     * by name will throw an invalid URL error because the asset path is not defined.\n     * This catches that error and logs something that is more developer-friendly.\n     * We also include a reference to the ion-icon element so developers can\n     * figure out which instance of ion-icon needs to be updated.\n     */\n    console.warn(`[Ionicons Warning]: Could not load icon with name \"${iconName}\". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`, iconEl);\n  }\n};\nconst getName = (iconName, icon, mode, ios, md) => {\n  // default to \"md\" if somehow the mode wasn't set\n  mode = (mode && toLower(mode)) === 'ios' ? 'ios' : 'md';\n  // if an icon was passed in using the ios or md attributes\n  // set the iconName to whatever was passed in\n  if (ios && mode === 'ios') {\n    iconName = toLower(ios);\n  }\n  else if (md && mode === 'md') {\n    iconName = toLower(md);\n  }\n  else {\n    if (!iconName && icon && !isSrc(icon)) {\n      iconName = icon;\n    }\n    if (isStr(iconName)) {\n      iconName = toLower(iconName);\n    }\n  }\n  if (!isStr(iconName) || iconName.trim() === '') {\n    return null;\n  }\n  // only allow alpha characters and dash\n  const invalidChars = iconName.replace(/[a-z]|-|\\d/gi, '');\n  if (invalidChars !== '') {\n    return null;\n  }\n  return iconName;\n};\nconst getSrc = (src) => {\n  if (isStr(src)) {\n    src = src.trim();\n    if (isSrc(src)) {\n      return src;\n    }\n  }\n  return null;\n};\nconst isSrc = (str) => str.length > 0 && /(\\/|\\.)/.test(str);\nconst isStr = (val) => typeof val === 'string';\nconst toLower = (val) => val.toLowerCase();\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n  const attributeObject = {};\n  attributes.forEach(attr => {\n    if (el.hasAttribute(attr)) {\n      const value = el.getAttribute(attr);\n      if (value !== null) {\n        attributeObject[attr] = el.getAttribute(attr);\n      }\n      el.removeAttribute(attr);\n    }\n  });\n  return attributeObject;\n};\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = (hostEl) => {\n  if (hostEl) {\n    if (hostEl.dir !== '') {\n      return hostEl.dir.toLowerCase() === 'rtl';\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\n\nconst buttonIosCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #3880ff);--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=icon-only]){font-size:1.8em}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:14px;--padding-top:13px;--padding-bottom:13px;--padding-start:1em;--padding-end:1em;--transition:background-color, opacity 100ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:3.1em;font-size:min(1rem, 48px);font-weight:500;letter-spacing:0}:host(.button-solid){--background-activated:var(--ion-color-primary-shade, #3171e0);--background-focused:var(--ion-color-primary-shade, #3171e0);--background-hover:var(--ion-color-primary-tint, #4c8dff);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1}:host(.button-outline){--border-radius:14px;--border-width:1px;--border-style:solid;--background-activated:var(--ion-color-primary, #3880ff);--background-focused:var(--ion-color-primary, #3880ff);--background-hover:transparent;--background-focused-opacity:.1;--color-activated:var(--ion-color-primary-contrast, #fff)}:host(.button-clear){--background-activated:transparent;--background-activated-opacity:0;--background-focused:var(--ion-color-primary, #3880ff);--background-hover:transparent;--background-focused-opacity:.1;font-size:min(1.0625rem, 51px);font-weight:normal}:host(.in-buttons){font-size:clamp(17px, 1.0625rem, 21.08px);font-weight:400}:host(.button-large){--border-radius:16px;--padding-top:17px;--padding-start:1em;--padding-end:1em;--padding-bottom:17px;min-height:3.1em;font-size:min(1.25rem, 60px)}:host(.button-small){--border-radius:6px;--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:min(0.8125rem, 39px)}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:0}:host(.button-round){--border-radius:64px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-strong){font-weight:600}:host(.button-outline.ion-focused.ion-color) .button-native,:host(.button-clear.ion-focused.ion-color) .button-native{color:var(--ion-color-base)}:host(.button-outline.ion-focused.ion-color) .button-native::after,:host(.button-clear.ion-focused.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.button-clear:not(.ion-activated):hover),:host(.button-outline:not(.ion-activated):hover){opacity:0.6}:host(.button-clear.ion-color:hover) .button-native,:host(.button-outline.ion-color:hover) .button-native{color:var(--ion-color-base)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:transparent}:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}:host(:hover.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color):not(.ion-activated)) .button-native::after{background:#fff;opacity:0.1}}:host(.button-clear.ion-activated){opacity:0.4}:host(.button-outline.ion-activated.ion-color) .button-native{color:var(--ion-color-contrast)}:host(.button-outline.ion-activated.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--color));color:var(--ion-toolbar-background, var(--background), var(--ion-color-primary-contrast, #fff))}\";\nconst IonButtonIosStyle0 = buttonIosCss;\n\nconst buttonMdCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #3880ff);--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #3880ff)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=icon-only]){font-size:1.8em}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:4px;--padding-top:8px;--padding-bottom:8px;--padding-start:1.1em;--padding-end:1.1em;--transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1),\\n                background-color 15ms linear,\\n                color 15ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:36px;font-size:0.875rem;font-weight:500;letter-spacing:0.06em;text-transform:uppercase}:host(.button-solid){--background-activated:transparent;--background-hover:var(--ion-color-primary-contrast, #fff);--background-focused:var(--ion-color-primary-contrast, #fff);--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)}:host(.button-solid.ion-activated){--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12)}:host(.button-outline){--border-width:2px;--border-style:solid;--box-shadow:none;--background-activated:transparent;--background-focused:var(--ion-color-primary, #3880ff);--background-hover:var(--ion-color-primary, #3880ff);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-outline.ion-activated.ion-color) .button-native{background:transparent}:host(.button-clear){--background-activated:transparent;--background-focused:var(--ion-color-primary, #3880ff);--background-hover:var(--ion-color-primary, #3880ff);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-round){--border-radius:64px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-large){--padding-top:14px;--padding-start:1em;--padding-end:1em;--padding-bottom:14px;min-height:2.8em;font-size:1.25rem}:host(.button-small){--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:0.8125rem}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:0}:host(.button-strong){font-weight:bold}::slotted(ion-icon[slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color.ion-focused) .button-native::after,:host(.button-outline.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-background, var(--color));color:var(--ion-toolbar-color, var(--background), var(--ion-color-primary-contrast, #fff))}\";\nconst IonButtonMdStyle0 = buttonMdCss;\n\nconst Button = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inItem = false;\n        this.inListHeader = false;\n        this.inToolbar = false;\n        this.formButtonEl = null;\n        this.formEl = null;\n        this.inheritedAttributes = {};\n        this.handleClick = (ev) => {\n            const { el } = this;\n            if (this.type === 'button') {\n                openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n            }\n            else if (hasShadowDom(el)) {\n                this.submitForm(ev);\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.color = undefined;\n        this.buttonType = 'button';\n        this.disabled = false;\n        this.expand = undefined;\n        this.fill = undefined;\n        this.routerDirection = 'forward';\n        this.routerAnimation = undefined;\n        this.download = undefined;\n        this.href = undefined;\n        this.rel = undefined;\n        this.shape = undefined;\n        this.size = undefined;\n        this.strong = false;\n        this.target = undefined;\n        this.type = 'button';\n        this.form = undefined;\n    }\n    disabledChanged() {\n        const { disabled } = this;\n        if (this.formButtonEl) {\n            this.formButtonEl.disabled = disabled;\n        }\n    }\n    /**\n     * This is responsible for rendering a hidden native\n     * button element inside the associated form. This allows\n     * users to submit a form by pressing \"Enter\" when a text\n     * field inside of the form is focused. The native button\n     * rendered inside of `ion-button` is in the Shadow DOM\n     * and therefore does not participate in form submission\n     * which is why the following code is necessary.\n     */\n    renderHiddenButton() {\n        const formEl = (this.formEl = this.findForm());\n        if (formEl) {\n            const { formButtonEl } = this;\n            /**\n             * If the form already has a rendered form button\n             * then do not append a new one again.\n             */\n            if (formButtonEl !== null && formEl.contains(formButtonEl)) {\n                return;\n            }\n            // Create a hidden native button inside of the form\n            const newFormButtonEl = (this.formButtonEl = document.createElement('button'));\n            newFormButtonEl.type = this.type;\n            newFormButtonEl.style.display = 'none';\n            // Only submit if the button is not disabled.\n            newFormButtonEl.disabled = this.disabled;\n            formEl.appendChild(newFormButtonEl);\n        }\n    }\n    componentWillLoad() {\n        this.inToolbar = !!this.el.closest('ion-buttons');\n        this.inListHeader = !!this.el.closest('ion-list-header');\n        this.inItem = !!this.el.closest('ion-item') || !!this.el.closest('ion-item-divider');\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    get hasIconOnly() {\n        return !!this.el.querySelector('[slot=\"icon-only\"]');\n    }\n    get rippleType() {\n        const hasClearFill = this.fill === undefined || this.fill === 'clear';\n        // If the button is in a toolbar, has a clear fill (which is the default)\n        // and only has an icon we use the unbounded \"circular\" ripple effect\n        if (hasClearFill && this.hasIconOnly && this.inToolbar) {\n            return 'unbounded';\n        }\n        return 'bounded';\n    }\n    /**\n     * Finds the form element based on the provided `form` selector\n     * or element reference provided.\n     */\n    findForm() {\n        const { form } = this;\n        if (form instanceof HTMLFormElement) {\n            return form;\n        }\n        if (typeof form === 'string') {\n            // Check if the string provided is a form id.\n            const el = document.getElementById(form);\n            if (el) {\n                if (el instanceof HTMLFormElement) {\n                    return el;\n                }\n                else {\n                    /**\n                     * The developer specified a string for the form attribute, but the\n                     * element with that id is not a form element.\n                     */\n                    printIonWarning(`Form with selector: \"#${form}\" could not be found. Verify that the id is attached to a <form> element.`, this.el);\n                    return null;\n                }\n            }\n            else {\n                /**\n                 * The developer specified a string for the form attribute, but the\n                 * element with that id could not be found in the DOM.\n                 */\n                printIonWarning(`Form with selector: \"#${form}\" could not be found. Verify that the id is correct and the form is rendered in the DOM.`, this.el);\n                return null;\n            }\n        }\n        if (form !== undefined) {\n            /**\n             * The developer specified a HTMLElement for the form attribute,\n             * but the element is not a HTMLFormElement.\n             * This will also catch if the developer tries to pass in null\n             * as the form attribute.\n             */\n            printIonWarning(`The provided \"form\" element is invalid. Verify that the form is a HTMLFormElement and rendered in the DOM.`, this.el);\n            return null;\n        }\n        /**\n         * If the form element is not set, the button may be inside\n         * of a form element. Query the closest form element to the button.\n         */\n        return this.el.closest('form');\n    }\n    submitForm(ev) {\n        // this button wants to specifically submit a form\n        // climb up the dom to see if we're in a <form>\n        // and if so, then use JS to submit it\n        if (this.formEl && this.formButtonEl) {\n            ev.preventDefault();\n            this.formButtonEl.click();\n        }\n    }\n    render() {\n        const mode = getIonMode$1(this);\n        const { buttonType, type, disabled, rel, target, size, href, color, expand, hasIconOnly, shape, strong, inheritedAttributes, } = this;\n        const finalSize = size === undefined && this.inItem ? 'small' : size;\n        const TagType = href === undefined ? 'button' : 'a';\n        const attrs = TagType === 'button'\n            ? { type }\n            : {\n                download: this.download,\n                href,\n                rel,\n                target,\n            };\n        let fill = this.fill;\n        /**\n         * We check both undefined and null to\n         * work around https://github.com/ionic-team/stencil/issues/3586.\n         */\n        if (fill == null) {\n            fill = this.inToolbar || this.inListHeader ? 'clear' : 'solid';\n        }\n        /**\n         * We call renderHiddenButton in the render function to account\n         * for any properties being set async. For example, changing the\n         * \"type\" prop from \"button\" to \"submit\" after the component has\n         * loaded would warrant the hidden button being added to the\n         * associated form.\n         */\n        {\n            type !== 'button' && this.renderHiddenButton();\n        }\n        return (h(Host, { key: '54569350aae49882123f0e22a720956eddee33f5', onClick: this.handleClick, \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses$1(color, {\n                [mode]: true,\n                [buttonType]: true,\n                [`${buttonType}-${expand}`]: expand !== undefined,\n                [`${buttonType}-${finalSize}`]: finalSize !== undefined,\n                [`${buttonType}-${shape}`]: shape !== undefined,\n                [`${buttonType}-${fill}`]: true,\n                [`${buttonType}-strong`]: strong,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'in-buttons': hostContext('ion-buttons', this.el),\n                'button-has-icon-only': hasIconOnly,\n                'button-disabled': disabled,\n                'ion-activatable': true,\n                'ion-focusable': true,\n            }) }, h(TagType, Object.assign({ key: '95c196fc5bf080ef308532c3627e2a9d68f24996' }, attrs, { class: \"button-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur }, inheritedAttributes), h(\"span\", { key: 'e247013b847158187258d28f37ab23905fe49110', class: \"button-inner\" }, h(\"slot\", { key: '58ac0bd8f643f4392ddbe465fab26eedffe02b7a', name: \"icon-only\" }), h(\"slot\", { key: '2289d37a37a64472ffe818b3b1126945cebf101e', name: \"start\" }), h(\"slot\", { key: 'b69a915dff465d251f0466449b91450d4a4048a9' }), h(\"slot\", { key: '594a80d6707fd5edabc03e467f7aae3011d020f6', name: \"end\" })), mode === 'md' && h(\"ion-ripple-effect\", { key: 'b034770cadefe74922ed6564a17147ddc6c9fdb1', type: this.rippleType }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nButton.style = {\n    ios: IonButtonIosStyle0,\n    md: IonButtonMdStyle0\n};\n\nconst validateContent = (svgContent) => {\n  const div = document.createElement('div');\n  div.innerHTML = svgContent;\n  // setup this way to ensure it works on our buddy IE\n  for (let i = div.childNodes.length - 1; i >= 0; i--) {\n    if (div.childNodes[i].nodeName.toLowerCase() !== 'svg') {\n      div.removeChild(div.childNodes[i]);\n    }\n  }\n  // must only have 1 root element\n  const svgElm = div.firstElementChild;\n  if (svgElm && svgElm.nodeName.toLowerCase() === 'svg') {\n    const svgClass = svgElm.getAttribute('class') || '';\n    svgElm.setAttribute('class', (svgClass + ' s-ion-icon').trim());\n    // root element must be an svg\n    // lets double check we've got valid elements\n    // do not allow scripts\n    if (isValid(svgElm)) {\n      return div.innerHTML;\n    }\n  }\n  return '';\n};\nconst isValid = (elm) => {\n  if (elm.nodeType === 1) {\n    if (elm.nodeName.toLowerCase() === 'script') {\n      return false;\n    }\n    for (let i = 0; i < elm.attributes.length; i++) {\n      const name = elm.attributes[i].name;\n      if (isStr(name) && name.toLowerCase().indexOf('on') === 0) {\n        return false;\n      }\n    }\n    for (let i = 0; i < elm.childNodes.length; i++) {\n      if (!isValid(elm.childNodes[i])) {\n        return false;\n      }\n    }\n  }\n  return true;\n};\nconst isSvgDataUrl = (url) => url.startsWith('data:image/svg+xml');\nconst isEncodedDataUrl = (url) => url.indexOf(';utf8,') !== -1;\n\nconst ioniconContent = new Map();\nconst requests = new Map();\nlet parser;\nconst getSvgContent = (url, sanitize) => {\n  // see if we already have a request for this url\n  let req = requests.get(url);\n  if (!req) {\n    if (typeof fetch !== 'undefined' && typeof document !== 'undefined') {\n      /**\n       * If the url is a data url of an svg, then try to parse it\n       * with the DOMParser. This works with content security policies enabled.\n       */\n      if (isSvgDataUrl(url) && isEncodedDataUrl(url)) {\n        if (!parser) {\n          /**\n           * Create an instance of the DOM parser. This creates a single\n           * parser instance for the entire app, which is more efficient.\n           */\n          parser = new DOMParser();\n        }\n        const doc = parser.parseFromString(url, 'text/html');\n        const svg = doc.querySelector('svg');\n        if (svg) {\n          ioniconContent.set(url, svg.outerHTML);\n        }\n        return Promise.resolve();\n      }\n      else {\n        // we don't already have a request\n        req = fetch(url).then((rsp) => {\n          if (rsp.ok) {\n            return rsp.text().then((svgContent) => {\n              if (svgContent && sanitize !== false) {\n                svgContent = validateContent(svgContent);\n              }\n              ioniconContent.set(url, svgContent || '');\n            });\n          }\n          ioniconContent.set(url, '');\n        });\n        // cache for the same requests\n        requests.set(url, req);\n      }\n    }\n    else {\n      // set to empty for ssr scenarios and resolve promise\n      ioniconContent.set(url, '');\n      return Promise.resolve();\n    }\n  }\n  return req;\n};\n\nconst iconCss = \":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}\";\nconst IonIconStyle0 = iconCss;\n\nconst Icon = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.iconName = null;\n        this.inheritedAttributes = {};\n        this.didLoadIcon = false;\n        this.svgContent = undefined;\n        this.isVisible = false;\n        this.mode = getIonMode();\n        this.color = undefined;\n        this.ios = undefined;\n        this.md = undefined;\n        this.flipRtl = undefined;\n        this.name = undefined;\n        this.src = undefined;\n        this.icon = undefined;\n        this.size = undefined;\n        this.lazy = false;\n        this.sanitize = true;\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    connectedCallback() {\n        // purposely do not return the promise here because loading\n        // the svg file should not hold up loading the app\n        // only load the svg if it's visible\n        this.waitUntilVisible(this.el, '50px', () => {\n            this.isVisible = true;\n            this.loadIcon();\n        });\n    }\n    componentDidLoad() {\n        /**\n         * Addresses an Angular issue where property values are assigned after the 'connectedCallback' but prior to the registration of watchers.\n         * This enhancement ensures the loading of an icon when the component has finished rendering and the icon has yet to apply the SVG data.\n         * This modification pertains to the usage of Angular's binding syntax:\n         * `<ion-icon [name]=\"myIconName\"></ion-icon>`\n         */\n        if (!this.didLoadIcon) {\n            this.loadIcon();\n        }\n    }\n    disconnectedCallback() {\n        if (this.io) {\n            this.io.disconnect();\n            this.io = undefined;\n        }\n    }\n    waitUntilVisible(el, rootMargin, cb) {\n        if (this.lazy && typeof window !== 'undefined' && window.IntersectionObserver) {\n            const io = (this.io = new window.IntersectionObserver((data) => {\n                if (data[0].isIntersecting) {\n                    io.disconnect();\n                    this.io = undefined;\n                    cb();\n                }\n            }, { rootMargin }));\n            io.observe(el);\n        }\n        else {\n            // browser doesn't support IntersectionObserver\n            // so just fallback to always show it\n            cb();\n        }\n    }\n    loadIcon() {\n        if (this.isVisible) {\n            const url = getUrl(this);\n            if (url) {\n                if (ioniconContent.has(url)) {\n                    // sync if it's already loaded\n                    this.svgContent = ioniconContent.get(url);\n                }\n                else {\n                    // async if it hasn't been loaded\n                    getSvgContent(url, this.sanitize).then(() => (this.svgContent = ioniconContent.get(url)));\n                }\n                this.didLoadIcon = true;\n            }\n        }\n        this.iconName = getName(this.name, this.icon, this.mode, this.ios, this.md);\n    }\n    render() {\n        const { flipRtl, iconName, inheritedAttributes, el } = this;\n        const mode = this.mode || 'md';\n        // we have designated that arrows & chevrons should automatically flip (unless flip-rtl is set to false) because \"back\" is left in ltr and right in rtl, and \"forward\" is the opposite\n        const shouldAutoFlip = iconName\n            ? (iconName.includes('arrow') || iconName.includes('chevron')) && flipRtl !== false\n            : false;\n        // if shouldBeFlippable is true, the icon should change direction when `dir` changes\n        const shouldBeFlippable = flipRtl || shouldAutoFlip;\n        return (h(Host, Object.assign({ role: \"img\", class: Object.assign(Object.assign({ [mode]: true }, createColorClasses(this.color)), { [`icon-${this.size}`]: !!this.size, 'flip-rtl': shouldBeFlippable, 'icon-rtl': shouldBeFlippable && isRTL(el) }) }, inheritedAttributes), this.svgContent ? (h(\"div\", { class: \"icon-inner\", innerHTML: this.svgContent })) : (h(\"div\", { class: \"icon-inner\" }))));\n    }\n    static get assetsDirs() { return [\"svg\"]; }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"name\": [\"loadIcon\"],\n        \"src\": [\"loadIcon\"],\n        \"icon\": [\"loadIcon\"],\n        \"ios\": [\"loadIcon\"],\n        \"md\": [\"loadIcon\"]\n    }; }\n};\nconst getIonMode = () => (typeof document !== 'undefined' && document.documentElement.getAttribute('mode')) || 'md';\nconst createColorClasses = (color) => {\n    return color\n        ? {\n            'ion-color': true,\n            [`ion-color-${color}`]: true,\n        }\n        : null;\n};\nIcon.style = IonIconStyle0;\n\nexport { Button as ion_button, Icon as ion_icon };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC/H,SAASC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,qBAAqB,QAAQ,uBAAuB;AACrF,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,oBAAoB,EAAEd,CAAC,IAAIe,WAAW,QAAQ,qBAAqB;AAC/F,SAASC,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAE9D,IAAIC,UAAU;AACd,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,IAAIC,GAAG,CAAC,CAAC;EAClB,CAAC,MACI;IACH,IAAI,CAACH,UAAU,EAAE;MACf,MAAMI,GAAG,GAAGF,MAAM;MAClBE,GAAG,CAACC,QAAQ,GAAGD,GAAG,CAACC,QAAQ,IAAI,CAAC,CAAC;MACjCL,UAAU,GAAGI,GAAG,CAACC,QAAQ,CAACC,GAAG,GAAGF,GAAG,CAACC,QAAQ,CAACC,GAAG,IAAI,IAAIH,GAAG,CAAC,CAAC;IAC/D;IACA,OAAOH,UAAU;EACnB;AACF,CAAC;AACD,MAAMO,MAAM,GAAIlB,CAAC,IAAK;EACpB,IAAImB,GAAG,GAAGC,MAAM,CAACpB,CAAC,CAACqB,GAAG,CAAC;EACvB,IAAIF,GAAG,EAAE;IACP,OAAOA,GAAG;EACZ;EACAA,GAAG,GAAGG,OAAO,CAACtB,CAAC,CAACuB,IAAI,EAAEvB,CAAC,CAACwB,IAAI,EAAExB,CAAC,CAACyB,IAAI,EAAEzB,CAAC,CAAC0B,GAAG,EAAE1B,CAAC,CAAC2B,EAAE,CAAC;EAClD,IAAIR,GAAG,EAAE;IACP,OAAOS,WAAW,CAACT,GAAG,EAAEnB,CAAC,CAAC;EAC5B;EACA,IAAIA,CAAC,CAACwB,IAAI,EAAE;IACVL,GAAG,GAAGC,MAAM,CAACpB,CAAC,CAACwB,IAAI,CAAC;IACpB,IAAIL,GAAG,EAAE;MACP,OAAOA,GAAG;IACZ;IACAA,GAAG,GAAGC,MAAM,CAACpB,CAAC,CAACwB,IAAI,CAACxB,CAAC,CAACyB,IAAI,CAAC,CAAC;IAC5B,IAAIN,GAAG,EAAE;MACP,OAAOA,GAAG;IACZ;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAMS,WAAW,GAAGA,CAACC,QAAQ,EAAEC,MAAM,KAAK;EACxC,MAAMX,GAAG,GAAGP,UAAU,CAAC,CAAC,CAACmB,GAAG,CAACF,QAAQ,CAAC;EACtC,IAAIV,GAAG,EAAE;IACP,OAAOA,GAAG;EACZ;EACA,IAAI;IACF,OAAO/B,YAAY,CAAC,OAAOyC,QAAQ,MAAM,CAAC;EAC5C,CAAC,CACD,OAAOG,CAAC,EAAE;IACR;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,OAAO,CAACC,IAAI,CAAC,sDAAsDL,QAAQ,0HAA0H,EAAEC,MAAM,CAAC;EAChN;AACF,CAAC;AACD,MAAMR,OAAO,GAAGA,CAACO,QAAQ,EAAEL,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,EAAE,KAAK;EACjD;EACAF,IAAI,GAAG,CAACA,IAAI,IAAIU,OAAO,CAACV,IAAI,CAAC,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI;EACvD;EACA;EACA,IAAIC,GAAG,IAAID,IAAI,KAAK,KAAK,EAAE;IACzBI,QAAQ,GAAGM,OAAO,CAACT,GAAG,CAAC;EACzB,CAAC,MACI,IAAIC,EAAE,IAAIF,IAAI,KAAK,IAAI,EAAE;IAC5BI,QAAQ,GAAGM,OAAO,CAACR,EAAE,CAAC;EACxB,CAAC,MACI;IACH,IAAI,CAACE,QAAQ,IAAIL,IAAI,IAAI,CAACY,KAAK,CAACZ,IAAI,CAAC,EAAE;MACrCK,QAAQ,GAAGL,IAAI;IACjB;IACA,IAAIa,KAAK,CAACR,QAAQ,CAAC,EAAE;MACnBA,QAAQ,GAAGM,OAAO,CAACN,QAAQ,CAAC;IAC9B;EACF;EACA,IAAI,CAACQ,KAAK,CAACR,QAAQ,CAAC,IAAIA,QAAQ,CAACS,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC9C,OAAO,IAAI;EACb;EACA;EACA,MAAMC,YAAY,GAAGV,QAAQ,CAACW,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EACzD,IAAID,YAAY,KAAK,EAAE,EAAE;IACvB,OAAO,IAAI;EACb;EACA,OAAOV,QAAQ;AACjB,CAAC;AACD,MAAMT,MAAM,GAAIC,GAAG,IAAK;EACtB,IAAIgB,KAAK,CAAChB,GAAG,CAAC,EAAE;IACdA,GAAG,GAAGA,GAAG,CAACiB,IAAI,CAAC,CAAC;IAChB,IAAIF,KAAK,CAACf,GAAG,CAAC,EAAE;MACd,OAAOA,GAAG;IACZ;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAMe,KAAK,GAAIK,GAAG,IAAKA,GAAG,CAACC,MAAM,GAAG,CAAC,IAAI,SAAS,CAACC,IAAI,CAACF,GAAG,CAAC;AAC5D,MAAMJ,KAAK,GAAIO,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ;AAC9C,MAAMT,OAAO,GAAIS,GAAG,IAAKA,GAAG,CAACC,WAAW,CAAC,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAACC,EAAE,EAAEC,UAAU,GAAG,EAAE,KAAK;EACjD,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1BD,UAAU,CAACE,OAAO,CAACC,IAAI,IAAI;IACzB,IAAIJ,EAAE,CAACK,YAAY,CAACD,IAAI,CAAC,EAAE;MACzB,MAAME,KAAK,GAAGN,EAAE,CAACO,YAAY,CAACH,IAAI,CAAC;MACnC,IAAIE,KAAK,KAAK,IAAI,EAAE;QAClBJ,eAAe,CAACE,IAAI,CAAC,GAAGJ,EAAE,CAACO,YAAY,CAACH,IAAI,CAAC;MAC/C;MACAJ,EAAE,CAACQ,eAAe,CAACJ,IAAI,CAAC;IAC1B;EACF,CAAC,CAAC;EACF,OAAOF,eAAe;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMO,KAAK,GAAIC,MAAM,IAAK;EACxB,IAAIA,MAAM,EAAE;IACV,IAAIA,MAAM,CAACC,GAAG,KAAK,EAAE,EAAE;MACrB,OAAOD,MAAM,CAACC,GAAG,CAACb,WAAW,CAAC,CAAC,KAAK,KAAK;IAC3C;EACF;EACA,OAAO,CAACc,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACD,GAAG,CAACb,WAAW,CAAC,CAAC,MAAM,KAAK;AACnG,CAAC;AAED,MAAMe,YAAY,GAAG,m0QAAm0Q;AACx1Q,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,43PAA43P;AACh5P,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjB5E,gBAAgB,CAAC,IAAI,EAAE4E,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAG3E,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC4E,OAAO,GAAG5E,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC6E,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAIC,EAAE,IAAK;MACvB,MAAM;QAAE7B;MAAG,CAAC,GAAG,IAAI;MACnB,IAAI,IAAI,CAAC8B,IAAI,KAAK,QAAQ,EAAE;QACxBxE,OAAO,CAAC,IAAI,CAACyE,IAAI,EAAEF,EAAE,EAAE,IAAI,CAACG,eAAe,EAAE,IAAI,CAACC,eAAe,CAAC;MACtE,CAAC,MACI,IAAIjF,YAAY,CAACgD,EAAE,CAAC,EAAE;QACvB,IAAI,CAACkC,UAAU,CAACL,EAAE,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACM,OAAO,GAAG,MAAM;MACjB,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAChB,OAAO,CAACe,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,MAAM,GAAGH,SAAS;IACvB,IAAI,CAACI,IAAI,GAAGJ,SAAS;IACrB,IAAI,CAACP,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAGM,SAAS;IAChC,IAAI,CAACK,QAAQ,GAAGL,SAAS;IACzB,IAAI,CAACR,IAAI,GAAGQ,SAAS;IACrB,IAAI,CAACM,GAAG,GAAGN,SAAS;IACpB,IAAI,CAACO,KAAK,GAAGP,SAAS;IACtB,IAAI,CAACQ,IAAI,GAAGR,SAAS;IACrB,IAAI,CAACS,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAGV,SAAS;IACvB,IAAI,CAACT,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACoB,IAAI,GAAGX,SAAS;EACzB;EACAY,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEV;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,IAAI,CAAChB,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACgB,QAAQ,GAAGA,QAAQ;IACzC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIW,kBAAkBA,CAAA,EAAG;IACjB,MAAM1B,MAAM,GAAI,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC2B,QAAQ,CAAC,CAAE;IAC9C,IAAI3B,MAAM,EAAE;MACR,MAAM;QAAED;MAAa,CAAC,GAAG,IAAI;MAC7B;AACZ;AACA;AACA;MACY,IAAIA,YAAY,KAAK,IAAI,IAAIC,MAAM,CAAC4B,QAAQ,CAAC7B,YAAY,CAAC,EAAE;QACxD;MACJ;MACA;MACA,MAAM8B,eAAe,GAAI,IAAI,CAAC9B,YAAY,GAAGb,QAAQ,CAAC4C,aAAa,CAAC,QAAQ,CAAE;MAC9ED,eAAe,CAACzB,IAAI,GAAG,IAAI,CAACA,IAAI;MAChCyB,eAAe,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;MACtC;MACAH,eAAe,CAACd,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACxCf,MAAM,CAACiC,WAAW,CAACJ,eAAe,CAAC;IACvC;EACJ;EACAK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACpC,SAAS,GAAG,CAAC,CAAC,IAAI,CAACxB,EAAE,CAAC6D,OAAO,CAAC,aAAa,CAAC;IACjD,IAAI,CAACtC,YAAY,GAAG,CAAC,CAAC,IAAI,CAACvB,EAAE,CAAC6D,OAAO,CAAC,iBAAiB,CAAC;IACxD,IAAI,CAACvC,MAAM,GAAG,CAAC,CAAC,IAAI,CAACtB,EAAE,CAAC6D,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC7D,EAAE,CAAC6D,OAAO,CAAC,kBAAkB,CAAC;IACpF,IAAI,CAAClC,mBAAmB,GAAGzE,qBAAqB,CAAC,IAAI,CAAC8C,EAAE,CAAC;EAC7D;EACA,IAAI8D,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC,IAAI,CAAC9D,EAAE,CAAC+D,aAAa,CAAC,oBAAoB,CAAC;EACxD;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,MAAMC,YAAY,GAAG,IAAI,CAACtB,IAAI,KAAKJ,SAAS,IAAI,IAAI,CAACI,IAAI,KAAK,OAAO;IACrE;IACA;IACA,IAAIsB,YAAY,IAAI,IAAI,CAACH,WAAW,IAAI,IAAI,CAACtC,SAAS,EAAE;MACpD,OAAO,WAAW;IACtB;IACA,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;EACI6B,QAAQA,CAAA,EAAG;IACP,MAAM;MAAEH;IAAK,CAAC,GAAG,IAAI;IACrB,IAAIA,IAAI,YAAYgB,eAAe,EAAE;MACjC,OAAOhB,IAAI;IACf;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B;MACA,MAAMlD,EAAE,GAAGY,QAAQ,CAACuD,cAAc,CAACjB,IAAI,CAAC;MACxC,IAAIlD,EAAE,EAAE;QACJ,IAAIA,EAAE,YAAYkE,eAAe,EAAE;UAC/B,OAAOlE,EAAE;QACb,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoB5C,eAAe,CAAC,yBAAyB8F,IAAI,2EAA2E,EAAE,IAAI,CAAClD,EAAE,CAAC;UAClI,OAAO,IAAI;QACf;MACJ,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgB5C,eAAe,CAAC,yBAAyB8F,IAAI,0FAA0F,EAAE,IAAI,CAAClD,EAAE,CAAC;QACjJ,OAAO,IAAI;MACf;IACJ;IACA,IAAIkD,IAAI,KAAKX,SAAS,EAAE;MACpB;AACZ;AACA;AACA;AACA;AACA;MACYnF,eAAe,CAAC,4GAA4G,EAAE,IAAI,CAAC4C,EAAE,CAAC;MACtI,OAAO,IAAI;IACf;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,EAAE,CAAC6D,OAAO,CAAC,MAAM,CAAC;EAClC;EACA3B,UAAUA,CAACL,EAAE,EAAE;IACX;IACA;IACA;IACA,IAAI,IAAI,CAACH,MAAM,IAAI,IAAI,CAACD,YAAY,EAAE;MAClCI,EAAE,CAACuC,cAAc,CAAC,CAAC;MACnB,IAAI,CAAC3C,YAAY,CAAC4C,KAAK,CAAC,CAAC;IAC7B;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM5F,IAAI,GAAGf,YAAY,CAAC,IAAI,CAAC;IAC/B,MAAM;MAAE6E,UAAU;MAAEV,IAAI;MAAEW,QAAQ;MAAEI,GAAG;MAAEI,MAAM;MAAEF,IAAI;MAAEhB,IAAI;MAAEO,KAAK;MAAEI,MAAM;MAAEoB,WAAW;MAAEhB,KAAK;MAAEE,MAAM;MAAErB;IAAqB,CAAC,GAAG,IAAI;IACrI,MAAM4C,SAAS,GAAGxB,IAAI,KAAKR,SAAS,IAAI,IAAI,CAACjB,MAAM,GAAG,OAAO,GAAGyB,IAAI;IACpE,MAAMyB,OAAO,GAAGzC,IAAI,KAAKQ,SAAS,GAAG,QAAQ,GAAG,GAAG;IACnD,MAAMkC,KAAK,GAAGD,OAAO,KAAK,QAAQ,GAC5B;MAAE1C;IAAK,CAAC,GACR;MACEc,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBb,IAAI;MACJc,GAAG;MACHI;IACJ,CAAC;IACL,IAAIN,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAIA,IAAI,IAAI,IAAI,EAAE;MACdA,IAAI,GAAG,IAAI,CAACnB,SAAS,IAAI,IAAI,CAACD,YAAY,GAAG,OAAO,GAAG,OAAO;IAClE;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ;MACIO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACsB,kBAAkB,CAAC,CAAC;IAClD;IACA,OAAQ1G,CAAC,CAACE,IAAI,EAAE;MAAE8H,GAAG,EAAE,0CAA0C;MAAEC,OAAO,EAAE,IAAI,CAAC/C,WAAW;MAAE,eAAe,EAAEa,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEmC,KAAK,EAAEpH,oBAAoB,CAAC8E,KAAK,EAAE;QACpK,CAAC5D,IAAI,GAAG,IAAI;QACZ,CAAC8D,UAAU,GAAG,IAAI;QAClB,CAAC,GAAGA,UAAU,IAAIE,MAAM,EAAE,GAAGA,MAAM,KAAKH,SAAS;QACjD,CAAC,GAAGC,UAAU,IAAI+B,SAAS,EAAE,GAAGA,SAAS,KAAKhC,SAAS;QACvD,CAAC,GAAGC,UAAU,IAAIM,KAAK,EAAE,GAAGA,KAAK,KAAKP,SAAS;QAC/C,CAAC,GAAGC,UAAU,IAAIG,IAAI,EAAE,GAAG,IAAI;QAC/B,CAAC,GAAGH,UAAU,SAAS,GAAGQ,MAAM;QAChC,YAAY,EAAEvF,WAAW,CAAC,aAAa,EAAE,IAAI,CAACuC,EAAE,CAAC;QACjD,kBAAkB,EAAEvC,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAACuC,EAAE,CAAC;QAC9D,YAAY,EAAEvC,WAAW,CAAC,aAAa,EAAE,IAAI,CAACuC,EAAE,CAAC;QACjD,sBAAsB,EAAE8D,WAAW;QACnC,iBAAiB,EAAErB,QAAQ;QAC3B,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE;MACrB,CAAC;IAAE,CAAC,EAAE/F,CAAC,CAAC8H,OAAO,EAAEK,MAAM,CAACC,MAAM,CAAC;MAAEJ,GAAG,EAAE;IAA2C,CAAC,EAAED,KAAK,EAAE;MAAEG,KAAK,EAAE,eAAe;MAAEG,IAAI,EAAE,QAAQ;MAAEtC,QAAQ,EAAEA,QAAQ;MAAEN,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,EAAEV,mBAAmB,CAAC,EAAEjF,CAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAe,CAAC,EAAElI,CAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAElG,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE9B,CAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAElG,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE9B,CAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAEhI,CAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAElG,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAEE,IAAI,KAAK,IAAI,IAAIhC,CAAC,CAAC,mBAAmB,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAE5C,IAAI,EAAE,IAAI,CAACkC;IAAW,CAAC,CAAC,CAAC,CAAC;EAChuB;EACA,IAAIhE,EAAEA,CAAA,EAAG;IAAE,OAAOlD,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACD/D,MAAM,CAACwC,KAAK,GAAG;EACX9E,GAAG,EAAEmC,kBAAkB;EACvBlC,EAAE,EAAEoC;AACR,CAAC;AAED,MAAMiE,eAAe,GAAIC,UAAU,IAAK;EACtC,MAAMC,GAAG,GAAGvE,QAAQ,CAAC4C,aAAa,CAAC,KAAK,CAAC;EACzC2B,GAAG,CAACC,SAAS,GAAGF,UAAU;EAC1B;EACA,KAAK,IAAIjI,CAAC,GAAGkI,GAAG,CAACE,UAAU,CAAC1F,MAAM,GAAG,CAAC,EAAE1C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnD,IAAIkI,GAAG,CAACE,UAAU,CAACpI,CAAC,CAAC,CAACqI,QAAQ,CAACxF,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;MACtDqF,GAAG,CAACI,WAAW,CAACJ,GAAG,CAACE,UAAU,CAACpI,CAAC,CAAC,CAAC;IACpC;EACF;EACA;EACA,MAAMuI,MAAM,GAAGL,GAAG,CAACM,iBAAiB;EACpC,IAAID,MAAM,IAAIA,MAAM,CAACF,QAAQ,CAACxF,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;IACrD,MAAM4F,QAAQ,GAAGF,MAAM,CAACjF,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;IACnDiF,MAAM,CAACG,YAAY,CAAC,OAAO,EAAE,CAACD,QAAQ,GAAG,aAAa,EAAEnG,IAAI,CAAC,CAAC,CAAC;IAC/D;IACA;IACA;IACA,IAAIqG,OAAO,CAACJ,MAAM,CAAC,EAAE;MACnB,OAAOL,GAAG,CAACC,SAAS;IACtB;EACF;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMQ,OAAO,GAAIC,GAAG,IAAK;EACvB,IAAIA,GAAG,CAACC,QAAQ,KAAK,CAAC,EAAE;IACtB,IAAID,GAAG,CAACP,QAAQ,CAACxF,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC3C,OAAO,KAAK;IACd;IACA,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,GAAG,CAAC5F,UAAU,CAACN,MAAM,EAAE1C,CAAC,EAAE,EAAE;MAC9C,MAAMuB,IAAI,GAAGqH,GAAG,CAAC5F,UAAU,CAAChD,CAAC,CAAC,CAACuB,IAAI;MACnC,IAAIc,KAAK,CAACd,IAAI,CAAC,IAAIA,IAAI,CAACsB,WAAW,CAAC,CAAC,CAACiG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACzD,OAAO,KAAK;MACd;IACF;IACA,KAAK,IAAI9I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,GAAG,CAACR,UAAU,CAAC1F,MAAM,EAAE1C,CAAC,EAAE,EAAE;MAC9C,IAAI,CAAC2I,OAAO,CAACC,GAAG,CAACR,UAAU,CAACpI,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;IACF;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAM+I,YAAY,GAAI5H,GAAG,IAAKA,GAAG,CAAC6H,UAAU,CAAC,oBAAoB,CAAC;AAClE,MAAMC,gBAAgB,GAAI9H,GAAG,IAAKA,GAAG,CAAC2H,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAE9D,MAAMI,cAAc,GAAG,IAAIpI,GAAG,CAAC,CAAC;AAChC,MAAMqI,QAAQ,GAAG,IAAIrI,GAAG,CAAC,CAAC;AAC1B,IAAIsI,MAAM;AACV,MAAMC,aAAa,GAAGA,CAAClI,GAAG,EAAEmI,QAAQ,KAAK;EACvC;EACA,IAAIC,GAAG,GAAGJ,QAAQ,CAACpH,GAAG,CAACZ,GAAG,CAAC;EAC3B,IAAI,CAACoI,GAAG,EAAE;IACR,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAI,OAAO7F,QAAQ,KAAK,WAAW,EAAE;MACnE;AACN;AACA;AACA;MACM,IAAIoF,YAAY,CAAC5H,GAAG,CAAC,IAAI8H,gBAAgB,CAAC9H,GAAG,CAAC,EAAE;QAC9C,IAAI,CAACiI,MAAM,EAAE;UACX;AACV;AACA;AACA;UACUA,MAAM,GAAG,IAAIK,SAAS,CAAC,CAAC;QAC1B;QACA,MAAMC,GAAG,GAAGN,MAAM,CAACO,eAAe,CAACxI,GAAG,EAAE,WAAW,CAAC;QACpD,MAAMyI,GAAG,GAAGF,GAAG,CAAC5C,aAAa,CAAC,KAAK,CAAC;QACpC,IAAI8C,GAAG,EAAE;UACPV,cAAc,CAACW,GAAG,CAAC1I,GAAG,EAAEyI,GAAG,CAACE,SAAS,CAAC;QACxC;QACA,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;MAC1B,CAAC,MACI;QACH;QACAT,GAAG,GAAGC,KAAK,CAACrI,GAAG,CAAC,CAAC8I,IAAI,CAAEC,GAAG,IAAK;UAC7B,IAAIA,GAAG,CAACC,EAAE,EAAE;YACV,OAAOD,GAAG,CAACE,IAAI,CAAC,CAAC,CAACH,IAAI,CAAEhC,UAAU,IAAK;cACrC,IAAIA,UAAU,IAAIqB,QAAQ,KAAK,KAAK,EAAE;gBACpCrB,UAAU,GAAGD,eAAe,CAACC,UAAU,CAAC;cAC1C;cACAiB,cAAc,CAACW,GAAG,CAAC1I,GAAG,EAAE8G,UAAU,IAAI,EAAE,CAAC;YAC3C,CAAC,CAAC;UACJ;UACAiB,cAAc,CAACW,GAAG,CAAC1I,GAAG,EAAE,EAAE,CAAC;QAC7B,CAAC,CAAC;QACF;QACAgI,QAAQ,CAACU,GAAG,CAAC1I,GAAG,EAAEoI,GAAG,CAAC;MACxB;IACF,CAAC,MACI;MACH;MACAL,cAAc,CAACW,GAAG,CAAC1I,GAAG,EAAE,EAAE,CAAC;MAC3B,OAAO4I,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;EACF;EACA,OAAOT,GAAG;AACZ,CAAC;AAED,MAAMc,OAAO,GAAG,wwDAAwwD;AACxxD,MAAMC,aAAa,GAAGD,OAAO;AAE7B,MAAME,IAAI,GAAG,MAAM;EACftG,WAAWA,CAACC,OAAO,EAAE;IACjB5E,gBAAgB,CAAC,IAAI,EAAE4E,OAAO,CAAC;IAC/B,IAAI,CAACrC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC6C,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAAC8F,WAAW,GAAG,KAAK;IACxB,IAAI,CAACvC,UAAU,GAAG3C,SAAS;IAC3B,IAAI,CAACmF,SAAS,GAAG,KAAK;IACtB,IAAI,CAAChJ,IAAI,GAAGiJ,UAAU,CAAC,CAAC;IACxB,IAAI,CAACrF,KAAK,GAAGC,SAAS;IACtB,IAAI,CAAC5D,GAAG,GAAG4D,SAAS;IACpB,IAAI,CAAC3D,EAAE,GAAG2D,SAAS;IACnB,IAAI,CAACqF,OAAO,GAAGrF,SAAS;IACxB,IAAI,CAAC/D,IAAI,GAAG+D,SAAS;IACrB,IAAI,CAACjE,GAAG,GAAGiE,SAAS;IACpB,IAAI,CAAC9D,IAAI,GAAG8D,SAAS;IACrB,IAAI,CAACQ,IAAI,GAAGR,SAAS;IACrB,IAAI,CAACsF,IAAI,GAAG,KAAK;IACjB,IAAI,CAACtB,QAAQ,GAAG,IAAI;EACxB;EACA3C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACjC,mBAAmB,GAAG5B,iBAAiB,CAAC,IAAI,CAACC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACzE;EACA8H,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA;IACA,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC/H,EAAE,EAAE,MAAM,EAAE,MAAM;MACzC,IAAI,CAAC0H,SAAS,GAAG,IAAI;MACrB,IAAI,CAACM,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACR,WAAW,EAAE;MACnB,IAAI,CAACO,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACC,UAAU,CAAC,CAAC;MACpB,IAAI,CAACD,EAAE,GAAG5F,SAAS;IACvB;EACJ;EACAwF,gBAAgBA,CAAC/H,EAAE,EAAEqI,UAAU,EAAEC,EAAE,EAAE;IACjC,IAAI,IAAI,CAACT,IAAI,IAAI,OAAO/J,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACyK,oBAAoB,EAAE;MAC3E,MAAMJ,EAAE,GAAI,IAAI,CAACA,EAAE,GAAG,IAAIrK,MAAM,CAACyK,oBAAoB,CAAEC,IAAI,IAAK;QAC5D,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,cAAc,EAAE;UACxBN,EAAE,CAACC,UAAU,CAAC,CAAC;UACf,IAAI,CAACD,EAAE,GAAG5F,SAAS;UACnB+F,EAAE,CAAC,CAAC;QACR;MACJ,CAAC,EAAE;QAAED;MAAW,CAAC,CAAE;MACnBF,EAAE,CAACO,OAAO,CAAC1I,EAAE,CAAC;IAClB,CAAC,MACI;MACD;MACA;MACAsI,EAAE,CAAC,CAAC;IACR;EACJ;EACAN,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACN,SAAS,EAAE;MAChB,MAAMtJ,GAAG,GAAGD,MAAM,CAAC,IAAI,CAAC;MACxB,IAAIC,GAAG,EAAE;QACL,IAAI+H,cAAc,CAACwC,GAAG,CAACvK,GAAG,CAAC,EAAE;UACzB;UACA,IAAI,CAAC8G,UAAU,GAAGiB,cAAc,CAACnH,GAAG,CAACZ,GAAG,CAAC;QAC7C,CAAC,MACI;UACD;UACAkI,aAAa,CAAClI,GAAG,EAAE,IAAI,CAACmI,QAAQ,CAAC,CAACW,IAAI,CAAC,MAAO,IAAI,CAAChC,UAAU,GAAGiB,cAAc,CAACnH,GAAG,CAACZ,GAAG,CAAE,CAAC;QAC7F;QACA,IAAI,CAACqJ,WAAW,GAAG,IAAI;MAC3B;IACJ;IACA,IAAI,CAAC3I,QAAQ,GAAGP,OAAO,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,EAAE,CAAC;EAC/E;EACA0F,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEsD,OAAO;MAAE9I,QAAQ;MAAE6C,mBAAmB;MAAE3B;IAAG,CAAC,GAAG,IAAI;IAC3D,MAAMtB,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,IAAI;IAC9B;IACA,MAAMkK,cAAc,GAAG9J,QAAQ,GACzB,CAACA,QAAQ,CAAC+J,QAAQ,CAAC,OAAO,CAAC,IAAI/J,QAAQ,CAAC+J,QAAQ,CAAC,SAAS,CAAC,KAAKjB,OAAO,KAAK,KAAK,GACjF,KAAK;IACX;IACA,MAAMkB,iBAAiB,GAAGlB,OAAO,IAAIgB,cAAc;IACnD,OAAQlM,CAAC,CAACE,IAAI,EAAEiI,MAAM,CAACC,MAAM,CAAC;MAAEiE,IAAI,EAAE,KAAK;MAAEnE,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE,CAACpG,IAAI,GAAG;MAAK,CAAC,EAAEsK,kBAAkB,CAAC,IAAI,CAAC1G,KAAK,CAAC,CAAC,EAAE;QAAE,CAAC,QAAQ,IAAI,CAACS,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAACA,IAAI;QAAE,UAAU,EAAE+F,iBAAiB;QAAE,UAAU,EAAEA,iBAAiB,IAAIrI,KAAK,CAACT,EAAE;MAAE,CAAC;IAAE,CAAC,EAAE2B,mBAAmB,CAAC,EAAE,IAAI,CAACuD,UAAU,GAAIxI,CAAC,CAAC,KAAK,EAAE;MAAEkI,KAAK,EAAE,YAAY;MAAEQ,SAAS,EAAE,IAAI,CAACF;IAAW,CAAC,CAAC,GAAKxI,CAAC,CAAC,KAAK,EAAE;MAAEkI,KAAK,EAAE;IAAa,CAAC,CAAE,CAAC;EAC3Y;EACA,WAAWqE,UAAUA,CAAA,EAAG;IAAE,OAAO,CAAC,KAAK,CAAC;EAAE;EAC1C,IAAIjJ,EAAEA,CAAA,EAAG;IAAE,OAAOlD,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,UAAU,CAAC;MACpB,KAAK,EAAE,CAAC,UAAU,CAAC;MACnB,MAAM,EAAE,CAAC,UAAU,CAAC;MACpB,KAAK,EAAE,CAAC,UAAU,CAAC;MACnB,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE;AACP,CAAC;AACD,MAAM2C,UAAU,GAAGA,CAAA,KAAO,OAAO/G,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACsI,eAAe,CAAC3I,YAAY,CAAC,MAAM,CAAC,IAAK,IAAI;AACnH,MAAMyI,kBAAkB,GAAI1G,KAAK,IAAK;EAClC,OAAOA,KAAK,GACN;IACE,WAAW,EAAE,IAAI;IACjB,CAAC,aAAaA,KAAK,EAAE,GAAG;EAC5B,CAAC,GACC,IAAI;AACd,CAAC;AACDkF,IAAI,CAAC/D,KAAK,GAAG8D,aAAa;AAE1B,SAAStG,MAAM,IAAIkI,UAAU,EAAE3B,IAAI,IAAI4B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}