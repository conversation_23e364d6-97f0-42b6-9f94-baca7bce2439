{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5];\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading amazing fashion...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_2_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_17_Template_div_click_0_listener() {\n      const category_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryClick(category_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", category_r4.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(category_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r4.name);\n  }\n}\nfunction HomeComponent_div_2_section_25_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", product_r6.discount, \"% OFF\");\n  }\n}\nfunction HomeComponent_div_2_section_25_div_7_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction HomeComponent_div_2_section_25_div_7_div_13_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n  }\n}\nfunction HomeComponent_div_2_section_25_div_7_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, HomeComponent_div_2_section_25_div_7_div_13_i_2_Template, 1, 2, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction HomeComponent_div_2_section_25_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_section_25_div_7_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"img\", 24);\n    i0.ɵɵtemplate(3, HomeComponent_div_2_section_25_div_7_div_3_Template, 3, 1, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 26)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 27);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 28)(10, \"span\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, HomeComponent_div_2_section_25_div_7_span_12_Template, 2, 1, \"span\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, HomeComponent_div_2_section_25_div_7_div_13_Template, 5, 3, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r6.images == null ? null : product_r6.images[0] == null ? null : product_r6.images[0].url) || (product_r6.images == null ? null : product_r6.images[0]), i0.ɵɵsanitizeUrl)(\"alt\", product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.discount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.rating);\n  }\n}\nfunction HomeComponent_div_2_section_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 10)(1, \"div\", 11)(2, \"h3\");\n    i0.ɵɵtext(3, \"Featured Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 19);\n    i0.ɵɵtext(5, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵtemplate(7, HomeComponent_div_2_section_25_div_7_Template, 14, 8, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredProducts);\n  }\n}\nfunction HomeComponent_div_2_section_26_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_section_26_div_7_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"img\", 24);\n    i0.ɵɵelementStart(3, \"div\", 39);\n    i0.ɵɵelement(4, \"i\", 40);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Trending\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 26)(8, \"h4\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 27);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 28)(13, \"span\", 29);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 41)(16, \"span\");\n    i0.ɵɵelement(17, \"i\", 42);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵelement(20, \"i\", 43);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r9.images == null ? null : product_r9.images[0] == null ? null : product_r9.images[0].url) || (product_r9.images == null ? null : product_r9.images[0]), i0.ɵɵsanitizeUrl)(\"alt\", product_r9.name);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r9.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r9.price));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatNumber((product_r9.analytics == null ? null : product_r9.analytics.views) || 0), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatNumber((product_r9.analytics == null ? null : product_r9.analytics.likes) || 0), \"\");\n  }\n}\nfunction HomeComponent_div_2_section_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 10)(1, \"div\", 11)(2, \"h3\");\n    i0.ɵɵtext(3, \"Trending Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 38);\n    i0.ɵɵtext(5, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵtemplate(7, HomeComponent_div_2_section_26_div_7_Template, 22, 7, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts);\n  }\n}\nfunction HomeComponent_div_2_section_27_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_section_27_div_7_Template_div_click_0_listener() {\n      const product_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r11));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"img\", 24);\n    i0.ɵɵelementStart(3, \"div\", 45)(4, \"span\");\n    i0.ɵɵtext(5, \"New\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"h4\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 27);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 28)(12, \"span\", 29);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r11.images == null ? null : product_r11.images[0] == null ? null : product_r11.images[0].url) || (product_r11.images == null ? null : product_r11.images[0]), i0.ɵɵsanitizeUrl)(\"alt\", product_r11.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(product_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r11.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r11.price));\n  }\n}\nfunction HomeComponent_div_2_section_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 10)(1, \"div\", 11)(2, \"h3\");\n    i0.ɵɵtext(3, \"New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 44);\n    i0.ɵɵtext(5, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵtemplate(7, HomeComponent_div_2_section_27_div_7_Template, 14, 5, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals);\n  }\n}\nfunction HomeComponent_div_2_section_28_div_7_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 55);\n  }\n}\nfunction HomeComponent_div_2_section_28_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵelement(2, \"img\", 24);\n    i0.ɵɵelementStart(3, \"div\", 51);\n    i0.ɵɵtemplate(4, HomeComponent_div_2_section_28_div_7_i_4_Template, 1, 0, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 53)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 54)(9, \"span\");\n    i0.ɵɵelement(10, \"i\", 42);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵelement(13, \"i\", 43);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const post_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", post_r12.mediaUrl || post_r12.featuredImage, i0.ɵɵsanitizeUrl)(\"alt\", post_r12.title || post_r12.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", post_r12.mediaType === \"video\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r12.title || (post_r12.content == null ? null : post_r12.content.substring(0, 50)) + \"...\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatNumber((post_r12.analytics == null ? null : post_r12.analytics.views) || post_r12.views || 0), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatNumber((post_r12.analytics == null ? null : post_r12.analytics.likes) || post_r12.likes || 0), \"\");\n  }\n}\nfunction HomeComponent_div_2_section_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 10)(1, \"div\", 11)(2, \"h3\");\n    i0.ɵɵtext(3, \"Fashion Inspiration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 46);\n    i0.ɵɵtext(5, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 47);\n    i0.ɵɵtemplate(7, HomeComponent_div_2_section_28_div_7_Template, 15, 6, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingPosts.slice(0, 4));\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"section\", 6)(2, \"div\", 7)(3, \"div\", 8)(4, \"h2\");\n    i0.ɵɵtext(5, \"Summer Collection 2024\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Discover the hottest trends\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 9);\n    i0.ɵɵtext(9, \"Shop Now\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"section\", 10)(11, \"div\", 11)(12, \"h3\");\n    i0.ɵɵtext(13, \"Shop by Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewAllCategories());\n    });\n    i0.ɵɵtext(15, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 13);\n    i0.ɵɵtemplate(17, HomeComponent_div_2_div_17_Template, 5, 5, \"div\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"section\", 10)(19, \"div\", 11)(20, \"h3\");\n    i0.ɵɵtext(21, \"Latest Stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 15);\n    i0.ɵɵtext(23, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(24, \"app-view-add-stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, HomeComponent_div_2_section_25_Template, 8, 1, \"section\", 16)(26, HomeComponent_div_2_section_26_Template, 8, 1, \"section\", 16)(27, HomeComponent_div_2_section_27_Template, 8, 1, \"section\", 16)(28, HomeComponent_div_2_section_28_Template, 8, 1, \"section\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.featuredProducts.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendingProducts.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.newArrivals.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendingPosts.length > 0);\n  }\n}\nexport class HomeComponent {\n  constructor(router, productService, authService) {\n    this.router = router;\n    this.productService = productService;\n    this.authService = authService;\n    this.featuredProducts = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.trendingPosts = [];\n    this.categories = [];\n    this.isLoading = true;\n    this.isAuthenticated = false;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n  }\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n    });\n  }\n  loadHomeData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        // Load all data in parallel\n        const [featured, trending, arrivals] = yield Promise.all([_this.productService.getFeaturedProducts().toPromise(), _this.productService.getTrendingProducts().toPromise(), _this.productService.getNewArrivals().toPromise()]);\n        _this.featuredProducts = featured?.slice(0, 8) || _this.getFallbackProducts();\n        _this.trendingProducts = trending?.slice(0, 8) || _this.getFallbackProducts();\n        _this.newArrivals = arrivals?.slice(0, 8) || _this.getFallbackProducts();\n        // Load categories\n        _this.loadCategories();\n        // Load trending posts (mock data for now)\n        _this.loadTrendingPosts();\n      } catch (error) {\n        console.error('Error loading home data:', error);\n        _this.loadFallbackData();\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      icon: 'fas fa-female',\n      color: 'linear-gradient(45deg, #f093fb, #f5576c)'\n    }, {\n      name: 'Men',\n      icon: 'fas fa-male',\n      color: 'linear-gradient(45deg, #667eea, #764ba2)'\n    }, {\n      name: 'Kids',\n      icon: 'fas fa-child',\n      color: 'linear-gradient(45deg, #4facfe, #00f2fe)'\n    }, {\n      name: 'Accessories',\n      icon: 'fas fa-gem',\n      color: 'linear-gradient(45deg, #43e97b, #38f9d7)'\n    }, {\n      name: 'Shoes',\n      icon: 'fas fa-shoe-prints',\n      color: 'linear-gradient(45deg, #fa709a, #fee140)'\n    }, {\n      name: 'Bags',\n      icon: 'fas fa-shopping-bag',\n      color: 'linear-gradient(45deg, #a8edea, #fed6e3)'\n    }];\n  }\n  loadTrendingPosts() {\n    this.trendingPosts = [{\n      title: 'Summer Fashion Trends 2024',\n      mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n      mediaType: 'image',\n      analytics: {\n        views: 15420,\n        likes: 892\n      }\n    }, {\n      title: 'Street Style Inspiration',\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400',\n      mediaType: 'image',\n      analytics: {\n        views: 12890,\n        likes: 1205\n      }\n    }];\n  }\n  getFallbackProducts() {\n    return [{\n      _id: '1',\n      name: 'Vintage Denim Jacket',\n      brand: 'Urban Threads',\n      price: 89.99,\n      originalPrice: 129.99,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400'\n      }],\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      analytics: {\n        views: 15420,\n        likes: 892\n      }\n    }, {\n      _id: '2',\n      name: 'Silk Slip Dress',\n      brand: 'Ethereal',\n      price: 159.99,\n      originalPrice: 199.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400'\n      }],\n      rating: {\n        average: 4.5,\n        count: 89\n      },\n      analytics: {\n        views: 12890,\n        likes: 1205\n      }\n    }];\n  }\n  loadFallbackData() {\n    this.featuredProducts = this.getFallbackProducts();\n    this.trendingProducts = this.getFallbackProducts();\n    this.newArrivals = this.getFallbackProducts();\n  }\n  // Navigation methods\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        category: category.name.toLowerCase()\n      }\n    });\n  }\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n  // Utility methods\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"home-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"main-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"main-content\"], [1, \"hero-section\"], [1, \"hero-slides\"], [1, \"slide-content\", 2, \"background\", \"linear-gradient(45deg, #667eea 0%, #764ba2 100%)\"], [1, \"cta-button\"], [1, \"section\"], [1, \"section-header\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"categories-grid\"], [\"class\", \"category-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/stories\", 1, \"view-all-btn\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"category-item\", 3, \"click\"], [1, \"category-icon\"], [\"routerLink\", \"/shop\", 1, \"view-all-btn\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [\"class\", \"product-badge\", 4, \"ngIf\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-badge\"], [1, \"original-price\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-star\"], [\"routerLink\", \"/shop?filter=trending\", 1, \"view-all-btn\"], [1, \"trending-badge\"], [1, \"fas\", \"fa-fire\"], [1, \"product-stats\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-heart\"], [\"routerLink\", \"/shop?filter=new\", 1, \"view-all-btn\"], [1, \"new-badge\"], [\"routerLink\", \"/feed\", 1, \"view-all-btn\"], [1, \"posts-grid\"], [\"class\", \"post-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"post-item\"], [1, \"post-image\"], [1, \"post-overlay\"], [\"class\", \"fas fa-play\", 4, \"ngIf\"], [1, \"post-content\"], [1, \"post-stats\"], [1, \"fas\", \"fa-play\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 4, 0, \"div\", 1)(2, HomeComponent_div_2_Template, 29, 5, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, RouterModule, i1.RouterLink, ViewAddStoriesComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 60px);\\n  background: #f8f9fa;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  gap: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid #e3e3e3;\\n  border-top: 3px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 14px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.main-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-slides[_ngcontent-%COMP%] {\\n  height: 300px;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-slides[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  text-align: center;\\n  padding: 40px;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-slides[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 32px;\\n  font-weight: 700;\\n  margin-bottom: 12px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-slides[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  font-size: 18px;\\n  margin-bottom: 24px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-slides[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .cta-button[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 2px solid white;\\n  color: white;\\n  padding: 12px 32px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-slides[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .cta-button[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  color: #667eea;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n.section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #2d3748;\\n  margin: 0;\\n}\\n.section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: 1px solid #667eea;\\n  color: #667eea;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n  transform: translateY(-1px);\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 24px;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 12px;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  text-align: center;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 24px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   .product-badge[_ngcontent-%COMP%], .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%], .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: white;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   .product-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f5576c, #f093fb);\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ffa500);\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  margin: 0 0 4px 0;\\n  line-height: 1.3;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #718096;\\n  margin: 0 0 8px 0;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #667eea;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #a0aec0;\\n  text-decoration: line-through;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #e2e8f0;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%] {\\n  color: #f6ad55;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #718096;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 12px;\\n  color: #718096;\\n}\\n.products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ViewAddStoriesComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "HomeComponent_div_2_div_17_Template_div_click_0_listener", "category_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onCategoryClick", "ɵɵadvance", "ɵɵstyleProp", "color", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "product_r6", "discount", "formatPrice", "originalPrice", "ɵɵclassProp", "star_r7", "rating", "average", "ɵɵtemplate", "HomeComponent_div_2_section_25_div_7_div_13_i_2_Template", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "count", "HomeComponent_div_2_section_25_div_7_Template_div_click_0_listener", "_r5", "onProductClick", "HomeComponent_div_2_section_25_div_7_div_3_Template", "HomeComponent_div_2_section_25_div_7_span_12_Template", "HomeComponent_div_2_section_25_div_7_div_13_Template", "images", "url", "ɵɵsanitizeUrl", "brand", "price", "HomeComponent_div_2_section_25_div_7_Template", "featuredProducts", "HomeComponent_div_2_section_26_div_7_Template_div_click_0_listener", "product_r9", "_r8", "formatNumber", "analytics", "views", "likes", "HomeComponent_div_2_section_26_div_7_Template", "trendingProducts", "HomeComponent_div_2_section_27_div_7_Template_div_click_0_listener", "product_r11", "_r10", "HomeComponent_div_2_section_27_div_7_Template", "newArrivals", "HomeComponent_div_2_section_28_div_7_i_4_Template", "post_r12", "mediaUrl", "featuredImage", "title", "content", "mediaType", "substring", "HomeComponent_div_2_section_28_div_7_Template", "trendingPosts", "slice", "HomeComponent_div_2_Template_button_click_14_listener", "_r1", "viewAllCategories", "HomeComponent_div_2_div_17_Template", "HomeComponent_div_2_section_25_Template", "HomeComponent_div_2_section_26_Template", "HomeComponent_div_2_section_27_Template", "HomeComponent_div_2_section_28_Template", "categories", "length", "HomeComponent", "constructor", "router", "productService", "authService", "isLoading", "isAuthenticated", "ngOnInit", "loadHomeData", "checkAuthStatus", "currentUser$", "subscribe", "user", "_this", "_asyncToGenerator", "featured", "trending", "arrivals", "Promise", "all", "getFeaturedProducts", "to<PERSON>romise", "getTrendingProducts", "getNewArrivals", "getFallbackProducts", "loadCategories", "loadTrendingPosts", "error", "console", "loadFallbackData", "_id", "product", "navigate", "id", "category", "queryParams", "toLowerCase", "Intl", "NumberFormat", "style", "currency", "format", "num", "toFixed", "toString", "ɵɵdirectiveInject", "i1", "Router", "i2", "ProductService", "i3", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, RouterModule, ViewAddStoriesComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  featuredProducts: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  trendingPosts: any[] = [];\n  categories: any[] = [];\n  isLoading = true;\n  isAuthenticated = false;\n\n  constructor(\n    private router: Router,\n    private productService: ProductService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n  }\n\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n    });\n  }\n\n  async loadHomeData() {\n    try {\n      this.isLoading = true;\n\n      // Load all data in parallel\n      const [featured, trending, arrivals] = await Promise.all([\n        this.productService.getFeaturedProducts().toPromise(),\n        this.productService.getTrendingProducts().toPromise(),\n        this.productService.getNewArrivals().toPromise()\n      ]);\n\n      this.featuredProducts = featured?.slice(0, 8) || this.getFallbackProducts();\n      this.trendingProducts = trending?.slice(0, 8) || this.getFallbackProducts();\n      this.newArrivals = arrivals?.slice(0, 8) || this.getFallbackProducts();\n\n      // Load categories\n      this.loadCategories();\n\n      // Load trending posts (mock data for now)\n      this.loadTrendingPosts();\n\n    } catch (error) {\n      console.error('Error loading home data:', error);\n      this.loadFallbackData();\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  loadCategories() {\n    this.categories = [\n      { name: 'Women', icon: 'fas fa-female', color: 'linear-gradient(45deg, #f093fb, #f5576c)' },\n      { name: 'Men', icon: 'fas fa-male', color: 'linear-gradient(45deg, #667eea, #764ba2)' },\n      { name: 'Kids', icon: 'fas fa-child', color: 'linear-gradient(45deg, #4facfe, #00f2fe)' },\n      { name: 'Accessories', icon: 'fas fa-gem', color: 'linear-gradient(45deg, #43e97b, #38f9d7)' },\n      { name: 'Shoes', icon: 'fas fa-shoe-prints', color: 'linear-gradient(45deg, #fa709a, #fee140)' },\n      { name: 'Bags', icon: 'fas fa-shopping-bag', color: 'linear-gradient(45deg, #a8edea, #fed6e3)' }\n    ];\n  }\n\n  loadTrendingPosts() {\n    this.trendingPosts = [\n      {\n        title: 'Summer Fashion Trends 2024',\n        mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n        mediaType: 'image',\n        analytics: { views: 15420, likes: 892 }\n      },\n      {\n        title: 'Street Style Inspiration',\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400',\n        mediaType: 'image',\n        analytics: { views: 12890, likes: 1205 }\n      }\n    ];\n  }\n\n  getFallbackProducts() {\n    return [\n      {\n        _id: '1',\n        name: 'Vintage Denim Jacket',\n        brand: 'Urban Threads',\n        price: 89.99,\n        originalPrice: 129.99,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400' }],\n        rating: { average: 4.2, count: 156 },\n        analytics: { views: 15420, likes: 892 }\n      },\n      {\n        _id: '2',\n        name: 'Silk Slip Dress',\n        brand: 'Ethereal',\n        price: 159.99,\n        originalPrice: 199.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400' }],\n        rating: { average: 4.5, count: 89 },\n        analytics: { views: 12890, likes: 1205 }\n      }\n    ];\n  }\n\n  loadFallbackData() {\n    this.featuredProducts = this.getFallbackProducts();\n    this.trendingProducts = this.getFallbackProducts();\n    this.newArrivals = this.getFallbackProducts();\n  }\n\n  // Navigation methods\n  onProductClick(product: any) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n\n  onCategoryClick(category: any) {\n    this.router.navigate(['/shop'], {\n      queryParams: { category: category.name.toLowerCase() }\n    });\n  }\n\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n\n  // Utility methods\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n}\n", "<div class=\"home-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Loading amazing fashion...</p>\n  </div>\n\n  <!-- Main Content -->\n  <div *ngIf=\"!isLoading\" class=\"main-content\">\n    <!-- Hero Banner -->\n    <section class=\"hero-section\">\n      <div class=\"hero-slides\">\n        <div class=\"slide-content\" style=\"background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);\">\n          <h2>Summer Collection 2024</h2>\n          <p>Discover the hottest trends</p>\n          <button class=\"cta-button\">Shop Now</button>\n        </div>\n      </div>\n    </section>\n\n    <!-- Categories -->\n    <section class=\"section\">\n      <div class=\"section-header\">\n        <h3>Shop by Category</h3>\n        <button class=\"view-all-btn\" (click)=\"viewAllCategories()\">View All</button>\n      </div>\n      <div class=\"categories-grid\">\n        <div\n          *ngFor=\"let category of categories\"\n          class=\"category-item\"\n          (click)=\"onCategoryClick(category)\"\n        >\n          <div class=\"category-icon\" [style.background]=\"category.color\">\n            <i [class]=\"category.icon\"></i>\n          </div>\n          <span>{{ category.name }}</span>\n        </div>\n      </div>\n    </section>\n\n    <!-- Instagram-style Stories -->\n    <section class=\"section\">\n      <div class=\"section-header\">\n        <h3>Latest Stories</h3>\n        <button class=\"view-all-btn\" routerLink=\"/stories\">View All</button>\n      </div>\n      <app-view-add-stories></app-view-add-stories>\n    </section>\n\n    <!-- Featured Products -->\n    <section class=\"section\" *ngIf=\"featuredProducts.length > 0\">\n      <div class=\"section-header\">\n        <h3>Featured Products</h3>\n        <button class=\"view-all-btn\" routerLink=\"/shop\">View All</button>\n      </div>\n      <div class=\"products-grid\">\n        <div *ngFor=\"let product of featuredProducts\" class=\"product-card\" (click)=\"onProductClick(product)\">\n          <div class=\"product-image\">\n            <img [src]=\"product.images?.[0]?.url || product.images?.[0]\" [alt]=\"product.name\">\n            <div class=\"product-badge\" *ngIf=\"product.discount\">\n              <span>{{ product.discount }}% OFF</span>\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h4>{{ product.name }}</h4>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-price\">\n              <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n              <span class=\"original-price\" *ngIf=\"product.originalPrice\">{{ formatPrice(product.originalPrice) }}</span>\n            </div>\n            <div class=\"product-rating\" *ngIf=\"product.rating\">\n              <div class=\"stars\">\n                <i class=\"fas fa-star\" *ngFor=\"let star of [1,2,3,4,5]\"\n                   [class.filled]=\"star <= product.rating.average\"></i>\n              </div>\n              <span>({{ product.rating.count }})</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Trending Products -->\n    <section class=\"section\" *ngIf=\"trendingProducts.length > 0\">\n      <div class=\"section-header\">\n        <h3>Trending Now</h3>\n        <button class=\"view-all-btn\" routerLink=\"/shop?filter=trending\">View All</button>\n      </div>\n      <div class=\"products-grid\">\n        <div *ngFor=\"let product of trendingProducts\" class=\"product-card\" (click)=\"onProductClick(product)\">\n          <div class=\"product-image\">\n            <img [src]=\"product.images?.[0]?.url || product.images?.[0]\" [alt]=\"product.name\">\n            <div class=\"trending-badge\">\n              <i class=\"fas fa-fire\"></i>\n              <span>Trending</span>\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h4>{{ product.name }}</h4>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-price\">\n              <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n            </div>\n            <div class=\"product-stats\">\n              <span><i class=\"fas fa-eye\"></i> {{ formatNumber(product.analytics?.views || 0) }}</span>\n              <span><i class=\"fas fa-heart\"></i> {{ formatNumber(product.analytics?.likes || 0) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- New Arrivals -->\n    <section class=\"section\" *ngIf=\"newArrivals.length > 0\">\n      <div class=\"section-header\">\n        <h3>New Arrivals</h3>\n        <button class=\"view-all-btn\" routerLink=\"/shop?filter=new\">View All</button>\n      </div>\n      <div class=\"products-grid\">\n        <div *ngFor=\"let product of newArrivals\" class=\"product-card\" (click)=\"onProductClick(product)\">\n          <div class=\"product-image\">\n            <img [src]=\"product.images?.[0]?.url || product.images?.[0]\" [alt]=\"product.name\">\n            <div class=\"new-badge\">\n              <span>New</span>\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h4>{{ product.name }}</h4>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-price\">\n              <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Fashion Inspiration / Posts -->\n    <section class=\"section\" *ngIf=\"trendingPosts.length > 0\">\n      <div class=\"section-header\">\n        <h3>Fashion Inspiration</h3>\n        <button class=\"view-all-btn\" routerLink=\"/feed\">View All</button>\n      </div>\n      <div class=\"posts-grid\">\n        <div *ngFor=\"let post of trendingPosts.slice(0, 4)\" class=\"post-item\">\n          <div class=\"post-image\">\n            <img [src]=\"post.mediaUrl || post.featuredImage\" [alt]=\"post.title || post.content\">\n            <div class=\"post-overlay\">\n              <i class=\"fas fa-play\" *ngIf=\"post.mediaType === 'video'\"></i>\n            </div>\n          </div>\n          <div class=\"post-content\">\n            <h4>{{ post.title || post.content?.substring(0, 50) + '...' }}</h4>\n            <div class=\"post-stats\">\n              <span><i class=\"fas fa-eye\"></i> {{ formatNumber(post.analytics?.views || post.views || 0) }}</span>\n              <span><i class=\"fas fa-heart\"></i> {{ formatNumber(post.analytics?.likes || post.likes || 0) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAGtD,SAASC,uBAAuB,QAAQ,+DAA+D;;;;;;;;;ICHrGC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAC/BH,EAD+B,CAAAI,YAAA,EAAI,EAC7B;;;;;;IAsBAJ,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAP,WAAA,CAAyB;IAAA,EAAC;IAEnCP,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAE,SAAA,QAA+B;IACjCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAO,EAC5B;;;;IAJuBJ,EAAA,CAAAe,SAAA,EAAmC;IAAnCf,EAAA,CAAAgB,WAAA,eAAAT,WAAA,CAAAU,KAAA,CAAmC;IACzDjB,EAAA,CAAAe,SAAA,EAAuB;IAAvBf,EAAA,CAAAkB,UAAA,CAAAX,WAAA,CAAAY,IAAA,CAAuB;IAEtBnB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAoB,iBAAA,CAAAb,WAAA,CAAAc,IAAA,CAAmB;;;;;IAyBrBrB,EADF,CAAAC,cAAA,cAAoD,WAC5C;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACnCH,EADmC,CAAAI,YAAA,EAAO,EACpC;;;;IADEJ,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAsB,kBAAA,KAAAC,UAAA,CAAAC,QAAA,UAA2B;;;;;IAQjCxB,EAAA,CAAAC,cAAA,eAA2D;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAA/CJ,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAc,WAAA,CAAAF,UAAA,CAAAG,aAAA,EAAwC;;;;;IAIjG1B,EAAA,CAAAE,SAAA,YACuD;;;;;IAApDF,EAAA,CAAA2B,WAAA,WAAAC,OAAA,IAAAL,UAAA,CAAAM,MAAA,CAAAC,OAAA,CAA+C;;;;;IAFpD9B,EADF,CAAAC,cAAA,cAAmD,cAC9B;IACjBD,EAAA,CAAA+B,UAAA,IAAAC,wDAAA,gBACmD;IACrDhC,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IACpCH,EADoC,CAAAI,YAAA,EAAO,EACrC;;;;IAJsCJ,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAiC,UAAA,YAAAjC,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAc;IAGlDnC,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAsB,kBAAA,MAAAC,UAAA,CAAAM,MAAA,CAAAO,KAAA,MAA4B;;;;;;IAnBxCpC,EAAA,CAAAC,cAAA,cAAqG;IAAlCD,EAAA,CAAAK,UAAA,mBAAAgC,mEAAA;MAAA,MAAAd,UAAA,GAAAvB,EAAA,CAAAQ,aAAA,CAAA8B,GAAA,EAAA5B,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA4B,cAAA,CAAAhB,UAAA,CAAuB;IAAA,EAAC;IAClGvB,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,cAAkF;IAClFF,EAAA,CAAA+B,UAAA,IAAAS,mDAAA,kBAAoD;IAGtDxC,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE9CJ,EADF,CAAAC,cAAA,cAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAA+B,UAAA,KAAAU,qDAAA,mBAA2D;IAC7DzC,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAA+B,UAAA,KAAAW,oDAAA,kBAAmD;IAQvD1C,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IApBGJ,EAAA,CAAAe,SAAA,GAAuD;IAACf,EAAxD,CAAAiC,UAAA,SAAAV,UAAA,CAAAoB,MAAA,kBAAApB,UAAA,CAAAoB,MAAA,qBAAApB,UAAA,CAAAoB,MAAA,IAAAC,GAAA,MAAArB,UAAA,CAAAoB,MAAA,kBAAApB,UAAA,CAAAoB,MAAA,MAAA3C,EAAA,CAAA6C,aAAA,CAAuD,QAAAtB,UAAA,CAAAF,IAAA,CAAqB;IACrDrB,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAC,QAAA,CAAsB;IAK9CxB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAoB,iBAAA,CAAAG,UAAA,CAAAF,IAAA,CAAkB;IACGrB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAoB,iBAAA,CAAAG,UAAA,CAAAuB,KAAA,CAAmB;IAEd9C,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAc,WAAA,CAAAF,UAAA,CAAAwB,KAAA,EAAgC;IAC9B/C,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAG,aAAA,CAA2B;IAE9B1B,EAAA,CAAAe,SAAA,EAAoB;IAApBf,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAM,MAAA,CAAoB;;;;;IAlBrD7B,EAFJ,CAAAC,cAAA,kBAA6D,cAC/B,SACtB;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,iBAAgD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAC1DH,EAD0D,CAAAI,YAAA,EAAS,EAC7D;IACNJ,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA+B,UAAA,IAAAiB,6CAAA,mBAAqG;IAwBzGhD,EADE,CAAAI,YAAA,EAAM,EACE;;;;IAxBmBJ,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAiC,UAAA,YAAAtB,MAAA,CAAAsC,gBAAA,CAAmB;;;;;;IAiC5CjD,EAAA,CAAAC,cAAA,cAAqG;IAAlCD,EAAA,CAAAK,UAAA,mBAAA6C,mEAAA;MAAA,MAAAC,UAAA,GAAAnD,EAAA,CAAAQ,aAAA,CAAA4C,GAAA,EAAA1C,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA4B,cAAA,CAAAY,UAAA,CAAuB;IAAA,EAAC;IAClGnD,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,cAAkF;IAClFF,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAElBH,EAFkB,CAAAI,YAAA,EAAO,EACjB,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE9CJ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAC9DH,EAD8D,CAAAI,YAAA,EAAO,EAC/D;IAEJJ,EADF,CAAAC,cAAA,eAA2B,YACnB;IAAAD,EAAA,CAAAE,SAAA,aAA0B;IAACF,EAAA,CAAAG,MAAA,IAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzFJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,SAAA,aAA4B;IAACF,EAAA,CAAAG,MAAA,IAAiD;IAG1FH,EAH0F,CAAAI,YAAA,EAAO,EACvF,EACF,EACF;;;;;IAjBGJ,EAAA,CAAAe,SAAA,GAAuD;IAACf,EAAxD,CAAAiC,UAAA,SAAAkB,UAAA,CAAAR,MAAA,kBAAAQ,UAAA,CAAAR,MAAA,qBAAAQ,UAAA,CAAAR,MAAA,IAAAC,GAAA,MAAAO,UAAA,CAAAR,MAAA,kBAAAQ,UAAA,CAAAR,MAAA,MAAA3C,EAAA,CAAA6C,aAAA,CAAuD,QAAAM,UAAA,CAAA9B,IAAA,CAAqB;IAO7ErB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAoB,iBAAA,CAAA+B,UAAA,CAAA9B,IAAA,CAAkB;IACGrB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAoB,iBAAA,CAAA+B,UAAA,CAAAL,KAAA,CAAmB;IAEd9C,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAc,WAAA,CAAA0B,UAAA,CAAAJ,KAAA,EAAgC;IAG3B/C,EAAA,CAAAe,SAAA,GAAiD;IAAjDf,EAAA,CAAAsB,kBAAA,MAAAX,MAAA,CAAA0C,YAAA,EAAAF,UAAA,CAAAG,SAAA,kBAAAH,UAAA,CAAAG,SAAA,CAAAC,KAAA,YAAiD;IAC/CvD,EAAA,CAAAe,SAAA,GAAiD;IAAjDf,EAAA,CAAAsB,kBAAA,MAAAX,MAAA,CAAA0C,YAAA,EAAAF,UAAA,CAAAG,SAAA,kBAAAH,UAAA,CAAAG,SAAA,CAAAE,KAAA,YAAiD;;;;;IApB1FxD,EAFJ,CAAAC,cAAA,kBAA6D,cAC/B,SACtB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrBJ,EAAA,CAAAC,cAAA,iBAAgE;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAC1EH,EAD0E,CAAAI,YAAA,EAAS,EAC7E;IACNJ,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA+B,UAAA,IAAA0B,6CAAA,mBAAqG;IAqBzGzD,EADE,CAAAI,YAAA,EAAM,EACE;;;;IArBmBJ,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAiC,UAAA,YAAAtB,MAAA,CAAA+C,gBAAA,CAAmB;;;;;;IA8B5C1D,EAAA,CAAAC,cAAA,cAAgG;IAAlCD,EAAA,CAAAK,UAAA,mBAAAsD,mEAAA;MAAA,MAAAC,WAAA,GAAA5D,EAAA,CAAAQ,aAAA,CAAAqD,IAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA4B,cAAA,CAAAqB,WAAA,CAAuB;IAAA,EAAC;IAC7F5D,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,cAAkF;IAEhFF,EADF,CAAAC,cAAA,cAAuB,WACf;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAEbH,EAFa,CAAAI,YAAA,EAAO,EACZ,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE9CJ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAGlEH,EAHkE,CAAAI,YAAA,EAAO,EAC/D,EACF,EACF;;;;;IAZGJ,EAAA,CAAAe,SAAA,GAAuD;IAACf,EAAxD,CAAAiC,UAAA,SAAA2B,WAAA,CAAAjB,MAAA,kBAAAiB,WAAA,CAAAjB,MAAA,qBAAAiB,WAAA,CAAAjB,MAAA,IAAAC,GAAA,MAAAgB,WAAA,CAAAjB,MAAA,kBAAAiB,WAAA,CAAAjB,MAAA,MAAA3C,EAAA,CAAA6C,aAAA,CAAuD,QAAAe,WAAA,CAAAvC,IAAA,CAAqB;IAM7ErB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAoB,iBAAA,CAAAwC,WAAA,CAAAvC,IAAA,CAAkB;IACGrB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAoB,iBAAA,CAAAwC,WAAA,CAAAd,KAAA,CAAmB;IAEd9C,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAc,WAAA,CAAAmC,WAAA,CAAAb,KAAA,EAAgC;;;;;IAflE/C,EAFJ,CAAAC,cAAA,kBAAwD,cAC1B,SACtB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrBJ,EAAA,CAAAC,cAAA,iBAA2D;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IACrEH,EADqE,CAAAI,YAAA,EAAS,EACxE;IACNJ,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA+B,UAAA,IAAA+B,6CAAA,mBAAgG;IAgBpG9D,EADE,CAAAI,YAAA,EAAM,EACE;;;;IAhBmBJ,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAiC,UAAA,YAAAtB,MAAA,CAAAoD,WAAA,CAAc;;;;;IA6BjC/D,EAAA,CAAAE,SAAA,YAA8D;;;;;IAHlEF,EADF,CAAAC,cAAA,cAAsE,cAC5C;IACtBD,EAAA,CAAAE,SAAA,cAAoF;IACpFF,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA+B,UAAA,IAAAiC,iDAAA,gBAA0D;IAE9DhE,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAG,MAAA,GAA0D;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEjEJ,EADF,CAAAC,cAAA,cAAwB,WAChB;IAAAD,EAAA,CAAAE,SAAA,aAA0B;IAACF,EAAA,CAAAG,MAAA,IAA4D;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpGJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,SAAA,aAA4B;IAACF,EAAA,CAAAG,MAAA,IAA4D;IAGrGH,EAHqG,CAAAI,YAAA,EAAO,EAClG,EACF,EACF;;;;;IAZGJ,EAAA,CAAAe,SAAA,GAA2C;IAACf,EAA5C,CAAAiC,UAAA,QAAAgC,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAE,aAAA,EAAAnE,EAAA,CAAA6C,aAAA,CAA2C,QAAAoB,QAAA,CAAAG,KAAA,IAAAH,QAAA,CAAAI,OAAA,CAAmC;IAEzDrE,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAiC,UAAA,SAAAgC,QAAA,CAAAK,SAAA,aAAgC;IAItDtE,EAAA,CAAAe,SAAA,GAA0D;IAA1Df,EAAA,CAAAoB,iBAAA,CAAA6C,QAAA,CAAAG,KAAA,KAAAH,QAAA,CAAAI,OAAA,kBAAAJ,QAAA,CAAAI,OAAA,CAAAE,SAAA,iBAA0D;IAE3BvE,EAAA,CAAAe,SAAA,GAA4D;IAA5Df,EAAA,CAAAsB,kBAAA,MAAAX,MAAA,CAAA0C,YAAA,EAAAY,QAAA,CAAAX,SAAA,kBAAAW,QAAA,CAAAX,SAAA,CAAAC,KAAA,KAAAU,QAAA,CAAAV,KAAA,WAA4D;IAC1DvD,EAAA,CAAAe,SAAA,GAA4D;IAA5Df,EAAA,CAAAsB,kBAAA,MAAAX,MAAA,CAAA0C,YAAA,EAAAY,QAAA,CAAAX,SAAA,kBAAAW,QAAA,CAAAX,SAAA,CAAAE,KAAA,KAAAS,QAAA,CAAAT,KAAA,WAA4D;;;;;IAfrGxD,EAFJ,CAAAC,cAAA,kBAA0D,cAC5B,SACtB;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,iBAAgD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAC1DH,EAD0D,CAAAI,YAAA,EAAS,EAC7D;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA+B,UAAA,IAAAyC,6CAAA,mBAAsE;IAgB1ExE,EADE,CAAAI,YAAA,EAAM,EACE;;;;IAhBgBJ,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAiC,UAAA,YAAAtB,MAAA,CAAA8D,aAAA,CAAAC,KAAA,OAA4B;;;;;;IAnIhD1E,EALR,CAAAC,cAAA,aAA6C,iBAEb,aACH,aAC0E,SAC3F;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,kCAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClCJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAGzCH,EAHyC,CAAAI,YAAA,EAAS,EACxC,EACF,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAyB,eACK,UACtB;IAAAD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,kBAA2D;IAA9BD,EAAA,CAAAK,UAAA,mBAAAsE,sDAAA;MAAA3E,EAAA,CAAAQ,aAAA,CAAAoE,GAAA;MAAA,MAAAjE,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkE,iBAAA,EAAmB;IAAA,EAAC;IAAC7E,EAAA,CAAAG,MAAA,gBAAQ;IACrEH,EADqE,CAAAI,YAAA,EAAS,EACxE;IACNJ,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA+B,UAAA,KAAA+C,mCAAA,kBAIC;IAOL9E,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAyB,eACK,UACtB;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvBJ,EAAA,CAAAC,cAAA,kBAAmD;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAC7DH,EAD6D,CAAAI,YAAA,EAAS,EAChE;IACNJ,EAAA,CAAAE,SAAA,4BAA6C;IAC/CF,EAAA,CAAAI,YAAA,EAAU;IA2FVJ,EAxFA,CAAA+B,UAAA,KAAAgD,uCAAA,sBAA6D,KAAAC,uCAAA,sBAiCA,KAAAC,uCAAA,sBA8BL,KAAAC,uCAAA,sBAyBE;IAuB5DlF,EAAA,CAAAI,YAAA,EAAM;;;;IArIuBJ,EAAA,CAAAe,SAAA,IAAa;IAAbf,EAAA,CAAAiC,UAAA,YAAAtB,MAAA,CAAAwE,UAAA,CAAa;IAsBdnF,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAiC,UAAA,SAAAtB,MAAA,CAAAsC,gBAAA,CAAAmC,MAAA,KAAiC;IAiCjCpF,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAiC,UAAA,SAAAtB,MAAA,CAAA+C,gBAAA,CAAA0B,MAAA,KAAiC;IA8BjCpF,EAAA,CAAAe,SAAA,EAA4B;IAA5Bf,EAAA,CAAAiC,UAAA,SAAAtB,MAAA,CAAAoD,WAAA,CAAAqB,MAAA,KAA4B;IAyB5BpF,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAiC,UAAA,SAAAtB,MAAA,CAAA8D,aAAA,CAAAW,MAAA,KAA8B;;;AD5H5D,OAAM,MAAOC,aAAa;EASxBC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IAXrB,KAAAxC,gBAAgB,GAAU,EAAE;IAC5B,KAAAS,gBAAgB,GAAU,EAAE;IAC5B,KAAAK,WAAW,GAAU,EAAE;IACvB,KAAAU,aAAa,GAAU,EAAE;IACzB,KAAAU,UAAU,GAAU,EAAE;IACtB,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAAC,eAAe,GAAG,KAAK;EAMpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACL,WAAW,CAACM,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACN,eAAe,GAAG,CAAC,CAACM,IAAI;IAC/B,CAAC,CAAC;EACJ;EAEMJ,YAAYA,CAAA;IAAA,IAAAK,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI;QACFD,KAAI,CAACR,SAAS,GAAG,IAAI;QAErB;QACA,MAAM,CAACU,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACvDN,KAAI,CAACV,cAAc,CAACiB,mBAAmB,EAAE,CAACC,SAAS,EAAE,EACrDR,KAAI,CAACV,cAAc,CAACmB,mBAAmB,EAAE,CAACD,SAAS,EAAE,EACrDR,KAAI,CAACV,cAAc,CAACoB,cAAc,EAAE,CAACF,SAAS,EAAE,CACjD,CAAC;QAEFR,KAAI,CAACjD,gBAAgB,GAAGmD,QAAQ,EAAE1B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIwB,KAAI,CAACW,mBAAmB,EAAE;QAC3EX,KAAI,CAACxC,gBAAgB,GAAG2C,QAAQ,EAAE3B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIwB,KAAI,CAACW,mBAAmB,EAAE;QAC3EX,KAAI,CAACnC,WAAW,GAAGuC,QAAQ,EAAE5B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIwB,KAAI,CAACW,mBAAmB,EAAE;QAEtE;QACAX,KAAI,CAACY,cAAc,EAAE;QAErB;QACAZ,KAAI,CAACa,iBAAiB,EAAE;OAEzB,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDd,KAAI,CAACgB,gBAAgB,EAAE;OACxB,SAAS;QACRhB,KAAI,CAACR,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAoB,cAAcA,CAAA;IACZ,IAAI,CAAC3B,UAAU,GAAG,CAChB;MAAE9D,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE,eAAe;MAAEF,KAAK,EAAE;IAA0C,CAAE,EAC3F;MAAEI,IAAI,EAAE,KAAK;MAAEF,IAAI,EAAE,aAAa;MAAEF,KAAK,EAAE;IAA0C,CAAE,EACvF;MAAEI,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE,cAAc;MAAEF,KAAK,EAAE;IAA0C,CAAE,EACzF;MAAEI,IAAI,EAAE,aAAa;MAAEF,IAAI,EAAE,YAAY;MAAEF,KAAK,EAAE;IAA0C,CAAE,EAC9F;MAAEI,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE,oBAAoB;MAAEF,KAAK,EAAE;IAA0C,CAAE,EAChG;MAAEI,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE,qBAAqB;MAAEF,KAAK,EAAE;IAA0C,CAAE,CACjG;EACH;EAEA8F,iBAAiBA,CAAA;IACf,IAAI,CAACtC,aAAa,GAAG,CACnB;MACEL,KAAK,EAAE,4BAA4B;MACnCF,QAAQ,EAAE,oEAAoE;MAC9EI,SAAS,EAAE,OAAO;MAClBhB,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG;KACtC,EACD;MACEY,KAAK,EAAE,0BAA0B;MACjCF,QAAQ,EAAE,oEAAoE;MAC9EI,SAAS,EAAE,OAAO;MAClBhB,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI;KACvC,CACF;EACH;EAEAqD,mBAAmBA,CAAA;IACjB,OAAO,CACL;MACEM,GAAG,EAAE,GAAG;MACR9F,IAAI,EAAE,sBAAsB;MAC5ByB,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,KAAK;MACZrB,aAAa,EAAE,MAAM;MACrBF,QAAQ,EAAE,EAAE;MACZmB,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAiE,CAAE,CAAC;MACpFf,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEM,KAAK,EAAE;MAAG,CAAE;MACpCkB,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG;KACtC,EACD;MACE2D,GAAG,EAAE,GAAG;MACR9F,IAAI,EAAE,iBAAiB;MACvByB,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,MAAM;MACbrB,aAAa,EAAE,MAAM;MACrBF,QAAQ,EAAE,EAAE;MACZmB,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFf,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEM,KAAK,EAAE;MAAE,CAAE;MACnCkB,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI;KACvC,CACF;EACH;EAEA0D,gBAAgBA,CAAA;IACd,IAAI,CAACjE,gBAAgB,GAAG,IAAI,CAAC4D,mBAAmB,EAAE;IAClD,IAAI,CAACnD,gBAAgB,GAAG,IAAI,CAACmD,mBAAmB,EAAE;IAClD,IAAI,CAAC9C,WAAW,GAAG,IAAI,CAAC8C,mBAAmB,EAAE;EAC/C;EAEA;EACAtE,cAAcA,CAAC6E,OAAY;IACzB,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACD,GAAG,IAAIC,OAAO,CAACE,EAAE,CAAC,CAAC;EAC/D;EAEAxG,eAAeA,CAACyG,QAAa;IAC3B,IAAI,CAAChC,MAAM,CAAC8B,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BG,WAAW,EAAE;QAAED,QAAQ,EAAEA,QAAQ,CAAClG,IAAI,CAACoG,WAAW;MAAE;KACrD,CAAC;EACJ;EAEA5C,iBAAiBA,CAAA;IACf,IAAI,CAACU,MAAM,CAAC8B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA;EACA5F,WAAWA,CAACsB,KAAa;IACvB,OAAO,IAAI2E,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAC;EAClB;EAEAM,YAAYA,CAAC0E,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;;;uBAlJW5C,aAAa,EAAArF,EAAA,CAAAkI,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApI,EAAA,CAAAkI,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtI,EAAA,CAAAkI,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAbnD,aAAa;MAAAoD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3I,EAAA,CAAA4I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd1BlJ,EAAA,CAAAC,cAAA,aAA4B;UAQ1BD,EANA,CAAA+B,UAAA,IAAAqH,4BAAA,iBAAiD,IAAAC,4BAAA,kBAMJ;UA0J/CrJ,EAAA,CAAAI,YAAA,EAAM;;;UAhKEJ,EAAA,CAAAe,SAAA,EAAe;UAAff,EAAA,CAAAiC,UAAA,SAAAkH,GAAA,CAAAzD,SAAA,CAAe;UAMf1F,EAAA,CAAAe,SAAA,EAAgB;UAAhBf,EAAA,CAAAiC,UAAA,UAAAkH,GAAA,CAAAzD,SAAA,CAAgB;;;qBDEZ7F,YAAY,EAAAyJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1J,YAAY,EAAAqI,EAAA,CAAAsB,UAAA,EAAE1J,uBAAuB;MAAA2J,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}