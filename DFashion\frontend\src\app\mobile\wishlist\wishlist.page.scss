// Mobile Wishlist Styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 200px;
  
  ion-spinner {
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

.empty-wishlist {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
  
  .empty-content {
    text-align: center;
    max-width: 300px;
    
    ion-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }
    
    h2 {
      color: var(--ion-color-dark);
      margin-bottom: 0.5rem;
    }
    
    p {
      color: var(--ion-color-medium);
      margin-bottom: 2rem;
    }
  }
}

// Selection Status Card
.selection-status-card {
  margin: 8px;
  border-left: 4px solid var(--ion-color-primary);
  
  .selection-info {
    .selection-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .selection-label {
      font-weight: 500;
      color: var(--ion-color-medium);
    }
    
    .selection-value {
      font-weight: 600;
      color: var(--ion-color-dark);
    }
  }
}

// Bulk Actions
.bulk-actions {
  padding: 8px;
  
  ion-button {
    margin-bottom: 8px;
    
    &[disabled] {
      opacity: 0.6;
    }
  }
}

// Wishlist Items
.selected-item {
  --background: var(--ion-color-primary-tint);
  --border-color: var(--ion-color-primary);
  border-left: 3px solid var(--ion-color-primary);
}

ion-item {
  --padding-start: 8px;
  cursor: pointer;
  
  ion-checkbox {
    margin-right: 8px;
  }
  
  ion-thumbnail {
    --size: 80px;
    --border-radius: 8px;
    margin-right: 12px;
    
    img {
      object-fit: cover;
      border-radius: 8px;
    }
  }
}

.item-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  
  .current-price {
    font-weight: 600;
    color: var(--ion-color-primary);
    font-size: 1.1rem;
  }
  
  .original-price {
    text-decoration: line-through;
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
  
  ion-chip {
    font-size: 0.7rem;
    height: 20px;
  }
}

.item-meta {
  margin: 4px 0;
  
  .added-date {
    font-size: 0.8rem;
    color: var(--ion-color-medium);
  }
}

.item-actions {
  display: flex;
  align-items: center;
  
  ion-button {
    --padding-start: 8px;
    --padding-end: 8px;
    height: 32px;
    width: 32px;
  }
}

// Action Buttons
.action-buttons {
  padding: 16px;
  
  ion-button {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Item sliding options
ion-item-options {
  ion-item-option {
    min-width: 60px;
    
    ion-icon {
      font-size: 1.2rem;
    }
  }
}

// Responsive Design
@media (max-width: 576px) {
  .selection-status-card,
  .bulk-actions {
    margin: 4px;
  }
  
  ion-thumbnail {
    --size: 60px;
  }
  
  .item-price {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .item-actions {
    flex-direction: column;
    gap: 4px;
  }
}
