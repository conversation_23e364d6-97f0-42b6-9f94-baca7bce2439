{"ast": null, "code": "import { AuthGuard } from './core/guards/auth.guard';\nexport const routes = [\n// Home Route (Public)\n{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n},\n// Authentication Routes\n{\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n},\n// Home with Auth\n{\n  path: 'home',\n  loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n  canActivate: [AuthGuard]\n},\n// Shop Routes\n{\n  path: 'shop',\n  loadComponent: () => import('./features/shop/shop.component').then(m => m.ShopComponent),\n  title: 'Shop - DFashion'\n},\n// Category Routes\n{\n  path: 'category/:category',\n  loadComponent: () => import('./features/category/category.component').then(m => m.CategoryComponent),\n  title: 'Category - DFashion'\n},\n// Wishlist Routes\n{\n  path: 'wishlist',\n  loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),\n  title: 'My Wishlist - DFashion'\n},\n// Products Routes (using existing product detail)\n{\n  path: 'product/:id',\n  loadComponent: () => import('./features/product/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n}, {\n  path: 'products/:id',\n  redirectTo: 'product/:id'\n},\n// Shopping Cart & Wishlist (will be created)\n{\n  path: 'cart',\n  loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n  canActivate: [AuthGuard],\n  title: 'Shopping Cart - DFashion'\n},\n// Checkout Process (using existing checkout)\n{\n  path: 'checkout',\n  loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n  canActivate: [AuthGuard],\n  title: 'Checkout - DFashion'\n},\n// User Account Management\n{\n  path: 'account',\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'profile',\n    pathMatch: 'full'\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes)\n  }, {\n    path: 'orders',\n    loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n    title: 'My Orders - DFashion'\n  }]\n},\n// Vendor Dashboard\n{\n  path: 'vendor',\n  loadChildren: () => import('./features/vendor/vendor.routes').then(m => m.vendorRoutes),\n  canActivate: [AuthGuard],\n  title: 'Vendor Dashboard - DFashion'\n},\n// Legacy Routes (maintain compatibility)\n{\n  path: 'shop',\n  loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'search',\n  loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n}, {\n  path: 'stories',\n  loadChildren: () => import('./features/story/story.routes').then(m => m.storyRoutes)\n}, {\n  path: 'product/:id',\n  redirectTo: 'products/:id'\n},\n// Admin Routes\n{\n  path: 'admin',\n  loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n},\n// Support & Help (using existing profile as placeholder)\n{\n  path: 'support',\n  loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n  title: 'Support - DFashion'\n},\n// Wildcard route\n{\n  path: '**',\n  redirectTo: '/'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "authRoutes", "homeRoutes", "canActivate", "loadComponent", "ShopComponent", "title", "CategoryComponent", "WishlistComponent", "ProductDetailComponent", "CheckoutComponent", "children", "profileRoutes", "ProfileComponent", "vendorRoutes", "shopRoutes", "searchRoutes", "storyRoutes", "AdminModule"], "sources": ["E:\\DFashion\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\n\nexport const routes: Routes = [\n  // Home Route (Public)\n  {\n    path: '',\n    redirectTo: '/home',\n    pathMatch: 'full'\n  },\n\n  // Authentication Routes\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n  },\n\n  // Home with Auth\n  {\n    path: 'home',\n    loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n    canActivate: [AuthGuard]\n  },\n\n  // Shop Routes\n  {\n    path: 'shop',\n    loadComponent: () => import('./features/shop/shop.component').then(m => m.ShopComponent),\n    title: 'Shop - DFashion'\n  },\n\n  // Category Routes\n  {\n    path: 'category/:category',\n    loadComponent: () => import('./features/category/category.component').then(m => m.CategoryComponent),\n    title: 'Category - DFashion'\n  },\n\n  // Wishlist Routes\n  {\n    path: 'wishlist',\n    loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),\n    title: 'My Wishlist - DFashion'\n  },\n\n  // Products Routes (using existing product detail)\n  {\n    path: 'product/:id',\n    loadComponent: () => import('./features/product/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n  },\n  {\n    path: 'products/:id',\n    redirectTo: 'product/:id'\n  },\n\n  // Shopping Cart & Wishlist (will be created)\n  {\n    path: 'cart',\n    loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n    canActivate: [AuthGuard],\n    title: 'Shopping Cart - DFashion'\n  },\n\n  // Checkout Process (using existing checkout)\n  {\n    path: 'checkout',\n    loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n    canActivate: [AuthGuard],\n    title: 'Checkout - DFashion'\n  },\n\n  // User Account Management\n  {\n    path: 'account',\n    canActivate: [AuthGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'profile',\n        pathMatch: 'full'\n      },\n      {\n        path: 'profile',\n        loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes)\n      },\n      {\n        path: 'orders',\n        loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n        title: 'My Orders - DFashion'\n      }\n    ]\n  },\n\n  // Vendor Dashboard\n  {\n    path: 'vendor',\n    loadChildren: () => import('./features/vendor/vendor.routes').then(m => m.vendorRoutes),\n    canActivate: [AuthGuard],\n    title: 'Vendor Dashboard - DFashion'\n  },\n\n  // Legacy Routes (maintain compatibility)\n  {\n    path: 'shop',\n    loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'search',\n    loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n  },\n  {\n    path: 'stories',\n    loadChildren: () => import('./features/story/story.routes').then(m => m.storyRoutes)\n  },\n  {\n    path: 'product/:id',\n    redirectTo: 'products/:id'\n  },\n\n  // Admin Routes\n  {\n    path: 'admin',\n    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n  },\n\n  // Support & Help (using existing profile as placeholder)\n  {\n    path: 'support',\n    loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n    title: 'Support - DFashion'\n  },\n\n  // Wildcard route\n  {\n    path: '**',\n    redirectTo: '/'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,0BAA0B;AAEpD,OAAO,MAAMC,MAAM,GAAW;AAC5B;AACA;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ;AAED;AACA;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF;AAED;AACA;EACEN,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,UAAU,CAAC;EACjFC,WAAW,EAAE,CAACV,SAAS;CACxB;AAED;AACA;EACEE,IAAI,EAAE,MAAM;EACZS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa,CAAC;EACxFC,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,oBAAoB;EAC1BS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,iBAAiB,CAAC;EACpGD,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,UAAU;EAChBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,iBAAiB,CAAC;EACpGF,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,aAAa;EACnBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4DAA4D,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,sBAAsB;CAC7H,EACD;EACEd,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;CACb;AAED;AACA;EACED,IAAI,EAAE,MAAM;EACZS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,iBAAiB,CAAC;EACpGP,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBa,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,UAAU;EAChBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,iBAAiB,CAAC;EACpGP,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBa,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,SAAS;EACfQ,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBkB,QAAQ,EAAE,CACR;IACEhB,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE;GACZ,EACD;IACEF,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,aAAa;GAC1F,EACD;IACEjB,IAAI,EAAE,QAAQ;IACdS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,gBAAgB,CAAC;IAC/GP,KAAK,EAAE;GACR;CAEJ;AAED;AACA;EACEX,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,YAAY,CAAC;EACvFX,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBa,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACe,UAAU,CAAC;EACjFZ,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACgB,YAAY;CACvF,EACD;EACErB,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACiB,WAAW;CACpF,EACD;EACEtB,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;CACb;AAED;AACA;EACED,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACkB,WAAW;CAC3E;AAED;AACA;EACEvB,IAAI,EAAE,SAAS;EACfS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,gBAAgB,CAAC;EAC/GP,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}