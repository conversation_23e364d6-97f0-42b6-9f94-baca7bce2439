.stories-grid-container {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.grid-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.view-options {
  display: flex;
  gap: 8px;
}

.view-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
}

.view-btn:hover,
.view-btn.active {
  background: #007bff;
  border-color: #007bff;
  color: #fff;
}

.stories-grid.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.stories-grid.list-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.story-group {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.story-group:hover {
  transform: translateY(-4px);
}

/* Grid View Styles */
.grid-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.story-preview {
  position: relative;
  aspect-ratio: 9/16;
  border-radius: 12px;
  overflow: hidden;
  background: #f0f0f0;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid;
  border-image: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c) 1;
  border-radius: 12px;
}

.story-count {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0,0,0,0.7);
  color: #fff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.video-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(0,0,0,0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 0.8rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 500;
  font-size: 0.9rem;
  color: #333;
}

.story-time {
  font-size: 0.8rem;
  color: #666;
}

/* List View Styles */
.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.list-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-container {
  position: relative;
}

.user-avatar-container .user-avatar {
  width: 48px;
  height: 48px;
}

.user-avatar-container .story-ring {
  border-radius: 50%;
}

.story-info {
  font-size: 0.85rem;
  color: #666;
}

.stories-preview {
  display: flex;
  gap: 8px;
  align-items: center;
}

.preview-item {
  position: relative;
  width: 40px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: #f0f0f0;
}

.preview-item.viewed {
  opacity: 0.6;
}

.preview-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-item .video-indicator {
  width: 16px;
  height: 16px;
  bottom: 4px;
  right: 4px;
  font-size: 0.6rem;
}

.more-indicator {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

/* Loading States */
.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.loading-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-preview {
  aspect-ratio: 9/16;
  border-radius: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-user {
  display: flex;
  gap: 10px;
  align-items: center;
}

.skeleton-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.skeleton-line {
  height: 12px;
  border-radius: 6px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-line.short {
  width: 60%;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-state i {
  font-size: 4rem;
  color: #ddd;
  margin-bottom: 20px;
}

.empty-state h4 {
  margin: 0 0 12px 0;
  font-size: 1.2rem;
  color: #333;
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stories-grid-container {
    padding: 16px;
  }

  .grid-header h3 {
    font-size: 1.2rem;
  }

  .stories-grid.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
  }

  .stories-grid.list-view {
    gap: 12px;
  }

  .list-item {
    padding: 12px;
  }

  .user-avatar-container .user-avatar {
    width: 40px;
    height: 40px;
  }

  .preview-item {
    width: 32px;
    height: 48px;
  }

  .loading-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .stories-grid.grid-view {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .view-options {
    display: none;
  }

  .list-item {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
