{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class RoleManagementService {\n  constructor() {\n    this.currentUserRoleSubject = new BehaviorSubject(null);\n    this.currentUserRole$ = this.currentUserRoleSubject.asObservable();\n    this.roleConfigurations = {\n      super_admin: {\n        role: 'super_admin',\n        department: 'administration',\n        hierarchy: 1,\n        displayName: 'Super Administrator',\n        description: 'Full system access and control',\n        color: '#FF6B6B',\n        icon: 'fas fa-crown',\n        permissions: [{\n          module: '*',\n          actions: ['*'],\n          scope: 'global'\n        }],\n        profileLayout: 'admin',\n        dashboardWidgets: ['system_overview', 'user_management', 'analytics', 'security', 'audit_logs']\n      },\n      admin: {\n        role: 'admin',\n        department: 'administration',\n        hierarchy: 2,\n        displayName: 'Administrator',\n        description: 'System administration and user management',\n        color: '#4ECDC4',\n        icon: 'fas fa-user-shield',\n        permissions: [{\n          module: 'users',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'global'\n        }, {\n          module: 'roles',\n          actions: ['read', 'update'],\n          scope: 'global'\n        }, {\n          module: 'system',\n          actions: ['read', 'update'],\n          scope: 'global'\n        }],\n        profileLayout: 'admin',\n        dashboardWidgets: ['user_stats', 'role_management', 'system_health', 'recent_activities']\n      },\n      sales_manager: {\n        role: 'sales_manager',\n        department: 'sales',\n        hierarchy: 3,\n        displayName: 'Sales Manager',\n        description: 'Sales team leadership and strategy',\n        color: '#45B7D1',\n        icon: 'fas fa-chart-line',\n        permissions: [{\n          module: 'sales',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'department'\n        }, {\n          module: 'customers',\n          actions: ['read', 'update'],\n          scope: 'department'\n        }, {\n          module: 'reports',\n          actions: ['read'],\n          scope: 'department'\n        }, {\n          module: 'team',\n          actions: ['read', 'update'],\n          scope: 'department'\n        }],\n        profileLayout: 'manager',\n        dashboardWidgets: ['sales_overview', 'team_performance', 'revenue_trends', 'customer_insights']\n      },\n      sales_executive: {\n        role: 'sales_executive',\n        department: 'sales',\n        hierarchy: 6,\n        displayName: 'Sales Executive',\n        description: 'Direct sales and customer relationship management',\n        color: '#96CEB4',\n        icon: 'fas fa-handshake',\n        permissions: [{\n          module: 'sales',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }, {\n          module: 'customers',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }, {\n          module: 'leads',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }],\n        profileLayout: 'specialist',\n        dashboardWidgets: ['my_sales', 'my_customers', 'targets', 'commission']\n      },\n      marketing_manager: {\n        role: 'marketing_manager',\n        department: 'marketing',\n        hierarchy: 3,\n        displayName: 'Marketing Manager',\n        description: 'Marketing strategy and campaign management',\n        color: '#F38BA8',\n        icon: 'fas fa-bullhorn',\n        permissions: [{\n          module: 'marketing',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'department'\n        }, {\n          module: 'campaigns',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'department'\n        }, {\n          module: 'content',\n          actions: ['read', 'update'],\n          scope: 'department'\n        }, {\n          module: 'analytics',\n          actions: ['read'],\n          scope: 'department'\n        }],\n        profileLayout: 'manager',\n        dashboardWidgets: ['campaign_performance', 'audience_insights', 'content_metrics', 'roi_analysis']\n      },\n      marketing_executive: {\n        role: 'marketing_executive',\n        department: 'marketing',\n        hierarchy: 6,\n        displayName: 'Marketing Executive',\n        description: 'Campaign execution and content creation',\n        color: '#DDA0DD',\n        icon: 'fas fa-palette',\n        permissions: [{\n          module: 'campaigns',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }, {\n          module: 'content',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }, {\n          module: 'social_media',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }],\n        profileLayout: 'specialist',\n        dashboardWidgets: ['my_campaigns', 'content_calendar', 'social_metrics', 'creative_assets']\n      },\n      account_manager: {\n        role: 'account_manager',\n        department: 'accounting',\n        hierarchy: 4,\n        displayName: 'Account Manager',\n        description: 'Financial oversight and accounting management',\n        color: '#FFD93D',\n        icon: 'fas fa-calculator',\n        permissions: [{\n          module: 'accounting',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'department'\n        }, {\n          module: 'financial_reports',\n          actions: ['create', 'read'],\n          scope: 'department'\n        }, {\n          module: 'budgets',\n          actions: ['create', 'read', 'update'],\n          scope: 'department'\n        }],\n        profileLayout: 'manager',\n        dashboardWidgets: ['financial_overview', 'budget_tracking', 'expense_analysis', 'profit_margins']\n      },\n      accountant: {\n        role: 'accountant',\n        department: 'accounting',\n        hierarchy: 7,\n        displayName: 'Accountant',\n        description: 'Financial record keeping and transaction management',\n        color: '#6BCF7F',\n        icon: 'fas fa-file-invoice-dollar',\n        permissions: [{\n          module: 'transactions',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }, {\n          module: 'invoices',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }, {\n          module: 'expenses',\n          actions: ['create', 'read', 'update'],\n          scope: 'self'\n        }],\n        profileLayout: 'specialist',\n        dashboardWidgets: ['daily_transactions', 'pending_invoices', 'expense_tracker', 'tax_summary']\n      },\n      support_manager: {\n        role: 'support_manager',\n        department: 'support',\n        hierarchy: 4,\n        displayName: 'Support Manager',\n        description: 'Customer support team leadership',\n        color: '#FF8C42',\n        icon: 'fas fa-headset',\n        permissions: [{\n          module: 'support',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'department'\n        }, {\n          module: 'tickets',\n          actions: ['read', 'update', 'assign'],\n          scope: 'department'\n        }, {\n          module: 'knowledge_base',\n          actions: ['create', 'read', 'update'],\n          scope: 'department'\n        }],\n        profileLayout: 'manager',\n        dashboardWidgets: ['support_overview', 'ticket_analytics', 'team_workload', 'satisfaction_scores']\n      },\n      support_agent: {\n        role: 'support_agent',\n        department: 'support',\n        hierarchy: 8,\n        displayName: 'Support Agent',\n        description: 'Direct customer support and issue resolution',\n        color: '#A8E6CF',\n        icon: 'fas fa-life-ring',\n        permissions: [{\n          module: 'tickets',\n          actions: ['read', 'update'],\n          scope: 'self'\n        }, {\n          module: 'knowledge_base',\n          actions: ['read'],\n          scope: 'department'\n        }, {\n          module: 'customer_communication',\n          actions: ['create', 'read'],\n          scope: 'self'\n        }],\n        profileLayout: 'specialist',\n        dashboardWidgets: ['my_tickets', 'response_times', 'customer_feedback', 'knowledge_search']\n      },\n      content_manager: {\n        role: 'content_manager',\n        department: 'content',\n        hierarchy: 5,\n        displayName: 'Content Manager',\n        description: 'Content strategy and editorial oversight',\n        color: '#B19CD9',\n        icon: 'fas fa-edit',\n        permissions: [{\n          module: 'content',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'department'\n        }, {\n          module: 'editorial',\n          actions: ['create', 'read', 'update'],\n          scope: 'department'\n        }, {\n          module: 'publishing',\n          actions: ['approve', 'schedule'],\n          scope: 'department'\n        }],\n        profileLayout: 'manager',\n        dashboardWidgets: ['content_pipeline', 'editorial_calendar', 'engagement_metrics', 'seo_performance']\n      },\n      vendor_manager: {\n        role: 'vendor_manager',\n        department: 'vendor_management',\n        hierarchy: 5,\n        displayName: 'Vendor Manager',\n        description: 'Vendor relationship and partnership management',\n        color: '#FFB6C1',\n        icon: 'fas fa-store',\n        permissions: [{\n          module: 'vendors',\n          actions: ['create', 'read', 'update', 'delete'],\n          scope: 'department'\n        }, {\n          module: 'contracts',\n          actions: ['create', 'read', 'update'],\n          scope: 'department'\n        }, {\n          module: 'vendor_performance',\n          actions: ['read', 'evaluate'],\n          scope: 'department'\n        }],\n        profileLayout: 'manager',\n        dashboardWidgets: ['vendor_overview', 'contract_status', 'performance_metrics', 'payment_tracking']\n      }\n    };\n  }\n  setCurrentUserRole(role) {\n    this.currentUserRoleSubject.next(role);\n  }\n  getCurrentUserRole() {\n    return this.currentUserRoleSubject.value;\n  }\n  getRoleConfig(role) {\n    return this.roleConfigurations[role];\n  }\n  getAllRoles() {\n    return Object.values(this.roleConfigurations);\n  }\n  getRolesByDepartment(department) {\n    return Object.values(this.roleConfigurations).filter(config => config.department === department).sort((a, b) => a.hierarchy - b.hierarchy);\n  }\n  hasPermission(role, module, action) {\n    const config = this.getRoleConfig(role);\n    // Super admin has all permissions\n    if (role === 'super_admin') return true;\n    return config.permissions.some(permission => {\n      const moduleMatch = permission.module === '*' || permission.module === module;\n      const actionMatch = permission.actions.includes('*') || permission.actions.includes(action);\n      return moduleMatch && actionMatch;\n    });\n  }\n  getAccessibleModules(role) {\n    const config = this.getRoleConfig(role);\n    if (role === 'super_admin') return ['*'];\n    return config.permissions.map(p => p.module);\n  }\n  isManager(role) {\n    return role.includes('_manager') || role === 'super_admin' || role === 'admin';\n  }\n  getSubordinateRoles(role) {\n    const config = this.getRoleConfig(role);\n    return Object.values(this.roleConfigurations).filter(c => c.department === config.department && c.hierarchy > config.hierarchy).map(c => c.role);\n  }\n  getDepartmentHierarchy(department) {\n    return this.getRolesByDepartment(department);\n  }\n  static {\n    this.ɵfac = function RoleManagementService_Factory(t) {\n      return new (t || RoleManagementService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleManagementService,\n      factory: RoleManagementService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "RoleManagementService", "constructor", "currentUserRoleSubject", "currentUserRole$", "asObservable", "roleConfigurations", "super_admin", "role", "department", "hierarchy", "displayName", "description", "color", "icon", "permissions", "module", "actions", "scope", "profileLayout", "dashboardWidgets", "admin", "sales_manager", "sales_executive", "marketing_manager", "marketing_executive", "account_manager", "accountant", "support_manager", "support_agent", "content_manager", "vendor_manager", "setCurrentUserRole", "next", "getCurrentUserRole", "value", "getRoleConfig", "getAllRoles", "Object", "values", "getRolesByDepartment", "filter", "config", "sort", "a", "b", "hasPermission", "action", "some", "permission", "moduleMatch", "actionMatch", "includes", "getAccessibleModules", "map", "p", "is<PERSON>anager", "getSubordinateRoles", "c", "getDepartmentHierarchy", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\role-management.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\n\nexport type UserRole = \n  | 'super_admin' \n  | 'admin' \n  | 'sales_manager' \n  | 'sales_executive' \n  | 'marketing_manager' \n  | 'marketing_executive' \n  | 'account_manager' \n  | 'accountant' \n  | 'support_manager' \n  | 'support_agent' \n  | 'content_manager' \n  | 'vendor_manager';\n\nexport type Department = \n  | 'administration' \n  | 'sales' \n  | 'marketing' \n  | 'accounting' \n  | 'support' \n  | 'content' \n  | 'vendor_management';\n\nexport interface RoleConfig {\n  role: UserRole;\n  department: Department;\n  hierarchy: number; // 1 = highest authority, 10 = lowest\n  displayName: string;\n  description: string;\n  color: string;\n  icon: string;\n  permissions: Permission[];\n  profileLayout: ProfileLayoutType;\n  dashboardWidgets: string[];\n}\n\nexport interface Permission {\n  module: string;\n  actions: string[];\n  scope: 'global' | 'department' | 'team' | 'self';\n}\n\nexport type ProfileLayoutType = \n  | 'executive' \n  | 'manager' \n  | 'specialist' \n  | 'admin';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RoleManagementService {\n  private currentUserRoleSubject = new BehaviorSubject<UserRole | null>(null);\n  public currentUserRole$ = this.currentUserRoleSubject.asObservable();\n\n  private readonly roleConfigurations: Record<UserRole, RoleConfig> = {\n    super_admin: {\n      role: 'super_admin',\n      department: 'administration',\n      hierarchy: 1,\n      displayName: 'Super Administrator',\n      description: 'Full system access and control',\n      color: '#FF6B6B',\n      icon: 'fas fa-crown',\n      permissions: [\n        { module: '*', actions: ['*'], scope: 'global' }\n      ],\n      profileLayout: 'admin',\n      dashboardWidgets: ['system_overview', 'user_management', 'analytics', 'security', 'audit_logs']\n    },\n    admin: {\n      role: 'admin',\n      department: 'administration',\n      hierarchy: 2,\n      displayName: 'Administrator',\n      description: 'System administration and user management',\n      color: '#4ECDC4',\n      icon: 'fas fa-user-shield',\n      permissions: [\n        { module: 'users', actions: ['create', 'read', 'update', 'delete'], scope: 'global' },\n        { module: 'roles', actions: ['read', 'update'], scope: 'global' },\n        { module: 'system', actions: ['read', 'update'], scope: 'global' }\n      ],\n      profileLayout: 'admin',\n      dashboardWidgets: ['user_stats', 'role_management', 'system_health', 'recent_activities']\n    },\n    sales_manager: {\n      role: 'sales_manager',\n      department: 'sales',\n      hierarchy: 3,\n      displayName: 'Sales Manager',\n      description: 'Sales team leadership and strategy',\n      color: '#45B7D1',\n      icon: 'fas fa-chart-line',\n      permissions: [\n        { module: 'sales', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },\n        { module: 'customers', actions: ['read', 'update'], scope: 'department' },\n        { module: 'reports', actions: ['read'], scope: 'department' },\n        { module: 'team', actions: ['read', 'update'], scope: 'department' }\n      ],\n      profileLayout: 'manager',\n      dashboardWidgets: ['sales_overview', 'team_performance', 'revenue_trends', 'customer_insights']\n    },\n    sales_executive: {\n      role: 'sales_executive',\n      department: 'sales',\n      hierarchy: 6,\n      displayName: 'Sales Executive',\n      description: 'Direct sales and customer relationship management',\n      color: '#96CEB4',\n      icon: 'fas fa-handshake',\n      permissions: [\n        { module: 'sales', actions: ['create', 'read', 'update'], scope: 'self' },\n        { module: 'customers', actions: ['create', 'read', 'update'], scope: 'self' },\n        { module: 'leads', actions: ['create', 'read', 'update'], scope: 'self' }\n      ],\n      profileLayout: 'specialist',\n      dashboardWidgets: ['my_sales', 'my_customers', 'targets', 'commission']\n    },\n    marketing_manager: {\n      role: 'marketing_manager',\n      department: 'marketing',\n      hierarchy: 3,\n      displayName: 'Marketing Manager',\n      description: 'Marketing strategy and campaign management',\n      color: '#F38BA8',\n      icon: 'fas fa-bullhorn',\n      permissions: [\n        { module: 'marketing', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },\n        { module: 'campaigns', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },\n        { module: 'content', actions: ['read', 'update'], scope: 'department' },\n        { module: 'analytics', actions: ['read'], scope: 'department' }\n      ],\n      profileLayout: 'manager',\n      dashboardWidgets: ['campaign_performance', 'audience_insights', 'content_metrics', 'roi_analysis']\n    },\n    marketing_executive: {\n      role: 'marketing_executive',\n      department: 'marketing',\n      hierarchy: 6,\n      displayName: 'Marketing Executive',\n      description: 'Campaign execution and content creation',\n      color: '#DDA0DD',\n      icon: 'fas fa-palette',\n      permissions: [\n        { module: 'campaigns', actions: ['create', 'read', 'update'], scope: 'self' },\n        { module: 'content', actions: ['create', 'read', 'update'], scope: 'self' },\n        { module: 'social_media', actions: ['create', 'read', 'update'], scope: 'self' }\n      ],\n      profileLayout: 'specialist',\n      dashboardWidgets: ['my_campaigns', 'content_calendar', 'social_metrics', 'creative_assets']\n    },\n    account_manager: {\n      role: 'account_manager',\n      department: 'accounting',\n      hierarchy: 4,\n      displayName: 'Account Manager',\n      description: 'Financial oversight and accounting management',\n      color: '#FFD93D',\n      icon: 'fas fa-calculator',\n      permissions: [\n        { module: 'accounting', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },\n        { module: 'financial_reports', actions: ['create', 'read'], scope: 'department' },\n        { module: 'budgets', actions: ['create', 'read', 'update'], scope: 'department' }\n      ],\n      profileLayout: 'manager',\n      dashboardWidgets: ['financial_overview', 'budget_tracking', 'expense_analysis', 'profit_margins']\n    },\n    accountant: {\n      role: 'accountant',\n      department: 'accounting',\n      hierarchy: 7,\n      displayName: 'Accountant',\n      description: 'Financial record keeping and transaction management',\n      color: '#6BCF7F',\n      icon: 'fas fa-file-invoice-dollar',\n      permissions: [\n        { module: 'transactions', actions: ['create', 'read', 'update'], scope: 'self' },\n        { module: 'invoices', actions: ['create', 'read', 'update'], scope: 'self' },\n        { module: 'expenses', actions: ['create', 'read', 'update'], scope: 'self' }\n      ],\n      profileLayout: 'specialist',\n      dashboardWidgets: ['daily_transactions', 'pending_invoices', 'expense_tracker', 'tax_summary']\n    },\n    support_manager: {\n      role: 'support_manager',\n      department: 'support',\n      hierarchy: 4,\n      displayName: 'Support Manager',\n      description: 'Customer support team leadership',\n      color: '#FF8C42',\n      icon: 'fas fa-headset',\n      permissions: [\n        { module: 'support', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },\n        { module: 'tickets', actions: ['read', 'update', 'assign'], scope: 'department' },\n        { module: 'knowledge_base', actions: ['create', 'read', 'update'], scope: 'department' }\n      ],\n      profileLayout: 'manager',\n      dashboardWidgets: ['support_overview', 'ticket_analytics', 'team_workload', 'satisfaction_scores']\n    },\n    support_agent: {\n      role: 'support_agent',\n      department: 'support',\n      hierarchy: 8,\n      displayName: 'Support Agent',\n      description: 'Direct customer support and issue resolution',\n      color: '#A8E6CF',\n      icon: 'fas fa-life-ring',\n      permissions: [\n        { module: 'tickets', actions: ['read', 'update'], scope: 'self' },\n        { module: 'knowledge_base', actions: ['read'], scope: 'department' },\n        { module: 'customer_communication', actions: ['create', 'read'], scope: 'self' }\n      ],\n      profileLayout: 'specialist',\n      dashboardWidgets: ['my_tickets', 'response_times', 'customer_feedback', 'knowledge_search']\n    },\n    content_manager: {\n      role: 'content_manager',\n      department: 'content',\n      hierarchy: 5,\n      displayName: 'Content Manager',\n      description: 'Content strategy and editorial oversight',\n      color: '#B19CD9',\n      icon: 'fas fa-edit',\n      permissions: [\n        { module: 'content', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },\n        { module: 'editorial', actions: ['create', 'read', 'update'], scope: 'department' },\n        { module: 'publishing', actions: ['approve', 'schedule'], scope: 'department' }\n      ],\n      profileLayout: 'manager',\n      dashboardWidgets: ['content_pipeline', 'editorial_calendar', 'engagement_metrics', 'seo_performance']\n    },\n    vendor_manager: {\n      role: 'vendor_manager',\n      department: 'vendor_management',\n      hierarchy: 5,\n      displayName: 'Vendor Manager',\n      description: 'Vendor relationship and partnership management',\n      color: '#FFB6C1',\n      icon: 'fas fa-store',\n      permissions: [\n        { module: 'vendors', actions: ['create', 'read', 'update', 'delete'], scope: 'department' },\n        { module: 'contracts', actions: ['create', 'read', 'update'], scope: 'department' },\n        { module: 'vendor_performance', actions: ['read', 'evaluate'], scope: 'department' }\n      ],\n      profileLayout: 'manager',\n      dashboardWidgets: ['vendor_overview', 'contract_status', 'performance_metrics', 'payment_tracking']\n    }\n  };\n\n  constructor() {}\n\n  setCurrentUserRole(role: UserRole): void {\n    this.currentUserRoleSubject.next(role);\n  }\n\n  getCurrentUserRole(): UserRole | null {\n    return this.currentUserRoleSubject.value;\n  }\n\n  getRoleConfig(role: UserRole): RoleConfig {\n    return this.roleConfigurations[role];\n  }\n\n  getAllRoles(): RoleConfig[] {\n    return Object.values(this.roleConfigurations);\n  }\n\n  getRolesByDepartment(department: Department): RoleConfig[] {\n    return Object.values(this.roleConfigurations)\n      .filter(config => config.department === department)\n      .sort((a, b) => a.hierarchy - b.hierarchy);\n  }\n\n  hasPermission(role: UserRole, module: string, action: string): boolean {\n    const config = this.getRoleConfig(role);\n    \n    // Super admin has all permissions\n    if (role === 'super_admin') return true;\n    \n    return config.permissions.some(permission => {\n      const moduleMatch = permission.module === '*' || permission.module === module;\n      const actionMatch = permission.actions.includes('*') || permission.actions.includes(action);\n      return moduleMatch && actionMatch;\n    });\n  }\n\n  getAccessibleModules(role: UserRole): string[] {\n    const config = this.getRoleConfig(role);\n    if (role === 'super_admin') return ['*'];\n    \n    return config.permissions.map(p => p.module);\n  }\n\n  isManager(role: UserRole): boolean {\n    return role.includes('_manager') || role === 'super_admin' || role === 'admin';\n  }\n\n  getSubordinateRoles(role: UserRole): UserRole[] {\n    const config = this.getRoleConfig(role);\n    return Object.values(this.roleConfigurations)\n      .filter(c => c.department === config.department && c.hierarchy > config.hierarchy)\n      .map(c => c.role);\n  }\n\n  getDepartmentHierarchy(department: Department): RoleConfig[] {\n    return this.getRolesByDepartment(department);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;;AAqDlD,OAAM,MAAOC,qBAAqB;EAuMhCC,YAAA;IAtMQ,KAAAC,sBAAsB,GAAG,IAAIH,eAAe,CAAkB,IAAI,CAAC;IACpE,KAAAI,gBAAgB,GAAG,IAAI,CAACD,sBAAsB,CAACE,YAAY,EAAE;IAEnD,KAAAC,kBAAkB,GAAiC;MAClEC,WAAW,EAAE;QACXC,IAAI,EAAE,aAAa;QACnBC,UAAU,EAAE,gBAAgB;QAC5BC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,qBAAqB;QAClCC,WAAW,EAAE,gCAAgC;QAC7CC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,cAAc;QACpBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,GAAG;UAAEC,OAAO,EAAE,CAAC,GAAG,CAAC;UAAEC,KAAK,EAAE;QAAQ,CAAE,CACjD;QACDC,aAAa,EAAE,OAAO;QACtBC,gBAAgB,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY;OAC/F;MACDC,KAAK,EAAE;QACLb,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,gBAAgB;QAC5BC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,2CAA2C;QACxDC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,oBAAoB;QAC1BC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,OAAO;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAQ,CAAE,EACrF;UAAEF,MAAM,EAAE,OAAO;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAQ,CAAE,EACjE;UAAEF,MAAM,EAAE,QAAQ;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAQ,CAAE,CACnE;QACDC,aAAa,EAAE,OAAO;QACtBC,gBAAgB,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,eAAe,EAAE,mBAAmB;OACzF;MACDE,aAAa,EAAE;QACbd,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAE,OAAO;QACnBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,oCAAoC;QACjDC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,OAAO;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACzF;UAAEF,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACzE;UAAEF,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EAC7D;UAAEF,MAAM,EAAE,MAAM;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,CACrE;QACDC,aAAa,EAAE,SAAS;QACxBC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB;OAC/F;MACDG,eAAe,EAAE;QACff,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,OAAO;QACnBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,iBAAiB;QAC9BC,WAAW,EAAE,mDAAmD;QAChEC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,kBAAkB;QACxBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,OAAO;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,EACzE;UAAEF,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,EAC7E;UAAEF,MAAM,EAAE,OAAO;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,CAC1E;QACDC,aAAa,EAAE,YAAY;QAC3BC,gBAAgB,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,YAAY;OACvE;MACDI,iBAAiB,EAAE;QACjBhB,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE,WAAW;QACvBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE,4CAA4C;QACzDC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EAC7F;UAAEF,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EAC7F;UAAEF,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACvE;UAAEF,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,MAAM,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,CAChE;QACDC,aAAa,EAAE,SAAS;QACxBC,gBAAgB,EAAE,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,cAAc;OAClG;MACDK,mBAAmB,EAAE;QACnBjB,IAAI,EAAE,qBAAqB;QAC3BC,UAAU,EAAE,WAAW;QACvBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,qBAAqB;QAClCC,WAAW,EAAE,yCAAyC;QACtDC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,EAC7E;UAAEF,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,EAC3E;UAAEF,MAAM,EAAE,cAAc;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,CACjF;QACDC,aAAa,EAAE,YAAY;QAC3BC,gBAAgB,EAAE,CAAC,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB;OAC3F;MACDM,eAAe,EAAE;QACflB,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,YAAY;QACxBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,iBAAiB;QAC9BC,WAAW,EAAE,+CAA+C;QAC5DC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,YAAY;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EAC9F;UAAEF,MAAM,EAAE,mBAAmB;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACjF;UAAEF,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,CAClF;QACDC,aAAa,EAAE,SAAS;QACxBC,gBAAgB,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB;OACjG;MACDO,UAAU,EAAE;QACVnB,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAE,YAAY;QACxBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,YAAY;QACzBC,WAAW,EAAE,qDAAqD;QAClEC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,4BAA4B;QAClCC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,cAAc;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,EAChF;UAAEF,MAAM,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,EAC5E;UAAEF,MAAM,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,CAC7E;QACDC,aAAa,EAAE,YAAY;QAC3BC,gBAAgB,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,aAAa;OAC9F;MACDQ,eAAe,EAAE;QACfpB,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,iBAAiB;QAC9BC,WAAW,EAAE,kCAAkC;QAC/CC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EAC3F;UAAEF,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACjF;UAAEF,MAAM,EAAE,gBAAgB;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,CACzF;QACDC,aAAa,EAAE,SAAS;QACxBC,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB;OAClG;MACDS,aAAa,EAAE;QACbrB,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,8CAA8C;QAC3DC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,kBAAkB;QACxBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,EACjE;UAAEF,MAAM,EAAE,gBAAgB;UAAEC,OAAO,EAAE,CAAC,MAAM,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACpE;UAAEF,MAAM,EAAE,wBAAwB;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;UAAEC,KAAK,EAAE;QAAM,CAAE,CACjF;QACDC,aAAa,EAAE,YAAY;QAC3BC,gBAAgB,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,kBAAkB;OAC3F;MACDU,eAAe,EAAE;QACftB,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,iBAAiB;QAC9BC,WAAW,EAAE,0CAA0C;QACvDC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,aAAa;QACnBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EAC3F;UAAEF,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACnF;UAAEF,MAAM,EAAE,YAAY;UAAEC,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,CAChF;QACDC,aAAa,EAAE,SAAS;QACxBC,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,iBAAiB;OACrG;MACDW,cAAc,EAAE;QACdvB,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE,mBAAmB;QAC/BC,SAAS,EAAE,CAAC;QACZC,WAAW,EAAE,gBAAgB;QAC7BC,WAAW,EAAE,gDAAgD;QAC7DC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,cAAc;QACpBC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EAC3F;UAAEF,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,EACnF;UAAEF,MAAM,EAAE,oBAAoB;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;UAAEC,KAAK,EAAE;QAAY,CAAE,CACrF;QACDC,aAAa,EAAE,SAAS;QACxBC,gBAAgB,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,kBAAkB;;KAErG;EAEc;EAEfY,kBAAkBA,CAACxB,IAAc;IAC/B,IAAI,CAACL,sBAAsB,CAAC8B,IAAI,CAACzB,IAAI,CAAC;EACxC;EAEA0B,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC/B,sBAAsB,CAACgC,KAAK;EAC1C;EAEAC,aAAaA,CAAC5B,IAAc;IAC1B,OAAO,IAAI,CAACF,kBAAkB,CAACE,IAAI,CAAC;EACtC;EAEA6B,WAAWA,CAAA;IACT,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACjC,kBAAkB,CAAC;EAC/C;EAEAkC,oBAAoBA,CAAC/B,UAAsB;IACzC,OAAO6B,MAAM,CAACC,MAAM,CAAC,IAAI,CAACjC,kBAAkB,CAAC,CAC1CmC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACjC,UAAU,KAAKA,UAAU,CAAC,CAClDkC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClC,SAAS,GAAGmC,CAAC,CAACnC,SAAS,CAAC;EAC9C;EAEAoC,aAAaA,CAACtC,IAAc,EAAEQ,MAAc,EAAE+B,MAAc;IAC1D,MAAML,MAAM,GAAG,IAAI,CAACN,aAAa,CAAC5B,IAAI,CAAC;IAEvC;IACA,IAAIA,IAAI,KAAK,aAAa,EAAE,OAAO,IAAI;IAEvC,OAAOkC,MAAM,CAAC3B,WAAW,CAACiC,IAAI,CAACC,UAAU,IAAG;MAC1C,MAAMC,WAAW,GAAGD,UAAU,CAACjC,MAAM,KAAK,GAAG,IAAIiC,UAAU,CAACjC,MAAM,KAAKA,MAAM;MAC7E,MAAMmC,WAAW,GAAGF,UAAU,CAAChC,OAAO,CAACmC,QAAQ,CAAC,GAAG,CAAC,IAAIH,UAAU,CAAChC,OAAO,CAACmC,QAAQ,CAACL,MAAM,CAAC;MAC3F,OAAOG,WAAW,IAAIC,WAAW;IACnC,CAAC,CAAC;EACJ;EAEAE,oBAAoBA,CAAC7C,IAAc;IACjC,MAAMkC,MAAM,GAAG,IAAI,CAACN,aAAa,CAAC5B,IAAI,CAAC;IACvC,IAAIA,IAAI,KAAK,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC;IAExC,OAAOkC,MAAM,CAAC3B,WAAW,CAACuC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvC,MAAM,CAAC;EAC9C;EAEAwC,SAASA,CAAChD,IAAc;IACtB,OAAOA,IAAI,CAAC4C,QAAQ,CAAC,UAAU,CAAC,IAAI5C,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,OAAO;EAChF;EAEAiD,mBAAmBA,CAACjD,IAAc;IAChC,MAAMkC,MAAM,GAAG,IAAI,CAACN,aAAa,CAAC5B,IAAI,CAAC;IACvC,OAAO8B,MAAM,CAACC,MAAM,CAAC,IAAI,CAACjC,kBAAkB,CAAC,CAC1CmC,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAACjD,UAAU,KAAKiC,MAAM,CAACjC,UAAU,IAAIiD,CAAC,CAAChD,SAAS,GAAGgC,MAAM,CAAChC,SAAS,CAAC,CACjF4C,GAAG,CAACI,CAAC,IAAIA,CAAC,CAAClD,IAAI,CAAC;EACrB;EAEAmD,sBAAsBA,CAAClD,UAAsB;IAC3C,OAAO,IAAI,CAAC+B,oBAAoB,CAAC/B,UAAU,CAAC;EAC9C;;;uBAhQWR,qBAAqB;IAAA;EAAA;;;aAArBA,qBAAqB;MAAA2D,OAAA,EAArB3D,qBAAqB,CAAA4D,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}