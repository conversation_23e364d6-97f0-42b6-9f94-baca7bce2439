{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Uses the compareWith param to compare two values to determine if they are equal.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst compareOptions = (currentValue, compareValue, compareWith) => {\n  if (typeof compareWith === 'function') {\n    return compareWith(currentValue, compareValue);\n  } else if (typeof compareWith === 'string') {\n    return currentValue[compareWith] === compareValue[compareWith];\n  } else {\n    return Array.isArray(compareValue) ? compareValue.includes(currentValue) : currentValue === compareValue;\n  }\n};\n/**\n * Compares a value against the current value(s) to determine if it is selected.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst isOptionSelected = (currentValue, compareValue, compareWith) => {\n  if (currentValue === undefined) {\n    return false;\n  }\n  if (Array.isArray(currentValue)) {\n    return currentValue.some(val => compareOptions(val, compareValue, compareWith));\n  } else {\n    return compareOptions(currentValue, compareValue, compareWith);\n  }\n};\nexport { compareOptions as c, isOptionSelected as i };", "map": {"version": 3, "names": ["compareOptions", "currentValue", "compareValue", "compareWith", "Array", "isArray", "includes", "isOptionSelected", "undefined", "some", "val", "c", "i"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/compare-with-utils-a96ff2ea.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Uses the compareWith param to compare two values to determine if they are equal.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst compareOptions = (currentValue, compareValue, compareWith) => {\n    if (typeof compareWith === 'function') {\n        return compareWith(currentValue, compareValue);\n    }\n    else if (typeof compareWith === 'string') {\n        return currentValue[compareWith] === compareValue[compareWith];\n    }\n    else {\n        return Array.isArray(compareValue) ? compareValue.includes(currentValue) : currentValue === compareValue;\n    }\n};\n/**\n * Compares a value against the current value(s) to determine if it is selected.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst isOptionSelected = (currentValue, compareValue, compareWith) => {\n    if (currentValue === undefined) {\n        return false;\n    }\n    if (Array.isArray(currentValue)) {\n        return currentValue.some((val) => compareOptions(val, compareValue, compareWith));\n    }\n    else {\n        return compareOptions(currentValue, compareValue, compareWith);\n    }\n};\n\nexport { compareOptions as c, isOptionSelected as i };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAGA,CAACC,YAAY,EAAEC,YAAY,EAAEC,WAAW,KAAK;EAChE,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IACnC,OAAOA,WAAW,CAACF,YAAY,EAAEC,YAAY,CAAC;EAClD,CAAC,MACI,IAAI,OAAOC,WAAW,KAAK,QAAQ,EAAE;IACtC,OAAOF,YAAY,CAACE,WAAW,CAAC,KAAKD,YAAY,CAACC,WAAW,CAAC;EAClE,CAAC,MACI;IACD,OAAOC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,CAACI,QAAQ,CAACL,YAAY,CAAC,GAAGA,YAAY,KAAKC,YAAY;EAC5G;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,gBAAgB,GAAGA,CAACN,YAAY,EAAEC,YAAY,EAAEC,WAAW,KAAK;EAClE,IAAIF,YAAY,KAAKO,SAAS,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,IAAIJ,KAAK,CAACC,OAAO,CAACJ,YAAY,CAAC,EAAE;IAC7B,OAAOA,YAAY,CAACQ,IAAI,CAAEC,GAAG,IAAKV,cAAc,CAACU,GAAG,EAAER,YAAY,EAAEC,WAAW,CAAC,CAAC;EACrF,CAAC,MACI;IACD,OAAOH,cAAc,CAACC,YAAY,EAAEC,YAAY,EAAEC,WAAW,CAAC;EAClE;AACJ,CAAC;AAED,SAASH,cAAc,IAAIW,CAAC,EAAEJ,gBAAgB,IAAIK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}