{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-a1a47f01.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst IonTextStyle0 = textCss;\nconst Text = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '4330b56cbc4e15953d9b3162fb40af728a8195dd',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: 'ec674a71d8fbb04d537fd79d617d9db4a607c340'\n    }));\n  }\n};\nText.style = IonTextStyle0;\nexport { Text as ion_text };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}