{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/cart.service\";\nimport * as i5 from \"src/app/core/services/wishlist.service\";\nimport * as i6 from \"src/app/core/services/social-media.service\";\nimport * as i7 from \"src/app/core/services/realtime.service\";\nimport * as i8 from \"src/app/core/services/button-actions.service\";\nimport * as i9 from \"@angular/common\";\nconst _c0 = [\"storiesSlider\"];\nconst _c1 = [\"storiesTrack\"];\nconst _c2 = [\"storyVideo\"];\nfunction ViewAddStoriesComponent_div_25_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_25_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", story_r4.products.length, \" item\", story_r4.products.length > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_25_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const story_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.openStory(story_r4, i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"div\", 28)(3, \"div\", 18);\n    i0.ɵɵelement(4, \"img\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ViewAddStoriesComponent_div_25_div_5_Template, 2, 0, \"div\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ViewAddStoriesComponent_div_25_div_8_Template, 2, 2, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-products\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", story_r4.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r5.currentStoryIndex)(\"completed\", i_r8 < ctx_r5.currentStoryIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r5.getProgressWidth(i_r8), \"%\");\n  }\n}\nfunction ViewAddStoriesComponent_div_28_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 63);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 64, 4);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_17_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.handleMiddleAreaClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \"View linked \", (tmp_6_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_6_0.linkedContent.type, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.getLinkedContentText());\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_div_click_0_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.openProductDetails(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"div\", 71);\n    i0.ɵɵelementStart(2, \"div\", 72);\n    i0.ɵɵelement(3, \"img\", 73);\n    i0.ɵɵelementStart(4, \"div\", 74)(5, \"span\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 76);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 77)(10, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_10_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.addToCart(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(11, \"i\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_12_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.addToWishlist(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(13, \"i\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_14_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      productTag_r11.product && ctx_r5.buyNow(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(15, \"i\", 83);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const productTag_r11 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", (productTag_r11.position == null ? null : productTag_r11.position.x) || 50, \"%\")(\"top\", (productTag_r11.position == null ? null : productTag_r11.position.y) || 50, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", productTag_r11.product.images[0].url || \"/assets/images/product-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", productTag_r11.product.name || \"Product\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r11.product.name || \"Product\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", productTag_r11.product.price || 0, \"\");\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_28_div_18_div_1_Template, 16, 8, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", (tmp_6_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_6_0.products);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_button_19_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      ctx_r5.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 85);\n    i0.ɵɵelementStart(2, \"span\", 86);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r5.showProductTags);\n    i0.ɵɵproperty(\"title\", \"View \" + ctx_r5.getProductCount() + \" product\" + (ctx_r5.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵattribute(\"aria-label\", \"View \" + ctx_r5.getProductCount() + \" product\" + (ctx_r5.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getProductCount());\n  }\n}\nfunction ViewAddStoriesComponent_div_28_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.previousStory());\n    });\n    i0.ɵɵelement(1, \"i\", 88);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_28_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.nextStory());\n    });\n    i0.ɵɵelement(1, \"i\", 90);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36, 3)(3, \"div\", 37);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_28_div_4_Template, 2, 6, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39);\n    i0.ɵɵelement(6, \"img\", 40);\n    i0.ɵɵelementStart(7, \"div\", 41)(8, \"span\", 42);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.closeStories());\n    });\n    i0.ɵɵelement(13, \"i\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_div_click_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_28_Template_div_touchstart_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_28_Template_div_touchmove_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_28_Template_div_touchend_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchEnd($event));\n    });\n    i0.ɵɵtemplate(15, ViewAddStoriesComponent_div_28_img_15_Template, 1, 1, \"img\", 47)(16, ViewAddStoriesComponent_div_28_video_16_Template, 2, 1, \"video\", 48)(17, ViewAddStoriesComponent_div_28_div_17_Template, 5, 3, \"div\", 49)(18, ViewAddStoriesComponent_div_28_div_18_Template, 2, 1, \"div\", 50)(19, ViewAddStoriesComponent_div_28_button_19_Template, 4, 5, \"button\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 52)(21, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.toggleLike());\n    });\n    i0.ɵɵelement(22, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.shareStory());\n    });\n    i0.ɵɵelement(24, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.saveStory());\n    });\n    i0.ɵɵelement(26, \"i\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, ViewAddStoriesComponent_div_28_button_27_Template, 2, 0, \"button\", 59)(28, ViewAddStoriesComponent_div_28_button_28_Template, 2, 0, \"button\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.stories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().user.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getCurrentStory().user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getTimeAgo(ctx_r5.getCurrentStory().createdAt));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_11_0.linkedContent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showProductTags && ((tmp_12_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_12_0.products));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.hasProducts());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r5.isLiked);\n    i0.ɵɵproperty(\"title\", ctx_r5.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r5.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex < ctx_r5.stories.length - 1);\n  }\n}\nexport let ViewAddStoriesComponent = /*#__PURE__*/(() => {\n  class ViewAddStoriesComponent {\n    constructor(router, http, authService, cartService, wishlistService, socialMediaService, realtimeService, cdr, buttonActionsService) {\n      this.router = router;\n      this.http = http;\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.socialMediaService = socialMediaService;\n      this.realtimeService = realtimeService;\n      this.cdr = cdr;\n      this.buttonActionsService = buttonActionsService;\n      this.currentUser = null;\n      this.stories = [];\n      this.isLoadingStories = true;\n      // Story viewer state\n      this.isOpen = false;\n      this.currentStoryIndex = 0;\n      this.showProductTags = false;\n      this.isLiked = false;\n      // Navigation state\n      this.canScrollLeft = false;\n      this.canScrollRight = false;\n      this.showNavArrows = true;\n      // Custom slider properties\n      this.translateX = 0;\n      this.itemWidth = 80; // Width of each story item including margin\n      this.visibleItems = 6; // Number of visible items\n      this.currentSlideIndex = 0;\n      this.storyDuration = 15000; // 15 seconds default\n      this.progressStartTime = 0;\n      this.currentProgress = 0; // Stable progress value for template binding\n      // Touch handling\n      this.touchStartTime = 0;\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.loadStories();\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n      });\n      // Check if we should show navigation arrows based on screen size\n      this.updateNavArrowsVisibility();\n    }\n    ngAfterViewInit() {\n      console.log('ngAfterViewInit called');\n      setTimeout(() => {\n        console.log('Initializing slider after view init');\n        this.calculateSliderDimensions();\n        this.updateScrollButtons();\n      }, 500);\n      // Also try immediate initialization\n      setTimeout(() => {\n        if (this.stories.length > 0) {\n          console.log('Re-initializing slider with stories');\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }\n      }, 1000);\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.clearStoryTimer();\n    }\n    onResize() {\n      this.updateNavArrowsVisibility();\n      this.calculateSliderDimensions();\n      this.updateScrollButtons();\n    }\n    updateNavArrowsVisibility() {\n      this.showNavArrows = window.innerWidth > 768;\n    }\n    calculateSliderDimensions() {\n      if (this.storiesSlider && this.storiesSlider.nativeElement) {\n        const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\n        const screenWidth = window.innerWidth;\n        console.log('Calculating slider dimensions:', {\n          containerWidth,\n          screenWidth\n        });\n        // Responsive visible items\n        if (screenWidth <= 600) {\n          this.visibleItems = 3;\n          this.itemWidth = containerWidth / 3;\n        } else if (screenWidth <= 900) {\n          this.visibleItems = 4;\n          this.itemWidth = containerWidth / 4;\n        } else {\n          this.visibleItems = 6;\n          this.itemWidth = containerWidth / 6;\n        }\n        console.log('Slider dimensions calculated:', {\n          visibleItems: this.visibleItems,\n          itemWidth: this.itemWidth\n        });\n        // Reset slider position if needed\n        this.currentSlideIndex = 0;\n        this.translateX = 0;\n      } else {\n        console.warn('Stories slider element not found, using default dimensions');\n        // Fallback dimensions\n        this.itemWidth = 100;\n        this.visibleItems = 6;\n      }\n    }\n    loadStories() {\n      this.isLoadingStories = true;\n      this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n        next: response => {\n          console.log('Stories API response:', response);\n          if (response.success && response.stories && response.stories.length > 0) {\n            this.stories = response.stories.map(story => ({\n              ...story,\n              mediaUrl: story.media?.url || story.mediaUrl,\n              mediaType: story.media?.type || story.mediaType\n            }));\n            console.log('Loaded stories from API:', this.stories);\n          } else {\n            console.log('No stories from API, loading fallback stories');\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n          setTimeout(() => {\n            this.calculateSliderDimensions();\n            this.updateScrollButtons();\n          }, 100);\n        },\n        error: error => {\n          console.error('Error loading stories:', error);\n          console.log('Loading fallback stories due to error');\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n          setTimeout(() => {\n            this.calculateSliderDimensions();\n            this.updateScrollButtons();\n          }, 100);\n        }\n      }));\n    }\n    loadFallbackStories() {\n      console.log('❌ No stories available from API');\n      this.stories = [];\n    }\n    // Navigation methods\n    scrollLeft() {\n      console.log('Scroll left clicked, current index:', this.currentSlideIndex);\n      if (this.currentSlideIndex > 0) {\n        this.currentSlideIndex--;\n        this.updateSliderPosition();\n        console.log('Scrolled left to index:', this.currentSlideIndex);\n      } else {\n        console.log('Cannot scroll left, already at start');\n      }\n    }\n    scrollRight() {\n      const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n      const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n      console.log('Scroll right clicked:', {\n        currentIndex: this.currentSlideIndex,\n        totalItems,\n        maxSlideIndex,\n        visibleItems: this.visibleItems\n      });\n      if (this.currentSlideIndex < maxSlideIndex) {\n        this.currentSlideIndex++;\n        this.updateSliderPosition();\n        console.log('Scrolled right to index:', this.currentSlideIndex);\n      } else {\n        console.log('Cannot scroll right, already at end');\n      }\n    }\n    updateSliderPosition() {\n      const newTranslateX = -this.currentSlideIndex * this.itemWidth;\n      console.log('Updating slider position:', {\n        currentIndex: this.currentSlideIndex,\n        itemWidth: this.itemWidth,\n        newTranslateX\n      });\n      this.translateX = newTranslateX;\n      this.updateScrollButtons();\n    }\n    updateScrollButtons() {\n      const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n      const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n      this.canScrollLeft = this.currentSlideIndex > 0;\n      this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\n      console.log('Updated scroll buttons:', {\n        canScrollLeft: this.canScrollLeft,\n        canScrollRight: this.canScrollRight,\n        totalItems,\n        maxSlideIndex,\n        currentIndex: this.currentSlideIndex\n      });\n    }\n    // Story viewer methods\n    openStory(_story, index) {\n      this.currentStoryIndex = index;\n      this.isOpen = true;\n      this.showProductTags = false;\n      this.startStoryTimer();\n      document.body.style.overflow = 'hidden';\n    }\n    closeStories() {\n      this.isOpen = false;\n      this.clearStoryTimer();\n      this.pauseAllVideos();\n      document.body.style.overflow = 'auto';\n    }\n    nextStory() {\n      if (this.currentStoryIndex < this.stories.length - 1) {\n        this.currentStoryIndex++;\n        this.startStoryTimer();\n      } else {\n        this.closeStories();\n      }\n    }\n    previousStory() {\n      if (this.currentStoryIndex > 0) {\n        this.currentStoryIndex--;\n        this.startStoryTimer();\n      }\n    }\n    getCurrentStory() {\n      return this.stories[this.currentStoryIndex] || this.stories[0];\n    }\n    hasProducts() {\n      const story = this.getCurrentStory();\n      return !!(story && story.products && story.products.length > 0);\n    }\n    getProductCount() {\n      const story = this.getCurrentStory();\n      return story?.products?.length || 0;\n    }\n    // Progress tracking\n    getProgressWidth(index) {\n      if (index < this.currentStoryIndex) return 100;\n      if (index > this.currentStoryIndex) return 0;\n      // Return the stable progress value for the current story\n      return this.currentProgress;\n    }\n    startStoryTimer() {\n      this.clearStoryTimer();\n      this.progressStartTime = Date.now();\n      this.currentProgress = 0;\n      // Update progress every 100ms for smooth animation\n      this.progressUpdateTimer = setInterval(() => {\n        if (this.progressStartTime) {\n          const elapsed = Date.now() - this.progressStartTime;\n          this.currentProgress = Math.min(elapsed / this.storyDuration * 100, 100);\n          this.cdr.detectChanges(); // Trigger change detection manually\n        }\n      }, 100);\n      this.progressTimer = setTimeout(() => {\n        this.nextStory();\n      }, this.storyDuration);\n    }\n    clearStoryTimer() {\n      if (this.progressTimer) {\n        clearTimeout(this.progressTimer);\n        this.progressTimer = null;\n      }\n      if (this.progressUpdateTimer) {\n        clearInterval(this.progressUpdateTimer);\n        this.progressUpdateTimer = null;\n      }\n      this.progressStartTime = 0;\n      this.currentProgress = 0;\n    }\n    // Story interaction methods\n    onStoryClick(event) {\n      const clickX = event.clientX;\n      const windowWidth = window.innerWidth;\n      if (clickX < windowWidth / 3) {\n        this.previousStory();\n      } else if (clickX > windowWidth * 2 / 3) {\n        this.nextStory();\n      }\n    }\n    // Touch handling\n    onTouchStart(_event) {\n      this.touchStartTime = Date.now();\n      this.longPressTimer = setTimeout(() => {\n        this.clearStoryTimer(); // Pause story progress on long press\n      }, 500);\n    }\n    onTouchMove(_event) {\n      if (this.longPressTimer) {\n        clearTimeout(this.longPressTimer);\n        this.longPressTimer = null;\n      }\n    }\n    onTouchEnd(event) {\n      if (this.longPressTimer) {\n        clearTimeout(this.longPressTimer);\n        this.longPressTimer = null;\n      }\n      const touchDuration = Date.now() - this.touchStartTime;\n      if (touchDuration < 500) {\n        // Short tap - treat as click\n        const touch = event.changedTouches[0];\n        this.onStoryClick({\n          clientX: touch.clientX\n        });\n      } else {\n        // Long press ended - resume story progress\n        this.startStoryTimer();\n      }\n    }\n    // Product interaction methods\n    toggleProductTags() {\n      this.showProductTags = !this.showProductTags;\n    }\n    openProductDetails(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    // Add product to cart with real-time functionality\n    addToCart(product) {\n      console.log('Adding product to cart:', product);\n      this.buttonActionsService.addToCart({\n        productId: product._id,\n        size: product.size,\n        color: product.color,\n        quantity: 1,\n        addedFrom: 'story'\n      }).subscribe({\n        next: result => {\n          if (result.success) {\n            console.log('Product added to cart successfully:', result);\n            // Show success message or toast\n          } else {\n            console.error('Failed to add product to cart:', result.message);\n          }\n        },\n        error: error => {\n          console.error('Error adding product to cart:', error);\n        }\n      });\n    }\n    // Add product to wishlist with real-time functionality\n    addToWishlist(product) {\n      console.log('Adding product to wishlist:', product);\n      this.buttonActionsService.addToWishlist({\n        productId: product._id,\n        size: product.size,\n        color: product.color,\n        addedFrom: 'story'\n      }).subscribe({\n        next: result => {\n          if (result.success) {\n            console.log('Product added to wishlist successfully:', result);\n            // Show success message or toast\n          } else {\n            console.error('Failed to add product to wishlist:', result.message);\n          }\n        },\n        error: error => {\n          console.error('Error adding product to wishlist:', error);\n        }\n      });\n    }\n    // Buy now functionality\n    buyNow(product) {\n      console.log('Buy now clicked for product:', product);\n      this.buttonActionsService.buyNow({\n        productId: product._id,\n        size: product.size,\n        color: product.color,\n        quantity: 1,\n        addedFrom: 'story'\n      }).subscribe({\n        next: result => {\n          if (result.success) {\n            console.log('Buy now successful:', result);\n            // Navigation to checkout will be handled by the service\n          } else {\n            console.error('Failed to buy now:', result.message);\n          }\n        },\n        error: error => {\n          console.error('Error in buy now:', error);\n        }\n      });\n    }\n    // Story actions with real-time functionality\n    toggleLike() {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return;\n      }\n      const currentStory = this.getCurrentStory();\n      this.isLiked = !this.isLiked;\n      // Call API to like/unlike story\n      const endpoint = this.isLiked ? 'like' : 'unlike';\n      this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\n        next: response => {\n          console.log(`Story ${endpoint}d successfully:`, response);\n          // Emit real-time event (using socket directly)\n          if (this.realtimeService.isConnected()) {\n            // For now, we'll track this locally until we add story-specific emit methods\n            console.log('Story liked event:', {\n              storyId: currentStory._id,\n              userId: this.authService.currentUserValue?._id,\n              liked: this.isLiked\n            });\n          }\n        },\n        error: error => {\n          console.error(`Error ${endpoint}ing story:`, error);\n          // Revert the like state on error\n          this.isLiked = !this.isLiked;\n        }\n      });\n    }\n    shareStory() {\n      const currentStory = this.getCurrentStory();\n      if (navigator.share) {\n        // Use native sharing if available\n        navigator.share({\n          title: `Story by ${currentStory.user.username}`,\n          text: currentStory.caption,\n          url: `${environment.frontendUrl}/story/${currentStory._id}`\n        }).then(() => {\n          console.log('Story shared successfully');\n          // Track share event\n          this.trackStoryShare(currentStory._id);\n        }).catch(error => {\n          console.error('Error sharing story:', error);\n        });\n      } else {\n        // Fallback: copy link to clipboard\n        const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\n        navigator.clipboard.writeText(shareUrl).then(() => {\n          console.log('Story link copied to clipboard');\n          this.trackStoryShare(currentStory._id);\n          // Show toast notification\n          this.showToast('Story link copied to clipboard!');\n        }).catch(error => {\n          console.error('Error copying to clipboard:', error);\n        });\n      }\n    }\n    saveStory() {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return;\n      }\n      const currentStory = this.getCurrentStory();\n      // Call API to save story\n      this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\n        next: response => {\n          console.log('Story saved successfully:', response);\n          this.showToast('Story saved to your collection!');\n          // Emit real-time event (using socket directly)\n          if (this.realtimeService.isConnected()) {\n            // For now, we'll track this locally until we add story-specific emit methods\n            console.log('Story saved event:', {\n              storyId: currentStory._id,\n              userId: this.authService.currentUserValue?._id\n            });\n          }\n        },\n        error: error => {\n          console.error('Error saving story:', error);\n          this.showToast('Error saving story. Please try again.');\n        }\n      });\n    }\n    trackStoryShare(storyId) {\n      if (this.authService.isAuthenticated) {\n        this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\n          next: response => {\n            console.log('Story share tracked:', response);\n          },\n          error: error => {\n            console.error('Error tracking story share:', error);\n          }\n        });\n      }\n    }\n    // Handle middle area click for product/category navigation\n    handleMiddleAreaClick(event) {\n      event.stopPropagation();\n      const currentStory = this.getCurrentStory();\n      if (!currentStory?.linkedContent) {\n        return;\n      }\n      console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\n      switch (currentStory.linkedContent.type) {\n        case 'product':\n          if (currentStory.linkedContent.productId) {\n            this.router.navigate(['/product', currentStory.linkedContent.productId]);\n          } else if (currentStory.products && currentStory.products.length > 0) {\n            // Fallback to first product if no specific productId\n            this.router.navigate(['/product', currentStory.products[0].product._id]);\n          }\n          break;\n        case 'category':\n          if (currentStory.linkedContent.categoryId) {\n            this.router.navigate(['/shop'], {\n              queryParams: {\n                category: currentStory.linkedContent.categoryId\n              }\n            });\n          }\n          break;\n        case 'brand':\n          if (currentStory.linkedContent.brandId) {\n            this.router.navigate(['/shop'], {\n              queryParams: {\n                brand: currentStory.linkedContent.brandId\n              }\n            });\n          }\n          break;\n        case 'collection':\n          if (currentStory.linkedContent.collectionId) {\n            this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\n          }\n          break;\n        default:\n          console.warn('Unknown linked content type:', currentStory.linkedContent.type);\n      }\n      // Track click event\n      this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\n    }\n    // Get text for linked content indicator\n    getLinkedContentText() {\n      const currentStory = this.getCurrentStory();\n      if (!currentStory?.linkedContent) {\n        return '';\n      }\n      switch (currentStory.linkedContent.type) {\n        case 'product':\n          return 'View Product';\n        case 'category':\n          return 'Browse Category';\n        case 'brand':\n          return 'View Brand';\n        case 'collection':\n          return 'View Collection';\n        default:\n          return 'View Details';\n      }\n    }\n    // Track linked content click for analytics\n    trackLinkedContentClick(storyId, linkedContent) {\n      if (this.authService.isAuthenticated) {\n        this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\n          contentType: linkedContent.type,\n          contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\n        }).subscribe({\n          next: response => {\n            console.log('Linked content click tracked:', response);\n          },\n          error: error => {\n            console.error('Error tracking linked content click:', error);\n          }\n        });\n      }\n    }\n    showToast(message) {\n      // Simple toast implementation - you can replace with your preferred toast library\n      const toast = document.createElement('div');\n      toast.textContent = message;\n      toast.style.cssText = `\n      position: fixed;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-size: 14px;\n    `;\n      document.body.appendChild(toast);\n      setTimeout(() => {\n        document.body.removeChild(toast);\n      }, 3000);\n    }\n    // Add story functionality\n    onAdd() {\n      this.router.navigate(['/stories/create']);\n    }\n    // Utility methods\n    getTimeAgo(dateString) {\n      if (!dateString) return 'Unknown';\n      const now = new Date();\n      let date;\n      if (typeof dateString === 'string') {\n        date = new Date(dateString);\n      } else {\n        date = dateString;\n      }\n      // Check if date is valid\n      if (isNaN(date.getTime())) {\n        return 'Unknown';\n      }\n      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n      if (diffInMinutes < 1) return 'now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m`;\n      const diffInHours = Math.floor(diffInMinutes / 60);\n      if (diffInHours < 24) return `${diffInHours}h`;\n      const diffInDays = Math.floor(diffInHours / 24);\n      return `${diffInDays}d`;\n    }\n    pauseAllVideos() {\n      const videos = document.querySelectorAll('video');\n      videos.forEach(video => {\n        if (video.pause) {\n          video.pause();\n        }\n      });\n    }\n    handleKeydown(event) {\n      if (!this.isOpen) return;\n      switch (event.key) {\n        case 'ArrowLeft':\n          this.previousStory();\n          break;\n        case 'ArrowRight':\n          this.nextStory();\n          break;\n        case 'Escape':\n          this.closeStories();\n          break;\n      }\n    }\n    static {\n      this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n        return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CartService), i0.ɵɵdirectiveInject(i5.WishlistService), i0.ɵɵdirectiveInject(i6.SocialMediaService), i0.ɵɵdirectiveInject(i7.RealtimeService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i8.ButtonActionsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ViewAddStoriesComponent,\n        selectors: [[\"app-view-add-stories\"]],\n        viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesTrack = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n          }\n        },\n        hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n              return ctx.onResize($event);\n            }, false, i0.ɵɵresolveWindow)(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n              return ctx.handleKeydown($event);\n            }, false, i0.ɵɵresolveDocument);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 29,\n        vars: 11,\n        consts: [[\"storiesContainer\", \"\"], [\"storiesSlider\", \"\"], [\"storiesTrack\", \"\"], [\"feedCover\", \"\"], [\"storyVideo\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [\"type\", \"button\", \"aria-label\", \"Create new story\", \"title\", \"Create a new story\", 1, \"create-story-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-plus\"], [1, \"stories-slider-wrapper\"], [\"type\", \"button\", \"aria-label\", \"Scroll stories left\", \"title\", \"Previous stories\", 1, \"nav-arrow\", \"nav-arrow-left\", 3, \"click\", \"disabled\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-chevron-left\"], [1, \"stories-slider\"], [1, \"stories-track\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus-btn\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"aria-label\", \"Scroll stories right\", \"title\", \"Next stories\", 1, \"nav-arrow\", \"nav-arrow-right\", 3, \"click\", \"disabled\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-chevron-right\"], [\"class\", \"stories-overlay\", 4, \"ngIf\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [\"class\", \"shopping-bag-indicator\", \"title\", \"Shoppable content\", 4, \"ngIf\"], [\"class\", \"product-count-badge\", 4, \"ngIf\"], [\"title\", \"Shoppable content\", 1, \"shopping-bag-indicator\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-count-badge\"], [1, \"stories-overlay\"], [1, \"stories-content\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [\"alt\", \"Avatar\", 1, \"story-header-avatar\", 3, \"src\"], [1, \"story-header-info\"], [1, \"username\"], [1, \"time\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-media\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [\"class\", \"story-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-video\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-middle-click-area\", 3, \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"shopping-bag-btn\", \"type\", \"button\", 3, \"active\", \"title\", \"click\", 4, \"ngIf\"], [1, \"story-actions\"], [\"type\", \"button\", 1, \"action-btn\", \"like-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"aria-label\", \"Share story\", \"title\", \"Share story\", 1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-share\"], [\"type\", \"button\", \"aria-label\", \"Save story\", \"title\", \"Save story\", 1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-bookmark\"], [\"class\", \"story-nav-btn story-nav-prev\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-nav-btn story-nav-next\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-image\", 3, \"src\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-video\", 3, \"src\"], [1, \"story-middle-click-area\", 3, \"click\", \"title\"], [1, \"middle-click-indicator\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-dot\"], [1, \"product-tag-info\"], [1, \"product-tag-image\", 3, \"src\", \"alt\"], [1, \"product-tag-details\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"product-tag-actions\"], [\"type\", \"button\", \"title\", \"Add to Cart\", 1, \"product-action-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [\"type\", \"button\", \"title\", \"Add to Wishlist\", 1, \"product-action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"title\", \"Buy Now\", 1, \"product-action-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [\"type\", \"button\", 1, \"shopping-bag-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-shopping-bag\"], [\"aria-hidden\", \"true\", 1, \"product-count\"], [1, \"story-nav-btn\", \"story-nav-prev\", 3, \"click\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"story-nav-btn\", \"story-nav-next\", 3, \"click\"], [1, \"fas\", \"fa-chevron-right\"]],\n        template: function ViewAddStoriesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 5, 0)(2, \"div\", 6)(3, \"h3\", 7);\n            i0.ɵɵtext(4, \"Stories\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_5_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onAdd());\n            });\n            i0.ɵɵelement(6, \"i\", 9);\n            i0.ɵɵelementStart(7, \"span\");\n            i0.ɵɵtext(8, \"Create\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(9, \"div\", 10)(10, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.scrollLeft());\n            });\n            i0.ɵɵelement(11, \"i\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 13, 1)(14, \"div\", 14, 2)(16, \"div\", 15);\n            i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_div_click_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onAdd());\n            });\n            i0.ɵɵelementStart(17, \"div\", 16)(18, \"div\", 17)(19, \"div\", 18);\n            i0.ɵɵelement(20, \"img\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\", 20);\n            i0.ɵɵelement(22, \"i\", 21);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 22);\n            i0.ɵɵtext(24, \"Your Story\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(25, ViewAddStoriesComponent_div_25_Template, 9, 7, \"div\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_26_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.scrollRight());\n            });\n            i0.ɵɵelement(27, \"i\", 25);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(28, ViewAddStoriesComponent_div_28_Template, 29, 15, \"div\", 26);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n            i0.ɵɵproperty(\"disabled\", !ctx.canScrollLeft);\n            i0.ɵɵadvance(4);\n            i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.translateX + \"px)\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n            i0.ɵɵproperty(\"disabled\", !ctx.canScrollRight);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          }\n        },\n        dependencies: [CommonModule, i9.NgForOf, i9.NgIf, FormsModule],\n        styles: [\".stories-container[_ngcontent-%COMP%]{padding:16px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;margin:16px;box-shadow:0 8px 32px #0000001a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);overflow:hidden}@media (max-width: 768px){.stories-container[_ngcontent-%COMP%]{margin:8px;padding:12px;border-radius:12px}}.stories-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;flex-wrap:wrap;gap:8px}.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%]{color:#fff;font-size:20px;font-weight:600;margin:0;text-shadow:0 2px 4px rgba(0,0,0,.3);flex:1;min-width:0}.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.3);border-radius:20px;padding:8px 16px;color:#fff;font-size:14px;font-weight:500;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:6px;white-space:nowrap;min-height:36px;flex-shrink:0}.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:0 4px 12px #0003}.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:focus{outline:2px solid rgba(255,255,255,.5);outline-offset:2px}.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}@media (max-width: 480px){.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]{padding:6px 12px;font-size:12px;min-height:32px}.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}@media (max-width: 768px){.stories-header[_ngcontent-%COMP%]{margin-bottom:12px}.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%]{font-size:18px}}@media (max-width: 480px){.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%]{font-size:16px}}.stories-slider-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;gap:12px}.nav-arrow[_ngcontent-%COMP%]{background:#ffffffe6;border:none;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 12px #00000026;z-index:10}.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled){background:#fff;transform:scale(1.1);box-shadow:0 6px 20px #0003}.nav-arrow[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.nav-arrow.hidden[_ngcontent-%COMP%]{display:none}.nav-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#333;font-size:14px}@media (max-width: 768px){.nav-arrow[_ngcontent-%COMP%]{display:none}}.stories-slider[_ngcontent-%COMP%]{flex:1;overflow:hidden;position:relative}.stories-track[_ngcontent-%COMP%]{display:flex;transition:transform .3s ease;will-change:transform}.story-item[_ngcontent-%COMP%]{text-align:center;cursor:pointer;transition:transform .3s ease;position:relative;flex-shrink:0;width:calc(16.6666666667% - 16px);margin:0 8px;box-sizing:border-box}.story-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px)}@media (max-width: 900px){.story-item[_ngcontent-%COMP%]{width:calc(25% - 12px);margin:0 6px}}@media (max-width: 600px){.story-item[_ngcontent-%COMP%]{width:calc(33.3333333333% - 12px);margin:0 6px}}.story-avatar-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;position:relative}.story-avatar[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);padding:3px;display:flex;align-items:center;justify-content:center;transition:all .3s ease;position:relative}.story-avatar.has-products[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff6b35,#f7931e,#ff6b35,#f7931e);background-size:200% 200%;animation:_ngcontent-%COMP%_gradientShift 3s ease-in-out infinite;border:3px solid transparent;background-clip:padding-box}.story-avatar.has-products[_ngcontent-%COMP%]:hover{animation-duration:1.5s;transform:scale(1.05)}@media (max-width: 768px){.story-avatar[_ngcontent-%COMP%]{width:60px;height:60px}}@keyframes _ngcontent-%COMP%_gradientShift{0%{background-position:0% 50%}50%{background-position:100% 50%}to{background-position:0% 50%}}.story-avatar-inner[_ngcontent-%COMP%]{width:64px;height:64px;border-radius:50%;background:#fff;display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative}@media (max-width: 768px){.story-avatar-inner[_ngcontent-%COMP%]{width:54px;height:54px}}.story-avatar-img[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;object-fit:cover}.shopping-bag-indicator[_ngcontent-%COMP%]{position:absolute;bottom:-6px;right:-6px;background:#ff6b35;border-radius:50%;width:26px;height:26px;display:flex;align-items:center;justify-content:center;border:3px solid white;box-shadow:0 2px 12px #ff6b354d;cursor:pointer;z-index:2;transition:all .3s ease}.shopping-bag-indicator[_ngcontent-%COMP%]:hover{transform:scale(1.1);box-shadow:0 4px 16px #ff6b3566}.shopping-bag-indicator[_ngcontent-%COMP%]:active{transform:scale(.95)}.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:12px;font-weight:600}@media (max-width: 768px){.shopping-bag-indicator[_ngcontent-%COMP%]{width:22px;height:22px;bottom:-4px;right:-4px;border-width:2px}.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}@media (max-width: 480px){.shopping-bag-indicator[_ngcontent-%COMP%]{width:18px;height:18px;bottom:-3px;right:-3px}.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:8px}}.story-username[_ngcontent-%COMP%]{margin-top:8px;font-size:12px;font-weight:500;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:80px;text-shadow:0 1px 2px rgba(0,0,0,.3)}@media (max-width: 768px){.story-username[_ngcontent-%COMP%]{font-size:11px;max-width:70px}}.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{color:#0095f6;font-weight:600;text-shadow:none}.product-count-badge[_ngcontent-%COMP%]{position:absolute;bottom:-8px;left:50%;transform:translate(-50%);background:#ffffffe6;color:#333;font-size:10px;font-weight:600;padding:2px 6px;border-radius:10px;white-space:nowrap;box-shadow:0 2px 4px #0000001a}@media (max-width: 768px){.product-count-badge[_ngcontent-%COMP%]{font-size:9px;padding:1px 4px}}.add-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f9f9f9,#e8e8e8);border:2px dashed #c7c7c7;position:relative}.add-avatar[_ngcontent-%COMP%]:hover{border-color:#0095f6;background:linear-gradient(135deg,#f0f8ff,#e6f3ff)}.add-avatar[_ngcontent-%COMP%]   .story-avatar-img[_ngcontent-%COMP%]{opacity:.8;filter:grayscale(20%)}.add-story-plus-btn[_ngcontent-%COMP%]{position:absolute;bottom:-6px;right:-6px;width:28px;height:28px;background:#0095f6;border:3px solid white;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:2;transition:all .3s ease;box-shadow:0 2px 12px #0095f64d}.add-story-plus-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);box-shadow:0 4px 16px #0095f666}.add-story-plus-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:14px;font-weight:600}@media (max-width: 768px){.add-story-plus-btn[_ngcontent-%COMP%]{width:24px;height:24px;bottom:-4px;right:-4px;border-width:2px}.add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 480px){.add-story-plus-btn[_ngcontent-%COMP%]{width:20px;height:20px;bottom:-3px;right:-3px}.add-story-plus-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}.stories-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,#000000e6,#000000f2);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:1000;display:flex;align-items:center;justify-content:center;animation:_ngcontent-%COMP%_fadeIn .3s ease}@media (max-width: 768px){.stories-overlay[_ngcontent-%COMP%]{background:#000}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}.stories-content[_ngcontent-%COMP%]{position:relative;width:90%;max-width:400px;height:80vh;max-height:700px;background:#000;border-radius:16px;overflow:hidden;box-shadow:0 20px 60px #00000080;animation:_ngcontent-%COMP%_slideUp .3s ease}@media (max-width: 768px){.stories-content[_ngcontent-%COMP%]{width:100%;height:100vh;max-height:none;border-radius:0}}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(50px) scale(.9)}to{opacity:1;transform:translateY(0) scale(1)}}.progress-container[_ngcontent-%COMP%]{position:absolute;top:8px;left:8px;right:8px;display:flex;gap:4px;z-index:10}.progress-bar[_ngcontent-%COMP%]{flex:1;height:3px;background:#ffffff4d;border-radius:2px;overflow:hidden}.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_progressFill 5s linear}.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{width:100%!important}.progress-fill[_ngcontent-%COMP%]{height:100%;background:#fff;border-radius:2px;transition:width .1s ease}@keyframes _ngcontent-%COMP%_progressFill{0%{width:0%}to{width:100%}}.story-header[_ngcontent-%COMP%]{position:absolute;top:0;left:0;right:0;display:flex;align-items:center;padding:16px;background:linear-gradient(180deg,rgba(0,0,0,.8) 0%,transparent 100%);color:#fff;z-index:10}@media (max-width: 768px){.story-header[_ngcontent-%COMP%]{padding:12px}}.story-header-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;margin-right:12px;border:2px solid rgba(255,255,255,.3)}@media (max-width: 768px){.story-header-avatar[_ngcontent-%COMP%]{width:36px;height:36px}}.story-header-info[_ngcontent-%COMP%]{flex:1}.username[_ngcontent-%COMP%]{display:block;font-weight:600;font-size:16px;margin-bottom:2px}@media (max-width: 768px){.username[_ngcontent-%COMP%]{font-size:14px}}.time[_ngcontent-%COMP%]{font-size:12px;color:#ffffffb3;font-weight:400}.close-btn[_ngcontent-%COMP%]{background:#fff3;border:none;border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;color:#fff;cursor:pointer;transition:all .3s ease}.close-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:scale(1.1)}.close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}@media (max-width: 768px){.close-btn[_ngcontent-%COMP%]{width:32px;height:32px}.close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}}.story-media[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;background:#000;display:flex;align-items:center;justify-content:center;overflow:hidden}.story-middle-click-area[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:5;cursor:pointer;transition:all .3s ease}.story-middle-click-area[_ngcontent-%COMP%]:hover{transform:translate(-50%,-50%) scale(1.1)}.middle-click-indicator[_ngcontent-%COMP%]{background:#000000b3;color:#fff;padding:12px 20px;border-radius:25px;display:flex;align-items:center;gap:8px;font-size:14px;font-weight:500;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:2px solid rgba(255,255,255,.2);box-shadow:0 4px 20px #0000004d;animation:_ngcontent-%COMP%_pulseIndicator 2s infinite}.middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.middle-click-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{white-space:nowrap}@media (max-width: 768px){.middle-click-indicator[_ngcontent-%COMP%]{padding:10px 16px;font-size:12px}.middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}@keyframes _ngcontent-%COMP%_pulseIndicator{0%{box-shadow:0 4px 20px #0000004d}50%{box-shadow:0 4px 20px #fff3}to{box-shadow:0 4px 20px #0000004d}}.story-image[_ngcontent-%COMP%], .story-video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;display:block}.product-tags[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.product-tag[_ngcontent-%COMP%]{position:absolute;pointer-events:all;cursor:pointer;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.1)}}.product-tag-dot[_ngcontent-%COMP%]{width:20px;height:20px;background:#fff;border-radius:50%;border:3px solid #667eea;position:relative;animation:_ngcontent-%COMP%_ripple 2s infinite}.product-tag-dot[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:8px;height:8px;background:#667eea;border-radius:50%}@keyframes _ngcontent-%COMP%_ripple{0%{box-shadow:0 0 #667eeab3}70%{box-shadow:0 0 0 10px #667eea00}to{box-shadow:0 0 #667eea00}}.product-tag-info[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);background:#000000e6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:12px;padding:12px;display:flex;align-items:center;gap:12px;min-width:200px;border:1px solid rgba(255,255,255,.1)}.product-tag-image[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:8px;object-fit:cover}.product-tag-details[_ngcontent-%COMP%]{flex:1;color:#fff}.product-tag-name[_ngcontent-%COMP%]{display:block;font-weight:600;font-size:14px;margin-bottom:4px;line-height:1.2}.product-tag-price[_ngcontent-%COMP%]{display:block;color:#667eea;font-weight:700;font-size:16px;margin-bottom:8px}.product-tag-actions[_ngcontent-%COMP%]{display:flex;gap:6px;justify-content:flex-start}.product-action-btn[_ngcontent-%COMP%]{background:#ffffffe6;border:none;border-radius:50%;width:32px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;color:#333;box-shadow:0 2px 8px #0000001a}.product-action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);box-shadow:0 4px 12px #0003}.product-action-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.product-action-btn.cart-btn[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff}.product-action-btn.wishlist-btn[_ngcontent-%COMP%]:hover{background:#f5576c;color:#fff}.product-action-btn.buy-btn[_ngcontent-%COMP%]:hover{background:#28a745;color:#fff}.product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}@media (max-width: 768px){.product-action-btn[_ngcontent-%COMP%]{width:28px;height:28px}.product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}.shopping-bag-btn[_ngcontent-%COMP%]{position:absolute;bottom:20px;right:20px;background:linear-gradient(135deg,#667eea,#764ba2);border:none;border-radius:50%;width:56px;height:56px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;box-shadow:0 8px 25px #667eea66;z-index:5}.shopping-bag-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);box-shadow:0 12px 35px #667eea99}.shopping-bag-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.shopping-bag-btn[_ngcontent-%COMP%]:focus{outline:2px solid rgba(255,255,255,.8);outline-offset:2px}.shopping-bag-btn.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c);animation:_ngcontent-%COMP%_bounce .6s ease}.shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:20px}.shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#f5576c;color:#fff;border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:600;border:2px solid white;min-width:24px}@media (max-width: 768px){.shopping-bag-btn[_ngcontent-%COMP%]{width:48px;height:48px;bottom:16px;right:16px}.shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px}.shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{width:20px;height:20px;font-size:10px;min-width:20px;top:-6px;right:-6px}}@media (max-width: 480px){.shopping-bag-btn[_ngcontent-%COMP%]{width:44px;height:44px;bottom:12px;right:12px}.shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{width:18px;height:18px;font-size:9px;min-width:18px;top:-5px;right:-5px}}@media (max-height: 600px){.shopping-bag-btn[_ngcontent-%COMP%]{bottom:10px;right:10px}}@keyframes _ngcontent-%COMP%_bounce{0%,20%,60%,to{transform:translateY(0)}40%{transform:translateY(-10px)}80%{transform:translateY(-5px)}}.story-actions[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;display:flex;flex-direction:column;gap:16px;z-index:5}@media (max-width: 768px){.story-actions[_ngcontent-%COMP%]{bottom:16px;left:16px;gap:12px}}@media (max-width: 480px){.story-actions[_ngcontent-%COMP%]{bottom:12px;left:12px;gap:10px}}@media (max-width: 768px) and (max-height: 600px){.story-actions[_ngcontent-%COMP%]{bottom:10px;left:10px;gap:8px}}.action-btn[_ngcontent-%COMP%]{background:#fff3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.3);border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;color:#fff}.action-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:scale(1.1)}.action-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.action-btn[_ngcontent-%COMP%]:focus{outline:2px solid rgba(255,255,255,.6);outline-offset:2px}.action-btn.liked[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c);color:#fff;animation:_ngcontent-%COMP%_heartBeat .6s ease}.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px}@media (max-width: 768px){.action-btn[_ngcontent-%COMP%]{width:40px;height:40px}.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}}@media (max-width: 480px){.action-btn[_ngcontent-%COMP%]{width:36px;height:36px}.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}}@media (max-width: 768px){.action-btn[_ngcontent-%COMP%]{min-width:44px;min-height:44px}}@keyframes _ngcontent-%COMP%_heartBeat{0%,to{transform:scale(1)}50%{transform:scale(1.2)}}.story-nav-btn[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);background:#ffffff1a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);border-radius:50%;width:48px;height:48px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;color:#fff;z-index:5}.story-nav-btn[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-50%) scale(1.1)}.story-nav-btn.story-nav-prev[_ngcontent-%COMP%]{left:20px}.story-nav-btn.story-nav-next[_ngcontent-%COMP%]{right:20px}.story-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px}@media (max-width: 768px){.story-nav-btn[_ngcontent-%COMP%]{display:none}}@media (max-width: 768px){.stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%]{font-size:18px}.stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]{padding:6px 12px;font-size:12px}.product-tag-info[_ngcontent-%COMP%]{min-width:160px;padding:8px}.product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%]{width:40px;height:40px}.product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%]{font-size:12px}.product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%]{font-size:14px}.stories-content[_ngcontent-%COMP%]   .shopping-bag-btn[_ngcontent-%COMP%]:not(:only-child){right:16px;bottom:80px}}@media (max-width: 480px){.stories-container[_ngcontent-%COMP%]{margin:4px;padding:8px;border-radius:8px}.product-tag-info[_ngcontent-%COMP%]{min-width:140px;padding:6px;bottom:20px}.product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%]{width:35px;height:35px}.product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%]{font-size:11px}.product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%]{font-size:13px}}@media (max-width: 768px) and (orientation: landscape){.stories-content[_ngcontent-%COMP%]{height:100vh;max-height:none}.shopping-bag-btn[_ngcontent-%COMP%]{bottom:10px;right:10px}.story-actions[_ngcontent-%COMP%]{bottom:10px;left:10px;gap:8px}}.story-item.loading[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.story-item[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .shopping-bag-btn[_ngcontent-%COMP%]:focus, .nav-arrow[_ngcontent-%COMP%]:focus{outline:2px solid #667eea;outline-offset:2px}@media (prefers-contrast: high){.story-avatar[_ngcontent-%COMP%]{border:2px solid #000}.shopping-bag-btn[_ngcontent-%COMP%]{border:2px solid #fff}}\"]\n      });\n    }\n  }\n  return ViewAddStoriesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}