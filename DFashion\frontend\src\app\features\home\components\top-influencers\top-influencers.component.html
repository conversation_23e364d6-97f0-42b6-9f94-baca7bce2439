<div class="top-influencers-container">
  <!-- Header -->
  <div class="section-header">
    <h2 class="section-title">
      <i class="fas fa-crown"></i>
      Top Fashion Influencers
    </h2>
    <button class="view-all-btn" (click)="router.navigate(['/influencers'])">
      View All
      <i class="fas fa-arrow-right"></i>
    </button>
  </div>

  <!-- Category Filter -->
  <div class="category-filter">
    <button class="category-btn" 
            *ngFor="let category of categories"
            [class.active]="selectedCategory === category.value"
            (click)="onCategoryChange(category.value)">
      <i [class]="getCategoryIcon(category.value)" 
         [style.color]="getCategoryColor(category.value)"></i>
      {{ category.label }}
    </button>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading && influencers.length === 0">
    <div class="loading-grid">
      <div class="influencer-skeleton" *ngFor="let item of [1,2,3,4,5,6]">
        <div class="skeleton-avatar"></div>
        <div class="skeleton-content">
          <div class="skeleton-line"></div>
          <div class="skeleton-line short"></div>
          <div class="skeleton-line"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="error && influencers.length === 0">
    <div class="error-content">
      <i class="fas fa-exclamation-triangle"></i>
      <h3>Unable to load top influencers</h3>
      <p>{{ error }}</p>
      <button class="retry-btn" (click)="retry()">
        <i class="fas fa-redo"></i>
        Try Again
      </button>
    </div>
  </div>

  <!-- Influencers Grid -->
  <div class="influencers-grid" *ngIf="influencers.length > 0">
    <div class="influencer-card" 
         *ngFor="let influencer of influencers; trackBy: trackByInfluencerId; let i = index"
         (click)="viewInfluencer(influencer)"
         [class.top-rank]="i < 3">
      
      <!-- Rank Badge -->
      <div class="rank-badge" *ngIf="i < 3">
        <span class="rank-number">#{{ i + 1 }}</span>
        <i class="fas fa-crown" *ngIf="i === 0"></i>
        <i class="fas fa-medal" *ngIf="i === 1"></i>
        <i class="fas fa-award" *ngIf="i === 2"></i>
      </div>

      <!-- Influencer Avatar -->
      <div class="influencer-avatar-container">
        <img [src]="influencer.avatar" 
             [alt]="influencer.fullName"
             class="influencer-avatar"
             loading="lazy">
        
        <!-- Verified Badge -->
        <div class="verified-badge" *ngIf="influencer.isInfluencer">
          <i class="fas fa-check-circle"></i>
        </div>

        <!-- Category Badge -->
        <div class="category-badge" 
             [style.background-color]="getCategoryColor(influencer.influencerStats.category)">
          <i [class]="getCategoryIcon(influencer.influencerStats.category)"></i>
        </div>
      </div>

      <!-- Influencer Info -->
      <div class="influencer-info">
        <!-- Name and Username -->
        <h3 class="influencer-name">{{ influencer.fullName }}</h3>
        <p class="influencer-username">{{ '@' + influencer.username }}</p>

        <!-- Bio -->
        <p class="influencer-bio">{{ influencer.bio }}</p>

        <!-- Stats -->
        <div class="influencer-stats">
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(influencer.socialStats.followersCount) }}</span>
            <span class="stat-label">Followers</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(influencer.socialStats.postsCount) }}</span>
            <span class="stat-label">Posts</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ influencer.influencerStats.engagementRate }}%</span>
            <span class="stat-label">Engagement</span>
          </div>
        </div>

        <!-- Performance Metrics -->
        <div class="performance-metrics">
          <div class="metric-item">
            <i class="fas fa-heart"></i>
            <span>{{ formatNumber(influencer.influencerStats.averageLikes) }} avg likes</span>
          </div>
          <div class="metric-item">
            <i class="fas fa-eye"></i>
            <span>{{ formatNumber(influencer.influencerStats.averageViews) }} avg views</span>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button class="follow-btn" 
                  (click)="followInfluencer(influencer, $event)">
            <i class="fas fa-plus"></i>
            Follow
          </button>
          <button class="share-btn" 
                  (click)="shareInfluencer(influencer, $event)">
            <i class="fas fa-share"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Load More Button -->
  <div class="load-more-container" *ngIf="hasMore">
    <button class="load-more-btn" 
            (click)="loadMore()"
            [disabled]="isLoading">
      <span *ngIf="!isLoading">Load More Influencers</span>
      <span *ngIf="isLoading">
        <i class="fas fa-spinner fa-spin"></i>
        Loading...
      </span>
    </button>
  </div>

  <!-- Empty State -->
  <div class="empty-container" *ngIf="influencers.length === 0 && !isLoading && !error">
    <div class="empty-content">
      <i class="fas fa-users"></i>
      <h3>No influencers found</h3>
      <p>Try selecting a different category or check back later!</p>
      <button class="browse-btn" (click)="onCategoryChange('')">
        View All Categories
      </button>
    </div>
  </div>
</div>
