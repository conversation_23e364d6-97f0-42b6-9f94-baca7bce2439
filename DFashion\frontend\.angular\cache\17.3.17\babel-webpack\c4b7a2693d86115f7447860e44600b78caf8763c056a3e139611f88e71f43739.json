{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nexport class PermissionGuard {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n  }\n  canActivate(route, state) {\n    const requiredPermission = route.data?.['permission'];\n    const requiredRole = route.data?.['role'];\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/admin/login']);\n      return false;\n    }\n    // Check role-based access\n    if (requiredRole) {\n      const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];\n      if (!this.authService.hasRole(allowedRoles)) {\n        this.showAccessDeniedMessage('You do not have the required role to access this page.');\n        this.router.navigate(['/admin/dashboard']);\n        return false;\n      }\n    }\n    // Check permission-based access\n    if (requiredPermission) {\n      const [module, action] = requiredPermission.split(':');\n      if (!this.authService.hasPermission(module, action)) {\n        this.showAccessDeniedMessage('You do not have permission to access this page.');\n        this.router.navigate(['/admin/dashboard']);\n        return false;\n      }\n    }\n    return true;\n  }\n  showAccessDeniedMessage(message) {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  static {\n    this.ɵfac = function PermissionGuard_Factory(t) {\n      return new (t || PermissionGuard)(i0.ɵɵinject(i1.AdminAuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PermissionGuard,\n      factory: PermissionGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["PermissionGuard", "constructor", "authService", "router", "snackBar", "canActivate", "route", "state", "requiredPermission", "data", "requiredRole", "isAuthenticated", "navigate", "allowedRoles", "Array", "isArray", "hasRole", "showAccessDeniedMessage", "module", "action", "split", "hasPermission", "message", "open", "duration", "panelClass", "i0", "ɵɵinject", "i1", "AdminAuthService", "i2", "Router", "i3", "MatSnackBar", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\admin\\guards\\permission.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { AdminAuthService } from '../services/admin-auth.service';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PermissionGuard implements CanActivate {\n\n  constructor(\n    private authService: AdminAuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    const requiredPermission = route.data?.['permission'];\n    const requiredRole = route.data?.['role'];\n\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/admin/login']);\n      return false;\n    }\n\n    // Check role-based access\n    if (requiredRole) {\n      const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];\n      if (!this.authService.hasRole(allowedRoles)) {\n        this.showAccessDeniedMessage('You do not have the required role to access this page.');\n        this.router.navigate(['/admin/dashboard']);\n        return false;\n      }\n    }\n\n    // Check permission-based access\n    if (requiredPermission) {\n      const [module, action] = requiredPermission.split(':');\n      if (!this.authService.hasPermission(module, action)) {\n        this.showAccessDeniedMessage('You do not have permission to access this page.');\n        this.router.navigate(['/admin/dashboard']);\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  private showAccessDeniedMessage(message: string): void {\n    this.snackBar.open(message, 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n}\n"], "mappings": ";;;;AASA,OAAM,MAAOA,eAAe;EAE1BC,YACUC,WAA6B,EAC7BC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;EACf;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B,MAAMC,kBAAkB,GAAGF,KAAK,CAACG,IAAI,GAAG,YAAY,CAAC;IACrD,MAAMC,YAAY,GAAGJ,KAAK,CAACG,IAAI,GAAG,MAAM,CAAC;IAEzC;IACA,IAAI,CAAC,IAAI,CAACP,WAAW,CAACS,eAAe,EAAE,EAAE;MACvC,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACtC,OAAO,KAAK;;IAGd;IACA,IAAIF,YAAY,EAAE;MAChB,MAAMG,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACL,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;MAChF,IAAI,CAAC,IAAI,CAACR,WAAW,CAACc,OAAO,CAACH,YAAY,CAAC,EAAE;QAC3C,IAAI,CAACI,uBAAuB,CAAC,wDAAwD,CAAC;QACtF,IAAI,CAACd,MAAM,CAACS,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC1C,OAAO,KAAK;;;IAIhB;IACA,IAAIJ,kBAAkB,EAAE;MACtB,MAAM,CAACU,MAAM,EAAEC,MAAM,CAAC,GAAGX,kBAAkB,CAACY,KAAK,CAAC,GAAG,CAAC;MACtD,IAAI,CAAC,IAAI,CAAClB,WAAW,CAACmB,aAAa,CAACH,MAAM,EAAEC,MAAM,CAAC,EAAE;QACnD,IAAI,CAACF,uBAAuB,CAAC,iDAAiD,CAAC;QAC/E,IAAI,CAACd,MAAM,CAACS,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC1C,OAAO,KAAK;;;IAIhB,OAAO,IAAI;EACb;EAEQK,uBAAuBA,CAACK,OAAe;IAC7C,IAAI,CAAClB,QAAQ,CAACmB,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;MACnCE,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;;;uBAlDWzB,eAAe,EAAA0B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAfjC,eAAe;MAAAkC,OAAA,EAAflC,eAAe,CAAAmC,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}