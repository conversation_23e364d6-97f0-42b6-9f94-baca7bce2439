{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { K as Keyboard } from './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst KEYBOARD_DID_OPEN = 'ionKeyboardDidShow';\nconst KEYBOARD_DID_CLOSE = 'ionKeyboardDidHide';\nconst KEYBOARD_THRESHOLD = 150;\n// TODO(FW-2832): types\nlet previousVisualViewport = {};\nlet currentVisualViewport = {};\nlet keyboardOpen = false;\n/**\n * This is only used for tests\n */\nconst resetKeyboardAssist = () => {\n  previousVisualViewport = {};\n  currentVisualViewport = {};\n  keyboardOpen = false;\n};\nconst startKeyboardAssist = win => {\n  const nativeEngine = Keyboard.getEngine();\n  /**\n   * If the native keyboard plugin is available\n   * then we are running in a native environment. As a result\n   * we should only listen on the native events instead of\n   * using the Visual Viewport as the Ionic webview manipulates\n   * how it resizes such that the Visual Viewport API is not\n   * reliable here.\n   */\n  if (nativeEngine) {\n    startNativeListeners(win);\n  } else {\n    if (!win.visualViewport) {\n      return;\n    }\n    currentVisualViewport = copyVisualViewport(win.visualViewport);\n    win.visualViewport.onresize = () => {\n      trackViewportChanges(win);\n      if (keyboardDidOpen() || keyboardDidResize(win)) {\n        setKeyboardOpen(win);\n      } else if (keyboardDidClose(win)) {\n        setKeyboardClose(win);\n      }\n    };\n  }\n};\n/**\n * Listen for events fired by native keyboard plugin\n * in Capacitor/Cordova so devs only need to listen\n * in one place.\n */\nconst startNativeListeners = win => {\n  win.addEventListener('keyboardDidShow', ev => setKeyboardOpen(win, ev));\n  win.addEventListener('keyboardDidHide', () => setKeyboardClose(win));\n};\nconst setKeyboardOpen = (win, ev) => {\n  fireKeyboardOpenEvent(win, ev);\n  keyboardOpen = true;\n};\nconst setKeyboardClose = win => {\n  fireKeyboardCloseEvent(win);\n  keyboardOpen = false;\n};\n/**\n * Returns `true` if the `keyboardOpen` flag is not\n * set, the previous visual viewport width equal the current\n * visual viewport width, and if the scaled difference\n * of the previous visual viewport height minus the current\n * visual viewport height is greater than KEYBOARD_THRESHOLD\n *\n * We need to be able to accommodate users who have zooming\n * enabled in their browser (or have zoomed in manually) which\n * is why we take into account the current visual viewport's\n * scale value.\n */\nconst keyboardDidOpen = () => {\n  const scaledHeightDifference = (previousVisualViewport.height - currentVisualViewport.height) * currentVisualViewport.scale;\n  return !keyboardOpen && previousVisualViewport.width === currentVisualViewport.width && scaledHeightDifference > KEYBOARD_THRESHOLD;\n};\n/**\n * Returns `true` if the keyboard is open,\n * but the keyboard did not close\n */\nconst keyboardDidResize = win => {\n  return keyboardOpen && !keyboardDidClose(win);\n};\n/**\n * Determine if the keyboard was closed\n * Returns `true` if the `keyboardOpen` flag is set and\n * the current visual viewport height equals the\n * layout viewport height.\n */\nconst keyboardDidClose = win => {\n  return keyboardOpen && currentVisualViewport.height === win.innerHeight;\n};\n/**\n * Dispatch a keyboard open event\n */\nconst fireKeyboardOpenEvent = (win, nativeEv) => {\n  const keyboardHeight = nativeEv ? nativeEv.keyboardHeight : win.innerHeight - currentVisualViewport.height;\n  const ev = new CustomEvent(KEYBOARD_DID_OPEN, {\n    detail: {\n      keyboardHeight\n    }\n  });\n  win.dispatchEvent(ev);\n};\n/**\n * Dispatch a keyboard close event\n */\nconst fireKeyboardCloseEvent = win => {\n  const ev = new CustomEvent(KEYBOARD_DID_CLOSE);\n  win.dispatchEvent(ev);\n};\n/**\n * Given a window object, create a copy of\n * the current visual and layout viewport states\n * while also preserving the previous visual and\n * layout viewport states\n */\nconst trackViewportChanges = win => {\n  previousVisualViewport = Object.assign({}, currentVisualViewport);\n  currentVisualViewport = copyVisualViewport(win.visualViewport);\n};\n/**\n * Creates a deep copy of the visual viewport\n * at a given state\n */\nconst copyVisualViewport = visualViewport => {\n  return {\n    width: Math.round(visualViewport.width),\n    height: Math.round(visualViewport.height),\n    offsetTop: visualViewport.offsetTop,\n    offsetLeft: visualViewport.offsetLeft,\n    pageTop: visualViewport.pageTop,\n    pageLeft: visualViewport.pageLeft,\n    scale: visualViewport.scale\n  };\n};\nexport { KEYBOARD_DID_CLOSE, KEYBOARD_DID_OPEN, copyVisualViewport, keyboardDidClose, keyboardDidOpen, keyboardDidResize, resetKeyboardAssist, setKeyboardClose, setKeyboardOpen, startKeyboardAssist, trackViewportChanges };", "map": {"version": 3, "names": ["K", "Keyboard", "KEYBOARD_DID_OPEN", "KEYBOARD_DID_CLOSE", "KEYBOARD_THRESHOLD", "previousVisualViewport", "currentVisualViewport", "keyboardOpen", "resetKeyboardAssist", "startKeyboardAssist", "win", "nativeEngine", "getEngine", "startNativeListeners", "visualViewport", "copyVisualViewport", "onresize", "trackViewportChanges", "keyboardDidOpen", "keyboardDidResize", "setKeyboardOpen", "keyboardDidClose", "setKeyboardClose", "addEventListener", "ev", "fireKeyboardOpenEvent", "fireKeyboardCloseEvent", "scaledHeightDifference", "height", "scale", "width", "innerHeight", "nativeEv", "keyboardHeight", "CustomEvent", "detail", "dispatchEvent", "Object", "assign", "Math", "round", "offsetTop", "offsetLeft", "pageTop", "pageLeft"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/keyboard-52278bd7.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { K as Keyboard } from './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\n\nconst KEYBOARD_DID_OPEN = 'ionKeyboardDidShow';\nconst KEYBOARD_DID_CLOSE = 'ionKeyboardDidHide';\nconst KEYBOARD_THRESHOLD = 150;\n// TODO(FW-2832): types\nlet previousVisualViewport = {};\nlet currentVisualViewport = {};\nlet keyboardOpen = false;\n/**\n * This is only used for tests\n */\nconst resetKeyboardAssist = () => {\n    previousVisualViewport = {};\n    currentVisualViewport = {};\n    keyboardOpen = false;\n};\nconst startKeyboardAssist = (win) => {\n    const nativeEngine = Keyboard.getEngine();\n    /**\n     * If the native keyboard plugin is available\n     * then we are running in a native environment. As a result\n     * we should only listen on the native events instead of\n     * using the Visual Viewport as the Ionic webview manipulates\n     * how it resizes such that the Visual Viewport API is not\n     * reliable here.\n     */\n    if (nativeEngine) {\n        startNativeListeners(win);\n    }\n    else {\n        if (!win.visualViewport) {\n            return;\n        }\n        currentVisualViewport = copyVisualViewport(win.visualViewport);\n        win.visualViewport.onresize = () => {\n            trackViewportChanges(win);\n            if (keyboardDidOpen() || keyboardDidResize(win)) {\n                setKeyboardOpen(win);\n            }\n            else if (keyboardDidClose(win)) {\n                setKeyboardClose(win);\n            }\n        };\n    }\n};\n/**\n * Listen for events fired by native keyboard plugin\n * in Capacitor/Cordova so devs only need to listen\n * in one place.\n */\nconst startNativeListeners = (win) => {\n    win.addEventListener('keyboardDidShow', (ev) => setKeyboardOpen(win, ev));\n    win.addEventListener('keyboardDidHide', () => setKeyboardClose(win));\n};\nconst setKeyboardOpen = (win, ev) => {\n    fireKeyboardOpenEvent(win, ev);\n    keyboardOpen = true;\n};\nconst setKeyboardClose = (win) => {\n    fireKeyboardCloseEvent(win);\n    keyboardOpen = false;\n};\n/**\n * Returns `true` if the `keyboardOpen` flag is not\n * set, the previous visual viewport width equal the current\n * visual viewport width, and if the scaled difference\n * of the previous visual viewport height minus the current\n * visual viewport height is greater than KEYBOARD_THRESHOLD\n *\n * We need to be able to accommodate users who have zooming\n * enabled in their browser (or have zoomed in manually) which\n * is why we take into account the current visual viewport's\n * scale value.\n */\nconst keyboardDidOpen = () => {\n    const scaledHeightDifference = (previousVisualViewport.height - currentVisualViewport.height) * currentVisualViewport.scale;\n    return (!keyboardOpen &&\n        previousVisualViewport.width === currentVisualViewport.width &&\n        scaledHeightDifference > KEYBOARD_THRESHOLD);\n};\n/**\n * Returns `true` if the keyboard is open,\n * but the keyboard did not close\n */\nconst keyboardDidResize = (win) => {\n    return keyboardOpen && !keyboardDidClose(win);\n};\n/**\n * Determine if the keyboard was closed\n * Returns `true` if the `keyboardOpen` flag is set and\n * the current visual viewport height equals the\n * layout viewport height.\n */\nconst keyboardDidClose = (win) => {\n    return keyboardOpen && currentVisualViewport.height === win.innerHeight;\n};\n/**\n * Dispatch a keyboard open event\n */\nconst fireKeyboardOpenEvent = (win, nativeEv) => {\n    const keyboardHeight = nativeEv ? nativeEv.keyboardHeight : win.innerHeight - currentVisualViewport.height;\n    const ev = new CustomEvent(KEYBOARD_DID_OPEN, {\n        detail: { keyboardHeight },\n    });\n    win.dispatchEvent(ev);\n};\n/**\n * Dispatch a keyboard close event\n */\nconst fireKeyboardCloseEvent = (win) => {\n    const ev = new CustomEvent(KEYBOARD_DID_CLOSE);\n    win.dispatchEvent(ev);\n};\n/**\n * Given a window object, create a copy of\n * the current visual and layout viewport states\n * while also preserving the previous visual and\n * layout viewport states\n */\nconst trackViewportChanges = (win) => {\n    previousVisualViewport = Object.assign({}, currentVisualViewport);\n    currentVisualViewport = copyVisualViewport(win.visualViewport);\n};\n/**\n * Creates a deep copy of the visual viewport\n * at a given state\n */\nconst copyVisualViewport = (visualViewport) => {\n    return {\n        width: Math.round(visualViewport.width),\n        height: Math.round(visualViewport.height),\n        offsetTop: visualViewport.offsetTop,\n        offsetLeft: visualViewport.offsetLeft,\n        pageTop: visualViewport.pageTop,\n        pageLeft: visualViewport.pageLeft,\n        scale: visualViewport.scale,\n    };\n};\n\nexport { KEYBOARD_DID_CLOSE, KEYBOARD_DID_OPEN, copyVisualViewport, keyboardDidClose, keyboardDidOpen, keyboardDidResize, resetKeyboardAssist, setKeyboardClose, setKeyboardOpen, startKeyboardAssist, trackViewportChanges };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,QAAQ,QAAQ,wBAAwB;AACtD,OAAO,yBAAyB;AAChC,OAAO,qBAAqB;AAE5B,MAAMC,iBAAiB,GAAG,oBAAoB;AAC9C,MAAMC,kBAAkB,GAAG,oBAAoB;AAC/C,MAAMC,kBAAkB,GAAG,GAAG;AAC9B;AACA,IAAIC,sBAAsB,GAAG,CAAC,CAAC;AAC/B,IAAIC,qBAAqB,GAAG,CAAC,CAAC;AAC9B,IAAIC,YAAY,GAAG,KAAK;AACxB;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAC9BH,sBAAsB,GAAG,CAAC,CAAC;EAC3BC,qBAAqB,GAAG,CAAC,CAAC;EAC1BC,YAAY,GAAG,KAAK;AACxB,CAAC;AACD,MAAME,mBAAmB,GAAIC,GAAG,IAAK;EACjC,MAAMC,YAAY,GAAGV,QAAQ,CAACW,SAAS,CAAC,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAID,YAAY,EAAE;IACdE,oBAAoB,CAACH,GAAG,CAAC;EAC7B,CAAC,MACI;IACD,IAAI,CAACA,GAAG,CAACI,cAAc,EAAE;MACrB;IACJ;IACAR,qBAAqB,GAAGS,kBAAkB,CAACL,GAAG,CAACI,cAAc,CAAC;IAC9DJ,GAAG,CAACI,cAAc,CAACE,QAAQ,GAAG,MAAM;MAChCC,oBAAoB,CAACP,GAAG,CAAC;MACzB,IAAIQ,eAAe,CAAC,CAAC,IAAIC,iBAAiB,CAACT,GAAG,CAAC,EAAE;QAC7CU,eAAe,CAACV,GAAG,CAAC;MACxB,CAAC,MACI,IAAIW,gBAAgB,CAACX,GAAG,CAAC,EAAE;QAC5BY,gBAAgB,CAACZ,GAAG,CAAC;MACzB;IACJ,CAAC;EACL;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,oBAAoB,GAAIH,GAAG,IAAK;EAClCA,GAAG,CAACa,gBAAgB,CAAC,iBAAiB,EAAGC,EAAE,IAAKJ,eAAe,CAACV,GAAG,EAAEc,EAAE,CAAC,CAAC;EACzEd,GAAG,CAACa,gBAAgB,CAAC,iBAAiB,EAAE,MAAMD,gBAAgB,CAACZ,GAAG,CAAC,CAAC;AACxE,CAAC;AACD,MAAMU,eAAe,GAAGA,CAACV,GAAG,EAAEc,EAAE,KAAK;EACjCC,qBAAqB,CAACf,GAAG,EAAEc,EAAE,CAAC;EAC9BjB,YAAY,GAAG,IAAI;AACvB,CAAC;AACD,MAAMe,gBAAgB,GAAIZ,GAAG,IAAK;EAC9BgB,sBAAsB,CAAChB,GAAG,CAAC;EAC3BH,YAAY,GAAG,KAAK;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAMS,sBAAsB,GAAG,CAACtB,sBAAsB,CAACuB,MAAM,GAAGtB,qBAAqB,CAACsB,MAAM,IAAItB,qBAAqB,CAACuB,KAAK;EAC3H,OAAQ,CAACtB,YAAY,IACjBF,sBAAsB,CAACyB,KAAK,KAAKxB,qBAAqB,CAACwB,KAAK,IAC5DH,sBAAsB,GAAGvB,kBAAkB;AACnD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMe,iBAAiB,GAAIT,GAAG,IAAK;EAC/B,OAAOH,YAAY,IAAI,CAACc,gBAAgB,CAACX,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,gBAAgB,GAAIX,GAAG,IAAK;EAC9B,OAAOH,YAAY,IAAID,qBAAqB,CAACsB,MAAM,KAAKlB,GAAG,CAACqB,WAAW;AAC3E,CAAC;AACD;AACA;AACA;AACA,MAAMN,qBAAqB,GAAGA,CAACf,GAAG,EAAEsB,QAAQ,KAAK;EAC7C,MAAMC,cAAc,GAAGD,QAAQ,GAAGA,QAAQ,CAACC,cAAc,GAAGvB,GAAG,CAACqB,WAAW,GAAGzB,qBAAqB,CAACsB,MAAM;EAC1G,MAAMJ,EAAE,GAAG,IAAIU,WAAW,CAAChC,iBAAiB,EAAE;IAC1CiC,MAAM,EAAE;MAAEF;IAAe;EAC7B,CAAC,CAAC;EACFvB,GAAG,CAAC0B,aAAa,CAACZ,EAAE,CAAC;AACzB,CAAC;AACD;AACA;AACA;AACA,MAAME,sBAAsB,GAAIhB,GAAG,IAAK;EACpC,MAAMc,EAAE,GAAG,IAAIU,WAAW,CAAC/B,kBAAkB,CAAC;EAC9CO,GAAG,CAAC0B,aAAa,CAACZ,EAAE,CAAC;AACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMP,oBAAoB,GAAIP,GAAG,IAAK;EAClCL,sBAAsB,GAAGgC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhC,qBAAqB,CAAC;EACjEA,qBAAqB,GAAGS,kBAAkB,CAACL,GAAG,CAACI,cAAc,CAAC;AAClE,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAID,cAAc,IAAK;EAC3C,OAAO;IACHgB,KAAK,EAAES,IAAI,CAACC,KAAK,CAAC1B,cAAc,CAACgB,KAAK,CAAC;IACvCF,MAAM,EAAEW,IAAI,CAACC,KAAK,CAAC1B,cAAc,CAACc,MAAM,CAAC;IACzCa,SAAS,EAAE3B,cAAc,CAAC2B,SAAS;IACnCC,UAAU,EAAE5B,cAAc,CAAC4B,UAAU;IACrCC,OAAO,EAAE7B,cAAc,CAAC6B,OAAO;IAC/BC,QAAQ,EAAE9B,cAAc,CAAC8B,QAAQ;IACjCf,KAAK,EAAEf,cAAc,CAACe;EAC1B,CAAC;AACL,CAAC;AAED,SAAS1B,kBAAkB,EAAED,iBAAiB,EAAEa,kBAAkB,EAAEM,gBAAgB,EAAEH,eAAe,EAAEC,iBAAiB,EAAEX,mBAAmB,EAAEc,gBAAgB,EAAEF,eAAe,EAAEX,mBAAmB,EAAEQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}