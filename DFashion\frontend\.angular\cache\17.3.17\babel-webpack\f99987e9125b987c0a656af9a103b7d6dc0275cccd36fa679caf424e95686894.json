{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5, 6];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_div_21_Template_div_click_0_listener() {\n      const i_r4 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"img\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", story_r5.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r5.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtext(3, \"Stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateStories(\"prev\"));\n    });\n    i0.ɵɵtext(6, \" \\u2190 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateStories(\"next\"));\n    });\n    i0.ɵɵtext(8, \" \\u2192 \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 16, 0)(11, \"div\", 17, 1)(13, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAdd());\n    });\n    i0.ɵɵelementStart(14, \"div\", 19)(15, \"div\", 20);\n    i0.ɵɵelement(16, \"img\", 21);\n    i0.ɵɵelementStart(17, \"span\", 22);\n    i0.ɵɵtext(18, \"+\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 23);\n    i0.ɵɵtext(20, \"Your Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, ViewAddStoriesComponent_div_2_div_21_Template, 6, 3, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollLeft);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollRight);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.avatar) || \"https://randomuser.me/api/portraits/women/43.jpg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.displayedStories);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 39);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.mediaUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption || \"Story image\");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_video_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 40);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStory.caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelementStart(1, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 30)(3, \"div\", 31);\n    i0.ɵɵelement(4, \"img\", 32);\n    i0.ɵɵelementStart(5, \"span\", 23);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵtext(11, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 35);\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_3_img_13_Template, 1, 2, \"img\", 36)(14, ViewAddStoriesComponent_div_3_video_14_Template, 1, 1, \"video\", 37)(15, ViewAddStoriesComponent_div_3_div_15_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, ctx_r1.currentStory.createdAt, \"short\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    // Stories data management\n    this.allStories = [];\n    this.visibleStories = [];\n    this.isLoadingStories = true;\n    this.isLoadingMore = false;\n    this.initialLoadCount = 10;\n    this.loadMoreCount = 5;\n    this.hasMoreStories = false;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.canScrollStoriesLeft = false;\n    this.canScrollStoriesRight = false;\n  }\n  ngOnInit() {\n    this.loadInitialStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  // Load initial 10 stories\n  loadInitialStories() {\n    this.isLoadingStories = true;\n    this.http.get('http://localhost:5000/api/stories').subscribe({\n      next: response => {\n        if (response.success) {\n          this.allStories = response.stories.filter(story => story.isActive);\n          this.loadVisibleStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.isLoadingStories = false;\n      }\n    });\n  }\n  // Load visible stories (initial 10)\n  loadVisibleStories() {\n    const currentCount = this.visibleStories.length;\n    const nextBatch = this.allStories.slice(0, this.initialLoadCount);\n    this.visibleStories = nextBatch;\n    this.hasMoreStories = this.allStories.length > this.visibleStories.length;\n    setTimeout(() => this.updateStoriesArrows(), 100);\n  }\n  // Load more stories when button is clicked\n  loadMoreStories() {\n    if (this.isLoadingMore || !this.hasMoreStories) return;\n    this.isLoadingMore = true;\n    // Simulate loading delay for better UX\n    setTimeout(() => {\n      const currentCount = this.visibleStories.length;\n      const nextBatch = this.allStories.slice(currentCount, currentCount + this.loadMoreCount);\n      this.visibleStories = [...this.visibleStories, ...nextBatch];\n      this.hasMoreStories = this.allStories.length > this.visibleStories.length;\n      this.isLoadingMore = false;\n      setTimeout(() => this.updateStoriesArrows(), 100);\n    }, 500);\n  }\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth;\n    }\n  }\n  // Open stories viewer\n  openStories(index) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    document.body.style.overflow = 'hidden';\n  }\n  // Close stories viewer\n  closeStories() {\n    this.isOpen = false;\n    document.body.style.overflow = 'auto';\n  }\n  // Navigate to next story\n  nextStory() {\n    if (this.currentIndex < this.visibleStories.length - 1) {\n      this.currentIndex++;\n    } else {\n      this.closeStories();\n    }\n  }\n  // Navigate to previous story\n  prevStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n    }\n  }\n  // Add new story\n  onAdd() {\n    this.router.navigate(['/stories/create']);\n  }\n  // Get current story\n  get currentStory() {\n    return this.visibleStories[this.currentIndex] || null;\n  }\n  // Setup event listeners\n  setupEventListeners() {\n    // Add any additional event listeners here\n  }\n  // Remove event listeners\n  removeEventListeners() {\n    // Remove any event listeners here\n  }\n  // Handle story scroll for arrows\n  onStoriesScroll() {\n    this.updateStoriesArrows();\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 3,\n      consts: [[\"storiesWrapper\", \"\"], [\"storiesSlider\", \"\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-section\", 4, \"ngIf\"], [\"class\", \"story-viewer-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-section\"], [1, \"stories-header\"], [1, \"stories-title\"], [1, \"stories-nav\"], [1, \"nav-btn\", 3, \"click\", \"disabled\"], [1, \"stories-wrapper\"], [1, \"stories-slider\"], [1, \"story-item\", \"add-story\", 3, \"click\"], [1, \"story-avatar\", \"add-story-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [1, \"story-viewer-overlay\", 3, \"click\"], [1, \"story-viewer\", 3, \"click\"], [1, \"story-header\"], [1, \"story-user-info\"], [1, \"story-user-avatar\", 3, \"src\", \"alt\"], [1, \"story-time\"], [1, \"close-btn\", 3, \"click\"], [1, \"story-content\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"story-media\", \"controls\", \"\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [1, \"story-media\", 3, \"src\", \"alt\"], [\"controls\", \"\", \"autoplay\", \"\", \"muted\", \"\", 1, \"story-media\", 3, \"src\"], [1, \"story-caption\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 3)(2, ViewAddStoriesComponent_div_2_Template, 22, 4, \"div\", 4)(3, ViewAddStoriesComponent_div_3_Template, 16, 10, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen && ctx.currentStory);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, FormsModule],\n      styles: [\"@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 78px;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story-skeleton[_ngcontent-%COMP%]   .skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.stories-loading[_ngcontent-%COMP%]   .story-skeleton[_ngcontent-%COMP%]   .skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-top: 8px;\\n  border-radius: 6px;\\n}\\n\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 12px;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: #262626;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  background: #efefef;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  border: none;\\n  font-size: 14px;\\n  color: #262626;\\n  transition: all 0.2s ease;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #dbdbdb;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  flex: 0 0 auto;\\n  width: 78px;\\n  cursor: pointer;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item.add-story[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item.add-story[_ngcontent-%COMP%]   .add-story-avatar[_ngcontent-%COMP%]   .add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  font-weight: bold;\\n  border: 2px solid white;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  padding: 2px;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n.stories-section[_ngcontent-%COMP%]   .stories-wrapper[_ngcontent-%COMP%]   .stories-slider[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 5px;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  max-width: 100%;\\n  text-align: center;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  background: #dbdbdb !important;\\n  position: relative;\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  background: #0095f6;\\n  color: #fff;\\n  border-radius: 50%;\\n  width: 22px;\\n  height: 22px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.2rem;\\n  border: 2px solid #fff;\\n  font-weight: bold;\\n}\\n\\n.load-more-item[_ngcontent-%COMP%]   .load-more-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);\\n  border: 2px dashed #ccc;\\n}\\n.load-more-item[_ngcontent-%COMP%]   .load-more-avatar.loading[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #e3f2fd, #bbdefb);\\n  border-color: #2196f3;\\n}\\n.load-more-item[_ngcontent-%COMP%]   .load-more-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: #666;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n.load-more-item[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid #f3f3f3;\\n  border-top: 2px solid #2196f3;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.load-more-item[_ngcontent-%COMP%]:hover   .load-more-avatar[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n  border-color: #2196f3;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.stories-viewer-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(0, 0, 0, 0.9);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n\\n.stories-viewer[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-width: 400px;\\n  max-height: 80vh;\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: white;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.story-user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.story-user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.story-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-height: 60vh;\\n  overflow: hidden;\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  display: block;\\n}\\n\\n.story-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\\n  color: white;\\n  padding: 20px 16px 16px;\\n  font-size: 14px;\\n}\\n\\n.story-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: rgba(0, 0, 0, 0.5);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  font-size: 18px;\\n  cursor: pointer;\\n}\\n.story-nav.prev[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.story-nav.next[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n.story-nav[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  padding: 8px 16px;\\n  background: white;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n}\\n\\n@media (max-width: 614px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    margin-bottom: 8px;\\n  }\\n  .stories-slider[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    padding: 8px 0;\\n  }\\n  .story-item[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    border-width: 1.5px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9jb21wb25lbnRzL2luc3RhZ3JhbS1zdG9yaWVzL3ZpZXctYWRkLXN0b3JpZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRTtJQUNFLDJCQUFBO0VBQ0Y7RUFDQTtJQUNFLDRCQUFBO0VBQ0Y7QUFDRjtBQUVBO0VBQ0UsaUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUFBRjs7QUFHQTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBQUY7QUFFRTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQUFKO0FBRUk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EseUVBQUE7RUFDQSwwQkFBQTtFQUNBLGdDQUFBO0FBQU47QUFHSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EseUVBQUE7RUFDQSwwQkFBQTtFQUNBLGdDQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0FBRE47O0FBT0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBSko7QUFNSTtFQUNFLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7QUFKTjtBQU9JO0VBQ0UsYUFBQTtFQUNBLFFBQUE7QUFMTjtBQU9NO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUFMUjtBQU9RO0VBQ0UsbUJBQUE7QUFMVjtBQVFRO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0FBTlY7QUFZRTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QUFWSjtBQVlJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSwrQkFBQTtFQUNBLHNCQUFBO0FBVk47QUFZTTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0FBVlI7QUFhVTtFQUNFLGtCQUFBO0FBWFo7QUFhWTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFFBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSx1QkFBQTtBQVhkO0FBZ0JRO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSwrRUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBZFY7QUFnQlE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBZFY7QUFnQlE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBQWRWO0FBZ0JRO0VBQ0UsZUFBQTtFQUNBLGVBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7QUFkVjs7QUFxQkE7RUFDRSw4QkFBQTtFQUNBLGtCQUFBO0FBbEJGOztBQXFCQTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxpQkFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUE7QUFsQkY7O0FBc0JFO0VBQ0Usb0RBQUE7RUFDQSx1QkFBQTtBQW5CSjtBQXFCSTtFQUNFLG9EQUFBO0VBQ0EscUJBQUE7QUFuQk47QUF1QkU7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxXQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0FBckJKO0FBd0JFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtBQXRCSjtBQTBCSTtFQUNFLHNCQUFBO0VBQ0EscUJBQUE7QUF4Qk47O0FBNkJBO0VBQ0U7SUFBSyx1QkFBQTtFQXpCTDtFQTBCQTtJQUFPLHlCQUFBO0VBdkJQO0FBQ0Y7QUF5QkE7RUFDRSxlQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDhCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0FBdkJGOztBQTBCQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQXZCRjs7QUEwQkE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSw2QkFBQTtBQXZCRjs7QUEwQkE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FBdkJGOztBQTBCQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQXZCRjs7QUEwQkE7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7QUF2QkY7O0FBMEJBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FBdkJGOztBQTBCQTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsNERBQUE7RUFDQSxZQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0FBdkJGOztBQTBCQTtFQUNFLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLDJCQUFBO0VBQ0EsOEJBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtBQXZCRjtBQXlCRTtFQUFTLFVBQUE7QUF0Qlg7QUF1QkU7RUFBUyxXQUFBO0FBcEJYO0FBc0JFO0VBQ0UsOEJBQUE7QUFwQko7O0FBd0JBO0VBQ0UsYUFBQTtFQUNBLFFBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0FBckJGOztBQXdCQTtFQUNFLE9BQUE7RUFDQSxXQUFBO0FBckJGOztBQXdCQTtFQUNFO0lBQ0Usa0JBQUE7RUFyQkY7RUF1QkE7SUFDRSxRQUFBO0lBQ0EsY0FBQTtFQXJCRjtFQXVCQTtJQUNFLFdBQUE7RUFyQkY7RUF1QkE7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQXJCRjtFQXVCQTtJQUNFLG1CQUFBO0VBckJGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyJAa2V5ZnJhbWVzIGxvYWRpbmcge1xuICAwJSB7XG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMjAwJSAwO1xuICB9XG4gIDEwMCUge1xuICAgIGJhY2tncm91bmQtcG9zaXRpb246IC0yMDAlIDA7XG4gIH1cbn1cblxuLnN0b3JpZXMtY29udGFpbmVyIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNkYmRiZGI7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgcGFkZGluZzogMTZweDtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4uc3Rvcmllcy1sb2FkaW5nIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAxNXB4O1xuICBcbiAgLnN0b3J5LXNrZWxldG9uIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICB3aWR0aDogNzhweDtcbiAgICBcbiAgICAuc2tlbGV0b24tYXZhdGFyIHtcbiAgICAgIHdpZHRoOiA2NnB4O1xuICAgICAgaGVpZ2h0OiA2NnB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjZjBmMGYwIDI1JSwgI2UwZTBlMCA1MCUsICNmMGYwZjAgNzUlKTtcbiAgICAgIGJhY2tncm91bmQtc2l6ZTogMjAwJSAxMDAlO1xuICAgICAgYW5pbWF0aW9uOiBsb2FkaW5nIDEuNXMgaW5maW5pdGU7XG4gICAgfVxuICAgIFxuICAgIC5za2VsZXRvbi1uYW1lIHtcbiAgICAgIHdpZHRoOiA2MHB4O1xuICAgICAgaGVpZ2h0OiAxMnB4O1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjZjBmMGYwIDI1JSwgI2UwZTBlMCA1MCUsICNmMGYwZjAgNzUlKTtcbiAgICAgIGJhY2tncm91bmQtc2l6ZTogMjAwJSAxMDAlO1xuICAgICAgYW5pbWF0aW9uOiBsb2FkaW5nIDEuNXMgaW5maW5pdGU7XG4gICAgICBtYXJnaW4tdG9wOiA4cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgfVxuICB9XG59XG5cbi5zdG9yaWVzLXNlY3Rpb24ge1xuICAuc3Rvcmllcy1oZWFkZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMTJweDtcbiAgICBcbiAgICAuc3Rvcmllcy10aXRsZSB7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgfVxuICAgIFxuICAgIC5zdG9yaWVzLW5hdiB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBcbiAgICAgIC5uYXYtYnRuIHtcbiAgICAgICAgd2lkdGg6IDI0cHg7XG4gICAgICAgIGhlaWdodDogMjRweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgICBiYWNrZ3JvdW5kOiAjZWZlZmVmO1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gICAgICAgIFxuICAgICAgICAmOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZGJkYmRiO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAmOmRpc2FibGVkIHtcbiAgICAgICAgICBvcGFjaXR5OiAwLjU7XG4gICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICBcbiAgLnN0b3JpZXMtd3JhcHBlciB7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgXG4gICAgLnN0b3JpZXMtc2xpZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDE1cHg7XG4gICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xuICAgICAgd2lsbC1jaGFuZ2U6IHRyYW5zZm9ybTtcbiAgICAgIFxuICAgICAgLnN0b3J5LWl0ZW0ge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBmbGV4OiAwIDAgYXV0bztcbiAgICAgICAgd2lkdGg6IDc4cHg7XG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgXG4gICAgICAgICYuYWRkLXN0b3J5IHtcbiAgICAgICAgICAuYWRkLXN0b3J5LWF2YXRhciB7XG4gICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC5hZGQtc3RvcnktcGx1cyB7XG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgICAgICAgYm90dG9tOiAwO1xuICAgICAgICAgICAgICByaWdodDogMDtcbiAgICAgICAgICAgICAgd2lkdGg6IDIwcHg7XG4gICAgICAgICAgICAgIGhlaWdodDogMjBweDtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzAwOTVmNjtcbiAgICAgICAgICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCB3aGl0ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC5zdG9yeS1hdmF0YXIge1xuICAgICAgICAgIHdpZHRoOiA2NnB4O1xuICAgICAgICAgIGhlaWdodDogNjZweDtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICAgICAgcGFkZGluZzogMnB4O1xuICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgI2YwOTQzMywgI2U2NjgzYywgI2RjMjc0MywgI2NjMjM2NiwgI2JjMTg4OCk7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICB9XG4gICAgICAgIC5zdG9yeS1hdmF0YXItaW5uZXIge1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICAgICAgYm9yZGVyOiAycHggc29saWQgd2hpdGU7XG4gICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgIH1cbiAgICAgICAgLnN0b3J5LWF2YXRhci1pbWcge1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgfVxuICAgICAgICAuc3RvcnktdXNlcm5hbWUge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgICBtYXJnaW4tdG9wOiA1cHg7XG4gICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG4gICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICAgIG1heC13aWR0aDogMTAwJTtcbiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLmFkZC1zdG9yeS1hdmF0YXIge1xuICBiYWNrZ3JvdW5kOiAjZGJkYmRiICFpbXBvcnRhbnQ7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLmFkZC1zdG9yeS1wbHVzIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBib3R0b206IDA7XG4gIHJpZ2h0OiAwO1xuICBiYWNrZ3JvdW5kOiAjMDA5NWY2O1xuICBjb2xvcjogI2ZmZjtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB3aWR0aDogMjJweDtcbiAgaGVpZ2h0OiAyMnB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZm9udC1zaXplOiAxLjJyZW07XG4gIGJvcmRlcjogMnB4IHNvbGlkICNmZmY7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xufVxuXG4ubG9hZC1tb3JlLWl0ZW0ge1xuICAubG9hZC1tb3JlLWF2YXRhciB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjZjBmMGYwLCAjZTBlMGUwKTtcbiAgICBib3JkZXI6IDJweCBkYXNoZWQgI2NjYztcbiAgICBcbiAgICAmLmxvYWRpbmcge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjZTNmMmZkLCAjYmJkZWZiKTtcbiAgICAgIGJvcmRlci1jb2xvcjogIzIxOTZmMztcbiAgICB9XG4gIH1cblxuICAubG9hZC1tb3JlLWljb24ge1xuICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICBjb2xvcjogIzY2NjtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgaGVpZ2h0OiAxMDAlO1xuICB9XG5cbiAgLmxvYWRpbmctc3Bpbm5lciB7XG4gICAgd2lkdGg6IDIwcHg7XG4gICAgaGVpZ2h0OiAyMHB4O1xuICAgIGJvcmRlcjogMnB4IHNvbGlkICNmM2YzZjM7XG4gICAgYm9yZGVyLXRvcDogMnB4IHNvbGlkICMyMTk2ZjM7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XG4gIH1cblxuICAmOmhvdmVyIHtcbiAgICAubG9hZC1tb3JlLWF2YXRhciB7XG4gICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xuICAgICAgYm9yZGVyLWNvbG9yOiAjMjE5NmYzO1xuICAgIH1cbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIHNwaW4ge1xuICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XG4gIDEwMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9XG59XG5cbi5zdG9yaWVzLXZpZXdlci1vdmVybGF5IHtcbiAgcG9zaXRpb246IGZpeGVkO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC45KTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHotaW5kZXg6IDEwMDA7XG59XG5cbi5zdG9yaWVzLXZpZXdlciB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgbWF4LXdpZHRoOiA0MDBweDtcbiAgbWF4LWhlaWdodDogODB2aDtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi5zdG9yeS1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIHBhZGRpbmc6IDEycHggMTZweDtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlO1xufVxuXG4uc3RvcnktdXNlci1pbmZvIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA4cHg7XG59XG5cbi5zdG9yeS11c2VyLWF2YXRhciB7XG4gIHdpZHRoOiAzMnB4O1xuICBoZWlnaHQ6IDMycHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG59XG5cbi5zdG9yeS1jb250ZW50IHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBtYXgtaGVpZ2h0OiA2MHZoO1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4uc3RvcnktbWVkaWEge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiBhdXRvO1xuICBkaXNwbGF5OiBibG9jaztcbn1cblxuLnN0b3J5LWNhcHRpb24ge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0cmFuc3BhcmVudCwgcmdiYSgwLDAsMCwwLjcpKTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiAyMHB4IDE2cHggMTZweDtcbiAgZm9udC1zaXplOiAxNHB4O1xufVxuXG4uc3RvcnktbmF2IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsMCwwLDAuNSk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgYm9yZGVyOiBub25lO1xuICB3aWR0aDogNDBweDtcbiAgaGVpZ2h0OiA0MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGZvbnQtc2l6ZTogMThweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICBcbiAgJi5wcmV2IHsgbGVmdDogMTZweDsgfVxuICAmLm5leHQgeyByaWdodDogMTZweDsgfVxuICBcbiAgJjpob3ZlciB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgwLDAsMCwwLjcpO1xuICB9XG59XG5cbi5zdG9yeS1wcm9ncmVzcyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMnB4O1xuICBwYWRkaW5nOiA4cHggMTZweDtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG59XG5cbi5wcm9ncmVzcy1iYXIge1xuICBmbGV4OiAxO1xuICBoZWlnaHQ6IDJweDtcbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDYxNHB4KSB7XG4gIC5zdG9yaWVzLXNsaWRlci13cmFwcGVyIHtcbiAgICBtYXJnaW4tYm90dG9tOiA4cHg7XG4gIH1cbiAgLnN0b3JpZXMtc2xpZGVyIHtcbiAgICBnYXA6IDhweDtcbiAgICBwYWRkaW5nOiA4cHggMDtcbiAgfVxuICAuc3RvcnktaXRlbSB7XG4gICAgd2lkdGg6IDYwcHg7XG4gIH1cbiAgLnN0b3J5LWF2YXRhciB7XG4gICAgd2lkdGg6IDU2cHg7XG4gICAgaGVpZ2h0OiA1NnB4O1xuICB9XG4gIC5zdG9yeS1hdmF0YXItaW5uZXIge1xuICAgIGJvcmRlci13aWR0aDogMS41cHg7XG4gIH1cbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵlistener", "ViewAddStoriesComponent_div_2_div_21_Template_div_click_0_listener", "i_r4", "ɵɵrestoreView", "_r3", "index", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "openStories", "ɵɵtext", "story_r5", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "ViewAddStoriesComponent_div_2_Template_button_click_5_listener", "_r1", "navigateStories", "ViewAddStoriesComponent_div_2_Template_button_click_7_listener", "ViewAddStoriesComponent_div_2_Template_div_click_13_listener", "onAdd", "ViewAddStoriesComponent_div_2_div_21_Template", "canScrollLeft", "canScrollRight", "currentUser", "displayedStories", "currentStory", "mediaUrl", "caption", "ɵɵtextInterpolate1", "ViewAddStoriesComponent_div_3_Template_div_click_0_listener", "_r6", "closeStories", "ViewAddStoriesComponent_div_3_Template_div_click_1_listener", "$event", "stopPropagation", "ViewAddStoriesComponent_div_3_Template_button_click_10_listener", "ViewAddStoriesComponent_div_3_img_13_Template", "ViewAddStoriesComponent_div_3_video_14_Template", "ViewAddStoriesComponent_div_3_div_15_Template", "ɵɵpipeBind2", "createdAt", "mediaType", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "allStories", "visibleStories", "isLoadingStories", "isLoadingMore", "initialLoadCount", "loadMoreCount", "hasMoreStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "canScrollStoriesLeft", "canScrollStoriesRight", "ngOnInit", "loadInitialStories", "setupEventListeners", "currentUser$", "subscribe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "get", "next", "response", "success", "stories", "filter", "story", "isActive", "loadVisibleStories", "error", "console", "currentCount", "length", "nextBatch", "slice", "setTimeout", "updateStoriesArrows", "loadMoreStories", "scrollStoriesLeft", "storiesSlider", "nativeElement", "scrollBy", "left", "behavior", "scrollStoriesRight", "el", "scrollLeft", "scrollWidth", "clientWidth", "document", "body", "style", "overflow", "nextStory", "prevStory", "navigate", "onStoriesScroll", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "ViewAddStoriesComponent_div_3_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["interface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  isActive: boolean;\n  expiresAt: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  views: number;\n  likes: number;\n  replies: any[];\n}\n\nimport { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { AuthService } from '../../../../core/services/auth.service';\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})export class ViewAddStoriesComponent implements OnInit, On<PERSON><PERSON>roy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  currentUser: any = null;\n\n  // Stories data management\n  allStories: Story[] = [];\n  visibleStories: Story[] = [];\n  isLoadingStories = true;\n  isLoadingMore = false;\n  initialLoadCount = 10;\n  loadMoreCount = 5;\n  hasMoreStories = false;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // For slider arrows\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n  canScrollStoriesLeft = false;\n  canScrollStoriesRight = false;\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadInitialStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  // Load initial 10 stories\n  loadInitialStories() {\n    this.isLoadingStories = true;\n    \n    this.http.get<any>('http://localhost:5000/api/stories').subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.allStories = response.stories.filter((story: Story) => story.isActive);\n          this.loadVisibleStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        this.isLoadingStories = false;\n      }\n    });\n  }\n\n  // Load visible stories (initial 10)\n  loadVisibleStories() {\n    const currentCount = this.visibleStories.length;\n    const nextBatch = this.allStories.slice(0, this.initialLoadCount);\n    this.visibleStories = nextBatch;\n    this.hasMoreStories = this.allStories.length > this.visibleStories.length;\n    \n    setTimeout(() => this.updateStoriesArrows(), 100);\n  }\n\n  // Load more stories when button is clicked\n  loadMoreStories() {\n    if (this.isLoadingMore || !this.hasMoreStories) return;\n    \n    this.isLoadingMore = true;\n    \n    // Simulate loading delay for better UX\n    setTimeout(() => {\n      const currentCount = this.visibleStories.length;\n      const nextBatch = this.allStories.slice(currentCount, currentCount + this.loadMoreCount);\n      \n      this.visibleStories = [...this.visibleStories, ...nextBatch];\n      this.hasMoreStories = this.allStories.length > this.visibleStories.length;\n      this.isLoadingMore = false;\n      \n      setTimeout(() => this.updateStoriesArrows(), 100);\n    }, 500);\n  }\n\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < (el.scrollWidth - el.clientWidth);\n    }\n  }\n\n  // Open stories viewer\n  openStories(index: number) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    document.body.style.overflow = 'hidden';\n  }\n\n  // Close stories viewer\n  closeStories() {\n    this.isOpen = false;\n    document.body.style.overflow = 'auto';\n  }\n\n  // Navigate to next story\n  nextStory() {\n    if (this.currentIndex < this.visibleStories.length - 1) {\n      this.currentIndex++;\n    } else {\n      this.closeStories();\n    }\n  }\n\n  // Navigate to previous story\n  prevStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n    }\n  }\n\n  // Add new story\n  onAdd() {\n    this.router.navigate(['/stories/create']);\n  }\n\n  // Get current story\n  get currentStory(): Story | null {\n    return this.visibleStories[this.currentIndex] || null;\n  }\n\n  // Setup event listeners\n  private setupEventListeners() {\n    // Add any additional event listeners here\n  }\n\n  // Remove event listeners\n  private removeEventListeners() {\n    // Remove any event listeners here\n  }\n\n  // Handle story scroll for arrows\n  onStoriesScroll() {\n    this.updateStoriesArrows();\n  }\n}", "<!-- Instagram-style Stories Container -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Section -->\n  <div class=\"stories-section\" *ngIf=\"!isLoadingStories\">\n    <!-- Stories Header -->\n    <div class=\"stories-header\">\n      <div class=\"stories-title\">Stories</div>\n      <div class=\"stories-nav\">\n        <button \n          class=\"nav-btn\" \n          [disabled]=\"!canScrollLeft\" \n          (click)=\"navigateStories('prev')\">\n          ←\n        </button>\n        <button \n          class=\"nav-btn\" \n          [disabled]=\"!canScrollRight\" \n          (click)=\"navigateStories('next')\">\n          →\n        </button>\n      </div>\n    </div>\n\n    <!-- Stories Wrapper -->\n    <div class=\"stories-wrapper\" #storiesWrapper>\n      <div class=\"stories-slider\" #storiesSlider>\n        <!-- Add Story Button -->\n        <div class=\"story-item add-story\" (click)=\"onAdd()\">\n          <div class=\"story-avatar add-story-avatar\">\n            <div class=\"story-avatar-inner\">\n              <img \n                class=\"story-avatar-img\" \n                [src]=\"currentUser?.avatar || 'https://randomuser.me/api/portraits/women/43.jpg'\" \n                alt=\"Your Story\">\n              <span class=\"add-story-plus\">+</span>\n            </div>\n          </div>\n          <div class=\"story-username\">Your Story</div>\n        </div>\n\n        <!-- Story Items -->\n        <div \n          *ngFor=\"let story of displayedStories; let i = index\" \n          class=\"story-item\"\n          (click)=\"openStories(i)\">\n          <div class=\"story-avatar\">\n            <div class=\"story-avatar-inner\">\n              <img \n                class=\"story-avatar-img\" \n                [src]=\"story.user.avatar\" \n                [alt]=\"story.user.username\">\n            </div>\n          </div>\n          <div class=\"story-username\">{{ story.user.username }}</div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Story Viewer Modal -->\n  <div *ngIf=\"isOpen && currentStory\" class=\"story-viewer-overlay\" (click)=\"closeStories()\">\n    <div class=\"story-viewer\" (click)=\"$event.stopPropagation()\">\n      <!-- Story Header -->\n      <div class=\"story-header\">\n        <div class=\"story-user-info\">\n          <img [src]=\"currentStory.user.avatar\" [alt]=\"currentStory.user.username\" class=\"story-user-avatar\">\n          <span class=\"story-username\">{{ currentStory.user.username }}</span>\n          <span class=\"story-time\">{{ currentStory.createdAt | date:'short' }}</span>\n        </div>\n        <button class=\"close-btn\" (click)=\"closeStories()\">×</button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\">\n        <img \n          *ngIf=\"currentStory.mediaType === 'image'\" \n          [src]=\"currentStory.mediaUrl\" \n          [alt]=\"currentStory.caption || 'Story image'\" \n          class=\"story-media\">\n        \n        <video \n          *ngIf=\"currentStory.mediaType === 'video'\" \n          [src]=\"currentStory.mediaUrl\" \n          class=\"story-media\" \n          controls \n          autoplay \n          muted>\n        </video>\n        \n        <div *ngIf=\"currentStory.caption\" class=\"story-caption\">\n          {{ currentStory.caption }}\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": "AAqBA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;IClBxCC,EAAA,CAAAC,cAAA,aAA+D;IAE7DD,EADA,CAAAE,SAAA,aAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAA+D;IAIjEL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAgB;;;;;;IA6ClCT,EAAA,CAAAC,cAAA,cAG2B;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,mEAAA;MAAA,MAAAC,IAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,IAAA,CAAc;IAAA,EAAC;IAEtBZ,EADF,CAAAC,cAAA,cAA0B,cACQ;IAC9BD,EAAA,CAAAE,SAAA,cAG8B;IAElCF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,GAAyB;IACvDpB,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IALEH,EAAA,CAAAM,SAAA,GAAyB;IACzBN,EADA,CAAAO,UAAA,QAAAc,QAAA,CAAAC,IAAA,CAAAC,MAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAyB,QAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CACE;IAGLzB,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA0B,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;;IA/CzDzB,EAHJ,CAAAC,cAAA,cAAuD,cAEzB,cACC;IAAAD,EAAA,CAAAoB,MAAA,cAAO;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAEtCH,EADF,CAAAC,cAAA,cAAyB,iBAIa;IAAlCD,EAAA,CAAAU,UAAA,mBAAAiB,+DAAA;MAAA3B,EAAA,CAAAa,aAAA,CAAAe,GAAA;MAAA,MAAAZ,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAa,eAAA,CAAgB,MAAM,CAAC;IAAA,EAAC;IACjC7B,EAAA,CAAAoB,MAAA,eACF;IAAApB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAGoC;IAAlCD,EAAA,CAAAU,UAAA,mBAAAoB,+DAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAe,GAAA;MAAA,MAAAZ,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAa,eAAA,CAAgB,MAAM,CAAC;IAAA,EAAC;IACjC7B,EAAA,CAAAoB,MAAA,eACF;IAEJpB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAMFH,EAHJ,CAAAC,cAAA,iBAA6C,kBACA,eAEW;IAAlBD,EAAA,CAAAU,UAAA,mBAAAqB,6DAAA;MAAA/B,EAAA,CAAAa,aAAA,CAAAe,GAAA;MAAA,MAAAZ,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAgB,KAAA,EAAO;IAAA,EAAC;IAE/ChC,EADF,CAAAC,cAAA,eAA2C,eACT;IAC9BD,EAAA,CAAAE,SAAA,eAGmB;IACnBF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAoB,MAAA,SAAC;IAElCpB,EAFkC,CAAAG,YAAA,EAAO,EACjC,EACF;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAoB,MAAA,kBAAU;IACxCpB,EADwC,CAAAG,YAAA,EAAM,EACxC;IAGNH,EAAA,CAAAI,UAAA,KAAA6B,6CAAA,kBAG2B;IAajCjC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA/CEH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAO,UAAA,cAAAS,MAAA,CAAAkB,aAAA,CAA2B;IAM3BlC,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAO,UAAA,cAAAS,MAAA,CAAAmB,cAAA,CAA4B;IAgBtBnC,EAAA,CAAAM,SAAA,GAAiF;IAAjFN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAoB,WAAA,kBAAApB,MAAA,CAAAoB,WAAA,CAAAb,MAAA,yDAAAvB,EAAA,CAAAwB,aAAA,CAAiF;IAUrExB,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAO,UAAA,YAAAS,MAAA,CAAAqB,gBAAA,CAAqB;;;;;IAgCzCrC,EAAA,CAAAE,SAAA,cAIsB;;;;IADpBF,EADA,CAAAO,UAAA,QAAAS,MAAA,CAAAsB,YAAA,CAAAC,QAAA,EAAAvC,EAAA,CAAAwB,aAAA,CAA6B,QAAAR,MAAA,CAAAsB,YAAA,CAAAE,OAAA,kBACgB;;;;;IAG/CxC,EAAA,CAAAE,SAAA,gBAOQ;;;;IALNF,EAAA,CAAAO,UAAA,QAAAS,MAAA,CAAAsB,YAAA,CAAAC,QAAA,EAAAvC,EAAA,CAAAwB,aAAA,CAA6B;;;;;IAO/BxB,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAyC,kBAAA,MAAAzB,MAAA,CAAAsB,YAAA,CAAAE,OAAA,MACF;;;;;;IA/BNxC,EAAA,CAAAC,cAAA,cAA0F;IAAzBD,EAAA,CAAAU,UAAA,mBAAAgC,4DAAA;MAAA1C,EAAA,CAAAa,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAA4B,YAAA,EAAc;IAAA,EAAC;IACvF5C,EAAA,CAAAC,cAAA,cAA6D;IAAnCD,EAAA,CAAAU,UAAA,mBAAAmC,4DAAAC,MAAA;MAAA9C,EAAA,CAAAa,aAAA,CAAA8B,GAAA;MAAA,OAAA3C,EAAA,CAAAkB,WAAA,CAAS4B,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAGxD/C,EADF,CAAAC,cAAA,cAA0B,cACK;IAC3BD,EAAA,CAAAE,SAAA,cAAmG;IACnGF,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAoB,MAAA,GAAgC;IAAApB,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAoB,MAAA,GAA2C;;IACtEpB,EADsE,CAAAG,YAAA,EAAO,EACvE;IACNH,EAAA,CAAAC,cAAA,kBAAmD;IAAzBD,EAAA,CAAAU,UAAA,mBAAAsC,gEAAA;MAAAhD,EAAA,CAAAa,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAA4B,YAAA,EAAc;IAAA,EAAC;IAAC5C,EAAA,CAAAoB,MAAA,cAAC;IACtDpB,EADsD,CAAAG,YAAA,EAAS,EACzD;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IAgBzBD,EAfA,CAAAI,UAAA,KAAA6C,6CAAA,kBAIsB,KAAAC,+CAAA,oBAQd,KAAAC,6CAAA,kBAGgD;IAK9DnD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA7BOH,EAAA,CAAAM,SAAA,GAAgC;IAACN,EAAjC,CAAAO,UAAA,QAAAS,MAAA,CAAAsB,YAAA,CAAAhB,IAAA,CAAAC,MAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAgC,QAAAR,MAAA,CAAAsB,YAAA,CAAAhB,IAAA,CAAAG,QAAA,CAAmC;IAC3CzB,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA0B,iBAAA,CAAAV,MAAA,CAAAsB,YAAA,CAAAhB,IAAA,CAAAG,QAAA,CAAgC;IACpCzB,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAoD,WAAA,OAAApC,MAAA,CAAAsB,YAAA,CAAAe,SAAA,WAA2C;IAQnErD,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAsB,YAAA,CAAAgB,SAAA,aAAwC;IAMxCtD,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAsB,YAAA,CAAAgB,SAAA,aAAwC;IAQrCtD,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAsB,YAAA,CAAAE,OAAA,CAA0B;;;AD/DtC,OAAM,MAAOe,uBAAuB;EAiCpCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAjCrB,KAAAvB,WAAW,GAAQ,IAAI;IAEvB;IACA,KAAAwB,UAAU,GAAY,EAAE;IACxB,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAC,gBAAgB,GAAG,IAAI;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,cAAc,GAAG,KAAK;IAEtB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAI1C,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,qBAAqB,GAAG,KAAK;EAM1B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACzB,WAAW,CAAC0B,YAAY,CAACC,SAAS,CAAChE,IAAI,IAAG;MAC7C,IAAI,CAACc,WAAW,GAAGd,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAiE,WAAWA,CAAA;IACT,IAAI,CAACR,aAAa,CAACS,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;EACAR,kBAAkBA,CAAA;IAChB,IAAI,CAACrB,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACJ,IAAI,CAACkC,GAAG,CAAM,mCAAmC,CAAC,CAACN,SAAS,CAAC;MAChEO,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACnC,UAAU,GAAGkC,QAAQ,CAACE,OAAO,CAACC,MAAM,CAAEC,KAAY,IAAKA,KAAK,CAACC,QAAQ,CAAC;UAC3E,IAAI,CAACC,kBAAkB,EAAE;;QAE3B,IAAI,CAACtC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACvC,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACJ;EAEA;EACAsC,kBAAkBA,CAAA;IAChB,MAAMG,YAAY,GAAG,IAAI,CAAC1C,cAAc,CAAC2C,MAAM;IAC/C,MAAMC,SAAS,GAAG,IAAI,CAAC7C,UAAU,CAAC8C,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC1C,gBAAgB,CAAC;IACjE,IAAI,CAACH,cAAc,GAAG4C,SAAS;IAC/B,IAAI,CAACvC,cAAc,GAAG,IAAI,CAACN,UAAU,CAAC4C,MAAM,GAAG,IAAI,CAAC3C,cAAc,CAAC2C,MAAM;IAEzEG,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;EACnD;EAEA;EACAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC9C,aAAa,IAAI,CAAC,IAAI,CAACG,cAAc,EAAE;IAEhD,IAAI,CAACH,aAAa,GAAG,IAAI;IAEzB;IACA4C,UAAU,CAAC,MAAK;MACd,MAAMJ,YAAY,GAAG,IAAI,CAAC1C,cAAc,CAAC2C,MAAM;MAC/C,MAAMC,SAAS,GAAG,IAAI,CAAC7C,UAAU,CAAC8C,KAAK,CAACH,YAAY,EAAEA,YAAY,GAAG,IAAI,CAACtC,aAAa,CAAC;MAExF,IAAI,CAACJ,cAAc,GAAG,CAAC,GAAG,IAAI,CAACA,cAAc,EAAE,GAAG4C,SAAS,CAAC;MAC5D,IAAI,CAACvC,cAAc,GAAG,IAAI,CAACN,UAAU,CAAC4C,MAAM,GAAG,IAAI,CAAC3C,cAAc,CAAC2C,MAAM;MACzE,IAAI,CAACzC,aAAa,GAAG,KAAK;MAE1B4C,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;IACnD,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAE,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAAC,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC7ER,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EAEAQ,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACL,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC5ER,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACG,aAAa,EAAE;MACtB,MAAMM,EAAE,GAAG,IAAI,CAACN,aAAa,CAACC,aAAa;MAC3C,IAAI,CAAChC,oBAAoB,GAAGqC,EAAE,CAACC,UAAU,GAAG,CAAC;MAC7C,IAAI,CAACrC,qBAAqB,GAAGoC,EAAE,CAACC,UAAU,GAAID,EAAE,CAACE,WAAW,GAAGF,EAAE,CAACG,WAAY;;EAElF;EAEA;EACArG,WAAWA,CAACJ,KAAa;IACvB,IAAI,CAACoD,YAAY,GAAGpD,KAAK;IACzB,IAAI,CAACqD,MAAM,GAAG,IAAI;IAClBqD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEA;EACAhF,YAAYA,CAAA;IACV,IAAI,CAACwB,MAAM,GAAG,KAAK;IACnBqD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACN,cAAc,CAAC2C,MAAM,GAAG,CAAC,EAAE;MACtD,IAAI,CAACrC,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACvB,YAAY,EAAE;;EAEvB;EAEA;EACAkF,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3D,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEA;EACAnC,KAAKA,CAAA;IACH,IAAI,CAACyB,MAAM,CAACsE,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA;EACA,IAAIzF,YAAYA,CAAA;IACd,OAAO,IAAI,CAACuB,cAAc,CAAC,IAAI,CAACM,YAAY,CAAC,IAAI,IAAI;EACvD;EAEA;EACQiB,mBAAmBA,CAAA;IACzB;EAAA;EAGF;EACQO,oBAAoBA,CAAA;IAC1B;EAAA;EAGF;EACAqC,eAAeA,CAAA;IACb,IAAI,CAACpB,mBAAmB,EAAE;EAC5B;;;uBA/KarD,uBAAuB,EAAAvD,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnI,EAAA,CAAAiI,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAArI,EAAA,CAAAiI,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBhF,uBAAuB;MAAAiF,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;UCjCtC3I,EAAA,CAAAC,cAAA,aAA+B;UAmE7BD,EAjEA,CAAAI,UAAA,IAAAyI,sCAAA,iBAAsD,IAAAC,sCAAA,kBAQC,IAAAC,sCAAA,mBAyDmC;UAmC5F/I,EAAA,CAAAG,YAAA,EAAM;;;UApGEH,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAAqI,GAAA,CAAA9E,gBAAA,CAAsB;UAQE9D,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAAqI,GAAA,CAAA9E,gBAAA,CAAuB;UAyD/C9D,EAAA,CAAAM,SAAA,EAA4B;UAA5BN,EAAA,CAAAO,UAAA,SAAAqI,GAAA,CAAAxE,MAAA,IAAAwE,GAAA,CAAAtG,YAAA,CAA4B;;;qBDrCxBxC,YAAY,EAAAkJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAEpJ,WAAW;MAAAqJ,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}