// Mobile-First Responsive Design System
// Base styles are for mobile, then we enhance for larger screens

// Breakpoints (mobile-first approach)
$breakpoints: (
  mobile: 768px,
  tablet: 1024px,
  desktop: 1440px,
  large-desktop: 1920px
);

// Mixins for responsive design
@mixin mobile-only {
  @media (max-width: #{map-get($breakpoints, mobile) - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{map-get($breakpoints, mobile)}) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: #{map-get($breakpoints, mobile)}) and (max-width: #{map-get($breakpoints, tablet) - 1px}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{map-get($breakpoints, tablet)}) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: #{map-get($breakpoints, tablet)}) and (max-width: #{map-get($breakpoints, large-desktop) - 1px}) {
    @content;
  }
}

@mixin large-desktop-up {
  @media (min-width: #{map-get($breakpoints, large-desktop)}) {
    @content;
  }
}

// Container system (mobile-first)
.container {
  width: 100%;
  padding: 0 16px;
  margin: 0 auto;

  @include tablet-up {
    padding: 0 24px;
    max-width: 768px;
  }

  @include desktop-up {
    padding: 0 32px;
    max-width: 1200px;
  }

  @include large-desktop-up {
    max-width: 1400px;
  }
}

.container-fluid {
  width: 100%;
  padding: 0 16px;

  @include tablet-up {
    padding: 0 24px;
  }

  @include desktop-up {
    padding: 0 32px;
  }
}

// Grid system (mobile-first)
.grid {
  display: grid;
  gap: 16px;
  
  // Mobile: 1 column by default
  grid-template-columns: 1fr;

  @include tablet-up {
    gap: 20px;
    grid-template-columns: repeat(2, 1fr);
  }

  @include desktop-up {
    gap: 24px;
    grid-template-columns: repeat(3, 1fr);
  }

  @include large-desktop-up {
    grid-template-columns: repeat(4, 1fr);
  }

  // Grid variations
  &.grid-1 {
    grid-template-columns: 1fr;
  }

  &.grid-2 {
    grid-template-columns: 1fr;

    @include tablet-up {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &.grid-3 {
    grid-template-columns: 1fr;

    @include tablet-up {
      grid-template-columns: repeat(2, 1fr);
    }

    @include desktop-up {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &.grid-4 {
    grid-template-columns: repeat(2, 1fr);

    @include tablet-up {
      grid-template-columns: repeat(3, 1fr);
    }

    @include desktop-up {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &.grid-auto {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

    @include mobile-only {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
  }
}

// Flexbox utilities (mobile-first)
.flex {
  display: flex;

  &.flex-col {
    flex-direction: column;
  }

  &.flex-row {
    flex-direction: row;
  }

  &.flex-wrap {
    flex-wrap: wrap;
  }

  &.justify-center {
    justify-content: center;
  }

  &.justify-between {
    justify-content: space-between;
  }

  &.justify-around {
    justify-content: space-around;
  }

  &.items-center {
    align-items: center;
  }

  &.items-start {
    align-items: flex-start;
  }

  &.items-end {
    align-items: flex-end;
  }
}

// Spacing system (mobile-first)
.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: 8px; padding-right: 8px; }
.px-2 { padding-left: 16px; padding-right: 16px; }
.px-3 { padding-left: 24px; padding-right: 24px; }
.px-4 { padding-left: 32px; padding-right: 32px; }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: 8px; padding-bottom: 8px; }
.py-2 { padding-top: 16px; padding-bottom: 16px; }
.py-3 { padding-top: 24px; padding-bottom: 24px; }
.py-4 { padding-top: 32px; padding-bottom: 32px; }

.m-0 { margin: 0; }
.m-1 { margin: 8px; }
.m-2 { margin: 16px; }
.m-3 { margin: 24px; }
.m-4 { margin: 32px; }

.mx-auto { margin-left: auto; margin-right: auto; }

// Typography (mobile-first)
.text-xs { font-size: 12px; line-height: 1.4; }
.text-sm { font-size: 14px; line-height: 1.4; }
.text-base { font-size: 16px; line-height: 1.5; }
.text-lg { font-size: 18px; line-height: 1.5; }
.text-xl { font-size: 20px; line-height: 1.4; }
.text-2xl { font-size: 24px; line-height: 1.3; }
.text-3xl { font-size: 30px; line-height: 1.2; }

@include tablet-up {
  .text-lg { font-size: 20px; }
  .text-xl { font-size: 24px; }
  .text-2xl { font-size: 28px; }
  .text-3xl { font-size: 36px; }
}

@include desktop-up {
  .text-xl { font-size: 28px; }
  .text-2xl { font-size: 32px; }
  .text-3xl { font-size: 42px; }
}

// Visibility utilities
.mobile-only {
  @include tablet-up {
    display: none !important;
  }
}

.tablet-up {
  @include mobile-only {
    display: none !important;
  }
}

.desktop-up {
  @include mobile-only {
    display: none !important;
  }

  @include tablet-only {
    display: none !important;
  }
}

.desktop-only {
  @include mobile-only {
    display: none !important;
  }

  @include tablet-only {
    display: none !important;
  }

  @include large-desktop-up {
    display: none !important;
  }
}

// Mobile-specific utilities
.mobile-full-width {
  @include mobile-only {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }
}

.mobile-sticky-bottom {
  @include mobile-only {
    position: sticky;
    bottom: 0;
    z-index: 100;
  }
}

// Safe area support for mobile devices
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.safe-area-all {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

// Scrollable containers (mobile-optimized)
.scrollable-x {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-snap-type: x mandatory;

  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
}

.scrollable-y {
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;

  @include mobile-only {
    &::-webkit-scrollbar {
      display: none;
    }

    scrollbar-width: none;
  }
}

// Touch-friendly interactive elements
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Accessibility improvements for mobile
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .card,
  .button,
  .input {
    border: 2px solid currentColor;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --border-color: #4a5568;
  }
}
