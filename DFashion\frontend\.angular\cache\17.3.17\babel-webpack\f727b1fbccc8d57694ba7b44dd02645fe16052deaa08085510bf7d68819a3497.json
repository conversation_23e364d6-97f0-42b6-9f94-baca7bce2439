{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/cart.service\";\nimport * as i2 from \"../../../../core/services/wishlist.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/media.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = [\"videoPlayer\"];\nfunction PostCardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PostCardComponent_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 37);\n    i0.ɵɵlistener(\"error\", function PostCardComponent_img_13_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleImageError($event));\n    })(\"load\", function PostCardComponent_img_13_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMediaLoadComplete());\n    })(\"dblclick\", function PostCardComponent_img_13_Template_img_dblclick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDoubleTap());\n    })(\"click\", function PostCardComponent_img_13_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleProductTags());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.currentMedia.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentMedia.alt);\n  }\n}\nfunction PostCardComponent_video_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 38, 0);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_video_14_Template_video_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleVideoPlay());\n    })(\"dblclick\", function PostCardComponent_video_14_Template_video_dblclick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDoubleTap());\n    })(\"loadeddata\", function PostCardComponent_video_14_Template_video_loadeddata_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMediaLoadComplete());\n    })(\"error\", function PostCardComponent_video_14_Template_video_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleVideoError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.currentMedia.url, i0.ɵɵsanitizeUrl)(\"poster\", ctx_r1.currentMedia.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"muted\", true)(\"loop\", true);\n  }\n}\nfunction PostCardComponent_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"span\", 44);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDuration(ctx_r1.currentMedia.duration));\n  }\n}\nfunction PostCardComponent_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.videoProgress, \"%\");\n  }\n}\nfunction PostCardComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleVideoPlay());\n    });\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PostCardComponent_div_15_div_3_Template, 3, 1, \"div\", 41)(4, PostCardComponent_div_15_div_4_Template, 2, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"visible\", ctx_r1.showVideoControls);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"playing\", ctx_r1.isVideoPlaying);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.isVideoPlaying ? \"fas fa-pause\" : \"fas fa-play\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentMedia.duration);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.videoDuration > 0);\n  }\n}\nfunction PostCardComponent_div_16_span_6_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 56);\n  }\n}\nfunction PostCardComponent_div_16_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_16_span_6_Template_span_click_0_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToMedia(i_r7));\n    });\n    i0.ɵɵtemplate(1, PostCardComponent_div_16_span_6_i_1_Template, 1, 0, \"i\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const media_r8 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentMediaIndex)(\"video\", media_r8.type === \"video\");\n    i0.ɵɵattribute(\"aria-label\", \"Go to media \" + (i_r7 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r8.type === \"video\");\n  }\n}\nfunction PostCardComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousMedia());\n    });\n    i0.ɵɵelement(2, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_16_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextMedia());\n    });\n    i0.ɵɵelement(4, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 52);\n    i0.ɵɵtemplate(6, PostCardComponent_div_16_span_6_Template, 2, 6, \"span\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentMediaIndex === 0);\n    i0.ɵɵattribute(\"aria-label\", \"Previous media\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentMediaIndex === ctx_r1.mediaItems.length - 1);\n    i0.ɵɵattribute(\"aria-label\", \"Next media\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.mediaItems);\n  }\n}\nfunction PostCardComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_19_div_1_Template_div_click_0_listener($event) {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.viewProduct(productTag_r10.product._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 60);\n    i0.ɵɵelement(2, \"div\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 62)(4, \"img\", 63);\n    i0.ɵɵlistener(\"error\", function PostCardComponent_div_19_div_1_Template_img_error_4_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleImageError($event, \"product\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 64)(6, \"h5\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_19_div_1_Template_button_click_12_listener() {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBuyNow(productTag_r10.product._id));\n    });\n    i0.ɵɵelement(13, \"i\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_19_div_1_Template_button_click_14_listener() {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(productTag_r10.product._id));\n    });\n    i0.ɵɵelement(15, \"i\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_19_div_1_Template_button_click_16_listener() {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(productTag_r10.product._id));\n    });\n    i0.ɵɵelement(17, \"i\", 15);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const productTag_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"top\", productTag_r10.position.y || 50, \"%\")(\"left\", productTag_r10.position.x || 50, \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImageUrl(productTag_r10.product.images[0].url || \"\"), i0.ɵɵsanitizeUrl)(\"alt\", productTag_r10.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r10.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(10, 8, productTag_r10.product.price), \"\");\n  }\n}\nfunction PostCardComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, PostCardComponent_div_19_div_1_Template, 18, 10, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"show-tags\", ctx_r1.showProductTags);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.products);\n  }\n}\nfunction PostCardComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Tap to view products\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PostCardComponent_div_31_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"img\", 77);\n    i0.ɵɵelementStart(2, \"div\", 78)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 79);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 80)(9, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_31_div_2_Template_button_click_9_listener() {\n      const productTag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(productTag_r12.product._id));\n    });\n    i0.ɵɵelement(10, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_31_div_2_Template_button_click_11_listener() {\n      const productTag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(productTag_r12.product._id));\n    });\n    i0.ɵɵelement(12, \"i\", 83);\n    i0.ɵɵtext(13, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_div_31_div_2_Template_button_click_14_listener() {\n      const productTag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow(productTag_r12.product._id));\n    });\n    i0.ɵɵtext(15, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const productTag_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", productTag_r12.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", productTag_r12.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r12.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(7, 8, productTag_r12.product.price), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isInWishlist(productTag_r12.product._id));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.isInWishlist(productTag_r12.product._id) ? \"fas fa-heart\" : \"far fa-heart\");\n  }\n}\nfunction PostCardComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74);\n    i0.ɵɵtemplate(2, PostCardComponent_div_31_div_2_Template, 16, 10, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.products);\n  }\n}\nfunction PostCardComponent_p_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" View all \", ctx_r1.post.comments.length, \" comments \");\n  }\n}\nfunction PostCardComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(comment_r13.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", comment_r13.text, \" \");\n  }\n}\nfunction PostCardComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function PostCardComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵtext(1, \" Post \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PostCardComponent {\n  constructor(cartService, wishlistService, router, mediaService) {\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.mediaService = mediaService;\n    this.liked = new EventEmitter();\n    this.commented = new EventEmitter();\n    this.shared = new EventEmitter();\n    this.isLiked = false;\n    this.isSaved = false;\n    this.likesCount = 0;\n    this.newComment = '';\n    this.showComments = false;\n    this.showProductTags = false;\n    this.wishlistItems = [];\n    this.cartItems = [];\n    // Media handling\n    this.mediaItems = [];\n    this.currentMediaIndex = 0;\n    // Video controls\n    this.isVideoPlaying = false;\n    this.videoDuration = 0;\n    this.videoProgress = 0;\n    this.showVideoControls = false;\n    this.showHeartAnimation = false;\n  }\n  ngOnInit() {\n    this.likesCount = this.post.analytics.likes;\n    this.loadWishlistItems();\n    this.loadCartItems();\n    this.initializeMedia();\n  }\n  initializeMedia() {\n    // Process media items with enhanced video support and content matching\n    const contentHint = this.post.caption || this.post.hashtags?.join(' ') || '';\n    this.mediaItems = this.mediaService.processMediaItems(this.post.media || []);\n    this.currentMediaIndex = 0;\n    this.currentMedia = this.mediaItems[0] || {\n      id: 'default',\n      type: 'image',\n      url: this.mediaService.getSafeImageUrl('', 'post'),\n      alt: 'Default post image'\n    };\n    // Preload media for better performance\n    this.preloadCurrentMedia();\n  }\n  preloadCurrentMedia() {\n    if (this.currentMedia) {\n      this.mediaService.preloadMedia([this.currentMedia]).catch(error => {\n        console.warn('Failed to preload media:', error);\n      });\n    }\n  }\n  loadWishlistItems() {\n    // Load from real API via service\n    this.wishlistItems = [];\n  }\n  loadCartItems() {\n    // Load from real API via service\n    this.cartItems = [];\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n  formatCaption(caption) {\n    return caption.replace(/#(\\w+)/g, '<span class=\"hashtag\">#$1</span>');\n  }\n  getRecentComments() {\n    return this.post.comments.slice(-2);\n  }\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    this.likesCount += this.isLiked ? 1 : -1;\n    this.liked.emit(this.post._id);\n  }\n  toggleSave() {\n    this.isSaved = !this.isSaved;\n  }\n  toggleComments() {\n    this.showComments = !this.showComments;\n  }\n  addComment() {\n    if (this.newComment.trim()) {\n      this.commented.emit({\n        postId: this.post._id,\n        comment: this.newComment.trim()\n      });\n      this.newComment = '';\n    }\n  }\n  sharePost() {\n    this.shared.emit(this.post._id);\n  }\n  // E-commerce methods\n  isInWishlist(productId) {\n    return this.wishlistService.isInWishlist(productId);\n  }\n  addToWishlist(productId) {\n    this.wishlistService.toggleWishlist(productId).subscribe({\n      next: response => {\n        if (this.isInWishlist(productId)) {\n          this.showNotification('Removed from wishlist', 'info');\n        } else {\n          this.showNotification('Added to wishlist ❤️', 'success');\n        }\n      },\n      error: error => {\n        console.error('Wishlist error:', error);\n        // Fallback to offline mode\n        this.wishlistService.toggleWishlistOffline(this.getProductById(productId));\n        this.showNotification(this.isInWishlist(productId) ? 'Removed from wishlist' : 'Added to wishlist ❤️', 'success');\n      }\n    });\n  }\n  addToCart(productId) {\n    this.cartService.addToCart(productId, 1).subscribe({\n      next: response => {\n        if (response.success) {\n          this.showNotification('Added to cart 🛒', 'success');\n        }\n      },\n      error: error => {\n        console.error('Cart error:', error);\n        this.showNotification('Failed to add to cart', 'error');\n      }\n    });\n  }\n  buyNow(productId) {\n    this.cartService.addToCart(productId, 1).subscribe({\n      next: response => {\n        if (response.success) {\n          this.showNotification('Redirecting to checkout...', 'info');\n          this.router.navigate(['/shop/checkout']);\n        }\n      },\n      error: error => {\n        console.error('Buy now error:', error);\n        this.showNotification('Failed to process purchase', 'error');\n      }\n    });\n  }\n  getProductById(productId) {\n    // Find product in post's products array\n    const productTag = this.post.products.find(p => p.product._id === productId);\n    return productTag ? productTag.product : null;\n  }\n  onBuyNow(productId) {\n    this.buyNow(productId);\n  }\n  // Media handling methods\n  getUserAvatarUrl(url) {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n  getProductImageUrl(url) {\n    return this.mediaService.getSafeImageUrl(url, 'product');\n  }\n  handleImageError(event, type = 'post') {\n    this.mediaService.handleImageError(event, type);\n  }\n  handleVideoError(event) {\n    console.error('Video load error:', event);\n    // Could implement fallback to thumbnail or different video\n  }\n  // Removed duplicate method - using onMediaLoadComplete instead\n  // Video control methods\n  toggleVideoPlay() {\n    if (!this.videoPlayer?.nativeElement) return;\n    const video = this.videoPlayer.nativeElement;\n    if (video.paused) {\n      video.play();\n      this.isVideoPlaying = true;\n      this.startVideoProgress();\n    } else {\n      video.pause();\n      this.isVideoPlaying = false;\n      this.stopVideoProgress();\n    }\n  }\n  startVideoProgress() {\n    if (this.videoProgressInterval) {\n      clearInterval(this.videoProgressInterval);\n    }\n    this.videoProgressInterval = window.setInterval(() => {\n      if (this.videoPlayer?.nativeElement) {\n        const video = this.videoPlayer.nativeElement;\n        this.videoDuration = video.duration || 0;\n        this.videoProgress = this.videoDuration > 0 ? video.currentTime / this.videoDuration * 100 : 0;\n        if (video.ended) {\n          this.isVideoPlaying = false;\n          this.stopVideoProgress();\n        }\n      }\n    }, 100);\n  }\n  stopVideoProgress() {\n    if (this.videoProgressInterval) {\n      clearInterval(this.videoProgressInterval);\n      this.videoProgressInterval = undefined;\n    }\n  }\n  // Media navigation methods with enhanced transitions\n  nextMedia() {\n    if (this.currentMediaIndex < this.mediaItems.length - 1) {\n      this.currentMediaIndex++;\n      this.currentMedia = this.mediaItems[this.currentMediaIndex];\n      this.resetVideoState();\n      this.preloadCurrentMedia();\n      this.trackMediaView();\n    }\n  }\n  previousMedia() {\n    if (this.currentMediaIndex > 0) {\n      this.currentMediaIndex--;\n      this.currentMedia = this.mediaItems[this.currentMediaIndex];\n      this.resetVideoState();\n      this.preloadCurrentMedia();\n      this.trackMediaView();\n    }\n  }\n  goToMedia(index) {\n    if (index >= 0 && index < this.mediaItems.length) {\n      this.currentMediaIndex = index;\n      this.currentMedia = this.mediaItems[index];\n      this.resetVideoState();\n      this.preloadCurrentMedia();\n      this.trackMediaView();\n    }\n  }\n  trackMediaView() {\n    // Track media view for analytics\n    console.log(`Viewing media ${this.currentMediaIndex + 1} of ${this.mediaItems.length}: ${this.currentMedia.type}`);\n  }\n  resetVideoState() {\n    this.isVideoPlaying = false;\n    this.videoProgress = 0;\n    this.stopVideoProgress();\n  }\n  ngOnDestroy() {\n    this.stopVideoProgress();\n  }\n  // Instagram-like interactions\n  onDoubleTap() {\n    this.toggleLike();\n    this.showHeartAnimation = true;\n    setTimeout(() => {\n      this.showHeartAnimation = false;\n    }, 1000);\n  }\n  toggleProductTags() {\n    // Toggle product tags visibility (Instagram-style)\n    if (this.post.products && this.post.products.length > 0) {\n      this.showProductTags = !this.showProductTags;\n      // Auto-hide after 3 seconds\n      if (this.showProductTags) {\n        setTimeout(() => {\n          this.showProductTags = false;\n        }, 3000);\n      }\n    }\n  }\n  viewProduct(productId) {\n    this.router.navigate(['/shop/product', productId]);\n  }\n  formatDuration(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  onMediaLoadComplete() {\n    // Media loaded successfully\n    if (this.currentMedia?.type === 'video') {\n      this.showVideoControls = true;\n      setTimeout(() => {\n        this.showVideoControls = false;\n      }, 3000);\n    }\n  }\n  showNotification(message, type) {\n    // Create notification element\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n    // Add styles\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n    // Add animation styles\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes slideIn {\n        from { transform: translateX(100%); opacity: 0; }\n        to { transform: translateX(0); opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n    document.body.appendChild(notification);\n    // Remove after 3 seconds\n    setTimeout(() => {\n      notification.remove();\n      style.remove();\n    }, 3000);\n  }\n  static {\n    this.ɵfac = function PostCardComponent_Factory(t) {\n      return new (t || PostCardComponent)(i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i2.WishlistService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PostCardComponent,\n      selectors: [[\"app-post-card\"]],\n      viewQuery: function PostCardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoPlayer = _t.first);\n        }\n      },\n      inputs: {\n        post: \"post\"\n      },\n      outputs: {\n        liked: \"liked\",\n        commented: \"commented\",\n        shared: \"shared\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 47,\n      vars: 31,\n      consts: [[\"videoPlayer\", \"\"], [1, \"post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"error\", \"src\", \"alt\"], [1, \"user-details\"], [1, \"more-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\"], [\"class\", \"media-loading\", 4, \"ngIf\"], [\"class\", \"post-image\", 3, \"src\", \"alt\", \"error\", \"load\", \"dblclick\", \"click\", 4, \"ngIf\"], [\"class\", \"post-video\", \"playsinline\", \"\", 3, \"src\", \"poster\", \"muted\", \"loop\", \"click\", \"dblclick\", \"loadeddata\", \"error\", 4, \"ngIf\"], [\"class\", \"video-controls\", 3, \"visible\", 4, \"ngIf\"], [\"class\", \"media-navigation\", 4, \"ngIf\"], [1, \"heart-animation\"], [1, \"fas\", \"fa-heart\"], [\"class\", \"product-tags\", 3, \"show-tags\", 4, \"ngIf\"], [\"class\", \"shopping-indicator\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"far\", \"fa-share\"], [1, \"save-btn\", 3, \"click\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"post-stats\"], [1, \"post-caption\"], [3, \"innerHTML\"], [1, \"post-comments\"], [\"class\", \"view-comments\", 4, \"ngIf\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"add-comment\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"class\", \"post-comment-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"media-loading\"], [1, \"loading-spinner\"], [1, \"post-image\", 3, \"error\", \"load\", \"dblclick\", \"click\", \"src\", \"alt\"], [\"playsinline\", \"\", 1, \"post-video\", 3, \"click\", \"dblclick\", \"loadeddata\", \"error\", \"src\", \"poster\", \"muted\", \"loop\"], [1, \"video-controls\"], [1, \"play-pause-btn\", 3, \"click\"], [\"class\", \"video-info\", 4, \"ngIf\"], [\"class\", \"video-progress\", 4, \"ngIf\"], [1, \"video-info\"], [1, \"video-duration\"], [1, \"video-progress\"], [1, \"progress-bar\"], [1, \"media-navigation\"], [1, \"nav-btn\", \"prev-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"nav-btn\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"media-indicators\"], [\"class\", \"indicator\", 3, \"active\", \"video\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"indicator\", 3, \"click\"], [\"class\", \"fas fa-play-circle\", 4, \"ngIf\"], [1, \"fas\", \"fa-play-circle\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"top\", \"left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"tag-dot\"], [1, \"tag-pulse\"], [1, \"product-info\"], [3, \"error\", \"src\", \"alt\"], [1, \"product-details\"], [1, \"product-quick-actions\"], [\"title\", \"Buy Now\", 1, \"quick-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [\"title\", \"Add to Cart\", 1, \"quick-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-cart-plus\"], [\"title\", \"Add to Wishlist\", 1, \"quick-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"shopping-indicator\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"ecommerce-actions\"], [1, \"products-showcase\"], [\"class\", \"product-showcase\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-showcase\"], [1, \"product-thumb\", 3, \"src\", \"alt\"], [1, \"product-info-inline\"], [1, \"price\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy-now\", 3, \"click\"], [1, \"view-comments\"], [1, \"comment\"], [1, \"post-comment-btn\", 3, \"click\"]],\n      template: function PostCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"article\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"img\", 4);\n          i0.ɵɵlistener(\"error\", function PostCardComponent_Template_img_error_3_listener($event) {\n            return ctx.handleImageError($event, \"user\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"h4\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"button\", 6);\n          i0.ɵɵelement(10, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtemplate(12, PostCardComponent_div_12_Template, 2, 0, \"div\", 9)(13, PostCardComponent_img_13_Template, 1, 2, \"img\", 10)(14, PostCardComponent_video_14_Template, 2, 4, \"video\", 11)(15, PostCardComponent_div_15_Template, 5, 8, \"div\", 12)(16, PostCardComponent_div_16_Template, 7, 5, \"div\", 13);\n          i0.ɵɵelementStart(17, \"div\", 14);\n          i0.ɵɵelement(18, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, PostCardComponent_div_19_Template, 2, 3, \"div\", 16)(20, PostCardComponent_div_20_Template, 4, 0, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 18)(22, \"div\", 19)(23, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_23_listener() {\n            return ctx.toggleLike();\n          });\n          i0.ɵɵelement(24, \"i\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_25_listener() {\n            return ctx.toggleComments();\n          });\n          i0.ɵɵelement(26, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_27_listener() {\n            return ctx.sharePost();\n          });\n          i0.ɵɵelement(28, \"i\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function PostCardComponent_Template_button_click_29_listener() {\n            return ctx.toggleSave();\n          });\n          i0.ɵɵelement(30, \"i\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, PostCardComponent_div_31_Template, 3, 1, \"div\", 25);\n          i0.ɵɵelementStart(32, \"div\", 26)(33, \"p\")(34, \"strong\");\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 27)(37, \"p\")(38, \"strong\");\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"span\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 29);\n          i0.ɵɵtemplate(42, PostCardComponent_p_42_Template, 2, 1, \"p\", 30)(43, PostCardComponent_div_43_Template, 5, 2, \"div\", 31);\n          i0.ɵɵelementStart(44, \"div\", 32)(45, \"input\", 33);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PostCardComponent_Template_input_ngModelChange_45_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newComment, $event) || (ctx.newComment = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function PostCardComponent_Template_input_keyup_enter_45_listener() {\n            return ctx.addComment();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, PostCardComponent_button_46_Template, 2, 0, \"button\", 34);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.getUserAvatarUrl(ctx.post.user.avatar), i0.ɵɵsanitizeUrl)(\"alt\", ctx.post.user.fullName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.post.user.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getTimeAgo(ctx.post.createdAt));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"video-container\", ctx.currentMedia.type === \"video\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentMedia);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.currentMedia == null ? null : ctx.currentMedia.type) === \"image\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.currentMedia == null ? null : ctx.currentMedia.type) === \"video\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.currentMedia == null ? null : ctx.currentMedia.type) === \"video\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.mediaItems.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"animate\", ctx.showHeartAnimation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.post.products && ctx.post.products.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.post.products && ctx.post.products.length > 0 && !ctx.showProductTags);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"liked\", ctx.isLiked);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"saved\", ctx.isSaved);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.post.products.length > 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", ctx.likesCount, \" likes\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.post.user.username);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"innerHTML\", ctx.formatCaption(ctx.post.caption), i0.ɵɵsanitizeHtml);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.post.comments.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.getRecentComments());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newComment);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.newComment.trim());\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\".post[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.user-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.user-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n\\n.more-options[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  color: #262626;\\n}\\n\\n.post-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  aspect-ratio: 1;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n}\\n\\n.post-media.video-container[_ngcontent-%COMP%] {\\n  background: #000;\\n}\\n\\n.media-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  background: #f8f9fa;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid #e3e3e3;\\n  border-top: 3px solid #007bff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.post-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n  cursor: pointer;\\n  transition: transform 0.1s ease;\\n}\\n\\n.post-image[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n\\n.post-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n  cursor: pointer;\\n  transition: transform 0.1s ease;\\n}\\n\\n.post-video[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n\\n\\n\\n.video-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: rgba(0, 0, 0, 0.3);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  pointer-events: none;\\n}\\n\\n.video-controls.visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  pointer-events: auto;\\n}\\n\\n.post-media[_ngcontent-%COMP%]:hover   .video-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  pointer-events: auto;\\n}\\n\\n.video-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  right: 15px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.video-duration[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n}\\n\\n.play-pause-btn[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-size: 20px;\\n  color: #333;\\n}\\n\\n.play-pause-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n\\n.play-pause-btn.playing[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n}\\n\\n.video-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10px;\\n  left: 10px;\\n  right: 10px;\\n  height: 3px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n  overflow: hidden;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #007bff;\\n  border-radius: 2px;\\n  transition: width 0.1s ease;\\n}\\n\\n\\n\\n.media-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n}\\n\\n.nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5);\\n  border: none;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  pointer-events: auto;\\n  opacity: 0;\\n}\\n\\n.post-media[_ngcontent-%COMP%]:hover   .nav-btn[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.nav-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.8);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n\\n.nav-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n\\n.prev-btn[_ngcontent-%COMP%] {\\n  left: 10px;\\n}\\n\\n.next-btn[_ngcontent-%COMP%] {\\n  right: 10px;\\n}\\n\\n.media-indicators[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  display: flex;\\n  gap: 8px;\\n  pointer-events: auto;\\n}\\n\\n.indicator[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.5);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.indicator.active[_ngcontent-%COMP%] {\\n  background: white;\\n  transform: scale(1.2);\\n}\\n\\n.indicator.video[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 12px;\\n  height: 12px;\\n}\\n\\n.indicator.video[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 6px;\\n  color: #333;\\n}\\n\\n.indicator.video.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n\\n\\n.heart-animation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  pointer-events: none;\\n  opacity: 0;\\n  z-index: 1000;\\n}\\n\\n.heart-animation.animate[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartPop 1s ease-out;\\n}\\n\\n.heart-animation[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 80px;\\n  color: #e91e63;\\n  filter: drop-shadow(0 0 10px rgba(233, 30, 99, 0.5));\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartPop {\\n  0% {\\n    opacity: 0;\\n    transform: translate(-50%, -50%) scale(0.5);\\n  }\\n  15% {\\n    opacity: 1;\\n    transform: translate(-50%, -50%) scale(1.2);\\n  }\\n  30% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  100% {\\n    opacity: 0;\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .post-card[_ngcontent-%COMP%] {\\n    margin: 0 -1rem;\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n  }\\n  .post-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .user-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .user-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .post-media[_ngcontent-%COMP%] {\\n    aspect-ratio: 4/5; \\n\\n  }\\n  .nav-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 14px;\\n  }\\n  .play-pause-btn[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    font-size: 18px;\\n  }\\n  .post-actions[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 1.2rem;\\n  }\\n  .post-content[_ngcontent-%COMP%] {\\n    padding: 0 1rem 1rem;\\n  }\\n  .likes-count[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .post-caption[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .comments-section[_ngcontent-%COMP%] {\\n    padding: 0 1rem 1rem;\\n  }\\n  .comment-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    font-size: 0.9rem;\\n  }\\n  .product-showcase[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .product-item[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n  }\\n  .product-image[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .product-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .product-price[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    gap: 0.25rem;\\n  }\\n  .product-btn[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.8rem;\\n    font-size: 0.7rem;\\n  }\\n}\\n\\n\\n@media (hover: none) and (pointer: coarse) {\\n  .nav-btn[_ngcontent-%COMP%], .play-pause-btn[_ngcontent-%COMP%], .action-btn[_ngcontent-%COMP%], .product-btn[_ngcontent-%COMP%] {\\n    transform: none;\\n    transition: background-color 0.2s ease;\\n  }\\n  .nav-btn[_ngcontent-%COMP%]:active, .play-pause-btn[_ngcontent-%COMP%]:active, .action-btn[_ngcontent-%COMP%]:active, .product-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n  }\\n  .post-media[_ngcontent-%COMP%]:hover   .nav-btn[_ngcontent-%COMP%] {\\n    opacity: 1;\\n  }\\n  .post-media[_ngcontent-%COMP%]:hover   .video-controls[_ngcontent-%COMP%] {\\n    opacity: 1;\\n  }\\n}\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.product-tags.show-tags[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  cursor: pointer;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: #fff;\\n  border-radius: 50%;\\n  border: 2px solid var(--primary-color);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: var(--primary-color);\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(0, 149, 246, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(0, 149, 246, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(0, 149, 246, 0);\\n  }\\n}\\n.product-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -120px;\\n  left: -100px;\\n  background: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  padding: 12px;\\n  width: 200px;\\n  display: none;\\n  z-index: 10;\\n}\\n\\n.product-tag[_ngcontent-%COMP%]:hover   .product-info[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 4px;\\n  object-fit: cover;\\n  float: left;\\n  margin-right: 12px;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.buy-now-btn[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border: none;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  width: 100%;\\n}\\n\\n.product-quick-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n  margin-top: 8px;\\n  justify-content: center;\\n}\\n\\n.quick-btn[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 10px;\\n  transition: all 0.3s ease;\\n}\\n\\n.quick-btn.buy-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\\n  color: white;\\n}\\n\\n.quick-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4834d4, #686de0);\\n  color: white;\\n}\\n\\n.quick-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9ff3, #f368e0);\\n  color: white;\\n}\\n\\n.quick-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n.shopping-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  left: 12px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 12px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  animation: _ngcontent-%COMP%_fadeInUp 0.3s ease;\\n  cursor: pointer;\\n}\\n.shopping-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 16px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 20px;\\n  color: #262626;\\n  transition: color 0.2s;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover, .save-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.like-btn.liked[_ngcontent-%COMP%] {\\n  color: #ed4956;\\n}\\n\\n.save-btn.saved[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.post-stats[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.post-stats[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.post-caption[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.post-comments[_ngcontent-%COMP%] {\\n  padding: 0 16px 16px;\\n}\\n\\n.view-comments[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n  cursor: pointer;\\n  margin-bottom: 8px;\\n}\\n\\n.comment[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.comment[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.add-comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.add-comment[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  font-size: 14px;\\n  background: transparent;\\n}\\n\\n.post-comment-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  cursor: pointer;\\n  font-size: 14px;\\n}\\n\\n.hashtag[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  cursor: pointer;\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #efefef;\\n  padding: 16px;\\n  background: #fafafa;\\n}\\n\\n.products-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.product-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: white;\\n  padding: 12px;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.product-thumb[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 6px;\\n  object-fit: cover;\\n}\\n\\n.product-info-inline[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-info-inline[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n  color: #262626;\\n}\\n\\n.product-info-inline[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #e91e63;\\n  margin: 0 0 8px 0;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%] {\\n  background: none;\\n  border: 1px solid #ddd;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 6px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  color: #666;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%]:hover {\\n  border-color: #e91e63;\\n  color: #e91e63;\\n}\\n\\n.btn-wishlist.active[_ngcontent-%COMP%] {\\n  background: #e91e63;\\n  border-color: #e91e63;\\n  color: white;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: white;\\n  border: none;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  transition: background 0.2s;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%]:hover {\\n  background: #1976d2;\\n}\\n\\n.btn-buy-now[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background 0.2s;\\n}\\n\\n.btn-buy-now[_ngcontent-%COMP%]:hover {\\n  background: #f57c00;\\n}\\n\\n@media (max-width: 768px) {\\n  .product-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n    align-items: stretch;\\n  }\\n  .btn-cart[_ngcontent-%COMP%], .btn-buy-now[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "PostCardComponent_img_13_Template_img_error_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "handleImageError", "PostCardComponent_img_13_Template_img_load_0_listener", "onMediaLoadComplete", "PostCardComponent_img_13_Template_img_dblclick_0_listener", "onDoubleTap", "PostCardComponent_img_13_Template_img_click_0_listener", "toggleProductTags", "ɵɵproperty", "currentMedia", "url", "ɵɵsanitizeUrl", "alt", "PostCardComponent_video_14_Template_video_click_0_listener", "_r3", "toggleVideoPlay", "PostCardComponent_video_14_Template_video_dblclick_0_listener", "PostCardComponent_video_14_Template_video_loadeddata_0_listener", "PostCardComponent_video_14_Template_video_error_0_listener", "handleVideoError", "thumbnailUrl", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "formatDuration", "duration", "ɵɵstyleProp", "videoProgress", "PostCardComponent_div_15_Template_button_click_1_listener", "_r4", "ɵɵtemplate", "PostCardComponent_div_15_div_3_Template", "PostCardComponent_div_15_div_4_Template", "ɵɵclassProp", "showVideoControls", "isVideoPlaying", "ɵɵclassMap", "videoDuration", "PostCardComponent_div_16_span_6_Template_span_click_0_listener", "i_r7", "_r6", "index", "goToMedia", "PostCardComponent_div_16_span_6_i_1_Template", "currentMediaIndex", "media_r8", "type", "PostCardComponent_div_16_Template_button_click_1_listener", "_r5", "previousMedia", "PostCardComponent_div_16_Template_button_click_3_listener", "nextMedia", "PostCardComponent_div_16_span_6_Template", "mediaItems", "length", "PostCardComponent_div_19_div_1_Template_div_click_0_listener", "productTag_r10", "_r9", "$implicit", "viewProduct", "product", "_id", "stopPropagation", "PostCardComponent_div_19_div_1_Template_img_error_4_listener", "PostCardComponent_div_19_div_1_Template_button_click_12_listener", "onBuyNow", "PostCardComponent_div_19_div_1_Template_button_click_14_listener", "addToCart", "PostCardComponent_div_19_div_1_Template_button_click_16_listener", "addToWishlist", "position", "y", "x", "getProductImageUrl", "images", "name", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "price", "PostCardComponent_div_19_div_1_Template", "showProductTags", "post", "products", "PostCardComponent_div_31_div_2_Template_button_click_9_listener", "productTag_r12", "_r11", "PostCardComponent_div_31_div_2_Template_button_click_11_listener", "PostCardComponent_div_31_div_2_Template_button_click_14_listener", "buyNow", "isInWishlist", "PostCardComponent_div_31_div_2_Template", "comments", "comment_r13", "user", "username", "text", "PostCardComponent_button_46_Template_button_click_0_listener", "_r14", "addComment", "PostCardComponent", "constructor", "cartService", "wishlistService", "router", "mediaService", "liked", "commented", "shared", "isLiked", "isSaved", "likesCount", "newComment", "showComments", "wishlistItems", "cartItems", "showHeartAnimation", "ngOnInit", "analytics", "likes", "loadWishlistItems", "loadCartItems", "initializeMedia", "contentHint", "caption", "hashtags", "join", "processMediaItems", "media", "id", "getSafeImageUrl", "preloadCurrentMedia", "preloadMedia", "catch", "error", "console", "warn", "getTimeAgo", "date", "now", "Date", "diff", "getTime", "hours", "Math", "floor", "days", "formatCaption", "replace", "getRecentComments", "slice", "toggleLike", "emit", "toggleSave", "toggleComments", "trim", "postId", "comment", "sharePost", "productId", "toggleWishlist", "subscribe", "next", "response", "showNotification", "toggleWishlistOffline", "getProductById", "success", "navigate", "productTag", "find", "p", "getUserAvatarUrl", "event", "videoPlayer", "nativeElement", "video", "paused", "play", "startVideoProgress", "pause", "stopVideoProgress", "videoProgressInterval", "clearInterval", "window", "setInterval", "currentTime", "ended", "undefined", "resetVideoState", "trackMediaView", "log", "ngOnDestroy", "setTimeout", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "message", "notification", "document", "createElement", "className", "textContent", "style", "cssText", "head", "append<PERSON><PERSON><PERSON>", "body", "remove", "ɵɵdirectiveInject", "i1", "CartService", "i2", "WishlistService", "i3", "Router", "i4", "MediaService", "selectors", "viewQuery", "PostCardComponent_Query", "rf", "ctx", "PostCardComponent_Template_img_error_3_listener", "PostCardComponent_div_12_Template", "PostCardComponent_img_13_Template", "PostCardComponent_video_14_Template", "PostCardComponent_div_15_Template", "PostCardComponent_div_16_Template", "PostCardComponent_div_19_Template", "PostCardComponent_div_20_Template", "PostCardComponent_Template_button_click_23_listener", "PostCardComponent_Template_button_click_25_listener", "PostCardComponent_Template_button_click_27_listener", "PostCardComponent_Template_button_click_29_listener", "PostCardComponent_div_31_Template", "PostCardComponent_p_42_Template", "PostCardComponent_div_43_Template", "ɵɵtwoWayListener", "PostCardComponent_Template_input_ngModelChange_45_listener", "ɵɵtwoWayBindingSet", "PostCardComponent_Template_input_keyup_enter_45_listener", "PostCardComponent_button_46_Template", "avatar", "fullName", "createdAt", "ɵɵsanitizeHtml", "ɵɵtwoWayProperty", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\post-card\\post-card.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, ViewChild, ElementRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { Post } from '../../../../core/models/post.model';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { MediaService, MediaItem } from '../../../../core/services/media.service';\n\n@Component({\n  selector: 'app-post-card',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <article class=\"post\">\n      <!-- Post Header -->\n      <div class=\"post-header\">\n        <div class=\"user-info\">\n          <img\n            [src]=\"getUserAvatarUrl(post.user.avatar)\"\n            [alt]=\"post.user.fullName\"\n            class=\"user-avatar\"\n            (error)=\"handleImageError($event, 'user')\"\n          >\n          <div class=\"user-details\">\n            <h4>{{ post.user.username }}</h4>\n            <span>{{ getTimeAgo(post.createdAt) }}</span>\n          </div>\n        </div>\n        <button class=\"more-options\">\n          <i class=\"fas fa-ellipsis-h\"></i>\n        </button>\n      </div>\n\n      <!-- Post Media -->\n      <div class=\"post-media\" [class.video-container]=\"currentMedia.type === 'video'\">\n        <!-- Loading Placeholder -->\n        <div *ngIf=\"!currentMedia\" class=\"media-loading\">\n          <div class=\"loading-spinner\"></div>\n        </div>\n\n        <!-- Image Media -->\n        <img\n          *ngIf=\"currentMedia?.type === 'image'\"\n          [src]=\"currentMedia.url\"\n          [alt]=\"currentMedia.alt\"\n          class=\"post-image\"\n          (error)=\"handleImageError($event)\"\n          (load)=\"onMediaLoadComplete()\"\n          (dblclick)=\"onDoubleTap()\"\n          (click)=\"toggleProductTags()\"\n        >\n\n        <!-- Video Media -->\n        <video\n          *ngIf=\"currentMedia?.type === 'video'\"\n          #videoPlayer\n          class=\"post-video\"\n          [src]=\"currentMedia.url\"\n          [poster]=\"currentMedia.thumbnailUrl\"\n          [muted]=\"true\"\n          [loop]=\"true\"\n          playsinline\n          (click)=\"toggleVideoPlay()\"\n          (dblclick)=\"onDoubleTap()\"\n          (loadeddata)=\"onMediaLoadComplete()\"\n          (error)=\"handleVideoError($event)\"\n        ></video>\n\n        <!-- Video Controls Overlay -->\n        <div *ngIf=\"currentMedia?.type === 'video'\" class=\"video-controls\" [class.visible]=\"showVideoControls\">\n          <button class=\"play-pause-btn\" (click)=\"toggleVideoPlay()\" [class.playing]=\"isVideoPlaying\">\n            <i [class]=\"isVideoPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\n          </button>\n          <div class=\"video-info\" *ngIf=\"currentMedia.duration\">\n            <span class=\"video-duration\">{{ formatDuration(currentMedia.duration) }}</span>\n          </div>\n          <div class=\"video-progress\" *ngIf=\"videoDuration > 0\">\n            <div class=\"progress-bar\" [style.width.%]=\"videoProgress\"></div>\n          </div>\n        </div>\n\n        <!-- Media Navigation (for multiple media) -->\n        <div *ngIf=\"mediaItems.length > 1\" class=\"media-navigation\">\n          <button\n            class=\"nav-btn prev-btn\"\n            (click)=\"previousMedia()\"\n            [disabled]=\"currentMediaIndex === 0\"\n            [attr.aria-label]=\"'Previous media'\"\n          >\n            <i class=\"fas fa-chevron-left\"></i>\n          </button>\n          <button\n            class=\"nav-btn next-btn\"\n            (click)=\"nextMedia()\"\n            [disabled]=\"currentMediaIndex === mediaItems.length - 1\"\n            [attr.aria-label]=\"'Next media'\"\n          >\n            <i class=\"fas fa-chevron-right\"></i>\n          </button>\n\n          <!-- Media Indicators -->\n          <div class=\"media-indicators\">\n            <span\n              *ngFor=\"let media of mediaItems; let i = index\"\n              class=\"indicator\"\n              [class.active]=\"i === currentMediaIndex\"\n              [class.video]=\"media.type === 'video'\"\n              (click)=\"goToMedia(i)\"\n              [attr.aria-label]=\"'Go to media ' + (i + 1)\"\n            >\n              <i *ngIf=\"media.type === 'video'\" class=\"fas fa-play-circle\"></i>\n            </span>\n          </div>\n        </div>\n\n        <!-- Double Tap Heart Animation -->\n        <div class=\"heart-animation\" [class.animate]=\"showHeartAnimation\">\n          <i class=\"fas fa-heart\"></i>\n        </div>\n\n        <!-- Product Tags (Instagram-style - hidden by default) -->\n        <div class=\"product-tags\"\n             [class.show-tags]=\"showProductTags\"\n             *ngIf=\"post.products && post.products.length > 0\">\n          <div\n            *ngFor=\"let productTag of post.products\"\n            class=\"product-tag\"\n            [style.top.%]=\"productTag.position.y || 50\"\n            [style.left.%]=\"productTag.position.x || 50\"\n            (click)=\"viewProduct(productTag.product._id); $event.stopPropagation()\"\n          >\n            <div class=\"tag-dot\">\n              <div class=\"tag-pulse\"></div>\n            </div>\n            <div class=\"product-info\">\n              <img\n                [src]=\"getProductImageUrl(productTag.product.images[0].url || '')\"\n                [alt]=\"productTag.product.name\"\n                (error)=\"handleImageError($event, 'product')\"\n              >\n              <div class=\"product-details\">\n                <h5>{{ productTag.product.name }}</h5>\n                <p>₹{{ productTag.product.price | number }}</p>\n                <div class=\"product-quick-actions\">\n                  <button class=\"quick-btn buy-btn\" (click)=\"onBuyNow(productTag.product._id)\" title=\"Buy Now\">\n                    <i class=\"fas fa-bolt\"></i>\n                  </button>\n                  <button class=\"quick-btn cart-btn\" (click)=\"addToCart(productTag.product._id)\" title=\"Add to Cart\">\n                    <i class=\"fas fa-cart-plus\"></i>\n                  </button>\n                  <button class=\"quick-btn wishlist-btn\" (click)=\"addToWishlist(productTag.product._id)\" title=\"Add to Wishlist\">\n                    <i class=\"fas fa-heart\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Shopping indicator (Instagram-style) -->\n        <div class=\"shopping-indicator\" *ngIf=\"post.products && post.products.length > 0 && !showProductTags\">\n          <i class=\"fas fa-shopping-bag\"></i>\n          <span>Tap to view products</span>\n        </div>\n      </div>\n\n      <!-- Post Actions -->\n      <div class=\"post-actions\">\n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isLiked\"\n            (click)=\"toggleLike()\"\n          >\n            <i [class]=\"isLiked ? 'fas fa-heart' : 'far fa-heart'\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"toggleComments()\">\n            <i class=\"far fa-comment\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"sharePost()\">\n            <i class=\"far fa-share\"></i>\n          </button>\n        </div>\n        <button class=\"save-btn\" [class.saved]=\"isSaved\" (click)=\"toggleSave()\">\n          <i [class]=\"isSaved ? 'fas fa-bookmark' : 'far fa-bookmark'\"></i>\n        </button>\n      </div>\n\n      <!-- E-commerce Actions -->\n      <div class=\"ecommerce-actions\" *ngIf=\"post.products.length > 0\">\n        <div class=\"products-showcase\">\n          <div *ngFor=\"let productTag of post.products\" class=\"product-showcase\">\n            <img [src]=\"productTag.product.images[0].url\" [alt]=\"productTag.product.name\" class=\"product-thumb\">\n            <div class=\"product-info-inline\">\n              <h5>{{ productTag.product.name }}</h5>\n              <p class=\"price\">₹{{ productTag.product.price | number }}</p>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(productTag.product._id)\" [class.active]=\"isInWishlist(productTag.product._id)\">\n                  <i [class]=\"isInWishlist(productTag.product._id) ? 'fas fa-heart' : 'far fa-heart'\"></i>\n                </button>\n                <button class=\"btn-cart\" (click)=\"addToCart(productTag.product._id)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                  Add to Cart\n                </button>\n                <button class=\"btn-buy-now\" (click)=\"buyNow(productTag.product._id)\">\n                  Buy Now\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Post Stats -->\n      <div class=\"post-stats\">\n        <p><strong>{{ likesCount }} likes</strong></p>\n      </div>\n\n      <!-- Post Caption -->\n      <div class=\"post-caption\">\n        <p>\n          <strong>{{ post.user.username }}</strong> \n          <span [innerHTML]=\"formatCaption(post.caption)\"></span>\n        </p>\n      </div>\n\n      <!-- Post Comments -->\n      <div class=\"post-comments\">\n        <p class=\"view-comments\" *ngIf=\"post.comments.length > 0\">\n          View all {{ post.comments.length }} comments\n        </p>\n        \n        <!-- Recent Comments -->\n        <div *ngFor=\"let comment of getRecentComments()\" class=\"comment\">\n          <p>\n            <strong>{{ comment.user.username }}</strong>\n            {{ comment.text }}\n          </p>\n        </div>\n\n        <!-- Add Comment -->\n        <div class=\"add-comment\">\n          <input \n            type=\"text\" \n            placeholder=\"Add a comment...\"\n            [(ngModel)]=\"newComment\"\n            (keyup.enter)=\"addComment()\"\n          >\n          <button \n            *ngIf=\"newComment.trim()\" \n            (click)=\"addComment()\"\n            class=\"post-comment-btn\"\n          >\n            Post\n          </button>\n        </div>\n      </div>\n    </article>\n  `,\n  styles: [`\n    .post {\n      background: #fff;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      overflow: hidden;\n    }\n\n    .post-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 16px;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .user-details h4 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 2px;\n    }\n\n    .user-details span {\n      font-size: 12px;\n      color: #8e8e8e;\n    }\n\n    .more-options {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 8px;\n      color: #262626;\n    }\n\n    .post-media {\n      position: relative;\n      width: 100%;\n      aspect-ratio: 1;\n      overflow: hidden;\n      background: #f8f9fa;\n    }\n\n    .post-media.video-container {\n      background: #000;\n    }\n\n    .media-loading {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      background: #f8f9fa;\n    }\n\n    .loading-spinner {\n      width: 40px;\n      height: 40px;\n      border: 3px solid #e3e3e3;\n      border-top: 3px solid #007bff;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .post-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      display: block;\n      cursor: pointer;\n      transition: transform 0.1s ease;\n    }\n\n    .post-image:active {\n      transform: scale(0.98);\n    }\n\n    .post-video {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      display: block;\n      cursor: pointer;\n      transition: transform 0.1s ease;\n    }\n\n    .post-video:active {\n      transform: scale(0.98);\n    }\n\n    /* Video Controls */\n    .video-controls {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: rgba(0, 0, 0, 0.3);\n      opacity: 0;\n      transition: opacity 0.3s ease;\n      pointer-events: none;\n    }\n\n    .video-controls.visible {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    .post-media:hover .video-controls {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    .video-info {\n      position: absolute;\n      top: 15px;\n      right: 15px;\n      background: rgba(0, 0, 0, 0.7);\n      color: white;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 12px;\n      font-weight: 500;\n    }\n\n    .video-duration {\n      font-family: monospace;\n    }\n\n    .play-pause-btn {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.9);\n      border: none;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      font-size: 20px;\n      color: #333;\n    }\n\n    .play-pause-btn:hover {\n      background: white;\n      transform: scale(1.1);\n    }\n\n    .play-pause-btn.playing {\n      background: rgba(0, 0, 0, 0.7);\n      color: white;\n    }\n\n    .video-progress {\n      position: absolute;\n      bottom: 10px;\n      left: 10px;\n      right: 10px;\n      height: 3px;\n      background: rgba(255, 255, 255, 0.3);\n      border-radius: 2px;\n      overflow: hidden;\n    }\n\n    .progress-bar {\n      height: 100%;\n      background: #007bff;\n      border-radius: 2px;\n      transition: width 0.1s ease;\n    }\n\n    /* Media Navigation */\n    .media-navigation {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      pointer-events: none;\n    }\n\n    .nav-btn {\n      position: absolute;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: rgba(0, 0, 0, 0.5);\n      border: none;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      pointer-events: auto;\n      opacity: 0;\n    }\n\n    .post-media:hover .nav-btn {\n      opacity: 1;\n    }\n\n    .nav-btn:hover {\n      background: rgba(0, 0, 0, 0.8);\n      transform: translateY(-50%) scale(1.1);\n    }\n\n    .nav-btn:disabled {\n      opacity: 0.3;\n      cursor: not-allowed;\n    }\n\n    .prev-btn {\n      left: 10px;\n    }\n\n    .next-btn {\n      right: 10px;\n    }\n\n    .media-indicators {\n      position: absolute;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      display: flex;\n      gap: 8px;\n      pointer-events: auto;\n    }\n\n    .indicator {\n      width: 8px;\n      height: 8px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.5);\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n\n    .indicator.active {\n      background: white;\n      transform: scale(1.2);\n    }\n\n    .indicator.video {\n      background: rgba(255, 255, 255, 0.8);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 12px;\n      height: 12px;\n    }\n\n    .indicator.video i {\n      font-size: 6px;\n      color: #333;\n    }\n\n    .indicator.video.active i {\n      color: white;\n    }\n\n    /* Heart Animation */\n    .heart-animation {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      pointer-events: none;\n      opacity: 0;\n      z-index: 1000;\n    }\n\n    .heart-animation.animate {\n      animation: heartPop 1s ease-out;\n    }\n\n    .heart-animation i {\n      font-size: 80px;\n      color: #e91e63;\n      filter: drop-shadow(0 0 10px rgba(233, 30, 99, 0.5));\n    }\n\n    @keyframes heartPop {\n      0% {\n        opacity: 0;\n        transform: translate(-50%, -50%) scale(0.5);\n      }\n      15% {\n        opacity: 1;\n        transform: translate(-50%, -50%) scale(1.2);\n      }\n      30% {\n        transform: translate(-50%, -50%) scale(1);\n      }\n      100% {\n        opacity: 0;\n        transform: translate(-50%, -50%) scale(1);\n      }\n    }\n\n    /* Mobile Responsive Styles */\n    @media (max-width: 768px) {\n      .post-card {\n        margin: 0 -1rem;\n        border-radius: 0;\n        border-left: none;\n        border-right: none;\n      }\n\n      .post-header {\n        padding: 1rem;\n      }\n\n      .user-avatar {\n        width: 35px;\n        height: 35px;\n      }\n\n      .user-details h4 {\n        font-size: 0.9rem;\n      }\n\n      .user-details span {\n        font-size: 0.8rem;\n      }\n\n      .post-media {\n        aspect-ratio: 4/5; /* More mobile-friendly aspect ratio */\n      }\n\n      .nav-btn {\n        width: 35px;\n        height: 35px;\n        font-size: 14px;\n      }\n\n      .play-pause-btn {\n        width: 50px;\n        height: 50px;\n        font-size: 18px;\n      }\n\n      .post-actions {\n        padding: 0.75rem 1rem;\n      }\n\n      .action-btn {\n        width: 35px;\n        height: 35px;\n        font-size: 1.2rem;\n      }\n\n      .post-content {\n        padding: 0 1rem 1rem;\n      }\n\n      .likes-count {\n        font-size: 0.9rem;\n      }\n\n      .post-caption {\n        font-size: 0.9rem;\n      }\n\n      .comments-section {\n        padding: 0 1rem 1rem;\n      }\n\n      .comment-input {\n        padding: 0.75rem;\n        font-size: 0.9rem;\n      }\n\n      .product-showcase {\n        padding: 1rem;\n      }\n\n      .product-item {\n        min-width: 140px;\n      }\n\n      .product-image {\n        width: 60px;\n        height: 60px;\n      }\n\n      .product-info h5 {\n        font-size: 0.8rem;\n      }\n\n      .product-price {\n        font-size: 0.8rem;\n      }\n\n      .product-actions {\n        gap: 0.25rem;\n      }\n\n      .product-btn {\n        padding: 0.4rem 0.8rem;\n        font-size: 0.7rem;\n      }\n    }\n\n    /* Touch-friendly interactions */\n    @media (hover: none) and (pointer: coarse) {\n      .nav-btn,\n      .play-pause-btn,\n      .action-btn,\n      .product-btn {\n        transform: none;\n        transition: background-color 0.2s ease;\n      }\n\n      .nav-btn:active,\n      .play-pause-btn:active,\n      .action-btn:active,\n      .product-btn:active {\n        transform: scale(0.95);\n      }\n\n      .post-media:hover .nav-btn {\n        opacity: 1;\n      }\n\n      .post-media:hover .video-controls {\n        opacity: 1;\n      }\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-tags.show-tags {\n      opacity: 1;\n    }\n\n    .product-tag {\n      position: absolute;\n      cursor: pointer;\n    }\n\n    .tag-dot {\n      width: 20px;\n      height: 20px;\n      background: #fff;\n      border-radius: 50%;\n      border: 2px solid var(--primary-color);\n      position: relative;\n      animation: pulse 2s infinite;\n    }\n\n    .tag-dot::after {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      width: 8px;\n      height: 8px;\n      background: var(--primary-color);\n      border-radius: 50%;\n    }\n\n    @keyframes pulse {\n      0% { box-shadow: 0 0 0 0 rgba(0, 149, 246, 0.7); }\n      70% { box-shadow: 0 0 0 10px rgba(0, 149, 246, 0); }\n      100% { box-shadow: 0 0 0 0 rgba(0, 149, 246, 0); }\n    }\n\n    .product-info {\n      position: absolute;\n      top: -120px;\n      left: -100px;\n      background: #fff;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      padding: 12px;\n      width: 200px;\n      display: none;\n      z-index: 10;\n    }\n\n    .product-tag:hover .product-info {\n      display: block;\n    }\n\n    .product-info img {\n      width: 60px;\n      height: 60px;\n      border-radius: 4px;\n      object-fit: cover;\n      float: left;\n      margin-right: 12px;\n    }\n\n    .product-details h5 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 4px;\n    }\n\n    .product-details p {\n      font-size: 14px;\n      font-weight: 600;\n      color: var(--primary-color);\n      margin-bottom: 8px;\n    }\n\n    .buy-now-btn {\n      background: var(--primary-color);\n      color: #fff;\n      border: none;\n      padding: 6px 12px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      width: 100%;\n    }\n\n    .product-quick-actions {\n      display: flex;\n      gap: 4px;\n      margin-top: 8px;\n      justify-content: center;\n    }\n\n    .quick-btn {\n      width: 28px;\n      height: 28px;\n      border: none;\n      border-radius: 50%;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 10px;\n      transition: all 0.3s ease;\n    }\n\n    .quick-btn.buy-btn {\n      background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n      color: white;\n    }\n\n    .quick-btn.cart-btn {\n      background: linear-gradient(135deg, #4834d4, #686de0);\n      color: white;\n    }\n\n    .quick-btn.wishlist-btn {\n      background: linear-gradient(135deg, #ff9ff3, #f368e0);\n      color: white;\n    }\n\n    .quick-btn:hover {\n      transform: scale(1.1);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n    }\n\n    .shopping-indicator {\n      position: absolute;\n      bottom: 12px;\n      left: 12px;\n      background: rgba(0, 0, 0, 0.7);\n      color: white;\n      padding: 8px 12px;\n      border-radius: 20px;\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      font-size: 12px;\n      backdrop-filter: blur(10px);\n      animation: fadeInUp 0.3s ease;\n      cursor: pointer;\n\n      i {\n        font-size: 14px;\n      }\n    }\n\n    @keyframes fadeInUp {\n      from {\n        opacity: 0;\n        transform: translateY(10px);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0);\n      }\n    }\n\n    .post-actions {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 8px 16px;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 16px;\n    }\n\n    .action-btn,\n    .save-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 8px;\n      font-size: 20px;\n      color: #262626;\n      transition: color 0.2s;\n    }\n\n    .action-btn:hover,\n    .save-btn:hover {\n      color: #8e8e8e;\n    }\n\n    .like-btn.liked {\n      color: #ed4956;\n    }\n\n    .save-btn.saved {\n      color: #262626;\n    }\n\n    .post-stats {\n      padding: 0 16px;\n      margin-bottom: 8px;\n    }\n\n    .post-stats p {\n      font-size: 14px;\n      font-weight: 600;\n    }\n\n    .post-caption {\n      padding: 0 16px;\n      margin-bottom: 8px;\n    }\n\n    .post-caption p {\n      font-size: 14px;\n      line-height: 1.4;\n    }\n\n    .post-comments {\n      padding: 0 16px 16px;\n    }\n\n    .view-comments {\n      font-size: 14px;\n      color: #8e8e8e;\n      cursor: pointer;\n      margin-bottom: 8px;\n    }\n\n    .comment {\n      margin-bottom: 4px;\n    }\n\n    .comment p {\n      font-size: 14px;\n    }\n\n    .add-comment {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 8px;\n    }\n\n    .add-comment input {\n      flex: 1;\n      border: none;\n      outline: none;\n      font-size: 14px;\n      background: transparent;\n    }\n\n    .post-comment-btn {\n      background: none;\n      border: none;\n      color: var(--primary-color);\n      font-weight: 600;\n      cursor: pointer;\n      font-size: 14px;\n    }\n\n    .hashtag {\n      color: var(--primary-color);\n      cursor: pointer;\n    }\n\n    .ecommerce-actions {\n      border-top: 1px solid #efefef;\n      padding: 16px;\n      background: #fafafa;\n    }\n\n    .products-showcase {\n      display: flex;\n      flex-direction: column;\n      gap: 12px;\n    }\n\n    .product-showcase {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      background: white;\n      padding: 12px;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .product-thumb {\n      width: 60px;\n      height: 60px;\n      border-radius: 6px;\n      object-fit: cover;\n    }\n\n    .product-info-inline {\n      flex: 1;\n    }\n\n    .product-info-inline h5 {\n      font-size: 14px;\n      font-weight: 600;\n      margin: 0 0 4px 0;\n      color: #262626;\n    }\n\n    .product-info-inline .price {\n      font-size: 16px;\n      font-weight: 700;\n      color: #e91e63;\n      margin: 0 0 8px 0;\n    }\n\n    .product-actions {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .btn-wishlist {\n      background: none;\n      border: 1px solid #ddd;\n      width: 36px;\n      height: 36px;\n      border-radius: 6px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.2s;\n      color: #666;\n    }\n\n    .btn-wishlist:hover {\n      border-color: #e91e63;\n      color: #e91e63;\n    }\n\n    .btn-wishlist.active {\n      background: #e91e63;\n      border-color: #e91e63;\n      color: white;\n    }\n\n    .btn-cart {\n      background: #2196f3;\n      color: white;\n      border: none;\n      padding: 8px 12px;\n      border-radius: 6px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      transition: background 0.2s;\n    }\n\n    .btn-cart:hover {\n      background: #1976d2;\n    }\n\n    .btn-buy-now {\n      background: #ff9800;\n      color: white;\n      border: none;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: background 0.2s;\n    }\n\n    .btn-buy-now:hover {\n      background: #f57c00;\n    }\n\n    @media (max-width: 768px) {\n      .product-actions {\n        flex-direction: column;\n        gap: 6px;\n        align-items: stretch;\n      }\n\n      .btn-cart,\n      .btn-buy-now {\n        justify-content: center;\n      }\n    }\n  `]\n})\nexport class PostCardComponent implements OnInit {\n  @Input() post!: Post;\n  @Output() liked = new EventEmitter<string>();\n  @Output() commented = new EventEmitter<{ postId: string; comment: string }>();\n  @Output() shared = new EventEmitter<string>();\n  @ViewChild('videoPlayer') videoPlayer!: ElementRef<HTMLVideoElement>;\n\n  isLiked = false;\n  isSaved = false;\n  likesCount = 0;\n  newComment = '';\n  showComments = false;\n  showProductTags = false;\n  wishlistItems: string[] = [];\n  cartItems: string[] = [];\n\n  // Media handling\n  mediaItems: MediaItem[] = [];\n  currentMediaIndex = 0;\n  currentMedia!: MediaItem;\n\n  // Video controls\n  isVideoPlaying = false;\n  videoDuration = 0;\n  videoProgress = 0;\n  showVideoControls = false;\n  showHeartAnimation = false;\n  private videoProgressInterval?: number;\n\n  constructor(\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.likesCount = this.post.analytics.likes;\n    this.loadWishlistItems();\n    this.loadCartItems();\n    this.initializeMedia();\n  }\n\n  initializeMedia() {\n    // Process media items with enhanced video support and content matching\n    const contentHint = this.post.caption || this.post.hashtags?.join(' ') || '';\n    this.mediaItems = this.mediaService.processMediaItems(this.post.media || []);\n\n    this.currentMediaIndex = 0;\n    this.currentMedia = this.mediaItems[0] || {\n      id: 'default',\n      type: 'image',\n      url: this.mediaService.getSafeImageUrl('', 'post'),\n      alt: 'Default post image'\n    };\n\n    // Preload media for better performance\n    this.preloadCurrentMedia();\n  }\n\n  private preloadCurrentMedia() {\n    if (this.currentMedia) {\n      this.mediaService.preloadMedia([this.currentMedia]).catch(error => {\n        console.warn('Failed to preload media:', error);\n      });\n    }\n  }\n\n  loadWishlistItems() {\n    // Load from real API via service\n    this.wishlistItems = [];\n  }\n\n  loadCartItems() {\n    // Load from real API via service\n    this.cartItems = [];\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    \n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n\n  formatCaption(caption: string): string {\n    return caption.replace(/#(\\w+)/g, '<span class=\"hashtag\">#$1</span>');\n  }\n\n  getRecentComments() {\n    return this.post.comments.slice(-2);\n  }\n\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    this.likesCount += this.isLiked ? 1 : -1;\n    this.liked.emit(this.post._id);\n  }\n\n  toggleSave() {\n    this.isSaved = !this.isSaved;\n  }\n\n  toggleComments() {\n    this.showComments = !this.showComments;\n  }\n\n  addComment() {\n    if (this.newComment.trim()) {\n      this.commented.emit({\n        postId: this.post._id,\n        comment: this.newComment.trim()\n      });\n      this.newComment = '';\n    }\n  }\n\n  sharePost() {\n    this.shared.emit(this.post._id);\n  }\n\n  // E-commerce methods\n  isInWishlist(productId: string): boolean {\n    return this.wishlistService.isInWishlist(productId);\n  }\n\n  addToWishlist(productId: string) {\n    this.wishlistService.toggleWishlist(productId).subscribe({\n      next: (response) => {\n        if (this.isInWishlist(productId)) {\n          this.showNotification('Removed from wishlist', 'info');\n        } else {\n          this.showNotification('Added to wishlist ❤️', 'success');\n        }\n      },\n      error: (error) => {\n        console.error('Wishlist error:', error);\n        // Fallback to offline mode\n        this.wishlistService.toggleWishlistOffline(this.getProductById(productId));\n        this.showNotification(this.isInWishlist(productId) ? 'Removed from wishlist' : 'Added to wishlist ❤️', 'success');\n      }\n    });\n  }\n\n  addToCart(productId: string) {\n    this.cartService.addToCart(productId, 1).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.showNotification('Added to cart 🛒', 'success');\n        }\n      },\n      error: (error: any) => {\n        console.error('Cart error:', error);\n        this.showNotification('Failed to add to cart', 'error');\n      }\n    });\n  }\n\n  buyNow(productId: string) {\n    this.cartService.addToCart(productId, 1).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.showNotification('Redirecting to checkout...', 'info');\n          this.router.navigate(['/shop/checkout']);\n        }\n      },\n      error: (error: any) => {\n        console.error('Buy now error:', error);\n        this.showNotification('Failed to process purchase', 'error');\n      }\n    });\n  }\n\n  private getProductById(productId: string): any {\n    // Find product in post's products array\n    const productTag = this.post.products.find(p => p.product._id === productId);\n    return productTag ? productTag.product : null;\n  }\n\n  onBuyNow(productId: string) {\n    this.buyNow(productId);\n  }\n\n  // Media handling methods\n  getUserAvatarUrl(url: string): string {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n\n  getProductImageUrl(url: string): string {\n    return this.mediaService.getSafeImageUrl(url, 'product');\n  }\n\n  handleImageError(event: Event, type: 'user' | 'product' | 'post' = 'post'): void {\n    this.mediaService.handleImageError(event, type);\n  }\n\n  handleVideoError(event: Event): void {\n    console.error('Video load error:', event);\n    // Could implement fallback to thumbnail or different video\n  }\n\n  // Removed duplicate method - using onMediaLoadComplete instead\n\n  // Video control methods\n  toggleVideoPlay(): void {\n    if (!this.videoPlayer?.nativeElement) return;\n\n    const video = this.videoPlayer.nativeElement;\n    if (video.paused) {\n      video.play();\n      this.isVideoPlaying = true;\n      this.startVideoProgress();\n    } else {\n      video.pause();\n      this.isVideoPlaying = false;\n      this.stopVideoProgress();\n    }\n  }\n\n  private startVideoProgress(): void {\n    if (this.videoProgressInterval) {\n      clearInterval(this.videoProgressInterval);\n    }\n\n    this.videoProgressInterval = window.setInterval(() => {\n      if (this.videoPlayer?.nativeElement) {\n        const video = this.videoPlayer.nativeElement;\n        this.videoDuration = video.duration || 0;\n        this.videoProgress = this.videoDuration > 0 ? (video.currentTime / this.videoDuration) * 100 : 0;\n\n        if (video.ended) {\n          this.isVideoPlaying = false;\n          this.stopVideoProgress();\n        }\n      }\n    }, 100);\n  }\n\n  private stopVideoProgress(): void {\n    if (this.videoProgressInterval) {\n      clearInterval(this.videoProgressInterval);\n      this.videoProgressInterval = undefined;\n    }\n  }\n\n  // Media navigation methods with enhanced transitions\n  nextMedia(): void {\n    if (this.currentMediaIndex < this.mediaItems.length - 1) {\n      this.currentMediaIndex++;\n      this.currentMedia = this.mediaItems[this.currentMediaIndex];\n      this.resetVideoState();\n      this.preloadCurrentMedia();\n      this.trackMediaView();\n    }\n  }\n\n  previousMedia(): void {\n    if (this.currentMediaIndex > 0) {\n      this.currentMediaIndex--;\n      this.currentMedia = this.mediaItems[this.currentMediaIndex];\n      this.resetVideoState();\n      this.preloadCurrentMedia();\n      this.trackMediaView();\n    }\n  }\n\n  goToMedia(index: number): void {\n    if (index >= 0 && index < this.mediaItems.length) {\n      this.currentMediaIndex = index;\n      this.currentMedia = this.mediaItems[index];\n      this.resetVideoState();\n      this.preloadCurrentMedia();\n      this.trackMediaView();\n    }\n  }\n\n  private trackMediaView(): void {\n    // Track media view for analytics\n    console.log(`Viewing media ${this.currentMediaIndex + 1} of ${this.mediaItems.length}: ${this.currentMedia.type}`);\n  }\n\n  private resetVideoState(): void {\n    this.isVideoPlaying = false;\n    this.videoProgress = 0;\n    this.stopVideoProgress();\n  }\n\n  ngOnDestroy(): void {\n    this.stopVideoProgress();\n  }\n\n  // Instagram-like interactions\n  onDoubleTap(): void {\n    this.toggleLike();\n    this.showHeartAnimation = true;\n    setTimeout(() => {\n      this.showHeartAnimation = false;\n    }, 1000);\n  }\n\n  toggleProductTags(): void {\n    // Toggle product tags visibility (Instagram-style)\n    if (this.post.products && this.post.products.length > 0) {\n      this.showProductTags = !this.showProductTags;\n\n      // Auto-hide after 3 seconds\n      if (this.showProductTags) {\n        setTimeout(() => {\n          this.showProductTags = false;\n        }, 3000);\n      }\n    }\n  }\n\n  viewProduct(productId: string): void {\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  formatDuration(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  onMediaLoadComplete(): void {\n    // Media loaded successfully\n    if (this.currentMedia?.type === 'video') {\n      this.showVideoControls = true;\n      setTimeout(() => {\n        this.showVideoControls = false;\n      }, 3000);\n    }\n  }\n\n  private showNotification(message: string, type: 'success' | 'info' | 'error') {\n    // Create notification element\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.textContent = message;\n\n    // Add styles\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n\n    // Add animation styles\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes slideIn {\n        from { transform: translateX(100%); opacity: 0; }\n        to { transform: translateX(0); opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n\n    document.body.appendChild(notification);\n\n    // Remove after 3 seconds\n    setTimeout(() => {\n      notification.remove();\n      style.remove();\n    }, 3000);\n  }\n}\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAuC,eAAe;AACrG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;IAoCpCC,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAGNH,EAAA,CAAAC,cAAA,cASC;IADCD,EAHA,CAAAI,UAAA,mBAAAC,uDAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,gBAAA,CAAAN,MAAA,CAAwB;IAAA,EAAC,kBAAAO,sDAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC1BF,MAAA,CAAAK,mBAAA,EAAqB;IAAA,EAAC,sBAAAC,0DAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAClBF,MAAA,CAAAO,WAAA,EAAa;IAAA,EAAC,mBAAAC,uDAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACjBF,MAAA,CAAAS,iBAAA,EAAmB;IAAA,EAAC;IAR/BlB,EAAA,CAAAG,YAAA,EASC;;;;IANCH,EADA,CAAAmB,UAAA,QAAAV,MAAA,CAAAW,YAAA,CAAAC,GAAA,EAAArB,EAAA,CAAAsB,aAAA,CAAwB,QAAAb,MAAA,CAAAW,YAAA,CAAAG,GAAA,CACA;;;;;;IAS1BvB,EAAA,CAAAC,cAAA,mBAaC;IADCD,EAHA,CAAAI,UAAA,mBAAAoB,2DAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiB,eAAA,EAAiB;IAAA,EAAC,sBAAAC,8DAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACfF,MAAA,CAAAO,WAAA,EAAa;IAAA,EAAC,wBAAAY,gEAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACZF,MAAA,CAAAK,mBAAA,EAAqB;IAAA,EAAC,mBAAAe,2DAAAvB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC3BF,MAAA,CAAAqB,gBAAA,CAAAxB,MAAA,CAAwB;IAAA,EAAC;IACnCN,EAAA,CAAAG,YAAA,EAAQ;;;;IANPH,EAHA,CAAAmB,UAAA,QAAAV,MAAA,CAAAW,YAAA,CAAAC,GAAA,EAAArB,EAAA,CAAAsB,aAAA,CAAwB,WAAAb,MAAA,CAAAW,YAAA,CAAAW,YAAA,EAAA/B,EAAA,CAAAsB,aAAA,CACY,eACtB,cACD;;;;;IAcXtB,EADF,CAAAC,cAAA,cAAsD,eACvB;IAAAD,EAAA,CAAAgC,MAAA,GAA2C;IAC1EhC,EAD0E,CAAAG,YAAA,EAAO,EAC3E;;;;IADyBH,EAAA,CAAAiC,SAAA,GAA2C;IAA3CjC,EAAA,CAAAkC,iBAAA,CAAAzB,MAAA,CAAA0B,cAAA,CAAA1B,MAAA,CAAAW,YAAA,CAAAgB,QAAA,EAA2C;;;;;IAE1EpC,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAE,SAAA,cAAgE;IAClEF,EAAA,CAAAG,YAAA,EAAM;;;;IADsBH,EAAA,CAAAiC,SAAA,EAA+B;IAA/BjC,EAAA,CAAAqC,WAAA,UAAA5B,MAAA,CAAA6B,aAAA,MAA+B;;;;;;IAP3DtC,EADF,CAAAC,cAAA,cAAuG,iBACT;IAA7DD,EAAA,CAAAI,UAAA,mBAAAmC,0DAAA;MAAAvC,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiB,eAAA,EAAiB;IAAA,EAAC;IACxD1B,EAAA,CAAAE,SAAA,QAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAS;IAITH,EAHA,CAAAyC,UAAA,IAAAC,uCAAA,kBAAsD,IAAAC,uCAAA,kBAGA;IAGxD3C,EAAA,CAAAG,YAAA,EAAM;;;;IAV6DH,EAAA,CAAA4C,WAAA,YAAAnC,MAAA,CAAAoC,iBAAA,CAAmC;IACzC7C,EAAA,CAAAiC,SAAA,EAAgC;IAAhCjC,EAAA,CAAA4C,WAAA,YAAAnC,MAAA,CAAAqC,cAAA,CAAgC;IACtF9C,EAAA,CAAAiC,SAAA,EAAyD;IAAzDjC,EAAA,CAAA+C,UAAA,CAAAtC,MAAA,CAAAqC,cAAA,kCAAyD;IAErC9C,EAAA,CAAAiC,SAAA,EAA2B;IAA3BjC,EAAA,CAAAmB,UAAA,SAAAV,MAAA,CAAAW,YAAA,CAAAgB,QAAA,CAA2B;IAGvBpC,EAAA,CAAAiC,SAAA,EAAuB;IAAvBjC,EAAA,CAAAmB,UAAA,SAAAV,MAAA,CAAAuC,aAAA,KAAuB;;;;;IAkChDhD,EAAA,CAAAE,SAAA,YAAiE;;;;;;IARnEF,EAAA,CAAAC,cAAA,eAOC;IAFCD,EAAA,CAAAI,UAAA,mBAAA6C,+DAAA;MAAA,MAAAC,IAAA,GAAAlD,EAAA,CAAAO,aAAA,CAAA4C,GAAA,EAAAC,KAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,SAAA,CAAAH,IAAA,CAAY;IAAA,EAAC;IAGtBlD,EAAA,CAAAyC,UAAA,IAAAa,4CAAA,gBAA6D;IAC/DtD,EAAA,CAAAG,YAAA,EAAO;;;;;;IALLH,EADA,CAAA4C,WAAA,WAAAM,IAAA,KAAAzC,MAAA,CAAA8C,iBAAA,CAAwC,UAAAC,QAAA,CAAAC,IAAA,aACF;;IAIlCzD,EAAA,CAAAiC,SAAA,EAA4B;IAA5BjC,EAAA,CAAAmB,UAAA,SAAAqC,QAAA,CAAAC,IAAA,aAA4B;;;;;;IA3BpCzD,EADF,CAAAC,cAAA,cAA4D,iBAMzD;IAHCD,EAAA,CAAAI,UAAA,mBAAAsD,0DAAA;MAAA1D,EAAA,CAAAO,aAAA,CAAAoD,GAAA;MAAA,MAAAlD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmD,aAAA,EAAe;IAAA,EAAC;IAIzB5D,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAI,UAAA,mBAAAyD,0DAAA;MAAA7D,EAAA,CAAAO,aAAA,CAAAoD,GAAA;MAAA,MAAAlD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqD,SAAA,EAAW;IAAA,EAAC;IAIrB9D,EAAA,CAAAE,SAAA,YAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAyC,UAAA,IAAAsB,wCAAA,mBAOC;IAIL/D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA3BFH,EAAA,CAAAiC,SAAA,EAAoC;IAApCjC,EAAA,CAAAmB,UAAA,aAAAV,MAAA,CAAA8C,iBAAA,OAAoC;;IAQpCvD,EAAA,CAAAiC,SAAA,GAAwD;IAAxDjC,EAAA,CAAAmB,UAAA,aAAAV,MAAA,CAAA8C,iBAAA,KAAA9C,MAAA,CAAAuD,UAAA,CAAAC,MAAA,KAAwD;;IASpCjE,EAAA,CAAAiC,SAAA,GAAe;IAAfjC,EAAA,CAAAmB,UAAA,YAAAV,MAAA,CAAAuD,UAAA,CAAe;;;;;;IAqBrChE,EAAA,CAAAC,cAAA,cAMC;IADCD,EAAA,CAAAI,UAAA,mBAAA8D,6DAAA5D,MAAA;MAAA,MAAA6D,cAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,SAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAA6D,WAAA,CAAAH,cAAA,CAAAI,OAAA,CAAAC,GAAA,CAAmC;MAAA,OAAAxE,EAAA,CAAAW,WAAA,CAAEL,MAAA,CAAAmE,eAAA,EAAwB;IAAA,EAAC;IAEvEzE,EAAA,CAAAC,cAAA,cAAqB;IACnBD,EAAA,CAAAE,SAAA,cAA6B;IAC/BF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,cAKvB;IADCD,EAAA,CAAAI,UAAA,mBAAAsE,6DAAApE,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,gBAAA,CAAAN,MAAA,EAAyB,SAAS,CAAC;IAAA,EAAC;IAH/CN,EAAA,CAAAG,YAAA,EAIC;IAECH,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAgC,MAAA,GAA6B;IAAAhC,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAgC,MAAA,GAAwC;;IAAAhC,EAAA,CAAAG,YAAA,EAAI;IAE7CH,EADF,CAAAC,cAAA,eAAmC,kBAC4D;IAA3DD,EAAA,CAAAI,UAAA,mBAAAuE,iEAAA;MAAA,MAAAR,cAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,SAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAT,cAAA,CAAAI,OAAA,CAAAC,GAAA,CAAgC;IAAA,EAAC;IAC1ExE,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAmG;IAAhED,EAAA,CAAAI,UAAA,mBAAAyE,iEAAA;MAAA,MAAAV,cAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,SAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqE,SAAA,CAAAX,cAAA,CAAAI,OAAA,CAAAC,GAAA,CAAiC;IAAA,EAAC;IAC5ExE,EAAA,CAAAE,SAAA,aAAgC;IAClCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+G;IAAxED,EAAA,CAAAI,UAAA,mBAAA2E,iEAAA;MAAA,MAAAZ,cAAA,GAAAnE,EAAA,CAAAO,aAAA,CAAA6D,GAAA,EAAAC,SAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuE,aAAA,CAAAb,cAAA,CAAAI,OAAA,CAAAC,GAAA,CAAqC;IAAA,EAAC;IACpFxE,EAAA,CAAAE,SAAA,aAA4B;IAKtCF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;;IA5BJH,EADA,CAAAqC,WAAA,QAAA8B,cAAA,CAAAc,QAAA,CAAAC,CAAA,YAA2C,SAAAf,cAAA,CAAAc,QAAA,CAAAE,CAAA,YACC;IAQxCnF,EAAA,CAAAiC,SAAA,GAAkE;IAClEjC,EADA,CAAAmB,UAAA,QAAAV,MAAA,CAAA2E,kBAAA,CAAAjB,cAAA,CAAAI,OAAA,CAAAc,MAAA,IAAAhE,GAAA,SAAArB,EAAA,CAAAsB,aAAA,CAAkE,QAAA6C,cAAA,CAAAI,OAAA,CAAAe,IAAA,CACnC;IAI3BtF,EAAA,CAAAiC,SAAA,GAA6B;IAA7BjC,EAAA,CAAAkC,iBAAA,CAAAiC,cAAA,CAAAI,OAAA,CAAAe,IAAA,CAA6B;IAC9BtF,EAAA,CAAAiC,SAAA,GAAwC;IAAxCjC,EAAA,CAAAuF,kBAAA,WAAAvF,EAAA,CAAAwF,WAAA,QAAArB,cAAA,CAAAI,OAAA,CAAAkB,KAAA,MAAwC;;;;;IArBnDzF,EAAA,CAAAC,cAAA,cAEuD;IACrDD,EAAA,CAAAyC,UAAA,IAAAiD,uCAAA,oBAMC;IA2BH1F,EAAA,CAAAG,YAAA,EAAM;;;;IAnCDH,EAAA,CAAA4C,WAAA,cAAAnC,MAAA,CAAAkF,eAAA,CAAmC;IAGb3F,EAAA,CAAAiC,SAAA,EAAgB;IAAhBjC,EAAA,CAAAmB,UAAA,YAAAV,MAAA,CAAAmF,IAAA,CAAAC,QAAA,CAAgB;;;;;IAmC3C7F,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAgC,MAAA,2BAAoB;IAC5BhC,EAD4B,CAAAG,YAAA,EAAO,EAC7B;;;;;;IA4BJH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAAoG;IAElGF,EADF,CAAAC,cAAA,cAAiC,SAC3B;IAAAD,EAAA,CAAAgC,MAAA,GAA6B;IAAAhC,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,YAAiB;IAAAD,EAAA,CAAAgC,MAAA,GAAwC;;IAAAhC,EAAA,CAAAG,YAAA,EAAI;IAE3DH,EADF,CAAAC,cAAA,cAA6B,iBACwG;IAAtGD,EAAA,CAAAI,UAAA,mBAAA0F,gEAAA;MAAA,MAAAC,cAAA,GAAA/F,EAAA,CAAAO,aAAA,CAAAyF,IAAA,EAAA3B,SAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuE,aAAA,CAAAe,cAAA,CAAAxB,OAAA,CAAAC,GAAA,CAAqC;IAAA,EAAC;IAC1ExE,EAAA,CAAAE,SAAA,SAAwF;IAC1FF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAqE;IAA5CD,EAAA,CAAAI,UAAA,mBAAA6F,iEAAA;MAAA,MAAAF,cAAA,GAAA/F,EAAA,CAAAO,aAAA,CAAAyF,IAAA,EAAA3B,SAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqE,SAAA,CAAAiB,cAAA,CAAAxB,OAAA,CAAAC,GAAA,CAAiC;IAAA,EAAC;IAClExE,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAgC,MAAA,qBACF;IAAAhC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAqE;IAAzCD,EAAA,CAAAI,UAAA,mBAAA8F,iEAAA;MAAA,MAAAH,cAAA,GAAA/F,EAAA,CAAAO,aAAA,CAAAyF,IAAA,EAAA3B,SAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0F,MAAA,CAAAJ,cAAA,CAAAxB,OAAA,CAAAC,GAAA,CAA8B;IAAA,EAAC;IAClExE,EAAA,CAAAgC,MAAA,iBACF;IAGNhC,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;IAjBCH,EAAA,CAAAiC,SAAA,EAAwC;IAACjC,EAAzC,CAAAmB,UAAA,QAAA4E,cAAA,CAAAxB,OAAA,CAAAc,MAAA,IAAAhE,GAAA,EAAArB,EAAA,CAAAsB,aAAA,CAAwC,QAAAyE,cAAA,CAAAxB,OAAA,CAAAe,IAAA,CAAgC;IAEvEtF,EAAA,CAAAiC,SAAA,GAA6B;IAA7BjC,EAAA,CAAAkC,iBAAA,CAAA6D,cAAA,CAAAxB,OAAA,CAAAe,IAAA,CAA6B;IAChBtF,EAAA,CAAAiC,SAAA,GAAwC;IAAxCjC,EAAA,CAAAuF,kBAAA,WAAAvF,EAAA,CAAAwF,WAAA,OAAAO,cAAA,CAAAxB,OAAA,CAAAkB,KAAA,MAAwC;IAEsBzF,EAAA,CAAAiC,SAAA,GAAqD;IAArDjC,EAAA,CAAA4C,WAAA,WAAAnC,MAAA,CAAA2F,YAAA,CAAAL,cAAA,CAAAxB,OAAA,CAAAC,GAAA,EAAqD;IAC7HxE,EAAA,CAAAiC,SAAA,EAAgF;IAAhFjC,EAAA,CAAA+C,UAAA,CAAAtC,MAAA,CAAA2F,YAAA,CAAAL,cAAA,CAAAxB,OAAA,CAAAC,GAAA,oCAAgF;;;;;IAR7FxE,EADF,CAAAC,cAAA,cAAgE,cAC/B;IAC7BD,EAAA,CAAAyC,UAAA,IAAA4D,uCAAA,oBAAuE;IAoB3ErG,EADE,CAAAG,YAAA,EAAM,EACF;;;;IApB0BH,EAAA,CAAAiC,SAAA,GAAgB;IAAhBjC,EAAA,CAAAmB,UAAA,YAAAV,MAAA,CAAAmF,IAAA,CAAAC,QAAA,CAAgB;;;;;IAqC9C7F,EAAA,CAAAC,cAAA,YAA0D;IACxDD,EAAA,CAAAgC,MAAA,GACF;IAAAhC,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAiC,SAAA,EACF;IADEjC,EAAA,CAAAuF,kBAAA,eAAA9E,MAAA,CAAAmF,IAAA,CAAAU,QAAA,CAAArC,MAAA,eACF;;;;;IAKIjE,EAFJ,CAAAC,cAAA,cAAiE,QAC5D,aACO;IAAAD,EAAA,CAAAgC,MAAA,GAA2B;IAAAhC,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAgC,MAAA,GACF;IACFhC,EADE,CAAAG,YAAA,EAAI,EACA;;;;IAHMH,EAAA,CAAAiC,SAAA,GAA2B;IAA3BjC,EAAA,CAAAkC,iBAAA,CAAAqE,WAAA,CAAAC,IAAA,CAAAC,QAAA,CAA2B;IACnCzG,EAAA,CAAAiC,SAAA,EACF;IADEjC,EAAA,CAAAuF,kBAAA,MAAAgB,WAAA,CAAAG,IAAA,MACF;;;;;;IAWA1G,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAI,UAAA,mBAAAuG,6DAAA;MAAA3G,EAAA,CAAAO,aAAA,CAAAqG,IAAA;MAAA,MAAAnG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoG,UAAA,EAAY;IAAA,EAAC;IAGtB7G,EAAA,CAAAgC,MAAA,aACF;IAAAhC,EAAA,CAAAG,YAAA,EAAS;;;AA41BnB,OAAM,MAAO2G,iBAAiB;EA6B5BC,YACUC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc,EACdC,YAA0B;IAH1B,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IA/BZ,KAAAC,KAAK,GAAG,IAAIvH,YAAY,EAAU;IAClC,KAAAwH,SAAS,GAAG,IAAIxH,YAAY,EAAuC;IACnE,KAAAyH,MAAM,GAAG,IAAIzH,YAAY,EAAU;IAG7C,KAAA0H,OAAO,GAAG,KAAK;IACf,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAhC,eAAe,GAAG,KAAK;IACvB,KAAAiC,aAAa,GAAa,EAAE;IAC5B,KAAAC,SAAS,GAAa,EAAE;IAExB;IACA,KAAA7D,UAAU,GAAgB,EAAE;IAC5B,KAAAT,iBAAiB,GAAG,CAAC;IAGrB;IACA,KAAAT,cAAc,GAAG,KAAK;IACtB,KAAAE,aAAa,GAAG,CAAC;IACjB,KAAAV,aAAa,GAAG,CAAC;IACjB,KAAAO,iBAAiB,GAAG,KAAK;IACzB,KAAAiF,kBAAkB,GAAG,KAAK;EAQvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACN,UAAU,GAAG,IAAI,CAAC7B,IAAI,CAACoC,SAAS,CAACC,KAAK;IAC3C,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzC,IAAI,CAAC0C,OAAO,IAAI,IAAI,CAAC1C,IAAI,CAAC2C,QAAQ,EAAEC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;IAC5E,IAAI,CAACxE,UAAU,GAAG,IAAI,CAACmD,YAAY,CAACsB,iBAAiB,CAAC,IAAI,CAAC7C,IAAI,CAAC8C,KAAK,IAAI,EAAE,CAAC;IAE5E,IAAI,CAACnF,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACnC,YAAY,GAAG,IAAI,CAAC4C,UAAU,CAAC,CAAC,CAAC,IAAI;MACxC2E,EAAE,EAAE,SAAS;MACblF,IAAI,EAAE,OAAO;MACbpC,GAAG,EAAE,IAAI,CAAC8F,YAAY,CAACyB,eAAe,CAAC,EAAE,EAAE,MAAM,CAAC;MAClDrH,GAAG,EAAE;KACN;IAED;IACA,IAAI,CAACsH,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACzH,YAAY,EAAE;MACrB,IAAI,CAAC+F,YAAY,CAAC2B,YAAY,CAAC,CAAC,IAAI,CAAC1H,YAAY,CAAC,CAAC,CAAC2H,KAAK,CAACC,KAAK,IAAG;QAChEC,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAEF,KAAK,CAAC;MACjD,CAAC,CAAC;;EAEN;EAEAd,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACN,aAAa,GAAG,EAAE;EACzB;EAEAO,aAAaA,CAAA;IACX;IACA,IAAI,CAACN,SAAS,GAAG,EAAE;EACrB;EAEAsB,UAAUA,CAACC,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACrD,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIE,KAAK,GAAG,CAAC,EAAE,OAAO,KAAK;IAC3B,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,GAAG;IAClC,MAAMG,IAAI,GAAGF,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,EAAE,CAAC;IACnC,OAAO,GAAGG,IAAI,GAAG;EACnB;EAEAC,aAAaA,CAACvB,OAAe;IAC3B,OAAOA,OAAO,CAACwB,OAAO,CAAC,SAAS,EAAE,kCAAkC,CAAC;EACvE;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACnE,IAAI,CAACU,QAAQ,CAAC0D,KAAK,CAAC,CAAC,CAAC,CAAC;EACrC;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAC1C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACE,UAAU,IAAI,IAAI,CAACF,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC,IAAI,CAACH,KAAK,CAAC8C,IAAI,CAAC,IAAI,CAACtE,IAAI,CAACpB,GAAG,CAAC;EAChC;EAEA2F,UAAUA,CAAA;IACR,IAAI,CAAC3C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;EAC9B;EAEA4C,cAAcA,CAAA;IACZ,IAAI,CAACzC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAd,UAAUA,CAAA;IACR,IAAI,IAAI,CAACa,UAAU,CAAC2C,IAAI,EAAE,EAAE;MAC1B,IAAI,CAAChD,SAAS,CAAC6C,IAAI,CAAC;QAClBI,MAAM,EAAE,IAAI,CAAC1E,IAAI,CAACpB,GAAG;QACrB+F,OAAO,EAAE,IAAI,CAAC7C,UAAU,CAAC2C,IAAI;OAC9B,CAAC;MACF,IAAI,CAAC3C,UAAU,GAAG,EAAE;;EAExB;EAEA8C,SAASA,CAAA;IACP,IAAI,CAAClD,MAAM,CAAC4C,IAAI,CAAC,IAAI,CAACtE,IAAI,CAACpB,GAAG,CAAC;EACjC;EAEA;EACA4B,YAAYA,CAACqE,SAAiB;IAC5B,OAAO,IAAI,CAACxD,eAAe,CAACb,YAAY,CAACqE,SAAS,CAAC;EACrD;EAEAzF,aAAaA,CAACyF,SAAiB;IAC7B,IAAI,CAACxD,eAAe,CAACyD,cAAc,CAACD,SAAS,CAAC,CAACE,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,IAAI,CAACzE,YAAY,CAACqE,SAAS,CAAC,EAAE;UAChC,IAAI,CAACK,gBAAgB,CAAC,uBAAuB,EAAE,MAAM,CAAC;SACvD,MAAM;UACL,IAAI,CAACA,gBAAgB,CAAC,sBAAsB,EAAE,SAAS,CAAC;;MAE5D,CAAC;MACD9B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvC;QACA,IAAI,CAAC/B,eAAe,CAAC8D,qBAAqB,CAAC,IAAI,CAACC,cAAc,CAACP,SAAS,CAAC,CAAC;QAC1E,IAAI,CAACK,gBAAgB,CAAC,IAAI,CAAC1E,YAAY,CAACqE,SAAS,CAAC,GAAG,uBAAuB,GAAG,sBAAsB,EAAE,SAAS,CAAC;MACnH;KACD,CAAC;EACJ;EAEA3F,SAASA,CAAC2F,SAAiB;IACzB,IAAI,CAACzD,WAAW,CAAClC,SAAS,CAAC2F,SAAS,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACI,OAAO,EAAE;UACpB,IAAI,CAACH,gBAAgB,CAAC,kBAAkB,EAAE,SAAS,CAAC;;MAExD,CAAC;MACD9B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC,IAAI,CAAC8B,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC;MACzD;KACD,CAAC;EACJ;EAEA3E,MAAMA,CAACsE,SAAiB;IACtB,IAAI,CAACzD,WAAW,CAAClC,SAAS,CAAC2F,SAAS,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACI,OAAO,EAAE;UACpB,IAAI,CAACH,gBAAgB,CAAC,4BAA4B,EAAE,MAAM,CAAC;UAC3D,IAAI,CAAC5D,MAAM,CAACgE,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;MAE5C,CAAC;MACDlC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,IAAI,CAAC8B,gBAAgB,CAAC,4BAA4B,EAAE,OAAO,CAAC;MAC9D;KACD,CAAC;EACJ;EAEQE,cAAcA,CAACP,SAAiB;IACtC;IACA,MAAMU,UAAU,GAAG,IAAI,CAACvF,IAAI,CAACC,QAAQ,CAACuF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9G,OAAO,CAACC,GAAG,KAAKiG,SAAS,CAAC;IAC5E,OAAOU,UAAU,GAAGA,UAAU,CAAC5G,OAAO,GAAG,IAAI;EAC/C;EAEAK,QAAQA,CAAC6F,SAAiB;IACxB,IAAI,CAACtE,MAAM,CAACsE,SAAS,CAAC;EACxB;EAEA;EACAa,gBAAgBA,CAACjK,GAAW;IAC1B,OAAO,IAAI,CAAC8F,YAAY,CAACyB,eAAe,CAACvH,GAAG,EAAE,MAAM,CAAC;EACvD;EAEA+D,kBAAkBA,CAAC/D,GAAW;IAC5B,OAAO,IAAI,CAAC8F,YAAY,CAACyB,eAAe,CAACvH,GAAG,EAAE,SAAS,CAAC;EAC1D;EAEAT,gBAAgBA,CAAC2K,KAAY,EAAE9H,IAAA,GAAoC,MAAM;IACvE,IAAI,CAAC0D,YAAY,CAACvG,gBAAgB,CAAC2K,KAAK,EAAE9H,IAAI,CAAC;EACjD;EAEA3B,gBAAgBA,CAACyJ,KAAY;IAC3BtC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEuC,KAAK,CAAC;IACzC;EACF;EAEA;EAEA;EACA7J,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC8J,WAAW,EAAEC,aAAa,EAAE;IAEtC,MAAMC,KAAK,GAAG,IAAI,CAACF,WAAW,CAACC,aAAa;IAC5C,IAAIC,KAAK,CAACC,MAAM,EAAE;MAChBD,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAAC9I,cAAc,GAAG,IAAI;MAC1B,IAAI,CAAC+I,kBAAkB,EAAE;KAC1B,MAAM;MACLH,KAAK,CAACI,KAAK,EAAE;MACb,IAAI,CAAChJ,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACiJ,iBAAiB,EAAE;;EAE5B;EAEQF,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACG,qBAAqB,EAAE;MAC9BC,aAAa,CAAC,IAAI,CAACD,qBAAqB,CAAC;;IAG3C,IAAI,CAACA,qBAAqB,GAAGE,MAAM,CAACC,WAAW,CAAC,MAAK;MACnD,IAAI,IAAI,CAACX,WAAW,EAAEC,aAAa,EAAE;QACnC,MAAMC,KAAK,GAAG,IAAI,CAACF,WAAW,CAACC,aAAa;QAC5C,IAAI,CAACzI,aAAa,GAAG0I,KAAK,CAACtJ,QAAQ,IAAI,CAAC;QACxC,IAAI,CAACE,aAAa,GAAG,IAAI,CAACU,aAAa,GAAG,CAAC,GAAI0I,KAAK,CAACU,WAAW,GAAG,IAAI,CAACpJ,aAAa,GAAI,GAAG,GAAG,CAAC;QAEhG,IAAI0I,KAAK,CAACW,KAAK,EAAE;UACf,IAAI,CAACvJ,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACiJ,iBAAiB,EAAE;;;IAG9B,CAAC,EAAE,GAAG,CAAC;EACT;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACC,qBAAqB,EAAE;MAC9BC,aAAa,CAAC,IAAI,CAACD,qBAAqB,CAAC;MACzC,IAAI,CAACA,qBAAqB,GAAGM,SAAS;;EAE1C;EAEA;EACAxI,SAASA,CAAA;IACP,IAAI,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAACS,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI,CAACV,iBAAiB,EAAE;MACxB,IAAI,CAACnC,YAAY,GAAG,IAAI,CAAC4C,UAAU,CAAC,IAAI,CAACT,iBAAiB,CAAC;MAC3D,IAAI,CAACgJ,eAAe,EAAE;MACtB,IAAI,CAAC1D,mBAAmB,EAAE;MAC1B,IAAI,CAAC2D,cAAc,EAAE;;EAEzB;EAEA5I,aAAaA,CAAA;IACX,IAAI,IAAI,CAACL,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACnC,YAAY,GAAG,IAAI,CAAC4C,UAAU,CAAC,IAAI,CAACT,iBAAiB,CAAC;MAC3D,IAAI,CAACgJ,eAAe,EAAE;MACtB,IAAI,CAAC1D,mBAAmB,EAAE;MAC1B,IAAI,CAAC2D,cAAc,EAAE;;EAEzB;EAEAnJ,SAASA,CAACD,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACY,UAAU,CAACC,MAAM,EAAE;MAChD,IAAI,CAACV,iBAAiB,GAAGH,KAAK;MAC9B,IAAI,CAAChC,YAAY,GAAG,IAAI,CAAC4C,UAAU,CAACZ,KAAK,CAAC;MAC1C,IAAI,CAACmJ,eAAe,EAAE;MACtB,IAAI,CAAC1D,mBAAmB,EAAE;MAC1B,IAAI,CAAC2D,cAAc,EAAE;;EAEzB;EAEQA,cAAcA,CAAA;IACpB;IACAvD,OAAO,CAACwD,GAAG,CAAC,iBAAiB,IAAI,CAAClJ,iBAAiB,GAAG,CAAC,OAAO,IAAI,CAACS,UAAU,CAACC,MAAM,KAAK,IAAI,CAAC7C,YAAY,CAACqC,IAAI,EAAE,CAAC;EACpH;EAEQ8I,eAAeA,CAAA;IACrB,IAAI,CAACzJ,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACR,aAAa,GAAG,CAAC;IACtB,IAAI,CAACyJ,iBAAiB,EAAE;EAC1B;EAEAW,WAAWA,CAAA;IACT,IAAI,CAACX,iBAAiB,EAAE;EAC1B;EAEA;EACA/K,WAAWA,CAAA;IACT,IAAI,CAACiJ,UAAU,EAAE;IACjB,IAAI,CAACnC,kBAAkB,GAAG,IAAI;IAC9B6E,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7E,kBAAkB,GAAG,KAAK;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA5G,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAAC0E,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACD,IAAI,CAACC,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI,CAAC0B,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;MAE5C;MACA,IAAI,IAAI,CAACA,eAAe,EAAE;QACxBgH,UAAU,CAAC,MAAK;UACd,IAAI,CAAChH,eAAe,GAAG,KAAK;QAC9B,CAAC,EAAE,IAAI,CAAC;;;EAGd;EAEArB,WAAWA,CAACmG,SAAiB;IAC3B,IAAI,CAACvD,MAAM,CAACgE,QAAQ,CAAC,CAAC,eAAe,EAAET,SAAS,CAAC,CAAC;EACpD;EAEAtI,cAAcA,CAACyK,OAAe;IAC5B,MAAMC,OAAO,GAAGnD,IAAI,CAACC,KAAK,CAACiD,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIC,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEAlM,mBAAmBA,CAAA;IACjB;IACA,IAAI,IAAI,CAACM,YAAY,EAAEqC,IAAI,KAAK,OAAO,EAAE;MACvC,IAAI,CAACZ,iBAAiB,GAAG,IAAI;MAC7B8J,UAAU,CAAC,MAAK;QACd,IAAI,CAAC9J,iBAAiB,GAAG,KAAK;MAChC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEQiI,gBAAgBA,CAACmC,OAAe,EAAExJ,IAAkC;IAC1E;IACA,MAAMyJ,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,6BAA6B5J,IAAI,EAAE;IAC5DyJ,YAAY,CAACI,WAAW,GAAGL,OAAO;IAElC;IACAC,YAAY,CAACK,KAAK,CAACC,OAAO,GAAG;;;;oBAIb/J,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGA,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;;;;;;;;;KASxF;IAED;IACA,MAAM8J,KAAK,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CG,KAAK,CAACD,WAAW,GAAG;;;;;KAKnB;IACDH,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,KAAK,CAAC;IAEhCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACAP,UAAU,CAAC,MAAK;MACdO,YAAY,CAACU,MAAM,EAAE;MACrBL,KAAK,CAACK,MAAM,EAAE;IAChB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAzXW9G,iBAAiB,EAAA9G,EAAA,CAAA6N,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/N,EAAA,CAAA6N,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAjO,EAAA,CAAA6N,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAnO,EAAA,CAAA6N,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAjBvH,iBAAiB;MAAAwH,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;UAzkCpBzO,EAJN,CAAAC,cAAA,iBAAsB,aAEK,aACA,aAMpB;UADCD,EAAA,CAAAI,UAAA,mBAAAuO,gDAAArO,MAAA;YAAA,OAASoO,GAAA,CAAA9N,gBAAA,CAAAN,MAAA,EAAyB,MAAM,CAAC;UAAA,EAAC;UAJ5CN,EAAA,CAAAG,YAAA,EAKC;UAECH,EADF,CAAAC,cAAA,aAA0B,SACpB;UAAAD,EAAA,CAAAgC,MAAA,GAAwB;UAAAhC,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAgC,MAAA,GAAgC;UAE1ChC,EAF0C,CAAAG,YAAA,EAAO,EACzC,EACF;UACNH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAE,SAAA,YAAiC;UAErCF,EADE,CAAAG,YAAA,EAAS,EACL;UAGNH,EAAA,CAAAC,cAAA,cAAgF;UAgD9ED,EA9CA,CAAAyC,UAAA,KAAAmM,iCAAA,iBAAiD,KAAAC,iCAAA,kBAchD,KAAAC,mCAAA,oBAgBA,KAAAC,iCAAA,kBAGsG,KAAAC,iCAAA,kBAa3C;UAkC5DhP,EAAA,CAAAC,cAAA,eAAkE;UAChED,EAAA,CAAAE,SAAA,aAA4B;UAC9BF,EAAA,CAAAG,YAAA,EAAM;UA0CNH,EAvCA,CAAAyC,UAAA,KAAAwM,iCAAA,kBAEuD,KAAAC,iCAAA,kBAqC+C;UAIxGlP,EAAA,CAAAG,YAAA,EAAM;UAKFH,EAFJ,CAAAC,cAAA,eAA0B,eACI,kBAKzB;UADCD,EAAA,CAAAI,UAAA,mBAAA+O,oDAAA;YAAA,OAAST,GAAA,CAAAzE,UAAA,EAAY;UAAA,EAAC;UAEtBjK,EAAA,CAAAE,SAAA,SAA2D;UAC7DF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAsD;UAA3BD,EAAA,CAAAI,UAAA,mBAAAgP,oDAAA;YAAA,OAASV,GAAA,CAAAtE,cAAA,EAAgB;UAAA,EAAC;UACnDpK,EAAA,CAAAE,SAAA,aAA8B;UAChCF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAiD;UAAtBD,EAAA,CAAAI,UAAA,mBAAAiP,oDAAA;YAAA,OAASX,GAAA,CAAAlE,SAAA,EAAW;UAAA,EAAC;UAC9CxK,EAAA,CAAAE,SAAA,aAA4B;UAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;UACNH,EAAA,CAAAC,cAAA,kBAAwE;UAAvBD,EAAA,CAAAI,UAAA,mBAAAkP,oDAAA;YAAA,OAASZ,GAAA,CAAAvE,UAAA,EAAY;UAAA,EAAC;UACrEnK,EAAA,CAAAE,SAAA,SAAiE;UAErEF,EADE,CAAAG,YAAA,EAAS,EACL;UAGNH,EAAA,CAAAyC,UAAA,KAAA8M,iCAAA,kBAAgE;UA0B3DvP,EADL,CAAAC,cAAA,eAAwB,SACnB,cAAQ;UAAAD,EAAA,CAAAgC,MAAA,IAAsB;UACnChC,EADmC,CAAAG,YAAA,EAAS,EAAI,EAC1C;UAKFH,EAFJ,CAAAC,cAAA,eAA0B,SACrB,cACO;UAAAD,EAAA,CAAAgC,MAAA,IAAwB;UAAAhC,EAAA,CAAAG,YAAA,EAAS;UACzCH,EAAA,CAAAE,SAAA,gBAAuD;UAE3DF,EADE,CAAAG,YAAA,EAAI,EACA;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UAMzBD,EALA,CAAAyC,UAAA,KAAA+M,+BAAA,gBAA0D,KAAAC,iCAAA,kBAKO;UAS/DzP,EADF,CAAAC,cAAA,eAAyB,iBAMtB;UAFCD,EAAA,CAAA0P,gBAAA,2BAAAC,2DAAArP,MAAA;YAAAN,EAAA,CAAA4P,kBAAA,CAAAlB,GAAA,CAAAhH,UAAA,EAAApH,MAAA,MAAAoO,GAAA,CAAAhH,UAAA,GAAApH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACxBN,EAAA,CAAAI,UAAA,yBAAAyP,yDAAA;YAAA,OAAenB,GAAA,CAAA7H,UAAA,EAAY;UAAA,EAAC;UAJ9B7G,EAAA,CAAAG,YAAA,EAKC;UACDH,EAAA,CAAAyC,UAAA,KAAAqN,oCAAA,qBAIC;UAKP9P,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;;;UA/OFH,EAAA,CAAAiC,SAAA,GAA0C;UAC1CjC,EADA,CAAAmB,UAAA,QAAAuN,GAAA,CAAApD,gBAAA,CAAAoD,GAAA,CAAA9I,IAAA,CAAAY,IAAA,CAAAuJ,MAAA,GAAA/P,EAAA,CAAAsB,aAAA,CAA0C,QAAAoN,GAAA,CAAA9I,IAAA,CAAAY,IAAA,CAAAwJ,QAAA,CAChB;UAKtBhQ,EAAA,CAAAiC,SAAA,GAAwB;UAAxBjC,EAAA,CAAAkC,iBAAA,CAAAwM,GAAA,CAAA9I,IAAA,CAAAY,IAAA,CAAAC,QAAA,CAAwB;UACtBzG,EAAA,CAAAiC,SAAA,GAAgC;UAAhCjC,EAAA,CAAAkC,iBAAA,CAAAwM,GAAA,CAAAvF,UAAA,CAAAuF,GAAA,CAAA9I,IAAA,CAAAqK,SAAA,EAAgC;UASpBjQ,EAAA,CAAAiC,SAAA,GAAuD;UAAvDjC,EAAA,CAAA4C,WAAA,oBAAA8L,GAAA,CAAAtN,YAAA,CAAAqC,IAAA,aAAuD;UAEvEzD,EAAA,CAAAiC,SAAA,EAAmB;UAAnBjC,EAAA,CAAAmB,UAAA,UAAAuN,GAAA,CAAAtN,YAAA,CAAmB;UAMtBpB,EAAA,CAAAiC,SAAA,EAAoC;UAApCjC,EAAA,CAAAmB,UAAA,UAAAuN,GAAA,CAAAtN,YAAA,kBAAAsN,GAAA,CAAAtN,YAAA,CAAAqC,IAAA,cAAoC;UAYpCzD,EAAA,CAAAiC,SAAA,EAAoC;UAApCjC,EAAA,CAAAmB,UAAA,UAAAuN,GAAA,CAAAtN,YAAA,kBAAAsN,GAAA,CAAAtN,YAAA,CAAAqC,IAAA,cAAoC;UAejCzD,EAAA,CAAAiC,SAAA,EAAoC;UAApCjC,EAAA,CAAAmB,UAAA,UAAAuN,GAAA,CAAAtN,YAAA,kBAAAsN,GAAA,CAAAtN,YAAA,CAAAqC,IAAA,cAAoC;UAapCzD,EAAA,CAAAiC,SAAA,EAA2B;UAA3BjC,EAAA,CAAAmB,UAAA,SAAAuN,GAAA,CAAA1K,UAAA,CAAAC,MAAA,KAA2B;UAkCJjE,EAAA,CAAAiC,SAAA,EAAoC;UAApCjC,EAAA,CAAA4C,WAAA,YAAA8L,GAAA,CAAA5G,kBAAA,CAAoC;UAO3D9H,EAAA,CAAAiC,SAAA,GAA+C;UAA/CjC,EAAA,CAAAmB,UAAA,SAAAuN,GAAA,CAAA9I,IAAA,CAAAC,QAAA,IAAA6I,GAAA,CAAA9I,IAAA,CAAAC,QAAA,CAAA5B,MAAA,KAA+C;UAqCpBjE,EAAA,CAAAiC,SAAA,EAAmE;UAAnEjC,EAAA,CAAAmB,UAAA,SAAAuN,GAAA,CAAA9I,IAAA,CAAAC,QAAA,IAAA6I,GAAA,CAAA9I,IAAA,CAAAC,QAAA,CAAA5B,MAAA,SAAAyK,GAAA,CAAA/I,eAAA,CAAmE;UAWhG3F,EAAA,CAAAiC,SAAA,GAAuB;UAAvBjC,EAAA,CAAA4C,WAAA,UAAA8L,GAAA,CAAAnH,OAAA,CAAuB;UAGpBvH,EAAA,CAAAiC,SAAA,EAAmD;UAAnDjC,EAAA,CAAA+C,UAAA,CAAA2L,GAAA,CAAAnH,OAAA,mCAAmD;UASjCvH,EAAA,CAAAiC,SAAA,GAAuB;UAAvBjC,EAAA,CAAA4C,WAAA,UAAA8L,GAAA,CAAAlH,OAAA,CAAuB;UAC3CxH,EAAA,CAAAiC,SAAA,EAAyD;UAAzDjC,EAAA,CAAA+C,UAAA,CAAA2L,GAAA,CAAAlH,OAAA,yCAAyD;UAKhCxH,EAAA,CAAAiC,SAAA,EAA8B;UAA9BjC,EAAA,CAAAmB,UAAA,SAAAuN,GAAA,CAAA9I,IAAA,CAAAC,QAAA,CAAA5B,MAAA,KAA8B;UA0BjDjE,EAAA,CAAAiC,SAAA,GAAsB;UAAtBjC,EAAA,CAAAuF,kBAAA,KAAAmJ,GAAA,CAAAjH,UAAA,WAAsB;UAMvBzH,EAAA,CAAAiC,SAAA,GAAwB;UAAxBjC,EAAA,CAAAkC,iBAAA,CAAAwM,GAAA,CAAA9I,IAAA,CAAAY,IAAA,CAAAC,QAAA,CAAwB;UAC1BzG,EAAA,CAAAiC,SAAA,EAAyC;UAAzCjC,EAAA,CAAAmB,UAAA,cAAAuN,GAAA,CAAA7E,aAAA,CAAA6E,GAAA,CAAA9I,IAAA,CAAA0C,OAAA,GAAAtI,EAAA,CAAAkQ,cAAA,CAAyC;UAMvBlQ,EAAA,CAAAiC,SAAA,GAA8B;UAA9BjC,EAAA,CAAAmB,UAAA,SAAAuN,GAAA,CAAA9I,IAAA,CAAAU,QAAA,CAAArC,MAAA,KAA8B;UAK/BjE,EAAA,CAAAiC,SAAA,EAAsB;UAAtBjC,EAAA,CAAAmB,UAAA,YAAAuN,GAAA,CAAA3E,iBAAA,GAAsB;UAY3C/J,EAAA,CAAAiC,SAAA,GAAwB;UAAxBjC,EAAA,CAAAmQ,gBAAA,YAAAzB,GAAA,CAAAhH,UAAA,CAAwB;UAIvB1H,EAAA,CAAAiC,SAAA,EAAuB;UAAvBjC,EAAA,CAAAmB,UAAA,SAAAuN,GAAA,CAAAhH,UAAA,CAAA2C,IAAA,GAAuB;;;qBA9OxBvK,YAAY,EAAAsQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAExQ,WAAW,EAAAyQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}