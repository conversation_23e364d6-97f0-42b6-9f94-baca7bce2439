{"ast": null, "code": "import { HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError, BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let ErrorHandlerService = /*#__PURE__*/(() => {\n  class ErrorHandlerService {\n    constructor() {\n      this.errorStateSubject = new BehaviorSubject({\n        hasError: false,\n        errors: [],\n        lastError: undefined\n      });\n      this.errorState$ = this.errorStateSubject.asObservable();\n    }\n    // Handle HTTP errors\n    handleHttpError(error, context) {\n      const appError = this.createAppError(error, context);\n      this.addError(appError);\n      return throwError(() => appError);\n    }\n    // Handle general errors\n    handleError(error, context) {\n      const appError = this.createAppError(error, context);\n      this.addError(appError);\n      return appError;\n    }\n    // Create standardized error object\n    createAppError(error, context) {\n      const errorId = this.generateErrorId();\n      let appError;\n      if (error instanceof HttpErrorResponse) {\n        appError = {\n          id: errorId,\n          type: this.getHttpErrorType(error.status),\n          message: this.getHttpErrorMessage(error),\n          details: {\n            status: error.status,\n            statusText: error.statusText,\n            url: error.url,\n            body: error.error\n          },\n          timestamp: new Date(),\n          context,\n          retryable: this.isRetryableHttpError(error.status),\n          userFriendly: this.isUserFriendlyHttpError(error.status)\n        };\n      } else if (error instanceof Error) {\n        appError = {\n          id: errorId,\n          type: 'client',\n          message: error.message,\n          details: {\n            name: error.name,\n            stack: error.stack\n          },\n          timestamp: new Date(),\n          context,\n          retryable: false,\n          userFriendly: false\n        };\n      } else {\n        appError = {\n          id: errorId,\n          type: 'unknown',\n          message: 'An unexpected error occurred',\n          details: error,\n          timestamp: new Date(),\n          context,\n          retryable: false,\n          userFriendly: true\n        };\n      }\n      return appError;\n    }\n    // Get error type based on HTTP status\n    getHttpErrorType(status) {\n      if (status >= 400 && status < 500) {\n        if (status === 401) return 'authentication';\n        if (status === 403) return 'authorization';\n        if (status === 422) return 'validation';\n        return 'client';\n      } else if (status >= 500) {\n        return 'server';\n      } else if (status === 0) {\n        return 'network';\n      }\n      return 'unknown';\n    }\n    // Get user-friendly error message\n    getHttpErrorMessage(error) {\n      // Try to extract message from error response\n      if (error.error?.message) {\n        return error.error.message;\n      }\n      // Default messages based on status code\n      switch (error.status) {\n        case 0:\n          return 'Unable to connect to the server. Please check your internet connection.';\n        case 400:\n          return 'Invalid request. Please check your input and try again.';\n        case 401:\n          return 'You need to log in to access this resource.';\n        case 403:\n          return 'You do not have permission to access this resource.';\n        case 404:\n          return 'The requested resource was not found.';\n        case 422:\n          return 'Please check your input and try again.';\n        case 429:\n          return 'Too many requests. Please wait a moment and try again.';\n        case 500:\n          return 'A server error occurred. Please try again later.';\n        case 502:\n          return 'Service temporarily unavailable. Please try again later.';\n        case 503:\n          return 'Service temporarily unavailable. Please try again later.';\n        default:\n          return `An error occurred (${error.status}). Please try again.`;\n      }\n    }\n    // Check if HTTP error is retryable\n    isRetryableHttpError(status) {\n      return [0, 408, 429, 500, 502, 503, 504].includes(status);\n    }\n    // Check if HTTP error should show user-friendly message\n    isUserFriendlyHttpError(status) {\n      return status >= 400 && status < 500;\n    }\n    // Add error to state\n    addError(error) {\n      const currentState = this.errorStateSubject.value;\n      const newErrors = [...currentState.errors, error];\n      // Keep only last 10 errors\n      if (newErrors.length > 10) {\n        newErrors.splice(0, newErrors.length - 10);\n      }\n      this.errorStateSubject.next({\n        hasError: true,\n        errors: newErrors,\n        lastError: error\n      });\n      // Log error for debugging\n      console.error('App Error:', error);\n    }\n    // Clear specific error\n    clearError(errorId) {\n      const currentState = this.errorStateSubject.value;\n      const filteredErrors = currentState.errors.filter(e => e.id !== errorId);\n      this.errorStateSubject.next({\n        hasError: filteredErrors.length > 0,\n        errors: filteredErrors,\n        lastError: filteredErrors[filteredErrors.length - 1]\n      });\n    }\n    // Clear all errors\n    clearAllErrors() {\n      this.errorStateSubject.next({\n        hasError: false,\n        errors: [],\n        lastError: undefined\n      });\n    }\n    // Get current error state\n    getCurrentErrorState() {\n      return this.errorStateSubject.value;\n    }\n    // Check if there are any errors\n    hasErrors() {\n      return this.errorStateSubject.value.hasError;\n    }\n    // Get errors by type\n    getErrorsByType(type) {\n      return this.errorStateSubject.value.errors.filter(e => e.type === type);\n    }\n    // Get retryable errors\n    getRetryableErrors() {\n      return this.errorStateSubject.value.errors.filter(e => e.retryable);\n    }\n    // Generate unique error ID\n    generateErrorId() {\n      return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    // Show user notification for error\n    showErrorNotification(error) {\n      if (error.userFriendly) {\n        // Show user-friendly notification\n        this.showToast(error.message, 'error');\n      } else {\n        // Show generic error message\n        this.showToast('An unexpected error occurred. Please try again.', 'error');\n      }\n    }\n    // Simple toast notification (can be replaced with proper toast service)\n    showToast(message, type) {\n      // This is a simple implementation - replace with your preferred toast library\n      console.log(`${type.toUpperCase()}: ${message}`);\n      // You can integrate with libraries like:\n      // - Angular Material Snackbar\n      // - ngx-toastr\n      // - Custom toast component\n    }\n    // Retry mechanism for retryable errors\n    retryOperation(operation, maxRetries = 3, delay = 1000) {\n      return new Observable(observer => {\n        let retryCount = 0;\n        const attemptOperation = () => {\n          operation().subscribe({\n            next: value => observer.next(value),\n            complete: () => observer.complete(),\n            error: error => {\n              const appError = this.createAppError(error);\n              if (appError.retryable && retryCount < maxRetries) {\n                retryCount++;\n                console.log(`Retrying operation (attempt ${retryCount}/${maxRetries})`);\n                setTimeout(attemptOperation, delay * retryCount);\n              } else {\n                this.addError(appError);\n                observer.error(appError);\n              }\n            }\n          });\n        };\n        attemptOperation();\n      });\n    }\n    static {\n      this.ɵfac = function ErrorHandlerService_Factory(t) {\n        return new (t || ErrorHandlerService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ErrorHandlerService,\n        factory: ErrorHandlerService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ErrorHandlerService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}