{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./realtime.service\";\nexport let WishlistService = /*#__PURE__*/(() => {\n  class WishlistService {\n    constructor(http, realtimeService) {\n      this.http = http;\n      this.realtimeService = realtimeService;\n      this.API_URL = 'http://localhost:5000/api';\n      this.wishlistItemsSubject = new BehaviorSubject([]);\n      this.wishlistCountSubject = new BehaviorSubject(0);\n      this.wishlistItems$ = this.wishlistItemsSubject.asObservable();\n      this.wishlistCount$ = this.wishlistCountSubject.asObservable();\n      this.initializeWishlist();\n      this.setupRealtimeListeners();\n    }\n    initializeWishlist() {\n      const token = localStorage.getItem('token');\n      if (token) {\n        // User is authenticated, load from API\n        this.loadWishlist();\n      } else {\n        // Guest user, load from local storage only\n        console.log('🔄 Guest user detected, loading wishlist from local storage only...');\n        this.loadWishlistFromLocalStorage();\n      }\n    }\n    setupRealtimeListeners() {\n      // Listen for real-time wishlist updates\n      this.realtimeService.onWishlistUpdate().subscribe(update => {\n        if (update) {\n          console.log('🔄 Real-time wishlist update received:', update);\n          switch (update.action) {\n            case 'add':\n              this.handleRealtimeWishlistAdd(update.item);\n              break;\n            case 'remove':\n              this.handleRealtimeWishlistRemove(update.itemId);\n              break;\n            default:\n              // For any other action, refresh the entire wishlist\n              this.loadWishlist();\n          }\n        }\n      });\n    }\n    handleRealtimeWishlistAdd(item) {\n      const currentItems = this.wishlistItemsSubject.value;\n      const existingIndex = currentItems.findIndex(wishlistItem => wishlistItem.product._id === item.product._id);\n      if (existingIndex >= 0) {\n        // Update existing item\n        currentItems[existingIndex] = item;\n      } else {\n        // Add new item\n        currentItems.push(item);\n      }\n      this.wishlistItemsSubject.next([...currentItems]);\n      this.wishlistCountSubject.next(currentItems.length);\n      console.log('❤️ Wishlist updated from another device');\n    }\n    handleRealtimeWishlistRemove(itemId) {\n      const currentItems = this.wishlistItemsSubject.value;\n      const filteredItems = currentItems.filter(item => item._id !== itemId);\n      this.wishlistItemsSubject.next(filteredItems);\n      this.wishlistCountSubject.next(filteredItems.length);\n      console.log('❤️ Item removed from wishlist on another device');\n    }\n    getWishlist(page = 1, limit = 12) {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.get(`${this.API_URL}/wishlist?page=${page}&limit=${limit}`, options).pipe(tap(response => {\n        if (response.success) {\n          this.wishlistItemsSubject.next(response.data.items);\n          this.wishlistCountSubject.next(response.data.pagination.totalItems);\n        }\n      }));\n    }\n    addToWishlist(productId) {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.post(`${this.API_URL}/wishlist`, {\n        productId\n      }, options).pipe(tap(response => {\n        if (response) {\n          // Emit real-time event\n          this.realtimeService.emitWishlistAdd({\n            productId\n          });\n        }\n        this.loadWishlist(); // Refresh wishlist after adding\n      }));\n    }\n    removeFromWishlist(productId) {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.delete(`${this.API_URL}/wishlist/${productId}`, options).pipe(tap(response => {\n        if (response) {\n          // Emit real-time event\n          this.realtimeService.emitWishlistRemove(productId);\n        }\n        this.loadWishlist(); // Refresh wishlist after removing\n      }));\n    }\n    clearWishlist() {\n      return this.http.delete(`${this.API_URL}/wishlist`).pipe(tap(() => {\n        this.wishlistItemsSubject.next([]);\n        this.wishlistCountSubject.next(0);\n      }));\n    }\n    moveToCart(productId, quantity = 1, size, color) {\n      return this.http.post(`${this.API_URL}/wishlist/move-to-cart/${productId}`, {\n        quantity,\n        size,\n        color\n      }).pipe(tap(() => {\n        this.loadWishlist(); // Refresh wishlist after moving\n      }));\n    }\n    getWishlistCount() {\n      return this.wishlistCountSubject.value;\n    }\n    isInWishlist(productId) {\n      const items = this.wishlistItemsSubject.value;\n      return items.some(item => item.product._id === productId);\n    }\n    toggleWishlist(productId) {\n      if (this.isInWishlist(productId)) {\n        return this.removeFromWishlist(productId);\n      } else {\n        return this.addToWishlist(productId);\n      }\n    }\n    loadWishlist() {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('❌ No authentication token, using local storage fallback');\n        this.loadWishlistFromLocalStorage();\n        return;\n      }\n      this.getWishlist().subscribe({\n        next: response => {\n          // Wishlist is already updated in the tap operator\n        },\n        error: error => {\n          console.error('Failed to load wishlist:', error);\n          if (error.status === 401) {\n            console.log('❌ Authentication failed, clearing token');\n            localStorage.removeItem('token');\n          }\n          // Use localStorage as fallback\n          this.loadWishlistFromLocalStorage();\n        }\n      });\n    }\n    loadWishlistFromLocalStorage() {\n      try {\n        const savedWishlist = localStorage.getItem('wishlist');\n        if (savedWishlist) {\n          const wishlistData = JSON.parse(savedWishlist);\n          this.wishlistItemsSubject.next(wishlistData.items || []);\n          this.wishlistCountSubject.next(wishlistData.items?.length || 0);\n        }\n      } catch (error) {\n        console.error('Failed to load wishlist from localStorage:', error);\n      }\n    }\n    // Fallback methods for offline functionality\n    addToWishlistOffline(product) {\n      try {\n        const savedWishlist = localStorage.getItem('wishlist');\n        let wishlistData = savedWishlist ? JSON.parse(savedWishlist) : {\n          items: []\n        };\n        const existingItem = wishlistData.items.find(item => item.product._id === product._id);\n        if (!existingItem) {\n          wishlistData.items.push({\n            _id: Date.now().toString(),\n            product: {\n              _id: product._id,\n              name: product.name,\n              price: product.price,\n              originalPrice: product.originalPrice,\n              images: product.images,\n              brand: product.brand,\n              discount: product.discount,\n              rating: product.rating,\n              analytics: product.analytics\n            },\n            addedAt: new Date()\n          });\n          localStorage.setItem('wishlist', JSON.stringify(wishlistData));\n          this.wishlistItemsSubject.next(wishlistData.items);\n          this.wishlistCountSubject.next(wishlistData.items.length);\n        }\n      } catch (error) {\n        console.error('Failed to add to wishlist offline:', error);\n      }\n    }\n    removeFromWishlistOffline(productId) {\n      try {\n        const savedWishlist = localStorage.getItem('wishlist');\n        if (savedWishlist) {\n          let wishlistData = JSON.parse(savedWishlist);\n          wishlistData.items = wishlistData.items.filter(item => item.product._id !== productId);\n          localStorage.setItem('wishlist', JSON.stringify(wishlistData));\n          this.wishlistItemsSubject.next(wishlistData.items);\n          this.wishlistCountSubject.next(wishlistData.items.length);\n        }\n      } catch (error) {\n        console.error('Failed to remove from wishlist offline:', error);\n      }\n    }\n    toggleWishlistOffline(product) {\n      if (this.isInWishlist(product._id)) {\n        this.removeFromWishlistOffline(product._id);\n      } else {\n        this.addToWishlistOffline(product);\n      }\n    }\n    // Sync with server when online\n    syncWithServer() {\n      return this.getWishlist().pipe(tap(response => {\n        if (response.success) {\n          // Update localStorage with server data\n          localStorage.setItem('wishlist', JSON.stringify({\n            items: response.data.items\n          }));\n        }\n      }));\n    }\n    static {\n      this.ɵfac = function WishlistService_Factory(t) {\n        return new (t || WishlistService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.RealtimeService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: WishlistService,\n        factory: WishlistService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return WishlistService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}