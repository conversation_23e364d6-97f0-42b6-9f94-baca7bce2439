{"ast": null, "code": "import { Overlay, CdkOverlayOrigin, CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';\nimport { NgClass, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Self, Attribute, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { _countGroupLabelsBeforeOption, _getOptionScrollPosition, _ErrorStateTracker, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nexport { MatOptgroup, MatOption } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nexport { Mat<PERSON><PERSON>r, MatFormField, MatHint, MatLabel, MatPrefix, MatSuffix } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, distinctUntilChanged, takeUntil, take } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nfunction MatSelect_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.triggerValue);\n  }\n}\nfunction MatSelect_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtemplate(1, MatSelect_Conditional_5_Conditional_1_Template, 1, 0)(2, MatSelect_Conditional_5_Conditional_2_Template, 2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.customTrigger ? 1 : 2);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 1);\n    i0.ɵɵlistener(\"@transformPanel.done\", function MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._panelDoneAnimatingStream.next($event.toState));\n    })(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r1._getPanelTheme(), \"\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelClass)(\"@transformPanel\", \"showing\");\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"-panel\")(\"aria-multiselectable\", ctx_r1.multiple)(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby());\n  }\n}\nconst matSelectAnimations = {\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: trigger('transformPanelWrap', [transition('* => void', query('@transformPanel', [animateChild()], {\n    optional: true\n  }))]),\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: trigger('transformPanel', [state('void', style({\n    opacity: 0,\n    transform: 'scale(1, 0.8)'\n  })), transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1, 1)'\n  }))), transition('* => void', animate('100ms linear', style({\n    opacity: 0\n  })))])\n};\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  constructor(/** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\nclass MatSelect {\n  /** Scrolls a particular option into the view. */\n  _scrollOptionIntoView(index) {\n    const option = this.options.toArray()[index];\n    if (option) {\n      const panel = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n      }\n    }\n  }\n  /** Called when the panel has been opened and the overlay has settled on its final position. */\n  _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n  /** Creates a change event object that should be emitted by the select. */\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /** Whether the select is focused. */\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  /** Placeholder to be shown if no value has been selected. */\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether the component is required. */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  /** Whether the user should be allowed to select multiple options. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n    this._multiple = value;\n  }\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  /** Object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Unique id of the element. */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  /** Whether the select is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  constructor(_viewportRuler, _changeDetectorRef,\n  /**\n   * @deprecated Unused param, will be removed.\n   * @breaking-change 19.0.0\n   */\n  _unusedNgZone, defaultErrorStateMatcher, _elementRef, _dir, parentForm, parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n    this._viewportRuler = _viewportRuler;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    this._parentFormField = _parentFormField;\n    this.ngControl = ngControl;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._defaultOptions = _defaultOptions;\n    /**\n     * This position config ensures that the top \"start\" corner of the overlay\n     * is aligned with with the top \"start\" of the origin by default (overlapping\n     * the trigger completely). If the panel cannot fit below the trigger, it\n     * will fall back to a position above the trigger.\n     */\n    this._positions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }];\n    /** Whether or not the overlay panel is open. */\n    this._panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    this._compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n    this._uid = `mat-select-${nextUniqueId++}`;\n    /** Current `aria-labelledby` value for the select trigger. */\n    this._triggerAriaLabelledBy = null;\n    /** Emits whenever the component is destroyed. */\n    this._destroy = new Subject();\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    this.stateChanges = new Subject();\n    /**\n     * Disable the automatic labeling to avoid issues like #27241.\n     * @docs-private\n     */\n    this.disableAutomaticLabeling = true;\n    /** `View -> model callback called when value changes` */\n    this._onChange = () => {};\n    /** `View -> model callback called when select has been touched` */\n    this._onTouched = () => {};\n    /** ID for the DOM node containing the select's value. */\n    this._valueId = `mat-select-value-${nextUniqueId++}`;\n    /** Emits when the panel element is finished transforming in. */\n    this._panelDoneAnimatingStream = new Subject();\n    this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    this._focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n    this.controlType = 'mat-select';\n    /** Whether the select is disabled. */\n    this.disabled = false;\n    /** Whether ripples in the select are disabled. */\n    this.disableRipple = false;\n    /** Tab index of the select. */\n    this.tabIndex = 0;\n    this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    this._multiple = false;\n    /** Whether to center the active option over the trigger. */\n    this.disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /** Aria label of the select. */\n    this.ariaLabel = '';\n    /**\n     * Width of the panel. If set to `auto`, the panel will match the trigger width.\n     * If set to null or an empty string, the panel will grow to match the longest option's text.\n     */\n    this.panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto';\n    this._initialized = new Subject();\n    /** Combined stream of all of the child options' change events. */\n    this.optionSelectionChanges = defer(() => {\n      const options = this.options;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n    this.openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the select has been closed. */\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the selected value has been changed by the user. */\n    this.selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    this.valueChange = new EventEmitter();\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    this._trackedModal = null;\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    this._skipPredicate = option => {\n      if (this.panelOpen) {\n        // Support keyboard focusing disabled options in an ARIA listbox.\n        return false;\n      }\n      // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n      // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n      // closed.\n      return option.disabled;\n    };\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (_defaultOptions?.typeaheadDebounceInterval != null) {\n      this.typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = parseInt(tabIndex) || 0;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next();\n    // We need `distinctUntilChanged` here, because some browsers will\n    // fire the animation end event twice for the same animation. See:\n    // https://github.com/angular/angular/issues/24084\n    this._panelDoneAnimatingStream.pipe(distinctUntilChanged(), takeUntil(this._destroy)).subscribe(() => this._panelDoneAnimating(this.panelOpen));\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngAfterContentInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._initKeyManager();\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n        this._previousControl = ngControl.control;\n      }\n      this.updateErrorState();\n    }\n  }\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by the input, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n    this._clearFromModal();\n  }\n  /** Toggles the overlay panel open or closed. */\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n  open() {\n    if (!this._canOpen()) {\n      return;\n    }\n    // It's important that we read this as late as possible, because doing so earlier will\n    // return a different element since it's based on queries in the form field which may\n    // not have run yet. Also this needs to be assigned before we measure the overlay width.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n    this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n    this._applyModalPanelOwnership();\n    this._panelOpen = true;\n    this._keyManager.withHorizontalOrientation(null);\n    this._highlightCorrectOption();\n    this._changeDetectorRef.markForCheck();\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n  }\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the reference to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (!this._trackedModal) {\n      // Most commonly, the autocomplete trigger is not used inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    this._trackedModal = null;\n  }\n  /** Closes the overlay panel and focuses the host element. */\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n      // Required for the MDC form field to pick up when the overlay has been closed.\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The value displayed in the trigger. */\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Refreshes the error state of the select. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Whether the element is in RTL mode. */\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n    // Open the select on ALT + arrow key to match the native <select>\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    this._focused = false;\n    this._keyManager?.cancelTypeahead();\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Callback that is invoked when the overlay panel has been attached.\n   */\n  _onAttached() {\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n  }\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  _setSelectionByValue(value) {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n      try {\n        // Treat null as a special reset value.\n        return option.value != null && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n  /** Gets how wide the overlay panel should be. */\n  _getOverlayWidth(preferredOrigin) {\n    if (this.panelWidth === 'auto') {\n      const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n      return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n    return this.panelWidth === null ? '' : this.panelWidth;\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n      // be the result of an expression changing. We have to use `detectChanges` in order\n      // to avoid \"changed after checked\" errors (see #14793).\n      this._changeDetectorRef.detectChanges();\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n    if (option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n      if (this.multiple) {\n        this._sortValues();\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges(fallbackValue) {\n    let valueToEmit;\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first *enabled* option.\n   */\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < this.options.length; index++) {\n          const option = this.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        this._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n  _canOpen() {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0;\n  }\n  /** Focuses the select element. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n  _getTriggerAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    let value = (labelId ? labelId + ' ' : '') + this._valueId;\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n    return value;\n  }\n  /** Called when the overlay panel is done animating. */\n  _panelDoneAnimating(isOpen) {\n    this.openedChange.emit(isOpen);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n  }\n  static {\n    this.ɵfac = function MatSelect_Factory(t) {\n      return new (t || MatSelect)(i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.ErrorStateMatcher), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NgForm, 8), i0.ɵɵdirectiveInject(i4.FormGroupDirective, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8), i0.ɵɵdirectiveInject(i4.NgControl, 10), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SELECT_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.LiveAnnouncer), i0.ɵɵdirectiveInject(MAT_SELECT_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSelect,\n      selectors: [[\"mat-select\"]],\n      contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n        }\n      },\n      viewQuery: function MatSelect_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"combobox\", \"aria-autocomplete\", \"none\", \"aria-haspopup\", \"listbox\", 1, \"mat-mdc-select\"],\n      hostVars: 19,\n      hostBindings: function MatSelect_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n            return ctx._onFocus();\n          })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n            return ctx._onBlur();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n          i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n        }\n      },\n      inputs: {\n        userAriaDescribedBy: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"userAriaDescribedBy\"],\n        panelClass: \"panelClass\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        hideSingleSelectionIndicator: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n        placeholder: \"placeholder\",\n        required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n        multiple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiple\", \"multiple\", booleanAttribute],\n        disableOptionCentering: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute],\n        compareWith: \"compareWith\",\n        value: \"value\",\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        errorStateMatcher: \"errorStateMatcher\",\n        typeaheadDebounceInterval: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute],\n        sortComparator: \"sortComparator\",\n        id: \"id\",\n        panelWidth: \"panelWidth\"\n      },\n      outputs: {\n        openedChange: \"openedChange\",\n        _openedStream: \"opened\",\n        _closedStream: \"closed\",\n        selectionChange: \"selectionChange\",\n        valueChange: \"valueChange\"\n      },\n      exportAs: [\"matSelect\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 11,\n      vars: 8,\n      consts: [[\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [\"panel\", \"\"], [\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [1, \"mat-mdc-select-value\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"backdropClick\", \"attach\", \"detach\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\"], [1, \"mat-mdc-select-value-text\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"keydown\", \"ngClass\"]],\n      template: function MatSelect_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.open());\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, MatSelect_Conditional_4_Template, 2, 1, \"span\", 4)(5, MatSelect_Conditional_5_Template, 3, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 7);\n          i0.ɵɵelement(9, \"path\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 9, \"ng-template\", 9);\n          i0.ɵɵlistener(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.close());\n          })(\"attach\", function MatSelect_Template_ng_template_attach_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onAttached());\n          })(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.close());\n          });\n        }\n        if (rf & 2) {\n          const fallbackOverlayOrigin_r4 = i0.ɵɵreference(1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"id\", ctx._valueId);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.empty ? 4 : 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || fallbackOverlayOrigin_r4)(\"cdkConnectedOverlayOpen\", ctx.panelOpen)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth);\n        }\n      },\n      dependencies: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSelectAnimations.transformPanel]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-autocomplete': 'none',\n        'aria-haspopup': 'listbox',\n        'class': 'mat-mdc-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        '[class.mat-mdc-select-disabled]': 'disabled',\n        '[class.mat-mdc-select-invalid]': 'errorState',\n        '[class.mat-mdc-select-required]': 'required',\n        '[class.mat-mdc-select-empty]': 'empty',\n        '[class.mat-mdc-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      animations: [matSelectAnimations.transformPanel],\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      standalone: true,\n      imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"]\n    }]\n  }], () => [{\n    type: i1.ViewportRuler\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i2.ErrorStateMatcher\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.NgForm,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.FormGroupDirective,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i6.MatFormField,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_FORM_FIELD]\n    }]\n  }, {\n    type: i4.NgControl,\n    decorators: [{\n      type: Self\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SELECT_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: i5.LiveAnnouncer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SELECT_CONFIG]\n    }]\n  }], {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableOptionCentering: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n  static {\n    this.ɵfac = function MatSelectTrigger_Factory(t) {\n      return new (t || MatSelectTrigger)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSelectTrigger,\n      selectors: [[\"mat-select-trigger\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass MatSelectModule {\n  static {\n    this.ɵfac = function MatSelectModule_Factory(t) {\n      return new (t || MatSelectModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSelectModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, matSelectAnimations };", "map": {"version": 3, "names": ["Overlay", "CdkOverlayOrigin", "CdkConnectedOverlay", "OverlayModule", "Ng<PERSON><PERSON>", "CommonModule", "i0", "InjectionToken", "inject", "EventEmitter", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "Self", "Attribute", "ContentChildren", "ContentChild", "Input", "ViewChild", "Output", "Directive", "NgModule", "i2", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "_ErrorStateTracker", "MAT_OPTION_PARENT_COMPONENT", "MatOption", "MAT_OPTGROUP", "MatOptionModule", "MatCommonModule", "MatOptgroup", "i6", "MAT_FORM_FIELD", "MatFormFieldControl", "MatFormFieldModule", "<PERSON><PERSON><PERSON><PERSON>", "MatFormField", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "MatPrefix", "MatSuffix", "i1", "CdkScrollableModule", "i5", "removeAriaReferencedId", "addAriaReferencedId", "ActiveDescendantKeyManager", "i3", "SelectionModel", "DOWN_ARROW", "UP_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "ENTER", "SPACE", "hasModifierKey", "A", "i4", "Validators", "Subject", "defer", "merge", "startWith", "switchMap", "filter", "map", "distinctUntilChanged", "takeUntil", "take", "trigger", "transition", "query", "animate<PERSON><PERSON><PERSON>", "state", "style", "animate", "_c0", "_c1", "_c2", "_c3", "MatSelect_Conditional_4_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "placeholder", "MatSelect_Conditional_5_Conditional_1_Template", "ɵɵprojection", "MatSelect_Conditional_5_Conditional_2_Template", "triggerValue", "MatSelect_Conditional_5_Template", "ɵɵtemplate", "ɵɵconditional", "customTrigger", "MatSelect_ng_template_10_Template", "_r3", "ɵɵgetCurrentView", "ɵɵlistener", "MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "_panelDoneAnimatingStream", "next", "toState", "MatSelect_ng_template_10_Template_div_keydown_0_listener", "_handleKeydown", "ɵɵclassMapInterpolate1", "_getPanelTheme", "ɵɵproperty", "panelClass", "ɵɵattribute", "id", "multiple", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "matSelectAnimations", "transformPanelWrap", "optional", "transformPanel", "opacity", "transform", "getMatSelectDynamicMultipleError", "Error", "getMatSelectNonArrayValueError", "getMatSelectNonFunctionValueError", "nextUniqueId", "MAT_SELECT_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "reposition", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MAT_SELECT_TRIGGER", "MatSelectChange", "constructor", "source", "value", "MatSelect", "_scrollOptionIntoView", "index", "option", "options", "toArray", "panel", "nativeElement", "labelCount", "optionGroups", "element", "_getHostElement", "scrollTop", "offsetTop", "offsetHeight", "_positioningSettled", "_keyManager", "activeItemIndex", "_getChangeEvent", "focused", "_focused", "_panelOpen", "hideSingleSelectionIndicator", "_hideSingleSelectionIndicator", "_syncParentProperties", "_placeholder", "stateChanges", "required", "_required", "ngControl", "control", "hasValidator", "_multiple", "_selectionModel", "ngDevMode", "compareWith", "_compareWith", "fn", "_initializeSelection", "_value", "newValue", "hasAssigned", "_assignValue", "_onChange", "errorStateMatcher", "_errorStateTracker", "matcher", "_id", "_uid", "errorState", "_viewportRuler", "_changeDetectorRef", "_unusedNgZone", "defaultErrorStateMatcher", "_elementRef", "_dir", "parentForm", "parentFormGroup", "_parentFormField", "tabIndex", "scrollStrategyFactory", "_liveAnnouncer", "_defaultOptions", "_positions", "originX", "originY", "overlayX", "overlayY", "o1", "o2", "_triggerAriaLabelledBy", "_destroy", "disableAutomaticLabeling", "_onTouched", "_valueId", "_overlayPanelClass", "overlayPanelClass", "controlType", "disabled", "disable<PERSON><PERSON><PERSON>", "disableOptionCentering", "panelWidth", "_initialized", "optionSelectionChanges", "changes", "pipe", "onSelectionChange", "openedChange", "_openedStream", "o", "_closedStream", "selectionChange", "valueChange", "_trackedModal", "_skipPredicate", "panelOpen", "valueAccessor", "typeaheadDebounceInterval", "_scrollStrategyFactory", "_scrollStrategy", "parseInt", "ngOnInit", "subscribe", "_panelDoneAnimating", "change", "_overlayWidth", "_getOverlayWidth", "_preferredOverlayOrigin", "detectChanges", "ngAfterContentInit", "complete", "_initKeyManager", "changed", "event", "added", "for<PERSON>ach", "select", "removed", "deselect", "_resetOptions", "ngDoCheck", "newAria<PERSON><PERSON><PERSON><PERSON>", "_getTriggerAriaLabe<PERSON>by", "setAttribute", "removeAttribute", "_previousControl", "undefined", "updateErrorState", "ngOnChanges", "withTypeAhead", "ngOnDestroy", "destroy", "_clearFromModal", "toggle", "close", "open", "_canOpen", "getConnectedOverlayOrigin", "_applyModalPanelOwnership", "withHorizontalOrientation", "_highlightCorrectOption", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modal", "closest", "panelId", "_isRtl", "writeValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "selected", "empty", "selectedOptions", "viewValue", "reverse", "join", "_handleOpenKeydown", "_handleClosedKeydown", "keyCode", "isArrowKey", "isOpenKey", "manager", "isTyping", "altKey", "preventDefault", "previouslySelectedOption", "onKeydown", "selectedOption", "announce", "activeItem", "_selectViaInteraction", "ctrl<PERSON>ey", "hasDeselectedOptions", "some", "opt", "previouslyFocusedIndex", "shift<PERSON>ey", "_onFocus", "_onBlur", "cancelTypeahead", "_onAttached", "_overlayDir", "positionChange", "color", "isEmpty", "Promise", "resolve", "then", "_setSelectionByValue", "setInactiveStyles", "clear", "Array", "isArray", "currentValue", "_selectOptionByValue", "_sortValues", "correspondingOption", "updateActiveItem", "find", "isSelected", "error", "console", "warn", "preferred<PERSON><PERSON>in", "refToMeasure", "elementRef", "getBoundingClientRect", "width", "withVerticalOrientation", "withHomeAndEnd", "withPageUpDown", "withAllowedModifierKeys", "skipPredicate", "tabOut", "focus", "changedOrDestroyed", "_onSelect", "isUserInput", "_stateChanges", "wasSelected", "_propagateChanges", "setActiveItem", "sort", "a", "b", "sortComparator", "indexOf", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "emit", "firstEnabledOptionIndex", "length", "get", "labelId", "getLabelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAriaActiveDescendant", "isOpen", "setDescribedByIds", "ids", "onContainerClick", "shouldLabelFloat", "ɵfac", "MatSelect_Factory", "t", "ɵɵdirectiveInject", "ViewportRuler", "ChangeDetectorRef", "NgZone", "ErrorStateMatcher", "ElementRef", "Directionality", "NgForm", "FormGroupDirective", "NgControl", "ɵɵinjectAttribute", "LiveAnnouncer", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "MatSelect_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatSelect_Query", "ɵɵviewQuery", "hostAttrs", "hostVars", "hostBindings", "MatSelect_HostBindings", "MatSelect_keydown_HostBindingHandler", "MatSelect_focus_HostBindingHandler", "MatSelect_blur_HostBindingHandler", "toString", "ɵɵclassProp", "inputs", "userAriaDescribedBy", "ɵɵInputFlags", "None", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "useExisting", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatSelect_Template", "_r1", "ɵɵprojectionDef", "MatSelect_Template_div_click_0_listener", "ɵɵnamespaceSVG", "ɵɵelement", "MatSelect_Template_ng_template_backdropClick_10_listener", "MatSelect_Template_ng_template_attach_10_listener", "MatSelect_Template_ng_template_detach_10_listener", "fallbackOverlayOrigin_r4", "ɵɵreference", "dependencies", "styles", "encapsulation", "data", "animation", "changeDetection", "ɵsetClassMetadata", "args", "selector", "OnPush", "host", "animations", "providers", "imports", "decorators", "descendants", "MatSelectTrigger", "MatSelectTrigger_Factory", "ɵdir", "ɵɵdefineDirective", "MatSelectModule", "MatSelectModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["E:/DFashion/frontend/node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["import { Overlay, CdkOverlayOrigin, CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';\nimport { NgClass, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Self, Attribute, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { _countGroupLabelsBeforeOption, _getOptionScrollPosition, _ErrorStateTracker, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nexport { MatOptgroup, MatOption } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nexport { Mat<PERSON><PERSON>r, MatFormField, MatHint, MatLabel, MatPrefix, MatSuffix } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, distinctUntilChanged, takeUntil, take } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst matSelectAnimations = {\n    /**\n     * This animation ensures the select's overlay panel animation (transformPanel) is called when\n     * closing the select.\n     * This is needed due to https://github.com/angular/angular/issues/23302\n     */\n    transformPanelWrap: trigger('transformPanelWrap', [\n        transition('* => void', query('@transformPanel', [animateChild()], { optional: true })),\n    ]),\n    /** This animation transforms the select's overlay panel on and off the page. */\n    transformPanel: trigger('transformPanel', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(1, 0.8)',\n        })),\n        transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1, 1)',\n        }))),\n        transition('* => void', animate('100ms linear', style({ opacity: 0 }))),\n    ]),\n};\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n    return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n    return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n    return Error('`compareWith` must be a function.');\n}\n\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_SELECT_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n    constructor(\n    /** Reference to the select that emitted the change event. */\n    source, \n    /** Current value of the select that emitted the event. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\nclass MatSelect {\n    /** Scrolls a particular option into the view. */\n    _scrollOptionIntoView(index) {\n        const option = this.options.toArray()[index];\n        if (option) {\n            const panel = this.panel.nativeElement;\n            const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n            const element = option._getHostElement();\n            if (index === 0 && labelCount === 1) {\n                // If we've got one group label before the option and we're at the top option,\n                // scroll the list to the top. This is better UX than scrolling the list to the\n                // top of the option, because it allows the user to read the top group's label.\n                panel.scrollTop = 0;\n            }\n            else {\n                panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n            }\n        }\n    }\n    /** Called when the panel has been opened and the overlay has settled on its final position. */\n    _positioningSettled() {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    /** Creates a change event object that should be emitted by the select. */\n    _getChangeEvent(value) {\n        return new MatSelectChange(this, value);\n    }\n    /** Whether the select is focused. */\n    get focused() {\n        return this._focused || this._panelOpen;\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncParentProperties();\n    }\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    /** Whether the component is required. */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = value;\n        this.stateChanges.next();\n    }\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectDynamicMultipleError();\n        }\n        this._multiple = value;\n    }\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectNonFunctionValueError();\n        }\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /** Value of the select control. */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        const hasAssigned = this._assignValue(newValue);\n        if (hasAssigned) {\n            this._onChange(newValue);\n        }\n    }\n    /** Object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /** Unique id of the element. */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n        this.stateChanges.next();\n    }\n    /** Whether the select is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    constructor(_viewportRuler, _changeDetectorRef, \n    /**\n     * @deprecated Unused param, will be removed.\n     * @breaking-change 19.0.0\n     */\n    _unusedNgZone, defaultErrorStateMatcher, _elementRef, _dir, parentForm, parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n        this._viewportRuler = _viewportRuler;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        this._parentFormField = _parentFormField;\n        this.ngControl = ngControl;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._defaultOptions = _defaultOptions;\n        /**\n         * This position config ensures that the top \"start\" corner of the overlay\n         * is aligned with with the top \"start\" of the origin by default (overlapping\n         * the trigger completely). If the panel cannot fit below the trigger, it\n         * will fall back to a position above the trigger.\n         */\n        this._positions = [\n            {\n                originX: 'start',\n                originY: 'bottom',\n                overlayX: 'start',\n                overlayY: 'top',\n            },\n            {\n                originX: 'end',\n                originY: 'bottom',\n                overlayX: 'end',\n                overlayY: 'top',\n            },\n            {\n                originX: 'start',\n                originY: 'top',\n                overlayX: 'start',\n                overlayY: 'bottom',\n                panelClass: 'mat-mdc-select-panel-above',\n            },\n            {\n                originX: 'end',\n                originY: 'top',\n                overlayX: 'end',\n                overlayY: 'bottom',\n                panelClass: 'mat-mdc-select-panel-above',\n            },\n        ];\n        /** Whether or not the overlay panel is open. */\n        this._panelOpen = false;\n        /** Comparison function to specify which option is displayed. Defaults to object equality. */\n        this._compareWith = (o1, o2) => o1 === o2;\n        /** Unique id for this input. */\n        this._uid = `mat-select-${nextUniqueId++}`;\n        /** Current `aria-labelledby` value for the select trigger. */\n        this._triggerAriaLabelledBy = null;\n        /** Emits whenever the component is destroyed. */\n        this._destroy = new Subject();\n        /**\n         * Emits whenever the component state changes and should cause the parent\n         * form-field to update. Implemented as part of `MatFormFieldControl`.\n         * @docs-private\n         */\n        this.stateChanges = new Subject();\n        /**\n         * Disable the automatic labeling to avoid issues like #27241.\n         * @docs-private\n         */\n        this.disableAutomaticLabeling = true;\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when select has been touched` */\n        this._onTouched = () => { };\n        /** ID for the DOM node containing the select's value. */\n        this._valueId = `mat-select-value-${nextUniqueId++}`;\n        /** Emits when the panel element is finished transforming in. */\n        this._panelDoneAnimatingStream = new Subject();\n        this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n        this._focused = false;\n        /** A name for this control that can be used by `mat-form-field`. */\n        this.controlType = 'mat-select';\n        /** Whether the select is disabled. */\n        this.disabled = false;\n        /** Whether ripples in the select are disabled. */\n        this.disableRipple = false;\n        /** Tab index of the select. */\n        this.tabIndex = 0;\n        this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n        this._multiple = false;\n        /** Whether to center the active option over the trigger. */\n        this.disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n        /** Aria label of the select. */\n        this.ariaLabel = '';\n        /**\n         * Width of the panel. If set to `auto`, the panel will match the trigger width.\n         * If set to null or an empty string, the panel will grow to match the longest option's text.\n         */\n        this.panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined'\n            ? this._defaultOptions.panelWidth\n            : 'auto';\n        this._initialized = new Subject();\n        /** Combined stream of all of the child options' change events. */\n        this.optionSelectionChanges = defer(() => {\n            const options = this.options;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n        });\n        /** Event emitted when the select panel has been toggled. */\n        this.openedChange = new EventEmitter();\n        /** Event emitted when the select has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the select has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the selected value has been changed by the user. */\n        this.selectionChange = new EventEmitter();\n        /**\n         * Event that emits whenever the raw value of the select changes. This is here primarily\n         * to facilitate the two-way binding for the `value` input.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        /**\n         * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n         * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n         * panel. Track the modal we have changed so we can undo the changes on destroy.\n         */\n        this._trackedModal = null;\n        // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n        // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n        // recommendation.\n        //\n        // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n        // makes a few exceptions for compound widgets.\n        //\n        // From [Developing a Keyboard Interface](\n        // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n        //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n        //   Listbox...\"\n        //\n        // The user can focus disabled options using the keyboard, but the user cannot click disabled\n        // options.\n        this._skipPredicate = (option) => {\n            if (this.panelOpen) {\n                // Support keyboard focusing disabled options in an ARIA listbox.\n                return false;\n            }\n            // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n            // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n            // closed.\n            return option.disabled;\n        };\n        if (this.ngControl) {\n            // Note: we provide the value accessor through here, instead of\n            // the `providers` to avoid running into a circular import.\n            this.ngControl.valueAccessor = this;\n        }\n        // Note that we only want to set this when the defaults pass it in, otherwise it should\n        // stay as `undefined` so that it falls back to the default in the key manager.\n        if (_defaultOptions?.typeaheadDebounceInterval != null) {\n            this.typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, ngControl, parentFormGroup, parentForm, this.stateChanges);\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this._scrollStrategy = this._scrollStrategyFactory();\n        this.tabIndex = parseInt(tabIndex) || 0;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple);\n        this.stateChanges.next();\n        // We need `distinctUntilChanged` here, because some browsers will\n        // fire the animation end event twice for the same animation. See:\n        // https://github.com/angular/angular/issues/24084\n        this._panelDoneAnimatingStream\n            .pipe(distinctUntilChanged(), takeUntil(this._destroy))\n            .subscribe(() => this._panelDoneAnimating(this.panelOpen));\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => {\n            if (this.panelOpen) {\n                this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n                this._changeDetectorRef.detectChanges();\n            }\n        });\n    }\n    ngAfterContentInit() {\n        this._initialized.next();\n        this._initialized.complete();\n        this._initKeyManager();\n        this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n            event.added.forEach(option => option.select());\n            event.removed.forEach(option => option.deselect());\n        });\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n            this._resetOptions();\n            this._initializeSelection();\n        });\n    }\n    ngDoCheck() {\n        const newAriaLabelledby = this._getTriggerAriaLabelledby();\n        const ngControl = this.ngControl;\n        // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n        // is computed as a result of a content query which can cause this binding to trigger a\n        // \"changed after checked\" error.\n        if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n            const element = this._elementRef.nativeElement;\n            this._triggerAriaLabelledBy = newAriaLabelledby;\n            if (newAriaLabelledby) {\n                element.setAttribute('aria-labelledby', newAriaLabelledby);\n            }\n            else {\n                element.removeAttribute('aria-labelledby');\n            }\n        }\n        if (ngControl) {\n            // The disabled state might go out of sync if the form group is swapped out. See #17860.\n            if (this._previousControl !== ngControl.control) {\n                if (this._previousControl !== undefined &&\n                    ngControl.disabled !== null &&\n                    ngControl.disabled !== this.disabled) {\n                    this.disabled = ngControl.disabled;\n                }\n                this._previousControl = ngControl.control;\n            }\n            this.updateErrorState();\n        }\n    }\n    ngOnChanges(changes) {\n        // Updating the disabled state is handled by the input, but we need to additionally let\n        // the parent form field know to run change detection when the disabled state changes.\n        if (changes['disabled'] || changes['userAriaDescribedBy']) {\n            this.stateChanges.next();\n        }\n        if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n            this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroy.next();\n        this._destroy.complete();\n        this.stateChanges.complete();\n        this._clearFromModal();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n        this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n        if (!this._canOpen()) {\n            return;\n        }\n        // It's important that we read this as late as possible, because doing so earlier will\n        // return a different element since it's based on queries in the form field which may\n        // not have run yet. Also this needs to be assigned before we measure the overlay width.\n        if (this._parentFormField) {\n            this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n        }\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._applyModalPanelOwnership();\n        this._panelOpen = true;\n        this._keyManager.withHorizontalOrientation(null);\n        this._highlightCorrectOption();\n        this._changeDetectorRef.markForCheck();\n        // Required for the MDC form field to pick up when the overlay has been opened.\n        this.stateChanges.next();\n    }\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the reference to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (!this._trackedModal) {\n            // Most commonly, the autocomplete trigger is not used inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        this._trackedModal = null;\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n        if (this._panelOpen) {\n            this._panelOpen = false;\n            this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n            this._changeDetectorRef.markForCheck();\n            this._onTouched();\n            // Required for the MDC form field to pick up when the overlay has been closed.\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n        this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n        return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n        if (this.empty) {\n            return '';\n        }\n        if (this._multiple) {\n            const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n            if (this._isRtl()) {\n                selectedOptions.reverse();\n            }\n            // TODO(crisbeto): delimiter should be configurable for proper localization.\n            return selectedOptions.join(', ');\n        }\n        return this._selectionModel.selected[0].viewValue;\n    }\n    /** Refreshes the error state of the select. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n        return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n        if (!this.disabled) {\n            this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n        }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW ||\n            keyCode === UP_ARROW ||\n            keyCode === LEFT_ARROW ||\n            keyCode === RIGHT_ARROW;\n        const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n        const manager = this._keyManager;\n        // Open the select on ALT + arrow key to match the native <select>\n        if ((!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n            ((this.multiple || event.altKey) && isArrowKey)) {\n            event.preventDefault(); // prevents the page from scrolling down when pressing space\n            this.open();\n        }\n        else if (!this.multiple) {\n            const previouslySelectedOption = this.selected;\n            manager.onKeydown(event);\n            const selectedOption = this.selected;\n            // Since the value has changed, we need to announce it ourselves.\n            if (selectedOption && previouslySelectedOption !== selectedOption) {\n                // We set a duration on the live announcement, because we want the live element to be\n                // cleared after a while so that users can't navigate to it using the arrow keys.\n                this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n            }\n        }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n        const manager = this._keyManager;\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n        const isTyping = manager.isTyping();\n        if (isArrowKey && event.altKey) {\n            // Close the select on ALT + arrow key to match the native <select>\n            event.preventDefault();\n            this.close();\n            // Don't do anything in this case if the user is typing,\n            // because the typing sequence can include the space key.\n        }\n        else if (!isTyping &&\n            (keyCode === ENTER || keyCode === SPACE) &&\n            manager.activeItem &&\n            !hasModifierKey(event)) {\n            event.preventDefault();\n            manager.activeItem._selectViaInteraction();\n        }\n        else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n            event.preventDefault();\n            const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n            this.options.forEach(option => {\n                if (!option.disabled) {\n                    hasDeselectedOptions ? option.select() : option.deselect();\n                }\n            });\n        }\n        else {\n            const previouslyFocusedIndex = manager.activeItemIndex;\n            manager.onKeydown(event);\n            if (this._multiple &&\n                isArrowKey &&\n                event.shiftKey &&\n                manager.activeItem &&\n                manager.activeItemIndex !== previouslyFocusedIndex) {\n                manager.activeItem._selectViaInteraction();\n            }\n        }\n    }\n    _onFocus() {\n        if (!this.disabled) {\n            this._focused = true;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n        this._focused = false;\n        this._keyManager?.cancelTypeahead();\n        if (!this.disabled && !this.panelOpen) {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Callback that is invoked when the overlay panel has been attached.\n     */\n    _onAttached() {\n        this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n            this._changeDetectorRef.detectChanges();\n            this._positioningSettled();\n        });\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n        return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n        return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl) {\n                this._value = this.ngControl.value;\n            }\n            this._setSelectionByValue(this._value);\n            this.stateChanges.next();\n        });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n        this.options.forEach(option => option.setInactiveStyles());\n        this._selectionModel.clear();\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getMatSelectNonArrayValueError();\n            }\n            value.forEach((currentValue) => this._selectOptionByValue(currentValue));\n            this._sortValues();\n        }\n        else {\n            const correspondingOption = this._selectOptionByValue(value);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what option the user interacted with last.\n            if (correspondingOption) {\n                this._keyManager.updateActiveItem(correspondingOption);\n            }\n            else if (!this.panelOpen) {\n                // Otherwise reset the highlighted option. Note that we only want to do this while\n                // closed, because doing it while open can shift the user's focus unnecessarily.\n                this._keyManager.updateActiveItem(-1);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n        const correspondingOption = this.options.find((option) => {\n            // Skip options that are already in the model. This allows us to handle cases\n            // where the same primitive value is selected multiple times.\n            if (this._selectionModel.isSelected(option)) {\n                return false;\n            }\n            try {\n                // Treat null as a special reset value.\n                return option.value != null && this._compareWith(option.value, value);\n            }\n            catch (error) {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    // Notify developers of errors in their comparator.\n                    console.warn(error);\n                }\n                return false;\n            }\n        });\n        if (correspondingOption) {\n            this._selectionModel.select(correspondingOption);\n        }\n        return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n        // Always re-assign an array, because it might have been mutated.\n        if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n            if (this.options) {\n                this._setSelectionByValue(newValue);\n            }\n            this._value = newValue;\n            return true;\n        }\n        return false;\n    }\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth(preferredOrigin) {\n        if (this.panelWidth === 'auto') {\n            const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin\n                ? preferredOrigin.elementRef\n                : preferredOrigin || this._elementRef;\n            return refToMeasure.nativeElement.getBoundingClientRect().width;\n        }\n        return this.panelWidth === null ? '' : this.panelWidth;\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withTypeAhead(this.typeaheadDebounceInterval)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n            .withHomeAndEnd()\n            .withPageUpDown()\n            .withAllowedModifierKeys(['shiftKey'])\n            .skipPredicate(this._skipPredicate);\n        this._keyManager.tabOut.subscribe(() => {\n            if (this.panelOpen) {\n                // Select the active item when tabbing away. This is consistent with how the native\n                // select behaves. Note that we only want to do this in single selection mode.\n                if (!this.multiple && this._keyManager.activeItem) {\n                    this._keyManager.activeItem._selectViaInteraction();\n                }\n                // Restore focus to the trigger before closing. Ensures that the focus\n                // position won't be lost if the user got focus into the overlay.\n                this.focus();\n                this.close();\n            }\n        });\n        this._keyManager.change.subscribe(() => {\n            if (this._panelOpen && this.panel) {\n                this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n            }\n            else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n                this._keyManager.activeItem._selectViaInteraction();\n            }\n        });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n        const changedOrDestroyed = merge(this.options.changes, this._destroy);\n        this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n            this._onSelect(event.source, event.isUserInput);\n            if (event.isUserInput && !this.multiple && this._panelOpen) {\n                this.close();\n                this.focus();\n            }\n        });\n        // Listen to changes in the internal state of the options and react accordingly.\n        // Handles cases like the labels of the selected options changing.\n        merge(...this.options.map(option => option._stateChanges))\n            .pipe(takeUntil(changedOrDestroyed))\n            .subscribe(() => {\n            // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n            // be the result of an expression changing. We have to use `detectChanges` in order\n            // to avoid \"changed after checked\" errors (see #14793).\n            this._changeDetectorRef.detectChanges();\n            this.stateChanges.next();\n        });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n        const wasSelected = this._selectionModel.isSelected(option);\n        if (option.value == null && !this._multiple) {\n            option.deselect();\n            this._selectionModel.clear();\n            if (this.value != null) {\n                this._propagateChanges(option.value);\n            }\n        }\n        else {\n            if (wasSelected !== option.selected) {\n                option.selected\n                    ? this._selectionModel.select(option)\n                    : this._selectionModel.deselect(option);\n            }\n            if (isUserInput) {\n                this._keyManager.setActiveItem(option);\n            }\n            if (this.multiple) {\n                this._sortValues();\n                if (isUserInput) {\n                    // In case the user selected the option with their mouse, we\n                    // want to restore focus back to the trigger, in order to\n                    // prevent the select keyboard controls from clashing with\n                    // the ones from `mat-option`.\n                    this.focus();\n                }\n            }\n        }\n        if (wasSelected !== this._selectionModel.isSelected(option)) {\n            this._propagateChanges();\n        }\n        this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n        if (this.multiple) {\n            const options = this.options.toArray();\n            this._selectionModel.sort((a, b) => {\n                return this.sortComparator\n                    ? this.sortComparator(a, b, options)\n                    : options.indexOf(a) - options.indexOf(b);\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit;\n        if (this.multiple) {\n            valueToEmit = this.selected.map(option => option.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first *enabled* option.\n     */\n    _highlightCorrectOption() {\n        if (this._keyManager) {\n            if (this.empty) {\n                // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n                // because it activates the first option that passes the skip predicate, rather than the\n                // first *enabled* option.\n                let firstEnabledOptionIndex = -1;\n                for (let index = 0; index < this.options.length; index++) {\n                    const option = this.options.get(index);\n                    if (!option.disabled) {\n                        firstEnabledOptionIndex = index;\n                        break;\n                    }\n                }\n                this._keyManager.setActiveItem(firstEnabledOptionIndex);\n            }\n            else {\n                this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n            }\n        }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n        return !this._panelOpen && !this.disabled && this.options?.length > 0;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n        if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n            return this._keyManager.activeItem.id;\n        }\n        return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        let value = (labelId ? labelId + ' ' : '') + this._valueId;\n        if (this.ariaLabelledby) {\n            value += ' ' + this.ariaLabelledby;\n        }\n        return value;\n    }\n    /** Called when the overlay panel is done animating. */\n    _panelDoneAnimating(isOpen) {\n        this.openedChange.emit(isOpen);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        this.focus();\n        this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        // Since the panel doesn't overlap the trigger, we\n        // want the label to only float when there's a value.\n        return this.panelOpen || !this.empty || (this.focused && !!this.placeholder);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelect, deps: [{ token: i1.ViewportRuler }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i2.ErrorStateMatcher }, { token: i0.ElementRef }, { token: i3.Directionality, optional: true }, { token: i4.NgForm, optional: true }, { token: i4.FormGroupDirective, optional: true }, { token: MAT_FORM_FIELD, optional: true }, { token: i4.NgControl, optional: true, self: true }, { token: 'tabindex', attribute: true }, { token: MAT_SELECT_SCROLL_STRATEGY }, { token: i5.LiveAnnouncer }, { token: MAT_SELECT_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatSelect, isStandalone: true, selector: \"mat-select\", inputs: { userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], panelClass: \"panelClass\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], placeholder: \"placeholder\", required: [\"required\", \"required\", booleanAttribute], multiple: [\"multiple\", \"multiple\", booleanAttribute], disableOptionCentering: [\"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute], compareWith: \"compareWith\", value: \"value\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], errorStateMatcher: \"errorStateMatcher\", typeaheadDebounceInterval: [\"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute], sortComparator: \"sortComparator\", id: \"id\", panelWidth: \"panelWidth\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", _closedStream: \"closed\", selectionChange: \"selectionChange\", valueChange: \"valueChange\" }, host: { attributes: { \"role\": \"combobox\", \"aria-autocomplete\": \"none\", \"aria-haspopup\": \"listbox\" }, listeners: { \"keydown\": \"_handleKeydown($event)\", \"focus\": \"_onFocus()\", \"blur\": \"_onBlur()\" }, properties: { \"attr.id\": \"id\", \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.aria-controls\": \"panelOpen ? id + \\\"-panel\\\" : null\", \"attr.aria-expanded\": \"panelOpen\", \"attr.aria-label\": \"ariaLabel || null\", \"attr.aria-required\": \"required.toString()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-activedescendant\": \"_getAriaActiveDescendant()\", \"class.mat-mdc-select-disabled\": \"disabled\", \"class.mat-mdc-select-invalid\": \"errorState\", \"class.mat-mdc-select-required\": \"required\", \"class.mat-mdc-select-empty\": \"empty\", \"class.mat-mdc-select-multiple\": \"multiple\" }, classAttribute: \"mat-mdc-select\" }, providers: [\n            { provide: MatFormFieldControl, useExisting: MatSelect },\n            { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n        ], queries: [{ propertyName: \"customTrigger\", first: true, predicate: MAT_SELECT_TRIGGER, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], viewQueries: [{ propertyName: \"trigger\", first: true, predicate: [\"trigger\"], descendants: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }, { propertyName: \"_overlayDir\", first: true, predicate: CdkConnectedOverlay, descendants: true }], exportAs: [\"matSelect\"], usesOnChanges: true, ngImport: i0, template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"], dependencies: [{ kind: \"directive\", type: CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }, { kind: \"directive\", type: CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\", \"cdkConnectedOverlayDisposeOnNavigation\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [matSelectAnimations.transformPanel], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelect, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-select', exportAs: 'matSelect', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'role': 'combobox',\n                        'aria-autocomplete': 'none',\n                        'aria-haspopup': 'listbox',\n                        'class': 'mat-mdc-select',\n                        '[attr.id]': 'id',\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n                        '[attr.aria-expanded]': 'panelOpen',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.aria-required]': 'required.toString()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n                        '[class.mat-mdc-select-disabled]': 'disabled',\n                        '[class.mat-mdc-select-invalid]': 'errorState',\n                        '[class.mat-mdc-select-required]': 'required',\n                        '[class.mat-mdc-select-empty]': 'empty',\n                        '[class.mat-mdc-select-multiple]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': '_onFocus()',\n                        '(blur)': '_onBlur()',\n                    }, animations: [matSelectAnimations.transformPanel], providers: [\n                        { provide: MatFormFieldControl, useExisting: MatSelect },\n                        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n                    ], standalone: true, imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass], template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"] }]\n        }], ctorParameters: () => [{ type: i1.ViewportRuler }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i2.ErrorStateMatcher }, { type: i0.ElementRef }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i4.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i6.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }] }, { type: i4.NgControl, decorators: [{\n                    type: Self\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SELECT_SCROLL_STRATEGY]\n                }] }, { type: i5.LiveAnnouncer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SELECT_CONFIG]\n                }] }], propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], customTrigger: [{\n                type: ContentChild,\n                args: [MAT_SELECT_TRIGGER]\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], trigger: [{\n                type: ViewChild,\n                args: ['trigger']\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], _overlayDir: [{\n                type: ViewChild,\n                args: [CdkConnectedOverlay]\n            }], panelClass: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableOptionCentering: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], typeaheadDebounceInterval: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], sortComparator: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], panelWidth: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], selectionChange: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSelectTrigger, isStandalone: true, selector: \"mat-select-trigger\", providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-select-trigger',\n                    providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }],\n                    standalone: true,\n                }]\n        }] });\n\nclass MatSelectModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, imports: [CommonModule,\n            OverlayModule,\n            MatOptionModule,\n            MatCommonModule,\n            MatSelect,\n            MatSelectTrigger], exports: [CdkScrollableModule,\n            MatFormFieldModule,\n            MatSelect,\n            MatSelectTrigger,\n            MatOptionModule,\n            MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER], imports: [CommonModule,\n            OverlayModule,\n            MatOptionModule,\n            MatCommonModule, CdkScrollableModule,\n            MatFormFieldModule,\n            MatOptionModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        OverlayModule,\n                        MatOptionModule,\n                        MatCommonModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatFormFieldModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, matSelectAnimations };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,sBAAsB;AACpG,SAASC,OAAO,EAAEC,YAAY,QAAQ,iBAAiB;AACvD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC/Q,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,6BAA6B,EAAEC,wBAAwB,EAAEC,kBAAkB,EAAEC,2BAA2B,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC5M,SAASC,WAAW,EAAEJ,SAAS,QAAQ,wBAAwB;AAC/D,OAAO,KAAKK,EAAE,MAAM,8BAA8B;AAClD,SAASC,cAAc,EAAEC,mBAAmB,EAAEC,kBAAkB,QAAQ,8BAA8B;AACtG,SAASC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,QAAQ,8BAA8B;AAC9G,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,sBAAsB,EAAEC,mBAAmB,EAAEC,0BAA0B,QAAQ,mBAAmB;AAC3G,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAEC,cAAc,EAAEC,CAAC,QAAQ,uBAAuB;AACtH,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AACzG,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;AAErG;AACA;AACA;AACA;AACA;AACA;AACA;AANA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAugCoGhF,EAAE,CAAAkF,cAAA,aAI0zB,CAAC;IAJ7zBlF,EAAE,CAAAmF,MAAA,EAIy0B,CAAC;IAJ50BnF,EAAE,CAAAoF,YAAA,CAIg1B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJn1BrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAAuF,SAAA,CAIy0B,CAAC;IAJ50BvF,EAAE,CAAAwF,iBAAA,CAAAH,MAAA,CAAAI,WAIy0B,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ50BhF,EAAE,CAAA2F,YAAA,EAIm/B,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJt/BhF,EAAE,CAAAkF,cAAA,cAI0jC,CAAC;IAJ7jClF,EAAE,CAAAmF,MAAA,EAI0kC,CAAC;IAJ7kCnF,EAAE,CAAAoF,YAAA,CAIilC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJplCrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAAuF,SAAA,CAI0kC,CAAC;IAJ7kCvF,EAAE,CAAAwF,iBAAA,CAAAH,MAAA,CAAAQ,YAI0kC,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ7kChF,EAAE,CAAAkF,cAAA,cAIi5B,CAAC;IAJp5BlF,EAAE,CAAA+F,UAAA,IAAAL,8CAAA,MAIg7B,CAAC,IAAAE,8CAAA,MAAqF,CAAC;IAJzgC5F,EAAE,CAAAoF,YAAA,CAI2mC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJ9mCrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAAuF,SAAA,CAI4lC,CAAC;IAJ/lCvF,EAAE,CAAAgG,aAAA,IAAAX,MAAA,CAAAY,aAAA,QAI4lC,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmB,GAAA,GAJ/lCnG,EAAE,CAAAoG,gBAAA;IAAFpG,EAAE,CAAAkF,cAAA,gBAIknF,CAAC;IAJrnFlF,EAAE,CAAAqG,UAAA,kCAAAC,+EAAAC,MAAA;MAAFvG,EAAE,CAAAwG,aAAA,CAAAL,GAAA;MAAA,MAAAd,MAAA,GAAFrF,EAAE,CAAAsF,aAAA;MAAA,OAAFtF,EAAE,CAAAyG,WAAA,CAIwhFpB,MAAA,CAAAqB,yBAAA,CAAAC,IAAA,CAAAJ,MAAA,CAAAK,OAA6C,CAAC;IAAA,CAAC,CAAC,qBAAAC,yDAAAN,MAAA;MAJ1kFvG,EAAE,CAAAwG,aAAA,CAAAL,GAAA;MAAA,MAAAd,MAAA,GAAFrF,EAAE,CAAAsF,aAAA;MAAA,OAAFtF,EAAE,CAAAyG,WAAA,CAI0lFpB,MAAA,CAAAyB,cAAA,CAAAP,MAAqB,CAAC;IAAA,CAAC,CAAC;IAJpnFvG,EAAE,CAAA2F,YAAA,KAIipF,CAAC;IAJppF3F,EAAE,CAAAoF,YAAA,CAI2pF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAJ9pFrF,EAAE,CAAAsF,aAAA;IAAFtF,EAAE,CAAA+G,sBAAA,kEAAA1B,MAAA,CAAA2B,cAAA,MAI+vE,CAAC;IAJlwEhH,EAAE,CAAAiH,UAAA,YAAA5B,MAAA,CAAA6B,UAIm9E,CAAC,6BAAoC,CAAC;IAJ3/ElH,EAAE,CAAAmH,WAAA,OAAA9B,MAAA,CAAA+B,EAAA,qCAAA/B,MAAA,CAAAgC,QAAA,gBAAAhC,MAAA,CAAAiC,SAAA,6BAAAjC,MAAA,CAAAkC,uBAAA;EAAA;AAAA;AAhgCtG,MAAMC,mBAAmB,GAAG;EACxB;AACJ;AACA;AACA;AACA;EACIC,kBAAkB,EAAErD,OAAO,CAAC,oBAAoB,EAAE,CAC9CC,UAAU,CAAC,WAAW,EAAEC,KAAK,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE;IAAEmD,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC,CAC1F,CAAC;EACF;EACAC,cAAc,EAAEvD,OAAO,CAAC,gBAAgB,EAAE,CACtCI,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChBmD,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,EACHxD,UAAU,CAAC,iBAAiB,EAAEK,OAAO,CAAC,kCAAkC,EAAED,KAAK,CAAC;IAC5EmD,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EACJxD,UAAU,CAAC,WAAW,EAAEK,OAAO,CAAC,cAAc,EAAED,KAAK,CAAC;IAAEmD,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAC1E;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gCAAgCA,CAAA,EAAG;EACxC,OAAOC,KAAK,CAAC,+DAA+D,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAOD,KAAK,CAAC,oDAAoD,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iCAAiCA,CAAA,EAAG;EACzC,OAAOF,KAAK,CAAC,mCAAmC,CAAC;AACrD;AAEA,IAAIG,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,0BAA0B,GAAG,IAAIlI,cAAc,CAAC,4BAA4B,EAAE;EAChFmI,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAGpI,MAAM,CAACR,OAAO,CAAC;IAC/B,OAAO,MAAM4I,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA,SAASC,2CAA2CA,CAACH,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAME,iBAAiB,GAAG,IAAIzI,cAAc,CAAC,mBAAmB,CAAC;AACjE;AACA,MAAM0I,mCAAmC,GAAG;EACxCC,OAAO,EAAET,0BAA0B;EACnCU,IAAI,EAAE,CAACnJ,OAAO,CAAC;EACfoJ,UAAU,EAAEL;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMM,kBAAkB,GAAG,IAAI9I,cAAc,CAAC,kBAAkB,CAAC;AACjE;AACA,MAAM+I,eAAe,CAAC;EAClBC,WAAWA,CACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA,MAAMC,SAAS,CAAC;EACZ;EACAC,qBAAqBA,CAACC,KAAK,EAAE;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACH,KAAK,CAAC;IAC5C,IAAIC,MAAM,EAAE;MACR,MAAMG,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,aAAa;MACtC,MAAMC,UAAU,GAAGvI,6BAA6B,CAACiI,KAAK,EAAE,IAAI,CAACE,OAAO,EAAE,IAAI,CAACK,YAAY,CAAC;MACxF,MAAMC,OAAO,GAAGP,MAAM,CAACQ,eAAe,CAAC,CAAC;MACxC,IAAIT,KAAK,KAAK,CAAC,IAAIM,UAAU,KAAK,CAAC,EAAE;QACjC;QACA;QACA;QACAF,KAAK,CAACM,SAAS,GAAG,CAAC;MACvB,CAAC,MACI;QACDN,KAAK,CAACM,SAAS,GAAG1I,wBAAwB,CAACwI,OAAO,CAACG,SAAS,EAAEH,OAAO,CAACI,YAAY,EAAER,KAAK,CAACM,SAAS,EAAEN,KAAK,CAACQ,YAAY,CAAC;MAC5H;IACJ;EACJ;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACd,qBAAqB,CAAC,IAAI,CAACe,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;EACrE;EACA;EACAC,eAAeA,CAACnB,KAAK,EAAE;IACnB,OAAO,IAAIH,eAAe,CAAC,IAAI,EAAEG,KAAK,CAAC;EAC3C;EACA;EACA,IAAIoB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,UAAU;EAC3C;EACA;EACA,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,6BAA6B;EAC7C;EACA,IAAID,4BAA4BA,CAACvB,KAAK,EAAE;IACpC,IAAI,CAACwB,6BAA6B,GAAGxB,KAAK;IAC1C,IAAI,CAACyB,qBAAqB,CAAC,CAAC;EAChC;EACA;EACA,IAAInF,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACoF,YAAY;EAC5B;EACA,IAAIpF,WAAWA,CAAC0D,KAAK,EAAE;IACnB,IAAI,CAAC0B,YAAY,GAAG1B,KAAK;IACzB,IAAI,CAAC2B,YAAY,CAACnE,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIoE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACC,SAAS,EAAEC,OAAO,EAAEC,YAAY,CAAC1H,UAAU,CAACsH,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQA,CAAC5B,KAAK,EAAE;IAChB,IAAI,CAAC6B,SAAS,GAAG7B,KAAK;IACtB,IAAI,CAAC2B,YAAY,CAACnE,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIU,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC+D,SAAS;EACzB;EACA,IAAI/D,QAAQA,CAAC8B,KAAK,EAAE;IAChB,IAAI,IAAI,CAACkC,eAAe,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAMxD,gCAAgC,CAAC,CAAC;IAC5C;IACA,IAAI,CAACsD,SAAS,GAAGjC,KAAK;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIoC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACE,EAAE,EAAE;IAChB,IAAI,OAAOA,EAAE,KAAK,UAAU,KAAK,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMrD,iCAAiC,CAAC,CAAC;IAC7C;IACA,IAAI,CAACuD,YAAY,GAAGC,EAAE;IACtB,IAAI,IAAI,CAACJ,eAAe,EAAE;MACtB;MACA,IAAI,CAACK,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACA,IAAIvC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACwC,MAAM;EACtB;EACA,IAAIxC,KAAKA,CAACyC,QAAQ,EAAE;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;IAC/C,IAAIC,WAAW,EAAE;MACb,IAAI,CAACE,SAAS,CAACH,QAAQ,CAAC;IAC5B;EACJ;EACA;EACA,IAAII,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB,CAACC,OAAO;EAC1C;EACA,IAAIF,iBAAiBA,CAAC7C,KAAK,EAAE;IACzB,IAAI,CAAC8C,kBAAkB,CAACC,OAAO,GAAG/C,KAAK;EAC3C;EACA;EACA,IAAI/B,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC+E,GAAG;EACnB;EACA,IAAI/E,EAAEA,CAAC+B,KAAK,EAAE;IACV,IAAI,CAACgD,GAAG,GAAGhD,KAAK,IAAI,IAAI,CAACiD,IAAI;IAC7B,IAAI,CAACtB,YAAY,CAACnE,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAI0F,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,kBAAkB,CAACI,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAAClD,KAAK,EAAE;IAClB,IAAI,CAAC8C,kBAAkB,CAACI,UAAU,GAAGlD,KAAK;EAC9C;EACAF,WAAWA,CAACqD,cAAc,EAAEC,kBAAkB;EAC9C;AACJ;AACA;AACA;EACIC,aAAa,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,IAAI,EAAEC,UAAU,EAAEC,eAAe,EAAEC,gBAAgB,EAAE7B,SAAS,EAAE8B,QAAQ,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,eAAe,EAAE;IACpL,IAAI,CAACZ,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACG,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC7B,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACgC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CACd;MACIC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClBrG,UAAU,EAAE;IAChB,CAAC,EACD;MACIkG,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,QAAQ;MAClBrG,UAAU,EAAE;IAChB,CAAC,CACJ;IACD;IACA,IAAI,CAACuD,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACe,YAAY,GAAG,CAACgC,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;IACzC;IACA,IAAI,CAACrB,IAAI,GAAG,cAAclE,YAAY,EAAE,EAAE;IAC1C;IACA,IAAI,CAACwF,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIjK,OAAO,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACoH,YAAY,GAAG,IAAIpH,OAAO,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACkK,wBAAwB,GAAG,IAAI;IACpC;IACA,IAAI,CAAC7B,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B;IACA,IAAI,CAAC8B,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B;IACA,IAAI,CAACC,QAAQ,GAAG,oBAAoB5F,YAAY,EAAE,EAAE;IACpD;IACA,IAAI,CAACxB,yBAAyB,GAAG,IAAIhD,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACqK,kBAAkB,GAAG,IAAI,CAACb,eAAe,EAAEc,iBAAiB,IAAI,EAAE;IACvE,IAAI,CAACxD,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACyD,WAAW,GAAG,YAAY;IAC/B;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACpB,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACpC,6BAA6B,GAAG,IAAI,CAACuC,eAAe,EAAExC,4BAA4B,IAAI,KAAK;IAChG,IAAI,CAACU,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACgD,sBAAsB,GAAG,IAAI,CAAClB,eAAe,EAAEkB,sBAAsB,IAAI,KAAK;IACnF;IACA,IAAI,CAAC9G,SAAS,GAAG,EAAE;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAAC+G,UAAU,GAAG,IAAI,CAACnB,eAAe,IAAI,OAAO,IAAI,CAACA,eAAe,CAACmB,UAAU,KAAK,WAAW,GAC1F,IAAI,CAACnB,eAAe,CAACmB,UAAU,GAC/B,MAAM;IACZ,IAAI,CAACC,YAAY,GAAG,IAAI5K,OAAO,CAAC,CAAC;IACjC;IACA,IAAI,CAAC6K,sBAAsB,GAAG5K,KAAK,CAAC,MAAM;MACtC,MAAM6F,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACgF,OAAO,CAACC,IAAI,CAAC5K,SAAS,CAAC2F,OAAO,CAAC,EAAE1F,SAAS,CAAC,MAAMF,KAAK,CAAC,GAAG4F,OAAO,CAACxF,GAAG,CAACuF,MAAM,IAAIA,MAAM,CAACmF,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA,OAAO,IAAI,CAACJ,YAAY,CAACG,IAAI,CAAC3K,SAAS,CAAC,MAAM,IAAI,CAACyK,sBAAsB,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF;IACA,IAAI,CAACI,YAAY,GAAG,IAAIxO,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACyO,aAAa,GAAG,IAAI,CAACD,YAAY,CAACF,IAAI,CAAC1K,MAAM,CAAC8K,CAAC,IAAIA,CAAC,CAAC,EAAE7K,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC3E;IACA,IAAI,CAAC8K,aAAa,GAAG,IAAI,CAACH,YAAY,CAACF,IAAI,CAAC1K,MAAM,CAAC8K,CAAC,IAAI,CAACA,CAAC,CAAC,EAAE7K,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC5E;IACA,IAAI,CAAC+K,eAAe,GAAG,IAAI5O,YAAY,CAAC,CAAC;IACzC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6O,WAAW,GAAG,IAAI7O,YAAY,CAAC,CAAC;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC8O,aAAa,GAAG,IAAI;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAI3F,MAAM,IAAK;MAC9B,IAAI,IAAI,CAAC4F,SAAS,EAAE;QAChB;QACA,OAAO,KAAK;MAChB;MACA;MACA;MACA;MACA,OAAO5F,MAAM,CAAC2E,QAAQ;IAC1B,CAAC;IACD,IAAI,IAAI,CAACjD,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,CAACmE,aAAa,GAAG,IAAI;IACvC;IACA;IACA;IACA,IAAIlC,eAAe,EAAEmC,yBAAyB,IAAI,IAAI,EAAE;MACpD,IAAI,CAACA,yBAAyB,GAAGnC,eAAe,CAACmC,yBAAyB;IAC9E;IACA,IAAI,CAACpD,kBAAkB,GAAG,IAAI1K,kBAAkB,CAACkL,wBAAwB,EAAExB,SAAS,EAAE4B,eAAe,EAAED,UAAU,EAAE,IAAI,CAAC9B,YAAY,CAAC;IACrI,IAAI,CAACwE,sBAAsB,GAAGtC,qBAAqB;IACnD,IAAI,CAACuC,eAAe,GAAG,IAAI,CAACD,sBAAsB,CAAC,CAAC;IACpD,IAAI,CAACvC,QAAQ,GAAGyC,QAAQ,CAACzC,QAAQ,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAAC3F,EAAE,GAAG,IAAI,CAACA,EAAE;EACrB;EACAqI,QAAQA,CAAA,EAAG;IACP,IAAI,CAACpE,eAAe,GAAG,IAAItI,cAAc,CAAC,IAAI,CAACsE,QAAQ,CAAC;IACxD,IAAI,CAACyD,YAAY,CAACnE,IAAI,CAAC,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAACD,yBAAyB,CACzB+H,IAAI,CAACxK,oBAAoB,CAAC,CAAC,EAAEC,SAAS,CAAC,IAAI,CAACyJ,QAAQ,CAAC,CAAC,CACtD+B,SAAS,CAAC,MAAM,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACR,SAAS,CAAC,CAAC;IAC9D,IAAI,CAAC7C,cAAc,CACdsD,MAAM,CAAC,CAAC,CACRnB,IAAI,CAACvK,SAAS,CAAC,IAAI,CAACyJ,QAAQ,CAAC,CAAC,CAC9B+B,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACP,SAAS,EAAE;QAChB,IAAI,CAACU,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;QACxE,IAAI,CAACxD,kBAAkB,CAACyD,aAAa,CAAC,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC3B,YAAY,CAAC3H,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC2H,YAAY,CAAC4B,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC9E,eAAe,CAAC+E,OAAO,CAAC3B,IAAI,CAACvK,SAAS,CAAC,IAAI,CAACyJ,QAAQ,CAAC,CAAC,CAAC+B,SAAS,CAACW,KAAK,IAAI;MAC3EA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAChH,MAAM,IAAIA,MAAM,CAACiH,MAAM,CAAC,CAAC,CAAC;MAC9CH,KAAK,CAACI,OAAO,CAACF,OAAO,CAAChH,MAAM,IAAIA,MAAM,CAACmH,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAAClH,OAAO,CAACgF,OAAO,CAACC,IAAI,CAAC5K,SAAS,CAAC,IAAI,CAAC,EAAEK,SAAS,CAAC,IAAI,CAACyJ,QAAQ,CAAC,CAAC,CAAC+B,SAAS,CAAC,MAAM;MACjF,IAAI,CAACiB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACjF,oBAAoB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACN;EACAkF,SAASA,CAAA,EAAG;IACR,MAAMC,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC1D,MAAM7F,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA;IACA;IACA,IAAI4F,iBAAiB,KAAK,IAAI,CAACnD,sBAAsB,EAAE;MACnD,MAAM5D,OAAO,GAAG,IAAI,CAAC4C,WAAW,CAAC/C,aAAa;MAC9C,IAAI,CAAC+D,sBAAsB,GAAGmD,iBAAiB;MAC/C,IAAIA,iBAAiB,EAAE;QACnB/G,OAAO,CAACiH,YAAY,CAAC,iBAAiB,EAAEF,iBAAiB,CAAC;MAC9D,CAAC,MACI;QACD/G,OAAO,CAACkH,eAAe,CAAC,iBAAiB,CAAC;MAC9C;IACJ;IACA,IAAI/F,SAAS,EAAE;MACX;MACA,IAAI,IAAI,CAACgG,gBAAgB,KAAKhG,SAAS,CAACC,OAAO,EAAE;QAC7C,IAAI,IAAI,CAAC+F,gBAAgB,KAAKC,SAAS,IACnCjG,SAAS,CAACiD,QAAQ,KAAK,IAAI,IAC3BjD,SAAS,CAACiD,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;UACtC,IAAI,CAACA,QAAQ,GAAGjD,SAAS,CAACiD,QAAQ;QACtC;QACA,IAAI,CAAC+C,gBAAgB,GAAGhG,SAAS,CAACC,OAAO;MAC7C;MACA,IAAI,CAACiG,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAC,WAAWA,CAAC5C,OAAO,EAAE;IACjB;IACA;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACvD,IAAI,CAAC1D,YAAY,CAACnE,IAAI,CAAC,CAAC;IAC5B;IACA,IAAI6H,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAACpE,WAAW,EAAE;MAC1D,IAAI,CAACA,WAAW,CAACiH,aAAa,CAAC,IAAI,CAAChC,yBAAyB,CAAC;IAClE;EACJ;EACAiC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClH,WAAW,EAAEmH,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC5D,QAAQ,CAAChH,IAAI,CAAC,CAAC;IACpB,IAAI,CAACgH,QAAQ,CAACuC,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACpF,YAAY,CAACoF,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACsB,eAAe,CAAC,CAAC;EAC1B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACtC,SAAS,GAAG,IAAI,CAACuC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACC,IAAI,CAAC,CAAC;EAC/C;EACA;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MAClB;IACJ;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC9E,gBAAgB,EAAE;MACvB,IAAI,CAACiD,uBAAuB,GAAG,IAAI,CAACjD,gBAAgB,CAAC+E,yBAAyB,CAAC,CAAC;IACpF;IACA,IAAI,CAAChC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;IACxE,IAAI,CAAC+B,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACrH,UAAU,GAAG,IAAI;IACtB,IAAI,CAACL,WAAW,CAAC2H,yBAAyB,CAAC,IAAI,CAAC;IAChD,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACzF,kBAAkB,CAAC0F,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACnH,YAAY,CAACnE,IAAI,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImL,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMI,KAAK,GAAG,IAAI,CAACxF,WAAW,CAAC/C,aAAa,CAACwI,OAAO,CAAC,mDAAmD,CAAC;IACzG,IAAI,CAACD,KAAK,EAAE;MACR;MACA;IACJ;IACA,MAAME,OAAO,GAAG,GAAG,IAAI,CAAChL,EAAE,QAAQ;IAClC,IAAI,IAAI,CAAC6H,aAAa,EAAE;MACpBtM,sBAAsB,CAAC,IAAI,CAACsM,aAAa,EAAE,WAAW,EAAEmD,OAAO,CAAC;IACpE;IACAxP,mBAAmB,CAACsP,KAAK,EAAE,WAAW,EAAEE,OAAO,CAAC;IAChD,IAAI,CAACnD,aAAa,GAAGiD,KAAK;EAC9B;EACA;EACAV,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACvC,aAAa,EAAE;MACrB;MACA;IACJ;IACA,MAAMmD,OAAO,GAAG,GAAG,IAAI,CAAChL,EAAE,QAAQ;IAClCzE,sBAAsB,CAAC,IAAI,CAACsM,aAAa,EAAE,WAAW,EAAEmD,OAAO,CAAC;IAChE,IAAI,CAACnD,aAAa,GAAG,IAAI;EAC7B;EACA;EACAyC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACjH,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACL,WAAW,CAAC2H,yBAAyB,CAAC,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;MACzE,IAAI,CAAC9F,kBAAkB,CAAC0F,YAAY,CAAC,CAAC;MACtC,IAAI,CAACpE,UAAU,CAAC,CAAC;MACjB;MACA,IAAI,CAAC/C,YAAY,CAACnE,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2L,UAAUA,CAACnJ,KAAK,EAAE;IACd,IAAI,CAAC2C,YAAY,CAAC3C,KAAK,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIoJ,gBAAgBA,CAAC9G,EAAE,EAAE;IACjB,IAAI,CAACM,SAAS,GAAGN,EAAE;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+G,iBAAiBA,CAAC/G,EAAE,EAAE;IAClB,IAAI,CAACoC,UAAU,GAAGpC,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgH,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACxE,QAAQ,GAAGwE,UAAU;IAC1B,IAAI,CAACnG,kBAAkB,CAAC0F,YAAY,CAAC,CAAC;IACtC,IAAI,CAACnH,YAAY,CAACnE,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIwI,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1E,UAAU;EAC1B;EACA;EACA,IAAIkI,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtL,QAAQ,GAAG,IAAI,CAACgE,eAAe,EAAEsH,QAAQ,IAAI,EAAE,GAAG,IAAI,CAACtH,eAAe,EAAEsH,QAAQ,CAAC,CAAC,CAAC;EACnG;EACA;EACA,IAAI9M,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC+M,KAAK,EAAE;MACZ,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACxH,SAAS,EAAE;MAChB,MAAMyH,eAAe,GAAG,IAAI,CAACxH,eAAe,CAACsH,QAAQ,CAAC3O,GAAG,CAACuF,MAAM,IAAIA,MAAM,CAACuJ,SAAS,CAAC;MACrF,IAAI,IAAI,CAACT,MAAM,CAAC,CAAC,EAAE;QACfQ,eAAe,CAACE,OAAO,CAAC,CAAC;MAC7B;MACA;MACA,OAAOF,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAAC3H,eAAe,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAACG,SAAS;EACrD;EACA;EACA3B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAClF,kBAAkB,CAACkF,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACAkB,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC1F,IAAI,GAAG,IAAI,CAACA,IAAI,CAACxD,KAAK,KAAK,KAAK,GAAG,KAAK;EACxD;EACA;EACArC,cAAcA,CAACuJ,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;MAChB,IAAI,CAACiB,SAAS,GAAG,IAAI,CAAC8D,kBAAkB,CAAC5C,KAAK,CAAC,GAAG,IAAI,CAAC6C,oBAAoB,CAAC7C,KAAK,CAAC;IACtF;EACJ;EACA;EACA6C,oBAAoBA,CAAC7C,KAAK,EAAE;IACxB,MAAM8C,OAAO,GAAG9C,KAAK,CAAC8C,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKnQ,UAAU,IACrCmQ,OAAO,KAAKlQ,QAAQ,IACpBkQ,OAAO,KAAKjQ,UAAU,IACtBiQ,OAAO,KAAKhQ,WAAW;IAC3B,MAAMkQ,SAAS,GAAGF,OAAO,KAAK/P,KAAK,IAAI+P,OAAO,KAAK9P,KAAK;IACxD,MAAMiQ,OAAO,GAAG,IAAI,CAAClJ,WAAW;IAChC;IACA,IAAK,CAACkJ,OAAO,CAACC,QAAQ,CAAC,CAAC,IAAIF,SAAS,IAAI,CAAC/P,cAAc,CAAC+M,KAAK,CAAC,IAC1D,CAAC,IAAI,CAAChJ,QAAQ,IAAIgJ,KAAK,CAACmD,MAAM,KAAKJ,UAAW,EAAE;MACjD/C,KAAK,CAACoD,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,CAAC9B,IAAI,CAAC,CAAC;IACf,CAAC,MACI,IAAI,CAAC,IAAI,CAACtK,QAAQ,EAAE;MACrB,MAAMqM,wBAAwB,GAAG,IAAI,CAACf,QAAQ;MAC9CW,OAAO,CAACK,SAAS,CAACtD,KAAK,CAAC;MACxB,MAAMuD,cAAc,GAAG,IAAI,CAACjB,QAAQ;MACpC;MACA,IAAIiB,cAAc,IAAIF,wBAAwB,KAAKE,cAAc,EAAE;QAC/D;QACA;QACA,IAAI,CAAC3G,cAAc,CAAC4G,QAAQ,CAACD,cAAc,CAACd,SAAS,EAAE,KAAK,CAAC;MACjE;IACJ;EACJ;EACA;EACAG,kBAAkBA,CAAC5C,KAAK,EAAE;IACtB,MAAMiD,OAAO,GAAG,IAAI,CAAClJ,WAAW;IAChC,MAAM+I,OAAO,GAAG9C,KAAK,CAAC8C,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKnQ,UAAU,IAAImQ,OAAO,KAAKlQ,QAAQ;IACjE,MAAMsQ,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAAC,CAAC;IACnC,IAAIH,UAAU,IAAI/C,KAAK,CAACmD,MAAM,EAAE;MAC5B;MACAnD,KAAK,CAACoD,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC/B,KAAK,CAAC,CAAC;MACZ;MACA;IACJ,CAAC,MACI,IAAI,CAAC6B,QAAQ,KACbJ,OAAO,KAAK/P,KAAK,IAAI+P,OAAO,KAAK9P,KAAK,CAAC,IACxCiQ,OAAO,CAACQ,UAAU,IAClB,CAACxQ,cAAc,CAAC+M,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACoD,cAAc,CAAC,CAAC;MACtBH,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;IAC9C,CAAC,MACI,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACnI,SAAS,IAAI+H,OAAO,KAAK5P,CAAC,IAAI8M,KAAK,CAAC2D,OAAO,EAAE;MACpE3D,KAAK,CAACoD,cAAc,CAAC,CAAC;MACtB,MAAMQ,oBAAoB,GAAG,IAAI,CAACzK,OAAO,CAAC0K,IAAI,CAACC,GAAG,IAAI,CAACA,GAAG,CAACjG,QAAQ,IAAI,CAACiG,GAAG,CAACxB,QAAQ,CAAC;MACrF,IAAI,CAACnJ,OAAO,CAAC+G,OAAO,CAAChH,MAAM,IAAI;QAC3B,IAAI,CAACA,MAAM,CAAC2E,QAAQ,EAAE;UAClB+F,oBAAoB,GAAG1K,MAAM,CAACiH,MAAM,CAAC,CAAC,GAAGjH,MAAM,CAACmH,QAAQ,CAAC,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAM0D,sBAAsB,GAAGd,OAAO,CAACjJ,eAAe;MACtDiJ,OAAO,CAACK,SAAS,CAACtD,KAAK,CAAC;MACxB,IAAI,IAAI,CAACjF,SAAS,IACdgI,UAAU,IACV/C,KAAK,CAACgE,QAAQ,IACdf,OAAO,CAACQ,UAAU,IAClBR,OAAO,CAACjJ,eAAe,KAAK+J,sBAAsB,EAAE;QACpDd,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;MAC9C;IACJ;EACJ;EACAO,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACpG,QAAQ,EAAE;MAChB,IAAI,CAAC1D,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACM,YAAY,CAACnE,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI4N,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC/J,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACJ,WAAW,EAAEoK,eAAe,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAACtG,QAAQ,IAAI,CAAC,IAAI,CAACiB,SAAS,EAAE;MACnC,IAAI,CAACtB,UAAU,CAAC,CAAC;MACjB,IAAI,CAACtB,kBAAkB,CAAC0F,YAAY,CAAC,CAAC;MACtC,IAAI,CAACnH,YAAY,CAACnE,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;EACI8N,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,CAACC,cAAc,CAAClG,IAAI,CAACtK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACuL,SAAS,CAAC,MAAM;MAC1D,IAAI,CAACnD,kBAAkB,CAACyD,aAAa,CAAC,CAAC;MACvC,IAAI,CAAC7F,mBAAmB,CAAC,CAAC;IAC9B,CAAC,CAAC;EACN;EACA;EACAnD,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC8F,gBAAgB,GAAG,OAAO,IAAI,CAACA,gBAAgB,CAAC8H,KAAK,EAAE,GAAG,EAAE;EAC5E;EACA;EACA,IAAIhC,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACvH,eAAe,IAAI,IAAI,CAACA,eAAe,CAACwJ,OAAO,CAAC,CAAC;EAClE;EACAnJ,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACAoJ,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,IAAI,CAAC/J,SAAS,EAAE;QAChB,IAAI,CAACU,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC9B,KAAK;MACtC;MACA,IAAI,CAAC8L,oBAAoB,CAAC,IAAI,CAACtJ,MAAM,CAAC;MACtC,IAAI,CAACb,YAAY,CAACnE,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIsO,oBAAoBA,CAAC9L,KAAK,EAAE;IACxB,IAAI,CAACK,OAAO,CAAC+G,OAAO,CAAChH,MAAM,IAAIA,MAAM,CAAC2L,iBAAiB,CAAC,CAAC,CAAC;IAC1D,IAAI,CAAC7J,eAAe,CAAC8J,KAAK,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAC9N,QAAQ,IAAI8B,KAAK,EAAE;MACxB,IAAI,CAACiM,KAAK,CAACC,OAAO,CAAClM,KAAK,CAAC,KAAK,OAAOmC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMtD,8BAA8B,CAAC,CAAC;MAC1C;MACAmB,KAAK,CAACoH,OAAO,CAAE+E,YAAY,IAAK,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAAC;MACxE,IAAI,CAACE,WAAW,CAAC,CAAC;IACtB,CAAC,MACI;MACD,MAAMC,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAACpM,KAAK,CAAC;MAC5D;MACA;MACA,IAAIsM,mBAAmB,EAAE;QACrB,IAAI,CAACrL,WAAW,CAACsL,gBAAgB,CAACD,mBAAmB,CAAC;MAC1D,CAAC,MACI,IAAI,CAAC,IAAI,CAACtG,SAAS,EAAE;QACtB;QACA;QACA,IAAI,CAAC/E,WAAW,CAACsL,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAACnJ,kBAAkB,CAAC0F,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIsD,oBAAoBA,CAACpM,KAAK,EAAE;IACxB,MAAMsM,mBAAmB,GAAG,IAAI,CAACjM,OAAO,CAACmM,IAAI,CAAEpM,MAAM,IAAK;MACtD;MACA;MACA,IAAI,IAAI,CAAC8B,eAAe,CAACuK,UAAU,CAACrM,MAAM,CAAC,EAAE;QACzC,OAAO,KAAK;MAChB;MACA,IAAI;QACA;QACA,OAAOA,MAAM,CAACJ,KAAK,IAAI,IAAI,IAAI,IAAI,CAACqC,YAAY,CAACjC,MAAM,CAACJ,KAAK,EAAEA,KAAK,CAAC;MACzE,CAAC,CACD,OAAO0M,KAAK,EAAE;QACV,IAAI,OAAOvK,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C;UACAwK,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;QACvB;QACA,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,IAAIJ,mBAAmB,EAAE;MACrB,IAAI,CAACpK,eAAe,CAACmF,MAAM,CAACiF,mBAAmB,CAAC;IACpD;IACA,OAAOA,mBAAmB;EAC9B;EACA;EACA3J,YAAYA,CAACF,QAAQ,EAAE;IACnB;IACA,IAAIA,QAAQ,KAAK,IAAI,CAACD,MAAM,IAAK,IAAI,CAACP,SAAS,IAAIgK,KAAK,CAACC,OAAO,CAACzJ,QAAQ,CAAE,EAAE;MACzE,IAAI,IAAI,CAACpC,OAAO,EAAE;QACd,IAAI,CAACyL,oBAAoB,CAACrJ,QAAQ,CAAC;MACvC;MACA,IAAI,CAACD,MAAM,GAAGC,QAAQ;MACtB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA;EACAkE,gBAAgBA,CAACkG,eAAe,EAAE;IAC9B,IAAI,IAAI,CAAC3H,UAAU,KAAK,MAAM,EAAE;MAC5B,MAAM4H,YAAY,GAAGD,eAAe,YAAYrW,gBAAgB,GAC1DqW,eAAe,CAACE,UAAU,GAC1BF,eAAe,IAAI,IAAI,CAACtJ,WAAW;MACzC,OAAOuJ,YAAY,CAACtM,aAAa,CAACwM,qBAAqB,CAAC,CAAC,CAACC,KAAK;IACnE;IACA,OAAO,IAAI,CAAC/H,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAACA,UAAU;EAC1D;EACA;EACAzD,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACpB,OAAO,EAAE;MACd,KAAK,MAAMD,MAAM,IAAI,IAAI,CAACC,OAAO,EAAE;QAC/BD,MAAM,CAACgD,kBAAkB,CAAC0F,YAAY,CAAC,CAAC;MAC5C;IACJ;EACJ;EACA;EACA9B,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC/F,WAAW,GAAG,IAAIvH,0BAA0B,CAAC,IAAI,CAAC2G,OAAO,CAAC,CAC1D6H,aAAa,CAAC,IAAI,CAAChC,yBAAyB,CAAC,CAC7CgH,uBAAuB,CAAC,CAAC,CACzBtE,yBAAyB,CAAC,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CACxDiE,cAAc,CAAC,CAAC,CAChBC,cAAc,CAAC,CAAC,CAChBC,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC,CACrCC,aAAa,CAAC,IAAI,CAACvH,cAAc,CAAC;IACvC,IAAI,CAAC9E,WAAW,CAACsM,MAAM,CAAChH,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACP,SAAS,EAAE;QAChB;QACA;QACA,IAAI,CAAC,IAAI,CAAC9H,QAAQ,IAAI,IAAI,CAAC+C,WAAW,CAAC0J,UAAU,EAAE;UAC/C,IAAI,CAAC1J,WAAW,CAAC0J,UAAU,CAACC,qBAAqB,CAAC,CAAC;QACvD;QACA;QACA;QACA,IAAI,CAAC4C,KAAK,CAAC,CAAC;QACZ,IAAI,CAACjF,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,IAAI,CAACtH,WAAW,CAACwF,MAAM,CAACF,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACjF,UAAU,IAAI,IAAI,CAACf,KAAK,EAAE;QAC/B,IAAI,CAACL,qBAAqB,CAAC,IAAI,CAACe,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;MACrE,CAAC,MACI,IAAI,CAAC,IAAI,CAACI,UAAU,IAAI,CAAC,IAAI,CAACpD,QAAQ,IAAI,IAAI,CAAC+C,WAAW,CAAC0J,UAAU,EAAE;QACxE,IAAI,CAAC1J,WAAW,CAAC0J,UAAU,CAACC,qBAAqB,CAAC,CAAC;MACvD;IACJ,CAAC,CAAC;EACN;EACA;EACApD,aAAaA,CAAA,EAAG;IACZ,MAAMiG,kBAAkB,GAAGhT,KAAK,CAAC,IAAI,CAAC4F,OAAO,CAACgF,OAAO,EAAE,IAAI,CAACb,QAAQ,CAAC;IACrE,IAAI,CAACY,sBAAsB,CAACE,IAAI,CAACvK,SAAS,CAAC0S,kBAAkB,CAAC,CAAC,CAAClH,SAAS,CAACW,KAAK,IAAI;MAC/E,IAAI,CAACwG,SAAS,CAACxG,KAAK,CAACnH,MAAM,EAAEmH,KAAK,CAACyG,WAAW,CAAC;MAC/C,IAAIzG,KAAK,CAACyG,WAAW,IAAI,CAAC,IAAI,CAACzP,QAAQ,IAAI,IAAI,CAACoD,UAAU,EAAE;QACxD,IAAI,CAACiH,KAAK,CAAC,CAAC;QACZ,IAAI,CAACiF,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF;IACA;IACA/S,KAAK,CAAC,GAAG,IAAI,CAAC4F,OAAO,CAACxF,GAAG,CAACuF,MAAM,IAAIA,MAAM,CAACwN,aAAa,CAAC,CAAC,CACrDtI,IAAI,CAACvK,SAAS,CAAC0S,kBAAkB,CAAC,CAAC,CACnClH,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACnD,kBAAkB,CAACyD,aAAa,CAAC,CAAC;MACvC,IAAI,CAAClF,YAAY,CAACnE,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;EACAkQ,SAASA,CAACtN,MAAM,EAAEuN,WAAW,EAAE;IAC3B,MAAME,WAAW,GAAG,IAAI,CAAC3L,eAAe,CAACuK,UAAU,CAACrM,MAAM,CAAC;IAC3D,IAAIA,MAAM,CAACJ,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAACiC,SAAS,EAAE;MACzC7B,MAAM,CAACmH,QAAQ,CAAC,CAAC;MACjB,IAAI,CAACrF,eAAe,CAAC8J,KAAK,CAAC,CAAC;MAC5B,IAAI,IAAI,CAAChM,KAAK,IAAI,IAAI,EAAE;QACpB,IAAI,CAAC8N,iBAAiB,CAAC1N,MAAM,CAACJ,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAI6N,WAAW,KAAKzN,MAAM,CAACoJ,QAAQ,EAAE;QACjCpJ,MAAM,CAACoJ,QAAQ,GACT,IAAI,CAACtH,eAAe,CAACmF,MAAM,CAACjH,MAAM,CAAC,GACnC,IAAI,CAAC8B,eAAe,CAACqF,QAAQ,CAACnH,MAAM,CAAC;MAC/C;MACA,IAAIuN,WAAW,EAAE;QACb,IAAI,CAAC1M,WAAW,CAAC8M,aAAa,CAAC3N,MAAM,CAAC;MAC1C;MACA,IAAI,IAAI,CAAClC,QAAQ,EAAE;QACf,IAAI,CAACmO,WAAW,CAAC,CAAC;QAClB,IAAIsB,WAAW,EAAE;UACb;UACA;UACA;UACA;UACA,IAAI,CAACH,KAAK,CAAC,CAAC;QAChB;MACJ;IACJ;IACA,IAAIK,WAAW,KAAK,IAAI,CAAC3L,eAAe,CAACuK,UAAU,CAACrM,MAAM,CAAC,EAAE;MACzD,IAAI,CAAC0N,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACnM,YAAY,CAACnE,IAAI,CAAC,CAAC;EAC5B;EACA;EACA6O,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACnO,QAAQ,EAAE;MACf,MAAMmC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtC,IAAI,CAAC4B,eAAe,CAAC8L,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAChC,OAAO,IAAI,CAACC,cAAc,GACpB,IAAI,CAACA,cAAc,CAACF,CAAC,EAAEC,CAAC,EAAE7N,OAAO,CAAC,GAClCA,OAAO,CAAC+N,OAAO,CAACH,CAAC,CAAC,GAAG5N,OAAO,CAAC+N,OAAO,CAACF,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAACvM,YAAY,CAACnE,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACAsQ,iBAAiBA,CAACO,aAAa,EAAE;IAC7B,IAAIC,WAAW;IACf,IAAI,IAAI,CAACpQ,QAAQ,EAAE;MACfoQ,WAAW,GAAG,IAAI,CAAC9E,QAAQ,CAAC3O,GAAG,CAACuF,MAAM,IAAIA,MAAM,CAACJ,KAAK,CAAC;IAC3D,CAAC,MACI;MACDsO,WAAW,GAAG,IAAI,CAAC9E,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACxJ,KAAK,GAAGqO,aAAa;IACrE;IACA,IAAI,CAAC7L,MAAM,GAAG8L,WAAW;IACzB,IAAI,CAACzI,WAAW,CAAC0I,IAAI,CAACD,WAAW,CAAC;IAClC,IAAI,CAAC1L,SAAS,CAAC0L,WAAW,CAAC;IAC3B,IAAI,CAAC1I,eAAe,CAAC2I,IAAI,CAAC,IAAI,CAACpN,eAAe,CAACmN,WAAW,CAAC,CAAC;IAC5D,IAAI,CAAClL,kBAAkB,CAAC0F,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACID,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC5H,WAAW,EAAE;MAClB,IAAI,IAAI,CAACwI,KAAK,EAAE;QACZ;QACA;QACA;QACA,IAAI+E,uBAAuB,GAAG,CAAC,CAAC;QAChC,KAAK,IAAIrO,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACE,OAAO,CAACoO,MAAM,EAAEtO,KAAK,EAAE,EAAE;UACtD,MAAMC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACqO,GAAG,CAACvO,KAAK,CAAC;UACtC,IAAI,CAACC,MAAM,CAAC2E,QAAQ,EAAE;YAClByJ,uBAAuB,GAAGrO,KAAK;YAC/B;UACJ;QACJ;QACA,IAAI,CAACc,WAAW,CAAC8M,aAAa,CAACS,uBAAuB,CAAC;MAC3D,CAAC,MACI;QACD,IAAI,CAACvN,WAAW,CAAC8M,aAAa,CAAC,IAAI,CAAC7L,eAAe,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpE;IACJ;EACJ;EACA;EACAf,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACnH,UAAU,IAAI,CAAC,IAAI,CAACyD,QAAQ,IAAI,IAAI,CAAC1E,OAAO,EAAEoO,MAAM,GAAG,CAAC;EACzE;EACA;EACAjB,KAAKA,CAACnN,OAAO,EAAE;IACX,IAAI,CAACkD,WAAW,CAAC/C,aAAa,CAACgN,KAAK,CAACnN,OAAO,CAAC;EACjD;EACA;EACAjC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACD,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAMwQ,OAAO,GAAG,IAAI,CAAChL,gBAAgB,EAAEiL,UAAU,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACG,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACC,cAAc,GAAGH,OAAO;EAChF;EACA;EACAI,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC/I,SAAS,IAAI,IAAI,CAAC/E,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC0J,UAAU,EAAE;MACnE,OAAO,IAAI,CAAC1J,WAAW,CAAC0J,UAAU,CAAC1M,EAAE;IACzC;IACA,OAAO,IAAI;EACf;EACA;EACA0J,yBAAyBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACxJ,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAMwQ,OAAO,GAAG,IAAI,CAAChL,gBAAgB,EAAEiL,UAAU,CAAC,CAAC;IACnD,IAAI5O,KAAK,GAAG,CAAC2O,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAAChK,QAAQ;IAC1D,IAAI,IAAI,CAACmK,cAAc,EAAE;MACrB9O,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC8O,cAAc;IACtC;IACA,OAAO9O,KAAK;EAChB;EACA;EACAwG,mBAAmBA,CAACwI,MAAM,EAAE;IACxB,IAAI,CAACxJ,YAAY,CAAC+I,IAAI,CAACS,MAAM,CAAC;EAClC;EACA;AACJ;AACA;AACA;EACIC,iBAAiBA,CAACC,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACT,MAAM,EAAE;MACZ,IAAI,CAAClL,WAAW,CAAC/C,aAAa,CAACoH,YAAY,CAAC,kBAAkB,EAAEsH,GAAG,CAACrF,IAAI,CAAC,GAAG,CAAC,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAACtG,WAAW,CAAC/C,aAAa,CAACqH,eAAe,CAAC,kBAAkB,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;EACIsH,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC3B,KAAK,CAAC,CAAC;IACZ,IAAI,CAAChF,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;EACI,IAAI4G,gBAAgBA,CAAA,EAAG;IACnB;IACA;IACA,OAAO,IAAI,CAACpJ,SAAS,IAAI,CAAC,IAAI,CAACyD,KAAK,IAAK,IAAI,CAACrI,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC9E,WAAY;EAChF;EACA;IAAS,IAAI,CAAC+S,IAAI,YAAAC,kBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFtP,SAAS,EAAnBpJ,EAAE,CAAA2Y,iBAAA,CAAmCnW,EAAE,CAACoW,aAAa,GAArD5Y,EAAE,CAAA2Y,iBAAA,CAAgE3Y,EAAE,CAAC6Y,iBAAiB,GAAtF7Y,EAAE,CAAA2Y,iBAAA,CAAiG3Y,EAAE,CAAC8Y,MAAM,GAA5G9Y,EAAE,CAAA2Y,iBAAA,CAAuHvX,EAAE,CAAC2X,iBAAiB,GAA7I/Y,EAAE,CAAA2Y,iBAAA,CAAwJ3Y,EAAE,CAACgZ,UAAU,GAAvKhZ,EAAE,CAAA2Y,iBAAA,CAAkL7V,EAAE,CAACmW,cAAc,MAArMjZ,EAAE,CAAA2Y,iBAAA,CAAgOnV,EAAE,CAAC0V,MAAM,MAA3OlZ,EAAE,CAAA2Y,iBAAA,CAAsQnV,EAAE,CAAC2V,kBAAkB,MAA7RnZ,EAAE,CAAA2Y,iBAAA,CAAwT5W,cAAc,MAAxU/B,EAAE,CAAA2Y,iBAAA,CAAmWnV,EAAE,CAAC4V,SAAS,OAAjXpZ,EAAE,CAAAqZ,iBAAA,CAAwZ,UAAU,GAAparZ,EAAE,CAAA2Y,iBAAA,CAAgcxQ,0BAA0B,GAA5dnI,EAAE,CAAA2Y,iBAAA,CAAuejW,EAAE,CAAC4W,aAAa,GAAzftZ,EAAE,CAAA2Y,iBAAA,CAAogBjQ,iBAAiB;IAAA,CAA4D;EAAE;EACrrB;IAAS,IAAI,CAAC6Q,IAAI,kBAD8EvZ,EAAE,CAAAwZ,iBAAA;MAAAC,IAAA,EACJrQ,SAAS;MAAAsQ,SAAA;MAAAC,cAAA,WAAAC,yBAAA5U,EAAA,EAAAC,GAAA,EAAA4U,QAAA;QAAA,IAAA7U,EAAA;UADPhF,EAAE,CAAA8Z,cAAA,CAAAD,QAAA,EAIxB9Q,kBAAkB;UAJI/I,EAAE,CAAA8Z,cAAA,CAAAD,QAAA,EAIuDpY,SAAS;UAJlEzB,EAAE,CAAA8Z,cAAA,CAAAD,QAAA,EAIkInY,YAAY;QAAA;QAAA,IAAAsD,EAAA;UAAA,IAAA+U,EAAA;UAJhJ/Z,EAAE,CAAAga,cAAA,CAAAD,EAAA,GAAF/Z,EAAE,CAAAia,WAAA,QAAAhV,GAAA,CAAAgB,aAAA,GAAA8T,EAAA,CAAAG,KAAA;UAAFla,EAAE,CAAAga,cAAA,CAAAD,EAAA,GAAF/Z,EAAE,CAAAia,WAAA,QAAAhV,GAAA,CAAAuE,OAAA,GAAAuQ,EAAA;UAAF/Z,EAAE,CAAAga,cAAA,CAAAD,EAAA,GAAF/Z,EAAE,CAAAia,WAAA,QAAAhV,GAAA,CAAA4E,YAAA,GAAAkQ,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAC,gBAAApV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAqa,WAAA,CAAA1V,GAAA;UAAF3E,EAAE,CAAAqa,WAAA,CAAAzV,GAAA;UAAF5E,EAAE,CAAAqa,WAAA,CAIiZza,mBAAmB;QAAA;QAAA,IAAAoF,EAAA;UAAA,IAAA+U,EAAA;UAJta/Z,EAAE,CAAAga,cAAA,CAAAD,EAAA,GAAF/Z,EAAE,CAAAia,WAAA,QAAAhV,GAAA,CAAAb,OAAA,GAAA2V,EAAA,CAAAG,KAAA;UAAFla,EAAE,CAAAga,cAAA,CAAAD,EAAA,GAAF/Z,EAAE,CAAAia,WAAA,QAAAhV,GAAA,CAAAyE,KAAA,GAAAqQ,EAAA,CAAAG,KAAA;UAAFla,EAAE,CAAAga,cAAA,CAAAD,EAAA,GAAF/Z,EAAE,CAAAia,WAAA,QAAAhV,GAAA,CAAAyP,WAAA,GAAAqF,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA,WACyuC,UAAU,uBAAuB,MAAM,mBAAmB,SAAS;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uBAAAzV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD9yChF,EAAE,CAAAqG,UAAA,qBAAAqU,qCAAAnU,MAAA;YAAA,OACJtB,GAAA,CAAA6B,cAAA,CAAAP,MAAqB,CAAC;UAAA,CAAd,CAAC,mBAAAoU,mCAAA;YAAA,OAAT1V,GAAA,CAAAqP,QAAA,CAAS,CAAC;UAAA,CAAF,CAAC,kBAAAsG,kCAAA;YAAA,OAAT3V,GAAA,CAAAsP,OAAA,CAAQ,CAAC;UAAA,CAAD,CAAC;QAAA;QAAA,IAAAvP,EAAA;UADPhF,EAAE,CAAAmH,WAAA,OAAAlC,GAAA,CAAAmC,EAAA,cAAAnC,GAAA,CAAAiJ,QAAA,IACQ,CAAC,GAAAjJ,GAAA,CAAA8H,QAAA,mBAAA9H,GAAA,CAAAkK,SAAA,GAAAlK,GAAA,CAAAmC,EAAA,GAAI,QAAQ,GAAG,IAAI,mBAAAnC,GAAA,CAAAkK,SAAA,gBAAAlK,GAAA,CAAAqC,SAAA,IAAnB,IAAI,mBAAjBrC,GAAA,CAAA8F,QAAA,CAAA8P,QAAA,CAAkB,CAAC,mBAAnB5V,GAAA,CAAAiJ,QAAA,CAAA2M,QAAA,CAAkB,CAAC,kBAAA5V,GAAA,CAAAoH,UAAA,2BAAnBpH,GAAA,CAAAiT,wBAAA,CAAyB,CAAC;UADxBlY,EAAE,CAAA8a,WAAA,4BAAA7V,GAAA,CAAAiJ,QACI,CAAC,2BAAAjJ,GAAA,CAAAoH,UAAD,CAAC,4BAAApH,GAAA,CAAA8F,QAAD,CAAC,yBAAA9F,GAAA,CAAA2N,KAAD,CAAC,4BAAA3N,GAAA,CAAAoC,QAAD,CAAC;QAAA;MAAA;MAAA0T,MAAA;QAAAC,mBAAA,GADPhb,EAAE,CAAAib,YAAA,CAAAC,IAAA;QAAAhU,UAAA;QAAAgH,QAAA,GAAFlO,EAAE,CAAAib,YAAA,CAAAE,0BAAA,0BAC4L/a,gBAAgB;QAAA+N,aAAA,GAD9MnO,EAAE,CAAAib,YAAA,CAAAE,0BAAA,oCACiQ/a,gBAAgB;QAAA2M,QAAA,GADnR/M,EAAE,CAAAib,YAAA,CAAAE,0BAAA,0BACwThS,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG9I,eAAe,CAAC8I,KAAK,CAAE;QAAAuB,4BAAA,GADhX1K,EAAE,CAAAib,YAAA,CAAAE,0BAAA,kEACgd/a,gBAAgB;QAAAqF,WAAA;QAAAsF,QAAA,GADle/K,EAAE,CAAAib,YAAA,CAAAE,0BAAA,0BACkiB/a,gBAAgB;QAAAiH,QAAA,GADpjBrH,EAAE,CAAAib,YAAA,CAAAE,0BAAA,0BACwlB/a,gBAAgB;QAAAgO,sBAAA,GAD1mBpO,EAAE,CAAAib,YAAA,CAAAE,0BAAA,sDACwrB/a,gBAAgB;QAAAmL,WAAA;QAAApC,KAAA;QAAA7B,SAAA,GAD1sBtH,EAAE,CAAAib,YAAA,CAAAC,IAAA;QAAAjD,cAAA,GAAFjY,EAAE,CAAAib,YAAA,CAAAC,IAAA;QAAAlP,iBAAA;QAAAqD,yBAAA,GAAFrP,EAAE,CAAAib,YAAA,CAAAE,0BAAA,4DACo9B9a,eAAe;QAAAiX,cAAA;QAAAlQ,EAAA;QAAAiH,UAAA;MAAA;MAAA+M,OAAA;QAAAzM,YAAA;QAAAC,aAAA;QAAAE,aAAA;QAAAC,eAAA;QAAAC,WAAA;MAAA;MAAAqM,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADr+Bvb,EAAE,CAAAwb,kBAAA,CACyiE,CACnoE;QAAE5S,OAAO,EAAE5G,mBAAmB;QAAEyZ,WAAW,EAAErS;MAAU,CAAC,EACxD;QAAER,OAAO,EAAEpH,2BAA2B;QAAEia,WAAW,EAAErS;MAAU,CAAC,CACnE,GAJ2FpJ,EAAE,CAAA0b,wBAAA,EAAF1b,EAAE,CAAA2b,oBAAA,EAAF3b,EAAE,CAAA4b,mBAAA;MAAAC,kBAAA,EAAA/W,GAAA;MAAAgX,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAAlX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAmX,GAAA,GAAFnc,EAAE,CAAAoG,gBAAA;UAAFpG,EAAE,CAAAoc,eAAA,CAAAvX,GAAA;UAAF7E,EAAE,CAAAkF,cAAA,eAI2pB,CAAC;UAJ9pBlF,EAAE,CAAAqG,UAAA,mBAAAgW,wCAAA;YAAFrc,EAAE,CAAAwG,aAAA,CAAA2V,GAAA;YAAA,OAAFnc,EAAE,CAAAyG,WAAA,CAIklBxB,GAAA,CAAA0M,IAAA,CAAK,CAAC;UAAA,CAAC,CAAC;UAJ5lB3R,EAAE,CAAAkF,cAAA,YAI4tB,CAAC;UAJ/tBlF,EAAE,CAAA+F,UAAA,IAAAhB,gCAAA,iBAI+uB,CAAC,IAAAe,gCAAA,MAA+G,CAAC;UAJl2B9F,EAAE,CAAAoF,YAAA,CAI4nC,CAAC;UAJ/nCpF,EAAE,CAAAkF,cAAA,YAI8qC,CAAC,YAAyC,CAAC;UAJ3tClF,EAAE,CAAAsc,cAAA;UAAFtc,EAAE,CAAAkF,cAAA,YAIy6C,CAAC;UAJ56ClF,EAAE,CAAAuc,SAAA,aAI+8C,CAAC;UAJl9Cvc,EAAE,CAAAoF,YAAA,CAI69C,CAAC,CAAW,CAAC,CAAS,CAAC,CAAO,CAAC;UAJ9/CpF,EAAE,CAAA+F,UAAA,KAAAG,iCAAA,wBAI6lE,CAAC;UAJhmElG,EAAE,CAAAqG,UAAA,2BAAAmW,yDAAA;YAAFxc,EAAE,CAAAwG,aAAA,CAAA2V,GAAA;YAAA,OAAFnc,EAAE,CAAAyG,WAAA,CAI8hExB,GAAA,CAAAyM,KAAA,CAAM,CAAC;UAAA,CAAC,CAAC,oBAAA+K,kDAAA;YAJziEzc,EAAE,CAAAwG,aAAA,CAAA2V,GAAA;YAAA,OAAFnc,EAAE,CAAAyG,WAAA,CAIsjExB,GAAA,CAAAwP,WAAA,CAAY,CAAC;UAAA,CAAC,CAAC,oBAAAiI,kDAAA;YAJvkE1c,EAAE,CAAAwG,aAAA,CAAA2V,GAAA;YAAA,OAAFnc,EAAE,CAAAyG,WAAA,CAIolExB,GAAA,CAAAyM,KAAA,CAAM,CAAC;UAAA,CAAC,CAAC;QAAA;QAAA,IAAA1M,EAAA;UAAA,MAAA2X,wBAAA,GAJ/lE3c,EAAE,CAAA4c,WAAA;UAAF5c,EAAE,CAAAuF,SAAA,EAI2tB,CAAC;UAJ9tBvF,EAAE,CAAAmH,WAAA,OAAAlC,GAAA,CAAA6I,QAAA;UAAF9N,EAAE,CAAAuF,SAAA,CAIknC,CAAC;UAJrnCvF,EAAE,CAAAgG,aAAA,IAAAf,GAAA,CAAA2N,KAAA,QAIknC,CAAC;UAJrnC5S,EAAE,CAAAuF,SAAA,EAI4uD,CAAC;UAJ/uDvF,EAAE,CAAAiH,UAAA,kCAAAhC,GAAA,CAAA8I,kBAI4uD,CAAC,sCAAA9I,GAAA,CAAAsK,eAA0D,CAAC,8BAAAtK,GAAA,CAAA8K,uBAAA,IAAA4M,wBAAmF,CAAC,4BAAA1X,GAAA,CAAAkK,SAA0C,CAAC,iCAAAlK,GAAA,CAAAkI,UAAgD,CAAC,6BAAAlI,GAAA,CAAA4K,aAA+C,CAAC;QAAA;MAAA;MAAAgN,YAAA,GAAqkJld,gBAAgB,EAAuIC,mBAAmB,EAA4+BE,OAAO;MAAAgd,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAAsE,CAACzV,mBAAmB,CAACG,cAAc;MAAC;MAAAuV,eAAA;IAAA,EAAiG;EAAE;AAC7hQ;AACA;EAAA,QAAA5R,SAAA,oBAAAA,SAAA,KANoGtL,EAAE,CAAAmd,iBAAA,CAMX/T,SAAS,EAAc,CAAC;IACvGqQ,IAAI,EAAEnZ,SAAS;IACf8c,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEhC,QAAQ,EAAE,WAAW;MAAE0B,aAAa,EAAExc,iBAAiB,CAAC2a,IAAI;MAAEgC,eAAe,EAAE1c,uBAAuB,CAAC8c,MAAM;MAAEC,IAAI,EAAE;QAC1I,MAAM,EAAE,UAAU;QAClB,mBAAmB,EAAE,MAAM;QAC3B,eAAe,EAAE,SAAS;QAC1B,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE,kCAAkC;QAC1D,sBAAsB,EAAE,WAAW;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,sBAAsB,EAAE,qBAAqB;QAC7C,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,YAAY;QACnC,8BAA8B,EAAE,4BAA4B;QAC5D,iCAAiC,EAAE,UAAU;QAC7C,gCAAgC,EAAE,YAAY;QAC9C,iCAAiC,EAAE,UAAU;QAC7C,8BAA8B,EAAE,OAAO;QACvC,iCAAiC,EAAE,UAAU;QAC7C,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE,YAAY;QACvB,QAAQ,EAAE;MACd,CAAC;MAAEC,UAAU,EAAE,CAAChW,mBAAmB,CAACG,cAAc,CAAC;MAAE8V,SAAS,EAAE,CAC5D;QAAE7U,OAAO,EAAE5G,mBAAmB;QAAEyZ,WAAW,EAAErS;MAAU,CAAC,EACxD;QAAER,OAAO,EAAEpH,2BAA2B;QAAEia,WAAW,EAAErS;MAAU,CAAC,CACnE;MAAEkS,UAAU,EAAE,IAAI;MAAEoC,OAAO,EAAE,CAAC/d,gBAAgB,EAAEC,mBAAmB,EAAEE,OAAO,CAAC;MAAEmc,QAAQ,EAAE,6qEAA6qE;MAAEa,MAAM,EAAE,CAAC,s2HAAs2H;IAAE,CAAC;EACvoM,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErD,IAAI,EAAEjX,EAAE,CAACoW;EAAc,CAAC,EAAE;IAAEa,IAAI,EAAEzZ,EAAE,CAAC6Y;EAAkB,CAAC,EAAE;IAAEY,IAAI,EAAEzZ,EAAE,CAAC8Y;EAAO,CAAC,EAAE;IAAEW,IAAI,EAAErY,EAAE,CAAC2X;EAAkB,CAAC,EAAE;IAAEU,IAAI,EAAEzZ,EAAE,CAACgZ;EAAW,CAAC,EAAE;IAAES,IAAI,EAAE3W,EAAE,CAACmW,cAAc;IAAE0E,UAAU,EAAE,CAAC;MACjMlE,IAAI,EAAEhZ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgZ,IAAI,EAAEjW,EAAE,CAAC0V,MAAM;IAAEyE,UAAU,EAAE,CAAC;MAClClE,IAAI,EAAEhZ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgZ,IAAI,EAAEjW,EAAE,CAAC2V,kBAAkB;IAAEwE,UAAU,EAAE,CAAC;MAC9ClE,IAAI,EAAEhZ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgZ,IAAI,EAAE3X,EAAE,CAACK,YAAY;IAAEwb,UAAU,EAAE,CAAC;MACxClE,IAAI,EAAEhZ;IACV,CAAC,EAAE;MACCgZ,IAAI,EAAE/Y,MAAM;MACZ0c,IAAI,EAAE,CAACrb,cAAc;IACzB,CAAC;EAAE,CAAC,EAAE;IAAE0X,IAAI,EAAEjW,EAAE,CAAC4V,SAAS;IAAEuE,UAAU,EAAE,CAAC;MACrClE,IAAI,EAAE9Y;IACV,CAAC,EAAE;MACC8Y,IAAI,EAAEhZ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgZ,IAAI,EAAEvI,SAAS;IAAEyM,UAAU,EAAE,CAAC;MAClClE,IAAI,EAAE7Y,SAAS;MACfwc,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAE3D,IAAI,EAAEvI,SAAS;IAAEyM,UAAU,EAAE,CAAC;MAClClE,IAAI,EAAE/Y,MAAM;MACZ0c,IAAI,EAAE,CAACjV,0BAA0B;IACrC,CAAC;EAAE,CAAC,EAAE;IAAEsR,IAAI,EAAE/W,EAAE,CAAC4W;EAAc,CAAC,EAAE;IAAEG,IAAI,EAAEvI,SAAS;IAAEyM,UAAU,EAAE,CAAC;MAC9DlE,IAAI,EAAEhZ;IACV,CAAC,EAAE;MACCgZ,IAAI,EAAE/Y,MAAM;MACZ0c,IAAI,EAAE,CAAC1U,iBAAiB;IAC5B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEc,OAAO,EAAE,CAAC;MACnCiQ,IAAI,EAAE5Y,eAAe;MACrBuc,IAAI,EAAE,CAAC3b,SAAS,EAAE;QAAEmc,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE/T,YAAY,EAAE,CAAC;MACf4P,IAAI,EAAE5Y,eAAe;MACrBuc,IAAI,EAAE,CAAC1b,YAAY,EAAE;QAAEkc,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAE3X,aAAa,EAAE,CAAC;MAChBwT,IAAI,EAAE3Y,YAAY;MAClBsc,IAAI,EAAE,CAACrU,kBAAkB;IAC7B,CAAC,CAAC;IAAEiS,mBAAmB,EAAE,CAAC;MACtBvB,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEhZ,OAAO,EAAE,CAAC;MACVqV,IAAI,EAAEzY,SAAS;MACfoc,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE1T,KAAK,EAAE,CAAC;MACR+P,IAAI,EAAEzY,SAAS;MACfoc,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE1I,WAAW,EAAE,CAAC;MACd+E,IAAI,EAAEzY,SAAS;MACfoc,IAAI,EAAE,CAACxd,mBAAmB;IAC9B,CAAC,CAAC;IAAEsH,UAAU,EAAE,CAAC;MACbuS,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAEmN,QAAQ,EAAE,CAAC;MACXuL,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QAAEvV,SAAS,EAAEzH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+N,aAAa,EAAE,CAAC;MAChBsL,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QAAEvV,SAAS,EAAEzH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2M,QAAQ,EAAE,CAAC;MACX0M,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QACCvV,SAAS,EAAGsB,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG9I,eAAe,CAAC8I,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEuB,4BAA4B,EAAE,CAAC;MAC/B+O,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QAAEvV,SAAS,EAAEzH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqF,WAAW,EAAE,CAAC;MACdgU,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAEgK,QAAQ,EAAE,CAAC;MACX0O,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QAAEvV,SAAS,EAAEzH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiH,QAAQ,EAAE,CAAC;MACXoS,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QAAEvV,SAAS,EAAEzH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgO,sBAAsB,EAAE,CAAC;MACzBqL,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QAAEvV,SAAS,EAAEzH;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmL,WAAW,EAAE,CAAC;MACdkO,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAEoI,KAAK,EAAE,CAAC;MACRsQ,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAEuG,SAAS,EAAE,CAAC;MACZmS,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEnF,cAAc,EAAE,CAAC;MACjBwB,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEpR,iBAAiB,EAAE,CAAC;MACpByN,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAEsO,yBAAyB,EAAE,CAAC;MAC5BoK,IAAI,EAAE1Y,KAAK;MACXqc,IAAI,EAAE,CAAC;QAAEvV,SAAS,EAAExH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEiX,cAAc,EAAE,CAAC;MACjBmC,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAEqG,EAAE,EAAE,CAAC;MACLqS,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAEsN,UAAU,EAAE,CAAC;MACboL,IAAI,EAAE1Y;IACV,CAAC,CAAC;IAAE4N,YAAY,EAAE,CAAC;MACf8K,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE2N,aAAa,EAAE,CAAC;MAChB6K,IAAI,EAAExY,MAAM;MACZmc,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEtO,aAAa,EAAE,CAAC;MAChB2K,IAAI,EAAExY,MAAM;MACZmc,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAErO,eAAe,EAAE,CAAC;MAClB0K,IAAI,EAAExY;IACV,CAAC,CAAC;IAAE+N,WAAW,EAAE,CAAC;MACdyK,IAAI,EAAExY;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM4c,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACrF,IAAI,YAAAsF,yBAAApF,CAAA;MAAA,YAAAA,CAAA,IAAwFmF,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACE,IAAI,kBAnJ8E/d,EAAE,CAAAge,iBAAA;MAAAvE,IAAA,EAmJJoE,gBAAgB;MAAAnE,SAAA;MAAA4B,UAAA;MAAAC,QAAA,GAnJdvb,EAAE,CAAAwb,kBAAA,CAmJ6E,CAAC;QAAE5S,OAAO,EAAEG,kBAAkB;QAAE0S,WAAW,EAAEoC;MAAiB,CAAC,CAAC;IAAA,EAAiB;EAAE;AACtQ;AACA;EAAA,QAAAvS,SAAA,oBAAAA,SAAA,KArJoGtL,EAAE,CAAAmd,iBAAA,CAqJXU,gBAAgB,EAAc,CAAC;IAC9GpE,IAAI,EAAEvY,SAAS;IACfkc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BI,SAAS,EAAE,CAAC;QAAE7U,OAAO,EAAEG,kBAAkB;QAAE0S,WAAW,EAAEoC;MAAiB,CAAC,CAAC;MAC3EvC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM2C,eAAe,CAAC;EAClB;IAAS,IAAI,CAACzF,IAAI,YAAA0F,wBAAAxF,CAAA;MAAA,YAAAA,CAAA,IAAwFuF,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBAhK8Ene,EAAE,CAAAoe,gBAAA;MAAA3E,IAAA,EAgKSwE;IAAe,EAU/F;EAAE;EAC7B;IAAS,IAAI,CAACI,IAAI,kBA3K8Ere,EAAE,CAAAse,gBAAA;MAAAb,SAAA,EA2KqC,CAAC9U,mCAAmC,CAAC;MAAA+U,OAAA,GAAY3d,YAAY,EAC5LF,aAAa,EACb8B,eAAe,EACfC,eAAe,EAAEa,mBAAmB,EACpCR,kBAAkB,EAClBN,eAAe,EACfC,eAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAA0J,SAAA,oBAAAA,SAAA,KAnLoGtL,EAAE,CAAAmd,iBAAA,CAmLXc,eAAe,EAAc,CAAC;IAC7GxE,IAAI,EAAEtY,QAAQ;IACdic,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CACL3d,YAAY,EACZF,aAAa,EACb8B,eAAe,EACfC,eAAe,EACfwH,SAAS,EACTyU,gBAAgB,CACnB;MACDU,OAAO,EAAE,CACL9b,mBAAmB,EACnBR,kBAAkB,EAClBmH,SAAS,EACTyU,gBAAgB,EAChBlc,eAAe,EACfC,eAAe,CAClB;MACD6b,SAAS,EAAE,CAAC9U,mCAAmC;IACnD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASD,iBAAiB,EAAEP,0BAA0B,EAAEQ,mCAAmC,EAAEF,2CAA2C,EAAEM,kBAAkB,EAAEK,SAAS,EAAEJ,eAAe,EAAEiV,eAAe,EAAEJ,gBAAgB,EAAErW,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}