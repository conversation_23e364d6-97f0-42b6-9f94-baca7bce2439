{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MediaService {\n  constructor() {\n    this.mediaErrors = new BehaviorSubject([]);\n    this.mediaErrors$ = this.mediaErrors.asObservable();\n    // Fallback images for different scenarios\n    this.fallbackImages = {\n      user: '/assets/images/default-avatar.svg',\n      product: '/assets/images/default-product.svg',\n      post: '/assets/images/default-post.svg',\n      story: '/assets/images/default-story.svg'\n    };\n    // Enhanced video library with fashion-focused content\n    this.sampleVideos = [{\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerBlazes.jpg',\n      duration: 15,\n      title: 'Fashion Showcase',\n      description: 'Stylish fashion presentation'\n    }, {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerEscapes.jpg',\n      duration: 15,\n      title: 'Style Journey',\n      description: 'Fashion adventure and style'\n    }, {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerFun.jpg',\n      duration: 60,\n      title: 'Fashion Fun',\n      description: 'Playful fashion moments'\n    }, {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerJoyrides.jpg',\n      duration: 15,\n      title: 'Style Ride',\n      description: 'Fashion on the go'\n    }, {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerMeltdowns.jpg',\n      duration: 15,\n      title: 'Fashion Drama',\n      description: 'Dramatic fashion moments'\n    }, {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/Sintel.jpg',\n      duration: 888,\n      title: 'Fashion Story',\n      description: 'A beautiful fashion narrative'\n    }];\n    // Broken URL patterns to fix\n    this.brokenUrlPatterns = ['/uploads/stories/images/', '/uploads/stories/videos/', 'sample-videos.com', 'localhost:', 'file://'];\n  }\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url, type = 'post') {\n    if (!url || url.trim() === '') {\n      return this.fallbackImages[type];\n    }\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return fallback for invalid URLs\n      return this.fallbackImages[type];\n    }\n  }\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  fixBrokenUrl(url, type) {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getRandomSampleVideo().url;\n        }\n        if (pattern === 'localhost:' || pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  getReplacementMediaUrl(originalUrl, type) {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n  /**\n   * Handle image load errors\n   */\n  handleImageError(event, fallbackType = 'post') {\n    const img = event.target;\n    if (img && img.src !== this.fallbackImages[fallbackType]) {\n      img.src = this.fallbackImages[fallbackType];\n      // Log error for debugging\n      this.logMediaError({\n        id: img.src,\n        type: 'load_error',\n        message: `Failed to load image: ${img.src}`,\n        fallbackUrl: this.fallbackImages[fallbackType]\n      });\n    }\n  }\n  /**\n   * Get a random sample video with enhanced metadata\n   */\n  getRandomSampleVideo() {\n    const randomIndex = Math.floor(Math.random() * this.sampleVideos.length);\n    return this.sampleVideos[randomIndex];\n  }\n  /**\n   * Get all sample videos with enhanced metadata\n   */\n  getSampleVideos() {\n    return [...this.sampleVideos];\n  }\n  /**\n   * Get video by content type (for better matching)\n   */\n  getVideoByType(contentType) {\n    const typeMapping = {\n      'fashion': 0,\n      'style': 1,\n      'showcase': 2,\n      'tutorial': 3,\n      'story': 5 // Sintel - Fashion Story\n    };\n    const index = typeMapping[contentType] || 0;\n    return this.sampleVideos[index] || this.sampleVideos[0];\n  }\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url) {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || lowerUrl.includes('video') || lowerUrl.includes('.mp4');\n  }\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl) {\n    // Check if it's one of our sample videos\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    if (sampleVideo) {\n      return sampleVideo.thumbnail;\n    }\n    // For other videos, try to generate thumbnail URL or use fallback\n    return this.fallbackImages.post;\n  }\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl) {\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    return sampleVideo ? sampleVideo.duration : 30; // Default 30 seconds\n  }\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray) {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16 / 9 : 1),\n        size: media.size\n      };\n    });\n  }\n  /**\n   * Add sample videos to existing media array\n   */\n  enhanceWithSampleVideos(mediaArray, videoCount = 2) {\n    const processedMedia = this.processMediaItems(mediaArray);\n    // Add sample videos if we don't have enough media\n    if (processedMedia.length < 3) {\n      const videosToAdd = Math.min(videoCount, this.sampleVideos.length);\n      for (let i = 0; i < videosToAdd; i++) {\n        const sampleVideo = this.sampleVideos[i];\n        processedMedia.push({\n          id: `sample_video_${i}`,\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnailUrl: sampleVideo.thumbnail,\n          alt: `Sample video ${i + 1}`,\n          duration: sampleVideo.duration,\n          aspectRatio: 16 / 9\n        });\n      }\n    }\n    return processedMedia;\n  }\n  /**\n   * Log media errors for debugging\n   */\n  logMediaError(error) {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n    console.warn('Media Error:', error);\n  }\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors() {\n    this.mediaErrors.next([]);\n  }\n  /**\n   * Preload media for better performance\n   */\n  preloadMedia(mediaItems) {\n    const promises = mediaItems.map(media => {\n      return new Promise((resolve, reject) => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => reject(new Error(`Failed to load image: ${media.url}`));\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => reject(new Error(`Failed to load video: ${media.url}`));\n          video.src = media.url;\n          video.load();\n        }\n      });\n    });\n    return Promise.all(promises);\n  }\n  static {\n    this.ɵfac = function MediaService_Factory(t) {\n      return new (t || MediaService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MediaService,\n      factory: MediaService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "MediaService", "constructor", "mediaErrors", "mediaErrors$", "asObservable", "fallbackImages", "user", "product", "post", "story", "sampleVideos", "url", "thumbnail", "duration", "title", "description", "brokenUrlPatterns", "getSafeImageUrl", "type", "trim", "fixedUrl", "fixBrokenUrl", "URL", "startsWith", "pattern", "includes", "getReplacementMediaUrl", "getRandomSampleVideo", "originalUrl", "urlMappings", "key", "replacementUrl", "Object", "entries", "toLowerCase", "handleImageError", "event", "fallbackType", "img", "target", "src", "logMediaError", "id", "message", "fallbackUrl", "randomIndex", "Math", "floor", "random", "length", "getSampleVideos", "getVideoByType", "contentType", "typeMapping", "index", "isVideoUrl", "videoExtensions", "lowerUrl", "some", "ext", "getVideoThumbnail", "videoUrl", "sampleVideo", "find", "v", "getVideoDuration", "processMediaItems", "mediaArray", "Array", "isArray", "map", "media", "isVideo", "_id", "thumbnailUrl", "undefined", "alt", "aspectRatio", "size", "enhanceWithSampleVideos", "videoCount", "processedMedia", "videosToAdd", "min", "i", "push", "error", "currentErrors", "value", "next", "console", "warn", "clearMediaErrors", "preloadMedia", "mediaItems", "promises", "Promise", "resolve", "reject", "Image", "onload", "onerror", "Error", "video", "document", "createElement", "onloadeddata", "load", "all", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\media.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\nexport interface MediaItem {\n  id: string;\n  type: 'image' | 'video';\n  url: string;\n  thumbnailUrl?: string;\n  alt?: string;\n  duration?: number; // for videos in seconds\n  aspectRatio?: number;\n  size?: number; // file size in bytes\n}\n\nexport interface MediaError {\n  id: string;\n  type: 'load_error' | 'network_error' | 'format_error';\n  message: string;\n  fallbackUrl?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MediaService {\n  private mediaErrors = new BehaviorSubject<MediaError[]>([]);\n  public mediaErrors$ = this.mediaErrors.asObservable();\n\n  // Fallback images for different scenarios\n  private readonly fallbackImages = {\n    user: '/assets/images/default-avatar.svg',\n    product: '/assets/images/default-product.svg',\n    post: '/assets/images/default-post.svg',\n    story: '/assets/images/default-story.svg'\n  };\n\n  // Enhanced video library with fashion-focused content\n  private readonly sampleVideos = [\n    {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerBlazes.jpg',\n      duration: 15,\n      title: 'Fashion Showcase',\n      description: 'Stylish fashion presentation'\n    },\n    {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerEscapes.jpg',\n      duration: 15,\n      title: 'Style Journey',\n      description: 'Fashion adventure and style'\n    },\n    {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerFun.jpg',\n      duration: 60,\n      title: 'Fashion Fun',\n      description: 'Playful fashion moments'\n    },\n    {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerJoyrides.jpg',\n      duration: 15,\n      title: 'Style Ride',\n      description: 'Fashion on the go'\n    },\n    {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerMeltdowns.jpg',\n      duration: 15,\n      title: 'Fashion Drama',\n      description: 'Dramatic fashion moments'\n    },\n    {\n      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',\n      thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/Sintel.jpg',\n      duration: 888,\n      title: 'Fashion Story',\n      description: 'A beautiful fashion narrative'\n    }\n  ];\n\n  // Broken URL patterns to fix\n  private readonly brokenUrlPatterns = [\n    '/uploads/stories/images/',\n    '/uploads/stories/videos/',\n    'sample-videos.com',\n    'localhost:',\n    'file://'\n  ];\n\n  constructor() {}\n\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url: string | undefined, type: 'user' | 'product' | 'post' | 'story' = 'post'): string {\n    if (!url || url.trim() === '') {\n      return this.fallbackImages[type];\n    }\n\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return fallback for invalid URLs\n      return this.fallbackImages[type];\n    }\n  }\n\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  private fixBrokenUrl(url: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getRandomSampleVideo().url;\n        }\n        if (pattern === 'localhost:' || pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  private getReplacementMediaUrl(originalUrl: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings: { [key: string]: string } = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n\n  /**\n   * Handle image load errors\n   */\n  handleImageError(event: Event, fallbackType: 'user' | 'product' | 'post' | 'story' = 'post'): void {\n    const img = event.target as HTMLImageElement;\n    if (img && img.src !== this.fallbackImages[fallbackType]) {\n      img.src = this.fallbackImages[fallbackType];\n      \n      // Log error for debugging\n      this.logMediaError({\n        id: img.src,\n        type: 'load_error',\n        message: `Failed to load image: ${img.src}`,\n        fallbackUrl: this.fallbackImages[fallbackType]\n      });\n    }\n  }\n\n  /**\n   * Get a random sample video with enhanced metadata\n   */\n  getRandomSampleVideo(): { url: string; thumbnail: string; duration: number; title?: string; description?: string } {\n    const randomIndex = Math.floor(Math.random() * this.sampleVideos.length);\n    return this.sampleVideos[randomIndex];\n  }\n\n  /**\n   * Get all sample videos with enhanced metadata\n   */\n  getSampleVideos(): { url: string; thumbnail: string; duration: number; title?: string; description?: string }[] {\n    return [...this.sampleVideos];\n  }\n\n  /**\n   * Get video by content type (for better matching)\n   */\n  getVideoByType(contentType: 'fashion' | 'style' | 'showcase' | 'tutorial' | 'story'): { url: string; thumbnail: string; duration: number; title?: string; description?: string } {\n    const typeMapping: { [key: string]: number } = {\n      'fashion': 0, // ForBiggerBlazes - Fashion Showcase\n      'style': 1,   // ForBiggerEscapes - Style Journey\n      'showcase': 2, // ForBiggerFun - Fashion Fun\n      'tutorial': 3, // ForBiggerJoyrides - Style Ride\n      'story': 5    // Sintel - Fashion Story\n    };\n\n    const index = typeMapping[contentType] || 0;\n    return this.sampleVideos[index] || this.sampleVideos[0];\n  }\n\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url: string): boolean {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || \n           lowerUrl.includes('video') || \n           lowerUrl.includes('.mp4');\n  }\n\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl: string): string {\n    // Check if it's one of our sample videos\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    if (sampleVideo) {\n      return sampleVideo.thumbnail;\n    }\n\n    // For other videos, try to generate thumbnail URL or use fallback\n    return this.fallbackImages.post;\n  }\n\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl: string): number {\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    return sampleVideo ? sampleVideo.duration : 30; // Default 30 seconds\n  }\n\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray: any[]): MediaItem[] {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      \n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16/9 : 1),\n        size: media.size\n      };\n    });\n  }\n\n  /**\n   * Add sample videos to existing media array\n   */\n  enhanceWithSampleVideos(mediaArray: any[], videoCount: number = 2): MediaItem[] {\n    const processedMedia = this.processMediaItems(mediaArray);\n    \n    // Add sample videos if we don't have enough media\n    if (processedMedia.length < 3) {\n      const videosToAdd = Math.min(videoCount, this.sampleVideos.length);\n      \n      for (let i = 0; i < videosToAdd; i++) {\n        const sampleVideo = this.sampleVideos[i];\n        processedMedia.push({\n          id: `sample_video_${i}`,\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnailUrl: sampleVideo.thumbnail,\n          alt: `Sample video ${i + 1}`,\n          duration: sampleVideo.duration,\n          aspectRatio: 16/9\n        });\n      }\n    }\n\n    return processedMedia;\n  }\n\n  /**\n   * Log media errors for debugging\n   */\n  private logMediaError(error: MediaError): void {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n    console.warn('Media Error:', error);\n  }\n\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors(): void {\n    this.mediaErrors.next([]);\n  }\n\n  /**\n   * Preload media for better performance\n   */\n  preloadMedia(mediaItems: MediaItem[]): Promise<void[]> {\n    const promises = mediaItems.map(media => {\n      return new Promise<void>((resolve, reject) => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => reject(new Error(`Failed to load image: ${media.url}`));\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => reject(new Error(`Failed to load video: ${media.url}`));\n          video.src = media.url;\n          video.load();\n        }\n      });\n    });\n\n    return Promise.all(promises);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAuBtC,OAAM,MAAOC,YAAY;EAmEvBC,YAAA;IAlEQ,KAAAC,WAAW,GAAG,IAAIH,eAAe,CAAe,EAAE,CAAC;IACpD,KAAAI,YAAY,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;IAErD;IACiB,KAAAC,cAAc,GAAG;MAChCC,IAAI,EAAE,mCAAmC;MACzCC,OAAO,EAAE,oCAAoC;MAC7CC,IAAI,EAAE,iCAAiC;MACvCC,KAAK,EAAE;KACR;IAED;IACiB,KAAAC,YAAY,GAAG,CAC9B;MACEC,GAAG,EAAE,uFAAuF;MAC5FC,SAAS,EAAE,8FAA8F;MACzGC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE;KACd,EACD;MACEJ,GAAG,EAAE,wFAAwF;MAC7FC,SAAS,EAAE,+FAA+F;MAC1GC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE;KACd,EACD;MACEJ,GAAG,EAAE,oFAAoF;MACzFC,SAAS,EAAE,2FAA2F;MACtGC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE;KACd,EACD;MACEJ,GAAG,EAAE,yFAAyF;MAC9FC,SAAS,EAAE,gGAAgG;MAC3GC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE;KACd,EACD;MACEJ,GAAG,EAAE,0FAA0F;MAC/FC,SAAS,EAAE,iGAAiG;MAC5GC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE;KACd,EACD;MACEJ,GAAG,EAAE,8EAA8E;MACnFC,SAAS,EAAE,qFAAqF;MAChGC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE;KACd,CACF;IAED;IACiB,KAAAC,iBAAiB,GAAG,CACnC,0BAA0B,EAC1B,0BAA0B,EAC1B,mBAAmB,EACnB,YAAY,EACZ,SAAS,CACV;EAEc;EAEf;;;EAGAC,eAAeA,CAACN,GAAuB,EAAEO,IAAA,GAA8C,MAAM;IAC3F,IAAI,CAACP,GAAG,IAAIA,GAAG,CAACQ,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7B,OAAO,IAAI,CAACd,cAAc,CAACa,IAAI,CAAC;;IAGlC;IACA,MAAME,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACV,GAAG,EAAEO,IAAI,CAAC;IAC7C,IAAIE,QAAQ,KAAKT,GAAG,EAAE;MACpB,OAAOS,QAAQ;;IAGjB;IACA,IAAI;MACF,IAAIE,GAAG,CAACX,GAAG,CAAC;MACZ,OAAOA,GAAG;KACX,CAAC,MAAM;MACN;MACA,IAAIA,GAAG,CAACY,UAAU,CAAC,GAAG,CAAC,IAAIZ,GAAG,CAACY,UAAU,CAAC,IAAI,CAAC,IAAIZ,GAAG,CAACY,UAAU,CAAC,KAAK,CAAC,EAAE;QACxE,OAAOZ,GAAG;;MAEZ;MACA,OAAO,IAAI,CAACN,cAAc,CAACa,IAAI,CAAC;;EAEpC;EAEA;;;EAGQG,YAAYA,CAACV,GAAW,EAAEO,IAA2C;IAC3E;IACA,KAAK,MAAMM,OAAO,IAAI,IAAI,CAACR,iBAAiB,EAAE;MAC5C,IAAIL,GAAG,CAACc,QAAQ,CAACD,OAAO,CAAC,EAAE;QACzB;QACA,IAAIA,OAAO,KAAK,0BAA0B,IAAIA,OAAO,KAAK,0BAA0B,EAAE;UACpF,OAAO,IAAI,CAACE,sBAAsB,CAACf,GAAG,EAAEO,IAAI,CAAC;;QAE/C,IAAIM,OAAO,KAAK,mBAAmB,EAAE;UACnC,OAAO,IAAI,CAACG,oBAAoB,EAAE,CAAChB,GAAG;;QAExC,IAAIa,OAAO,KAAK,YAAY,IAAIA,OAAO,KAAK,SAAS,EAAE;UACrD,OAAO,IAAI,CAACnB,cAAc,CAACa,IAAI,CAAC;;;;IAItC,OAAOP,GAAG;EACZ;EAEA;;;EAGQe,sBAAsBA,CAACE,WAAmB,EAAEV,IAA2C;IAC7F;IACA,MAAMW,WAAW,GAA8B;MAC7C,mBAAmB,EAAE,oEAAoE;MACzF,eAAe,EAAE,oEAAoE;MACrF,oBAAoB,EAAE,oEAAoE;MAC1F,cAAc,EAAE,oEAAoE;MACpF,QAAQ,EAAE;KACX;IAED;IACA,KAAK,MAAM,CAACC,GAAG,EAAEC,cAAc,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;MAC/D,IAAID,WAAW,CAACM,WAAW,EAAE,CAACT,QAAQ,CAACK,GAAG,CAAC,EAAE;QAC3C,OAAOC,cAAc;;;IAIzB;IACA,OAAO,IAAI,CAAC1B,cAAc,CAACa,IAAI,CAAC;EAClC;EAEA;;;EAGAiB,gBAAgBA,CAACC,KAAY,EAAEC,YAAA,GAAsD,MAAM;IACzF,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAA0B;IAC5C,IAAID,GAAG,IAAIA,GAAG,CAACE,GAAG,KAAK,IAAI,CAACnC,cAAc,CAACgC,YAAY,CAAC,EAAE;MACxDC,GAAG,CAACE,GAAG,GAAG,IAAI,CAACnC,cAAc,CAACgC,YAAY,CAAC;MAE3C;MACA,IAAI,CAACI,aAAa,CAAC;QACjBC,EAAE,EAAEJ,GAAG,CAACE,GAAG;QACXtB,IAAI,EAAE,YAAY;QAClByB,OAAO,EAAE,yBAAyBL,GAAG,CAACE,GAAG,EAAE;QAC3CI,WAAW,EAAE,IAAI,CAACvC,cAAc,CAACgC,YAAY;OAC9C,CAAC;;EAEN;EAEA;;;EAGAV,oBAAoBA,CAAA;IAClB,MAAMkB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAACtC,YAAY,CAACuC,MAAM,CAAC;IACxE,OAAO,IAAI,CAACvC,YAAY,CAACmC,WAAW,CAAC;EACvC;EAEA;;;EAGAK,eAAeA,CAAA;IACb,OAAO,CAAC,GAAG,IAAI,CAACxC,YAAY,CAAC;EAC/B;EAEA;;;EAGAyC,cAAcA,CAACC,WAAoE;IACjF,MAAMC,WAAW,GAA8B;MAC7C,SAAS,EAAE,CAAC;MACZ,OAAO,EAAE,CAAC;MACV,UAAU,EAAE,CAAC;MACb,UAAU,EAAE,CAAC;MACb,OAAO,EAAE,CAAC,CAAI;KACf;IAED,MAAMC,KAAK,GAAGD,WAAW,CAACD,WAAW,CAAC,IAAI,CAAC;IAC3C,OAAO,IAAI,CAAC1C,YAAY,CAAC4C,KAAK,CAAC,IAAI,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC;EACzD;EAEA;;;EAGA6C,UAAUA,CAAC5C,GAAW;IACpB,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;IACtB,MAAM6C,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjE,MAAMC,QAAQ,GAAG9C,GAAG,CAACuB,WAAW,EAAE;IAClC,OAAOsB,eAAe,CAACE,IAAI,CAACC,GAAG,IAAIF,QAAQ,CAAChC,QAAQ,CAACkC,GAAG,CAAC,CAAC,IACnDF,QAAQ,CAAChC,QAAQ,CAAC,OAAO,CAAC,IAC1BgC,QAAQ,CAAChC,QAAQ,CAAC,MAAM,CAAC;EAClC;EAEA;;;EAGAmC,iBAAiBA,CAACC,QAAgB;IAChC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACpD,YAAY,CAACqD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAKkD,QAAQ,CAAC;IACnE,IAAIC,WAAW,EAAE;MACf,OAAOA,WAAW,CAAClD,SAAS;;IAG9B;IACA,OAAO,IAAI,CAACP,cAAc,CAACG,IAAI;EACjC;EAEA;;;EAGAyD,gBAAgBA,CAACJ,QAAgB;IAC/B,MAAMC,WAAW,GAAG,IAAI,CAACpD,YAAY,CAACqD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAKkD,QAAQ,CAAC;IACnE,OAAOC,WAAW,GAAGA,WAAW,CAACjD,QAAQ,GAAG,EAAE,CAAC,CAAC;EAClD;EAEA;;;EAGAqD,iBAAiBA,CAACC,UAAiB;IACjC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;;IAGX,OAAOA,UAAU,CAACG,GAAG,CAAC,CAACC,KAAK,EAAEjB,KAAK,KAAI;MACrC,MAAMkB,OAAO,GAAG,IAAI,CAACjB,UAAU,CAACgB,KAAK,CAAC5D,GAAG,CAAC;MAE1C,OAAO;QACL+B,EAAE,EAAE6B,KAAK,CAACE,GAAG,IAAI,SAASnB,KAAK,EAAE;QACjCpC,IAAI,EAAEsD,OAAO,GAAG,OAAO,GAAG,OAAO;QACjC7D,GAAG,EAAE,IAAI,CAACM,eAAe,CAACsD,KAAK,CAAC5D,GAAG,CAAC;QACpC+D,YAAY,EAAEF,OAAO,GAAG,IAAI,CAACZ,iBAAiB,CAACW,KAAK,CAAC5D,GAAG,CAAC,GAAGgE,SAAS;QACrEC,GAAG,EAAEL,KAAK,CAACK,GAAG,IAAI,EAAE;QACpB/D,QAAQ,EAAE2D,OAAO,GAAG,IAAI,CAACP,gBAAgB,CAACM,KAAK,CAAC5D,GAAG,CAAC,GAAGgE,SAAS;QAChEE,WAAW,EAAEN,KAAK,CAACM,WAAW,KAAKL,OAAO,GAAG,EAAE,GAAC,CAAC,GAAG,CAAC,CAAC;QACtDM,IAAI,EAAEP,KAAK,CAACO;OACb;IACH,CAAC,CAAC;EACJ;EAEA;;;EAGAC,uBAAuBA,CAACZ,UAAiB,EAAEa,UAAA,GAAqB,CAAC;IAC/D,MAAMC,cAAc,GAAG,IAAI,CAACf,iBAAiB,CAACC,UAAU,CAAC;IAEzD;IACA,IAAIc,cAAc,CAAChC,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMiC,WAAW,GAAGpC,IAAI,CAACqC,GAAG,CAACH,UAAU,EAAE,IAAI,CAACtE,YAAY,CAACuC,MAAM,CAAC;MAElE,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,EAAEE,CAAC,EAAE,EAAE;QACpC,MAAMtB,WAAW,GAAG,IAAI,CAACpD,YAAY,CAAC0E,CAAC,CAAC;QACxCH,cAAc,CAACI,IAAI,CAAC;UAClB3C,EAAE,EAAE,gBAAgB0C,CAAC,EAAE;UACvBlE,IAAI,EAAE,OAAO;UACbP,GAAG,EAAEmD,WAAW,CAACnD,GAAG;UACpB+D,YAAY,EAAEZ,WAAW,CAAClD,SAAS;UACnCgE,GAAG,EAAE,gBAAgBQ,CAAC,GAAG,CAAC,EAAE;UAC5BvE,QAAQ,EAAEiD,WAAW,CAACjD,QAAQ;UAC9BgE,WAAW,EAAE,EAAE,GAAC;SACjB,CAAC;;;IAIN,OAAOI,cAAc;EACvB;EAEA;;;EAGQxC,aAAaA,CAAC6C,KAAiB;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACrF,WAAW,CAACsF,KAAK;IAC5C,IAAI,CAACtF,WAAW,CAACuF,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAED,KAAK,CAAC,CAAC;IAChDI,OAAO,CAACC,IAAI,CAAC,cAAc,EAAEL,KAAK,CAAC;EACrC;EAEA;;;EAGAM,gBAAgBA,CAAA;IACd,IAAI,CAAC1F,WAAW,CAACuF,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEA;;;EAGAI,YAAYA,CAACC,UAAuB;IAClC,MAAMC,QAAQ,GAAGD,UAAU,CAACxB,GAAG,CAACC,KAAK,IAAG;MACtC,OAAO,IAAIyB,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;QAC3C,IAAI3B,KAAK,CAACrD,IAAI,KAAK,OAAO,EAAE;UAC1B,MAAMoB,GAAG,GAAG,IAAI6D,KAAK,EAAE;UACvB7D,GAAG,CAAC8D,MAAM,GAAG,MAAMH,OAAO,EAAE;UAC5B3D,GAAG,CAAC+D,OAAO,GAAG,MAAMH,MAAM,CAAC,IAAII,KAAK,CAAC,yBAAyB/B,KAAK,CAAC5D,GAAG,EAAE,CAAC,CAAC;UAC3E2B,GAAG,CAACE,GAAG,GAAG+B,KAAK,CAAC5D,GAAG;SACpB,MAAM,IAAI4D,KAAK,CAACrD,IAAI,KAAK,OAAO,EAAE;UACjC,MAAMqF,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;UAC7CF,KAAK,CAACG,YAAY,GAAG,MAAMT,OAAO,EAAE;UACpCM,KAAK,CAACF,OAAO,GAAG,MAAMH,MAAM,CAAC,IAAII,KAAK,CAAC,yBAAyB/B,KAAK,CAAC5D,GAAG,EAAE,CAAC,CAAC;UAC7E4F,KAAK,CAAC/D,GAAG,GAAG+B,KAAK,CAAC5D,GAAG;UACrB4F,KAAK,CAACI,IAAI,EAAE;;MAEhB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOX,OAAO,CAACY,GAAG,CAACb,QAAQ,CAAC;EAC9B;;;uBA3TW/F,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAA6G,OAAA,EAAZ7G,YAAY,CAAA8G,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}