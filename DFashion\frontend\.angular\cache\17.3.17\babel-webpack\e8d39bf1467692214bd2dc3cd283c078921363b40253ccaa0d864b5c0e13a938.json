{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../core/services/product.service\";\nimport * as i3 from \"../../core/services/auth.service\";\nimport * as i4 from \"../../core/services/cart.service\";\nimport * as i5 from \"../../core/services/wishlist.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction CategoryComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"label\");\n    i0.ɵɵtext(2, \"Size:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 10);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryComponent_div_44_Template_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedSize, $event) || (ctx_r1.selectedSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CategoryComponent_div_44_Template_select_change_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelementStart(4, \"option\", 16);\n    i0.ɵɵtext(5, \"All Sizes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 27);\n    i0.ɵɵtext(7, \"XS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 28);\n    i0.ɵɵtext(9, \"S\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 29);\n    i0.ɵɵtext(11, \"M\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 30);\n    i0.ɵɵtext(13, \"L\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 31);\n    i0.ɵɵtext(15, \"XL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 32);\n    i0.ɵɵtext(17, \"XXL\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedSize);\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r4), \"% OFF \");\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_14_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 59);\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_14_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 60);\n  }\n}\nfunction CategoryComponent_div_47_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtemplate(2, CategoryComponent_div_47_div_1_div_14_i_2_Template, 1, 0, \"i\", 56)(3, CategoryComponent_div_47_div_1_div_14_i_3_Template, 1, 0, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 58);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStars(product_r4.rating.average));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getEmptyStars(product_r4.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r4.rating.count, \")\");\n  }\n}\nfunction CategoryComponent_div_47_div_1_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r4.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction CategoryComponent_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_div_click_0_listener() {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelement(2, \"img\", 37);\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_button_click_4_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r4, $event));\n    });\n    i0.ɵɵelement(5, \"i\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_button_click_6_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.quickView(product_r4, $event));\n    });\n    i0.ɵɵelement(7, \"i\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, CategoryComponent_div_47_div_1_div_8_Template, 2, 1, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 44)(10, \"h3\", 45);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 46);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CategoryComponent_div_47_div_1_div_14_Template, 6, 3, \"div\", 47);\n    i0.ɵɵelementStart(15, \"div\", 48)(16, \"span\", 49);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CategoryComponent_div_47_div_1_span_19_Template, 3, 4, \"span\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_47_div_1_Template_button_click_20_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r4, $event));\n    });\n    i0.ɵɵelement(21, \"i\", 52);\n    i0.ɵɵtext(22, \" Add to Cart \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r4), i0.ɵɵsanitizeUrl)(\"alt\", product_r4.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice && product_r4.originalPrice > product_r4.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(18, 8, product_r4.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice && product_r4.originalPrice > product_r4.price);\n  }\n}\nfunction CategoryComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, CategoryComponent_div_47_div_1_Template, 23, 11, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredProducts);\n  }\n}\nfunction CategoryComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"No products found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your filters or browse other categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_48_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFilters());\n    });\n    i0.ɵɵtext(8, \"Clear Filters\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CategoryComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵelement(1, \"div\", 67);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading products...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CategoryComponent = /*#__PURE__*/(() => {\n  class CategoryComponent {\n    constructor(route, router, productService, authService, cartService, wishlistService) {\n      this.route = route;\n      this.router = router;\n      this.productService = productService;\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.category = '';\n      this.products = [];\n      this.filteredProducts = [];\n      this.loading = true;\n      // Filters\n      this.sortBy = 'featured';\n      this.priceRange = '';\n      this.selectedSize = '';\n    }\n    ngOnInit() {\n      this.route.params.subscribe(params => {\n        this.category = params['slug'] || params['category'];\n        this.loadProducts();\n      });\n    }\n    loadProducts() {\n      this.loading = true;\n      // Load products from API using category slug\n      this.productService.getCategoryProducts(this.category).subscribe({\n        next: response => {\n          this.products = response.products || [];\n          this.applyFilters();\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading products:', error);\n          this.products = [];\n          this.filteredProducts = [];\n          this.loading = false;\n        }\n      });\n    }\n    getCategoryDisplayName() {\n      const categoryNames = {\n        'women': 'Women\\'s Fashion',\n        'men': 'Men\\'s Fashion',\n        'kids': 'Kids\\' Fashion',\n        'ethnic': 'Ethnic Wear',\n        'all': 'All Products'\n      };\n      return categoryNames[this.category] || this.category.charAt(0).toUpperCase() + this.category.slice(1);\n    }\n    getCategoryDescription() {\n      const descriptions = {\n        'women': 'Discover the latest trends in women\\'s fashion',\n        'men': 'Explore stylish and comfortable men\\'s clothing',\n        'kids': 'Fun and comfortable clothing for children',\n        'ethnic': 'Traditional and ethnic wear for special occasions',\n        'all': 'Browse our complete collection of fashion items'\n      };\n      return descriptions[this.category] || 'Explore our collection';\n    }\n    onSortChange() {\n      this.applyFilters();\n    }\n    onFilterChange() {\n      this.applyFilters();\n    }\n    applyFilters() {\n      let filtered = [...this.products];\n      // Apply price filter\n      if (this.priceRange) {\n        if (this.priceRange === '10000+') {\n          filtered = filtered.filter(p => p.price >= 10000);\n        } else {\n          const [min, max] = this.priceRange.split('-').map(Number);\n          filtered = filtered.filter(p => p.price >= min && p.price <= max);\n        }\n      }\n      // Apply size filter\n      if (this.selectedSize) {\n        // Filter products by available sizes\n        filtered = filtered.filter(p => p.sizes && p.sizes.some(size => size.size === this.selectedSize));\n      }\n      // Apply sorting\n      switch (this.sortBy) {\n        case 'price-low':\n          filtered.sort((a, b) => a.price - b.price);\n          break;\n        case 'price-high':\n          filtered.sort((a, b) => b.price - a.price);\n          break;\n        case 'rating':\n          filtered.sort((a, b) => (b.rating?.average || 0) - (a.rating?.average || 0));\n          break;\n        case 'newest':\n          // Sort by creation date (newest first)\n          filtered.sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime());\n          break;\n        default:\n          // Featured - keep original order\n          break;\n      }\n      this.filteredProducts = filtered;\n    }\n    clearFilters() {\n      this.sortBy = 'featured';\n      this.priceRange = '';\n      this.selectedSize = '';\n      this.filteredProducts = [...this.products];\n    }\n    getProductImage(product) {\n      return product.images[0]?.url || '/assets/images/placeholder.jpg';\n    }\n    getDiscountPercentage(product) {\n      if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    getStars(rating) {\n      return Array(Math.floor(rating)).fill(0);\n    }\n    getEmptyStars(rating) {\n      return Array(5 - Math.floor(rating)).fill(0);\n    }\n    viewProduct(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    addToWishlist(product, event) {\n      event.stopPropagation();\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          console.log('Product added to wishlist:', response);\n          // You could show a toast notification here\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n        }\n      });\n    }\n    quickView(product, event) {\n      event.stopPropagation();\n      // Navigate to product detail page\n      this.router.navigate(['/product', product._id]);\n    }\n    addToCart(product, event) {\n      event.stopPropagation();\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/login']);\n        return;\n      }\n      this.cartService.addToCart(product._id, 1).subscribe({\n        next: response => {\n          console.log('Product added to cart:', response);\n          alert('Product added to cart successfully!');\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n        }\n      });\n    }\n    goHome() {\n      this.router.navigate(['/home']);\n    }\n    static {\n      this.ɵfac = function CategoryComponent_Factory(t) {\n        return new (t || CategoryComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CartService), i0.ɵɵdirectiveInject(i5.WishlistService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CategoryComponent,\n        selectors: [[\"app-category\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 50,\n        vars: 10,\n        consts: [[1, \"category-page\"], [1, \"category-header\"], [1, \"breadcrumb\"], [3, \"click\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"current\"], [1, \"category-description\"], [1, \"filters-section\"], [1, \"filter-row\"], [1, \"filter-group\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"featured\"], [\"value\", \"price-low\"], [\"value\", \"price-high\"], [\"value\", \"rating\"], [\"value\", \"newest\"], [\"value\", \"\"], [\"value\", \"0-1000\"], [\"value\", \"1000-3000\"], [\"value\", \"3000-5000\"], [\"value\", \"5000-10000\"], [\"value\", \"10000+\"], [\"class\", \"filter-group\", 4, \"ngIf\"], [1, \"results-count\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"value\", \"XS\"], [\"value\", \"S\"], [\"value\", \"M\"], [\"value\", \"L\"], [\"value\", \"XL\"], [\"value\", \"XXL\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"btn-quick-view\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"btn-add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"discount-badge\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"far fa-star\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"fas\", \"fa-star\"], [1, \"far\", \"fa-star\"], [1, \"original-price\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"fas\", \"fa-search\"], [1, \"btn-primary\", 3, \"click\"], [1, \"loading-state\"], [1, \"loading-spinner\"]],\n        template: function CategoryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n            i0.ɵɵlistener(\"click\", function CategoryComponent_Template_span_click_3_listener() {\n              return ctx.goHome();\n            });\n            i0.ɵɵtext(4, \"Home\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"i\", 4);\n            i0.ɵɵelementStart(6, \"span\", 5);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"h1\");\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"p\", 6);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 8)(14, \"div\", 9)(15, \"label\");\n            i0.ɵɵtext(16, \"Sort by:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"select\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryComponent_Template_select_ngModelChange_17_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.sortBy, $event) || (ctx.sortBy = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"change\", function CategoryComponent_Template_select_change_17_listener() {\n              return ctx.onSortChange();\n            });\n            i0.ɵɵelementStart(18, \"option\", 11);\n            i0.ɵɵtext(19, \"Featured\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"option\", 12);\n            i0.ɵɵtext(21, \"Price: Low to High\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"option\", 13);\n            i0.ɵɵtext(23, \"Price: High to Low\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"option\", 14);\n            i0.ɵɵtext(25, \"Customer Rating\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"option\", 15);\n            i0.ɵɵtext(27, \"Newest First\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"div\", 9)(29, \"label\");\n            i0.ɵɵtext(30, \"Price Range:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"select\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryComponent_Template_select_ngModelChange_31_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.priceRange, $event) || (ctx.priceRange = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"change\", function CategoryComponent_Template_select_change_31_listener() {\n              return ctx.onFilterChange();\n            });\n            i0.ɵɵelementStart(32, \"option\", 16);\n            i0.ɵɵtext(33, \"All Prices\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"option\", 17);\n            i0.ɵɵtext(35, \"Under \\u20B91,000\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"option\", 18);\n            i0.ɵɵtext(37, \"\\u20B91,000 - \\u20B93,000\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"option\", 19);\n            i0.ɵɵtext(39, \"\\u20B93,000 - \\u20B95,000\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"option\", 20);\n            i0.ɵɵtext(41, \"\\u20B95,000 - \\u20B910,000\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"option\", 21);\n            i0.ɵɵtext(43, \"Above \\u20B910,000\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(44, CategoryComponent_div_44_Template, 18, 1, \"div\", 22);\n            i0.ɵɵelementStart(45, \"div\", 23);\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(47, CategoryComponent_div_47_Template, 2, 1, \"div\", 24)(48, CategoryComponent_div_48_Template, 9, 0, \"div\", 25)(49, CategoryComponent_div_49_Template, 4, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.getCategoryDisplayName());\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.getCategoryDisplayName());\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.getCategoryDescription());\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.sortBy);\n            i0.ɵɵadvance(14);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.priceRange);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.category === \"women\" || ctx.category === \"men\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.filteredProducts.length, \" products found \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.filteredProducts.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.filteredProducts.length === 0 && !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, i6.DecimalPipe, FormsModule, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.SelectControlValueAccessor, i7.NgControlStatus, i7.NgModel],\n        styles: [\".category-page[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.category-header[_ngcontent-%COMP%]{margin-bottom:30px}.breadcrumb[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-size:.9rem;color:#666}.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{cursor:pointer}.breadcrumb[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover{color:#007bff}.breadcrumb[_ngcontent-%COMP%]   .current[_ngcontent-%COMP%]{color:#333;font-weight:500}.category-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:8px;color:#333}.category-description[_ngcontent-%COMP%]{font-size:1.1rem;color:#666;margin:0}.filters-section[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:20px;margin-bottom:30px;box-shadow:0 2px 8px #0000001a}.filter-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:24px;flex-wrap:wrap}.filter-group[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;color:#333;white-space:nowrap}.filter-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:8px 12px;border:1px solid #ddd;border-radius:6px;font-size:.9rem;min-width:150px}.results-count[_ngcontent-%COMP%]{margin-left:auto;font-weight:500;color:#666}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:24px}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000001a;transition:all .3s ease;cursor:pointer}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.product-image[_ngcontent-%COMP%]{position:relative;height:300px;overflow:hidden}.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.05)}.product-actions[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;display:flex;flex-direction:column;gap:8px;opacity:0;transition:opacity .3s ease}.product-card[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%]{opacity:1}.btn-wishlist[_ngcontent-%COMP%], .btn-quick-view[_ngcontent-%COMP%]{width:40px;height:40px;border:none;border-radius:50%;background:#ffffffe6;color:#333;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.btn-wishlist[_ngcontent-%COMP%]:hover, .btn-quick-view[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:#ff4757;color:#fff;padding:4px 8px;border-radius:12px;font-size:.75rem;font-weight:600}.product-info[_ngcontent-%COMP%]{padding:20px}.product-name[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:4px;color:#333;line-height:1.3}.product-brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:8px}.product-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.stars[_ngcontent-%COMP%]{display:flex;gap:2px}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem;color:#ffc107}.rating-count[_ngcontent-%COMP%]{font-size:.8rem;color:#666}.product-price[_ngcontent-%COMP%]{margin-bottom:16px}.current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#333}.original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#999;text-decoration:line-through;margin-left:8px}.btn-add-cart[_ngcontent-%COMP%]{width:100%;padding:12px;background:#007bff;color:#fff;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:background .2s ease;display:flex;align-items:center;justify-content:center;gap:8px}.btn-add-cart[_ngcontent-%COMP%]:hover{background:#0056b3}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:80px 20px}.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#ddd;margin-bottom:20px}.empty-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:10px}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:30px}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff;padding:12px 24px;border:none;border-radius:6px;font-weight:500;cursor:pointer}.loading-state[_ngcontent-%COMP%]{text-align:center;padding:80px 20px}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 20px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.category-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.filter-row[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:16px}.filter-group[_ngcontent-%COMP%]{justify-content:space-between}.results-count[_ngcontent-%COMP%]{margin-left:0;text-align:center}.products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:16px}}\"]\n      });\n    }\n  }\n  return CategoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}