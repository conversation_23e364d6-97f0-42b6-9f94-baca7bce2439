import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export interface BillData {
  orderId: string;
  paymentId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddress: string;
  items: BillItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  paymentMethod: string;
  orderDate: Date;
  billNumber: string;
}

export interface BillItem {
  name: string;
  description: string;
  quantity: number;
  price: number;
  total: number;
  image?: string;
}

@Injectable({
  providedIn: 'root'
})
export class BillService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  generateBillPDF(billData: BillData): jsPDF {
    const doc = new jsPDF();
    
    // Company Header
    doc.setFontSize(24);
    doc.setTextColor(102, 126, 234); // DFashion brand color
    doc.text('DFashion', 20, 30);
    
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text('Fashion & Lifestyle Store', 20, 40);
    doc.text('Email: <EMAIL>', 20, 50);
    doc.text('Phone: +91 9876543210', 20, 60);
    
    // Bill Title
    doc.setFontSize(18);
    doc.setTextColor(102, 126, 234);
    doc.text('INVOICE', 150, 30);
    
    // Bill Details
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text(`Bill No: ${billData.billNumber}`, 150, 45);
    doc.text(`Order ID: ${billData.orderId}`, 150, 55);
    doc.text(`Payment ID: ${billData.paymentId}`, 150, 65);
    doc.text(`Date: ${new Date(billData.orderDate).toLocaleDateString()}`, 150, 75);
    
    // Customer Details
    doc.setFontSize(14);
    doc.setTextColor(102, 126, 234);
    doc.text('Bill To:', 20, 85);
    
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text(billData.customerName, 20, 95);
    doc.text(billData.customerEmail, 20, 105);
    doc.text(billData.customerPhone, 20, 115);
    
    // Address (split into multiple lines if needed)
    const addressLines = this.splitText(billData.customerAddress, 50);
    let addressY = 125;
    addressLines.forEach(line => {
      doc.text(line, 20, addressY);
      addressY += 10;
    });
    
    // Items Table
    const tableStartY = Math.max(addressY + 10, 140);
    
    const tableColumns = ['Item', 'Qty', 'Price', 'Total'];
    const tableRows = billData.items.map(item => [
      item.name,
      item.quantity.toString(),
      `₹${item.price.toFixed(2)}`,
      `₹${item.total.toFixed(2)}`
    ]);
    
    doc.autoTable({
      head: [tableColumns],
      body: tableRows,
      startY: tableStartY,
      theme: 'grid',
      headStyles: {
        fillColor: [102, 126, 234],
        textColor: [255, 255, 255],
        fontSize: 10
      },
      bodyStyles: {
        fontSize: 9
      },
      columnStyles: {
        0: { cellWidth: 80 },
        1: { cellWidth: 20, halign: 'center' },
        2: { cellWidth: 30, halign: 'right' },
        3: { cellWidth: 30, halign: 'right' }
      }
    });
    
    // Calculate totals section Y position
    const finalY = (doc as any).lastAutoTable.finalY + 20;
    
    // Totals Section
    const totalsX = 130;
    doc.setFontSize(10);
    
    doc.text('Subtotal:', totalsX, finalY);
    doc.text(`₹${billData.subtotal.toFixed(2)}`, totalsX + 40, finalY);
    
    if (billData.discount > 0) {
      doc.text('Discount:', totalsX, finalY + 10);
      doc.text(`-₹${billData.discount.toFixed(2)}`, totalsX + 40, finalY + 10);
    }
    
    doc.text('Shipping:', totalsX, finalY + 20);
    doc.text(`₹${billData.shipping.toFixed(2)}`, totalsX + 40, finalY + 20);
    
    doc.text('Tax (GST):', totalsX, finalY + 30);
    doc.text(`₹${billData.tax.toFixed(2)}`, totalsX + 40, finalY + 30);
    
    // Total
    doc.setFontSize(12);
    doc.setTextColor(102, 126, 234);
    doc.text('Total:', totalsX, finalY + 45);
    doc.text(`₹${billData.total.toFixed(2)}`, totalsX + 40, finalY + 45);
    
    // Payment Method
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text(`Payment Method: ${billData.paymentMethod}`, 20, finalY + 60);
    
    // Footer
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text('Thank you for shopping with DFashion!', 20, finalY + 80);
    doc.text('For any queries, contact <NAME_EMAIL>', 20, finalY + 90);
    
    return doc;
  }
  
  private splitText(text: string, maxLength: number): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';
    
    words.forEach(word => {
      if ((currentLine + word).length <= maxLength) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
      }
    });
    
    if (currentLine) lines.push(currentLine);
    return lines;
  }

  downloadBill(billData: BillData): void {
    const doc = this.generateBillPDF(billData);
    doc.save(`DFashion_Invoice_${billData.billNumber}.pdf`);
  }

  generateBillBlob(billData: BillData): Blob {
    const doc = this.generateBillPDF(billData);
    return doc.output('blob');
  }

  sendBillEmail(billData: BillData, message?: string): Observable<any> {
    const formData = new FormData();
    
    // Generate PDF blob
    const pdfBlob = this.generateBillBlob(billData);
    formData.append('billpdf', pdfBlob, `DFashion_Invoice_${billData.billNumber}.pdf`);
    
    // Add other data
    formData.append('userId', billData.orderId); // Using orderId as userId for now
    formData.append('name', billData.customerName);
    formData.append('email', billData.customerEmail);
    formData.append('message', message || 'Thank you for your purchase! Please find your invoice attached.');
    formData.append('paymentType', JSON.stringify({
      method: billData.paymentMethod,
      paymentId: billData.paymentId,
      orderId: billData.orderId
    }));
    
    return this.http.post(`${this.apiUrl}/email/send-bill`, formData);
  }

  generateBillNumber(): string {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `DF${year}${month}${day}${random}`;
  }

  calculateTotals(items: BillItem[], shippingCost: number = 0, discountAmount: number = 0, taxRate: number = 0.18): any {
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const discountedSubtotal = subtotal - discountAmount;
    const tax = discountedSubtotal * taxRate;
    const total = discountedSubtotal + tax + shippingCost;
    
    return {
      subtotal,
      tax,
      shipping: shippingCost,
      discount: discountAmount,
      total
    };
  }
}
