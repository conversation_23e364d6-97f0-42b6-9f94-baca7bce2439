{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { Subject } from 'rxjs';\nimport { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\nimport { ProductDialogComponent } from './product-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/product.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/table\";\nimport * as i10 from \"@angular/material/paginator\";\nimport * as i11 from \"@angular/material/sort\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/tooltip\";\nconst _c0 = () => [5, 10, 25, 50];\nfunction ProductManagementComponent_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r1.label, \" \");\n  }\n}\nfunction ProductManagementComponent_mat_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r2.label, \" \");\n  }\n}\nfunction ProductManagementComponent_th_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Product\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"img\", 26);\n    i0.ɵɵelementStart(3, \"div\", 27)(4, \"div\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.getProductImage(product_r3), i0.ɵɵsanitizeUrl)(\"alt\", product_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r3.brand);\n  }\n}\nfunction ProductManagementComponent_th_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"Category\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"span\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementStart(5, \"small\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, product_r5.category));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, product_r5.subcategory));\n  }\n}\nfunction ProductManagementComponent_th_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"Price\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_39_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r6.originalPrice, \"\");\n  }\n}\nfunction ProductManagementComponent_td_39_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", product_r6.discount, \"% OFF\");\n  }\n}\nfunction ProductManagementComponent_td_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 33)(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProductManagementComponent_td_39_span_4_Template, 2, 1, \"span\", 35)(5, ProductManagementComponent_td_39_span_5_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r6.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.discount > 0);\n  }\n}\nfunction ProductManagementComponent_th_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"div\", 40);\n    i0.ɵɵelementStart(3, \"span\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r3.getStatusColor(product_r7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getStatusText(product_r7));\n  }\n}\nfunction ProductManagementComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductManagementComponent_td_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 42)(2, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ProductManagementComponent_td_45_Template_button_click_2_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openProductDialog(product_r9));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProductManagementComponent_td_45_Template_button_click_5_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleProductStatus(product_r9));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProductManagementComponent_td_45_Template_button_click_8_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.deleteProduct(product_r9));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matTooltip\", product_r9.isActive ? \"Deactivate product\" : \"Activate product\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r9.isActive ? \"visibility_off\" : \"visibility\");\n  }\n}\nfunction ProductManagementComponent_tr_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 46);\n  }\n}\nfunction ProductManagementComponent_tr_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 47);\n  }\n}\nfunction ProductManagementComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"inventory_2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No products found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or add a new product.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ProductManagementComponent = /*#__PURE__*/(() => {\n  class ProductManagementComponent {\n    constructor(productService, dialog, snackBar) {\n      this.productService = productService;\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n      this.destroy$ = new Subject();\n      this.displayedColumns = ['product', 'category', 'price', 'status', 'actions'];\n      this.dataSource = new MatTableDataSource([]);\n      this.isLoading = false;\n      this.totalProducts = 0;\n      // Filters\n      this.searchControl = new FormControl('');\n      this.categoryFilter = new FormControl('');\n      this.statusFilter = new FormControl('');\n      this.categories = [{\n        value: '',\n        label: 'All Categories'\n      }, {\n        value: 'men',\n        label: 'Men'\n      }, {\n        value: 'women',\n        label: 'Women'\n      }, {\n        value: 'children',\n        label: 'Children'\n      }];\n      this.statuses = [{\n        value: '',\n        label: 'All Statuses'\n      }, {\n        value: 'true',\n        label: 'Active'\n      }, {\n        value: 'false',\n        label: 'Inactive'\n      }];\n    }\n    ngOnInit() {\n      this.setupFilters();\n      this.loadProducts();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    setupFilters() {\n      this.searchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {\n        this.loadProducts();\n      });\n      [this.categoryFilter, this.statusFilter].forEach(control => {\n        control.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n          this.loadProducts();\n        });\n      });\n    }\n    loadProducts() {\n      this.isLoading = true;\n      // Load from API - empty for now\n      this.dataSource.data = [];\n      this.totalProducts = 0;\n      this.isLoading = false;\n    }\n    onPageChange() {\n      this.loadProducts();\n    }\n    openProductDialog(product) {\n      const dialogRef = this.dialog.open(ProductDialogComponent, {\n        width: '800px',\n        data: product ? {\n          ...product\n        } : null\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result) {\n          this.loadProducts();\n        }\n      });\n    }\n    toggleProductStatus(product) {\n      this.snackBar.open('Product status updated', 'Close', {\n        duration: 3000\n      });\n    }\n    deleteProduct(product) {\n      if (confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n        this.snackBar.open('Product deleted', 'Close', {\n          duration: 3000\n        });\n        this.loadProducts();\n      }\n    }\n    getProductImage(product) {\n      return product.images?.[0]?.url || '/assets/images/placeholder-product.jpg';\n    }\n    getStatusColor(product) {\n      return product.isActive ? '#4caf50' : '#f44336';\n    }\n    getStatusText(product) {\n      return product.isActive ? 'Active' : 'Inactive';\n    }\n    static {\n      this.ɵfac = function ProductManagementComponent_Factory(t) {\n        return new (t || ProductManagementComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProductManagementComponent,\n        selectors: [[\"app-product-management\"]],\n        viewQuery: function ProductManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 50,\n        vars: 13,\n        consts: [[1, \"product-management\"], [1, \"filters-section\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Search by name, brand, or category\", 3, \"formControl\"], [\"matSuffix\", \"\"], [3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"product\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"category\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"price\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"page\", \"length\", \"pageSize\", \"pageSizeOptions\"], [3, \"value\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"product-cell\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [1, \"category-text\"], [1, \"subcategory-text\"], [1, \"price-cell\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"discount\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"discount\"], [1, \"status-indicator\"], [1, \"status-dot\"], [1, \"status-text\"], [1, \"actions-cell\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Edit product\", 3, \"click\"], [\"mat-icon-button\", \"\", 3, \"click\", \"matTooltip\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete product\", \"color\", \"warn\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"]],\n        template: function ProductManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4, \"Product Management\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n            i0.ɵɵtext(6, \"Manage products, inventory, and pricing\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 1)(9, \"mat-form-field\", 2)(10, \"mat-label\");\n            i0.ɵɵtext(11, \"Search products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(12, \"input\", 3);\n            i0.ɵɵelementStart(13, \"mat-icon\", 4);\n            i0.ɵɵtext(14, \"search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"mat-form-field\", 2)(16, \"mat-label\");\n            i0.ɵɵtext(17, \"Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"mat-select\", 5);\n            i0.ɵɵtemplate(19, ProductManagementComponent_mat_option_19_Template, 2, 2, \"mat-option\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"mat-form-field\", 2)(21, \"mat-label\");\n            i0.ɵɵtext(22, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"mat-select\", 5);\n            i0.ɵɵtemplate(24, ProductManagementComponent_mat_option_24_Template, 2, 2, \"mat-option\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function ProductManagementComponent_Template_button_click_25_listener() {\n              return ctx.openProductDialog();\n            });\n            i0.ɵɵelementStart(26, \"mat-icon\");\n            i0.ɵɵtext(27, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(28, \" Add Product \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 8)(30, \"table\", 9);\n            i0.ɵɵelementContainerStart(31, 10);\n            i0.ɵɵtemplate(32, ProductManagementComponent_th_32_Template, 2, 0, \"th\", 11)(33, ProductManagementComponent_td_33_Template, 8, 4, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(34, 13);\n            i0.ɵɵtemplate(35, ProductManagementComponent_th_35_Template, 2, 0, \"th\", 14)(36, ProductManagementComponent_td_36_Template, 8, 6, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(37, 15);\n            i0.ɵɵtemplate(38, ProductManagementComponent_th_38_Template, 2, 0, \"th\", 14)(39, ProductManagementComponent_td_39_Template, 6, 3, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(40, 16);\n            i0.ɵɵtemplate(41, ProductManagementComponent_th_41_Template, 2, 0, \"th\", 11)(42, ProductManagementComponent_td_42_Template, 5, 3, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(43, 17);\n            i0.ɵɵtemplate(44, ProductManagementComponent_th_44_Template, 2, 0, \"th\", 11)(45, ProductManagementComponent_td_45_Template, 11, 2, \"td\", 12);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(46, ProductManagementComponent_tr_46_Template, 1, 0, \"tr\", 18)(47, ProductManagementComponent_tr_47_Template, 1, 0, \"tr\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(48, ProductManagementComponent_div_48_Template, 7, 0, \"div\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"mat-paginator\", 21);\n            i0.ɵɵlistener(\"page\", function ProductManagementComponent_Template_mat_paginator_page_49_listener() {\n              return ctx.onPageChange();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"formControl\", ctx.searchControl);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formControl\", ctx.categoryFilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"formControl\", ctx.statusFilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.statuses);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.dataSource.data.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"length\", ctx.totalProducts)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(12, _c0));\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlDirective, i6.MatIcon, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatTable, i9.MatHeaderCellDef, i9.MatHeaderRowDef, i9.MatColumnDef, i9.MatCellDef, i9.MatRowDef, i9.MatHeaderCell, i9.MatCell, i9.MatHeaderRow, i9.MatRow, i10.MatPaginator, i11.MatSort, i11.MatSortHeader, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatSuffix, i14.MatSelect, i15.MatOption, i16.MatTooltip, i4.TitleCasePipe],\n        styles: [\".product-management[_ngcontent-%COMP%]{padding:1rem}.filters-section[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1rem;flex-wrap:wrap;align-items:center}.filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{min-width:200px}.filters-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content;margin-top:.5rem}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin-bottom:1rem}.product-cell[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.product-cell[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:50px;height:50px;object-fit:cover;border-radius:4px;border:1px solid #e0e0e0}.product-cell[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.25rem}.product-cell[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{font-size:.875rem;color:#666}.category-text[_ngcontent-%COMP%]{font-weight:500}.subcategory-text[_ngcontent-%COMP%]{color:#666}.price-cell[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-weight:600;color:#2e7d32}.price-cell[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{text-decoration:line-through;color:#999;margin-left:.5rem;font-size:.875rem}.price-cell[_ngcontent-%COMP%]   .discount[_ngcontent-%COMP%]{background:#ff5722;color:#fff;padding:.125rem .375rem;border-radius:4px;font-size:.75rem;margin-left:.5rem}.status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.status-indicator[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%}.status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-size:.875rem}.actions-cell[_ngcontent-%COMP%]{display:flex;gap:.25rem}.no-data[_ngcontent-%COMP%]{text-align:center;padding:3rem;color:#666}.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:1rem;opacity:.5}.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:1rem 0 .5rem;font-weight:400}.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.875rem}@media (max-width: 768px){.filters-section[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{min-width:auto;width:100%}.product-cell[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.product-cell[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:40px;height:40px}}\"]\n      });\n    }\n  }\n  return ProductManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}