{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';\nimport { ErrorDisplayComponent } from '../../../../shared/components/error-display/error-display.component';\nimport { FeaturedBrandsComponent } from '../../../../shared/components/featured-brands/featured-brands.component';\nimport { TrendingNowComponent } from '../../../../shared/components/trending-now/trending-now.component';\nimport { NewArrivalsComponent } from '../../../../shared/components/new-arrivals/new-arrivals.component';\nimport { QuickLinksComponent } from '../../../../shared/components/quick-links/quick-links.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"../../../../core/services/shop-data.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common/http\";\nimport * as i8 from \"../../../../core/services/error-handler.service\";\nimport * as i9 from \"../../../../core/services/loading.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nconst _c0 = () => [\"Men's Clothing\", \"Women's Fashion\", \"Footwear\", \"Accessories\"];\nfunction ShopComponent_app_loading_spinner_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-loading-spinner\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showGlobalLoading\", true);\n  }\n}\nfunction ShopComponent_div_3_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.searchQuery = \"\";\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.filterType = \"trending\";\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelement(2, \"i\", 26);\n    i0.ɵɵtext(3, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_17_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.filterType = \"new\";\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelement(5, \"i\", 27);\n    i0.ɵɵtext(6, \" New Arrivals \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_17_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.filterType = \"sale\";\n      return i0.ɵɵresetView(ctx_r1.onFilterChange());\n    });\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵtext(9, \" On Sale \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopComponent_div_3_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 32);\n    i0.ɵɵtext(5, \"Products\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 30)(7, \"span\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 32);\n    i0.ɵɵtext(10, \"Brands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 30)(12, \"span\", 31);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 32);\n    i0.ɵɵtext(15, \"Happy Customers\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.shopStats.totalProducts), \"+\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.shopStats.totalBrands), \"+\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.shopStats.totalUsers), \"+\");\n  }\n}\nfunction ShopComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"app-quick-links\", 34)(2, \"app-featured-brands\", 35)(3, \"app-trending-now\", 36)(4, \"app-new-arrivals\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showHeader\", true)(\"maxLinks\", 8)(\"showPopularCategories\", true)(\"showQuickActions\", true)(\"showFeaturedCollections\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"maxBrands\", 6)(\"showHeader\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"maxProducts\", 12)(\"showHeader\", true)(\"showScrollButtons\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"maxProducts\", 8)(\"showHeader\", true)(\"showLoadMore\", true);\n  }\n}\nfunction ShopComponent_div_3_div_20_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Search Results for \\\"\", ctx_r1.searchQuery, \"\\\"\");\n  }\n}\nfunction ShopComponent_div_3_div_20_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, ctx_r1.selectedCategory), \" Products\");\n  }\n}\nfunction ShopComponent_div_3_div_20_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, ctx_r1.filterType), \" Products\");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r7), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_div_12_div_1_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.navigateToProduct(product_r7._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 50);\n    i0.ɵɵelement(2, \"img\", 51);\n    i0.ɵɵtemplate(3, ShopComponent_div_3_div_20_div_12_div_1_div_3_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 53)(5, \"h3\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 55);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 56)(10, \"span\", 57);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ShopComponent_div_3_div_20_div_12_div_1_span_12_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r7), i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r7) > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice && product_r7.originalPrice > product_r7.price);\n  }\n}\nfunction ShopComponent_div_3_div_20_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, ShopComponent_div_3_div_20_div_12_div_1_Template, 13, 7, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredProducts);\n  }\n}\nfunction ShopComponent_div_3_div_20_app_no_data_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 61);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showActions\", true)(\"suggestions\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction ShopComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"h2\", 40);\n    i0.ɵɵtemplate(3, ShopComponent_div_3_div_20_span_3_Template, 2, 1, \"span\", 41)(4, ShopComponent_div_3_div_20_span_4_Template, 3, 3, \"span\", 41)(5, ShopComponent_div_3_div_20_span_5_Template, 3, 3, \"span\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 42)(7, \"span\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_div_20_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearFilters());\n    });\n    i0.ɵɵelement(10, \"i\", 23);\n    i0.ɵɵtext(11, \" Clear Filters \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, ShopComponent_div_3_div_20_div_12_Template, 2, 1, \"div\", 45)(13, ShopComponent_div_3_div_20_app_no_data_13_Template, 1, 3, \"app-no-data\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchQuery && ctx_r1.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchQuery && !ctx_r1.selectedCategory && ctx_r1.filterType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.filteredProducts.length, \" products found\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredProducts.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredProducts.length === 0 && !ctx_r1.isLoading);\n  }\n}\nfunction ShopComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Discover Fashion That Defines You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 9);\n    i0.ɵɵtext(6, \"Explore thousands of products from top brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"div\", 11)(9, \"div\", 12);\n    i0.ɵɵelement(10, \"i\", 13);\n    i0.ɵɵelementStart(11, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ShopComponent_div_3_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function ShopComponent_div_3_Template_input_keyup_enter_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ShopComponent_div_3_button_12_Template, 2, 0, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_3_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelement(14, \"i\", 17);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Search\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, ShopComponent_div_3_div_17_Template, 10, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ShopComponent_div_3_div_18_Template, 16, 3, \"div\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ShopComponent_div_3_div_19_Template, 5, 13, \"div\", 20)(20, ShopComponent_div_3_div_20_Template, 14, 6, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shopStats);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showMainSections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showMainSections);\n  }\n}\nexport let ShopComponent = /*#__PURE__*/(() => {\n  class ShopComponent {\n    constructor(productService, authService, cartService, wishlistService, shopDataService, router, route, http, errorHandlerService, loadingService) {\n      this.productService = productService;\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.shopDataService = shopDataService;\n      this.router = router;\n      this.route = route;\n      this.http = http;\n      this.errorHandlerService = errorHandlerService;\n      this.loadingService = loadingService;\n      // Main shop data\n      this.searchQuery = '';\n      this.isLoading = true;\n      this.showMainSections = true;\n      this.shopStats = null;\n      // Enhanced filtering and search\n      this.selectedCategory = '';\n      this.selectedSubcategory = '';\n      this.selectedBrand = '';\n      this.filterType = ''; // 'suggested', 'trending', etc.\n      this.sortBy = 'featured';\n      this.priceRange = {\n        min: 0,\n        max: 10000\n      };\n      // Pagination for filtered results\n      this.currentPage = 1;\n      this.itemsPerPage = 24;\n      this.hasMore = false;\n      // Filtered products (when searching/filtering)\n      this.allProducts = [];\n      this.filteredProducts = [];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      // Check for query parameters\n      this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.searchQuery = params['q'] || '';\n        this.selectedCategory = params['category'] || '';\n        this.filterType = params['filter'] || '';\n        this.sortBy = params['sort'] || 'featured';\n        this.loadShopData();\n      });\n      // Load shop statistics\n      this.loadShopStats();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadShopData() {\n      this.isLoading = true;\n      // If we have filters or search, load filtered products and hide main sections\n      if (this.searchQuery || this.selectedCategory || this.filterType) {\n        this.showMainSections = false;\n        this.loadProductsWithFilters();\n      } else {\n        // Show main sections and load all shop data\n        this.showMainSections = true;\n        this.shopDataService.loadAllShopData().pipe(takeUntil(this.destroy$)).subscribe({\n          next: data => {\n            console.log('✅ All shop data loaded successfully');\n            this.isLoading = false;\n          },\n          error: error => {\n            console.error('❌ Error loading shop data:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    loadShopStats() {\n      this.shopDataService.loadShopStats().pipe(takeUntil(this.destroy$)).subscribe({\n        next: stats => {\n          this.shopStats = stats;\n        },\n        error: error => {\n          console.error('Error loading shop stats:', error);\n        }\n      });\n    }\n    // Search and navigation methods\n    search() {\n      if (this.searchQuery.trim()) {\n        this.updateUrlParams();\n        this.loadShopData();\n      }\n    }\n    navigateToCategory(categorySlug) {\n      this.router.navigate(['/shop/category', categorySlug]);\n    }\n    navigateToBrand(brandSlug) {\n      this.router.navigate(['/shop/brand', brandSlug]);\n    }\n    navigateToProduct(productId) {\n      this.router.navigate(['/product', productId]);\n    }\n    // Enhanced filtering methods\n    onCategoryChange() {\n      this.selectedSubcategory = '';\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n    onFilterChange() {\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n    onSortChange() {\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n    clearFilters() {\n      this.searchQuery = '';\n      this.selectedCategory = '';\n      this.selectedSubcategory = '';\n      this.selectedBrand = '';\n      this.filterType = '';\n      this.sortBy = 'featured';\n      this.priceRange = {\n        min: 0,\n        max: 10000\n      };\n      this.updateUrlParams();\n      this.loadShopData();\n    }\n    updateUrlParams() {\n      const queryParams = {};\n      if (this.searchQuery) queryParams.q = this.searchQuery;\n      if (this.selectedCategory) queryParams.category = this.selectedCategory;\n      if (this.filterType) queryParams.filter = this.filterType;\n      if (this.sortBy !== 'featured') queryParams.sort = this.sortBy;\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams,\n        queryParamsHandling: 'merge'\n      });\n    }\n    // Enhanced product loading with filtering\n    loadProductsWithFilters() {\n      this.isLoading = true;\n      const filters = {\n        category: this.selectedCategory,\n        subcategory: this.selectedSubcategory,\n        brand: this.selectedBrand,\n        sortBy: this.sortBy,\n        page: this.currentPage,\n        limit: this.itemsPerPage\n      };\n      this.shopDataService.searchProducts(this.searchQuery, filters).pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          this.allProducts = response.products || [];\n          this.filteredProducts = this.allProducts;\n          this.hasMore = response.totalCount > this.currentPage * this.itemsPerPage;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading filtered products:', error);\n          this.allProducts = [];\n          this.filteredProducts = [];\n          this.isLoading = false;\n        }\n      });\n    }\n    // Utility methods\n    getProductImage(product) {\n      return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n    }\n    getDiscountPercentage(product) {\n      if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    formatNumber(num) {\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    static {\n      this.ɵfac = function ShopComponent_Factory(t) {\n        return new (t || ShopComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.ShopDataService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.HttpClient), i0.ɵɵdirectiveInject(i8.ErrorHandlerService), i0.ɵɵdirectiveInject(i9.LoadingService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ShopComponent,\n        selectors: [[\"app-shop\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 4,\n        vars: 3,\n        consts: [[1, \"shop-container\"], [3, \"showGlobalErrors\"], [\"message\", \"Loading shop data...\", \"webSpinnerType\", \"pulse\", 3, \"showGlobalLoading\", 4, \"ngIf\"], [\"class\", \"shop-content\", 4, \"ngIf\"], [\"message\", \"Loading shop data...\", \"webSpinnerType\", \"pulse\", 3, \"showGlobalLoading\"], [1, \"shop-content\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"search-container\"], [1, \"search-bar\"], [1, \"search-input-wrapper\"], [1, \"fas\", \"fa-search\", \"search-icon\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, categories...\", 1, \"search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"class\", \"clear-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"quick-filters\", 4, \"ngIf\"], [\"class\", \"shop-stats\", 4, \"ngIf\"], [\"class\", \"main-sections\", 4, \"ngIf\"], [\"class\", \"filtered-results\", 4, \"ngIf\"], [1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"quick-filters\"], [1, \"filter-chip\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [1, \"fas\", \"fa-sparkles\"], [1, \"fas\", \"fa-tags\"], [1, \"shop-stats\"], [1, \"stat-item\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"main-sections\"], [3, \"showHeader\", \"maxLinks\", \"showPopularCategories\", \"showQuickActions\", \"showFeaturedCollections\"], [3, \"maxBrands\", \"showHeader\"], [3, \"maxProducts\", \"showHeader\", \"showScrollButtons\"], [3, \"maxProducts\", \"showHeader\", \"showLoadMore\"], [1, \"filtered-results\"], [1, \"results-header\"], [1, \"results-title\"], [4, \"ngIf\"], [1, \"results-meta\"], [1, \"results-count\"], [1, \"clear-filters-btn\", 3, \"click\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"title\", \"No Products Found\", \"message\", \"Try adjusting your search or filters to find what you're looking for.\", \"iconClass\", \"fas fa-search\", \"containerClass\", \"compact\", \"primaryAction\", \"Clear Filters\", \"secondaryAction\", \"Browse All Products\", \"suggestionsTitle\", \"Popular Categories:\", 3, \"showActions\", \"suggestions\", 4, \"ngIf\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [1, \"product-image\", 3, \"src\", \"alt\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"discount-badge\"], [1, \"original-price\"], [\"title\", \"No Products Found\", \"message\", \"Try adjusting your search or filters to find what you're looking for.\", \"iconClass\", \"fas fa-search\", \"containerClass\", \"compact\", \"primaryAction\", \"Clear Filters\", \"secondaryAction\", \"Browse All Products\", \"suggestionsTitle\", \"Popular Categories:\", 3, \"showActions\", \"suggestions\"]],\n        template: function ShopComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"app-error-display\", 1);\n            i0.ɵɵtemplate(2, ShopComponent_app_loading_spinner_2_Template, 1, 1, \"app-loading-spinner\", 2)(3, ShopComponent_div_3_Template, 21, 6, \"div\", 3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"showGlobalErrors\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i10.NgForOf, i10.NgIf, i10.TitleCasePipe, FormsModule, i11.DefaultValueAccessor, i11.NgControlStatus, i11.NgModel, NoDataComponent, LoadingSpinnerComponent, ErrorDisplayComponent, FeaturedBrandsComponent, TrendingNowComponent, NewArrivalsComponent, QuickLinksComponent],\n        styles: [\".shop-container[_ngcontent-%COMP%]{max-width:1400px;margin:0 auto;padding:0;background:#f8f9fa;min-height:100vh}.shop-content[_ngcontent-%COMP%]{padding-bottom:2rem}.hero-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:4rem 2rem;text-align:center;position:relative;overflow:hidden}.hero-section[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');opacity:.3}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]{position:relative;z-index:2;max-width:800px;margin:0 auto}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]{font-size:3rem;font-weight:800;margin:0 0 1rem;line-height:1.2}@media (max-width: 768px){.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%]{font-size:2rem}}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%]{font-size:1.2rem;margin:0 0 2rem;opacity:.9}@media (max-width: 768px){.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%]{font-size:1rem}}.search-container[_ngcontent-%COMP%]{margin-bottom:2rem}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{display:flex;gap:1rem;max-width:700px;margin:0 auto 1.5rem}@media (max-width: 768px){.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{flex-direction:column;gap:.75rem}}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]{flex:1;position:relative;background:#fffffff2;border-radius:25px;box-shadow:0 4px 20px #0000001a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#8e8e8e;font-size:1rem}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;border:none;outline:none;background:transparent;font-size:1rem;color:#262626}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder{color:#8e8e8e}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]{position:absolute;right:1rem;top:50%;transform:translateY(-50%);background:none;border:none;color:#8e8e8e;cursor:pointer;width:24px;height:24px;border-radius:50%;display:flex;align-items:center;justify-content:center}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input-wrapper[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]:hover{background:#0000001a}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]{padding:1rem 2rem;background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);border-radius:25px;cursor:pointer;transition:all .3s ease;font-weight:600;display:flex;align-items:center;gap:.5rem;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80;transform:translateY(-2px)}@media (max-width: 768px){.search-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]{justify-content:center}}.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:1rem;flex-wrap:wrap}.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;background:#fff3;color:#fff;border:1px solid rgba(255,255,255,.3);border-radius:20px;cursor:pointer;transition:all .3s ease;font-size:.85rem;font-weight:500;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80;transform:translateY(-2px)}.search-container[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.shop-stats[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:3rem;margin-top:2rem}@media (max-width: 768px){.shop-stats[_ngcontent-%COMP%]{gap:2rem}}@media (max-width: 480px){.shop-stats[_ngcontent-%COMP%]{gap:1rem}}.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{text-align:center}.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{display:block;font-size:2rem;font-weight:800;line-height:1;margin-bottom:.25rem}@media (max-width: 768px){.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:1.5rem}}.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.9rem;opacity:.8;text-transform:uppercase;letter-spacing:.5px}@media (max-width: 768px){.shop-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.8rem}}.main-sections[_ngcontent-%COMP%]{background:#f8f9fa}.filtered-results[_ngcontent-%COMP%]{padding:2rem;background:#fff;margin:2rem;border-radius:16px;box-shadow:0 2px 12px #0000000f}.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem;padding-bottom:1rem;border-bottom:1px solid #e9ecef}@media (max-width: 768px){.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;align-items:flex-start}}.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#262626;margin:0}.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%]   .results-count[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.9rem}.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;background:#f8f9fa;color:#666;border:1px solid #dee2e6;border-radius:20px;cursor:pointer;transition:all .3s ease;font-size:.85rem}.filtered-results[_ngcontent-%COMP%]   .results-header[_ngcontent-%COMP%]   .results-meta[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1.5rem}@media (max-width: 768px){.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000000f;cursor:pointer;transition:all .3s ease}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #00000026}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]{position:relative;height:250px;overflow:hidden}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#e91e63;color:#fff;padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:600}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{padding:1rem}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#262626;margin:0 0 .25rem;line-height:1.3}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.85rem;margin:0 0 .5rem}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700;color:#262626}.filtered-results[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#8e8e8e;text-decoration:line-through;margin-left:.5rem}@media (prefers-color-scheme: dark){.shop-container[_ngcontent-%COMP%]{background:#121212}.hero-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1a1a2e,#16213e)}.filtered-results[_ngcontent-%COMP%]{background:#1e1e1e}.filtered-results[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%]{color:#fff}.filtered-results[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{background:#2a2a2a}.filtered-results[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{color:#fff}.filtered-results[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]{background:#2a2a2a;color:#fff;border-color:#333}.filtered-results[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover{background:#333}}\"]\n      });\n    }\n  }\n  return ShopComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}