import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-create-post',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  template: `
    <div class="create-post-container">
      <div class="header">
        <h1>Create New Post</h1>
        <p>Share your products with the community</p>
      </div>

      <form [formGroup]="postForm" (ngSubmit)="onSubmit()" class="post-form">
        <!-- Media Upload -->
        <div class="form-section">
          <h3>Media</h3>
          <div class="media-upload">
            <div class="upload-area" (click)="fileInput.click()" [class.has-files]="selectedFiles.length > 0">
              <input #fileInput type="file" multiple accept="image/*,video/*" (change)="onFileSelect($event)" style="display: none;">
              
              <div class="upload-content" *ngIf="selectedFiles.length === 0">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>Click to upload images or videos</p>
                <span>Support: JPG, PNG, MP4, MOV</span>
              </div>

              <div class="file-preview" *ngIf="selectedFiles.length > 0">
                <div class="file-item" *ngFor="let file of selectedFiles; let i = index">
                  <img *ngIf="file.type.startsWith('image')" [src]="file.preview" [alt]="file.name">
                  <video *ngIf="file.type.startsWith('video')" [src]="file.preview" controls></video>
                  <button type="button" class="remove-file" (click)="removeFile(i)">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Caption -->
        <div class="form-section">
          <h3>Caption</h3>
          <textarea 
            formControlName="caption" 
            placeholder="Write a caption for your post..."
            rows="4"
            maxlength="2000"
          ></textarea>
          <div class="char-count">{{ postForm.get('caption')?.value?.length || 0 }}/2000</div>
        </div>

        <!-- Link Type Selection -->
        <div class="form-section">
          <h3>Content Linking (Required)</h3>
          <p class="section-description">Choose how to link your post for better discoverability</p>

          <div class="link-type-selector">
            <label class="link-option">
              <input type="radio" formControlName="linkType" value="product">
              <span class="option-content">
                <i class="fas fa-box"></i>
                <strong>Link to Product</strong>
                <small>Tag specific products in your post</small>
              </span>
            </label>

            <label class="link-option">
              <input type="radio" formControlName="linkType" value="category">
              <span class="option-content">
                <i class="fas fa-tags"></i>
                <strong>Link to Category</strong>
                <small>Associate with a product category</small>
              </span>
            </label>
          </div>
        </div>

        <!-- Category Selection (when category is selected) -->
        <div class="form-section" *ngIf="linkType === 'category'">
          <h3>Select Category</h3>
          <div class="category-search">
            <input
              type="text"
              placeholder="Search categories..."
              (input)="searchCategories($event)"
              class="search-input"
            >

            <div class="category-results" *ngIf="categorySearchResults.length > 0">
              <div
                class="category-item"
                *ngFor="let category of categorySearchResults"
                (click)="selectCategory(category)"
              >
                <i class="fas fa-tag"></i>
                <div class="category-info">
                  <h4>{{ category.name }}</h4>
                  <p>{{ category.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="selected-category" *ngIf="selectedCategory">
            <h4>Selected Category:</h4>
            <div class="category-display">
              <i class="fas fa-tag"></i>
              <span>{{ selectedCategory.name }}</span>
              <button type="button" (click)="selectedCategory = null">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Product Tags (when product is selected) -->
        <div class="form-section" *ngIf="linkType === 'product'">
          <h3>Tag Products</h3>
          <div class="product-search">
            <input 
              type="text" 
              placeholder="Search your products..."
              (input)="searchProducts($event)"
              class="search-input"
            >
            
            <div class="product-results" *ngIf="searchResults.length > 0">
              <div 
                class="product-item" 
                *ngFor="let product of searchResults"
                (click)="addProductTag(product)"
              >
                <img [src]="product.images[0]?.url" [alt]="product.name">
                <div class="product-info">
                  <h4>{{ product.name }}</h4>
                  <p>₹{{ product.price | number:'1.0-0' }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="tagged-products" *ngIf="taggedProducts.length > 0">
            <h4>Tagged Products:</h4>
            <div class="tagged-list">
              <div class="tagged-item" *ngFor="let product of taggedProducts; let i = index">
                <img [src]="product.images[0]?.url" [alt]="product.name">
                <span>{{ product.name }}</span>
                <button type="button" (click)="removeProductTag(i)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Hashtags -->
        <div class="form-section">
          <h3>Hashtags</h3>
          <input 
            type="text" 
            placeholder="Add hashtags (e.g., #fashion #style #trending)"
            (keyup.enter)="addHashtag($event)"
            class="hashtag-input"
          >
          
          <div class="hashtags" *ngIf="hashtags.length > 0">
            <span class="hashtag" *ngFor="let tag of hashtags; let i = index">
              #{{ tag }}
              <button type="button" (click)="removeHashtag(i)">×</button>
            </span>
          </div>
        </div>

        <!-- Post Settings -->
        <div class="form-section">
          <h3>Post Settings</h3>
          <div class="settings-grid">
            <label class="setting-item">
              <input type="checkbox" formControlName="allowComments">
              <span>Allow comments</span>
            </label>
            <label class="setting-item">
              <input type="checkbox" formControlName="allowSharing">
              <span>Allow sharing</span>
            </label>
          </div>
        </div>

        <!-- Submit Buttons -->
        <div class="form-actions">
          <button type="button" class="btn-secondary" (click)="saveDraft()">Save as Draft</button>
          <button type="submit" class="btn-primary" [disabled]="!postForm.valid || uploading">
            <span *ngIf="uploading">Publishing...</span>
            <span *ngIf="!uploading">Publish Post</span>
          </button>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .create-post-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      margin-bottom: 30px;
    }

    .header h1 {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .header p {
      color: #666;
    }

    .post-form {
      background: white;
      border-radius: 8px;
      padding: 30px;
      border: 1px solid #eee;
    }

    .form-section {
      margin-bottom: 30px;
    }

    .form-section h3 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 15px;
    }

    .section-description {
      color: #666;
      font-size: 0.9rem;
      margin-bottom: 15px;
    }

    .link-type-selector {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 20px;
    }

    .link-option {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.2s;
      display: block;
    }

    .link-option:hover {
      border-color: #007bff;
      background: #f8f9ff;
    }

    .link-option input[type="radio"] {
      display: none;
    }

    .link-option input[type="radio"]:checked + .option-content {
      color: #007bff;
    }

    .link-option input[type="radio"]:checked + .option-content i {
      color: #007bff;
    }

    .link-option:has(input[type="radio"]:checked) {
      border-color: #007bff;
      background: #f8f9ff;
    }

    .option-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 8px;
    }

    .option-content i {
      font-size: 2rem;
      color: #6c757d;
      margin-bottom: 5px;
    }

    .option-content strong {
      font-size: 1rem;
      margin-bottom: 2px;
    }

    .option-content small {
      color: #6c757d;
      font-size: 0.85rem;
    }

    .category-results {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 6px;
    }

    .category-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      cursor: pointer;
      border-bottom: 1px solid #f5f5f5;
    }

    .category-item:hover {
      background: #f8f9fa;
    }

    .category-item i {
      color: #007bff;
      font-size: 1.2rem;
    }

    .category-info h4 {
      font-size: 0.9rem;
      margin-bottom: 2px;
    }

    .category-info p {
      color: #666;
      font-size: 0.85rem;
    }

    .selected-category {
      margin-top: 15px;
    }

    .category-display {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #f8f9fa;
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 0.85rem;
      width: fit-content;
    }

    .category-display i {
      color: #007bff;
    }

    .category-display button {
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      margin-left: 5px;
    }

    .upload-area {
      border: 2px dashed #ddd;
      border-radius: 8px;
      padding: 40px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s;
    }

    .upload-area:hover {
      border-color: #007bff;
      background: #f8f9ff;
    }

    .upload-area.has-files {
      padding: 20px;
    }

    .upload-content i {
      font-size: 3rem;
      color: #ddd;
      margin-bottom: 15px;
    }

    .upload-content p {
      font-size: 1.1rem;
      margin-bottom: 5px;
    }

    .upload-content span {
      color: #666;
      font-size: 0.9rem;
    }

    .file-preview {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 15px;
    }

    .file-item {
      position: relative;
      border-radius: 8px;
      overflow: hidden;
    }

    .file-item img,
    .file-item video {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .remove-file {
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(0,0,0,0.7);
      color: white;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
    }

    textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-family: inherit;
      resize: vertical;
    }

    .char-count {
      text-align: right;
      color: #666;
      font-size: 0.85rem;
      margin-top: 5px;
    }

    .search-input {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      margin-bottom: 10px;
    }

    .product-results {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 6px;
    }

    .product-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      cursor: pointer;
      border-bottom: 1px solid #f5f5f5;
    }

    .product-item:hover {
      background: #f8f9fa;
    }

    .product-item img {
      width: 50px;
      height: 50px;
      object-fit: cover;
      border-radius: 4px;
    }

    .product-info h4 {
      font-size: 0.9rem;
      margin-bottom: 2px;
    }

    .product-info p {
      color: #666;
      font-size: 0.85rem;
    }

    .tagged-products {
      margin-top: 15px;
    }

    .tagged-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }

    .tagged-item {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #f8f9fa;
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 0.85rem;
    }

    .tagged-item img {
      width: 24px;
      height: 24px;
      object-fit: cover;
      border-radius: 50%;
    }

    .tagged-item button {
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      margin-left: 5px;
    }

    .hashtag-input {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
    }

    .hashtags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 10px;
    }

    .hashtag {
      background: #007bff;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.85rem;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .hashtag button {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 1.1rem;
    }

    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }

    .setting-item {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
    }

    .form-actions {
      display: flex;
      gap: 15px;
      justify-content: flex-end;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .btn-primary, .btn-secondary {
      padding: 12px 24px;
      border-radius: 6px;
      font-weight: 500;
      cursor: pointer;
      border: none;
      transition: all 0.2s;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-secondary {
      background: #f8f9fa;
      color: #6c757d;
      border: 1px solid #dee2e6;
    }

    .btn-secondary:hover {
      background: #e9ecef;
    }

    @media (max-width: 768px) {
      .form-actions {
        flex-direction: column;
      }

      .file-preview {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      }

      .link-type-selector {
        grid-template-columns: 1fr;
      }

      .link-option {
        padding: 15px;
      }

      .option-content {
        flex-direction: row;
        text-align: left;
      }

      .option-content i {
        font-size: 1.5rem;
        margin-bottom: 0;
      }
    }
  `]
})
export class CreatePostComponent implements OnInit {
  postForm: FormGroup;
  selectedFiles: any[] = [];
  taggedProducts: any[] = [];
  hashtags: string[] = [];
  searchResults: any[] = [];
  uploading = false;

  // New properties for mandatory linking
  linkType: 'product' | 'category' = 'product';
  selectedCategory: any = null;
  categories: any[] = [];
  categorySearchResults: any[] = [];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private http: HttpClient
  ) {
    this.postForm = this.fb.group({
      caption: ['', [Validators.required, Validators.maxLength(2000)]],
      allowComments: [true],
      allowSharing: [true],
      linkType: ['product', Validators.required]
    });
  }

  ngOnInit() {
    // Initialize component
    this.loadCategories();

    // Watch for link type changes
    this.postForm.get('linkType')?.valueChanges.subscribe(value => {
      this.linkType = value;
      // Clear selections when switching types
      if (value === 'product') {
        this.selectedCategory = null;
      } else {
        this.taggedProducts = [];
      }
    });
  }

  loadCategories() {
    // Load categories from API
    this.http.get<any>('/api/categories').subscribe({
      next: (response) => {
        this.categories = response.categories || [];
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
  }

  onFileSelect(event: any) {
    const files = Array.from(event.target.files);
    files.forEach((file: any) => {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.selectedFiles.push({
          file,
          preview: e.target.result,
          type: file.type,
          name: file.name
        });
      };
      reader.readAsDataURL(file);
    });
  }

  removeFile(index: number) {
    this.selectedFiles.splice(index, 1);
  }

  searchProducts(event: any) {
    const query = event.target.value;
    if (query.length > 2) {
      // Search products from API
      this.http.get<any>(`/api/products/search?q=${query}`).subscribe({
        next: (response) => {
          this.searchResults = response.products || [];
        },
        error: (error) => {
          console.error('Error searching products:', error);
          this.searchResults = [];
        }
      });
    } else {
      this.searchResults = [];
    }
  }

  searchCategories(event: any) {
    const query = event.target.value;
    if (query.length > 2) {
      // Filter categories based on query
      this.categorySearchResults = this.categories.filter(category =>
        category.name.toLowerCase().includes(query.toLowerCase())
      );
    } else {
      this.categorySearchResults = [];
    }
  }

  selectCategory(category: any) {
    this.selectedCategory = category;
    this.categorySearchResults = [];
  }

  addProductTag(product: any) {
    if (!this.taggedProducts.find(p => p._id === product._id)) {
      this.taggedProducts.push(product);
    }
    this.searchResults = [];
  }

  removeProductTag(index: number) {
    this.taggedProducts.splice(index, 1);
  }

  addHashtag(event: any) {
    const tag = event.target.value.trim().replace('#', '');
    if (tag && !this.hashtags.includes(tag)) {
      this.hashtags.push(tag);
      event.target.value = '';
    }
  }

  removeHashtag(index: number) {
    this.hashtags.splice(index, 1);
  }

  saveDraft() {
    // TODO: Implement save as draft functionality
    console.log('Saving as draft...');
  }

  onSubmit() {
    // Validate mandatory linking
    if (this.linkType === 'product' && this.taggedProducts.length === 0) {
      alert('Please tag at least one product before publishing your post. This helps customers discover and purchase your products!');
      return;
    }

    if (this.linkType === 'category' && !this.selectedCategory) {
      alert('Please select a category before publishing your post. This helps customers find relevant content!');
      return;
    }

    if (this.postForm.valid && this.selectedFiles.length > 0) {
      this.uploading = true;

      // Prepare linked content based on type
      const linkedContent = this.linkType === 'product'
        ? {
            type: 'product',
            productId: this.taggedProducts[0]._id // Use first tagged product as primary link
          }
        : {
            type: 'category',
            categoryId: this.selectedCategory._id
          };

      const postData = {
        caption: this.postForm.value.caption,
        media: this.selectedFiles.map(f => ({
          type: f.type.startsWith('image') ? 'image' : 'video',
          url: f.preview // In real implementation, upload to server first
        })),
        linkedContent: linkedContent,
        products: this.taggedProducts.map(p => ({
          product: p._id,
          position: { x: 50, y: 50 } // Default position
        })),
        hashtags: this.hashtags,
        settings: {
          allowComments: this.postForm.value.allowComments,
          allowSharing: this.postForm.value.allowSharing
        }
      };

      // TODO: Implement actual post creation API
      this.http.post('/api/posts', postData).subscribe({
        next: (response) => {
          this.uploading = false;
          alert('Post created successfully!');
          this.router.navigate(['/vendor/posts']);
        },
        error: (error) => {
          this.uploading = false;
          alert('Error creating post: ' + (error.error?.message || 'Unknown error'));
        }
      });
    }
  }
}
