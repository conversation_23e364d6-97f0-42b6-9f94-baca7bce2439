{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index5.js';\nimport { c as config } from './ionic-global.js';\n\n/**\n * CloseWatcher is a newer API that lets\n * use detect the hardware back button event\n * in a web browser: https://caniuse.com/?search=closewatcher\n * However, not every browser supports it yet.\n *\n * This needs to be a function so that we can\n * check the config once it has been set.\n * Otherwise, this code would be evaluated the\n * moment this file is evaluated which could be\n * before the config is set.\n */\nconst shouldUseCloseWatcher = () => config.get('experimentalCloseWatcher', false) && win !== undefined && 'CloseWatcher' in win;\n/**\n * When hardwareBackButton: false in config,\n * we need to make sure we also block the default\n * webview behavior. If we don't then it will be\n * possible for users to navigate backward while\n * an overlay is still open. Additionally, it will\n * give the appearance that the hardwareBackButton\n * config is not working as the page transition\n * will still happen.\n */\nconst blockHardwareBackButton = () => {\n  document.addEventListener('backbutton', () => {}); // eslint-disable-line\n};\nconst startHardwareBackButton = () => {\n  const doc = document;\n  let busy = false;\n  const backButtonCallback = () => {\n    if (busy) {\n      return;\n    }\n    let index = 0;\n    let handlers = [];\n    const ev = new CustomEvent('ionBackButton', {\n      bubbles: false,\n      detail: {\n        register(priority, handler) {\n          handlers.push({\n            priority,\n            handler,\n            id: index++\n          });\n        }\n      }\n    });\n    doc.dispatchEvent(ev);\n    const executeAction = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (handlerRegister) {\n        try {\n          if (handlerRegister === null || handlerRegister === void 0 ? void 0 : handlerRegister.handler) {\n            const result = handlerRegister.handler(processHandlers);\n            if (result != null) {\n              yield result;\n            }\n          }\n        } catch (e) {\n          console.error(e);\n        }\n      });\n      return function executeAction(_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    const processHandlers = () => {\n      if (handlers.length > 0) {\n        let selectedHandler = {\n          priority: Number.MIN_SAFE_INTEGER,\n          handler: () => undefined,\n          id: -1\n        };\n        handlers.forEach(handler => {\n          if (handler.priority >= selectedHandler.priority) {\n            selectedHandler = handler;\n          }\n        });\n        busy = true;\n        handlers = handlers.filter(handler => handler.id !== selectedHandler.id);\n        executeAction(selectedHandler).then(() => busy = false);\n      }\n    };\n    processHandlers();\n  };\n  /**\n   * If the CloseWatcher is defined then\n   * we don't want to also listen for the native\n   * backbutton event otherwise we may get duplicate\n   * events firing.\n   */\n  if (shouldUseCloseWatcher()) {\n    let watcher;\n    const configureWatcher = () => {\n      watcher === null || watcher === void 0 ? void 0 : watcher.destroy();\n      watcher = new win.CloseWatcher();\n      /**\n       * Once a close request happens\n       * the watcher gets destroyed.\n       * As a result, we need to re-configure\n       * the watcher so we can respond to other\n       * close requests.\n       */\n      watcher.onclose = () => {\n        backButtonCallback();\n        configureWatcher();\n      };\n    };\n    configureWatcher();\n  } else {\n    doc.addEventListener('backbutton', backButtonCallback);\n  }\n};\nconst OVERLAY_BACK_BUTTON_PRIORITY = 100;\nconst MENU_BACK_BUTTON_PRIORITY = 99; // 1 less than overlay priority since menu is displayed behind overlays\n\nexport { MENU_BACK_BUTTON_PRIORITY, OVERLAY_BACK_BUTTON_PRIORITY, blockHardwareBackButton, shouldUseCloseWatcher, startHardwareBackButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}