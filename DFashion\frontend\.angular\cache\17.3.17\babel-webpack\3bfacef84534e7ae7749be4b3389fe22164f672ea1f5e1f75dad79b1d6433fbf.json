{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host } from './index-a1a47f01.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-1bf57181.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropIosStyle0 = backdropIosCss;\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropMdStyle0 = backdropMdCss;\nconst Backdrop = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n    this.blocker = GESTURE_CONTROLLER.createBlocker({\n      disableScroll: true\n    });\n    this.visible = true;\n    this.tappable = true;\n    this.stopPropagation = true;\n  }\n  connectedCallback() {\n    if (this.stopPropagation) {\n      this.blocker.block();\n    }\n  }\n  disconnectedCallback() {\n    this.blocker.unblock();\n  }\n  onMouseDown(ev) {\n    this.emitTap(ev);\n  }\n  emitTap(ev) {\n    if (this.stopPropagation) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n    if (this.tappable) {\n      this.ionBackdropTap.emit();\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '16b1328f4a058b8d3752e58dc56c44bed556c425',\n      tabindex: \"-1\",\n      \"aria-hidden\": \"true\",\n      class: {\n        [mode]: true,\n        'backdrop-hide': !this.visible,\n        'backdrop-no-tappable': !this.tappable\n      }\n    });\n  }\n};\nBackdrop.style = {\n  ios: IonBackdropIosStyle0,\n  md: IonBackdropMdStyle0\n};\nexport { Backdrop as ion_backdrop };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "G", "GESTURE_CONTROLLER", "b", "getIonMode", "backdropIosCss", "IonBackdropIosStyle0", "backdropMdCss", "IonBackdropMdStyle0", "Backdrop", "constructor", "hostRef", "ionBackdropTap", "blocker", "createBlocker", "disableScroll", "visible", "tappable", "stopPropagation", "connectedCallback", "block", "disconnectedCallback", "unblock", "onMouseDown", "ev", "emitTap", "preventDefault", "emit", "render", "mode", "key", "tabindex", "class", "style", "ios", "md", "ion_backdrop"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-backdrop.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host } from './index-a1a47f01.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-1bf57181.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropIosStyle0 = backdropIosCss;\n\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropMdStyle0 = backdropMdCss;\n\nconst Backdrop = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n        this.blocker = GESTURE_CONTROLLER.createBlocker({\n            disableScroll: true,\n        });\n        this.visible = true;\n        this.tappable = true;\n        this.stopPropagation = true;\n    }\n    connectedCallback() {\n        if (this.stopPropagation) {\n            this.blocker.block();\n        }\n    }\n    disconnectedCallback() {\n        this.blocker.unblock();\n    }\n    onMouseDown(ev) {\n        this.emitTap(ev);\n    }\n    emitTap(ev) {\n        if (this.stopPropagation) {\n            ev.preventDefault();\n            ev.stopPropagation();\n        }\n        if (this.tappable) {\n            this.ionBackdropTap.emit();\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '16b1328f4a058b8d3752e58dc56c44bed556c425', tabindex: \"-1\", \"aria-hidden\": \"true\", class: {\n                [mode]: true,\n                'backdrop-hide': !this.visible,\n                'backdrop-no-tappable': !this.tappable,\n            } }));\n    }\n};\nBackdrop.style = {\n    ios: IonBackdropIosStyle0,\n    md: IonBackdropMdStyle0\n};\n\nexport { Backdrop as ion_backdrop };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AAC3F,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,kCAAkC;AAC1E,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,MAAMC,cAAc,GAAG,wWAAwW;AAC/X,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,wWAAwW;AAC9X,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBhB,gBAAgB,CAAC,IAAI,EAAEgB,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGf,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACgB,OAAO,GAAGX,kBAAkB,CAACY,aAAa,CAAC;MAC5CC,aAAa,EAAE;IACnB,CAAC,CAAC;IACF,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACD,eAAe,EAAE;MACtB,IAAI,CAACL,OAAO,CAACO,KAAK,CAAC,CAAC;IACxB;EACJ;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACR,OAAO,CAACS,OAAO,CAAC,CAAC;EAC1B;EACAC,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACC,OAAO,CAACD,EAAE,CAAC;EACpB;EACAC,OAAOA,CAACD,EAAE,EAAE;IACR,IAAI,IAAI,CAACN,eAAe,EAAE;MACtBM,EAAE,CAACE,cAAc,CAAC,CAAC;MACnBF,EAAE,CAACN,eAAe,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf,IAAI,CAACL,cAAc,CAACe,IAAI,CAAC,CAAC;IAC9B;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGzB,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQN,CAAC,CAACE,IAAI,EAAE;MAAE8B,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,KAAK,EAAE;QACzG,CAACH,IAAI,GAAG,IAAI;QACZ,eAAe,EAAE,CAAC,IAAI,CAACb,OAAO;QAC9B,sBAAsB,EAAE,CAAC,IAAI,CAACC;MAClC;IAAE,CAAC,CAAC;EACZ;AACJ,CAAC;AACDR,QAAQ,CAACwB,KAAK,GAAG;EACbC,GAAG,EAAE5B,oBAAoB;EACzB6B,EAAE,EAAE3B;AACR,CAAC;AAED,SAASC,QAAQ,IAAI2B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}