{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProductService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/products`;\n    this.productsSubject = new BehaviorSubject([]);\n    this.products$ = this.productsSubject.asObservable();\n  }\n  // Get all products with filters\n  getProducts(filters = {}) {\n    let params = new HttpParams();\n    Object.keys(filters).forEach(key => {\n      const value = filters[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n    return this.http.get(this.apiUrl, {\n      params\n    });\n  }\n  // Get product by ID\n  getProductById(id) {\n    return this.http.get(`${this.apiUrl}/${id}`);\n  }\n  // Create new product\n  createProduct(product) {\n    return this.http.post(this.apiUrl, product);\n  }\n  // Update product\n  updateProduct(id, product) {\n    return this.http.put(`${this.apiUrl}/${id}`, product);\n  }\n  // Delete product\n  deleteProduct(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n  // Toggle product status (active/inactive)\n  toggleProductStatus(id) {\n    return this.http.patch(`${this.apiUrl}/${id}/toggle-status`, {});\n  }\n  // Toggle featured status\n  toggleFeaturedStatus(id) {\n    return this.http.patch(`${this.apiUrl}/${id}/toggle-featured`, {});\n  }\n  // Approve product (for vendor products)\n  approveProduct(id) {\n    return this.http.patch(`${this.apiUrl}/${id}/approve`, {});\n  }\n  // Get product categories\n  getCategories() {\n    return this.http.get(`${this.apiUrl}/categories`);\n  }\n  // Get subcategories by category\n  getSubcategories(category) {\n    return this.http.get(`${this.apiUrl}/categories/${category}/subcategories`);\n  }\n  // Get product brands\n  getBrands() {\n    return this.http.get(`${this.apiUrl}/brands`);\n  }\n  // Upload product images\n  uploadProductImages(productId, files) {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('images', file);\n    });\n    return this.http.post(`${this.apiUrl}/${productId}/images`, formData);\n  }\n  // Delete product image\n  deleteProductImage(productId, imageId) {\n    return this.http.delete(`${this.apiUrl}/${productId}/images/${imageId}`);\n  }\n  // Update product inventory\n  updateInventory(productId, inventory) {\n    return this.http.patch(`${this.apiUrl}/${productId}/inventory`, inventory);\n  }\n  // Get product analytics\n  getProductAnalytics(productId, period = '30d') {\n    return this.http.get(`${this.apiUrl}/${productId}/analytics?period=${period}`);\n  }\n  // Bulk operations\n  bulkUpdateProducts(productIds, updates) {\n    return this.http.patch(`${this.apiUrl}/bulk-update`, {\n      productIds,\n      updates\n    });\n  }\n  bulkDeleteProducts(productIds) {\n    return this.http.delete(`${this.apiUrl}/bulk-delete`, {\n      body: {\n        productIds\n      }\n    });\n  }\n  // Search products\n  searchProducts(query, filters = {}) {\n    const searchFilters = {\n      ...filters,\n      search: query\n    };\n    return this.getProducts(searchFilters);\n  }\n  // Get featured products\n  getFeaturedProducts(limit = 10) {\n    return this.http.get(`${this.apiUrl}/featured?limit=${limit}`);\n  }\n  // Get products by vendor\n  getProductsByVendor(vendorId, filters = {}) {\n    const vendorFilters = {\n      ...filters,\n      vendor: vendorId\n    };\n    return this.getProducts(vendorFilters);\n  }\n  // Update products subject\n  updateProductsSubject(products) {\n    this.productsSubject.next(products);\n  }\n  // Get current products\n  getCurrentProducts() {\n    return this.productsSubject.value;\n  }\n  static {\n    this.ɵfac = function ProductService_Factory(t) {\n      return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductService,\n      factory: ProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "environment", "ProductService", "constructor", "http", "apiUrl", "productsSubject", "products$", "asObservable", "getProducts", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getProductById", "id", "createProduct", "product", "post", "updateProduct", "put", "deleteProduct", "delete", "toggleProductStatus", "patch", "toggleFeaturedStatus", "approveProduct", "getCategories", "getSubcategories", "category", "getBrands", "uploadProductImages", "productId", "files", "formData", "FormData", "file", "append", "deleteProductImage", "imageId", "updateInventory", "inventory", "getProductAnalytics", "period", "bulkUpdateProducts", "productIds", "updates", "bulkDeleteProducts", "body", "searchProducts", "query", "searchFilters", "search", "getFeaturedProducts", "limit", "getProductsByVendor", "vendorId", "vendorFilters", "vendor", "updateProductsSubject", "products", "next", "getCurrentProducts", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\services\\product.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\n\nexport interface Product {\n  _id?: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice?: number;\n  discount: number;\n  category: string;\n  subcategory: string;\n  brand: string;\n  images: ProductImage[];\n  sizes: ProductSize[];\n  colors: ProductColor[];\n  vendor?: any;\n  isActive: boolean;\n  isFeatured: boolean;\n  isApproved?: boolean;\n  rating?: {\n    average: number;\n    count: number;\n  };\n  analytics?: {\n    views: number;\n    likes: number;\n    shares: number;\n    purchases: number;\n  };\n  tags?: string[];\n  createdAt?: string;\n  updatedAt?: string;\n}\n\nexport interface ProductImage {\n  url: string;\n  alt: string;\n  isPrimary: boolean;\n}\n\nexport interface ProductSize {\n  size: string;\n  stock: number;\n}\n\nexport interface ProductColor {\n  name: string;\n  code: string;\n  images: string[];\n}\n\nexport interface ProductFilters {\n  search?: string;\n  category?: string;\n  subcategory?: string;\n  brand?: string;\n  minPrice?: number;\n  maxPrice?: number;\n  isActive?: boolean;\n  isFeatured?: boolean;\n  vendor?: string;\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface ProductResponse {\n  products: Product[];\n  total: number;\n  page: number;\n  totalPages: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProductService {\n  private apiUrl = `${environment.apiUrl}/products`;\n  private productsSubject = new BehaviorSubject<Product[]>([]);\n  public products$ = this.productsSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  // Get all products with filters\n  getProducts(filters: ProductFilters = {}): Observable<ProductResponse> {\n    let params = new HttpParams();\n    \n    Object.keys(filters).forEach(key => {\n      const value = (filters as any)[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n\n    return this.http.get<ProductResponse>(this.apiUrl, { params });\n  }\n\n  // Get product by ID\n  getProductById(id: string): Observable<Product> {\n    return this.http.get<Product>(`${this.apiUrl}/${id}`);\n  }\n\n  // Create new product\n  createProduct(product: Partial<Product>): Observable<Product> {\n    return this.http.post<Product>(this.apiUrl, product);\n  }\n\n  // Update product\n  updateProduct(id: string, product: Partial<Product>): Observable<Product> {\n    return this.http.put<Product>(`${this.apiUrl}/${id}`, product);\n  }\n\n  // Delete product\n  deleteProduct(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\n  }\n\n  // Toggle product status (active/inactive)\n  toggleProductStatus(id: string): Observable<Product> {\n    return this.http.patch<Product>(`${this.apiUrl}/${id}/toggle-status`, {});\n  }\n\n  // Toggle featured status\n  toggleFeaturedStatus(id: string): Observable<Product> {\n    return this.http.patch<Product>(`${this.apiUrl}/${id}/toggle-featured`, {});\n  }\n\n  // Approve product (for vendor products)\n  approveProduct(id: string): Observable<Product> {\n    return this.http.patch<Product>(`${this.apiUrl}/${id}/approve`, {});\n  }\n\n  // Get product categories\n  getCategories(): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/categories`);\n  }\n\n  // Get subcategories by category\n  getSubcategories(category: string): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/categories/${category}/subcategories`);\n  }\n\n  // Get product brands\n  getBrands(): Observable<string[]> {\n    return this.http.get<string[]>(`${this.apiUrl}/brands`);\n  }\n\n  // Upload product images\n  uploadProductImages(productId: string, files: File[]): Observable<ProductImage[]> {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('images', file);\n    });\n    \n    return this.http.post<ProductImage[]>(`${this.apiUrl}/${productId}/images`, formData);\n  }\n\n  // Delete product image\n  deleteProductImage(productId: string, imageId: string): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/${productId}/images/${imageId}`);\n  }\n\n  // Update product inventory\n  updateInventory(productId: string, inventory: any): Observable<Product> {\n    return this.http.patch<Product>(`${this.apiUrl}/${productId}/inventory`, inventory);\n  }\n\n  // Get product analytics\n  getProductAnalytics(productId: string, period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/${productId}/analytics?period=${period}`);\n  }\n\n  // Bulk operations\n  bulkUpdateProducts(productIds: string[], updates: Partial<Product>): Observable<any> {\n    return this.http.patch<any>(`${this.apiUrl}/bulk-update`, {\n      productIds,\n      updates\n    });\n  }\n\n  bulkDeleteProducts(productIds: string[]): Observable<any> {\n    return this.http.delete<any>(`${this.apiUrl}/bulk-delete`, {\n      body: { productIds }\n    });\n  }\n\n  // Search products\n  searchProducts(query: string, filters: ProductFilters = {}): Observable<ProductResponse> {\n    const searchFilters = { ...filters, search: query };\n    return this.getProducts(searchFilters);\n  }\n\n  // Get featured products\n  getFeaturedProducts(limit: number = 10): Observable<Product[]> {\n    return this.http.get<Product[]>(`${this.apiUrl}/featured?limit=${limit}`);\n  }\n\n  // Get products by vendor\n  getProductsByVendor(vendorId: string, filters: ProductFilters = {}): Observable<ProductResponse> {\n    const vendorFilters = { ...filters, vendor: vendorId };\n    return this.getProducts(vendorFilters);\n  }\n\n  // Update products subject\n  updateProductsSubject(products: Product[]): void {\n    this.productsSubject.next(products);\n  }\n\n  // Get current products\n  getCurrentProducts(): Product[] {\n    return this.productsSubject.value;\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAAqBC,eAAe,QAAQ,MAAM;AAClD,SAASC,WAAW,QAAQ,mCAAmC;;;AA6E/D,OAAM,MAAOC,cAAc;EAKzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACI,MAAM,WAAW;IACzC,KAAAC,eAAe,GAAG,IAAIN,eAAe,CAAY,EAAE,CAAC;IACrD,KAAAO,SAAS,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEf;EAEvC;EACAC,WAAWA,CAACC,OAAA,GAA0B,EAAE;IACtC,IAAIC,MAAM,GAAG,IAAIZ,UAAU,EAAE;IAE7Ba,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;MACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;MACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;IAE9C,CAAC,CAAC;IAEF,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAkB,IAAI,CAACf,MAAM,EAAE;MAAEM;IAAM,CAAE,CAAC;EAChE;EAEA;EACAU,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAAClB,IAAI,CAACgB,GAAG,CAAU,GAAG,IAAI,CAACf,MAAM,IAAIiB,EAAE,EAAE,CAAC;EACvD;EAEA;EACAC,aAAaA,CAACC,OAAyB;IACrC,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAU,IAAI,CAACpB,MAAM,EAAEmB,OAAO,CAAC;EACtD;EAEA;EACAE,aAAaA,CAACJ,EAAU,EAAEE,OAAyB;IACjD,OAAO,IAAI,CAACpB,IAAI,CAACuB,GAAG,CAAU,GAAG,IAAI,CAACtB,MAAM,IAAIiB,EAAE,EAAE,EAAEE,OAAO,CAAC;EAChE;EAEA;EACAI,aAAaA,CAACN,EAAU;IACtB,OAAO,IAAI,CAAClB,IAAI,CAACyB,MAAM,CAAO,GAAG,IAAI,CAACxB,MAAM,IAAIiB,EAAE,EAAE,CAAC;EACvD;EAEA;EACAQ,mBAAmBA,CAACR,EAAU;IAC5B,OAAO,IAAI,CAAClB,IAAI,CAAC2B,KAAK,CAAU,GAAG,IAAI,CAAC1B,MAAM,IAAIiB,EAAE,gBAAgB,EAAE,EAAE,CAAC;EAC3E;EAEA;EACAU,oBAAoBA,CAACV,EAAU;IAC7B,OAAO,IAAI,CAAClB,IAAI,CAAC2B,KAAK,CAAU,GAAG,IAAI,CAAC1B,MAAM,IAAIiB,EAAE,kBAAkB,EAAE,EAAE,CAAC;EAC7E;EAEA;EACAW,cAAcA,CAACX,EAAU;IACvB,OAAO,IAAI,CAAClB,IAAI,CAAC2B,KAAK,CAAU,GAAG,IAAI,CAAC1B,MAAM,IAAIiB,EAAE,UAAU,EAAE,EAAE,CAAC;EACrE;EAEA;EACAY,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC9B,IAAI,CAACgB,GAAG,CAAQ,GAAG,IAAI,CAACf,MAAM,aAAa,CAAC;EAC1D;EAEA;EACA8B,gBAAgBA,CAACC,QAAgB;IAC/B,OAAO,IAAI,CAAChC,IAAI,CAACgB,GAAG,CAAQ,GAAG,IAAI,CAACf,MAAM,eAAe+B,QAAQ,gBAAgB,CAAC;EACpF;EAEA;EACAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACjC,IAAI,CAACgB,GAAG,CAAW,GAAG,IAAI,CAACf,MAAM,SAAS,CAAC;EACzD;EAEA;EACAiC,mBAAmBA,CAACC,SAAiB,EAAEC,KAAa;IAClD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BF,KAAK,CAAC1B,OAAO,CAAC6B,IAAI,IAAG;MACnBF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,OAAO,IAAI,CAACvC,IAAI,CAACqB,IAAI,CAAiB,GAAG,IAAI,CAACpB,MAAM,IAAIkC,SAAS,SAAS,EAAEE,QAAQ,CAAC;EACvF;EAEA;EACAI,kBAAkBA,CAACN,SAAiB,EAAEO,OAAe;IACnD,OAAO,IAAI,CAAC1C,IAAI,CAACyB,MAAM,CAAO,GAAG,IAAI,CAACxB,MAAM,IAAIkC,SAAS,WAAWO,OAAO,EAAE,CAAC;EAChF;EAEA;EACAC,eAAeA,CAACR,SAAiB,EAAES,SAAc;IAC/C,OAAO,IAAI,CAAC5C,IAAI,CAAC2B,KAAK,CAAU,GAAG,IAAI,CAAC1B,MAAM,IAAIkC,SAAS,YAAY,EAAES,SAAS,CAAC;EACrF;EAEA;EACAC,mBAAmBA,CAACV,SAAiB,EAAEW,MAAA,GAAiB,KAAK;IAC3D,OAAO,IAAI,CAAC9C,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACf,MAAM,IAAIkC,SAAS,qBAAqBW,MAAM,EAAE,CAAC;EACrF;EAEA;EACAC,kBAAkBA,CAACC,UAAoB,EAAEC,OAAyB;IAChE,OAAO,IAAI,CAACjD,IAAI,CAAC2B,KAAK,CAAM,GAAG,IAAI,CAAC1B,MAAM,cAAc,EAAE;MACxD+C,UAAU;MACVC;KACD,CAAC;EACJ;EAEAC,kBAAkBA,CAACF,UAAoB;IACrC,OAAO,IAAI,CAAChD,IAAI,CAACyB,MAAM,CAAM,GAAG,IAAI,CAACxB,MAAM,cAAc,EAAE;MACzDkD,IAAI,EAAE;QAAEH;MAAU;KACnB,CAAC;EACJ;EAEA;EACAI,cAAcA,CAACC,KAAa,EAAE/C,OAAA,GAA0B,EAAE;IACxD,MAAMgD,aAAa,GAAG;MAAE,GAAGhD,OAAO;MAAEiD,MAAM,EAAEF;IAAK,CAAE;IACnD,OAAO,IAAI,CAAChD,WAAW,CAACiD,aAAa,CAAC;EACxC;EAEA;EACAE,mBAAmBA,CAACC,KAAA,GAAgB,EAAE;IACpC,OAAO,IAAI,CAACzD,IAAI,CAACgB,GAAG,CAAY,GAAG,IAAI,CAACf,MAAM,mBAAmBwD,KAAK,EAAE,CAAC;EAC3E;EAEA;EACAC,mBAAmBA,CAACC,QAAgB,EAAErD,OAAA,GAA0B,EAAE;IAChE,MAAMsD,aAAa,GAAG;MAAE,GAAGtD,OAAO;MAAEuD,MAAM,EAAEF;IAAQ,CAAE;IACtD,OAAO,IAAI,CAACtD,WAAW,CAACuD,aAAa,CAAC;EACxC;EAEA;EACAE,qBAAqBA,CAACC,QAAmB;IACvC,IAAI,CAAC7D,eAAe,CAAC8D,IAAI,CAACD,QAAQ,CAAC;EACrC;EAEA;EACAE,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC/D,eAAe,CAACU,KAAK;EACnC;;;uBAvIWd,cAAc,EAAAoE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdvE,cAAc;MAAAwE,OAAA,EAAdxE,cAAc,CAAAyE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}