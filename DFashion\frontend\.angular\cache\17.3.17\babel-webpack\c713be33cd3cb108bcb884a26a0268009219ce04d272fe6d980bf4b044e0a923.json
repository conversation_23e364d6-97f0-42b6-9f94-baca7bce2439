{"ast": null, "code": "import { BehaviorSubject, tap, catchError, throwError, of } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = environment.apiUrl;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n  initializeAuth() {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: response => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // Clear invalid token without redirecting\n          this.clearAuth();\n        }\n      });\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/auth/login`, credentials).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n    }), catchError(error => {\n      console.error('Login error:', error);\n      // Fallback demo login when backend is not available\n      if (error.status === 0 || error.status === 404) {\n        console.log('Backend not available, using demo login');\n        return this.demoLogin(credentials.email, credentials.password);\n      }\n      return throwError(() => error);\n    }));\n  }\n  demoLogin(email, password) {\n    // Demo credentials\n    const demoUsers = {\n      '<EMAIL>': {\n        password: 'password123',\n        role: 'customer',\n        name: 'Test User'\n      },\n      '<EMAIL>': {\n        password: 'admin123',\n        role: 'admin',\n        name: 'Administrator'\n      },\n      '<EMAIL>': {\n        password: 'admin123',\n        role: 'admin',\n        name: 'Super Administrator'\n      },\n      '<EMAIL>': {\n        password: 'password123',\n        role: 'customer',\n        name: 'John Doe'\n      },\n      '<EMAIL>': {\n        password: 'vendor123',\n        role: 'vendor',\n        name: 'Fashion Vendor'\n      }\n    };\n    const user = demoUsers[email];\n    if (user && user.password === password) {\n      const response = {\n        message: 'Login successful (Demo Mode)',\n        token: 'demo-token-' + Date.now(),\n        user: {\n          _id: email,\n          email: email,\n          fullName: user.name,\n          role: user.role,\n          username: email.split('@')[0],\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n          isVerified: true,\n          isActive: true,\n          followers: [],\n          following: [],\n          socialStats: {\n            postsCount: 0,\n            followersCount: 0,\n            followingCount: 0\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }\n      };\n      // Set token and user\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n      return of(response);\n    } else {\n      return throwError(() => ({\n        error: {\n          message: 'Invalid credentials'\n        },\n        status: 400\n      }));\n    }\n  }\n  register(userData) {\n    return this.http.post(`${this.API_URL}/auth/register`, userData).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n    }));\n  }\n  logout() {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/auth/me`);\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  setToken(token) {\n    localStorage.setItem('token', token);\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  get isAuthenticated() {\n    return this.isAuthenticatedSubject.value;\n  }\n  isAdmin() {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n  isVendor() {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n  isCustomer() {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "catchError", "throwError", "of", "environment", "AuthService", "constructor", "http", "router", "API_URL", "apiUrl", "currentUserSubject", "isAuthenticatedSubject", "currentUser$", "asObservable", "isAuthenticated$", "initializeAuth", "token", "getToken", "getCurrentUser", "subscribe", "next", "response", "user", "error", "clearAuth", "login", "credentials", "post", "pipe", "setToken", "console", "status", "log", "demoLogin", "email", "password", "demoUsers", "role", "name", "message", "Date", "now", "_id", "fullName", "username", "split", "avatar", "isVerified", "isActive", "followers", "following", "socialStats", "postsCount", "followersCount", "followingCount", "createdAt", "updatedAt", "register", "userData", "logout", "localStorage", "removeItem", "navigate", "get", "getItem", "setItem", "currentUserValue", "value", "isAuthenticated", "isAdmin", "isVendor", "isCustomer", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap, catchError, throwError, of } from 'rxjs';\nimport { Router } from '@angular/router';\n\nimport { User, LoginRequest, RegisterRequest, AuthResponse } from '../models/user.model';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = environment.apiUrl;\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {}\n\n  initializeAuth(): void {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: (response) => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // Clear invalid token without redirecting\n          this.clearAuth();\n        }\n      });\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        }),\n        catchError(error => {\n          console.error('Login error:', error);\n\n          // Fallback demo login when backend is not available\n          if (error.status === 0 || error.status === 404) {\n            console.log('Backend not available, using demo login');\n            return this.demoLogin(credentials.email, credentials.password);\n          }\n\n          return throwError(() => error);\n        })\n      );\n  }\n\n  private demoLogin(email: string, password: string): Observable<AuthResponse> {\n    // Demo credentials\n    const demoUsers: { [key: string]: any } = {\n      '<EMAIL>': { password: 'password123', role: 'customer', name: 'Test User' },\n      '<EMAIL>': { password: 'admin123', role: 'admin', name: 'Administrator' },\n      '<EMAIL>': { password: 'admin123', role: 'admin', name: 'Super Administrator' },\n      '<EMAIL>': { password: 'password123', role: 'customer', name: 'John Doe' },\n      '<EMAIL>': { password: 'vendor123', role: 'vendor', name: 'Fashion Vendor' }\n    };\n\n    const user = demoUsers[email];\n\n    if (user && user.password === password) {\n      const response: AuthResponse = {\n        message: 'Login successful (Demo Mode)',\n        token: 'demo-token-' + Date.now(),\n        user: {\n          _id: email,\n          email: email,\n          fullName: user.name,\n          role: user.role,\n          username: email.split('@')[0],\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n          isVerified: true,\n          isActive: true,\n          followers: [],\n          following: [],\n          socialStats: {\n            postsCount: 0,\n            followersCount: 0,\n            followingCount: 0\n          },\n          createdAt: new Date(),\n          updatedAt: new Date()\n        }\n      };\n\n      // Set token and user\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n\n      return of(response);\n    } else {\n      return throwError(() => ({\n        error: { message: 'Invalid credentials' },\n        status: 400\n      }));\n    }\n  }\n\n\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/register`, userData)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        })\n      );\n  }\n\n\n\n  logout(): void {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n\n  getCurrentUser(): Observable<{ user: User }> {\n    return this.http.get<{ user: User }>(`${this.API_URL}/auth/me`);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  private setToken(token: string): void {\n    localStorage.setItem('token', token);\n  }\n\n  get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  get isAuthenticated(): boolean {\n    return this.isAuthenticatedSubject.value;\n  }\n\n  isAdmin(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n\n  isVendor(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n\n  isCustomer(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,EAAEC,UAAU,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAInF,SAASC,WAAW,QAAQ,mCAAmC;;;;AAK/D,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM;IACrC,KAAAC,kBAAkB,GAAG,IAAIZ,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAa,sBAAsB,GAAG,IAAIb,eAAe,CAAU,KAAK,CAAC;IAE7D,KAAAc,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACE,YAAY,EAAE;EAKjE;EAEHE,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,cAAc,EAAE,CAACC,SAAS,CAAC;QAC9BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACX,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;UAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QACxC,CAAC;QACDG,KAAK,EAAEA,CAAA,KAAK;UACV;UACA,IAAI,CAACC,SAAS,EAAE;QAClB;OACD,CAAC;;EAEN;EAEAC,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACnB,OAAO,aAAa,EAAEkB,WAAW,CAAC,CAC3EE,IAAI,CACH7B,GAAG,CAACsB,QAAQ,IAAG;MACb,IAAI,CAACQ,QAAQ,CAACR,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,EACFpB,UAAU,CAACuB,KAAK,IAAG;MACjBO,OAAO,CAACP,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MAEpC;MACA,IAAIA,KAAK,CAACQ,MAAM,KAAK,CAAC,IAAIR,KAAK,CAACQ,MAAM,KAAK,GAAG,EAAE;QAC9CD,OAAO,CAACE,GAAG,CAAC,yCAAyC,CAAC;QACtD,OAAO,IAAI,CAACC,SAAS,CAACP,WAAW,CAACQ,KAAK,EAAER,WAAW,CAACS,QAAQ,CAAC;;MAGhE,OAAOlC,UAAU,CAAC,MAAMsB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEQU,SAASA,CAACC,KAAa,EAAEC,QAAgB;IAC/C;IACA,MAAMC,SAAS,GAA2B;MACxC,mBAAmB,EAAE;QAAED,QAAQ,EAAE,aAAa;QAAEE,IAAI,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAW,CAAE;MACrF,oBAAoB,EAAE;QAAEH,QAAQ,EAAE,UAAU;QAAEE,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAe,CAAE;MACpF,yBAAyB,EAAE;QAAEH,QAAQ,EAAE,UAAU;QAAEE,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAqB,CAAE;MAC/F,kBAAkB,EAAE;QAAEH,QAAQ,EAAE,aAAa;QAAEE,IAAI,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAU,CAAE;MACnF,oBAAoB,EAAE;QAAEH,QAAQ,EAAE,WAAW;QAAEE,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAgB;KACtF;IAED,MAAMhB,IAAI,GAAGc,SAAS,CAACF,KAAK,CAAC;IAE7B,IAAIZ,IAAI,IAAIA,IAAI,CAACa,QAAQ,KAAKA,QAAQ,EAAE;MACtC,MAAMd,QAAQ,GAAiB;QAC7BkB,OAAO,EAAE,8BAA8B;QACvCvB,KAAK,EAAE,aAAa,GAAGwB,IAAI,CAACC,GAAG,EAAE;QACjCnB,IAAI,EAAE;UACJoB,GAAG,EAAER,KAAK;UACVA,KAAK,EAAEA,KAAK;UACZS,QAAQ,EAAErB,IAAI,CAACgB,IAAI;UACnBD,IAAI,EAAEf,IAAI,CAACe,IAAI;UACfO,QAAQ,EAAEV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC7BC,MAAM,EAAE,oEAAoE;UAC5EC,UAAU,EAAE,IAAI;UAChBC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,EAAE;UACbC,WAAW,EAAE;YACXC,UAAU,EAAE,CAAC;YACbC,cAAc,EAAE,CAAC;YACjBC,cAAc,EAAE;WACjB;UACDC,SAAS,EAAE,IAAIf,IAAI,EAAE;UACrBgB,SAAS,EAAE,IAAIhB,IAAI;;OAEtB;MAED;MACA,IAAI,CAACX,QAAQ,CAACR,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;MAEtC,OAAOlB,EAAE,CAACmB,QAAQ,CAAC;KACpB,MAAM;MACL,OAAOpB,UAAU,CAAC,OAAO;QACvBsB,KAAK,EAAE;UAAEgB,OAAO,EAAE;QAAqB,CAAE;QACzCR,MAAM,EAAE;OACT,CAAC,CAAC;;EAEP;EAIA0B,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAACpD,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACnB,OAAO,gBAAgB,EAAEkD,QAAQ,CAAC,CAC3E9B,IAAI,CACH7B,GAAG,CAACsB,QAAQ,IAAG;MACb,IAAI,CAACQ,QAAQ,CAACR,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,CACH;EACL;EAIAuC,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAACnD,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACb,MAAM,CAACuD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEA5C,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACZ,IAAI,CAACyD,GAAG,CAAiB,GAAG,IAAI,CAACvD,OAAO,UAAU,CAAC;EACjE;EAEAS,QAAQA,CAAA;IACN,OAAO2C,YAAY,CAACI,OAAO,CAAC,OAAO,CAAC;EACtC;EAEQnC,QAAQA,CAACb,KAAa;IAC5B4C,YAAY,CAACK,OAAO,CAAC,OAAO,EAAEjD,KAAK,CAAC;EACtC;EAEA,IAAIkD,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACxD,kBAAkB,CAACyD,KAAK;EACtC;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACzD,sBAAsB,CAACwD,KAAK;EAC1C;EAEAE,OAAOA,CAAA;IACL,MAAM/C,IAAI,GAAG,IAAI,CAAC4C,gBAAgB;IAClC,OAAO5C,IAAI,EAAEe,IAAI,KAAK,OAAO;EAC/B;EAEAiC,QAAQA,CAAA;IACN,MAAMhD,IAAI,GAAG,IAAI,CAAC4C,gBAAgB;IAClC,OAAO5C,IAAI,EAAEe,IAAI,KAAK,QAAQ;EAChC;EAEAkC,UAAUA,CAAA;IACR,MAAMjD,IAAI,GAAG,IAAI,CAAC4C,gBAAgB;IAClC,OAAO5C,IAAI,EAAEe,IAAI,KAAK,UAAU;EAClC;;;uBA7JWjC,WAAW,EAAAoE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXzE,WAAW;MAAA0E,OAAA,EAAX1E,WAAW,CAAA2E,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}