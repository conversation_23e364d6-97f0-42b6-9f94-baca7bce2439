{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst spinners = {\n  bubbles: {\n    dur: 1000,\n    circles: 9,\n    fn: (dur, index, total) => {\n      const animationDelay = `${dur * index / total - dur}ms`;\n      const angle = 2 * Math.PI * index / total;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circles: {\n    dur: 1000,\n    circles: 8,\n    fn: (dur, index, total) => {\n      const step = index / total;\n      const animationDelay = `${dur * step - dur}ms`;\n      const angle = 2 * Math.PI * step;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circular: {\n    dur: 1400,\n    elmDuration: true,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 20,\n        cx: 48,\n        cy: 48,\n        fill: 'none',\n        viewBox: '24 24 48 48',\n        transform: 'translate(0,0)',\n        style: {}\n      };\n    }\n  },\n  crescent: {\n    dur: 750,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 26,\n        style: {}\n      };\n    }\n  },\n  dots: {\n    dur: 750,\n    circles: 3,\n    fn: (_, index) => {\n      const animationDelay = -(110 * index) + 'ms';\n      return {\n        r: 6,\n        style: {\n          left: `${32 - 32 * index}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  lines: {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 14,\n        y2: 26,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-small': {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 17,\n        y2: 29,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp-small': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  }\n};\nconst SPINNERS = spinners;\nexport { SPINNERS as S };", "map": {"version": 3, "names": ["spinners", "bubbles", "dur", "circles", "fn", "index", "total", "animationDelay", "angle", "Math", "PI", "r", "style", "top", "sin", "left", "cos", "step", "circular", "elmDuration", "cx", "cy", "fill", "viewBox", "transform", "crescent", "dots", "_", "lines", "y1", "y2", "SPINNERS", "S"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/spinner-configs-964f7cf3.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst spinners = {\n    bubbles: {\n        dur: 1000,\n        circles: 9,\n        fn: (dur, index, total) => {\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            const angle = (2 * Math.PI * index) / total;\n            return {\n                r: 5,\n                style: {\n                    top: `${32 * Math.sin(angle)}%`,\n                    left: `${32 * Math.cos(angle)}%`,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    circles: {\n        dur: 1000,\n        circles: 8,\n        fn: (dur, index, total) => {\n            const step = index / total;\n            const animationDelay = `${dur * step - dur}ms`;\n            const angle = 2 * Math.PI * step;\n            return {\n                r: 5,\n                style: {\n                    top: `${32 * Math.sin(angle)}%`,\n                    left: `${32 * Math.cos(angle)}%`,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    circular: {\n        dur: 1400,\n        elmDuration: true,\n        circles: 1,\n        fn: () => {\n            return {\n                r: 20,\n                cx: 48,\n                cy: 48,\n                fill: 'none',\n                viewBox: '24 24 48 48',\n                transform: 'translate(0,0)',\n                style: {},\n            };\n        },\n    },\n    crescent: {\n        dur: 750,\n        circles: 1,\n        fn: () => {\n            return {\n                r: 26,\n                style: {},\n            };\n        },\n    },\n    dots: {\n        dur: 750,\n        circles: 3,\n        fn: (_, index) => {\n            const animationDelay = -(110 * index) + 'ms';\n            return {\n                r: 6,\n                style: {\n                    left: `${32 - 32 * index}%`,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    lines: {\n        dur: 1000,\n        lines: 8,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${(360 / total) * index + (index < total / 2 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 14,\n                y2: 26,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    'lines-small': {\n        dur: 1000,\n        lines: 8,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${(360 / total) * index + (index < total / 2 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 12,\n                y2: 20,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    'lines-sharp': {\n        dur: 1000,\n        lines: 12,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 17,\n                y2: 29,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    'lines-sharp-small': {\n        dur: 1000,\n        lines: 12,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 12,\n                y2: 20,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n};\nconst SPINNERS = spinners;\n\nexport { SPINNERS as S };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,QAAQ,GAAG;EACbC,OAAO,EAAE;IACLC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAACF,GAAG,EAAEG,KAAK,EAAEC,KAAK,KAAK;MACvB,MAAMC,cAAc,GAAG,GAAIL,GAAG,GAAGG,KAAK,GAAIC,KAAK,GAAGJ,GAAG,IAAI;MACzD,MAAMM,KAAK,GAAI,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAGL,KAAK,GAAIC,KAAK;MAC3C,OAAO;QACHK,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE;UACHC,GAAG,EAAE,GAAG,EAAE,GAAGJ,IAAI,CAACK,GAAG,CAACN,KAAK,CAAC,GAAG;UAC/BO,IAAI,EAAE,GAAG,EAAE,GAAGN,IAAI,CAACO,GAAG,CAACR,KAAK,CAAC,GAAG;UAChC,iBAAiB,EAAED;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACDJ,OAAO,EAAE;IACLD,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAACF,GAAG,EAAEG,KAAK,EAAEC,KAAK,KAAK;MACvB,MAAMW,IAAI,GAAGZ,KAAK,GAAGC,KAAK;MAC1B,MAAMC,cAAc,GAAG,GAAGL,GAAG,GAAGe,IAAI,GAAGf,GAAG,IAAI;MAC9C,MAAMM,KAAK,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAGO,IAAI;MAChC,OAAO;QACHN,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE;UACHC,GAAG,EAAE,GAAG,EAAE,GAAGJ,IAAI,CAACK,GAAG,CAACN,KAAK,CAAC,GAAG;UAC/BO,IAAI,EAAE,GAAG,EAAE,GAAGN,IAAI,CAACO,GAAG,CAACR,KAAK,CAAC,GAAG;UAChC,iBAAiB,EAAED;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACDW,QAAQ,EAAE;IACNhB,GAAG,EAAE,IAAI;IACTiB,WAAW,EAAE,IAAI;IACjBhB,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAAA,KAAM;MACN,OAAO;QACHO,CAAC,EAAE,EAAE;QACLS,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,aAAa;QACtBC,SAAS,EAAE,gBAAgB;QAC3BZ,KAAK,EAAE,CAAC;MACZ,CAAC;IACL;EACJ,CAAC;EACDa,QAAQ,EAAE;IACNvB,GAAG,EAAE,GAAG;IACRC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAAA,KAAM;MACN,OAAO;QACHO,CAAC,EAAE,EAAE;QACLC,KAAK,EAAE,CAAC;MACZ,CAAC;IACL;EACJ,CAAC;EACDc,IAAI,EAAE;IACFxB,GAAG,EAAE,GAAG;IACRC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAACuB,CAAC,EAAEtB,KAAK,KAAK;MACd,MAAME,cAAc,GAAG,EAAE,GAAG,GAAGF,KAAK,CAAC,GAAG,IAAI;MAC5C,OAAO;QACHM,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE;UACHG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAGV,KAAK,GAAG;UAC3B,iBAAiB,EAAEE;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACDqB,KAAK,EAAE;IACH1B,GAAG,EAAE,IAAI;IACT0B,KAAK,EAAE,CAAC;IACRxB,EAAE,EAAEA,CAACF,GAAG,EAAEG,KAAK,EAAEC,KAAK,KAAK;MACvB,MAAMkB,SAAS,GAAG,UAAW,GAAG,GAAGlB,KAAK,GAAID,KAAK,IAAIA,KAAK,GAAGC,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM;MAC1F,MAAMC,cAAc,GAAG,GAAIL,GAAG,GAAGG,KAAK,GAAIC,KAAK,GAAGJ,GAAG,IAAI;MACzD,OAAO;QACH2B,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNlB,KAAK,EAAE;UACHY,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEjB;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACD,aAAa,EAAE;IACXL,GAAG,EAAE,IAAI;IACT0B,KAAK,EAAE,CAAC;IACRxB,EAAE,EAAEA,CAACF,GAAG,EAAEG,KAAK,EAAEC,KAAK,KAAK;MACvB,MAAMkB,SAAS,GAAG,UAAW,GAAG,GAAGlB,KAAK,GAAID,KAAK,IAAIA,KAAK,GAAGC,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM;MAC1F,MAAMC,cAAc,GAAG,GAAIL,GAAG,GAAGG,KAAK,GAAIC,KAAK,GAAGJ,GAAG,IAAI;MACzD,OAAO;QACH2B,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNlB,KAAK,EAAE;UACHY,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEjB;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACD,aAAa,EAAE;IACXL,GAAG,EAAE,IAAI;IACT0B,KAAK,EAAE,EAAE;IACTxB,EAAE,EAAEA,CAACF,GAAG,EAAEG,KAAK,EAAEC,KAAK,KAAK;MACvB,MAAMkB,SAAS,GAAG,UAAU,EAAE,GAAGnB,KAAK,IAAIA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM;MACvE,MAAME,cAAc,GAAG,GAAIL,GAAG,GAAGG,KAAK,GAAIC,KAAK,GAAGJ,GAAG,IAAI;MACzD,OAAO;QACH2B,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNlB,KAAK,EAAE;UACHY,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEjB;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACD,mBAAmB,EAAE;IACjBL,GAAG,EAAE,IAAI;IACT0B,KAAK,EAAE,EAAE;IACTxB,EAAE,EAAEA,CAACF,GAAG,EAAEG,KAAK,EAAEC,KAAK,KAAK;MACvB,MAAMkB,SAAS,GAAG,UAAU,EAAE,GAAGnB,KAAK,IAAIA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM;MACvE,MAAME,cAAc,GAAG,GAAIL,GAAG,GAAGG,KAAK,GAAIC,KAAK,GAAGJ,GAAG,IAAI;MACzD,OAAO;QACH2B,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNlB,KAAK,EAAE;UACHY,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEjB;QACvB;MACJ,CAAC;IACL;EACJ;AACJ,CAAC;AACD,MAAMwB,QAAQ,GAAG/B,QAAQ;AAEzB,SAAS+B,QAAQ,IAAIC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}