{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nfunction TrendingNowComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_button_10_Template_button_click_0_listener() {\n      const filter_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveFilter(filter_r2.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeFilter === filter_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filter_r2.label, \" \");\n  }\n}\nfunction TrendingNowComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"div\", 20);\n    i0.ɵɵelementStart(2, \"div\", 21);\n    i0.ɵɵelement(3, \"div\", 22)(4, \"div\", 23)(5, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingNowComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵtemplate(2, TrendingNowComponent_div_15_div_2_Template, 6, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r5.pricing.discountPercentage, \"% OFF \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_div_20_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 56);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r5.rating.average);\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵtemplate(2, TrendingNowComponent_div_16_div_3_div_20_i_2_Template, 1, 2, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStarArray(product_r5.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r5.pricing.mrp, \"1.0-0\"), \" \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" In Stock (\", product_r5.availability.totalStock, \") \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Only \", product_r5.availability.totalStock, \" left \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵtext(2, \" Out of Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.enter\", function TrendingNowComponent_div_16_div_3_Template_div_keydown_enter_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.space\", function TrendingNowComponent_div_16_div_3_Template_div_keydown_space_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"img\", 30);\n    i0.ɵɵlistener(\"error\", function TrendingNowComponent_div_16_div_3_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵelement(4, \"i\", 5);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, TrendingNowComponent_div_16_div_3_div_7_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_button_click_9_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.toggleWishlist(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(10, \"i\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_button_click_11_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.quickView(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(12, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_button_click_13_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.addToCart(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(14, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 39)(16, \"div\", 40);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h3\", 41);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingNowComponent_div_16_div_3_div_20_Template, 5, 2, \"div\", 42);\n    i0.ɵɵelementStart(21, \"div\", 43)(22, \"span\", 44);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, TrendingNowComponent_div_16_div_3_span_25_Template, 3, 4, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 46)(27, \"div\", 47);\n    i0.ɵɵelement(28, \"i\", 36);\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 47);\n    i0.ɵɵelement(32, \"i\", 35);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 47);\n    i0.ɵɵelement(36, \"i\", 48);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 49);\n    i0.ɵɵtemplate(40, TrendingNowComponent_div_16_div_3_span_40_Template, 3, 1, \"span\", 50)(41, TrendingNowComponent_div_16_div_3_span_41_Template, 3, 1, \"span\", 50)(42, TrendingNowComponent_div_16_div_3_span_42_Template, 3, 0, \"span\", 50);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r5), i0.ɵɵsanitizeUrl)(\"alt\", product_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", product_r5.trendingScore, \"%\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add to wishlist\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"wishlisted\", ctx_r2.isInWishlist(product_r5._id));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Quick view\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", product_r5.availability.status !== \"in-stock\");\n    i0.ɵɵattribute(\"aria-label\", \"Add to cart\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.rating.count > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(24, 24, product_r5.pricing.sellingPrice, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(product_r5.analytics.views));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(product_r5.analytics.likes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(product_r5.analytics.purchases));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(product_r5.availability.status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"in-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"low-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"out-of-stock\");\n  }\n}\nfunction TrendingNowComponent_div_16_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.scrollProducts(\"left\"));\n    });\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canScrollLeft);\n  }\n}\nfunction TrendingNowComponent_div_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.scrollProducts(\"right\"));\n    });\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canScrollRight);\n  }\n}\nfunction TrendingNowComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 17, 0);\n    i0.ɵɵtemplate(3, TrendingNowComponent_div_16_div_3_Template, 43, 27, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TrendingNowComponent_div_16_button_4_Template, 2, 1, \"button\", 26)(5, TrendingNowComponent_div_16_button_5_Template, 2, 1, \"button\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.products)(\"ngForTrackBy\", ctx_r2.trackByProductId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showScrollButtons);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showScrollButtons);\n  }\n}\nfunction TrendingNowComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Trending Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Check back later for the hottest trends!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_17_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadProducts());\n    });\n    i0.ɵɵelement(8, \"i\", 69);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingNowComponent {\n  constructor(shopDataService, router) {\n    this.shopDataService = shopDataService;\n    this.router = router;\n    this.maxProducts = 12;\n    this.showHeader = true;\n    this.showScrollButtons = true;\n    this.products = [];\n    this.isLoading = true;\n    this.activeFilter = 'all';\n    this.wishlistItems = new Set();\n    this.canScrollLeft = false;\n    this.canScrollRight = true;\n    this.trendFilters = [{\n      label: 'All',\n      value: 'all'\n    }, {\n      label: 'Today',\n      value: 'today'\n    }, {\n      label: 'This Week',\n      value: 'week'\n    }, {\n      label: 'This Month',\n      value: 'month'\n    }];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadProducts();\n    this.loadWishlist();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadProducts() {\n    this.isLoading = true;\n    this.shopDataService.loadTrendingProducts(this.maxProducts).pipe(takeUntil(this.destroy$)).subscribe({\n      next: products => {\n        this.products = products;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading trending products:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  setActiveFilter(filter) {\n    this.activeFilter = filter;\n    // In a real implementation, this would filter the products\n    // For now, we'll just reload the products\n    this.loadProducts();\n  }\n  navigateToProduct(product) {\n    // Track product click\n    this.trackProductClick(product);\n    // Navigate to product detail page\n    this.router.navigate(['/product', product._id]);\n  }\n  viewAllTrending() {\n    this.router.navigate(['/shop/trending']);\n  }\n  toggleWishlist(product) {\n    if (this.wishlistItems.has(product._id)) {\n      this.wishlistItems.delete(product._id);\n    } else {\n      this.wishlistItems.add(product._id);\n    }\n    this.saveWishlist();\n  }\n  isInWishlist(productId) {\n    return this.wishlistItems.has(productId);\n  }\n  quickView(product) {\n    // Implement quick view modal\n    console.log('Quick view:', product);\n  }\n  addToCart(product) {\n    if (product.availability.status !== 'in-stock') {\n      return;\n    }\n    // Add to cart logic\n    console.log('Add to cart:', product);\n    // Show success message\n    this.showAddToCartSuccess(product);\n  }\n  scrollProducts(direction) {\n    const container = document.querySelector('.products-scroll');\n    if (!container) return;\n    const scrollAmount = 300;\n    const currentScroll = container.scrollLeft;\n    if (direction === 'left') {\n      container.scrollTo({\n        left: currentScroll - scrollAmount,\n        behavior: 'smooth'\n      });\n    } else {\n      container.scrollTo({\n        left: currentScroll + scrollAmount,\n        behavior: 'smooth'\n      });\n    }\n    // Update scroll button states\n    setTimeout(() => {\n      this.updateScrollButtons();\n    }, 300);\n  }\n  getProductImage(product) {\n    const primaryImage = product.images.find(img => img.isPrimary);\n    return primaryImage ? primaryImage.url : product.images[0]?.url || '';\n  }\n  getStarArray(rating) {\n    return Array(5).fill(0).map((_, i) => i + 1);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  onImageError(event) {\n    event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  updateScrollButtons() {\n    const container = document.querySelector('.products-scroll');\n    if (!container) return;\n    this.canScrollLeft = container.scrollLeft > 0;\n    this.canScrollRight = container.scrollLeft < container.scrollWidth - container.clientWidth;\n  }\n  trackProductClick(product) {\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'product_click', {\n        product_name: product.name,\n        product_id: product._id,\n        product_brand: product.brand,\n        product_category: product.category,\n        event_category: 'engagement'\n      });\n    }\n  }\n  loadWishlist() {\n    const stored = localStorage.getItem('dfashion_wishlist');\n    if (stored) {\n      try {\n        const wishlist = JSON.parse(stored);\n        this.wishlistItems = new Set(wishlist);\n      } catch (e) {\n        console.warn('Failed to load wishlist');\n      }\n    }\n  }\n  saveWishlist() {\n    localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));\n  }\n  showAddToCartSuccess(product) {\n    // Show a toast or notification\n    console.log(`${product.name} added to cart!`);\n    // In a real implementation, you might show a toast notification\n    // this.toastService.show(`${product.name} added to cart!`, 'success');\n  }\n  static {\n    this.ɵfac = function TrendingNowComponent_Factory(t) {\n      return new (t || TrendingNowComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingNowComponent,\n      selectors: [[\"app-trending-now\"]],\n      inputs: {\n        maxProducts: \"maxProducts\",\n        showHeader: \"showHeader\",\n        showScrollButtons: \"showScrollButtons\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 18,\n      vars: 4,\n      consts: [[\"productsScroll\", \"\"], [1, \"trending-now-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-fire\"], [1, \"section-subtitle\"], [1, \"header-actions\"], [1, \"trend-filters\"], [\"class\", \"filter-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"products-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"filter-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"products-scroll\"], [\"class\", \"product-card-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card-skeleton\"], [1, \"skeleton-image\"], [1, \"skeleton-content\"], [1, \"skeleton-text\"], [1, \"skeleton-text\", \"short\"], [1, \"products-container\"], [\"class\", \"product-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"scroll-btn scroll-left\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"scroll-btn scroll-right\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"product-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"error\", \"src\", \"alt\"], [1, \"trending-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"quick-actions\"], [1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"fas\", \"fa-eye\"], [1, \"quick-action-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"trending-stats\"], [1, \"stat-item\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"stock-status\"], [4, \"ngIf\"], [1, \"discount-badge\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"fas\", \"fa-star\"], [1, \"original-price\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"fas\", \"fa-times-circle\"], [1, \"scroll-btn\", \"scroll-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"scroll-btn\", \"scroll-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"fas\", \"fa-fire-extinguisher\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"]],\n      template: function TrendingNowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h2\", 4);\n          i0.ɵɵelement(4, \"i\", 5);\n          i0.ɵɵtext(5, \" Trending Now \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 6);\n          i0.ɵɵtext(7, \"Hot picks that everyone's talking about\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵtemplate(10, TrendingNowComponent_button_10_Template, 2, 3, \"button\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TrendingNowComponent_Template_button_click_11_listener() {\n            return ctx.viewAllTrending();\n          });\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"View All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(15, TrendingNowComponent_div_15_Template, 3, 2, \"div\", 12)(16, TrendingNowComponent_div_16_Template, 6, 4, \"div\", 13)(17, TrendingNowComponent_div_17_Template, 10, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.trendFilters);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe],\n      styles: [\".trending-now-section[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n  background: white;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 2rem;\\n  padding: 0 1rem;\\n  gap: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #262626;\\n  margin: 0 0 0.5rem 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n  font-size: 1.8rem;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n@media (max-width: 768px) {\\n  .trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n    align-items: stretch;\\n  }\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  background: #f8f9fa;\\n  padding: 0.25rem;\\n  border-radius: 25px;\\n}\\n@media (max-width: 768px) {\\n  .trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  background: transparent;\\n  color: #666;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n  color: #667eea;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn.active[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);\\n  color: white;\\n  border: none;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);\\n  white-space: nowrap;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(3px);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  overflow-x: hidden;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%] {\\n  min-width: 280px;\\n  background: #f8f9fa;\\n  border-radius: 16px;\\n  overflow: hidden;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 300px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 0 1rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  overflow-x: auto;\\n  scroll-behavior: smooth;\\n  padding-bottom: 1rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a1a1a1;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 48px;\\n  height: 48px;\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  z-index: 2;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n  border-color: #667eea;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  background: #f8f9fa;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn.scroll-left[_ngcontent-%COMP%] {\\n  left: -24px;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn.scroll-right[_ngcontent-%COMP%] {\\n  right: -24px;\\n}\\n@media (max-width: 768px) {\\n  .trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  min-width: 280px;\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:focus {\\n  outline: 3px solid #667eea;\\n  outline-offset: 2px;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 300px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ff6b6b, #ff8e53);\\n  color: white;\\n  padding: 0.5rem 0.75rem;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #e91e63;\\n  color: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  opacity: 0;\\n  transform: translateY(10px);\\n  transition: all 0.3s ease;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]   .fa-heart.wishlisted[_ngcontent-%COMP%] {\\n  color: #e91e63;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 0.25rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 0.5rem 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.125rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%] {\\n  color: #ddd;\\n  font-size: 0.8rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.8rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #262626;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #8e8e8e;\\n  text-decoration: line-through;\\n  margin-left: 0.5rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 0.75rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.75rem;\\n  color: #666;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n  width: 12px;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.in-stock[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.low-stock[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.out-of-stock[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem 1rem;\\n  color: #8e8e8e;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  margin-bottom: 1rem;\\n  color: #ddd;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #666;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n}\\n.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin: 0 auto;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .trending-now-section[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%] {\\n    background: rgba(30, 30, 30, 0.9);\\n    color: #ffffff;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n    border-color: #333;\\n    color: #ffffff;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]:hover {\\n    background: #667eea;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .trending-now-section[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover {\\n    background: rgba(102, 126, 234, 0.2);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "TrendingNowComponent_button_10_Template_button_click_0_listener", "filter_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "setActiveFilter", "value", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "activeFilter", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ɵɵtemplate", "TrendingNowComponent_div_15_div_2_Template", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "product_r5", "pricing", "discountPercentage", "star_r6", "rating", "average", "TrendingNowComponent_div_16_div_3_div_20_i_2_Template", "getStarArray", "count", "ɵɵpipeBind2", "mrp", "availability", "totalStock", "TrendingNowComponent_div_16_div_3_Template_div_click_0_listener", "_r4", "navigateToProduct", "TrendingNowComponent_div_16_div_3_Template_div_keydown_enter_0_listener", "TrendingNowComponent_div_16_div_3_Template_div_keydown_space_0_listener", "TrendingNowComponent_div_16_div_3_Template_img_error_2_listener", "$event", "onImageError", "TrendingNowComponent_div_16_div_3_div_7_Template", "TrendingNowComponent_div_16_div_3_Template_button_click_9_listener", "toggleWishlist", "stopPropagation", "TrendingNowComponent_div_16_div_3_Template_button_click_11_listener", "quickView", "TrendingNowComponent_div_16_div_3_Template_button_click_13_listener", "addToCart", "TrendingNowComponent_div_16_div_3_div_20_Template", "TrendingNowComponent_div_16_div_3_span_25_Template", "TrendingNowComponent_div_16_div_3_span_40_Template", "TrendingNowComponent_div_16_div_3_span_41_Template", "TrendingNowComponent_div_16_div_3_span_42_Template", "getProductImage", "ɵɵsanitizeUrl", "name", "trendingScore", "isInWishlist", "_id", "status", "ɵɵtextInterpolate", "brand", "sellingPrice", "formatNumber", "analytics", "views", "likes", "purchases", "ɵɵclassMap", "TrendingNowComponent_div_16_button_4_Template_button_click_0_listener", "_r7", "scrollProducts", "canScrollLeft", "TrendingNowComponent_div_16_button_5_Template_button_click_0_listener", "_r8", "canScrollRight", "TrendingNowComponent_div_16_div_3_Template", "TrendingNowComponent_div_16_button_4_Template", "TrendingNowComponent_div_16_button_5_Template", "products", "trackByProductId", "showScrollButtons", "TrendingNowComponent_div_17_Template_button_click_7_listener", "_r9", "loadProducts", "TrendingNowComponent", "constructor", "shopDataService", "router", "maxProducts", "showHeader", "isLoading", "wishlistItems", "Set", "trendFilters", "destroy$", "ngOnInit", "loadWishlist", "ngOnDestroy", "next", "complete", "loadTrendingProducts", "pipe", "subscribe", "error", "console", "filter", "product", "trackProductClick", "navigate", "viewAllTrending", "has", "delete", "add", "saveWishlist", "productId", "log", "showAddToCartSuccess", "direction", "container", "document", "querySelector", "scrollAmount", "currentScroll", "scrollLeft", "scrollTo", "left", "behavior", "setTimeout", "updateScrollButtons", "primaryImage", "images", "find", "img", "isPrimary", "url", "Array", "fill", "map", "_", "i", "num", "toFixed", "toString", "event", "target", "src", "index", "scrollWidth", "clientWidth", "window", "gtag", "product_name", "product_id", "product_brand", "product_category", "category", "event_category", "stored", "localStorage", "getItem", "wishlist", "JSON", "parse", "e", "warn", "setItem", "stringify", "from", "ɵɵdirectiveInject", "i1", "ShopDataService", "i2", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingNowComponent_Template", "rf", "ctx", "TrendingNowComponent_button_10_Template", "TrendingNowComponent_Template_button_click_11_listener", "TrendingNowComponent_div_15_Template", "TrendingNowComponent_div_16_Template", "TrendingNowComponent_div_17_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\trending-now\\trending-now.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { ShopDataService, TrendingProduct } from '../../../core/services/shop-data.service';\n\n@Component({\n  selector: 'app-trending-now',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"trending-now-section\">\n      <!-- Section Header -->\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <h2 class=\"section-title\">\n            <i class=\"fas fa-fire\"></i>\n            Trending Now\n          </h2>\n          <p class=\"section-subtitle\">Hot picks that everyone's talking about</p>\n        </div>\n        <div class=\"header-actions\">\n          <div class=\"trend-filters\">\n            <button \n              *ngFor=\"let filter of trendFilters\"\n              class=\"filter-btn\"\n              [class.active]=\"activeFilter === filter.value\"\n              (click)=\"setActiveFilter(filter.value)\">\n              {{ filter.label }}\n            </button>\n          </div>\n          <button class=\"view-all-btn\" (click)=\"viewAllTrending()\">\n            <span>View All</span>\n            <i class=\"fas fa-arrow-right\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Loading State -->\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <div class=\"products-scroll\">\n          <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"product-card-skeleton\">\n            <div class=\"skeleton-image\"></div>\n            <div class=\"skeleton-content\">\n              <div class=\"skeleton-text\"></div>\n              <div class=\"skeleton-text short\"></div>\n              <div class=\"skeleton-text\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Products Scroll -->\n      <div *ngIf=\"!isLoading && products.length > 0\" class=\"products-container\">\n        <div class=\"products-scroll\" #productsScroll>\n          <div \n            *ngFor=\"let product of products; trackBy: trackByProductId\" \n            class=\"product-card\"\n            (click)=\"navigateToProduct(product)\"\n            [attr.aria-label]=\"'View ' + product.name\"\n            tabindex=\"0\"\n            (keydown.enter)=\"navigateToProduct(product)\"\n            (keydown.space)=\"navigateToProduct(product)\">\n            \n            <!-- Product Image -->\n            <div class=\"product-image-container\">\n              <img \n                [src]=\"getProductImage(product)\" \n                [alt]=\"product.name\"\n                class=\"product-image\"\n                loading=\"lazy\"\n                (error)=\"onImageError($event)\">\n              \n              <!-- Trending Badge -->\n              <div class=\"trending-badge\">\n                <i class=\"fas fa-fire\"></i>\n                <span>{{ product.trendingScore }}%</span>\n              </div>\n              \n              <!-- Discount Badge -->\n              <div class=\"discount-badge\" *ngIf=\"product.pricing.discountPercentage > 0\">\n                {{ product.pricing.discountPercentage }}% OFF\n              </div>\n              \n              <!-- Quick Actions -->\n              <div class=\"quick-actions\">\n                <button \n                  class=\"quick-action-btn\"\n                  (click)=\"toggleWishlist(product); $event.stopPropagation()\"\n                  [attr.aria-label]=\"'Add to wishlist'\">\n                  <i class=\"fas fa-heart\" [class.wishlisted]=\"isInWishlist(product._id)\"></i>\n                </button>\n                <button \n                  class=\"quick-action-btn\"\n                  (click)=\"quickView(product); $event.stopPropagation()\"\n                  [attr.aria-label]=\"'Quick view'\">\n                  <i class=\"fas fa-eye\"></i>\n                </button>\n                <button \n                  class=\"quick-action-btn\"\n                  (click)=\"addToCart(product); $event.stopPropagation()\"\n                  [attr.aria-label]=\"'Add to cart'\"\n                  [disabled]=\"product.availability.status !== 'in-stock'\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n              </div>\n            </div>\n\n            <!-- Product Info -->\n            <div class=\"product-info\">\n              <div class=\"product-brand\">{{ product.brand }}</div>\n              <h3 class=\"product-name\">{{ product.name }}</h3>\n              \n              <!-- Rating -->\n              <div class=\"product-rating\" *ngIf=\"product.rating.count > 0\">\n                <div class=\"stars\">\n                  <i *ngFor=\"let star of getStarArray(product.rating.average)\" \n                     class=\"fas fa-star\" \n                     [class.filled]=\"star <= product.rating.average\"></i>\n                </div>\n                <span class=\"rating-text\">({{ product.rating.count }})</span>\n              </div>\n              \n              <!-- Pricing -->\n              <div class=\"product-pricing\">\n                <span class=\"current-price\">₹{{ product.pricing.sellingPrice | number:'1.0-0' }}</span>\n                <span class=\"original-price\" *ngIf=\"product.pricing.discountPercentage > 0\">\n                  ₹{{ product.pricing.mrp | number:'1.0-0' }}\n                </span>\n              </div>\n              \n              <!-- Trending Stats -->\n              <div class=\"trending-stats\">\n                <div class=\"stat-item\">\n                  <i class=\"fas fa-eye\"></i>\n                  <span>{{ formatNumber(product.analytics.views) }}</span>\n                </div>\n                <div class=\"stat-item\">\n                  <i class=\"fas fa-heart\"></i>\n                  <span>{{ formatNumber(product.analytics.likes) }}</span>\n                </div>\n                <div class=\"stat-item\">\n                  <i class=\"fas fa-shopping-bag\"></i>\n                  <span>{{ formatNumber(product.analytics.purchases) }}</span>\n                </div>\n              </div>\n              \n              <!-- Stock Status -->\n              <div class=\"stock-status\" [class]=\"product.availability.status\">\n                <span *ngIf=\"product.availability.status === 'in-stock'\">\n                  <i class=\"fas fa-check-circle\"></i>\n                  In Stock ({{ product.availability.totalStock }})\n                </span>\n                <span *ngIf=\"product.availability.status === 'low-stock'\">\n                  <i class=\"fas fa-exclamation-triangle\"></i>\n                  Only {{ product.availability.totalStock }} left\n                </span>\n                <span *ngIf=\"product.availability.status === 'out-of-stock'\">\n                  <i class=\"fas fa-times-circle\"></i>\n                  Out of Stock\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- Scroll Navigation -->\n        <button class=\"scroll-btn scroll-left\" \n                (click)=\"scrollProducts('left')\"\n                [disabled]=\"!canScrollLeft\"\n                *ngIf=\"showScrollButtons\">\n          <i class=\"fas fa-chevron-left\"></i>\n        </button>\n        <button class=\"scroll-btn scroll-right\" \n                (click)=\"scrollProducts('right')\"\n                [disabled]=\"!canScrollRight\"\n                *ngIf=\"showScrollButtons\">\n          <i class=\"fas fa-chevron-right\"></i>\n        </button>\n      </div>\n\n      <!-- Empty State -->\n      <div *ngIf=\"!isLoading && products.length === 0\" class=\"empty-state\">\n        <div class=\"empty-icon\">\n          <i class=\"fas fa-fire-extinguisher\"></i>\n        </div>\n        <h3>No Trending Products</h3>\n        <p>Check back later for the hottest trends!</p>\n        <button class=\"retry-btn\" (click)=\"loadProducts()\">\n          <i class=\"fas fa-refresh\"></i>\n          Try Again\n        </button>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./trending-now.component.scss']\n})\nexport class TrendingNowComponent implements OnInit, OnDestroy {\n  @Input() maxProducts: number = 12;\n  @Input() showHeader: boolean = true;\n  @Input() showScrollButtons: boolean = true;\n  \n  products: TrendingProduct[] = [];\n  isLoading: boolean = true;\n  activeFilter: string = 'all';\n  wishlistItems: Set<string> = new Set();\n  canScrollLeft: boolean = false;\n  canScrollRight: boolean = true;\n  \n  trendFilters = [\n    { label: 'All', value: 'all' },\n    { label: 'Today', value: 'today' },\n    { label: 'This Week', value: 'week' },\n    { label: 'This Month', value: 'month' }\n  ];\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private shopDataService: ShopDataService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadProducts();\n    this.loadWishlist();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadProducts(): void {\n    this.isLoading = true;\n    \n    this.shopDataService.loadTrendingProducts(this.maxProducts)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (products) => {\n          this.products = products;\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading trending products:', error);\n          this.isLoading = false;\n        }\n      });\n  }\n\n  setActiveFilter(filter: string): void {\n    this.activeFilter = filter;\n    // In a real implementation, this would filter the products\n    // For now, we'll just reload the products\n    this.loadProducts();\n  }\n\n  navigateToProduct(product: TrendingProduct): void {\n    // Track product click\n    this.trackProductClick(product);\n    \n    // Navigate to product detail page\n    this.router.navigate(['/product', product._id]);\n  }\n\n  viewAllTrending(): void {\n    this.router.navigate(['/shop/trending']);\n  }\n\n  toggleWishlist(product: TrendingProduct): void {\n    if (this.wishlistItems.has(product._id)) {\n      this.wishlistItems.delete(product._id);\n    } else {\n      this.wishlistItems.add(product._id);\n    }\n    this.saveWishlist();\n  }\n\n  isInWishlist(productId: string): boolean {\n    return this.wishlistItems.has(productId);\n  }\n\n  quickView(product: TrendingProduct): void {\n    // Implement quick view modal\n    console.log('Quick view:', product);\n  }\n\n  addToCart(product: TrendingProduct): void {\n    if (product.availability.status !== 'in-stock') {\n      return;\n    }\n    \n    // Add to cart logic\n    console.log('Add to cart:', product);\n    \n    // Show success message\n    this.showAddToCartSuccess(product);\n  }\n\n  scrollProducts(direction: 'left' | 'right'): void {\n    const container = document.querySelector('.products-scroll') as HTMLElement;\n    if (!container) return;\n    \n    const scrollAmount = 300;\n    const currentScroll = container.scrollLeft;\n    \n    if (direction === 'left') {\n      container.scrollTo({\n        left: currentScroll - scrollAmount,\n        behavior: 'smooth'\n      });\n    } else {\n      container.scrollTo({\n        left: currentScroll + scrollAmount,\n        behavior: 'smooth'\n      });\n    }\n    \n    // Update scroll button states\n    setTimeout(() => {\n      this.updateScrollButtons();\n    }, 300);\n  }\n\n  getProductImage(product: TrendingProduct): string {\n    const primaryImage = product.images.find(img => img.isPrimary);\n    return primaryImage ? primaryImage.url : product.images[0]?.url || '';\n  }\n\n  getStarArray(rating: number): number[] {\n    return Array(5).fill(0).map((_, i) => i + 1);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  onImageError(event: any): void {\n    event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';\n  }\n\n  trackByProductId(index: number, product: TrendingProduct): string {\n    return product._id;\n  }\n\n  private updateScrollButtons(): void {\n    const container = document.querySelector('.products-scroll') as HTMLElement;\n    if (!container) return;\n    \n    this.canScrollLeft = container.scrollLeft > 0;\n    this.canScrollRight = container.scrollLeft < (container.scrollWidth - container.clientWidth);\n  }\n\n  private trackProductClick(product: TrendingProduct): void {\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'product_click', {\n        product_name: product.name,\n        product_id: product._id,\n        product_brand: product.brand,\n        product_category: product.category,\n        event_category: 'engagement'\n      });\n    }\n  }\n\n  private loadWishlist(): void {\n    const stored = localStorage.getItem('dfashion_wishlist');\n    if (stored) {\n      try {\n        const wishlist = JSON.parse(stored);\n        this.wishlistItems = new Set(wishlist);\n      } catch (e) {\n        console.warn('Failed to load wishlist');\n      }\n    }\n  }\n\n  private saveWishlist(): void {\n    localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));\n  }\n\n  private showAddToCartSuccess(product: TrendingProduct): void {\n    // Show a toast or notification\n    console.log(`${product.name} added to cart!`);\n\n    // In a real implementation, you might show a toast notification\n    // this.toastService.show(`${product.name} added to cart!`, 'success');\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;IAoB7BC,EAAA,CAAAC,cAAA,iBAI0C;IAAxCD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAP,SAAA,CAAAQ,KAAA,CAA6B;IAAA,EAAC;IACvCZ,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAHPd,EAAA,CAAAe,WAAA,WAAAP,MAAA,CAAAQ,YAAA,KAAAZ,SAAA,CAAAQ,KAAA,CAA8C;IAE9CZ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAd,SAAA,CAAAe,KAAA,MACF;;;;;IAYFnB,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAoB,SAAA,cAAkC;IAClCpB,EAAA,CAAAC,cAAA,cAA8B;IAG5BD,EAFA,CAAAoB,SAAA,cAAiC,cACM,cACN;IAErCpB,EADE,CAAAc,YAAA,EAAM,EACF;;;;;IARRd,EADF,CAAAC,cAAA,cAAiD,cAClB;IAC3BD,EAAA,CAAAqB,UAAA,IAAAC,0CAAA,kBAAsE;IAS1EtB,EADE,CAAAc,YAAA,EAAM,EACF;;;IAToBd,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAgB;;;;;IAuClCzB,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAQ,UAAA,CAAAC,OAAA,CAAAC,kBAAA,WACF;;;;;IAkCI5B,EAAA,CAAAoB,SAAA,YAEuD;;;;;IAApDpB,EAAA,CAAAe,WAAA,WAAAc,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;;;;;IAHpD/B,EADF,CAAAC,cAAA,cAA6D,cACxC;IACjBD,EAAA,CAAAqB,UAAA,IAAAW,qDAAA,gBAEmD;IACrDhC,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IACxDb,EADwD,CAAAc,YAAA,EAAO,EACzD;;;;;IALkBd,EAAA,CAAAiB,SAAA,GAAuC;IAAvCjB,EAAA,CAAAuB,UAAA,YAAAf,MAAA,CAAAyB,YAAA,CAAAP,UAAA,CAAAI,MAAA,CAAAC,OAAA,EAAuC;IAInC/B,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,kBAAA,MAAAQ,UAAA,CAAAI,MAAA,CAAAI,KAAA,MAA4B;;;;;IAMtDlC,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAa,MAAA,GACF;;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IADLd,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,YAAAlB,EAAA,CAAAmC,WAAA,OAAAT,UAAA,CAAAC,OAAA,CAAAS,GAAA,gBACF;;;;;IAqBApC,EAAA,CAAAC,cAAA,WAAyD;IACvDD,EAAA,CAAAoB,SAAA,YAAmC;IACnCpB,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IADLd,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,gBAAAQ,UAAA,CAAAW,YAAA,CAAAC,UAAA,OACF;;;;;IACAtC,EAAA,CAAAC,cAAA,WAA0D;IACxDD,EAAA,CAAAoB,SAAA,YAA2C;IAC3CpB,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IADLd,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,WAAAQ,UAAA,CAAAW,YAAA,CAAAC,UAAA,WACF;;;;;IACAtC,EAAA,CAAAC,cAAA,WAA6D;IAC3DD,EAAA,CAAAoB,SAAA,YAAmC;IACnCpB,EAAA,CAAAa,MAAA,qBACF;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;;IAzGbd,EAAA,CAAAC,cAAA,cAO+C;IAA7CD,EAJA,CAAAE,UAAA,mBAAAqC,gEAAA;MAAA,MAAAb,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,iBAAA,CAAAf,UAAA,CAA0B;IAAA,EAAC,2BAAAgB,wEAAA;MAAA,MAAAhB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAGnBF,MAAA,CAAAiC,iBAAA,CAAAf,UAAA,CAA0B;IAAA,EAAC,2BAAAiB,wEAAA;MAAA,MAAAjB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAC3BF,MAAA,CAAAiC,iBAAA,CAAAf,UAAA,CAA0B;IAAA,EAAC;IAI1C1B,EADF,CAAAC,cAAA,cAAqC,cAMF;IAA/BD,EAAA,CAAAE,UAAA,mBAAA0C,gEAAAC,MAAA;MAAA7C,EAAA,CAAAK,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsC,YAAA,CAAAD,MAAA,CAAoB;IAAA,EAAC;IALhC7C,EAAA,CAAAc,YAAA,EAKiC;IAGjCd,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAoB,SAAA,WAA2B;IAC3BpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IACpCb,EADoC,CAAAc,YAAA,EAAO,EACrC;IAGNd,EAAA,CAAAqB,UAAA,IAAA0B,gDAAA,kBAA2E;IAMzE/C,EADF,CAAAC,cAAA,cAA2B,iBAIe;IADtCD,EAAA,CAAAE,UAAA,mBAAA8C,mEAAAH,MAAA;MAAA,MAAAnB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAASD,MAAA,CAAAyC,cAAA,CAAAvB,UAAA,CAAuB;MAAA,OAAA1B,EAAA,CAAAU,WAAA,CAAEmC,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAE3DlD,EAAA,CAAAoB,SAAA,aAA2E;IAC7EpB,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAGmC;IADjCD,EAAA,CAAAE,UAAA,mBAAAiD,oEAAAN,MAAA;MAAA,MAAAnB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAASD,MAAA,CAAA4C,SAAA,CAAA1B,UAAA,CAAkB;MAAA,OAAA1B,EAAA,CAAAU,WAAA,CAAEmC,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAEtDlD,EAAA,CAAAoB,SAAA,aAA0B;IAC5BpB,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAI0D;IAFxDD,EAAA,CAAAE,UAAA,mBAAAmD,oEAAAR,MAAA;MAAA,MAAAnB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAASD,MAAA,CAAA8C,SAAA,CAAA5B,UAAA,CAAkB;MAAA,OAAA1B,EAAA,CAAAU,WAAA,CAAEmC,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAGtDlD,EAAA,CAAAoB,SAAA,aAAoC;IAG1CpB,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAIJd,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAa,MAAA,IAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACpDd,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAa,MAAA,IAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAGhDd,EAAA,CAAAqB,UAAA,KAAAkC,iDAAA,kBAA6D;IAW3DvD,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAa,MAAA,IAAoD;;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACvFd,EAAA,CAAAqB,UAAA,KAAAmC,kDAAA,mBAA4E;IAG9ExD,EAAA,CAAAc,YAAA,EAAM;IAIJd,EADF,CAAAC,cAAA,eAA4B,eACH;IACrBD,EAAA,CAAAoB,SAAA,aAA0B;IAC1BpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA2C;IACnDb,EADmD,CAAAc,YAAA,EAAO,EACpD;IACNd,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAoB,SAAA,aAA4B;IAC5BpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA2C;IACnDb,EADmD,CAAAc,YAAA,EAAO,EACpD;IACNd,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAoB,SAAA,aAAmC;IACnCpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA+C;IAEzDb,EAFyD,CAAAc,YAAA,EAAO,EACxD,EACF;IAGNd,EAAA,CAAAC,cAAA,eAAgE;IAS9DD,EARA,CAAAqB,UAAA,KAAAoC,kDAAA,mBAAyD,KAAAC,kDAAA,mBAIC,KAAAC,kDAAA,mBAIG;IAMnE3D,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;;;;IAhGAd,EAAA,CAAAiB,SAAA,GAAgC;IAChCjB,EADA,CAAAuB,UAAA,QAAAf,MAAA,CAAAoD,eAAA,CAAAlC,UAAA,GAAA1B,EAAA,CAAA6D,aAAA,CAAgC,QAAAnC,UAAA,CAAAoC,IAAA,CACZ;IAQd9D,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,kBAAA,KAAAQ,UAAA,CAAAqC,aAAA,MAA4B;IAIP/D,EAAA,CAAAiB,SAAA,EAA4C;IAA5CjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAC,OAAA,CAAAC,kBAAA,KAA4C;IASrE5B,EAAA,CAAAiB,SAAA,GAAqC;;IACbjB,EAAA,CAAAiB,SAAA,EAA8C;IAA9CjB,EAAA,CAAAe,WAAA,eAAAP,MAAA,CAAAwD,YAAA,CAAAtC,UAAA,CAAAuC,GAAA,EAA8C;IAKtEjE,EAAA,CAAAiB,SAAA,EAAgC;;IAOhCjB,EAAA,CAAAiB,SAAA,GAAuD;IAAvDjB,EAAA,CAAAuB,UAAA,aAAAG,UAAA,CAAAW,YAAA,CAAA6B,MAAA,gBAAuD;;IAQhClE,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAmE,iBAAA,CAAAzC,UAAA,CAAA0C,KAAA,CAAmB;IACrBpE,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAmE,iBAAA,CAAAzC,UAAA,CAAAoC,IAAA,CAAkB;IAGd9D,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAI,MAAA,CAAAI,KAAA,KAA8B;IAW7BlC,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAkB,kBAAA,WAAAlB,EAAA,CAAAmC,WAAA,SAAAT,UAAA,CAAAC,OAAA,CAAA0C,YAAA,eAAoD;IAClDrE,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAC,OAAA,CAAAC,kBAAA,KAA4C;IASlE5B,EAAA,CAAAiB,SAAA,GAA2C;IAA3CjB,EAAA,CAAAmE,iBAAA,CAAA3D,MAAA,CAAA8D,YAAA,CAAA5C,UAAA,CAAA6C,SAAA,CAAAC,KAAA,EAA2C;IAI3CxE,EAAA,CAAAiB,SAAA,GAA2C;IAA3CjB,EAAA,CAAAmE,iBAAA,CAAA3D,MAAA,CAAA8D,YAAA,CAAA5C,UAAA,CAAA6C,SAAA,CAAAE,KAAA,EAA2C;IAI3CzE,EAAA,CAAAiB,SAAA,GAA+C;IAA/CjB,EAAA,CAAAmE,iBAAA,CAAA3D,MAAA,CAAA8D,YAAA,CAAA5C,UAAA,CAAA6C,SAAA,CAAAG,SAAA,EAA+C;IAK/B1E,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAA2E,UAAA,CAAAjD,UAAA,CAAAW,YAAA,CAAA6B,MAAA,CAAqC;IACtDlE,EAAA,CAAAiB,SAAA,EAAgD;IAAhDjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAW,YAAA,CAAA6B,MAAA,gBAAgD;IAIhDlE,EAAA,CAAAiB,SAAA,EAAiD;IAAjDjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAW,YAAA,CAAA6B,MAAA,iBAAiD;IAIjDlE,EAAA,CAAAiB,SAAA,EAAoD;IAApDjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAW,YAAA,CAAA6B,MAAA,oBAAoD;;;;;;IAUnElE,EAAA,CAAAC,cAAA,iBAGkC;IAF1BD,EAAA,CAAAE,UAAA,mBAAA0E,sEAAA;MAAA5E,EAAA,CAAAK,aAAA,CAAAwE,GAAA;MAAA,MAAArE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsE,cAAA,CAAe,MAAM,CAAC;IAAA,EAAC;IAGtC9E,EAAA,CAAAoB,SAAA,YAAmC;IACrCpB,EAAA,CAAAc,YAAA,EAAS;;;;IAHDd,EAAA,CAAAuB,UAAA,cAAAf,MAAA,CAAAuE,aAAA,CAA2B;;;;;;IAInC/E,EAAA,CAAAC,cAAA,iBAGkC;IAF1BD,EAAA,CAAAE,UAAA,mBAAA8E,sEAAA;MAAAhF,EAAA,CAAAK,aAAA,CAAA4E,GAAA;MAAA,MAAAzE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsE,cAAA,CAAe,OAAO,CAAC;IAAA,EAAC;IAGvC9E,EAAA,CAAAoB,SAAA,YAAoC;IACtCpB,EAAA,CAAAc,YAAA,EAAS;;;;IAHDd,EAAA,CAAAuB,UAAA,cAAAf,MAAA,CAAA0E,cAAA,CAA4B;;;;;IAzHpClF,EADF,CAAAC,cAAA,cAA0E,iBAC3B;IAC3CD,EAAA,CAAAqB,UAAA,IAAA8D,0CAAA,oBAO+C;IAsGjDnF,EAAA,CAAAc,YAAA,EAAM;IASNd,EANA,CAAAqB,UAAA,IAAA+D,6CAAA,qBAGkC,IAAAC,6CAAA,qBAMA;IAGpCrF,EAAA,CAAAc,YAAA,EAAM;;;;IA3HoBd,EAAA,CAAAiB,SAAA,GAAa;IAAAjB,EAAb,CAAAuB,UAAA,YAAAf,MAAA,CAAA8E,QAAA,CAAa,iBAAA9E,MAAA,CAAA+E,gBAAA,CAAyB;IAkHrDvF,EAAA,CAAAiB,SAAA,EAAuB;IAAvBjB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAgF,iBAAA,CAAuB;IAMvBxF,EAAA,CAAAiB,SAAA,EAAuB;IAAvBjB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAgF,iBAAA,CAAuB;;;;;;IAOhCxF,EADF,CAAAC,cAAA,cAAqE,cAC3C;IACtBD,EAAA,CAAAoB,SAAA,YAAwC;IAC1CpB,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,2BAAoB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC7Bd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,+CAAwC;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAC/Cd,EAAA,CAAAC,cAAA,iBAAmD;IAAzBD,EAAA,CAAAE,UAAA,mBAAAuF,6DAAA;MAAAzF,EAAA,CAAAK,aAAA,CAAAqF,GAAA;MAAA,MAAAlF,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmF,YAAA,EAAc;IAAA,EAAC;IAChD3F,EAAA,CAAAoB,SAAA,YAA8B;IAC9BpB,EAAA,CAAAa,MAAA,kBACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;;;AAKZ,OAAM,MAAO8E,oBAAoB;EAqB/BC,YACUC,eAAgC,EAChCC,MAAc;IADd,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAtBP,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,UAAU,GAAY,IAAI;IAC1B,KAAAT,iBAAiB,GAAY,IAAI;IAE1C,KAAAF,QAAQ,GAAsB,EAAE;IAChC,KAAAY,SAAS,GAAY,IAAI;IACzB,KAAAlF,YAAY,GAAW,KAAK;IAC5B,KAAAmF,aAAa,GAAgB,IAAIC,GAAG,EAAE;IACtC,KAAArB,aAAa,GAAY,KAAK;IAC9B,KAAAG,cAAc,GAAY,IAAI;IAE9B,KAAAmB,YAAY,GAAG,CACb;MAAElF,KAAK,EAAE,KAAK;MAAEP,KAAK,EAAE;IAAK,CAAE,EAC9B;MAAEO,KAAK,EAAE,OAAO;MAAEP,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAM,CAAE,EACrC;MAAEO,KAAK,EAAE,YAAY;MAAEP,KAAK,EAAE;IAAO,CAAE,CACxC;IAEO,KAAA0F,QAAQ,GAAG,IAAIxG,OAAO,EAAQ;EAKnC;EAEHyG,QAAQA,CAAA;IACN,IAAI,CAACZ,YAAY,EAAE;IACnB,IAAI,CAACa,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEAhB,YAAYA,CAAA;IACV,IAAI,CAACO,SAAS,GAAG,IAAI;IAErB,IAAI,CAACJ,eAAe,CAACc,oBAAoB,CAAC,IAAI,CAACZ,WAAW,CAAC,CACxDa,IAAI,CAAC9G,SAAS,CAAC,IAAI,CAACuG,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTJ,IAAI,EAAGpB,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACY,SAAS,GAAG,KAAK;MACxB,CAAC;MACDa,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAvF,eAAeA,CAACsG,MAAc;IAC5B,IAAI,CAACjG,YAAY,GAAGiG,MAAM;IAC1B;IACA;IACA,IAAI,CAACtB,YAAY,EAAE;EACrB;EAEAlD,iBAAiBA,CAACyE,OAAwB;IACxC;IACA,IAAI,CAACC,iBAAiB,CAACD,OAAO,CAAC;IAE/B;IACA,IAAI,CAACnB,MAAM,CAACqB,QAAQ,CAAC,CAAC,UAAU,EAAEF,OAAO,CAACjD,GAAG,CAAC,CAAC;EACjD;EAEAoD,eAAeA,CAAA;IACb,IAAI,CAACtB,MAAM,CAACqB,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAnE,cAAcA,CAACiE,OAAwB;IACrC,IAAI,IAAI,CAACf,aAAa,CAACmB,GAAG,CAACJ,OAAO,CAACjD,GAAG,CAAC,EAAE;MACvC,IAAI,CAACkC,aAAa,CAACoB,MAAM,CAACL,OAAO,CAACjD,GAAG,CAAC;KACvC,MAAM;MACL,IAAI,CAACkC,aAAa,CAACqB,GAAG,CAACN,OAAO,CAACjD,GAAG,CAAC;;IAErC,IAAI,CAACwD,YAAY,EAAE;EACrB;EAEAzD,YAAYA,CAAC0D,SAAiB;IAC5B,OAAO,IAAI,CAACvB,aAAa,CAACmB,GAAG,CAACI,SAAS,CAAC;EAC1C;EAEAtE,SAASA,CAAC8D,OAAwB;IAChC;IACAF,OAAO,CAACW,GAAG,CAAC,aAAa,EAAET,OAAO,CAAC;EACrC;EAEA5D,SAASA,CAAC4D,OAAwB;IAChC,IAAIA,OAAO,CAAC7E,YAAY,CAAC6B,MAAM,KAAK,UAAU,EAAE;MAC9C;;IAGF;IACA8C,OAAO,CAACW,GAAG,CAAC,cAAc,EAAET,OAAO,CAAC;IAEpC;IACA,IAAI,CAACU,oBAAoB,CAACV,OAAO,CAAC;EACpC;EAEApC,cAAcA,CAAC+C,SAA2B;IACxC,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAgB;IAC3E,IAAI,CAACF,SAAS,EAAE;IAEhB,MAAMG,YAAY,GAAG,GAAG;IACxB,MAAMC,aAAa,GAAGJ,SAAS,CAACK,UAAU;IAE1C,IAAIN,SAAS,KAAK,MAAM,EAAE;MACxBC,SAAS,CAACM,QAAQ,CAAC;QACjBC,IAAI,EAAEH,aAAa,GAAGD,YAAY;QAClCK,QAAQ,EAAE;OACX,CAAC;KACH,MAAM;MACLR,SAAS,CAACM,QAAQ,CAAC;QACjBC,IAAI,EAAEH,aAAa,GAAGD,YAAY;QAClCK,QAAQ,EAAE;OACX,CAAC;;IAGJ;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA5E,eAAeA,CAACsD,OAAwB;IACtC,MAAMuB,YAAY,GAAGvB,OAAO,CAACwB,MAAM,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC;IAC9D,OAAOJ,YAAY,GAAGA,YAAY,CAACK,GAAG,GAAG5B,OAAO,CAACwB,MAAM,CAAC,CAAC,CAAC,EAAEI,GAAG,IAAI,EAAE;EACvE;EAEA7G,YAAYA,CAACH,MAAc;IACzB,OAAOiH,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;EAC9C;EAEA7E,YAAYA,CAAC8E,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAxG,YAAYA,CAACyG,KAAU;IACrBA,KAAK,CAACC,MAAM,CAACC,GAAG,GAAG,mEAAmE;EACxF;EAEAlE,gBAAgBA,CAACmE,KAAa,EAAExC,OAAwB;IACtD,OAAOA,OAAO,CAACjD,GAAG;EACpB;EAEQuE,mBAAmBA,CAAA;IACzB,MAAMV,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAgB;IAC3E,IAAI,CAACF,SAAS,EAAE;IAEhB,IAAI,CAAC/C,aAAa,GAAG+C,SAAS,CAACK,UAAU,GAAG,CAAC;IAC7C,IAAI,CAACjD,cAAc,GAAG4C,SAAS,CAACK,UAAU,GAAIL,SAAS,CAAC6B,WAAW,GAAG7B,SAAS,CAAC8B,WAAY;EAC9F;EAEQzC,iBAAiBA,CAACD,OAAwB;IAChD,IAAI,OAAQ2C,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE;QAC7CC,YAAY,EAAE7C,OAAO,CAACpD,IAAI;QAC1BkG,UAAU,EAAE9C,OAAO,CAACjD,GAAG;QACvBgG,aAAa,EAAE/C,OAAO,CAAC9C,KAAK;QAC5B8F,gBAAgB,EAAEhD,OAAO,CAACiD,QAAQ;QAClCC,cAAc,EAAE;OACjB,CAAC;;EAEN;EAEQ5D,YAAYA,CAAA;IAClB,MAAM6D,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACxD,IAAIF,MAAM,EAAE;MACV,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;QACnC,IAAI,CAAClE,aAAa,GAAG,IAAIC,GAAG,CAACoE,QAAQ,CAAC;OACvC,CAAC,OAAOG,CAAC,EAAE;QACV3D,OAAO,CAAC4D,IAAI,CAAC,yBAAyB,CAAC;;;EAG7C;EAEQnD,YAAYA,CAAA;IAClB6C,YAAY,CAACO,OAAO,CAAC,mBAAmB,EAAEJ,IAAI,CAACK,SAAS,CAAC/B,KAAK,CAACgC,IAAI,CAAC,IAAI,CAAC5E,aAAa,CAAC,CAAC,CAAC;EAC3F;EAEQyB,oBAAoBA,CAACV,OAAwB;IACnD;IACAF,OAAO,CAACW,GAAG,CAAC,GAAGT,OAAO,CAACpD,IAAI,iBAAiB,CAAC;IAE7C;IACA;EACF;;;uBAnMW8B,oBAAoB,EAAA5F,EAAA,CAAAgL,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAlL,EAAA,CAAAgL,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBxF,oBAAoB;MAAAyF,SAAA;MAAAC,MAAA;QAAAtF,WAAA;QAAAC,UAAA;QAAAT,iBAAA;MAAA;MAAA+F,UAAA;MAAAC,QAAA,GAAAxL,EAAA,CAAAyL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtLvB/L,EAJN,CAAAC,cAAA,aAAkC,aAEJ,aACE,YACA;UACxBD,EAAA,CAAAoB,SAAA,WAA2B;UAC3BpB,EAAA,CAAAa,MAAA,qBACF;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACLd,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAa,MAAA,8CAAuC;UACrEb,EADqE,CAAAc,YAAA,EAAI,EACnE;UAEJd,EADF,CAAAC,cAAA,aAA4B,aACC;UACzBD,EAAA,CAAAqB,UAAA,KAAA4K,uCAAA,oBAI0C;UAG5CjM,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,kBAAyD;UAA5BD,EAAA,CAAAE,UAAA,mBAAAgM,uDAAA;YAAA,OAASF,GAAA,CAAA3E,eAAA,EAAiB;UAAA,EAAC;UACtDrH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAO;UACrBd,EAAA,CAAAoB,SAAA,aAAkC;UAGxCpB,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAkJNd,EA/IA,CAAAqB,UAAA,KAAA8K,oCAAA,kBAAiD,KAAAC,oCAAA,kBAcyB,KAAAC,oCAAA,mBAiIL;UAWvErM,EAAA,CAAAc,YAAA,EAAM;;;UAzKuBd,EAAA,CAAAiB,SAAA,IAAe;UAAfjB,EAAA,CAAAuB,UAAA,YAAAyK,GAAA,CAAA3F,YAAA,CAAe;UAepCrG,EAAA,CAAAiB,SAAA,GAAe;UAAfjB,EAAA,CAAAuB,UAAA,SAAAyK,GAAA,CAAA9F,SAAA,CAAe;UAcflG,EAAA,CAAAiB,SAAA,EAAuC;UAAvCjB,EAAA,CAAAuB,UAAA,UAAAyK,GAAA,CAAA9F,SAAA,IAAA8F,GAAA,CAAA1G,QAAA,CAAAgH,MAAA,KAAuC;UAiIvCtM,EAAA,CAAAiB,SAAA,EAAyC;UAAzCjB,EAAA,CAAAuB,UAAA,UAAAyK,GAAA,CAAA9F,SAAA,IAAA8F,GAAA,CAAA1G,QAAA,CAAAgH,MAAA,OAAyC;;;qBA7KzCzM,YAAY,EAAA0M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}