{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/services/product.service\";\nimport * as i3 from \"../../../core/services/cart.service\";\nimport * as i4 from \"../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nfunction ProductDetailComponent_div_0_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 34);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_img_7_Template_img_click_0_listener() {\n      const image_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectImage(image_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r1.selectedImage.url === image_r4.url);\n    i0.ɵɵproperty(\"src\", image_r4.url, i0.ɵɵsanitizeUrl)(\"alt\", image_r4.alt || ctx_r1.product.name);\n  }\n}\nfunction ProductDetailComponent_div_0_i_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const star_r5 = ctx.$implicit;\n    i0.ɵɵclassMap(star_r5);\n  }\n}\nfunction ProductDetailComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(2, 1, ctx_r1.product.originalPrice), \"\");\n  }\n}\nfunction ProductDetailComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.product.discount, \"% OFF\");\n  }\n}\nfunction ProductDetailComponent_div_0_div_25_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_div_25_button_4_Template_button_click_0_listener() {\n      const size_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectSize(size_r7.size));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.selectedSize === size_r7.size)(\"out-of-stock\", size_r7.stock === 0);\n    i0.ɵɵproperty(\"disabled\", size_r7.stock === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", size_r7.size, \" \");\n  }\n}\nfunction ProductDetailComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"h3\");\n    i0.ɵɵtext(2, \"Size\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38);\n    i0.ɵɵtemplate(4, ProductDetailComponent_div_0_div_25_button_4_Template, 2, 6, \"button\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.product.sizes);\n  }\n}\nfunction ProductDetailComponent_div_0_div_26_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_div_26_button_4_Template_button_click_0_listener() {\n      const color_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectColor(color_r9.name));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", color_r9.code);\n    i0.ɵɵclassProp(\"active\", ctx_r1.selectedColor === color_r9.name);\n    i0.ɵɵproperty(\"title\", color_r9.name);\n  }\n}\nfunction ProductDetailComponent_div_0_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"h3\");\n    i0.ɵɵtext(2, \"Color\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtemplate(4, ProductDetailComponent_div_0_div_26_button_4_Template, 1, 5, \"button\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.product.colors);\n  }\n}\nfunction ProductDetailComponent_div_0_div_49_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r10);\n  }\n}\nfunction ProductDetailComponent_div_0_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"h3\");\n    i0.ɵɵtext(2, \"Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, ProductDetailComponent_div_0_div_49_li_4_Template, 2, 1, \"li\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.product.features);\n  }\n}\nfunction ProductDetailComponent_div_0_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"h3\");\n    i0.ɵɵtext(2, \"Material\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.product.material);\n  }\n}\nfunction ProductDetailComponent_div_0_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"h3\");\n    i0.ɵɵtext(2, \"Care Instructions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.product.careInstructions);\n  }\n}\nfunction ProductDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n    i0.ɵɵelement(3, \"img\", 6);\n    i0.ɵɵelementStart(4, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleWishlist());\n    });\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8);\n    i0.ɵɵtemplate(7, ProductDetailComponent_div_0_img_7_Template, 1, 4, \"img\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 10)(9, \"div\", 11)(10, \"h1\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 12);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 14);\n    i0.ɵɵtemplate(16, ProductDetailComponent_div_0_i_16_Template, 1, 2, \"i\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 17)(20, \"div\", 18);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ProductDetailComponent_div_0_div_23_Template, 3, 3, \"div\", 19)(24, ProductDetailComponent_div_0_div_24_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, ProductDetailComponent_div_0_div_25_Template, 5, 1, \"div\", 21)(26, ProductDetailComponent_div_0_div_26_Template, 5, 1, \"div\", 22);\n    i0.ɵɵelementStart(27, \"div\", 23)(28, \"h3\");\n    i0.ɵɵtext(29, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 24)(31, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.decreaseQuantity());\n    });\n    i0.ɵɵtext(32, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 26);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.increaseQuantity());\n    });\n    i0.ɵɵtext(36, \"+\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 27)(38, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(39, \"i\", 29);\n    i0.ɵɵtext(40, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_0_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵtext(42, \" Buy Now \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 31)(44, \"div\", 32)(45, \"h3\");\n    i0.ɵɵtext(46, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(49, ProductDetailComponent_div_0_div_49_Template, 5, 1, \"div\", 33)(50, ProductDetailComponent_div_0_div_50_Template, 5, 1, \"div\", 33)(51, ProductDetailComponent_div_0_div_51_Template, 5, 1, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedImage.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedImage.alt || ctx_r1.product.name);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isInWishlist);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.isInWishlist ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.product.images);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.product.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStars());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.product.rating.count, \" reviews)\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(22, 25, ctx_r1.product.price), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.discount > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.sizes.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.colors.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.quantity <= 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.quantity);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.quantity >= ctx_r1.maxQuantity);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canAddToCart());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canAddToCart());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.product.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.features.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.material);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.careInstructions);\n  }\n}\nfunction ProductDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"div\", 47);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading product details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProductDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Product Not Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ProductDetailComponent_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵtext(8, \"Go Back\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nexport let ProductDetailComponent = /*#__PURE__*/(() => {\n  class ProductDetailComponent {\n    constructor(route, router, productService, cartService, wishlistService) {\n      this.route = route;\n      this.router = router;\n      this.productService = productService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.product = null;\n      this.isLoading = true;\n      this.error = null;\n      this.selectedImage = null;\n      this.selectedSize = '';\n      this.selectedColor = '';\n      this.quantity = 1;\n      this.maxQuantity = 10;\n      this.isInWishlist = false;\n    }\n    ngOnInit() {\n      this.route.params.subscribe(params => {\n        const productId = params['id'];\n        if (productId) {\n          this.loadProduct(productId);\n        }\n      });\n    }\n    loadProduct(id) {\n      this.isLoading = true;\n      this.error = null;\n      this.productService.getProduct(id).subscribe({\n        next: response => {\n          this.product = response.product;\n          this.selectedImage = this.product.images[0];\n          this.isInWishlist = this.wishlistService.isInWishlist(id);\n          this.isLoading = false;\n        },\n        error: error => {\n          this.error = 'Product not found or failed to load';\n          this.isLoading = false;\n          console.error('Product load error:', error);\n        }\n      });\n    }\n    selectImage(image) {\n      this.selectedImage = image;\n    }\n    selectSize(size) {\n      this.selectedSize = size;\n    }\n    selectColor(color) {\n      this.selectedColor = color;\n    }\n    increaseQuantity() {\n      if (this.quantity < this.maxQuantity) {\n        this.quantity++;\n      }\n    }\n    decreaseQuantity() {\n      if (this.quantity > 1) {\n        this.quantity--;\n      }\n    }\n    canAddToCart() {\n      if (!this.product) return false;\n      // Check if size is required and selected\n      if (this.product.sizes.length > 0 && !this.selectedSize) return false;\n      // Check if color is required and selected\n      if (this.product.colors.length > 0 && !this.selectedColor) return false;\n      return true;\n    }\n    addToCart() {\n      if (!this.product || !this.canAddToCart()) return;\n      this.cartService.addToCart(this.product._id, this.quantity, this.selectedSize, this.selectedColor).subscribe({\n        next: response => {\n          if (response.success) {\n            console.log('Added to cart successfully');\n          }\n        },\n        error: error => {\n          console.error('Failed to add to cart:', error);\n        }\n      });\n    }\n    buyNow() {\n      if (!this.product || !this.canAddToCart()) return;\n      this.cartService.addToCart(this.product._id, this.quantity, this.selectedSize, this.selectedColor).subscribe({\n        next: response => {\n          if (response.success) {\n            this.router.navigate(['/shop/checkout']);\n          }\n        },\n        error: error => {\n          console.error('Failed to add to cart:', error);\n        }\n      });\n    }\n    toggleWishlist() {\n      if (!this.product) return;\n      this.wishlistService.toggleWishlist(this.product._id).subscribe({\n        next: () => {\n          this.isInWishlist = !this.isInWishlist;\n        },\n        error: error => {\n          console.error('Wishlist error:', error);\n          // Fallback to offline mode\n          this.wishlistService.toggleWishlistOffline(this.product);\n          this.isInWishlist = !this.isInWishlist;\n        }\n      });\n    }\n    getStars() {\n      const rating = this.product?.rating.average || 0;\n      const stars = [];\n      for (let i = 1; i <= 5; i++) {\n        if (i <= rating) {\n          stars.push('fas fa-star');\n        } else if (i - 0.5 <= rating) {\n          stars.push('fas fa-star-half-alt');\n        } else {\n          stars.push('far fa-star');\n        }\n      }\n      return stars;\n    }\n    goBack() {\n      this.router.navigate(['/']);\n    }\n    static {\n      this.ɵfac = function ProductDetailComponent_Factory(t) {\n        return new (t || ProductDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProductDetailComponent,\n        selectors: [[\"app-product-detail\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 3,\n        consts: [[\"class\", \"product-detail-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"product-detail-container\"], [1, \"product-images\"], [1, \"main-image\"], [1, \"main-product-image\", 3, \"src\", \"alt\"], [1, \"wishlist-btn\", 3, \"click\"], [1, \"image-thumbnails\"], [\"class\", \"thumbnail\", 3, \"src\", \"alt\", \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-info\"], [1, \"product-header\"], [1, \"brand\"], [1, \"rating\"], [1, \"stars\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"discount\", 4, \"ngIf\"], [\"class\", \"size-selection\", 4, \"ngIf\"], [\"class\", \"color-selection\", 4, \"ngIf\"], [1, \"quantity-selection\"], [1, \"quantity-controls\"], [1, \"qty-btn\", 3, \"click\", \"disabled\"], [1, \"quantity\"], [1, \"action-buttons\"], [1, \"btn-add-to-cart\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy-now\", 3, \"click\", \"disabled\"], [1, \"product-details\"], [1, \"detail-section\"], [\"class\", \"detail-section\", 4, \"ngIf\"], [1, \"thumbnail\", 3, \"click\", \"src\", \"alt\"], [1, \"original-price\"], [1, \"discount\"], [1, \"size-selection\"], [1, \"size-options\"], [\"class\", \"size-btn\", 3, \"active\", \"out-of-stock\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"size-btn\", 3, \"click\", \"disabled\"], [1, \"color-selection\"], [1, \"color-options\"], [\"class\", \"color-btn\", 3, \"active\", \"background-color\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"color-btn\", 3, \"click\", \"title\"], [4, \"ngFor\", \"ngForOf\"], [1, \"loading-container\"], [1, \"spinner\"], [1, \"error-container\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"btn-back\", 3, \"click\"]],\n        template: function ProductDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ProductDetailComponent_div_0_Template, 52, 27, \"div\", 0)(1, ProductDetailComponent_div_1_Template, 4, 0, \"div\", 1)(2, ProductDetailComponent_div_2_Template, 9, 1, \"div\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.product);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, FormsModule],\n        styles: [\"@charset \\\"UTF-8\\\";.product-detail-container[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:3rem;max-width:1200px;margin:0 auto;padding:2rem}.product-images[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem}.main-image[_ngcontent-%COMP%]{position:relative;aspect-ratio:1;border-radius:12px;overflow:hidden;background:#f8f9fa}.main-product-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.wishlist-btn[_ngcontent-%COMP%]{position:absolute;top:1rem;right:1rem;width:48px;height:48px;border-radius:50%;background:#ffffffe6;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s;font-size:1.2rem;color:#666}.wishlist-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.wishlist-btn.active[_ngcontent-%COMP%]{color:#e91e63}.image-thumbnails[_ngcontent-%COMP%]{display:flex;gap:.5rem;overflow-x:auto}.thumbnail[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:8px;object-fit:cover;cursor:pointer;border:2px solid transparent;transition:border-color .2s}.thumbnail.active[_ngcontent-%COMP%]{border-color:#007bff}.product-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:2rem}.product-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;margin:0 0 .5rem;color:#333}.brand[_ngcontent-%COMP%]{font-size:1.1rem;color:#666;margin-bottom:1rem}.rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.stars[_ngcontent-%COMP%]{display:flex;gap:2px}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107}.pricing[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;flex-wrap:wrap}.current-price[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#e91e63}.original-price[_ngcontent-%COMP%]{font-size:1.2rem;color:#999;text-decoration:line-through}.discount[_ngcontent-%COMP%]{background:#e91e63;color:#fff;padding:.25rem .5rem;border-radius:4px;font-size:.9rem;font-weight:600}.size-options[_ngcontent-%COMP%], .color-options[_ngcontent-%COMP%]{display:flex;gap:.5rem;flex-wrap:wrap}.size-btn[_ngcontent-%COMP%]{padding:.75rem 1rem;border:2px solid #ddd;background:#fff;border-radius:8px;cursor:pointer;transition:all .2s;font-weight:600}.size-btn[_ngcontent-%COMP%]:hover{border-color:#007bff}.size-btn.active[_ngcontent-%COMP%]{border-color:#007bff;background:#007bff;color:#fff}.size-btn.out-of-stock[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.color-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid transparent;cursor:pointer;transition:all .2s}.color-btn.active[_ngcontent-%COMP%]{border-color:#333}.quantity-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.qty-btn[_ngcontent-%COMP%]{width:40px;height:40px;border:1px solid #ddd;background:#fff;border-radius:8px;cursor:pointer;font-size:1.2rem;font-weight:600}.quantity[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;min-width:2rem;text-align:center}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem}.btn-add-to-cart[_ngcontent-%COMP%], .btn-buy-now[_ngcontent-%COMP%]{flex:1;padding:1rem 2rem;border:none;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all .2s;display:flex;align-items:center;justify-content:center;gap:.5rem}.btn-add-to-cart[_ngcontent-%COMP%]{background:#2196f3;color:#fff}.btn-add-to-cart[_ngcontent-%COMP%]:hover{background:#1976d2}.btn-buy-now[_ngcontent-%COMP%]{background:#ff9800;color:#fff}.btn-buy-now[_ngcontent-%COMP%]:hover{background:#f57c00}.btn-add-to-cart[_ngcontent-%COMP%]:disabled, .btn-buy-now[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.detail-section[_ngcontent-%COMP%]{margin-bottom:1.5rem}.detail-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:.5rem;color:#333}.detail-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0}.detail-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{position:relative;padding:.25rem 0 .25rem 1rem}.detail-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:\\\"\\\\2022\\\";position:absolute;left:0;color:#007bff}@media (max-width: 768px){.product-detail-container[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:2rem;padding:1rem}.action-buttons[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return ProductDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}