import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { RoleManagementService, UserRole, RoleConfig } from '../../../core/services/role-management.service';

export interface ProfileStats {
  label: string;
  value: string | number;
  icon: string;
  color: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
}

export interface ProfileAction {
  label: string;
  icon: string;
  action: string;
  color: string;
  permission?: string;
}

export interface UserProfile {
  id: string;
  username: string;
  fullName: string;
  email: string;
  avatar: string;
  role: UserRole;
  department: string;
  joinDate: Date;
  lastActive: Date;
  bio?: string;
  location?: string;
  phone?: string;
  isVerified: boolean;
  isOnline: boolean;
}

@Component({
  selector: 'app-dynamic-profile',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="dynamic-profile" [attr.data-layout]="profileLayout" [attr.data-role]="userProfile?.role">
      <!-- Profile Header -->
      <div class="profile-header">
        <div class="profile-cover" [style.background]="getCoverGradient()">
          <div class="profile-actions">
            <button 
              *ngFor="let action of getProfileActions()" 
              class="action-btn"
              [style.background]="action.color"
              (click)="handleAction(action.action)"
              [title]="action.label">
              <i [class]="action.icon"></i>
            </button>
          </div>
        </div>
        
        <div class="profile-info">
          <div class="avatar-section">
            <div class="avatar-container">
              <img [src]="userProfile?.avatar || '/assets/images/default-avatar.png'" 
                   [alt]="userProfile?.fullName" 
                   class="profile-avatar">
              <div class="online-indicator" *ngIf="userProfile?.isOnline"></div>
              <div class="role-badge" [style.background]="roleConfig?.color">
                <i [class]="roleConfig?.icon"></i>
              </div>
            </div>
            
            <div class="profile-details">
              <div class="name-section">
                <h1 class="profile-name">{{ userProfile?.fullName }}</h1>
                <div class="verification-badge" *ngIf="userProfile?.isVerified">
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
              
              <p class="profile-username">&#64;{{ userProfile?.username || 'username' }}</p>
              <div class="role-info">
                <span class="role-title" [style.color]="roleConfig?.color">
                  {{ roleConfig?.displayName }}
                </span>
                <span class="department">{{ roleConfig?.department | titlecase }}</span>
              </div>
              
              <p class="profile-bio" *ngIf="userProfile?.bio">{{ userProfile?.bio }}</p>
              
              <div class="profile-meta">
                <div class="meta-item" *ngIf="userProfile?.location">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>{{ userProfile?.location }}</span>
                </div>
                <div class="meta-item" *ngIf="userProfile?.joinDate">
                  <i class="fas fa-calendar-alt"></i>
                  <span>Joined {{ userProfile?.joinDate | date:'MMM yyyy' }}</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-clock"></i>
                  <span>Last active {{ getLastActiveText() }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Stats -->
      <div class="profile-stats" *ngIf="getProfileStats().length > 0">
        <div class="stats-grid">
          <div 
            *ngFor="let stat of getProfileStats()" 
            class="stat-card"
            [style.border-left-color]="stat.color">
            <div class="stat-icon" [style.color]="stat.color">
              <i [class]="stat.icon"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-trend" *ngIf="stat.trend" [class]="'trend-' + stat.trend">
                <i [class]="getTrendIcon(stat.trend)"></i>
                <span>{{ stat.trendValue }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role-Specific Content -->
      <div class="role-content">
        <!-- Executive Layout -->
        <div *ngIf="profileLayout === 'executive'" class="executive-layout">
          <div class="executive-grid">
            <div class="executive-card">
              <h3>Leadership Overview</h3>
              <div class="leadership-metrics">
                <div class="metric">
                  <span class="metric-value">{{ getTeamSize() }}</span>
                  <span class="metric-label">Team Members</span>
                </div>
                <div class="metric">
                  <span class="metric-value">{{ getDepartmentCount() }}</span>
                  <span class="metric-label">Departments</span>
                </div>
              </div>
            </div>
            <div class="executive-card">
              <h3>Strategic Initiatives</h3>
              <div class="initiatives-list">
                <div class="initiative-item" *ngFor="let initiative of getStrategicInitiatives()">
                  <div class="initiative-status" [class]="initiative.status"></div>
                  <span>{{ initiative.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Manager Layout -->
        <div *ngIf="profileLayout === 'manager'" class="manager-layout">
          <div class="manager-grid">
            <div class="manager-card">
              <h3>Team Performance</h3>
              <div class="performance-chart">
                <!-- Team performance visualization -->
                <div class="performance-item" *ngFor="let member of getTeamMembers()">
                  <div class="member-info">
                    <img [src]="member.avatar" [alt]="member.name" class="member-avatar">
                    <span>{{ member.name }}</span>
                  </div>
                  <div class="performance-bar">
                    <div class="progress" [style.width.%]="member.performance"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="manager-card">
              <h3>Department Goals</h3>
              <div class="goals-list">
                <div class="goal-item" *ngFor="let goal of getDepartmentGoals()">
                  <div class="goal-progress">
                    <div class="progress-circle" [style.background]="getProgressColor(goal.progress)">
                      <span>{{ goal.progress }}%</span>
                    </div>
                  </div>
                  <div class="goal-details">
                    <h4>{{ goal.title }}</h4>
                    <p>{{ goal.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Specialist Layout -->
        <div *ngIf="profileLayout === 'specialist'" class="specialist-layout">
          <div class="specialist-grid">
            <div class="specialist-card">
              <h3>My Performance</h3>
              <div class="performance-metrics">
                <div class="metric-circle" *ngFor="let metric of getPersonalMetrics()">
                  <div class="circle-progress" [style.background]="metric.color">
                    <span>{{ metric.value }}</span>
                  </div>
                  <label>{{ metric.label }}</label>
                </div>
              </div>
            </div>
            <div class="specialist-card">
              <h3>Recent Achievements</h3>
              <div class="achievements-list">
                <div class="achievement-item" *ngFor="let achievement of getRecentAchievements()">
                  <div class="achievement-icon" [style.background]="achievement.color">
                    <i [class]="achievement.icon"></i>
                  </div>
                  <div class="achievement-details">
                    <h4>{{ achievement.title }}</h4>
                    <p>{{ achievement.description }}</p>
                    <span class="achievement-date">{{ achievement.date | date:'short' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Admin Layout -->
        <div *ngIf="profileLayout === 'admin'" class="admin-layout">
          <div class="admin-grid">
            <div class="admin-card">
              <h3>System Overview</h3>
              <div class="system-stats">
                <div class="system-metric" *ngFor="let metric of getSystemMetrics()">
                  <div class="metric-icon" [style.color]="metric.color">
                    <i [class]="metric.icon"></i>
                  </div>
                  <div class="metric-data">
                    <span class="metric-value">{{ metric.value }}</span>
                    <span class="metric-label">{{ metric.label }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="admin-card">
              <h3>Recent Activities</h3>
              <div class="activities-list">
                <div class="activity-item" *ngFor="let activity of getRecentActivities()">
                  <div class="activity-time">{{ activity.time | date:'short' }}</div>
                  <div class="activity-content">
                    <span class="activity-user">{{ activity.user }}</span>
                    <span class="activity-action">{{ activity.action }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./dynamic-profile.component.scss']
})
export class DynamicProfileComponent implements OnInit, OnDestroy {
  @Input() userProfile: UserProfile | null = null;
  
  roleConfig: RoleConfig | null = null;
  profileLayout: string = 'specialist';
  
  private destroy$ = new Subject<void>();

  constructor(
    private roleManagementService: RoleManagementService,
    private router: Router
  ) {}

  ngOnInit() {
    if (this.userProfile?.role) {
      this.roleConfig = this.roleManagementService.getRoleConfig(this.userProfile.role);
      this.profileLayout = this.roleConfig.profileLayout;
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getCoverGradient(): string {
    if (!this.roleConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    
    const color = this.roleConfig.color;
    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;
  }

  getProfileActions(): ProfileAction[] {
    if (!this.userProfile?.role) return [];

    const baseActions: ProfileAction[] = [
      { label: 'Edit Profile', icon: 'fas fa-edit', action: 'edit', color: '#4ECDC4' },
      { label: 'Settings', icon: 'fas fa-cog', action: 'settings', color: '#45B7D1' }
    ];

    // Add role-specific actions
    if (this.roleManagementService.isManager(this.userProfile.role)) {
      baseActions.push(
        { label: 'Team Management', icon: 'fas fa-users', action: 'team', color: '#96CEB4' },
        { label: 'Reports', icon: 'fas fa-chart-bar', action: 'reports', color: '#FFEAA7' }
      );
    }

    if (this.userProfile.role === 'super_admin' || this.userProfile.role === 'admin') {
      baseActions.push(
        { label: 'Admin Panel', icon: 'fas fa-shield-alt', action: 'admin', color: '#FF6B6B' }
      );
    }

    return baseActions;
  }

  getProfileStats(): ProfileStats[] {
    // This would be populated based on role and real data
    // For now, returning mock data based on role
    if (!this.userProfile?.role) return [];

    const role = this.userProfile.role;
    
    if (role.includes('sales')) {
      return [
        { label: 'Sales This Month', value: '₹2.4M', icon: 'fas fa-rupee-sign', color: '#45B7D1', trend: 'up', trendValue: '+12%' },
        { label: 'Deals Closed', value: 47, icon: 'fas fa-handshake', color: '#96CEB4', trend: 'up', trendValue: '+8%' },
        { label: 'Target Achievement', value: '94%', icon: 'fas fa-target', color: '#FFEAA7', trend: 'stable' }
      ];
    }

    if (role.includes('marketing')) {
      return [
        { label: 'Campaign Reach', value: '1.2M', icon: 'fas fa-eye', color: '#F38BA8', trend: 'up', trendValue: '+15%' },
        { label: 'Engagement Rate', value: '4.8%', icon: 'fas fa-heart', color: '#DDA0DD', trend: 'up', trendValue: '+0.3%' },
        { label: 'Conversions', value: 2847, icon: 'fas fa-exchange-alt', color: '#FFB6C1', trend: 'down', trendValue: '-2%' }
      ];
    }

    // Default stats for other roles
    return [
      { label: 'Tasks Completed', value: 156, icon: 'fas fa-check-circle', color: '#6BCF7F', trend: 'up', trendValue: '+5%' },
      { label: 'Projects Active', value: 8, icon: 'fas fa-project-diagram', color: '#4ECDC4', trend: 'stable' }
    ];
  }

  handleAction(action: string): void {
    switch (action) {
      case 'edit':
        this.router.navigate(['/profile/edit']);
        break;
      case 'settings':
        this.router.navigate(['/profile/settings']);
        break;
      case 'team':
        this.router.navigate(['/team/management']);
        break;
      case 'reports':
        this.router.navigate(['/reports']);
        break;
      case 'admin':
        this.router.navigate(['/admin']);
        break;
    }
  }

  getLastActiveText(): string {
    if (!this.userProfile?.lastActive) return 'Unknown';
    
    const now = new Date();
    const lastActive = new Date(this.userProfile.lastActive);
    const diffMs = now.getTime() - lastActive.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  }

  getTrendIcon(trend: string): string {
    switch (trend) {
      case 'up': return 'fas fa-arrow-up';
      case 'down': return 'fas fa-arrow-down';
      default: return 'fas fa-minus';
    }
  }

  // Mock data methods - these would be replaced with real data services
  getTeamSize(): number { return 12; }
  getDepartmentCount(): number { return 3; }
  getStrategicInitiatives(): any[] { return []; }
  getTeamMembers(): any[] { return []; }
  getDepartmentGoals(): any[] { return []; }
  getPersonalMetrics(): any[] { return []; }
  getRecentAchievements(): any[] { return []; }
  getSystemMetrics(): any[] { return []; }
  getRecentActivities(): any[] { return []; }
  getProgressColor(progress: number): string { return progress > 75 ? '#6BCF7F' : progress > 50 ? '#FFEAA7' : '#FF6B6B'; }
}
