{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_div_1_div_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeAddStoryModal());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h2\");\n    i0.ɵɵtext(4, \"Add Story\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form\", 16);\n    i0.ɵɵlistener(\"ngSubmit\", function ViewAddStoriesComponent_div_1_div_1_div_2_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.submitNewStory());\n    });\n    i0.ɵɵelementStart(6, \"input\", 17);\n    i0.ɵɵlistener(\"change\", function ViewAddStoriesComponent_div_1_div_1_div_2_Template_input_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onStoryFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ViewAddStoriesComponent_div_1_div_1_div_2_Template_textarea_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newStoryCaption, $event) || (ctx_r1.newStoryCaption = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 19)(9, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_1_div_1_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeAddStoryModal());\n    });\n    i0.ɵɵtext(10, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 21);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newStoryCaption);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isUploadingStory);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newStoryFile || ctx_r1.isUploadingStory);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isUploadingStory ? \"Uploading...\" : \"Post Story\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10);\n    i0.ɵɵtemplate(2, ViewAddStoriesComponent_div_1_div_1_div_2_Template, 13, 6, \"div\", 11);\n    i0.ɵɵelement(3, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showAddStoryModal);\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 4, 1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction ViewAddStoriesComponent_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_div_7_Template_div_click_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 24);\n    i0.ɵɵelement(2, \"div\", 30)(3, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r6.user.avatar + \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(story_r6.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddStory());\n    });\n    i0.ɵɵelementStart(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵelement(4, \"i\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 27);\n    i0.ɵɵtext(6, \"Add Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, ViewAddStoriesComponent_div_2_div_7_Template, 6, 3, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵelement(1, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r1.currentIndex)(\"completed\", i_r8 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 69);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 74);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 75)(4, \"div\", 76);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r10.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_3_div_21_div_1_Template, 8, 2, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33, 0)(3, \"div\", 34);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_3_div_4_Template, 2, 4, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"div\", 38);\n    i0.ɵɵelement(8, \"div\", 39);\n    i0.ɵɵelementStart(9, \"div\", 40);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 41);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 42);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 45);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_3_video_18_Template, 1, 1, \"video\", 46)(19, ViewAddStoriesComponent_div_3_div_19_Template, 1, 2, \"div\", 47)(20, ViewAddStoriesComponent_div_3_div_20_Template, 2, 1, \"div\", 48)(21, ViewAddStoriesComponent_div_3_div_21_Template, 2, 1, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 50)(23, \"div\", 51)(24, \"button\", 52);\n    i0.ɵɵelement(25, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 54);\n    i0.ɵɵelement(27, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 56);\n    i0.ɵɵelement(29, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 58)(31, \"button\", 59);\n    i0.ɵɵelement(32, \"i\", 60);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"button\", 61);\n    i0.ɵɵelement(36, \"i\", 53);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"button\", 62);\n    i0.ɵɵelement(40, \"i\", 26);\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Add to Cart\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(43, \"div\", 63)(44, \"div\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(45, \"div\", 65, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(24);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"i\", 80);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 81)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 82);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    // Handler for Add Story button\n    // Modal state\n    this.showAddStoryModal = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Try to load from API first\n    this.subscriptions.push(this.http.get(`http://localhost:3000/api/stories/active`).subscribe({\n      next: response => {\n        if (response.success && response.data) {\n          this.stories = response.data;\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [{\n      _id: 'story-1',\n      user: {\n        _id: 'user-1',\n        username: 'ai_fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n      mediaType: 'image',\n      caption: 'Sustainable fashion is the future! 🌱✨',\n      createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n      views: 1247,\n      isActive: true,\n      products: [{\n        _id: 'prod-1',\n        name: 'Eco-Friendly Summer Dress',\n        price: 2499,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n      }]\n    }, {\n      _id: 'story-2',\n      user: {\n        _id: 'user-2',\n        username: 'ai_stylist_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n      mediaType: 'image',\n      caption: 'Street style essentials for the modern man 🔥',\n      createdAt: new Date(Date.now() - 12 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18.8 * 60 * 60 * 1000).toISOString(),\n      views: 892,\n      isActive: true,\n      products: [{\n        _id: 'prod-2',\n        name: 'Urban Cotton T-Shirt',\n        price: 899,\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'\n      }]\n    }, {\n      _id: 'story-3',\n      user: {\n        _id: 'user-3',\n        username: 'ai_trendsetter_zara',\n        fullName: 'Zara Patel',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n      mediaType: 'image',\n      caption: 'Ethnic fusion at its finest! Traditional meets modern ✨',\n      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 21 * 60 * 60 * 1000).toISOString(),\n      views: 2156,\n      isActive: true,\n      products: [{\n        _id: 'prod-3',\n        name: 'Designer Ethnic Kurti',\n        price: 1899,\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=200'\n      }]\n    }, {\n      _id: 'story-4',\n      user: {\n        _id: 'user-4',\n        username: 'ai_minimalist_kai',\n        fullName: 'Kai Thompson',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',\n      mediaType: 'image',\n      caption: 'Less is more. Minimalist wardrobe essentials 🌿',\n      createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n      views: 1543,\n      isActive: true,\n      products: [{\n        _id: 'prod-4',\n        name: 'Classic Denim Jeans',\n        price: 2999,\n        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=200'\n      }]\n    }, {\n      _id: 'story-5',\n      user: {\n        _id: 'user-5',\n        username: 'ai_glamour_sophia',\n        fullName: 'Sophia Williams',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n      mediaType: 'image',\n      caption: 'Luxury accessories for the discerning fashionista 💎',\n      createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),\n      views: 3421,\n      isActive: true,\n      products: [{\n        _id: 'prod-5',\n        name: 'Luxury Leather Handbag',\n        price: 4999,\n        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200'\n      }]\n    }];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    // Left third goes back, right two-thirds go forward\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      // Snap back to current position\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    // Simple easing\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    // Check if animation is complete\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    // Update transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {\n    // Additional event listeners can be added here\n  }\n  removeEventListeners() {\n    // Clean up event listeners\n  }\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAddStory() {\n    this.showAddStoryModal = true;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.newStoryFile) return;\n      _this.isUploadingStory = true;\n      try {\n        const formData = new FormData();\n        formData.append('media', _this.newStoryFile);\n        formData.append('caption', _this.newStoryCaption);\n        // You may need to add user info here if not handled by backend auth\n        yield _this.http.post('/api/stories', formData).toPromise();\n        _this.showAddStoryModal = false;\n        _this.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-slider\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [\"class\", \"add-story-modal\", 4, \"ngIf\"], [1, \"skeleton-name\"], [1, \"add-story-modal\"], [1, \"add-story-modal-backdrop\", 3, \"click\"], [1, \"add-story-modal-content\"], [\"autocomplete\", \"off\", 3, \"ngSubmit\"], [\"type\", \"file\", \"accept\", \"image/*,video/*\", \"required\", \"\", 3, \"change\", \"disabled\"], [\"placeholder\", \"Write a caption...\", \"name\", \"caption\", \"rows\", \"2\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"add-story-modal-actions\"], [\"type\", \"button\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 3, \"disabled\"], [1, \"stories-slider\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-story-avatar\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-ring\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\"], [1, \"ecommerce-btn\", \"cart-btn\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 3)(2, ViewAddStoriesComponent_div_2_Template, 8, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, ViewAddStoriesComponent_div_3_Template, 47, 15, \"div\", 5)(4, ViewAddStoriesComponent_div_4_Template, 9, 0, \"div\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, FormsModule, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.NgModel, i4.NgForm],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #dbdbdb;\\n  padding: 16px 0;\\n  margin-bottom: 0;\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.stories-slider[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: transform 0.2s ease;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "ViewAddStoriesComponent_div_1_div_1_div_2_Template_div_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "closeAddStoryModal", "ɵɵelementEnd", "ɵɵtext", "ViewAddStoriesComponent_div_1_div_1_div_2_Template_form_ngSubmit_5_listener", "submitNewStory", "ViewAddStoriesComponent_div_1_div_1_div_2_Template_input_change_6_listener", "$event", "onStoryFileSelected", "ɵɵtwoWayListener", "ViewAddStoriesComponent_div_1_div_1_div_2_Template_textarea_ngModelChange_7_listener", "ɵɵtwoWayBindingSet", "newStoryCaption", "ViewAddStoriesComponent_div_1_div_1_div_2_Template_button_click_9_listener", "ɵɵadvance", "ɵɵproperty", "isUploadingStory", "ɵɵtwoWayProperty", "newStoryFile", "ɵɵtextInterpolate1", "ɵɵelement", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_div_2_Template", "showAddStoryModal", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵpureFunction0", "_c2", "ViewAddStoriesComponent_div_2_div_7_Template_div_click_0_listener", "i_r5", "_r4", "index", "openStories", "ɵɵstyleProp", "story_r6", "user", "avatar", "ɵɵtextInterpolate", "username", "ViewAddStoriesComponent_div_2_Template_div_click_1_listener", "_r3", "onAddStory", "ViewAddStoriesComponent_div_2_div_7_Template", "stories", "ɵɵclassProp", "i_r8", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "caption", "ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener", "product_r10", "_r9", "$implicit", "viewProduct", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_3_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_3_div_4_Template", "ViewAddStoriesComponent_div_3_Template_div_click_5_listener", "_r7", "onStoryClick", "ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_3_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_3_video_18_Template", "ViewAddStoriesComponent_div_3_div_19_Template", "ViewAddStoriesComponent_div_3_div_20_Template", "ViewAddStoriesComponent_div_3_div_21_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "ngOnInit", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "push", "get", "subscribe", "next", "response", "success", "data", "loadFallbackStories", "error", "console", "_id", "Date", "now", "toISOString", "expiresAt", "isActive", "products", "image", "showStory", "document", "body", "style", "overflow", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "clickX", "clientX", "windowWidth", "window", "innerWidth", "touches", "updateDragPosition", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getStoryProgress", "dateString", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "num", "toFixed", "toString", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "product", "navigate", "story", "file", "target", "files", "_this", "_asyncToGenerator", "formData", "FormData", "append", "post", "to<PERSON>romise", "err", "alert", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "ViewAddStoriesComponent_div_3_Template", "ViewAddStoriesComponent_div_4_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "NgForm", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild, HostListener\n \n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n// import { environment } from '../../../../environments/environment';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Try to load from API first\n    this.subscriptions.push(\n      this.http.get<any>(`http://localhost:3000/api/stories/active`).subscribe({\n        next: (response) => {\n          if (response.success && response.data) {\n            this.stories = response.data;\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [\n      {\n        _id: 'story-1',\n        user: {\n          _id: 'user-1',\n          username: 'ai_fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        mediaType: 'image',\n        caption: 'Sustainable fashion is the future! 🌱✨',\n        createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago\n        expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(), // 19 hours from now\n        views: 1247,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-1',\n            name: 'Eco-Friendly Summer Dress',\n            price: 2499,\n            image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-2',\n        user: {\n          _id: 'user-2',\n          username: 'ai_stylist_alex',\n          fullName: 'Alex Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',\n        mediaType: 'image',\n        caption: 'Street style essentials for the modern man 🔥',\n        createdAt: new Date(Date.now() - 12 * 60 * 1000).toISOString(), // 12 minutes ago\n        expiresAt: new Date(Date.now() + 18.8 * 60 * 60 * 1000).toISOString(),\n        views: 892,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-2',\n            name: 'Urban Cotton T-Shirt',\n            price: 899,\n            image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-3',\n        user: {\n          _id: 'user-3',\n          username: 'ai_trendsetter_zara',\n          fullName: 'Zara Patel',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',\n        mediaType: 'image',\n        caption: 'Ethnic fusion at its finest! Traditional meets modern ✨',\n        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago\n        expiresAt: new Date(Date.now() + 21 * 60 * 60 * 1000).toISOString(),\n        views: 2156,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-3',\n            name: 'Designer Ethnic Kurti',\n            price: 1899,\n            image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-4',\n        user: {\n          _id: 'user-4',\n          username: 'ai_minimalist_kai',\n          fullName: 'Kai Thompson',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',\n        mediaType: 'image',\n        caption: 'Less is more. Minimalist wardrobe essentials 🌿',\n        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago\n        expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n        views: 1543,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-4',\n            name: 'Classic Denim Jeans',\n            price: 2999,\n            image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-5',\n        user: {\n          _id: 'user-5',\n          username: 'ai_glamour_sophia',\n          fullName: 'Sophia Williams',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n        mediaType: 'image',\n        caption: 'Luxury accessories for the discerning fashionista 💎',\n        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago\n        expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),\n        views: 3421,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-5',\n            name: 'Luxury Leather Handbag',\n            price: 4999,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200'\n          }\n        ]\n      }\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    \n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    \n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    \n    // Left third goes back, right two-thirds go forward\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    \n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    \n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      // Snap back to current position\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    \n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n    \n    // Simple easing\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    \n    // Check if animation is complete\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      \n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    \n    // Update transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    \n    requestAnimationFrame(() => this.update());\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n\n  private setupEventListeners() {\n    // Additional event listeners can be added here\n  }\n\n  private removeEventListeners() {\n    // Clean up event listeners\n  }\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n\n  // Handler for Add Story button\n  // Modal state\n  showAddStoryModal = false;\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n\n  onAddStory() {\n    this.showAddStoryModal = true;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const formData = new FormData();\n      formData.append('media', this.newStoryFile);\n      formData.append('caption', this.newStoryCaption);\n      // You may need to add user info here if not handled by backend auth\n      await this.http.post('/api/stories', formData).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n}\n", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n\n      \n<!-- Add Story Modal -->\n<div class=\"add-story-modal\" *ngIf=\"showAddStoryModal\">\n  <div class=\"add-story-modal-backdrop\" (click)=\"closeAddStoryModal()\"></div>\n  <div class=\"add-story-modal-content\">\n    <h2>Add Story</h2>\n    <form (ngSubmit)=\"submitNewStory()\" autocomplete=\"off\">\n      <input type=\"file\" accept=\"image/*,video/*\" (change)=\"onStoryFileSelected($event)\" [disabled]=\"isUploadingStory\" required />\n      <textarea placeholder=\"Write a caption...\" [(ngModel)]=\"newStoryCaption\" name=\"caption\" rows=\"2\" [disabled]=\"isUploadingStory\"></textarea>\n      <div class=\"add-story-modal-actions\">\n        <button type=\"button\" (click)=\"closeAddStoryModal()\" [disabled]=\"isUploadingStory\">Cancel</button>\n        <button type=\"submit\" [disabled]=\"!newStoryFile || isUploadingStory\">\n          {{ isUploadingStory ? 'Uploading...' : 'Post Story' }}\n        </button>\n      </div>\n    </form>\n  </div>\n</div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Slider -->\n  <div class=\"stories-slider\" *ngIf=\"!isLoadingStories\">\n\n    <!-- Add Story Button -->\n    <div class=\"story-item add-story-item\" (click)=\"onAddStory()\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar add-story-avatar\">\n          <i class=\"fas fa-plus\"></i>\n        </div>\n      </div>\n      <div class=\"story-username\">Add Story</div>\n    </div>\n    <div\n      *ngFor=\"let story of stories; let i = index\"\n      class=\"story-item\"\n      (click)=\"openStories(i)\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar\" [style.background-image]=\"'url(' + story.user.avatar + ')'\"></div>\n        <div class=\"story-ring\"></div>\n      </div>\n      <div class=\"story-username\">{{ story.user.username }}</div>\n    </div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\">\n          <button class=\"ecommerce-btn buy-now-btn\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": ";AAGA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;ICM1CC,EADF,CAAAC,cAAA,cAAuD,cACgB;IAA/BD,EAAA,CAAAE,UAAA,mBAAAC,wEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAM;IAEzEV,EADF,CAAAC,cAAA,cAAqC,SAC/B;IAAAD,EAAA,CAAAW,MAAA,gBAAS;IAAAX,EAAA,CAAAU,YAAA,EAAK;IAClBV,EAAA,CAAAC,cAAA,eAAuD;IAAjDD,EAAA,CAAAE,UAAA,sBAAAU,4EAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAO,cAAA,EAAgB;IAAA,EAAC;IACjCb,EAAA,CAAAC,cAAA,gBAA4H;IAAhFD,EAAA,CAAAE,UAAA,oBAAAY,2EAAAC,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAU,mBAAA,CAAAD,MAAA,CAA2B;IAAA,EAAC;IAAlFf,EAAA,CAAAU,YAAA,EAA4H;IAC5HV,EAAA,CAAAC,cAAA,mBAA+H;IAApFD,EAAA,CAAAiB,gBAAA,2BAAAC,qFAAAH,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAmB,kBAAA,CAAAb,MAAA,CAAAc,eAAA,EAAAL,MAAA,MAAAT,MAAA,CAAAc,eAAA,GAAAL,MAAA;MAAA,OAAAf,EAAA,CAAAQ,WAAA,CAAAO,MAAA;IAAA,EAA6B;IAAuDf,EAAA,CAAAU,YAAA,EAAW;IAExIV,EADF,CAAAC,cAAA,cAAqC,iBACgD;IAA7DD,EAAA,CAAAE,UAAA,mBAAAmB,2EAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAA+BT,EAAA,CAAAW,MAAA,cAAM;IAAAX,EAAA,CAAAU,YAAA,EAAS;IAClGV,EAAA,CAAAC,cAAA,kBAAqE;IACnED,EAAA,CAAAW,MAAA,IACF;IAIRX,EAJQ,CAAAU,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;IAVmFV,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAkB,gBAAA,CAA6B;IACrExB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAyB,gBAAA,YAAAnB,MAAA,CAAAc,eAAA,CAA6B;IAAyBpB,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAkB,gBAAA,CAA6B;IAEvExB,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAkB,gBAAA,CAA6B;IAC5DxB,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAoB,YAAA,IAAApB,MAAA,CAAAkB,gBAAA,CAA8C;IAClExB,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAA2B,kBAAA,MAAArB,MAAA,CAAAkB,gBAAA,sCACF;;;;;IAhBJxB,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAA4B,SAAA,cAAmC;IAIzC5B,EAAA,CAAA6B,UAAA,IAAAC,kDAAA,mBAAuD;IAgBjD9B,EAAA,CAAA4B,SAAA,cAAiC;IACnC5B,EAAA,CAAAU,YAAA,EAAM;;;;IAjBoBV,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyB,iBAAA,CAAuB;;;;;IANnD/B,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAA6B,UAAA,IAAAG,4CAAA,iBAA6D;IAuB/DhC,EAAA,CAAAU,YAAA,EAAM;;;IAvBkBV,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAiC,eAAA,IAAAC,GAAA,EAAc;;;;;;IAqCpClC,EAAA,CAAAC,cAAA,cAG2B;IAAzBD,EAAA,CAAAE,UAAA,mBAAAiC,kEAAA;MAAA,MAAAC,IAAA,GAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA,EAAAC,KAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiC,WAAA,CAAAH,IAAA,CAAc;IAAA,EAAC;IACxBpC,EAAA,CAAAC,cAAA,cAAoC;IAElCD,EADA,CAAA4B,SAAA,cAA4F,cAC9D;IAChC5B,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAW,MAAA,GAAyB;IACvDX,EADuD,CAAAU,YAAA,EAAM,EACvD;;;;IAJwBV,EAAA,CAAAsB,SAAA,GAA2D;IAA3DtB,EAAA,CAAAwC,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAG3D3C,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAA4C,iBAAA,CAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;;IAhBvD7C,EAHF,CAAAC,cAAA,cAAsD,cAGU;IAAvBD,EAAA,CAAAE,UAAA,mBAAA4C,4DAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA2C,GAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0C,UAAA,EAAY;IAAA,EAAC;IAEzDhD,EADF,CAAAC,cAAA,cAAoC,cACS;IACzCD,EAAA,CAAA4B,SAAA,YAA2B;IAE/B5B,EADE,CAAAU,YAAA,EAAM,EACF;IACNV,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAW,MAAA,gBAAS;IACvCX,EADuC,CAAAU,YAAA,EAAM,EACvC;IACNV,EAAA,CAAA6B,UAAA,IAAAoB,4CAAA,kBAG2B;IAO7BjD,EAAA,CAAAU,YAAA,EAAM;;;;IATgBV,EAAA,CAAAsB,SAAA,GAAY;IAAZtB,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA4C,OAAA,CAAY;;;;;IAkB9BlD,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAA4B,SAAA,cAAwC;IAC1C5B,EAAA,CAAAU,YAAA,EAAM;;;;;IAFJV,EADA,CAAAmD,WAAA,WAAAC,IAAA,KAAA9C,MAAA,CAAA+C,YAAA,CAAmC,cAAAD,IAAA,GAAA9C,MAAA,CAAA+C,YAAA,CACC;;;;;IA6BpCrD,EAAA,CAAA4B,SAAA,gBAQQ;;;;IALN5B,EAAA,CAAAuB,UAAA,QAAAjB,MAAA,CAAAgD,eAAA,GAAAC,QAAA,EAAAvD,EAAA,CAAAwD,aAAA,CAAkC;;;;;IAQpCxD,EAAA,CAAA4B,SAAA,cAIM;;;;IADJ5B,EAAA,CAAAwC,WAAA,8BAAAlC,MAAA,CAAAgD,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItEvD,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAU,YAAA,EAAM;;;;IADJV,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAA2B,kBAAA,MAAArB,MAAA,CAAAgD,eAAA,GAAAG,OAAA,MACF;;;;;;IAIEzD,EAAA,CAAAC,cAAA,cAGiC;IAA/BD,EAAA,CAAAE,UAAA,mBAAAwD,yEAAA;MAAA,MAAAC,WAAA,GAAA3D,EAAA,CAAAI,aAAA,CAAAwD,GAAA,EAAAC,SAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,WAAA,CAAAH,WAAA,CAAoB;IAAA,EAAC;IAC9B3D,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAW,MAAA,yBAAG;IAAAX,EAAA,CAAAU,YAAA,EAAM;IAErCV,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAW,MAAA,GAAkB;IAAAX,EAAA,CAAAU,YAAA,EAAM;IACtDV,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAW,MAAA,GAAgC;IAEnEX,EAFmE,CAAAU,YAAA,EAAM,EACjE,EACF;;;;;IAH4BV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAA4C,iBAAA,CAAAe,WAAA,CAAAI,IAAA,CAAkB;IACjB/D,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAA4C,iBAAA,CAAAtC,MAAA,CAAA0D,WAAA,CAAAL,WAAA,CAAAM,KAAA,EAAgC;;;;;IARrEjE,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAA6B,UAAA,IAAAqC,mDAAA,kBAGiC;IAOnClE,EAAA,CAAAU,YAAA,EAAM;;;;IATkBV,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA6D,gBAAA,GAAqB;;;;;;IA3DjDnE,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAA6B,UAAA,IAAAuC,4CAAA,kBAIuC;IAGzCpE,EAAA,CAAAU,YAAA,EAAM;IAGNV,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAE,UAAA,mBAAAmE,4DAAAtD,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiE,YAAA,CAAAxD,MAAA,CAAoB;IAAA,EAAC,wBAAAyD,iEAAAzD,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAChBF,MAAA,CAAAmE,YAAA,CAAA1D,MAAA,CAAoB;IAAA,EAAC,uBAAA2D,gEAAA3D,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACtBF,MAAA,CAAAqE,WAAA,CAAA5D,MAAA,CAAmB;IAAA,EAAC,sBAAA6D,+DAAA7D,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACrBF,MAAA,CAAAuE,UAAA,CAAA9D,MAAA,CAAkB;IAAA,EAAC;IAIhCf,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAA4B,SAAA,cAAyG;IACzG5B,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAW,MAAA,IAAqC;IAAAX,EAAA,CAAAU,YAAA,EAAM;IACpEV,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAW,MAAA,IAA6C;IAAAX,EAAA,CAAAU,YAAA,EAAM;IAC5EV,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAW,MAAA,IAAiD;IAC7EX,EAD6E,CAAAU,YAAA,EAAM,EAC7E;IACNV,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAE,UAAA,mBAAA4E,gEAAA;MAAA9E,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyE,YAAA,EAAc;IAAA,EAAC;IACnD/E,EAAA,CAAA4B,SAAA,aAA4B;IAEhC5B,EADE,CAAAU,YAAA,EAAS,EACL;IAGNV,EAAA,CAAAC,cAAA,eAA4B;IAyB1BD,EAvBA,CAAA6B,UAAA,KAAAmD,+CAAA,oBAOc,KAAAC,6CAAA,kBAOyD,KAAAC,6CAAA,kBAIT,KAAAC,6CAAA,kBAKP;IAYzDnF,EAAA,CAAAU,YAAA,EAAM;IAKFV,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAA4B,SAAA,aAA4B;IAC9B5B,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAA4B,SAAA,aAA8B;IAChC5B,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAA4B,SAAA,aAA4B;IAEhC5B,EADE,CAAAU,YAAA,EAAS,EACL;IAIJV,EADF,CAAAC,cAAA,eAAsC,kBACM;IACxCD,EAAA,CAAA4B,SAAA,aAAoC;IACpC5B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,eAAO;IACfX,EADe,CAAAU,YAAA,EAAO,EACb;IACTV,EAAA,CAAAC,cAAA,kBAA2C;IACzCD,EAAA,CAAA4B,SAAA,aAA4B;IAC5B5B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,gBAAQ;IAChBX,EADgB,CAAAU,YAAA,EAAO,EACd;IACTV,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAA4B,SAAA,aAA2B;IAC3B5B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,mBAAW;IAGvBX,EAHuB,CAAAU,YAAA,EAAO,EACjB,EACL,EACF;IAINV,EADA,CAAA4B,SAAA,eAAmD,eACA;IAEvD5B,EADE,CAAAU,YAAA,EAAM,EACF;IAGNV,EAAA,CAAA4B,SAAA,kBAAqE;IACvE5B,EAAA,CAAAU,YAAA,EAAM;;;;IAlHuBV,EAAA,CAAAmD,WAAA,YAAA7C,MAAA,CAAA8E,MAAA,CAAwB;IAM3BpF,EAAA,CAAAsB,SAAA,GAAY;IAAZtB,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA4C,OAAA,CAAY;IAU7BlD,EAAA,CAAAsB,SAAA,EAAmC;;IASPtB,EAAA,CAAAsB,SAAA,GAAuE;IAAvEtB,EAAA,CAAAwC,WAAA,8BAAAlC,MAAA,CAAAgD,eAAA,GAAAZ,IAAA,CAAAC,MAAA,OAAuE;IACzE3C,EAAA,CAAAsB,SAAA,GAAqC;IAArCtB,EAAA,CAAA4C,iBAAA,CAAAtC,MAAA,CAAAgD,eAAA,GAAAZ,IAAA,CAAA2C,QAAA,CAAqC;IACrCrF,EAAA,CAAAsB,SAAA,GAA6C;IAA7CtB,EAAA,CAAA4C,iBAAA,CAAAtC,MAAA,CAAAgF,UAAA,CAAAhF,MAAA,CAAAgD,eAAA,GAAAiC,SAAA,EAA6C;IAC5CvF,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAA2B,kBAAA,KAAArB,MAAA,CAAAkF,YAAA,CAAAlF,MAAA,CAAAgD,eAAA,GAAAmC,KAAA,YAAiD;IAW1EzF,EAAA,CAAAsB,SAAA,GAA6C;IAA7CtB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,eAAA,GAAAoC,SAAA,aAA6C;IAW7C1F,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,eAAA,GAAAoC,SAAA,aAA6C;IAM1C1F,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,eAAA,GAAAG,OAAA,CAA+B;IAK/BzD,EAAA,CAAAsB,SAAA,EAAmB;IAAnBtB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAqF,WAAA,GAAmB;IAoDK3F,EAAA,CAAAsB,SAAA,IAA0B;IAA1BtB,EAAA,CAAAmD,WAAA,cAAA7C,MAAA,CAAA8E,MAAA,CAA0B;;;;;IAK9DpF,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAA4B,SAAA,YAAmC;IACnC5B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,qBAAc;IACtBX,EADsB,CAAAU,YAAA,EAAO,EACvB;IAEJV,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAU,YAAA,EAAO;IAC5BV,EAAA,CAAA4B,SAAA,YAAoC;IAExC5B,EADE,CAAAU,YAAA,EAAM,EACF;;;AD7IN,OAAM,MAAOkF,uBAAuB;EAwBlCC,YACUC,MAAc,EACdC,IAAgB;IADhB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IAtBd,KAAA7C,OAAO,GAAY,EAAE;IACrB,KAAA8C,gBAAgB,GAAG,IAAI;IAEvB,KAAA3C,YAAY,GAAG,CAAC;IAChB,KAAA+B,MAAM,GAAG,KAAK;IACd,KAAAa,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEX,KAAAC,aAAa,GAAmB,EAAE;IA6Y1C;IACA;IACA,KAAA5E,iBAAiB,GAAG,KAAK;IACzB,KAAAL,YAAY,GAAgB,IAAI;IAChC,KAAAN,eAAe,GAAG,EAAE;IACpB,KAAAI,gBAAgB,GAAG,KAAK;EA7YrB;EAEHoF,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACb,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACW,aAAa,CAACS,IAAI,CACrB,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAM,0CAA0C,CAAC,CAACC,SAAS,CAAC;MACvEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAACxE,OAAO,GAAGsE,QAAQ,CAACE,IAAI;SAC7B,MAAM;UACL,IAAI,CAACC,mBAAmB,EAAE;;QAE5B,IAAI,CAAC3B,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACD4B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAAC3B,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEA2B,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACzE,OAAO,GAAG,CACb;MACE4E,GAAG,EAAE,SAAS;MACdpF,IAAI,EAAE;QACJoF,GAAG,EAAE,QAAQ;QACbjF,QAAQ,EAAE,qBAAqB;QAC/BwC,QAAQ,EAAE,WAAW;QACrB1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,oEAAoE;MAC9EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,wCAAwC;MACjD8B,SAAS,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC7DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnExC,KAAK,EAAE,IAAI;MACX0C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACb/D,IAAI,EAAE,2BAA2B;QACjCE,KAAK,EAAE,IAAI;QACXoE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdpF,IAAI,EAAE;QACJoF,GAAG,EAAE,QAAQ;QACbjF,QAAQ,EAAE,iBAAiB;QAC3BwC,QAAQ,EAAE,gBAAgB;QAC1B1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,oEAAoE;MAC9EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,+CAA+C;MACxD8B,SAAS,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC9DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACrExC,KAAK,EAAE,GAAG;MACV0C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACb/D,IAAI,EAAE,sBAAsB;QAC5BE,KAAK,EAAE,GAAG;QACVoE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdpF,IAAI,EAAE;QACJoF,GAAG,EAAE,QAAQ;QACbjF,QAAQ,EAAE,qBAAqB;QAC/BwC,QAAQ,EAAE,YAAY;QACtB1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,oEAAoE;MAC9EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,yDAAyD;MAClE8B,SAAS,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnExC,KAAK,EAAE,IAAI;MACX0C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACb/D,IAAI,EAAE,uBAAuB;QAC7BE,KAAK,EAAE,IAAI;QACXoE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdpF,IAAI,EAAE;QACJoF,GAAG,EAAE,QAAQ;QACbjF,QAAQ,EAAE,mBAAmB;QAC7BwC,QAAQ,EAAE,cAAc;QACxB1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,iEAAiE;MAC3EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,iDAAiD;MAC1D8B,SAAS,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnExC,KAAK,EAAE,IAAI;MACX0C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACb/D,IAAI,EAAE,qBAAqB;QAC3BE,KAAK,EAAE,IAAI;QACXoE,KAAK,EAAE;OACR;KAEJ,EACD;MACEP,GAAG,EAAE,SAAS;MACdpF,IAAI,EAAE;QACJoF,GAAG,EAAE,QAAQ;QACbjF,QAAQ,EAAE,mBAAmB;QAC7BwC,QAAQ,EAAE,iBAAiB;QAC3B1C,MAAM,EAAE;OACT;MACDY,QAAQ,EAAE,iEAAiE;MAC3EmC,SAAS,EAAE,OAAO;MAClBjC,OAAO,EAAE,sDAAsD;MAC/D8B,SAAS,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnExC,KAAK,EAAE,IAAI;MACX0C,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEN,GAAG,EAAE,QAAQ;QACb/D,IAAI,EAAE,wBAAwB;QAC9BE,KAAK,EAAE,IAAI;QACXoE,KAAK,EAAE;OACR;KAEJ,CACF;EACH;EAEA9F,WAAWA,CAACD,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACe,YAAY,GAAGf,KAAK;IACzB,IAAI,CAAC8C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACkD,SAAS,CAAChG,KAAK,CAAC;IACrBiG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEA3D,YAAYA,CAAA;IACV,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACuD,cAAc,EAAE;IACrBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAX,SAASA,CAAChG,KAAa;IACrB,IAAI,CAACe,YAAY,GAAGf,KAAK;IACzB,IAAI,CAAC6D,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAACyC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC9F,YAAY,GAAG,IAAI,CAACH,OAAO,CAACkG,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAChD,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACoD,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACtE,YAAY,EAAE;;EAEvB;EAEAuE,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjG,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC+C,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACoD,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACtE,YAAY,EAAE;;EAEvB;EAGAwE,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACpE,MAAM,EAAE;IAElB,QAAQoE,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACpE,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAACiF,KAAiB;IAC5B,IAAI,IAAI,CAACvD,UAAU,EAAE;IAErB,MAAMyD,MAAM,GAAGF,KAAK,CAACG,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IAErC;IACA,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACN,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EAEA1E,YAAYA,CAAC+E,KAAiB;IAC5B,IAAI,CAACtD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGkD,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC1C,IAAI,CAACpD,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEA3B,WAAWA,CAAC6E,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACtD,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGiD,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC5C,IAAI,CAACK,kBAAkB,EAAE;EAC3B;EAEAnF,UAAUA,CAAC2E,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAACtD,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IAEvB,MAAM+D,SAAS,GAAG,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAM4D,SAAS,GAAGL,MAAM,CAACC,UAAU,GAAG,IAAI,CAACtD,0BAA0B;IAErE,IAAI2D,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACX,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL;MACA,IAAI,CAAC/C,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACoD,MAAM,EAAE;;EAEjB;EAEQW,kBAAkBA,CAAA;IACxB,MAAMC,SAAS,GAAG,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAI8D,SAAS,GAAGJ,MAAM,CAACC,UAAU,GAAI,EAAE;IAEnD,IAAI,IAAI,CAAClB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAAC/C,OAAO,MAAM;;EAErD;EAEQkD,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACpD,UAAU,EAAE;IAEtB;IACA,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAE1E;IACA,IAAIyD,IAAI,CAACC,GAAG,CAAC,IAAI,CAACjE,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAMgE,QAAQ,GAAG,IAAI,CAAChE,eAAe,KAAK,SAAS,GAC/C,IAAI,CAAChD,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAACiF,SAAS,CAAC+B,QAAQ,CAAC;QACxB,IAAI,CAAChE,eAAe,GAAG,IAAI;;MAE7B;;IAGF;IACA,IAAI,IAAI,CAACuC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAAC/C,OAAO,MAAM;;IAGnDmE,qBAAqB,CAAC,MAAM,IAAI,CAACjB,MAAM,EAAE,CAAC;EAC5C;EAEQV,cAAcA,CAAA;IACpB,MAAM4B,MAAM,GAAGhC,QAAQ,CAACiC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAACvD,OAAO,CAAEyD,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAEQ5D,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGF7D,eAAeA,CAAA;IACb,OAAO,IAAI,CAACJ,OAAO,CAAC,IAAI,CAACG,YAAY,CAAC;EACxC;EAEAsH,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACtH,YAAY,GAAG,CAAC,IAAI,IAAI,CAACH,OAAO,CAACkG,MAAM,GAAI,GAAG;EAC9D;EAEA9D,UAAUA,CAACsF,UAAkB;IAC3B,MAAM5C,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM8C,IAAI,GAAG,IAAI9C,IAAI,CAAC6C,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGX,IAAI,CAACY,KAAK,CAAC,CAAC/C,GAAG,CAACgD,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMG,WAAW,GAAGd,IAAI,CAACY,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGf,IAAI,CAACY,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEA1F,YAAYA,CAAC2F,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEArH,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIqH,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpC9C,KAAK,EAAE,UAAU;MACjB+C,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACzH,KAAK,CAAC;EAClB;EAEAH,WAAWA,CAAC6H,OAAY;IACtB,IAAI,CAAC7F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC7D,GAAG,CAAC,CAAC;EACjD;EAEAnC,WAAWA,CAAA;IACT,MAAMkG,KAAK,GAAG,IAAI,CAACvI,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEuI,KAAK,IAAIA,KAAK,CAACzD,QAAQ,IAAIyD,KAAK,CAACzD,QAAQ,CAACgB,MAAM,GAAG,CAAC,CAAC;EACjE;EAEAjF,gBAAgBA,CAAA;IACd,MAAM0H,KAAK,GAAG,IAAI,CAACvI,eAAe,EAAE;IACpC,OAAOuI,KAAK,EAAEzD,QAAQ,IAAI,EAAE;EAC9B;EASApF,UAAUA,CAAA;IACR,IAAI,CAACjB,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACL,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,eAAe,GAAG,EAAE;EAC3B;EAEAJ,mBAAmBA,CAACwI,KAAU;IAC5B,MAAMsC,IAAI,GAAGtC,KAAK,CAACuC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACpK,YAAY,GAAGoK,IAAI;;EAE5B;EAEMjL,cAAcA,CAAA;IAAA,IAAAoL,KAAA;IAAA,OAAAC,iBAAA;MAClB,IAAI,CAACD,KAAI,CAACvK,YAAY,EAAE;MACxBuK,KAAI,CAACzK,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAM2K,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,KAAI,CAACvK,YAAY,CAAC;QAC3CyK,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEJ,KAAI,CAAC7K,eAAe,CAAC;QAChD;QACA,MAAM6K,KAAI,CAAClG,IAAI,CAACuG,IAAI,CAAC,cAAc,EAAEH,QAAQ,CAAC,CAACI,SAAS,EAAE;QAC1DN,KAAI,CAAClK,iBAAiB,GAAG,KAAK;QAC9BkK,KAAI,CAACpF,WAAW,EAAE;OACnB,CAAC,OAAO2F,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACRR,KAAI,CAACzK,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EAEAf,kBAAkBA,CAAA;IAChB,IAAI,CAACsB,iBAAiB,GAAG,KAAK;EAChC;;;uBA3cW6D,uBAAuB,EAAA5F,EAAA,CAAA0M,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA5M,EAAA,CAAA0M,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAvBlH,uBAAuB;MAAAmH,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAvBlN,EAAA,CAAAE,UAAA,qBAAAkN,mDAAArM,MAAA;YAAA,OAAAoM,GAAA,CAAA5D,aAAA,CAAAxI,MAAA,CAAqB;UAAA,UAAAf,EAAA,CAAAqN,iBAAA,CAAE;;;;;;;;;;UCvCpCrN,EAAA,CAAAC,cAAA,aAA+B;UA6B7BD,EA3BA,CAAA6B,UAAA,IAAAyL,sCAAA,iBAAsD,IAAAC,sCAAA,iBA2BA;UAsBxDvN,EAAA,CAAAU,YAAA,EAAM;UAwHNV,EArHA,CAAA6B,UAAA,IAAA2L,sCAAA,mBAAqE,IAAAC,sCAAA,iBAqHxB;;;UAzKrCzN,EAAA,CAAAsB,SAAA,EAAsB;UAAtBtB,EAAA,CAAAuB,UAAA,SAAA4L,GAAA,CAAAnH,gBAAA,CAAsB;UA2BChG,EAAA,CAAAsB,SAAA,EAAuB;UAAvBtB,EAAA,CAAAuB,UAAA,UAAA4L,GAAA,CAAAnH,gBAAA,CAAuB;UAyBChG,EAAA,CAAAsB,SAAA,EAAY;UAAZtB,EAAA,CAAAuB,UAAA,SAAA4L,GAAA,CAAA/H,MAAA,CAAY;UAqHpCpF,EAAA,CAAAsB,SAAA,EAAY;UAAZtB,EAAA,CAAAuB,UAAA,SAAA4L,GAAA,CAAA/H,MAAA,CAAY;;;qBDxI/BtF,YAAY,EAAA4N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7N,WAAW,EAAA8N,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}