{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction VendorAnalyticsComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"span\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", data_r1.percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(3, 3, data_r1.value, \"1.0-0\"), \"\");\n  }\n}\nfunction VendorAnalyticsComponent_span_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(label_r2);\n  }\n}\nfunction VendorAnalyticsComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"div\", 41);\n    i0.ɵɵelementStart(2, \"span\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", item_r3.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r3.value, \"%\");\n  }\n}\nfunction VendorAnalyticsComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45);\n    i0.ɵɵelement(2, \"img\", 46);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r4.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 7, product_r4.views, \"1.0-0\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r4.orders);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(12, 10, product_r4.revenue, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", product_r4.conversion, \"%\");\n  }\n}\nexport class VendorAnalyticsComponent {\n  constructor() {\n    this.selectedPeriod = '30';\n    this.analytics = {\n      revenue: 125000,\n      orders: 150,\n      views: 12500,\n      conversionRate: 3.2\n    };\n    this.revenueData = [{\n      value: 15000,\n      percentage: 60\n    }, {\n      value: 18000,\n      percentage: 72\n    }, {\n      value: 22000,\n      percentage: 88\n    }, {\n      value: 25000,\n      percentage: 100\n    }, {\n      value: 20000,\n      percentage: 80\n    }, {\n      value: 23000,\n      percentage: 92\n    }, {\n      value: 28000,\n      percentage: 100\n    }];\n    this.chartLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    this.orderStatusData = [{\n      label: 'Delivered',\n      value: 65,\n      color: '#28a745'\n    }, {\n      label: 'Shipped',\n      value: 20,\n      color: '#007bff'\n    }, {\n      label: 'Confirmed',\n      value: 10,\n      color: '#ffc107'\n    }, {\n      label: 'Pending',\n      value: 5,\n      color: '#dc3545'\n    }];\n    this.topProducts = [{\n      name: 'Summer Dress',\n      image: '/assets/images/product1.jpg',\n      views: 2500,\n      orders: 45,\n      revenue: 134550,\n      conversion: 1.8\n    }, {\n      name: 'Casual Shirt',\n      image: '/assets/images/product2.jpg',\n      views: 1800,\n      orders: 32,\n      revenue: 51168,\n      conversion: 1.7\n    }, {\n      name: 'Sneakers',\n      image: '/assets/images/product3.jpg',\n      views: 1200,\n      orders: 28,\n      revenue: 139720,\n      conversion: 2.3\n    }];\n    this.socialStats = {\n      posts: {\n        total: 12,\n        likes: 456,\n        comments: 89,\n        shares: 34\n      },\n      stories: {\n        total: 8,\n        views: 1234,\n        replies: 67,\n        productClicks: 234\n      }\n    };\n  }\n  ngOnInit() {\n    this.loadAnalytics();\n  }\n  onPeriodChange() {\n    this.loadAnalytics();\n  }\n  loadAnalytics() {\n    // TODO: Implement API call to get analytics data based on selected period\n    console.log('Loading analytics for period:', this.selectedPeriod);\n  }\n  static {\n    this.ɵfac = function VendorAnalyticsComponent_Factory(t) {\n      return new (t || VendorAnalyticsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VendorAnalyticsComponent,\n      selectors: [[\"app-vendor-analytics\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 139,\n      vars: 23,\n      consts: [[1, \"vendor-analytics-container\"], [1, \"header\"], [1, \"date-filter\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"7\"], [\"value\", \"30\"], [\"value\", \"90\"], [\"value\", \"365\"], [1, \"metrics-grid\"], [1, \"metric-card\"], [1, \"metric-icon\"], [1, \"fas\", \"fa-rupee-sign\"], [1, \"metric-content\"], [1, \"metric-change\", \"positive\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-percentage\"], [1, \"metric-change\", \"negative\"], [1, \"charts-section\"], [1, \"chart-card\"], [1, \"chart-placeholder\"], [1, \"chart-bars\"], [\"class\", \"bar\", 3, \"height\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-labels\"], [4, \"ngFor\", \"ngForOf\"], [1, \"pie-chart\"], [\"class\", \"pie-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"top-products-section\"], [1, \"products-table\"], [1, \"table-header\"], [\"class\", \"table-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"social-performance\"], [1, \"performance-grid\"], [1, \"performance-card\"], [1, \"performance-stats\"], [1, \"stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"bar\"], [1, \"bar-value\"], [1, \"pie-item\"], [1, \"pie-color\"], [1, \"pie-label\"], [1, \"pie-value\"], [1, \"table-row\"], [1, \"product-info\"], [3, \"src\", \"alt\"]],\n      template: function VendorAnalyticsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Analytics Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"select\", 3);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function VendorAnalyticsComponent_Template_select_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedPeriod, $event) || (ctx.selectedPeriod = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"change\", function VendorAnalyticsComponent_Template_select_change_5_listener() {\n            return ctx.onPeriodChange();\n          });\n          i0.ɵɵelementStart(6, \"option\", 4);\n          i0.ɵɵtext(7, \"Last 7 days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"option\", 5);\n          i0.ɵɵtext(9, \"Last 30 days\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"option\", 6);\n          i0.ɵɵtext(11, \"Last 3 months\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"option\", 7);\n          i0.ɵɵtext(13, \"Last year\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9)(16, \"div\", 10);\n          i0.ɵɵelement(17, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 12)(19, \"h3\");\n          i0.ɵɵtext(20);\n          i0.ɵɵpipe(21, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"Total Revenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 13);\n          i0.ɵɵtext(25, \"+12.5%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 9)(27, \"div\", 10);\n          i0.ɵɵelement(28, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 12)(30, \"h3\");\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p\");\n          i0.ɵɵtext(33, \"Total Orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 13);\n          i0.ɵɵtext(35, \"+8.3%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10);\n          i0.ɵɵelement(38, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 12)(40, \"h3\");\n          i0.ɵɵtext(41);\n          i0.ɵɵpipe(42, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Product Views\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"span\", 13);\n          i0.ɵɵtext(46, \"+15.7%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 9)(48, \"div\", 10);\n          i0.ɵɵelement(49, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 12)(51, \"h3\");\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"p\");\n          i0.ɵɵtext(54, \"Conversion Rate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"span\", 17);\n          i0.ɵɵtext(56, \"-2.1%\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(57, \"div\", 18)(58, \"div\", 19)(59, \"h3\");\n          i0.ɵɵtext(60, \"Revenue Trend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 20)(62, \"div\", 21);\n          i0.ɵɵtemplate(63, VendorAnalyticsComponent_div_63_Template, 4, 6, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 23);\n          i0.ɵɵtemplate(65, VendorAnalyticsComponent_span_65_Template, 2, 1, \"span\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 19)(67, \"h3\");\n          i0.ɵɵtext(68, \"Order Status Distribution\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 25);\n          i0.ɵɵtemplate(70, VendorAnalyticsComponent_div_70_Template, 6, 4, \"div\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"div\", 27)(72, \"h3\");\n          i0.ɵɵtext(73, \"Top Performing Products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 28)(75, \"div\", 29)(76, \"span\");\n          i0.ɵɵtext(77, \"Product\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\");\n          i0.ɵɵtext(79, \"Views\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"span\");\n          i0.ɵɵtext(81, \"Orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"span\");\n          i0.ɵɵtext(83, \"Revenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"span\");\n          i0.ɵɵtext(85, \"Conversion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(86, VendorAnalyticsComponent_div_86_Template, 15, 13, \"div\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 31)(88, \"h3\");\n          i0.ɵɵtext(89, \"Content Performance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"div\", 32)(91, \"div\", 33)(92, \"h4\");\n          i0.ɵɵtext(93, \"Posts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"div\", 34)(95, \"div\", 35)(96, \"span\", 36);\n          i0.ɵɵtext(97);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"span\", 37);\n          i0.ɵɵtext(99, \"Total Posts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"div\", 35)(101, \"span\", 36);\n          i0.ɵɵtext(102);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"span\", 37);\n          i0.ɵɵtext(104, \"Total Likes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(105, \"div\", 35)(106, \"span\", 36);\n          i0.ɵɵtext(107);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"span\", 37);\n          i0.ɵɵtext(109, \"Comments\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(110, \"div\", 35)(111, \"span\", 36);\n          i0.ɵɵtext(112);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"span\", 37);\n          i0.ɵɵtext(114, \"Shares\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(115, \"div\", 33)(116, \"h4\");\n          i0.ɵɵtext(117, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"div\", 34)(119, \"div\", 35)(120, \"span\", 36);\n          i0.ɵɵtext(121);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"span\", 37);\n          i0.ɵɵtext(123, \"Total Stories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(124, \"div\", 35)(125, \"span\", 36);\n          i0.ɵɵtext(126);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"span\", 37);\n          i0.ɵɵtext(128, \"Total Views\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(129, \"div\", 35)(130, \"span\", 36);\n          i0.ɵɵtext(131);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"span\", 37);\n          i0.ɵɵtext(133, \"Replies\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(134, \"div\", 35)(135, \"span\", 36);\n          i0.ɵɵtext(136);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"span\", 37);\n          i0.ɵɵtext(138, \"Product Clicks\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedPeriod);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(21, 17, ctx.analytics.revenue, \"1.0-0\"), \"\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.analytics.orders);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(42, 20, ctx.analytics.views, \"1.0-0\"));\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", ctx.analytics.conversionRate, \"%\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.revenueData);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.chartLabels);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.orderStatusData);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topProducts);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.socialStats.posts.total);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.socialStats.posts.likes);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.socialStats.posts.comments);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.socialStats.posts.shares);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.socialStats.stories.total);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.socialStats.stories.views);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.socialStats.stories.replies);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.socialStats.stories.productClicks);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.DecimalPipe, FormsModule, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel],\n      styles: [\".vendor-analytics-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n}\\n\\n.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n\\n.date-filter[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n}\\n\\n.metrics-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.metric-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.metric-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  background: #f0f8ff;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #007bff;\\n  font-size: 1.2rem;\\n}\\n\\n.metric-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  margin-bottom: 4px;\\n  color: #333;\\n}\\n\\n.metric-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 8px;\\n}\\n\\n.metric-change[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n\\n.metric-change.positive[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n\\n.metric-change.negative[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n\\n.charts-section[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 24px;\\n  margin-bottom: 30px;\\n}\\n\\n.chart-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.chart-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n}\\n\\n.chart-placeholder[_ngcontent-%COMP%] {\\n  height: 200px;\\n  position: relative;\\n}\\n\\n.chart-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  height: 160px;\\n  gap: 8px;\\n  margin-bottom: 10px;\\n}\\n\\n.bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(to top, #007bff, #66b3ff);\\n  border-radius: 4px 4px 0 0;\\n  position: relative;\\n  min-height: 20px;\\n}\\n\\n.bar-value[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  font-size: 0.7rem;\\n  color: #666;\\n}\\n\\n.chart-labels[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.pie-chart[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.pie-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.pie-color[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 50%;\\n}\\n\\n.pie-label[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 0.9rem;\\n}\\n\\n.pie-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.top-products-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 30px;\\n}\\n\\n.top-products-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n}\\n\\n.products-table[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.table-header[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;\\n  gap: 16px;\\n  padding: 12px 0;\\n  border-bottom: 2px solid #eee;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.table-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;\\n  gap: 16px;\\n  padding: 16px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n  align-items: center;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  object-fit: cover;\\n  border-radius: 6px;\\n}\\n\\n.social-performance[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.social-performance[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n}\\n\\n.performance-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 24px;\\n}\\n\\n.performance-card[_ngcontent-%COMP%] {\\n  border: 1px solid #eee;\\n  border-radius: 8px;\\n  padding: 20px;\\n}\\n\\n.performance-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #333;\\n}\\n\\n.performance-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n\\n.stat[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #333;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n@media (max-width: 768px) {\\n  .header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  .charts-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .performance-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .table-header[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .table-row[_ngcontent-%COMP%] {\\n    background: #f8f9fa;\\n    border-radius: 8px;\\n    padding: 16px;\\n    margin-bottom: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvdmVuZG9yL3BhZ2VzL2FuYWx5dGljcy92ZW5kb3ItYW5hbHl0aWNzLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSx3Q0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0FBQU47O0FBR0k7RUFDRSxXQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHdDQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLHFCQUFBO0VBQ0EsYUFBQTtFQUNBLFFBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsT0FBQTtFQUNBLHFEQUFBO0VBQ0EsMEJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLGlCQUFBO0VBQ0EsV0FBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsT0FBQTtFQUNBLGlCQUFBO0FBQU47O0FBR0k7RUFDRSxnQkFBQTtFQUNBLFdBQUE7QUFBTjs7QUFHSTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esd0NBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSwwQ0FBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSwwQ0FBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0NBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHdDQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLFNBQUE7QUFBTjs7QUFHSTtFQUNFLHNCQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0FBQU47O0FBR0k7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxTQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7QUFBTjs7QUFHSTtFQUNFLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUFBTjs7QUFHSTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSxTQUFBO0lBQ0Esb0JBQUE7RUFBTjtFQUdJO0lBQ0UsMEJBQUE7RUFETjtFQUlJO0lBQ0UsMEJBQUE7RUFGTjtFQUtJOztJQUVFLDBCQUFBO0lBQ0EsUUFBQTtFQUhOO0VBTUk7SUFDRSxhQUFBO0VBSk47RUFPSTtJQUNFLG1CQUFBO0lBQ0Esa0JBQUE7SUFDQSxhQUFBO0lBQ0Esa0JBQUE7RUFMTjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnZlbmRvci1hbmFseXRpY3MtY29udGFpbmVyIHtcbiAgICAgIG1heC13aWR0aDogMTIwMHB4O1xuICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgIH1cblxuICAgIC5oZWFkZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xuICAgIH1cblxuICAgIC5oZWFkZXIgaDEge1xuICAgICAgZm9udC1zaXplOiAycmVtO1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG5cbiAgICAuZGF0ZS1maWx0ZXIgc2VsZWN0IHtcbiAgICAgIHBhZGRpbmc6IDhweCAxMnB4O1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RkZDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcbiAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgIH1cblxuICAgIC5tZXRyaWNzLWdyaWQge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjUwcHgsIDFmcikpO1xuICAgICAgZ2FwOiAyMHB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDtcbiAgICB9XG5cbiAgICAubWV0cmljLWNhcmQge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgcGFkZGluZzogMjRweDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTZweDtcbiAgICB9XG5cbiAgICAubWV0cmljLWljb24ge1xuICAgICAgd2lkdGg6IDUwcHg7XG4gICAgICBoZWlnaHQ6IDUwcHg7XG4gICAgICBiYWNrZ3JvdW5kOiAjZjBmOGZmO1xuICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBjb2xvcjogIzAwN2JmZjtcbiAgICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xuICAgIH1cblxuICAgIC5tZXRyaWMtY29udGVudCBoMyB7XG4gICAgICBmb250LXNpemU6IDEuOHJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICB9XG5cbiAgICAubWV0cmljLWNvbnRlbnQgcCB7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgIH1cblxuICAgIC5tZXRyaWMtY2hhbmdlIHtcbiAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICB9XG5cbiAgICAubWV0cmljLWNoYW5nZS5wb3NpdGl2ZSB7XG4gICAgICBjb2xvcjogIzI4YTc0NTtcbiAgICB9XG5cbiAgICAubWV0cmljLWNoYW5nZS5uZWdhdGl2ZSB7XG4gICAgICBjb2xvcjogI2RjMzU0NTtcbiAgICB9XG5cbiAgICAuY2hhcnRzLXNlY3Rpb24ge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMmZyIDFmcjtcbiAgICAgIGdhcDogMjRweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7XG4gICAgfVxuXG4gICAgLmNoYXJ0LWNhcmQge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgcGFkZGluZzogMjRweDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7XG4gICAgfVxuXG4gICAgLmNoYXJ0LWNhcmQgaDMge1xuICAgICAgZm9udC1zaXplOiAxLjJyZW07XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICB9XG5cbiAgICAuY2hhcnQtcGxhY2Vob2xkZXIge1xuICAgICAgaGVpZ2h0OiAyMDBweDtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB9XG5cbiAgICAuY2hhcnQtYmFycyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xuICAgICAgaGVpZ2h0OiAxNjBweDtcbiAgICAgIGdhcDogOHB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDtcbiAgICB9XG5cbiAgICAuYmFyIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gdG9wLCAjMDA3YmZmLCAjNjZiM2ZmKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweCA0cHggMCAwO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgbWluLWhlaWdodDogMjBweDtcbiAgICB9XG5cbiAgICAuYmFyLXZhbHVlIHtcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIHRvcDogLTIwcHg7XG4gICAgICBsZWZ0OiA1MCU7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XG4gICAgICBmb250LXNpemU6IDAuN3JlbTtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cblxuICAgIC5jaGFydC1sYWJlbHMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgfVxuXG4gICAgLnBpZS1jaGFydCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogMTJweDtcbiAgICB9XG5cbiAgICAucGllLWl0ZW0ge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDEycHg7XG4gICAgfVxuXG4gICAgLnBpZS1jb2xvciB7XG4gICAgICB3aWR0aDogMTZweDtcbiAgICAgIGhlaWdodDogMTZweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICB9XG5cbiAgICAucGllLWxhYmVsIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICB9XG5cbiAgICAucGllLXZhbHVlIHtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICB9XG5cbiAgICAudG9wLXByb2R1Y3RzLXNlY3Rpb24ge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgcGFkZGluZzogMjRweDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xuICAgIH1cblxuICAgIC50b3AtcHJvZHVjdHMtc2VjdGlvbiBoMyB7XG4gICAgICBmb250LXNpemU6IDEuMnJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIH1cblxuICAgIC5wcm9kdWN0cy10YWJsZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICB9XG5cbiAgICAudGFibGUtaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDJmciAxZnIgMWZyIDFmciAxZnI7XG4gICAgICBnYXA6IDE2cHg7XG4gICAgICBwYWRkaW5nOiAxMnB4IDA7XG4gICAgICBib3JkZXItYm90dG9tOiAycHggc29saWQgI2VlZTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICB9XG5cbiAgICAudGFibGUtcm93IHtcbiAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDJmciAxZnIgMWZyIDFmciAxZnI7XG4gICAgICBnYXA6IDE2cHg7XG4gICAgICBwYWRkaW5nOiAxNnB4IDA7XG4gICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtaW5mbyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTJweDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1pbmZvIGltZyB7XG4gICAgICB3aWR0aDogNDBweDtcbiAgICAgIGhlaWdodDogNDBweDtcbiAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgIH1cblxuICAgIC5zb2NpYWwtcGVyZm9ybWFuY2Uge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgcGFkZGluZzogMjRweDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7XG4gICAgfVxuXG4gICAgLnNvY2lhbC1wZXJmb3JtYW5jZSBoMyB7XG4gICAgICBmb250LXNpemU6IDEuMnJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIH1cblxuICAgIC5wZXJmb3JtYW5jZS1ncmlkIHtcbiAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7XG4gICAgICBnYXA6IDI0cHg7XG4gICAgfVxuXG4gICAgLnBlcmZvcm1hbmNlLWNhcmQge1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2VlZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIHBhZGRpbmc6IDIwcHg7XG4gICAgfVxuXG4gICAgLnBlcmZvcm1hbmNlLWNhcmQgaDQge1xuICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICAgIGNvbG9yOiAjMzMzO1xuICAgIH1cblxuICAgIC5wZXJmb3JtYW5jZS1zdGF0cyB7XG4gICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgMWZyO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgIH1cblxuICAgIC5zdGF0IHtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG5cbiAgICAuc3RhdC12YWx1ZSB7XG4gICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgIGNvbG9yOiAjMzMzO1xuICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgIH1cblxuICAgIC5zdGF0LWxhYmVsIHtcbiAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgLmhlYWRlciB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogMTZweDtcbiAgICAgICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG4gICAgICB9XG5cbiAgICAgIC5jaGFydHMtc2VjdGlvbiB7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgICAgfVxuXG4gICAgICAucGVyZm9ybWFuY2UtZ3JpZCB7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgICAgfVxuXG4gICAgICAudGFibGUtaGVhZGVyLFxuICAgICAgLnRhYmxlLXJvdyB7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgICAgICBnYXA6IDhweDtcbiAgICAgIH1cblxuICAgICAgLnRhYmxlLWhlYWRlciB7XG4gICAgICAgIGRpc3BsYXk6IG5vbmU7XG4gICAgICB9XG5cbiAgICAgIC50YWJsZS1yb3cge1xuICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "data_r1", "percentage", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "value", "ɵɵtextInterpolate", "label_r2", "ɵɵelement", "item_r3", "color", "label", "ɵɵproperty", "product_r4", "image", "ɵɵsanitizeUrl", "name", "views", "orders", "revenue", "conversion", "VendorAnalyticsComponent", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "analytics", "conversionRate", "revenueData", "chartLabels", "orderStatusData", "topProducts", "socialStats", "posts", "total", "likes", "comments", "shares", "stories", "replies", "productClicks", "ngOnInit", "loadAnalytics", "onPeriodChange", "console", "log", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "VendorAnalyticsComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "VendorAnalyticsComponent_Template_select_ngModelChange_5_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "VendorAnalyticsComponent_Template_select_change_5_listener", "ɵɵtemplate", "VendorAnalyticsComponent_div_63_Template", "VendorAnalyticsComponent_span_65_Template", "VendorAnalyticsComponent_div_70_Template", "VendorAnalyticsComponent_div_86_Template", "ɵɵtwoWayProperty", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DecimalPipe", "i2", "NgSelectOption", "ɵNgSelectMultipleOption", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\vendor\\pages\\analytics\\vendor-analytics.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-vendor-analytics',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"vendor-analytics-container\">\n      <div class=\"header\">\n        <h1>Analytics Dashboard</h1>\n        <div class=\"date-filter\">\n          <select [(ngModel)]=\"selectedPeriod\" (change)=\"onPeriodChange()\">\n            <option value=\"7\">Last 7 days</option>\n            <option value=\"30\">Last 30 days</option>\n            <option value=\"90\">Last 3 months</option>\n            <option value=\"365\">Last year</option>\n          </select>\n        </div>\n      </div>\n\n      <!-- Key Metrics -->\n      <div class=\"metrics-grid\">\n        <div class=\"metric-card\">\n          <div class=\"metric-icon\">\n            <i class=\"fas fa-rupee-sign\"></i>\n          </div>\n          <div class=\"metric-content\">\n            <h3>₹{{ analytics.revenue | number:'1.0-0' }}</h3>\n            <p>Total Revenue</p>\n            <span class=\"metric-change positive\">+12.5%</span>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon\">\n            <i class=\"fas fa-shopping-cart\"></i>\n          </div>\n          <div class=\"metric-content\">\n            <h3>{{ analytics.orders }}</h3>\n            <p>Total Orders</p>\n            <span class=\"metric-change positive\">+8.3%</span>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon\">\n            <i class=\"fas fa-eye\"></i>\n          </div>\n          <div class=\"metric-content\">\n            <h3>{{ analytics.views | number:'1.0-0' }}</h3>\n            <p>Product Views</p>\n            <span class=\"metric-change positive\">+15.7%</span>\n          </div>\n        </div>\n\n        <div class=\"metric-card\">\n          <div class=\"metric-icon\">\n            <i class=\"fas fa-percentage\"></i>\n          </div>\n          <div class=\"metric-content\">\n            <h3>{{ analytics.conversionRate }}%</h3>\n            <p>Conversion Rate</p>\n            <span class=\"metric-change negative\">-2.1%</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Charts Section -->\n      <div class=\"charts-section\">\n        <div class=\"chart-card\">\n          <h3>Revenue Trend</h3>\n          <div class=\"chart-placeholder\">\n            <div class=\"chart-bars\">\n              <div class=\"bar\" *ngFor=\"let data of revenueData\" [style.height.%]=\"data.percentage\">\n                <span class=\"bar-value\">₹{{ data.value | number:'1.0-0' }}</span>\n              </div>\n            </div>\n            <div class=\"chart-labels\">\n              <span *ngFor=\"let label of chartLabels\">{{ label }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"chart-card\">\n          <h3>Order Status Distribution</h3>\n          <div class=\"pie-chart\">\n            <div class=\"pie-item\" *ngFor=\"let item of orderStatusData\">\n              <div class=\"pie-color\" [style.background-color]=\"item.color\"></div>\n              <span class=\"pie-label\">{{ item.label }}</span>\n              <span class=\"pie-value\">{{ item.value }}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Top Products -->\n      <div class=\"top-products-section\">\n        <h3>Top Performing Products</h3>\n        <div class=\"products-table\">\n          <div class=\"table-header\">\n            <span>Product</span>\n            <span>Views</span>\n            <span>Orders</span>\n            <span>Revenue</span>\n            <span>Conversion</span>\n          </div>\n          <div class=\"table-row\" *ngFor=\"let product of topProducts\">\n            <div class=\"product-info\">\n              <img [src]=\"product.image\" [alt]=\"product.name\">\n              <span>{{ product.name }}</span>\n            </div>\n            <span>{{ product.views | number:'1.0-0' }}</span>\n            <span>{{ product.orders }}</span>\n            <span>₹{{ product.revenue | number:'1.0-0' }}</span>\n            <span>{{ product.conversion }}%</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Social Media Performance -->\n      <div class=\"social-performance\">\n        <h3>Content Performance</h3>\n        <div class=\"performance-grid\">\n          <div class=\"performance-card\">\n            <h4>Posts</h4>\n            <div class=\"performance-stats\">\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.posts.total }}</span>\n                <span class=\"stat-label\">Total Posts</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.posts.likes }}</span>\n                <span class=\"stat-label\">Total Likes</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.posts.comments }}</span>\n                <span class=\"stat-label\">Comments</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.posts.shares }}</span>\n                <span class=\"stat-label\">Shares</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"performance-card\">\n            <h4>Stories</h4>\n            <div class=\"performance-stats\">\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.stories.total }}</span>\n                <span class=\"stat-label\">Total Stories</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.stories.views }}</span>\n                <span class=\"stat-label\">Total Views</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.stories.replies }}</span>\n                <span class=\"stat-label\">Replies</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ socialStats.stories.productClicks }}</span>\n                <span class=\"stat-label\">Product Clicks</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .vendor-analytics-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n    }\n\n    .header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 30px;\n    }\n\n    .header h1 {\n      font-size: 2rem;\n      font-weight: 600;\n    }\n\n    .date-filter select {\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 6px;\n      font-size: 0.9rem;\n    }\n\n    .metrics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 20px;\n      margin-bottom: 30px;\n    }\n\n    .metric-card {\n      background: white;\n      border-radius: 12px;\n      padding: 24px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n      display: flex;\n      align-items: center;\n      gap: 16px;\n    }\n\n    .metric-icon {\n      width: 50px;\n      height: 50px;\n      background: #f0f8ff;\n      border-radius: 12px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #007bff;\n      font-size: 1.2rem;\n    }\n\n    .metric-content h3 {\n      font-size: 1.8rem;\n      font-weight: 700;\n      margin-bottom: 4px;\n      color: #333;\n    }\n\n    .metric-content p {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .metric-change {\n      font-size: 0.8rem;\n      font-weight: 500;\n    }\n\n    .metric-change.positive {\n      color: #28a745;\n    }\n\n    .metric-change.negative {\n      color: #dc3545;\n    }\n\n    .charts-section {\n      display: grid;\n      grid-template-columns: 2fr 1fr;\n      gap: 24px;\n      margin-bottom: 30px;\n    }\n\n    .chart-card {\n      background: white;\n      border-radius: 12px;\n      padding: 24px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n    }\n\n    .chart-card h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n\n    .chart-placeholder {\n      height: 200px;\n      position: relative;\n    }\n\n    .chart-bars {\n      display: flex;\n      align-items: flex-end;\n      height: 160px;\n      gap: 8px;\n      margin-bottom: 10px;\n    }\n\n    .bar {\n      flex: 1;\n      background: linear-gradient(to top, #007bff, #66b3ff);\n      border-radius: 4px 4px 0 0;\n      position: relative;\n      min-height: 20px;\n    }\n\n    .bar-value {\n      position: absolute;\n      top: -20px;\n      left: 50%;\n      transform: translateX(-50%);\n      font-size: 0.7rem;\n      color: #666;\n    }\n\n    .chart-labels {\n      display: flex;\n      justify-content: space-between;\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .pie-chart {\n      display: flex;\n      flex-direction: column;\n      gap: 12px;\n    }\n\n    .pie-item {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .pie-color {\n      width: 16px;\n      height: 16px;\n      border-radius: 50%;\n    }\n\n    .pie-label {\n      flex: 1;\n      font-size: 0.9rem;\n    }\n\n    .pie-value {\n      font-weight: 600;\n      color: #333;\n    }\n\n    .top-products-section {\n      background: white;\n      border-radius: 12px;\n      padding: 24px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n      margin-bottom: 30px;\n    }\n\n    .top-products-section h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n\n    .products-table {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .table-header {\n      display: grid;\n      grid-template-columns: 2fr 1fr 1fr 1fr 1fr;\n      gap: 16px;\n      padding: 12px 0;\n      border-bottom: 2px solid #eee;\n      font-weight: 600;\n      color: #333;\n    }\n\n    .table-row {\n      display: grid;\n      grid-template-columns: 2fr 1fr 1fr 1fr 1fr;\n      gap: 16px;\n      padding: 16px 0;\n      border-bottom: 1px solid #f0f0f0;\n      align-items: center;\n    }\n\n    .product-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .product-info img {\n      width: 40px;\n      height: 40px;\n      object-fit: cover;\n      border-radius: 6px;\n    }\n\n    .social-performance {\n      background: white;\n      border-radius: 12px;\n      padding: 24px;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n    }\n\n    .social-performance h3 {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n\n    .performance-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 24px;\n    }\n\n    .performance-card {\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 20px;\n    }\n\n    .performance-card h4 {\n      font-size: 1.1rem;\n      font-weight: 600;\n      margin-bottom: 16px;\n      color: #333;\n    }\n\n    .performance-stats {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 16px;\n    }\n\n    .stat {\n      text-align: center;\n    }\n\n    .stat-value {\n      display: block;\n      font-size: 1.5rem;\n      font-weight: 700;\n      color: #333;\n      margin-bottom: 4px;\n    }\n\n    .stat-label {\n      font-size: 0.8rem;\n      color: #666;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    @media (max-width: 768px) {\n      .header {\n        flex-direction: column;\n        gap: 16px;\n        align-items: stretch;\n      }\n\n      .charts-section {\n        grid-template-columns: 1fr;\n      }\n\n      .performance-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .table-header,\n      .table-row {\n        grid-template-columns: 1fr;\n        gap: 8px;\n      }\n\n      .table-header {\n        display: none;\n      }\n\n      .table-row {\n        background: #f8f9fa;\n        border-radius: 8px;\n        padding: 16px;\n        margin-bottom: 8px;\n      }\n    }\n  `]\n})\nexport class VendorAnalyticsComponent implements OnInit {\n  selectedPeriod = '30';\n  \n  analytics = {\n    revenue: 125000,\n    orders: 150,\n    views: 12500,\n    conversionRate: 3.2\n  };\n\n  revenueData = [\n    { value: 15000, percentage: 60 },\n    { value: 18000, percentage: 72 },\n    { value: 22000, percentage: 88 },\n    { value: 25000, percentage: 100 },\n    { value: 20000, percentage: 80 },\n    { value: 23000, percentage: 92 },\n    { value: 28000, percentage: 100 }\n  ];\n\n  chartLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n\n  orderStatusData = [\n    { label: 'Delivered', value: 65, color: '#28a745' },\n    { label: 'Shipped', value: 20, color: '#007bff' },\n    { label: 'Confirmed', value: 10, color: '#ffc107' },\n    { label: 'Pending', value: 5, color: '#dc3545' }\n  ];\n\n  topProducts = [\n    {\n      name: 'Summer Dress',\n      image: '/assets/images/product1.jpg',\n      views: 2500,\n      orders: 45,\n      revenue: 134550,\n      conversion: 1.8\n    },\n    {\n      name: 'Casual Shirt',\n      image: '/assets/images/product2.jpg',\n      views: 1800,\n      orders: 32,\n      revenue: 51168,\n      conversion: 1.7\n    },\n    {\n      name: 'Sneakers',\n      image: '/assets/images/product3.jpg',\n      views: 1200,\n      orders: 28,\n      revenue: 139720,\n      conversion: 2.3\n    }\n  ];\n\n  socialStats = {\n    posts: {\n      total: 12,\n      likes: 456,\n      comments: 89,\n      shares: 34\n    },\n    stories: {\n      total: 8,\n      views: 1234,\n      replies: 67,\n      productClicks: 234\n    }\n  };\n\n  constructor() {}\n\n  ngOnInit() {\n    this.loadAnalytics();\n  }\n\n  onPeriodChange() {\n    this.loadAnalytics();\n  }\n\n  loadAnalytics() {\n    // TODO: Implement API call to get analytics data based on selected period\n    console.log('Loading analytics for period:', this.selectedPeriod);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;IA0E5BC,EADF,CAAAC,cAAA,cAAqF,eAC3D;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAC5DF,EAD4D,CAAAG,YAAA,EAAO,EAC7D;;;;IAF4CH,EAAA,CAAAI,WAAA,WAAAC,OAAA,CAAAC,UAAA,MAAkC;IAC1DN,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,WAAAR,EAAA,CAAAS,WAAA,OAAAJ,OAAA,CAAAK,KAAA,eAAkC;;;;;IAI5DV,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlBH,EAAA,CAAAO,SAAA,EAAW;IAAXP,EAAA,CAAAW,iBAAA,CAAAC,QAAA,CAAW;;;;;IAQrDZ,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAa,SAAA,cAAmE;IACnEb,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;;;;IAHmBH,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAI,WAAA,qBAAAU,OAAA,CAAAC,KAAA,CAAqC;IACpCf,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAW,iBAAA,CAAAG,OAAA,CAAAE,KAAA,CAAgB;IAChBhB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,kBAAA,KAAAM,OAAA,CAAAJ,KAAA,MAAiB;;;;;IAkB3CV,EADF,CAAAC,cAAA,cAA2D,cAC/B;IACxBD,EAAA,CAAAa,SAAA,cAAgD;IAChDb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;;;;IAPGH,EAAA,CAAAO,SAAA,GAAqB;IAACP,EAAtB,CAAAiB,UAAA,QAAAC,UAAA,CAAAC,KAAA,EAAAnB,EAAA,CAAAoB,aAAA,CAAqB,QAAAF,UAAA,CAAAG,IAAA,CAAqB;IACzCrB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,iBAAA,CAAAO,UAAA,CAAAG,IAAA,CAAkB;IAEpBrB,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAS,WAAA,OAAAS,UAAA,CAAAI,KAAA,WAAoC;IACpCtB,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAO,UAAA,CAAAK,MAAA,CAAoB;IACpBvB,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,kBAAA,WAAAR,EAAA,CAAAS,WAAA,SAAAS,UAAA,CAAAM,OAAA,eAAuC;IACvCxB,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,kBAAA,KAAAU,UAAA,CAAAO,UAAA,MAAyB;;;AA4W3C,OAAM,MAAOC,wBAAwB;EAuEnCC,YAAA;IAtEA,KAAAC,cAAc,GAAG,IAAI;IAErB,KAAAC,SAAS,GAAG;MACVL,OAAO,EAAE,MAAM;MACfD,MAAM,EAAE,GAAG;MACXD,KAAK,EAAE,KAAK;MACZQ,cAAc,EAAE;KACjB;IAED,KAAAC,WAAW,GAAG,CACZ;MAAErB,KAAK,EAAE,KAAK;MAAEJ,UAAU,EAAE;IAAE,CAAE,EAChC;MAAEI,KAAK,EAAE,KAAK;MAAEJ,UAAU,EAAE;IAAE,CAAE,EAChC;MAAEI,KAAK,EAAE,KAAK;MAAEJ,UAAU,EAAE;IAAE,CAAE,EAChC;MAAEI,KAAK,EAAE,KAAK;MAAEJ,UAAU,EAAE;IAAG,CAAE,EACjC;MAAEI,KAAK,EAAE,KAAK;MAAEJ,UAAU,EAAE;IAAE,CAAE,EAChC;MAAEI,KAAK,EAAE,KAAK;MAAEJ,UAAU,EAAE;IAAE,CAAE,EAChC;MAAEI,KAAK,EAAE,KAAK;MAAEJ,UAAU,EAAE;IAAG,CAAE,CAClC;IAED,KAAA0B,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE/D,KAAAC,eAAe,GAAG,CAChB;MAAEjB,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE,EAAE;MAAEK,KAAK,EAAE;IAAS,CAAE,EACnD;MAAEC,KAAK,EAAE,SAAS;MAAEN,KAAK,EAAE,EAAE;MAAEK,KAAK,EAAE;IAAS,CAAE,EACjD;MAAEC,KAAK,EAAE,WAAW;MAAEN,KAAK,EAAE,EAAE;MAAEK,KAAK,EAAE;IAAS,CAAE,EACnD;MAAEC,KAAK,EAAE,SAAS;MAAEN,KAAK,EAAE,CAAC;MAAEK,KAAK,EAAE;IAAS,CAAE,CACjD;IAED,KAAAmB,WAAW,GAAG,CACZ;MACEb,IAAI,EAAE,cAAc;MACpBF,KAAK,EAAE,6BAA6B;MACpCG,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;KACb,EACD;MACEJ,IAAI,EAAE,cAAc;MACpBF,KAAK,EAAE,6BAA6B;MACpCG,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;KACb,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBF,KAAK,EAAE,6BAA6B;MACpCG,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;KACb,CACF;IAED,KAAAU,WAAW,GAAG;MACZC,KAAK,EAAE;QACLC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE;OACT;MACDC,OAAO,EAAE;QACPJ,KAAK,EAAE,CAAC;QACRf,KAAK,EAAE,IAAI;QACXoB,OAAO,EAAE,EAAE;QACXC,aAAa,EAAE;;KAElB;EAEc;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACD,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX;IACAE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACpB,cAAc,CAAC;EACnE;;;uBApFWF,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAuB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnD,EAAA,CAAAoD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArd7B1D,EAFJ,CAAAC,cAAA,aAAwC,aAClB,SACd;UAAAD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE1BH,EADF,CAAAC,cAAA,aAAyB,gBAC0C;UAAzDD,EAAA,CAAA4D,gBAAA,2BAAAC,kEAAAC,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA/B,cAAA,EAAAkC,MAAA,MAAAH,GAAA,CAAA/B,cAAA,GAAAkC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAAC9D,EAAA,CAAAgE,UAAA,oBAAAC,2DAAA;YAAA,OAAUN,GAAA,CAAAb,cAAA,EAAgB;UAAA,EAAC;UAC9D9C,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzCH,EAAA,CAAAC,cAAA,iBAAoB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAGnCF,EAHmC,CAAAG,YAAA,EAAS,EAC/B,EACL,EACF;UAKFH,EAFJ,CAAAC,cAAA,cAA0B,cACC,eACE;UACvBD,EAAA,CAAAa,SAAA,aAAiC;UACnCb,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAAyC;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpBH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;UAGJH,EADF,CAAAC,cAAA,cAAyB,eACE;UACvBD,EAAA,CAAAa,SAAA,aAAoC;UACtCb,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnBH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAE9CF,EAF8C,CAAAG,YAAA,EAAO,EAC7C,EACF;UAGJH,EADF,CAAAC,cAAA,cAAyB,eACE;UACvBD,EAAA,CAAAa,SAAA,aAA0B;UAC5Bb,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAAsC;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpBH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;UAGJH,EADF,CAAAC,cAAA,cAAyB,eACE;UACvBD,EAAA,CAAAa,SAAA,aAAiC;UACnCb,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAE,MAAA,IAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtBH,EAAA,CAAAC,cAAA,gBAAqC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAGhDF,EAHgD,CAAAG,YAAA,EAAO,EAC7C,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,eAA4B,eACF,UAClB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpBH,EADF,CAAAC,cAAA,eAA+B,eACL;UACtBD,EAAA,CAAAkE,UAAA,KAAAC,wCAAA,kBAAqF;UAGvFnE,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAAkE,UAAA,KAAAE,yCAAA,mBAAwC;UAG9CpE,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAGJH,EADF,CAAAC,cAAA,eAAwB,UAClB;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAkE,UAAA,KAAAG,wCAAA,kBAA2D;UAOjErE,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAIJH,EADF,CAAAC,cAAA,eAAkC,UAC5B;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG5BH,EAFJ,CAAAC,cAAA,eAA4B,eACA,YAClB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAClBF,EADkB,CAAAG,YAAA,EAAO,EACnB;UACNH,EAAA,CAAAkE,UAAA,KAAAI,wCAAA,oBAA2D;UAW/DtE,EADE,CAAAG,YAAA,EAAM,EACF;UAIJH,EADF,CAAAC,cAAA,eAAgC,UAC1B;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGxBH,EAFJ,CAAAC,cAAA,eAA8B,eACE,UACxB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGVH,EAFJ,CAAAC,cAAA,eAA+B,eACX,gBACS;UAAAD,EAAA,CAAAE,MAAA,IAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7DH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACtCF,EADsC,CAAAG,YAAA,EAAO,EACvC;UAEJH,EADF,CAAAC,cAAA,gBAAkB,iBACS;UAAAD,EAAA,CAAAE,MAAA,KAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7DH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACtCF,EADsC,CAAAG,YAAA,EAAO,EACvC;UAEJH,EADF,CAAAC,cAAA,gBAAkB,iBACS;UAAAD,EAAA,CAAAE,MAAA,KAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChEH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UACnCF,EADmC,CAAAG,YAAA,EAAO,EACpC;UAEJH,EADF,CAAAC,cAAA,gBAAkB,iBACS;UAAAD,EAAA,CAAAE,MAAA,KAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAGrCF,EAHqC,CAAAG,YAAA,EAAO,EAClC,EACF,EACF;UAGJH,EADF,CAAAC,cAAA,gBAA8B,WACxB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGZH,EAFJ,CAAAC,cAAA,gBAA+B,gBACX,iBACS;UAAAD,EAAA,CAAAE,MAAA,KAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACxCF,EADwC,CAAAG,YAAA,EAAO,EACzC;UAEJH,EADF,CAAAC,cAAA,gBAAkB,iBACS;UAAAD,EAAA,CAAAE,MAAA,KAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACtCF,EADsC,CAAAG,YAAA,EAAO,EACvC;UAEJH,EADF,CAAAC,cAAA,gBAAkB,iBACS;UAAAD,EAAA,CAAAE,MAAA,KAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjEH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;UAEJH,EADF,CAAAC,cAAA,gBAAkB,iBACS;UAAAD,EAAA,CAAAE,MAAA,KAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAMnDF,EANmD,CAAAG,YAAA,EAAO,EAC1C,EACF,EACF,EACF,EACF,EACF;;;UA7JQH,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAuE,gBAAA,YAAAZ,GAAA,CAAA/B,cAAA,CAA4B;UAgB9B5B,EAAA,CAAAO,SAAA,IAAyC;UAAzCP,EAAA,CAAAQ,kBAAA,WAAAR,EAAA,CAAAS,WAAA,SAAAkD,GAAA,CAAA9B,SAAA,CAAAL,OAAA,eAAyC;UAWzCxB,EAAA,CAAAO,SAAA,IAAsB;UAAtBP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAA9B,SAAA,CAAAN,MAAA,CAAsB;UAWtBvB,EAAA,CAAAO,SAAA,IAAsC;UAAtCP,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAS,WAAA,SAAAkD,GAAA,CAAA9B,SAAA,CAAAP,KAAA,WAAsC;UAWtCtB,EAAA,CAAAO,SAAA,IAA+B;UAA/BP,EAAA,CAAAQ,kBAAA,KAAAmD,GAAA,CAAA9B,SAAA,CAAAC,cAAA,MAA+B;UAaC9B,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAiB,UAAA,YAAA0C,GAAA,CAAA5B,WAAA,CAAc;UAKxB/B,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAiB,UAAA,YAAA0C,GAAA,CAAA3B,WAAA,CAAc;UAQDhC,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAAiB,UAAA,YAAA0C,GAAA,CAAA1B,eAAA,CAAkB;UAoBhBjC,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAiB,UAAA,YAAA0C,GAAA,CAAAzB,WAAA,CAAc;UAqB1BlC,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAC,KAAA,CAAAC,KAAA,CAA6B;UAI7BrC,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAC,KAAA,CAAAE,KAAA,CAA6B;UAI7BtC,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAC,KAAA,CAAAG,QAAA,CAAgC;UAIhCvC,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAC,KAAA,CAAAI,MAAA,CAA8B;UAU9BxC,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAM,OAAA,CAAAJ,KAAA,CAA+B;UAI/BrC,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAM,OAAA,CAAAnB,KAAA,CAA+B;UAI/BtB,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAM,OAAA,CAAAC,OAAA,CAAiC;UAIjC1C,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAW,iBAAA,CAAAgD,GAAA,CAAAxB,WAAA,CAAAM,OAAA,CAAAE,aAAA,CAAuC;;;qBA5JpE7C,YAAY,EAAA0E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,WAAA,EAAE3E,WAAW,EAAA4E,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,0BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}