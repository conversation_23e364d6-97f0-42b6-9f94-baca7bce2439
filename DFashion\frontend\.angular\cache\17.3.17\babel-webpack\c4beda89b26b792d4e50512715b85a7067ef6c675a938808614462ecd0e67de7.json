{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-slick-carousel\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nfunction ViewAddStoriesComponent_div_23_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"i\", 30);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_23_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", story_r4.products.length, \" item\", story_r4.products.length > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_23_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const story_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.openStory(story_r4, i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"div\", 25)(3, \"div\", 16);\n    i0.ɵɵelement(4, \"img\", 26);\n    i0.ɵɵtemplate(5, ViewAddStoriesComponent_div_23_div_5_Template, 2, 0, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ViewAddStoriesComponent_div_23_div_8_Template, 2, 2, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-products\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", story_r4.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r5.currentStoryIndex)(\"completed\", i_r8 < ctx_r5.currentStoryIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r5.getProgressWidth(i_r8), \"%\");\n  }\n}\nfunction ViewAddStoriesComponent_div_26_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 60, 3);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_div_17_div_1_Template_div_click_0_listener($event) {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      ctx_r5.openProductDetails(productTag_r10.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"div\", 64);\n    i0.ɵɵelementStart(2, \"div\", 65);\n    i0.ɵɵelement(3, \"img\", 66);\n    i0.ɵɵelementStart(4, \"div\", 67)(5, \"span\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 69);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const productTag_r10 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", (productTag_r10.position == null ? null : productTag_r10.position.x) || 50, \"%\")(\"top\", (productTag_r10.position == null ? null : productTag_r10.position.y) || 50, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", productTag_r10.product.images[0] == null ? null : productTag_r10.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", productTag_r10.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r10.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", productTag_r10.product.price, \"\");\n  }\n}\nfunction ViewAddStoriesComponent_div_26_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_26_div_17_div_1_Template, 9, 8, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.getCurrentStory().products);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_button_18_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      ctx_r5.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 30);\n    i0.ɵɵelementStart(2, \"span\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r5.showProductTags);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getCurrentStory().products.length);\n  }\n}\nfunction ViewAddStoriesComponent_div_26_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.previousStory());\n    });\n    i0.ɵɵelement(1, \"i\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_26_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.nextStory());\n    });\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33, 2)(3, \"div\", 34);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_26_div_4_Template, 2, 6, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36);\n    i0.ɵɵelement(6, \"img\", 37);\n    i0.ɵɵelementStart(7, \"div\", 38)(8, \"span\", 39);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 40);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.closeStories());\n    });\n    i0.ɵɵelement(13, \"i\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_div_click_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_26_Template_div_touchstart_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_26_Template_div_touchmove_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_26_Template_div_touchend_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchEnd($event));\n    });\n    i0.ɵɵtemplate(15, ViewAddStoriesComponent_div_26_img_15_Template, 1, 1, \"img\", 44)(16, ViewAddStoriesComponent_div_26_video_16_Template, 2, 1, \"video\", 45)(17, ViewAddStoriesComponent_div_26_div_17_Template, 2, 1, \"div\", 46)(18, ViewAddStoriesComponent_div_26_button_18_Template, 4, 3, \"button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 48)(20, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.toggleLike());\n    });\n    i0.ɵɵelement(21, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.shareStory());\n    });\n    i0.ɵɵelement(23, \"i\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_26_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.saveStory());\n    });\n    i0.ɵɵelement(25, \"i\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(26, ViewAddStoriesComponent_div_26_button_26_Template, 2, 0, \"button\", 55)(27, ViewAddStoriesComponent_div_26_button_27_Template, 2, 0, \"button\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.stories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().user.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getCurrentStory().user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getTimeAgo(ctx_r5.getCurrentStory().createdAt));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showProductTags && ctx_r5.getCurrentStory().products);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().products && ctx_r5.getCurrentStory().products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r5.isLiked);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex < ctx_r5.stories.length - 1);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.storyDuration = 5000; // 5 seconds per story\n    // Slider (carousel) configuration\n    this.storySliderConfig = {\n      slidesToShow: 6,\n      slidesToScroll: 2,\n      infinite: false,\n      arrows: false,\n      dots: false,\n      swipeToSlide: true,\n      responsive: [{\n        breakpoint: 900,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n    /*** Handlers for Adding Stories/Reels (using FormData and Multer) ***/\n    // (The code below shows modals and camera logic; see Node backend for upload handling)\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    // Load stories from backend\n    this.loadStories();\n    // Track current user (for \"Your Story\" avatar)\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n  /*** Story Loading ***/\n  loadStories() {\n    this.isLoadingStories = true;\n    const sub = this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n        } else {\n          // Handle fallback or no data\n          this.stories = [];\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.stories = [];\n        this.isLoadingStories = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n  /*** Story Viewer Open/Close ***/\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    this.startStoryTimer(); // start auto-advance\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    this.clearStoryTimer(); // stop auto-advance\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  /*** Show a specific story and reset rotation state ***/\n  showStory(index) {\n    if (index < 0 || index >= this.stories.length) {\n      this.closeStories();\n      return;\n    }\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  /*** Automatic progression ***/\n  startStoryTimer() {\n    this.clearStoryTimer();\n    this.storyInterval = setInterval(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n  clearStoryTimer() {\n    if (this.storyInterval) {\n      clearInterval(this.storyInterval);\n      this.storyInterval = null;\n    }\n  }\n  /*** Navigate to next story ***/\n  nextStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n  /*** Navigate to previous story ***/\n  previousStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n  /*** Apply smooth 3D rotation between stories ***/\n  updateRotation() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n        this.startStoryTimer(); // restart auto-advance after animation\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.updateRotation());\n  }\n  /*** Pause any playing videos when closing ***/\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  /*** Story Progress (percentage of stories seen) ***/\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  /*** Utility: format time ago (e.g., \"5m\", \"2h\") ***/\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  /*** Handle click on story (left half = previous, right half = next) ***/\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  /*** Drag (swipe) support for touch devices ***/\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n    this.clearStoryTimer(); // pause auto while dragging\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.updateRotation();\n      this.startStoryTimer(); // resume auto-advance\n    }\n  }\n  /*** Keydown navigation (Esc to close, arrows to move) ***/\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    if (event.key === 'ArrowLeft') {\n      this.previousStory();\n    } else if (event.key === 'ArrowRight') {\n      this.nextStory();\n    } else if (event.key === 'Escape') {\n      this.closeStories();\n    }\n  }\n  /*** Utilities for Products and Formatting ***/\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        // Upload video to backend\n        const uploadRes = yield _this2.http.post(`${environment.apiUrl}/stories/upload`, formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post(`${environment.apiUrl}/stories`, reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories(); // refresh list\n      } catch (err) {\n        alert('Failed to upload reel.');\n        console.error(err);\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post(`${environment.apiUrl}/stories/upload`, uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post(`${environment.apiUrl}/stories`, storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n        console.error(err);\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 27,\n      vars: 10,\n      consts: [[\"storiesContainer\", \"\"], [\"slickCarousel\", \"\"], [\"feedCover\", \"\"], [\"storyVideo\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [1, \"create-story-btn\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"stories-slider-wrapper\"], [1, \"nav-arrow\", \"nav-arrow-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"stories-slider\", 3, \"config\"], [\"ngxSlickItem\", \"\", 1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"ngxSlickItem\", \"\", \"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-arrow\", \"nav-arrow-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [\"class\", \"stories-overlay\", 4, \"ngIf\"], [\"ngxSlickItem\", \"\", 1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [\"class\", \"shopping-bag-indicator\", \"title\", \"Shoppable content\", 4, \"ngIf\"], [\"class\", \"product-count-badge\", 4, \"ngIf\"], [\"title\", \"Shoppable content\", 1, \"shopping-bag-indicator\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-count-badge\"], [1, \"stories-overlay\"], [1, \"stories-content\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [\"alt\", \"Avatar\", 1, \"story-header-avatar\", 3, \"src\"], [1, \"story-header-info\"], [1, \"username\"], [1, \"time\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-media\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [\"class\", \"story-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-video\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"shopping-bag-btn\", 3, \"active\", \"click\", 4, \"ngIf\"], [1, \"story-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [1, \"fas\", \"fa-bookmark\"], [\"class\", \"story-nav-btn story-nav-prev\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-nav-btn story-nav-next\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-image\", 3, \"src\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-video\", 3, \"src\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-dot\"], [1, \"product-tag-info\"], [1, \"product-tag-image\", 3, \"src\", \"alt\"], [1, \"product-tag-details\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"shopping-bag-btn\", 3, \"click\"], [1, \"product-count\"], [1, \"story-nav-btn\", \"story-nav-prev\", 3, \"click\"], [1, \"story-nav-btn\", \"story-nav-next\", 3, \"click\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4, 0)(2, \"div\", 5)(3, \"h3\", 6);\n          i0.ɵɵtext(4, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelement(6, \"i\", 8);\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"Create\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollLeft());\n          });\n          i0.ɵɵelement(11, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ngx-slick-carousel\", 12, 1)(14, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_div_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"div\", 15)(17, \"div\", 16);\n          i0.ɵɵelement(18, \"img\", 17);\n          i0.ɵɵelementStart(19, \"span\", 18);\n          i0.ɵɵtext(20, \"+\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 19);\n          i0.ɵɵtext(22, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, ViewAddStoriesComponent_div_23_Template, 9, 7, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollRight());\n          });\n          i0.ɵɵelement(25, \"i\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(26, ViewAddStoriesComponent_div_26_Template, 28, 12, \"div\", 23);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollLeft);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"config\", ctx.storySliderConfig);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollRight);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, SlickCarouselModule, i5.SlickCarouselComponent, i5.SlickItemDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  overflow: hidden;\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 8px;\\n    padding: 12px;\\n    border-radius: 12px;\\n  }\\n}\\n\\n.stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  color: white;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n}\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: white;\\n  transform: scale(1.1);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.nav-arrow.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.nav-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n}\\n@media (max-width: 768px) {\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 0 8px;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n}\\n@media (max-width: 768px) {\\n  .story-item[_ngcontent-%COMP%] {\\n    margin: 0 6px;\\n  }\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  padding: 3px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.story-avatar.has-products[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.8;\\n  }\\n}\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    width: 54px;\\n    height: 54px;\\n  }\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.shopping-bag-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-indicator[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n  .shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: white;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: 80px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n  }\\n}\\n\\n.product-count-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -8px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  white-space: nowrap;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .product-count-badge[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 1px 4px;\\n  }\\n}\\n\\n\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: 2px dashed rgba(255, 255, 255, 0.5);\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  font-size: 18px;\\n  font-weight: bold;\\n  color: #fff;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  bottom: -2px;\\n  right: -2px;\\n  z-index: 1;\\n  border: 2px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n@media (max-width: 768px) {\\n  .add-story-plus[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    font-size: 14px;\\n  }\\n}\\n\\n\\n\\n.stories-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95));\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-overlay[_ngcontent-%COMP%] {\\n    background: #000;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n.stories-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 90%;\\n  max-width: 400px;\\n  height: 80vh;\\n  max-height: 700px;\\n  background: #000;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100vh;\\n    max-height: none;\\n    border-radius: 0;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(50px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n.progress-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 4px;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 3px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n  overflow: hidden;\\n}\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progressFill 5s linear;\\n}\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  border-radius: 2px;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progressFill {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story-header[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);\\n  color: #fff;\\n  z-index: 10;\\n}\\n@media (max-width: 768px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n\\n.story-header-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-header-avatar[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n\\n.story-header-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 2px;\\n}\\n@media (max-width: 768px) {\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-weight: 400;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #fff;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n@media (max-width: 768px) {\\n  .close-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  background: #000;\\n}\\n\\n.story-image[_ngcontent-%COMP%], .story-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n}\\n\\n.story-progress-bar[_ngcontent-%COMP%] {\\n  height: 4px;\\n  background: #444;\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #0f0;\\n  transition: width 0.1s;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "SlickCarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "story_r4", "products", "length", "ɵɵlistener", "ViewAddStoriesComponent_div_23_Template_div_click_0_listener", "ctx_r2", "ɵɵrestoreView", "_r2", "$implicit", "i_r5", "index", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "openStory", "ɵɵtemplate", "ViewAddStoriesComponent_div_23_div_5_Template", "ViewAddStoriesComponent_div_23_div_8_Template", "ɵɵclassProp", "ɵɵproperty", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "i_r8", "currentStoryIndex", "ɵɵstyleProp", "getProgressWidth", "getCurrentStory", "mediaUrl", "ViewAddStoriesComponent_div_26_div_17_div_1_Template_div_click_0_listener", "$event", "productTag_r10", "_r9", "openProductDetails", "product", "stopPropagation", "position", "x", "y", "images", "url", "name", "ɵɵtextInterpolate1", "price", "ViewAddStoriesComponent_div_26_div_17_div_1_Template", "ViewAddStoriesComponent_div_26_button_18_Template_button_click_0_listener", "_r11", "toggleProductTags", "showProductTags", "ViewAddStoriesComponent_div_26_button_26_Template_button_click_0_listener", "_r12", "previousStory", "ViewAddStoriesComponent_div_26_button_27_Template_button_click_0_listener", "_r13", "nextStory", "ViewAddStoriesComponent_div_26_div_4_Template", "ViewAddStoriesComponent_div_26_Template_button_click_12_listener", "_r7", "closeStories", "ViewAddStoriesComponent_div_26_Template_div_click_14_listener", "onStoryClick", "ViewAddStoriesComponent_div_26_Template_div_touchstart_14_listener", "onTouchStart", "ViewAddStoriesComponent_div_26_Template_div_touchmove_14_listener", "onTouchMove", "ViewAddStoriesComponent_div_26_Template_div_touchend_14_listener", "onTouchEnd", "ViewAddStoriesComponent_div_26_img_15_Template", "ViewAddStoriesComponent_div_26_video_16_Template", "ViewAddStoriesComponent_div_26_div_17_Template", "ViewAddStoriesComponent_div_26_button_18_Template", "ViewAddStoriesComponent_div_26_Template_button_click_20_listener", "toggleLike", "ViewAddStoriesComponent_div_26_Template_button_click_22_listener", "shareStory", "ViewAddStoriesComponent_div_26_Template_button_click_24_listener", "saveStory", "ViewAddStoriesComponent_div_26_button_26_Template", "ViewAddStoriesComponent_div_26_button_27_Template", "stories", "getTimeAgo", "createdAt", "mediaType", "isLiked", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "currentUser", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "storyDuration", "storySliderConfig", "slidesToShow", "slidesToScroll", "infinite", "arrows", "dots", "swipeToSlide", "responsive", "breakpoint", "settings", "showAddModal", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "showCameraOrGallery", "permissionDenied", "isRecording", "recordedChunks", "mediaRecorder", "videoStream", "reelPreviewUrl", "isUploadingReel", "newReelCaption", "newStoryFile", "newStoryCaption", "isUploadingStory", "ngOnInit", "loadStories", "currentUser$", "subscribe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "clearStoryTimer", "get", "apiUrl", "next", "response", "success", "storyGroups", "error", "console", "push", "openStories", "showStory", "startStoryTimer", "document", "body", "style", "overflow", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "storyInterval", "setInterval", "clearInterval", "updateRotation", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getStoryProgress", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "event", "clickX", "clientX", "windowWidth", "window", "innerWidth", "touches", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "handleKeydown", "key", "formatNumber", "num", "toFixed", "toString", "formatPrice", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "viewProduct", "navigate", "_id", "hasProducts", "story", "getStoryProducts", "onAdd", "onAddStory", "onAddReel", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "startRecording", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stopRecording", "stop", "submitNewReel", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "caption", "isReel", "cleanupReelStream", "getTracks", "track", "closeAddReelModal", "handlePermissionResponse", "allow", "openCamera", "input", "setAttribute", "click", "openGallery", "removeAttribute", "onStoryFileSelected", "file", "target", "files", "submitNewStory", "_this3", "uploadForm", "storyPayload", "closeAddStoryModal", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_Template_button_click_5_listener", "_r1", "ViewAddStoriesComponent_Template_button_click_10_listener", "scrollLeft", "ViewAddStoriesComponent_Template_div_click_14_listener", "ViewAddStoriesComponent_div_23_Template", "ViewAddStoriesComponent_Template_button_click_24_listener", "scrollRight", "ViewAddStoriesComponent_div_26_Template", "showNavArrows", "canScrollLeft", "canScrollRight", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "SlickCarouselComponent", "SlickItemDirective", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["// import { Component, On<PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, HostListener } from '@angular/core';\n// import { CommonModule } from '@angular/common';\n// import { FormsModule } from '@angular/forms';\n// import { Router } from '@angular/router';\n// import { HttpClient } from '@angular/common/http';\n// import { Subscription } from 'rxjs';\n// import { environment } from 'src/environments/environment';\n// import { AuthService } from 'src/app/core/services/auth.service';\n\n// import { SlickCarouselModule } from 'ngx-slick-carousel';\n\n// interface Story {\n//   _id: string;\n//   user: {\n//     _id: string;\n//     username: string;\n//     fullName: string;\n//     avatar: string;\n//   };\n//   mediaUrl: string;\n//   mediaType: 'image' | 'video';\n//   caption?: string;\n//   createdAt: string;\n//   expiresAt: string;\n//   views: number;\n//   isActive: boolean;\n//   products?: Array<{\n//     _id: string;\n//     name: string;\n//     price: number;\n//     image: string;\n//   }>;\n// }\n\n// @Component({\n//   selector: 'app-view-add-stories',\n//   standalone: true,\n//   imports: [CommonModule, FormsModule, SlickCarouselModule],\n//   templateUrl: './view-add-stories.component.html',\n//   styleUrls: ['./view-add-stories.component.scss']\n// })\n// export class ViewAddStoriesComponent implements OnInit, OnDestroy {\n//   @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n//   @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n//   currentUser: any = null;\n\n//   stories: Story[] = [];\n//   isLoadingStories = true;\n\n//   currentIndex = 0;\n//   isOpen = false;\n//   isRotating = false;\n//   isDragging = false;\n//   rotateY = 0;\n//   targetRotateY = 0;\n//   targetDirection: 'forward' | 'back' | null = null;\n//   dragStartX = 0;\n//   dragCurrentX = 0;\n//   minDragPercentToTransition = 0.5;\n//   minVelocityToTransition = 0.65;\n//   transitionSpeed = 6;\n//   private subscriptions: Subscription[] = [];\n\n//   // For slider arrows\n//   @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n//   canScrollStoriesLeft = false;\n//   canScrollStoriesRight = false;\n// storySliderConfig = {\n//   slidesToShow: 6,\n//   slidesToScroll: 2,\n//   infinite: false,\n//   arrows: false,\n//   dots: false,\n//   variableWidth: false,\n//   swipeToSlide: true,\n//   responsive: [\n//     { breakpoint: 900, settings: { slidesToShow: 4 } },\n//     { breakpoint: 600, settings: { slidesToShow: 3 } }\n//   ]\n// };\n//   constructor(\n//     private router: Router,\n//     private http: HttpClient,\n//     private authService: AuthService\n//   ) {}\n\n//   ngOnInit() {\n//     this.loadStories();\n//     this.setupEventListeners();\n//     this.authService.currentUser$.subscribe(user => {\n//       this.currentUser = user;\n//     });\n//   }\n\n//   ngOnDestroy() {\n//     this.subscriptions.forEach(sub => sub.unsubscribe());\n//     this.removeEventListeners();\n//   }\n\n//   // --- Slider Arrow Logic ---\n//   scrollStoriesLeft() {\n//     if (this.storiesSlider) {\n//       this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n//       setTimeout(() => this.updateStoriesArrows(), 300);\n//     }\n//   }\n//   scrollStoriesRight() {\n//     if (this.storiesSlider) {\n//       this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n//       setTimeout(() => this.updateStoriesArrows(), 300);\n//     }\n//   }\n//   updateStoriesArrows() {\n//     if (this.storiesSlider) {\n//       const el = this.storiesSlider.nativeElement;\n//       this.canScrollStoriesLeft = el.scrollLeft > 0;\n//       this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n//     }\n//   }\n//   ngAfterViewInit() {\n//     setTimeout(() => this.updateStoriesArrows(), 500);\n//   }\n\n//   // --- Story Logic (unchanged) ---\n//   loadStories() {\n//     this.isLoadingStories = true;\n//     this.subscriptions.push(\n//       this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n//         next: (response) => {\n//           if (response.success && response.storyGroups) {\n//             this.stories = response.storyGroups;\n//           } else {\n//             this.loadFallbackStories();\n//           }\n//           this.isLoadingStories = false;\n//         },\n//         error: (error) => {\n//           console.error('Error loading stories:', error);\n//           this.loadFallbackStories();\n//           this.isLoadingStories = false;\n//         }\n//       })\n//     );\n//   }\n\n//   loadFallbackStories() {\n//     this.stories = [\n//       // ... (same as before, omitted for brevity)\n//     ];\n//   }\n\n//   openStories(index: number = 0) {\n//     this.currentIndex = index;\n//     this.isOpen = true;\n//     this.showStory(index);\n//     document.body.style.overflow = 'hidden';\n//   }\n//   closeStories() {\n//     this.isOpen = false;\n//     this.pauseAllVideos();\n//     document.body.style.overflow = 'auto';\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.classList.add('is-closed');\n//     }\n//     setTimeout(() => {\n//       if (this.storiesContainer) {\n//         this.storiesContainer.nativeElement.classList.remove('is-closed');\n//       }\n//     }, 300);\n//   }\n//   showStory(index: number) {\n//     this.currentIndex = index;\n//     this.rotateY = 0;\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n//     }\n//   }\n//   nextStory() {\n//     if (this.currentIndex < this.stories.length - 1) {\n//       this.targetRotateY = -90;\n//       this.targetDirection = 'forward';\n//       this.isRotating = true;\n//       this.update();\n//     } else {\n//       this.closeStories();\n//     }\n//   }\n//   previousStory() {\n//     if (this.currentIndex > 0) {\n//       this.targetRotateY = 90;\n//       this.targetDirection = 'back';\n//       this.isRotating = true;\n//       this.update();\n//     } else {\n//       this.closeStories();\n//     }\n//   }\n//   @HostListener('document:keydown', ['$event'])\n//   handleKeydown(event: KeyboardEvent) {\n//     if (!this.isOpen) return;\n//     switch (event.key) {\n//       case 'ArrowLeft':\n//         this.previousStory();\n//         break;\n//       case 'ArrowRight':\n//         this.nextStory();\n//         break;\n//       case 'Escape':\n//         this.closeStories();\n//         break;\n//     }\n//   }\n//   onStoryClick(event: MouseEvent) {\n//     if (this.isRotating) return;\n//     const clickX = event.clientX;\n//     const windowWidth = window.innerWidth;\n//     if (clickX < windowWidth / 3) {\n//       this.previousStory();\n//     } else {\n//       this.nextStory();\n//     }\n//   }\n//   onTouchStart(event: TouchEvent) {\n//     this.isDragging = true;\n//     this.dragStartX = event.touches[0].clientX;\n//     this.dragCurrentX = this.dragStartX;\n//   }\n//   onTouchMove(event: TouchEvent) {\n//     if (!this.isDragging) return;\n//     this.dragCurrentX = event.touches[0].clientX;\n//     this.updateDragPosition();\n//   }\n//   onTouchEnd(event: TouchEvent) {\n//     if (!this.isDragging) return;\n//     this.isDragging = false;\n//     const dragDelta = this.dragCurrentX - this.dragStartX;\n//     const threshold = window.innerWidth * this.minDragPercentToTransition;\n//     if (Math.abs(dragDelta) > threshold) {\n//       if (dragDelta > 0) {\n//         this.previousStory();\n//       } else {\n//         this.nextStory();\n//       }\n//     } else {\n//       this.targetRotateY = 0;\n//       this.isRotating = true;\n//       this.update();\n//     }\n//   }\n//   private updateDragPosition() {\n//     const dragDelta = this.dragCurrentX - this.dragStartX;\n//     this.rotateY = (dragDelta / window.innerWidth) * 90;\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.style.transform = \n//         `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n//     }\n//   }\n//   private update() {\n//     if (!this.isRotating) return;\n//     this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n//     if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n//       this.rotateY = this.targetRotateY;\n//       this.isRotating = false;\n//       if (this.targetDirection) {\n//         const newIndex = this.targetDirection === 'forward' \n//           ? this.currentIndex + 1 \n//           : this.currentIndex - 1;\n//         this.showStory(newIndex);\n//         this.targetDirection = null;\n//       }\n//       return;\n//     }\n//     if (this.storiesContainer) {\n//       this.storiesContainer.nativeElement.style.transform = \n//         `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n//     }\n//     requestAnimationFrame(() => this.update());\n//   }\n//   private pauseAllVideos() {\n//     const videos = document.querySelectorAll('.story__video');\n//     videos.forEach((video: any) => {\n//       if (video.pause) {\n//         video.pause();\n//       }\n//     });\n//   }\n//   private setupEventListeners() {}\n//   private removeEventListeners() {}\n//   getCurrentStory(): Story {\n//     return this.stories[this.currentIndex];\n//   }\n//   getStoryProgress(): number {\n//     return ((this.currentIndex + 1) / this.stories.length) * 100;\n//   }\n//   getTimeAgo(dateString: string): string {\n//     const now = new Date();\n//     const date = new Date(dateString);\n//     const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n//     if (diffInMinutes < 1) return 'now';\n//     if (diffInMinutes < 60) return `${diffInMinutes}m`;\n//     const diffInHours = Math.floor(diffInMinutes / 60);\n//     if (diffInHours < 24) return `${diffInHours}h`;\n//     const diffInDays = Math.floor(diffInHours / 24);\n//     return `${diffInDays}d`;\n//   }\n//   formatNumber(num: number): string {\n//     if (num >= 1000000) {\n//       return (num / 1000000).toFixed(1) + 'M';\n//     } else if (num >= 1000) {\n//       return (num / 1000).toFixed(1) + 'K';\n//     }\n//     return num.toString();\n//   }\n//   formatPrice(price: number): string {\n//     return new Intl.NumberFormat('en-IN', {\n//       style: 'currency',\n//       currency: 'INR',\n//       minimumFractionDigits: 0\n//     }).format(price);\n//   }\n//   viewProduct(product: any) {\n//     this.router.navigate(['/product', product._id]);\n//   }\n//   hasProducts(): boolean {\n//     const story = this.getCurrentStory();\n//     return !!(story && story.products && story.products.length > 0);\n//   }\n//   getStoryProducts(): any[] {\n//     const story = this.getCurrentStory();\n//     return story?.products || [];\n//   }\n//   // Handler for Add Story button\n//   // Modal state\n//   showAddModal = false;\n//   showAddStoryModal = false;\n//   showAddReelModal = false;\n//   showPermissionModal = false;\n//   showCameraOrGallery = false;\n//   permissionDenied = false;\n//   // Reel recording state\n//   isRecording = false;\n//   recordedChunks: Blob[] = [];\n//   mediaRecorder: any = null;\n//   videoStream: MediaStream | null = null;\n//   reelPreviewUrl: string | null = null;\n//   isUploadingReel = false;\n//   newReelCaption = '';\n//   newStoryFile: File | null = null;\n//   newStoryCaption = '';\n//   isUploadingStory = false;\n//   onAdd() {\n//     this.showAddModal = true;\n//     this.showAddStoryModal = false;\n//     this.showAddReelModal = false;\n//     this.showPermissionModal = false;\n//     this.showCameraOrGallery = false;\n//     this.permissionDenied = false;\n//     this.newStoryFile = null;\n//     this.newStoryCaption = '';\n//     this.reelPreviewUrl = null;\n//     this.newReelCaption = '';\n//   }\n//   onAddStory() {\n//     this.showAddModal = false;\n//     this.showPermissionModal = true;\n//     this.permissionDenied = false;\n//     this.showAddStoryModal = false;\n//     this.showCameraOrGallery = false;\n//     this.newStoryFile = null;\n//     this.newStoryCaption = '';\n//   }\n//   onAddReel() {\n//     this.showAddModal = false;\n//     this.showAddReelModal = true;\n//     this.reelPreviewUrl = null;\n//     this.newReelCaption = '';\n//     setTimeout(() => this.startCameraForReel(), 100);\n//   }\n//   async startCameraForReel() {\n//     try {\n//       this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n//       const video: any = document.getElementById('reel-video');\n//       if (video) {\n//         video.srcObject = this.videoStream;\n//         video.play();\n//       }\n//     } catch (err) {\n//       alert('Could not access camera.');\n//       this.showAddReelModal = false;\n//     }\n//   }\n//   startRecording() {\n//     if (!this.videoStream) return;\n//     this.recordedChunks = [];\n//     this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n//     this.mediaRecorder.ondataavailable = (e: any) => {\n//       if (e.data.size > 0) this.recordedChunks.push(e.data);\n//     };\n//     this.mediaRecorder.onstop = () => {\n//       const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n//       this.reelPreviewUrl = URL.createObjectURL(blob);\n//     };\n//     this.mediaRecorder.start();\n//     this.isRecording = true;\n//   }\n//   stopRecording() {\n//     if (this.mediaRecorder && this.isRecording) {\n//       this.mediaRecorder.stop();\n//       this.isRecording = false;\n//     }\n//   }\n//   async submitNewReel() {\n//     if (!this.reelPreviewUrl) return;\n//     this.isUploadingReel = true;\n//     try {\n//       const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n//       const formData = new FormData();\n//       formData.append('media', blob, 'reel.webm');\n//       const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n//       const reelPayload = {\n//         media: {\n//           type: uploadRes.type,\n//           url: uploadRes.url\n//         },\n//         caption: this.newReelCaption,\n//         isReel: true\n//       };\n//       await this.http.post('/api/stories', reelPayload).toPromise();\n//       this.showAddReelModal = false;\n//       this.loadStories();\n//     } catch (err) {\n//       alert('Failed to upload reel.');\n//     } finally {\n//       this.isUploadingReel = false;\n//       this.cleanupReelStream();\n//     }\n//   }\n//   cleanupReelStream() {\n//     if (this.videoStream) {\n//       this.videoStream.getTracks().forEach(track => track.stop());\n//       this.videoStream = null;\n//     }\n//     this.mediaRecorder = null;\n//     this.isRecording = false;\n//     this.reelPreviewUrl = null;\n//   }\n//   closeAddReelModal() {\n//     this.showAddReelModal = false;\n//     this.cleanupReelStream();\n//   }\n//   handlePermissionResponse(allow: boolean) {\n//     this.showPermissionModal = false;\n//     if (allow) {\n//       this.showCameraOrGallery = true;\n//     } else {\n//       this.permissionDenied = true;\n//     }\n//   }\n//   openCamera() {\n//     this.showCameraOrGallery = false;\n//     this.showAddStoryModal = true;\n//     setTimeout(() => {\n//       const input: any = document.getElementById('story-file-input');\n//       if (input) {\n//         input.setAttribute('capture', 'environment');\n//         input.click();\n//       }\n//     }, 100);\n//   }\n//   openGallery() {\n//     this.showCameraOrGallery = false;\n//     this.showAddStoryModal = true;\n//     setTimeout(() => {\n//       const input: any = document.getElementById('story-file-input');\n//       if (input) {\n//         input.removeAttribute('capture');\n//         input.click();\n//       }\n//     }, 100);\n//   }\n//   onStoryFileSelected(event: any) {\n//     const file = event.target.files[0];\n//     if (file) {\n//       this.newStoryFile = file;\n//     }\n//   }\n//   async submitNewStory() {\n//     if (!this.newStoryFile) return;\n//     this.isUploadingStory = true;\n//     try {\n//       const uploadForm = new FormData();\n//       uploadForm.append('media', this.newStoryFile);\n//       const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n//       const storyPayload = {\n//         media: {\n//           type: uploadRes.type,\n//           url: uploadRes.url\n//         },\n//         caption: this.newStoryCaption\n//       };\n//       await this.http.post('/api/stories', storyPayload).toPromise();\n//       this.showAddStoryModal = false;\n//       this.loadStories();\n//     } catch (err) {\n//       alert('Failed to upload story.');\n//     } finally {\n//       this.isUploadingStory = false;\n//     }\n//   }\n//   closeAddStoryModal() {\n//     this.showAddStoryModal = false;\n//   }\n// }\nimport { Component, OnInit, OnDestroy, ElementRef, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription, interval } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\n\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  currentUser: any = null;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // Auto-advance timer\n  private storyInterval: any;\n  private storyDuration = 5000; // 5 seconds per story\n\n  // Slider (carousel) configuration\n  storySliderConfig = {\n    slidesToShow: 6,\n    slidesToScroll: 2,\n    infinite: false,\n    arrows: false,\n    dots: false,\n    swipeToSlide: true,\n    responsive: [\n      { breakpoint: 900, settings: { slidesToShow: 4 } },\n      { breakpoint: 600, settings: { slidesToShow: 3 } }\n    ]\n  };\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) { }\n\n  ngOnInit() {\n    // Load stories from backend\n    this.loadStories();\n    // Track current user (for \"Your Story\" avatar)\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n\n  /*** Story Loading ***/\n  loadStories() {\n    this.isLoadingStories = true;\n    const sub = this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n      next: (response) => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n        } else {\n          // Handle fallback or no data\n          this.stories = [];\n        }\n        this.isLoadingStories = false;\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        this.stories = [];\n        this.isLoadingStories = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n\n  /*** Story Viewer Open/Close ***/\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    this.startStoryTimer();       // start auto-advance\n    document.body.style.overflow = 'hidden';\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    this.clearStoryTimer();        // stop auto-advance\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  /*** Show a specific story and reset rotation state ***/\n  showStory(index: number) {\n    if (index < 0 || index >= this.stories.length) {\n      this.closeStories();\n      return;\n    }\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  /*** Automatic progression ***/\n  private startStoryTimer() {\n    this.clearStoryTimer();\n    this.storyInterval = setInterval(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n\n  private clearStoryTimer() {\n    if (this.storyInterval) {\n      clearInterval(this.storyInterval);\n      this.storyInterval = null;\n    }\n  }\n\n  /*** Navigate to next story ***/\n  nextStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  /*** Navigate to previous story ***/\n  previousStory() {\n    this.clearStoryTimer();\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.updateRotation();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  /*** Apply smooth 3D rotation between stories ***/\n  private updateRotation() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n        this.startStoryTimer(); // restart auto-advance after animation\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.updateRotation());\n  }\n\n  /*** Pause any playing videos when closing ***/\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach((video: any) => {\n      if (video.pause) { video.pause(); }\n    });\n  }\n\n  /*** Story Progress (percentage of stories seen) ***/\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n\n  /*** Utility: format time ago (e.g., \"5m\", \"2h\") ***/\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  /*** Handle click on story (left half = previous, right half = next) ***/\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  /*** Drag (swipe) support for touch devices ***/\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n    this.clearStoryTimer(); // pause auto while dragging\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.updateRotation();\n      this.startStoryTimer(); // resume auto-advance\n    }\n  }\n\n  /*** Keydown navigation (Esc to close, arrows to move) ***/\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    if (event.key === 'ArrowLeft') {\n      this.previousStory();\n    } else if (event.key === 'ArrowRight') {\n      this.nextStory();\n    } else if (event.key === 'Escape') {\n      this.closeStories();\n    }\n  }\n\n  /*** Utilities for Products and Formatting ***/\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n\n  /*** Handlers for Adding Stories/Reels (using FormData and Multer) ***/\n  // (The code below shows modals and camera logic; see Node backend for upload handling)\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      // Upload video to backend\n      const uploadRes: any = await this.http.post(`${environment.apiUrl}/stories/upload`, formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post(`${environment.apiUrl}/stories`, reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories(); // refresh list\n    } catch (err) {\n      alert('Failed to upload reel.');\n      console.error(err);\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post(`${environment.apiUrl}/stories/upload`, uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post(`${environment.apiUrl}/stories`, storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n      console.error(err);\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n}\n\n\n", "<div class=\"stories-container\" #storiesContainer>\n  <!-- Stories Header -->\n  <div class=\"stories-header\">\n    <h3 class=\"stories-title\">Stories</h3>\n    <button class=\"create-story-btn\" (click)=\"onAdd()\">\n      <i class=\"fas fa-plus\"></i>\n      <span>Create</span>\n    </button>\n  </div>\n\n  <!-- Stories Slider with Navigation -->\n  <div class=\"stories-slider-wrapper\">\n    <!-- Navigation Arrow Left -->\n    <button class=\"nav-arrow nav-arrow-left\"\n            (click)=\"scrollLeft()\"\n            [disabled]=\"!canScrollLeft\"\n            [class.hidden]=\"!showNavArrows\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n\n    <!-- Carousel for Stories (Thumbnails) -->\n    <ngx-slick-carousel class=\"stories-slider\" [config]=\"storySliderConfig\" #slickCarousel>\n      <!-- Add Story Button -->\n      <div ngxSlickItem class=\"story-item add-story-item\" (click)=\"onAdd()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar add-avatar\">\n            <div class=\"story-avatar-inner\">\n              <img\n                class=\"story-avatar-img\"\n                [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\"\n                alt=\"Your Story\"\n              />\n              <span class=\"add-story-plus\">+</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"story-username\">Your Story</div>\n      </div>\n\n      <!-- Existing Stories -->\n      <div ngxSlickItem class=\"story-item\"\n           *ngFor=\"let story of stories; let i = index\"\n           (click)=\"openStory(story, i)\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar\" [class.has-products]=\"story.products && story.products.length > 0\">\n            <div class=\"story-avatar-inner\">\n              <img\n                class=\"story-avatar-img\"\n                [src]=\"story.user.avatar\"\n                [alt]=\"story.user.username\"\n              />\n              <!-- Shopping bag indicator -->\n              <div class=\"shopping-bag-indicator\"\n                   *ngIf=\"story.products && story.products.length > 0\"\n                   title=\"Shoppable content\">\n                <i class=\"fas fa-shopping-bag\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"story-username\">{{ story.user.username }}</div>\n        <!-- Product count badge -->\n        <div class=\"product-count-badge\"\n             *ngIf=\"story.products && story.products.length > 0\">\n          {{ story.products.length }} item{{ story.products.length > 1 ? 's' : '' }}\n        </div>\n      </div>\n    </ngx-slick-carousel>\n\n    <!-- Navigation Arrow Right -->\n    <button class=\"nav-arrow nav-arrow-right\"\n            (click)=\"scrollRight()\"\n            [disabled]=\"!canScrollRight\"\n            [class.hidden]=\"!showNavArrows\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n\n<!-- Fullscreen Stories Viewer (shown when isOpen is true) -->\n<div class=\"stories-overlay\" *ngIf=\"isOpen\">\n  <div class=\"stories-content\" #feedCover>\n    <!-- Progress Bars -->\n    <div class=\"progress-container\">\n      <div class=\"progress-bar\"\n           *ngFor=\"let story of stories; let i = index\"\n           [class.active]=\"i === currentStoryIndex\"\n           [class.completed]=\"i < currentStoryIndex\">\n        <div class=\"progress-fill\"\n             [style.width.%]=\"getProgressWidth(i)\"></div>\n      </div>\n    </div>\n\n    <!-- Story Header -->\n    <div class=\"story-header\">\n      <img class=\"story-header-avatar\" [src]=\"getCurrentStory().user.avatar\" alt=\"Avatar\"/>\n      <div class=\"story-header-info\">\n        <span class=\"username\">{{ getCurrentStory().user.username }}</span>\n        <span class=\"time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</span>\n      </div>\n      <button (click)=\"closeStories()\" class=\"close-btn\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Story Media -->\n    <div class=\"story-media\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n\n      <!-- Image Story -->\n      <img *ngIf=\"getCurrentStory().mediaType === 'image'\"\n           [src]=\"getCurrentStory().mediaUrl\"\n           class=\"story-image\"/>\n\n      <!-- Video Story -->\n      <video *ngIf=\"getCurrentStory().mediaType === 'video'\"\n             class=\"story-video\"\n             [src]=\"getCurrentStory().mediaUrl\"\n             autoplay muted #storyVideo></video>\n\n      <!-- Product Tags -->\n      <div class=\"product-tags\" *ngIf=\"showProductTags && getCurrentStory().products\">\n        <div class=\"product-tag\"\n             *ngFor=\"let productTag of getCurrentStory().products\"\n             [style.left.%]=\"productTag.position?.x || 50\"\n             [style.top.%]=\"productTag.position?.y || 50\"\n             (click)=\"openProductDetails(productTag.product); $event.stopPropagation()\">\n          <div class=\"product-tag-dot\"></div>\n          <div class=\"product-tag-info\">\n            <img [src]=\"productTag.product.images[0]?.url\"\n                 [alt]=\"productTag.product.name\"\n                 class=\"product-tag-image\">\n            <div class=\"product-tag-details\">\n              <span class=\"product-tag-name\">{{ productTag.product.name }}</span>\n              <span class=\"product-tag-price\">${{ productTag.product.price }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Shopping Bag Button -->\n      <button class=\"shopping-bag-btn\"\n              *ngIf=\"getCurrentStory().products && getCurrentStory().products.length > 0\"\n              (click)=\"toggleProductTags(); $event.stopPropagation()\"\n              [class.active]=\"showProductTags\">\n        <i class=\"fas fa-shopping-bag\"></i>\n        <span class=\"product-count\">{{ getCurrentStory().products.length }}</span>\n      </button>\n    </div>\n\n    <!-- Story Actions -->\n    <div class=\"story-actions\">\n      <button class=\"action-btn like-btn\"\n              (click)=\"toggleLike()\"\n              [class.liked]=\"isLiked\">\n        <i class=\"fas fa-heart\"></i>\n      </button>\n      <button class=\"action-btn share-btn\" (click)=\"shareStory()\">\n        <i class=\"fas fa-share\"></i>\n      </button>\n      <button class=\"action-btn save-btn\" (click)=\"saveStory()\">\n        <i class=\"fas fa-bookmark\"></i>\n      </button>\n    </div>\n\n    <!-- Navigation Arrows -->\n    <button class=\"story-nav-btn story-nav-prev\"\n            (click)=\"previousStory()\"\n            *ngIf=\"currentStoryIndex > 0\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n    <button class=\"story-nav-btn story-nav-next\"\n            (click)=\"nextStory()\"\n            *ngIf=\"currentStoryIndex < stories.length - 1\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": ";AAkgBA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,mBAAmB,QAAQ,oBAAoB;;;;;;;;;;;ICtd1CC,EAAA,CAAAC,cAAA,cAE+B;IAC7BD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMZH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,QAAA,CAAAC,QAAA,CAAAC,MAAA,WAAAF,QAAA,CAAAC,QAAA,CAAAC,MAAA,qBACF;;;;;;IAzBFT,EAAA,CAAAC,cAAA,cAEmC;IAA9BD,EAAA,CAAAU,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,QAAA,GAAAK,MAAA,CAAAG,SAAA;MAAA,MAAAC,IAAA,GAAAJ,MAAA,CAAAK,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAd,QAAA,EAAAS,IAAA,CAAmB;IAAA,EAAC;IAG5BhB,EAFJ,CAAAC,cAAA,cAAoC,cAC2D,cAC3D;IAC9BD,EAAA,CAAAE,SAAA,cAIE;IAEFF,EAAA,CAAAsB,UAAA,IAAAC,6CAAA,kBAE+B;IAKrCvB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE3DH,EAAA,CAAAsB,UAAA,IAAAE,6CAAA,kBACyD;IAG3DxB,EAAA,CAAAG,YAAA,EAAM;;;;IAtBwBH,EAAA,CAAAK,SAAA,GAAkE;IAAlEL,EAAA,CAAAyB,WAAA,iBAAAlB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAkE;IAItFT,EAAA,CAAAK,SAAA,GAAyB;IACzBL,EADA,CAAA0B,UAAA,QAAAnB,QAAA,CAAAoB,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAyB,QAAAtB,QAAA,CAAAoB,IAAA,CAAAG,QAAA,CACE;IAIvB9B,EAAA,CAAAK,SAAA,EAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAnB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;IAOjCT,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA+B,iBAAA,CAAAxB,QAAA,CAAAoB,IAAA,CAAAG,QAAA,CAAyB;IAG/C9B,EAAA,CAAAK,SAAA,EAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAnB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;;;;;IAqBzDT,EAAA,CAAAC,cAAA,cAG+C;IAC7CD,EAAA,CAAAE,SAAA,cACiD;IACnDF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EADA,CAAAyB,WAAA,WAAAO,IAAA,KAAAd,MAAA,CAAAe,iBAAA,CAAwC,cAAAD,IAAA,GAAAd,MAAA,CAAAe,iBAAA,CACC;IAEvCjC,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAkC,WAAA,UAAAhB,MAAA,CAAAiB,gBAAA,CAAAH,IAAA,OAAqC;;;;;IAwB5ChC,EAAA,CAAAE,SAAA,cAE0B;;;;IADrBF,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAC,QAAA,EAAArC,EAAA,CAAA6B,aAAA,CAAkC;;;;;IAIvC7B,EAAA,CAAAE,SAAA,mBAG0C;;;;IADnCF,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAC,QAAA,EAAArC,EAAA,CAAA6B,aAAA,CAAkC;;;;;;IAKvC7B,EAAA,CAAAC,cAAA,cAIgF;IAA3ED,EAAA,CAAAU,UAAA,mBAAA4B,0EAAAC,MAAA;MAAA,MAAAC,cAAA,GAAAxC,EAAA,CAAAa,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAwB,kBAAA,CAAAF,cAAA,CAAAG,OAAA,CAAsC;MAAA,OAAA3C,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAC7E5C,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,cAE+B;IAE7BF,EADF,CAAAC,cAAA,cAAiC,eACA;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAGrEJ,EAHqE,CAAAG,YAAA,EAAO,EAClE,EACF,EACF;;;;IAZDH,EADA,CAAAkC,WAAA,UAAAM,cAAA,CAAAK,QAAA,kBAAAL,cAAA,CAAAK,QAAA,CAAAC,CAAA,aAA6C,SAAAN,cAAA,CAAAK,QAAA,kBAAAL,cAAA,CAAAK,QAAA,CAAAE,CAAA,aACD;IAIxC/C,EAAA,CAAAK,SAAA,GAAyC;IACzCL,EADA,CAAA0B,UAAA,QAAAc,cAAA,CAAAG,OAAA,CAAAK,MAAA,qBAAAR,cAAA,CAAAG,OAAA,CAAAK,MAAA,IAAAC,GAAA,EAAAjD,EAAA,CAAA6B,aAAA,CAAyC,QAAAW,cAAA,CAAAG,OAAA,CAAAO,IAAA,CACV;IAGHlD,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAA+B,iBAAA,CAAAS,cAAA,CAAAG,OAAA,CAAAO,IAAA,CAA6B;IAC5BlD,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAmD,kBAAA,MAAAX,cAAA,CAAAG,OAAA,CAAAS,KAAA,KAA+B;;;;;IAbvEpD,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAsB,UAAA,IAAA+B,oDAAA,kBAIgF;IAYlFrD,EAAA,CAAAG,YAAA,EAAM;;;;IAfwBH,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAA0B,UAAA,YAAAR,MAAA,CAAAkB,eAAA,GAAA5B,QAAA,CAA6B;;;;;;IAkB3DR,EAAA,CAAAC,cAAA,iBAGyC;IADjCD,EAAA,CAAAU,UAAA,mBAAA4C,0EAAAf,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA0C,IAAA;MAAA,MAAArC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAsC,iBAAA,EAAmB;MAAA,OAAAxD,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAE7D5C,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAuC;IACrEJ,EADqE,CAAAG,YAAA,EAAO,EACnE;;;;IAHDH,EAAA,CAAAyB,WAAA,WAAAP,MAAA,CAAAuC,eAAA,CAAgC;IAEVzD,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAkB,eAAA,GAAA5B,QAAA,CAAAC,MAAA,CAAuC;;;;;;IAoBvET,EAAA,CAAAC,cAAA,iBAEsC;IAD9BD,EAAA,CAAAU,UAAA,mBAAAgD,0EAAA;MAAA1D,EAAA,CAAAa,aAAA,CAAA8C,IAAA;MAAA,MAAAzC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA0C,aAAA,EAAe;IAAA,EAAC;IAE/B5D,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAEuD;IAD/CD,EAAA,CAAAU,UAAA,mBAAAmD,0EAAA;MAAA7D,EAAA,CAAAa,aAAA,CAAAiD,IAAA;MAAA,MAAA5C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA6C,SAAA,EAAW;IAAA,EAAC;IAE3B/D,EAAA,CAAAE,SAAA,YAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA/FTH,EAHJ,CAAAC,cAAA,cAA4C,iBACF,cAEN;IAC9BD,EAAA,CAAAsB,UAAA,IAAA0C,6CAAA,kBAG+C;IAIjDhE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,cAAqF;IAEnFF,EADF,CAAAC,cAAA,cAA+B,eACN;IAAAD,EAAA,CAAAI,MAAA,GAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,IAA6C;IAClEJ,EADkE,CAAAG,YAAA,EAAO,EACnE;IACNH,EAAA,CAAAC,cAAA,kBAAmD;IAA3CD,EAAA,CAAAU,UAAA,mBAAAuD,iEAAA;MAAAjE,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAiD,YAAA,EAAc;IAAA,EAAC;IAC9BnE,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAIqC;IAAhCD,EAHA,CAAAU,UAAA,mBAAA0D,8DAAA7B,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAmD,YAAA,CAAA9B,MAAA,CAAoB;IAAA,EAAC,wBAAA+B,mEAAA/B,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAChBF,MAAA,CAAAqD,YAAA,CAAAhC,MAAA,CAAoB;IAAA,EAAC,uBAAAiC,kEAAAjC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACtBF,MAAA,CAAAuD,WAAA,CAAAlC,MAAA,CAAmB;IAAA,EAAC,sBAAAmC,iEAAAnC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACrBF,MAAA,CAAAyD,UAAA,CAAApC,MAAA,CAAkB;IAAA,EAAC;IAkClCvC,EA/BA,CAAAsB,UAAA,KAAAsD,8CAAA,kBAE0B,KAAAC,gDAAA,oBAMQ,KAAAC,8CAAA,kBAG8C,KAAAC,iDAAA,qBAuBvC;IAI3C/E,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2B,kBAGO;IADxBD,EAAA,CAAAU,UAAA,mBAAAsE,iEAAA;MAAAhF,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA+D,UAAA,EAAY;IAAA,EAAC;IAE5BjF,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAvBD,EAAA,CAAAU,UAAA,mBAAAwE,iEAAA;MAAAlF,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAiE,UAAA,EAAY;IAAA,EAAC;IACzDnF,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA0E,iEAAA;MAAApF,EAAA,CAAAa,aAAA,CAAAqD,GAAA;MAAA,MAAAhD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAmE,SAAA,EAAW;IAAA,EAAC;IACvDrF,EAAA,CAAAE,SAAA,aAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAS,EACL;IAQNH,EALA,CAAAsB,UAAA,KAAAgE,iDAAA,qBAEsC,KAAAC,iDAAA,qBAKiB;IAI3DvF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA/FuBH,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAA0B,UAAA,YAAAR,MAAA,CAAAsE,OAAA,CAAY;IAUFxF,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAT,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAqC;IAE7C7B,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAkB,eAAA,GAAAT,IAAA,CAAAG,QAAA,CAAqC;IACzC9B,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAuE,UAAA,CAAAvE,MAAA,CAAAkB,eAAA,GAAAsD,SAAA,EAA6C;IAe5D1F,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAkB,eAAA,GAAAuD,SAAA,aAA6C;IAK3C3F,EAAA,CAAAK,SAAA,EAA6C;IAA7CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAkB,eAAA,GAAAuD,SAAA,aAA6C;IAM1B3F,EAAA,CAAAK,SAAA,EAAmD;IAAnDL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAuC,eAAA,IAAAvC,MAAA,CAAAkB,eAAA,GAAA5B,QAAA,CAAmD;IAqBrER,EAAA,CAAAK,SAAA,EAAyE;IAAzEL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAkB,eAAA,GAAA5B,QAAA,IAAAU,MAAA,CAAAkB,eAAA,GAAA5B,QAAA,CAAAC,MAAA,KAAyE;IAY1ET,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAyB,WAAA,UAAAP,MAAA,CAAA0E,OAAA,CAAuB;IAcxB5F,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAe,iBAAA,KAA2B;IAK3BjC,EAAA,CAAAK,SAAA,EAA4C;IAA5CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAe,iBAAA,GAAAf,MAAA,CAAAsE,OAAA,CAAA/E,MAAA,KAA4C;;;AD0XzD,OAAM,MAAOoF,uBAAuB;EAuClCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAvCrB,KAAAC,WAAW,GAAQ,IAAI;IAEvB,KAAAV,OAAO,GAAY,EAAE;IACrB,KAAAW,gBAAgB,GAAG,IAAI;IACvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAIlC,KAAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IAE9B;IACA,KAAAC,iBAAiB,GAAG;MAClBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CACV;QAAEC,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAER,YAAY,EAAE;QAAC;MAAE,CAAE,EAClD;QAAEO,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAER,YAAY,EAAE;QAAC;MAAE,CAAE;KAErD;IA+QD;IACA;IACA,KAAAS,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAG,KAAK;EA1RpB;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,WAAW,EAAE;IAClB;IACA,IAAI,CAAC5C,WAAW,CAAC6C,YAAY,CAACC,SAAS,CAACpH,IAAI,IAAG;MAC7C,IAAI,CAACuE,WAAW,GAAGvE,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAqH,WAAWA,CAAA;IACT,IAAI,CAAChC,aAAa,CAACiC,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACAP,WAAWA,CAAA;IACT,IAAI,CAAC1C,gBAAgB,GAAG,IAAI;IAC5B,MAAM+C,GAAG,GAAG,IAAI,CAAClD,IAAI,CAACqD,GAAG,CAAM,GAAGvJ,WAAW,CAACwJ,MAAM,UAAU,CAAC,CAACP,SAAS,CAAC;MACxEQ,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAAClE,OAAO,GAAGgE,QAAQ,CAACE,WAAW;SACpC,MAAM;UACL;UACA,IAAI,CAAClE,OAAO,GAAG,EAAE;;QAEnB,IAAI,CAACW,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACnE,OAAO,GAAG,EAAE;QACjB,IAAI,CAACW,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;IACF,IAAI,CAACa,aAAa,CAAC6C,IAAI,CAACX,GAAG,CAAC;EAC9B;EAEA;EACAY,WAAWA,CAAC7I,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACmF,YAAY,GAAGnF,KAAK;IACzB,IAAI,CAACoF,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC0D,SAAS,CAAC9I,KAAK,CAAC;IACrB,IAAI,CAAC+I,eAAe,EAAE,CAAC,CAAO;IAC9BC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEAjG,YAAYA,CAAA;IACV,IAAI,CAACkC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACgE,cAAc,EAAE;IACrB,IAAI,CAACjB,eAAe,EAAE,CAAC,CAAQ;IAC/Ba,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAZ,SAASA,CAAC9I,KAAa;IACrB,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACuE,OAAO,CAAC/E,MAAM,EAAE;MAC7C,IAAI,CAAC0D,YAAY,EAAE;MACnB;;IAEF,IAAI,CAACiC,YAAY,GAAGnF,KAAK;IACzB,IAAI,CAACuF,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAAC8D,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GAAG,mBAAmB;;EAE7E;EAEA;EACQZ,eAAeA,CAAA;IACrB,IAAI,CAACZ,eAAe,EAAE;IACtB,IAAI,CAACyB,aAAa,GAAGC,WAAW,CAAC,MAAK;MACpC,IAAI,CAAC/G,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAACkD,aAAa,CAAC;EACxB;EAEQmC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACyB,aAAa,EAAE;MACtBE,aAAa,CAAC,IAAI,CAACF,aAAa,CAAC;MACjC,IAAI,CAACA,aAAa,GAAG,IAAI;;EAE7B;EAEA;EACA9G,SAASA,CAAA;IACP,IAAI,CAACqF,eAAe,EAAE;IACtB,IAAI,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACZ,OAAO,CAAC/E,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACgG,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC0E,cAAc,EAAE;KACtB,MAAM;MACL,IAAI,CAAC7G,YAAY,EAAE;;EAEvB;EAEA;EACAP,aAAaA,CAAA;IACX,IAAI,CAACwF,eAAe,EAAE;IACtB,IAAI,IAAI,CAAChD,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC0E,cAAc,EAAE;KACtB,MAAM;MACL,IAAI,CAAC7G,YAAY,EAAE;;EAEvB;EAEA;EACQ6G,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC1E,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAIkE,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC1E,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAMyE,QAAQ,GAAG,IAAI,CAACzE,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACN,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAAC2D,SAAS,CAACoB,QAAQ,CAAC;QACxB,IAAI,CAACzE,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACsD,eAAe,EAAE,CAAC,CAAC;;MAE1B;;IAEF,IAAI,IAAI,CAACM,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAACpE,OAAO,MAAM;;IAEnD4E,qBAAqB,CAAC,MAAM,IAAI,CAACJ,cAAc,EAAE,CAAC;EACpD;EAEA;EACQX,cAAcA,CAAA;IACpB,MAAMgB,MAAM,GAAGpB,QAAQ,CAACqB,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACpC,OAAO,CAAEsC,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QAAED,KAAK,CAACC,KAAK,EAAE;;IAClC,CAAC,CAAC;EACJ;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACrF,YAAY,GAAG,CAAC,IAAI,IAAI,CAACZ,OAAO,CAAC/E,MAAM,GAAI,GAAG;EAC9D;EAEA;EACAgF,UAAUA,CAACiG,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGb,IAAI,CAACc,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGhB,IAAI,CAACc,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGjB,IAAI,CAACc,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEA;EACA7H,YAAYA,CAAC8H,KAAiB;IAC5B,IAAI,IAAI,CAAC7F,UAAU,EAAE;IACrB,MAAM8F,MAAM,GAAGD,KAAK,CAACE,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAAC1I,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACG,SAAS,EAAE;;EAEpB;EAEA;EACAQ,YAAYA,CAAC4H,KAAiB;IAC5B,IAAI,CAAC5F,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGwF,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC1C,IAAI,CAACzF,YAAY,GAAG,IAAI,CAACD,UAAU;IACnC,IAAI,CAACyC,eAAe,EAAE,CAAC,CAAC;EAC1B;EAEA3E,WAAWA,CAAC0H,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC5F,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGuF,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC5C,MAAMK,SAAS,GAAG,IAAI,CAAC9F,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAIkG,SAAS,GAAGH,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAAClC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACJ,KAAK,CAACS,SAAS,GACjD,6BAA6B,IAAI,CAACpE,OAAO,MAAM;;EAErD;EAEA7B,UAAUA,CAACwH,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAC5F,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAMmG,SAAS,GAAG,IAAI,CAAC9F,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAMgG,SAAS,GAAGJ,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC3F,0BAA0B;IACrE,IAAIoE,IAAI,CAACC,GAAG,CAACwB,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAAC9I,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACG,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAAC0C,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC0E,cAAc,EAAE;MACrB,IAAI,CAAChB,eAAe,EAAE,CAAC,CAAC;;EAE5B;EAEA;EAEA4C,aAAaA,CAACT,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAC9F,MAAM,EAAE;IAClB,IAAI8F,KAAK,CAACU,GAAG,KAAK,WAAW,EAAE;MAC7B,IAAI,CAACjJ,aAAa,EAAE;KACrB,MAAM,IAAIuI,KAAK,CAACU,GAAG,KAAK,YAAY,EAAE;MACrC,IAAI,CAAC9I,SAAS,EAAE;KACjB,MAAM,IAAIoI,KAAK,CAACU,GAAG,KAAK,QAAQ,EAAE;MACjC,IAAI,CAAC1I,YAAY,EAAE;;EAEvB;EAEA;EACA2I,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAC,WAAWA,CAAC9J,KAAa;IACvB,OAAO,IAAI+J,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCjD,KAAK,EAAE,UAAU;MACjBkD,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACnK,KAAK,CAAC;EAClB;EAEAoK,WAAWA,CAAC7K,OAAY;IACtB,IAAI,CAACoD,MAAM,CAAC0H,QAAQ,CAAC,CAAC,UAAU,EAAE9K,OAAO,CAAC+K,GAAG,CAAC,CAAC;EACjD;EAEAC,WAAWA,CAAA;IACT,MAAMC,KAAK,GAAG,IAAI,CAACxL,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEwL,KAAK,IAAIA,KAAK,CAACpN,QAAQ,IAAIoN,KAAK,CAACpN,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;EACjE;EAEAoN,gBAAgBA,CAAA;IACd,MAAMD,KAAK,GAAG,IAAI,CAACxL,eAAe,EAAE;IACpC,OAAOwL,KAAK,EAAEpN,QAAQ,IAAI,EAAE;EAC9B;EAEA4B,eAAeA,CAAA;IACb,OAAO,IAAI,CAACoD,OAAO,CAAC,IAAI,CAACY,YAAY,CAAC;EACxC;EAqBA0H,KAAKA,CAAA;IACH,IAAI,CAAClG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EACAuF,UAAUA,CAAA;IACR,IAAI,CAACnG,YAAY,GAAG,KAAK;IACzB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EACAsF,SAASA,CAAA;IACP,IAAI,CAACpG,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBkC,UAAU,CAAC,MAAM,IAAI,CAACuD,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAAC7F,WAAW,SAAS+F,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAE/C,KAAK,EAAE,IAAI;UAAEgD,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAMhD,KAAK,GAAQtB,QAAQ,CAACuE,cAAc,CAAC,YAAY,CAAC;QACxD,IAAIjD,KAAK,EAAE;UACTA,KAAK,CAACkD,SAAS,GAAGP,KAAI,CAAC7F,WAAW;UAClCkD,KAAK,CAACmD,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAACpG,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACA+G,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACxG,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKmE,MAAc,CAACuC,aAAa,CAAC,IAAI,CAACzG,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAAC2G,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC/G,cAAc,CAAC0B,IAAI,CAACmF,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAAC7G,aAAa,CAAC+G,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAClH,cAAc,EAAE;QAAEmH,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAAChH,cAAc,GAAGiH,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAAChH,aAAa,CAACqH,KAAK,EAAE;IAC1B,IAAI,CAACvH,WAAW,GAAG,IAAI;EACzB;EACAwH,aAAaA,CAAA;IACX,IAAI,IAAI,CAACtH,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAACuH,IAAI,EAAE;MACzB,IAAI,CAACzH,WAAW,GAAG,KAAK;;EAE5B;EACM0H,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI,CAAC0B,MAAI,CAACvH,cAAc,EAAE;MAC1BuH,MAAI,CAACtH,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAM6G,IAAI,GAAG,IAAIC,IAAI,CAACQ,MAAI,CAAC1H,cAAc,EAAE;UAAEmH,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEZ,IAAI,EAAE,WAAW,CAAC;QAC3C;QACA,MAAMa,SAAS,SAAcJ,MAAI,CAAC7J,IAAI,CAACkK,IAAI,CAAC,GAAGpQ,WAAW,CAACwJ,MAAM,iBAAiB,EAAEwG,QAAQ,CAAC,CAACK,SAAS,EAAE;QACzG,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBrM,GAAG,EAAEgN,SAAS,CAAChN;WAChB;UACDqN,OAAO,EAAET,MAAI,CAACrH,cAAc;UAC5B+H,MAAM,EAAE;SACT;QACD,MAAMV,MAAI,CAAC7J,IAAI,CAACkK,IAAI,CAAC,GAAGpQ,WAAW,CAACwJ,MAAM,UAAU,EAAE8G,WAAW,CAAC,CAACD,SAAS,EAAE;QAC9EN,MAAI,CAAC/H,gBAAgB,GAAG,KAAK;QAC7B+H,MAAI,CAAChH,WAAW,EAAE,CAAC,CAAC;OACrB,CAAC,OAAO8F,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;QAC/BhF,OAAO,CAACD,KAAK,CAACgF,GAAG,CAAC;OACnB,SAAS;QACRkB,MAAI,CAACtH,eAAe,GAAG,KAAK;QAC5BsH,MAAI,CAACW,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACnI,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACoI,SAAS,EAAE,CAACxH,OAAO,CAACyH,KAAK,IAAIA,KAAK,CAACf,IAAI,EAAE,CAAC;MAC3D,IAAI,CAACtH,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EACAqI,iBAAiBA,CAAA;IACf,IAAI,CAAC7I,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC0I,iBAAiB,EAAE;EAC1B;EACAI,wBAAwBA,CAACC,KAAc;IACrC,IAAI,CAAC9I,mBAAmB,GAAG,KAAK;IAChC,IAAI8I,KAAK,EAAE;MACT,IAAI,CAAC7I,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEhC;EACA6I,UAAUA,CAAA;IACR,IAAI,CAAC9I,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B6C,UAAU,CAAC,MAAK;MACd,MAAMqG,KAAK,GAAQ9G,QAAQ,CAACuE,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIuC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAC,WAAWA,CAAA;IACT,IAAI,CAAClJ,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B6C,UAAU,CAAC,MAAK;MACd,MAAMqG,KAAK,GAAQ9G,QAAQ,CAACuE,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIuC,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,mBAAmBA,CAACjF,KAAU;IAC5B,MAAMkF,IAAI,GAAGlF,KAAK,CAACmF,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAAC5I,YAAY,GAAG4I,IAAI;;EAE5B;EACMG,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtD,iBAAA;MAClB,IAAI,CAACsD,MAAI,CAAChJ,YAAY,EAAE;MACxBgJ,MAAI,CAAC9I,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAM+I,UAAU,GAAG,IAAI3B,QAAQ,EAAE;QACjC2B,UAAU,CAAC1B,MAAM,CAAC,OAAO,EAAEyB,MAAI,CAAChJ,YAAY,CAAC;QAC7C,MAAMwH,SAAS,SAAcwB,MAAI,CAACzL,IAAI,CAACkK,IAAI,CAAC,GAAGpQ,WAAW,CAACwJ,MAAM,iBAAiB,EAAEoI,UAAU,CAAC,CAACvB,SAAS,EAAE;QAC3G,MAAMwB,YAAY,GAAG;UACnBtB,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBrM,GAAG,EAAEgN,SAAS,CAAChN;WAChB;UACDqN,OAAO,EAAEmB,MAAI,CAAC/I;SACf;QACD,MAAM+I,MAAI,CAACzL,IAAI,CAACkK,IAAI,CAAC,GAAGpQ,WAAW,CAACwJ,MAAM,UAAU,EAAEqI,YAAY,CAAC,CAACxB,SAAS,EAAE;QAC/EsB,MAAI,CAAC5J,iBAAiB,GAAG,KAAK;QAC9B4J,MAAI,CAAC5I,WAAW,EAAE;OACnB,CAAC,OAAO8F,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;QAChChF,OAAO,CAACD,KAAK,CAACgF,GAAG,CAAC;OACnB,SAAS;QACR8C,MAAI,CAAC9I,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAiJ,kBAAkBA,CAAA;IAChB,IAAI,CAAC/J,iBAAiB,GAAG,KAAK;EAChC;;;uBA3eWhC,uBAAuB,EAAA7F,EAAA,CAAA6R,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA/R,EAAA,CAAA6R,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAjS,EAAA,CAAA6R,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBtM,uBAAuB;MAAAuM,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAvBvS,EAAA,CAAAU,UAAA,qBAAA+R,mDAAAlQ,MAAA;YAAA,OAAAiQ,GAAA,CAAA5F,aAAA,CAAArK,MAAA,CAAqB;UAAA,UAAAvC,EAAA,CAAA0S,iBAAA,CAAE;;;;;;;;;;;UCviBhC1S,EAHJ,CAAAC,cAAA,gBAAiD,aAEnB,YACA;UAAAD,EAAA,CAAAI,MAAA,cAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,gBAAmD;UAAlBD,EAAA,CAAAU,UAAA,mBAAAiS,yDAAA;YAAA3S,EAAA,CAAAa,aAAA,CAAA+R,GAAA;YAAA,OAAA5S,EAAA,CAAAoB,WAAA,CAASoR,GAAA,CAAA1E,KAAA,EAAO;UAAA,EAAC;UAChD9N,EAAA,CAAAE,SAAA,WAA2B;UAC3BF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAI,MAAA,aAAM;UAEhBJ,EAFgB,CAAAG,YAAA,EAAO,EACZ,EACL;UAKJH,EAFF,CAAAC,cAAA,aAAoC,kBAKM;UAFhCD,EAAA,CAAAU,UAAA,mBAAAmS,0DAAA;YAAA7S,EAAA,CAAAa,aAAA,CAAA+R,GAAA;YAAA,OAAA5S,EAAA,CAAAoB,WAAA,CAASoR,GAAA,CAAAM,UAAA,EAAY;UAAA,EAAC;UAG5B9S,EAAA,CAAAE,SAAA,aAAmC;UACrCF,EAAA,CAAAG,YAAA,EAAS;UAKPH,EAFF,CAAAC,cAAA,iCAAuF,eAEf;UAAlBD,EAAA,CAAAU,UAAA,mBAAAqS,uDAAA;YAAA/S,EAAA,CAAAa,aAAA,CAAA+R,GAAA;YAAA,OAAA5S,EAAA,CAAAoB,WAAA,CAASoR,GAAA,CAAA1E,KAAA,EAAO;UAAA,EAAC;UAG/D9N,EAFJ,CAAAC,cAAA,eAAoC,eACG,eACH;UAC9BD,EAAA,CAAAE,SAAA,eAIE;UACFF,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAGpCJ,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UACxCJ,EADwC,CAAAG,YAAA,EAAM,EACxC;UAGNH,EAAA,CAAAsB,UAAA,KAAA0R,uCAAA,kBAEmC;UAyBrChT,EAAA,CAAAG,YAAA,EAAqB;UAGrBH,EAAA,CAAAC,cAAA,kBAGwC;UAFhCD,EAAA,CAAAU,UAAA,mBAAAuS,0DAAA;YAAAjT,EAAA,CAAAa,aAAA,CAAA+R,GAAA;YAAA,OAAA5S,EAAA,CAAAoB,WAAA,CAASoR,GAAA,CAAAU,WAAA,EAAa;UAAA,EAAC;UAG7BlT,EAAA,CAAAE,SAAA,aAAoC;UAG1CF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGNH,EAAA,CAAAsB,UAAA,KAAA6R,uCAAA,oBAA4C;;;UAhEhCnT,EAAA,CAAAK,SAAA,IAA+B;UAA/BL,EAAA,CAAAyB,WAAA,YAAA+Q,GAAA,CAAAY,aAAA,CAA+B;UAD/BpT,EAAA,CAAA0B,UAAA,cAAA8Q,GAAA,CAAAa,aAAA,CAA2B;UAMQrT,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAA0B,UAAA,WAAA8Q,GAAA,CAAAtL,iBAAA,CAA4B;UAQ3DlH,EAAA,CAAAK,SAAA,GAA0D;UAA1DL,EAAA,CAAA0B,UAAA,SAAA8Q,GAAA,CAAAtM,WAAA,kBAAAsM,GAAA,CAAAtM,WAAA,CAAAtE,MAAA,kCAAA5B,EAAA,CAAA6B,aAAA,CAA0D;UAY7C7B,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAA0B,UAAA,YAAA8Q,GAAA,CAAAhN,OAAA,CAAY;UAgC7BxF,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAAyB,WAAA,YAAA+Q,GAAA,CAAAY,aAAA,CAA+B;UAD/BpT,EAAA,CAAA0B,UAAA,cAAA8Q,GAAA,CAAAc,cAAA,CAA4B;UAQVtT,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAA0B,UAAA,SAAA8Q,GAAA,CAAAnM,MAAA,CAAY;;;qBDsd9BzG,YAAY,EAAA2T,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE5T,WAAW,EAAEE,mBAAmB,EAAA2T,EAAA,CAAAC,sBAAA,EAAAD,EAAA,CAAAE,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}