{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AdvancedSearchService = /*#__PURE__*/(() => {\n  class AdvancedSearchService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = `${environment.apiUrl}/search`;\n      // Search state management\n      this.searchHistorySubject = new BehaviorSubject([]);\n      this.recentSearchesSubject = new BehaviorSubject([]);\n      this.trendingSearchesSubject = new BehaviorSubject([]);\n      this.searchHistory$ = this.searchHistorySubject.asObservable();\n      this.recentSearches$ = this.recentSearchesSubject.asObservable();\n      this.trendingSearches$ = this.trendingSearchesSubject.asObservable();\n      // Query patterns for intelligent detection\n      this.queryPatterns = {\n        ambiguous: [/^kurts?$/i, /^shirts?$/i, /^pants?$/i, /^dresses?$/i, /^shoes?$/i, /^bags?$/i, /^watches?$/i, /^jewelry$/i],\n        specific: [/kurtas?\\s+for\\s+(men|boys|male)/i, /kurtas?\\s+for\\s+(women|girls|female)/i, /(men|boys|male)\\s+kurtas?/i, /(women|girls|female)\\s+kurtas?/i, /(men|boys|male)\\s+shirts?/i, /(women|girls|female)\\s+dresses?/i],\n        genderSpecific: [/(men|boys|male|gents)/i, /(women|girls|female|ladies)/i]\n      };\n      this.loadSearchHistory();\n      this.loadTrendingSearches();\n    }\n    /**\n     * Main search method with intelligent query processing\n     */\n    search(searchQuery) {\n      const startTime = Date.now();\n      // Analyze query type\n      const queryAnalysis = this.analyzeQuery(searchQuery.query);\n      // Handle ambiguous queries that need category selection\n      if (queryAnalysis.isAmbiguous && !searchQuery.category) {\n        return this.handleAmbiguousQuery(searchQuery.query, startTime);\n      }\n      // Process specific queries\n      if (queryAnalysis.isSpecific) {\n        searchQuery = this.enhanceSpecificQuery(searchQuery, queryAnalysis);\n      }\n      // Perform the actual search\n      return this.performSearch(searchQuery, startTime);\n    }\n    /**\n     * Get search suggestions as user types\n     */\n    getSuggestions(query) {\n      if (!query || query.length < 2) {\n        return of([]);\n      }\n      const params = new HttpParams().set('q', query).set('type', 'suggestions');\n      return this.http.get(`${this.API_URL}/suggestions`, {\n        params\n      }).pipe(map(response => response.suggestions || []), catchError(() => of(this.getFallbackSuggestions(query))));\n    }\n    /**\n     * Analyze query to determine type and intent\n     */\n    analyzeQuery(query) {\n      const normalizedQuery = query.toLowerCase().trim();\n      const analysis = {\n        isAmbiguous: false,\n        isSpecific: false,\n        detectedGender: null,\n        detectedCategory: null,\n        detectedSubcategory: null,\n        confidence: 0\n      };\n      // Check for ambiguous patterns\n      for (const pattern of this.queryPatterns.ambiguous) {\n        if (pattern.test(normalizedQuery)) {\n          analysis.isAmbiguous = true;\n          analysis.detectedCategory = this.extractCategoryFromPattern(normalizedQuery);\n          break;\n        }\n      }\n      // Check for specific patterns\n      for (const pattern of this.queryPatterns.specific) {\n        const match = pattern.exec(normalizedQuery);\n        if (match) {\n          analysis.isSpecific = true;\n          analysis.detectedGender = this.extractGender(match[1] || match[2]);\n          analysis.detectedCategory = this.extractCategoryFromQuery(normalizedQuery);\n          analysis.confidence = 0.9;\n          break;\n        }\n      }\n      // Check for gender-specific terms\n      for (const pattern of this.queryPatterns.genderSpecific) {\n        const match = pattern.exec(normalizedQuery);\n        if (match) {\n          analysis.detectedGender = this.extractGender(match[1]);\n          break;\n        }\n      }\n      return analysis;\n    }\n    /**\n     * Handle ambiguous queries by providing category options\n     */\n    handleAmbiguousQuery(query, startTime) {\n      const categories = this.getCategoryOptionsForQuery(query);\n      return of({\n        products: [],\n        totalCount: 0,\n        suggestions: [],\n        categoryRedirect: {\n          shouldRedirect: true,\n          categories: categories,\n          originalQuery: query\n        },\n        searchMetadata: {\n          queryType: 'ambiguous',\n          processingTime: Date.now() - startTime,\n          appliedFilters: {}\n        }\n      });\n    }\n    /**\n     * Enhance specific queries with detected parameters\n     */\n    enhanceSpecificQuery(searchQuery, analysis) {\n      const enhanced = {\n        ...searchQuery\n      };\n      if (analysis.detectedGender) {\n        enhanced.targetGender = analysis.detectedGender;\n        enhanced.category = analysis.detectedGender === 'men' ? 'men' : 'women';\n      }\n      if (analysis.detectedCategory) {\n        enhanced.subcategory = analysis.detectedCategory;\n      }\n      return enhanced;\n    }\n    /**\n     * Perform the actual search API call\n     */\n    performSearch(searchQuery, startTime) {\n      let params = new HttpParams();\n      // Add query parameters\n      if (searchQuery.query) params = params.set('q', searchQuery.query);\n      if (searchQuery.category) params = params.set('category', searchQuery.category);\n      if (searchQuery.subcategory) params = params.set('subcategory', searchQuery.subcategory);\n      if (searchQuery.targetGender) params = params.set('targetGender', searchQuery.targetGender);\n      if (searchQuery.brand) params = params.set('brand', searchQuery.brand);\n      if (searchQuery.sortBy) params = params.set('sortBy', searchQuery.sortBy);\n      // Add price range\n      if (searchQuery.priceRange) {\n        params = params.set('minPrice', searchQuery.priceRange.min.toString());\n        params = params.set('maxPrice', searchQuery.priceRange.max.toString());\n      }\n      // Add filters\n      if (searchQuery.filters) {\n        Object.keys(searchQuery.filters).forEach(key => {\n          const values = searchQuery.filters[key];\n          if (values && values.length > 0) {\n            params = params.set(key, values.join(','));\n          }\n        });\n      }\n      return this.http.get(`${this.API_URL}/products`, {\n        params\n      }).pipe(map(response => ({\n        products: response.products || [],\n        totalCount: response.totalCount || 0,\n        suggestions: response.suggestions || [],\n        searchMetadata: {\n          queryType: 'specific',\n          processingTime: Date.now() - startTime,\n          appliedFilters: searchQuery.filters || {}\n        }\n      })), catchError(() => of({\n        products: [],\n        totalCount: 0,\n        suggestions: [],\n        searchMetadata: {\n          queryType: 'specific',\n          processingTime: Date.now() - startTime,\n          appliedFilters: {}\n        }\n      })));\n    }\n    /**\n     * Get category options for ambiguous queries\n     */\n    getCategoryOptionsForQuery(query) {\n      const normalizedQuery = query.toLowerCase();\n      if (normalizedQuery.includes('kurt')) {\n        return [{\n          id: 'men-kurtas',\n          name: 'Men\\'s Kurtas',\n          description: 'Traditional and modern kurtas for men',\n          productCount: 45,\n          targetGender: 'men',\n          image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300'\n        }, {\n          id: 'women-kurtis',\n          name: 'Women\\'s Kurtis',\n          description: 'Stylish kurtis and kurtas for women',\n          productCount: 67,\n          targetGender: 'women',\n          image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300'\n        }];\n      }\n      // Add more category mappings as needed\n      return [];\n    }\n    /**\n     * Extract gender from query text\n     */\n    extractGender(genderText) {\n      const normalized = genderText.toLowerCase();\n      if (['men', 'boys', 'male', 'gents'].includes(normalized)) {\n        return 'men';\n      }\n      if (['women', 'girls', 'female', 'ladies'].includes(normalized)) {\n        return 'women';\n      }\n      return 'unisex';\n    }\n    /**\n     * Extract category from query pattern\n     */\n    extractCategoryFromPattern(query) {\n      if (query.includes('kurt')) return 'kurtas';\n      if (query.includes('shirt')) return 'shirts';\n      if (query.includes('pant')) return 'pants';\n      if (query.includes('dress')) return 'dresses';\n      return '';\n    }\n    /**\n     * Extract category from full query\n     */\n    extractCategoryFromQuery(query) {\n      if (query.includes('kurta')) return 'kurtas';\n      if (query.includes('kurti')) return 'kurtis';\n      if (query.includes('shirt')) return 'shirts';\n      if (query.includes('pant') || query.includes('trouser')) return 'pants';\n      if (query.includes('dress')) return 'dresses';\n      return '';\n    }\n    /**\n     * Get fallback suggestions when API fails\n     */\n    getFallbackSuggestions(query) {\n      const suggestions = [];\n      if (query.toLowerCase().includes('kurt')) {\n        suggestions.push({\n          text: 'kurtas for men',\n          type: 'keyword',\n          count: 45\n        }, {\n          text: 'kurtas for women',\n          type: 'keyword',\n          count: 67\n        }, {\n          text: 'cotton kurtas',\n          type: 'keyword',\n          count: 32\n        }, {\n          text: 'silk kurtas',\n          type: 'keyword',\n          count: 18\n        });\n      }\n      return suggestions;\n    }\n    /**\n     * Save search to history\n     */\n    saveSearchToHistory(query) {\n      const currentHistory = this.searchHistorySubject.value;\n      const updatedHistory = [query, ...currentHistory.filter(q => q !== query)].slice(0, 10);\n      this.searchHistorySubject.next(updatedHistory);\n      localStorage.setItem('dfashion_search_history', JSON.stringify(updatedHistory));\n    }\n    /**\n     * Load search history from storage\n     */\n    loadSearchHistory() {\n      const stored = localStorage.getItem('dfashion_search_history');\n      if (stored) {\n        try {\n          const history = JSON.parse(stored);\n          this.searchHistorySubject.next(history);\n        } catch (e) {\n          console.warn('Failed to load search history');\n        }\n      }\n    }\n    /**\n     * Load trending searches\n     */\n    loadTrendingSearches() {\n      // This would typically come from an API\n      const trending = ['kurtas for men', 'women ethnic wear', 'cotton shirts', 'formal dresses', 'casual wear'];\n      this.trendingSearchesSubject.next(trending);\n    }\n    /**\n     * Clear search history\n     */\n    clearSearchHistory() {\n      this.searchHistorySubject.next([]);\n      localStorage.removeItem('dfashion_search_history');\n    }\n    static {\n      this.ɵfac = function AdvancedSearchService_Factory(t) {\n        return new (t || AdvancedSearchService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AdvancedSearchService,\n        factory: AdvancedSearchService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AdvancedSearchService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}