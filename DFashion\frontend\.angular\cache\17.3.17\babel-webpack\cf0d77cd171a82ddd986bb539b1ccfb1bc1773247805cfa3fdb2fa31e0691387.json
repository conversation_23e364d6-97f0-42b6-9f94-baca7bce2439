{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nfunction NewArrivalsComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_button_10_Template_button_click_0_listener() {\n      const filter_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveTimeFilter(filter_r2.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeTimeFilter === filter_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filter_r2.label, \" \");\n  }\n}\nfunction NewArrivalsComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"div\", 20);\n    i0.ɵɵelementStart(2, \"div\", 21);\n    i0.ɵɵelement(3, \"div\", 22)(4, \"div\", 23)(5, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_15_div_2_Template, 6, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r5.pricing.discountPercentage, \"% OFF \");\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_div_22_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r5.rating.average);\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_16_div_1_div_22_i_2_Template, 1, 2, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStarArray(product_r5.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r5.pricing.mrp, \"1.0-0\"), \" \");\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \" In Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \" Limited Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" Out of Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.enter\", function NewArrivalsComponent_div_16_div_1_Template_div_keydown_enter_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.space\", function NewArrivalsComponent_div_16_div_1_Template_div_keydown_space_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"img\", 27);\n    i0.ɵɵlistener(\"error\", function NewArrivalsComponent_div_16_div_1_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵelement(4, \"i\", 4);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"NEW\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, NewArrivalsComponent_div_16_div_1_div_7_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementStart(8, \"div\", 30);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 31)(11, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_button_click_11_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.toggleWishlist(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(12, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_button_click_13_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.quickView(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(14, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_button_click_15_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.addToCart(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(16, \"i\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 37)(18, \"div\", 38);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"h3\", 39);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NewArrivalsComponent_div_16_div_1_div_22_Template, 5, 2, \"div\", 40);\n    i0.ɵɵelementStart(23, \"div\", 41)(24, \"span\", 42);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, NewArrivalsComponent_div_16_div_1_span_27_Template, 3, 4, \"span\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 44);\n    i0.ɵɵtemplate(29, NewArrivalsComponent_div_16_div_1_span_29_Template, 3, 0, \"span\", 45)(30, NewArrivalsComponent_div_16_div_1_span_30_Template, 3, 0, \"span\", 45)(31, NewArrivalsComponent_div_16_div_1_span_31_Template, 3, 0, \"span\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r5), i0.ɵɵsanitizeUrl)(\"alt\", product_r5.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getArrivalDateText(product_r5.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add to wishlist\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"wishlisted\", ctx_r2.isInWishlist(product_r5._id));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Quick view\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", product_r5.availability.status !== \"in-stock\");\n    i0.ɵɵattribute(\"aria-label\", \"Add to cart\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.rating.count > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(26, 21, product_r5.pricing.sellingPrice, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(product_r5.availability.status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"in-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"low-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"out-of-stock\");\n  }\n}\nfunction NewArrivalsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, NewArrivalsComponent_div_16_div_1_Template, 32, 24, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.products)(\"ngForTrackBy\", ctx_r2.trackByProductId);\n  }\n}\nfunction NewArrivalsComponent_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 60);\n  }\n}\nfunction NewArrivalsComponent_div_17_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 61);\n  }\n}\nfunction NewArrivalsComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadMoreProducts());\n    });\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_17_i_2_Template, 1, 0, \"i\", 58)(3, NewArrivalsComponent_div_17_i_3_Template, 1, 0, \"i\", 59);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoadingMore);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingMore);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingMore);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.isLoadingMore ? \"Loading...\" : \"Load More\");\n  }\n}\nfunction NewArrivalsComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Check back soon for the latest fashion!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_18_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadProducts());\n    });\n    i0.ɵɵelement(8, \"i\", 66);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let NewArrivalsComponent = /*#__PURE__*/(() => {\n  class NewArrivalsComponent {\n    constructor(shopDataService, router) {\n      this.shopDataService = shopDataService;\n      this.router = router;\n      this.maxProducts = 8;\n      this.showHeader = true;\n      this.showLoadMore = true;\n      this.products = [];\n      this.isLoading = true;\n      this.isLoadingMore = false;\n      this.hasMoreProducts = true;\n      this.activeTimeFilter = 'all';\n      this.wishlistItems = new Set();\n      this.currentPage = 1;\n      this.timeFilters = [{\n        label: 'All Time',\n        value: 'all'\n      }, {\n        label: 'Today',\n        value: 'today'\n      }, {\n        label: 'This Week',\n        value: 'week'\n      }, {\n        label: 'This Month',\n        value: 'month'\n      }];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.loadProducts();\n      this.loadWishlist();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadProducts() {\n      this.isLoading = true;\n      this.currentPage = 1;\n      this.shopDataService.loadNewArrivals(this.maxProducts).pipe(takeUntil(this.destroy$)).subscribe({\n        next: products => {\n          this.products = products;\n          this.hasMoreProducts = products.length === this.maxProducts;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading new arrivals:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n    loadMoreProducts() {\n      if (this.isLoadingMore || !this.hasMoreProducts) return;\n      this.isLoadingMore = true;\n      this.currentPage++;\n      // In a real implementation, this would load the next page\n      setTimeout(() => {\n        // Mock loading more products\n        this.isLoadingMore = false;\n        this.hasMoreProducts = false; // For demo purposes\n      }, 1000);\n    }\n    setActiveTimeFilter(filter) {\n      this.activeTimeFilter = filter;\n      this.loadProducts();\n    }\n    navigateToProduct(product) {\n      this.trackProductClick(product);\n      this.router.navigate(['/product', product._id]);\n    }\n    viewAllNewArrivals() {\n      this.router.navigate(['/shop/new-arrivals']);\n    }\n    toggleWishlist(product) {\n      if (this.wishlistItems.has(product._id)) {\n        this.wishlistItems.delete(product._id);\n      } else {\n        this.wishlistItems.add(product._id);\n      }\n      this.saveWishlist();\n    }\n    isInWishlist(productId) {\n      return this.wishlistItems.has(productId);\n    }\n    quickView(product) {\n      console.log('Quick view:', product);\n    }\n    addToCart(product) {\n      if (product.availability.status !== 'in-stock') {\n        return;\n      }\n      console.log('Add to cart:', product);\n      this.showAddToCartSuccess(product);\n    }\n    getProductImage(product) {\n      const primaryImage = product.images.find(img => img.isPrimary);\n      return primaryImage ? primaryImage.url : product.images[0]?.url || '';\n    }\n    getStarArray(rating) {\n      return Array(5).fill(0).map((_, i) => i + 1);\n    }\n    getArrivalDateText(dateString) {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffTime = Math.abs(now.getTime() - date.getTime());\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      if (diffDays === 1) {\n        return 'Today';\n      } else if (diffDays <= 7) {\n        return `${diffDays} days ago`;\n      } else if (diffDays <= 30) {\n        const weeks = Math.floor(diffDays / 7);\n        return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;\n      } else {\n        return date.toLocaleDateString('en-US', {\n          month: 'short',\n          day: 'numeric'\n        });\n      }\n    }\n    onImageError(event) {\n      event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';\n    }\n    trackByProductId(index, product) {\n      return product._id;\n    }\n    trackProductClick(product) {\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'product_click', {\n          product_name: product.name,\n          product_id: product._id,\n          product_brand: product.brand,\n          product_category: product.category,\n          event_category: 'new_arrivals'\n        });\n      }\n    }\n    loadWishlist() {\n      const stored = localStorage.getItem('dfashion_wishlist');\n      if (stored) {\n        try {\n          const wishlist = JSON.parse(stored);\n          this.wishlistItems = new Set(wishlist);\n        } catch (e) {\n          console.warn('Failed to load wishlist');\n        }\n      }\n    }\n    saveWishlist() {\n      localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));\n    }\n    showAddToCartSuccess(product) {\n      console.log(`${product.name} added to cart!`);\n    }\n    static {\n      this.ɵfac = function NewArrivalsComponent_Factory(t) {\n        return new (t || NewArrivalsComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NewArrivalsComponent,\n        selectors: [[\"app-new-arrivals\"]],\n        inputs: {\n          maxProducts: \"maxProducts\",\n          showHeader: \"showHeader\",\n          showLoadMore: \"showLoadMore\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 19,\n        vars: 5,\n        consts: [[1, \"new-arrivals-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-sparkles\"], [1, \"section-subtitle\"], [1, \"header-actions\"], [1, \"time-filters\"], [\"class\", \"filter-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"load-more-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"filter-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"products-grid\"], [\"class\", \"product-card-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card-skeleton\"], [1, \"skeleton-image\"], [1, \"skeleton-content\"], [1, \"skeleton-text\"], [1, \"skeleton-text\", \"short\"], [\"class\", \"product-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"tabindex\", \"0\", 1, \"product-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"error\", \"src\", \"alt\"], [1, \"new-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"arrival-date\"], [1, \"quick-actions\"], [1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"fas\", \"fa-eye\"], [1, \"quick-action-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"stock-status\"], [4, \"ngIf\"], [1, \"discount-badge\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"fas\", \"fa-star\"], [1, \"original-price\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"fas\", \"fa-times-circle\"], [1, \"load-more-container\"], [1, \"load-more-btn\", 3, \"click\", \"disabled\"], [\"class\", \"fas fa-plus\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-plus\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"fas\", \"fa-box-open\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"]],\n        template: function NewArrivalsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵtext(5, \" New Arrivals \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 5);\n            i0.ɵɵtext(7, \"Fresh styles just landed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7);\n            i0.ɵɵtemplate(10, NewArrivalsComponent_button_10_Template, 2, 3, \"button\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function NewArrivalsComponent_Template_button_click_11_listener() {\n              return ctx.viewAllNewArrivals();\n            });\n            i0.ɵɵelementStart(12, \"span\");\n            i0.ɵɵtext(13, \"View All\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(14, \"i\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(15, NewArrivalsComponent_div_15_Template, 3, 2, \"div\", 11)(16, NewArrivalsComponent_div_16_Template, 2, 2, \"div\", 12)(17, NewArrivalsComponent_div_17_Template, 6, 4, \"div\", 13)(18, NewArrivalsComponent_div_18_Template, 10, 0, \"div\", 14);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.timeFilters);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length > 0 && ctx.hasMoreProducts);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe],\n        styles: [\".new-arrivals-section[_ngcontent-%COMP%]{padding:2rem 0;background:#f8f9fa}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:2rem;padding:0 1rem;gap:1rem}@media (max-width: 768px){.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#262626;margin:0 0 .5rem;display:flex;align-items:center;gap:.5rem}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#00bcd4;font-size:1.8rem;animation:_ngcontent-%COMP%_sparkle 3s infinite}@media (max-width: 768px){.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.5rem}}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{color:#8e8e8e;font-size:1rem;margin:0}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}@media (max-width: 768px){.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{flex-direction:column;width:100%;align-items:stretch}}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]{display:flex;gap:.5rem;background:#fff;padding:.25rem;border-radius:25px;box-shadow:0 2px 8px #0000000f}@media (max-width: 768px){.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]{justify-content:center}}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{padding:.5rem 1rem;border:none;background:transparent;color:#666;border-radius:20px;cursor:pointer;transition:all .3s ease;font-weight:500;font-size:.85rem}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover{background:#00bcd41a;color:#00bcd4}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn.active[_ngcontent-%COMP%]{background:#00bcd4;color:#fff;box-shadow:0 2px 8px #00bcd44d}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.5rem;background:linear-gradient(135deg,#00bcd4,#26c6da);color:#fff;border:none;border-radius:25px;font-weight:600;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 12px #00bcd44d;white-space:nowrap}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #00bcd466}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:transform .3s ease}.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:translate(3px)}.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{padding:0 1rem}.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1.5rem}@media (max-width: 768px){.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}}.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 2px 12px #0000000f}.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%]{width:100%;height:300px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]{padding:1rem}.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%]{height:16px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:4px;margin-bottom:.5rem}.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text.short[_ngcontent-%COMP%]{width:60%}.new-arrivals-section[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1.5rem;padding:0 1rem}@media (max-width: 768px){.new-arrivals-section[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 2px 12px #0000000f;cursor:pointer;transition:all .3s ease;position:relative}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 32px #00000026}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]{transform:scale(1.05)}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:focus{outline:3px solid #00bcd4;outline-offset:2px}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]{position:relative;height:300px;overflow:hidden;background:#f8f9fa}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:linear-gradient(135deg,#00bcd4,#26c6da);color:#fff;padding:.5rem .75rem;border-radius:20px;font-size:.8rem;font-weight:600;display:flex;align-items:center;gap:.25rem;box-shadow:0 2px 8px #00bcd44d}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_sparkle 3s infinite}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#e91e63;color:#fff;padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:600}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .arrival-date[_ngcontent-%COMP%]{position:absolute;bottom:12px;left:12px;background:#000000b3;color:#fff;padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:500;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{position:absolute;bottom:12px;right:12px;display:flex;flex-direction:column;gap:.5rem;opacity:0;transform:translateY(10px);transition:all .3s ease}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]{width:40px;height:40px;background:#ffffffe6;border:none;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:hover{background:#00bcd4;color:#fff;transform:scale(1.1)}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]   .fa-heart.wishlisted[_ngcontent-%COMP%]{color:#e91e63}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{padding:1rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem;font-weight:500;text-transform:uppercase;letter-spacing:.5px;margin-bottom:.25rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#262626;margin:0 0 .5rem;line-height:1.3;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:.125rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%]{color:#ddd;font-size:.8rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star.filled[_ngcontent-%COMP%]{color:#ffc107}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]{margin-bottom:.75rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#262626}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#8e8e8e;text-decoration:line-through;margin-left:.5rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%]{font-size:.8rem;font-weight:500;display:flex;align-items:center;gap:.25rem}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.in-stock[_ngcontent-%COMP%]{color:#4caf50}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.low-stock[_ngcontent-%COMP%]{color:#ff9800}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.out-of-stock[_ngcontent-%COMP%]{color:#f44336}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem}.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]{text-align:center;margin-top:2rem;padding:0 1rem}.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:1rem 2rem;background:linear-gradient(135deg,#00bcd4,#26c6da);color:#fff;border:none;border-radius:25px;font-weight:600;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 12px #00bcd44d}.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 6px 20px #00bcd466}.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{text-align:center;padding:3rem 1rem;color:#8e8e8e}.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem;color:#ddd}.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#666}.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1.5rem}.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#00bcd4;color:#fff;border:none;padding:.75rem 1.5rem;border-radius:8px;cursor:pointer;display:flex;align-items:center;gap:.5rem;margin:0 auto}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}@keyframes _ngcontent-%COMP%_sparkle{0%,to{transform:scale(1) rotate(0);opacity:1}25%{transform:scale(1.1) rotate(90deg);opacity:.8}50%{transform:scale(1) rotate(180deg);opacity:1}75%{transform:scale(1.1) rotate(270deg);opacity:.8}}@media (prefers-color-scheme: dark){.new-arrivals-section[_ngcontent-%COMP%]{background:#121212}.new-arrivals-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#fff}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{background:#1e1e1e}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{color:#fff}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]{background:#2a2a2a}.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]{background:#1e1e1ee6;color:#fff}.new-arrivals-section[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]{background:#1e1e1e}.new-arrivals-section[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{color:#fff}.new-arrivals-section[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover{background:#00bcd433}.new-arrivals-section[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00bcd4,#26c6da)}}\"]\n      });\n    }\n  }\n  return NewArrivalsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}