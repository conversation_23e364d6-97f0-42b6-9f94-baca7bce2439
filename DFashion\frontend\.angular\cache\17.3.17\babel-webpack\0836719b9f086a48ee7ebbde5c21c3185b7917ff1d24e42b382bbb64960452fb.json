{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { io } from 'socket.io-client';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nexport let RealtimeService = /*#__PURE__*/(() => {\n  class RealtimeService {\n    constructor(authService) {\n      this.authService = authService;\n      this.socket = null;\n      this.connected = new BehaviorSubject(false);\n      this.events = new BehaviorSubject(null);\n      // Observables\n      this.connected$ = this.connected.asObservable();\n      this.events$ = this.events.asObservable();\n      // Specific event subjects\n      this.cartUpdates = new BehaviorSubject(null);\n      this.wishlistUpdates = new BehaviorSubject(null);\n      this.postUpdates = new BehaviorSubject(null);\n      this.storyUpdates = new BehaviorSubject(null);\n      this.productUpdates = new BehaviorSubject(null);\n      // Specific observables\n      this.cartUpdates$ = this.cartUpdates.asObservable();\n      this.wishlistUpdates$ = this.wishlistUpdates.asObservable();\n      this.postUpdates$ = this.postUpdates.asObservable();\n      this.storyUpdates$ = this.storyUpdates.asObservable();\n      this.productUpdates$ = this.productUpdates.asObservable();\n      // Auto-connect when user is authenticated\n      this.authService.isAuthenticated$.subscribe(isAuth => {\n        if (isAuth) {\n          this.connect();\n        } else {\n          this.disconnect();\n        }\n      });\n    }\n    connect() {\n      if (this.socket?.connected) {\n        return;\n      }\n      const token = localStorage.getItem('token');\n      this.socket = io(environment.socketUrl, {\n        auth: {\n          token: token\n        },\n        transports: ['websocket', 'polling']\n      });\n      this.setupEventListeners();\n    }\n    disconnect() {\n      if (this.socket) {\n        this.socket.disconnect();\n        this.socket = null;\n        this.connected.next(false);\n      }\n    }\n    setupEventListeners() {\n      if (!this.socket) return;\n      // Connection events\n      this.socket.on('connect', () => {\n        console.log('🔌 Connected to real-time server');\n        this.connected.next(true);\n      });\n      this.socket.on('disconnect', () => {\n        console.log('🔌 Disconnected from real-time server');\n        this.connected.next(false);\n      });\n      this.socket.on('connect_error', error => {\n        console.error('🚨 Socket connection error:', error);\n        this.connected.next(false);\n      });\n      // Cart events\n      this.socket.on('cart:updated', data => {\n        console.log('🛒 Cart updated:', data);\n        this.cartUpdates.next(data);\n        this.emitEvent('cart:updated', data);\n      });\n      // Wishlist events\n      this.socket.on('wishlist:updated', data => {\n        console.log('❤️ Wishlist updated:', data);\n        this.wishlistUpdates.next(data);\n        this.emitEvent('wishlist:updated', data);\n      });\n      // Post events\n      this.socket.on('post:liked', data => {\n        console.log('👍 Post liked:', data);\n        this.postUpdates.next({\n          type: 'liked',\n          ...data\n        });\n        this.emitEvent('post:liked', data);\n      });\n      this.socket.on('post:commented', data => {\n        console.log('💬 Post commented:', data);\n        this.postUpdates.next({\n          type: 'commented',\n          ...data\n        });\n        this.emitEvent('post:commented', data);\n      });\n      // Story events\n      this.socket.on('story:viewed', data => {\n        console.log('👁️ Story viewed:', data);\n        this.storyUpdates.next({\n          type: 'viewed',\n          ...data\n        });\n        this.emitEvent('story:viewed', data);\n      });\n      // Product events\n      this.socket.on('product:viewed', data => {\n        console.log('👀 Product viewed:', data);\n        this.productUpdates.next({\n          type: 'viewed',\n          ...data\n        });\n        this.emitEvent('product:viewed', data);\n      });\n    }\n    emitEvent(type, data) {\n      this.events.next({\n        type,\n        data,\n        timestamp: new Date()\n      });\n    }\n    // Emit events to server\n    emitCartAdd(item) {\n      this.socket?.emit('cart:add', item);\n    }\n    emitCartRemove(itemId) {\n      this.socket?.emit('cart:remove', {\n        itemId\n      });\n    }\n    emitCartUpdate(item) {\n      this.socket?.emit('cart:update', item);\n    }\n    emitWishlistAdd(item) {\n      this.socket?.emit('wishlist:add', item);\n    }\n    emitWishlistRemove(itemId) {\n      this.socket?.emit('wishlist:remove', {\n        itemId\n      });\n    }\n    emitPostLike(postId) {\n      this.socket?.emit('post:like', {\n        postId\n      });\n    }\n    emitPostComment(postId, comment) {\n      this.socket?.emit('post:comment', {\n        postId,\n        comment\n      });\n    }\n    emitStoryView(storyId, storyOwnerId) {\n      this.socket?.emit('story:view', {\n        storyId,\n        storyOwnerId\n      });\n    }\n    emitProductView(productId) {\n      this.socket?.emit('product:view', {\n        productId\n      });\n    }\n    // Room management\n    joinProductRoom(productId) {\n      this.socket?.emit('join:product', productId);\n    }\n    leaveProductRoom(productId) {\n      this.socket?.emit('leave:product', productId);\n    }\n    joinPostRoom(postId) {\n      this.socket?.emit('join:post', postId);\n    }\n    leavePostRoom(postId) {\n      this.socket?.emit('leave:post', postId);\n    }\n    // Utility methods\n    isConnected() {\n      return this.socket?.connected || false;\n    }\n    getConnectionStatus() {\n      return this.connected$;\n    }\n    // Subscribe to specific event types\n    onCartUpdate() {\n      return this.cartUpdates$;\n    }\n    onWishlistUpdate() {\n      return this.wishlistUpdates$;\n    }\n    onPostUpdate() {\n      return this.postUpdates$;\n    }\n    onStoryUpdate() {\n      return this.storyUpdates$;\n    }\n    onProductUpdate() {\n      return this.productUpdates$;\n    }\n    static {\n      this.ɵfac = function RealtimeService_Factory(t) {\n        return new (t || RealtimeService)(i0.ɵɵinject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RealtimeService,\n        factory: RealtimeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RealtimeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}