{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, HostListener, Component, ChangeDetectionStrategy, Attribute, Optional, SkipSelf, ViewChild, ContentChild, ContentChildren, forwardRef, Injectable, inject, Injector, EnvironmentInjector, APP_INITIALIZER, NgZone, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, MaxValidator, MinValidator } from '@angular/forms';\nimport * as i2$1 from '@ionic/angular/common';\nimport { ValueAccessor, setIonicClasses, IonRouterOutlet as IonRouterOutlet$1, IonTabs as IonTabs$1, IonBackButton as IonBackButton$1, IonNav as IonNav$1, RouterLinkDelegateDirective as RouterLinkDelegateDirective$1, RouterLinkWithHrefDelegateDirective as RouterLinkWithHrefDelegateDirective$1, IonModal as IonModal$1, IonPopover as IonPopover$1, OverlayBaseController, MenuController as Menu<PERSON><PERSON>roller$1, AngularDelegate, raf, ConfigToken, provideComponentInputBinding } from '@ionic/angular/common';\nexport { AngularDelegate, Config, DomController, IonicRouteStrategy, NavController, NavParams, Platform } from '@ionic/angular/common';\nimport { __decorate } from 'tslib';\nimport { fromEvent } from 'rxjs';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { alertController, createAnimation, getTimeGivenProgression, actionSheetController, createGesture, loadingController, menuController, modalController, pickerController, popoverController, toastController, setupConfig } from '@ionic/core';\nexport { IonicSafeString, IonicSlides, createAnimation, createGesture, getIonPageElement, getPlatforms, getTimeGivenProgression, iosTransitionAnimation, isPlatform, mdTransitionAnimation, openURL } from '@ionic/core';\nimport { applyPolyfills, defineCustomElements } from '@ionic/core/loader';\nconst _c0 = [\"*\"];\nconst _c1 = [\"outlet\"];\nconst _c2 = [[[\"\", \"slot\", \"top\"]], \"*\"];\nconst _c3 = [\"[slot=top]\", \"*\"];\nfunction IonModal_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelementContainer(1, 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template);\n  }\n}\nfunction IonPopover_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template);\n  }\n}\nlet BooleanValueAccessorDirective = /*#__PURE__*/(() => {\n  class BooleanValueAccessorDirective extends ValueAccessor {\n    constructor(injector, el) {\n      super(injector, el);\n    }\n    writeValue(value) {\n      this.elementRef.nativeElement.checked = this.lastValue = value;\n      setIonicClasses(this.elementRef);\n    }\n    _handleIonChange(el) {\n      this.handleValueChange(el, el.checked);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */BooleanValueAccessorDirective.ɵfac = function BooleanValueAccessorDirective_Factory(t) {\n    return new (t || BooleanValueAccessorDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  BooleanValueAccessorDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BooleanValueAccessorDirective,\n    selectors: [[\"ion-checkbox\"], [\"ion-toggle\"]],\n    hostBindings: function BooleanValueAccessorDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionChange\", function BooleanValueAccessorDirective_ionChange_HostBindingHandler($event) {\n          return ctx._handleIonChange($event.target);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: BooleanValueAccessorDirective,\n      multi: true\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return BooleanValueAccessorDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NumericValueAccessorDirective = /*#__PURE__*/(() => {\n  class NumericValueAccessorDirective extends ValueAccessor {\n    constructor(injector, el) {\n      super(injector, el);\n    }\n    handleInputEvent(el) {\n      this.handleValueChange(el, el.value);\n    }\n    registerOnChange(fn) {\n      super.registerOnChange(value => {\n        fn(value === '' ? null : parseFloat(value));\n      });\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */NumericValueAccessorDirective.ɵfac = function NumericValueAccessorDirective_Factory(t) {\n    return new (t || NumericValueAccessorDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  NumericValueAccessorDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NumericValueAccessorDirective,\n    selectors: [[\"ion-input\", \"type\", \"number\"]],\n    hostBindings: function NumericValueAccessorDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionInput\", function NumericValueAccessorDirective_ionInput_HostBindingHandler($event) {\n          return ctx.handleInputEvent($event.target);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: NumericValueAccessorDirective,\n      multi: true\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return NumericValueAccessorDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RadioValueAccessorDirective = /*#__PURE__*/(() => {\n  class RadioValueAccessorDirective extends ValueAccessor {\n    constructor(injector, el) {\n      super(injector, el);\n    }\n    _handleIonSelect(el) {\n      /**\n       * The `el` type is any to access the `checked` state property\n       * that is not exposed on the type interface.\n       */\n      this.handleValueChange(el, el.checked);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */RadioValueAccessorDirective.ɵfac = function RadioValueAccessorDirective_Factory(t) {\n    return new (t || RadioValueAccessorDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  RadioValueAccessorDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RadioValueAccessorDirective,\n    selectors: [[\"ion-radio\"]],\n    hostBindings: function RadioValueAccessorDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionSelect\", function RadioValueAccessorDirective_ionSelect_HostBindingHandler($event) {\n          return ctx._handleIonSelect($event.target);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: RadioValueAccessorDirective,\n      multi: true\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return RadioValueAccessorDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SelectValueAccessorDirective = /*#__PURE__*/(() => {\n  class SelectValueAccessorDirective extends ValueAccessor {\n    constructor(injector, el) {\n      super(injector, el);\n    }\n    _handleChangeEvent(el) {\n      this.handleValueChange(el, el.value);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */SelectValueAccessorDirective.ɵfac = function SelectValueAccessorDirective_Factory(t) {\n    return new (t || SelectValueAccessorDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  SelectValueAccessorDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: SelectValueAccessorDirective,\n    selectors: [[\"ion-select\"], [\"ion-radio-group\"], [\"ion-segment\"], [\"ion-datetime\"]],\n    hostBindings: function SelectValueAccessorDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionChange\", function SelectValueAccessorDirective_ionChange_HostBindingHandler($event) {\n          return ctx._handleChangeEvent($event.target);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: SelectValueAccessorDirective,\n      multi: true\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return SelectValueAccessorDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// TODO(FW-5495): rename class since range isn't a text component\nlet TextValueAccessorDirective = /*#__PURE__*/(() => {\n  class TextValueAccessorDirective extends ValueAccessor {\n    constructor(injector, el) {\n      super(injector, el);\n    }\n    _handleInputEvent(el) {\n      this.handleValueChange(el, el.value);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */TextValueAccessorDirective.ɵfac = function TextValueAccessorDirective_Factory(t) {\n    return new (t || TextValueAccessorDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  TextValueAccessorDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TextValueAccessorDirective,\n    selectors: [[\"ion-input\", 3, \"type\", \"number\"], [\"ion-textarea\"], [\"ion-searchbar\"], [\"ion-range\"]],\n    hostBindings: function TextValueAccessorDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionInput\", function TextValueAccessorDirective_ionInput_HostBindingHandler($event) {\n          return ctx._handleInputEvent($event.target);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: TextValueAccessorDirective,\n      multi: true\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return TextValueAccessorDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/* eslint-disable */\nconst proxyInputs = (Cmp, inputs) => {\n  const Prototype = Cmp.prototype;\n  inputs.forEach(item => {\n    Object.defineProperty(Prototype, item, {\n      get() {\n        return this.el[item];\n      },\n      set(val) {\n        this.z.runOutsideAngular(() => this.el[item] = val);\n      },\n      /**\n       * In the event that proxyInputs is called\n       * multiple times re-defining these inputs\n       * will cause an error to be thrown. As a result\n       * we set configurable: true to indicate these\n       * properties can be changed.\n       */\n      configurable: true\n    });\n  });\n};\nconst proxyMethods = (Cmp, methods) => {\n  const Prototype = Cmp.prototype;\n  methods.forEach(methodName => {\n    Prototype[methodName] = function () {\n      const args = arguments;\n      return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n    };\n  });\n};\nconst proxyOutputs = (instance, el, events) => {\n  events.forEach(eventName => instance[eventName] = fromEvent(el, eventName));\n};\nconst defineCustomElement = (tagName, customElement) => {\n  if (customElement !== undefined && typeof customElements !== 'undefined' && !customElements.get(tagName)) {\n    customElements.define(tagName, customElement);\n  }\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n  const decorator = function (cls) {\n    const {\n      defineCustomElementFn,\n      inputs,\n      methods\n    } = opts;\n    if (defineCustomElementFn !== undefined) {\n      defineCustomElementFn();\n    }\n    if (inputs) {\n      proxyInputs(cls, inputs);\n    }\n    if (methods) {\n      proxyMethods(cls, methods);\n    }\n    return cls;\n  };\n  return decorator;\n}\nlet IonAccordion = /*#__PURE__*/(() => {\n  let IonAccordion = class IonAccordion {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonAccordion.ɵfac = function IonAccordion_Factory(t) {\n    return new (t || IonAccordion)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonAccordion.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonAccordion,\n    selectors: [[\"ion-accordion\"]],\n    inputs: {\n      disabled: \"disabled\",\n      mode: \"mode\",\n      readonly: \"readonly\",\n      toggleIcon: \"toggleIcon\",\n      toggleIconSlot: \"toggleIconSlot\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonAccordion_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonAccordion = __decorate([ProxyCmp({\n    inputs: ['disabled', 'mode', 'readonly', 'toggleIcon', 'toggleIconSlot', 'value']\n  })], IonAccordion);\n  return IonAccordion;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonAccordionGroup = /*#__PURE__*/(() => {\n  let IonAccordionGroup = class IonAccordionGroup {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonAccordionGroup.ɵfac = function IonAccordionGroup_Factory(t) {\n    return new (t || IonAccordionGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonAccordionGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonAccordionGroup,\n    selectors: [[\"ion-accordion-group\"]],\n    inputs: {\n      animated: \"animated\",\n      disabled: \"disabled\",\n      expand: \"expand\",\n      mode: \"mode\",\n      multiple: \"multiple\",\n      readonly: \"readonly\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonAccordionGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonAccordionGroup = __decorate([ProxyCmp({\n    inputs: ['animated', 'disabled', 'expand', 'mode', 'multiple', 'readonly', 'value']\n  })], IonAccordionGroup);\n  return IonAccordionGroup;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonActionSheet = /*#__PURE__*/(() => {\n  let IonActionSheet = class IonActionSheet {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionActionSheetDidPresent', 'ionActionSheetWillPresent', 'ionActionSheetWillDismiss', 'ionActionSheetDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonActionSheet.ɵfac = function IonActionSheet_Factory(t) {\n    return new (t || IonActionSheet)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonActionSheet.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonActionSheet,\n    selectors: [[\"ion-action-sheet\"]],\n    inputs: {\n      animated: \"animated\",\n      backdropDismiss: \"backdropDismiss\",\n      buttons: \"buttons\",\n      cssClass: \"cssClass\",\n      enterAnimation: \"enterAnimation\",\n      header: \"header\",\n      htmlAttributes: \"htmlAttributes\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      subHeader: \"subHeader\",\n      translucent: \"translucent\",\n      trigger: \"trigger\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonActionSheet_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonActionSheet = __decorate([ProxyCmp({\n    inputs: ['animated', 'backdropDismiss', 'buttons', 'cssClass', 'enterAnimation', 'header', 'htmlAttributes', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'subHeader', 'translucent', 'trigger'],\n    methods: ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss']\n  })], IonActionSheet);\n  return IonActionSheet;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonAlert = /*#__PURE__*/(() => {\n  let IonAlert = class IonAlert {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionAlertDidPresent', 'ionAlertWillPresent', 'ionAlertWillDismiss', 'ionAlertDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonAlert.ɵfac = function IonAlert_Factory(t) {\n    return new (t || IonAlert)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonAlert.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonAlert,\n    selectors: [[\"ion-alert\"]],\n    inputs: {\n      animated: \"animated\",\n      backdropDismiss: \"backdropDismiss\",\n      buttons: \"buttons\",\n      cssClass: \"cssClass\",\n      enterAnimation: \"enterAnimation\",\n      header: \"header\",\n      htmlAttributes: \"htmlAttributes\",\n      inputs: \"inputs\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      message: \"message\",\n      mode: \"mode\",\n      subHeader: \"subHeader\",\n      translucent: \"translucent\",\n      trigger: \"trigger\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonAlert_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonAlert = __decorate([ProxyCmp({\n    inputs: ['animated', 'backdropDismiss', 'buttons', 'cssClass', 'enterAnimation', 'header', 'htmlAttributes', 'inputs', 'isOpen', 'keyboardClose', 'leaveAnimation', 'message', 'mode', 'subHeader', 'translucent', 'trigger'],\n    methods: ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss']\n  })], IonAlert);\n  return IonAlert;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonApp = /*#__PURE__*/(() => {\n  let IonApp = class IonApp {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonApp.ɵfac = function IonApp_Factory(t) {\n    return new (t || IonApp)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonApp.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonApp,\n    selectors: [[\"ion-app\"]],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonApp_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonApp = __decorate([ProxyCmp({})], IonApp);\n  return IonApp;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonAvatar = /*#__PURE__*/(() => {\n  let IonAvatar = class IonAvatar {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonAvatar.ɵfac = function IonAvatar_Factory(t) {\n    return new (t || IonAvatar)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonAvatar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonAvatar,\n    selectors: [[\"ion-avatar\"]],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonAvatar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonAvatar = __decorate([ProxyCmp({})], IonAvatar);\n  return IonAvatar;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonBackdrop = /*#__PURE__*/(() => {\n  let IonBackdrop = class IonBackdrop {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionBackdropTap']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonBackdrop.ɵfac = function IonBackdrop_Factory(t) {\n    return new (t || IonBackdrop)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonBackdrop.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonBackdrop,\n    selectors: [[\"ion-backdrop\"]],\n    inputs: {\n      stopPropagation: \"stopPropagation\",\n      tappable: \"tappable\",\n      visible: \"visible\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonBackdrop_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonBackdrop = __decorate([ProxyCmp({\n    inputs: ['stopPropagation', 'tappable', 'visible']\n  })], IonBackdrop);\n  return IonBackdrop;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonBadge = /*#__PURE__*/(() => {\n  let IonBadge = class IonBadge {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonBadge.ɵfac = function IonBadge_Factory(t) {\n    return new (t || IonBadge)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonBadge.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonBadge,\n    selectors: [[\"ion-badge\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonBadge_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonBadge = __decorate([ProxyCmp({\n    inputs: ['color', 'mode']\n  })], IonBadge);\n  return IonBadge;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonBreadcrumb = /*#__PURE__*/(() => {\n  let IonBreadcrumb = class IonBreadcrumb {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonBreadcrumb.ɵfac = function IonBreadcrumb_Factory(t) {\n    return new (t || IonBreadcrumb)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonBreadcrumb.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonBreadcrumb,\n    selectors: [[\"ion-breadcrumb\"]],\n    inputs: {\n      active: \"active\",\n      color: \"color\",\n      disabled: \"disabled\",\n      download: \"download\",\n      href: \"href\",\n      mode: \"mode\",\n      rel: \"rel\",\n      routerAnimation: \"routerAnimation\",\n      routerDirection: \"routerDirection\",\n      separator: \"separator\",\n      target: \"target\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonBreadcrumb_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonBreadcrumb = __decorate([ProxyCmp({\n    inputs: ['active', 'color', 'disabled', 'download', 'href', 'mode', 'rel', 'routerAnimation', 'routerDirection', 'separator', 'target']\n  })], IonBreadcrumb);\n  return IonBreadcrumb;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonBreadcrumbs = /*#__PURE__*/(() => {\n  let IonBreadcrumbs = class IonBreadcrumbs {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionCollapsedClick']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonBreadcrumbs.ɵfac = function IonBreadcrumbs_Factory(t) {\n    return new (t || IonBreadcrumbs)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonBreadcrumbs.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonBreadcrumbs,\n    selectors: [[\"ion-breadcrumbs\"]],\n    inputs: {\n      color: \"color\",\n      itemsAfterCollapse: \"itemsAfterCollapse\",\n      itemsBeforeCollapse: \"itemsBeforeCollapse\",\n      maxItems: \"maxItems\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonBreadcrumbs_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonBreadcrumbs = __decorate([ProxyCmp({\n    inputs: ['color', 'itemsAfterCollapse', 'itemsBeforeCollapse', 'maxItems', 'mode']\n  })], IonBreadcrumbs);\n  return IonBreadcrumbs;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonButton = /*#__PURE__*/(() => {\n  let IonButton = class IonButton {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonButton.ɵfac = function IonButton_Factory(t) {\n    return new (t || IonButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonButton,\n    selectors: [[\"ion-button\"]],\n    inputs: {\n      buttonType: \"buttonType\",\n      color: \"color\",\n      disabled: \"disabled\",\n      download: \"download\",\n      expand: \"expand\",\n      fill: \"fill\",\n      form: \"form\",\n      href: \"href\",\n      mode: \"mode\",\n      rel: \"rel\",\n      routerAnimation: \"routerAnimation\",\n      routerDirection: \"routerDirection\",\n      shape: \"shape\",\n      size: \"size\",\n      strong: \"strong\",\n      target: \"target\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonButton = __decorate([ProxyCmp({\n    inputs: ['buttonType', 'color', 'disabled', 'download', 'expand', 'fill', 'form', 'href', 'mode', 'rel', 'routerAnimation', 'routerDirection', 'shape', 'size', 'strong', 'target', 'type']\n  })], IonButton);\n  return IonButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonButtons = /*#__PURE__*/(() => {\n  let IonButtons = class IonButtons {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonButtons.ɵfac = function IonButtons_Factory(t) {\n    return new (t || IonButtons)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonButtons.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonButtons,\n    selectors: [[\"ion-buttons\"]],\n    inputs: {\n      collapse: \"collapse\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonButtons_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonButtons = __decorate([ProxyCmp({\n    inputs: ['collapse']\n  })], IonButtons);\n  return IonButtons;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonCard = /*#__PURE__*/(() => {\n  let IonCard = class IonCard {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonCard.ɵfac = function IonCard_Factory(t) {\n    return new (t || IonCard)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonCard.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonCard,\n    selectors: [[\"ion-card\"]],\n    inputs: {\n      button: \"button\",\n      color: \"color\",\n      disabled: \"disabled\",\n      download: \"download\",\n      href: \"href\",\n      mode: \"mode\",\n      rel: \"rel\",\n      routerAnimation: \"routerAnimation\",\n      routerDirection: \"routerDirection\",\n      target: \"target\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonCard_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonCard = __decorate([ProxyCmp({\n    inputs: ['button', 'color', 'disabled', 'download', 'href', 'mode', 'rel', 'routerAnimation', 'routerDirection', 'target', 'type']\n  })], IonCard);\n  return IonCard;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonCardContent = /*#__PURE__*/(() => {\n  let IonCardContent = class IonCardContent {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonCardContent.ɵfac = function IonCardContent_Factory(t) {\n    return new (t || IonCardContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonCardContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonCardContent,\n    selectors: [[\"ion-card-content\"]],\n    inputs: {\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonCardContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonCardContent = __decorate([ProxyCmp({\n    inputs: ['mode']\n  })], IonCardContent);\n  return IonCardContent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonCardHeader = /*#__PURE__*/(() => {\n  let IonCardHeader = class IonCardHeader {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonCardHeader.ɵfac = function IonCardHeader_Factory(t) {\n    return new (t || IonCardHeader)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonCardHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonCardHeader,\n    selectors: [[\"ion-card-header\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      translucent: \"translucent\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonCardHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonCardHeader = __decorate([ProxyCmp({\n    inputs: ['color', 'mode', 'translucent']\n  })], IonCardHeader);\n  return IonCardHeader;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonCardSubtitle = /*#__PURE__*/(() => {\n  let IonCardSubtitle = class IonCardSubtitle {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonCardSubtitle.ɵfac = function IonCardSubtitle_Factory(t) {\n    return new (t || IonCardSubtitle)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonCardSubtitle.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonCardSubtitle,\n    selectors: [[\"ion-card-subtitle\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonCardSubtitle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonCardSubtitle = __decorate([ProxyCmp({\n    inputs: ['color', 'mode']\n  })], IonCardSubtitle);\n  return IonCardSubtitle;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonCardTitle = /*#__PURE__*/(() => {\n  let IonCardTitle = class IonCardTitle {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonCardTitle.ɵfac = function IonCardTitle_Factory(t) {\n    return new (t || IonCardTitle)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonCardTitle.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonCardTitle,\n    selectors: [[\"ion-card-title\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonCardTitle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonCardTitle = __decorate([ProxyCmp({\n    inputs: ['color', 'mode']\n  })], IonCardTitle);\n  return IonCardTitle;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonCheckbox = /*#__PURE__*/(() => {\n  let IonCheckbox = class IonCheckbox {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange', 'ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonCheckbox.ɵfac = function IonCheckbox_Factory(t) {\n    return new (t || IonCheckbox)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonCheckbox.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonCheckbox,\n    selectors: [[\"ion-checkbox\"]],\n    inputs: {\n      alignment: \"alignment\",\n      checked: \"checked\",\n      color: \"color\",\n      disabled: \"disabled\",\n      indeterminate: \"indeterminate\",\n      justify: \"justify\",\n      labelPlacement: \"labelPlacement\",\n      legacy: \"legacy\",\n      mode: \"mode\",\n      name: \"name\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonCheckbox_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonCheckbox = __decorate([ProxyCmp({\n    inputs: ['alignment', 'checked', 'color', 'disabled', 'indeterminate', 'justify', 'labelPlacement', 'legacy', 'mode', 'name', 'value']\n  })], IonCheckbox);\n  return IonCheckbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonChip = /*#__PURE__*/(() => {\n  let IonChip = class IonChip {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonChip.ɵfac = function IonChip_Factory(t) {\n    return new (t || IonChip)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonChip.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonChip,\n    selectors: [[\"ion-chip\"]],\n    inputs: {\n      color: \"color\",\n      disabled: \"disabled\",\n      mode: \"mode\",\n      outline: \"outline\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonChip_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonChip = __decorate([ProxyCmp({\n    inputs: ['color', 'disabled', 'mode', 'outline']\n  })], IonChip);\n  return IonChip;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonCol = /*#__PURE__*/(() => {\n  let IonCol = class IonCol {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonCol.ɵfac = function IonCol_Factory(t) {\n    return new (t || IonCol)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonCol.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonCol,\n    selectors: [[\"ion-col\"]],\n    inputs: {\n      offset: \"offset\",\n      offsetLg: \"offsetLg\",\n      offsetMd: \"offsetMd\",\n      offsetSm: \"offsetSm\",\n      offsetXl: \"offsetXl\",\n      offsetXs: \"offsetXs\",\n      pull: \"pull\",\n      pullLg: \"pullLg\",\n      pullMd: \"pullMd\",\n      pullSm: \"pullSm\",\n      pullXl: \"pullXl\",\n      pullXs: \"pullXs\",\n      push: \"push\",\n      pushLg: \"pushLg\",\n      pushMd: \"pushMd\",\n      pushSm: \"pushSm\",\n      pushXl: \"pushXl\",\n      pushXs: \"pushXs\",\n      size: \"size\",\n      sizeLg: \"sizeLg\",\n      sizeMd: \"sizeMd\",\n      sizeSm: \"sizeSm\",\n      sizeXl: \"sizeXl\",\n      sizeXs: \"sizeXs\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonCol_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonCol = __decorate([ProxyCmp({\n    inputs: ['offset', 'offsetLg', 'offsetMd', 'offsetSm', 'offsetXl', 'offsetXs', 'pull', 'pullLg', 'pullMd', 'pullSm', 'pullXl', 'pullXs', 'push', 'pushLg', 'pushMd', 'pushSm', 'pushXl', 'pushXs', 'size', 'sizeLg', 'sizeMd', 'sizeSm', 'sizeXl', 'sizeXs']\n  })], IonCol);\n  return IonCol;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonContent = /*#__PURE__*/(() => {\n  let IonContent = class IonContent {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionScrollStart', 'ionScroll', 'ionScrollEnd']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonContent.ɵfac = function IonContent_Factory(t) {\n    return new (t || IonContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonContent,\n    selectors: [[\"ion-content\"]],\n    inputs: {\n      color: \"color\",\n      forceOverscroll: \"forceOverscroll\",\n      fullscreen: \"fullscreen\",\n      scrollEvents: \"scrollEvents\",\n      scrollX: \"scrollX\",\n      scrollY: \"scrollY\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonContent = __decorate([ProxyCmp({\n    inputs: ['color', 'forceOverscroll', 'fullscreen', 'scrollEvents', 'scrollX', 'scrollY'],\n    methods: ['getScrollElement', 'scrollToTop', 'scrollToBottom', 'scrollByPoint', 'scrollToPoint']\n  })], IonContent);\n  return IonContent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonDatetime = /*#__PURE__*/(() => {\n  let IonDatetime = class IonDatetime {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionCancel', 'ionChange', 'ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonDatetime.ɵfac = function IonDatetime_Factory(t) {\n    return new (t || IonDatetime)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonDatetime.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonDatetime,\n    selectors: [[\"ion-datetime\"]],\n    inputs: {\n      cancelText: \"cancelText\",\n      clearText: \"clearText\",\n      color: \"color\",\n      dayValues: \"dayValues\",\n      disabled: \"disabled\",\n      doneText: \"doneText\",\n      firstDayOfWeek: \"firstDayOfWeek\",\n      formatOptions: \"formatOptions\",\n      highlightedDates: \"highlightedDates\",\n      hourCycle: \"hourCycle\",\n      hourValues: \"hourValues\",\n      isDateEnabled: \"isDateEnabled\",\n      locale: \"locale\",\n      max: \"max\",\n      min: \"min\",\n      minuteValues: \"minuteValues\",\n      mode: \"mode\",\n      monthValues: \"monthValues\",\n      multiple: \"multiple\",\n      name: \"name\",\n      preferWheel: \"preferWheel\",\n      presentation: \"presentation\",\n      readonly: \"readonly\",\n      showClearButton: \"showClearButton\",\n      showDefaultButtons: \"showDefaultButtons\",\n      showDefaultTimeLabel: \"showDefaultTimeLabel\",\n      showDefaultTitle: \"showDefaultTitle\",\n      size: \"size\",\n      titleSelectedDatesFormatter: \"titleSelectedDatesFormatter\",\n      value: \"value\",\n      yearValues: \"yearValues\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonDatetime_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonDatetime = __decorate([ProxyCmp({\n    inputs: ['cancelText', 'clearText', 'color', 'dayValues', 'disabled', 'doneText', 'firstDayOfWeek', 'formatOptions', 'highlightedDates', 'hourCycle', 'hourValues', 'isDateEnabled', 'locale', 'max', 'min', 'minuteValues', 'mode', 'monthValues', 'multiple', 'name', 'preferWheel', 'presentation', 'readonly', 'showClearButton', 'showDefaultButtons', 'showDefaultTimeLabel', 'showDefaultTitle', 'size', 'titleSelectedDatesFormatter', 'value', 'yearValues'],\n    methods: ['confirm', 'reset', 'cancel']\n  })], IonDatetime);\n  return IonDatetime;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonDatetimeButton = /*#__PURE__*/(() => {\n  let IonDatetimeButton = class IonDatetimeButton {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonDatetimeButton.ɵfac = function IonDatetimeButton_Factory(t) {\n    return new (t || IonDatetimeButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonDatetimeButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonDatetimeButton,\n    selectors: [[\"ion-datetime-button\"]],\n    inputs: {\n      color: \"color\",\n      datetime: \"datetime\",\n      disabled: \"disabled\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonDatetimeButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonDatetimeButton = __decorate([ProxyCmp({\n    inputs: ['color', 'datetime', 'disabled', 'mode']\n  })], IonDatetimeButton);\n  return IonDatetimeButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonFab = /*#__PURE__*/(() => {\n  let IonFab = class IonFab {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonFab.ɵfac = function IonFab_Factory(t) {\n    return new (t || IonFab)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonFab.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonFab,\n    selectors: [[\"ion-fab\"]],\n    inputs: {\n      activated: \"activated\",\n      edge: \"edge\",\n      horizontal: \"horizontal\",\n      vertical: \"vertical\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonFab_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonFab = __decorate([ProxyCmp({\n    inputs: ['activated', 'edge', 'horizontal', 'vertical'],\n    methods: ['close']\n  })], IonFab);\n  return IonFab;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonFabButton = /*#__PURE__*/(() => {\n  let IonFabButton = class IonFabButton {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonFabButton.ɵfac = function IonFabButton_Factory(t) {\n    return new (t || IonFabButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonFabButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonFabButton,\n    selectors: [[\"ion-fab-button\"]],\n    inputs: {\n      activated: \"activated\",\n      closeIcon: \"closeIcon\",\n      color: \"color\",\n      disabled: \"disabled\",\n      download: \"download\",\n      href: \"href\",\n      mode: \"mode\",\n      rel: \"rel\",\n      routerAnimation: \"routerAnimation\",\n      routerDirection: \"routerDirection\",\n      show: \"show\",\n      size: \"size\",\n      target: \"target\",\n      translucent: \"translucent\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonFabButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonFabButton = __decorate([ProxyCmp({\n    inputs: ['activated', 'closeIcon', 'color', 'disabled', 'download', 'href', 'mode', 'rel', 'routerAnimation', 'routerDirection', 'show', 'size', 'target', 'translucent', 'type']\n  })], IonFabButton);\n  return IonFabButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonFabList = /*#__PURE__*/(() => {\n  let IonFabList = class IonFabList {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonFabList.ɵfac = function IonFabList_Factory(t) {\n    return new (t || IonFabList)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonFabList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonFabList,\n    selectors: [[\"ion-fab-list\"]],\n    inputs: {\n      activated: \"activated\",\n      side: \"side\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonFabList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonFabList = __decorate([ProxyCmp({\n    inputs: ['activated', 'side']\n  })], IonFabList);\n  return IonFabList;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonFooter = /*#__PURE__*/(() => {\n  let IonFooter = class IonFooter {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonFooter.ɵfac = function IonFooter_Factory(t) {\n    return new (t || IonFooter)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonFooter.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonFooter,\n    selectors: [[\"ion-footer\"]],\n    inputs: {\n      collapse: \"collapse\",\n      mode: \"mode\",\n      translucent: \"translucent\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonFooter_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonFooter = __decorate([ProxyCmp({\n    inputs: ['collapse', 'mode', 'translucent']\n  })], IonFooter);\n  return IonFooter;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonGrid = /*#__PURE__*/(() => {\n  let IonGrid = class IonGrid {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonGrid.ɵfac = function IonGrid_Factory(t) {\n    return new (t || IonGrid)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonGrid.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonGrid,\n    selectors: [[\"ion-grid\"]],\n    inputs: {\n      fixed: \"fixed\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonGrid_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonGrid = __decorate([ProxyCmp({\n    inputs: ['fixed']\n  })], IonGrid);\n  return IonGrid;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonHeader = /*#__PURE__*/(() => {\n  let IonHeader = class IonHeader {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonHeader.ɵfac = function IonHeader_Factory(t) {\n    return new (t || IonHeader)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonHeader,\n    selectors: [[\"ion-header\"]],\n    inputs: {\n      collapse: \"collapse\",\n      mode: \"mode\",\n      translucent: \"translucent\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonHeader = __decorate([ProxyCmp({\n    inputs: ['collapse', 'mode', 'translucent']\n  })], IonHeader);\n  return IonHeader;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonIcon = /*#__PURE__*/(() => {\n  let IonIcon = class IonIcon {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonIcon.ɵfac = function IonIcon_Factory(t) {\n    return new (t || IonIcon)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonIcon.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonIcon,\n    selectors: [[\"ion-icon\"]],\n    inputs: {\n      color: \"color\",\n      flipRtl: \"flipRtl\",\n      icon: \"icon\",\n      ios: \"ios\",\n      lazy: \"lazy\",\n      md: \"md\",\n      mode: \"mode\",\n      name: \"name\",\n      sanitize: \"sanitize\",\n      size: \"size\",\n      src: \"src\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonIcon = __decorate([ProxyCmp({\n    inputs: ['color', 'flipRtl', 'icon', 'ios', 'lazy', 'md', 'mode', 'name', 'sanitize', 'size', 'src']\n  })], IonIcon);\n  return IonIcon;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonImg = /*#__PURE__*/(() => {\n  let IonImg = class IonImg {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionImgWillLoad', 'ionImgDidLoad', 'ionError']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonImg.ɵfac = function IonImg_Factory(t) {\n    return new (t || IonImg)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonImg.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonImg,\n    selectors: [[\"ion-img\"]],\n    inputs: {\n      alt: \"alt\",\n      src: \"src\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonImg_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonImg = __decorate([ProxyCmp({\n    inputs: ['alt', 'src']\n  })], IonImg);\n  return IonImg;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonInfiniteScroll = /*#__PURE__*/(() => {\n  let IonInfiniteScroll = class IonInfiniteScroll {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionInfinite']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonInfiniteScroll.ɵfac = function IonInfiniteScroll_Factory(t) {\n    return new (t || IonInfiniteScroll)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonInfiniteScroll.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonInfiniteScroll,\n    selectors: [[\"ion-infinite-scroll\"]],\n    inputs: {\n      disabled: \"disabled\",\n      position: \"position\",\n      threshold: \"threshold\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonInfiniteScroll_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonInfiniteScroll = __decorate([ProxyCmp({\n    inputs: ['disabled', 'position', 'threshold'],\n    methods: ['complete']\n  })], IonInfiniteScroll);\n  return IonInfiniteScroll;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonInfiniteScrollContent = /*#__PURE__*/(() => {\n  let IonInfiniteScrollContent = class IonInfiniteScrollContent {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonInfiniteScrollContent.ɵfac = function IonInfiniteScrollContent_Factory(t) {\n    return new (t || IonInfiniteScrollContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonInfiniteScrollContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonInfiniteScrollContent,\n    selectors: [[\"ion-infinite-scroll-content\"]],\n    inputs: {\n      loadingSpinner: \"loadingSpinner\",\n      loadingText: \"loadingText\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonInfiniteScrollContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonInfiniteScrollContent = __decorate([ProxyCmp({\n    inputs: ['loadingSpinner', 'loadingText']\n  })], IonInfiniteScrollContent);\n  return IonInfiniteScrollContent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonInput = /*#__PURE__*/(() => {\n  let IonInput = class IonInput {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionInput', 'ionChange', 'ionBlur', 'ionFocus']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonInput.ɵfac = function IonInput_Factory(t) {\n    return new (t || IonInput)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonInput.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonInput,\n    selectors: [[\"ion-input\"]],\n    inputs: {\n      accept: \"accept\",\n      autocapitalize: \"autocapitalize\",\n      autocomplete: \"autocomplete\",\n      autocorrect: \"autocorrect\",\n      autofocus: \"autofocus\",\n      clearInput: \"clearInput\",\n      clearOnEdit: \"clearOnEdit\",\n      color: \"color\",\n      counter: \"counter\",\n      counterFormatter: \"counterFormatter\",\n      debounce: \"debounce\",\n      disabled: \"disabled\",\n      enterkeyhint: \"enterkeyhint\",\n      errorText: \"errorText\",\n      fill: \"fill\",\n      helperText: \"helperText\",\n      inputmode: \"inputmode\",\n      label: \"label\",\n      labelPlacement: \"labelPlacement\",\n      legacy: \"legacy\",\n      max: \"max\",\n      maxlength: \"maxlength\",\n      min: \"min\",\n      minlength: \"minlength\",\n      mode: \"mode\",\n      multiple: \"multiple\",\n      name: \"name\",\n      pattern: \"pattern\",\n      placeholder: \"placeholder\",\n      readonly: \"readonly\",\n      required: \"required\",\n      shape: \"shape\",\n      size: \"size\",\n      spellcheck: \"spellcheck\",\n      step: \"step\",\n      type: \"type\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonInput_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonInput = __decorate([ProxyCmp({\n    inputs: ['accept', 'autocapitalize', 'autocomplete', 'autocorrect', 'autofocus', 'clearInput', 'clearOnEdit', 'color', 'counter', 'counterFormatter', 'debounce', 'disabled', 'enterkeyhint', 'errorText', 'fill', 'helperText', 'inputmode', 'label', 'labelPlacement', 'legacy', 'max', 'maxlength', 'min', 'minlength', 'mode', 'multiple', 'name', 'pattern', 'placeholder', 'readonly', 'required', 'shape', 'size', 'spellcheck', 'step', 'type', 'value'],\n    methods: ['setFocus', 'getInputElement']\n  })], IonInput);\n  return IonInput;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonItem = /*#__PURE__*/(() => {\n  let IonItem = class IonItem {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonItem.ɵfac = function IonItem_Factory(t) {\n    return new (t || IonItem)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonItem.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonItem,\n    selectors: [[\"ion-item\"]],\n    inputs: {\n      button: \"button\",\n      color: \"color\",\n      counter: \"counter\",\n      counterFormatter: \"counterFormatter\",\n      detail: \"detail\",\n      detailIcon: \"detailIcon\",\n      disabled: \"disabled\",\n      download: \"download\",\n      fill: \"fill\",\n      href: \"href\",\n      lines: \"lines\",\n      mode: \"mode\",\n      rel: \"rel\",\n      routerAnimation: \"routerAnimation\",\n      routerDirection: \"routerDirection\",\n      shape: \"shape\",\n      target: \"target\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonItem_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonItem = __decorate([ProxyCmp({\n    inputs: ['button', 'color', 'counter', 'counterFormatter', 'detail', 'detailIcon', 'disabled', 'download', 'fill', 'href', 'lines', 'mode', 'rel', 'routerAnimation', 'routerDirection', 'shape', 'target', 'type']\n  })], IonItem);\n  return IonItem;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonItemDivider = /*#__PURE__*/(() => {\n  let IonItemDivider = class IonItemDivider {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonItemDivider.ɵfac = function IonItemDivider_Factory(t) {\n    return new (t || IonItemDivider)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonItemDivider.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonItemDivider,\n    selectors: [[\"ion-item-divider\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      sticky: \"sticky\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonItemDivider_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonItemDivider = __decorate([ProxyCmp({\n    inputs: ['color', 'mode', 'sticky']\n  })], IonItemDivider);\n  return IonItemDivider;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonItemGroup = /*#__PURE__*/(() => {\n  let IonItemGroup = class IonItemGroup {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonItemGroup.ɵfac = function IonItemGroup_Factory(t) {\n    return new (t || IonItemGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonItemGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonItemGroup,\n    selectors: [[\"ion-item-group\"]],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonItemGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonItemGroup = __decorate([ProxyCmp({})], IonItemGroup);\n  return IonItemGroup;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonItemOption = /*#__PURE__*/(() => {\n  let IonItemOption = class IonItemOption {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonItemOption.ɵfac = function IonItemOption_Factory(t) {\n    return new (t || IonItemOption)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonItemOption.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonItemOption,\n    selectors: [[\"ion-item-option\"]],\n    inputs: {\n      color: \"color\",\n      disabled: \"disabled\",\n      download: \"download\",\n      expandable: \"expandable\",\n      href: \"href\",\n      mode: \"mode\",\n      rel: \"rel\",\n      target: \"target\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonItemOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonItemOption = __decorate([ProxyCmp({\n    inputs: ['color', 'disabled', 'download', 'expandable', 'href', 'mode', 'rel', 'target', 'type']\n  })], IonItemOption);\n  return IonItemOption;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonItemOptions = /*#__PURE__*/(() => {\n  let IonItemOptions = class IonItemOptions {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionSwipe']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonItemOptions.ɵfac = function IonItemOptions_Factory(t) {\n    return new (t || IonItemOptions)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonItemOptions.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonItemOptions,\n    selectors: [[\"ion-item-options\"]],\n    inputs: {\n      side: \"side\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonItemOptions_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonItemOptions = __decorate([ProxyCmp({\n    inputs: ['side']\n  })], IonItemOptions);\n  return IonItemOptions;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonItemSliding = /*#__PURE__*/(() => {\n  let IonItemSliding = class IonItemSliding {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionDrag']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonItemSliding.ɵfac = function IonItemSliding_Factory(t) {\n    return new (t || IonItemSliding)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonItemSliding.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonItemSliding,\n    selectors: [[\"ion-item-sliding\"]],\n    inputs: {\n      disabled: \"disabled\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonItemSliding_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonItemSliding = __decorate([ProxyCmp({\n    inputs: ['disabled'],\n    methods: ['getOpenAmount', 'getSlidingRatio', 'open', 'close', 'closeOpened']\n  })], IonItemSliding);\n  return IonItemSliding;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonLabel = /*#__PURE__*/(() => {\n  let IonLabel = class IonLabel {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonLabel.ɵfac = function IonLabel_Factory(t) {\n    return new (t || IonLabel)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonLabel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonLabel,\n    selectors: [[\"ion-label\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      position: \"position\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonLabel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonLabel = __decorate([ProxyCmp({\n    inputs: ['color', 'mode', 'position']\n  })], IonLabel);\n  return IonLabel;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonList = /*#__PURE__*/(() => {\n  let IonList = class IonList {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonList.ɵfac = function IonList_Factory(t) {\n    return new (t || IonList)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonList,\n    selectors: [[\"ion-list\"]],\n    inputs: {\n      inset: \"inset\",\n      lines: \"lines\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonList = __decorate([ProxyCmp({\n    inputs: ['inset', 'lines', 'mode'],\n    methods: ['closeSlidingItems']\n  })], IonList);\n  return IonList;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonListHeader = /*#__PURE__*/(() => {\n  let IonListHeader = class IonListHeader {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonListHeader.ɵfac = function IonListHeader_Factory(t) {\n    return new (t || IonListHeader)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonListHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonListHeader,\n    selectors: [[\"ion-list-header\"]],\n    inputs: {\n      color: \"color\",\n      lines: \"lines\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonListHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonListHeader = __decorate([ProxyCmp({\n    inputs: ['color', 'lines', 'mode']\n  })], IonListHeader);\n  return IonListHeader;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonLoading = /*#__PURE__*/(() => {\n  let IonLoading = class IonLoading {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionLoadingDidPresent', 'ionLoadingWillPresent', 'ionLoadingWillDismiss', 'ionLoadingDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonLoading.ɵfac = function IonLoading_Factory(t) {\n    return new (t || IonLoading)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonLoading.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonLoading,\n    selectors: [[\"ion-loading\"]],\n    inputs: {\n      animated: \"animated\",\n      backdropDismiss: \"backdropDismiss\",\n      cssClass: \"cssClass\",\n      duration: \"duration\",\n      enterAnimation: \"enterAnimation\",\n      htmlAttributes: \"htmlAttributes\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      message: \"message\",\n      mode: \"mode\",\n      showBackdrop: \"showBackdrop\",\n      spinner: \"spinner\",\n      translucent: \"translucent\",\n      trigger: \"trigger\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonLoading_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonLoading = __decorate([ProxyCmp({\n    inputs: ['animated', 'backdropDismiss', 'cssClass', 'duration', 'enterAnimation', 'htmlAttributes', 'isOpen', 'keyboardClose', 'leaveAnimation', 'message', 'mode', 'showBackdrop', 'spinner', 'translucent', 'trigger'],\n    methods: ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss']\n  })], IonLoading);\n  return IonLoading;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonMenu = /*#__PURE__*/(() => {\n  let IonMenu = class IonMenu {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionWillOpen', 'ionWillClose', 'ionDidOpen', 'ionDidClose']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonMenu.ɵfac = function IonMenu_Factory(t) {\n    return new (t || IonMenu)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonMenu,\n    selectors: [[\"ion-menu\"]],\n    inputs: {\n      contentId: \"contentId\",\n      disabled: \"disabled\",\n      maxEdgeStart: \"maxEdgeStart\",\n      menuId: \"menuId\",\n      side: \"side\",\n      swipeGesture: \"swipeGesture\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonMenu = __decorate([ProxyCmp({\n    inputs: ['contentId', 'disabled', 'maxEdgeStart', 'menuId', 'side', 'swipeGesture', 'type'],\n    methods: ['isOpen', 'isActive', 'open', 'close', 'toggle', 'setOpen']\n  })], IonMenu);\n  return IonMenu;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonMenuButton = /*#__PURE__*/(() => {\n  let IonMenuButton = class IonMenuButton {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonMenuButton.ɵfac = function IonMenuButton_Factory(t) {\n    return new (t || IonMenuButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonMenuButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonMenuButton,\n    selectors: [[\"ion-menu-button\"]],\n    inputs: {\n      autoHide: \"autoHide\",\n      color: \"color\",\n      disabled: \"disabled\",\n      menu: \"menu\",\n      mode: \"mode\",\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonMenuButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonMenuButton = __decorate([ProxyCmp({\n    inputs: ['autoHide', 'color', 'disabled', 'menu', 'mode', 'type']\n  })], IonMenuButton);\n  return IonMenuButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonMenuToggle = /*#__PURE__*/(() => {\n  let IonMenuToggle = class IonMenuToggle {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonMenuToggle.ɵfac = function IonMenuToggle_Factory(t) {\n    return new (t || IonMenuToggle)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonMenuToggle.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonMenuToggle,\n    selectors: [[\"ion-menu-toggle\"]],\n    inputs: {\n      autoHide: \"autoHide\",\n      menu: \"menu\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonMenuToggle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonMenuToggle = __decorate([ProxyCmp({\n    inputs: ['autoHide', 'menu']\n  })], IonMenuToggle);\n  return IonMenuToggle;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonNavLink = /*#__PURE__*/(() => {\n  let IonNavLink = class IonNavLink {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonNavLink.ɵfac = function IonNavLink_Factory(t) {\n    return new (t || IonNavLink)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonNavLink.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonNavLink,\n    selectors: [[\"ion-nav-link\"]],\n    inputs: {\n      component: \"component\",\n      componentProps: \"componentProps\",\n      routerAnimation: \"routerAnimation\",\n      routerDirection: \"routerDirection\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonNavLink_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonNavLink = __decorate([ProxyCmp({\n    inputs: ['component', 'componentProps', 'routerAnimation', 'routerDirection']\n  })], IonNavLink);\n  return IonNavLink;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonNote = /*#__PURE__*/(() => {\n  let IonNote = class IonNote {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonNote.ɵfac = function IonNote_Factory(t) {\n    return new (t || IonNote)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonNote.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonNote,\n    selectors: [[\"ion-note\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonNote_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonNote = __decorate([ProxyCmp({\n    inputs: ['color', 'mode']\n  })], IonNote);\n  return IonNote;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonPicker = /*#__PURE__*/(() => {\n  let IonPicker = class IonPicker {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionPickerDidPresent', 'ionPickerWillPresent', 'ionPickerWillDismiss', 'ionPickerDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonPicker.ɵfac = function IonPicker_Factory(t) {\n    return new (t || IonPicker)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonPicker.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonPicker,\n    selectors: [[\"ion-picker\"]],\n    inputs: {\n      animated: \"animated\",\n      backdropDismiss: \"backdropDismiss\",\n      buttons: \"buttons\",\n      columns: \"columns\",\n      cssClass: \"cssClass\",\n      duration: \"duration\",\n      enterAnimation: \"enterAnimation\",\n      htmlAttributes: \"htmlAttributes\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      showBackdrop: \"showBackdrop\",\n      trigger: \"trigger\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonPicker_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonPicker = __decorate([ProxyCmp({\n    inputs: ['animated', 'backdropDismiss', 'buttons', 'columns', 'cssClass', 'duration', 'enterAnimation', 'htmlAttributes', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'showBackdrop', 'trigger'],\n    methods: ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss', 'getColumn']\n  })], IonPicker);\n  return IonPicker;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonProgressBar = /*#__PURE__*/(() => {\n  let IonProgressBar = class IonProgressBar {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonProgressBar.ɵfac = function IonProgressBar_Factory(t) {\n    return new (t || IonProgressBar)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonProgressBar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonProgressBar,\n    selectors: [[\"ion-progress-bar\"]],\n    inputs: {\n      buffer: \"buffer\",\n      color: \"color\",\n      mode: \"mode\",\n      reversed: \"reversed\",\n      type: \"type\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonProgressBar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonProgressBar = __decorate([ProxyCmp({\n    inputs: ['buffer', 'color', 'mode', 'reversed', 'type', 'value']\n  })], IonProgressBar);\n  return IonProgressBar;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonRadio = /*#__PURE__*/(() => {\n  let IonRadio = class IonRadio {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonRadio.ɵfac = function IonRadio_Factory(t) {\n    return new (t || IonRadio)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonRadio.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonRadio,\n    selectors: [[\"ion-radio\"]],\n    inputs: {\n      alignment: \"alignment\",\n      color: \"color\",\n      disabled: \"disabled\",\n      justify: \"justify\",\n      labelPlacement: \"labelPlacement\",\n      legacy: \"legacy\",\n      mode: \"mode\",\n      name: \"name\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonRadio_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonRadio = __decorate([ProxyCmp({\n    inputs: ['alignment', 'color', 'disabled', 'justify', 'labelPlacement', 'legacy', 'mode', 'name', 'value']\n  })], IonRadio);\n  return IonRadio;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonRadioGroup = /*#__PURE__*/(() => {\n  let IonRadioGroup = class IonRadioGroup {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonRadioGroup.ɵfac = function IonRadioGroup_Factory(t) {\n    return new (t || IonRadioGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonRadioGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonRadioGroup,\n    selectors: [[\"ion-radio-group\"]],\n    inputs: {\n      allowEmptySelection: \"allowEmptySelection\",\n      compareWith: \"compareWith\",\n      name: \"name\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonRadioGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonRadioGroup = __decorate([ProxyCmp({\n    inputs: ['allowEmptySelection', 'compareWith', 'name', 'value']\n  })], IonRadioGroup);\n  return IonRadioGroup;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonRange = /*#__PURE__*/(() => {\n  let IonRange = class IonRange {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange', 'ionInput', 'ionFocus', 'ionBlur', 'ionKnobMoveStart', 'ionKnobMoveEnd']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonRange.ɵfac = function IonRange_Factory(t) {\n    return new (t || IonRange)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonRange.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonRange,\n    selectors: [[\"ion-range\"]],\n    inputs: {\n      activeBarStart: \"activeBarStart\",\n      color: \"color\",\n      debounce: \"debounce\",\n      disabled: \"disabled\",\n      dualKnobs: \"dualKnobs\",\n      label: \"label\",\n      labelPlacement: \"labelPlacement\",\n      legacy: \"legacy\",\n      max: \"max\",\n      min: \"min\",\n      mode: \"mode\",\n      name: \"name\",\n      pin: \"pin\",\n      pinFormatter: \"pinFormatter\",\n      snaps: \"snaps\",\n      step: \"step\",\n      ticks: \"ticks\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonRange_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonRange = __decorate([ProxyCmp({\n    inputs: ['activeBarStart', 'color', 'debounce', 'disabled', 'dualKnobs', 'label', 'labelPlacement', 'legacy', 'max', 'min', 'mode', 'name', 'pin', 'pinFormatter', 'snaps', 'step', 'ticks', 'value']\n  })], IonRange);\n  return IonRange;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonRefresher = /*#__PURE__*/(() => {\n  let IonRefresher = class IonRefresher {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionRefresh', 'ionPull', 'ionStart']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonRefresher.ɵfac = function IonRefresher_Factory(t) {\n    return new (t || IonRefresher)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonRefresher.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonRefresher,\n    selectors: [[\"ion-refresher\"]],\n    inputs: {\n      closeDuration: \"closeDuration\",\n      disabled: \"disabled\",\n      mode: \"mode\",\n      pullFactor: \"pullFactor\",\n      pullMax: \"pullMax\",\n      pullMin: \"pullMin\",\n      snapbackDuration: \"snapbackDuration\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonRefresher_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonRefresher = __decorate([ProxyCmp({\n    inputs: ['closeDuration', 'disabled', 'mode', 'pullFactor', 'pullMax', 'pullMin', 'snapbackDuration'],\n    methods: ['complete', 'cancel', 'getProgress']\n  })], IonRefresher);\n  return IonRefresher;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonRefresherContent = /*#__PURE__*/(() => {\n  let IonRefresherContent = class IonRefresherContent {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonRefresherContent.ɵfac = function IonRefresherContent_Factory(t) {\n    return new (t || IonRefresherContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonRefresherContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonRefresherContent,\n    selectors: [[\"ion-refresher-content\"]],\n    inputs: {\n      pullingIcon: \"pullingIcon\",\n      pullingText: \"pullingText\",\n      refreshingSpinner: \"refreshingSpinner\",\n      refreshingText: \"refreshingText\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonRefresherContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonRefresherContent = __decorate([ProxyCmp({\n    inputs: ['pullingIcon', 'pullingText', 'refreshingSpinner', 'refreshingText']\n  })], IonRefresherContent);\n  return IonRefresherContent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonReorder = /*#__PURE__*/(() => {\n  let IonReorder = class IonReorder {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonReorder.ɵfac = function IonReorder_Factory(t) {\n    return new (t || IonReorder)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonReorder.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonReorder,\n    selectors: [[\"ion-reorder\"]],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonReorder_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonReorder = __decorate([ProxyCmp({})], IonReorder);\n  return IonReorder;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonReorderGroup = /*#__PURE__*/(() => {\n  let IonReorderGroup = class IonReorderGroup {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionItemReorder']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonReorderGroup.ɵfac = function IonReorderGroup_Factory(t) {\n    return new (t || IonReorderGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonReorderGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonReorderGroup,\n    selectors: [[\"ion-reorder-group\"]],\n    inputs: {\n      disabled: \"disabled\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonReorderGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonReorderGroup = __decorate([ProxyCmp({\n    inputs: ['disabled'],\n    methods: ['complete']\n  })], IonReorderGroup);\n  return IonReorderGroup;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonRippleEffect = /*#__PURE__*/(() => {\n  let IonRippleEffect = class IonRippleEffect {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonRippleEffect.ɵfac = function IonRippleEffect_Factory(t) {\n    return new (t || IonRippleEffect)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonRippleEffect.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonRippleEffect,\n    selectors: [[\"ion-ripple-effect\"]],\n    inputs: {\n      type: \"type\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonRippleEffect_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonRippleEffect = __decorate([ProxyCmp({\n    inputs: ['type'],\n    methods: ['addRipple']\n  })], IonRippleEffect);\n  return IonRippleEffect;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonRow = /*#__PURE__*/(() => {\n  let IonRow = class IonRow {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonRow.ɵfac = function IonRow_Factory(t) {\n    return new (t || IonRow)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonRow.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonRow,\n    selectors: [[\"ion-row\"]],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonRow = __decorate([ProxyCmp({})], IonRow);\n  return IonRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSearchbar = /*#__PURE__*/(() => {\n  let IonSearchbar = class IonSearchbar {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionInput', 'ionChange', 'ionCancel', 'ionClear', 'ionBlur', 'ionFocus']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSearchbar.ɵfac = function IonSearchbar_Factory(t) {\n    return new (t || IonSearchbar)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSearchbar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSearchbar,\n    selectors: [[\"ion-searchbar\"]],\n    inputs: {\n      animated: \"animated\",\n      autocapitalize: \"autocapitalize\",\n      autocomplete: \"autocomplete\",\n      autocorrect: \"autocorrect\",\n      cancelButtonIcon: \"cancelButtonIcon\",\n      cancelButtonText: \"cancelButtonText\",\n      clearIcon: \"clearIcon\",\n      color: \"color\",\n      debounce: \"debounce\",\n      disabled: \"disabled\",\n      enterkeyhint: \"enterkeyhint\",\n      inputmode: \"inputmode\",\n      maxlength: \"maxlength\",\n      minlength: \"minlength\",\n      mode: \"mode\",\n      name: \"name\",\n      placeholder: \"placeholder\",\n      searchIcon: \"searchIcon\",\n      showCancelButton: \"showCancelButton\",\n      showClearButton: \"showClearButton\",\n      spellcheck: \"spellcheck\",\n      type: \"type\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSearchbar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSearchbar = __decorate([ProxyCmp({\n    inputs: ['animated', 'autocapitalize', 'autocomplete', 'autocorrect', 'cancelButtonIcon', 'cancelButtonText', 'clearIcon', 'color', 'debounce', 'disabled', 'enterkeyhint', 'inputmode', 'maxlength', 'minlength', 'mode', 'name', 'placeholder', 'searchIcon', 'showCancelButton', 'showClearButton', 'spellcheck', 'type', 'value'],\n    methods: ['setFocus', 'getInputElement']\n  })], IonSearchbar);\n  return IonSearchbar;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSegment = /*#__PURE__*/(() => {\n  let IonSegment = class IonSegment {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSegment.ɵfac = function IonSegment_Factory(t) {\n    return new (t || IonSegment)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSegment.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSegment,\n    selectors: [[\"ion-segment\"]],\n    inputs: {\n      color: \"color\",\n      disabled: \"disabled\",\n      mode: \"mode\",\n      scrollable: \"scrollable\",\n      selectOnFocus: \"selectOnFocus\",\n      swipeGesture: \"swipeGesture\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSegment_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSegment = __decorate([ProxyCmp({\n    inputs: ['color', 'disabled', 'mode', 'scrollable', 'selectOnFocus', 'swipeGesture', 'value']\n  })], IonSegment);\n  return IonSegment;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSegmentButton = /*#__PURE__*/(() => {\n  let IonSegmentButton = class IonSegmentButton {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSegmentButton.ɵfac = function IonSegmentButton_Factory(t) {\n    return new (t || IonSegmentButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSegmentButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSegmentButton,\n    selectors: [[\"ion-segment-button\"]],\n    inputs: {\n      disabled: \"disabled\",\n      layout: \"layout\",\n      mode: \"mode\",\n      type: \"type\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSegmentButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSegmentButton = __decorate([ProxyCmp({\n    inputs: ['disabled', 'layout', 'mode', 'type', 'value']\n  })], IonSegmentButton);\n  return IonSegmentButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSelect = /*#__PURE__*/(() => {\n  let IonSelect = class IonSelect {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange', 'ionCancel', 'ionDismiss', 'ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSelect.ɵfac = function IonSelect_Factory(t) {\n    return new (t || IonSelect)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSelect.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSelect,\n    selectors: [[\"ion-select\"]],\n    inputs: {\n      cancelText: \"cancelText\",\n      color: \"color\",\n      compareWith: \"compareWith\",\n      disabled: \"disabled\",\n      expandedIcon: \"expandedIcon\",\n      fill: \"fill\",\n      interface: \"interface\",\n      interfaceOptions: \"interfaceOptions\",\n      justify: \"justify\",\n      label: \"label\",\n      labelPlacement: \"labelPlacement\",\n      legacy: \"legacy\",\n      mode: \"mode\",\n      multiple: \"multiple\",\n      name: \"name\",\n      okText: \"okText\",\n      placeholder: \"placeholder\",\n      selectedText: \"selectedText\",\n      shape: \"shape\",\n      toggleIcon: \"toggleIcon\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSelect_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSelect = __decorate([ProxyCmp({\n    inputs: ['cancelText', 'color', 'compareWith', 'disabled', 'expandedIcon', 'fill', 'interface', 'interfaceOptions', 'justify', 'label', 'labelPlacement', 'legacy', 'mode', 'multiple', 'name', 'okText', 'placeholder', 'selectedText', 'shape', 'toggleIcon', 'value'],\n    methods: ['open']\n  })], IonSelect);\n  return IonSelect;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSelectOption = /*#__PURE__*/(() => {\n  let IonSelectOption = class IonSelectOption {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSelectOption.ɵfac = function IonSelectOption_Factory(t) {\n    return new (t || IonSelectOption)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSelectOption.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSelectOption,\n    selectors: [[\"ion-select-option\"]],\n    inputs: {\n      disabled: \"disabled\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSelectOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSelectOption = __decorate([ProxyCmp({\n    inputs: ['disabled', 'value']\n  })], IonSelectOption);\n  return IonSelectOption;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSkeletonText = /*#__PURE__*/(() => {\n  let IonSkeletonText = class IonSkeletonText {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSkeletonText.ɵfac = function IonSkeletonText_Factory(t) {\n    return new (t || IonSkeletonText)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSkeletonText.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSkeletonText,\n    selectors: [[\"ion-skeleton-text\"]],\n    inputs: {\n      animated: \"animated\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSkeletonText_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSkeletonText = __decorate([ProxyCmp({\n    inputs: ['animated']\n  })], IonSkeletonText);\n  return IonSkeletonText;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSpinner = /*#__PURE__*/(() => {\n  let IonSpinner = class IonSpinner {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSpinner.ɵfac = function IonSpinner_Factory(t) {\n    return new (t || IonSpinner)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSpinner.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSpinner,\n    selectors: [[\"ion-spinner\"]],\n    inputs: {\n      color: \"color\",\n      duration: \"duration\",\n      name: \"name\",\n      paused: \"paused\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSpinner = __decorate([ProxyCmp({\n    inputs: ['color', 'duration', 'name', 'paused']\n  })], IonSpinner);\n  return IonSpinner;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonSplitPane = /*#__PURE__*/(() => {\n  let IonSplitPane = class IonSplitPane {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionSplitPaneVisible']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonSplitPane.ɵfac = function IonSplitPane_Factory(t) {\n    return new (t || IonSplitPane)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonSplitPane.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonSplitPane,\n    selectors: [[\"ion-split-pane\"]],\n    inputs: {\n      contentId: \"contentId\",\n      disabled: \"disabled\",\n      when: \"when\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonSplitPane_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonSplitPane = __decorate([ProxyCmp({\n    inputs: ['contentId', 'disabled', 'when']\n  })], IonSplitPane);\n  return IonSplitPane;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonTabBar = /*#__PURE__*/(() => {\n  let IonTabBar = class IonTabBar {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonTabBar.ɵfac = function IonTabBar_Factory(t) {\n    return new (t || IonTabBar)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonTabBar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonTabBar,\n    selectors: [[\"ion-tab-bar\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      selectedTab: \"selectedTab\",\n      translucent: \"translucent\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonTabBar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonTabBar = __decorate([ProxyCmp({\n    inputs: ['color', 'mode', 'selectedTab', 'translucent']\n  })], IonTabBar);\n  return IonTabBar;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonTabButton = /*#__PURE__*/(() => {\n  let IonTabButton = class IonTabButton {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonTabButton.ɵfac = function IonTabButton_Factory(t) {\n    return new (t || IonTabButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonTabButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonTabButton,\n    selectors: [[\"ion-tab-button\"]],\n    inputs: {\n      disabled: \"disabled\",\n      download: \"download\",\n      href: \"href\",\n      layout: \"layout\",\n      mode: \"mode\",\n      rel: \"rel\",\n      selected: \"selected\",\n      tab: \"tab\",\n      target: \"target\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonTabButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonTabButton = __decorate([ProxyCmp({\n    inputs: ['disabled', 'download', 'href', 'layout', 'mode', 'rel', 'selected', 'tab', 'target']\n  })], IonTabButton);\n  return IonTabButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonText = /*#__PURE__*/(() => {\n  let IonText = class IonText {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonText.ɵfac = function IonText_Factory(t) {\n    return new (t || IonText)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonText.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonText,\n    selectors: [[\"ion-text\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonText_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonText = __decorate([ProxyCmp({\n    inputs: ['color', 'mode']\n  })], IonText);\n  return IonText;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonTextarea = /*#__PURE__*/(() => {\n  let IonTextarea = class IonTextarea {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange', 'ionInput', 'ionBlur', 'ionFocus']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonTextarea.ɵfac = function IonTextarea_Factory(t) {\n    return new (t || IonTextarea)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonTextarea.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonTextarea,\n    selectors: [[\"ion-textarea\"]],\n    inputs: {\n      autoGrow: \"autoGrow\",\n      autocapitalize: \"autocapitalize\",\n      autofocus: \"autofocus\",\n      clearOnEdit: \"clearOnEdit\",\n      color: \"color\",\n      cols: \"cols\",\n      counter: \"counter\",\n      counterFormatter: \"counterFormatter\",\n      debounce: \"debounce\",\n      disabled: \"disabled\",\n      enterkeyhint: \"enterkeyhint\",\n      errorText: \"errorText\",\n      fill: \"fill\",\n      helperText: \"helperText\",\n      inputmode: \"inputmode\",\n      label: \"label\",\n      labelPlacement: \"labelPlacement\",\n      legacy: \"legacy\",\n      maxlength: \"maxlength\",\n      minlength: \"minlength\",\n      mode: \"mode\",\n      name: \"name\",\n      placeholder: \"placeholder\",\n      readonly: \"readonly\",\n      required: \"required\",\n      rows: \"rows\",\n      shape: \"shape\",\n      spellcheck: \"spellcheck\",\n      value: \"value\",\n      wrap: \"wrap\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonTextarea_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonTextarea = __decorate([ProxyCmp({\n    inputs: ['autoGrow', 'autocapitalize', 'autofocus', 'clearOnEdit', 'color', 'cols', 'counter', 'counterFormatter', 'debounce', 'disabled', 'enterkeyhint', 'errorText', 'fill', 'helperText', 'inputmode', 'label', 'labelPlacement', 'legacy', 'maxlength', 'minlength', 'mode', 'name', 'placeholder', 'readonly', 'required', 'rows', 'shape', 'spellcheck', 'value', 'wrap'],\n    methods: ['setFocus', 'getInputElement']\n  })], IonTextarea);\n  return IonTextarea;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonThumbnail = /*#__PURE__*/(() => {\n  let IonThumbnail = class IonThumbnail {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonThumbnail.ɵfac = function IonThumbnail_Factory(t) {\n    return new (t || IonThumbnail)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonThumbnail.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonThumbnail,\n    selectors: [[\"ion-thumbnail\"]],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonThumbnail_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonThumbnail = __decorate([ProxyCmp({})], IonThumbnail);\n  return IonThumbnail;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonTitle = /*#__PURE__*/(() => {\n  let IonTitle = class IonTitle {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonTitle.ɵfac = function IonTitle_Factory(t) {\n    return new (t || IonTitle)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonTitle.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonTitle,\n    selectors: [[\"ion-title\"]],\n    inputs: {\n      color: \"color\",\n      size: \"size\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonTitle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonTitle = __decorate([ProxyCmp({\n    inputs: ['color', 'size']\n  })], IonTitle);\n  return IonTitle;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonToast = /*#__PURE__*/(() => {\n  let IonToast = class IonToast {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionToastDidPresent', 'ionToastWillPresent', 'ionToastWillDismiss', 'ionToastDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonToast.ɵfac = function IonToast_Factory(t) {\n    return new (t || IonToast)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonToast.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonToast,\n    selectors: [[\"ion-toast\"]],\n    inputs: {\n      animated: \"animated\",\n      buttons: \"buttons\",\n      color: \"color\",\n      cssClass: \"cssClass\",\n      duration: \"duration\",\n      enterAnimation: \"enterAnimation\",\n      header: \"header\",\n      htmlAttributes: \"htmlAttributes\",\n      icon: \"icon\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      layout: \"layout\",\n      leaveAnimation: \"leaveAnimation\",\n      message: \"message\",\n      mode: \"mode\",\n      position: \"position\",\n      positionAnchor: \"positionAnchor\",\n      swipeGesture: \"swipeGesture\",\n      translucent: \"translucent\",\n      trigger: \"trigger\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonToast_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonToast = __decorate([ProxyCmp({\n    inputs: ['animated', 'buttons', 'color', 'cssClass', 'duration', 'enterAnimation', 'header', 'htmlAttributes', 'icon', 'isOpen', 'keyboardClose', 'layout', 'leaveAnimation', 'message', 'mode', 'position', 'positionAnchor', 'swipeGesture', 'translucent', 'trigger'],\n    methods: ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss']\n  })], IonToast);\n  return IonToast;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonToggle = /*#__PURE__*/(() => {\n  let IonToggle = class IonToggle {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n      proxyOutputs(this, this.el, ['ionChange', 'ionFocus', 'ionBlur']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonToggle.ɵfac = function IonToggle_Factory(t) {\n    return new (t || IonToggle)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonToggle.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonToggle,\n    selectors: [[\"ion-toggle\"]],\n    inputs: {\n      alignment: \"alignment\",\n      checked: \"checked\",\n      color: \"color\",\n      disabled: \"disabled\",\n      enableOnOffLabels: \"enableOnOffLabels\",\n      justify: \"justify\",\n      labelPlacement: \"labelPlacement\",\n      legacy: \"legacy\",\n      mode: \"mode\",\n      name: \"name\",\n      value: \"value\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonToggle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonToggle = __decorate([ProxyCmp({\n    inputs: ['alignment', 'checked', 'color', 'disabled', 'enableOnOffLabels', 'justify', 'labelPlacement', 'legacy', 'mode', 'name', 'value']\n  })], IonToggle);\n  return IonToggle;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonToolbar = /*#__PURE__*/(() => {\n  let IonToolbar = class IonToolbar {\n    constructor(c, r, z) {\n      this.z = z;\n      c.detach();\n      this.el = r.nativeElement;\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonToolbar.ɵfac = function IonToolbar_Factory(t) {\n    return new (t || IonToolbar)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonToolbar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonToolbar,\n    selectors: [[\"ion-toolbar\"]],\n    inputs: {\n      color: \"color\",\n      mode: \"mode\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonToolbar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  IonToolbar = __decorate([ProxyCmp({\n    inputs: ['color', 'mode']\n  })], IonToolbar);\n  return IonToolbar;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet IonRouterOutlet = /*#__PURE__*/(() => {\n  class IonRouterOutlet extends IonRouterOutlet$1 {\n    /**\n     * We need to pass in the correct instance of IonRouterOutlet\n     * otherwise parentOutlet will be null in a nested outlet context.\n     * This results in APIs such as NavController.pop not working\n     * in nested outlets because the parent outlet cannot be found.\n     */\n    constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n      super(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet);\n      this.parentOutlet = parentOutlet;\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */IonRouterOutlet.ɵfac = function IonRouterOutlet_Factory(t) {\n    return new (t || IonRouterOutlet)(i0.ɵɵinjectAttribute('name'), i0.ɵɵinjectAttribute('tabs'), i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(IonRouterOutlet, 12));\n  };\n  IonRouterOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonRouterOutlet,\n    selectors: [[\"ion-router-outlet\"]],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return IonRouterOutlet;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// eslint-disable-next-line @angular-eslint/component-class-suffix\nlet IonTabs = /*#__PURE__*/(() => {\n  class IonTabs extends IonTabs$1 {}\n  /** @nocollapse */\n  /** @nocollapse */IonTabs.ɵfac = /* @__PURE__ */(() => {\n    let ɵIonTabs_BaseFactory;\n    return function IonTabs_Factory(t) {\n      return (ɵIonTabs_BaseFactory || (ɵIonTabs_BaseFactory = i0.ɵɵgetInheritedFactory(IonTabs)))(t || IonTabs);\n    };\n  })();\n  IonTabs.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonTabs,\n    selectors: [[\"ion-tabs\"]],\n    contentQueries: function IonTabs_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, IonTabBar, 5);\n        i0.ɵɵcontentQuery(dirIndex, IonTabBar, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabBar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabBars = _t);\n      }\n    },\n    viewQuery: function IonTabs_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 5, IonRouterOutlet);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.outlet = _t.first);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c3,\n    decls: 6,\n    vars: 0,\n    consts: [[\"tabsInner\", \"\"], [\"outlet\", \"\"], [1, \"tabs-inner\"], [\"tabs\", \"true\", 3, \"stackWillChange\", \"stackDidChange\"]],\n    template: function IonTabs_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"div\", 2, 0)(3, \"ion-router-outlet\", 3, 1);\n        i0.ɵɵlistener(\"stackWillChange\", function IonTabs_Template_ion_router_outlet_stackWillChange_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onStackWillChange($event));\n        })(\"stackDidChange\", function IonTabs_Template_ion_router_outlet_stackDidChange_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onStackDidChange($event));\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵprojection(5, 1);\n      }\n    },\n    dependencies: [IonRouterOutlet],\n    styles: [\"[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}\"]\n  });\n  return IonTabs;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet IonBackButton = /*#__PURE__*/(() => {\n  class IonBackButton extends IonBackButton$1 {\n    constructor(routerOutlet, navCtrl, config, r, z, c) {\n      super(routerOutlet, navCtrl, config, r, z, c);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */IonBackButton.ɵfac = function IonBackButton_Factory(t) {\n    return new (t || IonBackButton)(i0.ɵɵdirectiveInject(IonRouterOutlet, 8), i0.ɵɵdirectiveInject(i2$1.NavController), i0.ɵɵdirectiveInject(i2$1.Config), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  IonBackButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonBackButton,\n    selectors: [[\"ion-back-button\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonBackButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return IonBackButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet IonNav = /*#__PURE__*/(() => {\n  class IonNav extends IonNav$1 {\n    constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n      super(ref, environmentInjector, injector, angularDelegate, z, c);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */IonNav.ɵfac = function IonNav_Factory(t) {\n    return new (t || IonNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.EnvironmentInjector), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i2$1.AngularDelegate), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  IonNav.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonNav,\n    selectors: [[\"ion-nav\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function IonNav_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return IonNav;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nlet RouterLinkDelegateDirective = /*#__PURE__*/(() => {\n  class RouterLinkDelegateDirective extends RouterLinkDelegateDirective$1 {}\n  /** @nocollapse */\n  /** @nocollapse */RouterLinkDelegateDirective.ɵfac = /* @__PURE__ */(() => {\n    let ɵRouterLinkDelegateDirective_BaseFactory;\n    return function RouterLinkDelegateDirective_Factory(t) {\n      return (ɵRouterLinkDelegateDirective_BaseFactory || (ɵRouterLinkDelegateDirective_BaseFactory = i0.ɵɵgetInheritedFactory(RouterLinkDelegateDirective)))(t || RouterLinkDelegateDirective);\n    };\n  })();\n  RouterLinkDelegateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkDelegateDirective,\n    selectors: [[\"\", \"routerLink\", \"\", 5, \"a\", 5, \"area\"]],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return RouterLinkDelegateDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RouterLinkWithHrefDelegateDirective = /*#__PURE__*/(() => {\n  class RouterLinkWithHrefDelegateDirective extends RouterLinkWithHrefDelegateDirective$1 {}\n  /** @nocollapse */\n  /** @nocollapse */RouterLinkWithHrefDelegateDirective.ɵfac = /* @__PURE__ */(() => {\n    let ɵRouterLinkWithHrefDelegateDirective_BaseFactory;\n    return function RouterLinkWithHrefDelegateDirective_Factory(t) {\n      return (ɵRouterLinkWithHrefDelegateDirective_BaseFactory || (ɵRouterLinkWithHrefDelegateDirective_BaseFactory = i0.ɵɵgetInheritedFactory(RouterLinkWithHrefDelegateDirective)))(t || RouterLinkWithHrefDelegateDirective);\n    };\n  })();\n  RouterLinkWithHrefDelegateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkWithHrefDelegateDirective,\n    selectors: [[\"a\", \"routerLink\", \"\"], [\"area\", \"routerLink\", \"\"]],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return RouterLinkWithHrefDelegateDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonModal = /*#__PURE__*/(() => {\n  class IonModal extends IonModal$1 {}\n  /** @nocollapse */\n  /** @nocollapse */IonModal.ɵfac = /* @__PURE__ */(() => {\n    let ɵIonModal_BaseFactory;\n    return function IonModal_Factory(t) {\n      return (ɵIonModal_BaseFactory || (ɵIonModal_BaseFactory = i0.ɵɵgetInheritedFactory(IonModal)))(t || IonModal);\n    };\n  })();\n  IonModal.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonModal,\n    selectors: [[\"ion-modal\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"ion-delegate-host ion-page\", 4, \"ngIf\"], [1, \"ion-delegate-host\", \"ion-page\"], [3, \"ngTemplateOutlet\"]],\n    template: function IonModal_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, IonModal_div_0_Template, 2, 1, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isCmpOpen || ctx.keepContentsMounted);\n      }\n    },\n    dependencies: [i1.NgIf, i1.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return IonModal;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet IonPopover = /*#__PURE__*/(() => {\n  class IonPopover extends IonPopover$1 {}\n  /** @nocollapse */\n  /** @nocollapse */IonPopover.ɵfac = /* @__PURE__ */(() => {\n    let ɵIonPopover_BaseFactory;\n    return function IonPopover_Factory(t) {\n      return (ɵIonPopover_BaseFactory || (ɵIonPopover_BaseFactory = i0.ɵɵgetInheritedFactory(IonPopover)))(t || IonPopover);\n    };\n  })();\n  IonPopover.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IonPopover,\n    selectors: [[\"ion-popover\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"ngTemplateOutlet\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"]],\n    template: function IonPopover_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, IonPopover_ng_container_0_Template, 1, 1, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isCmpOpen || ctx.keepContentsMounted);\n      }\n    },\n    dependencies: [i1.NgIf, i1.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return IonPopover;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @description\n * Provider which adds `MaxValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst ION_MAX_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => IonMaxValidator),\n  multi: true\n};\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet IonMaxValidator = /*#__PURE__*/(() => {\n  class IonMaxValidator extends MaxValidator {}\n  /** @nocollapse */\n  /** @nocollapse */IonMaxValidator.ɵfac = /* @__PURE__ */(() => {\n    let ɵIonMaxValidator_BaseFactory;\n    return function IonMaxValidator_Factory(t) {\n      return (ɵIonMaxValidator_BaseFactory || (ɵIonMaxValidator_BaseFactory = i0.ɵɵgetInheritedFactory(IonMaxValidator)))(t || IonMaxValidator);\n    };\n  })();\n  IonMaxValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonMaxValidator,\n    selectors: [[\"ion-input\", \"type\", \"number\", \"max\", \"\", \"formControlName\", \"\"], [\"ion-input\", \"type\", \"number\", \"max\", \"\", \"formControl\", \"\"], [\"ion-input\", \"type\", \"number\", \"max\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function IonMaxValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"max\", ctx._enabled ? ctx.max : null);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([ION_MAX_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return IonMaxValidator;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @description\n * Provider which adds `MinValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst ION_MIN_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => IonMinValidator),\n  multi: true\n};\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet IonMinValidator = /*#__PURE__*/(() => {\n  class IonMinValidator extends MinValidator {}\n  /** @nocollapse */\n  /** @nocollapse */IonMinValidator.ɵfac = /* @__PURE__ */(() => {\n    let ɵIonMinValidator_BaseFactory;\n    return function IonMinValidator_Factory(t) {\n      return (ɵIonMinValidator_BaseFactory || (ɵIonMinValidator_BaseFactory = i0.ɵɵgetInheritedFactory(IonMinValidator)))(t || IonMinValidator);\n    };\n  })();\n  IonMinValidator.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonMinValidator,\n    selectors: [[\"ion-input\", \"type\", \"number\", \"min\", \"\", \"formControlName\", \"\"], [\"ion-input\", \"type\", \"number\", \"min\", \"\", \"formControl\", \"\"], [\"ion-input\", \"type\", \"number\", \"min\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function IonMinValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"min\", ctx._enabled ? ctx.min : null);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([ION_MIN_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return IonMinValidator;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AlertController = /*#__PURE__*/(() => {\n  class AlertController extends OverlayBaseController {\n    constructor() {\n      super(alertController);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */AlertController.ɵfac = function AlertController_Factory(t) {\n    return new (t || AlertController)();\n  };\n  AlertController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AlertController,\n    factory: AlertController.ɵfac,\n    providedIn: 'root'\n  });\n  return AlertController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AnimationController = /*#__PURE__*/(() => {\n  class AnimationController {\n    /**\n     * Create a new animation\n     */\n    create(animationId) {\n      return createAnimation(animationId);\n    }\n    /**\n     * EXPERIMENTAL\n     *\n     * Given a progression and a cubic bezier function,\n     * this utility returns the time value(s) at which the\n     * cubic bezier reaches the given time progression.\n     *\n     * If the cubic bezier never reaches the progression\n     * the result will be an empty array.\n     *\n     * This is most useful for switching between easing curves\n     * when doing a gesture animation (i.e. going from linear easing\n     * during a drag, to another easing when `progressEnd` is called)\n     */\n    easingTime(p0, p1, p2, p3, progression) {\n      return getTimeGivenProgression(p0, p1, p2, p3, progression);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */AnimationController.ɵfac = function AnimationController_Factory(t) {\n    return new (t || AnimationController)();\n  };\n  AnimationController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AnimationController,\n    factory: AnimationController.ɵfac,\n    providedIn: 'root'\n  });\n  return AnimationController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ActionSheetController = /*#__PURE__*/(() => {\n  class ActionSheetController extends OverlayBaseController {\n    constructor() {\n      super(actionSheetController);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */ActionSheetController.ɵfac = function ActionSheetController_Factory(t) {\n    return new (t || ActionSheetController)();\n  };\n  ActionSheetController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ActionSheetController,\n    factory: ActionSheetController.ɵfac,\n    providedIn: 'root'\n  });\n  return ActionSheetController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet GestureController = /*#__PURE__*/(() => {\n  class GestureController {\n    constructor(zone) {\n      this.zone = zone;\n    }\n    /**\n     * Create a new gesture\n     */\n    create(opts, runInsideAngularZone = false) {\n      if (runInsideAngularZone) {\n        Object.getOwnPropertyNames(opts).forEach(key => {\n          if (typeof opts[key] === 'function') {\n            const fn = opts[key];\n            opts[key] = (...props) => this.zone.run(() => fn(...props));\n          }\n        });\n      }\n      return createGesture(opts);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */GestureController.ɵfac = function GestureController_Factory(t) {\n    return new (t || GestureController)(i0.ɵɵinject(i0.NgZone));\n  };\n  GestureController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: GestureController,\n    factory: GestureController.ɵfac,\n    providedIn: 'root'\n  });\n  return GestureController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet LoadingController = /*#__PURE__*/(() => {\n  class LoadingController extends OverlayBaseController {\n    constructor() {\n      super(loadingController);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */LoadingController.ɵfac = function LoadingController_Factory(t) {\n    return new (t || LoadingController)();\n  };\n  LoadingController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LoadingController,\n    factory: LoadingController.ɵfac,\n    providedIn: 'root'\n  });\n  return LoadingController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MenuController = /*#__PURE__*/(() => {\n  class MenuController extends MenuController$1 {\n    constructor() {\n      super(menuController);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */MenuController.ɵfac = function MenuController_Factory(t) {\n    return new (t || MenuController)();\n  };\n  MenuController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MenuController,\n    factory: MenuController.ɵfac,\n    providedIn: 'root'\n  });\n  return MenuController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ModalController = /*#__PURE__*/(() => {\n  class ModalController extends OverlayBaseController {\n    constructor() {\n      super(modalController);\n      this.angularDelegate = inject(AngularDelegate);\n      this.injector = inject(Injector);\n      this.environmentInjector = inject(EnvironmentInjector);\n    }\n    create(opts) {\n      return super.create({\n        ...opts,\n        delegate: this.angularDelegate.create(this.environmentInjector, this.injector, 'modal')\n      });\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */ModalController.ɵfac = function ModalController_Factory(t) {\n    return new (t || ModalController)();\n  };\n  ModalController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ModalController,\n    factory: ModalController.ɵfac\n  });\n  return ModalController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet PickerController = /*#__PURE__*/(() => {\n  class PickerController extends OverlayBaseController {\n    constructor() {\n      super(pickerController);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */PickerController.ɵfac = function PickerController_Factory(t) {\n    return new (t || PickerController)();\n  };\n  PickerController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PickerController,\n    factory: PickerController.ɵfac,\n    providedIn: 'root'\n  });\n  return PickerController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass PopoverController extends OverlayBaseController {\n  constructor() {\n    super(popoverController);\n    this.angularDelegate = inject(AngularDelegate);\n    this.injector = inject(Injector);\n    this.environmentInjector = inject(EnvironmentInjector);\n  }\n  create(opts) {\n    return super.create({\n      ...opts,\n      delegate: this.angularDelegate.create(this.environmentInjector, this.injector, 'popover')\n    });\n  }\n}\nlet ToastController = /*#__PURE__*/(() => {\n  class ToastController extends OverlayBaseController {\n    constructor() {\n      super(toastController);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */ToastController.ɵfac = function ToastController_Factory(t) {\n    return new (t || ToastController)();\n  };\n  ToastController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ToastController,\n    factory: ToastController.ɵfac,\n    providedIn: 'root'\n  });\n  return ToastController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// TODO(FW-2827): types\nconst appInitialize = (config, doc, zone) => {\n  return () => {\n    const win = doc.defaultView;\n    if (win && typeof window !== 'undefined') {\n      setupConfig({\n        ...config,\n        _zoneGate: h => zone.run(h)\n      });\n      const aelFn = '__zone_symbol__addEventListener' in doc.body ? '__zone_symbol__addEventListener' : 'addEventListener';\n      return applyPolyfills().then(() => {\n        return defineCustomElements(win, {\n          exclude: ['ion-tabs', 'ion-tab'],\n          syncQueue: true,\n          raf,\n          jmp: h => zone.runOutsideAngular(h),\n          ael(elm, eventName, cb, opts) {\n            elm[aelFn](eventName, cb, opts);\n          },\n          rel(elm, eventName, cb, opts) {\n            elm.removeEventListener(eventName, cb, opts);\n          }\n        });\n      });\n    }\n  };\n};\nconst DIRECTIVES = [IonAccordion, IonAccordionGroup, IonActionSheet, IonAlert, IonApp, IonAvatar, IonBackdrop, IonBadge, IonBreadcrumb, IonBreadcrumbs, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonCheckbox, IonChip, IonCol, IonContent, IonDatetime, IonDatetimeButton, IonFab, IonFabButton, IonFabList, IonFooter, IonGrid, IonHeader, IonIcon, IonImg, IonInfiniteScroll, IonInfiniteScrollContent, IonInput, IonItem, IonItemDivider, IonItemGroup, IonItemOption, IonItemOptions, IonItemSliding, IonLabel, IonList, IonListHeader, IonLoading, IonMenu, IonMenuButton, IonMenuToggle, IonNavLink, IonNote, IonPicker, IonProgressBar, IonRadio, IonRadioGroup, IonRange, IonRefresher, IonRefresherContent, IonReorder, IonReorderGroup, IonRippleEffect, IonRow, IonSearchbar, IonSegment, IonSegmentButton, IonSelect, IonSelectOption, IonSkeletonText, IonSpinner, IonSplitPane, IonTabBar, IonTabButton, IonText, IonTextarea, IonThumbnail, IonTitle, IonToast, IonToggle, IonToolbar];\nconst DECLARATIONS = [\n// generated proxies\n...DIRECTIVES,\n// manual proxies\nIonModal, IonPopover,\n// ngModel accessors\nBooleanValueAccessorDirective, NumericValueAccessorDirective, RadioValueAccessorDirective, SelectValueAccessorDirective, TextValueAccessorDirective,\n// navigation\nIonTabs, IonRouterOutlet, IonBackButton, IonNav, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective,\n// validators\nIonMinValidator, IonMaxValidator];\nlet IonicModule = /*#__PURE__*/(() => {\n  class IonicModule {\n    static forRoot(config) {\n      return {\n        ngModule: IonicModule,\n        providers: [{\n          provide: ConfigToken,\n          useValue: config\n        }, {\n          provide: APP_INITIALIZER,\n          useFactory: appInitialize,\n          multi: true,\n          deps: [ConfigToken, DOCUMENT, NgZone]\n        }, provideComponentInputBinding()]\n      };\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */\n\n  /** @nocollapse */IonicModule.ɵfac = function IonicModule_Factory(t) {\n    return new (t || IonicModule)();\n  };\n  IonicModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: IonicModule\n  });\n  IonicModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [AngularDelegate, ModalController, PopoverController],\n    imports: [CommonModule]\n  });\n  return IonicModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// DIRECTIVES\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ActionSheetController, AlertController, AnimationController, BooleanValueAccessorDirective as BooleanValueAccessor, GestureController, ION_MAX_VALIDATOR, ION_MIN_VALIDATOR, IonAccordion, IonAccordionGroup, IonActionSheet, IonAlert, IonApp, IonAvatar, IonBackButton, IonBackButton as IonBackButtonDelegate, IonBackdrop, IonBadge, IonBreadcrumb, IonBreadcrumbs, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonCheckbox, IonChip, IonCol, IonContent, IonDatetime, IonDatetimeButton, IonFab, IonFabButton, IonFabList, IonFooter, IonGrid, IonHeader, IonIcon, IonImg, IonInfiniteScroll, IonInfiniteScrollContent, IonInput, IonItem, IonItemDivider, IonItemGroup, IonItemOption, IonItemOptions, IonItemSliding, IonLabel, IonList, IonListHeader, IonLoading, IonMaxValidator, IonMenu, IonMenuButton, IonMenuToggle, IonMinValidator, IonModal, IonNav, IonNavLink, IonNote, IonPicker, IonPopover, IonProgressBar, IonRadio, IonRadioGroup, IonRange, IonRefresher, IonRefresherContent, IonReorder, IonReorderGroup, IonRippleEffect, IonRouterOutlet, IonRow, IonSearchbar, IonSegment, IonSegmentButton, IonSelect, IonSelectOption, IonSkeletonText, IonSpinner, IonSplitPane, IonTabBar, IonTabButton, IonTabs, IonText, IonTextarea, IonThumbnail, IonTitle, IonToast, IonToggle, IonToolbar, IonicModule, LoadingController, MenuController, ModalController, NumericValueAccessorDirective as NumericValueAccessor, PickerController, PopoverController, RadioValueAccessorDirective as RadioValueAccessor, RouterLinkDelegateDirective as RouterLinkDelegate, RouterLinkWithHrefDelegateDirective as RouterLinkWithHrefDelegate, SelectValueAccessorDirective as SelectValueAccessor, TextValueAccessorDirective as TextValueAccessor, ToastController };\n//# sourceMappingURL=ionic-angular.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}