import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { BillService, BillData } from '../../../core/services/bill.service';

@Component({
  selector: 'app-payment-success',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="payment-success-container">
      <div class="success-card">
        <!-- Success Icon -->
        <div class="success-icon">
          <div class="checkmark-circle">
            <div class="checkmark"></div>
          </div>
        </div>

        <!-- Success Message -->
        <div class="success-content">
          <h1>Payment Successful!</h1>
          <p class="success-message">
            Thank you for your purchase. Your order has been confirmed and you will receive an email confirmation shortly.
          </p>

          <!-- Order Details -->
          <div class="order-details" *ngIf="orderId">
            <h3>Order Details</h3>
            <div class="detail-row">
              <span class="label">Order ID:</span>
              <span class="value">{{ orderId }}</span>
            </div>
            <div class="detail-row" *ngIf="paymentMethod">
              <span class="label">Payment Method:</span>
              <span class="value">{{ getPaymentMethodName(paymentMethod) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Order Date:</span>
              <span class="value">{{ orderDate | date:'medium' }}</span>
            </div>
          </div>

          <!-- Email Notification -->
          <div class="email-notification">
            <i class="fas fa-envelope"></i>
            <p>A detailed invoice has been sent to your email address.</p>
          </div>

          <!-- Download Bill Button -->
          <div class="action-buttons">
            <button 
              class="btn btn-primary download-btn" 
              (click)="downloadBill()"
              [disabled]="isGeneratingBill"
            >
              <i class="fas fa-download"></i>
              {{ isGeneratingBill ? 'Generating...' : 'Download Invoice' }}
            </button>
            
            <button class="btn btn-secondary" (click)="viewOrder()">
              <i class="fas fa-eye"></i>
              View Order
            </button>
            
            <button class="btn btn-outline" (click)="continueShopping()">
              <i class="fas fa-shopping-bag"></i>
              Continue Shopping
            </button>
          </div>

          <!-- Additional Information -->
          <div class="additional-info">
            <div class="info-item">
              <i class="fas fa-truck"></i>
              <div>
                <h4>Shipping Information</h4>
                <p>Your order will be shipped within 2-3 business days. You'll receive tracking information via email.</p>
              </div>
            </div>
            
            <div class="info-item">
              <i class="fas fa-headset"></i>
              <div>
                <h4>Customer Support</h4>
                <p>Need help? Contact our support team at <a href="mailto:support&#64;dfashion.com">support&#64;dfashion.com</a></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Products or Recommendations -->
      <div class="recommendations" *ngIf="recommendedProducts.length > 0">
        <h3>You might also like</h3>
        <div class="products-grid">
          <div *ngFor="let product of recommendedProducts" class="product-card" (click)="viewProduct(product)">
            <img [src]="product.image" [alt]="product.name">
            <h4>{{ product.name }}</h4>
            <p class="price">{{ formatPrice(product.price) }}</p>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./payment-success.component.scss']
})
export class PaymentSuccessComponent implements OnInit {
  orderId: string = '';
  paymentMethod: string = '';
  orderDate: Date = new Date();
  isGeneratingBill = false;
  recommendedProducts: any[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private billService: BillService
  ) {}

  ngOnInit() {
    // Get order details from query parameters
    this.route.queryParams.subscribe(params => {
      this.orderId = params['orderId'] || '';
      this.paymentMethod = params['method'] || '';
    });

    this.loadRecommendedProducts();
  }

  loadRecommendedProducts() {
    // Mock recommended products - in real app, this would come from API
    this.recommendedProducts = [
      {
        id: '1',
        name: 'Summer Dress',
        price: 129.99,
        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'
      },
      {
        id: '2',
        name: 'Casual Shirt',
        price: 79.99,
        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200'
      },
      {
        id: '3',
        name: 'Denim Jacket',
        price: 159.99,
        image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=200'
      }
    ];
  }

  downloadBill() {
    this.isGeneratingBill = true;

    // Mock bill data - in real app, this would come from the order
    const billData: BillData = {
      orderId: this.orderId,
      paymentId: 'pay_' + Date.now(),
      customerName: 'Customer Name',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 9876543210',
      customerAddress: 'Sample Address, City, State - 123456',
      items: [
        {
          name: 'Sample Product',
          description: 'Product description',
          quantity: 1,
          price: 99.99,
          total: 99.99
        }
      ],
      subtotal: 99.99,
      tax: 17.99,
      shipping: 0,
      discount: 0,
      total: 117.98,
      paymentMethod: this.getPaymentMethodName(this.paymentMethod),
      orderDate: this.orderDate,
      billNumber: this.billService.generateBillNumber()
    };

    try {
      this.billService.downloadBill(billData);
    } catch (error) {
      console.error('Error downloading bill:', error);
      alert('Error downloading bill. Please try again.');
    } finally {
      this.isGeneratingBill = false;
    }
  }

  viewOrder() {
    this.router.navigate(['/account/orders', this.orderId]);
  }

  continueShopping() {
    this.router.navigate(['/']);
  }

  viewProduct(product: any) {
    this.router.navigate(['/product', product.id]);
  }

  getPaymentMethodName(method: string): string {
    const methodNames: { [key: string]: string } = {
      'razorpay': 'Credit/Debit Card',
      'upi': 'UPI',
      'netbanking': 'Net Banking',
      'wallet': 'Digital Wallet',
      'cod': 'Cash on Delivery'
    };
    return methodNames[method] || method;
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(price);
  }
}
