{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\nconst _c0 = [\"tabsInner\"];\nclass MenuController {\n  constructor(menuController) {\n    this.menuController = menuController;\n  }\n  /**\n   * Programmatically open the Menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully opened\n   */\n  open(menuId) {\n    return this.menuController.open(menuId);\n  }\n  /**\n   * Programmatically close the Menu. If no `menuId` is given as the first\n   * argument then it'll close any menu which is open. If a `menuId`\n   * is given then it'll close that exact menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully closed\n   */\n  close(menuId) {\n    return this.menuController.close(menuId);\n  }\n  /**\n   * Toggle the menu. If it's closed, it will open, and if opened, it\n   * will close.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu has been toggled\n   */\n  toggle(menuId) {\n    return this.menuController.toggle(menuId);\n  }\n  /**\n   * Used to enable or disable a menu. For example, there could be multiple\n   * left menus, but only one of them should be able to be opened at the same\n   * time. If there are multiple menus on the same side, then enabling one menu\n   * will also automatically disable all the others that are on the same side.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  enable(shouldEnable, menuId) {\n    return this.menuController.enable(shouldEnable, menuId);\n  }\n  /**\n   * Used to enable or disable the ability to swipe open the menu.\n   * @param shouldEnable  True if it should be swipe-able, false if not.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  swipeGesture(shouldEnable, menuId) {\n    return this.menuController.swipeGesture(shouldEnable, menuId);\n  }\n  /**\n   * @param [menuId] Optionally get the menu by its id, or side.\n   * @return Returns true if the specified menu is currently open, otherwise false.\n   * If the menuId is not specified, it returns true if ANY menu is currenly open.\n   */\n  isOpen(menuId) {\n    return this.menuController.isOpen(menuId);\n  }\n  /**\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns true if the menu is currently enabled, otherwise false.\n   */\n  isEnabled(menuId) {\n    return this.menuController.isEnabled(menuId);\n  }\n  /**\n   * Used to get a menu instance. If a `menuId` is not provided then it'll\n   * return the first menu found. If a `menuId` is `left` or `right`, then\n   * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n   * provided, then it'll try to find the menu using the menu's `id`\n   * property. If a menu is not found then it'll return `null`.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu if found, otherwise `null`.\n   */\n  get(menuId) {\n    return this.menuController.get(menuId);\n  }\n  /**\n   * @return Returns the instance of the menu already opened, otherwise `null`.\n   */\n  getOpen() {\n    return this.menuController.getOpen();\n  }\n  /**\n   * @return Returns an array of all menu instances.\n   */\n  getMenus() {\n    return this.menuController.getMenus();\n  }\n  registerAnimation(name, animation) {\n    return this.menuController.registerAnimation(name, animation);\n  }\n  isAnimating() {\n    return this.menuController.isAnimating();\n  }\n  _getOpenSync() {\n    return this.menuController._getOpenSync();\n  }\n  _createAnimation(type, menuCmp) {\n    return this.menuController._createAnimation(type, menuCmp);\n  }\n  _register(menu) {\n    return this.menuController._register(menu);\n  }\n  _unregister(menu) {\n    return this.menuController._unregister(menu);\n  }\n  _setOpen(menu, shouldOpen, animated) {\n    return this.menuController._setOpen(menu, shouldOpen, animated);\n  }\n}\nlet DomController = /*#__PURE__*/(() => {\n  class DomController {\n    /**\n     * Schedules a task to run during the READ phase of the next frame.\n     * This task should only read the DOM, but never modify it.\n     */\n    read(cb) {\n      getQueue().read(cb);\n    }\n    /**\n     * Schedules a task to run during the WRITE phase of the next frame.\n     * This task should write the DOM, but never READ it.\n     */\n    write(cb) {\n      getQueue().write(cb);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */DomController.ɵfac = function DomController_Factory(t) {\n    return new (t || DomController)();\n  };\n  DomController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomController,\n    factory: DomController.ɵfac,\n    providedIn: 'root'\n  });\n  return DomController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst getQueue = () => {\n  const win = typeof window !== 'undefined' ? window : null;\n  if (win != null) {\n    const Ionic = win.Ionic;\n    if (Ionic?.queue) {\n      return Ionic.queue;\n    }\n    return {\n      read: cb => win.requestAnimationFrame(cb),\n      write: cb => win.requestAnimationFrame(cb)\n    };\n  }\n  return {\n    read: cb => cb(),\n    write: cb => cb()\n  };\n};\nlet Platform = /*#__PURE__*/(() => {\n  class Platform {\n    constructor(doc, zone) {\n      this.doc = doc;\n      /**\n       * @hidden\n       */\n      this.backButton = new Subject();\n      /**\n       * The keyboardDidShow event emits when the\n       * on-screen keyboard is presented.\n       */\n      this.keyboardDidShow = new Subject();\n      /**\n       * The keyboardDidHide event emits when the\n       * on-screen keyboard is hidden.\n       */\n      this.keyboardDidHide = new Subject();\n      /**\n       * The pause event emits when the native platform puts the application\n       * into the background, typically when the user switches to a different\n       * application. This event would emit when a Cordova app is put into\n       * the background, however, it would not fire on a standard web browser.\n       */\n      this.pause = new Subject();\n      /**\n       * The resume event emits when the native platform pulls the application\n       * out from the background. This event would emit when a Cordova app comes\n       * out from the background, however, it would not fire on a standard web browser.\n       */\n      this.resume = new Subject();\n      /**\n       * The resize event emits when the browser window has changed dimensions. This\n       * could be from a browser window being physically resized, or from a device\n       * changing orientation.\n       */\n      this.resize = new Subject();\n      zone.run(() => {\n        this.win = doc.defaultView;\n        this.backButton.subscribeWithPriority = function (priority, callback) {\n          return this.subscribe(ev => {\n            return ev.register(priority, processNextHandler => zone.run(() => callback(processNextHandler)));\n          });\n        };\n        proxyEvent(this.pause, doc, 'pause', zone);\n        proxyEvent(this.resume, doc, 'resume', zone);\n        proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n        proxyEvent(this.resize, this.win, 'resize', zone);\n        proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n        proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n        let readyResolve;\n        this._readyPromise = new Promise(res => {\n          readyResolve = res;\n        });\n        if (this.win?.['cordova']) {\n          doc.addEventListener('deviceready', () => {\n            readyResolve('cordova');\n          }, {\n            once: true\n          });\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          readyResolve('dom');\n        }\n      });\n    }\n    /**\n     * @returns returns true/false based on platform.\n     * @description\n     * Depending on the platform the user is on, `is(platformName)` will\n     * return `true` or `false`. Note that the same app can return `true`\n     * for more than one platform name. For example, an app running from\n     * an iPad would return `true` for the platform names: `mobile`,\n     * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n     * from Cordova then `cordova` would be true, and if it was running\n     * from a web browser on the iPad then `mobileweb` would be `true`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     if (this.platform.is('ios')) {\n     *       // This will only print when on iOS\n     *       console.log('I am an iOS device!');\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * | Platform Name   | Description                        |\n     * |-----------------|------------------------------------|\n     * | android         | on a device running Android.       |\n     * | capacitor       | on a device running Capacitor.     |\n     * | cordova         | on a device running Cordova.       |\n     * | ios             | on a device running iOS.           |\n     * | ipad            | on an iPad device.                 |\n     * | iphone          | on an iPhone device.               |\n     * | phablet         | on a phablet device.               |\n     * | tablet          | on a tablet device.                |\n     * | electron        | in Electron on a desktop device.   |\n     * | pwa             | as a PWA app.                      |\n     * | mobile          | on a mobile device.                |\n     * | mobileweb       | on a mobile device in a browser.   |\n     * | desktop         | on a desktop device.               |\n     * | hybrid          | is a cordova or capacitor app.     |\n     *\n     */\n    is(platformName) {\n      return isPlatform(this.win, platformName);\n    }\n    /**\n     * @returns the array of platforms\n     * @description\n     * Depending on what device you are on, `platforms` can return multiple values.\n     * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n     * it would return `mobile`, `ios`, and `iphone`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     // This will print an array of the current platforms\n     *     console.log(this.platform.platforms());\n     *   }\n     * }\n     * ```\n     */\n    platforms() {\n      return getPlatforms(this.win);\n    }\n    /**\n     * Returns a promise when the platform is ready and native functionality\n     * can be called. If the app is running from within a web browser, then\n     * the promise will resolve when the DOM is ready. When the app is running\n     * from an application engine such as Cordova, then the promise will\n     * resolve when Cordova triggers the `deviceready` event.\n     *\n     * The resolved value is the `readySource`, which states which platform\n     * ready was used. For example, when Cordova is ready, the resolved ready\n     * source is `cordova`. The default ready source value will be `dom`. The\n     * `readySource` is useful if different logic should run depending on the\n     * platform the app is running from. For example, only Cordova can execute\n     * the status bar plugin, so the web should not run status bar plugin logic.\n     *\n     * ```\n     * import { Component } from '@angular/core';\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyApp {\n     *   constructor(public platform: Platform) {\n     *     this.platform.ready().then((readySource) => {\n     *       console.log('Platform ready from', readySource);\n     *       // Platform now ready, execute any required native code\n     *     });\n     *   }\n     * }\n     * ```\n     */\n    ready() {\n      return this._readyPromise;\n    }\n    /**\n     * Returns if this app is using right-to-left language direction or not.\n     * We recommend the app's `index.html` file already has the correct `dir`\n     * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n     * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n     */\n    get isRTL() {\n      return this.doc.dir === 'rtl';\n    }\n    /**\n     * Get the query string parameter\n     */\n    getQueryParam(key) {\n      return readQueryParam(this.win.location.href, key);\n    }\n    /**\n     * Returns `true` if the app is in landscape mode.\n     */\n    isLandscape() {\n      return !this.isPortrait();\n    }\n    /**\n     * Returns `true` if the app is in portrait mode.\n     */\n    isPortrait() {\n      return this.win.matchMedia?.('(orientation: portrait)').matches;\n    }\n    testUserAgent(expression) {\n      const nav = this.win.navigator;\n      return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n    }\n    /**\n     * Get the current url.\n     */\n    url() {\n      return this.win.location.href;\n    }\n    /**\n     * Gets the width of the platform's viewport using `window.innerWidth`.\n     */\n    width() {\n      return this.win.innerWidth;\n    }\n    /**\n     * Gets the height of the platform's viewport using `window.innerHeight`.\n     */\n    height() {\n      return this.win.innerHeight;\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */Platform.ɵfac = function Platform_Factory(t) {\n    return new (t || Platform)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone));\n  };\n  Platform.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n  return Platform;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst readQueryParam = (url, key) => {\n  key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n  const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n  const results = regex.exec(url);\n  return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n  if (el) {\n    el.addEventListener(eventName, ev => {\n      /**\n       * `zone.run` is required to make sure that we are running inside the Angular zone\n       * at all times. This is necessary since an app that has Capacitor will\n       * override the `document.addEventListener` with its own implementation.\n       * The override causes the event to no longer be in the Angular zone.\n       */\n      zone.run(() => {\n        // ?? cordova might emit \"null\" events\n        const value = ev != null ? ev.detail : undefined;\n        emitter.next(value);\n      });\n    });\n  }\n};\nlet NavController = /*#__PURE__*/(() => {\n  class NavController {\n    constructor(platform, location, serializer, router) {\n      this.location = location;\n      this.serializer = serializer;\n      this.router = router;\n      this.direction = DEFAULT_DIRECTION;\n      this.animated = DEFAULT_ANIMATED;\n      this.guessDirection = 'forward';\n      this.lastNavId = -1;\n      // Subscribe to router events to detect direction\n      if (router) {\n        router.events.subscribe(ev => {\n          if (ev instanceof NavigationStart) {\n            const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n            this.guessDirection = id < this.lastNavId ? 'back' : 'forward';\n            this.guessAnimation = !ev.restoredState ? this.guessDirection : undefined;\n            this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n          }\n        });\n      }\n      // Subscribe to backButton events\n      platform.backButton.subscribeWithPriority(0, processNextHandler => {\n        this.pop();\n        processNextHandler();\n      });\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n     *\n     * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n     * and that it will show a \"forward\" animation by default.\n     *\n     * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n     * ```\n     */\n    navigateForward(url, options = {}) {\n      this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n      return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('back');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n     * and that it will show a \"back\" animation by default.\n     *\n     * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n     * ```\n     */\n    navigateBack(url, options = {}) {\n      this.setDirection('back', options.animated, options.animationDirection, options.animation);\n      return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('root');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **root** means that all existing pages in the stack will be removed,\n     * and the navigated page will become the single page in the stack.\n     *\n     * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n     * ```\n     */\n    navigateRoot(url, options = {}) {\n      this.setDirection('root', options.animated, options.animationDirection, options.animation);\n      return this.navigate(url, options);\n    }\n    /**\n     * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n     * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n     * by default.\n     */\n    back(options = {\n      animated: true,\n      animationDirection: 'back'\n    }) {\n      this.setDirection('back', options.animated, options.animationDirection, options.animation);\n      return this.location.back();\n    }\n    /**\n     * This methods goes back in the context of Ionic's stack navigation.\n     *\n     * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n     * This is the recommended way to go back when you are using `ion-router-outlet`.\n     *\n     * Resolves to `true` if it was able to pop.\n     */\n    pop() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        let outlet = _this.topOutlet;\n        while (outlet) {\n          if (yield outlet.pop()) {\n            return true;\n          } else {\n            outlet = outlet.parentOutlet;\n          }\n        }\n        return false;\n      })();\n    }\n    /**\n     * This methods specifies the direction of the next navigation performed by the Angular router.\n     *\n     * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n     *\n     * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n     */\n    setDirection(direction, animated, animationDirection, animationBuilder) {\n      this.direction = direction;\n      this.animated = getAnimation(direction, animated, animationDirection);\n      this.animationBuilder = animationBuilder;\n    }\n    /**\n     * @internal\n     */\n    setTopOutlet(outlet) {\n      this.topOutlet = outlet;\n    }\n    /**\n     * @internal\n     */\n    consumeTransition() {\n      let direction = 'root';\n      let animation;\n      const animationBuilder = this.animationBuilder;\n      if (this.direction === 'auto') {\n        direction = this.guessDirection;\n        animation = this.guessAnimation;\n      } else {\n        animation = this.animated;\n        direction = this.direction;\n      }\n      this.direction = DEFAULT_DIRECTION;\n      this.animated = DEFAULT_ANIMATED;\n      this.animationBuilder = undefined;\n      return {\n        direction,\n        animation,\n        animationBuilder\n      };\n    }\n    navigate(url, options) {\n      if (Array.isArray(url)) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return this.router.navigate(url, options);\n      } else {\n        /**\n         * navigateByUrl ignores any properties that\n         * would change the url, so things like queryParams\n         * would be ignored unless we create a url tree\n         * More Info: https://github.com/angular/angular/issues/18798\n         */\n        const urlTree = this.serializer.parse(url.toString());\n        if (options.queryParams !== undefined) {\n          urlTree.queryParams = {\n            ...options.queryParams\n          };\n        }\n        if (options.fragment !== undefined) {\n          urlTree.fragment = options.fragment;\n        }\n        /**\n         * `navigateByUrl` will still apply `NavigationExtras` properties\n         * that do not modify the url, such as `replaceUrl` which is why\n         * `options` is passed in here.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return this.router.navigateByUrl(urlTree, options);\n      }\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */NavController.ɵfac = function NavController_Factory(t) {\n    return new (t || NavController)(i0.ɵɵinject(Platform), i0.ɵɵinject(i1.Location), i0.ɵɵinject(i3.UrlSerializer), i0.ɵɵinject(i3.Router, 8));\n  };\n  NavController.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NavController,\n    factory: NavController.ɵfac,\n    providedIn: 'root'\n  });\n  return NavController;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst getAnimation = (direction, animated, animationDirection) => {\n  if (animated === false) {\n    return undefined;\n  }\n  if (animationDirection !== undefined) {\n    return animationDirection;\n  }\n  if (direction === 'forward' || direction === 'back') {\n    return direction;\n  } else if (direction === 'root' && animated === true) {\n    return 'forward';\n  }\n  return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\nlet Config = /*#__PURE__*/(() => {\n  class Config {\n    get(key, fallback) {\n      const c = getConfig();\n      if (c) {\n        return c.get(key, fallback);\n      }\n      return null;\n    }\n    getBoolean(key, fallback) {\n      const c = getConfig();\n      if (c) {\n        return c.getBoolean(key, fallback);\n      }\n      return false;\n    }\n    getNumber(key, fallback) {\n      const c = getConfig();\n      if (c) {\n        return c.getNumber(key, fallback);\n      }\n      return 0;\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */Config.ɵfac = function Config_Factory(t) {\n    return new (t || Config)();\n  };\n  Config.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Config,\n    factory: Config.ɵfac,\n    providedIn: 'root'\n  });\n  return Config;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n  if (typeof window !== 'undefined') {\n    const Ionic = window.Ionic;\n    if (Ionic?.config) {\n      return Ionic.config;\n    }\n  }\n  return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n  constructor(data = {}) {\n    this.data = data;\n  }\n  /**\n   * Get the value of a nav-parameter for the current view\n   *\n   * ```ts\n   * import { NavParams } from 'ionic-angular';\n   *\n   * export class MyClass{\n   *  constructor(public navParams: NavParams){\n   *    // userParams is an object we have in our nav-parameters\n   *    this.navParams.get('userParams');\n   *  }\n   * }\n   * ```\n   *\n   * @param param Which param you want to look up\n   */\n  get(param) {\n    return this.data[param];\n  }\n}\n\n// TODO(FW-2827): types\nlet AngularDelegate = /*#__PURE__*/(() => {\n  class AngularDelegate {\n    constructor() {\n      this.zone = inject(NgZone);\n      this.applicationRef = inject(ApplicationRef);\n    }\n    create(environmentInjector, injector, elementReferenceKey) {\n      return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */AngularDelegate.ɵfac = function AngularDelegate_Factory(t) {\n    return new (t || AngularDelegate)();\n  };\n  AngularDelegate.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AngularDelegate,\n    factory: AngularDelegate.ɵfac\n  });\n  return AngularDelegate;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass AngularFrameworkDelegate {\n  constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey) {\n    this.environmentInjector = environmentInjector;\n    this.injector = injector;\n    this.applicationRef = applicationRef;\n    this.zone = zone;\n    this.elementReferenceKey = elementReferenceKey;\n    this.elRefMap = new WeakMap();\n    this.elEventsMap = new WeakMap();\n  }\n  attachViewToDom(container, component, params, cssClasses) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentProps = {\n          ...params\n        };\n        /**\n         * Ionic Angular passes a reference to a modal\n         * or popover that can be accessed using a\n         * variable in the overlay component. If\n         * elementReferenceKey is defined, then we should\n         * pass a reference to the component using\n         * elementReferenceKey as the key.\n         */\n        if (this.elementReferenceKey !== undefined) {\n          componentProps[this.elementReferenceKey] = container;\n        }\n        const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey);\n        resolve(el);\n      });\n    });\n  }\n  removeViewFromDom(_container, component) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentRef = this.elRefMap.get(component);\n        if (componentRef) {\n          componentRef.destroy();\n          this.elRefMap.delete(component);\n          const unbindEvents = this.elEventsMap.get(component);\n          if (unbindEvents) {\n            unbindEvents();\n            this.elEventsMap.delete(component);\n          }\n        }\n        resolve();\n      });\n    });\n  }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey) => {\n  /**\n   * Wraps the injector with a custom injector that\n   * provides NavParams to the component.\n   *\n   * NavParams is a legacy feature from Ionic v3 that allows\n   * Angular developers to provide data to a component\n   * and access it by providing NavParams as a dependency\n   * in the constructor.\n   *\n   * The modern approach is to access the data directly\n   * from the component's class instance.\n   */\n  const childInjector = Injector.create({\n    providers: getProviders(params),\n    parent: injector\n  });\n  const componentRef = createComponent(component, {\n    environmentInjector,\n    elementInjector: childInjector\n  });\n  const instance = componentRef.instance;\n  const hostElement = componentRef.location.nativeElement;\n  if (params) {\n    /**\n     * For modals and popovers, a reference to the component is\n     * added to `params` during the call to attachViewToDom. If\n     * a reference using this name is already set, this means\n     * the app is trying to use the name as a component prop,\n     * which will cause collisions.\n     */\n    if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n      console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n    }\n    Object.assign(instance, params);\n  }\n  if (cssClasses) {\n    for (const cssClass of cssClasses) {\n      hostElement.classList.add(cssClass);\n    }\n  }\n  const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n  container.appendChild(hostElement);\n  applicationRef.attachView(componentRef.hostView);\n  elRefMap.set(hostElement, componentRef);\n  elEventsMap.set(hostElement, unbindEvents);\n  return hostElement;\n};\nconst LIFECYCLES = [LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD];\nconst bindLifecycleEvents = (zone, instance, element) => {\n  return zone.run(() => {\n    const unregisters = LIFECYCLES.filter(eventName => typeof instance[eventName] === 'function').map(eventName => {\n      const handler = ev => instance[eventName](ev.detail);\n      element.addEventListener(eventName, handler);\n      return () => element.removeEventListener(eventName, handler);\n    });\n    return () => unregisters.forEach(fn => fn());\n  });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = params => {\n  return [{\n    provide: NavParamsToken,\n    useValue: params\n  }, {\n    provide: NavParams,\n    useFactory: provideNavParamsInjectable,\n    deps: [NavParamsToken]\n  }];\n};\nconst provideNavParamsInjectable = params => {\n  return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\nconst proxyInputs = (Cmp, inputs) => {\n  const Prototype = Cmp.prototype;\n  inputs.forEach(item => {\n    Object.defineProperty(Prototype, item, {\n      get() {\n        return this.el[item];\n      },\n      set(val) {\n        this.z.runOutsideAngular(() => this.el[item] = val);\n      }\n    });\n  });\n};\nconst proxyMethods = (Cmp, methods) => {\n  const Prototype = Cmp.prototype;\n  methods.forEach(methodName => {\n    Prototype[methodName] = function () {\n      const args = arguments;\n      return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n    };\n  });\n};\nconst proxyOutputs = (instance, el, events) => {\n  events.forEach(eventName => instance[eventName] = fromEvent(el, eventName));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n  const decorator = function (cls) {\n    const {\n      defineCustomElementFn,\n      inputs,\n      methods\n    } = opts;\n    if (defineCustomElementFn !== undefined) {\n      defineCustomElementFn();\n    }\n    if (inputs) {\n      proxyInputs(cls, inputs);\n    }\n    if (methods) {\n      proxyMethods(cls, methods);\n    }\n    return cls;\n  };\n  return decorator;\n}\nconst POPOVER_INPUTS = ['alignment', 'animated', 'arrow', 'keepContentsMounted', 'backdropDismiss', 'cssClass', 'dismissOnSelect', 'enterAnimation', 'event', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'showBackdrop', 'translucent', 'trigger', 'triggerAction', 'reference', 'size', 'side'];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = /*#__PURE__*/(() => {\n  let IonPopover = class IonPopover {\n    constructor(c, r, z) {\n      this.z = z;\n      this.isCmpOpen = false;\n      this.el = r.nativeElement;\n      this.el.addEventListener('ionMount', () => {\n        this.isCmpOpen = true;\n        c.detectChanges();\n      });\n      this.el.addEventListener('didDismiss', () => {\n        this.isCmpOpen = false;\n        c.detectChanges();\n      });\n      proxyOutputs(this, this.el, ['ionPopoverDidPresent', 'ionPopoverWillPresent', 'ionPopoverWillDismiss', 'ionPopoverDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonPopover.ɵfac = function IonPopover_Factory(t) {\n    return new (t || IonPopover)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonPopover.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonPopover,\n    selectors: [[\"ion-popover\"]],\n    contentQueries: function IonPopover_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      alignment: \"alignment\",\n      animated: \"animated\",\n      arrow: \"arrow\",\n      keepContentsMounted: \"keepContentsMounted\",\n      backdropDismiss: \"backdropDismiss\",\n      cssClass: \"cssClass\",\n      dismissOnSelect: \"dismissOnSelect\",\n      enterAnimation: \"enterAnimation\",\n      event: \"event\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      showBackdrop: \"showBackdrop\",\n      translucent: \"translucent\",\n      trigger: \"trigger\",\n      triggerAction: \"triggerAction\",\n      reference: \"reference\",\n      size: \"size\",\n      side: \"side\"\n    }\n  });\n  IonPopover = __decorate([ProxyCmp({\n    inputs: POPOVER_INPUTS,\n    methods: POPOVER_METHODS\n  })\n  /**\n   * @Component extends from @Directive\n   * so by defining the inputs here we\n   * do not need to re-define them for the\n   * lazy loaded popover.\n   */], IonPopover);\n  return IonPopover;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst MODAL_INPUTS = ['animated', 'keepContentsMounted', 'backdropBreakpoint', 'backdropDismiss', 'breakpoints', 'canDismiss', 'cssClass', 'enterAnimation', 'event', 'handle', 'handleBehavior', 'initialBreakpoint', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'presentingElement', 'showBackdrop', 'translucent', 'trigger'];\nconst MODAL_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss', 'setCurrentBreakpoint', 'getCurrentBreakpoint'];\nlet IonModal = /*#__PURE__*/(() => {\n  let IonModal = class IonModal {\n    constructor(c, r, z) {\n      this.z = z;\n      this.isCmpOpen = false;\n      this.el = r.nativeElement;\n      this.el.addEventListener('ionMount', () => {\n        this.isCmpOpen = true;\n        c.detectChanges();\n      });\n      this.el.addEventListener('didDismiss', () => {\n        this.isCmpOpen = false;\n        c.detectChanges();\n      });\n      proxyOutputs(this, this.el, ['ionModalDidPresent', 'ionModalWillPresent', 'ionModalWillDismiss', 'ionModalDidDismiss', 'ionBreakpointDidChange', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonModal.ɵfac = function IonModal_Factory(t) {\n    return new (t || IonModal)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  IonModal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonModal,\n    selectors: [[\"ion-modal\"]],\n    contentQueries: function IonModal_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    inputs: {\n      animated: \"animated\",\n      keepContentsMounted: \"keepContentsMounted\",\n      backdropBreakpoint: \"backdropBreakpoint\",\n      backdropDismiss: \"backdropDismiss\",\n      breakpoints: \"breakpoints\",\n      canDismiss: \"canDismiss\",\n      cssClass: \"cssClass\",\n      enterAnimation: \"enterAnimation\",\n      event: \"event\",\n      handle: \"handle\",\n      handleBehavior: \"handleBehavior\",\n      initialBreakpoint: \"initialBreakpoint\",\n      isOpen: \"isOpen\",\n      keyboardClose: \"keyboardClose\",\n      leaveAnimation: \"leaveAnimation\",\n      mode: \"mode\",\n      presentingElement: \"presentingElement\",\n      showBackdrop: \"showBackdrop\",\n      translucent: \"translucent\",\n      trigger: \"trigger\"\n    }\n  });\n  IonModal = __decorate([ProxyCmp({\n    inputs: MODAL_INPUTS,\n    methods: MODAL_METHODS\n  })\n  /**\n   * @Component extends from @Directive\n   * so by defining the inputs here we\n   * do not need to re-define them for the\n   * lazy loaded popover.\n   */], IonModal);\n  return IonModal;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst insertView = (views, view, direction) => {\n  if (direction === 'root') {\n    return setRoot(views, view);\n  } else if (direction === 'forward') {\n    return setForward(views, view);\n  } else {\n    return setBack(views, view);\n  }\n};\nconst setRoot = (views, view) => {\n  views = views.filter(v => v.stackId !== view.stackId);\n  views.push(view);\n  return views;\n};\nconst setForward = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    views = views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    views.push(view);\n  }\n  return views;\n};\nconst setBack = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    return views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    return setRoot(views, view);\n  }\n};\nconst getUrl = (router, activatedRoute) => {\n  const urlTree = router.createUrlTree(['.'], {\n    relativeTo: activatedRoute\n  });\n  return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n  if (!leavingView) {\n    return true;\n  }\n  return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n  if (!prefixUrl) {\n    return undefined;\n  }\n  const segments = toSegments(url);\n  for (let i = 0; i < segments.length; i++) {\n    if (i >= prefixUrl.length) {\n      return segments[i];\n    }\n    if (segments[i] !== prefixUrl[i]) {\n      return undefined;\n    }\n  }\n  return undefined;\n};\nconst toSegments = path => {\n  return path.split('/').map(s => s.trim()).filter(s => s !== '');\n};\nconst destroyView = view => {\n  if (view) {\n    view.ref.destroy();\n    view.unlistenEvents();\n  }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n  constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n    this.containerEl = containerEl;\n    this.router = router;\n    this.navCtrl = navCtrl;\n    this.zone = zone;\n    this.location = location;\n    this.views = [];\n    this.skipTransition = false;\n    this.nextId = 0;\n    this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n  }\n  createView(ref, activatedRoute) {\n    const url = getUrl(this.router, activatedRoute);\n    const element = ref?.location?.nativeElement;\n    const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n    return {\n      id: this.nextId++,\n      stackId: computeStackId(this.tabsPrefix, url),\n      unlistenEvents,\n      element,\n      ref,\n      url\n    };\n  }\n  getExistingView(activatedRoute) {\n    const activatedUrlKey = getUrl(this.router, activatedRoute);\n    const view = this.views.find(vw => vw.url === activatedUrlKey);\n    if (view) {\n      view.ref.changeDetectorRef.reattach();\n    }\n    return view;\n  }\n  setActive(enteringView) {\n    const consumeResult = this.navCtrl.consumeTransition();\n    let {\n      direction,\n      animation,\n      animationBuilder\n    } = consumeResult;\n    const leavingView = this.activeView;\n    const tabSwitch = isTabSwitch(enteringView, leavingView);\n    if (tabSwitch) {\n      direction = 'back';\n      animation = undefined;\n    }\n    const viewsSnapshot = this.views.slice();\n    let currentNavigation;\n    const router = this.router;\n    // Angular >= 7.2.0\n    if (router.getCurrentNavigation) {\n      currentNavigation = router.getCurrentNavigation();\n      // Angular < 7.2.0\n    } else if (router.navigations?.value) {\n      currentNavigation = router.navigations.value;\n    }\n    /**\n     * If the navigation action\n     * sets `replaceUrl: true`\n     * then we need to make sure\n     * we remove the last item\n     * from our views stack\n     */\n    if (currentNavigation?.extras?.replaceUrl) {\n      if (this.views.length > 0) {\n        this.views.splice(-1, 1);\n      }\n    }\n    const reused = this.views.includes(enteringView);\n    const views = this.insertView(enteringView, direction);\n    // Trigger change detection before transition starts\n    // This will call ngOnInit() the first time too, just after the view\n    // was attached to the dom, but BEFORE the transition starts\n    if (!reused) {\n      enteringView.ref.changeDetectorRef.detectChanges();\n    }\n    /**\n     * If we are going back from a page that\n     * was presented using a custom animation\n     * we should default to using that\n     * unless the developer explicitly\n     * provided another animation.\n     */\n    const customAnimation = enteringView.animationBuilder;\n    if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n      animationBuilder = customAnimation;\n    }\n    /**\n     * Save any custom animation so that navigating\n     * back will use this custom animation by default.\n     */\n    if (leavingView) {\n      leavingView.animationBuilder = animationBuilder;\n    }\n    // Wait until previous transitions finish\n    return this.zone.runOutsideAngular(() => {\n      return this.wait(() => {\n        // disconnect leaving page from change detection to\n        // reduce jank during the page transition\n        if (leavingView) {\n          leavingView.ref.changeDetectorRef.detach();\n        }\n        // In case the enteringView is the same as the leavingPage we need to reattach()\n        enteringView.ref.changeDetectorRef.reattach();\n        return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder).then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone)).then(() => ({\n          enteringView,\n          direction,\n          animation,\n          tabSwitch\n        }));\n      });\n    });\n  }\n  canGoBack(deep, stackId = this.getActiveStackId()) {\n    return this.getStack(stackId).length > deep;\n  }\n  pop(deep, stackId = this.getActiveStackId()) {\n    return this.zone.run(() => {\n      const views = this.getStack(stackId);\n      if (views.length <= deep) {\n        return Promise.resolve(false);\n      }\n      const view = views[views.length - deep - 1];\n      let url = view.url;\n      const viewSavedData = view.savedData;\n      if (viewSavedData) {\n        const primaryOutlet = viewSavedData.get('primary');\n        if (primaryOutlet?.route?._routerState?.snapshot.url) {\n          url = primaryOutlet.route._routerState.snapshot.url;\n        }\n      }\n      const {\n        animationBuilder\n      } = this.navCtrl.consumeTransition();\n      return this.navCtrl.navigateBack(url, {\n        ...view.savedExtras,\n        animation: animationBuilder\n      }).then(() => true);\n    });\n  }\n  startBackTransition() {\n    const leavingView = this.activeView;\n    if (leavingView) {\n      const views = this.getStack(leavingView.stackId);\n      const enteringView = views[views.length - 2];\n      const customAnimation = enteringView.animationBuilder;\n      return this.wait(() => {\n        return this.transition(enteringView,\n        // entering view\n        leavingView,\n        // leaving view\n        'back', this.canGoBack(2), true, customAnimation);\n      });\n    }\n    return Promise.resolve();\n  }\n  endBackTransition(shouldComplete) {\n    if (shouldComplete) {\n      this.skipTransition = true;\n      this.pop(1);\n    } else if (this.activeView) {\n      cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n    }\n  }\n  getLastUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[views.length - 1] : undefined;\n  }\n  /**\n   * @internal\n   */\n  getRootUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[0] : undefined;\n  }\n  getActiveStackId() {\n    return this.activeView ? this.activeView.stackId : undefined;\n  }\n  /**\n   * @internal\n   */\n  getActiveView() {\n    return this.activeView;\n  }\n  hasRunningTask() {\n    return this.runningTask !== undefined;\n  }\n  destroy() {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.containerEl = undefined;\n    this.views.forEach(destroyView);\n    this.activeView = undefined;\n    this.views = [];\n  }\n  getStack(stackId) {\n    return this.views.filter(v => v.stackId === stackId);\n  }\n  insertView(enteringView, direction) {\n    this.activeView = enteringView;\n    this.views = insertView(this.views, enteringView, direction);\n    return this.views.slice();\n  }\n  transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n    if (this.skipTransition) {\n      this.skipTransition = false;\n      return Promise.resolve(false);\n    }\n    if (leavingView === enteringView) {\n      return Promise.resolve(false);\n    }\n    const enteringEl = enteringView ? enteringView.element : undefined;\n    const leavingEl = leavingView ? leavingView.element : undefined;\n    const containerEl = this.containerEl;\n    if (enteringEl && enteringEl !== leavingEl) {\n      enteringEl.classList.add('ion-page');\n      enteringEl.classList.add('ion-page-invisible');\n      if (enteringEl.parentElement !== containerEl) {\n        containerEl.appendChild(enteringEl);\n      }\n      if (containerEl.commit) {\n        return containerEl.commit(enteringEl, leavingEl, {\n          duration: direction === undefined ? 0 : undefined,\n          direction,\n          showGoBack,\n          progressAnimation,\n          animationBuilder\n        });\n      }\n    }\n    return Promise.resolve(false);\n  }\n  wait(task) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.runningTask !== undefined) {\n        yield _this2.runningTask;\n        _this2.runningTask = undefined;\n      }\n      const promise = _this2.runningTask = task();\n      promise.finally(() => _this2.runningTask = undefined);\n      return promise;\n    })();\n  }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n  if (typeof requestAnimationFrame === 'function') {\n    return new Promise(resolve => {\n      requestAnimationFrame(() => {\n        cleanup(activeRoute, views, viewsSnapshot, location, zone);\n        resolve();\n      });\n    });\n  }\n  return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n  /**\n   * Re-enter the Angular zone when destroying page components. This will allow\n   * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n   */\n  zone.run(() => viewsSnapshot.filter(view => !views.includes(view)).forEach(destroyView));\n  views.forEach(view => {\n    /**\n     * In the event that a user navigated multiple\n     * times in rapid succession, we want to make sure\n     * we don't pre-emptively detach a view while\n     * it is in mid-transition.\n     *\n     * In this instance we also do not care about query\n     * params or fragments as it will be the same view regardless\n     */\n    const locationWithoutParams = location.path().split('?')[0];\n    const locationWithoutFragment = locationWithoutParams.split('#')[0];\n    if (view !== activeRoute && view.url !== locationWithoutFragment) {\n      const element = view.element;\n      element.setAttribute('aria-hidden', 'true');\n      element.classList.add('ion-page-hidden');\n      view.ref.changeDetectorRef.detach();\n    }\n  });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet IonRouterOutlet = /*#__PURE__*/(() => {\n  class IonRouterOutlet {\n    constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n      this.parentOutlet = parentOutlet;\n      this.activatedView = null;\n      // Maintain map of activated route proxies for each component instance\n      this.proxyMap = new WeakMap();\n      // Keep the latest activated route in a subject for the proxy routes to switch map to\n      this.currentActivatedRoute$ = new BehaviorSubject(null);\n      this.activated = null;\n      this._activatedRoute = null;\n      /**\n       * The name of the outlet\n       */\n      this.name = PRIMARY_OUTLET;\n      /** @internal */\n      this.stackWillChange = new EventEmitter();\n      /** @internal */\n      this.stackDidChange = new EventEmitter();\n      // eslint-disable-next-line @angular-eslint/no-output-rename\n      this.activateEvents = new EventEmitter();\n      // eslint-disable-next-line @angular-eslint/no-output-rename\n      this.deactivateEvents = new EventEmitter();\n      this.parentContexts = inject(ChildrenOutletContexts);\n      this.location = inject(ViewContainerRef);\n      this.environmentInjector = inject(EnvironmentInjector);\n      this.inputBinder = inject(INPUT_BINDER, {\n        optional: true\n      });\n      /** @nodoc */\n      this.supportsBindingToComponentInputs = true;\n      // Ionic providers\n      this.config = inject(Config);\n      this.navCtrl = inject(NavController);\n      this.nativeEl = elementRef.nativeElement;\n      this.name = name || PRIMARY_OUTLET;\n      this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n      this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n      this.parentContexts.onChildOutletCreated(this.name, this);\n    }\n    /** @internal */\n    get activatedComponentRef() {\n      return this.activated;\n    }\n    set animation(animation) {\n      this.nativeEl.animation = animation;\n    }\n    set animated(animated) {\n      this.nativeEl.animated = animated;\n    }\n    set swipeGesture(swipe) {\n      this._swipeGesture = swipe;\n      this.nativeEl.swipeHandler = swipe ? {\n        canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n        onStart: () => this.stackCtrl.startBackTransition(),\n        onEnd: shouldContinue => this.stackCtrl.endBackTransition(shouldContinue)\n      } : undefined;\n    }\n    ngOnDestroy() {\n      this.stackCtrl.destroy();\n      this.inputBinder?.unsubscribeFromRouteData(this);\n    }\n    getContext() {\n      return this.parentContexts.getContext(this.name);\n    }\n    ngOnInit() {\n      this.initializeOutletWithName();\n    }\n    // Note: Ionic deviates from the Angular Router implementation here\n    initializeOutletWithName() {\n      if (!this.activated) {\n        // If the outlet was not instantiated at the time the route got activated we need to populate\n        // the outlet when it is initialized (ie inside a NgIf)\n        const context = this.getContext();\n        if (context?.route) {\n          this.activateWith(context.route, context.injector);\n        }\n      }\n      new Promise(resolve => componentOnReady(this.nativeEl, resolve)).then(() => {\n        if (this._swipeGesture === undefined) {\n          this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n        }\n      });\n    }\n    get isActivated() {\n      return !!this.activated;\n    }\n    get component() {\n      if (!this.activated) {\n        throw new Error('Outlet is not activated');\n      }\n      return this.activated.instance;\n    }\n    get activatedRoute() {\n      if (!this.activated) {\n        throw new Error('Outlet is not activated');\n      }\n      return this._activatedRoute;\n    }\n    get activatedRouteData() {\n      if (this._activatedRoute) {\n        return this._activatedRoute.snapshot.data;\n      }\n      return {};\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to detach the subtree\n     */\n    detach() {\n      throw new Error('incompatible reuse strategy');\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    attach(_ref, _activatedRoute) {\n      throw new Error('incompatible reuse strategy');\n    }\n    deactivate() {\n      if (this.activated) {\n        if (this.activatedView) {\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const context = this.getContext();\n          this.activatedView.savedData = new Map(context.children['contexts']);\n          /**\n           * Angular v11.2.10 introduced a change\n           * where this route context is cleared out when\n           * a router-outlet is deactivated, However,\n           * we need this route information in order to\n           * return a user back to the correct tab when\n           * leaving and then going back to the tab context.\n           */\n          const primaryOutlet = this.activatedView.savedData.get('primary');\n          if (primaryOutlet && context.route) {\n            primaryOutlet.route = {\n              ...context.route\n            };\n          }\n          /**\n           * Ensure we are saving the NavigationExtras\n           * data otherwise it will be lost\n           */\n          this.activatedView.savedExtras = {};\n          if (context.route) {\n            const contextSnapshot = context.route.snapshot;\n            this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n            this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n          }\n        }\n        const c = this.component;\n        this.activatedView = null;\n        this.activated = null;\n        this._activatedRoute = null;\n        this.deactivateEvents.emit(c);\n      }\n    }\n    activateWith(activatedRoute, environmentInjector) {\n      if (this.isActivated) {\n        throw new Error('Cannot activate an already activated outlet');\n      }\n      this._activatedRoute = activatedRoute;\n      let cmpRef;\n      let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n      if (enteringView) {\n        cmpRef = this.activated = enteringView.ref;\n        const saved = enteringView.savedData;\n        if (saved) {\n          // self-restore\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const context = this.getContext();\n          context.children['contexts'] = saved;\n        }\n        // Updated activated route proxy for this component\n        this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n      } else {\n        const snapshot = activatedRoute._futureSnapshot;\n        /**\n         * Angular 14 introduces a new `loadComponent` property to the route config.\n         * This function will assign a `component` property to the route snapshot.\n         * We check for the presence of this property to determine if the route is\n         * using standalone components.\n         */\n        const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n        // We create an activated route proxy object that will maintain future updates for this component\n        // over its lifecycle in the stack.\n        const component$ = new BehaviorSubject(null);\n        const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n        const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const component = snapshot.routeConfig.component ?? snapshot.component;\n        cmpRef = this.activated = this.location.createComponent(component, {\n          index: this.location.length,\n          injector,\n          environmentInjector: environmentInjector ?? this.environmentInjector\n        });\n        // Once the component is created we can push it to our local subject supplied to the proxy\n        component$.next(cmpRef.instance);\n        // Calling `markForCheck` to make sure we will run the change detection when the\n        // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n        enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n        // Store references to the proxy by component\n        this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n        this.currentActivatedRoute$.next({\n          component: cmpRef.instance,\n          activatedRoute\n        });\n      }\n      this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n      this.activatedView = enteringView;\n      /**\n       * The top outlet is set prior to the entering view's transition completing,\n       * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n       * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n       * activated.\n       *\n       * In this scenario, activeWith is called for both the tabs and the root router outlet.\n       * To avoid a race condition, we assign the top outlet synchronously.\n       */\n      this.navCtrl.setTopOutlet(this);\n      const leavingView = this.stackCtrl.getActiveView();\n      this.stackWillChange.emit({\n        enteringView,\n        tabSwitch: isTabSwitch(enteringView, leavingView)\n      });\n      this.stackCtrl.setActive(enteringView).then(data => {\n        this.activateEvents.emit(cmpRef.instance);\n        this.stackDidChange.emit(data);\n      });\n    }\n    /**\n     * Returns `true` if there are pages in the stack to go back.\n     */\n    canGoBack(deep = 1, stackId) {\n      return this.stackCtrl.canGoBack(deep, stackId);\n    }\n    /**\n     * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n     */\n    pop(deep = 1, stackId) {\n      return this.stackCtrl.pop(deep, stackId);\n    }\n    /**\n     * Returns the URL of the active page of each stack.\n     */\n    getLastUrl(stackId) {\n      const active = this.stackCtrl.getLastUrl(stackId);\n      return active ? active.url : undefined;\n    }\n    /**\n     * Returns the RouteView of the active page of each stack.\n     * @internal\n     */\n    getLastRouteView(stackId) {\n      return this.stackCtrl.getLastUrl(stackId);\n    }\n    /**\n     * Returns the root view in the tab stack.\n     * @internal\n     */\n    getRootView(stackId) {\n      return this.stackCtrl.getRootUrl(stackId);\n    }\n    /**\n     * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n     */\n    getActiveStackId() {\n      return this.stackCtrl.getActiveStackId();\n    }\n    /**\n     * Since the activated route can change over the life time of a component in an ion router outlet, we create\n     * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n     */\n    createActivatedRouteProxy(component$, activatedRoute) {\n      const proxy = new ActivatedRoute();\n      proxy._futureSnapshot = activatedRoute._futureSnapshot;\n      proxy._routerState = activatedRoute._routerState;\n      proxy.snapshot = activatedRoute.snapshot;\n      proxy.outlet = activatedRoute.outlet;\n      proxy.component = activatedRoute.component;\n      // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n      proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n      proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n      proxy.url = this.proxyObservable(component$, 'url');\n      proxy.params = this.proxyObservable(component$, 'params');\n      proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n      proxy.fragment = this.proxyObservable(component$, 'fragment');\n      proxy.data = this.proxyObservable(component$, 'data');\n      return proxy;\n    }\n    /**\n     * Create a wrapped observable that will switch to the latest activated route matched by the given component\n     */\n    proxyObservable(component$, path) {\n      return component$.pipe(\n      // First wait until the component instance is pushed\n      filter(component => !!component), switchMap(component => this.currentActivatedRoute$.pipe(filter(current => current !== null && current.component === component), switchMap(current => current && current.activatedRoute[path]), distinctUntilChanged())));\n    }\n    /**\n     * Updates the activated route proxy for the given component to the new incoming router state\n     */\n    updateActivatedRouteProxy(component, activatedRoute) {\n      const proxy = this.proxyMap.get(component);\n      if (!proxy) {\n        throw new Error(`Could not find activated route proxy for view`);\n      }\n      proxy._futureSnapshot = activatedRoute._futureSnapshot;\n      proxy._routerState = activatedRoute._routerState;\n      proxy.snapshot = activatedRoute.snapshot;\n      proxy.outlet = activatedRoute.outlet;\n      proxy.component = activatedRoute.component;\n      this.currentActivatedRoute$.next({\n        component,\n        activatedRoute\n      });\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */IonRouterOutlet.ɵfac = function IonRouterOutlet_Factory(t) {\n    return new (t || IonRouterOutlet)(i0.ɵɵinjectAttribute('name'), i0.ɵɵinjectAttribute('tabs'), i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(IonRouterOutlet, 12));\n  };\n  IonRouterOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonRouterOutlet,\n    selectors: [[\"ion-router-outlet\"]],\n    inputs: {\n      animated: \"animated\",\n      animation: \"animation\",\n      mode: \"mode\",\n      swipeGesture: \"swipeGesture\",\n      name: \"name\"\n    },\n    outputs: {\n      stackWillChange: \"stackWillChange\",\n      stackDidChange: \"stackDidChange\",\n      activateEvents: \"activate\",\n      deactivateEvents: \"deactivate\"\n    },\n    exportAs: [\"outlet\"]\n  });\n  return IonRouterOutlet;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass OutletInjector {\n  constructor(route, childContexts, parent) {\n    this.route = route;\n    this.childContexts = childContexts;\n    this.parent = parent;\n  }\n  get(token, notFoundValue) {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n    return this.parent.get(token, notFoundValue);\n  }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nlet RoutedComponentInputBinder = /*#__PURE__*/(() => {\n  class RoutedComponentInputBinder {\n    constructor() {\n      this.outletDataSubscriptions = new Map();\n    }\n    bindActivatedRouteToOutletComponent(outlet) {\n      this.unsubscribeFromRouteData(outlet);\n      this.subscribeToRouteData(outlet);\n    }\n    unsubscribeFromRouteData(outlet) {\n      this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n      this.outletDataSubscriptions.delete(outlet);\n    }\n    subscribeToRouteData(outlet) {\n      const {\n        activatedRoute\n      } = outlet;\n      const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data]).pipe(switchMap(([queryParams, params, data], index) => {\n        data = {\n          ...queryParams,\n          ...params,\n          ...data\n        };\n        // Get the first result from the data subscription synchronously so it's available to\n        // the component as soon as possible (and doesn't require a second change detection).\n        if (index === 0) {\n          return of(data);\n        }\n        // Promise.resolve is used to avoid synchronously writing the wrong data when\n        // two of the Observables in the `combineLatest` stream emit one after\n        // another.\n        return Promise.resolve(data);\n      })).subscribe(data => {\n        // Outlet may have been deactivated or changed names to be associated with a different\n        // route\n        if (!outlet.isActivated || !outlet.activatedComponentRef || outlet.activatedRoute !== activatedRoute || activatedRoute.component === null) {\n          this.unsubscribeFromRouteData(outlet);\n          return;\n        }\n        const mirror = reflectComponentType(activatedRoute.component);\n        if (!mirror) {\n          this.unsubscribeFromRouteData(outlet);\n          return;\n        }\n        for (const {\n          templateName\n        } of mirror.inputs) {\n          outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n        }\n      });\n      this.outletDataSubscriptions.set(outlet, dataSubscription);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */RoutedComponentInputBinder.ɵfac = function RoutedComponentInputBinder_Factory(t) {\n    return new (t || RoutedComponentInputBinder)();\n  };\n  RoutedComponentInputBinder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RoutedComponentInputBinder,\n    factory: RoutedComponentInputBinder.ɵfac\n  });\n  return RoutedComponentInputBinder;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst provideComponentInputBinding = () => {\n  return {\n    provide: INPUT_BINDER,\n    useFactory: componentInputBindingFactory,\n    deps: [Router]\n  };\n};\nfunction componentInputBindingFactory(router) {\n  /**\n   * We cast the router to any here, since the componentInputBindingEnabled\n   * property is not available until Angular v16.\n   */\n  if (router?.componentInputBindingEnabled) {\n    return new RoutedComponentInputBinder();\n  }\n  return null;\n}\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = /*#__PURE__*/(() => {\n  let IonBackButton = class IonBackButton {\n    constructor(routerOutlet, navCtrl, config, r, z, c) {\n      this.routerOutlet = routerOutlet;\n      this.navCtrl = navCtrl;\n      this.config = config;\n      this.r = r;\n      this.z = z;\n      c.detach();\n      this.el = this.r.nativeElement;\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n      const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n      if (this.routerOutlet?.canGoBack()) {\n        this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n        this.routerOutlet.pop();\n        ev.preventDefault();\n      } else if (defaultHref != null) {\n        this.navCtrl.navigateBack(defaultHref, {\n          animation: this.routerAnimation\n        });\n        ev.preventDefault();\n      }\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonBackButton.ɵfac = function IonBackButton_Factory(t) {\n    return new (t || IonBackButton)(i0.ɵɵdirectiveInject(IonRouterOutlet, 8), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(Config), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  IonBackButton.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonBackButton,\n    hostBindings: function IonBackButton_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function IonBackButton_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n    },\n    inputs: {\n      color: \"color\",\n      defaultHref: \"defaultHref\",\n      disabled: \"disabled\",\n      icon: \"icon\",\n      mode: \"mode\",\n      routerAnimation: \"routerAnimation\",\n      text: \"text\",\n      type: \"type\"\n    }\n  });\n  IonBackButton = __decorate([ProxyCmp({\n    inputs: BACK_BUTTON_INPUTS\n  })], IonBackButton);\n  return IonBackButton;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nlet RouterLinkDelegateDirective = /*#__PURE__*/(() => {\n  class RouterLinkDelegateDirective {\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n      this.locationStrategy = locationStrategy;\n      this.navCtrl = navCtrl;\n      this.elementRef = elementRef;\n      this.router = router;\n      this.routerLink = routerLink;\n      this.routerDirection = 'forward';\n    }\n    ngOnInit() {\n      this.updateTargetUrlAndHref();\n    }\n    ngOnChanges() {\n      this.updateTargetUrlAndHref();\n    }\n    updateTargetUrlAndHref() {\n      if (this.routerLink?.urlTree) {\n        const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n        this.elementRef.nativeElement.href = href;\n      }\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n      this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n      /**\n       * This prevents the browser from\n       * performing a page reload when pressing\n       * an Ionic component with routerLink.\n       * The page reload interferes with routing\n       * and causes ion-back-button to disappear\n       * since the local history is wiped on reload.\n       */\n      ev.preventDefault();\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */RouterLinkDelegateDirective.ɵfac = function RouterLinkDelegateDirective_Factory(t) {\n    return new (t || RouterLinkDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n  };\n  RouterLinkDelegateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkDelegateDirective,\n    selectors: [[\"\", \"routerLink\", \"\", 5, \"a\", 5, \"area\"]],\n    hostBindings: function RouterLinkDelegateDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLinkDelegateDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n    },\n    inputs: {\n      routerDirection: \"routerDirection\",\n      routerAnimation: \"routerAnimation\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return RouterLinkDelegateDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RouterLinkWithHrefDelegateDirective = /*#__PURE__*/(() => {\n  class RouterLinkWithHrefDelegateDirective {\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n      this.locationStrategy = locationStrategy;\n      this.navCtrl = navCtrl;\n      this.elementRef = elementRef;\n      this.router = router;\n      this.routerLink = routerLink;\n      this.routerDirection = 'forward';\n    }\n    ngOnInit() {\n      this.updateTargetUrlAndHref();\n    }\n    ngOnChanges() {\n      this.updateTargetUrlAndHref();\n    }\n    updateTargetUrlAndHref() {\n      if (this.routerLink?.urlTree) {\n        const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n        this.elementRef.nativeElement.href = href;\n      }\n    }\n    /**\n     * @internal\n     */\n    onClick() {\n      this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */RouterLinkWithHrefDelegateDirective.ɵfac = function RouterLinkWithHrefDelegateDirective_Factory(t) {\n    return new (t || RouterLinkWithHrefDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n  };\n  RouterLinkWithHrefDelegateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RouterLinkWithHrefDelegateDirective,\n    selectors: [[\"a\", \"routerLink\", \"\"], [\"area\", \"routerLink\", \"\"]],\n    hostBindings: function RouterLinkWithHrefDelegateDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function RouterLinkWithHrefDelegateDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      routerDirection: \"routerDirection\",\n      routerAnimation: \"routerAnimation\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return RouterLinkWithHrefDelegateDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = ['push', 'insert', 'insertPages', 'pop', 'popTo', 'popToRoot', 'removeIndex', 'setRoot', 'setPages', 'getActive', 'getByIndex', 'canGoBack', 'getPrevious'];\nlet IonNav = /*#__PURE__*/(() => {\n  let IonNav = class IonNav {\n    constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n      this.z = z;\n      c.detach();\n      this.el = ref.nativeElement;\n      ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n      proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n    }\n  };\n  /** @nocollapse */\n  /** @nocollapse */IonNav.ɵfac = function IonNav_Factory(t) {\n    return new (t || IonNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.EnvironmentInjector), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(AngularDelegate), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  IonNav.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonNav,\n    inputs: {\n      animated: \"animated\",\n      animation: \"animation\",\n      root: \"root\",\n      rootParams: \"rootParams\",\n      swipeGesture: \"swipeGesture\"\n    }\n  });\n  IonNav = __decorate([ProxyCmp({\n    inputs: NAV_INPUTS,\n    methods: NAV_METHODS\n  })], IonNav);\n  return IonNav;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet IonTabs = /*#__PURE__*/(() => {\n  class IonTabs {\n    constructor(navCtrl) {\n      this.navCtrl = navCtrl;\n      /**\n       * Emitted before the tab view is changed.\n       */\n      this.ionTabsWillChange = new EventEmitter();\n      /**\n       * Emitted after the tab view is changed.\n       */\n      this.ionTabsDidChange = new EventEmitter();\n      this.tabBarSlot = 'bottom';\n    }\n    ngAfterContentInit() {\n      this.detectSlotChanges();\n    }\n    ngAfterContentChecked() {\n      this.detectSlotChanges();\n    }\n    /**\n     * @internal\n     */\n    onStackWillChange({\n      enteringView,\n      tabSwitch\n    }) {\n      const stackId = enteringView.stackId;\n      if (tabSwitch && stackId !== undefined) {\n        this.ionTabsWillChange.emit({\n          tab: stackId\n        });\n      }\n    }\n    /**\n     * @internal\n     */\n    onStackDidChange({\n      enteringView,\n      tabSwitch\n    }) {\n      const stackId = enteringView.stackId;\n      if (tabSwitch && stackId !== undefined) {\n        if (this.tabBar) {\n          this.tabBar.selectedTab = stackId;\n        }\n        this.ionTabsDidChange.emit({\n          tab: stackId\n        });\n      }\n    }\n    /**\n     * When a tab button is clicked, there are several scenarios:\n     * 1. If the selected tab is currently active (the tab button has been clicked\n     *    again), then it should go to the root view for that tab.\n     *\n     *   a. Get the saved root view from the router outlet. If the saved root view\n     *      matches the tabRootUrl, set the route view to this view including the\n     *      navigation extras.\n     *   b. If the saved root view from the router outlet does\n     *      not match, navigate to the tabRootUrl. No navigation extras are\n     *      included.\n     *\n     * 2. If the current tab tab is not currently selected, get the last route\n     *    view from the router outlet.\n     *\n     *   a. If the last route view exists, navigate to that view including any\n     *      navigation extras\n     *   b. If the last route view doesn't exist, then navigate\n     *      to the default tabRootUrl\n     */\n    select(tabOrEvent) {\n      const isTabString = typeof tabOrEvent === 'string';\n      const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n      const alreadySelected = this.outlet.getActiveStackId() === tab;\n      const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n      /**\n       * If this is a nested tab, prevent the event\n       * from bubbling otherwise the outer tabs\n       * will respond to this event too, causing\n       * the app to get directed to the wrong place.\n       */\n      if (!isTabString) {\n        tabOrEvent.stopPropagation();\n      }\n      if (alreadySelected) {\n        const activeStackId = this.outlet.getActiveStackId();\n        const activeView = this.outlet.getLastRouteView(activeStackId);\n        // If on root tab, do not navigate to root tab again\n        if (activeView?.url === tabRootUrl) {\n          return;\n        }\n        const rootView = this.outlet.getRootView(tab);\n        const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n        return this.navCtrl.navigateRoot(tabRootUrl, {\n          ...navigationExtras,\n          animated: true,\n          animationDirection: 'back'\n        });\n      } else {\n        const lastRoute = this.outlet.getLastRouteView(tab);\n        /**\n         * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n         * selected tab\n         */\n        const url = lastRoute?.url || tabRootUrl;\n        const navigationExtras = lastRoute?.savedExtras;\n        return this.navCtrl.navigateRoot(url, {\n          ...navigationExtras,\n          animated: true,\n          animationDirection: 'back'\n        });\n      }\n    }\n    getSelected() {\n      return this.outlet.getActiveStackId();\n    }\n    /**\n     * Detects changes to the slot attribute of the tab bar.\n     *\n     * If the slot attribute has changed, then the tab bar\n     * should be relocated to the new slot position.\n     */\n    detectSlotChanges() {\n      this.tabBars.forEach(tabBar => {\n        // el is a protected attribute from the generated component wrapper\n        const currentSlot = tabBar.el.getAttribute('slot');\n        if (currentSlot !== this.tabBarSlot) {\n          this.tabBarSlot = currentSlot;\n          this.relocateTabBar();\n        }\n      });\n    }\n    /**\n     * Relocates the tab bar to the new slot position.\n     */\n    relocateTabBar() {\n      /**\n       * `el` is a protected attribute from the generated component wrapper.\n       * To avoid having to manually create the wrapper for tab bar, we\n       * cast the tab bar to any and access the protected attribute.\n       */\n      const tabBar = this.tabBar.el;\n      if (this.tabBarSlot === 'top') {\n        /**\n         * A tab bar with a slot of \"top\" should be inserted\n         * at the top of the container.\n         */\n        this.tabsInner.nativeElement.before(tabBar);\n      } else {\n        /**\n         * A tab bar with a slot of \"bottom\" or without a slot\n         * should be inserted at the end of the container.\n         */\n        this.tabsInner.nativeElement.after(tabBar);\n      }\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */IonTabs.ɵfac = function IonTabs_Factory(t) {\n    return new (t || IonTabs)(i0.ɵɵdirectiveInject(NavController));\n  };\n  IonTabs.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: IonTabs,\n    selectors: [[\"ion-tabs\"]],\n    viewQuery: function IonTabs_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabsInner = _t.first);\n      }\n    },\n    hostBindings: function IonTabs_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionTabButtonClick\", function IonTabs_ionTabButtonClick_HostBindingHandler($event) {\n          return ctx.select($event);\n        });\n      }\n    },\n    outputs: {\n      ionTabsWillChange: \"ionTabsWillChange\",\n      ionTabsDidChange: \"ionTabsDidChange\"\n    }\n  });\n  return IonTabs;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nlet ValueAccessor = /*#__PURE__*/(() => {\n  class ValueAccessor {\n    constructor(injector, elementRef) {\n      this.injector = injector;\n      this.elementRef = elementRef;\n      this.onChange = () => {\n        /**/\n      };\n      this.onTouched = () => {\n        /**/\n      };\n    }\n    writeValue(value) {\n      this.elementRef.nativeElement.value = this.lastValue = value;\n      setIonicClasses(this.elementRef);\n    }\n    /**\n     * Notifies the ControlValueAccessor of a change in the value of the control.\n     *\n     * This is called by each of the ValueAccessor directives when we want to update\n     * the status and validity of the form control. For example with text components this\n     * is called when the ionInput event is fired. For select components this is called\n     * when the ionChange event is fired.\n     *\n     * This also updates the Ionic form status classes on the element.\n     *\n     * @param el The component element.\n     * @param value The new value of the control.\n     */\n    handleValueChange(el, value) {\n      if (el === this.elementRef.nativeElement) {\n        if (value !== this.lastValue) {\n          this.lastValue = value;\n          this.onChange(value);\n        }\n        setIonicClasses(this.elementRef);\n      }\n    }\n    _handleBlurEvent(el) {\n      if (el === this.elementRef.nativeElement) {\n        this.onTouched();\n        setIonicClasses(this.elementRef);\n      }\n    }\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n      this.elementRef.nativeElement.disabled = isDisabled;\n    }\n    ngOnDestroy() {\n      if (this.statusChanges) {\n        this.statusChanges.unsubscribe();\n      }\n    }\n    ngAfterViewInit() {\n      let ngControl;\n      try {\n        ngControl = this.injector.get(NgControl);\n      } catch {\n        /* No FormControl or ngModel binding */\n      }\n      if (!ngControl) {\n        return;\n      }\n      // Listen for changes in validity, disabled, or pending states\n      if (ngControl.statusChanges) {\n        this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n      }\n      /**\n       * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n       * whenever it is implemented.\n       */\n      const formControl = ngControl.control;\n      if (formControl) {\n        const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n        methodsToPatch.forEach(method => {\n          if (typeof formControl[method] !== 'undefined') {\n            const oldFn = formControl[method].bind(formControl);\n            formControl[method] = (...params) => {\n              oldFn(...params);\n              setIonicClasses(this.elementRef);\n            };\n          }\n        });\n      }\n    }\n  }\n  /** @nocollapse */\n  /** @nocollapse */ValueAccessor.ɵfac = function ValueAccessor_Factory(t) {\n    return new (t || ValueAccessor)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  ValueAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ValueAccessor,\n    hostBindings: function ValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"ionBlur\", function ValueAccessor_ionBlur_HostBindingHandler($event) {\n          return ctx._handleBlurEvent($event.target);\n        });\n      }\n    }\n  });\n  return ValueAccessor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst setIonicClasses = element => {\n  raf(() => {\n    const input = element.nativeElement;\n    const hasValue = input.value != null && input.value.toString().length > 0;\n    const classes = getClasses(input);\n    setClasses(input, classes);\n    const item = input.closest('ion-item');\n    if (item) {\n      if (hasValue) {\n        setClasses(item, [...classes, 'item-has-value']);\n      } else {\n        setClasses(item, classes);\n      }\n    }\n  });\n};\nconst getClasses = element => {\n  const classList = element.classList;\n  const classes = [];\n  for (let i = 0; i < classList.length; i++) {\n    const item = classList.item(i);\n    if (item !== null && startsWith(item, 'ng-')) {\n      classes.push(`ion-${item.substring(3)}`);\n    }\n  }\n  return classes;\n};\nconst setClasses = (element, classes) => {\n  const classList = element.classList;\n  classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n  classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n  return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   */\n  shouldDetach(_route) {\n    return false;\n  }\n  /**\n   * Returns `false`, meaning the route (and its subtree) is never reattached\n   */\n  shouldAttach(_route) {\n    return false;\n  }\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n  store(_route, _detachedTree) {\n    return;\n  }\n  /**\n   * Returns `null` because this strategy does not store routes for later re-use.\n   */\n  retrieve(_route) {\n    return null;\n  }\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and\n   * current route config are identical and all route parameters are identical.\n   */\n  shouldReuseRoute(future, curr) {\n    if (future.routeConfig !== curr.routeConfig) {\n      return false;\n    }\n    // checking router params\n    const futureParams = future.params;\n    const currentParams = curr.params;\n    const keysA = Object.keys(futureParams);\n    const keysB = Object.keys(currentParams);\n    if (keysA.length !== keysB.length) {\n      return false;\n    }\n    // Test for A's keys different from B.\n    for (const key of keysA) {\n      if (currentParams[key] !== futureParams[key]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n  constructor(ctrl) {\n    this.ctrl = ctrl;\n  }\n  /**\n   * Creates a new overlay\n   */\n  create(opts) {\n    return this.ctrl.create(opts || {});\n  }\n  /**\n   * When `id` is not provided, it dismisses the top overlay.\n   */\n  dismiss(data, role, id) {\n    return this.ctrl.dismiss(data, role, id);\n  }\n  /**\n   * Returns the top overlay.\n   */\n  getTop() {\n    return this.ctrl.getTop();\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };\n//# sourceMappingURL=ionic-angular-common.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}