<div class="auth-container">
  <div class="auth-card">
    <!-- Logo -->
    <div class="logo">
      <h1 class="gradient-text">DFashion</h1>
      <p>Social E-commerce Platform</p>
    </div>

    <!-- <PERSON>gin Header -->
    <div class="login-header">
      <h3>Welcome Back</h3>
      <p>Sign in to your account</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
      <div class="form-group">
        <input
          type="email"
          formControlName="email"
          placeholder="Email"
          class="form-control"
          [class.error]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
        >
        <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched" class="error-message">
          <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <input
          type="password"
          formControlName="password"
          placeholder="Password"
          class="form-control"
          [class.error]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
        >
        <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="error-message">
          <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="form-group remember-me">
        <label class="checkbox-label">
          <input type="checkbox" formControlName="rememberMe">
          <span class="checkmark"></span>
          Remember me
        </label>
      </div>

      <button
        type="submit"
        class="btn-primary auth-btn"
        [disabled]="loginForm.invalid || loading"
      >
        <span *ngIf="loading" class="loading-spinner"></span>
        {{ loading ? 'Signing in...' : 'Sign In' }}
      </button>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
    </form>

    <!-- Register Link -->
    <div class="auth-link">
      <p>Don't have an account? <a routerLink="/auth/register">Sign up</a></p>
    </div>
  </div>
</div>
