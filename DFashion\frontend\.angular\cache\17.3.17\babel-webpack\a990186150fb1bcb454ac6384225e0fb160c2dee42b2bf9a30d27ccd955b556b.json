{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let NoDataConfigService = /*#__PURE__*/(() => {\n  class NoDataConfigService {\n    constructor() {\n      this.configs = {\n        // Shop page configurations\n        'shop.featuredBrands': {\n          title: 'No Featured Brands',\n          message: 'We\\'re working on adding featured brands. Check back soon!',\n          iconClass: 'fas fa-store',\n          containerClass: 'compact',\n          showActions: true,\n          primaryAction: 'Browse All Brands',\n          suggestions: ['Nike', 'Adidas', 'Zara', 'H&M', 'Uniqlo'],\n          suggestionsTitle: 'Popular Brands:'\n        },\n        'shop.trendingProducts': {\n          title: 'No Trending Products',\n          message: 'Discover what\\'s hot! We\\'re updating our trending collection.',\n          iconClass: 'fas fa-fire',\n          containerClass: 'compact',\n          showActions: true,\n          primaryAction: 'Browse All Products',\n          secondaryAction: 'View Categories',\n          suggestions: ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Accessories'],\n          suggestionsTitle: 'Popular Categories:'\n        },\n        'shop.newArrivals': {\n          title: 'No New Arrivals',\n          message: 'Stay tuned for the latest fashion arrivals!',\n          iconClass: 'fas fa-sparkles',\n          containerClass: 'compact',\n          showActions: true,\n          primaryAction: 'Browse All Products',\n          secondaryAction: 'Set Alerts',\n          suggestions: ['Summer Collection', 'Winter Wear', 'Casual Outfits', 'Formal Wear'],\n          suggestionsTitle: 'Coming Soon:'\n        },\n        'shop.quickLinks': {\n          title: 'Quick Links Unavailable',\n          message: 'Quick navigation links are being updated.',\n          iconClass: 'fas fa-link',\n          containerClass: 'compact',\n          showActions: true,\n          primaryAction: 'Browse Categories',\n          suggestions: ['Women\\'s Fashion', 'Men\\'s Fashion', 'Kids\\' Fashion', 'Accessories'],\n          suggestionsTitle: 'Browse:'\n        },\n        // Search page configurations\n        'search.noResults': {\n          title: 'No Products Found',\n          message: 'Try searching with different keywords or browse our categories.',\n          iconClass: 'fas fa-search',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Browse Categories',\n          secondaryAction: 'Clear Filters',\n          suggestions: ['kurtas', 'jeans', 'dresses', 'shoes', 'bags', 'watches'],\n          suggestionsTitle: 'Popular searches:'\n        },\n        // Cart configurations\n        'cart.empty': {\n          title: 'Your Cart is Empty',\n          message: 'Looks like you haven\\'t added anything to your cart yet.',\n          iconClass: 'fas fa-shopping-cart',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Start Shopping',\n          secondaryAction: 'View Wishlist',\n          suggestions: ['Trending Products', 'New Arrivals', 'Sale Items'],\n          suggestionsTitle: 'Explore:'\n        },\n        // Wishlist configurations\n        'wishlist.empty': {\n          title: 'Your Wishlist is Empty',\n          message: 'Save items you love to your wishlist for easy access later.',\n          iconClass: 'fas fa-heart',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Discover Products',\n          suggestions: ['Trending Now', 'New Arrivals', 'Best Sellers'],\n          suggestionsTitle: 'Start with:'\n        },\n        // Posts configurations\n        'posts.noPosts': {\n          title: 'No Posts Yet',\n          message: 'Be the first to share your style! Create your first post.',\n          iconClass: 'fas fa-camera',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Create Post',\n          secondaryAction: 'Browse Feed',\n          suggestions: ['Fashion Tips', 'Outfit Ideas', 'Style Inspiration'],\n          suggestionsTitle: 'Post Ideas:'\n        },\n        // Stories configurations\n        'stories.noStories': {\n          title: 'No Stories Available',\n          message: 'Share your fashion moments with stories!',\n          iconClass: 'fas fa-plus-circle',\n          containerClass: 'compact',\n          showActions: true,\n          primaryAction: 'Create Story',\n          suggestions: ['Daily Outfit', 'Behind the Scenes', 'Style Tips'],\n          suggestionsTitle: 'Story Ideas:'\n        },\n        // Orders configurations\n        'orders.noOrders': {\n          title: 'No Orders Yet',\n          message: 'You haven\\'t placed any orders yet. Start shopping to see your orders here.',\n          iconClass: 'fas fa-receipt',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Start Shopping',\n          suggestions: ['Trending Products', 'New Arrivals', 'Best Deals'],\n          suggestionsTitle: 'Shop:'\n        },\n        // Reviews configurations\n        'reviews.noReviews': {\n          title: 'No Reviews Yet',\n          message: 'Be the first to review this product and help others make informed decisions.',\n          iconClass: 'fas fa-star',\n          containerClass: 'compact',\n          showActions: true,\n          primaryAction: 'Write Review',\n          suggestions: ['Quality', 'Fit', 'Style', 'Value for Money'],\n          suggestionsTitle: 'Review aspects:'\n        },\n        // Notifications configurations\n        'notifications.noNotifications': {\n          title: 'No Notifications',\n          message: 'You\\'re all caught up! No new notifications.',\n          iconClass: 'fas fa-bell',\n          containerClass: 'full-height',\n          showActions: false,\n          suggestions: ['Order Updates', 'New Arrivals', 'Sale Alerts'],\n          suggestionsTitle: 'You\\'ll be notified about:'\n        },\n        // Generic configurations\n        'generic.noData': {\n          title: 'No Data Available',\n          message: 'There is no data to display at the moment.',\n          iconClass: 'fas fa-inbox',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Refresh',\n          secondaryAction: 'Go Back'\n        },\n        'generic.error': {\n          title: 'Something Went Wrong',\n          message: 'We encountered an error while loading the data. Please try again.',\n          iconClass: 'fas fa-exclamation-triangle',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Try Again',\n          secondaryAction: 'Go Back'\n        },\n        'generic.maintenance': {\n          title: 'Under Maintenance',\n          message: 'This section is currently under maintenance. We\\'ll be back soon!',\n          iconClass: 'fas fa-tools',\n          containerClass: 'full-height',\n          showActions: true,\n          primaryAction: 'Go Back',\n          suggestions: ['Check our social media for updates'],\n          suggestionsTitle: 'Stay updated:'\n        }\n      };\n    }\n    getConfig(key) {\n      return this.configs[key] || this.configs['generic.noData'];\n    }\n    setConfig(key, config) {\n      this.configs[key] = config;\n    }\n    // Helper methods for common scenarios\n    getShopConfig(section) {\n      return this.getConfig(`shop.${section}`);\n    }\n    getSearchConfig() {\n      return this.getConfig('search.noResults');\n    }\n    getCartConfig() {\n      return this.getConfig('cart.empty');\n    }\n    getWishlistConfig() {\n      return this.getConfig('wishlist.empty');\n    }\n    getPostsConfig() {\n      return this.getConfig('posts.noPosts');\n    }\n    getStoriesConfig() {\n      return this.getConfig('stories.noStories');\n    }\n    getOrdersConfig() {\n      return this.getConfig('orders.noOrders');\n    }\n    getReviewsConfig() {\n      return this.getConfig('reviews.noReviews');\n    }\n    getNotificationsConfig() {\n      return this.getConfig('notifications.noNotifications');\n    }\n    getErrorConfig() {\n      return this.getConfig('generic.error');\n    }\n    getMaintenanceConfig() {\n      return this.getConfig('generic.maintenance');\n    }\n    static {\n      this.ɵfac = function NoDataConfigService_Factory(t) {\n        return new (t || NoDataConfigService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NoDataConfigService,\n        factory: NoDataConfigService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NoDataConfigService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}