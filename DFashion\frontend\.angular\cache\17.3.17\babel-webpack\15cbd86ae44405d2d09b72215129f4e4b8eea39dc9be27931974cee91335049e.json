{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, combineLatest } from 'rxjs';\nimport { DynamicProfileComponent } from '../../../../shared/components/dynamic-profile/dynamic-profile.component';\nimport { RoleBasedSettingsComponent } from '../../../../shared/components/role-based-settings/role-based-settings.component';\nimport { DepartmentDashboardComponent } from '../../../../shared/components/department-dashboard/department-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"../../../../core/services/role-management.service\";\nimport * as i4 from \"../../../../core/services/permission-management.service\";\nimport * as i5 from \"@angular/common\";\nfunction ProfileComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setActiveTab(\"dashboard\"));\n    });\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Dashboard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"dashboard\");\n  }\n}\nfunction ProfileComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setActiveTab(\"team\"));\n    });\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Team\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"team\");\n  }\n}\nfunction ProfileComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setActiveTab(\"analytics\"));\n    });\n    i0.ɵɵelement(1, \"i\", 16);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Analytics\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"analytics\");\n  }\n}\nfunction ProfileComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background\", ctx_r1.roleConfig == null ? null : ctx_r1.roleConfig.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.roleConfig.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.roleConfig.displayName);\n  }\n}\nfunction ProfileComponent_app_dynamic_profile_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-dynamic-profile\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"userProfile\", ctx_r1.currentUser);\n  }\n}\nfunction ProfileComponent_div_19_app_department_dashboard_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-department-dashboard\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"department\", ctx_r1.currentDepartment)(\"userRole\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.role) || null);\n  }\n}\nfunction ProfileComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, ProfileComponent_div_19_app_department_dashboard_1_Template, 1, 2, \"app-department-dashboard\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"dashboard\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentDepartment && (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.role));\n  }\n}\nfunction ProfileComponent_app_role_based_settings_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-role-based-settings\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"currentRole\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.role) || null);\n  }\n}\nfunction ProfileComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 22)(2, \"div\", 23)(3, \"h2\");\n    i0.ɵɵtext(4, \"Team Management\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Manage your team members and their roles\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"div\", 25)(9, \"div\", 26);\n    i0.ɵɵelement(10, \"i\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 27)(12, \"div\", 28);\n    i0.ɵɵtext(13, \"12\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 29);\n    i0.ɵɵtext(15, \"Team Members\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 25)(17, \"div\", 26);\n    i0.ɵɵelement(18, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 27)(20, \"div\", 28);\n    i0.ɵɵtext(21, \"10\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 29);\n    i0.ɵɵtext(23, \"Active Members\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"div\", 26);\n    i0.ɵɵelement(26, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 27)(28, \"div\", 28);\n    i0.ɵɵtext(29, \"87%\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 29);\n    i0.ɵɵtext(31, \"Team Performance\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 32)(33, \"button\", 33);\n    i0.ɵɵelement(34, \"i\", 34);\n    i0.ɵɵtext(35, \" Add Team Member \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 35);\n    i0.ɵɵelement(37, \"i\", 36);\n    i0.ɵɵtext(38, \" Export Report \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"team\");\n  }\n}\nfunction ProfileComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 37)(2, \"div\", 38)(3, \"h2\");\n    i0.ɵɵtext(4, \"Analytics Dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Performance insights and metrics\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 39)(8, \"div\", 40)(9, \"h3\");\n    i0.ɵɵtext(10, \"Performance Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 41);\n    i0.ɵɵelement(12, \"i\", 31);\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14, \"Performance Chart\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 40)(16, \"h3\");\n    i0.ɵɵtext(17, \"Key Metrics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 42)(19, \"div\", 43)(20, \"span\", 44);\n    i0.ɵɵtext(21, \"Total Revenue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 45);\n    i0.ɵɵtext(23, \"\\u20B92.4M\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 43)(25, \"span\", 44);\n    i0.ɵɵtext(26, \"Conversion Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 46);\n    i0.ɵɵtext(28, \"3.2%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 43)(30, \"span\", 44);\n    i0.ɵɵtext(31, \"Customer Satisfaction\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 47);\n    i0.ɵɵtext(33, \"4.8/5\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 40)(35, \"h3\");\n    i0.ɵɵtext(36, \"Recent Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 48)(38, \"div\", 49)(39, \"div\", 50);\n    i0.ɵɵelement(40, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 52)(42, \"div\", 53);\n    i0.ɵɵtext(43, \"New sale completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 54);\n    i0.ɵɵtext(45, \"2 hours ago\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 49)(47, \"div\", 55);\n    i0.ɵɵelement(48, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 52)(50, \"div\", 53);\n    i0.ɵɵtext(51, \"Team meeting scheduled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 54);\n    i0.ɵɵtext(53, \"4 hours ago\");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"analytics\");\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(route, router, authService, roleManagementService, permissionManagementService) {\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.roleManagementService = roleManagementService;\n      this.permissionManagementService = permissionManagementService;\n      this.currentUser = null;\n      this.currentDepartment = null;\n      this.roleConfig = null;\n      this.activeTab = 'profile';\n      // Permission flags\n      this.showDashboard = false;\n      this.canManageTeam = false;\n      this.canViewAnalytics = false;\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.loadUserProfile();\n      this.setupPermissions();\n      // Check for tab parameter in URL\n      this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        if (params['tab']) {\n          this.setActiveTab(params['tab']);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadUserProfile() {\n      // Get current user from auth service\n      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(response => {\n        if (response && response.user) {\n          const user = response.user;\n          this.currentUser = {\n            id: user._id,\n            username: user.username,\n            fullName: user.fullName,\n            email: user.email,\n            avatar: user.avatar,\n            role: user.role || 'support_agent',\n            department: 'support',\n            joinDate: new Date(user.createdAt || Date.now()),\n            lastActive: new Date(),\n            bio: user.bio || '',\n            location: user.address?.city || '',\n            phone: user.phone || '',\n            isVerified: user.isVerified,\n            isOnline: true\n          };\n          this.roleConfig = this.roleManagementService.getRoleConfig(this.currentUser.role);\n          this.currentDepartment = this.roleConfig.department;\n          // Set current user in permission service\n          this.permissionManagementService.setCurrentUser(user);\n        }\n      });\n    }\n    setupPermissions() {\n      combineLatest([this.permissionManagementService.isFeatureEnabled('team_management'), this.permissionManagementService.isFeatureEnabled('advanced_analytics'), this.permissionManagementService.canAccessComponent('team_dashboard')]).pipe(takeUntil(this.destroy$)).subscribe(([teamManagement, analytics, dashboard]) => {\n        this.canManageTeam = teamManagement;\n        this.canViewAnalytics = analytics;\n        this.showDashboard = dashboard;\n      });\n    }\n    setActiveTab(tab) {\n      this.activeTab = tab;\n      // Update URL without navigation\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          tab\n        },\n        queryParamsHandling: 'merge'\n      });\n    }\n    // Utility methods for role-based functionality\n    getRoleDisplayName() {\n      if (!this.currentUser?.role) return '';\n      return this.roleManagementService.getRoleConfig(this.currentUser.role).displayName;\n    }\n    getPerformanceColor(performance) {\n      if (performance >= 90) return '#4CAF50';\n      if (performance >= 75) return '#FF9800';\n      return '#f44336';\n    }\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.RoleManagementService), i0.ɵɵdirectiveInject(i4.PermissionManagementService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 24,\n        vars: 19,\n        consts: [[1, \"profile-page\"], [1, \"profile-navigation\"], [1, \"nav-container\"], [1, \"nav-tabs\"], [1, \"nav-tab\", 3, \"click\"], [1, \"fas\", \"fa-user\"], [\"class\", \"nav-tab\", 3, \"active\", \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-cog\"], [\"class\", \"role-badge\", 3, \"background\", 4, \"ngIf\"], [1, \"profile-content\"], [1, \"tab-content\"], [3, \"userProfile\", 4, \"ngIf\"], [\"class\", \"tab-content\", 3, \"active\", 4, \"ngIf\"], [3, \"currentRole\", 4, \"ngIf\"], [1, \"fas\", \"fa-tachometer-alt\"], [1, \"fas\", \"fa-users\"], [1, \"fas\", \"fa-chart-bar\"], [1, \"role-badge\"], [3, \"userProfile\"], [3, \"department\", \"userRole\", 4, \"ngIf\"], [3, \"department\", \"userRole\"], [3, \"currentRole\"], [1, \"team-management\"], [1, \"team-header\"], [1, \"team-stats\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"fas\", \"fa-user-check\"], [1, \"fas\", \"fa-chart-line\"], [1, \"team-actions\"], [1, \"btn\", \"btn-primary\"], [1, \"fas\", \"fa-user-plus\"], [1, \"btn\", \"btn-secondary\"], [1, \"fas\", \"fa-download\"], [1, \"analytics-dashboard\"], [1, \"analytics-header\"], [1, \"analytics-grid\"], [1, \"analytics-card\"], [1, \"chart-placeholder\"], [1, \"metrics-list\"], [1, \"metric-item\"], [1, \"metric-label\"], [1, \"metric-value\", 2, \"color\", \"#4CAF50\"], [1, \"metric-value\", 2, \"color\", \"#2196F3\"], [1, \"metric-value\", 2, \"color\", \"#FF9800\"], [1, \"activities-list\"], [1, \"activity-item\"], [1, \"activity-icon\", 2, \"background\", \"#4CAF50\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"activity-content\"], [1, \"activity-title\"], [1, \"activity-time\"], [1, \"activity-icon\", 2, \"background\", \"#2196F3\"], [1, \"fas\", \"fa-calendar\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_4_listener() {\n              return ctx.setActiveTab(\"profile\");\n            });\n            i0.ɵɵelement(5, \"i\", 5);\n            i0.ɵɵelementStart(6, \"span\");\n            i0.ɵɵtext(7, \"Profile\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(8, ProfileComponent_button_8_Template, 4, 2, \"button\", 6);\n            i0.ɵɵelementStart(9, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_9_listener() {\n              return ctx.setActiveTab(\"settings\");\n            });\n            i0.ɵɵelement(10, \"i\", 7);\n            i0.ɵɵelementStart(11, \"span\");\n            i0.ɵɵtext(12, \"Settings\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(13, ProfileComponent_button_13_Template, 4, 2, \"button\", 6)(14, ProfileComponent_button_14_Template, 4, 2, \"button\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(15, ProfileComponent_div_15_Template, 4, 5, \"div\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 9)(17, \"div\", 10);\n            i0.ɵɵtemplate(18, ProfileComponent_app_dynamic_profile_18_Template, 1, 1, \"app-dynamic-profile\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, ProfileComponent_div_19_Template, 2, 3, \"div\", 12);\n            i0.ɵɵelementStart(20, \"div\", 10);\n            i0.ɵɵtemplate(21, ProfileComponent_app_role_based_settings_21_Template, 1, 1, \"app-role-based-settings\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(22, ProfileComponent_div_22_Template, 39, 2, \"div\", 12)(23, ProfileComponent_div_23_Template, 54, 2, \"div\", 12);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"data-role\", ctx.currentUser == null ? null : ctx.currentUser.role)(\"data-department\", ctx.currentDepartment);\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"profile\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.showDashboard);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"settings\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.canManageTeam);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.canViewAnalytics);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.roleConfig);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"profile\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showDashboard);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"settings\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser == null ? null : ctx.currentUser.role);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.canManageTeam);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.canViewAnalytics);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, FormsModule, DynamicProfileComponent, RoleBasedSettingsComponent, DepartmentDashboardComponent],\n        styles: [\".profile-page[_ngcontent-%COMP%]{max-width:1400px;margin:0 auto;padding:1rem;background:#f8f9fa;min-height:100vh}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1rem;margin-bottom:2rem;box-shadow:0 2px 12px #0000000f}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;gap:2rem}@media (max-width: 768px){.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]{display:flex;gap:.5rem;overflow-x:auto;padding-bottom:.5rem}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar{height:4px}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:2px}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:2px}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.5rem;border:none;background:#f8f9fa;color:#6c757d;border-radius:12px;cursor:pointer;transition:all .3s ease;white-space:nowrap;font-weight:500}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;box-shadow:0 4px 12px #667eea4d}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}@media (max-width: 768px){.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]{padding:.5rem 1rem;font-size:.85rem}}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem;border-radius:20px;color:#fff;font-weight:600;font-size:.85rem;box-shadow:0 4px 12px #00000026}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}@media (max-width: 768px){.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%]   .nav-container[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%]{padding:.5rem .75rem;font-size:.8rem}}.profile-page[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]{position:relative}.profile-page[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]{display:none}.profile-page[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .tab-content.active[_ngcontent-%COMP%]{display:block}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:2rem;box-shadow:0 2px 12px #0000000f}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-header[_ngcontent-%COMP%]{margin-bottom:2rem;text-align:center}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#262626;font-size:2rem;font-weight:700}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#8e8e8e;font-size:1rem}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-stats[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.5rem;margin-bottom:2rem}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:12px;padding:1.5rem;display:flex;align-items:center;gap:1rem;transition:transform .3s ease}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:48px;height:48px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:12px;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.2rem}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:700;color:#262626;line-height:1}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.85rem;margin-top:.25rem}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:1rem;margin-bottom:2rem}@media (max-width: 768px){.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.5rem;border:none;border-radius:8px;font-weight:600;cursor:pointer;transition:all .3s ease;text-decoration:none}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;box-shadow:0 4px 12px #667eea4d}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 16px #667eea66}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%]   .team-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:2rem;box-shadow:0 2px 12px #0000000f}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]{margin-bottom:2rem;text-align:center}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#262626;font-size:2rem;font-weight:700}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#8e8e8e;font-size:1rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:12px;padding:1.5rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 1rem;color:#262626;font-size:1.1rem;font-weight:600}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]{height:200px;display:flex;flex-direction:column;align-items:center;justify-content:center;background:#fff;border-radius:8px;color:#6c757d}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:.5rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .metrics-list[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:.75rem 0;border-bottom:1px solid #e9ecef}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .metrics-list[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .metrics-list[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .metrics-list[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%]{font-weight:600;font-size:1rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:.75rem 0;border-bottom:1px solid #e9ecef}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:.9rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]{flex:1}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%]{font-weight:600;color:#262626;font-size:.9rem;margin-bottom:.25rem}.profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem}@media (prefers-color-scheme: dark){.profile-page[_ngcontent-%COMP%]{background:#121212}.profile-page[_ngcontent-%COMP%]   .profile-navigation[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .team-management[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .analytics-dashboard[_ngcontent-%COMP%]{background:#1e1e1e;color:#fff}.profile-page[_ngcontent-%COMP%]   .team-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .analytics-grid[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]{background:#2a2a2a}.profile-page[_ngcontent-%COMP%]   .team-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .analytics-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%]{color:#fff}.profile-page[_ngcontent-%COMP%]   .team-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%]{color:#b3b3b3}.profile-page[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]{background:#333!important;color:#b3b3b3}.profile-page[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%], .profile-page[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{border-bottom-color:#333}}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}