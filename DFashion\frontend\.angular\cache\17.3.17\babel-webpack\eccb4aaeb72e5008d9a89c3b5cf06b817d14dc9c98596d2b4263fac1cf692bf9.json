{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/wishlist.service\";\nimport * as i2 from \"../../../../core/services/cart.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction WishlistComponent_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.wishlistItems.length, \" items\");\n  }\n}\nfunction WishlistComponent_div_5_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(2, 1, item_r4.product.originalPrice), \"\");\n  }\n}\nfunction WishlistComponent_div_5_div_2_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", item_r4.product.discount, \"% OFF\");\n  }\n}\nfunction WishlistComponent_div_5_div_2_div_16_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const star_r5 = ctx.$implicit;\n    i0.ɵɵclassMap(star_r5);\n  }\n}\nfunction WishlistComponent_div_5_div_2_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵtemplate(2, WishlistComponent_div_5_div_2_div_16_i_2_Template, 1, 2, \"i\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getStars(item_r4.product.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", item_r4.product.rating.count, \")\");\n  }\n}\nfunction WishlistComponent_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementStart(3, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_5_div_2_Template_button_click_3_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.removeFromWishlist(item_r4.product._id));\n    });\n    i0.ɵɵelement(4, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 17)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 19)(11, \"span\", 20);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, WishlistComponent_div_5_div_2_span_14_Template, 3, 3, \"span\", 21)(15, WishlistComponent_div_5_div_2_span_15_Template, 2, 1, \"span\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, WishlistComponent_div_5_div_2_div_16_Template, 5, 2, \"div\", 23);\n    i0.ɵɵelementStart(17, \"div\", 24)(18, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_5_div_2_Template_button_click_18_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.addToCart(item_r4.product._id));\n    });\n    i0.ɵɵelement(19, \"i\", 26);\n    i0.ɵɵtext(20, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_5_div_2_Template_button_click_21_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.viewProduct(item_r4.product._id));\n    });\n    i0.ɵɵtext(22, \" View Details \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", item_r4.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", item_r4.product.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r4.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.product.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(13, 8, item_r4.product.price), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r4.product.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.product.discount > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4.product.rating);\n  }\n}\nfunction WishlistComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n    i0.ɵɵtemplate(2, WishlistComponent_div_5_div_2_Template, 23, 10, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_5_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.clearWishlist());\n    });\n    i0.ɵɵtext(5, \" Clear Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_5_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.continueShopping());\n    });\n    i0.ɵɵtext(7, \" Continue Shopping \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.wishlistItems);\n  }\n}\nfunction WishlistComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Your wishlist is empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Save items you love to your wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function WishlistComponent_div_6_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.continueShopping());\n    });\n    i0.ɵɵtext(7, \" Shop Now \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction WishlistComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"div\", 37);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading wishlist...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let WishlistComponent = /*#__PURE__*/(() => {\n  class WishlistComponent {\n    constructor(wishlistService, cartService, router) {\n      this.wishlistService = wishlistService;\n      this.cartService = cartService;\n      this.router = router;\n      this.wishlistItems = [];\n      this.isLoading = true;\n    }\n    ngOnInit() {\n      this.loadWishlist();\n      this.subscribeToWishlistUpdates();\n    }\n    loadWishlist() {\n      this.wishlistService.getWishlist().subscribe({\n        next: response => {\n          this.wishlistItems = response.data.items;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Failed to load wishlist:', error);\n          this.isLoading = false;\n          this.wishlistItems = [];\n        }\n      });\n    }\n    subscribeToWishlistUpdates() {\n      this.wishlistService.wishlistItems$.subscribe(items => {\n        this.wishlistItems = items;\n      });\n    }\n    removeFromWishlist(productId) {\n      this.wishlistService.removeFromWishlist(productId).subscribe({\n        next: () => {\n          this.loadWishlist(); // Refresh wishlist\n        },\n        error: error => {\n          console.error('Failed to remove from wishlist:', error);\n        }\n      });\n    }\n    addToCart(productId) {\n      this.cartService.addToCart(productId, 1).subscribe({\n        next: () => {\n          this.showNotification('Added to cart successfully!');\n        },\n        error: error => {\n          console.error('Failed to add to cart:', error);\n          this.showNotification('Failed to add to cart');\n        }\n      });\n    }\n    viewProduct(productId) {\n      this.router.navigate(['/product', productId]);\n    }\n    clearWishlist() {\n      if (confirm('Are you sure you want to clear your entire wishlist?')) {\n        this.wishlistService.clearWishlist().subscribe({\n          next: () => {\n            this.wishlistItems = [];\n          },\n          error: error => {\n            console.error('Failed to clear wishlist:', error);\n          }\n        });\n      }\n    }\n    continueShopping() {\n      this.router.navigate(['/']);\n    }\n    getStars(rating) {\n      const stars = [];\n      for (let i = 1; i <= 5; i++) {\n        if (i <= rating) {\n          stars.push('fas fa-star');\n        } else if (i - 0.5 <= rating) {\n          stars.push('fas fa-star-half-alt');\n        } else {\n          stars.push('far fa-star');\n        }\n      }\n      return stars;\n    }\n    showNotification(message) {\n      // Create a simple notification\n      const notification = document.createElement('div');\n      notification.textContent = message;\n      notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      z-index: 10000;\n      font-size: 14px;\n      animation: slideIn 0.3s ease;\n    `;\n      document.body.appendChild(notification);\n      setTimeout(() => {\n        notification.remove();\n      }, 3000);\n    }\n    static {\n      this.ɵfac = function WishlistComponent_Factory(t) {\n        return new (t || WishlistComponent)(i0.ɵɵdirectiveInject(i1.WishlistService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: WishlistComponent,\n        selectors: [[\"app-wishlist\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 8,\n        vars: 4,\n        consts: [[1, \"wishlist-page\"], [1, \"wishlist-header\"], [4, \"ngIf\"], [\"class\", \"wishlist-content\", 4, \"ngIf\"], [\"class\", \"empty-wishlist\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"wishlist-content\"], [1, \"wishlist-grid\"], [\"class\", \"wishlist-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"wishlist-actions\"], [1, \"clear-wishlist-btn\", 3, \"click\"], [1, \"continue-shopping-btn\", 3, \"click\"], [1, \"wishlist-item\"], [1, \"item-image\"], [3, \"src\", \"alt\"], [1, \"remove-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"item-details\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"discount\", 4, \"ngIf\"], [\"class\", \"rating\", 4, \"ngIf\"], [1, \"item-actions\"], [1, \"add-to-cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"view-product-btn\", 3, \"click\"], [1, \"original-price\"], [1, \"discount\"], [1, \"rating\"], [1, \"stars\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"empty-wishlist\"], [1, \"fas\", \"fa-heart\"], [1, \"shop-now-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"spinner\"]],\n        template: function WishlistComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"My Wishlist\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, WishlistComponent_p_4_Template, 2, 1, \"p\", 2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, WishlistComponent_div_5_Template, 8, 1, \"div\", 3)(6, WishlistComponent_div_6_Template, 8, 0, \"div\", 4)(7, WishlistComponent_div_7_Template, 4, 0, \"div\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.wishlistItems.length === 0 && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe],\n        styles: [\".wishlist-page[_ngcontent-%COMP%]{padding:2rem;max-width:1200px;margin:0 auto}.wishlist-header[_ngcontent-%COMP%]{margin-bottom:2rem;text-align:center}.wishlist-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;margin-bottom:.5rem;color:#333}.wishlist-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:2rem;margin-bottom:2rem}.wishlist-item[_ngcontent-%COMP%]{border:1px solid #eee;border-radius:12px;overflow:hidden;transition:transform .2s,box-shadow .2s}.wishlist-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #0000001a}.item-image[_ngcontent-%COMP%]{position:relative;aspect-ratio:1;overflow:hidden}.item-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.remove-btn[_ngcontent-%COMP%]{position:absolute;top:1rem;right:1rem;width:40px;height:40px;border-radius:50%;background:#ffffffe6;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s;color:#dc3545}.remove-btn[_ngcontent-%COMP%]:hover{background:#dc3545;color:#fff;transform:scale(1.1)}.item-details[_ngcontent-%COMP%]{padding:1rem}.item-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:.5rem;color:#333}.brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:.5rem}.price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem;flex-wrap:wrap}.current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#e91e63}.original-price[_ngcontent-%COMP%]{font-size:1rem;color:#999;text-decoration:line-through}.discount[_ngcontent-%COMP%]{background:#e91e63;color:#fff;padding:.25rem .5rem;border-radius:4px;font-size:.8rem;font-weight:600}.rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:1rem}.stars[_ngcontent-%COMP%]{display:flex;gap:2px}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107;font-size:.9rem}.item-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem}.add-to-cart-btn[_ngcontent-%COMP%]{flex:1;background:#007bff;color:#fff;border:none;padding:.75rem;border-radius:6px;font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:center;gap:.5rem;transition:background .2s}.add-to-cart-btn[_ngcontent-%COMP%]:hover{background:#0056b3}.view-product-btn[_ngcontent-%COMP%]{flex:1;background:transparent;color:#007bff;border:2px solid #007bff;padding:.75rem;border-radius:6px;font-weight:600;cursor:pointer;transition:all .2s}.view-product-btn[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.wishlist-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:center;margin-top:2rem}.clear-wishlist-btn[_ngcontent-%COMP%]{background:#dc3545;color:#fff;border:none;padding:1rem 2rem;border-radius:8px;font-weight:600;cursor:pointer;transition:background .2s}.clear-wishlist-btn[_ngcontent-%COMP%]:hover{background:#c82333}.continue-shopping-btn[_ngcontent-%COMP%]{background:transparent;color:#007bff;border:2px solid #007bff;padding:1rem 2rem;border-radius:8px;font-weight:600;cursor:pointer;transition:all .2s}.continue-shopping-btn[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.empty-wishlist[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem;color:#666}.empty-wishlist[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem;color:#e91e63}.shop-now-btn[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:1rem 2rem;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;margin-top:1rem}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.wishlist-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}.wishlist-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.clear-wishlist-btn[_ngcontent-%COMP%], .continue-shopping-btn[_ngcontent-%COMP%]{width:100%;max-width:300px}}\"]\n      });\n    }\n  }\n  return WishlistComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}