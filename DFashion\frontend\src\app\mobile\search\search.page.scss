.search-container {
  padding: 1rem;
  background: var(--ion-color-light);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  
  ion-spinner {
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}

.search-results {
  padding: 1rem;
  
  .results-header {
    margin-bottom: 1rem;
    
    h3 {
      margin: 0;
      color: var(--ion-color-dark);
      font-size: 1.125rem;
      font-weight: 600;
    }
  }
}

.no-results {
  text-align: center;
  padding: 3rem 1rem;
  
  ion-icon {
    font-size: 4rem;
    color: var(--ion-color-medium);
    margin-bottom: 1rem;
  }
  
  h3 {
    margin: 0 0 0.5rem 0;
    color: var(--ion-color-dark);
    font-size: 1.25rem;
  }
  
  p {
    margin: 0;
    color: var(--ion-color-medium);
    line-height: 1.5;
  }
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 1rem;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  .product-image {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .product-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      background: var(--ion-color-danger);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
    }
  }
  
  .product-info {
    padding: 0.75rem;
    
    h4 {
      margin: 0 0 0.25rem 0;
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--ion-color-dark);
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .brand {
      margin: 0 0 0.5rem 0;
      font-size: 0.75rem;
      color: var(--ion-color-medium);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .price-container {
      margin-bottom: 0.5rem;
      
      .current-price {
        font-size: 1rem;
        font-weight: 700;
        color: var(--ion-color-success);
        margin-right: 0.5rem;
      }
      
      .original-price {
        font-size: 0.875rem;
        color: var(--ion-color-medium);
        text-decoration: line-through;
      }
    }
    
    .rating {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      
      ion-icon {
        font-size: 0.875rem;
      }
      
      span {
        font-size: 0.75rem;
        color: var(--ion-color-medium);
        margin-left: 0.25rem;
      }
    }
  }
}

.default-content {
  padding: 1rem;
}

.section {
  margin-bottom: 2rem;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    h3 {
      margin: 0;
      color: var(--ion-color-dark);
      font-size: 1.125rem;
      font-weight: 600;
    }
  }
}

.search-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  
  ion-chip {
    --background: var(--ion-color-light);
    --color: var(--ion-color-dark);
    margin: 0;
    
    ion-icon {
      font-size: 1rem;
    }
  }
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
  
  .category-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    
    ion-icon {
      font-size: 1.5rem;
      color: white;
    }
  }
  
  span {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--ion-color-dark);
    text-align: center;
  }
}

// Responsive design
@media (min-width: 768px) {
  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}
