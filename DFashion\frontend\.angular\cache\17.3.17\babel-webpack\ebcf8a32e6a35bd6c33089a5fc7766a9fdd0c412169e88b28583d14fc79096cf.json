{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport let AdminAuthService = /*#__PURE__*/(() => {\n  class AdminAuthService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.apiUrl = 'http://localhost:5000/api';\n      this.currentUserSubject = new BehaviorSubject(null);\n      this.tokenSubject = new BehaviorSubject(null);\n      this.currentUser$ = this.currentUserSubject.asObservable();\n      this.token$ = this.tokenSubject.asObservable();\n      // Check for existing token on service initialization\n      const token = localStorage.getItem('admin_token');\n      const user = localStorage.getItem('admin_user');\n      if (token && user) {\n        this.tokenSubject.next(token);\n        this.currentUserSubject.next(JSON.parse(user));\n      }\n    }\n    // Login\n    login(email, password) {\n      return this.http.post(`${this.apiUrl}/auth/admin/login`, {\n        email,\n        password\n      }).pipe(tap(response => {\n        if (response.success) {\n          // Store token and user data\n          localStorage.setItem('admin_token', response.data.token);\n          localStorage.setItem('admin_user', JSON.stringify(response.data.user));\n          // Update subjects\n          this.tokenSubject.next(response.data.token);\n          this.currentUserSubject.next(response.data.user);\n        }\n      }), catchError(error => {\n        console.error('Login error:', error);\n        return throwError(error);\n      }));\n    }\n    // Logout\n    logout() {\n      // Clear local storage\n      localStorage.removeItem('admin_token');\n      localStorage.removeItem('admin_user');\n      // Clear subjects\n      this.tokenSubject.next(null);\n      this.currentUserSubject.next(null);\n      // Redirect to login\n      this.router.navigate(['/admin/login']);\n    }\n    // Get current user\n    getCurrentUser() {\n      return this.currentUserSubject.value;\n    }\n    // Get current token\n    getToken() {\n      return this.tokenSubject.value;\n    }\n    // Check if user is authenticated\n    isAuthenticated() {\n      const token = this.getToken();\n      const user = this.getCurrentUser();\n      return !!(token && user);\n    }\n    // Check if user has specific permission\n    hasPermission(module, action) {\n      const user = this.getCurrentUser();\n      if (!user) return false;\n      // Super admin has all permissions\n      if (user.role === 'super_admin') return true;\n      // Check specific permission\n      return user.permissions?.some(permission => permission.module === module && permission.actions.includes(action)) || false;\n    }\n    // Check if user has any of the specified roles\n    hasRole(roles) {\n      const user = this.getCurrentUser();\n      if (!user) return false;\n      const allowedRoles = Array.isArray(roles) ? roles : [roles];\n      return allowedRoles.includes(user.role);\n    }\n    // Verify token with server\n    verifyToken() {\n      const token = this.getToken();\n      if (!token) {\n        return throwError('No token found');\n      }\n      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n      return this.http.get(`${this.apiUrl}/auth/verify`, {\n        headers\n      }).pipe(tap(response => {\n        // Token is valid, update user data if needed\n        if (response && response.data?.user) {\n          this.currentUserSubject.next(response.data.user);\n        }\n      }), catchError(error => {\n        // Token is invalid, logout user\n        this.logout();\n        return throwError(error);\n      }));\n    }\n    // Get authorization headers\n    getAuthHeaders() {\n      const token = this.getToken();\n      return new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    }\n    // Refresh user data\n    refreshUserData() {\n      return this.verifyToken().pipe(map(response => response.data.user));\n    }\n    // Update user profile\n    updateProfile(profileData) {\n      const headers = this.getAuthHeaders();\n      return this.http.put(`${this.apiUrl}/admin/profile`, profileData, {\n        headers\n      }).pipe(tap(response => {\n        if (response && response.success) {\n          // Update current user data\n          const currentUser = this.getCurrentUser();\n          if (currentUser) {\n            const updatedUser = {\n              ...currentUser,\n              ...profileData\n            };\n            this.currentUserSubject.next(updatedUser);\n            localStorage.setItem('admin_user', JSON.stringify(updatedUser));\n          }\n        }\n      }));\n    }\n    // Change password\n    changePassword(currentPassword, newPassword) {\n      const headers = this.getAuthHeaders();\n      return this.http.post(`${this.apiUrl}/admin/change-password`, {\n        currentPassword,\n        newPassword\n      }, {\n        headers\n      });\n    }\n    // Get user permissions for display\n    getUserPermissions() {\n      const user = this.getCurrentUser();\n      return user?.permissions || [];\n    }\n    // Check if user can access admin panel\n    canAccessAdmin() {\n      const adminRoles = ['super_admin', 'admin', 'sales_manager', 'marketing_manager', 'account_manager', 'support_manager', 'sales_executive', 'marketing_executive', 'account_executive', 'support_executive'];\n      return this.hasRole(adminRoles);\n    }\n    // Get user's department\n    getUserDepartment() {\n      const user = this.getCurrentUser();\n      return user?.department || '';\n    }\n    // Get user's role display name\n    getRoleDisplayName() {\n      const user = this.getCurrentUser();\n      if (!user) return '';\n      const roleNames = {\n        'super_admin': 'Super Administrator',\n        'admin': 'Administrator',\n        'sales_manager': 'Sales Manager',\n        'marketing_manager': 'Marketing Manager',\n        'account_manager': 'Account Manager',\n        'support_manager': 'Support Manager',\n        'sales_executive': 'Sales Executive',\n        'marketing_executive': 'Marketing Executive',\n        'account_executive': 'Account Executive',\n        'support_executive': 'Support Executive'\n      };\n      return roleNames[user.role] || user.role;\n    }\n    // Auto-logout on token expiration\n    setupTokenExpiration() {\n      // This would typically decode JWT to get expiration time\n      // For now, we'll set a timeout for 8 hours (token expiry time)\n      setTimeout(() => {\n        this.logout();\n      }, 8 * 60 * 60 * 1000); // 8 hours\n    }\n    static {\n      this.ɵfac = function AdminAuthService_Factory(t) {\n        return new (t || AdminAuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AdminAuthService,\n        factory: AdminAuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AdminAuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}