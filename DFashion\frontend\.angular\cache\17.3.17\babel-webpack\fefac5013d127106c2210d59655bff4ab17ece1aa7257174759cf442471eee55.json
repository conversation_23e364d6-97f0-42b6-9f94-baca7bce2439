{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"../services/product.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/slide-toggle\";\nfunction ProductDialogComponent_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Product name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductDialogComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Description is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductDialogComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Brand is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductDialogComponent_mat_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r1.label, \" \");\n  }\n}\nfunction ProductDialogComponent_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Category is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductDialogComponent_mat_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subcategory_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", subcategory_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", subcategory_r2.label, \" \");\n  }\n}\nfunction ProductDialogComponent_mat_error_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Subcategory is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductDialogComponent_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Price is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductDialogComponent_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Price must be greater than 0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductDialogComponent_mat_spinner_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 23);\n  }\n}\nexport let ProductDialogComponent = /*#__PURE__*/(() => {\n  class ProductDialogComponent {\n    constructor(fb, dialogRef, data, productService, snackBar) {\n      this.fb = fb;\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.productService = productService;\n      this.snackBar = snackBar;\n      this.isEditMode = false;\n      this.isLoading = false;\n      this.categories = [{\n        value: 'men',\n        label: 'Men'\n      }, {\n        value: 'women',\n        label: 'Women'\n      }, {\n        value: 'children',\n        label: 'Children'\n      }];\n      this.subcategoriesMap = {\n        men: [{\n          value: 'shirts',\n          label: 'Shirts'\n        }, {\n          value: 'pants',\n          label: 'Pants'\n        }, {\n          value: 'tops',\n          label: 'Tops'\n        }, {\n          value: 'jackets',\n          label: 'Jackets'\n        }, {\n          value: 'shoes',\n          label: 'Shoes'\n        }],\n        women: [{\n          value: 'dresses',\n          label: 'Dresses'\n        }, {\n          value: 'tops',\n          label: 'Tops'\n        }, {\n          value: 'pants',\n          label: 'Pants'\n        }, {\n          value: 'skirts',\n          label: 'Skirts'\n        }, {\n          value: 'shoes',\n          label: 'Shoes'\n        }],\n        children: [{\n          value: 'tops',\n          label: 'Tops'\n        }, {\n          value: 'pants',\n          label: 'Pants'\n        }, {\n          value: 'dresses',\n          label: 'Dresses'\n        }, {\n          value: 'shoes',\n          label: 'Shoes'\n        }]\n      };\n      this.isEditMode = !!data;\n    }\n    ngOnInit() {\n      this.createForm();\n      if (this.isEditMode) {\n        this.populateForm();\n      }\n    }\n    createForm() {\n      this.productForm = this.fb.group({\n        name: ['', [Validators.required]],\n        description: ['', [Validators.required]],\n        brand: ['', [Validators.required]],\n        category: ['', [Validators.required]],\n        subcategory: ['', [Validators.required]],\n        price: [0, [Validators.required, Validators.min(1)]],\n        originalPrice: [0],\n        discount: [0, [Validators.min(0), Validators.max(100)]],\n        isActive: [true],\n        isFeatured: [false]\n      });\n    }\n    populateForm() {\n      if (this.data) {\n        this.productForm.patchValue({\n          name: this.data.name,\n          description: this.data.description,\n          brand: this.data.brand,\n          category: this.data.category,\n          subcategory: this.data.subcategory,\n          price: this.data.price,\n          originalPrice: this.data.originalPrice,\n          discount: this.data.discount,\n          isActive: this.data.isActive,\n          isFeatured: this.data.isFeatured\n        });\n      }\n    }\n    getSubcategories() {\n      const category = this.productForm.get('category')?.value;\n      return this.subcategoriesMap[category] || [];\n    }\n    onSave() {\n      if (this.productForm.valid) {\n        this.isLoading = true;\n        const formData = this.productForm.value;\n        // Simulate API call\n        setTimeout(() => {\n          this.isLoading = false;\n          this.snackBar.open(this.isEditMode ? 'Product updated successfully' : 'Product created successfully', 'Close', {\n            duration: 3000\n          });\n          this.dialogRef.close(formData);\n        }, 1000);\n      }\n    }\n    onCancel() {\n      this.dialogRef.close();\n    }\n    static {\n      this.ɵfac = function ProductDialogComponent_Factory(t) {\n        return new (t || ProductDialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i3.ProductService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProductDialogComponent,\n        selectors: [[\"app-product-dialog\"]],\n        decls: 72,\n        vars: 14,\n        consts: [[\"mat-dialog-title\", \"\"], [1, \"product-form\", 3, \"formGroup\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Enter product name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"Enter product description\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"brand\", \"placeholder\", \"Enter brand name\"], [\"formControlName\", \"category\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"subcategory\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"price\", \"placeholder\", \"0\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"originalPrice\", \"placeholder\", \"0\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"discount\", \"placeholder\", \"0\", \"min\", \"0\", \"max\", \"100\"], [\"formControlName\", \"isActive\", \"color\", \"primary\"], [\"formControlName\", \"isFeatured\", \"color\", \"primary\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", \"class\", \"save-spinner\", 4, \"ngIf\"], [3, \"value\"], [\"diameter\", \"20\", 1, \"save-spinner\"]],\n        template: function ProductDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"h2\", 0);\n            i0.ɵɵtext(1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"mat-dialog-content\")(3, \"form\", 1)(4, \"div\", 2)(5, \"h3\");\n            i0.ɵɵtext(6, \"Basic Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-form-field\", 4)(9, \"mat-label\");\n            i0.ɵɵtext(10, \"Product Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"input\", 5);\n            i0.ɵɵtemplate(12, ProductDialogComponent_mat_error_12_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 3)(14, \"mat-form-field\", 4)(15, \"mat-label\");\n            i0.ɵɵtext(16, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"textarea\", 7);\n            i0.ɵɵtemplate(18, ProductDialogComponent_mat_error_18_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 3)(20, \"mat-form-field\", 8)(21, \"mat-label\");\n            i0.ɵɵtext(22, \"Brand\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(23, \"input\", 9);\n            i0.ɵɵtemplate(24, ProductDialogComponent_mat_error_24_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"mat-form-field\", 8)(26, \"mat-label\");\n            i0.ɵɵtext(27, \"Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"mat-select\", 10);\n            i0.ɵɵtemplate(29, ProductDialogComponent_mat_option_29_Template, 2, 2, \"mat-option\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(30, ProductDialogComponent_mat_error_30_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 3)(32, \"mat-form-field\", 8)(33, \"mat-label\");\n            i0.ɵɵtext(34, \"Subcategory\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"mat-select\", 12);\n            i0.ɵɵtemplate(36, ProductDialogComponent_mat_option_36_Template, 2, 2, \"mat-option\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(37, ProductDialogComponent_mat_error_37_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(38, \"div\", 2)(39, \"h3\");\n            i0.ɵɵtext(40, \"Pricing\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 3)(42, \"mat-form-field\", 8)(43, \"mat-label\");\n            i0.ɵɵtext(44, \"Price (\\u20B9)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(45, \"input\", 13);\n            i0.ɵɵtemplate(46, ProductDialogComponent_mat_error_46_Template, 2, 0, \"mat-error\", 6)(47, ProductDialogComponent_mat_error_47_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"mat-form-field\", 8)(49, \"mat-label\");\n            i0.ɵɵtext(50, \"Original Price (\\u20B9)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(51, \"input\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 3)(53, \"mat-form-field\", 8)(54, \"mat-label\");\n            i0.ɵɵtext(55, \"Discount (%)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(56, \"input\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(57, \"div\", 2)(58, \"h3\");\n            i0.ɵɵtext(59, \"Product Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"div\", 3)(61, \"mat-slide-toggle\", 16);\n            i0.ɵɵtext(62, \" Active Product \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 3)(64, \"mat-slide-toggle\", 17);\n            i0.ɵɵtext(65, \" Featured Product \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(66, \"mat-dialog-actions\", 18)(67, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function ProductDialogComponent_Template_button_click_67_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(68, \"Cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function ProductDialogComponent_Template_button_click_69_listener() {\n              return ctx.onSave();\n            });\n            i0.ɵɵtemplate(70, ProductDialogComponent_mat_spinner_70_Template, 1, 0, \"mat-spinner\", 21);\n            i0.ɵɵtext(71);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_6_0;\n            let tmp_8_0;\n            let tmp_9_0;\n            let tmp_10_0;\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit Product\" : \"Add New Product\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.productForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.productForm.get(\"name\")) == null ? null : tmp_2_0.hasError(\"required\"));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.productForm.get(\"description\")) == null ? null : tmp_3_0.hasError(\"required\"));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.productForm.get(\"brand\")) == null ? null : tmp_4_0.hasError(\"required\"));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.productForm.get(\"category\")) == null ? null : tmp_6_0.hasError(\"required\"));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getSubcategories());\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.productForm.get(\"subcategory\")) == null ? null : tmp_8_0.hasError(\"required\"));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx.productForm.get(\"price\")) == null ? null : tmp_9_0.hasError(\"required\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = ctx.productForm.get(\"price\")) == null ? null : tmp_10_0.hasError(\"min\"));\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"disabled\", ctx.productForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatError, i9.MatSelect, i10.MatOption, i2.MatDialogTitle, i2.MatDialogActions, i2.MatDialogContent, i11.MatProgressSpinner, i12.MatSlideToggle],\n        styles: [\".product-form[_ngcontent-%COMP%]{min-width:600px;max-height:70vh;overflow-y:auto}.form-section[_ngcontent-%COMP%]{margin-bottom:2rem}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 1rem;color:#333;font-weight:500;border-bottom:1px solid #e0e0e0;padding-bottom:.5rem}.form-row[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1rem}.form-row[_ngcontent-%COMP%]:last-child{margin-bottom:0}.full-width[_ngcontent-%COMP%]{width:100%}.half-width[_ngcontent-%COMP%]{flex:1}.save-spinner[_ngcontent-%COMP%]{margin-right:.5rem}mat-slide-toggle[_ngcontent-%COMP%]{margin-bottom:1rem}@media (max-width: 768px){.product-form[_ngcontent-%COMP%]{min-width:auto;width:100%}.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.half-width[_ngcontent-%COMP%]{width:100%}}\"]\n      });\n    }\n  }\n  return ProductDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}