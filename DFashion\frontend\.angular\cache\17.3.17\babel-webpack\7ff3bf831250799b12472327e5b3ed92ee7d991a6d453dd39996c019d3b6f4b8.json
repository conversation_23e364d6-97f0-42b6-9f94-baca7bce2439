{"ast": null, "code": "/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText(text, x, y, styles, doc) {\n  styles = styles || {};\n  var PHYSICAL_LINE_HEIGHT = 1.15;\n  var k = doc.internal.scaleFactor;\n  var fontSize = doc.internal.getFontSize() / k;\n  var lineHeightFactor = doc.getLineHeightFactor ? doc.getLineHeightFactor() : PHYSICAL_LINE_HEIGHT;\n  var lineHeight = fontSize * lineHeightFactor;\n  var splitRegex = /\\r\\n|\\r|\\n/g;\n  var splitText = '';\n  var lineCount = 1;\n  if (styles.valign === 'middle' || styles.valign === 'bottom' || styles.halign === 'center' || styles.halign === 'right') {\n    splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n    lineCount = splitText.length || 1;\n  }\n  // Align the top\n  y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n  if (styles.valign === 'middle') y -= lineCount / 2 * lineHeight;else if (styles.valign === 'bottom') y -= lineCount * lineHeight;\n  if (styles.halign === 'center' || styles.halign === 'right') {\n    var alignSize = fontSize;\n    if (styles.halign === 'center') alignSize *= 0.5;\n    if (splitText && lineCount >= 1) {\n      for (var iLine = 0; iLine < splitText.length; iLine++) {\n        doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n        y += lineHeight;\n      }\n      return doc;\n    }\n    x -= doc.getStringUnitWidth(text) * alignSize;\n  }\n  if (styles.halign === 'justify') {\n    doc.text(text, x, y, {\n      maxWidth: styles.maxWidth || 100,\n      align: 'justify'\n    });\n  } else {\n    doc.text(text, x, y);\n  }\n  return doc;\n}\nvar globalDefaults = {};\nvar DocHandler = /** @class */function () {\n  function DocHandler(jsPDFDocument) {\n    this.jsPDFDocument = jsPDFDocument;\n    this.userStyles = {\n      // Black for versions of jspdf without getTextColor\n      textColor: jsPDFDocument.getTextColor ? this.jsPDFDocument.getTextColor() : 0,\n      fontSize: jsPDFDocument.internal.getFontSize(),\n      fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n      font: jsPDFDocument.internal.getFont().fontName,\n      // 0 for versions of jspdf without getLineWidth\n      lineWidth: jsPDFDocument.getLineWidth ? this.jsPDFDocument.getLineWidth() : 0,\n      // Black for versions of jspdf without getDrawColor\n      lineColor: jsPDFDocument.getDrawColor ? this.jsPDFDocument.getDrawColor() : 0\n    };\n  }\n  DocHandler.setDefaults = function (defaults, doc) {\n    if (doc === void 0) {\n      doc = null;\n    }\n    if (doc) {\n      doc.__autoTableDocumentDefaults = defaults;\n    } else {\n      globalDefaults = defaults;\n    }\n  };\n  DocHandler.unifyColor = function (c) {\n    if (Array.isArray(c)) {\n      return c;\n    } else if (typeof c === 'number') {\n      return [c, c, c];\n    } else if (typeof c === 'string') {\n      return [c];\n    } else {\n      return null;\n    }\n  };\n  DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n    // Font style needs to be applied before font\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n    var _a, _b, _c;\n    if (fontOnly === void 0) {\n      fontOnly = false;\n    }\n    if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n      this.jsPDFDocument.setFontStyle(styles.fontStyle);\n    }\n    var _d = this.jsPDFDocument.internal.getFont(),\n      fontStyle = _d.fontStyle,\n      fontName = _d.fontName;\n    if (styles.font) fontName = styles.font;\n    if (styles.fontStyle) {\n      fontStyle = styles.fontStyle;\n      var availableFontStyles = this.getFontList()[fontName];\n      if (availableFontStyles && availableFontStyles.indexOf(fontStyle) === -1 && this.jsPDFDocument.setFontStyle) {\n        // Common issue was that the default bold in headers\n        // made custom fonts not work. For example:\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n        this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n        fontStyle = availableFontStyles[0];\n      }\n    }\n    this.jsPDFDocument.setFont(fontName, fontStyle);\n    if (styles.fontSize) this.jsPDFDocument.setFontSize(styles.fontSize);\n    if (fontOnly) {\n      return; // Performance improvement\n    }\n    var color = DocHandler.unifyColor(styles.fillColor);\n    if (color) (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n    color = DocHandler.unifyColor(styles.textColor);\n    if (color) (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n    color = DocHandler.unifyColor(styles.lineColor);\n    if (color) (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n    if (typeof styles.lineWidth === 'number') {\n      this.jsPDFDocument.setLineWidth(styles.lineWidth);\n    }\n  };\n  DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n    return this.jsPDFDocument.splitTextToSize(text, size, opts);\n  };\n  /**\n   * Adds a rectangle to the PDF\n   * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n   * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n   * @param width Width (in units declared at inception of PDF document)\n   * @param height Height (in units declared at inception of PDF document)\n   * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n   */\n  DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n    // null is excluded from fillStyle possible values because it isn't needed\n    // and is prone to bugs as it's used to postpone setting the style\n    // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n    return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n  };\n  DocHandler.prototype.getLastAutoTable = function () {\n    return this.jsPDFDocument.lastAutoTable || null;\n  };\n  DocHandler.prototype.getTextWidth = function (text) {\n    return this.jsPDFDocument.getTextWidth(text);\n  };\n  DocHandler.prototype.getDocument = function () {\n    return this.jsPDFDocument;\n  };\n  DocHandler.prototype.setPage = function (page) {\n    this.jsPDFDocument.setPage(page);\n  };\n  DocHandler.prototype.addPage = function () {\n    return this.jsPDFDocument.addPage();\n  };\n  DocHandler.prototype.getFontList = function () {\n    return this.jsPDFDocument.getFontList();\n  };\n  DocHandler.prototype.getGlobalOptions = function () {\n    return globalDefaults || {};\n  };\n  DocHandler.prototype.getDocumentOptions = function () {\n    return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n  };\n  DocHandler.prototype.pageSize = function () {\n    var pageSize = this.jsPDFDocument.internal.pageSize;\n    // JSPDF 1.4 uses get functions instead of properties on pageSize\n    if (pageSize.width == null) {\n      pageSize = {\n        width: pageSize.getWidth(),\n        height: pageSize.getHeight()\n      };\n    }\n    return pageSize;\n  };\n  DocHandler.prototype.scaleFactor = function () {\n    return this.jsPDFDocument.internal.scaleFactor;\n  };\n  DocHandler.prototype.getLineHeightFactor = function () {\n    var doc = this.jsPDFDocument;\n    return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n  };\n  DocHandler.prototype.getLineHeight = function (fontSize) {\n    return fontSize / this.scaleFactor() * this.getLineHeightFactor();\n  };\n  DocHandler.prototype.pageNumber = function () {\n    var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n    if (!pageInfo) {\n      // Only recent versions of jspdf has pageInfo\n      return this.jsPDFDocument.internal.getNumberOfPages();\n    }\n    return pageInfo.pageNumber;\n  };\n  return DocHandler;\n}();\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nvar HtmlRowInput = /** @class */function (_super) {\n  __extends(HtmlRowInput, _super);\n  function HtmlRowInput(element) {\n    var _this = _super.call(this) || this;\n    _this._element = element;\n    return _this;\n  }\n  return HtmlRowInput;\n}(Array);\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n  return {\n    font: 'helvetica',\n    // helvetica, times, courier\n    fontStyle: 'normal',\n    // normal, bold, italic, bolditalic\n    overflow: 'linebreak',\n    // linebreak, ellipsize, visible or hidden\n    fillColor: false,\n    // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n    textColor: 20,\n    halign: 'left',\n    // left, center, right, justify\n    valign: 'top',\n    // top, middle, bottom\n    fontSize: 10,\n    cellPadding: 5 / scaleFactor,\n    // number or {top,left,right,left,vertical,horizontal}\n    lineColor: 200,\n    lineWidth: 0,\n    cellWidth: 'auto',\n    // 'auto'|'wrap'|number\n    minCellHeight: 0,\n    minCellWidth: 0\n  };\n}\nfunction getTheme(name) {\n  var themes = {\n    striped: {\n      table: {\n        fillColor: 255,\n        textColor: 80,\n        fontStyle: 'normal'\n      },\n      head: {\n        textColor: 255,\n        fillColor: [41, 128, 185],\n        fontStyle: 'bold'\n      },\n      body: {},\n      foot: {\n        textColor: 255,\n        fillColor: [41, 128, 185],\n        fontStyle: 'bold'\n      },\n      alternateRow: {\n        fillColor: 245\n      }\n    },\n    grid: {\n      table: {\n        fillColor: 255,\n        textColor: 80,\n        fontStyle: 'normal',\n        lineWidth: 0.1\n      },\n      head: {\n        textColor: 255,\n        fillColor: [26, 188, 156],\n        fontStyle: 'bold',\n        lineWidth: 0\n      },\n      body: {},\n      foot: {\n        textColor: 255,\n        fillColor: [26, 188, 156],\n        fontStyle: 'bold',\n        lineWidth: 0\n      },\n      alternateRow: {}\n    },\n    plain: {\n      head: {\n        fontStyle: 'bold'\n      },\n      foot: {\n        fontStyle: 'bold'\n      }\n    }\n  };\n  return themes[name];\n}\nfunction getStringWidth(text, styles, doc) {\n  doc.applyStyles(styles, true);\n  var textArr = Array.isArray(text) ? text : [text];\n  var widestLineWidth = textArr.map(function (text) {\n    return doc.getTextWidth(text);\n  }).reduce(function (a, b) {\n    return Math.max(a, b);\n  }, 0);\n  return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n  var lineWidth = table.settings.tableLineWidth;\n  var lineColor = table.settings.tableLineColor;\n  doc.applyStyles({\n    lineWidth: lineWidth,\n    lineColor: lineColor\n  });\n  var fillStyle = getFillStyle(lineWidth, false);\n  if (fillStyle) {\n    doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n  }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n  var drawLine = lineWidth > 0;\n  var drawBackground = fillColor || fillColor === 0;\n  if (drawLine && drawBackground) {\n    return 'DF'; // Fill then stroke\n  } else if (drawLine) {\n    return 'S'; // Only stroke (transparent background)\n  } else if (drawBackground) {\n    return 'F'; // Only fill, no stroke\n  } else {\n    return null;\n  }\n}\nfunction parseSpacing(value, defaultValue) {\n  var _a, _b, _c, _d;\n  value = value || defaultValue;\n  if (Array.isArray(value)) {\n    if (value.length >= 4) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[2],\n        left: value[3]\n      };\n    } else if (value.length === 3) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[2],\n        left: value[1]\n      };\n    } else if (value.length === 2) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[0],\n        left: value[1]\n      };\n    } else if (value.length === 1) {\n      value = value[0];\n    } else {\n      value = defaultValue;\n    }\n  }\n  if (typeof value === 'object') {\n    if (typeof value.vertical === 'number') {\n      value.top = value.vertical;\n      value.bottom = value.vertical;\n    }\n    if (typeof value.horizontal === 'number') {\n      value.right = value.horizontal;\n      value.left = value.horizontal;\n    }\n    return {\n      left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n      top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n      right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n      bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue\n    };\n  }\n  if (typeof value !== 'number') {\n    value = defaultValue;\n  }\n  return {\n    top: value,\n    right: value,\n    bottom: value,\n    left: value\n  };\n}\nfunction getPageAvailableWidth(doc, table) {\n  var margins = parseSpacing(table.settings.margin, 0);\n  return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n  var result = {};\n  var pxScaleFactor = 96 / 72;\n  var backgroundColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)['backgroundColor'];\n  });\n  if (backgroundColor != null) result.fillColor = backgroundColor;\n  var textColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)['color'];\n  });\n  if (textColor != null) result.textColor = textColor;\n  var padding = parsePadding(style, scaleFactor);\n  if (padding) result.cellPadding = padding;\n  var borderColorSide = 'borderTopColor';\n  var finalScaleFactor = pxScaleFactor * scaleFactor;\n  var btw = style.borderTopWidth;\n  if (style.borderBottomWidth === btw && style.borderRightWidth === btw && style.borderLeftWidth === btw) {\n    var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n    if (borderWidth) result.lineWidth = borderWidth;\n  } else {\n    result.lineWidth = {\n      top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n      right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n      bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n      left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor\n    };\n    // Choose border color of first available side\n    // could be improved by supporting object as lineColor\n    if (!result.lineWidth.top) {\n      if (result.lineWidth.right) {\n        borderColorSide = 'borderRightColor';\n      } else if (result.lineWidth.bottom) {\n        borderColorSide = 'borderBottomColor';\n      } else if (result.lineWidth.left) {\n        borderColorSide = 'borderLeftColor';\n      }\n    }\n  }\n  var borderColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)[borderColorSide];\n  });\n  if (borderColor != null) result.lineColor = borderColor;\n  var accepted = ['left', 'right', 'center', 'justify'];\n  if (accepted.indexOf(style.textAlign) !== -1) {\n    result.halign = style.textAlign;\n  }\n  accepted = ['middle', 'bottom', 'top'];\n  if (accepted.indexOf(style.verticalAlign) !== -1) {\n    result.valign = style.verticalAlign;\n  }\n  var res = parseInt(style.fontSize || '');\n  if (!isNaN(res)) result.fontSize = res / pxScaleFactor;\n  var fontStyle = parseFontStyle(style);\n  if (fontStyle) result.fontStyle = fontStyle;\n  var font = (style.fontFamily || '').toLowerCase();\n  if (supportedFonts.indexOf(font) !== -1) {\n    result.font = font;\n  }\n  return result;\n}\nfunction parseFontStyle(style) {\n  var res = '';\n  if (style.fontWeight === 'bold' || style.fontWeight === 'bolder' || parseInt(style.fontWeight) >= 700) {\n    res = 'bold';\n  }\n  if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n    res += 'italic';\n  }\n  return res;\n}\nfunction parseColor(element, styleGetter) {\n  var cssColor = realColor(element, styleGetter);\n  if (!cssColor) return null;\n  var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n  if (!rgba || !Array.isArray(rgba)) {\n    return null;\n  }\n  var color = [parseInt(rgba[1]), parseInt(rgba[2]), parseInt(rgba[3])];\n  var alpha = parseInt(rgba[4]);\n  if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n    return null;\n  }\n  return color;\n}\nfunction realColor(elem, styleGetter) {\n  var bg = styleGetter(elem);\n  if (bg === 'rgba(0, 0, 0, 0)' || bg === 'transparent' || bg === 'initial' || bg === 'inherit') {\n    if (elem.parentElement == null) {\n      return null;\n    }\n    return realColor(elem.parentElement, styleGetter);\n  } else {\n    return bg;\n  }\n}\nfunction parsePadding(style, scaleFactor) {\n  var val = [style.paddingTop, style.paddingRight, style.paddingBottom, style.paddingLeft];\n  var pxScaleFactor = 96 / (72 / scaleFactor);\n  var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n  var inputPadding = val.map(function (n) {\n    return parseInt(n || '0') / pxScaleFactor;\n  });\n  var padding = parseSpacing(inputPadding, 0);\n  if (linePadding > padding.top) {\n    padding.top = linePadding;\n  }\n  if (linePadding > padding.bottom) {\n    padding.bottom = linePadding;\n  }\n  return padding;\n}\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n  var _a, _b;\n  if (includeHiddenHtml === void 0) {\n    includeHiddenHtml = false;\n  }\n  if (useCss === void 0) {\n    useCss = false;\n  }\n  var tableElement;\n  if (typeof input === 'string') {\n    tableElement = window.document.querySelector(input);\n  } else {\n    tableElement = input;\n  }\n  var supportedFonts = Object.keys(doc.getFontList());\n  var scaleFactor = doc.scaleFactor();\n  var head = [],\n    body = [],\n    foot = [];\n  if (!tableElement) {\n    console.error('Html table could not be found with input: ', input);\n    return {\n      head: head,\n      body: body,\n      foot: foot\n    };\n  }\n  for (var i = 0; i < tableElement.rows.length; i++) {\n    var element = tableElement.rows[i];\n    var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n    var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n    if (!row) continue;\n    if (tagName === 'thead') {\n      head.push(row);\n    } else if (tagName === 'tfoot') {\n      foot.push(row);\n    } else {\n      // Add to body both if parent is tbody or table\n      body.push(row);\n    }\n  }\n  return {\n    head: head,\n    body: body,\n    foot: foot\n  };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n  var resultRow = new HtmlRowInput(row);\n  for (var i = 0; i < row.cells.length; i++) {\n    var cell = row.cells[i];\n    var style_1 = window.getComputedStyle(cell);\n    if (includeHidden || style_1.display !== 'none') {\n      var cellStyles = void 0;\n      if (useCss) {\n        cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n      }\n      resultRow.push({\n        rowSpan: cell.rowSpan,\n        colSpan: cell.colSpan,\n        styles: cellStyles,\n        _element: cell,\n        content: parseCellContent(cell)\n      });\n    }\n  }\n  var style = window.getComputedStyle(row);\n  if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n    return resultRow;\n  }\n}\nfunction parseCellContent(orgCell) {\n  // Work on cloned node to make sure no changes are applied to html table\n  var cell = orgCell.cloneNode(true);\n  // Remove extra space and line breaks in markup to make it more similar to\n  // what would be shown in html\n  cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n  // Preserve <br> tags as line breaks in the pdf\n  cell.innerHTML = cell.innerHTML.split(/<br.*?>/) //start with '<br' and ends with '>'.\n  .map(function (part) {\n    return part.trim();\n  }).join('\\n');\n  // innerText for ie\n  return cell.innerText || cell.textContent || '';\n}\nfunction validateInput(global, document, current) {\n  for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n    var options = _a[_i];\n    if (options && typeof options !== 'object') {\n      console.error('The options parameter should be of type object, is: ' + typeof options);\n    }\n    if (options.startY && typeof options.startY !== 'number') {\n      console.error('Invalid value for startY option', options.startY);\n      delete options.startY;\n    }\n  }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n  if (target == null) {\n    throw new TypeError('Cannot convert undefined or null to object');\n  }\n  var to = Object(target);\n  for (var index = 1; index < arguments.length; index++) {\n    // eslint-disable-next-line prefer-rest-params\n    var nextSource = arguments[index];\n    if (nextSource != null) {\n      // Skip over if undefined or null\n      for (var nextKey in nextSource) {\n        // Avoid bugs when hasOwnProperty is shadowed\n        if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n          to[nextKey] = nextSource[nextKey];\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction parseInput(d, current) {\n  var doc = new DocHandler(d);\n  var document = doc.getDocumentOptions();\n  var global = doc.getGlobalOptions();\n  validateInput(global, document, current);\n  var options = assign({}, global, document, current);\n  var win;\n  if (typeof window !== 'undefined') {\n    win = window;\n  }\n  var styles = parseStyles(global, document, current);\n  var hooks = parseHooks(global, document, current);\n  var settings = parseSettings(doc, options);\n  var content = parseContent$1(doc, options, win);\n  return {\n    id: current.tableId,\n    content: content,\n    hooks: hooks,\n    styles: styles,\n    settings: settings\n  };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n  var styleOptions = {\n    styles: {},\n    headStyles: {},\n    bodyStyles: {},\n    footStyles: {},\n    alternateRowStyles: {},\n    columnStyles: {}\n  };\n  var _loop_1 = function (prop) {\n    if (prop === 'columnStyles') {\n      var global_1 = gInput[prop];\n      var document_1 = dInput[prop];\n      var current = cInput[prop];\n      styleOptions.columnStyles = assign({}, global_1, document_1, current);\n    } else {\n      var allOptions = [gInput, dInput, cInput];\n      var styles = allOptions.map(function (opts) {\n        return opts[prop] || {};\n      });\n      styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n    }\n  };\n  for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n    var prop = _a[_i];\n    _loop_1(prop);\n  }\n  return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n  var allOptions = [global, document, current];\n  var result = {\n    didParseCell: [],\n    willDrawCell: [],\n    didDrawCell: [],\n    willDrawPage: [],\n    didDrawPage: []\n  };\n  for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n    var options = allOptions_1[_i];\n    if (options.didParseCell) result.didParseCell.push(options.didParseCell);\n    if (options.willDrawCell) result.willDrawCell.push(options.willDrawCell);\n    if (options.didDrawCell) result.didDrawCell.push(options.didDrawCell);\n    if (options.willDrawPage) result.willDrawPage.push(options.willDrawPage);\n    if (options.didDrawPage) result.didDrawPage.push(options.didDrawPage);\n  }\n  return result;\n}\nfunction parseSettings(doc, options) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n  var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n  var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n  var showFoot;\n  if (options.showFoot === true) {\n    showFoot = 'everyPage';\n  } else if (options.showFoot === false) {\n    showFoot = 'never';\n  } else {\n    showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n  }\n  var showHead;\n  if (options.showHead === true) {\n    showHead = 'everyPage';\n  } else if (options.showHead === false) {\n    showHead = 'never';\n  } else {\n    showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n  }\n  var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n  var theme = options.theme || (useCss ? 'plain' : 'striped');\n  var horizontalPageBreak = !!options.horizontalPageBreak;\n  var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n  return {\n    includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n    useCss: useCss,\n    theme: theme,\n    startY: startY,\n    margin: margin,\n    pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n    rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n    tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n    showHead: showHead,\n    showFoot: showFoot,\n    tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n    tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n    horizontalPageBreak: horizontalPageBreak,\n    horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n    horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows'\n  };\n}\nfunction getStartY(doc, userStartY) {\n  var previous = doc.getLastAutoTable();\n  var sf = doc.scaleFactor();\n  var currentPage = doc.pageNumber();\n  var isSamePageAsPreviousTable = false;\n  if (previous && previous.startPageNumber) {\n    var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n    isSamePageAsPreviousTable = endingPage === currentPage;\n  }\n  if (typeof userStartY === 'number') {\n    return userStartY;\n  } else if (userStartY == null || userStartY === false) {\n    if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n      // Some users had issues with overlapping tables when they used multiple\n      // tables without setting startY so setting it here to a sensible default.\n      return previous.finalY + 20 / sf;\n    }\n  }\n  return null;\n}\nfunction parseContent$1(doc, options, window) {\n  var head = options.head || [];\n  var body = options.body || [];\n  var foot = options.foot || [];\n  if (options.html) {\n    var hidden = options.includeHiddenHtml;\n    if (window) {\n      var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n      head = htmlContent.head || head;\n      body = htmlContent.body || head;\n      foot = htmlContent.foot || head;\n    } else {\n      console.error('Cannot parse html in non browser environment');\n    }\n  }\n  var columns = options.columns || parseColumns(head, body, foot);\n  return {\n    columns: columns,\n    head: head,\n    body: body,\n    foot: foot\n  };\n}\nfunction parseColumns(head, body, foot) {\n  var firstRow = head[0] || body[0] || foot[0] || [];\n  var result = [];\n  Object.keys(firstRow).filter(function (key) {\n    return key !== '_element';\n  }).forEach(function (key) {\n    var colSpan = 1;\n    var input;\n    if (Array.isArray(firstRow)) {\n      input = firstRow[parseInt(key)];\n    } else {\n      input = firstRow[key];\n    }\n    if (typeof input === 'object' && !Array.isArray(input)) {\n      colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n    }\n    for (var i = 0; i < colSpan; i++) {\n      var id = void 0;\n      if (Array.isArray(firstRow)) {\n        id = result.length;\n      } else {\n        id = key + (i > 0 ? \"_\".concat(i) : '');\n      }\n      var rowResult = {\n        dataKey: id\n      };\n      result.push(rowResult);\n    }\n  });\n  return result;\n}\nvar HookData = /** @class */function () {\n  function HookData(doc, table, cursor) {\n    this.table = table;\n    this.pageNumber = table.pageNumber;\n    this.settings = table.settings;\n    this.cursor = cursor;\n    this.doc = doc.getDocument();\n  }\n  return HookData;\n}();\nvar CellHookData = /** @class */function (_super) {\n  __extends(CellHookData, _super);\n  function CellHookData(doc, table, cell, row, column, cursor) {\n    var _this = _super.call(this, doc, table, cursor) || this;\n    _this.cell = cell;\n    _this.row = row;\n    _this.column = column;\n    _this.section = row.section;\n    return _this;\n  }\n  return CellHookData;\n}(HookData);\nvar Table = /** @class */function () {\n  function Table(input, content) {\n    this.pageNumber = 1;\n    this.id = input.id;\n    this.settings = input.settings;\n    this.styles = input.styles;\n    this.hooks = input.hooks;\n    this.columns = content.columns;\n    this.head = content.head;\n    this.body = content.body;\n    this.foot = content.foot;\n  }\n  Table.prototype.getHeadHeight = function (columns) {\n    return this.head.reduce(function (acc, row) {\n      return acc + row.getMaxCellHeight(columns);\n    }, 0);\n  };\n  Table.prototype.getFootHeight = function (columns) {\n    return this.foot.reduce(function (acc, row) {\n      return acc + row.getMaxCellHeight(columns);\n    }, 0);\n  };\n  Table.prototype.allRows = function () {\n    return this.head.concat(this.body).concat(this.foot);\n  };\n  Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n    for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n      var handler = handlers_1[_i];\n      var data = new CellHookData(doc, this, cell, row, column, cursor);\n      var result = handler(data) === false;\n      // Make sure text is always string[] since user can assign string\n      cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n      if (result) {\n        return false;\n      }\n    }\n    return true;\n  };\n  Table.prototype.callEndPageHooks = function (doc, cursor) {\n    doc.applyStyles(doc.userStyles);\n    for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n      var handler = _a[_i];\n      handler(new HookData(doc, this, cursor));\n    }\n  };\n  Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n    for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n      var handler = _a[_i];\n      handler(new HookData(doc, this, cursor));\n    }\n  };\n  Table.prototype.getWidth = function (pageWidth) {\n    if (typeof this.settings.tableWidth === 'number') {\n      return this.settings.tableWidth;\n    } else if (this.settings.tableWidth === 'wrap') {\n      var wrappedWidth = this.columns.reduce(function (total, col) {\n        return total + col.wrappedWidth;\n      }, 0);\n      return wrappedWidth;\n    } else {\n      var margin = this.settings.margin;\n      return pageWidth - margin.left - margin.right;\n    }\n  };\n  return Table;\n}();\nvar Row = /** @class */function () {\n  function Row(raw, index, section, cells, spansMultiplePages) {\n    if (spansMultiplePages === void 0) {\n      spansMultiplePages = false;\n    }\n    this.height = 0;\n    this.raw = raw;\n    if (raw instanceof HtmlRowInput) {\n      this.raw = raw._element;\n      this.element = raw._element;\n    }\n    this.index = index;\n    this.section = section;\n    this.cells = cells;\n    this.spansMultiplePages = spansMultiplePages;\n  }\n  Row.prototype.getMaxCellHeight = function (columns) {\n    var _this = this;\n    return columns.reduce(function (acc, column) {\n      var _a;\n      return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0);\n    }, 0);\n  };\n  Row.prototype.hasRowSpan = function (columns) {\n    var _this = this;\n    return columns.filter(function (column) {\n      var cell = _this.cells[column.index];\n      if (!cell) return false;\n      return cell.rowSpan > 1;\n    }).length > 0;\n  };\n  Row.prototype.canEntireRowFit = function (height, columns) {\n    return this.getMaxCellHeight(columns) <= height;\n  };\n  Row.prototype.getMinimumRowHeight = function (columns, doc) {\n    var _this = this;\n    return columns.reduce(function (acc, column) {\n      var cell = _this.cells[column.index];\n      if (!cell) return 0;\n      var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n      var vPadding = cell.padding('vertical');\n      var oneRowHeight = vPadding + lineHeight;\n      return oneRowHeight > acc ? oneRowHeight : acc;\n    }, 0);\n  };\n  return Row;\n}();\nvar Cell = /** @class */function () {\n  function Cell(raw, styles, section) {\n    var _a;\n    this.contentHeight = 0;\n    this.contentWidth = 0;\n    this.wrappedWidth = 0;\n    this.minReadableWidth = 0;\n    this.minWidth = 0;\n    this.width = 0;\n    this.height = 0;\n    this.x = 0;\n    this.y = 0;\n    this.styles = styles;\n    this.section = section;\n    this.raw = raw;\n    var content = raw;\n    if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n      this.rowSpan = raw.rowSpan || 1;\n      this.colSpan = raw.colSpan || 1;\n      content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n      if (raw._element) {\n        this.raw = raw._element;\n      }\n    } else {\n      this.rowSpan = 1;\n      this.colSpan = 1;\n    }\n    // Stringify 0 and false, but not undefined or null\n    var text = content != null ? '' + content : '';\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    this.text = text.split(splitRegex);\n  }\n  Cell.prototype.getTextPos = function () {\n    var y;\n    if (this.styles.valign === 'top') {\n      y = this.y + this.padding('top');\n    } else if (this.styles.valign === 'bottom') {\n      y = this.y + this.height - this.padding('bottom');\n    } else {\n      var netHeight = this.height - this.padding('vertical');\n      y = this.y + netHeight / 2 + this.padding('top');\n    }\n    var x;\n    if (this.styles.halign === 'right') {\n      x = this.x + this.width - this.padding('right');\n    } else if (this.styles.halign === 'center') {\n      var netWidth = this.width - this.padding('horizontal');\n      x = this.x + netWidth / 2 + this.padding('left');\n    } else {\n      x = this.x + this.padding('left');\n    }\n    return {\n      x: x,\n      y: y\n    };\n  };\n  // TODO (v4): replace parameters with only (lineHeight)\n  Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n    if (lineHeightFactor === void 0) {\n      lineHeightFactor = 1.15;\n    }\n    var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n    var lineHeight = this.styles.fontSize / scaleFactor * lineHeightFactor;\n    var height = lineCount * lineHeight + this.padding('vertical');\n    return Math.max(height, this.styles.minCellHeight);\n  };\n  Cell.prototype.padding = function (name) {\n    var padding = parseSpacing(this.styles.cellPadding, 0);\n    if (name === 'vertical') {\n      return padding.top + padding.bottom;\n    } else if (name === 'horizontal') {\n      return padding.left + padding.right;\n    } else {\n      return padding[name];\n    }\n  };\n  return Cell;\n}();\nvar Column = /** @class */function () {\n  function Column(dataKey, raw, index) {\n    this.wrappedWidth = 0;\n    this.minReadableWidth = 0;\n    this.minWidth = 0;\n    this.width = 0;\n    this.dataKey = dataKey;\n    this.raw = raw;\n    this.index = index;\n  }\n  Column.prototype.getMaxCustomCellWidth = function (table) {\n    var max = 0;\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n      var row = _a[_i];\n      var cell = row.cells[this.index];\n      if (cell && typeof cell.styles.cellWidth === 'number') {\n        max = Math.max(max, cell.styles.cellWidth);\n      }\n    }\n    return max;\n  };\n  return Column;\n}();\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n  calculate(doc, table);\n  var resizableColumns = [];\n  var initialTableWidth = 0;\n  table.columns.forEach(function (column) {\n    var customWidth = column.getMaxCustomCellWidth(table);\n    if (customWidth) {\n      // final column width\n      column.width = customWidth;\n    } else {\n      // initial column width (will be resized)\n      column.width = column.wrappedWidth;\n      resizableColumns.push(column);\n    }\n    initialTableWidth += column.width;\n  });\n  // width difference that needs to be distributed\n  var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n  // first resize attempt: with respect to minReadableWidth and minWidth\n  if (resizeWidth) {\n    resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n      return Math.max(column.minReadableWidth, column.minWidth);\n    });\n  }\n  // second resize attempt: ignore minReadableWidth but respect minWidth\n  if (resizeWidth) {\n    resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n      return column.minWidth;\n    });\n  }\n  resizeWidth = Math.abs(resizeWidth);\n  if (!table.settings.horizontalPageBreak && resizeWidth > 0.1 / doc.scaleFactor()) {\n    // Table can't get smaller due to custom-width or minWidth restrictions\n    // We can't really do much here. Up to user to for example\n    // reduce font size, increase page size or remove custom cell widths\n    // to allow more columns to be reduced in size\n    resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n    console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n  }\n  applyColSpans(table);\n  fitContent(table, doc);\n  applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n  var sf = doc.scaleFactor();\n  var horizontalPageBreak = table.settings.horizontalPageBreak;\n  var availablePageWidth = getPageAvailableWidth(doc, table);\n  table.allRows().forEach(function (row) {\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var cell = row.cells[column.index];\n      if (!cell) continue;\n      var hooks = table.hooks.didParseCell;\n      table.callCellHooks(doc, hooks, cell, row, column, null);\n      var padding = cell.padding('horizontal');\n      cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n      // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n      // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n      // them in the split process to ensure correct word separation and width\n      // calculation.\n      var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n      cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n      if (typeof cell.styles.cellWidth === 'number') {\n        cell.minWidth = cell.styles.cellWidth;\n        cell.wrappedWidth = cell.styles.cellWidth;\n      } else if (cell.styles.cellWidth === 'wrap' || horizontalPageBreak === true) {\n        // cell width should not be more than available page width\n        if (cell.contentWidth > availablePageWidth) {\n          cell.minWidth = availablePageWidth;\n          cell.wrappedWidth = availablePageWidth;\n        } else {\n          cell.minWidth = cell.contentWidth;\n          cell.wrappedWidth = cell.contentWidth;\n        }\n      } else {\n        // auto\n        var defaultMinWidth = 10 / sf;\n        cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n        cell.wrappedWidth = cell.contentWidth;\n        if (cell.minWidth > cell.wrappedWidth) {\n          cell.wrappedWidth = cell.minWidth;\n        }\n      }\n    }\n  });\n  table.allRows().forEach(function (row) {\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var cell = row.cells[column.index];\n      // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n      // Could probably be improved upon however.\n      if (cell && cell.colSpan === 1) {\n        column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n        column.minWidth = Math.max(column.minWidth, cell.minWidth);\n        column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n      } else {\n        // Respect cellWidth set in columnStyles even if there is no cells for this column\n        // or if the column only have colspan cells. Since the width of colspan cells\n        // does not affect the width of columns, setting columnStyles cellWidth enables the\n        // user to at least do it manually.\n        // Note that this is not perfect for now since for example row and table styles are\n        // not accounted for\n        var columnStyles = table.styles.columnStyles[column.dataKey] || table.styles.columnStyles[column.index] || {};\n        var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n        if (cellWidth && typeof cellWidth === 'number') {\n          column.minWidth = cellWidth;\n          column.wrappedWidth = cellWidth;\n        }\n      }\n      if (cell) {\n        // Make sure all columns get at least min width even though width calculations are not based on them\n        if (cell.colSpan > 1 && !column.minWidth) {\n          column.minWidth = cell.minWidth;\n        }\n        if (cell.colSpan > 1 && !column.wrappedWidth) {\n          column.wrappedWidth = cell.minWidth;\n        }\n      }\n    }\n  });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n  var initialResizeWidth = resizeWidth;\n  var sumWrappedWidth = columns.reduce(function (acc, column) {\n    return acc + column.wrappedWidth;\n  }, 0);\n  for (var i = 0; i < columns.length; i++) {\n    var column = columns[i];\n    var ratio = column.wrappedWidth / sumWrappedWidth;\n    var suggestedChange = initialResizeWidth * ratio;\n    var suggestedWidth = column.width + suggestedChange;\n    var minWidth = getMinWidth(column);\n    var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n    resizeWidth -= newWidth - column.width;\n    column.width = newWidth;\n  }\n  resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n  // Run the resizer again if there's remaining width needs\n  // to be distributed and there're columns that can be resized\n  if (resizeWidth) {\n    var resizableColumns = columns.filter(function (column) {\n      return resizeWidth < 0 ? column.width > getMinWidth(column) // check if column can shrink\n      : true; // check if column can grow\n    });\n    if (resizableColumns.length) {\n      resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n    }\n  }\n  return resizeWidth;\n}\nfunction applyRowSpans(table) {\n  var rowSpanCells = {};\n  var colRowSpansLeft = 1;\n  var all = table.allRows();\n  for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n    var row = all[rowIndex];\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var data = rowSpanCells[column.index];\n      if (colRowSpansLeft > 1) {\n        colRowSpansLeft--;\n        delete row.cells[column.index];\n      } else if (data) {\n        data.cell.height += row.height;\n        colRowSpansLeft = data.cell.colSpan;\n        delete row.cells[column.index];\n        data.left--;\n        if (data.left <= 1) {\n          delete rowSpanCells[column.index];\n        }\n      } else {\n        var cell = row.cells[column.index];\n        if (!cell) {\n          continue;\n        }\n        cell.height = row.height;\n        if (cell.rowSpan > 1) {\n          var remaining = all.length - rowIndex;\n          var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n          rowSpanCells[column.index] = {\n            cell: cell,\n            left: left,\n            row: row\n          };\n        }\n      }\n    }\n  }\n}\nfunction applyColSpans(table) {\n  var all = table.allRows();\n  for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n    var row = all[rowIndex];\n    var colSpanCell = null;\n    var combinedColSpanWidth = 0;\n    var colSpansLeft = 0;\n    for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n      var column = table.columns[columnIndex];\n      // Width and colspan\n      colSpansLeft -= 1;\n      if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n        combinedColSpanWidth += column.width;\n        delete row.cells[column.index];\n      } else if (colSpanCell) {\n        var cell = colSpanCell;\n        delete row.cells[column.index];\n        colSpanCell = null;\n        cell.width = column.width + combinedColSpanWidth;\n      } else {\n        var cell = row.cells[column.index];\n        if (!cell) continue;\n        colSpansLeft = cell.colSpan;\n        combinedColSpanWidth = 0;\n        if (cell.colSpan > 1) {\n          colSpanCell = cell;\n          combinedColSpanWidth += column.width;\n          continue;\n        }\n        cell.width = column.width + combinedColSpanWidth;\n      }\n    }\n  }\n}\nfunction fitContent(table, doc) {\n  var rowSpanHeight = {\n    count: 0,\n    height: 0\n  };\n  for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n    var row = _a[_i];\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n      var column = _c[_b];\n      var cell = row.cells[column.index];\n      if (!cell) continue;\n      doc.applyStyles(cell.styles, true);\n      var textSpace = cell.width - cell.padding('horizontal');\n      if (cell.styles.overflow === 'linebreak') {\n        // Add one pt to textSpace to fix rounding error\n        cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), {\n          fontSize: cell.styles.fontSize\n        });\n      } else if (cell.styles.overflow === 'ellipsize') {\n        cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n      } else if (cell.styles.overflow === 'hidden') {\n        cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n      } else if (typeof cell.styles.overflow === 'function') {\n        var result = cell.styles.overflow(cell.text, textSpace);\n        if (typeof result === 'string') {\n          cell.text = [result];\n        } else {\n          cell.text = result;\n        }\n      }\n      cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n      var realContentHeight = cell.contentHeight / cell.rowSpan;\n      if (cell.rowSpan > 1 && rowSpanHeight.count * rowSpanHeight.height < realContentHeight * cell.rowSpan) {\n        rowSpanHeight = {\n          height: realContentHeight,\n          count: cell.rowSpan\n        };\n      } else if (rowSpanHeight && rowSpanHeight.count > 0) {\n        if (rowSpanHeight.height > realContentHeight) {\n          realContentHeight = rowSpanHeight.height;\n        }\n      }\n      if (realContentHeight > row.height) {\n        row.height = realContentHeight;\n      }\n    }\n    rowSpanHeight.count--;\n  }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n  return text.map(function (str) {\n    return ellipsizeStr(str, width, styles, doc, overflow);\n  });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n  var precision = 10000 * doc.scaleFactor();\n  width = Math.ceil(width * precision) / precision;\n  if (width >= getStringWidth(text, styles, doc)) {\n    return text;\n  }\n  while (width < getStringWidth(text + overflow, styles, doc)) {\n    if (text.length <= 1) {\n      break;\n    }\n    text = text.substring(0, text.length - 1);\n  }\n  return text.trim() + overflow;\n}\nfunction createTable(jsPDFDoc, input) {\n  var doc = new DocHandler(jsPDFDoc);\n  var content = parseContent(input, doc.scaleFactor());\n  var table = new Table(input, content);\n  calculateWidths(doc, table);\n  doc.applyStyles(doc.userStyles);\n  return table;\n}\nfunction parseContent(input, sf) {\n  var content = input.content;\n  var columns = createColumns(content.columns);\n  // If no head or foot is set, try generating it with content from columns\n  if (content.head.length === 0) {\n    var sectionRow = generateSectionRow(columns, 'head');\n    if (sectionRow) content.head.push(sectionRow);\n  }\n  if (content.foot.length === 0) {\n    var sectionRow = generateSectionRow(columns, 'foot');\n    if (sectionRow) content.foot.push(sectionRow);\n  }\n  var theme = input.settings.theme;\n  var styles = input.styles;\n  return {\n    columns: columns,\n    head: parseSection('head', content.head, columns, styles, theme, sf),\n    body: parseSection('body', content.body, columns, styles, theme, sf),\n    foot: parseSection('foot', content.foot, columns, styles, theme, sf)\n  };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n  var rowSpansLeftForColumn = {};\n  var result = sectionRows.map(function (rawRow, rowIndex) {\n    var skippedRowForRowSpans = 0;\n    var cells = {};\n    var colSpansAdded = 0;\n    var columnSpansLeft = 0;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n      var column = columns_1[_i];\n      if (rowSpansLeftForColumn[column.index] == null || rowSpansLeftForColumn[column.index].left === 0) {\n        if (columnSpansLeft === 0) {\n          var rawCell = void 0;\n          if (Array.isArray(rawRow)) {\n            rawCell = rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n          } else {\n            rawCell = rawRow[column.dataKey];\n          }\n          var cellInputStyles = {};\n          if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n            cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n          }\n          var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n          var cell = new Cell(rawCell, styles, sectionName);\n          // dataKey is not used internally no more but keep for\n          // backwards compat in hooks\n          cells[column.dataKey] = cell;\n          cells[column.index] = cell;\n          columnSpansLeft = cell.colSpan - 1;\n          rowSpansLeftForColumn[column.index] = {\n            left: cell.rowSpan - 1,\n            times: columnSpansLeft\n          };\n        } else {\n          columnSpansLeft--;\n          colSpansAdded++;\n        }\n      } else {\n        rowSpansLeftForColumn[column.index].left--;\n        columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n        skippedRowForRowSpans++;\n      }\n    }\n    return new Row(rawRow, rowIndex, sectionName, cells);\n  });\n  return result;\n}\nfunction generateSectionRow(columns, section) {\n  var sectionRow = {};\n  columns.forEach(function (col) {\n    if (col.raw != null) {\n      var title = getSectionTitle(section, col.raw);\n      if (title != null) sectionRow[col.dataKey] = title;\n    }\n  });\n  return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n  if (section === 'head') {\n    if (typeof column === 'object') {\n      return column.header || null;\n    } else if (typeof column === 'string' || typeof column === 'number') {\n      return column;\n    }\n  } else if (section === 'foot' && typeof column === 'object') {\n    return column.footer;\n  }\n  return null;\n}\nfunction createColumns(columns) {\n  return columns.map(function (input, index) {\n    var _a;\n    var key;\n    if (typeof input === 'object') {\n      key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n    } else {\n      key = index;\n    }\n    return new Column(key, input, index);\n  });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n  var theme = getTheme(themeName);\n  var sectionStyles;\n  if (sectionName === 'head') {\n    sectionStyles = styles.headStyles;\n  } else if (sectionName === 'body') {\n    sectionStyles = styles.bodyStyles;\n  } else if (sectionName === 'foot') {\n    sectionStyles = styles.footStyles;\n  }\n  var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n  var columnStyles = styles.columnStyles[column.dataKey] || styles.columnStyles[column.index] || {};\n  var colStyles = sectionName === 'body' ? columnStyles : {};\n  var rowStyles = sectionName === 'body' && rowIndex % 2 === 0 ? assign({}, theme.alternateRow, styles.alternateRowStyles) : {};\n  var defaultStyle = defaultStyles(scaleFactor);\n  var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n  return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n  var _a;\n  if (config === void 0) {\n    config = {};\n  }\n  // Get page width\n  var remainingWidth = getPageAvailableWidth(doc, table);\n  // Get column data key to repeat\n  var repeatColumnsMap = new Map();\n  var colIndexes = [];\n  var columns = [];\n  var horizontalPageBreakRepeat = [];\n  if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n    horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n    // It can be a single value of type string or number (even number: 0)\n  } else if (typeof table.settings.horizontalPageBreakRepeat === 'string' || typeof table.settings.horizontalPageBreakRepeat === 'number') {\n    horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n  }\n  // Code to repeat the given column in split pages\n  horizontalPageBreakRepeat.forEach(function (field) {\n    var col = table.columns.find(function (item) {\n      return item.dataKey === field || item.index === field;\n    });\n    if (col && !repeatColumnsMap.has(col.index)) {\n      repeatColumnsMap.set(col.index, true);\n      colIndexes.push(col.index);\n      columns.push(table.columns[col.index]);\n      remainingWidth -= col.wrappedWidth;\n    }\n  });\n  var first = true;\n  var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n  while (i < table.columns.length) {\n    // Prevent duplicates\n    if (repeatColumnsMap.has(i)) {\n      i++;\n      continue;\n    }\n    var colWidth = table.columns[i].wrappedWidth;\n    // Take at least one column even if it doesn't fit\n    if (first || remainingWidth >= colWidth) {\n      first = false;\n      colIndexes.push(i);\n      columns.push(table.columns[i]);\n      remainingWidth -= colWidth;\n    } else {\n      break;\n    }\n    i++;\n  }\n  return {\n    colIndexes: colIndexes,\n    columns: columns,\n    lastIndex: i - 1\n  };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n  var allResults = [];\n  for (var i = 0; i < table.columns.length; i++) {\n    var result = getColumnsCanFitInPage(doc, table, {\n      start: i\n    });\n    if (result.columns.length) {\n      allResults.push(result);\n      i = result.lastIndex;\n    }\n  }\n  return allResults;\n}\nfunction drawTable(jsPDFDoc, table) {\n  var settings = table.settings;\n  var startY = settings.startY;\n  var margin = settings.margin;\n  var cursor = {\n    x: margin.left,\n    y: startY\n  };\n  var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n  var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n  if (settings.pageBreak === 'avoid') {\n    var rows = table.body;\n    var tableHeight = rows.reduce(function (acc, row) {\n      return acc + row.height;\n    }, 0);\n    minTableBottomPos += tableHeight;\n  }\n  var doc = new DocHandler(jsPDFDoc);\n  if (settings.pageBreak === 'always' || settings.startY != null && minTableBottomPos > doc.pageSize().height) {\n    nextPage(doc);\n    cursor.y = margin.top;\n  }\n  table.callWillDrawPageHooks(doc, cursor);\n  var startPos = assign({}, cursor);\n  table.startPageNumber = doc.pageNumber();\n  if (settings.horizontalPageBreak) {\n    // managed flow for split columns\n    printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n  } else {\n    // normal flow\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n      table.head.forEach(function (row) {\n        return printRow(doc, table, row, cursor, table.columns);\n      });\n    }\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n      var isLastRow = index === table.body.length - 1;\n      printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n    });\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n      table.foot.forEach(function (row) {\n        return printRow(doc, table, row, cursor, table.columns);\n      });\n    }\n  }\n  addTableBorder(doc, table, startPos, cursor);\n  table.callEndPageHooks(doc, cursor);\n  table.finalY = cursor.y;\n  jsPDFDoc.lastAutoTable = table;\n  doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n  // calculate width of columns and render only those which can fit into page\n  var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n  var settings = table.settings;\n  if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n    allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n      doc.applyStyles(doc.userStyles);\n      // add page to print next columns in new page\n      if (index > 0) {\n        // When adding a page here, make sure not to print the footers\n        // because they were already printed before on this same loop\n        addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n      } else {\n        // print head for selected columns\n        printHead(doc, table, cursor, colsAndIndexes.columns);\n      }\n      // print body & footer for selected columns\n      printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n      printFoot(doc, table, cursor, colsAndIndexes.columns);\n    });\n  } else {\n    var lastRowIndexOfLastPage_1 = -1;\n    var firstColumnsToFitResult = allColumnsCanFitResult[0];\n    var _loop_1 = function () {\n      // Print the first columns, taking note of the last row printed\n      var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n      if (firstColumnsToFitResult) {\n        doc.applyStyles(doc.userStyles);\n        var firstColumnsToFit = firstColumnsToFitResult.columns;\n        if (lastRowIndexOfLastPage_1 >= 0) {\n          // When adding a page here, make sure not to print the footers\n          // because they were already printed before on this same loop\n          addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n        } else {\n          printHead(doc, table, cursor, firstColumnsToFit);\n        }\n        lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n        printFoot(doc, table, cursor, firstColumnsToFit);\n      }\n      // Check how many rows were printed, so that the next columns would not print more rows than that\n      var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n      // Print the next columns, never exceding maxNumberOfRows\n      allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n        doc.applyStyles(doc.userStyles);\n        // When adding a page here, make sure not to print the footers\n        // because they were already printed before on this same loop\n        addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n        printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n        printFoot(doc, table, cursor, colsAndIndexes.columns);\n      });\n      lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n    };\n    while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n      _loop_1();\n    }\n  }\n}\nfunction printHead(doc, table, cursor, columns) {\n  var settings = table.settings;\n  doc.applyStyles(doc.userStyles);\n  if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n    table.head.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n  doc.applyStyles(doc.userStyles);\n  table.body.forEach(function (row, index) {\n    var isLastRow = index === table.body.length - 1;\n    printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n  });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n  doc.applyStyles(doc.userStyles);\n  maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n  var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n  var lastPrintedRowIndex = -1;\n  table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n    var isLastRow = startRowIndex + index === table.body.length - 1;\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n      printRow(doc, table, row, cursor, columns);\n      lastPrintedRowIndex = startRowIndex + index;\n    }\n  });\n  return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n  var settings = table.settings;\n  doc.applyStyles(doc.userStyles);\n  if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n    table.foot.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n  var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n  var vPadding = cell.padding('vertical');\n  var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n  return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n  var cells = {};\n  row.spansMultiplePages = true;\n  row.height = 0;\n  var rowHeight = 0;\n  for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n    var column = _a[_i];\n    var cell = row.cells[column.index];\n    if (!cell) continue;\n    if (!Array.isArray(cell.text)) {\n      cell.text = [cell.text];\n    }\n    var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n    remainderCell = assign(remainderCell, cell);\n    remainderCell.text = [];\n    var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n    if (cell.text.length > remainingLineCount) {\n      remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n    }\n    var scaleFactor = doc.scaleFactor();\n    var lineHeightFactor = doc.getLineHeightFactor();\n    cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n    if (cell.contentHeight >= remainingPageSpace) {\n      cell.contentHeight = remainingPageSpace;\n      remainderCell.styles.minCellHeight -= remainingPageSpace;\n    }\n    if (cell.contentHeight > row.height) {\n      row.height = cell.contentHeight;\n    }\n    remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n    if (remainderCell.contentHeight > rowHeight) {\n      rowHeight = remainderCell.contentHeight;\n    }\n    cells[column.index] = remainderCell;\n  }\n  var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n  remainderRow.height = rowHeight;\n  for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n    var column = _c[_b];\n    var remainderCell = remainderRow.cells[column.index];\n    if (remainderCell) {\n      remainderCell.height = remainderRow.height;\n    }\n    var cell = row.cells[column.index];\n    if (cell) {\n      cell.height = row.height;\n    }\n  }\n  return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n  var pageHeight = doc.pageSize().height;\n  var margin = table.settings.margin;\n  var marginHeight = margin.top + margin.bottom;\n  var maxRowHeight = pageHeight - marginHeight;\n  if (row.section === 'body') {\n    // Should also take into account that head and foot is not\n    // on every page with some settings\n    maxRowHeight -= table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n  }\n  var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n  var minRowFits = minRowHeight < remainingPageSpace;\n  if (minRowHeight > maxRowHeight) {\n    console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n    return true;\n  }\n  if (!minRowFits) {\n    return false;\n  }\n  var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n  var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n  if (rowHigherThanPage) {\n    if (rowHasRowSpanCell) {\n      console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n    }\n    return true;\n  }\n  if (rowHasRowSpanCell) {\n    // Currently a new page is required whenever a rowspan row don't fit a page.\n    return false;\n  }\n  if (table.settings.rowPageBreak === 'avoid') {\n    return false;\n  }\n  // In all other cases print the row on current page\n  return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n  var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n  if (row.canEntireRowFit(remainingSpace, columns)) {\n    // The row fits in the current page\n    printRow(doc, table, row, cursor, columns);\n  } else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n    // The row gets split in two here, each piece in one page\n    var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n    printRow(doc, table, row, cursor, columns);\n    addPage(doc, table, startPos, cursor, columns);\n    printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n  } else {\n    // The row get printed entirelly on the next page\n    addPage(doc, table, startPos, cursor, columns);\n    printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n  }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n  cursor.x = table.settings.margin.left;\n  for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n    var column = columns_1[_i];\n    var cell = row.cells[column.index];\n    if (!cell) {\n      cursor.x += column.width;\n      continue;\n    }\n    doc.applyStyles(cell.styles);\n    cell.x = cursor.x;\n    cell.y = cursor.y;\n    var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n    if (result === false) {\n      cursor.x += column.width;\n      continue;\n    }\n    drawCellRect(doc, cell, cursor);\n    var textPos = cell.getTextPos();\n    autoTableText(cell.text, textPos.x, textPos.y, {\n      halign: cell.styles.halign,\n      valign: cell.styles.valign,\n      maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right'))\n    }, doc.getDocument());\n    table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n    cursor.x += column.width;\n  }\n  cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n  var cellStyles = cell.styles;\n  // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n  // TODO (v4): better solution?\n  doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n  if (typeof cellStyles.lineWidth === 'number') {\n    // Draw cell background with normal borders\n    var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n    if (fillStyle) {\n      doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n    }\n  } else if (typeof cellStyles.lineWidth === 'object') {\n    // Draw cell background\n    if (cellStyles.fillColor) {\n      doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n    }\n    // Draw cell individual borders\n    drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n  }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n  var x1, y1, x2, y2;\n  if (lineWidth.top) {\n    x1 = cursor.x;\n    y1 = cursor.y;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y;\n    if (lineWidth.right) {\n      x2 += 0.5 * lineWidth.right;\n    }\n    if (lineWidth.left) {\n      x1 -= 0.5 * lineWidth.left;\n    }\n    drawLine(lineWidth.top, x1, y1, x2, y2);\n  }\n  if (lineWidth.bottom) {\n    x1 = cursor.x;\n    y1 = cursor.y + cell.height;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.right) {\n      x2 += 0.5 * lineWidth.right;\n    }\n    if (lineWidth.left) {\n      x1 -= 0.5 * lineWidth.left;\n    }\n    drawLine(lineWidth.bottom, x1, y1, x2, y2);\n  }\n  if (lineWidth.left) {\n    x1 = cursor.x;\n    y1 = cursor.y;\n    x2 = cursor.x;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.top) {\n      y1 -= 0.5 * lineWidth.top;\n    }\n    if (lineWidth.bottom) {\n      y2 += 0.5 * lineWidth.bottom;\n    }\n    drawLine(lineWidth.left, x1, y1, x2, y2);\n  }\n  if (lineWidth.right) {\n    x1 = cursor.x + cell.width;\n    y1 = cursor.y;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.top) {\n      y1 -= 0.5 * lineWidth.top;\n    }\n    if (lineWidth.bottom) {\n      y2 += 0.5 * lineWidth.bottom;\n    }\n    drawLine(lineWidth.right, x1, y1, x2, y2);\n  }\n  function drawLine(width, x1, y1, x2, y2) {\n    doc.getDocument().setLineWidth(width);\n    doc.getDocument().line(x1, y1, x2, y2, 'S');\n  }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n  var bottomContentHeight = table.settings.margin.bottom;\n  var showFoot = table.settings.showFoot;\n  if (showFoot === 'everyPage' || showFoot === 'lastPage' && isLastRow) {\n    bottomContentHeight += table.getFootHeight(table.columns);\n  }\n  return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n  if (columns === void 0) {\n    columns = [];\n  }\n  if (suppressFooter === void 0) {\n    suppressFooter = false;\n  }\n  doc.applyStyles(doc.userStyles);\n  if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n    table.foot.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n  // Add user content just before adding new page ensure it will\n  // be drawn above other things on the page\n  table.callEndPageHooks(doc, cursor);\n  var margin = table.settings.margin;\n  addTableBorder(doc, table, startPos, cursor);\n  nextPage(doc);\n  table.pageNumber++;\n  cursor.x = margin.left;\n  cursor.y = margin.top;\n  startPos.y = margin.top;\n  // call didAddPage hooks before any content is added to the page\n  table.callWillDrawPageHooks(doc, cursor);\n  if (table.settings.showHead === 'everyPage') {\n    table.head.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n    doc.applyStyles(doc.userStyles);\n  }\n}\nfunction nextPage(doc) {\n  var current = doc.pageNumber();\n  doc.setPage(current + 1);\n  var newCurrent = doc.pageNumber();\n  if (newCurrent === current) {\n    doc.addPage();\n    return true;\n  }\n  return false;\n}\nfunction applyPlugin(jsPDF) {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  jsPDF.API.autoTable = function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var options = args[0];\n    var input = parseInput(this, options);\n    var table = createTable(this, input);\n    drawTable(this, table);\n    return this;\n  };\n  // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n  jsPDF.API.lastAutoTable = false;\n  jsPDF.API.autoTableText = function (text, x, y, styles) {\n    autoTableText(text, x, y, styles, this);\n  };\n  jsPDF.API.autoTableSetDefaults = function (defaults) {\n    DocHandler.setDefaults(defaults, this);\n    return this;\n  };\n  jsPDF.autoTableSetDefaults = function (defaults, doc) {\n    DocHandler.setDefaults(defaults, doc);\n  };\n  jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n    var _a;\n    if (includeHiddenElements === void 0) {\n      includeHiddenElements = false;\n    }\n    if (typeof window === 'undefined') {\n      console.error('Cannot run autoTableHtmlToJson in non browser environment');\n      return null;\n    }\n    var doc = new DocHandler(this);\n    var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false),\n      head = _b.head,\n      body = _b.body;\n    var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) {\n      return c.content;\n    })) || [];\n    return {\n      columns: columns,\n      rows: body,\n      data: body\n    };\n  };\n}\nvar _a;\nfunction autoTable(d, options) {\n  var input = parseInput(d, options);\n  var table = createTable(d, input);\n  drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n  var input = parseInput(d, options);\n  return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n  drawTable(d, table);\n}\ntry {\n  if (typeof window !== 'undefined' && window) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    var anyWindow = window;\n    var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\n    if (jsPDF) {\n      applyPlugin(jsPDF);\n    }\n  }\n} catch (error) {\n  console.error('Could not apply autoTable plugin', error);\n}\nexport { Cell, CellHookData, Column, HookData, Row, Table, __createTable, __drawTable, applyPlugin, autoTable, autoTable as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}