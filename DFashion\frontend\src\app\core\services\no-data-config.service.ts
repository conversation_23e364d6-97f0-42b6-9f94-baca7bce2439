import { Injectable } from '@angular/core';

export interface NoDataConfig {
  title: string;
  message: string;
  iconClass: string;
  containerClass?: string;
  showActions?: boolean;
  primaryAction?: string;
  secondaryAction?: string;
  suggestions?: string[];
  suggestionsTitle?: string;
}

@Injectable({
  providedIn: 'root'
})
export class NoDataConfigService {

  private configs: { [key: string]: NoDataConfig } = {
    // Shop page configurations
    'shop.featuredBrands': {
      title: 'No Featured Brands',
      message: 'We\'re working on adding featured brands. Check back soon!',
      iconClass: 'fas fa-store',
      containerClass: 'compact',
      showActions: true,
      primaryAction: 'Browse All Brands',
      suggestions: ['Nike', 'Adidas', 'Zara', 'H&M', 'Uniqlo'],
      suggestionsTitle: 'Popular Brands:'
    },
    
    'shop.trendingProducts': {
      title: 'No Trending Products',
      message: 'Discover what\'s hot! We\'re updating our trending collection.',
      iconClass: 'fas fa-fire',
      containerClass: 'compact',
      showActions: true,
      primaryAction: 'Browse All Products',
      secondaryAction: 'View Categories',
      suggestions: ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Accessories'],
      suggestionsTitle: 'Popular Categories:'
    },
    
    'shop.newArrivals': {
      title: 'No New Arrivals',
      message: 'Stay tuned for the latest fashion arrivals!',
      iconClass: 'fas fa-sparkles',
      containerClass: 'compact',
      showActions: true,
      primaryAction: 'Browse All Products',
      secondaryAction: 'Set Alerts',
      suggestions: ['Summer Collection', 'Winter Wear', 'Casual Outfits', 'Formal Wear'],
      suggestionsTitle: 'Coming Soon:'
    },
    
    'shop.quickLinks': {
      title: 'Quick Links Unavailable',
      message: 'Quick navigation links are being updated.',
      iconClass: 'fas fa-link',
      containerClass: 'compact',
      showActions: true,
      primaryAction: 'Browse Categories',
      suggestions: ['Women\'s Fashion', 'Men\'s Fashion', 'Kids\' Fashion', 'Accessories'],
      suggestionsTitle: 'Browse:'
    },

    // Search page configurations
    'search.noResults': {
      title: 'No Products Found',
      message: 'Try searching with different keywords or browse our categories.',
      iconClass: 'fas fa-search',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Browse Categories',
      secondaryAction: 'Clear Filters',
      suggestions: ['kurtas', 'jeans', 'dresses', 'shoes', 'bags', 'watches'],
      suggestionsTitle: 'Popular searches:'
    },

    // Cart configurations
    'cart.empty': {
      title: 'Your Cart is Empty',
      message: 'Looks like you haven\'t added anything to your cart yet.',
      iconClass: 'fas fa-shopping-cart',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Start Shopping',
      secondaryAction: 'View Wishlist',
      suggestions: ['Trending Products', 'New Arrivals', 'Sale Items'],
      suggestionsTitle: 'Explore:'
    },

    // Wishlist configurations
    'wishlist.empty': {
      title: 'Your Wishlist is Empty',
      message: 'Save items you love to your wishlist for easy access later.',
      iconClass: 'fas fa-heart',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Discover Products',
      suggestions: ['Trending Now', 'New Arrivals', 'Best Sellers'],
      suggestionsTitle: 'Start with:'
    },

    // Posts configurations
    'posts.noPosts': {
      title: 'No Posts Yet',
      message: 'Be the first to share your style! Create your first post.',
      iconClass: 'fas fa-camera',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Create Post',
      secondaryAction: 'Browse Feed',
      suggestions: ['Fashion Tips', 'Outfit Ideas', 'Style Inspiration'],
      suggestionsTitle: 'Post Ideas:'
    },

    // Stories configurations
    'stories.noStories': {
      title: 'No Stories Available',
      message: 'Share your fashion moments with stories!',
      iconClass: 'fas fa-plus-circle',
      containerClass: 'compact',
      showActions: true,
      primaryAction: 'Create Story',
      suggestions: ['Daily Outfit', 'Behind the Scenes', 'Style Tips'],
      suggestionsTitle: 'Story Ideas:'
    },

    // Orders configurations
    'orders.noOrders': {
      title: 'No Orders Yet',
      message: 'You haven\'t placed any orders yet. Start shopping to see your orders here.',
      iconClass: 'fas fa-receipt',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Start Shopping',
      suggestions: ['Trending Products', 'New Arrivals', 'Best Deals'],
      suggestionsTitle: 'Shop:'
    },

    // Reviews configurations
    'reviews.noReviews': {
      title: 'No Reviews Yet',
      message: 'Be the first to review this product and help others make informed decisions.',
      iconClass: 'fas fa-star',
      containerClass: 'compact',
      showActions: true,
      primaryAction: 'Write Review',
      suggestions: ['Quality', 'Fit', 'Style', 'Value for Money'],
      suggestionsTitle: 'Review aspects:'
    },

    // Notifications configurations
    'notifications.noNotifications': {
      title: 'No Notifications',
      message: 'You\'re all caught up! No new notifications.',
      iconClass: 'fas fa-bell',
      containerClass: 'full-height',
      showActions: false,
      suggestions: ['Order Updates', 'New Arrivals', 'Sale Alerts'],
      suggestionsTitle: 'You\'ll be notified about:'
    },

    // Generic configurations
    'generic.noData': {
      title: 'No Data Available',
      message: 'There is no data to display at the moment.',
      iconClass: 'fas fa-inbox',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Refresh',
      secondaryAction: 'Go Back'
    },

    'generic.error': {
      title: 'Something Went Wrong',
      message: 'We encountered an error while loading the data. Please try again.',
      iconClass: 'fas fa-exclamation-triangle',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Try Again',
      secondaryAction: 'Go Back'
    },

    'generic.maintenance': {
      title: 'Under Maintenance',
      message: 'This section is currently under maintenance. We\'ll be back soon!',
      iconClass: 'fas fa-tools',
      containerClass: 'full-height',
      showActions: true,
      primaryAction: 'Go Back',
      suggestions: ['Check our social media for updates'],
      suggestionsTitle: 'Stay updated:'
    }
  };

  getConfig(key: string): NoDataConfig {
    return this.configs[key] || this.configs['generic.noData'];
  }

  setConfig(key: string, config: NoDataConfig): void {
    this.configs[key] = config;
  }

  // Helper methods for common scenarios
  getShopConfig(section: 'featuredBrands' | 'trendingProducts' | 'newArrivals' | 'quickLinks'): NoDataConfig {
    return this.getConfig(`shop.${section}`);
  }

  getSearchConfig(): NoDataConfig {
    return this.getConfig('search.noResults');
  }

  getCartConfig(): NoDataConfig {
    return this.getConfig('cart.empty');
  }

  getWishlistConfig(): NoDataConfig {
    return this.getConfig('wishlist.empty');
  }

  getPostsConfig(): NoDataConfig {
    return this.getConfig('posts.noPosts');
  }

  getStoriesConfig(): NoDataConfig {
    return this.getConfig('stories.noStories');
  }

  getOrdersConfig(): NoDataConfig {
    return this.getConfig('orders.noOrders');
  }

  getReviewsConfig(): NoDataConfig {
    return this.getConfig('reviews.noReviews');
  }

  getNotificationsConfig(): NoDataConfig {
    return this.getConfig('notifications.noNotifications');
  }

  getErrorConfig(): NoDataConfig {
    return this.getConfig('generic.error');
  }

  getMaintenanceConfig(): NoDataConfig {
    return this.getConfig('generic.maintenance');
  }
}
