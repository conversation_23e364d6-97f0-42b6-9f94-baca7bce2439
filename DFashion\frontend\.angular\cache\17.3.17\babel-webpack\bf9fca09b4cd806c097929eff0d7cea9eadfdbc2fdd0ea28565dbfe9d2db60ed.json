{"ast": null, "code": "import { AuthGuard } from './core/guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n}, {\n  path: 'home',\n  loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'profile',\n  loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'shop',\n  loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'admin',\n  loadChildren: () => import('./features/admin/admin.routes').then(m => m.adminRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/home'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "authRoutes", "homeRoutes", "canActivate", "profileRoutes", "shopRoutes", "adminRoutes"], "sources": ["E:\\DFashion\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    redirectTo: '/home',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n  },\n  {\n    path: 'home',\n    loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'shop',\n    loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'admin',\n    loadChildren: () => import('./features/admin/admin.routes').then(m => m.adminRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: '**',\n    redirectTo: '/home'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,0BAA0B;AAEpD,OAAO,MAAMC,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF,EACD;EACEN,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,UAAU,CAAC;EACjFC,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,aAAa,CAAC;EAC1FD,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,UAAU,CAAC;EACjFF,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,WAAW,CAAC;EACpFH,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}