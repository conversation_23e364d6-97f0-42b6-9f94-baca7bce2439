.sidenav-container {
  height: 100vh;
}

.sidenav {
  width: 280px;
  background: #1a1a2e;
  color: white;
  display: flex;
  flex-direction: column;
}

.sidenav-header {
  padding: 1.5rem 1rem 1rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;

    .logo-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: #667eea;
    }

    .logo-text {
      font-size: 1.5rem;
      font-weight: 600;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .admin-badge {
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
  }
}

.user-profile {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;

  .user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;

    .user-initials {
      font-size: 1.1rem;
    }
  }

  .user-info {
    flex: 1;
    min-width: 0;

    .user-name {
      font-weight: 500;
      font-size: 0.95rem;
      margin-bottom: 0.25rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-role {
      color: #667eea;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .user-department {
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.75rem;
    }
  }
}

.nav-list {
  flex: 1;
  padding-top: 1rem;

  .nav-item {
    margin: 0.25rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }

    &.active-nav-item {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      .nav-arrow {
        color: white;
      }
    }

    mat-icon {
      color: inherit;
    }

    .nav-arrow {
      margin-left: auto;
    }
  }
}

.sidenav-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .logout-button {
    width: 100%;
    color: rgba(255, 255, 255, 0.8);
    justify-content: flex-start;
    gap: 0.75rem;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }
  }
}

.main-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 1.25rem;
    font-weight: 500;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }

  .notification-button {
    margin-right: 0.5rem;
  }

  .user-menu-button {
    .toolbar-user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;

      .toolbar-user-initials {
        font-size: 0.9rem;
      }
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: #f5f5f5;
}

// User Menu Styles
::ng-deep .user-menu {
  .mat-menu-content {
    padding: 0;
  }

  .user-menu-header {
    padding: 1rem;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    gap: 1rem;

    .menu-user-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;

      .menu-user-initials {
        font-size: 1.1rem;
      }
    }

    .menu-user-info {
      .menu-user-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.25rem;
      }

      .menu-user-email {
        color: #666;
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
      }

      .menu-user-role {
        color: #667eea;
        font-size: 0.8rem;
        font-weight: 500;
      }
    }
  }

  .logout-menu-item {
    color: #f44336;

    mat-icon {
      color: #f44336;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .sidenav {
    width: 100%;
    max-width: 280px;
  }

  .page-content {
    padding: 1rem;
  }

  .user-profile {
    .user-info {
      .user-name {
        font-size: 0.9rem;
      }
    }
  }
}

// Material Overrides
::ng-deep {
  .mat-list-item {
    height: 48px !important;
  }

  .mat-list-item-content {
    padding: 0 16px !important;
  }

  .mat-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  }

  .mat-badge-content {
    background: #f44336 !important;
    color: white !important;
  }
}

// Animations
.nav-item {
  transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.user-menu-button {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}
